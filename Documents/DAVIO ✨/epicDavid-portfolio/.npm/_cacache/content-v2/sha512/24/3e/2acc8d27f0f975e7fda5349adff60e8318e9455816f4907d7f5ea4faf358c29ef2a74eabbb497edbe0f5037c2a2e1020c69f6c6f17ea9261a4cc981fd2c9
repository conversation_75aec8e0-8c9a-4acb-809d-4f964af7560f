{"_id": "hoist-non-react-statics", "_rev": "50-65a5bc4486a01a46bb658e82a31afc0d", "name": "hoist-non-react-statics", "description": "Copies non-react specific statics from a child component to a parent component", "dist-tags": {"latest": "3.3.2", "next": "3.0.0-rc.0"}, "versions": {"1.0.0": {"name": "hoist-non-react-statics", "version": "1.0.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^2.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^0.13.3"}, "keywords": ["react"], "gitHead": "7969d4ca99b8f567d56bd58aec443c62b5d80b5f", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.0", "_shasum": "886f930966095d298352f92ac1a8f0841d5d2e65", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.10.33", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "886f930966095d298352f92ac1a8f0841d5d2e65", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.0.tgz", "integrity": "sha512-f53RPvwxA21ZtIKoCNSdozP8kELCIOa/KUL2U43l9GZAzm6b4f0AfkmXcQxsCUZVQjUELjDtiNUuWGm3wSQdrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTvj9Rf32FZ0dwRvLX0f5HrDIfTdi0y3aRtahb0OiaBgIhAJl1I8Tu3eD9ncd+SAx5a0+abFQPy5CqriHwQUJjxoHW"}]}, "directories": {}}, "1.0.1": {"name": "hoist-non-react-statics", "version": "1.0.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^2.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^0.13.3"}, "keywords": ["react"], "gitHead": "86ffc1298cf0ab5832ba6502a0862145ead75c20", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics", "_id": "hoist-non-react-statics@1.0.1", "_shasum": "af21cbaf5406369b91ec88c7c85410ae837b4ac4", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "af21cbaf5406369b91ec88c7c85410ae837b4ac4", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.1.tgz", "integrity": "sha512-lmI3bN37XR7ujdWA1ELRYMJsaE46FknMWflixb9oTrMCWBLGPI51K+4PwySiexn9QB5rbTLvAInt01QnTYRKZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGjiaaHDahF4K7BSeEG0RIKcJH4qOyqKkigpgCe82WzDAiANwyukFRQUfFo1GY8a/hbWjrGiFUbs/EScSDOxi2v7fQ=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "hoist-non-react-statics", "version": "1.0.2", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^2.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^0.13.3"}, "keywords": ["react"], "gitHead": "4b9e379f8d84dfbf559f2e91eb27fdba0d18d7f1", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.2", "_shasum": "df06d7ba9209515a3c6ed0e64eab4ca4e094026b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.10.33", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "df06d7ba9209515a3c6ed0e64eab4ca4e094026b", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.2.tgz", "integrity": "sha512-FrgjNChf2YUnVoCmhIwAES3qPJfmS+LqxwmrbSlSDzgOibTc1n/4Bb+UE1tDHQgzli1ofWly+lpywQyWkOGVGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqlCvS7TdCuFL436ejzDcNNSmQV9HpJggtIonaWxWHmAIgHgXmpo+H0V2m5IJbO5NxIsz1dx27qhDFyqLVtOT+YJE="}]}, "directories": {}}, "1.0.3": {"name": "hoist-non-react-statics", "version": "1.0.3", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^2.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^0.13.3"}, "keywords": ["react"], "gitHead": "91e65b1c10e3a37d5f94ef1424b3233e442d69e9", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.3", "_shasum": "22cfc787e53cc7cf4832ee35214fa3c55ef2d8aa", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.10.33", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "22cfc787e53cc7cf4832ee35214fa3c55ef2d8aa", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.3.tgz", "integrity": "sha512-qZJQke6ulKVrBqIwk/nC5WR/DTb/NPIh2aPJtIJKDLaCSTZw8fvz0ibHAeZGqDDlUYsqbTyTzcY9Tz5HdZRRqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7i90k/ZeXZMQ37YEuxJNclpjTh/+IIjop4oIRsryENAiEAk+Wl0Na71J5FoTdvvsnrHwq9wi35hIhyS+Rk7GUTPpA="}]}, "directories": {}}, "1.0.4": {"name": "hoist-non-react-statics", "version": "1.0.4", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^3.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "<0.15.0"}, "keywords": ["react"], "gitHead": "8bba37ae1aafc082411c0aeb0d34971ad4e38797", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.4", "_shasum": "dfc529380709d47901192ea6f002d83c39f8a288", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "0.10.40", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "dfc529380709d47901192ea6f002d83c39f8a288", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.4.tgz", "integrity": "sha512-uFAgNdVcPm9GtNs+nD/acslgymmp2Wf+nGyUhurosAiIxxNNr1/Z+3E54YyxEbfZmn4PCrs22NVAfXas90MY2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEEaDb96MkorTfMUjAEh91pQigudVQWDGn/UGcNwNGvzAiAvpjyVWQBpOz4qtng9eGoCq1c5nQrcMZs9gtclO5p6bg=="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/hoist-non-react-statics-1.0.4.tgz_1454440383601_0.7808840223588049"}, "directories": {}}, "1.0.5": {"name": "hoist-non-react-statics", "version": "1.0.5", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^3.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "<0.15.0"}, "keywords": ["react"], "gitHead": "24395322f31203484f3cfb6958c2b4313006483f", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.5", "_shasum": "0e36d2c130c8511f267a0d4ceb45ec7d7e4f0c70", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "0.10.40", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "0e36d2c130c8511f267a0d4ceb45ec7d7e4f0c70", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.5.tgz", "integrity": "sha512-FP3+a7tJd8Y9L+D2TSlZ9FvG+mzPODZ6XAFF5cBwaYyw8AyJrn1TWqg4f+Voyfi7XY6pwpPIXgz3SX3YTfnjgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDh79SKb9U7z74ZilDt8Qm65lGamYm7EjivdPfqOrV5rAiEA0fYRmd7at/MGeQwlidMo4k3Vghdghf9EJ1ZHgNhVzz4="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/hoist-non-react-statics-1.0.5.tgz_1454447620415_0.7366878658067435"}, "directories": {}}, "1.0.6": {"name": "hoist-non-react-statics", "version": "1.0.6", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^3.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "749ce7548dd4d9e7cd6553ce6eeb367c0b0f400c", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.0.6", "_shasum": "87f66a62c5a5f210a90eec32ec1ebb019790577d", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "87f66a62c5a5f210a90eec32ec1ebb019790577d", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.0.6.tgz", "integrity": "sha512-RjwA2+dwSc/nJApSAgU2BpM7wEB1clVyPo+1UkYZwbJYd20Kd2KyfpnfGWSBqOQRap240MZXvRdyss3q60C31Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHF2fsIBI0lzLkLgtjS+NepRufmmHO8scn6nuRGY1b+IAiEAlG7XuUY56G7hwhyGl6KHbdXYU3lKxsW3MDHRfHi/NNU="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/hoist-non-react-statics-1.0.6.tgz_1462991335339_0.957615080056712"}, "directories": {}}, "1.1.0": {"name": "hoist-non-react-statics", "version": "1.1.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^3.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "4eb6d8007acefd893809510511fd9808ec48a5e4", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.1.0", "_shasum": "3227305f91f338ae06a6271f00005299eaf41b2c", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "3227305f91f338ae06a6271f00005299eaf41b2c", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.1.0.tgz", "integrity": "sha512-EIx7O624ZH/7KpLgGryI89h5HmDF9aRDeQvNLDg+oWdD1fzqeN2YxIvsNH6DQkzQ2v6BT9pDMRO8xlvAeAV0iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMTMF+Xvhe3h43jq6tMm6kUR/ZXRjiLNUOpoWWmGd+eAIhAJOMfnbaNoqveQN0F77GdXVoALTgxXXQNHn8pTJj++qJ"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/hoist-non-react-statics-1.1.0.tgz_1465837396307_0.0761197975371033"}, "directories": {}}, "1.2.0": {"name": "hoist-non-react-statics", "version": "1.2.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "licenses": [{"type": "BSD", "url": "https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md"}], "devDependencies": {"babel": "^5.0.7", "babel-eslint": "^3.0.1", "chai": "^3.0.0", "coveralls": "^2.11.1", "eslint": "^0.21.0", "istanbul": "^0.3.2", "mocha": "^2.0.1", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "437bebb16842657310b6a80ee89fce34864509bd", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@1.2.0", "_shasum": "aa448cf0986d55cc40773b17174b7dd066cb7cfb", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "dist": {"shasum": "aa448cf0986d55cc40773b17174b7dd066cb7cfb", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-1.2.0.tgz", "integrity": "sha512-r8huvKK+m+VraiRipdZYc+U4XW43j6OFG/oIafe7GfDbRpCduRoX9JI/DRxqgtBSCeL+et6N6ibZoedHS2NyOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFFI4jnk+jGfZO/CYqq/Rm1BhYN5pZ3X3vr7pLkEjcxAIgHwgzMLuRckPTsTvGFCCrVfJVNRnUpPr1RDfazP3d8ow="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/hoist-non-react-statics-1.2.0.tgz_1466185955248_0.8610786439385265"}, "directories": {}}, "2.0.0": {"name": "hoist-non-react-statics", "version": "2.0.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "bae548b3b6d7dfadcd87ba64f1b733d247c17a27", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.0.0", "_shasum": "843180515e0281952b08f41c620ca74870c7e354", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "843180515e0281952b08f41c620ca74870c7e354", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.0.0.tgz", "integrity": "sha512-Aj2O1/edY2EIXP1mSOukAiEf9rou9zq/GwhBeCDkpt7LZXuvH69/FlTd0zJDHbCCMx89hs0Baa3QPGWWla6/fQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFm3j5rogfnMqYiCdpobfIEOrDSyZjTW+X/GIaObWTEbAiEA6kwJVv/YU2WiG3MGijWjYqcydZZSfsARwaCy3naIeZg="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.0.0.tgz_1498501318896_0.8667218701448292"}, "directories": {}}, "2.1.0": {"name": "hoist-non-react-statics", "version": "2.1.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "b05f87480f89774ac0d7c29fdb6282bf3bb6614e", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.1.0", "_shasum": "44879d7c06796a5f1baaaed29833bfdc9995ca32", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "44879d7c06796a5f1baaaed29833bfdc9995ca32", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.1.0.tgz", "integrity": "sha512-KhsW6ii0Vdq9OThVvPGKldeUAcEQwdintpoJZYXA5v9DVwcDIqiHJ1/asQw8WtBaMqOLFOdzO7BRcQjLfxxGhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHp9Rio4f+m3l5Q4vLvcSWABERFSLkx9jl2lFHfjyD6RAiAH6OMrLOWJ6waDbiK5b/WwGNY1jMq9Gt59kTeIRmrloA=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.1.0.tgz_1499660528728_0.5973200779408216"}, "directories": {}}, "2.1.1": {"name": "hoist-non-react-statics", "version": "2.1.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "860301dd7b49c4d48016c958a007aa0bfef564f5", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.1.1", "_shasum": "71f47fe91400f57d55e867277e3a5013829dcb45", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "71f47fe91400f57d55e867277e3a5013829dcb45", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.1.1.tgz", "integrity": "sha512-aZ1sx3i+Vhdv2CX0rh1r7JWtSwguKzw3kZ4cBN6ttDxxbCd6MBKYgyJ8Wv60vnoG3/hDl87NR4kCYDUZ008F8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjIg2fHmp9RTHbb+DBgCkFqRJ/4/zad4CtsU7Y3yEJvAiBTpuGuGuIpRLSoxDylVG2tFguPNVRrEj6jXD/ClCK9/g=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.1.1.tgz_1499879361071_0.3841377585195005"}, "directories": {}}, "2.2.0": {"name": "hoist-non-react-statics", "version": "2.2.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "f1a25daf4824465772ed3422580437d4772697b4", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.2.0", "_shasum": "b099ca82f3640b1244309c8a526a2bd60ad9d7d9", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "b099ca82f3640b1244309c8a526a2bd60ad9d7d9", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.2.0.tgz", "integrity": "sha512-cGzh+KGP8v3JfBSn0KkDQbZ7KDv0vsjaUKKLON2BHT1oOLyQnLJGoJuSvtwEhv3kHaUp21IZSKwjfpi4ICEKbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDor4xpRUjbX7C7CwSjnxhrutZU741SXkZx95a6M15OogIhAOH4Z1NOXyDkjgKQY27K0+hcyaTPayAn6U527CFNee+U"}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.2.0.tgz_1499984624535_0.0574110324960202"}, "directories": {}}, "2.2.1": {"name": "hoist-non-react-statics", "version": "2.2.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "6bbca200090c2c7c5d70134512138a84e0170a7c", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.2.1", "_shasum": "a7e41c760121d0abfc7a2339b331d29a26389e52", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "a7e41c760121d0abfc7a2339b331d29a26389e52", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.2.1.tgz", "integrity": "sha512-LTDUMhGHPSsV1/hmQtyXSlMuNT0yRR//E1KC3zcnorg++fx1Vm9vK8y+LLEiWOZr4jQptuyCQ3DzVwt7dmEGoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFsDEYJ5UMbt/m2OlhvPhGlJ1zpxw229cdKQOLi/jGqQIgIWMy3bER5EyVAGjyXvAGH2L8DjDLki0dBizQnuVGhdk="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.2.1.tgz_1500925140035_0.0439110875595361"}, "directories": {}}, "2.2.2": {"name": "hoist-non-react-statics", "version": "2.2.2", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "b7496c8b6f90ed496d059abddd2e8da64b8c0ec1", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.2.2", "_shasum": "c0eca5a7d5a28c5ada3107eb763b01da6bfa81fb", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "c0eca5a7d5a28c5ada3107eb763b01da6bfa81fb", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.2.2.tgz", "integrity": "sha512-PP455sUlHAq0kxZJ0j8flg7T8+eOlASF2/d6tul2SqVHKMm7e7j79FNiSECCHbeEj8TEbcVO2eKrSb/jU5atfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkaXRMUfy4qQ4M30KrTIl1htF1ymSlGCsxcc7eS7HgQQIhAL8ngEyehnfFuuPlB8T83D4hsTMaaz/YaGo0Bu4jKVnc"}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.2.2.tgz_1502214389178_0.25843760883435607"}, "directories": {}}, "2.3.0": {"name": "hoist-non-react-statics", "version": "2.3.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "8d1c06013a50bb67659b55b8404554a25173f4c4", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.3.0", "_shasum": "ede16318c2ff1f9fe3a025396ba06fd4c44608bb", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "ede16318c2ff1f9fe3a025396ba06fd4c44608bb", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.3.0.tgz", "integrity": "sha512-khupcjOitR4Mpa76bvmApIr3nw+g+ZYQbpqd0W2Sw3lzzqSxmuqsYVvr8Xn12iGg6iK935bDGUUeP9hCORG7cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFNDNiILIPmn1za8NIHpuI0miBQBuxQQkO2gf+f1wVBAIhAP22C0IbdELV04ygCa08nJOyfgbrGHe/FgUPJQW8MNWy"}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.3.0.tgz_1503012362183_0.08840696583501995"}, "directories": {}}, "2.3.1": {"name": "hoist-non-react-statics", "version": "2.3.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "d42f54d137f60a24dfd22d207353db546c13676c", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.3.1", "_shasum": "343db84c6018c650778898240135a1420ee22ce0", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.2", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"shasum": "343db84c6018c650778898240135a1420ee22ce0", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.3.1.tgz", "integrity": "sha512-NXtwqX1pbjbM9iiUx02z8FuNYY9TVLx1m0qCnx8VhTx+muuTwPhp4cN+HcnJTJtDYeLeU11IJiwwOJU7sBn7NQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApflVgqK+d861CvkMHEMc7w6ZUdQLGBZluxwdFlexAPAiEAxB48Y7qTAOm8IYJUehdlc3j///iqt4rqkZKbVVMdGU4="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics-2.3.1.tgz_1504135770608_0.4960349383763969"}, "directories": {}}, "2.5.0": {"name": "hoist-non-react-statics", "version": "2.5.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint ./index.js", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2016": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^3.8.0", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0"}, "keywords": ["react"], "gitHead": "a1ebaa4a939af399c343d12083f608803f5925fc", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6Bl6XsDT1ntE0lHbIhr4Kp2PGcleGZ66qu5Jqk8lc0Xc/IeG6gVLmwUGs/K0Us+L8VWoKgj0uWdPMataOsm31w==", "shasum": "d2ca2dfc19c5a91c5a6615ce8e564ef0347e2a40", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.0.tgz", "fileCount": 5, "unpackedSize": 7969, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCT3G/KF6jLuolduKQpv+MYi7MwWXzyb3X80/4ejTp11QIgRxfqTO7pI4QZ+bMm+x1kWx60x8JOLuRwk5OipaWruxA="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.0_1518805296342_0.08159958816136936"}, "_hasShrinkwrap": false}, "2.5.1": {"name": "hoist-non-react-statics", "version": "2.5.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "module": "src/index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "6e6957c5f682de6802c38c6f00c5a618488018a1", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JSHbigDEPwGn2+bEVbijgT8iObjzCi8wHA+RLkFV6Dclvr+PS9tUD/nVxYsAXs0wIIBMztdwNQdsoHPXeUtLUw==", "shasum": "fcf9a5aff62c083aa135deb81c824b3137f00f5f", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.1.tgz", "fileCount": 7, "unpackedSize": 12877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGr4LCRA9TVsSAnZWagAA6QAP/ipQI5Ql8CUaxUz8M0Gf\nTynBPs3Lra1xsHGt01zCJE8j5HNDKcRR4mPK0KqPpSyoo2fR3EX3EZwdxL/Q\n0pXaA2iE2zvyd1xqjcD8h9nLKxEgrvgQQlpDtqrGtGhXCe06AUtiqEQTBD1C\nvfO1Pmf32t2scLGUTEB8GqQ2Hx3J2D/cW+EJ0GtT9T59VfxcLsKny41DSap4\nM0B+nE+lvkSnwsvkFMufx1sVo+On5tfNjUlE1K9bmqmU/PfLSYCQ08Q8gOnu\nCXZ1t3kAQPTRAjGpHl239uOvtuCGbBQlhYu9E1At9VGJGAFWYORZvn8r7rlO\nYcigjAeHg1cPVsV9nmi2Vdqycz9Bn+Mr2PuM7ruZCDbqlPyS8QSSv7NQcgc8\n4IDjo/WGYQIAEF0kQAb/m/JwDo/YAAPe0YCJvbW/JazG6DenTubYtyFEbECi\niP5l3Ny0ZNvXbJvj6AvAVayiJSqDfy/x6DVBbBIb6c4FKCcOAGjISoy0/2n/\n6U+pxvyhRwxvk7w5n0H01YAGDhgnju7qDCnJzTGGsc1ZpjORIMcwPh51pwFS\n5jhevuzzFYofVRlHy245sLHKijthjYIFYLMwxP08raMdeA3zCamuqMKm2AsX\n4b3Kp2awGvuGoKo6dSH6/nKCVj3K3T4jKte6c7oye7PQ52zptGiLgndxdQ3Q\nMahF\r\n=UVq9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEL1G5Fj2QH9TZOBi56whaA/5btYhoXOuzx0qlaRl7FLAiBl1TtfQR3yq8kMWStPYP20wWZx1sesL4/EerRRZUfIwg=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.1_1528479243138_0.7018232278039604"}, "_hasShrinkwrap": false, "deprecated": "Type definitions for this version are missing. Please use 2.5.2 for the correct type defintiion."}, "2.5.2": {"name": "hoist-non-react-statics", "version": "2.5.2", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "module": "src/index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "0cdcf6cf5d9bf52fcd11538152d7e9e1572254a6", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JU1VSsfE56/7NNKR4AadTyMVtfJN7nYkj9b4Bxz+0aIFFL5gEfJwyAG8QLhXPrAcOMe3Bv3XWxjBfKweQaCnPA==", "shasum": "de8381ea0fbed0041cb49d132c1ff654d3c79455", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.2.tgz", "fileCount": 8, "unpackedSize": 13165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGt87CRA9TVsSAnZWagAAVBcP/jxs3lGqwz13xryjxpPQ\nPDl/xxF1ZnOCHcFr3cKnHN05k4ukH/Pjd8mBE8mJcse3yIeg5MNIswYxqGLj\nbanJ2gdYu1Iln0xWhd0eBkBBswPZ+JeeWFMv6rxlCO4T0uKrLZt2uNzlOtU5\n6Kne0C5txU7+LfE5zy1Sld/hnaShaRSpuxRuneBLqwOK3vtQNfTzZR7BcG6Y\ngZbfnfA01iuHwmEK1oN7s+OW+pkskVEpD8C0HxkGmQN21BD6giuScLtqlbGF\nUniDafrqpGuqkTqQXjCVT+Giz88FXEuHDGSbHuPda+PKjIyiXH6CchJ0fOkI\n4prn0MFpi0cdmDFL2od2gLfIHCkdXSugD/ibiiyrJ5ROCn29oTzCYULX2wmJ\nmZaK6nx5SoeBFedBUYc0WLHxY9Hr5OVf1/C+oXPxFv3JMBfBr6I0NCgiLayE\nh88Lg6UpgqlQKPVUqCMzR9ue1aauMegsTcvmspD1Ef3LVUF/vLI+jtfUaZGG\nEhk/a9tQFukkpo0SwdTXfCm/olbbzk5VANm1KAEZfKWke6qgfZTyJMP3qTuF\n17zDw7rJFS/epDIsqezNCjQig2aEM7GpZUz4d3TqMFIQgaHznUCy7ljCMgT7\nFB9CaJSWMv+rk+XdmGB19Xevl/5bjzuljV+GQGw/dEaTWDUymGf7e5HPC/c5\n57Ks\r\n=H77e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBAsfvp4kpai/mGlr25lrDTFz25g3ztmlrwP6meO49PzAiB7MVkzn6Qpxl8i4gCD52K7SaVVsCaZhfnQ4NDoKaRrFg=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.2_1528487739293_0.32697913753816943"}, "_hasShrinkwrap": false}, "2.5.3": {"name": "hoist-non-react-statics", "version": "2.5.3", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "module": "src/index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "6fe5999de25d43b4296f87b7d5224899a0492ddb", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HGQcjn2qN9C40y81GvHhVODr2Ni0Ea2QG+ofWmc0GunB8xnnBK9KXcQSCynQeG5Qu7hBobNkhxY0R4XWu0W+4A==", "shasum": "db97beaf5e03d407ae03c74c1a7451524c051f4e", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.3.tgz", "fileCount": 8, "unpackedSize": 13159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGu5dCRA9TVsSAnZWagAA/eMP/0+V4d2tq4m4QP5VZN05\nsZ0AU5dKQ3oz1W0YkjWESnYy3ijEQpPmRPfM+3+UL50tP9QGXlrglyYGCc7m\n59SOaPOuucN82uWK1xlZoqQpv4anZl82gdm1vGnKFLBdREia/EWrgBV4aYcw\ndKwaflXHeByVc7VWIrvE3hg+Y409MbjhSyy+prdqbmSWSpSvoYP5yJhVTwyL\n/9c2DJkguYyL9qcI/lWjUzE/NTcOwGqp1LsrWA+/AmXIYwcGT8grPwhUh5Rx\nlLG4beq6nN835x0arXRUnWFKFPiwPJxwXZOuC5yPMd1uhHlcMv0VEb/c4Lt6\nP+1vtNhOuTzfxLvz8PuwMlgQXO+TPr7NHzvSy7PuKGeaA+Y3FkA7LRlzpNY2\njZgjTUX0OKy5ijmVeJ2BrAI0W2ae52p+PHTmixOOfmxvX/mqpAxpRBskT42h\nm7jFar/QdMRT6dOXQoVNEN4mJCKBYyvNXu8VTb4tGiMpU/xETCNzjvEBPo4O\nusYdmjgfrNy69opjLZGw4XlaVFyjNV0X40CTF3EdmbvD7cNRLoIT818f6JnW\nv2By8j4Lk0i6wy7Q+oceAAsoFW9AqWIk5u21Bsl+KVESJsTyBXpPUSB4cE7F\nBnfF5QRdkEOlyAl0C4PVkDajzKZ8oWakuWyMMbQ+auFnLxdoPUc5MmyIwlFp\nUXxm\r\n=58OR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID8Ism/W4irN6e8t7KHzaqBbNTKLMOeETvz5UwR7HHreAiEAsHvYfFwO0OG7WM7TNuXG1ehh57P//59ZFi8g/VZ/SgE="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.3_1528491612644_0.8805590959521266"}, "_hasShrinkwrap": false}, "2.5.4": {"name": "hoist-non-react-statics", "version": "2.5.4", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "203ff17f127e4e682c0e61051b9c4feb51ddde24", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yklXtcYj0Pt5Dz9No8xUh7d+/7fy5XRIm+r7U/BXgwJ/VsD75EfXA8t4p9tIL0jykzo5A/sGzt1xV6oqd/gP0w==", "shasum": "fc3b1ac05d2ae3abedec84eba846511b0d4fcc4f", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.4.tgz", "fileCount": 8, "unpackedSize": 13131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHvIoCRA9TVsSAnZWagAACfMQAJlf7a3L3luCTUSmp05g\nD93rSHKoLslB5ecbfUJiCQly3IJrQPFXMlc+l28qbpBkaj/mkIexI/pfKfoe\nxNcsxTWdwETRiPcTzLt5P1ZYBrjHBfQqM+icJ88yAzHiWeESWEqPJdB8q2ic\nY1C8+WZUITGslV0I09kaebCUKlVIROqOiN23MQg7HXnLkCO4EaqpZnD+Ecqw\nL25WSjokD6y0aV8qu+Zoj7JSNz6UaDbGmX6DAr+Dms5/A9jfnTCdQJBnB+3i\nOG0mIiu+oz1rUX/0wENnzfjjojEXma9QiLfvEkLW3nhIm55faIcFMjErtoEl\n/boWpzj/UUr6y6EfD7Hxa3c35ICaFO7W5PgPKrNk2eGDCTw2EZMj8pdvGYI3\n3BKP9b6NonBmxTG4BGwXB3FDWiGsvInb2HCncJvvZtg19s952IAGGvF69qtk\nLOC6wConFlxv+tsbIakng9SupwFFSORfG4gcQk5kWj2wUISQmJMG1Xn4W6iz\nWiAG+noq8DcUjC2q9194uqeUG6PviRPadgKhWvF4Jnwm6Z5rVcj6RY1nAzlm\nJ2/mLnx6leqBrbWBcRp90tIYLW6pgC+/ZpIHXSOhZASWHCaS5//v5MNcR7Qc\nPrRy0uputEiSRdnEIozfvpqkAZNAjYxOtTKfh0cu/1XHR6lWCQ8BWzhCXjet\n0u0H\r\n=MSrc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFBpRsceMga423DfUhRdmD0lq04dP9kmjVcBZjcO7a+xAiAX1dcAjq0hetsZY+J8nPeqEgnFySKfowc/27VCV6nwcg=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.4_1528754727676_0.7277648952531639"}, "_hasShrinkwrap": false}, "2.5.5": {"name": "hoist-non-react-statics", "version": "2.5.5", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --compilers js:babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^15.0.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "95bf2a2abf2112cefb9f1224dc436d1615e5174d", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@2.5.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw==", "shasum": "c5903cf409c0dfd908f388e619d86b9c1174cb47", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz", "fileCount": 8, "unpackedSize": 13137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ+ssCRA9TVsSAnZWagAAc6MP/A9MDQvYKvrFgldJWsev\n4XEc6B3W84rgPkUB9IHBzKCOg/hkdw0ltM0CgzegRN3OR5D2RblLjmNUN7ee\n1SvPAF6Dj7LCmpASFi/B6IeD/44/b2E8DGxJ1TDXmne6KyCNRYv+Q/rXBmKq\nZPL7sf0B3wKrE+O4ukb3pSCESN7KtAozGEGApgL+CnZOKBMukictxks2AS2D\n9t5BrUcwLOM91R/kHFHCCoXZGiyFQKnnysirdlSPRF4CWyQt4do1MRqZGe/4\nIwSwd1YzO8zBm2cArMLGhE9ma0u0wb4mpGVEAkiqYbZjzGizmMTWWrIApcZH\n6pWQiOHchndfS6SxeryH44Z32j0N6MUqo4amgjE2TDCbSWt7JU28eoGnvntS\ntJw6xgX2MALw75lQUTmGlXB9E9jPjD1fVu4juMvqxKHVP1bKxjrix+tVmxeL\nj/YfNKll5Ox/Q7Hs1kE04Q7UmpN5nXWaBGjVQ4N7GA2M2Hl9adWAdOWnkKpL\nffZ1/ga82/mXfWJMLjM6F7G/UqCUlj/RHP60IdeTYQepGxmcMMi4/zL7YfIg\nD5g1hpS13dO+awCHtBGR+JvB3CaiBWppEMXKQ2T55zt40bmJP/EwIginQhr2\nYx5pLyh2JNmqgHQimC5qb03lde9s1vvQEGg5wTxYYqEpoy/4RcKILT4+uUdg\noEND\r\n=N7Us\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtI7YH8nfkHaxAOpZfH6l6ZrYcmAYA8BDWDStxxF24EAIgRYKWrzMw3z9YnkzmklUHFJEWTPq+yI7PT/a16d+iu5U="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_2.5.5_1529342763664_0.9841331052052376"}, "_hasShrinkwrap": false}, "3.0.0-rc.0": {"name": "hoist-non-react-statics", "version": "3.0.0-rc.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "peerDependencies": {"react": ">=14.x"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "readme": "# hoist-non-react-statics\n\n[![NPM version](https://badge.fury.io/js/hoist-non-react-statics.svg)](http://badge.fury.io/js/hoist-non-react-statics)\n[![Build Status](https://img.shields.io/travis/mridgway/hoist-non-react-statics.svg)](https://travis-ci.org/mridgway/hoist-non-react-statics)\n[![Coverage Status](https://img.shields.io/coveralls/mridgway/hoist-non-react-statics.svg)](https://coveralls.io/r/mridgway/hoist-non-react-statics?branch=master)\n[![Dependency Status](https://img.shields.io/david/mridgway/hoist-non-react-statics.svg)](https://david-dm.org/mridgway/hoist-non-react-statics)\n[![devDependency Status](https://img.shields.io/david/dev/mridgway/hoist-non-react-statics.svg)](https://david-dm.org/mridgway/hoist-non-react-statics#info=devDependencies)\n\nCopies non-react specific statics from a child component to a parent component. \nSimilar to `Object.assign`, but with React static keywords blacklisted from\nbeing overridden.\n\n```bash\n$ npm install --save hoist-non-react-statics\n```\n\n## Usage\n\n```js\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\nhoistNonReactStatics(targetComponent, sourceComponent);\n```\n\nIf you have specific statics that you don't want to be hoisted, you can also pass a third parameter to exclude them:\n\n```js\nhoistNonReactStatics(targetComponent, sourceComponent, { myStatic: true, myOtherStatic: true });\n```\n\n## What does this module do?\n\nSee this [explanation](https://facebook.github.io/react/docs/higher-order-components.html#static-methods-must-be-copied-over) from the React docs.\n\n## Compatible React Versions\n\n| Compatible React Version | hoist-non-react-statics Version |\n|--------------------------|-------------------------------|\n| 16.3+ | >= 2.5.0 |\n| 0.13-16.2 | >= 1.0.0 |\n\n## Browser Support\n\nThis package uses `Object.defineProperty` which has a broken implementation in IE8. In order to use this package in IE8, you will need a polyfill that fixes this method.\n\n## License\nThis software is free to use under the Yahoo Inc. BSD license.\nSee the [LICENSE file][] for license text and copyright information.\n\n[LICENSE file]: https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md\n\nThird-party open source code used are listed in our [package.json file]( https://github.com/mridgway/hoist-non-react-statics/blob/master/package.json).\n", "readmeFilename": "README.md", "gitHead": "ac30f5ecee47b550bb1292b7faaeede3e5de6d80", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.0.0-rc.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zBfleLR6QtOp9cE1Q8s9GBW4i2dPy5CmddcMcECK41Qz8lj7VsQTEQ/VZYIL/lPjt3TjNRnoXWYe72mYPPfLUw==", "shasum": "96d3ed0fc8c3ef757c255ef38b97fdb8f8365013", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.0.0-rc.0.tgz", "fileCount": 8, "unpackedSize": 15918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPp0GCRA9TVsSAnZWagAACfIP/25hnXUh9f0V8pG7xEQY\nuIR68FapL3XhbtybqVXs+EkBh9PZJUkLQcJ1jQE+SVtfZdCbACGpjZmLGkcy\n/Y62YXaYIiHBI4R3r4TN4yxj/EBuD0ZAGU7UN/WzDi/B4QjRN0GXo5Hr4Mzd\nHWmTmhMwXjItG+NjFRSmObwsDk90H7AKW4FlKkUe16NEL+kWWdOP2WJCdZtt\nTbOxY8XnxR6WJaQSt3BICNGsRU97Q8ZGeA7dwCEq24YDsfFN/x2WtJ2p1Hie\nBKTSiYC4Ex+F01HDq3bNXOI7oXX6UDh2/RNuQQE2lXg0yT1COvK2gaZvZHwa\nxUVlzJt51NDu88LsVA+RVzxd7Y5HIZ8rzSLDHrIyW00fyLEpgvpglvN/bV+b\nM3D6hmGYdZhHEcPypFNLky1D41k5S/oDcqASFo5T92MDj5DQw747j8HWSBG6\n/1AtGZ/QAOmA9RvRmpoRb/Jq4aHIbIXL1xqQgP/SR5fkRiP1Rsk01wXoHqDv\nzyzP2Y9m/Lxr4saHnt5ms1oE/vYX8sMXY/MMPKemeiJGhGFqQc2ywPacH7iw\nzzO47S7f9WNkw5XPqSVcgcDkBIXILHV/HuaGl/2nvKYaZ2DhYMqneffYPDCh\nIGtj4hAz7lpIO75yFDdYRjExOz2/PMEAvlu0itOQETRvu/W0iIiuTtQ6ZuIF\nzEcR\r\n=OEHx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGpgnHzwL2OEd7uYQ3MjM+3538lE5MC4aWoXs6UUjHC6AiAB+8d78b9mh7luIcE54XbjE746s0NANgIuOa5JxfoK4A=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.0.0-rc.0_1530830085422_0.5365463350516828"}, "_hasShrinkwrap": false}, "3.0.0-rc.1": {"name": "hoist-non-react-statics", "version": "3.0.0-rc.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "peerDependencies": {"react": ">=14.x"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "2ab3d3374dfdf7cf8f9cfc070e21f879072a1740", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.0.0-rc.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Lm1f/VJw5v3hhX+EUinbujFa+Xg8gFRpeR8fSlXgCC+5WCaFUkO5BEJpJdeDcFahv9uGLqHQLIVl7TUXRCv6vw==", "shasum": "71896dbb85721986815be70734c8d5e5cc1c54a9", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.0.0-rc.1.tgz", "fileCount": 8, "unpackedSize": 15850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT7BTCRA9TVsSAnZWagAAwYgP/1nQxiF6OCy0qZYuEDIx\nbZIFSY0dleHrvCQlLlKCsjRzE98M6IloJK6f6Sxe/UCszmk0h4SvyN7t5PgN\neEUM4TCWK2HnNtifbH96FgHc1r5MKbr7rDzOXYjMvhu2uVq/q42CyfXARMcm\nGRXOJLswOTYWuimobNQUy7hUZmG7PCRKcoWJz/J+qklbWQ24svzNSReeZJ/n\nbBwNZhjdXaVF7vOGQ03bAfuMWvhRrRIPo2wThOj2UMlY9BNj9CftU6tWkdl1\nS58uYiw3yJKXov3WN0zOqLav0UmrszyuGBL/Mhkmj50UM5xIqssgrNQBb+xo\nHLueH9tljy6RC1iiPi2V7/Xo0CGnZEN8HJ9sv73pznXz9MBrdq/fA+zvTLq8\n4dOPCntj5Md5tQcq8LNbnoCPDoZy/FOOVvGD1Kv0unNjey9c2Zzv4EeE7mk0\nY/j3T3t2sC/nTFuxUBFM0YaKF58s8szJwmVMIgHQNXYIkKmE7idAFk7QyfiT\noDGTGtihRrvQqFjQ3sS1QgUc5Tk3zgMveLwRhcto1JFocJi8Mi6crBPtd+a6\nO1HMj3FbWKrhASlz/PdIaZrIvWm3RIlzcLhVm1yXHf4TCINhHqqzJG8doNM/\ninW8d3Gy2JhYjSgl0EFL0okrska9OSHRzKrQHY+A7jLb1IFKlo+tj/Fg+YAZ\naH8d\r\n=hEzl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQUIVwOJ2mZNOtzwMh8dOcXWkTy09has1lXSGvK/GxqAiEAlb4T1S0Sqy19iwc1nR+J2n/zU6uBj1J+cvz6WcMz+10="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.0.0-rc.1_1531949139905_0.00924442909978418"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "hoist-non-react-statics", "version": "3.0.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "peerDependencies": {"react": ">=14.x"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "94ea24cec171de5411b7299da60019309ed4e1d0", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1CgMxqs+l24nnyUG4+z22t7KFLQw5QN8mtTxIFKhKeQxGQyMcnJWaiU4/iU6sofS8Stdj1QC8uidTDROTRYMfA==", "shasum": "7e2c2804aa81af3d11d261f13d23153625c68b84", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.0.0.tgz", "fileCount": 7, "unpackedSize": 15645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWlb1CRA9TVsSAnZWagAAZWsQAITmoeMcJhdnJCasuJtj\nlqZHsLF2hgnHaTbIY7O+nDJj19iVhpj+3C3SabT3Rm4wV4z0Tn3lGJSRmm7y\nQM59PMWohHey48PJQ93soiwmM0qdhZ2JVU9w/a7/v6yrrY9whCboeIU9SskB\nqnZwryCYtrRfTLnM/rIM2HkEpmP7eoRuMHS+GaXl2HN6H3UsFoXyq8N2bmI2\n5qZifuTu4qNZe3QPV/IaWCzfOVWmBLERsUjtGRg6YuKa51RikKmoj48a/sY0\nXBbLkrOIU3O6XjmZJGEd3a++zX9tYKyTxVrws8qEb8JaG6W3aDZnxco/BUsi\nDfKL7SNQrvtrw0ydHRsfpLVs6W2+Cpfy9MMv5QuVTyhgevf7sCpag5oPYWgi\neoh7G3bSCJxyh3im9egiL83AmkqAmZGNupSOBicZo7Mdu6sboDqep48jcp+g\nB4w13NdLpKpkCZ/kgIfYoRRg232soZcotHazgb+itxgP1nRDc47Mr9qn1jE9\nIJZESF8JaKMaRvjdIxkul2l9pkdc+Gc73Ni69jhYQWmaC0N7zCVb6LKRr3fL\n3pRVUKMRekSz+fcoIg+gZfZPAxsS/lVUt7JSGNwkMpjGoeXjW2gW6moA2xW2\n2DBklZvyfoxbUvWxQ2F+geFzZMSoLZ9ijWdpqeH4LRyv9q8LkthLTlWkkMkP\nV9M6\r\n=qtb6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJlyZYJ98R8BSEnDZ33zPzFleENyaYnyUTIKWci4QE1AiBdwr0tQ4q86tOPAvdmI9AVIgVegNFWT4ZxvCY4NREoKA=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.0.0_1532647157101_0.6741266324696127"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "hoist-non-react-statics", "version": "3.0.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "files": ["src", "dist", "index.d.ts"], "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "peerDependencies": {"react": ">=14.x"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "ea626e699f483e332f5c1f5cba10df2cd2096e83", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1kXwPsOi0OGQIZNVMPvgWJ9tSnGMiMfJdihqEzrPEXlHOBh9AAHXX/QYmAJTXztnz/K+PQ8ryCb4eGaN6HlGbQ==", "shasum": "fba3e7df0210eb9447757ca1a7cb607162f0a364", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.0.1.tgz", "fileCount": 8, "unpackedSize": 15657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXNljCRA9TVsSAnZWagAApPIP/2iwN+ZzkzV53eHMZhGf\nTC8+MAJE4QTDUB2lC6+4ttEqVwqZMDSQpWd8YQo43k02cI2MgwC+JDoGEWzS\n0lsZ1aivJ4doYqxRsqQtVx45FlUJ37C3evo7tSp6XuAWRWHHg2R5m62a/+nI\ni7edkJMnNfRfUFklBbRd2LuFcRyJS2qq0ODvOLW18oGRlYTTOMaG6943vZYn\nF9VBn7+gr8tcT5cTYqXrf8eEUXvIVbkvivHOG3O3FtywVsIZjJZe80CySd9+\n1cMRireN3wtLjnYvgqsczcOPG3gUAtaKpc3NreYdBBCiD6ysuzRkojsXbzxl\nCFOl9gaQdBKs49pY769uI/gTdVjobfXDxFTBlRXI5rc7mIFW9SIfKBDQTeuk\nrlsJpudBwnkWrCU/r7tsJUs941zKREFwRIHXrlAv0DXf5Z8ig6tVTzhn4YdX\naDqpYvgG23U8EBM3o/QLpC83DhVUtm2uLCtuUUHP6m9A3Q4ExKEtYyzUIn3Y\nfWA1hOtfB9NV6p0LqRt7/8ld6aouLHnVDjC7FcsZZ2gKp0nd1HTHk28N5HWI\nXs/9nm3pjwjCCUSdIALJf3OLThZfqsa0rT4kmxZMOmkGJPvaZqZleFRaxnFp\nw0s0R0hpYikOJPv3+VYW0muZJDvprSpJqeF42eJVJBJoeFrMR9ukLZJMMmh8\ngbXN\r\n=14IB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBR6m8vYcN8CvHwQCYdGJXLarGwwq31RJIi+7FidfX4QIgGX45xMDSMxLDY2oVtriA9iS/e4Axmn672amUsRebptQ="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.0.1_1532811618966_0.4109228638007365"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "hoist-non-react-statics", "version": "3.1.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "peerDependencies": {"react": ">=14.x"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "6a08c7b70081a92869cd845bf8d94233597506c6", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MYcYuROh7SBM69xHGqXEwQqDux34s9tz+sCnxJmN18kgWh6JFdTw/5YdZtqsOdZJXddE/wUpCzfEdDrJj8p0Iw==", "shasum": "42414ccdfff019cd2168168be998c7b3bd5245c0", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.1.0.tgz", "fileCount": 8, "unpackedSize": 15008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb16X6CRA9TVsSAnZWagAAic0P+gJWSBXatgBqMdTrxeJ4\nN7++VLFlS7MwY5o6DDggU+1ZmmKYu1BOH0CBZe2s50Q1lBRiVff8c45HMJqe\nCBR+/uMyIrT4UybVtk3msmus0TPaxPxRtytlCy5pvufrO2di0O7ds2h4RZQR\nPP6S+7ztiMRnaB5wLF0r5ac1DygCWP6Fl1P3YjsGnN5SwzLYcG/tNkP8VQkT\nphbeQgLJlEhDOiuqwuJZaSSrh4iNgzdoez5zgrIMPXSWLYMt1ChU9yi2CPvs\nGrqOnTZaSV73Jf22lW6jDdYK6hpT9t01y4wchwbtMYUv8vShHHNU1QUs6bt/\n73HRYfN++qgTz4EOAPwSyiDYjDCO3QKg9VGGJL+RgNB7Ttry8G78L2pm6Unb\nSQ0F0eDzY3ZpKimQvKfUWvERBXbI14yTXzIGjIMan5JiCryF6MVlnL/Qa5nT\nJrh0TuRBqJ7MZ6Pgg3gzD+6GbGi5Uq3C5N7XIsG7+XuGRBoidABSgV4TYpWF\ny9c8YHi8o7hQOdfYvTiNScLm8VRg5mcIEKrM0ky2anv2HwvqAnn8/3lmUMtJ\nMcfFQP/Dp28uNO9JeX7QBdFvPfsG5LF2EkPb4Dt5n7Dgd/wqtJOT9qDPXdP2\nUsynkrwSSiZBZ7elDOUapeGavTfpcIqIqZfmg8nAFdjAEfgfxq1aqzQ4gjRm\nHBAI\r\n=wM9p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2lEuJfIopbQNnqTf5Nb+mJ1R5IAYkfCaCH1eztb+eNgIgG6/hNRQ2muuzT8WKH68cNY5/ebIC8pYF5diIN+cmne4="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.1.0_1540859385817_0.9136268766377968"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "hoist-non-react-statics", "version": "3.2.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "6fa52d51faa781e7c4d3e31704eae0b79332104a", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3IascCRfaEkbmHjJnUxWSspIUE1okLPjGTMVXW8zraUo1t3yg1BadKAxAGILHwgoBzmMnzrgeeaDGBvpuPz6dA==", "shasum": "d21b9fc72b50fdc38c5d88f6e2c52f2c2dbe5ee2", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.2.0.tgz", "fileCount": 8, "unpackedSize": 14944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/GulCRA9TVsSAnZWagAAjWUP/RP4jU0hhs/e814gmHv5\nnJaBWdoNFRNDh+wGX4iOhZSAJq5anKBig+qJTfVvZ930Sc3yfyy8xXi5U+aQ\ncxQrkakdZEAA4RnfDUqFs+eU2Iavshpl8oYPAqNhh7xAjlX5HD0TqLQS8gCJ\n5+uLmqGpa8ZMPQOSOKxK8D+i3E7gx/puH0FYc+bSAQ/g0F+JGTguXMK+VabU\noPevylK+pPt5KVtjs6/IC7f57lLDyj737Bcro1araMO29nuHd7uIOB1TI8yP\nKUY99HcJTsnt7AXvJWWPs3FauZdALE8m9eGn6+fU4qg6c9hHzZnEKrZMfgov\nFueiDQhh48Ow4pmncgM3LEAk4AkbWJDcbuv9q6w4fefBxV3WGwbUofWg4KVe\nG8YeC+/B75KQubGr4xob8aHw7iCNPTY/SsUCFoOVDOa/lS0H1Que1MpFvmyT\nFJNpxKjx++eDELnvteuYmx7B6frY93u4DpiUOqPlyxFm2t2Z59iHX7EPtCh/\nVtkItTSNg4Q9hZ3RnBgmCjxyCA+M2JKsDIdxje6L8xKwXghJ0Rliaiy5P0Ea\nwFd2wOtiuTdzxBtVfAhgvpnJKsX2n/5aEFRTWjG2iN3Ip7bJu54X4jDhgPc4\ncSCIkwb0ZNvwzxlZixfQSRLdEUOda0SmROd9v2xdCON0w0Y4IaKrlh6dceLk\nIfqw\r\n=v2gh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxtflAuk1q0UMfq2iDBCDOzvOlybSJVxInChSFRp738AiEA42pubMyyVOVOGpjHLeSeRDtKR/mADhRvK6YNtWxf62g="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.2.0_1543269284719_0.4948119731341467"}, "_hasShrinkwrap": false}, "3.2.1": {"name": "hoist-non-react-statics", "version": "3.2.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.3.2"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.3.2", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "6c5c23fe768e03e82b64dafa843994ffb2cd016e", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TFsu3TV3YLY+zFTZDrN8L2DTFanObwmBLpWvJs1qfUuEQ5bTAdFcwfx2T/bsCXfM9QHSLvjfP+nihEl0yvozxw==", "shasum": "c09c0555c84b38a7ede6912b61efddafd6e75e1e", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.2.1.tgz", "fileCount": 8, "unpackedSize": 16410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBXZBCRA9TVsSAnZWagAA494QAIjvBXenFJynFy71aFHX\nZy4vNeSc3Jlo7ZjefjhH/78AAKrh3xLfSDz9atWFMmuHDedor/XDUoun3TVs\nkTyZxvXzvjLo7ywXqTi5ergKAHSDrcDOjCWX7aLoM6M0542z/kd1TAwCTdMV\n/5PGTkseK2IzfNYfP3pKkIevSBUjnpAIkPzbozZMa6FtfWGpQU0FltkyJLJs\n67mDmwVwAkfkJEj8QUfUohUAoLrnhuvnBr3ccZsLh+O2dxv2l/bkt9eXsjxe\nTnhcOgy6MAyNbkoV3hEJteKvbstTLQY27j/iNHjTrt3JwO21GT5BHhLGGGSR\nP1/q41u/8SCOUaTJ+BR+EPWlfTKGuRr6y9WDTTfc7sxb12qpkPFI6lXn26F4\nKP6TSO6ZB0eKbH71Yos8GxoW9N3+22RKINBbGATT9PQ8ImggOvDw8KPJhURh\ndGwfgroVp1RSam3PbFKbbv0KLWQ4p5DsKS2/08v7U+ngqB2B2tLbQUvKwEB9\n+NBAv8hX2iVXX402EEZOha2NVL56nJNuJ67kM1ZqFNsiMEmyE0JJO04c7dTz\n0qqYkDQM/ziJcSNdFecrYR4Ft/NJJrbwONs7Q6kCy75ppXvahfCZN9DEh16C\nB5n8pxJAuHFxJ3dI8V0d6qxY/97g/DfQDhl3XQK8MDGOOM5hfBDzgtzhZURh\nFaDg\r\n=j4I+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4o+y6WLEP+PD2SNBOzjwmrvnvrENB+2rPvf0hmEVXGAiA6kV+46GsmBR4QDVCozD5yMbVvcFATWsD/iLjoNlBE1g=="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.2.1_1543861824953_0.8427887880714338"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "hoist-non-react-statics", "version": "3.3.0", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"cover": "node node_modules/istanbul/lib/cli.js cover --dir artifacts -- ./node_modules/mocha/bin/_mocha tests/unit/ --recursive --require babel/register --reporter spec", "lint": "eslint src", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "pretest": "npm run build", "test": "mocha tests/unit/ --recursive --compilers js:babel-register --reporter spec", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}, "devDependencies": {"babel": "^6.23.0", "babel-cli": "^6.24.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.1", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "istanbul": "^0.4.5", "mocha": "^3.4.2", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.7.0", "rimraf": "^2.6.2", "rollup": "^0.52.3", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-uglify": "^2.0.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "4fbd062b55ccb657b631505728bc043000ece9a5", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.2", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0XsbTXxgiaCDYDIWFcwkmerZPSwywfUqYmwT4jzewKTQSWoE6FCMoUVOeBJWK3E/CrWbxRG3m5GzY4lnIwGRBA==", "shasum": "b09178f0122184fb95acf525daaecb4d8f45958b", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.0.tgz", "fileCount": 8, "unpackedSize": 17374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSOpmCRA9TVsSAnZWagAANqIP/ilsR/etvK+NdYflfB68\nVVvySieVQYWtwAWLxgG4SXmziY0uX0zRneS1/JCbrypTyZJB9TQ9t4YtJuhO\nGBOvxTOnt1Mp3e7XYFolrNm1Y7RiovqURCirUdPj/6k6+Aq5/EL+pa1nGMyn\nhNuOYbCEWQOhkWRC03UxCgx5uCd0CkHu1bVARHWtINl8LnvvDn7GEoeelyRw\nRYT1tlTkk7umz6g2ql2RxWofhVYPD4hSc0xjbFz7Y2SqlkQHkBNK0tm3cvth\nKW3HRu7V+Ppzhdn0RtMAE6oC6W6r/IuPkKOkZE2ArWQ23wZKB38Enh9rW+Ve\nH5j3kMyqh6yUxpbiK/YQq+PXF6EBz1ctoUpI70Koxb1UGCd4TNcSVfMaXcF0\nfjnjNQ+RKDv5d3MTW8zxlwDWwBMMEMJsVx+lY5BDYRgBAu2ge0l4kPk3/Nzp\noNe5uwyYi7CqJIJf4EeAyUF2KyA0SpvKFYn7BFh11xldVxqteBKLR+xNMkvh\nreW6lYUXlPMotoRJZ1hSiw5RAv5Z8ZhSLZ2B0M+A5G6+4KQOvLd+jy/Ss6aV\nM8Z/M4uzrQ/BHkRaukduchcG1yg+8KabUPJdZPbL1N9ceZ4kbfWagDnIz7hY\nQQRC/P3gIsW962ydfsZgQPUwfAz0XhW/R6DAd/5aynrDchlFI53SVn4GUyNS\nxQ2n\r\n=uleK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmljDK3fLvNWdDc/VOf2UAfDcBq/OG43UHpgVaw3zH2AiEAv5czWuQfGWN9p5qxdQVPpt1F2Y10YCLsDeW4vKOAQV0="}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.3.0_1548282469761_0.11975113081366762"}, "_hasShrinkwrap": false}, "3.3.1": {"name": "hoist-non-react-statics", "version": "3.3.1", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"lint": "eslint src", "build": "rimraf dist && rollup -c", "test": "nyc mocha tests/unit/ --recursive --reporter spec --require=@babel/register", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/plugin-proposal-class-properties": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.4.4", "chai": "^4.2.0", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "mocha": "^6.1.4", "nyc": "^14.1.1", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.7.0", "rimraf": "^2.6.2", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "695c9884d19133dc97b2b2d37118836c03abcd41", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wbg3bpgA/ZqWrZuMOeJi8+SKMhr7X9TesL/rXMjTzh0p0JUBo3II8DHboYbuIXWRlttrUFxwcu/5kygrCw8fJw==", "shasum": "101685d3aff3b23ea213163f6e8e12f4f111e19f", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz", "fileCount": 8, "unpackedSize": 38460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzbx3CRA9TVsSAnZWagAAp4oP/2V/C4auFq58xjoKxAcg\ngsLm5AsLTzJCuMLtKcTQyVjTPVtZK1pShVZwNjgDsCMIKbwNB+umlV8Nt2Hl\nXOvUBVJBYPKQAwZn9YSFqx+AH+qxqUZ+M67f6AP5ZHNhcddMf3jm4h9DF0gv\ntrI4lKNRZPg9yI5ELJhSDFX4Fnr0gnWBoWoMAGesKd97n/sF+2xy4vOnrowB\npamAdfZtT7uoe7UPQU6CIkQZMBMTznMa73wkeErPylWr2GR8Q1Zpcv4maDkm\nZWVce4CcMO3CqtVcfEL9Bg3dFRwfiZHtFrmQP8RUkf9DCbz4cw+VVU8ay9qQ\n2v3EkLG+HNdDYXHQVZepaNOymwqJDrXZsk8F7PjwnkX7D3k4xsEWwElSHfu3\n6Tqfz6Dpftj/T8fiRLeW1jLUT5Fm/FTfGHLwM6IJG/org8YxOIJ0bJ8G144l\nz8hOo6m4YR4OE9WQh7DwNEH5t52Hi6hw+939IdHPGprrCdcHRU83rhV4mkUt\nRxVRgUM3/ShlCm45WJmPLvxflRNhhe2XIam6BBDO4qBPNbnA4Enw9krFzCJB\nxkRZzrDj+gJTXZIm76meVpX1LW/iBmkvTC5WBR5jICYdh83wWKkbXEgZcNIV\n+qHSZ/pUDJidqkTMYb4C5Qn6igdQE1wm21Dvqq8XPoeZdNL05PfjsxgawQMW\nVXna\r\n=BSeK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcshVGsxuX65GaVgdqi3Hll3urts89qHUH1/O6PTZBPgIhAND0Rl7B6lCxwwtMii6OMXbI2PbjbqX2kjy5BnMnuXba"}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.3.1_1573764215403_0.7545523176426623"}, "_hasShrinkwrap": false}, "3.3.2": {"name": "hoist-non-react-statics", "version": "3.3.2", "description": "Copies non-react specific statics from a child component to a parent component", "main": "dist/hoist-non-react-statics.cjs.js", "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "scripts": {"lint": "eslint src", "build": "rimraf dist && rollup -c", "test": "nyc mocha tests/unit/ --recursive --reporter spec --require=@babel/register", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/plugin-proposal-class-properties": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.4.4", "chai": "^4.2.0", "coveralls": "^2.11.1", "create-react-class": "^15.5.3", "eslint": "^4.13.1", "mocha": "^6.1.4", "nyc": "^14.1.1", "pre-commit": "^1.0.7", "prop-types": "^15.6.2", "react": "^16.7.0", "rimraf": "^2.6.2", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["react"], "gitHead": "1b5fb0b06856f196544ff2dda54c9a15e451522e", "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "_id": "hoist-non-react-statics@3.3.2", "_nodeVersion": "10.18.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==", "shasum": "ece0acaf71d62c2969c2ec59feff42a4b1a85b45", "tarball": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "fileCount": 8, "unpackedSize": 38855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKNjfCRA9TVsSAnZWagAASo4QAIEWBN64V3XRbCXo3Hls\nHmjmA1SnSRvVD7B1I3iwE3OQHjl627Kw+ToEn1DNNN7b8LvG7C7XS4XmJEgE\nYiFCGA70vdivXjcyMUZzN/JK8IWoOPJyd0/tiDclCsvZA/n0R+9pbBA/bEri\nyZMRCvejX1q6VUpgAexTRu0WTVA9dtqDR8rprz7qRgmZBRKJ7yIOsKIPZMaS\nftewV3VeLPhQyx2Kv+CrBEs8haLvetP8XjqSsyjetC+Y9eiBKbAoLeIAV59o\nHyiQ7cDuknhNQQJucHv1u7jUKKVChGPctGtZr0U20gczZCNoJekhMS59H8ep\nyhIX6mpM04AwepO2MzyUlSONbcGAsgRjVa9efVz1xZoEchkFUaXygyHrq3Na\nJtjn1UT7PTgde/O1T6kW3hKnR22GJ4LjdfknqnW1jv4LVYYjSTTwOyKYQcNk\ndxASQCIsFeRywLosutOIUexDsnesRBq7QRMq0u5DHjOQCsvSiGNLqfnLHC++\n8jiWbRYaKXMGEmLwcNo5oJ9nDG/ZCUYmt5e+mqzHuhOq+RIheDoORaXc3O9v\nYu2iTFsyzcc5fhzogWyBBYfU4qEucbxgGEsCsCM8G2cVbbvUOTPo81s88kDD\nASdpfCYIAXzPAkQaBY0XIvLVm3dEhbHO4Wm93rHr4X56B1RgCjt2pFVZ1Cdy\n3x6H\r\n=z1qH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiD57BDuajdt6xRzK662OzBWQHl9CzHPPP3uH5eRCIEgIhAIOXKG0QBwqnhw5vv4GXQMzN/AFwVzxnpp6t5b1fLcXl"}]}, "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "_npmUser": {"name": "mridgway", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hoist-non-react-statics_3.3.2_1579735262827_0.006688766803461865"}, "_hasShrinkwrap": false}}, "readme": "# hoist-non-react-statics\n\n[![NPM version](https://badge.fury.io/js/hoist-non-react-statics.svg)](http://badge.fury.io/js/hoist-non-react-statics)\n[![Build Status](https://img.shields.io/travis/mridgway/hoist-non-react-statics.svg)](https://travis-ci.org/mridgway/hoist-non-react-statics)\n[![Coverage Status](https://img.shields.io/coveralls/mridgway/hoist-non-react-statics.svg)](https://coveralls.io/r/mridgway/hoist-non-react-statics?branch=master)\n[![Dependency Status](https://img.shields.io/david/mridgway/hoist-non-react-statics.svg)](https://david-dm.org/mridgway/hoist-non-react-statics)\n[![devDependency Status](https://img.shields.io/david/dev/mridgway/hoist-non-react-statics.svg)](https://david-dm.org/mridgway/hoist-non-react-statics#info=devDependencies)\n\nCopies non-react specific statics from a child component to a parent component. \nSimilar to `Object.assign`, but with React static keywords blacklisted from\nbeing overridden.\n\n```bash\n$ npm install --save hoist-non-react-statics\n```\n\n## Usage\n\n```js\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\nhoistNonReactStatics(targetComponent, sourceComponent);\n```\n\nIf you have specific statics that you don't want to be hoisted, you can also pass a third parameter to exclude them:\n\n```js\nhoistNonReactStatics(targetComponent, sourceComponent, { myStatic: true, myOtherStatic: true });\n```\n\n## What does this module do?\n\nSee this [explanation](https://facebook.github.io/react/docs/higher-order-components.html#static-methods-must-be-copied-over) from the React docs.\n\n## Compatible React Versions\n\nPlease use latest 3.x. Versions prior to 3.x will not support ForwardRefs.\n\n| hoist-non-react-statics Version | Compatible React Version |\n|--------------------------|-------------------------------|\n| 3.x | 0.13-16.x With ForwardRef Support |\n| 2.x | 0.13-16.x Without ForwardRef Support |\n| 1.x | 0.13-16.2 |\n\n## Browser Support\n\nThis package uses `Object.defineProperty` which has a broken implementation in IE8. In order to use this package in IE8, you will need a polyfill that fixes this method.\n\n## License\nThis software is free to use under the Yahoo Inc. BSD license.\nSee the [LICENSE file][] for license text and copyright information.\n\n[LICENSE file]: https://github.com/mridgway/hoist-non-react-statics/blob/master/LICENSE.md\n\nThird-party open source code used are listed in our [package.json file]( https://github.com/mridgway/hoist-non-react-statics/blob/master/package.json).\n", "maintainers": [{"name": "mridgway", "email": "<EMAIL>"}], "time": {"modified": "2022-06-18T21:04:25.037Z", "created": "2015-05-27T23:39:34.473Z", "1.0.0": "2015-05-27T23:39:34.473Z", "1.0.1": "2015-05-30T23:31:11.202Z", "1.0.2": "2015-06-08T23:39:14.515Z", "1.0.3": "2015-08-10T20:39:03.169Z", "1.0.4": "2016-02-02T19:13:04.294Z", "1.0.5": "2016-02-02T21:13:42.645Z", "1.0.6": "2016-05-11T18:28:58.280Z", "1.1.0": "2016-06-13T17:03:20.703Z", "1.2.0": "2016-06-17T17:52:35.684Z", "2.0.0": "2017-06-26T18:21:58.990Z", "2.1.0": "2017-07-10T04:22:08.821Z", "2.1.1": "2017-07-12T17:09:22.082Z", "2.2.0": "2017-07-13T22:23:44.625Z", "2.2.1": "2017-07-24T19:39:00.128Z", "2.2.2": "2017-08-08T17:46:29.394Z", "2.3.0": "2017-08-17T23:26:02.319Z", "2.3.1": "2017-08-30T23:29:30.683Z", "2.5.0": "2018-02-16T18:21:36.410Z", "2.5.1": "2018-06-08T17:34:03.214Z", "2.5.2": "2018-06-08T19:55:39.408Z", "2.5.3": "2018-06-08T21:00:12.741Z", "2.5.4": "2018-06-11T22:05:27.766Z", "2.5.5": "2018-06-18T17:26:03.794Z", "3.0.0-rc.0": "2018-07-05T22:34:46.068Z", "3.0.0-rc.1": "2018-07-18T21:25:39.966Z", "3.0.0": "2018-07-26T23:19:17.169Z", "3.0.1": "2018-07-28T21:00:19.081Z", "3.1.0": "2018-10-30T00:29:45.977Z", "3.2.0": "2018-11-26T21:54:44.972Z", "3.2.1": "2018-12-03T18:30:25.199Z", "3.3.0": "2019-01-23T22:27:49.888Z", "3.3.1": "2019-11-14T20:43:35.552Z", "3.3.2": "2020-01-22T23:21:02.975Z"}, "homepage": "https://github.com/mridgway/hoist-non-react-statics#readme", "keywords": ["react"], "repository": {"type": "git", "url": "git://github.com/mridgway/hoist-non-react-statics.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mridgway/hoist-non-react-statics/issues"}, "readmeFilename": "README.md", "users": {"koulmomo": true, "dreamanddead": true, "tedyhy": true, "donggw2030521": true, "nelling": true, "ta2edchimp": true, "shakakira": true, "fytriht": true, "joaquin.briceno": true, "panlw": true}, "license": "BSD-3-<PERSON><PERSON>"}