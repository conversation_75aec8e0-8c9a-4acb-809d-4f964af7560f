{"_id": "@edge-runtime/vm", "_rev": "102-9120af52c8bc961292ae3b7a63e27f6e", "name": "@edge-runtime/vm", "dist-tags": {"beta": "3.0.0-beta.14", "latest": "5.0.0"}, "versions": {"0.12.5": {"name": "@edge-runtime/vm", "version": "0.12.5", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/edge-runtime#readme", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "a0e6f56ad0112e89de4cf7a2e0c27ba1c92225ec", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.5.tgz", "fileCount": 20, "integrity": "sha512-Ou2frfjrZwy9Y0sUsjRVRxy/r++bioKwmH0UDsX/ouGve7Vq+aiOsPJ2dLN1sIb1L/DXtBXTxkrWHu+lX6jOvg==", "signatures": [{"sig": "MEUCIBI+r4AdOCAVtveTXecCulJfWMgU3igPm+1PF2f6rrXDAiEAhcH8KkAcaq3LHc7ypFGixIHCt6PxyB6C26OO+J7LdGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJic8ofACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO1Q//d5oETPixIfWqk47ME/J/FWEE7IvxbvBXd1/9GulgAiKeGlXq\r\nQZTQlDL1yUGAxtve+XzOdAqInrL8+fbjKmkeeWRqXnN9DnUnvNFNn1/LemrS\r\nvI0jvhxxScQHm/Ib+3uYNaT4hCc1i6eqgA0/uaHZvTfCkYp11Nwm2wFFSM9j\r\n3RZQ2GoxgwDcSPfRJJx2y1aB99pr9rdeB8T244nU9lcfpt4iH2lSITRGVYyM\r\nn1gltyF6DD/nPzLbJSTSiFeKiu4L/SAtXG3xiueS6Tbr3pjX92/mb0gKHmJR\r\nMz9gLckkuBHh3mKTJmruDB0YwbQgw7XuFYl/RL4xSQSjhXhT4D8r8Grdvt3p\r\nl8sixfzLbZNsVaDHJzX8g6mq/w5sghQOYEi3EaAgTgbn7+juK+dP9QIauM2M\r\n7+X6lL5X/8VtOgb3e+oG+bJ/EowwLBUbiCCB2NEWJ2dg9yymV0mvoqZXZB+i\r\nS6afMh68bZ8Z7HvfuXm5fZ+vpVLXNfi53o8iesQ3bEYipEbzteYqy+/+7zl4\r\npLoBejshU0pUD0l3p2+YWzf8XQfeUcuNA5v2+RGD/FnMgBT+kI+G5OB0ziIv\r\nqxpIWXWA9LJmArI4mwGsD/pn5L6WlqEdS2xLs+xe+IcO5fPLbLc2m4pGPhna\r\nPFJM0CeqrBKn+drB/Jy4LmrrFykQ4+9mxFQ=\r\n=3ZKh\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "2c5ea38428e42cc858562aeefdd92347dbd5d3ac", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.1.1/node@v16.14.2+arm64 (darwin)", "description": "Low level bindings for creating Web Standard Contexts.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.5_1651755551435_0.3353876277997907", "host": "s3://npm-registry-packages"}}, "0.12.6": {"name": "@edge-runtime/vm", "version": "0.12.6", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.6", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.sh/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "7acc593d3524da0714ab2da3cd66611e0b736edf", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.6.tgz", "fileCount": 21, "integrity": "sha512-tIT6jIFIdIbfOyB2MqGbP0y7IscFtFCVYPOPrkjLncGe4qvGzIHikjJopY1LNnEKbhfJ9EL+U5DpI/2VxezMMQ==", "signatures": [{"sig": "MEYCIQCO5glNmmAetJX/D3Z0Y54PU00UXEH57mVnFi6dIb0rUAIhAPV+PfottKW53TiomXLGlCeYgTNJHSj9x0GZzTP153Cu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJic9hRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/UhAAntacBIggKvjNr8RAA4eaEklLHY6fhNfS5FG+J2//fv/1T6Pu\r\nSXWI0C/39ieYezhonvt+qxvKL3ZKtHOM47ZjYnTtlmTpKkcfT/87BcCFgS/y\r\n6WIcwu81Vug2hTyB4aGkhkDopgKPFB0Ayf5yNPEwACUPzz/3D5QtezDewiWp\r\nJjFGErKGqlPJjQdjvLXhYtpQN6mREejI96Ee79FuoXaHCMkqf1XEpR/Ge0X+\r\nZSBl4PG+kgTodAe1iewu8DRdyQNPJAv1+cShatCjIRICXi4gd7otjD5RhQEL\r\n27T/XuLa9ZIDm/ZADtGuMLkaCGhr1p7/8Q9dns93cya7wFAj5vL+augdSEXA\r\nQQjG1cBLkE9MaHEquSSqHM0EYeioCKhIFv7Kz2eg1Ao5a/u9cdtg17WQq6WC\r\nJL+TKDePAVnf0MxL0Gx9TseCcskT2KgWLSP24duLP3HKn1GZYoW6fyFn2vm6\r\nlPJJvH701NdxGGXvs21hjv3DUDYVNIz1sgaU+YRvnqjJW3AjGkrW9Bri08/V\r\niUCPzc95iXXBfiNM25aRAZdQVM2+qCkr9/iuJ+coZ9NvyHQ3zmQxAL8QQDTT\r\nYicOlh8KWFgc1nWnVsvH7TM4mMl/Ul8VO5jDBLByp9lN5a6gdCOHagWYPCAP\r\nyG9d4R04NKitBKXkJ37VfES+uDMHUrNAARQ=\r\n=M+Re\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "a0b1ff8bfd474af9c201022d35a2fd869e79773f", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.1.1/node@v16.14.2+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.6_1651759184968_0.23384407252585993", "host": "s3://npm-registry-packages"}}, "0.12.7": {"name": "@edge-runtime/vm", "version": "0.12.7", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.7", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "26934055265b752d2e704ebb009bfbdc4012de7f", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.7.tgz", "fileCount": 21, "integrity": "sha512-ziPXMzyABZRJTPKGOzc8wXk2x8FgADfj9lOe+sm5MizK9iTpIDF5i4EGrmgKPE3Yd4Ki9v5KN8WA+WS9cmo1Zg==", "signatures": [{"sig": "MEUCICfq0Co8trr4wRKr6lgmAi77wh7b0Oa5xilti8kgX9PiAiEAo5NFLoIUPDjjVpGG07cc6f73DBGVPdTImKMRkJ+3Iy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieOFQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9oBAAkBC4RlLLoWCdYNNsXiz7NoCYND7neO64tSum54J+4qaUzVsv\r\nT14d2EVG87FHMByCXwNnCdWEMbdrQ70m8uQY13ApADnCrTy55XpzmAaN/iID\r\nats/WZNCoau9Fhf6VC6k96pjOKhVXPICWLHR9ODyukLuZQOyAi1DEb7H/UVA\r\nze9ywv6XRvQeMiZiPC/I++/yd8mVWA+EFBbkwiBejxNM/LEITx8muWKeZMyH\r\nJMmHY/7LdT9YzKxmSohf6/YMF1/g9Q8dvtCLDSUzGf70D17PiBMmFLThgnOm\r\n2ejcTL7foeypLtHGEdny48Qi685LA0ZlgSRhErCPGeKeKDYv0g+FLgcYGEvJ\r\n5qmHK6emkFLMpjv71AaXWerIXjrM4uu8aRB74KSEMZBCTiF2qWPRNrqCwL2j\r\n9wKrM2O5gkB9MotdTqCQ2BTa6sLchsJGyUc1lNOhmlxVQIsImaRU6qwTujXy\r\nAZlCGfbg+GyArSWfCu5samxyifVpQFeW1NGuWngUXWpDzTlCgvTsAHiOywc7\r\nqu0u9nCXj9eyCdqIFwEmUzHLR3q5+gvqf+psK8hqEJyTp9ttfnhtuMiVuua/\r\n8ZIWlAcsHaCSLEHJ6Qc5c9r07aJskLZ/UkRfngtyHeoRALAZ1p/sbIMTblL8\r\n9hnlcAq5XoqBi/MQiXn4KK8qhjMnN6F8EsI=\r\n=BHB8\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0124d1f8796f68c02304a3db3fd43ee552ccc306", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.1.1/node@v16.14.2+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.7_1652089168744_0.14578396204571553", "host": "s3://npm-registry-packages"}}, "0.12.8": {"name": "@edge-runtime/vm", "version": "0.12.8", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.8", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "b36bcfa567800b05aee656374e29800bf9796cc1", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.8.tgz", "fileCount": 21, "integrity": "sha512-l9Bs+qgwOoCD1AOaB5zEBo3io61ZY3Blm+ZEEfTRw79L2aMjaCp4YazRP7ZesE+2wwq+jFHQjaQx9BQriFLY+Q==", "signatures": [{"sig": "MEQCIEsPMYtDc6Ul6b4Bp4XJrhMCzbnliDB9CSJt5/ILVKBXAiBnjwmlWGfvj9qulpkDV/NBoD+4HwkGx8sUlXlKg1/UuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieSP2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3IA/8CucD5lf3IBX/TyxV0vYwSLWSk2mBurzBouIYMRxfwoiwJBGr\r\n34ez9X+ywaUrt6Eek1tCFYYrqpN3Wv4CjOmRdkiu9rGUBxtxqx+k9IzZascL\r\nEE0PDuiY0FfnOGM0iPyNCDPuRfuQ5rYzndANPBiOBlUTapovykc6zHm/IVMh\r\n8Es3xMHH9gIq9NlfdGn4FFRsRLHaT77xFvpnnMWHrJLB6TmtUv3jdsnmLC0U\r\nyKxfuifehVVagIjLJH9UqZfbIR91pdpRxOv8a7PCAeg5tLmXOo5tbJjCh8KF\r\noFRPbishpHaJ0joGv98KTWLYORsnRoM/0ZO6VAHyXYql2E8hZPfDf6mRxN6s\r\n/iEiTuNMdeS32pHnuv2+Pbz3YXSv5GlqJaMxgqMHcVFFr+d+jWkR/irgMEDL\r\nIe06a4QJjLAm/CDBA9HC/Sn0R+DA1NtglFW9S91mKBdAqYr6FlyeReDKL3bM\r\nIWRVTzzzAWo/12P7M2TeDc+oUAhtqRKksSpOG+g0BT70a3bf4HZbottX6y7P\r\nBz4zOcjucMpUOBL+3yeV/WdOlPpP8+Nh65gvycvAA7p1PI352s9miWu2feWg\r\nge5kgWbAQ5itAbL+y/j74DNgrnH03SQWRqdSTyoWeV5vQspuheWShgfzuCep\r\nZMt7QJWke/2K1+r1YJEKo/fFbmOXVUBHRFk=\r\n=T+LD\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "d808f5468cda06a7941cec68acf50983bbcab5da", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.1.1/node@v16.14.2+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.8_1652106229925_0.41770128314432653", "host": "s3://npm-registry-packages"}}, "0.12.9": {"name": "@edge-runtime/vm", "version": "0.12.9", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.9", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "9fbf11a58847727f31cd3b99c7878bc466e52461", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.9.tgz", "fileCount": 21, "integrity": "sha512-F486cpPBuqVBV9Aamj0x8CWO+YD1PiaqSxeEwy6CuuhTjkL64HWdpQncibzfVXJYIaNNgMuYo7y9EL0M9u3EVw==", "signatures": [{"sig": "MEUCIQCAxHc3XoaWUxQ2gPhlBMpqOZH0adu3GeRl5xqTITtlQAIgU/daKtpM2kyEbKVsEzaPL4vrho2RTdpj0f9A/TY6jQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieSXvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6pQ//UtSzO+017qJGOeTvFl1lrIki4n4p+KEYXGIVBIzuHoTruu/x\r\nqpzCJb8iHbza3BVVyFWUbOd7StGyqf7zCIXuT2ln2ri1kmFeKQEU0xTvW7YL\r\nz8VirUUGBShVQ6N92AHTIYFKPuufyAZF76MfQhbZUCMrf6Pnseg3Cic+IMeJ\r\nRqz8hh9JlarxaBN6ah3LfFY2BWp3woayznF5O87rYLR7DKlmFDhSm6uDjSr4\r\nnH0sL6WaaBzTPr+Nx2rq3RFkzhl0yqK0Ej73vHu1QBzS72c3F9bJ85/2pH/L\r\nbBO6pE9KNVwUvXpbj6Zkpw1fDOjKBJCN0ZzV6c5Tf0Rg2aWvYSXm0703fcBP\r\ntUGi6nU1ng+T/0jlW4VBXOnC9CTltgb5kUPQHizX9LrD3SUFdo8xhPb3cVCi\r\nuBbT5LEfJYzMUfy0uaBVunXrTqDI5DFZA14V6rJBPPIbGoG5EMjK9Kl2Lde8\r\nByzprtyvwyo8GtNBAmK7TJXOqk0T0ZfkhnLW0d5pDFfxSIbaGoF7b5H27Z3X\r\nQtPW1J6H09As5qJthHa8cgacpFq28Q6Ai/YdqtjNQvarcy3eRleIiC2YkwlF\r\nLuoBDEoTbdWjmecl5VdJ/CN0liDVGqi3cN5MHlYfHhD4Vm3bVS7GUCRVz/ND\r\n7+aPa3wmibA3Catd+11/U/HO4JEA0ou+exc=\r\n=uB1D\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "9e447a77e708f1a72b9e2d46960f1f0c0d3bdc22", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.1.1/node@v16.14.2+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.9_1652106734808_0.45165471286036163", "host": "s3://npm-registry-packages"}}, "0.12.10": {"name": "@edge-runtime/vm", "version": "0.12.10", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@0.12.10", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "e6f94ff92a2131d9d53e039ae274b10bc5781df5", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-0.12.10.tgz", "fileCount": 21, "integrity": "sha512-/Zv0qiUAwZLzQm2iWOwBvQgVIgcL/DFCwbtrjbW/oU3QSKk4U9nko9Pgsnr/LzLHZpDbQWAUlv3pcX9DAe2e+Q==", "signatures": [{"sig": "MEQCICJ3GdTp0Jh3LkDgEzU638Ds3LYdi+rH5vRFu6Z/7XS/AiA21SKnPeyeGUbtaf3HeoMmAxnQM0KrsJT3q7soWovpMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifOT5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Kg/+J04ltm5EiHivNT5FdTtAdSE8CPbZ0tWbS98LmfqEz0LD5PN3\r\nz665WLSruKWzW0MYC2nP5H767zYjyJpuUl789Tt3uDxETFOMUY8Y6Atmahk/\r\nqv1lLu3ggMQ0njtrjtt6e5pbakwWdilFMdvo8Yt4amHhHul3D/567ITKgxyy\r\nQGGR+TKyI5GIG0zasHCG3yZZJRkCgIaK8TEksn02W8VVr27nqrrciQQ8k6Te\r\nq8cOMJ9lOkk/ctDt4XLXddOTyh/6bZWc+XgvTwu+cx2uxiAKOgcAt7JZtJ+w\r\nOGHurBx2hu6FwI5X++g9ukpjGnJTeyJcBYFLfDZdqKQx145RND6dtYU+gNih\r\nBZnDoFc5wZS07cPbRFeF17IPAMNw/LJXftmjwYnM14ghpYZ1HCz4Mq2vLAro\r\nGHXDTxyYKGtn7/QdIzh8P7xupkTQRPUPB8J+RTKvsGGWXkkcsGE+8+6E9nZI\r\n7PrjbSdoKjVlOoWdORDggVu4brrxlZz03frpTXuoXT2AnSMHaqQZA4VF4rXI\r\nM/g/58FNJ9/MOoPxW9FDO8BCy4rQM1Un2+PNxQrQIIFfPdeIkYRNGwCjc9w6\r\nKXC2HAp65eNuUBsc+wdNwLx7ZalCaCK39gunErw3lrwtGVhhWZJm0GLGlcgs\r\nEEFd9X56FA2MsSB4fmIJNt8c2ws+aVcQ3L4=\r\n=UKd7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "fc641f74863078229ecaa4c9998a2d93caa3d34a", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.2.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^0.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_0.12.10_1652352249037_0.11441300297319312", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@edge-runtime/vm", "version": "1.0.0", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "78162af6735eb2fe358595f19a02fd4089ab590e", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.0.tgz", "fileCount": 21, "integrity": "sha512-OSunqovY9TD2o2OIZftScCwB15BQf5l10w/1158ojTuvZEWjspZ+xxfqaTstJ46pHbf4YnfLi4eRs7qzUwUqyQ==", "signatures": [{"sig": "MEUCIAG9pEFpkEbdW4TVY9iHhnmoHZIasn6j2q/SVj3UQW+BAiEAsOesvDzMFiE4b4R2fj0CJPaDBVAcjh/OeUedSXgGb4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihWrlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLbw/+Ln9kblPax/Y9hEj8qfIoJmnwsu6gmIF3nSocYjXifTfawoX4\r\n3whTMq4q51hKdBEIZsYD3MUv1u1DSmKIVfFMjPgY/Qa3LqX/Njd9xpwBctA1\r\n/oBPpBTbZBfHAs8MAVB6Uy361Paj19D03M4On3Y+0OvnnJQb0XLtFZO9sF9n\r\n00X6bqdlzh417UmKF2Y4aBeIM4nTGxt28H/JRC5OKdpIyfPoWtevp5BaX7Pc\r\nxKasDB96gXaD5q4iOD2dQLP34ImK+MWLHFFdOg8n03flaXhJkXpOJdt7JBzP\r\nn2gDj6AV7goiOSTA34UAWweG6eWl21QPfEZ2qNpR5mKltvK13WbgdmHaC8Dk\r\n89u6YG6kHOKyTTU5BojIkL/uer3o/Jyy80xjWKHmi3SUYI246KF7sCW+fU+9\r\nzsmLvW3PI9+pqCNpf1RQFoqAqHTS+auwlfwDwUheJvKm5z2fMyEy6CGeRgMI\r\nMY+UwJALOSy7D+4I9wDCQ9QaTfPcXwYfN/FrEeq2vzuvBd645rN5FSaGbzqI\r\n9keOlU0FwhpAyhCUhOHwPVku7mfYN3wdl5N5VhaZp9y3j2Npzl7s+aLJuU7E\r\nSZsMGCTK7E7sQ2gANOUtrbWLg6TQj8K/pxm8VZ/MqFKOkOE7+EqgpgAbo91I\r\nS1VqRka0sDRhic+OQ5pp0R3lksreWQBmcsQ=\r\n=/wzN\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0fc9e3b28be41bc1965e448bbd3403da843e46e6", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.0_1652910820919_0.7385423506389854", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.0": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.0", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "255687f101364601e8b96112465f868aa2bf84e1", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.0.tgz", "fileCount": 21, "integrity": "sha512-BouncIaDG4A8SZ/3Wjtzr9I9yZertpK0IIZDD0ZKoQcoBM3nYuqiKHhHkRa0NVUhKDHNUQAwzR6Ws+0iI/J5bQ==", "signatures": [{"sig": "MEYCIQDXmEhOV0G8MteE/lfCH41RB2Rm+S9oj1VnpYGRvoLb3QIhAPYeo6ekQZ6QVnazBBmxmwvBvnnnaNuMQ7SMbwJzZG9i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii5IpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Tg/+O+aM42lv6Sj8z31gNVwxZJZZ1ZXE4+av/lX0OgsWA1VR1vhw\r\ncoW8cEXTB9r+mJKOQEoraT9H08PDGz9TojcSrbQv+L8J1O6MYrtEZ2Xurwq4\r\no0lKTdo2QT/Np5cqB9mlaflSqPAM8CZ+y4CKNJ3KMwhMrkUPkyt8HHKD6ygt\r\njFFoAh/M2jQlk7iS4jOFqW2w6QFlB2nSURLMXgDJEmuYUffPpUg0OY0Qp/iQ\r\nxa++ulSVvRWl7hDQAKt/7T9GVhmDUp0AgVlwyaWgqGLmr8aLHpl5IpKaZUnm\r\ne2NkbA+3e5JfL08KLcVCTgUBMHPuOFPLePX55zMmQgcwXBDuveQTVh4ia++t\r\n41MHbSMTQIGQsekvc/RNBAQyrwik1S6uCp0dqD8KTT4HHJC41+uxmFsIRzLI\r\n9ZzxpBv1OQ8sB1Dd5Sae1Hw5RQQCWBuJaRyN2bSJSU1e8mBlyH2QHpRcsyh/\r\n3K9GGfPf6iCWsAKfQpDq/NBPfcwfZyfrftvK0TC27F6lVEemvn6FeD61xLRZ\r\neAjWoUtod3vdDgtXlHVbqZ9ZS2BYpTKQmiQvmQ6+bUEIaQpe6VzpSmFNvwLC\r\nzA2M3kVdJ556xwOliFWoSfd7Tqo1qSSku8FHKb4VXnrUPlGOJUOtY6zE/U9H\r\nYfxcfwtObIw/LiuP9wkTchEnIrUtPdeRZWE=\r\n=CcKi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5af380e94d26740ef4d2c3ae2cc0113f59cc67ab", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.1-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.0_1653314089627_0.12515803857697705", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.1": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.1", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "754ad1a974d6d770451fb92314079cf6e5e4dd52", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.1.tgz", "fileCount": 21, "integrity": "sha512-oyT1GtUAjxyvP3YqBa9aevwOuqBZznZsBmpi3pVbAmCFEil511VtLZqQ2wn0EPzWoTLxANpgko8K3oG8Zxa7aA==", "signatures": [{"sig": "MEUCIQD1KHJYps2+eblOTT/xE7wkLWcOuXME7rAitVNjZiDvVQIgUrw9o6lYsretyHvIFqMz/SZhpKECmz4hj7eZ0fZSe0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLd+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIpw//UzjExl67GOYoxlc/tGIkzShUBc6FvkFMQB/5SRxbiNpOllZw\r\nYNCPRhA3S5KSRkaePj6sN1ZaXOe5gfMd6rVTfP4ZgS8Fn1CoXGkdgNvlcD2J\r\nmXB7eHhWot/fURBFbllVRDdajALApPUdHYE9nPFArkshsbz5jadstLHwp8xh\r\noGnxKREWmX6i0WYG2qyRS/GEFsmkx9jvnrk0rNMvaJDWHAQo03I2tmTMSzNF\r\nztvEWjKQqGBgLoqP/85hZ4ZC3qR60MUYsBGKD8XKjGLiJwAPyhhLhcD442XP\r\ncUjanILrwE3DKlx2nwg2UoYBPDMzCpgS7nkqN4uwCGdMbJU1iNTo2Aps/v5u\r\nx2y7xy6rZnpAOY7dBVzYHAA3V8+mUXyXzXD61fQcBAtwDmCj8Wa07HvMyrbb\r\nHsEy0sO5CWPGzYiZmzilb6EjW7ulgteZsWtEh8Lwa97sQWodKQwa9DVtZQ0Z\r\nYvhJjidtgv0IUqMhe/IiJfdbKK/LLLiJQKRx6OFmJj82Z0yqGsemYKY7NJly\r\ntIDhqcZrP6Lr72n06Vw5UFJ5xSeQYoKo5OAW+WC/odkBGG+8xlfjc0IgAJ22\r\nEXDxvhgfPoxVd5++dy9t2m4wppQ7/LLYpjhaiRGkwwOa2DALTGz7ilDzbAwL\r\nR2kxQBIfufw7T2S/Fdk6WYuxYdjx0v88SgM=\r\n=sSIX\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "2053c283b823d93313a028d7f41a23859a986935", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.1-beta.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.1_1653389182290_0.932154407198456", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.2": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.2", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "ba0280c3bf24ba7846e9bad7c9b6a369686e48b1", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.2.tgz", "fileCount": 21, "integrity": "sha512-xmg7Yv8RiJXBIWGZvIsr9qKD3u5ZP34K1DvMUw7oXh5SK6K91sShezui17qVLpDqn5fFTA/iJKd3SDqKehutqw==", "signatures": [{"sig": "MEUCIGHEUW9xob4AxhP2r5fXTKpMJaxIc0M3Dh1FpUVQKLJ+AiEAshfpLmVwR0WiJOqOKuEGO4SKLEFSLzCcdoCwNrrLoAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijOoJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrddRAAj4dmHpCr7EoTSymXEZqOAWW/UyN9UYEnBYV3JmlDXnXvxpzm\r\nT60z4McFiwfZasb8XxwxVO36Chus4LW8BNLkSE2nIpH7Mbl294GToaOG6wDJ\r\nkDhURlNCCqLZ0D1kHwLmHnhY6S9ywQR91w5+54tINCO/lDTPzSFUkhuGw6cc\r\nUUzhk4griwbzURhUK1SWAqs5W9pZLxtaj6U/T6+wBPL66pbz1ezlaCDXsNut\r\nLr8C9FKjp72K5QDMQShokvHD5TobOH3WRWVElZbwFNHEzn/Mp3JsGhdFbEtp\r\n4nuIzAn5ZXsJAPftcKs+9QiGpPoGvdTUNqGw2twaJOGbdXybkLw1iKTTLVN5\r\nm0jhPVuS/YSWGeYpA6xTs7VjBZh9FeGBXDvEPqSeoNUj7Hc3O+Z0RdZzEP2V\r\niIyq67QtaV+0QsyV1sfyUmgnaJUUgezU/SBJiSLnmGiv1VUTeyvaffgI3/Sr\r\nulFl2OQMZSC7Hyl0DR1OylIJnQnnD0ukphy/oOdeNeJiMct39u45FaOVP/3v\r\noWhS5pdjs2JAYr0ZOwgeAgY6Wa6TlPj+ZqdhZtpzVwfMRX8ChZQgnzjp8D9S\r\ni37wz8Eg6qJCjBizpn1eB/z76/S6wzmIfqMhGEybPEPGemN5CtuShv+ii1U9\r\n0z5CaME/HSVX7VDy3O1MZCw1WfnKyGsrHEU=\r\n=CYFc\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "651f8a694b8e2e6a589a18c62722b6a3bee5e6e4", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.1-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.2_1653402121162_0.5139515611622361", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.3": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.3", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "6f91fcf64e6a349bcd2097f694509f8419588d1d", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.3.tgz", "fileCount": 21, "integrity": "sha512-++VIZFypzmagFdGodtHoVg7j5rAgCCbwEFn4UIKZA2J11LizeQEpltEsh9Rbkqt6JrFTFbXE5c1OYwstA/APpg==", "signatures": [{"sig": "MEUCIQD/K+HEhGZdxDcSeBQ0/HVW3amF3HJuTK/Okfaz7bwJAwIgFkWrkcCyBY9ixqSbT6ZIMpcCIE+zCE16l+Vw53Qy00k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijOt6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdJw/9FskRrpgLPfvZQBh+JZx3Boqqx6mdWvAZULylG33cdxzYYs1h\r\nWgzBLe8OZW1IUaEugrQOdxQuzjQGgF0nFgK8oPpWVZHmIVcwzAMvF4M9mt7v\r\nQxCihIcI7fKyqirztzt2mU568mCLPtob14kk5FI8geUZq1TloWUEPPYjpJTs\r\nS1vYg0yyVmWmddueluVT/RQtlidCWPWirTPdA0rFKCOhEcj5HaplSE3v2fM6\r\n0F4AeosPPdTgA4GVuot3MxenwfOzDqnA/FhoIGJpd+HhIG9zpAgiu9fxDZZR\r\nnh5R19tgdA1JupTZ0gDZjWi7P83rZAh0UjLyPdEnxagAGppL3YJBk9VRf6By\r\nLmGDyAd5o3xVNjWW4Bj/7M9fGedTord4WQMRdLWwD9N5VpM3NvNr8oRpgsnQ\r\nG7DYhceYr4I7mxDhPtEiWpYYssycoYlSVNLtcecs4lm5qds6SQnXBOfY874a\r\nU6BqX2cvN1643CCBYeLyIgeLk6yhHIT6uOF0VrnlU/nTAQeIIQSuuQmXEW8h\r\n/6qQ7Qmtgn7hL0Y8vnDGmVudEK2Q9Lse6hh4MZVT0S85aXeVOt12GwNUo9p+\r\nshlrohsC5wTJ7OTIZu4uj6xm9YEXEvWNUrq2cyxy1O+ocOt4qHadIz65LVjM\r\nDbcKx7YZLuQYlbE8bbYLeqdzbgc2RH6db44=\r\n=ejha\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "6f32cedd77f2e208f02c76b1bab8acc51e2988b9", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.1-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.3_1653402490171_0.10031037048386304", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.4": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.4", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "a87e8e344e0e2f1a9586d1c630430f4bb7c49e10", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.4.tgz", "fileCount": 21, "integrity": "sha512-0B+AoIBwoUgxQMEzxol0ORw56YajlTYrYTDwW5hzAg0AlAxxNkHazUNvAH7TrwTi7aRi32R7I3bVBYxrevwCXA==", "signatures": [{"sig": "MEUCIQDUv07Fa5WJt5LRnx5AZsp9J4vW/unxmibtEYhkMHMgIAIgZSl/jBvJYFir5HBqMZc4rZzXUeGmH0VxjL9z1WBY5Z8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijjdJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAng/5ABJOp5pVIC51ueoBDM89w9xQFMyR0ufYqDLIoUiJEAoTKPPm\r\n3i1DZjBMwECQ1Df8GgB0xNgUF5ejiYIS3PWq2HQq8FnPRcVv/4yMzuaJsf97\r\np1pKwB/db9Ixc/Vv5Wil2HrRgdgN4sv3ASX1IzFuuM4uUJZvgR0ED5fmPJrn\r\n2VksdRDZ8RgvRCjkSU6nQmm4le2BIntD2/a+hz2IFvQ3f5edQnfi9j9VhvkV\r\ny6pYotiQaeeont0565uM0cY4zAIL75i8RZUiOb7BVE4CAsaOpLrqNcjWF+v1\r\njHxxkBVmQ27QOpUd9bTj3KEs4PKJJtcKB01J8ZgcLSaqda7b/+DE5OlUbTw+\r\n8V0N4VLGL/Ae1Okf5kEdGuYth07v9ztZ8MZG98RibXyBx+ut0BpH/dwNCAIo\r\nwHkchYwDh/QVJ70iGMqrm3IQ6EKy57CPVOetFbLL5oWomMSjPM1CodqDzoin\r\ndG3FU49V0hjXMpWxm0BlWuT8x5XZ8cZGlVf9OgEhuNXQdnCGGvoiyp9CpEH3\r\nRdd0mV6ZuKJkMTEBYq+U0EOjNlRLWRASJ2bmRvW2RYTwtjRjBrbKtkFpK1s9\r\nhePZydgzdrCYJ86mcfMy2Wrng1AuZiDFuoiwCWxWJnALEGrBS10nYRj1IZFx\r\ngY4B24QLSrgIxKH1B4MQ1r4SnZUc+VCeE98=\r\n=x2++\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "7d6c359c5454c3b3846a03dfdc91de877e2211a1", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"uuid": "8.3.2", "@types/uuid": "8.3.4", "@edge-runtime/primitives": "^1.0.1-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.4_1653487433010_0.7480908985314962", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.5": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.5", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "66eb12b8784f0077a004d466b45159234b59d5ae", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.5.tgz", "fileCount": 21, "integrity": "sha512-RBWL/rqIhuWAlhALBGwhArcB2UAfgvkoXbHIiGcKqjVSdFP5zq3mbUI7JZltIcBf5BWWKs3QUVgLKP5kIKdLmA==", "signatures": [{"sig": "MEUCIQDGDyQYKqrCiQYARyYRyESYgd1me+/J3CfqaocXGLz1rQIgDUzomCC7rmHTNWBO9ICF3X4XobrRwOSEoq1q0HC7i30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijoPVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8ZA//cxNPADbqme0AZGTq+38fYIEPZhxO0DUN9FB2NhQGHKHlAuuM\r\nqZUlVZ13jrpj6S337B8t1k6tRWhepsr99bzr0exTzftcSB9r+2Ik1Wqqo29K\r\nbXTeJ3SSIm9sV19VwvciI4ioT5MEmDbdWvcGE6BSV/aFAw65m5WOLOMkPH+H\r\ndalGUJq22VRv6ejGTqApJDhYslR6skYqbFk1QFMnLWivCqv+0ZOn3i4miEdY\r\nOjV3dnM3eOnDdJzv6k0dhc5ntV2EpqGzuz/VbBWWeLyosoo/7eQxQVCSYBaL\r\nl5wJ6IbsY9t63FLgcbgt/1gVRfl2WVcblKkFy612nx9IVVQekvZfZ9JC7Nk8\r\nq41lOFpk2NS6zLgOryjEb/Tos+Yd7vQ7W3DlzN2OEzQfCm2XOGU7+kKpFnNx\r\nkeSDCSfstYltY7gzMGtbFGdbd4ALbn+wzGPDMp2KKnD+Z3Bl9acWIB3WlHF7\r\njCA9t6rFCOfvfoNWrH9gwEdmG9lTf610k5PPk5c8iWY4ymCUPtZWsSBGr3hi\r\n5TwpvoXbt+M8uUkdw1+J/1cStxPIHytlArLKnpuGYc09aTWrBYcYs7es2qLv\r\nm6dJNJykOayVYVPA4emsvlKN9r0PM9c3GJixpTqr+XRotbN6KSX+OqaY2Bf+\r\n6/hBszxNa6KaBOaWCzAx0WyRIqli+naHhrQ=\r\n=PcYl\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "4bda69ccf3f585f873a4b8d3e2790d2668fb771e", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.5_1653507029351_0.22655831713516106", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.6": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.6", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.6", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "56385678d0542b23e82154e3fb4285a8c30068e0", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.6.tgz", "fileCount": 21, "integrity": "sha512-Z1wdH7zKKXkTDkJOHwGyMtJd/QGR+aJB8qMI/13kLxG1oqyFseu2WQE8kqe6NO4YoQiopwmrQ1bWwCNTQtbKYQ==", "signatures": [{"sig": "MEQCICc9yNdHwj+zdHs1DI/p9XARpAhkEFBf0xbKE8pojYYjAiBqYMp1Wjcay2bH/Utwii4A0KRoIDnpEsXneH4a9mPJFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijolKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHsQ/+Imi6rJUszlcII+3kLqJrqDO2jPJnb2btp/XT9GPZxYOcD04n\r\n43HmUaf7SJtLamFCX8DI1i2MsVRbXF03CJKNzPHlY+Mv6spr+mmwERP5mm18\r\nXiwigr8TmMlKluY0DOYjLOBgxmbwN+WdTvgOmFJZAaggHm0siIToqPTfXxy9\r\nR6dclTs4tuX+xGzkdHJ0aFmbZ3lMle8UBxxsUHop8e+EshqNdRdzUQ9PSlpZ\r\nk/IWUllwjqKQrD9N/QYr93Z1EG123G18Z3CZjthXTPK6/osCU8rcLSWlBhDc\r\n3GfxAG2WafShMxDmacj6y4LksgMInH64xVTQHDTcGIH/Cl/Rs9/Lyu8y/PH+\r\naGLq0xdJYBmB4xQeoS0L0xNCJOx72Hgnxzpamx45DoKY/grBqtvKfUwq4UrC\r\nhU0BMG9JPWjQh8s+rtq2+uIO1KDPvOPYAuPZmtAdehaBCh8uTFEpNw3zJieg\r\nCXMlZaO8Ivx/OtF2shnUkEIl6eMF3ev/8In9Q4lhxpWuVA+KrmjrhrlKMWXh\r\nAaFfktUnzvoNx+tj5Iv8oHeajIzjO4LPyCqJqtSeeJOU6TijmpBJq8XOrYQ2\r\nOh8D6/6Yn3NbLMqse6TIdTyNWPGnO7etb0vMQiUfqkgSImhnzs5mphuPEhlV\r\nUZH10Nwb8ka3Sn9MvDu870L8FFkCLY6r2V4=\r\n=YOaJ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "46c6248b5dd8bfe38ee22ac5e730b2c9c3030aa6", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.6_1653508426427_0.8959713737731025", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.7": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.7", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.7", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "55f3f9be997acc002538adc4705943d5cf208192", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.7.tgz", "fileCount": 21, "integrity": "sha512-mRb0JsydlT/vjEM8Km7vVkDPi1+3KNHlbvEWRjIg45FOkArNwaNWSgPXusgSWrGtjyHYcEnm8WuGjG/Kd+ME7Q==", "signatures": [{"sig": "MEQCIGe4eoCJAnv4n/x4Zjg+TtesR/hcxqcvCHmcmHXoIYZ7AiAAllAmfAs0iVKpwL0OWCLlBibrmqUik1awmMtiGwmbFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijoplACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrzvg//amp7083TvolVG0U1MkNN70IaApHEGlqEN/pvTkApW3NmwDNy\r\nBNi9jTIgHsWEivAHgpvtSK5zjJOWUw8d1W/xjevlUC/b0YmrBeQsQGdViwif\r\naz9NN9zPuM0KevyLHYrzJni6JAYAFQRvIaGgfqxfrK8uFevx0gl89hHsxey3\r\nIKbbYvhN6tcoidEkPHBUYkCzmxui9AkQ7SGxO3ZAMq4as+3j/hu+IjtzzSdK\r\ns6usGNFU5IH6MYNv9ICJ4k2fCzEF29ULTCTDD7LIkw5vtoyC3+Yg6VUnLZ8Y\r\nqB6nzpXQ8yMNuOdBa8eKD7/YkID2K3+5X0F8MOOLxFOZeMPCLsvx172kf04B\r\nYYsDmh9QuZeEm9XJBLXu/JCdkrgfjcNkUcRqyFgNZf6ndaNy/EP0WhQHDA8w\r\noqAtutxIJb43c7kWj1AYBSt9WoULNMuwojG31Bj99phD6Rp6l5+LrhD4mLtW\r\nt2l0+9MHG78eIE+rBDWx9r16xILHNmvemxm1R9QRPVzlWKOe38rLrsRe6Ye7\r\nkA4jo480cU9gT7RVqTEKOMsvGt0U+vH1/uL10irKFqk2i2ZcR3FezxRVWhQj\r\nFpQJTgb3Ymgo3RVFmSHx+pmgJ7QsWA7QeBZb0iYTLp//NsseSaH5UWSzQU42\r\nPMD6P/wf893d1y+SG4E6zyTlcNaNjbTqx4w=\r\n=rvVN\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "1297e68cbe75ed1a20a41b79062886907f815988", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.7_1653508709734_0.07030798966974272", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.8": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.8", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.8", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "995ceb56c80cf1199561addd74638872acdeb12e", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.8.tgz", "fileCount": 21, "integrity": "sha512-F9yHKQlEzegkqlzik5C/jXkDgG0tc6nmGFfpUHvl4JbwdtcEx6alGpcbRNeYWBxj8Snhc7EvZ+fvgZxwr+m82w==", "signatures": [{"sig": "MEUCIDPzXTYjFqCrI72lH+M8dMO0ELul8ttFcGI8Mu5cOiuaAiEA7q6H2fyMeNWTEhij5hGKlKjz6mAA0JuAaV7M6rt1cEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij27fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDJw/8COKBtSOlgO2lp0epuwljyToXFS69i4u+bv4J0ZGHibxeXgit\r\nMh4YE+jQOi+UzcLKDzDnnUEwShucvluLSJF6s8aGbFm1xV+t/4bCVVi41/DP\r\nosZq5pA64uMr6ZVZyfwP0AK1CHh1/v8CVSllwsL67EJbY6khk/yENHoVeihd\r\n8uY9qkTABP4XlREPg/0lKbL/Odq6+xFy5x+mCKURk/sQ6vVRIA/6dGL2PZGC\r\nrcSfSQ1rDsC4ABuhSyzsdGJzl/S7S9Pig3djI9qTc4PiPmCqjgSUkpYDcgw1\r\nXr1hP6GCLItq5K4m6GXZehmt+SBYyS7bO/0Y6h/v2hwlZia43vKR3zycTlJM\r\ngUHCBfB3u2hogrmt7dp5yxHLwluO29rM7cqxvTus4zLgWkJB7fD0rXdu63oL\r\na4+EIpcmP6L5U/m7AOyrHEJ6dYI9oioJqz8Geyb/eVGumLNSeFcx7Pn293O6\r\ngPloMXOpibwP1qAzD67iIUHf+fxE1/lmCaXKY1wLhxMOyvngCvKGVAFUgH3Q\r\n6EG4A/u+H4JcYHePUvkTRqUQv/jpmwJIa0HInjCIb7exOBmFrnQzCnFMLVK+\r\nyViYWAR3iz2gDl6jso6wn9nCHQeMBDUoMMsPPN6eAhylXmwy+R82gS8O5EaS\r\nMlT/OP519HS45Bk1LYEy0auVWC+n39/rOYU=\r\n=WDB/\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "b9488e923dc827ab253362fcfe4972966fa01299", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.8_1653567199695_0.22174594271677184", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.9": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.9", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.9", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "f75ceea703c186d412ea1a5387f6b4c0dd6014b2", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.9.tgz", "fileCount": 21, "integrity": "sha512-kxFPfTd9YKRaSuyDV3UD3NUeKuzbBCB02PvEIICK1M0d0yk4wioEWBgwisw+ydgO7vV3PGp99ZoKasxPjcg8PA==", "signatures": [{"sig": "MEQCID+s6wKptwdth7yqT/606ERzxuJzpAAjKuyfqLnKa7MRAiAfaZmT6wVZcnNjKkH1Ef5mfNdPcAnd5Q16ynHEmf7VyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij4DoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTfw/9G7CbUCUouW53h8GpoJJuO6jmh8nqLZxZY4nZjUd5AGmJ8yI8\r\n+OM7CTD75gfkW1oDTbVi9Yck+KR0OudhpuNiUnl1XQOPvC0zcjDCP2rBpWTs\r\njUSs+865CsxrTRth9TVcIZQNgEh2XNyQOLWpjitip562W/LuJuTK0+6zs6HR\r\nURF7R+H+rF3J9WK12jNBYsOvm6cU4OMZwNPu0owbS+rWkqRP/xlRNJFdvW9z\r\nDVIjm5BMJIWlzwHP4+6c8SL2HWLEO3hbJE8tco5Grww3p2XOqTRSykUU3c81\r\noUOx3n8lDd8jezOnpIroxx/+4B8gQ86Gr/obCxCfiAPuDKIEGr8Blcy64Ubk\r\nVffL9gNPcSiUWwn44nTCfaqli3TmUu8K6AClseBioozMT/Z5W85aDBreXbYO\r\n83sj9zUBOmNhJDolzNk+nxid1eaHY6WARMezI4jZjsj+upGRyUP4kXLcjYb3\r\nlnNLTg7dCga57YYmcQ/+Vt9LINNdF80dfHArRKKMGMmSuhZeWMaZpIj8xeMC\r\nrrJmseeD/zxMjV4jjhWIMr0VahMHMQ/VK8jXYsypdcRa7xa7ChQ0/Wfb3rqA\r\n6xO90ZIDdfXGnneT3mjIrt4MI0SmsQZSYTyiQE6LZ3DFQgNrlLpnhMHerwzT\r\nMlMUvgHYf0N90N29xqLE5WqephUcIVO+mxM=\r\n=/aNA\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "581ff98495ec4267d9aab1f444e3ab7f64daa57d", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.9_1653571816136_0.6183109717538491", "host": "s3://npm-registry-packages"}}, "1.0.1-beta.10": {"name": "@edge-runtime/vm", "version": "1.0.1-beta.10", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1-beta.10", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "ab42964d56accca146b1d03899228a850538203c", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1-beta.10.tgz", "fileCount": 21, "integrity": "sha512-+0NfrfyXjxhyJsEuhxSmZj87/zvPT7IU7X1mdfGOSiRF3Mt92780gLqT6cfv3/2h5009o79asc4aH1C4OfYAtA==", "signatures": [{"sig": "MEYCIQDohu45UPkl0mofVxFSGfNVC0rNUZDrQSFXNl15hBRRKgIhAOu2f/J+XCkcniwHAdnRXOpEmFbNLWqistrvlVk9BL9A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilKIRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEEg/8DAAZvXtA8hL/fId2CfswTfIsJgjT7oJPGLutHHlSf6KgMhCT\r\ncs1ASkzyhyBn/l5vIPdXPpGysVVjdRPBRh6/NVP1oY+l9EKAXi2hw2IGQpJo\r\n0/fyB+iVAbAG/zHU2kNkFYcGffzH5AOEdzaxG00Kv63fBnkABLGXJijjJTt3\r\npJ7WAq5UkFtAjqCNBgSV+xR/CWxo24CXK6t6N7ejjWmXcApvufNN8LLW6oZ1\r\nQ0pNSevm/Tx5iZHtlEOFke22QrRUb+DyCDUG/4jeY9X9iliL33jZvGtSzX/8\r\n2v7UX+IAAJCV8NREI5vqqHD4qZAzfIgsbPx9mTWlCDa/EHlkj7b31zsLN6II\r\nBVZA7/Q6RLhzhjCUsntWh20TprItWmzU/VDLNj0gXiz7Us7VhthOo1gLcOou\r\nCm+u2zIBKR3qd4H8hxHAP7eYSe6RfrbHbgAyr+VKi6Xl3PnG5WETBwZkiYi7\r\nAsblDJ9pfsINox/tu5E3Y9zK4Hl3COjnIWhj3Y1IPgaDtjb3kKez3473+yj0\r\nKKS5d9pEEdwC3bHt/2gQY3INo0sYX+kMDAIlZANJCNcOaVMCy9IgBPF2OIU8\r\nnRdVjRYYgK3bar9WCU9WEXmYqtXjqZcxVElBosJexO7VMAzsni4nJZ6kGHmR\r\n7dVHJqxQeRRFR9YpO6aJwCfBfApt79hbc6U=\r\n=EBzL\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "4979600bc2356b12f9cb86b5ef3a315caabb8dec", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.3.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1-beta.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1-beta.10_1653907984826_0.24236023305481091", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@edge-runtime/vm", "version": "1.0.1", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.0.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "0285efd9ccc12efbbbd725c70721b39b9812c300", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.0.1.tgz", "fileCount": 21, "integrity": "sha512-kkPeYBCGgeoyBxKdHwRy8zKtpGFS3aviPnDaRKNx/NaWFr/EwG8TTdv9T+VP3JzuFC48me0VF4fnnIEARrI/7Q==", "signatures": [{"sig": "MEUCIHRatdp8oQE5daLkkkBB+glLCRv1BipSqlvXVe9OMaNQAiEAiwLTE1fY0nShHahV6dReden4Y0mxoh1Fe1IRjXT3L4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJild6RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpP8hAAj0L43ldsEAooPA/ATKZBS2tHhEI8pEMnsP3JH0iywXqmt2iC\r\n3xQmtwwObQ2SgTn/yHSmqESR1vW3zTV4kMcD3/8MptgL30mgH7TQWKtg1YwG\r\nL2cpahXXALDXWi7tp7qJH73YeAf5MOXWstVDd+V84jfC3TjhHIR3Rf19mVxG\r\nspgW+fNBCvEczizxb7MEx2gG7rsqz2nRr2k0Pd+yAHKKpZURO29LhpT2/ElQ\r\nicOPFrQMopqwMzfRwi7+iwWk/orZfhFFAPrGuepqfY6UsES85DjVyFKwrLkv\r\nC8f40f9PuebvB7sDbUcRSBYNfPhxVBncheg9RlN2cYfZFp6wZg+VIf8pq8lE\r\nVT1svFexNuS8vpqRxX7GlhW84Trts74/vSjlipZ4s5aowYACk+kIToDVqNrp\r\ndUh9dqjPr8WSaqq/TyXw94ZzY6Wuikd4LCbfHg+PvkctAHi/eX+dZEvL0P7B\r\nqySIw/7aBVvX4wFI8mJv9lpVm5rpjQ07g+9e6nZ/Xc1LYqxPQribkWEHX5Wh\r\nJCOuQlEQWk+DcPs8bT+4UlW8uGCQf9gpwJHRaBpf/E8ICSWBq44RCq6ajVsx\r\nNdJvpVY+bFs2qdytokvqx7Ox7s80uLNvYosPdUppTbBU/9yIqbrYZ60Urf4S\r\nZQsF8pVkFsc9w7MLKu856/BA9/YkX/U9hNA=\r\n=ZZI9\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "4d5c7ddd2216facb2c3e5da32f466f56d15d3894", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.4.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.0.1_1653989008950_0.9638750742402207", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.0": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.0", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "5b8386fca8763dab4e521af80effbaee5c849ef8", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.0.tgz", "fileCount": 21, "integrity": "sha512-CgPqxM/+0iWtYuNUMjCqeW3nbqqmchZjRiV+etK7JyFbI/kriUkqB1QdyIfct978ir6cabmcC4mBgUI20hCqTw==", "signatures": [{"sig": "MEUCIGwBXkmagC4oZhYQ6tJ66OXl10i+o9f/T7hDu+lblfMTAiEA4UyQl+irN7Rb5dzd7TrStU+BRFQPTYqIUQQSAlaqiq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJindbTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog3RAAoLxuIf5WQsFIeMi2wtxRUJxU0FeusN74YjATQT8ue5ZfhN3M\r\n2st1Lt71QBnjX/pbg07GiIKbFLmuZ50xuKMZyBARgl3Kj+QYhFzFJPDyIc2W\r\nYCBbA89rJEGBYy1ELqgePOxQzfqFhQXH8ssRoTPlwQLMIcmIzAlTpm3QoehM\r\n7YmBTgffUqV1BPYH74EMDIaNaSJzJWSWVQcMK6z/amkbDH85NiB6wv95dj4q\r\niI13s3pCa9P0HAYfYFzsPywtKfVIs6qU+c6m2fvjJtEnACXveQVKTK0MdVCB\r\nDsJZ590nu8m9QNWbbY0aBjwu1h0eP/yAJGZJlMG7s7U9NV/Zn9M5on4Y0f2t\r\nvdCCHNAa9Zm5noFGyvF/YZUUMmOFh/p7tx9dexg2NF7n005oxVwmKLO/1aNg\r\nmESWGfVeIs2C9Nw71oGzHPVn8Jo0DKlqoVr72JpDudHGIoLurhmeuyzCEd7J\r\nd+GLJupcC3mlajiycYE+RUVhqQpywpgcKXDKFfn3HHIMRIBEkMFhiPQHTw/u\r\nekq2md3b2f8Bm4+tedQq7ZVxWE4vc89+87+PqbLyvMvhB4Tm/FFRfz+PInCM\r\nfr6M0PMPvXGmrPtqgRX+1opHyfR3lWlWqiEKH4QCAM0nSzzuTARY5pV8heZi\r\nn0j78GE9iLZpP1i7tBlib914ZGBV2/cb9vA=\r\n=wYF+\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "78efad9e63dbbe69cef35ed68a45bff22a3f5d7c", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.4.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.0_1654511315677_0.6293425816210363", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.1": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.1", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "eefe6bb1147c43446a66be3ea68fd03769ef56e3", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.1.tgz", "fileCount": 21, "integrity": "sha512-655ZV3X7IXqKNGX6sEgwJZ8ZtikW6WGOJWR1Krl2r0VYLyHv38bKsbNbOxdEdzoff/IFlhdhKK/1a2lFwueowg==", "signatures": [{"sig": "MEQCIE0W+V0kBBbfqjdd7TaeR7eZp6PPhtwk5rF5HxNxy0e+AiByxIyBA0b1vpiWL2Xz75IU+xOvSIkavI4xe/ksfGt7gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJini0jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMkg//dbiNRaMdFXyzO6VEG3TMEsFvedJvC48xOeZEajQTQ+N8dQ4B\r\nIRlY6svMxvEkS9/PP+Am69RLCod7acO82VTrhPXq4y4c4pfpBSV4Ho8UbD5m\r\nA5qYG3fYkQGuc+zVmTKGb4LX5YN0+YVNBpVlQ1GnhXO1+CnPlp3y9g1IilcG\r\nSxWyQgcgxxR5gRJb7QSUUau4Ql+PApk45pb+mgzUyKnl51ZVtUYy2ARI+iWP\r\nhsnV5nyncZbykxZ/BmlRSEdImn3r58AVPiTInbm1Dulfsx7RAy8x8o0LBp+L\r\nDvP/X55oFy09cFbuTd/lhgrPTznBHlsmdBrqCOYD4zpPoY0vK9hp2798mh51\r\nQHdpn2h6qP04NLLD6uz7nAr6f4JYvxjvWRD9fwW3jvOiFMAqbXbi7RoevM++\r\nV/VUP9BLZp1jy0vdZ6s3ayao+xvWJZkA0US17BUlh95tUsSTxrrsXTeyI/nI\r\nw03MZhhW2e/E07xA9vK5nwwFKqsezIKJM6UU6bjXkQgFPZMoHD05/rGLPOn0\r\neS2qyXJfXakPOHbbcLhQon/aDwrRwQirW1CLonhsxfcGHkNqGGxldyppZzAR\r\nAncnak9g3x/eMvKnWg5dVssl691ia/xTXrGHGDMSg09l5t/JEX5xD/ADADRg\r\nGHaaksrGHXoSQUij/uc8iWJ0NCK7h7P+Ezw=\r\n=3Re5\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0d620e3d0a11bb9bf5e834af8721c8c4b6c48ddd", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.4.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.1_1654533411439_0.24125919707469157", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.2": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.2", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "61ed5108816dd51dc0f340eafacf8b3a35811f7d", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.2.tgz", "fileCount": 21, "integrity": "sha512-JIAI74WKEnVnz9UQIuFD+ptqsiEAVNOOhCxJXs4CyxZQXhcZOGclCSFdXcttPB4yCwozhAmVJnC+5O7vJsNuTA==", "signatures": [{"sig": "MEUCICYUZyeQHwlsoy7r2WF9vRYnJugOy6BNtpwzk8H87CHBAiEA1RMJa58A0MfT45rvRQJeDNbQFRLYyvsviV7Cr8Na91k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioIx3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpApBAAk0kgHhPL2zb6mv0YpoMsSw2v3e/29+UpT82WfZ99oHDMRHsg\r\nqfTy/wei3tiC209vyd8QQUsYrOqWzcUb7gV5b/Ej0FsKSb3HJKE4/ozqItUs\r\nJv8dbZ4sIOoKkkxvKptPwkKkASfPlcR50684w9hzvwjrwz8hZgjW/Wgiy6Lu\r\nBQlVld1ZiXGPlh1jGfOmJ9JL6SDK0iqXQ+erWeMhYsoqvNi5ihRd4SBKzGqk\r\nx7P/ED40yIGPajzyxStZzD50z+KSfYJtyh2lbsIcT+waeUABjVy/JOyTnSDg\r\nR+J9yjQgSxNl5+Xh2W4PxlvjUZ69dsBOtWiBoVVMrQaFDGr/YNcidrTI9dHp\r\nSCT4Tr4bZ47RAL/ZZbc0XmSK8G1xlH/gqQG0pvqM7QCk4RAV841FqOR/EBDL\r\nfamFnvFk1zGPwvWQzicm9xyAfyvsT+OaGg8KET2AHYD8GnNISauDpgSA8tdl\r\n1TfFC4V+C2o+Yj7W8JlV/ap7HAA7Zy/fZeJXOPpd790K5Nbr8jpQ27Yq25+l\r\nE+vbG2zKe+nbvQM4jNUX7xG1dSArpk2nqPmrsRRoLXqidSn6sEcudPmHw7Rv\r\n4bN3CBKoAehrEec/b8YScfh+v719yUt6H7B5L/ieRc7OlTO6OK7HlZ5lgnSt\r\n+VFSDysE3/Cu0OL9LOf7xv1SaqOxsRbS9fI=\r\n=dKqs\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "6c438917cba8f76aa1537ad25cc84628645f5a87", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.4.0/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.2_1654688887784_0.8202023933099059", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.3": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.3", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "0d33cebec668aa4d598fff15ccf38afeb07fac98", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.3.tgz", "fileCount": 21, "integrity": "sha512-uEMqz2in0Tjn/exuyJH/ThdB7pkkIN25A2iU13lWHS++J72jzGXeUcUFRt5jTmk7+1US6sSTBERNpfQpjcUJ7g==", "signatures": [{"sig": "MEYCIQCd4F8eKO9OXp2nbx4vDKcz/sPOaUW8YRAnf2mURzH/7AIhANiwyMMytxPiSiaTFutshT5jZPWpT8RojOCywdkWeyMI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqHjyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFlw/9G44yCsx9331blfOrKogipSs4Fh4evFwJkHU+4bh6P65WmS76\r\nNeOTDpLKaEwV4VqcThKzEnnaaubqarWVd3VK7HNxGM2mxXsYhhfnsbRLe9Pi\r\nUIOBzLI2dzM3hU8uttlSN0y1+6gXXaIwLg/Cbp+9q/g+y+atT50ndZsSsVi2\r\n5kSQq5Xtvlktyndue10mTi9Au03hTxXOvhWzIMhVf1PAjdwLQOTdmMVLEHAT\r\nrRMyUY1e6sP8COW3GOcCshtBkGobfU31SvrPaLiAylsaFYV6AE7I4pauPCwM\r\nl0CqSuZtrmS+8corUh7uxeDc2YyunVDlGXnQeNaFLUS9Wb9IL8JEXg88gV9h\r\nSxRX9+ldHXqPMY/CwOCqoqNa+Z15rsSaOqJfCaIAJdk/jlsWbr6GnwVaGScr\r\nZx6BQ1jhgtGKtsuEkJqyXBenumSrGvvryVNNY00Ya3X+P9RD3Vkq2idmcySO\r\n88sAHFopFJFujWP2vSmjwpio8W4+HoNIKItyrI3aG1zhK8IOQqo/CZo9j9X1\r\nYvgrsqsu92aV4kPJ+Z3SZ4tw/F847ZX7YsKulZr2/5+Ew0LgtRb5B3uXZsuo\r\nWLZot9476zTRsUk/uBqSHuSKHjyLJdgOZ/QFv8WjLTa9iXSIeakE648ZrnuR\r\nkQ4H1vMYdToRn1wLjG/r7lrWw22Ph1oKbLM=\r\n=S5un\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5f4c615ce2dff17b5427fb236495f6e8c37803c4", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.3_1655208178276_0.7349275878588066", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.4": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.4", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "05a3386c2d60f15343615deeb3b455117177e57f", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.4.tgz", "fileCount": 21, "integrity": "sha512-pdtsYgbv4WuMDicvxxyR5v+q3hAVuOad3kl3a4qmJULvxeVnfZ725jO61f2LTu445X7f5qrSa/fmgwMmjExh6A==", "signatures": [{"sig": "MEUCIFbIHnasbCwnaLktOjoh0Ye7XSq8iOPnVGdFctpEjcEFAiEA8zAzCuezWXvgyZbUvwWDJZHwcNjHH68DxeQq5fdxXGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqzkBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNNQ/9HotEIbb9ByCamlwdtSs4cRrIUxqA8mvc6OZ6BKmWwSsq8H0L\r\n6ZIiTOYUyw9ccXsU4T4pLEwlXReQMGsbcf6w3gx3i/Eug28GQ0Aq7jhwNDog\r\nbYFqJ4uoeZJKiNfmNR6a3EUNZdQll+sX1eT0asGMKUPYOn+SeXiagGZBrobZ\r\nEbO1IqY1dVSvdjDKMPwQajfwtEzeI9BASSqAnJEmj5i+pX7wW6W45LZl/0ra\r\nfHopeU6KgoT5bS5jFi1On2S0WHEEC5CgP4Ms4leD5u6hvOM2sFVAB9lTho9v\r\noXDJOO8tZbQxh+1IKNtffAFjjBgFXSv5IDsBwlpB9CIuahiu16J14vJCisuo\r\n7GZW7Z1oz6Qwj7mmbwGbEGy2moVtQKwN6s08c3JMp1Bf8PIlNTcGQzo4CmOx\r\nEclx9ixm0KGx060QvR6vGMzNEMLt9so8cw3SToluInsW3ElyQVSkuQ7REEYm\r\n6jnHsZ2ru14qCmvkZXQtNhOjSXmsTN3/TEkT1RKCp3jEMsYxs0T7VpDq9r1v\r\n/LWnNFAX+bPK1moJumeIPrExdmBtwyALY7+6Os/A2CH86F8CX/VGsWtKHmUw\r\n/RJAGoBPam/LBwGruZrPahBu0osKyj8PuTloZBNmTBxJePfyz9k7F2FPxg9/\r\n8LuAkdiQ+/vgiSi6WBS2OnoW/Y0YiiGUA+0=\r\n=0OLi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "bfcddeda00aece1f9a58d16e29331f7450755e75", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.4_1655388417132_0.27992749911813086", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.5": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.5", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "60497f5e386c1cc59bbe4ece4570eb354d604e73", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.5.tgz", "fileCount": 21, "integrity": "sha512-K9q7Kya3aZgn9HDGzwk0JyjeylijMocS94tVoBas8vXSQNykhAFtqBF+AMn+T8rQKylkrIbaNP2afIycjKiANA==", "signatures": [{"sig": "MEQCIGk3piByD+6QOQDx03doZM9DhH7dv1BhWWUIjhS1VwWOAiBwJ5OgqReXc9vcwz2YRBE/twHzqPy3VP05FNtyZDEf+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq2sDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorjw/8CO43AqMKk6LqV42meLPFa/UevoCaV+uhw75t2a9+aRRw04PB\r\nvee1IiV+wWMpSI5x4Myyw/nimTQFgEGWRjRykEnNLZ7aJCsXJ+DuQSz2EZZq\r\neX1oP3EDEephimio75xOtVKqJjLluWOeN3jfi8wsZrAyhV7GuI07l+FRzWcZ\r\n5PNI479q0cY4Ik2DhaJaMlFMWzKH71NqXF3qQvD6Q1+5wYr0Hb8pSedmOwpj\r\nu/4p0ohwDDBAFINljE88Fyd2AgA2b/RSelfImub3TZ5/B7LHWwRF6TNm5Y2D\r\n6gp4lFyQKWXO9mVnKt+uuufjdESRBY3rFKjDLh9a/j9xK2X8OtkimI1Dab2a\r\n+dmMo4cLEn407eCPwEfS0Yb1Jki70v5xQjbhlWxQaefx1S/hE0n+71y8f/on\r\n5wRUAuvy8uBCFMWooKHKx88FkaoMpoYvrbGy9omwwyb6h3/3IsYlsEIQU/37\r\nM9asbhV8/+o0Vse067zYpA7KcqYQrZMI7WGiA5D3O3/tEWBs2UcCVs7IDhw8\r\nJEc+Z0ycvO+vzjUdTHIPhKdAMm/VwGbTSt/m1OPAyaPswmwZ2ZpmxE/NfohW\r\nH/WV0qY3AtL3Pn+gRqgQ6l+p+3/LiDUwewOnpmPCGed4fyGtVgL4v8kDePEN\r\nzyMOMdoPvROQgFdmuB+vr1kdI9rokUUBWgE=\r\n=K1Fr\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5501a6fc61a3619cd0b0f102037b88d20b0c2046", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.5_1655401219471_0.6516960233715672", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.6": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.6", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.6", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "4e5d6e8f2aaf3880a62964a1286b7d6eba6b319e", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.6.tgz", "fileCount": 21, "integrity": "sha512-OA6lCgAYbzW/cYw+ok+umq6OjeULMcMcnC2+4N4IG23xGOv/rWVbsAJnDeV9uU35OdKOzNHl6gpcyHLP9NAaoA==", "signatures": [{"sig": "MEUCIFw2ZLslv4jo8mSeegu9sDavkFchlZ+3uG7LLavgF+CoAiEAr5Oti+vTNFVvOHiQvM8FAk85vDTpP+flJMtHO2TI2no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirF+rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRog//SP2J2oDis7TzH8wc3TRPcWPxOZdwqlwiGh27TZDMpnR6egA3\r\n/4u02BWvgMUbd42fbh8tlBQ6RpnHPRh1v0rmNLMCkDIJDUU2yzp6+XWoRANj\r\nlx9GYwqqFQiRvCxAFMqZoW34jiJXocWFkEm08nt4Ob7CSyHekTzTWP/L1YXo\r\nby+g77KFgVO54ps74gOfBGzgO9JYF3HRM9eRyVPyyoGg2elmL/XwWsN89IfQ\r\nUJduuRWU+i1VNDSwm9vWHGEE823PauIYzNpZBhujG9CluhMS967ujrqiPI5i\r\nK8mWkJlHC0MSKZIg1Ijzvv9g7pPvEgUopkMVuKY6F+6DhG6FC4hbn5cxo7I7\r\nIV2Spmtgx5/otmG4I/Aj3fJOCtBvLgOciNEDdrdr5MqRND64x7atl21JDaSG\r\nBn7r8LLrQguOZIyu3+z/Muaztn0629heAFzQJArwdqQ/KHIWrnr3Zi21NKLw\r\n70sYN+XhsgIulnjMSY2dh51EepByf9mjvFX6zdSXCGemJdmbDDPQ/h+pFXsI\r\ng29Bjl1V62jPoomDw6N5wwOIAzSyNKVazEXfsB0RSlavU5KtZxmka4imPovG\r\nKZ7G1PnxXXc3xKuE1CW/yA+Mkic9fH+QuJYypLZY9nWNqfpwvJ5D468qPOpz\r\nc+Qx9NuWY/zk/8+8XOl3+8n9+VHI3BAZ7wE=\r\n=+WOF\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "f68e780877a93d63e11e9bdd16649d028a000e85", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.6_1655463850834_0.6733324917516303", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.7": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.7", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.7", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "d7f47d4f18710495094049ed88a28c1b207d60c8", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.7.tgz", "fileCount": 21, "integrity": "sha512-biH/Uxgql+PshksqThvCojd0luA9mnua3s8fvEeCwanPsNa0arajG7uwugNQ/7SOFTT0F/LY81wVZ89QFC2H4Q==", "signatures": [{"sig": "MEYCIQCTFqJXOE/24PdrazT46gbTDEAhseHAxxA+ML5tHO6v1gIhAKPSmLOuhTlNvHfmxAzlBxrc8ZkpFaeIOJ3X6jwNBWLi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisYnCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/tg//RZJnDSWzAnOUOPzcKc3VF6kbR0UsHGAXJdZp/rfkIg2m5hZt\r\nlWAP/2n9/y8T+PsDwU++E33P8i8xvp7Dq0aVt1MIpa9DqUb6u1wZlt9lM9Id\r\nR4IaHaQZvYuGo3rUUjSN+ltetX5UcPckA+hM6CmAOhd5Xn9Hw/oRa/M0GfNo\r\naLsc8dlH3ZxqOr39DPkadFd6XdDZLOlF0k8zH27Dl8T3hjGP2K9Ds39ejEVy\r\n+vo821wGo1SyWfmOIPZ3273EBE78ZnO4QKiNBkCk/zyNLmKA+EkqN7Z2eQd7\r\neqBRRRgKJJNo+Ixg5tKuO2xqCpCmD/Wv6pe9uHiQNBiULBh1koaMP0h5ZD2/\r\nn4Sl5CKVwj2C62qGqlhNY1hf6J9ruYOHBjaj/ROO4TmxnBYOhO3tAHVD1Hvm\r\nF7NDN7uq/DQe2EZBYauVrEfMmvtKmuzBAmbD2/12oArj2BRo5WT8d/BGjfr8\r\nhcPrk25LvZnNawALeBsd5agingKBeDUK+Z0IyyeE0KYhV/DPeRhouC7g+pe/\r\nvZOvs8lZC4wKlbaWMFKwloXa4GMLky3bLZV8bc6hn1cf8ZKo+t0+avRu5+wY\r\nzMxZv2h5Z2OQa1oSfcJUyBPC/dRlLsG97d91ooAdA4THoLlVIwwcv/2PO91z\r\nDLfOE//j3O0uJnZhpwjpbUucw/5Pn82J12I=\r\n=xW5R\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "361278e7ff4df993e9a3ca22e2f89b1efa3701a5", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.7_1655802306456_0.28589384199963064", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.8": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.8", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.8", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "5038cd37cd8c852d57dcf17b5252c8c3c8a0c8a6", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.8.tgz", "fileCount": 21, "integrity": "sha512-9F+9QSWPOQY7n5DLTfxV45JLPyYzGoA2Q8MRv4jNzQ1v/quw7ZEl+uDlx1VmxJl+TiMdhP18uF7uudIgh7iRiA==", "signatures": [{"sig": "MEUCIHFB8vmFH5+OFeKEgtOjHgOyNENC0yZTHTzy3wXAw14TAiEApPsOKkUIhYjGYY0eNNHn2GX1TA+YN4pGXvwh5dIxqoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiudjeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8SA/+JpLt1z5Bd8Nf4yShlSz5b7nWvavSUtav1rk8k5ZSw3RLhole\r\nFoowsba5ywSDsAhGBjYvkE+V9OOrSISqRffdAh+y74R+7O3cGL03jjZIkBlZ\r\nCu0TJhT+G5+rBMXhFhnkwRDiFBS4uEKfwtOoc4oGlz335MQw+dEzpU4EjGqU\r\nh/iGZqSsfFay7mtLOTijty2zsBjCj8zdYeEZ6gdz+GwwB0wIOOw0vdrDSSb1\r\n3QUoVCFvufhwekkQClVenbTdMPe1l/cny6H85i0M8gCZfY6pRxMmsHJLMArM\r\nQPqRzWbyAUc6OhHw6TF4wAvbYqQ0c95znE9YZPL/8QcZPiNz0mcH+ueTiy4f\r\ntT1Dru9PD+WtCdyHjTUtKM4z0hdCks0JsQdP1O/UgRIQI6f9QgbFfRDAuGdJ\r\nY1SMTIjAUCQVFK5gUy8auKdIFgJQl6gYZRXeTAq6J/k8Wfy6bDXFxbcjY8Wt\r\n1EbNGsqcMVdTpt+8hKkRL2LK5sF3s6tFHD/JRQyK7eB2KM7vwsbHp5TCQoaS\r\n9kRfxZwLIw47ausJ2BNwrYjkcN0EV/DsTDXokOY7fTfkJOVFNba21t9IepcK\r\nNZp5flyP8oD+/1D81ypvlA5BrC3UA1DdZkIxxirH5H8NwtC4sU9JnT1F3Xqe\r\nVPV/Iq34rH28erowquZcsr1+E1HQ+dQJB3I=\r\n=dV/E\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "71b0e662dcc1db7559da28898eca208c0a58c603", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v18.4.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.8_1656346846523_0.9167301414919535", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.9": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.9", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.9", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "91b5433f9127c4d038aaf25fb3493aad7937adc6", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.9.tgz", "fileCount": 21, "integrity": "sha512-0CREVDZ+pHVjB0zioOGFbwgAb38ixdmk0FZeFqGKDZXK0bo5MPA72WDFetqI6ky65T9I5YHdIbQO41HUvpQjLw==", "signatures": [{"sig": "MEQCICqadCMc50cVXuWxYwcV1rNvJJ5mmA7NXueaiz+AY2x0AiBOt/sf1XERkXMaMiXcCPqW+oZI3OpiAVL8vr94Llrufg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiustLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnZhAAju0p1YZycvKROqngNysnxv/sXZYEc4AAfqPfNw1mSFzrQdVm\r\n3IcZfME+vHPscYJLJ3AV9F4XvJDe+mmne0lY+WlUX0sUsArf7v25ExwAzhur\r\n2daoHubjTd05/D5tsr+uLQpnRsk9jCLK2Em0BFqzlkF79fvgoz7WRBv6GLc8\r\nqhL844EhWVIQDFQlfq7pjuC6a9QEs/Tbm6wjuiLXeRujYgQLxdtQ2OkLFyph\r\ntxQrTOIbRt/LpWE8L5IMNAJDEjyoP8X2uSeaO9NSasOmIC1CoN6MwG0M+PXw\r\n4VKWSm+TzBQ6jSbW6GT3CaAPGiijS5IXPRqI8ybgYdk6868op88pqmIWoRin\r\nIaYp8l5D3G52x8iWTbRniXL7FZ/QaenY9fWnIfVnSF4eNNLVQEKQJ9KRgMH7\r\nPvSFqh5EkK6GA3e+vytfCIPlsHizDPuGTcvNz4PqIOZKkHB96M0nMbDhVH15\r\noM5KlLXedUoqNQu9qzyPi/kpktPNSbR0fdgTYFxLXCYfCEywWK6tDjEfwP6L\r\nQL65mZF5pKuk3vtlZiOkOVQ6zkZw2X0KvttDKSGfy3WQO26RvV0ApD8erHqN\r\ncjwvv+IFsQRYwos4OuPbAdZUInWUQuqXBFRvldI6t7mDqNIHpZHhoyk81Rt0\r\nChPsyg9Vp5MiHFk4CU2Zgy5pFnSaZhuRDZk=\r\n=J3r8\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0a9cb014855dfe8dac6d0144d646fe691e7415a1", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.9_1656408907734_0.44930219160647633", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.10": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.10", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.10", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "90bcc46d5a07921a76da1fceb490e9e196bf98d2", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.10.tgz", "fileCount": 21, "integrity": "sha512-AHeIdWp1OUf4kvA4to56shXZzM66bR84LMNTbYGY7G3+BBr+VkUcvU+hWr8JYCOj/iAHi/fuE9r1TyrMm2pQaw==", "signatures": [{"sig": "MEUCIQDsouVR3p6Csgn6fNSlw8r8mWX5BsflBlBCRHoo2M8lEwIgFeRomezuS9qtgORT/ErnoyivlyWNPEHfPc+LHPajI68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuuctACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxXg//cwrQBK/TF1JNa3ryQ4bDPwGHFe65rjPvs30bxMZfFlH0sMh7\r\ne+MqudhcpkVy4giWXNgdlSLUXjn8aluxSUVDM918nJS7NysjKuYZAc/WSXxQ\r\n499XX5/bHe+9xuTQnk/JestWGRgtY6vL6CoTq1xUCQbsqJ3g7xzVI325CTd9\r\nZcMdmvHo9jqodfAL4IVyryTv/SY6/jL0rB3mauSH6uK5tfpAvwjLT/CqrTQf\r\nhmTalMWF3z83Jk1+rqOCPRRIb7QR9fSyuEZ/jpGmSCYLAA62/bfR97EyARNe\r\n/5DOL7CWq+kyi1oJntSLz1XBYDXrBMJBDZub/+h4+Xujza5bhyVZ6HCLQdKx\r\nDvFT6mlym175kbpo7XAs885wwGgl6XmdK41UgUPQdkYmyEVOPJZtyVa4IZlE\r\nY3luS/FX8B643Qfy7HihEDAP/mu2GIKhJFrK0wX5XUScrYygQ8YLRdQVKs/g\r\nrr7qzt0xRV7d5gqFY5dss92m6eeIYJF7PmiVuxMZc3/12ni5BRCSY78KQQG/\r\nlDBVKV+X5GXOmoQUZmc5NM7IBWoZFGH5xawQNavkV6u57wycXgfDhnH4lwql\r\nf/tCQ3WcX1xp2r9dYsNhuIXVdtbnxlr31zmcqvrZIn4ZfdB3x5T/FjPY6ckB\r\nyTNQVkfLJ89DkEzRDrZlPX4M2Buq6Idg5UQ=\r\n=bZYr\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "7a48989c137eb2fe439efb6e155b3b38c931c440", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.10_1656416045415_0.1761926562649545", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.11": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.11", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.11", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "77b802bcccaf9f0a7d47304c0c7148b2b2e0f9ca", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.11.tgz", "fileCount": 21, "integrity": "sha512-1kgoF2TLbkSa3gHjJmCh/OvUOF8/wJ6mtihHWbB6XGQvSbIRgSQopWsWHRwwV5nOSSi7EbCGudrm3NYSuRR5/A==", "signatures": [{"sig": "MEUCICti4n7Kar4kTHW74GRXzdLNWN7CJmMtL4pg45zkox33AiEApsC7OFOJOxRS8TGtBdj62WY/qWe+lYhQhibyYByNzbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwvrsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOLg//XrESK4zL+cyTa/BgYDQMXcDSD+XcEGwoYK2pHPO9vy6Q0r+h\r\nGYVJD3+XNOnQYpF6PXH05HayZDmLkOgfPl/yFV6IpLSOBzDudhg9DnrinWBY\r\njXChHIMFEZEh5jtTpSjoWClIRBnRYBTeUQuZomgmBIFTHGFURAXlbWGIu0rB\r\nF1YN7CJXM27YQ/O9k2MCe52anDrmWSFN9hub/hoM8f/7kyAAEk0KmlBcOglE\r\nvXc5QAmUxGGaeRb+oqZYgnSiJ70/iyvG6uTmfwhV3/I43EVnwxJ1calqHNOc\r\nUbXIpYzKQsfKn1LlozFwrNOWpfpHL8rbAMd9sau+OKXtV29QZtP8qI+hIzdO\r\nbP1Xi4JavGE2Kv4al7LjL48P5F1WyRpij9+WN5BKeGynLWlW0uE4CnQbj+cn\r\nKX9deTg4ibzyjglhAtDO0217wdzymMeT9aYDQRr3oHXW8kx+7Z8j3jvsqF8l\r\nQDuIybtrJlyZOB8Ptb5PzlCoxoCMIHFEe4GUeiS/L8Si4qgly2csKVBjsNSS\r\n+BqgI7CzyUWMDikvRhFv+NU3Z2RAmB6OKm81sbG0rYFkq1UqsKtV/Y2OD8u2\r\nE6JcXevcOkQNaaelQ0aKIYlJPP7gex+JGqMxqALqPFhApzl3yqD4/kWHBabi\r\nQOu+c4YYazpTXehs2z1BQqmE5Dp7ia9cM+M=\r\n=YESD\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5381871804e3d5c58a61154a82296e0a7096afb9", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.11_1656945388349_0.3122344335723608", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.12": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.12", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.12", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "fa5cbeff6b52eebd9dc384e9db6b1b5b9924e19b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.12.tgz", "fileCount": 21, "integrity": "sha512-Ao92Tp0dqpgic5jnr0sltiuyD09XGSDxGSMhwWXvKJWHS6+K5ppaXLASKKMEoRU0C2XwpuhYiDNqgM/l2AtVjQ==", "signatures": [{"sig": "MEUCIDWgUP3zboHUfD6ODv6KNtdJrgpfbhg7oARdTr2DjPkMAiEAus8YMBHSmSllwZE1P/xAUBVSXZV9HlHJjh8jlp15Wt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1VdXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHUg//aro+5DFezssgS7mEbjQLP+8xrvkgj63BMfQEdbx+IHP9duUk\r\nZgYGapzEmkXHrBwVIPiwCbAAGCc5xJJpCo7WlCFBgaYYicWmxieTEeS4xm8o\r\n+Toob52pOEfIo95wt7ls97udYr9Og6XtBX1txQFM5UtvX2tac2tqgkQVehZA\r\nWpWpye0gmkKwuGOUKs5PCCp6zSPmc8c8VnIxlQRd+Y/8CfHcbpddmpwctfvl\r\nA7VuvgFrXg9Hz6TMpS2E6ouGM9IswZEbpEqEwPXsYQtrtjoH3g4Ak2o6p+Vw\r\nnT9hij+3B8Rm4QMp05lnXsGXDlJeCqAlr80ZDcgtXsrE0k3K4gawdYnFrHBv\r\nCDmCgrtpXjo4EdtbdCgW7cTLV9F5PQCZ4n9EMjOtDbvPncSe6lW2l7FNzzht\r\nrslyB73YIWfsp9kLJuaaf4L0vO9NM6UxoG6Mc8TSQjsju25Ngtjvvz7UxZWW\r\njyArq9M0cyBZks4mVctEI4zEJjCXQ3bMvrwtmAZTwHsC4TAEvU5wiiD10nyW\r\nbSX44If8l8O/ApKXrNYVHk/vLoAyjczUjE05Pq7w3t9wRV8KYrLKUZ0sQ5ud\r\ntkqwqo1vqKAfffg0shQM9FV9pMbpnMy8ULhYe50nhL2or7bI9zBcP5xedlLI\r\nHagMmfuN60TvuJyP5PVy5ARfyc66kjRFIgY=\r\n=YPOo\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0e0c9d189a83b070551b04c094631f9e0b2cc8d1", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.12_1658148694992_0.6206229695640961", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.13": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.13", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.13", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "9074e2a4ae529001f7071ab857a8ab633f59fb98", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.13.tgz", "fileCount": 21, "integrity": "sha512-SoY0feMZgIGv7bl8MuSum45AP0INJPsFadwZy4JJAP6AIGk0YGwRXuRtTaCNgOonhdaESDamtwyXpykHcq6prg==", "signatures": [{"sig": "MEQCIC7wwfA1bqTnvSbKEE+Yx9FZuEQf0GXmgE/zbZAvaLOvAiBO12akETZC3M0vabnpTmpVaoQwZY1BV0M8jtzd2qM+QQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1ncHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqotg/+LVqhIGBLoqPPim0kFq4n60CBnCcAM0Q1MhLCftT4EYN58It8\r\nHKryBNSWIiWDYS5frQQ1sON6zH+q40VPIaqzfU8f7mrfjr6OsDkgzy13Xir0\r\nFArLCTO0dOirALOh4ytHOdNUppuIaj+cSs8NnGM3k5mA9j5AUciSs5UjF7BH\r\nhyqlyg6jrVzwei7nFqAZkYJFK11netYEurbAV2bu8NijZx0ayNpzH99SuFl8\r\nqfaQU7ZHuj2CWOphHT7jf33ZKCwfPJKh8LQsZGfEnH5SkVZ6Aa+LIk24bCVg\r\n2zjRsNkL/CnSbhfHx1OmsfeTCTig8RB9qTfw1gNnTvwiXDpdEZbJ2do8ga1w\r\nXNp0B6dwrsBl7PXqJqFXqzRZEDjJHvmQ8sMadKyl+KocGBwwy089+GNGJ2gs\r\nPd3/yPx5dObd0i4k2htNH/Eb/Lm5fhpglclv0qikA9P6MKtOePq5wLVKf+Oj\r\nvN424yGZrhCHLSfyl5twYA2llqgHssYDS3UCKEdotueG4p0fdzUf+5l9GH8M\r\ngjNIId/tm7oiULaa0W7xdCSLy2JL9mi6jjb2r1HeAYp0GXsawFooDVoFl/+E\r\nWpo9V42RlO0scbzi/esgBTcRK3m8n2UE8ix66kTHr7/Pu04DyKFT7QTHeVwB\r\n8KqJqleozj1K5psT0BWswqoCjCoUmE9o0RY=\r\n=OtTf\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "37d1fa2249b8082a36c56f155ca33cc545fc10c0", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.13_1658222343032_0.21234594182019495", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.14": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.14", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.14", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "b63a5e13061183df53284ff4e29700a51c14d445", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.14.tgz", "fileCount": 21, "integrity": "sha512-PMsUS4AZpOruis0j0ZNbEKSp6YE5vKY3xzSA8uHdYDE8+6WTpGvUzSYxkpAh536+FaO+ZEvDJRoDeWe2UhN70w==", "signatures": [{"sig": "MEQCIDG/VvE/Ig+J6ASD6eU9vrUSe9u0WDbtk9rzgtzz8ISRAiA2Xn/A//7/H37K8PxhUT+wRNclNNoxomGGbI+fatV1Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1oOhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6Qg//RYUfe/W0nAvMGenDDkwhey0sc2DLJ7N7XcRGWfZXT5qukzk2\r\n/TIQoqI6ZrJ9lXIe2iUocV7GevqMncOc4Mwz3acYYl678VuZ/LdPO7i5Z8It\r\nhZySZAzP28LYBrDPhB+/xtNvTNI4mPa0XCumQE3pxKBojEz60zIKi8pY5iGG\r\n8tr7YINPHfWoVW3a/892RhTw25vCCwJB3Ee3Wl2zlOVJUazSB0p6v9C0RxC5\r\n26RAsCPhl2BwZV20wSbQQNmFRTbWPRJNtyWGV5l24mJkZFNlUoLDey5g1t4L\r\n7NwkdQxl5ix/JRT+sopmyu1e3aNFcZI53prEtqiSoLmSV+GaqoxrAZA4eFC0\r\nZ39LkW+sBNGAhZlik/lnrwddmDb6/o0CtI19VTr8wej25ndGctr+4wfp6NF1\r\nSsd1yB5BI15+JBtxaeuYGSRqT7OBZEz4TQ9K4T0RoVcrLWluRoi333rwfKKQ\r\nMkSIiSsdz42Pu1fe86tzKTvIHYZ7pBSWjVXhKVFcJRsQy3MIsFHXtKbZ7omY\r\nS1qmuZMs8G+oktGxaeKe0jk+79Ydhpi1lVABIh3wNS8NV05G5joPVMOJ9LuB\r\no19o2WysPxjBaqNUjBB6We7kgTGNTgesO3eXTAhDm8zBiAioPd6d/KwhruSi\r\nrdqRx1vgn6RVzDAhOZRv1Y6FirorYkRjIV4=\r\n=n/Bw\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "db9ae3a301eb40d0b40c443c6cf7c312e1d97e83", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.14_1658225569476_0.8068771798757763", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.15": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.15", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.15", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "b71e3019df8970236696d0ce907409ab9ebe5d7e", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.15.tgz", "fileCount": 21, "integrity": "sha512-IBCffDAYzmF88jJJocnBU5aF/RcEXt+yvPVOFFgwMSeHM9gNtcpsQbLQjivHh0jxLc+NmbVsPBUwM2RX5WUbOw==", "signatures": [{"sig": "MEYCIQCLFxEvdKsVLg7NQ/joqHWNd4O6KvOFicN2Eg91RVdSDgIhAImXed7ycTgoS0ABJEsikuiQhawsh6seAtcZLoxH9L16", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1tZ/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoELRAAkd7bc9ARwlPJA9VwjTK8v7910zPeJIPcBkRKoLTlOunYnU63\r\ngBnLo+o9MCqX4zA0Ft0pg7ytWoy1nPiL94sqykF0dUUO0xyy1omnh0P+IZHQ\r\npqgIq25iLQ9fEZSySz1qZsDhnNMOuPp6XcAr4o8xgCTM1TnzlbWV6kbdzZln\r\n8qWVVu5rdU6UmZuXpaWOsODT4CeF2KNBoF0/eWTkSZeUndKdKlKOrIIFoveX\r\n2gk1VcTPHaz/ncFhDozADw6ybuBkHb4Z93xCBT30aBJuV66Wx14ihK6rmt9D\r\nfzT3MTb7nb6jYbgaPqkcoxQfqy9AyRbyXt/JFWWuiUS51PAzPbmktF2LqYlu\r\nG6UApzgisrdjp8IHcstAKpJCuxZcC4nQeqoBHAF4GiohaoFfZ9Wnu+z779Nq\r\nrdtEa8eUKB1cEFiEmhFzBGrVqlKMzcWZyOUDLo9/SCz7wqKi5nmDdO7ByiJM\r\n2gveEP+IIM2/gSBCR8swPDiojsb6kH0L2WzBAS5TzUnQcOg7UAdEJiAtEBs8\r\nd1wavY8A3Lquw5ybCY7R4ux+G2Hv/0W6oMvRp8Q0+h+pWWH1SCO9gE1NLre8\r\nflOjurrwocyJO+Tz0QvZVvrFsre+GzuF0mYdCfms5PyU0bSMeYJZWo/Sv5M/\r\nQHDe+sCt4qm1bE/JtIdagb+CMdNBmfTKqFg=\r\n=Od5j\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "1b84c548a076520898cff368b2b75dada9fe6657", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.15_1658246783244_0.4792102221361987", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.16": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.16", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.16", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "a9169dcdeb56a0466a854f4992aeb1ee3256f6a1", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.16.tgz", "fileCount": 21, "integrity": "sha512-ns/V2YAAMMKmWPnbB0guwsoEVE6Z07rgOSsyj7dUo+ivIozPaGXM7a+ZQTUO54rNp37haRIKu0gWLtWKRSDZ/w==", "signatures": [{"sig": "MEQCIEN/8MUurT5HI3Rh9p1bnvbjOoYRUh3xBIB3898z3/jnAiBsfAaV7jzf51fh3yDJRMDZjL5e0qlV9o9u8qOuLTdFug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1+fXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr75g//YB0Czf0zGMJ0cVxiLdffm+9LGz8RKDb+XjybVBQ+MjGUv4XP\r\nMnunI0V/TDn7v23oWzhUA2TmVbf7vxwdo8imFcG25+uwnNht01PbYOk/atvZ\r\ntjpdGKSzDru/nN/7eJwwwphGwaYPcTazbHocMUDItiOe39PTqNt4HxH+rOiZ\r\nWp9jqXzksgiODTBw93DE/Gj/oZS7RU7a2p5p9LFfI1FFb8m5p/ITfhn6WluQ\r\nCiTl7YHtBYXSXd3aILo1N9iKxAxvt6qU4saAGVhoGWbWG6UQKtiGOoRAqwCn\r\n0Jvb/Tu2i7JFTRFYiVDgUUWrvc1Y3X3ZmdDMbgmwoRk/0uQ3q7pMu/jwajtk\r\nIUVDljxhPJyHtrc8aKUIoJLQfebDESaqWLKzmkh9OovPf3WHpmaEKd0ErBmq\r\nRLZyugK7DzJXDdOH58EMtZg1DhQ6u0hlneEnwOZHeUuV7kLWyh3Y6kGpZhJn\r\nS2K5xwyJI9UGPw7h6p3ttcpkFUF8Z431H0AvPwjcfo4DWb4lWYoP3nSfqEh4\r\nT026evUEGIeC6xxeGEk12AMHaulBQh5Z02sUUHEzopZHz1Xm6zkrpiaqZfep\r\n6rNMZYCzIlY/aOpZgWrqF4HgYFKV6VnEbYh5e4q7tf3ZNzm8ve8mfodOiUVw\r\n+u68CApqPVEn+6JDfSYwUvGYpiU5XraxizY=\r\n=7TKJ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "fc869735c675fd622e35458ff8809ad2d0ef949c", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.15.1+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.16_1658316759627_0.5536068273547228", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.17": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.17", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.17", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "acb6653fcf6a72e14b40251d22e0ec00f679763b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.17.tgz", "fileCount": 21, "integrity": "sha512-HZSY3NUVTMDraQjZsvv78ZwAqQhns2Y9SmiYH6WCz5hSWOh4CkFQZJg/SxLdMeo+rtraZvLcPf1eP6AqwR6wlg==", "signatures": [{"sig": "MEUCIQD1DxEE9qsADqMH+cik6pm/+u1uOEx4WGR33UWSY3gYWwIge7q1/TFoBAcsTQo7SfZFlaxTGM3RGKcJoSpnJGm/9WA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2RU9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6sxAAoh8Afygj52L4KeJlVl5Zrka8ASMy3vlAoyZ4j00QiKOlWO1U\r\nvM3VxTU4FLxKFQoYaUTkM+HGia96Fp+2PVPLAbgwPgZLnjB6HFN7Ta9zxBXc\r\nZHkN15HmVdRIni3MW2vior2H2r1AsidemDaZ+QfbXn0v4QIE020Z4V6s6Oc3\r\nnNI30AvNF5hmnv96PPrq11rJf3nichhtMSp1HcQeDHtpkJA0271da3Nu8jep\r\noAPA96I8ICMm4hhtq3ISutXxZIJPkuUu4Qb4eSekFHycSMQUtmSUvoElTAIH\r\nH6MGZ8KBnLnMVJ/7+Ct5UeKIixTdAbsJ6OdJrvFBp0KDPHNFBpaWS7yxJJT3\r\n1U2pR6Ojoa2qYqzhPnr7WH1UpTXY1CUEkTCl2MoSOKtutYWj45i7EQG0oZw8\r\nMuz0OkYPILEZWBAHV7oaaL30YskMlmnlNwxylc6su5fkbAQgr2XtWc5jzXUB\r\n2AhYXOkQGkkLAjX32Wj9COu5HrmrLLkolUvvRrh18FlCThEiK7yzclfdtB9K\r\njrJn1dmj0Hs5YQZzIT93KOpwByG75oOVa4l/YUKrbgfPgkHi1+HsOF1DZdNK\r\nRFb5xeIkuZ3cEMVbtkjNJI9D5kf/3NHtWq2KNFetlMEmc0I+wLMC97iJViEo\r\nnL5hOO51BURaIqy2E0fQ5P7EePE+lylQa0Y=\r\n=5uLg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "7d41d3153f4edacd170a8b43be3c7c4bef100ebe", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.17_1658393917335_0.6363739091445826", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.18": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.18", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.18", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "d75d2f429591b019108434ad4bf55bc8824fc237", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.18.tgz", "fileCount": 21, "integrity": "sha512-ngS3n3H9/0E26I/WkdpmeLYFb8zQr54U83nViMHKaPhpLoX9Xy0viG8VLRQu+fR13l1scdndI+Vk4ec8t3K3WQ==", "signatures": [{"sig": "MEYCIQC/rhFGGpbH9FzZ9JPz5JJn2+Nc95oYr+FEd/slvSl9FQIhAN1dbCMPyyd52CUPGU+1uMSF3ZlD2djOJZf4b4QTJajs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3l0oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+iA/8DeHJl2ZMdXIyJ/SWkCURH0p0qndGOY2V6byYyBm8yIcnMC9e\r\nvEGXMuzpUBM3nJ0fauvVs/kE/iN67vYQgeoEd3ptBp37xjD0EjUQ66y6r8YG\r\nv7nyDhYItpjho4KAuZ47i5DgvvKrgRpiihXEnNmTad+DlYpOnErzkra896i3\r\nn/ww+pFug5raknt+bGeu5W24O/XW0q0zmHGXurpBWGla9qTVd0MYlUD8Qph8\r\nefgBPftTSTpCErYSzP4IEXtaVwVxivWp+daV4/3rfwUK2c/PHvMvqvrc9C2/\r\nqkQ2dC0dRJ7ct5iqTtmZSbASreicGqEJ0sNfV5uUnp/Tgnkd0u4STKKTZwV2\r\nHfK8/rFQTuaRAnjm6FqOzyFf5ttyUS7nUCUkIPOEtAGG6i5Z19UWwln7Q9bm\r\nwnTDGX2H6mes5fS6qzfB1nmPZPAES5aL90UyMdJhTAOYGwH6xuyHc+Pm8rR7\r\n6jqq8GxTlrN04gHIupmMiufEgJ71UpQCikaS4E4i8fwrS7L7CNItgH2Gmrx+\r\n51dDpmSwpBPzd02uIscLaDsQOLwla3redhwfpTsNF/MXMLiYC8EJWH4/5ZCW\r\nbIOsqwymJ7EUQ8zBJuebQ1DhnbA3ZtHMh0ZQ3MGDXz1bDd6Zil+bmm6qYuKk\r\nm0DR24GDdR74QlFov5S1LczkeYVsbJGgGM8=\r\n=XU0A\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "4c9a3b8fa8e3335dc0ab2c8f7b7a1078f599d997", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.18_1658740007928_0.30425997960953155", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.19": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.19", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.19", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "bfca0d0143da5489c255ca68636f2cba3cb1af88", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.19.tgz", "fileCount": 21, "integrity": "sha512-fruJF1HvCM5ZUFF/RH71l0Pg5Pjuxmqob7nsh6eh2sY2xt5n80q3yh+wLMXWHRkhLkvgBYlKjbSESM/ptnV9sw==", "signatures": [{"sig": "MEQCIBLF8R52k1UOVgJIvRSpoz63f81AhKeb1f6pe7GRpjjjAiArYurLMLlnDSaAeYzK8N3QIqvjqdQppW53GQM+OZBF7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3pZFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgnQ//TTTovFEqZXZUxTLWBB5JqkJn4LDKN1wHSKnzi57hbD5kGESe\r\nqrjIopz4MCRaWGoaF1d1iTDBEyXr5ctZ1VPL7002mjBVFMZlLZlg5+RRXp1M\r\nKkVCzfoLzs7LrSsn72Ths9CEhZ4HMasXUIhSDzzR4TvOSpNqwBqhnAx8/a6S\r\nP6vwgIQI854uxBrYsZmC2LmLxSVpReIWUEGZejNvuvs4N7sKD6KwyMcz+TV8\r\nMHqrfC2sBTfX3T2dbVJJupEETXNE4T11C7+ExH4L5jWrTozCNrYb/3O/EokJ\r\nQHa0zZ+BB7dlnlCWZSOtZRusDtrtb80exo6irZVVaYKY/N58V5LBltrxgH1H\r\nWRWZ6LEWbpWeSrZb7Os7ULENVDFn96s7IxBU89pIFtpGPaQ2iDQ96zG8JaVY\r\nWVVEoDK5EvbJzERPh5ebN127i3oqphfu3QbSSm3/53AeiEPUoAVVKOrXJA8/\r\nW7PUakL+OrESyIxPl31EL7kWD7y4lCQMilUrhWc6l1oae1OZ//yeLzlFg9w7\r\n4Ekk7TFvj8z/s8dKCuPuqz+RWQCaSYxsfDwCTyLxg5IqM3M2LhY2kq0TQcjt\r\nPJv+juNjuKSx6xg2XO8MhTfvJm9Q2x3ezqgfUnHKWQhmq8UpZklOuteenxi4\r\n8Bn/UGHFo92If8KvWw0vcT6gYxha7YPiq70=\r\n=dC/W\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "8b90a6f627231cd85f7e64b97e984b4d1cf8bb5d", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.19"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.19_1658754629002_0.9651418715907192", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.20": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.20", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.20", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "10f6b75a6a0cf3e32e75f9e0d3a8d57b8f12593a", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.20.tgz", "fileCount": 21, "integrity": "sha512-lAlA3V0ao6ozqtJu55gVVAC725LqhfeJMEi73O7x9WfUUnopYvbofgRYblZSOWSvijf5T/pIFyP0kar3tzrH0g==", "signatures": [{"sig": "MEQCIAGOhZC4nZxOkpHOJwU973sdxoHDvpHMDwUWF75P+btuAiAjgSkJzs+Yfn0164JQjW7MY/y/NxqsixqoeQd0HzO3ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi35XIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotgg//Xq/2HEjNn2dWGQf6MKDjG8NBv57uUzQ3Z/61XJihyvovkXTd\r\nVYMSXh3PmfMyooicK4aFJMOsLpYClk4xJtsHr9zwVLOcjUxc8F+LRUTwFPCj\r\nRohehdb6MqEDQVffGLIRklVnHVIW/ozf7llhkyl9lGg+nsEVmSf7fJIVVwST\r\nkA0PEvA5Osu2e2H/4y4Y19p/QMX/YCMXEsEymXpzrSZqz3cGtH7gSq7wdwob\r\nPPB8zNZ5V3VIBTZH8bWb3fr6fHfzvd2EEUavk+WY9Jf/SONaqpmPkTHjWpZZ\r\nRfU8e7Kx8ZEcDulArLdI5nPCM5lSqUCmFyVDQvws+8ffVriwXriPmNYwI/FH\r\ndB2GafpgZ0gTE+04lPbviWif7G3QDD9fx6zujlCWTZmomxk+FBLUyW0R5RBz\r\nw63dQQIXfu8XLAAe0ZmwQBUBQdrV03Ct67NnVhtD5BauuPcfivWrWDsBh61r\r\nBKhgOnEQoENLTLR8GCpPv6ZmmSoO6kFNn5xM+rLCj8516127RM8RajpqEvJT\r\ncyk05uSLcudfEI6FqSVgITcjvlF5pOXnl8XtsTbXRsUeElJEfcFxNCOzepCG\r\nwgBrIyODdGoA9tapohLWQXvgibtOtqyDLOE49AJKAX+xTJ8TgTEaJCGP0w0K\r\n+coS50mByYnqBT67xtj9kgzB8pytQNPlZSY=\r\n=ZOS5\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "01e1e2a4bcdd58ba5e3e9813a46c1d144ab95fab", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.20_1658820040687_0.04705278721220019", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.21": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.21", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.21", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "fe9e9bb64f1e3bc80e210eeb193379fc2bb36d35", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.21.tgz", "fileCount": 21, "integrity": "sha512-zcdsDGO4OCzw/aPkWgFN5HArXE2L3Gida2MKv/pZJkiy9CeTIgXRYpdXWPB21SMMTDQwCYWC1EtEErNpuvvStQ==", "signatures": [{"sig": "MEQCIGySuQeAfY8MRIdxERkbxJLaywlkaIfcBQQG7HLJ0v/WAiBbsMnQox3v0il13fFZjVR6TXzmDr/h0H0XFErxiq/WVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4O/kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGGA/9HkTa2sgBTbG5Q3JziGOoSo04ISexkZuvX/FgUOgMlOlnX0b6\r\nABqPdVYL9A5AP9mMG9jakvW/p+jPP26S42CDHfSugPbGCF1joGfJA1Ldtwjk\r\nyu/8o2eDy90J+ife5z5FautMAOoii/96vEzuL99MsCVdAAoCnfueGj+5oeo7\r\nJQpOMjmNtaUvs6Eb47kPmPUBfhPN4zI80QBJc9i0+kAbRrKtSuLnxpOu7n8Z\r\nDVVFW3+zy1Iq+815akcZ4XqxbJr56isaZZXJoe3rB0uiwsG7vu6GU3sSgX1u\r\nYKPuIMkDaDIm9PLEzEbk9wjUlSsAmvS+HP5ADoPkFbZRz8nbpaSP2LzUk13Y\r\n8XtNhpkAAo0UTyddvF1+JPRlgoQLOIWTNpCgXsjQyh0yZ+Nn+3dsrAXlECyM\r\nx/tzLIvxRpS9yb0opE25yKddyN3lk+i9R+i62JGsP/7jy40Qje2ZdyCbZQaq\r\nfcrR0zPvLaN8IK7LlDc+J8ds33g9bsK8Dbr0LUMLWYgKK/xiWRrrVPCzKWuy\r\nK/E/s2Dz9/XUtjbd1dmVsqP1Ez3/L+/eHt2NUKdmppqCrdfoxem97l/XgWRz\r\n2ih8iRSX96U2SN3tzEaDzrJKmExnwUhQiHbkc2m4ToDimvYO1qwGZQGwainL\r\nwDjV980T8H9unbsYXM18Tj7OvQwhYoqRt9Q=\r\n=+cmb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "a498711ec6da6bf290e538defd7689ccfb28855c", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v18.6.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.6.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.21"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.21_1658908644601_0.622034311379652", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.22": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.22", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.22", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "5c462153f503b445fb6fdf74e11475fc44c12e31", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.22.tgz", "fileCount": 21, "integrity": "sha512-gX9dShDbJM75DZUGD5+tt13QlIzah1TTXXLpOkYUFirJ5j2tRfiE23uqpL/95HZz2jFzRO4i7zgc7NV8aD4fJw==", "signatures": [{"sig": "MEUCIQDY6J7dJAVPDHh6yYhJElYoeNjdQCGm0164q7n6pKNc3QIgWR8FffS/M2/AnBmrMG0MxyHcMOGvvdNSLZKqXKzjjl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4PgxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5+A//edLMvoN5IcN4pCmEf8pTr85JQt2n0i9fUlYJ80MkPuCK/VR4\r\nFPYSsYy9x8bjIBlkkEqQGHTboXdAMhEwiSyK49PpSsmBNQctPkJoopRwPdqu\r\nQN1LFslfw/BZQL5KQMgehZMc1AVGycK2iuMOodoXPn7HUO4b2cQ+Mz5/8qMb\r\n1bcvVHx6MtXq1jg3WQqNDiiC7fVHkJa+5Vv5WPmPfw8O57ESexWlpUjUUm/9\r\nQKJ/AVhKamvE5bE7ID35gZgWL/xGIDVFOgmokFoXa5alQLyjEjI5wH6g3SXO\r\nVwntzg9G0zNR/5c2jpKiZToQcEkdVqITzdvEk+GEoHF9cKxW/YB9bqzq3IUO\r\n37lFvwrWy3qZUkDgBm+Ni21lxkEUBStZsNrJGSV4thZEBA0s12T5sLRRCuPA\r\nVQctiyGYAS2fDzKaYCY00rI21fqBby+23bGYRbiPXTnXuzkaPVGZFxcQc3GS\r\nOoc6WDU9nbHT4db3/2DvyYSe0jmbgDaAJrEETmS9K4nCIIHmtSiHKNbAq2fN\r\nmsBvGqXEqbty8i8T7JMrKsT3eknwYP9UFQOQSXK+9vpbTuU/DEhVYjQ/APML\r\n5YdO39XuvhkCdHADXrZYbvfjWAoHbkzd09Dk1UrMietaSxGpo4MgiGpVm8lo\r\nenvWB0/3IF2aw05lQ6RsfYRNIvffguEi6/Y=\r\n=E2ak\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5a305c7671fc3311a3ff3d8c81365ba3260533dd", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v18.6.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.6.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.22"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.22_1658910769106_0.7525124029598718", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.23": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.23", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.23", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "b55d9add18cb7bb57acf184f6cd7b6edec782a25", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.23.tgz", "fileCount": 21, "integrity": "sha512-XBp3rCuX4scJVOo2KconAotL5XGX3zdd8IkfDNr5VVSQ/B6HkiTNuf+EvzSQTpplF+fiyLTpfcP9EbNLibwLTA==", "signatures": [{"sig": "MEQCIFnh/hBT2uA/m5BRP26H/S7kr5TAloGChMna7oDvxCazAiBgSqV+FEn1Q7YH6hAL6fm+97b+unoa4qlC4qjaXXblLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4TmjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ0w/+LA8oFeEeozL6Lg7bibgyq8bBIqOPU0eD1Fjbtnm/P5L+LSOS\r\nMx+ANtAb8TJP33E72n8Eb13PV0yY/pvpagrDkdXRpMKKcvJEuzKwQRi8Ibt1\r\nZPlWDMLA2LAcCXg1RnBmxgdCt18T6ZyUGpihzTUsHKrVcDoOeQFXf8J4t/TW\r\n0bDGlbxk44l8SKatXAdYV/5vQGUM/mO46TdbTeOVpKiD59Bld5g53qaDmwa8\r\nKcFO7lso0a1pH5E11uAMp1jcsdqWrq0qMQ2qobqUe1mxdX6acHt70t03/Uku\r\nPs/ATV52xDa49oIZ4ILE+rYB4T/6nLvvojiOtr8Keib0m6zrjG819ZTlmI6Y\r\nR4UrPRv3b9z7gIBSiotYXuospz6fVYCQ38D617xqvXCQoCIgZKHkD6FXtAW9\r\nDuFEgIjQmBrfpIcGbfv5xT11mdSHe9RMduRyTNpjaoIg4FuN06Xr+Be/iWj9\r\n2fQ12E7U5vZzEz9GvDiS3HReonV5cWN3zS59dLCzNoXM9MXouL6XtskfWz0R\r\n58RmjEV12bQZb/kZexmN1UFLtbXlBhZv7ki5adbUeUywgV17RV0NaWDIjaBj\r\nCbSJnJr84r3bAxrLnO5TWDWGXI+Tk/5tPbvOGuv7MpfKh7hS2755AGxUYeQh\r\nEMXHRNYQlCErtOxAZBbe+c4Do+PUJm0zTTE=\r\n=L9bc\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "25cde1c4f38ebcdc3d00bfe5df4744fe66219bf5", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.5.1/node@v18.6.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.6.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.23"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.23_1658927522897_0.663886736211015", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.24": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.24", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.24", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "995eacd6a175c8d4f7fa811cb2cfe2ceb888a7f7", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.24.tgz", "fileCount": 21, "integrity": "sha512-aKvrU9irTA/dipr/BBGA3lZSgBzwX9tO1zghQZShlJ5h1hJh+s7sHDs5xONed51YnBWgyQ8SSEqWZF7SBx8gTA==", "signatures": [{"sig": "MEUCIQDRmfPhKCtA+cGH72SXZWANRv9C5yuK9+1FSKQY1NDkRgIgFSM0xA72NjOSATYe0vvJl/onFq+3wQiTfyCmfUpO8rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi59QHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRaQ//RAjnRz0fQI4e+mVpHZBlw5rTNt72RzppcQYGt+HbSbS6EhFG\r\n9/V1A8OWeBYkmHtpa5zVR32ED+pRj9tLYmNAf+0nQVag30sM3VVPEWt5qXKN\r\nRly3y2I9iEb/Zm8odK/OWgkbz1hGkRlalR6L6NQKLxEZOxEHPphwXiDWWJCb\r\neTQ/L+9wp15OE5BV2cJOUdwTEuiuyVgz2ED6yJMIEI2cOdivdhNIuFdZadOl\r\n6ZHLe+wt5fnBibhqQlYuqMbA32ukCvymINGINCEIYiUc7AqqBT8EGjnxxjtV\r\nO8hozzQtx2uVJvVM0rlLEAgaIC2FbQ5UvRyy0qsanOtMtZ5yBsibhDV7KjQ3\r\n7Ms4+oqmll+usKdjZ42k7fccN+sz500xRW0fuqn+S3Pz/NPpAKSJveCWMnoI\r\nbOBIAuWn7/OzkdpmwhhT76/1mFfMVCjn5Upn2+aje2nFDV8bGSSBuC+xpTpr\r\nG83wEdaug4ABiz25GEuTTA5P/G2ZB4CS7ilxDzm3Uawy5eWtkF+oUG1R+o0h\r\nnT7RxUvGDM3tFChB++9QxvlXAazFb6/A09DtRfcuv7FhdVtbxmuuRFNJ+Lqm\r\nNI9AgeZ/YmDCpIwuqmKkBjnRPXd1NzK+FYlWdJK6nrqrEVn8au4Ruz+Wstz7\r\nh1D85yWiHSt8nB07FszBDNY7jUJbZTNTQwc=\r\n=xWL9\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "92b4afdabbda92f9e4a42db38515b013b8f106d3", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.9.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.24"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.24_1659360263054_0.6573927101362174", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.25": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.25", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.25", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "f441627168f26da0333436576332985ac36ef2d2", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.25.tgz", "fileCount": 21, "integrity": "sha512-xDre6a3L0KXsxQxuZaXw5ukyGh5v7k1E7XL5ooxBbaHz+5GtvCidSZ3sPvFDzetlKq7eBT4ztg4RTkHbCUclDA==", "signatures": [{"sig": "MEYCIQD8Dce59BFMmK1u1ReGhZElM75+g7q+Im83MDNsMfPNxQIhAPXL6PaAPmrtJHdjfwFHI9I0Aklh0GcMqvJ/ZA9kEWSA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5+L0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLVA/+PEeQ7Q/pOcqWIZcVPt0EPgmdVN9+aLqlFjVfJQOM+42sesSH\r\nSzvoksPNJtqOCKemYPtv6j1Tg7tXhfYotfgUXhWn7zs6/9REa37/pUa3sXTR\r\nhFGuB1ibJC4YMVH6mSeNd5GcMdMHqYP0WW2llwu66QNM5APxzyoagAtFdiqD\r\nfbZbv1gFr8HRNLdyvPiyohthMAq9LgFa0waL1D4QuIA8Sul9EPe0eqklEkLW\r\nTOwzjSUYm3eLxnTmyGpIK9qFLxqdgA7S2/oRZ1V4d8aIOlrwEnxYKBSE3li7\r\ntY8+2ldSQbRLMs4FOh4ICKXDn6mDV9fglSh69VlLiGK8NMwgjy0EE80COwuA\r\nsyIFe0ATBxUGHW7eGUVaWSmg1/nNnrz5yuOft64zmQYupqcTxcSzwBgQvbJ6\r\n/U+atpI1znil0w1ugJu6/i7NBzvMlX+itTQe248bFCUOHGTfDX+nFsr5YQWR\r\n+cJ1LTjc233kur+0i4pgvlvVOchmO3dvVrJD7TU3DYwi//2CJLLkumTaIVcA\r\nseHExuCapR64cpaKJgUmEjBdd5lIo4DM+hrqxZhLLQJMKDPCRf5t8Ln28hv2\r\nOSV3n3sKU7YadhXXtTaHw1khZS8tftG6fgw6HoEY/AL/H5QvGnbQ3YwLlwuY\r\nPiVieZIKPuvHli1y5VfCow3EeAU/5246pEE=\r\n=p6rH\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "30f937729fe677231194526b07e0331d5666f521", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.9.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.25_1659364084101_0.40478599029580353", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.26": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.26", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.26", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "87b8a877d3ab8955a282c1b3213f23b981e3234e", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.26.tgz", "fileCount": 21, "integrity": "sha512-hxWtmuO13zgNkM3zHvRENfMeavM+PAKSoHhvzt+sHjSothxGlA06XXN38t/NT6LD4ND8p8FmPJ70+fTptL4a/A==", "signatures": [{"sig": "MEYCIQCdHaHtQlDPz1jnDL87rTSUfvMxX3gq7YIwhbeOs7FxgAIhAIe821Y00GOyOKwEedb+V3IUdw+er3/3P32nBj3PIGVs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi67nqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/rBAAjG/14bCQxNpZLfxi+JgbEJhXV+/QaHUes6MJZzguqQ+UlWl/\r\nQXT3PyoHiYIre8csibmhm6/VfnkBHAbyZ48iNC4TZbGvs0JjlOZB4wbEF475\r\ng/2EkVKe1yC7uCTDgmJzZqK0speLZD2V7O7evxeuA5jRrbQYke9mzpVks+dG\r\n7fdi//nFJ+LVA0roUde7Z1uXEm4tDQRrptwWKhE58/trI8kCsmC08QG2N7U7\r\nXLYXQNvyHxzEnDS7ltUCMI+YmF43JCC8srVEStYYqGT58Dqq7fncWj/6GH0b\r\nWnMnReukz77rx2N+EOoFJcOInnCL3bQCJirqm7ftTbVdZvkfLlfkPm5Na2pK\r\nDtwVlUYcuH06Q6ZWGB5oxUCm4KX2yZ9A7N0bJ6uLKGsIJuNu8Pq8ynHZBN3L\r\npHJrk4JY9UhxV5fKKPDyoM23IQpLfLO408BrgmYSNng354Cm8t0lvX0snjh/\r\nQX12sA+iwxu/3Feh2pOtywVV5jXGyeCa/ljdf8x36P/Ftk/JSYNGVSdoxGuy\r\nZZh7zxA6lzWcoLTMoBVsx+ovBe1F5qWSsAJpvEmJTTV7ZmOxrjO4Y1OAQwob\r\n1HO/HrIA6L+DgYR49TtGr1Q5IsFrKgrX7HEdvc5KBqkQwXubqzNblYYrfNk7\r\nfVZ+DvISKS2UHBynbDrypgeycp0mYUxGPYw=\r\n=3/zn\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "e89ddce0566537e64c282229b0a2df63ea0dbe6d", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.9.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.26"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.26_1659615722641_0.8442973651929597", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.27": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.27", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.27", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "9046029f859804685f1bbb2088c8f0f9c0e08f12", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.27.tgz", "fileCount": 21, "integrity": "sha512-1ua5EI3GXKDDdjF6+1CUi37rmpxXMSWItCCl5sIMLBaIaf2klgnVYzIFVXORvXp0fKh1N3jEcS+Ykkutg/uzaw==", "signatures": [{"sig": "MEUCICGWf7Sy8fqVNbxaksepImA2TI04H7/rykYazSTheFTZAiEAhttl1KuNVdHaVNlXlAHwCG+pNVuKWNp+j8ifK8yWVfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/02jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQHA//fVT4K6d721FlpcAUpy/OQexNrvdp4rreXvuvdfJNboIQl3jY\r\nrug5fyN8OTiKYoq5yO70SltQtZYzrABCajIVhxkpChZTa+B9yRY2/twhYDg7\r\nQJbqABJyQuj8+GpOtHcr4VHZwnWe5ziUuNqrRVI6vGSTKN0DJMIW+OyRYUyk\r\nR4ssq//qkHBl/JPbaDpWVJr/RHOWenelB7aL+VQCz2tVfrMsnDYtwHzMVBU3\r\nNMJ9ZLD++rzMlJqxfQu64bSvzOR5HmbG8n1F/0SMqoASwVbX/G/fLX7eMJfT\r\nSNuPXwayAEBNBr0VCZFV9SQ1j62JXWj6JcK1Ypq5PD2mXYa4twh3CyP3fmKu\r\nzTTantl86Zs5pLMrS8wqZ7ZXaeyN+cAx8bgRjMJ4ZqVxFf79QAN8PbR8+j0P\r\nneTKpGOcCbHoDVezNeLm0oq+5db92hFYOsspg2F99g1qUwZWG0PksNHea52v\r\noD0Vqm8TY2lyeUMhwl7dVmB/ZQnqG9E2Y22LOjkYH+qzZyrxQknwo1SZRbvB\r\nPxJdbV0Cw2k1jmozTSmdByu+m56S9s5vBWiW2eIIiZ7FXG97KV+RXqjOAk6j\r\nKTOVaC/RWYabmAhc33CfoSHspauQHdIMwC4UEVE2todx5mdWCO0e9bUSHBmk\r\nlGrIiKdA8oidL3sr5NRos+NpXQ8cxYRYY0A=\r\n=xwOa\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default", "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "testTimeout": 30000, "testEnvironment": "node"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "10445320c98c60299749fd201a70cab004a9afe0", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.10.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.27"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.27_1660898722833_0.8053371738918389", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.28": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.28", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.28", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "7c88d75aabea9c358ab103e81dd1203010289367", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.28.tgz", "fileCount": 21, "integrity": "sha512-tVstHtAy9GkqrGPesohDadUc7JoD+vX4Sydmrn8GVxalM/yJV+2Z4ROAALVTA8zGHMM6O4iMFTSWO7ZukumYMA==", "signatures": [{"sig": "MEUCIQDjATSRJL8JeQRCESAB0UWPeTxO0WZgpcfLQsodp7fBJAIgNz4PIJ0mmXmAVc3+oayMU5sLI/awAdZw6oJwYlQpMu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBfzmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYRA/+IXHehlV5UvhqIS8SJzNmkRqkNLN+TCg9rholhZD9YakMn2Bv\r\n3FbGTdixizAjzb3buq5J0ZW8PwULGZg0CyX6X/51Zp1hmKIcuugOlqwqwxXo\r\nkAkjOphGtmZYEJ6FGER1LGga1WszhcVETyIVyMARzx8+d8hEA1KJgd34e9zP\r\no3GAzHzKQ/sUHG14xXMCCKPn5E6Yq3cZ+t4hs74vZofhzMGZ1QwTZeFiqFwj\r\nm4D2kRUp6/8/WwxbgV9/wa8H0Rsd6cF0rC6NETd+gBu/mKijMjW54VZe7ZcW\r\nxlNAOyhHapE6cdpl+P6rYb8oaiKRugCuMoA4G3og8q56JQrSBjOKiU+4sMRQ\r\nXCwaedkFhwsoik2sSP35GrZepwHvkk2a0+ldIIIsBkHKh5sd9jroO98UuSJI\r\ndEM9oQGjxHz0dCR1NUAiRKi9USL9yhSraCmQ6y4k5S8jywbC56o3ndBurAmJ\r\ntRBPs+GtTiwhai3KscpOAutRqpmIgrzBezLJoHtqghUZhncBr2KsCMmDAU7a\r\nUMBIrlUeH3UeRpDjenX3N6fy8ImOqw1k9Qxz0QCTfwZPCqF7OFPDQO9zmVI8\r\nqhH7b4EPA/YKyjozZasF3wNF7iOrPOawePx0BS507xC2Ezy07Hst0EZy8stx\r\nldoEBxczYY7CEVGRZqFXE1tEHvJENbnSESg=\r\n=Obvf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "a16af3bd338793fcb6365de36d81506e1ee55357", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.11.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.28"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.28_1661336806000_0.07154603928726444", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.29": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.29", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.29", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "480d48f41dfadd0e028e550b4bdedf0386750309", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.29.tgz", "fileCount": 21, "integrity": "sha512-kpESS8KhcXJrJ+kZ9bZQVSZ+xVFBrSiIaMpYvhRK5eNWU3Xca9cIV02Br3WSGRwXeNuvYvz4Oeb8aRirIaz2+A==", "signatures": [{"sig": "MEUCIQCqbkz8HsQGiAbRJj1DWxORiSGoXUH8z6tdvfH7OlXS4gIgehLAdO3wqqKwAy5hi+On/STsWyuPBKgh/rpreyxQYZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBf69ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg3BAAnRFueEqM5MkxlMXKgXvC+GAJGpo8VMv7nSXcHAS/BXC0rw41\r\nikWiH5ibLch0++JGnQdf2f2eqj5ijv82EmWTdpzCSqh9ToZYCr7aCbUkRTST\r\n9o5nSdbSyeX88SAqP7o6XH2QcwZzCWsyt3Q5NnZfQIgzz2bfMDkr3VxxoVfQ\r\niWHZCIKzRr8OnqdHsGah38fBNatC8f8WIlefOfxkGkRF3931j/GG8FTSr9QT\r\nygkhS2UCWCLAqWfuOgExvwYHEqE/nycyyBck8hi4inEZctcZfbTrcl2HzO9j\r\nxUFsHeA7WBBci1XVxfnOilaBgFV+ZGDrY2I5O+A1PzauLZsHoDFu+v5ViJoH\r\ncu8JRgXlcYTBNmKf8E+VbK/DIZ0MzTcDgPJnk3Gwm7uTEg0AC0d/Ky3K7JY6\r\n1lbeeNUJzY/L1/GjnOaklAVIsOiE6dqPey2kAySWQhCtL3VJ40HbYW+8vOxu\r\ncrU0oI9XR8lQTseDRv1TnnEHIoLayKaMCvvAoRCWUCMUlS9Dbns4DOu2irXi\r\n3pV6S9I/bKll2OL1OpOxejcjQbNvKTY/hEbdEHuBVL7G6n9tiIwLkvhpTdIp\r\nm0CEiI/lTJewXWeTJiPEGa3amhJ+IgGvlwaZzvi8GInTqfQ1Vs8GhbEYyMK9\r\nWBgrLFYSTENd1edkSb+Aec3lTmjTsY0SufQ=\r\n=OkbR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "2723d86e97e7cf85145efa55fabc6f8447101e40", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.11.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.29"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.29_1661337277372_0.6803586843789309", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.30": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.30", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.30", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "1ecd0d36b5d380fc377e6af2cc6abd81fe684f9f", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.30.tgz", "fileCount": 21, "integrity": "sha512-rNbq27XSn/L4YV8ED8ewP4J8R/B2xb9qz4gFRQP46y4IwdBdD3pUP3LHi1vFLqxkvUIQYa74kHBcvXVU4xc5Cw==", "signatures": [{"sig": "MEYCIQDzvTzn9Fc5OUgfB3NC6+PjW9ZYoxkqPMJYgEOrkMPnAQIhAP5kdfd0Auqc57W582jsIrCx9EN4d7bQEvHHmAQiNmZp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBkv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCQRAAlmleiybyS+B0/aHkNl7HuX67LEzqs0ltEReZkXCPbUDCCGbu\r\n/CtooNnpb0x05xiiO05mRVIzcdd665brC0bVEu0tmI46mFZnLyWpqRdu4SBj\r\n1x5okLVyl2lMKJb78RqEWtqG4ahc9QnMK4CBcuod2ZLpe1Ga+Dwrf8Ltgdes\r\nB0hCDTLYbS6iSJrOqFE00IG3naDQFoqxzIrwkvOFiHIuMwt9Z5GvoRrfLJmx\r\nzlQpU/E2X7WGYaWFVmRnCqYYjOrCeeBv3SqUiISYs/fRV2w7UVShXJv4iEcd\r\nmNmshjEtxJKIuOaZD8XisKR7XhD/IXGZePC3E8eZbZvotO59XFhgA53RVUEs\r\nGHzfKlS3nw9XjhboE0OicKf5cyde1riQ0aEe/pHBIqSPxTkWEC5rRvP7WQvp\r\n4IfwzaDvV3N+842ijd3dFCAVWSCF81asw2aHkFk6jfpzLPml6D9yP3UKqUsp\r\nX9CnR66/0vV2z7YjcTV423sv1R8JRtbqHW0eqJzjZaRBBYocGdXM2xUNayRf\r\n2TAd1vr4QvDJZXDZYZEqVjI6Gr10wRmNXlO9XgehDZCeK+fdjFvC7mgQTkae\r\nJXKceJ2h7uLWY3iyGq9qojwlQZVAoaLO8uBeYvMUH3BezUWtXNv3mhp1yszS\r\nG+xEOscGNRIOtX28X8SCdhu5hAzOMLyPFoo=\r\n=4614\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "364d69f0001dbf6bc83b6a2d0124598182e7d332", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.11.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.30"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.30_1661357047471_0.1076154764267645", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.31": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.31", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.31", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "4deb95bf2b50cfc1bb2e7641ad2e165bde33e6c2", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.31.tgz", "fileCount": 21, "integrity": "sha512-D3<PERSON>W32u7LSTt0KbphGWx2F41jId7BY8H0Awr25PTRFWroqohfWo1C2huOh7/Yyn4qeyJOVEuxWeTzvbSkTyyTg==", "signatures": [{"sig": "MEQCIAfuzbwbI3AJqI3j5Kqs6MqNykX0vtii29L09W/ofW36AiBW99Uvw6tPn22kpiE7yNBpaWTmtpuYLv5bmNt2tIX4fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBky1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+bBAAo962yIY6LR+cA6z+pVTtWxr2rKFUG7952ftuw9bQNqCTR6kB\r\n9MW9GHZJyl/iMbP0IuXmX8Pz8LnPG5HApkgVYA7CNZ5UHWHVLTZAOUqqFTVG\r\n1mUSa/P05XnbuFvKYbIGGVSltEn++AG9Rb5XGGLduD98A/56qxPgT2GgDy9i\r\nAbxiiiiwKH5kayPGC9OHYYePBzjUwP3KtlXoAni3nIH3NOV81KKCxzV0ovY/\r\nafkDgqcR4nwJmV4CllwzgP4Oy4Q7mMvyKgm2U4x7m4C54rKKWQtmYo6XClbS\r\nv3AO2RJCxrRi8HCSlRCbXKoSUUWadQWSI13rhB68d2brasuVkJkIIJIHiWvw\r\n807zdM03bTT0mqzGRW1zoNPUfxs+uAyNs6SdmT7ICQD776LggPKqcuMYUxKP\r\nn9vQcfTz5YF7cE32Jj5z7IDN8466xVrauRWlOxnxKW9cfYOXpj5juPE/459L\r\norEb4aycdSc2Qc5YN1J4qIpZt5aO8Jze5X8jzQSAaiKBT+aQniy833zMuiSs\r\nfK+j2MaHRcXnh0fshpXk4MhjPjV1Y2PZ9YJnQDvg8AaBwQ3SD7Awp+nO95M6\r\nlbRqVZAZqhEZHUXx9tH0HBGUq85uhJSPJgMFZipT5GxgeEfU/U0sC2MYpDtV\r\niHAYjhxj5GOjdlMjunEDgAC5ck+UZJfxL9Q=\r\n=JP8Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "68c4c0ba7d6a8800c5aa0df1675a22d0404066da", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "lerna/1.11.0/node@v16.16.0+arm64 (darwin)", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.31"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.31_1661357237743_0.12813077567478182", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.32": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.32", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.32", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "1bc9c77a88343478d50009f30813b9fbf8a0f4ad", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.32.tgz", "fileCount": 21, "integrity": "sha512-G0SH80am2XjlK6oFI3RoKlg1SBS5ZAeqakYasfNhJEXqM7g7tsoh5jURMQcNxpLvo48XBTgHgAVEMzhAGgDPZg==", "signatures": [{"sig": "MEUCIFGcqs+cktP6os7isl5YiNX/4qEP2eWs8G0yzgHVyAKQAiEA/ay4cWfKsCDYkUQCILD0ZTVUVGW8cuekwx2EHdoljNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2YIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTTQ//WLDEv4KJ7V+oIF7QVWq9pVAoHqSpjLDy0MBvHW9sqjfCUY1g\r\nrW5O1G4DX7rXPcmokb0fH0Vd69pz7D0NxvqppmIEVbi8kvrgN4BXeDy1mZf+\r\nbghUaqerIcqpBHXRNctYO5dfNLNFKdHKxie/J06ceb51MX/f+jyo/GbsPt6j\r\nam0zMHNemYwq6Pa7RRH86MbuJcYfEDOFJ32JKZYnWGjUHcsCt974yIyFqJWZ\r\nsFXuanpkccn/MvAOV5uDSDHzasg7tfxyON5RWco8vuxaxlAf321ZSVTQ5B0F\r\nfq06nExZahWPpgpa1pEN4Sb/iuvnwKsgO0AD3zxKfJzSNDuINrQesBTioV5W\r\nbRWWZrUiCDx5pgtULYyNCmcZ8BKCOyr0zU+elfFKiok46M2/zS/ZoRcKN3iq\r\nOK3m1QumyqEVJBm7sojw97IM4bpHgP69PDivPyOcnAqr6xzVirORIgTi4KYN\r\nEmWnwyHAXYxtYFlX0VnNECGsZkk6Pqo4/NJSCzxGyYzz/4NyPeJj9puVBNfU\r\nEeqRyKN2SjMaeGeA3qb2+J+xWunRRJAgn1KYskDmeGvKKnh9b5RZUtMy63fM\r\nlWW8geA6kRvnZRpMPXPuVTnp/s2zMgrTQtvMiFGSAfvFG9A5zlSZlEWzfZ4B\r\nIf5kojwSmvWjWgXQi4UaiLX1kbMb6uhO9FQ=\r\n=yPLU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.32.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.32"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.32_1661429256418_0.7806958097964449", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.33": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.33", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.33", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "bd5591930c5ce64dcea7b74fa0182b8b5b17c638", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.33.tgz", "fileCount": 21, "integrity": "sha512-Aifd/elNDeI01oEzUnCF5URPtMgBIVDhnuy/F6SgS2OMJvzts/U5Rl2hxYliViU2OpC8ZkM/XT/t+Q7rQPJsgw==", "signatures": [{"sig": "MEUCIQDhuHE3nduIE+MPK2NW4t4lZ/c8A/7m+8DdDAqhl64rcwIgF00pn5u7tDC6YzJF9TqPIXzPvbWZ8alPp2fXpMZAktY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLCFkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6LQ//dzG35hseIcZ5o45ezR2Px1n1WxSMHQL9LwXnngYdX3J+2m40\r\nPt0gfud09tXULcrGi4AHRNb1pJYt1pBoy+bh7JQolhsicxUakVto4qEtwgHi\r\npWf/+/WNJqGPMI98JgG/yT2942YtP8luAEnK9OII+FGfcjMTaQBZR8osQGkt\r\n2OSNSiym3KlGdqrRZxzN4+rOEJHLP3Qt/VANEEuAWcpR7cVUFjzrsOmNvifr\r\n+/e4rRpNKP/kWzWoLKiukta9W4tKKwOJ51DUuRQeH4VwLkZ5uVLCzt4xkenq\r\n/0pw1yzWcON0rULVnVL9SDK3WBZU2YoYvc9KoMELMHk7Im+gQm1g0ZRbtG+h\r\nnmnzd4+JicpYSmhw5fXmT03JPT/oOTvQYMIn6BztO4vQoPNI+bvQu6i4GvtF\r\nY6C/GGM5sr+fLZrSOgG0iY3OBS8fULn7XIR9bYqIx4KGLEQKAwp8jdeaGR4W\r\nURw9cEJXsHBPq4q9f+rpDexQs6G7lHJ0Gl1VTy09sbQffMAx6lUQAKC3yZSH\r\nZWOPA0F5ZOZ+Ku7ma9gj2MruPvuxYzKveG/XZNdUocbI46S6umF/77ptwHYP\r\n2IvpoQxEOkjGbpV7xOHsFwM+OW54Uwv+b41/StrRuUrrHZXhCnyv8pBI/0QN\r\nqopSzWZ7ktoiqLKAlBPVr2iqIqoFRMTefaY=\r\n=68QD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.33.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.34"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.33_1663836516022_0.3945158934628594", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.34": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.34", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.34", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "e672e3567d35ba4f1676c3be998ed3ef51b67a31", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.34.tgz", "fileCount": 21, "integrity": "sha512-1rLtF7pQTnBCkz+1vYsUXTeecJ+PJSqZDRlCUPZbw2C4E0qZzn6P9Zt0ed+lhofosPnMNiagXoVZIuEWZAkZaA==", "signatures": [{"sig": "MEQCICKmzNGJL6RVI5nRK0WgALsmHEimegpOrm0qBKQLr+JcAiA0tzviPv8RjeS+Y7R0Y2MbGb+EuS7LtemjdvV5kLLSbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLHvsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHCA/+IK8KMKR5tCk2h/rTFFB7r4rIDY9rq9r9G1d1K60xECuYKbx/\r\nlznChER7hPL6DLTEHv7cgLYEZ7CGoWZXm1zHcPEKgVx+tP1G+yCnHlz/7vJP\r\nuM0uNzgEx4Lhz7nVjNaRW29JkuDD+/EIldS5Q0eVYVAfJgrIDVOgIJqyYgnP\r\n0xrLrCY3hbaqYjjo1V0xL7+SxyouaE6eqPAjmGewIk4BUqxuVf16z6F3KfT6\r\nCph9rd9FipF0t0h14GfHFunYsuziXSpk3wCA90P04wvv5u693fzLoK70yiDn\r\n4nSj8hPr+gUTKSeMAgeuGbEN2+mbAfl4QpHEL57nlKNtprRxZZcniVv387iu\r\npCLJg3s7/pEQZjEIgdEnt0wdeISlWYukdf24+aDpJEgPiLCFQUyRRWYtigI2\r\ny0TWreEthALBN0mkpg9lN4F1Inhh42VKQAJYG1a/jhBU6I3EXzcSquHa4NrV\r\n3BuQvkPy1+UE/dsbPiJh5ROo8Z/x4ABrDEG2PAEG24v6n4KTTTE1W4RCFWlK\r\nrDntpLtqG0u+tj6XICdFqKWjwca2SXg/m9aXNeOnlb4KX/3Z17dNdSqrcfKn\r\nHt9MCz/YCosQshzSxVOWyFloFlWMtGf0cwdw8htidE+ABEGkOwNvGPshlei4\r\nP4RxjKRU9YUu7OWzLlkEjxFlvyB1Tp7Le0Q=\r\n=/uA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.34.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.34"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.34_1663859692387_0.8283106902692188", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.35": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.35", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.35", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "12c919180f294882b6d305360f530b6c319cb656", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.35.tgz", "fileCount": 21, "integrity": "sha512-1LKPWHtVsYyY2AAdHb08fk4LKRqHsenZbIBOZYlZnPSXdos1NEhQYEc9YlALyo1ogREkRWJO+ExLDxZrCzO03A==", "signatures": [{"sig": "MEYCIQCc1khh/adI3/7GWiLeqvUPoAysUMYzhtibi3VaeDjdLgIhAJ9gdYsAOqpxKfbWm45mCH8r0dkOOQHQkDHphfohO+N7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPCRwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvDw/+MzLjqwuPHu3WCZAgMJ2bxbifPqZ7e9KqV5/v9kzUrLpl2cRA\r\nQgtKWJBmNIyUVWAd0DaqsfY+nI1AdE71EPe7M5o1l7UpPSkNfFY0pxSC3Mai\r\ngfUbtuxap3tg3lcnseo9DTZjWCP6X2YSx8f8oGKnDax4tXPXbTvX6iqafLtQ\r\n6V48E8zgbGqd+wihF2ilkOh8VThZ4wsUChvpVSAYyICvfugqPppFfvkSSt42\r\nSFE4L4+dAhZrYjm1QAQPEV7bwUkWqwkLjDKz5vHuQe4tU06BgMG4U3jVjoUs\r\nZ9l24+3MF7G7YoU19fYj2/MxSDjPoUfhdCCmVEZcQaxaae62el9ZaQJcIzZ5\r\ng0CrFFe9+RPqMdTV1DKCxvrGcCeGx0TM1oNpY2XEEB2rrnE3Eag7z8yyGuUB\r\nUMAFDPpgPedOGXAEI94qaC2zBJnVT3j7T5gfUcekQJNmEWdL2ySS1kRHBlm8\r\nRxQen5ogozbhsfMCkrxRJoEZdIwFQsX7VMhutruGj87A3VSbvLpJuknK/M1o\r\nlTWlH+V/KuDRvXgAwbu9GEqqRIur+MlKsqbOUJut2ea9Y6LhU2gNGTAqlUEr\r\nitg7+TxVttI8NLKAt9BHv4Yjyk0AWc0ad1KxiLomryF+ZAApkJCDUJcFu9t6\r\nIyhMY6RuodRksAGSh3j2kueQqRe+kFiVU68=\r\n=KNGU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.35.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"@edge-runtime/primitives": "^1.1.0-beta.36"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.35_1664885872601_0.5873487754394731", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.36": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.36", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.36", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "c5bd6d3823ec252f15fb97c5f65e94084100800c", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.36.tgz", "fileCount": 21, "integrity": "sha512-uPZmL7X+lKBFJsTg8nC0qPDBx4JGgpRqlgJi2s77g2NOtqitQOI90BfIKHZSSoMQEwTqfvAkpu2ui8nazOwHxA==", "signatures": [{"sig": "MEYCIQD4HrWsdgWMywvh3C9+lBI0zBPEsg14AFKpDxXVjbZxIQIhANyCiFePmQHDIxFmNEcSwg7cndpD0TIDJbGlKW+HUdm5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPZn/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfvQ/9E5QO6Rfi3LPA/NM1My6theQTg/i8LkmN+GY+5fCQZkyw114g\r\njLuNV4aTvJWnJ5buP03TdV+JBKc/oSj4wH+B02+B3EXEgpIxLuD+SrItm41R\r\nuve17suaManKoYYdmej/oUN/a2t3XXxo43PolH6khAb1oH8XzwBiq3vQQcpG\r\nXLjuWqFoObkSmowvYtQopMAvdJcMjdtYfDAV8nFL/V9oiEYfcWDHKfKvJ3Ro\r\nbvtPeZ495WC3aNeg9AMUpbm1Ft+E6jhP4ud0QeSvEWfXldeHh3klPgffYxDa\r\nzhHcTHdu6ojB0dL6hOjTvLYk4mlL1W9RUzY4wT7a4MI+RZL8crqT5eOcUZBt\r\nbHV63+YwVem+Jb+ng5VJrbTFaNX/sHUtNZvQ4tXnEv9I446yk3tIMYBeLzjM\r\nBOqeWNaD+Xs54SVEUd9s9iTF0dbE4zYymcnnpfLQsnyX3nvGgA83y5M5ys7k\r\nhaspfJNZYmPu5JbsfZ0gCHqp3Nun0eYLVEY1/pB/40DWHJNMKGOlc//zOOq3\r\nGNeq7eti5Hx4uzpdgq/HG+mg4M123n7O10uiLl428OamZAScYqEzs+xj8MgP\r\nPBBQ3BIPD3Vvsrusc2McIczXEJ9U4HNTcVKl7RAZlS5LHsOK06L7+/qoVwwt\r\nKMt374VOunOItSExzZbxnGQix8ee98m2gpc=\r\n=KT34\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.36.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"@edge-runtime/primitives": "1.1.0-beta.36"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.36_1664981503774_0.27438287252034765", "host": "s3://npm-registry-packages"}}, "1.1.0-beta.37": {"name": "@edge-runtime/vm", "version": "1.1.0-beta.37", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0-beta.37", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "60edbb46d6d7c500a3fd38361c1198a876899458", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0-beta.37.tgz", "fileCount": 21, "integrity": "sha512-1OrFifIxDFpwb1VyrFD8kwWVppc6D1uN7xh5jQzW7jHaVJ0Dw4C2nSAbLe6YKPYj67o09vW72WLzfUcryeD0Pg==", "signatures": [{"sig": "MEUCICfOMutKwKwYZ3UzUuxDQuLhM5IED9u2Jg1aN8eMYJiuAiEAnUBfhuh5MVZSAMzC+70d66nN6CwDOA4an70igaMEvpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTmDEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYzw/8DLISuumpQIn7lTWUhRtVDEvVKJPrVmwI7agD60WBJSWFNDQ9\r\nhK+0pk6lOmyARtlTzNRTukKaXmKFnk8ycGc4v23a/I9gtVf4LltBaMFoHF8q\r\naLPjUnCknrGYktt2KphTkQBpFasw/RNGwWx48j1dk8Y+YkGpWPrrakyaz0FB\r\nh0pVLzqmq/0nBGYcmG1rvSKfgttZoVleYDKsKDta0hfNsm4yVk+nM1WhNdRW\r\nRcC/90f1bG/W0TxB43I6DCkmw1qtnfxhueOA3An7vgcrMZqZBCSBy6Vap+LA\r\nUU44hxB3bD/eFx4c/6qZFsYPgNvrDwzRb/etuA0O9oVmem2ibPM7Ts66zxVG\r\ndtEiQRMEVmPjZ0XT3TpmiZrhYoT9AjQ8mKFu0bjhK7WfdkHWhPj/gshX40iL\r\nWJXr1PFgrDfv1qjvsX1DbaNBjuKCeWWUwWVjjxpYS/a3EphW0krZvU/cADVW\r\nYxAuBjlfvpUWD0hZiFe7WEnsoYWAfOE/Ht/T9UyEFRAd/CYNo4D6KYsk5jJP\r\nAHX42j4KHooTmPD485921xaKTPa2GyBU2QpuOKuVknLrFX0CNPrS6s+YxRka\r\n12W3pBZ7O2bK8+g9ZNGd9H9SCB2wPT1fO1O6E0w0FsJCc4oTnxFnccBnNBvN\r\nYgQp8MBGeU/Ui6kyvE5Hgo7ekKrWpoYEN44=\r\n=5sQd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0-beta.37.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"@edge-runtime/primitives": "1.1.0-beta.37"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0-beta.37_1666080964729_0.9235444091074303", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@edge-runtime/vm", "version": "1.1.0", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@1.1.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "e2a58ab4c244fa3b157c7de80a5bcdd1259dc147", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-1.1.0.tgz", "fileCount": 21, "integrity": "sha512-a3PSCdznoop5+ifkNDaSINB9V+Anwh+wpoaASIWhq9PLQuBF9D6Yxe/mLRZkuZRkOJ2ZmaTzMGDI5ROUChTL7g==", "signatures": [{"sig": "MEUCIQCzmXIrBpIQoeRgmpuQxMUMgrqfnevO731DGJ0FkDsx9AIgDx6/wrKQ7aLNToE5SU1nUKjEc5LOKJbYz3Tc1tB2mNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVqkBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8VBAAjrJhqCOdXH+9KYtqML+Qu37lzf3IfOfPl8tepIqBe9SBlZan\r\nt69c65OkYd+mcj4lGSNyisamqV2fwrOhQVF8wIC7jB8lAPodNoYLDYN0G6G1\r\nenD4we5pKYuHZLzMSnp/gkPiIxxkpZu7qgsJ3sTnyxWcASzMB6UbBqbEF6K3\r\nfL/gqy/JJhrw6copgX03r5tIUqT4oPPvzGE1EWwprdZucMI1IlaK3Q6NLgIG\r\n4lHd4sOf6I7TUotVGgddqiFPGGVruOS5P17UOMmdyDUCUqHhCrZRDPj8CVNZ\r\nNXAmbFNKuVM52rjyU3XvCupM3K3jGGWW49gJmxWZKIzlxACsS/YjappF6eax\r\nkcjw7RjH7DLROBFOLxBP90waGnh9qYR2AoZlueUGOwDaPoN/72TyXsoW3Z9o\r\n2L9fzm2aArxD4KZgsIrtFTBvmanOu/MIHRzl1M4/qtD9QGKOvR9UkUploABy\r\nUykXbj3CpDaca9l90Yvf7+TRO9otT0MGM612o8LqKrSKcslK2/usCbqMJeXL\r\n2CeoLaAslXBqC7N1eoEygfJptqhjNAwxI6/XVh5geVJAoSzc7JRDH7XevP75\r\ngDmAqIAS41tHrHLVZNkOePOwiLYhLV7+68r5ZO4o+hNQqQJySsD5OSVYHeQ0\r\nIX/ickSh7yxLze0QyKWUrnB1KyX8JcLBrA4=\r\n=fmZD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-1.1.0.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "6.14.17", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"@edge-runtime/primitives": "1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_1.1.0_1666623745651_0.8641095150842051", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@edge-runtime/vm", "version": "2.0.0", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "9170d2d03761eff4e27687888c4b2d9af1f94c7d", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.0.tgz", "fileCount": 21, "integrity": "sha512-BOLrAX8IWHRXu1siZocwLguKJPEUv7cr+rG8tI4hvHgMdIsBWHJlLeB8EjuUVnIURFrUiM49lVKn8DRrECmngw==", "signatures": [{"sig": "MEYCIQD3X7Rp0t8czLrsK4SgqmAaJDvOHyH1mM5o0IBttj05YgIhAJ4gsBLtRwmdnOXuuKcuqsDcZPnTTkhp6p2MWA0hptM6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWqniACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGsA//QftQ15EbSNw/ARj9j2/dUAsrYCBdoxXPRlKZzWxbvoJPcRtV\r\nWMM/ikuyE1QrKbWo8jUkjT56QUy6BHfn7PFEUEB5+imx6g0mhyA2dKsi37n0\r\nFEAOXRIEwU33N1gBAqXYcQS7zx+vhy/eLFSl3ApVAty21hq/+HHifBKfh+ud\r\nfuOgQCJtSHKAp3UKj/aEYK4aLeQSOm7TNPr5XRy7t39c0bLvq+CKPUcX98XF\r\nWyy2vrBzyEYS8+UaYVDtC6x9bP6RFDTP8cfjBpzPJ6TJeqO+bGQMiKrwcI2i\r\nBVfS1O9nqXoQHq7zFasoVaGe2fQ0Wl7oed7tKU0EnfKMSCeh5m/9tjx/BFTP\r\n6j33bB4BOa5Q3VvmtkNio6cDeS67iQk4kqbXTXLh5UzADOd4PT/cyD5expkJ\r\nMaf+K2RSVg4Bj5yiqGzOxrKHtMWfVlCFNHKjd5ajWvGiBVBsMt9/1lSrMEf7\r\nxcRCp6YzP0dK5GvoJ1DejcAgmGPcJxGBYSP7mjBEBo6ZGNhHF/oGP8kOZRrS\r\nnyJjt6lRF9AzLwzrwjqUtn/gn+ViONYdqgIlmeyCiBZvxqo3LAXDwvCnK5Dx\r\n1s3ocfPFrNGduawqXGLjzE2exjLVuIDV0iKyTf1U1i+IhQW8Wqc8FNsGoo2U\r\nKO1x2/ttumFn56+ZO19pxFpUFgD2LEZm8eA=\r\n=qnGz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.0.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/0a8b92c6a39c9df9f62fe12fd4c026dd/edge-runtime-vm-2.0.0.tgz", "_integrity": "sha512-BOLrAX8IWHRXu1siZocwLguKJPEUv7cr+rG8tI4hvHgMdIsBWHJlLeB8EjuUVnIURFrUiM49lVKn8DRrECmngw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.2", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"@edge-runtime/primitives": "2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.0_1666886113903_0.9383267272681228", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@edge-runtime/vm", "version": "2.0.1", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "a006ebf4b4362458f7ef0e87793d827a6b126ebd", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.1.tgz", "fileCount": 21, "integrity": "sha512-+5UtwEL08QidClBAPc+7NBrbKvF/Kkz0jPSOf6rsMlDs5Ac7F3k9ZwBknf81nfCQvo0gkuVtDjOPbcLR5qvYeg==", "signatures": [{"sig": "MEYCIQDY9Dog1/My+NYDEZElpB2KL2Oo2NzPvLb+nCGXLLbHdwIhAPUcSX7G2Tf6dJR2N5kymUHbcoxjbZCTHE4efV4+Iqp7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYlhtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoG9w/+Nh4ond1zpxIyy3jncn7Q8d1gKL+rW1XZqHsjgTPv2kqTySGE\r\n6JwzpjCceXLu1++0z+b3lrQDkPHD8I2E5VUov4HK8rX1qKnIBapXxOMf6JhI\r\nNyb9ii6Gvr9wkCmqwecR2gdV684z6048KLJ/f1WJHOWk6J2gQ2vNbJoXWbw4\r\nOoq1KgnLUJS2VL2YhI6E+C4KsxRRlJANK92BPLsl/WTWIKYkK1/RFaY3gsoc\r\nsGKILibPdpJ+suNITjJB1LqB/n9DUWZ+5S/z7FAmdmakp/RI5WY5CeQ6r/bK\r\nt93nN0bzllo6a/qMZSFbGHRgx1v2oZl25kDGrvfJPCOUsiva9uDUmL/CHTFy\r\ndk+FZCPjfizVia+EuRc4YjsxkE5xW7CS/x5e76GKVLZ9MXofTaFgK4Ha2ZuL\r\naAYxQcMPiyOvuzmakvXy1r5WZ9uYxkgrEfgwenptBjYxoyRZyq9b1vzFE7Gf\r\nSfT2thw1oJ0dz6hmWGep0gugzaWqw2shRl88iKYPk5t46OGs+f0JBgfU6kHX\r\n3m0xMRxp1f2GkD6p52yySgsXMu7NUY6YzRF1MuUTG/g1MHItTAAtFPKnYNg1\r\nNGyXiDxcxRcepcchdQMocCZqF0g+VQBe30bIlRLNJDdWJ0od0x2udICJ+z5w\r\nKUSWBQTU5ZCYeW+BLgftd8DARujkNvZk3DI=\r\n=FmSd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.1.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/8f3fa0e33dbb0778c74d038dab22ca30/edge-runtime-vm-2.0.1.tgz", "_integrity": "sha512-+5UtwEL08QidClBAPc+7NBrbKvF/Kkz0jPSOf6rsMlDs5Ac7F3k9ZwBknf81nfCQvo0gkuVtDjOPbcLR5qvYeg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.2", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"@edge-runtime/primitives": "2.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.1_1667389549393_0.08066363712705837", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@edge-runtime/vm", "version": "2.0.2", "keywords": ["context", "edge", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "57a5592657b17a8b8d735b9314def842108a4c42", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.2.tgz", "fileCount": 21, "integrity": "sha512-yQFE4H5R7jy98xRMnH3dOt3PQkORVtKTT2gn0U86JBHWMvoSZxvd1hw8UXxJtyp+bqW3yV7N1oTwDdX71It+Gg==", "signatures": [{"sig": "MEQCIDq568XfOjqLqllA1xB/PM4HxdChOKjEUm5dNjXHTaroAiAqCndhEeihLRlXuKew/+pNpv5yPlkGDVuH3JZAnvcoWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjapOaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEWw//eJfAY47rhtWBqBtEDh3kqLMbvz/0Lw+7ggdDOpJywVIX5HOC\r\nu06x+s+uISewKMt2T5a7CDlmnYx9AZvH4mJ4IRcxy/cSjbsy30IAse9jdff7\r\nTZHL0pZVNSLwQGi1YEh5RXSkqtpD3j6QzBroM3e2lVnWhYDRMAp0BdyGAMa4\r\nRnUJiMwD5nroOS8eSzfId3ba1eWv+qBO/P4/3cJHAkJpdZObaDFaKvuVlCmE\r\nVHM56maWurddUrAEU80D+djJAmvcqYkuvrLI9Ik1moxOCiwQX4cy39ffqD2+\r\n+9PxJg6kyXdReDAHb5X2JmqFkzHi8BDDZUt8JYYuQlySLTS8g4ut+B35QueH\r\nYi1c0vvTkyYGCFXxYA2GPDXgke9TUPHjj7PLUrw39+gwE7vxf2PHNiapOfL9\r\nkW/vs32r/1lCa0NWLZGklRbwnufvsFRn1SnA/62xKHNOU8G/2AHtbyKl1uga\r\n3G72/+jrhD9PbVJRG0J6Qk8LPzi1IfItum+SIn5dTUBpgwsnO5uneOq2ZBps\r\ng7nF5dwzGIH9Nfnh9M08sWw+HnmY7NJP/QgO5mbLftmqaOvISWOBttzsil2w\r\nQKsT29LvdakjWiElG4QjF/MIxDdEQrYCbExFaUPPpeKVJXD719ZywHmZPZNb\r\nxwoQirQMc8Eyc10HdXYY2uLPdNM/bHrAYR8=\r\n=lqSF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.2.tgz", "types": "dist/index.d.ts", "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/351bbd099a4ef74397f88503c46971f4/edge-runtime-vm-2.0.2.tgz", "_integrity": "sha512-yQFE4H5R7jy98xRMnH3dOt3PQkORVtKTT2gn0U86JBHWMvoSZxvd1hw8UXxJtyp+bqW3yV7N1oTwDdX71It+Gg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.2", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"@edge-runtime/primitives": "2.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.2_1667928986094_0.573707485417631", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@edge-runtime/vm", "version": "2.0.3", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "300ff81381c7b7ee0fff29d329726975af9e8473", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.3.tgz", "fileCount": 21, "integrity": "sha512-l0fTtkBIPozgr8vQ46LXCSrp40WBWSNFQy/wGpydAsXznm6So90/ZsZD9hFLiudT2dnPJAWFtdNHcTpEKv8smA==", "signatures": [{"sig": "MEUCICffo/UlhtOrxS+DRwYhC0pWLZOEK71vgkgw5Iv++xF5AiEAglh7U+vMe1MLBIN77w2sOdDUkDpNIM22hZ74Gy6DiA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyC/EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrusQ/+I7eJg9ry+z3GPFaKfvLvKQTJTmtGTa2Trew6cwgjB6ds1JYg\r\n8VIs+ubK35D4fxsOMlbrslUtNQngIrExTL1kvXWWjh1ppWG1eqVDHtjylDBq\r\nKfnnFPZSBS686undTY52HOjwy6exyud1uvHdTIIjsp8Wdko2ZtviUZ70NYho\r\nnZQTrzduygGCLnAcP8kievOklqPbmfxFfB6jLK0bb4twDOWms2UEaTISofiB\r\n0xNZka1d/wfIQdcZRPdpi2hnE0p6sx2f9w9Rg4i3lICmpdKFbn1eXyyp44g+\r\nkOit+08XD3czdvEuzNMYYjRcsu6BNCfBdtR8nS4W2V7YzXHcJpiTYwmyd6t0\r\nIyvgHrHiTfjshlHz+u3FunWvK+8g5JgWcJGnJdJ7MBWypedAWufOjMVr1Y5m\r\nElzbhgZv5xAoH3n3WQB14V/7N5Xpv0lvZ+FAwsJMcCSn1O8GmlB0frt9zpEg\r\nmEw/LxpZ0jpjZel3zsU0HAv64v/lNz4puD8Biw/eiVXKTZL7oNVLwGddtfYU\r\n9Gv8LDq2jG2tD1Z+8RW5AKNQN0aEhG0/VLliKxJKgGtrbNxFNyz8Jz3tnhCL\r\npMJvggiSCPNKw2berq932aSpMenu2NaeE5D5p/r1M+pINBrjxuU9hcTX/g34\r\nh3UzQzauPdcdgMwrM40QIM3gBZapzhji+oA=\r\n=d668\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.3.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/bf0a2946fa92f8dedc8827176b6cc9a8/edge-runtime-vm-2.0.3.tgz", "_integrity": "sha512-l0fTtkBIPozgr8vQ46LXCSrp40WBWSNFQy/wGpydAsXznm6So90/ZsZD9hFLiudT2dnPJAWFtdNHcTpEKv8smA==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.3", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@edge-runtime/primitives": "2.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.3_1674063812417_0.973788666471985", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@edge-runtime/vm", "version": "2.0.4", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "e61c5290223a527f54f503e925728e6be1650f10", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.4.tgz", "fileCount": 21, "integrity": "sha512-kA2gotpWS8tsXbEjO3rEGjPk4EtAbVWnjimMtFpvuUF4SBS5t+GtnkjsT/pjdcHq5b0PYAHit3zybqP0PhaXQg==", "signatures": [{"sig": "MEUCIHsdoZqyWKBLLcojVk6Nydtov6eeQArMCSmaU0KMMsQeAiEA2FFeaCKoWro1Tvia7UAl3GEH2J1zpJuG6wBh1pztgg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4kzPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZWA/+PWz2PjDg/BExdkQ7VevRYnAB1AyfQOJegbnSz6kpyQjX32Mo\r\ncox0b/czyPC4dTeQzbjaGMGy3bCBIHGf2cvn7/fldZZZm2+riCCMrxIgsUmN\r\ndlBnTyHcLRqJjhG5yVBuIkHV36D1O6++wQn+uMG3apK2jdDdAJpjblZIGbA6\r\n+dUX/ssVaqZAFd2/+LgbTqpWe9S4XYkq9bWlWSbv3IBCBs/ufaAcHQxNnLxc\r\nIINlfLfv49lRfp1IFGV4FlzRA4EdqebFqYwePGIdbcWgOyZD9kvdSB1O6OHO\r\njd2rAgu77mSNqUxsvtkj26phO3+5/2NJ8rvgX0o/bjm1GwOHA13gwxur5N5e\r\nNr2wZlJH/9baSvT0n4w/tKWy5Pq7gq9RkEwDIG8tQMZi/gg+sOQtkk7mU0wd\r\niwOisyhF/2KnNH09R3CcH2KevtRibBvGRu9ox+eihhmUr3V14EcNFBVkMaDq\r\nedUNU5adacpsm9qykv/KKdUM338pU6JvsLKumfCAQHOE4F6MaZIxulfVnL3Y\r\ncLAsh42o/keqBpVvuhpf3j9AnNTkZUkEjB6E50ZazjYNw0lua7jEIqWUXW2V\r\nk08FsJLtXMGdE3zWKG2RbJsZG+ZqtDakGdAJZrcUjDCMxuY8fguwICGhS9R7\r\n/yz6VAfyb9QGMiQrUtq+Zvlve+onphAUzXE=\r\n=eR0l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.4.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/58b2324496895e7cfa929bc6e485588a/edge-runtime-vm-2.0.4.tgz", "_integrity": "sha512-kA2gotpWS8tsXbEjO3rEGjPk4EtAbVWnjimMtFpvuUF4SBS5t+GtnkjsT/pjdcHq5b0PYAHit3zybqP0PhaXQg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.3", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@edge-runtime/primitives": "2.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.4_1675775183602_0.8470254958473595", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@edge-runtime/vm", "version": "2.0.5", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.0.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "7ce86e8cc14df096f99b00a74be84dd6cb5b0365", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.0.5.tgz", "fileCount": 21, "integrity": "sha512-6orUuYOUTWxVPoBjhQ7bQxqvkTICUFvlhumcsULb3FfBPQ6RRb9tjtTX6u06KNCPtvX0L4Fc5OicjEUZvvz8Nw==", "signatures": [{"sig": "MEUCIHkOpZsqi65B6uxeBkZMHwLDMAtjgKveMAeNLk/FrQQjAiEA0Q0eQ/F0SNbOnHJMnYPPGNVXZV5LA9GxCp99T6VcyGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj47YpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxFg//XxkUl5u4JQNum3ArJi7ozN0dDJJpWgHBPooKkAKAwNdJ8r7R\r\nePluPmokiUb7W9XLNE3plwfYM7JDQl0F7fNH7t/KOw/rlR9s/O7u3n6aqCV5\r\nI2QAIaaexduomA/MCVIcsMj+ODC3HGy7e3Mz30dHT6Eclj/uEbRWegf5T0gk\r\n+PRsHtAu4xA5WlMySE+u+Eg6wVSbvHfqAhLxX70rJp4pTSpNBZ1WMOSpXQ1U\r\n28EYsSxo4wYEPW/KGi2l1q1PL44ta/5LrSaZkD5zKjuRkS3kPzqy9piiOOpv\r\nEsLIjmoFq/fSaXrH5HtzQMExDvcPgQUJWYbjEipH4y2/DjgN6MgLLhWEI5Xx\r\n5IQirHzRruMxdhG7ZSSEEK1IKzh6QeRnLVHeDgnL6q//xLSWLjZA2Sp/JfRU\r\noTz6swQ/c1pN21UmBkI8QuZVrdGPC3hQAQouoGbgjOqhxWTC8gpayoXNcFOi\r\nm4A+KZkYaXadlNFnyiWXSbpzZc+T0ZQ80TJUrPNDPzpUmFPLXDpHR8PXvqvC\r\nPMIrnoaw83KsIzTcPAiVSzgJaLQteZer/FRuNehswoYhY8eSQ5p5Mrgum2Ig\r\najjJ0682yb2PaN0qp2v2o/e2uRojzFwNriGCr0KnLKosNC3c77904STdtLxd\r\nWeg5l/zrtFhBPoWuda8gfKei6V1/qqh7/cA=\r\n=W6kV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.0.5.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/7de9c1a2004c243cd8e3cb525692d149/edge-runtime-vm-2.0.5.tgz", "_integrity": "sha512-6orUuYOUTWxVPoBjhQ7bQxqvkTICUFvlhumcsULb3FfBPQ6RRb9tjtTX6u06KNCPtvX0L4Fc5OicjEUZvvz8Nw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "8.19.3", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"@edge-runtime/primitives": "2.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.0.5_1675867689004_0.6467296804688165", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@edge-runtime/vm", "version": "2.1.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.1.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "be35a6e89c148d72dad33477dd53ecfd035b185d", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.1.0.tgz", "fileCount": 21, "integrity": "sha512-ruYuUV0GPZ4d5JrUvhWOk8bfVhfcmIPM0f+uppI1VLQ++w7LvyRp4KMIm2ELOIak7OcmM7rP3G8D1YWWtQ1RKQ==", "signatures": [{"sig": "MEYCIQDpqBZ+9LKzexwCOtZJSEm5SqIxCPm+AoKTwQ0wOahB4AIhAPDdoIP0TC5iS2/KOa91mBKmZXfMZ8V6nRDmb5ROTH2c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/hKUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJdA/9HxjsX1UccsMFKKPKK4BOjznFMpYr2T88YSfTeCpgcT3xnKBR\r\n/BdZUbM6WAvouJJQXMNA81RFw1entRRzDQ/Wz1R1cqdRSLIem0GlO43iW3Qa\r\nNEnJpAt2028GFdC3z70ltJKpYbWiTpd/vxWz803RRFUEi4zrpk1azIV2jD8/\r\nRfm+A0FDNjXWZnxxpFfttsdHg6RUCD9ndD43l7eZEVQ8Qr8vtftmDRv81jcN\r\ngcdzsWGEEu72bz9KPH7mt8IUnlS144lrByUtptDPHbcKjavgtLPmSMDwHLvm\r\n1h+w66ItoPFQEmH6C2WAwJO+e0HTORQf/8KNA+j0psd6BiBYlmR3KiTKjGGX\r\nWthEMRiy8dqWKvbqK1eDO4/rCaKVFTHNzb7xa6TbCJzooJac+2v6/rphY1RT\r\nHb8p362GOCFORmpRDsFLdAhMR2mBbE24VPlZSCV0ATK/Hn0WvcbUqD4UDGql\r\nZi2LcXPdQn9h3f9yQ82ZIS29gsrAYiIyBuDT5CyAmJYvHk+Ydm8bJsBgzjLf\r\nwaixfWFzeMlmVUneXOtvYRRca1LBAHCiLZGnJAwcrRDM+C6YFGl+icNmo2mA\r\n1UX90yfpsh9LMkfLpNEgPWJRrzeJyc5p4y9u9VpKIKHG9ML5Nj7SJ8tMPlyF\r\nli8RNQ4gJYmvxLy7lXcRn6OWxGybD3k63Pw=\r\n=G1Ay\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.1.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/1dadaa7638e7f1615647a33e4a974192/edge-runtime-vm-2.1.0.tgz", "_integrity": "sha512-ruYuUV0GPZ4d5JrUvhWOk8bfVhfcmIPM0f+uppI1VLQ++w7LvyRp4KMIm2ELOIak7OcmM7rP3G8D1YWWtQ1RKQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.3.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"@edge-runtime/primitives": "2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.1.0_1677595284783_0.758896761993235", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@edge-runtime/vm", "version": "2.1.1", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPLv2", "_id": "@edge-runtime/vm@2.1.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "ea50767cf3b724377d69e827d291285459e4d2e6", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.1.1.tgz", "fileCount": 21, "integrity": "sha512-lkhS92ytYfuty2IuMGGFdHU6r8yYTN3kWoDujRmvOoLoN1z9kOa/BZvxClvOqMbcqSL0X8juyM/ATbHCkK5Qdw==", "signatures": [{"sig": "MEQCID5f7ImWtCxx08llQfC6//1ZxL7dHhwbxaUNiTxBnBuRAiBcEEK8oNGqW+6yl9HLSeHQG8QLI7IazHhkUCNDF9A+Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/hcQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQHg/+Mlc/UZ+1i571wmUeuw9UuCCbA9rXwhX+jwRbYypqHhAqIouY\r\nfXJR2p5wDI/i9c6vDXxhJEDy8z6f45N8GuI3xbY0re/BzIxnDDpZ8o1zQuFU\r\n3S5o7Boc9nEMH1olV7Qp1HYsLhV3T7XztFuLU5L6ye0i+Z2NkUHoyC5g4fTv\r\npcXklTs54inEtghk+N1QzlHzk6b/ZEqOVLPHvcCd0A9EMGUs6EHcd+8vdR8e\r\n8F21Kre3bC0NrgM6AqfOCsjMgNQZs6VWopoSARbK0bBQ25Pb5GShLAUWFUcH\r\nVv3tLsiGZ3wn52s7P7VKKEyuVCVdRw64J1GxvDqdxWWXWaJFVt3Kazsbwyiv\r\nKIWDOeLuZYobEPbdbyTbFBSBOLa7XmUmEm38O6rkdzLvB889TgmOrgvxrbBH\r\nh4HPmSFEBhLaiA8PueBwP9tH3nyMvRxcgyt/UZuURXSnjV6rMiTu70Bx7wIt\r\nYJ9jxGAlsAkNwc0RwXN8gXFWLXEFmMbAg/qVgHdfpCzor1kZ3RYpWr885qQb\r\nTDWCyX4tz7pkGL62T+VvFzQyql9E5vVL21srUwjDwNmuuTrlvba1yuulGiAw\r\n0pa5NZaJIDeFPkG7ZDGDQay9L9GNnV0gEzo7Z5dMeeJVe2OTXS1jdyfZn7/F\r\nI3KJpO3xuEfrw/Os3+YPvxhLBMNSjEzHpMk=\r\n=JnFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.1.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/20e1796b8f936c24b6e0f8f7897b722a/edge-runtime-vm-2.1.1.tgz", "_integrity": "sha512-lkhS92ytYfuty2IuMGGFdHU6r8yYTN3kWoDujRmvOoLoN1z9kOa/BZvxClvOqMbcqSL0X8juyM/ATbHCkK5Qdw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.3.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"@edge-runtime/primitives": "2.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.1.1_1677596432126_0.9771700513701265", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@edge-runtime/vm", "version": "2.1.2", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.1.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "d760ce27b659c17c470b23453321769c08d213f5", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.1.2.tgz", "fileCount": 21, "integrity": "sha512-j4H5S26NJhYOyjVMN8T/YJuwwslfnEX1P0j6N2Rq1FaubgNowdYunA9nlO7lg8Rgjv6dqJ2zKuM7GD1HFtNSGw==", "signatures": [{"sig": "MEUCIAX+InwizJFG+rSaNOk70ozIJMg3oM89SZ6338N8A5b4AiEAq3NNjY6Ugh8S0hnMKWB4ry1ZjcvHcessuRC95h8zXOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkA9NjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIuw//ZhWpO0quT6F3VUIDG3dJUMa07GZyhO7VPQNzoILo/jCUjc4a\r\nHwPnbWAzF3O/cIkhdDApcNwc97un8q/cOPGgbqD+x1iovkBVGNFbwj4mO+Yp\r\nrWB4T5gGp0mLWSbW5pzasmXOLpc1jo92nbPdC6Zta8h50twJGUnZmttLAX1i\r\nMqMQ+9GkUzmpyuhs8fxvf3lu4Sc/zc8lyrAa+HbeVqUEqxGxUXIj8wJyIRjE\r\nUsNjRuJhjCkHgmGGxrwrck4LtpyTU9ruhOFxcCvrH2ANqzIrUCwVh0g/030Y\r\nvy++V9Q3ZNfUf3jfscXo8gcgaWi6JiSYBNYf7fDLslFhp56qmnmQC9pOa8UT\r\ndcn0PybUargACj7NodTE6GGPfdABQXirVS44sd8G7gswVVrz8/An2pXRTqPX\r\nLdmVsojTns3ldVi2txsfYrDSXceeLz/qcxB6dnq/TVl5gEeHuTn+XyWpiLiS\r\n52GIHzpzSxaJnEXouq+s3Qwnjn90H+QWcm/vvv+v4dWp7suzrcow3aYfLzM8\r\nEBdzbl2u6nEYzLclr8Bt0yl/Ecobv9k3vvshQHxkDz7F8Ot9WkE3+sBlQdSp\r\n2o6F0nJ05mVMS8SlPP4e5Asf8VQcbhRJOOUcq3N7ZymMbohjsAgyBh8ybz3F\r\nc+oCrgc4/j3IZ0JUNePkgx2vWJ1H9MOOakA=\r\n=n1q/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.1.2.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/9fb7bb8f58ad607f06c5437195970d21/edge-runtime-vm-2.1.2.tgz", "_integrity": "sha512-j4H5S26NJhYOyjVMN8T/YJuwwslfnEX1P0j6N2Rq1FaubgNowdYunA9nlO7lg8Rgjv6dqJ2zKuM7GD1HFtNSGw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"@edge-runtime/primitives": "2.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/vm_2.1.2_1677972323473_0.8349970266483078", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.0": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "e5adda8cfb862a29c1a2e65475aba5f8782e2485", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.0.tgz", "fileCount": 21, "integrity": "sha512-95yP7xPZVQszlBgQGszuMT/xdo2WPuoGQHvooY4di9S4ThZoqyQcTreBEQs5kJKVNDULfk1sgESglvfCqmUIuQ==", "signatures": [{"sig": "MEUCIQC7bicMk34aNnwI2PjaR+ynIr34WohFsOA/9ZmFpCtYKQIgc1smLeu/h+bTnAeczghFBt0zrCNoXu2tvPQiSTye30I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUPW1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzyA//d/0kGoK/4ovHY+8IFIfAgW9c2gTexCCCBiJ5vk6AXjQ7coOn\r\nAB5tAu5dJNVFtYuSNOPfXmibT9YDtB2JxLpMELTgqxvSCvxRRbGKPVqYVS7H\r\nEVn0knDHo0yQzEi+rFKBjvO4fpXfABR93cF8wyB2jCkPHpV0McEpe1dK28ML\r\nw/z8ThmInkRTpoJvpmzWS/bNlVZzpYANMeA9HUmCa5EXBdmuTMzYAwzKuf5d\r\nfM83xoPcDN1zlPQxZdbkIoGpwxiZQwDQlGlMi7RdwtviyqI1vdbatv7qsDcq\r\nVWYvi6S9gG4Lmk2aOVNrjQ328XTdPIyomrOr2vf5cnKyTGJ04sucqlGuAXOO\r\n9u9+SrfejULCzKk8uPiy2LEFWj5TO1ARbBKSR02/RlIrso49ZZb3apEJ7J18\r\ns8JtzkzQD3MhyDfBG8az1d3S84Lisvcvi1YCWGHJ6T7WPJ8UJrmIBg7vuUFb\r\n8H48rWMY/Zi1mhD2ThEdSzjTMLhIaqEcc1/c2+g8dWyK1je4M5cV2zf1dbTB\r\nylykde6HlliYd8ABgl78zM/oOnWsaz/LlD/l4p7+RQf4neACnnjH4YI7CADv\r\nz3H9Bbsbj4tn/FgXZOxLgu+Uu0iam4pcCdMHTEKIQzALpxqRhzA4f+hg2lm4\r\nEiIAjglVBfXobzjjLH4CvBdWAbo/JusSzJk=\r\n=r4RC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/3a12934bc22f45f9a4f183b0e001fc6a/edge-runtime-vm-2.2.0-beta.0.tgz", "_integrity": "sha512-95yP7xPZVQszlBgQGszuMT/xdo2WPuoGQHvooY4di9S4ThZoqyQcTreBEQs5kJKVNDULfk1sgESglvfCqmUIuQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.0_1683027381355_0.6005695188971847", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.1", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "593681d6ce1190a21c87bdb0f8e93b892ef16246", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.1.tgz", "fileCount": 21, "integrity": "sha512-on3yGcN4MF6Ebctn9Scl3P0oPlrei3u5ijrYOvNsDb5hwVNcRmMUfc4nz2du7DFNFvjBHzmot1lfBfNYe/f9Wg==", "signatures": [{"sig": "MEUCIEklcBJk9HaBkf9vmopl1eEPDtKTADMUE/KEWWx1fCZMAiEAoby6lkLdTJnq6oc/mrCDdfmlPShZ9kpApSwNcwifiMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86574}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/76758f6bae74168b8879323980482912/edge-runtime-vm-2.2.0-beta.1.tgz", "_integrity": "sha512-on3yGcN4MF6Ebctn9Scl3P0oPlrei3u5ijrYOvNsDb5hwVNcRmMUfc4nz2du7DFNFvjBHzmot1lfBfNYe/f9Wg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.1_1683032558616_0.30087144655267384", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.2", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "c66f1db44d2397cabd7bf1ff950859af6999e0de", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.2.tgz", "fileCount": 21, "integrity": "sha512-W6lhTyTBGyarAcjAxwvjOmUYOq3blyMYe3fETo+AxQhQ9kENZKKbWLArViZk6Gn0CwkBTyqP6P1ruyPErkC35A==", "signatures": [{"sig": "MEQCIHFGjSjaSLgGK3cDeL3jEjBoQ686faZd1i9pwluuD7ezAiAWUwnv505I2C9cgawuGRJ4SNy2IJfXirySxO1SzMANlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUnhwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrK/BAAid1/tPpqGY7u7Mfa6dTTmlsWK9anqpDOgatXEInOzRjLTmQm\r\n3CS3aTtiHGAeSVrnaYd1tNbZAdAEDsLkckOqt8hmRqobGX/ANnCF3VBXYuso\r\nfGgd3AdtPBC2V0AfV0yg7wnBiiQzSrn5UDhYMGf06EVyPdcqBg1c4j1LCzmS\r\nl2W/kHo5WN2eJTyeijLrruB7U/LS38gp28eH1ZqBRm++82XAC4DEnZxetxsf\r\nze931NZqfjn2esPcg/VREAVUw+kXa3b/TVWS6xfxLyDRPMkBrsXDMDINcR2M\r\nmpIpTV0vxRMW/fR7euO5AW/d107P0+vY1yBy1pepkch2i/g+hju2cvy4j0/q\r\nMWemrKzBahgri5X9f+//C4b/Pj+bguSiEgVRv/bcLFR3jToZaA4pxuyg7u5I\r\nf3pGCG/pA4gR0im4FifCfZXolRJWmRbTVVFIcClDLh3JAA4ueqBcEOUdFowy\r\nf6UwUfgLQfJMR07GnMUAXO3C/Ts5DSmVSNy3FFqUO+nt4aOJaXSac8JfIcnZ\r\nalPNWCYycW9x4A+TdDThf98QTc7xnIKo4RYW3dXDpI0PK36N1+e22d9G+Mz8\r\nZjKXc1YvxbGJu2WXFDlXVCilGitk7JZjwmU7opGqOPQbdmNmmioahGdg5S40\r\nfjyk+Q1qYgC7RMq8AGVMNxn0Fhms6FZEPYs=\r\n=hcr6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.2.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/be3545e9d342862667d6a357eabc4ccf/edge-runtime-vm-2.2.0-beta.2.tgz", "_integrity": "sha512-W6lhTyTBGyarAcjAxwvjOmUYOq3blyMYe3fETo+AxQhQ9kENZKKbWLArViZk6Gn0CwkBTyqP6P1ruyPErkC35A==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.2_1683126384225_0.8157588524160739", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.3": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.3", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "44784ac44b2a2c98d786b693047966b68fa6ec05", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.3.tgz", "fileCount": 21, "integrity": "sha512-X6C7GyrFwc//6UdqRcFGK7WJK7RKIv4SZlT6lYq3e/9H8Hx7a4w+qXnylsHpwffLX6S3WBu78xhKd2UvQ1AWfQ==", "signatures": [{"sig": "MEQCIC7r2YgUVQPV9g1jJvgEkUFbrsMrysl94LvHFLM252rbAiBw3zkLcrk2WljH/zGE0fHAlUHaP28UJu4s0Vq8sRdzvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.3.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/2888099481b47eab37f376d8b8ab522d/edge-runtime-vm-2.2.0-beta.3.tgz", "_integrity": "sha512-X6C7GyrFwc//6UdqRcFGK7WJK7RKIv4SZlT6lYq3e/9H8Hx7a4w+qXnylsHpwffLX6S3WBu78xhKd2UvQ1AWfQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.3_1683639602022_0.6556937513138092", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.4": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.4", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "67806531fa284c971db6a9a4552f655d49bfb692", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.4.tgz", "fileCount": 21, "integrity": "sha512-L3dQkDnSQAqD1tWk3GpCoTl3mhqgGoI8LkcgEsr46lltsm92hszOfahWVEdJDQ9REKUBy2046uoeX72DlyeeLw==", "signatures": [{"sig": "MEYCIQCXlpovjPpIC6Rayp6bEW8WWF6mZ+nBWx9f8OMh/QmGDgIhAM52FES/bIylBGjL4URdao8eea64h1+hggKWZU2ozfMu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.4.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/d49804b5428bdac37d1bf630b98854bd/edge-runtime-vm-2.2.0-beta.4.tgz", "_integrity": "sha512-L3dQkDnSQAqD1tWk3GpCoTl3mhqgGoI8LkcgEsr46lltsm92hszOfahWVEdJDQ9REKUBy2046uoeX72DlyeeLw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.4_1683640435357_0.3206824380895248", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.5": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.5", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "f862f8222b236a13a4a62d8ba7f68a465ab9a47b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.5.tgz", "fileCount": 21, "integrity": "sha512-HR3u2Avk9mx/Z8DhPZH8XTfIRwyQuBdQ2Irm8LfbKWIYFYGVpF00slEtu6B2qlxDfSO8ZuZXHSWuHBTbeXkS9Q==", "signatures": [{"sig": "MEUCICSj3+HW9Wg/0kjRk5EBClbirQq4nwcu0d2i+89XwNWLAiEA/KSowMhoexb3LHQ01hY1nJBfLAEf/oO/HHGdXbaicdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.5.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/8abc4d2e0944cc706f0074272e13a522/edge-runtime-vm-2.2.0-beta.5.tgz", "_integrity": "sha512-HR3u2Avk9mx/Z8DhPZH8XTfIRwyQuBdQ2Irm8LfbKWIYFYGVpF00slEtu6B2qlxDfSO8ZuZXHSWuHBTbeXkS9Q==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.5_1683642901313_0.5732771708763875", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.6": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.6", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.6", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "348e78e437ef55784dbad16af23288ca6863470d", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.6.tgz", "fileCount": 21, "integrity": "sha512-SAnKeOU63Ip8x+Qsq97Uj3CLevCq6aFLdFXuuI+KKUe4guWQo56+LApruFGEnoj2cMdcCoDin60emDgOOdPa7Q==", "signatures": [{"sig": "MEUCIFgqiiiyMNXIadGk/FKSBR2WCLRirdEdHLF2GexMGysFAiEAnSVhep22wAU+2d95038dTZ71KBoMELqcK/u628tJMRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.6.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/2cd90565a6445267e70eb806e1c81de0/edge-runtime-vm-2.2.0-beta.6.tgz", "_integrity": "sha512-SAnKeOU63Ip8x+Qsq97Uj3CLevCq6aFLdFXuuI+KKUe4guWQo56+LApruFGEnoj2cMdcCoDin60emDgOOdPa7Q==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.6_1684928925966_0.04140396078650599", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.7": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.7", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.7", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "5d9470ef29279b305af34a5e11f18a82e5bb362b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.7.tgz", "fileCount": 21, "integrity": "sha512-VS1CRKHehKb1pfGBMA2zwTthArnrGFMZzUxxI7sY/Q8MjF9/xadO1KWu9N8pgxfTd0pws/LlX82MCJtV/KsVpw==", "signatures": [{"sig": "MEYCIQC1ZBX6EU0q8ECDFB4UglWUny4uCPELuRE0DQS/8oalWAIhAIvaOcxK7qiv8E4SfDjy313UyUJ0u4z08/CdW/BPW/eN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.7.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/070be747c9370d468720874495ac0ad7/edge-runtime-vm-2.2.0-beta.7.tgz", "_integrity": "sha512-VS1CRKHehKb1pfGBMA2zwTthArnrGFMZzUxxI7sY/Q8MjF9/xadO1KWu9N8pgxfTd0pws/LlX82MCJtV/KsVpw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.7_1684935505017_0.7151077240378663", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.8": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.8", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.8", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "6b57e2601e6b59f1d67f97de4aacafc528e0c7d2", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.8.tgz", "fileCount": 21, "integrity": "sha512-Q7tTvhT/x2bBKbFVAiJrG1i2wdzrrMy2BaGrFTlR6SC2CnrwK44ukyQ1mcq8GABxZNw1vEHjeMjtdcKF6MlsXw==", "signatures": [{"sig": "MEYCIQCXjReOeslBO1TozVL+rqAVGqxDmkFffLwWxzBe0Wbk2wIhALEhF2mKhsCydxaLxLF4asRqQFJH4J4m6xsmo5ozlfOU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.8.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/a185f56ae86af402ba59e3a430b9f1c1/edge-runtime-vm-2.2.0-beta.8.tgz", "_integrity": "sha512-Q7tTvhT/x2bBKbFVAiJrG1i2wdzrrMy2BaGrFTlR6SC2CnrwK44ukyQ1mcq8GABxZNw1vEHjeMjtdcKF6MlsXw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.8_1685261451387_0.21725934310395112", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.9": {"name": "@edge-runtime/vm", "version": "2.2.0-beta.9", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@2.2.0-beta.9", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "04997a4be0d1507f1f2f8eda7fbf40363a8845fd", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-2.2.0-beta.9.tgz", "fileCount": 21, "integrity": "sha512-hs8QWlsPVRBNanen6tNlisnkHRkS2FiCMI7rLjvRLqy7xtMToMW4PM/ryb4qf+kTVq/Abi6parkPYFGZISZtHQ==", "signatures": [{"sig": "MEYCIQCMyWxvSRkOFUrDPnxiAeOgBgKxRn+joHjohN5NGio3dAIhAJ7h2tvh23XI52Wgkjwtn/kOcU1zCIy3pbPBqwogbiZa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76609}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-2.2.0-beta.9.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/fa65b401927d5664a62386896b7abb79/edge-runtime-vm-2.2.0-beta.9.tgz", "_integrity": "sha512-hs8QWlsPVRBNanen6tNlisnkHRkS2FiCMI7rLjvRLqy7xtMToMW4PM/ryb4qf+kTVq/Abi6parkPYFGZISZtHQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_2.2.0-beta.9_1685263443811_0.8511100937045257", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.10": {"name": "@edge-runtime/vm", "version": "3.0.0-beta.10", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0-beta.10", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "6353a1ab10e12247aa4de268934282b8412f5c26", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0-beta.10.tgz", "fileCount": 15, "integrity": "sha512-DNxsxrwjunPdpXHopuBE2CjtC60jxT6nHQVggSfiIn0ID52w3+ioZQrCPhsXzNEV/NyRgXDfml9QuaFYkmRE4w==", "signatures": [{"sig": "MEQCIGw5LAMO7vnMdMWi+yW/gkILIiRv1fmkYsm2/42lJXDrAiAvDwTVvo810nh/VL8MNmRP1BVo4wo9W+E+JU7BeoQGoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61500}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0-beta.10.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/c67f011222ff0cd717285afbad1042ae/edge-runtime-vm-3.0.0-beta.10.tgz", "_integrity": "sha512-DNxsxrwjunPdpXHopuBE2CjtC60jxT6nHQVggSfiIn0ID52w3+ioZQrCPhsXzNEV/NyRgXDfml9QuaFYkmRE4w==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0-beta.10_1685268367880_0.1436883923159087", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.11": {"name": "@edge-runtime/vm", "version": "3.0.0-beta.11", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0-beta.11", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "87938067613f740b22ad0978ab423e5308b5b0ab", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0-beta.11.tgz", "fileCount": 15, "integrity": "sha512-J0Jg2eFfl6XuomKkBHhWIUVhnqO0QxeCg6wTQZLoREo4Rw9BX7eKvSGvXoxSZTf5vKTq/lxMKxue+LnK5TSGdQ==", "signatures": [{"sig": "MEYCIQDx3JkU/2JGA5rIfB4jA0ikDBc94Q6oX6CBw/8FArvqHwIhANZRI1kWIQcx3iCI5wMq+jKPkE2VG4hHAd9NwJZLLCwm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61501}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0-beta.11.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/37fa40f96dd7ac60e80d46face92743b/edge-runtime-vm-3.0.0-beta.11.tgz", "_integrity": "sha512-J0Jg2eFfl6XuomKkBHhWIUVhnqO0QxeCg6wTQZLoREo4Rw9BX7eKvSGvXoxSZTf5vKTq/lxMKxue+LnK5TSGdQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0-beta.11_1685278090387_0.7249489682942354", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.12": {"name": "@edge-runtime/vm", "version": "3.0.0-beta.12", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0-beta.12", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "26daf025df3030a29aa835aea40032c5f1cbf8bc", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0-beta.12.tgz", "fileCount": 15, "integrity": "sha512-O2iIjptKXADvCAMGbeM8AN2yKkn6hfXXR+e0rgSF86p6DJcnvJc4h1Gmq1hrEXHzYweXTHJ/J1Yjld+eA3HYSA==", "signatures": [{"sig": "MEUCICM1ODWGLwBai6BHvwoLpex+h8qmFItkeb2N02lCp9ouAiEA//7S495yKFo2cX9nskom9QJ/v97vAGzjmJPHH0GorPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61501}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0-beta.12.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/7eb4046d3416e72446ee8d63a2cbf8ff/edge-runtime-vm-3.0.0-beta.12.tgz", "_integrity": "sha512-O2iIjptKXADvCAMGbeM8AN2yKkn6hfXXR+e0rgSF86p6DJcnvJc4h1Gmq1hrEXHzYweXTHJ/J1Yjld+eA3HYSA==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "2.2.0-beta.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0-beta.12_1685288108866_0.1379990717880899", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.13": {"name": "@edge-runtime/vm", "version": "3.0.0-beta.13", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0-beta.13", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "6473db4276e28346ec15f3dc0e627c6a02e74b80", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0-beta.13.tgz", "fileCount": 15, "integrity": "sha512-jwX2oA3j4pqTUMnu0FsIWkK9dDpDayZYEIiBBEaiqGcalgSHzRqennkE+22DjYxT+WcOdllqspMptY+VwuiIHw==", "signatures": [{"sig": "MEUCIQDkYYSP1FDsfrw2SpW4GffGLYzq1zSflbQi0xbpT0RWEwIgUsIFXCBXB7qYhWn3S5RUD72cTUteCEiM69c1kI4LCGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61501}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0-beta.13.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/168519608f52ca6797be0bacc9377254/edge-runtime-vm-3.0.0-beta.13.tgz", "_integrity": "sha512-jwX2oA3j4pqTUMnu0FsIWkK9dDpDayZYEIiBBEaiqGcalgSHzRqennkE+22DjYxT+WcOdllqspMptY+VwuiIHw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.0-beta.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0-beta.13_1685347793612_0.5820873457873306", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.14": {"name": "@edge-runtime/vm", "version": "3.0.0-beta.14", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0-beta.14", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "f40bec30757903a76de79b112594348e166dd968", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0-beta.14.tgz", "fileCount": 15, "integrity": "sha512-sBVyoy2E8nS0TOLjQOwUPyNulsnfC8L2qoqPLDoEPO0d1jdWD0ex9BsIxDf0MuCfpEE0Sdz1A0Bvb3PU861VGg==", "signatures": [{"sig": "MEUCIQDZbg2OHuGLSkRGmiJ5sUDOM6z4kvSkI75od4KIQnielwIgMiKvMuh3CtcS0i/DyV7b4stShdZMUWyrL0BQqqPPhh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61501}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0-beta.14.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/85d2bc39150b98f1ecb8efea1d28a793/edge-runtime-vm-3.0.0-beta.14.tgz", "_integrity": "sha512-sBVyoy2E8nS0TOLjQOwUPyNulsnfC8L2qoqPLDoEPO0d1jdWD0ex9BsIxDf0MuCfpEE0Sdz1A0Bvb3PU861VGg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.0-beta.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0-beta.14_1685359582720_0.8258135578979995", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@edge-runtime/vm", "version": "3.0.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "34d7629542297ca8263878395f085b2744dc7b84", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.0.tgz", "fileCount": 15, "integrity": "sha512-M3vBgmyndFInsIc8EvI55Hbqd9ajartfhulh6mZR/ZXej+akX01KFj0yMB/nzYsuLxXcmgU8Xv0+Jo2tLRYvtg==", "signatures": [{"sig": "MEUCIBGV9IYZfsgtnkBk9gg2GSS5vIyf8F5AIPMgHwzQRmSmAiEAv90vP+oKdp/smgVNQh1medP+XQ5fYdLWA5YTlIYoglg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61485}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/fd4520383b50f375f75e77d85cea348d/edge-runtime-vm-3.0.0.tgz", "_integrity": "sha512-M3vBgmyndFInsIc8EvI55Hbqd9ajartfhulh6mZR/ZXej+akX01KFj0yMB/nzYsuLxXcmgU8Xv0+Jo2tLRYvtg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.0_1685362544235_0.4811604583783917", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "@edge-runtime/vm", "version": "3.0.1", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "6a81178fca67f89744ce0bc235fa791427191b3b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.1.tgz", "fileCount": 15, "integrity": "sha512-69twXLIcqVx0iNlc1vFqnXgka2CZi2c/QBAmMzXBk0M6mPG+ICCBh2dd+cv1K+HW2pfLuSW+EskkFXWGeCf1Vw==", "signatures": [{"sig": "MEYCIQDUs3oKV5ahBDmEVusDIchCvhHFeYqADtrkKsMZSYQ8RgIhALmVq4QB/YA/k5ZQCh1GWFzymCbzsIr/ZAEn3bfGWbBM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/e3d00d114b2519db7eccb23c21cd3a11/edge-runtime-vm-3.0.1.tgz", "_integrity": "sha512-69twXLIcqVx0iNlc1vFqnXgka2CZi2c/QBAmMzXBk0M6mPG+ICCBh2dd+cv1K+HW2pfLuSW+EskkFXWGeCf1Vw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.3", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.1_1685615720379_0.3048568145483057", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "@edge-runtime/vm", "version": "3.0.2", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "db4cfeeed495ba70341d54420de3e94790343d2b", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.2.tgz", "fileCount": 15, "integrity": "sha512-M/IotCRwjxiqm4nob4Cj5gVh48u8coeZFRvYYeLFL9bbu56M93TmEfSRtxBjVRAldheZmYj6U+1LLnsBwjuM6g==", "signatures": [{"sig": "MEUCIBssqbGF+qSXgyaUVtFDf7oNI7hus+9cVMckHQCp0AAyAiEA9dQckGmpVW+YgE06r3xXOusx7MbiXwl6cZosl7X2Dks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.2.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/ae28e586b6ac6ee6e689e25db6f602f6/edge-runtime-vm-3.0.2.tgz", "_integrity": "sha512-M/IotCRwjxiqm4nob4Cj5gVh48u8coeZFRvYYeLFL9bbu56M93TmEfSRtxBjVRAldheZmYj6U+1LLnsBwjuM6g==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.2_1686303127840_0.41270425507978326", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "@edge-runtime/vm", "version": "3.0.3", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "92f1930d1eb8d0ccf6a3c165561cc22b2d9ddff8", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.3.tgz", "fileCount": 15, "integrity": "sha512-SPfI1JeIRNs/4EEE2Oc0X6gG3RqjD1TnKu2lwmwFXq0435xgZGKhc3UiKkYAdoMn2dNFD73nlabMKHBRoMRpxg==", "signatures": [{"sig": "MEUCIQDAEn30lvnfPs+EeAZ4isVjMXHtrsSNwQpdae41WKsdvwIgVIkzJp4bcAJblixOQ4xPvKAAiZPU4HbjUeMif7+Qon0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.3.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/e34f01cb20fe84c24adaf96c6a4f62cf/edge-runtime-vm-3.0.3.tgz", "_integrity": "sha512-SPfI1JeIRNs/4EEE2Oc0X6gG3RqjD1TnKu2lwmwFXq0435xgZGKhc3UiKkYAdoMn2dNFD73nlabMKHBRoMRpxg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"@edge-runtime/primitives": "3.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.3_1686853380875_0.6394131236562268", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "@edge-runtime/vm", "version": "3.0.4", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.0.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "0f66ac96e321e089e6669a680ebfbfaceee75e6a", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.0.4.tgz", "fileCount": 15, "integrity": "sha512-/QvVIn2+nO48dO3cl5tD3cjoF/xJ7WfTKBIs/oKgfmmWbEQ4EixKlOTJEpcBdOni0E8FCHIc7yxoJGq1qcLdCg==", "signatures": [{"sig": "MEYCIQDc041HtKfpxFXgfF7Qa/5wNvzSvYNu4YMjgPQV8UhhIgIhAKMjrIQ7VXMjZLgaiRZgIc107gc7is0Q/YrwMrhHojI/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.0.4.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/9bbe5ddcf496f73e4f9e2f3fc207bed6/edge-runtime-vm-3.0.4.tgz", "_integrity": "sha512-/QvVIn2+nO48dO3cl5tD3cjoF/xJ7WfTKBIs/oKgfmmWbEQ4EixKlOTJEpcBdOni0E8FCHIc7yxoJGq1qcLdCg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.5.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"@edge-runtime/primitives": "3.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.0.4_1689617848999_0.19958783189434093", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "@edge-runtime/vm", "version": "3.1.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "7e95115020c471606726b0ef3ac8eea3bc237618", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.0.tgz", "fileCount": 15, "integrity": "sha512-Y2JZgJP+4byI17SiDeEZhvBUvJ+om7E5ll/jrS7aGRpet5qKnJSsGep6xxhMjqT/j8ulFvTMN/kdlMMy5pEKBQ==", "signatures": [{"sig": "MEUCIF9mA3q/rLTp6JSG2V8QcXMjvXctY4mEVbisbuZQZVrFAiEA0uueOingXjGnF5Ab5OSSj/h3vggQLtNaB6Xy8cDQT/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/bb640aa89a3b8ec8000704844194e2f4/edge-runtime-vm-3.1.0.tgz", "_integrity": "sha512-Y2JZgJP+4byI17SiDeEZhvBUvJ+om7E5ll/jrS7aGRpet5qKnJSsGep6xxhMjqT/j8ulFvTMN/kdlMMy5pEKBQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.6.7", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@edge-runtime/primitives": "3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.0_1692809173560_0.192035270524326", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "@edge-runtime/vm", "version": "3.1.1", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "1693bd43ceffb28eb34b852f375eb939d9ec134c", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.1.tgz", "fileCount": 15, "integrity": "sha512-6NJRRG04/91qnWLZj+wZm27q6fJkTbkZdIJdo/Ig++GTxkAv8Wh/45nIcz9Xg7AzIAMpAkflFdiCrCoZ3hp1Iw==", "signatures": [{"sig": "MEUCIBjwCK0J3E/WVpah4Y6OCoAx6QV4O2DlqXDQump4T1z0AiEA/szzC+tPw9SG7glnk2v+0gxfgBGCjcwXN1tNBlCAzd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/1e3e256d727db587ba18d8275858e4b1/edge-runtime-vm-3.1.1.tgz", "_integrity": "sha512-6NJRRG04/91qnWLZj+wZm27q6fJkTbkZdIJdo/Ig++GTxkAv8Wh/45nIcz9Xg7AzIAMpAkflFdiCrCoZ3hp1Iw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.6.7", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@edge-runtime/primitives": "3.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.13.0", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.1_1693909905406_0.9625070719881033", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "@edge-runtime/vm", "version": "3.1.2", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "903a89426d7e236d4e79c716c17b4e1ce9425e43", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.2.tgz", "fileCount": 15, "integrity": "sha512-SqUyvinQw15YG8HiMbGUWfniJBHYuk0n5j+FBso85fFmiUPPHZrcs3KK6UNUusots58RSLipofuPAK8rrNwLWQ==", "signatures": [{"sig": "MEYCIQD6uUYBATr8SRYqVIno201XvfzKkOPzFFItkCHQ8dO2WgIhANdPw1q3nCJRQ4rR+6GRsplzZ7uiodTm2x3IJ5+IlT+E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.2.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/0b65fa8cfc61b15137b01d8acbda10f8/edge-runtime-vm-3.1.2.tgz", "_integrity": "sha512-SqUyvinQw15YG8HiMbGUWfniJBHYuk0n5j+FBso85fFmiUPPHZrcs3KK6UNUusots58RSLipofuPAK8rrNwLWQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.6.7", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@edge-runtime/primitives": "4.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.1", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.2_1694424101986_0.5183114598065246", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "@edge-runtime/vm", "version": "3.1.3", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "1e27cd86a072dfc86f5e252cd52bcc2daa2009d5", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.3.tgz", "fileCount": 15, "integrity": "sha512-LkEtuVtT1kgOEghxFAEJZ+BeIpGz3XfI2l1Ts74HXzd3JneMmmx6RRkNiEE85DVBpuvv9d8KB1u+lc1CHTmz/g==", "signatures": [{"sig": "MEUCIQDqUkgibvipO8bhGFVeENsTzo8rXrmV/k7GQ/h31iujzQIgZuP+wKEWDakh9ln4H6lztsgOL1mXS2n2AfpICZ6HEcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61704}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.3.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/069cecf9063c9f3ec0d5935a0dcfb74e/edge-runtime-vm-3.1.3.tgz", "_integrity": "sha512-LkEtuVtT1kgOEghxFAEJZ+BeIpGz3XfI2l1Ts74HXzd3JneMmmx6RRkNiEE85DVBpuvv9d8KB1u+lc1CHTmz/g==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.6.7", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@edge-runtime/primitives": "4.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.1", "multer": "1.4.5-lts.1", "@types/ws": "8.5.5", "test-listen": "1.1.0", "@types/multer": "1.4.7", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.3_1694441170710_0.7651074076433511", "host": "s3://npm-registry-packages"}}, "3.1.4": {"name": "@edge-runtime/vm", "version": "3.1.4", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "330715a7cda6fecbf8be9d04ac5a59db8f9272c1", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.4.tgz", "fileCount": 15, "integrity": "sha512-k3AdzA2Bfdr7NCezxER0Ch3MPwMbPqEFJqlgJ7xSIvsuuW+8c2rRQpPDMoy8ZSPbnwzg+SK3cyoLGDt4hFhexQ==", "signatures": [{"sig": "MEUCIQCJ1xg5QeRxLjVRSi6WLR4h5gNP0RCjR9hFftI5eTtA2AIgTNM619ubeQpgSdFAoYjTzq6sJsOzgIKMlE9Yla2j9ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61670}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.4.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/8c2030ad99f49567b55eaa161b755ae8/edge-runtime-vm-3.1.4.tgz", "_integrity": "sha512-k3AdzA2Bfdr7NCezxER0Ch3MPwMbPqEFJqlgJ7xSIvsuuW+8c2rRQpPDMoy8ZSPbnwzg+SK3cyoLGDt4hFhexQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.8.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"@edge-runtime/primitives": "4.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.2", "multer": "1.4.5-lts.1", "@types/ws": "8.5.6", "test-listen": "1.1.0", "@types/multer": "1.4.8", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.4_1696320027948_0.22888178005742965", "host": "s3://npm-registry-packages"}}, "3.1.5": {"name": "@edge-runtime/vm", "version": "3.1.5", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.5", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "dbcbccc45772e0bf91c1ae7feae7164bc3739fdb", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.5.tgz", "fileCount": 15, "integrity": "sha512-vjSO7S9+iAeZJd/m8ulpiET2wmd3JpAt/twLPM5vYWe8JO8WH+R/JWGz7Vx9ih62Xq8VZucRcZJSWsi5G8+IRg==", "signatures": [{"sig": "MEQCIB8QJVSvETyl+j318o8zrP4lO8y6KQtPwVN4Z3JkGpFUAiAkspCwda0EqSf4BfShzNBKPHXbQPEo1MxLqarT3/EkfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61687}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.5.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/3ff45020452ee095c97a0c9feea639c6/edge-runtime-vm-3.1.5.tgz", "_integrity": "sha512-vjSO7S9+iAeZJd/m8ulpiET2wmd3JpAt/twLPM5vYWe8JO8WH+R/JWGz7Vx9ih62Xq8VZucRcZJSWsi5G8+IRg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.8.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"@edge-runtime/primitives": "4.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.2", "multer": "1.4.5-lts.1", "@types/ws": "8.5.7", "test-listen": "1.1.0", "@types/multer": "1.4.8", "@types/test-listen": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.5_1697536468648_0.5086549809159076", "host": "s3://npm-registry-packages"}}, "3.1.6": {"name": "@edge-runtime/vm", "version": "3.1.6", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.6", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "03ff97a8101944c15944445659b2568bf1468163", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.6.tgz", "fileCount": 15, "integrity": "sha512-ChMpk3eBZegGIkHjoDDQhnnx+c5rzolC/2+NNymVJsivHtTVMIYQAOdG4jyTUMhmmrEGnXUgnHxOflR+1M4SIA==", "signatures": [{"sig": "MEUCIBU+U395l1J7wzuAaK8iB9rPwOsWSI2LzK6sJ1hP/n/4AiEAuh72KxSjlZKyrNaAiarlnZDh8gfaZzJgm4WdltN9xMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61687}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.6.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/128b0e52c5d57352bcc8dd89b74b0400/edge-runtime-vm-3.1.6.tgz", "_integrity": "sha512-ChMpk3eBZegGIkHjoDDQhnnx+c5rzolC/2+NNymVJsivHtTVMIYQAOdG4jyTUMhmmrEGnXUgnHxOflR+1M4SIA==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.8.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@edge-runtime/primitives": "4.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.2", "multer": "1.4.5-lts.1", "@types/ws": "8.5.8", "test-listen": "1.1.0", "@types/multer": "1.4.9", "@types/test-listen": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.6_1698144616012_0.6835721881560777", "host": "s3://npm-registry-packages"}}, "3.1.7": {"name": "@edge-runtime/vm", "version": "3.1.7", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.7", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "74f3bc64220c07ca0c760e5686726651a3835dc4", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.7.tgz", "fileCount": 15, "integrity": "sha512-hUMFbDQ/nZN+1TLMi6iMO1QFz9RSV8yGG8S42WFPFma1d7VSNE0eMdJUmwjmtav22/iQkzHMmu6oTSfAvRGS8g==", "signatures": [{"sig": "MEUCIQCcFdqH0K+EEsPiE0L+6uTRDwm3dX4FvVSyJQb98eAazgIgCdTqjh006vmbI4UtZEnl5Z9n4VWPepQ0Hmg+Q18vFhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61628}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.7.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/94f0f6d3b089d5f1ff26b08b21f036c9/edge-runtime-vm-3.1.7.tgz", "_integrity": "sha512-hUMFbDQ/nZN+1TLMi6iMO1QFz9RSV8yGG8S42WFPFma1d7VSNE0eMdJUmwjmtav22/iQkzHMmu6oTSfAvRGS8g==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "9.8.1", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@edge-runtime/primitives": "4.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.14.2", "@types/ws": "8.5.8", "test-listen": "1.1.0", "@types/test-listen": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.7_1698936881720_0.8761515591172848", "host": "s3://npm-registry-packages"}}, "3.1.8": {"name": "@edge-runtime/vm", "version": "3.1.8", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.1.8", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "b1583e25e8b305fe870068aefec76b4eac5a20b0", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.1.8.tgz", "fileCount": 15, "integrity": "sha512-BticbgpQAh8zRXUrpkbwxhEy9eAMp0L0+thAcS+xLD+uhWpTqsyqovdvV6e4FeeRo5sBg+lnMpoSG8bulFHKTQ==", "signatures": [{"sig": "MEQCIEVzboPB2YuLM4yifziWN70S+gKY7fQ2/cQcIk50/LWkAiAEtZPuJhDRwthvHxYj7Ep1mTcvq/r7pZuC8IS7O9bkcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61829}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.1.8.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/2834ffd5cf0d8eba710267b16ba9f7f6/edge-runtime-vm-3.1.8.tgz", "_integrity": "sha512-BticbgpQAh8zRXUrpkbwxhEy9eAMp0L0+thAcS+xLD+uhWpTqsyqovdvV6e4FeeRo5sBg+lnMpoSG8bulFHKTQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.2.3", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"@edge-runtime/primitives": "4.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.16.0", "@types/ws": "8.5.10", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.1.8_1706015263041_0.10839519352428373", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "@edge-runtime/vm", "version": "3.2.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MPL-2.0", "_id": "@edge-runtime/vm@3.2.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "8a735241d14e9fdad85497b8b17d0ea157df4710", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-3.2.0.tgz", "fileCount": 15, "integrity": "sha512-0dEVyRLM/lG4gp1R/Ik5bfPl/1wX00xFwd5KcNH602tzBa09oF7pbTKETEhR1GjZ75K6OJnYFu8II2dyMhONMw==", "signatures": [{"sig": "MEYCIQCVin6zxMZRqs7sa9lLrRUHPKCEP8hAn7Z7e0Qwd4LP0QIhAPz9XQIY+1KN7h+zrzaH0QK7/Z/6zwyWxtwXE3KirACR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61829}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-3.2.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/9279e2e4e10a343cb23787e3eb149ab1/edge-runtime-vm-3.2.0.tgz", "_integrity": "sha512-0dEVyRLM/lG4gp1R/Ik5bfPl/1wX00xFwd5KcNH602tzBa09oF7pbTKETEhR1GjZ75K6OJnYFu8II2dyMhONMw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.2.3", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"@edge-runtime/primitives": "4.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.16.0", "@types/ws": "8.5.10", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_3.2.0_1707828782340_0.863352357725973", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@edge-runtime/vm", "version": "4.0.0", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MIT", "_id": "@edge-runtime/vm@4.0.0", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "361987e7d6c7bc1eb17e04211d8fb3ff5d5e80c9", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-4.0.0.tgz", "fileCount": 15, "integrity": "sha512-XHaLSnCVa5Z1pyQcbSVNYSUFWi+y4DTyN8QANdfeDl7aVg6PK4UtCb6WRAjVoFSBsYU/0oqbm4rjb27lhkW6bQ==", "signatures": [{"sig": "MEUCIQDRSzI9EwhhZ5Xx1uIdLKPISBYf/0QzMAPguQ5OcYcX2gIgblQ2pTFx+z0lBg9KvCerjHPObKrwh0+6V2+db795ObA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47272}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-4.0.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/1f79678d8ca595df777a97a3b1c64fdc/edge-runtime-vm-4.0.0.tgz", "_integrity": "sha512-XHaLSnCVa5Z1pyQcbSVNYSUFWi+y4DTyN8QANdfeDl7aVg6PK4UtCb6WRAjVoFSBsYU/0oqbm4rjb27lhkW6bQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.7.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"@edge-runtime/primitives": "5.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.17.0", "@types/ws": "8.5.10", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_4.0.0_1720371762455_0.16129563429576188", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@edge-runtime/vm", "version": "4.0.1", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MIT", "_id": "@edge-runtime/vm@4.0.1", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "5625d6f4a954db34ecfbb27491d30253fa75d6d0", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-4.0.1.tgz", "fileCount": 15, "integrity": "sha512-jYXrosmxlahsgLlChnSTRIfoOkcg+U+7jMEFvV5pQ9mJ7BrM2O4Kb+n/IDardpgEWZsXLBC9Y2RN7nhpuX7PTQ==", "signatures": [{"sig": "MEQCIE7ZpEGh8RxfV7ZlP66TrfQuQdpbO1pCdymqApFac63cAiApen+s46Lb+Vm2whp0FaeTGOsPeElfo36lLGqUEIYIEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47272}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-4.0.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/d1aa6414caa11b971f0ea6d358ac8fd9/edge-runtime-vm-4.0.1.tgz", "_integrity": "sha512-jYXrosmxlahsgLlChnSTRIfoOkcg+U+7jMEFvV5pQ9mJ7BrM2O4Kb+n/IDardpgEWZsXLBC9Y2RN7nhpuX7PTQ==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.7.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"@edge-runtime/primitives": "5.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.18.0", "@types/ws": "8.5.10", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_4.0.1_1721031789579_0.6576511797435098", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@edge-runtime/vm", "version": "4.0.2", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MIT", "_id": "@edge-runtime/vm@4.0.2", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "8b2da26da62a6b039153092d4a45380f31138eb8", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-4.0.2.tgz", "fileCount": 15, "integrity": "sha512-hhawzuXYjGFtE2gvoeh0ke4ulLCWnaS3e1G/UsNI3BAxDRVxsZkzZhyrp9OM6VMi3bAOgN/de/Zvs/8OMa0Nmg==", "signatures": [{"sig": "MEUCIGCAswKvXQ/nlRMo/JVnW44RYdAtAxr4w4f9uus8xcAcAiEAt3YxHYwX/mUdnI2jLCUcPm4ZCiw/NfXQf0XrqUuPz9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47272}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-4.0.2.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/80609919ab98e662eb04e8bb75db15e0/edge-runtime-vm-4.0.2.tgz", "_integrity": "sha512-hhawzuXYjGFtE2gvoeh0ke4ulLCWnaS3e1G/UsNI3BAxDRVxsZkzZhyrp9OM6VMi3bAOgN/de/Zvs/8OMa0Nmg==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.7.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"@edge-runtime/primitives": "5.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.18.0", "@types/ws": "8.5.10", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_4.0.2_1724067765356_0.9701545957684814", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "@edge-runtime/vm", "version": "4.0.3", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MIT", "_id": "@edge-runtime/vm@4.0.3", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "aeec32bf3cbd00005c46904694c3f0c62f6fc100", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-4.0.3.tgz", "fileCount": 15, "integrity": "sha512-2EKlqxSbZTV4D+XG8DTX+9P1SL+m48ahvNbDuxz+dZkmUZ+ju4hl/m28j7QMbC9kU5S+4HUJCYKCAfA+3gggLw==", "signatures": [{"sig": "MEUCICG8Rr//Kjqw7iezQvOLR5FgyPnZ23o4z+Cat3NqK30aAiEA6z5xgCgMN+31hWXcDX9tHGQbcI54VfWIvdTclvbBTj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47584}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-4.0.3.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/9e525a7baf737b193955e2a3134ffbbe/edge-runtime-vm-4.0.3.tgz", "_integrity": "sha512-2EKlqxSbZTV4D+XG8DTX+9P1SL+m48ahvNbDuxz+dZkmUZ+ju4hl/m28j7QMbC9kU5S+4HUJCYKCAfA+3gggLw==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.7.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"@edge-runtime/primitives": "5.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.18.0", "@types/ws": "8.5.12", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_4.0.3_1725354645829_0.5977273606156746", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "@edge-runtime/vm", "version": "4.0.4", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "license": "MIT", "_id": "@edge-runtime/vm@4.0.4", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://edge-runtime.vercel.app/packages/vm", "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "dist": {"shasum": "303adeb5a35c7445da4b22a5f81e1f22e13ca7e5", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-4.0.4.tgz", "fileCount": 15, "integrity": "sha512-LqPw+yaSPpCNnVZl5XoHQAySEzlnZiC9gReUuQHMh9GI03KKqwpVqWkIK1UfK116Yww7f2WZuAgnY/nhHwTsJA==", "signatures": [{"sig": "MEQCIGb/lLsY1kJbWkZotsc78jxq3eXROlr+FJo1Cjbdnb0jAiAMB/ZVS8yRraOmtjakGq8ruArWm0knr5Wh6OhZQtQbnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47584}, "main": "dist/index.js", "_from": "file:edge-runtime-vm-4.0.4.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=16"}, "scripts": {"test": "jest", "build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:node": "rm -rf node_modules", "clean:build": "rm -rf dist"}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/tmp/14eaeb8719ea2bc7c0947364948ae16e/edge-runtime-vm-4.0.4.tgz", "_integrity": "sha512-LqPw+yaSPpCNnVZl5XoHQAySEzlnZiC9gReUuQHMh9GI03KKqwpVqWkIK1UfK116Yww7f2WZuAgnY/nhHwTsJA==", "repository": {"url": "git+https://github.com/vercel/edge-runtime.git", "type": "git", "directory": "packages/vm"}, "_npmVersion": "10.7.0", "description": "Low level bindings for creating Web Standard contexts.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"@edge-runtime/primitives": "5.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "8.18.0", "@types/ws": "8.5.12", "test-listen": "1.1.0", "@types/test-listen": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/vm_4.0.4_1730896851512_0.4551432545468146", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "@edge-runtime/vm", "description": "Low level bindings for creating Web Standard contexts.", "homepage": "https://edge-runtime.vercel.app/packages/vm", "version": "5.0.0", "main": "dist/index.js", "repository": {"directory": "packages/vm", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "engines": {"node": ">=18"}, "license": "MIT", "publishConfig": {"access": "public"}, "types": "dist/index.d.ts", "dependencies": {"@edge-runtime/primitives": "6.0.0"}, "devDependencies": {"@types/test-listen": "1.1.2", "@types/ws": "8.5.12", "test-listen": "1.1.0", "ws": "8.18.0"}, "scripts": {"build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:build": "rm -rf dist", "clean:node": "rm -rf node_modules", "test": "jest"}, "_id": "@edge-runtime/vm@5.0.0", "_integrity": "sha512-NKBGBSIKUG584qrS1tyxVpX/AKJKQw5HgjYEnPLC0QsTw79JrGn+qUr8CXFb955Iy7GUdiiUv1rJ6JBGvaKb6w==", "_resolved": "/tmp/cb52a1a86f15e7014e948291e3d0ae5e/edge-runtime-vm-5.0.0.tgz", "_from": "file:edge-runtime-vm-5.0.0.tgz", "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NKBGBSIKUG584qrS1tyxVpX/AKJKQw5HgjYEnPLC0QsTw79JrGn+qUr8CXFb955Iy7GUdiiUv1rJ6JBGvaKb6w==", "shasum": "0db56607125248bb58d53470a4388c738e4f9bf4", "tarball": "https://registry.npmjs.org/@edge-runtime/vm/-/vm-5.0.0.tgz", "fileCount": 15, "unpackedSize": 47958, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDUfDOvxWTMDqO3I5DgZt07PL4YLvn5K3AGT6+jKzl1aAiEAuE8qc56vioUg5u1f/UVG81njHuXgYP+FwF5QCYhHQKE="}]}, "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vm_5.0.0_1733153430337_0.8913415884191109"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-05-05T12:59:11.385Z", "modified": "2024-12-02T15:30:30.679Z", "0.12.5": "2022-05-05T12:59:11.556Z", "0.12.6": "2022-05-05T13:59:45.153Z", "0.12.7": "2022-05-09T09:39:28.925Z", "0.12.8": "2022-05-09T14:23:50.074Z", "0.12.9": "2022-05-09T14:32:15.013Z", "0.12.10": "2022-05-12T10:44:09.205Z", "1.0.0": "2022-05-18T21:53:41.101Z", "1.0.1-beta.0": "2022-05-23T13:54:49.779Z", "1.0.1-beta.1": "2022-05-24T10:46:22.446Z", "1.0.1-beta.2": "2022-05-24T14:22:01.338Z", "1.0.1-beta.3": "2022-05-24T14:28:10.433Z", "1.0.1-beta.4": "2022-05-25T14:03:53.273Z", "1.0.1-beta.5": "2022-05-25T19:30:29.663Z", "1.0.1-beta.6": "2022-05-25T19:53:46.639Z", "1.0.1-beta.7": "2022-05-25T19:58:29.906Z", "1.0.1-beta.8": "2022-05-26T12:13:19.829Z", "1.0.1-beta.9": "2022-05-26T13:30:16.286Z", "1.0.1-beta.10": "2022-05-30T10:53:05.083Z", "1.0.1": "2022-05-31T09:23:29.247Z", "1.1.0-beta.0": "2022-06-06T10:28:35.938Z", "1.1.0-beta.1": "2022-06-06T16:36:51.591Z", "1.1.0-beta.2": "2022-06-08T11:48:07.967Z", "1.1.0-beta.3": "2022-06-14T12:02:58.416Z", "1.1.0-beta.4": "2022-06-16T14:06:57.363Z", "1.1.0-beta.5": "2022-06-16T17:40:19.642Z", "1.1.0-beta.6": "2022-06-17T11:04:11.031Z", "1.1.0-beta.7": "2022-06-21T09:05:06.636Z", "1.1.0-beta.8": "2022-06-27T16:20:46.719Z", "1.1.0-beta.9": "2022-06-28T09:35:07.965Z", "1.1.0-beta.10": "2022-06-28T11:34:05.605Z", "1.1.0-beta.11": "2022-07-04T14:36:28.570Z", "1.1.0-beta.12": "2022-07-18T12:51:35.233Z", "1.1.0-beta.13": "2022-07-19T09:19:03.174Z", "1.1.0-beta.14": "2022-07-19T10:12:49.629Z", "1.1.0-beta.15": "2022-07-19T16:06:23.436Z", "1.1.0-beta.16": "2022-07-20T11:32:39.752Z", "1.1.0-beta.17": "2022-07-21T08:58:37.555Z", "1.1.0-beta.18": "2022-07-25T09:06:48.124Z", "1.1.0-beta.19": "2022-07-25T13:10:29.155Z", "1.1.0-beta.20": "2022-07-26T07:20:40.868Z", "1.1.0-beta.21": "2022-07-27T07:57:24.822Z", "1.1.0-beta.22": "2022-07-27T08:32:49.311Z", "1.1.0-beta.23": "2022-07-27T13:12:03.153Z", "1.1.0-beta.24": "2022-08-01T13:24:23.189Z", "1.1.0-beta.25": "2022-08-01T14:28:04.316Z", "1.1.0-beta.26": "2022-08-04T12:22:02.830Z", "1.1.0-beta.27": "2022-08-19T08:45:23.014Z", "1.1.0-beta.28": "2022-08-24T10:26:46.174Z", "1.1.0-beta.29": "2022-08-24T10:34:37.673Z", "1.1.0-beta.30": "2022-08-24T16:04:07.639Z", "1.1.0-beta.31": "2022-08-24T16:07:17.931Z", "1.1.0-beta.32": "2022-08-25T12:07:36.605Z", "1.1.0-beta.33": "2022-09-22T08:48:36.202Z", "1.1.0-beta.34": "2022-09-22T15:14:52.592Z", "1.1.0-beta.35": "2022-10-04T12:17:52.813Z", "1.1.0-beta.36": "2022-10-05T14:51:43.956Z", "1.1.0-beta.37": "2022-10-18T08:16:04.926Z", "1.1.0": "2022-10-24T15:02:25.840Z", "2.0.0": "2022-10-27T15:55:14.015Z", "2.0.1": "2022-11-02T11:45:49.598Z", "2.0.2": "2022-11-08T17:36:26.248Z", "2.0.3": "2023-01-18T17:43:32.643Z", "2.0.4": "2023-02-07T13:06:23.815Z", "2.0.5": "2023-02-08T14:48:09.235Z", "2.1.0": "2023-02-28T14:41:24.952Z", "2.1.1": "2023-02-28T15:00:32.317Z", "2.1.2": "2023-03-04T23:25:23.667Z", "2.2.0-beta.0": "2023-05-02T11:36:21.537Z", "2.2.0-beta.1": "2023-05-02T13:02:38.800Z", "2.2.0-beta.2": "2023-05-03T15:06:24.426Z", "2.2.0-beta.3": "2023-05-09T13:40:02.196Z", "2.2.0-beta.4": "2023-05-09T13:53:55.551Z", "2.2.0-beta.5": "2023-05-09T14:35:01.524Z", "2.2.0-beta.6": "2023-05-24T11:48:46.156Z", "2.2.0-beta.7": "2023-05-24T13:38:25.187Z", "2.2.0-beta.8": "2023-05-28T08:10:51.567Z", "2.2.0-beta.9": "2023-05-28T08:44:04.084Z", "3.0.0-beta.10": "2023-05-28T10:06:08.203Z", "3.0.0-beta.11": "2023-05-28T12:48:10.585Z", "3.0.0-beta.12": "2023-05-28T15:35:09.083Z", "3.0.0-beta.13": "2023-05-29T08:09:53.786Z", "3.0.0-beta.14": "2023-05-29T11:26:23.013Z", "3.0.0": "2023-05-29T12:15:44.442Z", "3.0.1": "2023-06-01T10:35:20.548Z", "3.0.2": "2023-06-09T09:32:08.039Z", "3.0.3": "2023-06-15T18:23:01.062Z", "3.0.4": "2023-07-17T18:17:29.269Z", "3.1.0": "2023-08-23T16:46:13.778Z", "3.1.1": "2023-09-05T10:31:45.635Z", "3.1.2": "2023-09-11T09:21:42.158Z", "3.1.3": "2023-09-11T14:06:10.955Z", "3.1.4": "2023-10-03T08:00:28.138Z", "3.1.5": "2023-10-17T09:54:28.861Z", "3.1.6": "2023-10-24T10:50:16.322Z", "3.1.7": "2023-11-02T14:54:41.939Z", "3.1.8": "2024-01-23T13:07:43.180Z", "3.2.0": "2024-02-13T12:53:02.504Z", "4.0.0": "2024-07-07T17:02:42.642Z", "4.0.1": "2024-07-15T08:23:09.757Z", "4.0.2": "2024-08-19T11:42:45.500Z", "4.0.3": "2024-09-03T09:10:45.986Z", "4.0.4": "2024-11-06T12:40:51.683Z", "5.0.0": "2024-12-02T15:30:30.495Z"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "license": "MIT", "homepage": "https://edge-runtime.vercel.app/packages/vm", "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "repository": {"directory": "packages/vm", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "description": "Low level bindings for creating Web Standard contexts.", "maintainers": [{"name": "kikobeats", "email": "<EMAIL>"}], "readme": "<div align=\"center\">\n  <br>\n  <img src=\"https://user-images.githubusercontent.com/2096101/235130063-e561514e-1f66-4ff6-9034-70dbf7ca3260.png#gh-dark-mode-only\">\n  <img src=\"https://user-images.githubusercontent.com/2096101/235127419-ac6fe609-d0cd-4339-a593-c48305a83823.png#gh-light-mode-only\">\n  <br>\n  <br>\n  <p align=\"center\"><strong>@edge-runtime/vm</strong>: Low level bindings for creating Web Standard contexts.</p>\n  <p align=\"center\">See <a href=\"https://edge-runtime.vercel.app/packages/vm\" target='_blank' rel='noopener noreferrer'>@edge-runtime/vm</a> section in our <a href=\"https://edge-runtime.vercel.app/\" target='_blank' rel='noopener noreferrer'>website</a> for more information.</p>\n  <br>\n</div>\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install @edge-runtime/vm --save\n```\n\nor using yarn:\n\n```sh\nyarn add @edge-runtime/vm --dev\n```\n\nor using pnpm:\n\n```sh\npnpm install @edge-runtime/vm --save\n```\n\n## License\n\n**@edge-runtime/vm** © [Vercel](https://vercel.com), released under the [MPLv2](https://github.com/vercel/edge-runtime/blob/main/LICENSE.md) License.<br>\nAuthored and maintained by [Vercel](https://vercel.com) with help from [contributors](https://github.com/vercel/edge-runtime/contributors).\n\n> [vercel.com](https://vercel.com) · GitHub [Vercel](https://github.com/vercel) · Twitter [@vercel](https://twitter.com/vercel)\n", "readmeFilename": "README.md"}