{"_id": "@babel/plugin-transform-dynamic-import", "_rev": "33-b44102abeb6804a08ca02681a49b5ce6", "name": "@babel/plugin-transform-dynamic-import", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ad3e0661ff8167a195f2ff783c6947090fffd442", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-Rfuj4hqQoLoKWlEar7Y45KXQoOdBXRlbygBnft1bFOBSYXRegnhOw+rkGiGYGCGflG+pi1bU7+iNfzItQa7lSA==", "signatures": [{"sig": "MEUCIA00OzNCWZLXhhq+KQQ9UnanXZZaLmtTsnt2gzrvit3AAiEA0TrBzI17X6Omy+NY53G3YkAk91wPcFaRSQSwZCNHd6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6244}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.22.0_1685108714574_0.007223451496950473", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.22.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6c56afaf896a07026330cf39714532abed8d9ed1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.1.tgz", "fileCount": 5, "integrity": "sha512-rlhWtONnVBPdmt+jeewS0qSnMz/3yLFrqAP8hHC6EDcrYRSyuz9f9yQhHvVn2Ad6+yO9fHXac5piudeYrInxwQ==", "signatures": [{"sig": "MEQCIGpuFrQQdij43PoWppajs9JXTCm06cZTPveug9Kn2dsoAiBqwKcm8PoJ+511bh92egr3r18Y4zKxjvTuI3l3NZVclw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6163}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.22.1_1685118890762_0.4386237085330378", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d6908a8916a810468c4edff73b5b75bda6ad393e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-0MC3ppTB1AMxd8fXjSrbPa7LT9hrImt+/fcj+Pg5YMD7UQyWp/02+JWpdnCymmsXwIx5Z+sYn1bwCn4ZJNvhqQ==", "signatures": [{"sig": "MEYCIQCB0i4aIcyAgGYN4mMNgO0EkXF34G1lauKgOcumoCBcpQIhANuUsD4ZmK+Q/bJfH036xu0wVaNWJBbq/jZiiRV93Hgv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6163}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.22.5_1686248475200_0.7059361444646539", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7eb2f04bdf76900679b52bb2523df2bd6b284145", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-WkyMsm4a5/NUbvKItDCY23aXgi9LVBOs0Dphgr/udJUkOTdGuGy2l3OOI2Ib7KkHSwFM5ttTXnY7tWvEPTP6tw==", "signatures": [{"sig": "MEQCIGAnBb+qO0Wi47HssL0xUwOFv1mblV1oa1NSNfPMS0PGAiB3BKTy83XUmj1T7yTw3nLOPn0nVjZTx7a8AqbtOHtoUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.0_1689861589657_0.7520739649940247", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e577dab8a565827a5b7500ebdc35322b624bf34f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-q2A3gyhcerhmzaey8xmJKKYsHT+FU3jxFFJtu7pxFeKvwMalTwDmAe/Hrj2V+oXd/s+sJyoUr414tf0uLowlqw==", "signatures": [{"sig": "MEQCICnNOCRDBZXMEhoWjJPqU36PPGH4DdgCuJ2FpLISwKiGAiAKKS/ZkxU7byIP9GUohaTfbBb712yoQvuNQf1s6tC9Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.1_1690221096673_0.3528958227242711", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3fcdf06a65189d6f887e95b7d556ddbf8dc258f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-+IMhoH2tZgwKkdUw/G5oai3r+TFv7eSp0UdVlU393S8778WKj4fMLtMT+Q0i2/Uy04FBH9O0hKvUDk1allXcww==", "signatures": [{"sig": "MEUCIQCIFH9yxzoO3to+YcwoFsrVxnuDcfGjl2z8ESb7nNd0GgIgNIm6yv52RjbnWH6ioFJTXLJPj8/5Lo+gtAadXRK51S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5942}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.2_1691594089492_0.8703978876828264", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2c7722d2a5c01839eaf31518c6ff96d408e447aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-g/21plo58sfteWjaO0ZNVb+uEOkJNjAaHhbejrnBmu011l/eNDScmkbjCC3l4FKb10ViaGU4aOkFznSu2zRHgA==", "signatures": [{"sig": "MEQCIBucv0t3gVgehV4PdiknM9Tuzy4ycxE1Hz1JPdgVZVBcAiAKi44syeRABl1X7DlLQ+sl6b1AjiExHrau9QfU2CEVqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6129}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.22.11_1692882517741_0.8385840477291846", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7f7c7b008f63b004a13acaaa7871867548dd5930", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-a4dSMU9/bEwBXnkd7yMyCxp194nfQsYxUmt0XZ0NrvhFXdThtVW37luh+lKpqfAaj9bUf8IbdcQiEymljXuRtA==", "signatures": [{"sig": "MEQCIC2AarjyWxcmI6er869w1rIFFOTM9xBj2ealI2Hn552HAiBMr1oLnw4m7L3BwTv0UU95szb2Ec7XGwLFGdx0tlTxtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5984}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.3_1695740205687_0.5526633077425465", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "72fb8ed1fcc2ea444a80163b2cd68cc9c3bc10b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-JIUWVj4KpbpxxOXKftDMP2/jWT68M8I/Y4wjDpBxjfIfGvjM8/TYnCj+dH6dtcdOdEO40FZxc8hlob+WAB60fw==", "signatures": [{"sig": "MEUCIA5S6L8No6uytAuOR2qGvotppmE2S+q+CiogTcyrHIr3AiEAp5VwukiEDVwNnqNdx2S+GYWyhlva+dFwhswPn3xints=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5984}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.4_1697076371089_0.44183514152865566", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "82625924da9ed5fb11a428efb02e43bc9a3ab13e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-vTG+cTGxPFou12Rj7ll+eD5yWeNl5/8xvQvF08y5Gv3v4mZQoyFf8/n9zg4q5vvCWt5jmgymfzMAldO7orBn7A==", "signatures": [{"sig": "MEYCIQDkAKnLgefPhAiwIXOVBNwKNeKST2zYUr+AQ5QJAY8p9QIhAOSAouMY50xcohenriXIIqW0DcXMOdfACSeW6MjNCtWI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6209}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.23.3_1699513429230_0.37354616071241376", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c7629e7254011ac3630d47d7f34ddd40ca535143", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-V6jIbLhdJK86MaLh4Jpghi8ho5fGzt3imHOBu/x0jlBaPYqDoWz4RDXjmMOfnh+JWNaQleEAByZLV0QzBT4YQQ==", "signatures": [{"sig": "MEUCIE8Ll0LMJ2TaF+/ju4ttoGZjO4grW6LOcQ3pRxyzZUCFAiEA+KJ9WiDOc6i8IVa2GntWKWAajY4lPNnxj1TOsXLs8zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6215}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.23.4_1700490126568_0.9386403465562165", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "db699ed93293513794a6d735a492a8c3ead90eae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-IHd/mJDZuATmmMzgGTgARKc8ZDTYoQAH6/yRXOiIMVYXSPOcNfaQNSC2xNhfV0S69RN8MiN2lybzwjsPZ5madQ==", "signatures": [{"sig": "MEQCICsjSLReO93FMvZS1hLQszwGsYr2lTXdJHJyXmIqE29zAiAXLkBGvkbmfS6dN1caDDWaGVNMz7qHPk78M9rIfBMkrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6103}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.5_1702307914834_0.5559600038276262", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f7cb99e632615d004f8533c3a2699db9aecb770f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-+srxCSQm9Wu4lrNfWumgBXJiuZUq1poRLPoCo5DYbbrnlcbzXAb/nRZjvwAeSES6f8HHiyFHjAVemXlmYN2f4w==", "signatures": [{"sig": "MEUCIAvHuyo+BgTVygJeJYTlf+Rdc4W3/i2BCcb1/AzXqlSRAiEA4w+LSr6u74PjGsZPgu411eHoDCyL3m/wCUEaR2fjxRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6103}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.6_1706285635598_0.09551107666327474", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a62dc49b408721adfdce0773039db646f251cef3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-dAARrQKOnVZNCHFy0ByvVfW8zv87EcxMcqZLDsAwPaD4S9vhg4niZlAU1SrGzrXpezNDU1vNFJiciH3UWJ+DRg==", "signatures": [{"sig": "MEYCIQCaoRW/JC5utnHcluJcyMuGLdBA5+spW1HP5FN9S/Ue1QIhAPbWKY3rrPuDixm7/p47ZCfq9bu6/1hvza4tfR/O/YAU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6103}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.7_1709129084783_0.15888598010037014", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a5a49959201970dd09a5fca856cb651e44439dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-av2gdSTyXcJVdI+8aFZsCAtR29xJt0S5tas+Ef8NvBNmD1a+N/3ecMLeMBgfcK+xzsjdLDT6oHt+DFPyeqUbDA==", "signatures": [{"sig": "MEYCIQCsKNeayAOZMG7nGm7152IC5piovqro6XWnd5iN7iBBgwIhAJ9aSCxWitIkxJ8lAwEvhl9Q5m7AHEIOTcaThKQktnBT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6275}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.24.1_1710841714778_0.5697437667820375", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e5769875fef2c2dd6e9f4fc345ef11f48805fedb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-zVZzeqctpr68wMzo1Ig7eR+l7PeMOZUHkTm3m41wQrQBnARMVh7o5QAYiua9qtFUWZbknutedpfnEY+OBjakGA==", "signatures": [{"sig": "MEYCIQDaChNY2hlpiPmqZ7NVDXmxkmnMR5ZqcF0NtBxDRbpOtQIhALqkPxsU5bHKbj9SKQHN/pMu+LNNFcI8MWWLYP5Unk5Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6027}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.8_1712236785212_0.6864381608062902", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b477177761d56b15a4ba42a83be31cf72d757acf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-vpq8SSLRTBLOHUZHSnBqVo0AKX3PBaoPs2vVzYVWslXDTDIpwAcCDtfhUcHSQQoYoUvcFPTdC8TZYXu9ZnLT/w==", "signatures": [{"sig": "MEYCIQD7ArhTViHCImhbaMPtT2lkHJZHBTkVeDiF+TeBo3GmRwIhAKXrZYJckAPAk8H3k3PPfORkE1XaiRSbacmDObO0ttk1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72196}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.24.6_1716553466393_0.5158866765633474", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ba781591b83dd9d6dc09cf57155ae9b4d170844e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-zSnBANZ5+v95FT6Wg7wtPtxPLCnWAa9yVclkKmXRrXtgVWIU1KWDqh9EkdE84QFwah6+PSLkAEKoZ6/VOK8TNw==", "signatures": [{"sig": "MEYCIQDx6gXfotDisRdEHL2W/on15lR16xxT1HtUmkGLoavzrQIhAKzexWCXshE4tfwlJxBUFChGXbA952ER5P2t7e1vacBn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72258}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.9_1717423450379_0.5463981795027579", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "efd8ab92f1d0a7dece341edd7af4961f00f374cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-hyWLw53OCf599/JlHFWmoKuSMsRK/NlQ6SS2QpWAdlNlj1D5aF05IQCfKOVCWZM+bL1u/F5C3JVIZO0h9F/Y9w==", "signatures": [{"sig": "MEQCID8pfCO7qc79ZfkRghssRUUy+yht3Na5uyKQl6tm1mJmAiAZSwdyHhpTL9bFFOnoVqElDUacdCn8gKybgZmuwhdHgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72266}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.10_1717499997215_0.24988084527258536", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4d8b95e3bae2b037673091aa09cd33fecd6419f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-sc3X26PhZQDb3JhORmakcbvkeInvxz+A8oda99lj7J60QRuPZvNAk9wQlTBS1ZynelDrDmTU4pw1tyc5d5ZMUg==", "signatures": [{"sig": "MEQCIFV/6AG0WzLcYc6/I/PoHiyFpi72+KBofB7Bsp9brfBoAiAsOQb/9uyU8cfvmpCDR3ffvlQ525RAcKKHWAVwDcdZzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72192}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.24.7_1717593316862_0.9510111331779627", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4667254f53daea64ba24ad0d549d0f1f42040c64", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-DkV0gBk70leXPGFMTOVROMUu9r+aemX0SqF0LaWzzYHKghKINdQcm3Py5CxZVUh3GU3DV1hIK/+NnHknfCxMgw==", "signatures": [{"sig": "MEQCIGJYJX+Z8r3L7hwqSBXKP0nMBFBDV9F2khApkf5pCctrAiBTTpolE0RSonpK2xJ2WMeXB8VZcb2R1YzklfXhSBpJdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72155}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.11_1717751726570_0.5102035931653299", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "565b7c0dbd1296c69ff60fccd5457bb11a977da5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-HNd+GtpA8QhLsLvBmYpxk16DAIfJBKmmgjTxtzoYXOlC13yQXqLOVoDTuDGDsngo2zQRCP+YZe6KJreWE9FVVg==", "signatures": [{"sig": "MEQCIBEaieC5WyH1G5E+I5d3BxlH7qYmrlQoWPUYCkcFA3FXAiBasn5LNwQSFQtmb6c3I6hukn8Ix+nxMqNLydNPHHDRPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68951}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.12_1722015202614_0.4057206053732001", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "31905ab2cfa94dcf1b1f8ce66096720b2908e518", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-UvcLuual4h7/GfylKm2IAA3aph9rwvAM2XBA0uPKU3lca+Maai4jBjjEVUS568ld6kJcgbouuumCBhMd/Yz17w==", "signatures": [{"sig": "MEUCIGtsgGt0FeThIGIPN+rcl6OftEqPedHb6M3r/2wcmoJzAiEApZ2cjK2VkCSjKrUXUTt8DtZ5SW5g6X0Oh4N9Tmz4Q20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76760}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.25.7_1727882082089_0.7082436363497904", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f1edbe75b248cf44c70c8ca8ed3818a668753aaa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-gznWY+mr4ZQL/EWPcbBQUP3BXS5FwZp8RUOw06BaRn8tQLzN4XLIxXejpHN9Qo8x8jjBmAAKp6FoS51AgkSA/A==", "signatures": [{"sig": "MEUCICWAX8llxIN5NFjNM4ghno6uU5hrdNkxi7fV0MPXmzwAAiEAvWZWE7EYHEgJ3wdsJSOST/HTPb454QRI/q3FPWxGbAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77081}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.25.8_1728566708300_0.3898842962049387", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "23e917de63ed23c6600c5dd06d94669dce79f7b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-GCggjexbmSLaFhqsojeugBpeaRIgWNTcgKVq/0qIteFEqY2A+b9QidYadrWlnbWQUrW5fn+mCvf3tr7OeBFTyg==", "signatures": [{"sig": "MEYCIQDcCAxhxpOuyqG0G2neWdRWZbpyST8J93MV4gcg8sCA3AIhAIOWU5RlHwVHoQvnrT8JbNoXa527zctWTQwHvGGusbos", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6127}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.25.9_1729610460490_0.761864087358924", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "612e682b99dd098b327caf1ff180b15ee7d022f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-chrAMouwwOvJFNELDRCUuHPGUotTC/WLKAwuqzIpkgQYHcW1QGuPjHLu7hwYUm/Tq5pzOw6a7+dKIscZ/qolQA==", "signatures": [{"sig": "MEUCIQDDHPBl6CvoJuIr5C+6B/PxJlOBwfHC8BgP1Bb0RgrB6AIgLxwEp29E8uWkt9UkZvPVPNFFqnIDuDV4Zhl+Nk5w8Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.13_1729864444391_0.15419301319900214", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0f109d069054f364d922c7f746c69f83a44c53c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-xaOFdTch+tEH2adlxdklXIq6SSrs1gKaogjh4+b+i3l+6sQzcZC6cR2AM5HihmNBg2hpFSDbQsygvkLCHq+INg==", "signatures": [{"sig": "MEQCIEvfBXUv91yMIjrdWQTlDTWzX6tirqQrqHqBpE6ybGS0AiAU4LUd97gOooTmQl+Cg8HPcbGD0o1AMAdCWH8cOG1NXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.14_1733504035142_0.1959010734109592", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c0bc96222523fb1b60433be7ec6d7089849f4328", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-E/AaJ4YOqKV0n+kMEm4tG/4BeI2RQnSYwQlU2i0ppydYVTh3KW+s7MbleonB3WS3SsIPQzhV/c1/z8u6UOTiWw==", "signatures": [{"sig": "MEUCIG5UenjMziO5zk/hnzrBGBJl7UjmMNkFMCSsIB7Dtqk6AiEAqA6EfCucuWpU6iDXtgLJlY6CnwQftWmQutM10PWY9pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.15_1736529859889_0.10923859867289831", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4a3fe53d482cd25ef6f5ba7bdfbb48e925099ab2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-44asPlvFBm7ttpGQibndLLjjhRxpZLB+WvYPt3WJ6kSrkXeBTqzDD7fiA+4/HSoCSfbPVgiW+AX8luPd9nBwfg==", "signatures": [{"sig": "MEUCICh+6wJJV7OMMc2RInRcdgVcqVUsJJGCLvhC55Zl2L/AAiEApdlQnAeZ9O3ksZnZD6F94b9ke+xZSN9V4+Mf9qR3Lcw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.16_1739534336692_0.09368091511527998", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "39bc38cab1e3ce3fde030ed204a1ae68f0282412", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-HvhXLS2wy6XuclW65/2yirLbtw66I/IkocMTF/cE2AUNHgr+jr1h/wkoowi62HFV3g+joCsVd9XhLBDRfv/ygg==", "signatures": [{"sig": "MEUCIQDvPQjhl2YHhryDl9DeQQutAothe5iQk7T9Fs3lu6XDBwIgQ7BfEo6mJVYJVCpdzqWT5uE03QkkcOVlMCgEyhI5uqU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6327}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-alpha.17_1741717488535_0.7103448238732337", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-dynamic-import", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4c78f35552ac0e06aa1f6e3c573d67695e8af5a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==", "signatures": [{"sig": "MEUCIQDbKHIdlySVl2OqGrOsC8QTMvxuTdtkkChWuQogfDAwmwIgSexd3Aj2nJ5iQfJtV49P++z2wvOwu30fq8HLNIqxDN0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6127}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_7.27.1_1746025726042_0.7645022806863047", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5db4921ca627e8f061194e2fb93dbb42d2b7990f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-qQx3ikjHoKYZP0EQwJzK/6CLUhtYNcReAS7iYBm+qjlFqgWse4yF/3cOWfgjM04p7Us1GcWthFMw+f0xY8nKKA==", "signatures": [{"sig": "MEYCIQDb9su35BvDprNeNaRj+kVFDTPphGn69Of1pqW0b0sl2AIhALoxgbBYOEQ6TQnyKsWPYAXQY+ldWumdRDquxsz+3GO+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6301}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-dynamic-import_8.0.0-beta.0_1748620258482_0.6975580784602675", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-dynamic-import", "version": "8.0.0-beta.1", "description": "Transform import() expressions", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-dynamic-import@8.0.0-beta.1", "dist": {"shasum": "b28468ebc7ca4b2eda2bc21378f8ad422941fbb3", "integrity": "sha512-kRjW4d89iKKtlzuuTzcBqotu4Xp9/3V21Cb4pCHqf5mrVpvaddboIKC6/JCd9QYCRP457/nTkQjJaKpDRclQWA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 6301, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD3bbUHTLRyeAtP/t8uqw0z/EUZKs9qWbAEOy0nUpn6hwIhAPn33fuGcedCMLwYiqxHghx9UTuwcWp4AJOmWaUylSpB"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-dynamic-import_8.0.0-beta.1_1751447051875_0.08466529702349335"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:14.511Z", "modified": "2025-07-02T09:04:12.260Z", "7.22.0": "2023-05-26T13:45:14.690Z", "7.22.1": "2023-05-26T16:34:50.953Z", "7.22.5": "2023-06-08T18:21:15.386Z", "8.0.0-alpha.0": "2023-07-20T13:59:49.815Z", "8.0.0-alpha.1": "2023-07-24T17:51:36.837Z", "8.0.0-alpha.2": "2023-08-09T15:14:49.690Z", "7.22.11": "2023-08-24T13:08:37.896Z", "8.0.0-alpha.3": "2023-09-26T14:56:45.896Z", "8.0.0-alpha.4": "2023-10-12T02:06:11.302Z", "7.23.3": "2023-11-09T07:03:49.602Z", "7.23.4": "2023-11-20T14:22:06.757Z", "8.0.0-alpha.5": "2023-12-11T15:18:35.044Z", "8.0.0-alpha.6": "2024-01-26T16:13:55.804Z", "8.0.0-alpha.7": "2024-02-28T14:04:44.959Z", "7.24.1": "2024-03-19T09:48:34.926Z", "8.0.0-alpha.8": "2024-04-04T13:19:45.348Z", "7.24.6": "2024-05-24T12:24:26.653Z", "8.0.0-alpha.9": "2024-06-03T14:04:10.538Z", "8.0.0-alpha.10": "2024-06-04T11:19:57.415Z", "7.24.7": "2024-06-05T13:15:17.072Z", "8.0.0-alpha.11": "2024-06-07T09:15:26.739Z", "8.0.0-alpha.12": "2024-07-26T17:33:22.835Z", "7.25.7": "2024-10-02T15:14:42.372Z", "7.25.8": "2024-10-10T13:25:08.538Z", "7.25.9": "2024-10-22T15:21:00.690Z", "8.0.0-alpha.13": "2024-10-25T13:54:04.589Z", "8.0.0-alpha.14": "2024-12-06T16:53:55.324Z", "8.0.0-alpha.15": "2025-01-10T17:24:20.166Z", "8.0.0-alpha.16": "2025-02-14T11:58:56.920Z", "8.0.0-alpha.17": "2025-03-11T18:24:48.724Z", "7.27.1": "2025-04-30T15:08:46.214Z", "8.0.0-beta.0": "2025-05-30T15:50:58.686Z", "8.0.0-beta.1": "2025-07-02T09:04:12.024Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "description": "Transform import() expressions", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}