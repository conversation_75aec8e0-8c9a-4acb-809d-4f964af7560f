{"_id": "@babel/plugin-transform-logical-assignment-operators", "_rev": "33-bc27e3ea2f7002c575a454746560d190", "name": "@babel/plugin-transform-logical-assignment-operators", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "e7050803069f3fca2ed35dc1c16bd1d1b51a9ec7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-tSYLi4c8H5K1iSCLCjA4xaYgw+zQEl7WUP9YI2WpwXkmryDC7+Pu/uD43XQos7Sm326OIC6Yf+6LuWjBs8JJKQ==", "signatures": [{"sig": "MEYCIQCyxSiLtZJs6JHBQ/0Mc2svRdvuQYm5Qa6XnPHJgiko3gIhAOk1OypqEkQj6F1SOvSO9La2eIzmLZN4PtD1YHgSDRnd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8703}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.22.0_1685108715605_0.9473535783634408", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "9e021455810f33b0baccb82fb759b194f5dc36f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-CbayIfOw4av2v/HYZEsH+Klks3NC2/MFIR3QR8gnpGNNPEaq2fdlVCRYG/paKs7/5hvBLQ+H70pGWOHtlNEWNA==", "signatures": [{"sig": "MEUCIQCplZcogP4aIqNPWRVurJ6uWZSVlHLfONeEd9t5yN8v5QIgewtJ3DV+J5UWxZKmxa4LP4GsdiCrCTCT3uqHZAaLdwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8683}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.22.3_1685182255801_0.6949743112046505", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "66ae5f068fd5a9a5dc570df16f56c2a8462a9d6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-MQQOUW1KL8X0cDWfbwYP+TbVbZm16QmQXJQ+vndPtH/BoO0lOKpVoEDMI7+PskYxH+IiE0tS8xZye0qr1lGzSA==", "signatures": [{"sig": "MEUCIBBvsYKwcYDmWya2lHeOxEQvfEkKjneF3RDf7RSHDYciAiEA1hh0nkI9ysafqzI5nBD/2EW+XJ2BCLZ1cTkmVnKGcqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8683}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.22.5_1686248477159_0.4029285820372501", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "81506276ea749cf66fc3f5f07411de235c279e53", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-dysW3nyDvLb9FH7ZD+ejOeb84XiPZcBZXgWz4TyuCmWU91Dr1cWXqhE6KBfE21EpAXdCdjeo6uE/ddNaDo/vlw==", "signatures": [{"sig": "MEYCIQC64yixhSMyZYTfDwve8gY9E8Z5DvtHjDwvbEiIotB7sAIhAKBWv1gGjKicL5DDjtWM5VZ63YI7TYxslRIIp5mp+jU2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.0_1689861591591_0.9598945505002892", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "69ac73d95b52b0ef807bcc671a02db54c671ae46", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-+ZI8JaOAll3nm/mDJ9fuvVs9PHSVZ0j4S5D1JO3s+9OSRSbtog9xoj2lAiYic6lisFkiIgFD56WB+4kLTk+3cg==", "signatures": [{"sig": "MEUCIEkpynfhzfvZQAz7/2JzK7aGA1Yh2F9QlA6CDKK3+Q7dAiEAlleYXDo9Mv8k7Y+/yZPAFOhffUHTzFkz+hGBlFPsNyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8611}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.1_1690221106943_0.8610565614164596", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "278203e9dd3ede9b3bc47578d6fbae123f095ca8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-bY9BFc1M7nMrFVOdA2IJv5FF0WKpU5PgmkXuz7Ju1/DPl5SCzEjbE+kgMPAmRVvwnFkmgf2WIxEqSzNQ2WtGOA==", "signatures": [{"sig": "MEQCICfHV5MvC4A0Lz1XuwlyKT4Er+Tn9LnnWeYg+rlt915wAiAk0ToHpQIP7zDmn/ipTlLinoaMzuzJu/dfe4tJo88+EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8332}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.2_1691594091880_0.8115840159487586", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "24c522a61688bde045b7d9bc3c2597a4d948fc9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-qQwRTP4+6xFCDV5k7gZBF3C31K34ut0tbEcTKxlX/0KXxm9GLcO14p570aWxFvVzx6QAfPgq7gaeIHXJC8LswQ==", "signatures": [{"sig": "MEYCIQCdoJ93Lbr2BimmOaKdG6km2e1qrWA22x9lEOqQlc9PPwIhAOFm5xRe5lfvQWtMzh3y/sBKe68a0Z2gHTNmaEj+t9Ms", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8506}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.11"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.22.11_1692882518049_0.10220486023006625", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "3a9a7271a9aa0beefa9a6adbfc990fb76f262d38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-Fpk7OvoHC2nTr1NtuQdMuKS/XPuy7nuo+ndjymncC1jVLE28WyR3Zg/R4/dyE3xuOJVF0zpPMt0jbA9r9GZjBQ==", "signatures": [{"sig": "MEUCIQD/E2PS9eqTN0J5zbp5VPUkjzc7PqStDFHf1E/pecRY7AIgDgDJboV6dGdHnjNlgynQlTyJo6F9D76tG7QMPHKGGSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8374}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.3_1695740208472_0.40869343499679434", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "1d4ff21b632a5f8e8f8e18d898ca206181623194", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-sKNiGIhlqkTAn5SwfxIzPQbiwNIhzDH7rK74Q0CG7buEvHyLk0CV42t15aYOSBIfNqvzk6BsRFmdhLRAH9C+AA==", "signatures": [{"sig": "MEQCIG67MpRXZHjv6m8mfx6uU+z/AEFyFZgO4Jk+08hgV3R6AiAB1hnSTq2CxfNCJJGLoc2m9vYaVV5GdaIdpDs9pTAOGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8374}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.4_1697076374073_0.23840571899017604", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "3a406d6083feb9487083bca6d2334a3c9b6c4808", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-+pD5ZbxofyOygEp+zZAfujY2ShNCXRpDRIPOiBmTO693hhyOEteZgl876Xs9SAHPQpcV0vz8LvA/T+w8AzyX8A==", "signatures": [{"sig": "MEUCIEbB+bS+lWYgdbo4Ga+a1JUEjC5D9gJKfdfAaUTlQSgRAiEAn8tzqvvxsfaP1TT/RngNRD50MEDqQRwSTUmXlIs8i1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8584}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.23.3_1699513431836_0.061022848601096635", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "e599f82c51d55fac725f62ce55d3a0886279ecb5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-Mc/ALf1rmZTP4JKKEhUwiORU+vcfarFVLfcFiolKUo6sewoxSEgl36ak5t+4WamRsNr6nzjZXQjM35WsU+9vbg==", "signatures": [{"sig": "MEYCIQCD3+SwlgcWAF/LSvc+vB2vxBIuJluONoBtI5xWFKmxWAIhALfUgf6nq9/WMdqiTqJv0+PyaL0L54v8RW+PgrPEKnop", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8590}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.23.4_1700490127174_0.234433136602064", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "8054e33cee9de858a08d36c190413916426cef8a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-mIJZ2rrQq0dWicAAGhOupAuIUCo4RuW91nPMxr4v2DDvRZ2HmsVM1eK1Z7S0Aa0C00KNMSYKXvg/tOEJn7/FCw==", "signatures": [{"sig": "MEUCIESLKhBLlWJIJJJDnyaQaB4+K5qjGmTXgMNpChPukrS0AiEA4F/doMRmHCtIlMYM3BxYoPTW+NjGJHb2Jz1ue4d7VPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8493}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.5_1702307917969_0.42320738976172767", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "ec0a5cda7dbab1e2ed409d157c0c76216d451e65", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-xl8FV41ATCMcvoDPNOnezduTGOQjrq32UqV+dyOZjtXHcE970YOAH+Bt+7PaaGR2svLJEh4GuOzcUuqVTAJ39g==", "signatures": [{"sig": "MEYCIQDTa8Adc/FeSbHixdS8T71wAeB2BHOWvMGWPHYJS1cpUQIhAPIlSpwWc+xq6fLi2gkVO0xqv9pg+oWPmfa92NTMFZfG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8493}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.6_1706285637776_0.13981639147712133", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "9f6f4de74e3d0e99f9d089c21e819d8007fd1804", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-hjoOASquPwuieH18LuN6TabhlkVyj6miUKvi6419ctiGYIYY7yCooq6NjturOl5LKuiu1N63jZg0HPe91nR28g==", "signatures": [{"sig": "MEYCIQCCG76O6C+up1S1ZD9Q33viS5sNaa7DukYhvVGJxWde5QIhAN4DqWqdInijFobaMGEwJQAZAOVm0ZriG8C9CHAIJaeC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8493}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.7_1709129087573_0.3109730641153623", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "719d8aded1aa94b8fb34e3a785ae8518e24cfa40", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-OhN6J4Bpz+hIBqItTeWJujDOfNP+unqv/NJgyhlpSqgBTPm37KkMmZV6SYcOj+pnDbdcl1qRGV/ZiIjX9Iy34w==", "signatures": [{"sig": "MEQCIBYwh9cXaJDO+uvnP/ogHThHEtZUQu5Sj27EOoo4QOTzAiBYgW/tIDtMHXAdraDwFT8fFmC59Jmqmoz2R7c/wqhMXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8650}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.24.1_1710841719834_0.15612410912062868", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "3ead71aa8da84da3d7b5043a607363b29e125acb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-O29knrK04DzHJ4amFb+Gkyj8gCUXAYBI69X13+IuJLrjKDnvkRsPp8sLNKJB1kC1aK6ImJIYWN1+3Y3stHIpGw==", "signatures": [{"sig": "MEUCIQDq2NJ0KK98SQQHM4XdN/JJA2v8X7mp+OTy8AT8VCRUmQIgFJwJnDbrWYwgr1i0fvQPu3cZvfeXmlndFh2qoYxYGwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8417}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.8_1712236787039_0.7796577731310206", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "9cc7baa5629866566562c159dc1eae7569810f33", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-EKaWvnezBCMkRIHxMJSIIylzhqK09YpiJtDbr2wsXTwnO0TxyjMUkaw4RlFIZMIS0iDj0KyIg7H7XCguHu/YDA==", "signatures": [{"sig": "MEQCIGBVKjpEFS4uSL6+hAIUC58cA5h2R3+2fA/m6Q+iCiEUAiAtkg8ce7smh/QSMEMESZq7FT/vfbq6WSM6kCI7pYSguA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74636}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.24.6_1716553468556_0.7029090409107199", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "cb49ad8eea908d364a7ed3f7afd2abcabbbcfd28", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-iTxAyKwuhNnyeni9vV/tmXQy5CuBJcLStpQIyIlZby88srYck5GZb6bORXNG7Uj+mrYCk4U9TPbs7jbf1gwV0A==", "signatures": [{"sig": "MEYCIQDvP9v7vvvJHaiKphi2LZahiNZpQND0/ByiPQZe7+1VKQIhAOK4Q+jJYUT5i1KCWgru9Blaunbbe9Wdc3N5kjoxLnFW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74680}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.9_1717423451831_0.11073576550222541", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "f59d6d7b4e3623c0e0587fbcab2a85ff69dc899a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-6HYILb8DH30AljaMVGOjemZQGwwGH1HnVJmTEMtjyOLvLjOVbIPtIRdGkD5zmrcLmWm2C/ZTGwig1t3ZBe3KNg==", "signatures": [{"sig": "MEUCIQC4Cqby36WVP/sYlJn0WpbC9r4sTNGOa4kD/CFe6JHoDwIgcGadMMPCujQR5rVTW5JNV2fJCzr1yEVllFhK94XVAjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74688}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.10_1717500002567_0.3453266412766731", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "a58fb6eda16c9dc8f9ff1c7b1ba6deb7f4694cb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-4D2tpwlQ1odXmTEIFWy9ELJcZHqrStlzK/dAOWYyxX3zT0iXQB6banjgeOJQXzEc4S0E0a5A+hahxPaEFYftsw==", "signatures": [{"sig": "MEUCIQDkSd9/PIt2fjm3lgID+Kxeqvviw1doOCV1w7xoYlCsKwIgAJW9WLfYYIGMUFfCHFgGhwY+mQHAlo/nMkSbfEYxdmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74632}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.24.7_1717593319176_0.7034980176323116", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "bb7e697352b5c71d6bff5609cb5f2ea89fae2c77", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-W1gzNjRrxcoXRUBxonokp8kEIguHhaNga3KxoecSralLVw7oZaa3ApKmRNqfKWZjlWN93FgALuo3mzdstsjt8g==", "signatures": [{"sig": "MEUCIB5C7VEKgXi/4DYikbfnRprdKHYm0BbytrEROfFlFiZsAiEA74V3B3t5q1bHCzIdjdcBPF6TxtAd+bsE/4SPmo7X5fM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74577}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.11_1717751728650_0.7083717054383847", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "53119bf7e8cab0af621065c7dd722212f65d403f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-agpRPQ6N4zipacUTyKSIBo3VUyuYGCVck8UD+sghvSAGravQhBmumgrfWONNMPGzjzYfRs2vgor8Lcfu6cUe2g==", "signatures": [{"sig": "MEYCIQDoAsPlG9BS80BSIjJYb6116k22XP/d+mor5UPDu0MuwAIhAJXhV3B4gHmhFBrEvolnanahomEzw4KtWKGuQeWg3Bre", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71373}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.12_1722015205203_0.22526667703325876", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "93847feb513a1f191c5f5d903d991a0ee24fe99b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-iImzbA55BjiovLyG2bggWS+V+OLkaBorNvc/yJoeeDQGztknRnDdYfp2d/UPmunZYEnZi6Lg8QcTmNMHOB0lGA==", "signatures": [{"sig": "MEUCIH0JXBV9yCer16/N3z3RpHgPBANiXdYvSQEu47Lh6MhHAiEAtK+Ngs3zF6dVVYPuJhBd2YWL6xd6ldgILESlzl7zWmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79170}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.25.7_1727882085043_0.257932475410944", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "01868ff92daa9e525b4c7902aa51979082a05710", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-f5W0AhSbbI+yY6VakT04jmxdxz+WsID0neG7+kQZbCOjuyJNdL5Nn4WIBm4hRpKnUcO9lP0eipUhFN12JpoH8g==", "signatures": [{"sig": "MEQCIHOl01YMDwQ/Z56XbsMk0S9PUZURN6Ak9NVQcwJOTBjcAiAsTNKRSDaoL0yspnmVBhglRVt6yYy1suAqyvqf0fPfjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79458}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.8"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.25.8_1728566709979_0.5140684457458968", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "b19441a8c39a2fda0902900b306ea05ae1055db7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-wI4wRAzGko551Y8eVf6iOY9EouIDTtPb0ByZx+ktDGHwv6bHFimrgJM/2T021txPZ2s4c7bqvHbd+vXG6K948Q==", "signatures": [{"sig": "MEQCIGd6b4Afnb100ZvQcC6/DZlQi5HZI7nLB+7PKXBgrcqbAiATonEBsV2Xe0O8C9skcybpmCSPx39lGT8jK09DejbaWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8472}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.25.9_1729610463396_0.9106269976033505", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "92e257f7a42c3e000ba4207f59b3d56ac34fa7d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-0xZFRuWiZFDa4VlwjQeZchHD51I77YUKQp/WwsSTfXlAY1oHyh+PpmqhXXfwYFiHq8rdgzUWVBcJ6milsIrnZA==", "signatures": [{"sig": "MEQCICROXFhPhaE98Qv9yrJwmUMheKAseZiEChnjisoUfulZAiBtLxaLXHQxS47oVwIFVYillfCagotVNFeVR/TeywhMWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.13_1729864446792_0.46311846883744745", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "f8b5aed273dab38c5aed5ec45d0c61c27d305b16", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-B7+7o1MnrDj2ZMydPlyYg7+ARYwUGY4xZAfBqwqinxixSEP35kqB5Qh618zaMpbd6+rbsfsURhiC/WBEjLPfUA==", "signatures": [{"sig": "MEYCIQC2fJjaQtNiHBgpnMtFwoWIaOgbzrev//Nw3YiOtfCLHQIhAO4NDk6rsgTfx/rAApFsdtHgyPlRxTUOQPTI32vF5e8e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.14_1733504037303_0.1754453404265739", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "d61658031d6740c0ddb22af1a17445fc5cf09ca5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Q4Fd5FVn/cF1sG8I01LXVfIH/u3b3XX0QjfezPPPJyWt5znLv2DhF9PGnSXS7J2xTdgR2p1aE+Z77ySlJaySJA==", "signatures": [{"sig": "MEQCIGbAURtod4VpIL9VKfJA0k6VUvbKCIJCzQRGNfvsufbGAiAfUOZViZpYYLGFULhuaQId+dzKKizETk5B3A6wCAPCzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.15_1736529863304_0.742141497385632", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "740c90ca9db311ef5b56b30b1d40fde4e4aa3858", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-i21m23tETN0ey0jMeX8iv2FKaQqhUd/G1/YP2znSD7o/BzNGUs22BT+wZOBuFPAx6bkoeLrw+5KLy49KLomGIg==", "signatures": [{"sig": "MEQCID1jBojrvEHaCUaAewb2rVgUOT5IJ3BQFQu9eBXp/c8hAiBOVtQraz1XDgL7fHcxRdIeylDO/NBBQLPPspnkA4IHBw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.16_1739534339332_0.6214040365761839", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "c0077e06cc55156adfafd81afd8b1935dfc508ea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-aLXX85zzqcV+7pQ8Mk/9hpTIO6NVV1PsIGfoXygJA41fnHRyrakZn87q9YJZk8ZlfV8N5c9m90PLF5AvEaT/uQ==", "signatures": [{"sig": "MEUCIDcTPtoTS/NMFJvbTrQay/wi1WVnmYFBssckttWnmD5tAiEAxt22n0zfU+LUQrCRgskZFFExQIccFAlE8Y/M8XLYdV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-alpha.17_1741717490602_0.8991588132021151", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "890cb20e0270e0e5bebe3f025b434841c32d5baa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==", "signatures": [{"sig": "MEQCIDADA7mVmkYWPA5rRMavgy+leIlFQl9+JomyDSUS+o2JAiAnb/LP9xV4wp5/TNRrpJl1ugLgFD/bwBB07CKyztgaFA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8472}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_7.27.1_1746025729567_0.5551030106373465", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "dist": {"shasum": "5fd793293a286459a746affca399a7b0ec732c4d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-BLhUVQaiuOIfotjVZXgDCPv3E2FSQo7V8a3FzDsjhedVtCigSVqaGNIBbSx4ky2XQibo7+nrb9+UYcNTPDkKvQ==", "signatures": [{"sig": "MEQCIH/6wJ62AY0S7evgDPDKoH790NX+0HTGtTtWB9UcsMEUAiBfYHHF8+MiCNjSseg1oJel6vt4q8V/ToBbEEnhbYPy5A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8650}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-beta.0_1748620260985_0.6058575346750499", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-logical-assignment-operators", "version": "8.0.0-beta.1", "description": "Transforms logical assignment operators into short-circuited assignments", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-transform-nullish-coalescing-operator": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-logical-assignment-operators@8.0.0-beta.1", "dist": {"shasum": "220c8e5ff348817ddb9e656a0d82d61daff69491", "integrity": "sha512-IVjqc93GH0sujETOGoeE8+y9WcIrYCOybi3Act1ff2lmhkUXWV0xDScVzMhPPxjct9DHRJfpLCW1fJn2fwO/Qg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 8650, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG9vc8WQrjICXkVDiQEYfhxDd1PizdI4wIcbuAHUhJ7QAiEA9ssymEgQUbZZMoii54/j85bVOWOQ58xBh+M65W/UoHo="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-logical-assignment-operators_8.0.0-beta.1_1751447053999_0.981523889208811"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:15.552Z", "modified": "2025-07-02T09:04:14.398Z", "7.22.0": "2023-05-26T13:45:15.797Z", "7.22.3": "2023-05-27T10:10:56.005Z", "7.22.5": "2023-06-08T18:21:17.310Z", "8.0.0-alpha.0": "2023-07-20T13:59:51.777Z", "8.0.0-alpha.1": "2023-07-24T17:51:47.177Z", "8.0.0-alpha.2": "2023-08-09T15:14:52.077Z", "7.22.11": "2023-08-24T13:08:38.250Z", "8.0.0-alpha.3": "2023-09-26T14:56:48.666Z", "8.0.0-alpha.4": "2023-10-12T02:06:14.270Z", "7.23.3": "2023-11-09T07:03:52.110Z", "7.23.4": "2023-11-20T14:22:07.379Z", "8.0.0-alpha.5": "2023-12-11T15:18:38.192Z", "8.0.0-alpha.6": "2024-01-26T16:13:58.016Z", "8.0.0-alpha.7": "2024-02-28T14:04:47.749Z", "7.24.1": "2024-03-19T09:48:39.987Z", "8.0.0-alpha.8": "2024-04-04T13:19:47.212Z", "7.24.6": "2024-05-24T12:24:28.702Z", "8.0.0-alpha.9": "2024-06-03T14:04:12.004Z", "8.0.0-alpha.10": "2024-06-04T11:20:02.759Z", "7.24.7": "2024-06-05T13:15:19.355Z", "8.0.0-alpha.11": "2024-06-07T09:15:28.803Z", "8.0.0-alpha.12": "2024-07-26T17:33:25.392Z", "7.25.7": "2024-10-02T15:14:45.257Z", "7.25.8": "2024-10-10T13:25:10.143Z", "7.25.9": "2024-10-22T15:21:03.545Z", "8.0.0-alpha.13": "2024-10-25T13:54:07.017Z", "8.0.0-alpha.14": "2024-12-06T16:53:57.500Z", "8.0.0-alpha.15": "2025-01-10T17:24:23.575Z", "8.0.0-alpha.16": "2025-02-14T11:58:59.520Z", "8.0.0-alpha.17": "2025-03-11T18:24:50.783Z", "7.27.1": "2025-04-30T15:08:49.742Z", "8.0.0-beta.0": "2025-05-30T15:51:01.186Z", "8.0.0-beta.1": "2025-07-02T09:04:14.153Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "description": "Transforms logical assignment operators into short-circuited assignments", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}