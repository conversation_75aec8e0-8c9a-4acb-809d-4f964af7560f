{"_id": "@humanfs/node", "_rev": "16-e39d57fc2986d54e824898bcbe081925", "name": "@humanfs/node", "dist-tags": {"latest": "0.16.6"}, "versions": {"0.7.0": {"name": "@humanfs/node", "version": "0.7.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.7.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "3a4dfca8c31000ff4db1d229341712e03f462d8f", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-I9ZANUrlOpQ0SYNwVyYbBDT0QblYhvXeWqbee72xi8WVQrSttZQEoaWyr4CZyGGzHh09lsw3pDMCSBNcQuyLNA==", "signatures": [{"sig": "MEQCIBu0/nLA50AfIN86HZRlRvBLnOF3fQg56Oht1i+DD8VeAiB37M65YxIwUg7Bx4dbgccwUspZbbx+IvZYwpNTzUjEOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32060}, "type": "module", "types": "dist/node-hfs.d.ts", "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "ac91f0ea54f2f4b5644ff5383fa7cb8bbeda65f3", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.6.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.6.0", "@humanfs/types": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.7.0_1706574876444_0.15327222413859842", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "@humanfs/node", "version": "0.7.1", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.7.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "0aab612158896c8e463e56505acd4e7c8ac41864", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.7.1.tgz", "fileCount": 5, "integrity": "sha512-JvIPDgZ1zsl0VtxuqCGgqQlu9yQEo4qm304q3ItnVT8Gk7nZLuGmg/G3YhJhlaxtIyPJp4Xl5p4mj72h1FbPvA==", "signatures": [{"sig": "MEUCIQDPYdDIVBqZMRdyLavDz1MTuwYsBBBDqLFOQAwXiyOpHgIgVJo80oHdjt0zhGcguO0nFksfbGwb/Aqc7lr4wbAnDl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32104}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "fecc5744622a96af6d7689c6338db49241191ccf", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.7.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.6.1", "@humanfs/types": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.7.1_1706745091635_0.7484536407601792", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "@humanfs/node", "version": "0.8.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.8.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "907fe962b9bb22cc1087a312876a8c72e1fe1e05", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-aMpfAIXuKYJe3/GZ4e43FTM82pF662SQmCxLigYsBlMzb9r4+yPUOCw0bTHDwUsxxLp4SjDAnG45E85oGzmRGQ==", "signatures": [{"sig": "MEQCIC5icncjEYgq0+F4VvaEPRTW3UlB9w8sZThQvFQAbcmwAiBSJKkkjgSbWXMlzD/Hpc8GMX8bAvjmzw1My5ePYIJKDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32430}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "1afb8a9b48ea6eb0e5fe94f88951d60e98b80891", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.8.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.7.0", "@humanfs/types": "^0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.8.0_1707264180402_0.9258291589586298", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "@humanfs/node", "version": "0.9.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.9.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "f432ec2bf71282eaab93b36776b0f4fb444ea8bc", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.9.0.tgz", "fileCount": 5, "integrity": "sha512-BRVloDI2ptENDrTn63BQbcsu56kcRpDcMB1TUM3oua7W3JM9YGsVRYCfX0lhHLTYdJiZ3kGjz5GL765BPTndeg==", "signatures": [{"sig": "MEQCIEUkW5+t8mj1/PPSlKjIajnXtHhCMgM1kpjMIZ+bzHeCAiALzcHFSbVGao9Xg73iKi+k+BI3WZEAl25vixCmS7cleA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33555}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "98ad9217bec071f0530de68742771591e004e2f8", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.9.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.8.0", "@humanfs/types": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.9.0_1707434228991_0.9034902981723949", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "@humanfs/node", "version": "0.10.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.10.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "90a760b91d03f73720ce1d781715e9a13f448f78", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.10.0.tgz", "fileCount": 5, "integrity": "sha512-NvArtsdsKTeYSlZhbQmFW1X0RVhjAFa7mZYcg60dDBThvSokrpt7xsVJmvLIMlABg481M+MRaaM7yhtyr8bbfQ==", "signatures": [{"sig": "MEYCIQDc1L3ap1bQ50maieRIhx+F+4VeoDOga5WiKGvJeqcljAIhAPcCLuoFKfLOLqrkLjGsQCVi514gw+kbC+Rz8EZ9IcvP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35433}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "9ef50654fa4cab2bf4f3104dec49189972df2fdc", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.10.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.9.0", "@humanfs/types": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.10.0_1707513475086_0.10709146088347077", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "@humanfs/node", "version": "0.11.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.11.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "cb5c1e0db42f801ef6c21373f043837d3a2e8525", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.11.0.tgz", "fileCount": 5, "integrity": "sha512-PgjHbg1AswZzE9GeUcTLCAfZXVLTpjlZozjmzPMEl8gLAZrga6IpZ0zTWnCFMu0lNTE5YADaGpPqVXNSsOdMxg==", "signatures": [{"sig": "MEYCIQCMP1ll5d1WXJcHuAB4TwgX1Q36OrxvYTVA7j7D9OtnrgIhAPCGpLlHZqQAl27koMBpWf996xjquRVQK9oBbG9kz5qF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40092}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "ca9bd8a11f2f9a9c3e3cf28a037b2838dcb32e1e", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.11.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.10.0", "@humanfs/types": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.11.0_1707870655494_0.3034056549536437", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "@humanfs/node", "version": "0.12.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.12.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "1ade393b3a30eb965f3430593b76ada51fd351ac", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.12.0.tgz", "fileCount": 5, "integrity": "sha512-Dj64XAVQwiMMh42QpbBR2A4zOgj2igA2ASlevO2BMFF9L5vrY5GFBLqvCo5tRmNfZLAmL1pXR7J2qFDxNqGZJw==", "signatures": [{"sig": "MEUCIQCLhhwZ1Cy60l+vj9SwsFGWzkz0pvLTjuXuYRKGfuwoIwIgLSQFDKdlosw1X30LaAT8b0ADtRjBJ5PjidkMEJFdxB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41231}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "4d388e438d4432f200e0962fed25a6854d65b743", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"@humanfs/core": "^0.12.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.11.0", "@humanfs/types": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.12.0_1708378881920_0.2733480749236381", "host": "s3://npm-registry-packages"}}, "0.13.0": {"name": "@humanfs/node", "version": "0.13.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.13.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "1a39b246e7a272117773d8c2397777d01a118a1e", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.13.0.tgz", "fileCount": 5, "integrity": "sha512-QKvMqPz9GaUBG0aiaW+2XAk9aj8/GOwDBRofP2JevagprU2RoPP8k+VxBB9Q4H5Gfr8drhm7sN93V/hRlRYC/w==", "signatures": [{"sig": "MEUCIFbTCNraBRkaabGjlPdM7sYrzjUef4kjaDW9cQqDVDbdAiEAuSlhDm3/Z38E2fpSr/y8hP55qvvlBeEwEEfPZlaFCIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37539}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "33a6cd225abbac9fadfb8d2bedb413313a0157ba", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@humanfs/core": "^0.13.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.12.0", "@humanfs/types": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.13.0_1708736310595_0.31665152392968166", "host": "s3://npm-registry-packages"}}, "0.14.0": {"name": "@humanfs/node", "version": "0.14.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.14.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "8e102b38c6b7964a8927d218b3f8c509be5b49ba", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.14.0.tgz", "fileCount": 5, "integrity": "sha512-slar<PERSON>ZH1PaS80wlSnk8buLPz4RE85S46BHJ1xKb9gF8gOfQ1fyNbi2SkVynmVYtT0fAY+oMujLum6yPayG4Jxw==", "signatures": [{"sig": "MEQCIBb2n9cOV4+dU4oq4zbO3ijwnjBxe+vX/ueW54s6H7OIAiAQn3IY3HBEnngnEDYh/Ca4B+XdffNTRUM5xpl4vrYRww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36759}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "548a289733b023f0d30c6ce44d5c9327aabd105f", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@humanfs/core": "^0.14.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.13.0", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.14.0_1708992258043_0.783428920692862", "host": "s3://npm-registry-packages"}}, "0.14.1": {"name": "@humanfs/node", "version": "0.14.1", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.14.1", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "62f0bcdcdaf5d40d39524b60ac5470d612756512", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.14.1.tgz", "fileCount": 5, "integrity": "sha512-raECN1SmkVnYIjPylG6rzEAugkp78WXbhTYm5jRR2dGmS05uWo2qKVZRToFPCwc/Oo1IJdyGE69ZHXQcyrXn8w==", "signatures": [{"sig": "MEUCIBzc673lNczdH9qN7suJWtIFwgEe70mX3yhGaDW+6FgAAiEAovv/14BpRCJSq1QvfH3FXQGgq0V/nnf46u584QbEKWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36759}, "type": "module", "types": "dist/node-hfs.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/node-hfs.d.ts", "default": "./src/node-hfs.js"}}, "gitHead": "210e258aae0c35fd1b9232e02bd9243f087eba49", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@humanfs/core": "^0.15.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.13.0", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.14.1_1709166365939_0.2597132336413712", "host": "s3://npm-registry-packages"}}, "0.15.0": {"name": "@humanfs/node", "version": "0.15.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.15.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "e1e6ff361743e8b297dfe73cd1759bc653440ff2", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.15.0.tgz", "fileCount": 7, "integrity": "sha512-V9x8ye1JpB2NtQxfUubimZoyStNa2Mb4XbAmlEcfnY4Rp4bUy9fPlSINq+N1tLBCpj6u+0mCpz2n2VbTAHeYqg==", "signatures": [{"sig": "MEYCIQC1pm/WdBrRlD6dY3+7i0BlIUDc7R2CqkBdyVqzVKOtmQIhAKk4O0vKe5pxUf1Omt0OeDHNpxeC453+5rRzC+AX9cM9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36988}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "1f5ec18da2c2ae63eb6625e88bb095541ae4b1c5", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@humanfs/core": "^0.15.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.13.0", "@humanfs/types": "^0.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.15.0_1710355743525_0.5923298331977034", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@humanfs/node", "version": "0.16.0", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.16.0", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "82fa6d8e010cea6a72bd2735f48b0293043fad4a", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.0.tgz", "fileCount": 7, "integrity": "sha512-SRm0W5uNUXXcVUuPDQl0j+vnwnaaHqMPKlFR+6wG5qeD7ff3QBEMYSSLRg/qCrugCFIE9byNQkNk/rqbUSQMLQ==", "signatures": [{"sig": "MEUCIBs8el5W2fkTePeFaeWGfctCd0Fb6V2GbssoFrff01LQAiEAxz5A3tC5NecrtAYwAIhp716ov5kEuNVEbbnTs2w0ILA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37392}, "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "08722e974e21766b1f75631f3035f0569b6cffea", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@humanfs/core": "^0.17.0", "@humanwhocodes/retry": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.14.0", "@humanfs/types": "^0.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.16.0_1710957537224_0.4305523421026951", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@humanfs/node", "version": "0.16.2", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.16.2", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "59c4001ab4878bfc085b20bfeb174fafb4ac5869", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.2.tgz", "fileCount": 7, "integrity": "sha512-fjGN/xhs73YHfjoK1t8LXGjfOqTRrktYRipmuXi7VQNwwIFYsopQdrlV+SqE5NvDWqhgCy0D/EpniphJ+AMhZg==", "signatures": [{"sig": "MEUCICR9edfqXb3UNzA3N2RNYJNtDV0gX0WUySVhOc1WSN/kAiEAllAEGx3NZ6WKLkijl0otoOhrED09SObfTNZN2w9m4r4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37419}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "e08b2db62c8739e631dd3c915875d76bbc488ba3", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"@humanfs/core": "^0.18.1", "@humanwhocodes/retry": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.14.1", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.16.2_1718215652720_0.7933301733111242", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@humanfs/node", "version": "0.16.3", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.16.3", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "6e916b5668efdff12b5b8fc5b5f6f3233cfb3dfd", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.3.tgz", "fileCount": 7, "integrity": "sha512-o0mvgAAQQUMAWnu8H86czA25+q7I2YbtLEpCn6ZeTX08QK0iKiIfRM0mM6e3b4EUaBY81uKNO/wJzSWE3fl9Rw==", "signatures": [{"sig": "MEQCIEnTcKhmmVzYbwk9255X0wDNh07FewcmofMDEYZBRMh6AiBZ8uAsttKd9p5O2tQLNOedSSVZ4LDSDTLF1Z/0qe1wEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37419}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "4baeedc39f7665d67e971c2021d699884d6d4e96", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"@humanfs/core": "^0.18.2", "@humanwhocodes/retry": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.14.1", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.16.3_1718293918850_0.41483943166417303", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@humanfs/node", "version": "0.16.4", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.16.4", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "7878eda9ac917d7dda8ce9b6313999185e3cef67", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.4.tgz", "fileCount": 7, "integrity": "sha512-8osJSCatgyTEMvDmCTBtZxtmV5smQNhg2f30AG5T6Eid2AIZdcGBtB+NxCS5MUIq2BEYtgEDG0G7mYqMSjWQ+g==", "signatures": [{"sig": "MEUCIDLI3vg+OQResKq3jWDq+ZBfqIbF4sa95n+TaTHkMDgRAiEAw/GjJg4Uw6cjOd8PKQ7n0K+Ia/4Tz58EQNmhkrk8YPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37419}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "ab7b572d304d28fee95886bc228333e928cc351c", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"@humanfs/core": "^0.18.2", "@humanwhocodes/retry": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.15.0", "@humanfs/types": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.16.4_1721836407135_0.7515146110599784", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@humanfs/node", "version": "0.16.5", "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@humanfs/node@0.16.5", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "homepage": "https://github.com/humanwhocodes/humanfs#readme", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "dist": {"shasum": "a9febb7e7ad2aff65890fdc630938f8d20aa84ba", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.5.tgz", "fileCount": 8, "integrity": "sha512-K<PERSON><PERSON><PERSON>umqSG4LHYRodq31VDwKAvaTF4xmVlzM8Aeh4PlU1JQ3IG0wiA8C25d3RQ9nJyM3mBHyI53K06VVL/oFFg==", "signatures": [{"sig": "MEUCIQCkSvwQvq+kmg1IUfxqOqDdrGSwaST46qdepxs22GqaWwIgVe68Byo4Y+YnJnNbWwpnXrk2TFoksg767xcgX2MChXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44106}, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "gitHead": "85069f193cbacc371c3beb649ce746f8c5a493be", "scripts": {"test": "mocha ./tests/", "build": "tsc", "prepare": "npm run build", "pretest": "npm run build"}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/humanwhocodes/humanfs.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "The Node.js bindings of the humanfs library.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"@humanfs/core": "^0.19.0", "@humanwhocodes/retry": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.4", "@humanfs/test": "^0.15.0", "@humanfs/types": "^0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/node_0.16.5_1725911124187_0.3840600356842021", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@humanfs/node", "version": "0.16.6", "description": "The Node.js bindings of the humanfs library.", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}}, "scripts": {"build": "tsc", "prepare": "npm run build", "pretest": "npm run build", "test": "mocha ./tests/"}, "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/humanfs.git"}, "publishConfig": {"access": "public"}, "keywords": ["filesystem", "fs", "hfs", "files"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "homepage": "https://github.com/humanwhocodes/humanfs#readme", "engines": {"node": ">=18.18.0"}, "devDependencies": {"@types/node": "^20.9.4", "@humanfs/test": "^0.15.0", "@humanfs/types": "^0.15.0", "mocha": "^10.2.0", "typescript": "^5.2.2"}, "dependencies": {"@humanwhocodes/retry": "^0.3.0", "@humanfs/core": "^0.19.1"}, "_id": "@humanfs/node@0.16.6", "gitHead": "514883417ddb4880179b4000d874d2d764e30f2d", "_nodeVersion": "22.9.0", "_npmVersion": "10.4.0", "dist": {"integrity": "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==", "shasum": "ee2a10eaabd1131987bf0488fd9b820174cd765e", "tarball": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz", "fileCount": 8, "unpackedSize": 44106, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0unAjb2BoqOFNO/QczEzBo6nOcVMT+e95bG9vjn1+0AiEAjoD6AwGiGzHq6QWDUoVNuIOUqvM3QK6kgen80wbeg0o="}]}, "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node_0.16.6_1730123760957_0.7877192267934425"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-01-30T00:34:36.325Z", "modified": "2024-10-28T13:56:01.381Z", "0.7.0": "2024-01-30T00:34:36.640Z", "0.7.1": "2024-01-31T23:51:31.805Z", "0.8.0": "2024-02-07T00:03:00.558Z", "0.9.0": "2024-02-08T23:17:09.199Z", "0.10.0": "2024-02-09T21:17:55.335Z", "0.11.0": "2024-02-14T00:30:55.724Z", "0.12.0": "2024-02-19T21:41:22.067Z", "0.13.0": "2024-02-24T00:58:30.825Z", "0.14.0": "2024-02-27T00:04:18.204Z", "0.14.1": "2024-02-29T00:26:06.085Z", "0.15.0": "2024-03-13T18:49:03.747Z", "0.16.0": "2024-03-20T17:58:57.363Z", "0.16.2": "2024-06-12T18:07:32.851Z", "0.16.3": "2024-06-13T15:51:59.019Z", "0.16.4": "2024-07-24T15:53:27.353Z", "0.16.5": "2024-09-09T19:45:24.340Z", "0.16.6": "2024-10-28T13:56:01.167Z"}, "bugs": {"url": "https://github.com/humanwhocodes/humanfs/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/humanwhocodes/humanfs#readme", "keywords": ["filesystem", "fs", "hfs", "files"], "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/humanfs.git"}, "description": "The Node.js bindings of the humanfs library.", "maintainers": [{"name": "nzakas", "email": "<EMAIL>"}], "readme": "# `@humanfs/node`\n\nby [<PERSON>](https://humanwhocodes.com)\n\nIf you find this useful, please consider supporting my work with a [donation](https://humanwhocodes.com/donate) or [nominate me](https://stars.github.com/nominate/) for a GitHub Star.\n\n## Description\n\nThe `hfs` bindings for use in Node.js and Node.js-compatible runtimes.\n\n> [!WARNING]\n> This project is **experimental** and may change significantly before v1.0.0. Use at your own caution and definitely not in production!\n\n## Installation\n\nInstall using your favorite package manager:\n\n```shell\nnpm install @humanfs/node\n\n# or\n\npnpm install @humanfs/node\n\n# or\n\nyarn add @humanfs/node\n\n# or\n\nbun install @humanfs/node\n```\n\n## Usage\n\nThe easiest way to use hfs in your project is to import the `hfs` object:\n\n```js\nimport { hfs } from \"@humanfs/node\";\n```\n\nThen, you can use the API methods:\n\n```js\n// 1. Files\n\n// read from a text file\nconst text = await hfs.text(\"file.txt\");\n\n// read from a JSON file\nconst json = await hfs.json(\"file.json\");\n\n// read raw bytes from a text file\nconst arrayBuffer = await hfs.arrayBuffer(\"file.txt\");\n\n// write text to a file\nawait hfs.write(\"file.txt\", \"Hello world!\");\n\n// write bytes to a file\nawait hfs.write(\"file.txt\", new TextEncoder().encode(\"Hello world!\"));\n\n// append text to a file\nawait hfs.append(\"file.txt\", \"Hello world!\");\n\n// append bytes to a file\nawait hfs.append(\"file.txt\", new TextEncoder().encode(\"Hello world!\"));\n\n// does the file exist?\nconst found = await hfs.isFile(\"file.txt\");\n\n// how big is the file?\nconst size = await hfs.size(\"file.txt\");\n\n// when was the file modified?\nconst mtime = await hfs.lastModified(\"file.txt\");\n\n// copy a file from one location to another\nawait hfs.copy(\"file.txt\", \"file-copy.txt\");\n\n// move a file from one location to another\nawait hfs.move(\"file.txt\", \"renamed.txt\");\n\n// delete a file\nawait hfs.delete(\"file.txt\");\n\n// 2. Directories\n\n// create a directory\nawait hfs.createDirectory(\"dir\");\n\n// create a directory recursively\nawait hfs.createDirectory(\"dir/subdir\");\n\n// does the directory exist?\nconst dirFound = await hfs.isDirectory(\"dir\");\n\n// copy the entire directory\nhfs.copyAll(\"from-dir\", \"to-dir\");\n\n// move the entire directory\nhfs.moveAll(\"from-dir\", \"to-dir\");\n\n// delete a directory\nawait hfs.delete(\"dir\");\n\n// delete a non-empty directory\nawait hfs.deleteAll(\"dir\");\n```\n\nIf you'd like to create your own instance, import the `NodeHfs` constructor:\n\n```js\nimport { NodeHfs } from \"@humanfs/node\";\nimport fsp from \"fs/promises\";\n\nconst hfs = new NodeHfs();\n\n// optionally specify the fs/promises object to use\nconst hfs = new NodeHfs({ fsp });\n```\n\nIf you'd like to use just the impl, import the `NodeHfsImpl` constructor:\n\n```js\nimport { NodeHfsImpl } from \"@humanfs/node\";\nimport fsp from \"fs/promises\";\n\nconst hfs = new NodeHfsImpl();\n\n// optionally specify the fs/promises object to use\nconst hfs = new NodeHfsImpl({ fsp });\n```\n\n## Errors Handled\n\n* `ENOENT` - in most cases, these errors are handled silently.\n* `ENFILE` and `EMFILE` - calls that result in these errors are retried for up to 60 seconds before giving up for good.\n\n## License\n\nApache 2.0\n", "readmeFilename": "README.md"}