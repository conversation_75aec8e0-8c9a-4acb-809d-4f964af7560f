{"_id": "stack-utils", "_rev": "28-044533e92bfb9e2c762a89e52292a0ad", "name": "stack-utils", "description": "Captures and cleans stack traces", "dist-tags": {"latest": "2.0.6", "v1-legacy": "1.0.5"}, "versions": {"0.0.1": {"name": "stack-utils", "version": "0.0.1", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter lcov --reporter text --cache ava --verbose"}, "files": ["index.js"], "keywords": [], "config": {"nyc": {"exclude": ["fixtures/*"]}}, "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "coveralls": "^2.11.6", "flatten": "0.0.1", "nyc": "^5.2.0", "xo": "^0.12.1"}, "gitHead": "f0d147cc28c5aacaf6712ea4cdbf8cccfe3ba79c", "bugs": {"url": "https://github.com/jamestalmage/stack-utils/issues"}, "homepage": "https://github.com/jamestalmage/stack-utils#readme", "_id": "stack-utils@0.0.1", "_shasum": "5c7f358e79d45dedff43e8affb9fd905b33bd65c", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "5c7f358e79d45dedff43e8affb9fd905b33bd65c", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-0.0.1.tgz", "integrity": "sha512-FK9/QTx9mLgvX5GX9BzzpuFHnx2NLW029roms8SWIPd7EHds0wJ7DMVzEsABSvph0jbFfRqwMpWmgL4h4iRz9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGy/7OWy+tCHYJ8s+qESqcAEFFwnnt9iuA5dOH42uTCZAiBrWdbNsdXlR/MYVi3YvnmZiu4mD1VHKD1BfeQhW6AxUA=="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "stack-utils", "version": "0.1.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter lcov --reporter text --cache ava --verbose", "test-win": "ava --verbose"}, "files": ["index.js"], "keywords": [], "config": {"nyc": {"exclude": ["fixtures/*"]}}, "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "coveralls": "^2.11.6", "flatten": "0.0.1", "nyc": "^5.2.0", "xo": "^0.12.1"}, "gitHead": "772ffe6894b42d450e7aef2fd9a379b822993572", "bugs": {"url": "https://github.com/jamestalmage/stack-utils/issues"}, "homepage": "https://github.com/jamestalmage/stack-utils#readme", "_id": "stack-utils@0.1.0", "_shasum": "9b2de559f5d1940c3eb40b480ff8c6d9ff6a1b1b", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "9b2de559f5d1940c3eb40b480ff8c6d9ff6a1b1b", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-0.1.0.tgz", "integrity": "sha512-/wzjg1HZriEVWg+gaaAQOmvT76WOy1l1rKWmyyJxxmzUJssKs0cUqet7nErq/qoJ3OGk4sGe6GAaGyc8TfUSHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgeyTh7HOtJkA5rFqdljMqYfkgdebs/vOJkgFDyUBQ8wIgOTJijySCHC7IkiudNQdgMjWV8Whc31dBah5pdV7DgD8="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "stack-utils", "version": "0.2.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jamestalmage/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter lcov --reporter text --cache ava --verbose", "test-win": "ava --verbose"}, "files": ["index.js"], "keywords": [], "config": {"nyc": {"exclude": ["fixtures/*"]}}, "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "coveralls": "^2.11.6", "flatten": "0.0.1", "nyc": "^5.2.0", "xo": "^0.12.1"}, "gitHead": "64fa2c1f51caaeec57fec4843e8a65a4ed41fd8e", "bugs": {"url": "https://github.com/jamestalmage/stack-utils/issues"}, "homepage": "https://github.com/jamestalmage/stack-utils#readme", "_id": "stack-utils@0.2.0", "_shasum": "2e7a45dc67131a0e7bcf472caef3ff1f394b091d", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "2e7a45dc67131a0e7bcf472caef3ff1f394b091d", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-0.2.0.tgz", "integrity": "sha512-PmA0MGNzsXJ8j4jtKQD4v2oThVeKxfpfTMLChLrpIZvXzTtNUKZQWKmeRumOSPUIlms51EhQJEnPOGTH3YYZhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0vq3+nwcP0I5887Cf87E+41TslnBBeLM/lXcfzZ70ZwIgTvu+qa3MJ/oL1Ki4faRb/MHRsF9p5UZwWsOmmc6C4EA="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "stack-utils", "version": "0.3.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter lcov --reporter text --cache ava --verbose", "test-win": "ava --verbose"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "bluebird": "^3.1.1", "coveralls": "^2.11.6", "flatten": "0.0.1", "nested-error-stacks": "^1.0.2", "nyc": "^5.2.0", "pify": "^2.3.0", "q": "^1.4.1", "xo": "^0.12.1"}, "gitHead": "cc2fe4c667ddd9e9f11c633e6ef6397d689016be", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@0.3.0", "_shasum": "cddc6cec2ed9a5ce65f1ac05c90243b7a0ce325d", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "cddc6cec2ed9a5ce65f1ac05c90243b7a0ce325d", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-0.3.0.tgz", "integrity": "sha512-5m+W01Qi4lZ1enG7owlSEPhVg1weGQPtDnsU0jrexFWBo7YkDm9SPIJckI6cvltS/oIoSgoDA9zBgp1lIGjhUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGP9qmR09n5fYVxgIjdSJ8848lg/BXjnp3ILTXcXCEPfAiAsZ9maKhjIo6vsOcICK0a9huas4oJecg12ym4Gn6kIGg=="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "stack-utils", "version": "0.4.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter lcov --reporter text --cache ava --verbose", "test-win": "ava --verbose"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"ava": "^0.8.0", "bluebird": "^3.1.1", "coveralls": "^2.11.6", "flatten": "0.0.1", "nested-error-stacks": "^1.0.2", "nyc": "^5.2.0", "pify": "^2.3.0", "q": "^1.4.1", "xo": "^0.12.1"}, "gitHead": "8701171af041a235bae83a6da4ea516a815bb66a", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@0.4.0", "_shasum": "940cb82fccfa84e8ff2f3fdf293fe78016beccd1", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.4.1", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "940cb82fccfa84e8ff2f3fdf293fe78016beccd1", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-0.4.0.tgz", "integrity": "sha512-UMJIxXde+DIlsX3Ol6/labq6JsMfikqbGZm0u8fRNxMUFLNoPkp1UXKwYUh3dObNBGo3xJGOoOlQxs4cle2cjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/fucqsuyR/vNGRcUpOOhDpH2E7BXdkfDY+5tH6XRy5AiBmGi7fx+cqQOApjVCul0Ox7jCBBockEN6tVHzW7dmFlA=="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/stack-utils-0.4.0.tgz_1454711753671_0.9951272297184914"}, "directories": {}}, "1.0.0": {"name": "stack-utils", "version": "1.0.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js --cov"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"bluebird": "^3.1.1", "coveralls": "^2.11.6", "flatten": "0.0.1", "nested-error-stacks": "^2.0.0", "pify": "^2.3.0", "q": "^1.4.1", "tap": "^10.0.0"}, "gitHead": "faa81f19c8fd23cb5c9b13dfd7b26df6be7d7b1e", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.0", "_shasum": "2392cd8ddbd222492ed6c047960f7414b46c0f83", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2392cd8ddbd222492ed6c047960f7414b46c0f83", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.0.tgz", "integrity": "sha512-52IE4XpM82iuTs+PxjZTppEka+lBPQC7+LwHDL/0RcslD7GH9iQaRgLvbJBc1ajZNPpLiq9E9daAQ58SM34asA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDss+Fd9BY+XWtwOa7Li9Wb9zmiY7l4JqYcvj+a1QwMQAiBFRpG1DGNJAD8P2CvRcFNuGFeufDsaRGJoNAoWGGy1pg=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/stack-utils-1.0.0.tgz_1486016285424_0.8635052388999611"}, "directories": {}}, "1.0.1": {"name": "stack-utils", "version": "1.0.1", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "files": ["index.js"], "keywords": [], "dependencies": {}, "devDependencies": {"bluebird": "^3.1.1", "coveralls": "^2.11.6", "flatten": "0.0.1", "nested-error-stacks": "^2.0.0", "pify": "^2.3.0", "q": "^1.4.1", "tap": "^10.3.2"}, "gitHead": "f080bcb2ee9be20520cfb312837d6056011e9094", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.1", "_shasum": "d4f33ab54e8e38778b0ca5cfd3b3afb12db68620", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d4f33ab54e8e38778b0ca5cfd3b3afb12db68620", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.1.tgz", "integrity": "sha512-KeADm3hVFxp5R3yDROVatAG51zBtWTIZ8moWjz0xJrHGWzVYZjxNoJUbYJF2gyvUqQSTGsib+CgbRb+gvemV8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUJh6+ujaLEdVmZeYc2Ao7F9t7HmAROjIrELQ3P2GDOgIhAKZrIZIKpl+YL5DQU82G8MHci/d9Heqj/S5exqBLSTxp"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/stack-utils-1.0.1.tgz_1492979544062_0.2575548014137894"}, "directories": {}}, "1.0.2": {"name": "stack-utils", "version": "1.0.2", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "dependencies": {}, "devDependencies": {"bluebird": "^3.1.1", "coveralls": "^2.11.6", "flatten": "0.0.1", "nested-error-stacks": "^2.0.0", "pify": "^2.3.0", "q": "^1.4.1", "tap": "^10.3.2"}, "gitHead": "2d238dfac8c39ab85997b90a1e48ca51c096adc0", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MTX+MeG5U994cazkjd/9KNAapsHnibjMLnfXodlkXw76JEea0UiNzrqidzo1emMwk7w5Qhc9jd4Bn9TBb1MFwA==", "shasum": "33eba3897788558bebfc2db059dc158ec36cebb8", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.2.tgz", "fileCount": 4, "unpackedSize": 13721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7HAVCRA9TVsSAnZWagAA3ZkQAJ9+8f44fXIaG8+XPTW/\neyaJyPZ98COh665eiFo+9rzambeKm8CLZWlKuQDBEp5Y8bCGoF+ZEVuPzo5C\nz2G+4sVLaT9HzAedIYiXDzxN7LvICsDZhqj9lYdBk/PABZCfYNSQmQ4fzHXy\nkVmCaRmyg3E8nqnoiDf4IciuejRs70A3zN8OFk85ecAZ3p+8Q+aXAdV0UJKP\n0nzb5OgrdlZvL54de0NzxuGmiYPJRxej+J9x/9jLbkgRdG5K7x1BNA9qWbGW\nOybR9iBhrsv6qSr0WQqceVVHZn2naHjwAlYHftXp+uJ+gKoIY9iQW3s5cbIL\nkpG70/VJC/qkxATKygJaM2A5QUmB+3nLJ5JIE3b/QfaB6gvYfK329xcSLUG/\nR9UZBgrkm7n94NPq09bILMj9502zpsZyyMH4rIdGGaabqyyBHkQ7oR97Gqs/\nBh9/ilX/72KvAxnygZMcNorq30pEJ7n1qwvF9M9nEswhRx/he2CJgWL+7oRl\nzSUCBSKx1Uob/gHFyY7tjYuQ8J+nIKzHj1tcJHBzlbdQb9qcWGOKXlTDTULD\nkUjnlZCarjbjRducFB0WKbfnQn4W+8ifradXXec7R7u1zl8kTOyz7XhRKAFZ\ngfLYsP0ZJfFuM67V+d4qCq/39YYWqXyDNWtt839koivo8kg4TTY6PhJyiZ9z\nkGhB\r\n=3a8G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEXSml47r0EUEui2gTT9cCW3PzedaMN5bKjwr9Vwc8cQIgK03Y7OTEpCE3jtaaEH1xjYP5XSCwFz3B3doTIXUm+ZM="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_1.0.2_1542221844258_0.8869018687314081"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "stack-utils", "version": "2.0.0", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublish": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "=14.10.2-unbundled"}, "gitHead": "27d78e9b237f959e51452459d97ab0b23c5dfc08", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.0", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-4v/rS/yaVwqoELB+izpdHL94S8SC7S+u1+MaiJqsavAxoXP601sTuP7AJh/9qX4IeeXFnLQOuZ4zFhhfPL6C+g==", "shasum": "eeaf8bf4d0e343b98cf64781121b189570203ba0", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.0.tgz", "fileCount": 4, "unpackedSize": 14356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8zRlCRA9TVsSAnZWagAAe/YP/jUiIQ5tfPMn7P0Ox5+p\npNiWmgXS7r79Nc6wRlwhZiYuNs1+y9Au2ZR71du9OtBK7zIZA6NSMNT78vl/\nG64gM7UGnqCY29jTDY+0bhyXr2dYHjB4Ur1HbC10Zqo0bb3CQpBI8oc3tjpi\n56CCReebzEfOO4HaXij8GLObTgL29vQ/kVeypCo6YrKiP1XyjfiHoi24DV0z\n3ovi2DdtvysH/oWlaW/WkpwDE3kW3nV31+c5Q1UusfzSJ4UaRwqbLUdS8keH\n0PglY3v7/G1jEUspZbWOlOMRkStPNaw2dBD0FucUy8Vht3OMi7YSuA8RDCXi\nOib8g7dbeDwucedPdtS+7yFqfZ0GIlLErZFhNIbXDSb6v7j9XR9M4r18ToGY\n+laB3VNhJ3EXGQN6tBsXVwDBiSHfJEq0A/NldFMTEbicWfVW9QmS2exhA8QN\nMeU1DXGi6MwbA83DLygZwT8Qi5MwC00nPv1OMhEpyJlQwk7Q5bC2ee+BQ2M1\nJz/ySC7/3PiULLy5LHrzQ3mSMHQOP4wsk+/WjV8u3kdNT/4auNJ5VUHZCaT9\npGCK6NlYC9j5Ic+I1zEVHwLK4n1tkpEhs6T1mH5S7HxnzoYjk7FxmmqRWgXE\ny2FV7vY1nFiT2IpPszphx0Wbiv1i/Rgys8npfArsU1FmSkd7yb5hYuC/IPC3\ny965\r\n=V+ye\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD10f8OR6vFdOqnTYu4yrVqcNh7FuZEozmM/+SBaBtEcQIgGk0Nr2bXrjETLOwXMYJ6CnL2NoSAxg+Z/7FnI2/pmws="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.0_1576219748802_0.6404410148032522"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "stack-utils", "version": "2.0.1", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "=14.10.2-unbundled"}, "gitHead": "d3c7ee2a7d46744c271a36ce3acfd69465f36df5", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.1", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-BvBTnHGm8boe+HiJFqP19ywEsGlfQAKqW78pbfvUuzCbUuxPPUyLrH5dYFY+Xn9IpLY3b5ZmMcl8jAqXB4wddg==", "shasum": "3df48345a3b92adc06038f0e95782df61beff742", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.1.tgz", "fileCount": 4, "unpackedSize": 14360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8zS4CRA9TVsSAnZWagAAonIP/ikwm8hbgEJNwE9eqjwu\nRVOa5R0l+ioj7mL8AIl9hoJvh1T6jzjTEUa+NSVp9COCI8nWpiQdiEefnywV\ndyHRdCOLuAnXzvKqwLiaWpcYiNYU5goanNtbfMaX2CreB22iiAu7wDQ4wDCT\nQTr71DQWz95BspZ/eabL+oxKErLZNVIGsxDCbAFo/SHxhOXcfAi3SuIkEygR\n7KQbLFxTl+npD3raWR3zakONYUBKFt/6tYTX1bteN1qARxqk0KGY3EJ8jhxg\n4BsJu5lrVVYaoEKSCfsZ+ttBkS/d4fsobVxzYXYnDpD5RFB7b6HJAZ653j5E\nWQiRgLEYDVp3sLxFWAstCuRQ2bYfHILCODwpWVmPOtA5EjBZWP/cbB8zDTHG\neP2RQcz4sEJGDZCRWE8RSdKGrd8ZJWADNGiZA5Aoaswe8k5shS3To1xwLq2y\nDvsP96f+GVjJFFyHtlPb17abAOrOqInU4zpjxWRHWNfQ0k2plUoBMQGFQna5\n116M0jbs1GeHiJ+4ADcuEqnORQK4Y/IMeX389EbKjyKWJW3Ig1el5C134XwM\nFOqsFwieOsoqRLvl9QWy6o73gi0dS1iprdxtICchAlLTbIZv7inEAdbSiFee\nsSDImu9/UvJDBCe/EmQKNUQ6GdlASWjnXy+9wvZpA7dRNH/c7fF7M9tMsvnX\nwB41\r\n=P8Se\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhHJWkbYkzUV0AWseYd36wWS10sihhqmvgxa6B8g4VbgIgOMNqDdaeuAoWHKnuT0UKrjgk07C6Zg2cISTIgP3+/ZU="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.1_1576219832040_0.7200474147237628"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "stack-utils", "version": "2.0.2", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "=14.10.2-unbundled"}, "gitHead": "b3c146e3f157aceebe830e858eb41f1df9387f79", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-0H7QK2ECz3fyZMzQ8rH0j2ykpfbnd20BFtfg/SqVC2+sCTtcw0aDTGB7dk+de4U4uUeuz6nOtJcrkFFLG1B0Rg==", "shasum": "5cf48b4557becb4638d0bc4f21d23f5d19586593", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.2.tgz", "fileCount": 4, "unpackedSize": 14223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqEjBCRA9TVsSAnZWagAAousQAKKZPqlbu5SgG2TUAiM9\nk04+U5X7w/30OBcL8WdhEEH4LqsRAljlbVRacdBtTyk+SmsjZtb9aJgBR3tO\nL6EIrhd4zmAVTE67BT4pBBxdi3iJBPAlBqAFQzBYcRmLCxOT9Uj8U92L4WC/\np3Yb2He+HDw7NC1Jk9vU5A9xqBnB2kKsoxyBLUwg4FUdGGd2GTUU2XX05eC1\nXcSkcmFsPIJSn/4oENtaDcI/1bxhik2y0sSCgZQhQTrY8DZzkg41KK+u45qv\nN/f0Twzc/GFQLe1kJPHXEZ0LaSz4L5hH1UTXW0kPA1oiLl1W0hWgDE4ZvAnK\nokAY/iGbxK+oKk5rRF8p19CKjONeb4zQZuSuJw/uUQcPaMyEKxNWuB6Z/u6F\n1ZtMm1vXHahCLccQEJRsarRBBSMCN1R4ooNvcTySRJ3CeWu+A8sN6rbqRM0e\n9EeFmIN5aDu/LflvwQVCe9uYdz9GAFloVetPtiAWwNODQruHTX7h8zhGTDhM\niIm8yr/E3Gy5rC5iwWs1yxZUxzm1niTrO08VZiXT4Z9vcJ6O1o24WUAgxZrP\nxBwYa/oxBFqzOTgHTTQg+WQmJnK/6IJimzYUk0EMD1mgztSHKFmKUONwJuEC\nCBbZ/A/lCSS+P7bM0IzjRyksu/52cihXl+/qCLYMMUQQq9XImuUDQdXQIOtT\noC0e\r\n=FjL8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIm2P2BvkEsk8MdWnOu1wc9eXfhXnV/JIbFm4mzzte8gIhAKbm15CMurUWRPbUNYqaktOsnbAyeG9JOPShDN0fLlp6"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.2_1588086977285_0.5869045456973836"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "stack-utils", "version": "2.0.3", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "^14.10.8"}, "gitHead": "82097544610b7360e14c496b3eb23aedda53d3d0", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.3", "_nodeVersion": "15.2.0", "_npmVersion": "7.0.11", "dist": {"integrity": "sha512-gL//fkxfWUsIlFL2Tl42Cl6+HFALEaB1FU76I/Fy+oZjRreP7OPMXFlGbxM7NQsI0ZpUfw76sHnv0WNYuTb7Iw==", "shasum": "cd5f030126ff116b78ccb3c027fe302713b61277", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.3.tgz", "fileCount": 4, "unpackedSize": 14263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrxJSCRA9TVsSAnZWagAA0AwP/RhYdCOH8tMYU90DPjkC\nvI+mQctn1X0Yss8U+CG0odK45UB9xZE29ZnB2VanY/p2wP8JQtxq9EJKhxkt\n9CtU5ZsqPG9gzHj5j+WEnCjqDR290zLc6hwKSBjhg//5GFwjqz6MP4px1ccM\nHnWeKVmVWSBAE7rnfGY/pOTNdsr4jD8WAIeOVlDilefNF2qYaFV8E1aotzUY\njgBa/9Hf11ypRE4OydpP89ThTik7r1kO9m3MXMFd63UWCfwSEr7ZDWCp9Ikp\nnsGXdDWkaY2UF1Dyq78WeJ0Myps2WP//9YS/PIBpvxhQI5A/PaDU/9iEygmR\nY0PgaCSh/RROeMIWpIpQjWYGCVsLxRQ8xuWjQjB8KhUK3qkA4OoII3Ag2ce3\nTeX/sShTSfpr9gk0m/rRIgt/epgsBp0gQJl2n/MT6tJGE6rk2O/muIaGp1e3\n0NVvT9Ve7lBdvvij+PJWdv2qHLiwaKT/ldDwo5C8CsBD2ZZqOKlR2pHaStKH\nChdABmKxpCbPXgws/KT4a+u8ruQcAUMqnUqXAy5PuinD+jgfNHAepyl3Bi53\nDTrWFNytvbg6nC5W06KLcCTlYEWrbQBD7z2KPGHJPLBN85c8zckhx/B2aBXc\nldS5ps+QaKZ7Y5x3kNmQSDHk2qtuyY3FXE5vKKMEQd0xzN2oATD4UmWqxrnm\nGW6S\r\n=nA/R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCz7syMc5xD10eBxzv5zHbjqaVFRNgOmuc838s3XVhVaAIhAIpq+JWoeLe6J4UYJg3LsPpj40NdY3BRO633h58/E1bf"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.3_1605309010407_0.4168764025799361"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "stack-utils", "version": "1.0.3", "publishConfig": {"tag": "v1-legacy"}, "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=8"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "^14.10.8"}, "readme": "# stack-utils \n\n> Captures and cleans stack traces.\n\n[![Linux Build](https://travis-ci.org/tapjs/stack-utils.svg?branch=master)](https://travis-ci.org/tapjs/stack-utils) [![Build status](https://ci.appveyor.com/api/projects/status/fb9i157knoixe3iq/branch/master?svg=true)](https://ci.appveyor.com/project/jamestalmage/stack-utils-oiw96/branch/master)  [![Coverage](https://coveralls.io/repos/tapjs/stack-utils/badge.svg?branch=master&service=github)](https://coveralls.io/github/tapjs/stack-utils?branch=master)\n\n\nExtracted from `lib/stack.js` in the [`node-tap` project](https://github.com/tapjs/node-tap)\n\n## Install\n\n```\n$ npm install --save stack-utils\n```\n\n\n## Usage\n\n```js\nconst StackUtils = require('stack-utils');\nconst stack = new StackUtils({cwd: process.cwd(), internals: StackUtils.nodeInternals()});\n\nconsole.log(stack.clean(new Error().stack));\n// outputs a beautified stack trace\n```\n\n\n## API\n\n\n### new StackUtils([options])\n\nCreates a new `stackUtils` instance.\n\n#### options\n\n##### internals\n\nType: `array` of `RegularExpression`s  \n\nA set of regular expressions that match internal stack stack trace lines which should be culled from the stack trace.\nThe default is `StackUtils.nodeInternals()`, this can be disabled by setting `[]` or appended using\n`StackUtils.nodeInternals().concat(additionalRegExp)`.  See also `ignoredPackages`.\n\n##### ignoredPackages\n\nType: `array` of `string`s\n\nAn array of npm modules to be culled from the stack trace.  This list will mapped to regular\nexpressions and merged with the `internals`.\n\nDefault `''`.\n\n##### cwd\n\nType: `string`\n\nThe path to the current working directory. File names in the stack trace will be shown relative to this directory.\n\n##### wrapCallSite\n\nType: `function(CallSite)`\n\nA mapping function for manipulating CallSites before processing. The first argument is a CallSite instance, and the function should return a modified CallSite. This is useful for providing source map support.\n\n\n### StackUtils.nodeInternals()\n\nReturns an array of regular expressions that be used to cull lines from the stack trace that reference common Node.js internal files.\n\n\n### stackUtils.clean(stack, indent = 0)\n\nCleans up a stack trace by deleting any lines that match the `internals` passed to the constructor, and shortening file names relative to `cwd`.\n\nReturns a `string` with the cleaned up stack (always terminated with a `\\n` newline character).\nSpaces at the start of each line are trimmed, indentation can be added by setting `indent` to the desired number of spaces.\n\n#### stack\n\n*Required*  \nType: `string` or an `array` of `string`s\n\n\n### stackUtils.capture([limit], [startStackFunction])\n\nCaptures the current stack trace, returning an array of `CallSite`s. There are good overviews of the available CallSite methods [here](https://github.com/v8/v8/wiki/Stack%20Trace%20API#customizing-stack-traces), and [here](https://github.com/sindresorhus/callsites#api).\n\n#### limit\n\nType: `number`\nDefault: `Infinity`\n\nLimits the number of lines returned by dropping all lines in excess of the limit. This removes lines from the stack trace.\n\n#### startStackFunction\n\nType: `function`\n\nThe function where the stack trace should start. The first line of the stack trace will be the function that called `startStackFunction`. This removes lines from the end of the stack trace.\n\n\n### stackUtils.captureString([limit], [startStackFunction])\n\nCaptures the current stack trace, cleans it using `stackUtils.clean(stack)`, and returns a string with the cleaned stack trace. It takes the same arguments as `stackUtils.capture`.\n\n\n### stackUtils.at([startStackFunction])\n\nCaptures the first line of the stack trace (or the first line after `startStackFunction` if supplied), and returns a `CallSite` like object that is serialization friendly (properties are actual values instead of getter functions). \n\nThe available properties are:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `native`: `boolean`\n - `type`: `string`\n - `function`: `string`\n - `method`: `string`\n\n### stackUtils.parseLine(line)\n\nParses a `string` (which should be a single line from a stack trace), and generates an object with the following properties:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `evalLine`: `number`\n - `evalColumn`: `number`\n - `evalFile`: `string`\n - `native`: `boolean`\n - `function`: `string`\n - `method`: `string`\n\n\n## License\n\nMIT © [Isaac Z. Schlueter](http://github.com/isaacs), [James Talmage](http://github.com/jamestalmage)\n", "readmeFilename": "readme.md", "gitHead": "85ad2ca7a6f2ca83588f4c0d6960db485534e442", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.3", "_nodeVersion": "15.2.0", "_npmVersion": "7.0.11", "dist": {"integrity": "sha512-WldO+YmqhEpjp23eHZRhOT1NQF51STsbxZ+/AdpFD+EhheFxAe5d0WoK4DQVJkSHacPrJJX3OqRAl9CgHf78pg==", "shasum": "db7a475733b5b8bf6521907b18891d29006f7751", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.3.tgz", "fileCount": 4, "unpackedSize": 14490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrxd4CRA9TVsSAnZWagAAlMwP/3R+q0L2mYqe4OYfsX6X\nhgV15IQspUx5kecoxBp0Wo4Y7hYokCzJ8OtEMj5TRn+7TMrg3lZ/kaD+RuW9\nrvYkSkxfbrt9adI2DTdXDne2VX6IdVHNscj7J6oThnfgCHLQ1CYgrg6Bx8nx\nIwW8oZeuLDiZD9QQp71hWqWlBjXKbkm6xswqWfMSwDaSwGJX6YcW8u0J9h2U\nJRAjGMkvahrxMzqFlzazcunniOfbtrICYxBwsNdtOhGfkLMOMKBqQ4cBS2Gs\ndRIqoxcmRKEiywkbgZRsYH9bnww+ZG67urXmXlS8yfMrAUBP2STnW0vigkZ8\n5TUOAJxW7YYxy56UfiVdHC0YWmlIAnAoF1NHD+e+ZW2PaFjsgxMUZGDRTUij\nIhDNTuigZ/H18btH1hCt/w8gaGHR8UVjXjUW7FvDFH0Vxk+j1DC4zTmrkAUp\ntSQSkf4/4uByYXbhvwMJq1iSIuNndC/iiBQP7TOLBJPYtmx5GeRYPEedWZgu\n4NBnCdptmzpB+4bVbP8tI0K3IMVG9O0U3Pd8vXt82+IlnsxxBtdjIWP3HZ7g\nWZEdMY6W9+JMFx/1qS8VmkJlNTjJlXWvVoghIMEN8mnuKDxLYs9fU59D+Sky\n5T2QF0PcNzqyOCLUNypvVLGkwFf9QvyK49tL9WsuNjaGuZPzieNmk5f0ztJm\nqrRQ\r\n=vmBQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbTULAEeQve/49fVTX/zQHjbKmdNkPUkfhVzBHjfghOAiBj6XkxTkUvQKQmiuk+b4bUka+tk5gDI2cUr4cvF1EgkA=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_1.0.3_1605310327816_0.4557233752643852"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "stack-utils", "version": "1.0.4", "publishConfig": {"tag": "v1-legacy"}, "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=8"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "^14.10.8"}, "readme": "# stack-utils \n\n> Captures and cleans stack traces.\n\n[![Linux Build](https://travis-ci.org/tapjs/stack-utils.svg?branch=master)](https://travis-ci.org/tapjs/stack-utils) [![Build status](https://ci.appveyor.com/api/projects/status/fb9i157knoixe3iq/branch/master?svg=true)](https://ci.appveyor.com/project/jamestalmage/stack-utils-oiw96/branch/master)  [![Coverage](https://coveralls.io/repos/tapjs/stack-utils/badge.svg?branch=master&service=github)](https://coveralls.io/github/tapjs/stack-utils?branch=master)\n\n\nExtracted from `lib/stack.js` in the [`node-tap` project](https://github.com/tapjs/node-tap)\n\n## Install\n\n```\n$ npm install --save stack-utils\n```\n\n\n## Usage\n\n```js\nconst StackUtils = require('stack-utils');\nconst stack = new StackUtils({cwd: process.cwd(), internals: StackUtils.nodeInternals()});\n\nconsole.log(stack.clean(new Error().stack));\n// outputs a beautified stack trace\n```\n\n\n## API\n\n\n### new StackUtils([options])\n\nCreates a new `stackUtils` instance.\n\n#### options\n\n##### internals\n\nType: `array` of `RegularExpression`s  \n\nA set of regular expressions that match internal stack stack trace lines which should be culled from the stack trace.\nThe default is `StackUtils.nodeInternals()`, this can be disabled by setting `[]` or appended using\n`StackUtils.nodeInternals().concat(additionalRegExp)`.  See also `ignoredPackages`.\n\n##### ignoredPackages\n\nType: `array` of `string`s\n\nAn array of npm modules to be culled from the stack trace.  This list will mapped to regular\nexpressions and merged with the `internals`.\n\nDefault `''`.\n\n##### cwd\n\nType: `string`\n\nThe path to the current working directory. File names in the stack trace will be shown relative to this directory.\n\n##### wrapCallSite\n\nType: `function(CallSite)`\n\nA mapping function for manipulating CallSites before processing. The first argument is a CallSite instance, and the function should return a modified CallSite. This is useful for providing source map support.\n\n\n### StackUtils.nodeInternals()\n\nReturns an array of regular expressions that be used to cull lines from the stack trace that reference common Node.js internal files.\n\n\n### stackUtils.clean(stack, indent = 0)\n\nCleans up a stack trace by deleting any lines that match the `internals` passed to the constructor, and shortening file names relative to `cwd`.\n\nReturns a `string` with the cleaned up stack (always terminated with a `\\n` newline character).\nSpaces at the start of each line are trimmed, indentation can be added by setting `indent` to the desired number of spaces.\n\n#### stack\n\n*Required*  \nType: `string` or an `array` of `string`s\n\n\n### stackUtils.capture([limit], [startStackFunction])\n\nCaptures the current stack trace, returning an array of `CallSite`s. There are good overviews of the available CallSite methods [here](https://github.com/v8/v8/wiki/Stack%20Trace%20API#customizing-stack-traces), and [here](https://github.com/sindresorhus/callsites#api).\n\n#### limit\n\nType: `number`\nDefault: `Infinity`\n\nLimits the number of lines returned by dropping all lines in excess of the limit. This removes lines from the stack trace.\n\n#### startStackFunction\n\nType: `function`\n\nThe function where the stack trace should start. The first line of the stack trace will be the function that called `startStackFunction`. This removes lines from the end of the stack trace.\n\n\n### stackUtils.captureString([limit], [startStackFunction])\n\nCaptures the current stack trace, cleans it using `stackUtils.clean(stack)`, and returns a string with the cleaned stack trace. It takes the same arguments as `stackUtils.capture`.\n\n\n### stackUtils.at([startStackFunction])\n\nCaptures the first line of the stack trace (or the first line after `startStackFunction` if supplied), and returns a `CallSite` like object that is serialization friendly (properties are actual values instead of getter functions). \n\nThe available properties are:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `native`: `boolean`\n - `type`: `string`\n - `function`: `string`\n - `method`: `string`\n\n### stackUtils.parseLine(line)\n\nParses a `string` (which should be a single line from a stack trace), and generates an object with the following properties:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `evalLine`: `number`\n - `evalColumn`: `number`\n - `evalFile`: `string`\n - `native`: `boolean`\n - `function`: `string`\n - `method`: `string`\n\n\n## License\n\nMIT © [Isaac Z. Schlueter](http://github.com/isaacs), [James Talmage](http://github.com/jamestalmage)\n", "readmeFilename": "readme.md", "gitHead": "f64938000f55fa2f3aee0679e1e11f390b05c7f3", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.4", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.12", "dist": {"integrity": "sha512-IPDJfugEGbfizBwBZRZ3xpccMdRyP5lqsBWXGQWimVjua/ccLCeMOAVjlc1R7LxFjo5sEDhyNIXd8mo/AiDS9w==", "shasum": "4b600971dcfc6aed0cbdf2a8268177cc916c87c8", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.4.tgz", "fileCount": 4, "unpackedSize": 14502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvvxdCRA9TVsSAnZWagAAtB4P/3s+qNWNEd7m/4kTfQfb\nNlYcN59FD/34IkssYP4TZdbjs8+b4+fXHhsGggiQ4x7DFRKEK/Ro1PgkwAoi\nFXPT6EDc9CEEOj39/jEDMWhCPzr+xg/v953kr6DPVyt2S7MJL1OzzaJ2JAab\nj4q4yLyIkvs08OduGz5MutB2zCvbyVCGXyx7X4MV1pgSNieShhWB0sCIOjlg\nSM3FyxbBb7Dy8htXc3BGjSTuZ0NgdAGqsFTqrxy3fId7uykuHG0MRywj2AEp\nCso/t/KPB6UYJy46c2rhbVGDhnnYBRVk7ZjQwpmB7xE1kE4H5LrsC8U4FcDI\nezGUfO4MiuRolIRfkmFwF3+ghh8jQrfbf+5/4JXTWksP3TdKbj25/VHywyxY\nahYi5BlQkx+tKDn88RpXDa50tUZkZkwdkKdKLwNheULN4CeRsvuvPFi+e0bc\nqbyI6T5D//fyOVO+6yVHMHwuTJb4me+DiN/hD9Mk3uYWafRIJIGNon9uL1I7\nMU754QGO3GssDtwtYwLrs+4aXl6D25I+tIiHJN05w6IkALpVVUcSiw+SCV93\n6VUvuDHj6B6CfLcCB4D3Jr+Knh/atPCqobD8BkHIAieDaZxW8JrbNyBuPYYs\nE6Z0xDPGaUvQ6hObn5GSoWkLqZ9Otc0Njt6p8RYhnKbLP0aVR54SnjZjHZe7\nGQLe\r\n=qIDf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEbHIiUG64TXnX7Iohl7Xt0xTCmqmJwOFxANkOT2pMqYAiEArGRgMyLwBuhauXFbLoEQCAyr4ut+/yY90TUkmTQ4rQg="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_1.0.4_1606351964773_0.6983359601082031"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "stack-utils", "version": "1.0.5", "publishConfig": {"tag": "v1-legacy"}, "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=8"}, "scripts": {"test": "tap --no-esm --100", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "^14.10.8"}, "readme": "# stack-utils \n\n> Captures and cleans stack traces.\n\n[![Linux Build](https://travis-ci.org/tapjs/stack-utils.svg?branch=master)](https://travis-ci.org/tapjs/stack-utils) [![Build status](https://ci.appveyor.com/api/projects/status/fb9i157knoixe3iq/branch/master?svg=true)](https://ci.appveyor.com/project/jamestalmage/stack-utils-oiw96/branch/master)  [![Coverage](https://coveralls.io/repos/tapjs/stack-utils/badge.svg?branch=master&service=github)](https://coveralls.io/github/tapjs/stack-utils?branch=master)\n\n\nExtracted from `lib/stack.js` in the [`node-tap` project](https://github.com/tapjs/node-tap)\n\n## Install\n\n```\n$ npm install --save stack-utils\n```\n\n\n## Usage\n\n```js\nconst StackUtils = require('stack-utils');\nconst stack = new StackUtils({cwd: process.cwd(), internals: StackUtils.nodeInternals()});\n\nconsole.log(stack.clean(new Error().stack));\n// outputs a beautified stack trace\n```\n\n\n## API\n\n\n### new StackUtils([options])\n\nCreates a new `stackUtils` instance.\n\n#### options\n\n##### internals\n\nType: `array` of `RegularExpression`s  \n\nA set of regular expressions that match internal stack stack trace lines which should be culled from the stack trace.\nThe default is `StackUtils.nodeInternals()`, this can be disabled by setting `[]` or appended using\n`StackUtils.nodeInternals().concat(additionalRegExp)`.  See also `ignoredPackages`.\n\n##### ignoredPackages\n\nType: `array` of `string`s\n\nAn array of npm modules to be culled from the stack trace.  This list will mapped to regular\nexpressions and merged with the `internals`.\n\nDefault `''`.\n\n##### cwd\n\nType: `string`\n\nThe path to the current working directory. File names in the stack trace will be shown relative to this directory.\n\n##### wrapCallSite\n\nType: `function(CallSite)`\n\nA mapping function for manipulating CallSites before processing. The first argument is a CallSite instance, and the function should return a modified CallSite. This is useful for providing source map support.\n\n\n### StackUtils.nodeInternals()\n\nReturns an array of regular expressions that be used to cull lines from the stack trace that reference common Node.js internal files.\n\n\n### stackUtils.clean(stack, indent = 0)\n\nCleans up a stack trace by deleting any lines that match the `internals` passed to the constructor, and shortening file names relative to `cwd`.\n\nReturns a `string` with the cleaned up stack (always terminated with a `\\n` newline character).\nSpaces at the start of each line are trimmed, indentation can be added by setting `indent` to the desired number of spaces.\n\n#### stack\n\n*Required*  \nType: `string` or an `array` of `string`s\n\n\n### stackUtils.capture([limit], [startStackFunction])\n\nCaptures the current stack trace, returning an array of `CallSite`s. There are good overviews of the available CallSite methods [here](https://github.com/v8/v8/wiki/Stack%20Trace%20API#customizing-stack-traces), and [here](https://github.com/sindresorhus/callsites#api).\n\n#### limit\n\nType: `number`\nDefault: `Infinity`\n\nLimits the number of lines returned by dropping all lines in excess of the limit. This removes lines from the stack trace.\n\n#### startStackFunction\n\nType: `function`\n\nThe function where the stack trace should start. The first line of the stack trace will be the function that called `startStackFunction`. This removes lines from the end of the stack trace.\n\n\n### stackUtils.captureString([limit], [startStackFunction])\n\nCaptures the current stack trace, cleans it using `stackUtils.clean(stack)`, and returns a string with the cleaned stack trace. It takes the same arguments as `stackUtils.capture`.\n\n\n### stackUtils.at([startStackFunction])\n\nCaptures the first line of the stack trace (or the first line after `startStackFunction` if supplied), and returns a `CallSite` like object that is serialization friendly (properties are actual values instead of getter functions). \n\nThe available properties are:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `native`: `boolean`\n - `type`: `string`\n - `function`: `string`\n - `method`: `string`\n\n### stackUtils.parseLine(line)\n\nParses a `string` (which should be a single line from a stack trace), and generates an object with the following properties:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `evalLine`: `number`\n - `evalColumn`: `number`\n - `evalFile`: `string`\n - `native`: `boolean`\n - `function`: `string`\n - `method`: `string`\n\n\n## License\n\nMIT © [Isaac Z. Schlueter](http://github.com/isaacs), [James Talmage](http://github.com/jamestalmage)\n", "readmeFilename": "readme.md", "gitHead": "3fedcb900446a4d47d764a98d91d8d989b7c2e2b", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@1.0.5", "_nodeVersion": "15.3.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-KZiTzuV3CnSnSvgMRrARVCj+Ht7rMbauGDK0LdVFRGyenwdylpajAp4Q0i6SX8rEmbTpMMf6ryq2gb8pPq2WgQ==", "shasum": "a19b0b01947e0029c8e451d5d61a498f5bb1471b", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.5.tgz", "fileCount": 4, "unpackedSize": 14631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga1NeCRA9TVsSAnZWagAAzPgQAKSoV7e5pMl44n7c2C7M\n3pNk9hudEKkRdIbca7CpB9gmceQvYvzzoUGnIXqVViA9vu9l/HFL23FlEd5c\n61tKJ/IfIi1w50PSr9d552Xby7tLZB9k/oe019KIuE5zxXj54ejlXwp3RHJF\nGEgqGr5f8WwDz50foLUHRvY8/XGUY2lKoLfTEU8HqY2uaS+KD3AW8kYILy4F\ntzEkrymF9qk8xPHA4gpK/5rIKvkCqm6tqXjzVbQBFl6YY/7fGSlkheqHp1xp\n+hcVfZCP6eq8/yzoe+j7aEjAYVZ1UkaRGfAO/vCTMMjlH9P5bCOLYS0S2FWY\nyM2qkrdp1WjDntIyPKAA1xi6Upmle8Wo7DeegBp0iCV+Jjc3wAWCm7gKnI/E\nQ15C2lciFnumGqIeDPCHbaOVmIFPYnb4UGvVa971uPbbVYGUeLJ5IXLHPTPa\nK8RNgTUYF+erhgFhG2njPaWr5QgyA15gDWIKLidfP1g0a0cssMFJ2fbxcIcj\nMrSnvmXAEiY2o5aqSj1dsQbwTcGxQoJUTUi2PbjnWegXItNWxjEAfXhpzwFS\nJr2ZjEJApj+ut6+oFVp5oJWrb0ne9bSQLDtRLo/O0itMwqBzEB0pdAJOJ6jW\nlwmRRbaXUx9rCNoMtx3O3LNA7aO+uZcTfvxB1EcFR+6NTxI+vYIB+K0s3xgT\nvGjb\r\n=l7SS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApQTS0pVf6u+OiKro9V+zhPApNUS/p7tEcdfg++L8MNAiEAnsEp1eBxqX8IYwkKr4DrAg1AQ8hLWP8ymAeb/ZUs3Go="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_1.0.5_1617646430240_0.610412032205589"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "stack-utils", "version": "2.0.4", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "dependencies": {"escape-string-regexp": "^2.0.0", "source-map-support": "^0.5.20"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "tap": "^15.0.9"}, "gitHead": "0ec85bbb69c7aafae1398fde7f8a007db4fd1858", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.4", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"integrity": "sha512-ERg+H//lSSYlZhBIUu+wJnqg30AbyBbpZlIhcshpn7BNzpoRODZgfyr9J+8ERf3ooC6af3u7Lcl01nleau7MrA==", "shasum": "bf967ae2813d3d2d1e1f59a4408676495c8112ab", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.4.tgz", "fileCount": 4, "unpackedSize": 14432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLxRCRA9TVsSAnZWagAA1+QP/RO/FMJx8+YyiMMTrO11\nNFabXYYF4BQQkHv7BKSGd9Djow/hTIyu+w3wQ0/Vr9eleccFib2r58tBREAV\nt1aj4iv6lRzInP0b6vh1WdYT70Lu4RncLlL0q9EYrOhv06BWlKK5ikUXMXkP\nXnjDjrDnxDnXvVN7m8cGk2eDiPt68FaAipU9FdXXtygYJHLC0oaIsx7L0WSf\nilrOp4pXGvTPUH+cVe4VlrhQb5ZE9uq05hm/7nrAN4CR6fvqF86H8CrwTlom\nKwbY/L17yYNexkkkw6dg+jnPP1MZeluI57uzXCKyNnm9xuhvMJhs31DnJyse\nhSVeTwf7P7b1xGwRJPE5AQ98wwgAp0Pi0LQyBBerxL4FYcbhqXX9A4bZxdyO\nN2kShi9yMr8odD9D3davoqiNKeyJAJAmQChDc/4CQDqS/ABBOS99vlRjA2Ic\nTQ0YURCNdu45LNLAUAeZ/39zXqLOYayazx+AhA2tygh+2XM3KHxlsqDcHWB2\nhbBgbamh9QDa/fO/yhQ2sS6f1iNGwEaWw0sVBZGFoE3vzyKzWCsr+kBlu6/u\nStPIWWjVdtjmtuBNlUmE46VUlgoKrtELwaG+qgWjhbfJc5/qYAlBYpq8gekQ\namKPBpbmU5EDs0y7pY6iWP05naZ8NlWkkYhaVG/xCdCx96PCRpFisbLUphny\n5fnd\r\n=uliq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbLp8RvCFIkiEmYQ/8aJK+VF32pegFXQoe3tS9TC5NzAiAmPxD/ypscINT/MKjqtSHf4ZHpyA+BcGcrFEpJ2DeHAQ=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.4_1631632465223_0.8048252870081376"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "stack-utils", "version": "2.0.5", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "source-map-support": "^0.5.20", "tap": "^15.0.9"}, "gitHead": "536b9a263ab340a280b613277521b8cdafc46102", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.5", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"integrity": "sha512-xrQcmYhOsn/1kX+Vraq+7j4oE2j/6BFscZ0etmYg81xuM8Gq0022Pxb8+IqgOFUIaxHs0KaSb7T1+OegiNrNFA==", "shasum": "d25265fca995154659dbbfba3b49254778d2fdd5", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.5.tgz", "fileCount": 4, "unpackedSize": 14432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh277qCRA9TVsSAnZWagAASwIQAJgRDC95J1ptumJQ8keK\n7W575ktfQwBEzuwkch9mz0y8Rr4F2vl1EhjFiDXoIREgoiue4GAF2b1w4dar\nMzsShBw5yB+YbfbEwtDz17Xw9Q61L3tH4Rm2Xj+SNZnDO2YTMPmJAsElOndR\nNo6tJWT6aNz8aC1tg6bMZjBo1SXykrMb380kP7rz07QSMU9Gd2t3CoysmvIA\n4XMtlDUiTmVg9Tw1MGqR4RSuYctEGg77prjTp0MYp+gg6nN95ucbPfbdYqHa\nJRPeTb0A1At0ht7wd1CUNA9XJZlqd2U5zpnmts6hxUIq/MxT3M/CiE6uT+Hk\nc8q90B3vCt/poFDPIOHaR7TBFa3SObm1IdnSQfZbivFOKhlCXoiJDtaynhXQ\n7KrJJh2UlEPFu7iJib422xJZG/r0fVAvyhVl02dGPZhsZbQ5dCsCbw7xImoS\nJNpYzuL1Qnmlsg4zaX/+ErJBISlSvhey4RK4z3wdrYigUJBqZ9/JXh5/CF+H\nbYkIlxQR7ksXziCPKcNZoCGkw0XD4pc8r6QVwJdKbh0pDvDWty9dUVVvtu0e\n0OYBvmHUJp1YJFrlRXpMt3NRLc6hvI2Mj/cUZKdDDfY0OUQVBKioJLsLDIiO\nAXp5i2OmcWL7SlUWaDinzST96YLyMTY95Td0L17oVeqOznbfaV0+qiU8hl8o\noWER\r\n=ljHy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIoJX2f8mu6cIpJeqcVGOObZ2HZ0WaNaKSRVI42+Ky3AiEAtJbxlmZ5JDJTNVCK2NDwsgMh0BpcGsZtiuaB/iKR6ds="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.5_1631896353329_0.14363275142004728"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "stack-utils", "version": "2.0.6", "description": "Captures and cleans stack traces", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "source-map-support": "^0.5.20", "tap": "^16.3.0"}, "gitHead": "49817bdb87f5fd4096673933803b0bb1e26d32da", "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "_id": "stack-utils@2.0.6", "_nodeVersion": "18.12.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==", "shasum": "aaf0748169c02fc33c8232abccf933f54a1cc34f", "tarball": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "fileCount": 4, "unpackedSize": 14600, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuxUgyi/nNCqOFjfocXA2kgIbvJa87EhZmQcsKNvZkfAIgGI/7JgMzi/7wsWroEPurJvAmn1V0hGC2jMqXn5Khu+g="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjanhKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv+g/5AVXjcv2uYYrTyLswHIuUkBX+Mp5ZiswZuK0ICeDa1yczy8yH\r\nEYzRK5soudwwmL3D/bFV5pYBTlNnBKcgh4FKwOeL/1JsfjfmOpial0IMn3Gu\r\nx1CosUGcKrjFNJyPPqfAnKgvJ0Y3UhKK+ACRXLaCtNrsatVE7c832BCzPbUO\r\n2kx4oUNXKTYyLIPERfcsrTbz31pkMz2QGu3Wq4jByPLmf6EDCZhB26Oq8YYb\r\n1Chrlgkw9gUpdLAJpWZE2wtaQq3vzp48X1wYh+zLhZ5ZA8clCOFkBaP68DaO\r\nnlBuFhlQEAZZ7aS8wOlGSBL26oFDU9H1kaHxwFavO5EdJOy4ELVvFH162ciZ\r\nxzGj0dFPZP6dsbl73IQyidu6inTmg9bGCtRIP4UQwhZuL4JMC+ijGDQOEcLT\r\n5qqEV3ixZnHMfHFIC2v4dhjbQfMONWjPlW1oAczKqRr95rOcoWvXng3LIcoJ\r\ncmjJmVCRl1eUg+UpR68Z4rvcVk5NBVcPhwNmCY0uBvU64HF2bStNcY9ccEbF\r\nrS8G8ryq0i+zkzI6zoTAqT5tyUEopUEj3b3xDG2/CiCiVFbaHmHeAi1lj6qP\r\nYCuqpzDx7xbnMREywyTywoK9DyW6AJN7BciJHWYYtJFhy0eb696ZVSVJwlXY\r\nNcjqiooGM3aVe7wVv7872UFA+tRnbtc1zRQ=\r\n=Dq73\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-utils_2.0.6_1667921994191_0.7621443645020585"}, "_hasShrinkwrap": false}}, "readme": "# stack-utils \n\n> Captures and cleans stack traces.\n\n[![Linux Build](https://travis-ci.org/tapjs/stack-utils.svg?branch=master)](https://travis-ci.org/tapjs/stack-utils) [![Build status](https://ci.appveyor.com/api/projects/status/fb9i157knoixe3iq/branch/master?svg=true)](https://ci.appveyor.com/project/jamestalmage/stack-utils-oiw96/branch/master)  [![Coverage](https://coveralls.io/repos/tapjs/stack-utils/badge.svg?branch=master&service=github)](https://coveralls.io/github/tapjs/stack-utils?branch=master)\n\n\nExtracted from `lib/stack.js` in the [`node-tap` project](https://github.com/tapjs/node-tap)\n\n## Install\n\n```\n$ npm install --save stack-utils\n```\n\n\n## Usage\n\n```js\nconst StackUtils = require('stack-utils');\nconst stack = new StackUtils({cwd: process.cwd(), internals: StackUtils.nodeInternals()});\n\nconsole.log(stack.clean(new Error().stack));\n// outputs a beautified stack trace\n```\n\n\n## API\n\n\n### new StackUtils([options])\n\nCreates a new `stackUtils` instance.\n\n#### options\n\n##### internals\n\nType: `array` of `RegularExpression`s  \n\nA set of regular expressions that match internal stack stack trace lines which should be culled from the stack trace.\nThe default is `StackUtils.nodeInternals()`, this can be disabled by setting `[]` or appended using\n`StackUtils.nodeInternals().concat(additionalRegExp)`.  See also `ignoredPackages`.\n\n##### ignoredPackages\n\nType: `array` of `string`s\n\nAn array of npm modules to be culled from the stack trace.  This list will mapped to regular\nexpressions and merged with the `internals`.\n\nDefault `''`.\n\n##### cwd\n\nType: `string`\n\nThe path to the current working directory. File names in the stack trace will be shown relative to this directory.\n\n##### wrapCallSite\n\nType: `function(CallSite)`\n\nA mapping function for manipulating CallSites before processing. The first argument is a CallSite instance, and the function should return a modified CallSite. This is useful for providing source map support.\n\n\n### StackUtils.nodeInternals()\n\nReturns an array of regular expressions that be used to cull lines from the stack trace that reference common Node.js internal files.\n\n\n### stackUtils.clean(stack, indent = 0)\n\nCleans up a stack trace by deleting any lines that match the `internals` passed to the constructor, and shortening file names relative to `cwd`.\n\nReturns a `string` with the cleaned up stack (always terminated with a `\\n` newline character).\nSpaces at the start of each line are trimmed, indentation can be added by setting `indent` to the desired number of spaces.\n\n#### stack\n\n*Required*  \nType: `string` or an `array` of `string`s\n\n\n### stackUtils.capture([limit], [startStackFunction])\n\nCaptures the current stack trace, returning an array of `CallSite`s. There are good overviews of the available CallSite methods [here](https://github.com/v8/v8/wiki/Stack%20Trace%20API#customizing-stack-traces), and [here](https://github.com/sindresorhus/callsites#api).\n\n#### limit\n\nType: `number`\nDefault: `Infinity`\n\nLimits the number of lines returned by dropping all lines in excess of the limit. This removes lines from the stack trace.\n\n#### startStackFunction\n\nType: `function`\n\nThe function where the stack trace should start. The first line of the stack trace will be the function that called `startStackFunction`. This removes lines from the end of the stack trace.\n\n\n### stackUtils.captureString([limit], [startStackFunction])\n\nCaptures the current stack trace, cleans it using `stackUtils.clean(stack)`, and returns a string with the cleaned stack trace. It takes the same arguments as `stackUtils.capture`.\n\n\n### stackUtils.at([startStackFunction])\n\nCaptures the first line of the stack trace (or the first line after `startStackFunction` if supplied), and returns a `CallSite` like object that is serialization friendly (properties are actual values instead of getter functions). \n\nThe available properties are:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `native`: `boolean`\n - `type`: `string`\n - `function`: `string`\n - `method`: `string`\n\n### stackUtils.parseLine(line)\n\nParses a `string` (which should be a single line from a stack trace), and generates an object with the following properties:\n\n - `line`: `number` \n - `column`: `number`\n - `file`: `string`\n - `constructor`: `boolean`\n - `evalOrigin`: `string`\n - `evalLine`: `number`\n - `evalColumn`: `number`\n - `evalFile`: `string`\n - `native`: `boolean`\n - `function`: `string`\n - `method`: `string`\n\n\n## License\n\nMIT © [Isaac Z. Schlueter](http://github.com/isaacs), [James Talmage](http://github.com/jamestalmage)\n", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:33:37.156Z", "created": "2016-01-04T05:15:37.432Z", "0.0.1": "2016-01-04T05:15:37.432Z", "0.1.0": "2016-01-07T20:24:53.362Z", "0.2.0": "2016-01-07T21:19:49.097Z", "0.3.0": "2016-01-14T03:54:58.328Z", "0.4.0": "2016-02-05T22:35:56.320Z", "1.0.0": "2017-02-02T06:18:07.294Z", "1.0.1": "2017-04-23T20:32:26.102Z", "1.0.2": "2018-11-14T18:57:24.505Z", "2.0.0": "2019-12-13T06:49:08.966Z", "2.0.1": "2019-12-13T06:50:32.156Z", "2.0.2": "2020-04-28T15:16:17.414Z", "2.0.3": "2020-11-13T23:10:10.505Z", "1.0.3": "2020-11-13T23:32:07.930Z", "1.0.4": "2020-11-26T00:52:45.001Z", "1.0.5": "2021-04-05T18:13:50.414Z", "2.0.4": "2021-09-14T15:14:25.540Z", "2.0.5": "2021-09-17T16:32:33.477Z", "2.0.6": "2022-11-08T15:39:54.449Z"}, "homepage": "https://github.com/tapjs/stack-utils#readme", "repository": {"type": "git", "url": "git+https://github.com/tapjs/stack-utils.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "bugs": {"url": "https://github.com/tapjs/stack-utils/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"timdp": true, "monsterkodi": true, "lukvonstrom": true, "flumpus-dev": true}}