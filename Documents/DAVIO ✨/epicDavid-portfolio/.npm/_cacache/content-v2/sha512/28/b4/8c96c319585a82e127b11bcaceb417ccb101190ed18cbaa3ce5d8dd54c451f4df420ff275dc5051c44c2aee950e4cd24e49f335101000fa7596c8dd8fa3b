{"_id": "create-jest", "_rev": "29-181de9409048a73765d1a3e7c29f4c55", "name": "create-jest", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.4"}, "versions": {"0.0.0": {"name": "create-jest", "version": "0.0.0", "_id": "create-jest@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "aac404c8eb651ae521580580c682c518c18a1c11", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-0.0.0.tgz", "integrity": "sha512-xuAey0r8MAXbdmzvep7GHRERHH+wSH/bnxJLruZQ2htfwuU7oJzMoFab+cuL83eEUOajpYCjZsA8MGJVgDHeOg==", "signatures": [{"sig": "MEQCIBVlNDD/EFBKqng467ZX9wAnZl7RI/nwKk5oPVpgfivuAiAV2J0qcz3XxDkwwdUzvLyBPyzX1KBczUPUHn5fKZ5x6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "aac404c8eb651ae521580580c682c518c18a1c11", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/create-jest-0.0.0.tgz_1494406201430_0.19879153603687882", "host": "packages-18-east.internal.npmjs.com"}}, "29.7.0": {"name": "create-jest", "version": "29.7.0", "license": "MIT", "_id": "create-jest@29.7.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "a355c5b3cb1e1af02ba177fe7afd7feee49a5320", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz", "fileCount": 12, "integrity": "sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==", "signatures": [{"sig": "MEYCIQD3aWu2M4pZwx1o0kpFC52BH8rvMrjKv7FB0NLbolTBQQIhANSrum6AvK4ka8qpNuPmTbpGonWQVrkY3GqtPlSyonXQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15890}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "^29.7.0", "@jest/types": "^29.6.3", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_29.7.0_1694501044478_0.7705697422644979", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "create-jest", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "create-jest@30.0.0-alpha.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "095a81cfdfa6568a601a53ce7ed15b8ad416c972", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-jBfpm/sYj1Y9GkGBkcp+QZm5V/n1ObzRmBWUkxzNPhaHoposC/RMrrCCFyvEXZTlQeVfdJz9deG7rpFhJ3k8pQ==", "signatures": [{"sig": "MEUCIDWek1VDXQI+MsEDY3sh+mxvBCFwjmiGBUCOjuG9vnpFAiEA7A3zWvfaqKd4MQX/2jHZFgyHDrXrjkFn+9y5rD081yE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17376}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.1_1698672812916_0.07710999558579945", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "create-jest", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "create-jest@30.0.0-alpha.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "2abf34adb7e18e83146d293313121cbc292e9913", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-B8671F5cvdXpiG6ZmJeRLpJm1rvFUOorIcBDRy6luVFumtY6Yvkbt97ook7dkVNSl+fgmeFAHKlFRPfbrnU0aA==", "signatures": [{"sig": "MEYCIQCASHjZCGW8PrQPH+PqY6Pcl657ifF2hOpd9Npyi5VxLAIhAO3IUgC2dlN5/kTn+kD5XrG9uA0PDRM2mGGo+Rs68JxL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17376}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.2_1700126931506_0.3418768718429863", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "create-jest", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "create-jest@30.0.0-alpha.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "58b51d44926e0d8a72346e440bd4b560576b179c", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-7E1setBZ4Oa9qt3xqUVKcFtlZsjz8D7E5j+P8p1vxZinTEB/L/ZDXOI/XpUOG2ImeyIkR125GXVbOLKEoMCN7Q==", "signatures": [{"sig": "MEUCIQCTwMgWXFT9SaIVOegVQVGiYTV4MvkbK2dPHjHxD5iBGAIgVKSw/Ee34SwixsNxtI2Hl5l0KWBpXsGE5Phfy/DKHUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17371}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.3_1708427384557_0.4822629321891454", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "create-jest", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "create-jest@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "1dc09a6dcdf664490b8411be9621cc804ea2d5ef", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-k/w81ojKetslIlCTJMVrdsy8Ypw7Px5wEi+/WPuMV3YIiXEfDxLr4wDRd0K8qnCHsxA+08rCqb1RE62vxZWxwQ==", "signatures": [{"sig": "MEUCIQD1W45Jemku1jCdhsxHWFdvknJgUPJvd4SWOeHDBlNgcQIgGz1iPzsioEPxb4FaAuosLFEwKvIhfddXEJAers1Yr4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17401}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.4_1715550247915_0.30803035039305793", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "create-jest", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "create-jest@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bin": {"create-jest": "bin/create-jest.js"}, "dist": {"shasum": "d5fbf81acc11b85dcf11dc7e622994a1e662bceb", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.5.tgz", "fileCount": 7, "integrity": "sha512-asGT2AWqFvQH4lc5qfL5T8MQ/nvCso08HDF8BBW505BNQXA1DU84cku1Cj9FnAfXnnBhbGiL5uT/f85PM9A9XA==", "signatures": [{"sig": "MEUCIQCT4X+iKVVY13aqEacpxdSGTGM2X14cJrNFEik0crBIuQIgPhzl/sonMRQc/KVzXqxwQjGUDHAZYAEUog7VZx2NeG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17401}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.5_1717073069781_0.3712557799963243", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "create-jest", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "create-jest@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "09204f4ab3b38ac8a4790e4d7c1967b8861c5cde", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.6.tgz", "fileCount": 7, "integrity": "sha512-XyqBa3NFiD/gqvMaN+bQDC+JYOZF0IOFs1jw6UVbsIed1F44fFLcpU7fiKg+9GtQ5cR5eSqNOI96rlzHrURilA==", "signatures": [{"sig": "MEQCIG7vXsN6JuAaE08nu/4wBLoWILFGTMSmvp7LpCDB4v1VAiBNlK71CIuI25nWbCi9U/aY/Vmol7ZqVXczcL7oA4wfCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17387}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"exit": "^0.1.2", "chalk": "^4.0.0", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/exit": "^0.1.30", "@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.6_1723103007316_0.5574668469485913", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "create-jest", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "create-jest@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "0a50e5903945ed918d0131d6a4ef4e40c9924fce", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-alpha.7.tgz", "fileCount": 7, "integrity": "sha512-2q2kkoukLs/oSJDAxMMVk2CQfxWUe9s9IS9rkE5dT+Ki1BePdvYmTa6mkdC83UiIt4QqAOEJ66YfOSyD8Tl+6A==", "signatures": [{"sig": "MEYCIQCp/J2Y13VeN7HnWgwbHjPa0GOp4jyBcS4zxN7anXq3yQIhAJhMn7HD52dBo9gdj0S0dWZLPRN3Tu7edYk7Ww6GSUdm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17365}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-alpha.7_1738225735150_0.9126579276625169", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "create-jest", "version": "30.0.0-beta.2", "license": "MIT", "_id": "create-jest@30.0.0-beta.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "2db61800aa840f9706d0463b62cae78dc9d3437f", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-ijaOphn1chWDHKyezHWCN3RcIKJFO8uPaU+5wURAECUjKxYZak81hBKHRvWCBqS0ZQYh2yt2RBwIDFRvF2F5zg==", "signatures": [{"sig": "MEQCIADci7tCtdDgReLzm8FgQ7o17mDOhsIEQA0dIqfHEvfhAiB8tximtdjT6tltiX+vXlxWahuoLeSk/xrVfSmr6A0d3g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17229}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.2_1748309022362_0.9650965027662857", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "create-jest", "version": "30.0.0-beta.3", "license": "MIT", "_id": "create-jest@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "1b7981618805a03ec2b7e6d5eaaa1ad524a293d6", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.3.tgz", "fileCount": 7, "integrity": "sha512-2Yr4Z//jeAfFM57YQiLrtUPTiHQ0e06sTVOgbZ/paLgGKzQyeshl5L+CuA1za+ZBFEmMGEV5Z/gg+rRCoXVcUg==", "signatures": [{"sig": "MEYCIQDUm3Yzftq5ye62L9fb375FOnwb8nT/ln9C1sSdDQ1ksAIhAPw5wjbfxpNVBFG2p3J95mt9XWBShYMI600oDqII4OnB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17229}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.3_1748309289300_0.7457382304623199", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "create-jest", "version": "30.0.0-beta.4", "license": "MIT", "_id": "create-jest@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "e13a984cff9ae26ebce787f3650a82724492d78c", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.4.tgz", "fileCount": 7, "integrity": "sha512-5S8RXlgXAoVyNEW+C1UC2dZaw+Myp/mTwHnoVI7+GimWHlwCpd6+F8fVV5bP7gBHpqXLchmc963yQaYq+AgDSg==", "signatures": [{"sig": "MEYCIQDJ6j9tY/YyXeOXX4JO3C67iad+FlsV/VpWP51CCiEGhAIhAKKjFgn63kybUWYr3k75gb7r/ShbYrjtRnpytvQYIMBw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17229}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.4_1748329486142_0.17962856351322487", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "create-jest", "version": "30.0.0-beta.5", "license": "MIT", "_id": "create-jest@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "ac0ed752f40afdc1b1cf321e0ff6e8416e0b3530", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.5.tgz", "fileCount": 7, "integrity": "sha512-15gwbZ405n6OezCxeva192w04CCV2uBFbNa1nys7OUS6d7LdpxXXhRhufh9OIC/tC7+R9Mn2BH24JBlCjHTJHg==", "signatures": [{"sig": "MEUCIAQfIzPa/iP929qq16HdfT2VRe4ORaJmQlp42K7ikjsWAiEAoirb5yCmxcLoFvFVkW/8C6Xm+vV7ijdhpP/AmQgEh+g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17229}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.5_1748478634938_0.2649445090466953", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "create-jest", "version": "30.0.0-beta.6", "license": "MIT", "_id": "create-jest@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "a9ad33337603c28c47f9f7281f6bbb946162e148", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.6.tgz", "fileCount": 7, "integrity": "sha512-qy3RRFZ/JbY3pmZIdM/owXrIZooT7tUXBYtciljBDl1gcldskcumPCj1AcHB5mIRQQGLWpC52tX72iTLXQvJ4g==", "signatures": [{"sig": "MEYCIQD4m6Zqu6rJvPlV5JNj2OrGJqtf0NlNqvXUcQS0ZjCzcgIhAJKcZQ/59ooAEs7/GendeAHl8W8/CWdgOvI+RRPSBdeC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17240}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.0.0", "exit-x": "^0.2.2", "prompts": "^2.0.1", "jest-util": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "graceful-fs": "^4.2.9", "jest-config": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.6_1748994673335_0.39870751142173044", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "create-jest", "version": "30.0.0-beta.7", "license": "MIT", "_id": "create-jest@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "319453113b5e0d5d47ae0cb8864eeef2f4345888", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.7.tgz", "fileCount": 7, "integrity": "sha512-uC6UZsq6gnEpfK8XxP1sN3c7OA+o5WTzLVYKkNnnD3AAuwe09ihg7GIvTR6SKGJxjilHymOFKAEoJxFMcDveKg==", "signatures": [{"sig": "MEUCIC3ozgltdT6pLNxFXs1N+J3Uq5jHjLuScuDTOFU0WkBxAiEAnIcuyPXJjNV38/dwV+ehLGSKQbrWuA71n+SIO4F7ULA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17241}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "graceful-fs": "^4.2.11", "jest-config": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.7_1749008164877_0.44614875115375363", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "create-jest", "version": "30.0.0-beta.8", "license": "MIT", "_id": "create-jest@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "3fd05bb2f03e403e86ccce18c7b6960884baa654", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.8.tgz", "fileCount": 7, "integrity": "sha512-KvIL6YLoNSTczirFrfl6P1nPHWSCwzDhgKXgrJMUjlNiVx90ItMmqf8a1U6EoMb+4upmDdz0Zil7Ko804D8ybg==", "signatures": [{"sig": "MEQCIEk4lgeWxY+irGai3zzW4BtmoEE2z4WycrMmMA11rKzjAiBPm9nYopXMy8DjdJBBbRmf+rMYq1cFEPNzSIT5Ckk7VQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17241}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "graceful-fs": "^4.2.11", "jest-config": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.8_1749023614084_0.63982503104332", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.9": {"name": "create-jest", "version": "30.0.0-beta.9", "license": "MIT", "_id": "create-jest@30.0.0-beta.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "c8120a20d714dc9d702709895998da81e3749e6d", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-beta.9.tgz", "fileCount": 7, "integrity": "sha512-Lv3eCtmEWlTJBvRlEgQS7PfPReSE+/+DGmYcogeJ2Ja6IzhhRwl/np/N8J7Xp23tGuDMdsoKWFo/vD4P/3wl7w==", "signatures": [{"sig": "MEUCIF7QRFl6tGMEVa9Qlem07NO1ls/n4SXKd2O0KaNH1myWAiEAuzB80U8HtKCBnTTf/ocn7w26CEg3UIIOKxEzcxS1hCU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17241}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "2f52a9ed429fb8797a99868860430d55db6d5503", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "graceful-fs": "^4.2.11", "jest-config": "30.0.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-beta.9_1749109245394_0.9574198763913198", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "create-jest", "version": "30.0.0-rc.1", "license": "MIT", "_id": "create-jest@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "d8a94a973ced9faf78ce6e63826badc8fed23be4", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0-rc.1.tgz", "fileCount": 7, "integrity": "sha512-sxV9E36CVUu+wpwFjim0SQbM56Js5j1DoEpRwAViN/JVOKpgxVSIA7m5ZXlJnUqv5pc/pW2CixxUSykqKDxbjA==", "signatures": [{"sig": "MEYCIQD8x2KQ65fd7dMweZUVy9QmLamB5e8ebnkX/7aV5cR8ngIhALCwLuGBz1WRUfWkFpR3k0e6W4Zw9scchgXqvbLeZZYK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17235}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "graceful-fs": "^4.2.11", "jest-config": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0-rc.1_1749430990851_0.8955791182324688", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "create-jest", "version": "30.0.0", "license": "MIT", "_id": "create-jest@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "97d2ad8d2ed0acc05c020f0a17a957122bfbd130", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.0.tgz", "fileCount": 7, "integrity": "sha512-RMq7dtmFa8aBX9lP6bULS3m0x8B1PCeurZOnbXqYj64J43jqM5eKX91orkRMQJmo6BDRDdLCI0DRVjJJChVIGg==", "signatures": [{"sig": "MEUCIGlIs55UpUfJhFvxxaWyx6R56RKvPPOeJTVT+QPZpJ0+AiEAq085/L05AmdrRA763saEWDMHhwr5lozrQt6qQeLIijk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17213}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.0", "@jest/types": "30.0.0", "graceful-fs": "^4.2.11", "jest-config": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.0_1749521777359_0.4282497117423638", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "create-jest", "version": "30.0.1", "license": "MIT", "_id": "create-jest@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "36dc5e03ff128cfe261236a9fcb019969db20fbe", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.1.tgz", "fileCount": 7, "integrity": "sha512-5tX2fj816az2fwsY+ixr6u/n3M/0TZPsADQ/SeCxkjBP+lPJfRry16DsGlXTgnOe/AEoT+xVJmq1KXTGK7+gmg==", "signatures": [{"sig": "MEUCIFaRTGHLYOUb4l2x+Cd1MdIYY5WAy5utwOS8h8D75NqMAiEA8JNyqhSFLw3pTSjsGU6XOTllLI0eU6LAId3xOnK+7xY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17213}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.1", "@jest/types": "30.0.1", "graceful-fs": "^4.2.11", "jest-config": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.1_1750285914108_0.6930863747104361", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "create-jest", "version": "30.0.2", "license": "MIT", "_id": "create-jest@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "6717f884e64c9145372835454c489df1b5b57669", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.2.tgz", "fileCount": 7, "integrity": "sha512-IbsoktEC6jREQ8I7xu4eO6mjxoKr3CqvJJ8ucGTc5U4r9gUSHAPDZOSZnrWL3gZodH221ibzNnWMhySsHl2K5Q==", "signatures": [{"sig": "MEUCIQCgHuRNG7zWSpOo6GxAf5UBIA57Q0sIliVcZxLShUMHcAIgd2Q7lGRRDgzqHJBh1RJ9tpWlddu0gA37RbbtVbaMl3s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17213}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.2", "@jest/types": "30.0.1", "graceful-fs": "^4.2.11", "jest-config": "30.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.2_1750330001096_0.4871591098397434", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.3": {"name": "create-jest", "version": "30.0.3", "license": "MIT", "_id": "create-jest@30.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "bin": "./bin/create-jest.js", "dist": {"shasum": "725ead68467b85aa797445538fe5eb9d7869bf4b", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.3.tgz", "fileCount": 8, "integrity": "sha512-fjO/mP3ZxmYUnidmTlFeo2lscpgCUtV1WvvQymtM0gq+yY14CfIDVe7cyHhjL2+HAC2pG6kpgve7y9Mg/Yl8bQ==", "signatures": [{"sig": "MEYCIQCUNshhYuJIAKwJRryIt9Y6IlaggldRyCAO6MeZ69mpjQIhAMhK5C/PWad1bviIbkFdUsOo3cSh1kaY1qqLq0x4kOC8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17578}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "gitHead": "d4a6c94daf4f6e63c949f2d0ed907aeaee840d2f", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/create-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "description": "Create a new Jest project", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "exit-x": "^0.2.2", "prompts": "^2.4.2", "jest-util": "30.0.2", "@jest/types": "30.0.1", "graceful-fs": "^4.2.11", "jest-config": "30.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/prompts": "^2.4.9", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/create-jest_30.0.3_1750814530930_0.14396365259547061", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "create-jest", "description": "Create a new Jest project", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/create-jest"}, "license": "MIT", "bin": "./bin/create-jest.js", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "dependencies": {"@jest/types": "30.0.1", "chalk": "^4.1.2", "exit-x": "^0.2.2", "graceful-fs": "^4.2.11", "jest-config": "30.0.4", "jest-util": "30.0.2", "prompts": "^2.4.2"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "devDependencies": {"@types/graceful-fs": "^4.1.9", "@types/prompts": "^2.4.9"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "_id": "create-jest@30.0.4", "dist": {"integrity": "sha512-5uYiU+C+s3Wt1I7dfMd6OKAIrVh/WbhyK0BHIT2/2aiUnEvDaLwz7PkTt5qReZ3yiH5n2pa01dfiUqd1dZErMQ==", "shasum": "91b7a72a12c76267af902b23ea9b2e5ae98efb21", "tarball": "https://registry.npmjs.org/create-jest/-/create-jest-30.0.4.tgz", "fileCount": 8, "unpackedSize": 17578, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD1qO/bD8NpTZwsCvBju4E1disTBL93A1NuNzYBXDLaEAIgbpdsrjIcsQgo2y5FqkDFom5TRDoOoHYUIlXKPTnCXcA="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/create-jest_30.0.4_1751499967757_0.3333509655389395"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-05-10T08:50:02.078Z", "modified": "2025-07-02T23:46:08.129Z", "0.0.0": "2017-05-10T08:50:02.078Z", "29.7.0": "2023-09-12T06:44:04.713Z", "30.0.0-alpha.1": "2023-10-30T13:33:33.153Z", "30.0.0-alpha.2": "2023-11-16T09:28:51.718Z", "30.0.0-alpha.3": "2024-02-20T11:09:44.691Z", "30.0.0-alpha.4": "2024-05-12T21:44:08.076Z", "30.0.0-alpha.5": "2024-05-30T12:44:29.928Z", "30.0.0-alpha.6": "2024-08-08T07:43:27.479Z", "30.0.0-alpha.7": "2025-01-30T08:28:55.342Z", "30.0.0-beta.2": "2025-05-27T01:23:42.596Z", "30.0.0-beta.3": "2025-05-27T01:28:09.473Z", "30.0.0-beta.4": "2025-05-27T07:04:46.310Z", "30.0.0-beta.5": "2025-05-29T00:30:35.121Z", "30.0.0-beta.6": "2025-06-03T23:51:13.526Z", "30.0.0-beta.7": "2025-06-04T03:36:05.053Z", "30.0.0-beta.8": "2025-06-04T07:53:34.260Z", "30.0.0-beta.9": "2025-06-05T07:40:45.574Z", "30.0.0-rc.1": "2025-06-09T01:03:11.019Z", "30.0.0": "2025-06-10T02:16:17.548Z", "30.0.1": "2025-06-18T22:31:54.305Z", "30.0.2": "2025-06-19T10:46:41.256Z", "30.0.3": "2025-06-25T01:22:11.115Z", "30.0.4": "2025-07-02T23:46:07.934Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/create-jest"}, "description": "Create a new Jest project", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}