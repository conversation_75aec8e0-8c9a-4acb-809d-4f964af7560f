{"_id": "@babel/plugin-transform-shorthand-properties", "_rev": "112-9391c3e7f5de78b9b0636156a1ef7c9a", "name": "@babel/plugin-transform-shorthand-properties", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0a8e4c496a84d4b2e1461d4265bd09fadab639e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.4.tgz", "integrity": "sha512-PwZQL6GwdMGyTO4A/XuBF1MziRoDJ+awjlwRMrFTrN1rgFy32J/s92Q4OTY6TCxdMGAa3N8qSvVXxFodzc+gIg==", "signatures": [{"sig": "MEYCIQCxd2oJ4a5baqMdGmF588gM10OK1OHBS3Mfp34eojEqCAIhAL2UFz1k6EVAJPOpJnKIKQOOld1KHNhgIFb5uL4ouINR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.4.tgz_1509388528747_0.594475036021322", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "be582c9236e65b4a173c51117e4b125ebd84f2c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.5.tgz", "integrity": "sha512-XD6ICJZXk17G7ODpbZhA96/XAK0frcI9ZBiWYzsTkKyXfGGuIC4OkXPyjnTQGcOjOuY4vLknBLUGvIg3d+4kyw==", "signatures": [{"sig": "MEYCIQCXvSr6aRJhAsJrgLTtGholRhSyNvcjyhN52D7KGrukFAIhALj1jDRcmhXNwlDU+hgsPm2TLaGI+0AyMveW9antL4qu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.5.tgz_1509397025069_0.8909582628402859", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2131f3de24f701365b726d707070e5d3e19d82e6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.31.tgz", "integrity": "sha512-xOR7CJTjUsb/FgBEqQBuyHux4Fg9yyM0990DNuATtcFNrmcTgGR4X33Rg7OAKrhZWBb51H2+2m+iePzaJGbh7g==", "signatures": [{"sig": "MEYCIQC6FynsjPo4LQzoSQ1i3JgeiCGAGx08r8oQ6U0LTfkTHQIhANd9xf/rNSEL0IrOteCXRmWoRMvoCYTXQ9kg5f4TTFiI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.31.tgz_1509739437384_0.28831083606928587", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a5338542907ad2cfc5973fbb41e93f54e5fe6158", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.32.tgz", "integrity": "sha512-oLzdWzXqHrqCfGpFXoetpnaNCAzrqTnEysKYoAgv6f+R3su4y8KXXi80eg0/rTNwZ+i3N3hDC+Ipq5+eSVl4Iw==", "signatures": [{"sig": "MEUCIDUJLswE8qxouMSlBdkWZQ04f6okTInsJr/O/UYNl3Q6AiEAvVPW7RZNKk08UYQgjWSBW+PzPktD2NEEe3VWvIuwMWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.32.tgz_1510493602574_0.2861167057417333", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f665ea83eccac51a773a3abb476f2280c168e06b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.33.tgz", "integrity": "sha512-f93wIl10Ie/4ycGtj4oA5M6FM1nGQN2NdHozBUD3J8NGTlWMy9gL59DkxXfq8xyglUDNgmf5OP+aJIz4VJ2ctQ==", "signatures": [{"sig": "MEUCIQCX1S5oKI2lvRsHB0DzSdJth/6Hr+DWFuXIG8+yILrX1AIgM7NyrFfeu5bZsMeYzpg5PaPSS6fypuGrvIPTod5we2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.33.tgz_1512138504710_0.8231582322623581", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "98c14a87d02331ae0f110fa15ea8aa5455d0e0e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.34.tgz", "integrity": "sha512-ShonB4hSsHxSP3hhpyXIJShm2et0NW40nJCaSlZvw0yCPeIr0IEnwB4g8gjZXsYBUWLiEz3JanmWuZEIIrBBTA==", "signatures": [{"sig": "MEUCIQDMVoE5tZDd/0ioNxl0cZvU7SyWArcmJjGHssndj4VcZwIgaOtIzkGynCc8qFEUiRcsm3zDJFk7PXSts7UEzusHJ5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.34.tgz_1512225566157_0.5541731107514352", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "20077ee7a82d5845d4ecf03e2d11aa13f6b6a4d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.35.tgz", "integrity": "sha512-wjWlYd3fxEX1ptGYbxSvXAN+vfPEP7dSL+OXlWB0pVUXuZQYJu9aq9BNZl5gC4slMp2nC7HmHG4KzsTZ3sTCjQ==", "signatures": [{"sig": "MEUCIBzQ0LAr5MH7PxK5+9Wr14cjF8G/DjpJMwS6d3TK90IdAiEAqVHRJQ55V1vZzxh8aM/hNj+TdShEvpuhiJXPn8u+NKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.35.tgz_1513288071771_0.49933225056156516", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e91808349a2f7f59d93187a17e26b5df457fcbaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.36.tgz", "integrity": "sha512-iwyn7SuvUcp4uvm5mHC261kTOijMoTJwdj8R2d3piOVO9dUll4ENzg2xcXiZ6KkFHToaCHMKbCe7jXV0v4+BKg==", "signatures": [{"sig": "MEYCIQDP/qoMP8MFoh2hrrK8mwj3Xr3Xw84tchsWLHoEVhCKZQIhAJpLiNCsj/BxOkGsDaZJdWuDNv8dsHuLA75ewFy9gJXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.36.tgz_1514228685340_0.4063374511897564", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "39c39b513ed7965a4f7eb0b6569e3caa57e441b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.37.tgz", "integrity": "sha512-L7rWV6T/JkHqmFpH4s3BPArv4J0o4OjSC4vBTQQ1+E0vagsiCWVwIeQwvooSGQvLzomWMTzcWJluMsW8RXDz5g==", "signatures": [{"sig": "MEUCIQCuMP1HjLYuJsgoxwljb/2cmcvvzjQiEUJQcYz5iL/DGQIgO0uEe4GCX7CB3hR46oL3JUMEuZTtfl+2Y6ADOCPQinU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.37.tgz_1515427354698_0.6161871068179607", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e3c6a266632aa4e474b800531b6d26366ff22e7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.38.tgz", "integrity": "sha512-XeDeMOpDYRg69lErxWK+vHOQhZNU4Pui4HJWhsd7Sqw2MQL4/OYi6IQLfBmcJnGimnhUEQOONCyjcIkmeQxXJw==", "signatures": [{"sig": "MEQCIBNEPEh/uHkdhlBcPSomFmXQq5IyFihLtc/VXgN0PSWMAiA/LBiiq7SNwGPY58B+oI7akjBIOFqQ5Ut1FVnLyL70gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.38.tgz_1516206720971_0.2960007353685796", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "caa480ecb159481df8e1c405aaac865a54781630", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.39.tgz", "integrity": "sha512-XLBKvcWXYPq+TeAWRgR6RRS2pi0VAczOcVgtlCr3AcGIQTZKrfE/VPXnq7SGUpnyiJql4ANtR9zUFyr9L8lj8A==", "signatures": [{"sig": "MEUCIQDo24keUKVR3rRJAkCk5TRxa5qXKfxTrkVO6uYUNwaiewIgVfVac95J2NqBa4/P5iSwWXITW5tm3QgtLv0mQySuzCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties-7.0.0-beta.39.tgz_1517344056940_0.052896733628585935", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "421835237b0fcab0e67c941726d95dfc543514f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-1leHn9ST0PKFHwH7klJqGA76YPoqs3cR5zeJK6YGZETeX89YiAVtR+5JTSGhfI/1RR0Vcg9Tl1LnPpf7LmYlng==", "signatures": [{"sig": "MEUCIQCmXkUO1+Q0ptBO+vwhAZgCpvxUWwm8w7xPwMPe8eO7DAIgaATmXjOIlTZf/VItS4DB49P3CDd6PCSPWDAFWVUz9Vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2073}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.40_1518453702118_0.2202206269408049", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "93431505f2ea38245a0e7f4e7055253b240e49ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-737vv4OoBPhL7AXtkhL/E/kw894mx/nJFJeJ4fNjhLtWr/t2yBgvhhUP8wHAfUnnnsx5I0io6iz0ShQVxy4gLA==", "signatures": [{"sig": "MEUCIQCTvc4SCDB7o0eJ4QP2yCLYodyTcOwhDA6uuPOKIFnX1gIgRylttf2cp+oNzh7kRIW7MsRq5ke8R2rf3OxoIE7USRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2308}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.41_1521044776960_0.951030409684732", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fb0b66f4afd4a5a67d9d84a85cbf6f7fef0a7b4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-GID8z2s6R/XOHgaoxrKBH+zdBOBqFJTDYDS91w30fJGiHxVM4qFVOpYDNIMxmsjqW6bKVHyLeHBezp0OHv+9QQ==", "signatures": [{"sig": "MEYCIQDFIS8dcTb8qNj5InEWimV9RgxgHh9xXl95lur/vCQH+QIhALR0Kq8by47TIllX3TuejDcjwPKxwC2u+E5EgSKRCo+X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2308}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.42_1521147049699_0.5999531592884673", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "357a3ab0067b9689b28a0ad6490c8179f9a2814d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-eYfBjsW1wGmOaOul6I4vARWsRmcPqnpJBE+YvsGNCvI/qxmXaIOVKsJx9Nrrj9V6j6w8vkYiE1k6EXzCo//Osg==", "signatures": [{"sig": "MEUCIF3mcA7kP3E0cV5Qa1hqkzaFfpU9xqi5YYm6wHx3/jx8AiEAwbZZkDnxw0bwaTwclpiYAQOhwo8KTXEOMptwjZzSvbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.43_1522687709440_0.8892973423425157", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "42e2a31aaa5edf479adaf4c2b677cd3457c99991", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-LG213xsGpvCB09Tq7EMaO3COzyNhzV7Hss00UMXR3AId4EThwRoYiLKnekqoOasNdocN+09fKyH3cf/llJgZhQ==", "signatures": [{"sig": "MEYCIQCH9X/EPy2ErmlA6H4/PHLzs7fMHshIIYTUeG5kQCAW6gIhAJRlx1HTBX1z2e1JSmSSyY709TPptVU11RwZnGvxMawh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.44_1522707611591_0.7375722685349504", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "41a06643098453db64cfa68925bb89cd5dd1b3d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-XrBn/3x/bU7H1wM1yQQcTMMweQaZmmA7VvFFFNnShg9HlMCEdt7TLt67bu34fskOI5HlwIrOtnpqU1yPiIMbHA==", "signatures": [{"sig": "MEQCICQE7Pxv6PtVNLp7x4uOGAofDpgyrzJVhOycFmobepANAiAEHZ5lIwJyXUsAwLzcjr9uckS8lqRxXu6WynzAHrqK+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1xCRA9TVsSAnZWagAA3ZIQAJcHPPUBw/Cztk1R56oq\ngdPLiJ+Tq329uDWZX0hjSCVDL0Oq32SYyPH3gjBws8vLy7CU3JjoTh0zMYTg\n5k3UPahS8tKAo6df0XqPwn0lrYhvZm4zeBYfwjLkt4J2QFn6GyR+NbTS34vX\neFS2igGjdj+MzQlDkp2gWuuTJbogA1XMqCXQAL8ikSoFuLJJOH/8Jzr28oqf\n/JETZ5GJ8sljP1M6811uAEozzTmsRiEQILx9ohzkzyzMvJLEy15Y1aB3e9Ws\nNIcosOxV8b4OM6beSYIA1Vktr9VhbZgf4B/WCHYtFAIfK3HnWfHpsukv1z2d\ntphYNssQ7DyauedXHk2x+Mak8Xn+lAJsHyDYzP7kbfZ0DMoE84KNbFiW9suv\nFFvKZLLq49EKVt6mjkBKIGWoZjm3ePNegjotrDiBRpDJQ9zzjIL7pBc8uOGW\nBQfQ6cE8+uwSYwPKCLRrK+69y2uyTIMqaIBOH7Z/N94SDN7md4QA+msQPptZ\nFLEwhKJMiJUqk1czMcwpiAzJ3O0+xKhAe8HrdLJlZJQWOnr8WmBN5oPLSiFE\nFwGKk4Ho4m8C8z1pncaWiXolWNBPJq9KfIpiLSheGE2OtTuqRXk6pVc3NoNr\nKdeOyo3tvk6XSYiTl9FhBEJ2dQT85Z6icc0SFZssPwcnGVmnLuvcnmjyL12o\nP+2L\r\n=uIfs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.45_1524448625186_0.7514938462998106", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aa21512b0fef7b916fc5cbc87df717465c25515c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-1QkKFWPsjrvMppycLwjPBXF+usSnGvbTxGe0Q+eIzcZyhabwGCsCgkmDIKMisPSAi6F7bM5H1S8VbE85IW3oRg==", "signatures": [{"sig": "MEUCIAW9kXMpowr1NcR3AzskJ8UTL83NMhiSgNBDTSqiGe+wAiEAqQIJ0ZdUxmQYAF/bls8+xY8PYkIqzeAqIWmp6wvwFiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGfCRA9TVsSAnZWagAAZ3YQAJ/UQNREOYph5E6K+96f\n9Zf6pR8po5f0poiuFHfAPzYPw80wL+YAhi0c68eICG9omQuxe01KCHUQiWNH\nqKO0Fu9lt15DgnQk8blQouEobKVN5GxfMAZbQ7DYeNO5pKNkTiR/DrZqvT+A\nFgWJ3q1Tlurt4OYKr4hqOP5MGVpg3+d62hCGrZ4NZCnlFE1NKlH8y9Y4kVQn\n7S/qrIpvtkK2Fi3AtUM9nMg+axDHsufBzculRI37qPAig4K1GoZ67iAye0A0\nGZqxjY6ufTKq9LGIow6D2qLFpFgM7ooZ+2SccIfFIpCqKTIWB4g5ianc3COL\nqUdG/EjcHhAeJ2HmSUAHpvg9kQdE97yNRl4v45DNgitMS19mw5rCYprK8O3T\nw6I6Q7bZ+EmiRagjkWHaWEq7Df3gK0B2xNxOmKoMXaA6tkrLT/Gc9po5n2UC\nRKgFSKksdwYl2k3NNgV78ZUUqPiq4PKyPaxgfPGjLMuyztnUX7XBfNTPIVJv\nwdXMPPiP6htkdtizkJMXWxqGYpf9ndKhp8r3M3VzN6agLaquT2o3wDhxld/6\n6JV6rC1AdX5YjFXyAVTyfC3eSDWJPY8l9AgoP+JNXRJGndD7dVxba9iQz8sg\nwcz0QHoe16jXVzNztmd/Gl5b4PE3D5+zlk8HKAmTupVoz9U4HMzqlq3LE8fQ\nFtu6\r\n=i3Jx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.46_1524457886788_0.3524295568036049", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "00be44c4fad8fe2c00ed18ea15ea3c88dd519dbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-+o7/yb0Nrk4Gg/tnBgfBf+G1uGZbtkSluUnj8RyD37ajpDlWmysDjFEHSfktKcuD8YHeGz2M9AYNGcClk1fr/g==", "signatures": [{"sig": "MEQCIEJVLNeXeNNvI3JJmuJnwZe52LsaCtr1XAUgv1h/fZaWAiBpNXB7HtZXjo9Hq4XrabKYUf3yy/DmJCL0YnNdWuM/5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUuCRA9TVsSAnZWagAAK8AQAJ/ORdQQEb+DHoSUWFCY\nkBIym0RdNaZEtCuY3iuOZDuneWUPx8hngyTiJYDpPgT3+6qKn/jKYdRdslGF\nuJ4AMx2tULf5sg3sv08MqjI5SEoMHZ17m9w5UF1ON4sUp58jAD0My61WIrZR\nCUr1Wu2rpycHb5Os1V5hwJ6XyzwI3ANJWNmH5xYtGmOPGtq+QtdcRezIPUQi\nvko5QyojfskKIHDsjcYj3VuwSPYUZKg0Z2fjeQq97yBFQu1/+qtWQy4PUvno\no6CIz2KklPC2dtsOkZBETmtt4S3mcQZChL0u6GQbFJqGoluBgjGPr0inYgSS\nrfvgihw0OxOGsmlKU0GUYTlwFfLv+I5cr2Ld6tkyFsdkWl5hjMAPD+nz/bBg\nSkmExwpiT68XaU4sqdOaHh95clK5+zxBEKYEazlb4iMcieIWtr9bnWj7L5W4\naIedUuU3IAkVN/R9Fv80d3fLDaoEniYPCvuCYi+BAjnlj9GmPM3YLZCao0hE\nEn+NtT5eI+27YP1zLn+ZPhzN+SabhTWzda4Osc8V1vXAptG1TxABfjwu/+ww\nu2VizDmJD7jtq0C1rac8kBLSboxKpNz+R+ocNI9MF3jtZPyX6R+dZBalHMoV\nl4AjaAaD8GrCoTj5oFpNEsAABCTBhO1BzI2QCYqS7Hbme+yO0K25m0G0xKhd\ncLzx\r\n=wEZM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.47_1526342958556_0.8948828283242467", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5cc1c14bbc4f5132e5bd1ecf7ff3e9adeb9dfe35", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-9/ay/owcYN6auRT+yAMIwyyA/k5pVByecZ7QpcOu8z2bMaDvlCBzP00iAtgZjBsEgBlqxcCdy5VexVkWyDUkew==", "signatures": [{"sig": "MEYCIQCxddNWZjH2R5P6g5G5Z4yvwWis8pTzn+2MIEUtUXC1qwIhAOeNST66rVtgxm0c7Ad1fp0iKsIiLm2vDGe/5BAsoW1a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEPCRA9TVsSAnZWagAAcNkQAJ+ur4aRmnO+586jHmgR\nBQV2o8UOHnbnmxwagZcemVh410g6nVm+JPv5W+DOIBHqQqP7Ky/f49Ar0oPZ\n4nx6DcVOz8KlOUBeIe46NwrLw0UuprosUbwwRm8SJIr/zrueOw/pOD0Lvvz1\neALRJHHp6xV35dRsTS9FCk2iIErW2+lJhk2rgnNMA7yNtCMkYvXG4zw8Tzf/\ndnU/968kMue+bomFhzOxD95bzZgz4m9jIxidA27mf7r2oKR6iQI9qOYV7Ou9\nRIYUJcRs6p/N+LXzUEduj/HgBFLqK6aYLgdXzWv0fuz23ERaxp9Fg3bOO+CF\nGZSZXHQND54yblg3iaX4DCeA/QF7FJqtpwzwhuk4yPs7C1hn+6mTTOf8FXsO\n1kl52ze4TbVlQ3dMtTj2VkHGr7P/WpeayXp+gzaI4XvQEGuk/Gsl2RWo/ZfY\nLlBVclYYsaKGhCbi7+YJkUG70omGKeKEQOJo19BXTa9JvPiZYWZor6h6IABK\ne54vqpB/d4eO361k+Zl/0Ut3xAqJKVz34R3ENjhth+thhjK7y+lIk5YYlek9\nHQl55pQLuR8APyWJukCIrwNrebNya0GCNtGhw62ErKQv+BjSgHvkbKTblFd9\nmg3h6Mi1HHnQwSjAUOujmoTttUSBzIV936gRy0SEvQJpclopIbQH2ry742PX\nHYQm\r\n=Y9XD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.48_1527189774821_0.10058350598215293", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "49f134dbde4f655834c21524e9e61a58d4e17900", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-bONFvWqZEJkB6QE9OC54l7hUP2NgkW8d6xNezx5ndBoM1k0FMOLAw1usCFaZpUgwGfvFKssjAqwchJCS4lbHrw==", "signatures": [{"sig": "MEQCIFv14weU5MdEYWfiZWUJ4m9trh05rgUAVeB9fgLexlsGAiAMiVAfFLsTXmeded+dTrP2ZWKNrPO2OC830QnXVZjdEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOUCRA9TVsSAnZWagAAPZEQAJ751iJXLiL6mTfTbEU3\nbB1MnUA5sGvbKeIoaXAdAz+THXagD3fe8QbMCFb6XeHh9CP7cxwY8a94yJn9\neLpmhw1fN6G1goJMciNWr8ILPrQZ6/wIt5zZ0/IjYkeg0kLtrFpHw9H7OpRm\nKsV4AARPYlSo5WrNWVcxHmo0AAycLeMrHafyDxk3xLGgvAwSYpwMu5DsICJT\ntnITLFCNd14Eq2BIDFWeLF5EnyU8D2BpEDUVTSxsEhSsqCc9fIlIItVUvz3q\n3/DyI5Za6z7LuFeXlsLe9FXlPk4/LDARZ6DKuElyFbNBaZWJ3nKW+YwmiZHB\n7GJaCqh/ajoKKi9BsjdU/PEi7itbLviDRu0eDjd5xZihlI452DkFjsFP+G7l\nOZHpkkFksnO+NZ0LOdXLLaeMHyoJLubO5CzJtPRZo+zbC0eTuA3yf/YPzNjP\nGEm9rme2Kyf/dog0NNQePkqtLwItTnQPkMKZ9yISn7CnXXcwuNyHz4xSoem4\nAnGpO8zRNqjgFeFvLi0Kjy1OVIKbHkdJzPk0Pixyr3miZFE5q49XjLGDFi53\nas6iK+pcKgGr4xyhOAxX/wTbrAv9bZaexxOEaOgSPxx3W8ViUoxDLxjmIqks\nd32cYDi/0+swGi/8LqXbSepix9SfHoTPd+SSs+PyIBDZK/Q7xsJ5UsCvv9XD\nFn2h\r\n=seYH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "49f134dbde4f655834c21524e9e61a58d4e17900", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.49_1527264148450_0.4289384043909732", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7dace63200639e0f59f1be6adc00b2475d94e72c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-BE/drgYETmNdUMJXGKo5TuaXVufIt53DmoxhzuqXLQ2J304nlSyjrQujeA0x9XNyjspLL1epPoi4P7HtZvMZSw==", "signatures": [{"sig": "MEUCIGs86SLIdATxqq+5aLHQuw0+nWKeZFSqk6iyxOhgcU9yAiEAiUk5ZDxtdZg7mWXAeFPS9xoyJCsQsvYcXDbhqQqFtRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2128}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.50_1528832841781_0.772087165211357", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ddbc0b1ae1ddb3bcfe6969f2c968103f11e32bd9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-6m2QWe9LS+szyODVXMxkVPBk7JDsosMOarhvYdLiV4gik9rjwGPePB2uReg5lgupOVTrVP5wNGudS3cD3fBBZA==", "signatures": [{"sig": "MEQCIF9v2yriGKaLitxspyXJAobsCDgHKlZdMXSM98B3hKrsAiAIlIEJvq+zzNlYgB8Dj/X2Mmx3Ud2MfDTIdst9ejpsig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2142}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.51_1528838396863_0.5191201408626194", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f3cd777643d66878842a1bad5b95b4cc0b5ecb97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-mo6Ong649EEq3FKJtC6SuGHayBCK3LFUbyVHHEgqIM7jEkxE85RUyBr9MWcRxTEzF8tUnXrdD9l0nYovxjbeqw==", "signatures": [{"sig": "MEYCIQCzzZBObmvPa2h7a3Z9GRdDOna2hvSkpefewYatiAd/7AIhAJa3rtQKzjfZug5EkidnpaTz32OBXYwj9sy0NX4xTz2z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.52_1530838768369_0.6237911738044009", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dfc4881b6bd7658a0031ec3b8163e588f0898d4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-kNjYrpDKi+ZYjWM1qQwD10ERplviaDb37/9RoLpeIRezf+DXPm5PMdIMYJH2gD552fbtkYwESp44hm2Izpi3rg==", "signatures": [{"sig": "MEQCIBDaowLOfgrxLimdew4j10HaceYCvHhKwxIbLFK24WQ5AiAhSvZR7qBi+HuORh/cRcURT2+4ioAgiZ1fwsGR/mjFAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.53_1531316418496_0.158068739489168", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "50e73c2afc5898b1055510ddf60ee13a6301517f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-SeAt2sRzCJW1y/aKInrTp/R1cYoOSx2LTocUzUNqY8JNs4eKv/L61chQmVUSJyHtuLD8/MeCFkw1I8VrwVdYcg==", "signatures": [{"sig": "MEUCIBdccaFpZbIIHafWRNepwH5dZYcTi5WTPP6WSC3WeFi5AiEAwxVPAGLdNGuOc87+tUKIVBGhRkg9t6gpJRZR5hZ4nVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.54_1531764009505_0.852013300809271", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "75e97575b87c6fe31c008fc3d755fddcd6cb908a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-9LhHfViAMtauyEjRGYr4jssfdYQ1qaZnl0Gr2LI03bhaU3uTgjq97nHaPJqf1EJIxBJR1Xq8lSAHiLch2zAfFg==", "signatures": [{"sig": "MEUCIQDOgfvtOVkHi5CDM7k009sX22J+qhH2dGm7N146JRG9RAIgcGVt5xTdxqqSHJsBJ8UNUtm/r5t7TVchI2qCWKYgz6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.55_1532815642850_0.026577838224236094", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bf78900f016774a28d56ec8e83d0cec4d662b7ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-qfN9LTjglikI4N2K/WkZAJQijWQpsQefsC/sXEN6c4/G9n5ZJFyXt23aXXcjibncKbcTrRpmH0nTeMiZK0A+5A==", "signatures": [{"sig": "MEUCIAsKCbB6PIktGR6KO+a5tJEa2MRrtCFsQ9OMc/hFrRQMAiEApdboBEAVPgdJjC4uhHBMIbiw1fW48TsqJmpXSdGv5to=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPv/CRA9TVsSAnZWagAA0pEP/iJtr7M0nqdFa5XraVZB\nzPktQU1f2ALnltb+4b+d3w4IbAf5V94r7aUmxolMDdEzoqoplRMF782Sp8al\nsIR0M68rIcAFSnOGZf+fnycuWfKNHApLT7EhUWUgrhbhRCZnY+7jyYtzDXJr\nsGLsDFvgSXboaU3Zhvov7Luo4cGbM/977gH4eQBCylyHiegQdv2pDAUErLtf\njyni3fEV3MM9r9ikv7SLqqjZA1+kcUJ9A8X/iqCVsa/gZ+SYaAImf/1Ny+qw\na/Wbr9tzV/ex5w0h8CMQvsttI85ka3QwuQb0vSX7VjeVyUFgjk8k33HpnWpS\nTt4K+Cm0XuQ9/FPC73WZbJBYxbjYTfoFIOee9l5/xlKn9z7oZcx8oEE0/miD\no2QOZvykmaDnSMfNFvRDW2AK+ScXNgo0xBoB8KAMpcG11d/xp3MMYFk928rW\nMX/bg7SeviAn9+Az3sysFE1Yio8f1hbe5GqQGAc1Wp+wmu+gT5MrybfuNNu9\nhFORAkR0/NsMkk3SvHVawzz8PI5K076G6nbRYTXId1uvhjJuWVcs4dgQNj8N\n1k5bW+LmY5htLMOhb7Yfsmpf0i+qRuuayOFZZ4ghUPNNsVjZNmFW34pnb9Ks\n3x0RKgwu+ET0c7rWOHC0S2exsXG3P7W4/qIcCz+3F8hXreY3uGyoe3WbspdI\nGbgI\r\n=mWig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-beta.56_1533344766765_0.3468287985107308", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1c39ec178ca8ccb5db5f1dea46f75e43f043c699", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-hdBaUekR+Mq3MQ36o19wfGk8sMT+Yzd0LBS0+qgc7Iuqh/nmi+zqMuJZ3wAhujIevA5WwINuqtgebjNVnJlj+w==", "signatures": [{"sig": "MEUCIARRGXkLGK/wcagneKUfmlQh51D6AotsPQnd/IG4TtuRAiEAvJPjXMb6Y+eN7ESeCBD005C4SQTLkwnnTdBSzy07iYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSpCRA9TVsSAnZWagAA5mEQAJq0gNiwddHfUjSByZJT\nCHPfLBEVi1p8iJVvdQHNck21PtJdx9lqjefAFFOzLydQ7jqAI9AiHnfDx1d7\nVuFKZgkMoFlJlzqKF4pASNkXDNoU8S69AKdCZGuOYUtLhvaPMac4yIGmSxwV\nfLruNj46dW1z20UcJ3tyfh7MJzywJqLgxeiYNFXhc9VKfoeCqIxo2KEZWu9D\nB8jZoNBj8CwuFv+h+0jRp3wBNPtjuw4eIqZwVGLqm3MNl58qwCdiJexgSVys\neDyUu3KKhunvOQBqxx0ivPdgX0eNxO0/htEXSi8W5kjiBcR7ywp7SLHGRzdZ\neZceAQxEIXMFGvcJr8lkCIGR0pCDKq1mziboyl/f7Hj2Yx+3rslkL8xjocn4\nMRW/EeyqvfNyp/kzp5DxJe3HZrdEMS9EM7IeZY8953MlYhZZPUjmO1dGjhdW\nVV4HIeTWsjls7bT+uf9NYmCXW1WqRqiYQLl7n61PLkvjbQT3oY5XSzFAQeEZ\n98WYom3gkRhOf0BVahKXXNGtgCCftpqTuNBfOypZ02gELIe0Ee471V2TvKpf\nJQe+uRJkJMdFI58OvIAmSK93f3ed1uSYgmw7m0EBf3DgKeiXzD4aaMbwJp+q\n7wgU/DRKgMWF7OW9Ghm27AJojPfcU316lwvnmJ0YggBkvsTw36qp997/EWSz\nahem\r\n=NZet\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-rc.0_1533830313165_0.4370587376519528", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "21724d2199d988ffad690de8dbdce8b834a7f313", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-NkUsTSKL8txvPt9vtdkcbJEyiUtcSOAr6ZnAE+Vg4mB0hYI0sWEJCAzl26KDDFgdVSKJSAaenjX5UR3BAF3KaA==", "signatures": [{"sig": "MEUCIGZrbHwSkoHeXec59VJV2D1hSVJiH0rE8jR8C47Z4909AiEA7ja3fQvv9FwKL10n1OwaoVUH7fui8syXq8l/4CJydgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8vCRA9TVsSAnZWagAATL4P/i1LiIaiN/HxP8Fdl0+I\nOH14CjWQ4Cl680zoEcFkn6ynSfmLRVxuzRQyHqWjVrLZFDj4eidJdow7Ae5A\nEsLKP1m9qNtFEyvPg4+ireC6q3wwhMR9nbwtXXK7k0kU7P/SRqDqsuCrWe/8\nd7nUBVZBYYMR262Eoin29jrHlGhTUqQkZERhDdWwpgTjrxZ4u6rbdWptPuey\njDcdHWH609RW6JDocDZ3P+mpz7+0ZcQTBwud9mVRpSlzy1X5Il3247rxps/5\nEVWgcAOdHDewVX509iMprO1mjXGDypUpMco1Hnf7u9Rve69AHfqmtQ/m1cTG\nM7LcROsfasbu3l3DKu5GZiORwFOyVrfqdvqO05qBi62x/DaaxQ7h2zwM4+BS\nJpCuKX0Ip8GyhOUPdoCVbYnAN83HnOPGNdZIqzulOgYO2edftplHd9TgHri5\ny+zTQ+xSELZBN1k0S3sd/gHeGX2OrXNMFofaM6HZ+ia+vQLCkz9xWLITBQf/\n9UI+qSDSm5QhELpsD9ICSCt5Ydhw+wLuSIhPc9mfVUxRKdKmBaAI0rIdy3MM\nOw3QhNT/zkkzlN0pZcZaGd8RSRkSo2Fh4R/Vfc+rSjtrfY6b+QPYfLBGJlJQ\nrSJyIU7k0EcBevCFdZSAbi1GdMCtfISNyw+XMkcQyXjNn2tzVDVBRi0Fqa7K\nt7KS\r\n=Q80E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-rc.1_1533845294821_0.8787351309991114", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b920f4ffdcc4bbe75917cfb2e22f685a6771c231", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-IKkCwaGhfgRTGFrGKCqQmPFm7Z+y4hfzWPgctZNhZFtjPN0Y6B+ixaX7Ey0aFEIq6K+2cAIMiZTJbDflQEb0Fg==", "signatures": [{"sig": "MEYCIQCa1vU8Uf7DwiQMXcZ5majIkOlDse9bCgAR2v4GfKoDSQIhAL86omx3oWBeU1K6hb8Q3G+gpFsmvAY3kY7JQjh8ndLn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbiCRA9TVsSAnZWagAAf3wQAJ0eeqiIf6Q0ZLTxx/Zu\n438WH1Y46RaAoLo/UstUGMDGd1Nf1g70nLKGSET7aDKZW9bfYPAmWU18MYns\nC6DeS9aoCrT8xX7jWidG4mAfAyKAnHc1QUbtaZCeZ2p5GDso45q/B58KBvEL\nT8GKHlCfCkZss0dChNhFU5bqdY9T9+IgBxkGuIKFLHvJ8z/bKET2W46CPNE8\nNqryMOsPoHQ9aV2f2lOAten1o6s9yuxwr+9A8DtD2HcUCTHkJtpogcuEwWZ3\nsejZIbseZNKpFK2CtT2sqlOoHiMT31rgCouUIfW84ouSgx5xI0p0W6N1p+7X\nQ9GfdD2c7ocI0xLLKtHRLUBj42M+9wiNPeZyF3nR2AM6sADBvy5qcVODR1Ps\nVM42AdGTFC18jdn5KUn+pag4BF4x9SDZdmCdbdIy5Pw5zRgPFppXDavchN4X\ncm4H4k38RRoofH2uMFphsjBAGz5Q+7ir1SHaVBQ8ZOI70xWYe47WamYPAeqm\nJI/BobgmXylPP/jXZZUfV9WUyBA7eONMk4Wa1Aew90jNX5DnI4Jm5kCTZScv\nW+4pof/tGPCbdAfq78CUgg2z9Hcq2+atPOD5tOXcaH6RWiHN+Iaqq6VnUj1U\nePix4IPp8C56qfsbsX65ZiNqf1qf74UVfqIbNpOUcJE41EPUeGdhMK7UIryT\nkkbt\r\n=o2jp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-rc.2_1534879457930_0.5100753072282012", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "84c3529e15e0e285b446448ac45872886ea914c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-c9FVwPitpbfcICqsuFvgnar44OQZyZni06P9EYX8SXR77jOG8Z4aoL+1/2oRGEBV1Gfc3omy0wyLqqUxeZ8XoA==", "signatures": [{"sig": "MEUCIGIpjODk4xcg9sZL4cMhxuPz6ryDU641D+nHrasIhoRCAiEAxpwqX6ycf8RMVvafy1Dy6XnC7MskDD31EMkg+c2Vcg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmMCRA9TVsSAnZWagAA4AQP/Rbdf80Onpgf0chs4yno\niHVdfsbFdq5keMkAGkR8D8Ymh0z2KVrfDUUQrrMK/6aLzibgJRUr5P/IEGv5\nhaXszExHnVZ+gbZL7J1qtT/OyQ9/1VcfEWcbILlut2k7iob/znLvQ2UsKwok\nYwSIBiw6cvap1T/1rAEEK+hmFuEEMvpMDcU6/U26BrmnfXQORMHlpKQ4jnjh\nskgNf4TNxxSUHX2SjHoFI4/+5MwiSqFtJMmoM3hLluyYtHEIAk/Ox8HloVos\ncOL3pn0IctGih5nDadxHnYnt13xpMxLzjLu8LeCvsPartFrk+UQzutMN7LN8\nf8BHgAfJ+vOvMszLex/x6Fi0mNGgwYsRe4CDUd7l6yYH+7ONGGO2W8GVLyD/\nh1lAEYdJwAF3IQo0s2IKFVIf2boQ/FjllCB5OnqCoumbeZDMCv7+uOxSpDTu\nYxegMgdvWfIVTh1cgmMzc6olXkn8Hb0LCN0ICto++X4wuR8K/vxlh71fP19T\nykk5KabBi6VnPa7J4W7BK101zo6Peeih/DVl+Y+JAYsui5T30O8hqomv/RCS\n1DT0pLjIMsqlPUo9xo2l/BDluGSV3vD03p7IKHlI5nxsdRZzx4f392QX4yod\nDQVgHKkqgrzIMS0Ot76Hk35eO3xWaZCZ+E3+owj3e7wJAFik8vGvnOPLMdUS\nxHtU\r\n=StJQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-rc.3_1535134091968_0.8787873419430539", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "372fbf2e10b71594697c5c909d13bc98ef36bc3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-i6B2wGWE3kFegrA6zfWBDkip/txkZO01jpuVSlPTnvob4GUcmd2l0xBnGy1+tRJqYdCNu7aC0F/I5DoAmTX97A==", "signatures": [{"sig": "MEUCIEMm9GAHDc56BBKHfPOY75KorZPHSkBomQEG/tPQn3WqAiEAjCrTdeRnEXAfHgzjfBTvOz7lRrnmcEEfKHCGjLuFxSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpsCRA9TVsSAnZWagAASzkP/1+wgDv8mvb/lePWGO5p\nZPUJgS4c1HmoQxjMr0U/hPV/mJ+xPRnNvlq43sioYEgNubUlXVUF/Db5/kOP\nTGZcMr9cJ6E6+NcOoh56J/Nlg2YtbKP1D59/OlONYZRJClsocvk0rudEjgZf\nFewGGaVEnhGevc4npD98MPSBgGV7M6gC6F0GhA4zd77GiHHgHS7whPZMti0x\n7zb/aF/gfwEAMugxo8FhEmxlZDYyTopIGCoIOuWW9PuAqaNsJ43KiI73vTrI\ns87F9wcNdlTPEb5/iDRzvFA6Fe2nr8GG6QQM6fcluaBx0UbXYvdlVOWOWISI\nsJZ+wYsi9e802qOgrVlmd5xRWu71yejAz/KIo0UMCXetSpBeqETGyGAFgzEv\nFvVCKd0fjNOI+6DZ5m6MgI+3CzEi/Ce033x/uZSjpY4oSUH+fmJbt3GItIZk\ngrl/U7MXmcBM9B4B27fJDj/l1/qsPzgdrnWNsqYIe/iyhNt4sljPrFLwGvhn\n7kUnOuzw9JGCWsfnDQtUwoYnQ42AvPyHnMF9PFhAogOSjQsmGm4pncqXZzUB\nKQbBMtnVvZrYcer0ETJ+CmmMRHQpV0cWyigHuv94/IK4frlN50uhP9kfyaqj\nk6WOojnBqEJUOVEKRM3HZT+iCkUCp6gRIA8dwKYus9xr7CDGBc/+OsXnyDr+\nHl9V\r\n=8keK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0-rc.4_1535388268055_0.37692228696458185", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "85f8af592dcc07647541a0350e8c95c7bf419d15", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-g/99LI4vm5iOf5r1Gdxq5Xmu91zvjhEG5+yZDJW268AZELAu4J1EiFLnkSG3yuUsZyOipVOVUKoGPYwfsTymhw==", "signatures": [{"sig": "MEYCIQDhf/lK2i5w/UCRHsT6lkPvzMukrqm4J8fQ6WQBD12VPwIhAIfTsmmXH9K+vqvgUZ7sfFcX2r4IQdoQfxYgwvtdqbFQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3192, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHB9CRA9TVsSAnZWagAAUyYP/jxn5I7C9+HIg8Tmp7pL\nnxfZuJrvX9dSIrFgGcosyrfhBieWxf6Xzq2Jr1I2yd3u/GPnOv/V7Q4SmvtF\nTJ5jZ3xZzRmnEtIomMRJuGUq2rZ3IJznAsR2k6LFGFSUh21Q9ZhdhkTbrdGn\nuVf9YE8xFIy5OKi2bWFifffjrnkTouf0IrylI96mfqup5qbsjbT7CCVVZnXP\nEoq48Cu0PBMDiPP2ZPFgkNfzzvKyzXvCDrmDF/ogXalA+ZH+Y/UITnLFdmsM\nCR2cQC+OAnp2asY3WLAiMWPgoPKe3bUBEew1YogEB5WY4cH7j1ka4zNJfvuU\nwDxd6ASFxcDT4U+NQG2PASsGO4+xEFkTarlYz3I3Swk1dYWfB8IFvejGKP4h\nBm39vw9t81JoginvFnlI/4rR69zZRzikaloRGB8Gr7zoxmxxQgiZeizpuAeP\nQPM0TROe+o/tyUED3dwk6a00sn27YnohyVqm6n9H01N6c9q4mfmGdPM5DaSL\nPIN3Z7eScI+lw34ra969vcq3csq/fjZNN6Keyd2Ii+56gaPROcM9SJ6AUgjW\nDgEdWhTKKZPy40II/HYU/RfXF/q6+RiSsAT0PiYeIXSrjbTlabwj4uD+OiAf\nCiURaSNYefp0WDKH37Pa0ZCZu5RF6m7XrV6OeGBJWuCvQ0EZOw96kSLRZJHW\nC3FI\r\n=8xxJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.0.0_1535406204747_0.16566830662347098", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6333aee2f8d6ee7e28615457298934a3b46198f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-QP4eUM83ha9zmYtpbnyjTLAGKQritA5XW/iG9cjtuOI8s1RuL/3V6a3DeSHfKutJQ+ayUfeZJPcnCYEQzaPQqg==", "signatures": [{"sig": "MEUCIFG0gWTkxmocZGH+qrszaFJ/FBF+abGCmSNLCQiL88NVAiEAhr/YdqBCVpAtID9wwzF24VlKdcox9XgQ+3g4DSUcwAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2dCRA9TVsSAnZWagAAg2cP+QACcaTFEFHODNyVYBeX\nof2AWchVlti1yqE5xdJER9Wnhikr8IAoctPM2GGqdKru2VnYt3r9UXvQYvwS\nGCV84TwUgULCvpocv8rbF7u4kWXsoaAFjq1UCmt3J1huOpMA40pjN0Gr4pfP\n2g6n2dh3ySSQPBPfzOrqNVCE3D6nUtq/+73E+Bnf06PBCy7NO+zw8uztiArB\nd7NtkTBaYBBeG7FwOVTuCiR94d0Vu9C7ZwHG5kpM0SVQFo4bJGwmUh1LqMR3\n68QpaqYexSMCGAi2e7XasUt8W5ZY179xJoakg3SM0kDUXGe2bmp3c6IF15Uh\ngXlPFKcCnG7s7EQ0ze40qtDkqHbTfuJaTvc+bQrsVR/HKL1vC9ONAd8Oj1sU\ntihO6Fi8+EY1j/lgqSMKrX/DysrgJXOHCeI0HG8FQ6JPtI0o7cgRwzuyZ0c3\nFfFH8O92iqL2QhVQrJz+oy0RMsYl+rbPpbpN18KTBGOnb6M2Y/rdcS2kZx/M\n42wZE6ZCEx7WH756qrAQI8aNfhFBZyOxa1OK0F4x6NBz1NyJlQh+8/xNBIHk\nTF6SjW3F4dGtQI8IWgM4HWHVNYVTD5/vEXHMPc6Gp87sP8jjKyiYseDXT4nb\n4FCSYr4cAAXI5Ui8WlVm0FNAagPjEja+iPotWtlzJKjBAV4OSS8LQ9h6Rx7k\nbye/\r\n=VYlJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.2.0_1543863708665_0.3225372068602459", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "74a0a9b2f6d67a684c6fbfd5f0458eb7ba99891e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-q+suddWRfIcnyG5YiDP58sT65AJDZSUhXQDZE3r04AuqD6d/XLaQPPXSBzP2zGerkgBivqtQm9XKGLuHqBID6Q==", "signatures": [{"sig": "MEYCIQDVEc3JbE37FNWvL6PLR9mryVdjjkw5TcKAjLGhRDHHRAIhANYXXv6Tk+ACXAUKBK4H88AsEt2Pltp+V5sBng8xR0dY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAFCRA9TVsSAnZWagAAKocP/iv21W6rvtcHdMIqZX7i\nfDKPWsCY4FK1P2zC++t20YWQqD0ZVhGo5ax97F+mrzUQX0EymQWr96X1U/X0\n+h45BCVbmIl663fdaKIMnJXMZwcjjmtRtyGw0vmpJTju+kgmpRZt02UU0ZR8\nYVMJvC2bK5/o2PjQUd/CWxYUsJhQrYVlQ5Z5IFw/QfUTkTcWtPOg+2643aqW\ngdB79s3dxxt/n23DziTpAb1++cZTQdOYXe37oBEfvL8cC0wl99j5WvwBVPFp\nkwsFXphlk7a3U3/2igk3VY18awsYCUuJqHLbNHCGQiICtExmQsWjeC6FCbsZ\ntMLNTYe/fAdUpzMVf9/caH/vT27S4BS8G0unOAvEIoBXNw0S+oLr9am2FypT\nEF954NKrBKkbqru4NpeDnzxHAJNtjFeGoCRrsgwR7qPYiGH/Koc257HcE5GJ\nnwrwPogsC974iM1+2jL2NPfSXIG9AEuV0kBIhRTHEoC20iKgfQ+CwCIStKKX\nPiSSCDCh5INABQEwvbmT4VsMKW6fCnn/FwbGWOHPx+znfSOkPAsmXuzPNIQL\nek/6Jlu7u8ki3VHquNZXI4uZOXHOQH4ZXRkCy/pW8s1vRYQ4OPEEWYIxEuzg\nz0KT27corrhrPJu/3MW0QxqhvIqMbcDBrUPjAUOZuECkgnaIoInBstxUT1zy\nQQSz\r\n=48ex\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.7.4_1574465541042_0.978214854179531", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "38b43048e633878f82a3ef1353868c12015ac838", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-sExhzq63Gl2PMbl7ETpN7Z1A38rLD6GeCT6EEEIIKjTVt9u6dRqJ6nHhaquL7QgR3egj/8fcvq23UvzfPqGAYA==", "signatures": [{"sig": "MEQCIEFqzYieQLnQA45I67QDqplp9lWaPdQF5Xv/+A0UOJmJAiBegY58lmnoWj2u2NJcMjXgI2ixI3I6vpthk55eb+3tNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVoCRA9TVsSAnZWagAAiyAP/3P/D2BvjxbWXhmhvx6b\naEOCujADn2zJN53ulwN0qp6q8nUW7vJtrhT1BuWcTC9G3It6Ddg16lK+PIUM\ngwd5y7EDsp7/C2QEUTQUEsuwXYWzDY2QbzLyYTtVR+2mDQ2ZePnZ5jVVtlU6\nv5vog769fpPGRWpPBzfGcv3m6bELbx7DtjoVE1qYASS7E5bsUotRsZAy2M9c\ncNCBQC0pstq3dMFw4Z37h5ERmhyWqJGS2bFM4MgnIKzkKxa7yy48NHqPP84o\nrlDcfb/MQFoPJLbqywcZQk7vXUtAipI4pjXiWHDNZy8JuVJ2Qpvxyn6sUYOX\nlzymuAK1uWMpTavVjGDdSdIODJh1ZMRVYVORe2hkTrOA2UMTxD8y3N7kxB2R\ntgCen0q4REYoW5vczRB8lINGPGZJ7ML9uhRRH/D1K3326QUP4fBngmR25/cF\nL5EoDNZeigNQj/QRF0comgtFXpV11jljOEltayKmRsKo5mmeRDDfhrlAFMyz\nhZ2YZlpwaxzxBVcXCFDLVL1gxD5axeP8/FgnVjRs45FwL1R4mZmOJT/LxNva\nJz+rDWzxDlGgey/4bU7pqi80tl3feVfSE8X14tJyH9ZGGMwJqr7I+qIquCAc\nW2W84eNgVwvQf0q5Mgc5h1trcV/v26NS+hD32nKoIgUFwyzE3XvE1eUZVGVP\nxw9I\r\n=CM65\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.8.0_1578788199652_0.029522324683350032", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "28545216e023a832d4d3a1185ed492bcfeac08c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-I9DI6Odg0JJwxCHzbzW08ggMdCezoWcuQRz3ptdudgwaHxTjxw5HgdFJmZIkIMlRymL6YiZcped4TTCB0JcC8w==", "signatures": [{"sig": "MEUCIDs4RbraiYyZOkV4t+i70nBwT+VMAuh01G3r8ihUC0SPAiEAtCDYzRkC4MiS4xUllh5Omh5vO0CTntn89Ff1z5Z+Lp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQTCRA9TVsSAnZWagAAOqQP/iwBgDc2jm0/Umrb61PI\n5GuVbaaAsRUzDWFFtf8HmUEWsc3mQivzCwKj5jRKzSfctzamSq6wEo3JnC5W\nLEdN0n87kAiJuAEZDRhfdQb4OAXfbnJSu5SvRofXO07TYR7aNOwo888fw49I\nEzy723gsYSERwka4/C6k3ascnKTFQ6ufYQ+LqAPIe5aKPmqMCu0bG92s2EtX\nTwyXs8mTYkdWI6i2KWIZpFYR4bVSu3BROK3ZgPK2Sf4qz8QO5bAeChAEdyhH\nFV1x9rBGaENGpnOALtMhhr+cO7ICUdYmFfM9z5SKp23A79DmH/IRCxrfV4L1\nhlJQQ5JmT0YHX6HKI5ED7/pB40DqUp7Y52eRh5ICvsi5SsfwUh8xLM/+a4U3\n4FsMmvFjYV6Uv5XCnwqQv5FHGfN0ELjRxrKqR4hlPVHb0pBNfn9Zlvx6PGUZ\n5TRyQUgYaNMg/nCPcFpL27KtvbSOfZm4s/WdV43YZ5YXmmqZB5tn+d3YR3Nh\nD2dXsODUwEarSEiDzeL7XF9MEYGr3pm3Az1oB83mt2zSxm67JYPkeiMK/9JZ\nfHDl4NQgzs65zwUVSJTrAdu1y4BZLC1sGdCgkLEVNyzySn3yqYZhkOij07Lz\ngwxZd/N20wWUEcfVBYqKVkwfAphf8eBgR/7cj5L4192vsB9ugEbeRUDdniZt\nYijz\r\n=1KwM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-shorthand-properties", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.8.3_1578951699061_0.6698196229239075", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e8b54f238a1ccbae482c4dce946180ae7b3143f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-AR0E/lZMfLstScFwztApGeyTHJ5u3JUKMjneqRItWeEqDdHWZwAOKycvQNCasCK/3r5YXsuNG25funcJDu7Y2g==", "signatures": [{"sig": "MEUCIQCrr4kgWJDEMXRwLWwj0dZJ/3Yc/uO/FahC0+JXaZuujQIgedvfWHb/IBDs0fAT7e+pQfVkiK0HZSUDp1LNSCC2Qjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuStCRA9TVsSAnZWagAAjrUQAIjI380mTFFhVBsN3nJg\nVHsgqDII360hx+hfwlxNIXw2pDy9jEEdK5B8rwUel4wB8afgm+I/dD2iglRo\n4A4rUBvTp7ewgGs0K97RwcdKCroQOrJYwvtIxbG0N8nMWNzTiwbNxZ9lde+O\nf6YyMZtMwSd1oFfv+h5R0+e/M17I6StEkmPLgPllreF8v0zxrPWGotQBJsUL\ncX/4L5dPFI1o7JWkqbmajbP3z8/ddxH2UAwXPFplOh1NkTeqQNeht7v/nt0j\nd1Rz0YGpyyE/yy8Rwdw62x2dX9MwAhI+jx9ujxM3hs1jj1+M3dy//uvLbmwZ\nA/untjB8F/PyzqxqwdNbaH8PRRtd9FYy7IYgFz76vizLpqcdrOe3Y4I6dzHW\ntqImOu3bbF2THFtNpHor0y7YeJBUawcDlWdbxHFcnbwbshTpP1GSB+0qkNH+\nN9Noof17VMPQFBjdq++pjahkdMghyH4aUM8MHnN7zx3l80Cbuw/+ZeQu/u2C\nJU0ofK6pmhtrRee01aGyOil9/nz1tYRXDJEz6+Pf9a/HddvhsmwUbHlXT5CT\nYdWI5mf74GnL9xkxfXNnVCoHYyAqah3IxwNSpVJH9VXOd8q2AIet0vgi7z+U\n8sW0HwaRp6dBIUu+YwZn4oC1x5crqTzhzaE5rZKe1bNBrCCSjtgf2HthAUSs\ngUAy\r\n=ZuAa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.10.1_1590617253791_0.6468152551165778", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9fd25ec5cdd555bb7f473e5e6ee1c971eede4dd6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-AC2K/t7o07KeTIxMoHneyX90v3zkm5cjHJEokrPEAGEy3UCp8sLKfnfOIGdZ194fyN4wfX/zZUWT9trJZ0qc+Q==", "signatures": [{"sig": "MEQCIHVK5RjXoUltsgbld1yD1N3GZ/VCYxl4eiafIc1nUpnmAiA7cKH5FusehXwaNxOwpFjstLSJoXVBEho7FfONzpIjsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoqCRA9TVsSAnZWagAAmQcP/1g1ba5IvcFEZ1liAg9g\nOYqJ8ydbNpYKR40pfC3FAuQ+DU0AdMxagBdRyY1HdqAyazi38d1SY9i9rlzT\nmUdjyAZO+xTo8WZXn6TZTgeF3VbokUKLTpjK8XrAgAQvBO+O2/u6oLP7aOec\nzaN0Y9gzhLBNJcj/4gpNBvftOvog/CLpLxmIaACYRmQl1EeRkwj5i3BhVWSr\n2mj6FWcqJ3s/CsoD2lLmYGj1EMsQBHv9UNDTomzjpWM9kR295eyGOS8yuHzn\nRh34LuFnS9KiyaPjPmD7L9Ti4vjMUBwZWaKvVfkuH1qgqI+AtvGl9pErd/ep\nAUVbT8LZNcLzvusOIr4V1ZgkwrgyE1C6luJ3kyaMIVwVajr3NLkVQ3XspPcO\nXuHMwW0II0XcWqZ76sCeJXJkvw2ssk4MuUuKq/pT94v6Tj6kHgqxSwDThIHz\ngumZcQ21MmLM+Dn1CEuCrXddZ2jNk7j6PjZWaJUJtjj/p3csLryuCq4jMNdz\nUR3ifjhp27E4eayOEmYbNiE2TS83ucPjLGOAMxOI8nMSHlkiHuLj9j4Ymuas\nwN01Mb0Yngab09agVktq9OB3+utPvDVymyQQ2Pw/N5PV6LbrXYDGjHLvbxTx\nLw3j99sfGFneKvB2kcyVmBzIV1nQIQyy9WeOWS7ofZWiRSxT/fYcAP+QYHxp\ngE1f\r\n=DpRX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.10.4_1593522730600_0.03256386718364879", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "0bf9cac5550fce0cfdf043420f661d645fdc75e3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-GFZS3c/MhX1OusqB1MZ1ct2xRzX5ppQh2JU1h2Pnfk88HtFTM+TWQqJNfwkmxtPQtb/s1tk87oENfXJlx7rSDw==", "signatures": [{"sig": "MEUCIQDu9OeeOegWkJ40xeKPGexFszePCWqA745RByNiX3MwjwIgVAsQ7nIC4IFc9Y0nBRWtnTkUEy8viLEUFOAu4DR2ncQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/dCRA9TVsSAnZWagAAX+QQAJS9eQPuirleU8X6N67G\nSBsBSnFIkEa8e23YdphtUewD1w6M+gTxKi+QQSkB5XTBs5iW58ivgSlhdAzD\n6KEcgqZvOlSXzRT1x/+sRoFHQLYJGbvniqpvjtpZvwx7hgiibW60nFGjqqRm\n8r85e6hQ8GPQ8HmMFa3f6NfkOrHWh6eT/uvtlKe7t4H/MUtNptBm5UOgP9YX\nEjDO8nSn6nqVq5jRHpcZqQXAMDJIL3LHOCJkLbDrqbg0UJhISLujV4fGz2I5\nCdC9muwL8ElBl8iioea8laT+sTl3XhHj/D1onYmuviZcBvK8yaSiJgectWZQ\n/zaix3dyDwQWcHhvRnFeP7KwIVSDaRqFaomnpI8Qo90HwxPgQZppbtkfZMpM\nKBwK0FT3LZAaZ8oPlTmveMCd4jF+dplHKewdwcQ/xthYTfLMtvlDDC/wlDzX\nc7435PtHOunlHKB5TcnAtc0+lyskMGGcXBFfpFE/Gz6Ld2c52XZxk78ysod1\n0QzLj6aCWRuJtZhPs2kl3PYq0eVyPb3zVZ0BiaPobUy5X8n/cr8PnKS3VN9L\nwTQ/IiJezFeKrOM3jYB5Gvn2iyDPWDgofpFsmMimcLaAO9y/ZGDPIiPUXZuB\n96e7RD32D3hmpvnbln+iofWxXIT8HUAn1zw9SXCittt2N2e+GOAlcuYHH+gO\ncbvg\r\n=XQ/q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.12.1_1602801629204_0.6457629936992084", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "db755732b70c539d504c6390d9ce90fe64aff7ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-xpL49pqPnLtf0tVluuqvzWIgLEhuPpZzvs2yabUHSKRNlN7ScYU7aMlmavOeyXJZKgZKQRBlh8rHbKiJDraTSw==", "signatures": [{"sig": "MEQCIAwnw9tpw1qaZNxaQ8SdmJc6Qoe12s9Ngt6SOfpuRZTkAiB2ERmfvqMtFsturjIweyhFJtjYSni93gpOE64Anq0SMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgrCRA9TVsSAnZWagAAi5wQAIz0uzF6hb4Cmv0z6H+0\nk4hsVWHK+xMaiAmx2lc2M7RgO5ZwRAeH21jSJnGTol0OnMn9YFtsmt/VbrHb\n5l0ufmE4y+2zK07qZpq+xMI2KLJxRGU3lXSs+qC4QCOcSSwdiATnDBn5Ro0I\nuJFHDgcfUIqtkcK+2gDbysAy96QB3kQ8LT29VzUMvw8DqrDq+yeNqYOE8px7\nDINbdiDVN97RmBbHKkgUfC7fCBP0FHoiqULHmGv8mT3aQwgfpiPVojDna544\nX38StpzhE2AVIfatVRRKJ20MNPwEZh/GRkD8jp79MGQPxDZT/6Jn2n09n8yK\nYQ05mvGTTIlkRAxe+GrYZWks3NM1CchNc5NPoTo9kjSlOwyPx92vbZ3zc5SV\nfl+eYxso5LkRVV+DA6a3NZOcXbx+nZjZAuDMpN2uv9uyuY0re0UZIF/qFWk6\nKCu+JmPZdjfJCkYGisKZl0JM8pxLu/s7d7EroKKIZrWjruC0LEc5Hzjaay+F\nDZADbmatlKr3FkL1ONCuRVSMLT7Q7+sndSx3L/H/t5JkOkW0TmELNUpLaMyJ\nVykEoc2hxyxxgLSi3fBu3eFEk5bytuFPwdMRucBewSmZjXaElZ853PnNMeD6\nJbZ8mHcZ0kZX1J4QA3QePQcv7LUNVhszbbLFnMm5v7Y+1TNIqt5wHhqEkBqj\nZhPg\r\n=MfkW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.12.13_1612314667032_0.8686708694379073", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "97f13855f1409338d8cadcbaca670ad79e091a58", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-xLucks6T1VmGsTB+GWK5Pl9Jl5+nRXD1uoFdA5TSO6xtiNjtXTjKkmPdFXVLGlK5A2/or/wQMKfmQ2Y0XJfn5g==", "signatures": [{"sig": "MEUCIFeGeWZBiA3/UQvwe+tub6WzKYBl+1OsXa0G9AMkrKDYAiEAxpDAGhGEopHxV06VqMAZgFCFY5j4t9nqPZIuYtZF4ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrNCRA9TVsSAnZWagAAO4sQAIubFTG0h1GcHCxCur1u\niQXrQKM2AR8gCgviCDb2hyAGyxYADrkjdlXaoX/UUCu1s2Sa2l7pQXNpDdqI\noRCD/1OFbb5eiaZEWDs8+NnyUDMtuuO+EKBcdvIKQzzyM9DMsa+vLeyhtiy1\nxMXfMM/5gNX758/Kd8afg5nuImaM50umkXfL4CmGdLBwraN7i1Eq5NijaqR6\nC8r2EbFeErZr/NjI72r4rHOnGdL7kWYLvvD9LCROVZe1YmVbH0dXe/pYgRKn\n3oVOt5wEuAq1DUmzpGssyp1sl6xo/563PcKnqClp3IgViKWQPwEJOaT7Afvh\nDtKe4Fa36w5Ee6Yg+OAkW2nTjYkKYvUV5+ruCHL6cztJrW4b8Vp4gQnYEfqx\n9RVcNmbkcWeJayG2TuI+CjHSyC4n6L3jXPCIOG5wn5ioesDnDw/3ZHiO6PFD\nBkyM/KFa4mBQHXKqxrawGLvxAzee6N9auY6xb/Q68kXrZvYc9mZjdO5Yp3/8\nfCFKrb0XMHyL+tjmLZsOTETZ7KjTETfMWTr2HaudLBJCpu60hc3YNypYSCJ8\n7hWPud0Wa94YjjBB8oKmnVXrtcZ87i/KnrtKYXJ7QdWYj/Iq3HItrNomWglC\nSdFfH3F2E33PHb0R7pR9TcmPGZ/apxGPHekdalciqFYi0uMEtzCjRapLgdEa\nvazc\r\n=q6vq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.14.5_1623280333580_0.15427783564341335", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "090372e3141f7cc324ed70b3daf5379df2fa384d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-iVb1mTcD8fuhSv3k99+5tlXu5N0v8/DPm2mO3WACLG6al1CGZH7v09HJyUb1TtYl/Z+KrM6pHSIJdZxP5A+xow==", "signatures": [{"sig": "MEUCIB1DWB/e43cQ1B0arLMxIa0t3mXzvooYn3OUgfyTRq5fAiEApLmsTDXfG9LDLrIoL53u5f6QWK2jGMwMXW6WohMfiKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3938}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.16.0_1635551253984_0.18848407350857665", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "ccb60b1a23b799f5b9a14d97c5bc81025ffd96d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-ZbuWVcY+MAXJuuW7qDoCwoxDUNClfZxoo7/4swVbOW1s/qYLOMHlm9YRWMsxMFuLs44eXsv4op1vAaBaBaDMVg==", "signatures": [{"sig": "MEUCIBxwTuo2hdnvsNC0ZkHH0VswbqFzUXiomYkpIerTTGB9AiEA3PEGwdZd2gb3s3n7ZOyE+4O3KnlFb6GeAtTr8mmRB90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kPCRA9TVsSAnZWagAAl5EP/3La7adRf/3rFDAoKBYA\nd2yqvizj9FoG+ifnQEUuX/Cuwqo9ooQams6RzPfWLVm3yT+jIoXVrkhcAkQC\nGFxPy1FYBTMYWNYxcNdox3w9QJqXG7cgbwJMfPTVxgL4e0u8K7inKtFrQMu1\nFMTxfQf6/JjgLmCoxIELBeXpv/Xt9QKvWU4g8nUrl/cC3q3xlxbQLEKvzrcC\n4VEKTaFL+bDKIuXUAzAumn6Ru3KeF+nfHYvnm6tCWsUh4r4E1GCK4j/AlaUg\nxCcLj0n9oQybZjagoJxoIokbcu5wZLg68oK6dWyfCwC5Sw3MRGS0pIRoJ0VK\nehIeGecN5xgU/yNGUkIaTG4ekmCNWT9ql2cL/qVyDgenUjyRQ34on8Eg+qkB\nwMY2Jcw8pLnCynrGPnI/FPR3F/+4IvpkbNp1tRXxMQuf/rjtniY72zlcU0Wa\n42pfKzaqd4W0jxYd2WOwi53j5+h47Tx8fqOvQsPWqFbbDaYC1tOR5stuk2qs\ni9faXJAttR7lOIepqWivjU3u3/VmsbKSvTflREvTXWu4dP3iKbP59LGvBdEt\nA8/Xxy89/7aFo8BaFDYwHykZ/fQGvzxPhAE6uJhSrFwTY10duPXGXyQ/4mo1\nPIgVY33zBxHPuuq4vUTQYGU5qN4KA8pyVpxt3arv49oT0X+gd/kHzuYHwbKt\nqkum\r\n=OUos\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.16.5_1639434511147_0.012864255695961058", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "e8549ae4afcf8382f711794c0c7b6b934c5fbd2a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg==", "signatures": [{"sig": "MEUCIGZIDuMC4+a/KOwDbxy1AOADv1HvN6OUd0U78xg4OB80AiEA08HxVnwpsiIfX56jxDcCRUOxY6+kvpJoxfGwllEcT5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0yCRA9TVsSAnZWagAApvsP/1U4qUw2Ajy9hJinwuER\nwTiXlhfGiyoTqm/qD+2Uu/75O47OaKvR53MooZGboa5ARKC4KZWcOfadzh9G\nXFehb68TcKGxJsQ8dbACdliO2+TL5vg/pNzUM+vZkZlFYzXVkMZktLuK4Uoc\njN1aFHpui6r0UnrGs0JBCpJYH4mSpX+JZ5smDitx644R83MlCo4GpfPgBQEW\nPk/4vemgVVGHEvVaDSkg+x9OhFMJ494/ohAqPfGPdTV53nTu/+BRNjFmnna2\nrN35cZKl7gggXcIn8dwVXQFCIcEg37lMtcDmv7oqcX4Hp8UY9La9/LRPFzAD\ne0RrM35wv4S2BbPZixhP/r3bYr5BRtVQP06WYuPrNc2eL+U25WRH8UNpnw9L\nmDBIDCSbP6RYgx7d/niVVIXJ0iurm70sP/B8X0IVgSpXUmbAcJ1o+9yGp2RH\nqv80D4WkljmvOlexWOYSTmrey3kbaF9YHKfeDxLXnyGj+lAJItBg0j8T8SKj\nMj1vqxrDQyS/2iMC67CFrRJssgd3pPAhq0apBIuwEBSCfR+jYmYFl/i4WWU4\nS1iSj0r5y4T6a0w6jg7i67UhH0vd/6T8WwTGBPRNta3uMb9yTMpqRXmdQ2Pt\n2s2mwpk8Mqf6kW2i7RF+BWtF64YT3g2prgzTbATEIjlbELflGPB2N7s3paYF\nulUO\r\n=98eI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.16.7_1640910129926_0.42548316238913664", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "6d6df7983d67b195289be24909e3f12a8f664dc9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==", "signatures": [{"sig": "MEUCIQDX/XHZJxcCP1NevVPUekpy3GhoAFjdX/m2627jhYB9fAIgHtMd03wNHATr/5W95l0fMmMARP8jnZdhuo0Q+GlaqYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7JxAAkUv4HLhSF66zKk62mtl0+PujvCbkwNegG5SEwrA0W+NoXf/j\r\ngSfKmG6i89enTqfOjufZENiByaQxP/0BmZNuyJuxUn4qzOp5qRyNd53MqOys\r\niOxr0jKbPU6X1TWJwTwNgRO1s+VyJ6hADPaDX06fLF/KlTVRo3Y7+5+aFAJT\r\nrWsBnaNUAkL4YLLplnA8UwyDAorZ+9ZnqBC7mRvoU9anC1xQLgx2ggZOZDaS\r\nnyMxvF4cP2xt1aGELaEDPcSdGQGvBkHUK/aZ/dwUSiVkGqm2X9w0XZ5WooAc\r\n/BbujFvRizNvQne+TbO9tHvEtaKmAI4KS3j71Zg+nPOB3q/4YtHM1ygnQgbB\r\nahowrUJqP+0+9y9y4ZdflkiBAXD/naaONFhHzYOA7k0pyAP79fWmlz23uLTy\r\nAo+gJTfaOVjNk0kpKMPu0RPcJj2RkMTF3tJPrjQ8JDUpU9GVjUAaTjHmmJ6E\r\nJdMVl7seX5vqFS3rydW7MvuOCVIaMJ2/ZW76KcC9qyGexFI8dWEAI+5tU9sE\r\nbqMdgw/Dhp2bqWCTocX5z7vFB5Kz0/UYH1DyMqAZxRlbuUhEhJTikDuWyNIH\r\nlaXCLmpqvDqXbYtuffTcRMW1hW2i8P+iIWWbDk/B4s41VHynLRs7x4R2IP7N\r\n45wcICiIZPTffQv5eO8si1lBI8hPP9bPiAc=\r\n=zKG2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.18.6_1656359404308_0.6702507744786244", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "fdbad0c13d8e519aac0c2d25211476eeea5ae8fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-eVRTq4/3W71vdNlOGApCn9k8Q+pwpVKMepoEL/saSTwEQtRgXlRB/0sP1iNZSyOHdwjySTna7J9BnKGsrgOiAA==", "signatures": [{"sig": "MEQCIFA1PVdKnqHITDHXjQhwQtt8MCF53xrWxnjiZom/bPpxAiAIcWPKTi7K6vJbpXRVGj1kV6dZlkFvjWiac0nZwUpnng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq6Q/+JxEfK8aiV/oIi3t3V384kOs7jLPGS57XfHWsGHUJJTOvQ8J6\r\nmSSe0BnI/ZAzfKigU4Y4lDpGK6hH+8asT+oLqE7h+jBI4JjDllSa6Uk2dI8o\r\na+omrUzMKt7IPkEcERW1xCHe6Tk+/JRSmMnncpypxK5b29VSZYrgNSRxJ/TQ\r\nKEO7wVIFGf2yu4jsdumvCueFYDCYAjlKXNy1bjyAbQ1nA2EJy6pquskNMFCa\r\nMMXicPRTiqWVxbSLJyvsM+z3jy6/XUhjVXc5CfYRU8D+crG/P/uEUhVvty35\r\nDqY2FDfBmOue0f+M3M+ITO9TWUzLecC86zQOS7r1ooy5ratm8cOXYIT8NQ+6\r\nO2y3rlPLwHjKT2cc1LcK0q8WsXjO3pmKAPecmaP4IBXfo9RY1RUolbj2PVj3\r\nIIXYu19i7vtJuzMI06FuLQd776BMDGlTuia2ngqPKdOFJmVjA/NKSFNlbK5f\r\nGGFkJ2ddoTBw9xp6q75l2rlHeFMkqZ8fFMBAZo8unILUee/azQopJ2z2PczF\r\nvVAURu3vVgQToWQ7m3kfLbXEhltneoedlwQRgET9PHUGFPolFHxINnLtx1tg\r\nl7ZEIH008geset5LIf11SRHfHA1zWq+tWjLz9/8cBu/LMpCQkDKXRVt9af0R\r\nyLpyX5TzY57LnNRIPfC9bDGRKwkOW/hFjJ4=\r\n=Rlpl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.21.4-esm_1680617369537_0.01819436185848211", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "63e941f275531e3cf00310da1675b4660d078b5a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-93HT6qkfTOieVq3AjM1rE+eUZqLGJQGOCBJrpqx/mCuJo1AIedxRhvgWeyokVOxZ2KQhZ2A6ArMibtV+O9glrA==", "signatures": [{"sig": "MEUCIDErfD08Cv7S0S/71Ex/BCnnhxvJ9pMS8sQTSf9iOx8XAiEA/V9pyFKWMhhUlQJoiza8O37EwjYi53v7xv4TMhkB9uE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohwQ//civhIHtprHkUGqwdtyEf4QVauOTuVlcdRkCl+QM7WX20T48r\r\nA8PyDFrjJDC6xOgrUpBacBraWIV7dDQk1hc0xJNAPWGeIOksPd1uGEj2UbV9\r\nG0VGjwaKA8Hj/+8ZtrQPvdiarFc3yErhmWJPc308AnkFqZ6WmwFP9WFl8/Gv\r\ncmJJcsRgw9wsh1bzo14qqDxmwNwwDJO74TJ1N4AlCMZ28LXX+iy7kxYm36wf\r\nnOVdMbzoPT/dBrOdpkXGBGRVPgIf0KqNQpFaRGrq4MiPTnMIVDt+L6Ewyb+3\r\n4S+spdpCkI1YFgLZNq6nJz7yv6KshQ+fywvM4ifY/wcV76X+OJIWz8CZvBSm\r\nU7pCdBZzT6GY62D3iaF36XEt7ZISZtYI8ubdIOS3QkwWRhBBJgeW4MFkfu+X\r\n4xzDNwlWlFN8Bxb5JiMW3m3k3eoDtcLLYugLbQBeflfGOm/c6Hs0V/dYmuKm\r\nAP9CKPzfLsSOZU1YRfdSrbdQeSrEG87X2bNz8o+XceFpbFg4esLWEg8+fm2L\r\nBqn0xLShwAVHhnoUtkCbM6zQOGgt5RiCnaAPUn/ic0INFd+of9ULWUnRpoBW\r\nXU7pSQtWZ4oPbdPGaP8z9nqZyEq1mx30YBeMpWCei2IEE3KzLxLRqrVSMEd3\r\na3SA6SKXhEhRSDIQ4lY+fW5mazF7DsmNev0=\r\n=n3ve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.21.4-esm.1_1680618082753_0.272666597686966", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "df805ff737ec870de299c552e314b0b133bf134f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-9nn+gSVx2eJn8rDpB1JS0eeWHVmTqD3q3Ti3aWpE47X9W4G+UdJBDAZc9AGpyCANlWh7LW9oCpYBtzxBT9mTRg==", "signatures": [{"sig": "MEUCIGkHZpmZVZUEusevXc1f7/Gssm2HBFgFPg6cIrEd04JiAiEA5v8lmSvM9qvSoQENVrfcBypOB6GUkSXHFXBG4y93obE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDabACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbuRAAjQ5XrSA/YuiXy2vpoNyxFmNQcvr2fz27IFd6IH3mnf+bOkWo\r\nWuMM+5BE/mrDVlUdTPbBJagIOAAvJWjOL/hhLyvv+G1kKvjB+KUGnwWQDJzD\r\nzOoOc5WMmm+1c6dDamX1yLXatePHEQ9OXtNCpkGijn4NhF797V2c++xcTEA5\r\nwa/iyTXAeirzjnl19ONbp6VKbWD54mS1L2avtrIpBZZkPTuWGz2DwfPUTpUf\r\n41wO1Kc/ez6h3sBGeJ2CQbwA0XzMPEcBkhOI3TVjuj48UYPSzUnmDZKOV8Ew\r\nTAnOVOGVvQxEIzVmhwkZNlQUVqvdtJmmJhhGolmcMvNEqbxFyFpUhUzepaG2\r\nl1Ripxhr5YfbQm12RlFs/zDrbi7TO6dAK0JYcOKXOOSuYjE59Hz1mhZVrbJC\r\ngArJern7r92ifDYo/XPlkfJkPnsK+XsUTBPpPnyejOc6EURYra3zUq35bRo2\r\ntNe1IY2EsFtvnyrf7J72B/2Onk4MSeczYfr8KW8FMgiomP65k4s38vUKOxZd\r\nobl/2QWOGazyWox1bsalkVDJ33oU5MAPaIzybuzHtqQv9l1gEG656MXpAdCL\r\n/rd01of/pbmcY0xsVqRzrPb4HbQkNuf0eTudFvXUvbmWJDf/jvFylL43ELTQ\r\nTQlNElqOeG1pbwfiPPtqFUMUpgcK+0Z311Q=\r\n=/WrV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.21.4-esm.2_1680619163195_0.05531095312860401", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "85a613b1cfe03433a49cbd655ba390fad658c658", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-F6/HRR/Adleg1TE2bk46T9Ia2aCxXRlPEsqeP96S1+cPJMxfUivrIToMuvGjAzYhme7om1GVwB05LWStsMAA+w==", "signatures": [{"sig": "MEUCIQCxrQbZwmxN/333vPFnNRZFC7bk1bBgsyOYq4Hm9Ey9kAIgZpjEmW/kdXJdKBYJzVdgXW7pMvJ8NQ14kaTjlEyFXa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjshAAoDmhuJ+QGaSyjrX6IiF1KXDpR41gIxHLJabpTigFbdeYNMR3\r\nAKqhAKAKLNOxFrLROvdrjenQ+YWDu4lrxzYeBef8EB4jBrIblUXUPizRy04N\r\nkACFbJL3LEjseJ52AWy9pXkS663y589RkRRejCXs7x9lXoKnZu2eIJsBCZFB\r\ntv4c3BmmmEOmxMUeRt/ImcK9vv3EMSm813kOy/meYPa9MJCSGULcOyJGC1RU\r\neu+ADXzkjZ35LcB5nx6GvV+Xx8U78Xj8CF84et5bXH6ERBY3bYXyoHt89tVR\r\nLPKuU+llBWmYmsWFDsd4ffGBxriUEDq5iCJ33BxfrTCgOS1ovq0sPMSns5cj\r\npLKVbSZNau4WxRhnj7Ec5JG+ohGHF+rc+nu1woG6H5GAU4Uuvsb2Xgu96WSS\r\nd5+6BMmUZv2JbcSvIAdKb4uPt4dCLY1Jd7DjvBQG8baqzZ95CpyQfuc+/wAe\r\nv0s+62dzl2PfT5wFBqDipXuvbr2PgShaSWEruGeg45SfdUVRhpLLtT0dXLNO\r\n/fCHo3Oy9uVcHKOwO1FUeo5uzFYEiPKal1KK4XgeL57PNDPn/eSkfapuHSAG\r\nnTHb4K1WrW+yH1JFHIJ7ll8KrrBYAC8CAZmwWJbobluQlMvWX53rL/lZawa0\r\nazNPbXMRbhxICqqUeWZgILXVgBeEGbyuuY8=\r\n=dJX1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.21.4-esm.3_1680620171606_0.2521494912435802", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "4a5f29deba16be4ee7a72eaeeebadacc3bca7258", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-jtZTu/xKhr0tNrnU9/dlX59Vx3o6VvdcKgvA3UMgmGWMt3sH/kfm3Cg59Y79qKX09Z4z8QpwNZETt1cn9QT+hw==", "signatures": [{"sig": "MEQCIFlRw2D6I/peiPUHub3953tdEhTvvlTum1vVPriOL/WbAiAt5xe0Fjk0EN/1Vxg+G/A8fJCxtGkIoN2qx7edHdSKGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnbA//akP3+1eFYu+4tnqFyEJJwr2Mg7I9JEN8XD4j+MI1+JdZozYh\r\nqay1HyJlDa+/ygO5xmx+f+vxyrgzXdhCXBmWKHaw+4PgbMnPfoGyj97TwY6X\r\nWtEPEMveuXylFgpt2sC3rXbC/HaPZyhC1NJxH8qu3tQNjjNZ/NLQ6szompeu\r\nIMAX2Qp6Nws/5DvKWluMne5ylqUsVoIP9OYTqqEMXtKXXUG4TPGAszBvMyx0\r\n107La1Wj4ku4dR9euvV5xUorRPAx8daiwsWdetu2bWYBJBd+cNBgbRMwBWzZ\r\ng+X+fkGoBFzKT+RnoQkqFD9sOXXpiVxRlhLZ7SKQ+HGrvYjoJLKUXedJUOXX\r\naKwj3O3zL6DXNWGiMUmUqgr+k42254DfQxU/2UvRmrnDwwDS1Llq/LfiW9PB\r\nbfMC0Izq58VEVLxRZlxbtb1z7TlEb1vLmqVVDNT/tGG7lyy1OVymAxLvgDX5\r\nvOCeKbOMiYAsdj06mE1DlfWmkIoz7P088kmjtNzC3rLxkuSKb6jVn+xmMqr/\r\nZ298WmbkH08orFOHIHb+lwQN56Zq10+tq7XoIvGgFG6/jekVgeUDajoUeas2\r\njBnj6KZu5YT3nyeRv60C0GaE3eFZ26pZ38PM1vbMe45qdeiwEEZTDzxQ4MWs\r\n7yCZopYiNW7GTumDtSpl+4K94pTbmKiZ/VM=\r\n=gWLe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.21.4-esm.4_1680621204335_0.24137307161306798", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "6e277654be82b5559fc4b9f58088507c24f0c624", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-vM4fq9IXHscXVKzDv5itkO1X52SmdFBFcMIBZ2FRn2nqVYqw6dBexUgMvAjHW+KXpPPViD/Yo3GrDEBaRC0QYA==", "signatures": [{"sig": "MEYCIQDAxoUlRCVo93eMZz/gNmGiUnxkMcS/HODOZh+/EJ8HpQIhAMGmchXBSSJzgo8hN5ynYiustyjs9xGOj7nCQH4r26G7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7097}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.22.5_1686248481975_0.8017668663083461", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "ba2078f09a0efb3bbca8117ce9fdfbd905d9f3ef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-MKX9cXyMwYon90Je8ibR3UcEsv+EKK2ZLfr+05cCo6phb7rGOENA90kF4n3YwAr+rpX7mCF2GkoKoElXlGa6Sw==", "signatures": [{"sig": "MEYCIQDsWCzJmZIrFxTEMggah+EZfX0A/XyjS/3vbPiAjy6ByAIhAIQSjTAhO8tNQDA+Ql7jIiFpHf93tru7jjk21osDr5Dp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.0_1689861598105_0.04268004958593519", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "31c3ef24d7813b73bf677a99b5844adb7ea1d330", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-s4cs+a5SpjjXZL5/9+qde6i7CTtfi2VzQAltq/LgZ3DBqhrl/vFECD1/zyKkr9x+Omm1fVetRZt8eN7dC5/y+w==", "signatures": [{"sig": "MEQCIBhFnLRyY8Pp8hvEYNgfsHuEWLOJSpL1za/yzMAcHou6AiAnmY46CU8c0MLQAFmi+5iAviHusYjJ00WzpjPnm59ugw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.1_1690221122434_0.019293786168529614", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "81054a74860c9c3de04ba531453174bdb1834cf4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-SuepnefDJ0iAwR9dIZWKV4jTBuvHrbKwI3Gjw+7MAvigdD0nrbXYicNA+ifp/GvRxL9uX3KOLp+vMfvCBjOnwg==", "signatures": [{"sig": "MEQCIFyhJeXL4jzfIx2i5Gdv39vurtjzTrnujpc5bxTMdY6jAiA5wvgVcFqir1rpoBSVKcGncLfzXY3H3QazKvNBsPzFTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.2_1691594097682_0.5126706232081109", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "ea41f0c3446b303bdc42deb04d23375c9efea4bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-xhf4+vQqXawZ3FR4gDhbyqpApntHOdayfK3Wx589pcpVnMoV8Tv11n1Xudzlav3UxKpOANhXc92lXtrJqCga/g==", "signatures": [{"sig": "MEYCIQDQEWNf1kqTQO2j9zGFCfCeNIh68mB66nX4QSuDi0ANUQIhAKhI07hfAAasOVuS/fIvWuSTzU+pXGdLjnZFlNQVCl6i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.3_1695740216391_0.9812064306654547", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "72c2efa2c9360f8dea446d75b157271f0f61ecb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-EehMCpHYRaIWNiX2JgPdhKS+s7eVO8G+FI3vyE5hgXzKBZ2ZHFFgmusAh/vdfKpIjHTWDFsYmdrtMf4uNhQyoA==", "signatures": [{"sig": "MEYCIQDv1AfV3kqyktWurDJq9X17TL/w5Q9SomDz8XixqJnXDQIhAKQr1PUMX9JR1OHJLLZGHCpRPI5FtdBrdh1OydB38bjF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.4_1697076380607_0.7690741071175349", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "97d82a39b0e0c24f8a981568a8ed851745f59210", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-ED2fgqZLmexWiN+YNFX26fx4gh5qHDhn1O2gvEhreLW2iI63Sqm4llRLCXALKrCnbN4Jy0VcMQZl/SAzqug/jg==", "signatures": [{"sig": "MEQCIArxkkLFxjMgSj2ZbMPkBl4WDNVFJMqR5FBXKmdClqwRAiB0KDcLbKzPcgBs7Ux6d10h23O7CrqFM9d/q3jxH9XC1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7176}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.23.3_1699513441032_0.5152654881648573", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "89168fdfe7b2a553d0568890795f545cc6638390", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-IQUsXJsB7xxsqIn9cHt6idJQTrTIBkS1N25c/ft2B4nyliW4e0ZjDU83jNkDSJSV+lr8QG/gslzqj/0/7FRupw==", "signatures": [{"sig": "MEUCIQD6ygsYz6uRrvnKC0zn0hwbJzycVkDceNZIg8TFyC5moAIgJ+RpAUivsn31qqFEArpCHI6fNxieqyg8cyQyBrHfFvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7196}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.5_1702307935185_0.14337470387993712", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "767b03c60446f6d888bfe5e68e865cd1b4d5fabf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-FjDJ/Vl1eacTqSkaY2oeZrY7dWVEWofWrREvWfhzOhIOoq1wT+z/rGqT2OpVWPfTkAGzx0lh5Gn8j+CSrXaGUQ==", "signatures": [{"sig": "MEUCIQCX8MkcVXmU/udzaIwNJPMIXaxZm37jUtuRdrszulhpugIgFvQLx1ff1o8XwmMjGG4PMmNaEpQssx+qL888IKaHlZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7196}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.6_1706285648714_0.7674062192671678", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "3edbce4e93fb60524939383d852d07c954e82654", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-F4DvYSvUxSM8IfkLGIQxSvObqhZBt6qpXnCLWIqQbahQe5nA4B4udH9LlcJU72+CwscPhRXo4Pi2LUZXymTIWA==", "signatures": [{"sig": "MEYCIQDh6rvn5uSWHK4MAVxj0oNxMVHl3HbQszyqNwxp7SaKNwIhAO5UJleo3x1/sIWip8r911uSPwAVEy/dDEsvAzEdDQQT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7196}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.7_1709129097265_0.8381669166236967", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "ba9a09144cf55d35ec6b93a32253becad8ee5b55", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-LyjVB1nsJ6gTTUKRjRWx9C1s9hE7dLfP/knKdrfeH9UPtAGjYGgxIbFfx7xyLIEWs7Xe1Gnf8EWiUqfjLhInZA==", "signatures": [{"sig": "MEUCIC/yzJdehNevMNAi+aKsbDqc2kqgYKNVd2x1GjbKAWocAiEAnvQBFII0IOtTYj4gcjfykafpE2rd1XQJc6LmCKzTOKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7107}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.24.1_1710841744433_0.0757785612595756", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "db2259020db294e93efed9aeb9575513a5ccf13b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-gz3gcPbLYLTqCluS0h5bdcH+IgeX+pcNMIEZmxHkaApA7EZ/RjPFVph3BZcbew/XhOda+d9cnfPoiCoi2ZkTQw==", "signatures": [{"sig": "MEQCIDOq+rI6aUSFgaWoCP32CZS4PEJXDAjrpbI0WofSu66IAiB1tMuKO1Qe16qn5crYkbol6Fe0RY4TWbMhV5Ck/MBcew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7110}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.8_1712236792487_0.6482764470826292", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "ef734ebccc428d2174c7bb36015d0800faf5381e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-xnEUvHSMr9eOWS5Al2YPfc32ten7CXdH7Zwyyk7IqITg4nX61oHj+GxpNvl+y5JHjfN3KXE2IV55wAWowBYMVw==", "signatures": [{"sig": "MEUCIQDKM6c2fv5Lk780ND0IJp5yiJG+Y8DdCNOlPnZFLWx/bwIgG7rwTrLxCP/LJbLbYT3G6wzqX4hWp5zFUQ9Wj0YevRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73044}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.24.6_1716553474867_0.9153099062127557", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "647568312af454ea05c5274ba25fa0e997a5ae81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-SSXQfHJyF4wM0cqTjGIMIfh6nZ1TMCjcQa48IgyGzIhRjTw3Ei8icsl7gHrO4baTBG3uPHfsDEpDKqJJkXU8OA==", "signatures": [{"sig": "MEQCIGuSBivXp6M2B5Y5zztnp+fgFB9G+nwBbrASrTjSHHWFAiANftV1L2jPfAKJMHr1awpkCG+RR7v9f+kSV1rd20Le2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73357}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.9_1717423459754_0.801360675972834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "70fa3f314c750c1a75a82cf7446849e80e52527a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-ajzPWvrcxaRSmKPKJi/DVvG+ymD6kSzWY5MAwisppNEwBw0GbyZDnpCDNy9tWCpObS10FEet2Wdj2B+S7ITrxQ==", "signatures": [{"sig": "MEQCIGFi+kkKnQq/hybFO1lIYFyPO9dQ21488IWTGwOdZe/rAiBnAZxU95NAbDFwqzd4uvdycrcGx1HWCpILsTzi1e5WTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73364}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.10_1717500010494_0.8195377295231232", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "85448c6b996e122fa9e289746140aaa99da64e73", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-KsDsevZMDsigzbA09+vacnLpmPH4aWjcZjXdyFKGzpplxhbeB4wYtury3vglQkg6KM/xEPKt73eCjPPf1PgXBA==", "signatures": [{"sig": "MEYCIQCGU2Mq3wpxFsX/emyEDZ8FHic0Zn3ypPRcN/XAYDbTngIhAKfYooz0QzMNazOA/CkWH//p3MmdZeAd9LvwfsHYETSo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73040}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.24.7_1717593324645_0.3913990449468856", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "4d90e4c77b294da52ccfe9d3ea1b5f6d66655f3b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-QprsJyMNoO4nW+bIp7Znyn3Uvmwv8kjy4RBWeDtThRLP3hd2y7fN+GRQ9z8VcuIwCVX9hjEO8zuwmOkUIG14Sg==", "signatures": [{"sig": "MEUCIEH7zUYInQZLfPFbSwRDG1Z+3NWGnD/iWNECtNGpX0WWAiEA6shzY4raZCI7y2SfhiX8gNSF4YAojxmjGGZg9WzPvKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73253}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.11_1717751735073_0.7076139535492423", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "a8c1ffada4b429c9f6f7e1516827f78c36f32330", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-ZX3eeHlONmENIZiVqbBdxMhr9TqY5bq6NYrGldEg0yvGZAcDoTpFdjnfOFFsC/lqYv8s7viAfD/5upu7t41MEQ==", "signatures": [{"sig": "MEUCIAad06zEx6m3GHJHC84UmqpRwNwpCFa6O/aWP3Wfi17cAiEAysftqndi7kbFrl0T1Z8AITBEu/y8fQpGjBqXThRir1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70049}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.12_1722015211910_0.13275938632696782", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "92690a9c671915602d91533c278cc8f6bf12275f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-uBbxNwimHi5Bv3hUccmOFlUy3ATO6WagTApenHz9KzoIdn0XeACdB12ZJ4cjhuB2WSi80Ez2FWzJnarccriJeA==", "signatures": [{"sig": "MEYCIQDpJbFhbWf0HYXSKj7XdoB4/vwsZh1guQbcPjTIcIwRbQIhAIDTkSdqXaRed8d0NKPYKfa8aRGwur8LU+caLMO9SKXK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77578}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.25.7_1727882092946_0.7823004393644859", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "bb785e6091f99f826a95f9894fc16fde61c163f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-MUv6t0FhO5qHnS/W8XCbHmiRWOphNufpE1IVxhK5kuN3Td9FT1x4rx4K42s3RYdMXCXpfWkGSbCSd0Z64xA7Ng==", "signatures": [{"sig": "MEQCIAZq90v8Z42qTty6DnQnmxXlAgstFVAVe0h7jvOijKRxAiAa5mICWtNRisI/ua31zJ456KxhBs9kr/xfOUoaeH7VEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7107}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.25.9_1729610471351_0.31130384621754703", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "32e477c086c3789282cd0df8d050da11481fe861", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-qoG7kx61nfc+op7Z0SBoB+Z8iVjcZVqrSdORyR+T374i4dHKQyS0NCnSDsnmLAjyYdMLV7xra6vNoTl6T9NUMg==", "signatures": [{"sig": "MEUCIF7LRk2X7UmJkZ/Xu4ErErU57bqWrk55YJIAsHSWvVi2AiEAspoRcDLYbfM1ePFrKEJbr3AUI1mdct/50pyaMQ5WhFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7448}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.13_1729864453801_0.03664962012122075", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "94a8c9bcfb9a10e126475343e290cb32d39d563d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-IlhV0eXz37/Z970Hr3pjsdUelrlvQx1z5aL3JbSROyYXRm+sD5S/lXEqZYDm5s7cbcrk0n+iysddj6hUgdfxgA==", "signatures": [{"sig": "MEUCICPAxgZq8bE7VbS6+CqmbF+z8b9gVx4klhjNo6rGxkSJAiEA47W8xkR90U57gNLnOgH3KcJ6eem3MRw/454t21JNJG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7448}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.14_1733504045841_0.37246034572928277", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "4c86b3daca9f6ac51e37dc6666ef85797c0bb14f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-upDhhq5VwGOnCow7WJHMQ2IdqBjrVbdDf65rbjT0JRU3zQgamnJzgCvlyHIryeOYdmqiUUcSRAsUBxbwFY8t5w==", "signatures": [{"sig": "MEQCIByohmv0HnumCyn27zB3livky19xA78h1iW9D2/ItoEQAiAwV9EEew36sx6IVgNQTeu8KndBnUMA5lpHJq/kFzmhCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7448}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.15_1736529870828_0.21371868762974322", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "4cd03585aae73b668dca89de268982b1314261a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-mSmax9IrhafBa8Qh03ogpF6somLtqeS9jPw3hQtCtfNTjlvL/EdvW1lLoYyaydlyOLGuctz4Aj189e/Xc3V8vQ==", "signatures": [{"sig": "MEUCIG75xAmNQQeumOvT7nBRAr43OXhLfFLxACH4b7NS+bPnAiEA9EmkTlZY1MR0ULp5f55kcpwyBTbFpEA0zZ8Jd0hNKrE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7448}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.16_1739534347198_0.5470480242358875", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "0db60d83319a00b6bf5888f87f1e1a27a1a4ae4c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-HgEUCCwYSjWMyOq3V09W/Jx/yAphaWgIjM6qJJhaZaB+yV3BAgtPA3N958Z08HBXnq4LZ0HZFtFy9rwTGb28bg==", "signatures": [{"sig": "MEYCIQDmemYmBHZ7TReiw/4EEiklKCJJcDna/83ie322SLCN2wIhAJQMyPMgjfEbRtthGvgRjdvqHb7Dg9ltWYHEbgUWfJmt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7448}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-alpha.17_1741717499754_0.3047922706270514", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "532abdacdec87bfee1e0ef8e2fcdee543fe32b90", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==", "signatures": [{"sig": "MEUCIFZqxEAiYgFWcKzJxC9dUQTTlrsHwjpC285eF5TaaX4+AiEAtHlZRzh70egZPZfmGYC0WAQAhBY/x8cA5OdkikmKqqw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7107}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_7.27.1_1746025736656_0.3280774897437446", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "dist": {"shasum": "f5c0099dac2fbab098b9ba36c1e3fc3d787588f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-PvJ7QmrwV5g7wEM4pw7hq0tXGGdm0YVHDLxQx5p8m7QnUezPQsM28ODvXM6IbvBTxTbKUdyktPH6K+CRY7njjw==", "signatures": [{"sig": "MEQCIEvnT4KNBQUsl/O1xjinXgIaM5yzPAoSwfV4ZOwVQ3+6AiBCyKvWG7wsZmqcRS/nHzBbACVZ7TCzcnobEbV8ZcQkCA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7424}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-beta.0_1748620269003_0.04829751400636817", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-shorthand-properties", "version": "8.0.0-beta.1", "description": "Compile ES2015 shorthand properties to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-shorthand-properties@8.0.0-beta.1", "dist": {"shasum": "6f1340cd8aea1d8b6da359b868f3c6d22ecdc374", "integrity": "sha512-9PkcA6NOWXBa+lBSnJ0e0meTklRyXgPVRRzbPWN1FpoJdZZpps7ajqwN65Bt82YCG8TM7FK0sqCJ1OYKi1pf8A==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 7424, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDJMn1iqR3lcPoyd/7BDVlgiXoTvf3PCZowkpyhHx0djAiEAshzVcwjKvkQi+LWndJtIl44MBVQ54RXHaSUHpBTGSFQ="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-shorthand-properties_8.0.0-beta.1_1751447060898_0.6815932022703517"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:28.803Z", "modified": "2025-07-02T09:04:21.300Z", "7.0.0-beta.4": "2017-10-30T18:35:28.803Z", "7.0.0-beta.5": "2017-10-30T20:57:05.220Z", "7.0.0-beta.31": "2017-11-03T20:03:57.437Z", "7.0.0-beta.32": "2017-11-12T13:33:23.462Z", "7.0.0-beta.33": "2017-12-01T14:28:24.774Z", "7.0.0-beta.34": "2017-12-02T14:39:27.043Z", "7.0.0-beta.35": "2017-12-14T21:47:51.842Z", "7.0.0-beta.36": "2017-12-25T19:04:46.267Z", "7.0.0-beta.37": "2018-01-08T16:02:34.836Z", "7.0.0-beta.38": "2018-01-17T16:32:01.141Z", "7.0.0-beta.39": "2018-01-30T20:27:37.041Z", "7.0.0-beta.40": "2018-02-12T16:41:42.765Z", "7.0.0-beta.41": "2018-03-14T16:26:17.022Z", "7.0.0-beta.42": "2018-03-15T20:50:49.746Z", "7.0.0-beta.43": "2018-04-02T16:48:29.518Z", "7.0.0-beta.44": "2018-04-02T22:20:11.663Z", "7.0.0-beta.45": "2018-04-23T01:57:05.288Z", "7.0.0-beta.46": "2018-04-23T04:31:26.879Z", "7.0.0-beta.47": "2018-05-15T00:09:18.628Z", "7.0.0-beta.48": "2018-05-24T19:22:54.905Z", "7.0.0-beta.49": "2018-05-25T16:02:28.522Z", "7.0.0-beta.50": "2018-06-12T19:47:21.853Z", "7.0.0-beta.51": "2018-06-12T21:19:56.929Z", "7.0.0-beta.52": "2018-07-06T00:59:28.410Z", "7.0.0-beta.53": "2018-07-11T13:40:18.555Z", "7.0.0-beta.54": "2018-07-16T18:00:09.562Z", "7.0.0-beta.55": "2018-07-28T22:07:22.904Z", "7.0.0-beta.56": "2018-08-04T01:06:06.811Z", "7.0.0-rc.0": "2018-08-09T15:58:33.281Z", "7.0.0-rc.1": "2018-08-09T20:08:14.902Z", "7.0.0-rc.2": "2018-08-21T19:24:18.035Z", "7.0.0-rc.3": "2018-08-24T18:08:12.027Z", "7.0.0-rc.4": "2018-08-27T16:44:28.151Z", "7.0.0": "2018-08-27T21:43:24.832Z", "7.2.0": "2018-12-03T19:01:48.824Z", "7.7.4": "2019-11-22T23:32:21.174Z", "7.8.0": "2020-01-12T00:16:39.801Z", "7.8.3": "2020-01-13T21:41:39.205Z", "7.10.1": "2020-05-27T22:07:33.914Z", "7.10.4": "2020-06-30T13:12:10.707Z", "7.12.1": "2020-10-15T22:40:29.636Z", "7.12.13": "2021-02-03T01:11:07.242Z", "7.14.5": "2021-06-09T23:12:13.711Z", "7.16.0": "2021-10-29T23:47:34.159Z", "7.16.5": "2021-12-13T22:28:31.271Z", "7.16.7": "2021-12-31T00:22:10.092Z", "7.18.6": "2022-06-27T19:50:04.504Z", "7.21.4-esm": "2023-04-04T14:09:29.693Z", "7.21.4-esm.1": "2023-04-04T14:21:22.984Z", "7.21.4-esm.2": "2023-04-04T14:39:23.388Z", "7.21.4-esm.3": "2023-04-04T14:56:11.818Z", "7.21.4-esm.4": "2023-04-04T15:13:24.559Z", "7.22.5": "2023-06-08T18:21:22.199Z", "8.0.0-alpha.0": "2023-07-20T13:59:58.240Z", "8.0.0-alpha.1": "2023-07-24T17:52:02.616Z", "8.0.0-alpha.2": "2023-08-09T15:14:57.897Z", "8.0.0-alpha.3": "2023-09-26T14:56:56.595Z", "8.0.0-alpha.4": "2023-10-12T02:06:20.805Z", "7.23.3": "2023-11-09T07:04:01.515Z", "8.0.0-alpha.5": "2023-12-11T15:18:55.362Z", "8.0.0-alpha.6": "2024-01-26T16:14:08.870Z", "8.0.0-alpha.7": "2024-02-28T14:04:57.434Z", "7.24.1": "2024-03-19T09:49:04.592Z", "8.0.0-alpha.8": "2024-04-04T13:19:52.652Z", "7.24.6": "2024-05-24T12:24:35.047Z", "8.0.0-alpha.9": "2024-06-03T14:04:19.908Z", "8.0.0-alpha.10": "2024-06-04T11:20:10.631Z", "7.24.7": "2024-06-05T13:15:24.817Z", "8.0.0-alpha.11": "2024-06-07T09:15:35.296Z", "8.0.0-alpha.12": "2024-07-26T17:33:32.111Z", "7.25.7": "2024-10-02T15:14:53.129Z", "7.25.9": "2024-10-22T15:21:11.584Z", "8.0.0-alpha.13": "2024-10-25T13:54:13.985Z", "8.0.0-alpha.14": "2024-12-06T16:54:06.069Z", "8.0.0-alpha.15": "2025-01-10T17:24:30.997Z", "8.0.0-alpha.16": "2025-02-14T11:59:07.391Z", "8.0.0-alpha.17": "2025-03-11T18:24:59.906Z", "7.27.1": "2025-04-30T15:08:56.863Z", "8.0.0-beta.0": "2025-05-30T15:51:09.199Z", "8.0.0-beta.1": "2025-07-02T09:04:21.061Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-shorthand-properties", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-shorthand-properties"}, "description": "Compile ES2015 shorthand properties to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}