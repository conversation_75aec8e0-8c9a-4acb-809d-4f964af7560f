{"_id": "function-bind", "_rev": "21-9aa01cf26f7dbb16477c93bbb7628ab7", "name": "function-bind", "description": "Implementation of Function.prototype.bind", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.1.0": {"name": "function-bind", "version": "0.1.0", "description": "Implementation of function.prototype.bind", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.2"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/function-bind/raw/master/LICENSE"}], "scripts": {"test": "node ./test/index.js", "start": "node ./index.js", "watch": "nodemon -w ./index.js index.js", "travis-test": "istanbul cover ./test/index.js && ((cat coverage/lcov.info | coveralls) || exit 0)", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html", "test-browser": "testem-browser ./test/browser/index.js", "testem": "testem-both -b=./test/browser/index.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "function-bind@0.1.0", "dist": {"shasum": "4d356a3bbea3a1226d0dde4749a8a80087cda3e2", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-0.1.0.tgz", "integrity": "sha512-4UP4tXl/2KpwfhzRR9vtQ3Ft5QG4om3n1QDCq5FkqnODDd2ca/qqXFiBdf/RHxjlDcjzpGL1ocTUyXrfXci1NQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNXonyKvW3aq2Dt5dDse7duLKV9Wq8jSqVk/93L6VErQIgPkMu0ju/ZNrI843cZj06HKkUJVzoM6j8ggH8kOZ75do="}]}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "function-bind", "version": "1.0.0", "description": "Implementation of function.prototype.bind", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "~2.14.0", "covert": "~0.4.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/function-bind/raw/master/LICENSE"}], "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "2e324165faafc0211bea7ddc5ec54b97f884e350", "_id": "function-bind@1.0.0", "_shasum": "00e4e206738ad45ec0017d62a7ef77d9917ab2a2", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "00e4e206738ad45ec0017d62a7ef77d9917ab2a2", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-1.0.0.tgz", "integrity": "sha512-ZdHaPFa9xBJ0eBlxf+Ia/NQ7DQfEq26SruzXjpHJ1Et5uLsWnEGoHBD6LSaxshBfNTEGK0zAxCrHdABRcgo3dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5TMzEEuM2k3O29zQ/UDd4+YD0KJfV32UbGTlV4fL1iAIhAJvp8l4cCyTdxZdMmTEQIWhA18n5q+sdSFuLnrCVXcEP"}]}, "directories": {}}, "1.0.2": {"name": "function-bind", "version": "1.0.2", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "~3.0.0", "covert": "~1.0.0", "jscs": "~1.6.2"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/function-bind/raw/master/LICENSE"}], "scripts": {"test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "jscs *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "71784cd83079ccd7f20684e959e1958936a0e3ff", "_id": "function-bind@1.0.2", "_shasum": "c2873b69c5e6d7cefae47d2555172926c8c2e05e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "c2873b69c5e6d7cefae47d2555172926c8c2e05e", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-1.0.2.tgz", "integrity": "sha512-v2124bSW+kLVmfLEHDpsTeQy+sLEg9gdD/1aVTO7jolX/EmBEq9+atKWYEV3w791Os5USi8yNyuUtiVEXMWiAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/fb09LXqi/S/XCiGGR80GFQWGnlb//2iDXvrTilfDrAIhAI7izsP+giJBxJFL5uX/9k+Uwe75W26i1/oIUkjTnwEg"}]}, "directories": {}}, "1.1.0": {"name": "function-bind", "version": "1.1.0", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "^4.4.0", "covert": "^1.1.0", "jscs": "^2.9.0", "eslint": "^2.0.0", "@ljharb/eslint-config": "^2.1.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/function-bind/raw/master/LICENSE"}], "scripts": {"test": "npm run lint && npm run tests-only && npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "cb5057f2a0018ac48c812ccee86934a5af30efdb", "_id": "function-bind@1.1.0", "_shasum": "16176714c801798e4e8f2cf7f7529467bb4a5771", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "16176714c801798e4e8f2cf7f7529467bb4a5771", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.0.tgz", "integrity": "sha512-rdjNZR1BePD6g5bTgalqkSN9eMuHgB2KHOBupLM8f5TblXwiV8nSY31dygkdwLNFn1m2KAkjFsREUuLNcU1rdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIChAIxsZ/Z+Uf2sKfvL7vSYyvIua0IhoERa8wsDlRDMpAiEAzdZkw5K770jnn0pTnBbjF5h9dkklqX/QJaDMU5+nPHY="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/function-bind-1.1.0.tgz_1455438520627_0.822420896962285"}, "directories": {}}, "1.1.1": {"name": "function-bind", "version": "1.1.1", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.5.0", "jscs": "^3.0.7", "tape": "^4.8.0"}, "license": "MIT", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage -- --quiet", "tests-only": "node test", "coverage": "covert test/*.js", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "1213f807066d1cb8d39a0592d5118f4b1f03de4a", "_id": "function-bind@1.1.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "shasum": "a56899d3ea3c9bab874bb9773b7c5ede92f4895d", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsVxPdBe+3LZpIF8YZMP7chLM+i5RVRwvtzCd5Yx7zigIgHw6sg2uwy+ItJamfc9+b+XTQOi93JrtZ+8F6YNq3Jz8="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/function-bind-1.1.1.tgz_1503906695005_0.1665907499846071"}, "directories": {}}, "1.1.2": {"name": "function-bind", "version": "1.1.2", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/Raynos/function-bind.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1"}, "license": "MIT", "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "function-bind@1.1.2", "gitHead": "40197beb5f4cf89dd005f0b268256c1e4716ea81", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "shasum": "2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c", "tarball": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "fileCount": 12, "unpackedSize": 31427, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnYrMYyelTQQ9xaQa84pR8N36UHNo7fToz4G3Xhj1ioAiEAo0Wl8ZCzkWU1VC9qLQaMxOvLxcQy3YyIWnTLHe6hmCk="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/function-bind_1.1.2_1697137699495_0.5322546821621981"}, "_hasShrinkwrap": false}}, "readme": "# function-bind <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n<!--[![coverage][codecov-image]][codecov-url]-->\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nImplementation of function.prototype.bind\n\nOld versions of phantomjs, Internet Explorer < 9, and node < 0.6 don't support `Function.prototype.bind`.\n\n## Example\n\n```js\nFunction.prototype.bind = require(\"function-bind\")\n```\n\n## Installation\n\n`npm install function-bind`\n\n## Contributors\n\n - Raynos\n\n## MIT Licenced\n\n[package-url]: https://npmjs.org/package/function-bind\n[npm-version-svg]: https://versionbadg.es/Raynos/function-bind.svg\n[deps-svg]: https://david-dm.org/Raynos/function-bind.svg\n[deps-url]: https://david-dm.org/Raynos/function-bind\n[dev-deps-svg]: https://david-dm.org/Raynos/function-bind/dev-status.svg\n[dev-deps-url]: https://david-dm.org/Raynos/function-bind#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/function-bind.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/function-bind.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/function-bind.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=function-bind\n[codecov-image]: https://codecov.io/gh/Raynos/function-bind/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/Raynos/function-bind/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/Raynos/function-bind\n[actions-url]: https://github.com/Raynos/function-bind/actions\n", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2023-10-12T19:08:19.863Z", "created": "2013-06-16T23:25:41.232Z", "0.1.0": "2013-06-16T23:25:42.888Z", "1.0.0": "2014-08-09T17:02:51.069Z", "1.0.1": "2014-10-03T07:38:13.045Z", "1.0.2": "2014-10-05T07:23:52.930Z", "1.1.0": "2016-02-14T08:28:42.411Z", "1.1.1": "2017-08-28T07:51:35.937Z", "1.1.2": "2023-10-12T19:08:19.687Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/Raynos/function-bind.git"}, "homepage": "https://github.com/Raynos/function-bind", "keywords": ["function", "bind", "shim", "es5"], "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "readmeFilename": "README.md", "users": {"flumpus-dev": true}, "license": "MIT"}