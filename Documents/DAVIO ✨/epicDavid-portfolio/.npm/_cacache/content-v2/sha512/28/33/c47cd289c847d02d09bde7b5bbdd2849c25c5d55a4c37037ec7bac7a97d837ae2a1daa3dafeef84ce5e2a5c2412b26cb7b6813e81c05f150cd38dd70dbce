{"_id": "@babel/plugin-transform-optional-catch-binding", "_rev": "33-20910c7e0cad2e920f2c59f79a16584e", "name": "@babel/plugin-transform-optional-catch-binding", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "ce11afd4834e54020a67cb3f0bec9f8a4f16b308", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-x8HEst6X74Aut0TxZI4s1UbUCtqR7IW764w/o/tTIDsm9OY9g+y9BeNhfZ+GrN0/TErN1dBoHNxqo1JXHdfxyA==", "signatures": [{"sig": "MEUCIA8NNjben9y7Nzp0JsNaxie9YWNSuRUkZ6tyS6VBAsW2AiEApLhKZmxzIQHeEaQY79e5ENe4/5R21nEq58CgTjFMeQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4821}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.22.0_1685108717093_0.21239505400676095", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "e971a083fc7d209d9cd18253853af1db6d8dc42f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-bnDFWXFzWY0BsOyqaoSXvMQ2F35zutQipugog/rqotL2S4ciFOKlRYUu9djt4iq09oh2/34hqfRR2k1dIvuu4g==", "signatures": [{"sig": "MEYCIQDnSOjAHjm2iMlVGfIsXVeQTnxH0QfWu1Q0/Zkp5unAoQIhAN6PsonspVauDYwO2Sviw+H66zAnUl2O9RoCcqFU+dt6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4801}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.22.3_1685182258763_0.9048073777666785", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "842080be3076703be0eaf32ead6ac8174edee333", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-pH8orJahy+hzZje5b8e2QIlBWQvGpelS76C63Z+jhZKsmzfNaPQ+LaW6dcJ9bxTpo1mtXbgHwy765Ro3jftmUg==", "signatures": [{"sig": "MEQCIEgrcbK6mclsmRM4DcG231luAsqxxkEM4Jz0DqGnQVG8AiB9Pte+Wl4URaEcoTH5niLhjLvnatkmUenApBf82CtZZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4801}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.22.5_1686248479064_0.5029456083963457", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "dea8d599aef063c4c273ba4d5188be5d69ad599e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-GDipHwz7b4G4Wuk0CUeh91aoRcrp90KO7Fs/6WfWG1MC1VluRaztD7FkyKJxC0w/A9ELP/73KKx2vQhUta9Skg==", "signatures": [{"sig": "MEUCIQDnZqilYj1IWFGcWZAVsV4ZGPSKGScO7TP/eVgO8oaXyAIgZgnmSIeetuaF1AK3sLXij/rcOnckcP8+rg2Znday/7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4675}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.0_1689861593940_0.23119428396928954", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "13b8e887e14e5961f1eda5666da4c20936f17f26", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-pu7otuj81ohg3m18Jgpg6P5JN8tl4uJvHrahFmVbdeMjNAnSJlMUr9Z9PAwWPKpQtXxnSnky4EBXOIzeC9WVmg==", "signatures": [{"sig": "MEUCICgvGfXJOZSu3cON2WpyxdZBjKos9cBuECLDwXNS2ucYAiEAlhtsO0Den6Atln/wEqH8CaOVaxTG+CPEh644oTX23ak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4675}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.1_1690221114987_0.49233711710934314", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "c31e80f06d3573f72b87e7f181a0cfe05b11c888", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-zDJ34u2nCNkJ/QP8nRN8EHqqa24zMi9PG71q5LBhcgar1/ycs05g5rrzOrBBxiBWKHBIRTlM9ZMDPFPO+mykAw==", "signatures": [{"sig": "MEYCIQDs8U60IAKeN1o8BNqISSSd5n/dQaAOEsH+EEUPduxnSQIhAIAPl7Ic95WyOLkx5oQM+DF4PKjHmSi04Nf5QcxV4NN1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4505}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.2_1691594093729_0.6912221997254848", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "461cc4f578a127bb055527b3e77404cad38c08e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-rli0WxesXUeCJnMYhzAglEjLWVDF6ahb45HuprcmQuLidBJFWjNnOzssk2kuc6e33FlLaiZhG/kUIzUMWdBKaQ==", "signatures": [{"sig": "MEQCIEeuP7hhgGli5HgRx5t+nvIwibDkljPbSmucuHZ0S2YNAiByJuI4ehjrvfJ87fs21ElBXB6wSplOABhsD5cCsHAUNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4725}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.22.11_1692882520252_0.45998867434085766", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "617263d678218591541281f3c3ec6480e0d1e21d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-DDHsHHy5fzT+TP4cXf4S0p5G/COE3Mw5FaEiH+LyMfUOR11XQrXV5yalih9iKzaijXANTfcQkmYqW27KBzBEsA==", "signatures": [{"sig": "MEUCIQC535exPi65HrSDjTVvASdQ79nKBpLxt1oQ+crQ06K0tAIgXCXZVbUGlVpj53BPqHNimTbekE1Ph8LCi5kY1xPd0Ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4547}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.3_1695740210921_0.35653839287677047", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "40fb8b32826c88210e5c86a940dbb2b72f3a044e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-bN7kG5adM89wJQjj9WMF87SkHWzO0DWPZgzRLJojI8lMXAYEtFmYTFyCDKZMwktlg9UR8jYe5qjxhbwryD8ZNQ==", "signatures": [{"sig": "MEUCIQCn6jkl4LPvyjzrPLtwyZRMORkm3xom0bpHcX6/XXqGKAIgF0t5np5V7ka2MfASaYofusc6ItDUOAMoBsswOPe4fw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4547}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.4_1697076376612_0.8736297580166656", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "362c0b545ee9e5b0fa9d9e6fe77acf9d4c480027", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-LxYSb0iLjUamfm7f1D7GpiS4j0UAC8AOiehnsGAP8BEsIX8EOi3qV6bbctw8M7ZvLtcoZfZX5Z7rN9PlWk0m5A==", "signatures": [{"sig": "MEYCIQCcneJekv+2YzUVq01BcxUHaKAstuz4Wa8in1P3ib3VaAIhAKII1h09Fsukv5727jIiKM7hj5GiRiiRl0xBugBjQ+Q9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4806}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.23.3_1699513434337_0.4655140978656733", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "318066de6dacce7d92fa244ae475aa8d91778017", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-XIq8t0rJPHf6Wvmbn9nFxU6ao4c7WhghTR5WyV8SrJfUFzyxhCm4nhC+iAp3HFhbAKLfYpgzhJ6t4XCtVwqO5A==", "signatures": [{"sig": "MEUCIQCoEanokzX8cs9teYEXvnCZAm0hGHJYevk5JDy+5rjU+gIgfyP/Ew64ZW5LqCgNCOF+PkqiNxdWvjr4CHRdV2OLj6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4812}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.23.4_1700490128367_0.5773479222685907", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "43e796834cc195e8d13479e9ca5bcf9719850e23", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-0G+5YZ2NribBipxS3g454UiNhmHmEoa+Woawee8A6v7bAnxUs2Pd58aw1i0GA9o6ze352p/iLllKZiP8h7gGsg==", "signatures": [{"sig": "MEUCIBhJsTDM83Pvy2vLjaqsh09Iu9QogRmLqroc3CwND/m6AiEAxGCp870wtPXX+S7eS0KG3Bqjz88UGAv48J82p7dFaP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4666}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.5_1702307926935_0.887199309771098", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "4e08a4876ef9fb7c7402423d58e904b632327d1c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-xH1T5+++5WVbmHnJoZoZoPmMT7C/Px3pp5AJtfB234wYjfDkeOh9e5NtpO6XtPcttr909TjHbZIo/i702yHXDw==", "signatures": [{"sig": "MEQCIFkLfuaIjmTKwuJAPo4S3VTFqK/UWC6WO+AIWBl5zxElAiAgGmW6Gc/D1eIytTbuBRmh+/Oi61mUYN+19ZImKkKyxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4666}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.6_1706285644463_0.49267858087247585", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "c40b6c92436b73356b8a4e3018e6b59db7380eaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-kjXrx8Ksmi1xrIOwofmM23tWFA90BT9H6kn5IjsQCd3FScYoDy4Ccmre0ewd/edD+baP5IaB1Ds4Yh+eqYn5kA==", "signatures": [{"sig": "MEQCIBuvZJnVmbVVS7dMb3bHAAfqnUObOU3PB/tBIe+abksJAiAau7M8VM7YxMh1h8LstNeVakL1NvNT+PJIFLaFaHk+Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4666}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.7_1709129090692_0.2724322399183441", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "92a3d0efe847ba722f1a4508669b23134669e2da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-oBTH7oURV4Y+3EUrf6cWn1OHio3qG/PVwO5J03iSJmBg6m2EhKjkAu/xuaXaYwWW9miYtvbWv4LNf0AmR43LUA==", "signatures": [{"sig": "MEUCIE0sIEyLyrd1SnkTgV1xZ3gwlberyc2wAe4IjMUoje1TAiEAwMaxvw/ucH6KHwy1sRhvPM8pDeXMBpkHSa8bU0EWECc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4872}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.24.1_1710841734466_0.03705470176677572", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "d1e0bd99d80e7dcb61fc4cb90cfa481e962acb79", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-6zj109GsAv4xq1yNthOwt8l0SqRLUSkddRBG0w3Wd2UInDXLpeE2DfmpCuHnIi81fxatt9oufqKcUNTfONVECQ==", "signatures": [{"sig": "MEYCIQCPWuC2N/CKAm+6QZOPlpHfdSApHV4j99TAuYgXZExNWAIhAIvkU9ZC1a1CV2OXLdyOuroH14YzMQ5u4WGS5u5BFIDI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4590}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.8_1712236789344_0.11187165043783809", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "c81e90a971aad898e56f2b75a358e6c4855aeba3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-L5pZ+b3O1mSzJ71HmxSCmTVd03VOT2GXOigug6vDYJzE5awLI7P1g0wFcdmGuwSDSrQ0L2rDOe/hHws8J1rv3w==", "signatures": [{"sig": "MEYCIQDi9Ai/e/jFalTzMftzAAiqmbKcuWEC0rVLly6Ml+tKfQIhAMqTU5EV6yW61MrB+3WfKfVGn6UETjy4GfS1HkNSf1ha", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70842}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.24.6_1716553470836_0.8022230478693149", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "4b5d84926f36a085e30120ec1774626911a61dc5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-DcmakBqj8Vl6yQiMBBXufXhYx2Y76p1qZ1lxio1eBzJNeYfFok0Wr9ejjnuzS6FJ5RP4cdmo4JVLEEujWneIWw==", "signatures": [{"sig": "MEYCIQDx9rf07QtHZWcPGr14fV1avNnXFkPyHRn3+/VUsiCwNAIhAM3kFZdiOobPo1CW3aHjN9kFxc7+mGz6Zl2QwkOQ2R8W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70837}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.9_1717423455392_0.021484763594541878", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "2eab12f1788fcbd743deeec6224f4bea60a8ae62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-PaoppMOyCeQEy/kqrHNSDwz/7q4E2cEE1rGurBQpkxt8v1aXf9P2G4umrBl/v/nWRSu49bmt5lfEv0cTTQIvyg==", "signatures": [{"sig": "MEQCIH3+lBe4FNapMGNFUEE4fRbaf+jQwij89DD8ZtVXSSYoAiAOnF+hr6atNWNy9ImLQZTZ/6y+Gt/Z4pK5K1AHmMUgWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70844}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.10_1717500004134_0.3983823124061756", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "00eabd883d0dd6a60c1c557548785919b6e717b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-uLEndKqP5BfBbC/5jTwPxLh9kqPWWgzN/f8w6UwAIirAEqiIVJWWY312X72Eub09g5KF9+Zn7+hT7sDxmhRuKA==", "signatures": [{"sig": "MEUCIQCZpilBXN5qTITfKMR2aRxfRF1tXIaoPp86Ol//P1mrGgIgUgxWHyfrpkBw7aEAc5egxiqgnHboAbVMTX/TDaBEMJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70838}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.24.7_1717593320905_0.5216772647337187", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "fe1877cbea82e6723dd1864ee4c12b38f8f0008b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-32LHkO3/UUOq030xVXiLzeJeXEosXoUvAW+blBJfjsA5X4mXxg+SxtvlHQfI7mW90J4yAvsO+6oEi4Rec1cyug==", "signatures": [{"sig": "MEUCIQCI8F5ZvNL77ZIKcUatni89t/wuZjOF/wnRabI8NYLVqQIgQcbIS/J+XjP4oAvFyH1KY/yAwW6rAbYX8YDT0xq1ty4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70733}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.11_1717751730979_0.47680503026350674", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "eeee076fae092e5f3a3bd08f3400b8f4d9652453", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-k3yQB0g5tCGAhtM1s7gNt7OwxSNVk7ZmClN/CEI0EXSDj1SffHn/R9/QnmWEIhymeGrpy1O8JsyBwmOeZm1tDQ==", "signatures": [{"sig": "MEUCIQC99osbTMLhIrxgYX4ax8tWV6d4v5QjoZNlN/pOztFpvAIgVZLK65V9Lrg+HCz96jPwLnqcFZv067tAbZHsHy+na7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67529}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.12_1722015207750_0.09341904932104383", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "400e2d891f9288f5231694234696aa67164e4913", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-m9obYBA39mDPN7lJzD5WkGGb0GO54PPLXsbcnj1Hyeu8mSRz7Gb4b1A6zxNX32ZuUySDK4G6it8SDFWD1nCnqg==", "signatures": [{"sig": "MEYCIQCCKmyD9doAHPMJrB1wfdWaqLBGtYKD+m7OlWWt5o91GAIhAMoByRg9MWJCjTL1AVQhZgCD5X1CBzfpAS4QkWaUFhxp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75376}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.25.7_1727882087813_0.6777915994373405", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "2649b86a3bb202c6894ec81a6ddf41b94d8f3103", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-EbQYweoMAHOn7iJ9GgZo14ghhb9tTjgOc88xFgYngifx7Z9u580cENCV159M4xDh3q/irbhSjZVpuhpC2gKBbg==", "signatures": [{"sig": "MEUCIFIC/FrE5TsARsZIP0k6LVJwf95IusAX0HGvFenM0r8HAiEAyBv9GVXhJob1R1oDuwb4cwhDRLSsu6wEymL9TYR5bdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75689}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.25.8_1728566711568_0.6150271473894746", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "10e70d96d52bb1f10c5caaac59ac545ea2ba7ff3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-qM/6m6hQZzDcZF3onzIhZeDHDO43bkNNlOX0i8n3lR6zLbu0GN2d8qfM/IERJZYauhAHSLHy39NF0Ctdvcid7g==", "signatures": [{"sig": "MEUCIElMwsCdhdIp444lmW9s1CpYSortjyZege4LmBcU9i/PAiEA8t8223LeW99KtIfpzyKRi10Kg9NTu/3jpRbfVfv/7z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4719}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.25.9_1729610466453_0.4947179208118937", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "5c28fa8c7c925903c7cddd6ef07d196d5a35abe9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-9OFJxBPqX6wy6hfgg6OXrQSilHmrw6IU028o2jPQTJ/vgi9QyE0UF2+IriT8SxdZWeypDwKQjxDl636STWigSA==", "signatures": [{"sig": "MEQCIBhb1Yk5sT2f0xPxENro0tGc2pFmRhkP99YW5qBpg/zZAiBuPKkPwjntcP0iIE1nHz+7dPWHlxmwU93fYdnC94Wr0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.13_1729864449225_0.36117938089638857", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "bf5bc5a658ccaa1a615f16e743f67482689b8ee0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-SQFSzqvF6otwRke2ajJCGiGtpqude/d+Rz937mXisemA2IQmCJPKavckQSeYFbXGDmPW4uZMy3l7nmYPhB2ppA==", "signatures": [{"sig": "MEUCIHb01WaPir8OjnvIHU3za/5D9ybHJW2tsvbrz+3xUwDpAiEAu7Jtq5CrbhAFyrovTVAutlgnUUorsnbAin1xu60cTwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.14_1733504040339_0.4282694545494261", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "43ad502f61e402aa9370d94dec654d7a54eff171", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-<PERSON><PERSON>26cQwtCrXFeLO0Y9C5z3XchTmY4eBBL/gAwl2l92wC2rXHD0zq1bSc5eAPV4cacGcQ7QHrIq/x+wOWAjdbDg==", "signatures": [{"sig": "MEUCIQCDzqTIz7kM/baW1wFSxQjR9icwu/BlGYPkppJ5yYaVCwIgOlhNNRuISDUmmEovfz5Pl46eDU+K+fi7/zBUC7UDYEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.15_1736529866062_0.2247057629335627", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "2692b80979390eb964320be4594f304ec35b14ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Ri6jHOp9ouOsAiRnA5D7MB/rK6+pBrF6tmNgr/HfWU4Rvhefe6QFmA8tRVmx9L4ahsrXxqJmB3CKqkC9YZ1BEg==", "signatures": [{"sig": "MEQCIHaPBgCpXDAg+YLul/hT9EK8tf1DDgbOqBFYyTYMwjwrAiAFbD6SZd/EeRz1f9azPH5yoGlkg7+cDMaIVP+M+dBkbA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.16_1739534341689_0.4748221530823564", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "1a93e3ae83024e1bc41b88495b81c9a124a3fe17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-L7t046gcYYACmzCrH/7GxgHgdy5FeThDo5/J6z1p4GKQzO1Es+v2FzLsu1jgI+zoTydpR4qTKZt0PAmlSscq2w==", "signatures": [{"sig": "MEUCIDvcFmev9G5/EZ5JDrqFhhYRjZU7VyVTR4V674ObGqFWAiEA9Ou5i8R0deZcDWRMLqTCwjuHAMGvRQiYm0dpLeYJN3M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4857}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-alpha.17_1741717493926_0.08743183492063511", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "84c7341ebde35ccd36b137e9e45866825072a30c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==", "signatures": [{"sig": "MEYCIQDStwvuS9ACMohUujPU4mXYCG+QoEo0frjVN6sP9moTpQIhAKLwYhHaebueopevb1SWNlxCJPp7yy99U6mCQqNdnN7L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4719}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_7.27.1_1746025732022_0.31215698074140863", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "dist": {"shasum": "c0d9aafbdce84698fea39fbb4e81c96fac162252", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-dtbuRzjpaB69oDXud+t1OP5rF+Y0uLouffXclmoCKtEY7NIKveDfZi27L+PxBLZGKKl4IA8GQbWi0DwWOXx8GA==", "signatures": [{"sig": "MEUCIEOjP6wPd0fGQ83l4ZYHp9te1Dur9I7CURTfyemaAKyQAiEAprkNxA2gi6mDB+8qKSDd2wO31hikrpZPUl+KLysH/Bc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4833}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-beta.0_1748620264295_0.5062468138384058", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-optional-catch-binding", "version": "8.0.0-beta.1", "description": "Compile optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-optional-catch-binding@8.0.0-beta.1", "dist": {"shasum": "1fffe16343b634a2ccfa84f4190233cb1aea4e56", "integrity": "sha512-oedmErfmbsAY8UuCYxWCfOWojGIovZNtpm1ELYOG3ToeUFSX9Jz1HlLKnLhDb8Z/VDOuqQTkw6q0eRfNAhm6aw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4833, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFvZMqZ4VgDaLKpd3lhsrzHR1yDNqYKAMu1vOqu98D9+AiAlOgLc+B3TOZvYXbPa4xLyXdE+q2RsUNx0jOnLZBxxkw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-optional-catch-binding_8.0.0-beta.1_1751447056225_0.4892902519743252"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:17.023Z", "modified": "2025-07-02T09:04:16.602Z", "7.22.0": "2023-05-26T13:45:17.335Z", "7.22.3": "2023-05-27T10:10:58.944Z", "7.22.5": "2023-06-08T18:21:19.287Z", "8.0.0-alpha.0": "2023-07-20T13:59:54.104Z", "8.0.0-alpha.1": "2023-07-24T17:51:55.131Z", "8.0.0-alpha.2": "2023-08-09T15:14:54.019Z", "7.22.11": "2023-08-24T13:08:40.487Z", "8.0.0-alpha.3": "2023-09-26T14:56:51.182Z", "8.0.0-alpha.4": "2023-10-12T02:06:16.845Z", "7.23.3": "2023-11-09T07:03:54.582Z", "7.23.4": "2023-11-20T14:22:08.542Z", "8.0.0-alpha.5": "2023-12-11T15:18:47.125Z", "8.0.0-alpha.6": "2024-01-26T16:14:04.606Z", "8.0.0-alpha.7": "2024-02-28T14:04:50.846Z", "7.24.1": "2024-03-19T09:48:54.607Z", "8.0.0-alpha.8": "2024-04-04T13:19:49.492Z", "7.24.6": "2024-05-24T12:24:30.967Z", "8.0.0-alpha.9": "2024-06-03T14:04:15.562Z", "8.0.0-alpha.10": "2024-06-04T11:20:04.270Z", "7.24.7": "2024-06-05T13:15:21.065Z", "8.0.0-alpha.11": "2024-06-07T09:15:31.242Z", "8.0.0-alpha.12": "2024-07-26T17:33:28.036Z", "7.25.7": "2024-10-02T15:14:48.015Z", "7.25.8": "2024-10-10T13:25:11.751Z", "7.25.9": "2024-10-22T15:21:06.641Z", "8.0.0-alpha.13": "2024-10-25T13:54:09.407Z", "8.0.0-alpha.14": "2024-12-06T16:54:00.489Z", "8.0.0-alpha.15": "2025-01-10T17:24:26.260Z", "8.0.0-alpha.16": "2025-02-14T11:59:01.862Z", "8.0.0-alpha.17": "2025-03-11T18:24:54.097Z", "7.27.1": "2025-04-30T15:08:52.225Z", "8.0.0-beta.0": "2025-05-30T15:51:04.495Z", "8.0.0-beta.1": "2025-07-02T09:04:16.402Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-catch-binding", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-optional-catch-binding"}, "description": "Compile optional catch bindings", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}