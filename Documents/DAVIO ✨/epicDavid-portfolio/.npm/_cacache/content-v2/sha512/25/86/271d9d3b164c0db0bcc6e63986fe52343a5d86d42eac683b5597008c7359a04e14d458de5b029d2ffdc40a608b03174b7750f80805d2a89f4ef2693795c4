{"_id": "xml-name-validator", "_rev": "17-9bd5884b03278656c85971befdfe5e73", "name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "1.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "https://github.com/jsdom/xml-name-validator"}, "main": "lib/xml-name-validator.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate-grammar.js < lib/grammar.pegjs > lib/generated-parser.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "jshint lib && jscs lib"}, "devDependencies": {"jscs": "^1.8.1", "jshint": "^2.5.10", "mocha": "^2.0.1", "pegjs": "0.8.0"}, "gitHead": "f73d690f98a1ac8b5e323bffc7fabd2478db711e", "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "homepage": "https://github.com/jsdom/xml-name-validator", "_id": "xml-name-validator@1.0.0", "_shasum": "dcf82ee092322951ef8cc1ba596c9cbfd14a83f1", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.33", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "dcf82ee092322951ef8cc1ba596c9cbfd14a83f1", "tarball": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-1.0.0.tgz", "integrity": "sha512-XDmXffxxQs/+0VLW9NB2oSIbSoSINj6dQdhegY3kEM81LOoLr6NfsFE9RR59qrwsKEHaZLxa1MOSahFA0CE9Ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG5xxlCTDPXJxCjWgbJsnZFS9PQFOOc1TyZwYmnQbkFRAiAt4Ek3fxC5V83lmAJonv9QY9//Iw+QjcQuWae/hhbFWA=="}]}, "directories": {}}, "2.0.1": {"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "2.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "https://github.com/jsdom/xml-name-validator"}, "main": "lib/xml-name-validator.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate-grammar.js < lib/grammar.pegjs > lib/generated-parser.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "jshint lib && jscs lib"}, "devDependencies": {"jscs": "^1.8.1", "jshint": "^2.5.10", "mocha": "^2.0.1", "waka": "0.1.2"}, "gitHead": "48a7d8094fd6245e603121be4b3adb4475650748", "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "homepage": "https://github.com/jsdom/xml-name-validator", "_id": "xml-name-validator@2.0.1", "_shasum": "4d8b8f1eccd3419aa362061becef515e1e559635", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.3.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "4d8b8f1eccd3419aa362061becef515e1e559635", "tarball": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-2.0.1.tgz", "integrity": "sha512-jRKe/iQYMyVJpzPH+3HL97Lgu5HrCfii+qSo+TfjKHtOnvbnvdVfMYrn9Q34YV81M2e5sviJlI6Ko9y+nByzvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQNU5MCNvq9T5VxwiqcEHVCXOIauiAg/ZNQ1iNhmXJ5AIhAPrmQGm6jQJk+M+1GgYF16sIqFHXBvkBzrVXsSYiNMQI"}]}, "directories": {}}, "3.0.0": {"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "3.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/jsdom/xml-name-validator.git"}, "main": "lib/xml-name-validator.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate-grammar.js < lib/grammar.pegjs > lib/generated-parser.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "eslint ."}, "devDependencies": {"eslint": "^2.9.0", "mocha": "^2.4.5", "waka": "0.1.2"}, "gitHead": "f2bbf6ffee851b95381cd98053d777ab47419697", "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "homepage": "https://github.com/jsdom/xml-name-validator#readme", "_id": "xml-name-validator@3.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==", "shasum": "6ae73e06de4d8c6e47f9fb181f78d648ad457c6a", "tarball": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqmo5AzRd4MinM7cZhlCHp04/acMCKForhwwQ3mSFsVAiEAlkA87ZvPJlqY/HqSYQXX3/fZhZkYiKjyIEO71H4K648="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xml-name-validator-3.0.0.tgz_1508717316935_0.215595209505409"}, "directories": {}}, "4.0.0": {"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "4.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/jsdom/xml-name-validator.git"}, "main": "lib/xml-name-validator.js", "scripts": {"test": "mocha", "lint": "eslint ."}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "benchmark": "^2.1.4", "eslint": "^7.32.0", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}, "gitHead": "6b0e370056299556c01ca9300256b10f80800549", "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "homepage": "https://github.com/jsdom/xml-name-validator#readme", "_id": "xml-name-validator@4.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==", "shasum": "79a006e2e63149a8600f15430f0a4725d1524835", "tarball": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz", "fileCount": 4, "unpackedSize": 13545, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICgGMG620vrdMrmO0YET+L0UUEaKunJRNRyXnhD1/vv+AiEAuMI7Uqv8QUD0/ZQ6xj+Cn+zBrgS7Rx+71mXkTdU+hWQ="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xml-name-validator_4.0.0_1632002502156_0.8019144968342102"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "5.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/jsdom/xml-name-validator.git"}, "main": "lib/xml-name-validator.js", "scripts": {"test": "node --test", "benchmark": "node scripts/benchmark.js", "lint": "eslint ."}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "benchmark": "^2.1.4", "eslint": "^8.53.0"}, "engines": {"node": ">=18"}, "_id": "xml-name-validator@5.0.0", "gitHead": "836f307eec81279d2b1655587892e38a1effe039", "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "homepage": "https://github.com/jsdom/xml-name-validator#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==", "shasum": "82be9b957f7afdacf961e5980f1bf227c0bf7673", "tarball": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-5.0.0.tgz", "fileCount": 4, "unpackedSize": 13574, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLZ1eC1pNLglk7HSDKZaoNZhDb136x5+Mb13I6EpD4aAiAo4SGBqzx8oxERLUEU/yMRfPaz2m1MZzH289FI7RTN6w=="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xml-name-validator_5.0.0_1699767489708_0.3976051766574984"}, "_hasShrinkwrap": false}}, "readme": "# Validate XML Names and Qualified Names\n\nThis package simply tells you whether or not a string matches the [`Name`](http://www.w3.org/TR/xml/#NT-Name) or [`QName`](http://www.w3.org/TR/xml-names/#NT-QName) productions in the XML Namespaces specification. We use it for implementing the [validate](https://dom.spec.whatwg.org/#validate) algorithm in jsdom, but you can use it for whatever you want.\n\n## Usage\n\nThis package's main module exports two functions, `name()` and `qname()`. Both take a string and return a boolean indicating whether or not the string matches the relevant production.\n\n```js\n\"use strict\":\nconst xnv = require(\"xml-name-validator\");\n\n// Will return true\nxnv.name(\"x\");\nxnv.name(\":\");\nxnv.name(\"a:0\");\nxnv.name(\"a:b:c\");\n\n// Will return false\nxnv.name(\"\\\\\");\nxnv.name(\"'\");\nxnv.name(\"0\");\nxnv.name(\"a!\");\n\n// Will return true\nxnv.qname(\"x\");\nxnv.qname(\"a0\");\nxnv.qname(\"a:b\");\n\n// Will return false\nxnv.qname(\":a\");\nxnv.qname(\":b\");\nxnv.qname(\"a:b:c\");\nxnv.qname(\"a:0\");\n```\n", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "time": {"modified": "2023-11-12T05:38:10.221Z", "created": "2014-12-28T03:59:50.159Z", "1.0.0": "2014-12-28T03:59:50.159Z", "2.0.0": "2015-02-22T19:43:07.909Z", "2.0.1": "2015-02-24T18:09:59.382Z", "2.0.2": "2017-10-23T00:06:12.923Z", "3.0.0": "2017-10-23T00:08:37.874Z", "4.0.0": "2021-09-18T22:01:42.293Z", "5.0.0": "2023-11-12T05:38:09.927Z"}, "homepage": "https://github.com/jsdom/xml-name-validator#readme", "keywords": ["xml", "name", "qname"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/xml-name-validator.git"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/xml-name-validator/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md"}