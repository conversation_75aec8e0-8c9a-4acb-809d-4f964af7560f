{"_id": "babel-plugin-transform-typescript-metadata", "_rev": "8-5b2d6a625d389d4004d1383292f34e3b", "name": "babel-plugin-transform-typescript-metadata", "dist-tags": {"latest": "0.3.2"}, "versions": {"0.1.0": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.1.0", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage --reporters=default --reporters=jest-junit"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "increment": "conventional:@favoloso/emoji", "scripts": {"changelog": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji | tail -n +3", "beforeStage": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji -i CHANGELOG.md -s"}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-typescript": "^7.3.3", "@favoloso/conventional-changelog-emoji": "^0.9.0", "@types/jest": "^24.0.11", "babel-test": "^0.1.6", "conventional-changelog-cli": "^2.0.12", "husky": "^1.3.1", "jest": "^24.5.0", "jest-file-snapshot": "^0.3.6", "release-it": "^10.3.1", "typescript": "^3.3.4000"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "e709b2db581a99f503a4489602218d7905f7990f", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Jahh/sLf9KBonIxjxsvE4UCQZx4STxzSehBGwqV498VkQWElGX7ieA3EiA49fLrQ41lPEji93WV6ckEKTY+EsA==", "shasum": "2874f96da1e54b3fa3b634100414c8886073bea7", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.1.0.tgz", "fileCount": 16, "unpackedSize": 13774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl3TnCRA9TVsSAnZWagAAJhAP/0nNYcj2kqrqHkZmamqT\nxzBSMfyfsFq40NwJfxIipBZnWLSa1GwU4M7Z8N1zOzEYQEuhnirsddrg4s3R\nuoXdl1T24oXcJfFgqDMFHHoS9yqE3ou/jtuiKiZJ0lq9sPb1oNsDtWQEakoc\nUNKOdbVtH9N9i1RatZETJNwDQ1NBpc8uXwQ1aVHQ//j2an6cttp7BB7/TytL\nh7nXlG5wSPeDIy7tQ3O3g2U+qvz68MN+9Dvl0glI0bzCsvwe8UymgafTgWhS\nwTJBYSjxh1EHCqXB5mwR8cJl52RVpjOJ00HbBaozFeJOx6zkvfPrYmwXJeFD\n7HJoPsJ/GpuTx3aiZm7MHsfvdhENrdilYbKWp9B2qHoivcQxBjK0NJSflNaU\nNNP1+4tAzSgGuG6thyczj2D+4PR86F5RqSGnVvx/T0UAw+Gh1Fy48bstH7kG\nujEq6lgEf99XmVmwoe5QOLNUUm0Y0ckXzd+AoKURsO76HND/yXS4DSUearYj\nqKaYh6HY0c4CP+kb7OubNFPlrArmiKc5bFtfqUdJ41Dj9QyxyjO0rJNzM0ok\ntGpVc+0tofvBvv5sWw8Kz598Kek8rxLPp5opS/wnZjpqRZNE7MnSLTLzFPCS\nKtrY2uVYt49SdTzbfIZUyTcnT35g9PHptJDUfK1Wr82xnLKpczYxKZCeTEYy\nC6sq\r\n=L0sE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCngUxSA0JvLMOMEVMdeAThejkc/fsRxXsCcUQIXbxrQAIhAIxPpuSr+A5FX+ReVqz/Bx5VYlpVxY20Jx07szFvH9wu"}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.1.0_1553429734396_0.7002900923024715"}, "_hasShrinkwrap": false}, "0.1.1": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.1.1", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage --reporters=default --reporters=jest-junit"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "increment": "conventional:@favoloso/emoji", "scripts": {"changelog": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji | tail -n +3", "beforeStage": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji -i CHANGELOG.md -s"}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-typescript": "^7.3.3", "@favoloso/conventional-changelog-emoji": "^0.9.0", "@types/jest": "^24.0.11", "babel-test": "^0.1.6", "conventional-changelog-cli": "^2.0.12", "husky": "^1.3.1", "jest": "^24.5.0", "jest-file-snapshot": "^0.3.6", "release-it": "^10.3.1", "typescript": "^3.3.4000"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "1b1a2f1823e18c344609e4d157687308b74d34af", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4Fy9kPufiejtZN8fM+7mkvDmEn6OeRJuoOEnXWoTqYe2Ej589d7aXQ93v7IKnW4zQWQvEqIURwO7CxIuiR2XpQ==", "shasum": "0ea833f30949732db7fc7e80716d20773549d18e", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.1.1.tgz", "fileCount": 16, "unpackedSize": 14049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl3XTCRA9TVsSAnZWagAAz10QAI0igD1cKuv0UGRRmVWK\nHvSXsT6cOK1+CKX+8xAYR3AAhcFAj/a6C2Rr+oBIJnRYCzGrDwTsXBZ2LNjo\nWlmldyRQNNm9PVBAGCsx6CXaeC6IW+NryJqLgjLvwg/zdKkglGtVM3y1bymL\ncb53cPZNhoeVnsaqbQDvuoGY33qg/ttgXI0oNzeCyHmiAqrCTD17cx9LPEqL\nC0DnltG6NAW0FOs3CTBC2MSynfak5k70CCck+9GEKDCn2h7QhwMyjfxv2ZOt\n7fbJPc1+Ei6XB0YiijrtTVDrNh+SWEo4CcCleVFLz/qnWAF++9hXLnxwBBWi\nNdoI7pCqz5uOz4KzHCWZCnGJD/aq0gyy9G5fFWPkte75MmH3Jw+kHfDpaCkp\ng5tyVsTHtWLMYTNcAZVnQRRjRyCrD7WOP7/Gra5QlUf8THFnaeVj7GOF5xvY\nyq7V+JAGfiS5ocytsQszPuFva7H0otJMp6FiqUf4gYID4/0RE+eXo6F8gk3c\n6UyQabiiycOyiHvYLh9sDDMwQaS8fpUmNJ5K0X+tZalf03F/gaGf0tCQc879\nFJPnDPGiQjWaY5U/XWAbl1rrm5BP3LJ6yvx0yb5EtQKvjICQjNkPhRBGS0/c\n+eNjuHhaJUAtYyHU0NNdgu3Lwp+fu1oHUMCdQourXkvilIHbw43Uxo/8caPg\njRrq\r\n=orCJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnQ8xr348NxJpqYccFO7MkSoLjK03PpZdRBMH+YmHqAAIhALwo62uXDGM6CLRHxK2kpN60cA5EREDHWF1UoeJvzbKl"}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.1.1_1553429970786_0.8055027620914692"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.2.0", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "increment": "conventional:@favoloso/emoji", "scripts": {"changelog": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji | tail -n +3", "beforeStage": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji -i CHANGELOG.md -s"}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-typescript": "^7.3.3", "@favoloso/conventional-changelog-emoji": "^0.9.0", "@types/jest": "^24.0.11", "babel-test": "^0.1.6", "conventional-changelog-cli": "^2.0.12", "husky": "^1.3.1", "jest": "^24.5.0", "jest-file-snapshot": "^0.3.6", "release-it": "^10.3.1", "typescript": "^3.3.4000"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "7dfb7665ca6d159778c6e410fade32ba26c5f81c", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KYMd7wxPuZK6gScYVfjyKzptEmL0d5BpyelA3D3bc0dI2ljw6lvSb1p/FbpjKpXpUTQR9jY5G9F2EvI3R5/XPg==", "shasum": "eed24bc0fb89c14f6dc57794b70fd1ff2471b6ec", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.2.0.tgz", "fileCount": 16, "unpackedSize": 21070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl480CRA9TVsSAnZWagAAs8gP/A5SdOOSW3WHm9js08ZA\npgiyOStU/ZyZkwCE4hfDmvTpPTFNs3cLCxQkuX5eX4EvVMWOTfRqMfc8RbKM\n6hg7QdtWJKXcw9nzoIuW7C1T9y3kaT/MVl4mvItr9ZZOp1xhdwnHbAewdq0e\n2p8hD+l6fsYz9D4Oc3ng/XW/YKud60KKhBn3AUoQUzkBl6VzZzMsfCY2jP2B\nYjrCgsKIkxS2cXN2yM+cUDhAa+w3fLes3KN1pD0EEOW1C/MB6u+U7Z6ya5Bf\nQngli+dgjOD188477LBdD9ghNFOtja07IkhYehSaQuXrvp7MkMnOrLskwMog\nS0GWT7us1tWp5RYVBJ2MNmrf7ZW2jpIcTKoDymdHi6Wv3LgiHumXAnC1Hupf\nHlIzqyWO4d+D9PqfauU0kwTPwGhbgloQztpA0X+2ZUiCHDaq8oO5ct2zbMgn\nz/i6DqLY4FIaOp5F2Hx84l7wbx6Q505uzXmQcEB7j3Ebd9tj3WvYsraek8F7\n6M17JFdQvT+Uor3IHekwd0ZYClb4HYKqdh97hNfiNjz25wfUYjoVWGQ4ITxT\nuLOHBnQrve7mPWCvQR6F0HJknPtrG205VfJnzmTBoORPGZqbIG4315Jlk186\nwk/n9vuI0EvK/D6W96FB0o2TQM7eHl5pplR2AmsvDVNbEh3Xl/6xf/U4qRp2\nkH1S\r\n=KufQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2+7s0QDa1hA+zRWxX1jkRDH3w1MgrLJCcF63+t/ywLAiEAmP1zV3rKqVYllWchik2VKob92wH12YW48k1ogHDCT68="}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.2.0_1553436467720_0.16040153973812554"}, "_hasShrinkwrap": false}, "0.2.1": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.2.1", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"], "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "increment": "conventional:@favoloso/emoji", "scripts": {"changelog": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji | tail -n +3", "beforeStage": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji -i CHANGELOG.md -s"}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-typescript": "^7.3.3", "@favoloso/conventional-changelog-emoji": "^0.9.0", "@types/jest": "^24.0.11", "babel-test": "^0.1.6", "conventional-changelog-cli": "^2.0.12", "husky": "^1.3.1", "jest": "^24.5.0", "jest-file-snapshot": "^0.3.6", "release-it": "^10.3.1", "typescript": "^3.3.4000"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "44d7b581c547437edd233dc7e82579065eb47320", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4xROk0UQwG6gorCTSE0nymcGaLkSkG/pMhDvptuozqIKxg/bnGyMOAglTPOO2EQ3E1fW+Q0/wi4WAstqXw8wTQ==", "shasum": "64cab4eb1cb7ffd7afa8fdd08bd8a02d80ae05c6", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.2.1.tgz", "fileCount": 16, "unpackedSize": 22262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl5JWCRA9TVsSAnZWagAAgRUQAJGPczoKz4tg68RU2uo/\n1UtXqlWkX7/UUNMKZsrwq7OTXfDTofPOS/R1AclFJb7uB0WkRUv6z4A65KWY\n04TswMQLeoTOEZ5S8lOlECoGEwSOewxf/NbqISCKAwBHmIF0mUvBhGervGfJ\njkCXHGclNqMv+EZ0/rfGokRHCo8DG6NnUhJ902KjXa4qwLtAoNTnfpAocKj4\nIW1dmTIqW4Sw+V8dhXI1hxfShsx8bwFQaGYN+iMtutHUfL8+hQRJIifRKSJs\nkMXsWwHYL7rrJG8CF9uY09U0KeKXO1M8R5RVw/CAIJXpX3quTLhCbRSp6uod\nd1e1x3kEuZkFSX5FHrOGb22VQv9g5HTBOrQ4t8S8/90hvOv0gEqqCJRXywuS\nzY8f90kQ58PDk7TFpWwltA5ewXMO+lbgkS7eYgJbxX1pv0Bex0lx+n6p0wlS\nDdSM2eShZiDGK1tqV3EicCRImBZ0tHH6USH/D+j41MIg4YRrFpUw916G4+vz\n/jvnRzwGuKf2xppPdUQuQUE+MXFqRutsMcAdrRI7LBcDvPeXooXCEjctMVo7\nAUUUMOmRxANo9TspPSUYZm/T0uyDTAOCMiQltNSBGBwtmvKzlooGZMpKRhAd\nTph8k3wJolu/VMglI9ENa43MkLJg2afMdtgCTEnDBW3zF/L2QvCml2Zct1fY\nQTcz\r\n=ldGl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpmXnOue+yoReM3p3oX9zLTo/J4rGfRTPcr/MjT8D7AQIgcFwowShVDQ/9qcqUoZ8bN9/ZYyJPjT07pY4wu4bDa0s="}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.2.1_1553437269639_0.37591602400749125"}, "_hasShrinkwrap": false}, "0.2.2": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.2.2", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"], "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "increment": "conventional:@favoloso/emoji", "scripts": {"changelog": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji | tail -n +3", "beforeStage": "./node_modules/.bin/conventional-changelog -p @favoloso/emoji -i CHANGELOG.md -s"}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-typescript": "^7.3.3", "@babel/template": "^7.4.0", "@favoloso/conventional-changelog-emoji": "^0.9.0", "@types/jest": "^24.0.11", "babel-test": "^0.1.6", "conventional-changelog-cli": "^2.0.12", "husky": "^1.3.1", "jest": "^24.5.0", "jest-file-snapshot": "^0.3.6", "release-it": "^10.3.1", "typescript": "^3.3.4000"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "a90bdd5b005e17c72cdf3630623b27c27023f448", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.2.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wWRw1p3IDNZN8l3UOLgeScq9Nhh4lvxT7ZOvUzYi5sMw8lUWW+KdRlnBeQKehHAsgDs5TR6PvMJmsQ6e6NmKCg==", "shasum": "fc44611187409ed9d5cb372ca2f85939a359cada", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.2.2.tgz", "fileCount": 16, "unpackedSize": 26350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcm37OCRA9TVsSAnZWagAAYtEQAIwvAug9J7LAyxGB/LJL\nkRSKD/U4RCrWgjjdRlA7OlG/l+QTIGUp3V2GZsqFl92qw+PgCxBRd5p/h9nH\nE5ICQ0Sh4YebphJDuhHy0ZmKylBPFBdv2gpp/pLuE45LaMXM+1amtWA/UnOT\ni+bEVbl51IGGvB/HbEJkdzQt3To4pR+orNe095Le6C/VpILe6UAbgw8rr4uM\nXAXyN1EcJY8P9bNdx3LPp6ot5ecFHcX9v3cEWjWlkGcHJlVqZK1z2rExYWjH\nM/po4oebxI/DjyFIgUKajdC9aDVK1nhkhOKsYrOE5/3TWLtn/xMmlmY/pTIH\nRTT0E0tvJ5uPg6/roVUTNpAiHPIgB07q1kuZX9WCIWF5L4zhvK7mHdFwzhu4\nlf0CDGblnx3MUzwHbcGmZEaGV9ZTSMtRAVeoYaeNrv9PEpg+zMNyHOGbUuD4\nHEQ+lKxkduHdZnFWoXQHJp7G8XOaUJFPuh0k/5uvj/K34MA+4o470Hz9SPjB\n315rVa5iuHHrP7fEr51e+Mlk6aIHPz20nkE3jJz4278EeH3keikErao+jGGG\nloOkUJv8IEPKH698ebheRUU8oyqGXa+6MNuzHrUrZQ0LbZ6lfWjUFoqREk7l\nW2b238eSaYarPlEUgtboxHVAok5ixuxsgPVrt1MdxxT02jzZteQWuP37UvdD\nVjjh\r\n=MyFi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMpNkXzKA4cYFXeC9pAkdY3gDzTP+C2ZsKJgSGfLUUWQIhAJbZ5hrJ1XM+EhXMxC8t6zJ7MclGgm8j7Pfiof5WMoBR"}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.2.2_1553694414151_0.7959259769832798"}, "_hasShrinkwrap": false}, "0.3.0": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.3.0", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"], "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "@favoloso/emoji", "infile": "CHANGELOG.md"}}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.6.0", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@babel/template": "^7.6.0", "@babel/types": "^7.6.3", "@favoloso/conventional-changelog-emoji": "^0.10.0", "@release-it/conventional-changelog": "^1.1.0", "@types/jest": "^24.0.19", "babel-test": "^0.2.3", "conventional-changelog-cli": "^2.0.25", "husky": "^4.2.3", "jest": "^24.9.0", "jest-file-snapshot": "^0.3.7", "release-it": "^12.6.3", "typescript": "^3.6.4"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "3702dda5d660ce49d8d307dd0fec6dcff65c459f", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.3.0", "_nodeVersion": "10.14.1", "_npmVersion": "6.11.3", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ASYrM+bxtpfgZKsAOqQfjmLlekIDigRnNCfQBDOOdaqL18hLhZIsbdiHsuaNDTkljlqnbV/DlufaWY55jC2PBg==", "shasum": "70093ea8611baf985293fb3ec704d1b7db737ad9", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.3.0.tgz", "fileCount": 16, "unpackedSize": 28599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYRfwCRA9TVsSAnZWagAAyFEQAJCoXUlYdC/VIE21WerY\nzq/yV6kpdEnrUdr2HXjd1KhwKrtELACMI/w1BHoMd18S7I9X0H0YRpSlb0Aa\nV9oApYuns6ciMpfPZwsyMkrFiVUn4Aa0EBoyXVIKI3AEk2zY5/COMStNmaEM\ne01m6mJ4rm0ydyOxK0sM/80/tmS/58DfScPLXva9ReUkRjnPSUKq8iEgYRhx\nUC51ZCoHwZNOK7TOndSttj5vvwP3lHqjOwsHjxKNdmT3nHh2vyBLx7OJUsJf\npmihoPtmoVj9BFNJ4ZfUq0109nqWhacPBA42SkIaP9xGMtWIg1ybYtDSANwb\nU3tt7ES37zKv2aVzO1mjStC1DxBLKm1S9bJNQn43z/3SOT7Z8WFvNWUGqYwU\n3x/iXJshzLpw5B6EjhABm4tE2A8LMCT1vex1Di9GpQh1fqsLVU/reBSJ0f35\nroDeT8ZepLet4hlIJt+Ea/ThOzJi8AIu4sHR10EONBwGoyM3/IAMZQTaVRYw\n6xCJLNHcbCJsPDYKH/smuGYYicFFlUg5oT+UFANwRm35ddtREGVTX8f3qOqX\nDgZ8JZNDA7/WMJLiyXdVK96MIEz2/w5FNAywS0fCMASb9abvCklSUnH4dOgl\nnnfyz4onBUu/qX3lAm/OzqsX1T/uJ6DAzvyo8G3W/Zno3yUd9Jf6K/QPy0BI\nCq64\r\n=6/mW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxe89xfeLUQgcWkVBnwQrYssEZwrLgonhCnFukWGoAJwIgMsSZmj7pRVVY5RNZl+X1Qdya6GunweBexwZvcWWVhQk="}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.3.0_1583421424282_0.7003671550512818"}, "_hasShrinkwrap": false}, "0.3.1": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.3.1", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"], "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "@favoloso/emoji", "infile": "CHANGELOG.md"}}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.6.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@babel/template": "^7.6.0", "@babel/types": "^7.6.3", "@favoloso/conventional-changelog-emoji": "^0.10.0", "@release-it/conventional-changelog": "^1.1.0", "@types/jest": "^24.0.19", "babel-test": "^0.2.3", "conventional-changelog-cli": "^2.0.25", "husky": "^4.2.3", "jest": "^24.9.0", "jest-file-snapshot": "^0.3.7", "release-it": "13.7.1", "typescript": "^3.6.4"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "775caf3244a610efa3d8bfb5408a366b7a1fe321", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.3.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-thOuACZReULfLy7vh2o3/joYkkRerMKLBDmXy3ImCnkNUnxBmNw0uVa05JhhX0slluaEkio6OIFa7zPgaJdk6g==", "shasum": "d86599b7139131ba5e917f5f568d0c824a5cdfc3", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.3.1.tgz", "fileCount": 16, "unpackedSize": 28956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhJ3/CRA9TVsSAnZWagAAjVkP/0H98qYufUln5tAbCCNA\nT0F8OmNu8M1YOTOu+xe4+RpPbu8jfk7n1e5fRpgEA/8wCFhl0UMuG1J0rZt0\nI8TEPGXhi73zAh343uzBgK3178106NFk6NX3vaebN5CaNnuOGp3u1qlbOk/S\ncFR6XITFAc3dE+V89zifAnFCODiKHWcahZQrpDArAEJGeiFF+/0/y0/yZw97\nnbpDqpoZaUYfTtz3oOKIIp9XexR3sFnc9yB71Micps8m0DrUXq8JUjgNQ8V0\nF9yNfQb6oBtmqnUHCEVsYGlAX0PlcHOpVT/MbSdbqSm0o3g5DmASKH94CEcp\nwjGeYhHKdYB6ASLelPOH43Exhhj1yNAoRy+oeP9XLBamIKqejmUZk2vNhBjD\naPERvKk+e8XjXr+F7X+diQuj7h2kyXKnjpeWeZcx2UN48HSQk88o08x3k/ku\nWsFzVdzcuKQN+lOaCTO645hJEQTB7Id5xX8LPRwZT6js/VTZQUJQ8QcHWZiA\nIKY5KMOomt+q7dmeUzD2SWTM3tA+qHTTMJV0g/Dx357F5pZ1xmU8ODdTrbWe\nxa6OyyZni8xmyzGTEr22F7yKS824aXlYBxARBzIKpiKQL/HmO6fxdIos4bWu\nK8He2+kvTTUqu7D8yrQmYfmRPfIocTfu9H1Tu9cPNOZpCVrJuEqnfHMRu4tN\nPeLe\r\n=z+6r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvijlt0eOBLTsYQ6+TQZxRqmwDF55lrTY3XS1bnmHLYAiEArzRiXaU5wC5qk6zwlXU2hk93ThcYgs5oPJb6jly8SrA="}]}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.3.1_1602526718751_0.5311759614808589"}, "_hasShrinkwrap": false}, "0.3.2": {"name": "babel-plugin-transform-typescript-metadata", "version": "0.3.2", "description": "Babel plugin to emit decorator metadata like typescript compiler", "main": "lib/plugin.js", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"], "scripts": {"dev": "babel src -w -d lib -x '.ts,.tsx'", "build": "yarn build:lib && yarn build:types", "prebuild:lib": "rm -rf lib", "build:lib": "babel src -d lib -x '.ts,.tsx'", "build:types": "tsc --emitDeclarationOnly", "check-types": "tsc --noEmit", "release": "yarn test && yarn build && release-it", "test": "jest", "test:dev": "jest --watch", "test:ci": "jest --ci --runInBand --coverage"}, "husky": {"hooks": {"commit-msg": "emoji-commit-lint"}}, "release-it": {"git": {"tagName": "v${version}", "commitMessage": "🔖 v${version}"}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "@favoloso/emoji", "infile": "CHANGELOG.md"}}}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.6.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@babel/template": "^7.6.0", "@babel/types": "^7.6.3", "@favoloso/conventional-changelog-emoji": "^0.10.0", "@release-it/conventional-changelog": "^1.1.0", "@types/jest": "^24.0.19", "babel-test": "^0.2.3", "conventional-changelog-cli": "^2.0.25", "husky": "^4.2.3", "jest": "^24.9.0", "jest-file-snapshot": "^0.3.7", "release-it": "13.7.1", "typescript": "^3.6.4"}, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "gitHead": "ce4710a3b231a6315a8c37dd69cc1e4ac0998512", "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "_id": "babel-plugin-transform-typescript-metadata@0.3.2", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "leonardfactory", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mWEvCQTgXQf48yDqgN7CH50waTyYBeP2Lpqx4nNWab9sxEpdXVeKgfj1qYI2/TgUPQtNFZ85i3PemRtnXVYYJg==", "shasum": "7a327842d8c36ffe07ee1b5276434e56c297c9b7", "tarball": "https://registry.npmjs.org/babel-plugin-transform-typescript-metadata/-/babel-plugin-transform-typescript-metadata-0.3.2.tgz", "fileCount": 16, "unpackedSize": 30165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVJ9bCRA9TVsSAnZWagAAQfEP/igLbByeN8Op/87gRPUJ\nAFsftavFpPksMMzJt5+VkTQm4HjFRGPIlDjSA3SLiiEE3KeGMzORjCzAUEHh\nruHMBch8EYXYquAnhgJgrrtsx1BlSRsdDa3Qn+MakMJqXROk2ortCg9R9/Yl\nRon49DlT0C7eISUdMiZl2f9mEDouqHAdovmfR0i804dyvnmuaDX7dEy/usyY\nsGUC5rtw/XIXOjrP9Eum4d6jAaiJOTHhc195q2GLv3nScR7FeUg33W9a4Xc/\nh1Sgjk6IegnFFocMv8X0Vo9PIF0WrfE6Prl573icRJ/c73xR6YF71gPSYNXi\nOn14lk0JOl6nGMXiCx/X8sqL72A6zwlVh61JtLLDEugsaxeIx7peepDwld/G\nZTPmLfjgi7n0UNva6pzibMUwFNHElXVzCtjCwINdxAQBoi7+2tGERgvbQI2L\n1+p7FFfZf/uZm4KOPopf3C949Lte5y3CLET2lxDv2j855CRq8FwfApptlfQl\nILPJUU5IcgdYW51XIUxzIgpw1WqcBjkwuDW4TZdr9deSQJNHH1buhbHfpHc+\n/HwHj73h8wK4Bo3I0/6XrcOz4AD1faUX15n5ABqbTQXv7FOZAyWtEG6I+T9y\nPNHGmZOQJSnpXR8Bg5trfBPzUmBp9/SCCNOd1vxikaGCuNLGq+cLIxL9cWk8\n+G6K\r\n=8V1Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGbp5/zbv4myZ1LFz0e0AkKhTyDs6n6hepgxAei4k5wkAiEA6FVN9sHhTND9IgPr8la3daj3Wyu1R57NgbRct06NVIo="}]}, "directories": {}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-transform-typescript-metadata_0.3.2_1616158555279_0.7784530880728633"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-24T12:15:34.394Z", "0.1.0": "2019-03-24T12:15:34.514Z", "modified": "2022-04-11T16:45:54.873Z", "0.1.1": "2019-03-24T12:19:30.941Z", "0.2.0": "2019-03-24T14:07:47.864Z", "0.2.1": "2019-03-24T14:21:09.875Z", "0.2.2": "2019-03-27T13:46:54.245Z", "0.3.0": "2020-03-05T15:17:04.426Z", "0.3.1": "2020-10-12T18:18:38.901Z", "0.3.2": "2021-03-19T12:55:55.451Z"}, "maintainers": [{"name": "leonardfactory", "email": "<EMAIL>"}], "description": "Babel plugin to emit decorator metadata like typescript compiler", "homepage": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata#readme", "repository": {"type": "git", "url": "git+https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues"}, "license": "MIT", "readme": "# babel-plugin-transform-typescript-metadata\n\n[![<PERSON> (.com)](https://img.shields.io/travis/com/leonardfactory/babel-plugin-transform-typescript-metadata.svg)](https://travis-ci.com/leonardfactory/babel-plugin-transform-typescript-metadata)\n[![Codecov](https://img.shields.io/codecov/c/github/leonardfactory/babel-plugin-transform-typescript-metadata.svg)](https://codecov.io/gh/leonardfactory/babel-plugin-transform-typescript-metadata)\n[![npm](https://img.shields.io/npm/v/babel-plugin-transform-typescript-metadata.svg?style=popout)](https://www.npmjs.com/package/babel-plugin-transform-typescript-metadata)\n\nBabel plugin to emit decorator metadata like typescript compiler\n\n## Motivation\n\nTypeScript _Decorators_ allows advanced reflection patterns when combined\nwith [`Reflect.metadata`](https://rbuckton.github.io/reflect-metadata/) output.\n\nCurrent `@babel/preset-typescript` implementation however just strips all types and\n_does not_ emit the relative Metadata in the output code.\n\nSince this kind of information is used extensively in libraries like\n[Nest](https://docs.nestjs.com/providers) and [TypeORM](https://typeorm.io/#/)\nto implement advanced features like **Dependency Injection**, I've thought it would\nbe awesome to be able to provide the same functionality that [TypeScript\ncompiler `experimentalDecorators` and `emitDecoratorMetadata`\nflags](https://www.typescriptlang.org/docs/handbook/decorators.html) provide.\n\nThis means that code like:\n\n```ts\nimport { Injectable, Inject } from 'some-di-library'; // Just an example\nimport { MyService } from './MyService';\nimport { Configuration } from './Configuration';\n\n@Injectable()\nclass AnotherService {\n  @Inject()\n  config: Configuration;\n\n  constructor(private service: MyService) {}\n}\n```\n\nwill be interpreted like:\n\n```ts\nimport { MyService } from './MyService';\nimport { Configuration } from './Configuration';\n\n@Injectable()\******************('design:paramtypes', [MyService])\nclass AnotherService {\n  @Inject()\n  @Reflect.metadata('design:type', Configuration)\n  config: Configuration;\n\n  constructor(private service: MyService) {}\n}\n```\n\n### Parameter decorators\n\nSince decorators in typescript supports also _Parameters_, this plugin\nalso provides support for them, enabling the following syntax:\n\n```ts\n@Injectable()\nclass Some {\n  constructor(@Inject() private: SomeService);\n}\n```\n\nThis will be roughly translated to:\n\n```js\n// ...\nInject()(Some.prototype, undefined, 0);\n```\n\n## Installation\n\nWith npm:\n\n```sh\nnpm install --dev --save babel-plugin-transform-typescript-metadata\n```\n\nor with Yarn:\n\n```sh\nyarn add --dev babel-plugin-transform-typescript-metadata\n```\n\n## Usage\n\nWith `.babelrc`:\n\n> **Note:** should be placed **before** `@babel/plugin-proposal-decorators`.\n\n```js\n{\n  \"plugins\": [\n    \"babel-plugin-transform-typescript-metadata\",\n    [\"@babel/plugin-proposal-decorators\", { \"legacy\": true }],\n    [\"@babel/plugin-proposal-class-properties\", { \"loose\": true }],\n  ],\n  \"presets\": [\n    \"@babel/preset-typescript\"\n  ]\n}\n```\n\n### Usage with [InversifyJS](http://inversify.io)\n\nIf you are using normal dependency injection letting Inversify **create your instances**, you should be fine with all kind of decorators.\n\nInstead, if you are using **property injection**, when [the container does not\ncreate the instances](https://github.com/inversify/InversifyJS/blob/master/wiki/property_injection.md#when-we-cannot-use-inversifyjs-to-create-an-instance-of-a-class),\nyou would likely encounter errors since babel\ndecorators are not exactly the same as TypeScript.\n\nYou can fix it by _enhancing property decorators_ with the following function:\n\n```ts\nimport getDecorators from 'inversify-inject-decorators';\n// setup the container...\nlet { lazyInject: originalLazyInject } = getDecorators(container);\n\n// Additional function to make properties decorators compatible with babel.\nfunction fixPropertyDecorator<T extends Function>(decorator: T): T {\n  return ((...args: any[]) => (\n    target: any,\n    propertyName: any,\n    ...decoratorArgs: any[]\n  ) => {\n    decorator(...args)(target, propertyName, ...decoratorArgs);\n    return Object.getOwnPropertyDescriptor(target, propertyName);\n  }) as any;\n}\n\nexport const lazyInject = fixPropertyDecorator(originalLazyInject);\n```\n\n## Current Pitfalls\n\n- If you are using webpack and it complains about missing exports due to types\n  not being removed, you can switch from `import { MyType } from ...` to \n  `import type { MyType } from ...`. See [#46](https://github.com/leonardfactory/babel-plugin-transform-typescript-metadata/issues/46) for details and \n  examples.\n\n- We cannot know if type annotations are just types (i.e. `IMyInterface`) or\n  concrete values (like classes, etc.). In order to resolve this, we emit the\n  following: `typeof Type === 'undefined' ? Object : Type`. The code has the\n  advantage of not throwing. If you know a better way to do this, let me know!\n", "readmeFilename": "README.md", "keywords": ["babel", "babel-plugin", "babel-typescript", "decorators", "reflect-metadata"]}