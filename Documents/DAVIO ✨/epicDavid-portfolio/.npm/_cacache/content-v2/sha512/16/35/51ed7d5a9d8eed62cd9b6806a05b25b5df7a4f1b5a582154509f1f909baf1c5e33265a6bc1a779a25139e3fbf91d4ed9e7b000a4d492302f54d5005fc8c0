{"_id": "@vitest/utils", "_rev": "140-ae1562f344326bcd81938b1edd606e29", "name": "@vitest/utils", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"0.27.0": {"name": "@vitest/utils", "version": "0.27.0", "license": "MIT", "_id": "@vitest/utils@0.27.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2496625456d1f55f61cd42b0f4fbf00a28f31e87", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.27.0.tgz", "fileCount": 10, "integrity": "sha512-2i6LRUzgIFGGn5KuiJemHGkpqtPRnulnVz/Qs9kfwijURcI8da+XiYvhwJ4kqsfg9OIbeCcqG6jC/WOdj81efA==", "signatures": [{"sig": "MEUCIAHo9gD55mDu89FI6MUH/eFDoKEZrpzNkDxfoaoWG9pyAiEAxgeZm39pXepsyFL7Acjw0FZdGREeE8ehZ0w89CK95rY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvAmRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC/BAAk4gUMrrXQDNYQDVwvtvdPDaR+IQe/07jCy04Fa3r4KMZciJ3\r\ns2xyCRh7Tvawx5yu2qSyFqclwXs6G+sSKc/bPMtFLW1v3OrhD4HIwnGVB0nx\r\nYBQki0UsRvy5/IorOU7LBnqwqILcVyh44UziafFucpRzL+xrHr6mkAjr3ceJ\r\n4TiwCBpYGeXWwqx2r8J/IthLzkMUm9h/IfQFHXziVkOlXkQ5y1OTAr2yVf9N\r\niM0g8IfS10hhMLytgg0N35SXO48pV5AQb+6gXFB79q5Gc0Au0JE4E0aFm0aT\r\nrLH2OUZgreur10a7LAbV8mot+Tn0nrSC/PGCwlDN8Nk7PLZ5hQy08nxEffQo\r\n8U78k1aC3USZ2fsWtSeflQePu1/bt/0sHEn5TbDsRUg0vzJuSKSS1JxuINM/\r\n3KQOkTOcfqYkwA2URNbKaqK/Hc5q+z8IfyhvXsdkKn42o6L9owk0grSAbSPY\r\nsLTZIw/HZ6ybCBAVKvmoDkK7zRx32hW2kFwmnWxQlOYdcwfZA6kFIPY2Jpj4\r\n/OBgcYVrEnZkl8fjUv0LG+3FoAUwND4XeK1cLoL1gRZoBBbvocX/m/jpM512\r\nSMD+9Qy7Yh7FNW9OovxfJzFwKITCPhFRIXvhX40DCqh6sUHJJ3XnvzNt8yin\r\ntjMyNAu75ec5q7JGFLAqU+f9pOc773pK6hc=\r\n=zWdc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.27.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/20b38747b4246711ee64f1b6a630d4ef/vitest-utils-0.27.0.tgz", "_integrity": "sha512-2i6LRUzgIFGGn5KuiJemHGkpqtPRnulnVz/Qs9kfwijURcI8da+XiYvhwJ4kqsfg9OIbeCcqG6jC/WOdj81efA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.27.0_1673267601011_0.809862664094547", "host": "s3://npm-registry-packages"}}, "0.27.1": {"name": "@vitest/utils", "version": "0.27.1", "license": "MIT", "_id": "@vitest/utils@0.27.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8a07ab97ebae3bd7caaf63710bb305223c044602", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.27.1.tgz", "fileCount": 10, "integrity": "sha512-z/EykJojgWlj6/7TWKjG2VC0SFqhvzDggainz39Q8oCrH8zEvVOF/K62PUwvgG8htowwqv3T36/5wS0G4495kw==", "signatures": [{"sig": "MEYCIQCtTfwwf9D3b6wXwO7oyuzWYDLcGb8pjbBEqbn3KiLC9AIhAN9g6VGRAcvdChd3eihMYsHewdgew2/U3yriZMjuxaVQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvuSNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYag/8D54c32krUN8ayj9VXhJCKCkdyomplpk6ocMY7TQkEtGl3FFt\r\niJ++L7ntfBAMRpRN4jEsZGbXtS+rl2O2vz/i7AqXVqqK7qNBGbovb1XhB9+x\r\nFp8h4e7EMUgfN8r5s2hjI/0y2q0Hi5Nz2wMSlhFPZUJIpioOsvlZFpqjvTgo\r\nIGb2lio3a8rHz7ej1/HLP+mDcS6hSzi3wm1X7GC11rTcYQOdpzoW/msK5XNL\r\n7EWOezFiaTjdElsrABte+5mJmFyiiCzXRBP4Sjt7FS6n7jKOo3009s6o29n/\r\nZm03nEmqRDb6Wvow4VC3bUpGdGagyQ+OdsKgrICLyYQLbhXP+F3KWTSLN00R\r\n7D+ATB3HgJtLo+kIUuivUWkuP8gLmz8nrGfULZ2fKCe5jSvnUBH607yvMy/0\r\nw/3AsSzfhS/A8pR1G1Xi+ftrzfzOdjm3Zjlr/oBJHx3wtAP3JXbKH3of0RLD\r\n6zlSkBuTPRIi7z66dGXygpyME/H0OC9T+cKsun28Vv6mAgp9kWNIMFTLh0o6\r\nxsii/l0KXpsg3zHYVSGZwONG3ASaD5HgYB00rbhh/pT7nGlV6iY47GKO7GX0\r\nhkptOoENWUV8/Eoa/O2RY3jKB7knRUvngSVO+kcRW2XsoSdSXG72oAsRLKka\r\ncoObTcFT+ViacHd/HQ9n/CP9FGCwd4ccbRU=\r\n=1juy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.27.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8b0309e6e53e3178835b7b875d54d0be/vitest-utils-0.27.1.tgz", "_integrity": "sha512-z/EykJojgWlj6/7TWKjG2VC0SFqhvzDggainz39Q8oCrH8zEvVOF/K62PUwvgG8htowwqv3T36/5wS0G4495kw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.27.1_1673454733377_0.7886148860710707", "host": "s3://npm-registry-packages"}}, "0.27.2": {"name": "@vitest/utils", "version": "0.27.2", "license": "MIT", "_id": "@vitest/utils@0.27.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ab8a88d7552571a81dda570064e479e0f2ab0bb4", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.27.2.tgz", "fileCount": 10, "integrity": "sha512-rxrVsMyDoUym6XxxmUKnmkO+YdbsJc9UpwStTiySCIMoE3YLF619NAcDYibDszvzSKOwBwpHFboz4p2R2NdxOg==", "signatures": [{"sig": "MEUCIHlOigZay8Sxk9IPS4hyyJdYu1VOXKMt7y7kLoUKoBypAiEAxxPCU1pDNloi1w64I4RFT2e9fDpFpZoHG6M8AKbJagc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxlDtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDVhAAkjdE2nSNfoslHRpODpxTVRdZwQu5crrPbVMdAmZwo2DMBtdQ\r\nERo15r1skwcXhvtlBghGcC3c1GwX0u9ukaxV0EHUsOloyermd3XFaC+uIzL1\r\nLCOKLF2CT8AWDnSDvFlzAPaOK9xAK/PRmS3tO4YkpDyKGDWpW9UkhtBCKbJU\r\n/y147Y60HxRIm+iiKj0mQDVsbhenxy/jqU4LIe5lNuRruD9toWU8UPsJeHYY\r\nCV29exJCZlWthFubcgHwwfOFFZZVkf+DEWxdvUWPqBsNzpt0qeUPVetn2V9l\r\nYZvLdWAE9yHT0cqQkKGsHhn2lsqLuOLYYFRkHEjZeL5BEqqbaqWy59497IpJ\r\nmMy8/MRlz6G8y7ywr4f+R1l+xhn0B1CgqV2cvNvXs9czFGXv+8/9SCBt9y7t\r\nnfcwXsf+4HMiBlfv05e5HLH/q84VVF0rw4WGJduRynt/jbHYxw+rTh34Rha2\r\nUYMtRh3XwMvGUumKmax/8wRntfwn/v5BiuNTNE6UdfhACfgvuNZs8zVW6Wqb\r\nMW+F5KtdvV3vjJjLKTgosqRcrHAzEwldzpTF778tD55BjRfqI2XDKcgfqjvt\r\naoViKwJ1qTH4mq8TFyrBL9ZBicgkxF9Do4wJ8hG4LauM4dki3nosOsbmAR/6\r\ncVUD/T6YmLFj7WM4e4bwcYXhHM9vgugvbDw=\r\n=LoEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.27.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3993b5e9329ad320898a6ddcc817f1c5/vitest-utils-0.27.2.tgz", "_integrity": "sha512-rxrVsMyDoUym6XxxmUKnmkO+YdbsJc9UpwStTiySCIMoE3YLF619NAcDYibDszvzSKOwBwpHFboz4p2R2NdxOg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.27.2_1673941229081_0.8804626433549754", "host": "s3://npm-registry-packages"}}, "0.27.3": {"name": "@vitest/utils", "version": "0.27.3", "license": "MIT", "_id": "@vitest/utils@0.27.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ed8b202dc92482e7fc3d6fbe666a759b69f50dfc", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.27.3.tgz", "fileCount": 10, "integrity": "sha512-mmLhITTe9g7e8Y91NHXRfCRLD7qCHLIwgCJ7l5dl6+QXarEaAgHCufKCgkTLlfO3bmHQcZlGIf7Td4Qools04Q==", "signatures": [{"sig": "MEUCIQDvz8QdYHoHBmSvnjwM2j4kQd4bC1nw4500RMSSE4/6vwIgEIZRLB5zWAW//b+AUh/FV+AN7I40n1RoliM+Jpdn3BM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzBFgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIcBAAoPZl+GPRoHLJdkjVdE66LFqOIM16b0mMNXm/KqkcC7YtVerL\r\nM+RwoUOHcnDJm0PybyADwxDe6uRANYwCNIQHwocqf8joXWO+wcluWr2H5zbC\r\nqO3dQn+N1XugYa1etuvUZ/+L29t3QSOT4sxFbLYfFW/fvh3xr7YAt2FKbpJ1\r\nUdwmuPb88XJY3Jbe4A+vuQhSul6z0RNVq0skl9yb6J6nbtYQQ33DgfCdKBpY\r\n2d0A+RQZmOJ3Vca5Zs4yhe1AlJZcb4WwFO4Fx1ehGlSG+4B5sH0WoGBDUrca\r\nseWFvETZyWrTx0XmZCKSIVR+mzWnD930o+r8BbjnbrhOvjgDC5DYmUnbZrFA\r\nu3kZvNp65XjHPmOkKl1zbdrlxt3gJxbz58YRLGr3Aj2Us3nByR1/if+DGwpp\r\nx277WAwFGYo4yW8vWuT/E5cCFS+Ya//xNDB8/jKqlqbPqGOgAybfGc29DDen\r\nSIj2PDAYnwBcznhpAGtMWG6tO+buvH65YnalFLYdD6xGtibX/4C/mN+8WFuh\r\nHbYD0q+F2m6xGCgJg6/yCuNkvgQjgSnN5Dmdz8TGvF2K/G2qoPWhIEEQrzR4\r\nBbb+/UafXco9ycf+0k/Du9rifuQ2d80ektC8jt5YttHVxxRBmWydg3q0qgSe\r\nefHMVkjYGX1yUuajvXRz6csWJnrm8eqh7wc=\r\n=MVp/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.27.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/bf4363c30b5daa948c04a5aa8160fd64/vitest-utils-0.27.3.tgz", "_integrity": "sha512-mmLhITTe9g7e8Y91NHXRfCRLD7qCHLIwgCJ7l5dl6+QXarEaAgHCufKCgkTLlfO3bmHQcZlGIf7Td4Qools04Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.27.3_1674318176587_0.5694128759296959", "host": "s3://npm-registry-packages"}}, "0.28.0": {"name": "@vitest/utils", "version": "0.28.0", "license": "MIT", "_id": "@vitest/utils@0.28.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cd8802ef3a7e72ec22876d8932b8f5ddca849528", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.0.tgz", "fileCount": 10, "integrity": "sha512-Dt+jDZbwriZWzJ5Hi9nAUnz9IPgNb+ACE96tWiXPp/u9NmCYWIWcuNoUOYS8HQyGFz31GiNYGvaZ4ZEDjAgi1g==", "signatures": [{"sig": "MEUCIQDEI9/Tv/ZOtF9cvvkF48BJ14Z4bztpTD5aX7ZBhJuJvgIgOIp8Huuqx0Xx2TiVmn9cUiNNb/NI4YXuDcxWuTgQjak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzlMYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3Pw//QMSEJf5CH3QB3uO6lIqSwfWpexR1PX2AFL8SR5mPzQd7he1l\r\nsCsL51m5z2eOrjnjZ0LPXSqFiXbZAYYMXaErYlD9ZNZF+eY+6Hq+cMcQd4t7\r\ntDUpFTmvXIGn+F7uMcmrFkymfyrFmicHZz5T2SonA+P7dzaAVeE2gF6rJlxH\r\nTFh9cP3XoiJGr3GhvksorE5pS5xGxZdEqUBHxHgwT61CNkT+JTJgMdmOKBCJ\r\naFPVEVx68572DUrWvQ4WO25bK4Nc5x7Nv5fWJy3SVucq5E9bcBolvF1tVOUD\r\n/LFXudUwyD4D/Nc/z4YyF4ueh7rMamaP0NVZ0pnWZyWgQswkPn2wT8J0ImFa\r\nDbzTwwE07wk/chxJxw2j3GLBcF5ATlX9VVgj4+h0fP1xo/prKUQ3gh6WX+JT\r\n+CVVkxTH3LXnuHKnICiU0Bnm8uiComdTbdyhir5/CQ6VQQmSjKQHccew0qmd\r\n03Jo2IpZiSUiU67HfjCuPspPTpNPn8/jzS6Hs+3MRS5CSeNaYz1d5xjDq1OQ\r\nxsZsdDRivi/d8IaSyBE3skx29x2cBzeyiCKmns0pCbhpQVyqtHB0MQ4m9N9T\r\nwl3+KjZK2yIJ2TVx3SG/PT8GIh/TPR7EtInlnkDaNH75piOrSxPgC2l5OV+x\r\nndQNUd6CLOpeUmataWQIMPt99dElNEyUDFI=\r\n=3JaC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/287a16779bd49cfe1a3bcb672f994873/vitest-utils-0.28.0.tgz", "_integrity": "sha512-Dt+jDZbwriZWzJ5Hi9nAUnz9IPgNb+ACE96tWiXPp/u9NmCYWIWcuNoUOYS8HQyGFz31GiNYGvaZ4ZEDjAgi1g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.0_1674466072127_0.032219098590315465", "host": "s3://npm-registry-packages"}}, "0.28.1": {"name": "@vitest/utils", "version": "0.28.1", "license": "MIT", "_id": "@vitest/utils@0.28.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "01c347803dff8c02d7b097210f3749987e5b2ce6", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.1.tgz", "fileCount": 10, "integrity": "sha512-a7cV1fs5MeU+W+8sn8gM9gV+q7V/wYz3/4y016w/icyJEKm9AMdSHnrzxTWaElJ07X40pwU6m5353Jlw6Rbd8w==", "signatures": [{"sig": "MEQCIGqb5Du7Ul13pRto6gluSwSoqCuujG7K2UE9gl2tJJ3XAiBezqe9rh0hdIEcQdVWnzFOSXMA4geXXGGbeoRGZKcqng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzliAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiLg//RLqYJ5u1Jv/vbcz+mrmxkXGr/YIwpEeeKB1JsTZf0uFzidUU\r\n6nqgLhMvxO7fFbo8Nz/nssmQQL/jVH44BcpExMFAq/Sd+Wh2cJX4T8r+hi5Q\r\nLHsE6D3z+Rr8GNFpcVSmrmhTfHKUqrFQ1tL8iyFBeZgN4OuK3o3YV3cADlbR\r\nmgL6IO83crhj6RBnFcwr9W7HqRG23P8KTwK97jDUxQY15rGiklBhIgkCatTF\r\nmI+KKJxsk35HXkBz821841xAQsjeeevn4r9tYFrp/p/N2+QKgda+UOWdLNAj\r\nD2TFCIoK3sP9LYEr50icJSs1BVRxZ4gIbz1dQlf+xZAhCDqQXMEaO1tWs9t1\r\n1BA3INNX11JyckzgRPxPzpo98WUHAs9u1vqcvkLZAjNxr4uwpjZ0FLgz72/4\r\nRzfE4Q7+fYB7IvO362Y7yR8gyAnX3MWCiMDQ2+D+yswuvX02ZEzluDZo1g4K\r\nr9mi+kmvSRzhfCkxX/rqYxTDR3nl3stvJ9/6hqJp8rcx2a9bFuW1mvJ7l8W6\r\npgJ0L8CDV8oyNI7DPPNDA4pnWmJQWz5XmlCsigDSMzx/8yTLaQb/wphbFasx\r\nW8LmzTpMhEGGMAlk+ilNF7IIVBogIfGwOWOlk1NlVct07OaMIfWbJ7OQgsjG\r\nAT4Sv8sOUu8K8JqdwjhT20dlUrU3VTpxteI=\r\n=SFJR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/df304a3a45fd01427b926e3f99a84c73/vitest-utils-0.28.1.tgz", "_integrity": "sha512-a7cV1fs5MeU+W+8sn8gM9gV+q7V/wYz3/4y016w/icyJEKm9AMdSHnrzxTWaElJ07X40pwU6m5353Jlw6Rbd8w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.1_1674467455893_0.5463359502157059", "host": "s3://npm-registry-packages"}}, "0.28.2": {"name": "@vitest/utils", "version": "0.28.2", "license": "MIT", "_id": "@vitest/utils@0.28.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2d79de7afb44e821a2c7e692bafb40d2cf765bb7", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.2.tgz", "fileCount": 10, "integrity": "sha512-wcVTNnVdr22IGxZHDgiXrxWYcXsNg0iX2iBuOH3tVs9eme6fXJ0wxjn0/gCpp0TofQSoUwo3tX8LNACFVseDuA==", "signatures": [{"sig": "MEQCIGa1O2q0NNJocA9cpvgFSRA/3E6JIY0T4OcJQ27y/vM0AiBpWoxUBDSqJyUBau1ntfCVtZWthEzyNMH+bnI7Ti2M5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0RCVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpzow/5ATagLeELIBxXn7gpBATpzBfOd8de9YYmpncXxah5iG6MjV9E\r\n4MNd4u0s35vM2cKJiQZxK7ow3MayXQQ3nvzgs9+f1/0JqUPU8A0+L2CeouxL\r\nRc4Z7PWhJCbQK3hK2OW1i1rVuD8mZ3Wu1u/R0hSokc3cky0HWi7B6mUiIh4M\r\nZbRsKPb5a12VvH+p+lxfPSdhFstL4PEPTRs9dOrzMZPv1mTCVXUnekOWS5L7\r\nxHynDEJa2ka/l5iiIV3AwfFvPdo9jmt5eNQn4RCzBjnPGHZPilTQBYHZ+ud8\r\n40ty06KhZaWdp+syLhb2nBwgvBFie703UDT/xufS5HZ2w6/4oCYhCbmo8XRJ\r\npIA8ChqWbaHNpUkbTlwVDc9J527DEXN/hkSNxMiBVgJx6mO52bCqAe5+byl7\r\n4QHOY68Ot6MuVMCF/0IUmpT8I9AW3EpVEabj2BNDLHbvxXDTtWp/tcvtgLbD\r\nqn04yXGwsZHMo3KLL3FxGvyxzR2dI+09KY931dspOjzpzBTZPnD6IXEOfoHj\r\nJfsDuTA3tZdWeeHXaD/01E/ZjMYTTT0A6a/AQjwtZEAIMNrqIM9dDZ5bz+3k\r\n5RWvA5NbKf2PRzUB4yI4z/xfrdDbme8+Y8DD07KKfwS0I1gz6NEkH2ySG1dJ\r\niGifz+OsRzQ6NIzx72iaP4ezhv5FRt+QVEo=\r\n=tNI8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/30/nymxcyb909ggq2j5lwn7b_600000gn/T/216d634a64e140bdeba7df93f405d812/vitest-utils-0.28.2.tgz", "_integrity": "sha512-wcVTNnVdr22IGxZHDgiXrxWYcXsNg0iX2iBuOH3tVs9eme6fXJ0wxjn0/gCpp0TofQSoUwo3tX8LNACFVseDuA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.3", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.13.0", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.2_1674645652859_0.14592682175313776", "host": "s3://npm-registry-packages"}}, "0.28.3": {"name": "@vitest/utils", "version": "0.28.3", "license": "MIT", "_id": "@vitest/utils@0.28.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "75c076d4fdde5c48ee5de2808c83d615fc74d4ef", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.3.tgz", "fileCount": 10, "integrity": "sha512-YHiQEHQqXyIbhDqETOJUKx9/psybF7SFFVCNfOvap0FvyUqbzTSDCa3S5lL4C0CLXkwVZttz9xknDoyHMguFRQ==", "signatures": [{"sig": "MEUCIQCohEjDt4ziNN0h+TmyIIHBv/Vb/Y+LuEGrNOXixG++HgIgQM8ORk2R9RmenqSppjR4J8nnKbirwuqq7d7pIy5A++Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj078EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrULg//YfTo8DbWfFJjZJyZSrfHgZ7+MIWTm9Q+CeakYUO4ZuemZL51\r\nKkr8dhx10WSXGmLIj0FtZr9xuoasA5x6fnhJQE3GhzlwpEwsapx9k+7/k/KH\r\nCLVGu1OBxqasHRg6jwFmpSS0SAvT4BXCgojftxAhNEi19SeC2INDJGrc9Vqi\r\n2nd9Wbw6I4CxlBxlr7ITRqJTZ9KSXgfOPKYq4R9VxzTfbMVQnEE3Yy5eYY7+\r\ncByGaOosEWwMXvmieV3Yxn9SB00T9YgS3FrtiDn/oE/u2v0i8PVGMORWt4ui\r\nJLHeSQ9CRMGmiKOZc/SwDr/imgCC1gZ+QTbxd/tPkQLqFfxIpOQvNgCSf9RJ\r\nESQSUyyC2KYXzgsN2IAPuIWVUH3fX9XzBF6fBdkC3ISLTVWwa1UdkiXIOtNL\r\nLjcuoZtAedeI6TusbVK6Om3B1rlJFbS+lJ1ak5Ar/C4nmCUGOJLV2WXfAto4\r\n3JS8hw8kM+uTydRzC79PCqxEELVbH39LMdgi3q71/WO0vlL6/4H0a509Dc7e\r\nFEkg3/nmPJO8wUzClxaf9xXIWBsAI7SqxF+tCuOgFLNJw6H5ol2BgrC+wYzB\r\nGPcOSYUt4OcOzjISDw3FsfRrg501vaE0iiqZMWjBi8IAhOLii4ayA4UA/02f\r\nr/gvSAFTCmxOSbbf4UXWjId5sYwvbYXXHSQ=\r\n=tLGC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c925992c8cdd35149536bae58d44433b/vitest-utils-0.28.3.tgz", "_integrity": "sha512-YHiQEHQqXyIbhDqETOJUKx9/psybF7SFFVCNfOvap0FvyUqbzTSDCa3S5lL4C0CLXkwVZttz9xknDoyHMguFRQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.3_1674821380414_0.5856986561899946", "host": "s3://npm-registry-packages"}}, "0.28.4": {"name": "@vitest/utils", "version": "0.28.4", "license": "MIT", "_id": "@vitest/utils@0.28.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "be8378f860f40c2d48a62f46c808cf98b9736100", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.4.tgz", "fileCount": 10, "integrity": "sha512-l2QztOLdc2LkR+w/lP52RGh8hW+Ul4KESmCAgVE8q737I7e7bQoAfkARKpkPJ4JQtGpwW4deqlj1732VZD7TFw==", "signatures": [{"sig": "MEUCIQCOBaupofnmIqUfmlONyi+M5XUlY/dGIy5/Xw7eV6VSjQIgMMwQhFkols65t7IE/9Wv7MgNEtiCUaskwn0QaVZtQXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3Nw8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotUg//Qgm+JG+j2pHnT4hsoPCDrqQymZV+gqWqEB5FHWY0imDncBLI\r\n3S72Y91OR6AqwOYQ20x5gwxWd2sUJxjvtoNZwJxH65GQS/WZNvywwBCn1mQj\r\nzPuH7y3sD+mwlYh8sLz0vWtIupVjSu99LndTJIjerBkqb0nUwtWbxrg5c3cl\r\n9pvdJVpqRItJyH0xdR6AhwCgzISbOMrLk/MQErpQ6hVJSuIjnvGVZVrXOMUO\r\nT4dHnAaBfh2HK8ifZknAu98gCqRz3nyEuOIyf/MCwMtyPAyBvCEkPJPm+Q07\r\nL0s/y8XwxM9a7mJOfYoCHfn/HQkkgEgqD30F49MZJDAmC4TzlaEWbdCCr/0d\r\nqD269EaCttQstN/vJI3rVIhaLF0fEsb8ocoPLLE3CTJYVy3T2lYPvwgQbCot\r\n35OAGNQw4ab6w/HQUQZKDI54x/pdFJOGkqXdB9koFwm7ZHEUsYSwPuFfe7WP\r\nWC77QuqccbWfm/hqlgrlI/KcTsEKU6Sq43v3sJVjz0RnTvontcvOJGlMvCN1\r\ngc5nRfaJm3lAlZwLyg8xWSsGr1HKVqoZ3yvNZsM9H7PC7rantX/W5ew2RFGT\r\nXlgtECUDW57SplJEk7yaivIrYF3omYc7YKj+o8JVd8iZuw4Pmwr4IFB1mKpE\r\nJAyWSfE5fsalkDCKK2mOZYOauS378W/rWOs=\r\n=tkKx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8d6f37c131d138d85f5b1f75df75ebd1/vitest-utils-0.28.4.tgz", "_integrity": "sha512-l2QztOLdc2LkR+w/lP52RGh8hW+Ul4KESmCAgVE8q737I7e7bQoAfkARKpkPJ4JQtGpwW4deqlj1732VZD7TFw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.4_1675418684311_0.9516169856141998", "host": "s3://npm-registry-packages"}}, "0.28.5": {"name": "@vitest/utils", "version": "0.28.5", "license": "MIT", "_id": "@vitest/utils@0.28.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7b82b528df86adfbd4a1f6a3b72c39790e81de0d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.28.5.tgz", "fileCount": 10, "integrity": "sha512-UyZdYwdULlOa4LTUSwZ+Paz7nBHGTT72jKwdFSV4IjHF1xsokp+CabMdhjvVhYwkLfO88ylJT46YMilnkSARZA==", "signatures": [{"sig": "MEQCIFZVAdEisJD+mMUaYbn5oZ0xD6qc+IC3ZDDNhE+S5si6AiB8WM8DA7JQ5NU9gOPzpfQZWpYnJQCApatb8piWkqf2qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6iu/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4+hAAjCDdD6YAou8sU6NGpmYxQXZ8/e/rCBxnOBSEKNTEZyXRn5VS\r\nNX44wJ8q30OW9E77oZj8CvsR2jJxD6W/xO9gCmpcAHB4vahG0ZvwvdK+5gNp\r\n2mIgfWHcw/OkmhkExT6hqrc0lI6DWmc+Tv5VoS8TbK/dtjaJCtga4rsDK/fP\r\n3PIcyoCdmFLUOob9Y8ah+rGrre7RS7m07eTV4cY7Eo6k0prcgSJxpNLPrkmQ\r\n8V6dPx7MLhsWvzfcp0iTcMZzqgcX9toXPwsFKh6uMzZ+4oIpa3GJwB1sYR2I\r\nagpi8nP9EiHmIA56oH7ghCeUu5oOI7KedAQTN+baBp9IiByLzlkClP0wsoih\r\nWhFmE+0MhKX3IcnS2QAkIdalVWxccHHIw7cJFOV14rCwCPpiyNGAFa4VzIHg\r\nH4GARvR1DSuB20j0gT7PECtaOwAAXmkzrJnNo3HJdNS3/2GIKQJcqMNDa9IT\r\noWCD/FGU+ljlO/TpIMtFg6gDTw/q+OrnSDCn2soHYpSuc7GTN6Fn/rqVGZAs\r\n0dSm2YNnQ6d2rUaHZo1YSn7GqQHgYz7feuJgSeNQ9tuoStJkA7mbyL4VhLXT\r\n6/SQDfIlUIwR8aff/lWGrsi4xmHFNU3yxOBCFtIW92vwfcYD30rWVVvvYDOO\r\nmnQqssfKtaLWAuHPqTXTifbwqgsKe8dgmno=\r\n=Yz6+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.28.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2dd45bb17c7ff81083eb4ea35e75e41b/vitest-utils-0.28.5.tgz", "_integrity": "sha512-UyZdYwdULlOa4LTUSwZ+Paz7nBHGTT72jKwdFSV4IjHF1xsokp+CabMdhjvVhYwkLfO88ylJT46YMilnkSARZA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.28.5_1676291006901_0.49295847701805373", "host": "s3://npm-registry-packages"}}, "0.29.0": {"name": "@vitest/utils", "version": "0.29.0", "license": "MIT", "_id": "@vitest/utils@0.29.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cbcbd979aeee6f81f4fc7d4dafbf5fb3fd616a58", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.0.tgz", "fileCount": 10, "integrity": "sha512-jbP2UPZt5tpNOu//1aMpExqXvZ9olR3BsulzQiHXxlJ18WIyNiBuf3EDhcTMqFkDsuizF4UjCr1+z+qVye3NSw==", "signatures": [{"sig": "MEYCIQDJiMcYpAbECd8uAW4o3TAQaaK9Ep6qu1IG9kzyjz7aXQIhALbmj9daeRElQwqV/fEeAY33CG27AJ78cv59Oay3+Z4E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+cX5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8sQ//e2goEXQAmugwHbx7agg2T956okeKqj+FpBzJDT9vCSEE8VKW\r\nUKH/tjWfEvtSLffb3dXChqzQPNf2z/KEAl/MbutI0n5LhWJlusDNggWxrdob\r\nrWOEC2b7yjifQVlaksBP8U5XCzMk2cf/4Psq1VUkTdM0ufcWVZ7sLZsUAeGe\r\nN6BoWg98Hr5uHLaRghyz28WP5EPKkA4zrG4+hF7kEL9mk2bpyYsC86r/Q58L\r\nm3n+qn2Q3yeexuVniQ85aEQBl/dHtqlFhcrPe4eW4aYpPQM52dg0Geq8A5C7\r\ntoedrprBZVlpQ+7QD++/Yjux+RDx4g33HNhs7ypdUEORIDbJ/G4vuvr+b8s1\r\n2Ts0iYpC1J+h2sMXbxL3ODdTDAnMgxpTsDhNw7RYue/EnBmR3MCC2r1+AgRA\r\n7DhJ6+/bNRvUzfERymQlJMy21HV2VG9cNPG6EbOpDsNAhZ89DtFvtG3+g3iO\r\npr86UXlqIK0bhvHAqOYQw34tkriQCGRPX5e7tKFLKuiXZLhEwdJxHaQ4DX1Q\r\ntd3xHR+JBHXBLawPFV/+AgOnw6VA/oOBjTOdzRtq03YFQ/X6sh+hXBMRpyVG\r\nh635G1wOvsv3227QtgvwIN1EqhEkTPLBY6HoL4pkEFws3I11gNkZwV+jkdU8\r\n95NaCRE4maLEFZJBkvD3qV4hqeXQJMEeH8U=\r\n=mskC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/78c7dbb3d22f1fb789105d2eb8804402/vitest-utils-0.29.0.tgz", "_integrity": "sha512-jbP2UPZt5tpNOu//1aMpExqXvZ9olR3BsulzQiHXxlJ18WIyNiBuf3EDhcTMqFkDsuizF4UjCr1+z+qVye3NSw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.0_1677313529638_0.9685342701869588", "host": "s3://npm-registry-packages"}}, "0.29.1": {"name": "@vitest/utils", "version": "0.29.1", "license": "MIT", "_id": "@vitest/utils@0.29.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b5de0f836becd18539a8bf9a68c1b06eee23307c", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.1.tgz", "fileCount": 10, "integrity": "sha512-6npOEpmyE6zPS2wsWb7yX5oDpp6WY++cC5BX6/qaaMhGC3ZlPd8BbTz3RtGPi1PfPerPvfs4KqS/JDOIaB6J3w==", "signatures": [{"sig": "MEYCIQCGfc2bKG4fWjCIJ7z1fTD9ifIWKFK5ro+QgfhQT1M4AwIhAO9OCzAK9ayk1BFzWS+ek3bQS8ViCuLrJfemzAXUaVO9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+dPbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg7A/+NB4vn6+r6RTLJ+jUKyed9o9zs3DSarusANJPJG/88GDFz0Zx\r\neo/fc5b8BRZSnmd57yOdY4deBrQ9qQSZj0oelSLW5aDCjfxE3pltXyq/hkIp\r\nqDzV3+ZS2HA+nTvPDEGIUfLSWInoXPnC6JmUI3oFR7POXNnvqaG03T9//k8Y\r\ntd+ReG57QRxu7CB/M81ElDwZUFMtX88Ayv+Jirnv8+XReJTl4KcxLiJN8p4C\r\ndXWgtinNYWMnlg/1eeg+s+ZoJfl0uu4VgRoovzDgq85BlnUWJASnKVPmldIh\r\nRRv3IIAM1Wi/soQkhaH7sDdKQ9WFKfQ+4xqafwzeCLxvd76oPwOsOZBQPNn9\r\nnnb8piBPPmcFOII/jxrQw9+1Qw4jos9/EA9cfvplqT7DBuhmzHl/eBrnEGSb\r\nmEstYxEM/zjKO3gPaBxIO/jUA/ayz0clSPcrcpVHm2PcYrd2JejfPOBMPvYH\r\nJA2eqxl9JRgduzW3iuTT8st9d9jAgQhs1OYP2lsga6jEmBfYoXkTHImg6mqa\r\nd/3hb6PILYpvgjing0rxIE7a8MKXatgCl+tWOWKkkp/ZCjlQp1p030VCk3t3\r\nWC3+Bt54I9tfETKeNWXz9fqnbbityhLTmhByPXQkIHLaUD0mkyNTsvbMOrlX\r\n/DXC0j6jNFeAXMBQ7GGp5tv3o/rtJP3Mbtk=\r\n=TXRL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/58a2f41f7ec7497323fa7f88bcad7d0e/vitest-utils-0.29.1.tgz", "_integrity": "sha512-6npOEpmyE6zPS2wsWb7yX5oDpp6WY++cC5BX6/qaaMhGC3ZlPd8BbTz3RtGPi1PfPerPvfs4KqS/JDOIaB6J3w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.1_1677317083118_0.4751627072314961", "host": "s3://npm-registry-packages"}}, "0.29.2": {"name": "@vitest/utils", "version": "0.29.2", "license": "MIT", "_id": "@vitest/utils@0.29.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8990794a6855de19b59da80413dc5a1e1991da4d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.2.tgz", "fileCount": 10, "integrity": "sha512-F14/Uc+vCdclStS2KEoXJlOLAEyqRhnw0gM27iXw9bMTcyKRPJrQ+rlC6XZ125GIPvvKYMPpVxNhiou6PsEeYQ==", "signatures": [{"sig": "MEQCIClcCAECsztgWw2VLNtiJVyjNNDjkkAS1nEZ7A4GBiUfAiALUpbkYp/5LV09vwdtyOhO1ZoztV3BTeWLk8hNOyG4RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/ho9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBig/9Gr1Kpowernznge7QMHAjj1O2AfGHK6JdZMBZBE7kwhkKtyTV\r\n5MBwlHDtXZ1mV+XCMJRBQ5h6WlKfLz94/+k5oxhkmVpjCHm0J8sNIn44PHuL\r\nzgXY2oxJV4iwOxV473wlM7Hx25HXj3fuMQbyIA8/9hQxbpT+SuBC9ORDt6Kf\r\nLf07XqKMmkl/E5Zxf/sx1bWzJcmewKLwLK/+G30visGuvhLs2CfCHFMjElk7\r\nQQ7COYIdmh+H2LudizoMdGhzx4D0gWiqQa5JMp42S1suNSkfLnbQ97XxWs4C\r\n95khMFKz1OUSP4dRFQT+MaD9U2apojeg7RUY+ri/Mcxhhpl3pO52o9dzlv9x\r\n5hCanRXrmOsI/iZZDx9X1z5CUMZx27vVO70UIuZcGx7Ym4Z4IEzYaBHaYw0p\r\nFodKpzwSuuI/WWBv+5Fem/pU96pRXoyQI4HMN89LTDMJo8u/B3++tTEibjuC\r\nFq6ee08YZiMr3o6TnNyDQVX+m1L0soNWm4eZWTXsldA2UZM8Qnv5XK1MJ0Wf\r\nL0B89mH60cpNCzQi7GjL2lMKIGlPy2Kq+RKvPzpJqQkYPzO00De5npJUTsqf\r\nxVVDzIjDlAYeLBv130lcWOYDgpmmpIC6VLhhidjo4X+RWsqPnOCX5vvCQ4dv\r\nNDASezdaJrqE0Df40WZbFf/zkQVcgEmpIes=\r\n=s73D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/157e86cb7e4879413b38abdb69bcf8b3/vitest-utils-0.29.2.tgz", "_integrity": "sha512-F14/Uc+vCdclStS2KEoXJlOLAEyqRhnw0gM27iXw9bMTcyKRPJrQ+rlC6XZ125GIPvvKYMPpVxNhiou6PsEeYQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "picocolors": "^1.0.0", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.2_1677597245538_0.9958156944349137", "host": "s3://npm-registry-packages"}}, "0.29.3": {"name": "@vitest/utils", "version": "0.29.3", "license": "MIT", "_id": "@vitest/utils@0.29.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0e9eed6c61daa2d753031c1a5fda19755f48acd6", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.3.tgz", "fileCount": 10, "integrity": "sha512-hg4Ff8AM1GtUnLpUJlNMxrf9f4lZr/xRJjh3uJ0QFP+vjaW82HAxKrmeBmLnhc8Os2eRf+f+VBu4ts7TafPPkA==", "signatures": [{"sig": "MEQCIAL9L9Xo8KDpiL47xNk3QFZXODRFlDzngxZtuzj5/MOmAiBL8Z0Hvi9lO0ZXDuGU1ulUiQSAHrjTCgDCVRAZrMSmhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEiScACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9ZxAAkPH8PeFhU+psKRBceMvxM+QD8UHmiPCINvyywSNLR8Vc4NJp\r\nrEG6Tc1nVw+8IO9enMYGerRrMgETa98gw4Vu68CqVowbxrzh6AvQlOMq33ik\r\nABDDSKyJ2ovRWSKwwzzPbN0FCG8XOFq/AlX1LtDQv19wpZS/stCJpNpkR4Tm\r\nagKOm9HW1hQlqHYsRjZgSAY7vFGes4MARd9n1FKXT7/NhvdquIk9q15GrlpO\r\ntgQfOibompRV7hjDVoY1iEz3RbQ2fn5YtyceC7T2wi7qOHg7gjdHwPg7IBnj\r\nT2HGhlYrvipo9amWfbQUO9SAIHR9SUdKhDPO3GWXhKsskV3719jSBh2eycqm\r\nbKtncqkE8OPS7nW1C2PEw79HfqILrN7KGT/C7xN5a0IkIkQRdgoGZB9Fw12b\r\nXpps04Z0Zv9aLzjku0PiA7IoC8ixI8Bs8siS55NNhz/AW6XflLxG4gxmLubc\r\nZIMQ+SGMwJHKQdM5XVMtwZ8yGAiDZMH4Jf4PatOnveJmOkVJEx2rqBnFPMpj\r\nCGebhgarI57gMbdNqDT/fXk3ACQtNkcWlnHpA3FPaKKVUMrr5Sl8ixawMCTW\r\nQqYMrCCDUIo6YHJpj1tmbDnDfFkBcUZRdFREitd8Cy20WXbE9DXWithjfLBo\r\nVESiOYRL7nyqcn4pwlSK5uIywpbAp3af5J4=\r\n=w3tR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/261efedafc9ea909855be9dd9d5c7878/vitest-utils-0.29.3.tgz", "_integrity": "sha512-hg4Ff8AM1GtUnLpUJlNMxrf9f4lZr/xRJjh3uJ0QFP+vjaW82HAxKrmeBmLnhc8Os2eRf+f+VBu4ts7TafPPkA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.3_1678910619786_0.027545976767720415", "host": "s3://npm-registry-packages"}}, "0.29.4": {"name": "@vitest/utils", "version": "0.29.4", "license": "MIT", "_id": "@vitest/utils@0.29.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9807270f56b05409a5bbcfcd51f80c608e61788b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.4.tgz", "fileCount": 10, "integrity": "sha512-A1h+hEaoWYecEeIgz9lwLXo9zgvMLu8ZyGs/LtliqurI+JgWggm2dYRq2ceN/0vvEL7AeTMYOa1buZ2YmhJW2Q==", "signatures": [{"sig": "MEYCIQCtf6+4u3pw6JvaMBzMEeQDHjr6DeK+vFZdQ6hTBOui0QIhAPgu9VLBNqIFpYV4Enn5nACCuSCyH2zgI+YjMm2S+zs1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGI/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxyA//aqE/uQKDXB2O8NVBODEgmvaHTLU3Kay4ttJTGrlIGbNL20Ap\r\nO8v61tPb9J1RihWLQv2C55iKXwJPBhZCZ4wMIugmhgz5DaxMKy97lROEun5w\r\n4jztxLOlNHcRaSCPZnXBkNCRKwxBnP7d/GS3ZHbkTO1JsFnYaejD6SSJSM69\r\nWv4swqs5omjc84EONfxF2drdXbOYLPOqilYNKO1IXPbdy2eoRynBih7b2x2z\r\nr53tB0Ky8+dOShcUEKJQg4KOle9MJ1yYKHbhorKrAEMY0QkdAbezuAt+OwPB\r\nNCB+9AXMrHiDcDVQfQtNu6MDpQnwzoZ4PRoeUlMH5oXpPUJZgpqmovoibir+\r\nXMxmtmRzRRkT13vkmnfN7UkqkWXjACjYsiJRyLpMu2lN2rvmvvZdNjGgx/Oc\r\np7UfaSAqWliDdFuVedGi17EdWyPs1f55n/lnhAW0H28LT5i1GRec0EtrWKpl\r\nC6LKfEGRGRzRhelBTyWBOxpH1hxPLhncXcM0zqbnCSI4qAnU1bh7Wo3flal2\r\nEHNBlo0fLI8v2NG31Z58GnTqdPaVsHWH3ST8/0QgGgdsEq+it/nt3hkufryh\r\n4mfgTWa2AipaFjMaKPI+PPP6bRB8ymNMVST7ILQQBFYhrKZaKoYlzywgN8fX\r\nNuVCL71xjz4Ko0UjEVpnmHjZkh0ChBtj7NE=\r\n=1Rz4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/04819eabfe8b5659617cb52cdd68535b/vitest-utils-0.29.4.tgz", "_integrity": "sha512-A1h+hEaoWYecEeIgz9lwLXo9zgvMLu8ZyGs/LtliqurI+JgWggm2dYRq2ceN/0vvEL7AeTMYOa1buZ2YmhJW2Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.4_1679319614978_0.5339529680675954", "host": "s3://npm-registry-packages"}}, "0.29.5": {"name": "@vitest/utils", "version": "0.29.5", "license": "MIT", "_id": "@vitest/utils@0.29.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cd143a7a6c289017254f120557c53ea0f3816d5b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.5.tgz", "fileCount": 10, "integrity": "sha512-WL+uUZKRh3pIHv1W4RBCIxzQKIwBKhjX85qfxTjOMGJTALRacSsGfrVUPDKwfLlAYIKSZ9jZuU4WJaACkeStDg==", "signatures": [{"sig": "MEQCIB3eqwtB+Nc4gmMPlbYfAfy6Dml6Ml4Y3/gp31YZ/pJFAiBGp3THj3s2p31Pphw2ABY63GI0CH1EIbe/A+7nLqEHcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGmxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtJw/9Egy2U5zpLz64moFRkWA7YxC0qFgKyponIK4qQGrXz/Xo5Iyp\r\nwL1QM6jtfulFlwGZrg2ldspzLkFR/Zxzakt6HkzQ9RtFhF2dohDCcWSwtPGX\r\nHARMLOc4/Ytjf6aF5X0lE+LdG6vmxtDa/1S+y5fWXvjkxt+DGqfADXXjp/Vr\r\n9oIF4S7w7Hj/7lyoMuqODDuh/N0WnqDCBBM1bcyh6/KZg8Zx5V4RHoMyAP+e\r\nEjPKcInUryEUYtBtTaZYAfyaFps6hGNOng9I/RfjEgN0hyClCtvDyErhP1io\r\n9LiwJiY9AbERf3DpQ231BiYEanHIKh3LDATI5LqOoQylLajCepsHO4C4drqk\r\naLak4PWdDm3FmjpDxvvvas+LjmBPjuYrfrdAOWINo96BcM4K41tI8R2bcRne\r\n8SlLJ29LtG7tSvWOa3kHjd0KAI+VYpx9ty6lce1Jpnq/Tei41eVkpUwsRFbF\r\nA79JwEcZTqjW/AmDO1U1fjKVj7vHTVu15mQAHTfj8zjIWYDuQ2NVRoif0RRV\r\nn0otPMhGv3n7RgeMSB5U1UUoSGqYj256vwn3mht3J+8kb8zkBjflmwwQ8c9N\r\nzMLRbvgdQdDqdk4XrfAd86zjTbM37++P5t4upEMnWaJ5uQqgzWzAxpByanhB\r\nYd8RyRAVLUcZuHtVOw/YRf7r0ZZNYZQYbWM=\r\n=d283\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/347a19e1f976a184eff5fe43adf1562d/vitest-utils-0.29.5.tgz", "_integrity": "sha512-WL+uUZKRh3pIHv1W4RBCIxzQKIwBKhjX85qfxTjOMGJTALRacSsGfrVUPDKwfLlAYIKSZ9jZuU4WJaACkeStDg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.5_1679321521179_0.3413002057535275", "host": "s3://npm-registry-packages"}}, "0.29.6": {"name": "@vitest/utils", "version": "0.29.6", "license": "MIT", "_id": "@vitest/utils@0.29.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "180a051c432335f14c65ed32245ade4635705b49", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.6.tgz", "fileCount": 10, "integrity": "sha512-9XkNN4Fo0Z8I+jpc1MQeQnbeT39yzlZ8qyybwFj+ttGZofiFG6qi/MxeW+EZdtt/5BnGS1aIKFWcvBruyWIX+A==", "signatures": [{"sig": "MEYCIQDG3/NnhCnCyUe7ctIWMqN98+Q/fogSY4xGsmmEQmEuaQIhAJ4u3J67unIx+m+LSBjC0Ocnr5v9daJC7602gczIXmzg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMDAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjEg/+I45cxaKbYlvGy8aRYB1HRqrN3jKqJJMwS0LHtc3P7fOrqTUs\r\nvOi3Ti9MVUfaWIerTzLPg3XOcvnoj0sT3zXT0eZ864ferWKHLT7oFsgTcAFk\r\naDAw4A7Eirf6jJa9rWwva5Briz16KYo62J3MYyz23SG1tJnqLFkJJsVCHej0\r\nw248J1GTMs79M9CjfKYIgwaPuj2Snd3jA+fohZkX3rVlysY+XjrYZ1Vmhhqp\r\nIba6zopgsKS19jb0Tj5Tt2qoeR4MXleReoEIrAPwdBOqJX2C+FtP1oA1tsIC\r\nQSKaIh7CANAXcNdOBXn6ILILp0BZyvYvSzcZSguQrJZhnT/3XL8mgMZ0Vj4E\r\n1ixbopxW8+Ksi7Itr1VuopemwuS7GkQKxeiXfSKIgl5se8b8O2yQPysU+wDa\r\nZEtq2CD7/T3dQAPkDzx6ny6FFzOBZeM8IY1W+xBWNNFqonVINKIwPfnmNbPh\r\nPFnvCfJxDMQy5mseX+nCyYaneDdSp/CVw5TVmRWh7nKd5iILwJcykZSBpZJc\r\nd5Dh/WnlWxWDcvc5cC+2wYenHnVRkju75CKEaEGLu3VF8fzaqrIf5qU1tsgR\r\nn4ys1OfGRA0ERmdwGK0ZnKp97qa2qkKs/Yfq2bQnxiddG8C8E7dK9DcFZaX9\r\nupZdWGejiOtuUx8XKCYjENnLTgC6EBjWUcA=\r\n=O4Xn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fdfb64471bfd91d2f6c9e30a5dda44d6/vitest-utils-0.29.6.tgz", "_integrity": "sha512-9XkNN4Fo0Z8I+jpc1MQeQnbeT39yzlZ8qyybwFj+ttGZofiFG6qi/MxeW+EZdtt/5BnGS1aIKFWcvBruyWIX+A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.6_1679343808724_0.7102892294164671", "host": "s3://npm-registry-packages"}}, "0.29.7": {"name": "@vitest/utils", "version": "0.29.7", "license": "MIT", "_id": "@vitest/utils@0.29.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6544cffd07c9ba2afbd18373e705748c1e5903f2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.7.tgz", "fileCount": 10, "integrity": "sha512-vNgGadp2eE5XKCXtZXL5UyNEDn68npSct75OC9AlELenSK0DiV1Mb9tfkwJHKjRb69iek+e79iipoJx8+s3SdA==", "signatures": [{"sig": "MEYCIQD/qbdmTyhlxVu2yl5NO3C5uERxkIAcuzFcVr3hzuqsMAIhANx5+hW9pCHmxhb6O54OmVBAXWIeMqhrJ1Zh/bjBpS8R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMQFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpr5g//Z+H7VKRgtlGsSfWlzkI7dQveyq0gAhfDbLSa5CXQC7LQOqgo\r\nKZ8unuIwK5kQUnFSq184SpKDRpJEu+lZi1372sMPzqi199KBNMMYEvNzzclI\r\njSiFzC2y9Dv6U/eczkPVRPk2Dqd7AWiLgH2LcSAq78ouOpmfB4Y54/JffZeI\r\nVb4eZG89jeFL+y3EqLN7Gl63HDSyi+Lw7lmAyTQHXjM1Ccna4tpzcnHNKuZ1\r\nA4CQw0a9F41673wghvAlcfLhhUf7cI6n5RYvn8pgx2AVIBssoU4Lz5RllWcn\r\nNUTcQ42mrN3H+9TEMZF+kyb/nCt5/QM80viPGj8t87OE2+MU2u4DX/4CprhR\r\nnIW30gvFSjqYHn/n95YibyTd4Pdca67wRSQnyaN+WERAkIJSni4u7qxiWD8K\r\neStf1RzfnbP29UQGM18oJc0F9TrTvMoNHJogHqVLa8MtbWmOmSLWEF7ieCL7\r\nJzf1/YntXhWFXNZmuoUnDS2RU/H0FJrwz9vVwy/5KZd+acrb05N6Ws85fzoi\r\nq9W5crWsLHncr4rpeQu9mDRv1blrNAHVhdTdl11XjRPSvYKjqdPdMRGwnLOq\r\npubPUGcqA1qGcixoApfpMLiBYUUvP0cAiU+jwstaQMcJDPE6NtfVTKHUeeBR\r\n/41kXIDbc3oYbdZkJ86x/EYgbD0oZILMC9k=\r\n=zUOq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/76321ebac4aa48b48a6be3e989206745/vitest-utils-0.29.7.tgz", "_integrity": "sha512-vNgGadp2eE5XKCXtZXL5UyNEDn68npSct75OC9AlELenSK0DiV1Mb9tfkwJHKjRb69iek+e79iipoJx8+s3SdA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.7_1679344645740_0.35608351978774655", "host": "s3://npm-registry-packages"}}, "0.29.8": {"name": "@vitest/utils", "version": "0.29.8", "license": "MIT", "_id": "@vitest/utils@0.29.8", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "423da85fd0c6633f3ab496cf7d2fc0119b850df8", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.29.8.tgz", "fileCount": 10, "integrity": "sha512-qGzuf3vrTbnoY+RjjVVIBYfuWMjn3UMUqyQtdGNZ6ZIIyte7B37exj6LaVkrZiUTvzSadVvO/tJm8AEgbGCBPg==", "signatures": [{"sig": "MEYCIQDgGgDF628KQM0vKfwfVpjhfSHX6dxJVxiggoj+BiblFAIhAJlJN7AN2l6ieH3Lo/zz1BVJ98Q7/pvO5RC2zpcQyLJf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIuecACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTOw//TBrWeBivWB21TYJp24g+4siSdg6WY85hda/cNRWFUdeWc/RO\r\nNz/xhMnrvRqBWuqGxuM+0uN4ukrk4Pwnb6JbECqOZ7/hDQMPn0SqlsBDCYlM\r\nIljRsg+kaIAEJz1qD8x/AYHki4Rk203ULu2EAEs3n7GISLOKU6Dy869VA2Zl\r\nzF4UaYi/Ctc0GekTUQmaKHMY/m2iDReDp8s6Nproa4c6ey01X7GVF2E8yzpS\r\nsVY2NVT37TxO6MTjLaqJi6fJ65eqKQIGKVKpepNk7AXGKueOWdFhg2c7wVjp\r\nWotvVTVjRzVa3AQG4UQxxi0y4899i+tvDGIqupeaLGwb5Dqtt3aN45TsLeDV\r\nFdIegkflzxs2zKyN7HFehntcQlBpiQlThAjPgVfErOixzZZRprxSk6EjJrRj\r\n+ROx4CvgokCDSESSOapbzO4WjuiBVoIvIW2wX8rnixyLff9Tov8rI8oeNDIP\r\n/BgBpObH9tIJZKi69GggaS4KIC1Puo2reEOhJXmnFaYJTWKXmrwpSbMRckQI\r\nwCvwnVYmIP820H3hIAcNAQCcY9MljvXjEtLayCLVPcWu/ggnK9dWKawxp7Xs\r\nOrRdOHtvW2ebqS3ATRSmJZ/5uFEEOnD11BJAO4BIb6twP8qHAZqCUfC3L0rb\r\nma8gei1KQ8RnAIAVb3jFlG9fw1nMZr1eue0=\r\n=n0we\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.29.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a3f3aa3543edb2edcbcbf72526acdd7e/vitest-utils-0.29.8.tgz", "_integrity": "sha512-qGzuf3vrTbnoY+RjjVVIBYfuWMjn3UMUqyQtdGNZ6ZIIyte7B37exj6LaVkrZiUTvzSadVvO/tJm8AEgbGCBPg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"diff": "^5.1.0", "loupe": "^2.3.6", "cli-truncate": "^3.1.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/diff": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.29.8_1680009115855_0.5950249035953594", "host": "s3://npm-registry-packages"}}, "0.30.0": {"name": "@vitest/utils", "version": "0.30.0", "license": "MIT", "_id": "@vitest/utils@0.30.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a8c50c7a978f0e87f969ebb11c6540915f6d493f", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.30.0.tgz", "fileCount": 12, "integrity": "sha512-qFZgoOKQ+rJV9xG4BBxgOSilnLQ2gkfG4I+z1wBuuQ3AD33zQrnB88kMFfzsot1E1AbF3dNK1e4CU7q3ojahRA==", "signatures": [{"sig": "MEQCIAYxH/SSIvFf8vCHdo05xXakeov/m1isReimDHM9dNUbAiADGCMOdgl/e0bLUGbq6NHaiZj9LOt96qNh84/lWZZndg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMr/cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH/hAAlfvLz+DoY4KdrE940LrM7krJe7+XCb+VEFVr3JCP7/MuWmtN\r\nLPo6IEab/jfFBqzpsuaZZNpbODQZNgAt/jdr5i06EcyK3hoyTbQrkyG0GeYa\r\nH1JHO3UObUWnh2BXTq7UX1rY7PKkUc3R23u9be1hvFZLQDXT1aiNo38LH33/\r\nOQVlNmBRvh094JCU7nj0xPV04yRIfliSsTr24WLwpfZblJvo26Py5CCEyTxH\r\niEwVfaDutI97eP1iVp5oewMnsBDEsgVozuGqImrtxdTQ5YfmqiGjhI4d/Cid\r\nSs86jKGDl/I38emNKfjEfr0ni/pCoJV2ID8zF23evdV4fCryixsmt65h9s0y\r\n2mWo0h/dVYYg7zoip0UnUo4yy28pvhY5ikl83+JoJrY2Q8tcqMpJF2VIORfT\r\nB81IdiLw3WtsCVQ44uZKyGpL43UDXvLX4EJT/psCgyGL15DBpr/X1fYlj2RU\r\nrD0QtzcFTPl/Vc+h17afBaZpyHL6lomviD26zZU2SgkbhQ7iUCUKT22NoMGg\r\nijHS81mGvaY9WEvMNYjav+L9f8VIIji+LZOamqMhbhuwKNOZvFvHSvn8+Z3v\r\n1uTmkIwCJarPHchD4G2zqgvVqOtCY3ax+1pfA3nO44Ae6cQclN6H7bOs/XtZ\r\nbRkMO8LPwix1G4cFf68pT3ARmJjIA3h8EwA=\r\n=hr0f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.30.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b4d41af903d38216ec7709a30b69df8d/vitest-utils-0.30.0.tgz", "_integrity": "sha512-qFZgoOKQ+rJV9xG4BBxgOSilnLQ2gkfG4I+z1wBuuQ3AD33zQrnB88kMFfzsot1E1AbF3dNK1e4CU7q3ojahRA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.30.0_1681047515922_0.8056765837636615", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "@vitest/utils", "version": "0.30.1", "license": "MIT", "_id": "@vitest/utils@0.30.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0e5bf8c1b81a6dfa2b70120c2aa092a651440cda", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.30.1.tgz", "fileCount": 14, "integrity": "sha512-/c8Xv2zUVc+rnNt84QF0Y0zkfxnaGhp87K2dYJMLtLOIckPzuxLVzAtFCicGFdB4NeBHNzTRr1tNn7rCtQcWFA==", "signatures": [{"sig": "MEUCIQCOm/wpmDp6u6WxRPhipjpKdzt80fK5vri2yP3/L3EyKwIgVGGxmKekwHytYpD+lewDaJ333Bh4N8TayJ7clxHfLNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUPgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDg/9FNGTV0iX0TM12IGomRayU2d4Epl+QiGvh2aCuVItko9zESZi\r\nPBSTOM5qdRpPTHLBzQnb168/7rggmnJvbZdcy3kie77uqwRrXywxVQkZBjwZ\r\n5jmgbOtId0epI7K/1Z7IGbGUN3pTqBtv2kUzNLQ2BLpT1vYS2XlZpejdPBcd\r\nJzPzePDiIIauYZjAnXRwWWLWCfTdJA+AprBLTldQOuqNBw3hvjG4ixV40bZS\r\nrdTOD6592iyP3MfvO7er26FMpwESSbKB6sJa5u7rV9zQMz18bbisG+iYVCWa\r\nHPYrs+RNQy2uvak0rEoBElwpl/qfF95hEBM2xINaEaJhzlFAyudF4hrUNWzB\r\nZqDAKdCgcebeA3qa1x/Uplq/MMQvyu6yZRx8lo1n4KJKhsPDCzSaJD1t7wy2\r\njZ7yfhmgWUjVlkY8fRbSYlTEHDKTzcNfJB99DRGnc2Hk+51kxggDVoRh1C4v\r\nqn8bscrBLBg2YYwa1+dSDPInnBPbmb8oeMkLABc6M7teJPWOLlFhwUGDyBQt\r\n6/l6KmcSjtQpCZnpAZDp8R/Q1WVh09AUptYJdnoTW6OMMJ5KiQhD3WsXdlD7\r\n4wubMWHwvujUVT50bKnb6/Yw8w4ExbdjLYibNoqjKhu42y2QtcVqio8yAHZx\r\ng9zCKmz5YI5FY3eyCLsmoAh35UYEgbcTTg4=\r\n=x23W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.30.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2abe2ddc228bd623f1f9700ee5c46804/vitest-utils-0.30.1.tgz", "_integrity": "sha512-/c8Xv2zUVc+rnNt84QF0Y0zkfxnaGhp87K2dYJMLtLOIckPzuxLVzAtFCicGFdB4NeBHNzTRr1tNn7rCtQcWFA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.30.1_1681212383861_0.5533536625490341", "host": "s3://npm-registry-packages"}}, "0.31.0": {"name": "@vitest/utils", "version": "0.31.0", "license": "MIT", "_id": "@vitest/utils@0.31.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d0aae17150b95ebf7afdf4e5db8952ac21610ffa", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.31.0.tgz", "fileCount": 14, "integrity": "sha512-kahaRyLX7GS1urekRXN2752X4gIgOGVX4Wo8eDUGUkTWlGpXzf5ZS6N9RUUS+Re3XEE8nVGqNyxkSxF5HXlGhQ==", "signatures": [{"sig": "MEUCIQDaKBgRBHl7RTWUpmIpT1qTaYNkGX/LpiL631CN8FCg2AIgJw+cUgSGoAD3RSCKIEBAjDix9r9h9czAIWydwDeaf9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqMAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8Qw//W0HS84OB3AfRjyDzWK7u1TEWUwGaNWPOCGvnaNCeoOK7XsnV\r\nz2aZvoGBraiKwgdKI4lDkfZnFwYo3E5kxRiJc8j6vbtCpl+2tX4Nd3arQMZ0\r\nQ+gixY5d4B3X+ko3ekqQA4ITenT6yxmrbrO95SwN74bZOuhCDDFidTGRHD+F\r\n+PlYHAo5oFt9A2hLXWgZ0HFyZ4nSggOxEQ8Uwik45hKeHpPFhHp9mH9vr0Td\r\nHJEh9wHraTFV0sCNggLeEybtkOG/1gOkWY0vTyDEZgkNPphjfxAjxJBW8aBg\r\nRK0WFPEHOgsXvTz5x66MfWOWHk00qZZWBQXecgJdbDsuxaKp660wJ0zz4c/m\r\nM+LryZL8iVNbXHNIQl3L9Y98i/QsjyxBkmLcwap1vuD7ISc0xNMdUEMpgQ+o\r\nXz6E+/h4Y7vPF6/ExLzFpdcH/80hQ6Ak29Ldlb0gA/b459/z1W1b5HiB8107\r\naLQuWmMy4e5lN9MWu5skbZ7OlEfjmrO/kzpzsVsiy2iKxKGLX1gav4c1f6W8\r\nnIY26Gr/eIv8dsrhK7jBHJCq0nqxyR/dRldgNPL2haKOAhe/tpgMv6bZkKWm\r\nAO3nWnuqHeUgTmu9/56Hr0iBoh8yLMRjxr9mvB9ISHdIUh9JD6It7ZC1kLRb\r\n05WYBuTVd7Qa+WsMxZ9RJ1yQbHwYKMMHGdk=\r\n=LlKc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.31.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d8ebd5da5800c857c9e85066e5c6a273/vitest-utils-0.31.0.tgz", "_integrity": "sha512-kahaRyLX7GS1urekRXN2752X4gIgOGVX4Wo8eDUGUkTWlGpXzf5ZS6N9RUUS+Re3XEE8nVGqNyxkSxF5HXlGhQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.31.0_1683137279867_0.5910337857692454", "host": "s3://npm-registry-packages"}}, "0.31.1": {"name": "@vitest/utils", "version": "0.31.1", "license": "MIT", "_id": "@vitest/utils@0.31.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b810a458b37ef16931ab0d384ce79a9500f34e07", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.31.1.tgz", "fileCount": 14, "integrity": "sha512-yFyRD5ilwojsZfo3E0BnH72pSVSuLg2356cN1tCEe/0RtDzxTPYwOomIC+eQbot7m6DRy4tPZw+09mB7NkbMmA==", "signatures": [{"sig": "MEQCIC4G4SbyGJWmlTgpjOM8UKAc9YH6gJdWbwtnzvxiOCwMAiA5Yxm9uNANVLbrMSjIKAYySDscrs+mxz62MvWk4PiJFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41973}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.31.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/641403c2b3f9d1c432695f17a7d48cd8/vitest-utils-0.31.1.tgz", "_integrity": "sha512-yFyRD5ilwojsZfo3E0BnH72pSVSuLg2356cN1tCEe/0RtDzxTPYwOomIC+eQbot7m6DRy4tPZw+09mB7NkbMmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.15.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.31.1_1684333392583_0.29989730309767526", "host": "s3://npm-registry-packages"}}, "0.31.2": {"name": "@vitest/utils", "version": "0.31.2", "license": "MIT", "_id": "@vitest/utils@0.31.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c70e06cafe844ac082eb21d2df363ff421785787", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.31.2.tgz", "fileCount": 14, "integrity": "sha512-B2AoocMpIiBezediqFzSqvuXI7AZlmlPkh3oj20Jh3bL35c8YYWk9KfOLkEjsLCrOHOUFXoYFc+ACiELCIJVRw==", "signatures": [{"sig": "MEUCIGY1PcvAvQy+5L1whSzJd0k80R2gtwb3k/FPcGva2HU7AiEAiBX6+QeuftI7/bGHSZsi+7UrYCnn5xhoRuPcV7xBVro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.31.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/93fb3be3e70f35c7ec2282a2f177273b/vitest-utils-0.31.2.tgz", "_integrity": "sha512-B2AoocMpIiBezediqFzSqvuXI7AZlmlPkh3oj20Jh3bL35c8YYWk9KfOLkEjsLCrOHOUFXoYFc+ACiELCIJVRw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.31.2_1685451874097_0.7316026649757903", "host": "s3://npm-registry-packages"}}, "0.31.3": {"name": "@vitest/utils", "version": "0.31.3", "license": "MIT", "_id": "@vitest/utils@0.31.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "11d83ceb6cd46a3df37cbb80f9b366cbb4b76b3e", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.31.3.tgz", "fileCount": 14, "integrity": "sha512-aLxfilR38ARSw6U/E9hl5bkn2TNeWLK7rZaIWZQUpT6BBilbehwJbSVGENubW5Pb9cawqchZZ7IQMW99t0Ww3A==", "signatures": [{"sig": "MEYCIQDaaN6eYWkRYwyIofs5x66tlV59qhUnoyCln/OA6H9t9AIhAO4RJKa4Nh68XnMegaJsusoMdkmjFGQezkb19sNJ88tt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.31.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c155c97533550e62e076f6488a581976/vitest-utils-0.31.3.tgz", "_integrity": "sha512-aLxfilR38ARSw6U/E9hl5bkn2TNeWLK7rZaIWZQUpT6BBilbehwJbSVGENubW5Pb9cawqchZZ7IQMW99t0Ww3A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.31.3_1685544535556_0.012497449017600637", "host": "s3://npm-registry-packages"}}, "0.31.4": {"name": "@vitest/utils", "version": "0.31.4", "license": "MIT", "_id": "@vitest/utils@0.31.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5cfdcecfd604a7dbe3972cfe0f2b1e0af1246ad2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.31.4.tgz", "fileCount": 14, "integrity": "sha512-DobZbHacWznoGUfYU8XDPY78UubJxXfMNY1+SUdOp1NsI34eopSA6aZMeaGu10waSOeYwE8lxrd/pLfT0RMxjQ==", "signatures": [{"sig": "MEYCIQDNTCPz53hqLQhM0o7wagtUfn0HJYbQC8oNfcXAWyhDPwIhAKn0d3UTDfdxiwvZl4Sf+r1IrQBsDTeb1dWd1RfCEPsH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42335}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.31.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/540a92c44f53832dd192064312311085/vitest-utils-0.31.4.tgz", "_integrity": "sha512-DobZbHacWznoGUfYU8XDPY78UubJxXfMNY1+SUdOp1NsI34eopSA6aZMeaGu10waSOeYwE8lxrd/pLfT0RMxjQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.31.4_1685613297698_0.5859794941167411", "host": "s3://npm-registry-packages"}}, "0.32.0": {"name": "@vitest/utils", "version": "0.32.0", "license": "MIT", "_id": "@vitest/utils@0.32.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ffbc023083e0ef8401a3562086de0ff1b0599635", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.32.0.tgz", "fileCount": 18, "integrity": "sha512-53yXunzx47MmbuvcOPpLaVljHaeSu1G2dHdmy7+9ngMnQIkBQcvwOcoclWFnxDMxFbnq8exAfh3aKSZaK71J5A==", "signatures": [{"sig": "MEUCIQDflHklrjyGdVlZC0sSNmhYK6FyO2HFl7J1EPDGYdE0GAIgc9IeFMI1G2hR9z7EYuh/BzbQUH53FuUJdImknsOFt1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46471}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.32.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/db321a0c71323000ab18e635975139e3/vitest-utils-0.32.0.tgz", "_integrity": "sha512-53yXunzx47MmbuvcOPpLaVljHaeSu1G2dHdmy7+9ngMnQIkBQcvwOcoclWFnxDMxFbnq8exAfh3aKSZaK71J5A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "concordance": "^5.0.4", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.32.0_1686071039479_0.06369585960017177", "host": "s3://npm-registry-packages"}}, "0.32.1": {"name": "@vitest/utils", "version": "0.32.1", "license": "MIT", "_id": "@vitest/utils@0.32.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8938817883aa3213e13e9df80ccbd3ddaacc47c5", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.32.1.tgz", "fileCount": 18, "integrity": "sha512-LPuk++ko9iYhG+Bh8WfgXkr+lXvZ1c8ThofZnO7UV5+N7XvnGMA6B4nqHAY1TeDorQTnsGD/5WHsyQBsbARYtg==", "signatures": [{"sig": "MEUCIC+akKW9Z3IPVQvLqOylo7bUB0SMyRa26dGDwoxQZ6IaAiEA84SvbPqlkl0ew4oh8Jwl0qY4hLXJj4+VcD8GuJtrxwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80877}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.32.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1b8bf78b05979aa14f6907865c06479f/vitest-utils-0.32.1.tgz", "_integrity": "sha512-LPuk++ko9iYhG+Bh8WfgXkr+lXvZ1c8ThofZnO7UV5+N7XvnGMA6B4nqHAY1TeDorQTnsGD/5WHsyQBsbARYtg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^27.5.1", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.32.1_1686918127273_0.5015854759098566", "host": "s3://npm-registry-packages"}}, "0.32.2": {"name": "@vitest/utils", "version": "0.32.2", "license": "MIT", "_id": "@vitest/utils@0.32.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "809c720cafbf4b35ce651deb8570d57785e77819", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.32.2.tgz", "fileCount": 18, "integrity": "sha512-lnJ0T5i03j0IJaeW73hxe2AuVnZ/y1BhhCOuIcl9LIzXnbpXJT9Lrt6brwKHXLOiA7MZ6N5hSJjt0xE1dGNCzQ==", "signatures": [{"sig": "MEUCIQD1t0a6e04evFshCbZp/vAEtQqHberBpzDEmHN1Cq7DmgIgVlzJFRem0OFPO3gVMl4m6GolbyNneIp4dgZ5WJFo4GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80877}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.32.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/838cb0114737d39e2206d8b2f150bc62/vitest-utils-0.32.2.tgz", "_integrity": "sha512-lnJ0T5i03j0IJaeW73hxe2AuVnZ/y1BhhCOuIcl9LIzXnbpXJT9Lrt6brwKHXLOiA7MZ6N5hSJjt0xE1dGNCzQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^27.5.1", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.32.2_1686931535551_0.35090382728116354", "host": "s3://npm-registry-packages"}}, "0.32.3": {"name": "@vitest/utils", "version": "0.32.3", "license": "MIT", "_id": "@vitest/utils@0.32.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5f2046da1dab6a1af496ac629fe93d80a8bbdad6", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.32.3.tgz", "fileCount": 18, "integrity": "sha512-vZ0u4xuEfc2t61V9hV57klOklbKsohmzzXf3DERMkEbJ0YYgdoNwIvwLirul34L2MiRcsB3ewYx8c9oMuM2Zhg==", "signatures": [{"sig": "MEQCIGnxDuA0jIDIcraRaFwN6AYi6/yAONd+zU0LgwJT9D04AiBL4zycFFFmHCXz/hfpHsbrQuuCDbDlg8NtEhLPCoAg9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83392}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.32.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/492a8924a4158b17319a657f95e42b3a/vitest-utils-0.32.3.tgz", "_integrity": "sha512-vZ0u4xuEfc2t61V9hV57klOklbKsohmzzXf3DERMkEbJ0YYgdoNwIvwLirul34L2MiRcsB3ewYx8c9oMuM2Zhg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.32.3_1688373324283_0.05515370219747373", "host": "s3://npm-registry-packages"}}, "0.32.4": {"name": "@vitest/utils", "version": "0.32.4", "license": "MIT", "_id": "@vitest/utils@0.32.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "36283e3aa3f3b1a378e19493c7b3b9107dc4ea71", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.32.4.tgz", "fileCount": 18, "integrity": "sha512-Gwnl8dhd1uJ+HXrYyV0eRqfmk9ek1ASE/LWfTCuWMw+d07ogHqp4hEAV28NiecimK6UY9DpSEPh+pXBA5gtTBg==", "signatures": [{"sig": "MEUCIEVpPFzhQirRJEbV4JbhoAqUuowv3cgYMZgeQTpOknnNAiEAt3WWQy+pB0jCEvim9P4i1FBmx7MVSoZCc3A9e0Pt90c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83392}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.32.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d6898d01aff83a8489168cfad844a95e/vitest-utils-0.32.4.tgz", "_integrity": "sha512-Gwnl8dhd1uJ+HXrYyV0eRqfmk9ek1ASE/LWfTCuWMw+d07ogHqp4hEAV28NiecimK6UY9DpSEPh+pXBA5gtTBg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.32.4_1688382339886_0.46676884605027547", "host": "s3://npm-registry-packages"}}, "0.33.0": {"name": "@vitest/utils", "version": "0.33.0", "license": "MIT", "_id": "@vitest/utils@0.33.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6b9820cb8f128d649da6f78ecaa9b73d6222b277", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.33.0.tgz", "fileCount": 18, "integrity": "sha512-pF1w22ic965sv+EN6uoePkAOTkAPWM03Ri/jXNyMIKBb/XHLDPfhLvf/Fa9g0YECevAIz56oVYXhodLvLQ/awA==", "signatures": [{"sig": "MEYCIQC9AlEjkuJxoMly/eMoAi796mqxy4keRLhVyNC6r15n+wIhAICHKdZP386xVwtRdsYDbaRyniocGXiJWb3sASCfDd3c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83392}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.33.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/7c1b6a2d4b80d093897c07755c4e15bb/vitest-utils-0.33.0.tgz", "_integrity": "sha512-pF1w22ic965sv+EN6uoePkAOTkAPWM03Ri/jXNyMIKBb/XHLDPfhLvf/Fa9g0YECevAIz56oVYXhodLvLQ/awA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "8.19.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/utils_0.33.0_1688652669250_0.036717358588087556", "host": "s3://npm-registry-packages"}}, "0.34.0": {"name": "@vitest/utils", "version": "0.34.0", "license": "MIT", "_id": "@vitest/utils@0.34.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "828bd05e94a70561368343c3c39eba91e7d06180", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.0.tgz", "fileCount": 20, "integrity": "sha512-IktrDLhBKf3dEUUxH+lcHiPnaw952+GdGvoxg99liMscgP6IePf6LuMY7B9dEIHkFunB1R8VMR/wmI/4UGg1aw==", "signatures": [{"sig": "MEQCIB1XeGS3ZnnBxLb+HFfp6SYeHh9w4jxLJbOKKYjqn3e+AiANTLc+tZbrvZfrdMjt09Bi5RX37VXZddFl93pguBmiew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109014}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/361fe0651ad5e16c08c015f4bd153512/vitest-utils-0.34.0.tgz", "_integrity": "sha512-IktrDLhBKf3dEUUxH+lcHiPnaw952+GdGvoxg99liMscgP6IePf6LuMY7B9dEIHkFunB1R8VMR/wmI/4UGg1aw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.0_1690904498737_0.4835352408122009", "host": "s3://npm-registry-packages"}}, "0.34.1": {"name": "@vitest/utils", "version": "0.34.1", "license": "MIT", "_id": "@vitest/utils@0.34.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e5545c6618775fb9a2dae2a80d94fc2f35222233", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.1.tgz", "fileCount": 20, "integrity": "sha512-/ql9dsFi4iuEbiNcjNHQWXBum7aL8pyhxvfnD9gNtbjR9fUKAjxhj4AA3yfLXg6gJpMGGecvtF8Au2G9y3q47Q==", "signatures": [{"sig": "MEYCIQD8hURCXlfzlFR9JzOXpnfdoN1P1z9e1Jc7aRbu6HtQmwIhAKRMX2e7Q87DgBij7LufLpzLubDUDQUjxZprLBnt/4NX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109014}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/cd3735b64badbe9990947b781086ee75/vitest-utils-0.34.1.tgz", "_integrity": "sha512-/ql9dsFi4iuEbiNcjNHQWXBum7aL8pyhxvfnD9gNtbjR9fUKAjxhj4AA3yfLXg6gJpMGGecvtF8Au2G9y3q47Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.1_1690908819813_0.795109606339617", "host": "s3://npm-registry-packages"}}, "0.34.2": {"name": "@vitest/utils", "version": "0.34.2", "license": "MIT", "_id": "@vitest/utils@0.34.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5d291a1b0f5d01be99fd1801d212b837a610c53b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.2.tgz", "fileCount": 20, "integrity": "sha512-Lzw+kAsTPubhoQDp1uVAOP6DhNia1GMDsI9jgB0yMn+/nDaPieYQ88lKqz/gGjSHL4zwOItvpehec9OY+rS73w==", "signatures": [{"sig": "MEQCIEHw2UYBBha9sAtFRtPjnzFdgHcB0y0dfSt8srlwwvJ4AiBen+aocLWrUW/2Ht2tJaDgXs7C5hVCoF+Kpct6ol6s7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109399}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/87c13fb5475815ac0ec0ae5a73e578b8/vitest-utils-0.34.2.tgz", "_integrity": "sha512-Lzw+kAsTPubhoQDp1uVAOP6DhNia1GMDsI9jgB0yMn+/nDaPieYQ88lKqz/gGjSHL4zwOItvpehec9OY+rS73w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.2_1692266983872_0.14269830567027753", "host": "s3://npm-registry-packages"}}, "0.34.3": {"name": "@vitest/utils", "version": "0.34.3", "license": "MIT", "_id": "@vitest/utils@0.34.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6e243189a358b736b9fc0216e6b6979bc857e897", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.3.tgz", "fileCount": 20, "integrity": "sha512-kiSnzLG6m/tiT0XEl4U2H8JDBjFtwVlaE8I3QfGiMFR0QvnRDfYfdP3YvTBWM/6iJDAyaPY6yVQiCTUc7ZzTHA==", "signatures": [{"sig": "MEUCIQCKp/CEG1g26q2NB37rF+8r/xnVjkva6jYjTCBylFJ6GgIgCe3U1p06rzSMzo51U8HGxOCX7gvQ+FD6Opj6f/G71Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109399}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/abe1d3af80763a83abf454577551bea1/vitest-utils-0.34.3.tgz", "_integrity": "sha512-kiSnzLG6m/tiT0XEl4U2H8JDBjFtwVlaE8I3QfGiMFR0QvnRDfYfdP3YvTBWM/6iJDAyaPY6yVQiCTUc7ZzTHA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.3_1692948603188_0.06436261528155551", "host": "s3://npm-registry-packages"}}, "0.34.4": {"name": "@vitest/utils", "version": "0.34.4", "license": "MIT", "_id": "@vitest/utils@0.34.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0b6bf5fe07223ebb6ec24cd1edc0137cb301ecfd", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.4.tgz", "fileCount": 20, "integrity": "sha512-yR2+5CHhp/K4ySY0Qtd+CAL9f5Yh1aXrKfAT42bq6CtlGPh92jIDDDSg7ydlRow1CP+dys4TrOrbELOyNInHSg==", "signatures": [{"sig": "MEYCIQDytRc32ibPWI1jn1GLBIfUFCIYFIWP8rDL5AxPGHTaBgIhANNDKaYixLVTyu4HbG4Stx2Hka40ZdQIsQjsFUWiHjG/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109399}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5d41ecc3a3e6fc3be362fac18c6be780/vitest-utils-0.34.4.tgz", "_integrity": "sha512-yR2+5CHhp/K4ySY0Qtd+CAL9f5Yh1aXrKfAT42bq6CtlGPh92jIDDDSg7ydlRow1CP+dys4TrOrbELOyNInHSg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.4_1694169251739_0.22654859580648057", "host": "s3://npm-registry-packages"}}, "0.34.5": {"name": "@vitest/utils", "version": "0.34.5", "license": "MIT", "_id": "@vitest/utils@0.34.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2178fdbc36524d25b8d846b3d408962e1771e83a", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.5.tgz", "fileCount": 21, "integrity": "sha512-ur6CmmYQoeHMwmGb0v+qwkwN3yopZuZyf4xt1DBBSGBed8Hf9Gmbm/5dEWqgpLPdRx6Av6jcWXrjcKfkTzg/pw==", "signatures": [{"sig": "MEUCIEre72J5q7K3cYoCdJHdn1vFD7UaX89hKhSkcxqRZ0zrAiEA7OSl975kk8DJH+Y4+y/v2SAbPRNIe1GmPDn4hAYfsO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109709}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/180c7984a81df3d2ff1d5609a29c3c0b/vitest-utils-0.34.5.tgz", "_integrity": "sha512-ur6CmmYQoeHMwmGb0v+qwkwN3yopZuZyf4xt1DBBSGBed8Hf9Gmbm/5dEWqgpLPdRx6Av6jcWXrjcKfkTzg/pw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.5_1695304223758_0.3410405553937561", "host": "s3://npm-registry-packages"}}, "0.34.6": {"name": "@vitest/utils", "version": "0.34.6", "license": "MIT", "_id": "@vitest/utils@0.34.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "38a0a7eedddb8e7291af09a2409cb8a189516968", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.6.tgz", "fileCount": 21, "integrity": "sha512-IG5aDD8S6zlvloDsnzHw0Ut5xczlF+kv2BOTo+iXfPr54Yhi5qbVOgGB1hZaVq4iJ4C/MZ2J0y15IlsV/ZcI0A==", "signatures": [{"sig": "MEUCIBrAY83rWZW8gQkCb4yEv0BAZYn2o9CODs9+kuluNMd+AiEA0zDuxFk0YwmHaSlfu+DsL/Wjyibe8ua1dM1IXDttXnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109709}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/697f13890602754d0c9f9bd238bc006d/vitest-utils-0.34.6.tgz", "_integrity": "sha512-IG5aDD8S6zlvloDsnzHw0Ut5xczlF+kv2BOTo+iXfPr54Yhi5qbVOgGB1hZaVq4iJ4C/MZ2J0y15IlsV/ZcI0A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.6_1695972808501_0.7197760123757524", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "@vitest/utils", "version": "1.0.0-beta.0", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4135e45fd804523ccddce8d21b6dce49e7b254eb", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.0.tgz", "fileCount": 21, "integrity": "sha512-6mREW4SAEN0QPn4bQT2simhBMM5a0/WaGXADtKPz/UV3sUojp3OK0qbgTHaJOK5wYD6jTY7YH9lNIbE2BWEiGQ==", "signatures": [{"sig": "MEUCIQD6LA5m3p3ym1Y0YYNdNWCET1xSwit12RbeETnJ++IAnQIgX5ExpTNEsAzn7Dy5Q2iLPdB1uy7wXV10rTCCGBBSRek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109715}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1952764d63c11d86fe8273d0adfc5cac/vitest-utils-1.0.0-beta.0.tgz", "_integrity": "sha512-6mREW4SAEN0QPn4bQT2simhBMM5a0/WaGXADtKPz/UV3sUojp3OK0qbgTHaJOK5wYD6jTY7YH9lNIbE2BWEiGQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.0_1696264814064_0.6308937214392747", "host": "s3://npm-registry-packages"}}, "0.34.7": {"name": "@vitest/utils", "version": "0.34.7", "license": "MIT", "_id": "@vitest/utils@0.34.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "46d0d27cd0f6ca1894257d4e141c5c48d7f50295", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-0.34.7.tgz", "fileCount": 21, "integrity": "sha512-z<PERSON><PERSON>avQLpCYS9sLOorGrFFKmy2gnfiNU0ZJ15TsMz/K92NAPS/rp9K4z6AJQQk5Y8adCy4Iwpxy7pQumQ/psnRg==", "signatures": [{"sig": "MEUCIQC4986en1L72hgy4v7SQ5vZ+rl/+ovukZCYnAooj7T1xQIgBifItqKYc3Dc4J3h2D76nslhhzD5gP0djuCWe/mAnUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109709}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-0.34.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/88077b7162350d08af12147a08ddc680/vitest-utils-0.34.7.tgz", "_integrity": "sha512-z<PERSON><PERSON>avQLpCYS9sLOorGrFFKmy2gnfiNU0ZJ15TsMz/K92NAPS/rp9K4z6AJQQk5Y8adCy4Iwpxy7pQumQ/psnRg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_0.34.7_1696266189385_0.4246597302681374", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "@vitest/utils", "version": "1.0.0-beta.1", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c1302ec44845df2a18ca9c785fe303ef3a764198", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.1.tgz", "fileCount": 21, "integrity": "sha512-jfcqEzZamUA2Th76NVOHyRMAUIAuxMG9q+BstUbxzPQ9IkbCjz5GkhagPfy4r6zTFiElhX7mmPVj/nvB/Ea/bQ==", "signatures": [{"sig": "MEUCIQCSF1EpW8TECe9xybGeyCBOY7iVmvIceXOcxL75Ep0PSAIgZ+THhaaNwjXWs4llmYWWBq58CB99HFuxkRLdbY6f+08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109715}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b9b5b34f2161936fd220848ddadd5106/vitest-utils-1.0.0-beta.1.tgz", "_integrity": "sha512-jfcqEzZamUA2Th76NVOHyRMAUIAuxMG9q+BstUbxzPQ9IkbCjz5GkhagPfy4r6zTFiElhX7mmPVj/nvB/Ea/bQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.1_1696332678493_0.17562566781437727", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "@vitest/utils", "version": "1.0.0-beta.2", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9f394cac93af8727cec4e8abd171f237b44b4dda", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.2.tgz", "fileCount": 21, "integrity": "sha512-l5SWv83I91FYcJ9/dWLDmhacvl0LSVn4ATM9OcJNVxbx/P8YaTPPY93MI6OWLazTOdbFPOIDno+c9qIvxBTZ+Q==", "signatures": [{"sig": "MEUCIBS2xOHRIdwfxRRD4XwDL42gzk/8lycJLOnarcKUHu5VAiEArZtJUar/yYWv/lzlZmTnOLCxhWmPdy+wbTNJ0bgzN2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8065452e7650e00920ef741a90be0a58/vitest-utils-1.0.0-beta.2.tgz", "_integrity": "sha512-l5SWv83I91FYcJ9/dWLDmhacvl0LSVn4ATM9OcJNVxbx/P8YaTPPY93MI6OWLazTOdbFPOIDno+c9qIvxBTZ+Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.6", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.2_1697138816709_0.7104758608878592", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "@vitest/utils", "version": "1.0.0-beta.3", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "258754559931bba3f0e7f46897c1fc2338e4d168", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.3.tgz", "fileCount": 21, "integrity": "sha512-8j7Xg3A13kt78DqWJC3ludyMsBbKQn/LcX9wcULxZFMGQqmSkUFN8ij6JWFQs67w39T+clLWzPrV+NRxe+Mc9g==", "signatures": [{"sig": "MEUCIBHmFDjxNgHTDm4UcAyOwfXyx3CLB7JzUb47JUlSZJEPAiEAhIUnd//5w6S1cxLI7pYyQYufQDQnFpW7D35m+dVd4kM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109937}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d054fd5ed4b3126cba8864d48ec135bc/vitest-utils-1.0.0-beta.3.tgz", "_integrity": "sha512-8j7Xg3A13kt78DqWJC3ludyMsBbKQn/LcX9wcULxZFMGQqmSkUFN8ij6JWFQs67w39T+clLWzPrV+NRxe+Mc9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.3_1698410726217_0.2313718594606864", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "@vitest/utils", "version": "1.0.0-beta.4", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bc1264bb7b50daef6930657845059eda67935cdd", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.4.tgz", "fileCount": 21, "integrity": "sha512-YY4bhhVqyTxuNwuZJXiCM4/D0Z7Z3H3JDHNM8gXty7EyRUf4iPDQtXzIWe1r4zdTtoFnzFAeMr+891pWlv4SPA==", "signatures": [{"sig": "MEYCIQCC01rVkcwk9FQ4SyCLZNGsKKqvKiZxMuAZy9tePdGsQQIhAJWlelZ1NmaGW8BOPjpCUabPgqjijtkKqSjnVPvxNX5d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109964}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/0ef17b45ad1ee3d5d0c6eff919a9957a/vitest-utils-1.0.0-beta.4.tgz", "_integrity": "sha512-YY4bhhVqyTxuNwuZJXiCM4/D0Z7Z3H3JDHNM8gXty7EyRUf4iPDQtXzIWe1r4zdTtoFnzFAeMr+891pWlv4SPA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.5.0", "diff-sequences": "^29.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.4_1699524801596_0.4134083231281218", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "@vitest/utils", "version": "1.0.0-beta.5", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5b698419ca26d20de5476cfc154b968d4b262065", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.5.tgz", "fileCount": 20, "integrity": "sha512-5ippiVcc6KjnAZiMc5Gz5g1tWTG+21g5scr+cedYC+YxAjqZzOG/ncJuM/Eqq9a+/MAJJc5zOGBcDYl27x62jg==", "signatures": [{"sig": "MEYCIQDIr/C6YgST4w0TadvcSGNmI2SdHFcYezJpNgJv9vMxmgIhANcrvmO5Lu1QncX1jvMaKEEcAMgZIjDE9TGnrljxYhPY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105919}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ac8a3c5d6e438ff2befec080508b513f/vitest-utils-1.0.0-beta.5.tgz", "_integrity": "sha512-5ippiVcc6KjnAZiMc5Gz5g1tWTG+21g5scr+cedYC+YxAjqZzOG/ncJuM/Eqq9a+/MAJJc5zOGBcDYl27x62jg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.5_1700300659094_0.7183828706717688", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "@vitest/utils", "version": "1.0.0-beta.6", "license": "MIT", "_id": "@vitest/utils@1.0.0-beta.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "95d924bbf79a17c0865b54a57a0219a50127c9a6", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0-beta.6.tgz", "fileCount": 20, "integrity": "sha512-PyUf4dTFRLfq6YAdAab39i32O/qhpeCVM7cUpN1au7BrvYI/e43ZDhhHdwbt8zP14l06PNrXd5nTSZPFQwmsVw==", "signatures": [{"sig": "MEUCIGj9LFYryoczAmiVJh87nSpDA4AuSnkob8VnxZRFSqn1AiEA9w6oJt9d2lvOcQ+m80yYWP4JAn80GZmKeIBHBo6hfdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105919}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/be1a30edd6f33300f629461b02313572/vitest-utils-1.0.0-beta.6.tgz", "_integrity": "sha512-PyUf4dTFRLfq6YAdAab39i32O/qhpeCVM7cUpN1au7BrvYI/e43ZDhhHdwbt8zP14l06PNrXd5nTSZPFQwmsVw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0-beta.6_1701192421006_0.5179224312633617", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@vitest/utils", "version": "1.0.0", "license": "MIT", "_id": "@vitest/utils@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ae72a0fefcf76a9ada2c58777d241b9c101474db", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.0.tgz", "fileCount": 20, "integrity": "sha512-r9JhgaP2bUYSnKE9w0aNblCIK8SKpDhXfJgE4TzjDNq3G40Abo5WXJBEKYAteq5p+OWedSFUg6GirNOlH7pN7Q==", "signatures": [{"sig": "MEUCIQCUpI2q/VVyZWGcJyZTVB8/imAEK/Wi+C96CTB4draDowIgFOJLRo6bcRc9Zot31Nr+HdJJEcYMW5GrjLXmE+UwTWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105912}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3a8515eb264522664d3caee4b2855684/vitest-utils-1.0.0.tgz", "_integrity": "sha512-r9JhgaP2bUYSnKE9w0aNblCIK8SKpDhXfJgE4TzjDNq3G40Abo5WXJBEKYAteq5p+OWedSFUg6GirNOlH7pN7Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.0_1701704747019_0.023426576690622536", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vitest/utils", "version": "1.0.1", "license": "MIT", "_id": "@vitest/utils@1.0.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ab2bf6de50845649b252a9d263765ab7f16bd6a2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.1.tgz", "fileCount": 20, "integrity": "sha512-MGPCHkzXbbAyscrhwGzh8uP1HPrTYLWaj1WTDtWSGrpe2yJWLRN9mF9ooKawr6NMOg9vTBtg2JqWLfuLC7Dknw==", "signatures": [{"sig": "MEUCIHCyGxAOq30V049VqFvCxVwI5CzHhREb3oyjJI9/NQ+XAiEAiO/kbr3P1De2hZ1rQFsvTEvRt9BLRae+HZ2tu573qzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105912}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/971f06e7a7e24fca1d7e96ab59fed70d/vitest-utils-1.0.1.tgz", "_integrity": "sha512-MGPCHkzXbbAyscrhwGzh8uP1HPrTYLWaj1WTDtWSGrpe2yJWLRN9mF9ooKawr6NMOg9vTBtg2JqWLfuLC7Dknw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.1_1701713073695_0.01339433607085172", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@vitest/utils", "version": "1.0.2", "license": "MIT", "_id": "@vitest/utils@1.0.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fbc483a62d13a02fa4e2b470fbf565fdd616a242", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.2.tgz", "fileCount": 20, "integrity": "sha512-GPQkGHAnFAP/+seSbB9pCsj339yRrMgILoI5H2sPevTLCYgBq0VRjF8QSllmnQyvf0EontF6KUIt2t5s2SmqoQ==", "signatures": [{"sig": "MEYCIQDUI/pcZnhN5qHBAqw6iQTz6qeoG/dDlHZ7DxrbF/wyEQIhAPwbHYJlQBcm9rA5odwzepY5NSz487EJRKjpHQEbfq2i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105912}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8e3fe8dc447b0c7198587787af45c563/vitest-utils-1.0.2.tgz", "_integrity": "sha512-GPQkGHAnFAP/+seSbB9pCsj339yRrMgILoI5H2sPevTLCYgBq0VRjF8QSllmnQyvf0EontF6KUIt2t5s2SmqoQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.2_1701943963186_0.6443974370305041", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@vitest/utils", "version": "1.0.3", "license": "MIT", "_id": "@vitest/utils@1.0.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ef808ba07084db26bb30b6b480d00d2d05d88752", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.3.tgz", "fileCount": 20, "integrity": "sha512-ddGKC6CVjxwjA+ourSlMD6Emc+PhIH6+d25ISGBOQjryXi2NtKpsBSOt1yDT793c2Tqij8g8BBxe87jam3B95w==", "signatures": [{"sig": "MEUCIQDSKaLqkntd1IYIlbJZ85hh838fT7mZQALcp+mItg+EpgIgCRgNrM59075NJyjK8ZMUWV2VuS5i8eRcfyvp/IzXXpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106016}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/7acec34ece073faacff4affc2a9d938c/vitest-utils-1.0.3.tgz", "_integrity": "sha512-ddGKC6CVjxwjA+ourSlMD6Emc+PhIH6+d25ISGBOQjryXi2NtKpsBSOt1yDT793c2Tqij8g8BBxe87jam3B95w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.3_1702127135126_0.3253101305213537", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@vitest/utils", "version": "1.0.4", "license": "MIT", "_id": "@vitest/utils@1.0.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6e673eaf87a2ff28a12688d17bdbb62cc22bf773", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.0.4.tgz", "fileCount": 20, "integrity": "sha512-gsswWDXxtt0QvtK/y/LWukN7sGMYmnCcv1qv05CsY6cU/Y1zpGX1QuvLs+GO1inczpE6Owixeel3ShkjhYtGfA==", "signatures": [{"sig": "MEUCIQCBK1ILV2er3VmziATYOVFyozlVthySDvohfluedlrZZwIgPH3/D9KHshzYN4Rt9iQzuIYDyUssMVNfo/O8EyF3qEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106016}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ba0a4283ce671ec5808589cae995c389/vitest-utils-1.0.4.tgz", "_integrity": "sha512-gsswWDXxtt0QvtK/y/LWukN7sGMYmnCcv1qv05CsY6cU/Y1zpGX1QuvLs+GO1inczpE6Owixeel3ShkjhYtGfA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.0.4_1702148705159_0.9152778887066846", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@vitest/utils", "version": "1.1.0", "license": "MIT", "_id": "@vitest/utils@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d177a5f41bdb484bbb43c8d73a77ca782df068b5", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.1.0.tgz", "fileCount": 20, "integrity": "sha512-z+s510fKmYz4Y41XhNs3vcuFTFhcij2YF7F8VQfMEYAAUfqQh0Zfg7+w9xdgFGhPf3tX3TicAe+8BDITk6ampQ==", "signatures": [{"sig": "MEUCICuDTiXUZIAe+lwZUMnlVLjWWv92GmOzHtAB6pY3psw3AiEAvPqDCr8OOxUgdgBZtEstrZjKQi3c/5l1eg2xQvMtnVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106016}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a80313d429be5e02eccd55d50445bbc8/vitest-utils-1.1.0.tgz", "_integrity": "sha512-z+s510fKmYz4Y41XhNs3vcuFTFhcij2YF7F8VQfMEYAAUfqQh0Zfg7+w9xdgFGhPf3tX3TicAe+8BDITk6ampQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.1.0_1702994777341_0.8252528792940876", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@vitest/utils", "version": "1.1.1", "license": "MIT", "_id": "@vitest/utils@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "493d1963d917a3ac29fbd4c36c1c31cfd17a7b41", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.1.1.tgz", "fileCount": 20, "integrity": "sha512-E9LedH093vST/JuBSyHLFMpxJKW3dLhe/flUSPFedoyj4wKiFX7Jm8gYLtOIiin59dgrssfmFv0BJ1u8P/LC/A==", "signatures": [{"sig": "MEUCIEO8rAIBghG6emHXPT6iFBT6xIfzhIchwSidjvKPOOP1AiEAmSQeOrTy1d1TBRp+uhTBiUPwMvBfcNTuzE6NXMZHnOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106016}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a59db640bfc74a1c608efdd5f6108246/vitest-utils-1.1.1.tgz", "_integrity": "sha512-E9LedH093vST/JuBSyHLFMpxJKW3dLhe/flUSPFedoyj4wKiFX7Jm8gYLtOIiin59dgrssfmFv0BJ1u8P/LC/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.1.1_1704029866259_0.30506886282744716", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@vitest/utils", "version": "1.1.2", "license": "MIT", "_id": "@vitest/utils@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "00857b16a7535995241dee7aa25f854f2e893e81", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.1.2.tgz", "fileCount": 22, "integrity": "sha512-QrXfDieptshDkTkXnA+HmlVQto1h0jengbkSKcJjlbCMeXbSCr3AcALPPzozRQxEOKvFjqx9WHjljz62uxrGew==", "signatures": [{"sig": "MEQCIHqIBOovaypwPpxk8Lh4BikI+Kgg4Fq27ZfXjfRZhhvDAiB8iZGXZECgXAFjdiWaiJm65tww+2M/7EbYZAYxnTLttg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135110}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4a370f61737ad7e9624380625ccd5bbc/vitest-utils-1.1.2.tgz", "_integrity": "sha512-QrXfDieptshDkTkXnA+HmlVQto1h0jengbkSKcJjlbCMeXbSCr3AcALPPzozRQxEOKvFjqx9WHjljz62uxrGew==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.1.2_1704387509898_0.8069239932700749", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@vitest/utils", "version": "1.1.3", "license": "MIT", "_id": "@vitest/utils@1.1.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1f82122f916b0b6feb5e85fc854cfa1fbd522b55", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.1.3.tgz", "fileCount": 22, "integrity": "sha512-Dyt3UMcdElTll2H75vhxfpZu03uFpXRCHxWnzcrFjZxT1kTbq8ALUYIeBgGolo1gldVdI0YSlQRacsqxTwNqwg==", "signatures": [{"sig": "MEUCIQC5iZ8XgIiZFURILyUHzYCecEIHpjXzcP/lmK+pUpjFTAIgAaueGTBUggaAJu5fWRtDSjl5Fw8qVe27z70ZxnFXUKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135110}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/540110528dba170470fd83e5aadedcbb/vitest-utils-1.1.3.tgz", "_integrity": "sha512-Dyt3UMcdElTll2H75vhxfpZu03uFpXRCHxWnzcrFjZxT1kTbq8ALUYIeBgGolo1gldVdI0YSlQRacsqxTwNqwg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "9.6.7", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.1.3_1704442731119_0.5048497764252089", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@vitest/utils", "version": "1.2.0", "license": "MIT", "_id": "@vitest/utils@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "deb9bdc3d094bf47f93a592a6a0b3946aa575e7a", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.2.0.tgz", "fileCount": 22, "integrity": "sha512-FyD5bpugsXlwVpTcGLDf3wSPYy8g541fQt14qtzo8mJ4LdEpDKZ9mQy2+qdJm2TZRpjY5JLXihXCgIxiRJgi5g==", "signatures": [{"sig": "MEUCIQCDgoSDFTyjY9hVzt5+FLfYFvP4KAkFdzdWlwkVe6CYEAIgCk7pd1xPm4WNKeB9Et/tSy/TZVs68kdsm8givp0hr1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152605}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/518804b76006ae31d060d8e5cea24bdf/vitest-utils-1.2.0.tgz", "_integrity": "sha512-FyD5bpugsXlwVpTcGLDf3wSPYy8g541fQt14qtzo8mJ4LdEpDKZ9mQy2+qdJm2TZRpjY5JLXihXCgIxiRJgi5g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.3", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.20"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.2.0_1705075623788_0.8075053167847606", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@vitest/utils", "version": "1.2.1", "license": "MIT", "_id": "@vitest/utils@1.2.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ad798cb13ec9e9e97b13be65d135e9e8e3c586aa", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.2.1.tgz", "fileCount": 22, "integrity": "sha512-bsH6WVZYe/J2v3+81M5LDU8kW76xWObKIURpPrOXm2pjBniBu2MERI/XP60GpS4PHU3jyK50LUutOwrx4CyHUg==", "signatures": [{"sig": "MEYCIQDcpgO4fLhizYMgH8lARNYEHddq/F32Rtg1oH28dL09MwIhAJP3eJdRmj39h5w1XRSJAcCH5iqq/HyWEulTT1nqY+Vt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152469}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/40530e28051957c6382567e9f06dc808/vitest-utils-1.2.1.tgz", "_integrity": "sha512-bsH6WVZYe/J2v3+81M5LDU8kW76xWObKIURpPrOXm2pjBniBu2MERI/XP60GpS4PHU3jyK50LUutOwrx4CyHUg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.3", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.21"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.2.1_1705508625641_0.6880905731914484", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@vitest/utils", "version": "1.2.2", "license": "MIT", "_id": "@vitest/utils@1.2.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "94b5a1bd8745ac28cf220a99a8719efea1bcfc83", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.2.2.tgz", "fileCount": 22, "integrity": "sha512-WKITBHLsBHlpjnDQahr+XK6RE7MiAsgrIkr0pGhQ9ygoxBfUeG0lUG5iLlzqjmKSlBv3+j5EGsriBzh+C3Tq9g==", "signatures": [{"sig": "MEYCIQC8fX//qgcXhin/VMshxfUNugPkIYMFrAdkLQIQOP5EIwIhAIpzFuaAF1bil9kM0HWIw4RloXp4443n9XLMnikOtqLK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152469}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8e7f1b2989a1da62e9c4e4c6d311effd/vitest-utils-1.2.2.tgz", "_integrity": "sha512-WKITBHLsBHlpjnDQahr+XK6RE7MiAsgrIkr0pGhQ9ygoxBfUeG0lUG5iLlzqjmKSlBv3+j5EGsriBzh+C3Tq9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.3", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.2.2_1706286337809_0.9959697863510557", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@vitest/utils", "version": "1.3.0", "license": "MIT", "_id": "@vitest/utils@1.3.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7d46aa00617c1720b075eaeb4c4979a712d86c8e", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.3.0.tgz", "fileCount": 22, "integrity": "sha512-/LibEY/fkaXQufi4GDlQZhikQsPO2entBKtfuyIpr1jV4DpaeasqkeHjhdOhU24vSHshcSuEyVlWdzvv2XmYCw==", "signatures": [{"sig": "MEYCIQCOEYJp6wT/WiBwmEgIG5SMhl9i82PsAwB+sq8gnl1N+QIhAKnBFfdv8wGKTADfVHqJTqsTrSO6597j3Y8Nkts+IT68", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152485}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.3.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/de2ae31702a33dc2e91996bae93d69f8/vitest-utils-1.3.0.tgz", "_integrity": "sha512-/LibEY/fkaXQufi4GDlQZhikQsPO2entBKtfuyIpr1jV4DpaeasqkeHjhdOhU24vSHshcSuEyVlWdzvv2XmYCw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.3", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.3.0_1708104538836_0.17465232226037775", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@vitest/utils", "version": "1.3.1", "license": "MIT", "_id": "@vitest/utils@1.3.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7b05838654557544f694a372de767fcc9594d61a", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.3.1.tgz", "fileCount": 22, "integrity": "sha512-d3Waie/299qqRyHTm2DjADeTaNdNSVsnwHPWrs20JMpjh6eiVq7ggggweO8rc4arhf6rRkWuHKwvxGvejUXZZQ==", "signatures": [{"sig": "MEUCIQD/sbnK7VHCUeiqoWChmvY7IMNUwsLey1PQUzEZHeaxWgIgNS8rbqaW0u6X7Nr83q34gNbDuRkropUyGVET8UZGhB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 152384}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.3.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d73420811180e6faca1d292bbcc2df8a/vitest-utils-1.3.1.tgz", "_integrity": "sha512-d3Waie/299qqRyHTm2DjADeTaNdNSVsnwHPWrs20JMpjh6eiVq7ggggweO8rc4arhf6rRkWuHKwvxGvejUXZZQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.4", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.3.1_1708436898952_0.23735215486289274", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@vitest/utils", "version": "1.4.0", "license": "MIT", "_id": "@vitest/utils@1.4.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ea6297e0d329f9ff0a106f4e1f6daf3ff6aad3f0", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.4.0.tgz", "fileCount": 22, "integrity": "sha512-mx3Yd1/6e2Vt/PUC98DcqTirtfxUyAZ32uK82r8rZzbtBeBo+nqgnjx/LvqQdWsrvNtm14VmurNgcf4nqY5gJg==", "signatures": [{"sig": "MEYCIQC/OjX6sy5/WZ//UcVPk36YcEJRo7IsjbFnwHwGisgFiQIhAIm3qt3nbbuex5EZ12P/ckKvRbuwsTUBsC9tvwy8YBE1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 152384}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.4.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/92c11bc1c799a34369fa251c96a39908/vitest-utils-1.4.0.tgz", "_integrity": "sha512-mx3Yd1/6e2Vt/PUC98DcqTirtfxUyAZ32uK82r8rZzbtBeBo+nqgnjx/LvqQdWsrvNtm14VmurNgcf4nqY5gJg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.2.4", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.1", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.4.0_1710498640546_0.8357321528431023", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@vitest/utils", "version": "1.5.0", "license": "MIT", "_id": "@vitest/utils@1.5.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "90c9951f4516f6d595da24876b58e615f6c99863", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.5.0.tgz", "fileCount": 22, "integrity": "sha512-BDU0GNL8MWkRkSRdNFvCUCAVOeHaUlVJ9Tx0TYBZyXaaOTmGtUFObzchCivIBrIwKzvZA7A9sCejVhXM2aY98A==", "signatures": [{"sig": "MEUCIBs8iDQ6khuNtrKyq3PaK+rN4PVAN78eqiXjWBdDmLbZAiEAqnmt4eBX7UsUsyMbKlDFckCSzW8K2wP3eTKOofPJi/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154519}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bee5a17cfb61c42f2e1cf17456a08935/vitest-utils-1.5.0.tgz", "_integrity": "sha512-BDU0GNL8MWkRkSRdNFvCUCAVOeHaUlVJ9Tx0TYBZyXaaOTmGtUFObzchCivIBrIwKzvZA7A9sCejVhXM2aY98A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.1", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.5.0_1712857672046_0.8457249276331007", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@vitest/utils", "version": "1.5.1", "license": "MIT", "_id": "@vitest/utils@1.5.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b373a1f5274b2b8b52ce8d6023db5e006f92d770", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.5.1.tgz", "fileCount": 22, "integrity": "sha512-92pE17bBXUxA0Y7goPcvnATMCuq4NQLOmqsG0e2BtzRi7KLwZB5jpiELi/8ybY8IQNWemKjSD5rMoO7xTdv8ug==", "signatures": [{"sig": "MEQCID7v5dUt4jHRSRUkm51Jz1YMXQZDsXy+RZ9eS5DJrxOIAiB7fuNUjLt8efuOxON5lHYRHrcJXsCK2Zh8yeDlkhsmHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154519}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.5.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/67fe9d2c576f3bad0dfae258fb92d931/vitest-utils-1.5.1.tgz", "_integrity": "sha512-92pE17bBXUxA0Y7goPcvnATMCuq4NQLOmqsG0e2BtzRi7KLwZB5jpiELi/8ybY8IQNWemKjSD5rMoO7xTdv8ug==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.5.1_1713957739656_0.23242759821783743", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@vitest/utils", "version": "1.5.2", "license": "MIT", "_id": "@vitest/utils@1.5.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6a314daa8400a242b5509908cd8977a7bd66ef65", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.5.2.tgz", "fileCount": 22, "integrity": "sha512-sWOmyofuXLJ85VvXNsroZur7mOJGiQeM0JN3/0D1uU8U9bGFM69X1iqHaRXl6R8BwaLY6yPCogP257zxTzkUdA==", "signatures": [{"sig": "MEUCIQC0k6dCdybh8vgMnnlE286IBPuiJ+GqVeU8C6E2KS/cLQIgdPq9so3kMPGr27MIhVl1cSRnYNhFHm05enWpV89XU6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154519}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.5.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dcd3d44a681aec53da78f451f73e51e1/vitest-utils-1.5.2.tgz", "_integrity": "sha512-sWOmyofuXLJ85VvXNsroZur7mOJGiQeM0JN3/0D1uU8U9bGFM69X1iqHaRXl6R8BwaLY6yPCogP257zxTzkUdA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.5.2_1714036315952_0.6731489310421208", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@vitest/utils", "version": "1.5.3", "license": "MIT", "_id": "@vitest/utils@1.5.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "216068c28db577480ca77e0b2094f0b1fa29bbcd", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.5.3.tgz", "fileCount": 22, "integrity": "sha512-rE9DTN1BRhzkzqNQO+kw8ZgfeEBCLXiHJwetk668shmNBpSagQxneT5eSqEBLP+cqSiAeecvQmbpFfdMyLcIQA==", "signatures": [{"sig": "MEUCIHwrR66cX1JQSPSmTn88JLhUW+CMeJr824LWe5V08hY6AiEA4h6rLnijYM6RVm0Y0ifmLFFcEwznL+rf8VNOWEMyyG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154519}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.5.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/292c6d96152db9172ad23a1b14be6304/vitest-utils-1.5.3.tgz", "_integrity": "sha512-rE9DTN1BRhzkzqNQO+kw8ZgfeEBCLXiHJwetk668shmNBpSagQxneT5eSqEBLP+cqSiAeecvQmbpFfdMyLcIQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.5.3_1714466421600_0.503152607848546", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@vitest/utils", "version": "1.6.0", "license": "MIT", "_id": "@vitest/utils@1.6.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5c5675ca7d6f546a7b4337de9ae882e6c57896a1", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.6.0.tgz", "fileCount": 22, "integrity": "sha512-21c<PERSON>iuGMoMZwiOHa2i4LXkMkMkCGzA+MVFV70jRwHo95dL4x/ts5GZhML1QWuy7yfp3WzK3lRvZi3JnXTYqrBw==", "signatures": [{"sig": "MEUCIQCSOxV6hdA6xRWdO7xpKiUx+UufazA4TgackDtDr3r9EQIgLWaBjx9RI+s2pdrllAAMwTVkUWaQk45VFC1agFS6GL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154584}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.6.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6ca09745f5a600f89050579be5e519ed/vitest-utils-1.6.0.tgz", "_integrity": "sha512-21c<PERSON>iuGMoMZwiOHa2i4LXkMkMkCGzA+MVFV70jRwHo95dL4x/ts5GZhML1QWuy7yfp3WzK3lRvZi3JnXTYqrBw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.6.0_1714749727200_0.5987503666608431", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "@vitest/utils", "version": "2.0.0-beta.1", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8d1acc99c55b399f15463808b65c9a8ad074d8c2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.1.tgz", "fileCount": 22, "integrity": "sha512-3dYcKkMwV6d2z2CXfQPW2VCz1+nJqc4kd2Gs1MruIpMr9vc8Vk0h7tiKsodNiC6EkGLH1gcYuUY5nOdm9kxwdg==", "signatures": [{"sig": "MEUCIGBDPnEF7m4NIN80dtdVNeHC1QXAwRmCCuba5xm/n2JMAiEArYBYdDJhatbpLe/hUsIk/oFFpVt20MAGB/BmC8kx4lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154752}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/253e2d7bf2cc00b1113e6a02bb070e0b/vitest-utils-2.0.0-beta.1.tgz", "_integrity": "sha512-3dYcKkMwV6d2z2CXfQPW2VCz1+nJqc4kd2Gs1MruIpMr9vc8Vk0h7tiKsodNiC6EkGLH1gcYuUY5nOdm9kxwdg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^3.1.0", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.1_1715265141900_0.6933952523183691", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "@vitest/utils", "version": "2.0.0-beta.2", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13e49a414e1d26d9820691aaa19f9e9fe2757e2d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.2.tgz", "fileCount": 22, "integrity": "sha512-27AnDugvd5JYhlQ/sfjJYDKaGQPeoFPoH/ZWJrORaz+kt8W6ImBgXvTymcxIX22Jlg9C3XfhFZYiIomd56SfGA==", "signatures": [{"sig": "MEUCIQCe66XCow1evhbd2TT6kk/QnQmddR71kmCFW1bJnJnccQIgDbQU92GOSC8Es33GI/1YxSIej9zwAkUt5/jR8Wa87y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154752}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/787ce3c9a2bc1ed2ae0a9b013a4cf9af/vitest-utils-2.0.0-beta.2.tgz", "_integrity": "sha512-27AnDugvd5JYhlQ/sfjJYDKaGQPeoFPoH/ZWJrORaz+kt8W6ImBgXvTymcxIX22Jlg9C3XfhFZYiIomd56SfGA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^3.1.0", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.2_1715268677004_0.8518674823928889", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "@vitest/utils", "version": "2.0.0-beta.3", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4f5e66623abd6c1a8756fc82f3b88982142c7afd", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.3.tgz", "fileCount": 22, "integrity": "sha512-zVIp7HwPWooG7WKe44QfknrXS7uAGPp0xpGJxDmD+zCGCRhjI94ugv2AtuI1oe5Yf3uGElIVMrY1eBc1LiwbbQ==", "signatures": [{"sig": "MEUCIQC2NZl0vY3B99BMhhA7CZ/S6L8ojQNiDP5pYJHTZelgUgIgD5I0H4gCxN8rZh/S2jK8/MkJJDTuNsZrgtkpiz0JFAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154753}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/961192307151e30c16eddf430be4286e/vitest-utils-2.0.0-beta.3.tgz", "_integrity": "sha512-zVIp7HwPWooG7WKe44QfknrXS7uAGPp0xpGJxDmD+zCGCRhjI94ugv2AtuI1oe5Yf3uGElIVMrY1eBc1LiwbbQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"loupe": "^3.1.0", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.3_1715712275325_0.6710905474264697", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "@vitest/utils", "version": "2.0.0-beta.4", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "733186709bbbaedabbc3545b11e2ed2e2a9b0ced", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.4.tgz", "fileCount": 22, "integrity": "sha512-Brqj7CsfPixKrbP2uPQogP4tEgaciz2d7vFF71Kj2NpObbG9AEKvTo3YJwhz2Ti1fsoFE6kPBIcXU+W3HaBBuw==", "signatures": [{"sig": "MEYCIQCE/5sGCCLlEMV7pmpEAMC8U1fPazqAYSiVwPO8LJUIuQIhAJ6lmSTIdobESLkcQI1Mlp+BfVGAq2JHUYQcVBuHAPNb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154894}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/93503ce0bffa18c31660d1ce9f633751/vitest-utils-2.0.0-beta.4.tgz", "_integrity": "sha512-Brqj7CsfPixKrbP2uPQogP4tEgaciz2d7vFF71Kj2NpObbG9AEKvTo3YJwhz2Ti1fsoFE6kPBIcXU+W3HaBBuw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.4_1717330550012_0.9310153065727518", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "@vitest/utils", "version": "2.0.0-beta.5", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "55dc259c32ed8f76411c623849d1fe667391ac40", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.5.tgz", "fileCount": 22, "integrity": "sha512-VYvlmu6LeQSe3OsnbMTu8zf9lrbxsmN/V4l4Sod6rgun2g2NkUvh6KwDPqmryDM2MOsYFQqPdO8avASHKBlCvA==", "signatures": [{"sig": "MEUCIQD0GKvyZMpsyeiEx8WF91GZn0B1+P6zAmLVQXIy26euZgIgYn35iUkzasZy1Bb+sHWJCKebdgKiS1l3e8LhvR3zRlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154894}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9d3035f7187c7712a33642f161bd194c/vitest-utils-2.0.0-beta.5.tgz", "_integrity": "sha512-VYvlmu6LeQSe3OsnbMTu8zf9lrbxsmN/V4l4Sod6rgun2g2NkUvh6KwDPqmryDM2MOsYFQqPdO8avASHKBlCvA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.5_1717331263556_0.550275677491189", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "@vitest/utils", "version": "2.0.0-beta.6", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6defa34aa1b0afa579b8f61f6adcc451865caeb4", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.6.tgz", "fileCount": 22, "integrity": "sha512-UP/2Tj4U/eN5FoUCltAHg/EGgrvvIehkJaOVQNHQTIdTXo0XA0UijaXLyXwll117FrJ59sTSKVtyWobhcT7dcA==", "signatures": [{"sig": "MEYCIQDkdf3PLOo/qdGIcnCa1t01M2ZVYu1Gi9ziKnF3ut2PEQIhAP+IdK0oGzD3YF+pdMLsKQ2Tz8qcVyGYdfARUf2HIOXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154894}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/85608b16ed76f5acd72af5dc849c29ad/vitest-utils-2.0.0-beta.6.tgz", "_integrity": "sha512-UP/2Tj4U/eN5FoUCltAHg/EGgrvvIehkJaOVQNHQTIdTXo0XA0UijaXLyXwll117FrJ59sTSKVtyWobhcT7dcA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.6_1717355838992_0.40642819261616747", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "@vitest/utils", "version": "2.0.0-beta.7", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "da74fc609ae1ed7ecdb54797b0c9e4a6f0617f69", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.7.tgz", "fileCount": 22, "integrity": "sha512-riosSuQQ8jgvnkRU0w3VeRBlHyMc+8w+DE+vCeaH3/oUUnDeYqGYUtjrhPTl1hB3Tkx2T8fyQ+Ddnph3Mj9l0g==", "signatures": [{"sig": "MEUCIQDWtP3UYZYp1/1yHFtTmDccdurKXb6x6zZd8JU9/qEa0gIgT4BOBpD8I1nuoyVCOk10oVe4mGMRAIRGscRc8wkrHrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155241}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/811554054d0d27ac5ef35eb3d3106248/vitest-utils-2.0.0-beta.7.tgz", "_integrity": "sha512-riosSuQQ8jgvnkRU0w3VeRBlHyMc+8w+DE+vCeaH3/oUUnDeYqGYUtjrhPTl1hB3Tkx2T8fyQ+Ddnph3Mj9l0g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.7_1717414535944_0.9867611887083139", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "@vitest/utils", "version": "2.0.0-beta.8", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.8", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "95d23ab488f55dbde0ac577bcefcc6e0a1548b84", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.8.tgz", "fileCount": 22, "integrity": "sha512-iv1LQCAqt7T3DMJC51H8acJ4+DPMvgWwpSTcyy1pdHqm7eZ3uMTnFAPIy6Df63sHQsiuha79PESYXVwfPRT9tg==", "signatures": [{"sig": "MEUCIQDKyg7PnvVGXFpPnqPQgjYsmFN8M5SA/eDhl3Yc9Bc6jQIgKLpws9pzKdwyRACRkVLkDmlq/3OqPwCrQuCiwkpwgR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155241}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0eee9a74caa26cc1e724a00604df44cf/vitest-utils-2.0.0-beta.8.tgz", "_integrity": "sha512-iv1LQCAqt7T3DMJC51H8acJ4+DPMvgWwpSTcyy1pdHqm7eZ3uMTnFAPIy6Df63sHQsiuha79PESYXVwfPRT9tg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.8_1717504760239_0.09550835591037199", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "@vitest/utils", "version": "2.0.0-beta.9", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.9", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4c45d1e298eec525e7758d2fabcb216aca2dabb1", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.9.tgz", "fileCount": 22, "integrity": "sha512-NcY+BtAy+ChZGYen+Y+hNBAxYSEro/gA38kAbArnr6Yw1QSpyhKUe4Vdv1iG7djke+Zz/5Y1uVum8JAzlzEevQ==", "signatures": [{"sig": "MEQCIDTxG0wulxJHZEXwFJLXWqKxRNE2c3SWZPlWksuaAuznAiAlHf3dAs7V2fJGybj/xY4oqSubxlc3SB0TCSykRSKzsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155241}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3e00f289ffef348840f7a7ea66ddc52c/vitest-utils-2.0.0-beta.9.tgz", "_integrity": "sha512-NcY+BtAy+ChZGYen+Y+hNBAxYSEro/gA38kAbArnr6Yw1QSpyhKUe4Vdv1iG7djke+Zz/5Y1uVum8JAzlzEevQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.5.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.9_1717574456012_0.322357657027909", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "@vitest/utils", "version": "2.0.0-beta.10", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.10", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13bc01f606b0cdeaaa1ef559ad48b37e5723bb01", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.10.tgz", "fileCount": 22, "integrity": "sha512-QfkMTsHtNCav0ejxmJsRloUa1VT46vK0xJFQIA9hpA7rGnuUS3QZmKcbi9NrBRbCgPxk7TrXc2sJu2dk13Uifg==", "signatures": [{"sig": "MEUCIQCLAompzh7QdWXTPMjaR7ARoTmvUlFFFmRa513h7/onpwIgA6GqjtSAvz0FwTksSkyOBOU6pbuO9qjIkoP28UKV7Rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155467}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3ef3d6213f12e6ecb35169765b254db5/vitest-utils-2.0.0-beta.10.tgz", "_integrity": "sha512-QfkMTsHtNCav0ejxmJsRloUa1VT46vK0xJFQIA9hpA7rGnuUS3QZmKcbi9NrBRbCgPxk7TrXc2sJu2dk13Uifg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.10_1718194292079_0.26837687765547535", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "@vitest/utils", "version": "2.0.0-beta.11", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.11", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "180a69bd4568f3f0f27de863c8433912d6842170", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.11.tgz", "fileCount": 22, "integrity": "sha512-97nAwg5UaQdjA0lj0kFdtTd7HrsC8+eKKWsZEZlPdr/nx8MkJmpK2T/1K5hT3/opWBoXCvuYpin/UluyBkYIKg==", "signatures": [{"sig": "MEYCIQCIDsyNx3BTDfw7/YwjRbk3rQmnLsSaiMvMVDcTsK3SEQIhAK9Mvq/2ZAWxp070wE8rAlE4TmEidsKqdFnMJxG1WnKD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157096}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.11.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8f1b813a77403e452018769d0e675710/vitest-utils-2.0.0-beta.11.tgz", "_integrity": "sha512-97nAwg5UaQdjA0lj0kFdtTd7HrsC8+eKKWsZEZlPdr/nx8MkJmpK2T/1K5hT3/opWBoXCvuYpin/UluyBkYIKg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.11_1718828033880_0.7211479735059476", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.12": {"name": "@vitest/utils", "version": "2.0.0-beta.12", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.12", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c6ac81be90fab1449ded3fc832de9e12c623aaea", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.12.tgz", "fileCount": 22, "integrity": "sha512-qjVhzdcGnZeWMOoEDk/wgfuO4f/jcz7MIOpSAr2apxeJoWOZ+4GrV77/3EZ9qBodU4MbXBeHdR5KHdMPfl3kAQ==", "signatures": [{"sig": "MEYCIQClAsspZUsCLroRcO3wRGBjv7vW3KIFDVnwQtZAQx1PVAIhALLdkQ00PRlLs5yoNkF7wAhBi9hGDhWrwsYrEcf34ulT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157154}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.12.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/eb1348a13de8d35a2eb03e0c2a996d51/vitest-utils-2.0.0-beta.12.tgz", "_integrity": "sha512-qjVhzdcGnZeWMOoEDk/wgfuO4f/jcz7MIOpSAr2apxeJoWOZ+4GrV77/3EZ9qBodU4MbXBeHdR5KHdMPfl3kAQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.12_1719346570939_0.22481992567422826", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.13": {"name": "@vitest/utils", "version": "2.0.0-beta.13", "license": "MIT", "_id": "@vitest/utils@2.0.0-beta.13", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a07858c421176e5753662a9b7339dacc51b88d2e", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0-beta.13.tgz", "fileCount": 22, "integrity": "sha512-af+EpEofA39r5pbYxfVQxDv5dLwQxMiLXlwWMbDgjcw97BHNKarh2XEQ2D7AFWViYwC4HvIDc7kP2C/OC0xJHA==", "signatures": [{"sig": "MEUCIQDQqWMz1n/+5+J05Gp+jpgd9hW+fRdkC5iPrhn/6bQLdQIgEDXgftjOwHf94sKO7YyPfzFuUAXQkQE3RToaOOruDBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157287}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0-beta.13.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e9eccb1bd16e3539eaabd55568255170/vitest-utils-2.0.0-beta.13.tgz", "_integrity": "sha512-af+EpEofA39r5pbYxfVQxDv5dLwQxMiLXlwWMbDgjcw97BHNKarh2XEQ2D7AFWViYwC4HvIDc7kP2C/OC0xJHA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0-beta.13_1720101816849_0.5218126643591205", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vitest/utils", "version": "2.0.0", "license": "MIT", "_id": "@vitest/utils@2.0.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "08e6f8937da4f629f322af523f2f89da3c8c1538", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.0.tgz", "fileCount": 22, "integrity": "sha512-t0jbx8VugWEP6A29NbyfQKVU68Vo6oUw0iX3a8BwO3nrZuivfHcFO4Y5UsqXlplX+83P9UaqEvC2YQhspC0JSA==", "signatures": [{"sig": "MEUCIQCRS3Bp+qFnvARbKtqMP9yuqpWXIGeAqLRmrHWKvjLf1QIgMlCuCd32q1hQwAnh+56BnkUDw9P1K7HpSL6Opin6ZNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157279}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4b4599c3522e8e30a67f1404063d666f/vitest-utils-2.0.0.tgz", "_integrity": "sha512-t0jbx8VugWEP6A29NbyfQKVU68Vo6oUw0iX3a8BwO3nrZuivfHcFO4Y5UsqXlplX+83P9UaqEvC2YQhspC0JSA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.0_1720438758774_0.02311728913770872", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vitest/utils", "version": "2.0.1", "license": "MIT", "_id": "@vitest/utils@2.0.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bef005cbe51c900f01c5fe8bffa9adbbd8d97fcc", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.1.tgz", "fileCount": 22, "integrity": "sha512-STH+2fHZxlveh1mpU4tKzNgRk7RZJyr6kFGJYCI5vocdfqfPsQrgVC6k7dBWHfin5QNB4TLvRS0Ckly3Dt1uWw==", "signatures": [{"sig": "MEUCIBlNwGp5xRrx70oudtT0dfhgGbfqpRVoQKRSgDohrMsvAiEAi9iY/9cFAuisGSnNcdLsWET3jU36mYTkCFUrV3QkQGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157279}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2664bd381e3d5e18820b63d6462af340/vitest-utils-2.0.1.tgz", "_integrity": "sha512-STH+2fHZxlveh1mpU4tKzNgRk7RZJyr6kFGJYCI5vocdfqfPsQrgVC6k7dBWHfin5QNB4TLvRS0Ckly3Dt1uWw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"loupe": "^3.1.1", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.1_1720452774000_0.36993930839277156", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@vitest/utils", "version": "2.0.2", "license": "MIT", "_id": "@vitest/utils@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a2e829b126b08987e93e1d105323c7f7b99e271d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.2.tgz", "fileCount": 21, "integrity": "sha512-pxCY1v7kmOCWYWjzc0zfjGTA3Wmn8PKnlPvSrsA643P1NHl1fOyXj2Q9SaNlrlFE+ivCsxM80Ov3AR82RmHCWQ==", "signatures": [{"sig": "MEQCIBnnDE4fNDoR1pOJfaR6wiHn3XtPJI4PhjBowAED6iJUAiAhdIbtYgy/EyYCcRuXTffWmh9dvARapxr2EEwOk6xvPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 179331}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/08efc71f3f4999a50b968693f641f22d/vitest-utils-2.0.2.tgz", "_integrity": "sha512-pxCY1v7kmOCWYWjzc0zfjGTA3Wmn8PKnlPvSrsA643P1NHl1fOyXj2Q9SaNlrlFE+ivCsxM80Ov3AR82RmHCWQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.0.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.2_1720626394100_0.9233237032672015", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/utils", "version": "2.0.3", "license": "MIT", "_id": "@vitest/utils@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3c57f5338e49c91e3c4ac5be8c74ae22a3c2d5b4", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.3.tgz", "fileCount": 21, "integrity": "sha512-c/UdELMuHitQbbc/EVctlBaxoYAwQPQdSNwv7z/vHyBKy2edYZaFgptE27BRueZB7eW8po+cllotMNTDpL3HWg==", "signatures": [{"sig": "MEUCIDUQMAOxsiQmCKoxpulUr48BHv1qjITHfmpKx46iD9H2AiEAldO8OkrGvwTuWw1A3dKJmLiC6vWAcOlzPxe9j52eH2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 179331}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a666691e7c84891b810b88cb573da182/vitest-utils-2.0.3.tgz", "_integrity": "sha512-c/UdELMuHitQbbc/EVctlBaxoYAwQPQdSNwv7z/vHyBKy2edYZaFgptE27BRueZB7eW8po+cllotMNTDpL3HWg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.0.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.3_1721037811992_0.22882936905490348", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/utils", "version": "2.0.4", "license": "MIT", "_id": "@vitest/utils@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2db1df35aaeb5caa932770a190df636a68d284d5", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.4.tgz", "fileCount": 21, "integrity": "sha512-Zc75QuuoJhOBnlo99ZVUkJIuq4Oj0zAkrQ2VzCqNCx6wAwViHEh5Fnp4fiJTE9rA+sAoXRf00Z9xGgfEzV6fzQ==", "signatures": [{"sig": "MEQCIFT3aSIb0972Fj0Gi4ePKrFERGmakemiOU12mX3dr/ONAiBKI7flO3J0XTcPIzrK59YsymJ4tYXh6nUOCm+0pzfMOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 179304}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f1d58abc988e704ad6f3cdb6343f8527/vitest-utils-2.0.4.tgz", "_integrity": "sha512-Zc75QuuoJhOBnlo99ZVUkJIuq4Oj0zAkrQ2VzCqNCx6wAwViHEh5Fnp4fiJTE9rA+sAoXRf00Z9xGgfEzV6fzQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.0.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.4_1721639605862_0.3174895933857398", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/utils", "version": "2.0.5", "license": "MIT", "_id": "@vitest/utils@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6f8307a4b6bc6ceb9270007f73c67c915944e926", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.0.5.tgz", "fileCount": 21, "integrity": "sha512-d8HKbqIcya+GR67mkZbrzhS5kKhtp8dQLcmRZLGTscGVg7yImT82cIrhtn2L8+VujWcy6KZweApgNmPsTAO/UQ==", "signatures": [{"sig": "MEUCIEu0pN2n67mFtVF6/uRaAio8iWk5YPMLrhDCNS5MV65vAiEA8DQBA8F2csHQfR85bRgen/acbhCvw3kS7OtU6zpYvb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182266}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4964b1f2f1e5ad773fd336088c718748/vitest-utils-2.0.5.tgz", "_integrity": "sha512-d8HKbqIcya+GR67mkZbrzhS5kKhtp8dQLcmRZLGTscGVg7yImT82cIrhtn2L8+VujWcy6KZweApgNmPsTAO/UQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.7.0", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.0.5"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.0.5_1722422398073_0.5199974306803865", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/utils", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0e8efe17827a65bc719e18aaf1bfc4a61310b60f", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.1.tgz", "fileCount": 21, "integrity": "sha512-5iY0/ssHPQrgKz7iAHIY7hKIITTwKFlL5iXPoUNW1LPX2ZIl0O2w35yrQ4S2AJLmYcRw6Px5EJKWdo4yPuY5Nw==", "signatures": [{"sig": "MEYCIQCNvvEZLdSQbJ0OsGc++mIeQVn1+GxaVkwNRzKD/GGXAAIhANs3ZVH2P+Znhsfn8gQQ3fb16moHUfUaiIgc2/tULfNJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182280}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/86eb5631ab9948613c272eaf7f44c0c1/vitest-utils-2.1.0-beta.1.tgz", "_integrity": "sha512-5iY0/ssHPQrgKz7iAHIY7hKIITTwKFlL5iXPoUNW1LPX2ZIl0O2w35yrQ4S2AJLmYcRw6Px5EJKWdo4yPuY5Nw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.1_1723011685628_0.18411548672948697", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/utils", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0d64d1587def9e4601f3118dacc1f4f0fe2803fe", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.2.tgz", "fileCount": 21, "integrity": "sha512-48laE3Xq4yjnv86xI/gjRoVxRvyQFy4N6J3P7hD8564HGoleF7rEW3SBBG733XqjBktisq5n10al1nEpbQOrfQ==", "signatures": [{"sig": "MEUCIHfNJqzTfmJIsHPL5CifryFbcfi3XnVuJk9HGYLRB2TJAiEAmzcE/JEEnfmsozv/gJ+aZXtZSxesqGXE8ILip1BeET4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182280}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/61b1dd5f59bf160c673fe5ff04c568b3/vitest-utils-2.1.0-beta.2.tgz", "_integrity": "sha512-48laE3Xq4yjnv86xI/gjRoVxRvyQFy4N6J3P7hD8564HGoleF7rEW3SBBG733XqjBktisq5n10al1nEpbQOrfQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.2_1723017409857_0.505664657690134", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/utils", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9e4766f5662697dcc8fcfdbcbd129aff83bca8af", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.3.tgz", "fileCount": 21, "integrity": "sha512-KmUWRMkcceA/ueLGSSvQ0KZddwueOUTWHbRRtZE5ZTcmDsMMpsxdpB0uqsERePjVPJKViS8qXSFTO9Bz4wK4zg==", "signatures": [{"sig": "MEQCIQCBONoESUf7yxIUZ0RrBDlJ+CwEOm5+CtkXCMfqofrwwAIfMn9fueB/oHoee1IUemPFXvixrhOhsNFI/kpu3fWppQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182280}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8dbe102cc36fccaa9d2c43dacc3992f9/vitest-utils-2.1.0-beta.3.tgz", "_integrity": "sha512-KmUWRMkcceA/ueLGSSvQ0KZddwueOUTWHbRRtZE5ZTcmDsMMpsxdpB0uqsERePjVPJKViS8qXSFTO9Bz4wK4zg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.3_1723018621936_0.473182229945746", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/utils", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "63efbed25f3400f2c16e8e62b73c4a7ede9f715d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.4.tgz", "fileCount": 21, "integrity": "sha512-8PC748g3HRfdz9Xejd0eFx1JDGn7/caR3MdQdtzNYOYM0DIn0ZuOT1zVtDkrk9MmTPLON35hjif5g5nbyXnzYg==", "signatures": [{"sig": "MEYCIQDfnnhgx0qrWMNJzWdt7VqwLH9ZIUyJQk2PN/9pRYlbrQIhALUR8BQZCDVXgUeuZuWrckmaWpbFk5jgNU3gYCItVoWg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182353}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d7e335b51c235431e1ad2512f6f8928a/vitest-utils-2.1.0-beta.4.tgz", "_integrity": "sha512-8PC748g3HRfdz9Xejd0eFx1JDGn7/caR3MdQdtzNYOYM0DIn0ZuOT1zVtDkrk9MmTPLON35hjif5g5nbyXnzYg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.4_1723030971089_0.5661823478515025", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/utils", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7f896510e1b700c4e41be5af4318b64be366db16", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.5.tgz", "fileCount": 21, "integrity": "sha512-qA5vOskxYes4No2nC716NzQKKGZvq1bFqc4dLo4wtccj3UV4mTgvRGu2gJotkln0iWkT+EFug4EPe6CJi6Q1/w==", "signatures": [{"sig": "MEYCIQDjHh9+azYgfWif8/bkiFY6JqDhl9sROqPVd27iVMJ6fwIhALy+2JDyd5hnoIo313omvehj04+J7efgPNDQgAw4qdnM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182353}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e9a26ded88eaf49e4a17229e25c8f918/vitest-utils-2.1.0-beta.5.tgz", "_integrity": "sha512-qA5vOskxYes4No2nC716NzQKKGZvq1bFqc4dLo4wtccj3UV4mTgvRGu2gJotkln0iWkT+EFug4EPe6CJi6Q1/w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.5"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.5_1723462518878_0.5512580856746123", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/utils", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f09f248f55552b47f2723ac781c74e846cc34418", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.6.tgz", "fileCount": 21, "integrity": "sha512-0BAsL8FA1S/oHtD9xOPG3Kl03iNtLBbFYbugppxkMcTlwHXZPDKSGk2WufKHPpsQxwzenecekfisrSs5W/w6kw==", "signatures": [{"sig": "MEYCIQCf865athOBgLj1EJVge72IRRSay2OWBGhFDsbuS0o/3gIhAKOS+ndwhccOH+6A+7tUCHmFrH6Q0+dhlv4hpZHRbtxh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182353}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5ab62f1131c9e2f26cdb1e2181b6e49a/vitest-utils-2.1.0-beta.6.tgz", "_integrity": "sha512-0BAsL8FA1S/oHtD9xOPG3Kl03iNtLBbFYbugppxkMcTlwHXZPDKSGk2WufKHPpsQxwzenecekfisrSs5W/w6kw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.1", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "estree-walker": "^3.0.3", "@vitest/pretty-format": "2.1.0-beta.6"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.6_1724159910976_0.26447441396722926", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/utils", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/utils@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a0aa4f35f95eb9c8ad428a71665edbe72d0ccdb1", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0-beta.7.tgz", "fileCount": 19, "integrity": "sha512-/2khTCwV6CpNFuQvi4VyvkFsrk2Y5joFey20nJHC+vvIjfKsGiyJMEmYYrDHVHUdRRSbbekesMd1AdZV9ioDfg==", "signatures": [{"sig": "MEUCIQCzBQLnTAqTAIuIY0jJ0HjFerSz9bSxLNGv+skWijbSAgIgBrF9KFrZW9lm2yL7JAMDWEuiz+U3QOWJAHBFldxSBNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 153398}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/58bf70b146fd17acd5c0b025cc2547fa/vitest-utils-2.1.0-beta.7.tgz", "_integrity": "sha512-/2khTCwV6CpNFuQvi4VyvkFsrk2Y5joFey20nJHC+vvIjfKsGiyJMEmYYrDHVHUdRRSbbekesMd1AdZV9ioDfg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.0-beta.7"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0-beta.7_1725894793702_0.4156741064147409", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/utils", "version": "2.1.0", "license": "MIT", "_id": "@vitest/utils@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a01060eca9c331a2bde48a0e03972e1c9b821c1c", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.0.tgz", "fileCount": 19, "integrity": "sha512-rreyfVe0PuNqJfKYUwfPDfi6rrp0VSu0Wgvp5WBqJonP+4NvXHk48X6oBam1Lj47Hy6jbJtnMj3OcRdrkTP0tA==", "signatures": [{"sig": "MEUCIQDyTWJsfsuaaATbp2A7d1DcqVQl/de+tRhhcLfwWx+oyAIgOKlP30OjZOkej329PlXalC/iPzS7WCRu1AQa5t6ChHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 153384}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9cccc8702aaa79f411b4ef201ca90a49/vitest-utils-2.1.0.tgz", "_integrity": "sha512-rreyfVe0PuNqJfKYUwfPDfi6rrp0VSu0Wgvp5WBqJonP+4NvXHk48X6oBam1Lj47Hy6jbJtnMj3OcRdrkTP0tA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.0"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.0_1726149801451_0.8695908313821996", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/utils", "version": "2.1.1", "license": "MIT", "_id": "@vitest/utils@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "284d016449ecb4f8704d198d049fde8360cc136e", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.1.tgz", "fileCount": 19, "integrity": "sha512-Y6Q9TsI+qJ2CC0ZKj6VBb+T8UPz593N113nnUykqwANqhgf3QkZeHFlusgKLTqrnVHbj/XDKZcDHol+dxVT+rQ==", "signatures": [{"sig": "MEUCIQCUwJ1pCW5lOQwfk51VxTaPZg5UbADeEfFJ2IxPTCpAHAIgYfez1AleMJebIM0G2a3YtS4+2QJuBA6tFOQ01PQTqLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 153384}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fb44252af263d6417998e3660e954308/vitest-utils-2.1.1.tgz", "_integrity": "sha512-Y6Q9TsI+qJ2CC0ZKj6VBb+T8UPz593N113nnUykqwANqhgf3QkZeHFlusgKLTqrnVHbj/XDKZcDHol+dxVT+rQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.1_1726241555459_0.703813430314054", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/utils", "version": "2.1.2", "license": "MIT", "_id": "@vitest/utils@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "222ac35ba02493173e40581256eb7a62520fcdba", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.2.tgz", "fileCount": 19, "integrity": "sha512-zMO2KdYy6mx56btx9JvAqAZ6EyS3g49krMPPrgOp1yxGZiA93HumGk+bZ5jIZtOg5/VBYl5eBmGRQHqq4FG6uQ==", "signatures": [{"sig": "MEQCIGQ0Jqc3Kyjc7sH9b5ujx7SxAT7f9uut/XmsA2HdttX9AiBcID2IXSt2KwOUEz2jWV/cf0IxkZVmY0duN/dQeMpnxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bde3c901883a6d5fec6830d2f3de9c35/vitest-utils-2.1.2.tgz", "_integrity": "sha512-zMO2KdYy6mx56btx9JvAqAZ6EyS3g49krMPPrgOp1yxGZiA93HumGk+bZ5jIZtOg5/VBYl5eBmGRQHqq4FG6uQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.2_1727886002326_0.9269369904620244", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/utils", "version": "2.1.3", "license": "MIT", "_id": "@vitest/utils@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e52aa5745384091b151cbdf79bb5a3ad2bea88d2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.3.tgz", "fileCount": 19, "integrity": "sha512-xpiVfDSg1RrYT0tX6czgerkpcKFmFOF/gCr30+Mve5V2kewCy4Prn1/NDMSRwaSmT7PRaOF83wu+bEtsY1wrvA==", "signatures": [{"sig": "MEUCIAydnc8QnctDlcYReYArtWoVempUtncvMlKovqY3qmBNAiEAgX1O3cXktViXyt0CsIjuSZ0nIOc3nnQ2yVAR3u+aOWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e233c4c2f936564c903294314097bc3d/vitest-utils-2.1.3.tgz", "_integrity": "sha512-xpiVfDSg1RrYT0tX6czgerkpcKFmFOF/gCr30+Mve5V2kewCy4Prn1/NDMSRwaSmT7PRaOF83wu+bEtsY1wrvA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"loupe": "^3.1.1", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.3_1728903927591_0.5068475306460891", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/utils", "version": "2.1.4", "license": "MIT", "_id": "@vitest/utils@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6d67ac966647a21ce8bc497472ce230de3b64537", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.4.tgz", "fileCount": 19, "integrity": "sha512-MXDnZn0Awl2S86PSNIim5PWXgIAx8CIkzu35mBdSApUip6RFOGXBCf3YFyeEu8n1IHk4bWD46DeYFu9mQlFIRg==", "signatures": [{"sig": "MEQCIEbbQYp5ht5J/dro35LKRlfnhqYeuib9t1uyk22tqGRqAiAaKbHgk09jF3lIex8NPPZDK5ObyUhqQ1qpSgnz1+PmDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156585}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c1a6003442eab9ba87f4c17d2e4ac744/vitest-utils-2.1.4.tgz", "_integrity": "sha512-MXDnZn0Awl2S86PSNIim5PWXgIAx8CIkzu35mBdSApUip6RFOGXBCf3YFyeEu8n1IHk4bWD46DeYFu9mQlFIRg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.4_1730118436188_0.24838873401986938", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/utils", "version": "2.1.5", "license": "MIT", "_id": "@vitest/utils@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0e19ce677c870830a1573d33ee86b0d6109e9546", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.5.tgz", "fileCount": 19, "integrity": "sha512-yfj6Yrp0Vesw2cwJbP+cl04OC+IHFsuQsrsJBL9pyGeQXE56v1UAOQco+SR55Vf1nQzfV0QJg1Qum7AaWUwwYg==", "signatures": [{"sig": "MEUCIHMKmK9mwECu4vvx3Gd/m/Sr3m3C49xE4Mj7IfkqT/w6AiEAvPielY+xMsAPkkw5JXpn1Rv961KGiK8D+V/B5hZsj1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156950}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1c9528b7d710c87d94ce5b36b2f0c310/vitest-utils-2.1.5.tgz", "_integrity": "sha512-yfj6Yrp0Vesw2cwJbP+cl04OC+IHFsuQsrsJBL9pyGeQXE56v1UAOQco+SR55Vf1nQzfV0QJg1Qum7AaWUwwYg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.5"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.5_1731511443489_0.2320912032341056", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/utils", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/utils@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cab969ed03c6b78b9ad7c4b29c41b02a8622b060", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.2.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-2O86KbYxj/i/WvVuTnB6F27efBqDzzGAcSIbMubc4R8BvK6DIWDgQx0JMRTg3840zQinsPg+cgoU5dqbJNSIWg==", "signatures": [{"sig": "MEYCIQDPPwI4XkMLHBq76+TP9DsU7ZO2SA0SnLX66C5YG6bEqwIhANfGM95Eid5mjjlE0B1JBq1GbDwzm3bkNfDI6/V1B1oT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157565}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4955d81813f8ada4ffd2054999ea28dc/vitest-utils-2.2.0-beta.1.tgz", "_integrity": "sha512-2O86KbYxj/i/WvVuTnB6F27efBqDzzGAcSIbMubc4R8BvK6DIWDgQx0JMRTg3840zQinsPg+cgoU5dqbJNSIWg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.2.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.2.0-beta.1_1731518235773_0.030051892506819833", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/utils", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/utils@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a5b7b1adb3417d23c160d720df3cc205c1de3ab3", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.2.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-GImag6HksQwfmXQbHj41yAMpViDvvzDZgVwiLIGNQim6PcDp6bqonTTh8jNbesVo/1TAzdmbPVXVmoNiyK49TQ==", "signatures": [{"sig": "MEUCIF4Y1n8p4gfPT4+fMgBWfU30/9OPwfANLn4kWo4ItkQ5AiEA/Ft9STyH0plo7xKzsVWrYKTz1Q4fNML3s71XGDoXR0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157565}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f887b3bbc321f99792459a21dcf11abb/vitest-utils-2.2.0-beta.2.tgz", "_integrity": "sha512-GImag6HksQwfmXQbHj41yAMpViDvvzDZgVwiLIGNQim6PcDp6bqonTTh8jNbesVo/1TAzdmbPVXVmoNiyK49TQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.2.0-beta.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.2.0-beta.2_1731939485753_0.9627000714791036", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/utils", "version": "2.1.6", "license": "MIT", "_id": "@vitest/utils@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2af6a82c5c45da35ecd322d0568247a6e9c95c5f", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.6.tgz", "fileCount": 19, "integrity": "sha512-ixNkFy3k4vokOUTU2blIUvOgKq/N2PW8vKIjZZYsGJCMX69MRa9J2sKqX5hY/k5O5Gty3YJChepkqZ3KM9LyIQ==", "signatures": [{"sig": "MEYCIQDPGPVkYmRC8ryZaRYszqQ74zXT6m/LGyOGuQm1PKV4AgIhALJ4TPEpUSh4O4fplgebgEnpa0dwXMqMaaUZ01a3q/zl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156950}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9b7c8515296c2b310e1155b6c0696d26/vitest-utils-2.1.6.tgz", "_integrity": "sha512-ixNkFy3k4vokOUTU2blIUvOgKq/N2PW8vKIjZZYsGJCMX69MRa9J2sKqX5hY/k5O5Gty3YJChepkqZ3KM9LyIQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.6"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.6_1732623834450_0.8831262555286807", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/utils", "version": "2.1.7", "license": "MIT", "_id": "@vitest/utils@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e9ca17fcba9b3aaaf74c4e815a4148def585bfd2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.7.tgz", "fileCount": 19, "integrity": "sha512-7gUdvIzCCuIrMZu0WHTvDJo8C1NsUtOqmwmcS3bRHUcfHemj29wmkzLVNuWQD7WHoBD/+I7WIgrnzt7kxR54ow==", "signatures": [{"sig": "MEYCIQC5i0nAfgzNzNo6vrEHXyJoD5TPMMnbmfZ+0S5zpL9P7gIhAN4f4vP/7hQ1CBmzUWBwMu/4aZqcQfKleGkrXx+jns1t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156950}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/386aa305758ec7454e1f592e3f737022/vitest-utils-2.1.7.tgz", "_integrity": "sha512-7gUdvIzCCuIrMZu0WHTvDJo8C1NsUtOqmwmcS3bRHUcfHemj29wmkzLVNuWQD7WHoBD/+I7WIgrnzt7kxR54ow==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.7"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.7_1733132942259_0.3238277102776781", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/utils", "version": "2.1.8", "license": "MIT", "_id": "@vitest/utils@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f8ef85525f3362ebd37fd25d268745108d6ae388", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.8.tgz", "fileCount": 19, "integrity": "sha512-dwSoui6djdwbfFmIgbIjX2ZhIoG7Ex/+xpxyiEgIGzjliY8xGkcpITKTlp6B4MgtGkF2ilvm97cPM96XZaAgcA==", "signatures": [{"sig": "MEUCIC5Cqx9nxSXSiBMtmGm/KbiF/GXMs2hgn4fC5V59CKdlAiEA+ikknP38uw6qKhRvIugwfvNk2zSFGMVQqzglUvXLus0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156950}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4f45f5a756f6266be832741765540d63/vitest-utils-2.1.8.tgz", "_integrity": "sha512-dwSoui6djdwbfFmIgbIjX2ZhIoG7Ex/+xpxyiEgIGzjliY8xGkcpITKTlp6B4MgtGkF2ilvm97cPM96XZaAgcA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.8"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.8_1733150777628_0.26413259255655386", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/utils", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/utils@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "79247ca3f7e92cb738b48d3c55279750f6feec54", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-pxoUqW7lk/3/7iRAI3MqFRMfPpzdEKLn2Yz57TgMMUhP2/Cj3Izm3dXQaH45LhqU6uezi71U/fOLhLgTqMZnpA==", "signatures": [{"sig": "MEUCIQDwbKEbdghBBkOHGNIopiuBQBqiVhDxAzaAkMo14PvnVQIgBJuFxdTaXd2yPg1X8JCp1+oAgXbnvlsLN4SMQB/Q5AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157565}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fc2efeb15d3b400690f6e434a691d8b2/vitest-utils-3.0.0-beta.1.tgz", "_integrity": "sha512-pxoUqW7lk/3/7iRAI3MqFRMfPpzdEKLn2Yz57TgMMUhP2/Cj3Izm3dXQaH45LhqU6uezi71U/fOLhLgTqMZnpA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "3.0.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.0-beta.1_1733420017482_0.17195251994273342", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/utils", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/utils@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "48e84cb3924b86e0c0906508a3d8a8e4430c23a1", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-Jkib9LoI9Xm3gmzwI+9KgEAJVZNgJQFrR1RAyqBN7k9O3qezOTUjqyYBnvyz3UcPywygP1jEjZWBxUKx4ELpxw==", "signatures": [{"sig": "MEYCIQCrlHKNE4Q4V+EBbNp3PE2+P3h0daqpp3aV6WbZR0ArgQIhAIjB3IVd+HIKPv3KxlsSiLU1pzMEki0VRpry8ccBylxl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157837}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/61c02841b085bffeedc116e2cd1a6e0d/vitest-utils-3.0.0-beta.2.tgz", "_integrity": "sha512-Jkib9LoI9Xm3gmzwI+9KgEAJVZNgJQFrR1RAyqBN7k9O3qezOTUjqyYBnvyz3UcPywygP1jEjZWBxUKx4ELpxw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "3.0.0-beta.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.0-beta.2_1733826099109_0.3316672968395542", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/utils", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/utils@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9d203b900685f23d4d1362d296c7632860d2bc6d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.0-beta.3.tgz", "fileCount": 19, "integrity": "sha512-InEoZvpkcUTmg0J08/Phm6MLkQwWpf3RCJoqVuXiHK7OkUYW/EDoT0qfzL4MGo1PWq+UqIlMw93eAkkTf3RMvQ==", "signatures": [{"sig": "MEQCIHLquol8HlNkcskVAi0xR/MWBNM8JPjAG1fG6wkSwoJcAiBYGUMCDcHtyvhDo+fBWTH2Jz5eoMio8qu1oVk5yc36EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 157837}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/475a5880f494a88d32c6fbe334f21d16/vitest-utils-3.0.0-beta.3.tgz", "_integrity": "sha512-InEoZvpkcUTmg0J08/Phm6MLkQwWpf3RCJoqVuXiHK7OkUYW/EDoT0qfzL4MGo1PWq+UqIlMw93eAkkTf3RMvQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "3.0.0-beta.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.0-beta.3_1734712364015_0.7694325301700358", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/utils", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/utils@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fae725ef24e36e3abdb672e560500d7b05b5a555", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.0-beta.4.tgz", "fileCount": 19, "integrity": "sha512-ea90t+ajEQd5+jA60nuE5SemTlogk49T2Ttiq2ct2ZJpsSj4a7QEQBPsvWaqhKtE3+eVIhT4Qp/Mml2qqIMGEg==", "signatures": [{"sig": "MEUCIQCfGSUtaF53YCLWApxO8MWGMFILJRZrONAaMRmzWszXJwIgIZDxiZJk4J3lt8iNb5xV8m1fvuzw++Wv2q7Qip6GDaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 159870}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a55ad67f56273d3b61221fa38aa74892/vitest-utils-3.0.0-beta.4.tgz", "_integrity": "sha512-ea90t+ajEQd5+jA60nuE5SemTlogk49T2Ttiq2ct2ZJpsSj4a7QEQBPsvWaqhKtE3+eVIhT4Qp/Mml2qqIMGEg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "3.0.0-beta.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.0-beta.4_1736346240292_0.6304176211750239", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/utils", "version": "3.0.0", "license": "MIT", "_id": "@vitest/utils@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fd1fd693c7f8b136c02076a33126248fb79a96b2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.0.tgz", "fileCount": 19, "integrity": "sha512-l300v2/4diHyv5ZiQOj6y/H6VbaTWM6i1c2lC3lUZ5nn9rv9C+WneS/wqyaGLwM37reoh/QkrrYMSMKdfnDZpw==", "signatures": [{"sig": "MEYCIQCn+Um/PSStxUcyuEcPlz8XDNnKFYZg2ib1peMxS4ugGAIhAPdG4CtHVcW4F7/+KLr/BdPT3HF81cOdXg4CUH3uwtEN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 159918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a89652b9faa2565cf749fce063c2e000/vitest-utils-3.0.0.tgz", "_integrity": "sha512-l300v2/4diHyv5ZiQOj6y/H6VbaTWM6i1c2lC3lUZ5nn9rv9C+WneS/wqyaGLwM37reoh/QkrrYMSMKdfnDZpw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.0"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.0_1737036457192_0.6358829469553995", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/utils", "version": "3.0.1", "license": "MIT", "_id": "@vitest/utils@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e535916b1e5857badec6f289acb0132493f36bf2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.1.tgz", "fileCount": 19, "integrity": "sha512-i+Gm61rfIeSitPUsu4ZcWqucfb18ShAanRpOG6KlXfd1j6JVK5XxO2Z6lEmfjMnAQRIvvLtJ3JByzDTv347e8w==", "signatures": [{"sig": "MEUCIQCWGUKFHBqPFbk2dTUKqDlFpGslwmd0FGdhVavqepKP6AIgLJQy8IDtxxD8rn5SWi07PS1fHot5xHrmeEuXMcofNaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 159957}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/911a6a77759e285c7d5b911c97a79ba4/vitest-utils-3.0.1.tgz", "_integrity": "sha512-i+Gm61rfIeSitPUsu4ZcWqucfb18ShAanRpOG6KlXfd1j6JVK5XxO2Z6lEmfjMnAQRIvvLtJ3JByzDTv347e8w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.1_1737055965745_0.7606835253161366", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/utils", "version": "3.0.2", "license": "MIT", "_id": "@vitest/utils@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dff19c1d05890c93a2c4d0ff861e65fb81224d52", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.2.tgz", "fileCount": 19, "integrity": "sha512-Qu01ZYZlgHvDP02JnMBRpX43nRaZtNpIzw3C1clDXmn8eakgX6iQVGzTQ/NjkIr64WD8ioqOjkaYRVvHQI5qiw==", "signatures": [{"sig": "MEYCIQDJgv9fycBtE6olO9b0D60rIAK6pthI8bLhN59K83zAiQIhAOYLkEM5uCi3JP0tko9efushHGbmwpnrtFvI81jFbB3R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 159957}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f27daaff0844e330c4d10fea1da9982d/vitest-utils-3.0.2.tgz", "_integrity": "sha512-Qu01ZYZlgHvDP02JnMBRpX43nRaZtNpIzw3C1clDXmn8eakgX6iQVGzTQ/NjkIr64WD8ioqOjkaYRVvHQI5qiw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.2_1737123976130_0.668686751279155", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/utils", "version": "3.0.3", "license": "MIT", "_id": "@vitest/utils@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "25d5a2e0cd0b5529132b76482fd48139ca56c197", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.3.tgz", "fileCount": 19, "integrity": "sha512-f+s8CvyzPtMFY1eZKkIHGhPsQgYo5qCm6O8KZoim9qm1/jT64qBgGpO5tHscNH6BzRHM+edLNOP+3vO8+8pE/A==", "signatures": [{"sig": "MEQCIB2CqUvohfHyjIScp9c3GjxFnvT5WaeyO3Frz3hh8bBqAiAKoaRGs/t+o0wcJCgznAWezzUTC90QddDmcjI/ay0f7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 159957}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2344d7598f3ac834d8f65f45ba3bdda8/vitest-utils-3.0.3.tgz", "_integrity": "sha512-f+s8CvyzPtMFY1eZKkIHGhPsQgYo5qCm6O8KZoim9qm1/jT64qBgGpO5tHscNH6BzRHM+edLNOP+3vO8+8pE/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.3_1737467927390_0.8271103423014676", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/utils", "version": "3.0.4", "license": "MIT", "_id": "@vitest/utils@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9dd2336170097b20a6e5b778bb5ea7786cc56008", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.4.tgz", "fileCount": 19, "integrity": "sha512-8BqC1ksYsHtbWH+DfpOAKrFw3jl3Uf9J7yeFh85Pz52IWuh1hBBtyfEbRNNZNjl8H8A5yMLH9/t+k7HIKzQcZQ==", "signatures": [{"sig": "MEYCIQD6EU69aOy37F+nAZMJdI4fkybrPGG9PATS6rQEtbjVZwIhAL1hPqLnBdkGC5tTMfnYoYFoLKHC7ZoIbl1oreoFXSkK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 160023}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d62fd8ff5eedcbfe07b5082b4b3107e4/vitest-utils-3.0.4.tgz", "_integrity": "sha512-8BqC1ksYsHtbWH+DfpOAKrFw3jl3Uf9J7yeFh85Pz52IWuh1hBBtyfEbRNNZNjl8H8A5yMLH9/t+k7HIKzQcZQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.4_1737639704531_0.06683906175947141", "host": "s3://npm-registry-packages-npm-production"}}, "1.6.1": {"name": "@vitest/utils", "version": "1.6.1", "license": "MIT", "_id": "@vitest/utils@1.6.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6d2f36cb6d866f2bbf59da854a324d6bf8040f17", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-1.6.1.tgz", "fileCount": 22, "integrity": "sha512-jOrrUvXM4Av9ZWiG1EajNto0u96kWAhJ1LmPmJhXXQx/32MecEKd10pOLYgS2BQx1TgkGhloPU1ArDW2vvaY6g==", "signatures": [{"sig": "MEYCIQC3LW9q7795RFeR00kO1cmkpYl3mt0rprrkC7sYCJ6/mAIhAOdzVUzsDn7bRJuHQZkVxJrvl0Avbk12NYfDzPC4nHpC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 154584}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-1.6.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/055b166c8da94331661323b45bad08fb/vitest-utils-1.6.1.tgz", "_integrity": "sha512-jOrrUvXM4Av9ZWiG1EajNto0u96kWAhJ1LmPmJhXXQx/32MecEKd10pOLYgS2BQx1TgkGhloPU1ArDW2vvaY6g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"loupe": "^2.3.7", "estree-walker": "^3.0.3", "pretty-format": "^29.7.0", "diff-sequences": "^29.6.3"}, "typesVersions": {"*": {"source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.5", "tinyhighlight": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.22"}, "_npmOperationalInternal": {"tmp": "tmp/utils_1.6.1_1738589766088_0.7028151067526918", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/utils", "version": "2.1.9", "license": "MIT", "_id": "@vitest/utils@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4f2486de8a54acf7ecbf2c5c24ad7994a680a6c1", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-2.1.9.tgz", "fileCount": 19, "integrity": "sha512-v0psaMSkNJ3A2NMrUEHFRzJtDPFn+/VWZ5WxImB21T9fjucJRmS7xCS3ppEnARb9y11OAzaD+P2Ps+b+BGX5iQ==", "signatures": [{"sig": "MEQCIARNjSPrpBdzf+B7gvmUk0nh4sflkKGZyl91ndB8xo+sAiABipUo8To1RcxKCVMY/q/txd3MO3agGMTV5oylvsIqiA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 156950}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-2.1.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bc2020f845ddeb6663ca8b0992873235/vitest-utils-2.1.9.tgz", "_integrity": "sha512-v0psaMSkNJ3A2NMrUEHFRzJtDPFn+/VWZ5WxImB21T9fjucJRmS7xCS3ppEnARb9y11OAzaD+P2Ps+b+BGX5iQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^1.2.0", "@vitest/pretty-format": "2.1.9"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_2.1.9_1738590254842_0.5499392701770074", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/utils", "version": "3.0.5", "license": "MIT", "_id": "@vitest/utils@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "dc3eaefd3534598917e939af59d9a9b6a5be5082", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.5.tgz", "fileCount": 19, "integrity": "sha512-N9AX0NUoUtVwKwy21JtwzaqR5L5R5A99GAbrHfCCXK1lp593i/3AZAXhSP43wRQuxYsflrdzEfXZFo1reR1Nkg==", "signatures": [{"sig": "MEUCICx1/gL626eYJzrAN2I/ZUNdga07FpX1WOZ822cEXOxKAiEAnVrb6TBSn5E6k19fISjadwqwbkc57YXgXACgqrGGDl0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 160023}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/289a64b29df92cee841278541257750a/vitest-utils-3.0.5.tgz", "_integrity": "sha512-N9AX0NUoUtVwKwy21JtwzaqR5L5R5A99GAbrHfCCXK1lp593i/3AZAXhSP43wRQuxYsflrdzEfXZFo1reR1Nkg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"loupe": "^3.1.2", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.5"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.5_1738591323153_0.5564601307675858", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/utils", "version": "3.0.6", "license": "MIT", "_id": "@vitest/utils@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "be4246ab46a076db1e49b9e0479bdd9a7f65782f", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.6.tgz", "fileCount": 19, "integrity": "sha512-18ktZpf4GQFTbf9jK543uspU03Q2qya7ZGya5yiZ0Gx0nnnalBvd5ZBislbl2EhLjM8A8rt4OilqKG7QwcGkvQ==", "signatures": [{"sig": "MEUCIGtjGr73dITJU5yvwxbqfX1u0RUrctd2wqAQBffaVbvUAiEAvkocDwXjMvfyKxIXHIAXq9YIoMV/IBXKQuXd3gKhaWk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158286}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/534b622335ee154acf128bcfdab681b8/vitest-utils-3.0.6.tgz", "_integrity": "sha512-18ktZpf4GQFTbf9jK543uspU03Q2qya7ZGya5yiZ0Gx0nnnalBvd5ZBislbl2EhLjM8A8rt4OilqKG7QwcGkvQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.6"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.6_1739885924530_0.382364234914496", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/utils", "version": "3.0.7", "license": "MIT", "_id": "@vitest/utils@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "56268acac1027ead938150eceb2527c61d2fa204", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.7.tgz", "fileCount": 19, "integrity": "sha512-xePVpCRfooFX3rANQjwoditoXgWb1MaFbzmGuPP59MK6i13mrnDw/yEIyJudLeW6/38mCNcwCiJIGmpDPibAIg==", "signatures": [{"sig": "MEUCIDVpxp3l65yhNMNBQPj21a4wF8RPM7sHFU0RPqxaXJ3aAiEAuVswZVuMkeObIEnx3fQdxMUQaw2aDLYTgeWWrPO89EU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158286}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c32f296678adfa25994859f6a5dd63da/vitest-utils-3.0.7.tgz", "_integrity": "sha512-xePVpCRfooFX3rANQjwoditoXgWb1MaFbzmGuPP59MK6i13mrnDw/yEIyJudLeW6/38mCNcwCiJIGmpDPibAIg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.7"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.7_1740419450852_0.6117319315598635", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/utils", "version": "3.0.8", "license": "MIT", "_id": "@vitest/utils@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "289277fbd8e733dff69cfa993c34665415c9b66b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.8.tgz", "fileCount": 19, "integrity": "sha512-nkBC3aEhfX2PdtQI/QwAWp8qZWwzASsU4Npbcd5RdMPBSSLCpkZp52P3xku3s3uA0HIEhGvEcF8rNkBsz9dQ4Q==", "signatures": [{"sig": "MEUCIEE37C8z7geAff2kTcdrVIKO3/LtuitXAzzrg3ASDJxQAiEAhaPVn15KXF/2oDqBVqg0pghPV6SY4WAiHL080RuiXy4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158882}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/93fda6600673e567421c727dbb3a5212/vitest-utils-3.0.8.tgz", "_integrity": "sha512-nkBC3aEhfX2PdtQI/QwAWp8qZWwzASsU4Npbcd5RdMPBSSLCpkZp52P3xku3s3uA0HIEhGvEcF8rNkBsz9dQ4Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.8"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.8_1741274178231_0.2771373406131954", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/utils", "version": "3.0.9", "license": "MIT", "_id": "@vitest/utils@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "15da261d8cacd6035dc28a8d3ba38ee39545f82b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.0.9.tgz", "fileCount": 19, "integrity": "sha512-ilHM5fHhZ89MCp5aAaM9uhfl1c2JdxVxl3McqsdVyVNN6JffnEen8UMCdRTzOhGXNQGo5GNL9QugHrz727Wnng==", "signatures": [{"sig": "MEUCIQCfsOSL4bRXdMYw0ivtzfiTREHiIBZrx5vfKwrRo3y/bAIgA+/rurmV9UyCotvUJ1dmBb+HcIWHeBm/FCJQgG/9XV4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158417}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.0.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4a43d07971c5c31983c73fad21631d33/vitest-utils-3.0.9.tgz", "_integrity": "sha512-ilHM5fHhZ89MCp5aAaM9uhfl1c2JdxVxl3McqsdVyVNN6JffnEen8UMCdRTzOhGXNQGo5GNL9QugHrz727Wnng==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.0.9"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.0.9_1742212757855_0.15333546277454957", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/utils", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/utils@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "17730372e319e0f2d4b835a24bf9089a4be77e9b", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-n1Fgig8q94IE9UFlPKevfLS4jCJLbzEzNUv0W/BiYY5fmrjodBQ5iZHgzSx05xr2UgJvaICUzYzcve11cbbbaQ==", "signatures": [{"sig": "MEUCIQDAHVq+Xw5QbEVmCts1PwnqbTmtKfmTtqTf7EZovvVpMwIgI6sB5KoQ8JfahIdOUEBdaKV6rjfBZ163tDVIOatw56A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158547}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cb3aaa35cd07382a19fb39b3e99f59d8/vitest-utils-3.1.0-beta.1.tgz", "_integrity": "sha512-n1Fgig8q94IE9UFlPKevfLS4jCJLbzEzNUv0W/BiYY5fmrjodBQ5iZHgzSx05xr2UgJvaICUzYzcve11cbbbaQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.0-beta.1_1742213852231_0.047597000436195636", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/utils", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/utils@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5a7a7fe59ce890e4f499716d5ba3f4385da70926", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-t8POZrFAqyE2CmyQBUHXgQ3/74EALeQAWw1QKHBZG4uqXKNGTioXhWP+s0EBuPsyIS3Lcv68I1MOL3TklQ46FA==", "signatures": [{"sig": "MEUCIC0CuDzhXhNnMikoF+MUltYGRCsXCInIA1JxDfLOt/BsAiEAtBv0KJ+Xj9GhBRf8l48Ytufe1qxyvr+75bPJ6rhycjA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158014}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/42306de9b0b0cebaf1bec1a344c59303/vitest-utils-3.1.0-beta.2.tgz", "_integrity": "sha512-t8POZrFAqyE2CmyQBUHXgQ3/74EALeQAWw1QKHBZG4uqXKNGTioXhWP+s0EBuPsyIS3Lcv68I1MOL3TklQ46FA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.0-beta.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.6", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.0-beta.2_1742545697308_0.4383532513231867", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/utils", "version": "3.1.0", "license": "MIT", "_id": "@vitest/utils@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5177eb8255a21d6f40c1111d9e17a647a6f58ef2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.0.tgz", "fileCount": 19, "integrity": "sha512-FXrqcDTyZRW8Ybyv//6uvrveiFW5xZSf5Ogrwzlyo53b8jReXUy8sxzrbpz/ZiJzuvyn6Vk50aWKHd6PBaZuwQ==", "signatures": [{"sig": "MEYCIQD8brML1g0PigcUdnWSidNZwv4AlwUv6aKAU0Y/+0iudgIhAM3ryuPCU1nZ3E7GN0kIGYD6xRYQRGYVa5I8kpdpacID", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7748430a92b444005af4c9e7034380ae/vitest-utils-3.1.0.tgz", "_integrity": "sha512-FXrqcDTyZRW8Ybyv//6uvrveiFW5xZSf5Ogrwzlyo53b8jReXUy8sxzrbpz/ZiJzuvyn6Vk50aWKHd6PBaZuwQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.0"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.0_1743414345675_0.4035293601937999", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/utils", "version": "3.1.1", "license": "MIT", "_id": "@vitest/utils@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2893c30219ab6bdf109f07ce5cd287fe8058438d", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.1.tgz", "fileCount": 19, "integrity": "sha512-1XIjflyaU2k3HMArJ50bwSh3wKWPD6Q47wz/NUSmRV0zNywPc4w79ARjg/i/aNINHwA+mIALhUVqD9/aUvZNgg==", "signatures": [{"sig": "MEQCIBAN5HhpYthyCAxMj5NfqQOaDcSnjjblWsr+d3HsfadjAiBMIkWcJ5uZMm8iD4TcBfTvJ+OrFUlWiGCC3nXaSuRXyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b70892b1a59828edf567174c85cc70a2/vitest-utils-3.1.1.tgz", "_integrity": "sha512-1XIjflyaU2k3HMArJ50bwSh3wKWPD6Q47wz/NUSmRV0zNywPc4w79ARjg/i/aNINHwA+mIALhUVqD9/aUvZNgg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.1_1743416340725_0.212443482267628", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/utils", "version": "3.1.2", "license": "MIT", "_id": "@vitest/utils@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f3ae55b3a205c88c346a2a8dcde7c89210364932", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.2.tgz", "fileCount": 19, "integrity": "sha512-5GGd0ytZ7BH3H6JTj9Kw7Prn1Nbg0wZVrIvou+UWxm54d+WoXXgAgjFJ8wn3LdagWLFSEfpPeyYrByZaGEZHLg==", "signatures": [{"sig": "MEUCIQC19OCVC60U3xwYhPao4jHARmWwTsDINonOw5rsHAoZdgIgOIVdaFZMmXPBmCCMw0NwDSBb5zNsOe0jDZNaywtvKYY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/43b4af2ca67236d6f22b466715928ad9/vitest-utils-3.1.2.tgz", "_integrity": "sha512-5GGd0ytZ7BH3H6JTj9Kw7Prn1Nbg0wZVrIvou+UWxm54d+WoXXgAgjFJ8wn3LdagWLFSEfpPeyYrByZaGEZHLg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.2_1745225914547_0.33111625140119316", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/utils", "version": "3.1.3", "license": "MIT", "_id": "@vitest/utils@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4f31bdfd646cd82d30bfa730d7410cb59d529669", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.3.tgz", "fileCount": 19, "integrity": "sha512-2Ltrpht4OmHO9+c/nmHtF09HWiyWdworqnHIwjfvDyWjuwKbdkcS9AnhsDn+8E2RM4x++foD1/tNuLPVvWG1Rg==", "signatures": [{"sig": "MEUCIFoU2vpB/VhTJNDDIVjSKO+4W9B5vqsgkpOAxCDzVsNYAiEAkv8pp5R+ZU0bj5PiqMPqQAWqC2RKR/H1sK/RGI/jATE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/96293b82ca5944f4e0ad8cba5a98569f/vitest-utils-3.1.3.tgz", "_integrity": "sha512-2Ltrpht4OmHO9+c/nmHtF09HWiyWdworqnHIwjfvDyWjuwKbdkcS9AnhsDn+8E2RM4x++foD1/tNuLPVvWG1Rg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.3_1746452703859_0.41317193357827664", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/utils", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/utils@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e2a08485e81b3965d6df5d719e722d8290c5a7c0", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-eFooS0utc8Icm/HOxyO1Fp6Euc4HSe83BtTmxqyZ9oB8Ii818QVJHyyke0xUsn5vS3nqHz9E0qjvuVrJ9D56AA==", "signatures": [{"sig": "MEUCIQDMjKetokmZDrQf6X+lOcFHR1ZaGHPYLWJY4muCq02OHAIgUjHDHGweJOx6wzzgrV7kQEsG2vpOwE5sNIDL9nj5JSE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158423}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3b92ce49241486d9a1094829d37fcb42/vitest-utils-3.2.0-beta.1.tgz", "_integrity": "sha512-eFooS0utc8Icm/HOxyO1Fp6Euc4HSe83BtTmxqyZ9oB8Ii818QVJHyyke0xUsn5vS3nqHz9E0qjvuVrJ9D56AA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.0-beta.1_1746463877644_0.9934168956388982", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/utils", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/utils@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "37674d4522d4ec151370fb6e6a1083eaf5bf3a2f", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-4PlibaAmolorG1fmBCjmG4ul8+Rrzj2BbAZksannKZn0j/0gKvzjx6MFOdqZA31GqtoHxgKD6b4jO7V+agZS9w==", "signatures": [{"sig": "MEYCIQDlAkKRIuYt1TEoYn7F06BsGHrbGlyudY9Lmbr5NFIvAQIhAIYWM59+wLl1GNlT6FcGp/0uNEY6LI385MXRgedMup9S", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158437}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/de153db1fd3545dc16d5222526cc76d1/vitest-utils-3.2.0-beta.2.tgz", "_integrity": "sha512-4PlibaAmolorG1fmBCjmG4ul8+Rrzj2BbAZksannKZn0j/0gKvzjx6MFOdqZA31GqtoHxgKD6b4jO7V+agZS9w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.0-beta.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.0-beta.2_1747658325105_0.4702492837206751", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/utils", "version": "3.1.4", "license": "MIT", "_id": "@vitest/utils@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f9f20d92f1384a9d66548c480885390760047b5e", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.4.tgz", "fileCount": 19, "integrity": "sha512-yriMuO1cfFhmiGc8ataN51+9ooHRuURdfAZfwFd3usWynjzpLslZdYnRegTv32qdgtJTsj15FoeZe2g15fY1gg==", "signatures": [{"sig": "MEYCIQCcbbbWzZiaxrU3WXZjdmD7Q59FqqWnjbOu3ybOtttzmQIhAJy0inHuIsuZTDpP5HEhW9iWr+rC372M8MCvM4T0wFm4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 158409}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/138b39350400c5e57690d59550dc0595/vitest-utils-3.1.4.tgz", "_integrity": "sha512-yriMuO1cfFhmiGc8ataN51+9ooHRuURdfAZfwFd3usWynjzpLslZdYnRegTv32qdgtJTsj15FoeZe2g15fY1gg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.1.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.1.4_1747671836000_0.9828731392506653", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/utils", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/utils@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "05ad3215b00c586af97f6cac66346ab0af5dd8f3", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.0-beta.3.tgz", "fileCount": 19, "integrity": "sha512-Q0+cNz5L/6ItK4JAnNu2n46sNeb6p9aXumg4st4bYRaNtAB8A6hiaM5kxNZS+6f67I6SYwSjg9na9JMdmHy4qg==", "signatures": [{"sig": "MEQCICUrXhkfSsVgffiW/AS7yeBj4eZimffWqBDif2yT4H8PAiAHW9VWHyszou52TdUyvbfejxSZRcMVs6Gf+3xCL16tHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170377}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/023da8ebace121b06e280f20117776de/vitest-utils-3.2.0-beta.3.tgz", "_integrity": "sha512-Q0+cNz5L/6ItK4JAnNu2n46sNeb6p9aXumg4st4bYRaNtAB8A6hiaM5kxNZS+6f67I6SYwSjg9na9JMdmHy4qg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.0-beta.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.0-beta.3_1748442516513_0.06910900625907868", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/utils", "version": "3.2.0", "license": "MIT", "_id": "@vitest/utils@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5a6165d54436d9d699076c2ea2631466909a06a3", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.0.tgz", "fileCount": 19, "integrity": "sha512-gXXOe7Fj6toCsZKVQouTRLJftJwmvbhH5lKOBR6rlP950zUq9AitTUjnFoXS/CqjBC2aoejAztLPzzuva++XBw==", "signatures": [{"sig": "MEQCIELVWcZxXruAfMq6PMSV/VttL7wSZS+injRUJG5uaC5wAiA1PIl5t33DUX/P1LFKtKHnah3kkC+ugOoDD82BADRqZg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170875}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cc817042e32e0f3a0437e2a3e348c491/vitest-utils-3.2.0.tgz", "_integrity": "sha512-gXXOe7Fj6toCsZKVQouTRLJftJwmvbhH5lKOBR6rlP950zUq9AitTUjnFoXS/CqjBC2aoejAztLPzzuva++XBw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.0"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.0_1748862646585_0.610542887994427", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/utils", "version": "3.2.1", "license": "MIT", "_id": "@vitest/utils@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a84ffe54085af1fe1e818c387cb3852a07ee4c10", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.1.tgz", "fileCount": 19, "integrity": "sha512-KkHlGhePEKZSub5ViknBcN5KEF+u7dSUr9NW8QsVICusUojrgrOnnY3DEWWO877ax2Pyopuk2qHmt+gkNKnBVw==", "signatures": [{"sig": "MEYCIQD0YjrBdnfoeQgwGDu6gnff/2Rkq+vXzfwqadJbNhkXhgIhANVOD2LNIVQeT/y40ALUlGFNuq5krv297TvbQGrRpaR/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170875}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/80b699fa459b1069f2ee4f91457a79e7/vitest-utils-3.2.1.tgz", "_integrity": "sha512-KkHlGhePEKZSub5ViknBcN5KEF+u7dSUr9NW8QsVICusUojrgrOnnY3DEWWO877ax2Pyopuk2qHmt+gkNKnBVw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.1_1748970442881_0.0901672678215697", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/utils", "version": "3.2.2", "license": "MIT", "_id": "@vitest/utils@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "829a6634c3cbb953bcf1053a6cdbec8f1fa70554", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.2.tgz", "fileCount": 19, "integrity": "sha512-qJYMllrWpF/OYfWHP32T31QCaLa3BAzT/n/8mNGhPdVcjY+JYazQFO1nsJvXU12Kp1xMpNY4AGuljPTNjQve6A==", "signatures": [{"sig": "MEYCIQDY2WBdPMM/Ay+qoQtuXqwENVFZYeppMZpzyYt0XBWA2wIhAIkEAqj6mRbP1StQt46kHxtYON/0y7j9Qsdo6FcZr3SX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170875}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bd5a39e52faa36200c0350f7af751939/vitest-utils-3.2.2.tgz", "_integrity": "sha512-qJYMllrWpF/OYfWHP32T31QCaLa3BAzT/n/8mNGhPdVcjY+JYazQFO1nsJvXU12Kp1xMpNY4AGuljPTNjQve6A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.2"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.2_1749130936483_0.21179987416807955", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/utils", "version": "3.2.3", "license": "MIT", "_id": "@vitest/utils@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "388afbed1fb3c25ca64c5846a9afb904e3d63bf2", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.3.tgz", "fileCount": 19, "integrity": "sha512-4zFBCU5Pf+4Z6v+rwnZ1HU1yzOKKvDkMXZrymE2PBlbjKJRlrOxbvpfPSvJTGRIwGoahaOGvp+kbCoxifhzJ1Q==", "signatures": [{"sig": "MEQCIEDnEzb7+i/8ARiQdyr8FJqxOBWE5q3bysrE90we0tGRAiAXCvbwpqUFjncPtv3iH67cPN6hAvPs278+0llXXVvzVg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170875}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/df6045789c86f38b6cd58535cd2c12fc/vitest-utils-3.2.3.tgz", "_integrity": "sha512-4zFBCU5Pf+4Z6v+rwnZ1HU1yzOKKvDkMXZrymE2PBlbjKJRlrOxbvpfPSvJTGRIwGoahaOGvp+kbCoxifhzJ1Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"loupe": "^3.1.3", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.3"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.7", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.3_1749468744971_0.6698420556377276", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/utils", "version": "3.2.4", "license": "MIT", "_id": "@vitest/utils@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c0813bc42d99527fb8c5b138c7a88516bca46fea", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.4.tgz", "fileCount": 19, "integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "signatures": [{"sig": "MEUCIDnSLysGPEw2xB1l1yAywYJ6lvyL+1UCYjCw2iX/vG37AiEAwiM4CxjdTiIAZJ5Ffyx/lPrt0ANqqcTjlyUNP4PiEpg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 171099}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/3773a1b8c0ec434bc7c8723ec3f2ba20/vitest-utils-3.2.4.tgz", "_integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"loupe": "^3.1.4", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "3.2.4"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.8", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_3.2.4_1750182842422_0.1802816810734731", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/utils", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/utils@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "444bbe5d38d9a8532ebc1cdf8c266fc2d03c234c", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-4.0.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-hd2IrrmWyaokEa3qMQyLWgfKTOOvF+gd2TsOiODTuc7M13wkcOxD9Oyxm8lXzzg63L4U9K7llPLAGU2zY59U6w==", "signatures": [{"sig": "MEQCIA+JWIIosQiaZHODTAjAbtFkUHdy4wkq1NDNBBsqJuIRAiBzHD+FTX4t9Yf/ai0JXiXJca0Kvp2vOOl3lBEjjP5+yA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170814}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-utils-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/440adf2744b33ca1683e5d3fcaf0cb46/vitest-utils-4.0.0-beta.1.tgz", "_integrity": "sha512-hd2IrrmWyaokEa3qMQyLWgfKTOOvF+gd2TsOiODTuc7M13wkcOxD9Oyxm8lXzzg63L4U9K7llPLAGU2zY59U6w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "_npmVersion": "10.8.2", "description": "Shared Vitest utility functions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"loupe": "^3.1.4", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "4.0.0-beta.1"}, "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/estree": "^1.0.8", "tinyhighlight": "^0.3.2", "diff-sequences": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25"}, "_npmOperationalInternal": {"tmp": "tmp/utils_4.0.0-beta.1_1750433316710_0.1900795101093231", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/utils", "type": "module", "version": "4.0.0-beta.2", "description": "Shared Vitest utility functions", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./diff": {"types": "./dist/diff.d.ts", "default": "./dist/diff.js"}, "./ast": {"types": "./dist/ast.d.ts", "default": "./dist/ast.js"}, "./error": {"types": "./dist/error.d.ts", "default": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "default": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "default": "./dist/source-map.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"ast": ["dist/ast.d.ts"], "source-map": ["dist/source-map.d.ts"]}}, "dependencies": {"loupe": "^3.1.4", "tinyrainbow": "^2.0.0", "@vitest/pretty-format": "4.0.0-beta.2"}, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.25", "@types/estree": "^1.0.8", "diff-sequences": "^29.6.3", "tinyhighlight": "^0.3.2"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/utils@4.0.0-beta.2", "_integrity": "sha512-SJuRx8e+0TZBnhJGLRemzoVziJkGskgfhZoCiD27tLEoPUvE7kk7ygSQVGIkYBH/A7i/p+psbF1seMfxV2flOA==", "_resolved": "/tmp/ab7f069f60b08767444c6a5dc3fa878c/vitest-utils-4.0.0-beta.2.tgz", "_from": "file:vitest-utils-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-SJuRx8e+0TZBnhJGLRemzoVziJkGskgfhZoCiD27tLEoPUvE7kk7ygSQVGIkYBH/A7i/p+psbF1seMfxV2flOA==", "shasum": "6b9a487284e6421420668e86661ee8bda5ddd814", "tarball": "https://registry.npmjs.org/@vitest/utils/-/utils-4.0.0-beta.2.tgz", "fileCount": 19, "unpackedSize": 170814, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2futils@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICSMuCpFbYF6SXzAULTuVHKJnlW+JAJ7yIjr3K7Yf66IAiA1tCpIsMfGteandYJ5Og9BZPI6yT6rcGs16yauL+n9PQ=="}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/utils_4.0.0-beta.2_1750775095682_0.9413450576077749"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-01-09T12:33:20.951Z", "modified": "2025-06-24T14:24:56.257Z", "0.27.0": "2023-01-09T12:33:21.183Z", "0.27.1": "2023-01-11T16:32:13.524Z", "0.27.2": "2023-01-17T07:40:29.296Z", "0.27.3": "2023-01-21T16:22:56.727Z", "0.28.0": "2023-01-23T09:27:52.277Z", "0.28.1": "2023-01-23T09:50:56.032Z", "0.28.2": "2023-01-25T11:20:53.086Z", "0.28.3": "2023-01-27T12:09:40.574Z", "0.28.4": "2023-02-03T10:04:44.475Z", "0.28.5": "2023-02-13T12:23:27.161Z", "0.29.0": "2023-02-25T08:25:29.903Z", "0.29.1": "2023-02-25T09:24:43.321Z", "0.29.2": "2023-02-28T15:14:05.751Z", "0.29.3": "2023-03-15T20:03:40.023Z", "0.29.4": "2023-03-20T13:40:15.167Z", "0.29.5": "2023-03-20T14:12:01.328Z", "0.29.6": "2023-03-20T20:23:28.919Z", "0.29.7": "2023-03-20T20:37:25.920Z", "0.29.8": "2023-03-28T13:11:56.015Z", "0.30.0": "2023-04-09T13:38:36.117Z", "0.30.1": "2023-04-11T11:26:24.031Z", "0.31.0": "2023-05-03T18:08:00.067Z", "0.31.1": "2023-05-17T14:23:12.752Z", "0.31.2": "2023-05-30T13:04:34.395Z", "0.31.3": "2023-05-31T14:48:55.722Z", "0.31.4": "2023-06-01T09:54:57.863Z", "0.32.0": "2023-06-06T17:03:59.633Z", "0.32.1": "2023-06-16T12:22:07.450Z", "0.32.2": "2023-06-16T16:05:35.799Z", "0.32.3": "2023-07-03T08:35:24.528Z", "0.32.4": "2023-07-03T11:05:40.077Z", "0.33.0": "2023-07-06T14:11:09.513Z", "0.34.0": "2023-08-01T15:41:38.992Z", "0.34.1": "2023-08-01T16:53:40.022Z", "0.34.2": "2023-08-17T10:09:44.014Z", "0.34.3": "2023-08-25T07:30:03.457Z", "0.34.4": "2023-09-08T10:34:12.085Z", "0.34.5": "2023-09-21T13:50:23.992Z", "0.34.6": "2023-09-29T07:33:28.787Z", "1.0.0-beta.0": "2023-10-02T16:40:14.295Z", "0.34.7": "2023-10-02T17:03:09.565Z", "1.0.0-beta.1": "2023-10-03T11:31:18.684Z", "1.0.0-beta.2": "2023-10-12T19:26:57.013Z", "1.0.0-beta.3": "2023-10-27T12:45:26.486Z", "1.0.0-beta.4": "2023-11-09T10:13:21.825Z", "1.0.0-beta.5": "2023-11-18T09:44:19.269Z", "1.0.0-beta.6": "2023-11-28T17:27:01.156Z", "1.0.0": "2023-12-04T15:45:47.155Z", "1.0.1": "2023-12-04T18:04:33.884Z", "1.0.2": "2023-12-07T10:12:43.340Z", "1.0.3": "2023-12-09T13:05:35.287Z", "1.0.4": "2023-12-09T19:05:05.454Z", "1.1.0": "2023-12-19T14:06:17.526Z", "1.1.1": "2023-12-31T13:37:46.480Z", "1.1.2": "2024-01-04T16:58:30.057Z", "1.1.3": "2024-01-05T08:18:51.280Z", "1.2.0": "2024-01-12T16:07:03.982Z", "1.2.1": "2024-01-17T16:23:45.804Z", "1.2.2": "2024-01-26T16:25:37.987Z", "1.3.0": "2024-02-16T17:28:59.069Z", "1.3.1": "2024-02-20T13:48:19.120Z", "1.4.0": "2024-03-15T10:30:40.767Z", "1.5.0": "2024-04-11T17:47:52.227Z", "1.5.1": "2024-04-24T11:22:19.855Z", "1.5.2": "2024-04-25T09:11:56.106Z", "1.5.3": "2024-04-30T08:40:21.757Z", "1.6.0": "2024-05-03T15:22:07.428Z", "2.0.0-beta.1": "2024-05-09T14:32:22.040Z", "2.0.0-beta.2": "2024-05-09T15:31:17.179Z", "2.0.0-beta.3": "2024-05-14T18:44:35.517Z", "2.0.0-beta.4": "2024-06-02T12:15:50.158Z", "2.0.0-beta.5": "2024-06-02T12:27:43.732Z", "2.0.0-beta.6": "2024-06-02T19:17:19.188Z", "2.0.0-beta.7": "2024-06-03T11:35:36.153Z", "2.0.0-beta.8": "2024-06-04T12:39:20.377Z", "2.0.0-beta.9": "2024-06-05T08:00:56.169Z", "2.0.0-beta.10": "2024-06-12T12:11:32.344Z", "2.0.0-beta.11": "2024-06-19T20:13:54.065Z", "2.0.0-beta.12": "2024-06-25T20:16:11.118Z", "2.0.0-beta.13": "2024-07-04T14:03:37.021Z", "2.0.0": "2024-07-08T11:39:18.940Z", "2.0.1": "2024-07-08T15:32:54.145Z", "2.0.2": "2024-07-10T15:46:34.263Z", "2.0.3": "2024-07-15T10:03:32.170Z", "2.0.4": "2024-07-22T09:13:26.027Z", "2.0.5": "2024-07-31T10:39:58.252Z", "2.1.0-beta.1": "2024-08-07T06:21:25.837Z", "2.1.0-beta.2": "2024-08-07T07:56:50.001Z", "2.1.0-beta.3": "2024-08-07T08:17:02.152Z", "2.1.0-beta.4": "2024-08-07T11:42:51.261Z", "2.1.0-beta.5": "2024-08-12T11:35:19.042Z", "2.1.0-beta.6": "2024-08-20T13:18:31.162Z", "2.1.0-beta.7": "2024-09-09T15:13:13.888Z", "2.1.0": "2024-09-12T14:03:21.640Z", "2.1.1": "2024-09-13T15:32:35.763Z", "2.1.2": "2024-10-02T16:20:02.515Z", "2.1.3": "2024-10-14T11:05:27.790Z", "2.1.4": "2024-10-28T12:27:16.454Z", "2.1.5": "2024-11-13T15:24:03.718Z", "2.2.0-beta.1": "2024-11-13T17:17:16.002Z", "2.2.0-beta.2": "2024-11-18T14:18:05.970Z", "2.1.6": "2024-11-26T12:23:54.644Z", "2.1.7": "2024-12-02T09:49:02.451Z", "2.1.8": "2024-12-02T14:46:17.868Z", "3.0.0-beta.1": "2024-12-05T17:33:37.692Z", "3.0.0-beta.2": "2024-12-10T10:21:39.339Z", "3.0.0-beta.3": "2024-12-20T16:32:44.240Z", "3.0.0-beta.4": "2025-01-08T14:24:00.499Z", "3.0.0": "2025-01-16T14:07:37.428Z", "3.0.1": "2025-01-16T19:32:46.045Z", "3.0.2": "2025-01-17T14:26:16.322Z", "3.0.3": "2025-01-21T13:58:47.598Z", "3.0.4": "2025-01-23T13:41:44.804Z", "1.6.1": "2025-02-03T13:36:06.345Z", "2.1.9": "2025-02-03T13:44:15.031Z", "3.0.5": "2025-02-03T14:02:03.411Z", "3.0.6": "2025-02-18T13:38:44.680Z", "3.0.7": "2025-02-24T17:50:51.037Z", "3.0.8": "2025-03-06T15:16:18.426Z", "3.0.9": "2025-03-17T11:59:18.044Z", "3.1.0-beta.1": "2025-03-17T12:17:32.447Z", "3.1.0-beta.2": "2025-03-21T08:28:17.574Z", "3.1.0": "2025-03-31T09:45:45.854Z", "3.1.1": "2025-03-31T10:19:00.895Z", "3.1.2": "2025-04-21T08:58:34.705Z", "3.1.3": "2025-05-05T13:45:04.057Z", "3.2.0-beta.1": "2025-05-05T16:51:17.869Z", "3.2.0-beta.2": "2025-05-19T12:38:45.292Z", "3.1.4": "2025-05-19T16:23:56.182Z", "3.2.0-beta.3": "2025-05-28T14:28:36.699Z", "3.2.0": "2025-06-02T11:10:46.831Z", "3.2.1": "2025-06-03T17:07:23.107Z", "3.2.2": "2025-06-05T13:42:16.693Z", "3.2.3": "2025-06-09T11:32:25.170Z", "3.2.4": "2025-06-17T17:54:02.601Z", "4.0.0-beta.1": "2025-06-20T15:28:36.904Z", "4.0.0-beta.2": "2025-06-24T14:24:55.868Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/utils"}, "description": "Shared Vitest utility functions", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}