{"_id": "supports-preserve-symlinks-flag", "_rev": "1-8d7a0b792ab19d51365d64228ea32493", "name": "supports-preserve-symlinks-flag", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "supports-preserve-symlinks-flag", "version": "1.0.0", "description": "Determine if the current node version supports the `--preserve-symlinks` flag.", "main": "./index.js", "browser": "./browser.js", "exports": {".": [{"browser": "./browser.js", "default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/node-supports-preserve-symlinks-flag.git"}, "keywords": ["node", "flag", "symlink", "symlinks", "preserve-symlinks"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag/issues"}, "homepage": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag#readme", "devDependencies": {"@ljharb/eslint-config": "^20.1.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^8.6.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.4.0"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "1f7cac19c0c298cf40b3f2f3c735477ad579ac61", "_id": "supports-preserve-symlinks-flag@1.0.0", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "shasum": "6eda4bd344a3c94aea376d4cc31bc77311039e09", "tarball": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "fileCount": 10, "unpackedSize": 9178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0qRQCRA9TVsSAnZWagAAUXIP/RXFkdQS9EixIbL8N6/o\nwfOcVOd8kQCgEIH9PvXuhRXWq+pZmN4tGOJgXBu9mMLsvulJX2hVIzpQ+5+n\nXfroKni4cPeS7DUNc/YIzRBvwsdPaRRPF4wEdwYJleZ/uFI6rtKjVkf3AN38\nj0ofY+IWG2uIh4zr4/xZFszH3x1Fx19tBILBjUmlJoXPVt2zZ7RuGeK0s3vu\n0TY4fMD8F1TNR/Jv2x1BL6vwcBHeU0p01XrhcnGbBGuVcwLi4T6SKgm18Q3a\ndg4B/iQhG4BoD9KQ+WCO79p9ocssmsePckOE4jwGwnGQJA7KOOaUX0w76Bab\nGtpSFZ6jGOcSQtn35YSjbMlBZap83/11FZh5KkJztkwRr3nP1f5/uvFTLmWk\nbWt7bORpfgmp9ylG8Fw9+4ZRdyRQuPZI7veGobxsF4ha2xm7IwfD9nxfZSh/\nB2UzZzRJzepmR3BKaYkz87GCGj9XYQYw2YkQnPfv0apB2IigRV06h6GF8Z2U\nnuDM45iClRLh9opVxtYqUkwM+R5ny0HdV52clVJ5WOl3uptryNtCSxmWxhLR\njO5L/cKoy9FVvI3hkPoF597iBeqoVuy/2UfTmCfwooTH122lhtia3WrL5LWs\nMXRgqM87fmgT9n4C6jBmPt4YGrr4BnTM42jPszcaT1Cgt3BqKHfGjkBfm3cC\nudxD\r\n=G5s6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbGjRqHlhKnzCCIXQzWYR/EZTAkHcYVGSMuGr2wODv9QIgBmYVLR82wlbK4vF/fPOx4NG1EYvG/pwN6NlOp1nWVkI="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/supports-preserve-symlinks-flag_1.0.0_1641194576474_0.27878743076587753"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-01-03T07:22:56.474Z", "1.0.0": "2022-01-03T07:22:56.640Z", "modified": "2022-05-19T01:59:54.715Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Determine if the current node version supports the `--preserve-symlinks` flag.", "homepage": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag#readme", "keywords": ["node", "flag", "symlink", "symlinks", "preserve-symlinks"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/node-supports-preserve-symlinks-flag.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag/issues"}, "license": "MIT", "readme": "# node-supports-preserve-symlinks-flag <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nDetermine if the current node version supports the `--preserve-symlinks` flag.\n\n## Example\n\n```js\nvar supportsPreserveSymlinks = require('node-supports-preserve-symlinks-flag');\nvar assert = require('assert');\n\nassert.equal(supportsPreserveSymlinks, null); // in a browser\nassert.equal(supportsPreserveSymlinks, false); // in node < v6.2\nassert.equal(supportsPreserveSymlinks, true); // in node v6.2+\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/node-supports-preserve-symlinks-flag\n[npm-version-svg]: https://versionbadg.es/inspect-js/node-supports-preserve-symlinks-flag.svg\n[deps-svg]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag.svg\n[deps-url]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag\n[dev-deps-svg]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/node-supports-preserve-symlinks-flag#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/node-supports-preserve-symlinks-flag.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/node-supports-preserve-symlinks-flag.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/node-supports-preserve-symlinks-flag.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=node-supports-preserve-symlinks-flag\n[codecov-image]: https://codecov.io/gh/inspect-js/node-supports-preserve-symlinks-flag/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/node-supports-preserve-symlinks-flag/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/node-supports-preserve-symlinks-flag\n[actions-url]: https://github.com/inspect-js/node-supports-preserve-symlinks-flag/actions\n", "readmeFilename": "README.md"}