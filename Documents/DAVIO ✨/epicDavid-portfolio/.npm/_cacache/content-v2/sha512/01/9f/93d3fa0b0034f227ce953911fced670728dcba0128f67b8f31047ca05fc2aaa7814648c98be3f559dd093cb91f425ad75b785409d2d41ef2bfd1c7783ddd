{"_id": "glob-parent", "_rev": "32-0f8ad17d42da12bd6c301362fa1b2e96", "name": "glob-parent", "description": "Extract the non-magic parent path from a glob string.", "dist-tags": {"latest": "6.0.2"}, "versions": {"1.0.0": {"name": "glob-parent", "version": "1.0.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "directory"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^0.3.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "b08cbec38731960b35446da9d819824ba35e0c49", "_id": "glob-parent@1.0.0", "_shasum": "3344e0e0534fbdd478a7c1f1480f2d8851650a21", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "dist": {"shasum": "3344e0e0534fbdd478a7c1f1480f2d8851650a21", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-1.0.0.tgz", "integrity": "sha512-ljSwpCJxCwgPGgaokKbRzDcHeyHevuBWXNRHEcP8m6bzzOnCXL3/sEPGynrOCkSvDq8ZdZgEvnQicJOyvf5Cfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmYVt2unQzYbGadn2Bq9L+gkcBL8hZNYva3KfDfejspAiEA6ldBWlzTz5zrF6tQ1WBlwUkJyrvVX1KEYR6wHgtAZGw="}]}, "directories": {}}, "1.1.0": {"name": "glob-parent", "version": "1.1.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^1.1.1"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "5e33355528b4a2855861e8269b8071f897baea82", "_id": "glob-parent@1.1.0", "_shasum": "63e5780b13f84c7e7e1e8f4d5a3d96c7cce1f9fc", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "dist": {"shasum": "63e5780b13f84c7e7e1e8f4d5a3d96c7cce1f9fc", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-1.1.0.tgz", "integrity": "sha512-we0TEiMzEmt8RDSr69kBwTGUtYQOB/HR7tbn5zpH7g20sJsbnkHa7VNI+N1pZszhbMsVN0Q9onZd5qQ4WkD2SA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGzm1XfH7P4LrahfqhR/h/RNzli5x1kVp+h/aWmZ2oHvAiBglx/Mf07Jf7/WfCbppbwB95v1p6sDC1pgKwRzoK+7bQ=="}]}, "directories": {}}, "1.2.0": {"name": "glob-parent", "version": "1.2.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^1.1.1"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "9c31e43731d67f16ec0d858cb266319a843c546e", "_id": "glob-parent@1.2.0", "_shasum": "8deffabf6317db5b0f775f553fac731ecf41ded5", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "dist": {"shasum": "8deffabf6317db5b0f775f553fac731ecf41ded5", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-1.2.0.tgz", "integrity": "sha512-SY3bNMrDCLZrpF0KNjDaWfy8Gwr22Y4xhZN+KNz7y+CySV+FYUJ4SK78aW7F7Fzp01MQ9ot90b05CPe3xcCLPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDa4Q/Bhpmu5jLnkM2Fup1VZRwofmLuZVzwtx+BTNJwWAiBZ7ldRldfnY4zGrc+ukT7Ezgmr2FWbOfhp+i7t268zIw=="}]}, "directories": {}}, "1.3.0": {"name": "glob-parent", "version": "1.3.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "4fc6444ff8f64c18d19467fc89c0fa342bc01c42", "_id": "glob-parent@1.3.0", "_shasum": "971edd816ed5db58705b58079647a64d0aef7968", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "971edd816ed5db58705b58079647a64d0aef7968", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-1.3.0.tgz", "integrity": "sha512-hTmuuCjsIMiB85432X8VgmlgWVn99Np49NOWsRyfPkvsFBmsHOoCkOoFGNrMgauLMDD06Mzw+uVTw+oWNCAzgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIALJkFi2EfrX1q7DIJKiK0FzmLwMyAtMO4Uv2befaIgPAiEAmjvdajesmHgcEAPtuL/+DVoWy/qTbLZQVec1sHH6ReY="}]}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "glob-parent", "version": "2.0.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "a956910c7ccb5eafd1b3fe900ceb6335cc5b6d3d", "_id": "glob-parent@2.0.0", "_shasum": "81383d72db054fcccf5336daa902f182f6edbb28", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "81383d72db054fcccf5336daa902f182f6edbb28", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha512-JDYOvfxio/t42HKdxkAYaCiBN7oYiuxykOxKxdaUW5Qn0zaYN3gRQWolrwdnf0shM9/EP0ebuuTmyoXNr1cC5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF7gxwiqjhC5gOVvwVoR20/DAWu77KAGttmo14IPacYQAiAmNv/CN69+DnzRpvjV9vlPZEFIxAX1+gld/B0TNwkr2g=="}]}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "glob-parent", "version": "3.0.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^3.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "fd9c64f7696717cb3d440ca9eef5060f64d61c57", "_id": "glob-parent@3.0.0", "_shasum": "c7bdeb5260732196c740de9274c08814056014bb", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "c7bdeb5260732196c740de9274c08814056014bb", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.0.0.tgz", "integrity": "sha512-fpoA6LedobT+fXIQZYkGfw469/jWHRqzKZMGXLINwODbsKcGBwtLZ7SYpu2GfK09+nz9qpNzVPUrDIV+XVr+dQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBMWMi/3TIYw0ADCV9dQEtGeHDzuDgyqHgrJcV+v/LynAiBg6THCtjcHMVzNEkpgvGkc9L+AXZD48hUb1aVILlMf4Q=="}]}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/glob-parent-3.0.0.tgz_1473189232576_0.26026900205761194"}, "directories": {}}, "3.0.1": {"name": "glob-parent", "version": "3.0.1", "description": "Strips glob magic from a string to provide the parent directory path", "main": "index.js", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "a0eeab41a4e5f56d1bff7f2966260eb541816187", "_id": "glob-parent@3.0.1", "_shasum": "60021327cc963ddc3b5f085764f500479ecd82ff", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "60021327cc963ddc3b5f085764f500479ecd82ff", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.0.1.tgz", "integrity": "sha512-4THEFqMGtz6S1LjsZTCQqZvlgYS6j8eMaJ+XsWh3Tvjw8aYb6od4b+tPdXL1CRJXe41jvSI0Gj+jrWGu2Jp3JQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG2OlwHwoWgJIvSlYSwA1cfAoxtHyYy8gbjz8xUcr0vAIgGB0aiTncw46XMDnBBh+uH+Vi32FGAv5/rkhDG5zy5+c="}]}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/glob-parent-3.0.1.tgz_1476818425901_0.16260230354964733"}, "directories": {}}, "3.1.0": {"name": "glob-parent", "version": "3.1.0", "description": "Strips glob magic from a string to provide the parent directory path", "main": "index.js", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "files": ["index.js"], "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "gitHead": "880243f7758be2967318280ad3e599c95e832a76", "_id": "glob-parent@3.1.0", "_shasum": "9e6af6299d8d3bd2bd40430832bd113df906c5ae", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "9e6af6299d8d3bd2bd40430832bd113df906c5ae", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEmrW5CzOakLN4Q/EKP4R24UU28jeohNX1sUXqo0hr2AIgN6eKjXzvyo0h2o4V2h9Sn0M6wjRNs0Ov0rO6F0M66vI="}]}, "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/glob-parent-3.1.0.tgz_1481730526821_0.4327609031461179"}, "directories": {}}, "4.0.0": {"name": "glob-parent", "version": "4.0.0", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 0.10"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1", "path-dirname": "^1.0.2"}, "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^3.5.3", "nyc": "^10.3.2"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "762a6c27bed24773d8b9934e58a9ee836aa81ebc", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0xDQj8byAab39JmsVBzMsgieBkY2n1OgUs6DZ/FFaP0Y36XwdDrFMlgK6yAWuafTkXktvyc6/S3D7jGo8R6EsQ==", "shasum": "5ba0cc2916ffdcfb86eb471560dbdc5739a358d8", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-4.0.0.tgz", "fileCount": 4, "unpackedSize": 7329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnApSCRA9TVsSAnZWagAAi00P/iEvOrHI53PV8wL/6yHM\nq0XXQ7AjNF6aGtMEyjIRzOLHFghmYoa24L6x1nHk+2oD81Crff7X/MT5yeuG\nVPU9T7o7RNybZw09ACkN6zGea48f5Xehn5VA3hfFOR/KoPItnehxeOwx6NPe\newTGOcb03SKOzCFioQC5BbZWIxkN8FSpE1OBf86qE4J0WJp0/kEKWprNtVPA\nsiL3Ux4EKso3YuedxqQFoyubG1OB0x39HeBCYXgE5LYLGZxpCCqZMfce9e4G\nrG/4fmiy7zPyWyCSQNx8H35ruDrg3kha0PlWrG4acm6C6pzkxudqtRgBbK/5\ntx/c5gJiU30I3Wnb6BYuSm5r/JxjQeM3ZXec7PQJrmwveTOS1H3HDYeteMZa\nBlFeOrPwouGkOseG7/ogPGugeIY92fQrbu6cZrzi3AjNN0NZcdq6yfTQQ6c4\n4wq4LGz4cd73tiVo5GNHImsfja/7otYXx/mNWOIVd1ZO3Sx7F8IqpRiRLzcU\nMSSACBSiDdvrYLvD+6joDYheuvNNZrT3ZxLS88o2+zOJMjtqVVeFETcC9YN9\n1kv68NcCHFr36dqa+JXaD+6RW8Oh8ViM/rQ6LsHfGx4z7dwCRXBgBiAZ233D\ngcNCc2GM2Hom4gu30YnmUsL+Q1PabwJmjOuLnDKAuBjaehJQ3JlunisX4PMW\nmd6x\r\n=w6yE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiW7VKbHtKAIL81HY8Uj3tmYCwURJrUy6SaKRn9PJdSQIgPtys+9d09wcH9QjLzL93M6efKtjLVTCHK+uGaySdZW8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_4.0.0_1553730129908_0.12359635418449222"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "glob-parent", "version": "5.0.0", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "d4975484222bb811c02dc2dc268ea48e2fb7ae6d", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@5.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Z2RwiujPRGluePM6j699ktJYxmPpJKCfpGA13jz2hmFZC7gKetzrWvg5KN3+OsIFmydGyZ1AVwERCq1w/ZZwRg==", "shasum": "1dc99f0f39b006d3e92c2c284068382f0c20e954", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.0.0.tgz", "fileCount": 4, "unpackedSize": 7306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnp50CRA9TVsSAnZWagAAJZIP/22DpVTGanNcNxu2Zb1o\nJ3KLGAGAz2u/rMtbT0RMyJMf+yEYVL1l7++da6n1SnJTJmO8vL1emnQ6DNN5\nVlFJnNL4B0E9NyACSx7NUydO3gsPD9q4IhFBC8Duedp87v9rOZyrQznQOBM5\niCN6OBRAzJjslSkjHotGZrjutVOi2nFNipyu48RK5zJ8j9PG/zfXAkCK4Ach\nHvwkxO5BKdqcM57m3qoe79x7aAmhXpesUS2qqj9siIH+Rf4VtmBiMieog+Ln\n8Qp4eE7ico9H/hiOup0dU2dWSKbl7CmKAKCgyGjw3QQs54reh6KGW57jGByF\nOFhEVlCn53aaUext153cBfvEEW/7B1hKdj1fEqneSf4xXzp/7WmNlJLabM5h\nFzhhOh5MX4beBWm4VJd1AAW3akqTzqkSMuPjoeY/qcJh3Mf2/PN3SnalQ4rC\nKK5zbhxKXdFMokW9tqKlkAJoWpcnidU9QOo0Ljo5sGOhblZXskyi3bWHpn9s\nic9Zb0hbXUWnblbGc9GYJSRRrLUtzdQoWEvbTWWqnOVjn24QGYsNh8xiyZPE\naRHtB9R+mY/ZfOsX6XI4YEeIP3kRKKF4gS9hn5MitQZ79fhONmzl9QMK4mVg\nBQ1mq0CwMrdhMtAgE8LejSPLkCT7ZoRWS9KbzYiViR1F6GQGHv79juw7lWIS\nJHiA\r\n=YKqG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGTneZY0b/VFrkyFgmcyn0Agcpy6Dwdr0BWvhBiv5o8BAiB1QwFpzWkdAEdFAAY4NAmcuYQApTSlb5UMnvS+LYLGGg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "contra"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_5.0.0_1553899123737_0.8913332550953639"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "glob-parent", "version": "5.1.0", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "2e5386e14f56f0957985fb2b607a45158c9c7a4d", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@5.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qjtRgnIVmOfnKUE3NJAQEdk+lKrxfw8t5ke7SXtfMTHcjsBfOfWXCQfdb30zfDoZQ2IRSIiidmjtbHZPZ++Ihw==", "shasum": "5f4c1d1e748d30cd73ad2944b3577a81b081e8c2", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.0.tgz", "fileCount": 4, "unpackedSize": 7633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhq9xCRA9TVsSAnZWagAA1cUP/jXvsAgMtvtDGbSfYHlA\njIaZ/qXIxZ5OVQ0RhHqSjkcoPv/NP3bLKgQ8DH+SF08g5xQV0JCBJX8Jbb/V\n2X1rV0nTxfNnDqIF0OjVQft2lNR+/iFzKtZtbnSXblrfiXC8Q7w5QTR4oJZD\nzJlAOLzR5hm2DX+LQnNfNpkxJAowhmDoJKd4T3EH7GlOyu5TUWUoERHqrWS/\nC2cyCqJN1kwKiyOFcsqVp7nK6G/ceuNdoUwFHujLG3mgiY7GtF0Cyz2c2q6O\nUs2huH6jZgyeiyFlx/faFG7ZExEcf+YW1Ifc3xsmuL5eK+ULHsf0mFY7Q/f3\nzYkdbYkOmtR/K9GbfY+hfvVud4yKkE7upGrh947Wp2MIP0go9zt4mmXgcciW\ncqNlMa9uVMxb1D9qVHGVj+kqU0erXJvdyRj5qLdsdjlKjBpYMEZrG9s7sF4y\ns5/Iid+aaLeCZ/h0XCby5rtS2+00UqYIb227rxEAHgBZfPZ4jQama9m2CgG2\nkVftCPU5SnIHoTXuk+gAtJuHw9DAI4f+HTucZjJ1SeX77bPlYTX+ruVlfX0i\n/ZKojU9DXaRZSNRHcyYv6WZFkLwdZnQ2b/mJWwojPeAiY1hiRwzeJEkyzWRX\n61xD/U7KP8L1awdxn//OgGx2Cq7GUIpsx/BYNIV2oqijiVTzRdHtAoGY1iUs\nM++9\r\n=+PiF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL02WyaZwdIxjuyWvSw8QHLyX02/mEpLnzj0VF6lQBpAIgakJC8t9GbnQ9oPDOvbM/+8EinvbG4urZ24o9ygKodas="}]}, "maintainers": [{"email": "<EMAIL>", "name": "contra"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_5.1.0_1569107824875_0.8129032170320736"}, "_hasShrinkwrap": false}, "5.1.1": {"name": "glob-parent", "version": "5.1.1", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "^3.0.11", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "6ce8d11f2f1ed8e80a9526b1dc8cf3aa71f43474", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@5.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FnI+VGOpnlGHWZxthPGR+QhR78fuiK0sNLkHQv+bL9fQi57lNNdquIbna/WrfROrolq8GK5Ek6BiMwqL/voRYQ==", "shasum": "b6c1ef417c4e5663ea498f1c45afac6916bbc229", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.1.tgz", "fileCount": 4, "unpackedSize": 7610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedqBhCRA9TVsSAnZWagAA5/AP/3rbMfJRcMrSNj0tqF0Z\nRKOf3qaKIbQTL4VfwkTi3dYHmvSgxmcfpJgSgSTQbp/jFRTpJssKGo1pRru6\nkysEd1JlYpnd4YWRIl3N2rUE1BtYb5nrwTWXYC86WcksTl/5ZNBWmVNpZRYR\nvHV8gy938OAzGYZuqbPcVhPmt3OovM8M5aarFFD1atmGcU9hfR/Ko3g2693+\nTpI0DmqoEHXcq0tl4Ll+jtvUWC1NIzGze02YXUj5nk6Ai+XKoej9XApbbb+p\nnebUurHREX72WON5KyZ12rKyfBZJriQQbGBcDg9f1/r23oqDFfDXIm3luAzy\neY+KnjDGwpHlU8n2jerk+QIeG/InNugREjxAqnX9JJHiwbXpef5tqTRwHaG7\nDDMKBw3LJJfv13O96/QfPH/novqa63c8ySul6+LmSNn3FzMvC312fVKdUou5\nbMAprqSCoGoQPutkiR8Tn6/TbYpPl1ZZWnUJffsDLTNgZGHsM5mQqzTyo0+o\nrXUDetVC2oTVXRfyF/Gu0dlLqrtgkqKwDsGmkn2hBqmvkuzO1I7QGB9b1nDb\nmvq2bLCW8kGsn/m+wCzj5/7eJREETDoDIiYedmb7IbHB30+DtktQsjtiHi5/\nHDasPTdPJjYfVbUUg7ER0EUnt/UhI6eiJYT5ly0FK42L7rLWuGEsMmwayUKG\n69z1\r\n=Q1Ue\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtypzt2TTHqdncgcNzjAbxaUQwpIOnuNl3xEplLi524gIgMIq9Ygl23ZhzDFHENnOwHjCYnCaDdgmq2HwZ9/o7nZg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "contra"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "phated"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_5.1.1_1584832608680_0.3786335780744279"}, "_hasShrinkwrap": false}, "5.1.2": {"name": "glob-parent", "version": "5.1.2", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "^3.0.11", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "eb2c439de448c779b450472e591a2bc9e37e9668", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@5.1.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "shasum": "869832c58034fe68a4093c17dc15e8340d8401c4", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "fileCount": 5, "unpackedSize": 12134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQ/JoCRA9TVsSAnZWagAAti4P/3ugts4dkB5LG9bEA0Tz\nkHPnw6DIHXUrxnPohukjFFrkBoX9LXNhecqIogOPVAlJIpgPUgj6iuAP2Jdh\nKrjHN/D1lkxFPgpmo7I1bMNm1u8BEvfkQ7DmbtdOQtGJuM9+tBCIarzjGlGi\n/1ekZnH7sOjpMQgC+THjsMRuV0fMV6TYByIQn3r5gFnEwrm8uqS1frazXhNz\nvVTUSlSYYL0PftXKvFpb5Cg7+rgrB6IYjeo6APMEHfSsRLud2pqhEDKJOOjj\nnmIh5U/jHZ5T2ZsGCSqnco9bVAnh25M7cfb7ZJUdSDKFdLkHvzOMDlJ04gHu\norg7g+q1gY1oOUcHKIwex+jVkI19BXCJGTGQYf7lVX+WBXZOuzfCN587nj9k\n1gepBgw45CMwXbu7OGiC2B5GJrZhoLoYBTarTf9zNmZt1iASqnwULfgrwIrJ\nbhK8gwko/+1lgIv171YETL58NjhcGDEi9dAVnX0DgBpLVx2DNNLhmgd8APtV\n+XKtMJyHJydr1cfneiYMpRok7MlTHQZ0pwFaTnLEjjb5zIf6oWTSKwuN5Z6F\nTO7Rx7HUoKtUNPkxiqRpl4AtSZTbf+iGswgtsR2HnlGCTgepBHmu24S0pqr6\n+iPcjUX8H4fzWDSIeoBQwXD4uhd8oragxQa98tezZYk37GYOMEqrBSsJdKXy\nF4M7\r\n=Sdjx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsl80XgU7vMQRpLYZCdgFz8BeP0oAOyD6UplLbAY7dCgIhAJeVFAUWvwtFj6Jornu2GyVycFXA9ayCxIm9rCmwIE7m"}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_5.1.2_1615065704336_0.23469589484108866"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "glob-parent", "version": "6.0.0", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">=10.13.0"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "expect": "^26.0.1", "mocha": "^7.1.2", "nyc": "^15.0.1"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "3ad9597edfff30f8deb9f35e9f2554a618bd8656", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@6.0.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-Hdd4287VEJcZXUwv1l8a+vXC1GjOQqXe+VS30w/ypihpcnu9M1n3xeYeJu5CBpeEQj2nAab2xxz28GuA3vp4Ww==", "shasum": "f851b59b388e788f3a44d63fab50382b2859c33c", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.0.tgz", "fileCount": 5, "unpackedSize": 12373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkFnSCRA9TVsSAnZWagAANF4P/jw/JCO1Dii9RLJqLa/i\n+ix8oO9Y52HhdD24HWuX+ZdkFJbAzh+/MSdkbGBr7Seb9BKb4MbsrnF3UZpq\nC5FwTmNk65LMlpOgZ4RP3z7xSO8wYJaWLB2OkOPGNdwAG/gzcHl6u1dVllyC\nrK83mCTpC3PhYErjit4qTdJZcmhWhzkDxIQHkTupUjDssPuU6HXbAsO8WP+g\njlCxEHb87m4bSpRb09v+KC49aRhuIvHROvL6ad2OHdkPXnMLbZ2IG4a6yYUQ\n0xn7dP6GaRRnLcqEeFrpVhx9JLOvBYYK5UCEZTYJEPMR41/urnThH+4vksyY\n8onA3HsFLzLXoM1Ulh2IpWA6H/GDTdApSN2YlLe8HL499O1QV7YxHHRcWTcF\nxk0VMetdOTD7rU+m05+z4HSHrdtkcZ3ZIa5nwpOuCw9gOzhbHOeOtu9iUwEB\ntrzBAGjD9g0IUZ7sZBW7M41qAe38cK3BaXvno3alkS4yfQJxhwZHZZzVS7kH\nZ9MOrAXVjx2DxBHCuWDSbmXZ+QOBR8Keg1/f36vHWRDy2dlZwcYiL67TamyU\niaMuqxQRYNWgD3UphUj9ynbTatkZyv6tgWavGxbf/jHc6KldQw2wd6IciFw2\nevmt5oetiS13+mjrOuRd6z+az3juHLZWrTZOzZwYrR6pdbHB5neI+IqDGDE8\naCe0\r\n=ePNv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDl95LO/tO54RxKz4Tqr8NPveJV6IM6oalrnvClXxXhHQIhAJ6i1V0QoLoZShBr5yie6xJlLIPV6IVieQlWyOd0+cmJ"}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_6.0.0_1620072914014_0.2149999211656708"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "glob-parent", "version": "6.0.1", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">=10.13.0"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "expect": "^26.0.1", "mocha": "^7.1.2", "nyc": "^15.0.1"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "e1a15e1ce59a7b6c319878c03d291831461263b1", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@6.0.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-kEVjS71mQazDBHKcsq4E9u/vUzaLcw1A8EtUeydawvIWQCJM0qQ08G1H7/XTjFUulla6XQiDOG6MXSaG0HDKog==", "shasum": "42054f685eb6a44e7a7d189a96efa40a54971aa7", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.1.tgz", "fileCount": 5, "unpackedSize": 13065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9mPSCRA9TVsSAnZWagAAcJwP/RCV8pFeeQS5xNWB80kP\nxMnwpqCNl/RDx6jwy7ExDChJnrXNi5X93s7s5kFGqQGEAMxLHEsYDdwtGDdN\nAoVvFYsN7AerTWOYA3Xa7zTHCA3dsO9+BfRoaSD1CZbZXk5CF+0QzJTAsYlh\nwzUhSgRhcaj7BUQ7b5k/Ynl5kS8vKnJVGF6EA6XlluTaKjhV/RgC6Tc8Cnv8\ne2vcRg5cef0/kGFLtCntFJliq6gAHD7oQbAsRst6oKZMUcsZbkp6ZQHeKQaa\nzcXox4f/npL1m7ARmYD5UG1SBLSAAemeRvxECBtRQAPGu6/H//kcTbfXNmJr\nUnJkS261gx+v7ethNTqdFK75Q7KACWq4bsKkMtaJLT8GrvuOpDPmi1wXGwJi\ntyJK3EyIEa65/Bc2jwkwwPm6VOC9XAfoXwLhBT5odS/ObQyqprrrVYHpp5Fe\n0Cy0YnFCkx1fwJ3NQhXbfkMAE7nR/0W2ChYt/QtCqYaSR8jgGNDAvxY50Qj0\nklM32crGTZgiZApcFLVBaE+G7q4LFfCnYMWDK/8kuTuHlr1oV42CPiK69kUE\nyf4nB06FgbgyII7OxrXMNEKO69zd00+Mna7G2nsae905PIHbw2zyQVG1jJYV\nTjZ7YiHzgSQc8Xcf9UCvmDbDF6wE7nigNF4GNFB375wJoksVfnq5CaxHqp5W\nPk1t\r\n=T58i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSTkQm6QuNGkvqGU0Y4L1/mG6m81f34H6hweyOXPF9GwIgINc7nxxjNlDPlIURe/c0srUrHDLb20Me1iACBgV/nAw="}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_6.0.1_1626760146813_0.7155364154382644"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "glob-parent", "version": "6.0.2", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">=10.13.0"}, "main": "index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"is-glob": "^4.0.3"}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "expect": "^26.0.1", "mocha": "^7.1.2", "nyc": "^15.0.1"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "gitHead": "26ce5ecec10c687cffb9891c108fb2d2800b9140", "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "_id": "glob-parent@6.0.2", "_nodeVersion": "14.18.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "shasum": "6d237d99083950c79290f24c7642a3de9a28f9e3", "tarball": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "fileCount": 4, "unpackedSize": 7719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2sZpCRA9TVsSAnZWagAASs8QAJ8YXzO9X1sW7kZ/zr0M\nGKP8/O1/QHIZoL2zbB6SzBwPA0VMwB9n3jKCkoP4kUYa1iFZ/4CKTPN2ImKp\nXQrAYpOEAvGBbKbBOJ07FmByFJzmJ4qrPZBopbuI67rpAr3v8KLR7cKJdH9R\n994NHhFQ564JoKAgzei01q+p74rgipcoH2OTy+6YearDtQza75lcxJQ1D4b5\ngSsJsmecbIk9t8xO4A1WaPPVD4InDdrrmlJI9OXOXWJaT8Pf5NCH2br0lt35\nxH+RvO0371oFOOv1DcVbbAcb52w0cb1ur/E/HRUIYPAJIpsZLEPT1Y0cLAfN\nlCfBD8lgh+OiwB4omUpRxpwQ8ixB5FGaDOozRtH84AjF/oNkaXCFmYm8a9JO\nSqCunlKJJX+8u7VEb0HWvECZxe8mFn+VeIyRY4w2lJRQY62cCwchmaJCAh8E\nddj1SBAiUUob6BEC3iJqVZSLEz7Y06n2IJQUOkMrfMl3MtRAntBZJRPsVJOZ\nTWFf8NEho1yjNcqlaXUpyNJ2z3p9Y9KuXjszBXNUwNF693AMxefnN9Q5sX+h\n0631MSN1Lo+vxAyISoAaT/kLdOaNsv9Dxc/SK3OU1ZZaSMHqogqmi4y91cWt\nlP4rt8rVt4oulXypzL/1rXUiX0xY8aY+s5hZrXft68UH6psUHmE/dXBz8wA1\nQEy8\r\n=rkhO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVmN4H8BRJhnPIWNSJoAqKlmzhvMvtQZGlK4hYm97w4gIhAKkydC86Nbd7/huquxaaOv7B6PDCMlDBhpZ4ZFh6C10u"}]}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "phated", "email": "<EMAIL>"}, {"name": "contra", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/glob-parent_6.0.2_1632953664327_0.7899538086664579"}, "_hasShrinkwrap": false}}, "readme": "<p align=\"center\">\n  <a href=\"https://gulpjs.com\">\n    <img height=\"257\" width=\"114\" src=\"https://raw.githubusercontent.com/gulpjs/artwork/master/gulp-2x.png\">\n  </a>\n</p>\n\n# glob-parent\n\n[![NPM version][npm-image]][npm-url] [![Downloads][downloads-image]][npm-url] [![Build Status][ci-image]][ci-url] [![Coveralls Status][coveralls-image]][coveralls-url]\n\nExtract the non-magic parent path from a glob string.\n\n## Usage\n\n```js\nvar globParent = require('glob-parent');\n\nglobParent('path/to/*.js'); // 'path/to'\nglobParent('/root/path/to/*.js'); // '/root/path/to'\nglobParent('/*.js'); // '/'\nglobParent('*.js'); // '.'\nglobParent('**/*.js'); // '.'\nglobParent('path/{to,from}'); // 'path'\nglobParent('path/!(to|from)'); // 'path'\nglobParent('path/?(to|from)'); // 'path'\nglobParent('path/+(to|from)'); // 'path'\nglobParent('path/*(to|from)'); // 'path'\nglobParent('path/@(to|from)'); // 'path'\nglobParent('path/**/*'); // 'path'\n\n// if provided a non-glob path, returns the nearest dir\nglobParent('path/foo/bar.js'); // 'path/foo'\nglobParent('path/foo/'); // 'path/foo'\nglobParent('path/foo'); // 'path' (see issue #3 for details)\n```\n\n## API\n\n### `globParent(maybeGlobString, [options])`\n\nTakes a string and returns the part of the path before the glob begins. Be aware of Escaping rules and Limitations below.\n\n#### options\n\n```js\n{\n  // Disables the automatic conversion of slashes for Windows\n  flipBackslashes: true;\n}\n```\n\n## Escaping\n\nThe following characters have special significance in glob patterns and must be escaped if you want them to be treated as regular path characters:\n\n- `?` (question mark) unless used as a path segment alone\n- `*` (asterisk)\n- `|` (pipe)\n- `(` (opening parenthesis)\n- `)` (closing parenthesis)\n- `{` (opening curly brace)\n- `}` (closing curly brace)\n- `[` (opening bracket)\n- `]` (closing bracket)\n\n**Example**\n\n```js\nglobParent('foo/[bar]/'); // 'foo'\nglobParent('foo/\\\\[bar]/'); // 'foo/[bar]'\n```\n\n## Limitations\n\n### Braces & Brackets\n\nThis library attempts a quick and imperfect method of determining which path\nparts have glob magic without fully parsing/lexing the pattern. There are some\nadvanced use cases that can trip it up, such as nested braces where the outer\npair is escaped and the inner one contains a path separator. If you find\nyourself in the unlikely circumstance of being affected by this or need to\nensure higher-fidelity glob handling in your library, it is recommended that you\npre-process your input with [expand-braces] and/or [expand-brackets].\n\n### Windows\n\nBackslashes are not valid path separators for globs. If a path with backslashes\nis provided anyway, for simple cases, glob-parent will replace the path\nseparator for you and return the non-glob parent path (now with\nforward-slashes, which are still valid as Windows path separators).\n\nThis cannot be used in conjunction with escape characters.\n\n```js\n// BAD\nglobParent('C:\\\\Program Files \\\\(x86\\\\)\\\\*.ext'); // 'C:/Program Files /(x86/)'\n\n// GOOD\nglobParent('C:/Program Files\\\\(x86\\\\)/*.ext'); // 'C:/Program Files (x86)'\n```\n\nIf you are using escape characters for a pattern without path parts (i.e.\nrelative to `cwd`), prefix with `./` to avoid confusing glob-parent.\n\n```js\n// BAD\nglobParent('foo \\\\[bar]'); // 'foo '\nglobParent('foo \\\\[bar]*'); // 'foo '\n\n// GOOD\nglobParent('./foo \\\\[bar]'); // 'foo [bar]'\nglobParent('./foo \\\\[bar]*'); // '.'\n```\n\n## License\n\nISC\n\n<!-- prettier-ignore-start -->\n[downloads-image]: https://img.shields.io/npm/dm/glob-parent.svg?style=flat-square\n[npm-url]: https://www.npmjs.com/package/glob-parent\n[npm-image]: https://img.shields.io/npm/v/glob-parent.svg?style=flat-square\n\n[ci-url]: https://github.com/gulpjs/glob-parent/actions?query=workflow:dev\n[ci-image]: https://img.shields.io/github/workflow/status/gulpjs/glob-parent/dev?style=flat-square\n\n[coveralls-url]: https://coveralls.io/r/gulpjs/glob-parent\n[coveralls-image]: https://img.shields.io/coveralls/gulpjs/glob-parent/master.svg?style=flat-square\n<!-- prettier-ignore-end -->\n\n<!-- prettier-ignore-start -->\n[expand-braces]: https://github.com/jonschlinkert/expand-braces\n[expand-brackets]: https://github.com/jonschlinkert/expand-brackets\n<!-- prettier-ignore-end -->\n", "maintainers": [{"email": "<EMAIL>", "name": "yocontra"}, {"email": "<EMAIL>", "name": "phated"}, {"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "doowb"}], "time": {"modified": "2023-06-22T16:32:08.502Z", "created": "2015-01-12T20:32:54.422Z", "1.0.0": "2015-01-12T20:32:54.422Z", "1.1.0": "2015-03-04T15:11:54.539Z", "1.2.0": "2015-03-04T16:13:57.996Z", "1.3.0": "2015-09-18T14:49:40.290Z", "2.0.0": "2015-09-18T14:59:10.292Z", "3.0.0": "2016-09-06T19:13:54.031Z", "3.0.1": "2016-10-18T19:20:27.458Z", "3.1.0": "2016-12-14T15:48:47.422Z", "4.0.0": "2019-03-27T23:42:10.038Z", "5.0.0": "2019-03-29T22:38:43.850Z", "5.1.0": "2019-09-21T23:17:05.017Z", "5.1.1": "2020-03-21T23:16:48.765Z", "5.1.2": "2021-03-06T21:21:44.487Z", "6.0.0": "2021-05-03T20:15:14.160Z", "6.0.1": "2021-07-20T05:49:06.969Z", "6.0.2": "2021-09-29T22:14:24.467Z"}, "homepage": "https://github.com/gulpjs/glob-parent#readme", "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"youstrive": true, "flumpus-dev": true}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}