{"_id": "@babel/plugin-transform-modules-amd", "_rev": "128-a3081262e56524e977447f722cd79346", "name": "@babel/plugin-transform-modules-amd", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f5dc4671c7d0d63b68be6ec15ed409695cedb719", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.4.tgz", "integrity": "sha512-hAhy5gjpIkt/xSSCCMCysrYt/bWlhhvjL/3Ep5zpNjp9ixnEkntai+1BakI4jaJiUBYQrdRIxgJIWKCkEM2FNA==", "signatures": [{"sig": "MEUCIBwzt+HYnPvFoWSpvstDvVO0HX8QqU3/HTsIgobHVquRAiEAq0qxnkLQ9ozyUjn4yhhcTtcR1X6TSvyo+sT09Bxxxv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.4", "@babel/helper-module-transforms": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.4.tgz_1509388555482_0.22079859441146255", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8539a166805fedd07a48461dd3359e6b7bf40b9e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.5.tgz", "integrity": "sha512-FPJ3j1A/iPwCNp8eSf/0RCJaL1ytr0ngFPRHeA/BJZsvsohsGHDdU9wEyzmP+u8TrNBMqRiOpHjX2AnmleHJeQ==", "signatures": [{"sig": "MEYCIQDN84VGFyZKjPnzqzXi0lJ/hMEbhWsPBqcORTUwo2/yGwIhAMvZychR+RdvGzA7uL31oMsLgrNBKXCyEKMzZZGy/Mgl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.5", "@babel/helper-module-transforms": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.5.tgz_1509397054205_0.8795963807497174", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "35c4c951c97b3d80281659d0ee1582b37a81d564", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.31.tgz", "integrity": "sha512-k<PERSON><PERSON>zEyZryelm87TDULsNDGxHD+jUdHpUl1KbEApiZKoSpm5MbNS1fA4iALmBuxqEiEB2d+Hi0LxIC4fGZyUYA==", "signatures": [{"sig": "MEUCICjS6kxwP+YEVbqIXbgvb9z1SSiJVZRR5QCqBP2wzTsnAiEA8+5B7NQYYp1A0mywyHzm6GZ0EnX6+QwvPRNH9HaUP6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.31.tgz_1509739455788_0.21644151420332491", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1cf9171b40d9e1f9b3b451429d06df464819e7dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.32.tgz", "integrity": "sha512-JmwSwyznAcOsIvgD6uVGA0gB4pAVfn5KveUAKeF/3gWbCmaVtsb8D3N8VEnOmzG9C68/qdPVYs07fFeyyrShCQ==", "signatures": [{"sig": "MEUCIHMAaEA0lJjdZg+brEdP+5Lo4/8VZhlYzZJB9dDMnPiOAiEAuI7WYjQBESMeUAMWEIZjiXbtTMjQIybfe1XiCAxsw0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.32.tgz_1510493641347_0.21940661291591823", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "94d660ec954a23869e49f9d6cf238391a1e2a50c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.33.tgz", "integrity": "sha512-6jD7J6gffyZlXQVG2lWrZgzbCjQmR2zpAm7BnLHb901x+Nb5UHb7J5jKC9UnSnnB86hfEXk8/rOVRqJsIUYLAQ==", "signatures": [{"sig": "MEUCIHnPcwZqmvpCUREJWiC2yDFgOSa8bNInPM7XmxTjjrj6AiEAslmQUNy1wvV2NBXwmtEhIfIl4QRFgRuMC+Z110gziSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.33.tgz_1512138559252_0.6865929206833243", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "31464df8455921db3bad0dd6c9792ceb4a4d5e1c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.34.tgz", "integrity": "sha512-9T32hwmkBgrkuQk0Lu9lLGNIUFzvCNMN1GS7/M/3P+MgXj610yQTlYF1a1ED1qLP1xFR1EbB4B43wbtIyXUmqQ==", "signatures": [{"sig": "MEUCID3H+tDCkLDYWBSsyrS8niwb88VXWPugCjYD+Ub8Chp4AiEAmCeYkPLAgkLkoPUE8XEn25uSglxaWaq7uF+COu6IfZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.34.tgz_1512225616703_0.671834860695526", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3d26e48a481938983b9f067daf9b3458717419b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.35.tgz", "integrity": "sha512-0zC8M7Ud8kj8s6/lhyoXM+R3ted522NEe2/EUGjOjhsekyyFs54p34yMPpN/OxDjMHPHpSYmEgrV0HFdtOdx/Q==", "signatures": [{"sig": "MEQCIBPMHWUClNsojfnbsBEzJNF/+au0vWCthdZ2jKmQjpq8AiAOAWMhvD4o91uaR+OglWMCS38v6Xq4c7nDDTzwXcFZWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.35.tgz_1513288106964_0.11674768035300076", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fa65b9c051bfd39a35575fae7a17e0ee886d094f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.36.tgz", "integrity": "sha512-/rB1vki3iPfurd+r1EneEBK13TOCE/6NYgqVtqW3amlzLWwzr39cLf1+6DZqQCdo358hHHC5sIVoiXBU+GM2Bg==", "signatures": [{"sig": "MEUCIC/IPZJzRxeQdfQhMH4yACWaAmzrAPUr+JAvR7tE6yI0AiEAoOd0Ff9nHD7Ms6em3ZDYCDd58Xuu0CNG+nwWWpUTHB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.36.tgz_1514228738925_0.688713242765516", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "efdd1014d6a8d7b2a53750a2ac9f77b1ce9c3600", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.37.tgz", "integrity": "sha512-UF2OuQ4sjhUEz48Ar2wA4NS8i7CkqkoZ39xdmYMbXM+NoFafdR83+UkdWpFhlR96v+RwaPKZlOj3Zg+ZKQGo0Q==", "signatures": [{"sig": "MEQCIFV/gMiyYBtS3NnHDp+8eaxTZh3F8ef6hNlUOQZnGcJsAiBgiMen22padkgmEjTja1RMniOch+CV0WwaA81nquZbgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.37.tgz_1515427422043_0.24011913780122995", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b7bd10a994802629d469f99575e943e0a0cad516", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.38.tgz", "integrity": "sha512-BafGOUA0G8qmClL365eUqs4ty4fVeVUdwZhWrYvLuxDInOCwYmGfWk6se8Gnvg4CGKZmlKojpzwwJFNHSjJKXg==", "signatures": [{"sig": "MEUCIEv3qWZv30+zZwQ1rieLxEY+Ho+kgdYwErYNCTHXjK30AiEAjqy5iSRXEepVeJLHabvbpaM8Ie0K9sa2dBsk6l674fY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.38.tgz_1516206763126_0.9312229785136878", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "130bb5c05411d5be7d17ff7d2740058298fa23c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.39.tgz", "integrity": "sha512-IrNSOZyikMb0SUFH2dkvjfYB43ioN+atxIyDptHi+Riq/bxzsDjSwDTEal7y7J60p6LO0KVM1jmWa1LXkJ7gsg==", "signatures": [{"sig": "MEQCIBhfPhxQ12brB4CfQyenL17jqIVge2U94VXd/ILJDsM6AiAlHmwzwsQkCzXAU0BMaemK36Fa3FG3594Q56n4PFOmZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd-7.0.0-beta.39.tgz_1517344124606_0.1315180896781385", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1882f1a02b16d261a332c87c035c9aeefd402683", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-o/XTve9C+M9203MVxGRBOXNx4f9DZGiPLbwPPeDobdtw3NKHUCymFNbh9xxMJy0MPMEe8JldxbVwGy2f8DY/3w==", "signatures": [{"sig": "MEUCIF2acppuv33kfsxABNfMkRaJe0lMR+7RYi7A/m2wTNIEAiEAzRoKbQoYsb+RH06ls79SUjE91IDovaiGcSym6DlkC/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.40_1518453765542_0.9192388201455126", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e24c47638b95b7d60a5390f2a7eca0c6d68642fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-+1IUcj+ns/HtdBa/j4YyYyYoxzUXFKr6iXNLD8YZk9ukp6UEfW1fPSTWL30KuKrOOM/GNzb45X9FsWX6p3Aa8g==", "signatures": [{"sig": "MEUCICmmNUEv45Vu6CEI/FZ5DQF5ngJMUz9XRgHTJs9B0OxXAiEA3bZsCi6wrkEPt6/d5Ja9EnqaZjudB4iyJDSKaDqi8/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5373}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-module-transforms": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.41_1521044814347_0.31079502816963056", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f4c634f49b5051abf6cefcbae100b41ba1369eb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-JCNM+WPKImpNHVLZG6x9F87AyX+K8KpMCkuCqyE7YgxAJWB+5FHVnpkdF6lprDilE/LXGwplmNb9OrWulMss2g==", "signatures": [{"sig": "MEUCIHGg0v3KTezJW/yJ22v4mFuqrAvMC4vFk0ndWn3F9RkYAiEAs/GoedMPtfEVTIxwiHMYTu7rW4H+QxvGwGZGxrS0cBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5373}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-module-transforms": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.42_1521147127699_0.2738820413619205", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2b41f9bb758d2bd852f50446ecfaec9d04b986ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-MOXA/xuh3NmtLDrtPDjXuUzVUS4VHERHFya5v8Jp4j2BuFbisZ0iBQU1n3sUWAndR9z4GJBRkuexUFXvps2Gtg==", "signatures": [{"sig": "MEUCIBsrp8m9FLAEfCdZwu+ysZkh9awkjyS0yNNMCf8Nv2IcAiEAio4E4amiba13vrMWWkA0a9F8accQ27adbaOBVpnKZtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4925}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-module-transforms": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.43_1522687737196_0.6970905125806872", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4d2df3f507f00bbbea3bc3ee07505ed97df1f22e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-dVaGOhwv8VogqXOtIKoFczjCadSf6PMEafgumE+3tOmzIrM1gFplC7rDxi52RKSOxrl8q07Lc1PToh4k/7+nRA==", "signatures": [{"sig": "MEUCIQDn9D+cl00hRquezvqzxKma7Cuqh1kLxNgJtC+5NfekcgIgIEMl5byy86ieuh5DRXIFQYbxSPrnyVbkdECHkHvJeRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5767}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-module-transforms": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.44_1522707637955_0.01348740073835697", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aec99535272132d324de290e5f3b9604d80adf48", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-nOLMvLme9UwKzqnYsPsCL0Ek17lzBx3hJbaMAB6LEyqRsD52pKswnsstRWb5sALE2CvpJ9KaaOzatVR61YbE5g==", "signatures": [{"sig": "MEUCIEERsa/PrLMs2fNuZBPaTt619Ej6aprqT+gV3bZ1mgW2AiEA11fOHsKrpLWNxLcyULaDOk4Ez7ryu/eCaY8qj5yIMvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3KCRA9TVsSAnZWagAAteMP/2tnH68dhtzon+cvcMGi\nIixz/2iRMUYstdyHnCzVXqGFS/fcIV9ZyL4DYnNTwASxGxIyuQQ/xdDWJ3dm\nq/jJ9drcXQDSytdsT1IHH1jIsiA12Us14EzoZgM8wgSMIBam3RC96H75dlDe\nGp+PfsXul/IVJH/58hd1CXhLJLLgy2G68UG2D/oQkfC+S2Ytd0PvEnX2w7KR\nZeA7+gGQZNXNaFyZBTaS51njiaYc5dDaH0PQO37Nwu1AFQhDdA8QoxsAmasf\nhz192wwuzj5V+qTukaIo4nwEP/PDpd6ml/jytGeORyk0EY33BS3njBfl5Q/y\nu5p8JU3oeIw3WM5uOHAq+EIXPbrkvcGdPGGB/fyob0znUwixabBMgcb/X3Rx\nss316Ei4cIICEd89wpsiASc7iQnHAhIwQbVoyg+jfTdNbG1uCd/7sEubJY+G\nAYud/aHJZDw1ysU+AtHguLkQnq4F1p/oX42EdHrl3vDtWbb08coUW2CoW/Zm\n5s/IhVK77in2yYDrboBZAcJXW1tjn5MADriEVjl7DD8dS106yPTMzcY+rWv+\nVwaRtLmhElnAyty/exVpqN/PBB+iwCIt5tsSJBHrW24uhW4JAftJ2tLpRkPG\nDqospDFcq3zPiwsyqR0z2dtBaVKfVk8z+VyoBLSr94rvpXkmCuTbaX+lLq2T\nMxsA\r\n=JQQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-module-transforms": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.45_1524448714087_0.4685537759070404", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "01aeb4887c7df7059cefe4a206eefdf190c79f48", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-GNp7F3EqJlQIMY9sfoDS03P7EHa/+Bdy6PUugTIo1TjZQUnCy3JbeL2YGI4tU8XOHJGHiBKRjFMpeOsjOSmMtw==", "signatures": [{"sig": "MEUCIBESNsztuWyQiUKe/8VHR5hN9HdToZol5gWPDQ49rF0AAiEApmF1PtiweuBsafiiBahfOlciNSTYm/p+kiYr3nVeV6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHzCRA9TVsSAnZWagAAQO8P/1NVwbsDP6GN4Vt04vjc\nQxpkfYxeo0VslO2mb90gvToIFVdD/kZUVtn9R+Nbtmis5Wq8Gbv0C4Bk5sHX\n2sZ+fFbpq5Uz+19gi7LAyrqG59QgthSTmLcFS5IfzEj78Q0CdVjs+hsQs5KS\n8MDnCyBngOxT2ZGJy7Nkx94ldrqQW9dGGP4bBbmdVvkbesUulQfNJD0SPgmk\nyT2tIOrJVuws+DEGfIxiOdlveNRBNnHuppXcw7zZWmamP52zPAzhyQxWyyqd\ny3wx1iWH+1z4D4UrJehoCdajV867eDQ8+BAnM2rjZvtE87mxnKgi7aCPznDn\n5mLZC4TGpAcIEBMfXfT8r5QwKiaNIsyW8XE1Jsx6aMLOJC/gmZRytBcKomW8\nQv0Fr/Uf6dyeQPJqifxCF7FzBgGGc8GhehUGETISSe9Vz4UJ2eSXZjQUBbZn\nW1p1dhOlOzh09iwadkOeFxAxPRrfIAmJMCJO+2zFyjPmbyPH9am9QpAuTtgj\nvtMfEoh62Qkfv+F+ouUdHg+/fZh1k45wHg1BmB32WXiPgddDWqwftiV4APci\nIAaFzczPjVx7PzcOkGq+G93ljX1mSO/gvpmo6IP5ozmH3srx3Bq9Sw+xoUvz\nXMfATz4di8Xnwo6k7kkAoRGnVDD6GR1J9ALyXlznaz0U0r1T/8I0VRyud9VQ\nrg4h\r\n=y8bP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-module-transforms": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.46_1524457970732_0.35863069613569265", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "84564419b11c1be6b9fcd4c7b3a6737f2335aac4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-zW84YqQ5Kt8+t5pYrnFhjWQP2w2wq6Nxz9pozxpnvXP+lhqyJPqNdWM1lcVApORpWL1BF7BlgP08yk+5MVRfGA==", "signatures": [{"sig": "MEUCIQCKpt2ccSiMhE1Pqm0QDRCMJjXaGE1fWR2LCjn0wYb1tgIgMC5l4poQbh0VGPcgeRuUaKL1Llc8CPAaT0a4HCxfscE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icwCRA9TVsSAnZWagAAPLoP/R/m7x4/K4p8de9Nf9jU\nAx4ItAwBA0wF2Lua6Q2tZw5PJdQvveGUTNp/hEnhVTiRDnRFz5mUmq6UJLWA\ns/UtKlQCa7O3I2ReSSIEh0eC5dhmjW1qe9MYmuPtvklCIBRedAXmnv/2J32V\nYg/86yQN6FioK6yz5JX92Td0VypxOZtBwZ9oTfTnytwakPhyPleSxfQuSSse\nGKTPEtL5smaccUn2gH8JXHEnE1IscAJjrw/JmC5nWGUX1jNhs5qxpzqgKiK8\nNs2m6bBeiEYIlL7pX3j9SR4gMVtXCLzC2vVUA9fiiUHbCo+aD50vhAgIuv7Z\nDZ6tckTo+tpwDgnBrz4h8E1wDD9RVIkxVh4CnouqT8ptdDP4kpeHgy0UPHGH\nAPrSyCq1V4mKyyRH+Kh5GwHi1i0nG7VQZMatyfUg7V8IyEMXZaqdyy36yD7e\nQ2dP5ZQMAZDFJ20pbhYpCpjD7Ft644ihExYIIHtvbiYxoPDZwDhinqn8XpQp\nMSYkpwBGiaoQMIZbJ2jMMQzqRg/CvRNN3wGP9/niFCsN7DD5sSA39hJyQx4/\nyO7iOHYtErkWTFuTcnjlC+HiJ2sAXhXQyVN2Uqc94O0DZHx/saVu7534Mqw5\nAqPtS/UqwVRpzeF1Gl6U8aGCO+T5pEoq+9l+hcLgx/U6XNzOulFb3pXHJFUT\n1NP8\r\n=e7IO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-module-transforms": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.47_1526343472580_0.1367676211104205", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d95da7b86404df07aae92b7246bfa8adb7cef97e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-qDITOhRMjuWWfAE+5tk/JqnONG0kPU0nySTOneXFA7RaTYUmL6GJZRQqfzkvQraaeGsgWuz4hUqHci8HK6A+Mg==", "signatures": [{"sig": "MEUCIQCikpEivgOS3EZi7uCV1E74hylmKbJ0+d2MjMiOSBwSlgIgUcrFVqsEV4NqcaeVng30DzszycrM9Kiu0tc3Jd2xsJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGBCRA9TVsSAnZWagAAsgEP/RL6Q++TjuSmu99SPvWF\nzgNgs9RRO+9dr+AhYrZ+bPtNQ5qmMBy3LEL8Xcc0Itmv1rVffpcJfY2BP/dW\npr9hqtCk1gR1Y9MKDKSlI9P0jQsF62TTeDBi1g9fU9nkszWTz23HCQ8ErWNz\nTodN8wsoJMVxH2Wbqa3JOzVAE6A6g3wj1e6ykqsZ/nO/6u0KnVHGXvHIIzi3\nuM6JFkDndMiuOYlGqZY7I3CwP3PqzNuwXoW2LN7feHyg4ICVLqROSsor3KQb\nZaBZyrLBVlpJ3jSjVtLcCj3Rs5ASFzcDh1PCdQSMgJWRvXSjPTIj8uLn5fr0\nGgS49mr+vM2jVv+wlwj3ubmphJTJAMvvWtNe4jZTH6EFy4pt7Vm/KGLAIEGa\ntu2342878WMf3s6crHWrXAylVdKNpiSVEET2adT1CPYwWvfLUJHktCpajWxS\nd07eSRvTlCQoYq45jIs5DsEBCaQgvXEFAUZAPXNgu2YsLGbsa9imdpX2Ysba\n7TK1IHgz9DqS/AlwgdUa6aammIaker9SB5Bpu+p2OHxl6Q3azD2Qkz4jKKQP\nnDEDl6mnPgrFOZ5g/VIt1FgXhpaUE4pfQ3XZv/0wk9BXDcmgFgRONbV4jgl5\nj62rxwqvhvD3t729PTAv54FAlb1l2TRE4JrS7aJ+jbqymCcHEjHdOK58Y92z\n1/5K\r\n=TVWt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-module-transforms": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.48_1527189889069_0.08254382883003708", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "16d07480954b0415ea70f1ec3edbd0597bd3ddfe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-aRz4wZSxO2flANoo8JASXpV8mWWaGELllR0ey6jP+I8AByzmTnJuVybE8N691ZNURuELWoYxMQvEacBSK1PVzg==", "signatures": [{"sig": "MEYCIQCovLO6ixeEqc4PTOsS21Fj9D5sr0XCLWZDZcYUm9zSjQIhANdKASqP9OS8PCxddJidlOCxZU5wbcBL3eo4BoqFEqz1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQMCRA9TVsSAnZWagAAAxQP/AxSRFGQPHzboWxCZn4c\nA1S1M0CGeBqHgc2ho6jst7jj3ulSa9v53h+gq317QW8t/VU/MDN01BL9njCD\n3M9nha5uj39xXKC1aknH3iC1wxx5Yul7u7uH7Ev4/tm9CQ7Lzt6cbRMGdtq1\nASIARGOpu7DPggXELg/bzt3YMh3JLhEZO4Jjr+CeGBZtQJEiG1MOsKlZDGBp\n0TJDoGwhdt4EhbuUqum5g9VS6VShXVv/545i2DAV8rbEweKLfmmhZhaIvExg\nAm2VFJbm3w9qbobHVcH6OWU7/eE77facjnF/5Ww2HhctPG6PFMke1DPs4y2p\nGiZJLiCq+Nl0V5F+n6R41rRFFk7RiV/aKR5kV5zVSNXFeYP9Lb6bPMlYBD4c\nXMcyqo3fMLbShC3oGP0zhPQA+TDuYnEv5pXEcojtmHUVt1AC/97KwIKhnbMf\nxCEZnVrO2tt5WqWquCcgsAX/cRYDoSFlQUzXUQnh8Uek49p/PBtf4phbIH98\n88iEREsRms2H3GPGVoUNh4MQMAdVX/8kTAv+pAh3p4YPklGft8iRm7xMdzkl\n9fYbFt0TpiakAtL/zvGgP8bX8mtmzmiFP0cOTslYJ3DjYA8+wvwwsAvFewlB\n2zYzhef6VcoF4ENoEqIYP4Nk65NQ0SHBSs1YBn8fzGcTWweS17Teq/uSoy6h\n5ita\r\n=TmBJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "16d07480954b0415ea70f1ec3edbd0597bd3ddfe", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "3.10.10", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-module-transforms": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.49_1527264268223_0.27599786497252365", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b827cc951ccb5e751a5759820a4a53cd0df749dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-DG1WYZBsG8YYky7DkvXTEFtMU5FzvqhOA9TD5Va+6a1EG3ck63kUJ+RslQwXYsQBIjji8NhwEGkeOIJWStlMcg==", "signatures": [{"sig": "MEYCIQDQV1VVWXcD9KAOrchoNmURqF82S5nNwx6T4bRmxr8KOAIhAKBChOyIQ2ab67Qspl+c4E75nviFqH84SkNMscVkHwJg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4422}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-module-transforms": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.50_1528832885731_0.7587068627469753", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f68a8be7f65177d246506a3914dae4d66e675a1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-OoMQuQ1KuI1nUk9F2yXTeShHf/GAmohY3RGK9iOYV378FJzGq7eHLwgOVFZybTvbpC0fQp/EqNIPOW+JZToKBQ==", "signatures": [{"sig": "MEQCIDUxwmMBVh8TNUhihGSkYDZuFOjR7MO2Zp1j++9Fk1d9AiBX7oF5dhROiQpwbUYZkRYapu5Ali+YyZqplOfJBl7ReA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4436}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-module-transforms": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.51_1528838442574_0.42463594709668406", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "654b6f3b40aef9d9a83767820d75cb57a256fdc0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-tcM/qLbolKz4C+vHqwY9opCAFAR/nepfBbal6KgwrbK7nou0859iw8rd/FvTXITIspWcT3LVotpW5s2QYpvghQ==", "signatures": [{"sig": "MEUCIEIybGa0QU1ADlCgbTrySOQNCAhobsLAYq0eUk3SMPR2AiEA1PKd/5E73D9Fv3vEXBPHiyZhPZ1AyDHhB/QqjCB2Tcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-module-transforms": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.52_1530838786898_0.9139810032743478", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5854d739e679233a8877c0b418269c6beb7a322c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-DldT9fmUfjr+pY1/fLidnMq4wk2GiN4114oWshYHSd5Eachi5BkfM6Ao2CsTbDL5PyTy3rdIRB6K9nL+7ze0YQ==", "signatures": [{"sig": "MEYCIQDHV3tSYgBTUPTCnpFwNsIJ3D/n8+gpI3DMel2Rn5boLwIhAOeeL9Rm5zBJjLVK9EhXRmgPn+rC7WCPwjcz6tcMyaMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-module-transforms": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.53_1531316446332_0.5032411570267401", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "fb50740741420bb485ee1315d2e1133db4e433d2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-dKWwENRIPgF+ZIU+jqsZHEpkb1obxZ3SfmHfNJlHpBOoGsx8bfYoZ3sy/GHmohq9DS3NmuNSGVEn7qe/hloNmQ==", "signatures": [{"sig": "MEQCICqRoz2dZ8WvTC2iwoTOm/12+6G6J5E5QhXpYo3zcD+AAiB+OzUT/N1SEd2yobevP/aLbfWksFxOeuqeZ7Leya0SOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-module-transforms": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.54_1531764027902_0.711301090609582", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c8b59b84d6f4987512667c6f9410af3ddd562e12", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-8M+x7QaC0jZuI4fAHv9ZazsE7oIk/hODx5sdNs05h37gU7jQnsMOY7aeAablax9ftql2822TufweHECiJJMiwA==", "signatures": [{"sig": "MEYCIQDz77GgxHe06QlKLdv7KnY2rF1mF3p1wl9rL487Wbxn7gIhALhPu2FtanQDKhzuWyqXABbqN6sz8PfHo+FxesbZcW5n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-module-transforms": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.55_1532815673063_0.3862485552216808", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "000ae2a3f41a9dd43d55211df3fcc74ac8e17418", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-qbYJ9Xfi17D6GWzqs6AIKbT5oN5A4ONwxMzqkvr501ft0JognN4Hc5W5UAyCGdNgY/V6+Qdf1t8Opd+zTcS1FA==", "signatures": [{"sig": "MEQCIF6YUcq/7B5ul0c8Iv4fbgXUI9xklAYi9GYgx2D34LSIAiBCmM/CgLi/s5lpxkVZ8mX+WUY8a/cFnD97Zklo4Bw1+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPycCRA9TVsSAnZWagAA/l4P/RTnBGlI3/TkdUDeoAg+\nKQM5PE5kJLdjRTnxbarDcMiXK20n5tSA4PJGNZs33YM5xx+4DeTpQtyrmZXQ\n6P1vhu3pfoQc195LqUXuYYQv/eQlqMSLZO4gHP7fOJHQA2zyL05EtKFuDbHG\n34EDUSqg0GOxTbExAWsDEZSx3SYjX+T60PSjbhxCgUOI5qRTTu84WoQjbXt2\n9yXjM7FVTOtey13IcXB1YtDFy9ctLWYjNU3xfguZk0YlniOdbM7JDqW+AtJc\nTruHXX62f3WOwDuid4AUV/XPdJVgEyJcrNJOSzMPWb21X2NnuwGXMUspogJz\nzqRQ4E9l2pT6ohcM4BfkssRRL1iaDJi4m/gJXD+as2IFS1vOPB4cmq9Y/yEg\n0DFTDvg8fghO6FOneAYhEg78zVvuiuxHPUtkqDOv3DgtLm2f26g2N5saKz1G\n3u+jNTvlbEE6NRDp/Rqt7UKsoJttZ8AKBGabHrZsr958REUxJDbJI6zwbGWD\n9bBjqlkfWWStHOPfHHlHE3y0mEpK/n0SvNg3ZPkP3OCM4Bb+TDKX4kyF7vce\nGQtaPRzbw3pKG9htDqsxCwrxG+yHyHiWjeyUWqUyLcAfyZ0B+DZBHPZNIOOW\nb9rCug7v9Qxd3Nuznx0OkbImZFd97KO/6UXbPyygmjcJ6DCLYg5Pv1aVzc3+\nS4ct\r\n=qD9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-module-transforms": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-beta.56_1533344923728_0.792821967099002", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "92ec7ca662a616cfe00e77e2661b34615fd2b5fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-HZS05gRijG6JcvQ+uUkNtstEDNIX/uYXvxxK6mv7DydYEDldgBrq3jAR9YIUHTrGzFEQcjbpveuX0T/PbGQPiw==", "signatures": [{"sig": "MEUCIFtrti86RIx8UrOXnpU3557xu5ZQ88EeIWQj9DWpUROfAiEA+ZMaArq/YYLS6+xyqWSVTRoBI6gV5PlwHARUQ8US5IA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUCCRA9TVsSAnZWagAA3rsP/iFMUr2hkM8FV43eSYdr\nZEgCyAMFw23P8mSF6XhSXbwEv9nsoTc7UAz9OqkIxDMsgRYVn6WIAjKIIMnl\nZlqBItwO1WrV9BCXYoxHHLyrhXRkC8PryC0zK1IMyOBvdrmAicSz+dQOG+Qz\noFMc8TzhYgPbUGUEYoN1JeZQ8pej3P6o8R8dblV4+ArY881RD3vzO+wh4tGk\n2fybzcuCZ2fQUH1DwlidRjZ3P0FEzmFRz9eLgVi3wnQoYMiIv3ayhUsEmB0e\nDprxpGZK7HT9z0lkY1onpACiJEcAnHuGP5xKT1d7FPdNBT0Od44okS329Twj\niKduLzU6KedCvsZBRIpQUzkT5Nd1KpiTQcnBGLJxpWboXojtPLLGt5cVcL9t\nN+********************************++PZF+MWiEL12riMaBZtLi7cRM\nCcfQA3q9ZGIoiJ6yaNoeLwVM6e9n2edZB17xZeBzoXXaZWR3yKhVGNag1Zcd\ng/Z0aKs7b+SaEx/zi2YWF3hltM2BhYslYCRl61Iqz0tuH+vEA51/ZmBQ24k6\nqIQ6HUiyL2SQhs90nF3WJUUYVBLcqG+fXyC8+2Obj8oAUUSSHFgA+sQAVCvB\ndBpKh9ppjRjSfxNX5Fi7uR/CMmvmhtXH9Y9vONXFSm7tFMJcLW3F6PqD+yxo\n0tC8\r\n=7ppb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-module-transforms": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-rc.0_1533830401796_0.1296520515902726", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3f7d83c9ecf0bf5733748e119696cc50ae05987f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-xKIF2ZAFOZRgIhEeW6zuyieyqfjft59NaHvb2C7+N9omdFDVkrx5ZeHVLb8y163a3mUb2MqJg1PLfZXdwvz1EA==", "signatures": [{"sig": "MEUCIQDotUiMAQGZKgIgjUZ1dpwdnKxiHxRD29SEuX+gbCGSdQIgEAY90NydRw9W1ehG7DpFg+ATnU1BM/9pcqonpXdgR+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+VCRA9TVsSAnZWagAAF20P/A1tFsnXuL9wR0N0snhH\nV28Hk91Jc6h2m+/HSyJdBP1GFOscxJFjyPv+Heq4iVJX1LIxvgmH23nsndHX\nxLzk7Ij/UKOQNb24ZmdPM3kCZYbY8eDKIWRfl2uXL5zEzWxNrvk1SbFyWH8X\nIArHzHa9Y4Rb2LQ+DOVQ+qGDIg6yDBdEdCJHjw6hvbSnnmMyfUMx+vmy2sja\no/q/RsllrOYKzsaMnZaskFEq3mhOshAq5Y1wf+WbpS82VHFAIvSVq35xmSXZ\nfslrbV9K/LGaGTQMH3ofnQNEMYsai2XEVcUKRWRGsZNQH/z0qiHjFNcrsiT+\n2ZmBYaKbdK3itWFv2s6vzvvOfWu2EGTAyWOxl2tCX3RnRVBOIQJPT8eqcToD\n9BerGDHXSr8EQi5ytJAV26gfvZ2UwaMuR2PN3QbBCZ1d+LiuHhQkQ2DZB3RU\nzrlewLbYTu5dCwWmGV3vCqlrLzOTKR44uYz2hNeNxzWEAsbLy8Ua4BuDJBMZ\n6TTnPGL5kFjdpNlrk/0AytPZmEqbFI9dyIh88z2v2C+u88nWluyc+K1W5Qrb\nbXW3n349lh3J+FioViedFgRSRr1TWAI0qVV2lK7go/ObGtfn8dLV7Tpywu4C\nA1lDmvsuRJwVGQCxS8+/c1eJvjdqE358lW8MuqlV9jq/uktgiFhh6wRqlubv\nG6M6\r\n=m9og\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-module-transforms": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-rc.1_1533845396674_0.734556824441368", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f3c37e6de732c8ac07df01ea164cf976409de469", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-phr4bjRHDvLv3OK4g0NvT998A19kmqZKMrnOFZC5gvIEvdGCa18y9Y2mZPg2Jxi+tKI1lkh248L0puLUsEtwfw==", "signatures": [{"sig": "MEUCIAvBWrlv8ahhJVQe0DDn6VBAHcJEXdN4euQ0Gtg3fMxtAiEAnQPVIgj5tO+4PmrJZWZvLKmq+/eCGrYFFJy1FrzFSUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdHCRA9TVsSAnZWagAAmlMQAIRZLEMja1dNm4z0X08t\n+v2/NYiugFN7Cje/D2BbhG9ntCUe1t+4IcVztWSwvopCzchYuXMZc1ZUh/Cn\naKLLqOmy0VcRIp88ezN3Sx0MmeJS9EuAwSMMlMTLyJSE0xMSwguhsEIxRL8l\ntWVJhQLNRmAoL8VHcc/fpMlTm29n2lVwlnwyGaT6DWAR3AJZhe0Wx0Y1AfeL\n6/YsG91PNHfhKvp5exCtONoYWPfv77U0CN29SevCjrflGXp/kQihArmKdX3P\nAp5N4e1zJUjpGhNj5pVImg/uzronxsZM1xaTYTbBv1j/U9Y857XFUtlEmyv2\nyo1E302ILvDy/9oByHxZP0E14yqe7yfXz6F9kIz97jsidk03ZYPawSQvWldt\nDkYSCEHXTk7pCWy7Yg+idW92xT9pB34Ynm5aeBIaK9XTn/PDRAx+2nNXv9He\njPZDrLV3Q/CSMaweh2yGK3JbYWpmGZU169awDmKU2IY66PbeWGuk3wI0ih6D\nsEqFQfDAooQWXgdhMWvGch/k2WWyVmjE/eqGq1SMvBBkedpgGjpefLOs620A\nP/MbUxmNTucapMGIYzEBbBv1bRS0KPGkJAMtoNMIfGLncNCAp+vQyGQ+wqge\nS1nCMC4zGmnwG6jVK9iPsulXoohbSPzsulKAjCPiCjQ93p3Qo7eOvTD1s8fv\nL+P9\r\n=FcML\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-module-transforms": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-rc.2_1534879559095_0.1819313763093684", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1b4188bfaca1edccfb2787db396522ac7055a095", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-MZkr75Emc5zgHXZbthuJFU+lkEZYoC9hwZ+3l9aT+ajpetrML4HzjLgxs0WsVkgRmLQpqhbcTXc8XSWzIgeLyQ==", "signatures": [{"sig": "MEQCIB6RuC9l9k43ezlWlyGM5F/0Dz4m57ABS7M6KsWveN0XAiBKmwPehMFq7EDzzqQL0mWi+/wsuGMoQyQzi+3DDvw9Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnpCRA9TVsSAnZWagAAK7sQAJv8LLggFjNVUZQeTSEE\nfDFps8SkUlQ08T86BMYhPbUQTVB6jpHvu78jTtDSyFG4/xp29lKC+wUAp8Xt\nP1hMUy1H7Nsc37cn5Aqw6FbWvFQIwlnEEaDguiWkYOlNzr9PrVOMgBUhZLfV\nt96dHTER9BE5XsGrhcKdTRGVr9BIOB4C9DJgFS/MVwlR4xo1BAohQ+s+ztww\nLVohcjk3vRDyzYB1vMCrYiB0mbAMnCSpOxLcFdGmhE8HLyCfAxhPlC5at3//\nfQxqLkiI1Z4ODyRkbqflD3hNvQQgRpqrpA14cFO/Yz1B2tWTDy7jMEDySmBV\nTjLofBOqhpybTBOrwD9M6EeYlx3M98505v0k36InEKxMmif5YxLb/PkDJkJh\nm3dd+naUIeqQCIdESsHdfOXw30S7UA4poLmNaF7t9Qv3R37Ca+4HCLen8lbH\nQT6F7C6XeuH1u9PwNt+Il/WTWHrqEgRpQcr8iVQd88/qbn4VqJBPfeBmrYiF\nE4FaIYVu9NiIWzpvL3iJRaFVKjlG+X2hMAYWNX1oYy207eW+lXF30gcdGFM6\nPgr+saOCp9YBdswKupVBh5R1g5QCK02trKie9DwLInjXWqWklFR3MFmHlo7m\nLdcXHvit6KJd4BQbVDFndW/MUK/n24JVgx3ZthMtEqtWPS4dqU+DDdwWvu0g\nkKdF\r\n=IfdW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-module-transforms": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-rc.3_1535134184938_0.4856003632054229", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f3004c8b56630ef61390fbc3d6ac00b3ed0ef09b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-fWgv1fQ0+Q2N+FgfXHOc23zA1KhXef2s6gvOHWFmMoVqQeyVA0ACmRFT6y6zWkGAbfKSCHON8p02C7a0vZfYQw==", "signatures": [{"sig": "MEYCIQCsL5Tl90a26yAOb/y0DGYbTbPeXpbwoBZ47K344Nq3iwIhAJ35bVLZ9aYBrOMu2Qz97ci8u34yXiYcuaI4iezBl829", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrkCRA9TVsSAnZWagAAvDoQAIERytNuO9dULR6j8zTz\nXiEjiSP3YKjAGNDzDnbHB2EZuzC/Ccmu4Oyk26i9QwH65TNEBcnkPL9MO2VX\nuia1NBlzVCq/JeFw8ykiHRls5jo1PugcnIAAeFWYM2u7yjRkESLtxTpULvgo\nTcZBxhbsYthm+TLhm+fE2mzJ2fAB02H98Ruiv62xQ6ITVL1y3TgTH+kdpfvt\nOIYP1dwlXwmCeKd8EUnu25uUqVihshiWTEXe+GGYCw6809RgolagWGGIFcja\nbSGLMJhgB2BHwNVoG1sY9FwgILhrq/FxM6FGLyFcWmlMlOBz3DaQ3j4F1e+e\nGOah0SSBfkdHbAUW6bJweJxnr1u6xGvpnYUb7QB9+bF9MiOYLPjmw+UJAxWv\nzfNSaWIQioW73cK8tdnei7TgJyxwF76q1M/QkjsiHkt3hlDLEykVYGccccym\n/qwI9+tRyhfOemGKpO+ahAwIIQBoYBVkH9vJQgreO1qSQ1QaXlyOgUQ1PsM2\nvhwaPyXMFdUarJRMfQChYps+6BmkOVTo+0WBrsiU/06CsyNm9wXq+hKVc5kS\nA1RnYMjI1WBjZW0hWY098Yn2Ukbi5pnV+10LDyc3cGiV24Jgw+K3wlH0tKGG\n0TZLdOHoJz5zHsWRRMHwG9O7sPsXVL62ZT/Psfkb6loF9+X4Q3TVa4wLLMCh\n248y\r\n=YpSx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-module-transforms": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0-rc.4_1535388387518_0.4865309957803299", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2430ab73db9960c4ca89966f425b803f5d0d0468", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-CtSVpT/0tty/4405qczoIHm41YfFbPChplsmfBwsi3RTq/M9cHgVb3ixI5bqqgdKkqWwSX2sXqejvMKLuTVU+Q==", "signatures": [{"sig": "MEUCIFNOxRAdGxVQQ1g2C+Q4zYdvs3evJrgrMJidUeo2fgmAAiEA2s1gRKxkszYwz5rOORjpflcxXPIwozmaFlZiVBPtjsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDXCRA9TVsSAnZWagAAFp4P/jiuTIekAUqAiFxRwaHt\nQ7vGcsfGWuGHOWm3BraL1KpbKVOb0xYYK/8r117byZnWBjY7FQeOBtWukRvM\nzophDJ8qkHB6F+9UpXUWmE9Gc8P5FAckRD8ZnmZjZurxTNP3dr7S03SiNQhw\nFuye9dhwxIIGCofI/RB9k6nsgnUVFgHJoI9v32b8Dz1Xb0UHhGpO2IFO3u1K\nlnnryMIjXUNStUIj8F6Z1d4EmEeFuJZxiVCJpyjxf1PyQ7krxeLbVyuhBqnS\na6E950OQuTk1pYy4Iq6q1YITUPZjns98ZVtp6OLVGzXKlZw3JvgICD7mqnUa\nw1i9ZRBg+I2Dxn25Ntpjnfg22wlENTUeAy1dhxpin1eoVpRADfCy36vH2kYE\najUHnHocFxjcLZHYX18whht0J8xUI08HNt5X5PD9moJXnnLEC10cb+dSNlxo\nwGmII7p5PvQ27sG5NudB6mu6geBXMJjdlydqLZe+2AIio3oBJYIgl5k9jv8L\nwUpqyQibZZCleizM0gtAvTh9RBqj59C70dmtbLUFdZTWmsOrFVbilloSsLm5\nPAEhI3MtVXJZOTqKXiFOpMxawBYDGlEDVDayq7MiW6dL/d3zPxwFrmJVCp7d\n/lI7aAoDPpt4Dj2Am8UPDZe61LtvjaJrtdW3sCTjR3m/K+ShpoMQNLZ/X6Ky\nkxU5\r\n=ShRb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.0.0_1535406295058_0.7089858399843298", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f9e0a7072c12e296079b5a59f408ff5b97bf86a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-wt8P+xQ85rrnGNr2x1iV3DW32W8zrB6ctuBkYBbf5/ZzJY99Ob4MFgsZDFgczNU76iy9PWsy4EuxOliDjdKw6A==", "signatures": [{"sig": "MEUCIEoH/KK44bVY3lWGqBWOaJanEPrf3F9XRXbTzPVK2gTjAiEAkzNrehkgF9wmns9ql/a3evPzoJwa5ld4PxA3Fir19IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAD3CRA9TVsSAnZWagAA2+AP/ibqDpQWAuTkqvalZkpG\nyJyI+qwS+A/AsZh0ynKgAm3IA9LVCBaiY4r7Z6laPRbMDvlYBqpWRjuSTo9e\ntzskr3vlz/KEjGgp79uOtqUZH4bOHTfTHLJkBEtGDNPzEt7P4sUn6KE74ck0\nRQVPRMHsWN/jXs7SAqQ3Di62EFuAKW3RPuKXZFQoH8yMLs8C4gb55KrNvF4a\nQSHfp+QNYs5ZXN1QhClGQPf8G15KP4H+tGKeEXk0qiHNHXvY7rGHCUiXjiUc\nusGS269PQ1Lt+/mrDyeihBiomDItiikUNxGKw7bUFCG+MJeyXLjWV0QbBeAM\nKLWDpU8+ZE8gT0AX6HopDeSj76VdEX91/kLVKmQEA6/iiphJUtz1Jdm2kIwe\nzjInghQ2hhWmtdo0+xGqBg4rqdjIoYb7KHvT3Q5M/NEDrDdimL9Q1TOhQewI\nzTjCMiy7X0jrY8qCYDmCFXcrnGiZOTbbIGwWoS+BvslFmzZauXJfmGDokEcT\nj8QDJSHDo5F2qclwN9L1IV+dOPGsJaQFse6AjiTYhOA8kH5GfJ5bpeCmsv1y\nLLn0h8t4LzIytTgEcsqtsSMnIbe/lun3/mPAOKahRVlW1DxU7QuXvhX/Sgst\n5RG0qAOOWcauaQwff4D6XOlwdYlZtLMFT7/jVJZTQriXxd4cdLvibYdR3Vf2\nVvXE\r\n=upUs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.1.0_1537212662862_0.6988198992664616", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "82a9bce45b95441f617a24011dc89d12da7f4ee6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-mK2A8ucqz1qhrdqjS9VMIDfIvvT2thrEsIQzbaTdc5QFzhDjQv2CkJJ5f6BXIkgbmaoax3zBr2RyvV/8zeoUZw==", "signatures": [{"sig": "MEYCIQDFAVHSsYr+ur5yfgekxIS3+47L5Q6Yl46K0mySlJLJDAIhAMknJoZmjRSxgbVU7a8pWQCkhbP/30blYPTeSYNKMcGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2JCRA9TVsSAnZWagAAbfsP/jTSxUeMpGEahHHCH8MB\nUPyxzgGb++N0ukySefD2gJRuAMgKWsybhQxbaVvqH4iOPCE4Lk9i5WGdH5B7\ngEFi9C+MRH6JLMkkmCQvYoO5gWNFH2ob+NLI4B0wsnYjvcfhp59JO9SAlblI\ni4Jv0cnwXsRebb4QmNJUa0LM9GOEzd14Kb4kVip2OJNdAoEUpxpUu0+5sR7l\nrtPirto8iv6603MZ2gZxHaCRabZGEjRku+RHBGW56MdYDpa4d+rhYARZc3wH\nCIShx+r5m32fEhhjmx9aEAdGyOu3PEiR+ohNtXDJnKYNA1dEirFdPMLRCZ6R\n0uST79GZsxOsCiuurrFmxl7aY1Aw1pbV+Pvfx36LJbD9CWnDgDRL0EBUl0G8\nmBqTbLkQ/xQ8ttla1YspMyvbljFOgZfzw6CK+JnB7UZWhrP/r5oZcspX6ATH\n+F/9FPN2NgUNvmTq2yGKtmbzqSqReEXl7bXtk4jAMz8kGvL7ao5TGMbIDb6i\nz0Sk2wRL38gZJqxE05sqwVjjcDCTDP4D+t8Iu5fn6qVxvghuX47952YZ1gqi\nDs7rfK+CfvhxFdM1z7HYYjcFy6rkopvKwVvyfXYcq9P65dj71E+QFVq1MpCX\nJnVn4XJi2qsuU85A+qAYEprE0yzdUHhLbkTSUSfkGpwupU+/NCpmw9t1qa0Z\nr6M8\r\n=Qj2O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.2.0_1543863688162_0.1961718554174292", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ef00435d46da0a5961aa728a1d2ecff063e4fb91", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-n20UsQMKnWrltocZZm24cRURxQnWIvsABPJlw/fvoy9c6AgHZzoelAIzajDHAQrDpuKFFPPcFGd7ChsYuIUMpg==", "signatures": [{"sig": "MEUCIQCDTLUFSCgtnkTaUdA2DL+YxKjPeaSAdA2N5Mwrm4aJGAIgW2ApiK3PtdP1ScmiLZrO93uBnT1dTQpTvjl0duvsVfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffZCRA9TVsSAnZWagAALT0QAJs3IKuUxBUyYJwlvhxI\n2QBZqdJ5TCRpcdIFmx+8vPFAKXGgecG3njTp1x8LDU1hNRtmNNgbrcUgZHtI\n3BELn0NSIBkyHbuWvdx6rxAKw4odTF4W2+Dyj2r6IZODGf1t0OFkNzbORihK\nv0za5CD+kEWIrgwPQRgol50A5tAoYHGHbF0TOyYw7gSupUSmJBw8kaV9brfk\nHzjEcgvdy0f3lp1Yzoa/9mKpyCGBqsdFeEmAG2jeTcf7EHpibp2rGo5IRSrD\n4afIGjB8oAHe3+K9rIy+3CQRL3gm0dYj4mWDsiiHSpVaNl80EgiD7MTvmdjy\nnrtfi6LV8Ad5+tOHRoKF+m5OA3+Tjl5tGarCAQed/1PwgOyOujIGvvH9o/3w\nGbmh8qVknVYmtUlwfRWG1bAUWtGmQw9Ztap1Vd/1jv1GcfXNjiuLQSiFq/Mw\n/CamGMY5WTs2rs/aPxRamY99JRUUqL6ujRFf1aehRxyxViunFmYOE759OTaC\njc/caA1GSx0OWWsxy54nsqJSy1MVGD6FEXyuZ9wbVSFecKqpWfNVxrOHyLLr\naS/hlqV+VYSdi0ccS9n4zcFiATZ+8d7WEneWHN6+JZV6OMeXUDb9GpKD8uDC\nkXn4v/p7mdGNSjCA4IF4OEW6LSS2KG17FUZuXFEFEOLrhmuDZkpaFfQgZJxw\nceIP\r\n=ecKA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.1.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.5.0_1562245080253_0.016825313860392876", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-modules-amd", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "276b3845ca2b228f2995e453adc2e6f54d72fb71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-/542/5LNA18YDtg1F+QHvvUSlxdvjZoD/aldQwkq+E3WCkbEjNSN9zdrOXaSlfg3IfGi22ijzecklF/A7kVZFQ==", "signatures": [{"sig": "MEYCIQDZqpSQdFKuTPSfUBs/9Cb8w2uOtXAtjGc+N8Cj+tZwtwIhAPF2nuOGLDPvl3PLsVBefVu0j0aC+dCt6uwuYkH1Mkso", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBeCRA9TVsSAnZWagAALbEP/12BaQ9Hr6/97L3GD3fV\npaqVp3qxU5qKGOJd8Ft/Ag25bL9xRN8tPBjpqgmwUFi0zue/xuG9EqKJi5nA\nt/IPOvD/Lk88jrgXmPSRMTBHZaWSo9ShqCLo7cWqKUtRmwC//tSYHiUjz/4a\nrnabnCexB7rtAnBdDXFkCa7IRAJDW4XSI7Nb2kn8MpbQRDLnHQ03Z9F57B3k\n0IqGWaiwAb6ZRRFnIFaJR0q960/HjvWxAhDD/W4BmxqJ5pIgZx9HaAS0l2ZL\nHlxTAQKjCzkcPZw1vS31kEXVbbPyO798oDdH5l658EyaesUO67kZ921nVI/M\nM2l0V63n3fk8pQd5o1PJmXmJjfrqDC3mVHhn3tgdor0YFHnWIyJ0GR/7G7BT\nq1EBJqFmEllghD+jtl2Z2JNllv0lcWFsi79RFSh1mRbwtz8vLO/FgCX2UK41\nYoeVmoQ+8JLw17PzHDpB9W5rEsqGXVN7ido61Eb/uaZa4V/pbEncWgL+fbpi\nrpH1t79ndkKukWvTAXRFmsPQ0Sqfb3JVcEU3w0bvtggHFz1Ct7RoDXRAagXS\n5K+NsYRBnbn/+CD2FIgi6LIYlMpflZbIeB5EbcAITMSHJPBwXF2ZqRHCgcnN\nCY4Bn7eJkbSznaP21r/kzI0MhIZUiA46dfT7HZHucFve+6lSWQoSk/UPVAcD\npM7X\r\n=y3EZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.7.4", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.7.4_1574465629747_0.5488660440821862", "host": "s3://npm-registry-packages"}}, "7.7.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.7.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.7.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "39e0fb717224b59475b306402bb8eedab01e729c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.7.5.tgz", "fileCount": 4, "integrity": "sha512-CT57FG4A2ZUNU1v+HdvDSDrjNWBrtCmSH6YbbgN3Lrf0Di/q/lWRxZrE72p3+HCCz9UjfZOEBdphgC0nzOS6DQ==", "signatures": [{"sig": "MEUCIC5MxUE+aWPsUAN1fr6cHX4oTSGiS5s7c0Gtox8oW3c6AiEAuMpXsYYj0og7SfUaxwB1l0VXBqGGml4youaLD4KIMTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6lUFCRA9TVsSAnZWagAAU8sP/ixAF6DL+AW24SMcuOjZ\ncxCjDymp9S1YiYkrKO1Ut1FME2yuILf17yaNYUEWL1D1sffqL825NbFD5BN0\ncaUoD6gXrJAMBkl92RPYEWTeEqKLhy6EiiJHbs/jjYoUzyDKCdVBH0lZxDrt\nt6mtE9yyAyOt6NSJ+cr5Bi+udJ5kEwB+IHxyXZMhyQR4YXzCq8y7Ve4OB4ty\nP+rooyb1Gxyc3zFlUTG6MRkcho3UuJJR64dt7HEuVb+qymIvxB+xBvjs2hdE\nLFTRhPswPU+gUF7OgzY07TYNBNG2+TFccDsD6i1ADvl3DhJuDnoH8nAY8LTI\nUK3ELebTxhRrvDp8z+86Km2K6aqGOdzyuNnDdHKNJWkFGJhtIJUmC/c/uxr0\nGXkkQqeJMXI9+6NHJosDEZxhq+IS88+UUVSZtEaA6QBUFAiQtMyD0EaFwWQF\nJ+ThH66zzwgT9jG6VCLaQchLJRHvsJmigVwmOheIXY6ANTnK3P8Y1YX0X0Yj\n/8msCr4Pdo5WfJ6EvVzDgcHsnB5juRwdB69/9Jii+eir2z++nOuZkxzBEjC0\nMLl7KkLUnd9AYHpv5JNKITdo3c7LuTQvOJHvTztjPm+7r8YzUxC3a6UajrWQ\nO0mffckNAjInBe5VlktxowoLuKsmXgkoOU9jZdWbtETe3FFJse1DuIdChxaI\nyPNK\r\n=xtWs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d04508e510abc624b3e423ff334eff47f297502a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.7.5", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.5", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.7.5_1575638277476_0.4882804273322323", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "09f4fb47a2a7d4471866afeb446bc9a068a091b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-mFr1O3TaDL4XozM3AzNPz9AsxzzjTxwn4aOShYP5TlO+4rufvjagV2KKDTPMZTQm1ZA/C/PxJDsDekEnnUGz5A==", "signatures": [{"sig": "MEYCIQCkInafpQBv5Y/pEyvUhGC4pdYqwTFcczl806ijYEhMQAIhALcrLHCwu2cAlcbufMl7vCftoyQztEfYFn89Sgs2hsk+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWZCRA9TVsSAnZWagAApvYP/AgsZJsHRRQeyAqY+iJz\nvUUCdxoAL9joZSYVJ9aWwCsj+sgNNgSw5ozO7bBJnzuSzeBM5ojfzRXKutqL\n608qAlKPNTlRS1nvEBV8LMusqjTzf5CQyfIgkLV6BiauF39F6yN5jS0gmz39\nO7h/HORoO2XDBNIJHEHYP7efgtu+Vupa2gmq0dZNvA3ZbBY+YjD4bj7S3Cko\nYYfkMyxRKs4Y7g2xRof2xEgtFf0yFquAgG74JNHkJwUZx+TYPeVy8fomAPHY\nJYGk424eQYcCYtNBwO6yu9FVDsj0xGHNhtoQPfAb6onvsMgQLeeNP6Nigp1n\nTDiAscMgIHVh0CewZ7Fl44LfE2g8+RI6T8siJh+H5TJ6NbKVJ8dkvDZaghGP\n3Drwv9YTVRFhJzD7ech3BDqEriHIfR6qtAOpA9t9phKzK4Fnni/OWsApv7Zc\nVhmEM8SdbAT/TEp1OTwZt7DETGksWnMMy4RydiSyTREph5ukiF0sGPJkd9gF\nSSRRVvyfVf/ZY0EDSPLMMF33xlkUR2foEHYSJAKcUjQKGwZUcbV4TNASxmF1\ngLTGUIRqB2YD6gBkx/U2wuXcjSBNQytco+T+oWJLVIPBOt710oOBu2cbqxYL\n1merGUY6tTrXh+47GkvoAC52LDs5GTQgyqx/btIv05Mn95a8Bkdz8QVAfqR/\nLEep\r\n=PB0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-module-transforms": "^7.8.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.8.0_1578788249532_0.7634821167810832", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-modules-amd", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "65606d44616b50225e76f5578f33c568a0b876a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-MadJiU3rLKclzT5kBH4yxdry96odTUwuqrZM+GllFI/VhxfPz+k9MshJM+MwhfkCdxxclSbSBbUGciBngR+kEQ==", "signatures": [{"sig": "MEUCIG9N7EdaLr6CXzABZdT/ktao1N6jerOMfF34rMbnodOdAiEAm5t5gYHlhHJLjjyRGznEGOlpyG5+oGrpRaEyjI5+D1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ+CRA9TVsSAnZWagAAD5EQAKFcbJfRfoUp4rnNpDba\n3dVRjjynoAyibEUrbfET4wPUx2wFbkaq0OJs2JALBwxQwI89XdrHXpU8XAoy\n2IfvDVtaeug2zvteQtEr+5E/r/LGOPgnk6ZY7+zkzg34CbcwqcjplPQtOco2\nvLz6R/eJrf+VH/5OR9r1lKyfZxtsKWAY3CLLxXDK45vBLPqx7Ez2vW6DSNgE\nAg+xn3PXslgtMTx4dil7alNcscofvnDZxi3XwAfRxQZDWFLOoBh6w1fC9gtK\nwX//j4tY8I9/nyCJzn/KqLBevlCIfhBzgcIKkVzYtqAJ8InqsPCjuk6pqy2q\nFSu+BiQXSvsQmQFIrU4lfcATyCXqJduS1sV2d3jeM3WNWN92GzqNzYD3OYjT\n+dLxHHYmkAwpqAt8RNvldlISCkJgnOXKdCGjCjrbKDUuadsgjGy8Alk5ZHWE\nx2pvwddHn7TLBRaxWACxZdX9GgWuhwpYQXv0/9fTZeMTpMgm75z0ORDJpw21\nINaLvHyj1IlMmcDrMX9jwR3fSQypjSeLLw3vgkn2p57YYvDmzjv1S0eqXnkI\nQVFrtK3hnNnmEfA7dEBJiWPblmCJxICDMZoSI0rCMiBnGD+6ZGPII/kiYZXj\nTYXHsT6PpxqYrfA4TYRbvya6NoeDn/6Y44MkIzpfYB9P2sPpyQo9mKkUXwIe\nTHim\r\n=YD+s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-transforms": "^7.8.3", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.8.3_1578951742392_0.5143883111073468", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "19755ee721912cf5bb04c07d50280af3484efef4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-vZgDDF003B14O8zJy0XXLnPH4sg+9X5hFBBGN1V+B2rgrB+J2xIypSN6Rk9imB2hSTHQi5OHLrFWsZab1GMk+Q==", "signatures": [{"sig": "MEQCIE6LzKjxgKt5X/csinDzCnUxmXd0wzehvIO/uQlAU2fmAiAL4jZ+zr4Q1mNn27j//K3U/ve5UnFQ9m+ogE7jfL6Wdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPFCRA9TVsSAnZWagAAWNQP/Ra5nBPChCNuVFFDHo6O\n7jYezO4PVXXSsZ7OgHj929171x29Ynxk+gV0tjIb9cD+sHCxnW2anTK+nlyI\n2bXEwADmVD7WVFKBx1LKP2F0n65xkydUMCfpZPPaSK5zd2KCsc/l2ydoFAlg\n9O6yWLAsVTydSf2Yw9vbYfT+VVyX2T6WkNgVGzghxC6Lrmm14yxGqsxNAw2P\ndeV0NHxsKDKQOP+NCnKHqqFC1T1nZUSEoaMC4w1OfB9UjAQhURQf9hBbO7pW\nA9d/O41uluchA6lMD0Z6IUoqupehqGZHJTXU4euYmmyxX6UE+Bn0ns9Iz2Lm\nMM10fSN1uZmhJfK4oW2MTW7XW6Q1Eo/uuyeV//1JrLMH0WbKocObhc8Vi1K2\nOX3uomF+7uO0T2SObQ+7mjSVTyYZCSRva9cQB/5W5H8hWzsORAWveqbQjzSQ\nczPKn8+WiSpo/elqQR9AR+v7AnQE8HQvaKl/dtL8mjVBwWGGEURaujk2XG60\nMxaZJV2Fcgt1LdG6xSED458fGrewNt27hyIOEvxPOycT4ZN1B9i4aSrq9FYz\niNrvFdu0jY7yVtbdGU69+97fnLdMNTwXidvh/4Lb/rMYxgv5eU+PepRS9kJ0\nPd3ol69ws7AlrtqFKODtVyr51eaYxbc6zAJ/uNxLDcFNxLA+VySHCAt/s4f5\nFqWU\r\n=3hL7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.9.0_1584718788602_0.786243111608012", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/plugin-transform-modules-amd", "version": "7.9.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "8539ec42c153d12ea3836e0e3ac30d5aae7b258e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.9.6.tgz", "fileCount": 4, "integrity": "sha512-zoT0kgC3EixAyIAU+9vfaUVKTv9IxBDSabgHoUCBP6FqEJ+iNiN7ip7NBKcYqbfUDfuC2mFCbM7vbu4qJgOnDw==", "signatures": [{"sig": "MEYCIQCNG/2Mpq+SI+9LB6IU/8HWRbCyQIKevaiLWxSuvhNOmAIhAKGko8BjPs4WL0LiS9ZaJsK44FRAO7XRw+UFz9qk5/W/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmFCRA9TVsSAnZWagAAQXkQAJy6kl37uRT48pbboj58\npjlXNW+DDYd2dVjuzlpT48OtDBPLKgmWoJBtgGioUCQSPyx/yv5IYPebC6+z\ns5IP9QFKUThdzzD3xcmSB2dJVIc//V1yzYRVy5o54m5e4+A88mN9wao0OVet\nIJKGhbtzg0ou8K1fFCCH4DO6klksByPBofwXByScoWfsh/CoptH9SlcKW5yl\nCbRZbOospIUXFjqdlsG8Pt/RiwRa4fOeeEfRcj4Bnoo6xZ1ozHi5Bw0X7DwG\nhZKJbQ7d9AGngBY0P74rWOccjctiX6M5k+PJNxy/ehwQZxzN4hIlWWm2MG3S\nzwvq6Co9QIq9N5Eijr5wcIZMXbVACRIE5mqWOgB4/b8KZlbcG5CbU8VgAW6o\np/tZSdH6A+w6nGvvJMfuE62ckT3Rxt6IygzG3rITyd8MlRXNenx9x0e5EO7R\nFDon3FO4NCEIwgmtKwUNi2ZI4Q8gAnakvPdFd1T4mKWUs0PJfOSHQnlMDnlu\nq5r5lw/bLZQ/qq1PXkKawmvUH48Q7/woQJkaxrWckoBnUB2VHnBLN7XKb4tO\nj1vJiIV0M4B2XFINphnXoi6O7SYBvgz/OCRyD6417PFqZYGoMMgS2fsxx5od\nenZ+/iaSECB6o6NkFBNySRb4yENvnn0L19B757NVUeENM26RVvhPM5AFbhfI\nm4jw\r\n=bMvP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-amd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.9.6_1588185477567_0.33252483782916653", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "65950e8e05797ebd2fe532b96e19fc5482a1d52a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-31+hnWSFRI4/ACFr1qkboBbrTxoBIzj7qA69qlq8HY8p7+YCzkCT6/TvQ1a4B0z27VeWtAeJd6pr5G04dc1iHw==", "signatures": [{"sig": "MEUCIQD8P5OFatIfPLbqSdkXKyuYpvwJZZU8M1HXgzLsnQyCVgIgdLVKG7VkCIorRnfd8zNeTTxu7/2NLPdC8SDglKsD+1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTdCRA9TVsSAnZWagAAR58P/2kMi6zezIz4g46Nh+FC\n9fDNdJifUkVYPqyoTz5V639CE/SHicPFu6owQwYqMO3L+poDIWxjW14Ajmo+\nBkoJGj4twwTgyLF1I37E5/LZa2Tzf94BUEbNrcTBI7BYKn1gIyhiDjQwUkeC\nLWgXVWshi7rF5NlYhGOIxPriX2EEB/uKf9prvGtsnwghQVLfigmsJPQW8KpR\nuovta1CAZnWXzJKFe5BK76+CxRTBn4t4KX1W58VfqludIjpLQIPXrZH0qROP\nHzXcRRMaCbxM7v6LER/RfxXp9UrzhIxtlOnK/UY5PKnLaZnIjwrFOUjM1X90\n281zq9UoJITlc2RjxhOz+A9FOJJgb27o/MG/cMH0eaBBqhecLt3J76k/8JYW\ne8vyBHQNzd9WjFZo8rf6WEfUAi0qblaBjW/iv6nO9nP3K/0uqev+/crEXjWt\npJ+cc5h+NQX5F2JyzdlW89673jMtH6RxfK3F+q65ky1BQ+RAj9kLgC/nQEEL\nlNOQOxXN+I8F1upMOQO9S/hUKOjswilyl30EbUyeryYAi47lpeesNY3yMuCo\n0YrWotgvlM9KbhgEmwiajP5EoFx+fjc+nd/Waws9MO6Ey3to/6/mLbinQ/6P\nXFe9+er1ItP/PmOY3LzNRw9RNuFziUBqbxppurm50sjadXuJYBGDfMizb9uV\nB6UC\r\n=bvvn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-module-transforms": "^7.10.1", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.10.1_1590617308672_0.9312160158352145", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-modules-amd", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "cb407c68b862e4c1d13a2fc738c7ec5ed75fc520", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-3Fw+H3WLUrTlzi3zMiZWp3AR4xadAEMv6XRCYnd5jAlLM61Rn+CRJaZMaNvIpcJpQ3vs1kyifYvEVPFfoSkKOA==", "signatures": [{"sig": "MEYCIQCdaWvuv15VwIZ+DM3qptmYOycQCaSCPSks3Ui6oWfVnQIhAKfyFTo3s75Rvg1uOMctySXSXlRj+fB3sp1qEyuWFIYg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp5CRA9TVsSAnZWagAAPA8P+QH07DwkgVhd40T/usyr\nyqFxHubtDH6yQ4y6AHTvjOzBvXPD7msmTtIFXSev3qnpRew8T5X56UBsKv84\npcmMiQvblRMaqoIIn7suY9MGOW0gY/sDo9ij7w/uk1ktzPDQ81pIEEvP2JP4\ni9qwDc77at+3NYQHqdUW1m2kJv2/ptWDXhms0Pv7VY4wJDWTFuJpPAlo3DrE\nmIp8NE+sfgLr71KVk1ZlX/EMRIQfapI9jSUL3RsDH8FSWPfl96OTfvricr3b\nL+gRfTrlPrbvR++QEVi73B0MGonn6gdNlEEMk51Qc3nb/Lat/oOOuCQu7VY9\nsyMWli8qpYldERb/EsFa/P1aloIcf3sCBQyliVaArecf8eR/FVyYGz0isSWh\nB87GhPVhXC73+QWtka2OPGWgFrQYyCeInpjfFwMefQsNVjUfhqTN0pqWkCsJ\nB9Z2IzLk9dguqvEK1eEvtvnfSVrSYNd1elrAg4RACTye7442zBQ6Os4O019m\nGFUgifIvxOs7JsM6DjG59DbUaWHGp1lUb6S6JYPVuz/STerilAcSOaL4W+4B\nL6NVuB1uT0gi3Oz+I7O9nl+IJ+mgm/wNUFddune6aNfj+4JkiKJYMZDF/u8g\nKqGTV0mml0UTkTDl7LypINPRedMuggnGZfvF9VS27tw50VMLoPBnTlQWlmXS\nyZgb\r\n=5Y4X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-transforms": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.10.4_1593522809030_0.387964987342732", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.10.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "1b9cddaf05d9e88b3aad339cb3e445c4f020a9b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.10.5.tgz", "fileCount": 4, "integrity": "sha512-elm5uruNio7CTLFItVC/rIzKLfQ17+fX7EVz5W0TMgIHFo1zY0Ozzx+lgwhL4plzl8OzVn6Qasx5DeEFyoNiRw==", "signatures": [{"sig": "MEQCIDaO9eHYRAVW8WFeJ2OnZdogIbtjx6njdNO/L+RfTllTAiBgJrxnO2+y7K2Jx2rWo7TshXN3n2kx1x4wEkpZNwf6DA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbjCRA9TVsSAnZWagAAbRQP+wfPm85v6s/Vj4RQfId7\nbPv0UBX8K6Kk/IyqcyeyOc6ftVoasXaisT502jnD/8q0a28eYVOCO/H5RZdP\nK5qV5rvH6XbjJeSVtqrs7kzr6w1XDeHxxaQIPDBtgJNj7p4jiSnhJpeOtrgZ\n47w+AlK7+2fIM3gzVGnsol/hzLl4qrP7is4qJzyqxdlIaW/v7IYut3Hs5XmG\nV0m554kFSooF27TvAFuNmx0+AozTdcyM5oz4/xg/AOy6aqmLQT8bHcaWX2kO\nTo8Xi3xxGLAoTqfCLkLe92pjmNbeyMY1gaG9k+gMHTvS6yd49CPeEA2cVtyn\nSO/4ixOtSQ7Eui3dcC4NUgubTeZHVHFiKvEEvOTxhDOcYIv5zrXS14935JAS\nsHyB92AY7yNkKmOZb4hpDA7xoKBnXPz+7bN6DeZPWXKCps3dzr571wbYaZtY\nGmazBFDk1gT+yyL7HHvom+aFZskag1lx63rEHlgz9wMo9tDIlbZk8i0a8d3F\n1UGT2C2PHofP/OpargQWITwFwSKYcB3b9ivubrmGZNzrohX7Hj4wuEryrLbQ\nR8//v6lKkXQr2+sQ8wUJt5c1cQ/R3mteZ9mJKQkGHIuxLEjPrBW7pB3h3Lbp\nTtoK2TZdOGrKG0X+PqTs+u5Rv3l1Zd4nHDo05/Lozy8EYO5yCy0oN8qZG9Dr\nPwzk\r\n=0f2+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-transforms": "^7.10.5", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.10.5_1594750690782_0.7364021123953102", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "3154300b026185666eebb0c0ed7f8415fefcf6f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-tDW8hMkzad5oDtzsB70HIQQRBiTKrhfgwC/KkJeGsaNFTdWhKNt/BiE8c5yj19XiGyrxpbkOfH87qkNg1YGlOQ==", "signatures": [{"sig": "MEQCIBwn+bGvmlSs5CvaF5AR+tZp9jz+X9V4feJWIS8VLDy1AiBC004DdmUT5Kx+dpJaex0edyK39elLGncVd5T3i2d+0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNBBCRA9TVsSAnZWagAAXVIP/1bYKopS0F1kiMHLJQb2\n2dyuQQ5Ll/KkCUbaj3I348hWzKa6hVfKQQNGmx5TOwHWOUNhHjWh9m2HeHUq\nTjnfDisZvozGQd7B0uvr9i4BvjzVmsbscZvLJMIz8I5w4LY9SijiSIfaAeSu\n5mB6wVwmiTDiacdm7SN+ZuV9dx4c5agKR96iWsU30s+FwcBFu11UlQ9Vhvgt\nrV1R3mqItySl5y9am91cqA+7ZCzeDNJLK8ZNwCiAC3ziiYAotSyLw5V5Kf2B\nAiSiR+Jh1igAfR2va2B5ECfelXCCoTsIwxEaOZ8jxHlHIj0chDogEp/+8sgS\nWj7VZxI0wQzALknD7YiHkztj5iF9suN1Oz1C+kf0XzQYHAnypIAjLXwADIF+\npstVgtArgqIH/BF7rSwEfNsUMGfRBgqKbzTaDBabey5AxeDZYC/SXdrHZv4O\nOrIdV7KjjfhCmITdipwCcY8z9I+tnk1r8yIA+3yR59Zzf7dRQauDAxbDbGJO\n2bav/sU0aD7aL7fcxoD4tArc0lPLifKZmvHewNR85poN4ODTJOrljZxC6zat\niP3C6EMrHK0zeG+GEMxp+GqQ2iQRIAhsdaQHtIxc+5wj7vhLmOsTysNfZq3d\n39atHpnufa397Ue6eUeN4ATkfCJW5fB0v4cWGEH26Ykg0xrPTTv406V6+VVF\nJYqR\r\n=e4Mh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-transforms": "^7.12.1", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.12.1_1602801729016_0.15642146443894633", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-modules-amd", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "43db16249b274ee2e551e2422090aa1c47692d56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-JHLOU0o81m5UqG0Ulz/fPC68/v+UTuGTWaZBUwpEk1fYQ1D9LfKV6MPn4ttJKqRo5Lm460fkzjLTL4EHvCprvA==", "signatures": [{"sig": "MEQCIGLPw1JBP4ZO048t48BxFpDZt2hej648x4zFiTUfsWTiAiBXaQEIEkc2LzieOtQwYWfcQ2l7PEFUsMdvogaSPh736g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhvCRA9TVsSAnZWagAArg4QAIKDyBw5fP4U6nJnVarZ\nblK6J3PXyPBBiputC8rCuBCYDl0++JJ<PERSON>JcyIcRw0CNW4trTBjJeJOpg1twcw\nfushPZtiu9hdNeFla1xExdJDhfrhwoJaGl15yqtWQImr7UeMR6NFtXg4RLKI\nCxXCk7E4DwwJzZjvYHfSKRPkLkj2nxa2psDVJLx0X7/cTEonTCjjZMJJ6A7S\n2GZyjgC7oGqWmB7rYlQqbptLr4db37TfJRmXwJtPN2CObpW16N8dFaVkndQg\nCYNTGYRo/TZ7b5d5CBXXmElNsnGLb+ln9sV8N4T3DbIbJ6RRFR37GjAAPWI2\nVviVi3+7gS+iKO3N3Da1r37DPRqaw/XSvKYIP0PvYkP1q+Kvf62tycsldlO2\nkSOTDQ1/1xEpfPVBQTU+CIvZqFoWRxvCSO5NsYa4yvI9hyztyHsF7eUxJr8z\nl2K3bYlCf3MF2YgERHRy925TrW00II7lYULwCas54gP+0OnUyrRlzIfg9+c2\njAdbW9SccjvhLQfZ8l3QviknX6s4X8RapO6mPhJAhC9GwnazPtk/2+nLbE5m\nEG34xqhSy/ORPMeSv8zSdmvb9trMJQ81gNksABDFR09oejuC/plVhj7BicHe\nd83AhOWKk6Bh7SfdkH6sZDa6vfeQiCXI9N6cQBvwDZ1pv439QPeV9QwpxuHv\nv+NO\r\n=/5kX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-transforms": "^7.12.13", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.12.13_1612314735172_0.8742377658975826", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "19f511d60e3d8753cc5a6d4e775d3a5184866cc3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-EKy/E2NHhY/6Vw5d1k3rgoobftcNUmp9fGjb9XZwQLtTctsRBOTRO7RHHxfIky1ogMN5BxN7p9uMA3SzPfotMQ==", "signatures": [{"sig": "MEUCIDJWRVKH7pptT41XgZeDzGa31jiCUhlhhFNHhQTqQ2N1AiEA/HFocs0zodtDiOxBKF1sGDZOZG8e+jxwO9+MyD8b7po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU3CRA9TVsSAnZWagAAC7MP/1sVOCcSAHBHJMPtcBvC\nPoxLWbE0qudD3H2fpMfAZT8AsEkiTglTWRpMUjVVJqicmZqAxOFkUXk7aNrb\nQrxfHwgbzakAe49TsZoAmTKSSBV7kBY5O164Ab2j4WRMzBd9IljCOJHGKjz3\nkuma0/FLtEWy3wlUCOlhyLlq4epIvfluXq86R7jPF52X1z7g1hMw/UMH1+Eu\n9IDY0kzIfNqjnXiUa8Yyy31fUbXF6C3nHwxYvg9G/yBm6MtJYpACBu5z9zKY\n1brGrVE8L+nEBi/0DJa6oQokSk4lM72qLcV4TRmI4dSQWyWf2LQl6ByGLig1\njfjsGO2GtK6KK59C+67pHhHkwZgjdnB0ouBfjQubHPq6hWPSgDYBXhMz0fhN\nT7SKcOSfOqDkzt5w/3Bm7in8RHIXRwyqfViLjdsfWpOcT3a9zMRSrsOEJ9va\n+iioQYjuJIIpler6THZMbAbBuVAwkR/25SYcxETLbUriLknqpw4ozhH+QtWa\neVkgBLHHWkSX3cbJ+eTO8oTUCjeMEBhHcg6MgHbUVKVVkoIjnz4M4+dLpSNn\n8x6T/gggVEM9+/ml4FaLY1EhUGrzOa3lbaL+fxFuMQSuDVUurZuDgD4IgGYB\n7KbSRHs01kBp/cmZASo/lW1S9/YxDUadhwbWrejliN3MzdWqFS+p8/32Wt6s\nIb5W\r\n=bCGZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-transforms": "^7.13.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.13.0_1614034231223_0.02319202153276012", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.14.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "589494b5b290ff76cf7f59c798011f6d77026553", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.0.tgz", "fileCount": 4, "integrity": "sha512-CF4c5LX4LQ03LebQxJ5JZes2OYjzBuk1TdiF7cG7d5dK4lAdw9NZmaxq5K/mouUdNeqwz3TNjnW6v01UqUNgpQ==", "signatures": [{"sig": "MEQCIB9eTvU3Z/zwY/TZvfAk9ZshvpsiXzxRv+brMORZfM6GAiBZixnSHFCi7qNLwE9IRY1wn2kmzmt0e5q6TwrKhxeZcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKrCRA9TVsSAnZWagAAEKYQAIETBfDN/T4fES0JPjEl\nGpR+2NceQXiOH7j77V4/iR1eBkM4z+N1blBCC0v/7n/V+h44XZFKrZN9pwZC\n/Fj5G5T9+bGB69Wm6sOAXzArDxgun5gqU6zBNq+bTnCtQt74SzaWuxC1vKdA\nJVLr9wdbXxLmqR9oLQWzFejtU5xpt2xqPMBeWvNnbIhiPl+DlMRwOzIvwiJ6\nIqpkiifylkBg6qYZdpa7cajdNP+fassTdUCGFS0M1Hg3UEcpiNts4f+O+w2W\nriUB19yjBNvJRpcBBTckCaZ4jPLeEzLOHyHdwO4dlZCO0NqX4y5sVc+CEJ5S\nLE562i83J5abMjqZ0F7I3EFK1EJrhjawAroUJ2l1WSWb2pBM4Ba9srejYyYn\ndlcTQKyKBZTqb+18ALOK3wf2w/O/ebsRBvgUp85TEEbR1G3ePp+9MkvTQa1o\nxIxRR8TekMR8fX5RHILPSUH4WpTOzDsaW5PA0Z6a7o7F3cpYrAOgWUnqnrAG\n3rGBrVHRSjPN+LHgVxkE8SRpo/UjIuFPaxQWi9+PW8Ru0FlkxFFcaO94yefS\nzbOWCzabKtLW1esDv25rldwurQNdYh2hFQtP2uKu61O4PROzZx397i06iHDO\n4tiAabSk6THohdLGii3RGl8/JtnJBzBswX47pyULA/IgAvPn02jIEEjkz5ht\ng5Xk\r\n=fHTh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-transforms": "^7.14.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.0", "@babel/plugin-external-helpers": "7.12.13", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.14.0_1619727018923_0.7272942901080679", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/plugin-transform-modules-amd", "version": "7.14.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "6622806fe1a7c07a1388444222ef9535f2ca17b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.2.tgz", "fileCount": 4, "integrity": "sha512-hPC6XBswt8P3G2D1tSV2HzdKvkqOpmbyoy+g73JG0qlF/qx2y3KaMmXb1fLrpmWGLZYA0ojCvaHdzFWjlmV+Pw==", "signatures": [{"sig": "MEYCIQCN+jwQLG0V5ARXpP8UY59Ifdb10Gc9S+P7BOg3pekfNwIhAN6uj+pPX7OfMBYM7cUJidBq4P1Nc9wzJt0ADtBa0g7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvXCRA9TVsSAnZWagAADM4P/RbYAJHrmAbttJ9lxP54\nkS0jq0iMt7i+w2VdrPOfZY8H9dbx4gQuLHlcycQ63LcaxB6r6WbrA/DlBbtl\n798qfCneRHtKcxnT6Aahcgu9O8HhV/KVeLuek/QNELd7oiuyPvzDaTXeOzLT\nv5MQWSWktlonkZid4Sm220FM7JqheLa/p7WjMWqJXqDwmYW9im2UXoc9MQwJ\nVI7iL4l45XZcNWqMrHTNQcxeVfHbuim91E/jZaYJL9cPDDe3xi4bHm+8ZvDl\nTWtj75wKiDl6EknDwL+o+7sNz6Lei5sffEmE8qNMRG4S7FEdkf12F2hK6pNH\nR/xku4aK2+pKpU9+xPy/FXZZYdoEDQmCUlDxqaJzB5KfdiZCgGTBV2zwaAsn\nZWDdOVpwV+P8JuDU9QYUH/tByWiuSmzZX+3Two14l6/H8u0URo4Ik9FJc8Bu\nVSw7QeDPVuRTVxoAST1nLpXil3nqWQDDrFg8h5ea6GKCcWdy5Mfv+8gh7LEJ\nA+nlkzP280Ee3NB17OeXk5RIOet6tUEUOnbJ/D/1xNlglvZpqmkM6e9OGcG7\nW7iMMqrGmf0xZjijKBmMuISmIqr/VP923rBzQ8aqUrwHFIi2686OpC54T2Ln\nkD+/AlEckaXlM+kG1FqaIbwkR+SI0MXQe6/4ji6mAg26yQanGhskuXNdu5pF\nlTm2\r\n=TYqA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-transforms": "^7.14.2", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.2", "@babel/plugin-external-helpers": "7.12.13", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.14.2_1620839383117_0.33034240053875763", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "4fd9ce7e3411cb8b83848480b7041d83004858f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-3lpOU8Vxmp3roC4vzFpSdEpGUWSMsHFreTWOMMLzel2gNGfHE5UWIh/LN6ghHs2xurUp4jRFYMUIZhuFbody1g==", "signatures": [{"sig": "MEQCIEtYWmblB1VLqXcMDsF3s2WzKB6KbX4Xhc7QHr+GU2WzAiACCuMC+vd9eAcaQO7DpcT4xKURaDgJh0NHMRVDneLAug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsRCRA9TVsSAnZWagAA9BcP/1vhyn9BCe/gKcDucZ2J\nTMhecdbkGXwUVXdL5xlUk6RIG0P7ZOBca6UnTsoIaiqMNRH7J1orfxdMEDtG\negizJ8I2yV1s0SI8rbs1rx6PIIoILyE4yqI+VM6rJ05fvF8o2TlxOquRUVQv\nI6GkqT6MsReAufNgW5JtyL/lnA2yGGQXsqtSxDjrIvhCAizjSzgxtmPEG0J5\nmEJe0xlsEdOqSge9VrSpKApjTvKVG21e3t1+Xyf4NSQAKXAHhr1uYn8/duhq\nxLQVY1jFh+5JRW5A5IoIQxp463YdrLrJtG7YL2+2sVqu7s4iUH4ndcabyH9K\nF7kxH4AtEYOwarN2896HxQHc8i5U+rl4uO7hN+26fhTG/la+sehoBDSc6Jak\n9Qy+CKwyuiXg6tYsKLm6hXp4PrRbVa3fML7OVwem/mpTDfPsTbevvDqPfVIy\ni4aF2Lem64UZez7QldAq1Z5u5ziyRdEMik+5cCt0OMz7b2Y3UPfVojV2sQjL\nlg+JSYvDjBj9ebX7bborkyh8K6MfkpvkP2FQyNhv6l99zN6t+AWLzsFZuLW2\nvY+UiFI4DGLJt9zAEnf64Uugzp5hUag8A9/s9R7zxuzOPLLthB0Xq7GqCKXy\nyDp/nS5VKhGIiSUwO5TRpP6eFqQgmHT4Z0yoi+/zpbBHuT+xL66l4V+iE5k+\nulfu\r\n=7j/G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-transforms": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/plugin-external-helpers": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.14.5_1623280401540_0.3543998220988416", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "09abd41e18dcf4fd479c598c1cef7bd39eb1337e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-rWFhWbCJ9Wdmzln1NmSCqn7P0RAD+ogXG/bd9Kg5c7PKWkJtkiXmYsMBeXjDlzHpVTJ4I/hnjs45zX4dEv81xw==", "signatures": [{"sig": "MEYCIQC2HlO3757w3T09IXFJEAaTHikYDBomKG1t8xQBaJ05XAIhAIP7ljDvU5cujEaCNTpEhK2W0dD3yVag97O+6p9hvACo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7874}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-transforms": "^7.16.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-external-helpers": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.16.0_1635551279937_0.0991513949240983", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "92c0a3e83f642cb7e75fada9ab497c12c2616527", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-oHI15S/hdJuSCfnwIz+4lm6wu/wBn7oJ8+QrkzPPwSFGXk8kgdI/AIKcbR/XnD1nQVMg/i6eNaXpszbGuwYDRQ==", "signatures": [{"sig": "MEUCIFgEtereACcTHthcaJbZDAK+Z9qqUgPAoGPlT7S8u42/AiEA14gzLe8SeHRY60OtC21f01cOmsui7Q6X9yLkVPMNGm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kkCRA9TVsSAnZWagAALmoP/R2Etke/k7AabXf9HYjo\n9/UmCxRdZjaDqJUz9sO+pwnhf8mUCOR1v1JS9dfvxsXLs8xzuyrDtka3JJPg\nyyE5r4KRm8umknvNvEDylJlN8fpBEeyA79X/mKx+0sdIB3t/KPYaFnTd8Ave\n0Zt3Vchzt3UFNNW/VW9DhgWHxIOv8YXQlEyfiTfMb77+aQkO/1z+9ilyB44E\n4YO0eo0fToxhYnJ4QlLgQCleuYZ9IRjLo9hQkKKCEHD6jGow2WsYYk35PtHs\nDq4//BoK8laTgYaAI07BtaEfcepDDq8xaFhBU4tkIo0Eq7PEYYaYdA26wRWL\nH3yiuE663RwMIU3ww3GhN1T3jcJuy1h/cOS2kE0bQ1tMzgKyerALruap4wxK\nuzF7g+O5vLPOXCWDSv2SEVkBaX45I2d+0YygDuB6nhm+ekxKvFfyMfoEPFEE\nGstFrR5S9aiVgVANy6x6MJfH7Z0vk8qRNFzmrYmz7v8qvF7zVmSaQN51MYc8\n3xSriVVJ7CuzbBYAftxuzpelm9yxED31+0mgkutcNGvCQ66KvqKdbfD7ddmi\nEVIP79cJPhpeM0QWpIDDfXaFs9JcM1w3RNJkqkDCpPrAUyv8NRtmwHCrF3eg\nRUPj78bjgbNuFlLfoBqOHXl+OLDZzuJiSYtevGaK4l/fXT8kM7lZ7mCED5Qz\nKZ6t\r\n=0V98\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-module-transforms": "^7.16.5", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/plugin-external-helpers": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.16.5_1639434532444_0.5572231713741278", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-modules-amd", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "b28d323016a7daaae8609781d1f8c9da42b13186", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-KaaEtgBL7FKYwjJ/teH63oAmE3lP34N3kshz8mm4VMAw7U3PxjVwwUmxEFksbgsNUaO3wId9R2AVQYSEGRa2+g==", "signatures": [{"sig": "MEYCIQDFrmtVx9/BoJ2eiSajzDqYer9O9Y5SlM5MqIMEEGaN5AIhAKfskOj187Ssc4+ZrHkLX0z9ZW6gzjpuv0TOHCtxGSP5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1pCRA9TVsSAnZWagAAtmsP/1ulqApmAHX8aC0i1GM+\nqqz+BWjSxbSLTl9yFOzgEmWFpsrPxN72WHZ4iFFPvop8HngcyBorHpbh0rvK\nknddOGF5HVzKGWE+7YZRYGa1HNQmGvKrBZyzKr0AIh5nwVk9KgMQr/Jz5Whc\nxiI0UDpANq47xzSeBVTJ2YWoXsL0D+gEAVAw41NbYwr5Y83REmuPrvDmJ/wr\nWghsVQ7fUgnu1GAYQRgxquTafkeo1opDsg4GyDJgSTKfb1oFYzZ7h1ax1oVo\n3UKArugOYKBCFv4V92gfTm/ip5ZjFn6ZNtj32F8/mpYSt+yCNsI85YLg1Pdw\nSPgI0ZEWJ6g46qNbKnm0PyuPGQyT4MN7xfymXY7Q1j01XnGSu8VZTl4OY3XA\nJCSeGdbVE6uo/A9m8HPdOEPQEo/5qTzKe4yfTT8H/qsU0/SjGw/uU5xdqJqG\nCUsrBmrhr9YhsqUp7SljsFGJeL6z8Y0Ibdeyt0Z8Pn/rqxrgBJveQaOT2SZ6\nZQGp6dJwiRjGcrYFG3+j9EGjzESIMgZ5/MpXXu6Y3q8Ch7iMa8ekSK9uAM2v\nP72FSQCTJsYFt0o0nDNGk5RYfkyMVxdhzVK1WFJpnibKUk/tc3yTiitJMbK4\nEssP095l6WizEEPzvbSCZaQjPbR4gzKRMIB1gvw4YZ5CRyzvilU0T5Z2F048\nRzSy\r\n=0UwC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-transforms": "^7.16.7", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-external-helpers": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.16.7_1640910184843_0.2509885997526593", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-modules-amd", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "08ec1f10f854c15bb3b44952e60f1fc126d7d481", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-p5rt9tB5Ndcc2Za7CeNxVf7YAjRcUMR6yi8o8tKjb9KhRkEvXwa+C0hj6DA5bVDkKRxB0NYhMUGbVKoFu4+zEA==", "signatures": [{"sig": "MEYCIQD99D+eu85r17q5anM5A1m32w1EF07V9QheRV2fLlLrpAIhAPNELbUCO14eu3AjFZyjq3RUJ+Wam1xCTxjfcwLHz+RS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHkxAAof4T/CCqk0ji22AKZbLq88IUR/6nGAO2436O8xDwFIGO45iS\r\n9RnBIm7BxPupgMYw6GULlmmS37hUHaFYbtPvjW+P2g3FfiQIceHJ2r8ClbSS\r\n4eOzBTjemr1OfKYdN0ifPP9QKpAYwNEM50NeYTcSmQCXLIAVmdfq+2dp3xEn\r\nDFFQTkNYdGqA0wEw1rCvTOqPTxz5nkdGfEuzJkvrv4TOlg46eZNBc3EcSfQU\r\nuX8Tqw4Aiz031MexEY7K+nBpkx8DOHK2+/j7IORujUAB5M6bVwXl40Mp4q3S\r\n7stZ6vDTnGoiZUDLcI888axvP652qD5Qvj5TyBfMYycn2+ui4MZrnu0GqWVS\r\n5XQjggVLEIQKV1b9540TSwIEBnWMI8y9VAUWBQoOwfjF801SXsXJGwfefoaO\r\nIgSqg9kC1skehwVv9gZTk/cyflb4fLBgvtdJxDZIthZtMobsY52cp0JfutAd\r\npQTBfJuzbRgjk1yJXUi3rueAhLpwo0CnARJ9/bT/IJY3/lkC9z5QwmeM86JQ\r\nw3BugDDAN3BUihJhqjx+PM7mDrXOmvkVHLVb7nswKUAwwEsacgqDWdYJf14i\r\ns5ONRA/itWCrAguLsBF9UrcpcvtOZBR1+uRt1Eu6jKHSy3PC+oXHq3Skzzks\r\nsMDeNirUJL0rzKQONsKqJDPTq4ZJ4xvB2Z8=\r\n=wyS3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-transforms": "^7.17.12", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/plugin-external-helpers": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.17.12_1652729595710_0.14283213937609363", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.18.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "7ef1002e67e36da3155edc8bf1ac9398064c02ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.18.0.tgz", "fileCount": 4, "integrity": "sha512-h8FjOlYmdZwl7Xm2Ug4iX2j7Qy63NANI+NQVWQzv6r25fqgg7k2dZl03p95kvqNclglHs4FZ+isv4p1uXMA+QA==", "signatures": [{"sig": "MEUCIDcBhfqXIQ56FZj2/LAkWQmXGrRZ+52LdyRCDypSBWrgAiEAypNs8TeTnToSwFpmBXHRLKRtKwBMoP04GNL/txF1FVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonSBAAnlZBGL4OOG8l5tfTAoeiis36im1+ScK3K5t+vlVuLSyoL0LS\r\nTvL/LsuS98DnO0UFBkyF3D+wDfIiDi2NYkuibyGd09j2Bm54Iyi95S5++gPy\r\nfX9VdY0BF+9oO3bc1dELi3HaQ48wQR61zo6FEz4S6f3iizgmbKc9cyq9UiNn\r\nbW12Q58jjfCWI7xTZM1PyZTUK78zgoFajuXackPXmr8ZzZOuuDA/VGEM3AUb\r\nbeRYQyatmXjoJHjkvB6w+8ZXe+mXO/LtzOJrxUzKhGHy1SqsdFFHW1TyqhOl\r\nV6j7GjZrZMzHsrVV/6+xP8nAtaLeGUkfR6tSdxA8vjb7n2zHcXPjsYe7VM0/\r\naU9rXSOP6y8aKjoOtklXrmJqrpc6ZiRZSqbeAkN95XtMqRBEnq0xmGkWYNeO\r\nZHKyzQSncAgjQsIaPaT5j0vJDcboA6NZrSVSaYD2WtsPud8t7lnmLGMlHwRB\r\nu9ll42/ywCRIw+WLskDEBtOpTmcME7hb573mald+/lV6zHY7wbrK9lhXeKI6\r\nA+ycuJ6+Z1UTtfyEog969l/hv2Ghm8ialIc7V7qmj08Db7s3ttVJrYwErUUw\r\naMC7KHIqvytsnmIcPMx1kbfbWed6qKSNtbpLeV2HG7J6cSOGtBylE8dx3fgd\r\nHRHdoG7dnX45uHie8q4D6qX504ynnDKIs/Y=\r\n=GxGN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-transforms": "^7.18.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/plugin-external-helpers": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.18.0_1652984200557_0.1473527341787071", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-modules-amd", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "8c91f8c5115d2202f277549848874027d7172d21", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-Pra5aXsmTsOnjM3IajS8rTaLCy++nGM4v3YR4esk5PCsyg9z8NA5oQLwxzMUtDBd8F+UmVza3VxoAaWCbzH1rg==", "signatures": [{"sig": "MEYCIQDRcYlQ0FgiDBjWPqs6PCXt4RY2Vawp4V3HQ0A01V6S0AIhALDgJdKqqWlzDP0Mkhpzvu9ZQhEVZxJTlnk259ho7CSD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx+w//RXWjw73JsFOtzmOJMsC7z0MTx0+jO9J/vwGjPychLJxof2Gm\r\ngig4+p1mWCVBkw791hwDHXv4N30mxXm/y2YvQu7lwr2GH6raM87uhVyPPQt8\r\n5yN7XXzJKJmV8yUbbeGQY4IVasz7BwVJ33daap/sF8gKGAph/J9vzvHZHagB\r\nkX1DyUgEhl396EFpLWkBVM7PMdbRl66qwDncxwXUoS/9A6hQY6rO5CPlDs4i\r\nUYSX72RzyZCplIrVSeUDNY2ygi3bk2p6MulO/LtqlIL7d+qM6Zq7SgpiLO+L\r\n7KnFi7Y+68WHkwsgR7NLy/HPS27xCnrkxytfUwnpz6wQGP9ZeP/mHTWO95s1\r\ns8QiDIEHKBjns4yTSiM47mBVc+D1G9O52Zw4QxQQCN95FxAW5MkAPol5UNQu\r\nZ+q50E2O5z0eXb1OJ7Oc+Pmpo3WpZ/0qgGzGQaquHEJQ7vUeWvLJQjGxjAvP\r\nVUMfqnlqRRjcNivSDSDdhJq8MTgJwjisN9bVs3x4rHG+VOByG0Yd8wucia4j\r\ndbHiRZozhlZmfpFAuaQRi7BTpE0HP/LLbYTi8NwMVH3225i3dukgpr7vSes9\r\nUtNxJ1AL+mCCQAjY3/pRFW9K5+NazO2naXp59cFDkqifPi48XiH7RA6JAwFJ\r\nVAXghbrEcnChtYNeN2QfAlyag2D4V1Ed35I=\r\n=0BeE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-module-transforms": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-external-helpers": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.18.6_1656359440360_0.43083760394543935", "host": "s3://npm-registry-packages"}}, "7.19.6": {"name": "@babel/plugin-transform-modules-amd", "version": "7.19.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.19.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "aca391801ae55d19c4d8d2ebfeaa33df5f2a2cbd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.19.6.tgz", "fileCount": 5, "integrity": "sha512-uG3od2mXvAtIFQIh0xrpLH6r5fpSQN04gIVovl+ODLdUMANokxQLZnPBHcjmv3GxRjnqwLuHvppjjcelqUFZvg==", "signatures": [{"sig": "MEUCIQDlmQAxTijJzHQcFNCKHQbnP0t3gUc3UhCnOaHfcKhOeAIgV0AM6MdL8GNCw3AbOtlEyeRRej7Z6uhZz2xbtvpZLRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZzhAAldBan+U+oQe5GS2edlkzXzeHi1uaCcW5akaHChxM4QVuQF3k\r\njgT0yfaEB1LJHtHS5TuX3RxvTS2M7yCreQhlyrz+FLXXqa3KenUjuta90WzO\r\nx/d5BQfQDvtYn7Cp4R/LehackKbYdZfpC4sqP/kjZ5Th/Q7T7K346y8zH5mO\r\nMiPqIX5kPI7GymilJL9ZE6XIMT+vvCeILs+nsdPSCs4svAkOaitC0dVYUOZD\r\nENrSOmZg8pxpNGoXHgLARY+JPcp/yvhv1lp1J/4UElOuh8OihGpKDunV0sUf\r\nChruhka4cJCuIq3pzflfBTNZ0/oI4Cqv27sekKKne2U/S2aEFc941awmLuxH\r\n2D10OaiXEzTZ3ctu078ZAokEyob8WqngtKIiVKYlJHHrXnUg5RbX8xEcR+TV\r\nB8sJ7Hb6+5+p5plwSNLgGq1pZf22LTs85KM8fynYJx6q98991pCsCpC5xt9S\r\n7gY9rFqpqD8fNMSDUTrDlvY6lCMDpKYyiUU1i+edJLjqpNCaeDExuHHEdOmK\r\nOJz9hxEc1pPVvJ79GAJ1Y5UZBMEjJq11peQrPALiXeNRgCn0BZ+1zDoWFGu+\r\nHY/oeftUDVEb2OaDyheZnRbJlu3StNaMDhnQ8pa3aY7EYDJI+jkbgfncro/1\r\ntIB3OlWCNSORUyZKG1VqtCKyuGXEjeLVUYo=\r\n=1qHG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-module-transforms": "^7.19.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/plugin-external-helpers": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.19.6_1666256615700_0.2605711906157968", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/plugin-transform-modules-amd", "version": "7.20.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "1e5cfeb4e5f9b392e86f85698896336b442f8760", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.7.tgz", "fileCount": 5, "integrity": "sha512-+1IV<PERSON>+dHOzRZWNFFSoyPZz4ffsVmOP+OhhjeahLKpU97v/52LcCb9RabRl5eHM1/HAuH5Dl0q9Pyzrq1v2otQ==", "signatures": [{"sig": "MEQCIC66842MJVaU1tiNSNUqv9M3+ABc1uk5wLYLA9g277W5AiAbNRfCLbOUAmFCpinDr6KvEKN4EYSabin8yPFxxcLJaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraiQ//TuQDpdfLkW1vl7/HgZzK4xieSZZj9cObgSGtjA3Q5INR53P2\r\nuAkFyekJwQmRp6BLGTLrtm6uWc6zxw0n+uvneRVDaTG40q4pUtNnV9OkSZ81\r\nKYMh0BT2RnYpSKjnNMqzPiWr8L7MyX7erNX8KBy1S4mEEFIg+sB9mY6LRYw4\r\nVcRw1u2Cids+zN7FE/kIxo/iuYi3RlzO7a62JSjOdMhyFQUkV5n8ey/E/Q7d\r\nCJxbvtVZypPY5CFznCC9S8DmmvvRahIsQQ0BMhJRyDcFsgDdbt05p21XyCX7\r\nIGbxl96/Stul/3GvV0TOqv1mFEOqnWlzTRxzFd0Gw3iZO/aYTg9L+M9Wj3EC\r\nJjQPycUp1nqrOJf/8A0LBcCN/w6MHdXzKiRW5C0Bgxw2DI4epOb3S845+vZi\r\nC98gUFzdcwIamuM8pCKoQanB0xaDVTgbXP10YyPpXYhXevfSonax5ZghDa2w\r\na0D7s1uK/73PbG2qiTvYd6daaQ5qv+DogNeUdiw2pNIU0E2PLdXERZ8D8D/D\r\ngZhiYKjKm/MjxNnTMK50hPRRpRKqOqvhgvoSEVAokivSkE8F140CmU0p3Fb9\r\nzJZKI+85DDx3PQkejGx9ycocriHDhBfIv0LoU9OX/4EqaZW1VSJU9LaqZp4n\r\naZSEx6sCcN1uobEU68JjsIiiEvD9xdCe7iY=\r\n=Ntkh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-transforms": "^7.20.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/plugin-external-helpers": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.20.7_1671702337306_0.28947766856585844", "host": "s3://npm-registry-packages"}}, "7.20.11": {"name": "@babel/plugin-transform-modules-amd", "version": "7.20.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.20.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "3daccca8e4cc309f03c3a0c4b41dc4b26f55214a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.11.tgz", "fileCount": 5, "integrity": "sha512-NuzCt5IIYOW0O30UvqktzHYR2ud5bOWbY0yaxWZ6G+aFzOMJvrs5YHNikrbdaT15+KNO31nPOy5Fim3ku6Zb5g==", "signatures": [{"sig": "MEUCIF/cgzdrlRgl4uRh8otBrCo9lKO0iKYdnCzFLME/2qqSAiEAm1eR0dnkmyvPNko9X4zZk8XiIzJU+hBhqP7rMV7bc94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjphi4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqALg/+MPkV5BT8ITC/wKpUHBHvTGT3VkuPiM1P3oC78fuYjsdmZIK2\r\nCt4elMr3jurZW4HQg0mrxjh1aHI8LpLgCGLARJUOm4kfkPYN+8H/X9V3jaHu\r\npuGpWNXszdAtRPTcZ2fR0MJGClKXV0HCaayZHwupN9hM9RQl/xnhkV89nqJt\r\ntomcA5aTudx809yZnrEXIpMCcYDHXMnLbFTZtad6m9oE739w0neIqE410AFJ\r\nNvwEpcmn1PzBOmDVydopPnLSTSWLWvQozRA5/aihinwS4nZUlB0hmDayLHSv\r\ndmfSJ4TLXAdU9H7OZVWowdnwhhrIcurfZpkV3DNa2gTnUorPObvAh7NZDhjb\r\nzZZrNbilIu6gJDFti48mU5wyka0lUlKlHFsLsAwloCX+LXkMwaK4frPtGVHx\r\nfOy5EWq1oeW+MzIJkp/gdIExa/VC/uWNP0EDVphEjh/6egaoW2KzpEC2qkCq\r\nr0RQRJ4nCVQ59E77nwP26spLHRqoZP0ahsEPmSCFfHEg2D6a6Bd2cizG45GD\r\nLQesfnf85tNe/TUENejOLwuuNLuM3W2GspuQfEMRwBy8Ma82DFxIaATni3Zk\r\nRuvtSQQ3agEDWKUNeWhinq4PajZgUF8Xag//yxJA9fMuBJb/2B0KlKDbiPwx\r\nhttrTH1MyMYfzy8xhV8gjOngYeBMLWuzLvw=\r\n=HFSr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-module-transforms": "^7.20.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/plugin-external-helpers": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.20.11_1671829688363_0.34511850809661215", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-modules-amd", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "63ab452f7f7226721c2d9c4a1a8890d09a555589", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-RitJAiYQ4mff950DiqnULZ87vW0GUPQfMc/pVGJJhq0yekjnJ6VY7WHvYu1hTVmeu6e6+JfPMtv02Qn4HNln1A==", "signatures": [{"sig": "MEYCIQCVW5BhDaGtqooQTJWGyfWHNppiDcIIG1zRYw+wBjLaxgIhANVY82+4k+Pslz3Is/RP+SElRHrYNOOMJjnSpn1/YEf/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBVA/+MyuWCnxXE5wkjRenvBm8cEfN5nuxeU/TxdIewbhCJcVUtKIR\r\nYlgoX4p0Q6VlSJ5BOI3vNpVXOR5qvdxdTy5VKN+pUkEHPhazt1UfI5Nlx2K2\r\no2sL1xSPqpoEh7nqx9bKLxRC4ypdDVzX1vX3aWytczOC03LdX/6Y93rGbXV3\r\n8mKWWOZVqA6pPTcwEI87aVQKzd10K3Fzx4kn0ghWOEL+JXYKn6F8Z8XebpPS\r\nqw3iYzAr9yGp35d2KnPJxtRu/X5m0MbPL/h9LucHV/g9mO9Zv+pbswtDKDUm\r\nxSArkXlS7dWoGiAdILTspDyP/0f3WtoBsYy0tm9b8UsPSszt+s94kMl7N1K7\r\nUJs47oYeC1KuhBY8Vqdsl/bwM1YBfHXxda8UvY2m/Iesd/1N5JR89o7EVhUS\r\nOLbF8qNIhCnzXBHlMcfxuqqoSS7vowYIjNHF0KaWzkJtN2IB3zHrE/gszlhB\r\neMqTtL/V/slQ0k8azVnVLg3EesX3/ah793czbqDPh6OIlvxjCrd815vdsFV0\r\n+NvlsOvDzRBsZfN+VvrIOFLH+JVfWx5+orM7ogp6oIQRnP1u78Up4nzfuhq+\r\nneNKl5hTXE9+MezbkLCox9qeL+M/+EO6QV3HFBf7zrMdRi7SFbvleSrgyNBn\r\nf2rfzH90htacTm0lTfOhjjxTdEXSRYkcI8o=\r\n=zGfS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-module-transforms": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/plugin-external-helpers": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.21.4-esm_1680617397023_0.9121311013800086", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "1e16b87193e4930d9f49c9c5a5807df32aeb120d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-WqICtMz0Tw/qY24JtsGzLSsh2SvKcHqsLBWrBxwJCTeftfd7j64l0j8Ftkf5yls/Pwze5NDZGFoJcwbYQspnww==", "signatures": [{"sig": "MEUCICAPUcQ++cXqMfF24mmEF1Q/DYNes2O2HSeTuJKmMBjzAiEA7nbLPds5I0R56r9EgXedvxv8Dz7nIEfKn9gAn7GmX8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUdg/+L5l5GMhsaDwFcdK2zTLOzfVfY/Isow8imse/z/LmOIJUxUsq\r\ntoAG1UEIVBQenAFX0kHLSsKcMMqkBw3Y6W89w3zhkSe1ZFeymSzqNBUSzQZZ\r\n34MmS5sJPaLBg7OMEvB9w+r1fyspVAij4C4Z8JsQOJ/VGQzTNoC5sxcfVAHp\r\nY8Ca36Ajp3dGhrx/BRwZDv3yXZRif+z6G/khALry1PCCNz4FQ57dqZTl2pTb\r\nxmtPphqIU0IimxoAtlQlBNGS3DyUXV/2doydXzAbsLWokhTrVouzFfMLx033\r\nhAI4Ypg8+9ekeNO0klWV8YL/LM51flVLFeiYmc3LZQ7VEiHryasqrMmE0OlT\r\ncUK4T39rkCcBF5zuJpsFT+jKRIwY3ALL0Auz2s/niWvDJ7079qtlXrHuHEQW\r\nfxgfvHSK3jMPwgixCAYylaxbns107URkYHK15TDjFsJKDYVW8EFi6dt0imKJ\r\ntsTdVIw/Xy8qDtvP0xx3UOC+ymLdI7g+oS1SQzlu5auhtcgESsMnzmXNS0fd\r\nzCZRrWmGrH69RAWzc8vxqjNwtHlK2NEmtmhPeWhYP8Kyh/IuxKIenqRaXXUH\r\nTqTZLpGBxWULOyYFJBFkXfAk/r6YoP2xrSVSGig9JooNqivEnMyydkik0jF0\r\nVBSlrH9ndz2vFY0OVp96n2h+andPdE8T7ss=\r\n=7kDg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-module-transforms": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/plugin-external-helpers": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.21.4-esm.1_1680618114994_0.36506032149758205", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-modules-amd", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "9a395fa3014c0d2bfdd55eb0361fbb086649c49b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-HSJc9c/M0qZ+TtMJ7CXl2t3N5/dMk8jovxk/81x/w5latYEcyCXaglO4XvIqdQTKYalHhlXv3Km4D+lRHMjVSg==", "signatures": [{"sig": "MEUCIHSZbiF3DvubQMwLd1iCLwfAJ3e2syXYu6fGxJuVX+nlAiEA9hyr7QlYqz7Z8RFfRYhlayA5jgABaX0j0nyF5LzFdu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH3w//csRD1MWq4TW+1CuDEYaZr+YDqDhAvuJgC6ZFHs3onlC6PwOs\r\nwSlvpXnl26fV0r9+QVq58R3Y1SS0oVN8xhMYjyEeklVHa8xFDJglNQvxpscF\r\nFMxiDzod1Op6CVthBX3rpQzgyAttT2yHydyFbSzsHdIib5gkJQ1ZMtY2AZxQ\r\nZGQldFtw5EMMnEQ6kEpm/w5xnRubxU2bzUAFIoOkJtpKDXsq4zoj8fKjRUWQ\r\niw0zE7fRZ1aRnERT/Oj98VYOoSeZTWLLrn67eqr0jy5fTKWM7hlT5vYsxOYy\r\npOpQYhuORzPlydsPFMJmehRi1+NFK1gceKL6zdZ9/WwHvmxH8Rp06s+QeuFU\r\nDxuXDJaETU6kZJF6OojsFNu2DYpS5Cqk/o4/btSREQEUuUAWoLOSX/pBfrEV\r\naOeYcyU+OgE6btgJFoREJrRBSGvKmS3RgIFZw/cwGMzduw4jCLekwLBxz17Q\r\nI4Vup3JCaRjCo33EQO/2MHwJIWzRLTsmo4iDC6l660Bve+S5+bfbgmQPIdgn\r\nhlsdzXP5Sq4e6n+B8dSqm+xduHShSq5GZn7xjmdlC9pywGarOGLB+PDxn/Rh\r\nsVbw4p/sFpjEPU2ETd4TVnZh6beDjcqDUbk7Ph82hqnb6wcqPrRjVfCsktvQ\r\nDtyNP13+m3TeaXdDX0+K5/Y4p4A91YjG21k=\r\n=+cWu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-module-transforms": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/plugin-external-helpers": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.21.4-esm.2_1680619199254_0.15330932085397575", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-modules-amd", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "c47f83ed4139d8bfe1dfde2c465fb082cc903ca1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-m6irXqiJnQaB0BrzqX2q7UqVqm0gukhIheTwDKkMm0Hi0WJ/B08Q2Xuib56/LviiWBeA/ho3D+BWjrN2gd0BQQ==", "signatures": [{"sig": "MEUCIQDpLKJXhZn8yveY5L4n6JR5l/jP1mRsXmfX8hJFvmHtMwIgA2BfscXwUMpwW8Xb/0t8fsPUSUboSH6zNurp5cuGdVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqquQ//YATXwh31zYgG+adVf6UNugq4kalhhNIBMzyEWE5kZSIyk7Ex\r\n6jVEqPAxCshigEEObLXzJDTQJWiTIBPXkrLYJRXlh+IY4UXUx3RJv7Ba8h9b\r\nOyk++qgjHTGpmH/XXPWt3rTYsvua41p1zuwinha1GqOY9NfCRkWuDR2hfv9O\r\n07dVtdbSevTrsGYtVyjgGASBdZdubWfgE+T2EZ9HQ6gX4IqS61JiuRbQiEYX\r\nddkxfiEes+hDlt5U8lGxXnoYUvSFq3/b48UdHuA1IrgXIEy1dlmKY0qCyY18\r\n8a0RGZVliqH9lf0NJaQ/FUiXrLU8V/I0IfASFFmT5URME1Hkg62fTb8PN6+h\r\n9/FBRUF4GUYf3W7aHcp8FVdqgYWm5fLY/BISd7aD6+ezhXwSEhygq/8o9dET\r\nM8IyTNy56o0gYLmJeteCVZqOpgOA3VhN0sUvPZwcKo4LEhA7yxaK5r1JDFF7\r\nr3FKYHNsE1ohdo7Mt/EQbmVF6H0zQ0TuPBqs6ttTxL0UwqskU7SlyKea//Gg\r\nerdc9r2WtyejBlax6xqDEFYgldXA8S6LT+pVSKgwQDZufQebE1r+BEkyxNkP\r\nBcpae/ty/1DFj10pwlY1vKU7poJpfT6+JcJBIfgzJJK/OC7S5zH1HQbwM6dV\r\n/ptXNhgbIoPI8uGfzjQNnU9TbKbrRl9gQN0=\r\n=EV7Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-module-transforms": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/plugin-external-helpers": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.21.4-esm.3_1680620201606_0.17795821569310832", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-modules-amd", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "06114e5dc57a46eeafa2cfe114001c2b4c88d38e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-MwHIIAc3224nf7mw49jJtswcuHxOjUt3sc6qkck8Dgw826EV48psgqh+p0xzdCeZEAEVT115m18l5dtuGqSzpQ==", "signatures": [{"sig": "MEQCIAN3kN+lD0NG/it8N4dTcxjujp2wMsMikj8K+xBAeCnLAiA0wwlykrS8c49A0bBRHM1mIZ/zwalDl5ZlBjzUTAyFFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRGQ//Uehu3UQDUJQ/kUKs/TwTsLjXSF74/fxkKQMv6bWzqRwRa0g5\r\njbpew0nc3MsSS9iRNsK1RZD260Bw47qhc+3cZ47itvCrYurZPOqC8v16ivLO\r\n2GerD950SJBr+a8LA0uRwJDts7EKultRQQOzkrED+CdSH5TPCUPl8EHzy5ED\r\n0pPGN1ihTu0j36XM0fWwgGRtLaydO7Dt8c+psjwqFIfuqQ9r46SeE8EmcUsH\r\ngfq4kWnVVjKBJlnvYKXnfDHis4JzjS0aS6Q+DLTVl0zbq82ca0rUNtj1qYVJ\r\nnWL0WSv8yfUmrneqEQZ5StTtFdJsWQECQ8TfRdafzfEcJhyUXHkPd76kRlrc\r\nB72hQlbdGAqqx04ZD3MuMmsPlTHy/8COPI7bObvOGnZx2WC7KCE1SHt8A8gU\r\nyQDZqGgRa0wWuEbFJSKGJLsXXGhqWYh2QO9dscVNsBpFHV4/UjM0x4TS4ibV\r\nqHLtDxhHV5v6W8ywLgJTX406CX3Nb53/hxNeahY02un95d2hkIn+vlPqE7OP\r\nXC0AfoVpfHihEif+gH7X0LXy3I4XdIkeUqiv2KGE2/zhQjKicwa7gCs5dO7X\r\n+FZpVdYPBzunqH3cKUhhb6EdNm9w3k4h86UUBCZ+UzQQwa2Y1vrJeCrp42mL\r\nRlDV//+fF7x52EJkMK8/890+1CfB2BvU5I8=\r\n=HCL2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-module-transforms": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/plugin-external-helpers": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.21.4-esm.4_1680621230207_0.09010969149994552", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-modules-amd", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "4e045f55dcf98afd00f85691a68fc0780704f526", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-R+PTfLTcYEmb1+kK7FNkhQ1gP4KgjpSO6HfH9+f8/yfp2Nt3ggBjiVpRwmwTlfqZLafYKJACy36yDXlEmI9HjQ==", "signatures": [{"sig": "MEUCICwux+8XpnjFPk5VPaP5yGBqryRqwqq6azVa6Orl59VhAiEAudblqycacuFKNBgOQ1LvVrfgoVBj+nkYkgUbbJuMXPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20318}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-transforms": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-external-helpers": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.22.5_1686248505614_0.6235010579391647", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "5d8e9fb9e81986c088d4c3d116c64ab3db69d498", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-38XRW3YSPDCjxXnUq3t9s0Esji3fszlducfruFgSbPxPxrY3EVLAkKBdBcHhEhh5I4nYn+UU26TyfCUbsC4OtQ==", "signatures": [{"sig": "MEQCIHtGIRAC/Y+roTRYLzeXbuU9F5re+dmYgErgfWwjAjiDAiBImsIzCGXBtzVcGsgDOyenx1E+Zrgpx4DR45WB4XVyOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20221}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-module-transforms": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/plugin-external-helpers": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.0_1689861620099_0.008105060906088868", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "4f31274065b9b57d73dc9e2869fbf041c8056159", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-71WJZHZUjB9PczobKgpNlINBlH+f5S4N+drZ8IsWFXK2WQ+LEecNuiwFa/wQEYJ8ACajLRIaKL/aapul07JSow==", "signatures": [{"sig": "MEUCIHJVJoJ0KtfJjAxdgomvIbZlJxxTVpjkIebVh7EimPMEAiEA6jphyWvCj8jHyzHfSxbJGLDwiRyhPivcOtWbGaL8Y0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20221}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-module-transforms": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/plugin-external-helpers": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.1_1690221173690_0.9156927639002159", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "94681328f1cf1f3304a2d5ee6cb424837af738c4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-Ecmf6eDyQZ3s7Tc43fm/qMGNcgn7Kwx+inJroszA0cs1wUoUKwZD3J/rwLkQz6zMNbV5kodE5rJiqjLBil83WA==", "signatures": [{"sig": "MEYCIQDP9FtWi5PzKJ/t+/4c5eZR3mNbWlhy+JUTA5u1stuaBwIhAIYW11F5qqmdj/aMg8AL6YmYleTcITKhRfZZdEeu+HEs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20221}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-module-transforms": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/plugin-external-helpers": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.2_1691594116872_0.19551949552931247", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/plugin-transform-modules-amd", "version": "7.23.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "05b2bc43373faa6d30ca89214731f76f966f3b88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.23.0.tgz", "fileCount": 5, "integrity": "sha512-xWT5gefv2HGSm4QHtgc1sYPbseOyf+FFDo2JbpE25GWl5BqTGO9IMwTYJRoIdjsF85GE+VegHxSCUt5EvoYTAw==", "signatures": [{"sig": "MEYCIQDzIVVvbQT3nUX5H0823eWrxH75bNMG3Kmuw8beLsjZcgIhAKdnf/Mb1x/uODTeObvrvR/xKGPneG6veBwiJfK4kbfU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20876}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-transforms": "^7.23.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-external-helpers": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.23.0_1695629489062_0.7951505984550564", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "36e701c3d12135a30bd14f4150c05055ae38ace9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-7Yk4LvK8GwWNYPTa/hB58AmEaXlhD83b+KnsmxJ8qVYBLbRgo3w57kSSXHAEh2goZYW6dWiCffFwOscYcsasAg==", "signatures": [{"sig": "MEQCID3zFClTqaKEh2Np8wOdIERP7aA95nDUsVjL9RcC45ckAiAS9n7h3BYy3OHpGCHTTcvyS5OVSqJvBGICUNYwQvXtwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20792}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-module-transforms": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/plugin-external-helpers": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.3_1695740248719_0.6561742703107121", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "6133ea264f41b4b50bbfea19b50f4849f18f6d3b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-pq3cFFy/jn83heiYEBS7h62SA8vPtqhDLXcppteTFvJEnLCoNNBDf0dEW7sogjNdDLK8ndVvyOujRvp52nJX8A==", "signatures": [{"sig": "MEUCIQCbXM3lIe7em66z1uYUYzarf9PRHv206f8K+/ISE83R8QIgLrWhquxFFWhHed4rmFXQ7qoPrGfxJU8TVODoArUjYv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20792}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-module-transforms": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/plugin-external-helpers": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.4_1697076402616_0.18899459300754984", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-modules-amd", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "e19b55436a1416829df0a1afc495deedfae17f7d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-vJYQGxeKM4t8hYCKVBlZX/gtIY2I7mRGFNcm85sgXGMTBcoV3QdVtdpbcWEbzbfUIUZKwvgFT82mRvaQIebZzw==", "signatures": [{"sig": "MEQCIC5aFWOSSZUf5iwlEEJ3Kj+0kmVpQjile8qFwjr7pcxjAiApqYY2AX5VxMF4D59ZqgEkkzG7Xg9S7/Xd4kH4XjlljA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20957}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-external-helpers": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.23.3_1699513452363_0.9763694117360002", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "b568ca0c8d931f5efe0821e06419859c9f92ab5b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-r8zqGehLrrDJRbf8rLg/+ygda476c2l3O0kIkYSG6i+N65AdqP2jizSPsNyee3FrAv0RZQ8hjmIMxlt+SCWcQg==", "signatures": [{"sig": "MEUCIEZ9kMAsaMdRt9tCalBwxy5UyTOfabmUuC5jvc8adY4fAiEAu9PyiHxJX4OPJHaDxMzUpl63HFPZCjAkI0sGiAb0+dM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20905}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-module-transforms": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/plugin-external-helpers": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.5_1702307975053_0.673094110282165", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "1ff3f02731511aa1b4e522544cf70e154d6fc5f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-muzyyvkorMaJ1mHz+j0rmtPhpzosj0UKVO427DvAPkNHDcZBcUgUGJpr8X4STqHdsPAC10Hl3duNVzhWq+Uj1Q==", "signatures": [{"sig": "MEUCIGM9yUyrT2tiy2Ldpfntjyw/8LIKVXrwxuMFZc+KupqoAiEAh6CFR11q+qvlcT48edV6iNQAngg4ZF4ppBqhq63IP+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20905}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-module-transforms": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/plugin-external-helpers": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.6_1706285674378_0.7733460274148762", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "586cc0794b0a4630edc171ea5032c84c73b43fc7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Rp/PBxvADhTDBbmNffQpEWbg7hfA8Ch+bGY1LML3LJ0uKVWd20Fu1M6QDNXqohOp0CMwXzLff+dxs/pweryWZQ==", "signatures": [{"sig": "MEUCIQCS/UdHjiG/Yoz9c4lI9q8m5KeZUYAM3qHEsnvZPow75AIgW2ebGlorQp9XaHOkawuIhThxfb+9BqKwtjM4wyTGMEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20905}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-module-transforms": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/plugin-external-helpers": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.7_1709129134504_0.41238615063459005", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "b6d829ed15258536977e9c7cc6437814871ffa39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-lAxNHi4HVtjnHd5Rxg3D5t99Xm6H7b04hUS7EHIXcUl2EV4yl1gWdqZrNzXnSrHveL9qMdbODlLF55mvgjAfaQ==", "signatures": [{"sig": "MEYCIQC8NG1a2GrvKPYZM52eFotyT4nTzinlk6T/PXHK8RpoNgIhAKPCyBXnflzyrHD5qje1BzKmGNz7Ub9IXJ/PL/VlYjb8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20888}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-module-transforms": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/plugin-external-helpers": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.24.1_1710841721132_0.30791809825247296", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "763eb570ac8b12c7b67b38256c8149429223653f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-RDjUjAzFJblsBPSGbTdViiF1H6RkXV3moydm0I/5S1Un4rIqZXMCr6lMx0Fx/qqTAZy4ku1IAhslPc/y1h0ENw==", "signatures": [{"sig": "MEUCIGX4WNtiagoa43Jht8voXxLitMSWb8omL3uBZqGIprwxAiEA7S0/UGXvMG1HbFIECydNCH8x19wxCXd7SE59J0VGQWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20819}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-module-transforms": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/plugin-external-helpers": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.8_1712236813578_0.15439619316221176", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-modules-amd", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "09aeac7acb7913496aaaafdc64f40683e0db7e41", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-eAGogjZgcwqAxhyFgqghvoHRr+EYRQPFjUXrTYKBRb5qPnAVxOOglaxc4/byHqjvq/bqO2F3/CGwTHsgKJYHhQ==", "signatures": [{"sig": "MEUCIQDwAhMDtRNbNwEPlrT4uk1+w/XSww2M97v0zUNFgzIyEQIgBc8bX/grT7kkRSn/+bvncuQboYsdy+EcCCaAzuKcf2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88376}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-module-transforms": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/plugin-external-helpers": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.24.6_1716553504164_0.8509275195476422", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "0cbb9aa3e4a5c5431b6d1023f3843f420826a948", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Ocr5Ot+rX0dJyBLGFOHK7k4M2mSTTPu+rxWYPueJUNybsCMQuOtEjvI5jnpviA5EO4onW83D0SZhggoOXFQQFQ==", "signatures": [{"sig": "MEYCIQCn8gANwoRL30azL8AQuuUoQIUnYGMayZfBOOJh37fgNQIhAKSAEmAgs4fZx2g/m5a2fHCSwyvgNJmO2T27f0e85LrT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89087}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-module-transforms": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/plugin-external-helpers": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.9_1717423542117_0.732472656718717", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "8e180f21f74e955545844a80df8d68a8ccf0524a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-yPo2e1m/Y04+TQH6JlBH2G157UI1qK9AeY1srRi5hXllY+0qrlbk1KbHB5IOzEMS9dp4ob1LFHey10KjrsoDyg==", "signatures": [{"sig": "MEUCIE0Kq2SM9KWRX2x9KGMknvdOv9jh8DbCi/ehtqCRaXZKAiEA+dX+TNgQuH0h3jRVz68oi7iSzq9zGXbWTSi+/IUf2GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89096}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-module-transforms": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/plugin-external-helpers": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.10_1717500042096_0.6705270858104724", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-modules-amd", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "65090ed493c4a834976a3ca1cde776e6ccff32d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-9+pB1qxV3vs/8Hdmz/CulFB8w2tuu6EB94JZFsjdqxQokwGa9Unap7Bo2gGBGIvPmDIVvQrom7r5m/TCDMURhg==", "signatures": [{"sig": "MEUCIQDj0wEQOXrH08o7LB+TC5HEbk6LqTyUzcAC8b8UsDrAygIgVBhsd1TlkCjsVfE7rWJdFr77kVl5K/Re1f3D9df/HCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88331}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-external-helpers": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.24.7_1717593355705_0.19455400115415244", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "5c4b3bd051ab94460ae76379ed639b4f3ca92751", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-yRSZuU1La5lkYM9pNYGgLsYyqJRV1B6N7YBueW5kZtjs93va4TAJjazc9D8GSlfoTEn6XeGFjfyPt2e1zSgbog==", "signatures": [{"sig": "MEYCIQDGfCLY5IT7PveGwyd+sVpc3J5xmFRtNVTYS56SRy765AIhAIUT9Qg9C3pK9V/oKM2YtVUsO4VtuSvAL+MpCdmT4qGY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88985}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-module-transforms": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/plugin-external-helpers": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.11_1717751766196_0.37723997528213804", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "24f79a97fc7d45e50e4ef7d971f849fe22672cef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-EtBadP5U2pZJBGuiIYqUPN21G70AWrjM33Wg+2r/Hbq+4G9Xj0lL4E9/9B346yVHy5smIJ7DQ6rqDFYgJy9RAQ==", "signatures": [{"sig": "MEYCIQC0KG5Px4wQqPGXG6aBTvMzqwiyCATxcOfhdBrgjNb52AIhAMoPNbFaXFenBfwrqCKOatNhSXaGRxa48GEh/gPy+9YE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85657}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-module-transforms": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/plugin-external-helpers": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.12_1722015240916_0.8414267039738186", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-modules-amd", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "bb4e543b5611f6c8c685a2fd485408713a3adf3d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-CgselSGCGzjQvKzghCvDTxKHP3iooenLpJDO842ehn5D2G5fJB222ptnDwQho0WjEvg7zyoxb9P+wiYxiJX5yA==", "signatures": [{"sig": "MEQCIBfOwvjuKA9te5f6LJXnRxei7XnoyW710bE/1h/lui9wAiAQAyDfYxBuC0inia3aQzammFRmQgA1Y5DIs0qPZp6TOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92829}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/plugin-external-helpers": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.25.7_1727882130968_0.5622151929611923", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-modules-amd", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "49ba478f2295101544abd794486cd3088dddb6c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-g5T11tnI36jVClQlMlt4qKDLlWnG5pP9CSM4GhdRciTNMRgkfpo5cR6b4rGIOYPgRRuFAvwjPQ/Yk+ql4dyhbw==", "signatures": [{"sig": "MEUCIHSX0EwzloHekTDdvVlieO9H59nME3ymNJfINejtDzMDAiEAiJwn6rOwve81EfQvrfKbB7L6LGWDYvLbersY7UPuxAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20879}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-module-transforms": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.25.9_1729610506678_0.9331172630623024", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "828d3e2df03819f353498335f144d5786a938a97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-CLVuXROEKp/2C2tPE5iivZ9SW7UOkzctT2tZDlFH/TR+9IFdT2WDayGZq8IqgFzLSDsZu4z+J0QhwzY8NGJjQg==", "signatures": [{"sig": "MEYCIQCpJR4QWLiRauQFnMBJ+oUZAau++7wydnvEgLLboB6pxQIhAINglolGJlIhmGrjPtq6sx26HBR6Bn8MRGv0ri23p1c7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21652}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-module-transforms": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/plugin-external-helpers": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.13_1729864486828_0.9575186859784257", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "2d3af4428963b3b8c6dd177ac34125a6fd2d2e71", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-2gGtNF6M7jBt9IWKejomxX349mmQlxJ9pa7xb1fGg62KJ2Yraqgc88l9oz+0R5TQwtO6x/cWueTKegibsnJxlw==", "signatures": [{"sig": "MEUCIQC4xSwgADUkoxA75BJG5NkeePdje1tTCBHyPXLwgjUjWAIgZGuG2lTiV0cVYD4PG1nkpIdXhhRpXEpRxGV812KvQNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21652}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-module-transforms": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/plugin-external-helpers": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.14_1733504075906_0.33378800582821255", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "5921495372b0eb39b536e59dd2e36a2a39ee55d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-BDMeH9HWMKkfd4tYwqjcOlVdq/Rb1DjFJpEm5he835jo65O8eywjweY6RZV+8wLLQrN3kJFTLynuIZEafl0cxQ==", "signatures": [{"sig": "MEUCIQCsaZi41oXJz83uyhT0EtOik3wyjDPEy9iMIXD46UsoHAIgTIe+U6by5jnAWM5T9YDYZ+Wb+49JQUbr1WayFlaFT/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21652}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-module-transforms": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/plugin-external-helpers": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.15_1736529905648_0.6190825953283949", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "e1b467db927f0aa1670609344343df714c277661", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-RHr8NczeGarm46DWYvJ2bEjq7XoOuXXk/2qnII6tElHU1tvakNtKiVoEHwhGnHZt1XLaTJN50mxhzgYoiHGLpw==", "signatures": [{"sig": "MEUCIQDh3B1E/HoCoYVibNvtprmKVfGpaHMbd4waTbkg0UpiDAIgWPp9rxRALKMBc7Ln2ohWR6egoarCuIlXATGfuwj7aNo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21652}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-module-transforms": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/plugin-external-helpers": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.16_1739534379745_0.5609531602205604", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "16acdc62f71c8ef93f59079120c3a2d039e17245", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-VMt84Cxm36Gi57IOyf257IRhKOpKVPW/CXLf72TkCYsOYoGh99DxwqmEfM5tSn39wVipGa4A+KTirszQsUZYMQ==", "signatures": [{"sig": "MEQCIBaGxOlwe24Mp3I/NvUjE+ttJnA12Th3LdVsTB4x7ifKAiBnQFnqMLZ1VzzK+mCUaVzJnGxuG/c5i5PvYZMo55jg/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21652}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-module-transforms": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/plugin-external-helpers": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-alpha.17_1741717534152_0.4635210090752997", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-modules-amd", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "a4145f9d87c2291fe2d05f994b65dba4e3e7196f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==", "signatures": [{"sig": "MEQCIHLftgz0IDg8cDpaQNc9EZrs9g+qjf+/5qpPXsyYv3KbAiA3JcQOSGQz3nEmAbRMb8Eaq4s+ate896Zmu/vcU7S/Ag==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20879}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_7.27.1_1746025768339_0.6899305123953279", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-amd@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "dist": {"shasum": "277e906faff3b6fc6ab2f8adad95405237919202", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-ozoJX7wnnQe6oDiO9kehS1byDeEg6T9Auj6jWQC3eUO7cTkjsHt2zDZY5LsnluA3IFwih5PlejTpOpQXQxsGJg==", "signatures": [{"sig": "MEYCIQDAbVI6pEIlkcopAKfX5lKhFYU3RSPnXanGdO3Uz1BSkgIhALuJbhpyzs0Ore2tY/BC3x64mx4PwZnaVbyH1eoe4+CP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21624}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-module-transforms": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/plugin-external-helpers": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-amd_8.0.0-beta.0_1748620305949_0.2991804003083294", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-modules-amd", "version": "8.0.0-beta.1", "description": "This plugin transforms ES2015 modules to AMD", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-amd"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-external-helpers": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-modules-amd@8.0.0-beta.1", "dist": {"shasum": "e39417eea8d8fed0782d453497fb0e874e4bffb0", "integrity": "sha512-5ASMIlu3vRTU3IPYGgDaCcC9FHUqDjkcAi/iolXE/fhi5MLMCrPdPvzXER+eI1zwYnez+CxyQ9b8Zp9tjpQoTg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 21624, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDjqub1cW3/eW/jCEOSa4xv92OotE6tJP1XbZY7ywemzAIhAJL3M/3Qti7M33xBSQLp9lh4MgSjsjbkjQU/Lsa2J7mv"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-modules-amd_8.0.0-beta.1_1751447088130_0.4929875543499007"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:55.535Z", "modified": "2025-07-02T09:04:48.547Z", "7.0.0-beta.4": "2017-10-30T18:35:55.535Z", "7.0.0-beta.5": "2017-10-30T20:57:34.256Z", "7.0.0-beta.31": "2017-11-03T20:04:15.855Z", "7.0.0-beta.32": "2017-11-12T13:34:02.197Z", "7.0.0-beta.33": "2017-12-01T14:29:20.204Z", "7.0.0-beta.34": "2017-12-02T14:40:17.562Z", "7.0.0-beta.35": "2017-12-14T21:48:27.099Z", "7.0.0-beta.36": "2017-12-25T19:05:39.060Z", "7.0.0-beta.37": "2018-01-08T16:03:43.061Z", "7.0.0-beta.38": "2018-01-17T16:32:43.208Z", "7.0.0-beta.39": "2018-01-30T20:28:44.769Z", "7.0.0-beta.40": "2018-02-12T16:42:45.586Z", "7.0.0-beta.41": "2018-03-14T16:26:54.421Z", "7.0.0-beta.42": "2018-03-15T20:52:07.819Z", "7.0.0-beta.43": "2018-04-02T16:48:57.259Z", "7.0.0-beta.44": "2018-04-02T22:20:37.994Z", "7.0.0-beta.45": "2018-04-23T01:58:34.153Z", "7.0.0-beta.46": "2018-04-23T04:32:50.787Z", "7.0.0-beta.47": "2018-05-15T00:17:52.673Z", "7.0.0-beta.48": "2018-05-24T19:24:49.151Z", "7.0.0-beta.49": "2018-05-25T16:04:28.293Z", "7.0.0-beta.50": "2018-06-12T19:48:05.778Z", "7.0.0-beta.51": "2018-06-12T21:20:42.666Z", "7.0.0-beta.52": "2018-07-06T00:59:46.962Z", "7.0.0-beta.53": "2018-07-11T13:40:46.398Z", "7.0.0-beta.54": "2018-07-16T18:00:27.954Z", "7.0.0-beta.55": "2018-07-28T22:07:53.120Z", "7.0.0-beta.56": "2018-08-04T01:08:43.814Z", "7.0.0-rc.0": "2018-08-09T16:00:01.907Z", "7.0.0-rc.1": "2018-08-09T20:09:56.758Z", "7.0.0-rc.2": "2018-08-21T19:25:59.158Z", "7.0.0-rc.3": "2018-08-24T18:09:45.028Z", "7.0.0-rc.4": "2018-08-27T16:46:27.636Z", "7.0.0": "2018-08-27T21:44:55.102Z", "7.1.0": "2018-09-17T19:31:03.007Z", "7.2.0": "2018-12-03T19:01:28.561Z", "7.5.0": "2019-07-04T12:58:00.382Z", "7.7.4": "2019-11-22T23:33:49.856Z", "7.7.5": "2019-12-06T13:17:57.594Z", "7.8.0": "2020-01-12T00:17:29.685Z", "7.8.3": "2020-01-13T21:42:22.512Z", "7.9.0": "2020-03-20T15:39:48.802Z", "7.9.6": "2020-04-29T18:37:57.671Z", "7.10.1": "2020-05-27T22:08:28.831Z", "7.10.4": "2020-06-30T13:13:29.187Z", "7.10.5": "2020-07-14T18:18:10.887Z", "7.12.1": "2020-10-15T22:42:09.186Z", "7.12.13": "2021-02-03T01:12:15.300Z", "7.13.0": "2021-02-22T22:50:31.358Z", "7.14.0": "2021-04-29T20:10:19.036Z", "7.14.2": "2021-05-12T17:09:43.341Z", "7.14.5": "2021-06-09T23:13:21.667Z", "7.16.0": "2021-10-29T23:48:00.074Z", "7.16.5": "2021-12-13T22:28:52.567Z", "7.16.7": "2021-12-31T00:23:05.096Z", "7.17.12": "2022-05-16T19:33:15.847Z", "7.18.0": "2022-05-19T18:16:40.766Z", "7.18.6": "2022-06-27T19:50:40.546Z", "7.19.6": "2022-10-20T09:03:35.885Z", "7.20.7": "2022-12-22T09:45:37.492Z", "7.20.11": "2022-12-23T21:08:08.543Z", "7.21.4-esm": "2023-04-04T14:09:57.189Z", "7.21.4-esm.1": "2023-04-04T14:21:55.134Z", "7.21.4-esm.2": "2023-04-04T14:39:59.402Z", "7.21.4-esm.3": "2023-04-04T14:56:41.750Z", "7.21.4-esm.4": "2023-04-04T15:13:50.443Z", "7.22.5": "2023-06-08T18:21:45.878Z", "8.0.0-alpha.0": "2023-07-20T14:00:20.284Z", "8.0.0-alpha.1": "2023-07-24T17:52:53.840Z", "8.0.0-alpha.2": "2023-08-09T15:15:17.017Z", "7.23.0": "2023-09-25T08:11:29.307Z", "8.0.0-alpha.3": "2023-09-26T14:57:28.969Z", "8.0.0-alpha.4": "2023-10-12T02:06:42.934Z", "7.23.3": "2023-11-09T07:04:12.528Z", "8.0.0-alpha.5": "2023-12-11T15:19:35.272Z", "8.0.0-alpha.6": "2024-01-26T16:14:34.522Z", "8.0.0-alpha.7": "2024-02-28T14:05:34.680Z", "7.24.1": "2024-03-19T09:48:41.312Z", "8.0.0-alpha.8": "2024-04-04T13:20:13.774Z", "7.24.6": "2024-05-24T12:25:04.319Z", "8.0.0-alpha.9": "2024-06-03T14:05:42.296Z", "8.0.0-alpha.10": "2024-06-04T11:20:42.285Z", "7.24.7": "2024-06-05T13:15:55.855Z", "8.0.0-alpha.11": "2024-06-07T09:16:06.341Z", "8.0.0-alpha.12": "2024-07-26T17:34:01.164Z", "7.25.7": "2024-10-02T15:15:31.184Z", "7.25.9": "2024-10-22T15:21:46.898Z", "8.0.0-alpha.13": "2024-10-25T13:54:46.981Z", "8.0.0-alpha.14": "2024-12-06T16:54:36.081Z", "8.0.0-alpha.15": "2025-01-10T17:25:05.805Z", "8.0.0-alpha.16": "2025-02-14T11:59:39.942Z", "8.0.0-alpha.17": "2025-03-11T18:25:34.348Z", "7.27.1": "2025-04-30T15:09:28.514Z", "8.0.0-beta.0": "2025-05-30T15:51:46.121Z", "8.0.0-beta.1": "2025-07-02T09:04:48.309Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-amd"}, "description": "This plugin transforms ES2015 modules to AMD", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}