{"_id": "lodash.isplainobject", "_rev": "47-ebc8a10de2671914f27e90727928f25f", "name": "lodash.isplainobject", "description": "The lodash method `_.isPlainObject` exported as a module.", "dist-tags": {"latest": "4.0.6"}, "versions": {"2.0.0": {"name": "lodash.isplainobject", "version": "2.0.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.0.0", "lodash._shimisplainobject": "~2.0.0"}, "_id": "lodash.isplainobject@2.0.0", "dist": {"shasum": "d2964a26b3c7afb78bd86bce6766cbca311c0129", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.0.0.tgz", "integrity": "sha512-Kl8nhSRuxUjCtgu9CbLlcfNSbRgZaHD3co7+9WdWErr5CXKEqb2mn1hQYoGnufMcxTTTQaj67wgllE5n46FwPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFUnc36miWreRQc+74U7BkJ6/9w7LxG61smzhwhN31uAIhAJE+5NE3VO8wP6XfAD7Atkga/lB+GW5f/cPVYmzVcFPG"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "lodash.isplainobject", "version": "2.1.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.1.0", "lodash._shimisplainobject": "~2.1.0"}, "_id": "lodash.isplainobject@2.1.0", "dist": {"shasum": "e6867d54cc91105191ed6b8502b59d068f1391be", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.1.0.tgz", "integrity": "sha512-C2IRbB95w4LvlIEWKLYhwKUnd3yX/AepboAWdnpZEafGAEbrVTHGMhEjFh0xCfvBMcz0gPr64TOu8edWomlYTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTG+T5voPFAR3z/HG5FFMU7peKdT5WeZnIzmwhtcpOOwIhANnNGgqnwZ3XFrJ8PVbZAkOAKRnrx5xNpgY1+eTc5EdK"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.0": {"name": "lodash.isplainobject", "version": "2.2.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.2.0", "lodash._shimisplainobject": "~2.2.0"}, "_id": "lodash.isplainobject@2.2.0", "dist": {"shasum": "f2938963137c9922cef607e38e68ecacbb9c5b6c", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.2.0.tgz", "integrity": "sha512-UHQo1MTGHU9q7x6PO3VgoP4+3GGKxnwIzXQswr9Ls/KFiQA6iIW7JFsjsilVsLtnHIhzIerv1QiR12SGd+PIJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFo4UCbastyJOJTBXr/FtdGlPKgjNQqCqZTJnpUKhqAEAiAVcWUg8628s/YOE4VjvYn2POi39fay9XEoFzNc7gNecA=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.2.1": {"name": "lodash.isplainobject", "version": "2.2.1", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.2.1", "lodash._shimisplainobject": "~2.2.1"}, "_id": "lodash.isplainobject@2.2.1", "dist": {"shasum": "c08c1de9ceecf5109f84709ddd52ffcde1a76464", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.2.1.tgz", "integrity": "sha512-JKcDB73B77QWlP6+19F68V3GR2m3Ln/B9xk2++a1E/5UALVKVFs10WNTw2sHIpHqZLufS1cxl6r/f+nFzkDADg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmcytBQH8+W1maiS9731PWj7rRnjSl1dbSyY11XC1rbAIgVsFRZ2h1XaTvQkfRrDm/AmEdGwON0ccMkveg9N9RbuE="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.3.0": {"name": "lodash.isplainobject", "version": "2.3.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.3.0", "lodash._shimisplainobject": "~2.3.0"}, "_id": "lodash.isplainobject@2.3.0", "dist": {"shasum": "ccb0c70aa4882f3dbc73f9c3bd39e7984c7a27f7", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.3.0.tgz", "integrity": "sha512-SUkmzY55hlsMDEFJGxdsq5GYz+7jLdenmRC+3fq7DmLqE5Elvux47oyUF6yh3OdOOVtNGWSZBfhaAeLhp7BBeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpY7JlVaWHHU+kPlor5n/MKK69s+OQo1DMm/Ki8OIGpgIhAK1fD3s7jObTAAKorkoYSD6nYulWLc/MkRuh2idR6QLx"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.0": {"name": "lodash.isplainobject", "version": "2.4.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.4.0", "lodash._shimisplainobject": "~2.4.0"}, "_id": "lodash.isplainobject@2.4.0", "dist": {"shasum": "a3539e240eb8f880495338373632d200878ee22b", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.4.0.tgz", "integrity": "sha512-NlEbQkBN8wBhDJuUWT6z3Yssm2hydhbxzcmQ4FKVOOTdHCfj2yBD7KWrDZj2Fh6Urem5azjlYFiP7aCZkYN+CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICxQS9iHmmuPNCP02rBHrDzdVVKbYqMAILoAdn+2ZGMKAiEAhPXx8/00cj4QQGdID5+OwXX180e1m2SZthyXfIcOgTs="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "2.4.1": {"name": "lodash.isplainobject", "version": "2.4.1", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._isnative": "~2.4.1", "lodash._shimisplainobject": "~2.4.1"}, "_id": "lodash.isplainobject@2.4.1", "dist": {"shasum": "ac7385e2ea9ac0321f30dc3b8032a6d2231a8011", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.4.1.tgz", "integrity": "sha512-2giZfjfnDTJbU/WOB8vzWvO6/8+6btWKwvbZZirewRf+SEGr8CiygksV1BhIv6XEmGYAoGXydq5Ekg9S21q0QQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLQHOrnipBP1PHBJmG0BXEcoWF9DqdedpdqYcE2VXRlgIgVetaXgCCbstKToLlLtpbcRsXvluWmwZXHpE2yNluePU="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.0.0": {"name": "lodash.isplainobject", "version": "3.0.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.0", "_shasum": "a0e0d00714e900bf33b26c3b8cef13b858042fa2", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "a0e0d00714e900bf33b26c3b8cef13b858042fa2", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.0.0.tgz", "integrity": "sha512-16Y59ucRgfWr+1KTUeGXyN1QQWQhgTidvWx+FjhzsIoGtaJLI+k54Z8+MG6alT3QooKJme2cXPwfxkiu8DbJ7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFNZdhzkcmGtXo+VZPHSuh/zpLxIUxwVLrA7Oq/cwvaAIhAP77hwL+RmBg33wBmUrSpUytASy/dPCj9m0WUOsdUL7U"}]}}, "3.0.1": {"name": "lodash.isplainobject", "version": "3.0.1", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.1", "_shasum": "94b601b0c281651e17c988e8589162c1de3bb176", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "94b601b0c281651e17c988e8589162c1de3bb176", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.0.1.tgz", "integrity": "sha512-jUKYvBBDQTd7yijiXaNDVD2YcRr56EhI2roK+GwyMdePQlhnrb+HJBla5DRoOO+ZhP2YLMfECoeJqOBjoK/k5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGSl0S6+u//1vaHYbHY+FR48LdvHqEk6+KCkT3nL7n6gIhAM9xE0S5xUk75PiWIS0NINACaABq6IjkioHIDLqxAi3U"}]}}, "3.0.2": {"name": "lodash.isplainobject", "version": "3.0.2", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.2", "_shasum": "15d4dcf27f89986b71e25b995e1f916ffbf2d561", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "15d4dcf27f89986b71e25b995e1f916ffbf2d561", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.0.2.tgz", "integrity": "sha512-+Rf3lIANaz3hYCbVwgqLF9fR7P1QKuU7iiY11y6mAKmM8tTbQQDe6cNHiZejMNWJVcywwHNa+fN/YfT5Tl18AA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICdbBaj7HZQPkr0kDMqMLksxcvwg8hns6PyWDwsyjLuVAiA3v7WnYSZZ3iEP0GE5qA7cjDGrNz7nxaJn/Kh32BkwpA=="}]}}, "3.1.0": {"name": "lodash.isplainobject", "version": "3.1.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash._getnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.1.0", "_shasum": "52c4231910e3eda5719c7d21d8bfdd74aad5376a", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "52c4231910e3eda5719c7d21d8bfdd74aad5376a", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.1.0.tgz", "integrity": "sha512-DzehkwGgUBWBwGGL01+yQNH07kLIzONaEZ8bPSjXuwywbO4ctD3rV1aLTOn3GCczJ65PdMUFUvxuYgnw4jUKrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDXI1tIcoBNJLu1BiDi127ZQkeF5Vd18wmAEWQRNNDIxAiBusekm5M7MB58wuJaGy++hYcwHKHkr31RCBb5ae+LNVA=="}]}}, "3.2.0": {"name": "lodash.isplainobject", "version": "3.2.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.2.0", "_shasum": "9a8238ae16b200432960cd7346512d0123fbf4c5", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "9a8238ae16b200432960cd7346512d0123fbf4c5", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.2.0.tgz", "integrity": "sha512-P4wZnho5curNqeEq/x292Pb57e1v+woR7DJ84DURelKB46lby8aDEGVobSaYtzHdQBWQrJSdxcCwjlGOvvdIyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBANmBZGC6rE3CQYoWUU5GTUadULtd4q2PUlN8WA3fohAiAZHybb33jNEkB9Sv7JTx3G3Ium8JI0eZO7Nh5oQdEpPw=="}]}}, "4.0.0": {"name": "lodash.isplainobject", "version": "4.0.0", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.0", "_shasum": "70f6d1e860a12bc1a2977fd170b2a7f7327c14fa", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "70f6d1e860a12bc1a2977fd170b2a7f7327c14fa", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.0.tgz", "integrity": "sha512-ZkePJ/JCgT8bN3e/4WwcFmINEUeoCgRHsLwyTtAYTJcrxJngpdR/pPZZyjNHONbxsKpRIU93Wg7Hpw5SDEDKTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/OUZvhFIoCKjM2XWnNBSOY2k6OGshm87yG3gaIX9kuAiA6v/giXmf5XQ0CseyCI4plb5puMVBanL9uEO1eVBkXwA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "4.0.1": {"name": "lodash.isplainobject", "version": "4.0.1", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.1", "_shasum": "12af3dcc3f36297869ccdb82c6fda8f01896abc5", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "12af3dcc3f36297869ccdb82c6fda8f01896abc5", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.1.tgz", "integrity": "sha512-JJlFVyArqDQYdhAe1nC6JQ0io30/0eyDgP4yxNWQgrUZVvNZLIGiPM1iiJ/zd9KF45YmBOuHJJG3qf/bKp8Cfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+eCALLba1LjvAA7eizOXaXZJW0qv3wm3lKcJ358sDgAIgN8lKhayobPQM5L4Zi28vkuW5Wipl9WvkR9MplVyo7g4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.1.tgz_1454484532395_0.5940206786617637"}}, "4.0.2": {"name": "lodash.isplainobject", "version": "4.0.2", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.2", "_shasum": "72d737a3fa74c8c1b8ec0faf1e468d1e4ca02efb", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "72d737a3fa74c8c1b8ec0faf1e468d1e4ca02efb", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.2.tgz", "integrity": "sha512-GOCDvdp0Ak/0shsQBspaZ+CiK3x5EElZ2u4VQ5OGjocS8kuDUtJJK0+mxJbYvhHwt/w9QnlODjvpl4qlkhXhpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdyU7Kt0/iOgc7hnvJ8XWvWBCJKuhbcu/aOhcnKRjrUwIgFdvHZTx+cXU+kbKLfh8RUsFRvI3pJNekMpbFToHquZI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.2.tgz_1455615394325_0.4941383805125952"}}, "4.0.3": {"name": "lodash.isplainobject", "version": "4.0.3", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.3", "_shasum": "32d7a64edaf9da9ae48188cd7aa7cfd07515efc1", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "32d7a64edaf9da9ae48188cd7aa7cfd07515efc1", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.3.tgz", "integrity": "sha512-SJTgqr7yyGfxaRHKN+Jk/IGiQsd2lmw+b4F+3ZyPwtPgFJuDVnbA7861vMVeHMM9CXXUa3GnSqxlN/Pep5qDQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrZL5T1DF4eXvRktsXWeCvWs/Da69QGY7IfU+KPMjPZQIhALEdT61QucXaVb/cANm0l31aVaT7vT4l/E/gLJU4yiOM"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.3.tgz_1456330932268_0.40299616008996964"}}, "4.0.4": {"name": "lodash.isplainobject", "version": "4.0.4", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.4", "_shasum": "61c7b80ef01fbeea60cd4bf76957580188fa6b1c", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "61c7b80ef01fbeea60cd4bf76957580188fa6b1c", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.4.tgz", "integrity": "sha512-XHHWBSw2MXzCqjKE7xiSOtNDzpmSR1+yCzto7HZ61dubRBkNawlJ7qpsOhpPAKW3/j6KLX5v6/PNleBG313CZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8G3y1yLGv9MwYXXYRqJRtqfCkUcPPDTUbMYvVUK82BgIhAM6YQQCgtfnSWSHhF/l6CsjDtydAU1PvTkDnHcA21Wk5"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.4.tgz_1459655414760_0.7456746795214713"}}, "4.0.5": {"name": "lodash.isplainobject", "version": "4.0.5", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.5", "_shasum": "0a7520765351ec3bc764ceb5fda07f5ed5f3560d", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0a7520765351ec3bc764ceb5fda07f5ed5f3560d", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.5.tgz", "integrity": "sha512-PJGSj3BYFLNA3MUJAtASIoBfRTEbfVH1nh6o6qA1PHq7IE7iXuGwv4xaNAEZP63f6nHk9hHEon/GYlXwsecxIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1bxpRzPghKmEeV8CqKNHRWDVXAVgr9FTn6at37QmSNAiEApWWg1wMXZqPL5wS6JrWnnKUVLxRkd+V8UBeTJRSwEMQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.5.tgz_1469457985171_0.5964741928037256"}}, "4.0.6": {"name": "lodash.isplainobject", "version": "4.0.6", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.6", "_shasum": "7c526a52d89b45c45cc690b88163be0497f550cb", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7c526a52d89b45c45cc690b88163be0497f550cb", "tarball": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDg212GEjn6VLb2naZMKrEYVuQ2Oh3I9I0EaKk/3FmRrwIgKRCFL2p5MVHgNUa4NfOaBtBm3VgObHD44iQeLGam8Ks="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.6.tgz_1471110064885_0.12097060843370855"}}}, "readme": "# lodash.isplainobject v4.0.6\n\nThe [lodash](https://lodash.com/) method `_.isPlainObject` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isplainobject\n```\n\nIn Node.js:\n```js\nvar isPlainObject = require('lodash.isplainobject');\n```\n\nSee the [documentation](https://lodash.com/docs#isPlainObject) or [package source](https://github.com/lodash/lodash/blob/4.0.6-npm-packages/lodash.isplainobject) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:36:10.975Z", "created": "2013-09-23T06:34:38.811Z", "2.0.0": "2013-09-23T07:38:13.320Z", "2.1.0": "2013-09-23T07:56:34.793Z", "2.2.0": "2013-09-29T22:09:54.093Z", "2.2.1": "2013-10-03T18:50:29.346Z", "2.3.0": "2013-11-11T16:47:50.892Z", "2.4.0": "2013-11-26T19:55:56.026Z", "2.4.1": "2013-12-03T17:14:43.989Z", "3.0.0": "2015-01-26T15:29:26.090Z", "3.0.1": "2015-03-25T23:36:02.824Z", "3.0.2": "2015-04-16T16:32:00.224Z", "3.1.0": "2015-05-19T19:52:23.266Z", "3.2.0": "2015-06-30T15:21:42.104Z", "4.0.0": "2016-01-13T11:06:06.941Z", "4.0.1": "2016-02-03T07:28:53.142Z", "4.0.2": "2016-02-16T09:36:36.279Z", "4.0.3": "2016-02-24T16:22:12.961Z", "4.0.4": "2016-04-03T03:50:15.384Z", "4.0.5": "2016-07-25T14:46:28.634Z", "4.0.6": "2016-08-13T17:41:07.730Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isplainobject"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"nicodinh": true, "ubenzer": true}}