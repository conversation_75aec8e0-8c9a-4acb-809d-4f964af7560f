{"_id": "cac", "_rev": "133-c129c2ac97c48c7b6d5d49490c4b9a66", "name": "cac", "description": "Simple yet powerful framework for building command-line apps.", "dist-tags": {"latest": "6.7.14"}, "versions": {"0.0.0": {"name": "cac", "version": "0.0.0", "description": "command line parser made simple yet powerful.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "keywords": ["cli"], "author": {"name": "EGOIST"}, "license": "MIT", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "dependencies": {"minimist": "^1.2.0"}, "engines": {"node": ">= 4"}, "devDependencies": {"cz-conventional-changelog": "^1.1.5"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "gitHead": "da3fcb891b991aad102de8f45c55d52feb2b8530", "_id": "cac@0.0.0", "_shasum": "0d41b546183451704bd3a6fa64ec29ffff567c0e", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "0d41b546183451704bd3a6fa64ec29ffff567c0e", "tarball": "https://registry.npmjs.org/cac/-/cac-0.0.0.tgz", "integrity": "sha512-O1+E4uaCjvmeUf/gtfypDD+hG0X6syrhcBtpWQTOfD+5pLW2eotiM8VXCP+hTuqllb91cOnMxfKALlVaUX7MgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlmN6T7/B7sAodVBi8WRUN37i9ToWHnfdjt6XDhGLMawIgNLKx4sECVweL7WKBZF8fUVCm5ICMhAr3zX0gPIoSG70="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "cac", "version": "0.1.0", "description": "Command And Conquer.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "EGOIST", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["cli", "commander", "meow", "minimist"], "devDependencies": {"ava": "latest", "coveralls": "^2.11.9", "nyc": "^6.4.0", "then-sleep": "^1.0.1", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"operator-linebreak": [2, "before"], "no-warning-comments": [2, {"terms": ["fixme"], "location": "start"}]}}, "dependencies": {"camelcase-keys": "^2.1.0", "co": "^4.6.0", "deep-assign": "^2.0.0", "is-generator-function": "^1.0.3", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1"}, "gitHead": "9e8616c972fc7a2b1aef6c51228ce968264e1568", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@0.1.0", "_shasum": "ab8bf841182952b1a6a112a371ddb645897e59c8", "_from": ".", "_npmVersion": "3.7.2", "_nodeVersion": "4.2.4", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "ab8bf841182952b1a6a112a371ddb645897e59c8", "tarball": "https://registry.npmjs.org/cac/-/cac-0.1.0.tgz", "integrity": "sha512-GXqM5NC/WkWxuyMrnUCkRQALVu/yEm7MiKRYzcYK2Z+K3S40VqFucWYnPnmPjV/nW6+j4MnEv6TGW45ZxuJyUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgi6/zEMi4ZuDEP4fkR83d7jcPd7I/ZW/F0uNRYz1PFAIhAOAliBNlLxyauuB3EzcjmfIDO20+BAY0cIpJRh2g9Cqy"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-0.1.0.tgz_1461858829817_0.21692546433769166"}, "directories": {}}, "1.0.0": {"name": "cac", "version": "1.0.0", "description": "Command And Conquer.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "EGOIST", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "example:help": "node examples/help -h", "example:alias-command": "node examples/alias-command r"}, "files": ["index.js"], "keywords": ["cli", "commander", "meow", "minimist"], "devDependencies": {"ava": "latest", "coveralls": "^2.11.9", "nyc": "^6.4.0", "then-sleep": "^1.0.1", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"operator-linebreak": [2, "before"], "no-warning-comments": [2, {"terms": ["fixme"], "location": "start"}]}}, "dependencies": {"camelcase-keys": "^2.1.0", "co": "^4.6.0", "deep-assign": "^2.0.0", "is-generator-function": "^1.0.3", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1"}, "gitHead": "71dde7cc2c01ed87540d9c92a80ab22995d165b6", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@1.0.0", "_shasum": "84cf96064b568b02a8bd087de9e2a04413e9e613", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "4.2.4", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "84cf96064b568b02a8bd087de9e2a04413e9e613", "tarball": "https://registry.npmjs.org/cac/-/cac-1.0.0.tgz", "integrity": "sha512-lV13ZoBrgnGtzg05Z7WlxPtJXpWXay71XfABIdI8dNZOBug05kGXvxNXkQyP0PpbfWW192Y2/hFUTzvHexj5Bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5sNPXVwFZnQHs9yQIL0Fk1X4AK/1f+DFrpB8unbyIDQIgSyhHVpxIDlwNFada7wDSsmBiYG5Kyp5g1hjBHnpRRus="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cac-1.0.0.tgz_1461920855150_0.902758345939219"}, "directories": {}}, "2.0.0": {"name": "cac", "version": "2.0.0", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"testScript": "test2"}, "gitHead": "1dd5bc0813fc905f9385c2e0a6370a72c3fbb5ac", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.0.0", "_shasum": "4e7f80ae2312a8722853bd9993ba85a34c74c648", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "4e7f80ae2312a8722853bd9993ba85a34c74c648", "tarball": "https://registry.npmjs.org/cac/-/cac-2.0.0.tgz", "integrity": "sha512-ozNI9WFPddwf3cM3qTIkLafMdYyCw7iOco0xgqNzUTeXvYy856DVFksHxlYWphJlWHeiVOFYEU2XivNby/Injw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgCyM5I/J4DxH/rgrzUUyUXiT0xR+9nswvbkZHmlsu8AiBbc9IDv50ARU8GjWxGfHlBb5xFxJuZJrTx8hbyGvMtcQ=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.0.0.tgz_1472376016916_0.8896047654561698"}, "directories": {}}, "2.0.1": {"name": "cac", "version": "2.0.1", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "97251aecca9cd5c70820de932588ab0e190fa3e0", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.0.1", "_shasum": "1f622dcbb133d6d621463b8846b2f9b374b70fc6", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "1f622dcbb133d6d621463b8846b2f9b374b70fc6", "tarball": "https://registry.npmjs.org/cac/-/cac-2.0.1.tgz", "integrity": "sha512-rM7tqpBitM5ju8E1wnqGUjERKShLyJc5ktZeLHuIxjTUMxFF+7Rp5vlCXMJeYAyFByN76FdYD08o5oRLeo3geQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEAaoxMx/m4Y7bWYlcTZ5Rf3ffk77gbLXMgDTD3QYA7AIhAKqDKZzXZZZKGrQiP44imbw95/rnymnbvsYwqiKaSfN9"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.0.1.tgz_1472378056655_0.1761709344573319"}, "directories": {}}, "2.0.2": {"name": "cac", "version": "2.0.2", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "e7462a323a14628cb778b5c753559faae198ceda", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.0.2", "_shasum": "c989e12be5226ee71750961950a7ba46db4831fb", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "c989e12be5226ee71750961950a7ba46db4831fb", "tarball": "https://registry.npmjs.org/cac/-/cac-2.0.2.tgz", "integrity": "sha512-IRPwWkDaTCfEsRlNbpCTf2RCZ+sbvNzJFyHgiphNvGE4Bu916ZqX28LAYoKoKl0LI3XCZu5Iv6xoRZiWKVhy5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjucGlV75bwT/7LI5yzzWhQptJlCLolWbAGXy3BNes7AiBR9xI6kkGDAHMzIZpZZQW7gC/2CXn+AE6dVrVpU3pYGA=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.0.2.tgz_1472379307330_0.9612424371298403"}, "directories": {}}, "2.0.3": {"name": "cac", "version": "2.0.3", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "659989373e8761f85b9cf7cf3ab78966ea722f9a", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.0.3", "_shasum": "36b390213bb6ceecb2f33be1eb25614fcb31b20a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "36b390213bb6ceecb2f33be1eb25614fcb31b20a", "tarball": "https://registry.npmjs.org/cac/-/cac-2.0.3.tgz", "integrity": "sha512-2168S8m1/RMPwTqJdGvlLCs7pUsDZ4IqJdtm8LqqmLd0gRDA2wuLBlw64yeU5ItgyoerIxmYPx6udVJACFhYAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAJiqFtZzeAt0FSP0qoOJT/7gMoleHKkbI5cArxMyx2jAiEA/vfmKtZrbITlKops0aSwqTbMkgOdS94lzVQoVTLMjBk="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.0.3.tgz_1472389821946_0.7794828207697719"}, "directories": {}}, "2.1.0": {"name": "cac", "version": "2.1.0", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "9040bab9cfffe37324b740ac2b952e106086792b", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.1.0", "_shasum": "b9102fd6c15264ae8380c3889e0c62f16f4cd72a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "b9102fd6c15264ae8380c3889e0c62f16f4cd72a", "tarball": "https://registry.npmjs.org/cac/-/cac-2.1.0.tgz", "integrity": "sha512-f/O8VUFsjKvPJ67GgvovAHW7UXRSU2zsmRL2Nr8TRJXLAvDyrrB424wkF31Pfbe2ZxT0R1SO2j2LjE03oyKiUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB+ER+V2d4z4SX3LQ4Ksz3GE2/IVml1z4mXbkG3GSjLMAiArAFK6wZcs25P5nqFyUOxNulc0P2WFkkst62v+mlVXDg=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.1.0.tgz_1472392043001_0.35682942625135183"}, "directories": {}}, "2.1.1": {"name": "cac", "version": "2.1.1", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "b8849224bfe1d780001766c09eb3cb5f06800e87", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.1.1", "_shasum": "15f4e1daed2432397d8ff88295651ae396520d91", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "15f4e1daed2432397d8ff88295651ae396520d91", "tarball": "https://registry.npmjs.org/cac/-/cac-2.1.1.tgz", "integrity": "sha512-dDW1vhbzmz7DVho+llZaCtQRXb/SFM2briUFATeX1T43K5eH/hravigU2+9Ja6A3Xxn3foPQLckBt88tkh/GiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC04nIfq+Kx5W+X8YJJY0Dd6YSoMqZWwtI0IdIpMYfeNAIgR/HxkUGSEB+wNNJuyTygOwLw4OziVS+vJIYNozuvWWU="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.1.1.tgz_1472393032254_0.9371873319614679"}, "directories": {}}, "2.2.0": {"name": "cac", "version": "2.2.0", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "c42edc79033f13f3373f5eb213ef974c022d002f", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.2.0", "_shasum": "61c10f3a3b6dd8b2533fb98d87691fb7210db558", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "61c10f3a3b6dd8b2533fb98d87691fb7210db558", "tarball": "https://registry.npmjs.org/cac/-/cac-2.2.0.tgz", "integrity": "sha512-xKgff464jjRPCa1sngZJHx4A/U16ZS6YJaKwZPxUeLE1P+cjLsEiBlTsXPIfSsc7WzZc9qzn7U56KXg1b5qUbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICk0modN2pMMWIzyTa+Q2EMzEe8ofpmGq6lYFtCOG8zyAiEA/NJKy8UPQA8OZB7YmMbySRj//FSW5/KzOWAvEtKerQU="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.2.0.tgz_1472438775275_0.10297623206861317"}, "directories": {}}, "2.2.1": {"name": "cac", "version": "2.2.1", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup", "dev": "node watch"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "7fc3f2622f3f8b9d05ed1ff6bd7c9457651780a9", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.2.1", "_shasum": "33a35c19e27c84a370fd1847232fd0224f40d1eb", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "33a35c19e27c84a370fd1847232fd0224f40d1eb", "tarball": "https://registry.npmjs.org/cac/-/cac-2.2.1.tgz", "integrity": "sha512-szE0SEYY/ndSg32joOoxWTjuMD3KoO2H3PsphJL6+u/qA4pSlChDE/Fqd6WmWFhLRTpF/MbLXMV5c5rLo3GaRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBi5SoxeKOpoZjUPv/Bv5CKgkpi7t99XMUCtRw+XhsB4AiAu5l4p3rPvzly3zf6f1KJ5lTr73G5HQWkDpiJT78PKxg=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.2.1.tgz_1472466781079_0.7791916392743587"}, "directories": {}}, "2.2.2": {"name": "cac", "version": "2.2.2", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup", "dev": "node watch"}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "aa244a2c637574667ae09208eeaad0f4a422ac23", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.2.2", "_shasum": "907e09fbb7892cb1b36a9201cb9b77ff4d6fe22d", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "907e09fbb7892cb1b36a9201cb9b77ff4d6fe22d", "tarball": "https://registry.npmjs.org/cac/-/cac-2.2.2.tgz", "integrity": "sha512-HzNkwcu5t3ZszCv/vCxWUr8fcuaMWIziosfbt5b11BKZ4jy/mT2FUJzOMraTFV5i1dRmphQgii8kN2R/LnR0Yw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk6XVsv4gAjtm3tzzbdAU5jdGk22mtm7V0CTpKn4+beAIgGwzjQomArgdAdwbb+IcRESbZ3JKKi3olaTQyYaL/yR4="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.2.2.tgz_1472794662588_0.31051017134450376"}, "directories": {}}, "2.3.0": {"name": "cac", "version": "2.3.0", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup", "dev": "node watch"}, "watch": {"./src": ["build"]}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "a528984441e59dfaec5dc33f595f48089f4c9b89", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.3.0", "_shasum": "a14c82d4fec8758a82b2a5fac9f35753c766b4ac", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "kchan", "email": "<EMAIL>"}, "dist": {"shasum": "a14c82d4fec8758a82b2a5fac9f35753c766b4ac", "tarball": "https://registry.npmjs.org/cac/-/cac-2.3.0.tgz", "integrity": "sha512-Gfzy3GJDsveIJvpHt7TjhwPgsKqak9bbSsVZAoPxdycGWYFfC2kzAD0AK5wng4lZiAs/2uI7hW/DYYEX6kT5ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDpQIcI+/fvu8SZK4fVvt+JJMG4KWncJB0exnDOvR2VUAiEAy9Blt+jwdRT+OyAoU5cZx0j3DoveK7TtNLCjziMQyTc="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cac-2.3.0.tgz_1472980320781_0.1935557909309864"}, "directories": {}}, "2.3.1": {"name": "cac", "version": "2.3.1", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup", "dev": "node watch"}, "watch": {"./src": ["build"]}, "files": ["index.js"], "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "fead407bcb00f8d108f570e6761a38dbd38f495e", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.3.1", "_shasum": "ae8fa3d25179b669cc7d1185275a5063cb6b086a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "ae8fa3d25179b669cc7d1185275a5063cb6b086a", "tarball": "https://registry.npmjs.org/cac/-/cac-2.3.1.tgz", "integrity": "sha512-wKdKKHNeOMuqAhH+XJT6GtZcqgNghDVS/PgPWNmAbQhW/a8mJggPffmOWk6lBvy2JcmRCizaEUI9umCkE8Se/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAhlnaXWfR8Mz0POUYn6Vn3xcwYvRpRqi2jt1NhsxKBBAiBzKrYMLQ5HM1GbwUpGXA1JKzhqWmHRrX3pPKHkzZEaLQ=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cac-2.3.1.tgz_1473921569410_0.3027623272500932"}, "directories": {}}, "2.3.2": {"name": "cac", "version": "2.3.2", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bubleup", "dev": "node watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "text-table": "^0.2.0"}, "bubleup": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "27b58b790354cd1bd2546dd780e0e35d453aeccf", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@2.3.2", "_shasum": "8826353e82ea8df6281bb30d8b46d60cbaa2b4fa", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "8826353e82ea8df6281bb30d8b46d60cbaa2b4fa", "tarball": "https://registry.npmjs.org/cac/-/cac-2.3.2.tgz", "integrity": "sha512-rGqCrTY06RT5nNZtfRCrMZa2P0FDK4/f0OltuAUG/osvcHU568p+oT4cOMFNY/2SgVHTSrwTL7amhkty7OOpMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ/jIqfzZEVuhq9J26A+5eXaPb7D2OaCLnh18h0lOLggIgCbskvCZ+B5UVcPkbV4Qw6q75I33T6VofAMjFe3Pf9oc="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-2.3.2.tgz_1474371787886_0.2062102125491947"}, "directories": {}}, "3.0.0": {"name": "cac", "version": "3.0.0", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bili", "dev": "bili --watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0, "unicorn/no-process-exit": 0, "import/no-dynamic-require": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "suffix": "^0.1.0", "text-table": "^0.2.0"}, "bili": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "134f761d0f0ed23c1a0a3f09bd0b1aa415cd5eff", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@3.0.0", "_shasum": "0f92702ac20c59ac5a77e5807ba44502000dfbb4", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "0f92702ac20c59ac5a77e5807ba44502000dfbb4", "tarball": "https://registry.npmjs.org/cac/-/cac-3.0.0.tgz", "integrity": "sha512-6Bp3VneC6sf7E7hOUqWPQf9H5BiDF6hdCee8v2+z98Uk4SkKNrmo3cg8vAq4ubrRxwA3VEhXbNTw/zFQr6GF5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDR4oDY8PlSuQxO5op+Q0cbG7zZG0up0Qd0xSK3X+gpnAiB39v0JzoLGpUpQwY3yaLizfcHcFAONPORV3vTuimu/Uw=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cac-3.0.0.tgz_1477581402861_0.37951334402896464"}, "directories": {}}, "3.0.1": {"name": "cac", "version": "3.0.1", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bili", "dev": "bili --watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0, "unicorn/no-process-exit": 0, "import/no-dynamic-require": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "suffix": "^0.1.0", "text-table": "^0.2.0"}, "bili": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "fcda8ae095c10e8df3d7b795cce959aa744e21f3", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@3.0.1", "_shasum": "e1c7d20d4e79d8638d6dd29a2c23d51e484cced8", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "e1c7d20d4e79d8638d6dd29a2c23d51e484cced8", "tarball": "https://registry.npmjs.org/cac/-/cac-3.0.1.tgz", "integrity": "sha512-28iK0wlbhrIlb4wpV3tHvr7fr32d77A8bdFTuiqZquc+zncbyTWZmDl42O+5fMxHJlS2dvaKCpl2nLE/d/y9uA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8c83e7ZwPznPNRpGrxGIA5O3s8E0PgimKCx1m/l8mvgIgNJMQ9mL5wNiw2NPT0s4rrHwZPiipdA+uVJh/krVu9Gk="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cac-3.0.1.tgz_1477583479423_0.09910450433380902"}, "directories": {}}, "3.0.2": {"name": "cac", "version": "3.0.2", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bili", "dev": "bili --watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0, "unicorn/no-process-exit": 0, "import/no-dynamic-require": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "suffix": "^0.1.0", "text-table": "^0.2.0"}, "bili": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "4e3ac0fa7f85d0e5c133929ea73e6b4103d193b4", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@3.0.2", "_shasum": "aad7d68ab3df87d6a6a0248726c2906d895303d0", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "aad7d68ab3df87d6a6a0248726c2906d895303d0", "tarball": "https://registry.npmjs.org/cac/-/cac-3.0.2.tgz", "integrity": "sha512-7Q18ZvlQpJkxIBPdpe0aQgK+BVbiWXcAKdcnLjqdJZbFoD8EX3/84TyfxNyX+4TLIxFVAKzvqR8eTgA2+Nk5KA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRXtLe+rd6TOitUUbiTwTbnNGzbrkT1sq+q1R/56Ft0AIgVV+OQUT3GJJmqSaaE5nLWzCAPy+IGF+IJZ+PfZpRzwM="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cac-3.0.2.tgz_1477593478531_0.3355484220664948"}, "directories": {}}, "3.0.3": {"name": "cac", "version": "3.0.3", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bili", "dev": "bili --watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0, "unicorn/no-process-exit": 0, "import/no-dynamic-require": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "suffix": "^0.1.0", "text-table": "^0.2.0"}, "bili": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "909b6d88ba21366cd9735c64cb351fee82687f7a", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@3.0.3", "_shasum": "f02d451bbc8f2a7eb2708f6019393e783bffbfe0", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "f02d451bbc8f2a7eb2708f6019393e783bffbfe0", "tarball": "https://registry.npmjs.org/cac/-/cac-3.0.3.tgz", "integrity": "sha512-KIS6rk0MOdfsPFWs28kOId2eyWcFyxd3/uoMDWGf4KvObhT8Rs7xnX6KiO9LNwfUi92tXRvZWlheSTYAQIp7nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6/a/TZMUVZQ5zULPOo7py6EM80IY4hFXtU8Z7+qP4PAIhAJaqI9PpyrCdc8SAGZtmLKPIxx+Mozk+zkWe9my8Yy+d"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cac-3.0.3.tgz_1477984936508_0.6361349150538445"}, "directories": {}}, "3.0.4": {"name": "cac", "version": "3.0.4", "description": "Command & Conquer, the command-line queen.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/egoist/cac.git"}, "author": {"name": "egoist", "email": "<EMAIL>", "url": "github.com/egoist"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo src/index.js", "test2": "npm run build && npm test", "build": "bili", "dev": "bili --watch"}, "watch": {"./src": ["build"]}, "files": ["dist"], "main": "dist/cac.common.js", "keywords": ["cli", "commander", "yargs", "args", "meow", "minimist"], "devDependencies": {"nswatch": "^0.1.0", "xo": "latest"}, "xo": {"semicolon": false, "space": 2, "rules": {"xo/no-process-exit": 0, "unicorn/no-process-exit": 0, "import/no-dynamic-require": 0}}, "dependencies": {"camelcase-keys": "^3.0.0", "chalk": "^1.1.3", "indent-string": "^3.0.0", "minimist": "^1.2.0", "read-pkg-up": "^1.0.1", "suffix": "^0.1.0", "text-table": "^0.2.0"}, "bili": {"transforms": {"dangerousForOf": true}}, "kanpai": {"test": "test2"}, "gitHead": "e123c81a292b8ff129d2c6de3f4e387dd0bea4f1", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@3.0.4", "_shasum": "6d24ceec372efe5c9b798808bc7f49b47242a4ef", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"shasum": "6d24ceec372efe5c9b798808bc7f49b47242a4ef", "tarball": "https://registry.npmjs.org/cac/-/cac-3.0.4.tgz", "integrity": "sha512-hq4rxE3NT5PlaEiVV39Z45d6MoFcQZG5dsgJqtAUeOz3408LEQAElToDkf9i5IYSCOmK0If/81dLg7nKxqPR0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAWyZmwjJ/YNlijZ0x8DhTJmvMDUSgI3UInTdNuchwt8AiEA7kTHEVBCw979Wv7iyRdGWKlV/RhcNszwp1ds04L+eeQ="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cac-3.0.4.tgz_1479919036758_0.776412**********"}, "directories": {}}, "4.0.0": {"name": "cac", "version": "4.0.0", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "echo 'no tests!' && npm run lint", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0", "winston": "^2.3.1"}, "devDependencies": {"babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "xo": "^0.18.0"}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "dee6a3f3ea2836d8144188c011d0076e6a1179ce", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "7.6.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tZbijGF4yQDZgvQtTM2+Zgo+ks7ZJ5yfhjNoaOnAmC4BfC/imzoqd+9EnRotMVITTVCMKE2kZ/z36noFAOOhtQ==", "shasum": "fcf5974f099f425729162a5cf781249db5eed272", "tarball": "https://registry.npmjs.org/cac/-/cac-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzrKmPIYwEzBd+LPEWgZK/fH4NkdVaMsiA03qgozPBxwIhALgYwYTFvUwoFMELtodwENh13fl79oiH4NGT5/M1VP3Q"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.0.0.tgz_1501057960744_0.9955184182617813"}, "directories": {}}, "4.1.0": {"name": "cac", "version": "4.1.0", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "mri": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "fa0b3bb21e908661a4294ad40d2a90dbcd1a2815", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "7.6.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KNUoIMu/J0DVBMaXr9yeZT18pm+omdT04CGziedVGgmOkmX3X7c/ttF7X4SbBEsgQWhuFWo0y/L5FVgRpbJBTg==", "shasum": "17684e188cad95059bfd92525a0cd431fb19aab0", "tarball": "https://registry.npmjs.org/cac/-/cac-4.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBF9Y0nGKajQShYmRUk8S4r/wEdZyl2w2bIgLWJpqgaQAiBLqPJ/oTLpAeTFXA7QkrEvmTMh8hFGq15Ejb+eEr3DTg=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.1.0.tgz_1501253106156_0.6765515957958996"}, "directories": {}}, "4.1.1": {"name": "cac", "version": "4.1.1", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "4a2d8f0431f8f249510c9f3ffc2b567ceb0c618c", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.1.1", "_npmVersion": "5.3.0", "_nodeVersion": "7.6.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MHrkQhuvwBqDiAzDh3hGR1llwD8g86CDWvQpw2xUyfABzBycfO5kTEQPYbRXAOGw5Nbjv27H5AEnREvMITitrA==", "shasum": "491a05944175642265dfda306cc0d0ed3877783d", "tarball": "https://registry.npmjs.org/cac/-/cac-4.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5ycRPVVpaBw4SlpE+KJFOvOAPcEwRGZ+xPBlDB4G1EQIgA2nNWFZ0L+Re1olKHTtbVgQxJsslfpgDkMg0tphntsM="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.1.1.tgz_1501255756978_0.5816148270387203"}, "directories": {}}, "4.2.0": {"name": "cac", "version": "4.2.0", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "4149772f62a033151deb13eb05166e1feeb13817", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "7.6.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PG3SxlTRESXZi6LOKxaSwTqGLqJh04ZhwCLd+BvY1rXpcrpCHhbvjnVz7Pq1vNfYnwwW7QzY7/xgRdpRzqlcQg==", "shasum": "e79eb86d2a784ad02b5fb704f17a4128dd2e7082", "tarball": "https://registry.npmjs.org/cac/-/cac-4.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFcZDRgjmjUHT06EHg/ZKQ4ngf2VbPR4RpFOo2U2ZhqTAiB0hJ9Grcx+gvL5FPWm5ZvR150pSCKN5kehefLOUDy0xA=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.2.0.tgz_1501338497987_0.24529933230951428"}, "directories": {}}, "4.2.1": {"name": "cac", "version": "4.2.1", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "e047c331cb4a6495a0bf962788d426fc495e643f", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.2.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Z<PERSON>+KiezmMqK87tkgZ1MS9SFO4sQiYkQX1HSup/zFnXsNMQ3yKNAfsYtUQL7dBkf3atcYRYw8p7vMXiq0rYJqLQ==", "shasum": "a4c1eee2f33d404cc0b068c63c67499d5d79e279", "tarball": "https://registry.npmjs.org/cac/-/cac-4.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDNXS4phQoVJkdFxgGmXQgTqtNLZtaih9Jf3/GEge3obAiEAgQE3z0o2NrHfB9QbpItnudulITNDr3elGEEzJsFWGcg="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.2.1.tgz_1504401942494_0.9743832123931497"}, "directories": {}}, "4.2.2": {"name": "cac", "version": "4.2.2", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "ffd886d44adff39e30908c857316e5730db7f230", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.2.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fqJeNRfftPlGuIdN6VhaeuEQfIzDVCOhykcWE+jhQZ32Tqr64NSRRjAKw9uWBv89UymhcyFsnpuyPyT/4MArjw==", "shasum": "d268ccd4423692491dfa083660574d2cd7e248c6", "tarball": "https://registry.npmjs.org/cac/-/cac-4.2.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2XLU7eqHbxiMzYRR4BqXTRWdxAUMhR1o8Nhk/P4BojgIgTgkjusrbHHUlsxgwiFmy5eWjJPWJxqGdPHm+WRIo5Yg="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.2.2.tgz_1506221685094_0.27371677034534514"}, "directories": {}}, "4.2.3": {"name": "cac", "version": "4.2.3", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "a6edccd09ebf9d28dee80cd17f9fd59b9025aef3", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.2.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YSWsIDVtwSFn5gBdjl83TY+uLA99OXaRsflSke6YfkHES/FzbniMogOQ2S9Y7N4XDmnr0BkRVOY6itf68nPzrQ==", "shasum": "f62e0fedd235c18b5db1dd5253329b3b466f289c", "tarball": "https://registry.npmjs.org/cac/-/cac-4.2.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyNPHCebs1ynBKKkwoA3ipbCNptxteCI+x4q4yOHRaTgIhAOv4Y4xocdstcw1XyTFccIWoT6Eis0whISqz6ItTYVmW"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.2.3.tgz_1508339536676_0.7564813757780939"}, "directories": {}}, "4.2.4": {"name": "cac", "version": "4.2.4", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.common.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 4", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^0.17.3", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "f8d315a7174b69a054f47f0409259cf28bcaac59", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.2.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NDrDHpl54rbeIrtrCpuSzr1bIPBA2GH3l8vmQt6a7QO1GB2Iw7Z/OcJPtd/nJpruK99dQ1HpEaBad55vg05vKQ==", "shasum": "019f35db752f13ee57ba7499e524de940c89528e", "tarball": "https://registry.npmjs.org/cac/-/cac-4.2.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1ig6oJLlYmXQFgxEba8cDn/i+nj5vJC7pNpi7IfYyTQIgGUC32gdmf6375V4CQF9yZQv+f7r0nGE6V74uQNzmEI4="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.2.4.tgz_1508944725185_0.9603231796063483"}, "directories": {}}, "4.3.0": {"name": "cac", "version": "4.3.0", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "40ed09d53c84b61afbf6c63806dce3bdbbbeeb63", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-huz+9U88F0LONRvy3fShkGCbbAZUJ71OWR4oQlXKEL/hVcr4nTMP1vD/ZsLiCSdCYeYfrz0XQAQhC62cuNZvCQ==", "shasum": "11e2acaa0b180716fc3ee843949b70aa7cd3e6ca", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHREf4mOJK5wrijR3qTeySDfuSFs6gcob+0fKSTbp2bAAiEA0VBmOlsnysbUN48RV0BWw6jhtNWUQzw6JaFvjFqIlJU="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.0.tgz_1514446925723_0.7830794553738087"}, "directories": {}}, "4.3.1": {"name": "cac", "version": "4.3.1", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "8def816b03bd028a01b6444c0398bedbaacc1505", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JGr3i8CGHyIxnLXHwmK/8iFHLxw+VN3hhEmtjxO2CikhpAzxxhes7atENArW8NZf0wlfCUOc39gAIWg+TCg44A==", "shasum": "be5e9e521626c9f0b324b70af1d0f7471eba36d3", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7BO4kzgeddNQY3e6ER1T2CwGblWRotELj7uQaxT8ZcgIgCTZlLF5D+Nrv/Hnu1pQpCoXL/k5jptPkb8ePm0ZWNHQ="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.1.tgz_1514606747682_0.5648776334710419"}, "directories": {}}, "4.3.2": {"name": "cac", "version": "4.3.2", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "8d73855408149d1dc8abbe6944b0b414dc4ae418", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VHNuOoTkYF10msdM9Hh6OBh9r9BF3wRDbmHzkW7a1D0s60SskXSWrmD8GUSiZdC+x4I+4SAXOC5lazhci7JEig==", "shasum": "4e25b5b6679524972189f6cd3721c9b3af9b36d6", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEUAbfagJ98hwl3ptu7nJkOwpeiwPysB2JlQIpF9MYOAIhANkzcNSDWCt/Q56wCPX+DSmDdIm9/8fPgINYPapY0svs"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.2.tgz_1515660166926_0.8828573278151453"}, "directories": {}}, "4.3.3": {"name": "cac", "version": "4.3.3", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "86236e806e44b15b057f05dbd0f7fc2eafdbd9bf", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eP3zxswP50JTg2UxCDU1qbKvKLxZp763GvcR9x04MnnPrmLG2KHF8CAcDqKgMrhPiPoJm0Cje5AUPPJ8UOUy5g==", "shasum": "7800530dd2a1f41b91d4c7504e8c6b146cc1227f", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMNHXPxY6BdXFW1iQWyx/q7SyYc7HV2XpafhLviwj2vAiB8rOIbmcfcBxfeYXQs3Omyt9lYYDJBdkOc/YRqQK2iRg=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.3.tgz_1515661791114_0.14778707129880786"}, "directories": {}}, "4.3.4": {"name": "cac", "version": "4.3.4", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "23e54a633687d5c06185c1f475b8842f8169a689", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IapGElve7mP7NsAJ/GeaZqomacVPbp/ej04KUOa5gwtp7io8eoAK4j/d5cPjqDteVzmC1iVL2uU0tROCgNYhJA==", "shasum": "ec994ca18ea4b4a1a2338cf5d49d070e19774377", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJtrUYi60/v5471GJJPlKfbxsDm0KB9FK67IhTHHxijgIhAOa1YDnTzvC6SP5r5Jv0gqR8fq7GdA4498sdXBBKSNWW"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.4.tgz_1515661860710_0.8916400435846299"}, "directories": {}}, "4.3.5": {"name": "cac", "version": "4.3.5", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "307d2bbfcba8ba1857e07705dc56b47db1eccbdb", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1Jtc+WBVx6nJnzPAfpHX92PuLs5pH5DyjmNPE0gVqimFCZqIn+7yAdTSloC2XkLI1RkjBYf2UXXjr5WoNyBfeA==", "shasum": "f1b21193c390988d52dcdaf374a7316d8c537a7b", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqNjboidlMCtwXJVsS+tW2p6CDyCtb17kjK/tLwn54TgIhAOORacHMC7CnydNiyNNfQicurM2Ef9mPeM7x5A8B667z"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.5.tgz_1517212135300_0.31918853195384145"}, "directories": {}}, "4.3.6": {"name": "cac", "version": "4.3.6", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "ceb69244135b22e61570fdd97e53b2b06867f39a", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.6", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PFWT4/fumEuNOFdtunCFunFqhCO22zgauDdevg0KhL2EFhvQFRHjymCU9XfDAMQmTf3kAnhbxtW6ssJtuAZ05w==", "shasum": "b98997d846c24a7b17e4344306c97c0b578cf694", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0TJjbLM3JWZygCXfXdVHW3umLjcKpcrZO6R/8wlA1VAiAVE15j8ncfBa+PBsLxPtQBVF4Dk7jB7T61jpbur/WD0Q=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.6.tgz_1517212490653_0.5881075141951442"}, "directories": {}}, "4.3.7": {"name": "cac", "version": "4.3.7", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^1.1.0", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "b25f0e8a5644fd3aeea8df4f61ebc9b0a1ea2436", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.3.7", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nqWi3IyhSndfHDe2F1UF6gYy03Ak/0JJKq93PKgTGz9PSmZQv+qrY2gNpdBY9T+9k2bCSZXSkW1ppWfvf8wr/w==", "shasum": "3898dd6a247e1923bafdf8c948c6801d9f2e9be6", "tarball": "https://registry.npmjs.org/cac/-/cac-4.3.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh7G2r+TPULy6GQqkDpOUwVIGvZOtMbmVqrOf3lfJrLwIhAKKrBbeuMEHECRynhuL94ey/8oZcthBFVal4MaRm3vmd"}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.3.7.tgz_1517381151838_0.18821849790401757"}, "directories": {}}, "4.4.0": {"name": "cac", "version": "4.4.0", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^2.2.6", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "edb59f2fbec120253e130979d399dca40cfe8959", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.4.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Jqc0DebeTJF4zic/hK8hUBSx7yoMdcLa/AST0WgX5qypIT4yI+HaPTJHmL1SmcvGPzGYWyO1pNwE/U7zFVo/Dg==", "shasum": "00f16e3685878e27c6ebebabdf394cda45acb6b7", "tarball": "https://registry.npmjs.org/cac/-/cac-4.4.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYxg6ZfM3IHUdfJ0MomTMBvubwZQwFZd7sF6shghdZbwIgXXcAVt1i+UEovzmdNxI9k1UEllFQK/8Jhn3YMp5/c6k="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.4.0.tgz_1517408348474_0.4325934329535812"}, "directories": {}}, "4.4.1": {"name": "cac", "version": "4.4.1", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^2.2.6", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "gitHead": "03822fcc42cd7d2b18b262ba9f542f3110027bfc", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@4.4.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ab4JHxgvSNI6niHz5M3JCYRXnzERhY314HDwkndOh7FmIyOX7xY+iUqsl0nE17wjt8+kdvxw+b1HDtmFMiVPNA==", "shasum": "c6867f731c4be7b0c270689d1689400b914c7788", "tarball": "https://registry.npmjs.org/cac/-/cac-4.4.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF+y/Gt2zxokyPUqW89/WMqjf6/gn3iPCYG4RfO7EMXhAiAO6yAjBGV/Q9A7i8E6Kwr4KLLxJfkinHlNsU1EetnJeQ=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac-4.4.1.tgz_1517409859874_0.358522058930248"}, "directories": {}}, "4.4.2": {"name": "cac", "version": "4.4.2", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.21.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^2.2.6", "eslint-config-rem": "^3.0.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@4.4.2", "dist": {"shasum": "ac6a36e1746f9d27edecb3a2b3c5ae7e1f04ba66", "tarball": "https://registry.npmjs.org/cac/-/cac-4.4.2.tgz", "fileCount": 7, "unpackedSize": 21930, "integrity": "sha512-RDNjym+M9/0Iz1xtjSQ2CT7mLflo416bdX9ogHVM85A+Ij3LzHv92aCT8Gxm1vkmN7JxeBUXV7frP0HaG5rpIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBT6EJ6PAWKTsj+7IStfPgGY8DWiygHkqQRneqRpn+95AiA3NXsy3wFFcZwpndwm3fYrJrifgHysDzZn00UJwNDT0Q=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_4.4.2_1520687342896_0.2892311138158954"}, "_hasShrinkwrap": false}, "4.4.3": {"name": "cac", "version": "4.4.3", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist", "index.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.25.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^2.2.6", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@4.4.3", "dist": {"shasum": "88e96116cf2e10416cfb38158e5c01f8448bce5e", "tarball": "https://registry.npmjs.org/cac/-/cac-4.4.3.tgz", "fileCount": 7, "unpackedSize": 22097, "integrity": "sha512-Ni0xshit1m8XCgNhsf1yq2sWSzOq605UqUt7vohl3BU3UU/5zLe4xSbj7H1uICaBVn52OIbzNIgjrwxl4r+IzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwJ2XeNA8yIGJrhx8VgWqD3bOv8PjLcxF+SFbuI6Th8AiAMbhP0ZEn2osXmBVlg/3cfJU8hU+VJxUSB3413wlM8zw=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_4.4.3_1520703932535_0.4962556740610329"}, "_hasShrinkwrap": false}, "4.4.4": {"name": "cac", "version": "4.4.4", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "files": ["dist"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "npm run lint && ava", "lint": "xo", "build": "bili --buble.target.node 6 --filename cac.js", "prepublish": "npm run build", "toc": "markdown-toc -i README.md"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.0.1", "minimost": "^1.0.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"ava": "^0.25.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.24.1", "bili": "^2.2.6", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "markdown-toc": "^1.1.0", "xo": "^0.18.0"}, "ava": {"babel": "inherit", "require": ["babel-register"]}, "xo": {"extends": "rem/prettier", "ignores": ["examples/**"], "rules": {"unicorn/filename-case": 0}}, "babel": {"presets": [["env", {"targets": {"node": "current"}}]], "plugins": ["transform-object-rest-spread"]}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@4.4.4", "dist": {"shasum": "dec5f3f6aae29ce988d7654e1fb3c6e8077924b1", "tarball": "https://registry.npmjs.org/cac/-/cac-4.4.4.tgz", "fileCount": 6, "unpackedSize": 21965, "integrity": "sha512-KAaI/eh+BLYKEOQutYUT9nU9n9G2OaX5sxwME+WPYgUk2nbkerIGmk7hEaA2epTdhIePaurDsW4Gxl9GCNvjUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHMfD0qGNy4pkCY/pfg5dPQfIlG+BCN6DWpADaeCU7uvAiAI7mKYHGA1XOFG8sM22rhzE6+5YhDM/Uwuita+SogNZA=="}]}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_4.4.4_1521560921911_0.8250328902942101"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "cac", "version": "5.0.0", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "redent": "^2.0.0", "rollup-plugin-typescript2": "^0.13.0", "string-width": "^2.1.1", "strip-ansi": "^4.0.0", "text-table": "^0.2.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.0", "dist": {"shasum": "416703ef51a6e163ea2e8dc7c0810a1c396dbb58", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.0.tgz", "fileCount": 21, "unpackedSize": 37384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1uKzCRA9TVsSAnZWagAAD5oP/jGnD8vaoJU73QjWw4MU\niu6M7eKXufP482BQyGR9qVDjd+sbGXIFjx3rr919CrhKJ6dKrbcODNjGUDP1\nfrlubTSo0C+VxFPsQMuB5Cl7+KQGo8el5xaExSxG8aYl2VEjIwYbObpfQIeD\nd88PPaxar7BJjgze3JCKeyTIFHWzBFFtvuT2tQn3CbJU3RzLvdXeNiRU2qZ3\nY3MSuyUChrMZWJtrdLmTHmJgD1ZfVQ8AdAD2Aq6WpHvNX6AY8VO3xuIFkB0N\njyHGJ73G9LlAp9ehi9/H9KdA0rJ5oJoWlt/8qUOBuAto5idz+XzI9kUK4x6j\naRx7US1ovNgo17nXod0s5BJJDnrendshXwhpV4Tqxnd4KSdurbJCo3CE6+lZ\nH4PCLUt4/6XbP/mXK0cqO73UGEN8iBX2aR8N6X3Ic4HwJQ2qYdBMeJeEQdsI\nZIfr9qALMFUfT8epJ8kCG/VoyVwSfbqjFbDmJc5eYdNmQ3T156rdFToGxlzG\ni2z8kI/qu0cpJRp4iE9fUJ9JlnG/56oD4Jj2diioxRAHzCyi2BXVM17aKv8Q\nZm2lX75pbOW0weO5AFS3UKLLJSf/2YBknc4C2Tf9oYrw5MIVb9uEMFKGDL5I\nHiDRKV/Z69HgsjnRyP4koEqZBjqolNnsLdXCvgAXUvZBZg1rIjoBjdPibTAt\nAKr8\r\n=wfrx\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-Byh8J9O2yYnS3Jrvu+K4Grz5esEOkl1yQVnyGMuHdXWPDhTb4UAH1nBWFOxNfUwi2bHZzw3iilCaIBS6ASe1Fw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFzknp4yX9hkgY+jLqKYRD1vKxqVHUpczeCKPVjX/8SHAiEA6PsQjOIfgdEntqeWhfypmwERxyEcEEWgL89s6VxLcNU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.0_1524032178481_0.9666540198354825"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "cac", "version": "5.0.1", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.1", "dist": {"shasum": "7967de3cde0d1fb3ef5cf86b49f3515828edba2f", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.1.tgz", "fileCount": 21, "unpackedSize": 31799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1uSTCRA9TVsSAnZWagAAqLkP/0zRsSRtZ+EhawBvY0z9\nKOO7jYsNjbRKkWBKGc+YO05SGoy4TGBS/eCYBx5NI11HBJKczESAxSKg5SJR\n+FbvxZUIvZ2vdxOrgizCymwatJAVkbcCsdv9t8f0iOeUGgovGHLde+58kE0r\nM7jcUEP2Xz5IJEkS7xm3V+MHfjnMIRZICB+L4EbAKabMSfoJkkIDoLVTNQsp\ndfk+wtyAD5MVgYNhzo+/STehrs5d2CMpRGlII5mCmmQ0zJ1YjKjir8OH0wh4\ngfypmGt59yj4Sn+h4zpbI7Gn5GdXg8r/pLvb9zgm3ClmY12g5JPEO7L/iWO/\nj4Bl4bxEW4Yg6+K0rH5efXxNAjHKNWDTRE4Mqa89fteOe6UmbgShNlxpas+Q\nWRsg/u+jD2boCGPSobfAuhiXZuBH0KPNVM/VBkGbdt7rjpULU9bpY4dbWcd5\ns2BxUPRRqV0piXjPaNlLFtEnT95215gXVJpJOAP4q5VLdnwalVhIK6krmkug\nqP4yS1oOtymbdAUuAAhvTRgyGZcuO85PrEcjUjUzW7GpzU4qHQzcvppy5r9J\nHsPvwJzghUl81WfWv+1rIDmC0fxPxsCRn2iBDEtxrSGaypXLDlNgvo3OGIGN\nTJ9fGUWeghx067ozuk83TmPtBM58AHRkguFrGuvkaOzBbPMHC+a/kyvJC0U4\nEpF7\r\n=FeCO\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-pMy8eJGilyYYYx+mSRSXgq4Ln+Xlq0VdWcL0lAbO1Cc5YGEhVhM41uZgQS/eVaNpodcfmy3luQZ0st1tUq1vyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDilL1S+rIPw5fOPJ72WOqcwQj15yODRGJ3oG24PLpswwIgTktR+GLsMMbGtv6H+Elppqq8x1NGC0JUoVrfIjLqZmg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.1_1524032657755_0.3186123222977668"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "cac", "version": "5.0.2", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.2", "dist": {"shasum": "47adcd9166da3f34e85fcb37e8e3c3953174f8f8", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.2.tgz", "fileCount": 21, "unpackedSize": 31800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1ubqCRA9TVsSAnZWagAAA7AP/0UjDiBEgQwDEym9rz2u\naTRwBi0j+jDjMZwjw/wVkSEc6IwlKLBE7QnlEqAvkeLLbd/FOwpKglHUvVFb\n/XQBiQFjDFdxRUFKQ26typQu+AaikACS29fzONzFmSKJnet7fdx/YdcvdSlU\nO1ZCB843fMLnPXfdG2Qrn/CsZHNIEPP9E0OSQoh0VJEUbvB9n+McIrPF+w+T\ng/G3Mk979ooroFCtZ2oKSglDUe1A6MSo05X+zkNLpMxEBidLYNAyixfGA4vL\nZoNSSsD17FHUGi5et81zpAvb8PYs5F2IPJtZtjcxaxCJBzCAdKZZ92SKRXHE\nKZapt7vo0g4MXb0gFYBrXYNYL56R7EmQKqqrh7ZBbqfY7wlvXnTqjMhXFWcu\n3I8J2oqFNWSnMnUDSRpXGR159V3i63Qiu1Vt4hZLC457gQ07/WIQX1loLRj/\nND3oP5bAeHYAstwfa4zBmWoks+lzYSIEALmZBA3SYRhhcxpCm+eyY62yZ56k\nbA2gs8PvtezMXycvKrwQXp/h9e9UIh0BrNCkLoElPJBMmDsgjIaRLsONdRDW\nG2M0xsFVSsoA3NtNgS4P1d3C0o++zxB8cWqqkzs8xaqe4kda7uR+gMOeh2js\nzOW3oWJs1ZCLceJKZ1UPGircpAz+TMgWAjt8ZWdIfyFlYw7XfI4N+YlWnJRC\neGAh\r\n=m8vT\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-VcxcOT9bGbBNNv/1/TbxvVtrDEprgOH/HBKi76hjsSWOBbtDzKv+vz5zuV4XBF1G6Ar7hMlJqvkVOrBUgWzguA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNHGcwyKnDCuqA/3UacE6/hBAGWR0rl/nzSnH0UZEVKAIhAN0DHhTvY6za8kcDE3X2d7rjZFqxjL2jkb6eJcXK6BUQ"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.2_1524033258202_0.655619934238836"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "cac", "version": "5.0.3", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.3", "dist": {"shasum": "cb894149c8f961924679364b3e2871dfbd1484a1", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.3.tgz", "fileCount": 21, "unpackedSize": 31860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1ulBCRA9TVsSAnZWagAAbPQP/jNB2T2R5SDHWHHt2S1P\nepo1I9mwNHnlo35Nq2eiapMZsaD4UrYuEv/gDA8hCEu/AoDYzJBMzuvBStoZ\n6N180cFhcVeorUhvVX18EIKxT4nnnGgmxd7vcVfAyEsD63Cs2zc4RX6Odaxu\nS1QC/iTCAfe/GnK9ZcPWIgorDKD0R3A2bIg0+UrmPOZSib3cyqBimvxF/daq\nZSM0AXQwmlgbiCokk4Ds/drwEG9lhWbCxgU6KzwGnu5uyYeZ5wgAW5wn0aNW\n35mXdYr1nFh7PdxxHrgqtbeFZcPZK454VnzW5F/AFEbol8jKVWWIeUnCGm52\nC4CRL7KyjLTgc8TNJfqhyqiXGSkRaHSi+PrljXlVs7Qef34eMpqWexgvhoch\nyLPXQufUuM10QXsAZZwE8mejn+NBiUDxeOnpMdnNytEyLxBCdsJo5z93xeBJ\ntL4f46DQZ49uEPImanah8m6YqwzsxJIGOvRtJN97ZsZnrJZ0+Hd+LDA+zt81\nN0pKkJGXb0Bq/MoBlh5sEsk75QvMD18tfqtUS0KVfIuT+E8/JrsdnVejuH6/\nVp36GLqLEYKCPW0d28yfjXlaMTy0Kq8+fORmvNyFQPI763TFPiBIeNyiWYWw\nl2cJ8tYPZnGCCz3LTRqHUqa5zg9AGKDSdyq39/50iouIKfCglee4Hanjz+xd\nXsS1\r\n=FjVK\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-WeAC/FI2S5+IN9zFOyF7KrQSlCeIFU+nMlFo/ldzwFZyhbfv08KPc1fZL933KwtarNrv49E72XgZ/gXs5KvcUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID/8MEzCFy3UfdN3akC50nZOFBKfDUK92JOxGpkHi+ffAiEA3ueFpZsw6YuGXwqYwRy6Pks9GcYoJe4yAKF2psNIyV0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.3_1524033857124_0.8016573849246873"}, "_hasShrinkwrap": false}, "5.0.4": {"name": "cac", "version": "5.0.4", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.4", "dist": {"shasum": "2e9c45bc48bda9d3d9703d7317ed98d2a038bc54", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.4.tgz", "fileCount": 21, "unpackedSize": 31522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1uoeCRA9TVsSAnZWagAAoQsP/0a/S+NUsfUedW4WZmkQ\nnqPGF5TUn8tQMcgnvd39LjaGqQwZ4HqfBN/JhUHvvFAXDPK2V23isFFTcgLO\nrmZ6tmOz5Vn6efFl9WVQ1NhxRSZ0XRLPQgIl4U1JHxaueImumBPDEVkecr3t\nSl88dzfO5rrw0XWDPGks//iYHFI88+vTY9A6ziP903Sd+i1IRggCBYBabhTz\nekT67m/vdMuO8gTKOVjvybFniJt+QA+GAZBjiiTHZgFKfMjyvE9nPb/jXOC/\nBiaQfg8fwXkEaHptNkW8kGVsRMp35/Wz1J+q/XtQIX6zTtf8ZMDMSv9/FDM6\nHNiwxjLtecnyXG//sSMy2Mfu7Et991CfUtkiJC+akG8bux/WDEomdXOu6mdX\nGiV8aC7l3Mdn6IkKZEYkrXlioAJg13dVeiUcIrg43WVU9FwhX9qsSolRbQF6\nuZ7dPuL0Gd4bDG0XVLZWYgowxo+lmM609enE3y3sf4Q08R6yP6izq7O/9JkT\nfjS+vExokH0SfLxBGtM+q26QTmmkZMEu4oqHKF7xsJ8XVNu4oynGlc4YcPkF\nw4wMibgkjfSxDnpHeGlvDWUUSRjYS6qeA2l7+wYQdJ6T98ia94erB2RJwAzr\ncz/yXDM5cBptrTuWwHZ1eWtuI35si+1Ddj61i5+PLiNdSxA+6aybH4edQC/K\nM7kx\r\n=uMDD\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-FCV+a34j++LnLCgte4shTz8qNiWuFVL/J+iQr1eJ5xpQm8yyNg22pOet8vdFwpzcv0DPUOiyJbnqh0337uUEbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGccYMBRoEK/5fL8Md2hDhvGzsnLYGu4FIqp3oHz3CowAiBfxZphPQE6+DY6llHd5aoxfUS2hOEEr4AMXSweDHeIcQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.4_1524034077062_0.5856568861306928"}, "_hasShrinkwrap": false}, "5.0.5": {"name": "cac", "version": "5.0.5", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.5", "dist": {"shasum": "0acbcb78b91f68ace03d6c97c99daf6ecb8c1723", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.5.tgz", "fileCount": 21, "unpackedSize": 32048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1uzTCRA9TVsSAnZWagAAKQoP/3Djh94e+YaOlFn9LG1Z\npZTuuchO77gqxCx79mdmX7o3cHvbLk11xGZEeH2LtBcaSdNykAYUMk3JjQCA\naRHmaGGs1CL/PF3SifxTQemKT2afOEdLphvgnKhGyzGP3KmFZ+zoWUcrC8Gz\nxgDtcZDX7+Ak9bWewsg8ArQXWKRefKkxjmUjBIJxj7QFaUSkDKMh0t92bCTu\nTxZJOOjIaCTdJ5txfVKFMuC0ZnRjbQrxpHpSpib0XqTZtwSbud5qtFOZtxh7\nz8hVbk99xxsyDPbhUG/hXehIFoFYx9yLV8CTRI1MS8RxGJnGT1zAIhWvbk1Y\n+aCwY824yyEQygVdzdhrDQYMvTcNqQwczrQb+vz8njF8xNqJc5V9SpPrVuWE\nJqLoa8rqdlogw/O9oKLoxf7P3mEl9YINxJUKiOozEPZ2KfNO6BJ8NAmYP3HU\nIixqUb6+VyYVb7BdfZW+sXHKGbFNVQBHdS34s+4y8tn52QtkE1NPDLiShwhj\nSvddFOsZIlus+feR3t5OZ7X3ZgpjxFixLaamHh4uDCJRbpruCp/hr3TXg8bj\naZ3A+Yz1vUOCqL8PZR8qLtmncSK1pVZlPUUzOO1t6xD7F9WWObCCt9V3+bXI\nsbFVjxUyxODWwSs8nhpAwd4hWQEBSFxbg9FgkGY+483qzYwHpf7fkLaZ7RM0\n/STL\r\n=uwj9\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-tE63hjMkP6nGzoB5Padcj+I6Ahijkjop1SMWdskyW6ar9N0+4dMt1fBr7EYKTcz39WbQ/ttcIDCGCMnvsEPlRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1k7waRIWt5/rbzb7/R4KwANqy7Ezc1MxzdDixoU7jWAiEAnlCWwjj1U8a8TKTrcmERKpu3VB15FeqHAMt2J3N29Fw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.5_1524034770551_0.2792564746830777"}, "_hasShrinkwrap": false}, "5.0.6": {"name": "cac", "version": "5.0.6", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.6", "dist": {"shasum": "ed81ebc5a585b26639e04581455d3d1c3f4ad1b7", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.6.tgz", "fileCount": 21, "unpackedSize": 31978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1w62CRA9TVsSAnZWagAAUxIP/RZ0oUpVejYCDyO3Mm9o\ngrmLosc2ra1TJi96e5xzJcCJPBDzxdsr51equrNwTynMXECvhaZ/Cny6aFlp\nCiP4zSCYmxUCyvRk1mAvSg19pj5SaSlv9o1sxOIYwfFuTtJgv32OHgnDAzFN\n0i3FIW5/SKY+1q7inTRDe6OOTf43sXhspo4hJY6PFGAL5YGq1wEsSYL30FUL\nkl1vWDOEkUTSi5eZV6EHaP/zJW/6ybv7pOcaK8CFKpz4UAajrhMSSIAxoEil\nFOZLriTh3NKiPN7Z9nSzYNHl+sqUpJMOU8n01n9Kro84/8Ih5i3h6yS+dMUL\nd563SwrYZtWj125MfE//y6AkvUi8AXrvA80vb87StPkUsg2RxyIP5x5WtFVg\npj0rbNzPiZQ33e3KchILI5PkdUUco/bfIJEj5dcRUPvtGFQwn2ZVRter2mTr\nWt463BVD/qBTmud55YFrlgHLNnhuyiiBVUaN4K2dALjRrM4HxNQJF6hPTzOE\nwrHW54Vz9wqOq22ZO42/wUeFTi+Yt8kualg3CJi1mk4AvzfuV538J62s8ngh\n/FEwG8XMniw2kB94nUEv1BIQOWlPCQ4E1j9shxDUgqR3/wzMQjGC8tUbYl9x\nAUselUeTCctWaqTZ6Hcbe4uAHZp4lLpyujLcrT/tt6nPMxhFMOrhA+O3CiwL\n/dNx\r\n=BZsg\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-p/4BVbBM+1M6oXLmUXBfuncs6sxxVxtOoC/nZN5gkhXOz9wCSoHeEsueJh36fQARTFEcblHYBeyM7ts1lsltFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzV2MsKc4Y03UmCKJFfIgbUgL0W4pBux3pZmob3A2n4gIgWAIkFys2y2QClZ1YpkQyR733gZq8uwn7mtlFtUUr0XI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.6_1524043445598_0.9892034900042708"}, "_hasShrinkwrap": false}, "5.0.7": {"name": "cac", "version": "5.0.7", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.7", "dist": {"shasum": "9cac5ea0df9f200b6b6a70f651d856eed28f8aef", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.7.tgz", "fileCount": 21, "unpackedSize": 32012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1xNeCRA9TVsSAnZWagAA9wQP/04Ka23FCRxwNcf5ecOy\ne+t+3MYCeOPDQzNTRIKCFhW/OMJ9zUcCy9NLQpkNxAg+Y58TBw/oaBACcW4o\nndPbOQjSl8F4AL/uosK0ZTRRO5ANCxhy7ETi/4Q6AV1IV+PPq9bJAnRFfkGt\nfQ99qEP7s3GSeS5joXZhXkTbsOk0cI+mIpcbCoHL/N4GSIoA2sNn8v8/j2ag\nbNEnaXsEtbXpBWH+Wgs6OD4vu40Zfw008kx3Huy/44X0Mx2wJGBVEypP9P2F\nc1gilKGVVPjAeac7Vy0qfaiNmY06teybicVj9YTI/ribFIQkYiaxosoSO+sG\neyQ4NwfmmuE9989szH/INpJV/hQcnpCNkXHYS1EjygAKhIqnh+RpxfU7ic9M\nmF6HDHyc7H1pti1YzzCGRic1VDk7oZaOjLgETbEpGu9QAb9WwlAfR6bOfRv6\nH9LaHVSP8YUhjbPH6Y7vnhjH0tSEab+72HUV5bK6U52nuuncv1g6Nd9IxEEA\nKoqrL0AbEkBepoltzKUY5xNXGvBC1yfIfHcp5ZwIyalFpsVF5DMSb3Irjqs1\nfN+vEUhtdTYt1aQSzEXFR3KzsG7pmywaUz5n5Bb2ThR+8B6sEtZCpp0x51ZI\ngLpAok8m+Aup36/HvA7ecKUIXiEUvcFmB4nF+pEvMZNUHDC9oRYnVYZ95LUP\nyj0E\r\n=7bmX\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-ysRTjlVdozpE92M2917j0/m0ChxUgmAqeRZWarctFfp5/mXEKHKnRucIL3hIgLH/BOihT+Iolysbmla0Q2/Z3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2BXpB08N7r+O5DHRKPu4dCW5UCmXBEyBSgEy3OMHWNAiBmiJsK7rhCs0UEPnGbXtISjLmQnqFdSkBJi0fbjSxDwQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.7_1524044638260_0.011979305027409515"}, "_hasShrinkwrap": false}, "5.0.8": {"name": "cac", "version": "5.0.8", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.8", "dist": {"shasum": "32b0bdd182e915289e68d8b7ca346271c6037206", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.8.tgz", "fileCount": 18, "unpackedSize": 32024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2t71CRA9TVsSAnZWagAArowP/iE+3IaDgHO1Dgvxz+Wh\nnzMWmg952ONyhOvrrVQ51uBb+wfsoT7kYU6HRqxFZ2bl1aQG7YV7nu56pO3i\nhbNMstLrHIw0jge+TMkwvinrl72y8ya/EPSlj5mUPi2FSi4Gq3apzahgGbp8\nAOtjJlSOJgrbk9VTfN2J6fc9ngq+wQznkfWgpb8ArvxjZUIOY3qOBmIrQKMI\nSc0RZZnAKvBhMzzAUSiDPs/4jVVnmVLPBMj04BYdN7SHwIvzE8zSXr2ji5A7\nSC5dTzqnDV5BtsQuESr52DsxOlpfYuGBhDKyV4OsK/7JtfU9vLhTbFu3PlZ/\nmBdE6GAT9+WX3nXhbTLJZUqCan6aYlJyu5cbVvkMf9WePEKoi+CjJyBSuByK\nhdXKmMWH3Q/JUeEU7YI7AfZhjr/l5p0wY84qZ/IYaIC2fv1xoddORaE8rNts\nGTxdJ2HasSCbOPonP8s0upnsPgF103IyugOs5Ut6PaTz1y71Fcx3hNNgHseI\nuWBvvR20h5xrEwTEqW7VseD5FC6rc4oGHbBguigomqZCAeEwDGu73+tCPEJy\nNR9OwMsiMVZ48iP4va58u9hTOWJ1QjJGCCsgaHiuFDei4+SDYixsMOaqEEub\neiHb9czVWXJX/QjrqPcGKGe59nKR9T8Y5nV47G2aTbxJzOFVjZRG+JZDWrJ8\n6kGP\r\n=8ny8\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-IuoU+OZueeOXfVOQJ5kEc6xMbs/c7UNp/HqmOIl9HgRuxnkmeWix2UdJe6EJkIc6FDM69zHVffeeQ1M6AdLv9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAFBDGoCLYLeUiqHVaB9iOnpUa4/hGBxalYz4rWe6z5QIgdDXTMVDTyX0w6K2SoRIYMndf/L/xXkMQ2gbuLDqc3o0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.8_1524293364169_0.4060519695429665"}, "_hasShrinkwrap": false}, "5.0.9": {"name": "cac", "version": "5.0.9", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.9", "dist": {"shasum": "d6eb038606660e4ee6e739e80e398422224a3e67", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.9.tgz", "fileCount": 18, "unpackedSize": 32139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2t/9CRA9TVsSAnZWagAAs0MP/3XCsYEYhns4UzRhd2fT\nduPOw1qWUQcQuWVtrelboPMsNW31uqwkli8R15o9WcmjwaYB4M7xHrp/OK0x\n1WBon/biZvw1rbgKCN3W6VzmrSG76r5RCcsrdRivF6tipt2HEL/L/5q2OFoF\np2HoY4n8bcABL0nuUgKi7qrctCuoVLLeNXYZ/DVJJyXl0cGGbme/WIK7CfUs\nQXapjB6e8Pvd8lXQ7dTxyZDOr6Uk5BESGXWMzcuA9gGtwO3Lk95jdR6n71PN\nyCAyUa4WLtU+AP6wkLoItPF9X5bvlddWZKlbP6m+S84hYAfdB+Tvi12mk2o8\nPll1Z7q/WNWZ5bi1qmzo9M3080kQqbiXmSZAnkU8egcwJ5IV8S9AzevWT1rQ\nkMXV8BGe3VhhBLj/OXxmZwz4+HEkxawhUjXaQVGe9Nh3mMk8Qac19xddAjuG\nCAl6bhuIqKI2FDddg9N57MUbb98fDiknPbABHyqeogcbRsKOp/LQbs8/qX7U\nSlBDxnWlrqwGvEly8PQVqFPpxb1foOdJflBY915eYmcCFVyZOhBZ+/qfnCwu\nFzXq1m+s1v6cZExlF6rk0OGdE24zEB/0Pp9GShGoPFeSCKVgpUYk1EBztSPK\n+tEXA3gBtYxHL+cGqmtAl6BqqUw3pELRqc5R17ySpdhuLWQK4iXVCMXn9rtw\n4hC6\r\n=eCgj\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-EieOpvHLXqyZ1oeMJW1jQw0ySNZT1SSCd0dqGgMZkB/io9kepZRSC535bpGq/IjIa7nIhtRztGB37QP7GXH5sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD56A3PHMHwziUhf+LRhFjUhyq5ik1wo4eGnOGtyn5YegIhAPynbRWT3ITJdY/QJyQ4TZljDONzSRxU1bovLhpZWA0t"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.9_1524293628318_0.18059304298492984"}, "_hasShrinkwrap": false}, "5.0.10": {"name": "cac", "version": "5.0.10", "description": "Command-line queen.", "repository": {"url": "https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "files": ["dist", "declarations.d.ts"], "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint", "api": "typedoc src/index.ts --mode file --out docs/.vuepress/dist/api --readme none --hideGenerator", "build:docs": "vuepress build docs", "dev:docs": "vuepress dev docs", "postbuild:docs": "yarn api"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "minimost": "^1.1.0", "read-pkg-up": "^2.0.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typedoc": "^0.11.1", "typescript": "^2.8.1", "vuepress": "^0.5.1"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) egoist <<EMAIL>> (https://egoist.moe)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "cac@5.0.10", "dist": {"shasum": "311de657c839dee4da58582b816e1fe44d20e54f", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.10.tgz", "fileCount": 18, "unpackedSize": 32147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2wsZCRA9TVsSAnZWagAAF7gP/i6sW4Wru29BhmP3QIWU\nq5RKFl67x4MgR3u3yay7Xq7e0tzdsL3Qd/8O7xm151Jh8DfzDPuCjACb7SOJ\nvjXQaraGicit6g/j0RE4RmiI88ACEIBzaP+gUvJy9/sLBFN7szNmp0/HrOfe\nNnnbA4BYhRUP6IH2ujb44nuq3Rq++k4An5DSogMtkNx90aq7wu4/v4fgkchN\ngdPHzrVqfR9K/kkkPA3FzRurYtfousGxP4+N89G8wdAqXB2KIUy2EJ34DVCJ\n/ptS7Dr4vE4+mP6RX9LCCYrT3ZPMOLqFOb+Yr3ahLIlFFlib9Kddxy3M0o4C\nkdEAeNwH2Fr+4MlVwWX8yVtsI57RWsGDRtfQng5qvjiVyE4uue2Pn34NQyzj\nx++uMpUVT5HY/dE9/itGc3jRGQMji7ilucRjXPkytvtSBl6MrTw7b+BgEscz\n3OfQb4IRCAI8PrZOY4wI0UgyLh4btOBmjF/77gun10H7ssmxdg2A0vwv31wA\njAnwe/Y+j1dL/rHI5r6fazOpGlXTbTdigA58ZoO5GxyndOZNQBMFEiwmi/Pb\ni02TRL3oXR+vfL2fl7G2hianzJMnYwsKB+lvrs0pf+bcRZ08YGWGcfYOWVyF\n4z36ghVf1ntJX+dMONiXzNzFThFIiCF0AmVFJyHeZqI5XWYk4nHhs515hrrz\n4MoK\r\n=SitQ\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-Z1AlvPlpOFZVXSuSNPTbZdDd+Uq8Cpl9XQGVZm0G0hwV3DdAbRN3BjiBUw5mTLPJ9oD+U+CTB+NGZFGltJanhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAVJzioth+ubAwSLnMZI0cuDU9yp3P268SKb7xp1iJksAiEAvEDTAi8A+OMQ2TDj16S/uKYg/GMXrWCrVRwfdn3/jDY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.10_1524304664138_0.7643292300758462"}, "_hasShrinkwrap": false}, "5.0.11": {"name": "cac", "version": "5.0.11", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "joycon": "^2.1.1", "minimost": "^1.1.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typescript": "^2.8.1"}, "gitHead": "ab2553a97af2a65a2ad9aae1dffa1ea2a90ac821", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.11", "_npmVersion": "6.2.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IZRLdnOsdi8VhZqqlHsJqgwwjxBbJ6uut8D5A/kEsdMcvRfrt0KjQLUPLeAEqTX9hj9Ml/S1hf1A2LuCkKEvzg==", "shasum": "92ad452c15893313551bc46e09cf9a5acb15484f", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.11.tgz", "fileCount": 15, "unpackedSize": 32815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkjnoCRA9TVsSAnZWagAAZY4P/RoswuXLuoAXy2FctJ05\nMjQuUQjWQmIj3OZnmF9kw57sgc7ufbA9qLSwavbNqukdiMTt1r6mIfQ/UQ5l\ncNVxEO+RTONo+KgmBg2/qdc1ZDsXzrvx6XwvUJtGKr3gbb2Osq2/wOD0QYDe\nBo5+ytjoJFyzMk7oJnhWz47OQ1Od0RiPrAaqYVuySU8vL4z7arl+mAVbCtkF\ny5G0Cl02i6SQcP0NxT7EAgvgwsFrdPRn5R7N/+Re8oLvkVxJk4RubQeQcAo/\nHHfwL1sTKiGnjuwATO58blvArXmuTngeSE5K+X+pJjujFgbjoer8vUQT9X9Q\nHQc91UtXk10yDSEiwuqQ5k6WQN8Qt6tNPfBtDUJDFM70Z9hoOuruakQ5eh1+\nZbU/OTZ6EUNgXcrzG4/9NjqI7UHtoeimBgP81ZnbP32pprwycVIXVTiTSOqy\nP2Mea6Y5HE/nQCPXxaelLfZnY/TC2r+jfQ5MfRKPW3/jtUp2K+BJ5upTqEBg\nucg4hVWYpDqUYKm8x7Jj3xSiH6WaGzrUsqItyf2WuHfWnRvd6M0kKUAOvD22\nwlLCCyy2FlcpJIwPG4ZW4mylPi289FOyZ9tc57iccy7fR79fHM/jbufJMLAU\nVL27HPuFYIZgb3DTywgamwrFsXXk+I2CI+vlXLkY13gX4lX2jZn+E7+crIeI\nnYOm\r\n=ZSD3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIALzB0WhwjJkBPpkQ4kQKBLjDufwe60l6WrkGpOBeIygAiAvbCH31fWN1uV3PNDYrzpc5hWXgyREw8dw8ankgI9uAg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.11_1536309735710_0.7096133246341716"}, "_hasShrinkwrap": false}, "5.0.12": {"name": "cac", "version": "5.0.12", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "joycon": "^2.1.1", "minimost": "^1.1.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typescript": "^2.8.1"}, "gitHead": "d9692acac857e580a7b972b40ff3ee95c4c322f8", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.12", "_npmVersion": "6.2.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LHla8VnhQjsFkxA1YIvLDyji1bsZKLr7zNDFEWCbZajZQJ8TIbYs0tevMvP5ms3WkZ4/dhmbkRVrjiYhqK54Cw==", "shasum": "037cd0afae0349d1759c20b651e6ec3413998e99", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.12.tgz", "fileCount": 15, "unpackedSize": 33107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblzyTCRA9TVsSAnZWagAACSYP/1pRbKCJbtWFOJtquWmZ\nvgxTgigYuhMtKg8QInnI++4P1xz/s88Kpzv7/7BDE6glRu9zNxzmAX6MmYfr\nzX4lBvG4Ee60aTHyVtzcrZBiK2hVnoyCFxIeXI/4uQw9OnwmEOHmowI40gr8\nxbvHwIJQ1gwYutmj371KN55I8dpkn7r6Rk3yynEOtjWjBUk/ec49oepcF9l4\nJrusb29x24bMfCOm7e5HFXyCUypTaCC5QtyX0hZU80Ufia2UPcPuEktGsUGG\nVZTdGk5McThG2cOM0K4WGV7RSUd6TrV7B9u01AFMWERPmIy6VLPhG2dhK4OC\n1AYuAwOgUTfix703KdQfbbBBru+x8JTEA1qilyH8LBZs0h3RaS5q/K1ZMvuu\nRt1qDLUh34HXhvy6TNnugWox9v7yLo/G7HIV7uN2uL4V3T5io4g4PgRJxod4\nqgoV8WZ5ywzUTpPpYU3gki0NlCtAwx96w6OYVha6S/uIOqjuLDnBVtVj1DDl\nWZqaI2vpOEjO/PHUq9/j+kvh7saXY7LLuFJVTZFV+2fbL/SyPXl6jlUe5y+N\n+gEyGTJNyn7SehvZfle5ZSuGOPgiPNxogE1fxXwzIak+jD3rArPc68hn9rJe\nmjHBZRdFNdZevmGE+ZRjGHQh476u0q1yHH8lcC6/RwRlX29+pyWYtsvA800V\nk7ui\r\n=kGBy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNNtCuK6AhAKdci16ansTOnknp4hnJisueMl78brltmAIhAIs3nLzFv7wOL5stKx7lBaoniU+84STdOGzBM5y/wyGd"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.12_1536638098461_0.4671614738018832"}, "_hasShrinkwrap": false}, "5.0.13": {"name": "cac", "version": "5.0.13", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "joycon": "^2.1.1", "minimost": "^1.1.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typescript": "^2.8.1"}, "gitHead": "2db65c9330677fb92166590d6d13c75db24bf2c7", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.13", "_npmVersion": "6.4.1", "_nodeVersion": "10.7.0", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GMfSsNeIOu3lfHfaqhBsMLv6EW760w6x33H19XKlrTbAnTI5aqA0ncDUgtzddZ9eBbgy6GOrXUmVcnrP3R75sA==", "shasum": "ccbc03c2200977543031d83c1d8f4e8622ac971f", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.13.tgz", "fileCount": 15, "unpackedSize": 33178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvtdzCRA9TVsSAnZWagAAqLYQAKTrodCtWwRVuFgjwwpk\npLcgsGPe6edDJtxnaO7xDc7nmL1NkJIBzxMyTyheDj0uoAY+G8/EAw6RgHNw\ndDLW7qZW+g4Tj4uzY1oTM3zDgNd1F2WsS6WiSVlU3AqvkJ5rcNESbBvPfpb2\nojwBuvtnJcVy1kJ9cC1LZvwT1b5UuFKm6cguzKIs05iuWuBbDrPSjYr4EKAR\nkLztNuobrdlQKJes+YdKssmh4aV7/pUt8e1CfxEjfrdFCXCSQT+REBdOQ+T3\nUL7T92D0UaKDf01izXkTlz+Z0lH/kzUpWuA2dop+6cCQU7CSHHnZ23yDSm6I\nq6HA6r/+T+sjMojO/JSb3VfIGVF/wVlXXzevkES36X0nmd9cqpkg0fghM3Uf\nL29nXdS83LNyDd1FU7W4hh3vKRXmeEl0+LkMYxgjULR14ANIO6dZQwG3Zwjb\n5A4+I9iuK9XtuB2XGG+A2K7wKwWfKC0EvPZLpPkSgU7Q05tyf4N92+88p/Dx\nCcNnjV+KmSbD0+/EIse8Z9KJ+yUMt/S0fvFFlt1QdrStI5nT9sbXkgbSBPmJ\n6JeoyTOZYO+FhVrFwQvrgZdqsOQuF9ccAz5j7nAAK8MWVuuYRPBxdkMcnkn4\n+DQuIhjMOe1wqMFWS6s51uLmN7zJEfGy0qJLllHBA6RvKj4AUxvifjVnbgqc\nvxu9\r\n=OjBr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOi3oiNM/+vy5ajc92LlKlmmBDaFMx6U1yitwijbbakAIgCKZfJbzX1h4uPXs9e8plAB1gGiuiA+xh8HWcD/pKjXw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.13_1539233650609_0.29731918349425324"}, "_hasShrinkwrap": false}, "5.0.14": {"name": "cac", "version": "5.0.14", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.3.2", "joycon": "^2.1.1", "minimost": "^1.1.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/minimist": "^1.2.0", "@types/node": "^9.6.5", "@types/read-pkg-up": "^3.0.1", "@types/strip-ansi": "^3.0.0", "bili": "^3.1.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.13.0", "strip-ansi": "^4.0.0", "ts-jest": "^22.4.3", "typescript": "^2.8.1"}, "gitHead": "264bf856338f08e90d57a7b2bcefe1bb57c2a4d9", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.14", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RIjxjK8MJ5Zp1E0JUIti7lmVclYE3J69+fYtLEf1hdFs0lg+OgaO0qYQNEoZv6OQZBp1XjHEwJvZFpVgVzG3MQ==", "shasum": "2feead60699d5522cf4b3845153dcaf28d046782", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.14.tgz", "fileCount": 15, "unpackedSize": 33180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0WnFCRA9TVsSAnZWagAAnTEP/i1pWCZ0N4+0rMkEeYxe\nUrCxBmGwG3amKJrF6fzKK2dJPTxH2rfDci8nmKwSHbBxEclozw2kPYU7RKzh\nzDbSbsR0tuAc75WI9EV/37+JHtZyAmCb4ATIenIR6jL2Q9rfMZxmDwO+pcC+\n1IgS7KW8mAUVa4MmbhZQbihIpBLJjH2GEgqgHNpA2uiR0OHBLyOFPa2CEoxB\nJaClBYVQgFG4VIPO/krmXq93UxnwRnG9d6Qve/ZCX/p8sIbAMi79T0SW9j/2\nmUo6k+h/bddaHWg/Ldc6i9SGjQzVvkNcSlJ0qPZrAl/5P2T/RwQ3372pJpJS\n+0NYvBwYQ8pVzsot+tcWP7uTLSmjLZc9zcZpCwvHrGvIZd6FS1U0cWf8W9X8\nJUkPu5a7cXBx7EeH9WPXdU481HCqxJ/Ah34yYIylhoTgMR+AA0GgDNDEaaxe\nflaFhpuPkTAIMgCD8Qvl6JueJWh2WsT3pmBsaVxgc4YstNU/oFkP6ihD1EFd\n/yUepEOScSRF36yO+O3YJ08Kei2QpG46AadxouCr3fSgHzvZyIAP8/tM2LLP\nOC2Oe883ysjTtk91Nugh5fcxMZ5afnNp0nIbE3Tc6sbVfLaMxS6aJlucKBQL\nWBxMwCeD+73mMYT54+AnVPsVHiLbQf2TWlLgz4DUXmuzx9frq31/cFJ6t3Ru\nCrlS\r\n=kEhV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXBPTre3z1e0rLsD5cRaIrqpy4PB1ya2xpOCwPsAghxQIhAL3ts88/LcTWOda3D+qFDTw4ISWN23tg0M8lLid6fzFr"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.14_1540450756604_0.5278978623987534"}, "_hasShrinkwrap": false}, "5.0.15": {"name": "cac", "version": "5.0.15", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.4.1", "joycon": "^2.1.2", "minimost": "^1.2.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/node": "^9.6.5", "@types/strip-ansi": "^3.0.0", "bili": "^3.3.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.17.2", "strip-ansi": "^4.0.0", "ts-jest": "^23.10.4", "typescript": "^3.1.6"}, "gitHead": "9a4ed68b345c283b7d3256972c14dda5f43a524e", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.15", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.2", "_npmUser": {"name": "rem", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8jTquyS9whajPHyT34giTxj3dUtim/+g01/3A7nJ2YPlat9O5SjsQ0hynF3lZp0+Pe7f9dh571jyDYBM9gVFdw==", "shasum": "01fb2de24d8fc81050e4ec6ee1fe2b81003431ad", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.15.tgz", "fileCount": 15, "unpackedSize": 33148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb29hcCRA9TVsSAnZWagAAO90P/jUUf6XOLNSeL5WKmYhj\nbUdRPnUxE003JrREdc0+Io3wQDayv60ErDlA6h0wtxE1aWJ38DX5//4kwSDX\n/ozmc4NUHABFfU8nWnAcqa4RxhGcjSoh27qeacI9BRZzF37Gyvd6ppaADVQJ\nKykorChK2K/9k1T4huZA+tGMr8I2B6Lwjl4GYElnINsM8F4GKs7fvyTvZnHT\nFMl9YsoCzFVO9hUOhvJEOYR9TdiwjSyuPewgwrvKdbPMl0bFShZEIENj/p/P\nXS09itpt3FvEgzmGviXcMaXXv5X6UJdvfEzLF1SyVt3WWESguCb4BiYwtJVa\nwt4UsjpDj+bLCBiQh5PxBkVOYBv6PPh7rciHdoBOH/HBby5pFqedk8noxQUP\nsHmOxyzLlvMYRoZNBhio0eC1EQCCx5Qi/shDtXy3v+C+monnUAS5/8tR3dsz\nsNwWjENNiE+R+glXxx4YFZ953yyM+JIuVcavgVuxV2LWoquLX0FBuXtkTP5/\nK3VlUNdnmG/NDVT5ebM34LEdZwUliKYTgPVwjEIszUQBlxp4hBbQVdO/GKlo\nVQwC+J4oZFzub2UHdCWQ0liTmMOyRAcEng+R5L/4hLTkq/cQg/Npj8WmWpEY\nG2RCKhs5329Ul8zuMtBWkpBIRTXbVrw4k8z/jMPXND6g3wdyypRG7rRarp9v\nRd8L\r\n=19hV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGsLtTem+E/TOyOnK5Ty+NnRrkhx7UfShubcn2Dzm1WgAiEAs6iaV7Bbcsb6hvOYmJ+UY9g7yHVkPuEZc1peTbpv7ck="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.15_1541134427763_0.1574208926548477"}, "_hasShrinkwrap": false}, "5.0.16": {"name": "cac", "version": "5.0.16", "description": "Command-line queen.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.js", "types": "dist/index.d.ts", "keywords": ["cli", "framework", "parse", "argv", "app", "simple", "cac"], "engines": {"node": ">=6"}, "scripts": {"test": "yarn lint && yarn integration", "integration": "yarn build && jest --env node", "build": "bili", "postbuild": "rm -rf dist/__test__", "prepublishOnly": "yarn build", "toc": "markdown-toc -i README.md", "lint": "echo lint"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"chalk": "^2.4.1", "joycon": "^2.1.2", "minimost": "^1.2.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^22.2.3", "@types/node": "^9.6.5", "@types/strip-ansi": "^3.0.0", "bili": "^3.3.0", "eslint-config-rem": "^3.0.0", "execa": "^0.9.0", "jest": "^22.4.3", "markdown-toc": "^1.1.0", "rollup-plugin-typescript2": "^0.17.2", "strip-ansi": "^4.0.0", "ts-jest": "^23.10.4", "typescript": "^3.1.6"}, "gitHead": "fa6545df97c01c848b38c7c2e7944a9b96a8aa89", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@5.0.16", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QHPtQCPvzqtTkcdrRAcylHJWa48zvBdQVDdutMgIL70CHiyQ6WmKh9ZYJZXSk3lq6sE2K7y92xItXFO1Bk892A==", "shasum": "1914697f58a16e863f9bc2bd1b6111662045c147", "tarball": "https://registry.npmjs.org/cac/-/cac-5.0.16.tgz", "fileCount": 15, "unpackedSize": 33368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+WXuCRA9TVsSAnZWagAAgCQQAIpvNTAnClFVoMbYdyBO\n05Dk2IJ7bJ4JZD51jpCKFNHp9TOISEzfW68McoMvPpUKw2kQFDjTkk4vHE/+\n/qgVvobuohuIgKjm55pb+Y0d4WTR4a3BJyqit7efcAqd7X2cFNzB/SrCq7+q\n3GnYoqDdoV8h2xP9gew49ezwc5cFIu6WM7qiK2MdLt/JKZDQ98DNjAcSM6Li\n7HTLdR5yRzIfczFhkLbrvGuaMoF/7EimaewqU3xGD47GZsm3QJ7tMgA4if0E\n0XprzRy+sNCQhTMTTveNhmrddfY3prZoUUSXhJ904SB8cgl8U+0ZFVCp+58c\nYkINg6jwtJjHtS2Z7fciFeUWktQ9Ljc3mFvkC+zF8FYl0NhhCqYgQZ46wD2q\nKo1ShljsrETL5yxTlLStTa8U4ZKTlKfx2T+YVeU2LTcWAC0WlIjpFxwwXmRc\nPMMEKVz4g7QuhNWONqn/TuYFqp9q4kxMRG32SGFR/busknosLMZZWIBy24Q3\nPd1w78Ghcgi+WpbGXimmKW8f8GhBde8TCgF9xnzazRT6wIGmpGwYFC3MjeLm\nZ/Z/NFefNPw1w/GkqR6EHBPtvvTma63yD7DQjOnSjJYRsHdCS3/Xr8Qcu0F1\nNhcFJLSv+HMwIYB76OydkX9x/P+hKBaJ6NEXIi647QSSOmPi/3kUHkEeaZvN\n7q7c\r\n=luVt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBwhH2QXdEiZq0NhYR5kPUP088Cg8Cwd5aaYZ1iCwUHsAiEAr2coX9PEWY/w9KbMBxGcHte9aU3Rnj0F9L5OsrifNWg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_5.0.16_1543071213533_0.35357153368944183"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "cac", "version": "6.0.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/cac.cjs.js", "scripts": {"test": "jest", "build": "bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "rollup-plugin-typescript2": "^0.18.0", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimost": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["yarn toc", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "9cb4668ce267331afacb360dbc670a18b4cbe4b6", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-p8HnNBWeDs1FB0tMBm+5n3ciqpg7CxjjzqOE4qIbYa3EJavyVqVw7J2Nb1juK0MniAV/9U1adOJxDZfrRuT0vQ==", "shasum": "4966ed34bad624bee117387f188b1c404f3dd89c", "tarball": "https://registry.npmjs.org/cac/-/cac-6.0.0.tgz", "fileCount": 8, "unpackedSize": 30713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+rIMCRA9TVsSAnZWagAAkcsP/2Wam90tarn0uzySmu6S\nxJxT8QuyGNjWkXsZ3hPGk28HvL6DN2AoZleJVpfzZRJ0s++iwWe+LU88+rAx\n+ulbSfLMWsaNEXCfwt3YXks27hHQkD3i8La0fdkv5h4VrcDMM18jkV1MM3sO\n1eSOSSutqbBviSq74rFYlIF8Pi6ouHHoSRtA9G+hh8XCeur7TBZwxf2w/Suu\nl7FLfnTOD5n0nPU37ehPXM5GaaoxAdNGBd9RZNbPkylrq7GJkbxrk/6CeY4o\n2CvprOB5f7NDOopyKF+0D7NmCqgwl8SyDtDd93q26LEeGt1K7poO0AcdbGKZ\n/bUslZLfUbzIl4xsdkyEorcx1jE4EybM0kO2DCZNBWxNouLnmOV74i4TU1DG\nEvdxQSflLqLMTek/q3QPK2MTY7QnW4E0Q7ITuNHBW1+Ltn4t7qcZU2Q2a20f\n/wtbq3E9DS4OIscvGZEVWlYllsfarXyhyunswwnvIE+DtwN6TkNmR53QDqFE\n+gpXUWNc6fLA2kwJcKEK598p/r/Kt/OiujYBIlVk6ihZO4b+L94YelGj5+Sz\nq/C6e33/0p67sdjlWtNtCOdKMneaBh/ttNVyifwFvMfpRbOoghrVTVHwOXA8\nqugIyu8yKA1QD4f4SvLvl8a6XoOwD+Cfh2iMfVKH5IFTPeDATvF6/njx+0y3\nSYqI\r\n=Fl2C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDP9Z0JTxzDPajDdWHrr1bEQaB21qMoDJP9j/U9oHVPaAIhAJMJrDeUdpZ+ljN/pmFcA9BH721o99Vyqm3TYc9Abim+"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.0.0_1543156236223_0.22598614504613024"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "cac", "version": "6.0.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimost": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "1d668c85d8ed187caa952daa036ccbc5a004ce83", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9pYdRwP5buE/OHLYF/RUT6iLEbQsBstI36uiVPf9jfiOokH5LyT7UZxIJvzhMnhHdEe9/FlypZzh1cbQAXj20A==", "shasum": "a0de49a3d64190534fefd907d214622edff30249", "tarball": "https://registry.npmjs.org/cac/-/cac-6.0.1.tgz", "fileCount": 11, "unpackedSize": 30838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+rvCCRA9TVsSAnZWagAAVkoP/1G1oN7MXFM9XOpgzTw1\n/X9iZBHJhWLJsI91bR4k7UwArbyyjzl0eobvppF5FyTVQh1DsjE9DaYvo8tJ\nS2hTzm3THGFmj5g7iyf8Oeh9yvUA9Sjdmqs0huovH/HNYApHwhsrf+VELRHa\n+oq1RAtICZRjITKBQ3sRPQeTzerLI6/2I78mCreDAMVTazIc2Fiyye8nSrYO\nIyIxEmvxuCljycFvdGSYOEsEMmAZ05epOQujBJzCJDM1iMFt0UuvhHcjxKeV\nHlkPVylgLftFO/rTf5MOcicN2dCfl0UhmgXZ1GxDlt3rwxrFagjJn8giBUGe\n1Wg8wV1Ldr7STp7mnMZ/MBA1NxvtHRSnxMMa70xsZcFysztuk1nOQnJF9+xA\ndoJfBSuWrERUGNgDVGqQfH0eSm18MyKoiKfF0LhBhOxqKyv7k9/PRRiU3qdP\n2DgtvwvXeU0DGo1WZwmUei8QgrV4QYnVah5TXEoKaaLSZgf9v/jpSrxoWilG\nDmEVDPtAkwORr3K/oW59sxde/Ay/thuWoF+5EDlTOunE52CNWEIYX9JZDSUB\nxEneNcEFd4LdYtYxvH4Mc+L8EWMX3VcYIrE895Vntbhgbk5wP4VAPPS9B7Xc\n4/XCySiSTAwcIbmWCRr7hA9ZGxTFBOBIBDU6bu5or6Z4S9EYmZp1HelhgkaQ\nGZLm\r\n=h/E5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFd47LRKC8+ombYRt7nzTyfaVf3OonKseBNdRvvuYoVDAiAURKXpVi4paGWQJrb0S4UAdhsG98HPHfc3ZBrtXEqYAg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.0.1_1543158721661_0.554108935324729"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "cac", "version": "6.0.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimost": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "8c5ec9eda3c260c072c4d934bb2d0b9ec11517f6", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NUtaxpWj6+PLjvBgoiyaDfMtyKeaT4E2CX1DgsDEoMJzcIkX47RywJFd96P9Nr7335QvAg94/ug9RpKCwkELaQ==", "shasum": "68d10cd31f495d9b467e35ebb00bc33a5b44c260", "tarball": "https://registry.npmjs.org/cac/-/cac-6.0.2.tgz", "fileCount": 11, "unpackedSize": 31826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+sn6CRA9TVsSAnZWagAAl1gP/R3w9/0DX2fQAl40QRj+\nm1hfx3XEBL5BZ7dcR0/BvsKnpcN19ZlnuG7QJbxuTTcg1SGjD69Of+XDK2JN\nR63Dh9UhRn2NWg7hk/8koUnAc8mO38nlJ6uOXF9YXaEYafTooR6VFM56MdC8\ns/9Y8+ohX8yvbEeLIgMTP+iPOL41YK+FLNPlUJeo3CKaD6OAb3MadJJWRP91\n/m3L+7+F4Upcgokq1DXA3y0pXf7QG8HfzlKCqa3dhxuGmTdkxbjyqnW6VPF3\nT8NgbUFE/vkwCjB6FOPudjpIXH79Lt2nMW81L3Ca5KpqvI1Qy6CH+PfxwiQB\n+gfDx73VI2AMApKoY+QnKA3EuB2fSGSyGKN/DEj08F15meCjDZeAPTwEnWHH\nLP6wxcPTaSvxLIRBs7KjrKparu8w1t0D/1LhO1O/Qzyyp2weD1DCtbPmPVaM\nqrZBhbH7e6LuFGdi40MLx2XQcCzBhZe+IqASfIY9stlXMtXk+4M/O95N7dni\nrgDsbAP3vrxz1C/bAJ8HzJcxVCBVICe1PxZ90Z0DePr3GYT+U+yG35q9FuTJ\nIMGTtoGbBpOrUfW1b7OETUsLZnO2sEbJhh3+gYX7qhktf8ZPUsSRFDWuuvQ1\nMStNYUc8cqPkw3+KDJOVCaEbvGiZpaJN/XopK1m12ACLaJTivGlMIKxQLazt\nmyhj\r\n=gsxu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIm9vJoEtw90yXRknHqYI6/7wWHx8GYeg2uXGw9RUYIQIgATknvPliHzlUIUGsPqTbTIuoAUYEQ0NpkjM8bixwFnY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.0.2_1543162361981_0.597303047305012"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "cac", "version": "6.1.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimost": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "e51fe6e9a0afa053aee6ad69d5b1575745a33188", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-owFVoAiVHdz7f6HDvEzFiWeu20iQt1VqeyJUVGi8JaIoreHcqwQYpAih9KmAU6f10YgQeOBMbXXIQF5IC2oXbA==", "shasum": "ea7ad6207a46bfcdd9a7602b8cffdcd0c0164b34", "tarball": "https://registry.npmjs.org/cac/-/cac-6.1.0.tgz", "fileCount": 11, "unpackedSize": 32247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+2wyCRA9TVsSAnZWagAAqAAP/27KRpfK/qCMNdpIdExw\nckSLkEPHB8kZr6fVgxe9mdkkuQrzL48e85b/8lnwPsui3lvntEbJzZ9O7Y9n\nRa6rCjM5DWCIjZpBf8P+MK0DChpPHaTeLrmKmwpzX8l/Ap9DBqDkMubOIm6V\nDmfKrGpcunY5meZXc51PGGls2JPtMLT6APfLs1ujdazPNbMK+wN2Ubm+gChG\nNiJUGojuX5W7+KENQKOCY4sKQbIiPGvS/HTZMJ72ccRNpHBHHvhIxVqCsAD7\nWCK8UZaWvaiuz3k/Clvdz5ZPTq7TlFtpKxoEZvuAXDyxQ5/xgBlgsOrZbLxa\nemwKHsnKfYpBCtr7Bjh4rNgVbLfKow2clJ1jJ5xKmW0tYOd6DaIcOgapCPKX\njnX2g/ZuhzQrZRqtvytRyJtkGZY2OImJE7RjhNFeB1XU+emyUN3C94sAhmbw\npzGq9ErkWg+Ms6vJDh99k5htbV0j5gzvEz/CbxLa1pZdPPIZH5EYJ4mjK2GP\noJaAQz8ZemvXiLTESzj/mjftd9Zy0J146YV7cVubSSaMTY+jZpYgd4oiCJpm\njTsRUbFFRmhUHLbTBx3AaXgtqi2IQuCwtb9AFf0jwr/T5iJgnElkxnnIOHhB\nKTG2h8QJCBlousf31faOR/Wf4xNtH4EuxQlODO345ZBtjZj5RiRxZIWQJlHN\n3m90\r\n=IkLz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8MaPwobQfM1/Bb0l8blNOo3uGNKVKkzE4JJ+BGwaRzgIgf8x5S8zMF96pBaKZRFmyqx9nW6eaOLiiXVajOIrAj50="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.1.0_1543203889300_0.3987148432470333"}, "_hasShrinkwrap": false}, "6.1.1": {"name": "cac", "version": "6.1.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/minimist": "^1.2.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimist": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "b51adbe896039d9fffb2c05c333b67b52a9bc9bd", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6VGi0ip+hpvWHGmi5MuIQJTULWv7J94xU3QCjqlzwXW1P2jE1b/ezs9rFItvyeo9opPSdOHKV+b/vYJFmtuZ1Q==", "shasum": "cb5b4ed66e02a9d2f12613d9244bb33ba59da613", "tarball": "https://registry.npmjs.org/cac/-/cac-6.1.1.tgz", "fileCount": 11, "unpackedSize": 32991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+4OgCRA9TVsSAnZWagAA+RoP/1BqkQRXBu9AMlEfgqNg\nin0AWfBD/l5uNsel5wCP3wlYBKi3zLK8g8zRV5PgyrJmcgIqIHu07jgSaEUU\nUmRsQjVCC82XinAgSL+x0NQBCp+LnF/IuS45bapTSCI89chDSN+prRRkwDYb\nnfdRd9/fxGt/l0rw/xeUhq5ms/zu/Zf7EyGuB96vTII1xhmMXC7lrpbDoyn1\ntLgHgWALuiyfHGbzHu06L2OwYna7sbdS2x5H5RtNXFQHha75DR1t/9TiwdE1\nLNc2zkaigoETDuqyjYPeYfcFWSdA+llc00RogDTEcfLwZn32Jaoi8sZrt0VU\noZszU+5+rZzHtjBE+5r4AnIIpWLVEK54pGUo0oNResSUl0edHy1n+K+hshwk\nV2q3zn8XxYtlK2t6EGNrxtGgM9Y+LZeXHjlWs7uPuTlWYXZ3TopLcz7vj/hP\njM27UTITLxEmp+4khw+DN1m/C12ZlBHyCsWOEEPM9XlwEz3qaSIpgRgBsm3p\nHNKDiMnkvXHdJRlASbnUajbJC6KRuWm1pyviS+bvf2CDNuTtNRBDe3nT2jOD\nxWe1nyUwYC3ap/iiX+pko/OM6rsDD4t0zXUtZaPtz0WVuAE6rnjNjszDUVKU\n8hJUObp6r903rI4S1a0n+v6jwP51MqvKOSngMuzvusaSyZAhqzR9nIDnvCoj\nYrBC\r\n=/0mk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVeqsOIuIKAW63JDh7KuGaJ7rfG0SSPdH4x+WsA1We8AIhAIiWDv/sB7kaFfwa1FC5nBl4AU4ZOLHrdR2NLzxJ4yZQ"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.1.1_1543209887330_0.9075314944458803"}, "_hasShrinkwrap": false}, "6.1.2": {"name": "cac", "version": "6.1.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/minimist": "^1.2.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimist": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "99914b6f3882ff1a244668cf90b05c9f60f97133", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.1.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gZqhygDDR4Cz/2nssBAgaGlJcsrcj9ERaSki0PTNJub/iDT1o6GYEhC+VX6nNdou9BNNEA2D319Ch6qjJDtgPg==", "shasum": "5ad752f3053342dd4f8ee8e5fe5eba5bb34b4745", "tarball": "https://registry.npmjs.org/cac/-/cac-6.1.2.tgz", "fileCount": 11, "unpackedSize": 33022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+4fUCRA9TVsSAnZWagAApcwP/juvaYQdpf70BH9TRFc4\nd2oa/+MltghpEvxxTgO1Luic4HWbziB+7j42rl0cg1Prst2T7a98/PBn00Cg\nQV/5tolZDrdIF+SqQ+6rQ6YiiLvXeVge/UzbS3nyLPDDzNk4o79d2Im/sCDN\ns3MGcrStT/XD5UWwN82XZhPklo5eHzloAj/mR5f35lg1W2wCgj7gKF0eaKYn\nxI8pr9SCxCQ0PN3InLE8EQzoelIEJch8erGdT0LHRCKVhjWuUGShJXF1nEDf\nm1fc9ZCsoI3TN2UnIvkUUtn35fPpZmzt0LwKQZ1/5xJvqkrK692IIh5d8EfG\n83RN4YDhWL7rckDqoUmT+zupxAPpb+5EWrK+Sk3Gd60s2ndv63aQTan4qC3q\nsvGSQ+vl5NHW+Sysw6mbF9UECJkc7SDTWq2zEDgWQUE3Le7tVa9BkKTDnOXr\nNttRSS0Ltjd8aLcQgIASfxDXUeu4qvvZ5SD0Oh2bceSio63aRvmdyJet2akc\nY4chPoQ0QhQV1WMbZN7oDwYUsO490r/FTUMJfF+IcTYsudbxW9Fy8zLVXlcJ\nbjWSFwymCGfmY4fUWhPIku8QqQ6t7IwXQhCcKfrl1BmQBVB3Lxjjflo39ljN\nOkmZgWbh4PsDHZiDPqSbYyaDxSmr5ZXE7lPRC/fBTWLUPP7Ykt5rEofwOpFr\n+N20\r\n=vB3G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtV5bYkSz+Gb7qB+KOXIi6D0cYQ6cco0ohcw6afBwgHAiA++kCL1tbPEJKc5vxp9KBvbNbFeklFTbXIfiXj0HQQOg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.1.2_1543210964204_0.6329744780944409"}, "_hasShrinkwrap": false}, "6.1.3": {"name": "cac", "version": "6.1.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/minimist": "^1.2.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimist": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "13dab8ecd44a214ccbb65326c3e65e7da2c7927d", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IxFpgHi/ZNCWifRlCjM/HE+nLjGadXNNnVdG3Bf6VxcxGUM9Ofoo6eKHZ+YwkP3WrXCLofWkDINZgxFH4+ng7Q==", "shasum": "30d3de5c255231288eb00040241c600e47af2a40", "tarball": "https://registry.npmjs.org/cac/-/cac-6.1.3.tgz", "fileCount": 11, "unpackedSize": 33014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+4veCRA9TVsSAnZWagAAmjgP/0Zu2mi4sJa8NKSJlvaR\nJ1n//b+xw/fGY2k06q8Za/51gitDFA7GoV6/MOaF3vekRetMxPVslpZ9Ax4G\n9wsBQfIXHpH7ZSxZfEemBFYkaur5lCxH3v1TJ0CEH/KdJq6bgL7KuN2Ns5rp\neogham/rwE8cxQ29bOvYsW5mZK8sdwYWNlLs/3or99wbVLsG0D43brRrFWpE\n2GX5Axjr0+nuQPpqd0BCR0it6L1YOgSyMXg1+HpHyQQ1AgAyBF1f6KXngzcj\nk+oj2f0IhLyk2Fxg+GjswRBGCjgdEegg4/9BHCFMlnmbWLnMCQJEXSzdIShv\nPoueTqdqxoVjcnFEmArtYacg/FvILY/RxYUZgiK+l3/XVIn655lxdpbm1dAB\n1dcdnZAy+kscd+1xDxCHBhYzk8CJeqhINBOejsS8MysV5DDZJwgcFZHCUGYs\nesz1RUKG0s63Uz0XhhkJ+VUO3ce0z5bEEBUogE78fD83Fqwdu2lr4eqZgV3b\nEP26qo2UsKl9HLD69J6MnxKuR5ff/lo0HaLCdFcngtihaHc27L9lmYM/mvMN\ntBT1l7oIqU1JIoq0SIwbjd8JRwL29hor21cuJB8K/0R+sJd3l7BddzACbGSo\nUA2h4t+p6Vk0TqCysfZipcvPW+Yp/GSc5QoT+xsuJ2SlTZnv1XkGhMNmKSBt\nFQPW\r\n=djdL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3DpHCpfhU9QQi8sbt3tRHvErNR7NHS89qnVvHv0QljAiAX808zJturYekb2SjHOkgKryBc0D6TSIbZhwzO+8INzA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.1.3_1543211997406_0.8231042313833854"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "cac", "version": "6.2.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/minimist": "^1.2.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimist": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "50395ec44cf01f8df066e50978295fe52fc22c2c", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sPZHGmUIQHbkKYzJYW9xv2HBVoNJYP0GU9rl+h3tYc1kNushpnCcRnul78Fxyl/sg3OT3LDwIfdIgqHPTvIn+Q==", "shasum": "072bc83fee7d68371da6ced2326151b653b2437c", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.0.tgz", "fileCount": 11, "unpackedSize": 33960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+6wtCRA9TVsSAnZWagAAkAEP/31tB0NZXyCbchvQz6NE\nB9+mPQlU0WsjPe/vcY9tmbWX+dHClj6OBpkt1b92/ciu0du5S65jzN/qydfU\nY1fsyFlyCFjMJ9STt8SikmezmJYcNXAUNfAiKs6mR9NfxTRxsvHS+I2J2ZHn\ntTRA4FhKBlbaOPHqvUdTvBkhilaID1/n6T3GRxSGTKrmmrP28e/9me8jy3EZ\nsZxtePnEE2txWiD1VTy890Jc+LdsLGc9esYm9TxNgRtdL5uiZidVStXhS1EA\njV+sjTrJ91E1Bys/dJiHgmSnyT2Gb0TSDvEh1k6L657DziWe75RqXpylGxA/\nwovnTScUZJotkeObv00BmBRF9AU6vL5dEk6s5SylO6qCMVA6brLJiaQV+Fez\nmb23SgTdsFsN7IdaI7oDC+U87EfGGMB9aebsNG/Ktx3sShDdhTXTMf232Ww8\nPhGzgoX4H3KzK1kAh4s15ggufk/F1Nz5FFc7qNyhtPS+8cZIlU9NGOpNI0Qw\n0pnkVhQGJYJTDxBivrnaAg0rSzWaTzUb3Z2DrYPlNWUT3ZU0BIkvLGXVeogl\n3KxI3MBMxtdRnbWDtS5IJjavNI3fQVo88cWw+0o0rDcDynFHPh33xtEJgUwd\nIwosLUxpSFVcbTYTTCWez0udKoIrqSqrPvsi13UCcDtXa2PATDAAuFQr/tb6\nK0Rk\r\n=aXe7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5n1kGVb0MnWLPBbndTEVRTbe1qrI3ejz4g3Y6KbwEqQIgAf/8fS4m0ClD1CHz2D1SCbvllOaVna/CfJVSQJ4NAuY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.0_1543220269102_0.9591244937085805"}, "_hasShrinkwrap": false}, "6.2.1": {"name": "cac", "version": "6.2.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/minimist": "^1.2.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"minimist": "^1.2.0"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "93e2baec92b9e6ed0313c55479ed0c25cbb56fac", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-u1NyxKG3N7FvdyYxi3Unbj4PI4mjqaB4TpFhhDleuZz9fPfritkJuZeVgOdPWipIEh+DNACofwh3sqKglZpmug==", "shasum": "05a5306a1167deb95b26e6f0ce1732384f8f50a2", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.1.tgz", "fileCount": 11, "unpackedSize": 34851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+7UFCRA9TVsSAnZWagAASuYQAJbg5eIb4u9o56mAO0Z+\n2+0dg2mPq8KQScZYZFcavnDsmO9zfrmfXoH3NOPqV7Wd3WCEz+AIWgBSJMuV\nE7UHdocesdhba0yqejfSZ2/KjqJBREYDI+BO9vRP4YSaXzcytnyEDJ5APufp\nyzd/eBRd8zNOa7GONnzdQeyiv+apwiISdP3iqqAgLgcGzVGd79jccmREnIX8\nKARhVtVfkJqPJAJMWSdDNgSXfIlqwhYFr9HxgTMlY1H4VOQF4ri04KjX3rHM\nHLgh45cV2IAznfRYooDdVC1IpnJrgrz7/6m4WIL7VivLRH3mnWKO9Oa/gDva\nUC6DsN5mHPhjNWcJf8KqptKXrgqrbYQe/TbS4jG3FGKAkWzqgfxPUYPzGWf3\nBcPlKOHHY3eqz7ADQvBykR+N/KPGOwKmpADIO0zfCvBYAKkqgY+2F1TFCBrF\nFZszCfnEWPk7Lv4FACio9qnZUUI5FCSVhXjewFu2XIp1FC8DjtSKQvTbUZXF\nVGplLDcs8pD9Cjk+C8R0xp3r9zkzi5dG6f4FxWtWyOY5xJ8+6dLm+OMbQxSu\nS8BKwTwsSXkPRAB/rgLa25WiiGpCIESvrcpQ1TT0hCSMDfV62b0j3m9q/nZL\nBq3+pZMjOc+4/DguuAzM/W7rVcf8Ut3N7kNX7br3YYpGN2/vHASMHNq9++Mg\niMIb\r\n=XWVj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzxFhrwuczl/8ykyKjUxhJvAWI7L+nsbI0+hOYRrN18AIgLKubdFtRpSyKT6Ndnq9MparO5Chz1Xh+WSIrK3j484w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.1_1543222532584_0.527414476886777"}, "_hasShrinkwrap": false}, "6.2.2": {"name": "cac", "version": "6.2.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"mri": "^1.1.1"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "af8ea3cf8db901834d50115020481fdda19a70b5", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IIAbH8R6xmq31/9pcUAp60l1GIm1Dj3/7EgjmUFg9cTVZ76K5D6M6N9OnA/dyG1FOzv2O5c/1drlNrY7vaqSeA==", "shasum": "4c6bb5b5a47a5543e38899cdc40ff9a575edbf99", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.2.tgz", "fileCount": 11, "unpackedSize": 35021, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+8YDCRA9TVsSAnZWagAAfjkP/3EYs8P0nCPchH2xakqq\nspv5deH8TJa/SrkBnE9PG1yA3djJVk5VYJO1xCC8XvpvO7rDOiI0kD7cDPu+\nA9uACnUXTSm+G+Gm5lOtUU8McWaxiQ5z71aE6e5EWEdnk5UEgoKwv52LMBcD\n48MvgRSUMRzgQHmupWBQVwyod/+6wgTSqcEgPz+fxVH0ejqyuX30pX7+LcbL\nWdXXadm3sQ54LjCQ6S6bGNBm6oIHBgUcjJOmeJJwIn70KpQ6dJBb/SAbqaro\n65WU455nnS3TdVffJKxGW67r5ONyjqFiewQ7Tx+K/hvNYtDhVPuOPxwgVvdz\nkiSKUgjsmrsuqYo7+9hUvKEw920EgCBCySIBzptOzMxPN2y+QWW0a07WfyPU\nxoF8Q3R8DgA9NaplWLPwr3ygOJixZEiBhg/KHCspxb8bIGqyCg7qdigWxvgb\nhtP+9q0NDtChStLbq2TYb/xY7Mn2ELNZM+ekcap8eYGkgINP8X2UuUZxt+nV\nZ0oVeM7v2V9O+teLlRMZUue2CTaMmEczrGlE1kycGNTftKPFRi/xOovcEYIw\n/OHrRz+btAbERwKpnL1strqjdOUz0/z3m+oKJqA5CHRh+Na3x5avsYntcaMi\nj2f3/XjOJhm9L13dVtSQeZ9xxn1Cm0od0R9bpM6NmVS0eSFLZx6kdmaZslut\nSTN3\r\n=6G5Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMMm0DyL7d6zGcyNt30H1Jh6JT9HplALgHHv3n/fSh0AiEA0Z63hZjhai6s+eQL5ejZ9reTL9BPAMgvVWq3aiopl8s="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.2_1543226882455_0.19318684983391643"}, "_hasShrinkwrap": false}, "6.2.3": {"name": "cac", "version": "6.2.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"mri": "^1.1.1"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "dc3e2dacaad97ffacd70831056f87db444ab7d20", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TnyaNF3qd8sUH0Lxpbg+4xgYh+xu9jqjHRuyFWFtqJk6/B6bgyVrASc4jRaJBVRFNhfi3UusgobeWqJP9hh2kg==", "shasum": "54f32b3ff5bc443da7c0ac6f955c743a6d137695", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.3.tgz", "fileCount": 11, "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+8ccCRA9TVsSAnZWagAAfAQP/jZEuPPaOIYBnraSmfXr\nxKa+N3du/PIp8z9UYd+e+4neKuYaCaG14znMvtTRGxT2Iv8DrhYvV9EbTJtH\ntxisI/c8TIF3O8jO8ZYAqceh1QqRVy+rZicGTYA7NpN/acnWgALE2B/11Yqx\nZlbDvBod22yhXQM6AdOyDQNaR2T9JJDAuKgMsK7otphwWV13cAQcVkPg9WbJ\nmuojq2bLwP5FAKLt2ou+L1wuK4OR6L2cKhvxK/LO4b5liY33rkof9B2T4LIA\nmhD97b+nLR27l1TqfLxHja/0f7wBxwMverwj+hQgKyh2q16uOTy8sdkRDuOd\nLmU4ieLq1BUYIPjh0JSoIqz0kPxQlf3u97n2YHpySskdkJp1PUP/09SDCSyx\nmNQj04l62AnTwu/S9zgCHxkv/dk6zxP2lTBjFzg0wOG+3CZ5SgJoBpJYNcH+\nMGc52K6ADvzMDJm0VtCn8vF1ZIH4j2aLENjAF2tXFjCOLKK6p+5/V2bvqfwS\nYi/LTnu8KAZ2BMNEAlHnoyNguF29Yclwxiq5nnxvQMsEgjeoGAIzzKz0F1eH\nw6OMIarzOAQK3WS23tRoowEKAY2evgR0n7Ggm6T7gOrXWlC8Rn7IDpXhkWaU\n29xmazsd05ord4kX8ushCArP9L5/e+CEuf1SiymBLcVWYuNQgfLGCf9MWP3F\nwnaq\r\n=MWLc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSE1EfNR9OyqUR0m3xg2LotBFbPMXPnm7WLgbVL9PVGwIgJA2n7RFWHpL+DuICeLBeVDNrXThsRpUFaL5sP+DdN7Q="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.3_1543227163622_0.5891905979763856"}, "_hasShrinkwrap": false}, "6.2.4": {"name": "cac", "version": "6.2.4", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"mri": "^1.1.1"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "8ec05e93948d3d6dc17efdaf737356c1fe970849", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.4", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IJ5aFnUi2H8OYHrKAW5yrLScPWamlGwwGe//PpPNh6CQrLS6X4PXHrbhziBZx9bstoK6qz00h1ej+SDhd8J0wg==", "shasum": "0503a6e5b97a13efea2fafe709972c628fba8106", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.4.tgz", "fileCount": 11, "unpackedSize": 35671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+81QCRA9TVsSAnZWagAAzbUP/iImRWU4mjZcg/QDTe5J\nIS4vFy99NbYqbQfnsZ4f154MePwZBtoric/dc3IBlzYta2tn+aRtSN5jYotC\nemp7xcZiHrau9pSTO+OvVTlIvGYD4bkf1tvyqcRseWktqLQuK5eOd2KqsDki\n7cH1D3M1bdBbr6lirfC1964kxgKFFBei5c3bXZx+oHsv1ozzvyLQDRZgsuL0\nSsDxHditKHs+B2PiAdmvBuApWRn3gbxr0ZDQk229tmn6PadCVJrbYKZv0blw\nKT/4klbRLz3SRxh4JwwMJ/eh6P96WE5tveQ7+mb1M/EeuM0gK4awj/Gqg/0O\n8Y3+vfPw1Vqs1aoDhbg41xTYQjBkANICxqzAwKmzFZez7rT6Rv6+LQJcODa7\nGmhT1PMvb88MG6AmZOz4PSvrsdMTKkfKCCBNunfOMk5fnJ5feM8rz6IGkdWc\nmisFm4JS/ku/iKsb4JBW9/JKWqM73gnc2iPoUG8dOrtfDZd9zgPGZj/qTM0c\nT9WkiSb3PO9clA36ZNRwvwYiyo8HV7TjZL0Q5Ejh3s8lffLzYNG6vh6VLvu8\ngjXt4MUaruRgVe2oaPaoWrVWVabm9XNggWdCcoMI1tJd4dhPSz88X50Jp5l2\nM2uNrQmLJ8nv5FXqg5jwuI4SzaFrNEN1AaL1GqSui/FZUtM+LGXy9EOIa+YY\nEc9K\r\n=/4Pe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwSGopTIfYkNMCwOYBXBaSArEuqy9JG/eHXKspzB+37gIgRWXDMmdlMVU938WzoeihxmydZaTtOWJAdC6EyZOQYjI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.4_1543228752256_0.5583540805116445"}, "_hasShrinkwrap": false}, "6.2.5": {"name": "cac", "version": "6.2.5", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"mri": "^1.1.1"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "e362410d63ed413c93c2e5982b5040ef5f1bddbd", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.5", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-owdF+REI3Vg8j/i58b3wT2f0dRvIMUfCMisJnClgqZrGAMKuTfhGNyo0GUJDtv1qEc0IIuQHsWt1+5dVupHyPg==", "shasum": "a847acbd1e8a49a3d9e1aa9e0046a45aaaa98d66", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.5.tgz", "fileCount": 11, "unpackedSize": 35720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+9pACRA9TVsSAnZWagAAx/YP/i/ynWsk4LGoJrUx++3t\nDD1dimhz1z9on8m9YWDWrpm6aRYQw0s2743ZaQIQT36kHF/GKh0N7ct1OnOU\nls25PnHBIiiKJ48soQdEfuK+i1O4RXucDY/j/ZZ3kKW2nICD5jKUOLHHiRGz\nVm6tdzRyA10j6Nwy+Q3mHxchUTAvpU+/iZcEANVqN+FSMUaBqLQPrFqc587H\nNJ+MdNvHk6/Zg+RGsHMP0Nv+BbI0IX5+1cItNh51QBDEJLeddUoH+v6iwGnv\nAEDO3o8BbONhSIPDtDh9VgnPu9GV1y+7xrM1ZpZfbepJG24v+rZblr2yfumO\nWrfzDPUjIPrK05Tu5YQ2XuWOVwONUGAz+JmsGcSG6Fqadi1EvGbkqLBFwfVB\nTKDslvN30aS2nr/yXCpNACY0lOjedb95HcF3w3o28hQKLWF4PnPbiz8UEGwo\nOH4YySTFZZOjbiDTETFP8g4IoyP86X0hzhnDR5NsITcRTk9jP32IjKqJV27M\nLrwKoM/jd8bkLfplh/XSKWDwhP1gkhUHPJInxk5RQBgI4nhCC+7oHMqrfYoV\nSA4VBuqhTma7bjLSR2lPvSvQqDnCS0+K1cx0GEmnirqwyEU4MvGwCEeHgi1t\n48urnAU9g9V7Ei+OK3UeFEcvRmkTeDfK2oMPX6C6hwmMKM5nSYUBPhkGkdlA\nxyQ+\r\n=++SN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExDiPvxBYZApPalFEeTyPvii9nZl40jDLCetoYH5nziAiEAp35SBkD5McRPR8eDKO8iUuILNa1WVEJXEhLWRzOmCUQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.5_1543232063863_0.011249112232368352"}, "_hasShrinkwrap": false}, "6.2.6": {"name": "cac", "version": "6.2.6", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {"mri": "^1.1.1"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "1d8a0541ec6394510635fcda0e240085bd7e1f82", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.2.6", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-W6yDh/ZzSCjpSWSVfwPh8bD0uLeTdrI7Rf6KgYsuOH5UlYhhNJpf2seRsoGrJ9VH1PG4B/QPHxynvAhtRi1YeA==", "shasum": "bd7f64c41311b4d248c0bfaa66bfb4dfdb0b8a61", "tarball": "https://registry.npmjs.org/cac/-/cac-6.2.6.tgz", "fileCount": 13, "unpackedSize": 37618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+/4ECRA9TVsSAnZWagAAXs0P/AkwPsn7OROI2zJNpCWH\nQSsLgCa/ascOzKyVUio/73l/bajHrXv4lfp0iQITahYzygTN/WKUzrv5B68U\nZJB+OAHj/ZmYItTvw3p1HwHizTT3wcpiR4ObTEO6fWEfGxrqtJHSoBimB0Xi\nqWzE+P3DGwBuhMuL4jGgXWXzDf0WCv5UvWH1Ux+erFi21E81kHan1SXmasv/\nZJH2Onrdw/KjQIDQu3CTXG80G0MJuITnm2apFcA94aUmd+IT6CoMHPLRARC+\nhf1zEXUQ6sI+wNMrzQ+ZrWhQvaT9YXX3PEkBdgIuixyztbh8fe54A/rskOSj\nMqp44ygNfvSYYE+fwJy7GkGp7O7rfSh74y63QO1fQJRSBicqgS6v3MLZCKgL\ngc69RuX2AanLlbA+K1BAEeLphtzRlqz2W3G8s/YG+1HizXu8+viBhD0Sr20n\nICPUTkqECpP1bw4VI5FIBlSUd+artSb8IuMWbbD1Mgq9PEYZd0Mzbh0+HbF8\nprYvkZNtTaLUgQfrS+CvZZz1NxGDvpzCiRNOLN962cD9XXyFPE2koxEEuWz5\nAEG1Z7x6Sc2FvJeg9WeipJtAqw0rKYgRfqB7anSTl9Rgwn3dFpu+Tk9dOwBN\nJicb3iTLFawklSUmMcq+YqyQIsRpzy09eTuJBCofrUjsYqa0QvQFmIAYXK5a\ndVaw\r\n=N2eN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFBxSLB3HJaRaRx6i/iXNQVcRsfckivkCSJ1f+e0xMFAiAIB3xpOUlodjSOcX4kPDqY8I+7SC+ZJ+FMCzSxeKQOAQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.2.6_1543241220060_0.6767821473006701"}, "_hasShrinkwrap": false}, "6.3.0": {"name": "cac", "version": "6.3.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "686ef98a8f07cd174b13ebff4fcfa6d204929632", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5Jc8fukUK3IzPsKfhMp+C6CTxjkcd4d5251S4MRYaSOkCh1/XTYnD9m6dJfKBKubkTt40roPCfgqDIgk3Q5uEA==", "shasum": "a29d835e7b0e6659baac6f9b099e9720a124467c", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.0.tgz", "fileCount": 9, "unpackedSize": 42398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/M1qCRA9TVsSAnZWagAA3E4QAJM63x74YrI/dVkRh/k8\niv8O+/DN9JqDolxepTbTXhkTe7QXSQvUE8HKhZBLI/y425FNtPX8Lq25L2XD\n+T0nKj8PlTBuXf95/iahA3RWIk6hw45Rzh5/V2Ot8HKIaA3n29xAaaAIqRJa\nuwsidlnb+RffuZZhrpRKnIkc4kmyCT0h/x/W1o+FvwWpADC5FYOHJIN/N9T5\nhz3UhYA37dqUoPT20riPfc/YW8REEyE+THeC0hu0XyWG/Ooh3G3ZdK7+teDw\nA76WTOx33lGYIbdXRht77ZZaQnmfh+1HYsj7RYUyMKBDOGoH/ILuzZcPCYof\nOEcgPBoH1LxTgl8NVU4vB+6D+USgabyNhOjFDyNDpAjc6attuKaDahFoVI4k\njrUkaB9rqbcpMYZGOdv182faR1n5bLgOlb0GttX9hycffLaSldr6MQVeuBQ/\nB7DDGQIvxzb1iQEEG6qrIxA8B5l/C4sZsY+4VCpMBz9bHoryP7/HZvK8qHUU\n016FSGlvjVCehr25iXXqaLkymGZw3NK9wrUl3LTlVmgOd/XmGOkVFBvkEJDg\nn4XH6TCWzqHQudwD2U8036YJw3k9J+7ZT1wIiwFjRq7VbK3KOlcKfqXB6Eiy\nMQb73j3tNqKB9fK1xGX60UzTnw1llcinqBzo4vcdMZYhX4heNW2RGLNKrobR\n3A62\r\n=PrdU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0zlm+s4BMwega5Hz/ylXs9zzKjIAJpZU+qX4l9fMyOAiBXyEgZ68d+Y7EX/A4IypSEehWrY55Qw/Pjb2ivk9R4Aw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.0_1543294314332_0.8152166757792247"}, "_hasShrinkwrap": false}, "6.3.1": {"name": "cac", "version": "6.3.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "fadec6882040bb8e4f775d1e4213b2ebad61d1e7", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-D6rKsF9lpGdGpLxukObu9n6LxvfRG6Suyv5MVy2swcrFjHTxaU92xzlsRpLOTNDhQsTXtqCsPW8Y1MTTtZIWZQ==", "shasum": "19033cd6ac9a5d62749115b152eb17c4c9ffe672", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.1.tgz", "fileCount": 9, "unpackedSize": 42768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/Nb3CRA9TVsSAnZWagAAK1AQAJHAW4vwU6GpqFlMS+RK\nZw6xGeyhPFcJRxD7ZfHSSLXUiptJ1BUJqfdr4z4ucXmwjHbDkIwzwoqqMqW7\nnPe5T1isoCwYDFSjVZbu3iogwA3BFkvjJSo/80iwADaxjDmIECu5nt2c2YYZ\nQtrNgkeeUN9KZvo0Z96tUO4cXmKCc+eVRVYkPPTmWSSOI0MX9EpkKnnr2GB1\nQfE0YdHkUQSe/ibvLDLYShjWuSAsdlnEP7oXknJ1HI6Pl0PwrkEEgpWBUsqn\nBWuPST1EbQxyjU2OSY4Y68zBMw5d1egXSdNOL4frmwJf99NnUKU+Dh+C6HBq\ndoWNMK2kS7fcvUdi+uzw5J3bNv5nkx1M0ONZEGbiOU1eesHnRfZLuyte3UOp\nKIV0+ENh9bNzkcGpwCLpu3sR+FMBXIPX0+x4CI15ZVJkt0DVXdD3hJcKGMJ1\nr7Ux0J6cHHu8R8hdS70LSuH7qQaBgVpBEvD7VDGYc7tSnBDXCfxrvOXgbuwi\nVK3+NFBDlZkjcS2aP4q7DKImqVOxArZL+Kp/kQpD8hE4aUmh4bIkuDgVCSTA\ne3KbZ8ICLX/KLHundMrctFDMzXadOywlEKEAXWDVzH02fZb9xEmVKsRXaPDA\n46TuehoCJdjb3RqWBkq5hdvVIjwOMR+OQbIbAkO0PHgvQuXmqJRMc1eSHATZ\n4EXa\r\n=jdoO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgw0ByBizpnJHdoriNEL5eincCSBVtLTO10/5tf1acbAiEAuTiVku8PkiZtVX+LW46AQbE0zuJh7OSbq/fkw+pZL7w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.1_1543296758479_0.4423371531657678"}, "_hasShrinkwrap": false}, "6.3.2": {"name": "cac", "version": "6.3.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "c38e92cfd8547fd70330c33de933b364f32241e6", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wQeSOao7ysqTrKt/9TwxRH02gQnsLO21fLDyPsFoRQg9tbVX2HmUR8WdiyRsZHwkJwwFnbonRcHNK7/sPH9lOw==", "shasum": "7578e161670abac154812860b2951782c32192a7", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.2.tgz", "fileCount": 9, "unpackedSize": 43885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/O7hCRA9TVsSAnZWagAAC+kP/25yl/u4JcbsnJavvtIg\njU3R2sg2hOffUi0X/n5kSBV5t/NMW2+B0LUkNzb1pznO0r3+kR2dGazPI6hi\nWcgkUi2iBbC+DeuzW8cROqorlpHXBDRNsEb60aQad/bIhcEAZ8NsBL5Ygk3C\n5bMw0jSCX5204fjYcw61+3pkvvD0JuSvbymoDe3QCkJiKK0mFPiz7wzxVPs0\n2yPQ7XJUYd0NYENpE3NTUbWUeVojLgIACM2M8sn+2RzcNIPAbfaqUDvri+JN\nUyTaERvgmmVtz97ZKIJebVud+fXjg1Eobc24TADBEvMbZStuJuE3VRRsqmgQ\nqI+v4a5mVgyXP7hCdR8T5+w0N97geerxK8xVEYOu1lPL+Cyx2Ar85jXPqK7P\nPFOpIdjVbleUu174Iaup6v6QANbcFGWs2KZaBlbcsO3NnNx85QT5dhZKU3t9\nJeCjbB9+QkzTq0UPTD7MuE4CVlLynYG9lOE6trtq4Ned6EeB7OihXfvw+thY\nM/K00bcdNKCbs6r7C5AZd1/Elwqdy+ffhrToqgoVUaiflfNdEBZRYfyjMIOT\n1cW34plf023h1/8xkoK7XRpD+2sFzBzHMn0Pd/SURq5geZQiUPH/ZMaQXSCj\nO1HA+qnDlyzVE0gsexrBquQ4IYvSvimwx0Q9dlD8zYxmz0hEY3G6S3C2cdjf\no7HS\r\n=4ICw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKxcLVEmYtLez+w2YLkoUadtUF7sOu/6pS+Ab1xC5cxwIgHLgIMEQkGMh0A+acX8y9pxBf9dlXaSXLRjD3lJMKz1Q="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.2_1543302880554_0.7435874464615535"}, "_hasShrinkwrap": false}, "6.3.3": {"name": "cac", "version": "6.3.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "3f79727d0b4372972ccc5f395b0e2f47bd228165", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aM3mGcf95cGzLuNbCVr0OK1iOc+BTcgnL+BN11gvoaL/67tM+LV3tJZbFUOzGIXXaXShpTatG6vvs6TBhbAQrQ==", "shasum": "01e56f50068bd1be326b1612950d77d31112400d", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.3.tgz", "fileCount": 9, "unpackedSize": 44062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/WiqCRA9TVsSAnZWagAAuV0P/2gY1HN2eh+YTuevp15w\npmT4vbhFCKLynPDeyYc+RUIXI5Ba7uklcT4jLazJ9EVHavogqPMMmXry+S9U\nzgZef+faRudbaKrTihfVOOC9Xr8hC9cfyXdJd5J7eP6iD9DrDZkDEFrCRq8Q\n6d+V6RdYpiOtEfygNPcJVwfozRbbMgJJrCf7Vs8gbx7bp0ht/cCIljCjbM+J\nLeGKY/3kyhVtOIWZ15vnMJWDtwvXpUdDvi8J3RxF5fh4gYJsoLrpCaJlwA1L\n7q0WyEqBoefFx9oWb5MlhfTsMvZUm7fXKWRwKGrIAa2D+uFwxA77QQNlumq0\ntfnx8sdqddtoYt+wxvtJ9r5bkBtGZjQFtT+PrYU9rfBawsl1JdbpY7XTb2PI\nIAWQg46TLIdygEUz4I1WBCLVcM8OPprNt2C6IvQ9Lizd3xqcq7BgGr7pMgjL\nYOFsk6Qr9q7jMH+XbGOyj/WX780YFtfe2FDbIlt8ALnTR1mSJRa4PnA8rJ3P\noxCM6k0A8lIeVBi90ku4XIbLBSesPLVEWqRqSuLJPO7raR71hgVe9jQXB1RC\nTmlyitjVNcMIsfwz4aQIEIJlVHjAaOUeb3ilInOXnjcbmaqC0Dpm8hBHwJQP\n6JPXC18O8VQI3VBPatg4HDG4iI5deimHs+lBKmt5RmlrQRhfrlKMdUwnVg2g\nRfoe\r\n=CEjU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBXR9A/7Bsocd1+KpCadwqJMnATocttoyobUCxw6CvKyAiBKmqjgBcLl4THHFbxCGNudexlyYpLQJOS33NuWnQLRGw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.3_1543334057415_0.34573533928735767"}, "_hasShrinkwrap": false}, "6.3.4": {"name": "cac", "version": "6.3.4", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "1ec6d5ec5313e7dc3793f8a2a9d2195cbaf32e73", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.4", "_npmVersion": "6.4.1", "_nodeVersion": "11.2.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7UZiAMxQoZpSdzTdyPmEOnqsOX78c4CKixC6jJgIrk6sTYusl7Ly1A2u8Sfdl+qbjxHW1BDL/OitN1UGESC6BA==", "shasum": "83317bd23fd4f44da8e63bb0533610dc953d6c2e", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.4.tgz", "fileCount": 9, "unpackedSize": 44324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/mJyCRA9TVsSAnZWagAAqAIP/A5aQxD5f3pxwHBTSyTh\n6RU1cGEIXSuvSOFZAatvsrYmrWYiwET8po/8SbWIEN5GoBf+qBfJ/+O2kF8N\nt+SDDo0kzuLIfEYPprmLRFdBEfuO0MH9SXm08FRmD8lDLD9oVf1mxM4gfc4R\nDmvH+28W5DI2rXKDlq//zqb8Yx5ki79esLn7Ga15LKaKj8i+LzX5zVya7vCR\nVxwnD0IqrNmtmYrcOvEquz1ZowQ6AOz83DS2URW7bm5UIou0DUI9ostGrZsa\nAAGlRp+luT6VtHIPt2ApdrSU70J/UOFY/9AVeDLe4M4gmMXl19YGZvlMJBzo\nuaTgETxVAlBDQfhYBcxsYIPKGN5wHIO2r6TDapA2QObzZxVD8HMCCEsh5SgG\nEdEomGoyzMmHlLbXXNiZhE7wuZRbpC4xBlhOawrRIGuUNDzF/BN2fLt7A6/b\nWRvJTumCNES96owKjxQPvyQBNbCNoYm1D9k4DV11E9siVRKoYT2zOTtbcmqN\nCERG2onr5VytZNeAuHAVfw+jj0fI/BxKww1PykEi5zeBkNruDMIN0zJ1rVLH\n1gCR55XwbxP3a7kMA3Vmja2/OQoscQLKrbKw3AVhvmCIDcJv04FIpPlKFqOY\nFtL99X1TPgxToSBGQp56tMuQtT035t4ctJGco6RRujHtiNt5Z4OaOk8qBgld\nKCCh\r\n=ye8r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7wNd7YETmT/mlwPfpNcojguwfLGZUgMODBXTgPaJwnAIhAMJO3+05WoNLbJvDFtp6OyU17J6Pi0bJ9wZViQHH+1YU"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.4_1543398001682_0.5543152170672887"}, "_hasShrinkwrap": false}, "6.3.5": {"name": "cac", "version": "6.3.5", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "b1fbe181a8406ce752868823c9a8d6dbc264d7a7", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.5", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NXArStrzJwXbUFCCp3JFqTJHJEaqcDNTW0mxwS6YI/j3qNP79Jx26boNdEipY2kS6dP0zebDyTcX6HpV6Y24BA==", "shasum": "aeafabdcdf57f95bf310937f2520a2bb9c94f5c2", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.5.tgz", "fileCount": 9, "unpackedSize": 44540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAO/QCRA9TVsSAnZWagAA2CUP/ivrxwhcySzW8CNVXnt4\n+Jnto1k1F84GO/n6hFlKrz2FwG0XhOUWHoQ30iGA6cHyw0hV6F/gBhSJxZ/a\nBWEbyOOLFCSKklfk+SGWX/qgAxBuegRVRe8thCFgw03qYNygNUXLILTAW9xX\nAjX3jrEud4f8+k58c5awIENEGaB9y/lFqByvBO6IvTelzJjA5gTr7ZdGamgr\nGsFRegOnBXCcOpz4ARMdIX93MooIDPml9+SW2EHCFSPByl2O/RWtW2RKYTjL\nUnA1CSO6Ppgh3s3oJ1XdD9I3WfucgutqVxdEXcMK/VrQeoJ9h2SP3EZdUQxF\ncMJ6uZeCb0qnNmQ91reZbhnZPrv+CMrPYMn44nU+cQQVsycom9nrxTvG41IB\nggFaRACLyTeuLES5Gj9OKaIsk4tl4HANrVboo/GJHDVYYgA7zLXI/Akn/63w\neXYnvAc/4Y3yfU0nOo1SSkhINKn1L/lnTggHRv+ZG2nT5eP4MrGxeHBzvkwn\niuVORbzBPisUgQJJbnZ7YCgAhQ5LHtXUSsnZ7IkbJyHAlI7cpkK9gT07kLBT\n6o/xReELa34WbsqJGY3f2rzGvxjih9BH/EBAqTpz+r5Dd6vWazX4cptNJTtq\nV8UWN92CgGHsj7YRcL7RnLqRB9eSCgfpfPDmrC3RwD+BE844EM0R1hIOeOwv\nmx4E\r\n=/VY3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEUj5oO923uZVq4RDoJHw+9Q68XlPRpd2yzBpxDOVvvQAiEA7jLMhCZ3eV+CLJUn5ym4VOKOW98e6pEqpJb67amDJDk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.5_1543565263128_0.10674020844940912"}, "_hasShrinkwrap": false}, "6.3.6": {"name": "cac", "version": "6.3.6", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "ca1b5bb3219de6e27d183b15201d92901be55768", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.6", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5sGrDwMwigDelM7f8GY2lkQov9XWgugnuAQ8e2KjSfsI9nXwtAbgn2n7u+5lloiWOCIPRUEhU2FXi3OJ8GEbqA==", "shasum": "d846a71efef3af168f9994d2a751f0398fb41718", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.6.tgz", "fileCount": 9, "unpackedSize": 45129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAQ5BCRA9TVsSAnZWagAAlF0P/3+6N1wvKHYeChjX6Yfs\nmjRs9iiS+p2KiESaeIBSU1qpKlLV+uJRfRikhTSybtiNAMi/tQx02c2usf4x\nIrXQws13S96qlu20o0U2OohyMNk8wdSo7Sb73DMGVBBmkoEdiXXfo89qPalW\n3f2YPIma1DcpZPDLq9/0aEg9Btl2Keg6ncCB01Cc0IbkN7mVwaSy5xqEfDBE\nIX2fbQ46oQsBFcXw/Q52M8EzcuDu9HFx19nqc4C1HuUdmE0idnjQxIq6PWRe\nAf73xYn16qlS3IZAppRxIEbGwBXRLVKVGhNdfF/Z+1Nx/m0+d+zXqhssrFqk\n2mOszgZitOX1RhjOf1XTiJ1+NxaL3TQnRLYRuavwB/s01g3b4/WqfJwTMUkv\n/L4lfh/wyQQtlX8E62sYnj7cLuDl0mSUziAPJW6c75Df5ZrXyIIGp2gy8Mor\nhbQb3k1tnSyYKMk/ky/tLPnomcrx/0NJEksN2ERWfuUbS+pYYamwmfedqiqC\nnyvVyoCt0kj/yjhDYR18Z7sDMzYhyTXcRdXhJFzXaQ4EXu3bdCZ1VpCgwwLv\n76ahxVbf+pNuQwD2Fgl8D4hIwBv3RB/DSM5hiINlr9XqRhmFhzHynkiUZQ+F\n6XLi52Xf/9QFi4Po6tMwXC6odWe2q+JXjBnYRRU6V5MtqUP6JxqSazepcYoN\nwBSW\r\n=YjhC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAQeohjmlYtLa4GNhrz5vbPMqnlj/m8xSjXBjfRDS1PwIgUga2kErem8TilNg3PCcDTiWorQW1uS1L/jZlAhZwwqU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.6_1543573057256_0.076411951378202"}, "_hasShrinkwrap": false}, "6.3.7": {"name": "cac", "version": "6.3.7", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "df9669d33e8836000543168a342753dde7f74561", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.7", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cdb7zeOJ4y2ojjBicbOSn4OgwWGlnenk1Gxk9yVJwSKqxOsmbI6gKl2VFgX5KlQLNzixfe9t5FzuokpAqA/DZQ==", "shasum": "f9e4226effe87bd8139964a49fa8f7fac12e1c03", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.7.tgz", "fileCount": 9, "unpackedSize": 45688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAiR+CRA9TVsSAnZWagAAasMP/1d5NhHCgIWOqhXrtT5Z\naoJG4aTOAUFRr/Pu8zT85SQqAqqd1qPBa3TTbtP7NcCKSvHG0A4BvogglV0v\np3rAhEGUOb4FxAj7e02ZOvgJCkjztuaat5sb8dNqz+qG26Upy78a7z21oD24\nsMPoKXqCGg0IvevT8jdisxXJORLqSCRLeF8fx8/tjUI6ZwG1/UiXNidN8Upc\nv0worSdoH4OBrUX3vRncYn7wi9GtBBm1mmHLX9nCBiFJaj10Tfq8SmSHLExO\n31aQeWnGdW6n0KaVqp4Ovq904TC6CIUj4pkK+fN/8m9ygJvEN09PgvxGWeY7\nSTlP2hFAq7iugxHOaIKBYBwrTcpcSk4IVeo/Ii3FwX0FO06R9vtqwH5+Cunr\nfCvCbKWZbxe5lZOYnVeLHppdJoQ7JjfVAJEi8ntyzgm9j7PCXB3hsawxYHnM\n6j7+MS3owROw4oiJOeuSJaRgj30Kcn968AsA+e3iaLh2kBGPqPlTTUKYoPVW\nHah9YOTohrm0f9b0Jd+krEaMhYQFYZ6D/ibOyNuY3ptOiUl6Jkiy1aWqkkHS\nosAwk6dDtDyHfTbdQBbUtGsCqN9FgM2SeJaT24/Dd4p8A279G4pG09N6nB1H\ndYNePI0tAXrvZAFcmPshrV9UPaWxNVsuihovCqnjM8IuhwCNGkKePvSeXijI\nNk2b\r\n=lQKa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBqEcLZN/b/QJiXVg/bMOZoP6QvwLBD9hhZStyWEaqihAiBNnJxAeZOLoxbrx4AFel6sMSHdYrGocASwZENi802Gdw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.7_1543644285138_0.436602723161744"}, "_hasShrinkwrap": false}, "6.3.8": {"name": "cac", "version": "6.3.8", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "5dae31f0a48f87ace6017ec1607d2221f6e12029", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.8", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/Us1Vy9uE064H4T7KWairSW5Qv+CJP2wjqtKpTq+XjBoeOsxxCtImVnRupc2JOQUDd9+zyIV1Cu5ghTMvVF6KQ==", "shasum": "b998bef470a72ae485b990380ddcbc4340ebc6e6", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.8.tgz", "fileCount": 9, "unpackedSize": 45872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAiVFCRA9TVsSAnZWagAAbXwP/2KDbYf1oj0kXzj62Dh5\n91P87mSo40A/TT8yY+lazvkZTlZMInDfS3/d+1jRcnrqTEgFVX07KxfLzg9G\nHNWeIxf475BgmIHjnly64oGrmX60lYl1IJg9c8kHW72DLBZLAN/YYsUL3YTs\nba0jexFDP335RN/jf6mW7UudMhrblOO8UTImeYMX5OQQVYJ7NRt2o7NkQpaa\nFI6kPOyIX2nPwLj7l3FNqpV8qjpik0lrjv90RdGfPVM5EPoMsgJKg3dDTFuE\nE8QVdtE4NTXCxXXIfRuiFUUSBXBBsECqTlSIfXM5xuBXi1DtVwNR5aKQCpJN\nzi/sJvkppQa/BF2P1VS4P9sDPsLdObkYHIwEnuUFu8PWN3uUmI14/SpIHGMH\nkquqdrrScu31thGUGv1fuRzs+32DQy4rW60zrFgRxTt0OBvJsZ9SFALxng8O\nqvrihAUKHE6sc4hERzAwJmOLTENyiJUoyC98htNFJMtoCmieJhNDrX78jM/e\nBFMw2MwcSV7v2vxRe8hrIpzFgYAoDif9RJOHbRBTSwxvP6DYLpAkQFAhV1ni\nLtHp2sTM7xKwVb2/dWLqhpOkiE+615lIM0OmifIQZmaTpe5OdNG/UR/EdVIE\nFWYNBAKTrmoU4FjN1JD031fjVsnOGc9MpdqNtEkdUqebNlAKagmLVHAfH9/+\nx/ao\r\n=OiMU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJOXN1mjR+trLH6dG/FtuSGL8IEO+kudHUZzkKUAN0kAIhAJuRAQHq4lEbgHpHAVouQ/jfnWOeDvrioeQuecmW0keC"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.8_1543644484207_0.22702926366927412"}, "_hasShrinkwrap": false}, "6.3.9": {"name": "cac", "version": "6.3.9", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "e597874cf980a419addaee8872e670b664cc09a9", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.9", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vWuINsAe+HYqdp/adfi03pP/A0wCmbg9WmHKNBywwUTuHoFhctEGbGp/x4ecQemihUEqvWq1FB9sna7gLF3KCw==", "shasum": "0cdf3f2d3a7d3fb71ab89c476c73e472084859a2", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.9.tgz", "fileCount": 9, "unpackedSize": 45877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcA4XqCRA9TVsSAnZWagAAnp4P/iVw1f01sshG4KncRgbI\nlLLwr/RJGQKT1AXZ4ORbRtu6Mw0YaPxNS2roadCmpTA7lmTuIEc6a0143WYS\n8Uq3jNkTHbPJrS4NSWxnSTkrLIkiVoDjPEaj+x0+HWqxxK6wbkGb/kwxI73D\nUXLs6w8bEKzHqHR6Wa1hhXq03XsTNmHXBLQVTLlmf+/fwB4qTNzXLvEH60Kz\nfOKcK45THiLnwEONGNbezxAMBwURu5Imn6SkxbjEnMByiu6XI7WmO6R6GKkm\nPCB5ckUtSi6RrVoMvr9nluUj1/VG1CnPdtEf2cnQ3tW3ZC13pmcqhR8kMq0y\noZYlglxKUh8u7HQsP1pPIba98gopfCij3gOhF0bbPyUWLd65hj4qU3ZxQEPK\nL+psfmOHC7iN7t9Qz9dJjRF1La2UV9jQNHgsdz65lBR0n4ygrV0l8qJI4dRG\nN/9X5WSTxlADczqPYL6bNTj3hCn9JfDZXeAb8+2GtknXZ7XrmIZViJMcb8l+\nf2YklkjrXCSXGp96iZFdFSoCVXM1DVg+G7mflpba1dJ1iHFjzZmRob4g7Nym\nCMEkjHu5W4vNGG8wWq0DpdNQfPo9tiiKqEZg05W6c/dfA5ZkS7/PJgGhQ1/L\ntiSgqsjqiYrzu/38z5Y8AUcwRuo6s1qzwYb4c/SvcxE6NaCDYmgVJS33gaGu\nzkx1\r\n=/tEG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNUdniw0DOvsBQxOzBF/u9ym1enI2V24iY4JCMdeorZAIhANx+XEzhH5hKq9Q+O2qX6sjyK6eRPB22CuFVXz9YZfSf"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.9_1543734761887_0.5598915951112478"}, "_hasShrinkwrap": false}, "6.3.10": {"name": "cac", "version": "6.3.10", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "dependencies": {}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "78eeb317889fe62332e56540ae1039c67c313fb2", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.10", "_npmVersion": "6.4.1", "_nodeVersion": "11.4.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-67NFEmsAhMaiL2vEvt7M+0KwLmfVMGWe8JAyt66aU/EUICTugkGpr1uABLuqhNUrY7kaWH8QtPJOyJDUekYWpw==", "shasum": "8e3e53206ca26ee5ad057968fb047627e12b7d87", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.10.tgz", "fileCount": 9, "unpackedSize": 46325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDoBqCRA9TVsSAnZWagAABCwP/1apBey916RoRBWbffeL\nKk+ZClqWUkxU9PgEirgz845bttpeR75fMtj+HwXKbRaQjwIblJky75dap+l2\nog8Qj4o7/IjXMkyNqZvHjOCK7jSvnWPdZGUBSzZkGWTcmVrTcOVvQT4QbEbO\n9yNIFqV1TYp7JQo83SADBQWtJNvJ+6w6uF41Ffyq6BNUhRLTGlV8Tyw2f9jr\n0pkGspOpk+5FnMRM1aEI5ebeo+KMeGDcpx3REPAelm85XTHrJnUZezvoC71O\nxt8e1PAsQMeltl2mKgP4O6reK3wSwLAmU/6dJUQpnrUQCEK0CU9IVSqXkZuZ\n9wbPGuyd8TLHGIod2RzYTJcoOoBn+UFqCIButz8q/zzsR6Fk/6neLNqeTymd\n4n0svreR+z2VdJ2q0BJzTy8WNg6Tp5JH9w6LrzWuYZ1iR7UHU8YMmFQaoE8f\nKOc/y8pexphV1AuEaPOUrh1aWSSP9S8Wu4JP4BDYqzHqH6FyNWkeeH7ILEvJ\nGKJ/YWanc9W3zRbRKdjt+RxVoGAxrmTPXzT3BJsN3YU03DQFxvs/IMrAQAcE\nEjtv1bVfaj9Fc5mp1POdi/qTZfzCqVoJyPglYplhxP13zJbbKGTd+kNMdwEZ\nK80Cta8EkVwuxF6bCTnw60h3zdw0UFR+QF26HhqvakykQMsKghp+yIixSq1G\nobb9\r\n=Rlw3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDlFsWdmUD7ULCHdG1cWYH+V93TuCQlfz4JBUPFQG6oyAiEAzccc9TkqQM64vtAiZa4pKH5CsCDx+psXBvDg7cuy1b8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.10_1544454249321_0.5027839332613337"}, "_hasShrinkwrap": false}, "6.3.11": {"name": "cac", "version": "6.3.11", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "d8a5a7e35da8f917b4e2183bdc65a99b14b2739b", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.11", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WfCHMe7XR5sJ9kjt3aQmTr84J3hEKihALAyiLodjHnZKkoskDym3AEzhH9WRvUXj4F2f3TSq4HP7q4Px2KFaCQ==", "shasum": "492ee9256776630b10a29acc0f22c5db9d729718", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.11.tgz", "fileCount": 9, "unpackedSize": 46866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH1OMCRA9TVsSAnZWagAAR3AP/2AcF7Po6S8HNG4x/tO7\nBi9sdhozCwsSnaOqD+JgQ3oE5k1C8RN4SoVOXlmuzOIOypKMCihaNzp04sFm\nm7pEYBLxpAkuJgnPk1bwPZ8fubNzFJ6JU/qsNiVDSMqSHyjf+BrpEoz8cgp6\nO5sYa1lEMJpUhEtiU5C8R91vW3+pK32IcczSw+UQ09HRumYMm9O9UHNxfQyW\nD9vWUidZWUPQRjv704mHXJfa5uU5HM8zvc9x/cuRDQ6NG0tMZuPI7QzkXknH\n6JKB3tdb6lPt51Y2nuJcjNkKepRXTHMreNApnJSDAr1vB7jp8bA6xkqsYnsk\npufT+etERM+Vtkp4y85iYtTRD0jRWEkpuwTlevnCeoyO103oc1AhOiOldbPD\nIpFP93bS0z3JrFakPTv3x9uXh1ZC3QSV21lAMbGN1Ze/iHiOPFVW0kTsmF6P\nXXmdbV9IxP44xMVuVmlt0PfNSsV5SvE7WGr3XUdiSo007XZAlF/TZMgoqnPc\nboK78J+u5/vB/qHDLn0yIGmvdjfiArMBsxlPl8AhjEOSy4qfgGOXCKcP8BUr\nV9P9GRQf0f9PHnWXl93Yk4bFzx2UieGU/s6SVVl2gJs75FZu1Stbf/AAA2ll\n4YBPZ/t0fRZEpieAUTVFIr7/bso0cxzscX1yQVCEjFWtW56jsifH+TRshSas\nycYB\r\n=RnZm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFTY7WpjjkY5iG0xJp/4yjiSRju3EFjQRMWEWXgf7hakAiEAnYMxoNybz+5/8wElzJcSVOXByS2/1WeYOgdzGyBCaeE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.11_1545556875943_0.8513390901599656"}, "_hasShrinkwrap": false}, "6.3.12": {"name": "cac", "version": "6.3.12", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "113d78ce1adfd43150834397f0d90fadda41eab3", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.3.12", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0tJOuYFAfQc8s3G6BHSPCvSTCkcVm9etd3nSeLW55Prz3uv3I6cMli9bfkEGbVUZQ39t750rAY6nCFp3dLboxw==", "shasum": "eb45205c7d09ac7c141104e8b8d1859bd9a09d39", "tarball": "https://registry.npmjs.org/cac/-/cac-6.3.12.tgz", "fileCount": 9, "unpackedSize": 47374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIjJSCRA9TVsSAnZWagAAEgsP/0N7Ao8w3njXtDvnvv0c\nRyF7SLH3wx8vlvjByub0Gk3Wd/ekOyydeXU4Uj4XzsKOmW+3ipSbZwe6Dq1R\nG53e9S26OhhiGHkjTWeieLwmfSTv4mi3y/r6rfY2iYISATAOD91k8ad1rtTw\n55v1tmr5wM2n0XLQfictrGArWDxqwc95Fg3J13t6x9jm8MuZxyuebZrwUnY8\nywb5UaxlyeNl4vSrY6nThYk8WwkWPweIhA9gMpGsurNFlOfivOWiFb9+ceSr\nZAZcPw6nE7fltmirW04JJUBnfjMf8qRsEF/RNeiA/TIQtOYc787lAF6x6OnV\nL1VM5/YZ1KaBiKMJXyg1V6fjp973qPcHTJPgus9Z89dDNbhc0JflzJTEEDJ7\nT+NjkwJGwIU1txuPAG59TOvOcoXFScAUIiJ9IfLyEKeaeZeoKFD3D1PdZ1qg\nJTaann5cVGq4ns5BeTkNx4i96WgGpLjpbQoq/SANUK0SHE8N16/FFp8K9wbi\nKfmeWtUkQPDQGKLNqzmzom/4HhfibAN+Ob2iCTLQ+zDYGEaAlBdFKEGvnyNU\nF1KnAVG2KGlA7fGNutYtDtou3vlT0kWCfk038IMTbYRcjuuKL2DpuUpuZg3K\n4M9QvoIiLJrPabzv5ymZXDb+jMeUhZsfzElAF6XK1via14Uvik36f1r4Syof\nKEt2\r\n=rKvW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzOnIxtVq4yzuQqIP0CK9i8NzNygRTM5Pvs/UwP8QKpAiEA6nMiIF63cg4VuoV678Ex8FWj8qzbEH3EZULagukl4Rc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.3.12_1545744977925_0.01710444607242345"}, "_hasShrinkwrap": false}, "6.4.0": {"name": "cac", "version": "6.4.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "96acbc7d34565e7af4004ae15e5e3229bf8bddc4", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.4.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.6.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Pvz61H9E+SyAklec5AgTQfLQJGmayaLSJM01WotbUGvjVz/F9EhZYKB78Dcu8aBoNEgjIQ+12+O5ikOzueIY6g==", "shasum": "cf23dff1ac1492ec7401f7a64de66a705b2f2bf5", "tarball": "https://registry.npmjs.org/cac/-/cac-6.4.0.tgz", "fileCount": 9, "unpackedSize": 48682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNwbwCRA9TVsSAnZWagAAsxQP+wcR8y+y0vTV9e/+oUBl\nNkYBQ92mrI6HwstrDHqoAX2cQaXNi+o7m9qVOBzt6oLQlnyD520+53B4Y8sy\nT27f3XXcMV7qEdSadHC6SdzDZIGpDK1DnyGcQR+6vhUj+xKU77A+TfWRe+mB\nuNAnjPKYBvaJrvbT2MBiNf3+wAFBCIKn7uDEvNPBqHa2c2bleliMbXrMo2nm\n4y67rCgtdoVb0uoi3v/MUEnUiVow8I/9oFMzadubnb17oDHnoKleLO6OTKLb\nuB79lmMzSZNTFUU93u7YBqn9T0zjH7mTyMi42arDmfhi+XgHDAT1qW0b77JW\njNqZKAp/wCd8EFjzPv4elkjbN/0JSwpdZrUqsgp/DU6xQ9tQXu9NB0eiZum4\n/+rwmzEJmmsQUsPDdqmL5Di3ml/xP5cMpgQTMppai7jqZuJnvQjdvuZsIvHT\n3hDUMbwVTFMPRECOcVYGz4z5AG0vO6N54nqjEadrzBTLTNTi51nQp0LUb43L\nnp3uEJTDNyf+iQ7pPETbT074nz3FrZUJVNQlkPg0dsFUJ+Ff/FbhmNAbjkaO\ncJf3qGkO0iocErIQ1lCdgiKfTrU/l7TUdwYtsBYrcqptyHqW6Ca6JYRljbKj\ncPfK4V+8PhgXEAtEgwnpxx2Gc1gBwhPe4WqVOcZKKP8GJnVeZI/+I389Odh1\nc+ks\r\n=6Q73\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCphzZ+5suD8azyJ/S+k7OJGOCSZ/WMTIQJbIrWbK9x+gIgbA3oKuFYXMglouaJTvijDkcHVXkt9V9IrkFP+T6kDtw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.4.0_1547110128247_0.6916168398316982"}, "_hasShrinkwrap": false}, "6.4.1": {"name": "cac", "version": "6.4.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "b97708d027764121dab212a1ee1bd442b3e72aa7", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.4.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.7.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8QS/LR8V5gbQGkACiFtkdLKUHuEHJ2SMTWdkJNeVZLPApJKZ+opExEQu41YHHDnb4EXvhkVK879Rt76zV6U7Ng==", "shasum": "2c3d08f2d04b9ca71398b27309026a96918cea76", "tarball": "https://registry.npmjs.org/cac/-/cac-6.4.1.tgz", "fileCount": 9, "unpackedSize": 48820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTFuwCRA9TVsSAnZWagAAdcEP/34fQg4IzHcobt3LGayj\nyJ0hPM/v6a9rOSzfy6KUAEaUFEaAmpMKI4AbjW+3fa1VrLB2L4WovAovBfXV\ni+NJnKyFVCKWBAsmnmH8BhEG3T8BM8z5w4mPwUX0Y+zqGet4ZR7EnCvSai/B\nGfpcugMK+rhTKcFAS5WZx+BwEoTKgucU6YSUTbOQejFP1gtthWLLLyJvQN1Y\njKZvSeeMzvlQ2Jl5DaTvjOhc4edQyeMqwQVizQI0xZvKfPC+1xNIr0PxJN0S\nmbgtspTjtmu8fTmpJZvRWODbd9TSQ7Uc/oVuzgwuuLJyFYM6nGwLj2RqEkrs\nD7oVkxnMgZzodWaqAJ/xTO0i0LSmoXXmOohcnToNpioGCRXr2dAqCpV2W0iC\ngp2MBipK6b/wY3VV1fRjgweRKfp9uEzBEaLJ+Cx4vtbB+9UztAEFzgpY116m\nay3N1EzLypZC1MiTGjSH8R03PxElQahVfJU5DDUDGsZStRLw7QkVYCJoWXWs\nPr54tI8H0g5aCVHMwT4EDpVSSaaELpy7mMTjzkIlE/RlFyEaW3Jfo1o/FcCE\ndACchfoLGw6b2vc5KeE5c1b5GfWgnN+j4RBfts1U7d/sEGkJIf+ReUVGAxR5\nN7zuFg0ueXwfGd+mPbEgjHQntA6RPn+096K8ZtAhrJ+/D0b3oxARy/sq/sjE\nPvcm\r\n=t4Dx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG7P+VpF3IEbkGzXwLUWnn34h37cXtgVa0ewWDQigjqzAiEA4YthH/uav7RhsZTwy7V7aBpTnsNeCnBuxNrPTdKxsvM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.4.1_1548508080060_0.36462482358780757"}, "_hasShrinkwrap": false}, "6.4.2": {"name": "cac", "version": "6.4.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "d03bb94e46d1aee1aceac1a0e22e70161d5ee6ef", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.4.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.7.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HM7kl/Izk6pb2HD7z66LVNU52j0xWpBXg8J2MLnOdRMUo2wsrcxu3yRnubnDe/cT0btDzSOl3FPhze09uaT1Vw==", "shasum": "6b2a2ab12619b778b3acc19f97e5298761d9c3de", "tarball": "https://registry.npmjs.org/cac/-/cac-6.4.2.tgz", "fileCount": 9, "unpackedSize": 48821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTGBOCRA9TVsSAnZWagAAgG8P/iMB9YsY3Gi55o7bDzFD\n71/E1nTaaPhd4QfHJXfYveV7PMl4rfS3JfX8kIP8Dzh3yFby+Bj1TJeA8o4J\nhFzXtuCE9j3Isy7D863/tXxjODDi+1N0EDIOP/3WUtGrrVfYGQWEXJZT6doo\n/UZa/Ns7AU4E2aFVipDRqpFPhpuMTJpvt0/0EM+ar2EC0OlPjW/klK6azklg\nnaAKq7RU9rberEifYTzLUEgNZ2F+Zl4MxxqQgVTKvB0J4C4vCP3SzIvqxU6X\nZ9G+IDTSz+ZurEYsdc/oOhzkgPVdDyUWgZFgpVrRkZWQshr/FGuVOgnzeJEA\nnZYIcMYL93pNkKGin0r3S6F/38YzPIBGMcgFj0UKo/4bM0hQ7ZYNwd9C17sN\nBgLY40JiFd0+bhjKZ+zbE5tY2CiqDwnGU6waCw7+7eQGV57/9UfGUiNNOHsl\n6N8IN1VnUWwZj6pPRr6las9XVhsSEKjCZ5eDVrLNeLZiw88FzgI9sW3vuTFc\nn4Xj8zivr2Yzn7RX4mB0rbIm3cqV7rGaM/Ci4a7irHmLtRt0kdswuB0C8ddd\nyNgYqQO9TjHotMnrqQ1UwUo4y+yOlROghJlKv8+/ZKftQnMrnvJLksGu1VHE\nYhHRMTEm9iGOSNk0lKEHUQMQ/BH1DhTGbmUxlymd415uBRoU5g5Ae8YFaxl7\nA6gD\r\n=e3bo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3QLX6zBtbDwXBTwiVgFLEN+rIXosNuxcGC0viYJs+cAIgf11KVQ23bV4Y4NPMSJqOGkcgN9cV6A2X5sZC985TXk0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.4.2_1548509261991_0.7119553988920353"}, "_hasShrinkwrap": false}, "6.4.3": {"name": "cac", "version": "6.4.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc && bili", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "bili": "^3.4.2", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "31ff4b3b1caa18b99a3360c8e84c317f3d68e183", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.4.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.13.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vnSAe5+uEWFnCre3NkjYJS/Gq6PjZLXw9iErjtPH/2pBr3Ieyvh751zpA33eNBhyX4gDjlsaJBQLn70lawytZQ==", "shasum": "903bd577194912781bdeec039b1b10a30a6c1fcd", "tarball": "https://registry.npmjs.org/cac/-/cac-6.4.3.tgz", "fileCount": 9, "unpackedSize": 48982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrEJPCRA9TVsSAnZWagAAQpMP/3yhxzsf6e8UE3lY5WOb\nKxavI0Bd47FOgPlJOdvsAAelXzcMsuOC0JqGIW2OyYzC3DOgCDPIZa8HKp1F\nJ908fIVdnwFcpU8QCMMw4zdBQqfgxFmzVMhRa65wbNfNZYOtd+KPsGfIZHfM\nwJ6599OxlB7Sslm9VbIAmmohzegfvZgS04ZsNlYGN6od7HY668/wj+f4/VPe\nk9AiXUuKzPFTB2iDT0oTAd490P8OMO+pb380HffpFcMjrbFGlQLbiOumf4gT\nR5JRv/3e5022lz9gjcPwqWjG8WwK9HOwUkH7s/aie44wVm6ezsfij65FFXwf\nHm7JdEbUq9MCYSxi5xZBtD5NVEavqXM1uhxOR/GnkWbDE0uBTjxW522DxMFJ\nze4ILFxBF1Gv5B8UKOuXz/1mxNwSLxrzJIYgX/Xq+Fc5IuczgfTwgz+2dgHu\npKeukB2PMPirjKS8KWZqwLaxDpyMGvu4KrIol74WtIPGrLSJJni3lAt+tKIH\nylOKHCGNyqMGC/gOBwOFwROnYnEXpjBhb7mSa2q7vISvExj/wAVDknVYnBcl\nW28R8/U2Z9vvi7GN0gbw+05uUGob8mlLAtDZAaW1kSCxN8fcr3SZFOP3lY60\nXGp8dYEwCQYK+S0Ig/VvTcfnwD4nWPFvveC3EvpPY/kmvBPHKL6yV/dd1MS+\n+JU5\r\n=ThCR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICQFZEyBHI80F0nxWhHKbibeWej6+9IWj+e1HElbq9jLAiEAj/uiLIpB4UDqVLEbYpEexGhKYGiEm5esfvgGfUfQpf4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.4.3_1554793038282_0.7685628588463147"}, "_hasShrinkwrap": false}, "6.5.0": {"name": "cac", "version": "6.5.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "70bc5331842598cb793d496fccc7a43ac017b246", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.14.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1pARVB6Awkfi7fjam0FOsztjiwczXa5pPELjqq97FgjP4QRNqLNfr5bcWcFhvOHwxX3efCjo98p4zDjdQwcYfg==", "shasum": "c9b0a09755b6621efaa87cf4d53bf41b4b5aaf88", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.0.tgz", "fileCount": 10, "unpackedSize": 46748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcszlNCRA9TVsSAnZWagAArr4P/0a09EklzNt6X6mxDFVp\n4DIaN0MkjaXvM/4dvVTmdqav/kxuxszs5B7VreQ0thVoDKUGwi46TFn3YO5L\nFmFAGB9PbFojRC2bfm11GWa/bCYddXU9MvWMY+/tFjTHRTJGw2ukBZ+95cNm\nKZly309BOAZGRL0pRJXIqGYOmZ6CPvZ7YFZ/f+uOMfNv5tG5OwPgwP0TRLln\nAZUFNRzgWcamxKRYyzmuf9cjVkItwgeelprW/jTZ9kWQmLtQDUZlvuy6cQ1T\nALuRbaq/O+tEt+ADsT26GeoFbkNkqMVfC0BXbBu+/WCptfGWVVjUhdElNbbn\nci6Kj53hscl33jsaoJUJMCRkPishmYOkMBhbN6y+BjSeMKpzp4j/AXoNRCEf\nVzFmxW9TGXtG4xnocSxbZ/GIADioipFJmw5eq3uLz8RBiE1ie7Ano0NN7VjA\nQAK+L3pmsrXZLSiuxmjFfLmhdtC6tebbbMd540stxcDycpYCweAsubRe4b3T\n3SmHUs9eUE1VpoX92Lb7eC5gxWEc/qaDFU+mcd/MI2ziUujUbiKYvnkIg73N\npx8hBQ1yXFneWvW5R8y+Ci6xFEySnDoEQ5qqhssmSNQb3acqQK4RuN1yXX3P\ntC94W8srAI/QMiCFpb5PHkQIK5Zojuz3TaQ7ZOjb0jqO8Qv4gOjZ+SAQrIhe\nNi+E\r\n=zSyv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBi5UuPHOLHFMMyeHjFz08Mjbgtu6jbu9qcN/4qocoUOAiB3ktkFsYbTJUbiLKuD/t7+pTVUrKlEF6eT/NxOh8IulQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.0_1555249484576_0.437966552019301"}, "_hasShrinkwrap": false}, "6.5.1": {"name": "cac", "version": "6.5.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "3897a5235e2eb68d0441ca15dac4fbfcbf8b0167", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.14.0", "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mqKv0Ub/nXSoqlACRvrso3k7+nCdzUNimdh+3l0ywDNt27d/x5s/zEg2Ubf1MrsV5Q/DncxtXhNJLrJN6zq1jQ==", "shasum": "c5685ebc20a8448558191d70a2a76636640308a0", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.1.tgz", "fileCount": 11, "unpackedSize": 81801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcszlbCRA9TVsSAnZWagAAXnQP/RV8SG4iAUcFIBlsSNZn\nLIyS1Aa3GTfJKJ1gOHTYNPI7szJut7UO9h2bVvQGUpcGv48ukivjjf2KVDdp\nNwgeMppEbyaTEfGPyONNUWqYNM9lovdWhHEE+0oQgal+91dC1DsBFfWNCxlM\ncSCwxs4YSm98PR/935bxNa56Q1VGtFd5z/LBsX+Qz5tL6iZC9+Foj2gMSncl\nVIn2kj7XbBbRi+MkM8ooroy9nIRSyjif2Y4iiI2X7KWcH/TcfHwVORf8NPct\nbG5kuty6/yeR9OXLYFoYT916Z+mhgRtALV2wAXEKs7LkhGFVeozTJujGqAix\nLsvKOcW5yvO74+bZyjc8bIDbnnbyCx3a9xcRBOJxrNKbWwPpWa0p1Ol3vX1Z\nqhgslMT/yASlS3xNoHpyaCuKUwq92XnQueGBttJL9f/6OixN0VGgOo+fZvne\nze3GX36upYZLqCKLEMvEBBG9WOoPaMF2jXQPhn9KRNybyEid1r667DyfmlnK\npzjt9Gyl8MUtcJeSpVaQAqUCIYYEwkWmHoddsgMiI2W5PTqL+tr65jI4K8ee\n4knOBqo6HGTqqyRlHdCr97usP3lMF+RjPZnMHw6wiwA1zoYU20yF4nz9HYkw\nuABHW6313fv0irN76ZMYTD6XWlxtX8YrVZWTeBSP4x0vGa8tVre3HDrAXprR\nnwY3\r\n=TaCg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpvIkP+f5Rx1RxW/YPnC+8nemiy+1GN1I0ztTLrpprhQIgTvfmpWFfSSc4CjfOaRDRpURoagux1Wf1tG53+d4yRLU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.1_1555249498460_0.9053979405089954"}, "_hasShrinkwrap": false}, "6.5.2": {"name": "cac", "version": "6.5.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "9ec373f02ebf81da65a6e003f72fca4b7de66dac", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.2", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-8JdiD9/ZLsG418j/chyZQ3VWuhFELSGlH4EUxzNKgIH8wK8dO0j5Pqu6Pk7B/RP3kX9aasyQhPrrUjYO5e0w7w==", "shasum": "92ef1490b9ffde5f0be7eeadec5ea926f0e78ef6", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.2.tgz", "fileCount": 11, "unpackedSize": 69317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc99rJCRA9TVsSAnZWagAAGNkP/20DXduoe2bKJuJ0g9D6\n7cUbf5srHw3OSLX6tqvqRDQS5g3PT3kYh5ADUhjSnNymvGJrDBOxMDg5C2LH\nEZ89TFiAUKvHwBAJHhX4TvD2REn4AtbMqiBRxGUeNCYYOnLlqdqa54YRhZx1\nXYUMaT14O8NazsAMeMeZ6kQzVT5rcEAvDP5ebUnR3kt9OuUWUCtcqAEshzQG\nP/bB6wbrXEq0YLnUN4cklmq/qu4FNqKf7D7ZSktyuWuWUIrdOL9oRv/+VH4Q\nIzaAaU763V6QWawPGSeNiRl3KU+C0z2+Bv8F6Yss6pJazYMK/A5zKmjYBuW6\nxlPq+eNC1w/9vtPoqVGslp2Vii2U46Y5BrBOsxrbeLtc8Y9NjAF0oDl8Egu1\nMG3ai0qpXZO1hBofDkc7snF7QXcgyqu4t9H7aTdB2BP3Zucm94DTKSHED0JM\nJvfQAvq+l6ohS9vvGo/98JcVm+b9UJxwuHNx9U/4MwXFjIdnLkwqt6Q97b9R\ncUPOu9xPPrjc7J7RfUvrBiOQ/qIWkdMf4fcOG8wOx9R3aXCHYB4WiQlf9MQ4\n9Xh3YxnmhytM8qkqQzuHvN4LpoKLtb9D0BptufB6J+ELYDQeJLsByr2PCyls\nQgq0IxeveSJ2DNiX9hVh4w4T226063t46o4DHtD2Efe4J89PKcgIBezECQX8\nC45T\r\n=jJm8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBfSU9BQk06bnNmLCJWplgDRTgzmCqBZ/io5YkgTjwP7AiEAvP8y5qTKWhDk68d0HW5E7rq1tO3jYAezF89F++yH/mE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.2_1559747272364_0.3174370510234028"}, "_hasShrinkwrap": false}, "6.5.3": {"name": "cac", "version": "6.5.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "1042c34be2c642eff80d11cc885fd97c4ee1164d", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.3", "_nodeVersion": "12.10.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-wZfzSWVXuue1H3J7TDNjbzg4KTqPXCmh7F3QIzEYXfnhMCcOUrx99M7rpO2UDVJA9dqv3butGj2nHvCV47CmPg==", "shasum": "dcd8b7496b563b87e8a1fc69e3118bdf29c781f2", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.3.tgz", "fileCount": 11, "unpackedSize": 69391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdd6AYCRA9TVsSAnZWagAAFPUP/0lfxdsV704S2mrqBedS\nB7v1uwJsrTkbGfUkwk2RhBury97NDAWWr+rWKCOOdp7OJPU+D1jQ6n+jgW8k\n6L7gMudOXS7etz6VL4ladFAL1Bc7O9mEAyH94DOk+XgUc+Tj31B4A0cVzcpe\nim9Oo1t/pt7oOsnntY91tx6jWcw8HEiJQr94qBvcv5pUwwK6s+2q1TvYK5//\n/oQLfxZTTSi3dM3u8RMz9GhPwyc3lFFkaU9URS2XVE0PQl3ss62JEApWYvqd\n+ghopCC3v58zfQBBb0sYyz/8fJlmCweEIPdHuLEVgKHwu7NGUhTmyuQFIqwf\nAO1mK3SculkCQs7ghiTC2YDRH7jtdqFAX092v+XN0Aq/8go21+GkCHi+R4fU\nQj8sKG0fSTTip4p/yzL2+jOkeUHn3c6fPM430Sio995vZG7TNfBzJYE6prMG\nea9b5fyXzfnu49kC6QtW6E5EJI4UyfitK1va+BtoQs7LVUauXL1m2yzJZ1/5\nWHtcvt9Ln7mgyWfsI1ypj+4DP9APqeTyVkH0eNDx53XpKktUWfONnV3L+MmT\n/j7igKuu3Rr6agPlmQFSlAqBErGzXelGVTpaZ8JRMwIRhq8/eSTDpbapu5xT\nwf+tohkuBO5Jp2BxGHkr3IYBWQITcZ8tLEA/Sc26EF3eFZM2RXSJR8z1uv7o\nYe9D\r\n=4Pbw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPBfF1o2cEYiaCavdn8FsnVpJ5WwEz71X+dIgduENT6AiBM3z+FrkX6yqtKCZ/mu/BNgw5L11QZQXN5y22g8K8BbA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.3_1568120855232_0.6239104755960321"}, "_hasShrinkwrap": false}, "6.5.4": {"name": "cac", "version": "6.5.4", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^23.6.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "64e22f179202bfab42cb31fc42312ed243167e5d", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.4", "_nodeVersion": "13.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-XN09SSFwQr64fq3MjxrgUmBensNAhQBC3iOW6BiqgHVxwWe1mYO/lCbIQAFqyd03F6/HVzUn6j5iUvT5YFjB1w==", "shasum": "f59a8363b219dbdcacf9a971a1cc58b18ff43d29", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.4.tgz", "fileCount": 11, "unpackedSize": 70126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHG6YCRA9TVsSAnZWagAA0ksP/2KSgksfyriuZU1vAHS7\nV4CD8b3V553i1Unnfkwhuz8JGsidkxwuzhfAoTBZQUMFcbtIqIdfloP2CrrT\ns4dvNTLNiWH1GXi1dmFR981VllrUlYp7LND8jgOME/jm1dVLYqSEWzkq7fpp\nUQgDTneIWlhfB1lwMCcBjqDo7dDPvcsiFL45I+yLzf151IHypSfcDFDRz8is\nAYj1f9ywQkW9nxDCMsCwDHJpW82IAMmZojhnq4jwV3oDK0booTgTAZQB9H46\n7A3D+E/Qf8zBA7/bp0DTeOI74RBRgvPh4zy3aqLk+dU3/bcnG6V6+L4S/rj+\nXo3AjzCD3QJ8NieyiIj1GuXu7HrJriV0FRr5/eikhUD0GSMt9eB0tLl15AZ5\nWOhyZyuf82P/xM/qWJbtgM3+XLWADGBglkcFdUf6qVwnenCNR+7c7wKxnhHW\nqXEO7rx0YdRbBDyXCBpG9Ap2tPKMHXk9gCLtKZFZ1f5LHN2ytRcV3/oJZVX8\nzznNr7+9C3M33MN6Uow7GQr/ynSnYvmN2ZsWtx+Fumnv+at0lIihUC6M4z84\n4cYFloxlRljULYyoV+VzKLEKj8HXOcKYb1DG/cCYP+8vdi7lPrjb34iWrcHw\nR/QAgbsUGnS1/X6i5P40GqlU9MHVjSLEjvGlCWEm1k1LQf6TK7SNPeg7vPzx\nOqIZ\r\n=ioxB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMKhD3L+kKrqAWJtm3timpqHS+msswqXO6hybtgdbvMgIgfdKpFt9Oh5rBVDg2TUGcnmjV5QgpT4grkVgm2kF+EHc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.4_1578921624028_0.7635952480317472"}, "_hasShrinkwrap": false}, "6.5.5": {"name": "cac", "version": "6.5.5", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "e9ea9b7a5edfe3ce0b4796a9895cbc840cc793b8", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.5", "_nodeVersion": "13.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-DF1HgrY2BkyrY1+g3Fwk/HFUcycI1PHpV3hCCxHM9qAPe+nrIOKFbzej2uiWdAjw4ry5eSYVWR4MLQYp2nV5YA==", "shasum": "fb7720ecb95988d1425e5b4a2db41f6c5cd6e672", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.5.tgz", "fileCount": 11, "unpackedSize": 70063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHyJ7CRA9TVsSAnZWagAAel8P/AkdcvDrVg3VN7wr6gqB\nHjN2PAmCIqcUEHlOtTtiPKyZEFSzYUu2bCMvzkNYB3aT8D4WCc3WeDmqP9V5\nbMGZs5Pi9O9ronKgaQWicpSCzz7ByWXkL3Zl8Lu4C156ZhaHtbVOa+hxTfSr\nUXj8YUCMTrnpjg0/pgt3JJNew2y4NuvgYO7XzeJeJBEOlm3sfiK11V7TGVlL\nWC13gNFcAcmAAZna9uCXw8KsklJHKO190GTS73jIRGCzB6vLRoEhfBDhzGd5\nOBZNeNWxnNmhYcof9BwejfW6+oPrNRgakE98eUrx/WWRBUkxcpX4nEkLZdBx\nd1mDNc+N4yyVgwFaVtQfkJP+FnsMHgyXS0rt2rc+c57DYF7gl8sSOakITVvm\n6qXW/yFZCI6Cx0+fkAGccfwzdDI8BmXBBHzhXlBZ5RgZiztEbmpNuUSVHSTT\nsK/M9hzLkgKJPNPyeKYpGv/bptUfFdTQXtcKzFZK9v7zA2u1lrlvFEPW8Qk3\naX84/KnyeCMNlR3LYDXRWl+loB8MrfwLpnaCDyNXaiFtOo3NWyTaQChx8xSb\nUopP25sDT4TcAGmyyTWcDF5WNrwnF3HVibEAU2lme69c6QHSO8XdF1ZXeVns\ntTacOdfzQOtaXqTSyh/YbK+WD1BT+ZfSQ1XTVcvq+cxkNfPTJiSOoUay3ODv\ntBb7\r\n=Wo78\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbbhoN9kOsC5dx6C8etOTphAT9wLca1Jy2qS+Od0FCzAiEA7EqiNQnIs/1TjE7XXBsPCVBcJV6aH1dRGjyKLAPibZ4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.5_1579098747147_0.03347466816335887"}, "_hasShrinkwrap": false}, "6.5.6": {"name": "cac", "version": "6.5.6", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.10.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-node-resolve": "^4.2.3", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "809db105a6ff738e915cf979c3855fc8f459817a", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.6", "_nodeVersion": "13.7.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-8jsGLeBiYEVYTDExaj/rDPG4tyra4yjjacIL10TQ+MobPcg9/IST+dkKLu6sOzq0GcIC6fQqX1nkH9HoskQLAw==", "shasum": "0120e39c9e56a7ab6418b078e6ad0595f2982375", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.6.tgz", "fileCount": 11, "unpackedSize": 70224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLrcNCRA9TVsSAnZWagAAPG8P/0t8P/CHcN/X+LnUKvWm\npkV5dlMLZispSZNJl/V0Qtmw861ZTmdWRDTa9ZXPBjz5UO+SCe0I+jjVdR//\nurV5Ly6DzSm2JKoVcr/JpUejmFuIlMpEF5FaIreVn/IfTx9dfp+9mRpbPLKC\nJ0MxCJVYUjzRtqgmzUhZI3CIwbH052/MIYpBmsLKQSatETqXB5ac1SzIiF67\nEFG+COIT6DnPt5E/ClvIiq7Qul50FnAJj3CO/Qeod3eHHvEwboZWR5gvVdTy\nu09FgfdROTLxkDBMIgQ9y41+TmJx5SPAY5aS6YHYRx8xTwQ2b7W2Nicokjrn\nUUfP2xMhVmPZPsDhIfCTW2PPJtnisLl3AxomBvwVh3hCVQBCSBo7XqEy3+Xa\nkeI8olcrgf+oWJVlKIRNrBYL3Nr63EY75zaOu97LVfjGvi+aMhq3DUMjc8cP\n8zQczvA8XMyToumRTDB9rnXtvG4L9vWF2o5g7o2Gn4F6tHL/rZdZfghzSBFl\nLunruzRv0rY0PhqVgRB/rVh83qziK6eeL1QGsW5U2nYCuX+RrFSRBgGjvKZR\nsCzYnF69KVISZwihzOHOpqqYQcSw7qBLakq3a/SniMVXcOnquMCsH/paF6Yl\n9f50l+wvk17z3gzpXmQuUzRRppvF28cvJUFK4TRuRq8s/6TTFZQ/IsLzC8Fu\n8Gw0\r\n=JFvu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZWkAUHbcY0wKZaER+6BIv3jbU8k/PJzDtqfv7W7ZBygIgSl61qcPbyAIhUdfpulLSTjPPCmKXkYSaz3E2a17gbIw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.6_1580119821197_0.32201727568150607"}, "_hasShrinkwrap": false}, "6.5.7": {"name": "cac", "version": "6.5.7", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.32.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "aa8f86833c854ad09abf2fdac8b101c03335c0c6", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.7", "_nodeVersion": "13.8.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-DjjOqLvoX/oO/snovTNm553kRYWTmIIQBfHQ2UqktbCudoHJuxzvRhjwdCHkXrQwp/lnu3bYyZ+LfaHtwk0Wjw==", "shasum": "3359494ced169f58f4ccdaa64f9b760badddde7a", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.7.tgz", "fileCount": 11, "unpackedSize": 82732, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYgtgCRA9TVsSAnZWagAAlrwP/Rycj2KwNQVIZz6DF0AY\n6LWTY3RHnfTNMkUO1UaupHQg+67mKWlNK0NQonzTc71jf2x7RBDey4/juAfP\nGtO3AU+LwimvsM+BzHbHHRBUB5AWKOkIEAdwEF1LTMHbiaQWI7LT/3AsU2At\nFGmUusvD4lZKgWESI2hRrtAQbhFd5Nr1mhilaB7Go/Oym+sUb7OBfH/Lj0hu\nKOuKL/x5N+IqlLSRbcN7DIbAwLTFv9T0s+NxrdlG5E+mApvztRnGQK+mCSRm\nG7nrrw6zhJL/JFPYk4vY83XG0KU/B1Ky8fSFocJclhxxfqDaxMXDgWh2ENyY\nyhw0uNZ+PVKwxQAXuNHJsaxLTIlTE0IBJb7sJT8rP+K90ebtqrp1xy93E8tn\nkjJNgHMnmHcLHzNu/2F+KwU6xUrC04ByYs154ZNqycqmj6M9D/a20p4bnF0c\nQGVM3cIAqayRSx8tLaM7wvHqzzh45evuXsdKSH/n/k23KPAppFKOlVWl8meF\nwX9lLE/ZBAtdDBXd06UaY+SYVi6IlfPObX92ekXDlkzS8+TWOCHoxzLZa1aX\n/Pjc4LhexppMJfyjPrB11ahxUMUAJSHUTB58xixBy6VaJjAsghRwiOezrqDE\nGIaFzNlUjJ1FHdjxWBPE0gV30y6V34j3fm+Xqg0kv5tULJSLOoZ9Niqd4AVK\nakSl\r\n=FSqy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/4CK4neYE2UAvgTMFxC0h6Pm/Tt41Y2TuwtaElKJfjQIgKgNCWaI1baX6y4itH/suEKjv+LrcG5XQ2UQYdddbtSE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.7_1583483743866_0.3201840255797608"}, "_hasShrinkwrap": false}, "6.5.8": {"name": "cac", "version": "6.5.8", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "tsc -m ESNext && rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^1.32.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "f607e96cb51a460c7f134de47afc776509599ca5", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.8", "_nodeVersion": "13.10.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-jLv2+ps4T2HRVR1k4UlQZoAFvliAhf5LVR0yjPjIaIr/Cw99p/I7CXIEkXtw5q+AkYk4NCFJcF5ErmELSyrZnw==", "shasum": "b15d183ee478226f846888be74612ac080533667", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.8.tgz", "fileCount": 11, "unpackedSize": 83224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedFSGCRA9TVsSAnZWagAA71UQAKKCN1h6MWjfAVbBWbzc\nDcI5XZBwfWcvrn/AiiExJ3KgKiEp97hgyqsDk9A7xBtSYa95VbuFFf8eqe+k\nkLB5CncwnWwqd0nCToNPSByOwrtQWZ6S4A8BWOWFb4VkzbtUsfAg7jYsH/Ci\nEpg/1eREFQcMkpGecm52eanFIXG9PFytbjKbHsSzw10PiSJVXdFBNN9ClHQj\nQZLBVgAI3etmXw80q/XVs7t5ZuGf6wA2j6iM5jDbZDGc1GTEapRrfhJVjDyU\nztDK8pjtYXRj+FZ5aJU0zw1khIF6q7dwOeNO0YvRK/oNLivFrWIDJPwSUQE4\n7ib1Q80XkrTq3Mkgds9PluzFsAKyfKlo3BCxQaGT4H4Jl6uziCj/OJ80mbKs\n7+Wv2UxTCT6kNF67mhc55hYueJHzD37Jv3ikq9UEPZ/9QDp8OZFOV8aNkvVf\nFEpYcQNuuOs2N/Wb2f8PqXDG2mTK215dHHpTRKQrfwELjpr8YZh9WgygIwkf\nRApSb6V5TJohuemua9cHqOuj5wsFrAE6SxTMBnBT7MEFpmptklk7feIcspzS\n7XHQGVq2qWuTktJZxjUaYCIb587h5CB8URclQ7+9KSqvtA+r8H6nqUSFW9LC\n0NCIawKkmZ0s5U0FxpCtau3F0kCR1VGQDBO0H71KRNPHVcSr4EPKmFWDwG0z\ny7++\r\n=Ee3M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzdhI9GLwyDynSLJzBbioU3SpxSmVeYEtDS0lmENCfhQIhAPyjPUHCAtmDmgUwYyY6FQ/ZR6oNAusPr2lFk7eVYAR5"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.8_1584682118145_0.8699976314016518"}, "_hasShrinkwrap": false}, "6.5.9": {"name": "cac", "version": "6.5.9", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "976c21ffb1c3525188f4f56022303f5cd5ec2e4d", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.9", "_nodeVersion": "14.2.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-raPrIuUiiwuFjumuq8oFh2PNJ/ySE92n+umpIka7JS7fv0oioc7Fx+8Xp/muUr6uocngpaOvFY89idJ63pZtYw==", "shasum": "c4fabd5e3b97e088be5df9ab588efe41067e54d8", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.9.tgz", "fileCount": 7, "unpackedSize": 77052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevOqgCRA9TVsSAnZWagAA6QQP/Rk9nZb7CugjWnfH/uZ4\nD5uDZPLJfZ3/wK1bQgXaS11xKVUZEry7xRBkcf+JUvKZN3ecvhjFRGrCdu6m\nHTKbBWzhlTC/HGzI91D+4B3nOWpAyKuj6dM4W93iMDWMvaPswBnshQb9xHp2\n/9+ZKuUezN/O21ttAdepkrN1hUt8/L2hC/dQYRBwTunbQ+PRekAil0iuSQqu\n9wJMAC1OOj0PziSWHz1WikLsJnBN/2JqLcg3aurTHDclxB0qj39BA+d7QY96\nrs9sJK2IVHckbBKnhpnpxZOLsgp1dlQweA59yNQeVQjFU0RYOsRSZ2k+jAER\nDFW/O65hbCvEiEWc7qE/D/RgDec7+D7J2r45gbDcd2Dcb3lfj+pYWcghc3i7\nJ+3ilpfgJjaW2LQ/wTiKg3v2kV8ubvENVLS6YUwkscyw1bCsdTDR/KXY7P0s\noVoao/kLlJgS+NY/kUFWAL69jCzssqewJh/fcWHjUoXgDnszME6MZvHW6i+u\nn4vzDrL1IX24bg6b5CBfnQ1Ra0kgj/CdaNgri04X6bNQjMucJxVBC7xQWbjh\nC0nXZzeY+IeeGDsXagWK6V4LDMwJhy/sagNoBMYWvIwKVtEDdVKXeXy3oomq\nCFRAnq6zK6z1mkY264dhAGS+XieFxZPr+mpYgYNpGcEgBK/fWIjQJSYAe0W4\nSBxK\r\n=wJyh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIACQp/jukymJejJlXVh7XQERM1CRNDGuVjpmgPqtHPH3AiAzO4uaW0gikkill+FZqSWILcOpvSM9mfXu7VprTgif1A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.9_1589439136357_0.9622782229682105"}, "_hasShrinkwrap": false}, "6.5.10": {"name": "cac", "version": "6.5.10", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "dcd80282d03e5556cde638b88d931f06ec8a0ac4", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.10", "_nodeVersion": "14.2.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-uxyxsID5p5kYlFFnhw86A4c8K5QTLRp6JM4AY2OtCq5lnnn4DGxV8YI1Z5rlt6KUjEKpA4qM+WZQshMoJY6dQQ==", "shasum": "be1d4509af1809dca601685145cd80ea85dabe0d", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.10.tgz", "fileCount": 7, "unpackedSize": 77142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexSLUCRA9TVsSAnZWagAAzVsP/2oQavvJRvDIMPEC/I3x\nvY8erUpdMrBwFSRrGeT+FV4gXg05cJentcX0Aph5SeyO4xQPuPTEu1TxorXD\nXR+6TX52Xyv/b6FXlQG8yR5wB9x7+nizicZp8YFBKgzPRh2AXinhrFjQHRvv\nEG/mFNPUDrETzV0g8QQUuBIi4EIc5dsLgeplkbGb5BT6B+IpkI1Wr+IS6DOi\nYuBf+PgI6It003Ru90YBW/JrE/pGl3pLvpXdiWjV7ZebMzblwLipjkZu8obO\n4gix45P3X6zOxIbngtfFnjHanFLJeHTkxjPyVssEBBuAXAVkxF0Moj2LOVjg\nXKEUCRILyQqSqAnjKjRYcktHDIvGRGJ2PDcxgcAs0Jg5cXno6XhAKjY36d0f\nnpT4a25ASBuk22uQAxKwM3uyDtjROYDZduMpeIPedgIFYoMB7bKyh4fe8Ouv\nWYO2igzrVIXNgdU5AExPSwy5jXzKI3KGt+BviVKStbjX+fl/xbc1K77ZVf7p\nLE7jQTuodCDJdkxfVubB3dEScD6tMsG8LmEbZC62Uk6W2fFJQjJoUTar6xf0\njlDNqxK0Q38JwOR040nyYWxiZV1HxA+9JgR7L5LW23tzft7MYR9n2KoL8j6D\n21RD8QTYpsr3EPeF6Sp93gTaVKmOcLL0jrggLKjhCufekBXasJtrMUoVpa9k\nTISB\r\n=Fc3r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5KB8SUCmshQwtI/T1ugTXT6STn3Cwvc92W0JjVx7GvwIhALc2y+wm4++p4TWKyeyRu9qGxkPI2z7rDsZUtMcclYJ2"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.10_1589977811809_0.16810834694520027"}, "_hasShrinkwrap": false}, "6.5.11": {"name": "cac", "version": "6.5.11", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "60362adc87d445720a5ccacab132b6931f25511c", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.11", "_nodeVersion": "12.18.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-GByEDKKjssQxwULgpVgn5c4HcnWeamvMfPdrFeFUtJC1ZNaAwxZ5S5OAR3wpfLDQxnRuqKGZbhjxMNKWk8SXOA==", "shasum": "bd18a6a8baab35bca580889e775e042552741729", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.11.tgz", "fileCount": 7, "unpackedSize": 77989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCIZdCRA9TVsSAnZWagAAQ3EP/2Ooabda2xUEPjZuYBxG\nbRx85TqpOybOwLmObzDOTO50ksqJ42KQ0chkjyrD0FVD3jUM9stiTrNn0Er2\nJDvt7bAM4nkmT3TprHWOQ8OGO/pp/soPdZOgURWHJcIzFIVLMkje+Ucgw30K\nVjYvOES9RlR8XOltTJ9/HMTbmTl5j7/OimF3C4O11mtNqqkeOWOzOR2L1qqz\nX63VLmeVwvUdAUIVESTqi3J1X4uoWd4DqZURUTAWq5r+45/RUuh/sl+HhbU9\nkNgF1AHt+7BoBJ8ky3uaQygpYsuHH+oU9KqpXHSkpMym8V3KClxv1IuCUn/v\nJjzeuDJeXW3UzSZF+XgEEwYGbSS5mnt2TNJP1jByHqyumdQ5aCTTwSCyzTT7\n8O9YXwl2g/yIn2lnyrv1bwMztVv5Jag2HCIoCKreZDKdGbRj6vbvORYxlDJM\nib0UzQJoBz/C3mGeevu2hEkg85/SEGMpGkDgCpgE5fttWFTCkvsoAP4XYMme\nF+mhtCR2xiSfuGzImHaIhArSEyZKQI0rUnijJRDJCEE5yXQIKwEfFzTmFbkY\n9z5eoQ4bq2zu2d8kJ//PzPC15TZbcV8y+f4a+bOBPJFYxGXYmevOIBV8xa4N\nevKQ/AguJ31FyZ8Gl1ZT8oD5icxPcTantUbI+srFHBZq2b+Nwgpz36iSp287\nARlH\r\n=VA3h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaWnjK7xcdegZnLdpkS8tH1SYdaP/YLCJ92i2t155bbAiB9wyC8ndfyeYxeTJ6/Pg1Azs4G8yoN3LuyAeNkTRf4uQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.11_1594394205346_0.801124239822417"}, "_hasShrinkwrap": false}, "6.5.12": {"name": "cac", "version": "6.5.12", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "557757cb30833d045e722d50d1d62cba1d37a30b", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.12", "_nodeVersion": "12.18.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-XUhnXoAEdDCufbxn0U1vhbPKMoTx3D/D5Dcn8s0bOl6RZmYdxFE7TsWFbMqMMAqVFGhdbnZpXN+HosiuJiGfAQ==", "shasum": "e166a9e68fea2689f5fee08bb03d9d9d4fadaaa7", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.12.tgz", "fileCount": 7, "unpackedSize": 77993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCIgdCRA9TVsSAnZWagAA/cYP/RNMXdZhD8pQ9Ixw1rtB\nMXqC1YRbr+LCp8byKCaNr/1vPQzvDYXInAvJVNZdEg9ZBDc1tRXVQ5k98agC\nGjH+tI++c8nS2C+Jrx5Pot3xbiNovPkGIsdpMhRZaQKBc8yp4TJUICV4UkKC\nOGpySdepCTDhPUw6ZdkkEE0WBqUY7+rOZEnTGX4mQmI8kcTku2FcIPYqvNXN\nxMYTp3swQXNmMNuNc15GKSuFWMIE2qS7batj4v2AhjxFIxH+0o1Fu8oCAjIP\nw0RgnEB6Tqe5jUZazTZxR/Ydbr04tXzEathqE+40Bd8dpUTMPOSZ8RjJrmhf\nc290PE+DQizByiP3wINUV19AqB790q1Il0j79g0WEMnTYniA7wBrOX+nLK+M\nhrMeeQR9tyWuBwQZTLEe5IbnfeWhRX3uP6fdwI1yv4buLGswDOIFWnVylSYu\n8Czrje8Ps+dRc5/jT7hCH+WH38UV9uJjfKi/HEGxvQQJep/wgu73nyKyFgRj\nrK7gt0MO/qQlHgzPTLM2hgiJwZgVZ7qAI/BNzfPWRu0GDKPl0YneKhHPlJ4g\nRBLbQDrd/8njSUA3WB+IhJHaDs9vDfwctBG099EsPN9fVARTO3c/XfVYtn9/\naMlfmYfmlBUsPOzmZXS3nsMtQVP7sR4hyO3itio4zn36iQbyFkuhUU8fNM4+\nVI9m\r\n=ale0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGBy+HeYH3gjd4qrNrMB4nqXYWsCLcLnTAq2Hp1sscPAiAbOnsT+fSrGyWh7Mtgfg2mg+uDVbug1///BzxRtH27KA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.12_1594394652836_0.3336968136798988"}, "_hasShrinkwrap": false}, "6.5.13": {"name": "cac", "version": "6.5.13", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "f5fb1090699a2208693e8af9dc6972a6412e1734", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.5.13", "_nodeVersion": "12.18.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-pt643//kQb3S6syXMJ/EIfNZpivKeFpCdnnPbJ3Iq9ZVZyXQcQjXvzVMWTEQuUspp+rHUlhZYZZfnviP88VuYA==", "shasum": "8d9be6dce0afb707c76c5962bf9cc6571f4c933a", "tarball": "https://registry.npmjs.org/cac/-/cac-6.5.13.tgz", "fileCount": 7, "unpackedSize": 78067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCyShCRA9TVsSAnZWagAA5oUP+wR8gnNY2dJlGBIdPMI3\niExjTEQbD645q5k7i2poXyGAFip339BlTRDkJl8xl0PSRL6kdosofwogZaky\n0CvnTWsxWAROCXcqCM00uMBaf5EXPat5WpjAfZHEkyqc1mOGK9GXGvL3kpZc\nUOYTh6bwH6wMbSPXsgeSd0WDv7VGOVJCiePNqQWAX+8z6XHufD4hXrituKU7\ntMfvFrabCjyqL6Ch87xhcajs8cAqFxsmgRND3K2U1VgK6DAj0Yn9MlkPmcRd\nnJVMwMrP6uXsb+JZRRuZJGra/XzuZzkawbvTEEaufMuBi0mlFd9vyDQrLcq3\nWdTdmmSwdK5Z3GGowRjCV3SLteh3m9e6RDi42z9UGMRx8lg6vFtUQXVS4hJi\nOrTHe/HYUjKeAVD2SJY4/Kgxj1OvO53dzGBxU0YDGvoH0AYuMhY+LUG1/+TH\nqk4N9aJ6dVzU46qpJZSKXM1CsxxZoHiXnDPgRnADYK8QOvlqZIEtm1+Aajtq\nhb24mG16ZHZE124VSDoDKsSH/gcQUsP1P9cJk12IaOWhY+fI4sBplA1DBgfz\nk/wLuXRlSjbkN9c1MwWaHQ85HinLNblkNxOU3G5/eGLM+WZSR4bngwXQ32eK\njobo4dmFAUZgEDi2k0EFCNf0PUh9W6o0LvtnUBzT91ObW3qh/iypcahNXd/8\nZm3z\r\n=mOGI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXBRsfQ7hSaMU573uBgeIUWlMT1s4PUpJQaQ4GFkDuDQIgTXF7uoOp8jGfvbq9AYvXWptoaOyxCE3rkJL5/JoWB3w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.5.13_1594565793129_0.760046458201282"}, "_hasShrinkwrap": false}, "6.6.0": {"name": "cac", "version": "6.6.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "mod.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./mod.js", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "fa236d40b7ee636a20e96a6018e4fa0cd38cd92e", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.6.0", "_nodeVersion": "12.18.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-F219zLS+TGmEw+AkKQZUCcC3GRJpckU3lsBIoyBoEUA82WapjT+SRmhh5YO1U4UzGXHxxaUctE3wAFAi0k36LQ==", "shasum": "15c3c63132aaefcb2b4b72e4f6c7a2eeee84177e", "tarball": "https://registry.npmjs.org/cac/-/cac-6.6.0.tgz", "fileCount": 7, "unpackedSize": 78244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDe6HCRA9TVsSAnZWagAADTIQAILBVVwZD8D5FqVek6Cm\nt0QTlF7srK77Djfw/fKmrQTobV/WXjN7hkE7TQ0eP4vLjNCWlFIX9didzqbA\nUtbOblZI3mgvjJh6pCEllKP6OLzBJyEerU/DL0V3IhogabmY6/Cheo7juEgF\n2LQZjdCNebuB61oOJWNoX+vWVgUqqTmsEOua+NqFzVtvR2ltlEb7+z+OEYqR\nHUD116I79VwbMJ/Ea+g0IUb4RPp9jDlgfxkpP2tJzDzgGMVygPCOgaTaYYsO\nByKNN7ujiAV9BPxrERIeZ1SpcqOTickaX6RLCeu0RgCACR/+QTQqqDcetnu1\ngo0Yav2J3eH3t4VW0a8A4nBZz8pVG15txkcQaLcsQC1o/Bw+oXjZNQkbUt89\nHXyl70UQCFgdyLuxhi4uouN5CPHNx25RIGrBMq7664RO3srdo9aGV2jBiEeM\nihBltevtWew8RY0GI3ro+H7s6iUyO1GZtxdQdHxsdxDBbPkFJVG/ipayqQxM\nL6d5Ik711ZfE0vdcNUmZwCooWS0LXfFsAU3PkOtdxmPBaux0Mx3/GaPGg3yR\ny1ocPBwv24u9DziUbRxTplxaV3r2yTYM+kHvqAeTwc2MTwbwQ26s9vtxBEtP\nVXemHPc5X4kAi4DFdBfTSDg06MsU6dgUdHSuf0avdRC/VlOBTKk/g99j9Knr\nnZye\r\n=2NOT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaoDUf8JGt97Raxz9vkDn7A0sohRKeVkW0AGL7nfHrvAiAzp4r1Nr48Bem9QndDowW6Ok02mpYWNBkr7/RXNM464Q=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.6.0_1594748550926_0.6327128063055316"}, "_hasShrinkwrap": false}, "6.6.1": {"name": "cac", "version": "6.6.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "mod.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./mod.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build": "rollup -c", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@types/execa": "^0.9.0", "@types/jest": "^23.3.9", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "eslint-config-rem": "^3.0.0", "events": "^3.0.0", "execa": "^1.0.0", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.1", "prettier": "^1.15.2", "rollup": "^2.10.0", "rollup-plugin-dts": "^1.4.3", "rollup-plugin-esbuild": "^1.4.1", "semantic-release": "^15.12.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.17.6", "typescript": "^3.9.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "d2c6b8a169359d0a5f17f0e3aa30e9c0e0a920ad", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.6.1", "_nodeVersion": "12.18.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-uhki4T3Ax68hw7Dufi0bATVAF8ayBSwOKUEJHjObPrUN4tlQ8Lf7oljpTje/mArLxYN0D743c2zJt4C1bVTCqg==", "shasum": "3dde3f6943f45d42a56729ea3573c08b3e7b6a6d", "tarball": "https://registry.npmjs.org/cac/-/cac-6.6.1.tgz", "fileCount": 8, "unpackedSize": 108687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDo12CRA9TVsSAnZWagAAbZEP/28KpYOrluzj9G1aHb3j\nmVw+vGliJN6m7juLZ2d1bciOhfSTcFRr+7zcXxxOPTL04izopHwGjTdN9tFT\n+LsiXMqB0F4z/uO3Th8EsqULadd+H0q2OUeqpaR+3kVeShAoKVdDzijV+MnL\nFAt14lFG3F6xlKz0vW0EvM4gysGQtNl7Y5Ud4Cdl8F/IkZ2023f0foY+mk5J\nx+H6Qmld1E4gSAwEOqfOP2MkHYCxFiicUnLFb3zy2i3YEg6yWX2DYFbhEEtO\nN1qkYS5GB8Q73wiJJuGOXQKtAj+vgTV2GMuuxt+ih2nWYqyTaLAFGU76GdCD\nQFLeKZ0xPVrNpLy+ElLuLQ2FC10oP/HjiOvUtEfK/eybldJGkSs40FOayGbu\nBx7xAYkVk+PYZXhVDqEWYHh+5o56A39JZnwj4SOZSHxLhypfshVedICfO1Di\nLeCpxGZXRC74y/NvrE4wSsLxBrwFE5PPS8vk7FBVtSVCo1kWJ72pMlKWEfnK\n4iuyxLDjzOHzp9ChpTQJ830veAiFKKpC2TUAhpUbfLlSARL/VjiYJ+uF3zi/\nx1UBvy3vPq54I2ZbbgUMnXnnV5KHgMp7pCsn8OvW+ETmIfkA9r7Ks+fKcZRD\n4SQuWhVtSKQEksw8/Hg9ALTloUq9sRpLNepbNhk8+bLoEstnvXxvMYAwB3o4\nfjY0\r\n=lPXH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOAeHhDq9m2FvXybDTSaRE+GcVOsRTt469TQUL6zf0TgIhAOw5WtZ0o9EFcnB/pqgBkPCgEAI7uAbGaArQrUXNbzOi"}]}, "maintainers": [{"email": "<EMAIL>", "name": "egoist"}, {"email": "<EMAIL>", "name": "kchan"}, {"email": "<EMAIL>", "name": "rem"}], "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.6.1_1594789237584_0.7352683556841542"}, "_hasShrinkwrap": false}, "6.7.0": {"name": "cac", "version": "6.7.0", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "47061349110edec0effe9bf83ff21ce4911fee9b", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.0", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-LV9bR76mUB1RlVdoe7+K8j8J/594bJpCdbJRaTsVH9hVIpjeX+4JJHmk7m6fofCX0vyv7OmDH3DOyUUnsFncMg==", "shasum": "be1ef7e3682a0180fc27e57fb1bf1afb88f2dbeb", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.0.tgz", "fileCount": 13, "unpackedSize": 80677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf06UbCRA9TVsSAnZWagAA/xkQAITy4yFmvXQiOdg3v55x\npglsy4x6/qi2UtocWlRq13eSs4RW8kZnIHD1s+gUDnePCgqMu7Jd49TC7byn\nRes2WKMH0nnFp8s5dJfhl0Ytvzt3xpXaMUpExGNu2mrFK9TopXdAjB457zjd\nd/Ffcg5d7zqNChV1MRoTu2isHCCgDq0WrOR6q1wQCuXtFeyAzMpbi7/kFVuQ\nzbj+ViLZe4sLRbDaUXBbx83Vmlu8lC1VTu4F7W7R26zKf8V9TI0RbUiKTN1Y\nB5o6Nq1OJ11huZSU7MZl0D0X5/J9M+VE6V3NA5AYAID+UKTPgGXh+82auwOi\nqdYlIBHTKtBF1r1c0VMGupJYIHK3uTdOo2EH09G6V9GqVhqUUJI8gRQZyBBS\nMLz7bReo6kFVQoFDDxTktaMzpSlSJMUk9gnwnbiHRhOt2cxSYxlQvOu9xZSK\nJ835gEbwZCskO6DaXoUIuqBUsKDT+lvknon+FDJIQLHwDtsktb5/waEUb8yz\nfAf+cNLn2sm+C4MaESN1Dzs46JPYWubry+MDLZD35K/m14xprxzc0XWEhOMK\nSdZoiMSb6w0W7zY4r5OWN8kdG5x+fKraa9mL+KEqvsN2vN7hdyrGdIt88Hxn\npxw5vZlMMIcilA/11PbOvCp2y1Y2+wMw2WQtTxtPoM8Yovq/fbwYdsogfgt7\nWToR\r\n=Q/l/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHJtuwslnWVQCEnOHRJtPNyUkpL1VjImxPeOX606psxgAiEA6kerWncdR/k5EaxNFIcx5/lDSzHp7bE5qAYb2Xlf18g="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.0_1607705882459_0.9022539105018799"}, "_hasShrinkwrap": false}, "6.7.1": {"name": "cac", "version": "6.7.1", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "8e4536eca023e7de199dac399bee5178c663d1bf", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.1", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-LfGt47+ugCY65W4yUEyxnZKd/tJSBJD/gUAxQGiQjH7yqdhbaX2XN0Rli4+0W0DJiDONmYeh0TlJxMtXGZspIg==", "shasum": "0609d28a31d887746de4b66a15e3914f106f880c", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.1.tgz", "fileCount": 14, "unpackedSize": 80720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf06ZkCRA9TVsSAnZWagAA0qcP/3oqER/b/gCQbnZ5RFFZ\nNak2gEEq1R2GFMTWEh0mBxhHcc1mX5cpgPxHko0plVKoPygO9T5Fo86D8loN\ndNVir585M5AF7idTxOFSZFWoA2+f9CJB2IZD2pP7WymZbln/gpu9sdyi8nP6\nDXWkYRUpDcMWdhYF/ZtbVXaL/XOHX43mK4Q0WsGodbUE3okl8LljLBxn3yv4\nJq670nd3oc7epAm65k3PWQqXYIZLDEIRcN29Y/iNjiW5tAGQ20Gcv38r9DQm\nxut1YHbvwNFT68e7o33GzC2XnbLG5zzd46cN/WI8lA2oa/RUhKleqDvO188v\n67gzqWroWhx5kLkeFL2RL+o7rLG50nbLSfDOKj3GyAVXDC6YNepprm91HV5V\noEdbTB5IM6q0mfEmoHOd35CHDlj3HT50zIYE+0deAbWfJ7HC8KjQOKUGXYnd\ng3m7ZeUWd+5HTvyXUbK/UbFpB1SG0GHks9ZmFOz6Zx0Jtj+dQenON+ieQvhF\nlS0q9ZSj8WOyf7WthpqxTtArMKW4IqOBfNlvApcDb+fgHqYSsh558UC5XXN7\nv6VncI2xHagbCIcB7Jj0sTuw7gPr9wL3iJ2+ldCcWE+3GOHOEfYcwofCreb4\nz/qvoh3TFo4mvOU2cHcCb6+drLY8ICI9xyeh+F8QIg4hg35MclBGmfi9OgfH\n03lT\r\n=c8hC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHqNVkYhThp/frvp+u3exnXXaIgJ0Vo5W2lM8aKsXITIAiEArTAG+0/VD2uj7XdW4tImPdKH26N/Nloh8/O8EdbIaao="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.1_1607706212346_0.6127443469319294"}, "_hasShrinkwrap": false}, "6.7.2": {"name": "cac", "version": "6.7.2", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "f51fc2254d7ea30b4faea76f69f52fe291811e4f", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.2", "_nodeVersion": "12.20.2", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-w0bH1IF9rEjdi0a6lTtlXYT+vBZEJL9oytaXXRdsD68MH6+SrZGOGsu7s2saHQvYXqwo/wBdkW75tt8wFpj+mw==", "shasum": "e7f0d21f4776c46c7d0de7976e56fa5562e17597", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.2.tgz", "fileCount": 14, "unpackedSize": 81107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLLY0CRA9TVsSAnZWagAAhX8P/36UnIjTIUABGPzb7nwO\n1P59348bRrMKrMfJSHNY/QZHt+Lh0WBB8gvQb4kIk4iDpToHJpgifSiYKF8S\nKF5pThTemaP0Qr1DpkyLEZT7gH7qN+wMNotHG2Y4G1enPzFgP7n34HbOmPpr\nNdmfIiI288gkBa/CUGLzpZVrAN46o6KXjqgpAyEVdH2qQVXBC57TvCkANHM9\naBuNMoyE7sFzidFG3pSYEtXTTb36qqEPvSizbQ3LNuz6bZCCupVzmUf3vhlS\nQaN5qfPsLUWCsDLkMlVQfy16VQ9U4LUj6et3wrnRvm7MJYL7YSrFWUovsqPt\nHxYmWsKWAq8VPukV2RarfWbJpPJuXAYYe8O63rHObcatZlsEf5DwuCPt2hH3\nYRqWV51vCZc4dSGDzVG+NZv53liLZI5MGru4MdvPwK+n46gqislaCn16NX6d\nugIw/wegAsu6DM5zbpt45pCU1VrXe5//JDPr/KuGSFk4LUC+ywShNd17V55B\ndRor2UF9al6P84DfXcbNIQ5cxoGpXCPSJs6MfQmYltlUvepVcLhfqs/Q12je\nYn0c1SrwbyuIrw7GcTL5vsMhlVqk0NRju14YXN/YsNWZOvxIwBNwXdkBrS3z\n1F9E7tRm5FNu/qASX/PtdZ5CjgNBSqlPoqFFEOQ5oeaYRjZmV8nN0STS63Op\nAxlm\r\n=9KdJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCICeZ2v41enkRg0ryEl1949AtrAbP2POSMExIM4aK15QIgEOT/35vT/WL1O9NJKFb/byQnaNYC2YAgKsKdoNwN9Ss="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.2_1613542963434_0.28543049437650536"}, "_hasShrinkwrap": false}, "6.7.3": {"name": "cac", "version": "6.7.3", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "5060bd9af7361a0757d83d84ef52ea34c72f291a", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.3", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-ECVqVZh74qgSuZG9YOt2OJPI3wGcf+EwwuF/XIOYqZBD0KZYLtgPWqFPxmDPQ6joxI1nOlvVgRV6VT53Ooyocg==", "shasum": "10410b8611677990cc2e3c8b576d471c1d71b768", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.3.tgz", "fileCount": 14, "unpackedSize": 81191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggWvpCRA9TVsSAnZWagAA8qIP/i3MdYFgG6T94y6yiyB/\nJYje1WFKcSWmlirkQX2BqLCXDdzO72zckMxXVBZrhXbWV1vjkz6hX//iDQa2\nG+ELRZDvhJ8Krpvtvb3U+wR5ELeZxUXpLYcirDdFezDLvpBESFhRjOkJCNBA\nsfFfZaDuc2gdHJT6UegYFTqA2EYvsEDnnV7H/nfRxwj7z69qafiPJ3w/pTQf\n6d7SG0QoRry7sZbKpwVq9dHZMK3ee/dFV/2NcYH2vn38YTmD9GD32P8/aOSa\nHQ6rL3p2pzpKmKdsB+/o7zRkkAuhfLvpgc69t4OkEaIFVaPj5D6y3GhXOce1\noRYYj+S2lZ0KENshUXg1xw9Q+0vDZ3FIOBKUJxKzt4XWvKun3S4mZioImWwk\n2JHjWtqP0yqxjtbZRCTgrIAAT7h61xQDCnmtKaweV9jOecDkT9IUH6zO3qF8\n22uYDloTQYlcdSCbONkNJQ8KTQIZk+3oD3eTxNe7+znYyYCO+mLtuser3THb\nlb90yVb6vk5YoO1YK61MK9esptamhTCTsh6UOt8CnYRTZ99/9yB4t/7L+Fi7\n2aJ1Xs07Z3A0J16qd2BeS6hEZ4BmqneQoV/JAtBHJM7wR6BJV5iaVRTJ5J/E\naXqi5NmjnmVf5XvqXpFNeDzUQdljZDNDUIFMhG4cBAr34QkcQQ3a0XJTq3Lo\n2FYh\r\n=hIc4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFY++VmganNhgiPOudZmMTOHb4BEzTO21N0wjFbqiM9GAiBLJx+qsgc1/FPs1w9gnnJdq0bLWbzsFdgpY1xsvtdRlA=="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.3_1619094504352_0.1905422078140515"}, "_hasShrinkwrap": false}, "6.7.4": {"name": "cac", "version": "6.7.4", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "550d0f7f7e00e8e16c52860ca54bc2e8505338b2", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.4", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-sbhOAYEJZPJ5kZc5DnyK7usCKDGPe/ypDBw6hYGdZF4N5tmNIaD5rPVAFtKOexVpOi2hbuqQ5AKWUe4lbveApg==", "shasum": "af5fd087826e4ca239071a03efb9afe022fd46c6", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.4.tgz", "fileCount": 14, "unpackedSize": 81308, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjkkIGJpvc5JvJ+1XVbfwTxxYPKRUAb8FnaCBVctmpkAiAx1vDr5wC5hOoVJbZxrkQ2J3z+kg8/98boTT2pK6CWsA=="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.4_1633522997905_0.5516200732220959"}, "_hasShrinkwrap": false}, "6.7.5": {"name": "cac", "version": "6.7.5", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "4247f99c7eea048c1c7f898ce9b1638f46dbc515", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.5", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-SqI788LHLGFwxRjiwqkJARpKlBmlEEJXQpcZaBkiu+DBArqBbqb8stCorRCu0r7Qvlu1XzYwzC67XHqKwmgKMg==", "shasum": "ea1fd914e5efa78623e44eb1bc81bebf284dd345", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.5.tgz", "fileCount": 14, "unpackedSize": 81191, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnMt8WQ7oZh6pRiLtwpUo63tL+aGAOxl39dYDXT9rTzAIgVRAuVUrr3wglB3H2Wl22uNq88g7xh5R/uYfvY9k+tso="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.5_1633523905029_0.3537767395408691"}, "_hasShrinkwrap": false}, "6.7.6": {"name": "cac", "version": "6.7.6", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "54d5fab50fca9d56b68ab2bbd598b7e5b58d136f", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.6", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-drX5G/c5oLn3DdBVX8RinEhfnsrtAiPG1pLAtlRpomfNK/jzfapC5lheNeADV2OyvIS4jX4eXyPcgc5E6TmjQg==", "shasum": "3792cf91319cedd1cb52b6f5c4e91ea11af35d65", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.6.tgz", "fileCount": 14, "unpackedSize": 81401, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEBjpSt8EvLtIOGVKl0mRvy32YluCD8NV7QR5mCL9249AiA5TL5ne6W1aOErMbo7Y9Hz3plob8vHaZQH28bTqvofVg=="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.6_1633526156724_0.6550735387282576"}, "_hasShrinkwrap": false}, "6.7.7": {"name": "cac", "version": "6.7.7", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "abe662b577429b854b1eb6f811d828d23f21ce1c", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.7", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-N9W3rhyGH4O1rPAPYREsZkGmWJifXVhmJ9UrTUNy+FQQZb1w/676qBgzZThR4r/3HahLOhvy/DVJ6qug3gCReQ==", "shasum": "04e35209bc3105ef4eeee34ec247aef48013efe0", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.7.tgz", "fileCount": 14, "unpackedSize": 81296, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwrZvzDbS0jxd9rlqtbiNdnYx7nXuJ9WTY7IEvm4AdVwIhAIC9muDuEN/I+RwX61BeDXRvsU1AvjfbRD1e0kki8Mi7"}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.7_1633527227425_0.6952919416770238"}, "_hasShrinkwrap": false}, "6.7.8": {"name": "cac", "version": "6.7.8", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "702d41af0c5d2c6ab8969a22d5647dab9f34f5da", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.8", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-EozC0vCN4aX3LbcVAzN0tW0qKeDFDXZ74xIAfCviLP2B6yzWPMaYLlMP0EHi2oZKvNqRgY35bOKQtWc5xQ0dDA==", "shasum": "5b73bc5265fb55ccc132befef40eb9b831ae610b", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.8.tgz", "fileCount": 14, "unpackedSize": 81619, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCc9t/wifkR3rqxw4cG15boj8hBmE72z/jQsRYz4xIh4wIhAMLFyXsDDHdEZ3G4PHE1PdTfQBddQTjxCYzkAO7oAnEf"}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.8_1633702601302_0.021708693652749522"}, "_hasShrinkwrap": false}, "6.7.9": {"name": "cac", "version": "6.7.9", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "0815980974e9863619087a6643193328d446d2e4", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.9", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-XN5qEpfNQCJ8jRaZgitSkkukjMRCGio+X3Ks5KUbGGlPbV+pSem1l9VuzooCBXOiMFshUZgyYqg6rgN8rjkb/w==", "shasum": "70a2013067ce97c34b4acf18293cfcdbbef556dd", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.9.tgz", "fileCount": 14, "unpackedSize": 81697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3hA+rtxwHDXD6wQop1sB+DNcovjlpGUgRILK9URriAAiA9kWWbTrH5vkuBYmE/8jCI3rxcymDFnYIdPKZU1L/Iyg=="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.9_1634026565114_0.9019410706247455"}, "_hasShrinkwrap": false}, "6.7.10": {"name": "cac", "version": "6.7.10", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "index-compat.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "46b2114e5597f049ad31a6ca50873ccbe1b60ba3", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.10", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-g/nCWTX3w6dUG8mipU6Z0gZD5XP+nzO4T+TotHjCq4Bzvv3qEoLLGzbIkrnRDXl2coK4y4RFkmTNR/9OT6L2yQ==", "shasum": "9977f5841a2640883c6f5aea330e15201d9a2809", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.10.tgz", "fileCount": 15, "unpackedSize": 81578, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFjbySfyYSrTo+2AVQ0Ls1I7EkvfeQv632IK0ZG4nR+aAiEAh4uGnbOz0TRWtFvSi8wwCER9qoiVcrRng9ImPQmkWR4="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.10_1634032004276_0.2681200531615935"}, "_hasShrinkwrap": false}, "6.7.11": {"name": "cac", "version": "6.7.11", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "index-compat.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./index-compat.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "5e07a980f31c53db80c286fc90a8e060598ada17", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.11", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-m4xrA2MKfid6uDV2j2+0mXrtPGxlvAW0y+7Gnn2P8WVMSG+4e4tcoYX++94ZPblPfpBccJ5e7HvKdghlX5yiDA==", "shasum": "8828e0874e578b60d839a7591567d7d5bdc7cbce", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.11.tgz", "fileCount": 15, "unpackedSize": 81580, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZF17sfWD8OMLLXY5hrBNxOMWigxTFVeFf2Ok4aQwERAIgHeRil7LKdNBvm5L6fnRD0Klus/qcxAHKLfWNCgGa3K0="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.11_1634040916403_0.9790955954248615"}, "_hasShrinkwrap": false}, "6.7.12": {"name": "cac", "version": "6.7.12", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "index-compat.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./index-compat.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "ef52bc9c967550c19c43f53b623a4dff95741eed", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.12", "_nodeVersion": "12.22.7", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-rM7E2ygtMkJqD9c7WnFU6fruFcN3xe4FM5yUmgxhZzIKJk4uHl9U/fhwdajGFQbQuv43FAUo1Fe8gX/oIKDeSA==", "shasum": "6fb5ea2ff50bd01490dbda497f4ae75a99415193", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.12.tgz", "fileCount": 15, "unpackedSize": 81581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2lVyCRA9TVsSAnZWagAAyj4P/2aiEBD10QFDWi/NFgeK\n6Nem1kbZm7lgsFjRAmsoGmBr0OBvo8zJgA+iTknXysRCFbROJiGUp3eRkFUh\nIDTzwsSVCCIBynInnK+iOPEVxYlblQby4kRnJkTW/pTzW0Du5rTpf9+40z/9\n0tmls8FJE4PGCu2w0GJIdKuW3hawOGJmqSmO4Or/O6sRCrEUw/hUty5sPgWT\nABzhcEEPWT+x4Qsceyha9StXwqJLT7zrs5/TOd1C8DW4/KpLgAOeSD2VKNMJ\nm4DYRqCgTLIQJt4kdwaUpdxYwr9V3F2yk7IYWo1P+4GcwviYYM8xYCb8nv6J\nxqI+OlCSRtsi/wXW9kdKfw0NauyCuquGZL8PSXdaytx5Hnc7E/nypjMDGL9X\nQOICxk+DT54TrZMHI+ejxNDPEkF4ldu8Mg4JqDmsaajR15elKaFW1iE23e6l\nfrJf/qb+y0Aob8ZimzWrTNsen6aBJv0CdoIc3pZ5+1K99HF6oDgLzRs5fjVg\nkWfpJuFUazd5RM07u6GHIA0Yn5ZtwBNsDgE4m4Bvv8xLOOlErLA8QnHqpx6/\ntvk6eM2tPtlbg72u39VmRQZOquFv2Au4pFhFJi9HR5CRqyvTAvYoX6Etwt18\n2SjILEoQDtBKuj796vTKs5g/EWOYCB60toog9HpWrkym/yFT0Jwwk23Rljmn\nVBRK\r\n=X+F/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsRIzLUem99YYZAjmvnl07xAazhlfMBUI+7hsYbwMr3AiBskrQR7syE18cZPNx+jh+m7GctV2hetCRgdy545uyynQ=="}]}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.12_1636680898817_0.25580898207883984"}, "_hasShrinkwrap": false}, "6.7.13": {"name": "cac", "version": "6.7.13", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "index-compat.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./index-compat.js"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "b8aab4a63d6ac44633484d43e674ae8b0959aaa0", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.13", "_nodeVersion": "12.22.7", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-vmYNsJLaApX5bnQH+ZCcKrTzeQbq4Go+XsMpP+TZqZRENQYFctWswzY7fp5OUKsm/FhYIjzD8rmwrUgd0hpFpQ==", "shasum": "c4369f6b685f7d9396847c39c6a1dfa4b139026a", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.13.tgz", "fileCount": 15, "unpackedSize": 81799, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhq++lvGBO/XuG9N0zysmzLJxyOYEy+wJA1drWDlaWZAIgULokwY6KxtvfujqC9g8oblKbnQ5xJ9GOWZE0otUhuRU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDB3NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhtw/9HA/rhXKjTbEiLRqCA9Q7pgVyNYGcLg4Bik2UKREHL2NSRuPG\r\n6SUiaIZZNYEYeTIdMgi8dZBSMUFezkkvFT/VxxlwKlD61C5QuS5KLgXJCKQt\r\nb+22KTAv6FOm7E3AuHcRxb2CGoPaAt++IBdw0QY+4YNiMid7yYmIy4jd+7wK\r\nfw2OYLEkTMJF5G4wtJMCHHvsSsES/P3d0ZeOxqULv6X5jiPaPSQO21eNrabs\r\nOxCLXfMsaYOGAR6DPT/K33/lFsNZ1sdUP/cBQMc1WHulmi74pq11Zcnfsg9f\r\npOHOOJ3J5OlIIvP749novJUR449Vj2g0gq+q44oTbJn1hV4PR8nr6jdhY66K\r\nBzHzfRi+ikh9+dnfn3pCsCqz6DFoa3EnQ6IyOzn1QS4lFnxRnLOibq42LWjN\r\niN/h0mgRxJseKzC5mFWCbiUzJW+alIyXi5JiQbN64JaV4IxGr7NYloTq5DpX\r\nvma36JKwlBglQsJZXbu3A4yiTnHcLaaedkC+p7C60UvalP/7U3/610Baf98R\r\nkqrYmavyqX1BTRQaINhQl4caQutG3GbdbZ5O+fNApd//VRNYnKloihTWIYAe\r\n4NHTP2PtMCTHDOrJYVJNnBZpfQJYvXqVVyybuZmdi7RQfWmXKbReJ/EfwQ7+\r\nqgJvk246g10zwxW870K+mW9F6pdU/ECxLfw=\r\n=5yKW\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.13_1661738444864_0.2700067082952122"}, "_hasShrinkwrap": false}, "6.7.14": {"name": "cac", "version": "6.7.14", "description": "Simple yet powerful framework for building command-line apps.", "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "main": "index-compat.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./index-compat.js"}, "./package.json": "./package.json", "./*": "./*"}, "scripts": {"test": "jest", "test:cov": "jest --coverage", "build:deno": "node -r sucrase/register scripts/build-deno.ts", "build:node": "rollup -c", "build": "yarn build:deno && yarn build:node", "toc": "markdown-toc -i README.md", "prepublishOnly": "npm run build && cp mod.js mod.mjs", "docs:api": "typedoc --out api-doc --readme none --exclude \"**/__test__/**\" --theme minimal"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-syntax-typescript": "^7.12.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@types/fs-extra": "^9.0.5", "@types/jest": "^26.0.19", "@types/mri": "^1.1.0", "cz-conventional-changelog": "^2.1.0", "esbuild": "^0.8.21", "eslint-config-rem": "^3.0.0", "execa": "^5.0.0", "fs-extra": "^9.0.1", "globby": "^11.0.1", "husky": "^1.2.0", "jest": "^24.9.0", "lint-staged": "^8.1.0", "markdown-toc": "^1.2.0", "mri": "^1.1.6", "prettier": "^2.2.1", "rollup": "^2.34.2", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-esbuild": "^2.6.1", "semantic-release": "^17.3.0", "sucrase": "^3.16.0", "ts-jest": "^26.4.4", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typescript": "^4.1.2"}, "engines": {"node": ">=8"}, "release": {"branch": "master"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"linters": {"*.{js,json,ts}": ["prettier --write", "git add"], "*.md": ["markdown-toc -i", "prettier --write", "git add"]}, "ignore": ["dist/**", "mod.js"]}, "husky": {"hooks": {"pre-commit": "npm t && lint-staged"}}, "gitHead": "8342919821fbffa733c6ab9558f4d60fc43f9ff0", "bugs": {"url": "https://github.com/egoist/cac/issues"}, "homepage": "https://github.com/egoist/cac#readme", "_id": "cac@6.7.14", "_nodeVersion": "12.22.7", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==", "shasum": "804e1e6f506ee363cb0e3ccbb09cad5dd9870959", "tarball": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "fileCount": 15, "unpackedSize": 81801, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpa8AN4aK0bsq3rKgaJuIays928WOExQjUFRbEaYRCXwIhAP57xVxkkbLQgnT4UhOAVdpE4j+0FHJk8+EkzlSFNtG5"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDCwrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmochQ/9FzF1eg/YpriuaXJulJb2qcwRy3qHeL78nM2KyAW71cEYla2Z\r\nvGBN41IcS8+8aygih3xHHBybhNGaQO8TxPCldNoVAc2493TV+EH7v2xYcfMj\r\n8wbxH0Y8PvYM7LUMggfSyEWS7l4ACzwDLfeMhOcFgQQAZRKXKRwqHSSkpkyW\r\no3KGc47fk7SWNGMr5rW3IyLXeW21VbpDbrVrgd48qVCw673vho7TG0Q12TTp\r\nBaBCm9C56Ae29F8WGGmbepBPmGGtX6cb6xCX9sxTEwrutCgDIrBOnLKiZr6P\r\n+HnUK08cxSXAJIviJla1AgFax0O+LkjXsTVU7RvGGb0aTFgrmOTz+A0YecF2\r\nXQVt+t56Cy2SlWQG6o7/ZX2BS9b4+m4qzt0I7PFciEX9fG4mK943ksjWKzgM\r\n9K2K7U882kKZ7SfaC+5G8C362e5wcY5mqrQzcyXs6lzlTCaPefa/6VvY9zoH\r\nka0xIHbDuKjFI7eyUt8KkwU5AEfr8aHeNBd1X+UdgI8sj+cAzU3ZAUTk4tyo\r\nd+/WPRHY822WBSgYygBtn53AYOald0YR9jnUFVL5hOUHGJO6VbTWb13Zoiyr\r\nL2Pvbjf9tZ6eySgTt4Rn87KCCAgWhgupomWC9BQ3wujSys57NjI6enplOtjS\r\nDrQINgOWVRYzcEHIs9jjoXsrbwSKO4L38PU=\r\n=qoxE\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "egoist", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cac_6.7.14_1661742123283_0.003141552115743629"}, "_hasShrinkwrap": false}}, "readme": "<img width=\"945\" alt=\"2017-07-26 9 27 05\" src=\"https://user-images.githubusercontent.com/8784712/28623641-373450f4-7249-11e7-854d-1b076dab274d.png\">\n\n[![NPM version](https://img.shields.io/npm/v/cac.svg?style=flat)](https://npmjs.com/package/cac) [![NPM downloads](https://img.shields.io/npm/dm/cac.svg?style=flat)](https://npmjs.com/package/cac) [![CircleCI](https://circleci.com/gh/cacjs/cac/tree/master.svg?style=shield)](https://circleci.com/gh/cacjs/cac/tree/master) [![Codecov](https://badgen.net/codecov/c/github/cacjs/cac/master)](https://codecov.io/gh/cacjs/cac) [![donate](https://img.shields.io/badge/$-donate-ff69b4.svg?maxAge=2592000&style=flat)](https://github.com/egoist/donate) [![chat](https://img.shields.io/badge/chat-on%20discord-7289DA.svg?style=flat)](https://chat.egoist.moe) [![install size](https://badgen.net/packagephobia/install/cac)](https://packagephobia.now.sh/result?p=cac)\n\n## Introduction\n\n**C**ommand **A**nd **C**onquer is a JavaScript library for building CLI apps.\n\n## Features\n\n- **Super light-weight**: No dependency, just a single file.\n- **Easy to learn**. There're only 4 APIs you need to learn for building simple CLIs: `cli.option` `cli.version` `cli.help` `cli.parse`.\n- **Yet so powerful**. Enable features like default command, git-like subcommands, validation for required arguments and options, variadic arguments, dot-nested options, automated help message generation and so on.\n- **Developer friendly**. Written in TypeScript.\n\n## Table of Contents\n\n<!-- toc -->\n\n- [Install](#install)\n- [Usage](#usage)\n  - [Simple Parsing](#simple-parsing)\n  - [Display Help Message and Version](#display-help-message-and-version)\n  - [Command-specific Options](#command-specific-options)\n  - [Dash in option names](#dash-in-option-names)\n  - [Brackets](#brackets)\n  - [Negated Options](#negated-options)\n  - [Variadic Arguments](#variadic-arguments)\n  - [Dot-nested Options](#dot-nested-options)\n  - [Default Command](#default-command)\n  - [Supply an array as option value](#supply-an-array-as-option-value)\n  - [Error Handling](#error-handling)\n  - [With TypeScript](#with-typescript)\n  - [With Deno](#with-deno)\n- [Projects Using CAC](#projects-using-cac)\n- [References](#references)\n  - [CLI Instance](#cli-instance)\n    - [cac(name?)](#cacname)\n    - [cli.command(name, description, config?)](#clicommandname-description-config)\n    - [cli.option(name, description, config?)](#clioptionname-description-config)\n    - [cli.parse(argv?)](#cliparseargv)\n    - [cli.version(version, customFlags?)](#cliversionversion-customflags)\n    - [cli.help(callback?)](#clihelpcallback)\n    - [cli.outputHelp()](#clioutputhelp)\n    - [cli.usage(text)](#cliusagetext)\n  - [Command Instance](#command-instance)\n    - [command.option()](#commandoption)\n    - [command.action(callback)](#commandactioncallback)\n    - [command.alias(name)](#commandaliasname)\n    - [command.allowUnknownOptions()](#commandallowunknownoptions)\n    - [command.example(example)](#commandexampleexample)\n    - [command.usage(text)](#commandusagetext)\n  - [Events](#events)\n- [FAQ](#faq)\n  - [How is the name written and pronounced?](#how-is-the-name-written-and-pronounced)\n  - [Why not use Commander.js?](#why-not-use-commanderjs)\n- [Project Stats](#project-stats)\n- [Contributing](#contributing)\n- [Author](#author)\n\n<!-- tocstop -->\n\n## Install\n\n```bash\nyarn add cac\n```\n\n## Usage\n\n### Simple Parsing\n\nUse CAC as simple argument parser:\n\n```js\n// examples/basic-usage.js\nconst cli = require('cac')()\n\ncli.option('--type <type>', 'Choose a project type', {\n  default: 'node',\n})\n\nconst parsed = cli.parse()\n\nconsole.log(JSON.stringify(parsed, null, 2))\n```\n\n<img width=\"500\" alt=\"2018-11-26 12 28 03\" src=\"https://user-images.githubusercontent.com/8784712/48981576-2a871000-f112-11e8-8151-80f61e9b9908.png\">\n\n### Display Help Message and Version\n\n```js\n// examples/help.js\nconst cli = require('cac')()\n\ncli.option('--type [type]', 'Choose a project type', {\n  default: 'node',\n})\ncli.option('--name <name>', 'Provide your name')\n\ncli.command('lint [...files]', 'Lint files').action((files, options) => {\n  console.log(files, options)\n})\n\n// Display help message when `-h` or `--help` appears\ncli.help()\n// Display version number when `-v` or `--version` appears\n// It's also used in help message\ncli.version('0.0.0')\n\ncli.parse()\n```\n\n<img width=\"500\" alt=\"2018-11-25 8 21 14\" src=\"https://user-images.githubusercontent.com/8784712/48979012-acb20d00-f0ef-11e8-9cc6-8ffca00ab78a.png\">\n\n### Command-specific Options\n\nYou can attach options to a command.\n\n```js\nconst cli = require('cac')()\n\ncli\n  .command('rm <dir>', 'Remove a dir')\n  .option('-r, --recursive', 'Remove recursively')\n  .action((dir, options) => {\n    console.log('remove ' + dir + (options.recursive ? ' recursively' : ''))\n  })\n\ncli.help()\n\ncli.parse()\n```\n\nA command's options are validated when the command is used. Any unknown options will be reported as an error. However, if an action-based command does not define an action, then the options are not validated. If you really want to use unknown options, use [`command.allowUnknownOptions`](#commandallowunknownoptions).\n\n<img alt=\"command options\" width=\"500\" src=\"https://user-images.githubusercontent.com/8784712/49065552-49dc8500-f259-11e8-9c7b-a7c32d70920e.png\">\n\n### Dash in option names\n\nOptions in kebab-case should be referenced in camelCase in your code:\n\n```js\ncli\n  .command('dev', 'Start dev server')\n  .option('--clear-screen', 'Clear screen')\n  .action((options) => {\n    console.log(options.clearScreen)\n  })\n```\n\nIn fact `--clear-screen` and `--clearScreen` are both mapped to `options.clearScreen`.\n\n### Brackets\n\nWhen using brackets in command name, angled brackets indicate required command arguments, while square bracket indicate optional arguments.\n\nWhen using brackets in option name, angled brackets indicate that a string / number value is required, while square bracket indicate that the value can also be `true`.\n\n```js\nconst cli = require('cac')()\n\ncli\n  .command('deploy <folder>', 'Deploy a folder to AWS')\n  .option('--scale [level]', 'Scaling level')\n  .action((folder, options) => {\n    // ...\n  })\n\ncli\n  .command('build [project]', 'Build a project')\n  .option('--out <dir>', 'Output directory')\n  .action((folder, options) => {\n    // ...\n  })\n\ncli.parse()\n```\n\n### Negated Options\n\nTo allow an option whose value is `false`, you need to manually specify a negated option:\n\n```js\ncli\n  .command('build [project]', 'Build a project')\n  .option('--no-config', 'Disable config file')\n  .option('--config <path>', 'Use a custom config file')\n```\n\nThis will let CAC set the default value of `config` to true, and you can use `--no-config` flag to set it to `false`.\n\n### Variadic Arguments\n\nThe last argument of a command can be variadic, and only the last argument. To make an argument variadic you have to add `...` to the start of argument name, just like the rest operator in JavaScript. Here is an example:\n\n```js\nconst cli = require('cac')()\n\ncli\n  .command('build <entry> [...otherFiles]', 'Build your app')\n  .option('--foo', 'Foo option')\n  .action((entry, otherFiles, options) => {\n    console.log(entry)\n    console.log(otherFiles)\n    console.log(options)\n  })\n\ncli.help()\n\ncli.parse()\n```\n\n<img width=\"500\" alt=\"2018-11-25 8 25 30\" src=\"https://user-images.githubusercontent.com/8784712/48979056-47125080-f0f0-11e8-9d8f-3219e0beb0ed.png\">\n\n### Dot-nested Options\n\nDot-nested options will be merged into a single option.\n\n```js\nconst cli = require('cac')()\n\ncli\n  .command('build', 'desc')\n  .option('--env <env>', 'Set envs')\n  .example('--env.API_SECRET xxx')\n  .action((options) => {\n    console.log(options)\n  })\n\ncli.help()\n\ncli.parse()\n```\n\n<img width=\"500\" alt=\"2018-11-25 9 37 53\" src=\"https://user-images.githubusercontent.com/8784712/48979771-6ada9400-f0fa-11e8-8192-e541b2cfd9da.png\">\n\n### Default Command\n\nRegister a command that will be used when no other command is matched.\n\n```js\nconst cli = require('cac')()\n\ncli\n  // Simply omit the command name, just brackets\n  .command('[...files]', 'Build files')\n  .option('--minimize', 'Minimize output')\n  .action((files, options) => {\n    console.log(files)\n    console.log(options.minimize)\n  })\n\ncli.parse()\n```\n\n### Supply an array as option value\n\n```bash\nnode cli.js --include project-a\n# The parsed options will be:\n# { include: 'project-a' }\n\nnode cli.js --include project-a --include project-b\n# The parsed options will be:\n# { include: ['project-a', 'project-b'] }\n```\n\n### Error Handling\n\nTo handle command errors globally:\n\n```js\ntry {\n  // Parse CLI args without running the command\n  cli.parse(process.argv, { run: false })\n  // Run the command yourself\n  // You only need `await` when your command action returns a Promise\n  await cli.runMatchedCommand()\n} catch (error) {\n  // Handle error here..\n  // e.g.\n  // console.error(error.stack)\n  // process.exit(1)\n}\n```\n\n### With TypeScript\n\nFirst you need `@types/node` to be installed as a dev dependency in your project:\n\n```bash\nyarn add @types/node --dev\n```\n\nThen everything just works out of the box:\n\n```js\nconst { cac } = require('cac')\n// OR ES modules\nimport { cac } from 'cac'\n```\n\n### With Deno\n\n```ts\nimport { cac } from 'https://unpkg.com/cac/mod.ts'\n\nconst cli = cac('my-program')\n```\n\n## Projects Using CAC\n\nProjects that use **CAC**:\n\n- [VuePress](https://github.com/vuejs/vuepress): :memo: Minimalistic Vue-powered static site generator.\n- [SAO](https://github.com/egoist/sao): ⚔️ Futuristic scaffolding tool.\n- [DocPad](https://github.com/docpad/docpad): 🏹 Powerful Static Site Generator.\n- [Poi](https://github.com/egoist/poi): ⚡️ Delightful web development.\n- [bili](https://github.com/egoist/bili): 🥂 Schweizer Armeemesser for bundling JavaScript libraries.\n- [Lad](https://github.com/ladjs/lad): 👦 Lad scaffolds a Koa webapp and API framework for Node.js.\n- [Lass](https://github.com/lassjs/lass): 💁🏻 Scaffold a modern package boilerplate for Node.js.\n- [Foy](https://github.com/zaaack/foy): 🏗 A lightweight and modern task runner and build tool for general purpose.\n- [Vuese](https://github.com/vuese/vuese): 🤗 One-stop solution for vue component documentation.\n- [NUT](https://github.com/nut-project/nut): 🌰 A framework born for microfrontends\n- Feel free to add yours here...\n\n## References\n\n**💁 Check out [the generated docs](https://cac-api-doc.egoist.sh/classes/_cac_.cac.html) from source code if you want a more in-depth API references.**\n\nBelow is a brief overview.\n\n### CLI Instance\n\nCLI instance is created by invoking the `cac` function:\n\n```js\nconst cac = require('cac')\nconst cli = cac()\n```\n\n#### cac(name?)\n\nCreate a CLI instance, optionally specify the program name which will be used to display in help and version message. When not set we use the basename of `argv[1]`.\n\n#### cli.command(name, description, config?)\n\n- Type: `(name: string, description: string) => Command`\n\nCreate a command instance.\n\nThe option also accepts a third argument `config` for additional command config:\n\n- `config.allowUnknownOptions`: `boolean` Allow unknown options in this command.\n- `config.ignoreOptionDefaultValue`: `boolean` Don't use the options's default value in parsed options, only display them in help message.\n\n#### cli.option(name, description, config?)\n\n- Type: `(name: string, description: string, config?: OptionConfig) => CLI`\n\nAdd a global option.\n\nThe option also accepts a third argument `config` for additional option config:\n\n- `config.default`: Default value for the option.\n- `config.type`: `any[]` When set to `[]`, the option value returns an array type. You can also use a conversion function such as `[String]`, which will invoke the option value with `String`.\n\n#### cli.parse(argv?)\n\n- Type: `(argv = process.argv) => ParsedArgv`\n\n```ts\ninterface ParsedArgv {\n  args: string[]\n  options: {\n    [k: string]: any\n  }\n}\n```\n\nWhen this method is called, `cli.rawArgs` `cli.args` `cli.options` `cli.matchedCommand` will also be available.\n\n#### cli.version(version, customFlags?)\n\n- Type: `(version: string, customFlags = '-v, --version') => CLI`\n\nOutput version number when `-v, --version` flag appears.\n\n#### cli.help(callback?)\n\n- Type: `(callback?: HelpCallback) => CLI`\n\nOutput help message when `-h, --help` flag appears.\n\nOptional `callback` allows post-processing of help text before it is displayed:\n\n```ts\ntype HelpCallback = (sections: HelpSection[]) => void\n\ninterface HelpSection {\n  title?: string\n  body: string\n}\n```\n\n#### cli.outputHelp()\n\n- Type: `() => CLI`\n\nOutput help message.\n\n#### cli.usage(text)\n\n- Type: `(text: string) => CLI`\n\nAdd a global usage text. This is not used by sub-commands.\n\n### Command Instance\n\nCommand instance is created by invoking the `cli.command` method:\n\n```js\nconst command = cli.command('build [...files]', 'Build given files')\n```\n\n#### command.option()\n\nBasically the same as `cli.option` but this adds the option to specific command.\n\n#### command.action(callback)\n\n- Type: `(callback: ActionCallback) => Command`\n\nUse a callback function as the command action when the command matches user inputs.\n\n```ts\ntype ActionCallback = (\n  // Parsed CLI args\n  // The last arg will be an array if it's a variadic argument\n  ...args: string | string[] | number | number[]\n  // Parsed CLI options\n  options: Options\n) => any\n\ninterface Options {\n  [k: string]: any\n}\n```\n\n#### command.alias(name)\n\n- Type: `(name: string) => Command`\n\nAdd an alias name to this command, the `name` here can't contain brackets.\n\n#### command.allowUnknownOptions()\n\n- Type: `() => Command`\n\nAllow unknown options in this command, by default CAC will log an error when unknown options are used.\n\n#### command.example(example)\n\n- Type: `(example: CommandExample) => Command`\n\nAdd an example which will be displayed at the end of help message.\n\n```ts\ntype CommandExample = ((name: string) => string) | string\n```\n\n#### command.usage(text)\n\n- Type: `(text: string) => Command`\n\nAdd a usage text for this command.\n\n### Events\n\nListen to commands:\n\n```js\n// Listen to the `foo` command\ncli.on('command:foo', () => {\n  // Do something\n})\n\n// Listen to the default command\ncli.on('command:!', () => {\n  // Do something\n})\n\n// Listen to unknown commands\ncli.on('command:*', () => {\n  console.error('Invalid command: %s', cli.args.join(' '))\n  process.exit(1)\n})\n```\n\n## FAQ\n\n### How is the name written and pronounced?\n\nCAC, or cac, pronounced `C-A-C`.\n\nThis project is dedicated to our lovely C.C. sama. Maybe CAC stands for C&C as well :P\n\n<img src=\"http://i.giphy.com/v3FeH4swox9mg.gif\" width=\"400\"/>\n\n### Why not use Commander.js?\n\nCAC is very similar to Commander.js, while the latter does not support dot nested options, i.e. something like `--env.API_SECRET foo`. Besides, you can't use unknown options in Commander.js either.\n\n_And maybe more..._\n\nBasically I made CAC to fulfill my own needs for building CLI apps like [Poi](https://poi.js.org), [SAO](https://sao.vercel.app) and all my CLI apps. It's small, simple but powerful :P\n\n## Project Stats\n\n![Alt](https://repobeats.axiom.co/api/embed/58caf6203631bcdb9bbe22f0728a0af1683dc0bb.svg 'Repobeats analytics image')\n\n## Contributing\n\n1. Fork it!\n2. Create your feature branch: `git checkout -b my-new-feature`\n3. Commit your changes: `git commit -am 'Add some feature'`\n4. Push to the branch: `git push origin my-new-feature`\n5. Submit a pull request :D\n\n## Author\n\n**CAC** © [EGOIST](https://github.com/egoist), Released under the [MIT](./LICENSE) License.<br>\nAuthored and maintained by egoist with help from contributors ([list](https://github.com/cacjs/cac/contributors)).\n\n> [Website](https://egoist.sh) · GitHub [@egoist](https://github.com/egoist) · Twitter [@\\_egoistlily](https://twitter.com/_egoistlily)\n", "maintainers": [{"name": "kchan", "email": "<EMAIL>"}, {"name": "egoist", "email": "<EMAIL>"}, {"name": "rem", "email": "<EMAIL>"}], "time": {"modified": "2023-07-09T22:05:04.734Z", "created": "2016-01-14T10:28:23.556Z", "0.0.0": "2016-01-14T10:28:23.556Z", "0.1.0": "2016-04-28T15:53:50.247Z", "1.0.0": "2016-04-29T09:07:37.099Z", "2.0.0": "2016-08-28T09:20:17.164Z", "2.0.1": "2016-08-28T09:54:16.886Z", "2.0.2": "2016-08-28T10:15:07.582Z", "2.0.3": "2016-08-28T13:10:22.171Z", "2.1.0": "2016-08-28T13:47:23.249Z", "2.1.1": "2016-08-28T14:03:52.504Z", "2.2.0": "2016-08-29T02:46:15.515Z", "2.2.1": "2016-08-29T10:33:01.309Z", "2.2.2": "2016-09-02T05:37:42.844Z", "2.3.0": "2016-09-04T09:12:04.987Z", "2.3.1": "2016-09-15T06:39:31.809Z", "2.3.2": "2016-09-20T11:43:08.137Z", "3.0.0": "2016-10-27T15:16:46.536Z", "3.0.1": "2016-10-27T15:51:19.664Z", "3.0.2": "2016-10-27T18:38:02.232Z", "3.0.3": "2016-11-01T07:22:18.110Z", "3.0.4": "2016-11-23T16:37:18.776Z", "4.0.0": "2017-07-26T08:32:40.851Z", "4.1.0": "2017-07-28T14:45:06.465Z", "4.1.1": "2017-07-28T15:29:17.226Z", "4.2.0": "2017-07-29T14:28:18.205Z", "4.2.1": "2017-09-03T01:25:42.620Z", "4.2.2": "2017-09-24T02:54:45.191Z", "4.2.3": "2017-10-18T15:12:16.764Z", "4.2.4": "2017-10-25T15:18:45.259Z", "4.3.0": "2017-12-28T07:42:05.801Z", "4.3.1": "2017-12-30T04:05:47.784Z", "4.3.2": "2018-01-11T08:42:47.003Z", "4.3.3": "2018-01-11T09:09:51.283Z", "4.3.4": "2018-01-11T09:11:00.790Z", "4.3.5": "2018-01-29T07:48:55.388Z", "4.3.6": "2018-01-29T07:54:50.805Z", "4.3.7": "2018-01-31T06:45:51.924Z", "4.4.0": "2018-01-31T14:19:08.635Z", "4.4.1": "2018-01-31T14:44:20.842Z", "4.4.2": "2018-03-10T13:09:02.957Z", "4.4.3": "2018-03-10T17:45:32.618Z", "4.4.4": "2018-03-20T15:48:41.981Z", "5.0.0": "2018-04-18T06:16:18.562Z", "5.0.1": "2018-04-18T06:24:17.855Z", "5.0.2": "2018-04-18T06:34:18.386Z", "5.0.3": "2018-04-18T06:44:17.276Z", "5.0.4": "2018-04-18T06:47:57.449Z", "5.0.5": "2018-04-18T06:59:30.683Z", "5.0.6": "2018-04-18T09:24:05.683Z", "5.0.7": "2018-04-18T09:43:58.380Z", "5.0.8": "2018-04-21T06:49:24.329Z", "5.0.9": "2018-04-21T06:53:48.414Z", "5.0.10": "2018-04-21T09:57:44.543Z", "5.0.11": "2018-09-07T08:42:15.835Z", "5.0.12": "2018-09-11T03:54:58.585Z", "5.0.13": "2018-10-11T04:54:10.739Z", "5.0.14": "2018-10-25T06:59:16.812Z", "5.0.15": "2018-11-02T04:53:47.896Z", "5.0.16": "2018-11-24T14:53:33.694Z", "6.0.0": "2018-11-25T14:30:36.383Z", "6.0.1": "2018-11-25T15:12:01.754Z", "6.0.2": "2018-11-25T16:12:42.177Z", "6.1.0": "2018-11-26T03:44:49.429Z", "6.1.1": "2018-11-26T05:24:47.600Z", "6.1.2": "2018-11-26T05:42:44.355Z", "6.1.3": "2018-11-26T05:59:57.586Z", "6.2.0": "2018-11-26T08:17:49.320Z", "6.2.1": "2018-11-26T08:55:32.773Z", "6.2.2": "2018-11-26T10:08:02.589Z", "6.2.3": "2018-11-26T10:12:43.745Z", "6.2.4": "2018-11-26T10:39:12.394Z", "6.2.5": "2018-11-26T11:34:24.054Z", "6.2.6": "2018-11-26T14:07:00.197Z", "6.3.0": "2018-11-27T04:51:54.442Z", "6.3.1": "2018-11-27T05:32:38.680Z", "6.3.2": "2018-11-27T07:14:40.675Z", "6.3.3": "2018-11-27T15:54:17.586Z", "6.3.4": "2018-11-28T09:40:01.818Z", "6.3.5": "2018-11-30T08:07:43.315Z", "6.3.6": "2018-11-30T10:17:37.397Z", "6.3.7": "2018-12-01T06:04:45.284Z", "6.3.8": "2018-12-01T06:08:04.400Z", "6.3.9": "2018-12-02T07:12:42.027Z", "6.3.10": "2018-12-10T15:04:09.446Z", "6.3.11": "2018-12-23T09:21:16.094Z", "6.3.12": "2018-12-25T13:36:18.065Z", "6.4.0": "2019-01-10T08:48:48.384Z", "6.4.1": "2019-01-26T13:08:00.233Z", "6.4.2": "2019-01-26T13:27:42.116Z", "6.4.3": "2019-04-09T06:57:18.423Z", "6.5.0": "2019-04-14T13:44:44.709Z", "6.5.1": "2019-04-14T13:44:58.713Z", "6.5.2": "2019-06-05T15:07:52.483Z", "6.5.3": "2019-09-10T13:07:35.378Z", "6.5.4": "2020-01-13T13:20:24.145Z", "6.5.5": "2020-01-15T14:32:27.269Z", "6.5.6": "2020-01-27T10:10:21.327Z", "6.5.7": "2020-03-06T08:35:44.005Z", "6.5.8": "2020-03-20T05:28:38.283Z", "6.5.9": "2020-05-14T06:52:16.455Z", "6.5.10": "2020-05-20T12:30:12.003Z", "6.5.11": "2020-07-10T15:16:45.440Z", "6.5.12": "2020-07-10T15:24:12.934Z", "6.5.13": "2020-07-12T14:56:33.234Z", "6.6.0": "2020-07-14T17:42:31.116Z", "6.6.1": "2020-07-15T05:00:37.721Z", "6.7.0": "2020-12-11T16:58:02.753Z", "6.7.1": "2020-12-11T17:03:32.507Z", "6.7.2": "2021-02-17T06:22:43.607Z", "6.7.3": "2021-04-22T12:28:24.539Z", "6.7.4": "2021-10-06T12:23:18.051Z", "6.7.5": "2021-10-06T12:38:25.218Z", "6.7.6": "2021-10-06T13:15:56.900Z", "6.7.7": "2021-10-06T13:33:47.577Z", "6.7.8": "2021-10-08T14:16:41.432Z", "6.7.9": "2021-10-12T08:16:05.287Z", "6.7.10": "2021-10-12T09:46:44.399Z", "6.7.11": "2021-10-12T12:15:16.569Z", "6.7.12": "2021-11-12T01:34:59.011Z", "6.7.13": "2022-08-29T02:00:45.069Z", "6.7.14": "2022-08-29T03:02:03.481Z"}, "repository": {"url": "git+https://github.com/egoist/cac.git", "type": "git"}, "author": {"name": "egoist", "email": "<EMAIL>"}, "license": "MIT", "readmeFilename": "README.md", "users": {"kchan": true, "demian_dark": true, "davidbwaters": true, "soenkekluth": true, "flumpus-dev": true}, "homepage": "https://github.com/egoist/cac#readme", "bugs": {"url": "https://github.com/egoist/cac/issues"}}