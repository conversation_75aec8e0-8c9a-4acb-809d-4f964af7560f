{"_id": "universalify", "_rev": "14-fbae87ac1caa02b41666ea1b57cf19a5", "name": "universalify", "description": "Make a callback- or promise-based function support both promises and callbacks.", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.0.1": {"name": "universalify", "version": "0.0.1", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "gitHead": "131e6f2b86511f880e04e5995e7a8db468e5bc24", "_id": "universalify@0.0.1", "_shasum": "937b1b69cecc2f27e27395428eb7952bc7939617", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "dist": {"shasum": "937b1b69cecc2f27e27395428eb7952bc7939617", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.0.1.tgz", "integrity": "sha512-1L8JNmaIuH/QHyT0Rc3+ba62M0fR/agg5EF+CXM2m9O0OzP/oRjYa1NBSzb7NLke+bIT0C2JEGNlF4Fe9rWi8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEB8OlPdjNcq6ME+aDL/UkF/LuaQUrGPIqY9/nJuo6PbAiEA+wo8970D7lckB0xKUNP3zq20qzRgmHr2bRXlICvsIwY="}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/universalify-0.0.1.tgz_1491672518379_0.970205559860915"}, "directories": {}}, "0.1.0": {"name": "universalify", "version": "0.1.0", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "gitHead": "003666ace34712273c8dfb27b7a92b1112573f15", "_id": "universalify@0.1.0", "_shasum": "9eb1c4651debcc670cc94f1a75762332bb967778", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "dist": {"shasum": "9eb1c4651debcc670cc94f1a75762332bb967778", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.1.0.tgz", "integrity": "sha512-fYHpatg8wmx63V78K3cA10Xo1bXeFZB0m18BkXjs7buvgq/Ew96SX8wPIIl/qUuEOo5L1/KJ7nfbUlMtAHmbPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDej6jV2fZvWCJyndO6JjKIwudCmPXVKI8a3ykdmq36uAiBxvZ3wKOrm6Xf9j2ERkyrfPr67Y/pLv/BDxhbJomRy+Q=="}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/universalify-0.1.0.tgz_1492871560174_0.5206800866872072"}, "directories": {}}, "0.1.1": {"name": "universalify", "version": "0.1.1", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "gitHead": "6dcade29ad1fc945f3d2a6e63cf92ec041fa83d2", "_id": "universalify@0.1.1", "_shasum": "fa71badd4437af4c148841e3b3b165f9e9e590b7", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "dist": {"shasum": "fa71badd4437af4c148841e3b3b165f9e9e590b7", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.1.1.tgz", "integrity": "sha512-MVi79HEPwGk0grI7/Kl6H51fX7wcDTe6gGoCdK22pkRG6IPsi9L6NltClWJfBLUoIE5y3pKy3SplFAs/b0G+QQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDP7RD6l8fSzQ2o4Wejwu/WVVuXKZozpK4UAvHFd3DbnAIgQ8VVGpB0U/7eqa//vyCTto5O5xGimzqk2bo5xEDlwqg="}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify-0.1.1.tgz_1500561668015_0.5723595882300287"}, "directories": {}}, "0.1.2": {"name": "universalify", "version": "0.1.2", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "ab4b5cd7e962c4915da68bdf0e5b8f9aa677f8f6", "_id": "universalify@0.1.2", "_npmVersion": "5.8.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==", "shasum": "b646f69be3942dabcecc9d6639c80dc105efaa66", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "fileCount": 4, "unpackedSize": 4705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKje3CRA9TVsSAnZWagAAzfAP/2cu0REZCOFPwB1oH6Cx\n6X0kZDIlx4BiR2hrB/vqUi5qk3Qdqz8oPdpFpfeOgZAQeYXtxgysvjoc64eM\n07sOBQD48/Vu2dkmNKyA8WHTqQoKgIGOE2adcx6OSuxmbmoXBSw1a4BZLapd\nRTIrJ6k1YzmpYfTI4TeIPeax72gcRkESN2C/als++gAja9FzeQtR3khi+tX4\n81iqOb3HZn3Z5+Md15e4jx+ipsIOrta603D1BceBBpcM2fwTvptQiPcvurjX\nKFZfb0s8LwFOTKNFbhi1x5162wEpEBXud2ojJaf3JaUQj/6p5yEzh4rgTpSS\n0ls1QPOv172+xubTCx3Met0ZPn48DWMBN6zXEBWKvnrGVdu3QcYQqzBPlHQ3\nXl2OsAJqIJu0V0kTQJNV26gOf/ng5aobum6kq1+1ZrOFFX6AxtiFzna5Nvti\nPGIvazjovq2+MSk+felrDoP4DKfFSyU4A+6BRSfZotKnw9iJcXX7X8pwseOG\njE32ATbOu1wG2dOZNSHB9aju1uxQ/jcMRVzF6c1TtvJC1oSQuPNVGX7g+s3b\ng7AlmQhPf234Q+ogyJoAZRp8p68v6WNv/ubV3bZm8odzjGkIe5S7Tb6jV0OR\nMKH8bbN6dKIdKTHET7xcrmIQKA+khuJaxWCgms3CpVqJqEkU/0KZQVZlfalL\nC+UO\r\n=qMZi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIFJJlBb3NMu2yzCbLL8SN4SGcgEBKNQU2q+ce85GngAiEA48VjFyzEF1Gi8BP9hc29Bp+LerBkjRDnbim4EJNUavs="}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify_0.1.2_1529493431283_0.2533941036533853"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "universalify", "version": "0.2.0", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^10.2.0", "standard": "^10.0.1", "tape": "^4.6.3"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "4342aa98a8801428ac66319c956443c0bfb0ce7e", "_id": "universalify@0.2.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==", "shasum": "6451760566fa857534745ab1dde952d1b1761be0", "tarball": "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz", "fileCount": 4, "unpackedSize": 4787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTWFOCRA9TVsSAnZWagAAzaAP/1Hy5aD8x+vNprh3nhXz\n9G9xfPyYCmeDLGjJq+1YSpcbRb+ysv/E0jLb3D2CvVtda4rtIeMRPeI7yFS6\n0OpaLg5S2Vc8DLCq1XHeY/k/l7hIeV0/x39mk2WAgoAEC58YDSOhJqkrl1p9\nMj0BtoX3198IvcWo55h4g1vq78zNBwvoqQwyh2vGAkXKNOuYEEaZxBhVsSTQ\nJL3l23OCNiKVjMZn28g7LUDX8XYX5U8rlIZG6MTObvk3wd1FXD+lYGy7ynAn\nsXTqIeZfRPIHwzlG575CbqnOTi7bqS5g6tsG+EEhUjbu9BMJsLTjR39v8A9K\nA2vNrgf8oMGW4P1s7Sn0N1gQ0xpXI/aHIh5WEBmfciEMJLTM6ACFbaryrCJR\nbj8SsfHbh9YsFuzQh5wTei0RYmnZw87MAIqNUBoRSL3Kw2rgcMlDNDDQr2o0\n0KZ6sy1NZhOUNQtBMbecynZ6GUnIkaSfm5RhG8KVbIXOl5MitQB8o62uaO1Z\n0Ap18RBusQzj86C0fDyqLXJ5y3dA8EgIHm3F0zGrMXo4VCmI5S5z+VQGyqWH\nfroq/vxTKJyVizIGmt0BPD/x7AJOSKdEGZgTKW8O23NOHQ7u57kVMhH3T64G\nRQk3L13YwLsTEkWrdKfWXIiBt1uQoaNaI9QCbrQt3IyaMKUw7TmDhD7u2QPG\nWpFK\r\n=D1xp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8/gfmFTBj8Snx4yrqso64v51SSGhbaTiyfR38wvVv3AIhAMhopDxUdsXylZvq/iwwNDw4iBAoBsFGSW9o1zNGSNL4"}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify_0.2.0_1582129485961_0.19346377093010547"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "universalify", "version": "1.0.0", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^15.0.0", "standard": "^14.3.1", "tape": "^4.6.3"}, "engines": {"node": ">= 10.0.0"}, "gitHead": "17c5efbc1bc96c201d8b0b116d43f7b5e2cad297", "_id": "universalify@1.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-rb6X1W158d7pRQBg5gkR8uPaSfiids68LTJQYOtEUhoJUWBdaQHsuT/EUduxXYxcrt4r5PJ4fuHW1MHT6p0qug==", "shasum": "b61a1da173e8435b2fe3c67d29b9adf8594bd16d", "tarball": "https://registry.npmjs.org/universalify/-/universalify-1.0.0.tgz", "fileCount": 4, "unpackedSize": 4626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYsMsCRA9TVsSAnZWagAA+CIQAI8Mr8UmJvqRPuZ2keSr\n004jky2VGkYB8X+uwvvyg5CtEqyX39HSIn8HP2wKuTYV4dZ+Azo8Z8v9Wsow\nI+vdRmzYP8yNMggk0sUCYQ/QBShmZo4m/VPsjWkbo8oS5f85Eg2vmjLRh7qg\nDY6lu7E9CDpizHVff9bzTZ+SS2M24+/aF0Y6NhhyTCInh5OutQgODLMmNELG\nb18ROSoyse9Yf0cYOplQTIZduihAeswLnJNOoDrmlasa1B0mwrkspQwC5CXd\nECe0X2Uj6zou02tsrOkPvMsPayoGwP7+zVwM3uxDijLyEzt4rukveOmaVRCI\nO1F+WE0816ivAPh2Ub2Zngkjo6F2LIpiZnri164WqDarrRP8+xPNPD/ayR0G\nZTv0WFi8VfED4keYBiJMae4Y75lTDqGBGA3qnkp20UJ8XYi9xBIvPH7lwV3h\nlOo3i/t0hVw7quZOamZkVSNzA1Av5UxPBeoBtDWp9bkvrSu9VvMck9q+r6nS\nZPjAdd1n6+Mp5P/nQobY3d0pnuuAtVR8hDQ8A77hxjTBs+PMA0TEM+1FYH2z\n0EG5siMX5KYVxzm7J7Udz+LF3mStUriR2WczVBp1Jow6LEJHUayd7pj6iH4f\nvRTdtZwC5Us3Mi+/Dgwf8P/+GY1HzB6fZsMCmXzgVUugydS6/hq8kvfK3f7m\naz8X\r\n=c/nI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvdqdzukTONEe8KCmVilWGBlq2ggSe4B7uDr2M/4wplAIgEWtUzSY44ulVWnJql/+VP0hDOyhCJbSYHeIYVoO4bLs="}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify_1.0.0_1583530796485_0.383723721188459"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "universalify", "version": "2.0.0", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^15.0.0", "standard": "^14.3.1", "tape": "^5.0.1"}, "engines": {"node": ">= 10.0.0"}, "gitHead": "a853a4aedc63c69fcdc62b77643d75b0d162a098", "_id": "universalify@2.0.0", "_nodeVersion": "14.0.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==", "shasum": "75a4984efedc4b08975c5aeb73f530d02df25717", "tarball": "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz", "fileCount": 4, "unpackedSize": 4639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHDUNCRA9TVsSAnZWagAAYNEP/RZVxoo7bLXWTTzcZmi6\nVwER5sd6QoPf/Q1mWa5sBFTIUFivbm/NNFZo891lRwAFovDcR14390HNF8yz\n7U+YtOOhBDV1NsLonz1OHvvde0ucqz+fcEG3zBI3MWOP/BNgAo1PXYINjKmi\nwGk+sSBzrM+ZFo6LNfhQJ9aK2LXxNjP7YhshBfQW5Y7cElnpzCRrvdx42fZR\nYXfn1Av1Y36woZ48PB9nfgLJiLRJEzr+BFbuGlwdd/FNzXAmoH5cEmZkPYeP\nXbqhswpWOHuee4RVRkSRsPMcbpo/WemxmHrMxuG49Hcqe6rXrUEWZVU7FgJt\n1U5KOT4W5IbSuqNIL222cyDV62Tw1yhc1Y4A5LhhggI6n0aRAiNFbn9YTJU1\nN0U2ARxHYfnwLI623nLluLZvYjDGyMO6XpiCOAp+T2scbtO/T7uudsd4FP6+\nAd3A7LPQsvXdlv9FtRN941tdkVQ9AN4ZYFtrv3+2m1NpkwUnqUGlRugJlUb5\n7bn6Dd1HLU92xnk0K84tb3nQ+fNjXA/fJmO0yDuSJRMhamJ1gyrY/W7rsh8C\nwWdHWQWxVfFSvvL4pA4ZZb9TaZhNIPwB0/NRTLay1HQKm4k0+U0dVGeyPkmh\n37LjowAQrzwA4rXTwDNiBPmNvTlT8xiPQifRjiNZb7ffNiPOtrxVoO15wBw6\n5Hym\r\n=lhr8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfzHvKe3Ujr8OkM/r7gRIC8lRqcO8zDF9nVeIBguVDxwIhAMVxBOPq6uoO7pcsx5yA7K5aiqunDvGAjtKmqxZR5LGB"}]}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify_2.0.0_1595684108772_0.826098899436974"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "universalify", "version": "2.0.1", "description": "Make a callback- or promise-based function support both promises and callbacks.", "keywords": ["callback", "native", "promise"], "homepage": "https://github.com/RyanZim/universalify#readme", "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "scripts": {"test": "standard && nyc --reporter text --reporter lcovonly tape test/*.js | colortape"}, "devDependencies": {"colortape": "^0.1.2", "coveralls": "^3.0.1", "nyc": "^15.0.0", "standard": "^14.3.1", "tape": "^5.0.1"}, "engines": {"node": ">= 10.0.0"}, "gitHead": "dc17e0e00fb39c8d52e97ce77e494cdadfa8d19c", "_id": "universalify@2.0.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "shasum": "168efc2180964e6386d061e094df61afe239b18d", "tarball": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "fileCount": 4, "unpackedSize": 4675, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUFWZO9ik+8IH55REhY6tJ/JfNYwYpdKIM3FXV7NX/7AiEAnLH4+tAcL1mGyIz4gVmPJVA16jBN25Z/HykKLkZFn84="}]}, "_npmUser": {"name": "ryanzim", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/universalify_2.0.1_1698858765140_0.22716497055102036"}, "_hasShrinkwrap": false}}, "readme": "# universalify\n\n![GitHub Workflow Status (branch)](https://img.shields.io/github/actions/workflow/status/RyanZim/universalify/ci.yml?branch=master)\n![Coveralls github branch](https://img.shields.io/coveralls/github/RyanZim/universalify/master.svg)\n![npm](https://img.shields.io/npm/dm/universalify.svg)\n![npm](https://img.shields.io/npm/l/universalify.svg)\n\nMake a callback- or promise-based function support both promises and callbacks.\n\nUses the native promise implementation.\n\n## Installation\n\n```bash\nnpm install universalify\n```\n\n## API\n\n### `universalify.fromCallback(fn)`\n\nTakes a callback-based function to universalify, and returns the universalified  function.\n\nFunction must take a callback as the last parameter that will be called with the signature `(error, result)`. `universalify` does not support calling the callback with three or more arguments, and does not ensure that the callback is only called once.\n\n```js\nfunction callbackFn (n, cb) {\n  setTimeout(() => cb(null, n), 15)\n}\n\nconst fn = universalify.fromCallback(callbackFn)\n\n// Works with Promises:\nfn('Hello World!')\n.then(result => console.log(result)) // -> Hello World!\n.catch(error => console.error(error))\n\n// Works with Callbacks:\nfn('Hi!', (error, result) => {\n  if (error) return console.error(error)\n  console.log(result)\n  // -> Hi!\n})\n```\n\n### `universalify.fromPromise(fn)`\n\nTakes a promise-based function to universalify, and returns the universalified  function.\n\nFunction must return a valid JS promise. `universalify` does not ensure that a valid promise is returned.\n\n```js\nfunction promiseFn (n) {\n  return new Promise(resolve => {\n    setTimeout(() => resolve(n), 15)\n  })\n}\n\nconst fn = universalify.fromPromise(promiseFn)\n\n// Works with Promises:\nfn('Hello World!')\n.then(result => console.log(result)) // -> Hello World!\n.catch(error => console.error(error))\n\n// Works with Callbacks:\nfn('Hi!', (error, result) => {\n  if (error) return console.error(error)\n  console.log(result)\n  // -> Hi!\n})\n```\n\n## License\n\nMIT\n", "maintainers": [{"name": "ryanzim", "email": "<EMAIL>"}], "time": {"modified": "2023-11-01T17:12:45.559Z", "created": "2017-04-08T17:28:40.301Z", "0.0.1": "2017-04-08T17:28:40.301Z", "0.1.0": "2017-04-22T14:32:41.242Z", "0.1.1": "2017-07-20T14:41:09.194Z", "0.1.2": "2018-06-20T11:17:11.369Z", "0.2.0": "2020-02-19T16:24:46.080Z", "1.0.0": "2020-03-06T21:39:56.588Z", "2.0.0": "2020-07-25T13:35:08.897Z", "2.0.1": "2023-11-01T17:12:45.392Z"}, "homepage": "https://github.com/RyanZim/universalify#readme", "keywords": ["callback", "native", "promise"], "repository": {"type": "git", "url": "git+https://github.com/RyanZim/universalify.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/RyanZim/universalify/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"heartnett": true, "larrychen": true, "arniu": true, "xiatian": true}}