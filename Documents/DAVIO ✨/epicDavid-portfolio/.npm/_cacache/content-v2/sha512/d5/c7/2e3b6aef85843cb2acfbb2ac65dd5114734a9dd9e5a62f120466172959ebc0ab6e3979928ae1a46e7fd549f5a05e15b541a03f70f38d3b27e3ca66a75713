{"_id": "fast-le<PERSON><PERSON><PERSON>", "_rev": "53-e6bb9791c1d75ace1601456b6c7a5c38", "name": "fast-le<PERSON><PERSON><PERSON>", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.0", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.0", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "dist": {"shasum": "0370eda31cff867c88745cdb89921e4c76ae8393", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.0.tgz", "integrity": "sha512-hx0D818+r2dQtEDGYWwyO5UTXpalnBh5B+93Udhb+J4TEdP1Cil15VDgLf2f5oS8gbA+B6N9I5Mr1oeSW9Yz1A==", "signatures": [{"sig": "MEYCIQCTNmf6iMIYXCOTWonNQdVIbaJF9C72hAPZNV2wrMMHEAIhAPdJDTCKOfi/VGjfBHY0mTteK1cbrBDWl7KVBllpypTa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.1.49", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {"test": "test"}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-mocha-test": "~0.2.2", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.1": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.1", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.1", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "0548ef9dd6f2578b1ad22dc64515807eba2247b5", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.1.tgz", "integrity": "sha512-NP9Rw24KNmDXupnfoiGU+enA0upReUTVjt1jL8yrCd+vDup7lAlYjHTvWThjxDdpXZLshSJUqEL/wFGSDBh6rw==", "signatures": [{"sig": "MEYCIQCH6K7iGbxlfHLkOiFI1btvNE0kzXXYHxn6EnHbH98BLAIhAJx5yij7LCpcb0faYGEhx1ENwtWdIuZpZom2MUg+Wn7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.3.22", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.2", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.2", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "cf564134f8339d92125a7b2634aab2a885d7df39", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.2.tgz", "integrity": "sha512-429sQelDI9wPYju5wVmeYLtuZHeI5i5/AgaeORQ1GA0NjWqvQiEsfPagsJMvaELXCLtDMtcZIiJu3OxHWz8qhw==", "signatures": [{"sig": "MEQCIAD4+Qfd4D5uD65wWEiFXOX4MBJQksfl3W97lcO0emw2AiBGH1TAKLWM/jWWx2yrsiB2jEtMjO1Zvf2miHHydBzGag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.3.22", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.3", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.3", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "fcee403eaa08374e9028b8bd11d09a4d2f75e62b", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.3.tgz", "integrity": "sha512-DpHWl+dZY8EJfPMdPHqQyBC5dn3YDx03WMg72m0ixzCFlwDihdVSyluv8c73CU3ly/poEY0GZvGmGSheQ6Bd3w==", "signatures": [{"sig": "MEUCIHHa1WuC2arzMagQcqRYwz4QZCAR58FDSrPdDn3qo6uwAiEAntojrR6XFPlanfA1uRO6jbpx/QqWoX3KObP1K6DagUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.3.22", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.4", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.4", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "4d48aabd04a9e5790b2851f93ea84cf4634eeb0c", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.4.tgz", "integrity": "sha512-q0Xdux1nM3eiKoCw4bsJFN+AzS2m2iGZDg+a95nv5tOunxkZlkbYKTLukf8R1F+5bXbOPRA6xCmX3JrIi7qQzA==", "signatures": [{"sig": "MEUCIQCtsBpjOA+sBWhLYTa/aQZFg+z4TtXiyUAA5h4LbV1PoAIgEzmnFO9MnnLI/l8wkwm6N1odEgMHRrwVx3gQKTSpsiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "_shasum": "4d48aabd04a9e5790b2851f93ea84cf4634eeb0c", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.5": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.5", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.5", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "d21e2fa0a784a4d2147476df5b8b5048f9d67a5d", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.5.tgz", "integrity": "sha512-C<PERSON><PERSON>RUG93XcJcp1Do6neSU1T58ndMGP7sRGLYVO32HvWmOeAAEqp18vU3Sn0h91NHxgunC9HUWUqkxCzKAq3mQ==", "signatures": [{"sig": "MEYCIQDpNoUiswTiRdixHb2V78RbVafSjD8EXPoJT9DCKKS1dwIhAP591welkK2lqHSCCpaBTPkToCMsuBBriwtKOZ8iagWq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "_shasum": "d21e2fa0a784a4d2147476df5b8b5048f9d67a5d", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.6": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.6", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.6", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "3bedb184e39f95cb0d88928688e6b1ee3273446a", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.6.tgz", "integrity": "sha512-g+RPaM3i4gaTlGSIjItZI0jHGQvGeDzYsrKAuOq2KcgYcbEFOZWdV5DN5aEVnYSDdVwmTysS1EQyohsKwwKJiA==", "signatures": [{"sig": "MEQCICuWQwdcZucZL56T4yq068szW4WeVd00JUXK5ur2wlqzAiB0Fs0qQalWUHcC/pUTMROhHWKklBN8ESgAhvWrLGvEzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "_shasum": "3bedb184e39f95cb0d88928688e6b1ee3273446a", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.0.7": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.7", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.0.7", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "0178dcdee023b92905193af0959e8a7639cfdcb9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.7.tgz", "integrity": "sha512-hYsfI0s4lfQ2rHVFKXwAr/L/ZSbq9TZwgXtZqW7ANcn9o9GKvcbWxOnxx7jykXf/Ezv1V8TvaBEKcGK7DWKX5A==", "signatures": [{"sig": "MEYCIQCKU0ew+4UvqIASzRRdwAfnfAqBZGOrAO95+zAx8lrsDQIhAJKb52uFkmX3MasMuRvwx0wEBQF0sbhBr7/L9OQFhdUw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "0178dcdee023b92905193af0959e8a7639cfdcb9", "gitHead": "321ca56691c248823bbdb73b6fda57d6973f7f8c", "scripts": {"build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.1.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.0", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.1.0", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "87fa00416ee6b80fc326ca61993bce3b93974469", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.0.tgz", "integrity": "sha512-+BofjGJr1hVwh/qbt8tcLTYazQ9mo+7U38pyQmxwmpT+9+qlLAytiRVi43MO2LRbmeosPJtKUEdIx0iygpx6bQ==", "signatures": [{"sig": "MEYCIQDpx2rOzZNKvjAq85xuh3CNDjoYip++KH+gZ+iGZDmM3AIhAI3fQKyjJYeqCXX0Ih9/B9hv0bALz4ouaw+1Txd5yOP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "87fa00416ee6b80fc326ca61993bce3b93974469", "gitHead": "428c87160f18d7b6867a0d37cdebb44471658ed3", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "0.12.9", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": ">=3.0.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.1.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.2", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.1.2", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "5df059fc7a131a7471eff164e6884e5993f72777", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.2.tgz", "integrity": "sha512-lKf7jPz/vR+HbZwAzFySUP6Yy6WMNkvUh6cvmuq49ZAgarcixNMZ7oscrZXTr5Ucdq8g8+iAkQjETl/oqvRz/w==", "signatures": [{"sig": "MEUCIDiGkrpN8dntSy7zbLI4jUaVgwPDJTz9FWO7+A6fTr5fAiEA6ZYEd2/nOSS2EndRmIKTmVsD3/tUT3/UZMC/DOnrC00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "5df059fc7a131a7471eff164e6884e5993f72777", "gitHead": "0cd8ad5c8e23c219261f8cdde875e79ee4c06f3f", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "0.12.9", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.1.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.3", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.1.3", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "2ae7b32abc1e612da48a4e13849b888a2f61e7e9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.3.tgz", "integrity": "sha512-zd0Ci3bYxNv/UpEQfS0dLzBn25s2c60T7oX6j7WYH/mhe9+OXYGphb5ZVyJbqvJM713f7l9OAg/12fJZ8VNmdg==", "signatures": [{"sig": "MEQCIAu3QIqcccYZ4UK1NISDP5vnptCek+bPSBBzVTjOjLn1AiBZG4N2FvPcMKCR1XMyKX1xE8g7uyEeXedld3NBrGsBAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "2ae7b32abc1e612da48a4e13849b888a2f61e7e9", "gitHead": "bce911eb885fb52e37b1be793c22cfaf88d580d2", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "0.12.9", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}}, "1.1.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.4", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@1.1.4", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "e6a754cc8f15e58987aa9cbd27af66fd6f4e5af9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.4.tgz", "integrity": "sha512-Ia0sQNrMPXXkqVFt6w6M1n1oKo3NfKs+mvaV811Jwir7vAk9a6PVV9VPYf6X3BU97QiLEmuW3uXH9u87zDFfdw==", "signatures": [{"sig": "MEUCIGcBiTbrVkXeFWPHzKHqUW/8oPdn/D5qSiPGk5+rFcsWAiEAnfunHIgt1dAbY/rs7Xs+ZDYMSX0DOuGcR3YL4A2ZKDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "e6a754cc8f15e58987aa9cbd27af66fd6f4e5af9", "gitHead": "80874a9c0cd8d9f5aee9d73aa39d9ab0a9d51435", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-1.1.4.tgz_1469168840025_0.9637497058138251", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.0", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.0", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "0e833c3710f11982027e23963dc48c03c31a81b5", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.0.tgz", "integrity": "sha512-h9jLQGo+61esNmfWhgXeTDxwHx3Q8HrvIj1i6EmugXfRN1+FZk48L5AOsfA/Wh5yLATboWhTughjgqS/buEZQA==", "signatures": [{"sig": "MEUCIQCs+wh1STd9Si3B+c38eAx/K8Kf0tfZBEP5n2/PvTv5ZAIgU3kEWWIqT1p0dIa9N5GYecfmax9mLjGwOohpARkxEqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "0e833c3710f11982027e23963dc48c03c31a81b5", "gitHead": "c69f41f8c41b34cb53013afa7f82bbca98849149", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.0.tgz_1472192613292_0.7607214960735291", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.1": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.1", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.1", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "3fa8823203529e8453f2ed8f2852bd2a6db182a6", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.1.tgz", "integrity": "sha512-3AD61Yz2v2ehvRQxnpHozUZmPNlYHu5DiHcbF1OXx7Krholt3OkLZEhYR731S0i90lB+k4vhPrBRGy5SZIzJnQ==", "signatures": [{"sig": "MEUCIQC1NiboFo6qo7xPVTnKh7VpkULVWsdM2dEEc6uEc7+fhwIgRNJoZ5qunlv8Sq+bTZ77+9buSiahQgEsKzFG4JC1c9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "3fa8823203529e8453f2ed8f2852bd2a6db182a6", "gitHead": "d4ccf25d63974280bd62924d29ab4ccf9567517c", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of <PERSON><PERSON><PERSON>ein algorithm with asynchronous callback support", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.1.tgz_1472201643290_0.96603913907893", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.2", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.2", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "8b7a7c9b135353941b5283e4cb1e3e16831c0719", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.2.tgz", "integrity": "sha512-yCGlZJsnJ2xpVjJNeOXEDg+rbRRLPu+o+HR4AtoQOPN3Qtxj3jrXnn9CsQdoOlan/Xn0iPOAOek2LCKgiDv2JQ==", "signatures": [{"sig": "MEQCIHdu4/Q9W19UQ3fFvL18dYkV3zJJXuay6Ckd03hjp7HaAiA6RNDcingkY2rkTX2Fxqfx9MsiE6VqG5oAJWM8BBu2Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "8b7a7c9b135353941b5283e4cb1e3e16831c0719", "gitHead": "1e611abfb4a2110ef16d934e7bcbbcf7f7f3d5c8", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of Levenshtein algorithm", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.2.tgz_1472203513561_0.8094956018030643", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.3", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.3", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "2e4b613e0a0a2f53df583c765a816b97b0c98a9e", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.3.tgz", "integrity": "sha512-XZJQcE7MJydu1y2McHEl315JybTyLdgiXWTPHoLtxNyAjcZNRsQgg9yVxY6bsUaHG9vWiGeQX9sqkwkA3gV+XQ==", "signatures": [{"sig": "MEUCIQDA95lkSZ3fMz2TcM/ITdo6Efiy9jCvqvcE+CW8zbfBcgIgUbSK7TzjL/k92wD5X5mzTctJ1pdFdQ9Zw0bTB5UgweU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "2e4b613e0a0a2f53df583c765a816b97b0c98a9e", "gitHead": "7d2dacdf375a12793e105ea38cdba002af12ef77", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.3.tgz_1472203622853_0.8779530352912843", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.4", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.4", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "e31e729eea62233c60a7bc9dce2bdcc88b4fffe3", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.4.tgz", "integrity": "sha512-rXyNkgl+50O/pR2wLRXW92TjZGSEEkLb/xW0PE/BFPlfae23ljaFvvkGjerrmytV/Yzr99f5Mfs71pRnHIm5Mw==", "signatures": [{"sig": "MEUCIQDBhhTihvRrS4YYRd+LPEobtReEWt1mJFOgNvLS7ysPQAIga9bGZRpOVSKZ/tLZDttQZf0LoMcm+G0EDeftnc4d2Bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "e31e729eea62233c60a7bc9dce2bdcc88b4fffe3", "gitHead": "8eac08c77caa7e77deaea37ad20b66c1ef660fd3", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark", "prepublish": "npm run build"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.4.tgz_1473223237736_0.9074071520008147", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.5": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.5", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.5", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "bd33145744519ab1c36c3ee9f31f08e9079b67f2", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.5.tgz", "integrity": "sha512-k/Cyamv0o4QoCvYZIRx1vza6Pu54UV4HLCR//rWS83ZJYD8FAGSyf5u43u6y+eDpzKK+az2+uZdESTY7G2Q+Yw==", "signatures": [{"sig": "MEQCIAf9/E59JGiz5mQ/YhVkcMHU4VX0yjgKnUxda4OdF2B+AiAUazzl1oSX3ntKKgseWUfqfCsk4h/Jxyn0DDYffkAkHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "bd33145744519ab1c36c3ee9f31f08e9079b67f2", "gitHead": "7c41122f8725a63587ec34bb19b12dd91f12465f", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark", "prepublish": "npm run build"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.5.tgz_1475102215149_0.23410583310760558", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.6": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.6", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.6", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "3d8a5c66883a16a30ca8643e851f19baa7797917", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "signatures": [{"sig": "MEQCIAOQFCpX5RDz9dO3fg+1X0nUZ5rtlMAZAyNZtp638CJpAiAERjRKr0xakMHlk9hrdu4nPCpyy8TCa7W+4eVFC+z8Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "levenshtein.js", "_from": ".", "files": ["levenshtein.js"], "_shasum": "3d8a5c66883a16a30ca8643e851f19baa7797917", "gitHead": "5bffe7151f99fb02f319f70a004e653105a760fb", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark", "prepublish": "npm run build"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein-2.0.6.tgz_1482873305730_0.48711988772265613", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "3.0.0", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fast-le<PERSON><PERSON><PERSON>@3.0.0", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "dist": {"shasum": "37b899ae47e1090e40e3fd2318e4d5f0142ca912", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-hKKNajm46uNmTlhHSyZkmToAc56uZJwYq7yrciZjqOxnlfQwERDQJmHPUp7m1m9wx8vgOe8IaCKZ5Kv2k1DdCQ==", "signatures": [{"sig": "MEUCIHsSsc5Rk4bKSA2O9rbMGbXUqlftlrZiOel4PnufXCp4AiEAkFTOISsdiiHtd1Emw8RSxzjhqKm1AUOzvrcbqQnsREg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGEyOCRA9TVsSAnZWagAA9wkP/0OtwmE7r4Zttrw5HjFF\n+KZR9XY29uR8BLdqoPYZRnP+FVthRNVQDCI/W69ufVL8RjFwaBXgYHHAC6NV\n/gO1N4O6P0RSRrSESlqek/0OWNmsRN+YKi0QCavD1qgRPtiJRUp/wmO5Z4lQ\necWnVaCn0J19gLlxW9qPkeF0TFwHKCQZW1D66M2bxrdkkyxGEcXWW8E/xMjr\nryas7hWZWHFolz4v3xarqjBMuFHYf0Ia6KC7NAp7mOMdjp7245p38cm+oMfC\n0n9KJJaeFvuslsdV3ggaKWTUyZ4afuVaoxxK2twcR3CvFgTRPGihhG03zGc/\n9YwMtPb0q3Gg0pvguUPPhtJ4FW+G2Rg5pMsPov90CsX8GAqAJu0tnm+Ezdvo\nXKeVM4LChO3KrBXnjbfsfbuhm52uD6gZrQhkEkNkNwmrcC8YkXqZ1jF5s5Um\nZ71gHQXAOp4RR3weT+3YT6zFJcBQcE+SN2AVPlDpofOccxTJ+URxX8AUbKk2\neQo8oXsJUtSojiJN5f5Py5s8eIApGNxljnKg1mCh9UnSUtpYzgYSW2EhxEcl\nmqTVCgqwvikfQvrP+3a+jtqt548ES3vG6svIfwI4EZsl6O2EYoi53JoQmLci\numAV8s4I+T3L35vB+jwkiuE80DrRKkbJI1DYi8aTjd3OJTHllxfrYZqUNM9/\n4kYD\r\n=sqfc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "levenshtein.js", "gitHead": "d2434d324e56c9ef70d6f8330382e7ff758f9911", "scripts": {"test": "mocha", "build": "grunt build", "benchmark": "grunt benchmark", "prepublish": "npm run build"}, "_npmUser": {"name": "hiddentao", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"fastest-levenshtein": "^1.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/fast-levenshtein_3.0.0_1595427981926_0.6198370966231346", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-04-18T05:57:10.372Z", "modified": "2024-11-04T19:13:47.279Z", "1.0.0": "2013-04-18T05:57:15.181Z", "1.0.1": "2014-07-10T03:39:50.757Z", "1.0.2": "2014-07-10T04:19:05.817Z", "1.0.3": "2014-07-10T05:07:35.370Z", "1.0.4": "2014-09-15T06:06:35.043Z", "1.0.5": "2014-12-06T03:04:03.113Z", "1.0.6": "2014-12-17T16:32:54.020Z", "1.0.7": "2015-08-13T01:08:40.011Z", "1.1.0": "2015-12-29T10:52:41.779Z", "1.1.2": "2016-01-26T10:27:10.654Z", "1.1.3": "2016-01-26T19:21:05.750Z", "1.1.4": "2016-07-22T06:27:22.895Z", "2.0.0": "2016-08-26T06:23:35.146Z", "2.0.1": "2016-08-26T08:54:04.982Z", "2.0.2": "2016-08-26T09:25:13.798Z", "2.0.3": "2016-08-26T09:27:04.507Z", "2.0.4": "2016-09-07T04:40:37.975Z", "2.0.5": "2016-09-28T22:36:57.195Z", "2.0.6": "2016-12-27T21:15:07.820Z", "3.0.0": "2020-07-22T14:26:22.114Z"}, "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "author": {"url": "http://www.hiddentao.com/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "repository": {"url": "git+https://github.com/hiddentao/fast-levenshtein.git", "type": "git"}, "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "maintainers": [{"name": "hiddentao", "email": "<EMAIL>"}], "readme": "# fast-levenshtein - Levenshtein algorithm in Javascript\n\n[![Build Status](https://secure.travis-ci.org/hiddentao/fast-levenshtein.png)](http://travis-ci.org/hiddentao/fast-levenshtein)\n[![NPM module](https://badge.fury.io/js/fast-levenshtein.png)](https://badge.fury.io/js/fast-levenshtein)\n[![NPM downloads](https://img.shields.io/npm/dm/fast-levenshtein.svg?maxAge=2592000)](https://www.npmjs.com/package/fast-levenshtein)\n[![Follow on Twitter](https://img.shields.io/twitter/url/http/shields.io.svg?style=social&label=Follow&maxAge=2592000)](https://twitter.com/hiddentao)\n\nA Javascript implementation of the [Levenshtein algorithm](http://en.wikipedia.org/wiki/Levenshtein_distance) with locale-specific collator support. This uses [fastest-levenshtein](https://github.com/ka-weihe/fastest-levenshtein) under the hood.\n\n## Features\n\n* Works in node.js and in the browser.\n* Locale-sensitive string comparisons if needed.\n* Comprehensive test suite.\n\n## Installation\n\n```bash\n$ npm install fast-levenshtein\n```\n**CDN**\n\nThe latest version is now also always available at https://npm-cdn.com/pkg/fast-levenshtein/ \n\n## Examples\n\n**Default usage**\n\n```javascript\nvar levenshtein = require('fast-levenshtein');\n\nvar distance = levenshtein.get('back', 'book');   // 2\nvar distance = levenshtein.get('我愛你', '我叫你');   // 1\n```\n\n**Locale-sensitive string comparisons**\n\nIt supports using [Intl.Collator](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Collator) for locale-sensitive  string comparisons:\n\n```javascript\nvar levenshtein = require('fast-levenshtein');\n\nlevenshtein.get('mikailovitch', 'Mikhaïlovitch', { useCollator: true});\n// 1\n```\n\n## Building and Testing\n\nTo build the code and run the tests:\n\n```bash\n$ npm install -g grunt-cli\n$ npm install\n$ npm run build\n```\n\n## Performance\n\nThis uses [fastest-levenshtein](https://github.com/ka-weihe/fastest-levenshtein) under the hood.\n\n## Contributing\n\nIf you wish to submit a pull request please update and/or create new tests for any changes you make and ensure the grunt build passes.\n\nSee [CONTRIBUTING.md](https://github.com/hiddentao/fast-levenshtein/blob/master/CONTRIBUTING.md) for details.\n\n## License\n\nMIT - see [LICENSE.md](https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md)\n", "readmeFilename": "README.md", "users": {"cgraff": true, "hasyee": true, "mimmo1": true, "zambon": true, "keenwon": true, "morphar": true, "heartnett": true, "karbunkul": true, "wolfram77": true, "dwayneford": true, "kreshikhin": true, "rocket0191": true, "seangenabe": true, "christangga": true, "flumpus-dev": true, "ganeshkbhat": true, "nskondratev": true}}