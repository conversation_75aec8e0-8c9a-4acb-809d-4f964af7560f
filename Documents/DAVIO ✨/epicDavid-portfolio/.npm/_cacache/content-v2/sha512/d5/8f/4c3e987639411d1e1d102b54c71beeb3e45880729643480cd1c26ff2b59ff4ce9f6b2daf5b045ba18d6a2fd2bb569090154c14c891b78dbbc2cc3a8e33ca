{"_id": "@babel/plugin-syntax-private-property-in-object", "_rev": "3-92e0c6d11c41a8d7afc7c5f89c8fb27d", "name": "@babel/plugin-syntax-private-property-in-object", "dist-tags": {"latest": "7.14.5"}, "versions": {"7.14.0": {"name": "@babel/plugin-syntax-private-property-in-object", "version": "7.14.0", "description": "Allow parsing of '#foo in obj' brand checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_id": "@babel/plugin-syntax-private-property-in-object@7.14.0", "dist": {"shasum": "762a4babec61176fec6c88480dec40372b140c0b", "integrity": "sha512-bda3xF8wGl5/5btF794utNOL0Jw+9jE5C1sLZcoK7c4uonE/y3iQiyG+KbkF3WBV/paX58VCpjhxLPkdj5Fe4w==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.0.tgz", "fileCount": 4, "unpackedSize": 2679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKVCRA9TVsSAnZWagAA/SMP/2ZQCtDLvBwogwx2XWqD\nJh2md3ZNOg2MN2ti+kRZHIEHvm4m2U1OrO2dy3fJ5wIsS6lG5vtghvOAypF7\nkofotZ9kNuAZ8m8pCDHNYv5W0UFVooIDp/3u8tbTI3HNYrxota+rgdaC+w/m\nbDbRWtkUBuDi46q9YK0DH9PBzk4aNlcTUiqrs2O2GG9nvv5chIT/iTad8PSb\nNuKXntJ3Pn6oi4ASOAot1dqymiH+US1EBrqkrmamA8oEtIet7Fq7e++rvRTt\njHtg2pYAqYs35RWsLoAvH1DyvQErD8FcQJ2qEgsgoPt2siAzihZu0y5O+tMk\nUCuYwVi74Ft+HRhfDHrO9Hkb1q0G1D1KLzE9+Ex0j2FYtJE1wuhYZcUSV/O8\nuOZ+dIW8Zl/EtO797FtyuDfNAuAtBUKqu3Da/yyROfrysL5PY5lJZr71SK9+\nCjm45+Je48/tV4sQJvaJcOq4Dh7QJEjpaq+HQqUHvCLdCZWQqY6/yEms6hzB\nwM+AqyaH0GZK4bHqFW2++GiW1dBumd81MXfiqF/DscOtU8JBHjoGJ+GkWJ3Z\n9rbAJjEuBeb61urM81L+SCziZcqrKKG6H/XhD74OJl3qhU5OHLNujez4xwwe\nszgpZo8SrrAkRZHzL6cGwAuFpYO5M3SO6pvXJpkSnNNKgHBY6lR0vKNc81jA\nITfx\r\n=GeOI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYy/pHZmPsvKsS/2z+wTHJxFaYGwBYytb1j0JLqoaopAiA5yi7RB9Q6+9agn+FEv9h1oHqm2b08osahYs713HJ7Ug=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-private-property-in-object_7.14.0_1619726996631_0.6708702484651825"}, "_hasShrinkwrap": false}, "7.14.5": {"name": "@babel/plugin-syntax-private-property-in-object", "version": "7.14.5", "description": "Allow parsing of '#foo in obj' brand checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-private-property-in-object@7.14.5", "dist": {"shasum": "0dc6671ec0ea22b6e94a1114f857970cd39de1ad", "integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "fileCount": 4, "unpackedSize": 2823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq5CRA9TVsSAnZWagAAM1MP/3NEfJOgmfeIe/RtBg7V\nsvPhVOiiRyjF/OZvgmROL9cTP4ctIa9ZkKz81kbWlxIcVyZ5jXxC2Yw3s+pH\nY6vTBN81e5MkUuU85+DCaJonwQNgKAqxyzcnIIkrbR1q1dWGnqcky0GGqb1N\nGgloIqBnHoKG7FSCuF2VLg5s3lOpI6rIz0uHozaJLPaex1Bb90vpLd+P9Gke\nfKoSG8ObMobGmehJlk7cqQHegM9PSLSFnMICghvnVfHLs0gL1qIoG8sCT/3i\nbtk+ODJrXMFlsSgj5UoAYFLyJ1JWwXyFIcHksTie/w1revxZaQnCXQ5CIkpt\n9+SujWztCmrB0zLqGebziqYtZw0Uxo9tA6h3sOnmMMMOhYtz6BMp5ngWxb+S\ncL/SMYZiZLqrWw24gcQ3brwkwCSol9AYUnmN35HyU2YqYK8fkCmk1h0XCFw+\nnU7CLOjohOWv9Th3yk0ZWLzbLAgjks15Yu969KSF+dMn9lXT5vZdnoqvh35M\nN14SMyZGE1eGhhjFjQWDbNoFZC+pqLlCzBMlB1BA2J3ZhkXoqFKMGUY2db2Z\nLlXZbcze2CdzzgFRZzWeyeOSWc2Vio2ZR3MlNOVZXtg3j0PYbyEdXcKODxvA\n55Dm4y4PiXGsWlAXTVNy6qZtPFetu1J6FIHwaUT8Uuv5P9/A1ocfYItBrR1e\nv4Sr\r\n=oZwU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWCAloKlxo/Q3s6ue9f70a+MjJ+TzmiNu1jYzgKnb6nwIhAKElrPpZQZN982BUjBFNofV3hevBzPW6zIv3LO80ANtx"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-private-property-in-object_7.14.5_1623280313003_0.7659614816942493"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-04-29T20:09:56.338Z", "7.14.0": "2021-04-29T20:09:56.823Z", "modified": "2022-04-04T17:25:31.614Z", "7.14.5": "2021-06-09T23:11:53.125Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of '#foo in obj' brand checks", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-private-property-in-object", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-private-property-in-object"}, "license": "MIT", "readme": "", "readmeFilename": "", "author": "The Babel Team (https://babel.dev/team)"}