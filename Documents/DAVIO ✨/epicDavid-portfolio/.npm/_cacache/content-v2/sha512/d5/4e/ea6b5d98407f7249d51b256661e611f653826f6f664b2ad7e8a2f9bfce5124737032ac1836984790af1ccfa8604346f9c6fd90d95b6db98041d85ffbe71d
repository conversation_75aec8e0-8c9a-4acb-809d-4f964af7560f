{"_id": "@babel/helper-create-regexp-features-plugin", "_rev": "77-f430f624a78b19612d16386d7dedecfa", "name": "@babel/helper-create-regexp-features-plugin", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.7.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.7.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.7.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "2e8badfe201cfafb5d930f46cf1e0b6f1cdcab23", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.7.0.tgz", "fileCount": 6, "integrity": "sha512-ZhagAAVGD3L6MPM9/zZi7RRteonfBFLVUz3kjsnYsMAtr9hOJCKI9BAKIMpqn3NyWicPieoX779UL+7/3BEAOA==", "signatures": [{"sig": "MEQCIBn4fPXc+S0/5dbIE7mNqaXN2uhnao7qqlAj1IBaf2QYAiAls3+h1ebLFZJ+7UORHGY0gbqtwYumQ8yCsFjTM2S8Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSXCRA9TVsSAnZWagAAEp8QAIdoqRF+7UTUdVlpf57b\nBKKajpL7ru64ikteSWxTqYzPrhZpiPwWc5XPI+y+5j3Up4rIE6pj4MGJj2Wi\n4xK2TIaWCZNEM7MIJP+EEYzUYMwVUnKkzNL7plelgcGuZ5mgUkQhvLkxAAbc\n9k6AwECGglikkJSEPQtz+cOgV/2FHGkUy7/tc9WEb5cycGGtnHqNcEYOO76f\n9xBc3AUi/9JZ5NF7lM6UMJ5+jKtLcwAQfidS/p2ULh/AGRy7UQDuRpzh5bOx\nW3XZabDgE37p2E73NOGAnwpT95zyxZ4rptCCuhcC+6DBrWwZoqGHmDe2Hzik\n5Vtywc8Tpqwz1tyfOh7PkBP8y0LA11cxIQgRumD+w+4wGdguYKnKF7hznIkv\n6G+SJfxooTV2ElLRbIsqhKA150ABfNB6wmXcPibBNtIRhHpmuqSjK/jOFjrJ\nQbFwp/4m2fj98kSUBZvFX+J3moGKpBVG0U6qxYuG6bN9w/WbIwkjWWXUW4Te\nUtKxloyRtXEWciC/UEqKNXTQ0c9uUW0DMvIAgknogtixyA3yCkF6/xP5fcc/\nZnAKPDcvN3tr2m1CaYIhSrlb/toFTS7KBzZ3rQNfPD7IIuX0ZCWYZ6slQ1zU\niLQek+TfB1Cow5QHRPKLMrLt0P1Ag+/RmL4wSMgX2NtWg4MYg31j5SMYsoD1\nsoC5\r\n=N41f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin", "repository": "https://github.com/babel/babel"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.7.0_1572951191437_0.007743693955059916", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.7.2", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.7.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6f20443778c8fce2af2ff4206284afc0ced65db6", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.7.2.tgz", "fileCount": 6, "integrity": "sha512-pAil/ZixjTlrzNpjx+l/C/wJk002Wo7XbbZ8oujH/AoJ3Juv0iN/UTcPUHXKMFLqsfS0Hy6Aow8M31brUYBlQQ==", "signatures": [{"sig": "MEUCIHg+0viKqIRdeCUu5BnI46k8D9xbi4WzOV4z6fYUQOCoAiEA0HJyutA7AM8Ii/Ia9TP2qC1rW5UaQqwwKoH/09/R4po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1baCRA9TVsSAnZWagAAdVQP/ArUjm9NHh/Yk7ILaAls\nrznhSxZyv6I1n4xg3kRLqml5+wHJLhfdsy96Qkvz5w6nBRWiHvSbxOh7R/XV\n27WA5IpEml6nlgpQUb4TR7P3v78yYkG+F29cnyWmHoCCETdRptjC5b42n6Oe\niYaRR8bm2Rh1D2n67ffztFNwVNChjGBnkVMuJzHzGGmFzRm3zXgVOvDsWmtd\nzN4xw3BrhuLaSX8NOiNyBqo+IZYac7DerFycqKvnmEYobk+VOe0RcPxRPcuj\n80NqESdpzJ6/D4Mb1eKNu0k3w9/S6VWJWenYnMZAHcv8v+LzBpYG8C3wVedH\nO1ZA0bl1PXZV91CjsfruVtNc0cfUCxVEajasjhDDcBkP4vchKKtI+rE2kO2F\nTnQlWjm4Eu2mAaBiK8GNkVFzvm+3gxrVt/pLFHC5fl4c9B19F83fobK/4+k3\n44o9vfFby1mHT8l0G6ADRcOfncmqXgXKZuenJ6Pi51Rj4v8H4qFtSjB8Hy7m\nD+VpNPGkVp4iOEyCN8GeIojbb+mtBn7A1MFoG+vxIgyPaCVdgvNRMs+H3kJZ\n1gQXklVdjpPaCSAVB1qOaKmvfZ/l0JjIQNBBFvkDSRKBU9sRebbzknTQrURQ\na1Few4stvvSD2dWPDD0Cn48cYb8mQEkKOrcz/AZ6zhgNIpXITxk3Ug6smslW\n02zO\r\n=ViPr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.2", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.7.2_1573082841882_0.8807329349903439", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.7.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6d5762359fd34f4da1500e4cff9955b5299aaf59", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.7.4.tgz", "fileCount": 6, "integrity": "sha512-Mt+jBKaxL0zfOIWrfQpnfYCN7/rS6GKx6CCCfuoqVVd+17R8zNDlzVYmIi9qyb2wOk002NsmSTDymkIygDUH7A==", "signatures": [{"sig": "MEYCIQCEXv+Ufr8cVB4Sh1bDcUz91SB+3RPu4Mt/I47XCp95cQIhANvs2lOD2G+Dk4ODB0/edvN9AFL6RkZz6usA7PxvE4YC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/jCRA9TVsSAnZWagAA1mIP/1UpY1deBVqzfJMd5IM5\nFVIqL025G6M9pTtMR2zCoR7gs1P3DTVv5l1hcCePJ3s/cfjuobgENPmGMZof\nVwIW+JI8TXBs5loggscWZRzqROqk8eusKXGIsQslwuRR+nzAnj8/P/AI+aHi\nl2xXx9RUJBx3fERKVzqmRS+MvaGfWTRkeZQDFA4WRbdI//dKjR1MQzZokmxW\nBFS7AVkA76Lqw4z8EodhvulhvqRcs9JdN22SVKecy3ksDABgaDm56/LAXOlA\nOAIkch0ZlNNn6Iinu6zAbeV46W0Vh8r4lqZovYHnadZbXJVUZR1ety03o/u5\nURSQXYYXXhCiY0XQ8QCAg8clZhkAfyybjCNMS4jp+cdQdIIOJuvePtD2I08t\n7YfbfOO2dSKhfUPVOir2xsWiznryMjMT3oMJ2S1gSorDTIODH8AywT6fw8MI\ng7/hgEBYQ7PLhVemue6xfWLdqLPfWmjaK0tU1MbUyRxy+fpJ9wL1xN5LIGHq\n6ZMssnBe1WdybsdfmNblKmzwGZY7pQMdvLBR23o2wsIiuHIzmJbwE+Z5zgPw\n9rwRL07t9Hp6Yup5f1FIcBUOhZKvbA/JshtS5Qdrq5VK3VQqR0LN2eSZfrqr\nImhc0A2s5donDcsP3LWO88kHMXbhb8WE+UKabmnAdaGn4WLKI/t43AO48ytV\n1WbD\r\n=0+Tf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.7.4_1574465506872_0.6293221360041532", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.8.0", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ae551572b840189a7b94e39eddc1a68d355974eb", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.8.0.tgz", "fileCount": 6, "integrity": "sha512-vJj2hPbxxLUWJEV86iZiac5curAnC3ZVc+rFmFeWZigUOcuCPpbF+KxoEmxrkmuCGylHFF9t4lkpcDUcxnhQ5g==", "signatures": [{"sig": "MEUCIHYmndFGIf1i212AqZHU4yeYgt80hTnsmzhZWJ8FhdKlAiEAijDmyYJyaq4QmJ+Kooc/r8z8DIJgmBH36jS5ct2J+BE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVJCRA9TVsSAnZWagAAwvQQAJUhfBEWVKg7Vhh1E3UP\nNj/ISoAxT68d3679qBdzkpZzqc+8Dd336So0p5fFIZJG/f1NWSa98t0Llg07\ndVjHfEJ45wqcljhyfVcH9gvqn7NSTnF0jM0VKUOvN8+Dmgl6kLetV+BuISHt\noK0Wiq8wb+9UXgID77tgl/nN/RLoJmahozZB/cPMdcs2sog28n//SFVHb2L7\nfUME2WGKFk+YFnSMGItmUUZ3VmdgpnJDOExk7iIz4/p3Oql/Vkq7dV2NuCEY\noPgCx47msw6U+jGxcHaGLqIBR0btYwf83d9nEbJNP1M7Olcld3OUxnRs60DF\n9/kpUtSCL6ZK6HUyROpogK5fEz8apF+v82BL/4v5s1hzBqYwNWeJX0yGHeoI\nxxmzaTCJcsqAG96NBjKdwz9H8LvR2H/qhxaazG44LBAOk3Ud7EMltiwwZAcf\n+BOGk0rxeO9GZXe+YY1IEd1dwEkGv/pSrYTm456L36Zlrrju0JCSoh6DrwmQ\n2a5s/EBuiOG/es6trTZHIIzkYrologhmOPNGq3cgA8BennA73uCg2h/Uv5tV\niH+L8VlfERA0kT8IpLHYscbn/rp3P2ycuDbIKbBcgv4euEy1I3EwVQ14GRCE\nyFiLIyEZ6YvrF0fxGmnuxPYgjNdAJbl+rInlFuZyPlMHN6iYwjOy6ko3bbBM\nBBY1\r\n=XXAA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.8.0_1578788169511_0.10524995289870409", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.8.3", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c774268c95ec07ee92476a3862b75cc2839beb79", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.8.3.tgz", "fileCount": 6, "integrity": "sha512-Gcsm1OHCUr9o9TcJln57xhWHtdXbA2pgQ58S0Lxlks0WMGNXuki4+GLfX0p+L2ZkINUGZvfkz8rzoqJQSthI+Q==", "signatures": [{"sig": "MEYCIQCrpdcyAx5SgjC0kK8X+0Evevd9e7dbYFgZGJlDQ1Fi/QIhAPQ1aUeIF41I35Sc3TWz66KmUgwoKZ46d+wuc4okKs1M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQiCRA9TVsSAnZWagAAiZoP/jddGEfISYvUlWkeqvZw\nxLo53AvNGG9s2YzgV4ZmrchwyZ7p25bas5o0m0Fp87ETT73YIIdgfyo1SbUo\nBmC082T9iNj679sveEpe/Im9Axfeqx3gJSq2nAt0aqW7VfTZGerzNm/0OF67\nRKVJdezSzOHnD44wh0m6nkAF2BvrxFb4AwKOApDLwOuKbpYHmMbuLyoB0ssz\nuvvTIksVOacxV92dxHu5rgBYH86i7dFM/1jJosExbgGk0ao2uzlOkiz0TPAx\nKZ9jfXabXkkzQjRZNJ9rFoP/QVw+A/MD/RGcbp6nEEMPiVNDvht/smVc8E0r\nGldaXIZxKkO6i49N1cUUK46f/ooP/7rC8UhEmu3+n6RL4q164rCTILfUDaIR\naIm5NSeN8glbs1WEdmRtzP+CeTzJLXUfHSH/vF/M/xLekv/cbKnkamn5Qzl2\nmsHiY9TCffaNYUH3yDY0N3w7IuvtuvF7TQ3Vn8xQV4WYseUUTEZ+bNVOVb2s\nidcCwAhrg+xnGEDSYFvTGzVG2FeeKe7QUDdqt74x5xSMJqHWfqh0eUSszwxL\nlO47Z8cgiy5ZIW33v8mvulJ4xEjBBiKm/x6JzZSqhmsMjE9WbbmWgmim91yx\nvQxbsrork+t90hVbSmc/gx+YkwpSJ6cPfTGpIkjTLETtuxarkmclAnLpufFo\nSneb\r\n=4u0S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.8.3_1578951714232_0.8749487045107711", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.8.6", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "7fa040c97fb8aebe1247a5c645330c32d083066b", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.8.6.tgz", "fileCount": 6, "integrity": "sha512-bPyujWfsHhV/ztUkwGHz/RPV1T1TDEsSZDsN42JPehndA+p1KKTh3npvTadux0ZhCrytx9tvjpWNowKby3tM6A==", "signatures": [{"sig": "MEYCIQD6rvF0MXstDX8P2mUskaStynXtzSrJKzG+NsycPaVXTAIhALvPIuQO5P+EJnFnLN0+eyJOhIuZTAMGrx5b6vWn9MQT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RBCRA9TVsSAnZWagAAdAUP/1JfDmvqaybeUdQP1m1B\nkQ+dhinWPSNQj0rYJSySQMlqicoI8Ri8mcq8jxjYA0BykB0MGsMypAajPFFy\ndjgfV5mbw+KbNLqMIXof825G9v/F8KDtqwiF0tOmfIuOOVio68u4HK5Jsh8s\nwmz6UBGMXZhWmip9hVm/1qApGe5aErwwM9OxdmjXIP+XF76N9aV9VlpY3BuP\nLlX3vj5uhopj7Xpbgnj6DdqnHJYvkGSxu3J4Xc/PQRnHEz4Ubzi7o9/mg6Yo\nEoZJA5Cuana9InlPUTh1SdYD0riHtspxE2MiXnDs0fwo2fvSUKw1+rbycas8\n8z2iYY8l/OZlvfmoE1zW9LMfeIxFD0UU0V6Kk+FUAGSHC+qxrqeLxdxBxDe2\njwSrQpwmarGgtjy3GSVqL9GBznS/t+vo84TnMLwHxWSnwTHkmb/tmJk2CUiA\nWyuWCdmmcFXdW/La8VhjEes1bXC7nWtT2Ip1Z89E3fhzM2H5Q0Hd7vqGs2/S\nI4KGFZ9xFr9MqWE2KyqjE3XdsMHVRS4NMpP0SpB56Zvm5PQcFwXiRPor/AgK\nNs1uLGRl0LZcMcIGm3pUfeZ8WgquclWxyCDs96KrTzwy7YdLcwD5xZHFEpG4\ni9k9wi2ji+XtVJMrHFpl9Syr4oh8PzDaYvlUSH8i59ShRPxkoAMuhTRIpEa4\ne6Id\r\n=kjyN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"regexpu-core": "^4.6.0", "@babel/helper-regex": "^7.8.3", "@babel/helper-annotate-as-pure": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.8.6_1582806081430_0.00458802840801642", "host": "s3://npm-registry-packages"}}, "7.8.8": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.8.8", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.8.8", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5d84180b588f560b7864efaeea89243e58312087", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.8.8.tgz", "fileCount": 6, "integrity": "sha512-LYVPdwkrQEiX9+1R29Ld/wTrmQu1SSKYnuOk3g0CkcZMA1p0gsNxJFj/3gBdaJ7Cg0Fnek5z0DsMULePP7Lrqg==", "signatures": [{"sig": "MEUCIC++q+tOUtW6u8yTAaG60pRZsvUUv7cYoYI48piX7S0aAiEA4N+UWE9xJJ0edGhwuV8pM54FFCaHFL2HIjbjvrjNb1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaoQaCRA9TVsSAnZWagAATEEP/3U7uRmOwVUzDoDrkEK/\nr094pF2jrz/H+Cr0CBHEv2tU8HbF27MFvCdQ8LEvKq+2ZHFSNPE+7FVl/p5M\nHoe5W7caE4OxIJ1I121CoLOSkh2ykGklWOn0AyxQ4rLUFRlqacKVWYJu33x5\nbq2DMOfjZm5cul7ziZOgnUOa5+kTiY/1rjBWb3vxp59HeP2qHe9YxzLMK84M\noHxiuQ3Fs52NQ/0Lf9kEECwaMD7DQ/k29IdSmr10p4m5tkhBGYR1pD/9X7IA\nMiMLhejYVc5DXq0JyGRAoO2u3qcImXPrsq8tB/8hebcvM31BCPkC6t+wMdCO\njUPuaeYPkQoiH9xNM/vEfVvEjeXdUKsTLyhxaDbJeMvlYcXSh6kMb1URjb2R\nThOgWeAnlE0/swj2HYLRJACmAg8n5kL9wo1Q1xiloDtl4snMbUCnCbBgNPhs\nY0XJgNvcQWUXpGqqcifE77xaPLL+QS0F2D3hgEDMBBcChnObAMYwFaF2CSU+\nNgecL8wNsLcOE282M87g20hghfMAogUKbr/dmuZcWMEm//ZS/Q/C59aEtZS+\nIWVL8PiJLhTF4PlBqPOvVSbQBlUrZ6uG9w7MQSE5TQ/gM3/RZ/nu13xdFNHh\neB0g1PLs2WsvdLfnJvs7oRNh6CfEzDZnNYqZRiAyvy7xnlqDXIFjgd5kyh07\ng5NV\r\n=wRWt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "c831a2450dbf252c75750a455c63e1016c2f2244", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v13.9.0+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"regexpu-core": "^4.7.0", "@babel/helper-regex": "^7.8.3", "@babel/helper-annotate-as-pure": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.6", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.8.8_1584038937950_0.20501556144478839", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.10.1", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "1b8feeab1594cbcfbf3ab5a3bbcabac0468efdbd", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.10.1.tgz", "fileCount": 6, "integrity": "sha512-Rx4rHS0pVuJn5pJOqaqcZR4XSgeF9G/pO/79t+4r7380tXFJdzImFnxMU19f83wjSrmKHq6myrM10pFHTGzkUA==", "signatures": [{"sig": "MEUCIFoB0H8r1b1fLgszaSFZuHoZ4VPVXxd/wIrjz1N2mkd7AiEAxHGNgUDbCUJgDE+ld3u9RkPuwsI82J20fxn+pmpNKN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS/CRA9TVsSAnZWagAApoAP/2lFUEDSLP9bmWEQWvgj\ndPcJ+SwqLIOL2a/A9n4pjZiFexrVuD2odXGbQJEuCCQnYQ4VdCl4F16Z2eVK\nRy2J3/ry3kv2xV0LPx4qPoz9EcmQ/PYcfML4yEc9ZTuRpiZHBFjXIcpXQZYQ\nYBe8fMGPall6Grti8/Idb0AcS9foKvw4ygjyRMPKLpX1voqEQ+2DiHnOJ3Ba\nHp3iYPH9klhiqiA0iPkp6P2UKeisc35KjSFyjaeRLQhuTPaL02oblxcrnly8\nOSKUmsJKXHUm64AapAvLn2A1RF4T7Hun9cMsiVnF06FqtR9WbRuk4+a1AE0f\n/cQf49bi1cKFcFmJQOUjceA8WpU/LAqog2VBL08f9+x367/ZFPf+tCJhko82\noS1B7yaOHPaI62C18PQStjCTjvpePXAVC4kIK+3Wph5sAcddZHaDVuhb/+So\nv9GGnV9HC5qV7a2cnzxCWOueGvb/9ayJ8G3Zvo1xMaFtWvv++Yprg7rEtlQK\nhmwJhcEPw5IXoewHXi87fZRl+15sjM9DwPmXD9ijR+8oHOLiYncuMEOlEs6l\nZPjngxvRv7PskeeWhbAKhhC0rvXiGhwAvcf73j4PsA62xQoxItYnfwRH03Ec\nDoJbAjVv09hSGFXEURkfg5zhXK0e7dIx8zdsGFzIODwlIuiXbXWsQKJ5Sf41\nWQ3W\r\n=2FIs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"regexpu-core": "^4.7.0", "@babel/helper-regex": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.10.1_1590617279257_0.015166672021674543", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.10.4", "keywords": ["babel", "babel-plugin"], "author": {"url": "https://babeljs.io/team", "name": "The Babel Team"}, "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fdd60d88524659a0b6959c0579925e425714f3b8", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.10.4.tgz", "fileCount": 6, "integrity": "sha512-2/hu58IEPKeoLF45DBwx3XFqsbCXmkdAay4spVr2x0jYgRxrSNp+ePwvSsy9g6YSaNDcKIQVPXk1Ov8S2edk2g==", "signatures": [{"sig": "MEUCICGqzSq5tOy8uR2zNcWJ7WNGyj3pAiBHKXO+g+LVNRh6AiEA1uga/oTNvhEHLM4j8mxPyld1uLNJJg0WgTU8J4pGcTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpGCRA9TVsSAnZWagAAGjAP+wdP2R84fWP2whGp0Tzp\n3lAeXz4dChix7xvzzzRFlBb+27+NsB2h6ieDaYX/5ulWNrYvOOQBYKOcidLH\nAaOOSdOQ1h8M4cAHxIeD8GHDfFCHvopWNcUITCLvib2/AmUcozFX++nWzXYa\nXHhbXOAJxiVyYdwAsgfL1+18SWSxKpBbaG0MjFQEILoV/GsnfleOGlqy/bNF\neBn1gbKVGiIC5Esx1j/iqdz+tHMj5/s17YRxsCnthwR9+Tn4AzUoQGkFFYfe\naD3AUC6WoCS4I1/n7ATvhT2HNdfEyG1+ChIDZOq5/9/Yx6cEcSTZUyRk/E1I\nEq0x9JdYRSqhuvrPJEAgW93Avs78OSXWVueF9FOiP5YHXIKCy/U3YxLqf7gs\nJ3s+ndrjCpUtpoImR7N8fC3lJzSnV6ye/rYEPiDHWQqMMWphywIN5tlCGx+P\nQCLIuGDkZOyRvHnStQh6i5wwFRl96xP2zErpIatUCznRbo+paJ2dvWlNHjXV\nu6ohr4Z+NzAYM9q4eIkefqR2MaxUD0QSDi+fVgLO81/ppAvbKAFBIMSpuTTd\nXTeiqaPEsZsONBGl+T9Ulk2B1vm+gg6x57Vh0JuhVwPbGMQ2CFpTWPlnERyH\nXgP4o8xTdcKSLQmo8upq0UnTJnE74HUSroOesUCN0/pUsm5Fhi1j/ZX788tK\ncoeW\r\n=FTzh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"regexpu-core": "^4.7.0", "@babel/helper-regex": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.10.4_1593522758140_0.040901824891864225", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "858cef57039f3b3a9012273597288a71e1dff8ca", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.0.tgz", "fileCount": 6, "integrity": "sha512-YBqH+3wLcom+tko8/JLgRcG8DMqORgmjqNRNI751gTioJSZHWFybO1mRoLtJtWIlYSHY+zT9LqqnbbK1c3KIVQ==", "signatures": [{"sig": "MEUCIG2TCtYgPERe3/NSiwJqvzjS0L1iWIB6dzPB7pasd1KhAiEA953sSoqiEeYmeh8HcqmD/cfbyLD7+5LCOiHqO9YJLms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lbCRA9TVsSAnZWagAAFSkP/jJOEZj0iZ0QU2ySlgdy\nbC91QuYs7ORdtqxlW3pm9u2q96+qBIMGQkDhQhi3kDlUQHqkhpRgn0aGKUw8\n5tfHLIU2HniGp/tmBHuWfHLqhVqj9oK9moRTWnTNZ/t1AfSlnPkDdp8u6Sfn\narE0cSz2M7gWuE2awjdMcsVGslFfYDFHVMfCR2jkPX8+3KKLo6m4tO7ASZO1\nwWJ/TdZkxInM1AS+cEI54V8fx7AIUaz7yN0v/1SF6xn3Wv+EjEzO4mMkXqBF\nge11hhF2BrLjAFUDIMbOQ+1srautdPS/W3PG7sFXGfiafW6dfDIx2csunh1I\nFEhkJcZRalfi+CbRUyl7+aOw+8flogm2r5EvIpAzHHB77QyQ08yYJAjWe3uX\n8Edtbu/8+SjYyVNTQugqgWr8m1cghgFjMXEZ3i3T3bxCSj5N/R/O40LAy0qL\nXc6NwAhPLYZeINnkyECw7Y88cXTya/zqzWacbPXZ/TIEmSqzdKbMnpL9x7Ef\nXfBydp1kOfqpTB5TUNjwEzFvAqB7bRx+t1aIQxJiwuwkrkOl4gxBzRfibqEv\nZSOILovLd7CqdyaDRDLEoKFOYSCK3vx1fZXajhQTbhC88v2IEnb1LzjbEIgO\n1KdIglelrLfLnFAanFb5aY6SaL9bwP4dji3Pah8M+BBvn8Xo/G5Arh+OkMp8\ni2fC\r\n=ynTD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-regex": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.0_1602705754758_0.7830036783227137", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "18b1302d4677f9dc4740fe8c9ed96680e29d37e8", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-rsZ4LGvFTZnzdNZR5HZdmJVuXK8834R5QkF3WvcnBhrlVtF0HSIUC6zbreL9MgjTywhKokn8RIYRiq99+DLAxA==", "signatures": [{"sig": "MEQCH3LQx17mE9FzZGJ2loARX6PRpH0HPPJB/Ye9jW83rXkCIQDWO00xE/Fvdyt9zgFcGgmXWoWXtCGqCMWNnRdbK/EjVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+XCRA9TVsSAnZWagAA5jIP/R7FsyALDkYN1sfAy2xN\nimLqDKGCUpMFpGYBWT4srzCuHatYh0hCnL6l3diWguxzIFzZFUnTZLGsD7qv\nu0sR9TUK3toP7ptwZSLf1Q17I1vC6hHw6d0US9oN7AKTXln0OU0bRgds8PfO\n5lZCLRJicc2aNp6BbPfcssxdD/ks6lxw9U69FqSCr9689LyDnBQIVl9xFBkC\nHE2nNybQikLkaNVK2OAA57JSm3G3hYawB1rsBHog2LA363UiLINKOq8oyU/M\nu9O0v8TK9SxsuZLvlr9pFytzifctQF/e9i+VXJk6MeHqs/uuRniS9GHTJLS9\nmiYVWPQQbP/uXQ8fLJT/xkrkD2hWG2Ae3tyW1DB/y5VAd5tg0CtFAViPr+z/\nL7wOa1YgG9lkzAKG8B/tpMf5HfCVvketFNnCClyhXMDd+aD4e2psj92UZ3QJ\nGxumvfPf35I0KJZal0HW7zo4OYgZ5nwGlXmLz4kg/QCYVkLwE2+QdJ/5oura\nIj2RN1rZEqGnZLBLl911dPr+AJLAftXPGMO7E1im/57ATg3dIC7YF0tPUvqU\nDAwN2X5uwzjtgMDQMrCBWyOZsZTlIfMp4mDGzMr0yxDj1lYF2oikmRgs4MqJ\nforgRKmthPHysNQ7oZZnZdiqKW4It91lRkEdxtk+qz76qTxCromcrhu1m8oW\npQGw\r\n=vJpu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-regex": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.1_1602801559329_0.0742135246115665", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "2084172e95443fa0a09214ba1bb328f9aea1278f", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.7.tgz", "fileCount": 6, "integrity": "sha512-idnutvQPdpbduutvi3JVfEgcVIHooQnhvhx0Nk9isOINOIGYkZea1Pk2JlJRiUnMefrlvr0vkByATBY/mB4vjQ==", "signatures": [{"sig": "MEYCIQCDdzcMIIrQLarRDwoQBA9/UTxcuoutUSNIx7PnsiqgIgIhAN5f2AGwHzhmCuDdBozzCp2usDnVnAYHWCsJC7JUU2Do", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7726, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+fCRA9TVsSAnZWagAAbWUQAJdYKR1c0wlpP6szhpfP\nf/ZlKsuecmKNMPM7J5UyrmQbbryramVFhcIFpiThGKA9E+a0pm04czqO53zm\njtX0AOj0uSEGCajJYaiYMLFY3KbKaut4l49+Qo5dL6qdlrt92GXhZP4MIEoR\nDqPeWQhOxxYha/IutR6zGVgA38AIOJ7AsoU9lLMQ5k2lNF0utrzwdyt1JqIH\nQN1SkAzbEsWhHn4aDKa8KJSVBHQ0hZbeg6EL9IAanZ00zo2L8JtgZAm0059a\n8bmd3UQhbXwIXLmRe1Urdd6oLkDCQ+9wF4Vi/J0IK/rr7jVb1SlU21RN/6Gl\nX2kmYFP85XVTwuaf9RV5evGVeB+TP9LftWPVXdjmfN0nEaINQhfZEi6RnCqn\nTysYlDrTV1y7FEMySxaXwNzMTfY5TGTxZ47ivdYkKj3ntisPiXKzn3kdY0q/\nBadCG1aYeVwmcQQJkpgx1WowIVifKqe0HXDlXqK9VbrCO5eHvqmBCeQgSNum\nwwmcRQ45kA/aqBUU3clNKArwGHR3DRDqDHyNOvkmeOhuWgREKB7GC3BqOky6\nVKp4mFujuHIACnz0CwPldzH9hmvxskGz2PjS2q74aoQDSDAWuCwx71T3MoJO\nXNw6dTAGi+JNfaHOOnG/GGctVfNytXEFpQECjGMQCPYd+pO2acSFioQc3zQp\nNLuX\r\n=6hO/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.7_1605906335171_0.5037697224947897", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0996d370a92896c612ae41a4215544bd152579c0", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.13.tgz", "fileCount": 6, "integrity": "sha512-XC+kiA0J3at6E85dL5UnCYfVOcIZ834QcAY0TIpgUVnz0zDzg+0TtvZTnJ4g9L1dPRGe30Qi03XCIS4tYCLtqw==", "signatures": [{"sig": "MEUCIBvITzUxbkZkMhZ953VqK7QTT2dDwLf0Q0fdPRzqCtxtAiEA2kkNLQIrUZfO8b/AFhR0F/c3sk90eAFxZHm+NOJBT+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfg1CRA9TVsSAnZWagAAEisP/1kl5qz4CZA81B3UXiBk\nZ5Ztuck0uW99M0vLrUWoJOJDh5ofOo86tFDtw9JltTz4JjxjXGm/RzlzpIjV\nWwhyNKLtjX8xaLux0WY1WMXluZjxMV4e/Sd+IyaXMzrsBRlO8ecKwgsyEw3j\nuSecOiGlbdvF2LYWlj2Qsfi5nI75Gbi7Dx+/XxEKYOQ0XuhTQtrlOyDcir1R\nJN3BTpwQ0wLhokwCf7xfU3tE3W4jFgthlcSkV3Gz82saX6XvLO08r7KVnRRL\nUexsEIx7UBKKURw+k14DgBIVMfi0mXCajWWbbrD3Cd/cKRkjQ+II+ZjzFd6+\nHw9eOAYM/TcWTqIPT5NgLkqllBfWBUZ+1A9Jm6yPtGnERS7e+97JO+E1kaee\nh3sMQ0eDIjfimfMlFW+zAtCiYtKgY5HDMzog1w9JGg2MEBhSQCBrKPwOa4md\n7JfXzaHYhQmugI/BYnEwg88cZN09xfLlDXxde6YgVva/A0Iw7B0YLB8TKl/Z\ntaOm6tpA4xuDWGvGiY0ZVsOMAaIJe2eidZakO/W7VSk01pQFaxQOWGb/K6WV\nMjN7lmgZYRwngJxCfNwoeMv2LbPuUORddhvxZ2Ze211RxMCze5AXCR6qz0dx\nfIEDHjDOpCKD75lA2PIY4Y49xf2Q+puI9MR60OFXh+LGyOj9rAwaNt69IW7m\nLSkr\r\n=YSxh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.13_1612314676915_0.1631306856792436", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3b31d13f39f930fad975e151163b7df7d4ffe9d3", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.16.tgz", "fileCount": 5, "integrity": "sha512-jAcQ1biDYZBdaAxB4yg46/XirgX7jBDiMHDbwYQOgtViLBXGxJpZQ24jutmBqAIB/q+AwB6j+NbBXjKxEY8vqg==", "signatures": [{"sig": "MEUCIGmTjRZPtaqIOujRq+E4puW32ftChAl59xqNhBmUNo6rAiEAzlIwuctXd6LKiyP67pdFT/C5673iSzyyU58JxQQkUNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPbCRA9TVsSAnZWagAAfasP/0ZyeVeOBNdP2XQHs+G7\nHJ2RYoV1537cXGGWyjjBJvakMHjl6/iy257CdtsN/9mMhRcNniVB+Qq6UTDz\nAM3JGWy/XiOSsiJD6vQq7EdgYSBxscF7iZuRLt+eR2vHT0pTYjdSfFbC79qL\nKfh5EpUmIaU/JY3cQlILiCnBqNNoCOw+L3udfASn+MTXCjK1GhvsSypOOfDn\nN2wVUEulD9xU2eWv/c0+XxS8jUQZWfgJFri1ZROFyR0G+0Mn6hjggyD0dOax\n8OWj4TJZotgXIO038mA0BrVLIvBKsvpvJ+atcD7Fki93rrTHJ6eJa4XrCLCj\n8KPQzkDkSEdmoq1IVr57OWnvt3zwWL7zfwnCArKgGDJmZDUQGhMYxwKOsNNQ\nj0XpiI41hyMvWLUc/BSKV9i6tpTQ+FZTTzGxuID1M3ECd2YYkDvHy3AziJbf\nITwhxX4BtFezWzQjsHyV3u7k1+TreU5f4ZifOjtlmyoOFWxA1qVD4wFUEBmG\nG36Zwj//4Fgray3gMxAts//w0ySXLsExUUThA20UoW26aShWD5MQ3QagBxdJ\nSwH+7soDMb98mHIJ2Xze6gVkXwxj6NOQ6xXQ5cBN5UpcrQjZRQN9GuWQKp1y\nZqpmB6ZQPnbHvldvU5d5rOMKvwsWRUnBS6Tf7NlQ8vJTl7Za6h1+iERp3ewS\nQlav\r\n=Sosv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.16", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.16_1613083610987_0.3996205524858436", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.12.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a2ac87e9e319269ac655b8d4415e94d38d663cb7", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.12.17.tgz", "fileCount": 6, "integrity": "sha512-p2VGmBu9oefLZ2nQpgnEnG0ZlRPvL8gAGvPUMQwUdaE8k49rOMuZpOwdQoy5qJf6K8jL3bcAMhVUlHAjIgJHUg==", "signatures": [{"sig": "MEUCIAx/v+feW9fd9earABmH33gFEprDMRFMCyUJrvaDWCc/AiEAkiZON+fJlvXzeHKC5vSZAO2FAPxto3Ij4HwAaioBn+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoOsCRA9TVsSAnZWagAASK4QAJ4voFLUrWRiMXJ53aQJ\nU/EHUYgq0cXmhLKTkNYN1nAecvvWr+thD/5YdhzRReoyqwWWI6JphddvgSy9\nUajMDLwYAampi6/4cyzIvn1GGhBFdrm2Ghy6Fi+ATfpraOkYZKTEteADXn+A\nFkEFi8dO7sMojvfLJ7YcFPRBVFkxgpabtDQFA+jY1h/B8UrRMOhefDC2UOKo\ndma320D2zNGLKl2nQ/zuy0/89LIT1A/Z/kdytBv6k735D+m12pLW0+aeKfvf\ni0M2sIry4uZBq3fAsmz31nVlZr6fw+xZOvWWQkuBa39am5YbL3QYvY9oC1b1\nsdOFzk0ncP8hpuq+23VyOkjUmbAcHkcb8ucpl1G4C0N4ApsHUSyoIrq/ptk1\n3cftk+uE5FgNu+YwYQGFddLOjcaNhCTjlEURmPoWQAGMUV16flkTOqU1owiq\nn0Fh8Oe86NDVlmxMycQs8vXmr8Wn3MCWx63xHUxzayyde5qA3+52EmHukMBh\nGzUudfnzEmnVmKZ4aFja8zQL/jroeBeODAP7IxmHPaE3dDtrzW2q9ZjO5o2+\nMTxrgHRzvscEe0N0DUQFW61/iVxM3B4WpQN2qC0RZqEoJMFMZiTEFRkDNyud\nzuz0QAPgzEQm3SLrgEf9szmR3nDb03EV5Wv2aAjqH/Oyx5CpjZHzi9OOapgN\n+IqR\r\n=McKy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.17", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.12.17_1613661100150_0.7208953270510601", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.14.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "149aa6d78c016e318c43e2409a0ae9c136a86688", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.14.3.tgz", "fileCount": 6, "integrity": "sha512-JIB2+XJrb7v3zceV2XzDhGIB902CmKGSpSl4q2C6agU9SNLG/2V1RtFRGPG1Ajh9STj3+q6zJMOC+N/pp2P9DA==", "signatures": [{"sig": "MEUCIQDy2kwfoPqdKmi0YJp61S6EN/iB+xZjs0zMUGximLwLPgIgXM/919UUFrqimp0gUsy3DjeIRa2XOqnkW057k/LlOlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWhCRA9TVsSAnZWagAA4U8P/iAxlTUms1kPkwqennvM\nUbjwUs9AXfvndK1iCYiQV/HngEJ9TSxYdNtqBSxHw1wgkdbGyRh85OP6SVxW\nrURSuepymRgZGlpL+7hD/rH0l8h3KOdtpRbtpTfxPcetEkXd/QG8mXXPUNFN\nsgl/lrodYnXQflVRNxSuBEK86WVqxHpoohBZWug7DSF3PzNayL6Fu3um9Yua\nQgYfjMD/AL75ymO0Brwjz9QbxIVQpv4UyaDXqeiwO3MIBWxp1KZn6jXEc0Lf\n4jPY2BLgxqS306APYsm8nzJ+MMwa8DrtxGKMO1SnLQE353zIvbV/AQbuJ9Gl\n4GonQc43Rzxye/FRWARezoL19aRNX3YnGlX6XbPK9WMkkt78mCbW+pwmw2wE\noA6L0nLjqyM/3diryfAkLx95UvS7G0gj/lDw51ZJhkHUHF21flgK7HjpLdGi\nJnDSDoFFAipwPwgithwfmfGQiT/r0tEG41k/UUvzbU8lspbkvqhAFfd9ZkKP\n30neXOpP492mt6ttp2MLqNOql/6J9az6NghFyXejubul/1u/SaRHcHV+aHX4\nDNcIHNPdMVVECwmgtBt/oPXfH0PsStuxyHO41msO7VXprKdrPNnv2L3a+n7u\nOMcUElkQGWc6obYDp3ELklc4hpV1wyikVmw/mmniuKyHXqs+7ZmnDJKQcKjb\n0Z9Z\r\n=o4ew\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.3", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.14.3_1621284257500_0.9072262354428777", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.14.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c7d5ac5e9cf621c26057722fb7a8a4c5889358c4", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-TLawwqpOErY2HhWbGJ2nZT5wSkR192QpN+nBg1THfBfftrlvOh+WbhrxXCH4q4xJ9Gl16BGPR/48JA+Ryiho/A==", "signatures": [{"sig": "MEUCIBleJgqkQiaY4atJVAu0N1AmvbpX+sOMJfbcIPpWCSYlAiEAnZQMLamVwiOIRiGRxxVXBI3Ze+FNL7WKIGm9eojPPZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUroCRA9TVsSAnZWagAAmn0P/2FYDkiYF28DiY9fyXbF\nm0FsE5cKyklvJiYczDYy2cWXoyOhj7KjB/OwdBltB2ZXBZs9ri7glRWnPk1I\nYkUdmtEK+inwfdXOtz+CvGBfKAid92yJ5o7RqvRmRtKt14fnbhXvcCghw6ze\nL8hwECch8Wt8BquvoAEASYxjfYmhSBRylNDRltzQBGuO5clnDt1t8Rpo9/F/\nmt+3f11x2vhl3ihwPapHJygqhM92E8tfTmfD7rD8KLrHxGCvOD0ZA+zTxs9x\naH2pY2HtcQYNsZn4/V+JdcP1Up7y995jpkvDuc6EqXxApmMiYNrCe4rwtdJI\nkKKnduia02L6fUQpjOL40N51i1Dj7D5cwG9IB3OcJzrKlRZ2k4lYChVWTcc3\nnv1ZRFmZ6eif1gMV9JBBVWTiaEHdkX+brkz9LSyhTvNcDVwtX+smhX03jchj\ng3VfPVB2RR/L0+vab6qX9qeSISSupaaDLpb4BQN3kvBuD2c6YS7DmTX5JfIE\nzYGcU6iNgB7B1tiJ1+Y5reLCWzhSex4adQ2WEQsCcUJZ4PJufQMbOdHIVrPe\nw8Hybg1X8NJ+5yGVaQhMt862VeTVxmydN6CxuPxBUpqZKjsUdBvuFWDpvS9h\natP4G6FqbtX1ZOaNNBEZ3+weqSd8PVieIK4w5Y+hHOhMoSnx7tbJjnuI1UI8\nuCGG\r\n=68tj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.14.5_1623280360365_0.5313869024080444", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.16.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "06b2348ce37fccc4f5e18dcd8d75053f2a7c44ff", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.16.0.tgz", "fileCount": 6, "integrity": "sha512-3DyG0zAFAZKcOp7aVr33ddwkxJ0Z0Jr5V99y3I690eYLpukJsJvAbzTy1ewoCqsML8SbIrjH14Jc/nSQ4TvNPA==", "signatures": [{"sig": "MEUCIQC67z/DlVYXDD2/BIroq9TzoAHLAxKIS059mwhUjgwyPwIgHwlYgpXBkiljsPgg7pyNtxde+9VgoyE2mKHVTBLA01o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDGCRA9TVsSAnZWagAAEWIQAI4Bo8gRiIFe6rXy9TWb\n50lBbTM1oIneMJFbu6ATWHxcX3sHUaG+cWXRNwQRz9SjtBhN9AsoWeLpWmFF\nYjlw+kDUi2MP50QO7XPCFLSlNN5kAZDCK15D9FOe1UBwANwniXhHNbUepUL3\nIuGIVjJ4FGVHl/HAhG87HhfXCm5zaohv8VpWvWijyzJ6QnNKJi+ORJA/9Go3\numU9imSRQgRO3MGXHBQAJtfF1OQJ6J9c1hncA/e1V/NePSqPLuOhqtAF2rtG\n+4knSxD+AzvnoQMJ01cvzDsTr7jHcIEOEDYlszPIPDfBya78qRlH6D0R9j+Z\nBG54OImArGjUm/U1BtHVTtFFmauf9yqlQqWEPN1hKkatmrNJVMjxccMk+coo\nN0WniyoqbESEt5FXSTCB8MOwsHCbFqpSRfHxwp4CI2fOezH0bwWVoIr6tX1h\nlpc1Abf/SXbL+VqpqX3HwfTh21fI4OryI6IeDvgwawOVgDQ8HN16Kdtafcgm\nlVAY9d/9U02H0HcCPm3y2CHIYXnR8Ema9u9pkSNjnChLtnt0ObAccD7cDRKy\nnYxi1WLi3xiM97SbdN6xPD8sxfJc8qjl1Tc+hzZpIR+16ALXu/BMhWRk6+wZ\n//FzooRCXrSy+xJEN0dN1tc6/0ZfYAKpup94cSLpbE3KZXqwZ1i1oiZ7HV4v\neR/+\r\n=eCn/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.16.0_1635551266237_0.4550545242166846", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.16.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0cb82b9bac358eb73bfbd73985a776bfa6b14d48", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-fk5A6ymfp+O5+p2yCkXAu5Kyj6v0xh0RBeNcAkYUMDvvAAoxvSKXn+Jb37t/yWFiQVDFK1ELpUTD8/aLhCPu+g==", "signatures": [{"sig": "MEYCIQCpG4PkG1o0BSo4r8e3lvfiozePINPcqPBVKzoF2zpoHgIhANg8O6DTmw5DbNhFy0Cb1sjs1VS9SBgf7JZqW6j8MR8J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1JCRA9TVsSAnZWagAAZK0P/0AKf4jVUEhzYs0E3vAv\n/ZqrHw5OZ7dvmLPcbCfb3XlIYUhZcWm0pEPRndGsa40V/Z2qox34BP5/cT4k\nodQpqPwzRulTR4ZNMK5WeF9EK9ClwyMklA97283dBCHXtL6Mr4jxQiuJm6EK\nJjGxrJXAsNzXP3FEpXiwX2oROzOBrP0zvg5UOaHj4lxs9Jb82XdSy6K8IBzL\n9pPfA8hTQEifb3Sgi1Pk6p/CQUphfBQqK2RMe0QQO37JAc0UU3N6yyD/+GC+\nOP74jOYDm98i7tZtfvgUlV6qRPgH0s2LtN8o8GWU8P+91nxFq79qMuhTHWJJ\nYErHuO1tb7jiKte8x7ZFgRYIXjYxHbAGyqfPt16uu5ZZKaGJInwqo+rNWGRJ\noAsUiJ1RoJqjDR7ncnemkYqMf+fPo10WTfo81K9JIZvsZXY34GtP6W/2qdEr\n9aUBTNp1RbXqKcJO5xGt9cJwpfUkcCj2bySSirbDJJ3F8y47NuTOgl3YbmOV\nPAATFCJe2InjDUgTzS4YC2x1h3SlirLX0jmIDk9d4qBrRLjF4F44vGN3Frdb\n0oalbrspUnuxXK5szCOE0e452OH6leLVo7ixD7EqTZpLhr/+mCrEwEwUsBc8\notk8wGc1LFjhFwTJky2u21BvKIko0B/a/2/r8HZC98SL36bGQg951cOHkxW+\n6Mhz\r\n=Kr5y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^4.7.1", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.16.7_1640910153260_0.7763172523145925", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.17.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1dcc7d40ba0c6b6b25618997c5dbfd310f186fe1", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.0.tgz", "fileCount": 6, "integrity": "sha512-awO2So99wG6KnlE+TPs6rn83gCz5WlEePJDTnLEqbchMVrBeAujURVphRdigsk094VhvZehFoNOihSlcBjwsXA==", "signatures": [{"sig": "MEUCICRXuOfEN16YtjE7okeLEd+dU/PHaGAROp7vAgc8YohrAiEAhSPRP5T3QzTLhOo9r1YM67FPTGZ9kMvckCR/0AKBvPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4LCRA9TVsSAnZWagAAFmsP/2cMbenfFlzvSkXPK81M\ncJQd12ptLohUkXhi16noF1mcDVV4gvVN1zfAPFzQEljNAOd3M9wsKOmJgq/W\nbsvcSeEQC9XwSYm53AXMW36J7qKdpmxLVGu3BHO3N+KpxxoYcnA3dPQtpCKe\necv0TKhmYBRfwi1wAKk2l7A63k903WX9yLgFsUU2abqYPQpBGYnoBj/LaQid\nTuKrfhhibAxrAYsTUrd0kLJ0pHxmM46Ylq+QV0mrXWXwa8MAsVgu3RCFP5b4\ns1OxdeU1isW7DODrwma18KzORJZ8nIpfs6Me5vmYSRu9/jroQBfgoT+NXpmu\nzOAcS/PGmCjnJhZsHblM7tY7nJIymstWlGQx/uoBQa81VGOgGGTDo1ZHAeZe\nTJ8QqOMjIngBuq4AwMqNDPJBWIZRL6rmdPxOVItjhqwcQ87rsZbLzdGs5MLD\nIKqDJHVFFC4b0fmuqRlYNQH95iqGVJKdk2EbN2A0FMTtF5e5+g9jtOivwc4g\nNyy4WDWlZz6lLX5/a2MUTn25wUcaKo4Ku//HTM/fLdofUOQD0U1hZxCM7uHj\nJu/vki99ocn8XY3645wsBnGyqa6Ha/nNUaOPrmSa7uF568yP7dC8rCIrYhS4\nOajifhG2pWWQh1qoePhC6WUgTxzRVJGTqj3joos7pFtFXLIhmoLHte1E5ba5\nld+w\r\n=25nn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.0.1", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.17.0_1643843083747_0.6992847408125917", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.17.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bb37ca467f9694bbe55b884ae7a5cc1e0084e4fd", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.12.tgz", "fileCount": 6, "integrity": "sha512-b2aZrV4zvutr9AIa6/gA3wsZKRwTKYoDxYiFKcESS3Ug2GTXzwBEvMuuFLhCQpEnRXs1zng4ISAXSUxxKBIcxw==", "signatures": [{"sig": "MEYCIQD6OVJdHeVWx/zWzfYMlWcWqzT7dEpSes5S3cEwG51LdwIhAJAIbUvI5b7/AN6cAIMC1A90VmsPkgPxwUtUsqpdvY/W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqa8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIrQ//bFzSplJbSqH1k+qEgkWfIz79WzNY6KdgsNVbklyK/fX77Zj/\r\nZjn7VSjVgz72kcrxI72SAa/WzH5TWqa6m6bxLROTse5cJR2J+XV0ki4coNFQ\r\n2yYChwlto8WKBG+/iacXgXM0PLwT+MTkofPw++2KlNY7WP+x7N5OF42ITyJi\r\npw2YUGP4fXKWSH+E6Q4dWcw8+dmtAvsoq0ZhOvqg9vrd9qI0xByO29KBqXh7\r\nUL3nwbgQ5qVsKljHV2LAviJiT1loLO3Y7KUIfvuPQ9OTTcjNSu7IKlwYZm1k\r\nxuDJieW5i2twsmokW+CdYdZ/QxhHF/S0IuVcrn1jdBlK/QINGrMUrvzofcqT\r\nAONtGkIadMeOZv0rXIxeAZW5Bp37Pi7MLJ2bBXCe5kYRmhhp1xvLfMPga0QJ\r\nGFQ43m2xXMTaSn0lHonNo2f36EZ/9qsECb7Zb1B8c01Y/RGPQUwqDcLWZbAU\r\npWV+yTBb4UlI7l+PNiqC5JQQ2qxxwdEGdTcgPgZ9aktyOBLjsl0Fpbn+7OFm\r\ndtN9N1/GYuvbEtRaBuogjbYHXg3bdDGNlYuBjk7qSgHKWl6BsPihKnIU4Bid\r\ngTqRg4jovhP+2XhQGYPVVZb9fWHNZQxP03adTE+r2naf5jd5hW3ozJsVAfqy\r\nF0Rmy0WV4AZMilghmc469vGfYfxc5OOFtFE=\r\n=E45g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.0.1", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.17.12_1652729532606_0.2147458454909208", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.18.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3e35f4e04acbbf25f1b3534a657610a000543d3c", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-7LcpH1wnQLGrI+4v+nPp+zUvIkF9x0ddv1Hkdue10tg3gmRnLy97DXh4STiOf1qeIInyD69Qv5kKSZzKD8B/7A==", "signatures": [{"sig": "MEYCIQCEzjNhOsy2apoLRtK1SRmIqFRnC8vaRhcV1ESW2dpmvQIhAJH8VYJfk2YxHsS0dldKtK5FTm1k69VWyh6lDBIf7AYP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugn5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV0w/9EaPIyh6N5tfMzLz29mima/d5PQv/C6rv21eG3Iaqqd2vjydT\r\nVRlwGhpGimmddRpm77VD/ShbVH4GbypNGprhlNW+YKGBBil8//2rEr3XnMfR\r\nvqqlN2H97f2/IJrcuWx9Mp8rRu6mBDXD2mjlHgyEBsdK8nntu8/wG320NyjY\r\ntgAMiU6dedqCp/sB9rq8tVhFi/LQ7JzUNSREiN39APTVIrmBAJncEL05+Ack\r\nRVlL4s7R8MhpW3FnlQej3l4LAikpjm6pzPOUbDQfAWxEZ7uWaRGAdL/WWN+1\r\nL08HOiqHEQssGvphWSGjh5BpqNMvR5vwznRYk+HCoXIog2t60034tiNemSAK\r\ndRFzpRU5hkV6VWHvsMTn8M0+oAuA52APBPsnnsS9QyWb7ZDw5e2gXnQ7F+Ww\r\nctamUSt7DDjoXwbx+o7W1HpV0w/IwYiYDXGFhxKMo09PuUyO+9hc/lR0vdm+\r\njj8pJzivtVxRCtLLaxrm1DZaS6BnEex5/HCosoJwImLJLsbKvx38XmdUAzbB\r\nFJOxycDNPtTkHqO57HDI9TRbOvaXl+8GOZZvAcGGAEbhRsXthjyYh02Oah1a\r\nBOYfaFH3dKQvtbtJa5qryeP9N7fbOqZNN5h14ByQA8JiPkRh9FfqJcXBp7wd\r\nFfVJJVMqNugn1YSQv17k2KtzKWkzUCduK+E=\r\n=xva8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.1.0", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.18.6_1656359417685_0.9648559097135572", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.19.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7976aca61c0984202baca73d84e2337a5424a41b", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz", "fileCount": 9, "integrity": "sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==", "signatures": [{"sig": "MEQCIELCd4eWfb6Y6FaUWCOzZUsCjuibbUsKK4I14mpkntu8AiAUelkkvM9U6Q9h2zreBX51LjAL+QTe0sZmalHygSNgtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGVw/7BWRq/CG/Zv7g4MmU4WVgwY2mc0S1vqIPQxI0sjz7xH89yals\r\nlHKdLdUPcKFb37ZIZz1JaRDIhHLLT9N7IalxVRd1vgzMhX+aLusfPbAyLVeS\r\nrEmEEf5ZZI04BAumtX5Z1cqNDLmbN8MryQBy1ZQzPz9HNJmQR6mb5OnUDnd6\r\nEb9gw0vPKN79QMibV5fmevgw7yPwW9+Gp3dJ4tt1vpcz37SJ7L/5XuiY05pm\r\nS1HJ9CWkAOjYn4LSnZC7xugTNQFfKYaT/cH71emfulcfNo5sRh4Sw0XidDzs\r\ngAqOurRsBYDcdcAH3dmEmAvkl3z4mYKcCxx/H2sT4t+fQBkhKk8ColXpQdvi\r\nkZWm+CLlrTGVj/GSwZ9mw7aLI0G1LBzEXWnTy2Gyi3J9z8qBTfLKq0FrF2CK\r\nXwdi6mxdn9S1QuetRG66rAQEiu0fpTukw/vpghsDRc1r5/Jr2WRNt2vXdHKs\r\nc8cri6HX/epuRpTmux8LswkddHdiqwCwPBPtSdaNp4uzbJg7iXzvODEVoh8q\r\nI491l3S/YcUFa0hbKF+Jt7X5jtZzyFekGrKdBg716+qU0jrIt1Z44x2qpL6n\r\nf7gx3tdo14q1wx7kDct6/pB4xdkh6Wi5ZPkATE0e0AW785W2pWRj9FMkVZlz\r\nA12xB4bf+F/y+PVG+CvMmpol6O2Nsvo0FQo=\r\n=kbFz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.1.0", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.19.0_1662404532772_0.8216830486876454", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.20.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5ea79b59962a09ec2acf20a963a01ab4d076ccca", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.20.5.tgz", "fileCount": 9, "integrity": "sha512-m68B1lkg3XDGX5yCvGO0kPx3v9WIYLnzjKfPcQiwntEQa5ZeRkPmo2X/ISJc8qxWGfwUr+kvZAeEzAwLec2r2w==", "signatures": [{"sig": "MEUCIC+spFhFbV23PPjxNI40n4nyJUHfncAWZE/f2fmNwnnKAiEA0/VSGVBPgPVCxNI4QoiKlhkOJ9VIOrub0c458ur20oM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjwQ/6ArAahMEt6+rfIKVDEKnmYuTLpaZqooG1vA/zFgFwA71UU7XO\r\nCDUKXuUymdMr9icUsgkeGKtj2GjrJDPChUcxBqd3ajx/1R3Cv2BW8uFf2zIK\r\nUipS4qhZ8HWm0a4LViHcfJFzu3xh5vfBmvLFlvThT1I2E3+yWcW2TaWiHqph\r\nLZ0foXl7KUtStnrv2KOZv2r5jVJXsc567LwW2hK6IG/qbDKms4n0VCfGUdDU\r\n2jLNVjBSPlwdBYu+GYd3AyxuSeG0nSpPTlKHcGzhunHVzEuMNAy5VVEr8Xcm\r\nABJV/vpj0ki9a6Q+KVJprFatB0Czu/vkYJkcth1wKVFqYAHEiIwlvZej/DJo\r\nmSzxPEc8HHClcxEYPzj+t/9srgmLL8IH4q+0OtiuG40yF9Xe4WfHCqMZ5Bhj\r\neyZG0zMZVSt+6MFE2vo/Ohmpghev9cTDPqxdewJFC4NQAhm0fNqM8TJsvbla\r\nfjtDcFL0aU2bsP+FJXkBcPDCPXMytXdb3wo2Vjg8aqP0dFME7cNoHwt9WTEH\r\nr5LkqOrFTf6LuIVu8B3zCGMo1y13t6ZYu38oHDuy4FFmXW4O+cdKLPPpCTsF\r\nksWsrCDiRk43ZdyDdC3C4p2mGhYw0kU5YW1wsT3PP5orYU+XKFJdlg93blVC\r\ne3CY91En/NLv7IURwOhUN0Eoj1UgJXtdkS0=\r\n=ikHD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.2.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.20.5_1669630362364_0.012034266527676696", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "53ff78472e5ce10a52664272a239787107603ebb", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-N+LaFW/auRSWdx7SHD/HiARwXQju1vXTW4fKr4u5SgBUTm51OKEjKgj+cs00ggW3kEvNqwErnlwuq7Y3xBe4eg==", "signatures": [{"sig": "MEUCIE9j+383C/pKSrj1rlXNn9ntT8AfFx6qixa0O/SfOaUpAiEAoKyEvUQoIN6bTKPkOh9s6C2nuR87UEIBoRoRD2t/gHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSaw/8CJ+eRLlhjWy/LNaePC/lZwdQKNT75Zin+2yfav1EDs8aVsXP\r\nkLx0Pex5aLEtrx5qwoIpdkiM3PeF/lkGVN68ydkdhKuC2WvUydlFGKgxIGm+\r\n/DoWDpWtJ1cOT6Mbk+7AL6U6jVEiqNMUQNzFOGzwP3AWFTLtcsx2FrNmL7zS\r\n55uo2KKbGL/CcT9vcH4OnteyHCtruKEpxo7iU2QaLcCwEyietBOpUlFl9LR1\r\nYn0F+IRAxWxHN7SOEwPCq2PQd45R9G7q7DydSVPU1F0ahxZep3Zqfs+7Fz0r\r\ncXMkMcvmh155HXw18eLKj8jTaP3WrhpNd8Nf18svOaz699v8SXbTKlkgbBYD\r\nQiIibZrHuLod9nFoXLFpYIMuQ+HmJZeUdCoQNxapAIR4+t87T72PXJ4zKFMf\r\n8p8eStY6AsrK6ifwyesbjqsXkovuwpKQrJ9Tq00I8g3csY1wMwoJOoDvyB+p\r\nJFrAN7Ou71F/YVoo9priF0K/wFDb4F/vtcM9h7ulqCAJwmAtcpe6C0ZRmJQA\r\nt6VBgJUodLBWHipeoI//SkS8cYU7ty1RRfGWcU1/gmu/qeGemf5pypQxPWQW\r\nswaqNYU+F4JrRUZJLPfx80JRa4G6jdBBmhkTogoVkHOkWFym5JQFEd0MgL26\r\ncSjMYjWrmEYcGWm91LKfBuEqvGO1GWf+Ajg=\r\n=F2N3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.0_1676907052732_0.33476726770262966", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "40411a8ab134258ad2cf3a3d987ec6aa0723cee5", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4.tgz", "fileCount": 9, "integrity": "sha512-M00OuhU+0GyZ5iBBN9czjugzWrEq2vDpf/zCYHxxf93ul/Q5rv+a5h+/+0WnI1AebHNVtl5bFV0qsJoH23DbfA==", "signatures": [{"sig": "MEUCIQCt6tzR8DoJPBlyYmNz56mdoI2/dk1xA96RHPG9HjPVYQIgEGiWopuEEK8ssi+br+f2VIG5Gk2rU8US+LrcFoJV8mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqF/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbBw/+KvieR9H9V4Qbdci/Wch2+pY7U2t/g7jbsYqLNu7DGKGEy6CE\r\nO3c5pWadj7QPmwQ/spCxpB7o1xHKmxExIvNoeRbBIkemCYd3OUOdc4VnZHi5\r\nQU9zZfIWEI+atkBYCBz0N6VP9U9DznFws7s6sQZ9JR3uayhhd5I8/l/hzpTF\r\noNEHLufXiwLI0KFp3fGFtqcafbWWAsZMVyM/oitn5VfA5XJYYWmvu58qebvv\r\n5JnSuO3+AyQBYRILJpixzAghNqhcHOHsbnSzJGvXf8BYdXmNX/Tv1xGUje/y\r\ng0WU6Na0JhOPVjSLYVvUgeQELQ37Nsbq5axNs/EJ33RLHRw2+uzW+rR4YVSu\r\nct7/kxZJhODMoYGAxQROwG3t/eEfkcrsh6SNYmc5usZhTorqKo5N1aZFq7AL\r\nMOnAkA81ymJt4xkvnV6DKRmOLMox5gjawgHBtUf3YkPQHfbMCYGsRmLfu+va\r\nnehCkKljBZEXfmHs1BePF8lbZ6IRyY+HTTqFjWG9feGI//lJWuzJP1H33tIr\r\nExhmkNJtMmGcLfuP986PBlyfr2EOPJjDCRWi60DaENJGymHRSqKCTNoGZOu/\r\nsgJGT0MBgbWhFnwo8SJVvLf0NDE5QYDGNsaETqQHSDijv7NCcMKXCEQgpeRi\r\nqXQVjb+atD9wjstBaD/LwyhQTMc2yRm4LB0=\r\n=J535\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4_1680253310802_0.5415899985670156", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4-esm", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0029e58e8c37bb146c7854ea4a46321c6c2405c9", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-AqI4OhRXGrrq4dgwHOmpVkU/OUaNOJKSKjN9GdnsFkmCfX26b/ZBWnYjlUoEizzvLlnjIA4aMC+xNod+JqvjPQ==", "signatures": [{"sig": "MEQCIBAe67MZZQ6tNu5bvTou6OVqvDrOPJqXjgAoPRpKKIWSAiB/i8YhaKrNCCyYbyJo5i+yWi+tipTj4SimsIh4xAdzjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU5A/5AGc82virk4OAC6mnyCkeW7Nb1SnynIwVX9rNlIIo9YtYvcCv\r\nlLQlYNmP2SPOgAUl12B5Svo/VvvqDVgFEAQIM6Bisy4hSnIyu0BZR+CHNKT3\r\nIavf/hNp0TXQH5lHpdJ8BWi1SgUiOFHY/YAfmQTl73jozwYYJMxO/DV3hYTb\r\n7FraWM5Jm4VoNi3zrpY6Dee3OzrPlsBNWK95OWf+F0KDCS7Tx1LB+0m5+72s\r\nlSuHdQZFqBkEm9CVeV6mv8Pe2jJ1xyu6JToGgugFpMrAq85H35/81ymmcd9A\r\nSdlnrjNKJmInmX3SRN/tAJp/HH9E+0hPmy8IQtoJqGNlHjxNhp+G64Ip5EEN\r\nc7OxRggxvCL29DylcPBKkmjE3L6s7mc2tChlwr64DMqA5kRQzCWKqQUVG4f7\r\np0NB3tljwYr21/PoAjzmLamJnTH5N+7bMDCkRtloPbhXZGX865S6ISKR9t/R\r\ny/PNz19mKiCZk/3DVDrbm5diukrPzZgLZZdxuOE+q/8zxw/q/a9KxdqcgpVg\r\np6wKNmzmcYctG+VFX1JrtilLC1WwU+8lnWTU2YoTva9OeE7XsEgpjSqES82p\r\ns55QzgCkguekimcEtdb9GCZuGhQaOIh9+lFPCE6p1FkdPMNuIDQKTk9zvIAQ\r\nqaaOT4f8C41UMKprgXbaj87Z/d/7y4le1Qo=\r\n=OC2e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4-esm_1680617382218_0.7204489625201402", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4-esm.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fec1fbcd0ad476014deb406e863a044330ab9728", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-P0P4zvdsMqm2v30rJPGzGti67orkkM9NAmIXkREj55E/K5f2TxMv7KmU3FdxlIleF33PirBiTlK8qROLhj9b6Q==", "signatures": [{"sig": "MEUCIHIPzWQgIB0GSq4Yx8AfoFiIUvGvGOKYMhxBqLYs81rZAiEA8o5yc/FNbgOoO9UpK0QJ2bCHQM8a5/uS+ZTwv6QB73U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7cw//Vh965Sw9X8Vtz6GFj+MjKp9O5EmlBvtzgpqrfK/kCdiONF6r\r\nVt5LcL2er7tlLpwDLxEIsaCn/4uPgv2AzuybWdt15nhB3PE9BjOJdmiFd0cn\r\nsFjrm1v7965nnx6BDFtXdiNQRjsfrQSV6wzntnmyQpSR4JpyXrmTahfITPfC\r\nhCF+piqJKaxWJDinn9D3ui2L+MjfG+vTpfBXmfcajFKrGAFi7nSUjgN2H5+4\r\nbUIbjmFCvy6IK0uFoyMMdBRFHwESJE6JhHTBU6SS0D5MivGs3rIv4F8gAjpo\r\nhOJxT1S7+rgGO0z0BncI8NuEOZA2HquHk6KlJ46BT6GkfmAc1NJs7f8X+O5G\r\n96oex4SwDIZZKXAFi/6FLujeoWb9XaV37Dz0qmVwDHxtmHTby7YckKdOAhhn\r\nE8it8Jm5CgkGTvj8wnsfoueEGuEu6NIrHojXGtZSDDnFvNiDAl+gkSrgFXkY\r\nWxNrj16DOiNDXcWv7VLKlxRVf4uMavEeCrdV6W/OooDC15r0ID4pkvDD088i\r\nIC1dB6PVL0bFxBE7ff0pxnAdhtH+UH6dyqp/ErtntFGLqo8quFEthMR9uIfc\r\nKzvMryrllqIKE1diM1we8MI1f/YkWiofCgLtyl3jT5iLOB7/Fh8GaUBGutxH\r\ngW112gJhskvPoK9R2XaBVFzVu0Djz6oYFpo=\r\n=9dRc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4-esm.1_1680618098452_0.1401665950925879", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4-esm.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6e176d02e94ff07529aef566929e63d9b8f4a5f1", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-JX/NZHJ794Aa5KIXTHD9dE67cEsbVM/KC8OtoQHpA3zS2TYcS9A3Yrm7opdtHlyVvEMVqukgXvf1q86cFc8tYw==", "signatures": [{"sig": "MEUCIQDL5ygaZjX7Xi8Fwjp3QLHKDlpF80ZXmA0Y8TXUMsG52wIgT5JABa+3Ux4hHYYOVPKmhTgtIf2v6WhIlL+wzveecsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDamACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEXw/9HIgIwvQHPiJg97gsnTSAGgyJzlY/uzpJ33pluAHTifnqFyBx\r\neq2Du8SNbLrpP8LchH9ZYkwTr2QRo4DNYbyAlKpg/yoCrDtLn5KjVQFAT5QU\r\nz+l4JDifBOkQ5/DpVij4De46vJ3eNFZ4mhyRU/T3ES1rA3rjAx59H40lav7t\r\nk27Hz1HYSYx9pIEyhhqP7lfnAiyIEz8pf1HGby1qs4d8mOxlzaf9alO3cG9c\r\n3gEDvcIMhGnp8VoXXUU7rtiv1OJhtB24FUvlTEPI3uvfgC7TiEcscS7SyFTA\r\nui5CX4Fn5qD4yj2GZHgBrVonij1TcjFwTQqz2mL4iiU4fq18P5CU0q4RLdTO\r\nuG5yia7CkXEKRXh1gpq0mzU/l+F2F8/OEBDZUz4cL4FxZVkjseRByn5ydwHc\r\nVx6Nmemx0lH5OgOHvAeYxi4kj8sm7011vZemtsdb0vxlsJlTdiTmCX4BmBFM\r\nxuYp/w7xWVzwh9YCcsniuKqF7lqeIBV5hFJYH/aXSeqhnfDcBtAB3/DsDxzq\r\nz7CZJKT9w5/u//SIGORG0Mh5iF6VPTc41VqyXcwffIK1nTxBFZuJ1f0IVoNm\r\nZ4GiyEGJDx6ijLmiodLNV8E7MuMU17VBG2ZwuSRnYmUfZblqUSX+ejZSl1sy\r\nP1SByOirPgWyROmowM6G3BwwIxi/33z1vYY=\r\n=w4UF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4-esm.2_1680619174409_0.380714720191059", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4-esm.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "890a56bc027f1df8ec423f5bd749e70bcf6f30b6", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-++5Zew7GOnKKfZ+iGCXMOnMXtc/xDnHYKJTnJMq9yomqVt4ojBlCofWQAtR3gschfV6+pAEv8KirLBrFvMj7pg==", "signatures": [{"sig": "MEUCIGjeasckIVO/HnetunrodZQojT2zj+wMvjC0tQf33VMhAiEAiVUMswMlrQCNsxbkt21hUKWEI8oDV/W3dAy3VUjaVSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohDw//d9zBu5YqYbcigeS8v6KLhhZw1aHko5dC51DkZ8yF5WosfRSn\r\n1jGT938LFxLCjc0Ux0wJi88WVodCWDFMGNKoyjbVa/oPgrmrlb5YiFlYigmH\r\n1bNqTlkx73nNmyl4YV+GvuF/lSqB1ttiUHTeBlBZyFnMYCj6QRCo0f+8COwP\r\nyDP29IE2mRvyAGHykaQbC/Tyi6Yzs4NSGnGoty0xhZzsSX6ZOwYXEl9bIysL\r\nS9MLbeL0PX6nSXQuV0calQNttclSjhFkwtq1sPfE0l30AnM1ApPz/XeZRxmO\r\nMwciKG80FY0F2KDdtLVIEvnSFDawO3N5Q/gJZ0wVwLrJ37ZWKgwxfTNoxuTN\r\nEUCkfsNNyMI9iqOk6psvFtA7xhah2ikON/jfNhz1OufAGy7zXIlspgJ1l+iu\r\nuOtuGOMSBVQ0zqwsk44LYICgeq9/XJoZ+yMF5uNBvSIbelVqGKg/XD2M+qKJ\r\nLtCUimgSYq0D2nbaglkemJFJpprVufGQYzvmgSzHyNtQFxWh5tqEsU43l5Kr\r\nDEBJ8VcPXmZO4SvTytSAHVJl2/TF8XLTTqAB50J+0ovWs9yOswhk4IZLfwYr\r\nQZmnIuiV0XEK8DI/nu55EtzqnJNkjwnre9b+kUm7rTUJBXlAocP+k12EFAIw\r\nbgxlfz+e3n8slLNjK2g25e8R/9KdGkvcPKE=\r\n=2NrT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4-esm.3_1680620184300_0.20030314708721875", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.4-esm.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "aa47e5ebbf9eaec7c5e12bfd26c25a53b8bc0678", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-M9isMsqfkvgA7Ll5B68HcNSVVY6GUKaBmwH8Y+MuwPnxuF3Scbbprg8wvRGTeQdks4OtmnN9RLUlDA746OHjJw==", "signatures": [{"sig": "MEQCIGyTHA6IRmX/AmDOhnnREPDmU6NIuQcTQLzrMmEFnz+DAiBkoP1IWAx9nl7hSfuTuv3jtW5PwFlrFXkZcc/D0un1AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIExAAjCPcCre2kXqkBHWEtdIcNWevjQSnn7gl8UQcwMhQg5cYwVY0\r\n7LfZTXogzKh6B90rC3jXN66UfOpFgO8qBhihr+Rlziubp7KV6YHdDvyQBicg\r\nm+Of8/ND80ZIjUboDxrVidU5ZdehSKQFZbGTpRJPaduXgrF1q6Zxk0v/8K27\r\nAJV72ERjcymaod+YG20qy25igHNUo/JpofHWcySPFqFaW08nZNWvTdM06PDO\r\neKBCIQaOu7Kywuidbu+z269+kH+lxG4I4/hyiU9kxZSnW7BO8NCIxslIF80S\r\n4ooaG5lhnkPpvsWuAkgdzmPPhlcuD5qqQM1X1/a4Quxg7foWkYbAKqYcs9Ew\r\nCO2nWi+v54+DoncycCquTskgo6IvCfa6Lp3x43fCDwmRTkDzejJ8gvRhqzwV\r\n7q2xyNuTNqb5EWB9Pg8VdKNYO5xeafqAYG4ZyuBT8e42Ana8ckMft2qRJB61\r\nA0NaYS2VVb0U16ajPvdBZI58dW+ej6gHf4/wyEZwA7fE/xt2fzQASovbIZ3t\r\ncZedF9LqbnP3Gfx4ey/aPpx6X7h9IH4KHxWQ6aSGvHl+OdM/ByQV3o7KQqmx\r\nRXlm6HpcfcqXzGL8sAL8vp3+GdpbuCUu1cS8Jj5x0uhFJHggWcy7htVV6zD2\r\nCvbd4rHCkq6Je6VKYbbFOD9dEHvPNzYByyE=\r\n=wwFx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.4-esm.4_1680621214904_0.940066426391138", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4ce6ffaf497a241aa6c62192416b273987a8daa3", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.5.tgz", "fileCount": 9, "integrity": "sha512-1+DPMcln46eNAta/rPIqQYXYRGvQ/LRy6bRKnSt9Dzt/yLjNUbbsh+6yzD6fUHmtzc9kWvVnAhtcMSMyziHmUA==", "signatures": [{"sig": "MEUCIG7e3Q4+MzxkkvXwnjbLngsezAOSDemRY5e5aGWjJbDkAiEA0Q+Ijkv0xvpCU0EdFm6Q4K5sTlcprUBCqG/FtGAuMJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6ew/8CNTfrWGKkwATE4YI0PjcaoDG7StZiu43qMee45NkILTSdb6J\r\nXLsCJ/B5uUIgFsEwycBYanGyH8ns06tQky+2m3BgPjKFVV78WA+c63zRCYyi\r\nTO3fYMphQrSpSeNd/6K1AEpcZX2lb7Sga7RxorC5dcUN/a6XK0SEMkm5FIgp\r\nQLgFsoxxkqjO6tVmofN1efOT3aKA1OLvhY7JEgQcCiivu/7sG6UVb/1nBuGd\r\n9xByfG8q3Wx1JmMtKr9eu2BfpyKNY/reuzQT+eDuEhWhIG7K6RflILfhe0Cb\r\nFFbN3r6OVgJYuhbM8ps54sub8R9z+dtUNV9N6jHHRZ3EMPiSLgF1IHR+2GGM\r\nBuIfy4vFb+X7gxNEOQUwrsXIe3WFdEN+oLk/Piy2OwPA5EkHRZ0HAdQcKLoI\r\nmh+5zafyUnZ1Rs271VkyQ3LYoPBtQnw67j7RzTALaeJEgehXjD4xraJymrtR\r\nkObvzLn5dC71fgWrxpBLd5Fxbn/2u1jSAzVUGRP3MeC0OahEKBOyNZrVXP2c\r\nHiq9ih4eX52c7Mv8UdLqOQIeo2Mj/wqDbCgunexiT2TccH+aOD/hU+4GSryc\r\ntADrB7hofjpFmSVAjiBbERT540a4L+M3KgjwQVur8Tv+aQOEhGHM/v2FhHrB\r\nuNKtp3d3joXvPByA/2QioGoD+Nito43dXag=\r\n=R4c0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.0", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.5_1682711414267_0.09224017974382903", "host": "s3://npm-registry-packages"}}, "7.21.8": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.21.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.21.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a7886f61c2e29e21fd4aaeaf1e473deba6b571dc", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.8.tgz", "fileCount": 9, "integrity": "sha512-zGuSdedkFtsFHGbexAvNuipg1hbtitDLo2XE8/uf6Y9sOQV1xsYX/2pNbtedp/X0eU1pIt+kGvaqHCowkRbS5g==", "signatures": [{"sig": "MEUCICXIr0TCX+vrXdIR0SfmDIng1raElHNUgRIlFo3z/W4/AiEAoWEejCh/zIYpY2jmDP4NdbK1lT7Qd2U3B6PDmSanzgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27068}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.0", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.8", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.21.8_1683040509579_0.8042832200726411", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5a73d782c26dacd3f53663073d5271aa3a0c967f", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.0.tgz", "fileCount": 10, "integrity": "sha512-zeXvgF5qWtHDCwJcUD94ujCvK1y8BgxH1lNLpAxnVgjeXrtVGnEqRTFpSkpiMaAQPLh1nMdw8z16Vd7G9KBeVQ==", "signatures": [{"sig": "MEUCIQD21JoweLLFaxy4gD9zgFkBPScexjVSc6KbNWEJGgYBegIgKfj7++Rrzhbi3D5Sg7CEa9kyeFZJ5+PYPPnI9f3fOAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27040}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.0", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.0_1685108711543_0.06919614829026832", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a7ed9a8488b45b467fca353cd1a44dc5f0cf5c70", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.1.tgz", "fileCount": 9, "integrity": "sha512-WWjdnfR3LPIe+0EY8td7WmjhytxXtjKAEpnAxun/hkNiyOaPlvGK+NZaBFIdi9ndYV3Gav7BpFvtUwnaJlwi1w==", "signatures": [{"sig": "MEQCICS8aXf3NoSvZb/QrR1E7ZMIOKylnYkm8qN2uZACbHxmAiB0kp94ngslRwkIpmcAjldDUsPHljSIm3v+5cyP5TeOxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26979}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.0", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.1_1685118889746_0.8680055653428786", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bb2bf0debfe39b831986a4efbf4066586819c6e4", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-1VpEFOIbMRaXyDeUwUfmTIxExLwQ+zkW+Bh5zXpApA3oQedBx9v/updixWxnx/bZpKw7u8VxWjb/qWpIcmPq8A==", "signatures": [{"sig": "MEQCICLfgA7AJfDYOH8+JBdteU7Fpk0cHSvoYJovMJnIdA7fAiBNrkRM7rscWdv5nj3BdE0VVQ/63Elj72mIEthNGaY4tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26979}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.0", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.5_1686248491815_0.7947896633111542", "host": "s3://npm-registry-packages"}}, "7.22.6": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "87afd63012688ad792de430ceb3b6dc28e4e7a40", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.6.tgz", "fileCount": 9, "integrity": "sha512-nBookhLKxAWo/TUCmhnaEJyLz2dekjQvv5SRpE9epWQBcpedWLKt8aZdsuT9XV5ovzR3fENLjRXVT0GsSlGGhA==", "signatures": [{"sig": "MEUCIDWQcfKiUm81i2x5BGiP+bhkOPkO9ArUGNnwoKZSSo2JAiEA8TSgYih36a64xWU/SOtTbTqZKX/wFVwEHN/bePF0D7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27134}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"regexpu-core": "^5.3.1", "@nicolo-ribaudo/semver-v6": "^6.3.3", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.6_1688456927970_0.5038454059403594", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9d8e61a8d9366fe66198f57c40565663de0825f6", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.9.tgz", "fileCount": 9, "integrity": "sha512-+svjVa/tFwsNSG4NEy1h85+HQ5imbT92Q5/bgtS7P0GTQlP8WuFdqsiABmQouhiFGyV66oGxZFpeYHza1rNsKw==", "signatures": [{"sig": "MEQCIHQy5NHSKzJmVwrq7ecrsD92Ct+KLsrVVmaAk3/6E/RSAiBpDQ9R7tdDAgSNE56UdbR1VhbWi2APbZgplXwQuEywyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26976}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.9_1689180812191_0.3511494777449844", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bfddfb79cda7ebe6ce4548837062f00f9ccfbdd6", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-W0Gf6hI7rHntRcLluJxU/H4bJYDjOzxz3AZwE73apIVKnztk4LybEEt0KV9ZJDBfcQPiCN+Jq5Hf6byPnIsn4g==", "signatures": [{"sig": "MEYCIQCqil/qaB0MK7gsi6LLKeMhiHCTU/Wk1PkwI4z5fZdkBgIhALba3AYKbqxeuaEtnFpLGqba3Ay0UkfoWDwHWcgzEa1L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34827}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.0_1689861612092_0.19942464390377834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "71773bab294b684d34d6e0233725aa55a09fd023", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-B/TDvrwxsWs/A+ftLKaN8QVzgoDwFFndJ4ybt7EPCCQh8aO1U0uQjQ4qZhcEnPCoOfeKa3Abr0McVlJhhi2PMA==", "signatures": [{"sig": "MEUCIQDY9xRx19Y1MLysVkJ8Qp9FKlWejgAlQmlWuxIioqfWpAIgQLFmWzI9BwKnF0x/lLNExUhXf5FDeb6daPyviq/BCWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34827}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.1_1690221149390_0.10977033753140408", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a35b727c32b8a68f406b8af6464ba87c3db420e7", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-z/Wc4fPUBqAhocMlrGVDqCUJTtqxtiqMq5tIk9wFrUsBy/uVQ5EooadWpxL9LLxc1g/y5Jf3v5tiXYg4MdhzDw==", "signatures": [{"sig": "MEQCIAPGxi3hGX30hOfn9WlFU5zCpt0x7Pj5/eqSVvV/CqkbAiAESW1KtpvEJKuFk9kSj4qjQAiJSOKLa8MZbXeQdWn75A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34827}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.2_1691594110223_0.4794793695623307", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.22.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5ee90093914ea09639b01c711db0d6775e558be1", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==", "signatures": [{"sig": "MEQCIC7/Gh0gzm6dDm+hVstGWbiWf5qhKtvmGAyp4QsyQqRcAiAujypgZAKyoWSREHooSbc8Ne88XS8GEuCFEaYSfVHxow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27010}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.22.15_1693830301841_0.1221676048451692", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "841ad5ff16696d687d484bbc4e30f0b0c9826875", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-vIutC+2vlUh2evO4rNUGKLkccMCCOj5Pp3sRSCSiaSimwrZ9WvDkNa6tqw5f6mYCh5geyhRJgme9neVefYhxig==", "signatures": [{"sig": "MEUCIQCNmO71Fg62FtpZCU2JjqWc9XCsmaS2qAsg9Aks+tXLfgIgBTYnGVIuSFf7fzA+VX/vliSOz3bhTtQslVu+dh4SppY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.3_1695740237615_0.015613827374711287", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.4", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9941bb9d8901a8839556282761ca7b228f99cf25", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-k0GYNy1o5Pf1yJorOcGSoJlr+ccjBktRzi3aB06gjo22AGczeuTfMxpiQqb/cmia617BTqCbR+9nYMAFEWoxFw==", "signatures": [{"sig": "MEUCIAxK4K5J2mbgXTOFtHSySfXAHpYbDB06aSS+4FuW7LdSAiEAkIUqbvG3V3zj3h/GflIdy99RIngF4quGvQgVOcSIEwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.4_1697076394153_0.8002487323695935", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.5", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2e73fa874abfbfb21ee157fe7193858abf277278", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-FhNB9U1//jAJLYLQrvKPP2W0+N1QEa8ZWfhVFmoc3BUcyejFPAp5F0zeuwJU1SIfgejQlM/zmyOp1D7yPyfcsQ==", "signatures": [{"sig": "MEQCICHkosRskXAoAwAC6IMhzikac0+z7PFRFopGr70txm12AiBnmF0OE4iAhhmYeXnHuWglj4AYHVKnZhi38+SmjJN5xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.5_1702307962634_0.764747413059218", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "07ca4b2e9bc2222c698b961a3ba7be8041ae2134", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-n4Y38F7NapXK7cpU1UPHcDimZ6WmGttbmFt6J3MaqdipmKUTlecKPQMg32sLPU8fn3ou5tevfRGxWMBNKRX9Jw==", "signatures": [{"sig": "MEUCIQCqt14YLvjcZYyd3CVXZOc4ekMkB5llp9XEVnMS9oQ/dwIgGLLg0LoOOTj8UM7K44yr2ZYPtrQ35AnNK6qf/293afU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.6_1706285661981_0.07134257077545358", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "22eb29611bc53fccc1506f9e810d1d212affb760", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-59Nv5F9ZCuhjLUtVOqS/bYaGtu/SncEZWrTf6nzES/LJTYe2jUYduOZRTXcmbRkiLvzfoFUY2TpxcXYmWGLZlQ==", "signatures": [{"sig": "MEYCIQCJqkb9Pz6+KHSvk/su0jVew+GWh9rsN3ps0GeSSJHlowIhANYn61H2gdo2mJVIatAfp0In5mLNFm9zsjn0jOGJ/j6I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.7_1709129117889_0.32150598854112733", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.8", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9aa87cd27ab895dd39aa172debff0e43af33e57e", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-mzeAbmN0T/2zpkqUy4K1QPJDC2nont9rbZJmIw67ytbEaQERu98q5SUz6+v9z89bNj9xA6ddF+7ok8GoMUyBjQ==", "signatures": [{"sig": "MEUCIHiCrMAHAcEAsxTE8BtKba46fclo+fW0RzN/6868HgFpAiEAgx3d5Kd1piYuzMPTo+u8ye7u8Y6PZBiyqFI6WvfFC8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24862}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.8_1712236804410_0.11716783838625622", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.24.6", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "47d382dec0d49e74ca1b6f7f3b81f5968022a3c8", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.24.6.tgz", "fileCount": 11, "integrity": "sha512-C875lFBIWWwyv6MHZUG9HmRrlTDgOsLWZfYR0nW69gaKJNe0/Mpxx5r0EID2ZdHQkdUmQo2t0uNckTL08/1BgA==", "signatures": [{"sig": "MEUCIDvFPZB4Gm8OzLjusOjA55F2v/4wwfEaJfHrWWWBQlMfAiEA7/CVT5v0zgnFFqhx5zusD8B4fLidB8nuLR1Y2/cXfKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93541}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.24.6_1716553492403_0.10296698384630831", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3922dfd4a9c692974aee775dbe64542221dd54e2", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-3oskiP6hDLEJOxXVXloX82QogwJe1EW3SLr7DTiTd+SQyILsVLwPanymksN3BpwA+OJ11/z/xAMomwczkl8cRQ==", "signatures": [{"sig": "MEUCIQDEkgBIqoNihBWHVuiW//ouck3vkzZ9JQbn8jJIfxIi4wIgAXaYHNO5ea6EwwQdM1tJBjAAO+xAyT8YiYJzhKxc/4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.9_1717423483898_0.9294174057341724", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.10", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "820f3775d14f1ddd0cc0276e688928d2b884effe", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-XT1LY8rP7KkcMi6qUaqhYVry0AK0ttQzf9/ZFb+mbrhbmw3BFKeh/1m9FslEkKQaUXjQliNWDoiXZ61rG+l9dA==", "signatures": [{"sig": "MEQCIAUXOJ3qvVPtNJbTIvGek7ZiA39eMc3NZ2FsT7MxtRd9AiAKzb+jDarsrbAccKEmuj1hFwXpLk1szRkbZREt8TyKKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92144}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.10_1717500022135_0.9994575505959877", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.24.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "be4f435a80dc2b053c76eeb4b7d16dd22cfc89da", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.24.7.tgz", "fileCount": 11, "integrity": "sha512-03TCmXy2FtXJEZfbXDTSqq1fRJArk7lX9DOFC/47VthYcxyIOx+eXQmdo6DOQvrbpIix+KfXwvuXdFDZHxt+rA==", "signatures": [{"sig": "MEUCIQDGvsXkUAC8BkTD31+LTkPZ3LmnS51wqbmKPeDcm5tk7AIgaEdgvGQXBeQRKdqhfd/2vvx/85+IvJKww49Dtirk/B0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93482}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.24.7_1717593335511_0.6309650188099314", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.11", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f304da00ba63bc7dc5baacffbe68e601872855c4", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-e0Bvyu4ud/F5f6g+vTNyMJ7ufaasaPq3+/SQzfZ3UpDi0CnqbaggdweMtUWsmhxP+alE5+jGS1H9oxNXcBN/mQ==", "signatures": [{"sig": "MEUCIQD8V7lGfohWh1D9qcjcj5OTYDTHkJ40lESbIHRwwrjK7AIgbg/P+vXd2ukutpTCnWUlCFfLhfu/LCKA1dsWQBvQaOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92035}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.11_1717751746149_0.10973483045633303", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.25.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "17afe5d23b3a833a90f0fab9c2ae69fea192de5c", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.25.0.tgz", "fileCount": 11, "integrity": "sha512-q0T+dknZS+L5LDazIP+02gEZITG5unzvb6yIjcmj5i0eFrs5ToBV2m2JGH4EsE/gtP8ygEGLGApBgRIZkTm7zg==", "signatures": [{"sig": "MEQCIFjcS4TKf/ovFZN86zaGso2z8gsAzC2MvH8KxqmZ1aTVAiA2KocNTAQJuMBwsMFiccoRJpSvDVGBGDt9VHqw//pA8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90122}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.25.0_1722013158166_0.6916504955537279", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.12", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c033e4da62bf613f665c7517a9ece3f34d5c15ee", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-MVz22QBliOBWKjWrZvspcOSmle5oZhOOnHI42z1kQgTf7ZK0M4BYL94Rn0cofjgACMKcdlM5dj6Tgu1uKHE2Tg==", "signatures": [{"sig": "MEQCIEeMM/xb1DVOEJ9hrY7NEjMxapfvwsRYto0JnnhxUizTAiAsVPv63FRkpPuvklW+lG65KjcvamTt27fdRpicQgVMGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88784}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.12_1722015221378_0.1798476119379564", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.25.2", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "24c75974ed74183797ffd5f134169316cd1808d9", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.25.2.tgz", "fileCount": 11, "integrity": "sha512-+wqVGP+DFmqwFD3EH6TMTfUNeqDehV3E/dl+Sd54eaXqm17tEUNbEIn4sVivVowbvUpOtIGxdo3GoXyDH9N/9g==", "signatures": [{"sig": "MEQCIH3fn5+1T05MZxQdcEW0iMUq18dGWWyuklV7CZbd9bVMAiBC3QlT2M5Mt7tAWL0oh5B0DhdM9xiw0qik+Am/Cw7aTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90125}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^5.3.1", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.25.2_1722308087918_0.10329949467729005", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.25.7", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "dcb464f0e2cdfe0c25cc2a0a59c37ab940ce894e", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.25.7.tgz", "fileCount": 11, "integrity": "sha512-byHhumTj/X47wJ6C6eLpK7wW/WBEcnUeb7D0FNc/jFQnQVw7DOso3Zz5u9x/zLrFVkHa89ZGDbkAa1D54NdrCQ==", "signatures": [{"sig": "MEUCIA9RTFnWtoHbbjzI2jcqeDZNHTGi+wJxXyWBf4BMrGsOAiEAhaSrWFf/vXD7aZTSbnCFvr5va6RTdhGPdUXAM27cC9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97675}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^6.1.1", "@babel/helper-annotate-as-pure": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.25.7_1727882106981_0.1881417366338407", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.25.9", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3e8999db94728ad2b2458d7a470e7770b7764e26", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-ORPNZ3h6ZRkOyAa/SaHU+XsLZr0UQzRwuDQ0cczIA17nAzZ+85G5cVkOJIj7QavLZGSe8QXUmNFxSZzjcZF9bw==", "signatures": [{"sig": "MEUCIEiIysMXd6rR6mgMzbWNz7dEW2BHt/K3jOc4qi/Ly7TnAiEA31jIeE1KTGvZ+VkVZEPzDaxYtTpwuJ/iYDJ+Zv6fudA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26712}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^6.1.1", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.25.9_1729610481699_0.262216072176773", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.13", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "31d7e9c199db14bfd8ecdf94f3831d1901ec36e4", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-pyKCeNaNpYkzY0QbxFCz0oZlcZybrVrDG/5/h7joqDh9FBeYL2pQcFNx+ocn/Ep49KXhAAwZmBI8D+19gGZyuA==", "signatures": [{"sig": "MEUCIDimKSQsYLAPUhxg4j5Qqe6WiDiXI5ktzEdH4pEI57hwAiEA5z/JJWtmKSu9FQ57CCKo/Cok2tKZuWcjwfr7Yoz7wD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.1.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.13_1729864463806_0.3855796517339731", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.26.3", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5169756ecbe1d95f7866b90bb555b022595302a0", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.26.3.tgz", "fileCount": 9, "integrity": "sha512-G7ZRb40uUgdKOQqPLjfD12ZmGA54PzqDFUv2BKImnC9QIfGhIHKvVML0oN8IUiDq4iRqpq74ABpvOaerfWdong==", "signatures": [{"sig": "MEYCIQCznpicVnRDLwZflMh9d4i6BpInzQoUPc0IDVXf9+KGxwIhAKHKT6ZtmEKtHng1P5UHPZt+t51d3cJpQ3M3Whf9ZO+S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26712}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.26.3_1733315734108_0.7291659589421431", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.14", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6995e1712309db1c1273b7bc501265ff74f8d7fc", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-rssn/IKNcA7gJ9ouxSbhx0m0+t5nbGKdwvASWkEr86LISGG/BkxqQdElXUVqqPGdN8rFy0+0QDDlcqORioxCbg==", "signatures": [{"sig": "MEYCIQDEsoe0cW/g7tNerneeIU2JstGbJKWNNc8G3xil4UvafQIhALE1UTG0Q51sNwiu7xS+24oBJecmj31Ps84nnP58+PA3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.14_1733504053945_0.06799998645121019", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.15", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "521225ccc2f3d63277fe831ffbdf35e6c80d6c1e", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-A1zd9Zs4gDC3nfjD2oIpwLh8YtGLcee9XwTTufLRqYJDsEowEAH+MhODrSGfra6GaxyHxkWZNYvPvhIgGUukow==", "signatures": [{"sig": "MEQCICksmkAwyQJGk4vAFKm+UGRK1yVkAGIzx2qSSyyhPPQZAiB7rDT8+kivknbmxpKdYahYEioGUxRrw4NpFPDLvhBQCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.15_1736529880490_0.5421287474716765", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.16", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5a1e03779c5b58263b3834ac7a563a6a34cdee35", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-yLKg4m6fF2I2FindteJwNbuIZyrBZrozchM8r+M++DgUm8dUJaM/p+jlb1/Oagb1IgsfmNLLc0RaLcQta3cltQ==", "signatures": [{"sig": "MEUCIHYOv7jRY0xs6kvYj2g7QzhY34BiOcDU9rVjB8F+VsXWAiEAwCOH3TlC4RWHzkldQs/hLijFcRps5aKvSDHN9CqehRE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.16_1739534356746_0.7653654307068158", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-alpha.17", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "26ce46b2f7da0c5a6d1edadf5c4ffbfa4b26136b", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-CFAlm8K/JsLQ/miPtAJoRrXyJ4QJ/mi2B8AlIx6IAudnvNRu74jM15uyH4Dtj+HwiK2bdjP9qbLXAiANnenJpA==", "signatures": [{"sig": "MEUCICv5UFnb0O05HXSVHWnWknMidRvgAft7SEGUv86HYpBbAiEArpFzc5d8Bualzdx4XJfR7liYFkgKCZdZ9Q/r8oFlhQ8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-alpha.17_1741717509459_0.5353199165524507", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.27.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0e41f7d38c2ebe06ebd9cf0e02fb26019c77cd95", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.0.tgz", "fileCount": 9, "integrity": "sha512-fO8l08T76v48BhpNRW/nQ0MxfnSdoSKUJBMjubOAYffsVuGG5qOfMq7N6Es7UJvi7Y8goXXo07EfcHZXDPuELQ==", "signatures": [{"sig": "MEQCIHrT8XJ98WS0XpoTu9EjAPd2yJfhG/zIZFiQaVNBCIZlAiB/7yLaLIvIAHLYmltmELl4zUmE1TNtVkRlHAOsMsRgtQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26825}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.10", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.27.0_1742838101410_0.5324959124701176", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "7.27.1", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "05b0882d97ba1d4d03519e4bce615d70afa18c53", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "fileCount": 9, "integrity": "sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==", "signatures": [{"sig": "MEYCIQDk4T+Q2sr86vHPBAo2oYM+Q2eu17jVY4Yd5uPvMoiyVQIhANdMPx3t6qnYNafWE7BiPXrgTsdsJqlAe+DYU4s0KSEn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26824}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^6.3.1", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_7.27.1_1746025746377_0.9505630348214917", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-beta.0", "keywords": ["babel", "babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d1d2b828aac1f5bafb61ef8a705b35f20cfea634", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-pcnq1/uvrEDmeQeMVDGBRHC3El6lv7oF6grwEA3anDWBk3kisd8Xt7me6+kn+MJBivVv1BBOAjCPb4F4+qsJ8w==", "signatures": [{"sig": "MEQCIHHNPOy2ckECh0i9MRpGbkforY1XbboPB1flA3vObWetAiBree/q4XQIiuxfZ38Vl9XC1qBhrX9x1f7oXDVz2ZCZLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25522}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "directories": {}, "dependencies": {"semver": "^7.3.4", "regexpu-core": "^6.2.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-beta.0_1748620282733_0.6944661217999903", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-create-regexp-features-plugin", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Compile ESNext Regular Expressions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "main": "./lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "regexpu-core": "^6.2.0", "semver": "^7.3.4"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-create-regexp-features-plugin@8.0.0-beta.1", "dist": {"shasum": "b4c14ad72a194bead57ce9d5db74168db6a2e08e", "integrity": "sha512-nkjgC2rZBQksvVTgry6ir7mhenF4zSBT7axJtUdjhD30m4uS+Fzq0uhpRVLZRZreBvtupbSGxBwQOCIkDDvSBA==", "tarball": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 25522, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDyg6V+BWAlGa/peo6z2bhjJDz0XnmvUqlgnAMzi4CoxQIhAP4XKH+O19mJdcvdrsqgkAJDc6hiOXDmfqqdfMHQ7Npz"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-create-regexp-features-plugin_8.0.0-beta.1_1751447067782_0.9881426140365077"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-11-05T10:53:11.116Z", "modified": "2025-07-02T09:04:28.135Z", "7.7.0": "2019-11-05T10:53:11.609Z", "7.7.2": "2019-11-06T23:27:22.022Z", "7.7.4": "2019-11-22T23:31:46.989Z", "7.8.0": "2020-01-12T00:16:09.711Z", "7.8.3": "2020-01-13T21:41:54.406Z", "7.8.6": "2020-02-27T12:21:21.519Z", "7.8.8": "2020-03-12T18:48:58.118Z", "7.10.1": "2020-05-27T22:07:59.415Z", "7.10.4": "2020-06-30T13:12:38.234Z", "7.12.0": "2020-10-14T20:02:34.970Z", "7.12.1": "2020-10-15T22:39:19.433Z", "7.12.7": "2020-11-20T21:05:35.366Z", "7.12.13": "2021-02-03T01:11:17.051Z", "7.12.16": "2021-02-11T22:46:51.093Z", "7.12.17": "2021-02-18T15:11:40.265Z", "7.14.3": "2021-05-17T20:44:17.631Z", "7.14.5": "2021-06-09T23:12:40.520Z", "7.16.0": "2021-10-29T23:47:46.393Z", "7.16.7": "2021-12-31T00:22:33.403Z", "7.17.0": "2022-02-02T23:04:43.881Z", "7.17.12": "2022-05-16T19:32:12.756Z", "7.18.6": "2022-06-27T19:50:17.862Z", "7.19.0": "2022-09-05T19:02:13.060Z", "7.20.5": "2022-11-28T10:12:42.513Z", "7.21.0": "2023-02-20T15:30:52.961Z", "7.21.4": "2023-03-31T09:01:50.977Z", "7.21.4-esm": "2023-04-04T14:09:42.320Z", "7.21.4-esm.1": "2023-04-04T14:21:38.594Z", "7.21.4-esm.2": "2023-04-04T14:39:34.670Z", "7.21.4-esm.3": "2023-04-04T14:56:24.473Z", "7.21.4-esm.4": "2023-04-04T15:13:35.050Z", "7.21.5": "2023-04-28T19:50:14.421Z", "7.21.8": "2023-05-02T15:15:09.746Z", "7.22.0": "2023-05-26T13:45:11.742Z", "7.22.1": "2023-05-26T16:34:49.896Z", "7.22.5": "2023-06-08T18:21:31.972Z", "7.22.6": "2023-07-04T07:48:48.163Z", "7.22.9": "2023-07-12T16:53:32.323Z", "8.0.0-alpha.0": "2023-07-20T14:00:12.280Z", "8.0.0-alpha.1": "2023-07-24T17:52:29.578Z", "8.0.0-alpha.2": "2023-08-09T15:15:10.467Z", "7.22.15": "2023-09-04T12:25:02.191Z", "8.0.0-alpha.3": "2023-09-26T14:57:17.784Z", "8.0.0-alpha.4": "2023-10-12T02:06:34.335Z", "8.0.0-alpha.5": "2023-12-11T15:19:22.765Z", "8.0.0-alpha.6": "2024-01-26T16:14:22.131Z", "8.0.0-alpha.7": "2024-02-28T14:05:18.064Z", "8.0.0-alpha.8": "2024-04-04T13:20:04.664Z", "7.24.6": "2024-05-24T12:24:52.589Z", "8.0.0-alpha.9": "2024-06-03T14:04:44.102Z", "8.0.0-alpha.10": "2024-06-04T11:20:22.279Z", "7.24.7": "2024-06-05T13:15:35.667Z", "8.0.0-alpha.11": "2024-06-07T09:15:46.307Z", "7.25.0": "2024-07-26T16:59:18.519Z", "8.0.0-alpha.12": "2024-07-26T17:33:41.603Z", "7.25.2": "2024-07-30T02:54:48.080Z", "7.25.7": "2024-10-02T15:15:07.197Z", "7.25.9": "2024-10-22T15:21:21.892Z", "8.0.0-alpha.13": "2024-10-25T13:54:24.013Z", "7.26.3": "2024-12-04T12:35:34.316Z", "8.0.0-alpha.14": "2024-12-06T16:54:14.134Z", "8.0.0-alpha.15": "2025-01-10T17:24:40.722Z", "8.0.0-alpha.16": "2025-02-14T11:59:16.950Z", "8.0.0-alpha.17": "2025-03-11T18:25:09.638Z", "7.27.0": "2025-03-24T17:41:41.604Z", "7.27.1": "2025-04-30T15:09:06.530Z", "8.0.0-beta.0": "2025-05-30T15:51:22.966Z", "8.0.0-beta.1": "2025-07-02T09:04:27.948Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel", "babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "description": "Compile ESNext Regular Expressions to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}