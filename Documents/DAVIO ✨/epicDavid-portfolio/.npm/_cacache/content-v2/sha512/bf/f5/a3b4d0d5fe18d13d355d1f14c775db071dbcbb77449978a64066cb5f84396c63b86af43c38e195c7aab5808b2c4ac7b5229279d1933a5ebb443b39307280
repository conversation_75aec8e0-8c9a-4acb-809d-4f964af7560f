{"_id": "unicode-canonical-property-names-ecmascript", "_rev": "10-01d6b83d9494db17f3c0c75cac17faeb", "name": "unicode-canonical-property-names-ecmascript", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "unicode-canonical-property-names-ecmascript", "version": "1.0.0", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@1.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "20068aef4ada99ea2a80bf3ff8299a4db3e5bed0", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.0.tgz", "integrity": "sha512-Qg/BTqr3kD/u1JaM5KW2ccNcRJQUqbgVfXtbGQEMVE28Jt0CBfFGgYxPxM/ctOF4btVI4F9SM8GBbuOxJH1qWg==", "signatures": [{"sig": "MEYCIQD4B2JCRLhuJfoEJJKfZQGz/aZDIxuC0NUTbhC0Y4rOnwIhAIlg8OT786noz0xkxus8WCl+uknmOi6LSSz4VZGB9jGR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "20068aef4ada99ea2a80bf3ff8299a4db3e5bed0", "engines": {"node": ">=4"}, "gitHead": "5102ef7dfe9857859fa20ac65d12861f3a36dc1f", "scripts": {"test": "ava ./tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript-1.0.0.tgz_1492260704824_0.2752008477691561", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.1": {"name": "unicode-canonical-property-names-ecmascript", "version": "1.0.1", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@1.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "6ebdf12ad8bb94ea935658f3397799564174025f", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.1.tgz", "integrity": "sha512-c9juFqQ+es3yrZYrWPgykz4RjhTNv5PEneMmNEXGTAw6RqiCzSl1w4uDB9rxCGasiovwzBeGitMJYO+u9jU7Zg==", "signatures": [{"sig": "MEYCIQCIDswCX9hBnySvPG7rF7273rN+i2LUyQVu9QLR2AFrhQIhAJzvKbetmj1qZ3uFTWd9uJkal9mRcgBRmWNr0ZUzX0WV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js"], "engines": {"node": ">=4"}, "gitHead": "1e80e7b4e6cab38a016f9479c31872565bd06270", "scripts": {"test": "ava ./tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "8.0.0", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript-1.0.1.tgz_1497976631951_0.9614502231124789", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "unicode-canonical-property-names-ecmascript", "version": "1.0.2", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@1.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "08ef9ec454392fc5ed99b08a70524ae3881c0306", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.2.tgz", "integrity": "sha512-tIHxB3DR9aS8uArhzIja8y0LQDvWx7DZIRkHJtLPnM29x/m0sNiMF9FYsxIASpkj85qfBvMWBsFURZoHIX6ceA==", "signatures": [{"sig": "MEUCIHVZlOYxBTg1FqsbFl9p8uNIXjIcp+cYnUDPba9RTmX/AiEAtyiYdaRN1J8w5wHUuD3skqawxlXmbu3XfyLP0sJRHr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js"], "engines": {"node": ">=4"}, "gitHead": "598f494488f218342068f7f2b203b37069939e2f", "scripts": {"test": "ava ./tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "8.1.2", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript-1.0.2.tgz_1502961446156_0.15737194032408297", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "unicode-canonical-property-names-ecmascript", "version": "1.0.3", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@1.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "f6119f417467593c0086357c85546b6ad5abc583", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.3.tgz", "integrity": "sha512-iG/2t0F2LAU8aZYPkX5gi7ebukHnr3sWFESpb+zPQeeaQwOkfoO6ZW17YX7MdRPNG9pCy+tjzGill+Ah0Em0HA==", "signatures": [{"sig": "MEYCIQC6th0XKLqScPxCraTUirh6KfTza1Th1TgUKc5kanCTQgIhANob6Aj0FKNlCfabpWWrSXU2Hjbj3HcSLn+66Jh+xE1R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js"], "engines": {"node": ">=4"}, "gitHead": "4395da9cfe6f108170aa7610643f4c56f1c053ef", "scripts": {"test": "ava ./tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "8.6.0", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript-1.0.3.tgz_1508689810176_0.5450696726329625", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "unicode-canonical-property-names-ecmascript", "version": "1.0.4", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@1.0.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "2619800c4c825800efdd8343af7dd9933cbe2818", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "fileCount": 4, "integrity": "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ==", "signatures": [{"sig": "MEUCIDgrqxCEsDatoFbankQz44ygEDebXHGMpFm+IREdL3MuAiEAzVRYLuIu2Ha1ryMFooFgl+OARVBeDT18DaXyqnZ99Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGSuNCRA9TVsSAnZWagAA+L8P/iT4Ov8JP41hPjQRpC7d\nti6UTwRMCsu3oVayYoI9anM/UxqmnjvELBnOfmNXkmbKQlrDqTGzWfRUdjhr\nAYfA0ySVMeRjfnxIk6tc42MtcX8C2ilTDyOC194Oau2ZyccQVaSHU5a3qx6k\naVShKr8EC/sYg7Pa7H/ZR5yiIjtr/vJsrustcNi+6d7bPvvenFHHg8tQZQ+3\nfkmfCOxs3G6gKSCPRq9cEll+zu0kKg7FRYdFJ+wvOva3j8lAyMuOqAE9nKKq\nojNKyeqGooQcyPgPbTGB27qGl0CQ97B7n2+2N7g2Eonv1DXIch2TyVuMM/CP\nqaBfbf8POL7x7sVrYLB6c3KMwCRzXxCDxDvx0pYp4zLDvvahhvHzDaWjKPr/\n6wipXWjlo/aidm9gHCzKFvMjR2s7GsmUf5wJJDqdp257p4PRztR1pbNw4hM6\nuC5mcrxHwuvWABNRREoSewh/3MdHBDgT26+KS/cSRZ13AqJ6xAjBp7Y0uyMF\nedirlD0ChcIHqkABWATXkk/7H73aVSVLdNYeucBLiwdx+P+WGIzgX+ArGb0z\nxiwSknSgyAQbAmaLwBZPq4G0xEJKNR1tNxIUZnvp9zZgKNfq4lRbYoaXrqjl\nClSM5SHmFNSXUqjh14N4SrT5Con7mJCxo7tQ2rVBAYXOQzSACE9ckF+mzIpa\nUb6j\r\n=jxlK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js"], "engines": {"node": ">=4"}, "gitHead": "99ba468fc734cb16ce06f06535d0b6e045beec2b", "scripts": {"test": "ava ./tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "8.11.1", "_hasShrinkwrap": false, "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript_1.0.4_1528376204643_0.30586353654389176", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "unicode-canonical-property-names-ecmascript", "version": "2.0.0", "keywords": ["unicode", "unicode properties"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "unicode-canonical-property-names-ecmascript@2.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "dist": {"shasum": "301acdc525631670d39f6146e0e77ff6bbdebddc", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==", "signatures": [{"sig": "MEUCIQCLWLMbo8r73emUrqCqsXpx7tSK5X9fDPyI4UzaVrKFMQIgMePgHBY8Zg/eTGyHYkGL5PPhDCjBmLwvtNwmqQKphwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQHmHCRA9TVsSAnZWagAApMQP/3GCSI5ZLOHPADSLYeN3\nwvmArL3WFG9AWvaAnZDD8x1iVIaiYjADnWtfjne7CFida47pn4IaWY50KETh\neoSsuL7ghO81qrJa6qkycR1quQRTPs32WQDxvGy1aew32xpRjjpmH4MhNpsU\nj2Dahqy/ZhZoPAtnzY8n8GznkZlexECKMMRUJ4gEX3xI690Azmukargf0SKY\nV+4IOuWQL3PGWfDo9qiORDCy4idLLb4hWoNfwEP3K8WUODH5UHNP9Kih+K/6\nYQzW+7qL/SojOzfJk5JgDCnY2DYpbloG4aeRZhfzd/mthlF0pcyvQNe+Hu/6\nxl31470zMto2SRXaAn/sLlHlToz7ZZaA/pKsGBseFH9b3273drIlk2NpZaQh\nB0lt+dAJrOtpySeGhSkZT5c55XEbguYkHu00GhUmAfAqlbwPmiHvraln1rjP\ntETaAfnZX5bSaHGFL7W3tv8fxiufTDRkZ8UnOdOGYoQJUMNCyVQSrQItbfCm\ndVugwnmorokGkgTd8rJXY6YpCHj6XJsmeMQibOxmYeWW0E3BWgVJ6fChI6c0\noj7keinQXxc+uIoXfUnz2sPaFpgpcaO3OI3GHEZ7/ag8vbH5EYoH/wtM84WW\nLV0IR6GnmTvEiTX1hinrKhc/yXSGcC/tezD24RW44fd4WfFF5a51anWXLCoA\nLbTh\r\n=Np0a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "f1a9e844df0d9f98adbf1f6fb9226c68cdabbf6a", "scripts": {"test": "ava tests/tests.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "directories": {}, "_nodeVersion": "14.17.6", "_hasShrinkwrap": false, "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/unicode-canonical-property-names-ecmascript_2.0.0_1631615366979_0.6571394131243542", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "unicode-canonical-property-names-ecmascript", "version": "2.0.1", "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode properties"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava tests/tests.js"}, "_id": "unicode-canonical-property-names-ecmascript@2.0.1", "gitHead": "cb0be99b57b853b5af127f159d6edf0b1caf1c65", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==", "shasum": "cb3173fe47ca743e228216e4a3ddc4c84d628cc2", "tarball": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "fileCount": 4, "unpackedSize": 4817, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDc3CH7FyWCd9QXYH1ups6vffw9oTfnrDGq3TNhEsOvKgIgAbHgATNz0xO/xjGR1RjVGCE0nVmo8utEH01GsciB7JI="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-canonical-property-names-ecmascript_2.0.1_1726129249430_0.20067995427740826"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-04-15T12:51:45.495Z", "modified": "2024-09-12T08:20:49.726Z", "1.0.0": "2017-04-15T12:51:45.495Z", "1.0.1": "2017-06-20T16:37:13.185Z", "1.0.2": "2017-08-17T09:17:26.984Z", "1.0.3": "2017-10-22T16:30:10.235Z", "1.0.4": "2018-06-07T12:56:44.698Z", "2.0.0": "2021-09-14T10:29:27.145Z", "2.0.1": "2024-09-12T08:20:49.555Z"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "license": "MIT", "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "keywords": ["unicode", "unicode properties"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git"}, "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "readme": "# unicode-canonical-property-names-ecmascript [![unicode-canonical-property-names-ecmascript on npm](https://img.shields.io/npm/v/unicode-canonical-property-names-ecmascript)](https://www.npmjs.com/package/unicode-canonical-property-names-ecmascript)\n\n_unicode-canonical-property-names-ecmascript_ exports the set of canonical Unicode property names that are supported in [ECMAScript RegExp property escapes](https://github.com/tc39/proposal-regexp-unicode-property-escapes).\n\n## Installation\n\nTo use _unicode-canonical-property-names-ecmascript_, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\n$ npm install unicode-canonical-property-names-ecmascript\n```\n\nThen, `require` it:\n\n```js\nconst properties = require('unicode-canonical-property-names-ecmascript');\n```\n\n## Example\n\n```js\nproperties.has('ID_Start');\n// → true\nproperties.has('IDS');\n// → false\n```\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_unicode-canonical-property-names-ecmascript_ is available under the [MIT](https://mths.be/mit) license.\n", "readmeFilename": "README.md"}