{"_id": "@babel/plugin-transform-react-pure-annotations", "_rev": "47-217c5b317a155a21a35b3d7cab03565c", "name": "@babel/plugin-transform-react-pure-annotations", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.10.0": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.10.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "b3794b3c28e6289f104a6fc49ddf6e71401a84cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.10.0.tgz", "fileCount": 3, "integrity": "sha512-rn8QOUd0wnJYKGROZ9GYIPBbl/c3aC2tuMh1dmkklrL0l3D5MzXmQBlrsWTizbFnJC16TkEHwSs+a0rEO/hvcQ==", "signatures": [{"sig": "MEUCIQCsblLbjFDSn9Umrwi3XPmyaRM84yFaN2zTVo8sRh17QgIgGfuE6o+uSo/TJC9PlvRQoyUmg7cBCXzi2EztqAh6XPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1yCRA9TVsSAnZWagAAGDoP/jI9q1pRXOiZkcDhxyn7\nv4bOuhqbWju4JHkC+03LV7k8rqus32MWxJz813xVuyyf3x97ZqGbyGVA2kON\nclqsTGy5a/iT0oKHVfJGH5eXL+r3hhv+u6P5fi4bTf4fM2p1pX8wQr828uOO\n2n77DmNR66+WqylxJcpESLSyiqPd6ZohEi77NsvRXJubUq6BgVx94oDuX7vL\n8g8k76HHw1XYrNc7KnTWYTzkc9kAqT9hIb7I4bznSAJV2OIbRDDWwodzzLF1\nM5fOG7SPeIh9O1j5qP+/NgP2LG6LnvMyqwxTqWtT4Qgqsxc/7T18LfSo0eP8\nTzXATADlSRje5cNpoLx5+6DGKO4BahLKVd1689tlcgwAyjpO3zT1NHt5vZ4k\nBNtjhrk6WrarPYv8xrwrYqRWj6ItGJkc3o49qdYylkrP1+jAkXyHnate6/2s\n79LX0Frc1YE9/4xmVjNgsqC5wvi2dRo8XH37pxt/j2zbmVEU/aGo2Q5VA3Qp\n+n4Alpp95XV4m+degSJE+kIKBvEbB/V1F14yg/zkR+C8xOwYwnyBdt30plem\nVG4qIadlpsh46EHf9yXZKhZrM3NJGMs3PXHfkzWnj1nflCD7cmkL9CbMuE9i\neTo4d3Yrahm/g8TMdiGE3nUqcqrkSxeKfThh67spapRfPkE/kOlVYMzj2ZHA\n/K+M\r\n=XmCk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-pure", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-annotate-as-pure": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.10.0_1590529393933_0.13449050177328825", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f5e7c755d3e7614d4c926e144f501648a5277b70", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.10.1.tgz", "fileCount": 3, "integrity": "sha512-mfhoiai083AkeewsBHUpaS/FM1dmUENHBMpS/tugSJ7VXqXO5dCN1Gkint2YvM1Cdv1uhmAKt1ZOuAjceKmlLA==", "signatures": [{"sig": "MEYCIQCnMhfIwlWpgZQ8a1JX3ZnyQZQN6U5si5gXnvupwJitagIhAPDgl79c9f/ydE3jRtt0vFhjWCkXMQ6GfktcMK4DvJxC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS7CRA9TVsSAnZWagAA2s8P/1l5mD6NgjDKh/3V/Eki\n2fdGbjMhVah9fnn1ZffHyF+zXla/4ixKDMKCMb+82/oNLfXLBhI/l30D0Uyk\ne7Dlk8uVIvuvCuemAVryBW9JkGFNiC7I49cZGZmtyOm9iCwOOuBw45wqzt+v\nZjTF0l8VTARli3Z+b2FA7Git88zl7lGtqrSn4gmyePmW3o+eLuMugQ19iMWL\nT0LrRkCu5zhRSZFpPKV8ys+swdCRs//dbZyrjKZnqrY4J4qq0IMgWz57E2tx\nmOoaPKdRRyt3aNco9O4J9SgG2IapIhsPUgd4U5U8vL3eMKcPj3iF0Zi2/SCJ\nX7CZW8227VFrdvrqBoPvrl9RjoyPnrPfB++1n6escnQvQ228N6cdk8liGT/8\nqC1i1nc9HHg1aC0aeHxjddxZFCTGdOA6wbhWgV1uA/e9p6+edZmDg2cyEHzE\nyr1MxCdyAcIP+qzfzfj8dv5nyUrXmVFXz/6q/CABBRC3ijMA6lhGf5VmbMGQ\niaxjFeZoWngSOGRjC50PWfi3p37h0N/rBgvS0cNqkg1TJaoLUEpsULbRQ5iZ\nLPrVINGnDN+FhDJZSLTm4MMCeg7mZAgF6FOqbhQJNDOPSiHI9F5mgdRqsP/+\ns5vuyuPmLwpqaX1BC0XYVpKfXeP9X11mrNzqC8ywdDIZOFNXBYhouAbGlOPr\nEEC0\r\n=nFN5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-annotate-as-pure": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.10.1_1590617275451_0.6829839379087297", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.10.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "97840981673fcb0df2cc33fb25b56cc421f7deef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.10.3.tgz", "fileCount": 3, "integrity": "sha512-n/fWYGqvTl7OLZs/QcWaKMFdADPvC3V6jYuEOpPyvz97onsW9TXn196fHnHW1ZgkO20/rxLOgKnEtN1q9jkgqA==", "signatures": [{"sig": "MEUCIQCiGy7Kt5CHclDsZGujRVGvv7/kryvs6jzIhQilhr8fpwIge9lwPwqQB9ZUpKTdMZobHy+8nBLt/Ek9oT1N0Ye3XA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYBCRA9TVsSAnZWagAAcFgP/j3z8XWiT9UN+kCMl/Fh\n881V+oCQVpVyYW7MnNqd/+sBGg39z9HsLS64Xu+jwA7QvUVOKanDpGMcxTjU\n8tMon/fQVHgw8iUKrFLeM+XhdBNF7zl92dPrTvoeq1V8HSnli6zbU2vdgMeP\n999H6/nzxiNUW2Q2VrE7yn0Hjg11gR6Gg5XHHjF3jlSQ4qNPopZrCQ9eDDFj\nL9BrGxvBfZ5yUal44EaOSX096zObgFygFMuD/fSV0KVkNCSWmwD7aqAluJ6I\n5DChzB3CcqKxg8A1RdnrHkmfI5x1678rnoo8Pf2faW1GtMjNmca+cmdmrcBD\nR+87bc/cdMtl9a40HdhR356alRBHaPtCPMuMGzZRfXNyCa/VXdNv0TUqTA43\neboFQEuUJv1Y+4uoCloSAZzBuapD6RtBoIO6QRZouFYL3rhdARU8bG2MZR0Y\n13lf2vvMZK3AlcdB1ix6gGdOQxjonTdafl51CpmcztqIbWzffhfFqoUarECB\ng2HiPauay41iPjSSysUmkaMZ5cW1O53VH/6E6Mmt0ro6A9Q7yfTNp2ZSJNHl\nDG6x325XerNyw8KCu1u2xV0ij1e7SWuQ3FmJPtIaLnnbZSQp+6C0+Di/vXkY\nkNqvt4WYT9uOUmHj3at/j27Cv/31RBhWXReoX8FLA9iDAqC/hLQOdZfRlgax\noFZo\r\n=iJUo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.3", "@babel/helper-annotate-as-pure": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.10.3_1592600064608_0.2920978215817114", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "3eefbb73db94afbc075f097523e445354a1c6501", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.10.4.tgz", "fileCount": 3, "integrity": "sha512-+njZkqcOuS8RaPakrnR9KvxjoG1ASJWpoIv/doyWngId88JoFlPlISenGXjrVacZUIALGUr6eodRs1vmPnF23A==", "signatures": [{"sig": "MEUCIQChOQZYV5yZuf/81R0WWC/R1Z6oCRI5CZ7mwMAlBudRvwIgXMQyRixF/S96DcpCnN9IkdJ2h3Qr8tM1t4QK72hDBsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpHCRA9TVsSAnZWagAAxCYP/1R3pR7/INdEiWOLzium\npaVW1KAUDb6CqhglJNuVqYYR7FWSx/Jr/CCWOw6WlcYYhZ9oqCbnu4eCrppc\njK9VI8h/LwlL4AY8z8pkTomfn5YLtCEVEgK6vpWGiwesvGyLSTCbBo2Q4xh/\nLWqXCn1HOFZGm/Hse0FZKEl9yf6K+FFBC2VANjxMnOWLs9y9tmjN/+40ty1F\na5QvhYPE0iNO8d3rZfl4q4E1UiO9QV06btM37ENewqzqQta/n0MTC/K+6okM\nHbv3xBC5kBaeoCT2H/BEGE8Qp6nLmoetp2gUoIXIZRMFqfk0urYzb6RHYMuH\nVVbW2PPWd56xY5U3a+JX6N3rSrj4UgmeGleinLWMdx/vurRKlAYL9diFpTi7\nVFJJH02vw16MXtxREQbtO4mfNCbDp0PQLlU1RFnmRJpWRfUkBaX8Z3NaC0xN\nabUvhrtVtu3WtbBMF6SipepQI7G11AWPCK4+diyu+qO29HUGThaioha2CZEo\nizAnaTXSM9RHKvUtW00RjQIkkC9cka4wrQi949zDGXrfusTm+p+oy8y5LiYR\nIvoAPv33FJ5XpGJU/ZRqbUfctfgk+udqfN6eDjZ0YM3amixlhG05EPA0EL2x\nmsA8mm1fVMMyffTqVqyipF4GLHkcc5/yfHMhdhlG6SCtTvN7XzcXO5xK26Ww\ngFLT\r\n=+l5G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.10.4_1593522758766_0.31849742504522727", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "05d46f0ab4d1339ac59adf20a1462c91b37a1a42", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.12.1.tgz", "fileCount": 3, "integrity": "sha512-R<PERSON><PERSON>HiwZtphSIUZ5I85PEH19LOSzxfuEazoY7/pWASCAIBuATQzpSVD+eT6MebeeZT2F4eSL0u4vw6n4Nm0Mjg==", "signatures": [{"sig": "MEUCIFDugVnBNInduZkUeVim3sJEhzUy/jBuUY4UKziL4WALAiEAySt9791KJ3JZAD91x5sarcrlcxI4xbAeU17svJKyt7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/cCRA9TVsSAnZWagAAoS4QAJurypaIK43r/w85ptJi\nmWuyY7MSEnO1W5QUKdkUs4zmR4Ygz+ZOYnpcwhx2gTixNebCF9GODU4FMIJt\nMghHg5QZohZiaoMDD87+UFIgI137SZ/E5qBWdGD9CUERjtklF8Ou6X8YUp1K\nPAs6WBzRhRFxI5fBOONP0mzc0yrQB/NEU9ZQ7thMJpivmk+u5J5bNCwq6Cz3\n2Z9zyXlb29/pUmJPrzKEc/hXv631/MthJ+aSzRIOv/ZsI3UL1HAMKDMqV2YU\n6cVO0eugefoupAtj5Q5hSOZtwBh42O414bkfSbk9bxgUn4mM+gXrADuBYlvJ\n5q2ZeZCLwtE5pADiko5KmeKBcBvtFXTAMUtbZ9eJ+5Aa3kCNN9DmdLiIS7jA\nq5u+xC+kEHsyNWSDJnlB+0ZbGNWJWFN2VVprzXV0U7ZUcOUR6e30CuuLKWdX\nb5/pkjnk0zbcMFxLRGuCqDBV7GEDYn/BXWQrH/k8ZS7swKnJsPD5TMWEc3Qd\noqHf8z8V4/C13jylHtKw+JmbxfwNIqv4CvchkTvX4KBtbSJ/rBXywsvIulJd\nkEQSle5nU6/tEwtOCTACHxm7L8CRVGwkchdcPS5QvlJ1jszGaRb7wj3rMBUr\nbcGgcjOUd7Gnjg97meO1/AOpwnQlSIZs4MXBpf1hw/vcaWHPqAGSHNwkF2vu\nBf5m\r\n=vyQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-annotate-as-pure": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.12.1_1602801627546_0.9930165112234992", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "18de612b84021e3a9802cbc212c9d9f46d0d11fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.14.5.tgz", "fileCount": 3, "integrity": "sha512-3X4HpBJimNxW4rhUy/SONPyNQHp5YRr0HhJdT2OH1BRp0of7u3Dkirc7x9FRJMKMqTBI079VZ1hzv7Ouuz///g==", "signatures": [{"sig": "MEUCIQDlRv+G5qHaOzvgr3VWyqw51bfISzBn/Wu+I3NjF8UJtAIgdNhhbOaU5NfU0LUdAKtX9fPaXPN83Jvckpl63xkoWSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrtCRA9TVsSAnZWagAAg2UP/0ltPbqUJxpM+TIZsfP9\nkU/Nq6+Ox7GtPRQJ/D6J/U8sSy4IL2yXvt4AaKKnHLEK05FB0Addhp5HnGsD\npAYvyJqe6fB0SpvM2s0SXR/LiYcxkX0ED2RCrNKmPtQrtD5/MIj2im06+JN6\n2Czv9Q/mvMLpKOSwrxYxOej6hFnFVf045c2a6wvVnOLDUzg8pqezx8QI63XP\ntLlSDtMHgvcCdQ1loYfMzQ0tW8CgN1aRjfkCo0/KUV98FxfIwagxQYm2jbEV\n62BO0e3lfzuK9jYdvJtoO5Q6ENqPmmLdco0stq1o1OO54FzBKvcuUM2shGKq\nHzxAUWM9M9PWrQpjL2mj5W79DKmld60WzsDvVCW38CHvpWU1J/Wq7EDuneuY\nVBHFh3MCPgkhhxtfvq51jV6jTZBO2CzEqoXGpI5nKqSRuWUc3c9JBdYmP0oc\nYER4fXJmpT8Jg2zf+DjpkoyXvcMgnsk+Ej9GF0VXqJWDOVioKsIhEpF9mAUh\nt0IXGsIfCzWThpnwiI1O0ueguDuZVB+Gee5IWg8D1RwYew1UIhn/SRFAAeZt\neDknWW/ZXvB/hf6x4R2VXT1H/gBDqz5nonxPWvu//uaH24tzvwXJ2CJ8j2Ye\nrDFkjNYc7HkUAo9L49dMyCls6gSAU8MpxyAfZqtG0ZuNEo06Ms7H4hWw+/cz\nEqC4\r\n=PyeA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.14.5_1623280365832_0.8803519214465689", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "23db6ddf558d8abde41b8ad9d59f48ad5532ccab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.0.tgz", "fileCount": 3, "integrity": "sha512-NC/Bj2MG+t8Ef5Pdpo34Ay74X4Rt804h5y81PwOpfPtmAK3i6CizmQqwyBQzIepz1Yt8wNr2Z2L7Lu3qBMfZMA==", "signatures": [{"sig": "MEUCIQDXglavDF/cK9UWqbxT5WOz55KCD/kQ0zSoMkDQsmO3fgIgI7C4RlR4OZhpJvH2ny734diF1zEyyQF2xFB65pv9dsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3532}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.16.0_1635551268051_0.005435380497484932", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6535d0fe67c7a3a26c5105f92c8cbcbe844cd94b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-0nYU30hCxnCVCbRjSy9ahlhWZ2Sn6khbY4FqR91W+2RbSqkWEbVu2gXh45EqNy4Bq7sRU+H4i0/6YKwOSzh16A==", "signatures": [{"sig": "MEUCIQCBBN8FNXH8evhA0S75ZOQXtH+HjaMAgvCwG3RUbuSgnAIgKxpSAmVtQuQRXYiwqZcOMgNQhZ3E2JUjx7UX9Jauyj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kMCRA9TVsSAnZWagAA9Q0QAKAlnTpt2BC9V7qntm0G\nOBnheVmw9JNIxzznFmC2+NICsmrtmfr/mqvXPnDGkjJ4u8nhRnXYyYzwNvLA\nXoI1O0H+RSTTLOeXXBX6QGnfrEaPBc0rTJx5nV7YBzx6FvRrM7Q4K5e+M6e3\nZfxsrn4p8TXaYJBx93bERWm3VcvrDgInQlrdWRu63RxMUXk5Ls24F/hbuTxb\nQnxMYl+bSK6bWRV3gV4YhT6R6peTzXLHvSicCGpaiAnwf4Run61p1w/kl69O\nZI9jFbndlERt2N4q1RWOM/QZbkfo55qx9UM2UNLAqO1cbptQ3DvZ8EhWShnQ\nR1iH5VgR82ON3aZtlF538sMo8FPr6trdqB8ESQ01KWWlCI4jtAZ7kSYXsHy/\nAYd5PUVTjYaM/cB91ka4NQ4r39GjB5i/slEAPJIpie3g8r5qT31vPxaHs9rq\nSpqpAC9Bg8iVzoG1LekvQhb6LHD/Naf7YivYqGECigiyi2q/EKs1WmS0f87X\nZcbr4s5kGKCjwG/sUlaCpNaq4rfKVcUirwt1JBJEpc9659z61Nh2jMMXL5Ps\n1v534dLp8Plvc8wNpe1uI+e9uNdR1eZi7efHyOUuXN1JCMLymn1fo6aD08Sy\nVcOn36mIXECU5HOT2nj94P0ajYouw2jwwdLv/PsGHxP+hW94w8z5FtjfZI5D\nJl/n\r\n=9atB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-annotate-as-pure": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.16.5_1639434508000_0.916898527216683", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "232bfd2f12eb551d6d7d01d13fe3f86b45eb9c67", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-hs71ToC97k3QWxswh2ElzMFABXHvGiJ01IB1TbYQDGeWRKWz/MPUTh5jGExdHvosYKpnJW5Pm3S4+TA3FyX+GA==", "signatures": [{"sig": "MEUCIF/VIcVssX92dH2Mmnpmkj+OD1/go9r9/5FVU7N81XrzAiEAiqH89Ctp72h52GrcWL7HpH+tuYACjxPP2Xm2njo0yfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1NCRA9TVsSAnZWagAANlwP/0jaN1jLvb0tppFGvCsY\nCLN0l36n6RTAZ0UZZ+ekZpgvqGjSk0xCUaP8QF3hNbB8eisv2xky4ILyHbot\nIRuXnQN52rlijf3qoNcM2967FzO6AxdAPFLR98dnZ5IUwzPZ/rEDuQpkdTR4\nF/wjz8A9ELReylDBjWXDirKyI0s7SMA0GslP19MXMzzXJTsMV4FUfErA6jFR\n5DMEFvXxRnTRuiGpWj1PtkO0ZyZ6N4jZ4JTa5aW34sU9HGthq/fXWNrhL+ew\nj+IMv4ehKFvjlhTxZiZ9h+2BfZrRbHdpqODE6gMF9fddEFZA+Men7IDrkbTK\nVbInEZtEdm+j+6v3VTkLeNW1dUvYeijkprBhqsB/WUOBdkRMc/KKW74HiTkm\naIDBqfQHm88hn1UcP9Sc0j/yT88PRDX45kj1REPFR/hKSlIVcag722qSQPhY\nXkHqLaI8FQcWiF0E73362+2a21G33w7SJHdLd2jGeSRAKSOr+zNM6Yo8ge8P\n3TnxhhEVg4K90eT04elNHyXf4V+ouAWo0BFlEAP7xRg74TLco315vNcQUJkm\nJ/GWBUGMLOQ7FqPMfFr7wC7HDL1HTtz2mONU7MPtlq2f2pRd0D7xylZxMcgW\nlXW++sKZt3FVXVrxjrsyn58EchZJTA6FnnY3BrvTXeMAI60U48Eogd6mP2cd\nzmRI\r\n=AFSz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.16.7_1640910157531_0.7993576285009918", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.18.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ef82c8e310913f3522462c9ac967d395092f1954", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.18.0.tgz", "fileCount": 4, "integrity": "sha512-6+0IK6ouvqDn9bmEG7mEyF/pwlJXVj5lwydybpyyH3D0A7Hftk+NCTdYjnLNZksn261xaOV5ksmp20pQEmc2RQ==", "signatures": [{"sig": "MEUCIDYRlOy58d8036jdKgeadC0aWMJ4VxbJ62bgBeoclZ0rAiEAnoURV4f6Ul2o72RWTfE4RRqyAqKUBDdsj3uOuQ3Jb+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUJg//SkeoeH1DtPALjQeyF/fXuYU8R+sX4loDU5SnmfxiRla85S47\r\nPH63PDZVMxIy1RwakQzWjIMQMq+BuOXVSwed5dlJSl6owQr6+afem848tGiC\r\nzpTee1EEwDzp/E1mCSv+Ax4CalC3hwu0dxSZt7EX5c7VB1OStD03uc50GGUi\r\n/qVojb/uSq0tpjtXU6Aeres7D5gD4L9rCWxUnKfbe20DYQ8qJVyrqTOgXoF+\r\nnVX3tAU3VP2liGukFXy1pI8fMObhvjdN7LHKelbKnqbZ9x+mVY+2ZBGkbjIW\r\nkgauqjsmPVDDfzBeTZyUdPHdzf9Eq8+akD6XScADV3FFG2dF+TBk7CdbwdsH\r\nEWKUDB9BsAHfAG8W3qZnkcPGLySC2VhvBe5rorr5gzqUrl6MGHVswZshBT8s\r\nVpFDkU6n9iprcLctc061xONpgdFeVixG9ZGDLIVQ6bT64XFTDO4dwC0o4roI\r\nUpffZkkQn8nt+dTiA4I2asMVEFBxzXJtAc2XlJMwa0XPrbPMFPd9Ss2oQkUz\r\ny1zA9WV+AeWn0F38+xjxBegmcV6ldx1qRc2BS7lYBZMP3e81bPub/cT49dAv\r\nXXQTZjeWlY3ne5T5SBcHCwfKZZ20WT8A52HNPgJhRBWu1qCQiWkXocjeRWwd\r\nGsHbR5f4fwGA0NMIkzWdjvBxuJVrKS68mz4=\r\n=RThu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-annotate-as-pure": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.18.0_1652984194185_0.5293326883504297", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "561af267f19f3e5d59291f9950fd7b9663d0d844", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-I8VfEPg9r2TRDdvnHgPepTKvuRomzA8+u+nhY7qSI1fR2hRNebasZEETLyM5mAUr0Ku56OkXJ0I7NHJnO6cJiQ==", "signatures": [{"sig": "MEQCIB8Y4oL7R96a6PWQf7snNgOmljMrHPD6DASaqTZ589RVAiAlk0gc1ZMQSNCcc6tMCJePWEB1Kqor++vJWFRiU/Ts8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugn9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptXA/7Bar+wHMMZY4EzdIfYGB/muAPYdpSsxVJC3XUw9HIVQhTXW2h\r\n4YWhuvGtFyF0h3fGlr/ZqCIBoCk9FmDkljw4VGgehaxnFUdt/fyl/B2RCTDt\r\nc7IWKXHuMlsBskFSmv9d0TcbuTsB93lWmWVJxmrGTP+yeJkq+0EX9wnoqSBm\r\n4vyytw+AoRPuWNCpgxG75ZDg66AYbtjrZND1dtu4Ca2WO/Froy9lEYbU8+Tx\r\npkb2p1mQ7s9T0Nyc+4b4nzzZYPk4CdUJx1qlcKGnzuZAdqf7TDE6ZuZk0iq7\r\n0a2dAiP2TJ9ScDfOeBm3TOnaArjqCG9M3gVqQbzE9wvuP+2+tVieCIz8H/1N\r\nyiP1WDeX/tLBKFD0Ftmmn3LC2689/umSHP2FF0H4oXqnvo9DgpryZ2zBgyk2\r\nIPyCSIsPfQKLnkEOEoEZNWo63p7KoRLOR7WovO/1fxCW+LaXz6ER6T801Q8D\r\nk+XdazaJtqx/BkZmKuA3HsF0H3+64SZ3F2eg0+rRQhizZHqH6Pr4NJwkWA4G\r\nnEI41mO5qRKU0DdRzVsFV+G/oviH0lg7vXHBsCaXdG0Vjoz/YpFjMBhFp0U4\r\n6tJS85w4jYw8Us2Dx5p9dFMHkx6EE/NuiJzVoflOsPrQdeIdM6sSYiwYLENH\r\nBcXRIgtuqbskamAn4vFAtHvLfzt0hfoKn0I=\r\n=tTMR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-annotate-as-pure": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.18.6_1656359421406_0.1288059348591799", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3c5a6b7dd2c76d400f85d2fc0710d3af29d52f8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-9fiuo76+DU9ThUWK2YZvKuZeePStiyDzyKFCnjyeP7v7U9Ju8eOmKuZD5ou6An7aecr0oU69QtKQMoMRj8FprQ==", "signatures": [{"sig": "MEYCIQCE700pI56B+TA9j2jTP80PxwyFfHAB+1K4GY9d4rEbWQIhANG31SDCFDhp14iuhFUVdXBwsUEHZisDc9MXN1kyDy8x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprSQ/+Mw57p4RDlYk905u91j0HUfUN9UaEMJ/jYWknt+QkgIX9Hsy6\r\n1IKD3BDU3Qoim4aV6wDPEVUHynYS/X36UOlIIo8wsNs5ymUC5U68wI/4eqwA\r\n33VBUlBXuCq0pw9wRJxVIQjSmDnV8zp2h4/iTdGUPW6zNp1vUc4f+VPJQHSh\r\nDa9OtnSLiqIFg5S5iZNBxhXneRtFk/cFzHZU0K2e5kS0tDUsvwhGcl/+ni92\r\nQc2/7hvXJqI5Tog92eO+9+FCphwj7gBzoGiRL8VJ5o5fo7r94KLQ0V7aboW9\r\ngfdT7z5e35ziSwv814cd1INa+tG+n7PcZ9n/J+42rBrx/Zd88ScU/aF4WrcW\r\nrk/d+/3217fMm8nXz/HIkjumPXv3NYm4IJiYfRwQy/UdZICpiw3iiBSHNcY+\r\nXyuV/zaCdvVYzn5svHj+kMZKnNCiaa8vpsDwzoiek4fRy8YwG4u+EB5UjZAt\r\nyHIyYBMn9rODY+92LFZqpAbXiLtBie98ui5vkqM0FbFNHSi4wvdfPW1TTvM1\r\nwDsbuCmiGe0c2yDlmiMRSws89hx8jolkxnqq+Ul96e/Glk6o323AqxF+kolb\r\na2YKyVYfn2YsACYvGdasH24aHKzjuctMF9LZ4yLNfq3vg7rmM96JgI0gp9xu\r\njEysf5BWC7UIYvOca6orOxLhpg2hj9+U/HA=\r\n=+sC5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-annotate-as-pure": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.21.4-esm_1680617384461_0.865088895279593", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "45eaa5d1f2030c51f2afa25aad8b2696120eb791", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-qZenPuYTcpTyyi6mD1cFnMnIdRA6dGVC5mHxRER4ky/uEmbkJHTHlOM6sLkraPIQY/r8YyZUvY5GancivVJdXw==", "signatures": [{"sig": "MEUCIQCaEGPdfJqyUoMPke/pF0Vwm3aQhmNHmpMyqwVle13f9gIgG4I7ePETByT0VSIBt99jqccMl9RtonJaylbx4YrDWA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDAQ//VFJN9lDTSNKRQF/aNaYbVEoW3Bz/o9QG+BInBa04lEZHqk19\r\nU9zwUT2mRhe5DykLpm8WBL6FuUN1qfw2Re2rMC5d1iRifBxpBJqGa4tb6Qjp\r\ngTmmsjlRYp5FzN7cEPYs28V8b6JBaTJ/DlIHwTU6xadQVXrmKGMcFRAzanv7\r\nCpYbAF0zFp575Y9EcWhj3OBu2W9VVWa4D73f0ZCq/BfNlvB1PdBfuAL6FXL8\r\nVrq6KjZVB2uS917QR+2DsxwFE0gfS9h4r2ToHs5ia/36u3xZR5vy4ZAKHhI4\r\nYdINKzgwWiICAa9b5nzXm83R9Z+yEYu8ONcEU4Ii4+fEP0EcXdWp0dN9Zl68\r\nEHSJ0GmXKnZqnw3Fg86SAUfV7R8tAmWHZCcPyzTGQs8YpwYP15VasPV5tdX/\r\nRxzbVeQbuTgXTFWRjmQ0+LDEITwWBje4YxONgMAi9goHzd+BYlSQ0OTAPfHN\r\n9YvAm8z0U0jArjYdyET5nGeHZ1v1mUbYobVnCl7fEK96MOCuyLNySu8eABQb\r\n6aYcut0P0q2hhtXZ42aO+/MXb+sQCQ5nLBHCz+kddc0AH8pYGq+n8wMRARVZ\r\nyNBkY1K3bEraaPq5NqeuYxMnylAPmEiQrzpPbMSDotDSCBFLfECK8iFQkAvL\r\nh4RrWLv0QpmRKdck2k8M2PwQAmTTte+iy88=\r\n=/tyB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-annotate-as-pure": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.21.4-esm.1_1680618102193_0.4111300263033182", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ea81f4e32a5d39677d321e4a04ae1efb7a34ccd3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-SztAMmMNNwu5LsWqDCpOHewv+mqRbe5cmPjd4hCs4n28nti82o76CvXw2xbTN7dz2kasjriv6C55XoB/6yirpA==", "signatures": [{"sig": "MEUCIDbrNVFCsCfC76Suna3GoLzdPwjXEPZwMzRGAh6oFhuSAiEAzgu0xEDZjv1ShojeLycqcnezf1u3hZtBGfVcegs1aAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDapACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUlg//f/urEn9l0dfNC7y8/H98nkniKXXHwR7oQwknqrvgZJ5MKoWn\r\nG04oa1HhHz3tNuVHSjCW9J89XYyrqpIPOhQ1oFxT87s3Fi8OLGwxQ5rlHCev\r\n0Rw+Yoflqt0CQbItxbQDRHBICB5lrVrGjo7TJQpV+S1kCXeNXC6g/KOFESHM\r\nBxRJoc3EzDhfamboADBLeL5vSmk55CUnB2yUN9WdNPLZZNd/Uom4O+1MUmXU\r\nCrBMg2cNvrqJff4CsxdFPXNV9kzGeZkcHr+RtmEHByN3gg0+JoA7+KbUY1FZ\r\nHxkEPSVtQQ/O/arkJhgXVmJcJ0E8chKh1WKxZkVdHT5he9zqdUyInzwmg19k\r\nko8tK3LrZ3oqym1CAYDmX4oW1jqIrAx+PjGcw2N3zKNbym6eCTmgx39biVRu\r\nwQDGFrxLnFpiQa0OziaeElUdlFq/0LEyBuyCCcy49ASv959WrPQ1RZ8IyYTe\r\nIvKkwBngA0kiHKsuKdjJMT5SGeWcD91SU8E7XEsUnGEzUeHCGUdLA1xFs508\r\n20wWFN4blPHaaBbr+92kzJ/atAftm1Lu81rsQvwP/m7JQyDNCNJA3k+SRWas\r\nee5QlzI5CNhhpiikre3zsYti9i1Ae2q/e61GVfQ2BkPd9dOH8UZqRsOb2hio\r\nj6jj4OfdxIiw4AFdZgZW2z7GszTrWK4BNDU=\r\n=9dwt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-annotate-as-pure": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.21.4-esm.2_1680619177197_0.6084788484089336", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "54f302d7c3ab4d795cad3794edd8804af0996483", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-ZX86bYI4awIdevtFOMllmrPzUdM2jWR2RiiVjE3TtMnX39FFQSOFzPwOlg/8HlT3YTi7UCLFc9Agtc8HbkM4ug==", "signatures": [{"sig": "MEUCIQD5tedfWqK4k0YNQ22EuNmqP/gztCyDbOJzxEUXvIBwNAIgS9Xat2UUoEDxLvQlMU/Np9p+PPbqFqQfMaIw/KPzrl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1jA/+KW1zWqxDbqpqtYMZSQtSoyk3oEBpsIsgqN+SjcaZkzE8YwOO\r\nm32EjgGVLffLgzbBzn0e2EkQRtMkEzl6uJBegonXvNnGwdDJCI1VDswduk7S\r\nR7UtYC15CdURD92BB146dXi0IJ4H6TIursHjBtaUY+4RTmAbh+5GuftryCtQ\r\nNqL+jsuu7a+FAajzaQ5XKgEd1JqIL+1fW+Akp6vFRu08NhlqNH8aUUA5lE3i\r\nIfuCyVDYq2sRDXCvBXaeRaQyDYdjM+jJUPIggxuEtiRa8D/OpKM39Uu9z0OV\r\n8s5wLxVqRPMVXagss7CPCSKWpsQDIEjMH9fQ67fTt9dVuBK/HpVPY2SK0t9M\r\nyqiAvUUKhTRvxZaNpW6oZCNATZZyxGBIhHb04DroJb4ZH/ky38tRlNdS4jcu\r\nUGxGXra+F4x1Utg2Pd8BF3JhweOSkQQVWf0IHWgOdLNZkAJUYPr7Y8640FZV\r\nHGAUTbeZGpVlfmQThBqujRIRnx2sETLHlMF1q4TrPhgOVqYbLeKAAI5Zu/sI\r\nIQlM2/8Cwc2tH/IlLIMFWwmCYX6SKU+69+pzbw5HeVDHp61ttSI4HJSajH44\r\nRAM+2eSLtfrxwI75TvNMv23I2EQwvDFbPFqr3oS53nnTw/nmfhemeHbr7Y9Q\r\njCYoJfOUlWsok3sovTdIQWcJ3S/t3YAhLfY=\r\n=L+4x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-annotate-as-pure": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.21.4-esm.3_1680620186839_0.6925788348972843", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6278d6a22d97a30cd2939d5a074eecc1aaba60ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-XdVTNfUmGiJE2hed8X6LXPzpnX1iWYSI0t+ujSPjwJxu5bf44MDCiuwgrFUCKLYt1iuSrdvugW2dIuSBYmQLmg==", "signatures": [{"sig": "MEUCIQCJqoabm9/kThM8JTYehwyiQrZBvIyr/QX7O+Y/D332EAIgI97yjXBotl3FI7QowrRanUtxW0hfaT7OCoGpts+5e3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe6Q/+KO3sU+maS8lQ+gpUL5Hd6uTbprAYQimX+V3qBidpfBCp7PkE\r\nYXFOhXihdZ1UIWo3/PGXcSVjLh6BUzUXgCp3PjudLWw/OjIRGnUEsQt4BnfP\r\nknwcaFI3CvXehT04zByfjq7nZiT7KT4ILZnZc3+M92y0u7UP4lPyiWE9RNhc\r\nAx7jkEoJI/o6AG4GNOmEg2Ua/b9WDuXvz4Z7MXLfGm1eX923RW9R/gczzdnQ\r\nb3z7LwFkTIlDmhX1s5QVo/Gr3FiEehYQ5aIBPMjiSdas/k6T4wAUWoQX6GU7\r\nFq7i60MjVwiaV8go3ipkP4Ui5Gf3t6krLp9pDB5P4O7XJU+Kox23AXhbho5d\r\nAJAt3D5gkj+6QchLrYCYsJ8rcE/KlWQ8OVbUzSMGiLJ5vyX+7wuzQ/uzD2ru\r\nuXIE5jUXG5qy9CQmVW9AgGCq+F4AP4zRC/D80twvwMU1UFYz8QNjJWmR9gBI\r\nOA/TOCcoxUo142ZUJCzoRE+SDjdD24G5MJnWUeReTCtMZqbiQaR52XcSXmh/\r\nxngKPOvGvQmcL88TSNFV8Qb+NxUveMrKna1c9pJ1BWDvPDKH+Y4bGetVMvxA\r\nq/ts0vw8BNSDE9aZMZTEmmWhWK/s07OIscMMYVZ/MeD/PnsfxhrP9H6nPrdl\r\n/xd90ggiDmdb6fyIOdHdoh7zs2RO6VQAVX4=\r\n=eoSG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-annotate-as-pure": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.21.4-esm.4_1680621217404_0.262086625002087", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1f58363eef6626d6fa517b95ac66fe94685e32c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-gP4k85wx09q+brArVinTXhWiyzLl9UpmGva0+mWyKxk6JZequ05x3eUcIUE+FyttPKJFRRVtAvQaJ6YF9h1ZpA==", "signatures": [{"sig": "MEQCIGAsVdVmE5ffhHKt3/f3Z//gTh0OYe2zlegaXdScatIHAiBG4/Fw1G5+jQGrhBbxzBZuoPIJn+gPUn6LW21/m55QWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8330}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.22.5_1686248494234_0.7405690817450024", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "dff82e34c44c0b0e545de23c6f8a7c5602714fde", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-qfOWd3M38E0AuG84KNkQR0qRNi/R8tgjRcHoOjnh5GLpX1JBIQA++sBL1tA2VM4qSsVUQdkxdTjf3lHCzIUAvQ==", "signatures": [{"sig": "MEUCIQClXvsYHluMFWmxW3yoZ5CriQCAevP8GCVxLLbBiPo0agIgRJ9FL4vL9JXx40wV5f8PMmSC9cry+pM7hO72fHycMr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8274}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.0_1689861616299_0.7114307194646368", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "330dbc088eef1f4b374d5a2fe0b43a1e9602da67", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-qUKqqJ9ltvzCnnxku7t3ErK70BFReDeJ0GRa/M1FtcuopA/qAmvGutBcM9+SO5XLiQqytPPJCRude1U5hNqcbQ==", "signatures": [{"sig": "MEQCIFkHrlVgkMviEf4x0a0E4N91ReniLboB/k1wARMXl9rMAiAHBAkUjvRXirZsXYVvzdsbh+eYr7N0i7ezwxuJ6Qa67A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8274}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.1_1690221169217_0.39341661439521225", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e4243800cbe31550c22aedac6da4a8fc6a21d890", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-5e8xMxQKV51Ss9/A9caCnJIfgjNyRuC9EH55HFPXOIrSSIg+VZh0sMT3cvQq+x/slXe/92suBemf2nEehbuteg==", "signatures": [{"sig": "MEUCIHA6ucAXQIS/q2lJ/1hrFw+MVmHmIW66wm2BZbeeHC2FAiEApe0nU1ETeihb/RKxrqrhyAhBdF+GJFZgNulcSxHQaFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8274}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.2_1691594113140_0.008022308041202963", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3b34720b8925d23a7a395d0322f34ca7826bdcf0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-7iX2TBsu+IUR8pDYljiFiKw5PFzSzoT2XMhxDwE0IFL/wZHU8jzHJQN+s6rioCFVhODKE00HhwivY5BOsSfeiQ==", "signatures": [{"sig": "MEQCIDFSGwwrfTsIw2wiNFPux+Mo2l9ivtX1RAGRmvpnsFJ+AiAuIPxidhNJtNyl7naVy6uN441M8KtwH8UKHikdKn7qzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8274}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.3_1695740243954_0.26217519717177007", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4a3c7e1ec213a1e977b41f55287b5d2cf11cb32c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-T1Ao3/qD1p8bAUZHHbY3wA5465PkASGsTiDmTQKdA1ihXeeaOCa8i3apOst0W2HBke1FxwzigGOoVcPPts9lpQ==", "signatures": [{"sig": "MEYCIQC6/pEN6SEFCsM+w8kNHQNNBsI/5PyyHbd+tlcRxQBjcgIhAOKbl2MaatjwrSACLjqy41UZB+hmGaHKYtYf6Q/k2JLH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8274}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.4_1697076398369_0.23942970731317348", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fabedbdb8ee40edf5da96f3ecfc6958e3783b93c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-qMFdSS+TUhB7Q/3HVPnEdYJDQIk57jkntAwSuz9xfSE4n+3I+vHYCli3HoHawN1Z3RfCz/y1zXA/JXjG6cVImQ==", "signatures": [{"sig": "MEUCIQD2wrzdJm/I/rufCJaOIN2441ZwcwBFNFv/Vh77isuEMgIgQDboFrTO8BEE+28r07knpDNdh0qKR88KA79blCjlg4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8410}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.23.3_1699513439616_0.5714627061960817", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a94ba3e316baac59563869ebba3613990b5965a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-kKejo15JMnCF8Ccfn7kHc6ldTeMd2tek4Ucq7gLsuZiUz8LaYStXF219NB/rwuDLgHBgFC66meagi+N9/DHtzg==", "signatures": [{"sig": "MEUCICdZr/SG6kQwNpRyqhn3jgKgxIzEr/9bxtYNxJqfiRD3AiEA1CFCy6S0deRA0FjqoaK+6LCHbKdy3NIfjvZEeXj7kXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8387}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.5_1702307967860_0.764638426923713", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e208c7cba216572dee91e0e9bdc2c4706099661e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-KeiDnkHQWgVcvH6iHtTkGGuh7yj8S7n54PJAO2oQSHolSCziMyxnQPZrnfBjRmQo2Yh/SlXWgPBe40Lc6AF+og==", "signatures": [{"sig": "MEUCIQD6fwDRt99W1dzsa3FZ7t5g/yaoJ/Lphq7BMVlFmsxFIwIgYOs5sSKfogJpY6cz9oVuzRGk0IPbLRwxMg1QkSNCv2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8387}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.6_1706285666809_0.23746388759564807", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "72c6fa6af825a382304b32768bf1173a65449733", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-WhC7h2N7/a9G0uw1lkNw5FkHjKZeWe43ujhqjtTve/hG3+9+8GqyExJNZpOwqTUwVPZGbjDtlUJI5IsQEoC4uA==", "signatures": [{"sig": "MEUCIQDxrscJeTOfY75rLHkN11r7K3pv0BLsrj5QjOEuwdmNvgIgR0tUEUulyzuYYRaaJbGjpG6OGEeaaVIEs6onCkW1Q5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8387}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.7_1709129126591_0.411744065157285", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c86bce22a53956331210d268e49a0ff06e392470", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-+pWEAaDJvSm9aFvJNpLiM2+ktl2Sn2U5DdyiWdZBxmLc6+xGt88dvFqsHiAiDS+8WqUwbDfkKz9jRxK3M0k+kA==", "signatures": [{"sig": "MEYCIQCdtu6DY8/u3Wm78JL/dNIIjwKSEw1vD2ry1LPcIXdmFQIhAI11xHzlDZF37xE9W9/+GvuBb8eeusdUC1oQKIoJ8PGU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8341}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-annotate-as-pure": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.24.1_1710841740737_0.998186138880963", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c5f0dbc3914492e2987c3f053e8ebc91c64ab5bb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-PLj+WNYQnSvzFf6CZbM+40F/kgYA2OKXZ6StIXhQCLxWIFQCcErF39JeRWbwSF3M2GEPBBJVYbOClqaTqQusAQ==", "signatures": [{"sig": "MEUCIHLhm1570Z1IRjqDPGJlMgLQLnFugmDvLSOlYMGoQHUZAiEArCt5omvrCTEqzBByAONuCJg2CnE6AwbAOvZ8TDuieFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8301}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.8_1712236808270_0.7183665058675166", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d2bad8d70c3635cb63a69ee66c9c891f9392435c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-0HoDQlFJJkXRyV2N+xOpUETbKHcouSwijRQbKWVtxsPoq5bbB30qZag9/pSc5xcWVYjTHlLsBsY+hZDnzQTPNw==", "signatures": [{"sig": "MEUCIQC9ETRQlqD9tffXzHI5n5c0oSn67RXRcF0CGL4/TAzTQQIgIED1hu/rj4veWP+th9B/TGHexd6MRkFN+9jti7sLZl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74531}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.24.6_1716553498621_0.9827402214922756", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1f5ed7b3e7ddda3eb6ca681e06507f8c972d054e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-c4CU6nVS13TpSyizmaKYHW7qmfoOrg2UMIzGEn5MjotEulsW4drfFv2cvlaSpTdeMu7xk3ivIrmZ59ddxclIPg==", "signatures": [{"sig": "MEUCID2Niu7V5VPmM/fVyaVHSo135WiMthcqN7jNb4/XXjyaAiEA4ULsXjR/QDB9oedk91V+4rXOtktmWGqw6bHQrXCjpUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74748}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.9_1717423485233_0.34065269795313813", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a21def121b0067bd59498072ffbf29c918199d7d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-2OFGEG17gk/9QCT1B+Z1pdRYVLy8yEvfv3Se1eUs2oVFeJt8srAdFZVFGXuMWcNEj9IrUNoWoshA7O2I55DmOg==", "signatures": [{"sig": "MEQCID2vxnAZErUWZlC9aISWpdF3JvClWTa/GfCQ6W0DMHkeAiADRdfVuB06sF4REjapxJl2W3u5jo8zpHB9DiyiJyKmZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74756}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.10_1717500023207_0.6956254597574847", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bdd9d140d1c318b4f28b29a00fb94f97ecab1595", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-PLgBVk3fzbmEjBJ/u8kFzOqS9tUeDjiaWud/rRym/yjCo/M9cASPlnrd2ZmmZpQT40fOOrvR8jh+n8jikrOhNA==", "signatures": [{"sig": "MEQCIFtST9g1au6T+62ravKvVh41FYZ4ftVuP9tBybZmNCDMAiBlMF1FSVF3EYWSTkyB1LpqVLrPKd7w5hi5dOhTRsHXcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74474}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.24.7_1717593337068_0.5947877673603512", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f78e9a95a9677d5de15a132ed7e9679bf2258a24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-gW8pHZ5tt84TYiGw5hKEI5Db9wJNTI4hHKbVFmQhz+XRRVUUduBIUXnu5GcJVamvg+3jld6bNrIjI0OiMQCyiw==", "signatures": [{"sig": "MEQCIQC5DUukXXlgjpxM8/A7WhhiRizPWP1c5saJQyM4rLAkEAIfKeOSeyLN8cT03UvCqrKOYdu+CrA6hGMiUDcNPJXoYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74645}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.11_1717751747526_0.8006390644025456", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0d83d3fbc14a487df93c1293c8f31852b092ea49", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-WTQfEl6zrgMu2I2rocL1GC5SX0wadyzOfhXzqtZG54zU7fR6m6upSbNSTj62flRJn5Oc5lVpAyPQoWqNrzQLMw==", "signatures": [{"sig": "MEUCIQC0r2ZC4dp5ZKiECL+SR4Nx6RY+5Tn5UmWXG55/mlszVgIgWqixVIcYa6ePSU88yVHxNsrRwHC5jMuyRCLu48R+O5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71428}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.12_1722015221342_0.06729737706041661", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6d0b8dadb2d3c5cbb8ade68c5efd49470b0d65f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-6YTHJ7yjjgYqGc8S+CbEXhLICODk0Tn92j+vNJo07HFk9t3bjFgAKxPLFhHwF2NjmQVSI1zBRfBWUeVBa2osfA==", "signatures": [{"sig": "MEUCIQC4ai1M/Aj+GrtAk2MqNbQ8ffMNiauJbPPQGCofEWhbNgIgdhILbERrQ+DlnSZ6xhEbqAYh/DslQqvxCn89qajmoA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78999}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.25.7_1727882107006_0.4779856137025098", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ea1c11b2f9dbb8e2d97025f43a3b5bc47e18ae62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-KQ/Takk3T8Qzj5TppkS1be588lkbTp5uj7w6a0LeQaTMSckU/wK0oJ/pih+T690tkgI5jfmg2TqDJvd41Sj1Cg==", "signatures": [{"sig": "MEYCIQDd+iOYH1KnqYvgBowlsbkQHGv4/PMpFImpXYT+/Nv/EgIhAPqx3oVgjCBg1Gr8m3Rq8RtGsa03idkL3Tg+OF+4gw5m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8304}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.25.9_1729610482016_0.620472943010109", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "38855ec6bfaa222e8e9fc9fe16642617ee8ecb3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-gLEoeqbiP0/tEaoGeFEwJUKPXIqS7PR1+0MHibb/tR0IKJ82GizoZ4DijYS9p4a1VdTHcf+Yp+LKQ1ol6piaxQ==", "signatures": [{"sig": "MEUCIEDx43bMN8bJYTytnvb+sY/hJuLFTt5ddru1SOKXMkHDAiEAtOBkS61yeqfuP95/HtFjhum14QlBAMTdpDwqKFpt10k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8603}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.13_1729864463572_0.7759605582629052", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "98f0490e4a8e41d969f84b32048edc33b7e18e82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-dZ+Y0oNKc3R8LvmdzGnz8LK253OTLY9nVOGOhh0UAKlzdhVz/VxrVYi2z4D+6K9wQfTP7ofofc1uObmgUSespw==", "signatures": [{"sig": "MEQCID904Uh7qn2YIAanmTLGnl6tG75eEuJ/a4/ADIWRWBu7AiBq0K8e8OJ13haxfHlmPpOfBXRDrsNcKXUtBTe6rPcs5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8603}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.14_1733504053649_0.11234104747824869", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "29e0bb00ae803974e2985b74df6a8acb1c34aaa1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-ILOQpxTsXQko7zHfsDQozMF4C1TexCdA808lzymZEy0QfnqaOHEGtsCOsunmwTifectqW4I5/r2EiPVUCfuP6Q==", "signatures": [{"sig": "MEQCIHbEsf3o0mO5ORX26U1Ak2dFiZ2CdLtTPEmL9CyfTF/UAiBocYT948sdJH3x23NvYvJv2u3YBDjnw5pUQOwfzSyRDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8603}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.15_1736529880065_0.8760930236090521", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "04acbc25366954f72b43d63e11cf97a22a376ed4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-BdKzIuABGyXEBppzeR5WeFlRUuzmff1eNWhOA5QZALwWR/ccJfaf0xHcKHN3pF9Xe+Z62Jl6pC3Ur2UKw19PTQ==", "signatures": [{"sig": "MEYCIQD8nJQrweF4I1MKp5VubJXjzrJk33jgWk/IRH5+tXd7mQIhAO9cBGgKSTK7Q3Q4U5gCK0UKks0Xsr8gvD9KsT5MfQao", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8603}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.16_1739534356721_0.5101353876272945", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "967c3697967bec8362362c6221c961394b539d8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-4P0vjw0A0M0yjkguPUJ/NBNcP3NPmimg3G7T9x9JTV07Wd+PLcQUQNC4yRAte0xvvFTwC+dHVCWt0p7XIDm9Tg==", "signatures": [{"sig": "MEYCIQD7jwPHNhw9V6C1eaHACGrGHf2U2gMON/DTXIUAWVoqCQIhAJed1KwM2rixE7scnNg07QrzmOp6U2tRzgA0ShhK1Nld", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8603}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-alpha.17_1741717509397_0.7939624714644733", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "339f1ce355eae242e0649f232b1c68907c02e879", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-JfuinvDOsD9FVMTHpzA/pBLisxpv1aSf+OIV8lgH3MuWrks19R27e6a6DipIg4aX1Zm9Wpb04p8wljfKrVSnPA==", "signatures": [{"sig": "MEYCIQDwPJkT1Ax9HEDCpK4fhzypZc28ZgWeh+Ao/AO0FOTOhQIhAKdr8n2ujk+ZVKNo8OlA0SLZgsM0YT6C/SKwLaD31K3Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8304}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_7.27.1_1746025746274_0.5935142790485592", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0663281db179b1fc8086fad2652c7fccf8eade38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-JtYJFsWjzLsDs2+pNkZyRWNMI6WIx+ljxwqDGuwEflw7dBI3IzFlSLOFsc3P3YgA041rTKRO0WGHN1+VThIyRw==", "signatures": [{"sig": "MEQCIC/kqCHSMy5R1R+Dzp8lvXvSjLcyCCvTx3wUVnptZvhSAiAzzKnZj/OZ9/D+88jTlriZ/iXntZU5UKBBAStDROVGKQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8577}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-beta.0_1748620282726_0.5620978882502545", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-react-pure-annotations", "version": "8.0.0-beta.1", "description": "Mark top-level React method calls as pure for tree shaking", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-react-pure-annotations@8.0.0-beta.1", "dist": {"shasum": "eaca26052da775d2ca9126aa5c8eff2c2ce9fc77", "integrity": "sha512-TXfk/l+JhFrcUMmmEJ+l2NYnfSNouOhC9F4VSybzZ7Tev0Ubr+3DT2tFJwBtiYIaSdgSUbMNnFuVBuPxwWcPGA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 8577, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFhtUDz7n1gH4Bq9dxUEgBe6sq8omEwMYGvFqGpLqcE0AiEAkjm+8HYlvOf3Jlgv8RX2lhYJ2PfPpufP2YDakyjAsVM="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-react-pure-annotations_8.0.0-beta.1_1751447067816_0.8208805986045529"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-05-26T21:43:13.452Z", "modified": "2025-07-02T09:04:28.214Z", "7.10.0": "2020-05-26T21:43:14.062Z", "7.10.1": "2020-05-27T22:07:55.570Z", "7.10.3": "2020-06-19T20:54:24.707Z", "7.10.4": "2020-06-30T13:12:38.890Z", "7.12.1": "2020-10-15T22:40:27.736Z", "7.14.5": "2021-06-09T23:12:45.976Z", "7.16.0": "2021-10-29T23:47:48.177Z", "7.16.5": "2021-12-13T22:28:28.168Z", "7.16.7": "2021-12-31T00:22:37.691Z", "7.18.0": "2022-05-19T18:16:34.342Z", "7.18.6": "2022-06-27T19:50:21.541Z", "7.21.4-esm": "2023-04-04T14:09:44.613Z", "7.21.4-esm.1": "2023-04-04T14:21:42.356Z", "7.21.4-esm.2": "2023-04-04T14:39:37.294Z", "7.21.4-esm.3": "2023-04-04T14:56:27.034Z", "7.21.4-esm.4": "2023-04-04T15:13:37.606Z", "7.22.5": "2023-06-08T18:21:34.451Z", "8.0.0-alpha.0": "2023-07-20T14:00:16.455Z", "8.0.0-alpha.1": "2023-07-24T17:52:49.358Z", "8.0.0-alpha.2": "2023-08-09T15:15:13.325Z", "8.0.0-alpha.3": "2023-09-26T14:57:24.149Z", "8.0.0-alpha.4": "2023-10-12T02:06:38.595Z", "7.23.3": "2023-11-09T07:03:59.927Z", "8.0.0-alpha.5": "2023-12-11T15:19:28.012Z", "8.0.0-alpha.6": "2024-01-26T16:14:26.973Z", "8.0.0-alpha.7": "2024-02-28T14:05:26.781Z", "7.24.1": "2024-03-19T09:49:00.881Z", "8.0.0-alpha.8": "2024-04-04T13:20:08.446Z", "7.24.6": "2024-05-24T12:24:58.823Z", "8.0.0-alpha.9": "2024-06-03T14:04:45.391Z", "8.0.0-alpha.10": "2024-06-04T11:20:23.353Z", "7.24.7": "2024-06-05T13:15:37.248Z", "8.0.0-alpha.11": "2024-06-07T09:15:47.705Z", "8.0.0-alpha.12": "2024-07-26T17:33:41.520Z", "7.25.7": "2024-10-02T15:15:07.264Z", "7.25.9": "2024-10-22T15:21:22.224Z", "8.0.0-alpha.13": "2024-10-25T13:54:23.802Z", "8.0.0-alpha.14": "2024-12-06T16:54:13.816Z", "8.0.0-alpha.15": "2025-01-10T17:24:40.233Z", "8.0.0-alpha.16": "2025-02-14T11:59:16.900Z", "8.0.0-alpha.17": "2025-03-11T18:25:09.590Z", "7.27.1": "2025-04-30T15:09:06.483Z", "8.0.0-beta.0": "2025-05-30T15:51:22.987Z", "8.0.0-beta.1": "2025-07-02T09:04:27.980Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-react-pure-annotations"}, "description": "Mark top-level React method calls as pure for tree shaking", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}