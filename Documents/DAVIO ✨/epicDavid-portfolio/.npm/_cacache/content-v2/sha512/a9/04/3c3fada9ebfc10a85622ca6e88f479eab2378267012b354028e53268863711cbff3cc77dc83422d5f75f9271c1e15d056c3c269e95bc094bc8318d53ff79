{"_id": "babel-preset-jest", "_rev": "316-d933bd516869cf7787f0a40d8f4d0f27", "name": "babel-preset-jest", "dist-tags": {"next": "30.0.0-beta.8", "latest": "30.0.1"}, "versions": {"0.0.1": {"name": "babel-preset-jest", "version": "0.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@0.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "75a0b102e0f171f89b808e8bf6ddc853673c08c8", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-0.0.1.tgz", "integrity": "sha512-HYjWdg4zsLd7ESJFec7TbmW0GP1JIzm3hiMVEAJoaHOLVRKFMp6FGiIBjlMIvGly6HZBjHGGv8PXVFr5ydhHFA==", "signatures": [{"sig": "MEQCIGBVlRITQfg3UJadLHBG3UwhA99tjW9//QOMszIcaMjPAiBT0w6NA+eAGZrl3JDIozpIjzDwHZ5TsQEbMWc7TfhOgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "75a0b102e0f171f89b808e8bf6ddc853673c08c8", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"babel-plugin-jest-unmock": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-0.0.1.tgz_1456381390025_0.8412327701225877", "host": "packages-9-west.internal.npmjs.com"}}, "0.0.2": {"name": "babel-preset-jest", "version": "0.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@0.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "12187e9a847128e0b8b48871432d56c9fe98f090", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-0.0.2.tgz", "integrity": "sha512-ILJ/bdG43p/wkyKI64mIrrw6i8oYb3M+KgGGkynWFxNcyWmmXf2tTPs6IqBrwdWyiyX99MlyUo9QjTLkTjcaHg==", "signatures": [{"sig": "MEUCIG5SkAqXvLPT1HRYMDLRlcluOZ5SyoiltjHv3EmbW/pYAiEAsEogF6176Bmhz95MSjF8+12DG0W09HvHIfML3vwz4/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "12187e9a847128e0b8b48871432d56c9fe98f090", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-unmock": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-0.0.2.tgz_1456387303512_0.4187697102315724", "host": "packages-5-east.internal.npmjs.com"}}, "1.0.0": {"name": "babel-preset-jest", "version": "1.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@1.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0dfee6c11aadce0ed8e41613b1bac70167314638", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-1.0.0.tgz", "integrity": "sha512-xiiyuzavV4c6kiEC+gKTxG1Ob3OY784n81ilHOGFIkG9gU2xZG4cYVO0+10xqKPeOGRWBMquv3bSrxG4B2WVRg==", "signatures": [{"sig": "MEUCIGvMffbl5IbulmgZNUmZBBn4VeZQHevm0AWMRNTAdKUFAiEA7bES2wzk9Dq+ajZ2d5VQxuO3EVIXF9SfbryR7/vfHbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0dfee6c11aadce0ed8e41613b1bac70167314638", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-unmock": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-1.0.0.tgz_1457137786175_0.00751109654083848", "host": "packages-13-west.internal.npmjs.com"}}, "9.0.3": {"name": "babel-preset-jest", "version": "9.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@9.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "17af40dae9c6eafc052f36f74a30e18e6b7bcbdb", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-9.0.3.tgz", "integrity": "sha512-ccUSCg/2fJfMtNCagRRELQyv6UHdrjnk5GUbfhtVqmR7z2Gkzz1lD3p7t9MJPLnfaZqd4iszSQIH7cpDOoCikg==", "signatures": [{"sig": "MEYCIQCYeoEjNrz99cU50nUznOrMaHoA8cZvlZI7WUKJLv727AIhAJFoasJTjXJcu2eSGqf1FPhBtl03W3I5d+C9xBgr0XSF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "17af40dae9c6eafc052f36f74a30e18e6b7bcbdb", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-hoist": "^9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-9.0.3.tgz_1458695377020_0.05409627943299711", "host": "packages-13-west.internal.npmjs.com"}}, "10.0.1": {"name": "babel-preset-jest", "version": "10.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@10.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed7d0bdc4ed7a35752beb22c090bd026b3e2bf07", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-10.0.1.tgz", "integrity": "sha512-Tbzbny/TYzXgl/B50HNrxOOgM1/efU6s9bV65YbDSz3iM0KTPvG45gFvYGrYNcDPc6xRBLXpy+msU/IMKZ+1FA==", "signatures": [{"sig": "MEUCIQDKc9qR4LPtXH3+jNUN0kFlVjZItyx3hQWUaxbhhmSTJQIgCCrCO5svVm8mKDunhEu6BJc7jhcCEybf6IFEFhzRXcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ed7d0bdc4ed7a35752beb22c090bd026b3e2bf07", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-hoist": "^10.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-10.0.1.tgz_1459381449732_0.27721701632253826", "host": "packages-12-west.internal.npmjs.com"}}, "10.0.2": {"name": "babel-preset-jest", "version": "10.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@10.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8b7c610c612f274db3926824715e841a0a814b9a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-10.0.2.tgz", "integrity": "sha512-dEFJ0hhsMwn4G3X1/EaPav1nnJzsWnBugsA8rMAyDWQT7ICwqNExIa/D/gajkcSgSyMXZEGgJ4kpy4BjQZecfg==", "signatures": [{"sig": "MEYCIQCKavoCa94p/fSX886BJddCo1iY7PBB3knStHrilgnYUwIhAJFsnEyFDmM5uymFpvPWcDgszHzPCMQkg+AFodg4Coke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8b7c610c612f274db3926824715e841a0a814b9a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-hoist": "^10.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-10.0.2.tgz_1460363041234_0.13817338016815484", "host": "packages-12-west.internal.npmjs.com"}}, "11.0.0": {"name": "babel-preset-jest", "version": "11.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@11.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7cccb66d919c121e41e84723c45bb689d207ae7c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-11.0.0.tgz", "integrity": "sha512-TYpXIuHx2SzyTNOCe7asiN3iKQxpTKX2qH0K7ZnCrn8X2HrBMopcT5ncgWvjMNQtKAg6tEZRAfhKyY4CZqqApA==", "signatures": [{"sig": "MEQCIDk3E1wUWh0I2rraENeoFquAqqUEG5Q7XHmHBLstYv0VAiAaCV+5/7DVL8d4B8djw9PFN05irh95ajFZDtI6fSOsBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7cccb66d919c121e41e84723c45bb689d207ae7c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"babel-plugin-jest-hoist": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-11.0.0.tgz_1460447765954_0.2240443287882954", "host": "packages-12-west.internal.npmjs.com"}}, "11.0.1": {"name": "babel-preset-jest", "version": "11.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@11.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a4bd9afd01d30aa70b9643784ac9b693f61b253", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-11.0.1.tgz", "integrity": "sha512-6fQP1AXNckOVRvUy1KMtjsjBUnAo9nsk83dArPoYWwf4vA7R/lLWJI7XpAKqWoQLapOAvTyq5UsFvW6j/f66BQ==", "signatures": [{"sig": "MEQCIA2EiuTb1Tr8Ro9W8mvrnbn7DD7cXl8j5WoOsKedFfU5AiBsbTrS5wev0u5bC/sIVvM7gXFhNBlcel55v1Y8bLzntg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8a4bd9afd01d30aa70b9643784ac9b693f61b253", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"babel-plugin-jest-hoist": "^11.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-11.0.1.tgz_1460692946570_0.27588037331588566", "host": "packages-12-west.internal.npmjs.com"}}, "11.0.2": {"name": "babel-preset-jest", "version": "11.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@11.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1d2b1e6e1e978e2ca488a26b0d128f7ceb08783", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-11.0.2.tgz", "integrity": "sha512-ViQrZBC0uOyRkPTBJd5Uoj9iuLrSOu2O6HXyuGrlYSsIIkiunG5Y2nvuLFm+o4g9cFf3c6WyL1H+n6A9JoTheQ==", "signatures": [{"sig": "MEQCICbIvNjZpviffauAelrQx5Czmf9Osk3C95tBrNQtqKi3AiAwvkgeiFBO4VxPOfo/CvKyOFtYwQHOGA8/MAhd2eIhOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b1d2b1e6e1e978e2ca488a26b0d128f7ceb08783", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"babel-plugin-jest-hoist": "^11.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-11.0.2.tgz_1460952198921_0.8115114977117628", "host": "packages-16-east.internal.npmjs.com"}}, "12.0.0": {"name": "babel-preset-jest", "version": "12.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76ef4813dce8796843d67ce0a3d31ef2265e5e4c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.0.0.tgz", "integrity": "sha512-vJ2cai68l/DFa5jIcX8rvB3034X9eupEA43ao2POzoplsLWSgU59X96mSZFXNUtz65p8PfF1nJj7Ko+A0dyINg==", "signatures": [{"sig": "MEUCICAiXDb/0UIaiZK/ut0Ksz7jHttcqgzCpUQzIwAxgtFvAiEA6vugWtZnBiTOgdjwexPH1UhN7zKNnH7AedvkzXpNvM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "76ef4813dce8796843d67ce0a3d31ef2265e5e4c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"babel-plugin-jest-hoist": "^12.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.0.0.tgz_1461734650064_0.06774087226949632", "host": "packages-16-east.internal.npmjs.com"}}, "12.0.1": {"name": "babel-preset-jest", "version": "12.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0546060af940701a69ac3f0bf7f18b14da2a5476", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.0.1.tgz", "integrity": "sha512-GzcgmZOsaKc3wa60yMY/t7Q5SnlgSWzMpQzoW2jghAzzn0jLOKteu8j1kdAzgTAbE9aKDp4LnrTGmK+fWFhmmw==", "signatures": [{"sig": "MEUCIQCgKihmoo7oowkVOmIAWuYUPgwcaQECH6LGmMC2vxhTagIgbP7ZDTZiRncvkVFz5d+ubPn5PZS1EwWBSCLjsc5K4qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0546060af940701a69ac3f0bf7f18b14da2a5476", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"babel-plugin-jest-hoist": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.0.1.tgz_1461751775769_0.9287438411265612", "host": "packages-16-east.internal.npmjs.com"}}, "12.0.2": {"name": "babel-preset-jest", "version": "12.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "193af35e9b0a0e470e508917a1b7a2065a5d336a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.0.2.tgz", "integrity": "sha512-pMNpD6OIoutxrPHjHahMT6UCfyAUBR3V7PQmPynL2fAeAjpCl3n9O1W0EtyYTBDsHRpV1d+BQnJku5+/ho3XwA==", "signatures": [{"sig": "MEUCIBNL1ZHGIa3MDraPjiQ3bzral15agFZXo/m5HgIbfE62AiEAkFol86jo6A2oxLz6vn/f+N1n9xGZM5IM3tDxXVXawzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "193af35e9b0a0e470e508917a1b7a2065a5d336a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.0.2.tgz_1461817899134_0.9889642565976828", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.0": {"name": "babel-preset-jest", "version": "12.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b401e6e8a1c4eabc55dbcb59bc46312eb1b6bf6", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.0.tgz", "integrity": "sha512-vjWBMvOafJb+LbxWNrAzJOm1Db+b4KEMFaZ/7iLWoIlzWRK0mOgnRhvQKg8Tqu3kWiiXcV8xeFr907zFxDtTIg==", "signatures": [{"sig": "MEQCIBpEElxzHkoc975dZ47XD6lDK+RmZ6XNBsvNKcYWyvrcAiACQFQedJ0cteSXPxbCs1/PHr+Vr1hPM+3Ga6kEzcPAZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0b401e6e8a1c4eabc55dbcb59bc46312eb1b6bf6", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.0.tgz_1463767563219_0.9721926448401064", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.1-alpha.2935e14d": {"name": "babel-preset-jest", "version": "12.1.1-alpha.2935e14d", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.1-alpha.2935e14d", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2081b46d2c3bb1cdfff114b5d73620b0ed21c818", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.1-alpha.2935e14d.tgz", "integrity": "sha512-vPHKpLh88v1XwuLoJ4PF5JqZaU4CEdhSr6wLyUvhBTWGZjuXRd4IwrXr66gVn1jrtHZ9L6BUHxwGlTCfbwv29A==", "signatures": [{"sig": "MEUCID1PcOnV8n5BjbxYa0SvLbkXlWQHF6l5PQzeli2/Pob2AiEA1RuAa4jBJpyoszviiKUnp7BmVhOY94P1cQ8PQ3CU6Ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2081b46d2c3bb1cdfff114b5d73620b0ed21c818", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.1-alpha.2935e14d"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.1-alpha.2935e14d.tgz_1466141593237_0.3918725224211812", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.2-alpha.a482b15c": {"name": "babel-preset-jest", "version": "12.1.2-alpha.a482b15c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.2-alpha.a482b15c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a42b56817cde90df6bf207ad20fd08755789aa7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.2-alpha.a482b15c.tgz", "integrity": "sha512-pkrIKbBe9GW7bCR2544Hlx+eneS33+ZwDPXsiZWOuTeTWLOPoPdgk046lwZwinGjeIGybjd0159ggKy1TugfdA==", "signatures": [{"sig": "MEUCIQCtboouPm8kn3XPK1nPhBDhRR+eSqTcE4WoiRBI6A64fAIgYbON9LgVIIahFJyuWEs8r+Jc3LTuRnlqHHz3fyR6r4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3a42b56817cde90df6bf207ad20fd08755789aa7", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.2-alpha.a482b15c"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.2-alpha.a482b15c.tgz_1466150283078_0.05130982678383589", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.2-alpha.6230044c": {"name": "babel-preset-jest", "version": "12.1.2-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.2-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c188495dbee2941325f2cd13f95a42214b5fbe5a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.2-alpha.6230044c.tgz", "integrity": "sha512-XXEVuZpu4+f0hprc1+JGd0aTgSdSq9T1/m56TqEfIZX1jt2jsWGsxLxgWNIgyHO6lsuDEH2EUThYOTnziDuc2g==", "signatures": [{"sig": "MEUCIQDC/MfY7mcIVBfgLPnq7UZG08NM7854kY8/I5MRirunpgIgJD94wPc9UVD3ByV3L6obGuv+YjQdG+Yv1apc7e1fDDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c188495dbee2941325f2cd13f95a42214b5fbe5a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.2-alpha.6230044c"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.2-alpha.6230044c.tgz_1466536514412_0.4771072403527796", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.3-alpha.6230044c": {"name": "babel-preset-jest", "version": "12.1.3-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.3-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0486f3321d314354e2e131090e7bb97cf0d3349e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.3-alpha.6230044c.tgz", "integrity": "sha512-kVJ/zuYUHOhgWi95TWVwjxN8WKMOxCq6/8IJWAQmtS8FD/Cqga+gbPMpu43Zv//c/XZCYhZowk420tn4jfyaQw==", "signatures": [{"sig": "MEQCIAtWKzxuP5DnVIqxyD1n5TlNSJqcC1dtN9P12ZiGGbFkAiA2mSsWsahsKfn29xzwUHpd9R2evlZddSfN1DcjcEpb3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0486f3321d314354e2e131090e7bb97cf0d3349e", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.3-alpha.6230044c"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.3-alpha.6230044c.tgz_1466539907270_0.7115114263724536", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.4-alpha.a737c6e5": {"name": "babel-preset-jest", "version": "12.1.4-alpha.a737c6e5", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.4-alpha.a737c6e5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f2586a2785ab14e2e560327f8771d1cbc62624c9", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.4-alpha.a737c6e5.tgz", "integrity": "sha512-HtDCmlvPz9B+yqRCj3+FPCm0CCWTBxg5dtRkCVblGya+nftuYZlbgZSEua3dy6DdGqJwWQ2/bknHQ7ON2xVeoQ==", "signatures": [{"sig": "MEUCIGFXgAue9W58Cs4kfYUc8k+3lRd+Rv+aUQvUdLTyzs5eAiEA4+L9LItD7+9gHeUHZhAdu7N0x9PnnYzGSDfCuGhohow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f2586a2785ab14e2e560327f8771d1cbc62624c9", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.4-alpha.a737c6e5"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.4-alpha.a737c6e5.tgz_1466566601712_0.4843530533835292", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.5-alpha.b5322422": {"name": "babel-preset-jest", "version": "12.1.5-alpha.b5322422", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@12.1.5-alpha.b5322422", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bf3311a8cfef50e43bee37f68f98b8b131b274ff", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-12.1.5-alpha.b5322422.tgz", "integrity": "sha512-TG/sZYvdY1WAYuoSkzKn+D/7Yhb5cXOGDz3EqUAprmDs/EHg2mMd3Nj0PT+IgQpPQ/0VEWXPKCK3rq1YYXNLmA==", "signatures": [{"sig": "MEQCIQDPQTXePa91HBIb+nlwHudM3bFuzimsqLUfWmgGMOcZ2wIfFLIPBTLRVmVHe6NJwB+UAxCV36ZQSzjxmkdM5TSktA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bf3311a8cfef50e43bee37f68f98b8b131b274ff", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^12.1.5-alpha.b5322422"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-12.1.5-alpha.b5322422.tgz_1466577948053_0.5690079582855105", "host": "packages-16-east.internal.npmjs.com"}}, "13.0.0": {"name": "babel-preset-jest", "version": "13.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a53f4860154caddbd1cb23e30e73235379737d0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.0.0.tgz", "integrity": "sha512-gM4dXUF6OeR6GnOuRdUTuPbJJRdH+bQIKyZhaRTD3e0qaAJArNSGnJVOjSo1DWtrnmoN89OVbMMhSAjQvpqfAQ==", "signatures": [{"sig": "MEUCIF+2prJS55+ynqcUFnEhO6gkjPWbbijIaNj2vUN3aYpiAiEAziID24QNI/hMIglHw//ZaYA5OuK5hQZtLZ82uKF8BYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5a53f4860154caddbd1cb23e30e73235379737d0", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"babel-plugin-jest-hoist": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.0.0.tgz_1466626696133_0.09706400008872151", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.1": {"name": "babel-preset-jest", "version": "13.2.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac6c5d7961f5aa763b41895c491b99834fb95460", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.2.1.tgz", "integrity": "sha512-m56ieQhZu+1rD9r28i3UHMZdBBNtXwKDOwHxcRR6IzTJ9urRY2XRGF61d/xiGfQifgFXm1AI3hOEknf5LPwTnQ==", "signatures": [{"sig": "MEUCIFBacpUhPUUU3bpoyNrsF209lDb68FKuDB2xaqV6CbflAiEA/1GLPQ/R8ajiBb0tYHJBMKvxx3JTYxHPF3u1uG1zChM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ac6c5d7961f5aa763b41895c491b99834fb95460", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.2.1.tgz_1467856774646_0.3223738451488316", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.2": {"name": "babel-preset-jest", "version": "13.2.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.2.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "62e271f69a2a0b71cb27897d81679d64c0323a4e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.2.2.tgz", "integrity": "sha512-Ci8Om1CkQ/xTqy5BoSdNclSTyV9XCWqUvK+e2mG8vFP/ivu5JwpEqYnwK9pbgbkD19CbKCy+hu2C0kQTKhqS/Q==", "signatures": [{"sig": "MEQCIDkIT2JRead/DKK7jy0ObM8ESdtA4A0lQIEQC+DQYDzgAiApPFaax5UG/dsm98KHD93zOP0NyHJ0jVc9gHx4D7F7nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "62e271f69a2a0b71cb27897d81679d64c0323a4e", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.2.2.tgz_1467857579973_0.7690031304955482", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.a44f195f": {"name": "babel-preset-jest", "version": "13.3.0-alpha.a44f195f", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.3.0-alpha.a44f195f", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5dd38b314d8bdcbe90b431912874984ab06e4d1b", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.3.0-alpha.a44f195f.tgz", "integrity": "sha512-hpFtX1cYtIebs3nOi/6ibfmuyLYAOSCQOBqseOrLuZPtlpoRxA3NkysPMe7iY1Vf32zUo8wA75y3x1bWPwUHPg==", "signatures": [{"sig": "MEUCIQDOhL/a4uhbcW1UvACXDmkhtYmFpvq5KGXaZ1vmJu+q4AIgJeaSiERxr9v6+CU2o8CzDMzmASK6Q90wwQLcwXkc+4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5dd38b314d8bdcbe90b431912874984ab06e4d1b", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.3.0-alpha.a44f195f"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.3.0-alpha.a44f195f.tgz_1468231007542_0.15473377145826817", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.4eb0c908": {"name": "babel-preset-jest", "version": "13.3.0-alpha.4eb0c908", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.3.0-alpha.4eb0c908", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1e9eb517ee0f6e32e39e3b45ae7f3f6697f0054", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.3.0-alpha.4eb0c908.tgz", "integrity": "sha512-kewRiri/jPkQy3HzenFRVe7fzMi7sLG30JufeF8tB/YI0fC6DVFAG86q7Tabqv8nVLq5XuCtRiN585my+fq2Ig==", "signatures": [{"sig": "MEUCIQDbr1wzTc4SJe9rJPt5JNF/ZLJRl4/eBIyOZSdQZBHRpwIgRSrmWV6rMCtrUiW2fNf0n6lj4S0GLDtNQgWPYPTGAGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c1e9eb517ee0f6e32e39e3b45ae7f3f6697f0054", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.3.0-alpha.4eb0c908"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.3.0-alpha.4eb0c908.tgz_1468231471243_0.514961053384468", "host": "packages-16-east.internal.npmjs.com"}}, "13.2.3-alpha.ffc7404b": {"name": "babel-preset-jest", "version": "13.2.3-alpha.ffc7404b", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.2.3-alpha.ffc7404b", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e45cdbce249321fc1c8662d42c0c259a0e0600e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.2.3-alpha.ffc7404b.tgz", "integrity": "sha512-m8K4lDjURw5ylP06SEJbnbLc0Ga6E9K2C/U6Klc06qKt0o39lemrnDaFiOrQ8NvMiDS/IzoxIeOJDlgqHPmSww==", "signatures": [{"sig": "MEYCIQDqloK1hr6eL2489dlknaoMMU9Kx3DaOXdVaqVLhHEJfAIhAN3V/NxE4bTJTOoTY7PxVueMN85ejrSuL73upho5ahjL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9e45cdbce249321fc1c8662d42c0c259a0e0600e", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.2.3-alpha.ffc7404b"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.2.3-alpha.ffc7404b.tgz_1468232399069_0.6999936471693218", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.ffc7404b": {"name": "babel-preset-jest", "version": "13.3.0-alpha.ffc7404b", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.3.0-alpha.ffc7404b", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9814afaaa0f63ca41c4048f9789e87ad2ccb626c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.3.0-alpha.ffc7404b.tgz", "integrity": "sha512-mpwqlGviqnl3PXYnP2+gcXJ+Wi2OxFdt8MJxkc0+3KMJa7c4gorUn6q2rnxKAL79vGUyy35qXabufbtd1MlV9A==", "signatures": [{"sig": "MEUCIGw5bv9pdrbwhpIEmFsBB1hveaicPbqViJUEXZ7AtBD9AiEA60LTvCHhvL/Tycq8LTsZ7jlBf1Od5ngqPHAtp0mV1E0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9814afaaa0f63ca41c4048f9789e87ad2ccb626c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.3.0-alpha.ffc7404b"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.3.0-alpha.ffc7404b.tgz_1468232409636_0.8125236928462982", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.8b48d59e": {"name": "babel-preset-jest", "version": "13.3.0-alpha.8b48d59e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.3.0-alpha.8b48d59e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b131db266709497f4ae961d8501393d261b57016", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.3.0-alpha.8b48d59e.tgz", "integrity": "sha512-/FGPTnfelZHBALJlx/m1o0nMCK1bHU4cDsMcXWFnXE81b5kIoC0x190saL5+smHrSHfjg5Fl6m1zzgmiNztwmg==", "signatures": [{"sig": "MEUCIGECTbUdvDar2UE07TBrKQDMf8hyL5pBNvRdSEftgcL2AiEAjzEE+tGO0Mb8Nl5TJlUe3qLM2ytG2iSmvUpozoG1erI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b131db266709497f4ae961d8501393d261b57016", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.3.0-alpha.8b48d59e"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.3.0-alpha.8b48d59e.tgz_1468390444864_0.5104869871865958", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.g8b48d59": {"name": "babel-preset-jest", "version": "13.3.0-alpha.g8b48d59", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.3.0-alpha.g8b48d59", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a10a67ca724a254b66eab5c9015914681dda15f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.3.0-alpha.g8b48d59.tgz", "integrity": "sha512-p8FQNvG0JB6eAPjP3YGxr9gU0kbmNAvKnt7IdYB5x9cLwF8L3CHMXX4MSSJ55fedVtdPjnl/9SrBWmM5BxaJKA==", "signatures": [{"sig": "MEUCIQDGWbIe7AAUKmyCPl7lPbi0nwJGzlBQgOv92vm9E0iy3QIgNJYoIeey6fIk4gasX1V/BZ6O9OyHwzWSNOJMqr4qjfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4a10a67ca724a254b66eab5c9015914681dda15f", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.3.0-alpha.g8b48d59"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.3.0-alpha.g8b48d59.tgz_1468391977755_0.21692585316486657", "host": "packages-12-west.internal.npmjs.com"}}, "13.4.0-alpha.d2632006": {"name": "babel-preset-jest", "version": "13.4.0-alpha.d2632006", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@13.4.0-alpha.d2632006", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e28d8f5a2bcb3efb07bd5abe6893c8ae4a39156", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-13.4.0-alpha.d2632006.tgz", "integrity": "sha512-eUiEg81CgULaBrmFoqjVbzXI7zaZXnQIyttGDWk6pj/FU35LabIrt1TWyunuzOTy9EcKYykzbmASr9U4SzXrtQ==", "signatures": [{"sig": "MEUCIHQznIUKBUU1nYxkQxeUyY4guhxLPQs55vyMbUqromJIAiEApxVtNrxYGMu8bi+I/u8NejW9ezN/vmh0AeTZKu1Qj+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4e28d8f5a2bcb3efb07bd5abe6893c8ae4a39156", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^13.4.0-alpha.d2632006"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-13.4.0-alpha.d2632006.tgz_1469609830848_0.9451488982886076", "host": "packages-12-west.internal.npmjs.com"}}, "14.0.0": {"name": "babel-preset-jest", "version": "14.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73ea8d75d2bb0886eee31523d9ce0bb06490b042", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.0.0.tgz", "integrity": "sha512-dkyX2QVnyVniylJtB0JT6QXnrMvfcP2fO/PIpQJyIahkVEfkMCeoY9xAi5vSBXxLc+nqVqL9Kumgf1d7joLpLg==", "signatures": [{"sig": "MEUCIQDhzDJ1vxL9zHnvHoy6tlWBDb06QPg7NwPFDfZzBZTAIAIgZ/zeWLbrgy5rz2pZgCSiTSbZ/5wC9YUwEhp296gc3F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "73ea8d75d2bb0886eee31523d9ce0bb06490b042", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.0.0.tgz_1469610876714_0.6191915334202349", "host": "packages-12-west.internal.npmjs.com"}}, "14.1.0": {"name": "babel-preset-jest", "version": "14.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "55da655d620ad10005b2bedf46accc525ad9c2a7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.1.0.tgz", "integrity": "sha512-nOblRpqDT0Zwwndxw0gNxTiRpFZz3UU4w/xTnQFjlvW9CQ9f5Y3R2RlK4Nn/eHT6aNeZEdMCxbvOMeD7ATXyag==", "signatures": [{"sig": "MEUCIEbPXHW20It5mXh+aJthAgKJKFda8UdWJ3HwZqrvfDtOAiEAhBRG1nFUvJ4pMkCINKkhO/uMDzkVmtbGP5WeaCD4bRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "55da655d620ad10005b2bedf46accc525ad9c2a7", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.1.0.tgz_1470047189284_0.4109590577427298", "host": "packages-16-east.internal.npmjs.com"}}, "14.2.0-alpha.ca8bfb6e": {"name": "babel-preset-jest", "version": "14.2.0-alpha.ca8bfb6e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.2.0-alpha.ca8bfb6e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "348d74524a02f788f0e57c8cdfd46b6d348634ea", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.2.0-alpha.ca8bfb6e.tgz", "integrity": "sha512-iRgf/uHrqt3Vg+MbmvyGINhaAtqJmtQieSXFIx9GTtvtWC8fs4cvUU7XbqeXUl68F6bSzuwJCUdHsT/cFB/3Yw==", "signatures": [{"sig": "MEUCIFMkzXC1pgJvoJ600BgGGBIsiuRkNWlSMeOik3mJRAE0AiEAnYVyGykLVsNJM6040/Hd4E+uf83agufQEhTusq+t7zE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "348d74524a02f788f0e57c8cdfd46b6d348634ea", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.2.0-alpha.ca8bfb6e"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.2.0-alpha.ca8bfb6e.tgz_1471287273350_0.5947008314542472", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.1-alpha.e21d71a4": {"name": "babel-preset-jest", "version": "14.2.1-alpha.e21d71a4", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.2.1-alpha.e21d71a4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d7328d7599709f5fd15732d9141db6d6b90e30f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.2.1-alpha.e21d71a4.tgz", "integrity": "sha512-f/K8edtMVRD+hQ7GcOEG4tD48YNYCDfgy7gTgNK2zJBzC1UOCeplW1i9cfM+YOD/49I9VPJk0Lj3QE7Fs43kEw==", "signatures": [{"sig": "MEYCIQCVqUuLf06RBqEo5iIJUfFjXAJnZrrE4HcdnntRjiVsKwIhAIMY/yOK3dknvNeGHAmC4C7f1PWxsPlRL+O5RiMQLGtM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3d7328d7599709f5fd15732d9141db6d6b90e30f", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.2.1-alpha.e21d71a4"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.2.1-alpha.e21d71a4.tgz_1471382313879_0.4993831079918891", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.2-alpha.22bd3c33": {"name": "babel-preset-jest", "version": "14.2.2-alpha.22bd3c33", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.2.2-alpha.22bd3c33", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08c3f2e9c6e59eb56a8ad3e0ac342e6d5e9a298d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.2.2-alpha.22bd3c33.tgz", "integrity": "sha512-du2vaCuGTuEdPW2/oPIidksGkiK6dGKhfDtZtbmGnvNCrLob9wt8nfxJv3c634E3m3hAvBE26rXoPjc22LM/7g==", "signatures": [{"sig": "MEYCIQCI3QCB44KotmDCRduU8p8uX6ky/roIX5yKo8rz7JuxAAIhAPdTkdcy6pUCJ1JoERe01+pSNURKRFQwzDibf5u3Vdpi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "08c3f2e9c6e59eb56a8ad3e0ac342e6d5e9a298d", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.2.2-alpha.22bd3c33"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.2.2-alpha.22bd3c33.tgz_1471388085972_0.11583728529512882", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.0-alpha.d13c163e": {"name": "babel-preset-jest", "version": "14.3.0-alpha.d13c163e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.3.0-alpha.d13c163e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6eefcc3a2393cea9d4ce2385c066e01f2676e6d0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.3.0-alpha.d13c163e.tgz", "integrity": "sha512-SJntgbOYIgS5qFZHkSeq4h5RxI3ssatZZEe8FsK3DP53Tx3rRep0SAdlOZ6+9tNYXB+oxeqNWS2fiudVFnAz2A==", "signatures": [{"sig": "MEYCIQCHhZFyUa6cgL2dmtthcM4LEaXI1ZsbZsII7etpUYBM5gIhAPVuStWid/PqPlbsOvyJXOkJTRXLoWeSqRBwkC7vPH0Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6eefcc3a2393cea9d4ce2385c066e01f2676e6d0", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"babel-plugin-jest-hoist": "^14.3.0-alpha.d13c163e"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.3.0-alpha.d13c163e.tgz_1471552521722_0.8892539662774652", "host": "packages-12-west.internal.npmjs.com"}}, "14.3.1-alpha.410cb91a": {"name": "babel-preset-jest", "version": "14.3.1-alpha.410cb91a", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.3.1-alpha.410cb91a", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "78665b7c7b688036bbb009ce6a4324a5cfcedcbe", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.3.1-alpha.410cb91a.tgz", "integrity": "sha512-sivBqOWXPSapXvHTo50DqtF0AyRISKeJZNFsSifVebWQkso78/XUhf9Re1u2nN+8MFqAduhnQpUrGNWmv99ysQ==", "signatures": [{"sig": "MEQCIHQmbBJHO2drluP8qKgv+AfgjabEta575uw5K/eOI5HCAiBDFSNkoyWnf5veC2jvp5N7pClYwyuzW2Ipr6w7bbqSzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "78665b7c7b688036bbb009ce6a4324a5cfcedcbe", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babel-plugin-jest-hoist": "^14.3.1-alpha.410cb91a"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.3.1-alpha.410cb91a.tgz_1472593923206_0.671647236449644", "host": "packages-12-west.internal.npmjs.com"}}, "14.3.2-alpha.83c25417": {"name": "babel-preset-jest", "version": "14.3.2-alpha.83c25417", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@14.3.2-alpha.83c25417", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7580ced1fc4e49408fcc979b3cfa8cf04e114ddd", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-14.3.2-alpha.83c25417.tgz", "integrity": "sha512-nFvlFi3YXq5L6l4rdLTwJ7WVr65hmqJJn6mUJCoT146A7ccpvAOmgT77aypwd+c5YQFprRpz0iGplVChmQ2i/Q==", "signatures": [{"sig": "MEQCIG6RT6+cnI0r+Tgyu+WrDcXGWMweV1Ls2GOlGgp7BJefAiBkGPCWaXB/OPKW/xcM1GPvHWt2iX/whffSuV43x1EQSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7580ced1fc4e49408fcc979b3cfa8cf04e114ddd", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babel-plugin-jest-hoist": "^14.3.2-alpha.83c25417"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-14.3.2-alpha.83c25417.tgz_1472669430875_0.3869765547569841", "host": "packages-12-west.internal.npmjs.com"}}, "15.0.0": {"name": "babel-preset-jest", "version": "15.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@15.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f23988f1f918673ff9b470fdfd60fcc19bc618f5", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-15.0.0.tgz", "integrity": "sha512-aaKIftI2z5rSXuSX+b/UenusEmSlutZivrELyHPxz+VksAJ2MNJ2l4oTvzTCi22eeBLMe04UeYMF4fycwXk74w==", "signatures": [{"sig": "MEUCIF7U+TbCwA62f/X2rhQXgsJoyP1CBbpkl5PyFK/hCpIFAiEAjjRj2ueElQehEf5bwHl6PdYVYfLW/l+UNSI8fH5rtQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f23988f1f918673ff9b470fdfd60fcc19bc618f5", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babel-plugin-jest-hoist": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-15.0.0.tgz_1472669723809_0.6084967420902103", "host": "packages-16-east.internal.npmjs.com"}}, "15.2.0-alpha.c681f819": {"name": "babel-preset-jest", "version": "15.2.0-alpha.c681f819", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@15.2.0-alpha.c681f819", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cacb80fafd19cbe61c1b88a8c76a2f734dfee985", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-15.2.0-alpha.c681f819.tgz", "integrity": "sha512-c5lOWP5PG3WLD0xh2/CoR0TuNn2HxiqShm7U5AoFue3V5T58GcVqUPL6C+WP2F+2ymo4plnhKOQsxKWhROFpsw==", "signatures": [{"sig": "MEUCIDDRvbHsdAL0ishyKhZu/Ow9UW201b2mK4GCyGlP0BC7AiEAs+vBUHC2DUMSrInvN878xIjNIlArmVegb+myZTmoE1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cacb80fafd19cbe61c1b88a8c76a2f734dfee985", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"babel-plugin-jest-hoist": "^15.2.0-alpha.c681f819"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-15.2.0-alpha.c681f819.tgz_1475139733700_0.5802954970858991", "host": "packages-16-east.internal.npmjs.com"}}, "16.0.0": {"name": "babel-preset-jest", "version": "16.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@16.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "417aabc2d7d93170f43c20ef1ea0145e8f7f2db5", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-16.0.0.tgz", "integrity": "sha512-ZpQDoNEsWMOMBdQVXjhD224zq+9SfFlA72oj+PwBPiAaX900jdEiDnnW3mG88KqGHvsVI30WV2qmhpkkoMa1Hg==", "signatures": [{"sig": "MEQCHxtNuyT4VwtrH7yVSER52sPFoGdCQ5N5Gn4ERBqxtdwCIQD/KBZj2wdhXz3CtfZfYqzumE3htGmJIzehxMdx5rmOOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "417aabc2d7d93170f43c20ef1ea0145e8f7f2db5", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"babel-plugin-jest-hoist": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-16.0.0.tgz_1475483907850_0.5196316472720355", "host": "packages-16-east.internal.npmjs.com"}}, "16.1.0-alpha.691b0e22": {"name": "babel-preset-jest", "version": "16.1.0-alpha.691b0e22", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@16.1.0-alpha.691b0e22", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b149e2884dd04a9e2f6f2ec283c15daadc201bce", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-16.1.0-alpha.691b0e22.tgz", "integrity": "sha512-30oIcpvq457j7grTSy9FYCGgnRiyJWoSfzcDgTVtKAuo8z3CDQ8TXLPfgW9eJ05RevDHYLGYKJCZEhSB/6jIJw==", "signatures": [{"sig": "MEUCIQD+69Mg/eo5lw38OicdmwKOO77C4bhosDkaBqiBvo+2wQIgcgdqdGnEPKnMOYho6bn2m0cRCSkhYYkBmgO0FLfhelY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b149e2884dd04a9e2f6f2ec283c15daadc201bce", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"babel-plugin-jest-hoist": "^16.1.0-alpha.691b0e22"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-16.1.0-alpha.691b0e22.tgz_1477639644623_0.7701420914381742", "host": "packages-18-east.internal.npmjs.com"}}, "17.0.2": {"name": "babel-preset-jest", "version": "17.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@17.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "141e935debe164aaa0364c220d31ccb2176493b2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-17.0.2.tgz", "integrity": "sha512-ZX5m9vGKEcwDi3scQIzVs5F7lF7aqm/cpzgq7JvmYtT829Iprms0zAkRJjd3ZITIgGTPHArZeeYxfXbADKmAFg==", "signatures": [{"sig": "MEYCIQCVcVQ62Uy2U9pqfP0jVpo5XZAg+lXQ1tpNHOgJjkVIFgIhAJKseh/elltKupVhJK8kL7sSk5Odenf5snKI7kyP8o3c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "141e935debe164aaa0364c220d31ccb2176493b2", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"babel-plugin-jest-hoist": "^17.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-17.0.2.tgz_1479170355634_0.8119601616635919", "host": "packages-18-east.internal.npmjs.com"}}, "18.0.0": {"name": "babel-preset-jest", "version": "18.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@18.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "84faf8ca3ec65aba7d5e3f59bbaed935ab24049e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-18.0.0.tgz", "integrity": "sha512-NJt+2uBq5VnCdTtSSDpXabsPzXVfpHohE2ZBlO3uDQBaNPe3iL9Ca9WWAoXnjzygDHBL0hGmCLVTYhFL8hg5ZA==", "signatures": [{"sig": "MEUCIDd8UoFKrStS8pc7cJKe+ofJWgzOMhYcX9mdNzB32VoaAiEAmO11y9+h2mm8vBG1H/qNW5hAHozV37ZyW3zo1mWZNQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "84faf8ca3ec65aba7d5e3f59bbaed935ab24049e", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"babel-plugin-jest-hoist": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-18.0.0.tgz_1481801061953_0.7550930224824697", "host": "packages-18-east.internal.npmjs.com"}}, "18.5.0-alpha.7da3df39": {"name": "babel-preset-jest", "version": "18.5.0-alpha.7da3df39", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@18.5.0-alpha.7da3df39", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "053021e6a2ac7f6bd1c1d295942397226253ae0c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-18.5.0-alpha.7da3df39.tgz", "integrity": "sha512-tM4Kt0yPsNuRoEb5is5KabhbK2SfaZ3Dxl/VIKvUQMkvzuYTzBHjrTjCD/TQyrARCAQQbYO5bN4hMG5iaO3g5Q==", "signatures": [{"sig": "MEUCIQDO2nJ5hT0lJuc+YPwVys9nuuTWktBg7AlP/2vGh4uGRQIgXNLk1OzitfTAQs5jiKWwy8P4DUo0CAh0JOi6RZvaKnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "053021e6a2ac7f6bd1c1d295942397226253ae0c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"babel-plugin-jest-hoist": "^18.5.0-alpha.7da3df39"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-18.5.0-alpha.7da3df39.tgz_1487350689219_0.47574325860477984", "host": "packages-18-east.internal.npmjs.com"}}, "19.0.0": {"name": "babel-preset-jest", "version": "19.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@19.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "22d67201d02324a195811288eb38294bb3cac396", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-19.0.0.tgz", "integrity": "sha512-Jb+X0+98UWEuPQdZSGHC9ZKkngeIcpc9MKAM51Ze9GFJnpBuk7u9qDbN2Irh7JwZA3lc/MQ4I/RyObSqDFMbpw==", "signatures": [{"sig": "MEUCIQCnNY4xhGSI0KAl8iNjWAHQ0+0/UK+98YwzjoMzSe1wigIgb6AnnOBhEtyJWUzqCMQgP6z12nAOME6UviJV6+KrceI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "22d67201d02324a195811288eb38294bb3cac396", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"babel-plugin-jest-hoist": "^19.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-19.0.0.tgz_1487669409196_0.6107435391750187", "host": "packages-12-west.internal.npmjs.com"}}, "19.1.0-alpha.eed82034": {"name": "babel-preset-jest", "version": "19.1.0-alpha.eed82034", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@19.1.0-alpha.eed82034", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1924ee9755629ff939e7bc348e42266cc869b9b3", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-19.1.0-alpha.eed82034.tgz", "integrity": "sha512-triL3eoeWC9iHkRyMujDLtHdWg7XZZBgEEqAc+4gPv/ThP8HK2iDabmxi5n4bpbUzJ+/XzUibRP9M0+LQZE3Xw==", "signatures": [{"sig": "MEYCIQDilAWgkBJL4XfWcM0cu6cvvmJSkqjBwGhXKqN8Q0pTtwIhAMdrK3hz1rL45upIux/0pHP+If07uIMlGi/4TGku5571", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1924ee9755629ff939e7bc348e42266cc869b9b3", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"babel-plugin-jest-hoist": "^19.1.0-alpha.eed82034"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-19.1.0-alpha.eed82034.tgz_1489711286359_0.6606757477857172", "host": "packages-18-east.internal.npmjs.com"}}, "19.2.0-alpha.993e64af": {"name": "babel-preset-jest", "version": "19.2.0-alpha.993e64af", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@19.2.0-alpha.993e64af", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "57e0463a55f87166e09310e9eaf82010c174d67c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-19.2.0-alpha.993e64af.tgz", "integrity": "sha512-uq0osFEvdmkemtiarjBYH5PPO1Wq9KuNjx2PTltodWTQXJ/+kevsYCTY60OisD8k9KuVHalVQAmhCkzzROZ/cg==", "signatures": [{"sig": "MEUCIQDs0Xii06YiKk0VDIu3qOFQa+0GRUB8ha/c2boAR7XKrAIgZ6ZTufkQ2kTywk7rwBPxLypNco5xnRhbym7AIMg2lpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "57e0463a55f87166e09310e9eaf82010c174d67c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"babel-plugin-jest-hoist": "^19.2.0-alpha.993e64af"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-19.2.0-alpha.993e64af.tgz_1493912295384_0.5871717985719442", "host": "packages-12-west.internal.npmjs.com"}}, "19.3.0-alpha.85402254": {"name": "babel-preset-jest", "version": "19.3.0-alpha.85402254", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@19.3.0-alpha.85402254", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e122799e89367f6cf56db1920a0729f534015d1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-19.3.0-alpha.85402254.tgz", "integrity": "sha512-c5a6/Nv9sXZ8CBCgIMNylngPMR7VWy8MZJb+DK/wAbr+74B+rbq/Yz6sCR+iCy6a4udL+nlMyN5jNNG0sEz9Jw==", "signatures": [{"sig": "MEUCIQDqsAzNJUZyBqeUYMt4F2t463KnCIdaYOyQhABzWjAcAwIgQVfxy/u60iSTVs4RV/N4AKJGcJbjdjhztZ0vmK88KIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9e122799e89367f6cf56db1920a0729f534015d1", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"babel-plugin-jest-hoist": "^19.3.0-alpha.85402254"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-19.3.0-alpha.85402254.tgz_1493984905494_0.7062527071684599", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.0": {"name": "babel-preset-jest", "version": "20.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16b992c9351c2525e87a19fd36ba14e47df51bad", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.0.0.tgz", "integrity": "sha512-t5s9Vj8N9Ld51QTVupuQTp7Bp8iG4ncS8MBftRfeNBz2lG3mGOZh16p21Dim8HChL0KvjrUIzEVBrPmZZmW9UQ==", "signatures": [{"sig": "MEUCIQCjhj3Q3KVzqTmI6YJ7mC6jZub3i0QN0DpmkXLXDAIILAIgD1SAAx0uyNBs1B0a7mizPf4cX223ai8JwpExM/5q2wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "16b992c9351c2525e87a19fd36ba14e47df51bad", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"babel-plugin-jest-hoist": "^20.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.0.0.tgz_1494073980139_0.15288994275033474", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.1": {"name": "babel-preset-jest", "version": "20.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a9e23ce8a0f0c49835de53ed73ecf75dd6daa2e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.0.1.tgz", "integrity": "sha512-oJxp9MVkGs/e8tNt7e0eqetQHuU6CubFt65MEs/SFKY1z164CrIJOSajTkxb2YsdOguJAZDKgnlL0rzazZjAYQ==", "signatures": [{"sig": "MEUCIDzk019lFUZysEwJSydsNb3dvpM8lEA3GMWCzOJp2sgCAiEA2xZscMCejry6urL9Aph544VWP/n/E/nVckVhKGcGAmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8a9e23ce8a0f0c49835de53ed73ecf75dd6daa2e", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"babel-plugin-jest-hoist": "^20.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.0.1.tgz_1494499810823_0.7471895206253976", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.2": {"name": "babel-preset-jest", "version": "20.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c3ac7bb3d8bf56cd69caa0b0fef48dbde5bd9de7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.0.2.tgz", "integrity": "sha512-7cDGKTxea3IEe3fbkkMFIRRt6RKdpmPVH7GM6rPcSZLZV+FB+2DE/jCrr3UJybSrY+T3KV11g32EScx6RlLQng==", "signatures": [{"sig": "MEYCIQDxMfm0G/huEN600wXrCBA3HHKRe1ozisdHtLyXeifnEAIhAIE4KOBz/yCDMiPnpLX8blj35cszg6DF1jS+e/GaM63N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c3ac7bb3d8bf56cd69caa0b0fef48dbde5bd9de7", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"babel-plugin-jest-hoist": "^20.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.0.2.tgz_1495018225988_0.7469219865743071", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.3": {"name": "babel-preset-jest", "version": "20.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cbacaadecb5d689ca1e1de1360ebfc66862c178a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.0.3.tgz", "integrity": "sha512-qeYOCuriyIDRKDE/dByN/yCc/przV2LdHbxldeSsjOr1khQi2o2CzrHXUQM7dSWfmqwxTifznw+bNg59kkI18w==", "signatures": [{"sig": "MEUCIQCDA1CEzz2rMm58+N6QPz97pfqgosgN7+ell8vlc/qJSgIgHPHcNcIZmQ1Bj75/fURe96NWqIhvwSvDAaKckZ1glaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cbacaadecb5d689ca1e1de1360ebfc66862c178a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"babel-plugin-jest-hoist": "^20.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.0.3.tgz_1495018635399_0.48499758425168693", "host": "packages-12-west.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "babel-preset-jest", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "985c26b7f08c01a9038581e89d9fe2b00bbf234b", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-alpha.1.tgz", "integrity": "sha512-3tLd8Q7mvWgSezI0oCT3gAsJURotjlAmaWSlzVL/****************************************/VHs0Q==", "signatures": [{"sig": "MEYCIQCZHxM2R/wm94WcapOqWPWjy4WL9U3bG2yyoXzLSZwIGwIhALFNI1fNl1wmXJm+H1VFO3lwFhWcmDx29rU6nseStBiz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"babel-plugin-jest-hoist": "^20.1.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-alpha.1.tgz_1498644983770_0.1372955737169832", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "babel-preset-jest", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4d21065044d50e8a45ee0d585bf5cf0579b3b060", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-alpha.2.tgz", "integrity": "sha512-vAjSV+jokeNpd9dw8vALe6s2RF89yT8ObI8BO0nzRZMNW45gfQx3rzybUIENaWf5YTBQVE9+QGW3VoAhZ2rTAQ==", "signatures": [{"sig": "MEUCIA+o+GgGOR+55e8Da4aFkpazRDkYKgbaK/5d1/i53D32AiEAmiYJdc6j/sviCGWEdP6ttgPU+zJ0f4UFpyrQnJe3caE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"babel-plugin-jest-hoist": "^20.1.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-alpha.2.tgz_1498754210414_0.3681445950642228", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "babel-preset-jest", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf00e857b4eb3a51dec010317229252b5766ea3d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-alpha.3.tgz", "integrity": "sha512-62MvQGLiwjuX1ZmXCbkGg7wM0wonTdcN0+Kid+mEcDJubJb4HAkU+vn2JWkVkP1cIcUV4AvLMSmjupw7d7k7fA==", "signatures": [{"sig": "MEUCIQDccR7WnC+pzYkGErPAnBc1clCqfhm0Q0qA+SUA8FKXqAIgEaRRYrJqgtkggZWl/OYuKxZJoZHzd7d6EFhkScUhbvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"babel-plugin-jest-hoist": "^20.1.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-alpha.3.tgz_1498832456989_0.3085821068380028", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "babel-preset-jest", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "71059e0ee92ae043c853f074604c41eed59d6a56", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-beta.1.tgz", "integrity": "sha512-0Y7fro4atNnPqRN26DBARAZdqR053yMtSEhM6pgT/y5wobbQ8AYgej23FRI2f0jQja53bVozqMqaDNmSrclzzg==", "signatures": [{"sig": "MEQCID6b1r51/w9FHKgyHZYIT3syEllG2LqCvDt6F+Fod+y2AiAfeCbrT0VG8XACDTjKURzg/y6lR6GXCtNUSwlrtgV+1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"babel-plugin-jest-hoist": "^20.1.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-beta.1.tgz_1499942025894_0.983691762201488", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "babel-preset-jest", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d0727385eb8315d0efb1682ddc46feb9448cfade", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-chi.1.tgz", "integrity": "sha512-tVwTeFBf3pz0Pr9y19gTG0DolUIp7RKwGpidi3OSm9nwP023TV7FRpRTbuROCaS+aVZlbfe0rTll+NCt77HQHA==", "signatures": [{"sig": "MEUCIQDBvhy9Jp3Qa0yS/0ZduT5ZPABHYfWdAGyuCxve6AhaXgIgIx2JIXNssSE4p/mnRZxVb3DBnlcjALDFsOMN5LfEtdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"babel-plugin-jest-hoist": "^20.1.0-chi.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-chi.1.tgz_1500027908854_0.5351339536719024", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "babel-preset-jest", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "312d2ec79097f9bc940f008c5d650ca47ba6c7f1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-delta.1.tgz", "integrity": "sha512-xdDe+1Oh6RQLvSbKxvMXe0afy5o6wFKfDoju69WfJ8qitbma1pS5DHb5abLnaPFCZ0MgRqsafWrxGscWz+Xxyw==", "signatures": [{"sig": "MEUCIFIP3PM5ItOl7m/sXWzAIoxp1or/piiusGnVLEcuflWvAiEA8foU4IdH4RXIVh/Po+iaTOM8u5I+pDjHl9V2tug+bQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-delta.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-delta.1.tgz_1500367617984_0.014668263727799058", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "babel-preset-jest", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9506c174dbb785dc995cfaae82515a069be70c9c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-delta.2.tgz", "integrity": "sha512-VB5vNfRCh87V24RHAikCDmJ+iLJKNmN62yFHgzw6BBOA+IzffNekpkECVNNgU7+QddFyFjmO/YgyZ3OU9/84Fg==", "signatures": [{"sig": "MEYCIQCVYomoVZRfVp+ezG983E8BFwoJI+phdJFbvq60Yiq8lwIhAMl+6QGYwI+9LULoWlDHsNr19Z8GPD0OOkSOcDfq7pQ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-delta.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-delta.2.tgz_1500469007634_0.4273257127497345", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "babel-preset-jest", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9032b8d014ef7476ab664580edc45acee29bfe34", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-delta.3.tgz", "integrity": "sha512-38mrKuym0S4FFk8AwXrKDZhU0mPg/sgEGxYUs+b5k4hi2Jzc+/k05Dj0BVb01BaD2TGq2n8+h/tbMRs3y9FRpQ==", "signatures": [{"sig": "MEYCIQCEUET5nlRVclfMWLJ1ZtvIpr+nPkpBktptU/Vo7nuguwIhANLpraKOwQP5Uzm1R+oqKaH04Nk7HZoh9HboVwF8P3C6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-delta.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-delta.3.tgz_1501020747584_0.5391136787366122", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "babel-preset-jest", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9d7f28816cfd4d64cecde72169e371fe5c599d1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-delta.4.tgz", "integrity": "sha512-pCFCOupglo/IWDV+4PU3Gqi+5rCQkHcP+2I4PBJ3zussuovwT1gAiyjE6FJpESKwLNoiFN2JopIY/J08ZT2+3g==", "signatures": [{"sig": "MEUCIQCAGufNfcvBVyv0daLIud5zyIrbZQ7dCqmbOTBhd+750QIgLrQFfqmlr3J5asLttEBQGjFxK0QzKjPPqt6oy4v+Xos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-delta.4"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-delta.4.tgz_1501175950115_0.3096292663831264", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "babel-preset-jest", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a2303dc7546b21466683b9bbc3aec148f8fc01ec", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-delta.5.tgz", "integrity": "sha512-9wCpHa8+TjYVBcDKevVA/Jyds9AXTPkWoJfTiuvw/bC/DqdAkGytDis3lYlqUkyCmgN+2aB4be4UcdIOZQK5sA==", "signatures": [{"sig": "MEUCIQC94aimaoE+EQCk+TQnYMGY+VdN3Cm79+TogtO5EXIl9QIgPOlFCo8J8jqYTZM0q3GCT6EEEydxmEYAZusEXceIM34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-delta.5"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-delta.5.tgz_1501605218329_0.5081551091279835", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "babel-preset-jest", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8864c9f5e5808c56918963076ff7033bf89e9a48", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-20.1.0-echo.1.tgz", "integrity": "sha512-F0mL2Z8rnYOJYHvO1pfaCZpIQUD9qBnqD7Va+nsjSdAsi3o7JjZvwZxNzFkXoOgwXo4r4lRqqjHzUYgmTZ014Q==", "signatures": [{"sig": "MEYCIQCwUeF+Q6VyVeNBvHmCGmsyyCpaP4ZjZThgzH7RbcFMpgIhALr86VyhNPgE0Phupsbnba3kgtGmGP9JluhlcqVbDVrd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"babel-plugin-jest-hoist": "20.1.0-echo.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-20.1.0-echo.1.tgz_1502210998996_0.42252186918631196", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "babel-preset-jest", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7ace62bd29ffa1093fc9107bf53e4d97c5758a2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.0.0-alpha.1.tgz", "integrity": "sha512-J8+/hgCrHAxrfIUU/JH62c3vbdvdJdjvfcQJlm61VxwjTgflscZ/Og9jOLZL1DfALO7moTyPQ8T6C9HNYxqnCQ==", "signatures": [{"sig": "MEUCIBGYu5Iqztk9rthu26cpzEW19XJsAnxMbpW9UvwYjUDIAiEAy8F+A1c8I7XCDSFexBZTcLzBTZJLuQYakZuCT8rPlcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"babel-plugin-jest-hoist": "21.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.0.0-alpha.1.tgz_1502446448712_0.6463428817223758", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "babel-preset-jest", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65a835a32cb7ec02802cc593fd135051f108cd1c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.0.0-alpha.2.tgz", "integrity": "sha512-sHGQEQXvrqUVMClRp63JdvTNWWzh96Lo5R/8iE4B3ADYnsUNq2cgloqMJqp8cYzbGJytWc2ye3AXwF8iBQxvqA==", "signatures": [{"sig": "MEQCIA/PZ3H1Lrsr56FzzHtJ1kUBSypBp27Tl0cPeLKxT2OcAiAa/IEoQCHUNqqXU5tqDxvCKN/1+cs1RGUgsX0Zy6MRhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"babel-plugin-jest-hoist": "21.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.0.0-alpha.2.tgz_1503353210021_0.12356520560570061", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "babel-preset-jest", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "add07784e2c2038856873fb1eec64c4cc554a092", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.0.0-beta.1.tgz", "integrity": "sha512-1QWWAzYVs22Ysx1rijmttkcmMYvo6xj2Bb6zM7VGnxObLEpVKcV3IJgH2ICV46sCm4EOoHEpGafecNw75at54A==", "signatures": [{"sig": "MEQCIDjIXXtIldRUl2l6KIGHJJTWr5gYtpm57xQnICwRumbGAiBCLd0XTkS8PK/umuZeLGY7KKB6cv4AzKBPc+yuiSiESQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.0.0-beta.1.tgz_1503610041210_0.2538796819280833", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "babel-preset-jest", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "13a8d82e999aa49f8b2dc14d0023d362f2e4ba23", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.0.0.tgz", "integrity": "sha512-AFTR98CHu62hdQgNyHyvbapM2Ke0ETqZKgIBd26cLCu2HNs6BIeF4HbqosfICHJAF2lFHkTz6d3xHrolsQ9Lcw==", "signatures": [{"sig": "MEUCIQC7u5SGrpyoqeYdzau5af3xoS2P7DXYHINfmqWJ4mDnHwIgc81f1KA8sEzIU2Fug01PojyJ7/ScrCC/S7o4xSz2M30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.0.0.tgz_1504537318663_0.9419932118616998", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "babel-preset-jest", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-preset-jest@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9db25def2329f49eace3f5ea0de42a0b898d12cc", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.0.2.tgz", "integrity": "sha512-WyzCFIoU+ga307hPnobHL9eRrZlpZYHQf7M3yOtimAwrLAgNFoSfin7ZVw903+zz81ZLuowZMKWCd0w3PNpAhg==", "signatures": [{"sig": "MEQCIAt/L4tAcjx+ThuBbr+K65/EjwkkiNH0MYbHLl+n4//mAiAJUJABycS5BNe3NxetKi+c+6aOBsqp1hWFrEOg5/NSWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "^21.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.0.2.tgz_1504880375748_0.5702539815101773", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "babel-preset-jest", "version": "21.2.0", "license": "MIT", "_id": "babel-preset-jest@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff9d2bce08abd98e8a36d9a8a5189b9173b85638", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.2.0.tgz", "integrity": "sha512-hm9cBnr2h3J7yXoTtAVV0zg+3vg0Q/gT2GYuzlreTU0EPkJRtlNgKJJ3tBKEn0+VjAi3JykV6xCJkuUYttEEfA==", "signatures": [{"sig": "MEYCIQCmjSJPaeTpir6XIiWpoOUwuxxZfFt8OA89FrtMUvr/OAIhAK5umRQQ7n8AV1xJU6tbjTzue9ZNQxrOP9v8OL5mjBh4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "^21.2.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.2.0.tgz_1506457342536_0.6615452107507735", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "babel-preset-jest", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "babel-preset-jest@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf38ed53a4391db49b5f53981e9ddec6ed96c5e0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-4sKbUlCzUhdlzAIlDwwXKD0E8FIYiDsUmE22jqjexK7VukLBQeP0fnTPaBhBAJQu3VsXZ4H7LmeRkAdZkuOdlQ==", "signatures": [{"sig": "MEUCIQCO/MkzoySw2G25/fyi2h/yZzHFMQBue1lh7PkLLISKzAIgVFxcpXF0TwZMDkJaNDKbxAyn7LEG3OHaOcXuArGQRho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-alpha.1e3ee68e", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-alpha.1e3ee68e.tgz_1506608446980_0.7847467476967722", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "babel-preset-jest", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "babel-preset-jest@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66d7eba2099b2971c95bba04d3373d52feb75054", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-ktyyLyD1aRVnDvTox1jUufgT7lQ0NfjZaTfmEoyELHxbCqbQF0/pH4l1DtbP6JeZRlXedkZULp/ofz+3XmPkdw==", "signatures": [{"sig": "MEYCIQC4wBX79+scNDk2tPTjSt5AXeTwmQbCtCW7ielwFJBtUAIhAJp3RoZ+92cVNYqBZsj0K6+ZGRJFOpsZgpT9xwbQPdbY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-alpha.eff7a1cf", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-alpha.eff7a1cf.tgz_1506876414201_0.994248787406832", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.1": {"name": "babel-preset-jest", "version": "21.3.0-beta.1", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b97b79664b5bc12348ec5ad8844f905f65a1557", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.1.tgz", "integrity": "sha512-wfnZSDzX8Sz/IbEGRe8wii/3fut0lM++j9yEiCfg0X2BkWYzC7UavkgK35J1xVg+utHbk6i4xd5pbdjAOBjDGw==", "signatures": [{"sig": "MEQCIE+VT2yu7LJIPsO5BqZ7lBNMMx93v2Rpg7L0b0cNqNHgAiAW+Wq23nFrTRrMUo1izDp+rwR82cbaR6lwGZQphGTCJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.1.tgz_1507114123572_0.5809641457162797", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.2": {"name": "babel-preset-jest", "version": "21.3.0-beta.2", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb33d54da8c813eb334f9a5e78571b317b65d11a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.2.tgz", "integrity": "sha512-avGe6tJ14oEqLHpGOd5UXgNbWj1gllBHUqbgrVipUeDtCuz6zJlkimwZ5DZjHa2sAimrYyjd2mHxWWIR9q/duA==", "signatures": [{"sig": "MEUCIAgOdQrtr57hiITgaCCp84hEQRvsVa14Jcc5gRDG5MvBAiEAp8AQSmW0k+T7IhkqnF0kDPtvE+DFL/7Lg1VA3BXMYYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.2.tgz_1507888451521_0.014784065075218678", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.3": {"name": "babel-preset-jest", "version": "21.3.0-beta.3", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "baa7a290f939327c14548eaaf479ded6a73ee755", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.3.tgz", "integrity": "sha512-anjbxzW/S96seC+iUHrQiKCq6/hvrhQrJFChHkiNfBNSb2lx/VFHxIAL0+0YI9Z4hBQzve+tGxKxjOP9433YOw==", "signatures": [{"sig": "MEYCIQC0H45x3qCnS8HVot0KRKzuJ0LNLBDQaGV0pO1KXbNIaQIhAPbxLO+S5lOMjDaG4T+neFOVsKBPCjTBDqDwar3rITru", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.3", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.3.tgz_1508960049533_0.05375771154649556", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.4": {"name": "babel-preset-jest", "version": "21.3.0-beta.4", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.4", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d4f797e00a23c0a027a42341684702888d90789a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.4.tgz", "integrity": "sha512-rViXe62wNNREDPi7y+LXYt6I+WBiQN4AZt8WmZYUYpVwtMNFnSfabeTNUpWamH+ncD0tccBsNWZy5MQQjFBrwg==", "signatures": [{"sig": "MEQCIHq+DBPwuvSv7Hj9L5Jk+7V9/iEVEJL9nDu8SwGjmVbcAiAbMYGV1rr3+vz3Aub3Nzeq2uxak2IPZfEv9urgHNFakw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.4", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.4.tgz_1509024424457_0.31247352808713913", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.5": {"name": "babel-preset-jest", "version": "21.3.0-beta.5", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2a2d99d825fa1e58173702736de321bc9f75eec", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.5.tgz", "integrity": "sha512-hhok47m8rx3cQTzZf5IhqtP6iK9tsS1utbnNrtaBkwvkLPG/IPrT5FbCCXDFXz/RhYU52zSHg92MKZOhEwGwVw==", "signatures": [{"sig": "MEUCIEVt1J3u1aozTFW48otxP5O2rdNOmI66hnGjrNinqU+qAiEAolB17ZWTct/1kZRfkrLq9IgzX0CbldX323ZNKO1tsb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.5", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.5.tgz_1509628655673_0.5621862104162574", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.6": {"name": "babel-preset-jest", "version": "21.3.0-beta.6", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6fac2c5488ce8b6dec44b3eb1b8e3f32ffd6f3a4", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.6.tgz", "integrity": "sha512-iLjor7nwrfoqNHA13j0Ev8iDaqoIUo5r5HStKbiNt5Oe30sTQLX0vDlFmjaU//Kgz/yHzRi1YqfA1CgVbwJ2kQ==", "signatures": [{"sig": "MEUCIGW0Ai1/Bvzindx5txQiaV7mAhsBdPgH6rhPB3y4zVBHAiEAj/o3yj07exv5gVkkBdWI3ZJtTuQeBOw/IgvU+S9Gi0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.6", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.6.tgz_1509726100163_0.05459837312810123", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.7": {"name": "babel-preset-jest", "version": "21.3.0-beta.7", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.7", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05569ec77403f957a36e2b08b4b561584a0cb31e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.7.tgz", "integrity": "sha512-+CeA1got4JIBLHGeysBctzl0iifLK2oeLsZ0EN8UhKFGhuTBfRNWiNM27AnlfviB1hBXOVwsacTrM3CGOWDUpQ==", "signatures": [{"sig": "MEUCIQD75sfhXigoFaSwFXJu4iaSMdJ2GplM3ZkzT0oAy2fGywIgVx79MmdRr5EHsFFlVA53lF3As4OiwEyybSJ++X8GoVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.7", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.7.tgz_1509961193474_0.43100154027342796", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.8": {"name": "babel-preset-jest", "version": "21.3.0-beta.8", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.8", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f76a538f3babbe9c9f1d4bac0572f05496ebb0c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.8.tgz", "integrity": "sha512-txUoaEVbpo/f3KRTzN7k14sdIcXCnDskehyH3Wx3GLLlTMJIl4bTnH8A+m8YF0vDJAoFKhl+IFUkLm2xEaM39A==", "signatures": [{"sig": "MEUCIDq3e9AEA1b/U01z0+lYNf40GSJHS7lXbbcBBQ3nuAbMAiEAoX8Xqj5BLDjyFPBk6BDdR2PzmI5hvcVeFRGY/ftP1FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.8", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.8.tgz_1510076629736_0.3046064982190728", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "babel-preset-jest", "version": "21.3.0-beta.9", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.9", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fcd75f53ffa188a7294b54cd48cfb20fc4d81e4d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.9.tgz", "integrity": "sha512-gVopRFNkMY1dsiYpn9lsHb5XgELkTxKVIQ1Pr+Uajny1EhEmh3ioU3vk4zUf/LWQq/FNnblaSi2U+9iU70Dl7w==", "signatures": [{"sig": "MEQCIFQ2PRAoAD9Bv8BPExSc/xUqKn3SOJNBiaDLPQE4YeRFAiADu0z8PbUj5w5pj5me+Vk/rMoHnsmLKaQzdeHn0rEBQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.9", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.9.tgz_1511356659494_0.2966085528023541", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "babel-preset-jest", "version": "21.3.0-beta.10", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.10", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "963a77239eac3831a71071e6cbe352cad2b1cc9a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.10.tgz", "integrity": "sha512-uO6pvmbkoPo3By02DlI/D9NXqHQUrw1VGcWaO5Q7KzyaKk1Fl9ya4JvEsJDRQnU96/ObuNoiS7IKCITiMDHexw==", "signatures": [{"sig": "MEUCIGSYl6nQyGZyBxAidEH1k+J81/qbb4yg3o5fsj3MIyXCAiEAlnKpCW4yvYwQ7NVRGR0hIjkQiux3lR8EuK1kY9AFCHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.10", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.10.tgz_1511613569976_0.7332230759784579", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "babel-preset-jest", "version": "21.3.0-beta.11", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.11", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e63a7f5485d5d611db2eb2cd5422fbfb806da7c4", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.11.tgz", "integrity": "sha512-W+1G6sj77ig6Zp9LqYNlJESblN1HVKXvCT2/F5vvKcZE92x7eBy8sgqtkM3uZ6iYq25fRSAmIG5HnbzNw8NNnQ==", "signatures": [{"sig": "MEYCIQCFEz0ssJCBm5QH0zNeWoc+lyATUJcFznjgWbyPHeyCcwIhAJ5Al8DbplLI09Rm9SOCysrmDesNKQq+4E8r7vMfq9+M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.11", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.11.tgz_1511965885933_0.41332830814644694", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "babel-preset-jest", "version": "21.3.0-beta.12", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.12", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8ff0d2cdfa2d3360df1393e48dbc671ac4ed3f29", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.12.tgz", "integrity": "sha512-KL9y24vdKy7bgojodnCzjETW+Y3gSDn2eVPJZBZEatb9dJ+tF4nU826S5esbdy6B3VsTAFP3V6rFqa3euN1BsQ==", "signatures": [{"sig": "MEUCICQucJrYvZXbNDNcd/7VMSDfApbps20A2Tj3aNefi2HVAiEA1jdLSF5IU11DAuaV9m1FEJ3irGulxmHKpKSk/AZPXrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.12", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.12.tgz_1512499719027_0.2791147781535983", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "babel-preset-jest", "version": "21.3.0-beta.13", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.13", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1e9138b5cfe956ab9a5dbc375495e684a08c885", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.13.tgz", "integrity": "sha512-juHBlQ+e30Cegv+e2wDIu40+F4A4S5VFYEBohL91Y1toFNNx2lCkm5drXwlAyvjcx3NZvqL3bWO10RkaDJPDKQ==", "signatures": [{"sig": "MEYCIQDlHxD9vmXpEq5F0m2Wp5ZZCBpMPntyYdvfBKnvQtNczgIhANV8LPhk7p5JXPvfeuSpElmws352i5PHawznXkeYSAPI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.13", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.13.tgz_1512571046787_0.8742694242391735", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "babel-preset-jest", "version": "21.3.0-beta.14", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.14", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "25f38768a5d7d815c26878764762593f9003a2e7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.14.tgz", "integrity": "sha512-txhRdvfVoR0FCjkt1bJkhIm0u6/ok+jfEtwf3rieeO+DrD5nkPYfpuzBT4UE+4pNMw2sDHCfcHkQly9k8idXTA==", "signatures": [{"sig": "MEQCIAOze9FYQ6lIHf0W9hjGt3wSlJAHaJcdq8eN3jcA71lVAiAD0hA4fG7w0zHjiYEh8r4jTl5xuCJiUJ1ig2knPac+pA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.14", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.14.tgz_1513075960433_0.28755884361453354", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "babel-preset-jest", "version": "21.3.0-beta.15", "license": "MIT", "_id": "babel-preset-jest@21.3.0-beta.15", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "509c231749e2c713faf6a5d546361f3cebe37192", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-21.3.0-beta.15.tgz", "integrity": "sha512-kU8RE51Ho5xbm++4Gh/c0WeSqwduHtkBlLIrxfN72ItJm6EZ4knqsrWVT+eDciM/a1AmYfzHRLIiNF76b4BKeg==", "signatures": [{"sig": "MEYCIQCIQz4RUgI6tg5fOxK3uzxxx9fkGvptI8FkR2J1i3OFaQIhANxNlwLAUFgMF+0BPG0Fbl2NzqeO69bkXd/LHdimgLDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"babel-plugin-jest-hoist": "21.3.0-beta.15", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-21.3.0-beta.15.tgz_1513344463905_0.2175476672127843", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "babel-preset-jest", "version": "22.0.0", "license": "MIT", "_id": "babel-preset-jest@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c93d9791030ba2b7ebb4c814b4c243bf29afb36", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.0.0.tgz", "integrity": "sha512-k4RfjTU6iurpbOpjV1wMaCcIezVHp4snbpTH6rz6VMsZEHjDu4r0vp22dE4kF29DMLJ7yCaCdBrBfjClmQfirQ==", "signatures": [{"sig": "MEUCIBs7CfnCH0kwh0uVM5YO0pmISUs+oZ3xyTg5cGMuHKOsAiEAm2V/zV8eG0BrJgwWnpCFc/rSiI1hTvdUOlRItUpcj/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "^22.0.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.0.0.tgz_1513595008498_0.9382855172734708", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "babel-preset-jest", "version": "22.0.1", "license": "MIT", "_id": "babel-preset-jest@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e94c3f8d701502d233da6c63925cb6d938d3771f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.0.1.tgz", "integrity": "sha512-JqTyoxwbCQxuqfbANfVS5yKUd9LrNYapZjsrzm/I8hFIQ92ReDPDcaupXtbJIH3JZlHHpuiRzHLOdoc5O820Qw==", "signatures": [{"sig": "MEYCIQDOx/HRnyao29ScuobC+4/fCbZoAG7bEyj3bxF8jTdEDQIhAPWBnkPadCiFwaubyfjQ612tEhhg6AnOziv0sA1i9FTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"babel-plugin-jest-hoist": "^22.0.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.0.1.tgz_1513628968487_0.7723444115836173", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "babel-preset-jest", "version": "22.0.2", "license": "MIT", "_id": "babel-preset-jest@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d4a80171d0ea4b4ac39ac7272a033d59ffa9a6f1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.0.2.tgz", "integrity": "sha512-aYFV/xqBb3iEEsCbA1U3ien+bPvztlQuiNOglY8ILibl0huObicyADenzWQfjsXqJW72dtiwHpulsHs1gVsc0A==", "signatures": [{"sig": "MEYCIQCzChOw9DRkf1djq/T+9GDbTeSwdmTiW6AZn4GgyJun/gIhAON1fdR2LaI3pyf/RVPqaxEDgUURtDXB/TO08riLKXhG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"babel-plugin-jest-hoist": "^22.0.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.0.2.tgz_1513691587263_0.17306122672744095", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "babel-preset-jest", "version": "22.0.3", "license": "MIT", "_id": "babel-preset-jest@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2bb6f6b4a509d3ea0931f013db78c5a84856693", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.0.3.tgz", "integrity": "sha512-FbMMniSMXFvkKldCf+e4Tuol/v3XMaIpIp8xiT1WFlEW3ZapTKDW9YgVt3hqcpZXsIGFf6eUF3Owxom7yFlI8w==", "signatures": [{"sig": "MEYCIQCT++MxSyyHqg7D2ygj0VSCF/2oNAgH5S36/WYj2iuyCwIhANttMeX9z4d3weYmZgPr3WbdBSv8cr22qI7yMBct0Pv0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"babel-plugin-jest-hoist": "^22.0.3", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.0.3.tgz_1513695539553_0.828593066195026", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "babel-preset-jest", "version": "22.0.6", "license": "MIT", "_id": "babel-preset-jest@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d13202533db9495c98663044d9f51b273d3984c8", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.0.6.tgz", "integrity": "sha512-NZEqqliZAlDO5mOta5SQPwt14S+KxLYbdTRMvSmpaGcvEnStOWEBOPNmdYrVxP7j7MgTQwP+bUstiaxMc1mrXQ==", "signatures": [{"sig": "MEUCICArmsQa/FhI0wRjtAOAgODjovNtD2pEFb0gs1K1mCrFAiEAq5mcuVBQLLHGrzFeDDG3H1FMZBWE84oCnz753KTl3Hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"babel-plugin-jest-hoist": "^22.0.6", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.0.6.tgz_1515664009772_0.6673106383532286", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "babel-preset-jest", "version": "22.1.0", "license": "MIT", "_id": "babel-preset-jest@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff4e704102f9642765e2254226050561d8942ec9", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.1.0.tgz", "integrity": "sha512-ps2UYz7IQpP2IgZ41tJjUuUDTxJioprHXD8fi9DoycKDGNqB3nAX/ggy1S3plaQd43ktBvMS1FkkyGNoBujFpg==", "signatures": [{"sig": "MEYCIQCXIW9Ivt0NMZ1MZLaX4RALStUkoV6TTTqlrOpCbmNiPgIhALHVhRbVP7S+oka/Lhk8BfSIuuXnWwJj9BIvIbAGqulL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"babel-plugin-jest-hoist": "^22.1.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest-22.1.0.tgz_1516017444887_0.07522569759748876", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "babel-preset-jest", "version": "22.2.0", "license": "MIT", "_id": "babel-preset-jest@22.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f77b43f06ef4d8547214b2e206cc76a25c3ba0e2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.2.0.tgz", "fileCount": 3, "integrity": "sha512-p61cPMGYlSgfNScn1yQuVnLguWE4bjhB/br4KQDMbYZG+v6ryE5Ch7TKukjA6mRuIQj1zhyou7Sbpqrh4/N6Pg==", "signatures": [{"sig": "MEQCIGqooYV2h+BUmn5V995xr6v/nZLYI09i9lgd3C7yvVwRAiAjiFgVxJcfJOoe9PLe1ezMrJR+2XLCeP/+j1SjiO1w3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1224}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "^22.2.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_22.2.0_1517999163434_0.7414826994141201", "host": "s3://npm-registry-packages"}}, "22.4.1": {"name": "babel-preset-jest", "version": "22.4.1", "license": "MIT", "_id": "babel-preset-jest@22.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "efa2e5f5334242a9457a068452d7d09735db172a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.4.1.tgz", "fileCount": 3, "integrity": "sha512-gW3+spyB8fkSAI9fX+41BQMwar5LjR+nyKa2QRvK22snxnI29+jJVAMfId+osucFJzJJvhlvzKWnfwX8Omodvg==", "signatures": [{"sig": "MEYCIQDxlaeCLioDkosQeqT3G0T9GRQufTMbLIx+EENhos4axAIhAPftX4t0tMVR4ukeVsJ3/nNkc5+ZR+9dwMG9iOlEvuyN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1224}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "^22.4.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_22.4.1_1519334941548_0.5541308758220189", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.0": {"name": "babel-preset-jest", "version": "23.0.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bb7c274b82ae75303ea368c34c97eb34490417d3", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-766zZk1gxTRMDkGC40VRB8sSmQeU4EIqV9bSS0yQBITZQSickrnp+wmXPIxEbcLBvAINmYeCw7d5FOLNcyJXDQ==", "signatures": [{"sig": "MEQCIG60F2BqPlQjqNXwmFb5+BvD+ZiwJ346U0ydx5nXmcbnAiBpN5uOq9TuAvu6x4VMhG3A0w3ZzWXCiAl9QNY12ZuDlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.0_1521125738989_0.5916168027665543", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.1": {"name": "babel-preset-jest", "version": "23.0.0-alpha.1", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "57ecc8d3ef3186621355eefc72cff53e78920ed5", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-tx28hph74usKTf6x9qLQcgp6GEt/UQX1CVG0aO2vr9F9rF7+0anymIc+ONdL2VZrky2H0yxNjU0V1NLohC7cBQ==", "signatures": [{"sig": "MEUCIF4pMUCqdhhS6ognT9Q+zdLyyaadukSWelv7Zh7AT9P4AiEA1Zm9o6Yn9x+N4CKe3s06ZsFOFkQpfSiLJ4+DdfWGIIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.1_1521648017574_0.6235136421284", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "babel-preset-jest", "version": "22.4.3", "license": "MIT", "_id": "babel-preset-jest@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e92eef9813b7026ab4ca675799f37419b5a44156", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.4.3.tgz", "fileCount": 3, "integrity": "sha512-a+M3LTEXTq3gxv0uBN9Qm6ahUl7a8pj923nFbCUdqFUSsf3YrX8Uc+C3MEwji5Af3LiQjSC7w4ooYewlz8HRTA==", "signatures": [{"sig": "MEUCIQCiYlr/TYaGIkGWr0MOkgTG3ygTrzClStD7b3CveWCZnwIgbQbvmcA40efBm6sWOWCA1ArEbIJCETLDMt02RuYW+0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1224}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "^22.4.3", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_22.4.3_1521648547545_0.5848356261521099", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.2": {"name": "babel-preset-jest", "version": "23.0.0-alpha.2", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "15fc1dcb3e8dbe8bbca26aaac89b87798bc8748f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>g<PERSON>+JxaBAIe9s/hh3rD2vIvSlaSdPba61G3HSTIa//U7GnnFLbJhmiu8dUyoBvPKRHHjW/W6QlRsobZxIRDXA==", "signatures": [{"sig": "MEUCIFyyxnk9JcNJxXhV24o+Nw3Goe179ePD/jVwfwun61m6AiEA9TvZjzVqqjO+UBp+NkYS06iWThe6ElXKhbvuI03Q57I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.2_1522060850496_0.9219067283022726", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.4": {"name": "babel-preset-jest", "version": "23.0.0-alpha.4", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d3080507330e6d23c47dcc366d69f7a330174f70", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-Y2MOyfl3+iaigs+F0En18iPL348Pu9492E+wAb8AQeuSwECovbQd5WF841la6iFMWS4Ip3p4DbKQwVlfgQFVCQ==", "signatures": [{"sig": "MEUCIQDZ0kLKW0fXmRMEcBxD11E7M+tb2Pgmd55D0Pmw7ZK9ngIgA2+6bYmN9f6bYAyjAyf8Ig01UdpcutlzkElt30VN024=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.4", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.4_1522067503686_0.6667101593089948", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5": {"name": "babel-preset-jest", "version": "23.0.0-alpha.5", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2acb14805bfcb73b4884e8fadc776bae26e30a50", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-wkhUfzzkiFkOZXPp+cGTNOoW7omuJi044Df0sqQkUsaJK/wtL62aEYNU3BGXdhiDfitMrUNOQ+r0D6O2NQ31Pg==", "signatures": [{"sig": "MEUCIGpLvcJulWdgLh6Vs4vNf0DMzXPT6G6TGNFKO9A43eb6AiEA09PEITLbytihUwtdFvA8mN3pCXqSws8FkzgFMH4gdb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.5", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.5_1523387902490_0.4652502871222144", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5r": {"name": "babel-preset-jest", "version": "23.0.0-alpha.5r", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.5r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2b01ed977ec78e11fef5f367d7998f8b193aa58f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.5r.tgz", "fileCount": 4, "integrity": "sha512-VR0ZnYEKCftgdKRQPbZlO7tZnl/BfRM77nJ3rRFhhFJM3qTLZCWxHfN0Tj5ZQTa310+EpjmXPG6rwc1E96cj4Q==", "signatures": [{"sig": "MEUCIQCPdj51gjMcvLc9rJZ2vj4PLxOuklz+oyrZI4+AbdBLeAIgDz6Baad7yw2sYS0ZEbtM49Q1FTqKBzukSPQKb9Nj8ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1242}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.5r", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.5r_1523425971638_0.6855103644746294", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.6r": {"name": "babel-preset-jest", "version": "23.0.0-alpha.6r", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.6r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9de930083502690845885973edeb0a0ac9661f40", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.6r.tgz", "fileCount": 4, "integrity": "sha512-QfNMOJ3h5JJFrGpdSycuiWjG0vZGQvkMYrITGy/KKV2SzOM5N8bnJOU+Pl56QrGf+u/njgdnReaXzJyx9GpV/w==", "signatures": [{"sig": "MEQCIAVcwwN/7xZYr4Cz0kEZCLkHi/fTHsUCkXVdm/BdTQvKAiAp0eEvIfNmfkfIVy4zoYzj57silbE1jYBNTTI7DM25yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1242}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.6r", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.6r_1523516497168_0.01721164495654981", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.7": {"name": "babel-preset-jest", "version": "23.0.0-alpha.7", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b6352b87fc749e0f5733e1a1fb55ec20e0c3208a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-nw3WmTyaFPcsUO/H7zjU8yRhLgzOB6Qy43Nh7zG3Gf5Hw4nAYPC/6+3jX591n6zjEgMMske++bwUEF7EBAyF9Q==", "signatures": [{"sig": "MEUCIQCEE1Dbq8PF3nKEKRvi3vRy79aHuS9VDogseqfKupkXdQIgUGLHXGfvYJFqWkd0xB/xZA0+eM6TXXZfxjmFfFJqFio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1kMjCRA9TVsSAnZWagAANIMP/jNHcSDT0H88BQuKyIXX\nSRGfVyyUrl8lGIo8yhagjfuEGbF8hzlb1LkE3iL4Rets6lXIXglu+JAPDAHt\n01tw84Hhvo80hlydpyvV1jYVGPKuA+cC2CoVvKIMz+C6w6lD4zv5jX+7u2PC\nJfyX7XsMpuHfcyxcYSLo2bIoBY2ylzyxZaD7vW4TaNlW4NxuWjKIW+mRLz9E\n1X52uoLSH1L7NOg8Mr0z0CZCUz2I+tR4lMtgWMIt7QB8kYx6HyqiBbRs0rBR\n403yjWF/PqpacXVe6GEXU/opxHH7RqOt13sDtGFkh5xc9fIWBeef+tuZHCvq\nPzPMySEfVwRt72zDSAhQ1cSYiIfl55hz7xoxyqtwEF17TvosDia6DH0eMZcI\n/YY5/MShTNAWkuIroBmynise1hIm/M3KBSOBeZIsNa5HrObhrrdWpZ1kHC1+\nMTKpreTlBtEUEhF0rC+TxEKJ5KTN9r+cALu5ZFICOo3iQRBaqPUdl6gn0vIb\ngmUemCkbxWd8tDSdSE3zThML064PFGmblY1YlMKYsBdJIbZ/+WF+BUa2a2PS\nnQby9MoLLNzr1lESu8ebHxCchZLKVpM0KwV5J7GalS5vK/yOEIXeLKcV6HYG\nQRhGger+SdisrYmLAk9v9L2B1XKbBwKk151QDitQdAiVdqr0hdH5quBPKkhF\nDAeZ\r\n=xrln\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.7", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.7_1523991329231_0.5501638381774185", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.0": {"name": "babel-preset-jest", "version": "23.0.0-beta.0", "license": "MIT", "_id": "babel-preset-jest@23.0.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cfa02f7ccabe8de39901448c66766f14e1d768b0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-beta.0.tgz", "fileCount": 4, "integrity": "sha512-CDgSScmfG65n8cE9qE4NTbsMRvKKf9o64nF86bum8wFOpINjB8Ti0Xo2/8xKzVCC/4lCxs0CG8tSOuIyoZs0Mg==", "signatures": [{"sig": "MEUCICXQsRY7wV55Y1fn3YXL3tTxOvq34jTOd8M7MjPHeiHaAiEAzEbsNEzTj4DRzgoK6A0nZQXvdO/FU6S2tFiEjXb/RK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2byxCRA9TVsSAnZWagAADxgP/1Eq1Iq7ipQKbfbZ6YrT\nODGDhkNCFOBmYQoOGADD0t2Z79YM1okqI/0SLD+EjVtGpubYi5hMOC54NQHZ\newZ5SBkAO7f9NlRXyTkYnDdFleKccpEakQSUJssNWUoERclLmuZwdlfqNzOx\nLe3GUtDkClq6IQYZnKP7I+qCJmdD2eUIwWxujXU+KwtqvdgJpQtd8JTUHXCJ\nwn4p8KKlsDvEVTFhBJPUxOKguqHxj/jKNd/RQbrF3C9ohF33wSN663LpNmJR\nR4aI/pzO1zNGCuYl7m2ueS46r/ULKUjEsGuq8U8BaERR58QCLDWl3QMj2WpT\nMjpkR8cys/Utqogj9GinaEROaRZHVwqpOu7rPtwbv6FlOECxPF01UjkP80QT\n+h7w5tLXVdo5naVz3a7S+/5losCoWtXT9Yo+xtWSD1RCF1kFJewg0DG1ql8O\ndWpz+IS21Pcdmzykqh8dSLXD2fNF2+TfMzEeWpbUtcgu72gC+1y/edkG5bnf\n5E5dSLMrbnYJSMnRLIbOnpU5sNc6Lx/cXBrGcz64vfhdntq5WQbfUj5EnJs9\nqAhV5Ny2OIQ44lSdGJXRNB9+hDnK69EEgbC7F/yxllPdtaIyfZSDbXanACDN\nLzCkZNeEhofE+RTsMsW7oEdZKVhBVIfSVT1KemYqG6yhpjBzaLeYLBZOKJmq\nkOwD\r\n=Ij7H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-beta.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-beta.0_1524219056638_0.5958574616375669", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.1": {"name": "babel-preset-jest", "version": "23.0.0-beta.1", "license": "MIT", "_id": "babel-preset-jest@23.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "446310974fa9a774b11a403380fe1d44d8021a20", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-beta.1.tgz", "fileCount": 4, "integrity": "sha512-Nbn/A7gKHBU/Bz0r4iZc63Goq1NJ45e6l+GplDhoOc9hM19Nc1soKw5ncxWPcIoTarv+4x/G4OfyxJmgzwC3Jw==", "signatures": [{"sig": "MEQCIAYzMexhq6WwrKN9zwKrzx5lT6PYzFTY3Eqk02yXwvEjAiB+dND5QEOA+QPNLu92OhHkrILCyrjKkI35xN/fY3Q4nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa21xgCRA9TVsSAnZWagAAUaQP/2yvbmq1R7Qkegm6BYyz\n+qC2IQbG2XqSvm1pS8Ev9E2/1cN3Diktp6qFwCQ2AMEHxWD/KXfWxCwn0oHF\nkpj19i4qMpVzMLsT/SWtZyeBVR5d2x/iCakaMhO7DLoLQHNwLoA1ACrtcNtR\nPvZKwmPuhqMCQYGWbp/aB2wq2EtTPr+1/OD7xabTa7boX8IFmakXtRo6mEN1\n8dgZbAOamCQfBVFScGLkndd/T65Yfg9XlHEBpeGlsgLvFk90gfyutF78kOTQ\na68drTgh5i43oUKiXLM3omlCs2cyhMvmjz8aMvrQaIepn1lu0ikEYKtwbeA7\naCOp6nifqYtxWh+/QrpL3u+TXv/38eglNmo9jnmlZRhtEP4gs0vdrKlu/gSh\nVMP07vpGeoiKu6MQ5zLhBD2hTlzMyZ6+Ay4cc89fnsWws8XGlNLj+C3rujRe\nuisio58E4Q4F3QtvYK2NxllkW/f8Bsp1Kmj5dViIooE/htDsz5Y7THNavNSd\n2Kz1YhHSnl+BvWWUMjQu3YfNkTortfwGmTD4jjbZoxIbJQWIKkdLtecvW1Xe\nVpewz88nXlkSSZhSSxYlnH29MHu6RY1qINEDtn/A7lmb4Wva+V6taq/OZWZF\n/+cRYUACRTidQ4DGSZSGR+auatPxgMdd+NEw0uKo0TfFhVaDh+iB/mXRX8Cb\nX8Wq\r\n=4D/Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-beta.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-beta.1_1524325471895_0.6895095455471318", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.2": {"name": "babel-preset-jest", "version": "23.0.0-beta.2", "license": "MIT", "_id": "babel-preset-jest@23.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d76091615e21ecb733b6400cb63c71cd12ed47ac", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-1yoxKbW4Hfku9tCbAoehTsaCV5bbOpgo4Lh+4kAH0S2W4YrUjDl9mqdHMHZhkYfCKhhm5VnhBfJHEwH5hn2FnQ==", "signatures": [{"sig": "MEQCIDJmUOr1fc4/dQ5xSY4YPYSG99qisq1+53HY2rckoxLlAiBea9H8FYLtVsuJXvZJl7t3z7i7fUedGVJh+KDli4uvdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4kH5CRA9TVsSAnZWagAA+NIP+gJjiagYktDJtxPUqfEG\nvOkopkzsT+ZlIDFhr1S9pV0jlobpsWVLoe1S9rG2ntcxrbEoy4cwbnHTEU+C\nNC7YXJ0EMcOmObE+tTOr/+OUnb5MBZLMrRDOZ8mU9jAVE5YjmS5LtP2rMSib\nk2oHJSvErSdwi62kKGnGrA8Nbl4IBCG+U+fP96BBw37JWtULiek15AuZ+NBE\nHgEkJeMM6ZfJGgnYfpS62QIRwEclq6+dBeFacCZmk5Z0fBZwonD8qjRFdgkb\nY4/O1uGjLvnT42iyMHAXH3+xE4jRYKdjvRm6q/OdyXem9/J5KMZFkwE4LvJF\n7v68AVBkhrxda5HA752BAL0V3T8eDYgFf/iXQr4NmqSyDqN5cgnJoCaPwpQz\nrR37Sy/GmTUiH504HHBvycG2/uyBsKZiendKu0/J7zOsqbYrJV4MI/n7mao4\n8GPd8Y9LdkSil68WEJ6GvSpuJmF4bsTwkTNPopp5OQPQIJ0bmKgDHkOAKCWk\nOgAPmMTl8w5khkvNySVvbX6Q9wjdk55wakmBvBkdOcd7AFjEtLO5KjDSY8ya\nbKfJHdbx4+eUqaNWw+iS+GTASo4yV25BWDDPyyhtJas1Bn29+Jj3hu2j5w7k\nm1DTY5naj3befcYHTfyMBFSsz/SWk8T9PHc5TgZkNPvE7ecE5LodrtGkg36t\nOGmj\r\n=OAGg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-beta.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-beta.2_1524777464743_0.8224419964863494", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.3r": {"name": "babel-preset-jest", "version": "23.0.0-alpha.3r", "license": "MIT", "_id": "babel-preset-jest@23.0.0-alpha.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8c49018d92476210a42e40018bae0ffa3b5c5457", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-alpha.3r.tgz", "fileCount": 4, "integrity": "sha512-mnTQPn7qhsp1L81/Q3iq0E8Yye+rxrPj0hlew2Wvne3S+pg84oHKB8zRismeJZIDsM7WPTVs6vZk2IwSamXHFw==", "signatures": [{"sig": "MEQCIAIBjQ3lIswSykFgNq7AHBxOT5gzZM8IHhp10UMJPbOHAiBienys18XNB3G1aM2Vb29Q8LbNaopYs6vWbJwebpEGkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xW/CRA9TVsSAnZWagAAkbUP/A3c9u8f9JYyWoFzR94Z\n3Sf/SFD9invpysN4Gz7vHpYwB7YAWXi8EkzOWydLU1EjwJYxLrXuFhAAh0Mp\nEsdEcuDvXdQ+38WKJ92rphZzx4vi2DYw0QVTfHu6kLfHm+tNoD1mkEsgvc0k\nxMLEm2t7KJ7cRJNarAnBUyoJ5/8AzQAgMakktyrfS/MdpbOg9z8l8B1el7KF\neqT2X57ajmEF4DZ+S3OuwN4VofuwEhtffljpKW1HGe8lUDO8Bb4dpY4QEJQE\nGXFIJxRqyINHIkWtoN6S1BBNUm0XAQRBDUbN6rstAD7LLhDhT3yH6D+kRsAp\nLydpH0nuZjpROzVikx/kkaFY+SS2mVki9f/svlcSww7f0RA+3vfQF31BkBFL\nb1YUjdPHEM/32fIGw5tRzKYHVz8ErzgqMB3OMFuM5AHJIfjQhjOgbF22cIrz\n1VB7L6FFjDvV16OJIzCcI4q20pBEeuz0vDdXca3XjMEmCvolDh6n28+/Vz1F\nFe8gFBflQC2MVVH+HEEsQlADyRfHLeoOaHUxvR4wOmiRBpgq2Ox0S8aK1Vci\nAzIQJC412AwBM090w1HnAcP6tbavtcHbMc81j11qr42gdXh8MhTtgAceMLaW\nj4EZjx25D0u4tEQ4W7D3VgMJGgxzeav44gGB2gZO9RB5fQ7lV7t/z2PwM67K\nKF6b\r\n=TwJj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-alpha.3r", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-alpha.3r_1525093822563_0.2875017653233214", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.3r": {"name": "babel-preset-jest", "version": "23.0.0-beta.3r", "license": "MIT", "_id": "babel-preset-jest@23.0.0-beta.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "25de5cf00bc3da2098af2f48fd941f7e0a167d45", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-beta.3r.tgz", "fileCount": 4, "integrity": "sha512-j8hbvfSfj1ibKsvwRsB5p33lvLvdyyaE76JSL9EPcqZZrZkk0PfFsBfnQgRwIGdnOsUCgnjvsc0vsLQa2spi9g==", "signatures": [{"sig": "MEYCIQDRouDogU+J7zs5u/Ep9fG14z1XyWTwYMj5HA4MlQEvqAIhAId87d2Ak3Fmncjl7xHmsAzivN7dBKkwYKjpGWJJsX09", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xbXCRA9TVsSAnZWagAAc6kP/33ULJOQy4I7d2Au7Pyz\n7OKJA1GKlB2Jbm1ADqWQg1nov00ku/VDAgdpzaLTV0KIuo9VGXGFKf6Avpj6\nE9bkiCK3bG0b7ziwR3njWQ/X0+CPEtr26yGAtDoS0GCiBta7Rjemutzu/GSA\nOm1PzNFl/9Y0+Ir5tjknwjFUE4k3ziw4UqiR7zBTttQNv3e2KhJRBlwR1/zT\n+25LYYp0sscbSBAps+DK3NctT9xqBmPVJfFO2QewWH5OrF2+DC749gmskgkW\nhlKjQzDFctNbzvS82Kz7xAkfRzz9qHG0gpqQWQA4oHZ4vwoUfM5rTr/MVGVn\n/N3GHEHB5Q/Cf0eSVLL5rDxShcd/BTBH9DHo8qiJS6DrsWxAUPf86i3YmNYF\nwcghLDskkYBqsYRb3wT/mctOH2ZpSlfejBicNISreb/SsR+oaYjnsJjmSiqD\n8ZLQYEFFn0XpeOeHd8jnjsyeeMzX/WOeaFiScZy2DLsXuZvFrPN2W/l4I3XD\nUIjHsDlIorTW2dIzkD8ttj+eYfpMKXiEUL6k0WJygzvamfPj57KZ9XtKc22H\n8di1LdSdDQYsI1jjp97vyn3JP+IAWP34RHoE+D6kl4VVG3/I/pwPaCJ/5XtO\nGaSKQaH/sfp6rh8aU21YKUmyb8suhGRxHkN+uaNHf6JYRWVz+P1809rzY345\njLox\r\n=Ft2k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-beta.3r", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-beta.3r_1525094102194_0.6559156902135947", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.0": {"name": "babel-preset-jest", "version": "23.0.0-charlie.0", "license": "MIT", "_id": "babel-preset-jest@23.0.0-charlie.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "76791f04d45fadcd001106436a2de691679e0776", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-charlie.0.tgz", "fileCount": 4, "integrity": "sha512-f5Mh3qVm3z26cKv6MrixJ2M85tN52C6x3AIgeoTSsXOFZ/atJQDGNxtoXbKpc/8Jqm5dD2nupQOAvzo0BUGWAw==", "signatures": [{"sig": "MEUCIBPodsyqzOCiq89Jph/6vLrgB7gShhks/08opgvp0eJ1AiEA1dNUsswnbwp60SlTGIlRnRbpDb39ertQnnKGl2IpAwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6ZljCRA9TVsSAnZWagAAeJYP/1lo7dCj59w79TCzeME0\nwKKdCxG5HfSC4y0lKADhK7KpccGKiTunn76hs+PmTf1efTvFSgUws78UdGRY\ndpk4Ds2aapijt35X3CjSrsJ1lWcJQ2leul8US6wFIOe9Y7QhEL7jUYKCB6Jn\nzqzcAgCNdwLUt5dl29kIj52U5iB1+QgB1/+ZGI9f4TVI1Z+hJzm3HRgXF95f\nBW8yY5sJkeMVOScu0mgC4k8DZiE7BwEhU+aMBa+WjP4Yl6TDwNQ7wavYYva3\nJGuv9pZYdwiag000VkXlC91qvSn/fc3YXG4a4a5DFjrrWBhEec3uSg2nZF43\ne6eMFff/Cn2Gu0TSFnLtYrvsbsIQx/i7Hj2p+U2wmSySMsDY7eVCw+STXlwg\nPwxj+KTdH4I2tVotP+HT+7wRnMKuzRfZLbx/Ij13fvY5zhNybSFzPQ8qYpxd\n8AUltIwqlTTbkGsyMUzn0Z8EfMd4scNPUkYVuXgeNAMYYYfdHo2LA1SQHzdA\n7y2YWT3rw3PMMZpyXG0pZwBQiHq38h591ZK9Wod/m+1JmMY4DGYR6hyxP/0f\nVa6dFTEm8Y1fGYn4k3EwJY2G/GlUNd/oGAKUzUtTCwVNcXS+LYwdh8mMPTfG\ncTgYVgdMey2lh1t2eAS//RT9xn5sk+BraDTtGN56s3gOn4kgLYH16uBhJm+S\n2ZZ9\r\n=jv5J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-charlie.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-charlie.0_1525258595234_0.7818449816278232", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.1": {"name": "babel-preset-jest", "version": "23.0.0-charlie.1", "license": "MIT", "_id": "babel-preset-jest@23.0.0-charlie.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "188ac3695737c1f8e76b1737e3064d2d8c783141", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-charlie.1.tgz", "fileCount": 4, "integrity": "sha512-Ok9RVPY8ond8J+LkssiN/8mrNabANqkE2pU2vS4qNj9jfQ5ONIf8O/R3OUWq70Jlestrl3pM25EaOcEAtqLmiA==", "signatures": [{"sig": "MEUCICcqwzR9bW7S+oMY8bBzD17mNqFqMgZ11kMVjv2BO5ZJAiEAtgI1fstQm5bFoqYmxNh6s/+0ykF7hG7GBn4Kfv+X5Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6vwwCRA9TVsSAnZWagAAvIsP/RYI7n27X6dA68UnQvVR\nZWJRCliMqGKYCe6eIb6EANTvgnALZS/Zrpwt6OTvzzsqO+9jkTe4lMXjKoIg\nC7YdFYngi30rdFT1zLKi9JwfsoMWlWnHkrdUgSOpOSjik3jRAKt8PNEOQbZl\nFqSMHkL4pJNAlul5gDNL6UXi7HKA0M7l4YvYnFvrDcWuZKAR+a+8MZt6Jpnx\nkX6/GEEszcz5WGijqQocn+wmzpr8CZDGYbRahcVrbbeGuOegG0LoOJkuIKld\nt6Eji/YDU5lTtYaa3Tos9/fjsOVyADuevWv1jsCwoGhy1+6/Ys4MCQ4URmt9\nDxVU6BQ1RPlTKUVpWbapDGTqv0n3YDvN59ytlqokOldfwxeVJcPmP0MbkTcJ\nVSoYEV9t406On+9xU9n/w/Kn8HisYpyApqGIDvx7eMQPuWUWlIaw3V4eRHWa\nbsnGHTYybSzpVAGweYIC7xDem0cCRmLGNedhFC4LlGfmx/TJBXZ0xl0/sqOz\ng6DA4r1aYIxYAlOS9CQA/8/CHmWHvOnOGhy2H6uyg8feFYZ18J7IbVqj126u\ndHvqd1WXU4JtXEegNmgIGwz6wPOb3cLefPFU8Mu4L7nsQLSADHbr6/5KwhfH\nbTXW1VZMTe/K7yc4j9Ad4ba5WAkdGhz4zUnZg6ilrzI0TDvi+mjoqDWBTlin\nVR3e\r\n=Cvj2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-charlie.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-charlie.1_1525349423926_0.010914567212714221", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.2": {"name": "babel-preset-jest", "version": "23.0.0-charlie.2", "license": "MIT", "_id": "babel-preset-jest@23.0.0-charlie.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9fdaac2c568ef3ab65d787945d7d001ff1597099", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-charlie.2.tgz", "fileCount": 4, "integrity": "sha512-aF1+wK6pUhBTMuu5UGpBO83NGO5356N3Sv0bagdjfQhIi3BWs+zkK9ExwRXitABnFWbIC+KsKLIkiaOzf7LesQ==", "signatures": [{"sig": "MEUCICnXAZlCW3D2XwTgYSXsvxLZitMg8TT/tmwocJl6CzCQAiEAvQKnXa+kbSOB3dSAMf4cIWmIQiISpfzwriXRVssZpP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+q2qCRA9TVsSAnZWagAA1/wP/3938wZl48a/BS96OedR\neYbpL130arlpwKwMKpkgANVzlEPBEPFhXXHKRvmjP0y/IfknAHSEqAwTjAZS\nHueWSNxuZu+5PFXAW6Y2+1Z4aw0G3CM8tmia0KfbFdJemplhe4Y+btdFyDLU\nECl8MmnKYt4uQ4/8L9ZDmzugw8waMTjMhm3bKhYMSAI4Cl/Hm9sYjLCLitAU\nofRoZfuFJNbPAeXVSmPzIwwqVI7NC/rggbJ6ahxuPmvKln1m1ldck0eswiCU\nBNjKNJaE3x9b84L97VRSbtdlRfQvahWvQbhUlm/fzuy+XOmwsnqIMkzvhZBY\nwuOMt2OHIkgTyu+DR8qEkpIHrFz9NN4ZrcId0IIGiM45VtVnlN+CVGRTkm0I\nt8E7UnYhYcvSNNqvy5pWle4GSjRwSk1l1E1X19Dt/u/bV42Tlkq+n7maWL1d\ncHF1N3pB+NOF36oak/lMQ5TQvg3FDaLkebu+LSmKHZUIYC7mnLYVuVJRbGVB\nqlnXp6qSePMaC6S/nvjUL18q0U06K8lb3dbQc2wg65ftzbitlVpNwFvg5Tfg\n5XjewdefoA2whr5NqyHqPfhlmjcvVEW//+fnQbITeMCUSzRK53UEJIeNBn4w\nNDJZ1VfJhzM9s/pPBdhlQs7ApwDQ2AOwallDQKB7iwYXDurCkmJyhu7KPvkj\nesF9\r\n=WMnt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-charlie.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-charlie.2_1526377898055_0.6430986101587344", "host": "s3://npm-registry-packages"}}, "22.4.4": {"name": "babel-preset-jest", "version": "22.4.4", "license": "MIT", "_id": "babel-preset-jest@22.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec9fbd8bcd7dfd24b8b5320e0e688013235b7c39", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-22.4.4.tgz", "fileCount": 3, "integrity": "sha512-+dxMtOFwnSYWfum0NaEc0O03oSdwBsjx4tMSChRDPGwu/4wSY6Q6ANW3wkjKpJzzguaovRs/DODcT4hbSN8yiA==", "signatures": [{"sig": "MEQCIHroxE5b98fxWxucxANhGt8S991WM/8pgWFbAOz5hYCoAiAL0VrfIp8ZrQqyLVgrleXnlx6XGCvzmctZTflGP68Qaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/s4NCRA9TVsSAnZWagAA51AP/1T85fQjOBDdTtVnvQEt\nr1nu1/tjw6/z54lg6B6+OWFKrSKN0QDoN+xbwv9IQWPhzoA1XE28yJHgPuji\njk78dmUGnWAa4lOzOdvXsOiBgjduiBwh9TXuLewFGh8bSzvKQA3GhKLT8uvs\niq9AATHlT3nPp7nkOWfRqdrPsLyzxg1YP6S2oEUr6X4FKY+z7k6HU2GbQa/g\n7fHVbcLEW0Sjc2feV2IBE7JzJYWy2QW0xS0mnXuEMca02JA0ki/UHG5w+3DB\nSOGG+X0xxVsAHXB+dFT7RDqYiJQWEyaq8co+gKwdEjOItlxj7k3wcl1rNPtV\nGUn4c8CgoVI/s9QRfR6/CcEbJuMvGynY1EVxfGXETdyLjez/BJbS8H2M839z\nuFbVRDk339mSHWsV5bLw1rGMOiIMmm+iatbzmKG5y/MZ+ll/V2DqMzxGCgrB\nuc5736yKXblXgL8E9WZlekw8exHLCtnrJggc3xTuVSDf0AI/Ap0ClnOW2npS\n3HgKfN8ZiAws1zQnlwkQ6M/Fue+L0U2hV6Jg9BSAQG9h6mZIkPp4yBW5YuuM\nT1k87Vvw4RqUwY6xxZJ+NYo6gdwHGD1ZmoKuFmSSU+eSK/Jbg4w4ORQR70Lj\nZPI5ocTWWa2/Q2UaIk36PjabIWw9QKXlcl5jB2jKTocvUOKswcB9CL450EHZ\nCrP0\r\n=Dl7M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"babel-plugin-jest-hoist": "^22.4.4", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_22.4.4_1526648332150_0.2867770433580734", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.3": {"name": "babel-preset-jest", "version": "23.0.0-charlie.3", "license": "MIT", "_id": "babel-preset-jest@23.0.0-charlie.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "70783e26cee9a09d96090c49f0c991f7c3a51fac", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-charlie.3.tgz", "fileCount": 4, "integrity": "sha512-yaCw+6iBFbQ8kW+plB+MEhAhLs9DPcNZ7i01qt1abbMKLIZ67nkEG1UnpWKFI+xWmEcTQTai2iyVgHji5TwhHQ==", "signatures": [{"sig": "MEYCIQCRbfj0gc4sdr1iP1ObQnGGLBVmmU1qf0+IlyeA8GeSCQIhAMQtFLQEMuCrR/OUG3zkBtHofUZ157SAiR7ipaxGlATl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBDA+CRA9TVsSAnZWagAAnLUQAIdntOh2nUZccIuAqMZS\n2N5liQVQIpq3CrzVsv6DjeAh+XVblqP6wQ5rNDzrasZCphC3xKD9T+9QSruA\nNujCEDCDqFcN1KLkhO1Xosh8bUP1mAbXDP7j5w2FaeCCGG21lIev/J+59xyW\nBmwnJyNn81dLDpdr7vIsxyBwlV5HJCb+6xPW9o6QWqmxtsTK77t7K4Zsx3uO\nJikc+5rOBTAIvvOrJhAIgNMePvHw0cQRhWHMsgHycATlYuoaftOgddSP8kQE\nVpzmtX1Rvd8aQkDhe4gxaBZLiB3FgqKfFJ6Sy9h3tMYTzf2MklLEIu0LVdKu\nmerDUgWnvrSff5aZiG9ZBPM/66kMenzPdY3Dr+ARgLoibv+9U95LW1Qrphqu\nsTHPYmxpsXG4uDNnEl3r7OFDx14W4kkXlL7xO9RbWOymZsIvISY2/ap0X2Hj\nno08hoayB5W2iXrZbUrxXw+w2A9WpLE5uFp5m6/WOipXhtbtitkhzAh1OjRj\nXJc8s/D4+cDIJAtPegv85nTHkvYgk88DGzfIbXS5Qa1pmdUAy2h4EYcwk6eY\niOvbbcnhsh9tF7ZX+VpSVQLbLg5XVQfR9gSb6jIHhqJVwA49EG2eNrlzYIv5\nzlpV/RwWJDoQEUjdZhItcTuMB1iRWu/Zu/lbfvPP0SeclKryRX0plQDmVKPH\ntwCw\r\n=5kuW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-charlie.3", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-charlie.3_1527001150346_0.8993526605908058", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.4": {"name": "babel-preset-jest", "version": "23.0.0-charlie.4", "license": "MIT", "_id": "babel-preset-jest@23.0.0-charlie.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f7c16fc26590a68bf735af8de1674352e0d0fccc", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0-charlie.4.tgz", "fileCount": 4, "integrity": "sha512-13FlKNb8kU+ssUaUAO+fwhIv/LAiQV++HwMlH5EooqYKQ8s+zAJtcVq0PblDgXcyoi4JO4OllHQMzvYxqj5OgQ==", "signatures": [{"sig": "MEUCIFTORwwCsCdeNz3AT1YynxwHuej/GnnpG9ctHAvJslTmAiEAg31MuphE/b0fMGV37rxChaE9yTwRNnjAOfSwJhvOoPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBUWbCRA9TVsSAnZWagAAGsIP/3I42WVIQmuVVy7X9Ere\nxlz88nSsD/YyrzKcda07OAhZVlt47EwKbFRTSfeMutHfPKrleBqq2wsgWTTs\n9QAUTXvmamxOEpKvs9EPffuOzZfs+G9/P9xPhClZhF5VVzdVhOlAzDZrZgwn\n+dHomrLjT9qqwPntxUMJPwNeuVRTi4JycbvgMwz/1pvlYlqxcMUjSM57Pstz\n2da+kzdlANg72uuCS0h/t1Zc0Sr6OZ0/GEsEhn/OJQDx4OZmMkYNrlpw6LRe\ng7047ZZ/R+mRCOO+aKG5Uc+cks14rdQwN3F8WZ/qjYUcuEGxD3HraoOgb5Io\nu459y8R1bwhklwugMiRzekG1zdIKdKOf1b8CMsc1dksVgUqfBSJ6zurGDGFR\nrK4T1zmuWU15+spqxA/zJ86OXN+pZJYmmk929vG+TZfO3LZJgmVK9R/teLgZ\nGhwzyGy58g6CXh9G4kasI0Me4ZkBG38eC++f3oEfRUu2JvFXEEVSsdbKcSbN\nWnbOv2nSMYvboTOM6fKLCTtNHFKjZOp3F9XZdOah/Rhr/RZkNsC2GVFhCuR1\nbqfY9W6U4LvXBSxSqKHUI7T710e7PUhwgrBG+tnBrjsCD6m9LtLg4QcVmfjI\nYtuOg3EZ72ZAy8iRmuBDx9QA00PIcTgnPQSLGXJOu7uGkEEFtRFTutzcOl67\nJE54\r\n=MAmY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0-charlie.4", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0-charlie.4_1527072154448_0.34525553485747285", "host": "s3://npm-registry-packages"}}, "23.0.0": {"name": "babel-preset-jest", "version": "23.0.0", "license": "MIT", "_id": "babel-preset-jest@23.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "49de0303f1b6875dcad6163eaa7eb8330d54824d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.0.tgz", "fileCount": 4, "integrity": "sha512-tdlal8tintHXS8oamIKAxociWBAxOY0FkfJwRMAC/qxjBHhJ34GYFboW9aCSApHLyU7enAE1Ht8nEk6tYH0ddQ==", "signatures": [{"sig": "MEUCIQCwof/deMvWKpu5bRQ3yo3ayjKaobpdwNLZxrqjHBH3FgIgAyq9nG5y+PXGhzIvPeN0bhbCNuV8k2vtcizJTFmuu18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBvXLCRA9TVsSAnZWagAArwcP/iPvRm29Nf3J9OHU0UOK\niQLFTvQW6uFHsHlESDd4BqVXPP/CopJXuYDsDvINleW2xrk0MQCZ43OcEUu9\nHYikPBnDks1ugE0jo8o0VSJTjVhd4i8JuWQHc/SeW2XPh4jPBBbRAgKe/spH\neAG0bdUGmz4my/moMWUnvpYlg7CHVhgcY9AUQTbCrI+dUjbnPgluu33vbjIl\nXw8V1l+mZRqKACTW/c0oJCZO/nhHMzE6gOxJw21H/+00PRs5axVtnEwE87v2\nUlnsYd0058U3MIOOEk34mlbaA6LawOuSiRdQlOtkFEqLlbyrnnG//Z8jE7r2\nkxn0+FL9deG1gt3PmuSQPXAKWEs8A//Uhn9qqcxUMz2rngtQ7E9FM7Q8FYvN\n9+KSb4emIvMV1KUbt8qhZ1pLNiYFOBPD5B4cERoPTXQWq66I19w397wZjPBj\nieGQKw3W9agC82c2yIV0KTlTWGtvV5F4Enan5QLwuMIUzivT2qDDH8rO72ZD\nGvWusswmxUo9EJEs+U3S4SzmLE/NkeSHoZM4YUP12SPkOqdteWskrw8gqbwE\nLGI5AuJNq9LBJohZqce/9uqz/SRwu5Au3xjTNImdnDpupZmw5vXVs5o/ayCu\nEh1gqGN0RYyjrQzOFu0sVt5aV65C9Yd3b0uJER2YiCpwKVbuyKZy3uJbXgHq\ncHgH\r\n=gJoV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when > using > [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.0_1527182795155_0.4147908417500168", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "babel-preset-jest", "version": "23.0.1", "license": "MIT", "_id": "babel-preset-jest@23.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "631cc545c6cf021943013bcaf22f45d87fe62198", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.0.1.tgz", "fileCount": 4, "integrity": "sha512-bH0J1juTGa9dS6kQwjWoAGfXg5+Ic3Rb9rHQFvoUbp/VdkGSA2nJnjgmFmS8jO0XH+Q5LQOTeQUjxx3AF6dcqw==", "signatures": [{"sig": "MEYCIQCAO6zeLteslSUzirl51qWWGpPd6edP+BR4cl5tOMDj/gIhAMPsNdsIuwG04WdY91317Kf47XohxsRWzIVfA9Tj5wso", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs85CRA9TVsSAnZWagAA+7IQAJU9eiCfZw4Mqq5m74VR\nMvPq1PdSHm9sQ1KLFk9qjHBIJS6qwZANuXggxmGIBQunSjQEmuOgK21HpJ5L\nOFOKry5p+quFlfFMsd5/S5asBF9Oc5Uj0IRnpWjXLQIM/Fa6096A5scrQRx6\n7hqZMT2GVFm6tw4FUqfyu+sBWIhHpxEM3ggOWvZd+wfl9TGovU92MiwaSIdQ\ne+O38Xh0Cet84P32PXnYU1bAqlaIUeOnMC/w+lnXgkSApwbmSROqE3bQnVbR\nWYF1m1mERxmb1EhJw3Pzio9fFJyJotrBOFWpSkyrBGxE0/HTmviQNFUWvjMI\nB2XTBqUD4Duxp8EnYsY8h9XFvGJgbNEDPrbBg+OiFWHTAJ6Kc2NtVjmWWInH\nbTcYY6EdAnIhk7quRkGC26Wl9U4symXV+lD//aSPRgJDBqLEraZOrlfVvjfQ\nIGvdT39cpFCS0fbc4jexftHqWOm9Q+1ZSR83qISA96nfYghTB7AINLaS4Pj9\nyojOTIxu7ZuqH5++8CqL4+9ZcDDNIYX8lYcTD8BWVJssR+KGzZ3nVsXbmcPH\nstVwto6SMyNckseTzgvgEreUfe+1g6ql3hhbAPp4f/+dF/Cw180cIyIB5v5I\n2bBnFGBTRLy2vpHWvR5gkkhQnuRX5GTjy/1/QXOAlL0syH7iUdp3x9vs5vq0\nKXKV\r\n=UeUU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.0.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.0.1_1527435064974_0.8593626222681037", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "babel-preset-jest", "version": "23.2.0", "license": "MIT", "_id": "babel-preset-jest@23.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8ec7a03a138f001a1a8fb1e8113652bf1a55da46", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.2.0.tgz", "fileCount": 4, "integrity": "sha512-AdfWwc0PYvDtwr009yyVNh72Ev68os7SsPmOFVX7zSA+STXuk5CV2iMVazZU01bEoHCSwTkgv4E4HOOcODPkPg==", "signatures": [{"sig": "MEUCIQClR+Lzv51pPfkLeVztfWN6uPKISQklY5fYZwT11VZcdgIgTU36ZQo7YrLAyatqAJt1AhB7+d1P6EDfHmBQu7Ss2g8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1220}, "main": "index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "dependencies": {"babel-plugin-jest-hoist": "^23.2.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_23.2.0_1529935519742_0.2976840587037439", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "babel-preset-jest", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1932f15862653b28f95adbdf54fdb95a35dbaca", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-MJ4ktzK6feUtOcDHnfo5/hihsosOrK+FA9vPSK+44faMrNrdl+JoXmE50KLjseR/+3mQs38zTq863vEDFuYDbQ==", "signatures": [{"sig": "MEUCIQCIicz4s5hCdTC2C/Qzco3F5wR1BnWensk8h4GwtrDFgwIgHSRJObVFAlZp/TI60RzB5sRV0BA7dDxPJWbYy9U0rwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycpECRA9TVsSAnZWagAAuyAP/3MySREd9kOrZPgGL3So\nqVKiVYveuSoyq2evxfUH+xLNfcCb6mTfCHFRrAaEfmfULXpDq+3E9yQaoWnu\nNcg3hc27fHxhWOO/cPi79PQtPWFfTfmL7VU4x05H9EGM8NWyyW5kDVPGfwnp\npOwScwgCoXI7qs0fg4hGLrta3vvuezfr/UkJZdF3/xudpsYte9v8FpsogNzx\n40Qi3HFUN99Z1TMzGV2Cf9pzzr/hsS2PF9gJ/gNvzyXcY4IZtQOAjfMDY38N\n0wERt9aisIOlFFTmfYwLhR+GUS9jYlkTgy9zwZidh4svhmMOxbbDbFx/+t4d\nMLmVT3bpuf5+9ZCy1owk/p0gpHEQJwTMZUCeMrI2X9QpyUOILuNhv+fLJHBa\nTxvOLqqx67UpSSeBUvlSJr/xqD13rhmtPUvEx4TKX1fbrDF/IpSJSg+fYTfL\nHIfpcq5H4cAJ1uaOTirCw01lIiV+wxlS9RHSZSEun8UErkrdetrNihpILtRh\nKYeureJxawc7CPy+jOypZBarLqTr1yUzQmbLL7eDThqtwgQeErX8TekyiQc/\npE/tZHkkJhuLrcaxH91NRUbeHwdLl7EaIWUyi6TkLd2tFbN1+p8GSsz9h5tG\ncfWTRS9968VcIehvn6lEdZXc4jfdaAxcvpaQntl0ocWCK0DZ4BTLhVfrfwPj\nz8st\r\n=rq8Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.0_1539951171701_0.9885720091199088", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "babel-preset-jest", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3fd2d18325a36d299d6c1b8ce827fc7a2c7ebcc9", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-aEI9leFxeFXG2lyHSMKR2JBYwRSnfsA0Ayu1osqI5ydOz0iZci1IXYP90ZOZp5Gfm/nBidG9ywHs/JmUXyvvLw==", "signatures": [{"sig": "MEUCIEf+ixjv8al09YGP25kW8dbiQ/ghI9NCRlafGt8BH2qAAiEAwtaOxtRxtTtPAeXyHtNOcDzt60wDsaS+gd3vy46YNNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5oCRA9TVsSAnZWagAAG40P/0KGc7GfAw5FAB1+FV7s\nVYlIwPIcH2nZXnSlSutFM9D0kqQBiVQLyIw/N5jTVKBFqNmwDIxFt0Xi8DXk\nJSzmqsKNUXsYJ3U2auyxXzBQ9UshrqitbM5PwBKdhPZJ3cP4i9AR3tMyuqme\nNR1Og/ALdpYDqxBZLzcRCQGadzZMCKZ19Efl8f2cr0RAnwqsxOv9R3tL1j6T\nJpCHhB1wJxmH8E2mxjBYuXDIehA2RdEooZNXpUGsOsoaPnLfSxJYFDqAVVvM\nighqDtL89GDcoN+kgzkQIGHIwLu4wO1GSHNW+FWZ6V4pixhKNQrHl2pMsSLn\nX7MEPYc4LBajs3saDUWmFbllRYNbWT3P1TExdgfyIvvMQsgog1DtqH6FCvua\ncoerXiIKqX5gUsvCm3QPVtA6j3hU+pd45Qqu+DJ+E7abh8SluM+QpjK8VIgR\nzsL46eo5vUTwMuS3Sk9yuHIORZKcl3RULcgINisLCKVM0C1stT3A25BIh1x+\nV/9sZheOkZDQN7LGq6NXgE6nOnoR9H7NDMrnQSgDcLGf2Qx8/ORiWPmeXNB7\nOh0FABinQBo97CyW0sEi4wisqht4Fql6CwG0S+7LpAWk9fBdpBa+GEbs9VQr\ntJbKzjk7prQLTvghwQSlZHLFp5iil6evEHWuXNUBIZMfxa77dPh+/VCpKZei\nFxDf\r\n=sOjJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.1_1540222567389_0.98419505676057", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "babel-preset-jest", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff1ab8b9830255ad2dbbc7b4648bf462fc805050", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-+Ir4ca+vhL+XN1T9CchVUW/kIqVvkYrXMesAtsHVro12/Y1cmO3RvKUxgljRTq5mPOC9p8h57j9NC61wpsuEzQ==", "signatures": [{"sig": "MEYCIQC5p0a5ubZqETXU05iHUPQ30Cp+kRMeHsNmeuqzL9cJAgIhAI0NpDkm9F8p4uuneA6+rvwh+KiV6tXMHqXcARDFOVBB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aARCRA9TVsSAnZWagAA46gP/1Pw51wbJ3LoIhCjN6fa\nPWTHli4hOLTIl3LXBaxkgd/5zZ81oJrAZdxvUEbDBA6P0xW5wi7ZgFzFr1q0\nUX+tZ3AI7bF+TybAz/sfwzcKJ6wkjp8riWxJ6rjvxfa6ZylKUeizyg4oXuTt\nOCgsEMWa4Z5faTqCLReqTIXAsqDgBVjM4FPhk6SLjap7Il3n2WspiqyrkJ3U\nXN0QHwUiVI7xLsYFXudt1jt25CgTVuFgxUABfFbAlG6aWTasU5uePYztJKmt\nB2hHM4zrxmeWvFFGJ40+zej/gmTVreun/CtKrC0f2Lzd84YnuSmR6gG6RJZx\nA4xusbm64/XWwAb40UqWBfV5Fvl6VsGbdPemqKnAysiwbpotwN3EkQvYEG89\nFqVYuz9GSBhqEqqKBsgvUmPx8jCjR3607L1xhxQmzweHYhrrmx8pYlxjEt/l\n07MZjFStaxmpZVjVQkyM5N1K6BYMNHSlDHOhTnAQQyc8VYWFJnrO7zjvj4hF\nWyZsKPSyJCe4/4o0QKH/fLHmpnUVZ9Pk9/0CiW93DGC/UJhAhwl+6Ka4w/Yg\nIv929x63KenXWmeeAWPJvB/1R5BLTsEmBRgDTtb+7iI205qSDaJd1HtC6srj\nbqssDX+YTZHVijiYdoAOSZ14oR4a6l3ntYdZzkEzZtwBxWThEEK8yVlzqyTw\nmq+/\r\n=TRPS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.2", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.2_1540464656562_0.9563255070841885", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "babel-preset-jest", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2f704b57695d801d1622fb809355a426c5ff0556", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-4/TENlz9eSYPTFBWGsJ8Ezpoj77BkFtIJz2tT7EykfFbbh1OwPoc97+JcFWd0HUtOrKf7SYoT3DW2Adc7XveuA==", "signatures": [{"sig": "MEQCIBNErtA9Q7RXkprf4REiItA7OHjiYhzBjZY9VnZH1qMiAiB5CytZqAXl6uUoJ8camFj4ZYtiHpaYQzaWc6UChYx5Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00HRCRA9TVsSAnZWagAAwEoP/3wUNdfMroQhGjFggw7U\n9o2A57JgEA17733YFN8Ix40va6OAZi8gBQM3GTGASxgz6dl/Pz75jJxayZV2\nyWxWEfmn5cQlbsfoNENRN1W9RBGGowPHszWr3ZMN5HvY8iIa1Z2kqZ00aSnO\nvZPYlNa8qEahDva+La5c+44GavufeZIh/Up9mHmKSqzBUgtdHLvzw2HmFCPl\n+JNZpGdi3qD7wzs48do6O7AlYjO90abTC3J6O3kQzpxLwHqkS1A++R7IIS/y\nx7AlqWjD4TkJTXnp2xpF0L0szyy4JMzlqZ7s4AxyZrJ38I054juaZtrF0ThC\nbD3sk8wwqNM+uORaL9tl2JQV8Dz+cmy033i/u3QMorCv3SALg9IGBCXoPMuJ\nxsxA+fWjDNzvDu3/1sRj0e5yg71zy6CWHMviDznWa+2reA+nUZ//yxUMMi1p\n1lF+k2AbC09X64e8OJtp6sBKBavrshxgNURg3s953tKQj/BoEGA4MrDTKr2E\nrnLzdsA66QQtMHEZsN6tfaObU+vgBihL9ny+tw4qPQNnZAKc7sHywwqB9AjM\n2aZTKE6Nce6mhiK6T63ZT4mwAumy/QhOFx0suLKV49jZncf0XiwZMQ7qPxyn\nSMmQ2eHGQYiMHyHrRsEbVNntVFoBdO6RDzviWtah5xQTQRJ0GjtXr9qCeYkk\nBKZl\r\n=vl3M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.4", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.4_1540571600720_0.8703751959545067", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "babel-preset-jest", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d283ddcae973d233ae67e1d72da8d8e5b0fa898d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-x0o57p1/oeraFhxR3Pc6b+830hmx7qvKiv7CzsYwDDwhnbdp/2OmpDN3QayrEx2kHe9nh4EzoruyQLc9tf/mxw==", "signatures": [{"sig": "MEQCIFTkPBe5Tr0dXfw+TI5s9RiH+wUS+fcrHTrRGmddYTWnAiAojAlvK7xqe9lvV2dTjEHb13mwEmxKiJ8tdo0fVe6TCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfUCRA9TVsSAnZWagAAQk4P/jm8oN40YEnk9RuIHhWx\n6avv4O+hkSAOjxmCNlEIfp38fvcw9ibF6oZlwyNkDYCjAmyfoL3TThs72WYu\nBH76/030XnfLr6W73gzF1KI4WIWE5VMOHvZ9gSWRRDSXe5GU6eUZWUiVFr6p\n9RezSriKLETVm/MpLhmGubdFu8VqCOFh+7yJzjjx7JLoNmqBQn46RxjyJrJY\nxhGd0SItlh4QBfUk+NEL3EdiRszKikXMQzp0Gc5YJEJ+F+uNP5cGxnA9Asmq\nSZOxdVEhVMGH+hOk30fw/9CqGAvAVZ7pCcoaSrT0S5Emfiy+hp9FLxjxOC+T\n6TDIbdRZ+pTG7r2WXIIpSI0lFcKdyPDPernpa9f0Gff+2+JBXOZO2QOrhSiS\neF19BQH5YHBtw3Sub3/EkKgEiZcxecrxJE1+y0eUz3lkPqR5DoOM56y7ETZO\nCVYwkCuf/9yqzXIF6HDpX9Z7UN/yjHB0QJh9yxwcoLiqaY0/Y38xaXSPyCyW\nF+1XIctRUWi+6QyNm8+ZTbqQ1MnubeDXBPdr/MUULoNa/eu6R8REECBCU9+n\nBKFCg/A0GxFvt/pGBoG2IxXVegCntezKjaz2BlrDIhzOF7Bp2UlDU76UwxAE\nUeaQaxsfQnw2TmHT5QlgWvB3OUABUTHLQU9Qm88Ih0amDYFzdz1otdi4WLru\nvITD\r\n=8omJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.5", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.5_1541769171457_0.3595289684378229", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "babel-preset-jest", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "effeb7f6927ac2734dd55e2f3936bbaab7312a6a", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-XuKtUS/hIRfRgJHTVpwGe2yZfE6SFqTElzMdG79tUuOs8z+BxURTCNuVXecVcXDuermn7HA7NAi9VS0zgSmc4g==", "signatures": [{"sig": "MEYCIQDnOGuwhTT2r2DA3S2mB0GlbEkUd9IGuzV4aTuomNfATQIhALMe+2EVX0EwgHgLLPi6movAEgZenr7SW9nwjFQ3y3xT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ci8CRA9TVsSAnZWagAA41MP/2wjIj5PaRa6N8eP3Wgk\n8TwEZ9ZSYtjDuXI/sU6eHvWM4BGoRP/tA4TV4YH5daA0ZMz1B0WRSeaD7nBX\nCb7klk/AGaoK6BMJi2SSdiPjmYi7gdRAIt2Drq76Ace50U9InutGGKsRSLu3\no5Kkzr1BMOKbNw2yw/WFm6kLcSBW0UqqLaGIMpMxibt4GEm5tlZcgfiNM+BH\nqL8eHu234km4kEQ9HIDJAjLLrB8ZKSpzyGgVhVwySilGJF556dm7qyzhOwVW\nigxnX1q2lafcmO3US/QlKr/YLMyQDZH2PQWLZpdbawyDvpLz7/vtXhDGAiA6\nSljAFQaN7zVesvFLr3HC9kjkK6dJTgwugX1rnENfsCQsXkVGNpjT845XhXm7\nKl9pX0b2n1zpk/n+21ga8tFEtHtGC1qtd9p8chzghvnrcUtUizx0B2HWtzuY\nAuyoiKzwW5wh8HgKIXg4EfAbJ6b+t4aiREKlpWDcGELb7PPqXqe+xAXpKKkW\nLB7Y0abdxShK/eMK02/zsK4bSSHvpBUiHwDfMKDzYd9aCQK7br8ODzQNWQQK\n+0WD0bnuOZ5ZbQpRHygSRrikN1spPyy97ljAEWqXB3C+VNs1f0vVTZCVrMae\ntXoIqhs3k4vFFrfCye1vNTuaoekA2QjRSh7a6x3NQcmexepYvMuR5LPEYLNl\nbhml\r\n=wovL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.6", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.6_1541785787334_0.6548102364427544", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "babel-preset-jest", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f3ae0d16cabb2690f4b26cb2d9951cfb5c2e40f4", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-fLZwqHUp4YnEV+UC2QEvLlG/3uU8eoN1GubwTvz8FTxTU+4EwEfbcx94YTIW2b5GbJR3+pzZdKsvpwZ4bn3GNA==", "signatures": [{"sig": "MEQCIAaWZQR2w67o4AGEUKdJraksSqS694tZT6ZKslp5msGnAiAg1DosbC05j9nN2up4jWcOjEqMvf1TdHF+Qz/jXTsqmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+IUCRA9TVsSAnZWagAA2CQP/j+IyaVol8fuZT4kSjon\nCanJTaDBD1F1KdknPWrUreHYBANRRAFvyiFKkYyjCNKz5pc6eA9ttq5yQXJg\nlOo5GsRNPyZciFAEABmpNDMnkiEnfRXIkNHCCFMWipYiMfAzt8y/rGfz4ZT1\nqgeDblNVFFAeQ1PIb3ttmsr+dDjfVq/EoAqeV0TAXJqR/7N9qpJKPPMs6sfQ\nFAdcc/NSeG47sRFuITm/cT8cK238fJC6FtxAZVo542qjxsE/Dvx4B3cxI3vD\nlV0/Tg/R3S09LL5fM8EkQ/K/tsBaJASOs5cR6nSgmDItaPyY9ohKc9NmzipJ\n7uLHLpeR8wqxVT6BJAPaW/3mfyMueqgDKpMkmv7BSWdq+hPD/tfPUrZs1y8M\nyYGYMdaKxomJOfFQJ8I+Ig+IZ6PFmDG+rLGC17UyVTrl8Ndc4PJmr8/AszYl\nWKkOg68hMBVUmspwcoRo4WKiyOVW9O/CsowXxP3bfNbycXiZ1BqMSxKLDD4w\npIA39jZZNYvcW/HrCTjuI1lgN0CvNL187ehDRcwhm0QPepqgAgvJo3UZCaBg\njJ43esZLAsH+XpNg4bZa37XzOMz+kKygVMmOiyAwyc4fr7RZQeZLpvbZm5nA\nBVDa52iMPSamvoPpkRjBC9we62Qc/GEMbH/W7yHRXkvezy5dK5TgTRB8VmBn\nSVyU\r\n=YAJw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.7", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.7_1544544787263_0.6713777092176036", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "babel-preset-jest", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65b72edebadb92878c84849c8bfdad3ffa5235a0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-/klyveUbCSOAnwYtflB45vJM4EpeDMLpgUAo3SS/GO9Jq2D04/0esO+Qtkef4sLn79u8qi6Gao4lZXZ4vB5FyQ==", "signatures": [{"sig": "MEQCIDRy/dZ3BNg6opPvEGm1ZcHmr9B7ygzUV+IBbXBzmuFvAiBvAaaxzYNNHJN6XT6kHd1eTAkqlgB4EXIQ2QU15vf7LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlQ6CRA9TVsSAnZWagAABn4P/iIh+i2wLxD7xWJPoLUs\nOtpQn8/oH7n0ZPUPN4TZkDbt1Sh2dA/kACj8/qWbq3AqBJBMrl/wnYY6TohF\nUN+Ex1dum4ouVqxHlbXDdDH60RJAV9gCHTHC6FQAcOWP8T5Uqgi8ZHEPyx0O\nVYpHJiOp3pUEvmzG/6HhQayYh45cq4snnP581Kjuc8loIVMe87btZpZg44vW\nz9yZuy+jmz4vrp+7uYEc7rHahKkFXlk3yjE1eth2z8dwp4T6IzocSINs3Ac9\nBS7sP4ik8Og5swp7m89mquW7YkwBE6nhAboBgLxCSsq+70jSlfsSZdKxXe6A\nxze2NllGaP6wxlJJCTDmWTfMBEowlbbKgTqO4KFimvyUNYeqTs7MTjscxYcA\nLzusQQYf+yn1vsrIc9RejCF8gXfJFVSIJ/9A0CLKUqlKY6T6tH9IBLV6+319\nk4hZsZ8a5EmHBK0xz6bVhk5A93S/SVR3jH3ODsNI7XrfbOgTAMhZ5RmQUXFo\n9xlFl87rmC9Bc8qcExxiQHfTvqXe4McaagsrBbmSyHm0QJ4/JIIh8lKv77mE\ndpNoq0ybGSKgq7/cD0K7i/UcM5XpUCS0i347k9rm7vdlx5YRHO1fAnI4mZxg\n/ghhEKLNgUBLP8L0xaNGHTer7qsk7PArLs/Q7cRde0WA/z4f2AWQ9EwQxyYt\nRa2A\r\n=/dox\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.9", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.9_1545229369540_0.1536282443214596", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "babel-preset-jest", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c069582d6c8d1cbeadf7bc633464c14c6b19e399", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-plljqEXrcE+A4NvezNl7K+EwMgBQLeSoPRRV27zpUdwJNn0Oto9R+P1VxZCF+oapNhh9Eo+zXbXkdC+dTG49cw==", "signatures": [{"sig": "MEYCIQDw5xb8c7BTeVtWHxbHCkoVPW8ZP71/HGAO15syLysCYgIhAOOUhuWVkIpAQP3Ne2VTsHDA9jue5TlrfckxM5rCZUA4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNijHCRA9TVsSAnZWagAA50QP/RejPcYdd4iYi+Ica6aD\n/5/2c17KUr+Zor60fSNfHwe3OPSDXl7a4tbakFDEX/crbKQDVCQhHFzPuekh\nI4xlfu0YPkLCgxV0NoENCzP5VnjRe+IK5YxpRZQjwIllJKwhXRIBkkcYQrf3\nNNdj/uuXHgG1fVLyGJ5WdD1sKivHwkM1JWz4cNuYRISNAZ48uGhTEKHaMhjx\nqf7tPO4wjxU32M9JxyF6L31yDiwxoP/EkqI+oZWAJTIaZhP/EYAE3yt03FoU\nlIYAlDlaSPp3CGKMTJZYHXrCDO+3UfdHdTy/aXcMpDCsrF4xpKleGE22+yRL\noTdFuZBRTbQzktIwc212RMX6A8t/KFKW0Qst8O5vwLnQLzqSaYO2uGeT8pMA\nPzr5BCvwenWAuLLbJD4x4fMilsINPsXkO9L3uocijFAJGrnt2JOq/Te0Xewy\n5cVm2eI2o5vl72egealymr/AS+FLq7gcnlfE+v92B4E0yWXVGpX43efvKfO4\ndx9nKHEnKgytWpwviVJBMYPfOIrKPwdkRE69vkhqm4HtV4hmp0aKS6HgRX7G\nEN+fI+m63ouHJSQuTcooJjUkia5ONeFkuq6s5Cq5O/m86maCWwGNEwS2NYnp\nf16wk8gypEvJtamYefq81rmRNZOPVtjfdoaGr/jrKojRqBkTgD7jt1sSlCJ3\n7Kg2\r\n=RrM+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.10", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.10_1547053254777_0.5411978881569897", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "babel-preset-jest", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1da0dc9632be057a3b5ae22be60d764e791ab722", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-bM6cls0h+K16/xfHUKWMhjHXqLCY27Mc4rT3qvTjyeWG1ovUJHAjKixFdUpZEwRVd8zZAmyM5s2yAObvHGD6EQ==", "signatures": [{"sig": "MEQCIE/XwA73Vp6ZN9kGeE6mwLA6V28u14a1fDN6R5TJTQDNAiBM9rrki49ewVkYvrMhWYsnDCF9FrLzIK/jCKsFchV4Ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN5HQCRA9TVsSAnZWagAAvIcQAKKSsF6SGwLaI1lVzJut\ncztn3NP7UklyIarkCEA/Nx36QAXbVYNdaMDwOT63LUYXNQ+V2NV7pSRBluLi\nPzRclANXiqhUsExaSwcfo6WwLoGv+PQE9tWurN1zIVl6Lhx+rRrkq84h4an+\nWNiYq75J+81i/4NcLAJEk5SCPQNsg/xW1q7jq4QAGEv1GMmv7Yidt7SzDpKE\nf/liCyvIwhbqglikZZY34Fx2FCpLdQZHhkNon2nz1f2WaBtLk+Jg0X86XC2e\nIvpfHrjzdL+wSS8d4lNX4y6QJjMGxFRsiM67cW4hjUH87r2hrr34ogTe0wFz\nkZGrdpvYFIpUw/aivlbfU+oZZh0Pd1iK8ATB6f0Ou1a0JbPqJV3YpJGFM5Hn\nBpI0jIgyvWvbTogG6qx6sGBgleFbDMjV1O0+14q8AZjgFxfweH+uu1DOpzIW\nTaCJjFAso6XEyEnYdBD6UCIlMyEbtr/+Rv9CA95Jtk8H9Xp5WbSUbEKIKqG3\n6uVK29uw+6vxIh8MqDFJyOkPFzgQCi1CACZ55+I65v4XhtRu+haxIMB/tvla\nEt5MO8RESn8BwhrBlLGb0l6dLwf33IVL+6mBD+wJUcqc/LUS9Bm6Poz35Yiu\nU5TLjFPC7FgfVNi/lBedf79Nt11+81cNhPGyFBPFvzuOGviT3aY9ICOuN3xd\n3yWI\r\n=8Zm3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.11", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.11_1547145679674_0.29972167195117816", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "babel-preset-jest", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "be0b4f6fc74943b54b2da66415ce64900cd048b2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.12.tgz", "fileCount": 4, "integrity": "sha512-CvKXR1nwN/kHLGfzjfoCJtBw8uRyktVxJQ9As5IIDycwnU8KidiHpWYIh4lY5OoOFHdE9oUR5ZbixDmelfNLGw==", "signatures": [{"sig": "MEQCIHSEz3rJ3DPW5NhaU472KLLxr1YIldc4Chz69tnkUiC7AiAyWS7T9b3KhLLIj07SZgvZ+OS51y5eC3KPCi2ozqgN4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK72CRA9TVsSAnZWagAArqYQAKDSBoqrU7383wo6RV/a\nfurXaTJA0sJ83Nh30I5eDYPdJXnPJdaZeZ+SZ71jZExMCyKmsoFKj3iOeI1z\nkBOLX1YCc9n1yxdgSwnKtlEIdTz0tMO1r+THaCTvtkGVo5sPV9ZiJPfMQ5nS\nGTWqEN7to+0am2C/J3ra6d1PQQQ3/svYIKWiAXyd7Af9kjxM8tUSQwU1pWUq\nbYVt5jmCJ/IAl1IMSDhYFNlArCAreoGbSpIFL5u08Q9WnO1KgRhCzICcF/yV\nzugRZKeXi3OeQc1twNXMuhwLnLSIGeoPQd1G5Sy0NsQy2Edc7hrNsybVsF4s\nl4Rsm4K63eDCRbMaE2GqmjSGSt06oNyP1jm1HOg9SdIKpvKRSn5mFHsdrQo5\nzfIJvedGAPinjcIeWc5Bh5xXJdSOYkKE7I4BgzSmCEWF0ffHMWaAH3Y/94gH\nHvqy8AuWcKKZ6tVJcFhcII3OmFtGP9ApkSRiKve9kq8PE5uAlPXSg8UdFtjl\nVppbLorn6ZfVtbkVbyPkRLd13xBfC2cfhoclOo+W/Bgzo1n0Bat5af7KQD02\nBwIaF4LzfUQZZcKlUnuk7R6xY1k/AMmDfH0b2OfxGTKqk0okof7oX+/JsFlR\neXTE4tDzyBUY4bACtDp71iZrBCaXd9JCsJO+dHP2n/g3SJDq3nKNI0Mro7g0\n/X++\r\n=PXZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.12", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.12_1547218678311_0.7573370757074402", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "babel-preset-jest", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec6e89804a807e01d081a6fcd2e516bc7ed9c7e3", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.13.tgz", "fileCount": 4, "integrity": "sha512-u2BAGxMx1aBPW/DIGhjJBRDJ//x+NEuLjt8TaPAtY+Ff2CNPAnT3HSmNWAesWKLNnFLezY4vtauMykCDH2ABXg==", "signatures": [{"sig": "MEUCIGe5MqWJGhaeXmx/csIR7EYR+MoFkUfek+Ysu15+r5KzAiEAoxzW1Xcdu7w6UlRXJ2CFNXmqJHPq5eyf+Dk7kfArEAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUWCRA9TVsSAnZWagAA55QP/1RWhSW+/qy59trBZQWy\nb1Mf5CyIXpy+QlKgMxgXjUDAbluMC6vzt7nPC9qriJOh2zeX0PEbBiv0CatY\nV7z5zgMhu9M+m/hUOu3wMsm+TYY79o+XRR0eIu2YTRoxgPOSzJZIbwwZR506\nwnxk2tA/4iP6ouhlRxqNaOMf4VLqnN5OpYq1w9mogWzFW8CkAN8g24PJ+ceP\n82WIKW1YJOigACCHvqQMx2aCprKUCYcIBz+r71FuDttwKGrVhuNTd7d69y18\nYdid/DhfrCio5PdSSXM+5FWR9OkyfdgmQVC1ky9ZAI2z5g1ItGkLknSFjpo8\n4JLMToAzpG8pLFR6JuH/mpTrDZK3/qxf1otKfYcr0CjVXdiwJbfYPA7cA96s\npzGy92hPDOOR55u4x/D/UY12dc2ThRkxYkPT0oTmNgyrM7lYMAU8EklTr30A\njFwrNMrK0WF1RH0YeODsICs91iZNMDMByQ5FAoAOMFS3NX9yBJGFQ2NgijGg\nJOZUBwuLQvtAfrQ1liOlpXByazR4iWJmPuM7rsGdK/qlZ4avICY6LJmYbN3G\njahds37NL4ne0fnEnXLc46EE0sNsRwERmKn93RrzGGha3pWn0W1NgoLaYtBo\nxrxE0Dkfk+4fgPAKApg79CQk5yY9lelu3t+C+F9P7SUCiC5cA3GKZ7LHpwDa\n1Lc1\r\n=b0FK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.13", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.13_1548256534159_0.7385910983045403", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "babel-preset-jest", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "385a3f8757217b9ae77705f30374b389fd0f5c29", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.15.tgz", "fileCount": 4, "integrity": "sha512-jIle3YqCFNm4cuQZLHyI2cmpfsipAOxh474QqqqLf9Fou4FcLMlMUhaWcIeEkzttU77+x4R+CSTjnOLHsMZLsg==", "signatures": [{"sig": "MEYCIQDFxrRYb6wIH5Jfatn75Ur3YxYB2nAz9Rpjyecy/gP/2QIhAKnOh5nU39bfFaZXT0GbonFw1CvYiqH/jZRg7CfgtidI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftnCRA9TVsSAnZWagAA6dYP/j3mq/hpSEad+2IXZNt3\nJcH1x8jZEi+SS4dB+wRhhBX5s83k6h8eux3gffbvt8G6tEnoTyyA9gCKzwtM\nCq6jSBSHnLKP2gLknQaGTyTI2xnyN3px5l9JvxCrvbDd0rP17PrUQbySRqjt\nkFq/h4M42A3f2bU5oiNIzGDbjkGfchgU7ogZgL9EPfS/mUFguE/XnkNpHS+p\nX2bHbnjkARooZ7es3tB+6zsJKmC4vxG8+q2+V2MhhW3xjyQZfaf2eGv8JKLw\nv+nLvX41Xl0StZ0e4g4ho8kRNrOEjp7bY+GOtu6+TFSeknkea+Wh4WxD3rHI\ncoj+o9GNbGldwColnQZKb04owFmAiMiECYry+D3bipj7lnShvetgseJJewLw\npPBycxpqqN6BZsCvLDTtpLFj0wLOG4SOqJfVK9Ib2GfyIoiBpO+TZhl+Qr4g\n7QKHd2qv61UTr3s6R+H55NIsKkvhLvfWp7CHJ6o/QOzGEuugE7WvhLa524fn\noiglLx7wOBnn450Ga67MmVmMkovatu+k7XimU3CeDAS5dnnpBspsDNDobieG\nUxNLdcNs9TgUZ40zGj9hxooyhF4Qu8imiRTj9eBt2VP7HN1vNP/V5c8StVcA\nBIJ6bLk/+a7+shotmKE2So8rFumohj8YU99tu6b4tMF0E9w/gbKvAXVKsJQM\nWNFX\r\n=uCBX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.15", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.15_1548352359109_0.4598831810396333", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "babel-preset-jest", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "babel-preset-jest@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "71fec29b939b53482dbf1d8a2791e3a69b6c1102", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0-alpha.16.tgz", "fileCount": 4, "integrity": "sha512-lvskkhpF8eHNVBnubLkrvnbdQCZBN+sP4Izdn8jks0FWaktdDKdWevPFSuqvNH29ujlqNOmJUikq9Pt/SSbZDw==", "signatures": [{"sig": "MEYCIQDfDP7whbGVWhRDmppxQ/Vt1Jb/+cr9LRKcW4JJorW4FwIhAJ4BQf34qkgm/O5WaczNxKnOV1ssNDiL6maHj3b0tE8x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIxCRA9TVsSAnZWagAAaIEP/1+faY6VnfIZLn6TpUkI\nfXloctrx2rsNVcemGNwAflFBJbGUz3KCNdv8qhsRUflnO9tRVejDrQSe+qb8\ny5XDj6UDzECqBUX15Y1rLFMHQn5ua62hTn50gBk3dyzV9+fSfZW/1aFX617f\nmrSwyMZ9ZHK3zLB9MVQOtPswVSybj/eu3FdxqYF50dxxd4egoM5cDDR10wOZ\nNC4/3M6EAkP5/EH2y0yboPe5m9Cmt3J8Yn29ufAXlSDsOk++zPCMAlpRk0KA\n/ZokWklzLrWhKCoUW/BobJly5x5+iSWHBGR6H46F43dEQBdTqDRZfuje8psE\n5e3HmxlpAsI9UstDC+rRO7i4KMw+Np+HXdzl+RpNUgJCFN6PNEvJrF8/7KJr\nSkC7NNEfp+I2i0V6bR2Pm8JRZrQF+mnvXbDD/mOCBbTCOL3XsanmGTggR8S/\nhlrqsPYCKVjikT/MaWtWd+rjXr+LW818E3aagwNnNFXXLixcxI+Vp65J5l17\nHHWkTlgwXLdkLAh3rl5P5/0THYhOuPoybtd3LYokKNUtqd8AZedHk8SQE2Hd\n024qh/s9MVItkAVeqEAGLNAM0lf3WlkZIG6ZIjN3FmnSEYWxEUsktK5owRjC\njeZwpJuGrBU5sPe2NANvyEa/RToIzk0m8/RPu1SoFRZXCnQ+9Z6yRnJBqFsg\n3ZsV\r\n=vyws\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0-alpha.16", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0-alpha.16_1548423728937_0.20258241962712398", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "babel-preset-jest", "version": "24.0.0", "license": "MIT", "_id": "babel-preset-jest@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d23782e5e036cff517859640a80960bd628bd82b", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.0.0.tgz", "fileCount": 4, "integrity": "sha512-ECMMOLvNDCmsn3geBa3JkwzylcfpThMpAdfreONQm8EmXcs4tXUpXZDQPxiIMg7nMobTuAC2zDGIKrbrBXW2Vg==", "signatures": [{"sig": "MEUCIQC4cZF8jRv80tfN2iMi/xC3VmiH5JRUtYwlGG9sMnJh7QIgLjZgm2ZRP9T8D3ccALeuvUmvgzpTs2okC2UfRUURjXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWgCRA9TVsSAnZWagAAE/AP/0+Dmp+dRxFP+hUt9TzD\nBc/XYgC4rSGds5F74acpMc+hcLCCJQCC8XKsTMvMJi5wOrTbxBptRXkRWRYx\nEp1MeZWvLPQRSfuegorcDvgOnkUJnDrZRbnaSScaNe7GEivjRXl/23kPxldV\n7DUHJ5boJBeLha52xnnoF9cygGFaYKZpZeb5Vf448BSDj8ZGKu2SMIHhnJOQ\nXrEyJGszLX8O6vYBCiS//ruawSLKkWqJ3xwZvCHG8msrcJvQYZcsFrY3Btbu\nLLTAtgJtE9gNKxPGa2qNJu6C7wnfCoofDPhC9vCUJ4BnL3mFFQYVhoIlBfL4\nlSitL5JFewD5kxxHoLay58ErrEbRsuf9IOgUiMHG6W3KOGzy3ESjjB+Ygcsa\nQj81q2vGpOaOlPwesXlkVUWYONKEmqdZB3cwtlUjgucdv02xS72zX8TcsID1\nWu769aRTd/sWGnRBksu6cfxhuNgse8wegvM/upDFGIPW8d6ZGTWT4rcg69bq\nt8rsPv+2JIZY83+5X6QHkQNyiMGNivtqvU0OcVfJXGZ20/Eyh+rKJkJm7O/j\neoB5FRdOGtsVaayvw71h70135/l+XOSqJj06h3cEfCMQ4Mf8cWZBJepYam52\nRki3wB4+/nmfUo6JO/4uAT1jv0wZlNBHKQ0yGBSbQuY22/tHILUW041LcUgO\nLlcb\r\n=v9kX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.0.0_1548428704013_0.018383086769627077", "host": "s3://npm-registry-packages"}}, "24.1.0": {"name": "babel-preset-jest", "version": "24.1.0", "license": "MIT", "_id": "babel-preset-jest@24.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "83bc564fdcd4903641af65ec63f2f5de6b04132e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.1.0.tgz", "fileCount": 4, "integrity": "sha512-FfNLDxFWsNX9lUmtwY7NheGlANnagvxq8LZdl5PKnVG3umP+S/g0XbVBfwtA4Ai3Ri/IMkWabBz3Tyk9wdspcw==", "signatures": [{"sig": "MEQCIFTfZByJT2ZCqwqxUt1GyeN13L3t6xJTHZktKcxE0z1kAiALzQmyhzgoSg3d5+bRb88ryopWZSgW14XG3qeCnlPs2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWaaJCRA9TVsSAnZWagAAqscP/jzoj/xtQU1tpYmkDzWf\n1bzwlu76MRSH+VwWdftb4Yn5aEPqEE8Hm9KAqr22xopeXSd6OY66DEp0XJdq\nB1U+uH8lv6FFq5Ry6TOgp2G0RL3f33AYyAtEDYzf4WC3KGq1ZXNBTgaj4/wV\n1Ui8fksLh6RmAiuqN05I4P7lO2nGs/kQy9XqGRsYgA39tyxYJr9bpvC/xVWV\nTnrTjVXfcG+cmkLxOwm50v9UJcNGUW7UgYQNWbulS/rDiA+e/jXxwzqQNojn\nUzo/TfITLBwLBgpGIOXgrugaBj3QxfQDb63H25hW4mwBawwRzU8yvINyf40v\n6IilSjqqWta60L9IRcw0l0aaj+LHFHJanWIls15nR+2QMoHA6FSO++tGXaBZ\nKe2pyaTN+1EOfIsjxA4QbCAiCeQBSYy1a9WDOkT+T3frYzJyvGUjGtm20jbY\nHNH8MpIj0zweUvsKJaBr/oF1BpFHE6L9ciHaXXkWH1AvaJCNbZiujA5L4R1R\nH7zgyOSNxyxaPDV0ckBmoWTMJfsherum4nibLg7k4/kODq+nsO/N9RfcJ/Xs\npYXUGn5GRHOVulHzqCO8xaGWWln6GSv50t5lDoHlKmFTDi7sMXRGJNt+lKxk\nIn8CZUUDHT5O7xyj5LQyags586kbSZRDK7Plma3xX+jlhZeiRg65vss9w7fc\n8zOq\r\n=xCKM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "b16789230fd45056a7f2fa199bae06c7a1780deb", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.1.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.1.0_1549379208979_0.19561399883518704", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "babel-preset-jest", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a15d01270838aaf9d6b933ce3d24827b9d90c3de", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.2.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-jLEbH9aAe+8Dg0sqIYHbn0mRdSNfzR+VS5bpH4CWK8IPKcn5LzYnocjlIV2CKyXtoCO38k1IZM0WwBj/cx4uUA==", "signatures": [{"sig": "MEUCIQCASf8IC29CvZi6qdRMf1qTzWtcusaT7Uk6dGl0/Z6ZkQIgQyEZboduHZa6ZI77M1hHgdtJGojKM0cBIAZU4N0UOeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovhCRA9TVsSAnZWagAAyygQAIOTnHncejNgcoU50VR2\nkXWd2HrfLvEIl57ypKfiQjIy0x9SPyydoVMu9ADAgbviJ4WpG5fqi+fveoXX\niyfP5+bRbVbtvnOnBOloMOLrUP+Jlq8txX/hHRVIAWGdr8qRds8lND6WX1YX\n4i/vwW/TlOzAcS2amxU/K5Yg0lT9hrv+k4nRl5TucbsN+Nv19O6Y1vLIwDwM\nZe3ejIj9Fqhzc/ClWLt2kSZ4TafS0fan9ccbEMQM/u6rE3Feh+Ase3jCUp/Y\nB3b+eb6m/G6GjvLmlDjqJhHiRcspRzPoIvjg5qf/LpO5FcPgx5YDmVuigw+Y\nkBEWX6pwSRT/1udvRqA/WQTZoca8dqo0XtncJrs2rvQDX31vxEI0Sp/WFtgZ\nNR2qlGJV0pN4W27HxG4DwFjlwDiJ8Gj59/X+7PHwXml8oxavuW7FshBWhmhx\nxMADbQkDl4DJPWMOqy3kXHQ/M8yMWMZaQ4WvKsVJzbD51Vo4jrqaRLNG8vBs\nkwIqIykhPX1bBrFH8eMVq5iwnxL6UxDdTtUfIit8SUVDRKGKLdn5f9H5Kwy6\neCb8YpZBy8mwq7uISuDKAW/0LrStk4cW4NceRK+0UYCEwa9Ax98pV1EWdSjQ\ngr4wIiWqpfNAXsCSPxsUQkhj8JB0VSucG5fN0f7WQCMmDIZGGt2J4BaQ7dip\no3jM\r\n=/c0q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.2.0-alpha.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.2.0-alpha.0_1551797216663_0.16138364867741384", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "babel-preset-jest", "version": "24.3.0", "license": "MIT", "_id": "babel-preset-jest@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db88497e18869f15b24d9c0e547d8e0ab950796d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.3.0.tgz", "fileCount": 4, "integrity": "sha512-VGTV2QYBa/Kn3WCOKdfS31j9qomaXSgJqi65B6o05/1GsJyj9LVhSljM9ro4S+IBGj/ENhNBuH9bpqzztKAQSw==", "signatures": [{"sig": "MEYCIQC0gQKAnMpSVmn5Pix3ZovQxjIW653nFCCQ6pyVZsM6SgIhAKU4No7aFZi3obMA+m8cwkdpb8Y1/uQC8IwX6yCF2Yr6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRW7CRA9TVsSAnZWagAAHUIP/3usNnRuzwbCSrqy/S5R\nH3MP6YlzvXhXBQwWAjG2CKg3OlRjhedy2IdHe3mW2Vio5GqWgA70bQs2rLXG\nDjl2Aln7JvMjRByFG438JpHY7FNLLq7gE8VRQKwk1JpyguqnmX33ICmaLRz8\nLqESiE+0+tlBmph+vEFJkyZ7X95mlFwAx3gzFzgYyJ10EQ9QKhM6T5nB4OPr\nlsJfdpAVGpFsQsi91GwQ8/ag6Y/69Jb3CdhBO1Y8kj5hDnHvIbHL+YGDr1eU\nNBwRsqD+dbG6tdX43w0izD+34RqKRekFEoFoI7OaQRERNBcxx2xw5B/7f6PA\nYwUbST9hR5hE7cgM2+tlHUzCUugQ8idOeqFEoB0Rxq+8q9K74oZJd7+VIq4v\ncKT+N+Dtyet2sb0ADNmuVweI1TtQKuizuADh3sSqU94RU5yPi7RJEFt3c7YF\nWYShsVjdCMYtzQcQLY/Z02fzBg9dWYpvsS3cgjO7YuQlc01hWuLl3sYmaluo\nNiCtxk71nGOX82uGRUEw1AaZXIk7jQdYetyDSeQEfOZUuZjxfZYQMNaddzBN\n1mxp4bm7h8LYX4h6iG16/CEh+YHpSebB5258P9OBqxotXAzfHi7FBFXDZ83t\n16fVx+rju/Lg95LFfT6NKg5n+6tDKUa8/fGhn0mjioOPOXrGt07nLCuXF8j8\n/sNZ\r\n=CSqK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.3.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.3.0_1551963579080_0.5951511537321053", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "babel-preset-jest", "version": "24.6.0", "license": "MIT", "_id": "babel-preset-jest@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66f06136eefce87797539c0d63f1769cc3915984", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.6.0.tgz", "fileCount": 4, "integrity": "sha512-pdZqLEdmy1ZK5kyRUfvBb2IfTPb2BUvIJczlPspS8fWmBQslNNDBqVfh7BW5leOVJMDZKzjD8XEyABTk6gQ5yw==", "signatures": [{"sig": "MEQCIFaVEbP4CeQBaCOP7pIjIaV1Ul7DI/PJDnB7YIzIgKflAiB0TtksMZuowwY6hKJUF+GayJ3FiNhGC9ydWw6e9V0v6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAOCRA9TVsSAnZWagAA2egQAJ/kTPPqN4iFl+R/vZt9\nQbajsg4XX2YL3lBw/nD3JJGsTyOvn3FBPBwHiLI4cJJzDKmD0wZLVZnkSiOc\nr1jDAKcFrFaQwOmqgU2Da6eG4g7+VrCZVyLbV6KBOed6MXk4xhZvxnmNJL1Y\nnqzUpR6wM+ffd4pE29/s7e+dMk2e4G1FIzBwQUU3/DJ69UVudp4NgIYu5FiM\nmkDjnyQ3DGWMBn6hn1a/lZgJtlIymCVS6odF0KLDqU23PlTCPJOmsbkhnWXG\nAvEyIofsBGuFgHkAD45d3YFvxKevB+Y4UnrT/mhL5YQMGrI+rkJnYvfADsQf\nQwLmogVdXEPXuIW7JrGDcpxaaIqzLPSBC7qiJpw5tUBEGPTXQ2gqRNY04EsE\nfQBvsMHlRd74kk1xpAgFAPTb7h22AwIWiTl7OF55SZqbAwNEz0r2lvkwq+yr\nfyYP1OT2P/VpRjFs+2BF/OJ7ClHL5OPxGYkznrmSjXqhXkfk7IdRazS1mpRM\nHw2oeclEChFnHDVxCybebKlJF5mG3cxkLYbHobsr6CMxAIamPZHZfN2OybIR\nQ/ycaafYukIJ9EM476Kijnkkqp1B69qcZ3I6dnNCW/VikIO8qJhgVHERDl0f\nCL4kNL2IX2MNai67KWR5LvkkFdQ24wUucYyQtO5RSA0yAAZdXfutFKH/5KrS\nwQS1\r\n=K8kV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"babel-plugin-jest-hoist": "^24.6.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.6.0_1554157582333_0.3721424320206599", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "babel-preset-jest", "version": "24.9.0", "license": "MIT", "_id": "babel-preset-jest@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "192b521e2217fb1d1f67cf73f70c336650ad3cdc", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-24.9.0.tgz", "fileCount": 4, "integrity": "sha512-izTUuhE4TMfTRPF92fFwD2QfdXaZW08qvWTFCI51V8rW5x00UuPgc3ajRoWofXOuxjfcOM5zzSYsQS3H8KGCAg==", "signatures": [{"sig": "MEUCIHHNiJ2xdTbuyY4fPkWk64QU2awNS8DPXo6tX6NOISdrAiEAyHw/clY7ZWui7QDc5HkgOd+vU5TI4CytVnyxZbj657g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVpCRA9TVsSAnZWagAADggP/jGSNQTFlBD02ERvWf0g\nHeY9Sa0EP+biksKMNFGRa3mdDkr7/ThhjyHpq76L0JckZVUrM3FkgMBJXT8o\n5GaZqeR+mZAZ+hnLKcMKQvacjb4xYL6PJsWSvv1AqOLprZlEvu1r2hzcVo6y\nlDiHb5AqtGphkOF3qsbP6/5em3TDdA1NtU9xucqAclHpPi1y9MacwXV2JNAF\n1ve8ywpxBLcJBs4Tpo7Z0oRnxxYNAnbs8hpF4vQWAGeEF3LLkwDOQSTTmTRu\nLKJsuatavA2GSW2hM28yGrCHr5HDbOFlmQ0SC6ekwpOYfexXhNDqtvdsivk8\nZLVto6h9xddtMePhgDIAWL2DEI3yxaEJWVJprG7u1coTzlBV7ZngkudFiKjW\nOZQlkBJWBfiepOVR+Y+OiilHl5gbg5vAalcyqOxPAn6j6ZVTny+W58vzq5rO\n+a5ULNn5r7WBG+e5/XcGLy+aGzDEs38PKTgX94Cr4czJ+uRDqLHZGDFqKbHL\nxz7m9hsez4muRSLDo6wL+A36UDfQ8TCkjgGd+NvPzqsMnkKqqxMcRYP4UPSL\nYRsfeJpl+Py7mOFZsPFrh2AIZPfWHkzdmB50fpTjiHSpYFu9o4BoALvM187M\nP2fNRu6yExK+TPhGhR2UD+zyUjG3STyQPoYPNHwpX90BqZ8+IBXKD9eOzz2X\nnzVV\r\n=mONT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"babel-plugin-jest-hoist": "^24.9.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_24.9.0_1565934953145_0.68410419931406", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "babel-preset-jest", "version": "25.0.0", "license": "MIT", "_id": "babel-preset-jest@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "087cf31bfc490dd483dd5bf060468eedea84ad30", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.0.0.tgz", "fileCount": 4, "integrity": "sha512-mlgq22WCCEPNzK4hz42lTBCNcOCNEd0Q3xZkSyws+6muTdErgXKFT3AVsjyw1K3WBgu1TVp/HG3GmYAk/pnO1g==", "signatures": [{"sig": "MEYCIQCPm9SsPkRDvr0wwz9KR8AroO8z4emXc46kZXnN5HDyAwIhAN5BI6wti4ikIW5pWxFtyYfBLhAXfneirAr+hXV8Yh9z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrJCRA9TVsSAnZWagAA/24QAJ9pli030bUI0oTGtqBx\nKfPmrSlQtkfpnBu5S3TklWJ07MycRNxl2x60Aj7iaHUnJMHR+7va/CI567bO\n4yrhfEV9FQKVtqBjcd1lAqx1XFzjGZGVy0M1RxBkhunuknsTrYTJoXnHSKw7\nfkTxenN7yQ6z7vZ6i6eXWo4GwiQ1LQ0QIpciw/JnwzrLjdCpyGDDqbTHHzhX\ne0L+51cWXMXfHVvLNO60Xy0MWHLBp3pKZg1uO5C90GU06HNS19x2Gs7TL1M6\n0rv0B5wt6Hbd8mZ/3CpKg8oPbGDK58EGHCvMpds7zA05SXUgMUbYWhyQutzM\nJ8w48uwkml9hCGjTQ/nBLMgCW0qo1A6XriAlkHg3l3Exe3W1e7ixC09Y9wQR\ntCKG/N33cDPvAsGnQypXr9wuQ8r7VCZd5k4HD8+r2W+ti0BIsdOlVKK91EHs\nFL1wy14lZGB9SQ+j6HaRn2dCPBRmKAieaZjwuWuoY+eS0+DOQuLR222BrXZE\npbmUj52ENgnXj0xdJ36mm4pIV5o3ulgpn2oJAUE0P1sVirnbU2TxlvFTdG34\nM3eg7XN5CkOMATusstb4x6ajqFy3lIuxqwwTI8TJsiI8pfxCGbvsajXVElEG\nhXaSI0w/+PzgiVzCN6bVgBGQeRBVq+5SAOl2aVgqWM4k1rsxm8abuKRaoDng\ngoQc\r\n=qTLk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"babel-plugin-jest-hoist": "^25.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.0.0_1566444233077_0.9774373747278475", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "babel-preset-jest", "version": "25.1.0", "license": "MIT", "_id": "babel-preset-jest@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d0aebfebb2177a21cde710996fce8486d34f1d33", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.1.0.tgz", "fileCount": 4, "integrity": "sha512-eCGn64olaqwUMaugXsTtGAM2I0QTahjEtnRu0ql8Ie+gDWAc1N6wqN0k2NilnyTunM69Pad7gJY7LOtwLimoFQ==", "signatures": [{"sig": "MEUCIENkJNO7sZ5qn2mb/rpYXfWs7rowtMly0uW5SmhA93LsAiEAh2Kvslr5tvSLnUxJ4tjRSJVNCenqaZPE0J3gKIutzWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ICRA9TVsSAnZWagAArPwQAIgoVVa+kxELMxmSW2Ab\nPhS/XtZI54vuGSNsuGgOQ7HOz5X2MODLteNqQ+FQCW4RRYyM83Swd233CqmG\nN2g7HO0lP2ksWOvTKf+zGqhGse29d8ntkJS+O7oGcivEi24B/0nul9gXRvLa\n2S1D7Dz2sMyYgAZm71qs+3QcfcBEcNxRUUa2nfr54gpFiYRnmGvjsbxwzYpA\nG8jKqROr+LtooQ/1r/TUTrGC88OWMH1LNI0I/wmy+81MRy1Wif9ni2Vhohhx\nsSqql82ixBnl/3jaqSxmoMjEP1oLyt3cDmy7b70ZrzdFieIbFrHwBuSsxS+a\n/QA5w0skHC+0p18guxmUAJtQYnV9WWS67NgV8M1adg91ph9aQ2ZChD+kYjdz\ndSixASllyKMcdJ3xVEkNSr0fTt6fT2s6jRbF9jgZaWc2YM0UVDNBnWvRO1oi\noBK2pPs9iqOxx/481xbf+XqfOJt99hGK+JcOzdiN4alFQyWC8+qaP7mi5oOw\nL3fOvnCU6+JxvEBzEUn9FMIkFjNiadspyTQtxQYdwXDvJuusqA2wW9f5d2J2\na/CNmIT1zwtPZL3yZjP48tIzu2/uKv6DEPs3Hjqcykm+45um7lfJ0tIgm0Co\nAhLxRtvUNpxTixfjb33qQd2zYTLnZK2anTHoBlLGY3GxtQa0eP7ult1gHlqY\nznin\r\n=FObg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"babel-plugin-jest-hoist": "^25.1.0", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.1.0_1579654792332_0.1500958436135733", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "babel-preset-jest", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "babel-preset-jest@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd5729012e2d1feb236e6b78b7fb873d47e10e8f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.0-alpha.86.tgz", "fileCount": 4, "integrity": "sha512-vUuXXeynbawYinsx1kjRKRvHP8sTrWRkeY+9aoSp9qaLmm/THFT3sj3vzGoL0OqD7IVTFiGAlTXr/1UIhhq8SA==", "signatures": [{"sig": "MEQCIFpLGkFsGvm3D/ZHvX6Wq3eQhUPKwAfSsgMLComsgQHzAiAVBwobbYANjkNRZBiNrinDsvF2JhnP/x23vNXVVhYAGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HoCRA9TVsSAnZWagAAi9gP/0a6Hju05oZx2D+2tBv9\nI0B5hxNBSzKD7aW2yfGbgwTGIKfU2T/J1nTi1QGfgsTSR+J1BDQyV4JW2e+T\nEHhTZL3mw4g2gK6ImUTRJwEGgHVPj5tJ8zSkFORs8isP0ZIq/3OdwouB6KFK\ngAF9/Qb9cQcw89avPUoCDdthl2FfG66FuCotchquLJsmYCcO1d2qko5TCIyz\n1L3DKO/1CDacFXv9Dklk1wj+PEPIQFJAeWw3fi2fe5aXMigHYfSyHxd7e06f\nWdPxjJ1jQVC4XWSsYglDMAuqJdxfuTIyzURVNd5vQyL/LN0qvTbqoDq3TOJo\nSZSdRDvV1EHwodHd2hXciCauxfZ8e4vK7jSJyMe7Yp7tk8Y66nKF5jfcqVf2\nFgjNY27wxlJw5P2MABBNcuLHCW+j61XMgI4GH3z+zhG1mzlYZRB1FOwb7xAN\nRf0WiTq2jWhhBe3p83t3+qM9/GTkvnW5vp37AjN7pQWFBcmYn9yFDHzjPKXF\n5hY5V5isNsRVeUi+ezCBXprmGvnTKgRQg3hebYwcLamKWtEgT9N5kI/fSeDs\n0xT0YWMUQWb5aG4qZqalSzPY1XLnbFfMI3UvZyjjQYhWkSw/bk1if3E9+tQc\nOwDo6LVn3C+X0v3T3JG5f4co40TU0gIwC8ROeuGdHxFvxDF7vKMQo22JFK+q\niQKM\r\n=gKj+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.0-alpha.86+cd98198c9", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.0-alpha.86_1585156584091_0.5715217086851796", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "babel-preset-jest", "version": "25.2.0", "license": "MIT", "_id": "babel-preset-jest@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0e2532ec023d3c0192475f911ff0a952191cdeb", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.0.tgz", "fileCount": 4, "integrity": "sha512-rgqa2neISQ+PT3KyzNTKK51PUuezRUB2AB5SiBidbvme5cVkic5CbWzsRkz7nP6WVqVxsnc6te1F+pHs9rhd7g==", "signatures": [{"sig": "MEUCIAUNrp4FkHvqS5OP8yGTCNqXktvslL4vkCUN2HfDwfTqAiEAy4aJ/CGNCDO3dQZJG2jHFUj9Kk28l2DuRyl4vHOhDQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uwCRA9TVsSAnZWagAAerAP/iz++Vykke2Vw3X4z1Mr\n7f6jKww5urI/cMehwSCNaYjTvXN/JtHw7VjroyZPatY6aS17BJJu2ncooGvj\ntvQYi0YZIs32J76bOd7h7Ej5ofWagsJv07r+fR8/Z7iL+OfxI4Fv3QQMwLm8\nCTka7alksZyCVKRQZMXG5CdIN+VKkCMVb0gWdF3Qht+UomXykDOKqgEplcZS\nFIq21AyaFiy/4hBznNGl+FQ/+mnMsAcJnaZB/2pwMeZ1dQhpT3JkF4BPTAJ6\nezV1rzLF7D2QIa1Wkit82EkqI/wQy9D/z011+/o5GlKxMGzfZ3ufCwTbdpGA\nX+3Y2Z2mmcnaOoq+nUOWPncvxDQBLrHdM7qlOd9IbfCH0CBYB0gzcmC5mRgg\nIFHDXIOcEgYDJ3LCuBKUgDlCXuRYRIEvuHRZbajlD2aowqZCyvcSqDTFAdcc\nMJcldSSXUienFWo+9e8Qlleg8P1PDmkGX/4JoFD92CZ6kL3QawIaw/Cg7bgY\nwMWTtoXoS+6jky9sb1vC4H4mRe6VDfKHPaXZDcmVQbFDYRGeXXKo2uoVJM+7\n4z6IciNTW1aq25bARe4DyJVOlD7K9ESLIcLVLkaa9YKjjCouY20NU2r1DGNE\neaTuBnkXywa/qlZ5gSMZFA5eWt6+u9O0PluU2jGCEOGeayWltPojD6yhtImq\nHWLi\r\n=AOHV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.0", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.0_1585159088036_0.16999609324458298", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "babel-preset-jest", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "babel-preset-jest@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85a84ffd5146ff45b2414fc2c00331575cc911e8", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.1-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-80LbX5g79aPKZulojsLG7ieLIYX5sgGhfnX06SI8DFa93KmhogONdVzuKE29rFCYxx7XYHe97dxnVML+EwXrFQ==", "signatures": [{"sig": "MEYCIQC0t+E32sNH2ig6oYMM8CvXLVT8ER7yuC5FO9gYLBXNiQIhAIwvEqnLq8cgPYIwePaUgmdeBFaFq8JChVWMxHK3Ozsy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+sCRA9TVsSAnZWagAAB/YP/25q4aCr7U90izn7hJNM\nDyVIsTVh88BLbvCUSM1jMizVhah8RI6VlBH1X/lWO68KxNC01UFKq7TBxZuF\nLMqOASxJJ+TkZFSOpZ5jNEh+OBqQ7mk+cKFKALzXigPv71xCIx0Ec8vY+fUq\n9jHus3p7LfCZQFf5k4/s/uwloaa9mYqwdt89nm5yZ23QfzEKBkdTQfVuvMxt\nQkqajIyRpFaHXqqidjoZPKtSR+4ksKv9owK0W0pVWY7ggQaf3e/8gJbb63S9\n+wrAMsgCA81yAs21VdzhBbGRLUaY4C+TQ9XpjvLOygODXFCC5AtGt2473lkN\nNWOibHQ8ij9ae+dLrepCxe4zguauHtjsWWnWeE55Hk4P1JrS+UfdJcBHdutM\ne3kUl++9Y+N2DWoKy2vmPMia/b8NLHmtm1VKC33Z4o72Gek5qlqMnHwuXflQ\nVMCvoPYIdhkz3zUTf/Ao5ZNGrfof7cNe4vdQB3gx2PA0t7LVGUGipda7yL5m\ntl1GT8BHbQdJbbD8M00vEN1eGilz/MjGuX6qCHzXY2hBVF8odJbJNBHgV16L\nmAjIy1g5YLo9fTXS3tScWrHx0UfRUKf5eyesgY3/3xjpyiBcTI8zeh7MaVii\n/EOTis3dMePgWraKShz2qKL3Tt4LlpYZeRAwOleBamzlAptBApqmpSQgBt9R\nTo/2\r\n=R/VV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.1-alpha.1+5cc2ccdac", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.1-alpha.1_1585209260344_0.6869619664543114", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "babel-preset-jest", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "babel-preset-jest@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21592b36af4db1568d77f5000b3d21a892865653", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.1-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-atpNW40hawbH4Usd8IaUp4EKurrKG7ZiWvlh9jMHdFD8w+gY/MEiuOu4cTtjfYyKx5KBR4DpT8gJPZU2Wqcj1g==", "signatures": [{"sig": "MEYCIQCiSN3bGSUZA/2axZznr8W94SK8OmUpO/2Mz+WB5ezP7wIhAIkmUG/syGlDhZPgpt4ocHEJWTQKrwxJW5STPCCTB2iT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2675, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGN2CRA9TVsSAnZWagAAbIgP/i5kJBTzrfBS7anEfU4p\njpFmm+TTJlaoPVSB+kYFY1Oi+zjbtXYSTT16TTcSX4bDdBRyXaGz3k5b2fX+\nlM4YmR+6Ia9RTSS7oa5gw4Rf1mrlqa40zmj6mtexcyfpXT0S123D4NZ0poRI\nV78O6oN2Vgf7/P91uSi0yfBPbBFbIpep1O2yZOLu38VGF2vA5wm/HatLdur7\nfz7DcCBLX7upiKFXuAtpGDHrQNXj60ovvkhj1sJKTkbBDhOhqXEgMmOZMpYs\nszBlyfyQD8jc1Pp0l9K1y23/mlIv81YNqhJR9enQI8HIg8WvB27jJ432w1Eh\n/5igN4lmtlAhp8sPlGii5AHxENlrtfNzWpJbibEzuV5L54DK7K/8NaVAFRxX\nqolPGJIz+W3MNLsJ4/9KQC8jThCuarMN87N6joaRJMLHe7GM7ZcWGweaALKE\nzaH9QG3nyL7g+Si0dRuCFzRYqeD0ejeckh8/tqwHCWgINv6zpmUwo+nAOOGm\nqZxPCOFJaauAsJ4yI0I+5bhP3vG3o5Qt537WoZ0CEHoxnQx/W3tX4/MS2x+7\niCd3M4hCdMtn/2efd8Gdkhfgi3XjV4nG/4UmO5Ub3QG1zm1brJfgJpI4cxNm\nVF/zKzTyDccn/DbZ7DQ3uafPN5+LhkgxzqEEcYulk2rdBlFAIuHpV4E31pie\nv0JJ\r\n=yNNl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.1-alpha.2+79b7ab67c", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.1-alpha.2_1585210229588_0.20880176790037397", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "babel-preset-jest", "version": "25.2.1", "license": "MIT", "_id": "babel-preset-jest@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ccd0e577f69aa11b71806edfe8b25a5c3ac93a2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.1.tgz", "fileCount": 4, "integrity": "sha512-zXHJBM5iR8oEO4cvdF83AQqqJf3tJrXy3x8nfu2Nlqvn4cneg4Ca8M7cQvC5S9BzDDy1O0tZ9iXru9J6E3ym+A==", "signatures": [{"sig": "MEQCIAcSXxjnYr1yUmqk8eJThc/7zKT5Id4wfnRdnFHCzY4nAiBQWLYfX3xvRPE/3Ik69k6mPGQG06BiKE0fq9yUU4cCTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9WCRA9TVsSAnZWagAAnwoQAIgQu5EcRkTVdEfoagj2\nsHnz/yHNgJ6wBC2cWaMdU3TxSnzAYbZmKQcUMW7iV7msbUo/ow43WeajqpWt\nJ/aSwtZLVJw3JGjH0yt//ZafAlk0KwKBtKSA0dqQUHBiypTrHUlYORMdl3kM\nEUnjgKx8eItUUdljW3jN/dLiU5v0nKf5COk6wBpwGidybyMC4tmTz1uRvdvH\nWzpgaxmPlpjzPidb6xMZm3uMMWPVJ0bPeav+Y2BUgDwO+dYufGa/OJ57Y1fq\nsB7onrIDKIAfMKNx9+Ja7q0PvqvMC5e3u/H7hxdCbdeOq7/kHqDxBM6AZDUM\n9Icik6Ls7s+j2pxQMFFBZFxHk97e1w7wlXWXoISzSTfMJjUmxuRa/xuHllXv\nHS8EByEuqlkPKkHoDdDqnqojv9oM21PbAfQEIVjBe8D+w54ur29wjfVm/rzU\nKuDrT15HUg3i4T5xIPO/crB9u6fnJ/dAi8XRgm5klnlB5ddGnkUnv9ENsdjl\nq4/Z1/yJN8ptIjS3ojToEb8O1G2czzrGFoLT31mAEFHOetTPw4rfmPQmyVoC\nDd7qUwFYJOSpWl2KxYWBDYJPR0CpuFsvmQrofAeF2wVgHGqCm+CmsjJn40JN\no0NMRT+xFrAxDKP9owsId68i3EwrVSTF385LOkV9oUmH+1FiOMsPYmAoJ4h3\nNWwm\r\n=3aig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.1", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.1_1585213269587_0.07883459771847856", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "babel-preset-jest", "version": "25.2.6", "license": "MIT", "_id": "babel-preset-jest@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d3f7c99e2a8508d61775c9d68506d143b7f71b5", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.2.6.tgz", "fileCount": 4, "integrity": "sha512-Xh2eEAwaLY9+SyMt/xmGZDnXTW/7pSaBPG0EMo7EuhvosFKVWYB6CqwYD31DaEQuoTL090oDZ0FEqygffGRaSQ==", "signatures": [{"sig": "MEUCIQCTEWcjxQwGZvI9HUc3oJLgNrPVg+hwt/HsWN1tavncgAIgWvr3JfdyJU5m4BnRuEH+ynsYhdlTWZ2ffyK2Hl5nfCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb59CRA9TVsSAnZWagAA2xcP/2Ce1OHpzj5RMQOppcFy\nyV4hhyEqMm09K8E6G6TwenXdagcPxWSAGcVVoQ/28Lek0PmWiEQrWLJkH9RV\n7fhfxBM2BEi4Qare18rQwVhrCtV4YbD1FGHVhKMz7AYr8jbsWnyMImP32VJG\n0EHJOY0B4jfDLJIfJod1aMcPGR/HXZKQthDjCxOSbNbRXXUoWnkG5mv3aZIL\nJCQb6jkkN+YoMrgHgE4hLYCUwwKjQJY1K9XvuIoDuYqswpcrKPHJHKPQF5QV\n/4xMWEharloCfjx/asaunHhQpfK5TKniF7htddRCFhhZqFLXI5KfKR5XNlmd\nc215NcWxAhM6AKE6TMXHtnHHwT/1jz1XvUROga7POLE1VnVJPxLdbYOZTs/0\nP/mtaukib7srC92V02I5y7uHddiKsNUgfuNUfO94Jd53MFyEvzkc7eYC7Bfv\niXJBUSAADu/n84e0PunnjO3CMktr+kZf1rZWatxeH9qNesfzYZZkw/jZHt4T\nJQUqU5Bf99DdOrUQUwF9qVQZ9bXR8x8kn4th3BQQni0NrCY2aD1Os0M/OTVb\nOW7i5jU9TzN+tah8TuRZaHsH8cshwGBWFgsYn7aV5C/D2OJI6Z1Wv+Vuo21T\nSFO9mL6CwGrCT76+uQcWYwF7JHrdDjT6y8p+cgpn8uQUawbQBiYuxYN2aFTN\nSjQv\r\n=nWQF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.6", "@babel/plugin-syntax-bigint": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.2.6_1585823356740_0.6489643365205426", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "babel-preset-jest", "version": "25.3.0", "license": "MIT", "_id": "babel-preset-jest@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9ab40aee52a19bdc52b8b1ec2403d5914ac3d86b", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.3.0.tgz", "fileCount": 4, "integrity": "sha512-tjdvLKNMwDI9r+QWz9sZUQGTq1dpoxjUqFUpEasAc7MOtHg9XuLT2fx0udFG+k1nvMV0WvHHVAN7VmCZ+1Zxbw==", "signatures": [{"sig": "MEQCID1ZJA18g8cc8/aQIheA1OfdlNgJfdkVq1hYHewXiPetAiA1vTK7Eg/dENhNWFTRi/Of4ylj9hGZDwGcPreQKoaqLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc+6CRA9TVsSAnZWagAAaVkP/2nlz5OmLpn531n3iIH9\nY5xWq34TPC8aJDKZxBx1eS3Xwhi7ejGCdRqibbLv2x/txbR3hLhwLC/Wde7P\nEEQcKqoTyp7XxzAtPj5zbI7YMaOqWzSvqFPJhFn1MV1s/IVIV0KT1GCjYsle\nGmRC7NV8SYYGtYrZ7ay/DUxn6mxcHWldjpjlTRJBUGQYCijA1d93ll1Sf9/B\n7d2MXJjeqeDg816YHC7CC3zHV8C3fiz8nVRCJedAwWSVlNgvci+hHGKX6A3D\nREkG6pCSMfTdkRLjs0PpJG4gnN0hWNBfnaOWcFlVrAtUxxlmXlCXqbdiZtKE\nw98etwQ71d3YXAaVTIkUKpaqN1s21/CEEq24Ml9j9mM0u1rKK9QKJyFnLkZm\nzbCCjV/po5UygthujHZBrubawH4O3xDszmqpnWu0IFjY0/axIwIiQv2v5Cwp\njEljGK5M6SoRX1BGZVWsweqx8x1/UH7CFqI9zP/r64wgQI7X+I4d7fwpWGwW\n+fvB4/RX1IslokTzLiflIkhhY0v/fgJOj9ic60VP3LRdgz557gpwyJ86/USZ\ndHGCXjpziQZ+3q05ZNsx2XJecbWeRR62Re3FAd1ip+Rab4MxuE96I7R0sGcV\n4gAff9xKA93KqxVAci83CKT5N2+U4qlVfdYJq24+Tjmx2tFrkXkJ2L01XuIK\nfEZA\r\n=AUhm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"babel-plugin-jest-hoist": "^25.2.6", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.3.0_1586352058320_0.30549798822955787", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "babel-preset-jest", "version": "25.4.0", "license": "MIT", "_id": "babel-preset-jest@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "10037cc32b751b994b260964629e49dc479abf4c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.4.0.tgz", "fileCount": 4, "integrity": "sha512-PwFiEWflHdu3JCeTr0Pb9NcHHE34qWFnPQRVPvqQITx4CsDCzs6o05923I10XvLvn9nNsRHuiVgB72wG/90ZHQ==", "signatures": [{"sig": "MEUCIBFXpUYjA0NSP49j4cAloaq8goWEqlAJ4aqBsOPvypV9AiEAky7NR4J58FbvBFb9rqZhxlSj9FSGI80GcMipReW+nwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMefCRA9TVsSAnZWagAASKkP/3yIhnpu1JzYdNf/ooRv\nteDP8WsFkICMZwINQOSw6GQcltKx8vsyBvUDFbMzsu9TzV/9Avfe74ttx3ju\noiIWpJTDwNDGcv4xRMGA/wV7+oEDsCGVG8nzysKX+V7LsBrsW2+O9R16N6cB\ncxg0R/OpCFCqi3txwaKZJGf0S9jUP28IN5qmm6y4U+QxhAYynscGcQBSb9fO\n3a0eOFjBo6x5gZj09P8wYVl8vwgAeZgc/Rjs2sHItOFZDoqzJyW9v+UGiVkb\nX3942Ewh5hbWviiINBvsQZKFtCX/h1tgou75f/SVOsI9/F/Ww8gVYuuArk2P\n6x7im4RowBY+dUTg6cLseMMZyIvTjh1iJdV5OzSYwZkPuxocnJpCSBdKldR8\ntwzHZ3f6tWeZQq9Gt30kssNmS2/oiKX+h2qMc7h5kKe2zvyJs2uLJKgBlgtc\nZFWYJ9/HGNuX+4k85wfBqvSoV4TwsTdN4Mi22OWnj5esgWTn9tOKCll/f6Ow\nPox2msYBtHz4Js/s7KlzGjGHkafoW017StJMGvEbOrv24FwieYXZ1flJ7pXV\nY3HRFmuEcKfS5s8n2k3oQLFxvPtwsxLkr+vX3mtOWf+Df4+DO/30RGnPyoGb\nMfm9HHWE/uPtzPmzln4ENbeRJPTW9T02n6++KceF02KsoCD23cQ0cje4zB0l\nffiv\r\n=4ByI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"babel-plugin-jest-hoist": "^25.4.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.4.0_1587333022643_0.8002255776960823", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "babel-preset-jest", "version": "25.5.0", "license": "MIT", "_id": "babel-preset-jest@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1d7f191829487a907764c65307faa0e66590b49", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-25.5.0.tgz", "fileCount": 4, "integrity": "sha512-8ZczygctQkBU+63DtSOKGh7tFL0CeCuz+1ieud9lJ1WPQ9O6A1a/r+LGn6Y705PA6whHQ3T1XuB/PmpfNYf8Fw==", "signatures": [{"sig": "MEUCICP7MBfqutA4Pd2aHQhskf/cu2yxUKox3jseoVKippcfAiEAjo3xZCSgGjkZjkIKguWE08NUGdUPGWPPFUYE31JvT9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfQCRA9TVsSAnZWagAAVeEP/j2TXhcYb1TuenJrK3v+\nAHB2D8kTUEjLSV6CsBEaKgp3WBW5Gb39BZJvgAINylhglSj8b8ibANLLkNws\nM7emW7r69PzRos/ZANinINqrFTnTtq8eOkOgh4mPPgna8TjN9JbO4QiJFWiR\ngtuPGdMWzAMwz10y1Z77yHg4zBVuHWPZ/0oY9VOcDcHAKrwxbFgEwyh2W6GP\nLdzt9Rq+EiXcclj5WQ03Z4to2JbSDkRvTXNPYT6KskobNb8RqwgYMMB9yLuZ\nf9lvV+Wna17lxsZzJrFcxh7zRa7WvJdC0/6yc6ygtBvj98Sm6f2uJEXK1NKm\nttGntfb0l9hY+Zeb4IVdlnC/6cIrmvSXBnrqk3gdSsCyEFzuHPMYOa0fvu+r\nG3HSj4r+njBDPZ3pArA27uhXqNUhSwu3IvvgyrbTG2dctGlQ2Zj8An7pMj0Y\nkUgcuTHLOLkRaJHLPZ5faQG+TD4ujv8J7o1JKuaCzAHi0YE0Ge/ErDOtcRkn\nb7MEQBXfggMbT5CDhTUspihli9o+VGYLQOAIX1FG3uAWNgNhFDj4HvHQEoJX\n35IOOr5roxAHMufAbZEDPpc86c7c+WgttODumjYp6v4u4mrT/ix+d274R+8h\nvl6O02QWqQocHvNc+aK7H5V2jSWJwajO510ygqxoEuCEx4fAHh6+EJlfonv1\nT3dX\r\n=g2YW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"babel-plugin-jest-hoist": "^25.5.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_25.5.0_1588103120090_0.24475667736657059", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "babel-preset-jest", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d347ebb61f1f55e11bcdb0bab545910db9b854db", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-cOaQ1q9XTOH1vXu3KWv0wMDv/Q7A6zljYBvP5f8GWY9xVSiNY6sr71PCtFgUeT1ocn4LGVzQoZ9emLQv7n4/9w==", "signatures": [{"sig": "MEQCIBHJTb35TdDWw5KtSjWryESngyGgrlYwGd9RCGKdt5auAiBmnJd4H/DliwUqxmz+Xv1LRb8CUzxfdoQ72yNnhrP2tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPLCRA9TVsSAnZWagAAbPgP/3M9Vs/pFfYWTdl92+/q\njuDL2ZAAcY12mYhD+AavYSs1mJ3gbRkk+N9mo36ZyTGVprnZeajzSnHZKmVo\nltNfjrSS4Q+HByAUL5s0VCUjk8SZKmGCBw9XtFhRerOEXqaJPFbInmqz2zQe\nDQBEH5L88S2mGL6Spj31bFXAFvx2pRTgWS6bCOC/2z5JuSyILEMaGCiDFyHF\n+wj3pFL+J7Jqt/YSBxEwgcfrA2SDweJW+SGddrtmLB37ADfKpGhXb2Qq3uSI\nVKaePjOi7WTyYMx0LTkYp6FXkZrohQoJeoSaBnojkkDaBiccWTGp4/hzgoAf\nCg65DSf5D8CHtQUZQmiMYTYc2LKW8wCjEbsu8GTpLAQhbYbBuH/IdHbB6mV6\n7HxK9OvJVWfvSgcTbX/CBY1KHEIFrMrQgZjxojH/6B5QUPriHx0t+7TTiz/s\nEo5UP0smGsuS0FKz432odM8z/4p0u0/Lnwi6e8mT+aF2F9fDiU352zD6rklr\ni3D2T5wgRA7IPadainVg6aUmOoGzyMTCASA8pF1hQ3PNECjrbBlL0zIftt5d\nvcBCSMGC01IFZvFH6c/ofIiylLfv8wx4z+wpsp0FyrrEq/JcHRb3kCwdFlrS\n30mZ+nUF49qjTjJK5vP90QnpwPRJmcGP5R/I7bDp5svLqBTZJu7PKq8yaaLA\nyt6T\r\n=duKD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"babel-plugin-jest-hoist": "^26.0.0-alpha.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.0.0-alpha.0_1588421579102_0.3844856402853831", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "babel-preset-jest", "version": "26.0.0", "license": "MIT", "_id": "babel-preset-jest@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1eac82f513ad36c4db2e9263d7c485c825b1faa6", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-9ce+DatAa31DpR4Uir8g4Ahxs5K4W4L8refzt+qHWQANb6LhGcAEfIFgLUwk67oya2cCUd6t4eUMtO/z64ocNw==", "signatures": [{"sig": "MEUCIFG2oskIK+Aqyh/T8N0ccE4sTVddeE7SXgUy5MAjqeKfAiEAj21l+snZiIX1hHq1jUahuhYf7z2DwCW/wXl6ag4F+mM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaACRA9TVsSAnZWagAA8+MP/1J1FkQFZAsHyBfsJv0H\n0tgj4paAVOvdJPrmK+I2uSSiKwQdEtytdlKwoB40d8KoNDemr9eiTP5HPb32\nt5p0xlfvvDyQI/t4d/IyMiuTwATitcX1SUBaEGUB0+DTlhWbXvI0f4y0C7Po\nrNNgqhvxpjecg0ekrPGhShgiFlJyvPGOB6H220RjfKnGadRlPb3xcf2xs1ND\nLpUEgr34lIwiVl4JikUEE1BAWmESSQpacLPl03N/j2FcaGwm6GSUnuu7LHwn\n826XLJ6nAJbT0f8qgGsXU4H+FQ922WBRFoE5uO/tWsJU4mKM4o9cmFlQ0um5\n2YFIODgditJBe48Lneg+hSrTh1Dfok/0vPFzuO2NZ+ycYr535+Nabn2qR2zC\nk9WlByCo0iNG8F0I3PySwPySu012rF4WJZwJkGW3Nbhd+ID+BijYG7bjjWU4\n69qiySmzMzAwWwfUsmN2PKeWsNyvmW/ywTgy9NOj2rA8OvW+GQsrbLdmgQnz\nJebcWXusFwX6OMXZh8Q7OeL+a0NX2U2BhWYA0iJdjXqzC7ogkAD+eA1Tm33/\noCZsHHVY7PWyQl8J+h/5GtF7UzeCjPS6s6M31vyjrQ9n9y2jJsp/Oumjr5Y+\napTj0ADACUoYYwrkC30SyJjMhbJKQdX7w/YmYffQOELOg7wEaWdFEGpuGCaU\nEGWv\r\n=6cdx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"babel-plugin-jest-hoist": "^26.0.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.0.0_1588614784527_0.5319114238602565", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "babel-preset-jest", "version": "26.1.0", "license": "MIT", "_id": "babel-preset-jest@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "612f714e5b457394acfd863793c564cbcdb7d1c1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.1.0.tgz", "fileCount": 4, "integrity": "sha512-na9qCqFksknlEj5iSdw1ehMVR06LCCTkZLGKeEtxDDdhg8xpUF09m29Kvh1pRbZ07h7AQ5ttLYUwpXL4tO6w7w==", "signatures": [{"sig": "MEUCIFzxmnqCjASUIJmBOaUYpkOOSVcWLLe1+j344VYR4sKuAiEAnujLk+/qsrOvyioU4lpT0lc9OIvd/629TfmLR8CLz30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx9CRA9TVsSAnZWagAAlVYP/A3zpfP1AVPGpJbexbU6\ntXm9jj4C0WJSL+01903Gh1WVVQPEh64XpXGv5s/0GnmRC61NotS0dGFkG1lC\nkEW+pGBn3hHFrVdgaoBy5EDetJ9ddEXJxAZDWkg7Fn13nVCoeDXEuUyQ344L\nHw0tIZlohp4w39tJ6FVuZFYoe59DYhxXDSROuJmaNDqWnzrFpAKm4UOFKsuX\nr/7nQ72P6lGtfPnKKTMSgG0Dn+WvqMgnStFLdzfqk7+0yF4rN2REot8I0MGG\nDR95b2v/dzdO9wsl39ui98/7LdgT9sow4aubv7OMru7L3+E5NtkKN5TvTbfk\nGdCIlayVBXek4N4RibjcnN4EEWmTAl+RQtYx4hMamp+lPijftUw+vrm9Ah0q\niVevEeImAyD8euajEFjdzCj1yQRKfpaJSp08IBlNLylATqwgsmn/Bkv4vUAY\nz5+umTiZ20RvJm3NyHXqzd+FVFSIvTGRfv/U1M33CHqGOg5g4ZPENlXTXdIF\nsIklJYuxIfBNJNLrlkcLMD1zdBNmXIHnPsVjE4+ZprSCrnWc8rBrLliaxGdJ\n2a+JWQBG8YmJWTs1i4jtCTO6d8JAlsrZgTD9DSn/ai89ZeqtuB97bTl6Ly3u\nK+/fa6G54I/TDdabU0QBhtt6bhhSz9yPLCqEu9RbLxEuMDCnKHXzYAg2x9ES\ngwKE\r\n=T/nI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"babel-plugin-jest-hoist": "^26.1.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.1.0_1592925309389_0.7663964129724878", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "babel-preset-jest", "version": "26.2.0", "license": "MIT", "_id": "babel-preset-jest@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f198201a4e543a43eb40bc481e19736e095fd3e0", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.2.0.tgz", "fileCount": 4, "integrity": "sha512-R1k8kdP3R9phYQugXeNnK/nvCGlBzG4m3EoIIukC80GXb6wCv2XiwPhK6K9MAkQcMszWBYvl2Wm+yigyXFQqXg==", "signatures": [{"sig": "MEUCIGLi7tvcxRpzmtLrz6IySEtw3H0UDq5Wy9hu5/zp/JKnAiEAmXBMrREjg2Co34rJJ9wyJaMBf5foXD6oOP9lD66tGgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzlCRA9TVsSAnZWagAAcDQP/RGEXVJP7xfdy36Nx2t9\nQzbpVsJZxjFiWwNonavsp7idlPXYQILByMJra/YGo/NoOlV/S/ydO/UhdINh\noyhj22JkOacwondYqjaZF385/Y8vktNoWH691Qqmh5/0K7OhLaiq8eWQr7Za\nNnFXx8gsKLYCErR7yWS0dbXGJM77nQzem3TJGxZHHxp4D2BoGXUdrvGLuCin\nEFInRIynS6UdS9IH4e5P98rNv0bTtH1uMmQ5MANC4gQGbJ6TRTqyGS2XTQO9\nheXa7s7yooXhHr9yFu/C2UVs73WZJGoKjUo1MlaSu347RdcklfbozIQUQmZA\nncbiDliuXTbUxlmItcHHCOvorCn+0HyBPY1wwWmUjcc8wV7/VT25Ujqj5eRs\nwBZ80EB7UfLb7OL/fYjDtq+JxcmEteQxQrzXVEbq97V3rNxVDsOWvlG/70NN\nJ2G2WQI4NNJUGX/kDRqtuAdEsh48i548s5YfXS3kmvu5Wb4P/3Pps8M1sOfO\nR7Uir4UUuNiTfOFGGhr5pRhoMYJMgRpo5KOxRlIk6wCK9TUh+svTpESqyr4J\n5XzzwQAhcItwcjHzFwRpF1t6E/tnE3+Zy9qvEP7LrGoTwjd60YOa/zQ5Wc1z\n3Ni9lXB39wL6WQc2I5uY7EcOmKikA7/t6BbUZXEpzrDTwME3axWJGwmFl8zr\nk7lh\r\n=KTnv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"babel-plugin-jest-hoist": "^26.2.0", "babel-preset-current-node-syntax": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.2.0_1596103908922_0.9602339723776365", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "babel-preset-jest", "version": "26.3.0", "license": "MIT", "_id": "babel-preset-jest@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed6344506225c065fd8a0b53e191986f74890776", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.3.0.tgz", "fileCount": 4, "integrity": "sha512-5WPdf7nyYi2/eRxCbVrE1kKCWxgWY4RsPEbdJWFm7QsesFGqjdkyLeu1zRkwM1cxK6EPIlNd6d2AxLk7J+t4pw==", "signatures": [{"sig": "MEUCIAI3pGmRyESB4QfUIP7jJaxdkCoiTO3P3MikSp+OYU2IAiEAn9Lw3bOnURzEc1fGnbosdtp76Kl1+vZAKADLfh2pZn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAdCRA9TVsSAnZWagAAXLAP/jmVnk0G9eFmDDeeMvTD\nWQgZELL6cVd3oLK1xd7Xc0U0ryOY9bSjrXYK/ZZx/aR5z/NFmWiKgLa/w/d6\n69j88zuRGk8jWPGZx2znTLfNwisWSoNl9IxBQqKG+iCRlVGrdkuMKBWR927Q\n6YKIBmRCO4qsocyfpRKLrGYGjOmwQvk+T9Boi2ZHXYHu3kHNAB6Em0/6GAlk\nm4R0Er7ioAxcUQKV6ChB92xuW1BaXQ9PYP02ifkAAcS7ZCYQ9upA9EqG9cJG\n0aT7ajBFazHbI8kJ8OMNrWjQTz35F09KMI9vo4U8xo9CjR/7gLnEqJn7TYRC\nhFPO/QsXuyNRC+C2M67y0NGrL6oBqbqxzX5BnSrbZ1o/YeeUy41SL4jofBWj\nMFADflmGJivKVizfbhw8c43RfaUCnsZQHRqzu28dl9654mTsYmJCIsOuQdoF\nekB1VEgRZpnM/HMld3SylMoWrNThYfRl4YI+H0EcKdrGWMSugUU3O745A/7Q\ne7sgdgYMawdqfvTZFLF+CLQgVFWw4cXcwQ5ZMNTP1QesnOKqo3MkKJhYYMRE\n6WNeVfCYGb7qeRajXmj1yYV9cAaeQdx4hWLgw3Tly8k0Z71ksgzJxfgO3XXd\nPEwARJpTY35sqB/c+0tqtSD6+bNYq799SvdwKH/FLzvoPKtE87qqC1imRxz2\nBDq7\r\n=/8m9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"babel-plugin-jest-hoist": "^26.2.0", "babel-preset-current-node-syntax": "^0.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.3.0_1597059101461_0.2614152398032039", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "babel-preset-jest", "version": "26.5.0", "license": "MIT", "_id": "babel-preset-jest@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f1b166045cd21437d1188d29f7fba470d5bdb0e7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.5.0.tgz", "fileCount": 4, "integrity": "sha512-F2vTluljhqkiGSJGBg/jOruA8vIIIL11YrxRcO7nviNTMbbofPSHwnm8mgP7d/wS7wRSexRoI6X1A6T74d4LQA==", "signatures": [{"sig": "MEUCIDAxioT26uGddwPXshuxYUeUmPWZel4dEE87T2Z1suThAiEA2fCX697PyETiyVN23aSEmm4Co3r157C39Zk57IPE1D4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucuCRA9TVsSAnZWagAAFiAP/Aug/ed5zgUKmvvJs92c\nCLrc/Ec8k/I9PNxpRyg4td17JJCsvLVzYZ2DTYry9qGAI6YukLreW95gyTFz\nICWF2CbNoQ2UPuA9f2xsb4C3jvIQI/MoXIOlQAc4MOvlsh/PSsshru/bSrn9\nWjzOofgiWE4QiNmT9vqqewgu60cZA7R9C6Fyu8hfrONm079ByrJWSuil1038\n7U2yMc4kPKUSvO+Q3Zwo3Zc4spoOiA7EhtrmWw5yMTnYctA0N4gVKKL5IZeU\ncbyZr4KFbi+dRUjVWJRou00HovilXAiKhtvfBSzeLNTTw/8oxO+MkdYx7CH4\nVNkC71oK8t/bjSlthOzfqZwScR3topZ+cchSGdUl2t38OlBWpydG2ZDvtyLg\nUCd7uILXd5EFygw5XWbGIGv3zMOF9of3z4l/bklYGW09abvVZbxJ84oJ6ObF\nFTZcI1gT5o2G3ykqQpIQrqazO5M25LaFZrlTgG6gkSK5ONtLzu/kvUzkU3kP\nBmtk79uiV+3disUpYbFuh4vGV8wGNmGm2sAIYUZA+oyPcjEu42FPsoz2Mcy4\nmNQiECAQ06Pd7CNCu4spxXATkK901K0IdR6uMZ/hBmSu7Z6AiAqVl2U7wDNd\n4w3Jd4Q1l9jda0h2kEe4Jldfq5nkCGlsf8UklNWB0mbqKXcMNNN1xakJuU8Q\nuLy2\r\n=cNJc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"babel-plugin-jest-hoist": "^26.5.0", "babel-preset-current-node-syntax": "^0.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.5.0_1601890093664_0.4360252889266827", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "babel-preset-jest", "version": "26.6.2", "license": "MIT", "_id": "babel-preset-jest@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "747872b1171df032252426586881d62d31798fee", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-26.6.2.tgz", "fileCount": 4, "integrity": "sha512-YvdtlVm9t3k777c5NPQIv6cxFFFapys25HiUmuSgHwIZhfifweR5c5Sf5nwE3MAbfu327CYSvps8Yx6ANLyleQ==", "signatures": [{"sig": "MEYCIQDB+QS2+WMfPo7wAI4UsTT0oVzv6a+BNwIvFGuGJQnC0QIhAKVJyW6qL0FQm6A3lJWRI1qRrFUqHIMp0nV6H/9/y7HD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADKCRA9TVsSAnZWagAAsLUP/2AzVoYAtZqgengrk1eu\n9AgJAXZ1MGGN45Dc5Lcr1BsLrCm1Xe/ZFNclKUSemoME45G5zcK9NsksV/fz\nsOt4rO8UOJhCKa4Tzzpi2hj+6r+Wy2pX22RykRBj1Ye2aFmHuX5f8JKLpp0S\nVONMnKZSHnwxZGVxsddC+fTRmXHs5OZ1VbaSKVPnIUTBayENwfEdFC6cF85S\nZ1zQV5vFujjvAd4DYjLjY+n7gCrWXty5idZ6kBtcVTGYUartlUTBsycdW2OP\n/iLVCeQymIDpUSoSmvXWyiRBcXqGUjwsZguPPExWdY9bZRQVEJdFkc+LRo5S\n981ZnHJrAgSVDCz1iXOJgbXTJc1HTyxsZynqy7AiCWdZmrfSeGhWhw5EDH2h\nJqTYTHB1W4CiZ/AsOCyuKfmj9nL7bOihO5e+0kIq2J+VDoA57Po0ikHD1Arj\nUG8S+FEer+6I/QWpJJBOH7Fe73F2NPYgBx00ULxzbo/8cC6G4L3+aKHJNXPG\nz7DtlgXFHdsApNk5q8E1GtYdEtWWTRCWNmb8NOes1WrFKS0moRs0RzH84qy7\nvFrQFkDXPW5gZnjifsIPlP0hxOTTeqo4I5ryYxKZU85IW7wFC9S94LOnnaI6\ntsHbeF6coVb/E8Wylu6Q63uxCrrhMuUMEJB6n1aMk4bFS/uz14qixi0EWeG2\npHOx\r\n=g4JL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"babel-plugin-jest-hoist": "^26.6.2", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_26.6.2_1604321482048_0.7900511233499983", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "babel-preset-jest", "version": "27.0.0-next.0", "license": "MIT", "_id": "babel-preset-jest@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63f14fb91e0b92b68be07be30a7c74395e6cc38d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-IBwrt4KpbgfPaxFEzgKlj75bOYCH4RfB71fLsJ4BMUW8jO/602Lck3HmbMNEzf//lQ2vvVlQcj8XdF3QvfSzLw==", "signatures": [{"sig": "MEUCIEUpL++wbYCBaJH6/I8DQa+hiD6zbyjCALt+hU/UrW6RAiEAlnbokKWbV/UHqneda9kj6As3Cr3TXinfJuMr7uIpVx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8J8CRA9TVsSAnZWagAAFJAP/3M5z2t/poxaSEDZETkE\neJpaTw56q0X/NTVqj7FCDK6cV7DsXaGkbB+CP2FN8k1ivXVLQS1qUspO1rJv\nfb659wod5zRclzvusC/eV1/avsQsdnR0EEqAkTumd8jD4nEWYfDoYQmhkFRB\nJSE/HKz3yNTtFlflY/1w8vw5aS8u9XbGMgrjQ1M8yUb7+lBj0M5aGiIpl1Y2\nLakHm2o6eX97RMEAE+dJj2YL6o6VbH7GLJIArEhNrLhwaCvwcNRSkDOu4Dvg\nYH+fazEYhVn1B9qAIC42rpZkZt/Fmu22QLQX/NwDh32WP6F+aoOz8yrT9FJE\nxhGawZd6O3L4z6KK/WYYI5syAG7I76ihPIl1XrclYw1XTen+1oInNN8+y9nk\nyM1LKO8/ixFM/F4cuRuCm84gWUoL1w/WW1rJZobaZGAt6ElPnzdEGSbORkUK\nkDfY7e1HG3otrbarQ/ap+x3zh/doz3FX3eCPQqUdT+NEujTzyLAvvpmIqxqc\nsnV85w/dUd03i5YPYj+LiMp0dsyJEQHVhivBodhHjAWNBOTKkoN7luC85lcA\n5kc+zlqJEkiLcigtpTTkuYwKVKJkx42x17tY2BVhP4HE6337f6LrCc7dKUTT\nULs8Kfmy4K89BDJHNyM4atI/sEpt81UZi8d/OLMQq6Tz8qVeK9vwxshUqVCi\nzJ3H\r\n=VhOn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"babel-plugin-jest-hoist": "^27.0.0-next.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.0.0-next.0_1607189115704_0.19925786702887494", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "babel-preset-jest", "version": "27.0.0-next.3", "license": "MIT", "_id": "babel-preset-jest@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2c696e2e048f7bf5597f9602790b36373beb8c28", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.0.0-next.3.tgz", "fileCount": 4, "integrity": "sha512-1WoguNHYXSFCVvPqfjf+/J7q+OLohJzkT3vyGOACBlpjwE0b7yixW+M9O66U958/8n3UmpHXuC+cSR5p49XkcQ==", "signatures": [{"sig": "MEQCIHeh0jCzvUbwxDmKDqhvNVSYRojyetjkUcHrTPh3troDAiB2h2reDv2Aj7GD5BAbC+yGqEbzG2DrZmrcLxsoHhzYWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWtCRA9TVsSAnZWagAADtIP/3dfQY1oBj6spKk/QSNX\niGxRmHn5C89in29wQYPCZp1Px4hxYOuKOoExztsOj6J1JszBjBVWG+0vd82W\nUMSRUKTudzDtDFz6tjeuPFG9Z3QLQSJ7XrAVNsIXHXcdWhfqx8npg0qxYqvm\nfJXX+VaPzaZ3RrK8WCrWh11N9xK0ZsFv5Psme50K6s1mJzJiXZYt8ohrfl/m\nUABDXaIZEAmpw2La18TlxwAEVApr7u77MzoG0HbYulqtODTJGpbXDpcQ7L0M\nW+SBceTns5WSyoq5TMlKY5873YLyQopJGrbCJU8Am65ugdpnZsgC5tfFk0St\nFyfSXlRTbZA7JAl360eiJZTwrqMabq0M4IS6jalfN9w3y2D02Kjj2TJcTelp\nz2ztRw64vSXvgVztvwtc9G9mSeVFAC8v6W8UO3leT/GoKp1OGBzymBZG6sGc\nYMy0ba9jyRwUIttkazNd9tkLEirfEmS8uMI/JBHNuvuG4iJMhiTd48K9Hjlp\nYQ/68yMTy4I4HHUifSqFP3gftSbqiL0zZR0aYxDMsQumZ7lt1dW7P8/bjuLG\nLhIqIiVwDcsPqQwwHa9j+HshK+nwmEtuovh/H2RFSoDgHwvL39fxq8PyS1Pq\nsUk1vcmiRZsnqxdFbryUoVUAW6aJLbKqI4uIlwgANMhPSNNhoMQCsxcxt+6D\n7HSE\r\n=Cghs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"babel-plugin-jest-hoist": "^27.0.0-next.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.0.0-next.3_1613686189368_0.007316106977572456", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "babel-preset-jest", "version": "27.0.0-next.10", "license": "MIT", "_id": "babel-preset-jest@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f55cfc8fc054e1352ae333b3446e3f4a6cfefc51", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.0.0-next.10.tgz", "fileCount": 4, "integrity": "sha512-rrHt8Tjiv2NiSSWnl64kz44Fe9AG+Bm6u79ZZ8WB25ne6w2Ajyggu+KNqulrWru5ARUKFJ35Kh1M8ytH4MACKA==", "signatures": [{"sig": "MEUCIHctioNi6brCe75ogytgFwQ8MGgbiHIJTEkv3r/LfPrFAiEAibIfBW7R3pIAKkNLHXLvzeo5K3GbJImsgxhaADaEb6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4ECRA9TVsSAnZWagAA7HkP/22HkuoL11gCNFpw15Kt\nMFWrDxCbSQ2GQdHEXZes1M3MQb1VbqvYlik8wLpdQmQMxhd7WTNomwitVh/R\nVvclu34jDcSp6TdUp4O14RFCDsuhnO+NaJHGVRnrmd8lpR27WOVVvX0A6bUa\ni0iqJZndyWVGoruILil3yrpMUBEmV8/eFZNh2xVEdypwfcOxQIpzq5wA86St\ndEUXMX/HHPFlppBGDLl0LKz2gHZA3DHHNiANkeY6Kj3Et/K8oGcvaT9Uqfqn\nBOLPxzqv/QZG1FOEjLGTJkHsRXCyfFfceK6GNkmH6VswCiqeE6PWbCZ35uzH\nDjFP6IZAeqM04VvJYlW6QUSWwMSAVrQU41TvR5k8tGlf2Q1uvacKEHL91H5d\npcLlL6O2dMqQ+Qu0xePqxuS2keDu+jikqAN+mMu8yd5VjrmHQ1A6uMMul/ck\nyiyP4UUZ3tz7C63kDgYGZZH24RD5GR0CH54KELOaR2fHkF+CTit7h2nmfwdI\n0wLlFkONv9tnHsjeLoMHBeAwV1X5qsv85OvZk18/rEkhbYc4kI8sn2IxTwht\nwJ2vCyIBFdrs6zjI6PJ22pcAHZ1kGBw3eADyfSZvVn1Rk6tEdpBOU4jnPMnu\nWacCvjNLElNDyMccmHkhS9sCPqFRNFNWZ4SKSxXXfCa6K2rOT+uvDTH9sQAW\nfBwt\r\n=GG7A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"babel-plugin-jest-hoist": "^27.0.0-next.10", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.0.0-next.10_1621519875791_0.23509471497049095", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "babel-preset-jest", "version": "27.0.1", "license": "MIT", "_id": "babel-preset-jest@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a50c75d16647c23a2cf5158d5bb9eb206b10e20", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-nIBIqCEpuiyhvjQs2mVNwTxQQa2xk70p9Dd/0obQGBf8FBzbnI8QhQKzLsWMN2i6q+5B0OcWDtrboBX5gmOLyA==", "signatures": [{"sig": "MEQCIG7xrMVvMD4f1YlmYS8hRJtBbBpWtKZrN8FB4AZdbNCOAiBWeMB7rHvGT2GZH08i2UZtvuDTLr3xUDTjHR6E1IfY+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwmCRA9TVsSAnZWagAADn8P/iOlsS5CmF58kVHPUq3o\nMd64a3Jvemv81+ehuipn7eF4qwfKsUJUf7IfWMhCkbKpIY/CGr7wPgdvAJHO\nLrbW5Te5d4/zhXJzMzA7cyVi5BH2QuXnmpCRIzm6/6CnNt13EwuSGSXQXxIi\nysfsnm9us43lGZPVrgU29fY7gtFGEcsb8zO9ozVHj5rgwRfUW6yLb422dzGd\nEgZOWeX5kbLRztZjYmye8/MSANZS9Mq2f1LxFKjaDYFnZY/bMbJJL8Nl6t5Y\ncInmVpzQ1oMtBUF6zkyjis54yjkW2g02pA64Pr1RmRL2/ruHZYSHpxqDMeF9\n7XEq8dDb53qIzGZhihR6Cx/foMrrGnrh93NE/AJ5ZQRzu0YTXIf1/NSBKP1C\nCPwG+UrZMV8CeoiNtJoXDvYrl4z/TQcoa4GXfqWlgcZaLdzsvrUJmfcNqCtQ\nAredldQF6HdSllCqAOzOcR/J2j2SQP+BlOupIR9do08Vmby7k5aoboQHFAom\nOL5VhrN9eWIjfaey7fpgXabW6/+Yng+sIkKQi8qRc8CQXG7LmGeI5oh9MKg9\n1XqvsDANcONplBBFm2+CyNJrm3rqAzZEL4GyXE8sa1UV95UJ1AdXSk18FxNx\nMsbQXR/XB74D/AE0LmP06EuWduyzICOfOmDB9ysGCnJhyvDfOTKJf4q+8UpJ\nicq6\r\n=8Ba4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"babel-plugin-jest-hoist": "^27.0.1", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.0.1_1621937189563_0.13386005472473217", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "babel-preset-jest", "version": "27.0.6", "license": "MIT", "_id": "babel-preset-jest@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "909ef08e9f24a4679768be2f60a3df0856843f9d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-WObA0/Biw2LrVVwZkF/2GqbOdzhKD6Fkdwhoy9ASIrOWr/zodcSpQh72JOkEn6NWyjmnPDjNSqaGN4KnpKzhXw==", "signatures": [{"sig": "MEQCIB0U6XxVKThZuE6+3/Qz9fCOdtgnrdkPkDvnpBOE3szSAiBzOjAQ/vQJvpMksP/lUrT4mi2J9KAep2lhrUjfncmTkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFhCRA9TVsSAnZWagAAKa0P+QGqrXoGw3mSBZTCI6SV\nPoTLGW3bWzh8dBIo9uyGKw5NSYw4yr0bsQEA5viKVTjnM7+d4RisilLNyN7v\n0psKnyyQ7st3rLmSaaMRHGi331yLhlk/iv6Q443ygjuHZv4y8RGQmW1Gwt8w\nz+SEgTp1n9DY9TvyqrZ0Mw1SsHogt4rl19pbwzONmk4Irk8IPY4Oa3cC3JXN\n/nwzPIcQjPRXUiO1izFvG0SEvog9NO17AdZ0z02/qnmwsA64GZoS9DCiIII+\nS0UGZNECRTL2ohHVEACAlyPhVfzOXHJ7UzxOLjv4owrzSuvNpFta1NrX5xb0\nWyUutzlSENAl+8YCdtfPZMe+Zc6TldpBPJNpVOtVsANp8I5iiQK9dl4T70Su\npkLkTe04ac3vmxlN4qDrIHMNqkpDq2c82MnIMjJWk7rbl+2Qyu8gOw0vL3i5\nFOVpdRd8rM8Q2LAZXIOweweaBm3jd06G33P5pQYikRqwSDU9Zdwok3Uc4GNC\ngbQ1Z1cbPg9pco4uCNDPx6jvlSbnOvexTpoPUlO7VG+uF/AFj/OT5ItqMZSg\npL+Mi80mK+dRZMFKl+6CYGpGlLhsUwmqgXbjxLCkBNwr92UY9ybSKLKGEpGj\ngdQtxBLUsHVcnWwG9XLmefHHpbd7oze82ZLxodbeSM5G19zR/WBiX8LgQukY\nJsAq\r\n=GeOc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"babel-plugin-jest-hoist": "^27.0.6", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.0.6_1624899937460_0.6999610240304648", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "babel-preset-jest", "version": "27.2.0", "license": "MIT", "_id": "babel-preset-jest@27.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "556bbbf340608fed5670ab0ea0c8ef2449fba885", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.2.0.tgz", "fileCount": 4, "integrity": "sha512-z7MgQ3peBwN5L5aCqBKnF6iqdlvZvFUQynEhu0J+X9nHLU72jO3iY331lcYrg+AssJ8q7xsv5/3AICzVmJ/wvg==", "signatures": [{"sig": "MEUCIHAGhXYGjiQIDv5SP6bMgtOW9+kT8iM479wZzDRhMkdIAiEAnx8S06n1H4RMsqjdE9XakNP5X2Ha6bpMR3ZEslbv+mM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaRCRA9TVsSAnZWagAAyc4P/A9QDh+8JNrjlX74I1iz\nn8B/mE+juunXRC6yiV1RWaRA4Ls8GHX2M9tVUIMMAAYDM9yrf6qWbR1EU2ax\nYWAf9dEU6pK4Qvu+3V13dBXRa6qEIONErPrJghoaMhJOdJRTUzrZvBDun5vr\n5wi9QZJae4CqWiloUDh/pYeBgxBxPzEfU+UtWAUTNu0/91tA86SbtUZVqs/Q\nYjh6KgI0m0bpBGoF2DsL0zPB2g2v5U55o3GhIbfu8pzGgIogVPhkcPrPGUxV\n92r67hoUQlyDYzeuZQl/Jx01sUMt/ZR1uUe3g7if3Xm8nidyvm3pcCHfaaet\nZdgBVE7tHyXF4+WNeahIPBd3hhZ5++qy8iT5Z23adDa0ORXnFeipfi8oL3Ku\n4IPKfCqZepshlVFO/XVivmQZ9xme3jGlkMoPFett+wbCJPNbDAccp/SZx/3L\nbiG61Tw8SQsfjEDS73D0Xy1VG25QNJrcukz7oaWO9cmFbKd2JwZyoCLGJaUj\njYyFw6MwIHn08RDGg3atC6IOBJolI3YBi3HrK+Bi1+CTso0QKeokd0lwYdUh\nATEyE0EMguo8IzLjQCZIH1LweVB2NFKDWE26RRqqT8oCztgGQ1uyup/xDFUy\nm3Niw1Ao4wgJ22veqNnJFhsmlw+WC4VpRwrIYlDVnmoPtcDgVR9hic/4v84i\nsGfN\r\n=MxtE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"babel-plugin-jest-hoist": "^27.2.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.2.0_1631520401831_0.9914108980617928", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "babel-preset-jest", "version": "27.4.0", "license": "MIT", "_id": "babel-preset-jest@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70d0e676a282ccb200fbabd7f415db5fdf393bca", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-NK4jGYpnBvNxcGo7/ZpZJr51jCGT+3bwwpVIDY2oNfTxJJldRtB4VAcYdgp1loDE50ODuTu+yBjpMAswv5tlpg==", "signatures": [{"sig": "MEQCIFOda2qt8W0bPhwI5cot4SwGiRySPF/vNy0NbTrhMI0oAiB3+tk8j/86rMbfpVUzv/WNYV5X/9NUWZKdzaGvTBv4OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd/CRA9TVsSAnZWagAADRwP/2L7O+Sao2IQyPgBR8ih\n1VK3HFt3R3IHhhZ3ve0SBzCYewHWLWUEfqVXIH+QMLbVoSdTPYND+fgkOo16\nfehcbhL4umbIwzDrBilAr9m/9ZLK/hdEjLeY/UNlMcpTbaK3AWbdJZh1HmBs\n/NSh/KUk1QjcFEkmNqiv1ar08Wi2NmPj6jZhrKCwup8OGj6poX5HMiv+YrBQ\n/Qurs0LRcLx+gbe0jAsnO/0DjT/IjveCXSuUZaW3nbxPnG237wbNOstLW4AE\nDh+5lj3I6xsSkoOQx52Ttaqk7JhAaAB7Rp1NoLGmmRtoCkuOR/J/XRSegOKf\nfk7y7Kp9gJP2DRgXF7p/0oWGgU+q27ZSEK0nFBzVs/MTwa22DQtnlDbUsawS\nFNe0KEwSHKZEPRx828dZLynPZDPsi0qooQUOXPrdY7LOE215B4U+XOXjztlA\n5FWPiip5NYOKaFKpqo8XACoijAnPLA+WatPyqNSi6HhSPMe/sBhfo8VVyINq\npy1bUC6LqFd1kAuwJzcKZ1kPumyYHhtmindiuI2huPTboipfdrMkui5euYiz\n8pd/d7zWrLCwGSj5pFH3F7ZwWTs7L2yu2uNTmWIE4VBMhVYOOm5WwkR1Kmn4\n7RZxDElFwYEaxV7cRE9g7VURNQxfENFA1nwkKHUIS8Q0Tn6G0PvUDUms4m7Y\nHXb3\r\n=GA3q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"babel-plugin-jest-hoist": "^27.4.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.4.0_1638193023120_0.2063143700557608", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "babel-preset-jest", "version": "27.5.0", "license": "MIT", "_id": "babel-preset-jest@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e308711c3d2ff1f45cf5d9a23646e37b621fc9f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-7bfu1cJBlgK/nKfTvMlElzA3jpi6GzDWX3fntnyP2cQSzoi/KUz6ewGlcb3PSRYZGyv+uPnVHY0Im3JbsViqgA==", "signatures": [{"sig": "MEUCIGYI9dxD8OgJQ8EOeC4XU6OfM7jhXIaCe2Y81tD9lkYQAiEA+an+qjg+fhYsbLWqukZIvzSBAkNt3al/N2mRZ6ybLTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp6CRA9TVsSAnZWagAAz2kP/i98xNjLSznr+E71PpIt\nqBELMfINJ6E7Et9Mul4OIRu/Dt/CPbKSw5DA1pHvZ9RbGzlEK2nCld0igC5M\nFzRKK4LpDrJWp34qJzAUiMh8Y36PsX6bvdz4Iz93BpqNmX5niEQXSP1t8dYn\nhg40NwDAbSWJO4Cw3Rrfv35+XHemEaxkrROYqIHPhPBjovRCD6SoYqWtZ1hi\nwBpiL4/Lwy0Q37zNnpKsCcKJM2BBKLVqtGE9EmOGFqOz6J7wSl2qvV0tuH6k\ncskDNelQQHk6yjGXTHn6So2q74lr1mEvl3BT3tPvX4zUl4f38o/mzz1xN3Yz\nMN4BorrEaB5XAy88h5lESSS9u8gtXbhEcOkmd9Ev0z/RWYipeD9KkAwbS71V\ndUOM2tTtKsXB0oZJzn7P2ElvcoSfPu770WNVWM05WLyYmiaIWi9spjk/fzP2\nCi6gZa+iiW1cHtPGiFi2EOUhsvFTxkoTbmufd0TmptlkFtlSs82RDgcjSak8\ngq2mne3KOmDfJHR650LGxFEJqIjiKYlp+gCcUywCcqmcQ0Cmah098B6HdSYn\nUDDtosgPf+5bEfurb9VhzbDczBgorYQZik3/SkWzlBfH7Wynes1XyRBfYDc1\niV4eDl6MWa5JpcA1SWiY++339OUBUPYymflg1dP/PENIowZNMrB3hoEJuLLf\nInMS\r\n=DWFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"babel-plugin-jest-hoist": "^27.5.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.5.0_1644055162760_0.5535699311414972", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "babel-preset-jest", "version": "27.5.1", "license": "MIT", "_id": "babel-preset-jest@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "91f10f58034cb7989cb4f962b69fa6eef6a6bc81", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-Nptf2FzlPCWYuJg41HBqXVT8ym6bXOevuCTbhxlUpjwtysGaIWFvDEjp4y+G7fl13FgOdjs7P/DmErqH7da0Ag==", "signatures": [{"sig": "MEUCIQCm4ynZ+rAIpiI+ZKn5S2IMN6PuNt/n3TmBFI3GLFUDaQIgL+Cr42pdaMoAhSUec/Xs958Emk4+JDq73UN6eLrC258=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktfCRA9TVsSAnZWagAAjc4P/2/V/m2g0qXKYqw9wFi2\nFglZWJhVdkwASNnHmVWZIloYtJKss7nRhQBnasabxJdJ8ntwLuWziq7G19l0\nDX8xGdv5qCbU4cr64cXbrmjkIopKrt5CBBgG3K3MDKhP9+JO4cXtRQ8FxuOZ\n+NnL2Par6mOI2UvVOTPZ4BewWlPwFsESHVcuAqaHXBnGnIPAJaqpBwjdf5Rh\nJH6zlosq058+N9rDGsH2zqc6OGGGzgbwM0Ua9o+mSoBnaPS9B+whixNbqn1g\ntG0vXZwzkS0PjPbq7vgP2m1RV2KgcmAPR3T2/YVkrJH3p/xQwlVB3gxIFE7b\nyyqVi7SsAuxD8a5illkXKU2u76PVU+6RbckAQLNeQL4fhUiUzd0t29kfj4eL\nE+nERVmTeXs6quoTTrFfi97MKEiWlXc6TiYuVRzn+miTAVC2v+cYfFoR8iwP\nhgGRsxiPQO5m1lFA9gvND019Wh2J7S/KOP/QC8wRLk4lH1BxAp4dt9r9uxZp\n6BKOq3+6u9UVzI1Tji2kP26rNQAvJufj9OXW3xG0QID17GihMDFRzBeVNpSq\n8OuNEqgE4PI9A11P/2iSB/2OBiTVgDQzH7DhaP7NAbqyJZXBmp+GYor45jZA\nvEGheIn2+LJAq1ArIupORZEdfFNVBy5TZA0l4Ox9mzy248vMy3bBxi/7LpWc\nGnWv\r\n=D3W9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"babel-plugin-jest-hoist": "^27.5.1", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_27.5.1_1644317535188_0.6408919543196236", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "babel-preset-jest", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9dc49704fd2e5247940c7dabeef307391bf069f", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-LprAB+COpnZfM4+NMBI/Wwd+WwC4cpSqU0c51fHHcoY/6gSVWvPXaG7mjQ8Az56E2NH02au0AziErRKk6UZdAA==", "signatures": [{"sig": "MEUCIQDwYUwRxjXK24Mc3VsxPM2NEvJkEulykRznjp4grWEQKgIgSGYp7eYYFvMZ72dRELyBWKVRONj+8VLpkrpzzT5kzms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa7CRA9TVsSAnZWagAAiccP/1pdz3Jj1EJR5vOCuYQy\neD1+TeDB+7EXEB3ArR8hIY++RmqhVHSzdk5ovPTaUuT4bOVaAbBLReukG+kx\ntiBhQ5PJN7z3Pyw1iSLBSviX8+xbGChTiNrI0a5i25PbDsTfNOo8Cvh0C6ag\n5hz12qvKIYcfIAC/KrMiIOTqC3pSHttLq+HkxFpwD8eNoR44xtNiZXmecVVS\nIejWMHdZisL3cY4IA2Yc0RoJ2xEG2MA/U5ZzDSZeKW3QgbEntbM7TCHz6ORH\n0Y/vallXxN+haVnPWdtObQhUloYILb0H4zpbAy6Hxjcp8VFoom8LXMB0+9gV\nBgDCzC5CEuV5hMs1TbKSN9Lt5ejM3dpy/M58mHGUWngRfsbQTRyHiZuEtiH7\nicJzuq8ccxjKslViiiz/AmFv3Hk2T4XOH3TfwEdt9K1gVEKnC0IM3mXIx4B2\ndZPoFNlpr3KpiNgtJZiHMB4Pf0C+DCBGG1CMckZQekAFXZfNz9JoxPSo0nDL\nz6Nq00OC+WGQEQ/vj3luDeI9KUQzjjo5XxAbIfc2vThZxE76KB6N2iNM69rM\n1hs90VLi8mUFMXj0c2qgnNQ4/97426HGxfAnhtGyjT9bQ2H2tENrylRPTejg\nsqaHEaKfGzr2EsacWUSBNOUlKQs+0+urlYXmYAJTkZTGQrtXL6CiMojy7fAg\n4Fy1\r\n=u4m0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0-alpha.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0-alpha.0_1644517050689_0.5904403640615647", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "babel-preset-jest", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "babel-preset-jest@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f897aeca60312054a2da3017fc836383bbda615e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-SijEwxgIVe597XTMeVZco9AHEG8jKHw8kLPpdrssVp9uhZvSBiYcspxEInaxBL7tFIwNVpXIhAnpIdmxvAhfkA==", "signatures": [{"sig": "MEQCIC7ifbEUmscgEK5ukbKvv93hQpbnml4DjoaRBZdlwMh4AiA17vjxsZepCLGfiEpKBS4ZnscFPI4fu+S2GYFjrKz3jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSqA//XD5rAT6PoxuHzEGZaeLIwkEgvLfM2H5hzUaCxqDzFRHZgenO\r\nKd0Sn00GzWvFHlAQZkdiUyu59dZmi/hkjDY1W0wgCsoh8dioXU6eHh3JKhno\r\nVBmP082QizxhshW1d+hd4rPdk17uRLmxau1VmeBSy+deQaZschRtEm2f8XJI\r\nyb6YNhQ0B8kFdD0JLmuzpe85Fh0GCTOzFkj3/64Bo1F6zdVXKGCS1VidvsJp\r\nD/lWkEM2Q/28IV4cT1sjYxiLUzYo5Dk6vuiyyQTlUOnpsEpyIKbXso1D6jJk\r\nqHpUG2G9alFKBfJqChG/dVknrnOmZzUXsvNd3rfjcEaZQWdfkf95B62HJn/n\r\noPjnT08laF1P61JWkjlSvVEeNg+orsryTjgyKgs3WlY4NMcvh1/fcEz5J11k\r\nH+RWUAtsK4Wjd1sH1i3/mfGcu1zDwZoDkhxGw+xmM8zn07qIe3/Hh3CC7CDF\r\nddWZ6HgGz/TNcF7xPnq8YU/vCdm98trnlFLAbv9BQpj99wwc768NmSHSk4TT\r\n6YRbWbYpsMuLzWiIlawf5ROECABA3v0Zwg8CdXovtNpm39AvFm9P54ul4S6X\r\nN0x1BlWlHHLniDR0QvKUOyJYp0Scg872o4pFVlUDaRQzgr9/3H4jIajex8ti\r\n4JUe01LjpfTe8G3JJQ+IebI3fPKupEWlYbk=\r\n=r0TX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0-alpha.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0-alpha.3_1645112541166_0.6179600088030675", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "babel-preset-jest", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "babel-preset-jest@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2420cb87a8a6bfe13103725cc8aaf0f68a1e2f28", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-OTU3rutAhYpuTIACgPcHonFlfRH/OwW4A10LqQPr32vCmUh8hDqNeHtCgFbAXY/ay2/jfOp4jPWvTBPlmXDVbQ==", "signatures": [{"sig": "MEYCIQCaQY4RwSI29yxNcIjmnfqVWWPy6UNdHYDeWl/MX+yNJAIhAOflotempux6z8EPy+DVsgijllKQr5vgE7c8g1JA1aCr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNODACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZvxAAo4Eomg4QIWdz04nQzYhGmyDCrAlHOIFxVq8itiKBiml8asfJ\r\nh6CZ/LwfR3IITZ2ux4G3fwzNgYEUn/UkEdnV8dnvPHc72ASbcbp/O6Ml/Pkt\r\n2hh/NYikas/bSL+CInMBjbmKFR2Lsru7cXaetnPLFD20PYSwRKhf2KDhHF8K\r\nqjKWEKeUezHAFa5zK0IM7Pi0h+Y8b1Bs1AvQdd1nF4R2KZ1LL/iYh8Hqj9wR\r\nF64Q8V7h6ZXykqCF5DTs0FHweUOpYqXkczv55VhKIgUgHyr9WSvOSFtQKEcJ\r\n8T7qtupOwDlJsfUEPq4JKu4itGkl3zD0wc8IkBrrh8PpFgLIiTL96s8l0yOs\r\nC6D4ZlXjbO5q7naloqT4+dJD7pZOC5A4H2uF1YcT4Waro9He27LWbVQEUq+x\r\nI9s1PuPq5JwH9ixiH9VnnA6CMMlR/ozU2vD5tLKhkCz8kNxh0dOKVI7sUsm/\r\n23/2l6IvpLQm6diE8RhM+H+gztNVoziqp/gu7nOrkQMsgiWstZJVSuK0u6+a\r\nSa+nuzh6PON4ssRSsZitTdMBxpxKrkd64AcDxEoMu+3DtTNmu3MyEXttQFcs\r\nu4Sd8zkBPLf7/RRpoWclQ+g7H+XT1HQBCDt44S8Eb4Dl9VHV6TLlhre7jtEQ\r\n/u8WSAymBsWlf1U0sztnn6mUphoVmHL6FhU=\r\n=db9u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0-alpha.4", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0-alpha.4_1645532035536_0.48325612999920975", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "babel-preset-jest", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "babel-preset-jest@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c40e6bd3ab2215b42ff7f28d85e7980358eb9624", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-hrZsFqduyeXyJffn/SV4Ha/hVyNYrd2/6dbr02bAg4O0m7Mzi8ZMktm9uxQ2ma0naFxbh2CemCRfr23a1BUgqQ==", "signatures": [{"sig": "MEQCIB5sCrEq0t7cszFQYGq9RRMMhdThgi/doOUMhVhWcne4AiAs414nqiK1ZChGgua8dER9TUaU95SIt4pHongP7AZRGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqlsg/+NCk84Ua5qZmA5hr5ghsJ0PGlaclPgXbD+argiHzb0oCsDC+s\r\nNF7JQSn2HVvi/lpUgxK0iFR5EfEPtyNxIXc8JwNx65PK5O0yW6VR3Qfo+v8j\r\nvz9ZJO8CoICYOg84qAAzWz2tlAg8JSPJHSQUlKejyX7Q9J0o3hEi9Jn5w7Kn\r\nGKnhZgGIoURbPVk4f/hYGUt5DUBy6seJw3WcN9GnnthTwEYcJEBDy8CZZ5hb\r\nvCLvxN6xZRTWL7sAQn0fWoC3qlAyZeuUkVPNU5tHxWtN1840SHyhlaockCbV\r\n5eculv3PZAJAZLE0Ld2rFPFjPllORc/+wmdBhZs1gKV2M48RdTbBiHYmDs3w\r\n8pYWgw9Ucl1XVW8k6Y25akf5kbW3ELCSOKb7pw9KTB9JQrg8vMeHmASh1IrB\r\n8HZCjCFd/19rBB350OFPp1Fh+uqd/tyFmi3quyVLKCukj61+655lgecS2HbQ\r\nj2E5oMO4h3DbCSLjzx5YnvojKO7lx4oxxnOZx0LmrxbACpaYGvZh03kEplDk\r\nYwnqKsQFxlA1IohCxY8FChGM2VbYYquF3eNyDDLoUIfrZ6Jz0FSyv0FSV2e8\r\nJDDd/oMuIxk2lju3wjQmPSZ8qL7lPFxEDj1fMfnZ4Ys6NaiWOSpBquZvrFGs\r\nW0MjbN3w2Iuy94fFu4SqQfH5VCkB55ePbm4=\r\n=6lzb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0-alpha.6", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0-alpha.6_1646123543852_0.1239413755780201", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "babel-preset-jest", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "babel-preset-jest@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5e98b5efd5a754a618b7042ce170b656f20d58b1", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-xcQN291UhBj7PA83vJ3waHfq/rzI3KVmFKYE0BeIHtJMebu5goWcNDiQlLMH+o64yzi3LCns2dGTiBxjN7vmmA==", "signatures": [{"sig": "MEUCIQD2QSQET8m5wzVCdm2wynIJO3tpoA/3AE1WqqgtXCVhVwIge8wKFlch55Fx4wc/gQGMDy62hL3QUq59oAAyifR4Q5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5JRAAnIjgtkC0k/T8U6wKQ0S7lk4kKNB7FC4Lqke9X/h0YpX3JF33\r\nOF2gnbHSLB+OtMHRHT9ZAjJmw0kJDbiqH7qBQGFmAV3uSoIAozMgTm9wQoxT\r\nTREd7onTvsfgE4bN6c2LySqC9Jol/lZddbMDR1YVyY3/lt2gcpFKovVaSBJq\r\nrKZl6qX/pa0GLNADE2DQt0ITGcyIu6k9XwV+5TPmWI7pioXcyx8NawPAGqvX\r\ndLJRt94j4XP4gzXdMUK2PGMRwPHFFZk4n3cFoZrvR4YF+MBuzh/O3b24hzb6\r\nyDcLj5WNRrA+fcxIIx2JkZGz6GkKXHc+VYezj5i3MXm0x06KeeRG2BEQzD0D\r\nASqiX7tp3yTm/Kc8s9Spa8NaIfU8VGpnKxawaPS4NNEW4ecA5Podgai73BiA\r\n8RWQxYNAGqLq2j2kvmRvMguxs9xGYm0fosX0jQUnWLmwRtlHoxMTgW1UM83J\r\nUwaE7ou6/hlPRwevhmc21CzoHbLJqgVKiHfVezYIOy6VP8XUAhHAe8EJ4NUn\r\nniLJQIzRMn/6a5BZK7rp4iUO1Lip4VfhQANPTQZwcjMQIQg3sbnFK8wZdHjS\r\nioQKsBGS+th0FvwWaGcJYZ/hattxfeU69mHMH4NrV9D8qYPR05ho/A1MRsDg\r\nojioNoWJ6f2yTgSAD5QK6WvqtxPFTx7w7yc=\r\n=NrBK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0-alpha.9", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0-alpha.9_1650365954986_0.0038515739352482647", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "babel-preset-jest", "version": "28.0.0", "license": "MIT", "_id": "babel-preset-jest@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e7c5f4097b63a3e9edb8f59b8030eca6901771c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-JLyjfCmqCWS3tXUw86ei5fQwuwn34slNBPTluNbhoqHVI1Cbw6MsmvgEl54jPjbyzkmA6XAHJTg3EGNY7rnr4A==", "signatures": [{"sig": "MEUCIQC24o+m7BtbF+PkFwEZRQXu/HCt8l3JyIrd82cnPC5zrAIgEAACPqWodJEK+O/J16L4ww8KubgdEoUHRhYB/tpzt5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3KA/7BkMe/UtMR3texoynSyqNanokzzCdhpvMYhxImKNIR5tLqbIs\r\nmnN1vByLEa84NWbN+sHbfH1RIH7chH/BTZ7y86rDWKRdtUG+m0+ZEp0fBj7B\r\nEwYg2hnGOZUuT3U4MIqp0IseZziGP9R2MNt+qf93wo13GOoUAQi909SBAPkh\r\nwsSZ1a6BednykPK+7e7zit8hxX1STveYmZTAjqZuyb+FLIqTFCBn6Uvj9Y9a\r\n69pOD+drbKr+zfWPd0mKJFqJtmL02Sh+mJTVNsDCGZAckVQTkoVHUSTcCyvy\r\nK67/WQBtIsTi40RUJHz9t77hBV+bNT+BhQJL7ySWJA6XguKqXjoP26iujgCc\r\nGZvauq7ESU804EjycX8cTvzJgu3EY5ZID0W/Mj8ryvgaaIEJaFsO6AbIqN2j\r\nFrkzir7jxsrAlF4tyn5l/KrKppeS6b6xnAVLd5tI3dnzf0CMPdzjbaoKoJsD\r\nrHl9usPShD6MGpZW6zeUBRWnC2UYBJCkJOksc+nATRUtGrblAhkgIehs43ov\r\nE4Ndl/glDgjKj2joElNG0dpU9B+sFSaOiMSgWMzyFw62RGuCIrCoFs6D8mw9\r\n+OvC1WC5Uu3uPQ+SVX6e062GzPKlGTKvCVU2vvOEXYR8Wr/JkhsVLQQ6573B\r\nPPPYMQ/UET+E8jkHY19wxP1mdUzZ4bw+5uE=\r\n=a9om\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"babel-plugin-jest-hoist": "^28.0.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.0_1650888486584_0.46647971513982234", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "babel-preset-jest", "version": "28.0.2", "license": "MIT", "_id": "babel-preset-jest@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d8210fe4e46c1017e9fa13d7794b166e93aa9f89", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-sYzXIdgIXXroJTFeB3S6sNDWtlJ2dllCdTEsnZ65ACrMojj3hVNFRmnJ1HZtomGi+Be7aqpY/HJ92fr8OhKVkQ==", "signatures": [{"sig": "MEUCIHlqFMAgvd3yWhvCeWbY/DwnuUYRcA5xdrhpLBCdD8doAiEAiJnesv+AIxjichN7SiJ1YT/BVhBFidm3MAelrjMK31k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfqg//ZTPAR8L5yRKrur9zXJJ+oJ634D1+l9C/wR+PTKYuwVQ1YqUY\r\nno7sJO1RpAiV/JQ9VyeD/U7j91zJWLvVxUOweV33pYR6AzZUe+hTFfst+8Lf\r\nKfzp+DDeV/81362qHUyRor8XvkQQJzrWKN+QYk+zth3534VF/Pd4TUXtm+lF\r\nZr3JnomAVMmQrq7m+WXSXL4LwchinB2yxkLbVzt5X9xrFWkc13Q+Wyqnz+z8\r\nm9SW6ZmX+UxVRpbrXp+gOPj3D1lNpro8helidJqb0pdtNUH1p54qg4efYOTo\r\nSpXTyKe2Aj6nWfcuydbKkPmdwBDJ+MvcW01aWPPqVMOE7sn4/x1PDhRnlZYu\r\nv6YY60AHOnHHs5fRI9nRnt2lDPGgBPJHBASOJJB7N21RdPmrz1c7aguPqest\r\nZ6JT98SXJjbL6G3Bx0Wr0YBOooCIx+Z7Tci3CchCcbStNA8uhaUcYDffcKs3\r\nj0GMbE9uYJbcv4ofSHgP8cJo3S8FrG8xfHXjuYW85y0PsWvub67PYOGY0XqQ\r\nGbZImQZY7xl3z8ZAbkZjJuM9UZSAzzpm4cdSn7UB7ww2qpy0Da+zdksfkzCa\r\n1IBBokDcDrLmQ8ngDjdjRU4U5wW0FwHs914J8YgobQESj9bx9O8Z1WqQ/RU9\r\nn/W4xEAJl3i3vOvMonNm38qIFAQDpHb0K6k=\r\n=Rnm2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"babel-plugin-jest-hoist": "^28.0.2", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.0.2_1651045441636_0.12756445400093064", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "babel-preset-jest", "version": "28.1.1", "license": "MIT", "_id": "babel-preset-jest@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b6e5e69f963eb2d70f739c607b8f723c0ee75e4", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.1.1.tgz", "fileCount": 4, "integrity": "sha512-FCq9Oud0ReTeWtcneYf/48981aTfXYuB9gbU4rBNNJVBSQ6ssv7E6v/qvbBxtOWwZFXjLZwpg+W3q7J6vhH25g==", "signatures": [{"sig": "MEUCIQCmMOOi4lirElbg5XHJRvoMWJ2IPSWC3/st8I+sFefRLgIgOTKTRG3kxQpnpSzWK4aszlby1V3MxnVXpLsgjwlsg0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuugACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo+w/+OUowKI5rPTx9jJPH0M4aAL//S9DS90mbFtMqOpCPDt7NAILF\r\nFixHUYICyzgj3QPDbbpLF5LxcjwMVkDjv7SXkHMD73gLrjn+rg/ywGChAWzI\r\n4AXHggBwvq6zeplx+fxqONEOYU/xfI1AW6kOfAEiyy/+Q5G7iqHmf9vmYB79\r\nKPg1r3QcTfrGMWSCszlM6WVnXBM7w8uaHBqiWcF7+3qQj4ZCJ0OuWB62oc5a\r\nfgR7+tOgSiey+1y51FH4WwiAHUvIUjVmGDEbm9TSm8spFpeKqiUxmrry9zTQ\r\nU2UBN5hEAb0tq8N2YSEEHDqQoUDeUa1Y0UUpppVMK+balhOvoOM6Agl2VCeA\r\nEXPJztNBmzIDLvHHKBADnzmfGr5z70f1bm668TxwRhAsRAB5/shnH57PJtBL\r\noLmz516pLZ5kp1lFbmVTuPnC5KFE53vd+oJtFVg8ppVO6cjrX7Iw/cKbRkMh\r\nB2NNOSmA2gznS9LN68Rs6qbcFOOiV3IWdq60jJ7QGwx1ddoB4laJNnNJ+kpX\r\n2rNRQ7lISazC1ELlhsdqRMaQUhvQzRSFHXQjuPbtqQsoh8DgHgTLtWiVimft\r\nYOwhLmxEqP9oL46n29+NC+3uSt440J01g/n0ex284uNMQ8NW7mFXZhCDMyN4\r\nCWY7ePnEF1w50RqV5XKE0lxwnJfmZ6VFEcI=\r\n=Gs0w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"babel-plugin-jest-hoist": "^28.1.1", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.1.1_1654582176534_0.7099865871412447", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "babel-preset-jest", "version": "28.1.3", "license": "MIT", "_id": "babel-preset-jest@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5dfc20b99abed5db994406c2b9ab94c73aaa419d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.1.3.tgz", "fileCount": 4, "integrity": "sha512-L+fupJvlWAHbQfn74coNX3zf60LXMJsezNvvx8eIh7iOR1luJ1poxYgQk1F8PYtNq/6QODDHCqsSnTFSWC491A==", "signatures": [{"sig": "MEUCIQDoizZ5sKxZlBQS9cquTygxIFRarEjPneIoRE2X8n9aOwIgGPj7iTInewKoMMRviNySAts/R19RdtH2m9FyViGjE1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpngQ//c3R0+gpzlOI13hiTX02As+3EukBEO4XvuTcathay7xw9ubGa\r\nlA4hPXiXqpn/v2MKPLXrRhC2n/V/xVCUH92vnGv936uIPa8u3uconQ6/Q4vI\r\n4P2LxPRPBgKUznoSiD7lQe1fqGvlZIua7j9bJR/TkdOOKzRgB+CTdLagXaBI\r\n697tVLj2J4kZw7fH5ga66x6zjnPr/v7ymxI0Ms2K3Q8DFpqwZjPmYzFvC0E1\r\nYevlUQSZTKuwrPcUuBEyC8sMvofTnlz4iFCCmEuQygqY1h3ABr7hqtBOCGSn\r\nfH5vfbcCbKC4bepcDpJTAcrefgcGpcKzRvxH8xMddf5AcduwCGWqVvnkqzqy\r\ntKTIGFsNdvlqkjBKAZkrR2nQfOWTcKGtffhUDJ7Nvy9WyEw5ofqmAO+H78m2\r\naxzlOrFtI0b3CurjjPHgdT62Z2X6RXQm8MQzfeFtjCvZkkKSHj0mnISlABmw\r\nR9kGfS/cyQeHl0TWrIDJuCr+o5IFcQKPCTw4krfgiXtQHY4B93qgNxBqoD/Q\r\na2xsu+eW78p4XhtDJNAEzxEYk8+pClf8WQKZQt7pYGzuEQ7o0/7EsywH+WLI\r\nO8/LJ30gai2IUCgxgspkeMNBe8H9uOO/Zl/vHqzMpDFcgrHfAZmBziPUiFrG\r\naWx/RHqoEmhpHggx7/yI5vYn+IWaal2Bouw=\r\n=m2cM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"babel-plugin-jest-hoist": "^28.1.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_28.1.3_1657721547451_0.3949503729000672", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "babel-preset-jest", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "babel-preset-jest@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04c4c77f5d916a78fedc4383cb814f7e4c648c67", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-4auQ7spMJUIzPYPN7KkbBYzaViY45vBVulIwzQm8Tn8moTKTmCOZ9qyj5f8Dcp3Q9JqRx9SIqysNmASuu7UINA==", "signatures": [{"sig": "MEUCIEs1COqvSGkkLhg7o/AFIqMZbzrjjo/ukCz+AAsFXcfIAiEA4fyhzqlUH8gwhDk9FsVIRcKuAluk4fS9Y7Q8PO4ALuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMxg//fxyyb/ldAhEAJSiOL9LJuLCTtJp8VJ3x0JWF1F5tz8gXrUhl\r\nVSByodVFsVuWZOfwX8Dt/n5ObmZNjKoZ+h7+nHd/9MxLA9llHsLwacPgyXJ5\r\ncG4HInlnZkOiAd4wj8NNoifknDA/mKureGZNLeCAqz2u24SLGt85qUiH4syy\r\nY9nmpw4JVzzxhp6fW57LJip+5olF081+VOTRJczFqaHg4b4TNLGX9eSNYeDa\r\nKbpq1gT17CotJdimvOjFp6MdhW+qj2GN07c/faqO5wUzkkVkxN9N99cSh+1O\r\n+ViwC/YFPtT296wyVAn0DtEUsPoYicufI9+2kes5TvlgzPqp2E92glfsF3w2\r\n51BEEM2/N1GeOMrGNtWhb0I4bXMv1hgt0raa/ReqCxDczTnT1bQBVy7stHc7\r\n0lW3fn7eh/lkmr9Lj2CLmDkgBUwaG0os4Q+S/iQ+4KF+xmhzHkkW07ssZ8ul\r\nhV23Z8AQJ1DmI+JOO1R8ehT3WWiT7DN06LiDVY2Ga5Gq6FdeulWdqAcsCkmC\r\nEy977xZg+Obiwrm0EEn2O10Mc7VgD52WuLF39/dRzz55M+KQ5dpit3hviZxt\r\nCbjKC7p6k173L+pkMSKQ50Ckwp/37Y0BXPw8bZvoghzMuklDK5cj4NVQ1ZkT\r\nRepdrNKCFseX1n4wiPrNR+d6Jt64tZVLTkI=\r\n=kIsE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"babel-plugin-jest-hoist": "^29.0.0-alpha.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.0.0-alpha.0_1658095628360_0.8471477750235397", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "babel-preset-jest", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "babel-preset-jest@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "084c0886403dcff417bef8e7e189ad09b073aa48", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-QqHLCixLAkOv23hZl/5x3CgumeYik7E1MjUL5hveMw83rEmi0rvqPRdWlvnnlYlrDJDdPzMdJWDysYnAVYwd0A==", "signatures": [{"sig": "MEUCIGl5HztEQ47Dpe4WmP1Swvw2e6Yr4EjVcZ9Z4DKHlIbWAiEA5ugbshUzS9xAgOy9KLrQsJpA+l2uo42czB5DfuIFbjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7afOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFlw/9HR4IQcehFl4EAnkPX7VI4h72rqmqjWM+lWzNgetli4kMJB3a\r\nEYCsA0rwSrXavmo5jhGWstK9jx+ljenHn0LQUZwnVE26ymZKZjuojFMvOMBZ\r\nL09aGSGp4fUWhxruZANMMqjLSXjrpW9yQlB5NVqc1r2uTfzuv4CssBXOXdWV\r\ngXsdos1gr/C3LQZeOs5R3AU2CiCE36v1wiwI3/J7DLvBiYQ3Wfg+q7cyIeg1\r\nYY48RPJfeiP2btTHT13hexN/1lqm2KhXt5cjKoLdYSYJIHKY4nRfLctjGHLl\r\nd05jOGUD3ZuEqcZwNh5E95C7jYYW/92TiJsL26obwWEnFpRCJeTCgr077BsQ\r\ngdmEC8OS740c+bvHbUxDz4gH1ZIauPJPXuxsbN9eo40jLDMMmSNbcNzv58aP\r\noqjmxhdbr10q+EdkNvD8hFFMs+Z96OdLBgJdWN9LzNzX0St2mFfCqSiy3xYI\r\n4OLsXTJhDMxTddM3U2YDA47OIbn5z6jAAzVrwIYCYRjdws/NliMddczNpjJr\r\nPFHbSwIjKQEtghTYAfvoomGbvvOBxYQ5V4vReydzh78FaL8jsjwliLkPVUj3\r\nsb7NwY4hnj6lCHIUQaPabWgUSSd5+485SobHEJPlUOctGaNaEN13fnu+tdnI\r\ny/GTl1Bv5N2D0i6+LO5bdry9/w0v3uoSNwg=\r\n=N25k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "5b7a51361a19fb519215d051205867e8ad65c18c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"babel-plugin-jest-hoist": "^29.0.0-alpha.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.0.0-alpha.2_1659742157996_0.21355963820446489", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "babel-preset-jest", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "babel-preset-jest@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1af43d982f05ab42c356ea3075bbbc4e4aaba74c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-zWMK2x9fZsdlRDcpRrjeMXSHEXt+RR9fKvMRxSny3mAhjcS+wyaTiE0kQmTx9F1G2XJlxxXOg8ZR9cTpNMsX+A==", "signatures": [{"sig": "MEQCIFc++qOmohL7Z5ng3NO/BZWnCHjC0QHm4SQnPdTMkOGeAiA8xCYhZBzAPImkcS5e1GsfOQtjAVWUged350GvUYvp8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ENACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5aA/+NWlz/LzPA/HxUh1QEKvULFzF4JCz2O53jP6XSTkWN6j1d2pl\r\nQLzOlo3nOkAQ8Wrnc0g6MJTs5kW8tQIfhucVoGdYDEXijNG7qKE4l0APgVZX\r\nG5DjnrBETdVXw3pq077GUSTwhj8ds12IzB586A9bSTLTrOovW2y31HdUk2EB\r\nsaQvAQ5+6+oEYw8tR4kFEfiqSUMytKAlPfeOjL5vYElbEN3ryceL7c7FRoMw\r\nLhD39obAXGpSu3M3Fue43O6YN3t/7odRj/NRaSGpzOG+E0tyfpAOUNceknq0\r\nukdbMz/BiQFarErzsf/e+AZBN1d5G0IGsWJgbQ/m+yVrF4JKV4QceLy8BqVH\r\n7r4hhkt6DZPUVcQJOrbMcXYc6HonoTbZh9ZDz+7M7+WuFw1Z0D/+pE7loU8m\r\nGMbtxN7UcVRpNPb/tQbOb2blpwx5L4xk+PlD7JQSlVPpnQpZaX8bKxjhqy7F\r\nLxKs9Qp17woyccp52kRSp72cuEEFaAU2fLhCqpjJpX7Ds53E2wqNKqkfA8c4\r\nDPISEjQRMM/bJKAuNrvvTuKUXV5cVBU7pnvg35+97zFheQwevt+vrmq4SSXX\r\naguAkyljwvmrJw6iF2I6g3Ft32+XESccf5i+stmcb8GWvb96GDdyKuI8TFse\r\nfR6nq9q/3tVNJo4ujJGwTntO/owaYGg6Zes=\r\n=pw69\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"babel-plugin-jest-hoist": "^29.0.0-alpha.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.0.0-alpha.3_1659879693012_0.11390376889928477", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "babel-preset-jest", "version": "29.0.0", "license": "MIT", "_id": "babel-preset-jest@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "52d7f1afe3a15d14a3c5ab4349cbd388d98d330b", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-B5Ke47Xcs8rDF3p1korT3LoilpADCwbG93ALqtvqu6Xpf4d8alKkrCBTExbNzdHJcIuEPpfYvEaFFRGee2kUgQ==", "signatures": [{"sig": "MEQCICrewy8UzrluoiD+f39XT9Qbuymlmia/BXr2MBp6rlbZAiAqzv9jwVG/miGTKSBY5no4fMONf13jXPvcdf/4PHkJhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNuBAAndbt647EyZBJQhpIXYwojV1kH96/UY1qTQXLDer1bqzrmoN9\r\ntcZ7YQhUrkF1CAbqsNgv9oJ3lU3TkCe/fGBVNPSz9V0y2E5IT/uZVmgd2D0x\r\nwcVy2J1EGyuP6FrwkTwcKQj8AQuDznkUPbVtdNma0uquf3l+wNq09kmLh3Ki\r\nPnmfRz0WxZ/2AcyBl7Wilqt8idSbOH4BI8SVObc0n93u3yDQysnRNoZjmCVf\r\nttWnsBafj070mRP3VaB1RjHfrhFInct3Mt5c9f2K0YqtES/wvSR2FjVcLyZD\r\nn9pcfU3d2IeKTKSDoFYNmIrnGqW0wSkjj/DQbbQqOadQtSMW+GpNrJ9Gp7Ki\r\nPMS/QWLaq7hRBI4LfYr1+HCMx5VISIvj56VCk+i31TVN/BaFCe3o6MpZwgxP\r\nJNMiYM57swmndTO7qNTa7kZFDKyWp4iySfKKaOq6GXKECUdbAvK100yiCs7N\r\nfrID0mtvNw2vzECcffJfo9oEN1ypS0v+dFTrrXpkE8DO6grxafOKVNHhEHL1\r\nzW53AESV2biLXmLmYOF82/a6wv5gYKZ8qbdM4jpVPRE+45GP71dVY1WY5NsY\r\nH4bK6NXS6lnb68mLNTPnJzoT054HdfEZ8jTSNtKsUKlYYBC3uCnDyRY3jKf7\r\n8hLmyoXAFYCdePgG4Y23cPTazYDCFj8pIFY=\r\n=Akgg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"babel-plugin-jest-hoist": "^29.0.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.0.0_1661430805782_0.40298053101145914", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "babel-preset-jest", "version": "29.0.2", "license": "MIT", "_id": "babel-preset-jest@29.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e14a7124e22b161551818d89e5bdcfb3b2b0eac7", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.0.2.tgz", "fileCount": 4, "integrity": "sha512-BeVXp7rH5TK96ofyEnHjznjLMQ2nAeDJ+QzxKnHAAMs0RgrQsCywjAN8m4mOm5Di0pxU//3AoEeJJrerMH5UeA==", "signatures": [{"sig": "MEYCIQCcmDp3BaNup0AdeqE9+I4bWF4YeUZ5Ye+hjfU+8I8WZQIhAPjkRjJXT4p92z88r3eKcgyrzEDmdzzJXcvaVG9RboB3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzDyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSlg/+NlZmHgaEfvuM/Y65q31o4pMorgXeob2KYNjrzeV8JJSTZeJu\r\noSANE5hMDtsU3bX57KpuN58+w3P/48eTgdDRIQ+Mp51F5tRcoi0uwOs2X6qQ\r\n+8Qa3Z/709dC8QSSSwpVtbu0JVwjys4JGLqQWMOGkDkJau6vo7Wrceup4Gwh\r\nZli5T3DzPh+QQwh0Tr1wZlLl2MV36lv5LO+CbeAXxMwx/Iot9HBsMOvSN2y/\r\n9MX8OhHEpRBMe+/4yVCJOYV0kQyDfMZ/BJlF9GaIIyKSJ66xyUXYgmWL0Tg5\r\nMCe0KErxrsX82fk4D6rvstmRxwvQgY2tIRUqV0+QDCyxuuYZK5PhwD9IuJIx\r\n9Ccqu8lXSbQQmtUllgeKdKryfPOXbSUDCkZ68B2wYTZHZADdSXCUPzRt69EM\r\nnQo/J479f/mro6eOPH7aGSFpLYHUMH0M4UFoRs9N3Isz5FtjBInoXBzId6a+\r\n8V1cDFUSEat4o6i+O4WoHzGiHF/L508pd2JKQiBPmMU1r8Hvdg315Mau2DPR\r\nFEYx/keB4Y2btllpvUfOpDcLlS03e7Wx8FNzszlpC1wErjs1Lrp2Bl0sgeCo\r\nNY9A1yXSr8x00FpCDa4FYCUFyv6e9fHDKvD+nNm76R8Gy/HL0hLSzyLoyJqO\r\nhIUlc5FzKhZfRiM9YylcCjGRthVH2wRH4fY=\r\n=UAtj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"babel-plugin-jest-hoist": "^29.0.2", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.0.2_1662202098125_0.499995455909114", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "babel-preset-jest", "version": "29.2.0", "license": "MIT", "_id": "babel-preset-jest@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3048bea3a1af222e3505e4a767a974c95a7620dc", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-z9JmMJppMxNv8N7fNRHvhMg9cvIkMxQBXgFkane3yKVEvEOP+kB50lk8DFRvF9PGqbyXxlmebKWhuDORO8RgdA==", "signatures": [{"sig": "MEUCIQC6ToIFZNAV17ucLKpoqJyNjE1/pts62/t4RZI2FREGYQIgZPxJptbl5UiyalcqVfRnAx2dAUMkhn/FksZKAjxakpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB5Q/9Gh0VK33b7ftEGmghXuy2qnQrRQIa4Ya1CNDVJRgopasjhnDc\r\n4lPt53QQpj2mCuI/JDyQgbZRSyZZzscwtQXHamJ/8UEthTBmjNbjoDjkKjAe\r\nMOeoqW2/+cLxvpLQ+bfZAEarc15BZ9cAetLXmzjKze1BxnUHBPrCzj43ml7l\r\nlmOO4DMnL7GeAPAFlUeuZZPZn5jO1vrR+g0EpWIACkkFgEnJ8JLKyLGr5Ek5\r\nc4Sc9sH/5CNVEvktHbXiwLhfZfKt0FA/bnz4FkOEh15MoD2NuXLFoZ3LJpuu\r\niGw0eYcWH2ko+AZSye8YuwVrEMr7F3507UcEvZiPdiGV/z5Zyl0Q81sUMrDb\r\n9ZRgwR34is3DQFTDFL70CGhDU3p7SlS+OqrXxXHcYu5ZgMAruvcvO+Pc9AXO\r\nHa2aSawsk0ZZdvfC2H7jxlNLfD5eqmoRV22+BYKHGJh2vr0BsmR2YATe5+9x\r\nFNw+FAe6Xt+jm15QiyjKuWgi6h5WY7Rd7wURP/+CCIQtWQCHPzoX+71Y0diu\r\n9Xa+ra5T+wlRkoOwDqysLmpG0r6wB/94/M705wgx6qrTjcWk2T2lDYR6tCsz\r\nf4MLFPlcn8F5NVltwIiFWfXgi3odujmge4OD8IV6K2zrEDwyP0Y92HRJwg81\r\nPFy1zfpMMVIDsIQVYEepi4y05vGwSUVhwWI=\r\n=n6H6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"babel-plugin-jest-hoist": "^29.2.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.2.0_1665738823372_0.39748661329792356", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "babel-preset-jest", "version": "29.4.0", "license": "MIT", "_id": "babel-preset-jest@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2b03c548b02dea0a18ae21d5759c136f9251ee4", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.4.0.tgz", "fileCount": 4, "integrity": "sha512-fUB9vZflUSM3dO/6M2TCAepTzvA4VkOvl67PjErcrQMGt9Eve7uazaeyCZ2th3UtI7ljpiBJES0F7A1vBRsLZA==", "signatures": [{"sig": "MEQCIEv2qSvbsAhtZQLMYfTGSxGCc7s3VsQqHxHSp9H+ZO9eAiAnETkzQdSRJDjZW1MeHl3JfmLRA/pA66gu0RaDgHcW5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7kxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhfQ/+JLTu/lxQJirayt4atjYDa+56ncl80/RJiAk8jnUVvAPfvlsW\r\n8lJr7oj9/oUZ9pNwCMZDJIWK3FWtQoEN8NHjwShLEIF93YIE2UX7OeciCa80\r\ngcnSYOCzx0JDq7DArN+rWkWE6esRgN8dh8sD8qMxurP6MUmvG8kRghwivEtX\r\nXtSEXuv2kvdI3q3W2Aqx6w8OVy/UWwd+JjQ6xESTQ7ixc4YyWWn5Phg5R9g/\r\nqFJMKIxovk3Wukj3utum45buw8x7J7DHDYpfoWXuVy4NAS0z+//vEcABiqTY\r\nTxhaVYWFvwMgKLWZIW98HLu97j4PZxHA5m5O5wMBb/lbX0UF+YKRlMXA4ap5\r\ndVnGNXFFrugktvFW9wbKlIf30FdjGCnmOgvTRXQquvK856ZsgcLh3e6/d6Do\r\nEgdxbyH8HAJuncnSOlIwAEG8+yqaeKATjz1guvRDMWmtCl1ZUIWFlMk/p2Eh\r\nUWsIsoU1xPCKP5iGAQ/8OOuIFj8+Gw8v+gpGfQjI5Cqzc1eZywjvuYl6l+xA\r\nEqZU7u8vaeUob4hbSyRmBthRGnFIyRqDebTEZj1IiO+usY1MfnfP3B/3oDBN\r\nbsU1Jy5uO3aYjghV1HVTuQnignO/8q9I+ZHvdxp69rilHAuBfah42iMcsvQB\r\n6l7fPhnFHMBkgRPFne1tqQkVxPVRBb0dfsQ=\r\n=6HvI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"babel-plugin-jest-hoist": "^29.4.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.4.0_1674557744784_0.47371498845240967", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "babel-preset-jest", "version": "29.4.2", "license": "MIT", "_id": "babel-preset-jest@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f0b20c6a79a9f155515e72a2d4f537fe002a4e38", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-ecWdaLY/8JyfUDr0oELBMpj3R5I1L6ZqG+kRJmwqfHtLWuPrJStR0LUkvUhfykJWTsXXMnohsayN/twltBbDrQ==", "signatures": [{"sig": "MEYCIQCJJSqoCepJMCilPv0zkCHCv/P0A8q7VGghP06nNs8+kAIhAK1r0sWXbZJ52VhXigcL2ktJlAvDmpkNynAKTS7DzTCp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbfQ/+MBAoHhdVAjNX0EanYbZwrhpcqIb4/tij1g1tQvRwj3uXB5Gs\r\nhWzwy6zVVnqkvFZAgiMIE71Qpk8SVBZvK3PICr0zweWdXYlHh5UyI2glKNhh\r\nZ4w3nAKz0jExOrgZssu+pROborvgX67nRjFPA7cjUaS+dW0KstioN+XOuaIc\r\nqgwgdkDocX1p3wlZjBg2nzXboCO9btcDQRL4gNuF+GHvCthRgXtCAGioNAWb\r\nfQ6vln4xLhWHNNCI2uoIitEtWN82u2FYE4dTjYGQYtHHLp/ahcLILm7+sazt\r\n9xBGuimu7iV86xfAytFdxwNfPt7ULX8327QK/wFWgHW3Dsz7mzcFzuKi9zbE\r\n0tmH8YfY7c2DazhXXEFR4G+kRSKTWHmWh8g3KRiRdYwv9r6fTY7fzTOCPnm0\r\nvRj4QNM3MgUZfMJVOdcnomTSTLnBAnEBjAgyz3VSAXsaYtSpUjjiJQ0dWtpT\r\nq3ko7lndvSaHJIm9Kk0JBCPhKJvkLMnIWThQNPGVK3trC9Hla9X3bqKuN4r9\r\nroOR8/IZVtBcToSb1iprlR1jtMWctNMCCIY7Tgn76t0Cou/qxMIy7CeGK31i\r\nf7aK7lZ0Ab1ofZ05e3INpWcbpKlGugXvJKJASaEPwAU+tVMI8z8cyD3tObC5\r\n96PkiDj9lqux2kM3mGwhw8swKHOkPjtvf6E=\r\n=y4bA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"babel-plugin-jest-hoist": "^29.4.2", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.4.2_1675777523029_0.9407978522127078", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "babel-preset-jest", "version": "29.4.3", "license": "MIT", "_id": "babel-preset-jest@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bb926b66ae253b69c6e3ef87511b8bb5c53c5b52", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-gWx6COtSuma6n9bw+8/F+2PCXrIgxV/D1TJFnp6OyBK2cxPWg0K9p/sriNYeifKjpUkMViWQ09DSWtzJQRETsw==", "signatures": [{"sig": "MEQCIDA2N+q1JUBhv58AAcjyrRma6akKE0MiIKR2RHPr1yxEAiBPFSV24LSu62E+Tb5LLlifJSYdKiyYAuasfrOl9LZI2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MidACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoePg/9E4lf0eJ8zUy5kZkKWLwMD0lzNCBzOozNfS7M210KcBH811VT\r\nFkpWiysIDwumdojx9bRjK+uTOIDZxduuKXephUb+R9qqFWQe3xEjmIVNKZBX\r\npHm0V8Gx+qiFKCU9QPuBLRqKhVr8yUAzkHQ0F8tEPzfF3zLrHtIwhuDxyaWx\r\ncOTY2Q6y8PAtrunB/5l9M3uSNzBZ7qGRSJoSMpBYcl35j3qma0l4StgPQVxZ\r\nXOMYUzWq1rnAtIWmtzWSqEP3hjE1LehbBB6EjeOTzRtfMY+ypOKHfREC6kIP\r\nh1l+wR7NnwX24tJKbTu8E61X9tWymBN3KEOnRFLUIjg9Frt/QX2xSxims+AJ\r\ndmX1Vxke75UMEOO3BwBfHZnrBGTpNu1FhB/TAN+064IaOqM4oPXcSVwkE0Ec\r\n2ClLHLTmizVjlrOg/vamMKsCA9RfGgS/+zTwijRSIU0XUhYSCLh/yH+fpYUN\r\nKwHjqg1XwbhrlRbOWObADMKdnaocoQOHXlbJASS1P+k+spY+DO7KASB+nKf9\r\nSSpGd0aBlfm1XGc64pjvZoQZWZvnHMXwshyw6BXm/VaubI0qmSOUj4x77rxM\r\nWmpN+HCoFFMSyVJEsbP2O1HeeDbHRYEvG1585zK3ZBkwMxxgbRwm1aVOM6h/\r\nWnabp1Vr6glk1z5HB81SGOTi8m23wMDbyYU=\r\n=PAUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"babel-plugin-jest-hoist": "^29.4.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.4.3_1676462237700_0.032495626540286704", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "babel-preset-jest", "version": "29.5.0", "license": "MIT", "_id": "babel-preset-jest@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "57bc8cc88097af7ff6a5ab59d1cd29d52a5916e2", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.5.0.tgz", "fileCount": 4, "integrity": "sha512-JOMloxOqdiBSxMAzjRaH023/vvcaSaec49zvg+2LmNsktC7ei39LTJGw02J+9uUtTZUq6xbLyJ4dxe9sSmIuAg==", "signatures": [{"sig": "MEQCIER4euoRghEyRcsG73TX5fwJ0o6TnpsdMuvAquzS8gOKAiBzAf40lOpeJ699Se3vmZBn5SXaxoRkqUjv0C8dsML3cA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeunACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrURg//ZxYRYjC1o3iw+nuIU1oJrQyO3MHLtZzfNs0p9rPt7Rkz2S1K\r\n7Nl6kiBFV7mQM5yjomn2XJiseuLz8AY6YsTqlxsLVKhplrddUnuQlyNPZoa/\r\nmo1lkQTjYuSA3sL68DIjJ15IT+qWjDWjcKysMyoDa8FbDuf+mcBC63R6P0X0\r\nBtAIUvRpizZZvPGa4brgQR5FBd9SHaAYpfjyn/42aUYMHjgjGNBfaJdoe5Hu\r\ncdTJxQe24G65a93O0K9IvH24JQTvODxW2yoLnKK3tq63fSB0RRYPMja8+m9K\r\nqn7hNYwAUXQlB1JLjdbmg2b5hGpLu1au1ELwKOXesFhqr6wO2Q+z7eTo+JWp\r\nCmqP+7kmFMiP/21JD9iyUUBsFnt4V3g13zjFPSHfiEWVdLWbNUSGjeT5vJmP\r\nw3VtKMC0ERpF4tcwRvrBqiB+jma15JmZGMi7T0+YR/SIkljuqMHTBy57NMbp\r\nI7vkAYbO1/SdUgoS+O6D+pHkmUyQ82OVWJB4jIAjRoXjcElOxLJZ90oDxETE\r\n+pW9e72l/KW/MJCPM/dtymfWnyjTanErFO+ITo+xlG/6KqIfw3KTUSYREuKP\r\nFW5/MOydCKokJfvdt6ds/VZZ44aznmgaMI3O2kab+kTdga0RHXbZbNuhb2pM\r\nahAYZJ8E8nYg/CdFMyh0069ALCKp6733+co=\r\n=QT/8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"babel-plugin-jest-hoist": "^29.5.0", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.5.0_1678109607032_0.48284216465953533", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "babel-preset-jest", "version": "29.6.3", "license": "MIT", "_id": "babel-preset-jest@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fa05fa510e7d493896d7b0dd2033601c840f171c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==", "signatures": [{"sig": "MEYCIQCIqb6qrM5nx7c4kHp98jmtPwlQZ+XfmifQB21IQpErAAIhAMchapFzEIbGXHy0vuk9OenEwD/Jmeitb+7YBtj7w2+Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2693}, "main": "./index.js", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_29.6.3_1692621539404_0.27781292219606146", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "babel-preset-jest", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "bb6176f015ece73d905ebe460f5d9dc85806cf28", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-54ABRmqOwHUDLwWMugF4Pv5YY0vYDYFLJPfCcd16y+9nJ5Kkrj5PYOEos9XKzBLN2AAJORsJhwcgcn37VDFP1g==", "signatures": [{"sig": "MEUCIQD8iyWlhK8Wj00H12WPkKD2EdzS5/IMAG8Vxb7+A+eRfwIgV4JD2ASJZcqtNGkZSwi/NOJPNCXN0oCqLSTNb3wZsj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2709}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.1", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.1_1698672767991_0.3481472097816636", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "babel-preset-jest", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8042093b55ffdbf64ae53b5494b376715f3df570", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-ODN9eToXZJI0ad4YtKRtDm6iARHYvr7Kzo5WgAtZ5sGMu+MDlR2ZGaSb4A4nOmwToqE9s1jEaYGdYq6uC5pwmA==", "signatures": [{"sig": "MEUCIHcg43+IwUd45ahZvXzrLm7+jnElCGrGsO8coPqndaSJAiEAqGC2CxYuHyqNc35UsKa6x/yYIF9g1sm60K8mvn8DuS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2709}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.2", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.2_1700126895882_0.9298566251393976", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "babel-preset-jest", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e0e20a04a2199f394287ee0d860e33b8f3ce4832", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-1ySm/kw6hjNddygeHSNmwObOipFDDrmx/ADNjQjptO6SWmqV33iWwf8Usz+zQxb70+zbgEBI31Sk2BBFtTmAGA==", "signatures": [{"sig": "MEQCIC98ZczjVGX2YeDBl1R1t1ytPNEdCaD3Wc2NHDVl6ZPKAiA59y0j8HtCe9UqJmkLWWjxKwKyGuZ0o5ka06sQ5gVkbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2709}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.3_1708427333561_0.4285847350337213", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "babel-preset-jest", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "14963003dd7c026c213a0b23f07f438940f405c6", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-/AhJ06+KEaf1ZvzWJvHSosyk64BKKMj6/gDHE1MwvHFb7AoQI0JHsCp6Hyjsr7k8tQ+w17VAst9HCQ1Wz0Cp+A==", "signatures": [{"sig": "MEUCIQDrFD72K57i03rrod3/XQkNC2BALKIqjnCvItArxsWiMQIgfb2LYvPwybPJJpPQgddVWnilD18fO97sUeQuQS0ZWGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2796}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.4", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.4_1715550197374_0.7571987032869731", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "babel-preset-jest", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "444891c3bc5b32f5d9aeda48fdf8250c53926a2d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-LmoEZClo/Bk87e7g7PTJq25Ote7Bjw/M0AgvPJHz7yc6OGN5cEasP5Ovew8A5RgAGvO39HfWE2R+p5fol8lAhQ==", "signatures": [{"sig": "MEUCIQCEbhrb6ZxDhRw9NF3E1xRDcj0/AL335S+FP01S/lmIpwIgV4LaBjiKdalFar66PkdFlWRmr56YyPFp+o60GuYKY6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2796}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.5", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.5_1717073034586_0.3433974775541673", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "babel-preset-jest", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "d490267eacd1a8f7040a99211f15f5ead754f52d", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-Xsis7RI2oT2zlyCIEzMtjDiES0wKoQxTUo5BGzx1q3ZemnDE1/7xTC4/lI4eBLmAtwk/hpZLRYwltvbQEvyRWw==", "signatures": [{"sig": "MEUCIFcYE8a/lURZ/sExWPDlMfZznulVEH2ng0UJmQ8o+cb7AiEAjeMTf0gOMjkq6puajx8ohVhLKcE3mYjJ0gGv1MqT5EU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2796}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.6", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.6_1723102977790_0.1509050219360648", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "babel-preset-jest", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "babel-preset-jest@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "4848db41db4c6c6b7829b35c51c2af2b355258bf", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-LUWdMJ6mIJhXv56hVe+t3/lxokSd0geN8+TT1FHjJAEdHXi+ye6TRqVggn6fdrL/mxXXg9QusAzHJVjF4vxWEA==", "signatures": [{"sig": "MEUCICHUDjiaX9MgR8P2E2ehzLbn9FVMRmZfdJxjl+5g9P3FAiEA69f/jK8W3RpP8uJexv6rX1SKvVgYEEWcjb+zYQT6fzE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2796}, "main": "./index.js", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-alpha.7", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-alpha.7_1738225704202_0.5059004284275117", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "babel-preset-jest", "version": "30.0.0-beta.3", "license": "MIT", "_id": "babel-preset-jest@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "838c697e8d0515475a0f4168f248956e4004613e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-2/Oy4J/MxFwNszlwYPO4L7Z+XI7CNCbiz5HZwrsfWnEEDBxJZBJzblfc8TP9lzeiQ4v+Vvem7BMS6B2dVCfzOg==", "signatures": [{"sig": "MEUCIQDlTQkhisHVs5UUVwq+igEBpFNL6Kw7ljbTUQMvAz8CVwIgUZDZwbrQ0msfcibWS7nuj49FmvV5Jb1mk8LM2QjDOnw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2793}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-beta.3", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-beta.3_1748309257147_0.7166054064054836", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "babel-preset-jest", "version": "30.0.0-beta.4", "license": "MIT", "_id": "babel-preset-jest@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2f0c2bc6cdfb40762bd7bdb12e4cac0d20d865c5", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-Bk1pvt9HXVNOCFSWHAJOFRbGTJ5ci+/wraZbnc/xHnzz24TeXKFbAsAtcbkV5dnSdZEw/w8HyLQFggrTgz2E6g==", "signatures": [{"sig": "MEUCIGrr/TIHMplYBZ7/+dxUjJhF9uxFYK0F+wBV8WZwLFSJAiEA36MTp+sL16jbDCtyUiQhYLq5yG++Qf6mj4eMDDgL9Kk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2793}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-beta.4", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-beta.4_1748329466988_0.3108523892156583", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "babel-preset-jest", "version": "30.0.0-beta.6", "license": "MIT", "_id": "babel-preset-jest@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2eff2d071ae299765a643abafa75effd3254093e", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-DtxZUkoDZHLhE+r/RLk+ER1b0vj7rtyTojarUzHdjTDVOci1uamKPkhX+/EV0fK1Ay4lhPZ/XUIGdh8/pp7Q4Q==", "signatures": [{"sig": "MEUCIHxlsJ0ZppdSaDit16mW8KDKMzRc4zpnFonfdoJ5ImfbAiEAufI4JsFpf+1EipAVz3r5bb5Yw/v06L8sBDnQRDuq2VU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2804}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-beta.6", "babel-preset-current-node-syntax": "^1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-beta.6_1748994639094_0.7709908430160961", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "babel-preset-jest", "version": "30.0.0-beta.7", "license": "MIT", "_id": "babel-preset-jest@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "da82b28e4daa9ea8483462b02edbb3757b145cca", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-mqI43Lmkm4lvdwf8bqp9SM0RsBsprgDDuTBxFdCYw1+HqxKhkgdQvhNkEDJIRnIc4ltqAFeoaOJ0kBsPfd2y2Q==", "signatures": [{"sig": "MEYCIQDU2BfpSF1sbvQE+/WXuZRDkUIfB/7V4iPE7SRP4ITpcwIhANH3ChAuyId1S3Jf1SogV/qu2RVv7lxc5w0gjBZy9Qn6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2804}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-beta.7", "babel-preset-current-node-syntax": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-beta.7_1749008136614_0.19409639318091698", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "babel-preset-jest", "version": "30.0.0-beta.8", "license": "MIT", "_id": "babel-preset-jest@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "96694297ca9f527421a0fd928150f61341f9e60c", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0-beta.8.tgz", "fileCount": 4, "integrity": "sha512-qo1zxbwAW3ooz0vduLgtaQOr4akub6iDq4OSR8U3T5h0tU3dmvCJbm8Wd7HI2i6NXP265NSia+qi4jlMAM5eXQ==", "signatures": [{"sig": "MEQCIE5X5+cmv7D6r3UbTsThqHF+KSSrTmQkcyeC9BUqmVoeAiA3Vf6inVs0rKe0MTIhub9fcc2l2FQhXglu2o9rleHRmw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2804}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0-beta.8", "babel-preset-current-node-syntax": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0-beta.8_1749023584246_0.21487140469763122", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "babel-preset-jest", "version": "30.0.0", "license": "MIT", "_id": "babel-preset-jest@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "54b16c96c1b687b9c72baa37a00b01fe9be4c4f3", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.0.tgz", "fileCount": 4, "integrity": "sha512-hgEuu/W7gk8QOWUA9+m3Zk+WpGvKc1Egp6rFQEfYxEoM9Fk/q8nuTXNL65OkhwGrTApauEGgakOoWVXj+UfhKw==", "signatures": [{"sig": "MEYCIQCbV+qMpBk2RMw6hdOIIyI2e0SFhEWplvLpdfGySFNnbQIhAP1vmwyc5Qyd0xVuAm1ZfAU4IeqnykSRXooVlfF4gfqi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2790}, "main": "./index.js", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-preset-jest"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"babel-plugin-jest-hoist": "30.0.0", "babel-preset-current-node-syntax": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"@babel/core": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-jest_30.0.0_1749521741846_0.9901971572563895", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "babel-preset-jest", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-preset-jest"}, "license": "MIT", "main": "./index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "dependencies": {"babel-plugin-jest-hoist": "30.0.1", "babel-preset-current-node-syntax": "^1.1.0"}, "peerDependencies": {"@babel/core": "^7.11.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "babel-preset-jest@30.0.1", "dist": {"integrity": "sha512-+YHejD5iTWI46cZmcc/YtX4gaKBtdqCHCVfuVinizVpbmyjO3zYmeuyFdfA8duRqQZfgCAMlsfmkVbJ+e2MAJw==", "shasum": "7d28db9531bce264e846c8483d54236244b8ae88", "tarball": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.0.1.tgz", "fileCount": 4, "unpackedSize": 2790, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICeFRaROvQN1nNSr612rdKYw2Yf9qTiU0NEgX8rPGckVAiEA0hJaKorc8+KWc0Ufe5Ene6CTaUUJvRaMtZh/LYuFt/I="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/babel-preset-jest_30.0.1_1750285879927_0.4774362691171292"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-02-25T06:23:10.813Z", "modified": "2025-06-18T22:31:20.300Z", "0.0.1": "2016-02-25T06:23:10.813Z", "0.0.2": "2016-02-25T08:01:45.650Z", "1.0.0": "2016-03-05T00:29:47.650Z", "9.0.3": "2016-03-23T01:09:37.586Z", "10.0.1": "2016-03-30T23:44:10.190Z", "10.0.2": "2016-04-11T08:24:01.740Z", "11.0.0": "2016-04-12T07:56:06.526Z", "11.0.1": "2016-04-15T04:02:27.403Z", "11.0.2": "2016-04-18T04:03:21.108Z", "12.0.0": "2016-04-27T05:24:12.567Z", "12.0.1": "2016-04-27T10:09:38.073Z", "12.0.2": "2016-04-28T04:31:39.668Z", "12.1.0": "2016-05-20T18:06:05.571Z", "12.1.1-alpha.2935e14d": "2016-06-17T05:33:15.647Z", "12.1.2-alpha.a482b15c": "2016-06-17T07:58:05.460Z", "12.1.2-alpha.6230044c": "2016-06-21T19:15:14.947Z", "12.1.3-alpha.6230044c": "2016-06-21T20:11:50.748Z", "12.1.4-alpha.a737c6e5": "2016-06-22T03:36:42.332Z", "12.1.5-alpha.b5322422": "2016-06-22T06:45:51.180Z", "13.0.0": "2016-06-22T20:18:16.968Z", "13.2.1": "2016-07-07T01:59:37.055Z", "13.2.2": "2016-07-07T02:13:01.227Z", "13.3.0-alpha.a44f195f": "2016-07-11T09:56:48.141Z", "13.3.0-alpha.4eb0c908": "2016-07-11T10:04:34.496Z", "13.2.3-alpha.ffc7404b": "2016-07-11T10:20:02.091Z", "13.3.0-alpha.ffc7404b": "2016-07-11T10:20:12.818Z", "13.3.0-alpha.8b48d59e": "2016-07-13T06:14:08.190Z", "13.3.0-alpha.g8b48d59": "2016-07-13T06:39:38.685Z", "13.4.0-alpha.d2632006": "2016-07-27T08:57:11.130Z", "14.0.0": "2016-07-27T09:14:36.966Z", "14.1.0": "2016-08-01T10:26:31.269Z", "14.2.0-alpha.ca8bfb6e": "2016-08-15T18:54:34.935Z", "14.2.1-alpha.e21d71a4": "2016-08-16T21:18:35.353Z", "14.2.2-alpha.22bd3c33": "2016-08-16T22:54:51.905Z", "14.3.0-alpha.d13c163e": "2016-08-18T20:35:23.579Z", "14.3.1-alpha.410cb91a": "2016-08-30T21:52:04.972Z", "14.3.2-alpha.83c25417": "2016-08-31T18:50:32.360Z", "15.0.0": "2016-08-31T18:55:25.695Z", "15.2.0-alpha.c681f819": "2016-09-29T09:02:15.585Z", "16.0.0": "2016-10-03T08:38:30.474Z", "16.1.0-alpha.691b0e22": "2016-10-28T07:27:28.583Z", "17.0.2": "2016-11-15T00:39:17.617Z", "18.0.0": "2016-12-15T11:24:22.727Z", "18.5.0-alpha.7da3df39": "2017-02-17T16:58:09.829Z", "19.0.0": "2017-02-21T09:30:11.111Z", "19.1.0-alpha.eed82034": "2017-03-17T00:41:28.476Z", "19.2.0-alpha.993e64af": "2017-05-04T15:38:17.126Z", "19.3.0-alpha.85402254": "2017-05-05T11:48:27.419Z", "20.0.0": "2017-05-06T12:33:00.827Z", "20.0.1": "2017-05-11T10:50:12.676Z", "20.0.2": "2017-05-17T10:50:27.489Z", "20.0.3": "2017-05-17T10:57:17.232Z", "20.1.0-alpha.1": "2017-06-28T10:16:24.650Z", "20.1.0-alpha.2": "2017-06-29T16:36:51.336Z", "20.1.0-alpha.3": "2017-06-30T14:20:57.910Z", "20.1.0-beta.1": "2017-07-13T10:33:46.821Z", "20.1.0-chi.1": "2017-07-14T10:25:09.824Z", "20.1.0-delta.1": "2017-07-18T08:46:58.948Z", "20.1.0-delta.2": "2017-07-19T12:56:48.549Z", "20.1.0-delta.3": "2017-07-25T22:12:28.249Z", "20.1.0-delta.4": "2017-07-27T17:19:10.220Z", "20.1.0-delta.5": "2017-08-01T16:33:38.561Z", "20.1.0-echo.1": "2017-08-08T16:50:02.008Z", "21.0.0-alpha.1": "2017-08-11T10:14:10.186Z", "21.0.0-alpha.2": "2017-08-21T22:06:50.176Z", "21.0.0-beta.1": "2017-08-24T21:27:22.164Z", "21.0.0": "2017-09-04T15:01:59.591Z", "21.0.2": "2017-09-08T14:19:36.684Z", "21.2.0": "2017-09-26T20:22:23.464Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:47.954Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:55.122Z", "21.3.0-beta.1": "2017-10-04T10:48:44.495Z", "21.3.0-beta.2": "2017-10-13T09:54:12.442Z", "21.3.0-beta.3": "2017-10-25T19:34:10.438Z", "21.3.0-beta.4": "2017-10-26T13:27:05.341Z", "21.3.0-beta.5": "2017-11-02T13:17:36.583Z", "21.3.0-beta.6": "2017-11-03T16:21:41.068Z", "21.3.0-beta.7": "2017-11-06T09:39:53.625Z", "21.3.0-beta.8": "2017-11-07T17:43:50.656Z", "21.3.0-beta.9": "2017-11-22T13:17:40.454Z", "21.3.0-beta.10": "2017-11-25T12:39:30.844Z", "21.3.0-beta.11": "2017-11-29T14:31:26.800Z", "21.3.0-beta.12": "2017-12-05T18:48:39.454Z", "21.3.0-beta.13": "2017-12-06T14:37:26.868Z", "21.3.0-beta.14": "2017-12-12T10:52:41.317Z", "21.3.0-beta.15": "2017-12-15T13:27:44.807Z", "22.0.0": "2017-12-18T11:03:29.529Z", "22.0.1": "2017-12-18T20:29:28.563Z", "22.0.2": "2017-12-19T13:53:07.412Z", "22.0.3": "2017-12-19T14:59:00.457Z", "22.0.6": "2018-01-11T09:46:50.717Z", "22.1.0": "2018-01-15T11:57:25.726Z", "22.2.0": "2018-02-07T10:26:04.153Z", "22.4.1": "2018-02-22T21:29:01.607Z", "23.0.0-alpha.0": "2018-03-15T14:55:39.055Z", "23.0.0-alpha.1": "2018-03-21T16:00:17.621Z", "22.4.3": "2018-03-21T16:09:07.623Z", "23.0.0-alpha.2": "2018-03-26T10:40:50.680Z", "23.0.0-alpha.4": "2018-03-26T12:31:43.726Z", "23.0.0-alpha.5": "2018-04-10T19:18:22.586Z", "23.0.0-alpha.5r": "2018-04-11T05:52:51.708Z", "23.0.0-alpha.6r": "2018-04-12T07:01:37.804Z", "23.0.0-alpha.7": "2018-04-17T18:55:29.280Z", "23.0.0-beta.0": "2018-04-20T10:10:56.681Z", "23.0.0-beta.1": "2018-04-21T15:44:32.054Z", "23.0.0-beta.2": "2018-04-26T21:17:44.814Z", "23.0.0-alpha.3r": "2018-04-30T13:10:22.760Z", "23.0.0-beta.3r": "2018-04-30T13:15:02.282Z", "23.0.0-charlie.0": "2018-05-02T10:56:35.292Z", "23.0.0-charlie.1": "2018-05-03T12:10:24.031Z", "23.0.0-charlie.2": "2018-05-15T09:51:38.118Z", "22.4.4": "2018-05-18T12:58:52.270Z", "23.0.0-charlie.3": "2018-05-22T14:59:10.594Z", "23.0.0-charlie.4": "2018-05-23T10:42:35.387Z", "23.0.0": "2018-05-24T17:26:35.199Z", "23.0.1": "2018-05-27T15:31:05.020Z", "23.2.0": "2018-06-25T14:05:19.792Z", "24.0.0-alpha.0": "2018-10-19T12:12:51.852Z", "24.0.0-alpha.1": "2018-10-22T15:36:07.504Z", "24.0.0-alpha.2": "2018-10-25T10:50:56.697Z", "24.0.0-alpha.4": "2018-10-26T16:33:20.894Z", "24.0.0-alpha.5": "2018-11-09T13:12:51.555Z", "24.0.0-alpha.6": "2018-11-09T17:49:47.563Z", "24.0.0-alpha.7": "2018-12-11T16:13:07.529Z", "24.0.0-alpha.9": "2018-12-19T14:22:49.679Z", "24.0.0-alpha.10": "2019-01-09T17:00:54.898Z", "24.0.0-alpha.11": "2019-01-10T18:41:19.859Z", "24.0.0-alpha.12": "2019-01-11T14:57:58.444Z", "24.0.0-alpha.13": "2019-01-23T15:15:34.286Z", "24.0.0-alpha.15": "2019-01-24T17:52:39.184Z", "24.0.0-alpha.16": "2019-01-25T13:42:09.117Z", "24.0.0": "2019-01-25T15:05:04.116Z", "24.1.0": "2019-02-05T15:06:49.254Z", "24.2.0-alpha.0": "2019-03-05T14:46:56.908Z", "24.3.0": "2019-03-07T12:59:39.192Z", "24.6.0": "2019-04-01T22:26:22.458Z", "24.9.0": "2019-08-16T05:55:53.257Z", "25.0.0": "2019-08-22T03:23:53.203Z", "25.1.0": "2020-01-22T00:59:52.449Z", "25.2.0-alpha.86": "2020-03-25T17:16:24.282Z", "25.2.0": "2020-03-25T17:58:08.199Z", "25.2.1-alpha.1": "2020-03-26T07:54:20.459Z", "25.2.1-alpha.2": "2020-03-26T08:10:29.742Z", "25.2.1": "2020-03-26T09:01:09.795Z", "25.2.6": "2020-04-02T10:29:16.878Z", "25.3.0": "2020-04-08T13:20:58.464Z", "25.4.0": "2020-04-19T21:50:22.768Z", "25.5.0": "2020-04-28T19:45:20.189Z", "26.0.0-alpha.0": "2020-05-02T12:12:59.301Z", "26.0.0": "2020-05-04T17:53:04.624Z", "26.1.0": "2020-06-23T15:15:09.494Z", "26.2.0": "2020-07-30T10:11:49.077Z", "26.3.0": "2020-08-10T11:31:41.632Z", "26.5.0": "2020-10-05T09:28:13.783Z", "26.6.2": "2020-11-02T12:51:22.176Z", "27.0.0-next.0": "2020-12-05T17:25:15.834Z", "27.0.0-next.3": "2021-02-18T22:09:49.490Z", "27.0.0-next.10": "2021-05-20T14:11:15.901Z", "27.0.1": "2021-05-25T10:06:29.701Z", "27.0.6": "2021-06-28T17:05:37.595Z", "27.2.0": "2021-09-13T08:06:41.970Z", "27.4.0": "2021-11-29T13:37:03.320Z", "27.5.0": "2022-02-05T09:59:22.929Z", "27.5.1": "2022-02-08T10:52:15.363Z", "28.0.0-alpha.0": "2022-02-10T18:17:31.020Z", "28.0.0-alpha.3": "2022-02-17T15:42:21.295Z", "28.0.0-alpha.4": "2022-02-22T12:13:55.706Z", "28.0.0-alpha.6": "2022-03-01T08:32:24.047Z", "28.0.0-alpha.9": "2022-04-19T10:59:15.164Z", "28.0.0": "2022-04-25T12:08:06.821Z", "28.0.2": "2022-04-27T07:44:01.789Z", "28.1.1": "2022-06-07T06:09:36.691Z", "28.1.3": "2022-07-13T14:12:27.785Z", "29.0.0-alpha.0": "2022-07-17T22:07:08.508Z", "29.0.0-alpha.2": "2022-08-05T23:29:18.163Z", "29.0.0-alpha.3": "2022-08-07T13:41:33.199Z", "29.0.0": "2022-08-25T12:33:26.004Z", "29.0.2": "2022-09-03T10:48:18.358Z", "29.2.0": "2022-10-14T09:13:43.545Z", "29.4.0": "2023-01-24T10:55:44.985Z", "29.4.2": "2023-02-07T13:45:23.141Z", "29.4.3": "2023-02-15T11:57:17.839Z", "29.5.0": "2023-03-06T13:33:27.231Z", "29.6.3": "2023-08-21T12:38:59.567Z", "30.0.0-alpha.1": "2023-10-30T13:32:48.131Z", "30.0.0-alpha.2": "2023-11-16T09:28:16.030Z", "30.0.0-alpha.3": "2024-02-20T11:08:53.735Z", "30.0.0-alpha.4": "2024-05-12T21:43:17.510Z", "30.0.0-alpha.5": "2024-05-30T12:43:54.763Z", "30.0.0-alpha.6": "2024-08-08T07:42:57.930Z", "30.0.0-alpha.7": "2025-01-30T08:28:24.371Z", "30.0.0-beta.3": "2025-05-27T01:27:37.319Z", "30.0.0-beta.4": "2025-05-27T07:04:27.136Z", "30.0.0-beta.6": "2025-06-03T23:50:39.260Z", "30.0.0-beta.7": "2025-06-04T03:35:36.796Z", "30.0.0-beta.8": "2025-06-04T07:53:04.409Z", "30.0.0": "2025-06-10T02:15:42.031Z", "30.0.1": "2025-06-18T22:31:20.120Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-preset-jest"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"adamduehansen": true}}