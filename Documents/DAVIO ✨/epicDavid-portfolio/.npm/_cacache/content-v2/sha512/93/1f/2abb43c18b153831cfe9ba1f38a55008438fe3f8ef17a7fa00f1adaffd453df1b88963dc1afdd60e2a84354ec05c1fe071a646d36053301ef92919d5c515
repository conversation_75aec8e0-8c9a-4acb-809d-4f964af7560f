{"_id": "@babel/plugin-syntax-async-generators", "_rev": "71-4638ed20cb5f3f3ebe09d6ec13dd90a6", "name": "@babel/plugin-syntax-async-generators", "description": "Allow parsing of async generator functions", "dist-tags": {"latest": "7.8.4"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.4", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7TeToIYD2SCSxeW3taT/spAR1YAdNJUvMO5qTqnZ1KVoFTv7p4nQIBTiJJHca+ISYoWO9hSDkfw0tfD/YKmwQg==", "shasum": "66e1cdcf1a797b4924080b10585ded40bcacdffd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHykcC4A9HriA6RK1IiodwShDJ3x6tg39D6a2j/XA1aQIgRyCM+7PdhuZMUqSzw7L2HpztPB2vGytAOXl0ZpDdp8Q="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.4.tgz_1509388450962_0.4220747465733439"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.5", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b9hVwDDuLSgdj0OJZkFPI3cdpAfFerTG5Y/KZxfwCbbq3QzeuPMPwZGkaORRF9FMJLb7TY8dscsPs4FxZ1BwWg==", "shasum": "9c19c0df0b0a8c577016f31b1026216779c174b8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkryjFa+64R21MfdaivrTBY07H6pGHXlLb875rLaeeIAiEAxSakIPu9nHa2WqZG2Z6OMCfYeJid0oXpjkrfYnLvg9c="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.5.tgz_1509396956022_0.5890050330199301"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.31", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g13cISFMdNw/zQnl1q9L8aDAHLWy2SIMwra803DdrNnSvlaKbJKqR/iqxEGD6b/ZPSQv+wBKxqnfZ+P9XKWfrg==", "shasum": "f7347f918941c2106b246adfa87bdc439d797e01", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAlPA1Oh7X9kPxk76lGpPnd+tYHh2kcBTSOowyOZQCHQIhAKy5755w7nQXVtQaPuSpn3hLtY3J0vyyWnIjFO3vVS7i"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.31.tgz_1509739389458_0.009400941664353013"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.32", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FfWnqZ9LsPqTJiYXSOlfoVTBf/WdwZh/tnhXoGAK0Y77ExfcAaJqIqt0QalHHFxLy5uIewgkRilkXTk+qZV2OA==", "shasum": "64eea83224be72f5b122829d96761a6b2a988d43", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuRnLLpKOhuTQ/QhBRa7w+36Nn5mJcLHCYgbwVKXYsgwIhAPth1JS6QoKwNg4E85L6K9bL0M5a/TkSviEOIl2SYakZ"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.32.tgz_1510493578215_0.5511072671506554"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.33", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-e+uzrYkwt/gjHFpcnR5bqSJcAlmg1AuprverngomqlXfH8a+dLDh0Zfs+29/IVSQ4SuP0ZOhgbFR/d0OTyVm7Q==", "shasum": "a2311e7d3511fa1ea718c3524b5cb8bd3ced1d7c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDT4c24Pi0yzXdsFQh7943MCWgy3YLrct9u19OyBoGpqgIgL3TObeaRD/kI2gG4VGNAqXA/wYdsfBhz36mHziQZRc0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.33.tgz_1512137825316_0.8210733181331307"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.34", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4y/+dUbzOgWE5gOgK1KKTd/3bSH1Tc97Ba5xlvZk+ufTBiHsG+WSvdaK0aNtPmBcA3mvfHFDYiLPRslU/X88ww==", "shasum": "8f3990ec7f98ded0ede1edc65a636bebc63fa34b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDw0XU/AJYn8EPQEY9zHUEnXNF9a9Qq+PcPSwrvxJdWTAiADtsRFpTmtO2yvkzT7l2vvNLVFQqgIIBNGwiBwCXvw1A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.34.tgz_1512225539116_0.46694142441265285"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.35", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-i6igBBo3ED9CyvtwnBjgYkD6x3qJU5B9h97bU6DVaCGbaJKTFfqv8z+4JAO9JG96cfod5aiYdoDbdawfAoX1LA==", "shasum": "ff672774420f1fa1cedc407ba0c33e8503339198", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6QhUBUHXUwiAf1ZYFHHRRzkVZ90hKZmQ1ftAFQ1fVvAIhALBmi5Ys0AWT5/lfkN/V8aEkUioRKvoWsvUA45+rDKXO"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.35.tgz_1513288051090_0.4342331965453923"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.36", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jBtmcbbDSCCI5IA7QC8Wvhw6YMjTKgc8FCKGiZAl4GTI6PApW9fZQCf0DoQU/P69lxG4cFKYddb8GIItEBm98w==", "shasum": "6c5d1d9606e6875fc0548345c2d67c1ea5e029b4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGjo4GzjMciNBv8jvT+YQlhzejdKxi8wxIexcnT5NDbPAiEAs2oJkb/wBKkZEfAJYTrqUToe0/XY3xhWXI0apfDDFfc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.36.tgz_1514228654632_0.23273320239968598"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.37", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2If1a2pcisdP+4vXhmrlOgoHRgYHzap0vvIBy26CwaCB6Vdf+oJm7IxPi+O5isLTn85H/CQHLGXbpJqHDF76DQ==", "shasum": "6c5d102534a9e7c0fa1af9b751756a1866b5d01a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ2OtARN868bzF34Z5xDbOVFPD65AVNOdQq7xd1a7CXQIgWe+x3M74J5F11+StHyWayG1gsVGGEJwtBbdhiP6eYYE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.37.tgz_1515427338277_0.20304346503689885"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.38", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YboIs/srf1yKLRvZ6JoaGjpkcW3UYsPKhkPt3hWO54OVouS34A5dwK8wO3wGhvKgUvq2GV+tU4eRNkgEcx7aLA==", "shasum": "1224bcd4a5052e6e4c0eb95c96bbe9f617a119f4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkDw0nEN2VhCcP2rWoK60Sen9Q3xsBBUHuDFNro37iZgIhAN9dOd3kPssw93InrXaD4NnP9McEoUrhDbWbIWub9LnM"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.38.tgz_1516206696853_0.0939793549478054"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.39", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oK7XywgBSLd5L5pGcyksz40GtHj3u9DfKP0CnOUGhneqk2pTtAyeH2Jxgks84mE9QjdJ2voTtBtIzG2DwmXlkA==", "shasum": "ace93b8bb53e256a330b21d78304843fd6d72ab4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJevpNQJbEiWB15bD4Iy2Do9PoTZE+ggREtrFUQj3MMgIhAMMPVeK6IlRn2oAFSYsd9ZO/4BMlMtvRdKca/2Bgr/R8"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators-7.0.0-beta.39.tgz_1517344043405_0.9504216045606881"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.40", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UczObsgk1A4DaSMqTj59iETtmtuiXdBMs/1WBpy6LvLtf8AdjO/bZ2IbvrwKR5gEp8xJxBgzNq2sfK8RUsQBsQ==", "shasum": "6c45889569add3b3721cc9a481ae99906f240874", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1508, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeiqeZEf6QeOq7ds1MR5jJswwBmzsnUzMLFY+B1xsX/AIgDZ0FJPKW9wIpQdRW9McxI0OkpfsXHKv1yZ1FfFebto8="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.40_1518453405426_0.5529148978826566"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.41", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cU8m6hWU8ILtYQrI0maJgTHzfKSeO9B9kEONAa141SCm5BQNBCHNECbblvQyeg3mJH+/HYzZeJsUJ21k6oGDZg==", "shasum": "a4ff2eff7491e9f93d92ca2b51a0ef6f966d98c0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1743, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwxqbI+wUnTBvKCSkHeWfdhVSb0Tc4M6huBTSyTVYvwQIhAMh+pm5oiXG/R/5Gxdt/HjJNczhZ2oZEOdSM+l+oSlbe"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.41_1521044748852_0.6257234861021139"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.42", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-k6oVMsmY/mcp+rPRcK4dJL/J1ahtIRucXtNHNvAVRV9WFc7G3r1rrb1GlM4iNHareXBNdRlf7mkxyVaVpyJ3TQ==", "shasum": "deccff2f01c2ed280493b0ba256b14df232ca299", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1743, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw3SKtAVU9gp6Y8mqbM8LxEtSNa1xQt5LJMuc5SPYKoQIgfLQUatjfZoKVW16y3Oph+PBxCSy5EMHzKIdCZ6r7rVs="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.42_1521147023703_0.8847886168884977"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.43", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GiaruXW2cXOrpXXwVyE1ldQKMubAuUzw/u+0PPoLKhToOCdcNnaEVIhP1dSFbNhxhG+uawMmcTSINhk3JK6ViQ==", "shasum": "43e0893f58bdb834c4c00504e1992926d38d660b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1848, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdCm0tpteoUWGfYUU8ojgevAUluZRY5KEnDxUN/a6xSwIhANjzo/IeuNc2VqmH9qPSBJe3AzoiYIdJx3XcYBSgZyJw"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.43_1522687691969_0.5273868891241027"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.44", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-42MXWQvAN2IPPcW4HUbqgeyqkAwAsCGIHzxgSYoI7aOgkfOwHf5LRqSlJ56HAH+WwlWDQUvXeT/2PrQQY1vSCw==", "shasum": "5cf7ec4256ddd7df62654171059188bee2b3addc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1899, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVlRNXIpnr2F7HHuhfKGpW11bzfjmtuI1JmygjwQqAsAIhAJ8JeQ3jnXdKPbvnuaX+KJd8X/mJIFVkeqbmnQ23cO1Q"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.44_1522707593651_0.3202179321441918"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.45", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fmJJUb4/5d9NMI7IDgj7SAM6cEu6sEy9n0Hra8YB/bnUb2+v0UGRLsYgyFOcw4cXhBl7LL+5ilBbCW/JBP9vDQ==", "shasum": "d771548ff07df1036f938398f5bcb3faafe20e6a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0yCRA9TVsSAnZWagAAJPgP/R23IZnhuMUHp24FQp09\nLLBDq3UZBYNBX4BhumbgJqqla+LIk7QHNsq/70YMsHc8cRvksSNWYdDm6vFu\nn8xKqADvQbjT1MEIFGjGu1TWMNbE/lnCceTl3Tlk0S67St6rz5QWLM6OIkvL\ni3zb12tWO3s5sduNljntBXbfj95ND6Fobrqk9TCQr6qV51NINXcYXBhB1eEK\nCAXvGPhwzq1rJpPYtxVDybvb1Jq06u/b14Nrv8gFhyVc3iJ6enyKXVsWZbrv\nA3jJ3R7UnhoCV0n8kutx6tPlkC2LV9EQz6R0wVj/ppKkc6lZvOdSvJqYx/k5\ngdOCNaVYoGu1h0T1V5Bs9dXdUzVGF9Xvr1nzB2zVuL/7w6u54E2P6giKO7Hd\nzBZ60KEbtGeA12PN9JXniyvXuMKub4orqfqd8IZ0rq0eT4BSrq6/KljaVkPV\nd5jpQBgaRK8ZsHoU+THrLt5joTwsIduXFUjn3aXuG7X47CSSshMDf+yAWSPQ\niJB/YGKBktEK0jmANvdcr8TclZ9cGRSjONnhfHRbGAciSKTPqaALaFh6ea2R\nT498QjHbHUBho5WsA74bAZyYla/mTa0dXVj6A6KM5tkEcIJhtZjk+mcESDU9\nNzwconUYaebvfBMFL0WLfJ5DRibuPS/bBhML1qHt10F7MFhCpeCpgsr0qKm8\nwgDj\r\n=GM0E\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnG+xp5DwCoebgg8NCzvyb24RhayRnXxukalf6vwYMTAIgPVte7Z8FmbWw9+jZnC/sCQHHl1JCBg+PxcFBY1bWkyc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.45_1524448561074_0.25346228745041777"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.46", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-igLQzZYcSrY/W3eACq6Nrf9u9mfw7m5G6+JeTZlyI7w0z+upGvWvrpTaAP1Gni6MckQkx5q+pspgpV+/5ioPXg==", "shasum": "b35149e02748922d8e39506b0ac001a27bf449ed", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFiCRA9TVsSAnZWagAAn28P/jZCSd8xSeuzD1xOvpeM\nyC53JxImvXZqgB3a42C3ccfF9SvvcDVqFrLlDzu6n+bG0ElNUGI3gdW4Rk/V\n4EGlnoEUL1DFgmyZ8P9utlV7xuvf3GIEgjRYaC3BMk6fEnBM7LU0U08PwAWf\nspxoYkopC8WLJTrRvfopqStRczU6RyC66DnLfDySYbZPiQeTXaxVr6Ym5wuA\ndXpoMU0YF/CLSD8bMzg/Kh/4/QfLsgcD97jjhhgluvRP3KsYbmXoJE2f7y5w\nhsmmxF/AmOELp1mzcrOgMoeexdEbY3uqAGfX8QyWkeddfEbA63/+qakiTtTZ\nGnuUC6GQ68YQ13WnqXeEN7bBaTGqD9jgCsM6yh6W2todIy3vgzQLdUfFzYf3\nC0280amlL6lmKNGjfpnK88EYVZQcXZ2EB5v1j5pV/Kl8Sm6plPDMspDHQ9BG\nvFu+t6A5nQSvmMJt7NtoUg3LMcstKT4I8PM7ASsjlEanZJEFU1HRNgd4Lxo5\nYSszjP+En99nsre/HR1/ISXKQSo2DXrN0sGxg13AT6d4y0r9EnscQ/+UL1+l\nAPXNKcyx1jBlawzNu129CvU8xCmgzGIcg/Q0oe4EgNyS6MIYL8YJocp30w0J\nERowHtkEA1WKKifbXv7UEDVe3pXwmaIAX9DQYjJC6AeebwiIzj5ztemQFl8d\n2QVe\r\n=kNq7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDRiJq0LVP6y/hhzNTIt1RHtnGMmA8j1Lf8IVHCyfddUAiEAoabVzxsoc9Htx+sEVieNFnrMW+SRG7IZz7w9UB5ItHQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.46_1524457826398_0.8622227826870432"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.47", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LCNGYTLpQAsvTmVmT/I2pz7dIh3Bu+9BLxqL7b3QfplsMQxNsoBBMend33Arb4EtPt5dX7KeeVof8tL0trDRRA==", "shasum": "8ab94852bf348badc866af85bd852221f0961256", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTdCRA9TVsSAnZWagAA8t8P/jTykAqmGQw7aSR2aPDO\n9h+WpidnZcz7OxUinpUgHYXla2I7H7xTN5GlGjU4njVzJD7TqEyqx6K5GWQL\noQve7oMqvwKeQcUPDIlsSkGZoIeclq5aMEZRCGIPfA4fv2QENTVII9ttUJbT\npUmTAxOhCniIibvpodZ1PDNJxL/9hCvcwGmdHSTq4pJSk8uab4LPn1bcreGk\nxVNmIIyiFu/tl/i6QEhzYuLd/oegS1G91QMPczKRNvDWcSkwy3U6KRd/v8+j\nUEDKNXOby6dxwPcS2nqonUhAcCt3tY6qNxodsHg8MLomu1a3FoXUd7dkPv/U\nPkwtT9iVvk14m6SwLLahQIEPeqjTSgpIXPieQtGFILlaNiOCu+Ys1+yshvqH\nTNdiSYOQzw9EjDctLlLR56x0ZelOlxJ+NaM2CEhs1IDGZkYxoxwOAO//Fcfn\nxbemz9bOS3+SCzfrJzwS9dRDWEAz2CbokEaOnSqJVi9++aTuvt9o6SU9LfB9\nB2xS3wvZ+ckEf9ZhaSWVKNTm6eENNi1g3S5jsQjk8UTkn0TqQ99mWRnyDN74\nNw7BaZ8UB633p1Gr6a2AL3W3fb2BvxgMTbo3oC0SjCtNjgAlN/jpnlgtkT1i\nMoGL8BjjYMc5S1zj8CTIKvWnnnJUTIzJCFLMwnV+gxZzm77aEKctdLq2e/RQ\nHEST\r\n=Jn0c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErdFEPZRvdq9tUdjIeI9eO0khe5uiBXXYiKP6omL0IAAiB9L9cDL7l4g6I/gY/Jivptof6lE9BPYh5kNt5yx4t2ww=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.47_1526342876157_0.15277706280676417"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.48", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cDLSZqUeTFhu11zwyFQw6tFsgH2SrPSXVUisaON1OnMG7A05E4M6rhfDGvv0aLHpX2njSD4nUwVoFsIitJE5Kg==", "shasum": "8b21eb61806660fb9a2e80085eb211aeefa3323c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCsCRA9TVsSAnZWagAAMW8P/j4l8UhoNbANPB1J1U6E\nie5RZ55awucZBRw08RkfHxmc9On2I8VZWI+rQlK+JzRF2d8W8VecpxJrNyzw\nn+NF+C+kkz2wEJWU8GKxjmdu0Q74TpqE1FEoilCW3cWQ1zKNgAROCiGl8EkH\nJU01Sy+o6qYIZKGymoasWjs2ISBZRN6JQ3Iw0cHMdJLx/svP9sa3b7jGjCq0\nPl1JCR5F4Hio84yOe2cYR/j6xtBWvCZ7oX2TmmxjGEajZh1pT/7H0ETfOs/N\ng8jwXuHWPGUTKqvR8/eP+fdl3v5+fGWUSb9D3JECl7L7sS3UxJMamb3MQ/6x\nfrskfeMqkpFUCnWy5K1+X4fNZ16+IGLCPeGUJwBiqCLIeFGAcdPSIaWnN9cG\nwqIB876WFcQ6wWpQz7kdvcbP0HZhfECgxOnf5IcXP7weqSgG2jZjyLLQG+IV\nqJ8SrmUjpSOpkOo8lkikTnSgmkOfHY3/QAJfbM0fmE7sLmXkCNHnT8Pvf8Wz\nYo1gMawxZIUAs2UL/38mLWNXOhzNJrFOHUix7xnrU4YZ5fPSeFPLIll1zvuW\nTEmYvkM16Q70ixmG+wsnVSpnT7oY/nlCfzZcFDOqNA9llirJeaOOg8GvfQ3H\nTIblk58nga7ds2wCxXoqB8ISECBIEsdxYgGHl7+WoqW5BzaceHiABPt1xvLr\nguV1\r\n=kxHm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnq6cQdIgGecynEcfyBa8f86P5HkuzJ2YXj81/Fd27mQIhAO2DTtg99FvDypfoH3SOWS1Fq3fDRSZ4DgSnUlcXC+go"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.48_1527189676088_0.2903949552256806"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.49", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.49", "scripts": {}, "_shasum": "50ee943002aedc9ab3a8d12292bd35dd9edb1df8", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "50ee943002aedc9ab3a8d12292bd35dd9edb1df8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDM4CRA9TVsSAnZWagAArlwP/Ranz8rslO/xgWIyd2B9\ngQmIbqWViNiUOfPVg+LZPEO/dUC331GYrojMasjU6dr6KbyD7iGCvRyBLwf0\njCUfTzvQM7Iclf8ZW1S3grInP3kyzj3a5iuE1j/Un/kSEmPBssAQeanXep8z\nSh8nrbxMvI8RJSKYd6mON0dvxQrwljRDiCf0mJy/FrSe9U3w8qRHfR9ydf0f\nR0h1ZQJe4gHFSoTKbDLV36LF968r5RYTH6oB8IO9/1mIRQUpLErpOQmx2xIQ\nNRxgkjIBO/nCi34xdvODzkkFO8xl8IW6BwzGGJ3ReaZ9HYqA0TK+rCAU/GsV\n9vgUh45YMPlBdFoQcbZdksudtbEJovmCvz292GG9sxf1bngEFBspc/HOtJNR\nhVa7Z85KcuZFdQQh9PzEUZaENFZhyjWwgkEzgLyjib9CSE0lFve9LM/vl6Fq\ne4NTwufsWRrLu77ypmbko7m358GnGW8jIeq5Q/28E4f5jboEKR8AdyxrBVPh\n9uxT35zIY5vC9Rdz9ODZDxH8gq0JjfrH50dLBac6hp3wGUoKdAh7GJpIuqJ4\nhjxkOzHKSKnHLkFrKH8wDtaQ8lnvc5k1zE5voTC6h9UU3jQDnUuKQCtXDnMI\nVbUcwDwlZeLH2pXf8A3Izwt9o3LkPEGQ/LaSfo8P89QrubyqhAQhF9xJxdgi\nGz5z\r\n=GEmK\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-PiGlaj7fOPz/liqiwFgjFHf++/cRCf056c/4LqcvIV80nksh3JoTWUHc3oLzMnpkdWRtj/rz6n68QncQTdoyUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIECroOBLAd64ultZtOjBlr2JBPAj3M24hcP5BiP4R9OsAiBT3Wl8ocfemd1cTwpuRgOiexHBGlYqtkyeCiAoDoinMw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.49_1527264056447_0.284546378449164"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.50", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.50", "dist": {"shasum": "9e23fb95acd2832e0bbfb1962a964a140d61d980", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1461, "integrity": "sha512-s9HcA3+UeZ83pIYxc5suY1FMygiY52rtSw6zRPk5mSWyPXR5sBFksi2Iry7zUyZ8TwuImIh9tTRH0aE7UWMC1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFsG8rRsTlNKqtwQecXa0OO5rlMTg0AjiW3qKDrIn0hAiA/tkwXtTk0AVv3odJBJlr1wvWoODFxj8UELk/wMb2nsQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.50_1528832817513_0.9318460383505418"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.51", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.51", "dist": {"shasum": "6921af1dc3da0fcedde0a61073eec797b8caa707", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1475, "integrity": "sha512-SwpEaCj++dU7wppWneJyJaZz/LQ/8eOIp7qDLqIMIwNSYncIUr6ODNja0X1pFArYd85HlPfFQSjCa6WTgNKgbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSukpKpz9KrGOz3s54KQU91uI6figqf1ujYIxuykc2hAIhAOf7APi9+on2lEQzlUln7o8XC448CQd5ucZskmEU5IBR"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.51_1528838366422_0.6794174070179471"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.52", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.52", "dist": {"shasum": "52d99f0e38cadec8240582f3fb792c8190db24c6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1474, "integrity": "sha512-UXFRKCXEG12/HKpEG4o9/kDCgJFOU8ojTYncbF9bMutWG8iuzyRuLmBHLFa3m2U+t6u1i2hUbK2o5Ejj0aerqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2yWDRB0mxqtjQxsBnrdthuUONOQ76MOg2kxM+SQP11AiAnrBODxDKczkgiw/vUVnh9/hvRQYn3x2wPUxSN/LHjTA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.52_1530838755533_0.8185044921090996"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.53", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.53", "dist": {"shasum": "829bef6f150179e9ed0bb943339f2a31233aa921", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1474, "integrity": "sha512-9/6YL7I6/FaGGnJhoqfiDuMdQjYlDh0AYXXSxV904h9Q0hNcs5p9UyETr9B/U9CXSS6e3lkGSaUXHpI/l8k51g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXTlaqhhDuBfw+EUjwju3T4yEYH7PP+m4TZoqRo5586QIhAKUOcp8uYs4OubE2TyZ5kB9hRyZmv9jheJwFURBANPKq"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.53_1531316405251_0.5274644471273766"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.54", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.54", "dist": {"shasum": "ffac8f64927614762897cc9643495fd38097dd41", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1474, "integrity": "sha512-c2qkw8p8gh7EXNk38mS3RGHgpVBpsEmdit0dpV/q0f9Y741O0BXFiMFBzCILW0DXn9nXE91F4y4OEiWogLG3Ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/OdUcYOlIW1AN/4mEIhiK/nxNyo6AbdytTbyzBEtM+QIge/yC180OIbocyG0UJrlUyJxijNdK4oWqP7mcvoUG5T0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.54_1531763995647_0.6737277512038273"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.55", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.55", "dist": {"shasum": "e72b3857eb80b695c77c3721237b149072cda46b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1474, "integrity": "sha512-itjbx0JdHRTmNEaW9dzLD7+2yK+tC3QlqKnLhMiez35vaXs1iv5TLamANGVgrmxP2LhGbs3xfXbcpEs9bFF4dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCA1H+DqscDccAhxvJUSvLWK1KTjL9HRCfZ9bUJof7DgIhAOrDZCc5qwwHoejuVtLEGwpYYN3zkFGQzNz49PC11btb"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.55_1532815619352_0.23293989839069518"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-beta.56", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-beta.56", "dist": {"shasum": "e784a9f104fa9df3a6273079a8be7ef5085259ea", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-beta.56.tgz", "integrity": "sha512-tC8sGd1RridRn0147GUh2rF1WB+8FnP0siTD0ofuqLYJEbOTYn9NF0WD4DNzwwHwOZMBxgHFy8N/B1sNmEC8SQ==", "fileCount": 5, "unpackedSize": 1474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuBCRA9TVsSAnZWagAAg6wP/2A5GM1h1bxmCYUIJeBj\nQqRwlIQjeWMSH7lTWygacd0w8hN7BSayEmhjMpVo7O29U7m1Wsj8+cyOVQVO\no29FVzjadQzr6Zuu8Z7i8NjSipEvCcGIC14M3/lsxLBpkNTDMMnfiRQAkMgH\nEeLPgk6op//SB76hthY7Q6joUQOWl5/rhTFwg+FiegtrkldbdIShd4Nq70d6\n67IJBo+vegTg/5Om5LXwHCMA/b8ljP5lJYkb37EHwdIpR2k+Zx0I8i4w5SzQ\nDjZTje85tO9wJNfVGE/2g1JK1eszqMGjHnT3gs+NhmgSvN/1mhx7DW2gly8S\n3xwzvCS0WgUMOHFB9Gt+C8lfJKVDgSM/ttuJ7IkeVr16VxXU5Ix/yDlSTog9\nvQRcmO1CnpLRwn+5rhgGnmuftXcy2AHAGlSBSIc+yHOFGR3hSEfhgrzz/MuU\nkwYfj/yJyPA5NhZzxmvmg29qXpFFFsrAxmJXCKu+xYB/3Sr8WLFSEa2d2vHE\nNuzhdYqvYKRCBi1sn1aTZUy7mbU4LP9afQ+33nYKgW1QGAczP7BGrYoBU2t2\nd0z7+z9gVD7exO/UsAsgF8k6O+Y9rqyLhdZolrwlykfWxO11jhI3Gy3ES2Zt\nvI6cyA+Ss7fNSl/BvCTHBGH4AbsGYCq5P5VFlLRFrUX5ofPKUPlC4tSgAy7m\nxVnZ\r\n=+Hxi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFd1gRFbWV91LlQZtPUt/qjwxM9ejzONBIamfUVjAkbmAiEA/YJnIWlHkcjPLDvilbS4U3//DIy5UbfOvOr2omV6J30="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-beta.56_1533344640653_0.8246557617828334"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-rc.0", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-rc.0", "dist": {"shasum": "780ceae4d07c0fc7075da29c3469547b7a63480a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-rc.0.tgz", "integrity": "sha512-iv63+PBQtsTyoMhUmi7N4lSRsd5rzHWUhyz+VfNXmm08bpfTe+qHgQsvAFfy9Od6sYqh87y8glstie4ZKy4JJA==", "fileCount": 5, "unpackedSize": 1465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRVCRA9TVsSAnZWagAA8OIP/11CJlx0FM+bqH+NB22C\nrRQekKPUDNE36iPQJmxQNbvIHaUCXRDMwGYprdodcCPSXZvpJvIhJeWxLoMS\nk+2QxdQVN+3XBT3OqztoEhXET/hAuUCi34BgAgU3D0eH6H0RS8+2Ci9aS1BW\nKcLekcWd77wCTnOF4tiTSv0PzsciooPhBy35AJZMngGLAwp/x4f7T3imAhHg\n2NcZWArAcQyEP037KiqC2NLtOzz5W8kEYR+QvBZnF6SLpfFUA6ACCsVlwSHY\nq7G0mPl5RGQ5Aq+VjV1mSHVnOhnn3c7DyX2JDQyx9kuo1g8KvX8HLubiXB77\nmv4YYuNQVzJZvfRLNz553CQfi2YxJDvM7mZJ9TEKBVYYbrT2WBS1Tcqm2S2N\npPqcLBqIWdGUmTp7aduhas9D+LLxx+hHlXUKXQIVc5epEUVjt3oxrx7crY9V\nNswLRYuW3BlA75LDN6Q0thSExgkdp3ebC0GoeeY9MZUzM7JhcXdZ2Gv4W9zx\n3Cl1T8PX9XCyp8uHjBxFT60iYVa3EgoWpXHZhgisnUeV78J45eUQfgR0OyD8\nh+LvIAQPbq2TBpzsl+o3TXOJKwS/PDhbrDjSJq1KAFxH4j9harvr0uD+mfxc\nJlRi8xPzAA4NFj9zFv0cDY/ZwDADBFcZXPG/3sjyAi95hZgtjReL1JlqXsXk\nKNnq\r\n=VnOp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGXctNNEjG6tKKccFkyMz0YWrwWZy9kr3ZM5cwfV3CkQAiEA0Pt9+vJa4FplJUa23ACE4ZcM50IE2nN6SHitvBTQfA4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-rc.0_1533830228922_0.5224052510103523"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-rc.1", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-rc.1", "dist": {"shasum": "71d016f1a241d5e735b120f6cb94b8c57d53d255", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-rc.1.tgz", "integrity": "sha512-2F5FYc89TCrqE/8+qFlr5jVMTHfkhEOg9JUx+GXI3inW2OfcY+J6bN8EDc8PLz84PHaR8W630YOuh2PveJu3WA==", "fileCount": 5, "unpackedSize": 1446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7jCRA9TVsSAnZWagAA1BEP/R0mcwtHp2uaLPSfcqN9\nZDPHr0R24J68/XehjOh4iXROc0HstCojBvljc/FITVUJ4lDDtLZ2UTdkRHpf\nuggNe2twTT17rlqJdP+LBcr7lZpetltlP5gpT3HtUO19Th2IvQPf/SQazM20\nIZ+4zkiTkv1nmjbtYfj4Fadr46tcteX3tMg0aOansM7XHQwE/r45FrlVo7Sv\n9NXuwedBbcYa/rXxAW5FMSoWo47R+PKCPCfzSDyOvJ8zIfrUBJMLJjPaRiot\nGxxOUJcGlusVv3Mckat2FjKexzwfmYQJ3mvuAFU3uxAOZ70HHwkMhKmDHu17\np+EzPuYh+5wo8+NmGHXAgD5gwUlvcsWRyCFgSUpb2kVFaX9y8CUzT/J4slvb\nAJ+2/MJzEQnulCW7yRXyFJwU/fXW7HasKQifN3sfDxWSg63edqlrTBoy6GU2\nDqp1KMS2JvAhrdKgcYHJnktsDYnfBlpS+ycKnZTXe5NVWH0FH0Qo5K3VvpfS\nGPYtooIbW+F1jwxadDn8NzNUF39VlaNj296psDBtqmYX49EwrXhqOJ4RMmSp\n4tSpWVFMI1OAT2WpoRGw97w5dAw9/DC7/8tk63HLlXw8aRcAYSRsUTtfZGgJ\nijA+oMmT2K9NDn4FMUH4Pypp/ntjiBdQgqfHHMKu+rzLNMZYAkqhXIQl3pje\n/hTU\r\n=qLyy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQ5ZeCqgq/JpAcKUiY2RKVQvoCC6kuUgKlOAsUELJ+0AiAxIuSZgvP8oCm6aSEGUpATrkI5zwCVczLX89KVpf/vnQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-rc.1_1533845218549_0.989001564675327"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-rc.2", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-async-generators@7.0.0-rc.2", "dist": {"shasum": "2d1726dd0b4d375e1c16fcb983ab4d89c0eeb2b3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-rc.2.tgz", "integrity": "sha512-ZG9eCI+3RJVYZL/i794SsW0gFXj75nF+Kk14XvhxyvVAiT7DP1z+iMcNckr0dzZ4pvEymVCVxkU++X1dkgQQow==", "fileCount": 5, "unpackedSize": 1446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaTCRA9TVsSAnZWagAA+8cQAKLnmYPhlWuO/nCeaMtm\nuk6sKu5rtPiPw4iFTI1psYplS4YC233hd92xJyCHB2HFsbZTuQtJyoJOIeEE\nRaq8/Rc2e6NnvwhBfQJAmQzXSgXfPHxg7gYYituF57VUqtH7+yq7RILp00Yb\ntDGcXHjmHKyyeZ1vaAAg/DI4X7/sL5j8BYTNgVUIwuIPlPd8ZWDckRymjWlc\nHH7Q7K1vkUOuLEK8khVKSekjuPdDHrwNLZrDfxFz/kRdoWXSK2n/6vjUn7Ok\nFXMGgWMNTksWpP/+RX/IhLqUY9NLEfFu/zbFvvkuxugfMLP9VTolREqgfg+Q\nVlBNhU/A6uP5qJd91g/o7xIWPlirRsd0pNIdqmOwRLFggmxFER26VHqvRxta\nQZMpQAJNmp2PjriipHVrZbYeEHE7otCfHScxuEiEdL1udrNZkAFFDwkodOY5\nfyrkW+cxUNaHQPUULd+5asH5QS1bX1316Y7esJ2bJF+0mpYPHIPRevqXbyNb\njmjzy9Woy6iwc06EGvOlcWRB2PELJEWwunMP8o8Z5TWwAIkuzo8IiGgAaRBB\nuloTiVAf/bVx+BcazEoWtpUIrTrVpnuQEvuPVT/bSYvOidgAjo+ZRLtSSWgj\njt9FpZFY7hoaOiGymaeW2POalXozKLnCItK40Mmw5ic5HUX6QFtnYrJN0YmC\n8148\r\n=rsEq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGYJPqEYoMcDPbSShl0fDixWONEaGmyG7WslTcbTbCieAiEA/LsuRw8nWpEIzGQI8UIONCRmijL0OfKAvVdiMBPKLhs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-rc.2_1534879379207_0.9334265296566993"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-rc.3", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-async-generators@7.0.0-rc.3", "dist": {"shasum": "7d768f8bb18597781ade989c87519181b69764bf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-rc.3.tgz", "integrity": "sha512-PQH83pVU55hBpmTdbLz00asO7w6iFCyWQ23JuKcraLBANoiDXxSyamoRibafIAbEZDUZBbqq6VmqCIZ0B2pNew==", "fileCount": 6, "unpackedSize": 2545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElCCRA9TVsSAnZWagAAUDoQAIUtU0G3qN5N+5N/nLlH\neugfLS8JE1XuJEMAJGHO91WfxGvNWQp5sZmQyHLciMeHSMox6erunucvKJvL\nKMC6PwdsXdoyGZiXiHRLuWyXpNorY5TYyue/rt03nlO11OT4+/KGkP7tu7+1\nhsDkkj58SR/aLRfgVpgK/tXdPpAikTmTvivZb37jcZmQJTMykg4xVNb88CAB\ncZ99242GK1vsuq++NKJDxw+szWPpMIq488HTVIVqb16Ai9mas06+rS9IoPPJ\ntJn5oaFILc6/8JBIwpoptjH8XUkMEeX110Iil9IOy3KOE74CUU6PEb8QmDkA\nYepNT++gbDpntFcsVVf445yl0896CvobMXrwJa6Uh/hdTUTCRJjh8BpTpGtJ\nVgPcoA58DMIAUZ/zMQIgoMFaCI0dzZMVR7S5VvifzmTJ/1plUpw/6xoR2wyb\nOEtOXrpBI7OBJ1i2auUtumd2ixMj9/Kx6CoTiyg+pe3FnHfAqdOHXhPIbdA5\nHeCEdaVT2w/PEiekS6n2MUaGp6CHSOvy/KbXn6XzjZKy4bddo9rZy6GdpuM+\nX4l1bX0A3pkRz6p0TlHVc2xKpwxc9nK2Gr5qGfcHyHULli/SATKNluR9t0IY\npqXD0ZAI9R/kFAllLqQ2jJdqXnRcbAuN+Jc8kqEAMh66oRueFtPKoq7eeD5K\nzX2M\r\n=XstT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBafad2kfetTH0B5yNBSMN+93xHpUqMHwnnJe4OTKB3bAiBnmjq3zgR2K3x+jrkXLxfsUMttKojykfH8mYsD8QGEcQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-rc.3_1535134017601_0.2623042836493803"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0-rc.4", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-async-generators@7.0.0-rc.4", "dist": {"shasum": "edd57c4ba51806e904a28509d621cfc7146f387a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0-rc.4.tgz", "integrity": "sha512-B+4ocLKGZ8+PTlRADv7efyNJZManuk/3RvH3XJMIREuK4ZrgfMvYWbNrHLYqQmWFAq+EUUZG/QLzr29gDm2BCQ==", "fileCount": 6, "unpackedSize": 2547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCofCRA9TVsSAnZWagAAircP/R1yPicblnGZghxpNQwz\n6yYhwMZN5yJySjpLmMixewOiIfZqarVHmNSGlQbI6tgc+5Z7d+J5XMmHsOmy\nEEeQ41IIztTuvImmQZ44IfUkFtFGqtU74Q8/MMGGpEsLwbX2hkV3jakXKHLQ\n7F1Yvvu3h5V7G61ebEEJANQPCpUljQhMMA8lx6zh0sEKUmjX6pbzXKaKg4Vy\nQ8vy8OTXbCU5cxDwZv8yPg2hQ4P1kbi3g/VB4FcFzmy6LWzVtWTaYERe1rFx\nqJePKX57U9uLmqt6jgm3iFuoQhU1vfsxmyvsh/ItjcZZbybhxB82zGwrfzRR\n0s0xmtTmipYlzVF87dCettkbsvTi8DKOt6WQCrwYWH06T9EknfD7OvR5Uv78\nCVnC8KvMH7JMswJqoT0fF5G2ZzbTyk5jQK2NzDzyrdh20MzOFnJ6F5By+lxu\nLA9ncGapy5d9ydIb7lnvCN8KwSIXrqSF9m/YzpHYBr05IBjEGtiMWTdIf8XM\nqwHGTbdTPQOXOqxZOuRsh7Vh9lLeIRp0wetGNLgxDb6mqr2wzSKEdBeRHZ1r\nzc9uuRHGBXNb5jlCQPDrmZ4nq+dwQa9FUqDXS78xli9JvW0S6gjEzuHJrfNz\n4nmn78S4vZ7WcfEr1JR3t9KzJluySMdbjtQ/xrVusc1K9pT4q7FmaCKrDR+e\nvSw9\r\n=Lp+S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIATYMgIVQoiaHkC2bYuRerTHYm2r9gj0SkSRMyCnEd9WAiEA4E3bsktjnJsLWqRAY33Ao9AGaYUw7k2a9tNK+ozrsVs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0-rc.4_1535388190703_0.6888259816987616"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-async-generators", "version": "7.0.0", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-async-generators\n\n> Allow parsing of async generator functions\n\nSee our website [@babel/plugin-syntax-async-generators](https://babeljs.io/docs/en/next/babel-plugin-syntax-async-generators.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-async-generators\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-async-generators --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-async-generators@7.0.0", "dist": {"shasum": "bf0891dcdbf59558359d0c626fdc9490e20bc13c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0.tgz", "integrity": "sha512-im7ged00ddGKAjcZgewXmp1vxSZQQywuQXe2B1A7kajjZmDeY/ekMPmWr9zJgveSaQH0k7BcGrojQhcK06l0zA==", "fileCount": 6, "unpackedSize": 2532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHA8CRA9TVsSAnZWagAAhxwP/izuUP0bWDYVrjNo3mx7\nJT4z8V+2WmoIJXYpZXE6yTXCdH9hKWJ5YTW/pDxnu+bzTDDHbFtBCFob1cKe\nBF+otwVZyh2e1eOlgVR9yywxNV/DP05JNlaYNUgpVJgOs1A6Q14Gkrww2rz0\ni6X+aD3KjBIgFisAZWunK7NXC8YGSOpRtlzSRdVG2jp3p7+Wc9FeTESHEKAX\n4wvf83iYWH9TCAJA0X/Z2yloJGG27iX/A9+SWOyt1T43AVUq28vKrASduzw9\npoveO0h5qrGmcwY2C+qSjQxqoIOYlC+lcyJ7xI1w+CrgUjwCVWK7jDiZkqIW\n4Sye0DeYsvtC8/701PqKfbdaD2J0b7FW0pEQjoeJgFhhA7iURNjSO9jwZD9g\najjFs5AW4pXn0y377un1XgwDJ5kGbbWNCTz7udGzAiYrd57fn9ENWWua+Vto\nqb7q2OrXck40WYeCZQZ9mH3miJI1TWJBThg1Cl3jh8Ml50TNqmGqfBxT0hTh\n7CkiRYcDYpv0F9p8/sbZqm9mmKNwE37R60NdH5pUyc6oyE4mLBWT20Pnzel0\nFkTn3UNjBPN/86agFpIql8kn7pnSrDNOgkfjIUTAuRE5jDfLPipw2BKhAA8X\nAWtcykyH1S9CTIbH2MsBLsAL53BvnhR8tafMiDXFGD/WezxLAaqSdIFKUxKK\n1iX+\r\n=I9i6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNuy/Fp7sOBio4AUUzRxpZ8xisMfdu9lB2O6eKkiCfZgIhAOrKb6ZGsyO9M8LisoMvqKC39g8aBm7wPljJiu4mJJvO"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.0.0_1535406139579_0.26920394504876577"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-async-generators", "version": "7.2.0", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-async-generators@7.2.0", "dist": {"shasum": "69e1f0db34c6f5a0cf7e2b3323bf159a76c8cb7f", "integrity": "sha512-1ZrIRBv2t0GSlcwVoQ6VgSLpLgiN/FVQUzt9znxo7v2Ov4jJrs8RY8tv0wvDmFN3qIdMKWrmMMW6yZ0G19MfGg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX0QCRA9TVsSAnZWagAA0iYP/2qRT9f2vZQMoE0d5hdM\nC9J07n4Iz8J/8gn2wSF3mSjhGoStDAovo5klW8OYTO4VevKpIDw/GXEPJCoe\np4LBIqBzBvxkQaT4KFV2ylV/RC8LPdJR9U7xI6a48lJP0KF4MaCALGtqytUy\nTh0w4Pf5jR91g4/zk8VEnVHwXozjz+fIPczaNn9W+XWXIQ61A/9rOLyfhUf5\ngY96AqKiLvx7b3cSYOP8QFfcGkTblSY4TOpY2HpfSTv0sEKRqLHMoSE3VGtQ\nqn7TcZFOttZFdSBFBtg3s1t7Pfpo7bIMUwQKb4J96mL9jZVFl0mW5RtKO07/\n3DjGrzwcEvAouONxLFYuLGUR1+jsqBk0VninbbX8G5o01fTmN5ybjLa38W/U\nNGkQopbRaFOGhD/m9I2VMSGkOLy3LxldMUbqu5v+zFuyoZQttqf+TmA6O02y\nmISwBVCEVjyiVPnseMn4ZYV0+hdCWTKzkD2dRTd2Y8kNUAVl0rEgCu36vG4l\nitR7aRTTNUJ+K1+Q6l8G34Ez0CwBXg91He84V1TRAg2Je8CQwvISbeyVS8ka\nMwwCwyezinBOycZlMIjS/AoM7HiiZ8KWWGD8VOGwD6H+9IbfD85TzPHXbVRS\nMM9MLHPAjUu0+reOeueZermf7Gp931nAe5tZ7Lb/65L9QKT27+E/lsgTc9yo\nBOCw\r\n=bbTX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHA6d8P/ZnlPAE2YMmCXUxJKCZ77/NnakNwpAK3mnOP1AiEAqcEm2pUth2WK6PtNMeHs+KAlKzRwQI3F5szQUMRIiXE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.2.0_1543863568088_0.1456483930877266"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-async-generators", "version": "7.7.4", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-async-generators@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-Li4+EjSpBgxcsmeEF8IFcfV/+yJGxHXDirDkEoyFjumuwbmfCVHUt0HuowD/iGM7OhIRyXJH9YXxqiH6N815+g==", "shasum": "331aaf310a10c80c44a66b238b6e49132bd3c889", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/iCRA9TVsSAnZWagAAi/kP/jHOVisy3BGkFSDm7o+0\ngW+ZEsf5cLA78CXp7CheNAAjRlPjlK4WffYcs3KRaow9jT3gzDKcScZLEvvO\n6KYmViD4GqOMpVgPbShAyNxjgGj/Ftw1wAPIgEvKmnI2vAt/vy87ddh22cEO\n3Q/Y15UWjy7gBEt+MCsbaV7BMx0pNvhdL0rQxezUkBv62Wcu7rL8QiqqN616\ndXKMsrjl0hmcBoIsqM5Xy0xi7C3LIRhw4a5JTBUCn5OaPOxVltBRkE8LZYo/\nkW9Byx+opRpWUl8a91vuq7ZY3IvVjyPjNzUMpevZjV/EvhQ3a7E/AHPWkLid\nxo07GlU9BklIhrW5Y8aQGuWiPKbTinxHN+KrdZQPrqOatBycJ+K0Y+04eaSs\nenz5n41MkfDNm0qXbbQ4yiYq3oSRrzD9srQAYm4t+VD741Sf3FleUqSizJza\njVmfD0c7m8Vl5dp7sKw7Xm1yRHp+exxKyQb/yo9ByXpkPNr93B2k/zQqYE0J\n+OpQffELaTOpOxlHGw0MxQFD4x9wuQ/TK8CKSIfNFBj62uzzScMjxvyZBw+G\nNlcDfZdNeyWAtd76/YCfkkQ+ZzU5h5UKx+v5vR9cEOEwFO2VYBdQPgswPaLz\nPdbYjAjNWmm0rb4FvbU91WsXF+7y/nv+WJ/iL/t+bX+6KnS1q6AFt7XMhpqc\nHsxB\r\n=3qF/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFTBvhofwybzYxl65AAIml9KjYILV8vkmmjAUKgtC1sgIhALeOp1MdBNb3FoOFaEi0WCXlYThWD5pWoJNidcL59eeT"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.7.4_1574465506514_0.1596251491120626"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-async-generators", "version": "7.8.0", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-async-generators@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-a8w8k7pK8nYhem07rXdAq03T+DlTX8LFojUptrh9JEx80AgLqGiuoFIyQOGTWif39kFnDOQqbzl1s6KQqrfV+A==", "shasum": "e6c3dba5a61ecf72ba00a3f3f5f1234989a58e6a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVLCRA9TVsSAnZWagAAtTAP/2N94Lm6a3j+xnmwknbB\n8EGUKh901Yhk9hPzyIkM9u3xjvDoWuF9I3v3ZDyorfeUppTnAfv/pZx5xjk7\n6clnqaPNkQ0cWlfTVLZiJVpjqQBcPJCtpLeZ+5IUkirv1ycOMbpWOP6f0pIU\n65lidEd7GgRbDwh6i6OFlDIf5PEeL42ZBOXeFCDwVAcDZ+coVpatTX5iEqrT\nuyc/pP+L+dybi8m7gmV9tCwbiEh+WWFWN/6wuQEde85BX8CsGGvtofbSRKRe\nuPpQRBf9TWX6v5W9RNKv10AKnaxCZaFyy161Vg1/Bd0YKKtevXlzQXxwcVtP\nLQP6g2AGnTv6EKscjxkFd+DI9tqIVlsIRZ7a7s5+jDsJ/QSnRn/c+jUayeCZ\ngCkofBLXwP4FK/EHbxLJT/4jEuw7Es3sNTd2rfk6D50r2z9gsWSAqKziRiXR\nmUInh+yR82i4a2S2UM5uSdqFDsl0f1FjRiV5FWNBRx7PJG0fMeH5Fc5WoDCh\noHGKRn0IgVyEhm9+EFh1jZqUx40Z8CtKpx1rGBJ3ydsjs4dqyVZQJZDu9FA+\nU6hAbyNo/cCpphJls1GisEuOoWNpr0GcpsCW3CXbPZkQwfw4TScVO93l/O30\n5VfJzsRwqhRgMuHlNNVacncMo7pxVm0vrrX4abzCuzlth4MZUhCEfbPHpznR\nMp55\r\n=S4de\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1Pge/AxJ89CL+5EWpVfJFt5W3CSh66rqaWFv4UmKUjAIgVo9OXp7kztfmdDp+RSoTM2IpdIOZCtNBjxXzw9QsG4A="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.8.0_1578788171363_0.49073120066507236"}, "_hasShrinkwrap": false}, "7.8.4": {"name": "@babel/plugin-syntax-async-generators", "version": "7.8.4", "description": "Allow parsing of async generator functions", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-async-generators@7.8.4", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "shasum": "a983fb1aeb2ec3f6ed042a210f640e90e786fe0d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "fileCount": 4, "unpackedSize": 2524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN4FCRA9TVsSAnZWagAA/oMP/161Dzg5Hdg58RZW2OCu\nflLVRYrrzP1WFQE6iQOwArIPOjpAVmp4cdrxAhm+gYr9M94pWpv8+JHrxb3/\neLHZqbAsE7GTIQEijWdbhrjqx19kky58uudgGh6nri166l8A3zpYfDt2WHfU\nPTc+yAFQRHKzxuLjQpO/f72L/GOBpY4DtZbi4k/i1vafJC9lJ0R+tGpejMbf\n1oadeHZEKTMsa6d1Mr4xJzaEbBuDBuQzuyim3owxEWKZs3At9gVeqKXkhM2n\nbErwUZe/nCO4RKh3t7hmoCzJA9Bp80d5En1SveAFFjc4EeH/bEXNNhFXllsS\nA+3OXxl8IRJAOnUoLyzEK3F9nMNF160q5T5YOYhsIWabgdIp9qlQlbNMUHmf\n2FUOgbAgjsXWYkDRL8QUnL2wCqRUNhY2jVBS4ke5fD2tqv84h1Tk49STrYTu\nHjF/G8Ad7NYl98C9BKD7OHIUs29cPZrpZzjJdnbt6nvKtT+Obb/ydlevwF8n\nw86quvNXx5nMSUddtvDzrvmizlAf2rfYd2SpFiHSG7YUMcBl2XtwZBJBxMIU\n4AU3WtpHhyFV+e7XEC4o28cPCY/T9aMs446vyXgXS8MP4D0baLs5PqDeuZMy\nS6z0cBZdD97JAAUI+YlMnHtb/p3cwgNEvDU6lXa2QRyBT0+O+3UxJwaqMItx\n1VOg\r\n=Blt9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDZTkFwwIb3kDUdmAbiXaqt03tNDk925c6IxXssOc3RnwIfJMObcjHKMY8RAsysMEG5JizAV2RoM+Bw9Vu0EqFGEw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-async-generators_7.8.4_1578950149169_0.5923932659882423"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-async-generators\n\n> Allow parsing of async generator functions\n\nSee our website [@babel/plugin-syntax-async-generators](https://babeljs.io/docs/en/next/babel-plugin-syntax-async-generators.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-async-generators\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-async-generators --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:00:58.394Z", "created": "2017-10-30T18:34:11.113Z", "7.0.0-beta.4": "2017-10-30T18:34:11.113Z", "7.0.0-beta.5": "2017-10-30T20:55:56.084Z", "7.0.0-beta.31": "2017-11-03T20:03:09.614Z", "7.0.0-beta.32": "2017-11-12T13:32:58.269Z", "7.0.0-beta.33": "2017-12-01T14:17:06.191Z", "7.0.0-beta.34": "2017-12-02T14:39:00.003Z", "7.0.0-beta.35": "2017-12-14T21:47:31.177Z", "7.0.0-beta.36": "2017-12-25T19:04:14.754Z", "7.0.0-beta.37": "2018-01-08T16:02:18.935Z", "7.0.0-beta.38": "2018-01-17T16:31:36.987Z", "7.0.0-beta.39": "2018-01-30T20:27:23.559Z", "7.0.0-beta.40": "2018-02-12T16:36:45.539Z", "7.0.0-beta.41": "2018-03-14T16:25:48.915Z", "7.0.0-beta.42": "2018-03-15T20:50:23.765Z", "7.0.0-beta.43": "2018-04-02T16:48:12.010Z", "7.0.0-beta.44": "2018-04-02T22:19:53.742Z", "7.0.0-beta.45": "2018-04-23T01:56:01.658Z", "7.0.0-beta.46": "2018-04-23T04:30:26.453Z", "7.0.0-beta.47": "2018-05-15T00:07:56.238Z", "7.0.0-beta.48": "2018-05-24T19:21:16.141Z", "7.0.0-beta.49": "2018-05-25T16:00:56.508Z", "7.0.0-beta.50": "2018-06-12T19:46:57.670Z", "7.0.0-beta.51": "2018-06-12T21:19:26.482Z", "7.0.0-beta.52": "2018-07-06T00:59:15.611Z", "7.0.0-beta.53": "2018-07-11T13:40:05.343Z", "7.0.0-beta.54": "2018-07-16T17:59:55.705Z", "7.0.0-beta.55": "2018-07-28T22:06:59.401Z", "7.0.0-beta.56": "2018-08-04T01:04:00.750Z", "7.0.0-rc.0": "2018-08-09T15:57:09.000Z", "7.0.0-rc.1": "2018-08-09T20:06:58.610Z", "7.0.0-rc.2": "2018-08-21T19:22:59.315Z", "7.0.0-rc.3": "2018-08-24T18:06:57.650Z", "7.0.0-rc.4": "2018-08-27T16:43:10.806Z", "7.0.0": "2018-08-27T21:42:19.633Z", "7.2.0": "2018-12-03T18:59:28.246Z", "7.7.4": "2019-11-22T23:31:46.682Z", "7.8.0": "2020-01-12T00:16:11.480Z", "7.8.3": "2020-01-13T20:57:44.313Z", "7.8.4": "2020-01-13T21:15:49.301Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "license": "MIT", "readmeFilename": "README.md"}