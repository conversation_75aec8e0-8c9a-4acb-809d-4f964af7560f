{"_id": "brace-expansion", "_rev": "41-a1af4f19c907d899ab380311fe076641", "name": "brace-expansion", "dist-tags": {"latest": "4.0.1", "3.x": "3.0.1", "2.x": "2.0.2", "1.x": "1.1.12"}, "versions": {"0.0.0": {"name": "brace-expansion", "version": "0.0.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@0.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "b2142015e8ee12d4cdae2a23908d28d44c2baa9f", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-0.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>tiom0CcPQjWOvuqQsl/jP/GbJYO9oRJwJiZcB0f2e4PM3EAwoxAzTJBOcUJ0SSlKShb0wB5bkpzoH4YgbYg==", "signatures": [{"sig": "MEYCIQDIz68OppfZj5bE9pvkPOiULQUHgRnY5X0txTOV7vNCFQIhAI4tRdbxB4npUxcuFaodGaFxxqwJMGQt0kUIqjs5WvNq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Brace expansion as known from sh/bash", "directories": {}, "dependencies": {"concat-map": "0.0.0", "balanced-match": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}}, "1.0.0": {"name": "brace-expansion", "version": "1.0.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.0.0.tgz", "integrity": "sha512-lpqC6FxtM5XVWHdevRkMRPWSpsoLOWqurCALDPKm0VnLHf3DQ2rqFO8WBc6ierDnXeiMnCzwtDl6PgZrPY7xxA==", "signatures": [{"sig": "MEUCIHiiZuTN8rlOZuQfGyNVObHLXk06S8FCymzz59nrA6kIAiEAkQNqXm+yIrcqfrgeOfchnIebTgu7lM/7ohwc2jaPqnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "gitHead": "55329dcf69a61c2ea76320c5e87a56de48682c80", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"concat-map": "0.0.0", "balanced-match": "^0.2.0"}, "devDependencies": {"tape": "~1.1.1"}}, "1.0.1": {"name": "brace-expansion", "version": "1.0.1", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.0.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "817708d72ab27a8c312d25efababaea963439ed5", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.0.1.tgz", "integrity": "sha512-agencL/m7vghsxEHLqdfg0cz3hHCEo46p+VCthmo2ldRTsmW7DANziRJnYCzGPT2Rc6OaYoNmiC9Fq/6laK8Lg==", "signatures": [{"sig": "MEYCIQCGL4FbAvj1GETCGq8al+snilcC+LBgaWobxTbx8NWHZAIhANmwsSB+I/6UwWGG0pJTZ61b1BqcCFCycRpzUjMB5IUG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "817708d72ab27a8c312d25efababaea963439ed5", "gitHead": "ceba9627f19c590feb7df404e1d6c41f8c01b93a", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {"concat-map": "0.0.0", "balanced-match": "^0.2.0"}, "devDependencies": {"tape": "~1.1.1"}}, "1.1.0": {"name": "brace-expansion", "version": "1.1.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.0.tgz", "integrity": "sha512-jW1t9kL3kiXzovHnEgYNuYMnF+hHB1TlyK2wox32dPrWRvwNEJlXz3NdB5mdjFK1Pom22qVVvpGXN2hICWmvGw==", "signatures": [{"sig": "MEYCIQDAPkyBXMMTJXxO2G60LgymQM/x1fVRSoTL+X3M2ijMQAIhAIy8QqTEZzxuJKSFpS2zCFxK5+XDyIaYWZeOlil5bFAa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "gitHead": "b5fa3b1c74e5e2dba2d0efa19b28335641bc1164", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.1.10", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.2.0"}, "devDependencies": {"tape": "^3.0.3"}}, "1.1.1": {"name": "brace-expansion", "version": "1.1.1", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.1.tgz", "integrity": "sha512-8sehXzl+5+hVq+azy8bdvi/vdY1DA0eKIM+k+wK4XqBAy3e0khAcxN+CMIf6QObpDLR4LXBBH8eRRR500WDidg==", "signatures": [{"sig": "MEUCIAMsztomnUx31iO0XaIv4hcdVeg9nUiL0BNflX2zT1mKAiEAnKJYmKMf1DIPlz3tsslDFKMUMZBV5K6i41Erb0rdfOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "gitHead": "f50da498166d76ea570cf3b30179f01f0f119612", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.2.0"}, "devDependencies": {"tape": "^3.0.3"}}, "1.1.2": {"name": "brace-expansion", "version": "1.1.2", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.2.tgz", "integrity": "sha512-QY1LGlHZzEwE7NbolI6UYCtLE2zp0I49Cx7anmMGHjwPcb5E/fN/mk5i6oERkhhx78K/UPNEwLjLhHM3tZwjcw==", "signatures": [{"sig": "MEQCIGzXvMvui4rDxLgCCSSd5sHHHnPnkk+FFuMXmUmTPuasAiAI9w6HqWYhvFwjJ1Lt2kvx9juCc42nu86lNVjZVmiVBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "gitHead": "b03773a30fa516b1374945b68e9acb6253d595fa", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.3.0"}, "devDependencies": {"tape": "4.2.2"}}, "1.1.3": {"name": "brace-expansion", "version": "1.1.3", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.3", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.3.tgz", "integrity": "sha512-JzSkuJYnfzmR0jZiCE/Nbw1I9/NL2Z2diIfhffu5Aq3nihHtfO8CNYcwxmAyTKYKWyte1b1vYBHMVhMbe+WZdw==", "signatures": [{"sig": "MEYCIQDTtnEPVW6mkENQFogGyd+jwVVZk4fv5oQrv59tJZl+LQIhAIDBZRtfmnRz3bd3iBTboDlyyKBBzKcmMQRB4i13N8Aa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "gitHead": "f0da1bb668e655f67b6b2d660c6e1c19e2a6f231", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.3.0"}, "devDependencies": {"tape": "4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.3.tgz_1455216688668_0.948847763473168", "host": "packages-6-west.internal.npmjs.com"}}, "1.1.4": {"name": "brace-expansion", "version": "1.1.4", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.4", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.4.tgz", "integrity": "sha512-wpJYpqGrDNnMWoi1GX8s8C4/SkHCuuLV0Sxlkvc4+rEBTNkUI2xLiUU3McR0b5dVw71Yw50l+sBGhusHNnjFnw==", "signatures": [{"sig": "MEUCIQDv5gzTuC2pQtSUO2lUpdIu+EEkjv5yy57Xfhq9mKbJZAIgNucwl3w78pmRKhcaEEkqf8ALdEUhrSTClfu6fid9vy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "gitHead": "1660b75d0bf03b022e7888b576cd5a4080692c1d", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.4.1"}, "devDependencies": {"tape": "4.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.4.tgz_1462130058897_0.14984136167913675", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.5": {"name": "brace-expansion", "version": "1.1.5", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.5", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.5.tgz", "integrity": "sha512-FtnR1B5L0wpwEeryoTeqAmxrybW2/7BI8lqG9WSk6FxHoPCg5O474xPgWWQkoS7wAilt97IWvz3hDOWtgqMNzg==", "signatures": [{"sig": "MEUCIDcdxKt/UDpxuuF6QUTSAj+Ndice1oRjJYdg0ZT4vFlxAiEA/qg6+kDz31bAvPhuTGit0IkXoFtpj5xgb9i8K7XPmOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "gitHead": "ff31acab078f1bb696ac4c55ca56ea24e6495fb6", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "4.4.5", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.4.1"}, "devDependencies": {"tape": "4.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.5.tgz_1465989660138_0.34528115345165133", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.6": {"name": "brace-expansion", "version": "1.1.6", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.6", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.6.tgz", "integrity": "sha512-do+EUHPJZmz1wYWxOspwBMwgEqs0T5xSClPfYRwug3giEKZoiuMN9Ans1hjT8yZZ1Dkx1oaU4yRe540HKKHA0A==", "signatures": [{"sig": "MEUCIQD32m58z3rzGaG1vElCS5FolKUXPn6odedg6Xfq9KZQOAIgWSBG2qBNxWBr+2EzNkySLFXLqC2Gj9ZQkHSOkPDUabM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "gitHead": "791262fa06625e9c5594cde529a21d82086af5f2", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.4.1"}, "devDependencies": {"tape": "^4.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.6.tgz_1469047715600_0.9362958471756428", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.7": {"name": "brace-expansion", "version": "1.1.7", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.7", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.7.tgz", "integrity": "sha512-ebXXDR1wKKxJNfTM872trAU5hpKduCkTN37ipoxsh5yibWq8FfxiobiHuVlPFkspSSNhrxbPHbM4kGyDGdJ5mg==", "signatures": [{"sig": "MEUCIQDjfkyICBvvj8rQb/0E8LXObvB5Ip4Son+jWmF+agQUewIgYtJplpbk9QT8k8fK4+mvwW/2SG88zw7RyfanboCbDYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "gitHead": "892512024872ca7680554be90f6e8ce065053372", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^0.4.1"}, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.7.tgz_1491552830231_0.7213963181711733", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.8": {"name": "brace-expansion", "version": "1.1.8", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.8", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.8.tgz", "integrity": "sha512-Dnfc9ROAPrkkeLIUweEbh7LFT9Mc53tO/bbM044rKjhgAEyIGKvKXg97PM/kRizZIfUHaROZIoeEaWao+Unzfw==", "signatures": [{"sig": "MEYCIQCnJTT7JQLt62sEnsf0tHq2Bjs0s5hzFPLTKZ0ezxe48wIhAMjCqrWYo5zNLTOR2UuSzCxYcXppfgandM0w69ZeHWpY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "gitHead": "8f59e68bd5c915a0d624e8e39354e1ccf672edf6", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^1.0.0"}, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion-1.1.8.tgz_1497251980593_0.6575565172825009", "host": "s3://npm-registry-packages"}}, "1.1.9": {"name": "brace-expansion", "version": "1.1.9", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "acdc7dde0e939fb3b32fe933336573e2a7dc2b7c", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.9.tgz", "fileCount": 3, "integrity": "sha512-/+o3o6OV1cm3WKrO7U4wykU+ZICE6HiMEuravc2d03NIuM/VaRn5iMcoQ7NyxFXjvpmRICP2EER0YOnh4yIapA==", "signatures": [{"sig": "MEYCIQCAFhLoOH1TGldXfcUQuons91mJSbJrZN7qvWgErbY1lwIhALBOh4f4dcTZ4xMkCIZbI0YlooUneFTZMFuQiPvTFZVW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9867}, "main": "index.js", "gitHead": "0f82dab6708f7c451e4a865b817057bc5a6b3c8e", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "9.0.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_1.1.9_1518170016033_0.0827503901708313", "host": "s3://npm-registry-packages"}}, "1.1.10": {"name": "brace-expansion", "version": "1.1.10", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "5205cdf64c9798c180dc74b7bfc670c3974e6300", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.10.tgz", "fileCount": 4, "integrity": "sha512-u0KjSZq9NOEh36yRmKT/pIYOu0rpGAyUTeUmJgNd1K2tpAaUomh092TZ0fqbBGQc4hz85BVngAiB2mqekvQvIw==", "signatures": [{"sig": "MEYCIQDli7mxJRbuSCfeiMcIL+s+gaQlXvfuResXwhPtt2QeKwIhAOXD8xbx/PBIDoeu5Oy4kLIhozwj20XbJgDGdsYvwrnj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10964}, "main": "index.js", "gitHead": "54a6176731eb223cd3dede1473190d885d6b3648", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "9.0.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_1.1.10_1518210808996_0.14734749523785462", "host": "s3://npm-registry-packages"}}, "1.1.11": {"name": "brace-expansion", "version": "1.1.11", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@1.1.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "3c7fcbf529d87226f3d2f52b966ff5271eb441dd", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "fileCount": 4, "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "signatures": [{"sig": "MEUCIQC2I9J9tPlxp6j/HHQEZt6m3oGHr2r9mzmIpCuNqtxU8AIgEDoaUyizhrLzwPIwhskq7pIaySeBQHqkhwY/BQL5cCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11059}, "main": "index.js", "gitHead": "01a21de7441549d26ac0c0a9ff91385d16e5c21c", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "9.0.0", "dependencies": {"concat-map": "0.0.1", "balanced-match": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_1.1.11_1518248541320_0.33962849281003904", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "brace-expansion", "version": "2.0.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@2.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "3b53b490c803c23a6a5d6c9c8b309879c37c7f98", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-A4GHY1GpcTnp+Elcwp1CbKHY6ZQwwVR7QdjZk4fPetEh7oNBfICu+eLvvVvTEMHgC+SGn+XiLAgGo0MnPPBGOg==", "signatures": [{"sig": "MEUCIQCr5ZL6FcHVP65o923WcJjCEbbjT/6loKJU+zYITXIhbQIgGEfe3/Y91JY20BO7ZulW1OEI8SlP/Xvlh6hngSAJcF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfewZYCRA9TVsSAnZWagAAd70P/2QT8aQ9+pjZQwM2pk0Z\nB/jNeaDu5O0/Y06KZF3Pzcxl9SFVCWfEr+7WP5mqb+R+dbthggNppICoM2Tk\nmilkoIgrUecspuKsvnJ0qJRYDSktSwD1IgcY/V3Yr8jCW5J56tU5SiUozvuj\nl3od5svv9vsPilwIHnMoRS4p00La7dlKK6v6R9QgdIF300jd+F++5GmSZOmj\nRxQslhhmFcM0nxIrJ1Ku06Tino2o8E8R0XzBUZS42uexstrDk9DGTtQmjqUn\nvnR/KRlJVppSdOeQ5P0L1UjvDObub5XUdfRo4JnQDrPrDZMdItLZ8CeoEVPh\nIwBCNCBoeWxbbPgAr4QdYMTpyIidFpMDd2lhNB+UTibold67Of4tbOn5KcOG\nac1lCdmturxz0AkyxawmQDkelpLdnatWdBzwGmPDk/Nh6bCSR03iKEgT3oJI\nu+NtciBopPto2emV8eN6E9yvlpGz8b7qDxi7FgOSYvEZ4Vy7spRpj6mS8PYl\nXbkTFUaNDr1KIMHlvXjeYX1I0MfFeE5u1uWovNS+bRmPYqVo78kRxJmlEL/J\nsOGS+PEnPx2thCA9VU4IZ+uGn1dx5mR28xylmatytWU2o3kXF4clXVeosSOl\nZYBAoXlsicuQxhrLzwvkReVpfCYNYVLTRjjn1eLrrba+M+FxT8ULtj6Z5Zh+\nqlBk\r\n=GlK9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0b6a022491103b806770bc037654744bef3e63be", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"balanced-match": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_2.0.0_1601898071832_0.5293279392460339", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "brace-expansion", "version": "2.0.1", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@2.0.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "1edc459e0f0c548486ecf9fc99f2221364b9a0ae", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "signatures": [{"sig": "MEYCIQDgNFdV3ddgnkb39ucmGYRgdjxRfJEZcnAt+BXCAQaVPgIhAKW05a01tUGrzy0G/gZFOVqMptjiKbs9uy+dDpF0C75F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgM9lGCRA9TVsSAnZWagAAkYIQAIalRvvQqAOlBPALOfU+\nuIHTUBeNj/D6vRuqzPgWQVtUxRpdvXMI/aLxJx38aeZ6WgCvZWBQn3jItTEs\n3H2zWGue5+DAeWvBCqxSjdVV4ai+4EJuyS4+1D1qTm2syzT0aPdYRlhVMA/s\nOpiuPVHF1vqwSwPMCUXNW1sMi4N0qJzpAInYOCQ2NFUFZb5OssTqYQ1bzdl1\nRq/FtfkqOmz7OC/879lo3SCp+uvdXmkkQnSOGVU65HvzJp/NIvsFk5pHwo68\naRXefo/GRnqGFwFYOSqUUlBVjgEJYFdRVrYN+CNHK8iNJ6cphqz3EE1Edl1d\njT1SsFm9dJCqkfz5M/tW03vbMV88MYKhdDff5/Fugz4vcCAKfp+JcJolxUxz\nYXnB/xH/MsIEFIqwfDHYf+HFDZsZk7kJKm5JUciIV9CORiWtHz4d/y+4FYZM\n48okE1VAa5E7DVlGhTEUJUUt05JHztbm4EPklRd4/il61edoL516wp1XryxB\nSG3Jb+wLHH/ZHUQHpqrnBWvs68fxE8848EwiWIPKUk7pP/MtHdftjw2ouPSa\nD3EeHKJirZ3GAJqmwDy/vrOSB5/bQX82dGviV097AdPpnCAH6HJuaXBww+lN\nXgbxcuBiXlMuQAxpmQ578BOIGHnCo9EeauL2Ik9pHissAPUcpFCSUVZvXC3P\nJbt7\r\n=ayXm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b9c0e57027317a8d0a56a7ccee28fc478d847da2", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"balanced-match": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_2.0.1_1614010693500_0.3082242768639887", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "brace-expansion", "version": "3.0.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@3.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "2ba8d16a84bb3b440107587dae0fa59cf8672452", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-P+6OwxY7i0tsp0Xdei2CjvVOQke51REB4c2d2wCckcMn6NBElNqLuzr6PsxFCdJ3i/cpGEkZ/Nng5I7ZkLo0CA==", "signatures": [{"sig": "MEQCIEC9uzfKooJ89Q8QLlD+tzLeFwFe/78sLtWLDO3ReypKAiBnVIsCDBixEed2GXe6+kCRV/O2pdWrYiYU+YT9S1FG4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12214}, "type": "module", "engines": {"node": ">= 18"}, "exports": "./index.js", "gitHead": "b01a637b0578a7c59acc7d8386f11f8d0710b512", "scripts": {"test": "standard --fix && node --test", "bench": "matcha bench/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"balanced-match": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^17.1.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_3.0.0_1696685462916_0.3750340778742729", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "brace-expansion", "version": "4.0.0", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@4.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "bb24b89bf4d4b37d742acac89b65d1a32b379a81", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-l/mOwLWs7BQIgOKrL46dIAbyCKvPV7YJPDspkuc88rHsZRlg3hptUGdU7Trv0VFP4d3xnSGBQrKu5ZvGB7UeIw==", "signatures": [{"sig": "MEUCIQDrhNEy/hwZjHIlsHCYab0+IHgrz7kDfa1w6u/e+kx1EAIgOp8c3E2/Sn13tVi4fV1P31KM5fQX1SqJtVtpcVNa8gI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12770}, "type": "module", "engines": {"node": ">= 18"}, "exports": "./index.js", "gitHead": "6a39bdddcf944374b475d99b0e8292d3727c7ebe", "scripts": {"test": "standard --fix && node --test", "bench": "matcha bench/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"balanced-match": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^17.1.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_4.0.0_1709035002841_0.7308632197804894", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "brace-expansion", "version": "4.0.1", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@4.0.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "3387e13eaa2992025d05ea47308f77e4a8dedd1e", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-YClrbvTCXGe70pU2JiEiPLYXO9gQkyxYeKpJIQHVS/gOs6EWMQP2RYBwjFLNT322Ji8TOC3IMPfsYCedNpzKfA==", "signatures": [{"sig": "MEYCIQDM35BPIuCbJPcYEBCh7CiAu49GUmmIYkH5k35GV1gmCQIhAKwPJK1YnN3VtTML0OB+JNUt9BSXLQMdK/TXeVbrLslu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12775}, "type": "module", "engines": {"node": ">= 18"}, "exports": "./index.js", "gitHead": "c85b8ad3f53d1eb65f4996a495cae61949855f7c", "scripts": {"test": "standard --fix && node --test", "bench": "matcha bench/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"balanced-match": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^17.1.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_4.0.1_1749625463581_0.5120348729969268", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "brace-expansion", "version": "3.0.1", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@3.0.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "ca8272e391c052107d076e60da89c4b8b06bdf3c", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-d+NXbXQ463UPpTMIHWzVzp8x9lG5ALx1cR+0MUo9IztykL5+U5bN07D5rlT+yQGcr1mz7ydxFaw5FdaFYW247w==", "signatures": [{"sig": "MEQCIEhfp8EcrJDnKMI+oXQmK2sWQEVXPhBm/CMyG28znj4wAiB6YiEk1m4Ka4QJFvNPVxa/Qm6fd+nlNUZKH/A4a6zVjg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12262}, "type": "module", "engines": {"node": ">= 18"}, "exports": "./index.js", "gitHead": "a057bebec555a951f378148351ce25c54000d4d2", "scripts": {"test": "standard --fix && node --test", "bench": "matcha bench/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"balanced-match": "^3.0.0"}, "publishConfig": {"tag": "3.x"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"standard": "^17.1.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_3.0.1_1749631452111_0.9477068221541256", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.2": {"name": "brace-expansion", "version": "2.0.2", "keywords": [], "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "brace-expansion@2.0.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/juliangruber/brace-expansion", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dist": {"shasum": "54fc53237a613d854c7bd37463aad17df87214e7", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "fileCount": 5, "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "signatures": [{"sig": "MEQCIHowR6pWiAws0hC5C7DsbWOVB2Ob/pQlQIgpExp7SNSeAiBnxaRTaBM/EMpAPQicV/a54Zg9vvJzXmLkfYtCCGVOxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11534}, "main": "index.js", "gitHead": "a3efcee659ef0fb381e2b50d759c720900580a15", "scripts": {"test": "tape test/*.js", "bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Brace expansion as known from sh/bash", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"balanced-match": "^1.0.0"}, "publishConfig": {"tag": "2.x"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^4.6.0", "@c4312/matcha": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/brace-expansion_2.0.2_1749631716389_0.6531654539004599", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.12": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.12", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "publishConfig": {"tag": "1.x"}, "_id": "brace-expansion@1.1.12", "readmeFilename": "README.md", "gitHead": "44f33b47c5c6a965d507421af43e86cf5971d711", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "shasum": "ab9b454466e5a8cc3a187beaad580412a9c5b843", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "fileCount": 4, "unpackedSize": 11107, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCefRVlAiPWPz42vj2hoqvreB3Job7AZ0kiUGVtB1yl1gIge5j1MzW2qkqi81uRNwZK1QPOMLfb8Xo8f2zlR2sYDV0="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/brace-expansion_1.1.12_1749631977985_0.4250458404269102"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-10-13T12:58:47.118Z", "modified": "2025-06-11T08:52:58.305Z", "0.0.0": "2013-10-13T12:58:50.153Z", "1.0.0": "2014-11-30T09:58:55.317Z", "1.0.1": "2014-12-03T07:58:39.708Z", "1.1.0": "2014-12-16T18:58:15.116Z", "1.1.1": "2015-09-27T21:58:47.098Z", "1.1.2": "2015-11-28T12:58:57.647Z", "1.1.3": "2016-02-11T18:51:31.874Z", "1.1.4": "2016-05-01T19:14:21.252Z", "1.1.5": "2016-06-15T11:21:03.644Z", "1.1.6": "2016-07-20T20:48:37.117Z", "1.1.7": "2017-04-07T08:13:51.907Z", "1.1.8": "2017-06-12T07:19:41.589Z", "1.1.9": "2018-02-09T09:53:36.709Z", "1.1.10": "2018-02-09T21:13:29.675Z", "1.1.11": "2018-02-10T07:42:22.313Z", "2.0.0": "2020-10-05T11:41:11.973Z", "2.0.1": "2021-02-22T16:18:13.617Z", "3.0.0": "2023-10-07T13:31:03.177Z", "4.0.0": "2024-02-27T11:56:43.001Z", "4.0.1": "2025-06-11T07:04:23.771Z", "3.0.1": "2025-06-11T08:44:12.277Z", "2.0.2": "2025-06-11T08:48:36.535Z", "1.1.12": "2025-06-11T08:52:58.148Z"}, "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "author": {"url": "http://juliangruber.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/juliangruber/brace-expansion", "keywords": [], "repository": {"url": "git://github.com/juliangruber/brace-expansion.git", "type": "git"}, "description": "Brace expansion as known from sh/bash", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"fotooo": true, "i-erokhin": true, "sbruchmann": true, "flumpus-dev": true, "shaomingquan": true, "scottfreecode": true}}