{"_id": "didyoume<PERSON>", "_rev": "36-8b28600ef7426487a78150eaa5dc15a0", "name": "didyoume<PERSON>", "description": "Match human-quality input to potential matches by edit distance.", "dist-tags": {"latest": "1.2.2"}, "versions": {"1.1.0": {"name": "didyoume<PERSON>", "version": "1.1.0", "description": "Match human-quality input to potential matches by edit distance.", "homepage": "https://github.com/dcporter/didyoumean.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "main": "./didYouMean-1.1.0.js", "repository": {"type": "git", "url": "https://github.com/dcporter/didyoumean.js"}, "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache", "_id": "didyoumean@1.1.0", "dist": {"shasum": "45668d70f92624249451ee264c4072ec28a7e226", "tarball": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.1.0.tgz", "integrity": "sha512-jRophNrTu6lwlwApr1UpQwZ+OgAnGqfrO7v8qdYFo79VVeKWdN97qWlqmt1wUAqjesCulMhRu0Q3B2G+ND23Mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzXL6efr3G7PQp1n4ZhJB6b5zDTyclAQYoRQk2ymLtVQIgZA7ymO7GSN5ixk4jnfww7PXbb+JvhNvZp05pyw60BkY="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "dcporter", "email": "<EMAIL>"}, "maintainers": [{"name": "dcporter", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "didyoume<PERSON>", "version": "1.2.0", "description": "Match human-quality input to potential matches by edit distance.", "homepage": "https://github.com/dcporter/didyoumean.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "main": "./didYouMean-1.2.0.js", "repository": {"type": "git", "url": "https://github.com/dcporter/didyoumean.js.git"}, "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache", "_id": "didyoumean@1.2.0", "dist": {"shasum": "71256ddd39ff77af2c9aa65598737b1e94ec3751", "tarball": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.0.tgz", "integrity": "sha512-mHu8UTARbWtA++dpbOh/H/IwBOtwmaMKmzhmeTppkn6efwke3XeKraAZnig/vTpKFIwRrzE4+q9t7F/O75LthQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCXPV5AcDKmqQ4NIjPfRaT6HDs31ChUB3m3HQx3C0h0AiBWlrp8+eWejbngF18YxzjC/QEeGVHC3cO5vFUy/MgTdQ=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "dcporter", "email": "<EMAIL>"}, "maintainers": [{"name": "dcporter", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "didyoume<PERSON>", "version": "1.2.1", "description": "Match human-quality input to potential matches by edit distance.", "homepage": "https://github.com/dcporter/didyoumean.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "main": "./didYouMean-1.2.1.js", "repository": {"type": "git", "url": "https://github.com/dcporter/didyoumean.js.git"}, "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache", "_id": "didyoumean@1.2.1", "dist": {"shasum": "e92edfdada6537d484d73c0172fd1eba0c4976ff", "tarball": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.1.tgz", "integrity": "sha512-d<PERSON><PERSON>lbyYW1Oymektz87sYR+bhL24mASDW0cNgs3zbR68JxM8nKD9yQ+ARAr/IOfOARv8SwI9ARA7vGDwW4NWUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBcC4dzEghQB64AxyO4NbakVHwN09zmSIpsNN2VebLcOAiEAuLcDI+kccVDW40MiMlGwouifVDdP2Qe0/F7/5sBeHJc="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "dcporter", "email": "<EMAIL>"}, "maintainers": [{"name": "dcporter", "email": "<EMAIL>"}], "directories": {}}, "1.2.2": {"name": "didyoume<PERSON>", "version": "1.2.2", "description": "Match human-quality input to potential matches by edit distance.", "homepage": "https://github.com/dcporter/didyoumean.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "main": "./didYouMean-1.2.1.js", "repository": {"type": "git", "url": "git+https://github.com/dcporter/didyoumean.js.git"}, "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache-2.0", "gitHead": "f58cc8d9d3d127f31aa88b9e23b6daf4ead34abb", "_id": "didyoumean@1.2.2", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==", "shasum": "989346ffe9e839b4555ecf5666edea0d3e8ad037", "tarball": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz", "fileCount": 5, "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1I/qCRA9TVsSAnZWagAABxwQAIN4TCC+I+8vVhMBQpj1\nT+XedJLRu/hhXWFW2Sun7Lm+LeEwvtijbhBuLiSvn8Wgszhs9QtR2GNyqGzS\nuWzhyGYex3+KJ8UtXtALTSHE0ZmtdZsHhWA67h4bMzh6qiPNkKWapHa/dYWE\nC3jPok+ekvF21UOkQ58voOWhpvjbve+OrKXNya3SJjJ9VfWEgjlTaNJoeH+e\nXTTABj/4FUe9lEcnjaqP1RUPySkdpblGaCk8qOVkRqaAi2rYFy63bLpAbogO\n9T7vLqhWNO1ssCgnseMLH8TGORQNeFg+0MVeA9tQxXdQ1fgNRfctUhizQGrp\n2AT+pfv/1quNSGTZbbu+eCZHDqc9oCPCpFx86+U4QjbZs0i8s1zYdiX4xnG4\n0eVJrq31cVBzCeQqMv9Pha5RRdulUuU9VF77MnF4qEhqXLQFLb6WOIB5VK3O\ni/XNGGwuI+XUFLbZz6BAceuIPfzE8zy1tzNSWb3z49FsrPQtpdEM5kvGgk79\nphw/vMzsRlOrAZL+t1P5yysWnfWSFZYEIvOCoKwAnAq/G4MfzJ3N6h+5XaaT\nlGCISnTdMqmm0rhHbznnG3GA13/eHVZeVLUPfUoYgaTHPwDh4soTN081Jtcc\n2cBpzkdUeaP9FOneefcX6q3Kmt8uzTTwUUkLvGj3t92MJJSBS1zTPmdkNjAw\nprVx\r\n=Yu+O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDU84r/vu5NhVFmfkn/YBup+Rx4yG1je1LYrjWtM6AsrgIhAP6nbhzqPATQerSpcl4cP14w5Mb/f7XZsFpg5owYZmnT"}]}, "_npmUser": {"name": "dcporter", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "dcporter", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/didyoumean_1.2.2_1624543209601_0.5660588049172106"}, "_hasShrinkwrap": false}}, "readme": "didYouMean.js - A simple JavaScript matching engine\n===================================================\n\n[Available on GitHub](https://github.com/dcporter/didyoumean.js).\n\nA super-simple, highly optimized JS library for matching human-quality input to a list of potential\nmatches. You can use it to suggest a misspelled command-line utility option to a user, or to offer\nlinks to nearby valid URLs on your 404 page. (The examples below are taken from a personal project,\nmy [HTML5 business card](http://dcporter.aws.af.cm/me), which uses didYouMean.js to suggest correct\nURLs from misspelled ones, such as [dcporter.aws.af.cm/me/instagarm](http://dcporter.aws.af.cm/me/instagarm).)\nUses the [Levenshtein distance algorithm](https://en.wikipedia.org/wiki/Levenshtein_distance).\n\ndidYouMean.js works in the browser as well as in node.js. To install it for use in node:\n\n```\nnpm install didyoumean\n```\n\n\nExamples\n--------\n\nMatching against a list of strings:\n```\nvar input = 'insargrm'\nvar list = ['facebook', 'twitter', 'instagram', 'linkedin'];\nconsole.log(didYouMean(input, list));\n> 'instagram'\n// The method matches 'insargrm' to 'instagram'.\n\ninput = 'google plus';\nconsole.log(didYouMean(input, list));\n> null\n// The method was unable to find 'google plus' in the list of options.\n```\n\nMatching against a list of objects:\n```\nvar input = 'insargrm';\nvar list = [ { id: 'facebook' }, { id: 'twitter' }, { id: 'instagram' }, { id: 'linkedin' } ];\nvar key = 'id';\nconsole.log(didYouMean(input, list, key));\n> 'instagram'\n// The method returns the matching value.\n\ndidYouMean.returnWinningObject = true;\nconsole.log(didYouMean(input, list, key));\n> { id: 'instagram' }\n// The method returns the matching object.\n```\n\n\ndidYouMean(str, list, [key])\n----------------------------\n\n- str: The string input to match.\n- list: An array of strings or objects to match against.\n- key (OPTIONAL): If your list array contains objects, you must specify the key which contains the string\n  to match against.\n\nReturns: the closest matching string, or null if no strings exceed the threshold.\n\n\nOptions\n-------\n\nOptions are set on the didYouMean function object. You may change them at any time.\n\n### threshold\n\n  By default, the method will only return strings whose edit distance is less than 40% (0.4x) of their length.\n  For example, if a ten-letter string is five edits away from its nearest match, the method will return null.\n\n  You can control this by setting the \"threshold\" value on the didYouMean function. For example, to set the\n  edit distance threshold to 50% of the input string's length:\n\n  ```\n  didYouMean.threshold = 0.5;\n  ```\n\n  To return the nearest match no matter the threshold, set this value to null.\n\n### thresholdAbsolute\n\n  This option behaves the same as threshold, but instead takes an integer number of edit steps. For example,\n  if thresholdAbsolute is set to 20 (the default), then the method will only return strings whose edit distance\n  is less than 20. Both options apply.\n\n### caseSensitive\n\n  By default, the method will perform case-insensitive comparisons. If you wish to force case sensitivity, set\n  the \"caseSensitive\" value to true:\n\n  ```\n  didYouMean.caseSensitive = true;\n  ```\n\n### nullResultValue\n\n  By default, the method will return null if there is no sufficiently close match. You can change this value here.\n\n### returnWinningObject\n\n  By default, the method will return the winning string value (if any). If your list contains objects rather\n  than strings, you may set returnWinningObject to true.\n  \n  ```\n  didYouMean.returnWinningObject = true;\n  ```\n  \n  This option has no effect on lists of strings.\n\n### returnFirstMatch\n  \n  By default, the method will search all values and return the closest match. If you're simply looking for a \"good-\n  enough\" match, you can set your thresholds appropriately and set returnFirstMatch to true to substantially speed\n  things up.\n\n\nLicense\n-------\n\ndidYouMean copyright (c) 2013-2014 Dave Porter.\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License\n[here](http://www.apache.org/licenses/LICENSE-2.0).\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n", "maintainers": [{"name": "dcporter", "email": "<EMAIL>"}], "time": {"modified": "2022-06-15T03:34:05.151Z", "created": "2013-01-19T21:53:32.674Z", "1.1.0": "2013-01-19T21:53:33.778Z", "1.2.0": "2014-03-16T02:12:02.695Z", "1.2.1": "2014-09-14T22:33:36.668Z", "1.2.2": "2021-06-24T14:00:09.748Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "repository": {"type": "git", "url": "git+https://github.com/dcporter/didyoumean.js.git"}, "homepage": "https://github.com/dcporter/didyoumean.js", "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md", "users": {"techscientist": true, "rocket0191": true}}