{"_id": "is-potential-custom-element-name", "_rev": "5-489c5441eee497ab9a8ab43f2d8f6fd5", "name": "is-potential-custom-element-name", "description": "Check whether a given string matches the `PotentialCustomElementName` production as defined in the HTML Standard.", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "is-potential-custom-element-name", "version": "1.0.0", "description": "Check whether a given string matches the `PotentialCustomElementName` production as defined in the HTML Standard.", "homepage": "https://github.com/mathiasbynens/is-potential-custom-element-name", "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["html", "custom element", "custom element name", "web components"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/is-potential-custom-element-name.git"}, "bugs": {"url": "https://github.com/mathiasbynens/is-potential-custom-element-name/issues"}, "dependencies": {}, "devDependencies": {"mocha": "^2.2.1", "regenerate": "^1.3.1"}, "scripts": {"build": "node build.js", "test": "mocha"}, "gitHead": "40ce324ab00825a57a5dd0d6189346046c12ee58", "_id": "is-potential-custom-element-name@1.0.0", "_shasum": "0c52e54bcca391bb2c494b21e8626d7336c6e397", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "0c52e54bcca391bb2c494b21e8626d7336c6e397", "tarball": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.0.tgz", "integrity": "sha512-CE64XFKRrCIpeuy4hhp3nBSUTGziMV09Rn0CPW7G3RRvNBcwysyrJlgIpuEzafB6g1T2P1kGc35nG2bMHqH73w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOQHjWUBzWC+tgCrjfQr/PqW1j2NWVT7KMGJIwxBseRAiBWETPxbC9ByVYtVhZrFo17CWXCFvB8sAvYdB2IgbyFPw=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/is-potential-custom-element-name-1.0.0.tgz_1466533772314_0.8823262923397124"}, "directories": {}}, "1.0.1": {"name": "is-potential-custom-element-name", "version": "1.0.1", "description": "Check whether a given string matches the `PotentialCustomElementName` production as defined in the HTML Standard.", "homepage": "https://github.com/mathiasbynens/is-potential-custom-element-name", "main": "index.js", "keywords": ["html", "custom element", "custom element name", "web components"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/is-potential-custom-element-name.git"}, "bugs": {"url": "https://github.com/mathiasbynens/is-potential-custom-element-name/issues"}, "devDependencies": {"mocha": "^2.2.1", "regenerate": "^1.4.2"}, "scripts": {"build": "node build.js", "test": "mocha"}, "gitHead": "e24778654416e14cfcf259253b916e32e53aaa75", "_id": "is-potential-custom-element-name@1.0.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==", "shasum": "171ed6f19e3ac554394edf78caa05784a45bebb5", "tarball": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", "fileCount": 4, "unpackedSize": 3918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbWKFCRA9TVsSAnZWagAAo+0P/RI23vJJOYxiajcBt+n6\nvQlcEOpOBwXY625Rq6lNhLp/j4zqbcVvdnJElMIqQY/sLlZYj3VxT1593e8H\nFlxbzXTxsqIJNQQJwRlrcmbSVnkd1xjaK0NFFHYw+bttdW3tHwcRjRSWpsIr\nrjvIUOSEUvcBIhkBGOly3NhWSqaLcx94TPUD2/hSaOQrOWPtzdL63q/HYQVO\nRqersTJNHdQcLuQnao94Ot5b4DGBOlCn0+46rs4PQwq8ouwuutAkIxAL5pNr\n+bg/y0fXEXCV0VC6s8jeW6mNBvVAZWKINjZynraCdu1x5gsvs7NJoNsJksvi\ny/4tRl3rDp/ckOLB/3XkMORTcG6enzFZdp3mALL1IYvIJutwSrZtCU1GVkpJ\nhRYkS8iKMLYTdY38iCK/2y7uzruN9llUfTqWpStPPtRNacnmKPYS2bRdTeQa\n8M4h9qU2RVIJ80qEvCghKWw9Junm1N+5l9t4l38neVn8g63WomyCqYXJ/QPS\n6x0o3MjMdMRyK8bWCgGznKc3+OmEll4BDuH4Hu8zEOO0Igw02AgBsbSylPKw\niPee7Vz2oGCn4UAY40XlfsLLTvYQTiEq6JIcjKxPoU6UCfrjYHmAFPYCvhxz\nEwSvW4UO6w5Az3RbQBvy5KCelxeudBUgVW3ULrO1USBT3ZcGzYwb4hiPD9bB\nwWkk\r\n=zUTJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICmMQK4mcP4FqRbI7mviVApWjKxB5IkWCevFwVCGwt/BAiEA4D7WcsbaCbpkD8rfQgHqlMAr9JOCOEF6feRFSVVDFQk="}]}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-potential-custom-element-name_1.0.1_1617781380523_0.31455370235585556"}, "_hasShrinkwrap": false}}, "readme": "# is-potential-custom-element-name [![Build status](https://travis-ci.org/mathiasbynens/is-potential-custom-element-name.svg?branch=master)](https://travis-ci.org/mathiasbynens/is-potential-custom-element-name)\n\n_is-potential-custom-element-name_ checks whether a given string matches [the `PotentialCustomElementName` production](https://html.spec.whatwg.org/multipage/scripting.html#prod-potentialcustomelementname) as defined in the HTML Standard.\n\n## Installation\n\nTo use _is-potential-custom-element-name_ programmatically, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\n$ npm install is-potential-custom-element-name\n```\n\nThen, `require` it:\n\n```js\nconst isPotentialCustomElementName = require('is-potential-custom-element-name');\n```\n\n## Usage\n\n```js\nisPotentialCustomElementName('foo-bar');\n// → true\nisPotentialCustomElementName('Foo-bar');\n// → false\nisPotentialCustomElementName('baz-©');\n// → false\nisPotentialCustomElementName('annotation-xml');\n// → true\n```\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_is-potential-custom-element-name_ is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T02:48:31.172Z", "created": "2016-06-21T18:29:34.044Z", "1.0.0": "2016-06-21T18:29:34.044Z", "1.0.1": "2021-04-07T07:43:00.708Z"}, "homepage": "https://github.com/mathiasbynens/is-potential-custom-element-name", "keywords": ["html", "custom element", "custom element name", "web components"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/is-potential-custom-element-name.git"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/is-potential-custom-element-name/issues"}, "license": "MIT", "readmeFilename": "README.md"}