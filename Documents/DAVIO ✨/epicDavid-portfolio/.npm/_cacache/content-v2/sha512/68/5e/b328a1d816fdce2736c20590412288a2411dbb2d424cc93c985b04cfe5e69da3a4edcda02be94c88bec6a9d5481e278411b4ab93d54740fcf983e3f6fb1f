{"_id": "@babel/plugin-transform-class-properties", "_rev": "31-81fecdf81e2414c94f31a06f58f6bf1d", "name": "@babel/plugin-transform-class-properties", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-class-properties", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "1e9aecbe2e40c2697ea7d14e2905ec8d250173a9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-m04PcP0S4OR+NpRQNIOEPHVdGcXqbOEn+pIYzrqRTXMlOjKy6s7s30MZ1WzglHQhD/X/yhngun4yG0FqPszZzw==", "signatures": [{"sig": "MEYCIQDtjOdeOt9+kyapfRasGekyCnKlANU7K2AGrm/zsDfBbQIhAMIUeeP6+7K4OvzBACU/is5TV3d9PXObXGyRcg44O5ux", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4880}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-class-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.22.0_1685108748760_0.8563944143334163", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-class-properties", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "3407145e513830df77f0cef828b8b231c166fe4c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-mASLsd6rhOrLZ5F3WbCxkzl67mmOnqik0zrg5W6D/X0QMW7HtvnoL1dRARLKIbMP3vXwkwziuLesPqWVGIl6Bw==", "signatures": [{"sig": "MEUCIQDoSPo6fPuRp2dwgz7YNtotq2Aa3yjB06jk3NnD8p+KTQIgcJiDyOgCV2K2VheK4kRY23331dLhhTCUFETgYg1ebSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4860}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-class-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.22.3_1685182254562_0.888522262527712", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-class-properties", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "97a56e31ad8c9dc06a0b3710ce7803d5a48cca77", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-nDkQ0NfkOhPTq8YCLiWNxp1+f9fCobEjCb0n8WdbNUBc4IB5V7P1QnX9IjpSoquKrXF5SKojHleVNs2vGeHCHQ==", "signatures": [{"sig": "MEQCIEC0rlZOKyN7F+Wx0mp5XdOm59RKKhek1qt45LrR9y2NAiAjKGVi3Axw6wrfItnP9dd3s2ALB/+tu7jWrEdd9RI+KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4860}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.22.5_1686248508946_0.22313711690598814", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "6813a48ca752b6530558449383c5d1f9a0fa05ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-B8JS0y23+i5Zo4QIomyaGJvrzO5wGkL909UodqY5I3HidEw9dBPFPIXkGX8t2UwqEXHBHb80GBQN/Q8U0NhD2w==", "signatures": [{"sig": "MEYCIQD21HsKDqL2eyLk55vaXDUwAd7jS90zCsSFr6c0O7SMpwIhAPdtjeY2SzqPpKTaycxE8xHF0pb8TzplAZiiMP+OTUZd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.0_1689861631811_0.026875135418085838", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "13fb803c5ef77463b932c09fc9b2a3e71e919878", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-quvz1ajgWLC+0/BnSnBnjgSgNNe4TdPu/xRhoW5IHjKhhdkP11m7vSdp7qeWiPTHvQlWvp3sAYQmX6Xx6w5IYw==", "signatures": [{"sig": "MEUCIQCaiaSPtE9IMgw5sR9iNv65dbkjy94Q0lIcRotqZQ2h/wIgH9i5sShb7vBY4l9yzYiDEiX+zUkHI8sVZ47ljNkuQKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.1_1690221184731_0.03394713643586211", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "78e6516f70405ac51173bad971e2dfbb4fc22d55", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-kh1Wqwh+QsndzRJZKqn3T/l6MLNNDFswCkvxoBaqh2fEwud4siwVyyCFxoSJoAA0hVD+9QOWYwoqP4z893cu+g==", "signatures": [{"sig": "MEYCIQDUE5p9uBUC56weUOkA1kaCh8kDPAIQ3LIlM0GPpUdi3AIhALgxXfVBToRi83ObZgeDqi4nq6iBqUoLM9xlrWQJD7uN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.2_1691594126022_0.9321413967924834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "57a8b421359b3d26f83d650291dd8ca056d3d339", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-CmREIyQQpcb5h6BEM9olI/wl3qV1JSbF3HnzRIu17m454vYx67PCcjMhe2DF0jRwmAHYNPhLyi30levdburg3A==", "signatures": [{"sig": "MEYCIQCpsFIHu6p07+xJ2gNZ6W5we95eaMo8ACMjxzD4HCHulQIhAPqo0Ot5E7ldvc/POODXzTfp5eo0LJMQFn+YQ9347ra3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.3_1695740259365_0.708192375642243", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "b2281519a8fec2618d35593960bf46f66ed8b51b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-NbgNyarHebME9G9YibdBFN+tynTe1CXP4ybPxiNI2TsAfbCSuvA0nNuoIyX31lTOxi3y6AoVI8Eket+/zkLTzQ==", "signatures": [{"sig": "MEYCIQDwUYtf4lbWBUkCmbF2w8QeDf/THQibCl/b071fCGMi6gIhAI/1Oa7G77aGVG3smAadyp+GBkar1NPaZu4s/dMLZLU2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.4_1697076413023_0.4463995807077765", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-class-properties", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "35c377db11ca92a785a718b6aa4e3ed1eb65dc48", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-uM+AN8yCIjDPccsKGlw271xjJtGii+xQIF/uMPS8H15L12jZTsLfF4o5vNO7d/oUguOyfdikHGc/yi9ge4SGIg==", "signatures": [{"sig": "MEUCIBKHKo5IKayAZqc/lWyzn1s7QMIcJiAxQJnW+tF8vopPAiEApMYUkApy97rha7hYITeZg2A2kPEhqsHqfXIy4fga42A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4940}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.23.3_1699513426619_0.11506963740837084", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "14f61fb17883b436d7efdcf20ff47a07e16a57ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-H39CkN8/ivwe2+inVXwTQsXQuRHRcfUsfaxrLYbteSVOEoAxUQzjMZN2LqaXmduejvNQfsNRtQ1DvBQm5jZ4HA==", "signatures": [{"sig": "MEQCICGPLMvX3RY/KSHJMoQV82DKZo5pkAWfxDfaJWoMwdSFAiBAN1Ff7Mfu4TFvqhbjesTAQhwqAQ88h4BWFOuN/y55qQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.5_1702307987351_0.9819270683064942", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "36eea643e9135f995aaa7a345e76650a043082ea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-73PEMW9sxXZ/5ua98KjMTtIo99aFW7poJtbu/0LkJlNiEarWYErvmNEnkEaV45+gp0lWE34Eqv02vVPaRKEQrQ==", "signatures": [{"sig": "MEUCICid2+twI6aSqjyji21nmWaWxijJi0jkr68JLXjofd9oAiEAoFGZB8m4qNP2j1wO0nvxtwK3JV+FShtPiR/T02nOrtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.6_1706285692301_0.34843048596247184", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "2cfc8a6eac63b4595b6ee7d9801a14662a83c565", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-SqMbzcRhnZO4CkhgQVVYrkVCN7HNJkXSPvBlv77YRL657ziU0g7lfkzQUlKkyjSydg+GFrT31rSrUUDMPqFJwQ==", "signatures": [{"sig": "MEYCIQCYhTCT6nTNVJRvt/kCEkaAjBeTbp0clFx/EZn170cAHQIhAMEo/O+zHO2n426DWbNBBJ20YcQnGspDEoGeHjMwyJQQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.7_1709129149481_0.001933739843465121", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-class-properties", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "bcbf1aef6ba6085cfddec9fc8d58871cf011fc29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-OMLCXi0NqvJfORTaPQBwqLXHhb93wkBKZ4aNwMl6WtehO7ar+cmp+89iPEQPqxAnxsOKTaMcs3POz3rKayJ72g==", "signatures": [{"sig": "MEUCIQCDXF0RtJGQqj9yNEPctTSYk+fmU2MA53is72wkl+9UhAIgNL39FHxJS3n/leV3wy7rAQuUpUGd8UJZ8XiNxWrb7M4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4870}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-create-class-features-plugin": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.24.1_1710841773260_0.15053312797027463", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "8fbd66dd7b67b83c658be8d5007cae1bcaf3a08c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-j3qXMH0Nn753xEu4XOjhEdAVO7k+ytODYHnJIJ2D0GhaVzwH5We8EkL5wHT953bUFGjskThdVUZHeMGGJDQN6A==", "signatures": [{"sig": "MEUCIAM/mB+ci+OKKO4y9Wwd/lz05AxfojVZPQbTsdX47j7bAiEA8gDMiOU69sAWDrIsapn9Oc4Or9aAidVAOhfG8I2necA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4675}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.8_1712236822904_0.09098845948493328", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-class-properties", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "d9f394e97e88ef905d5a1e5e7a16238621b7982e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-j6dZ0Z2Z2slWLR3kt9aOmSIrBvnntWjMDN/TVcMPxhXMLmJVqX605CBRlcGI4b32GMbfifTEsdEjGjiE+j/c3A==", "signatures": [{"sig": "MEUCIEkU9gCo7NmG4VFfXsVhMYEuucWfca9bEeDMfgQlB/M8AiEA8vIQUA90DS+GQjMA9Gn5/PmOMl3jLLkOanlk3bphSgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71752}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-class-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.24.6_1716553513289_0.4956125933367246", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "7e1e8e83662635f9bcb475eeacb0186d9641b885", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-p0oYLS4QMAICWI+4QtXR+7pQQtipo/2yC+lbyn0WW7pdDOQwC/lKptcjkpNFz2l1x8HipcpJ+nYuiIbkSTFpRA==", "signatures": [{"sig": "MEYCIQCI7JjlaZHEyegYb5HuwYFSdoYotV63y0twzzqaSVZw0wIhANsZXtIworgN7C3/kDbIhHk56sTBZ23GtHD7fScyT1v5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71893}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.9_1717423548558_0.5948602645744285", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "03850fea0c6b32a15f7727dc95da6196472cbf14", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-MHhm++XALPudaH8lZm5036NQ1D34oHs5aEf7051gb+RI7cnK83JX0lnvsD0OFnhr7mEmSJ8mwN53nCALRlVV5w==", "signatures": [{"sig": "MEUCIAr8wJzW/aXIkWgBvDpeP2y4ueSJadRw6eUQ6R/dXpnkAiEApFNTx50ZzgpL1/mXphNwosSvBjAw/rWos1Qr/FlPlDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71901}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.10_1717500046685_0.5536360087520522", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-class-properties", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "256879467b57b0b68c7ddfc5b76584f398cd6834", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-vKbfawVYayKcSeSR5YYzzyXvsDFWU2mD8U5TFeXtbCPLFUqe7GyCgvO6XDHzje862ODrOwy6WCPmKeWHbCFJ4w==", "signatures": [{"sig": "MEQCIHanWhCBbJ340PwEKGe7hjp60tk4SCSrR2MDmMwTmFUxAiASVg1AwEQNnlt2rOFWFxsgLQGAh/qsmiUglDxi3Jy6BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71748}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.24.7_1717593358717_0.6418275434756087", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "b29e0fe06aab9db65627d8d46aae5b7acf5a1063", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-GCSzVWtv9Gxo6cE7YPgsweWM7PUNMiAeqQXClK4wY0IZ7uhK4pygUvqP+TLvSvoj+cWV5VFosQuHlbivGTelXg==", "signatures": [{"sig": "MEUCIHyIsetk23jEFX9Mca+hOS3XvMQu4CeAfq31G1GwngJ3AiEAiphJ+cVD8X6zCdnGsV0yQo18XRhzgfkLjOp8QvdfJhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71790}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.11_1717751769292_0.5485889388970748", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "844eb0f8a301382f741911248bcb2c1d43752506", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-91An583VUCVV2GWK3efuHivve9U6G/+XOuL4dTKSnqQ6NyS9lEF97fLyaCHxIJGiK4uMC2GWBdyfYQzEB4kzRg==", "signatures": [{"sig": "MEQCIH9y33fyGU08aDvwyL+ttjPAeMD9AqSxeGAca6zB9abwAiBngDQV8synX6MMe8Qx2USJyRjSNSq7xz8zu5YzLdud5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68517}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.12_1722015244465_0.791601250582332", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/plugin-transform-class-properties", "version": "7.25.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "bae7dbfcdcc2e8667355cd1fb5eda298f05189fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.4.tgz", "fileCount": 7, "integrity": "sha512-nZeZHyCWPfjkdU5pA/uHiTaDAFUEqkpzf1YoQT2NeSynCGYq9rxfyI3XpQbfx/a0hSnFH6TGlEXvae5Vi7GD8g==", "signatures": [{"sig": "MEUCIA0QM7eptC8CiYtQ7l8H2B9KV2CVkst9PW8ZxQYtlBjxAiEAn2y3povlbxnRK/E3WxnMk2kWknVXVqF6ZP55/Wyg5hE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68576}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-create-class-features-plugin": "^7.25.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.25.4_1724319277511_0.40669303681543734", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-class-properties", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "a389cfca7a10ac80e3ff4c75fca08bd097ad1523", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-mhyfEW4gufjIqYFo9krXHJ3ElbFLIze5IDp+wQTxoPd+mwFb1NxatNAwmv8Q8Iuxv7Zc+q8EkiMQwc9IhyGf4g==", "signatures": [{"sig": "MEQCICMptdmttB10Af+rdacxpobW6rer+W+misxffVRP7b8uAiBF2awlW/Z9o+jLlSPm1t3NOImWkKjUt7GxrLCBDnR6pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76379}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.25.7_1727882134892_0.8163840708669785", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-class-properties", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "a8ce84fedb9ad512549984101fa84080a9f5f51f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-bbMAII8GRSkcd0h0b4X+36GksxuheLFjP65ul9w6C3KgAamI3JqErNgSrosX6ZPj+Mpim5VvEbawXxJCyEUV3Q==", "signatures": [{"sig": "MEUCIFdbgZ0GrJ6A4DgB4IspQJXDa3V13wCsyWasxy08fefZAiEAmwqMuWixjsawpd9HpSXCtm7n4b7GX/90fkDbbeqMbWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5028}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.25.9_1729610510131_0.47614020782743505", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "38a7d5636cf717b889c5d7f68445aa93a15aa019", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-1e1N/I/6oiPao/t+HOryq97Uc4c2Bs0kEuBf5mYNgTGNdPOwLVJdhvCHOm/2kF7EaeFfuqnUL6sNXarq4Hfx5A==", "signatures": [{"sig": "MEUCIHjRwGh67fLm99UtpbXbPGO4JzZ+4eGFU6jIPpplonUFAiEA7X6R5Coxg+v2PI/PvMYhg3FYflTbl9eLGFsRDD4THe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.13_1729864490908_0.25283267074448723", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "8f44d03b4751724234cafb094ec0502c5c71bb08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-Zr+tkTk0m4bvAM83YAfHH6Hqi8q/kpf+mWqxWZYaPolOyLcP8JtnabR6eViVFp4ETc3tL80DtoL1UfqF6HTRIw==", "signatures": [{"sig": "MEQCIDnHwMIGfd2I0Yr0tKEFa4FOdAUeCT/5TYKF6m5CKigTAiA2pzNr6iPeW8Aa5N8G7qt5JY4Xkb53wo/iAbwxJbVNOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.14_1733504079614_0.7228002689293893", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "034301ccd7e01fb3ba0562338f0c8cbde7387b1d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-YQjFXWdkJza4Gl8xauQ/1B8VEliLNvrB/sn+iPDmnvsuyutdq4WDB6I1p2wmo1t0XUVCfCTJNJy0ENcWUEFfnQ==", "signatures": [{"sig": "MEUCIFI4O+VDttyrF5IQ48yl8DB8NQGGv6clJ327qonnrhojAiEAuHRGpL42myEPELqHv4JcjjikNE5dGmBKTIvNjfdjzSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.15_1736529909703_0.23578694800393007", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "0b6969ef4dcc8d85a851d5d693a6af56a7d2fa34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-tI++5IBQeI73MPJpQhQb1XMe9OxGxtha3CA1ZHviV/pqeRsapI/R/ut3DVFGy+BLAehB2e57hS4zVyAzh66Jbw==", "signatures": [{"sig": "MEQCIFkUiwukP2IUgRR8Ow2evACNE2lMubGRu6xuXAjq7tLtAiBPPh3b99qh0nwLqb/fuj5RBgWnGsu75paRGpp3hjodFA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.16_1739534383457_0.8121798185746651", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "68b7180685667b7c81dbd08fd04dfa20d309a059", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-E5YiDc4YIffeAvT/UD49f8bAjWNpL/0ulY5oy8z+JL0DvX53iM8nl00EWYQjGf/tEub2cMCnPK+QQvQshN3IZg==", "signatures": [{"sig": "MEUCIDCgo/ebJGopo8roR4vjtTwEACSYVhAw5q7lj1/jvpW3AiEAlOK7Vhm9+RuHDbzEoHWECCenfmXj8tdWXOdQ1muFtfY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-alpha.17_1741717537834_0.8790717046309733", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-class-properties", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "dd40a6a370dfd49d32362ae206ddaf2bb082a925", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==", "signatures": [{"sig": "MEYCIQCFhLe47xPyZZLGUL6S0bBYO8visdtx8RxN828X+ts1dQIhALJWzRUpC7p0UwdER+rQwIZutrGAA2s3GYmi2kSJIo6l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5028}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_7.27.1_1746025773374_0.8926830776235779", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-class-properties@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "dist": {"shasum": "e81c4d2897ce3600062823aa771177bafbb9b1db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-2r2h1KPNbIO+bPThkgLl5Iz2uAOP7wrAafw++F/26hac4KktFQYpWuSoJG35AM9gBphEI17mSvaEXbfMXyKgiA==", "signatures": [{"sig": "MEMCIARWf7HgdjMIkbaFHd+PlUicayvhSWIwT6xoS0f6E98wAh8uYbg7PZb8yCzwgONMH9VHuZA94yJFRvD34gGcdhDM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4979}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-class-properties_8.0.0-beta.0_1748620309239_0.4016035259828321", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-class-properties", "version": "8.0.0-beta.1", "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-class-properties@8.0.0-beta.1", "dist": {"shasum": "ba461a0f740f9f12e513ea28324dc0f17b815c5d", "integrity": "sha512-J7yrwM6cfO11QnhNUx+i6n9XMmbZWCBl1Iyi54OSsv7AF/pbikE/vUozdbf/NejeGgVbQZOpovDh4iLssI6ilQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4979, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDRVt4cCf2MxSXwjTZOg0fZaFxOg9Qtrcac+Z0AnVtQwAiEA8erHHHXXJbutEgOYrQQs2PoslrV43FUSc7jfF8lWZoY="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-class-properties_8.0.0-beta.1_1751447091573_0.027491938203128896"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:48.679Z", "modified": "2025-07-02T09:04:51.949Z", "7.22.0": "2023-05-26T13:45:48.946Z", "7.22.3": "2023-05-27T10:10:54.772Z", "7.22.5": "2023-06-08T18:21:49.142Z", "8.0.0-alpha.0": "2023-07-20T14:00:31.958Z", "8.0.0-alpha.1": "2023-07-24T17:53:04.903Z", "8.0.0-alpha.2": "2023-08-09T15:15:26.265Z", "8.0.0-alpha.3": "2023-09-26T14:57:39.499Z", "8.0.0-alpha.4": "2023-10-12T02:06:53.184Z", "7.23.3": "2023-11-09T07:03:46.863Z", "8.0.0-alpha.5": "2023-12-11T15:19:47.499Z", "8.0.0-alpha.6": "2024-01-26T16:14:52.443Z", "8.0.0-alpha.7": "2024-02-28T14:05:49.631Z", "7.24.1": "2024-03-19T09:49:33.424Z", "8.0.0-alpha.8": "2024-04-04T13:20:23.069Z", "7.24.6": "2024-05-24T12:25:13.518Z", "8.0.0-alpha.9": "2024-06-03T14:05:48.694Z", "8.0.0-alpha.10": "2024-06-04T11:20:46.834Z", "7.24.7": "2024-06-05T13:15:58.886Z", "8.0.0-alpha.11": "2024-06-07T09:16:09.496Z", "8.0.0-alpha.12": "2024-07-26T17:34:04.721Z", "7.25.4": "2024-08-22T09:34:37.729Z", "7.25.7": "2024-10-02T15:15:35.078Z", "7.25.9": "2024-10-22T15:21:50.316Z", "8.0.0-alpha.13": "2024-10-25T13:54:51.062Z", "8.0.0-alpha.14": "2024-12-06T16:54:39.792Z", "8.0.0-alpha.15": "2025-01-10T17:25:09.885Z", "8.0.0-alpha.16": "2025-02-14T11:59:43.594Z", "8.0.0-alpha.17": "2025-03-11T18:25:38.017Z", "7.27.1": "2025-04-30T15:09:33.557Z", "8.0.0-beta.0": "2025-05-30T15:51:49.407Z", "8.0.0-beta.1": "2025-07-02T09:04:51.761Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-properties", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-class-properties"}, "description": "This plugin transforms static class properties as well as properties declared with the property initializer syntax", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}