{"_id": "@babel/plugin-syntax-import-attributes", "_rev": "32-04435ee4eac3950aaae651e98d31c520", "name": "@babel/plugin-syntax-import-attributes", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "042498f96410fec073fe76585cbfae0e458e577b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-TFqy+gFAiTh8KlVS8/c6w97uhAVcCVyd2R0srMHVYymBcBK5N5P+bf8VG6tEAiYCZ3TLYvi6fpzU9Rq79t9oxw==", "signatures": [{"sig": "MEYCIQCurVFFO7wuEGNyqj9I0/66XVuzTBL0HRESv6d56bAUfAIhANU5sm+I6wLX4F6jGetRDS3lFH9bIiERdVQpQ25sPmQu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5146}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.22.0_1685108713282_0.6631986484338737", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d7168f22b9b49a6cc1792cec78e06a18ad2e7b4b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-i35jZJv6aO7hxEbIWQ41adVfOzjm9dcYDNeWlBMd8p0ZQRtNUCBrmGwZt+H5lb+oOC9a3svp956KP0oWGA1YsA==", "signatures": [{"sig": "MEUCIQCHu0GrsLnv5MeGktcwxdbFd1LnmujNCEdIfX04MHNS8AIgSGdA/ZKxU8WJW1ckM3ZtSaefBUUHu6CjvU6jzuaYIQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5124}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.22.3_1685182253810_0.6650120289766583", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ab840248d834410b829f569f5262b9e517555ecb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-KwvoWDeNKPETmozyFE0P2rOLqh39EoQHNjqizrI5B8Vt0ZNS7M56s7dAiAqbYfiAYOuIzIh96z3iR2ktgu3tEg==", "signatures": [{"sig": "MEUCIQD8JvIPOOi6/Wx7hbcF0Q8H2AyBDvU3BFsJGdcugEvnzwIgc8T9evUENYKmALRlDosDMxNFIm3SQ4gO7rvD41dRA3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5114}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.22.5_1686248471239_0.40412956501690744", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6f32ad7b439409f94332e1cd2bf95ba54ae3b38a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-E15KaQU9k413qFJMt4oV8ZUj7dDYZMYNRQuRtFx8Le08EmUfH7sfM42FVGR2gGb9jSapJ9hQHLXJdRiCEb7zVw==", "signatures": [{"sig": "MEQCIA59+rp8frA3p1iR9Nt2T91jHyN7okYOfGzASiU920PcAiADxQEgeinlOaxRNeHw3wIz0NBOULuGxhHKH6YVHud4SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.0_1689861585169_0.9659451404690549", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "95556c91e2a69298afd519c0da9998f24c61248d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-z5JpjJ+sFtFQaHz2ZMl2ND7IbRJQHqewd3CMrkxy0WQa/aHQ70ncEYnkxjo4y07vcrQjeFhkeRwqq+bVOA1ksQ==", "signatures": [{"sig": "MEUCIA6HsMfkU4vaZMEic7SoBn1zPMfh9xQgwdn6wi8kLN5KAiEA77Z3KOecBaFgg3gprNpMNOPrJA1/+37l+b0eL0YlUyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.1_1690221081464_0.7109082219167442", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "aa06748c2b1e03375eb9550f3a7f3cd9b9458c3e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-rGYsKHX2gWfd7R3L5dMBBVaeWbfLalU7hN6L3rEt5mYSWIyYOIInigHT9zpPknn7Ut4giFze2Rv5vavYi9mM3g==", "signatures": [{"sig": "MEQCIEgtfacJdKIRuowj6siw+PzXtcVuJdL5kVbiHveLTGOsAiAdnhl95KGLg3OwQmKXzw5e9r7VtmogduR3/AhvpuODHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.2_1691594084186_0.11489308581401403", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fb0e656bae2bbf999f6eeb748d10a33167ac8e98", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-0yOKkq3WFeandGYKzgP3/6PacZqY5r4mG4b6zypliM/2hM68dX97vdOYaMlfZt2b0AWASISZkzhkMv091wlUxg==", "signatures": [{"sig": "MEYCIQCu7HTfyxOJi+KoRl4TiYOYJm40vMW/fDSOYY29mKqXAgIhAIoKD/Q/1ALC2LdYCZ3hDFg1sw4LnrdvU629jB3NpLtb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.3_1695740198319_0.8039887332345625", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6343c68a39d0d04e54e1909b0266b2e3183c70f1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Ngy4c7oDQWRzEWc1vygSbbvfNa2D35QWann4Uwqf1bmknZ7EfCLwUsdbHFFAfy630QH4aCePezAEagyJDizK3g==", "signatures": [{"sig": "MEUCIHSR7vPPIzoXuEmXTbOqD2uaN2PPkETDiabnOQsmcwESAiEAqo+MAXl868W0yqBy1IPvnVOWFsdBGaZ7i7jw5ZxeKgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4760}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.4_1697076364774_0.17905536007660228", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "992aee922cf04512461d7dae3ff6951b90a2dc06", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-pawnE0P9g10xgoP7yKr6CK63K2FMsTE+FZidZO/1PwRdzmAPVs+HS1mAURUsgaoxammTJvULUdIkEK0gOcU2tA==", "signatures": [{"sig": "MEUCIQC0A3CZjf24rnlI1cOK4shsDYEck1caq+0ut8a6aKSNpQIgY7d54rdzG0L4NxGs1ilVzP7UEN0myDIj7hXmT5OpgtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5193}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.23.3_1699513422208_0.4649078333199954", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0bd31145bca9d21555e45ebc1014689d4eecec4c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-nlPu8yDHLeJANUKTfkQV24Z3Ja662F60xVwBrlU/u7WyA0vYvMR6pOkPYyn+mC9iImFHxXq6l657hOAVsuKCZQ==", "signatures": [{"sig": "MEUCIBQfkuZBpho8Wv65vrsiPsC8Va0A4uiGdF88uj0UAE1AAiEA0KbDToL9iHNF9LRPJj8a9V1dXo6Pu9cdiOCqVE2ylXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4865}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.5_1702307907875_0.22360754633640534", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e5ce620808ad555b81e8d95464c8af94ba13d346", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-JnfhHFoc8uMKHGr2UbXOP5vKuasqmsnWWtSkzINHEeGBZm0NbuMxj1CNFF/2HQoOFpuTiqpB6PU/o3xOjHegCg==", "signatures": [{"sig": "MEUCIQC7hYkUYJo2yZFjKYn5h5cWxygl0MiOg8n/bxSqn2UfSgIgA81BgyA+44IK3C6pV4a96mZf1BmBHib9Qgtx/xW+r/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4865}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.6_1706285630390_0.07925562030423161", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1a4a87382eb5df0ccf4a739d107ee626d1de6624", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-nJRj4NsnmBPnaBewaR9r3B0a+Yeq/rQ/1BdFMjNZdf/UewSWsbInIDYqgUnyBgaJckZ3V7AI5LG3hHY0oxyeFQ==", "signatures": [{"sig": "MEQCIFKSr3xwT0hBJR+9AR26RNczHpnhDFANJoC+uhIhkSUOAiBIwVk+F+qnjryz3QdLHKkqOnSkEcRgaeptjo1/kdX8wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4865}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.7_1709129076359_0.035584028750726526", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c66b966c63b714c4eec508fcf5763b1f2d381093", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-zhQTMH0X2nVLnb04tz+s7AMuasX8U0FnpE+nHTOhSOINjWMnopoZTxtIKsd45n4GQ/HIZLyfIpoul8e2m0DnRA==", "signatures": [{"sig": "MEUCIQDePqKe10e80jJ/k+8mqXNCtoyTmbd9rIxU63kknCKzZwIgXuxus3FMPc3qtxD3UruJX1yDt/Gw53Gpi7Fe99bks9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5124}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.24.1_1710841699573_0.7065821944113051", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "099d369e6b9ce3da8ea18f0e54f7865099aa3900", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-n/KclTu94V4UyQ/GqxM7MrF6tV5isWRfL8tZmFTeZGz2C18fg1PKm6uXPTOzLRpQDP1jm+4hZeTtd+TKNtHGEA==", "signatures": [{"sig": "MEQCIAdXmyFGYAAQVEJoDHmgS+V9EfFdrtnj/PrSI/srcMjGAiAHbjmv7VaG4TTFYqWr4mq3JCfr6TsnGtOhji/p8xMsow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4779}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.8_1712236779680_0.35059028623540667", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "12aba325534129584672920274fefa4dc2d5f68e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-D+CfsVZousPXIdudSII7RGy52+dYRtbyKAZcvtQKq/NpsivyMVduepzcLqG5pMBugtMdedxdC8Ramdpcne9ZWQ==", "signatures": [{"sig": "MEUCIQC/DYXEAM65mzzKosELe14CQi/RecNSEYe4+lSFUOdShwIgWKVGHkoAAhnweDRDCvnVlRRVjsqU7U05f/wDUOa87K0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71129}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.24.6_1716553460693_0.2868398416173461", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "afd92e707dd68f9c6e02b0b1d590fbe06cae884e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-tS/YGXdlBfyGl1q0RpIvylDbISZ0kEQpgTiSCnXojX/f0Erfaqtcyi9QWAigTywlymLHCpYBkzisTv6oyUUzTg==", "signatures": [{"sig": "MEQCIGHOUgqZyXFCJ+vGDZ9wwuzNOwAUbXlMj5CZyzQE55mVAiBI8bvWrbim97sCQQiLUT/pLwx/ydC2BXdsaAW9DpsIRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71053}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.9_1717423438734_0.24489046786006763", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0c23e85d85d114daec0ae7359249e65e08f1bfa1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-Y8aSVMh0IA0XIZrEutox/J88X8OEwh0xF6OOj/EUyg3PIx3j26wPdr0zSRIm4FMHccV6gKzH2c5WgxZveA5FYw==", "signatures": [{"sig": "MEQCIA6Gys45hdeglNnH7UmlsS0WTL427Cv0XQOHgvBq5pziAiADmDHaFJzDgrqh+5mdZQutlSqQLU/vGh6tdbhhoffjjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.10_1717499991261_0.625505707840478", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b4f9ea95a79e6912480c4b626739f86a076624ca", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-hbX+lKKeUMGihnK8nvKqmXBInriT3GVjzXKFriV3YC6APGxMbP8RZNFwy91+hocLXq90Mta+HshoB31802bb8A==", "signatures": [{"sig": "MEUCIBClog4Pv4Ya5erTDqv3KrbEEoFGw+W3uW4phXDI1pmBAiEA6RorTKa3G29LTEW9WKBXyJde3N1x6/I3MuhGwXw9cJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71125}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.24.7_1717593309886_0.2677187568718782", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8ebf4b42b2d27d6bf2c961519e5f47f8d4636592", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-ryC5vIu0eUv66xZiWXbEa1AK9sbo6V2eFuFnRMUVNRespC7Puw/2//42B6slPuo3oKkOiVFFqNF2i4UZeMLuqg==", "signatures": [{"sig": "MEMCH3ZuZ8svHtqI5LbIU7iWDqEJ3mSaA+sLqCwE7yBPMzACIB2TzemSQB5InyHnwtSepv8EAub1a9KlXFR89+aaGmnH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70948}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.11_1717751721092_0.768041758906022", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "37f252caca36f82365517cc234c14eec19f61a64", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-HDE0yIT/bnRwgth1Jf6AkkIDyJ3ApbsSarVgYp/Am9Vb3/Zjo6ThFkJ37KYsVNHpBv9bNvJlev3ScyiBDow/UA==", "signatures": [{"sig": "MEYCIQCfn0EwoiWaasTlrDhbDvxU70M+RNF40HoDREbkG30+NQIhAMHWpAndP6AT5sJLKbKeDQLd6jUruoG0K4/F86fLMtvZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67740}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.12_1722015196294_0.04887313670615612", "host": "s3://npm-registry-packages"}}, "7.25.6": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.25.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.25.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6d4c78f042db0e82fd6436cd65fec5dc78ad2bde", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.25.6.tgz", "fileCount": 7, "integrity": "sha512-sXaDXaJN9SNLymBdlWFA+bjzBhFD617ZaFiY13dGt7TVslVvVgA6fkZOP7Ki3IGElC45lwHdOTrCtKZGVAWeLQ==", "signatures": [{"sig": "MEYCIQDJnccAyOpmfqE7fcjqGn5gVJykwdJSu+cW+h6yoQKtnQIhAOBhn2Klawota/TMUA3HPJq+ghc4uRL2knMs0GeOD7Yl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68684}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.25.6_1724926461177_0.8004831475698833", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d78dd0499d30df19a598e63ab895e21b909bc43f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-AqVo+dguCgmpi/3mYBdu9lkngOBlQ2w2vnNpa6gfiCxQZLzV4ZbhsXitJ2Yblkoe1VQwtHSaNmIaGll/26YWRw==", "signatures": [{"sig": "MEYCIQC5DH0QeGh9065N/529Ydn+RtUSjwkp2YFgSGV+H9XGAgIhAJJppS7nq3WUYbqmNahmR7xGJUrO9ROZMBFD6S+eegBK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76487}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.25.7_1727882075344_0.8911901991424551", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "29c9643445deea4533c05e6ac6c39d15424bbe78", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-u3EN9ub8LyYvgTnrgp8gboElouayiwPdnM7x5tcnW3iSt09/lQYPwMNK40I9IUxo7QOZhAsPHCmmuO7EPdruqg==", "signatures": [{"sig": "MEYCIQCxMRj3wWH6RvH8z9HPtkz6LtYL/7EIc4jLxRdIUop+GgIhAP4gTxnrAls7H4TK/gYjmNreBicUZ5z6LN6GUCLhSQqN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6032}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.25.9_1729610452473_0.6094244638599497", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.26.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3b1412847699eea739b4f2602c74ce36f6b0b0f7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==", "signatures": [{"sig": "MEQCIG+mnrLG8F7PgnEeNUVVmO/2MC9H3/1blMAPX9m6FTHMAiANRIobtt6+izospcHd4HzcKEBLT871bq3igZ4U+atGlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6354}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.26.0_1729863009603_0.3336477121049519", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e09c97eedd67d2e70b6eeedfb82d5f6e7cd92974", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-+LtIbfmJ5nrCvhdXTjDo9yfj1h73NDnzq5KqpH96X4R0m39gReCs5rUpQ1qoI0ekbDH4QvOHHoy3HKpjlCM1kg==", "signatures": [{"sig": "MEYCIQDt70tK4eyOg03Eq//u9pIyOrvX5o9H+DxpKD9d7aSWKAIhAMcZLfrrWZZ6F6CaDd5BrMsLSSSgfTjNXKi2BxLPAtm7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6361}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.13_1729864437007_0.9544694877240198", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "892174f42e2696b8bdb09969789d2474b9a34e18", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-ILfipZDZzqlydgjk+wKnfgKLNaiA13DAXIRoPxrlp5l/o6LfP/jb+k4guOs0h7xcSe19VUfoHD5P1G6Y1L24lA==", "signatures": [{"sig": "MEYCIQDjbFhYUBh8Oq5fkU07G4eOTgOP9oz0q5QJhZU1j6WtHwIhAMP2f9rW9XGs+4TENrAKiykhpDY39PV19bIC81CcLfih", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6361}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.14_1733504028529_0.5078457856719583", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "395d841e3be46a5d00c120e2b830f31f24951c1e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-CeAp9t4A13Byv/FqYJBHnbXT3DFE4CDJTO1xRhKLcSofLX37biTjDPlOykUcXDyt9K7VcriCKuE/aGVJaCpsxA==", "signatures": [{"sig": "MEYCIQCXR6wkTT7NMA13cMJTe0h/SP/pvLR79KEVCzBKh0qsdAIhAP1OuuYW14Za34VD9iVYBNBuyV0vAhg/KEu5VKYJYOmV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6361}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.15_1736529853001_0.6794293085877974", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "215e4a794c4ad822eebd93fedb5bdfbe1c3cfe66", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-n8o2HMxLIavG2uzdZfgjf7E+lnvBV0SNom/a40M79zMfxiHQvVr5hDOprzo1FLGMbP8VYemCONoUBJzmYDsXEA==", "signatures": [{"sig": "MEQCIAaFzYOoHzzZ7blErL5+4oI5hNoZqPOF/0O0kySdEyTDAiBf7uOl9FD7hWgpnYu6JUgFlg5i2CReQtTaKT0BaF9CWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6361}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.16_1739534329373_0.7131966234127012", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a42053b6316a95e1bca03ca602f44d7ec21026d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-qH8tQGL7bH5buFTh6YjOU1IqSzrbP9v3knASJODlq2LNSDwJcCstoe5J/hQvbWGF6saE5QmhkCoQsEXmAhCedg==", "signatures": [{"sig": "MEYCIQDiLPUfYdWYEriwe6/hJW5qher01mQLd4caqv4QBqhlygIhAKPj4SKrFJNVMJS/uyh6lhkOv1u3tjbo/jDwv8bqruXN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6361}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-alpha.17_1741717481342_0.032101145833655975", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-syntax-import-attributes", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "34c017d54496f9b11b61474e7ea3dfd5563ffe07", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==", "signatures": [{"sig": "MEUCIApa+6OfxJqE3Anc6Vs2kBsxOaZ9vByQnUG1uV7wrRTNAiEAwYEkQZqwZTMS1BoVL5Mq9sbvoeL+UHgbJcdaSiXxfnY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6354}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_7.27.1_1746025719714_0.2958429870456514", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3586718356d0e135423b5836664a772acf2a303d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-H/Fby7rek/hgFLlFxtrqReFyLyMnqY3m1xLY1r8PxumSkoDw9US/3MBqpHpKbmcKMhcpiD6mrEkvSoaHO3tpgA==", "signatures": [{"sig": "MEUCIEH9MlMXNwlzL1W7AMawR5UDsG3iTlgqzDg8l2XDNBMuAiEA0QzF5pXyTz+5RzEvZPYf2gc6hbAUZfpkH7+EF+6GysE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6337}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-attributes_8.0.0-beta.0_1748620252619_0.5424804853127645", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-syntax-import-attributes", "version": "8.0.0-beta.1", "description": "Allow parsing of the module attributes in the import statement", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-import-attributes@8.0.0-beta.1", "dist": {"shasum": "7421ed686e39c8c48afe1d91e6a4d390195cbb58", "integrity": "sha512-IdSjbiEqLnbl6q4UryRW8MIEJs55F8PWa/Qc/RM4+z2PQm2Itoj1FmB0UXM6eaWGOVvT/yyluROmgh9qNe17iw==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 6337, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCUV3m/4XwhJI4HyijNRJPGRPLvi/kR5UmG8DxIdZqKTgIgPccNS4RdxQH6VR0Ez/o+Pd/sKWHsTnc9qGB8i6KiGiw="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-import-attributes_8.0.0-beta.1_1751447046453_0.6120205358613646"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:13.228Z", "modified": "2025-07-02T09:04:06.849Z", "7.22.0": "2023-05-26T13:45:13.456Z", "7.22.3": "2023-05-27T10:10:53.960Z", "7.22.5": "2023-06-08T18:21:11.373Z", "8.0.0-alpha.0": "2023-07-20T13:59:45.325Z", "8.0.0-alpha.1": "2023-07-24T17:51:21.643Z", "8.0.0-alpha.2": "2023-08-09T15:14:44.341Z", "8.0.0-alpha.3": "2023-09-26T14:56:38.499Z", "8.0.0-alpha.4": "2023-10-12T02:06:05.027Z", "7.23.3": "2023-11-09T07:03:42.364Z", "8.0.0-alpha.5": "2023-12-11T15:18:28.040Z", "8.0.0-alpha.6": "2024-01-26T16:13:50.620Z", "8.0.0-alpha.7": "2024-02-28T14:04:36.508Z", "7.24.1": "2024-03-19T09:48:19.740Z", "8.0.0-alpha.8": "2024-04-04T13:19:39.861Z", "7.24.6": "2024-05-24T12:24:20.855Z", "8.0.0-alpha.9": "2024-06-03T14:03:58.942Z", "8.0.0-alpha.10": "2024-06-04T11:19:51.428Z", "7.24.7": "2024-06-05T13:15:10.091Z", "8.0.0-alpha.11": "2024-06-07T09:15:21.237Z", "8.0.0-alpha.12": "2024-07-26T17:33:16.478Z", "7.25.6": "2024-08-29T10:14:21.385Z", "7.25.7": "2024-10-02T15:14:35.703Z", "7.25.9": "2024-10-22T15:20:52.624Z", "7.26.0": "2024-10-25T13:30:09.802Z", "8.0.0-alpha.13": "2024-10-25T13:53:57.228Z", "8.0.0-alpha.14": "2024-12-06T16:53:48.798Z", "8.0.0-alpha.15": "2025-01-10T17:24:13.282Z", "8.0.0-alpha.16": "2025-02-14T11:58:49.526Z", "8.0.0-alpha.17": "2025-03-11T18:24:41.556Z", "7.27.1": "2025-04-30T15:08:40.045Z", "8.0.0-beta.0": "2025-05-30T15:50:52.792Z", "8.0.0-beta.1": "2025-07-02T09:04:06.645Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "description": "Allow parsing of the module attributes in the import statement", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}