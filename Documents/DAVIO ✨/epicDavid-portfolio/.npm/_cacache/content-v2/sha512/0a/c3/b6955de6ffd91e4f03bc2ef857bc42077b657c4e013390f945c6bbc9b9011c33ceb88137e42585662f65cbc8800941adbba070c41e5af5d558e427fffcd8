{"_id": "@jest/console", "_rev": "142-9b3faea93f8e93b0d18efa848e39265c", "name": "@jest/console", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.4"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/console", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/console@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fef131dc35980ecb5c8c9f91179f82d863eaf79a", "tarball": "https://registry.npmjs.org/@jest/console/-/console-24.2.0-alpha.0.tgz", "fileCount": 21, "integrity": "sha512-XUHZFuEybeoaOh5Zv3gv5laFrxgjQvJLB9rOHYjR+yYFUVp9u9zGTUurjnqHqQFVlKpAuxSxPLbW53n9EXx8nA==", "signatures": [{"sig": "MEQCIAZUeInVfSAqbmlbIcW3PIoeLtHTvOVjEEXXA1u1I6BIAiBjQ4JkGKj3ZEQNtnR8g7gW39wuoC05+GOCyqdP0+xj4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovTCRA9TVsSAnZWagAAghAP/j8yu2P9m51q5WcW5Cwx\nRZ8EkLbH5vHu+w56ZHEbFMptca1iWSsenHPPbJ1BS2fbk/owgUoYXDMFRR6l\nsdXM4XFDIrqpWDD8Kz1e7E3s0+q/KgyXExJ46CxeVSQI/snE0wjd8MP8jsIv\noSikN3cx1gp67mKOeRiVtSZpRRbWzigq1CwmjFo/XZXTO0K9bcEgBT2jfJGb\n7TbPQ7jzA3VdV6CK5Y82F96qTo6PnRNvGDbIw0q0SJssvBcyHZptkCmLFFzL\nmar+C6nW4bO5oojOn/A2ZdrhpVG7+9q1ppkYpeFb08pldbQyvB5qnJcvjQIe\ncpybkpNh8TwowdcWd1CvDSiUMgD5fP4UL+m0AYFi+QRPuHz3HWMySWAL8zFt\n1y28H3gx5bIuEIj0j9jTBdqC1oGFA1OcEMXuTWYmSJutpO4veKFMX7yDtTF4\nxAOew7UEiyJaXiDyxBsHVhyOkLWkEzRRcN7Gmb7JPJYF7gBeWAG+IOkgyKdD\nXg3Rk/v3B7Rp4Vlt6mdQdyWwakJ6bMdDdcFEIvA6sK1vdpY1yXSoeWUpcB1d\nDszigez0VTmy9SVI7kW2OVJL6Ug3eRRUaMVp2F1jAxHZNDNrybISE01gG+RW\nd/2nza3XWWbDxkYPEnQJrLJQd3oVfu2Sei1TneqHTGbQQFcPZr2rhoESL6qz\ni5fc\r\n=btnM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "@types/node": "*", "@jest/source-map": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/slash": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_24.2.0-alpha.0_1551797202755_0.6602347246964855", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/console", "version": "24.3.0", "license": "MIT", "_id": "@jest/console@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7bd920d250988ba0bf1352c4493a48e1cb97671e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-24.3.0.tgz", "fileCount": 21, "integrity": "sha512-NaCty/OOei6rSDcbPdMiCbYCI0KGFGPgGO6B09lwWt5QTxnkuhKYET9El5u5z1GAcSxkQmSMtM63e24YabCWqA==", "signatures": [{"sig": "MEQCIHU+7pkZ4gA7j7SkAE/rzHgtAkksRCIGkv+K/0CVbfchAiA1RZ8q6I5b32FvZ/mLi95FyqZa3AV5QWUpX0jFVXCayw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRW7CRA9TVsSAnZWagAAHV4P/j+iM6Y2UoU6lDP/gvbe\nQtlb7c/a6IF8jWwPNj2muY8wkuO22t2TMMUTz2kUFFD4ChjurHJmhXLzIMTr\nA/wh1vxYZmbfGZ04Z5id2Vc9lUtyjJuh0MHmO6Te0Do1Eqof7JJM2qu/bsD3\nVDI4zp4yP6Ozxg14o3+BX/cst3VlcPj/F9ZCplYp2yy/nt8GMuuDdTTpvn4N\nWmKYtLQICiK+z61UBZFvHKQg/Hl5vUlC2CE6roIPNpVljgJDIk1H8YS7NINi\n4/ISKomqECS5gd9z2PI779UrVq/6B04f48+Sb5A5WVbvQRXsK7iZ6nAMeEbR\nuX7cnxBui0DxUcxsX8pe8gbzD6fI5rtmkupwU8X2uTp0YsYtZi9jwwc8T69n\nD7vv4FQbphJ0enlcYD7H7CwWL26I8F3t12TjZLOaSmEbmWx7YfdTAY8Chl33\naHxh+extPQvvUQs7GWeo6wcL5JN9zzfiRB47uut6CZaH4cd9YHFYFkpXHRN4\nEVX1DUkVVoPu0CJ0U2luUE0JBR5e8YoPIpnNSyeKlqQJhFNS5vFwWEYlEB4c\nbQV0UNmBk1F2ZKTgSe+oQjAx3mmyksvnxshU5cVvyIpLkQR2y7+vMT86Ihzj\nrcmPrz+Aj1AyjcQBS3tfAHpwTsXr+oHjcJ9BQD5ooiWqeic/1/6SiX5BEXRL\nZBGy\r\n=xMwX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "@types/node": "*", "@jest/source-map": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/slash": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_24.3.0_1551963579187_0.7359164506049181", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/console", "version": "24.6.0", "license": "MIT", "_id": "@jest/console@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63225e6889f3865ab5b7a0d8797e8aed417c4e0b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-24.6.0.tgz", "fileCount": 22, "integrity": "sha512-nNZbwtZwW6dr7bvZpRBCdBNvZYi+jr6lfnubSOCELk/Km/5csDmGdqeS4qKwGKIVlHTyZ95MYExYevpdh26tDA==", "signatures": [{"sig": "MEUCIAZZYdgcSYoGdWhm6wiei7CaxgRBijKHslR1lbgWscmKAiEA2+QhhRViP1KqGrWI8iw7zgoy7mAaW0hhAKPX33ku0TA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAJCRA9TVsSAnZWagAAWLkP/ApiDYgbHtc01qURbQHp\n1vlrChOMmYVPl0LU0wZlQEy9MJdVxD4CgQvRwOmlx4/sorkJCzeHSxwPic8e\ncRHo0wPgnRKGdlBzAPXLXrnv7k3Y8FQLMu9CfuoBYiLXrdTWGnKo5gSQy0NV\nsvMU1r0HAUhTV78pZzvcXmomvTPX0qogmTTokWVWrmhAMtdL82maL0pmedoo\nBFfB3PZChokVmFYb6Gu8kfi7FUurSKfBdMEEMa1xStSM4zjgRkUe5CyhqHQw\nYYTtGsSvWS9zmX0ulMkX1v1nkMJwQfBkZoEdYtDCzL82J6xI6bmkohhjFeZq\nD4C7cABbB346/wxhHFzhxlQlkIbeISzHwLXqbBzenuQaXM+lZbGlKsRSSA4w\nVXvULuEb5+VHlCCINsiRYoLK9YqKGoL3OtwsB8sbr1X/scKzxXn7B2Y4bMhS\neWYSmVYe8rbdByco2kqlOFhxMrbapVERYUfAG2/qhJ0MgblYaXiOhNQit8o/\n3tu241gA3/AjaXPIQWgl/iaFqphR23kzFewtY8AzSgPczFJmZ3ZzIUT2ygVH\nSwKIV0yGsvT4noMYKI/VHnAATlmE2HVOTEpYTiZu+c6R/rqWYagWu1s/b54k\nYhY+ikg6NQ+RyitCpELRVi8CXayzuK6uwMPBuDupNtw+R1HDJsHoHj2P6YN8\nc1GB\r\n=dX1W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "@jest/source-map": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/slash": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_24.6.0_1554157576554_0.231059387908783", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/console", "version": "24.7.1", "license": "MIT", "_id": "@jest/console@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "32a9e42535a97aedfe037e725bd67e954b459545", "tarball": "https://registry.npmjs.org/@jest/console/-/console-24.7.1.tgz", "fileCount": 22, "integrity": "sha512-iNhtIy2M8bXlAOULWVTUxmnelTLFneTNEkHCgPmgd+zNwy9zVddJ6oS5rZ9iwoscNdT5mMwUd0C51v/fSlzItg==", "signatures": [{"sig": "MEUCIQC6irC7kNg/B96fcY1KSudxQHisYN5g4fbLEf+lKuQ5iwIgR26Gtkdl11/3nIEOXpI1ZHXExtVvXVhtx1a2afsND8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVtyCRA9TVsSAnZWagAA0HcP/jSL29Sz2zAgJRctszTc\niGv6pjOB5BL9WZOZhFJwY7wolQgTlwA7ybeXwQ/k9SJ4SacQ+gU5TlY2+n/B\n/VDDS8vJt01J3qX0e5peL7981IkjH+BXY1DnjDHMeQmGkGlPHn/Bw3CUdT6o\nGCSd7eesfepZpcBnfcvxNzV8d9SvJ3ltdHkNOAk7UtfbBAQH6ED9CeNLpqN2\neRu0lfWoySdxOMt1r4aifS7Qge6ElRn9vgCEPcPqXUog+Zk4tNnpu3FC+Ikj\nA3EmUBzMt74o4VierTAY0RCHD/ozPNTwNmNb6sX1AeCBdcQGEnvKxiOzx/ng\n3N683jH9I5mGQ2SMma3iklVGUbxm3G1hHH5URSlbWztgJWGa9qYURWKt40Tz\nx6vf2aBB8MnExtdewyYYRt7fiqcBPXqeZe+SzzE53AbUIVaHlSYVIgbO9h+g\nPqtOh52HWnMT1wlv+GMKJ6ObRfrIznTYq7h1CWy/ToZkV1msaC2IpLSHHaUg\nMSRtoYsCWEeapwu9recAcy86MuStuLVm1GcI2k400fbV1vOc+qGVKL+MyTP5\nDNY2kX2QxVD17ouZGCw30olYYakasTPmaNCEmlm5+w5zUQ8+PEDpAdHuOp2q\nXemmnbaKhiuDkqdDeft3DwkRbMQfVlnEu8PNp+TMsQJfmRS5jWzTnkN0Uzjs\nRAaU\r\n=gVJ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "@jest/source-map": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/slash": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_24.7.1_1554340721749_0.9952416591073949", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/console", "version": "24.9.0", "license": "MIT", "_id": "@jest/console@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79b1bc06fb74a8cfb01cbdedf945584b1b9707f0", "tarball": "https://registry.npmjs.org/@jest/console/-/console-24.9.0.tgz", "fileCount": 20, "integrity": "sha512-Zuj6b8TnKXi3q4ymac8EQfc3ea/uhLeCGThFqXeC8H9/raaH8ARPUTdId+XyGd03Z4In0/VjD2OYFcBF09fNLQ==", "signatures": [{"sig": "MEMCHxng29VBVZKlM/sDFLtQFop+spD4ONPAonXbXiWDRGwCIDh4Djt8EQ4hr3J3inta9STeYPEudBqqmsNjrUfFLWrw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVrCRA9TVsSAnZWagAAZgMP/ii9VIWfIkCaBz0YL3zP\n/BiH5tD1UOUL9PJjzAAJxthcZIxZo2fZuweIvFgzGqmaVTUm4lJNA1AQlao6\nRvQ+bhvhO0KmzWrunp6Y/TlQ2DC6aIsiUKG7OpRgN8kaR6zxh5pa9kp+iW4x\nNcZF9x9Uf8/5gEikXCM6qMsPSbY7RovGs0S+nXpnE6XfoUXjPYuvlcmeCZWc\nN7TGnv/JML7ZE1x8J/1Z/UsuLl1pbhG1q2xiie1ta322eqSjsNKvUPKTxK8W\ntsN4bddr3xspjJxNsRYBlBjK5Y7nBrjSbxxdn+rzfz6OhG/nUeky85jkHfdU\npAAz5aouOEnJSfff8hMOj4Y3A2eLCmYb3uZxJOudx5i4Y75hrw/VjPSTF9hM\nCBV4z6BS87X1b4nkVqrfehCLFms6trAuQmAqlxHG5Gwoaukx8Dlk2/mu6H3q\nN6pTBLCOWlZGHAx6PCoQc8J2NveQbXPSaT5H/N7Frk++50RGpDtXqLZdIrq9\n2X3MRo91MTqWtFQHnkLIk0TJv2COnlutZ+GI4Lfc4aRfWsJpYiwYuHU2Kf1b\nxpDjkYyeFFKVSme/7CN2kgjeX+oSYeJ6ndnSwyAQ3YlFBlRxvacYgjfFF4Is\nkY6U1Jo6WuIqCBsIsVAJENvoKOlEmBUUoKIWgEDdGYZUNTgQzuye4XSx/pe9\njaC/\r\n=zlth\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "@jest/source-map": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/slash": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_24.9.0_1565934954828_0.8819733416027515", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/console", "version": "25.0.0", "license": "MIT", "_id": "@jest/console@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fe19cbff9acf848ded867bb92b15e38c4f4b540", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.0.0.tgz", "fileCount": 20, "integrity": "sha512-Yu/Uv9+VfWqyTVxTWmFUwPBhaH5daoeO/FPb0QadbRbNbJtV63bhNHGXOo52mTBI5r0YQaMV3x0swRfDgSthcA==", "signatures": [{"sig": "MEQCIAq17GzQCKYnDu3cPFt3SvriF7Z20DTZq+TTZEHSXKfYAiBDfMxg/0O+lHFbHw6IbwzRmPr7H2pGTaNgLSUJ6ybZQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrJCRA9TVsSAnZWagAArY8P/RL/SJZBU90FnHRIhRb2\nF2i19tbQbGy2D6s2DlTT6w8wVAD1nomhkm5cUX+pZgNptiofekrfsxDsCscu\nVVkj4U1hAYufL8X/V/R70SlwpDXlqPA1mYz8YZ5HlXqAnOtgbB/cnK5mWiXg\n/ZxZkgWufW6Dlh7EiWj4IRgUVfEvWL1RbkN6Z/lH7dyE50bsGSIe59+ucmXR\nFth6TTu6KZkMtIW/IUPxj8ZeT/bW7uqVFKnk4N/OaPblFZcbJXGTvlC+8qoy\nlrq+x7ueRN7UHczRN6brlyFxaZg3vwY0RwSYXp3vZmDlvfEOYLVlJ2kZqj7e\nuOOhh0dd9VM5DUHl6fV4DYNUaa8IhRMVouOSHtSpMSESyKQosUlC8yN/PN9x\n67K9uqFVkA4CFEkYyVdK43HqX3HBLD8TrhX/ekhTYfV0Fyt8vJfxgup0HQCR\nEqKI7faSg958bYRiLF+5aEU+PCr6zeGm+6eNo3+Y+gSSlpRmL/kBo5seyJI8\na3N/dAwYcdYU0KewTz6NUlYPXWj5L/iUivQvYEAUuevxNKkGKapjwslwJhDs\nUkpDDAVg+yZMjnGF6hLMWHVh3Su6V1mBgn+lF5iY8eJoBZauUm7UxlG+66nB\nC5D3Vxe0wxl7NhMSSoFRhaBa2zYsAg/tj+RH9KMu6aH/t7eMMMsu8qFUCtZ6\n2jwQ\r\n=AvcE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^3.0.0", "@jest/source-map": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/console_25.0.0_1566444232937_0.5362241713190585", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/console", "version": "25.1.0", "license": "MIT", "_id": "@jest/console@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1fc765d44a1e11aec5029c08e798246bd37075ab", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.1.0.tgz", "fileCount": 20, "integrity": "sha512-3P1DpqAMK/L07ag/Y9/Jup5iDEG9P4pRAuZiMQnU0JB3UOvCyYCjCoxr7sIA80SeyUCUKrr24fKAxVpmBgQonA==", "signatures": [{"sig": "MEUCIQDIgWhpqj2crPdOjeSOUMWNWusYnIDDS4Q/f1rKNzUPqQIgIgvoQ5a5piis4gyucD/rNA4FM8Rna01PQViB5wE+nbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56MCRA9TVsSAnZWagAAQMQQAKPFRim87tX08sNPIT+C\nsb9VxFR2OLsbCbVSYr5RM+OP7cKaNy4z0LEUTI5XthHbBGXgCAnuIvayBJvZ\nR1JrngvtUzOTbtaKVMZkTwOpW1sPvjBZl6+2w9FCwUEPeRnRvLOvtX/wof9K\nBluxxezF3PO1d0o7XoRqDLgFPPDodjPGNy0pnAkIww/BmyaBzIj5WxbKzT0R\nfMlOS0Dp6+lZbbHIxjuWCGhUXW31d7msYw3EENQcadgytxfAbLKjsW8+rDv6\nqwlQcXM/0nUYwSIxjEfBB+4B1rmAX5xxV81cQnuAdFUIOad9sqAwKswa9cUV\nJGMmE1MCf50Zql4YZWB/JIIplAj8Q631KQ6eq2hlLE9beqzwWzpZxJnJNnhk\nTWAAetX64yRS7yFpkRPSpYilhlcXDE8cYJJgCaqsbzvH5kVZZcGqezhwwCGs\nBe/51MCMMiPo3xvaqfc969Qr8zwQ8ePtMfbPQx7n+aCR7iMwVsbc7hVt1Tug\nIi7kglTxo6e22M5xxteIsmFdwfGbIWDp4PPwq+b/ip/1JtjIQfD4jdQ/N1y7\nPyUcMk4ybDDV3DZ5SNpnH5Izwkjoa/fGujrk7hiDHvwAa+2Nhmj75heUeX1q\nBZSWruPqEi+4tFYljATsh7WA54ZXM+jxqOLABsR0Nz7h1Y7V46jWB6aYdaoH\nUMEY\r\n=uZU7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.1.0", "@jest/source-map": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/console_25.1.0_1579654796214_0.5880345640343905", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/console", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/console@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a0282c782e1628c30296fbdc3e19b0425198a933", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.0-alpha.86.tgz", "fileCount": 20, "integrity": "sha512-13K9fx37Jz4uK51/biRHhb/hMS5h27beQtkGUMqkIcrMltJwWw1R8DTis8bKBjV+mYIW1KKJ0cTSf5ePepuJqw==", "signatures": [{"sig": "MEQCIGEvlF/9oKKhjXTBNX/1ixS/sJiBbvCcCxf9JJPeoCQ9AiB1IDGTBhxbcQZNeGSX5/zlp7OqoDwNwDd8FFT3dTomTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HsCRA9TVsSAnZWagAA/g8P/0xlgM09zhJmbrn+nb5z\nd6hFv98vV9gdXr6uUG7VRV2DcfmjMhWGG/07hwrWq0qYfocg0jLiP3qdfycV\n5TVp+u2nXtVPbIm2ycoGvQ1qft1pmNkzVKCFGrngVW3xOaNobfmuY7lRUTYv\nA+iFWwICwi/KZGwcXODmt1H3qXC1T1i8be+ioYXB45NRpDAEdleWqKGKKh77\nyqx0C4RBDTPJi6mFcbiXZ3GQhh8FZh1VUfzzToCpywpfOLfaHwty5yO9eS8O\nb8sUBuUFf0d6/h4h1gU2OFrokuVERQ0uS8/JmAhlpNOfzJf9VS77o/ibVhEE\ni/AjajJfLrzB2Jy7MQ1vTVdxkqZV+cmLUiClmBDVmi2to6tyOLU8r1s8tEO8\nzQTDrNshi0PH9n1H7CxDH47v1Rfxq+alWxuauETT1quaPNTgYJtyssp6y0iO\n97CVVQdvO26r3QRbjB82ZjIAxPYYjWvTP7o3rxLV5CRbUUPoA6NZl/5/BYqz\naQ5uylY9RgZiFtxO665SFSzUXTPIzZX3SZhFIe9C4bFRMxbEfLLRNdMpD9q/\nstTbaxBr6UjS/Z2SLGyV2l9v8fmTb8SNFuMMHnXszvyzdNQuBDm9xX0QZmxa\natxjfvvrS5F2d8kMIi/KR3kkhkgKYxRcWfgredZX79JmVePuVZ+v6LCg65CO\nkLU8\r\n=Pio4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.0-alpha.86+cd98198c9", "@jest/source-map": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.0-alpha.86_1585156588388_0.9951584404935809", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/console", "version": "25.2.0", "license": "MIT", "_id": "@jest/console@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2c37024d97ded0448be8c846db4ce54aa4b66dd", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.0.tgz", "fileCount": 20, "integrity": "sha512-mUQTLxw/q0S8duskmb1PY0Yq7RQ0Sr1st7pUhFCcJ7wcPRPFs5t6k6bJWTellAF/8wH/ar8tZSwSIiBYAj559w==", "signatures": [{"sig": "MEUCICdFWDgdHHbeiiHBrAX5cXQjicigKOppZsRkP/5f4xYfAiEA5FYVjNBIWvu9vx1fZkvxwTJlrFWqzlySyOsbbkmaSXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5u6CRA9TVsSAnZWagAA+uwQAKANfi9PkuuicdX2/OMZ\ncDPo90CuiBl9R7Fay5soO60/S7u0TsFO62rkUl6Iw00gm2fwlVgMQLiXdIYy\nP1I5z1mCo/MqjP+AT1b7+82SabbWjsP92a7PNK9c4DFfQv2N1akqMNS+rPo5\ny1XCKtaiI9tX9LRQnZxF8yM97I6qAsiWSxbuIy2U3i0ZHIqol4KheTP1U4Sm\nFr5y31HdF/AeoYYNuJ2jQ3Xf6pah+s0oCUy7wdmmMYVob1TQ9LZj2sndrKvE\nvkdDlrAbN2wqmCqktxuO+0YFwNDpvHCHDVhbhb3u2YBr5DfLaMIPXOO3lrsN\nfYCGo7IBBdBWnLGSl55LWLq1Wu0b9Kz7PCitPmZlD/kBZLrtRJ8uX7UljgzJ\nYG68En+K4p08It3w/VlMFQNsYepU01hsqdNbkIG4k6ogylkJnS3lfMgHdvGf\nVhP1wyE1F9VAbfYJFIQmVwhkOm6vp/thn/M7IJeyJtiwg+AbgXe/+q06jLcg\n3pSQMk2eHZjZN4jyutrH4CdGj5YGDwkwgkBR+izLiRSEROdOoFKxLttbhhKa\nh4Zg86+oDs5pg+qzRaH3HsjoiT9tNMIOmsAMsfx4sY0P3didH/8z8QUimIdy\nKVI9vjU9Zn1byAnbDb+FZH0UCho7l4tVdsnC4zmmqevI3VIIxdBo2XL8zbxj\nG/E/\r\n=Q2OJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.0", "@jest/source-map": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.0_1585159097578_0.06822257725933434", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/console", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/console@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8b1d5add460806465b404b55d895a89b74954325", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.1-alpha.1.tgz", "fileCount": 26, "integrity": "sha512-kE7pmbPmAc+R1ViaYghF9bC9WbtqPoFi4/S4k6vPmaO4GU/vmsKjjNpUk6SfJUFFOHvJxqTbrKh/rXy4ONRYPw==", "signatures": [{"sig": "MEYCIQCfsosLDTr8KLPHH+LpKp+fePYAGrCBQWeZojp8GzIsugIhAP+E22A6ufd+CVicg5buH8T1XxNdRgSxU2wnPqVylUBX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+xCRA9TVsSAnZWagAAziEP/11eA8eLMcmnAJHokIdM\n6jMSVfX25nwfs4GPnKVKvRq6kuGWxTQOfUJLAo0HwuPD5qf2ZpVxI4Ijo6k8\nlaOmPkcx27NhJcM/+o4PsXRresm77pUz/BBJWHS7ooue5+WiEx+6KpaOZ/N+\np61PQ7ZXCzOgQjwnqmh22xJF4P6HNhHEpBSFem0B96i9F0L7wOw965Zyy+3f\nUGCOgjbgl22U6onrQ+YdsY6G8kmjWX/VI/DQoyPqEeIA7fQZQlKIRV+cmaRX\nqBipKcf+PJX+7jhX2wpHSBG1NaTHgQfBoiU9UxBxWLpgz0IgQlv7s8Qb101R\n569aYmDIgT8osL0XTnbrTNbJd2v8lbt6ATAIwxKt08Z0Mv3Y4Ra9soLFgvEF\nvvvCS6U5OEup5Cu8Zyj8+AZinKq+KVxJ04dWLD6YwYVrQ+c9Jv/inZ059zHa\nZBjSBsV+fQTaRkACw+qwGNj6IftmbItaGTrhZs6tWqHUYObAsRld2mSQmILU\nQFq/ZCUAsUBOy/dWubtvyBNDTzhLUQMpBVUd8FoLexDTJscNXUDGvvsVfvhe\nCs2o1g20C7oQZkbnjm6B25dbsJr9had1Gp2B6o1FGFaZaNFNYhFvGKui9mIm\nm+3iKaIz/yyqocm5PIM1bBWFPeg1tAwlu9wf4KP4hmYoXlBIf9r8dsk/q6LE\naJ+o\r\n=DE7L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.1-alpha.1+5cc2ccdac", "@jest/source-map": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.1-alpha.1_1585209265123_0.2660414050174431", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/console", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/console@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3706f9968dff226486e9ffbb720eb75210988c27", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.1-alpha.2.tgz", "fileCount": 38, "integrity": "sha512-PkRhAT5Q3GjD/NuVECDUg7nacp+t0aNNa2sk91ArbXR1csWjJ69/GVKl7/lvcOMhGoakLnqBHQC1dG/bGvOmQQ==", "signatures": [{"sig": "MEUCIEg2tNDtP7a3ilTCw/BKaUlGP+10kDxlMAakwJC1eeRoAiEA+1y5H8mJcbhXYlaMBFRAMPwZmESCHYcheE+Xvk9p4zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGN5CRA9TVsSAnZWagAAMC4P/0yTjdbbOredCm6B/KSj\nrbWvf9WuvaYMuWajL7ezhlGtx6p3GwnzLdQ5mBC8X+xosVh9qdt/pO+mC/4z\nCpKSVtVHs9xOfXEvCrhcSuCn9v8vQpPJHrYwoO86vtQYcWspNln/h0mHZ6Up\nhbaudi6SoBALRtKi12Y+SmAu+cbKKQZnyv4wQQmYCZoEjXlPN6sa15MQgEkU\nUjx/N2ZkyMwj/nQQy8ACCL4hu/fTuGhXuyZyo0r4+0oMR1BKrzTJ8d5zgUUX\nGfNt3kOUR7sgSJkJg4uzICSqGBF6aKr004bluxcxUldvvY69Yv3rlShamupL\nrHVfWKCWwZhJ/XEyRML0YTEEHx4mRH0EVt9FOTSHjAHdZclX42wnft6+9x2l\nIhSO3kd1LTMpE06Fb16/fFUKPpT4iywnr8l2TfZ9fRUNQO3XaU4Skx4Z+bqZ\nKqxo2FVxtmEpdundSESOzAXazgT4SrJYp8+qNx77io2k/SbfARfcs16aamuM\ngaguRSvbtBod693fyZtQQbbks435miCdIj1m0Dk0C5emhLKMV7hFjHrJ2e0e\nV8K5SWzDA3HEkMtQECflLwmn7GtFcL1yGTgKGdxdoCOKvJkfkcEpFRJvmWvi\ne4TIh6aPUrGAn+Y3pgtd66HFV8kASW9aXOxKPZTZFRWfTzdLUmNAFl7FDKp5\n7ccI\r\n=nka9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.1-alpha.2+79b7ab67c", "@jest/source-map": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.1-alpha.2_1585210233299_0.1817364582177634", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/console", "version": "25.2.1", "license": "MIT", "_id": "@jest/console@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63b35b6a2b67f26866f8dcbb9725452a1c8c0d3b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.1.tgz", "fileCount": 26, "integrity": "sha512-v3tkMr5AeVm6R23wnZdC5dzXdHPFa6j2uiTC15iHISYkGIilE9O1qmAYKELHPXZifDbz9c8WwzsqoN8K8uG4jg==", "signatures": [{"sig": "MEUCIDvwdrnmbKm2MPRTgRE6fZffBL6FNeXvUaxWG/zkSWoIAiEAm7W56q7oKzb8j06JZzbttZUYDs9YCDXHtNNgy0uivss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9cCRA9TVsSAnZWagAAP4EQAIp+61391aSz6vcM4WAr\nF+vkDVEH+sqRrRKOLSsQJmHWpyj08Q36czvwstFlKZR77Tr/fYrNXCPvGagF\niWcUiyo98wzyvIqiWoTNoaauM3anthcfIHAFMlY6BpHduOECQXaH5D4iNs7G\nPw3I4VLiZDDG0hpQ2NtIgiZF5H+ir+9HTJJvFQVW5bkltYl8urYXK7wPCeEi\n0OSYb6cmwhialOcTxeLUp9/Q/FDzC0V593Y/wm2fJZAmsP9z8F23FX8GIxcC\n8uZARVz5tN7Jj17vcEgETUlEPX8tCfRpsQVmUhlhP2YlC1/vmXPQOtPMcCJF\ndC+Nyw7XC55wEDmc/UQfamfp3CzXZeYgjT7caQHNPYZe6+unZCulKLfPndc3\n55p8qTfncrv6h6n01Opu06qPA1StvwleJYfPox8kt/ZHVTkaN5j7dGUrDTip\nwFyRpavuqjutexbrFWas3UufW9HRiAI+8VWsNpQLDBWsVXOM6NRbXiKkWpAx\njZbTgYJR0cuzp25g3d6JOdTM693IM8H0xnY0fL0R3NiK9tcEWWApdKa4u5Hz\n0XaWGnM2mAJrzv+IOxcVE8UajSoBb+BdSoeXNaYckldJXy0tManUphpqv7UP\n8k24rh3TBBeOVUhKvZ+vfN8OEC/uUz5fzqLffAUGFJ4dS8qmu/XQso/vTPUn\nwifh\r\n=CC1R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.1", "@jest/source-map": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.1_1585213276282_0.04445540782877688", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/console", "version": "25.2.3", "license": "MIT", "_id": "@jest/console@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "38ac19b916ff61457173799239472659e1a67c39", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.3.tgz", "fileCount": 26, "integrity": "sha512-k+37B1aSvOt9tKHWbZZSOy1jdgzesB0bj96igCVUG1nAH1W5EoUfgc5EXbBVU08KSLvkVdWopLXaO3xfVGlxtQ==", "signatures": [{"sig": "MEYCIQDdWZxQMSNYqcG5LmUOMWPFp8HYpNQuQFbcNNdNmyi2uQIhAPWYTZezy+ev49CRATot86dGTgLUuXc1VbjdeWUeGsVh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+TCRA9TVsSAnZWagAAVO0QAJwqxrtIpU0pIrQIYRm+\nY46XJOMeSYNCsQHKEXs2J8n/GSHjK/7dccbQA41zElSsME2IyQ7PU/biww0U\nTjPCSAOG8mrs0QVzemcAeVHo/lR3FeFIKhsWVdFuKaD98r5I9vdXwNHAOKHa\nnhr5Y5nW2wJiAork49F9ogJYadFYH7gjX9mykTXO2sxOiluFsCPSldKqyLH9\n0OvBCLWuSiAh41hYsSlq9MRvtRPYXQ5j84ofnafB7dAGcDTSe3DjTMVjebKY\nF/AMKIU/mav77+u8Mh8ZIHqnK3WZtjK6e+1BD2zCUjuiXIJd7+wMeDVPc0SM\noRKB+/oPc/MWUERa5Y/Z/hIbLbkB0ulkkqsBfWfaQW3oi1jqa/Uuqm6vNxVG\nvP6XG7cg6VfSHNuQc2dMYkzSfb4X/M66c9rwxKL50Cf1mqDya1frMIQm9RMa\nvwC8pLTg8xk6pI+yFB2Mt3cMOAEalIhjxd0fueJGXlPswj92/w9JsItTSqGj\nTPv/q1HhrFgeBSpINwof500/lV/fV3M3RSVfs6tdjDLTMv6euOvfUnlRKZg0\n54uQXpnfPYwPBzVaQPprDx/0tUgClHJxukngu7om4gjbjMJldHXyjWtDVmsJ\na7W301Uogmpp/BqM2UTFo6jiJ9QY41TKkhMLwcjqLyhQ1f3LfQGDhbos9pEe\nvuHx\r\n=v7vu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.3", "@jest/source-map": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.3_1585254291450_0.030254892478684514", "host": "s3://npm-registry-packages"}}, "25.2.5": {"name": "@jest/console", "version": "25.2.5", "license": "MIT", "_id": "@jest/console@25.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "18439d33701a537e9251a2920fac123b3c64b9c2", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.5.tgz", "fileCount": 26, "integrity": "sha512-HUvymiMuMvM7J6Wvnv+rkptRa/CWv0Hv3dvpnuLU0q7/iiBGR/h2GP3HqxAvYYaKuiDBswNqt+0fXMveBVt5zg==", "signatures": [{"sig": "MEUCIAlojqmjAp4g5uE9Hiau+2TQrIJEg7t9Hesc1cdWajL3AiEA0ymrSxzC0j0i7nmN4zS3+Z6hHksr0aR1u35G8nYyAFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb0MCRA9TVsSAnZWagAAJ00P/jIQ62Vc7gdhcaJ5/CJz\n6zSKa8j3GCLwnl/LqhV2wHbgwlC6g56lIVGj4JEmSGuIljwxspATpP8vLqu5\nJ4hmf4nb11PaBKqy57hHSlWUrh5L811ru7s2m1txwZVJTr4aq7DfSHUds1oL\ntKZxaukWZdLztW0Su16VsHEJ0mSwDuJMaWSUopDKyEN2T4rf8Zq0C0Wbovba\n7cM27OsRt8LKVttrLs/VMqjvbZSw1tRoI6u/cimoZgXvCx6ymMVrk+5Vms1J\nwsUQDf2JmqXdUv2fPG+nwHOqsqDRQR7jdQEOQhJIh6LV8A8ziORmiJnTPvdt\nJmHeZtDVlJCKIDDJZTOJWKcE8oBEZ6u1OmJ74YRaVgik9LgDxxOWWHdMAoIa\nUDfdl/U2lPMbgzo4oeEggULtv8gM+X1gzGas8r7rWvHYdE9Nxn2DJC+sohS1\nkGUHC4BwsxGT+I5THVvRkcX0gbWTZ8Wg81P8Aap2sa26tVDgk0XK8NUQM3NG\n/B0Aoz65wxkfqAd4zGV4to25LSZEDNz7lY6oflOLVpWIQ4O8/8vStA8NHL24\nE9r/oimP37C24S7XqjZKc81zuZC0z9Q9fng1fEYQ7Cidn70lC9KTQuM5wN35\ncr3Dt3ah+ybQ7g1OvF2qhcqrLrrkynDgTAw70q2uknThtLmvTBrec+DD0q52\nqjZb\r\n=V3qN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "964ec0ea0754caa2d8bef16dc89c1f926971f5eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "deprecated": "Faulty publish, please use 25.2.6 or newer", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.5", "@jest/source-map": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.5_1585822988355_0.5351882917998099", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/console", "version": "25.2.6", "license": "MIT", "_id": "@jest/console@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f594847ec8aef3cf27f448abe97e76e491212e97", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.2.6.tgz", "fileCount": 26, "integrity": "sha512-bGp+0PicZVCEhb+ifnW9wpKWONNdkhtJsRE7ap729hiAfTvCN6VhGx0s/l/V/skA2pnyqq+N/7xl9ZWfykDpsg==", "signatures": [{"sig": "MEUCIGf1rQJJZoppr2keO6644nvQzDF08GgK7pvjOdXO7EaOAiEAtBDNpWr9u6a/FdvOx6HEtwqCVHya+Emw5sCUiQ0SdPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6BCRA9TVsSAnZWagAAhTMP/RKuGiATm+2hxpbVX/M1\npYyipWBSCdQp+jb1S4beXSlFSn/cpUaN83ye0YXbLFhZNssfXyvyn10G28gZ\nAggUzdwiiOynSWLQJHOR3aam2iqcnM1/HRzqzJdSCeRH26HI0PdycflPRxQA\ni/RSjDA6r+zx6004XqphPjfYv02DzuHe47ezOV4MzTacgOJxJ771NcDiRDrp\nhuhUqysEizWPB14Jg86bJjZ7L4AevsQaHvwRuCIMd66jhPP8Rib1+gQoweTo\nQXqrnU0CSGPi7qAiZmN3/BB8/SggG3dD4PR3pjYTteSwfVAL0yVsWkX+bOON\nvtgl7IUmdOiR8gEgzp6sYtw6oSiil4ZZIUvyfxeY9aO+o+vDV0xATss50DcY\nDJy+8YAONq5dfMtcxhiEvs2zWReNf7eFqrd9J47cG9z7FnPPLF4P/Qll5WhO\neWJ+PmK7RGArIprxNLmehOY5AhvnUtDNc7oW0bCk92JSyPZBnU2t6cA+VcHf\nDnebzJ3No68LGndlpJZ2daxi01f2CI/KuQTU2+ymihvh03/WqSmjz3XwXlfF\nPHJ/rKKVc+2YjIx7MlmiVmmgDL3oGoyk8lYAIzZZ27xkw0AOpoZ9Q5iJk5pn\n2J44vAMggZEQ8lykS/a6JvQ9H99jwsMbqNNHmaKkwt3ijIJSSaIsGxdhByI2\nnk2G\r\n=+nIJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.2.6", "@jest/source-map": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.2.6_1585823361189_0.8645772363573418", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/console", "version": "25.3.0", "license": "MIT", "_id": "@jest/console@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "33b56b81238427bf3ebe3f7b3378d2f79cdbd409", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.3.0.tgz", "fileCount": 26, "integrity": "sha512-LvSDNqpmZIZyweFaEQ6wKY7CbexPitlsLHGJtcooNECo0An/w49rFhjCJzu6efeb6+a3ee946xss1Jcd9r03UQ==", "signatures": [{"sig": "MEUCIQCynBHMXMPjAHv9bh8mniVeBedS7tHkO1m/q7RpoWpjZgIgS7ISfo9C9Vm3dZc0PqXNmvVWU2JZjnvCgYqRXGTJyxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/JCRA9TVsSAnZWagAAfI8P/0tO8+M3ecb<PERSON>26ialkRS\nb7s2V+cAdcNstKLrjtNbHuKgBBVmAMKwvi85Lq7uje/T7BpsMF2sqanblsLg\ngCMH7QWs+EhRPMqQfl0l/QHeoAgiGSWdZYzSvHhExk3nwGhu13SMzlozr6g+\nFSMlFy8+tflB9/V96nZfHCa8We7tvyBpDrY+hD1CVECFYeB7jgeWasHz8BGQ\nIBEYJ+TkQUJf+JcyF2uTnFPnZSRNdPbSaCjW6k41rrIxwF8WFcgtSamtp92g\n3G5BteyUxvQ5PnJSTaE94txoXutYAGMTkaTrUlF0SdwxnKpVZxqQ9hJkREMX\nJgLyf0sRTx6pTSymA9E+dMRayczQAWpi9pL+pzfnOh58yCPOo0TeMZzyT/Jd\n+ogVYsVySCVoxwliYwW1Lf6bzeYzsdIRwcxbW0W863Bn7VVZ34uyWH3BcckY\nQ10WB6cGudMXWBOGcXVcyyEGIaBy+gkH1bRi0rT/U3OUFBQmEwg4xPZSNALO\nuGVv8f7P/6JU5QDctXKmpJL7IdVSQk/wmk/Ifc5W4hqEJexNx70eik/acGro\nmGiuQd9Akb4yq51EX950pm31VEq9QHxhHo3jDuLxRdqu9j1HgycNk3E5j2TE\nJYnwsKuXW6VO1bQBJR+2eSy6g5yP9CyU+1c7Ow6+2fky7FPvsWkNGTde3+8Y\nJLCr\r\n=GkVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.3.0", "@jest/source-map": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.3.0_1586352072701_0.9896521626018768", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/console", "version": "25.4.0", "license": "MIT", "_id": "@jest/console@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2760b532701137801ba824dcff6bc822c961bac", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.4.0.tgz", "fileCount": 20, "integrity": "sha512-CfE0erx4hdJ6t7RzAcE1wLG6ZzsHSmybvIBQDoCkDM1QaSeWL9wJMzID/2BbHHa7ll9SsbbK43HjbERbBaFX2A==", "signatures": [{"sig": "MEUCICQgJJUKySZargngZ6xjLGUJ1mlVx1GZTl71Edj2typmAiEA7txnmvcnqO0In5TPeci9GSr5lAKZnv8E6WkUP+xOsBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMemCRA9TVsSAnZWagAAD9oP/jmxukXV8S+/OiJIT4dj\n0q2+6Yy041HdZ/UGKm9GUqgPGfNP1NJPQiH9qCnYt4yC/jRi8P9sxfAV7s2e\ntZDKPm/Ezt8BIDWPgdjcq8fLTee9GAIIjIz4otA9fGs6LyutZwWNiNg1goe2\nG1zE6x4GVW/coQw1T/TnsZGrnIK5hia29kGyVSIfBoH9xKIwj25Aot0OZOHF\nDvNt0RIBTVWuDCEqMrfIZvYqxmYWNkd2TPNbtxao8fb+wo7JNGDWzA4jZ0kb\n6+qlnrXZQtDs0aabEQYYHvsyor7zfLxp3crKzeS73pbmeeDUw2bVoRy1cMnq\n/iNQlLLCKeq7h+0A2d830az7D75TGz4MTgkmmYCVlOpxpB0jOOXgiNy5VNFK\nXSxRU754rPeouv6Q2pSgXggJoEI2SLrC9zqp4Vdo/4Ru/tBm8oC7Wu+qXQUV\nQwaa/321KS9B//ysucYh4Db4DDC/cQbXxjgvxaGdQFI5qyLRAR9w9keXQ3uG\n2exZIG0p4X36MLpJT4anggsKel77eWNNpT+2P+IBpvi6Klfe8G0UGYOXSyZl\nh3KDOYDc01vRzDlu9yarugQE1Teb9DEQDYzkNdUm3GxpnkdKkg3s20/eJIbB\nHz/t5SUREHX4oEzBgHe5Wv+DwxuqzmTt3DoMrbx5RThSvmEFE5mDCo0uwHOR\nnP1l\r\n=xtro\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.4.0", "@jest/types": "^25.4.0", "jest-message-util": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.4.0_1587333029530_0.7920221563090921", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/console", "version": "25.5.0", "license": "MIT", "_id": "@jest/console@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "770800799d510f37329c508a9edd0b7b447d9abb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-25.5.0.tgz", "fileCount": 20, "integrity": "sha512-T48kZa6MK1Y6k4b89sexwmSF4YLeZS/Udqg3Jj3jG/cHH+N/sLFCEoXEDMOKugJQ9FxPN1osxIknvKkxt6MKyw==", "signatures": [{"sig": "MEUCIQCLu8WVjof0ZzvqiPhkHAxa1ieAldnGwRVtwSpoctFouAIgHCWUOHmbVAPWrro3ULtUIlr14liLLVrWQsK/XjkK8os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfXCRA9TVsSAnZWagAAq+oP/1/IHDx1Zs8W3BomO2Ia\nHD6odWRTmHy28vZHWY5M5qGowDHHHm1HZebzCw1TLL2Xi5XWc4y/Dm5AbaRW\n3rm+Mmb07gc0nnrv4hswPX6Y899s5kK3gDXaW+nicp/6AxUikulZ4+GAtsSx\n1ZkIwI4/uDQPHxBK1M1y56xKZHtPQDLlDGmBLXdMTklo2WrmTeVhVhIWk0cA\nJac1WwG3cI+OA6mfLHad9hgjbDMf+1LjemXOb4utF7mDBdgto0GGufAGxDlz\nR22Q1q3TpDvR5tP7P4JI8qK8LoJEXWstFTUg5lIvdL/+XnVxZjEsypWQjvzT\nGAaebg8ZV8mhSAND2wJNy2T8d/AE74ipykJ3pZ0+G0KGV90j4baiOTSrV8ry\nxSlmA9OdMD8LpaWcs4rsXNiNyEsTV5OuNc5HdiZs8MxWgaSIVGX9ta7oPUQK\n1pNKBTp27OkLWpw3/0IotL6ksRjGFLvFPN7P9Mg2/J3uqq0DIyJQlD9oogjN\nWGYsIsrmCIozZdX9cRoOBuD9Vc1vz9AV2o454QfwJp7pUCt9oTTKFP/lLuPT\npBEFCnzcFiPJ081pm3UlBG8cGu6eC3mFlwl0at50LmwZzP2v9GH/X+mdC9pz\nrw9ophzZTFvQldV8LvmBkCe/fUyqOSPGUXhhCwyZSkERURGPKDnwvSU275/d\n3QnF\r\n=sitT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "jest-util": "^25.5.0", "@jest/types": "^25.5.0", "jest-message-util": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_25.5.0_1588103127405_0.6501251208296743", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/console", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/console@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "95ed3a1d4d104fe59e6c94e5d999edf747f75292", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.0-alpha.0.tgz", "fileCount": 14, "integrity": "sha512-tzRWpm9v3KP0C7yYfeS1UPj7blaiWP7RBl93ZjfExxBq/Tr6Mz2jxNsY0FIl7YqYQak6NNF9ZgmU8kNQjUyROA==", "signatures": [{"sig": "MEUCIQDmN9hTyy80AAndESucU+XRE+jcP8w+Fj823ZeRCfY/XwIgUTTIIf3vKIvPOKuzjc5NivQt0idLlfvBOryD/5z8l3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19690, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPVCRA9TVsSAnZWagAAGf0P/3V5AxyT7v1O34YT6NXK\nuuQAdPue1/2ugXocYZ4gMJauI1ivshV98+dhks8PlmShEXc3W2KHM6ocKsrX\nJoZeVho/kOyR9MQM7P1dyYmfoFeDseVGKs8xC7di8k9XT9JA55J6wmTS+E2l\ntnvOPgxjESJn0NJacz9q+bmrFY7HrOz63kVU1cFP9YdMIjJFtM2gs4zJnUWL\n6N3Grab0tQcGiUIWUKKEHdwCOkw4Q3W50huX7UfMqVxhK7MZu8URMw2hQwPz\ny3PTyiPDhpc/cZzfPTwsx7rt66ENM4TjFGIvOqkU9I+dkUEKdTTRzWX2KEWF\n9lmZInAy27pBSCFPbQBju/8iVxNHVHXbLkZNbZpA2+wqT6tS8uUlyNRkYNDv\noQFgNCQ9npS6/Xrr+g+PxejuI2JD5MRiQGvT9BdKr7YoQvRiVkr4VQxq2gGp\nCBtR8oEIsYTiCJCtCp/7aB4ziINcBnl1IIdzAE/IDoPL2X2ABQ9rmH9oY1gr\nX2v3puAYfIJA4/8pfiqXW4GVRi+0X4I7cMe85jvxat+MGEtU40z8uCK5EkwB\nkIHRFww/4law8X6v3fRWj6sDrqHVF1rSC9GDgc2FjA5m5677GWT8lPbt26Kf\nrxyTKM06OR/gzfGkVMsDZ6ICXknw7IP+K+6b22lOPmo2DT0wm2IZF/Yy3JWd\naDP0\r\n=YgK6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.0-alpha.0", "@jest/types": "^26.0.0-alpha.0", "jest-message-util": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.0-alpha.0_1588421589057_0.02294890406465755", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/console", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/console@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fb652edb56e578e4be9aed006e47e13c12a3ee49", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.0-alpha.1.tgz", "fileCount": 14, "integrity": "sha512-H0tTqX9WZvpND3ikwWy4zXMDeRISAcp0BDpBtXOeVqXxJvJkEtuD293p/Gz35II1C3fnbmjX6P6+hvAx9ZwF/Q==", "signatures": [{"sig": "MEQCIEO88bITzJsmqPWf9p4ZzdALn9P8xEoF96NEk+nAQJTTAiAKyuHSRWmwEmInuo/zDMAd6s4tzfJfdYkCvjeRFhuafA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHlCRA9TVsSAnZWagAACeQP/A2oB6xSU2JCSiOqwCC8\n+d2Z83YXwUf85TV7w/TA94FWmHL6LTuQ62PV41YA6LFVTbVP2e5g8zP8AzPZ\njnq1jgpITOwKvceFjqPsnSk6SsTDGsWEIV9JjSvXciIlnjkW5ahkBq4Wq0p2\nFFjDpZNyiv9pirCXnGg+lg5fD21Dg+n55+AQ+4mSRMq6DcrKYVj4Sall09xy\nv6hcb1/pkwtIbcOvGzAcKNlYONmx0iPO+2ZVIiRiCpXi4UFKxhGd7IWKvsLR\nyCEfTowMt6D1Ct233IpxQFGGIczXf/P6eeU6uKMY+HdZqAL3s/zIbr9W2Q0D\ndkQsAyMNg/ZmB2tb3gNOB2qYrRE0x5xBxSUd/h4wqCdbTx/6xPFU4cvLnXBE\nAdnrg25XCQWOV2xIbHwXm8KtUjGD+CjgYPjUQgYOCJbaLhyPlP6NkKis8x/A\nqNVmjxm1+x9Ydko2Un3fbratxxA9uc1k9WeChUUlHWBLdUQcHMTFDuNWEQo7\nqi1ypGGkhuq+BJdJhmCjcDesNiPDc80QpEqDMioHT0ixsinAAdMK1PCReuGo\nl4qHoXxzXzgr6353aa+56mqWuSLBgmzWQMsYHxnX+WvG6C7y9XJkrVBe9iz+\nu7h0ao7z4hmBp09BBrpusEHiYjI3E27jiDaZzxntbSuGntHRDNQgacmGzgjj\nBe8l\r\n=R9l7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.0-alpha.1", "@jest/types": "^26.0.0-alpha.1", "jest-message-util": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.0-alpha.1_1588531685410_0.2437588599319611", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/console", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/console@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de8ffeb12522121bfad15b47736fd9800d5d83eb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.0-alpha.2.tgz", "fileCount": 14, "integrity": "sha512-rHqAvbWVmKvZgtAuj94fUsYAeVy+V3mEqsKcLNqhUNpLPDzmGO5hcaR1EnCMQMEjG0nzOpSlcAQIdTi2pU6yAA==", "signatures": [{"sig": "MEYCIQCky6OCRCuVxKEcOkEpHWFXL5Lty+z4l3TvjFEgDBIVtQIhAPmdYpqhm+lNiKi0NhL+hUrvodCg5cslktBPe2CM+eTO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1UCRA9TVsSAnZWagAAZ7YQAIsZCM5TKW6fDWj4P/c5\n0s4wmYkXG9xRiHwYguOx3WpsoLfAabH4KdQriJ5D8egOHe5mkbBXAdigBTTz\nuRhGmmOXjI9YScii7znTJyhH9SRXm6Du8VZJTYL5H4JUlPATEBYFsP0AYPx/\niAPYbD2CoS0euOEWSnvfFleo7PdQRtwJp8bNmahCakl4xLl/d3Qvf2ywIZZh\nLNdLEimVAuLNGuLO0c2uWO8D3x6qTZfWaDD4Lyh5xTovM16k+hEjq1CKLEF3\nNZThuL/ES/E8ZfdNbHZlHkzq3wbS0LrJoDxvkTHMR6d1nQNjUtHUV6uBha+G\njEWe8Fyb8b6O6GsAjjEuPkJcQ/F6QH7wkn1JTU0IB83RMObq4UPK5cc3TV0l\nbNdBJvyanw6bKMsjKEoldpGeWHHdbogJqzEkjm4Z8AkkvNvE0Hvs6Ic4auJ8\n3aSg7dCY8Ut+CbRGJKyZlczMQ6EDk7TLHQb4u5I5PDqtA1UbOEjtz8c6/28i\nVHlJlDjnfuQk5wyOgIbvM8XXwWS6HImS+AQKnzxGeOM7cHrlUYco1I/Qs7TL\nyJucAgAZclAsxkBsEgYfma7NPzsvfeN6Jg9hax/QEp+0iEgY9ouwCCraTZR1\nW8NwFKIi0PVLKnrzeFXsMkDoLtj/d4ZJpJUkVyOBq/EdWA3JeXVK7zWh3q7U\nywE5\r\n=WHPI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.0-alpha.2", "@jest/types": "^26.0.0-alpha.2", "jest-message-util": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.0-alpha.2_1588608339804_0.4026209946295187", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/console", "version": "26.0.0", "license": "MIT", "_id": "@jest/console@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9df6b00aeb800aabf6859337bced5f05b1c32005", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.0.tgz", "fileCount": 14, "integrity": "sha512-ZyWQQN6skHd7GtiwcZp0nz2fe3FHYmhGjt14zBR7kVOIXUWKlu7JsCRNWwLwVY4/Ja4oE6xYXYzIB7As4D2JPQ==", "signatures": [{"sig": "MEUCIG1sX6AAmshfox0Wg0mSjW7ETuJ6WQt9W0t26SLuRY5aAiEA56fwuKzT8qZbBv3rgqDwfE8SnGEHHdHrRuyNF5gJ9Qs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaNCRA9TVsSAnZWagAAWaQP/0r87gu8uVVWMlFkKcwP\n284O9vsiVAIZf819+y/aaWlIil6d/wJ/iUfbeFQoaqcA7f7Ex8dRasmuXhKx\nNWVPnkHdfYB4Y/xxZvxofIzMIOK9aDlKNUU1iX5YTMQqS/rLpz0PxlqdG2/q\nIfppwI/z5+svPT3KdPnNJuZQjO3IkC/RYODN9cA2mkDsO0u57eaGp7cVQq+t\nDn8YKHMf5VRaFVhmZVDFxe6/QGQFwguYLKBhaLCVB9moQiF73uScYmxssaxz\nFrXqqxc+z6jsNJS4W4epAafkcLnQPVDio+4hKzYO1ph2Jq+AYUeI3p5nYwMl\npvH12YFvrCdNdsg3XB3NsOJPpeJZdK434xtuZsTY1jzNZ3L15tupymn0ACks\nEuk80Ijr6xE3Eaad1921ENWT691BnZl9gUpmZmo4bCNzJO1/KBWZlRgyRRG8\nBT2qmGNLFBDZKjBKIpCF04sb6kiEwo/p9XILqeev+W6r3LWCiwVd5InHZEk/\nTflVPLNmqAWN9ZXy3/JfJbX9KnaP2Ql48u9AUVuKsSLF1YsLRCbGrf+YYo3G\nKTXXIdSd2DGRqtPRIjfTTbLbjkc651Me04gXnzzZShpIObpDOgSAEXp0ZmOh\nX0yAj9b1SipKW+qxHyD6nimpdSG6SLJX0iZagzksrKZXQITyvCS8nwWKjWgY\n9beB\r\n=SaFf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.0", "@jest/types": "^26.0.0", "jest-message-util": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.0_1588614797011_0.45204372275729177", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/console", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/console@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "29b3e39217e815574c10b629b0040350379b159d", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.1-alpha.0.tgz", "fileCount": 14, "integrity": "sha512-wnGqp2TTT6W2aC2Or6XY5AzUQ4mgBPUEv7yC6xpb/TJup6t3RgsawZ8PQ8MPJ8Ma6V//paG2N9/2ouq2/a26kA==", "signatures": [{"sig": "MEUCICGnnHbS46vv60WLaIVRmcx/4sJHrwYLzKb3r8lsV1Q6AiEAjO3wxd7shNbeuB6mgIEwyiVOeH39GswuY0vA6SQkVa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQkCRA9TVsSAnZWagAAAGQP/2ix8mOr5l8QzNh4DN9H\n6cannp5prW4RnxA+NV+eMvkcDVvnd+8DWh4KC1a2tk5BuXoLySmNgyzNeGJ8\nMJs6pg/5S+kBkrGnOP70EWZxmmtPad4JK7+l+5N6+RmS66le0rXkvOrDdBpE\nS5GveEXw0C90yAmnvw65d/iju3RAyLKYkVkSL7f6RrwJCMW/Qda+y6AB573s\neXF2fSZjxiaU1mHxD2UMnfXEuGvzFGvUzNRLn39y9nUfayxR6+SBF5k0trVC\nLeYb62Tl0a0jprl9eZ0GiWMU16XTJYk80ZNe+mKkT2vq7MPWB7B9JJMW073/\n0RHC25Tam3FOwKBRXqJdw48n76t80k65QVxBitBNYthskje8FTCMJSBE2Ed+\nRRfPDg021DYxddPW1tugBXGXXPzkA0dwaNXwahda++ybjHk/XV8+9Zyj4Fdo\nMY04r4SM9jkDBACaYjwQQLQWns7v4nZU1vSk83cOGGgZr12HvH/UNDxsk8aY\nWs27U2C/NpEEoCoB1e7Zx06HCIRi1s7cB/VnHshClxojOvS5DUrtmvoUppSO\nfAEuSvfNftnwRZEMF2bT3yXJIpHQLX0WwFR+JGdpg/s/bpLpqrtIXMPhN2Tg\njClI9oEEquk/SqiUIthdCbk0tYgB5oaSf7lNeq+DyXvrNZR1rb2PyLEGhDTX\nXDEW\r\n=jxx9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.1-alpha.0", "@jest/types": "^26.0.1-alpha.0", "jest-message-util": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.1-alpha.0_1588630563887_0.3057449273751889", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/console", "version": "26.0.1", "license": "MIT", "_id": "@jest/console@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "62b3b2fa8990f3cbffbef695c42ae9ddbc8f4b39", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.0.1.tgz", "fileCount": 14, "integrity": "sha512-9t1KUe/93coV1rBSxMmBAOIK3/HVpwxArCA1CxskKyRiv6o8J70V8C/V3OJminVCTa2M0hQI9AWRd5wxu2dAHw==", "signatures": [{"sig": "MEUCIDIKBdnDfD72l4sTuILdIsSyWJfv2GxCqVTGH1plG5LOAiEAuCG3roFKh62ZNtoiZiMyQIEJ+Z61VihPEaTs7PTIhiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK1CRA9TVsSAnZWagAAM6AQAIb9h9RznENYbYkaa/Zb\nBR6rqPB5g0BnGxhWI4ti0YWAXnFPDPKEoUH+7k/eT2xpX3CAXXs+s5abeL87\nh4VxhfmKPhbk0BSmTnaxFIbQaBhnco9vUR0T1bY2csOH+G7l1PsUNFvFnMdJ\nUykCyOTJjWO0C8MWYe5fmvofYXEXhUvbfWVAV/aL0X1YinOHkYOpM6MCFxgS\n4spJSB5B9sLyasPKUSVUn07dULhuQjgwqWeoG1IkSTKxXQx2OhonBqf6SvKe\nJH3bYWH4PqISbB38YnAujahJ3tMMTCui2RugBgmYTGHofcnUWRwNwsdsAcnu\n8zDtvBbeufUrL/adclzMWoGRRreu7J7Ol8yOvhcH2Zy90z0+brs7n3BlDc8s\nymIGdM0tfnPhSVJ+SYjyoDVjL5PR7P/KtzzyEw5eVcb/HwEHbcrikmGVB9p7\nGaDOlbGwxnP3TZs4at2gaQcuYt1mHpx6JtfrFzHBdLSBMonQERHJ7i5j/JHQ\n3jA2+7LfL1eP2o6o15gn1GR3LgSiE29bG0wGFzBstAK2n8liTI+977SR2Hhk\nydTmujgWWATHVKuzUZO5uFPX87rc9fMeASUw6rEkK/VkheNZ4dp+1fxt10EL\nAqRDIaDoEwf75kuVW7eI0gjnZOruwOxUAfNmneDduRUMihTWXqV/R1LoCLHm\n3YEW\r\n=bPuM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.0.1", "@jest/types": "^26.0.1", "jest-message-util": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.0.1_1588675253108_0.435967896547935", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/console", "version": "26.1.0", "license": "MIT", "_id": "@jest/console@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f67c89e4f4d04dbcf7b052aed5ab9c74f915b954", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.1.0.tgz", "fileCount": 14, "integrity": "sha512-+0lpTHMd/8pJp+Nd4lyip+/Iyf2dZJvcCqrlkeZQoQid+JlThA4M9vxHtheyrQ99jJTMQam+es4BcvZ5W5cC3A==", "signatures": [{"sig": "MEUCIHjdxkVkX/oJNZLob7TVxJhZpB+NaHxRRTiPffmgF/u7AiEAkAolFEuZKztct4EJ0nPNOZLTKVLbv6g4FFoo5RWDL1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyFCRA9TVsSAnZWagAA53cP/1AOgziLEgthdxUaWqTr\nUC5kbCS0a3vJF+/iE8SmAxr1i87KJNAcHZ7hAO+WDkoqCPuGp3o3qwbQwbgH\nzPVzcMi6M2qs64xYGBZMQ6on3GnOL0myf56Fpp0xERQcexBLU8A2GRqQnfJ1\no5OdH4nGh4ta9I4g8yT+kpVALGGVL35lUfCMX6YBHOROXUW2sSLQmkQITe/6\nu2eZHR55znFsS5xi+D6QihaezcmfwyzhD0fiu7dbiC2NYQQ0K59lK7TmPrF7\nVP3hdjStLsutM1jXB99B3zK90EYrxHOgGYtooN7XLaBMagYzIfH+D0IuPYZX\nRo9zTn9mtYpQ6TVgltmDGmSjOrNU57SqdHB0smjtcuwnRl+I5CGdalcvNvDx\nqjzAWyr8+SWtGUKk63HVzJeIvoJ1xkTnBP9lsc176L6nFRl1QlgVN2fm6C/K\nGDAbwc6nz+vRoEMJpHAoOS0FySazrPA2TXP3dYbukqtEjDPtG+OQw4XGumiX\nZP+zK/iS052Mjjvbvxwi9rQgP08UDEB9JfMlbK/iy9d70hTNDhkGE/3Ay90z\nKSpIcVSQbx40rU013Zgpfkdr4cFu3HJ3TcH6YeEwPBsxO036JDYdhpm1qAMr\nPa136NQACooB96IBL3DNl8wJ/KtsH9VptwuXgJ9YvW+/BkEFEgygZ/ozfaEp\n3NtG\r\n=PVdl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.1.0", "@jest/types": "^26.1.0", "jest-message-util": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.1.0_1592925317128_0.9948532872287879", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/console", "version": "26.2.0", "license": "MIT", "_id": "@jest/console@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d18f2659b90930e7ec3925fb7209f1ba2cf463f0", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.2.0.tgz", "fileCount": 14, "integrity": "sha512-mXQfx3nSLwiHm1i7jbu+uvi+vvpVjNGzIQYLCfsat9rapC+MJkS4zBseNrgJE0vU921b3P67bQzhduphjY3Tig==", "signatures": [{"sig": "MEYCIQCgstyoKbKn3xSGDOx7/ANzeWTOHBFSvPa2M+D4w3O12AIhANO8nHdi7PvOFCoG3Yk62rhCDOjk4f2lZ2gR/03CWJ0O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzlCRA9TVsSAnZWagAAyPkQAJlkvIiSg87dTybTNUXp\nzURjqhQws3x77h/xkmWg25KZ9ShuBrJal4NGK/EcuIzlk7HkgE6tplAeR9hz\nOVuk/s44TrYJPtreeALCr9DyrwDOYhPn6U39CICWuqZbiYYihZLxwMhkVzFk\nAkulaOyS53/kvU+SoU8DfwzfzHew3yp+5BVzX6wqNGyYjNrJ8I8QuUOCPvxK\nGCCftkzLFLR9FSn3Cad2YOrU1dmW02Kg1THUrXP2jOPJoLyQqoOXa+s3Wuvj\nK7RN3vrC24cPr0CPnh4B6BHfWRagPwqJ2/pNMhF+TO3zjAWR+xfXZDmDwGtD\ngYGZ09Sbfb9c9Qjvsi6kvsrypK2ZbJvojxd3b8rY0+++bWuBhf/DAXLr6oxN\nO1zqluhjRQOnQSFp+tE/bd3+jYxYbRgDUqY/E8bdoCdvFDYxzUG60vXaC3HQ\nLbGaJdNZNJYKsOX1Xrm/RvgbDdR3bTginhSNIyrxQt9cgLgHcYJDvDsH9nwO\nH2op6mEsASU5K5mYx7CcEXzAHFRMzDu1l4TCnUByHj7CNG+EezCf41zvn0eS\n94wRivwyFkXOOExGX37SXzvA4XWrOM20tFCkBLrgoFuLyQzHzWPOgrl2getB\n2e6WSz8MXKMmHaZACYv4rMc8bYu/8dI9kuQQrXVR50CtAl3NebNT4ZgnUY2S\nHBtk\r\n=PDMd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.2.0", "@jest/types": "^26.2.0", "@types/node": "*", "jest-message-util": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.2.0_1596103909148_0.8340708389528266", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/console", "version": "26.3.0", "license": "MIT", "_id": "@jest/console@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed04063efb280c88ba87388b6f16427c0a85c856", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.3.0.tgz", "fileCount": 14, "integrity": "sha512-/5Pn6sJev0nPUcAdpJHMVIsA8sKizL2ZkcKPE5+dJrCccks7tcM7c9wbgHudBJbxXLoTbqsHkG1Dofoem4F09w==", "signatures": [{"sig": "MEQCICUfaiv2Xfn2Tuy+ffZF8FktkLDRqkFGUzntQLYt8VURAiBG2UkRd+CSmYJgaYF7giooUwv6Ej7KogPTlOpoj3b2zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTArCRA9TVsSAnZWagAAnfkP/3e9nj4VEtZ+1S2OmxZU\nnT5o6ZDaPDLQ8AGBUSPHW+n076l9/1eDD5gB/rFra3N23fYXP7LeDtHx0QPW\n61uucqnY1Un/W33VW4hsvr4ZzjAHlYNGIT9g7+yddwQ0Z2D9h2BNnY8yklo2\nsOtxUsVNGbaV6aD4zhNnW99BSBcEPe6jLDnp4JWLBGJQ78sl+f9rb7A09l0R\ni8MLilVh7yLOsL5elcQacI+f1ZeseYHeBLk7pZXSOLug4LFbuhUA9cHjBI8u\nuJnYFMpxhaaXt+PCXsJMqbASR5nR+9URtm5VGAxAyCW2F6auAgK/BDJsHoas\n1F+a9exIq7r6Cd34NEK7XEnJT7WOMZR4VN5Fnc8YmZbIM+rP1Cl8NuzN54uU\n7SkGoT86Mfmk3V9ehUa8HtPmgK8dToL4NbbKmUoGip3Y07pSj2H4GQpfyiDQ\njhQaNHuRx3Yn8DVp32heem4ZigEp6IGYSZ6rr12t/e/Vm+2Y5HJeCwgNUdlL\nfR+9Pl/LM6osA3i45JCUkGQXZ5Sf04dbviHtefoE4tT0RcwC5cXb/zMBwdWp\nvHGlxueENoZl9vBrhHxcP1tMCdtolg2NiGZ9AAsQZZQsq1Xgnzp+yoOq2Y7h\nRVKqNbqr1qCoU/3Q+RrAXoDWXMUqOpwGvtIy7hLB2wKmzWJABTASOcj9frDb\nJW8L\r\n=BGzH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.3.0", "@jest/types": "^26.3.0", "@types/node": "*", "jest-message-util": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.3.0_1597059114740_0.043238743641055954", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/console", "version": "26.5.0", "license": "MIT", "_id": "@jest/console@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "89a1c5ae8329907fda842ebc5b475d5c9f935766", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.5.0.tgz", "fileCount": 14, "integrity": "sha512-oh59scth4yf8XUgMJb8ruY7BHm0X5JZDNgGGsVnlOt2XQuq9s2NMllIrN4n70Yds+++bjrTGZ9EoOKraaPKPlg==", "signatures": [{"sig": "MEUCIQCcz6lt0H84KEjJy/0iFJVrmUvK179arceBof9ok2BG5AIgD5EhsD2V6Ainhkfe+n6hCbVk+fBYHfmTnxtWFonpjXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc1CRA9TVsSAnZWagAAxmsP/jWBIPJbfjfiabf5OwdF\nZJB22lzMAzD3fS71vDD654nd2xPYnTncCPS0RU5I3cfA0lURVrCvsT55SZRc\nnxuemXSjrHeTiJMl21vmZ0Th4DtKHeFd7BjzUxdTpM7BimXMo5LSZA+fM2aO\ngMfdRpEfxE1UyUuncNqCyarxOUOt0Sohr+mTiFg63CO/hjqTKae4b0uhlgk8\n8Fdyrqrv4riYJPRGZ5lT/BdA43vIrHjPtHOGTe0OPkAaSTxGOE7UjdQJRj+B\ndUGlSnQHTbpC/wiYTQz/kwqffQ8H1aRy6QnTVoYcAOaypHQX+d1JiJoInh7N\nbhUVvM5N+8aVhVvDOz9nK5aIP0qBHWvuum6lx1f0NHfcq5bHMv0kYabL5MQG\niGfS2FcaWuwDpom8Aa5FZGidyuXsuaB4uN/eJVqpGVipaKnhNGZpwKNHTUOh\nSdg2SP1KKJyk08Kz9oIx0jVY6Vq97b0O/Rc5604TnluHJ3ujbRNTNyrU1xaj\n6tap5iV2hSitkcvRGZzgOmqf0FyDCErKoL2kr0I0w/7qdbD52oV2VJbtNTfW\n2M2lFxvbwOdwJTUt0oD8Gb7EcFFP4u+ncj/zz6E3vKkZ/aWZl7mqoNWFfnGa\nKshjfete4r+VyWMiu/A+pR93t6XwR7XCLdu5fJcwG0kctiN83fKlWutOXhKg\nj46G\r\n=/fR8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.5.0", "@jest/types": "^26.5.0", "@types/node": "*", "jest-message-util": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.5.0_1601890101248_0.4432996552474353", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/console", "version": "26.5.2", "license": "MIT", "_id": "@jest/console@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "94fc4865b1abed7c352b5e21e6c57be4b95604a6", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.5.2.tgz", "fileCount": 14, "integrity": "sha512-lJELzKINpF1v74DXHbCRIkQ/+nUV1M+ntj+X1J8LxCgpmJZjfLmhFejiMSbjjD66fayxl5Z06tbs3HMyuik6rw==", "signatures": [{"sig": "MEUCIQCSdZvpmm9wqmOOQHv30viRWrwLryEntyb+RwvTofWhewIgNmDwUTy16FRamlGvhFcZ6WDkeS4u941MPMojfLCM3hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyCCRA9TVsSAnZWagAAqFwP/14pzTtq3MtPSXD/HWpj\neHddbsxNCtZlr14isuUh8EIQS7LUa1U861uG+9O46bkQIhekiC2JlGebMmeu\nUI7IvvfLjXr0Fk42WlAe8s+03sH8yKPNcB4VhUGyoAexiBxJVRN7GWQ/ZTaI\nbdaCokl8WDhpVy7xDZ91bJ7Rq10OUd6JhHtFjCS8BfZU5DWJ8VcY1krc2rTV\nKdikWsMepNg62Xhb8qoquVUxDKlqwk13Rvoar4237IutPllhkXIRvuRDX0bO\nMbgkzZKuQoMj7eF3v2A/IlYD9Qqz0D4d27z26Qt8IHo6drAITewcC2ux1F1e\nAjXI7S94/XhNGh+GSt/4LP6hQV5xBuImK2Eo75L/K06EUCCIvCo+gYnW19EI\ncqG6r09wJCM7rxSFwY6NembiaaPbdS1ZxEt341YOkUN1D1qbkonfYw5DIc75\nhje1saP0rvOKluM8v8OelQlh0y1hGpu/VTSUxhZK1UTaXK1K/wSlDyRbufc6\nc0y8T9TOxKZMsQZfzKDTMs9zbW42Lblz/dUantOz7KJZroH/u6Y/yEkfU23/\nPzJPUyDdn78HFOS70bGKznQSMud6s4PjrRAK5ricaxJAGedGblmyw6Y0axFB\nqhxeyYyLsvTAdu99gbbgIfwCLklT9PTX4bt4ZhUlMpgBtZm5u5L9jSO5+Z9h\nnEFC\r\n=gong\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.5.2", "@jest/types": "^26.5.2", "@types/node": "*", "jest-message-util": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.5.2_1601981570115_0.999718667914973", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/console", "version": "26.6.0", "license": "MIT", "_id": "@jest/console@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd4a4733df3c50260aefb227414296aee96e682f", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.6.0.tgz", "fileCount": 14, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>ZWAEYMWmWnc/QvxLDvFmGRPvmHeulhS7FUUAlUGR5vS/SqMfArsGaYmIFEThSotCMnEihwx1h62I1eg5lg==", "signatures": [{"sig": "MEUCIEOJ5VKuEcwhgS++8ekgCSdzg+8YnZTpVwuYH1pTUY1OAiEAp8k5rFStmv1vfrU/j8L3oZLhXBU4tKRU5HYg4GtWBS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX9yCRA9TVsSAnZWagAAKfsP/1YHo7jFl+cldAd+CO2E\njhs8lBe62j1kwVmmeFxZaMUvkcPkzFOLNT2XhDWYOKBiuRd5UzVKPFLRXM8h\nkpVX54XzkbhbWMLo259G7rpP6o+9IVhJxR/eI4AXRZavcRqV2uzs1N6Yxntg\n7sYsvGCA5bg0PCHIcvx7wLQ7HO2qS7OBSPcj2OmKsacsXNuUmkOGFnzXSC2E\n4WblIHmuTni0ZqiTzqTBGS3NFLlV76V4nhxxWw87N8PtaRwqhKvVcZ4+2Jht\nVWL3fvEeEyDgbiOO1NoSAYodLu7SmS78yETE+Lb9FCyJxamekpnTEZEAxd2M\nO3bganu+HH7TyoUa4l/12Ml6+7AKHPmyvrD1ZAwrGS76wIWmw7KGfdnILdmC\neOs/ur62ml5N+OYNZYQtphXWIXWv0bVhcJeDHeRI3re0RcNyuJQJmQpdC5z7\nZQVGNkpBBWGFmu7+GsEp3g3ivHINskA7tTj+lYgG+0c2rpmvkWUrkgOw+WsO\ndY813poVwCgrq1xsMv075OS7DjKdfWdA8NYJgY9BsXkzWtur/jS3UP2J8En2\nQeU+A8qtgXmWkd9bvPZKvUH05MfcsKhiA2IcYaJVWAsxr9plSsuE/wZC8fLi\nBOJV+gMWi4je49P6Q7GuA3J6A4apPeIFBOIdtzjQIzLHuH+mu3ujV7knYegD\n0LDs\r\n=Yog1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.6.0", "@jest/types": "^26.6.0", "@types/node": "*", "jest-message-util": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.6.0_1603108722361_0.4191011081838465", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/console", "version": "26.6.1", "license": "MIT", "_id": "@jest/console@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6a19eaac4aa8687b4db9130495817c65aec3d34e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.6.1.tgz", "fileCount": 14, "integrity": "sha512-cjqcXepwC5M+VeIhwT6Xpi/tT4AiNzlIx8SMJ9IihduHnsSrnWNvTBfKIpmqOOCNOPqtbBx6w2JqfoLOJguo8g==", "signatures": [{"sig": "MEYCIQCBR9ncoowlZP+3XKjMD9cNhKa4RoIa6ANMn8VteG/USQIhAMkG3uTbCw+7Hx3iMn7EWQWomoU4MQ8EW0UJfqILiyXy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0PCRA9TVsSAnZWagAAnIkP/1J27DpQnCiGNgYtn6h8\n1zlVqs90KOpsT2onODmmAFr4r0QY8wUe7kYp6EmxD2fmxWQYfxe+k8weCGue\ncX+J6x0sqVOpLX8aY7BbQ5IE8uoiUu0QnhtHpB/dwfifQ9N34K5UFgDpumTI\nHNAMl9cB1hrW2rBy8MmNiLIxLj6jgQF3PZl6S55tROB3eYrrdMtwihjDFj+A\nSQ3SLIGXbM8AMXRF3XMiRclJDgUbZBngdHj4uytx/WUsBQXTGEbs/C9Ln07c\n/XytG1WJ93EktiYal0vLNFlHeIg9flbKrp2f6D487JxSNyjhBeR84FFctmx7\naQeLO4CRulqS1Xu7iRAV+kp4qpSiqnLyCX+4BF/n1WzHqimx/jf9CKYaCH36\nz/oWEcLv+DLiv/P6AdxbWJtl6nVNQpfMA4XZ6xiyvM1YQfW6Pn0Tvdu5HBeT\n4RvKJGIRqgmPHxuqnQqZby6tOtUiojbd1ATcnyB2J2IRaoN4udIfjL75zf82\n/VYsHusQ2IzN6/th6ACrCx5gAU8SdLo6n9mZDbqK7oKJcoDMmEBaSPbY5p0y\nXcn1WSarZ1plWtJEJpMdHaVaHYLGwJHwUKrkiX9+WVvrINhs31u/TUnm7IrT\nvNNNaL3YNVPucU4JYD1+zYhlsTxCQBoXxCZ2CDEXDALYuU3gdcKqO5N3fbLs\neJXl\r\n=Byif\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.6.1", "@jest/types": "^26.6.1", "@types/node": "*", "jest-message-util": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.6.1_1603443982954_0.16925714972559902", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/console", "version": "26.6.2", "license": "MIT", "_id": "@jest/console@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e04bc464014358b03ab4937805ee36a0aeb98f2", "tarball": "https://registry.npmjs.org/@jest/console/-/console-26.6.2.tgz", "fileCount": 14, "integrity": "sha512-IY1R2i2aLsLr7Id3S6p2BA82GNWryt4oSvEXLAKc+L2zdi89dSkE8xC1C+0kpATG4JhBJREnQOH7/zmccM2B0g==", "signatures": [{"sig": "MEQCIHrC1+cA50JaHB0FngxTXwm4cnLDNCC0V+wBZWMOtZd6AiAuc5IJhY4TNOxJdZUToejUSju9ZAa0tcMC1spbA2ef2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADgCRA9TVsSAnZWagAAq2QQAIdQi1l81de/FqEOpsfp\nUG5bmZRCPDQpZFWlFwD8h3vAVTI6tYTwfsD2TtfCMuXFUwd1rft/UMKAz/Lc\n1db/dsVgueM/5ZhoVHUw4z/nE+5lAG96BQ5mRCpeBCtEjDwFklnakyXPiNHo\nLxcsORoZNyCux837dlordT8ODEHvJPDU/zLyQ8PvVA56mFT3M50s2SLt0BH/\n9ek4ppDpu1uts3qWFhoMKDvU1JKMjHUDgBA7R7bbrQ51WBz8b2XG0+hx9Po6\nxOHD9t+/4Tsiera3j6eQF3mWBWV2CfNnnc+p7dHUZW7zAe7k+/AwxIs0XZac\nV7cFtijQhyBta5s3oj4/2oR8bCf8r3OltNZoaJ7+L8vOxR1fLJf7g6vsyKi2\n68PbbbxrEgUT2e7JextyXNQlBQDvevJ8R7s6Vz1zSiNWXPYxZQzGXwjGY7Pn\nUZxzNnch+j9oVIEJAeliSmLF9n11erqjiwm9crcm6ufbesRmTWOWbFaFWJJ0\nyZ+w8d+Sg5h7qvru0vQ9raUzVe6P3Ec/2hQYFkazlDbcGGqw2bE4gqiISPCe\nMsf93T1qqRlsIvyDGiFKDLQHekDu4ZLWk1Fhc/SRKhMS7DVC+R9Fg56TdZ7K\nLNsBB1QrDbUXsG+4Bz53aioGaV/wp2XnbKEbssZxe4lTXbJuaVMKqgulHpEx\n428N\r\n=9ArF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-message-util": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^26.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_26.6.2_1604321504272_0.3084868179993199", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/console", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/console@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "192ef4e3ba642dc27bc59c8e21fe288e79d1e6bb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.0.tgz", "fileCount": 14, "integrity": "sha512-mcuMGE4vqxlCU8ENp7lwhj+sX98rbL/DnfDI4RETDck8MA415JN/a8rWSFunt+zOok13GCsuWyGl4seHUafZEw==", "signatures": [{"sig": "MEUCIE0EQdnFm/niSr5IjohkRr6E8zA4ZFYuIejHySZIs5duAiEAh0sYqgF8TLaqfypC2gCINe9vhnLsOG+5h9zfCgLWQ+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KMCRA9TVsSAnZWagAA6nQP/j/BZDN/zQBC7uRshdUv\nR47LMKSATAdAv88vwpMbz+NsZJ6zC2tUgnNXXFxfqW1nXvhWF7Yvb+sWoSCH\nJ4HJPjeErSSdUq8Nesum+StiQ6cHKu+AkfJhYobx84sqimyzwi8hoiBWxXqp\nga1TS4CLwM0848E3YOijgVMyHgL2yLnCYIvzObn//JJOHoDriuxuQziCneeF\nh04gz9IGlLGgrIopNaMeu6PRH3TIopTPazYdiAongsD2VnbUkiew52aiG/Y7\nu2B16nuZe4c1uCbOF8RcifD99yH3J1xjNg8RNlrW1ZbfzpBo+QLJb1AUVQaH\nOFQmIDlQIzyUVhrKfN3tZcEDujPfjm1/gIr7h0K+RBRjRV7ytr7OAQHh+1Iv\ngE3K7Apb3KHioVmLC/w9zqeI7ovS/CipWpmMTvjmBoCqd+MsbvJAkUdw/wAQ\n1wraOuopRZezv0CnymZ4mZRv5/wG4WMfopLpXbypHW78LqCafSZ/Wyg4UIEX\npSyTLDzEyl90HjgNP2T5lCRGFyt/uJTwKMU7GLGsbDy+1fbgV1kdBEoCiBrG\nZ+s7zjZq/5vdRs0P0F4eFV7uhNTZ7Pw34xuYOmH7ytQqIZJ9RznH2UlBKlhU\nDhNNuQXaFeRKXkwcloeqYDMtkq7xLuVnR4Bo5L5A0avWKwZDcpeD6+LpEBvw\ny6fj\r\n=e6w2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.0", "@jest/types": "^27.0.0-next.0", "@types/node": "*", "jest-message-util": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.0_1607189132321_0.37942600947496774", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/console", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/console@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "10a6aecbb0d19fb69b6028412bdd41c1692b8902", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.1.tgz", "fileCount": 14, "integrity": "sha512-N2EZfiBPItMZXtRd06Vjqg6sYdhmxbvavFF83ApLC/w2zyx1oiubEC3BM9aUXaTGKKbpWdSr66zGQL7T8PodJg==", "signatures": [{"sig": "MEQCIFf4AE9oWoP6gHFVm9WP+haRiUwqHuQVZHjVch8Ff8YRAiBGcM2uu3l7pMxLtHuspk4dM5+aLkk3ZwKG39og5fimyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziN2CRA9TVsSAnZWagAAC9UP/A5RrEPPpw0RpY4ZF3hp\n1tvDCQPwwl3NLePxtyWmuFxIprJdryOUPeHE0zR/54Ou0hM9kY9T1MP+TyHa\n8TWYMRpEdwA9IT5rofMwc19zGXYoQY0IJTYgvztlIdGaaUwUJq7kMtSpsr+R\nIBsUQGxWakrLCHf2CAHYdSwEAoFGWQKWchVrbWZFc5vX+AHiYsACSDIBtcqQ\nz90G3VTjzfKp3Mc48d/zeH0ziS4S5K9d3wwZeoXbW0abkjRbSy1Bt/YT896R\nSMPSaN9ELwWkAecXzls4nIfCSXXSQXw3uIpHBwWhWcflgEhgJjUGNmubLmUg\nFXd2EUX6lHY+ChYXCebE5j/nIHJob9WLMSgh8kqG/ugkW1pa23vqdSFFE/zs\n554G7MInkh1q5n55YUfElNQR17TjwUFJN8BhA5xHGeWh4NymUXso6jFbZ8zY\nAGurwWQBs7DVxzkFz5AW4vavewgIrOUPw4A9DU5aR6kxpyyNF16JDnNX2vdU\ntatev9KcpVF2nmyD3jH5tVC0If4j+1N1E3KH9oHFPPH4Vq8uopz/KEJWJ9CT\n5yxyMYwWD+Vtb/NJdE25DdmBJwP7GrKZ76Iyk9Dz9UlcH+VHdyUGuLUZYmth\njQyx4BwsRx1KfGbipXclomXO5rNlFLHG9V9iKqL3SoIG6sA/6EZ+zcHclxH+\nsEFE\r\n=LvAW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.1", "@jest/types": "^27.0.0-next.1", "@types/node": "*", "jest-message-util": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.1_1607345013646_0.8196989111570727", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/console", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/console@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "25b293fc1a4269d475c0bd5b50542e06d4450655", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.3.tgz", "fileCount": 14, "integrity": "sha512-WqmVZKsdP9a7IDpsbgVO74iONPfqW9peIsXyNjPrbBMalRcCxWendLvIrwJRU4Y7m2TSou/Wxw7jXC1mbelMBA==", "signatures": [{"sig": "MEQCIDsI75zGLQ95zA5qScu3EIEg4QBbg81zZno/R08ZLHwjAiASkzajIm0+D4pK37T7eceLFZRcblBHGF1pA+Gnm2fAGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuW9CRA9TVsSAnZWagAAkDcP/0bVPym7XHjgkmGgBFGX\nkdZHKe+DrMrF2ZDHPtb0k8WTeRo0AZym2eQpxd0WSU6a5eb960Cmx58U/qZh\ntRSla2+9O8gK0KdUTV7wtQAKnchUAAj9zAjHoXa5OovGb8dqudcj9IXpRy9j\nzLUzrMnX7TugXeDUwQcrvqljeM8tAcON5k2gtsPEnx7FxFylsGQd3HXRN0no\nm/zTYqzYDSTmJxUY8zM+qFYKYf37iR4sFPnlAO9QlY8iKHjJdGWwotuT0oeI\nWXFQVrnWcH26FsAtqOygzteJu+hXnx52i6X2QY3i1FN8gpuxhEKm7NG1tmW9\nx4R5Qs7+uriZMWNZaquQp8YCRBGx8b6nQ1yyenZGLe5SRKFB7BXhGTz5AhoC\nWxS0QbgnCh2fYD6ShmjPfYkDsbF7VozGZzFH5/6FIsM3rZbS7ejDzvAR5XVw\ndTkj6f63GjWb9Q6bqG5CayfcK6CzSNjJeUyLgj9/0eiLWKNImvfNESuQyWst\n7Ijg250UOzeuc1Hhu+mTYA/9sYVJGieuSkYYvjKHZx/fYQg2h/B73dF3s9Bn\nYrClwfrHiyLc9o15MZ+CmP5UmLz4th3iKdaao7ij6GH5c8lIWbspi6FEIQZU\nPxa2pFfD0RS1U3Ir0iqeFOa5m5asqC6dTob7Le2S7y5jJ4Vrx2mxa2wQLStg\nRplu\r\n=g5aT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.3_1613686204891_0.1893270243505878", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/console", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/console@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08516054a42eff2cd2133fa9b3e4fa72a419618e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.5.tgz", "fileCount": 14, "integrity": "sha512-jg1y96FlIZLI3H4xW7OSTUEb+TWQRLBZRE19gRzuw+kkxOqUL/I0nkWUIQs1eGqL41/P260B+IFT22d/5Si3hg==", "signatures": [{"sig": "MEQCIH/PuIRlAUw3FHCMpTIajcFkhqC85iDsxygXZdEOoohzAiAw/rkyKr/e1y3cRZ6xLT/5aD8YobKlGjig55Gf7uMwOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1siCRA9TVsSAnZWagAAIV0P/1WTeZbnyFblagUKaLSU\ne2cI/kmevbD7FJZ4V42j19maq2/+tJiq9PctR3oeRUVify8U4xUN5piW4sKE\nl2Wg+10rAWv169/+xQp2xbCqI2kDUxdSSnsOlbFnKscqv+lwK3M/Wc9DbIJD\nxM5lau8QJwpnJr4mxgyGxkc0FkhJi1MifZU6TMaNWKUujeK1ve6lkY5a2zPT\nK0YGVJpaMbC9adzWvaozwnaShgMWiSQo3clo4L1dDncNWX7iZns4xSEyHKms\ni+Frd8fqDvloSoGnsbrJbsUd6loKcKvW94pGHIIfeGPA8D9ADlv/bKAgCAuj\nb1mzCO5JdEjjxRbVLeTFgtx6OU/3a2JrjJSXXdXm9UC+VClMAw1Wg7amZ1Pi\nIPTewa0S6F6IFtdT2ufkplecpDBDjYIrJHPsZV2fNrqgPM0pyGhempXfyIhD\nS7s1PVi5IyVLBz2J4GklCCQTYOmb8BX2WxuU+NRX5liiexQHTBIllB+0zfzu\nJYFZ14C1igpB59xtIZJJ9C/GTychClxkzfYZY9g4Gp1+hYmx5skvDS0yBQvc\npf4VNv8D3YJeLvYirWlhUOFkR/Na3eiwOIeP88X1LhfS2jnf6okYa+q8QrVj\nw31mVxRmTYqxi3K6N4p0YZrm9vAymfAIGpkAq5FtxymUF25+ZmM2T6MVi1j3\nHzGa\r\n=Xp7h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.5"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.5_1615813409925_0.9619277767978762", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/console", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/console@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1ddb1171b537d6798930307e25e9ab1b3af27388", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.6.tgz", "fileCount": 14, "integrity": "sha512-wmS52IJLQHbfyOiSxB/IaoB3ReeOIsRhg2TJyugBE4Q0+Gq8NsIe2HYSDce7252Lta2fM7Rsa48Iok9imX8hSQ==", "signatures": [{"sig": "MEYCIQDXxchSoKg/CO5adSEex3SKX5gmoKY+0SYubDH6qBk5HgIhAPPx/3qB+6PoA5fY9zdSHPQB1AodDnValIvbkqILEGux", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcaCRA9TVsSAnZWagAAUCwQAJwolLTewMoL4kztRsLF\nwXJw2ixCJlDiU86gbxGLRlm5J39lxg/doh1VbVXXvpNEqUlXoXSzHJF1xTcW\nR+MJSRehEqxJfUv53h2WURofLQ3BPIzeaDwsrUOhSd+AQCtjiMz5HTnx5jOq\nPDKiZspshYxqZK/fcSnyd33Oc87ln5uCNizY1OeNrnrNXsjA811uqND8nqqr\noS656ajlY1y33klF8vyfUfQnsBSl8R3c+PvQaKzfopFNZJQw41BFJqmIZ3Xf\nzcKN2Jbscm3LA1VeDZrE/E7ymDJsb5G8ZorefTtk6XX87JV2DkO6LFtVmAo0\nZxW+3OSW+octX6wewR1DqIFAx1JYu4pr3kFq/L8LnNEcnnGLPzxG3IO4oLj4\nSI7ga64D+XAsZa7gy3Xg1h46FVIg+OU5wap0teuE4K+W67uxbY0JQ+t879wS\n1q9sr8cTneKlTx40/K92D2WYAs2TPLdnfL7nlraC7dbqcsCY0lc9jMbaOf7P\nVaglXZ/1cGVVop/B6JN8viidG5CBd+JwyxDjrLTkpd/Lp7Vdx7I2ilolhjRi\n0hgED5UGnyL9w+C4+GS/CxEfkAkmNjJfQHOr24VrPhO9Z5Ae31DBkCX1lRGX\nnj65kU5IjRFx2307VZtfd98NSfa5r92eJ4t5UA8imGW+kXcdhGxlPiwS1YGZ\nGsoA\r\n=tphj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.6", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "jest-message-util": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.6_1616701210348_0.7901139200474174", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/console", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/console@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aea7c5faf642c4a0bd917622279a5cb14fd4c3e1", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.7.tgz", "fileCount": 14, "integrity": "sha512-CqLIn2weCavHQwN+plAF2WyLfhaCKqpE1BfJwRLpyNREfjg91RNZHdna6U6+Gkfe3vRtgaKNFZkUDc5lFxXymQ==", "signatures": [{"sig": "MEQCIHqg1L6W8mQXSrmM+GuuyzoT1t113C/Kq3+AWxtd+p7gAiBScX+U1/JcjSM75kbRL5qc9m8+dEE9C+ad4AnTox2u0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCTCRA9TVsSAnZWagAArsIP/RunmF1UGMwgGDDlAVXY\n5qLkP7ZcOkKl4h5e7crK+6iyifvNy1FpIejEgEinLy55jKVqi6y2C2Pfvw2N\nO0ctSwGCiOpb8hvFrrBT8u2ozdkUMT3ne5eXJo4B7Uqq95Io20ZesBapt1UN\nKx75wmCiJffkmURVQsw4WYCr/6UY3TudJIs9SK4gOP5Qfrk02Bsx98aNmTcJ\nNcMIoZNfLemOp1emd9e7ENtIVVPtTn1ycEiiiUa2LgzmS0PszNAU9hs3yNbl\nBC0NpsyQ3yfFm2gYCPzFmIwbEV0Gdb65nEPrxzte52O8rL9ZmLvyG696I1Dk\n7S7Jsyy2rOhNkpRJIsJYGhjHfmcj7EAYcdQIVJDFhcrD4q39f2Le1Byhx1Y4\nb8joYeDSYuhx26EyY+2eGpEV3uPsGtOG2Hz7gG+IL290CMSuo5GFZe6oeQtP\n9gZ27JtOO/VV5rTN8q2fotjlqzBvGi41+hyhm0G3n8AmhNYiwzIWItlVOrnI\npUnPZDonefp/fa6LgfE/lbCkFYvnWxnoxbk5AoyGFuOWCAsgugWb74vL9i7i\ng0BNmy8O2tyFqdpA+qvf4LAduw3DiJ2S+x3yOL/dvgvXcVvA+V51jy69Fuzf\ncVUToJZIKQ6f94j7tvUN1tw0Bykis5gZG4RyF46P0MvjkPN0EyuRmqY8uPK2\ntfdX\r\n=XL82\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.7", "@jest/types": "^27.0.0-next.7", "@types/node": "*", "jest-message-util": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.7"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.7_1617371283497_0.4386556853421293", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/console", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/console@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac43a30fe7ad588b42b979a5be0112bc5cec7fc3", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.8.tgz", "fileCount": 14, "integrity": "sha512-ADF51Qyd98moc7ZWHPAut/GqEzzD/1MCqoX0OS0C/VQ+L649zo7ekMTIacPkkbM4d/T8K/XVS362+NUC1sgTdw==", "signatures": [{"sig": "MEQCIEQ6+QyPaUgXz5Yq27ES8MA/SvVFXmbjzghEreYVGMYWAiAQdiNDYiPiQj70qEoDw8IWslODV0nQvpqc/KiCgC/MTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzcCRA9TVsSAnZWagAA/DoQAJE5RWqurYBTZCYV7BZ4\nOOorIDhCPEbv8A7hJoO+GSErhb4F7pQ86EwRXEJdpBisOu7Wb+iTFX6TBnQI\nGakqNv+W7n+Ap2Gfk/3RxsIckSgsqNBSSIz43+VobwFsICMqcVZcEWp/d2Is\nraodYHctpmcR9r1wU/PZeSzPDc7qxOK54EfxvzBY0DUrnt4RQeAXKTF6zkKw\nWQCg0CTN/fucn0+EtFgHMIugSHZwMoZp7JeMXoDprBnd2Wj5UGvjnm75OJol\nD1XMxWbKaeCGLaJII+bRvZH0jtfVH44ki3Jm+rnlsi88baMHVNu5+lab6XX7\nzJv2talDRid+9QtHm7KSOcMz1fG3qd1sIqBMIIa8E8eMAM+9POdhXdR9q77t\n2p4bBHzv1FINdmiiPfpe8hrkwv9oEa932KpwLrueRDfnVfW7ec+3smxCpuot\nRkcCAisLAWpurL9PNCWqVLk5KTIL3T0AXQR7wiQOJMEUfiy2FuncgPim5X8L\nuSiQ7t5oujOUvhl3uRHCJHKe/F8ALbFyh1zD7sPDgVI4wjfXS9A3bHHrL98z\nXNjos5UHlPU/pzqJjoVMr63se0RVBPIbwy7ZtYsboXHIH6s4KComHazCJlmj\nalRX0iV32htKByr/fs98Csonzs7qv6lsTshzevIdzw+kea6+c+9/KYqVrEA+\neUsy\r\n=IvvD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "jest-message-util": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.8"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.8_1618267356539_0.09961788083847645", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/console", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/console@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5cb651a1c1eb3b345fc90360553c5b9a9c9364e5", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.9.tgz", "fileCount": 14, "integrity": "sha512-7o8IKI2mnAwrIvaVLQZ+JXS+sxaadYBrTwOm2XBBl46fCH54sy/U4q84UvjTOX5KMvmpGIhz1iDSUpLSLbk6Qw==", "signatures": [{"sig": "MEUCIEqjWcIUvJnZQzaA6zO9dREDRK3Z0kjLY+RHkIBLFRncAiEA2W/L5ZNM9rEUCnr57eoY+WUHO52MW8u1GY9MMeOHcGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjMCRA9TVsSAnZWagAAUo4P/A0ketNWpEIySHNUOIry\nUNRydgSBYFEalHYXlEhjJIfLuEvd9iaBnAZQbjmAeY2/d7rXVxFYCEQB05fw\nX3KMppzCdh/e09mwixrPDSKeOzR0wKcEQzPCJkHvStE5hUFYk0ylI3r+nAmR\nOKYp9+X7rOMJG2Kbgdb/H+OMP+Ezv967UMYQYMfnlangpqh0UScxVTjaLPIK\nNNGTHMeeHZeWt5H4oeMzdXpeTSA+uEeYBCclDCy8woxaB0u35f01AaGigjcR\nDzdpU+cu/6ACBUUhPq5auNy2URojpKER3eBybDrrpE+GZaW84MBlBIPEFGhB\n5GS+VP1CwBK0ePBPygMZmjge+jEwdHAGK65lUh02VahyaIuzOK/qz+axww3p\nnxuCkTDQyekqJrL4DqvnWKJ3TnbI8WmBdDpN7FjOP3mdJL384ES6FSmLEd9N\nJ1mzdeisw9qFJxHeSnf70jfJq11+B2djwXt/r5IGkrAolrPBRxyoVU9NVSH7\nuIy4nnUhtWho7i7lONEcT2sDLj9QWrvb+jSM6Eoq/Tvc+kiMnUiyBohzu5wj\nSvoTyXQyJKLnMNODD5ARz/Buaju/1yj5dtdVpJn47r3CpGZD+K10BoK70HAw\nIaAZFAj7zfvMnmHDRt7wfQaAdh1RLse4ZD3EgGRyhyEKqwfdLspXCHuCbf/W\nSQpG\r\n=k90X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.9", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "jest-message-util": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.9"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.9_1620109516402_0.39020589220276514", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/console", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/console@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e8d8d7314f6f91196282fa667e7e5963abb3370", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.10.tgz", "fileCount": 14, "integrity": "sha512-W7KBPvl9Px5yY7dFj8Yo6kgZrzy2B018AyvV+tis2siFXRB66x0jNtu344JSjI6nKCqifcHXETpJUiSv0nd7tw==", "signatures": [{"sig": "MEUCIQDifOkPNF99FDRYGn7P1nJyPOPvEreJln7D1J80aOUZkQIgOStjAwL89g9nBEZnZu9dWIdh1PtBG/o8zs4makaohKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4QCRA9TVsSAnZWagAAv0sP/3Kr2GA4N8OmER83eobz\nQb2lnRGk3k5tDPi/83nNJH+u6ehQiV9365vwfOndL9DT1FObu+0aXElRWT9l\naYrO48K0Ujne9bb4p+MRkNnnAYgtQjobMg2V0d1tQbttRlI9q7Olgmtapixz\nHr19xfm1Qyi8jEPlHLkFIUOgpJNZcF33WWGVuaFd9wNBXQe2po6vuGHNenss\nFA/B4Xk+kd8Co/TYNAqqzswYw8g36wENwoAS1hife4wUaC4tosW2IgD5deWt\nZjYhJq2Cf6zld2ToRAoZzDGiVQMc+xczxfRanDWUG09ozMzW988nxNF5zatt\n46flSGbKe0maYc8MjyyO6YFm2/Q6LB+/R+zjCCEfk51Xwkmgx+EKtQSBeJiv\npoFTcdHwf1SKhrR4UfIlkVK5OeK250BSeG7Ty+ETls9AdPRb7Tng3xEynr0F\n7Gfs1F0qCMP3861qKn49KDb3aGcY4efrgae5xdH9P6nP2rMivA5ZOQIetsuw\nbs937ja8vDnrHHD91IWmRifGC7gRXPq90jhCDcQZyJv/QjNq3/AFQ/qBHYsE\nkiY1PQnBxxu0mdKl8Z1X1kDT+o2tXFobHp6g4WWGHSEXCTsPrLa2MuhX2u+N\nd25Zk7eIV5X0i3Tg0tRdpO4VLpjO3RZfc1uWoT9IhKuV2gI2ypzKdoCBMs0Y\nvWSf\r\n=bOnH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.10"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.10_1621519887805_0.6078492406823424", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/console", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/console@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dd3dc397242bab7efabbe1f3e77012b0cd98e38b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0-next.11.tgz", "fileCount": 14, "integrity": "sha512-NexRzR3t/ykr/gUkus2HNwGmPDL8keEj07sZFZNmNsh+MWsiwvZWSjtaorkLai+wYD3EHfwirdjGzHMXSUJXww==", "signatures": [{"sig": "MEUCIFkQ/1U0GwKpE2frWhhfz5cNzmC4GiqUFwthdNJrlkSIAiEA6ci4gHZcpdFq4BjmId1LmXPqHtFV9G+UxdI22R2F8LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKiCRA9TVsSAnZWagAAuG4P/35GCSLmzlX2WKIXLfUG\nTce+KlDM6Lgl99UpyaY0kIxEE+EjF933KtTYmvfmAod967md3B9UaCiLeLo0\nYG75mBD73DxulYepemOcABbubP6NmQNpxlhyivBT19IEEq0iNNNrTf1VXTu3\nbyBcXYR5madA+TKkwojb/5ItaU3+wTvT8EqgFDCbIugMTHVNH8gxuDljZsYx\nDGVAu2qr+3lgXr7EhoOR8AIqnE27Ik8l7PE2WMtS74wz4drBPLuZF3AoSa0o\nnyxaatSlO7TnS+b+xhFR57ivt2rEv/Q+FBiCD2ZRxEv2xDn6q92mO3TZ3cqs\nVQoY85XLhVNUVWejtyz+myLAOaw/XNERrPW42jE6WVkTZlpcIznm/jQUkxyh\njUfbxKAlZB+5g0KQ0uXd8jxvC6+cVKMnFdHVhvAm5yFeXx9+zKGysTP0R+4K\nrTiBDQ/VT39kj+L57oF2fC3Rm4FS5jrqg7Lfs72mAkmPT3o5lXd5Nz2aF0qs\niSUrN4xARwlj/VzvxSo9TvUFbNLivjk9dP25HdNACah31AotB7xPNwrncyfK\nKG0h1A9fvp9g6PzPmmkQ6/71eWRXxDkoRr4+Z7TayD3KodRuCIlOLeQiQmbw\nazLxOIGd3iDq6ZDlEWcp8X9bkdDXEBglLBbfLD3uiWh8MuvVsNLRl9V6LtEx\ngY1Q\r\n=YTiR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0-next.11"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0-next.11_1621549729588_0.9389614359906568", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/console", "version": "27.0.0", "license": "MIT", "_id": "@jest/console@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4042d7b0f97a91c170b41177f8224caeecfe60b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.0.tgz", "fileCount": 14, "integrity": "sha512-qLjnl6L2fyfVz2yJKt9ubLskbhYY8/aDDLxZMtlbt22wli1Qj/KCGoL6QHo0nxxq0IIK3FZru/mr8I6PU49pLg==", "signatures": [{"sig": "MEUCIFb2xvX3fxg0Ky+TClYmbRL2hAkISk3NEpeLtmnTbHXRAiEA+gNrUIwPuVLF4/7Oby51mHpdkWWYO/AQ+XPJimtH+20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIXCRA9TVsSAnZWagAAkN8P/R/FdAMAMG/31n4Ov1P+\nvrS/BcyDM+f05dkhXC1nG2k/LxLwj8rWveftKFoBfo772mFs5q4H/bCSko34\nhbMuAw2nilMzhqsRWd5RzPS3or7fmiTB6Iu53xtPcUbW95oCI88R9MQV1o+b\npUK12b/IPhaZ6YAPq+cp9AtCURBz7f4vt5JSZoD1TIUpUpG50XMx+7hWZ51J\n1pJT8HRsI1qzYgb1AGkXnDW0RhbmSz8W0ttR5Zx7KH/Kxt05cwERGcITg0Ow\nvwQxNYLaX12tFO0ZEZx0j47pjBEkNng+KKV3fFHKWSaIHpToxPrSDv9VJJB1\nriwIY+J8FESCrePlCrms1kLUO/BuRgYvEDEdfKy1qo3pOIkoICs00E4jKxn/\nJzB26XmZpfOrrHUDSU65EJwxHT0d2D0PgEVfGDZRlq58doOgk0RLNT8RVQuD\nGfEz8BuQZmUX0thWqnT2xAER76eCv/uL8p1GLRUkFQZ8EFogRII+Ul+EilEm\nQuSIZ73eqXWygvRkug52B7Jgi55O6LR2BBdDSrxjPgqHgHR00V+LjI4tr/Yo\nUL9B88+qxN76/YXU2y2VI6mhfBIr054ewGndBtL2yv2w4ydNixOPaIoJ+AXG\nhV0sa4K9mXEOnd7eMLqiEJdVqys+LUzoWUGD87V/AfPN+nzQDQBTRMx9GAgS\nf1sW\r\n=96JX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "jest-message-util": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.0_1621930518652_0.721813156207086", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/console", "version": "27.0.1", "license": "MIT", "_id": "@jest/console@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c6acfec201f9b6823596eb6c4fcd77c89a8b27e9", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.1.tgz", "fileCount": 14, "integrity": "sha512-50E6nN2F5cAXn1lDljn0gE9F0WFXHYz/u0EeR7sOt4nbRPNli34ckbl6CUDaDABJbHt62DYnyQAIB3KgdzwKDw==", "signatures": [{"sig": "MEQCIBI7CHeO30QONhxXSiq5iXDSG2rOYvXMRF2Vq7iEMYspAiAhuVZrxWVQPSL6vvXe7XqpsxwAgAWLlRNq+eKCtr/jng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwwCRA9TVsSAnZWagAAykEQAJE4OPBc42c8hVWsOeoH\ncwyxn2dJdzWHiqYGU+Po7R89xAhnvuSyvKV3RmTiRT2LPeStU8su2KoMqsKo\nDTTTPJ/ikXORJEM0eL/4/pc4QTZFO2RZDHTTMKANiGiZouz3d3Aurkbe7nfe\nuhnCZy93xdoZ3JSoqsAK/theT4EN5wNM2bK9taJEGM7pmj9upkqPVTSTR4aH\n+mXPdjfDI4pPE3tnyzxGymz3dkPJMeHVlExe1yDR4EUUIVuUUmf8WYUVoxkZ\nmzQVoQHPmzh/4La7FIxpC2YEEIPkrLuS6QaFe3Q7ovpNesoun5yN+OuHmrlN\nlV+kX49MoqmH1kJchcO2iF8MG8QYD6ijqUravlXR8V40lf2xtJVIk+GbCR3w\n6KB22I//ijhcO3A3MITNB05o7eAT5BWCcvNXgpH8dLoN9LrIHrIi5XkWZepI\nvLKUu68uDmutunHONL8QDJ+ecjh4TUlQKSovgIp857SnQLWWYqHswTWGqKdl\niszgub4UtTFygB8dVRNh/wzRc8suD2C//TuGxWTmkLQ/boxydBjbgl0RXEfX\nrhba0D3x7tF59JByDUGJHXrUfVmBvrwQH+i6Yp9O67uqgXxf3eDeMCI1nZX8\nakgDY3wIoI0tVVpB2kJ7nneGjRleKUtqHEYMSukmFCnKd1VAgXmGufwcMe+f\nnhoI\r\n=zgkn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.1", "@jest/types": "^27.0.1", "@types/node": "*", "jest-message-util": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.1_1621937200408_0.04488552964848691", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/console", "version": "27.0.2", "license": "MIT", "_id": "@jest/console@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b8eeff8f21ac51d224c851e1729d2630c18631e6", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.2.tgz", "fileCount": 14, "integrity": "sha512-/zYigssuHLImGeMAACkjI4VLAiiJznHgAl3xnFT19iWyct2LhrH3KXOjHRmxBGTkiPLZKKAJAgaPpiU9EZ9K+w==", "signatures": [{"sig": "MEUCIQDEz3PvOwsn/4uyphjz7G+o841svrvYigmpPXZ07h687QIgJ5xrDxyhv2f9P3jjnP8IqPcvfc89AjHS1qpQ+Ya2GiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5+CRA9TVsSAnZWagAASpkP/iw5PYCM2Ue4CVIwP1KY\nUUg7JMs8KqMbr1tJ1Y0p15YRARN12ArDEMHkuy4qn9C/W/20g3qLGU0zTxGn\n/Jx4nZB+s9pfANuN7Pyxqvs0CZf7xhRA5IWOzOBTbmI2l22zKDlWAlDobwYh\nQ3b91bTmM1dD1lP/soWkqnZ108OzKLNokLBpPXpuHkVQzO9v07vUBvsDtUxi\ndLLCWv58d3g5VUWBjW2G3xPTQOWxz4XIWs8zZyT8RcUJ5SzdQVk+saip+S+P\n7S+vGIVxj0xQ0LMLkgZxGSB2hp11XeIVxGrEQRrXJdzq10iHP+ksOvwcnnFl\nOoXbWf2ahWWgYUjWIaFfusqvVpicWTRIuJwh+Vxx5AOYYkPlRh+Kcy5VQQ+L\nuR0X2SuREEEGUKV2bw/8Gj6xxlp/LC8TZkr0rSyDReoKLkKy23E7J79q0ZFq\n3nJDv8UfcD0Sa9Z+g9ZSUgYbIjyRkPwepC+J60Vg0XIb5ztIRO37WkPz2tPw\nD1gcVVeDuQRphbBn316uG0CawK/RyHm7NNYd+ys19u0iYG8NNgY4Roczpg6a\nqvSQjyDrfmUCd8PvAxPN6DbwkoVkOlf9GAAlUtJkGQ5j9QdfdrNCeImvV/IM\nrECoSRdb81RVvTKwstIGfR65WqxA9bUSb2F2JQSr6ggAllrFD7AqdnG2aYzD\nwF0H\r\n=DySA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "jest-message-util": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.2_1622290046033_0.5473815556619164", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/console", "version": "27.0.6", "license": "MIT", "_id": "@jest/console@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3eb72ea80897495c3d73dd97aab7f26770e2260f", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.0.6.tgz", "fileCount": 14, "integrity": "sha512-fMlIBocSHPZ3JxgWiDNW/KPj6s+YRd0hicb33IrmelCcjXo/pXPwvuiKFmZz+XuqI/1u7nbUK10zSsWL/1aegg==", "signatures": [{"sig": "MEYCIQCdVjXaA3E1TjBCCAU8I1L4yNLJACHhdMMl+GciFKd1iQIhAKokuOev+mRbSojUwxjVRFj/cJVZlPLML79/Wpp+EwM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFsCRA9TVsSAnZWagAAHBIQAJDpeU8SjJ0HZlKWHA00\n2Lm5AMq2Wj95X9EvQ4UKWJXo+vU3f4fOcMP42wxkPqQsBjLZSNmry9++XY+p\nnXHPOnmlhnddMIg03FgR280zU+8wtBjTk4RNlD40HgrV/DovltJaMaxpJICh\ngDIU4dOAkgwYacXVlxHu3v3r5/hPa2vr0g/i3WDN9bY3b0x4MXfOiiCiJTKW\nna6bsaRT5ZKlggUMF6zZUZyPih2nok7ThRwU4M4p5RfXKErvVyjQE1xI72i5\nCkMRwB1PmGA7OnIcllIU7vi3sR2YcbNiF1yi4KtjyXYxop6v18lv2GCxz91x\n1VPw4ytwaTofq6sg2FdDA/Lc5L8QB1T5sLu9VFtt9U40PXgKwPipmIfa+HEZ\nsoql2C5ZaugPdv79SLTNvEzYydVOP7cagH/VatE7d0ZB5L5En6QiZNTlRbZs\nf0bohP1nxxDjqQ1VZ+8jy5FoifOaqomrrb6bYiktRqQDo1krXVJl5QDQSnKz\nAwD5FY9llZiKFqWlEqgwjgqMsPiKHIFwY5h3qPGRx5hnFCeO00euMup66gqp\nmApewwfyZNt0sfnsqp4woYelsrFub9nc2JCsE1Mok4ECXMaA2slT6oOvR94Q\nFLYF+FEL8YCm84Q1KA/qzf+xZp9jw1yBRbsDivtO2BQvW1qVJWD6YAhs3y45\nYjt4\r\n=Bwmb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.0.6", "@jest/types": "^27.0.6", "@types/node": "*", "jest-message-util": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.0.6_1624899948033_0.292952143873072", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/console", "version": "27.1.0", "license": "MIT", "_id": "@jest/console@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de13b603cb1d389b50c0dc6296e86e112381e43c", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.1.0.tgz", "fileCount": 14, "integrity": "sha512-+Vl+xmLwAXLNlqT61gmHEixeRbS4L8MUzAjtpBCOPWH+izNI/dR16IeXjkXJdRtIVWVSf9DO1gdp67B1XorZhQ==", "signatures": [{"sig": "MEQCIQDwnUjWKZ11+vuX1hzo+0Iv3otiYAGcLhJFQPFCOtxYNQIfF1L63fZP7bUH7xVc2/iu3prbMlxhxy4nbcMNonH/DQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeQCRA9TVsSAnZWagAAMD4P/RdcG3AxCzDkGDYnYoft\nkpVLyc1WJWyFVjFINF4Tw29iY1PVZZsUg6qIQQu46Rb+6CXQrnw4AQVAxjKW\nYnVpQYkL1lPHvj/IbKQXv4NUDkMkPivji10oFUglQrRVJ1VhRXo64EGwZAv0\nca9Yqw2DgU/TwgoddKs9TkYA3ibH5NSQKYSw56jVmqwBAgNucqEvSEnlk7I1\nAzp9xjhPNgDOhRucc8PpIWMzP65+3R9EuxShYMbkpIqM0m3homd5odyJtJlM\nWG4jniV3kSIfJwuL1rwOzSe4UcABsT4e4XXh2pn9HSgLKNX1Kb2YuCRh2Ojt\ndFPGxZLzEmP8N3zX93ibTCZs+ukan4BuHWa32+vQfuPP18u8GtbS20bsOS/O\nbq2XBetXhYQeWIL9p3c5X4GOiHNnJJrk5XV3S++V2HhWz3ol1mKx44gvdZ5c\nwimks0IuhJHhPmaJWoLwW4oihE3vPniarp1SU5H+c7gdKYHIp6FT2X9aQzaZ\n6N75IP/Cj1wzvWdHyx1jQA9POsK9QiaxF2xrZC6bzWtq3+earMbZc/MTxmjI\n7Bv/eVs8jHR77muXJOU6XcvWynJrMdnIaUhb4EtlEV0T0nVqYzOr0CcxkT4n\n9OjraKoaseGtkamtOSMKI22+MgXvXLaNgqSPbLWr2MxrZiEAlm+335lvK3v0\nSvEb\r\n=R8ao\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.1.0", "@jest/types": "^27.1.0", "@types/node": "*", "jest-message-util": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.1.0_1630058384158_0.3195906001991584", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/console", "version": "27.1.1", "license": "MIT", "_id": "@jest/console@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e1eb8ef8a410e75e80bb17429047ed5d43411d20", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.1.1.tgz", "fileCount": 14, "integrity": "sha512-VpQJRsWSeAem0zpBjeRtDbcD6DlbNoK11dNYt+PSQ+DDORh9q2/xyEpErfwgnLjWX0EKkSZmTGx/iH9Inzs6vQ==", "signatures": [{"sig": "MEUCIQDXyHyaOO7qDq1123yaBo8J1TVRy/JkAKP23MGxz3bHIwIgGF18CV7LP4WE42MRthJ+VnqapvwQQ62XtypRxW5h4/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyCCRA9TVsSAnZWagAA3qoP/RjdzXpKVVSVM+gjz4rG\ncIi1v6VpVPJqNlTsxsjBTyNbJZRtpl3w7q+sNJ7Ep1SH4XgijJe2Jul01doL\ndKF4eNjLl2CCfH6E2bf6jpsg9CtylZL1jPvQz1ZjcmrDemPtSE09ndO8uzsn\nry9qsUWkqWdWFVj5CFn+nwx12Sugu+bQ99rryxhDozmlgiDS+ykW5WT8rniV\n3rDmFSLKqdguTsZoroJTWfLTwVgyRAxcRiAgHSWCTWuGrvDZQQ4hOMeE4peE\nGXkSQbjlaNBfBKpd4P8ztL4cevef8C/mW+uTCyo2Wc9/YiiX1DyOdLtrKuvW\nivJ2gm959Gk43103s2mwayD2qldUl0EB5Vv2bnwjduY8EOSxvq+robxXMYa+\nbvWHLyKNMKRUB04E5Galx40fcHQmzxbf9eF+fGs7KkajEj3RWuTmr8WBqGVY\nrnRe47c8Xgfu9T2CbgX+aVyIO2ju5MLjRRkveA+V+L5ALaLd3io9nLN/geLu\naAhhiA25qgjFC0l16PRu2XK29v40pAK9IrtaclnWKp3FCeaBhmXgfBP2Wvyk\nZynBJYnPk+SYSt2tX9Kdow/U2Msj0b9aX4Wt1FcGqU9So82DDkF/XIsn3NZS\nrOukb+X+7qdg49n8st5TUbp+VuMZeRyuB6t3rdp+eSEcqZgSWoiQuWvujzr4\nroAt\r\n=kAEA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.1.1_1631095938247_0.14015090531488772", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/console", "version": "27.2.0", "license": "MIT", "_id": "@jest/console@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "57f702837ec52899be58c3794dce5941c77a8b63", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.2.0.tgz", "fileCount": 14, "integrity": "sha512-35z+RqsK2CCgNxn+lWyK8X4KkaDtfL4BggT7oeZ0JffIiAiEYFYPo5B67V50ZubqDS1ehBrdCR2jduFnIrZOYw==", "signatures": [{"sig": "MEUCIQDlNko8KxR2/Tb7Aoyu/pgNEPrixJjDVSYisvpH1R/GZgIgFJ7m3GUWImqOPYpY7Oo12iobX392MN2zfTmAD0kpHWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaYCRA9TVsSAnZWagAAI+wP/Ax66PMajuzFMX8Feefj\nRiv7MPR+ECVK0xCGKfkLFFON9FJ1F74379bZjzZ67rqcBMbOrLjbnaG5ofK7\nz4h8HypZoTh7PGBPqhvJy8iqu/zJ0R6NPCWBdwe7laIPKgeYbZZ5KfT2Q8YI\nmjYitpaFKS5ZEO20mzG/qS8mhruajeGsn7aHKTguckTPKQIHtpvsx+wm8vxv\nLkQU+xJyQ2e8uZ7sz+Kp/PFOdWT9MkSDsX9sIHWfdnJwYI+QxMrRLKqcEfR8\nBFgOIB3DTonw14Gjet3wRhJhqxi9KAwOJZYVmmJB9nAikD4Bxn01uG8fg8UW\nlu4UP38rHdJ3kQGYd3fYA1AIcHlsVmNzz2BFVHX6W2UMsCNnuCNHtlaGflbu\nYoo/2NiH6Ts4mFhDEEiZRCK/kGdExKuFumaVEyeIKZrDJPVzoUgRwTN5lO0x\nDGQbpkGI1BNLQFcqzX2ep2Pjd4p8cMxvNOevPLEbnKXntvjyMekOl/cF+2Yg\nx6g3sRyL/pTHHYwx43WMzURiJ7wWO3Zz7pysyC/IGaJa6UIhbfyzlWDPYoZe\niShNCEjPGvPpr2/udmRGV6bbuGv2LWT8Gz4EPwGWLwqdz+rKB3NAQDd0zNgi\nwKukZcM57cRyKPcKm83L+eCIKECMUskeiSiqA6uSEqIHbQ3S6mFeIx4kiT4G\nw/vq\r\n=NsSz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.2.0_1631520408226_0.8935150654549084", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/console", "version": "27.2.2", "license": "MIT", "_id": "@jest/console@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a977245155c519ac2ef713ec0e722d13eda893c9", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.2.2.tgz", "fileCount": 14, "integrity": "sha512-m7tbzPWyvSFfoanTknJoDnaeruDARsUe555tkVjG/qeaRDKwyPqqbgs4yFx583gmoETiAts1deeYozR5sVRhNA==", "signatures": [{"sig": "MEYCIQCyrh4+tUl3/cPVwUzV6spthbm2D86wtnv4fMVglHrH+AIhAJ8lVIL9frQnLg9GhZjRJ4ILCmvAq8uRdBZCvFoKwIJ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "jest-message-util": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.2.2_1632576910363_0.16749505221000294", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/console", "version": "27.2.3", "license": "MIT", "_id": "@jest/console@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c87fe48397dc7511089be71da93fb41869b75b7e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.2.3.tgz", "fileCount": 14, "integrity": "sha512-7akAz7p6T31EEYVVKxs6fKaR7CUgem22M/0TjCP7a64FIhNif2EiWcRzMkkDZbYhImG+Tz5qy9gMk2Wtl5GV1g==", "signatures": [{"sig": "MEUCIEbFjF7lDWWzVd5BUKbtlWdB4fCdvmhBZZ8JLpthN5B/AiEAxy/y1mOWdZZkub79FVH0ImIyN837gFBAKgj5RG5srzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.2.3", "@jest/types": "^27.2.3", "@types/node": "*", "jest-message-util": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.2.3_1632823883558_0.24548696302615047", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/console", "version": "27.2.4", "license": "MIT", "_id": "@jest/console@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2f1a4bf82b9940065d4818fac271def99ec55e5e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.2.4.tgz", "fileCount": 14, "integrity": "sha512-94znCKynPZpDpYHQ6esRJSc11AmONrVkBOBZiD7S+bSubHhrUfbS95EY5HIOxhm4PQO7cnvZkL3oJcY0oMA+Wg==", "signatures": [{"sig": "MEUCIQDoORhoYpAJxe0kyRROR3gCmMk5yL1CgtUoD/x2H8nnygIgJYHqZHWM4cchpnWm2Ll5LowAp5rCxl0f15G5ylgd/Gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.2.4", "@jest/types": "^27.2.4", "@types/node": "*", "jest-message-util": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.2.4_1632924290836_0.00046303780543954787", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/console", "version": "27.2.5", "license": "MIT", "_id": "@jest/console@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bddbf8d41c191f17b52bf0c9e6c0d18605e35d6e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.2.5.tgz", "fileCount": 14, "integrity": "sha512-smtlRF9vNKorRMCUtJ+yllIoiY8oFmfFG7xlzsAE76nKEwXNhjPOJIsc7Dv+AUitVt76t+KjIpUP9m98Crn2LQ==", "signatures": [{"sig": "MEYCIQDYfH2YZTDqvq0ABxTEsnIJ1exKlks37G7fPPCa6y7qlQIhAKu0OJk9haElklMVA5Aay0pe+jkXh+/bjCfjCqqcTYI3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.2.5", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.2.5_1633700363951_0.2748666130069928", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/console", "version": "27.3.0", "license": "MIT", "_id": "@jest/console@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a55f03a4f7e1e92a5879bdab2e8b9fe4dd5312ba", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.3.0.tgz", "fileCount": 14, "integrity": "sha512-+Tr/xoNiosjckq96xIGpDaGsybeIm45VWXpSvDR8T9deXmWjYKX85prhz8yFPhLG4UVOeMo/B6RI/+flw3sO8A==", "signatures": [{"sig": "MEYCIQCB+iAgmZlhi/dYScvYwtj7jWI2THQst5UqFRH8DzZmYAIhAIeByRqSwVHNN9qEXN+uFUlurSWq2dh0VksJJ23JyLRj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.3.0", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.3.0_1634495688400_0.03578937440763519", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/console", "version": "27.3.1", "license": "MIT", "_id": "@jest/console@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e8ea3a475d3f8162f23d69efbfaa9cbe486bee93", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.3.1.tgz", "fileCount": 14, "integrity": "sha512-RkFNWmv0iui+qsOr/29q9dyfKTTT5DCuP31kUwg7rmOKPT/ozLeGLKJKVIiOfbiKyleUZKIrHwhmiZWVe8IMdw==", "signatures": [{"sig": "MEYCIQCRAjh4dAGji3l4Gdsk22szPXiIqTr2lYrJNxfJRvEosgIhAMedvTLwnAPwfktWJUb+03ksb2FD5GbDx94xE8R2ezvJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21609}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.3.1", "@jest/types": "^27.2.5", "@types/node": "*", "jest-message-util": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.3.1_1634626654418_0.5328219789936024", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/console", "version": "27.4.0", "license": "MIT", "_id": "@jest/console@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b3fd8de361da5366357ecda3c4d966dfdf03374", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.4.0.tgz", "fileCount": 14, "integrity": "sha512-2m7Xwcd1zTWtai5DCl+b0TAfoH8p5uqUoKmfzJCAfCrIwoJAf3xB+4nx3eKEGoyNfg5oavrh3gjbZ1n5z5eh4Q==", "signatures": [{"sig": "MEUCIQDwgBFtqck932RM1pGMBGP3+RN/T4GeAfAZrvIO6Iz2HgIgXybjD5wVV9YIykTFJuqVC+9DN0YGG4pvhPYHlSbpcjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNeOCRA9TVsSAnZWagAAejkP/RF/TJw41FfaDZdUTLzG\nXOIIfpUJL0ejroQywsbNogXEOQ98STTMmZzAtdQu/pdvb8J/uHNSlAQSQr6G\nXvwTH8bX2SZcS9gFwMQyu/n4NIqwOMOC6vYgv1yjM36Z2mLDU2QKellk/jk2\nLZM/OecJ8sD3iTpu7hn2K8PxEzok3P2I70YYYPjT4IU7tpTLY3Jki5NB754J\nVU7OKkGtFg6u+/5Xs7F1bGxTeRpTM6mmQrKgn8ksQo1Y0oMt3piiNoSsRETA\ncD374j4zzfL47nvdIzb1jDUn6fAAXHjydZ8W3HPL9AcMC61iGkeHvKVXyfec\ncL0CFzflRkq/AHADMzMyi2Nz1taD646+PogF9eWT06K0jjkkBCeepUlGTii8\nT2SLtfQnDG6EsOyu3rGGj60SQslLXl9nAmz1dQxTyJ1kcGW3S4CEhTBJ12K+\nKGI6OCXWGXYDo6rV1RXwLZbkyL00VmbZIDdvcp3590sfI/bY5HCplWxNik+P\nsvf9VlS569VxpjLVcNPfP136kgU950Wp/NXOXiV2Uq99bbch1FrW3sPgx//N\nygs7/dCZnTsdYgj5ko/rMqF4alZPxv1H1SB6IissFfOIkIBbDraNsZjfgOCk\ntbr15YhBThoTSuaP5nLaYkBrJ4mjVu/CRVO03bZPP7UWAafUTlJR/0mhCghq\nY+13\r\n=f2Or\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.4.0", "@jest/types": "^27.4.0", "@types/node": "*", "jest-message-util": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.4.0_1638193038398_0.8400227796103343", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/console", "version": "27.4.1", "license": "MIT", "_id": "@jest/console@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a702b77b3715503d67e596badc0105020114d68a", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.4.1.tgz", "fileCount": 14, "integrity": "sha512-sZqhfQnSpjSST2Nxu7XLzAVtXzPnNHD+irkb1gOK6AXoQwx9l9qZB9+iPUuBHlB4enr5ljvfWuXzwMgM4aKjOw==", "signatures": [{"sig": "MEUCICXFjRxo903eHH8dyNlyfdjvOzazBrT3K2ubFW3wO6YaAiEA9cUuFQ6FIpAQt7wblNnoJxTWuiuWRUVETaChHK0/Cbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK6CRA9TVsSAnZWagAAlKYQAJ2lDjUxVg7WcCtcnDGi\nZLx9/BlQhw5VMDOdnhBL0lrkBv8cZgUqI/U/P15qnm1O0LiWqVZDS9pIgNqa\nQPeAuVon/qLDIlWwUsmPLaFaE6GM/pkx+1e/NrU2m8ud4CgcpIBQe7SXJ2wT\nS0Te7iBUHeknYviWtz5UHUsKql7faxeSB19tAXDv2tEXm/hZ/K/5mTSKzrPH\nC2XlgAH//RjMgostbtxdU040Rv+oQfSdN5/ZoWmpV+vV6/AVE45iA877ssfy\nzWy9vPXzt++Soayx0XMzosdaksRhytyYosz4WkPtRFXUYh3/Oa1+GokZrwjZ\naO7vSGB4IEMV5q6I8dlZAjqeA/WJrpHU243th52r4wKh89Mb3vj1HE8kU6RD\nRSMHv6tq+7jOir6D8vE3pWYRgMEmnRDK7+lNTvu5whsgOD79b7WAh+LnQvsG\nvp5R8tBzIxysRmLt6OJUIJVJGNm4jBjk7a38ZU/QkUJqC9x+t8dVJiIZGfJW\n5C74Byhr+0PmG2GxFDL96vim6j8PjwzrKh1Z2CKn5pV0Xc12v4dsTPvxptGZ\nt5StuGWoEoL4YLOp5mOj8fqvF2lyy5dZ8lW/fGTzs5dmsyTF1wsDhznQ9VQR\nNlfKDaFFUj5x7ClkjUzWGyyErX9eEnO3ZhOH8upd5/BDqyafRUXAj5iBu8GJ\ncfkR\r\n=JYgG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.4.1", "@jest/types": "^27.4.1", "@types/node": "*", "jest-message-util": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.4.1_1638261434316_0.37885253943577", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/console", "version": "27.4.2", "license": "MIT", "_id": "@jest/console@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a95612d38c007ddb528ee446fe5e5e785e685ce", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.4.2.tgz", "fileCount": 14, "integrity": "sha512-xknHThRsPB/To1FUbi6pCe43y58qFC03zfb6R7fDb/FfC7k2R3i1l+izRBJf8DI46KhYGRaF14Eo9A3qbBoixg==", "signatures": [{"sig": "MEUCIE9geox/U/o6KOAEXamYMnxghEisMCb50Efj5enG9LTZAiEAx/9y9rlMJzV9rnc6k5GnncjUt4lIpRpEd4iuOZX1OlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDPCRA9TVsSAnZWagAA3NwQAJbx0AA5yE4uvVwMwHwK\nJw5fzifD5vaktUjIe+SbwqPmgUQdHqgis8USfQARnfwZTjlOGvajVGWFN0M4\nFcBbCvYKIXw5EqnnyMutB7BUP2JrLSZao/HadSWT08o3++Wg/Pl25JDrp6go\nxIo/OaAdEcNHEVFp13Q2tDr+Hv+GI3pSQC6RDHKTl7DLCgMXAunFIow5hzdr\nZw+Bn0Pvc20vLFpcpzZoDG9hsZn585vqnJalDtFrb+6RLYYBF0qZQFG5x1kd\nrPVYQgCoA8/rWmJJYlCE21jHOfR8WKplt11ygp/+SQSzfRSuH6kpBj4QcgQX\nf21QwsvOF7kv6WmwEmAG+xBRVEnt4cF3ujHI7Wfm/4r0ptkDBWCgX4Anw5Qh\nUu7a7U37ElMn8yaCgFk5dF2cyb4919BCp+dgHGhtDNM/MK5RK07c83PsGoXy\nHLenNTG96LZ4GObsa6gAfbWRl0j5ZICRYRhPUOF3URQ/yz3oKwXIoBBoM3YO\nhmR++xzdwJRBk9pVQzQ/c56RjZQO3kSeJV+7Z2t2Y2kSqr/S6xeCQ+1ZM6gb\nhHabAY+ZXpJ4vlDurI3RqlHYrH6VTRw35mwxg+ZRETvKPLhO2CvntmlE2Kas\nBDmWVaBimDi7jVeAiFCUQmlFUNbR31wC0CF0s9NP7gyK6E3SA0r4tqW014G2\nwiDZ\r\n=/4rU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "jest-message-util": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.4.2_1638273231301_0.17984507488911827", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/console", "version": "27.4.6", "license": "MIT", "_id": "@jest/console@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0742e6787f682b22bdad56f9db2a8a77f6a86107", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.4.6.tgz", "fileCount": 14, "integrity": "sha512-jauXyacQD33n47A44KrlOVeiXHEXDqapSdfb9kTekOchH/Pd18kBIO1+xxJQRLuG+LUuljFCwTG92ra4NW7SpA==", "signatures": [{"sig": "MEUCIQDEcVnSsjZxCs9WCDvKD818EO60D3G4rcCFmZ9h60iEIAIgNuZDSJNOHtUlOZsiiilB/XsfLXed9rNzLXo2BR1QckA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJMCRA9TVsSAnZWagAAp3sP/0EaWwnYVYcZPHUub2A7\nJ9wOVZiHfuoqRYoa5zFiuVaY5Gsl//wKsbuQkZlxTrBwQcu5fWKBNDzE5i0/\n+6Mk6Qpggq/MeNYSBWFeRWJUtz5IWGBiLxUvi0JZaenIsjoHMTUH/NLXL9yU\nBXVYq14yGeOSuMaNIvuo7YREugOmsoAAYM5qTcGjjMZ7MqusYFWehHHbsC8g\nSRMcPNb68FNGP7SrEZWDk84LnxoaEOwhqJQFETmJJIA2Q1NoPQWBgr8bkumN\nMq9CTyid2dlXu05sRQvtw1o6jufkZP6skrh9l9AdRwXl1PZuiBs4UQPurbOn\n81lXQiytSLaWgInofsZXNSKOzoK4edKisDZCYQb6moBmjyy12DSiisKOhM7f\nxK+a+P+U5BTIQJxR/XkVKgTUEEwjSNOkDYvf0M0ob5QIOXihwqpmjK4IWcPJ\nSllGEcVCsELUzTpIx7kyg3/ECcsvdNQpgHclTMBf1DUH+WyyuUK1gXfvG5fc\nepnTEdbEDFmJuhoo3QUib8GSMIgLe3mkfPH92Lw5OloFSqCnYXCJiQQ9owff\nr2NuW7HkE5mKeBXBy7jORs+SkY81jxctEo8ILfplx4WLgGCDCFRpQPEBGBw3\nd8Q84R9AzsMcjR3ZGDyYRIT5qg+cNrO5SCYCcy95svcVj46I0q9NFM8lQ+D2\nOdJz\r\n=ZEpv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "jest-message-util": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.4.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.4.6_1641337420000_0.8321169035742626", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/console", "version": "27.5.0", "license": "MIT", "_id": "@jest/console@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82289a589ad5803555b50b64178128b7a8e45282", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.5.0.tgz", "fileCount": 14, "integrity": "sha512-WUzX5neFb0IOQOy/7A2VhiGdxJKk85Xns2Oq29JaHmtnSel+BsjwyQZxzAs2Xxfd2i452fwdDG9ox/IWi81bdQ==", "signatures": [{"sig": "MEYCIQDgDY+2y+SBJPvXlgMQyahN9YTP1JTx1spxM2+mx/GfRAIhAO69Os7EjSGon7pSiD0qLR6rUpWjoGiusaMTadNRA4d+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp9CRA9TVsSAnZWagAAb2gQAJxRqDGnJBVuKz0+RkxP\nCib9frLlc3E0xHbfJrZR8s7nn0Y21RVwaujTvRxQLke60YOtx08iZT1IYUgc\nEHPcdwF4kL8losAUeIW1RPLquF7YlRFmH3tjiq9nyLvi6quR13z1QD+kbw7Z\nYTVqoEB4ktDoA1VY14xZ3pPhYQYON+2bEQ+WTNTDaJiBdwPU3ioP2m6La8KR\naLy4JJSh1PkC6asWmBq4ih21f7M2MH/qp9Q3M+MASAOW2O3m37gzzaXG+ZYQ\n6ia20JXByQYwzjdgyl9fl6HsAzm3fqs6dkBVf/zcm1TdfINEogMYeBiS7XU0\nSydPorFiqUwG8LWshDW02ntA2uhcbyBNzxicwheHBmyn3CymEimUQQcj3t5a\nNO+dPk59B6LyROfHMf6IYn0N+yS/HCZuhVB1A3oXLeknOJuykhMl1tIHzrys\nYJbJ0/bWYyJSvXR6lxqQWaYvqhtpgJhhckBu7Yic7+frZ34XJM39nWcmDtL3\nSNAcZjbD+dNWPcSYtzYcjIDDMhpgQ9IWswHWQUMmhzIyBitEGcYAS+1Yspvy\nbhgV21ZeU9EA/u8fPjT+rQrNWh89f/1dstnLZ2s1WjN+JsT1RSQYdhHFjoQV\n309zV7c2Dsd08qDYCttlfWyHNQrw25a50C6EGQmszfyn+wd11ZwT9J2wwViu\nVI1M\r\n=qSGk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.5.0", "@jest/types": "^27.5.0", "@types/node": "*", "jest-message-util": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.5.0_1644055165079_0.7947237279597978", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/console", "version": "27.5.1", "license": "MIT", "_id": "@jest/console@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "260fe7239602fe5130a94f1aa386eff54b014bba", "tarball": "https://registry.npmjs.org/@jest/console/-/console-27.5.1.tgz", "fileCount": 14, "integrity": "sha512-kZ/tNpS3NXn0mlXXXPNuDZnb4c0oZ20r4K5eemM2k30ZC3G0T02nXUvyhf5YdbXWHPEJLc9qGLxEZ216MdL+Zg==", "signatures": [{"sig": "MEUCIQChvf4XGivtaDNOm8PNVqzqbOPKPdafpyKJlLhU1sJJXgIgdflBQ1gnrvuoVp+gPNWZTCLmlpX8DB/jL6B+ibgKEeA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktnCRA9TVsSAnZWagAARQsP/0ordBt/1W9p1n8azseF\nqZbfnrF9drmhv35FlTHXsz3d/CqO4z6RlXdonS31c4w8Of5uYTJdXgnVc5oG\nrKsovUzU3eAQOwAQySi93IgLuoZHcpZtPYVODwRaixG3wz6VKbGLr7KEkCrn\ni30CVagPl4iMo9d/L7Dbgcrmd/0ZQwhGYau+LYB8JHzpChVSww5Pc2kcpR5j\na9R+vdjzh7cmX1nG+xAwrwnvHwGmZxSsZ3Va+OGgEColKfoK1ZwTst/G1MjM\nelRIu+3slCIm7ieMufDBwF4PsOqmcEnPJU7Pv8UQcb2Gq7MNnwmhmohhAbSI\nI4UyQ1okWo+VX9+crre7A5GX+6eyY4gKtQqY/uYyEq+YjQCr9MipTYt05305\nOr/hxvSCLyRI97DUshPUiCSZEt11ZOBlWsPo4TTaBrVZ3Fd960SQmTtoNNNM\ng7mFRn3+7JaAa1/mN+Ry4R6mN8QvVNH15uekPpPBjmiIwM7VBJ5hzahELoUK\no0PwxPacoEZ4MAOc6E3NxcGu4RTMoC2EnekqOwkQYvF4gkQpulsbQdDYaw5k\ntucVI+kp3FSdHbK3oHpQqOzERlHsrKLDD4LUK74UksoQ5cbr8dyQtO9Le741\nviP/A60dCnk8/TM1rJlf8SmUWohwnfOQX9oer2cBqiMiOrFGGD7qfihVGqVv\nXVPU\r\n=Ao3F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-message-util": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^27.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_27.5.1_1644317543532_0.46491967973450143", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/console", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0141c4ed5c17a6aebc2c05821892fc03c99d5ddb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-mFPSgQM//GbrSYeJiBJifVIlLC7x5F8MA8Uc7Y8w6z+ZpMqI9CgvZhMlWcgq10LkbOhlU2eooP6K3UZ4FxZbXA==", "signatures": [{"sig": "MEQCIEmc05F80QTV6dpt9ag/CYVAiE+SVZUSlEZ9LBJflg6vAiAPzVArf/somIFyEmlpon8brzZok5JGfiX52rKs8eKHJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbBCRA9TVsSAnZWagAAYQgP/12lvx/9ud9ZOl2pda55\n1GDO7RdjFz6NpL/65uWcxMqgpCbSqM64aux9xkwOTSkk617GcG/qxaZk9IP2\nlSMghedDBI02hIL7DlqIGSO/RbY5CGcwFd7/hSDiklN+CbH4UCIBMMqW0IRl\nPJvM3HgetUiRXYkNgeRleRNPtQBu/s7SMQjYywXpZcJQmhCWBREPfHcLRMy2\nJHmHYTd1rbJj2XuvR/iB1XcN2HUkUxRGjh8TwTNNJbk9kwBKMWZyuLcQOjrB\nUW4P1NNI7+BgtUpsC2puzC2vKX+MnkAZwfgc8hiWmmzx5jKsqTbsKHXtC95P\nbg6ECJaHN4LjqBN2+Nlg3Dje/l4P5b5S9F8GXhTAlsIhSRzeX8u+KteMdPTa\ni0TI7s7521sLoZwADmQ/WtP84nCPD/drowb100xWS7ilhdLCp5adoVBYGEgW\nteko2Jm50P5rY3d7qDdghjW5hr0yO8D0R2dsrabw6AytO44iogU+NHW7VFvq\nj/i1BhbfzlVOcW9Knz1WTha3QL48RMG/WpI/7YyJ0fEIgN0fQgDk2rQHua3m\n+MZHLel1X3ctRlMz7s9ml/Gg3vkj2i8BWPmYkdYjpbSaB6p2JOrunU6d5DhU\n6dCquTRGmFKrWOiO38axX0217no6oF5L2ylc8L/gVyQysUQd/1quCPMIXWcr\nWdiJ\r\n=2OtX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.0", "@jest/types": "^28.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.0_1644517057833_0.41307750301549695", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/console", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ca68fc6743f3248bf00b80279ba19c250887962", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-WdATCoXXHgIYWzqslM2Asr592upKu/NpgabNYDJtmWjtLpWa7W5YsFqm3LUJvxHQrtTBQFzX207jhqzRjpHG/w==", "signatures": [{"sig": "MEQCID2+5XBeYnAd6LYsQBe6muMygpkh7RFpZEpa9Jk7wifCAiAq5yI8Xxhm57SdCkdiQ4VllcmMdFJNBNRHnjI30yXItg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqlCRA9TVsSAnZWagAA6uwP/3ZmQgNUUAzue2fmEWdM\nQrBJwOAREC+0RSah/OQt5NnDTkA9daDCM9YZdXsK0Bt9xqU45HbmaGz/xFXP\nIQdNqlXKB+NuwEC5yx6NP7eutdNGnCK2FVYSNXwtti74p+koE1RsHBf+Cqer\nJojWw9NNNQqTFLU2omZyS/Pk6vx4n1CCRdNB/qTW/pJESUG5toyK9iB39n2l\nmc9KhnNJbif7QwHdChrfYsXF20lVW8ClaaqRdONSIWWSO+/Aykk1SR4JE4t1\nmL7BKTFdYFcmWyZHRhwcXUIwsXQp0ee5xX7J68rkv4bVXP8tsWJodNWIF3Wb\nhW2FStIMBjtDvUMTLwtSkDiTYzUNgsBRvGfBg5/CIHEaLWrwh1P06tcZfZBq\nmmafeDeAH/qj/MCOHqzEiD8PHzjbQxM49hGcE64pRklHzpIRIVah90GIPLLu\nAXWwiORZ88cV+FGsnVrg0AbWp6lpREYBn13JFSuxkJqEH509o9bXl++jyMNI\nJI9uaZughuHnEMayOzCs5I2S0jraNWt/xThOBk0SB8KMNgji5nD4eIk4QSKC\nYNTULmwwpu9DwNzt/js1VCj16ZoPhonv2JXHvKGFBGBE1uMd4Glp4mBpsf8w\nc/82LCtL3+c6QLeswiPL824sJsPbH0axYXHoxN6iE94xWJUSNl49yKtuQeyx\nBVhy\r\n=NRkE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.1", "@jest/types": "^28.0.0-alpha.1", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.1_1644960421627_0.39542593713663576", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/console", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "403ff2d78d36fec5dc6090439403ebcda2ebee06", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-5vvDnNq0FpEk5ny4bFrjV6Xd++L76KhMmB9oJEw+5pkywBTkWgQM5mL9joUYJZIVh2fwqeMcfrhEZ+4LHdKWpQ==", "signatures": [{"sig": "MEQCIARIeCt6dlgclosGOWRGH0ob5KwLHmo0a6JIlgkF3wMOAiAHLRpEyBtJIZJ9LJfF9GdjhmqmC4PTHWj0z8pMGAMoog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT56CRA9TVsSAnZWagAAMl0P/0sXTrMi+pzcGPb2pREw\nC2G93ghS7YHQp0c8G8Xb8wmhwSHIsC5XlzpCgaN6N1E8jaffZG6OdKNS4Wnw\n/JBMjLG03IQYurSGEgWegdgx1CKAjCU4MgQidWomTPECzRHFgIBAvDpCu19Q\n+EeExlF6mK3cgG+TON8s2rqwMlwJqgk+TBAmNf5Zec0axl0f4HhzpzyQLYRw\nsWBoLuVbafuYs9+KfudPv1kDKp0jj1HLHFp8UlLqAihvQ98/fFxmrTALi32Z\nqFDBSXcqr6GdTYKQ6HrjHOnuKodMgAM/qKQCQ9L6WbB39fwqXL9HhMasQHCK\nRTqH48ByvHLt3gvoC7aDSIw4D4nvRekWCBRJe+KXPGfbJxbPU3hxNKOEFGqc\nfYW0wV3BtTdbDfSWo4LEyY6DJPB7rJPuloA4qSrN40JAyoNGJ44yfnleXldG\ny+8qxLfn6S1D6iNBfTAtfgGaULG+dI6VdlgkSogjBjOlVAluIFN7p89TgorQ\nZ6XJ8PWT5XIGS3qoxa6eH09MCRIdzirOLV3H4qsYVd4smeiQ2TqAfsETQT6b\nb4XVhRhWeEMiXrxWC7KXdTvgegkdrUXEoZgHWyP63Rcf+N4ArDNLXNLOAIt1\nvqMCbIYYJFQnuSAa74TrewLf+hoa8VaeydI1XXoy+XjaFykcXWBxgnjaNIV2\nLwDQ\r\n=fJoq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.2", "@jest/types": "^28.0.0-alpha.2", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.2_1645035130690_0.8684591007684952", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/console", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3faaea113121ecfece753582722b44689152fe23", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-wXugzJlay/fnsKqKEnpzHOBYjAHh8HzhnJi0eT+xbMaoNh8LktIpW2wUiqH08Txso0GjdkANh0h5RUfRMgewAw==", "signatures": [{"sig": "MEUCIFsQIou186viVfiTUTdSJSqEnY5cliN8HMvetr6RkiW+AiEA2ViSRXNCXSyPTfe6D4PU4vKdAtbGPQ0srO+OsZWIpw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0kg//UHrmfa7QF6KoN0GaMRjJRkpkme35yYM6J+iK9pZRvZI48PUx\r\nzbZpvtIZCbIWgrYWFpIj5ZK0Nh2aF3tAYgERbXJrfI60hbfULdNDa5o2BXIG\r\nSlaJRuKktyda3SWe0tB71k1Uw+kLOZOLxi8yB7nraQAoaMbBi6Ey2eHbZMeC\r\nt/TFR0o+5DZVocrV12xVp+sMB4S2FFDletTJD2jhq61dtwT1iAiXQoEPLDF7\r\niBC7fV6A3XmDOLHJg2gjq/a5TzRUTl6AudI2r68hs90D8D7vA8wcKNn9HVUG\r\n3hd6juR5hFB+paeFYRVDJDdchvR5ZLMD7CLh52G6WWI6gP60uSg5b8ZNnOiJ\r\nQgUfpeTW0iwUtUpvn/rHHOb1YwKK2zXD4PoCLg4MkhBrxgMeQ+LB0HYzO2wU\r\nMipMlQ13V793W+fK2yLzJYeivs62ea6TgEGBpbDAFnFAvh7s5BitNiU6OnVy\r\ngRoZHpNBDKqixG8GdZnZgoH9QJmYNQ/7rCDXkdBAEApC1xgqFhED09ManNWF\r\n0oPsDlLTSIQ5+y6rowd7u+kf+Ne5gerSFQqI3+xUjSU3BA9tgBktu3OH+p31\r\n3QZL40Ha5is9JgsSeXCiuMCQ84OIfvGcVB/d1+TVV/rcj8KSSrcas78H62mG\r\nD/Pseer81RdhEVJgg79DpnOHdqyHZaT0NSA=\r\n=4uZM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.3", "@jest/types": "^28.0.0-alpha.3", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.3_1645112544067_0.3799447072706441", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/console", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c8c6030aee8f9e9176212bafc6b902a6a126b9b3", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-971DgMsSvyfMeRvod/Z4o6wLnQMeI3+Og8Z5A7lyovS/sEEs1AUaMKrl+qgsSmuRIPIqDvGRY3K9UN/VRybIag==", "signatures": [{"sig": "MEUCIDnkmfbvYKyuYOp6SxyIzawwNnxrglywdrNee5Jr0fefAiEA0GD8YvNuzW70RijRZwMYH6NVIwIrFW6TKNjDV7V9Uo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquOQ//dTJi4dbM7Bwowy4FDTsgdvPA8nc+qiCNltu28RcOdcoc2MWF\r\nf91T+FIl0EW9lzEiVbctA+ZGBsJnET4ncyj233rrpj0UbOEIlkc0ZAL891N8\r\nOoms9jqOi5rIbQ1kAVHHFbr7GNQUJJ8kfK0c1i1qo6cAGiQDeIWGKPCXGIij\r\nAKIUegMxKGdxPjfGtw6uZNrtbhCfv1M/sE3FEIOROy1RAbtqpmvd9B9B8fym\r\nUZ5z081OXRrvjVg1sG4fXobBIiVRuftfZlVKZxtZzW/DtLHdiQ/RlkFni5RO\r\nri4mdK47JRwIsPxquSVdDYIOvPTWhN756OXBIG/bQ2Y1a74aAOZZsaUGgwXq\r\neZd44N/YI3l6LCaqNNTTaUVEhfCOcrojjVlp0KSfwqrU94tqdMuQLX8oTDBV\r\nfauHBxSs6Emvk9kGiGNqmY5/m41nbcu7LiTeJT60AHwRZmmkyjjjtVRDXh87\r\n9hXbual1fpHYkPm04II6Icfp1MlpBiniZ39eyTrgCEjk9R2n3F7zY5fPFO0X\r\n4F9XVpnT1kgy4JweepucsCrPRK8Skss8IMh3XOZYjbOZNwE0vBwUqXMv/GEU\r\ntzzjJzQlaK1R4PDMfeNr74H+nWCFFueuurB6JPcC6hg4xU16gfDCUki4dKhC\r\nx3GZ2QbZCa4Sn2FZElH05wos8F/paShFyNc=\r\n=HpmQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.4", "@jest/types": "^28.0.0-alpha.4", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.4_1645532036794_0.9495238078368253", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/console", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dcd4d3059002b0a7ee03522cc0afe9f63f96b66e", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.5.tgz", "fileCount": 9, "integrity": "sha512-T64if2TCPSPYhk31q3xOMZQIhHwSOU6XwbXGzj0RY9lR3eQjkzGZB9piZGdTdWLB2jfLGvdxeYEhQDJSeTCzaw==", "signatures": [{"sig": "MEUCIBRcishaherJq0x5s4vwXU20XknPK6N3dXM1UiU2Il6PAiEAzK3K5AMVs4RkHK0T+EunLqMdSrCSFM2IgpWcqnEDmlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/ExACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2aBAAoeiQoZ0RYZzH3Z8YZXOrzaI0bayW4bUcXi77Qs5+jMiUQl3E\r\nLvUaPjssxpCbclO/hW4BkXEzk4swizeilYrR/yfWfHaPdhHiDU/HSYgRcbtH\r\n1k1HiZ5eeC3QH1fnNOuLAWc+XY3lt6uU+4CqoyX3tiLETYAcs0ibFAvbD51i\r\n4ApBwgI0VqjqDmXU/QBK50UjA7Rk3JSHVE2FEw93QnPrs06fgMFyZAcTXJ8P\r\ncH/DUU3vZXifK1OVyrRD299C+uuM7/XhqScjcUuXVrgAc/pKqNf69jNxZLJt\r\nvEw6r5CGeAQtmcqercKyolcEHL+Vcn1mF+rAIAglW5l82SYCtXV15vTmHbLe\r\nVeLq9XY8oylZiXaftfKN6Kjd2W9IKa5tqwiQPmFoQpj24/ymOjuF9IyrOPxA\r\n9MZ/TgtDhh8S7kPo4vY0tHNSp1LXyt2UAYvv0iNNsrpRugnOc96kQrNGKch6\r\nSqgv5Xc9CVsepg8qE7h2ZyoJM2kab3NuDQrufh+PkMPg2DGP5cHRnDu66wRw\r\npUgPk8dXmFtJrTHoIP8K865ig7OQ2j7O+KefJVlw5gjdpyJtL1IONVTnlQIA\r\njAME9Z4bJ+ItBtqZwTf8uEocJ5GKbsB3nmCCECfUyG8RCWKkiSroyMipEDw/\r\nTAi2quhd4YB3hjTwF/zINUYKs5lmZefh+nw=\r\n=70Xz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.5", "@jest/types": "^28.0.0-alpha.5", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.5_1645736241454_0.580876320923059", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/console", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9583dd7a750804d60ae96c046befc23f0780ecb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.6.tgz", "fileCount": 9, "integrity": "sha512-OoYGmIbE5hmbAyeLJsgAf5x6a0zn2XafBQ570fw8MIhyrsL7ec2jWMWjZcmsklgEorF4rfX2SMcZj1SA8elciw==", "signatures": [{"sig": "MEQCIBGWtgd5cH4tfHAaQ+5ZXkOdgLJGCNnFO8zDabi8/tmHAiA8edCmFictwbFPMAgxfvMEdFgRaMrf+A/cYmuqBXI6Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2LQ/9EoZTKSOX4DNytlc8Vj96HfpLNUKoZy8/Nj3QaLdHfDACo+6+\r\nAPnNX3P/KdZJS+iM8KsPzjIJvDyMrXHtODOYhekAV83zhafMzmQytMyBqon2\r\nDx7+WmGTWvabj9dMDH5sRwUig+EFAKMVR43BQ1hnATgxN1XqLzYYyXH/NuAs\r\n9EUMky6gFpKy5WPT8y6jqyr9osq96sjsVN5svnuaRgQd7QYgKyeeYPTHOPHr\r\ngBeIcq2lOZw62nKd9XGv0YUlzIlO6sFhB8NoaeBNJ3AFHuR54hkYRfN+HjJq\r\ny8ZxQP3RyNve10Elt4ZIJSuJx8MTXpvjxoHAQSc51hEZgBqYAeGsRSIhM+EC\r\nVHe5d2v6AccO0EXmIIO60oVWvDfbbvSiXwxM+PN7Hmijpgv3B6g6qjpICNsy\r\nBnyg8dftJAtiYTjhSJEsSa6PfePP+wuN6mR4BIVGgAjkA+DXaf558XGllajl\r\nOGti+foZ+E+mhWs7Fuogv6NeC8ccLM4gURp7h2KMGvDyJr5KqtaGFGBprWYK\r\npys9hP1KiskDb5tzSfYQ5WolKm7CMXbpV1u21VXUgpTnsPV9bpzstiTsoT63\r\negmOGmC5IEDDqyfRBkRmtR0zLcDxJ11hnyjrWtCqPPxTE7m+JYEFZuDPyvkJ\r\nKBUP/6XSEeIYfHFGDm09r15HE41cs7sZ90g=\r\n=Pxzn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.6", "@jest/types": "^28.0.0-alpha.6", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.6_1646123546395_0.3718304502223193", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/console", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d77531c79237f3c17eb25cbbd261f4bed6cdb4fe", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.7.tgz", "fileCount": 9, "integrity": "sha512-QoGYyOukBfAaPi+H0r9a6c590yb7hokOyRiwAPPZ6fjrn28MKvhqXpXnDckEn1u5Ljca6c3FgcrQ/9+IywtDCw==", "signatures": [{"sig": "MEQCIFQGgzhVENxbaQhoUNCXtJw1GuGxkLyMvTUEEAv9EfosAiAI/ymgYQd3cqNVlHLw9PmmA7a6r35dZ60uU/U9HgfzAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWtQ/9H94+ZQbg3sLB822d/hE4K59cAG0CxdK9DNbxncXfQdgG9O2k\r\nLdSLnIslC74qAXgjIoxNy/QNx0adBebBqO5SceHd0vWapYN/0k/Ji3S4kyJp\r\ncHD/lUu5HGnyKwbjrQFGMFb9notvPCH3krDScsXSMmZ/d3cEeGY9/Z5T2x/7\r\nwTc/QtFCdZDIHzn+IDBaiqyjDYdJKqjGuhbKO2+VcDIb3lkqmeezr4VG0Jh3\r\nGx52F4RkLdE9hhcDCQi2OuOy+omFct/MsTbfzVc/TUmNn4Orxj1SWFijhh68\r\nuC3zpdBv7q2K0Ti/uKyfDcxDW7ZzWQ4OKr6mbI90QgU7g26OQqvesh5a5K4U\r\nq82PVU4QKiquAAx+uT1kxp+l3Cd4+lnYtdc780ZEmc5zIjaaFIz3j3uGQi57\r\nbWuTP/N8k9rFziwMOLoYE5WlapBvDT+ydGFf3jBiekvYlhdTxfUE3+bkFBF0\r\nzhJ00wL7TrX40BY2H/L0HHwMn/UOsGAMOc1SlTrCphlyics0KhSzz5kmhyma\r\n2KD5OIozwhU6o3N3SPeCQu5hA8heSwa6n9FxXYq36JEu6Qf81a8Ol0fWzLI1\r\nhQPn9YpoKdH2Gw0GREgPYyq6oDRsslEDB4fAf4UJ9Ac079Z0Kbker+ikl1Uw\r\nEV3pqY8K5jymQ+Xm+sA8+4scrlHCBlrLLnE=\r\n=JDly\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.7", "@jest/types": "^28.0.0-alpha.7", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.7_1646560962938_0.7283190764225385", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/console", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8b7967c9cd19dfc62a98aed335fa9940e01aa65", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.8.tgz", "fileCount": 9, "integrity": "sha512-ixkP9Ve5zsIoN+YOcYUgmCWJkBHaj1PsfyVmG13ZL9KrnVi6u9wGshc5cz/p0WTB1N+YDDTLTqLzQtGRnNUdKg==", "signatures": [{"sig": "MEYCIQDFFO8BV/h5Od2DL7btkvMVjH0/4W7LZtplLdlyVoLJbwIhAO/n8w2CtA71UuyXtgZtSJuuIdX4mSflLE4iF4mDNFVN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7RQ//RUU2bIyZr7PSwKY1wAttb9vwyiUy/lD0HnOQSEE1GLg+QJCX\r\nnCsaSTtqcZ/8QZHvgf9IxskwS4ArTDPT9YcuNEXEnmqcbK+5QSF04iI6cBuP\r\n6hSe4JgrEm3PDXE/Ld8868YRv1NBtcUk3jFOr/dfhrdTv5fsbTSH/s0MHDoZ\r\njNHzrJnvHvAHpCZ3AIL3Uj767GlSIYtHAltPMoXc5SJlMcD/ivMNxV45RE5Y\r\nZWVoWNMs/KPsZL2QgAEqJ9gZ4s0Qx8k7oO/E+W8yNClDBvhitg+AlbgTP0Xa\r\nvb0JY+cEt8YAeJOnguofNrSyPppSCQUmIdOzvVojfbZ755CcX/nh/c70+24J\r\ndPkfrxRgEYh6PgDvZQiNW7pYhM7NAAOE2FC2th6q6zKRQSHwYqX36Zj5BN2/\r\ne4ZfqxHmSqoWJcCc7xHVXXpjwsJM/xF4wv2DoEJTjrnq9NlF6MbVpZs2KyI2\r\nryj/zqx8ixghwOOa8rZmZV6ZoYcyL2If+b+tJIFvG3HSn6aDyLm8ZePAPdyh\r\nEgV9YASEIf9xMmTBiXCNUiBHB+izCTlJOaGACeuQZGNd5JrZ4u3xyq0TVeh+\r\nMnbfMuccbqiTvAmhtwUBZrZazXV7cSGxfPUJQMSvSUxR1rrx5MbrO7JD+GqK\r\n6uiICji42Bb3MWaDRb3EmdOaBW9LyB171kQ=\r\n=mOTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.8", "@jest/types": "^28.0.0-alpha.8", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@jest/test-utils": "^28.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.8_1649170793697_0.1861429141631843", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/console", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/console@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e82825ad06100af036aa569092887c8d8dd0fc3", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0-alpha.9.tgz", "fileCount": 9, "integrity": "sha512-RG57d+pu6qEM3cl8gWXfrR6YqmwC/n8ep/QA9kCpuTUAeVebE90//XhV0IL9r7iHlnUOESwLejGGneP8O3i86g==", "signatures": [{"sig": "MEUCIEQNhTKvdcDqw+4oYf1WfoIdmKTpiUiLa6VmDeep1tFAAiEAlFGis+O3m7dl9hcyP2cnxOG48xbqMThNFAoDMZfGYnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCEw//YnWQdRR7xqvZkJ3IeNFU+opl6L9dc59gCMEp+NhtbkYHoZqF\r\nC8w7YKW5Ukv0CaROPuaVS3eO5DQELMSzVb6T+U3Nrkcd95thknxYDiabwrBH\r\nr6RiFGOw5z7wegaYPjI6HiyBCplfm0CbNxQopcdDWgQ/jOjFGuGlOofEmVvL\r\nRQfCcIAgVdntp97C0oQljJMnUHEwxfdTAbOvr4ldg44z7N1CfP3aSnMRX91b\r\n0shbx19qrGegaJrc0lMTk4usEe+L4moqbYSNuyGpLlY2z+aZPc1G3/nJtRDM\r\n6mjHUjWMBdK9VVRZgrj4GPQV+1EvCST+HQBw+EURhXsCQlw4XqvIWLCPAlR+\r\n+4pv026hJwlfXQTi49oeFMkw8rtQBNGbKQxHT6sl+lEMoT1/1Xr2BeKBQw4r\r\nPhYphx3xWfQRrlwcKLg8EP4wkJCrzzZElBiA8Fga7kVOjVMNetv542+fVCm8\r\nkDRFCGzZuoPQ84vLY7nrv6qDrLBlqkQgawtHber/qX1NPrQLZYzdzuAYpu9u\r\noQvQseVk5UxH84JQkTlV3eJpkJiEObRJ6ZUUFK6Bc+X01qwX8mK+NroRMtXa\r\nI/zp+Px9y7jN6y+2JAheKT1yT+khRTW4/sK7WOMLLYgPqzXLiXrjBCVaJp65\r\nDGHXFY+aQLuF9hRxSBOlreEWYTULmUI3FV0=\r\n=f3Un\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0-alpha.9", "@jest/types": "^28.0.0-alpha.9", "@types/node": "*", "jest-message-util": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0-alpha.9_1650365956452_0.9403126191101545", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/console", "version": "28.0.0", "license": "MIT", "_id": "@jest/console@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01a2b9452a2ade0ecc4e0304e6f6284faf2ebfa7", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.0.tgz", "fileCount": 9, "integrity": "sha512-LXXHbaVzluR26JGHz1iBYt32KM4+795/BFzDDoNuVDNFTcUANofye+zNl5Uzs/MfY2oFB7Zw+nJHpXEb1OGwpQ==", "signatures": [{"sig": "MEYCIQD2ou7MRakaMM9swSPpvLBvb7KxXA/qI6X6j7YbN3YdpwIhAMNix1J0MFMIjdDJ4G9bFEP7tPqCQuMrXs78U7yN0OOB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcvQ//TC09OWn8dXZanknhdHPLzF6JUR+D6y3Jz8y+BKoihSp75U7S\r\nYIcMJEq1kUcyNuHYkMGU63Tih18kCTH2uxcvCWkY97HRoJ37L9YPkhyGCB1x\r\nu5OjByk/XByvyxH4QZOy5ZPECnbw6rvCZY5YM7+s+obJmG6LSaznUHsFdgm9\r\nTTOkqS/lodC/elaWT+/IpCfkRUEPKiF7T4KVaTrKvZ4CV5QBA4TpJbt8zXYS\r\nnt67xdUQEZDCiQnkkIgT4El4ZRe3tHVUFzqFBvf2HLsGhiBcadG4w7VSDR0F\r\nFGbV7GyLUH3RJ6QaShVR13OIg6J2rzdD0jIBliV1Y82TMPso4PpQiv3FJlm0\r\nyq8Eu40WWhMCTnJxMWv3sX60VEnqqBBb5LKsz62lF65BrKpYc1/Raq2Xon7Z\r\nMk8VrYxuB6Epf8tjh8oxZXtglSCW6gF6M2e/aNNQhDR0qdX0j/u1d515BwHi\r\n7M3jEiFyrlJktWi4RXV7mNa3SCbPmz0fgE03NuixLV7Lw4HfPqzqXNqprQmh\r\nzJs3Xmh1U4dIGCaFSuEozEgTtM0vnML+0RI8D1oUj5EjAEWk0rHmNW9h0ZUr\r\nS13IQBKjXd/9jaWaWJ0lqQK6v6KTM2OObZx7m02W9w6q99DNfxf7GQA2n8nv\r\nyJkb+RO6X/s/ezHBAzctGMGgFvDe1k0Try8=\r\n=/76F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.0", "@jest/types": "^28.0.0", "@types/node": "*", "jest-message-util": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.0_1650888491985_0.6685139982441095", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/console", "version": "28.0.1", "license": "MIT", "_id": "@jest/console@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4869cecbc5ffddbd03feaaeddcc73110d9e23f49", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.1.tgz", "fileCount": 9, "integrity": "sha512-c05/4ZS+1d/TM4svDxrsh+vbYUPC08C0zG/DWJgdv2rtkDgYHRfLtt9bSaWpSISE+NtqdRbnzbUtJeBXjTKyhQ==", "signatures": [{"sig": "MEQCIFY/brpHLamkq9ffU0BZxve+K2AInSgZw58NOO195xLDAiALmwHk7TkgKc78UuKApBUZKC0mBj6LGDlaNYhGrlByaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ+Q//a85go7TJxY3nGhx36VzlD6IFa/wvFx9WZuWTLZnNS4HQ9Rca\r\nCGWWn5TBT1KEBM+tNDejxeZKUDiZdgl9O05DW2TLtTJtIgnT8R2Rp13k35Gm\r\n+2l52yp88PnV0Aia/uf8DOzKCqw9ZGx6ULhqCA9iRBaLqU1cMdyZayXj0g3n\r\nNXOI2oe7RZC/eKLzXIYuoPjOwGCK3FJC6oEiIxOI6HXH3ctNIxUAAqg5Mkha\r\nhDhorJG7u+hgtFUktQLSQLknDKTL555hvSoRyJoMEUo25s8C9EtNZrVAf00J\r\nz99B1etjaMDvn+CNv3RIfQRWijN+kfAf8idlVnIePbvp3CYY5waydzONtHGw\r\nT0UWUEE5eb3kHMcI35HQfuxeMEFjIBd7XoiNavRrKdoOcmDz+x20aeZKNmVj\r\nGUzdZHVJ7nCRM9w6sNMON6czz/2tQBZoWVhvb/5F8gBq2XXHRccJp2M+D0yp\r\nELVqRj4dC+Zkg2HGnUygF9ld9zmo193J13IAnMa8AZw2/DZ6xrJeFTJeHPmi\r\nSqDn+xvCaByeL27j4Hw8CNB/vM77mzVicXtJWvKHQfyw0b2K9jEUC0HS6Xgq\r\nMzvA81rA9Zz+l/hiE9U4/KYesRuSSiL3WTe1StSTFTH04xJzkNbPBCEN/eQJ\r\nUXAxCuI4zlcQeKaCpG7b4hHGD+klPzFBKP8=\r\n=kg8t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.1", "@jest/types": "^28.0.1", "@types/node": "*", "jest-message-util": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.1_1650967361360_0.34269887843172153", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/console", "version": "28.0.2", "license": "MIT", "_id": "@jest/console@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d11e8b43ae431ae9b3112656848417ae4008fcad", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.0.2.tgz", "fileCount": 9, "integrity": "sha512-tiRpnMeeyQuuzgL5UNSeiqMwF8UOWPbAE5rzcu/1zyq4oPG2Ox6xm4YCOruwbp10F8odWc+XwVxTyGzMSLMqxA==", "signatures": [{"sig": "MEUCIG/MFySzTTBA20WXIXVkUBEmWXwEN92g3RAjyx2k1DXGAiEAoOgMW+XqsX5PRjByI0zZE6pnZCSIcdnJOpo0nsH6pCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPREACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrF1g/7Bn0PtWphIvq1fnsW89Zmb9Si92MrD9+A7sJePXHyAvFXtWY6\r\nOYqcdHd3MOtSnqmn6VURgiutZwxjR+8F0MFqTgX7mw1W3b9QmESHML6eO+80\r\nDAxG2OMX/9pQBooB6BEmcwHVv6Hx0yOMtWzErsWZM/Rn7unhbg4epOkqRuU6\r\nKrc9S7E+QBcOV0E6xmBOKI1fuOuHfv5AlYyX8rFPWpemiZSyF4aX6MqgHkU3\r\nkhwzWa55nPuFKEzr0golxR+P+ENGre0HuoK3Lop3ygNwa10DdsWJu2cYfbq9\r\nWhqW92zQqUeqIlkwbkVUDn1dMimDJXzcMM7iVpcvwrDdSIo5gxHig0layPRj\r\nrOWB5WTJzMJCqSMZfUpEXhogAVIdg2mGEYt+o+gw9xDVtxyJDm7gPq+ayu0/\r\nEBiku3qlgqCA2zyMNfdI8gYT7S7o+eN2ZDIGXm2MFAq1UmrJtHAgt/jFnn3f\r\nXIBCF6bZDzr8zz49IrW50N4jvRN4lsR2kYdwD5SoNRQHm9XjSMSQrDQeA269\r\nq6LPf7XJZiJalxsgVyobmE84W3wWtdfUrhKqZO7nXOP3VcGJ18S7HxWOgxx4\r\nPL2wfcWcgmoYq+bRt6NdweahrI1pANzhRp7+17mOkUWG8Zi8r7Ei8JQJaGuB\r\nttqmQFD6uqCfF3k6QZCptqfx7HpggPnivss=\r\n=+J5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.0.2", "@jest/types": "^28.0.2", "@types/node": "*", "jest-message-util": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.0.2_1651045444793_0.963277142299507", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/console", "version": "28.1.0", "license": "MIT", "_id": "@jest/console@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db78222c3d3b0c1db82f1b9de51094c2aaff2176", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.1.0.tgz", "fileCount": 9, "integrity": "sha512-tscn3dlJFGay47kb4qVruQg/XWlmvU0xp3EJOjzzY+sBaI+YgwKcvAmTcyYU7xEiLLIY5HCdWRooAL8dqkFlDA==", "signatures": [{"sig": "MEQCIAVSJ0NvZLy+Y3d/b9P8FS3eMdmb6a+pqYBVsRZ5dBZnAiBHJnKPRF1En+Zavjlg0wQZy5paAwMMN+xbxB5hLXMihQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBVg/9G9xPIe6nwRgfNolBlKcVMgR7RWArSDbj59Si0J4EvI8bleP8\r\niOqogC7O+f0CFwlkbi0Sf/EWTuWKhgsVThG6sXtKSTNeAALfPUtL4EPclCc2\r\nQ1/Olvnu6VqFEcyhCqfroyUvBP7+QKLULC7035Y0dAE3FtwOSBIu1KmY9Gdi\r\nt11YCUx2NHBPWVlS9dcSn4gEESfgspAfwUydQ41zoJbzRM8Bzd4HB+lWMDsN\r\nCNSdNFhDS9vVenwlw1RlswBwtiYb5wrsPWPAny7GiPUrOX+C/pqlJZ3lcuKa\r\no3klXFh+R19nRYAdLZOeKnbMIjqFnWv5ToEKRqWsg4pElvWBMOPyDzK5ZUl/\r\nyzVDKktnW8tTRhGzFVpnfX8hGFLrmOCGKk96c5HJX3cSFta0JlTeG28IDru1\r\nBkmijPm/g8NzL2/eVsXMqmumsonG+gIAA3SRoS1hsf1gl/WSsezUcg/IfOvA\r\nQGYeTSP2+Y2zkF62O8Lgw+8AWn+pKAy/L12G7CPlAKymt95pIlTecICa/13Y\r\naptVoAfuiOubDwycyvbkfLGkkmw2zRFQoytP6dkeeYpJqtXSgBGbdqMjQqp0\r\n5Mw4gMt+Q4OgF2pvaGQ7RvbyU0zstMM/jvFkNc98JHLHEBPL1ReG6zR5l1hx\r\nK3eIrFBfPTaZlGBC2iMgQpiPh44lE9+4o88=\r\n=A/4r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.1.0", "@jest/types": "^28.1.0", "@types/node": "*", "jest-message-util": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.1.0_1651834135851_0.939210979268583", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/console", "version": "28.1.1", "license": "MIT", "_id": "@jest/console@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "305f8ca50b6e70413839f54c0e002b60a0f2fd7d", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.1.1.tgz", "fileCount": 9, "integrity": "sha512-0RiUocPVFEm3WRMOStIHbRWllG6iW6E3/gUPnf4lkrVFyXIIDeCe+vlKeYyFOMhB2EPE6FLFCNADSOOQMaqvyA==", "signatures": [{"sig": "MEUCIQD/7o1IkEjYNiQfC2yEQroR8VfGrl9sUYHjE1a8ghmamwIgJv6m7It2zul1+0pacutYqbkrfE+MF5qnMCIT01YfZho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuuiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOlQ/9HNxCzhFTxgYQi1twiaR9H089zTaOBDxH6i66v53plOM22R88\r\nSRNuWSVx5NNR2/WCfYCaBqjB0l/k4EIzasfxFu3uh1YmtV7TjPAzcA9wScjO\r\nXbYd7eHF/kgPdFUtBLoi55qgwMbyvr6NaEkaJmgtsgrxV+Ye5ER60A5/kM9u\r\nsll1Se94rZ3aMu4BIQy2EIVm+T9xQH+2Eyw5WjJoE8WBJy+Kfub9c0+LLvSk\r\nlBf2K0DuajZ26K5dNMzznGJo08CR4PTEKl5j27gfVOAcEIb53vQCM93vzkVC\r\nLYxqZrTBqQsE0Kr29Vf0Nk1g1TDl/KsmVvM9gRxNKEOZygorpz+0bL+2RDW5\r\nby9tea863gTjn/cTahVLny8e8CfL5fIgj65LmXVwfky8AlMT2gAdwuVrqArd\r\nDBsfRpms6gw/s7geqHlj51A1xVFHtv8rCjYmnOga/DO9Q3NZJ2tIfBsVE7ZC\r\n4mwUCtR/5i3lDAHJAdfrBsa4yVItLTVHVPwCCCriVsmWm7pDGuQwK7KKxDxm\r\nKXMSyO03tk32cu5iPa7ibN7+hgczYMnd+OdHEN9s/lQTFe3H7Zp+56/Dm7KS\r\nKtSt1pRW3O39F49m4D6SijwOKec9IUm6b9ytyR2H4qdXxxiJ4RJ0bKQJFLR/\r\nM0ut5eTVm6mGPiWqDOkBQ2Zwaswm6/ezVmY=\r\n=vlV4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "jest-message-util": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.1.1_1654582178103_0.03466984645026239", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/console", "version": "28.1.3", "license": "MIT", "_id": "@jest/console@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2030606ec03a18c31803b8a36382762e447655df", "tarball": "https://registry.npmjs.org/@jest/console/-/console-28.1.3.tgz", "fileCount": 9, "integrity": "sha512-QPAkP5EwKdK/bxIr6C1I4Vs0rm2nHiANzj/Z5X2JQkrZo6IqvC4ldZ9K95tF0HdidhA8Bo6egxSzUFPYKcEXLw==", "signatures": [{"sig": "MEUCIGNNPblnsSxuMMmIQvfrXFLC+gcF97fEFFm560wejKl1AiEAyFcAG+vpgwvT5/1e211iYHNwoDEQ8Nrz4VazWCImJJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp55Q/9Fl3vCvJBAwZllfTegAbo5wpQ2+gziX+xwhdTAMOOScCofdOo\r\nXqx0vtH23jRlB8dnf0WV0AVVGX1j3/On315V81A+H8tDubgOggUC7fbf9rr0\r\nnAO4AZF8JDlWsxe5CG2TpXqyj1b7Ev0ZNgSOBPhsVn66JMCG6SFR8qD2LnDo\r\n4vm64ToYT6d8gPIc4RXDn9wPanG0Jqwpip7jpE+KIMnUN4Zzl5suMAE1BrBG\r\nZhzjiwJ8JfR04ai8KjzkEEr2Gsq4KEg795TOrS4sKmdqD5ULDhgDwXgjXu57\r\nDgDemEIcKgwMKflQDAsEAQljjxty3qXTJcXrCJzx5YO/4KOFL8fohh3fvdR6\r\npr6LqWQtp8CVX1QikbsaRCEXohc+3JhXSnvUoJ+BfZMsp1C112FfblTu5EEZ\r\nP7P4t1W/4xOM3E8HnLsYzcGzPha3Mz8IPrHtI0SBhCGwhxaU4CShNNSfThg5\r\nBZCAzsue+mUwNwU/ia2U26C+DSuP2sDUYosrJKhle8/i9btp7S9rdO9qUHuU\r\nwJFVaSoYYLjVAHouRevYWyLaX7sjuXCYp4P/Lv1bBXq8b5hm/19OzNLrsr9e\r\nmdRJBFk5D4uU0KQSQC1zceZrGIIIvEllnfu/vR8TG0LYR/5WNtdJzWdRJiCq\r\n46diCUdWZvMeodX3jBQ7drofxZL8uIU+zEE=\r\n=CH47\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "jest-message-util": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_28.1.3_1657721550259_0.044016422363481356", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/console", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/console@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f95a2d1d46ca7b57dc47897f07f24dc81fa8ce6a", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-nNuEOdQAjOkyYqGNzbNdE3Z/vUVGRTjLh1tPVnxDzkoiS3j8btDlf+EhsTYIu7u2C3t/2ZIp7MO6w8uByPLLcA==", "signatures": [{"sig": "MEQCIB7Hm9BL+Dp8dQ/t+15WwFbeTWciOpFGEs2He+D+oOgdAiB90c/1uzS4fG4YmRbO0vG0Z0w8WCpnTlJ5IAiL1JNIHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTWQ/9F7Z/YGAbH1vYRkltnKVstfkkkNmKl9sWP43d7Gl6fNNQsXyg\r\niSzu16D+7KCwc7QNfhd6LBacXrCmJrso38QKiXGRCP++HzIT7CaMXUn+dokn\r\nMVKWUhtzocuFIpG+cw20cGtENchXOHpH39yqCSt+kVDK6nbpgYLGYoM3lcP2\r\nIkAMLQcGE5BDkU24POnqPMcN41WFMc/TzsDtDBmV9A1u3KEsmuj8QbsFMfBp\r\n+OIOxHup2ae5m6dAHZTEM/eP9taumJkoCFGAn3KUl3KxoMLc0LDqDWIr2Yze\r\naCbERz95YLR6bwcQEIE9j1kOWIoeiiOR5ReEeV1vCggU4rx6kby9SrEzueBw\r\ncY/n9q4WbvzCqBFGmq0sJf3V7FZwpiFEp0+kaW04RuwRFj3iZEb6l+N8aMpQ\r\nBGLhiAKQTGPoG4kxGPZkTSQ9jo0NIZBhN/3zqF80YXgc/P4CdW1Y1eU53Mc7\r\n1cFKcKg3PHhQo4KItCpJ6nzL5xf2kfILbjdnIikAzXcxpVQVyi4xTnIA0K1I\r\nv7FV2t3Wrm3lRdWreoc0DYXDLPuQLbDS/v4/cl+yuXbxdXkcwu+TrZd/QbkE\r\nbPWnJiPiJTFtfnCQAaUquMNlP2vzZsu01HcL6qp1IN4Jl1+iHF1ngG8AibCw\r\nnEqYMbq72gIv+KeHXkzS+aNEeo6zcx5fRxI=\r\n=gqhg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0-alpha.0_1658095629208_0.9436421507366386", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/console", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/console@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e12a4672eb2762b2f01c69509ef81839eea29092", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-bugDCGYnIYcbghBQ9GawM6jai6lnIl5Zr6krQU5kqKFkty800KSZeL1uWr8RZbBq9i/sEk1NO6WA8mejoiCOWw==", "signatures": [{"sig": "MEUCIHGCuIlKC+20vfzo1+0SJLl7U6+ZlgebUCEuuUFRZYbzAiEAgw07wTk/jysTWvB+LagjR+lM1cv3+QCXkGB4/mj2V9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPTA/9HBYotdb7gDkzeAIrlo1a5dTyJ+GK07luSG4OnD5+HWvK8HPm\r\n08tD07iT7NZeXWDKFyHSZ448A9G4CDgGcbGAAWSg5eJkqgE8MNPbN9etI8GS\r\n8ZfqjuikBOpnuDr+ZTzWSsP438pPHTLRBB1WQvtimaGMDTybJj0ntOLY0sqN\r\nmgGMpFulfFPnOEx961/1eQF69i6NITYkBXCMUhDffo1fVgCMdHRdXok3DQiJ\r\nNAj68q4JIHuCDER09eTpVkNMN2r/6FYGBJZapLKQTJnOCduv0tRMCilA5WMh\r\n0MqYphuzJqxP3VTGhygxi+Q/7Y6SPkBa0dqiiMI7yR1iA68HzgmSalKzqUpD\r\nDyNFudrs+AJOTsdVC7L+dRszFv2w5m3/jyn6FbofYQEHUKgISFuKsatFVV5/\r\n6i6ke7tlnJpHctDs2UXbUSBuRy/yRnrJjKFubIB1rCPSfzNjkVB7z1xuiQZP\r\nNmif88Pv2fpCDBJyhODjUpJATZSrtH8fsVRFl0gT8KJN2aWppoc2PxE61+B0\r\n7TEDMKCQARmcrJ6hA9PHYQUH6dvM5LEZEJlgh/zfLlTR1zk3UrtK8ONf3KNv\r\n9q98R+7UzuctSeg3a4PnhIHxNvdBVHQSKKRhQadjqArHTFbQBUbv0RCAfsZj\r\nlheCq8f5OmYgPHEMqUpK/b7dp44iyOyoJFM=\r\n=mBUe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0-alpha.1_1659601411347_0.44341993696364446", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/console", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/console@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0db77fe8f59481e87ec661c634300c1f08557cc", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-vP9YLsmdr374gBkNfwE1ikgDSbnirjdrpMxDFn/hhWzBt9Ce/mh0p0nz5sy8IqMG5rfUWK7mZqGkCIxVSGARvw==", "signatures": [{"sig": "MEUCIG/3hsOJTN+z7nx2XKBg3XcjB2j7tU2hJnw7chKe/+IUAiEAzpux+u+hjM7TRdSSuh5EpIeayfnrKTT29w5YXD0kpZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ERACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqweQ/+J1Pg424AqPq4ZPNtGWeSYRQV+fN3ad95+MbQU5uNF9TMdb1Y\r\nie1mzdszvknk62bfKfQCgBuaS6t50lCDzTgN65yV/tGra2oo9Gxmo5dvY2oe\r\nljL+gTnS2apDT9uQFLhCPkRYsOYyLAHNXndeXXCIoO2jQfq2bI3RQmLnL1Bu\r\nyUsWWJFkInlew+Tdr3wCPmWosjGBPA/eY5x/RNZsRvRbVPbL30/6ArAsPjzX\r\nkvucdjyuke8P/d+xgndz1v8Z7xRoDYqRKmZskYGPEpGCPIiH4an9MxfpPB1J\r\nmr0SfgrEB3E5gcg8dyCH8gZOu5wFHYVhL1LYZQaK9NQ4VFRQ3roOZT/yIsk/\r\nqMXxG3qiyameOpyydrmT6fQfI9mRg1VjDNCjibHIomH1J+YHYzlKrinnQcMG\r\nhyvTZY4Gx7r6OPKogmy7fYXVaVu4jyb7oim2FqEm/0yexQp+ZVC6hqsYqz/u\r\nT6ezx2dvDffOXIuEHRqjPCUW70V+nvU6tvZoWffxNCDVnxx/rXO9STcfyJhO\r\n7PSnujl0vZ57aNPpOspwuURlClkjrR1B+O4V2+vOrSJrQYCwIIfqByur9FZy\r\nUH8obNCqw7p3kgNbUykTs/ySmh7NqRtODmkYC3L6TlpjVIJZDblOMmLG9SUn\r\nb71wiAK5QS9LqXywIftmfNL9rJjZBz9XXJw=\r\n=537R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0-alpha.3", "@jest/types": "^29.0.0-alpha.3", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0-alpha.3_1659879697405_0.7450879470157101", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/console", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/console@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "38882bd93a19f324a0f48e7f72b74bc455f36ba9", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-kH2ha7n6De0AwnFAQBvIRYrzGFR6AKcAh+Hh7m+kQ7vCK+++5y2nA1hZtYlLqybZ4COIafUmYB7nnH/5CO//7Q==", "signatures": [{"sig": "MEQCIHU66DLG7csbgyYbCCmpK5FZeMmvZyxAt68TzqUU/M20AiB5Do8w8VNnrHXzSJFzKMBaKr+qqXYgJvEHPfFB3rPKag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QogACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpcjw/+NKBvSoE1iONMRC2xDKp7BhWl8F53sHu12xPC9tk9ijP/h8zb\r\nGMrcdEPaF22daSygukeLJfMHnoyHzcm4DOFm0WP/o0KwuoC3XUzcNc5Hmei2\r\nB0RM3hROfAJkOZxblEhUV1PriBhr3+bpZ1IweYWh/x+89WjHl5pA9iKK/NSq\r\nYHk06PKopfsiRw6vvyiUiuC3VJVmrqL1L3aC5MbmJ8Xzj51UPsAoRdvKAx+7\r\nWVnxPPGrYHTpA6Q+GZc8U8UNkV0JotKVyDOy0JupvvbBZkSbobeQKhYXQwSu\r\nyTZJKqvQlKDPIqqn/DXHF7J77BnhxaewrqkGgIDScxyHDnAcpx5beqL8M54m\r\neLiFF+FR4hjmMEsM7grNf7XQ3bzOU6zCL1aV6nX9vZAOoKOsB+wUPs6ADv8U\r\ngmYbAysxzHxgeKA31FYTzgI0oIDX1ojFSDu2+t4t82aHNC3ewcIU1kIk0fnP\r\nAgYwGmQl7Zp/8RdzRUPt8wwluW4snOpQZqxAGUR4wpzAjGFUilizU8WyrjM2\r\nLp47Ps2NKLhvj2oTDCfq9vnuxww+aJX5RhPEg5+LQencGjle/riDkkwfQdgX\r\nAhZm8LzeKoP7qEFPzZTQrxDOI2fF4nQtZRH3GzyXYlGBCZRBkjSxXOcs4UkF\r\nUxeaSNcs3835aJMl2Bn7OOyC8+m8gO2xcNU=\r\n=/FyJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0-alpha.4", "@jest/types": "^29.0.0-alpha.4", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0-alpha.4_1659963935903_0.518876558622122", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/console", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/console@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "31b33070497e0412f7056e0a237fcaf55a0cfcc9", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0-alpha.6.tgz", "fileCount": 14, "integrity": "sha512-3xHPg3zl24Hivz4ChwQWYhva4DvEEGRnOE7p4kpvzYfTpwRZhhJWqevalbKIUJsgrKH2cS/Mdcqlu539G426AQ==", "signatures": [{"sig": "MEUCIBheAbXKOhVxQaFlwGHIIatVYCtzPXnJqfaQ+XPUvPAMAiEA/7iADUymnvslIMAGQYT7hFgZGcwf/PMoj8/s0rZwpXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5beACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJnA//eVK2jyzwaBEb32k6bdgjWTb1/DZAAf2K6jcC9s8e8kjXa0T7\r\nv5u/cBRaNTz98KAphsRt4ka4tLUWannKdbB8bniOOYCkY1rixVhTGoMkyFJQ\r\nIIUMR6ELC48dAbWErlgX+hdf9FvUTDzmuEwUVkx4mzd9WOIkDwcpnDUsSy9z\r\nhJowcXQqAQaGHGBqYHglE91ctf20Cq9d3QMnz9cP1KX44eI6xnAF4OoKFQTL\r\nRAr+akKJJ7rf7zs0qnS0dQid/pPYbHhyJz1Bj+pqmm/6+KO4BxbFifMkBLEQ\r\nYcx/wKG24YK8Wc9DPHn5aULYqlccNHgByMJjC9Cqc4sL+LVuzPO76kmKQ4Q4\r\n1kACqCtIhQEbCy8pNhuEWomhXI8XHCAIhahZJyxhWASvBZmK2hJybC9DHtyD\r\nVGON3lGz3Ne2usUfztDj0LJSP7d3IRbWtWu2U50TWNvlEAN51mtF2ixa+EqL\r\nzyN+CU6ypDyc+ZbFonkZsYz0NH3Dew6AdxlyWubze4W1tcrhJgGSPt9uJVLn\r\nMdD3bCtpgXDO34kzx9fUMsJDh9aUtQDgPJtXwUaMJh3n0/VbuOvoTbvTHaXT\r\nG6SdnMYhVz5JqiA0jkXaHo6c59uULbsmR5Yw4V8t5xwmikuL5I1VxKtgY5cI\r\ndN5ZJJBclZoACNFj5ZzXANLkKe0k1lQniIA=\r\n=k3IF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0-alpha.6", "@jest/types": "^29.0.0-alpha.6", "@types/node": "*", "jest-message-util": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0-alpha.6_1660917470586_0.8612162101650511", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/console", "version": "29.0.0", "license": "MIT", "_id": "@jest/console@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1e49fcbff7d20c88a6747f27e2fec0e759e405ca", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.0.tgz", "fileCount": 14, "integrity": "sha512-rHsKEqT2Kx73PqO9qIOdwg0Grd6Y3COyqNpi5SKRI0qXgmlqXkOczQMfIb8I0Gdnc9/kaMj6cTnBGLyBA03Xrg==", "signatures": [{"sig": "MEYCIQDJYTwwJMLgrVgEy10pZvfNInjqhVZ7UlOeLenZ5H3yewIhAKjnD/a9iqUcr+OI+j7Med6bD8U34OCdrZKoO0eR/8FG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFvg/9HWmthH9ORXeSL9zOTkaVB7QoddEvpf8iesz71p8f9RebTyYq\r\nuqeXOgW595Bzwhx/Im7xc8w5vKBxUbsjH+XVXKxi6kxE0i2Nhj7L5dHhqj/S\r\nZKMO93+MlkTh4bkuvqPiUx+b/SIUIO+PQNuwGRznTL8t/BUDe7D/T/Ey0SGB\r\nbuWbM1/VtHMDRdGDDia++f8Cgl0zbAHlx8fEqNLohAXIgoa2Sm8Ys6EIu1hn\r\nCs75SX4vOw+AH0sOdg9OzQ8HovjBN7wfgiBQxZmFlD75dga9R+IMy6qqqE0z\r\noFjHsEWLZWslMYzLoQAq9v8WxIOMJl84yRG5JkdNkDK26g5OzrKC7vDcbGZB\r\nIRMM0fPz2D93QDEnODHI0KKXixpyT9gA25PcCMAqI+1VtoOWl1BxLMBlLt36\r\ny/tcRluAfNTW6h3QdymQd5J//oKuzcSSh0bHg46mmsVWflx/p6ZyMAbeCHeo\r\nkl1JNouv/E/JvW+yGRcDdejQkcPM1LnkfN9jrBJAk53cyP+kh0vEFLX2wjnB\r\naNpl81vDnGBrvN6V8ByI9hn/gLyLDoL+sKp4QZEEo9f0g7DpXFOlNoxgww2m\r\nVDZGu6YfYLCbb7u2NGu03SRKHKkN2EUmd/yJSIAsMZGp68LJywdvac6SrKKs\r\nmZIlnimfPPbkban/QQaWNmU2tYvqUzQFTgQ=\r\n=vppa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.0", "@jest/types": "^29.0.0", "@types/node": "*", "jest-message-util": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.0_1661430809468_0.6413443870443338", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/console", "version": "29.0.1", "license": "MIT", "_id": "@jest/console@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0e429cfc89900e3a46ce27f493bf488395ade39", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.1.tgz", "fileCount": 9, "integrity": "sha512-SxLvSKf9gk4Rvt3p2KRQWVQ3sVj7S37rjlCHwp2+xNcRO/X+Uw0idbkfOtciUpjghHIxyggqcrrKhThQ+vClLQ==", "signatures": [{"sig": "MEUCIGWjySBYQvR3rXlrYxBU8aQxAL7/sAqqJLQVTrLwcuqYAiEA0EUHzUNBEy1TUrilPKlzG1Ae3uQoZLx21ZGRCG4cFYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMvzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEAQ/+MAjyD6j6mWcXmhGn5+0RlqKGbrn0xG6uhFoijB0lNzFZEp/N\r\nun+O7B4sxT+mJYugWFq5RkOHpGNko67eGh6g3VOYHltzUOm+DvVsMeEbpdfD\r\ngeOZx2CEg91D2xD7f97T2+JxoEkAtbaznxk6enYuZvyoBusm/cfUe60D2zNv\r\nB8atvlsA95Q6/jgALUr1VrauOI4xw+R6Upcjgv0PJRKcXwkQjtMoFGUYii01\r\n1Ed4zYCUlLe7Tb7vrrIg1y2EaGRc/eyJOq4MzRWuxRTDtxB/B+UnZKRiCHt5\r\n5fes3n5/eo5c6hzPYXT+NELYaFzNoOPPku9zauD5+batNr4Gh1fwXkmythb6\r\nGqp3N9EI2jmubIxhpX2BFPb9/uDlqDErOt3mNqoO9CuSrIPULUcT5PmNhMTW\r\nuVWPog40S9ipHZTFttMWixSoxZhpY1qTAj3wYVfYeYiH93PTs0cPvE+AxhCU\r\nFEBsYi390OG2BRn44TVMPMrOgVEBdFfrCSpwh1tlA5Ow8HIG84wlT8tl+Y9A\r\nDMKcAuJzmSXYzD+nQ8ZPUNjgE4tWeVls04S9tnVV73tT+YdCCfp4cYmuLaVQ\r\nKa7xtHptbHoI0roCDYBEUhaPtQQZ07NI3Px9vwkoPcQcOuRpS6l5XMDWbrQH\r\nDKtWS/A9XlBXNPVauV2mQbFNcKtnPxkWts0=\r\n=wY9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.1", "@jest/types": "^29.0.1", "@types/node": "*", "jest-message-util": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.1_1661520883038_0.18721430325260413", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/console", "version": "29.0.2", "license": "MIT", "_id": "@jest/console@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a02dccad4dd37c25fd30013df67ec50998402ce", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.2.tgz", "fileCount": 9, "integrity": "sha512-Fv02ijyhF4D/Wb3DvZO3iBJQz5DnzpJEIDBDbvje8Em099N889tNMUnBw7SalmSuOI+NflNG40RA1iK71kImPw==", "signatures": [{"sig": "MEUCIQCFhSyyw/xk+33vPEbdj068bH+ippJGisLkLBWMXvkwrAIgZjsvtfrMPRYAENoZfxVPWNoNtWsD/ywJxYTEIMxBLSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq+g/+NwevH/tgTG9RfojkSpqdNGSr3mVKN7tyBMq1cwkxFYtWQp6h\r\n+M+35XL7HjKVflBjB8guct/cd3IHl569md1fTLXKzNbdQV5ucv4gkOBIEUxg\r\nM68WPjqzZkGUmY6MjJ/hXb/c2bvvLuNQnGDCpnXotSUC75qwhA79rawk3n52\r\nhTlvt9TySyDkbx1RKqAew6nUfMHSAS+3wZrfNlsqU0RstezfQYnydNu2FtKR\r\ndmQ47JoXXS4TvQOt6GBQBVchONBuLZO1k93Ef0id1MIKR0uSGjVDKpBrkhRz\r\nAmE2iHIJLmrReDwYd7+GfFPPW3cqSPtL4jdv+C2y6YjR69PKNtzfy23NGvq3\r\n0FlU7qbp1vmAM1mDFkc6aFl6coQrflREVG/fspIp9ax7E6KwX+F4CnfdBrRA\r\ns9LoEgOkQkKxUIp83s/Jdxf5mfMeAZ1+78vlh5JERuHu6Q//JQ3VAUOmvZT7\r\niJahU4dlOzRTRC5yE3vlajco4dmsOWfwWF9jbCZ/VxPbZ3bUkH5KkulyueBs\r\n1RZayr8rpJMj53BLl/qabR5k5g27uT2aKFclFc2YyZ2H2CDKcMOjN+5alp5X\r\nWi5bEdmBkwGOYDuVX8yjJfd4NKB90qjGwm+gYuApBa4sg5fbkoGq7ydYX+/8\r\nC9fYoGRn7O95mLZNB6Mfv+wg4SvS8dRFm7Q=\r\n=VLXo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.2", "@jest/types": "^29.0.2", "@types/node": "*", "jest-message-util": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.2_1662202100891_0.8062972146412848", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/console", "version": "29.0.3", "license": "MIT", "_id": "@jest/console@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a222ab87e399317a89db88a58eaec289519e807a", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.0.3.tgz", "fileCount": 9, "integrity": "sha512-cGg0r+klVHSYnfE977S9wmpuQ9L+iYuYgL+5bPXiUlUynLLYunRxswEmhBzvrSKGof5AKiHuTTmUKAqRcDY9dg==", "signatures": [{"sig": "MEUCIGYUK6X5kC1cCR9bCkb6CL5N4jzex7rTH6J7nlhrMFJWAiEAioJXdvLm3VznCamw8RDiPv+ClgY5mHY8c+T2ZBkrGlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGOQ/+NTt35XbtSwjHLoBlbtYP/4mPp3cCWkIs2njwrVKF2Tq+YuHc\r\npQgP4ZrFr15u9yM3aOjNPKSdiqiTdfgML1Rb/6E8bXnpDyINW/v0UOrFa3u/\r\nRHvnrQRZMzTjUD8OvEM2zdsyD2jzL5srWSppm3h/b/JhMtusUxC8cBQDopbe\r\ngRYo+Gm2k2el+y5E+ZkEffr2XEQb7QjZ2RXorBGrIaKaekZO3AUGZJ48Vn+s\r\ny8dhO0rYTMC+vU5gIF0jw2DVvYEVfcvVcHs3WHGsv3yisfLyo/i0yoOgNtZH\r\nMo8oh7H7aELHl8wz9ANCpAcqvlCswcoADfbXtCCsq9QY/N9WkYF1Io0WvSZA\r\n6slcKdCCKIvv6RZLP5y79QneXOFP22aLiaHuIVOZYkR7zMBnyCjRptvQNL41\r\ntKtesOLToMCU18WGzNd727G62qfaaEgKFaw2MODjYgCsJm91Q/E90gMoJD6E\r\nS+1S/cyTyPlOhEYTlSF6zbnv28T0NxaFoatx5kS4TYJNmYNuHXMKgL8t7ZOF\r\nQ+NQw0RqDPXJLtlEJ7Rr5puGGIyop07UUHmX/U/REv8VbFrIMuddltOk8ZQb\r\n6O1uBESWEnNs2fC7fP1gZdK2uoRa2pA/ys2qrsQNJ3zmCmcy4AbR77wXjJxS\r\ncbSL5b74STVjxBHwvWRduXAGlg6//QxmRUo=\r\n=Z4W1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.0.3", "@jest/types": "^29.0.3", "@types/node": "*", "jest-message-util": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.0.3_1662820899327_0.7797856942540908", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/console", "version": "29.1.0", "license": "MIT", "_id": "@jest/console@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3b8e9e86aef28af15fcadfd8df7321ed1327c3e7", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.1.0.tgz", "fileCount": 9, "integrity": "sha512-yNoFMuAsXTP8OyweaMaIoa6Px6rJkbbG7HtgYKGP3CY7lE7ADRA0Fn5ad9O+KefKcaf6W9rywKpCWOw21WMsAw==", "signatures": [{"sig": "MEYCIQDA9LIgxF0uY+5Ne/8hiYmvJ9PWynqHAy5Pmisrx469YQIhAMQ5ui3bJhnjN085WOzcuvGzPujlP626Vc8JYHsv0QCw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyOA/+MEIi3CZkhiRprc5PBTdrnNaxh9cEsthA93jUjrhd3/Kfg5ls\r\nULFGtjzhdCNjRZPx6YFgalPzJ0ne/Iq3ongAhKJRauyocHEuGfLS3iyMmmQE\r\n+1g+wjHZzMyh+i+OJrliWzammmPRjogWY+ayNYXy7XRyxBh/Z+Fi7htpMKmN\r\nQub5hjvBtSWYC/Q4O6bTSq/44csd3GoycYeMKlowJLjLaeIumWX1IoouxAUA\r\nxx2gdiQfwsJwqslLHmpupqoG7mQAQHS7KP2IMhHGz2QXxTwi5FeHQ0tHUqk1\r\nagEaKnfSWcSWWS4liU7o5qMpznz0nJ2QdeEQLNFKtR3PUoUwl6xlmFwL6/yy\r\nH3GKA3y84My/mRmFh4a6Z1v5QygCEhQ91IonskCvZQ79lpdkS+iB8XaHfJf+\r\n9FVCifYxIshxc/0MSH4mULMaUZLm0EyeI4wRkEb86kS1/bImHBC2ZeQfjnGv\r\nVkgy5LFjIKtp3vFsYI1gX85kmirq0KFNXHbYQEO5XhKW7VDzQSPgGeu1215F\r\n3hLgmO1yOUXWg65SN+/wM5r/iz2c+bU39pCxXHIggm9Rtto07dE/LoJP4KSI\r\ndmmCa4fv0DHJjYNBLjPCPz6j1m2rEfGAU36TzAcnVLpKchQgvIEo+5oi5mba\r\n84ay984vuYTLeNjrtjShFYQmIi33RmTkp2U=\r\n=wMz3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.1.0", "@jest/types": "^29.1.0", "@types/node": "*", "jest-message-util": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.1.0_1664350662469_0.6061177909763062", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/console", "version": "29.1.2", "license": "MIT", "_id": "@jest/console@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ae975a70004696f8320490fcaa1a4152f7b62e4", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.1.2.tgz", "fileCount": 9, "integrity": "sha512-ujEBCcYs82BTmRxqfHMQggSlkUZP63AE5YEaTPj7eFyJOzukkTorstOUC7L6nE3w5SYadGVAnTsQ/ZjTGL0qYQ==", "signatures": [{"sig": "MEYCIQDuvPI2XGhYZilJWRO0O62VFkxOZ33LZ2F2k56o31EuXQIhANQkbMrUT52KG8tRMZG0dyc66QktAZNI+KA3o4bgeWsW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrvug/+Jp8U8gVp6NwDI3JT9OgXj7oIYcwQNsGovT0ZLJLJA/Ukgcxe\r\nu/R7a3VYJJrRR2RLMthEzXMxLg4DhxGPVW4yWgWU7BUOEbOLp1S03Sfv23xV\r\noVvUEOhsy4NB6jf5mP+rw0/U4REv1xNXFhsROu8TeSY2qyXr3GwiVlipdU20\r\nnNGI7BexdYvrowcYu9TpwrRwJUvh7b1Tta39sWndV0vY6ZKyrVzJnRJ5S+Ca\r\nKJw3YZi02stGv19H1navA6baFKayNnK5T2o425unH9EbeTlXD1BxQWD7loWG\r\naj4+tT4UwvE7kDr8G43beO1FkKa8JgEh0DopU2GycWE2CWSAX+OJD4Cdortk\r\nQDas8oTCx6kNJHoTdnDLevTeBOrr+5hKezfzYR7qwUg6Qhm+G7Vlhbod2xjD\r\nBuxAp6OBmx/PcoEr9B76Omh1rCThgXkrfhYJKrMRgQEx7UM2GIXqv2cEkhhT\r\n4/84Ks2TsDxOnU0SDW7pqtqCVL/Uwn3qiLUaf9DN4CIcF/tVvcSgN3ucJBFz\r\ncUEzdY6ZDJvMhbUgdkg2u4eJrT/IDRvv2SQuS1sHTgXQ02mnb9RqQ9xcP2TQ\r\nqFl/QQ7PPlYWWKZuwvWNlW7pvafcHpRh/EAAxWc4Gwxu7z5+LXzUhqjOml86\r\ngAhYNmnNmTc4fAi/Uz2IfySW3uDBAVeMpck=\r\n=NO7I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.1.2", "@jest/types": "^29.1.2", "@types/node": "*", "jest-message-util": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.1.2_1664522570066_0.7105830335323269", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/console", "version": "29.2.0", "license": "MIT", "_id": "@jest/console@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e906bdbfc83baf79590f05b515dad900b3b71fed", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.2.0.tgz", "fileCount": 9, "integrity": "sha512-Xz1Wu+ZZxcB3RS8U3HdkFxlRJ7kLXI/by9X7d2/gvseIWPwYu/c1EsYy77cB5iyyHGOy3whS2HycjcuzIF4Jow==", "signatures": [{"sig": "MEYCIQDXV85og82MBhpPSD8oNXTa3qaFumRlecOyPCaUoQhpsgIhAMya59DM76QIYjWechQq06eF2plZXgsEcPVwhkC/uiAG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUiQ/+K6BwWpSf81BhpF7g4nAjv36l3e0TA6Zif6aRjHuTrecwJhOS\r\n9NNhwvjyJpjUefGoruLYSwFW8IQ4/vOKQDsU9aeZTTYfwSXh/JNvh+kLnyXn\r\nMZC9Uks6L5O+2UBKxEk54uScHAgO8iSjFGW+akxQJmdU9JBH+x4dtWqj03Y/\r\nJU2pE+m+hVgY1uMvTNobefkjawGNsFbVEb13NiC3oy1JC8g8xdMIkqj4L6xi\r\nVxUMN4bZVEywGHL2zeA2c8laiDO02BCitmo0vLwitjW09nSvl23TLzM01Zr6\r\nStvfS2eeA3eIDI1M9VWnQWg7q7SK4jRsoCuWlP9gkcWSwYBf9WMxpcGA93F4\r\nxSVL98v2ewvWwTAX4W9GmMFctKYzCtbns9VKb50leKNU+VdOOiywH7z2COwc\r\nz1i12bOKkMVPbFVUXCA8uE8AQ+YOZQBz16bstIBvGQRTW9BEG23tj10yQNhm\r\nKEw46BHR5Lj3qw58HHYoRvACCVetVSCz4z+tEofPDLdSxtwGsQ2qJqNjQGB7\r\nyjEjELcNWh2sJdPg0Y12RvHpLgrv6IZVbv3jQzRfQ08Bk7ppNZiKUukiYZ+k\r\nuQ6h1/SI9agH2EGpxGJsv90y/bY0Zuw1OYvg9rC8A507K9RWGCEQtBV8N3ZH\r\n9235OUpvCdCPu2yiM/khJF8AM2XaQ/HflRs=\r\n=ZNJj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.2.0", "@jest/types": "^29.2.0", "@types/node": "*", "jest-message-util": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.2.0_1665738831455_0.9874667169321079", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/console", "version": "29.2.1", "license": "MIT", "_id": "@jest/console@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5f2c62dcdd5ce66e94b6d6729e021758bceea090", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.2.1.tgz", "fileCount": 9, "integrity": "sha512-MF8Adcw+WPLZGBiNxn76DOuczG3BhODTcMlDCA4+cFi41OkaY/lyI0XUUhi73F88Y+7IHoGmD80pN5CtxQUdSw==", "signatures": [{"sig": "MEUCIQDFy63gP6ORbXleJWw872CzT4OJX1lD8BsZduHCqcy8EAIgPx5Dq3lo9W5o+3GUcG4nGMq6Hlr3X34LTKjsPH4FkZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUmQ/8DkOsuK6SrMkWYQofVw5UTlCwzs1GTBwtTODEnPOXqjQMpKf1\r\nnkxmz/urPMmtDj/ScBOv5u0ianMZwkMr6kVvEJZEileBc+9mjsEftfya2Eu0\r\nfD0YXP2Hxeq3qT+3YCyp2l5NvZ60PJ2FwviXQVZcMsO6Swv//fqmEelX/Q7L\r\nwEWUHUI6QjiZVg62K63sNmKIlJQnGkfSagKC6bfta4dmxh/eMPHayJbTm6Gc\r\nsM0WY5OrA7FLyPiEOIHPiOd1qZecmcj/22KJr26t3xa13JtVpeCZawHDpbCV\r\nuxQ7xQg341g8JgEw+vtG3xOb5vX5Qv8UoZIN/kUEYHGe8Fbf9Bjl/7b295sh\r\nv1VYGbysi973ebrwpQSCqZ1pN0QLqrHPRePtRgZRXYEupUVnun7qb5TWiI0y\r\nsDdOs7tGNpHvjVYD+7KNlu/bQCbEcbyyA14IdAhc5ikgT0MBQoCh+0dt5c4d\r\nL4rkUM4yEltTGVMxtyggY9GPnXKS4yMG0sFyloG/BwErX1/lru95cXPY3+s6\r\n6YOboi8cVl6FJh2ux+LQn+D96B2Hkdx67Y5ehTDJ7cA95qO9j8jI3l2Tzsd8\r\nCKwVteD7Awcg3RVhzTffxlMNKJBJm+7YiOmyhJw6ttxKrbrsSw1+oH+5SRLl\r\n6ZpnISn06PZTAAUprmSR4Ul/dzk+yOqEscM=\r\n=C3qT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "jest-message-util": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.2.1_1666108814706_0.4698083645359381", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/console", "version": "29.3.1", "license": "MIT", "_id": "@jest/console@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3e3f876e4e47616ea3b1464b9fbda981872e9583", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.3.1.tgz", "fileCount": 9, "integrity": "sha512-IRE6GD47KwcqA09RIWrabKdHPiKDGgtAL31xDxbi/RjQMsr+lY+ppxmHwY0dUEV3qvvxZzoe5Hl0RXZJOjQNUg==", "signatures": [{"sig": "MEUCIQCmd0ocTVaiOgu5qO2Yys1V94COH+DeMVdvYA9U6rPx0gIgVEMXjWbAArmoT2ZFNxIlfXWTeAehZLa6iCGY/vbMKIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUAQ/+JT3hGppr+KwCoqjWyQH2cl+N7deGrpR0JdW6kGAdql0lxNKy\r\nYqhmEk5KiMl26KKaaJhxBQDyWa4zvDZOVpY3g3I2P4BlzfDf/jvDyEttrk25\r\nsrArruTFtF4AbsH68dJC/z5atTvJsDJa6xGQo3S6IF02viw9ugUCl/V1Zu4v\r\ndurX34jgeuMqppYQFB+Jre2aeXX9MGpUR0bJESb3frMsDnBCJMk4K4nn+Otc\r\nBe2jlKOHRI9cisxMoNz7PT/GL6furzBRx2ry+ySheoOo+iojJ4Aq3M9PStzh\r\nI+Ly6abzZ5k9E2uyfZqH+TD19+rwjV/0ONqqw+opG5E7qpGSkJNqgm9GXiGd\r\nUF83E2m6pZQF6Vu8g0AYRhd03SMXjn7M463UiqEIZmFUDA6xJHR7CwaeOE0F\r\nDQcykYkNDL3qtiMt/RVBfU00DrETSZ1R+d3+/gq9YAH1yjlKWFW3Oq930Fon\r\noTfryRX7E9iGXjFlv/VpQji3q51ZbjR1YE9b+cWHGaCRxCi0auaUJ313fJSz\r\n5737oslps2qsn088iryz7lmw7DUiIxdmaYO+stnZVBWI/fas7wZBd9WNEWXt\r\nDnJ4ZT9dRxE9am4CGP38/q1UdHgUD6R/0XNKp+ftBj8HONOsMCtWnwUySZZA\r\nwgSDgPWSBdEQlZZ10V9fTcnDIZVRv2R02U0=\r\n=31zc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.3.1", "@jest/types": "^29.3.1", "@types/node": "*", "jest-message-util": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.3.1_1667948184483_0.9573488065926758", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/console", "version": "29.4.0", "license": "MIT", "_id": "@jest/console@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed2e5bc783791c4be75d0054d1bdf66a46deb163", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.4.0.tgz", "fileCount": 10, "integrity": "sha512-xpXud7e/8zo4syxQlAMDz+EQiFsf8/zXDPslBYm+UaSJ5uGTKQHhbSHfECp7Fw1trQtopjYumeved0n3waijhQ==", "signatures": [{"sig": "MEQCIACLdGBJRIDZGH6yiio5fLXFi0/40GZS5P+6zb2f6IMVAiBe5dVsXnqBHBacnI/BB3lspmRw+KGL1gaaIIS87U6IhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1Vg/+O3pTN6MQ2/8YtSlKlBi5110KGGfj2wgrhcDwdP7PyHFyGYpy\r\nD8kv9C4LrVqI/dWfaD1MkcCblbyPjsD9MK/bCWljRonaxi4eiMPCxTXCggtK\r\nAwDH2PyqML9bgiuHtApPSbxsoS+bUKzXPboMvqfD9jRxlduo6U3ZUAf+lSJe\r\nfQQcTPsk90KlIVfnxHsptfpgBa9TpWtFSZFsQ9PqKMpdVTwvkhnRLit7wHPb\r\nCD1hC22TbnoegAFx0EDbOmIqTi8B1DdoKfRwdNKDTISB9e144HWeG7cC92VS\r\nvRDAkmZRwB4mnAgMF13fS6t0b2p27VDfuNsDS7k8dXIJW3SToCCmcA7iS7ZN\r\nnW8Mopyr89ZvFbIrevtMzFb7N0eDF49BSiXPR9GGckjKXJRy599Om2Fp/nkc\r\npeQBIwa/CWF/ybPC0rp/g/219F63Y4vkex9d7rPrLMkPbohq7jYRDrzBLzTR\r\nbq0bEB8HszWDO18+fyo33tLFl7JkUJqlvBbDtbRwTJrf2+y7L58nlTE2483I\r\nJEZZWr7RB0qnH0wDjmzI5huLQ7uDumJTTkzVoUMK76bwMmg5mlW5Bh5yj6DJ\r\n5h4wHRJqHsWey89IdXnLIjwnLhmzsLCjZrf16v3XB/9VfzT8HiWqJSKuyWfy\r\n5cjV+J0hX4JjcdZ9oNsSSdZXci7jyU/0XCc=\r\n=ACxW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.4.0", "@jest/types": "^29.4.0", "@types/node": "*", "jest-message-util": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.4.0_1674557754037_0.922310309669341", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/console", "version": "29.4.1", "license": "MIT", "_id": "@jest/console@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cbc31d73f6329f693b3d34b365124de797704fff", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.4.1.tgz", "fileCount": 10, "integrity": "sha512-m+XpwKSi3PPM9znm5NGS8bBReeAJJpSkL1OuFCqaMaJL2YX9YXLkkI+MBchMPwu+ZuM2rynL51sgfkQteQ1CKQ==", "signatures": [{"sig": "MEYCIQDEegim1x6wHbFEq/uJMWqYpwdtsbzu7CJUtW8pUxMrpwIhAIid2fkE78nKC2tWCXnN+xmLXyPEabHgn2vChv+bJC4p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5xRAAm1VFOKWepciy4WugB4lOhxBvK4Wa74dizCkVpRxnjSd5BCTu\r\np+vCq/7qkY9L0FZynKFzzNbgs6ucNlt30qXSvET0dUD7caDLBxPfMnsKJyN0\r\nmY9pfTwpX5wojEpkTo4ZDQXICphYFzXKDJdtqtyfOI3g1vfrd1m+sYSLTw/9\r\nkE8tHBNmtDlr7GbpogUrHWKsaJpeSTIIbEaRRblHcyEDG2hdOHbk8w7Av8oG\r\n8jC+6hAr/Pwu7lZrYovwtlFMFUu9gbeeb0yILiqWiMd2gOOY63u4ch3ZNLE6\r\nNCuZIVQYwN+bqlcwuBrxMbrIDIqFrWCIajEmbK38MFaJahrsuADo4bdc/42Y\r\ng+gbKs+JzHrCEF/57ASpm7kAsnSOBjDmsu8MyTLqChoQ0Klnwnvo6dSrhcR4\r\n0Kjcra6TOKg40y5WreSTrROgh5Zu8tgbUFRBYE2F916LQoB+gij6siS2GUAy\r\nXsGPpryuoxvTzqOGNtiLP+WVqHyVDJXcFIi7tSBpxG0IYiOL64Tc5yjGSZpH\r\ntIPn1jLx0qAdk4f2uDX9bWmloVYDpzsQ97s2VBAuWEs1j7qdnRJNr4COJ7ZY\r\noD5CHmI7VheS0QIMFwbgl99jz4nQD43n1Gx2Y3qg9kSvnvYGEdE5OLmuOTJ+\r\nCj1dKhTu1Sb0d2fHRxYoHkZCKvP6SP365Jg=\r\n=uQ/w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.4.1", "@jest/types": "^29.4.1", "@types/node": "*", "jest-message-util": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.4.1_1674745718393_0.8700727010063893", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/console", "version": "29.4.2", "license": "MIT", "_id": "@jest/console@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f78374905c2454764152904a344a2d5226b0ef09", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.4.2.tgz", "fileCount": 9, "integrity": "sha512-0I/rEJwMpV9iwi9cDEnT71a5nNGK9lj8Z4+1pRAU2x/thVXCDnaTGrvxyK+cAqZTFVFCiR+hfVrP4l2m+dCmQg==", "signatures": [{"sig": "MEUCIQCNwHdVmdOZXCu34HZKBKvksWiE3Y3gDA5PNrVWrj1VcwIgT+32cBZUNqS61ryS7B/jOfoFh722Qrd60Y1jBC7ZnSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lX6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPXw//SRfDdfM1tu+/SnVhntSCoC1LPqOY0pKKG7L249HMbYbPd4t7\r\nZDS/GVYxvrW9/xcPMbC6Q6uFEVV5PXDxHtRqWuFfN2pJQh7CGmd17HlRp0MS\r\nUom4/xbdQTTSJdgc/pHyz75sGDSLP7hSsW+V0R4rjEiT3zZadlEyMxeQNvHe\r\n0Fwm/Yv7t4yW07+I1lF1suGNOQIs37WNQDQKLU7ORYfWkV8mNW/gOk2Nl5UB\r\nURt55QGuy5TlLNPeIOKUisOnwPYji3D+LFsKrevmbqJpr38T0kP8vDkXUs7n\r\nj3NreIbXiF3TjO+b0l29z5Y/UYF7N3vsJuVzF01GKgDgqt7N2s/LHxPFo8Ou\r\nub8eGJptewm7s7EZa2xH1tqpwmtPQCalbd+Msh3TkwjWx4c7uqy9QBSs9rht\r\nDkIYNzVkGfTZ2oEQUFlFoeF2P8beogPu1w20l8RrPLD1I/Fx6kZWk0/98CbQ\r\nPLkWdt9n8bEae/kYrgzMQfa0nRq+S9oIMMc77Z6FZi8Q8X24W2KC8XDp6wU9\r\nQjai2dMCYj56hG1BdjSirRgbZaqHctE38z9EkrCeP78HzfXXTmO3bZZ1i9L1\r\n1+9eaB0V1aMyl65RmiqxUd3lHNmF5UmA5sICatUBjdDqnr+jvYF4Syjz7VLg\r\neKYGFftKb+RBoapuzEm1IRwXyIUrd7gK83o=\r\n=e4s1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.4.2", "@jest/types": "^29.4.2", "@types/node": "*", "jest-message-util": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.4.2_1675777530576_0.040749739560809406", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/console", "version": "29.4.3", "license": "MIT", "_id": "@jest/console@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f25a99f7f860e4c46423b5b1038262466fadde1", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.4.3.tgz", "fileCount": 9, "integrity": "sha512-W/o/34+wQuXlgqlPYTansOSiBnuxrTv61dEVkA6HNmpcgHLUjfaUbdqt6oVvOzaawwo9IdW9QOtMgQ1ScSZC4A==", "signatures": [{"sig": "MEUCIQDCLyWYJ/zoHEok4sH1Chi2DQ6RTgqPgfilvXq9oa88AwIgTb15ztxZpoDoNrxP16ekcDwS/8cPYM7EKGtU0IKNSWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MikACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU3A//a2as2bZxvXiGk7y3vSbAJhmkUURyqOUcdfDCxjGKVtGjBsAF\r\nRgRr3aNF3v2eSrpIQJ660tlU657O1MLv2dMZQJL0mmuGOmzskPhMQZHCWC5K\r\nW0V+Jif/gnm2FdaGrDgafu4todduh5ZhrMu1SAduqaIKpNGs7NR9M0ePgzWB\r\n6up/Hnov+9FKNjS9/KbsL9dCQPOxu8iVjla6PmkiW81L0rIr0Xotw67gy1qs\r\nK2cYATGXQrZKjwjLYl6S4/4+SnURSYNAgtmpCKKgaeohcjv+lwh9V8dpyzTJ\r\nUnPKjimdPNanlKFH50CmYAY2oCnUHVMX+ygGbhS+7PaxWBedHLWGrtXtD+nQ\r\nJ23xTQu7nJFrEx4/93vs+MJgzAPOJP3vfhkOqRs3vjbgXGhtdX0JxxtQx9DT\r\n0MCDHBKC86WY8I1wLx6hzXFNXemp883I+SZKZpGW7sX3bw4UL8mD8BAhrpEE\r\nptdcyZ0zd4d1a46PNFhJ79lttQDcMvho4ShA+0Wp37b5NkVe+rD8Lnlny6Ui\r\nS3pceb7cNL0IhCoHhjWvJGWpF5hKCKIlHLc55WUe8egGYn+jlFrTeGOmdZaM\r\noSx2I/0eu8geDJVPbDLqheT9OsZT5Nj1/KDh8G4FBoykwSjz/sG/uzKwChzX\r\ncJQay4YR1dOuY/tGDOx/ZC86CYtSZVjjaJQ=\r\n=rkj3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.4.3", "@jest/types": "^29.4.3", "@types/node": "*", "jest-message-util": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.4.3_1676462243812_0.14376636754075833", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/console", "version": "29.5.0", "license": "MIT", "_id": "@jest/console@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "593a6c5c0d3f75689835f1b3b4688c4f8544cb57", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.5.0.tgz", "fileCount": 9, "integrity": "sha512-NEpkObxPwyw/XxZVLPmAGKE89IQRp4puc6IQRPru6JKd1M3fW9v1xM1AnzIJE65hbCkzQAdnL8P47e9hzhiYLQ==", "signatures": [{"sig": "MEUCIQC58Wp+bSzOQikAheUpYpPiWHDe9+0eZ6KtJFviiFmAzgIgSjV1sqD8ZHeFaUyPDbSNrL7iqiZbYr44lSxmIFa2/Fc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeutACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+Dw//SHrAskYgQpgVdTW5EwZvJa/YSxrFw27Mrtjp4AMotNhqhTa9\r\ntgxjuVOn6joM3TAXcCg8hA2uYQmprbsk7EJ3CXPFqtqCAbqnAFfIGwXPHeBT\r\nmGrxGUYlUTPN/vC7O8CeJuZ3MwdYbdV4QeqNY0ehfVV66lEVeuYNiIKGvDqB\r\nh14UO2ve+LYxQ62l7GPGmwEzpAyno/1CVqJXkOwokOlS2Fm/2Su4m/LcUlLu\r\nqICq4BeCeKJeUnCNgAvWMsh6gyco6Zu+BHYyeKo5oSDta8Z855KMoTgT00c5\r\n68y/ptgjOUqqVTxoBPeo1SIli8CpxASM6EKo8Po6j3oIC7B+w3caJ4/9ok0X\r\nt/fbaoSuJqdCdDJ//zt7EF7DQPKU8IaazUhUjPpwCTurDVqXZM7AyR35hyDe\r\nvTvCHNGkvqyPS42nQzivEYHvawPDSZUMdTL3HDAKL5ZLKqr8Yy9ed5hXMj2H\r\nIlbh10nPNegIza4USl78QP/2u7n1jI/PqCHJxnlnFtiuuiPpG/5NuZeWGlVU\r\n3F8YXXdRvfTTo1s2EAdgK+UpdEKLwyVCBU57BMH05/lvjYGcxWN+SQ15+H1o\r\nAVVOhQLI6SEmhygU+jB9L2vojs/wrToJAW5BaKqbiX/1NL7qRw4CyQnjcF8P\r\nUeSKcny0hTSjgqbYohUpJ63mdBQ1LsRRtM8=\r\n=2Tmv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.5.0", "@jest/types": "^29.5.0", "@types/node": "*", "jest-message-util": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.5.0_1678109613039_0.8324066355812414", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/console", "version": "29.6.0", "license": "MIT", "_id": "@jest/console@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad0ae19e56e3ca34f620bab7b3e0bb7e3e655275", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.6.0.tgz", "fileCount": 9, "integrity": "sha512-anb6L1yg7uPQpytNVA5skRaXy3BmrsU8icRhTVNbWdjYWDDfy8M1Kq5HIVRpYoABdbpqsc5Dr+jtu4+qWRQBiQ==", "signatures": [{"sig": "MEYCIQDdeU3AklZIav3QWhI+blr+52asiig+4isJh8Q933oCKAIhAOP0lNi4nnV1kL0ehytOik6TFEJLPQrBWxLL8UPSCU9o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19100}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.6.0", "@jest/types": "^29.6.0", "@types/node": "*", "jest-message-util": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.6.0_1688484348302_0.16454859825129087", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/console", "version": "29.6.1", "license": "MIT", "_id": "@jest/console@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b48ba7b9c34b51483e6d590f46e5837f1ab5f639", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.6.1.tgz", "fileCount": 9, "integrity": "sha512-Aj772AYgwTSr5w8qnyoJ0eDYvN6bMsH3ORH1ivMotrInHLKdUz6BDlaEXHdM6kODaBIkNIyQGzsMvRdOv7VG7Q==", "signatures": [{"sig": "MEUCIQCSCCsQNI+xkKI8kRkW4ZXDe02nxf+JWZFV/nwsjN1BiQIgPJbBFRPC514f0SiTXTrLxCl2YEfzIdp14aaQxxQajQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19100}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-message-util": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.6.1_1688653107631_0.1666285406517769", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/console", "version": "29.6.2", "license": "MIT", "_id": "@jest/console@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bf1d4101347c23e07c029a1b1ae07d550f5cc541", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.6.2.tgz", "fileCount": 9, "integrity": "sha512-0N0yZof5hi44HAR2pPS+ikJ3nzKNoZdVu8FffRf3wy47I7Dm7etk/3KetMdRUqzVd16V4O2m2ISpNTbnIuqy1w==", "signatures": [{"sig": "MEYCIQChHc53AHb3C0jhXCQMG61gIWV7LLQEBXz4O9V551CngQIhAKDKsWD9XRRFdNTAFtQd2c4Nr9E4tq7udZwYkOTulDOC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19100}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.6.2", "@jest/types": "^29.6.1", "@types/node": "*", "jest-message-util": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.6.2_1690449692584_0.9865933543663317", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/console", "version": "29.6.3", "license": "MIT", "_id": "@jest/console@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "55ad945087c27e380d6d9fcbb85181ed802543f3", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.6.3.tgz", "fileCount": 9, "integrity": "sha512-ukZbHAdDH4ktZIOKvWs1juAXhiVAdvCyM8zv4S/7Ii3vJSDvMW5k+wOVGMQmHLHUFw3Ko63ZQNy7NI6PSlsD5w==", "signatures": [{"sig": "MEQCIEMtbp5cAlEco6GFjvKSU2aOZWExrX3kZIiFu5R302kKAiAuAKmTByu0mJZQAVYtUz1IQNAgXt9uCyzYRrYILgK1ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19014}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.6.3_1692621558023_0.49355600404437117", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/console", "version": "29.6.4", "license": "MIT", "_id": "@jest/console@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a7e2d84516301f986bba0dd55af9d5fe37f46527", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.6.4.tgz", "fileCount": 9, "integrity": "sha512-wNK6gC0Ha9QeEPSkeJedQuTQqxZYnDPuDcDhVuVatRvMkL4D0VTvFVZj+Yuh6caG2aOfzkUZ36KtCmLNtR02hw==", "signatures": [{"sig": "MEYCIQDyGdwTogO6ckedeaSKxIaezwehp05DH/uGDh53c3Qo4AIhAPkWc0TpwjtWZs1PKvdTe2VLpOIWdfO06PFf2N/Z5i4M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19014}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.6.4_1692875425879_0.027082366083194476", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/console", "version": "29.7.0", "license": "MIT", "_id": "@jest/console@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "cd4822dbdb84529265c5a2bdb529a3c9cc950ffc", "tarball": "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz", "fileCount": 9, "integrity": "sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==", "signatures": [{"sig": "MEUCIDhzbQdc7Ev0DuiL7NkOF141Mv8gPl6VHWLOTQlNoB8hAiEA9tpgGTtnKU/i/5w9BHbTM+fMLeZFS2uvJ0b+jlz5Ci0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19014}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-message-util": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_29.7.0_1694501025204_0.17899056486336318", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/console", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "863a590f9c3a048be8da1cac06a28080a176b47b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-yo/YOkJtRLTVdoPTfBYbZc1Ks79rMpMbcAFIQryn88E+VZuDEWlUKpTuYtabAs3okRtP009KBepOT2X8BiSknQ==", "signatures": [{"sig": "MEUCIBWef3GDdARTy9/toSp8A8tRoM654iJRROksEx5dLqogAiEAy82O4PX7Gi2Q6txl5i9mR8xlNnG3Iu5WJqvWR9zIbLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20962}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "@types/node": "*", "jest-message-util": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.1_1698672793389_0.08811898564932519", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/console", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d0121888af91a9b201088f2939b083d57ec96416", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-UX4wXwMnQuJX4PAxVhkbH9QLANx5egfrDgisNZqvJYGFI9C407ByTTYttVn7hQ4AHaMYYw4w0lZTX0E4GGVqTA==", "signatures": [{"sig": "MEQCIA7eKO0TrnbXt5ntlKtbskQ+ze9BrGqrcJd8w1QxRKxeAiBkmbSj3fOyPjCz7cFPdUavuBgcpteU4C4xA/jkY6dX4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20953}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "@types/node": "*", "jest-message-util": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.2_1700126909688_0.3722035635978411", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/console", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b899cb6a55e761eb0902e64fabcc5b3c5e6c7cb0", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-YswdyaJ3/6sVYe4n3iClqiuq3M2jA3FevbwBr1CE/QrRUuZFAd58sxRnW/XD2bS7pr5zI0dviaSLsFCkUfuuiA==", "signatures": [{"sig": "MEYCIQDHnEjp+Hlomvqdg6BU+boVtOgcndGlAjS3Z/KH1eQA4gIhALcgTK88bzbTg4pdR2WcaYnBnMM0u/ebcUvqKKxeTeHb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20943}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "@types/node": "*", "jest-message-util": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.3_1708427350361_0.33033378411270253", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/console", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "6e4a8f09d6f8b3f81495275ee3e880391a1faad0", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-CVV3rvFJW1QOaRtSK02NUUCqfhc7FnDFNL28/fo4qO5+feSQtD7NlnRxub1FXRnTec8jdUHUE4469nBiDc0d7Q==", "signatures": [{"sig": "MEYCIQDO5pObkD9fZpY6OpJfd7CoSNfQMZPvQ8UUgsFZn3tzDwIhAPIR8JQcHZjLPBB/hlKBqxs9eT94ToYhredtT9AU135D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20987}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "@types/node": "*", "jest-message-util": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.4_1715550209247_0.3486676701625695", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/console", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "58c902a83ed432ef159960116357747170ca8e7a", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-60bIFNdU3otnWJljjfnbIntLJ9JUiULI3o+uslyao2j2yP7rYfKdg8sye7NfQ29Zb0IoluBefpEN7s2hVzyz3A==", "signatures": [{"sig": "MEUCIBUe7sz4WHvUKHqCYjXJ4Cg+rvYmpoZ/i6eg6JZBKXU+AiEA0lEcrtnp6Oc5VGTVPQn0Zx6r4cWPo+9isuNIl60XhsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20987}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "@types/node": "*", "jest-message-util": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.5_1717073048514_0.4512252403785695", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/console", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "ef666f9338701042210c55711d5751a94de6417f", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-08BeAnuabmauj5B+Xa4GNPAotQUGm3PLKSE3rnpnmxniZzR4tXhx8+AA2+HGTri4bbVRY/r3Jl0vJnkhvHTkeQ==", "signatures": [{"sig": "MEQCIBRXq0fLrL5Ckh0qvN4ul8raN2oQU0dSJWe3Ydwi7oLXAiA6CDNk+LRqJlBBV5OyMXqNcbjNvo6Rdqmr58ytJ+NwBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20901}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "@types/node": "*", "jest-message-util": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.6_1723102987489_0.4832434511206265", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/console", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/console@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "eccb426c97384ba731a2a3c90ac17816d185dfdb", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-6tNcZwg3dJeww2Ne9Y+x0Au4OPtbWwYzOfwh2ecK/Yp3/KNkULp5oNvx0qJUvvGgQWSBbYvLRrgLnoRlRhpZew==", "signatures": [{"sig": "MEQCIBLkPv/1na5BV/gUYd5nT8dArW1k5Ynhx6v1AwyD8a5RAiA47hk5wFG5sVX0DWYXzWlq1HNryuE1NuU2rmdN9wCSow==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20902}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "@types/node": "*", "jest-message-util": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-alpha.7_1738225714433_0.25328319623180184", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/console", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/console@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "06d54ed8f609e06cdd81454cba113ef2f8f674ec", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-JwkXjDdqVDYnHIhNT4X9EeZo4DTMgnvxPkZ3sDotkTCCgJQjWHziXoPCM/IU2pZwNCF2x5zhj6r/G6aPB66xhA==", "signatures": [{"sig": "MEUCIGlml5azQvc2DPp6YlQ1A0kXUWtJ/Ir2fq0Tu0G5LCpSAiEA+p/BSuoSEyc4oH34Hel8Q+EEAUAB6V6aWqO2hDHhsGs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20892}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "@types/node": "*", "jest-message-util": "30.0.0-beta.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.2_1748308993342_0.5085057422901127", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/console", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/console@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7d6e07946ad94e487c67839e0f9852687dd4811b", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-+kv9KZac0t8IP3PEAvBDS+lN6KMQTYSESjHQUdCJTG7Cwpk1fJ+5SLZn8AOQ5/wGL5fFGI0Tcz4THzASNLwlbQ==", "signatures": [{"sig": "MEUCIQCwbu+eGHPZWvTrQoYSvvu+QALePWLeVuHbpmRtRFqw2AIgTuSVAEIYO/wg2MzQb2w/3z1BdQdscLysjOtOr79HHhM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20892}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.3_1748309268654_0.8331452179395948", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/console", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/console@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "cd2fb65ccc5d420021634d5d844720c8461b9d8d", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-8u578PSQQG8LREcuoU7AYpWypDP2mD3jZGOvkNnOwb8Cwi/rL5JU/8xN4s4krvBxjy3Gm1pCYZWZKqDOOg3Hrg==", "signatures": [{"sig": "MEUCIQDfc71nLPi6zmAB4I7yBmWSjDW0wyaxmMb5cPJvv/UU2gIgR1azaBD2sgb3fvg9jW6nAmGv9Gz8rcx8KUR7Ux+fd48=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20892}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.4_1748329467007_0.970635441743896", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/console", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/console@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "8dc5926c5dbb16337d7b2afd2359d88cc3bc66c1", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-48ZxV2U94Sy8hVHJXu/ciwNxuQaoZ52tj6svNx3aMtgoQn62tfr5Hsw8lp4K3ytjq2B+bi0vM+ZRqRohyF/kdg==", "signatures": [{"sig": "MEUCIFNxQc6A2s3h/QMzYqqKL8s8ejKvdEZGZZiQtgAmxbU5AiEAvOkzLpbfsys8f5AT7BHfObO/UnctUpexBMxLi0FV6to=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20892}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "jest-message-util": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.5_1748478610981_0.014704766308419881", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/console", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/console@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c3c3856c55f2d5e133bffff9dd9f1906072c6446", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-ifhoeSXDUX1afVsc4rhHvUmJRtu1skPR6FLDONqxB6jp/riJgyVjavwKVsEwGertfAMl+1j7koCU0gTOcJXFCw==", "signatures": [{"sig": "MEUCIFSRL89kt3HEoNLGUFM3bQhh0tgcoRJz7nGqlodUxTjOAiEAnPYG0+Z2CDqGx98w0ZtMikWppglY+5bNVlaYuyCxViw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20903}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "jest-util": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "@types/node": "*", "jest-message-util": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.6_1748994649534_0.05897368867502473", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/console", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/console@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "849d249d6f7b7993805f6050498120869b5ac2f7", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-DvxPButfFWgUtOITnGc5UPW9QkrB2gOFc5E0Iqu+r08VKHbpVGOhG6gK38x7g5YdXqoOykOrENa2Khx0sEoFKQ==", "signatures": [{"sig": "MEUCIEJIg/B427llLeB0BAOEh0snWjZGp3g9mRpD+F+M8ksFAiEA6FStI00JLS0dhITZNKNpl3z4EEk5U6itmUBKTimGo4g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20903}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "@types/node": "*", "jest-message-util": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.7"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.7_1749008142978_0.5895600020278489", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/console", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/console@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "35d67c6a400893ea7a041a291b6bf14355eea631", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-wvjXdkAz0GatFtEAfLamkxS2UDQBGn0uq8PNIdeSuTdCbD8STZBIYuFr023FBYeuWJdsPcpFUn+76BGnnvoZrg==", "signatures": [{"sig": "MEYCIQC1ZyPZCSWFVTxcEL00dFr8sqIm5eDZ3wv4IzuFkCFNhAIhAM49AK6AEJ7fjVK6SxFbieQtqkVf5LHSrnw6vzFMzwtb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20903}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "jest-message-util": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.8"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-beta.8_1749023592429_0.7668899400375695", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/console", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/console@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2c7cf1ecf0f2a4e544980596e52153639eff505c", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-WizStN0Hjq9VN7e51xiAb+3rzajcyR9YexbQr8ZUI6ADulzKvxgS5TlXn9QlqneI+9w58FVJs1ADsZKqlMmrQg==", "signatures": [{"sig": "MEQCIFX4IxTLpuuUddzFyogjHL6mpazGZ4zci/ntWTHdUuu6AiBA2JUp6L+RqcqyhXJ8UXwGI7j04zwfJ/vJV5H7f4hcAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20895}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "jest-message-util": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0-rc.1_1749430965512_0.6154706192751573", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/console", "version": "30.0.0", "license": "MIT", "_id": "@jest/console@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7f8f66adc20ea795cc74afb74280e08947e55c13", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-vfpJap6JZQ3I8sUN8dsFqNAKJYO4KIGxkcB+3Fw7Q/BJiWY5HwtMMiuT1oP0avsiDhjE/TCLaDgbGfHwDdBVeg==", "signatures": [{"sig": "MEQCHxssfmFA7PYXm0jnEOIWmAglHm2dqJJP60gyDP23HCQCIQDCMSx84wTy/nfV0Q6D5vtav93+CwhBc6jF6uRz9+76iw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20868}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.0", "@jest/types": "30.0.0", "@types/node": "*", "jest-message-util": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.0_1749521752274_0.4989704285912533", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/console", "version": "30.0.1", "license": "MIT", "_id": "@jest/console@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2e060bcf9645335029b45c4aee2bf5579f0b9998", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-ThsJ+1I1/7CSTCmddZWqwkwremh3kmKCEoa7oafYL0A1a4tiXWKHzp8+a4m0EbXfGsYVjaVjjzywOQ1ZCnLlzg==", "signatures": [{"sig": "MEYCIQDcDON+rDAKn7h8xyU84emTDu1vCRisXYnx12A3cvePtAIhAIBlL+PM567V/NoWKCdrsKGb6SrC/VGf/pQMJHNUZmdl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20868}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.1", "@jest/types": "30.0.1", "@types/node": "*", "jest-message-util": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.1_1750285890163_0.042004027508812936", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/console", "version": "30.0.2", "license": "MIT", "_id": "@jest/console@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "e2bf6c7703d45f9824d77c7332388c3e1685afd7", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.2.tgz", "fileCount": 5, "integrity": "sha512-krGElPU0FipAqpVZ/BRZOy0MZh/ARdJ0Nj+PiH1ykFY1+VpBlYNLjdjVA5CFKxnKR6PFqFutO4Z7cdK9BlGiDA==", "signatures": [{"sig": "MEQCIB2dWO7PcJAkiCJMyIhOalSKEf+41jtmQqAxt7BNVlaCAiA4REN9J4vz4XrraAzIE/ahINpldzONDz7eOh+/zpSs5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20868}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-console"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "jest-util": "30.0.2", "@jest/types": "30.0.1", "@types/node": "*", "jest-message-util": "30.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/console_30.0.2_1750329980258_0.4176120428525989", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "@jest/console", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-console"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/types": "30.0.1", "@types/node": "*", "chalk": "^4.1.2", "jest-message-util": "30.0.2", "jest-util": "30.0.2", "slash": "^3.0.0"}, "devDependencies": {"@jest/test-utils": "30.0.4"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "_id": "@jest/console@30.0.4", "dist": {"integrity": "sha512-tMLCDvBJBwPqMm4OAiuKm2uF5y5Qe26KgcMn+nrDSWpEW+eeFmqA0iO4zJfL16GP7gE3bUUQ3hIuUJ22AqVRnw==", "shasum": "943a62c3c8e3f495290f2e2c3749b7b4516c3e93", "tarball": "https://registry.npmjs.org/@jest/console/-/console-30.0.4.tgz", "fileCount": 6, "unpackedSize": 24719, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBGwuNyHnzXWxnI5zRUFflwcT+9U8XgC1HxjA6bTT490AiEAlU52G1k6HrNUxVwfM9f0OrjC/cSCfDuRMlYaAa3+cfg="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/console_30.0.4_1751499945713_0.44640652261808933"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T14:46:42.431Z", "modified": "2025-07-02T23:45:46.142Z", "24.2.0-alpha.0": "2019-03-05T14:46:42.962Z", "24.3.0": "2019-03-07T12:59:39.344Z", "24.6.0": "2019-04-01T22:26:16.788Z", "24.7.1": "2019-04-04T01:18:41.936Z", "24.9.0": "2019-08-16T05:55:54.905Z", "25.0.0": "2019-08-22T03:23:53.089Z", "25.1.0": "2020-01-22T00:59:56.357Z", "25.2.0-alpha.86": "2020-03-25T17:16:28.513Z", "25.2.0": "2020-03-25T17:58:17.780Z", "25.2.1-alpha.1": "2020-03-26T07:54:25.238Z", "25.2.1-alpha.2": "2020-03-26T08:10:33.402Z", "25.2.1": "2020-03-26T09:01:16.476Z", "25.2.3": "2020-03-26T20:24:51.605Z", "25.2.5": "2020-04-02T10:23:08.499Z", "25.2.6": "2020-04-02T10:29:21.313Z", "25.3.0": "2020-04-08T13:21:12.806Z", "25.4.0": "2020-04-19T21:50:29.714Z", "25.5.0": "2020-04-28T19:45:27.565Z", "26.0.0-alpha.0": "2020-05-02T12:13:09.157Z", "26.0.0-alpha.1": "2020-05-03T18:48:05.495Z", "26.0.0-alpha.2": "2020-05-04T16:05:39.912Z", "26.0.0": "2020-05-04T17:53:17.148Z", "26.0.1-alpha.0": "2020-05-04T22:16:04.022Z", "26.0.1": "2020-05-05T10:40:53.193Z", "26.1.0": "2020-06-23T15:15:17.412Z", "26.2.0": "2020-07-30T10:11:49.313Z", "26.3.0": "2020-08-10T11:31:54.873Z", "26.5.0": "2020-10-05T09:28:21.469Z", "26.5.2": "2020-10-06T10:52:50.256Z", "26.6.0": "2020-10-19T11:58:42.488Z", "26.6.1": "2020-10-23T09:06:23.082Z", "26.6.2": "2020-11-02T12:51:44.387Z", "27.0.0-next.0": "2020-12-05T17:25:32.430Z", "27.0.0-next.1": "2020-12-07T12:43:33.815Z", "27.0.0-next.3": "2021-02-18T22:10:05.009Z", "27.0.0-next.5": "2021-03-15T13:03:30.092Z", "27.0.0-next.6": "2021-03-25T19:40:10.464Z", "27.0.0-next.7": "2021-04-02T13:48:03.683Z", "27.0.0-next.8": "2021-04-12T22:42:36.684Z", "27.0.0-next.9": "2021-05-04T06:25:16.548Z", "27.0.0-next.10": "2021-05-20T14:11:27.912Z", "27.0.0-next.11": "2021-05-20T22:28:49.733Z", "27.0.0": "2021-05-25T08:15:18.764Z", "27.0.1": "2021-05-25T10:06:40.565Z", "27.0.2": "2021-05-29T12:07:26.243Z", "27.0.6": "2021-06-28T17:05:48.204Z", "27.1.0": "2021-08-27T09:59:44.340Z", "27.1.1": "2021-09-08T10:12:18.417Z", "27.2.0": "2021-09-13T08:06:48.387Z", "27.2.2": "2021-09-25T13:35:10.507Z", "27.2.3": "2021-09-28T10:11:23.648Z", "27.2.4": "2021-09-29T14:04:51.678Z", "27.2.5": "2021-10-08T13:39:24.078Z", "27.3.0": "2021-10-17T18:34:48.533Z", "27.3.1": "2021-10-19T06:57:34.563Z", "27.4.0": "2021-11-29T13:37:18.511Z", "27.4.1": "2021-11-30T08:37:14.642Z", "27.4.2": "2021-11-30T11:53:51.449Z", "27.4.6": "2022-01-04T23:03:40.160Z", "27.5.0": "2022-02-05T09:59:25.214Z", "27.5.1": "2022-02-08T10:52:23.686Z", "28.0.0-alpha.0": "2022-02-10T18:17:37.962Z", "28.0.0-alpha.1": "2022-02-15T21:27:01.757Z", "28.0.0-alpha.2": "2022-02-16T18:12:10.836Z", "28.0.0-alpha.3": "2022-02-17T15:42:24.211Z", "28.0.0-alpha.4": "2022-02-22T12:13:56.910Z", "28.0.0-alpha.5": "2022-02-24T20:57:21.606Z", "28.0.0-alpha.6": "2022-03-01T08:32:26.525Z", "28.0.0-alpha.7": "2022-03-06T10:02:43.050Z", "28.0.0-alpha.8": "2022-04-05T14:59:53.841Z", "28.0.0-alpha.9": "2022-04-19T10:59:16.812Z", "28.0.0": "2022-04-25T12:08:12.122Z", "28.0.1": "2022-04-26T10:02:41.690Z", "28.0.2": "2022-04-27T07:44:04.897Z", "28.1.0": "2022-05-06T10:48:55.996Z", "28.1.1": "2022-06-07T06:09:38.262Z", "28.1.3": "2022-07-13T14:12:30.401Z", "29.0.0-alpha.0": "2022-07-17T22:07:09.392Z", "29.0.0-alpha.1": "2022-08-04T08:23:31.591Z", "29.0.0-alpha.3": "2022-08-07T13:41:37.518Z", "29.0.0-alpha.4": "2022-08-08T13:05:36.083Z", "29.0.0-alpha.6": "2022-08-19T13:57:50.750Z", "29.0.0": "2022-08-25T12:33:29.709Z", "29.0.1": "2022-08-26T13:34:43.264Z", "29.0.2": "2022-09-03T10:48:21.089Z", "29.0.3": "2022-09-10T14:41:39.635Z", "29.1.0": "2022-09-28T07:37:42.611Z", "29.1.2": "2022-09-30T07:22:50.239Z", "29.2.0": "2022-10-14T09:13:51.688Z", "29.2.1": "2022-10-18T16:00:14.842Z", "29.3.1": "2022-11-08T22:56:24.609Z", "29.4.0": "2023-01-24T10:55:54.231Z", "29.4.1": "2023-01-26T15:08:38.511Z", "29.4.2": "2023-02-07T13:45:30.699Z", "29.4.3": "2023-02-15T11:57:24.000Z", "29.5.0": "2023-03-06T13:33:33.176Z", "29.6.0": "2023-07-04T15:25:48.472Z", "29.6.1": "2023-07-06T14:18:27.825Z", "29.6.2": "2023-07-27T09:21:32.759Z", "29.6.3": "2023-08-21T12:39:18.208Z", "29.6.4": "2023-08-24T11:10:26.058Z", "29.7.0": "2023-09-12T06:43:45.443Z", "30.0.0-alpha.1": "2023-10-30T13:33:13.609Z", "30.0.0-alpha.2": "2023-11-16T09:28:29.845Z", "30.0.0-alpha.3": "2024-02-20T11:09:10.529Z", "30.0.0-alpha.4": "2024-05-12T21:43:29.416Z", "30.0.0-alpha.5": "2024-05-30T12:44:08.687Z", "30.0.0-alpha.6": "2024-08-08T07:43:07.654Z", "30.0.0-alpha.7": "2025-01-30T08:28:34.601Z", "30.0.0-beta.2": "2025-05-27T01:23:13.511Z", "30.0.0-beta.3": "2025-05-27T01:27:48.838Z", "30.0.0-beta.4": "2025-05-27T07:04:27.191Z", "30.0.0-beta.5": "2025-05-29T00:30:11.163Z", "30.0.0-beta.6": "2025-06-03T23:50:49.747Z", "30.0.0-beta.7": "2025-06-04T03:35:43.150Z", "30.0.0-beta.8": "2025-06-04T07:53:12.619Z", "30.0.0-rc.1": "2025-06-09T01:02:45.700Z", "30.0.0": "2025-06-10T02:15:52.496Z", "30.0.1": "2025-06-18T22:31:30.365Z", "30.0.2": "2025-06-19T10:46:20.460Z", "30.0.4": "2025-07-02T23:45:45.887Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-console"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}