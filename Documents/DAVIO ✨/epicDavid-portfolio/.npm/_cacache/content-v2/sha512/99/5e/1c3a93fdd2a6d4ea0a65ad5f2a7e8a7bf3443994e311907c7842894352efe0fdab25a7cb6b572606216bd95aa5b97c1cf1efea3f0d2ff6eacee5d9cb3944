{"_id": "read-cache", "_rev": "3-d44c035816db3ffb5b917ba6aa3aacee", "name": "read-cache", "description": "Reads and caches the entire contents of a file until it is modified", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "read-cache", "version": "1.0.0", "description": "Reads and caches the entire contents of a file until it is modified", "files": ["index.js"], "main": "index.js", "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "git+https://github.com/TrySound/read-cache.git"}, "keywords": ["fs", "read", "cache"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/TrySound/read-cache/issues"}, "homepage": "https://github.com/TrySound/read-cache#readme", "devDependencies": {"ava": "^0.9.1", "del": "^2.2.0"}, "dependencies": {"pify": "^2.3.0"}, "gitHead": "d44d4fcae1b3ed2ee5f22cf7ad2ead6f665949ae", "_id": "read-cache@1.0.0", "_shasum": "e664ef31161166c9751cdbe8dbcf86b5fb58f774", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "0.12.7", "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "maintainers": [{"name": "trysound", "email": "<EMAIL>"}], "dist": {"shasum": "e664ef31161166c9751cdbe8dbcf86b5fb58f774", "tarball": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz", "integrity": "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvnI8tO/ZAxqFZmwkj7TTqIlsJ4vM6MOldegYQoOVVgAiEAkMVE6m55VuG3Y4lKI8bwt76YTuv0jwTPa5gsQQI9t44="}]}}}, "readme": "# read-cache [![Build Status](https://travis-ci.org/TrySound/read-cache.svg?branch=master)](https://travis-ci.org/TrySound/read-cache)\r\n\r\nReads and caches the entire contents of a file until it is modified.\r\n\r\n\r\n## Install\r\n\r\n```\r\n$ npm i read-cache\r\n```\r\n\r\n\r\n## Usage\r\n\r\n```js\r\n// foo.js\r\nvar readCache = require('read-cache');\r\n\r\nreadCache('foo.js').then(function (contents) {\r\n\tconsole.log(contents);\r\n});\r\n```\r\n\r\n\r\n## API\r\n\r\n### readCache(path[, encoding])\r\n\r\nReturns a promise that resolves with the file's contents.\r\n\r\n### readCache.sync(path[, encoding])\r\n\r\nReturns the content of the file.\r\n\r\n### readCache.get(path[, encoding])\r\n\r\nReturns the content of cached file or null.\r\n\r\n### readCache.clear()\r\n\r\nClears the contents of the cache.\r\n\r\n\r\n## License\r\n\r\nMIT © [<PERSON><PERSON><PERSON>](mailto:<EMAIL>)\r\n", "maintainers": [{"name": "trysound", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T09:12:53.453Z", "created": "2016-01-11T01:53:14.064Z", "1.0.0": "2016-01-11T01:53:14.064Z"}, "homepage": "https://github.com/TrySound/read-cache#readme", "keywords": ["fs", "read", "cache"], "repository": {"type": "git", "url": "git+https://github.com/TrySound/read-cache.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/TrySound/read-cache/issues"}, "license": "MIT", "readmeFilename": "README.md"}