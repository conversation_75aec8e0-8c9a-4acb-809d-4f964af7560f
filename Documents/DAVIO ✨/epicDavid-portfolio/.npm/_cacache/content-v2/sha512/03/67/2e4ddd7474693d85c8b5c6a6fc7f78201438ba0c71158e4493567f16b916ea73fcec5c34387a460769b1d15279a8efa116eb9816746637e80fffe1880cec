{"_id": "arg", "_rev": "340-b22ffac1803729c62686b53ef52dda5e", "name": "arg", "dist-tags": {"latest": "5.0.2"}, "versions": {"0.0.1": {"name": "arg", "version": "0.0.1", "keywords": ["arg", "args", "arguments", "arguments parser"], "author": {"name": "<PERSON>, http://bit.ly/JXLHCO"}, "_id": "arg@0.0.1", "maintainers": [{"name": "tnhu", "email": "<EMAIL>"}], "dist": {"shasum": "0601ff2031f8a76ae232a9337cdf761d1f1d5739", "tarball": "https://registry.npmjs.org/arg/-/arg-0.0.1.tgz", "integrity": "sha512-nJXtzp+lLY3b3xBcHNRySUNWIlfyZ4SXgpvfyUQv8YimglkUQCndK1HyMmYJmsqrycOwY4nsteR+8lKb/VrXTg==", "signatures": [{"sig": "MEYCIQC0CeuOdEnowYWdRhsvMgu0wMPomNhsS+oR8IULUDY2EgIhAMOGW3WF6LP9Q4XKE5MiVetdj/C8PAK2GZ2kTdhmjmtJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "arg.js", "engines": {"node": ">=0.4.12"}, "_npmUser": {"name": "tnhu", "email": "<EMAIL>"}, "licenses": [{"url": "http://bit.ly/JXKHyp", "type": "PUBLIC LICENSE"}], "repository": {"url": "git://github.com/tnhu/arg.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "Simple arguments parser in 100 bytes", "directories": {}, "_nodeVersion": "v0.6.17", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.0": {"name": "arg", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@1.0.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "sergiodxa", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "zeit-admin", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"complexity": 0}}, "dist": {"shasum": "444d885a4e25b121640b55155ef7cd03975d6050", "tarball": "https://registry.npmjs.org/arg/-/arg-1.0.0.tgz", "integrity": "sha512-Wk7TEzl1KqvTGs/uyhmHO/3XLd3t1UeU4IstvPXVzGPM522cTjqjNZ99esCkcL52sjqjo8e8CTBcWhkxvGzoAw==", "signatures": [{"sig": "MEUCIByMbEdBTr1NVYHsG7V8bFLxLq7vY8a6tVrACeZcVu5RAiEA/TQAsXqVMKnelbfMffuAR+aEMPmt9L2Q8feet3lSfl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "7e6c663df46f695ca621e70370fbc8ddcf84b32d", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg-1.0.0.tgz_1508829168232_0.9177050201687962", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "arg", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lipp", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "pranaygp", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sergiodxa", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "zeit-admin", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"complexity": 0}}, "dist": {"shasum": "892a26d841bd5a64880bbc8f73dd64a705910ca3", "tarball": "https://registry.npmjs.org/arg/-/arg-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-yZ7k3hP42f9XI+8s4Sg6nPlHXpqfu5G9ijGZ+7XDF1+9CchByI6Bqs8gB+6bFw0MoSN6WwnfYTLyD3Bv/4W8eA==", "signatures": [{"sig": "MEUCIQDJ/Ao4QGNTKU/gqaK3pw3n6wLem0OjTldAOKnJpKZDmAIgHF9OgC6tEdNbYjj4FPCVxmraC1uXzM4ok5j0fNZ1vQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128406}, "main": "index.js", "gitHead": "f584f4fc4d518fd322a2719af3e09b64e02d820d", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_1.0.1_1520561581294_0.5192438570063114", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "arg", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lipp", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "pranaygp", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sergiodxa", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "zeit-admin", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"complexity": 0}}, "dist": {"shasum": "c06e7ff69ab05b3a4a03ebe0407fac4cba657545", "tarball": "https://registry.npmjs.org/arg/-/arg-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-XxNTUzKnz1ctK3ZIcI2XUPlD96wbHP2nGqkPKpvk/HNRlPveYrXIVSTk9m3LcqOgDPg3B1nMvdV/K8wZd7PG4w==", "signatures": [{"sig": "MEYCIQDvTUl3pupf/4UsKQtYp0yAgOEE3PSfUHBdUAfxwj8UYAIhAMLwY/R3sYpypEQpKrVsXZ0BehJOn5YqOESBe5ixX5lM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130609}, "main": "index.js", "gitHead": "507d9826349559ebbf425aa679207d18c5e51256", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "9.6.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_2.0.0_1522571220160_0.8013429191548453", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "arg", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "pranaygp", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"complexity": 0}}, "dist": {"shasum": "ded3abf645efd3b5f7e446035b75aad6fd68aa71", "tarball": "https://registry.npmjs.org/arg/-/arg-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-cvmPpB9OWbIP0pG3ov/11PMd/z+nBG+nY4eynzHxX/+pxJg7f5r02rFeJr8NwosJpxHIujA2jJpB+c3xR21vig==", "signatures": [{"sig": "MEQCIEPf7D/Uz8cOVumoXmC00cqFIDHMAUlLJrj095hzlIqUAiAGZ/xkXHKra7iF1JiPydaC259HTMtfWni1HtFMEneV7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7BCxCRA9TVsSAnZWagAAN/sP/izehzqP4nAis7EWF5Jf\nvkr0QkBKqMrBPHM3LBq0ZiCQADRGZtnRwRBTR1HH4iBId8PaTcQltYdBkAts\ncRhRVrhxnCrXZJ5bsiUdvbvALAaiGO+el7Y/2PxDvC7keXJqTgQRRmHtJuao\n9ls2D6v1HkDe4q3pMijxchlM7x/a2qzGlGhgb7BDq6n3gomndY2UU/X8WXCc\n05zIIQEoMmCiUln0ei2wkZ3DYEv5hhvXTDX48agNH7/JQ4TJQ2Uhydkko7Q9\n9CMz5d1R+ndO/cib32t1iKFhSj+bI+vgUIuj1X1HCRvmgAUQxCeUgTDQfN3A\nvz9tIAFL/zsCC/CfewY+RTwiqpCRIAdblNEYW1/shE6vL+An+RqvALdW6bZ1\nckzFKFGwRoxsxMqwfSkz26y7vozNqkfBLOup3xyK039jJbuv1hwJQ4HOw9Te\na/UgYYrK+DiTzMnMf878oi7WCxn+n9XqKzVMjQcGmoxNaOJw0T22hogrOkXE\ni0q+RN+ZWjVrz3zlhumhI/pjFK0YejNShJC53mOhxCaGz3CRYZ55O92kvBDC\nqrMU2q3rFTtr99Lj1g0L5SfS0qMTKwmETznJ+8samQ4nRAX2SW1wGu+SiYSV\ni1Vm1Sp7Iv+EY7+2AIlxHh2fGjnD4DccF+XxLy1iha/7Y2c3NDa9zsVUaTnm\nAErg\r\n=6dpK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b2c0da56c435b0ad0f11a746e582ea84f88fb1a0", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "timneutkens", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.7.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_2.0.1_1542197425269_0.1936038245972549", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "arg", "version": "3.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "johno", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lipp", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"complexity": 0}}, "dist": {"shasum": "386c20035dfbeb13e280ca8a1e6868aa17942def", "tarball": "https://registry.npmjs.org/arg/-/arg-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-C5Scb477yHhNck9AFzW5RwAzS2Eqn0HR+Fv0pmcZBXBT8g/g7OOuZTr0upVSSUGWZQH+XWdAKIw2OfC86EuggQ==", "signatures": [{"sig": "MEUCIQDOtqQGTpbjLN5itkNjLCEzUh5KAV5ipWDbL5gEkzWZ9AIgA1EznL331/frJudAIJIbV8L4QUXWmZE00Aoxk8S09gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcE6fXCRA9TVsSAnZWagAAdKAP/jLT+N50we6ebLj4vKUP\nNqqZOj9AdfD6XBOB/GpQPr3c8lHhhe2+pz7bV0w7FB2GDsI9PcoK43GJuD3y\nm7QepZqhA53i/MXmtOkt0JCEdqwDcL5MuvyuXm/pEMzmqr28qEXsPVdW1HqH\nUhcTj+6+EhVbDssROqJtPAoPD1ROa6tjpXGK5HckT4IQaTct+Ac5/6K62tRH\n57/eszHHCGwx00ovHoOlPQyCiOyVFJto9Q49RmFeLUx79eh0anDVIBZNQTQf\ndwPsMG0O3bQyZb+zZ3NJ7lPeztfWM+098ZY7RoQ9gpsPXJNleGGuMe1gPyOE\nsG4a0NpeqA+EpDiWXti05aIEQysi3d7RobOjL+Sd02joXZZuSZuwrqYF98wn\n7wVzxkO7GOpfkKCSCnmAMwAhR+qiTXmn56TnyQpGkVWwnTDKlWv0rM+vGmgH\nZUARPZVW2rmzj9l9fN8p7lgfKHYTKE2qUtKzQ/zJqf88cIV/vHvwkpuAETfY\njMHCBjdW1NFtpvEgo633M7Lqs/ukCjtntXoHkDH2t1y5v5jjUi2BQQhFo8ld\n8TbVvKMG8X27iIz9PRUhd0kAhqRtgqIm0VupiYYJFEHH1aTV3exqDNr8KByC\nQnffATvmAE3Jlp7DCvllyuYMvQGwl5zfy105uUp9f0S9vGxFa49HAu8tEOOI\nzw5S\r\n=2s7z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "30826caf576c9398991d491d3148a72cdf0d0aa5", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "timneutkens", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.7.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_3.0.0_1544792022944_0.5736200505986897", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "arg", "version": "4.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lipp", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0}}, "dist": {"shasum": "dffcf2b647587e65754f95105a89c5c47419653d", "tarball": "https://registry.npmjs.org/arg/-/arg-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-beu38yBAP+6n5KPnEOx1xw6TUv0aCZSPwaYhnjG7Vf4vq0N/ry2ZcGJwPEud/0fNFi9Z4zpt97sboMQhS5kTsQ==", "signatures": [{"sig": "MEQCIDCRyT3CDdTNG83LwuW+QVAdd+G6Dk4g5ZPb6f8/iTsCAiAp8NnfykUhcpptW7rlvwRQYYWwFH0Qb9W9w6HjGJo1Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJpAECRA9TVsSAnZWagAAbi8P/A/OSVrCVCSAKcUfG0b8\nT/laweP+MIwLI/wpHTBaowO1gBNKdYhH2Isktf5Aoa8HowaSeqdMG7d9L1zM\nsjCm/SSMZpga/tFz+AU/17TZnZOeug27snv5qmziDkWkyrIouzt8Hw+6mJ01\ntGqkFOecgnjjXC9EvF+ytmaNOIhMCjPC+6ibopBwtcRD85X/wqh/ybx2yRDk\npVx477+ByQ9SI4Trm6DFNyzXEnT3G50wO3PO6AHmGGit490+4GzzfI7+laUz\nJ2Rh9egEiOQAzXyxXGHCgm12H0zX0xLfD79hAc5EVInaRjnXucsW5coRcKC8\nzjAr6eIh5e6XuavFxg2uyjVylhXu0tGesNFy/hTohoXHzrhavHr2/8Kfc5TE\n3Vz00FH96PDajpdUxwTSSBS5JDcIMGT41XAxHO4Hl9RyOlCeGNlQypQXkVdA\ngG+EcTmGelZNL15NToxyapS0C5dMuEoLtFShB1bgGzcm7cr8y+/JiyHoDpYQ\n95bwNfglYpuCz/iOCZcscOCNCpcITsJQR0HbOLVGKq+Hk8u5o0Zxt0atdSwF\ndCPuknx1B34VVUAzcV+LtyKDRczw/bcgw8HSWWDdGfc+km5ttQdg8+JY4km2\njhMSIe8X4/EMDXSFw1nIgbau91MKNEZ/0gFJdA0403Duciuc7mF6sWThxTVC\nI8O9\r\n=Lm1f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "f17ffd2ce4fe2aff9deabf3f008195fdba7a6f5d", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.14.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_4.0.1_1546031107922_0.3296507786623166", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "arg", "version": "4.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jamo", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lipp", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0}}, "dist": {"shasum": "583c518199419e0037abb74062c37f8519e575f0", "tarball": "https://registry.npmjs.org/arg/-/arg-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-ZWc51jO3qegGkVh8Hwpv636EkbesNV5ZNQPCtRa+0qytRYPEs9IYT9qITY9buezqUH5uqyzlWLcufrzU2rffdg==", "signatures": [{"sig": "MEUCIQCptoMrtjn7WJlJqpfrAjcbdYCk3uqxl3S6XK57Q6BEbgIgKpVT2cEZZ900jz3ftF4+eLa2dK5b9h51+LbTV5TCabE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLIogCRA9TVsSAnZWagAAA2wP/3NNxMt2KdSZp02gUtRc\nJZ0MTR2RqoEXjweRJ+bIkmaDT3o7IGYeEpJjVGLYZpl8AVSFr7vmXL29iiNH\nd9QnYrjUvhHoLcJndg55IGSu8MdublPwRfd2R9aAKjoX8AN1hn8gGlN8y/VB\nxwMtA1Zqm+MmOK1LXELJfkfdLnAlx5VBMSC583mHK46YcA+fgjII7MZsPafV\n+Q7HStFH9/SF6WMZNnrc3cKygxC1cCD1WNB0VPlDZqq+epS5/Yqvwubctbbp\nQ+33yFmIhcsTgLvoBhOlYwyNF2pn2uGCK37xWCvEFzDjBRsCcUdKeuJGxf4q\nEljyLiMy/IqWjL0db3sF/8wg+esFqrFVtOPr4TKRvu3Cb3wuMbllaE23gwH7\n78PTakHW+EeZCgJIo3MDSIW9KWC/yLddqCzmZapoUYKtSF9nclRzkrns5zLl\ndnScAXB3mzdJuEYQ4r/Vq/hunKLJ3Zdy8/9EUUYUCbwIQJ6caSQJNnYnU4Us\niANKPqBgVHiY27a1kDhQ6j9aCcTo9EGsLKqvD1zj7aNX1TrIh4oMrt8tryDL\nysh4Z/vDWCkTBU7oDCevCAeRyKOiTWgaZn4GTE05p7ApyXG+jQ72g/VU286M\nzM9/g31WjWbzo4YfJZMPpYx3o/qPiQZb1Q3PgJTl18oQabCWrnEyi1DuO/Pb\nroaB\r\n=4KPQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "5e5b9bd4671ccf8952aee4e96e47288dd9fab0a1", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.14.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_4.1.0_1546422815737_0.8567037382412817", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "arg", "version": "4.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@4.1.1", "maintainers": [{"name": "alexaltea", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "caarlos0", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "huvik", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "manovotny", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sarupbanskota", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "t.sophe<PERSON><EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0, "no-div-regex": 0}}, "dist": {"shasum": "485f8e7c390ce4c5f78257dbea80d4be11feda4c", "tarball": "https://registry.npmjs.org/arg/-/arg-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-SlmP3fEA88MBv0PypnXZ8ZfJhwmDeIE3SP71j37AiXQBXYosPV0x6uISAaHYSlSVhmHOVkomen0tbGk6Anlebw==", "signatures": [{"sig": "MEUCIQDmCjPMo1iAXT+3GyXaFOFK4gzWhrsRk+UKtArVNbv4DAIgYxjRWEZVL1X+r3F1L4AB6lFAD6A0ZHZ0FKh5piOtktw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKGUVCRA9TVsSAnZWagAAjVcP/35h/Wk9M2KGu3wjwzRy\nIoQlJYwpgSNY31tKVT1GLB5mjZ8dG9Jxq7cCSmMiQe31LL8o3226siDB0fxL\nIzTlU4eETRiFEgxC9u/ljvRb5s7lavrhhuuMglfgKmPiFf2UifTUYy5IM9+2\n0+05788cmxtTgohqNG373ONL6gFaIdeqBiVwTkBXy82DieseEkDsc8v2DuF5\nSBzu00s9ksnEOM56I3k9J87NReS9Si2i03RApwHXgrTHPhV3mCUpt9s9TN3k\n4I9ck8d3jfJKmBMJ5u+727s5qC+47PuS4VRrxRE/auZahfXOGhJTbXV+LP6u\nB2YL5ob48gxzmGVn4/SolY+mxVVYx/+XqugEfJm456ZQNsNu87SYYWXrQApl\nActlodBHJt+Yl+8Wip5jRZ3TQOLuxKaOh6IjrVggJn+FOzDNaBKCwo2vfN0e\n75eBjJsXhdMWczZBQ85XLduNLpMcHCJ1FYdl4qb0n83F17jb9y16wv9shif2\nUU/zliRz/G76imWIEtqnmHyYQbqiPnspCQbU+Pwt+eD5It2IgIwMtGnfwzm+\nlydKQkH14j+8f1a90MWRUZ3OQ+dJmGixNuyvDWtEpVkeYcaBaJQXoVkO3DVg\noE27a6XRZciMRSjvHNkp9FjvQuqDBYoypgexvLe0p7uMBQTeZXgWRZlH9MCo\n07ez\r\n=qVME\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "e62c2ab8a0056504b9a20f0f1a037a436861c546", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_4.1.1_1562928405087_0.555279075456431", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "arg", "version": "4.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@4.1.2", "maintainers": [{"name": "alexaltea", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "caarlos0", "email": "<EMAIL>"}, {"name": "cleishm", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "manovotny", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sarupbanskota", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "t.sophe<PERSON><EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0, "no-div-regex": 0}}, "dist": {"shasum": "e70c90579e02c63d80e3ad4e31d8bfdb8bd50064", "tarball": "https://registry.npmjs.org/arg/-/arg-4.1.2.tgz", "fileCount": 5, "integrity": "sha512-+ytCkGcBtHZ3V2r2Z06AncYO8jz46UEamcspGoU8lHcEbpn6J77QK0vdWvChsclg/tM5XIJC5tnjmPp7Eq6Obg==", "signatures": [{"sig": "MEQCIAFfygR9dYcR6hEiwtZcYmKM55IqT7IelicBDp4RxmDSAiBUNAWbf3IL6R6w12YFS5/oJn31ONqGsRQsA6GSf8itqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3AfaCRA9TVsSAnZWagAA5d8P/2iBeRMjoL4AsVmUisxg\nh+sGZToijvubvV3NWhb010i1k3n21vDVu7sQaTQHRgEfio7OpuiKy6bjYB7R\nw2CHS5N94w+EUybxL8cH6cIEsJNiiPRduFOlV3qtE5J9zcM0x7X9v0s6ftkd\nD09rEPixPx4VwG5M2+6Tgs2Q7YdulB7lE1XQxr6S/OEEKNzwcruy6z5o+eu3\nJlt+gZPx1XQJqZ+yc0DjVZF7ImbhCF/MzXdQsRYq6RzImE+SEbzmU1r8f/wd\nIcSCNZl64hxV/xJPEjsImSb8U6pnMoC4yk7C9yyRCTo2F5KVf9gWD056RJPZ\nZayz4QDHnfpKqMHKp8ATyBfB6zyKK03LCRV1PFhIk73z0C7cVwwe3d1CEdYa\n+rKpISX7BZFpT9unTldbmZ7ChkfhtxZlODFoTpCYyShV4vb+IzDYeaZpp0hK\nL+zkGpCaqZH4CvnKjMqO7OIPL/oylOYqouk2G5bBuSnuMBzes698n47c7VA0\nEh94l33In7lwLzKoDClypmkj/qTna6rrVQFVIbtI2aUovZ01lsmoyOVClWIO\nQ69ZRgLVQhJknU9GeOn6Q51LgSxoY+qTiwh8bzmyReUOmu1Zn0ejApUUc8Tu\n7zKZxuNjh3MPeV3AsRyDb3DhouvIhWSmiGCjgIKYMf7Mif+XtteTRtRyYood\nv/Dz\r\n=y4sy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "f01c810d0d5b4284c9d999326ddedb23e55372ad", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_4.1.2_1574701017653_0.8580659126986387", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "arg", "version": "4.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@4.1.3", "maintainers": [{"name": "alexaltea", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "caarlos0", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "cleishm", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "manovotny", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sarupbanskota", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/arg#readme", "bugs": {"url": "https://github.com/zeit/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0, "no-div-regex": 0}}, "dist": {"shasum": "269fc7ad5b8e42cb63c896d5666017261c144089", "tarball": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "fileCount": 5, "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "signatures": [{"sig": "MEUCIQCfrSUeWZZkOG7WkelSXdQit234ge5WSpFHxs5Rgsum6gIgOJlPR1fprnYc9JO5IQUdDibNpNYHqDWkpArUff0YtsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNwyLCRA9TVsSAnZWagAACRwQAKPjS4mhwNPdU2Ayqtm8\nD9id8za+eqWYJEFbo+cz9MpWCDeCf5/2dyD43mei4XYqDCUeKfhxVffG2PXn\n+vj3SrrZybw/blI3Ut6dmCu8/FZmAgSNTotTytiAe/qxf1A2mLqVTHZ+sTZN\nZkpwnYJUDaFStIWRBWwKkrg5RHK2hQOpqz7b7mRfUgYCI+Fmf3n9sDlu8lpv\nuxuvgzO8sM+BGhKjD7kmcalN/oGf+CkaznY7kpaljWohqq5TdgUyK3MN8p8J\nLrU82+pZYL+jRDy0SHclXm6eisIxygAqojkM+mttrhKvzEfcoDyX6jIQ+Yxn\nmOGMhtHCMh9M7HHpfRdaxx6HKaulS0TlhukovtseW1+8Xulb1ou80zr5lSGf\nngo+aHDmMtPEnsL5JIaXWlre4lK1aG+p+ph6D3QIRUM0szSbVGW4XMnSSfvb\nS0LCrF+P6uMYJRlRmZO4IzWX5W4uh2ec7CsIyQ8A0xWH6iGfiXTfpT5Q7fu7\nic08I52ZSah7/HyXWUeV1uBBOSY5rBLcg8/3bEA4/XZKZZ3CxOfQHYincDSh\nZXclF4tfx8dctSDpzy5WonzfD5ijtr50sNJmuH2WNaPYNnrd5M2O6uDjsJps\nUtjPeswz7UEokLsneYI65EWedeL5D55V70J6BYPu1wst9SokL+fatsfonjwc\nm7eU\r\n=+xEG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "3ce0a32c783e47cca1c549ded84b48347bc5d4be", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/arg.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Another simple argument parser", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_4.1.3_1580665994480_0.37399324996184813", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "arg", "version": "5.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "cleishm", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/arg#readme", "bugs": {"url": "https://github.com/vercel/arg/issues"}, "xo": {"rules": {"max-depth": 0, "complexity": 0, "no-div-regex": 0}}, "dist": {"shasum": "a20e2bb5710e82950a516b3f933fee5ed478be90", "tarball": "https://registry.npmjs.org/arg/-/arg-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-4P8Zm2H+BRS+c/xX1LrHw0qKpEhdlZjLCgWy+d78T9vqa2Z2SiD2wMrYuWIAFy5IZUD7nnNXroRttz+0RzlrzQ==", "signatures": [{"sig": "MEYCIQCKL9TPJ48p85ZOm6tdFPrY0nD4g3wNDTmDhB/hCWNHOAIhAITW2bY1nTS1QLpOVojm7L1S5rVTM67l2N6T34GsJS+i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsyhlCRA9TVsSAnZWagAANXoP+wXZSCUz9gBM9tOF10pZ\nUn4nOrzgqCrLOZyRLyrRQ6PFFRnwu/XTeFy6P+oQG0RFT+wCBT9U6Y8KwTeb\n7fkStIOboERf5mkNsWklGWCtP/34ERpf/fcXohLUN5CPDgJ9FG9uIVv19To8\n8+1klHu9U8WPKm7fTyvwtiTr8SQCdFFFvi4XQQnqH2UI4uBKUBIv8Eq3HTOZ\nz1s2kKTU0KKsm7MifAYGw4tpMjo7ll0DWv7ysiOlxPy2bKgRfG+5x3iReHKy\nC6ctTdrfmLMWjOT8JFxc7p6tRu3WqABL+ayBrSdHgrkQGTnB+Zl7Oh3wX2OD\nCSwf+dd+it1qcH/HC9OMCYxsz5DvulGmMLLLpcF9e9uVgPfAGVpD6URqan25\nUvSfdm4EkA8zAwzY40Os/G6jGYd+3qk8KORpd5Rfk/lh7P+hvkOghQqV6TrJ\nuMVIg5KFDXw4BJ/weRAjP1WhBesLvyiiCHWlhzs7Dx3aNwtmZcLGPIxmB2Ex\ns4FjeELgEkdBHD1v/QYLVXY6ty7MBvPzSYqVgV7v8Jscz605YJLImpDJHkih\nCuZw5pVA6aa1fNWiALnriNTxzddU19ZU2oPtUtz0fgF+wxVUpcSdcvHJ/OD+\nWD0hPNwJgLrDhfFntCZGYZ2q//OqcUc6mv3uzxRK5Cd8M3hp8BIZAPKNLM1F\nTNUF\r\n=GgBc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "99b578ee3e7822c8e42a02907c02f58f5d2745e8", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2", "pretest": "xo"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/arg.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Unopinionated, no-frills CLI argument parser", "directories": {}, "_nodeVersion": "14.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.18.2", "chai": "^4.1.1", "jest": "^20.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/arg_5.0.0_1605576804746_0.3294240995491229", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "arg", "version": "5.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@5.0.1", "maintainers": [{"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "aspctub", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/arg#readme", "bugs": {"url": "https://github.com/vercel/arg/issues"}, "dist": {"shasum": "eb0c9a8f77786cad2af8ff2b862899842d7b6adb", "tarball": "https://registry.npmjs.org/arg/-/arg-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-e0hDa9H2Z9AwFkk2qDlwhoMYE4eToKarchkQHovNdLTCYMHZHeRjI71crOh+dio4K6u1IcwubQqo79Ga4CyAQA==", "signatures": [{"sig": "MEQCIAM83pwSdThb89pexfcKMrfk4X23EydZnZzPnFP6wRKjAiBLxhWpKbWwbg1bCCh1uPdv4Lw5azQYae2q9p3pdspFgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGxtVCRA9TVsSAnZWagAARt8P/jtu/qr+yyDMb5C5oMLW\nw8i3MDm7X5KO9FXI59YOETM25C++5dBmM6F/QYXouWq2VtK0BQ3tOgXFHX2T\nuNLwmPtU0SiXb3WeIJsIM8OJZ/siSoon3kTuaeBGj+OqTanmGTGIbxyEGy8y\nyd3eY09Sq7MihsvGH+Vyo7xowr+8VGGkOnya8McC34iTlFEqgDoZcIor6MC8\n0gU66onfl/CXW1NDWTZI+XO6Ye9LfCcrlRhNeocMekeeaJNKtUwAlSuTL4uQ\nHW3dYLzJ9rnurdhPekuXOsG5udYAUrRPVG5v5eQ08MMQyo8IYl5VJ0fKX1vZ\nMRMQhmPslv1io2dImre+kAOIsxEPibrHCZD3I2RwqfkVzg9UJoqQB6Elqe6m\nNNKN20ARyZ/0wAV7Taj1aCSqBsoK+08XHBF1vCVMo+IGj/kbTMwap36HVJVf\nTsd3HYXpXVQD7qx9BwV+FVAQ0wQ7TX9oVF+R5l0iwwTLIqhefds3ebOkrmaZ\nfiWDo9c0IXZNPAO/iyDYhgPk2oWAIWWJW8x7SQI229XrqDFpXxXIsdQLqdbF\nvgO0tKjRuC0ycSWTl7X1/SUTvUI5v2q3rHXklnZTiY4KGAHhr9P6RKqBbneI\nT8mnBNgo3AqTHS1iA20R4jPTYpSB9wFikOkXrQhv3Ei8ZPFB364NDgeSjtO+\noZuf\r\n=wsol\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "c1109765e0061ea41211a646fbbd8ff11bee2944", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "prettier": {"tabWidth": 2, "arrowParens": "always", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/vercel/arg.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Unopinionated, no-frills CLI argument parser", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "jest": "^27.0.6", "prettier": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/arg_5.0.1_1629166421795_0.6769869375980777", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "arg", "version": "5.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "arg@5.0.2", "maintainers": [{"name": "<PERSON><PERSON>born<PERSON>", "email": "<EMAIL>"}, {"name": "lubakravche", "email": "luba.kra<PERSON><PERSON>@vercel.com"}, {"name": "aaronbrown-vercel", "email": "<EMAIL>"}, {"name": "denizkusef", "email": "<EMAIL>"}, {"name": "javierbyte", "email": "<EMAIL>"}, {"name": "kayernyc", "email": "<EMAIL>"}, {"name": "janory", "email": "<EMAIL>"}, {"name": "goncy", "email": "<EMAIL>"}, {"name": "co<PERSON><PERSON>den", "email": "<EMAIL>"}, {"name": "feedthejim", "email": "<EMAIL>"}, {"name": "tilly3g", "email": "<EMAIL>"}, {"name": "wits", "email": "<EMAIL>"}, {"name": "feugy", "email": "<EMAIL>"}, {"name": "gbibeaulaviolette", "email": "<EMAIL>"}, {"name": "megbird", "email": "<EMAIL>"}, {"name": "<PERSON>up", "email": "<EMAIL>"}, {"name": "vin-e", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "l<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sambe<PERSON>", "email": "<EMAIL>"}, {"name": "craigandrews", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kale-stew", "email": "<EMAIL>"}, {"name": "chloe.tedder", "email": "<EMAIL>"}, {"name": "pbto", "email": "<EMAIL>"}, {"name": "daniel.campbell", "email": "<EMAIL>"}, {"name": "arian-vercel", "email": "<EMAIL>"}, {"name": "nuta", "email": "<EMAIL>"}, {"name": "almonk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "sam.selik<PERSON>@gmail.com"}, {"name": "d<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "baruchadi", "email": "<EMAIL>"}, {"name": "ejc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doque", "email": "<EMAIL>"}, {"name": "ryanto", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "john<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tk<PERSON>man", "email": "<EMAIL>"}, {"name": "agad<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thomcrowe", "email": "<EMAIL>"}, {"name": "emeraldsanto", "email": "<EMAIL>"}, {"name": "ecklf", "email": "<EMAIL>"}, {"name": "timeyoutakeit", "email": "<EMAIL>"}, {"name": "cramforce", "email": "<EMAIL>"}, {"name": "balazs4", "email": "<EMAIL>"}, {"name": "casey.gowrie", "email": "<EMAIL>"}, {"name": "samuel.foster", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "swarna<PERSON><PERSON>up<PERSON>@gmail.com"}, {"name": "lyd<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kaka<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "amanhimself_", "email": "<EMAIL>"}, {"name": "endangered<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nick.tracey", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}, {"name": "schlez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "epallerols", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "n<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "arbw", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marc<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "falcoagustin", "email": "<EMAIL>"}, {"name": "nabsul", "email": "<EMAIL>"}, {"name": "bme<PERSON>", "email": "blake<PERSON><PERSON>@gmail.com"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "breth<PERSON>on", "email": "<EMAIL>"}, {"name": "matt.straka", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON>@vercel.com"}, {"name": "f3d0r", "email": "<EMAIL>"}, {"name": "gaspar09", "email": "<EMAIL>"}, {"name": "jtaylor0196", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>er", "email": "<EMAIL>"}, {"name": "kelly", "email": "<EMAIL>"}, {"name": "dferber90", "email": "<EMAIL>"}, {"name": "healeycodes", "email": "<EMAIL>"}, {"name": "broph123", "email": "<EMAIL>"}, {"name": "codybrouwers", "email": "<EMAIL>"}, {"name": "gdborton", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ebb-tide", "email": "<EMAIL>"}, {"name": "msimulcik", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nutlope", "email": "<EMAIL>"}, {"name": "hungrybearstudio", "email": "<EMAIL>"}, {"name": "codetaromiura", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "dglsparsons", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vercel-release-bot", "email": "<EMAIL>"}, {"name": "southpolesteve", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "kdy1", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/arg#readme", "bugs": {"url": "https://github.com/vercel/arg/issues"}, "dist": {"shasum": "c81433cc427c92c4dcf4865142dbca6f15acd59c", "tarball": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz", "fileCount": 5, "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==", "signatures": [{"sig": "MEUCIFKy/cnh3S48XIzfhBiQxypBglkxns6H9PE3zqA5rPgFAiEAzA8huTdOsTuiZjX7fg8SZg2dBOXlxHTZg5jXK3hDE40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinPPZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWHg//c7NDJyQYCxH5Rd/1VGlpG4216t/6Y6NxD8ONX5ke2DJaeygk\r\n/u/WlglGbOx9jyQEZSVXxKsMW7juYYaet9X9cQ7dzUgSEWgYGM8z09oSDDl4\r\nz7kazg1l7EoxeGgd+4vFVnAalKY9NdNbdbpMmJq6l9MIX0YzMW/bcIVcMk/B\r\nnTk9z7VFHmVnleNixTucW6+c3y8qVbmfjegGWY4XldFcrkRvYiQ+6ScmfQ2d\r\n4nqkW+A6s85AP67CFvWAJrm7rLaMOAFDyp6JBug5xckekSLaP1d1m/wySaEB\r\nthchzp8BpB/GKB6gXvQQ920KVYS3kKq1NBTX91LylbtaVpz0X9e54EjU2lph\r\nvfJ7pabhDQfXprwFPe8tYrtR8ozlS81oeua8K0IvCJjloSa61JDgEfc1hY9/\r\nEPPS+0Dp2Oby1H4fojw6JHf7JTotYUVl44pDSVj8IjxbIEPeN2UJ7fNEDrFC\r\nwcv2EAWTwKgjHmKE576KHjSky2oqt7QSyGF5tUxSKeRd8SdmrwwjNoz4BtJ2\r\n/a/TliU+5k6/RoHryHfdBFGHr0EWaJ+XG8eI8o8nrLrrVa7o0wnhj6Fo409y\r\nUK0yqaf4iQYxusmQugCshTb2awrt2RZPdETiygt652Jb5pt9m06aw/lEPCEa\r\njG7atKPoD48ryGsIkZQw8rRjLB+0hcYYyOE=\r\n=kxpn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "20e630a8a581dfe7ac9a47290b9be66d2ac5c752", "scripts": {"test": "WARN_EXIT=1 jest --coverage -w 2"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "prettier": {"tabWidth": 2, "arrowParens": "always", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/vercel/arg.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Unopinionated, no-frills CLI argument parser", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "jest": "^27.0.6", "prettier": "^2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/arg_5.0.2_1654453208991_0.39616482612140946", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-06-01T15:39:02.676Z", "modified": "2025-01-31T01:55:09.080Z", "0.0.1": "2012-06-01T15:39:03.936Z", "1.0.0": "2017-10-24T07:12:48.302Z", "1.0.1": "2018-03-09T02:13:03.219Z", "2.0.0": "2018-04-01T08:27:00.291Z", "2.0.1": "2018-11-14T12:10:25.430Z", "3.0.0": "2018-12-14T12:53:43.156Z", "4.0.1": "2018-12-28T21:05:08.072Z", "4.1.0": "2019-01-02T09:53:35.942Z", "4.1.1": "2019-07-12T10:46:45.233Z", "4.1.2": "2019-11-25T16:56:57.731Z", "4.1.3": "2020-02-02T17:53:14.581Z", "5.0.0": "2020-11-17T01:33:24.907Z", "5.0.1": "2021-08-17T02:13:41.950Z", "5.0.2": "2022-06-05T18:20:09.200Z"}, "bugs": {"url": "https://github.com/vercel/arg/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/vercel/arg#readme", "repository": {"url": "git+https://github.com/vercel/arg.git", "type": "git"}, "description": "Unopinionated, no-frills CLI argument parser", "maintainers": [{"email": "<EMAIL>", "name": "matheuss"}, {"email": "<EMAIL>", "name": "nick.tracey"}, {"email": "<EMAIL>", "name": "vercel-release-bot"}, {"email": "<EMAIL>", "name": "zeit-bot"}, {"email": "<EMAIL>", "name": "matt.straka"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON>"}], "readme": "# Arg\n\n`arg` is an unopinionated, no-frills CLI argument parser.\n\n## Installation\n\n```bash\nnpm install arg\n```\n\n## Usage\n\n`arg()` takes either 1 or 2 arguments:\n\n1. Command line specification object (see below)\n2. Parse options (_Optional_, defaults to `{permissive: false, argv: process.argv.slice(2), stopAtPositional: false}`)\n\nIt returns an object with any values present on the command-line (missing options are thus\nmissing from the resulting object). Arg performs no validation/requirement checking - we\nleave that up to the application.\n\nAll parameters that aren't consumed by options (commonly referred to as \"extra\" parameters)\nare added to `result._`, which is _always_ an array (even if no extra parameters are passed,\nin which case an empty array is returned).\n\n```javascript\nconst arg = require('arg');\n\n// `options` is an optional parameter\nconst args = arg(\n\tspec,\n\t(options = { permissive: false, argv: process.argv.slice(2) })\n);\n```\n\nFor example:\n\n```console\n$ node ./hello.js --verbose -vvv --port=1234 -n 'My name' foo bar --tag qux --tag=qix -- --foobar\n```\n\n```javascript\n// hello.js\nconst arg = require('arg');\n\nconst args = arg({\n\t// Types\n\t'--help': Boolean,\n\t'--version': Boolean,\n\t'--verbose': arg.COUNT, // Counts the number of times --verbose is passed\n\t'--port': Number, // --port <number> or --port=<number>\n\t'--name': String, // --name <string> or --name=<string>\n\t'--tag': [String], // --tag <string> or --tag=<string>\n\n\t// Aliases\n\t'-v': '--verbose',\n\t'-n': '--name', // -n <string>; result is stored in --name\n\t'--label': '--name' // --label <string> or --label=<string>;\n\t//     result is stored in --name\n});\n\nconsole.log(args);\n/*\n{\n\t_: [\"foo\", \"bar\", \"--foobar\"],\n\t'--port': 1234,\n\t'--verbose': 4,\n\t'--name': \"My name\",\n\t'--tag': [\"qux\", \"qix\"]\n}\n*/\n```\n\nThe values for each key=&gt;value pair is either a type (function or [function]) or a string (indicating an alias).\n\n- In the case of a function, the string value of the argument's value is passed to it,\n  and the return value is used as the ultimate value.\n\n- In the case of an array, the only element _must_ be a type function. Array types indicate\n  that the argument may be passed multiple times, and as such the resulting value in the returned\n  object is an array with all of the values that were passed using the specified flag.\n\n- In the case of a string, an alias is established. If a flag is passed that matches the _key_,\n  then the _value_ is substituted in its place.\n\nType functions are passed three arguments:\n\n1. The parameter value (always a string)\n2. The parameter name (e.g. `--label`)\n3. The previous value for the destination (useful for reduce-like operations or for supporting `-v` multiple times, etc.)\n\nThis means the built-in `String`, `Number`, and `Boolean` type constructors \"just work\" as type functions.\n\nNote that `Boolean` and `[Boolean]` have special treatment - an option argument is _not_ consumed or passed, but instead `true` is\nreturned. These options are called \"flags\".\n\nFor custom handlers that wish to behave as flags, you may pass the function through `arg.flag()`:\n\n```javascript\nconst arg = require('arg');\n\nconst argv = [\n\t'--foo',\n\t'bar',\n\t'-ff',\n\t'baz',\n\t'--foo',\n\t'--foo',\n\t'qux',\n\t'-fff',\n\t'qix'\n];\n\nfunction myHandler(value, argName, previousValue) {\n\t/* `value` is always `true` */\n\treturn 'na ' + (previousValue || 'batman!');\n}\n\nconst args = arg(\n\t{\n\t\t'--foo': arg.flag(myHandler),\n\t\t'-f': '--foo'\n\t},\n\t{\n\t\targv\n\t}\n);\n\nconsole.log(args);\n/*\n{\n\t_: ['bar', 'baz', 'qux', 'qix'],\n\t'--foo': 'na na na na na na na na batman!'\n}\n*/\n```\n\nAs well, `arg` supplies a helper argument handler called `arg.COUNT`, which equivalent to a `[Boolean]` argument's `.length`\nproperty - effectively counting the number of times the boolean flag, denoted by the key, is passed on the command line..\nFor example, this is how you could implement `ssh`'s multiple levels of verbosity (`-vvvv` being the most verbose).\n\n```javascript\nconst arg = require('arg');\n\nconst argv = ['-AAAA', '-BBBB'];\n\nconst args = arg(\n\t{\n\t\t'-A': arg.COUNT,\n\t\t'-B': [Boolean]\n\t},\n\t{\n\t\targv\n\t}\n);\n\nconsole.log(args);\n/*\n{\n\t_: [],\n\t'-A': 4,\n\t'-B': [true, true, true, true]\n}\n*/\n```\n\n### Options\n\nIf a second parameter is specified and is an object, it specifies parsing options to modify the behavior of `arg()`.\n\n#### `argv`\n\nIf you have already sliced or generated a number of raw arguments to be parsed (as opposed to letting `arg`\nslice them from `process.argv`) you may specify them in the `argv` option.\n\nFor example:\n\n```javascript\nconst args = arg(\n\t{\n\t\t'--foo': String\n\t},\n\t{\n\t\targv: ['hello', '--foo', 'world']\n\t}\n);\n```\n\nresults in:\n\n```javascript\nconst args = {\n\t_: ['hello'],\n\t'--foo': 'world'\n};\n```\n\n#### `permissive`\n\nWhen `permissive` set to `true`, `arg` will push any unknown arguments\nonto the \"extra\" argument array (`result._`) instead of throwing an error about\nan unknown flag.\n\nFor example:\n\n```javascript\nconst arg = require('arg');\n\nconst argv = [\n\t'--foo',\n\t'hello',\n\t'--qux',\n\t'qix',\n\t'--bar',\n\t'12345',\n\t'hello again'\n];\n\nconst args = arg(\n\t{\n\t\t'--foo': String,\n\t\t'--bar': Number\n\t},\n\t{\n\t\targv,\n\t\tpermissive: true\n\t}\n);\n```\n\nresults in:\n\n```javascript\nconst args = {\n\t_: ['--qux', 'qix', 'hello again'],\n\t'--foo': 'hello',\n\t'--bar': 12345\n};\n```\n\n#### `stopAtPositional`\n\nWhen `stopAtPositional` is set to `true`, `arg` will halt parsing at the first\npositional argument.\n\nFor example:\n\n```javascript\nconst arg = require('arg');\n\nconst argv = ['--foo', 'hello', '--bar'];\n\nconst args = arg(\n\t{\n\t\t'--foo': Boolean,\n\t\t'--bar': Boolean\n\t},\n\t{\n\t\targv,\n\t\tstopAtPositional: true\n\t}\n);\n```\n\nresults in:\n\n```javascript\nconst args = {\n\t_: ['hello', '--bar'],\n\t'--foo': true\n};\n```\n\n### Errors\n\nSome errors that `arg` throws provide a `.code` property in order to aid in recovering from user error, or to\ndifferentiate between user error and developer error (bug).\n\n##### ARG_UNKNOWN_OPTION\n\nIf an unknown option (not defined in the spec object) is passed, an error with code `ARG_UNKNOWN_OPTION` will be thrown:\n\n```js\n// cli.js\ntry {\n\trequire('arg')({ '--hi': String });\n} catch (err) {\n\tif (err.code === 'ARG_UNKNOWN_OPTION') {\n\t\tconsole.log(err.message);\n\t} else {\n\t\tthrow err;\n\t}\n}\n```\n\n```shell\nnode cli.js --extraneous true\nUnknown or unexpected option: --extraneous\n```\n\n# FAQ\n\nA few questions and answers that have been asked before:\n\n### How do I require an argument with `arg`?\n\nDo the assertion yourself, such as:\n\n```javascript\nconst args = arg({ '--name': String });\n\nif (!args['--name']) throw new Error('missing required argument: --name');\n```\n\n# License\n\nReleased under the [MIT License](LICENSE.md).\n", "readmeFilename": "README.md", "users": {"xiechao06": true, "flumpus-dev": true}}