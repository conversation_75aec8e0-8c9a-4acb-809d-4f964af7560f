{"_id": "react-icons", "_rev": "131-ee8e7045ecaae115cd7ecae2bd629e19", "name": "react-icons", "dist-tags": {"beta": "4.8.1-snapshot.2", "latest": "5.5.0"}, "versions": {"0.0.1": {"name": "react-icons", "version": "0.0.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.0.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "60985b98b68eaf73b9755d7b074bcc8e7ad543b8", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.0.1.tgz", "integrity": "sha512-th2JcLUadN40SmSv3IStKBera+XXTGGFnTJ5PQ2mfL5tkdb5I1de8R5dLqE2DNoFPM8Oldk9b6H6EvNqnxZyaA==", "signatures": [{"sig": "MEUCIENi6VA0Xn4jQ75HwrltNb5cOyGXc9L0SpurTsHteYk1AiEAxzkjrL3FFy114oGY3POrIsKhsdaPnDSkHhFGjQMJ3+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "icons.js", "_from": ".", "_shasum": "60985b98b68eaf73b9755d7b074bcc8e7ad543b8", "gitHead": "968978b2f3e8f109ee2f6d0be4a778970a54d65a", "scripts": {"build": "node build.js"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "esnext inline react icons", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "underscore": "^1.8.3"}}, "0.0.2": {"name": "react-icons", "version": "0.0.2", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.0.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "281627434d59e70a6654470338472688f299cfd6", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.0.2.tgz", "integrity": "sha512-j9b8lmB/+gCkijHI9jYdkapuu7jYsEP8N+0akMITMDPWQwbdBxiuV3X4SLUtwNlvqE+zdE2+P662tC4gP9BI/w==", "signatures": [{"sig": "MEUCIGliM3Uyt0to5P/eDQ865O1NYblAuze1nYpQAi4p7x19AiEA6oWxqPvhDT7Y9Qa4sWP4P2OHYTdhoFVKG1rE/r+kyqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "icons.js", "_from": ".", "_shasum": "281627434d59e70a6654470338472688f299cfd6", "gitHead": "5d24a10e379e1c215128dd39a94052556ed48c01", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/build.js"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "esnext inline react icons", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "babel": "^5.8.23", "react": "^0.14.0", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "react-dom": "^0.14.0", "capitalize": "^1.0.0", "classnames": "^2.2.0", "underscore": "^1.8.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "material-design-lite": "^1.0.5"}}, "0.0.3": {"name": "react-icons", "version": "0.0.3", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.0.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "aa8ded33ef5e332fd8dae16f8b19c111adbc9740", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.0.3.tgz", "integrity": "sha512-PsRYsh00aueBHv2GsxmIwUc4HcsgwHKJbU+iIJUmucvKiOsndbYBou4d+Schl2x/+XqJUvzuP3ZvPkJxP0quqA==", "signatures": [{"sig": "MEUCIQCWlpHTjFHC+8+cfEnV+bjrXJhcG+egO0snkZpg/o0RdAIgLSxv2B4nTfKhaZJxpuxyb6CK/twgAQb5TKbJqmKuXok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "icons.js", "_from": ".", "_shasum": "aa8ded33ef5e332fd8dae16f8b19c111adbc9740", "gitHead": "6d73141069d0df7f503b1894a7f9add9de53ab2c", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/build.js"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "babel": "^5.8.23", "react": "^0.14.0", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "react-dom": "^0.14.0", "capitalize": "^1.0.0", "classnames": "^2.2.0", "underscore": "^1.8.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "material-design-lite": "^1.0.5"}}, "0.0.4": {"name": "react-icons", "version": "0.0.4", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.0.4", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "358180e34054b8393b29cdb2b45b506437aee149", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.0.4.tgz", "integrity": "sha512-UNt4HwFBGd/2XI8EiyX9Miq4H7v8z+M/Cb/l1ThpwV5CcMtEETBeuVa9bCo4aeUFsIH8o1lBxeWjPNERkRbHHQ==", "signatures": [{"sig": "MEUCIF55D7l8CWkxT05MP26AbYE9prBSClxQbTDyjX6kPKJAAiEA1fcCNvjp1j4mL4zX0+VhcpvAh+UlT59MRylc2z8p/CM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "icons.js", "_from": ".", "_shasum": "358180e34054b8393b29cdb2b45b506437aee149", "gitHead": "80204024adbd5554572317686b076009aa2decc2", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "npm run create | xargs babel --out-dir lib", "create": "node ./bin/create.js"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "babel": "^5.8.23", "react": "^0.14.0", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "react-dom": "^0.14.0", "capitalize": "^1.0.0", "classnames": "^2.2.0", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}}, "0.1.0": {"name": "react-icons", "version": "0.1.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.1.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "0ad32b456f4f0a6284d554c369a7887b69c6835c", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.1.0.tgz", "integrity": "sha512-iTvu2rUg0EakJu/I+Rmb6IXgPJ8PhM3t0ZjmMWZT5FFwuLWn266r8f+cXevrsIyIhcmRNvCz+N5hqn0JFRgN3A==", "signatures": [{"sig": "MEUCIQDMuzYM6pXgo4/O6FZugR12gMwY7R7WvCi4SQVJl0L5PwIgBrgo2gh2qA1Qc6OPq59ZbSDoqpXl+/NdlNYEApuekXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "icons.js", "_from": ".", "_shasum": "0ad32b456f4f0a6284d554c369a7887b69c6835c", "gitHead": "01ae694fb37248e3762177717bd056b98bb59dc4", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "create": "node ./bin/create.js"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "babel": "^5.8.23", "react": "^0.14.0", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "react-dom": "^0.14.0", "capitalize": "^1.0.0", "classnames": "^2.2.0", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}}, "0.1.2": {"name": "react-icons", "version": "0.1.2", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.1.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "1eafe0bf4bae0a56818710a1e96898e582cca5c5", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.1.2.tgz", "integrity": "sha512-Gl96aoCe0Sk9E7XRLJNaxKjDpOI4XqOhqnL3HUMg1sPALtSWQsGyxZFPd9MPIEZZdHX5iprefJDAPz1BHkAoNQ==", "signatures": [{"sig": "MEUCIBlryYowFzjdWoGXh/2c2M311bgtwCGWt5lVPCkH8QGaAiEA1tV05omqOLa+MKgYrvFsZQo0BeCoPNEkocs2/ywGEW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "1eafe0bf4bae0a56818710a1e96898e582cca5c5", "gitHead": "d61f077ad8e805dfdfe2c1fe0490c9aaac327041", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "babel": "^5.8.23", "react": "^0.14.0", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "react-dom": "^0.14.0", "capitalize": "^1.0.0", "classnames": "^2.2.0", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}}, "0.1.3": {"name": "react-icons", "version": "0.1.3", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.1.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "ee3c833b530d7e62b6257c9cbd5af2a9cfd5afac", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.1.3.tgz", "integrity": "sha512-bshjuQESLMEy7EowCD/JImpRjfywB/P0AreTVW2M5lYlRBb4pn5FOQfd3D1ABlZx/bPdmXWP4yyVXUTPt1Z9Jw==", "signatures": [{"sig": "MEUCIFh3C0oIomJ7S35TzitY4Ultwp3R18z/Lqy92u2x5zcXAiEAigZbInwlp79BE2dKf49AciSLyyXSjveIcONrKFDVhvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "ee3c833b530d7e62b6257c9cbd5af2a9cfd5afac", "gitHead": "e0de7128c1a73b82af58b4c2ddcfd73be133cb9b", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "0.0.4"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.1.4": {"name": "react-icons", "version": "0.1.4", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.1.4", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "033c3aab3272c4531a5c038b4338a8d3deed59b0", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.1.4.tgz", "integrity": "sha512-CgZqh5zICWOD89l6WwP9W4YXfC/FiN2Lqc2TrxTG/+qKkGQjVEN+NoyOo4jSBItxC4YH11YGa/DWXZ4tJT6e1w==", "signatures": [{"sig": "MEUCIGc1ZTM7ti3Owz97pLIclL1WhxCpuqignAAiziBiH0qzAiEA9Hrb0NjciarPom+aD5m9WDhHxeohrC+co+Dp7Jzgwr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "033c3aab3272c4531a5c038b4338a8d3deed59b0", "gitHead": "373b3d845d6d3813433b230103df48e70a648d07", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "0.0.5"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.1.5": {"name": "react-icons", "version": "0.1.5", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.1.5", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "f65825b0122b87a6da75a5d4a479e28db3e9c821", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.1.5.tgz", "integrity": "sha512-VzW6mAMlia/cMTklFsAxveDm+x6jgBZ6g0oKqtO5k6fVrD5r4EE0DJveax5OLr7WfhcVa+ux7/CiVUx+X1CBDg==", "signatures": [{"sig": "MEQCIHPqB57CH6WpIOoJPGCe5q3f3enu+t1cASc84NkXAFpiAiAN5WvxHz/006GmF6IQ2EgG43/8M56sFOojsNCIRQdhaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "f65825b0122b87a6da75a5d4a479e28db3e9c821", "gitHead": "e58a2b397cdeac3b91150c1896a20da4f6f8bbe4", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "0.0.6"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.2.0": {"name": "react-icons", "version": "0.2.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.2.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "b042a55fce0d149bb1b86bcd19ac20306709b940", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.2.0.tgz", "integrity": "sha512-gqQwnFfXGXCg1yYYvLH2rgDAHEYW0ofg1RvpNpZ6wkuUKNWOtZ+i4XEONJR9Yvb3HxxcfwzTVGy602R1FdQe/w==", "signatures": [{"sig": "MEUCIAx0wg4If2H0cP+9w6yJQeJE7lQ4bYDEqOdOHKs25OHCAiEAulebpbLa37Onv3MHih1hOyXiloS+tggNg7nx5Ve+Ws0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "b042a55fce0d149bb1b86bcd19ac20306709b940", "gitHead": "be86ab046d8aaf0c25b75fe597e30530b095d88f", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"decamelize": "1.1.1", "react-icon-base": "0.0.6"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.2.1": {"name": "react-icons", "version": "0.2.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.2.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "72fe5220d953671bcf7bb1aa8656e61fa6e31b71", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.2.1.tgz", "integrity": "sha512-pCgu/kjVt5A8PKIuBxuJJe2kCVy4fPnmNpwjWfyP1Tjzwu342/cyOEoT+w4Vj7ma+lIvtYXPQakoFIm9Q7AeTQ==", "signatures": [{"sig": "MEYCIQDeylsE97A4XXYi9OgtDJN5l1j3FVG50xD7U+MInYF4xwIhALqJmrhXdDQJ8j5PDt889afZ93sBKgj4vKsTTlhUONVH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "72fe5220d953671bcf7bb1aa8656e61fa6e31b71", "gitHead": "4a33e69d6f0d94e768db11cded0fcf7c3175a2b7", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"decamelize": "1.1.1", "react-icon-base": "0.0.7"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.2.2": {"name": "react-icons", "version": "0.2.2", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.2.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "81aa01f73bfa5e873b673904bd2a6daab74bae1a", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.2.2.tgz", "integrity": "sha512-iV2bMFQjcNjwgHfKZRsxUaBMno4is5bIw6mtd8E5iab9daNu5DM8jEBdwglh3TdTssW0KP49E6RJ3wNs/priFA==", "signatures": [{"sig": "MEUCIHOwuQFt59nbTqs1qRDibeT33NWotC8QfYhxh5OniTOOAiEA5aMcNX6PkqGYi8eXKBJlR4K4Wj3sF87n5v5HXGAKljw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "81aa01f73bfa5e873b673904bd2a6daab74bae1a", "gitHead": "c96cac16893aaa51f16b5c645962b8b33f7011cf", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "0.1.0"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.3.0": {"name": "react-icons", "version": "0.3.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.3.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "5d2fe5faadac4a52eacb1640ac1050d58aaf159e", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.3.0.tgz", "integrity": "sha512-YBv2+miKYgPfUr1YmzSQA+azXyEhfPLzIyZ20wcgPGBLNTZTAFQ+2M+z9zWDry6zurPluNa5n52EmnLxFliH+w==", "signatures": [{"sig": "MEQCIEgBFBKoteSo2btZm6uFwLtig/G9a9qwBucDMOfCKCEYAiAGpudZM9XlL6GDvuh1FSdYJPOyGGSbAHrYeXfYI1GiMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "5d2fe5faadac4a52eacb1640ac1050d58aaf159e", "gitHead": "b6396477e506e3059148da2ff97ab687ecd52b2b", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "0.1.0"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "0.3.1": {"name": "react-icons", "version": "0.3.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@0.3.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "235bdea297a642a6544f36d62c1dfc4c4e579914", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-0.3.1.tgz", "integrity": "sha512-fgG9nt6Wr6JfQZ6KmC4qwjCD/7ZNqHvOWzgmbZn7zFV8kUvbtkmYOReHjavAL1uZSZ3igwtIG2VTmoWLiPtv9g==", "signatures": [{"sig": "MEYCIQDA6vFqrosctn+xp+/+jvQ8Qw/Nag+jdabR+/InBWhyUQIhAL2tuqrsZc+j17aO37O4Yw9lOdVRM4zAx1TavHREzEBO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "235bdea297a642a6544f36d62c1dfc4c4e579914", "gitHead": "cff6a5c25f895d8f95f0de21ec8f75d17ba5d2bb", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "1.0.0": {"name": "react-icons", "version": "1.0.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "67fd9f98bf025aa50409d1aba8428e7c258b35aa", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.0.tgz", "integrity": "sha512-R4pKlxkrb+bbf+mHMgBXp9ZckJM0sdCIYYg5a+nWkgPXRnR7CSQOjLSVCRX1QFasH6XIsGByTYYytKp9umVaeQ==", "signatures": [{"sig": "MEUCIQCCBnp+6BF7xYOqe/2O3Je7Fy+a+cF95NbwlREJY7HYawIgR31tk40AcaDDzajAw1w5YKzOnJ5e4/HuRxiFyw3pXGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "67fd9f98bf025aa50409d1aba8428e7c258b35aa", "gitHead": "ea737d8d9b3daa6d8677cf9a39dd1cc86377853b", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "1.0.1": {"name": "react-icons", "version": "1.0.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "48ea68441d6a600ab53dba39d4fd37cd33272243", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.1.tgz", "integrity": "sha512-ib+f9C6e7BRZgXs3qO3Xps6crQLVu9CcVwiOWJB+Fas54ArorL8T5lCtVt1HfwCP0miijqxEVlLOr2lsYv6mMQ==", "signatures": [{"sig": "MEYCIQDFJS176zdt+hhs1veqCoGG9nuIuAJ24ZxYw+ypLCfGCgIhAKsv1wN3gBwDe7hvx4N8b3WFmGMfwPSwwtbl93S/JFhw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "48ea68441d6a600ab53dba39d4fd37cd33272243", "gitHead": "6b5e5785fff744aa85b389557fcef82a860330b8", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "1.0.2": {"name": "react-icons", "version": "1.0.2", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "f7efdb30cc52234b27e695b5f14c7fb1de4adb46", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.2.tgz", "integrity": "sha512-Ca9FKILWZWO0WqUhU/IAdx3HrFKSMOBuC3FHm7/0ElHUXxDZFbwS19lA1NVtzYJDRt5k8syhGaP6OukExTsyDA==", "signatures": [{"sig": "MEYCIQCwP3cWGZCP0FTDWJQFLdb9qs81Ujdgnt7NWiYQDzR3dwIhANQLeYIj+/9gVOUcG7huS/FopJbPl+orUVGrp7oah2RD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "f7efdb30cc52234b27e695b5f14c7fb1de4adb46", "gitHead": "323e5568347abb61323f75f70e63c346603a263e", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "1.0.3": {"name": "react-icons", "version": "1.0.3", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "bd222e22b71f10e687d1601a75e6f5a004a62324", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.3.tgz", "integrity": "sha512-m8iYhxSaIlsQBzFs0jGo7m4HyKq5dlK8ZDvY9/MpimnLTExE6BmBgYv4rRd5ExlP3cULTpA99+T4yIqdlz/IFQ==", "signatures": [{"sig": "MEQCIGsUoaFW7jaMN/G+piynJPjYZoIc3iwMpKW2RxJPXzfRAiB5/6fmNx1385M1SO1bH6dIr9tQ3ai3peViWFeIA9HpUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "bd222e22b71f10e687d1601a75e6f5a004a62324", "gitHead": "90f6ec3ff800ee69ea93a794e5611ff2eaa2e840", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.1", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^5.0.15", "gulp": "^3.9.0", "babel": "^5.8.23", "eslint": "^1.7.3", "cheerio": "^0.19.0", "camelcase": "^1.2.1", "capitalize": "^1.0.0", "classnames": "^2.2.0", "decamelize": "1.1.1", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^4.1.3", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "eslint-plugin-react": "^3.6.3", "eslint-config-airbnb": "^0.1.0", "material-design-lite": "^1.0.5"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}}, "1.0.5": {"name": "react-icons", "version": "1.0.5", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.5", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "f6406bba2dbd4411f2bd5267a9c755287ff8ced0", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.5.tgz", "integrity": "sha512-JOmvAxg7IA7CNHuDXLVl6S3nZAa0naVRojQ76UK9R5GgN5IGSqsukgl2ad7MHdRTV+unobU2m5q9ph27PF7lhQ==", "signatures": [{"sig": "MEUCIQDXpLxJhXolkK/IFHtBuyJV3qxzHaY7Cm2yDgm+q0mYHgIgV6Kxv4q6xz1yod525cgS1HYQXhmqsSX7v/zRlpPI3c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "f6406bba2dbd4411f2bd5267a9c755287ff8ced0", "gitHead": "9fb0b51408f5952da9617b2ab98da58c9a973e5c", "scripts": {"docs": "babel-node --stage 0 ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-1.0.5.tgz_1460502783329_0.6834153928793967", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.6": {"name": "react-icons", "version": "1.0.6", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@1.0.6", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "d59b5950a8ca32be8f51a490eb81fb9126ca2bff", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-1.0.6.tgz", "integrity": "sha512-azinbp6pnzggp84p7EtXAu2pgwptJT1Smylc0Q32+/neMLdS88T5zNeOX5iu4gxhRvgSpamhAZvCt6MDtKRCXg==", "signatures": [{"sig": "MEQCIGZpJ3SP597KAMaUcn/yBtrgXjEOuP6DAcTjud6Kp1vfAiAc8KKvBfGAoeRw0XeWNSCqpwbgDmFOj6oluwFQKKORfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/icons.js", "_from": ".", "_shasum": "d59b5950a8ca32be8f51a490eb81fb9126ca2bff", "gitHead": "cb33c1e79fbde2973abebc6b714a57eae30418c9", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint ."}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "jsnext:main": "icons.js", "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-1.0.6.tgz_1460563294349_0.25262298225425184", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "react-icons", "version": "2.0.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.0.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "f4c1d3d2ba8859037553de672134da4076dbe4d1", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.0.0.tgz", "integrity": "sha512-6fu4AiHlObqZ1VchwtZvFA0bSJoT4cXDOhM6vm87oFtv1sXkz7Cxy0DW7HINKP4RtcKPA9HRbf4el9Bj3P2u5A==", "signatures": [{"sig": "MEQCICSjsn7LXcZxK/W99HrPD1GHQsKAKloDS8MvLfLUoIujAiBEUGkNlsYZ/YMVI9SlvXVxWWZfjqiU9++yfLyQfO2j7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f4c1d3d2ba8859037553de672134da4076dbe4d1", "gitHead": "200c8a30026813409b9989dcacdeaa32e80b2a33", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.0.0.tgz_1460645543084_0.26260172156617045", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "react-icons", "version": "2.0.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.0.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "21080fc28ca4f8f0977fcaa6f062d6aaba13f55a", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.0.1.tgz", "integrity": "sha512-HjiA0De44jrE6W4LEBDJoEnTrj53HfAmBAFNuk4wLvGXcW/uD2QQl7XZe+V03pzRFVqMzJv8TUFCWTrzGbVwrA==", "signatures": [{"sig": "MEYCIQDG9HMEFiO8OQhRwbeVsk2GRFmdAup9lWkkgF9rEbX9xAIhAKdCCjtwLbGpHpQnrq4v1ZUrxjwlbHGBl45SBs+h5NLj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "21080fc28ca4f8f0977fcaa6f062d6aaba13f55a", "gitHead": "aeda0628ce790467556421acea6c17c54327ecab", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "feroc1ty", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "1.0.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.0.1.tgz_1460966203525_0.19606869132257998", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "react-icons", "version": "2.1.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.1.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "4a21f2a2a06f637fc0a4cdc665f1ceef04bbbca2", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.1.0.tgz", "integrity": "sha512-N1NQ8Hk6wVPmbKgHH1NwBM2upWOF9XmTUReaFdaqU/kAwSjbKPe9sh9BSsEZjo+JtS8ohBV9MBXt+fwGVBmhdw==", "signatures": [{"sig": "MEUCIQCzyfJnlERYEzCz2qrK9Tqrn0fI3CEH7VHHygt+xxDRlwIgYvQhUVtfu6CWM+uNiCyUVnhfmT2lYRdyDysXf1WWFkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4a21f2a2a06f637fc0a4cdc665f1ceef04bbbca2", "gitHead": "6a31c2e36245a8fa8a36badd0f4c55f18258cb91", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "2.0.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.1.0.tgz_1464183258901_0.8180433611851186", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "react-icons", "version": "2.2.0", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "12faf90036aa9a44e63f3630ff7dd9635c7211b1", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.0.tgz", "integrity": "sha512-LEgFxJKcYrrKCga0CzoNPwTT8Xni/Ln8pn7jBFbfa3ViZKrDpWJQ73i5zTTjfdB3WK8HfuUnHankdmf5Ic8o7A==", "signatures": [{"sig": "MEUCIQDHssPiLIdCdlgZEz+jIU1+1l2nUbvWQmqps8WzFSZLrgIgKOTwS/BHkchigWuILvNWWdIPT7LwDq6eXaIEsE+M5AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "12faf90036aa9a44e63f3630ff7dd9635c7211b1", "gitHead": "db742f1cea384a415cfa553e3cd9be879bb0dbc1", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"react-icon-base": "2.0.2"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.0.tgz_1467802796898_0.16988437133841217", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.1": {"name": "react-icons", "version": "2.2.1", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "354d725d60acf341d09fd50c6398ae7b97d7f157", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.1.tgz", "integrity": "sha512-Yw6QCUL9shN7FvL7+0sIkmfzofxgR5hTnv8q830FcTbE8e1oUnpavBE6ExNIcaE3+p5KheB2YoVF+4pxV6qIlA==", "signatures": [{"sig": "MEUCIEDLxyBDw579x/sk+dttqcgsT2qWtRULay5vkDqJ0fmRAiEAxSzrJpLWBdDoy7GbtIITH40LHhjZ29fgRyZOWY0410E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "_shasum": "354d725d60acf341d09fd50c6398ae7b97d7f157", "gitHead": "af9fef158df816cc4c7e16bea857c5a4a96f8ab8", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "bentatum", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"react-icon-base": "2.0.3"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.1.tgz_1468165270311_0.6596705876290798", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.2": {"name": "react-icons", "version": "2.2.2", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "b200a87d516466157f5bd0bb4595d4b34694a532", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.2.tgz", "integrity": "sha512-6ULp1hG/9G54nnNZXNcZd8ugpYs6Azi3bI+Fj3n2egdcid7BtuxZj4vcjCvX41+W+nKdFNMNYUiFjtWW25DRHg==", "signatures": [{"sig": "MEUCIQCGwQ/iABQ1AKYxPP4fGUE8kAI+FYMdztNzBlsPHkaq2AIgIk7CzbhJEW11SMLusZK7WdVcoRjR2QXiAFpz/JLneSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "_shasum": "b200a87d516466157f5bd0bb4595d4b34694a532", "gitHead": "312da86451f199ddcbf4595cdfdcf25fccee0fcf", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "bentatum", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"react-icon-base": "2.0.4"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.2.tgz_1482189769850_0.779999723425135", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.3": {"name": "react-icons", "version": "2.2.3", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "3b4847923065ecb09f8ba7f7e422112de0195196", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.3.tgz", "integrity": "sha512-WC+nNExe49axX55fKoX/NGEq8TBCSQX95MfvnDrbmMMFUIWBgG6UyjocXrVe+v/7v+/e+tXPVNJhfV3LXtms1w==", "signatures": [{"sig": "MEYCIQDCNNy9gOdo30Dpfn7EwGqZtYzBuIgTGTP96TmMdTxGSQIhAJGigikGzgddcw6TsvJztUUCqkdvw4ANpNLtoHoEZqp6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "_shasum": "3b4847923065ecb09f8ba7f7e422112de0195196", "gitHead": "396b4d9241a61b6d22f3907273158b6a91aea2fd", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "bentatum", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"react-icon-base": "2.0.4"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.3.tgz_1483030957899_0.699574276804924", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.4": {"name": "react-icons", "version": "2.2.4", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.4", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "51360baf89a644886ca3221c99de3c9dba98c1cc", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.4.tgz", "integrity": "sha512-r9aKyIrJ3W02gqBkEZyDAkqCuGUKGutn8C747YBAo0QolNoeeowmEE7fCoJYKjicqGnK2PyNSjODwzJBm16wmA==", "signatures": [{"sig": "MEUCIQC0OcmcqH4apkX2TixU6R284wwgVGp4F+aCczAxhO069AIgIiEqIvF7fwELp14+cEx/jgCX6vfzLzVIwEokX5amYF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "_shasum": "51360baf89a644886ca3221c99de3c9dba98c1cc", "gitHead": "6d13210d09f46052f21bc23520a71de52585efcc", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"react-icon-base": "2.0.6"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.4.tgz_1493939066857_0.4699200091417879", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.5": {"name": "react-icons", "version": "2.2.5", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.5", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "f942501c21a4cc0456ce2bbee5032c93f6051dcf", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.5.tgz", "integrity": "sha512-Ni+MKH5+LVrzRfuFBRuxNmEdD4XHI9XTAUHFFObAV6dn8aXeEOKUadf/z+Ypr2jYK3cKfh2+cJtIG5YmMpNwwQ==", "signatures": [{"sig": "MEUCIQC7nWhrdlMS/p41cWlxHJg8wcvJ8TqDd+KY/KagVfdQbgIgRmhP6eB5bmsEYyzU8W5W7vQ9asml/0lONEofm/H4oxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "_shasum": "f942501c21a4cc0456ce2bbee5032c93f6051dcf", "gitHead": "e1c4ea282750d57fcbddc637c1247a4dd6ec96b8", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"react-icon-base": "2.0.7"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.5.tgz_1494151125741_0.1271830960176885", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.6": {"name": "react-icons", "version": "2.2.6", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.6", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "c46d87035153f22920fd81522081466bb8818779", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.6.tgz", "integrity": "sha512-v33NSsR4kxXOQ6zO2NwN0M1bwwILrBegAt9YB250fGuqFy9iRfZPdbReBiN7RtP1bNGS+EEPXyFnt0kY9WODKw==", "signatures": [{"sig": "MEUCIQDcvwZ65p6IVtsAmaex3o9wUZtw+TROmpUY1D4yJSv5NAIgGNXTKq5is0x0FE955y2c3Js+bcNWw8yHLZuhm7nTw8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "gitHead": "35fa7881a3275d078390737e4979ab95ecb6a5a6", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "nwwells", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"react-icon-base": "2.0.7"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16.0.0", "react-dom": "^0.14.0 || ^15.0.0 || ^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.6.tgz_1507243025114_0.17326055094599724", "host": "s3://npm-registry-packages"}}, "2.2.7": {"name": "react-icons", "version": "2.2.7", "keywords": ["react", "icons", "inline", "svg", "font", "awesome", "material", "design"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@2.2.7", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "feroc1ty", "email": "<EMAIL>"}], "homepage": "https://github.com/gorangajic/react-icons#readme", "bugs": {"url": "https://github.com/gorangajic/react-icons/issues"}, "dist": {"shasum": "d7860826b258557510dac10680abea5ca23cf650", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-2.2.7.tgz", "integrity": "sha512-0n4lcGqzJFcIQLoQytLdJCE0DKSA9dkwEZRYoGrIDJZFvIT6Hbajx5mv9geqhqFiNjUgtxg8kPyDfjlhymbGFg==", "signatures": [{"sig": "MEUCIAmquAH+Qi+QJe1zyJVlFua+cjdxh0KowyC3drjkiyXJAiEA8ddfaQXFlhEsmQZghw3UkUO9odnHkbwge3V/k0W9M30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": ["issue", "master", "revert"], "warnOnFail": false, "helpMessage": "", "subjectPattern": ".+", "maxSubjectLength": 100, "subjectPatternErrorMsg": "subject does not match subject pattern!"}}, "gitHead": "07d8b62766294b235f982efba7d13e02f94bb070", "scripts": {"docs": "babel-node ./bin/generate-docs", "build": "node ./bin/create | xargs babel --out-dir lib", "eslint": "eslint .", "prepublish": "npm run build"}, "_npmUser": {"name": "nwwells", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/gorangajic/react-icons.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "svg react icons of popular icon packs using ES6 imports", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"react-icon-base": "2.1.0"}, "devDependencies": {"glob": "^6.0.4", "gulp": "^3.9.0", "babel": "6.5.2", "eslint": "^1.10.3", "ghooks": "^1.3.2", "cheerio": "^0.19.0", "babel-cli": "6.7.5", "camelcase": "^2.0.1", "capitalize": "^1.0.0", "classnames": "^2.2.3", "commitizen": "^2.8.2", "decamelize": "1.1.2", "svg-scaler": "github:gorangajic/svg-scaler#take-size-viewbox", "underscore": "^1.8.3", "babel-eslint": "^5.0.0-beta6", "marky-markdown": "git+https://github.com/npm/marky-markdown.git", "babel-preset-react": "6.5.0", "babel-preset-es2015": "6.6.0", "eslint-plugin-react": "^3.15.0", "validate-commit-msg": "^2.6.1", "babel-preset-stage-0": "6.5.0", "eslint-config-airbnb": "^3.1.0", "material-design-lite": "^1.0.6", "cz-conventional-changelog": "^1.1.6", "babel-plugin-add-module-exports": "0.1.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16.0.0", "react-dom": "^0.14.0 || ^15.0.0 || ^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons-2.2.7.tgz_1507243095134_0.26463766349479556", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "react-icons", "version": "3.0.0-beta.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0-beta.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "89fd0926ba1b0b8010297108e52f7312ddd064e6", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0-beta.1.tgz", "fileCount": 39, "integrity": "sha512-2aMCBhRNprW5Ouj6FyxonhrNvh8fCFKQ4O3gPeLeoa4v2pTOsdey0DNVUYijjqj3553XKp4drYYeneX/+y/oDA==", "signatures": [{"sig": "MEYCIQCFz3S+mC2lnaqED/m0nI2Fs/csNj6LF+6YlSVSF4ciPAIhAJLeOpdCTtK6HHgm/i7GVCI2iEA3V2Mct/I/ApcPcsJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4177154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUG60CRA9TVsSAnZWagAANRYP/1/uHRYDz7mTLmHCcMb9\n70Jz6hMbRjAEIQMbQE6isJa7CztqXuHHEVgxSeIndEgcYe4oOUkS18p0p0mG\nG/wqTNDYohEBDgxXxUkYdYrfG4BtrmD47SnRkK9OV46+orm4nBv/LV2SBSqn\nFtGfLN/cyxQbFhp4Tf1sHwxn8XYs+i8pkAj5M9RIzfhw3I5XwlR7eeufXnge\nS4k1+SppJWuZAxh8hS9TlWH1DDxyaQ0r2x0HRWLJyl3EJNEjeJ7wnHJ9OQQr\n+06rp/pPv+01ju+qSM9vSLbDWJoNn4GcJ0WWLCCCFxImFp93OSqA4ODPb8/y\nhZxaxEZI3UUESLrnhpktTdznkcpr78NtLcw4i7bkjh4khd+UHGMfx1COe2VJ\nZewaZINriUTyVRvjgWhR3zeDc6AqjRZUtvDiMLg7J4UYZxeerEX9NaIKb7GX\nkyOgb5hsIr2cRbb97eSJdabvcSiFKDdqkR34He4Elmi/dKf7+L8ZuzBrFLXX\nu3GHWLUsRfHsIRPLCkkYLNSTyT8m4WSWpUuBOT7l+ivN6XFo0yTJqXCg8vhI\nc/P7+dgOwLbWxErcz6lcPA3gvLLyFaPf5gvjy1fm/AIBlPHSXcIFsa/Y56if\nSJJNkENA/k2m/VTlYdHk9vgQMEOgXgzkAA5joC/UdRmBTnMpvonxUjJk5Vem\nYmnF\r\n=zZY1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0-beta.1.tgz", "scripts": {"build": "node scripts/build.js && node ../../node_modules/typescript/bin/tsc"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"cheerio": "^1.0.0-rc.2", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0-beta.1_1531997876336_0.5962557144499767", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "react-icons", "version": "3.0.0-beta.2", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0-beta.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "e436301ea816e7e450d042fe75c2fc5e7c214111", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0-beta.2.tgz", "fileCount": 60, "integrity": "sha512-z4YqKcwe/oGet/8weB8RH+CQD4nO1N7XkImkW6kldpFuQjHKnK8Rsa42AY8Sru9L1J++x/fcxWFttG4kQzSTRw==", "signatures": [{"sig": "MEYCIQD9FTsmbOchWvpolodVCh9dKHuxcNGVcw5b+otPybD8RQIhANhgupqCn5tc91ehxxK24Ht8su2+xJviPHNlyWQ3Cp9Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9114940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWneDCRA9TVsSAnZWagAAz+8P+QG8DQxMY/2hbp+MrEq/\nDHXtvCYphNMw5JFU86bYllQnivR2Qru1cbotmhKV4N46JfaXZQsv69OrNIrY\n81djMNCac0UItCV2rEuoaBQKlB5niPm7d+2Gy9m5PrRTO4VjhdhhA31eWSx7\n8+9Fmwi4WO8PVdniJlUN2/fCBA3+uLBCHr9KcDXEREaxjjOpLLlDqT1XXJMO\noRTVnvCd4gjcZHD2EYpkCvea5G0ZQIyeAw8maClK5TqMyy3Crx6LfYmn97rg\nwxpg15E2iIUhzOVTv7kF7fpHq9CcZwQZFVJBZXgdVgdYR04h/BXuE2MFlrK1\nyzdJWmpu3NQt258XNtGYFy9Ysz9Rj+TB4C8f/nM0R2akxSxW5VjNi9VMzG61\nFKwnU79ZfUIkNmVHZyAclzL3wvTl8FTMz+X94LTxvzqfROvMXTomX+MwI1q/\naDr5G+shwIl3e3SkNd89eqVWrjP+KP+1Se2h/cCgINYVkPgwXnGcyp+WAw0i\nIUvbC1kX6oiMMH6Nza0MwKKUfAqRUuzjcVgm22fhovZeXl1nHqiuiTDLuXrw\n8zt2E2gaulj+6AZL0i19NXepH42O9IkaJlxgxxcOMPM9mCFOXIo/s7IEwP3A\nra4hJrXnNgBrnDH3LWQuUmNr7WnCP0YnvBeYIPwbzpn1tZfza5atgv0fR5Dn\nEsyJ\r\n=1/uL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0-beta.2.tgz", "scripts": {"build": "node scripts/build.js && node ../../node_modules/typescript/bin/tsc"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"cheerio": "^1.0.0-rc.2", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0-beta.2_1532655491510_0.6218485426276097", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.3": {"name": "react-icons", "version": "3.0.0-beta.3", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0-beta.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "66884255ed517c0728724b47c91de22c8988b924", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0-beta.3.tgz", "fileCount": 39, "integrity": "sha512-jyIinMBgJ0z8sW42viCNAsd786b4ABbhChmIK4ATmY2gm5nxNmbviXo8KWL50z6Md1PKhyegxjkcZ/7Ygt6gJg==", "signatures": [{"sig": "MEQCIFtUTbsg3aE/bbeG5QdqeYza3lwJKI5d4xl/v8QvdRs/AiBiOALIvFuL1ysVsmt5QUPLTFK56l4Nq6uMLSzrnKXWIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4951243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWo1OCRA9TVsSAnZWagAAYvMP/R0nh1Lhmpd5lFIGccVL\n6l6z52HDcJ0pgeZScCxcB3Vf38xt4ImKk73lgTDL114Skk0aVteHAoDt2Pv1\nnjA32ZxbBxT8HZqd6//V05FyZwus5RMDfFUghDe8SbhDfBb6uKvJDFYgYP9u\nEyNFwYFouS5HKqNYftuJgVNS7bjvkysBVR5Av3Ld3dQzTduZ+42tKd0dXuP3\nZcG1pVAvnarCocl1SxJ+D/eUGsffuyWDmW1KtMv6PHt6Hhf5MavhQx6F+ikU\nUZlVEjJ2QpjKhkNlgTaItsMWrLhW5qEElC9kUFFFe2doYMeyASSol8xXBOwU\ndK6vPF7sYAXbWInECL+18fuvKSl8T1t2SOgbCYSlJcOzQHCNqI5Fl3QX1F+Y\nsFI2tqB6RmOv036HFqiIRbYLBpCKleu86xVpgvACTuqi3cmqDAsZvfrdWdPb\nT4JT8XDntV2N8ZPfFq7lM/C0lmYmhYzDShtB6gR9oDT+337aWTVAy1zldkIs\nrr79pplwPf7tqNHlU2WFgYB93j4BJ4MwrK9z8LxzjvYrojXTnYfffqwyGI0O\naMB0ck59GhN2GMOAB38AZ2opypf0Vsd5RRAZfX5DT5XIOSOcAdkOqC938Rbe\n2ovZC/tNB6r3EsNDq9oTD7B3UGlEhNjcQc+KEMZSNCqy5Vm8bD5u6pbxYB7A\nlo3f\r\n=lXpv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0-beta.3.tgz", "scripts": {"build": "node scripts/build.js && node ../../node_modules/typescript/bin/tsc"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"cheerio": "^1.0.0-rc.2", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0-beta.3_1532661069846_0.4055584399455643", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.4": {"name": "react-icons", "version": "3.0.0-beta.4", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0-beta.4", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "fc38ad88b31433e5689dca3dd4c6974b8adf5139", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0-beta.4.tgz", "fileCount": 43, "integrity": "sha512-78hw52dMIjgcI8oh8eVUQ+8dHrz9PlIiQEkTPrAM68kDKQCoxNl/+KHKoqU5YNlzXOVTkXN11pyKVjCe1dLjIw==", "signatures": [{"sig": "MEUCIQC5l134xnjmpQuT2m40uFJuaTOcaB3nPl/kzCEw2eShlAIgZiSIispvQPeyd9WDe083zC6QGTr+UzoRwGXDqvAJYSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4953899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWqo9CRA9TVsSAnZWagAAX94P/3uMiSEuIHJZs2nMOwOa\ngSU1IDjbNmZXPNPK6IEZ3L/ClC8D1EHVIF3L495ibYTGs5jdshQySLP5M6pq\nXXmvqCaFYeKtsqLa8R1cnjoD6lYk8eqmneuCPGCWZZOZjCYhRZ1PgQlH/Bjl\nM3Tlym+P8EFJCCOO67TxaxsefSgedmNCWa51mWIPHbC4/AdEFPjBnIOruIwz\nAlGIkNV2rjCv7APHvxYqaExpOaSpiDNvIJ6NV/aB/mgZ/RfziuAe1vH099/j\nkSFMMB4rTRwhYXW5qxZvMbut8KAuoADUdxEGcd7shxyjXViYqpIgfPobpKlo\n/PVizRuBJS1xYobmEgwnH/IKUlZ6vV0NNaxlxJx36bixk12VVGdbSoc6FEx2\nF+FVUWJMjh++5FiJFMDBOCWJCrEsTgrMj1Sg1jRDO8W1dLCgHx+YP8XA6O+S\nxkHp31FBp0JrPoM4Utbk55UGvbvnVqbZ3UxM8NA/O44VRhdyzjgc3jrLJ24B\nniI+Jwd+29KtSCFL5vuWTsV7gEXKy9tuxcqJyFUSQiC0eiYQ6VqoZGr522AK\n+t7+KAcqH/P5T8ARZbDvjpnz5J9X7hNxcNOmdqdUfQM7keWTbtCEdwuPQZ/X\n1ycEVyLsLGL+U1lOmNTbFQvpY7H2pgaDjIYSdIDczW0Bi/3WKqcgnzuyIztv\nP8S5\r\n=FDry\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0-beta.4.tgz", "scripts": {"build": "rimraf ./lib && node ../../node_modules/typescript/bin/tsc && renamer --find js --replace mjs ./lib/* && node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json && node scripts/build.js"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0-beta.4_1532668477528_0.22506763006637143", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.5": {"name": "react-icons", "version": "3.0.0-beta.5", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0-beta.5", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "01e83dd182ce68b0c8ecdb894eccf491193fd88e", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0-beta.5.tgz", "fileCount": 43, "integrity": "sha512-ztQU4KBTSERAGTa8piYe6l7oFubmaaxGDRU6ytQKGvxxYrdLvB99EEGY+OIocEfkZAesNPM0NnhfdvyScQBC7w==", "signatures": [{"sig": "MEQCIBkfKC5JE199JhM+83zDJ08lzd9TwWwXNPmEefefPr6RAiBvfMs56HxZIa8y3lMv+ue57+kjxCHQ2UNmi5qI5LJYHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4396679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWuZOCRA9TVsSAnZWagAAP7cP/1BhMSTgrebfUeyjBG3r\nTWfL60YtTWgHiLaZYRFvK50jktFXuYfAP1uqzQ3ud+6asnW9HeKj5+w7C66c\nncVrSQ2gSF5SfzieN2XdKp4ixE9Q+99tI2RDsr8dV9aBb8lK+nUWkB/NNsec\nU8MWNSDhSf20gdBF9JR2j/bigfjPp3ryH2asgWnF3s8loeqF1xM6FtTrKXX2\n2e1j6NsQYnFmd4Ivelo5phLLbf3qSFP0dSKw89/nOao3hWm88tQa1t/rj4El\nToo/c1BR/8xbXSL6m/MOepHwbTfJZOvAyCpzBtjN7zBcO+sP0D7lZ3b0+0tD\nLKM8ZgFdyd05UCYEAYCfxp8AL/PmJbrV6doA9F2tMWo3Ah1B4uE+l0/AdgcQ\nuqP9WFokuP6UpvRlt4Sry6babPIUEGEUHBfxEQSV3q9qLIbtEVY6fKcpllkp\nFGYFrfJvgswvggLtRMc57MIYC1sIFRgqE6Z0jmED5pSd9OrktjzL7lV8m1Ri\nqnXBR4ZG7mtrV8Bjb+sDqBqrmw4o8fjakZCThrjJtlQvtzkVahCLgBDW2dq5\nRMHAR1Az2YalEMLeKtKBcAJmR9IvOBUMU/WJkVg2QIlfhXBKGcGmuUXG2Bfc\nSHs67qFkPChHC+EDfAVE0+1ElkpGBCv+/T78F9zaHpRkbricZxL8Bo3x2pEp\nXFIA\r\n=BD9c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0-beta.5.tgz", "scripts": {"build": "node ../../node_modules/rimraf/bin.js ./lib && node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json && node scripts/build.js"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0-beta.5_1532683853894_0.8643082400166553", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "react-icons", "version": "3.0.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "862da773b207e8b88f3d291c5e22ece3263ee608", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.0.tgz", "fileCount": 43, "integrity": "sha512-YeCAAmYkTSOLrUTNuE8eN2goXuqoV3XhqAFbexxjfYGPYIOoPL7x2QCHtWY3dJpXrao6ZQ2qnZNxV9I789WP+g==", "signatures": [{"sig": "MEYCIQC08xYgVx8CRSQMDFK18I2Bv8XA4+oOPS4RFmaoHv+irAIhAP9NtNmsxZFlhXI3e7UT8dz8QXfQsVxEdh/uULIjz7wq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4396672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXoieCRA9TVsSAnZWagAApVsP/0uKT10X7e1K8Grad+9l\nlbtMARTzN8lsZ8F1NflutVgxKRPBqfuIJvS6yebk95CHLcuFODKGtVt7hNxq\nvrmHTrfGmsGgSRgWGLamV4ATFb1FkONDCjk9Nc1Q23vq0DraCQMpP/TeR1Q0\nulK8B/MMKuFgJ3WReBeZQBbD56LNp1oZjgtQy5aqvzoJHgNgIkVB+dIqFUSa\nImQQFJSf7tfdiIo+RI8R+RwA77zYnzo6knrjzlvw0RQMy8W77/mmeWrC18zv\nWr2VZKsRZJTQ5+IxhC6K4mFT/i3oSyuw6MvTQz/TxUvyFVC4VqDZosgT9p30\nuNe9cezCsjpEBWeKLU6iiKNlJFKlTIN893EGgnm6LQy3jD5RiFjxgsq5Vkpl\nQI6iSBICKaJ7XMsbuf5winQ6NsMubUXNU2dAiafso5Jil9ujmy8jxOUpuLE7\ng6kUAEZ80tq+ErOTHHBnyaP7BE2ANgjS4MDZNLyoyI4Y1P8kfgJefCO++zxC\n+76iVu/eiOvSpO8zg9tCoGu5yXqBfLGVWnQiUMOi/vSlfx6Rq6n3lVmbcHzg\nXzQm5BRVP9DHop0DYEUyLPJSMbvVzV6bN/sagwZ9D6d/MGBh2Wln72IGNQeZ\n3fV9DlcRHJl2qfvihV4sd9ewgwASjd77lj6gray/ch14AuwYm0OjFBkTqEa9\n+K5H\r\n=U2fN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.0.tgz", "scripts": {"build": "node ../../node_modules/rimraf/bin.js ./lib && node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json && node scripts/build.js"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.0_1532922013912_0.2506875583687658", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "react-icons", "version": "3.0.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "947cc0f953c28212f687e61ac11da5326fc3a981", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.1.tgz", "fileCount": 44, "integrity": "sha512-IicPqnnwhvKuiHORibY4i/uJ5tqAYeAc10jlcmaXBqHB3t+TU0du6lRoY7RMOuOmVA4q0Ec7KeOWJB4jM8Wi5g==", "signatures": [{"sig": "MEYCIQCmLO5eSIpy8Ouqm/q6feLa1KcLu7vU5WzDcQ10QW0RtAIhAKUGNzmH+Mykc14+1n62voMMsjy0iAyfM2lXtmvKhYV4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXozgCRA9TVsSAnZWagAAp94P/351kWHH2e6Vz6qHFCm2\n0lGJDgOk0BdPTkh4FCqTB7G2Dzze9eEJAJgghcY/VBMb5R2MbfbTYgJEvqtF\nDCe/3RCTM6OyHd3FOeYn3ufpfLdzITIra1lCrFZPuFJsAXDo78HYv0ww4uNe\nWlhkHIgqhobWuW8FW0G2Gay9apBpe0MRZcc67CRapmdSrTpI2vGMFNW0XFYr\nKeyzfuumkZIsbTrt8GWCK8QoiZEbmnK6ECMuYISHq/taaiKiLglkLSsMC9ny\n5mfcW9vNKT4vnxNcJy9FMgZAxMGGfvPDR/36TA1V9wCMJuZ03L5V4Uvw3Ct3\nEutvM1FgaYoYPf6CzX+YG5khwGQsK7sgeCPl5EfgUVUS5e2NG7cFRmvt/5vV\nmvErAF00DtgPV+QhuTDKNq18e3cDO8FXV/O9ak3CTg8GdUfAIOw8I3Le1t4L\nkwJSpNBy1PBr3SQeP+81jeRXmJxAXzTrRHf0PZr1CVrucgfYAC6KpB4q7Qhr\nxPbv8k3X94lsvoDvvBWdKfy+oOMvPrftbVWlpp8JFgyGW9KVk9Grl5mmNuH6\nH4tdsisttIF7irfV2ngY8LFPoDMa13jng0IyNeLPcONQCSbSVnKGRa8OMIB0\n+9E+wFhJciAyl7wrtn2il1wvyex5Dy1E0jmIeZeXSgeKcQI04sj/AVd5SURV\nLSof\r\n=9K8Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.1.tgz", "scripts": {"build": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib && node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json && node scripts/build.js"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "typescript": "^2.8.3", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.1_1532923104150_0.15023031373092555", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "react-icons", "version": "3.0.2", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "bd87ea96fcb25633ec065cbcccb08d24303a22fb", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.2.tgz", "fileCount": 46, "integrity": "sha512-wJq2F5QF8V30mMYFLPifU5v4SYtEczIj3SzeiYWmEUMW9jKMZ77v9TjS4ilHWdNk8q6FpPnrur+LZyloHJ2cZA==", "signatures": [{"sig": "MEYCIQD2s/tonZKJforHDfobdJs1ZzCNofj8QpBgH7Uq3kid0gIhAOPSOGtO43dWHPk977TpAquAttkfL3ThiieqPrxnQBGl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYmcKCRA9TVsSAnZWagAAxusP/iUex+nrWCmmH4aZNgMx\nPux7pepT9PLLemDwyFXjjP1n2p6N98BlSDnTVrQQKdGESB52QUlohgJv3w66\nDsapa8+w7epgp3TITssCmLX/ddzktwiQfq3pGlZx4nhch9MS4eBt/X35LRkL\nUzUCz0vbJD1+2UM9Ts+y7r3q0vakdWhPi5VGLUDkYEyHBlVkTPqK84VMCGw6\nF0B2oqyid+AJvBdLbqm7iyj7jiv50uQqYd95wV9i5z+ehnuxgmS7XFoOvTg5\nLt/k2GFMXDad5wnG0g/ku9G2PGwVVL3PDBNAZBpI5Sy0hYOvHL0/kBXY6KOm\nWyhHenI9th6pm6Pd/NWFDBiLS34SdtgPRBplaBzNDUMwXwHxNrH6OuCcQwNI\njkfhs91o6nMeTlIAGoMAnal8WsNUmj/8DGxhmsgTBZg1VaRatPgesU5rSmmS\nEZP/pQsb5a7h1j4C4NBEIMcs2rUWUlVHgv7rmHLx8/i3tQJehUMVHzH31Ufe\n5NdbLObDQ00VKHlHie86j9g2R8bKTmI9GGFxL/DOQYA/6e1NMdjzN5XrCwKE\nqWVpoIwrbCWw/bR7dERCz265xG7kTK4TkkXmLmEDxXMY9v+2Yt5f71tsfZ1x\ntKDdn05XDcWNZib7RAmlpTnfV7WDAKqqvmdbwUFDMVGXhKCZYcSmL7Qw93kH\nGhyK\r\n=Q7Nz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.2.tgz", "scripts": {"build": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib && node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib && node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json && node scripts/build.js"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.2_1533175562383_0.6387986810184421", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "react-icons", "version": "3.0.3", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.3", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "41a6ad5c54da6518b2a4ae10b16181ec724e3ba7", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.3.tgz", "fileCount": 45, "integrity": "sha512-aG/q3CPFKY4B4r08E8gWJ7blt9BTH0DAN8Nqi6vwVbgto0wzCK9jWwL87NoEq/4XMTUEPRPSiRhvxIYW6u1rSw==", "signatures": [{"sig": "MEQCIB90njSC0kQVXXigGUC+rCjnpcvS9UwPf6Q7t2M3O+mkAiB9/clswV/52ccu9woN8Xz2lYX3fp5ldumJ8gMMn2NvlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4397408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYwOgCRA9TVsSAnZWagAA0XwQAIoOiwYa9ReWMkc8Fjbg\nohW2icAOmF7tIowzxxj8pjDjsmiDXmkMe9VX+GDmk0Xa9CpLohpVB+nYUlJx\nEJ4A2MUTqpOpgSyFk8qPj9dgZHGagqiIsIpibeteLsYASKV1otI/uBKAgNmS\nM6EduELuBFl9I3PDDAL5KtbWgzOZQyrlUjjbtBuJf49FUoBmAByRDiojQN6A\nLk0NA/rbphKKeH7cvDzTltD176zPgaZ38U84Y1SmMP6T1UMSfT/RRJIrsekk\n/rJNaPlHc0/V19WjQskXHb8zAPF0s6o6kC5WgzeF2uSmkOSmLNucOKLEIMV8\nSRHepAsaE9e0fs+BYeWzSBjtt+4F3ULhSnGdE3LzlD5QfXeqjqZBEmdyp7Kt\nOcjjkGTOgGVqnA5ksui9nlBAK79OdKy8+huGvm+FP7PQeQpVAtMsuE4aGUnl\n9ZNrzjLy7e9hhnx+WR8n9mRS06xMtj46ueggpidxyNgS8/uQRNQEgJQMSxNd\noYbHtn1AVTUGb2YRTBX+W86leGwKMG/gSCAY5IrRPX/bdCF320ohm31X2DMN\n98xza9noAObGtl5WxgX6Lm8n2MBEA8ybS0jKyYJcwxS15/4e7SWOIT8J+eC3\n/VMLVWjoLE4rw71V+SBkHHteAOF8s9SeVY5YdnbejTSW+2R6azM0QkpRLP8e\nI/8Q\r\n=KQuq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.3.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.3_1533215637862_0.9669402759992416", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "react-icons", "version": "3.0.4", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.4", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "e1f09a51f837dd0218a4a7d30060e1be79a59a8f", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.4.tgz", "fileCount": 45, "integrity": "sha512-zX0F+6CUzhwykcQKFENLiR/7aAjtXGRUmpMv24TQPAUg/ca2s0vPTbSCNwPXu9eWTrLm6IRoFZv7BnY8SK/HCQ==", "signatures": [{"sig": "MEYCIQCMjdi70MGUZRzsoaxNSAVanTv4g/IYH8OZeSpQneloGAIhAOiOi4/LlPzMhrsoGRM7BJI8xYcEUhQox9KAR+bULYT6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYwv7CRA9TVsSAnZWagAAv+4P/2k/7MqSBXZFz2OcKKiz\nh8cM3wqFE5elxfmDge7HYMdMZbpBlNBn5dCPt3gksrfb0f4bI16KpM5/MiDx\nlhZgulmK+W00TwGva1y+UfX/W6WNfbLEKxSoAlbcF9hzJ49ai1nUn9cUVVKe\nS0oE9ZzqNQ2SXDjoKMlxVyW4DEcjYPr8Mpqu/yVh+ZycRusOeEax+bKuwUDl\nl5T+4LIFPXWo/xnW+foSB/qmCJfUAJ5g3BJrmOC294zxiGfehOc27Oi3midh\n+P+5bFHfT4yNbb3CqQqDu1FhUxrdk+GigGdrDBkmM9t70FqK9vU/OJ3sXB+O\nmQoSzdHspDA9iyO5gtzcpDnrE17/Rp76FM48ya3icuRYHFSfkcOYDsYgPWCH\n21bHIWJ4HfO4LSmHy/jjuUIxC4vQ85+5mL4Hprz9eknWrfC79Dd64C73ypRB\nIzgkTDkeisedhz4bQdBMFl8u9VtJy8mceBf0+kG+BVX3URbOU0gB80RnRxr3\nzcQue4/W6maNaemH/OLu6NzkcVPaIJ1vXZWtZxY7DX1PFdopey7E4559rQcS\n4cr+AXK07XK/lT2HzF65lGDsioyeXWgtcfvV0Q4X8FlJ0MDJvYOprfxU6CxM\nd8RzjBN577E1MUfZg+DI41T/VPmklyYCMDNgKjvMBOQ+qEby8RqiPtCh7miZ\nUPx8\r\n=BVWf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.4.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.4_1533217786614_0.7092475752982623", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "react-icons", "version": "3.0.5", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.0.5", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "9f22a319a48b562e052af2255a3dda8ded363d1c", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.0.5.tgz", "fileCount": 45, "integrity": "sha512-gNOTMhB3QCFsDnBkO0psdcz84BjprjT95pX6SSJ9pNARQozTsBqOKeVl+uw8zMIBGGDX9GpdY9TflnRjiX4z1A==", "signatures": [{"sig": "MEYCIQD+yStcGRfLwnuu2UKPCDmYS3tOC9hOqlkuocKJhuYy3gIhAMKibylWfvBYc4BsWZCmitXabAJJxeASb8NQ1vLQAu+z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZ907CRA9TVsSAnZWagAAwKoP/16zxpOzqknWrHH5Iv/c\n7swwB0qU6aAlXE4HC0mFKSZ6LtwUHSnuz1PbVF90eVHxpOaKXs2YUL+fM4LG\nX+iQahTMFRWbS8wCQOOyw8zbF82UyTvNbG0ndxur5bix4lzMBzW/0sZUN9li\nOVw0mcB8A7fXDhg7PcY32i220xSDu8Vd7uMxHQKxtQli7FG5JfnfMXX3czBy\nmzuZnhrSaI6mknBn9gJhEKTTS0GCC6y3MbGdqkJy+7FeF3rIVvNb10a3<PERSON>ael\nUUO75cO0jFD1Xz6H3y6ppxhn/HQYYOMkCOHJgCffk33IgZwC2voOFzE6pSwd\nH6ujIEeCKN0q+fISMsxcQDcWh+dv+oxMR3mw3VC3TSLjL2NZL8d7sOxWuSHl\nBeJ8ng/2wiXPQQTc2QkBowuMi8fWviL6aP07G/e1YMc8jeUKBomW6JgtwTSb\n7tYkysfxylPvNgmoCAq+TTcpWHxJwz85SH8WuARF14AMHA7JlEyoJZd1DAWH\nn4JAIXT5gXb8bSljRYR40Zyl/j9MwA5+ELUnNaKELpS2wjBIW6uIjMpx7YYc\nOW1x2Qt6/ecdJSAr83EhSZMng81CiL3Ij8ZSRPmOp4Y3i91+dHNnx3aD/kOx\na/pfV7SPXL1qO/kGN3xwFNb9r+wMniUTYgZap/lOnwshzJdtZEKth0QZZ1Ci\nQu6d\r\n=l6Xn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.0.5.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.0.5_1533533498555_0.3640972118270167", "host": "s3://npm-registry-packages"}}, "3.1.0-beta.1": {"name": "react-icons", "version": "3.1.0-beta.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.1.0-beta.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "2b91e4eefa05dd663517bf459182bd8f00406fae", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.1.0-beta.1.tgz", "fileCount": 45, "integrity": "sha512-+tutr5xkerFe/UtKk8/esKu37UeSy9RqgBv4gBwAKK9TK1BBK08Hfs15oYYVeHHvM95GM8hwz62jeOp4IvETKA==", "signatures": [{"sig": "MEYCIQDSPP+H2+T8kPILsGBKDSryYm3s/Fri2UVZG7sHYF1WNQIhAIHNbc2IcfbQ8TL4H6HYSuYlWeuH+WSiqqiE9IV1kksk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4598169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkjtJCRA9TVsSAnZWagAAKEsP/1POJHxy226nEmVvDtCr\n78cOwxk4oI6QaZ5jE3Y/UHMdVqw2v9AmJAzzB9/x0uQHDWAkxJONfzw8AOX6\nGdblp4ZWLhcHl+VJI5gFsuT90yFn93nRWLhpzORpLH0ie4zqJ8FSFMfnBENs\nITuBBgHNQMPhd1iDp27aa+srSiLDirUTB7bwrCGyBjs2YY3c0i03sKSyZT1V\n4Yahl/DGTcLwsERibocQuaSZqYL8fgEq5PNVNhiPGWrovx/1NcWVHfTbdpGC\naoGsRMwOoBzi0rFCQbX/UcHlzFvdeuDfXp7ICCfYGtfdlX96Argoop+/9nVV\nupWosbpjFMQcznT/Lz85QpUjVPmbKFQDdRqMGY5x3PWcj492SnsHw37QnnVn\n+AAKRcNdn8p7I9/Yj4n4qDkFVZCXeYBa03RJmFVL6LxnzkosO4ckRIRhfoDj\nTs4acdNb1lDWmCDW2OeQaXmA1HyoBbHCBfVVEHwfixn97MOdvCBMvsjECsT/\n8pKCtuvd+ypP6Knw7OyX+K/Z80g3FKNKx/SWt1K9vr2FJtodsdhRoZGBUZGd\nvkm5k1qCVNNr8O5XECH6BrbivxsvtwLfHlvyFb+TdaJfCDpbTMClcm9q27Um\nk6t3gPd9PiVuUlIcjd3r/ijtQmGSImoso4E67CBuqam1wB7oWriDb+yWBVOo\n9vv+\r\n=9PZn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.1.0-beta.1.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.1.0-beta.1_1536310088310_0.4857433026310245", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "react-icons", "version": "3.1.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.1.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "004bc8d8b64a5480cdebd86ccb72ba5930b0e434", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.1.0.tgz", "fileCount": 45, "integrity": "sha512-kz0Dg1pEY+Ln0zcHYmBfx75pWz4U1kvCGku+QdwZkaIrCK+UQhPd69oLwik0KW5KpNhWHed0aMtAx6DnBhfIxQ==", "signatures": [{"sig": "MEUCIQCyeWwJzO9qMGuSuFZnTfN/WCDDA1vgqVzgINX2zVLvYQIgVIC60cmIv4RdQh2elmphwpCG9uSlEWm45Cu9Eqvboj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4598174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbod8UCRA9TVsSAnZWagAAROMQAJ3jE0EAjBGa8iNoWh5J\nAGuWIDuWIWugVmed3kWrFvR/w10dqxsmMt/vIleLVsMPMo6/Op8FUtQDhA6c\n2VcFo3Su98V6Xt0zPGlmsFQJPs3Y7NHmmLi2/Ualp3ChS7nb6NGxZxJr1AMt\nA3kXyt20pqvCb5a1z9UA4p+3aq3NkL4MkAbslbnlqxXBhvvTAOc/m5I+kGnI\nqaa2Hi+llL+6Mg78cK+G38GAg9SiFzskWUL8LNX7bjq0ZZ+kWZUAX+bRRqDg\nACErzOMEEhE5a1Rvpr11pNXrA8F2Pbmy4bzEMqwxPBnmZlrEkMI8E3f1I51w\n90zVGfM+CKbV4jtos2zRuBS/3/wBXYmX29a8wiJz5Y9qwUcNzWD9ofxmcPF5\n+UGpOudfA6bvBnlRwYTmfIsjBV0On9fAKE+GT5JmODUr9R7VsF0bUvyQC73l\nlzQj43A5BOcDsDPYg54a4YWePLjwQBJYp6hZx0xJOv9F1PoGIDlXHTlG5j+g\nfvbvMivn6dF5xFU4oVG3I89EA0iNQGwkSIbKyJun0LlsSefXj6o4NMTJY1sJ\nIDPKDU9Sx4tscbgy2m9ERgCMJS0XkxQyYGB/SjFSbxsjLCuplccGToH5fQYB\n5RzFKyLCXv/UhMPZI8J0yFMmgubMVxcj1UUTrkEEl/aPeRyXPPJJGCZ3/3Sw\nifuE\r\n=hOAS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.1.0.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.0.0", "octicons": "^7.3.0", "camelcase": "^5.0.0", "@babel/cli": "^7.0.0-beta.55", "typescript": "^2.8.3", "@babel/core": "^7.0.0-beta.55", "@types/react": "^16.3.14", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.1.0_1537335060195_0.7593579391789984", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "react-icons", "version": "3.2.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.2.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "fc1bf36cb4d231d61d1aa1de3a6ba773d07e47d2", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.2.0.tgz", "fileCount": 45, "integrity": "sha512-ZWqIiOr5wTqtE6NNM9JgWBgNbq2JRi79rmqf2j50V1ewC6LiwKFhq7J/AjaVKBi5oPE4/9JN/jMKaVrJlIQoiw==", "signatures": [{"sig": "MEYCIQChzqyk9htvLK0KYdN6TFJCG4qk2DHeO0S6GsyFDNsFOgIhAMR3EITIr5sI8NCvewGcf5Oc4XWYiJKqtcMdETw3t1lk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5604808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv/kgCRA9TVsSAnZWagAAWgMP/3VcJV4KOdd10XR5nFSS\nH6Q+GR3GrJbGkK7B66HKnGqNVMEkCDe/18ekFeHMF2vL56sz4ygzuqHL7GKG\noRKPNy8i4h+sP5Aem1zrTSSZ/NeBusrgln/tsN0kePMr/ctwW/mnityk5X18\nWIQufMKG/UoaZDV0j0QPCq0u2lBmwqQdI7RRmfPJJ80645OeMYL+8iYVKSlh\n4EMQBYplHTxQNi6AWyP/APboQKzPqVVml4WzWmbep+nTQPQeJG50izI1ZF7d\nGpoh4+b/K7LkSzdr+Cj/AZDb8hjGn/bskkYhW0IeEFdKnjWgh0SPf7VsPvDb\nAmeEeIbB1jZp5TDaDUQ/h1hUDZwNzK7aSwbwr8LiyyieQ1PRM+NY+qaRVPCh\nNWtB0b1C1U4FdUFYhJX4C8AW9ed0k+qMkieHC02HwcBbJIFNgvZSWDf8PU9P\nZgctv23X0+G0C5OuMU5egiODBgf+l6wQG0DYh/Qe79ZdrUxdTz/3PqnC62dY\nz+yKAQrJiulKeVkozgl0tFHt+XWK15DyOe7zsm1WTEdsnHfPY7OihLvpkhPQ\nq9gFZRgnL9/+bA4aI7Jpuo3jC7rA5Dlu7f/hGtHOOtxzz+5G8ycFghCzXfHZ\nEi6zEgYKygrtKjrs/R3/p8k6Qm40Zo53kYb8uLqutBcSLN1dInYqTBzrdWO3\n83PS\r\n=hy2R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.2.0.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.2.0_1539307807907_0.4398837206122743", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "react-icons", "version": "3.2.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.2.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "6f7f9ccbeec05ff7076df070703e03238903ac30", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.2.1.tgz", "fileCount": 45, "integrity": "sha512-NQhnh7G5hWCAK7pXPeeI944RkeZ85mtvFATx3wfcVQRTW4ejaQgriemYF72gQIFIyoApMXLTdb01BKp8PaXlrg==", "signatures": [{"sig": "MEQCIER4FcIL+BPArXoxglv/P0kWFx6CYX5T1T57zSfU1MCmAiB7FCL16mSSqreEVyne8siRysMpVqkJnQbyo+xxj1g85g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5605195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxTaUCRA9TVsSAnZWagAAklMP/34nzVPIP1tdPmdXzRk/\nk+9rR9iDb2QJZYG7Fvz1N90qR92TClaxdZQUTB5uUNGNO8n8Qzn6y3+tN8Mk\nm5dkMSVxUxhdcZYy5PxZ+VhLrhkMjgbnmgR5PuohRQsIh//CAScdubBGowZA\ndmULNCjGz7II9sWOVRyQb9XLC9jJsWtXyyTluWKZfGp/eUe9VmUUSz1/Buin\nUJX0P0eKqVDlXhkwLm0pTnnnNKaFoV9tPOeHK9pPJTdZ1YdtkNYFjRgBIj/+\n8P/V59vIOTTb31NXPZByROlcHpKIXfAGmGwaw+cDg3YWoEgk5Y3TDYHt/1bU\neyuSKsH1ZdLZZ7k7HWv1yp0hXqiQRpISIj+nUcF+oNipUqJEEc/tmLIIH+Hz\nfs3Iv6JjAPvRPEOvE0ACX9N9tRDkmWH0J55REhb4TvngQGjD/+UO3Gy+QmJZ\nqq70fQxNDuhcZqj7mZkIBMRcqXTSirwofM7ORN/SzrzHRKOqNkigWqj+smKQ\n4dOEZJlH32NkKlssi3zbEYvkGChm7o1FP3+WtYcjcOU0hXtPGH31iUKia/PD\nO3HIHXAKQU3UnGnzOaGvegIficrZEMFuPI5rmztvmhPy9B7QIeoxSWpjnonU\nVJj3jKwPUITOwL3vj4+14CxCtKSuQTsV0J49IPzpyjm7B7eG3jy2VI4NIr07\nRlWB\r\n=HVL0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.2.1.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.2.1_1539651219480_0.7347938408404096", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "react-icons", "version": "3.2.2", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.2.2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "7b1dc795004a119a35175e62ecef91e5c9c90962", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.2.2.tgz", "fileCount": 45, "integrity": "sha512-0DiXTcECUNoMvze2zcFwYYrHAFgQ2rBM0+HIHfaECKRfE1NZYwfuUSbwyg9s1uVPuy7o/KqQKSwBGMWQFvdrhQ==", "signatures": [{"sig": "MEUCIQCpeAQdaS8wtBDZc9jvovXnKIydbWjRI5lmmIclDgst0gIgfD1BohIX63Rjv89zQXBA7zOVyrW95DFkPwrWhoRxhbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5612643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0BM1CRA9TVsSAnZWagAAv4cP/2B7DFMImXn88lVCU1Pc\n3K9UxF+Ondb0zO+wdroaiR6d9rAaodY6OKbXBs1C3Mk/sMqbHqpiMrXNj4yD\nNc2tyJ4xnDf9AXDopsp3ZAl3nN9ag37DHP9sjIwdaNe5EFWuZdZ6+pbUuY0B\n86kuXIt0s06rfIMpW/cChecsprMBOH5aF53O8UHC5wtVgg9gsXZTPJuZToDB\nXihN5eBkex/c5+Wb6lY5ESDbKuvLNlCLAS9o6mSXEVvApOT0YB0VAxXfUAj7\n8dmWplhwBXM9tx0ylVLRv6+bizhc52lVosWMKMghGmEoePcHllPuwJIJJ47N\nPDTrZ3Jq8/9VX8Nv4JhSushhxD64dwUwzzF0z+VZ2S55oXpOiGb2+8bcYvqw\nFui8wMtcN2AzFoHyY/v9lZqENgmvmapMhOMc49kzud9rmJv0mxSehgChXN1r\n8oI8TW/GQm1jdKHAme3cJIVuprwmyH3KdpEOdFa6YxzxUyKpMuW5vVEpXJ9i\nX1d8HpQbtoCs4MUyO+BpDkJeR5VXrqGzV7KJ6Q7ZKrjtrDKU/WJBFtRAQX1C\nYfS3+W2zCXl2KIPOxPgObKvXu7WixXLBfrxtz2yMVM8SIkb1a9E+KnsjeyW0\naF44gBRhxnu70MhzUzhlcAxOvBFSiRchbWVDSzPGb3yoONnTKkh5b4uzZr4j\nYhOT\r\n=vpzw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.2.2.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.2.2_1540363060496_0.13772698446589615", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "react-icons", "version": "3.3.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.3.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "09409cad23c39149e419af5c928eee6f9f456b93", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.3.0.tgz", "fileCount": 48, "integrity": "sha512-OY8yBAyww1VEro12Zbt6dZqz77zN7LXukC4oA4i4fWjxTjCbbqsPy6BrfAh7BYPtIRaTXhpoYlhdoDaT/kxdog==", "signatures": [{"sig": "MEUCIGYSDbkwc2F9cGEqDi8HyGqW9Kd7a9vDGZG6zfXsb/C5AiEAgueaacXsFVsGXPkgj0VXgZ6trx23GgAnTcZXcUVo5uQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18397943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNsPDCRA9TVsSAnZWagAA3IIQAJ4lLKVgGTGUE1/2Gn6M\n6tBXVOpOYFzpBJGxY2lri0XS9B5GXuaFmrx75D4N6Z/hs/5lIfwg3XKsiixC\ntjBaedbfJRM7LzS5YxJQ9V4d2nn+jVSdNYtC13AR3wZIgOBPTYdIn0eSgyp7\nr8DKFJkrZfbFqdUAjGUl8VByCEgw8um1p2hktAlj4I+SiRLdD0D+S6M0R3+N\nUYtMXgivo8korH3XnWMbCRbbOmwUwuwVN6BPE2QpFCHlmWWM3aZFrhOUGqXB\n7C7tLfKIHZdfQZdUJwYGSWgLf8P8uFZ5VVZ0rgEE0NcFiKCvSEY8vlQ962i3\nke+GBCT+F+prZ6jFT0HJF+BqQFZtjxPuTdlZo2TExGvcAHVtrNHfELrl7rpI\nqDOYorT2eewnFneIxF6SMGIe2JRlKLpnFTnNOt1KT+QJ/AB+uQEHWAxgVLgN\nDATpzBh27D9q9Y2C2zX1jPgpIvcq4NH4OkJ9SGJ5TDJYODK5Zae8DipojfKC\n5ptJyfrhgthDYUM/uOmOdeMEf28W2eXgvtaZZetG0o5s23DgcVX2vIKtL6sV\n2qE6pxxU9Ef0ivc2e5V7YKE384h96SLl7Fd2tmFiJwdWfsbHBEDEgNRJ0xuO\ndwTP7yqOGFIyhe7O4Rj66He65B01Aai1Os86SzgkvXW1Fxn1GX4fB0SeLgz3\nqnC7\r\n=h1ED\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.3.0.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.3.0_1547092931109_0.013541641170192298", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "react-icons", "version": "3.4.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.4.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "3be5139a81bad5fa8ffbc0a87137275dc7a75737", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.4.0.tgz", "fileCount": 51, "integrity": "sha512-5cyYrl34KGovU/MH3YO+wN1k787vv3EKLeGDyaGOY92jSTQ/xRjkQ7iim5Ml9rgMLU1i0vszev80kWV4uRZ3Bw==", "signatures": [{"sig": "MEYCIQC6muHKsCy39X/8nfyT296yr4PeVCvMQXTOlvRYb8jSzAIhANhRxZDKJg4mz2ZQhjVhA3i7qjNSZq5sRf01AQ/pVNLt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19292940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYigmCRA9TVsSAnZWagAAgcgP/2CmdTUikdvRISGccDw0\nnS79qoVTpME+QeJlbIuOwKWfZfWmDNt0RYSRCC1yitPjfpaWXxXLe1h7XvQ/\n9dTY7NyXgd73UmixkbYb9zWLXjgejPeCE+z+ZuWb8wlmmMLn6q2j+xVEVpSH\nyNh+MZ6LrDvk6Rn91Y44Lb7zfO3yKvCnNyN3dOVof/l4qsSSkzZdcErCCaU2\n2bR1xC/XCeCaGy6Lq8/27mfpds1dPSp87bdwqUg07wpNbll2huRthK8zjvJn\n676wri1IP4fArenIOB8sMaFSfHyWAvso7K+iCzgwW4NnVRgfz99uk/7z2CS8\nLenT64E+zXdBecjhMsQ6eozol6e8KrBTNQFTL9zmd3pQbLd9lPFNhon1j9kG\nJqqewE3U2IG1hyzI+EAlse62LXMl0xFeH8p2F4Jm6KoQtgih3vynqoIRdIdL\nqbx5/cxyvApH1Az6VRGtmcVlarGX8vCn9KL/h/NLNU2FryKje+02/EZBRcWn\n5wBuZ57Mqd4zDPEMlF9D2vxen29gCJ9IxK8y86xdmEOkNOvq3kjelcKdK2/l\nY9AYOoXpN8GY2j6Gs3z/Uo72MYAo2MAiz/AwLWWPjRwYjXZMnnOT+vZ7vedp\nkPK+4QqPxnYoQW0MvegXvAPWei4JvEfGUdqQa2waOTbdpiK3fhfSQVgonna6\nQgp/\r\n=aejI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.4.0.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.4.0_1549936677743_0.4348447533847639", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "react-icons", "version": "3.5.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.5.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "a6135480e3bcbc63f5dd045193ef2a814263d8d1", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.5.0.tgz", "fileCount": 54, "integrity": "sha512-LuKUcavgPWjPrRkIdNbsGw8LqcnhfNN0AGCtU4Td1UkOenJSIWbYppSJrD6zi/TDZOHtTs9opu6ZKB/NFWk21g==", "signatures": [{"sig": "MEYCIQCxHMucKzt8iI+oCergoz1Z6KenztIiEa2JQUpoo4Z4xwIhAKpetmIED2b+L0fACYokup9jMxhjFxBxMjKTrOqcIq6W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20404175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf0NTCRA9TVsSAnZWagAAI/8QAJaDgn7NAymgAfq/xF7E\ndTzZ66yEsJJI8605yK1Nv+Rs+bAjxgLGbXnm8/u1VT+rhQhpj4/bOMffMgaf\nFpK0/cmZZdht40unPt6LZkgEP8TeEZ+bcGHsr1mwDc420aYyfjYfT7DnxUOE\nFb3l8IOKfuw/L4+HR+ZHpJCjpbxusX8vZNiohURtIoe+TMQuFrVbHc5UemV5\nJewjLVmS6jnRKdPXfVox4S29obJot/2/A93QIOqGI7hSORVIDfEp9Oirl3oB\nkdW7F8MF0Hl9rbBIi00mUNL72sO9X+UXwJEb28DAzrEo0UAwXWkPFktwesVH\nECFkjB0bV1KmKRrArbtHhxb9AGSxFO1dg+bSddE1dJG9HMF6hduR+vpr4rze\nHOi1JjwUlr4wmgo6Qxo6dc3nqMI/cBsCQsVQEIeYPjp+Eth05XX6nowkDr2v\nB3saJet25O5Yzjjp8yq+670KoCLvLORoVx7FxAeUtHLG18wp0MSw5ts1jZok\ngRveyhs4jD8pG4jMoL47lL7LAiPPBS+lkKhCBTPF2O8ClzOw2Ij1GwsiAHGK\nDC/8uApFPm3esM3ZRe3PRT7avKDG2RE9qxtwRTX9M5tTaDq7khkdTXnvz+To\nqzj6JC5cJB6vKlBDMa0hjrqXkTaeCwfBvzKKIomHr++z2+mnx7lToyy8snf5\nV/gu\r\n=u0bK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.5.0.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.5.0_1551844178609_0.07663982374010625", "host": "s3://npm-registry-packages"}}, "3.6.0-beta1": {"name": "react-icons", "version": "3.6.0-beta1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.6.0-beta1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "ad78bca783b52f2a762a9a6711b9274bcdc60bbf", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.6.0-beta1.tgz", "fileCount": 63, "integrity": "sha512-cQqKRK1j4EK+SNPIaxxCzJKTKyRXBqJ2Iv/rGqmdW3aTLTwD3PvksfVEAJMYyeF6/lhHdL61w9iC6XntNtkHew==", "signatures": [{"sig": "MEYCIQD270N8B0I5g6Ue0Du1cQfVnr7EpRX5hXclTtOdYk584gIhALsBxNW0QWeNqOc+tQInjMKg9P4SrTr52JTOOM5yMZ7e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20336213, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf0O2CRA9TVsSAnZWagAAgJIP/2Zu8/ThoJNEhKqcb9i1\nvI5Bo2METk/16V5D2rdQUkIQkb97SgLC9iTFFQTI3RvFdi9dT30tN0XgP1Om\n6v7OuUkhbH8H6JspobY/ktAMFDNVKgvSXyyOLTUsKnRcpuvWo1g7uu745Rd7\ny2d1a8KPcpMrML4pni+od0UCF4Gf7CMP7Fnbb5BneFqctf3oBVtUYBJK2YS2\n2aLKpD6WXwrCeH/19Ln0UG/C4z+GPyqd7jIN4GX4Iry3X4Z6KQ1zA5c6iTXk\n6DKRoHesVPWhoqY6RSTVsmZmCuVecM6El8rh68Mia0d3GKUWODTXQz0wpL/L\nSjlMLrtzDxZD6JL/pPJRlC6OzjDfC8s2zsQRTUkRIvff3UIs+gFnlxO+5B8F\nkgBZBnikwjwqpjXGGS/5DBdz9IzUYV7dbE/+tXhHzRZmVJEClF8b141brYIl\nGfm/lia0qjQaX+4E4x89Ki9j88MAO8KYHmmOYYR3CAjuSQ+v7jO4kGiifXmz\nbC0HMPLIEoSzO1GllHR7WGM0h0xF0a+sJEvbv64MI10ca+aJnnUmY2OSwi5R\nNNA3Gwl3wh939kxpsp4n+j0fuJIPs7AYHvzm6Coo+fwH2v3qXXG63xUcsYAL\nCvLrjuthuoMjMXZs+jKHsPbCed+pQKL870vz9997TaQp3bF3e8qJPTMckHaj\n/ts6\r\n=sAMM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.6.0-beta1.tgz", "scripts": {"build": "yarn build-mjs && yarn build-cjs && node scripts/build.js", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/renamer/bin/cli.js --find js --replace mjs ./lib/* && node ../../node_modules/@babel/cli/bin/babel.js ./lib -x .mjs --keep-file-extension -d ./lib"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.6.0-beta1_1551844277512_0.5885889958592403", "host": "s3://npm-registry-packages"}}, "3.6.0-beta2": {"name": "react-icons", "version": "3.6.0-beta2", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.6.0-beta2", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "62e117c12559997d28a6700a1bb9416778d132c5", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.6.0-beta2.tgz", "fileCount": 68, "integrity": "sha512-RuxTvSyTSxA/UtesczuI+YhdWyg2q7Y+WowUttdqODNOg7HfayFkOaX2sMTM6Sxr5d5a0c0cgMAwOdNx/elNvQ==", "signatures": [{"sig": "MEUCID/huw2SXYYdvgX7Pf8eVt+NkN8ZwEQoAmw6oc4tA3jDAiEAmOp3lE5/f1i/V60mzhV3+IWzCQ+9reSBDAjyJ+5CFrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20337036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgeNvCRA9TVsSAnZWagAAYVIP/24NtE86LVfB4SFwdYBC\nI29GqGp610labQFt/+8tn6J/HAwBmIe/BF39WoTfpKoLUzANZPXZh0IgqiSc\nRO/GjiDR9ZFyih4Kc0w45wRRlr2KQaCbyfeqgqiK026XhcjHnxZnQ6XxlgI7\n4GTRPfg9OkIHTHaqcfMUjhk+zi2Khb52/7K7z5Xxvlh9XJHzRrPopMM6TPnV\nR+OI+WZICMTbPjlV8arnxTiGdD3YRQ8f+hyYFXSIlwrFGxCZ/tQ0qtgwjYgV\n+28DgnT17Ew8pqCARmbGTitcOC3Rl6xB4WoanpldUmRRC0aWKCQNbkhR/gM5\nZfHaC9CERKAvZ23UFKr3qrEEQhnUN1nPSlNTZI3oTrltqx1PaeuMVhijweVV\n/4lAa1ro7uOgQmntPAAsf2ZT64EraAI4Bnfdv72i5Y6zPg8DRRvdrHanw/BV\ncrZtUkCcxgsD6gT49GxZVXSmNgTtnKwrd8H09FFkWFODRt+uYTE8KG+YeMYt\nNyHikoW4pKLE1EnBhU2y2FVWbzKgYycEwZ9zmyANk+lvElGCaBeZfzJ72+bZ\nJH/HW5kNkenL5KmTmJVTnCbCU1BYCNY99rl/4Y65nQc6aOQFRfwLdrR7spfV\n9Qnq7oekMKZkKo/0gIZdhlfcs7NoC2x1Q6WIQvqgXf6Mx2NnlcrBxmiS/HSv\nGR6v\r\n=Jwh9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index", "_from": "file:react-icons-3.6.0-beta2.tgz", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/@babel/cli/bin/babel.js ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.15.0", "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.6.0-beta2_1552016238732_0.6022695096233452", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "react-icons", "version": "3.6.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.6.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "229ba896ea110c431c9f6dd811babdd31866390f", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.6.0.tgz", "fileCount": 44, "integrity": "sha512-oburoiCTdEZm7SSph9qnvqneCRF9+hv7UqbsWASnZeIne4ztiGFPzOdM5py1hq0q84gk5M2cZjqgOs8Dvfrvgg==", "signatures": [{"sig": "MEUCIEd0Frmx/7S58Zya9jPdZRRpFIalm4t9DJmVliqLG1vkAiEA1J1D4dHSTvLIier//VklSA9tphxA/z174a7rk/nH0PY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5615469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctYaOCRA9TVsSAnZWagAAFHYP/0ydRssi0LHSjNjF++NI\n33khpDp4xIfyYgHSi6X2ujAPKt2dj+JNmbnbCRZRW52gc1tY0eEQIiaxMTFe\ns3G/sIjHwbmuKPYsRBy3kFl4Ljp8UrXw9W+SWuv4Tld4j8n/Z8E2GMpdifx1\nXbZk9Z62oeTixAbepByXm11r222dU0PLugGCFtA1aurDNlEbpQKTAx/jy84W\nDD/Ad1GZgNslglb+1Ge2vsoN3Jq6P2ZDjxV+kjXy35RftXhgvMFpMjQ21GjI\nUtHKoaVxATIEOv75eBEdJjkXDlnVlAUo3Q2Voc1hLOvCmqkQEmCWsb4FuU/U\nX8X20e/168ePuCihIxNpbhtldIaMHYklxiXPspKOg4KY2hS0OFc/y+BJU8Yc\nJM3Sj88U6H8sv1zn8YIyBb6rZzRgOwBP7Rb9Gl3wlTT0YcAo5UKYJZelW9Qe\nxN+gRM4EdzI/42uSU94hqoLoHYdgDhxqEEJUu0Zb86lhG0pxqLj0yMyRYp3b\nTqDdUglhPquZ2W3wbFTPhTJWyBeS0CUxj23Aq9hchMooZtcDXaUGZqTHBUI1\nk+lB33LCoTrqV0HlcRFKJDDJUdpdihhB190UgHkDHrmxSoc29dJ6+3qygFTr\nLaOzTTNJpb3P60ThVYe1npd+fnIwLFdsStUnyiKX4BeczQ4+yA75s3yIUtr5\nl6zF\r\n=Mcve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "gitHead": "8e93c576d7a3de552a031e9900f27ee0738948f5", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && node ../../node_modules/rimraf/bin.js ./lib", "build-cjs": "node ../../node_modules/typescript/bin/tsc -p ./tsconfig.commonjs.json", "build-mjs": "node ../../node_modules/typescript/bin/tsc && node ../../node_modules/@babel/cli/bin/babel.js ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "lerna/3.13.1/node@v10.8.0+x64 (win32)", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.6.0_1555400332956_0.6308091415433528", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "react-icons", "version": "3.6.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.6.1", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "794da15fff7788907e0390237e13a50692bffd00", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.6.1.tgz", "fileCount": 68, "integrity": "sha512-TpdkxqS2zohHjMjO58AfMB3aW4HE6Hn2FWX2HhIbfCptHpPzW3fIrXRBSOhkzsm59CZUr7M5v2F/j3eBwws95Q==", "signatures": [{"sig": "MEYCIQDYJkk/EUWryPNjE9UEdlxTbgJ5jqnbkfGNQseEKRwt6QIhAK8z/qpv+BmpqZ7Kzki6UCm9CzOGNFru3He9+27zLhS9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20478541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctaQVCRA9TVsSAnZWagAAihoP+gPP8xXMilSaxKvMjSzW\nE/jCoVuzeg3Hj034uZiBzU+/D6MxfV67M791IeIbR+C2uoZO4ZLyztfPqQj0\nQLPSYoo0qKFjVf7NwXWrRrJPUxodBhHV+UzVb6T35RkpgDAAv7VjvfTmW1cl\nePuz3AzSiijpMrCVw1YqZh7bzTV7nLCLG4+GmkgsHrpJQkbrIJemGRJH/y2n\nOlPVeoBzan+fIbjkN1ZvXyBCfQO5346H2BH+C7SXQTHsRy5VSOYv3UinAFDh\nKR0YS5l51GvphjXEM2isxd3JGd7iQsq+GaFPYXLnU1savPbKfuPmdYRRX08r\n3OoHyfK0lg0UHxKEMA2dWQqn1MF+8Y9y52RfLq/RoW4nVvzGjBix0o0Esp4w\nl34uQaG5hXtdarCNV1cPsf22jA6vOflLOLy6J6IF60vLsewUr24UtVPGD6Mr\nlgEbAz4ApOPkG6iqz8byHJSQp7/wtQ/fdNJg/EM85O3fhFzEFU+ScvVt5mRY\n4SbLrwO1XppJYNlSz5+wpkNxSMaymfryX66v5iDoKmFhQAdmKhTEx5G+d9it\nuaYq3bE6kt4cRVSGM86TUdtlwBTwVHxnm0/lXyXMFR3e57p9GTdyllZyGml8\n8mYh7ZOxT1sSwRy9Q2J2w93TsMUwpS/b0xdjHnAyk79vfYKRt6FzpEX9ppnw\nF+0v\r\n=MLcE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.6.1.tgz", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.13.1", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.1.2", "typescript": "^3.1.2", "@babel/core": "^7.1.2", "netlify-cli": "^2.0.0", "@types/react": "^16.4.16", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^7.0.1", "eslint-config-google": "^0.10.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.6.1_1555407892637_0.3257245234148236", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "react-icons", "version": "3.7.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.7.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "64fe46231fabfeea27895edeae6c3b78114b8c8f", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.7.0.tgz", "fileCount": 68, "integrity": "sha512-7MyPwjIhuyW0D2N3s4DEd0hGPGFf0sK+IIRKhc1FvSpZNVmnUoGvHbmAwzGJU+3my+fvihVWgwU5SDtlAri56Q==", "signatures": [{"sig": "MEUCIDL2N3CIR4PYjNvMjgebeASRWaTwt715ecZKBULmnF9JAiEAybjjsNG5np2jduaYVbW5EGdOlbX22AsGiiF+h9jHhnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20477640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1RRSCRA9TVsSAnZWagAAdRUP/1F6EAg3szeyo++Y1dQC\n+cXJY5LbhKKyMBIYx3BHQqBzuYGzytHTGs187AI64BSyEMYxve56qDbp0/xm\nJGV/ceKD+Vp0WLkNVkXk/1gKnKNQqQvITtI549WRvnDohaNlGVErHL3k82Ma\nnXdS+WuYpw2b89z+/B2H5gnS6XWPmXLOrv7GpvPmHBG2QkYYhNv3XlsP2r1k\nUXR25RoUGc69kQsLqpPaAFlzYJPnCEl8tJVwRggAjHmxaQgucWE9chS5J2Nm\nuhZD5QUyw3e6JOm9R1bhCUqOpy35pBivMw+YkjPE30v8JwlQdV2Fki4LVoH1\nVqp5ska8ZX5ixFwE28y8whdqmeHwuZoDvg3v22ma5aAc+QvbHPz8CqiCz4Mo\nwR4U9bLWc6J/nnNJC3ZrnkVQIEAMi8I3+LF7O3c5ma3OMPeBf1h9l+aFV1yV\nn6n0GQtI9H5T1pTxrujVA0QZzw+OtJ+987J9GsL0yKKhUoZtq8ge++8R12/N\nkRkSo21ZtMkdf+xGcDM15JZ4uYpX0Cak1oAZau8GuIXyAByJyX4UHeVott3L\njENGidZp0hJdq5JHkl1O4LOI7vVmeprpe2TZbDev0mlhmzOwbY4i7WvJDKsv\nVqKOz3Vs8bd5Jbti6xN44wjavgmHMAr4w8DHvnS0VJcU6OVIj/QCHNqazIKF\nEOPE\r\n=Ycou\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.7.0.tgz", "types": "./lib/esm/index.d.ts", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "10.8.0", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.13.4", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.4.4", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "netlify-cli": "^2.11.18", "@types/react": "^16.8.17", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^9.0.1", "eslint-config-google": "^0.12.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^4.2.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.7.0_1557468241457_0.18378223474912603", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "react-icons", "version": "3.8.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.8.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "229de5904809696c9f46932bd9b6126b2522866e", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.8.0.tgz", "fileCount": 72, "integrity": "sha512-rA/8GRKjPulft8BSBSMsHkE1AGPqJ7LjNsyk0BE7XjG70Iz62zOled2SJk7LDo8x9z86a3xOstDlKlMZ4pAy7A==", "signatures": [{"sig": "MEYCIQDanyZ9JOgzz+487bOGHnqsNTCuUqwjyk9qmYBs9iNY3gIhAJb79ZtNASGFExWhCiILOw4VLakhWDeIRpaj1+94pNhq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21785261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdssUjCRA9TVsSAnZWagAASxYP/3MwZQDWlTZlSDKPC7h0\n4orWmc7UbHWJg0PvFMsz3f0QQvTWZhfSH2NsSO6hVA0nP7FAaO7iU6OzibUi\nACxse1wMCzcdrQKEjvd9T/5+ZXKckAFLAgRiNDRCSPr8g1zkh6iNRCCirpV4\ncScs7QfrwM7mQMETaF65i6W3tEsHr1iBs0AooXdz2sYEon18wxURHOgBwqaz\nhJWpd4QwwWo1+NEsH1+CeP4kWx8v263omO3f544HkOSoF/o84InQwH8HO6o0\nXSqAdavlnWVOVHzKsG3UlnVDmaQjPtZcTGCiEdzI6uCoj0xPdONbIzZpe+m9\nLRCPgAbKfyy79uOHZRmOLvIfWFAbhRHXEq/d7OS5FiPHtU4MEOhmhMsalZqY\nLGOUEuXB9fZWDtauR/3cnzemrU2ZtxU48UX+SED+I5Ran1sYlrTw/P0C9gq2\nG7J6SutCdPBixNR9cXde5o8ZOYpDcAY1Pn4bLpi5rV6/ep6IisepkVUIDGpq\nVzFqZm6sLHuC5JOlPaLVktzeoOwHnyYTrI1eUaNUtrDc9TeKopUvkcbVl8RA\nsNAPK3eTKIK89qqgwNBVxS9W+LA8Xrb1YtSZ09EOfSYjIK2T8bgkdZ9y3b+i\n0jr8KpWEZIa1+9vsXrRUe/wwnFosRZrF9KM4r4DkzJpoJHfQ7+9iWP6Ht6ti\nb8LS\r\n=hrUT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.8.0.tgz", "types": "./lib/esm/index.d.ts", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "12.3.1", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.13.4", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.14.3", "camelcase": "^5.0.0", "@babel/cli": "^7.4.4", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "netlify-cli": "^2.11.18", "@types/react": "^16.8.17", "babel-eslint": "^10.0.1", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^9.0.1", "eslint-config-google": "^0.12.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^4.2.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.8.0_1571996962581_0.035667136272448774", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "react-icons", "version": "3.9.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.9.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "89a00f20a0e02e6bfd899977eaf46eb4624239d5", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.9.0.tgz", "fileCount": 82, "integrity": "sha512-gKbYKR+4QsD3PmIHLAM9TDDpnaTsr3XZeK1NTAb6WQQ+gxEdJ0xuCgLq0pxXdS7Utg2AIpcVhM1ut/jlDhcyNg==", "signatures": [{"sig": "MEQCIFOXm1PV7gHy48JcBJCfbAWk2+rAlBJH5LvPcc7RbjBZAiAFGYNesEA1GChJCOnU2EZ9KF+wJFM/oMN3EHhIEQHM6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23284833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMnlMCRA9TVsSAnZWagAAKeMP/2ChDLk4YSyXccBiZHHo\nS574CP1pJ8Z20Z1vtMwSV1qopMF2mdiVCIHp4tmSvubWY5NvFAXzPU2Lr7J4\ng20OjFiJB5HhFiLeZD751x/ddzCmf3JEb/BmZs8AXZRhr6/akHiCusKJ7NDS\nLBb/BcTLZ91a8qG56V9AbuQF9r4+Jz3D5JJFW3wveTvxp7L9U4TyDyDMSmws\nzBMohiKu08XbptYfma3z2Ak0w1TR+swOAjYn5RzrqNHgJKIeIcbRtruGIx2/\nxERVgprpnOrzGAaJl4cuuGIdUkl6rL9cqJzcnaUg1Qy1jFQkXQNDJv/oHuTp\neK8+BZ0D+jwUz4QyvcWIUxjJ1jCa1lqVkZe5h+ygzpH8WJVj5VuJom3EgtD+\ntEtfghsyzQsAGXp3auQaX5TeABKS61Tfv50ynrklotuNpm6MRpq63oHdLrVf\n2wcybcyDxdy6UHxSwzfPdyEw+gK+QpW2uGlNINHWL7W4z2I/QUfzGGtHpmx3\n1RX8vFqLc41SKCvOwsP4i4MNngh4DjuqHcoyrz/qy6Qj2Q9zGV0lIZB9eDTA\nxDCqVDpnm184tU3pddpX2JRHUoGwYqQVIa5sSjhhQhuvMF/+R/ovPJIIAdsI\nBUfZU2wLE96RV9p9SfVNnfHAnMgBq/WpxCDCZ9kIPUaHznxRVMQymv0emMqr\nYATQ\r\n=KjYi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.9.0.tgz", "types": "./lib/esm/index.d.ts", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "12.3.1", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.20.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.19.1", "camelcase": "^5.0.0", "@babel/cli": "^7.8.3", "typescript": "^3.4.5", "@babel/core": "^7.8.3", "netlify-cli": "^2.30.0", "@types/react": "^16.9.19", "babel-eslint": "^10.0.3", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^9.0.1", "eslint-config-google": "^0.12.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^4.2.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.9.0_1580366155511_0.9468574973333332", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "react-icons", "version": "3.10.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.10.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "6c217a2dde2e8fa8d293210023914b123f317297", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.10.0.tgz", "fileCount": 91, "integrity": "sha512-WsQ5n1JToG9VixWilSo1bHv842Cj5aZqTGiS3Ud47myF6aK7S/IUY2+dHcBdmkQcCFRuHsJ9OMUI0kTDfjyZXQ==", "signatures": [{"sig": "MEUCIQDIuzIs/oVHehzoNOo8sDx61fA0vUDBkfZ41ap4a9DJdgIgLKtTbU7bliSytz7iLEKVIBuiEhDwd0dmGVgEpez6XHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25630455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeno5qCRA9TVsSAnZWagAA5uQP+wZqaEx6PsEPSRYq/Jy4\ncrrJx6Ds6FzlXBlIC4JqbAo4SPPkH5AxZm3c4+TfEJI/AlnRC5mSHSRHLGao\nz6vSn+rpYmlvUg1xEJBTC/ZCI32r8WQgHJBsp3Vq7gbAuP2/SypYAqLwKTrl\nE3Mf/bikZLkOmXrgMSZsER3VhmRmvDcKe4rIStOZ4nUv6s07ePWDjtUDxatN\n5mxudrgREbf5hREBYHmWWk+cpLMMrh33MX3UT6wbFR/LZwvJZXzkERq1p2rE\nbZMbsUxJ1fYrnHAv0J+SWEqlOx4u2/nazLIdsM52lABVjdRHDX1MlQsau2wH\nWkTe0jxp4cwu79m5a+K67/J992pne+KQ02//tUdX+5GPx5D/eZ3nnOj3sZoL\n9xrFOXst69p5inmgSFTYc0MK6JyHuNbq+vIhee9OFHOWKhwX1rXTQILT6eD3\nJZwp8ShoP6QFzeTJVxsDbhzfK0F5FbN8HMU6QKcYr+gwqyLiPWEDHPlPl9uW\nw6aJZutZp9ApeETOAZOkn+PnDByOU4uo0nxSnlyGRTQB2fRS6BwgRONbtkPF\njFK5cuuVOSbcNtIiIxB+LzCJ2Dm7n1xnCl0cgtRa5DQGclkegD2Ccnzn8Gu6\ncaDa/Wm3ar06lw/9IrCvZysaojrIF6JMaKCJMSU4qqBTH0bO4O/Q+Y/Doi1z\nzRfk\r\n=6HcY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.10.0.tgz", "types": "./lib/esm/index.d.ts", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "12.9.0", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.20.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.19.1", "camelcase": "^5.0.0", "@babel/cli": "^7.8.3", "typescript": "^3.4.5", "@babel/core": "^7.8.3", "netlify-cli": "^2.30.0", "@types/react": "^16.9.19", "babel-eslint": "^10.0.3", "find-package": "^1.0.0", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^9.0.1", "eslint-config-google": "^0.12.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^4.2.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.10.0_1587449449196_0.9718955249110206", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "react-icons", "version": "3.11.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@3.11.0", "maintainers": [{"name": "feroc1ty", "email": "<EMAIL>"}, {"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "2ca2903dfab8268ca18ebd8cc2e879921ec3b254", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-3.11.0.tgz", "fileCount": 115, "integrity": "sha512-JRgiI/vdF6uyBgyZhVyYJUZAop95Sy4XDe/jmT3R/bKliFWpO/uZBwvSjWEdxwzec7SYbEPNPck0Kff2tUGM2Q==", "signatures": [{"sig": "MEUCIQDmLFEL6NEhSAkcJdIaEvr+nd37XIzhoEcoZqKHgx8vowIgQwBPcwl0zuxRfvhjNYmvFDTG57cmLY35OLehYzJsHQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33620634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPOfFCRA9TVsSAnZWagAAmy4P/0zfvc68Cw9wwafHalJz\nb9C1CTzJvy4ZYcXAiX7IbzFgTQjArF1Ln6fpzEIk1HG4jJvneHT5A0yG8xdR\nfrLyzmM/VkjcdS6zbdbnN+OePIce/Od7gbnt8+MACTjs0LPXT8UiqRiAJkMm\nvmlpqNlhzJH40aevYfYLz9gtwBQt6Lg76DDyhc1s1l0ahZhc52UoFijayEXU\nnDf8dmbOY2vyaQXMaS70302jOIJtcPo+KALEm3GGpVfJ6dRttcGgAAOg65CS\nV5CXEN8YvysuTfzqLkfrMjoNnJlZ8QggBxEhoLsy1LLGWDIDJqfFUU5JmEZJ\nMy++ZvKOhBzlXTCzZ4B05zpqT89QKvIAzMqgxiWaE+3ACIxORewnG9NAY6im\nFH9yfLW6cbfdihWc2PnAIgfkocO6sfw8pYHVQ4B+7ZC+TETzcu2p7TXUi+Ba\nwPTaKJzdL0K3WFvG1g1eO2rfB6x/rPvJnzi8wZ4F5xXPfUKrc+wMQNYTU8AB\nQJddp7MM+6gDoSmoqspKqGGb0Miqc1+fXxsQFf6yu+fF4Hv3gZecRSQdVGv6\n/o0XsITE9P3KtEKCKFYdxwvldJNzYDEE7t5fuCq8oNvL/avlYkctUpywqGFJ\nIIDXQSS73F4AUQPolT7zds5tUYDtcr3nTYfY628ceQlEMggtYEEgpPLFybrD\nvvQp\r\n=c21I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-3.11.0.tgz", "types": "./lib/esm/index.d.ts", "scripts": {"build": "node scripts/build.js && yarn build-mjs && yarn build-cjs", "prebuild": "node ../../node_modules/copy/bin/cli.js ../../README.md ./packages/react-icons && rimraf ./lib", "build-cjs": "tsc -p ./tsconfig.commonjs.json", "build-mjs": "tsc && babel ./lib/esm -d ./lib/esm"}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "12.16.3", "dependencies": {"camelcase": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"copy": "^0.3.2", "lerna": "^3.20.2", "eslint": "^5.6.1", "rimraf": "^2.6.2", "cheerio": "^1.0.0-rc.2", "renamer": "^1.0.0", "ionicons": "^4.4.4", "octicons": "^8.1.0", "prettier": "^1.19.1", "camelcase": "^5.0.0", "@babel/cli": "^7.8.3", "typescript": "^3.4.5", "@babel/core": "^7.8.3", "netlify-cli": "^2.30.0", "@types/react": "^16.9.19", "babel-eslint": "^10.0.3", "find-package": "^1.0.0", "glob-promise": "^3.4.0", "feather-icons": "^4.7.3", "eslint-plugin-node": "^9.0.1", "eslint-config-google": "^0.12.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-promise": "^4.0.1", "eslint-config-prettier": "^4.2.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-standard": "^4.0.0"}, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_3.11.0_1597827012367_0.12159238342563805", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "react-icons", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.0.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "7ee18489472eb5797719bf997a1e2037b22d37d1", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.0.0.tgz", "fileCount": 105, "integrity": "sha512-HNS9Bsi73SImFZjwBAuaDIPfSqjLzDNZAN3PSjhZ7+7XJSCjkgFuzaMnyZetZXyawKwyoIPwmvGMSeFsEfsCPg==", "signatures": [{"sig": "MEUCIQCgWP4j5E3gxvotoWgoqM3MjhDrOht8msJQllh/27VHtgIgPa36rmhCKQ2SvBT+cmbgcd9cypVQI8WfUmMqwSZ8RCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33609099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvyrqCRA9TVsSAnZWagAAPVUP/iD2/YSf3QBTww+w70NI\nrii1pOGYm9QkCgMrAdhuue5huAg7AmahoPr+kkeoK5xqcERZLsriQPINT/U0\nCxU/1D5QFCFA5NabKN0nVWJtAGgyxmtBm5CmthiMHKwpQEqKkJ8gGAxM5L5w\n8YSGR8JRoyNGd2Gqp0l5VD39rxV/atUAQ7/TABFHp5RR2T+h6Qk6vlSqe2/g\ntYaQYNaZYos3U7U3YBSo4IgD0b8GxseZ29Ecmv2OSifUtXlWocFEGQdllDLy\n7HtpmxdWr2Ug5hTnONSVT5k/UidIsC3JM8aHH4wpwQvemUGKCtJ/7+qUprGj\nVNxy/gfhqf/hogmdua0pQ5B2poIZA6Il6hiVNNo7ciCZ9G8x6ywFWE/JcYCh\nW5bHp6VWyfD1VaHmpawthQ9eum9a+6BvXvAp6aoRvsBReSb4hiz8qhoZAzN/\n5pOugXWxCS+iFQIqiwPnXt9eY4YrxQ1ZYGy35+xx1l5ptbcT314ez/ccy8kT\nURMpJZuc95VR5hVpQJTbLVFiiQ7mjEdyG47wYNmmKENOClvFwj+qoryJIqsf\na5PTb5CbTnZ64ywIWXWGupQPCObEWgwmeH+EzKCXsITHNQsjpsps0qRuT0ag\nTqXuvjx5XleJNH0ZyBaEM2M2VwDJDUQpu+ZiFjO4JDJwznN/G9BvDBKgT+1Q\nbufT\r\n=JsOf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-4.0.0.tgz", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.3.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.0.0_1606363881020_0.3368877871154732", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "react-icons", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.1.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "9ca9bcbf2e3aee8e86e378bb9d465842947bbfc3", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.1.0.tgz", "fileCount": 109, "integrity": "sha512-FCXBg1JbbR0vWALXIxmFAfozHdVIJmmwCD81Jk0EKOt7Ax4AdBNcaRkWhR0NaKy9ugJgoY3fFvo0PHpte55pXg==", "signatures": [{"sig": "MEUCIQDi+X6GwgvRxidhAcTtDt4cTOwcCT/zH1y8xNS2mifVnwIgZhgDpYNnEmoD/LmFnqIGJx1J7Y8p0kGcpVsjsbY0bYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36458822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfv23rCRA9TVsSAnZWagAAwlIP/Atv7mR4Uzz8apVb9fvY\nU1isG808wh0wyb2pVRb0j7pafqeLYTAT+7yJ9oUTH5ZLBgVtPlDD/GA8yger\nEU0ziUF4KCRUYs+yhbP+Ota3JP940YI3s/zn1lfDmeK6CQEr+EZzFE/ZlJwV\nPc58v5ZLHd4OtY0Wv7SzzT6TZ/gzbLbmcu4c3FR1QAXp6HieBcpuc/iaY/3i\nC5r4ZRw7jk9JMjDSV6SdmPjA/K3anlMMYXtId4O6Vi9fsdTdtk5+3Q5p81dB\nRRu5aYKhaQ0Sl75QwbltHZNsPuFIIG1IkGwZ6kkWlHROLOb85SERiEh0hKt2\nBJwuqWuxktQAS0jH3vSffNV8HFLJ0IrFZoxK1UvxCgP4SCmaiVCauT+xxHy2\nMuFODm3vBsrzVEPK9D0WRE43DBF5mUUyFrRVvv3NUhjjWduo1B/RNepf8D16\nAz7uUgvfzpUfKZbhPp0fa0bIiNYgONJvafs46W//TUu7CzIdoWIcDeP/O5Bi\n69T4QYGktvPp7O+pcTFBipgCHRDC7jSjOUxePEMqhgHT6rfgzAcqJAh84axg\nLCOaf+pVFrh2l45gySy633UqcNDzN/b8XcTWlraGZDvN1zv8rkTlthCaLXxd\nW1orOAk0m4AGZmHZXRx6yZEkxsZAOpHrwyzWvDahf63JzdVLjqX5LN2k2hWq\n6Gys\r\n=iCfI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-4.1.0.tgz", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.3.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.1.0_1606381034199_0.6209788454722016", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "react-icons", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.2.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "6dda80c8a8f338ff96a1851424d63083282630d0", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.2.0.tgz", "fileCount": 109, "integrity": "sha512-rmzEDFt+AVXRzD7zDE21gcxyBizD/3NqjbX6cmViAgdqfJ2UiLer8927/QhhrXQV7dEj/1EGuOTPp7JnLYVJKQ==", "signatures": [{"sig": "MEYCIQC7KXFLihHiDUCJPf5C4O88VAR+Bg2BGeQVo6Rdmb9/7gIhAMddcfuhXh5Pwl0kYX3kdXJkyf85oihyjzp+Ou0OEuCL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36553478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgH2ckCRA9TVsSAnZWagAA1q4P/jqHmC4AyjvYyeDj8cMd\ng31u5RFIty5ZGJ8LdDzjPdznkMHaB+WBgXmPOOJz0iaGAx3NFjD53IGKRTJS\noKhYtq0PvDpFT2Ua5MrKq820gvSYVXAfmiXlAEYG+p1ypY4BhMGME2nXQo/f\nUBeyLJ/dHKcANRcEANv8xgTmaAD+L8L3/9hxEcOamWUDFNCiqCrTfOHXirfy\nKWISWzj/yZQaZEjnnOKLqexWtfvXcHvE9gfXxVmMmMTWwlCTBOloOXrND2yn\nnYalS6OABxz156PGvQ9XHhSlEJln4JJFg5MWlq9DCYTUpjKvNB3C+61GGp7N\nAyiUoGLDTotyq+ZZ22Rmc4RvIDrggKXEwGwO1wdJYoTVou5bzMMzjLIgmvbs\n3tM0R4NMeJPvbUrRksZKWpvM3IfPB6VN4EdYTj7nxjdyj8ai62PrLt4nTFz2\n42kn6msAQgSyouIEoR0bvgvDTMRoefaawmlL1lp3R/IxiEnwQXZRT9pOvQHZ\nMOpLV9mgHlPeVU7tfmCfhgUnk5U73OGpFwVIcUmOUFxPumrjjpv6ToyBn633\nvHVy5sJ0Eai13Vgs6HV80bRFJClurbJUf3Raf1ukizVP8OG5hLjInngDLNQ+\nmMC9vPv61aCiXq7ba1lGFqgoI5qoPpd8u8R72QbSjges9UHnibaokaJu3FnS\n9unL\r\n=gdLz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "types": "./lib/esm/index.d.ts", "gitHead": "e463db8c0305c98ecbf31dc2f10c4bb601962e60", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.2.0_1612670755871_0.9907624124445773", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "react-icons", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.3.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "c71b266a07a7e10e0c8ff7386e3e9c6727d33195", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.3.0.tgz", "fileCount": 97, "integrity": "sha512-Rb0pqr5vp7RR8K6QLWjcjvhlxUhQTmnBxTcIdA/8YIsLqZn0BB2kCm1K/5T63Hx/G/omvntxAnMkvwv2a6ojRA==", "signatures": [{"sig": "MEQCIC/hgiDQmf8aHBLKlFBqeCTvz5WWnNOxCjK69KSQzMhDAiBY4AYKAwqQtlfaIlPsSS0fGxHCTZrNg28kahul25R5yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3182293}, "main": "lib", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.3.0_1633193703298_0.8043959180526954", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "react-icons", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.3.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "2fa92aebbbc71f43d2db2ed1aed07361124e91ca", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.3.1.tgz", "fileCount": 109, "integrity": "sha512-cB10MXLTs3gVuXimblAdI71jrJx8njrJZmNMEMC+sQu5B/BIOmlsAjskdqpn81y8UBVEGuHODd7/ci5DvoSzTQ==", "signatures": [{"sig": "MEUCIQCRbzEyPtuu3W8L5a+FywbdUnEWCOQwtrndGkXcn4zIVgIgWC1o1SXTm4LYDtIq7z9zm3onn3/yPrAh4MYE5WBaikw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44019609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh24ZDCRA9TVsSAnZWagAAky4P/iqkmdKjhjIkSE1eH0ry\naNuJbzQfkFSg4MnIFQ4z68kdDQ+VHiXvx/pWl5iY4un4Nb3S6U7x42FLlh9/\npxcomQNfcUIQ4Bb7LnQFkH3FAEMaqQ4CItjMk4qQrcHFWOvyrwn8Ro2Rpp9m\nZix+UlZc4l2KO8nfG3/JxLp5GRF0nrAv3EANLBjMSUdo2Wr3UmR0IIJmKdEu\n5uEoOypTJ3FiRan6ByixQfyVcH/trccOfRLnFqaLDPfLDRrR/aeQgH3BIBjW\nq83zWWnK37xMDQZWmVD6mbXjkdPod8gp+o8COoovBdqqj1taTdNLFcernEHa\n3BtHTzMm5YNrjptoXV+DlePtwtoRnE7os8aA8sCaF9FdRTh993JSUf/N28yJ\nJ1m2wz99PXLFkX1h0Smthm7U4cnOSyQl1yorJIXAbqhl4XbA7wvo3AIoCZnN\nVuTh15VwfjQHkr1tc/nuQNFbkiU+YqrsEly6YjiOHAY3KPJ5HxJM92/FXLR2\nNgXq4+dlN1W+NQCF4n+owVkgi9CnH7qeYT8TMV4qAwhex/hmQSUgNpSGg484\nzm/odHrmez2WErXeCtpr89Vkh1asGIW71d6zyOPNfeN9WiLB/AMQTObc2bVG\nfvGxddOruxdFSZ3IzVw3B4c+U0abGhRqG5vfXaTDQVQNM0EMDhJ/ciK06Wwg\nBinE\r\n=x1h6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.3.1_1633269679185_0.24482530211870213", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "react-icons", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.4.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "a13a8a20c254854e1ec9aecef28a95cdf24ef703", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.4.0.tgz", "fileCount": 113, "integrity": "sha512-fSbvHeVYo/B5/L4VhB7sBA1i2tS8MkT0Hb9t2H1AVPkwGfVHLJCqyr2Py9dKMxsyM63Eng1GkdZfbWj+Fmv8Rg==", "signatures": [{"sig": "MEYCIQCoVGWH5svUK4f6HjMUREfv9WZOkCB14Nz5xb5c4Xm9BgIhAMxJafosGDVyFRbASvGiK0DdaI0T741NabVGY3VXtOD0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46523272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilUCwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU5xAAgZSihenoutjF45s9ZPmzpzgFq/Gjm9ygc1ceu12ozJALQawL\r\nyZotjh60kYut42ALl7KRqHY+oYP9mDiU28zOBBJvweRuwJO8nZ49s8IzBjDn\r\n4+OZBu1c+IV0EDkShFsWzlOrIrFsqmYcAEV39uAqrlSh17GjEsKw5F8r7I3n\r\nGz4EJmnhfhuDNGKqj9VpDQLBK4ssbufv+PqRb8KJ/zeJVg25uNjvi7QbSJ0k\r\nqQAbw/1GY8tsELA2kB531iRCU78uLJnyRaZBijPwtEseQIEyCXW/ugftFV+Q\r\nl+m48Z6Z8HRColwRKiHk0AS01c86Wl9qwrc2SvvHwvmNdByQweFWJuHbRnVb\r\nYJq2eltvXZ073QFg1Ye45n1kcUm13+FI9YYVR2OFrmPLBi27QEoZcCDZh957\r\n8a8EO4mGJGKIlhmJQHth7vwoHWnsaVxtTi4+C7cNEquPMM6/yeCeq9aSD/LQ\r\nY3jXRRiJPbo/sknlXBj6FrmR8+OutuI3OdjNnSe+oHs1EvA39UG3JNU3LyvA\r\nMoZRsnSPEDqH33jZU2SQWWqIMfWaU3liifnNfsFrb+Gu0wN/jxWsg1D+v+Lp\r\nXyIcY3V99hYPyGkLBYCNhsTDmyKr/6YCjJepbnnCT/ZggMr0u/ngSxXhOXw4\r\nEBunueIKyBJ1WlOc9M9G4sgFqSjvFPCLVm0=\r\n=PBaF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "types": "./lib/esm/index.d.ts", "gitHead": "dcbe5f09fb3e2537d27aa5247e681633deb91e32", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.2.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.4.0_1653948592167_0.290890767549991", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "react-icons", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.6.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "f83eda179af5d02c047449a20b702c858653d397", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.6.0.tgz", "fileCount": 129, "integrity": "sha512-rR/L9m9340yO8yv1QT1QurxWQvWpbNHqVX0fzMln2HEb9TEIrQRGsqiNFQfiv9/JEUbyHmHPlNTB2LWm2Ttz0g==", "signatures": [{"sig": "MEUCIHMJwssEQoIszBsqlCyYi0qvU5mCuQUEalAwXO2lU7qgAiEAxUsYxR/k8umT1KKWHDzne714MMERRTtXy47juWFSrM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48629701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSW8jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6OQ/9HytjYXhHNZo1afxB9eSttBhrTLbqvYgR4BGBYkitE+cNrkPE\r\nJ/G5VTqQF4vcR4sNOKGa5YukMTPDX3UdvB7rQ8squ0xBTOsJKBCLq3cbjsQK\r\nwFAFXcD9E0bu5KM01rRdU0K/RUYSm0xOl3f4PQxNm81klTUXJmJmV0AIqdc9\r\nRyjh9EetKidMM/wJUi3f185VsVHC9EEJohF0I8x62vlHA47FFxGUer8IxjJK\r\n1ca60/cieb768bAD5Y04oX9jAV5I3ctlnjvQJ1tIflMpjAMB92EpK6g7s8sW\r\ncGot3uU1v99O/I84MF1G2w48JJeQRtR/HRIpqyP+whuznJ590vidgO8bUYaU\r\nOgmY9FTFI44PV2lLxo+2MGSmXYN1y8m3vFFzAe+Leg7rT9uGHNCC0vEsxP0q\r\nr13+P2UnpBLcMF2giUmZK9WDsk3kvOHILJm6nTUnxPyIxOIAZmDe9BlQqxtX\r\nzwqmxQEQ+SHTNoUb7oGDuXpXZKGAumLKKT4xSXSym06g+G8VwiysKZpnybO8\r\nRbaEHTs7GWVABtljm/6FAz/sv9nIc+C1uj3GPJe/z1seXOHXC8FZ+d1IBhAf\r\nQciddSE8BWiUc5f9PG+5f8F8XtzasKJg3H83rFmtB4LH9kWRf7Nw6LgHMdS1\r\nU8pN74Ne9DRerdYDw/D3J7nDWJZSZkDMwnM=\r\n=8+5q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-4.6.0.tgz", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "", "_integrity": "", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.6.0_1665756963247_0.12148893171143604", "host": "s3://npm-registry-packages"}}, "4.7.1": {"name": "react-icons", "version": "4.7.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.7.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "0f4b25a5694e6972677cb189d2a72eabea7a8345", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.7.1.tgz", "fileCount": 133, "integrity": "sha512-yHd3oKGMgm7zxo3EA7H2n7vxSoiGmHk5t6Ou4bXsfcgWyhfDKMpyKfhHR6Bjnn63c+YXBLBPUql9H4wPJM6sXw==", "signatures": [{"sig": "MEQCIASJhTeCUnAWe89TV4miloJvIBkjefIH8FzkDbOARBvYAiAGy9gVv4qJsf55k3/RZGH8hH+PDpahSvghmi569ZW8kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49395885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhV4TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBJg//akdChdyXfc06o/eqYaLGvH4sLan+OCVldmFzghrd2sYG93Fy\r\nlFVYWi6TDizJTh4fAU6TjJb4fjlmbojv8fsmyAHQX3unrL/GzUeW5wbgEZi6\r\n0LmE8B80ncfIQo2No52Jpq1WeVNwhm2LtuMrmT/HMHa7e4vT8kf9OS5OyQwc\r\nY9FNen0jSYm2crtR6OcB7lMiQM4mfDzHTcvz5MAfRKtbH3j19GcvFVsjObrq\r\nEEqZceZeASiL0loe9PiGhnJodsk8eepPQScfUxtaJa04eR4yHvE6iRG2nxHn\r\nMU6nLU2XBOFTtvqqOf8B/vN4BKqiWliap0WEi/MFFWL1pQBx520wbH8lOm+e\r\nVbpVaQf2vqns9h5V/U3CMFfXsj/60HU1un3hZM0fMW3CQ/jbpbuQFz3Ye18W\r\nofVedOOSOsOS/JHS6yOxYAdVtWvNk3ni8ajRbLCdiKbC9+SW8s/cvHHV6+Wb\r\nGJ9e8dY3rB9jc7ZtInYOLJfOBGb01k+ZaY3u8Uca25yVgRUgs8VXRCF03phM\r\nnqADad5EBG//mESvhiGhZ+sr/FKHlgowx6AcU3Qs3YA7TeWDicRUYZaUI0so\r\ny0vGE4sc8iZ60/g2XrZqT670+dFiXMw8+Ju5OYa8LvgFiixNTVD1uK3DPdeF\r\nT9PtRjX3hh6DOp09LJFDcyydwlXb9DqLWuI=\r\n=IiVG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-4.7.1.tgz", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/tmp/react-icons-4.7.1.tgz", "_integrity": "sha512-yHd3oKGMgm7zxo3EA7H2n7vxSoiGmHk5t6Ou4bXsfcgWyhfDKMpyKfhHR6Bjnn63c+YXBLBPUql9H4wPJM6sXw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.7.1_1669684755220_0.8432951323399005", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "react-icons", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.8.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "621e900caa23b912f737e41be57f27f6b2bff445", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.8.0.tgz", "fileCount": 133, "integrity": "sha512-N6+kOLcihDiAnj5Czu637waJqSnwlMNROzVZMhfX68V/9bu9qHaMIJC4UdozWoOk57gahFCNHwVvWzm0MTzRjg==", "signatures": [{"sig": "MEUCIQCWlFnXcwHALu3VzjdmO5TnUqgEfOnrHwvEDBzDM6M2aAIgJdPtNnM3EEtdj3vhBFCXfG20fNXAZj9jsseKZpVFurc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55658189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAY1fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjWg//ZkvvJusBm/tO5DKzabT6vWZaUqrXbuulqQUE7fcGCo1QDRR3\r\nOmFwqBzlDDaEehmJ+/0Xw+h3hkD4VOdmeEPdrThas9rpg1uIAKO+o4dQgED7\r\nUo6bxYLVb6uQnSD8lYAEDhAVNEmcQG5SNeXvdzCpojtM6rmKTxDI+McSJ5iT\r\n6VNwu1kZBPTg8eQ72PpVuTTheUXQvY6FSl/dhaFwD8ny4s+/wlJdAsIo21IK\r\nvNyXelvmrbPkUJbnJqQrqj6mgTlcDOhM0o9jk50K0hzD+j6LUgSjCcfR6gyX\r\nAL5ca7Bh+3ffszhcEFNBX1R893m1GCn8Z3+LM0hxOlgbl0DR8wrCUu5FqB1Z\r\nDhySd83Z4EA/sQ/yhmqOGY7P6+VJ3TNo3gNqwRDIWutRY4qxgZkqCNhvIEI3\r\ncEnfjNVBzY7fj+L5ILBWAwl/2QsCRrsIt9zDu2bqbcV09KMPM01aFTTKxeP0\r\no/Mf5H+Wb56xg+3nTiGQKMYxZuVu52qIIay21BpMos/d/8hbHBaYMEQKZw0L\r\nwdqPOKmZzSOtPjjxFNSOvmHWs5Kx2892wKt8hDMCMvZWurM5eNingd4+qVey\r\nEeTLZke8rTf+VYD66DPCPiWLq915opF3qy5Q0LG5UGh6wddEPjYKO3JYNkEY\r\nOiFdzmo6sESPvCjhxDvQnXVirvyFwtF9D7g=\r\n=F1Vz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "_from": "file:react-icons-4.8.0.tgz", "types": "./lib/esm/index.d.ts", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "C:\\Users\\<USER>\\ghq\\github.com\\react-icons\\react-icons\\react-icons-4.8.0.tgz", "_integrity": "sha512-N6+kOLcihDiAnj5Czu637waJqSnwlMNROzVZMhfX68V/9bu9qHaMIJC4UdozWoOk57gahFCNHwVvWzm0MTzRjg==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.8.0_1677823326986_0.48171360629653237", "host": "s3://npm-registry-packages"}}, "4.8.1-snapshot.0": {"name": "react-icons", "version": "4.8.1-snapshot.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.8.1-snapshot.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "ac6324381ab9bcbc92db3730309061a917988fdb", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.8.1-snapshot.0.tgz", "fileCount": 116, "integrity": "sha512-pASrJ3vPvm/DIMNFaQi1HsK7Y/QiwXQw9858JEoWvrvmIAhUoYRGcUZiFTV1JaFDKhkrrMaREyhjxWnzNjhn2Q==", "signatures": [{"sig": "MEUCIQDvM1PctLzHblHvrNL10GBcPwmH615zZIJ7YS9zKulgWgIgcD3FgM/xvbrrPlx1SFklTgiO749cgrM/00K659SKhdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55638071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAZW9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQKA//cbhDDaZyPmAR5nmnb/60AvvL9gL1njJHJGhmieWU14EGpSbM\r\nr3eHUEsrsKzsNeMwiMFoHs7qKyPXHn040d7lANpEwehToMR2U3A3rqmp8Brh\r\n86ISO85I741tE1Xny950YnuMuC0zUbWpxXnK2avTPYiXS7xRhIeXZsP45Byc\r\nFf2lMulGfwOPhiCMevr+6xrqsuEGQNQ7g3swsKvOqVWdJfRoGo7MmWVUnzmS\r\nZsuk6sLYSJjlDXG+5KJolvwQJK57nLPOWK91ztDGAEHOl7Rmu+WNu91x7kO+\r\nnHis74Cxb+l2sHwp3e+VmRnthsiZC7kisVFHfWYglBko/fk4fg1oYvbxg6tB\r\n55kWqFf94/DiDTMAn4SlbyWbQjZ7rTrvMJu3PSvMGk8sVTKjMg/sbCtWcsb+\r\nzKgHFT3uCz2s0x9ne0SwQVXQ3i+oJmCPqOTNLMJavoy3/yEUwtmq27gvf29k\r\nUkvwSVVm475EYKwB/oFN34lmeGNFW8gKQtWhg4Rd4zLZFA69sPb5tI3tzV1a\r\nymk902H2PNfrfi8DU6A5DL+hsgSVIBhwLpSpXIbIO4s/uLmvGufSa0QVrh9I\r\n9uOm5Ar2r/hlio6ZsYDRLPPoBPvWWJpzv/zwNefdR0Sqm5vvhxDGrRSCC3Gw\r\nglcQVF/dEgn9d9Q6EXYyD+8VzneivZlFK3k=\r\n=iSrv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/index.js", "_from": "file:react-icons-4.8.1-snapshot.0.tgz", "types": "./lib/esm/index.d.ts", "module": "./lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "C:\\Users\\<USER>\\ghq\\github.com\\react-icons\\react-icons\\react-icons-4.8.1-snapshot.0.tgz", "_integrity": "sha512-pASrJ3vPvm/DIMNFaQi1HsK7Y/QiwXQw9858JEoWvrvmIAhUoYRGcUZiFTV1JaFDKhkrrMaREyhjxWnzNjhn2Q==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.8.1-snapshot.0_1677825468443_0.4541148610023036", "host": "s3://npm-registry-packages"}}, "4.8.1-snapshot.1": {"name": "react-icons", "version": "4.8.1-snapshot.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.8.1-snapshot.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "978a3d8b5948de770ff442038e862529ecc7fe61", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.8.1-snapshot.1.tgz", "fileCount": 133, "integrity": "sha512-ibe3enpOkR3VVeQiP4p/n+8GIrcrwE+dmkvNlP2XXYT84LlKfSSczdZeBb4hhp/B/4VIRUdaWuLCCOKngZbwqw==", "signatures": [{"sig": "MEUCIQD/aeD6r7/gOx3irM5nvMRHWq1A7fFQaZDc5LgRs5Jt0gIgRHNILAeQCIte8efoqahQj9UxZT4/j6Z6fAH5B+hW2Vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55658239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBcDAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY0hAAlUEXhmmjIaM/l9Gmjg2Wa1AT1s3UDQeJVjibbcuaIPD+DxdW\r\nqsr6QKl4nuuO96AkneBzLVibbqy7M9KhlpZKfAcZoIT1cIiajc7XXafcbN3P\r\nlaKoSOgo0IJvqFhNDCF8lZ9eIsbnTEvyZUHWihasp91Vd64n+KeTgcyriX9C\r\n5vdfoHDyfuT8S1ZYDeSFHm8l3FAMgUCPXirWyVyGwkIhuzBXBVoHIkKU7aEW\r\ne1Wtv+wsBSai+AOefcVr+C43TjX0fyoW3HFFRHADEQ8T9yIII6EdVfA699gb\r\ngxDLTG1mE7zawMERrYbbQbRmfxz1v58aMK3JPuyfn9Ebkcuu0uJTNLiXm+9G\r\niazW4l8cv11pxkHcZmBU/GzpbFY6cyBtRMxXFnmf14iX15rtw//Q8Pz8sB1u\r\n5M78Y72+hVFK+AlvDsyMscmhoW7bdctTheWT0WHH87n9of99NDwST3d11XD7\r\n6WadjPl2Nqk7Jfaol2ui+PYF7Ry8LUsriKgaU9I/DIgbMMyi8RshtAP6ojMO\r\nCJKuHkLJ65OOs5KbidDW6ybf+w1W4zj8nd636RZnWdWvQfNlTho3Ywj+87Tb\r\n0hBzg5gzs7JQCjYy/tPsVvTY2bJDghOLc84C13zCvQXFvVWwNHRDWqdLYt8z\r\n2SZ8fic3tWclUvPjnjS+2PZz510/rgi1tYg=\r\n=THiT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.8.1-snapshot.1.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "C:\\Users\\<USER>\\ghq\\github.com\\react-icons\\react-icons\\react-icons-4.8.1-snapshot.1.tgz", "_integrity": "sha512-ibe3enpOkR3VVeQiP4p/n+8GIrcrwE+dmkvNlP2XXYT84LlKfSSczdZeBb4hhp/B/4VIRUdaWuLCCOKngZbwqw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.8.1-snapshot.1_1678098624482_0.5623994375156569", "host": "s3://npm-registry-packages"}}, "4.8.1-snapshot.2": {"name": "react-icons", "version": "4.8.1-snapshot.2", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.8.1-snapshot.2", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "336ecd15cacd99c799df4e7a244e2f99c5703432", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.8.1-snapshot.2.tgz", "fileCount": 131, "integrity": "sha512-EC41ETSFRtpAuwstiDw/8rkjWbw13TQAQDIQDckCOnAgf1bPcXOx+OLUzCNpNd4sPwl/P8dSFqTwSUYybYWmJQ==", "signatures": [{"sig": "MEYCIQCa64x/z5xRHSM3CNmmhNtlsUWomfSL9SKIMUbe0q620wIhAP3eDYbxy4wU14aYDoM0ElkOQjw13EelDyKDUvLaDr3/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55657161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIoOsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriBg/9GnC6NDYUt91rwe4QhKfqSVrz4qoEadQMaTzeRaVAQpp2UVmX\r\ni3UgsWNuN3YY6Vy2UtE6IWPZo3SGVgJr7vMLuUkCyMF6/ylhq3ThJgwDuWvS\r\n/U7rqSoUVm3FXZRPDSEVpRsWhC3AZtIpS4k+KxWevmgod/0m6nJTMgD3+Ehg\r\n/QgaQF9kIlm94//theJzkb73BY3ka5G6hU74Rz2ghNKZblujPirgAtg4bGBJ\r\neAcJ2OIUiVulIvScQv21K6LVg2ud2qybkW2Zh3TgiPhDHeFhjLfPPsTRLwM9\r\nxTcvCsxiqU9EBGJr4IVncjbwpHzH3U82qQ3WgeSfqyu5JQ6aQ6fZ5i0xwZxi\r\nJOoU/o//HstzBHBdzamLBwQkFeMiAe0Rcf++BMXFFDiaPVhKMo0+jZLN4lSA\r\nThbDlvtnj5UZ3/r8ZE4NpzFKgJMOQh+aPwm6JK0hxWYkQj7AfFeZyZtngHl3\r\naZquUMBgvvsN4yE404F7GjoORSaih2Lpx3UM/WVjQtasnLCu1QV5rNAxRmND\r\n/1TA98Cm7mU7Ik3mCoa7BnFbm2f6PJcJ586roZz3suLoRyEcfBFug1VyTZBm\r\n6nYhKCG7AWWne/O3smzOAiU/1xqbgpGsZtmKGt3YOywrjDv918OSGU1zjYHI\r\nxmMH0zqGM5wPhOAyhqHE7gyGRouJvbGiCuE=\r\n=uxsc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.8.1-snapshot.2.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "C:\\Users\\<USER>\\ghq\\github.com\\react-icons\\react-icons\\react-icons-4.8.1-snapshot.2.tgz", "_integrity": "sha512-EC41ETSFRtpAuwstiDw/8rkjWbw13TQAQDIQDckCOnAgf1bPcXOx+OLUzCNpNd4sPwl/P8dSFqTwSUYybYWmJQ==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.8.1-snapshot.2_1679983531615_0.6510019656317838", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "react-icons", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.9.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "ba44f436a053393adb1bdcafbc5c158b7b70d2a3", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.9.0.tgz", "fileCount": 135, "integrity": "sha512-ijUnFr//ycebOqujtqtV9PFS7JjhWg0QU6ykURVHuL4cbofvRCf3f6GMn9+fBktEFQOIVZnuAYLZdiyadRQRFg==", "signatures": [{"sig": "MEMCIAjPVHFUCbrKRpsmxhp2Y3RfvyS9CP2+u0V9Xd2G80iZAh83F4DzYmiZ4H7KIfTEeoBGE1Jeq5pUaQzZ0PWFUijw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56701398}, "main": "lib/cjs/index.js", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "gitHead": "65c628616403aa7ceb1aceeaffeb43c378c80216", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "19.9.0", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.9.0_1685284369434_0.8058962271413936", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "react-icons", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.10.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "387f8ba8edb75dcf3b7d640ecdb7e9f616e22c48", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.10.0.tgz", "fileCount": 139, "integrity": "sha512-SoBQp5sP2NgJgnzIoW/0foYRzuB/dlbfaFaFv7N5c7Mk+WvzAIKwvld7LTPLhRo+UQA5oCzdtm9h6oBKTbuKCA==", "signatures": [{"sig": "MEUCIFmn3r9boo0DBSLu4605zl8rRXy7YdRaQRnR1qBnsgXlAiEA43+MJW9HwJUwUIvFtmxAzazo4xjXZBAZUhBoB96LdpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61211602}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.10.0.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/mnt/c/Users/<USER>/ghq/github.com/react-icons/react-icons/react-icons-4.10.0.tgz", "_integrity": "sha512-SoBQp5sP2NgJgnzIoW/0foYRzuB/dlbfaFaFv7N5c7Mk+WvzAIKwvld7LTPLhRo+UQA5oCzdtm9h6oBKTbuKCA==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.10.0_1687407804985_0.31896071525988834", "host": "s3://npm-registry-packages"}}, "4.10.1": {"name": "react-icons", "version": "4.10.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.10.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "3f3b5eec1f63c1796f6a26174a1091ca6437a500", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.10.1.tgz", "fileCount": 147, "integrity": "sha512-/ngzDP/77tlCfqthiiGNZeYFACw85fUjZtLbedmJ5DTlNDIwETxhwBzdOJ21zj4iJdvc0J3y7yOsX3PpxAJzrw==", "signatures": [{"sig": "MEUCIHhej/sObvuZi1ST9/+OV/o5QuxNdrObcVs/SUQBETU1AiEA0j01FcjZS5E/CDF348eZlGXKfTnkdTlaZhLyS+2YXCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77662681}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.10.1.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/mnt/c/Users/<USER>/ghq/github.com/react-icons/react-icons/react-icons-4.10.1.tgz", "_integrity": "sha512-/ngzDP/77tlCfqthiiGNZeYFACw85fUjZtLbedmJ5DTlNDIwETxhwBzdOJ21zj4iJdvc0J3y7yOsX3PpxAJzrw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.10.1_1687412633044_0.01582392030551416", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "react-icons", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.11.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "4b0e31c9bfc919608095cc429c4f1846f4d66c65", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.11.0.tgz", "fileCount": 147, "integrity": "sha512-V+4khzYcE5EBk/BvcuYRq6V/osf11ODUM2J8hg2FDSswRrGvqiYUYPRy4OdrWaQOBj4NcpJfmHZLNaD+VH0TyA==", "signatures": [{"sig": "MEUCIDGrLGOoXkk1NeLCvN65HhfR/u1Gs7d2rt8jyQQQ8B5XAiEA/mp0ui7bgZlkGpABvfmQfT9wTSvvo+gFNod0BuBbvc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79264413}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.11.0.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-4.11.0.tgz", "_integrity": "sha512-V+4khzYcE5EBk/BvcuYRq6V/osf11ODUM2J8hg2FDSswRrGvqiYUYPRy4OdrWaQOBj4NcpJfmHZLNaD+VH0TyA==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.11.0_1694055982383_0.14325636940985076", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "react-icons", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@4.12.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "54806159a966961bfd5cdb26e492f4dafd6a8d78", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-4.12.0.tgz", "fileCount": 147, "integrity": "sha512-IBaDuHiShdZqmfc/TwHu6+d6k2ltNCf3AszxNmjJc1KUfXdEeRJOKyNvLmAHaarhzGmTSVygNdyu8/opXv2gaw==", "signatures": [{"sig": "MEQCIB4r+sD01pXTOG1Wv8TjVYn/46wap0YfiXDW6FwxrLCiAiB+9k6PqB/7nPUdQjfPxPzIn5vt/B5N0XVldTM67wCMkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79933352}, "main": "lib/cjs/index.js", "_from": "file:react-icons-4.12.0.tgz", "types": "lib/esm/index.d.ts", "module": "lib/esm/index.js", "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-4.12.0.tgz", "_integrity": "sha512-IBaDuHiShdZqmfc/TwHu6+d6k2ltNCf3AszxNmjJc1KUfXdEeRJOKyNvLmAHaarhzGmTSVygNdyu8/opXv2gaw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_4.12.0_1699954804974_0.2927474787185458", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "react-icons", "version": "5.0.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.0.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "3a860fac02787984e4c11d9d05bec53226de0dc9", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.0.0.tgz", "fileCount": 143, "integrity": "sha512-8gBBjYRrO14BU9RuMEVptG5aB1gwNPAAgsKOuPggx2hg332pTky/r8FidEJW95cL6IG2mYCEHaYuAxw74wLefw==", "signatures": [{"sig": "MEUCIQDWXaFRJDBisXyD4ekbYqV9uUplBMd2sihAwEzQPzYiPwIgZsSJtciHk+kxp46ONkuDdGjwC5AwxZDKBwH83j3QfJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81781901}, "main": "lib/index.js", "_from": "file:react-icons-5.0.0.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.0.0.tgz", "_integrity": "sha512-8gBBjYRrO14BU9RuMEVptG5aB1gwNPAAgsKOuPggx2hg332pTky/r8FidEJW95cL6IG2mYCEHaYuAxw74wLefw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.0.0_1704866991207_0.29822306186302594", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "react-icons", "version": "5.0.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.0.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "1694e11bfa2a2888cab47dcc30154ce90485feee", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.0.1.tgz", "fileCount": 143, "integrity": "sha512-WqLZJ4bLzlhmsvme6iFdgO8gfZP17rfjYEJ2m9RsZjZ+cc4k1hTzknEz63YS1MeT50kVzoa1Nz36f4BEx+Wigw==", "signatures": [{"sig": "MEUCICHlVr3HVZm/r4e0NGm5oq1hYvTjpiC5e9UhkhSWRDDIAiEAwFxjP+iwMtPw6Svnr73gNB90QJuV4S0Yez/uP1F5P/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81577858}, "main": "lib/index.js", "_from": "file:react-icons-5.0.1.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.0.1.tgz", "_integrity": "sha512-WqLZJ4bLzlhmsvme6iFdgO8gfZP17rfjYEJ2m9RsZjZ+cc4k1hTzknEz63YS1MeT50kVzoa1Nz36f4BEx+Wigw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.0.1_1704957932071_0.08636233388332593", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "react-icons", "version": "5.1.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.1.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "9e7533cc256571a610c2a1ec8a7a143fb1222943", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.1.0.tgz", "fileCount": 143, "integrity": "sha512-D3zug1270S4hbSlIRJ0CUS97QE1yNNKDjzQe3HqY0aefp2CBn9VgzgES27sRR2gOvFK+0CNx/BW0ggOESp6fqQ==", "signatures": [{"sig": "MEQCIBIVYq2yW6+BQuUjEdnYNyUeiJYiz9URR1z1vQ4GbKCAAiA81LmbxqYOyRqManNb4aktl6ETGbiveG9FeKnDGTP22Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82897116}, "main": "lib/index.js", "_from": "file:react-icons-5.1.0.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.1.0.tgz", "_integrity": "sha512-D3zug1270S4hbSlIRJ0CUS97QE1yNNKDjzQe3HqY0aefp2CBn9VgzgES27sRR2gOvFK+0CNx/BW0ggOESp6fqQ==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.1.0_1713152038161_0.47449057004627604", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "react-icons", "version": "5.2.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.2.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "790c92d5f1888ef2dbbd3468c553fd4bb5c8bf7e", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.2.0.tgz", "fileCount": 143, "integrity": "sha512-n52Y7Eb4MgQZHsSZOhSXv1zs2668/hBYKfSRIvKh42yExjyhZu0d1IK2CLLZ3BZB1oo13lDfwx2vOh2z9FTV6Q==", "signatures": [{"sig": "MEUCIQCAJlZPGj4kBig/2u849Jw/Cf9JBMp0+gsyco7Ku49GGgIgT5vHPMz+t4uZV+2/H7xZm7O+LVlVh+HWlJ1aFwQtCw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84322362}, "main": "lib/index.js", "_from": "file:react-icons-5.2.0.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.2.0.tgz", "_integrity": "sha512-n52Y7Eb4MgQZHsSZOhSXv1zs2668/hBYKfSRIvKh42yExjyhZu0d1IK2CLLZ3BZB1oo13lDfwx2vOh2z9FTV6Q==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.2.0_1714532617641_0.7102213331809042", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "react-icons", "version": "5.2.1", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.2.1", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "28c2040917b2a2eda639b0f797bff1888e018e4a", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.2.1.tgz", "fileCount": 143, "integrity": "sha512-zdbW5GstTzXaVKvGSyTaBalt7HSfuK5ovrzlpyiWHAFXndXTdd/1hdDHI4xBM1Mn7YriT6aqESucFl9kEXzrdw==", "signatures": [{"sig": "MEYCIQDG2ZgYThVc1z2RxE2nB1IlNV9cEVFRowrH2a955aoNtQIhAMcd2vCcjLyq4pEP7erDmqU2SG+ovzfCjOgyi6vtjq5h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85043580}, "main": "lib/index.js", "_from": "file:react-icons-5.2.1.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.2.1.tgz", "_integrity": "sha512-zdbW5GstTzXaVKvGSyTaBalt7HSfuK5ovrzlpyiWHAFXndXTdd/1hdDHI4xBM1Mn7YriT6aqESucFl9kEXzrdw==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.2.1_1715043879798_0.03934367176324782", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "react-icons", "version": "5.3.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.3.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "ccad07a30aebd40a89f8cfa7d82e466019203f1c", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.3.0.tgz", "fileCount": 143, "integrity": "sha512-DnUk8aFbTyQPSkCfF8dbX6kQjXA9DktMeJqfjrg6cK9vwQVMxmcA3BfP4QoiztVmEHtwlTgLFsPuH2NskKT6eg==", "signatures": [{"sig": "MEQCIGVcGWKdBbC9PPDgirB+/iWARLRHVsU7cvUzbLwf7YPjAiB43Br/anyYy7ycfC4R7qrXWNnnvieDgPbDsNYb2zG7RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85311776}, "main": "lib/index.js", "_from": "file:react-icons-5.3.0.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.3.0.tgz", "_integrity": "sha512-DnUk8aFbTyQPSkCfF8dbX6kQjXA9DktMeJqfjrg6cK9vwQVMxmcA3BfP4QoiztVmEHtwlTgLFsPuH2NskKT6eg==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.2", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.3.0_1723525883329_0.9395052459686848", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "react-icons", "version": "5.4.0", "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "react-icons@5.4.0", "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "homepage": "https://github.com/react-icons/react-icons#readme", "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "dist": {"shasum": "443000f6e5123ee1b21ea8c0a716f6e7797f7416", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.4.0.tgz", "fileCount": 143, "integrity": "sha512-7eltJxgVt7X64oHh6wSWNwwbKTCtMfK35hcjvJS0yxEAhPM8oUKdS3+kqaW1vicIltw+kR2unHaa12S9pPALoQ==", "signatures": [{"sig": "MEYCIQDvExqwMLRJgxqybGfkJeGwazrTao9fmQno7n9A3kWmVwIhAJhtKX5ztpTytSDZEX3l943KjNTo70rzWpdQ5SNAFj7g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86188055}, "main": "lib/index.js", "_from": "file:react-icons-5.4.0.tgz", "types": "lib/index.d.ts", "module": "lib/index.mjs", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs", "require": "./index.js"}, "./ai": {"types": "./ai/index.d.ts", "import": "./ai/index.mjs", "default": "./ai/index.mjs", "require": "./ai/index.js"}, "./bi": {"types": "./bi/index.d.ts", "import": "./bi/index.mjs", "default": "./bi/index.mjs", "require": "./bi/index.js"}, "./bs": {"types": "./bs/index.d.ts", "import": "./bs/index.mjs", "default": "./bs/index.mjs", "require": "./bs/index.js"}, "./cg": {"types": "./cg/index.d.ts", "import": "./cg/index.mjs", "default": "./cg/index.mjs", "require": "./cg/index.js"}, "./ci": {"types": "./ci/index.d.ts", "import": "./ci/index.mjs", "default": "./ci/index.mjs", "require": "./ci/index.js"}, "./di": {"types": "./di/index.d.ts", "import": "./di/index.mjs", "default": "./di/index.mjs", "require": "./di/index.js"}, "./fa": {"types": "./fa/index.d.ts", "import": "./fa/index.mjs", "default": "./fa/index.mjs", "require": "./fa/index.js"}, "./fc": {"types": "./fc/index.d.ts", "import": "./fc/index.mjs", "default": "./fc/index.mjs", "require": "./fc/index.js"}, "./fi": {"types": "./fi/index.d.ts", "import": "./fi/index.mjs", "default": "./fi/index.mjs", "require": "./fi/index.js"}, "./gi": {"types": "./gi/index.d.ts", "import": "./gi/index.mjs", "default": "./gi/index.mjs", "require": "./gi/index.js"}, "./go": {"types": "./go/index.d.ts", "import": "./go/index.mjs", "default": "./go/index.mjs", "require": "./go/index.js"}, "./gr": {"types": "./gr/index.d.ts", "import": "./gr/index.mjs", "default": "./gr/index.mjs", "require": "./gr/index.js"}, "./hi": {"types": "./hi/index.d.ts", "import": "./hi/index.mjs", "default": "./hi/index.mjs", "require": "./hi/index.js"}, "./im": {"types": "./im/index.d.ts", "import": "./im/index.mjs", "default": "./im/index.mjs", "require": "./im/index.js"}, "./io": {"types": "./io/index.d.ts", "import": "./io/index.mjs", "default": "./io/index.mjs", "require": "./io/index.js"}, "./lu": {"types": "./lu/index.d.ts", "import": "./lu/index.mjs", "default": "./lu/index.mjs", "require": "./lu/index.js"}, "./md": {"types": "./md/index.d.ts", "import": "./md/index.mjs", "default": "./md/index.mjs", "require": "./md/index.js"}, "./pi": {"types": "./pi/index.d.ts", "import": "./pi/index.mjs", "default": "./pi/index.mjs", "require": "./pi/index.js"}, "./ri": {"types": "./ri/index.d.ts", "import": "./ri/index.mjs", "default": "./ri/index.mjs", "require": "./ri/index.js"}, "./rx": {"types": "./rx/index.d.ts", "import": "./rx/index.mjs", "default": "./rx/index.mjs", "require": "./rx/index.js"}, "./si": {"types": "./si/index.d.ts", "import": "./si/index.mjs", "default": "./si/index.mjs", "require": "./si/index.js"}, "./sl": {"types": "./sl/index.d.ts", "import": "./sl/index.mjs", "default": "./sl/index.mjs", "require": "./sl/index.js"}, "./tb": {"types": "./tb/index.d.ts", "import": "./tb/index.mjs", "default": "./tb/index.mjs", "require": "./tb/index.js"}, "./ti": {"types": "./ti/index.d.ts", "import": "./ti/index.mjs", "default": "./ti/index.mjs", "require": "./ti/index.js"}, "./wi": {"types": "./wi/index.d.ts", "import": "./wi/index.mjs", "default": "./wi/index.mjs", "require": "./wi/index.js"}, "./fa6": {"types": "./fa6/index.d.ts", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs", "require": "./fa6/index.js"}, "./hi2": {"types": "./hi2/index.d.ts", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs", "require": "./hi2/index.js"}, "./io5": {"types": "./io5/index.d.ts", "import": "./io5/index.mjs", "default": "./io5/index.mjs", "require": "./io5/index.js"}, "./lia": {"types": "./lia/index.d.ts", "import": "./lia/index.mjs", "default": "./lia/index.mjs", "require": "./lia/index.js"}, "./lib": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "default": "./lib/index.mjs", "require": "./lib/index.js"}, "./tfi": {"types": "./tfi/index.d.ts", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs", "require": "./tfi/index.js"}, "./vsc": {"types": "./vsc/index.d.ts", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs", "require": "./vsc/index.js"}}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.4.0.tgz", "_integrity": "sha512-7eltJxgVt7X64oHh6wSWNwwbKTCtMfK35hcjvJS0yxEAhPM8oUKdS3+kqaW1vicIltw+kR2unHaa12S9pPALoQ==", "repository": {"url": "git+ssh://**************/react-icons/react-icons.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "SVG React icons of popular icon packs using ES6 imports", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "peerDependencies": {"react": "*"}, "_npmOperationalInternal": {"tmp": "tmp/react-icons_5.4.0_1733219338513_0.4248311941896681", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "react-icons", "version": "5.5.0", "description": "SVG React icons of popular icon packs using ES6 imports", "author": {"name": "<PERSON><PERSON>"}, "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "license": "MIT", "main": "lib/index.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "git+ssh://**************/react-icons/react-icons.git"}, "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "homepage": "https://github.com/react-icons/react-icons#readme", "peerDependencies": {"react": "*"}, "exports": {".": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.mjs", "default": "./index.mjs"}, "./lib": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "import": "./lib/index.mjs", "default": "./lib/index.mjs"}, "./ci": {"types": "./ci/index.d.ts", "require": "./ci/index.js", "import": "./ci/index.mjs", "default": "./ci/index.mjs"}, "./fa": {"types": "./fa/index.d.ts", "require": "./fa/index.js", "import": "./fa/index.mjs", "default": "./fa/index.mjs"}, "./fa6": {"types": "./fa6/index.d.ts", "require": "./fa6/index.js", "import": "./fa6/index.mjs", "default": "./fa6/index.mjs"}, "./io": {"types": "./io/index.d.ts", "require": "./io/index.js", "import": "./io/index.mjs", "default": "./io/index.mjs"}, "./io5": {"types": "./io5/index.d.ts", "require": "./io5/index.js", "import": "./io5/index.mjs", "default": "./io5/index.mjs"}, "./md": {"types": "./md/index.d.ts", "require": "./md/index.js", "import": "./md/index.mjs", "default": "./md/index.mjs"}, "./ti": {"types": "./ti/index.d.ts", "require": "./ti/index.js", "import": "./ti/index.mjs", "default": "./ti/index.mjs"}, "./go": {"types": "./go/index.d.ts", "require": "./go/index.js", "import": "./go/index.mjs", "default": "./go/index.mjs"}, "./fi": {"types": "./fi/index.d.ts", "require": "./fi/index.js", "import": "./fi/index.mjs", "default": "./fi/index.mjs"}, "./lu": {"types": "./lu/index.d.ts", "require": "./lu/index.js", "import": "./lu/index.mjs", "default": "./lu/index.mjs"}, "./gi": {"types": "./gi/index.d.ts", "require": "./gi/index.js", "import": "./gi/index.mjs", "default": "./gi/index.mjs"}, "./wi": {"types": "./wi/index.d.ts", "require": "./wi/index.js", "import": "./wi/index.mjs", "default": "./wi/index.mjs"}, "./di": {"types": "./di/index.d.ts", "require": "./di/index.js", "import": "./di/index.mjs", "default": "./di/index.mjs"}, "./ai": {"types": "./ai/index.d.ts", "require": "./ai/index.js", "import": "./ai/index.mjs", "default": "./ai/index.mjs"}, "./bs": {"types": "./bs/index.d.ts", "require": "./bs/index.js", "import": "./bs/index.mjs", "default": "./bs/index.mjs"}, "./ri": {"types": "./ri/index.d.ts", "require": "./ri/index.js", "import": "./ri/index.mjs", "default": "./ri/index.mjs"}, "./fc": {"types": "./fc/index.d.ts", "require": "./fc/index.js", "import": "./fc/index.mjs", "default": "./fc/index.mjs"}, "./gr": {"types": "./gr/index.d.ts", "require": "./gr/index.js", "import": "./gr/index.mjs", "default": "./gr/index.mjs"}, "./hi": {"types": "./hi/index.d.ts", "require": "./hi/index.js", "import": "./hi/index.mjs", "default": "./hi/index.mjs"}, "./hi2": {"types": "./hi2/index.d.ts", "require": "./hi2/index.js", "import": "./hi2/index.mjs", "default": "./hi2/index.mjs"}, "./si": {"types": "./si/index.d.ts", "require": "./si/index.js", "import": "./si/index.mjs", "default": "./si/index.mjs"}, "./sl": {"types": "./sl/index.d.ts", "require": "./sl/index.js", "import": "./sl/index.mjs", "default": "./sl/index.mjs"}, "./im": {"types": "./im/index.d.ts", "require": "./im/index.js", "import": "./im/index.mjs", "default": "./im/index.mjs"}, "./bi": {"types": "./bi/index.d.ts", "require": "./bi/index.js", "import": "./bi/index.mjs", "default": "./bi/index.mjs"}, "./cg": {"types": "./cg/index.d.ts", "require": "./cg/index.js", "import": "./cg/index.mjs", "default": "./cg/index.mjs"}, "./vsc": {"types": "./vsc/index.d.ts", "require": "./vsc/index.js", "import": "./vsc/index.mjs", "default": "./vsc/index.mjs"}, "./tb": {"types": "./tb/index.d.ts", "require": "./tb/index.js", "import": "./tb/index.mjs", "default": "./tb/index.mjs"}, "./tfi": {"types": "./tfi/index.d.ts", "require": "./tfi/index.js", "import": "./tfi/index.mjs", "default": "./tfi/index.mjs"}, "./rx": {"types": "./rx/index.d.ts", "require": "./rx/index.js", "import": "./rx/index.mjs", "default": "./rx/index.mjs"}, "./pi": {"types": "./pi/index.d.ts", "require": "./pi/index.js", "import": "./pi/index.mjs", "default": "./pi/index.mjs"}, "./lia": {"types": "./lia/index.d.ts", "require": "./lia/index.js", "import": "./lia/index.mjs", "default": "./lia/index.mjs"}}, "_id": "react-icons@5.5.0", "_integrity": "sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==", "_resolved": "/home/<USER>/ghq/github.com/react-icons/react-icons/react-icons-5.5.0.tgz", "_from": "file:react-icons-5.5.0.tgz", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==", "shasum": "8aa25d3543ff84231685d3331164c00299cdfaf2", "tarball": "https://registry.npmjs.org/react-icons/-/react-icons-5.5.0.tgz", "fileCount": 143, "unpackedSize": 86188059, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDqqLuzXl1njj2aVLQUSX5MrXE4Hx8ap6+3wD7G+IVXOwIhAJRn/E8XxyQ7mVj2gaWmH8fgTEWySiiz+/8C6MyYWfyk"}]}, "_npmUser": {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/react-icons_5.5.0_1739935227850_0.3812798137177271"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-10-23T20:55:28.496Z", "modified": "2025-02-19T03:20:28.443Z", "0.0.1": "2015-10-23T20:55:28.496Z", "0.0.2": "2015-10-24T16:21:38.532Z", "0.0.3": "2015-10-24T16:33:39.840Z", "0.0.4": "2015-10-28T01:48:19.083Z", "0.1.0": "2015-10-28T02:11:00.360Z", "0.1.2": "2015-10-29T09:10:37.326Z", "0.1.3": "2015-10-31T15:08:38.987Z", "0.1.4": "2015-10-31T15:22:06.541Z", "0.1.5": "2015-11-28T10:00:44.210Z", "0.2.0": "2015-11-28T10:57:55.550Z", "0.2.1": "2015-12-02T12:41:23.673Z", "0.2.2": "2015-12-03T22:04:40.072Z", "0.3.0": "2015-12-03T22:11:04.353Z", "0.3.1": "2015-12-03T22:27:02.720Z", "1.0.0": "2015-12-03T23:19:06.010Z", "1.0.1": "2015-12-03T23:47:00.046Z", "1.0.2": "2015-12-04T10:35:53.958Z", "1.0.3": "2015-12-04T11:49:27.716Z", "1.0.5": "2016-04-12T23:13:05.045Z", "1.0.6": "2016-04-13T16:01:37.953Z", "2.0.0": "2016-04-14T14:52:26.161Z", "2.0.1": "2016-04-18T07:56:46.682Z", "2.1.0": "2016-05-25T13:34:21.682Z", "2.2.0": "2016-07-06T10:59:57.895Z", "2.2.1": "2016-07-10T15:41:11.786Z", "2.2.2": "2016-12-19T23:22:52.376Z", "2.2.3": "2016-12-29T17:02:40.692Z", "2.2.4": "2017-05-04T23:04:29.514Z", "2.2.5": "2017-05-07T09:58:46.532Z", "2.2.6": "2017-10-05T22:37:07.283Z", "2.2.7": "2017-10-05T22:38:16.867Z", "3.0.0-beta.1": "2018-07-19T10:57:56.498Z", "3.0.0-beta.2": "2018-07-27T01:38:11.662Z", "3.0.0-beta.3": "2018-07-27T03:11:10.017Z", "3.0.0-beta.4": "2018-07-27T05:14:37.678Z", "3.0.0-beta.5": "2018-07-27T09:30:54.145Z", "3.0.0": "2018-07-30T03:40:14.097Z", "3.0.1": "2018-07-30T03:58:24.337Z", "3.0.2": "2018-08-02T02:06:02.574Z", "3.0.3": "2018-08-02T13:13:58.981Z", "3.0.4": "2018-08-02T13:49:46.806Z", "3.0.5": "2018-08-06T05:31:38.750Z", "3.1.0-beta.1": "2018-09-07T08:48:08.562Z", "3.1.0": "2018-09-19T05:31:00.431Z", "3.2.0": "2018-10-12T01:30:08.199Z", "3.2.1": "2018-10-16T00:53:39.665Z", "3.2.2": "2018-10-24T06:37:40.705Z", "3.3.0": "2019-01-10T04:02:11.444Z", "3.4.0": "2019-02-12T01:57:58.146Z", "3.5.0": "2019-03-06T03:49:39.022Z", "3.6.0-beta1": "2019-03-06T03:51:17.930Z", "3.6.0-beta2": "2019-03-08T03:37:19.159Z", "3.6.0": "2019-04-16T07:38:53.155Z", "3.6.1": "2019-04-16T09:44:53.024Z", "3.7.0": "2019-05-10T06:04:01.948Z", "3.8.0": "2019-10-25T09:49:22.970Z", "3.9.0": "2020-01-30T06:35:56.007Z", "3.10.0": "2020-04-21T06:10:49.636Z", "3.11.0": "2020-08-19T08:50:12.806Z", "4.0.0": "2020-11-26T04:11:21.509Z", "4.1.0": "2020-11-26T08:57:14.684Z", "4.2.0": "2021-02-07T04:05:56.376Z", "4.3.0": "2021-10-02T16:55:03.519Z", "4.3.1": "2021-10-03T14:01:19.558Z", "4.4.0": "2022-05-30T22:09:52.591Z", "4.6.0": "2022-10-14T14:16:03.652Z", "4.7.1": "2022-11-29T01:19:15.793Z", "4.8.0": "2023-03-03T06:02:07.544Z", "4.8.1-snapshot.0": "2023-03-03T06:37:49.022Z", "4.8.1-snapshot.1": "2023-03-06T10:30:24.925Z", "4.8.1-snapshot.2": "2023-03-28T06:05:32.188Z", "4.9.0": "2023-05-28T14:32:49.882Z", "4.10.0": "2023-06-22T04:23:25.387Z", "4.10.1": "2023-06-22T05:43:53.693Z", "4.11.0": "2023-09-07T03:06:22.834Z", "4.12.0": "2023-11-14T09:40:05.531Z", "5.0.0": "2024-01-10T06:09:51.620Z", "5.0.1": "2024-01-11T07:25:32.481Z", "5.1.0": "2024-04-15T03:33:58.648Z", "5.2.0": "2024-05-01T03:03:38.096Z", "5.2.1": "2024-05-07T01:04:40.208Z", "5.3.0": "2024-08-13T05:11:23.817Z", "5.4.0": "2024-12-03T09:48:59.098Z", "5.5.0": "2025-02-19T03:20:28.280Z"}, "bugs": {"url": "https://github.com/react-icons/react-icons/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/react-icons/react-icons#readme", "repository": {"type": "git", "url": "git+ssh://**************/react-icons/react-icons.git"}, "description": "SVG React icons of popular icon packs using ES6 imports", "contributors": [{"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}], "maintainers": [{"name": "nwwells", "email": "<EMAIL>"}, {"name": "tusbar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kami<PERSON>_fanta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<img src=\"https://raw.githubusercontent.com/react-icons/react-icons/master/react-icons.svg\" width=\"120\" alt=\"React Icons\">\n\n# [React Icons](https://react-icons.github.io/react-icons)\n\n[![npm][npm-image]][npm-url]\n\n[npm-image]: https://img.shields.io/npm/v/react-icons.svg?style=flat-square\n[npm-url]: https://www.npmjs.com/package/react-icons\n\nInclude popular icons in your React projects easily with `react-icons`, which utilizes ES6 imports that allows you to include only the icons that your project is using.\n\n## Installation (for standard modern project)\n\n```bash\nyarn add react-icons\n# or\nnpm install react-icons --save\n```\n\nexample usage\n\n```jsx\nimport { FaBeer } from \"react-icons/fa\";\n\nfunction Question() {\n  return (\n    <h3>\n      Lets go for a <FaBeer />?\n    </h3>\n  );\n}\n```\n\n[View the documentation](https://react-icons.github.io/react-icons) for further usage examples and how to use icons from other packages. _NOTE_: each Icon package has it's own subfolder under `react-icons` you import from.\n\nFor example, to use an icon from **Material Design**, your import would be: `import { ICON_NAME } from 'react-icons/md';`\n\n## Installation (for meteorjs, gatsbyjs, etc)\n\n> **Note**\n> This option has not had a new release for some time.\n> More info https://github.com/react-icons/react-icons/issues/593\n\nIf your project grows in size, this option is available.\nThis method has the trade-off that it takes a long time to install the package.\n\n```bash\nyarn add @react-icons/all-files\n# or\nnpm install @react-icons/all-files --save\n```\n\nexample usage\n\n```jsx\nimport { FaBeer } from \"@react-icons/all-files/fa/FaBeer\";\n\nfunction Question() {\n  return (\n    <h3>\n      Lets go for a <FaBeer />?\n    </h3>\n  );\n}\n```\n\n## Icons\n\n| Icon Library                                                            | License                                                                                           | Version                                  | Count |\n| ----------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- | ---------------------------------------- | ----: |\n| [Circum Icons](https://circumicons.com/)                                | [MPL-2.0 license](https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE)                 | 1.0.0                                    |   288 |\n| [Font Awesome 5](https://fontawesome.com/)                              | [CC BY 4.0 License](https://creativecommons.org/licenses/by/4.0/)                                 | 5.15.4-3-gafecf2a                        |  1612 |\n| [Font Awesome 6](https://fontawesome.com/)                              | [CC BY 4.0 License](https://creativecommons.org/licenses/by/4.0/)                                 | 6.5.2                                    |  2045 |\n| [Ionicons 4](https://ionicons.com/)                                     | [MIT](https://github.com/ionic-team/ionicons/blob/master/LICENSE)                                 | 4.6.3                                    |   696 |\n| [Ionicons 5](https://ionicons.com/)                                     | [MIT](https://github.com/ionic-team/ionicons/blob/master/LICENSE)                                 | 5.5.4                                    |  1332 |\n| [Material Design icons](http://google.github.io/material-design-icons/) | [Apache License Version 2.0](https://github.com/google/material-design-icons/blob/master/LICENSE) | 4.0.0-98-g9beae745bb                     |  4341 |\n| [Typicons](http://s-ings.com/typicons/)                                 | [CC BY-SA 3.0](https://creativecommons.org/licenses/by-sa/3.0/)                                   | 2.1.2                                    |   336 |\n| [Github Octicons icons](https://octicons.github.com/)                   | [MIT](https://github.com/primer/octicons/blob/master/LICENSE)                                     | 18.3.0                                   |   264 |\n| [Feather](https://feathericons.com/)                                    | [MIT](https://github.com/feathericons/feather/blob/master/LICENSE)                                | 4.29.1                                   |   287 |\n| [Lucide](https://lucide.dev/)                                           | [ISC](https://github.com/lucide-icons/lucide/blob/main/LICENSE)                                   | v5.1.0-6-g438f572e                       |  1215 |\n| [Game Icons](https://game-icons.net/)                                   | [CC BY 3.0](https://creativecommons.org/licenses/by/3.0/)                                         | 12920d6565588f0512542a3cb0cdfd36a497f910 |  4040 |\n| [Weather Icons](https://erikflowers.github.io/weather-icons/)           | [SIL OFL 1.1](http://scripts.sil.org/OFL)                                                         | 2.0.12                                   |   219 |\n| [Devicons](https://vorillaz.github.io/devicons/)                        | [MIT](https://opensource.org/licenses/MIT)                                                        | 1.8.0                                    |   192 |\n| [Ant Design Icons](https://github.com/ant-design/ant-design-icons)      | [MIT](https://opensource.org/licenses/MIT)                                                        | 4.4.2                                    |   831 |\n| [Bootstrap Icons](https://github.com/twbs/icons)                        | [MIT](https://opensource.org/licenses/MIT)                                                        | 1.11.3                                   |  2716 |\n| [Remix Icon](https://github.com/Remix-Design/RemixIcon)                 | [Apache License Version 2.0](http://www.apache.org/licenses/)                                     | 4.2.0                                    |  2860 |\n| [Flat Color Icons](https://github.com/icons8/flat-color-icons)          | [MIT](https://opensource.org/licenses/MIT)                                                        | 1.0.2                                    |   329 |\n| [Grommet-Icons](https://github.com/grommet/grommet-icons)               | [Apache License Version 2.0](http://www.apache.org/licenses/)                                     | 4.12.1                                   |   635 |\n| [Heroicons](https://github.com/tailwindlabs/heroicons)                  | [MIT](https://opensource.org/licenses/MIT)                                                        | 1.0.6                                    |   460 |\n| [Heroicons 2](https://github.com/tailwindlabs/heroicons)                | [MIT](https://opensource.org/licenses/MIT)                                                        | 2.1.3                                    |   888 |\n| [Simple Icons](https://simpleicons.org/)                                | [CC0 1.0 Universal](https://creativecommons.org/publicdomain/zero/1.0/)                           | 12.14.0                                  |  3209 |\n| [Simple Line Icons](https://thesabbir.github.io/simple-line-icons/)     | [MIT](https://opensource.org/licenses/MIT)                                                        | 2.5.5                                    |   189 |\n| [IcoMoon Free](https://github.com/Keyamoon/IcoMoon-Free)                | [CC BY 4.0 License](https://github.com/Keyamoon/IcoMoon-Free/blob/master/License.txt)             | d006795ede82361e1bac1ee76f215cf1dc51e4ca |   491 |\n| [BoxIcons](https://github.com/atisawd/boxicons)                         | [MIT](https://github.com/atisawd/boxicons/blob/master/LICENSE)                                    | 2.1.4                                    |  1634 |\n| [css.gg](https://github.com/astrit/css.gg)                              | [MIT](https://opensource.org/licenses/MIT)                                                        | 2.1.1                                    |   704 |\n| [VS Code Icons](https://github.com/microsoft/vscode-codicons)           | [CC BY 4.0](https://creativecommons.org/licenses/by/4.0/)                                         | 0.0.35                                   |   461 |\n| [Tabler Icons](https://github.com/tabler/tabler-icons)                  | [MIT](https://opensource.org/licenses/MIT)                                                        | 3.2.0                                    |  5237 |\n| [Themify Icons](https://github.com/lykmapipo/themify-icons)             | [MIT](https://github.com/thecreation/standard-icons/blob/master/modules/themify-icons/LICENSE)    | v0.1.2-2-g9600186                        |   352 |\n| [Radix Icons](https://icons.radix-ui.com)                               | [MIT](https://github.com/radix-ui/icons/blob/master/LICENSE)                                      | @radix-ui/react-icons@1.3.0-1-g94b3fcf   |   318 |\n| [Phosphor Icons](https://github.com/phosphor-icons/core)                | [MIT](https://github.com/phosphor-icons/core/blob/main/LICENSE)                                   | 2.1.1                                    |  9072 |\n| [Icons8 Line Awesome](https://icons8.com/line-awesome)                  | [MIT](https://github.com/icons8/line-awesome/blob/master/LICENSE.md)                              | 1.3.1                                    |  1544 |\n\nYou can add more icons by submitting pull requests or creating issues.\n\n## Configuration\n\nYou can configure react-icons props using [React Context API](https://reactjs.org/docs/context.html).\n\n_Requires **React 16.3** or higher._\n\n```jsx\nimport { IconContext } from \"react-icons\";\n\n<IconContext.Provider value={{ color: \"blue\", className: \"global-class-name\" }}>\n  <div>\n    <FaFolder />\n  </div>\n</IconContext.Provider>;\n```\n\n| Key         | Default               | Notes                              |\n| ----------- | --------------------- | ---------------------------------- |\n| `color`     | `undefined` (inherit) |                                    |\n| `size`      | `1em`                 |                                    |\n| `className` | `undefined`           |                                    |\n| `style`     | `undefined`           | Can overwrite size and color       |\n| `attr`      | `undefined`           | Overwritten by other attributes    |\n| `title`     | `undefined`           | Icon description for accessibility |\n\n## Migrating from version 2 -> 3\n\n### Change import style\n\nImport path has changed. You need to rewrite from the old style.\n\n```jsx\n// OLD IMPORT STYLE\nimport FaBeer from \"react-icons/lib/fa/beer\";\n\nfunction Question() {\n  return (\n    <h3>\n      Lets go for a <FaBeer />?\n    </h3>\n  );\n}\n```\n\n```jsx\n// NEW IMPORT STYLE\nimport { FaBeer } from \"react-icons/fa\";\n\nfunction Question() {\n  return (\n    <h3>\n      Lets go for a <FaBeer />?\n    </h3>\n  );\n}\n```\n\nEnding up with a large JS bundle? Check out [this issue](https://github.com/react-icons/react-icons/issues/154).\n\n### Adjustment CSS\n\nFrom version 3, `vertical-align: middle` is not automatically given. Please use IconContext to specify className or specify an inline style.\n\n#### Global Inline Styling\n\n```tsx\n<IconContext.Provider value={{ style: { verticalAlign: 'middle' } }}>\n```\n\n#### Global `className` Styling\n\nComponent\n\n```tsx\n<IconContext.Provider value={{ className: 'react-icons' }}>\n```\n\nCSS\n\n```css\n.react-icons {\n  vertical-align: middle;\n}\n```\n\n### TypeScript native support\n\nDependencies on `@types/react-icons` can be deleted.\n\n#### Yarn\n\n```bash\nyarn remove @types/react-icons\n```\n\n#### NPM\n\n```bash\nnpm remove @types/react-icons\n```\n\n## Contributing\n\n`./build-script.sh` will build the whole project. See also CI scripts for more information.\n\n### Development\n\n```bash\nyarn\ncd packages/react-icons\nyarn fetch  # fetch icon sources\nyarn build\n```\n\n### Add/Update icon set\n\nFirst, check the discussion to see if anyone would like to add an icon set.\n\nhttps://github.com/react-icons/react-icons/discussions/categories/new-icon-set\n\nThe SVG files to be fetched are managed in this file. Edit this file and run `yarn fetch && yarn check && yarn build`.\n\nhttps://github.com/react-icons/react-icons/blob/master/packages/react-icons/src/icons/index.ts\n\n### Preview\n\n> **Note**\n> The project is not actively accepting PR for the preview site at this time.\n\nThe preview site is the [`react-icons`](https://react-icons.github.io/react-icons/) website, built in Astro+React.\n\n```bash\ncd packages/react-icons\nyarn fetch\nyarn build\n\ncd ../preview-astro\nyarn start\n```\n\n### Demo\n\nThe demo is a [Create React App](https://create-react-app.dev/) boilerplate with `react-icons` added as a dependency for easy testing.\n\n```bash\ncd packages/react-icons\nyarn fetch\nyarn build\n\ncd ../demo\nyarn start\n```\n\n## Why React SVG components instead of fonts?\n\nSVG is [supported by all major browsers](http://caniuse.com/#search=svg). With `react-icons`, you can serve only the needed icons instead of one big font file to the users, helping you to recognize which icons are used in your project.\n\n## Related Projects\n\n- [react-svg-morph](https://github.com/gorangajic/react-svg-morph/)\n\n## Licence\n\nMIT\n\n- Icons are taken from the other projects so please check each project licences accordingly.\n", "readmeFilename": "README.md", "users": {"kvrao": true, "zorak": true, "zzzze": true, "aaaman": true, "gcasat": true, "hssrrw": true, "lestad": true, "pedall": true, "adrtho4": true, "bashkos": true, "bart1208": true, "bpolonia": true, "hugovila": true, "stefanof": true, "tracker1": true, "abuelwafa": true, "ireneshih": true, "sternelee": true, "anujaks995": true, "cfleschhut": true, "incendiary": true, "mauricioml": true, "ryanlittle": true, "flumpus-dev": true, "marloncouto": true, "ayoola_moore": true, "gustavohenke": true, "gamersdelight": true, "arcticicestudio": true, "sebastianjacobs": true}}