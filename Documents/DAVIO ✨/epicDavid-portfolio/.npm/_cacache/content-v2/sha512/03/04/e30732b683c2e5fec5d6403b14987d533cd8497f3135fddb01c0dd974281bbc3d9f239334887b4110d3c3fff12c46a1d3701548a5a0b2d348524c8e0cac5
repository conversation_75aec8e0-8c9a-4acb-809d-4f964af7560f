{"_id": "@babel/plugin-transform-unicode-escapes", "_rev": "49-7e24310de2cf1481298a0546369d0312", "name": "@babel/plugin-transform-unicode-escapes", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.10.0": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.10.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "63b4da633af14740b6570b928a2d5537495314cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-6DwSPQzJ9kSRI1kNFfVAeYdeH7sUH0c1NOYSBGnpJ1ZUZ7mxPY1hxeAqzcrO5NKlOx7ghcy4nAbfFWTPx5IVEg==", "signatures": [{"sig": "MEYCIQCSEh4oNFhEdlMrdIhhAviWi9HmNYGTj10OHFqE9wkfVQIhAJjSeK/axl6Az6zUHf64W/4Wh2OpFvltGcLQml0I8fXs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY17CRA9TVsSAnZWagAA45AP/A+uAw8kfgJF2Q+q9/pb\nouWsVnlfKFvqHKpV/SRo0jkjneMB68BUcssAmzcPC1ObSJImXZx5ntabCU5b\nRBrcikfuQHgqc69fSvMXzuoLCRKDVo7W/BTzpJ8D88MBsG+dZ8om2F0XjhZc\n/ZjgYIRhi6KdnZup/aUXgFPnPgrsI4rNbC3FO5+EvLz6NAS//XFZBIz3SHyE\nbqDxPTZL39xkvN7Z/cUWCAe8V/SRftrN4j/MjXzCxwVrwtzPf68HLnfh3mMF\nYhlhCzVqPM43Ea7tqRmxmN4yPl8lf/CV0Un4DXmjs/X6+sHRWUEaHIlN0y5M\nVruTpwWYhOUpUVGo+S6DjfWYjWUOUpJ/j89hVcev4G+zruEqI7Nh9HOzWr3u\nPpjrWO3g+baDg5OJtm2bEuR+Kb+IyfvhFuUGZyrdStlo05jRxjzpH+MKQgvQ\n3HxYc554OG0GsHMwZ8Yi3+N/w2bKGU31BKLymruXnf4JKkN27J3dIVQFtOsZ\npzTViw4W+jbm3c4LhfgtBk4lTDoTvlFcWWXd6guqykZqcEot/e3y8ualPxiT\n9FoHQAV9Q3OmWHIbBKrGCbQmmnH9SmlSR4e05R5SnBOH7LYb6Wa6LIPzkDC2\nH9BNKaO3cORo4IfqpzwzEUfST3X/jAbBpZukZs6FbpkZH0FxE9xuMtabhZHB\n1P9E\r\n=xgAl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-unicode-escapes", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.10.0_1590529402565_0.4755354245786483", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "add0f8483dab60570d9e03cecef6c023aa8c9940", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-zZ0Poh/yy1d4jeDWpx/mNwbKJVwUYJX73q+gyh4bwtG0/iUlzdEu0sLMda8yuDFS6LBQlT/ST1SJAR6zYwXWgw==", "signatures": [{"sig": "MEUCIQDJx+zW+VnTfamDWKj3/yxPfPr12G6wBZs+gQGaPEJOLQIgVtVRbIjBlCkf6ru55C7PVZMVD5l8YDQcLwQLaqqexXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSjCRA9TVsSAnZWagAAVbcQAIiZ5iI9Ph62I3u/Ia4Z\n0AIyAlY0iX0P6gI4zduZ22jXRrXRxmhlrIGr4yT5qq1IxJuQ778sjaP91KTo\nAdNicy2lP9NinNbOeUI/MkIrqKD1DtlIJI+26GzHt0aAiHPt5kmukWA1R9n2\nR8CT/ZQHRyrikxChDwOuGrol0SQSB1nQmvW5jQbmVqDt7TziebhOF6TssJYq\n/Ut5RYZfTVGWwJ7RqInaEf5zXy4yZLYNb8Hjx16TGDXY7vOz3Gn4MQjdn97o\n5z7wXYUmC06KHVan10IwHJHBCflzB/JzqJ2AtLgFPU8RCI9wvBXyNO1KG3XV\n8KH7ZjSBXyqy8g62Ym6357RHoDh2O0eDnxTDNkt+7XzOKXMdpqc9lOtqQbCy\niw9T0oxLbhR6wWtGBL1eWI0zA8MIePmJmQrbzqNXSOZgyb8gJmgRx31SE5/L\n1l5DNHXp/4j5CkxnaDS5Lb34NusqYwxo4Bbo3Z46d0U45ztgUBH+k+aT3O0R\nw8GFXDUJpvshx/gGd2XVi+8YkyTLG+8vR6PrXwvHV7+BCwYy+JxYG+eXo2AR\n9Njxxya8fm8GpfxiSdE3OOJVSBR8SFYUsrZE6T7LEZsc1Cc8P+3TxoA5Ncc9\n7Tn7IEpSrqIQk3o0E96ho90jqV/CtJZoaGBmy0xobT5dOXKH1rW5c0ubNOOn\nJAGv\r\n=FjLm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.10.1_1590617251005_0.4719933718596043", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "feae523391c7651ddac115dae0a9d06857892007", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-y5XJ9waMti2J+e7ij20e+aH+fho7Wb7W8rNuu72aKRwCHFqQdhkdU2lo3uZ9tQuboEJcUFayXdARhcxLQ3+6Fg==", "signatures": [{"sig": "MEYCIQDcqg89lGfbkw4/QV4Cl2najTpD4IpIcdIfShKuqNj8awIhAMbf/wEmXA3DNLhYkx6Z4zHSQ+txJEwV/kIP4iuELmJ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zouCRA9TVsSAnZWagAAOpYP/2nuB9/4enjFrDdUh/MO\nV/wobq80wAh5+PUdJvYfoHYxQJ9YCk3PMoE0eQ8E+0og71Ya1EqkAFuPwgbH\nVL0iuGIjiGC1W/4Fd0Xx4dlzTK9No73eBrxlJQlZWcwX0+fNfRBY+YuV0T9T\nPUyPbe+xHMsJOTgOA1gwTYUOpMWB+cBIXfjjELsv58v8Y/YZn6ek59lqqo//\neFLCu4+yOGjV5JOhTn1KkVI1slLMWc4ms9NPrrAKXN3nr1cgD4rlMs7ANV3H\nhMysNGdJF8KRknMMAWefp80I+FPlcU2fBTd6MTu8cYdOcuBUDuFAP6tLjgYX\nInoFL2fnVQ8wyabVcwEjHiOb11iy9YC7SMQWqCiPy6I3EvsZrFVAGYtzCBWE\nYRnispFGWMHYRQbo0MAKzcDh4tNE+sIgu3ZYQWttQ3uBeR9LU6WCcWgtSvpl\nf3nATyGUsEqS8ttpQmcAbx3XfItfMEllGCbS8mS+n9YmWa3fllHuDDvr7hPE\n0aiRW9Yu0Fy+QpEOQqp1ucaSNpR3bgPpQvxcSKJTyBeHq+P3QRLTBbyQAa2I\nz6UYn1xh6CIHqZFKca3fqFwSbV1sKiplHzUNYcgq3EPRk/+KtfEw998rsggG\n4Hinb2EXGoRu8AK26WU8utzrIjt3bFA7DCkjUnELPuEcWGglIkKCyhAkAxQ3\nJNnA\r\n=lEeG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.10.4_1593522734303_0.1788316868783113", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "5232b9f81ccb07070b7c3c36c67a1b78f1845709", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-I8gNHJLIc7GdApm7wkVnStWssPNbSRMPtgHdmH3sRM1zopz09UWPS4x5V4n1yz/MIWTVnJ9sp6IkuXdWM4w+2Q==", "signatures": [{"sig": "MEQCIDWGFf3HX05qmHH5AYqf4jsczwa1r2dUXgkARgZipMwUAiAuOriej6yy0ICvh+hCfwC2KpDlDrfr3/0V7YixPbrVQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/iCRA9TVsSAnZWagAAUg8P/2UXPqa33f7Xmzzs7yMO\n+LcQkEoLJk8BpO1RSsHmfvumQ5/3X1oMO0vN+KTkZdd4ygAF63QdDa3u5GRV\nTPSEQMHb+SZ58TAgEr0RH1/oNL00DdPwd7ctvgKBhNHn2PCQpo/DqjeO7uTv\nYwz3Oh60FQFGXzl0x9um/Nt4Ktf2Vwg4RZbrnE0pTEYOAKIBCaSHWolqcfHI\nyhLfREPDtjEwwIe8X611JsFuX5hXQcrT5hdC2pohV+/YpejwgiNeaxA6szGb\nHtqBEVDbL6ytPFu4fCOPYta3NSNA6lvhC0PnVGY7gUHID5PkxlOn8RAG8gZi\nLH6TFRlYfwwyHMyySeYBKZ9ns1t2Wj6i1+xnCyIy60QyMiFr+jBTjxoEgd++\n6WJO1pDLDrK/y5R81tQ8QVV7dtN+fbQyrIvTlHdEdlaqPJcVQV6K3HbxW4N1\nbk4wErxkgC/8PlbtI2czZtrp35smn3+tCHaeGnAQutJq3sVaZhXYNeOwSFyU\nIQKeK2WwctXquum4KUnYJK6yvO0yoE1zu+FlUovJecTjFiNXcvL0UzprB3mx\ndJJlIA8S1pdE75C+3bPhUGP6kHSowoMBdbDipkY14IsBxvnWypsf00HERyl/\nBKsRKIwD4aBtRX+oyRCWYHvk1Iw1AmF4SZoLeA9jN4CWms4K21YFoXoyDIdi\nMGYH\r\n=qzr5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.12.1_1602801634373_0.48724703294953353", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "840ced3b816d3b5127dd1d12dcedc5dead1a5e74", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-0bHEkdwJ/sN/ikBHfSmOXPypN/beiGqjo+o4/5K+vxEFNPRPdImhviPakMKG4x96l85emoa0Z6cDflsdBusZbw==", "signatures": [{"sig": "MEQCIGkmyQVJL4DlOKnaaR5zc6Gh+PyA9Ff7QjXe2EIpNQMKAiBLbxQIHcv25XBskmM4eMOo5acdj3F6jPAns1eYhKMVsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgxCRA9TVsSAnZWagAA6iAP/R1PFnUbw3Ki8YlFYDBS\nfMnjMfCpfxOFPHG0aghBNOtgjwtwp9FmmWN7WyEshThBNTAnTTRmp/Yz8P2L\nUkknpfDrJjaeJ2qSWmNKuS0vB+xhqkn6NfH4N70JnL/2Eo1EHs+d4X+xeFDW\nbn4LdtjfjY5B0MSCnt1EdDz9yrRD3Gogh3ShbZg6xnDe+rs+J09TTVHcwruZ\nFc4C89r2cASNQ1IcK8QmX7+bUHEBjwkS1PR+0rXz2HrIZwxID0TPcotykVxN\n3TEdzP4uHelin08Oc5rUVKL7bLKbQ2UXzG9ywOR9hcyB/NdCXnC5DbC/fHz4\n+4FjjkbDmFAFuR7knYxGP1xHCCEmDyrS9eO82qBqvJh1kWM97sDHaxsAwGuo\nGnb4Zx8EW3V294CJXQ3ZnxyAtxvMSUWelv1vvdAtwlO5bvd1u6kEGh7Sg2OD\nWMrmssa9Efc5Egg89aKVTxdR65Wm2tDrgaTxBcEknxpszIyrFUbKGHFw10K9\nAJ9WLvK+AGsTemkz189J1Mt38HE4CGD8Z1+HGNR6ehdkTTKHstKQE6UoOPp0\naquEcvI6iZVcuuRdPAzWmNQiD0zLVMlFFbQDSsi5JFlHiv73PfO/PC2XINlH\n3t7A2set3tqampv5+wrrBI6gC1PfCpV6ZTS82g1ThPp224gJ0vYDpGZGnNNV\noOsp\r\n=r8VL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.12.13_1612314673502_0.9798698184789609", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "9d4bd2a681e3c5d7acf4f57fa9e51175d91d0c6b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-crTo4jATEOjxj7bt9lbYXcBAM3LZaUrbP2uUdxb6WIorLmjNKSpHfIybgY4B8SRpbf8tEVIWH3Vtm7ayCrKocA==", "signatures": [{"sig": "MEYCIQCkJdcxEMPwS9EQMFO8xzb2bXWiZFPs5s91NmIJVYJh7gIhAMLrV17jBEeLL9H/YhHdiON6hq1ORkFCDeN8IIo4KLUE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrSCRA9TVsSAnZWagAA0NUP/0AvBd+f4YZDG6VblT5U\n7UzOPw5v/Vs0XUfLWi7+4vZEMvygvsey0f7/azqOrQ8nI6H6A7FnEzACSLIu\n6ARHpIroX2xJpf7cx3dDLZ4CT8Xb9YanI13HqayLtYx+DDHVV1FfSKS8uK9R\n/VlUfPL7XCn6ZD/G7HziSGYRQyPcfaPRJZkIWwwqOyU5PJF8ZbYcUIR2T4gD\n3DgHssRkwVbruW2fxJ+qOuE+F0XeSSZlj1kF6ibY6+X/0irEtRKLlH0vqPs+\n91b5/LU0MgHBFqjLUS1Jog58PvxzbdeAYHhmqMOo7eC5Cbm5UDlu/K0vbIln\ngqnszMrDa9OqkX38pHUFNhK5rmGMQjk1koMeOlHJI8yiywzNWe/XWZ5a/xci\nW8f8JhLEtPlKtI2zzpFH5dHA6kaoLj8lopPzE+MN+44GvV1Qsp6NUVFEzvpq\nhrFciITscLg2ugFUGrQDT67Kw3x5IgdwQmgObSE2EJuaVceoY5OeY/QpKuin\nF5YnSU6aA1m8do4Ltjq3q5esIvE/j5NLi6hH8rRwYwP3/o4aXFJOHzswVwwQ\nQWQwexq89ptqSadYRfoMkyFjDTUU+IspOFYEwnRNM8QqziFVvYGGNFSDrFSd\n8rKZcj5ki2SUpaMZhcgwp1wjOCfTXP+1UYUiFHaoRKpgL6hTrDn3n4NljUG6\ngH0b\r\n=Mt8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.14.5_1623280337968_0.5334518912679489", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "1a354064b4c45663a32334f46fa0cf6100b5b1f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-VFi4dhgJM7Bpk8lRc5CMaRGlKZ29W9C3geZjt9beuzSUrlJxsNwX7ReLwaL6WEvsOf2EQkyIJEPtF8EXjB/g2A==", "signatures": [{"sig": "MEUCIATznTiNpCbyuMBCtZrpexGPI2kkbSRMNhS58gI9ug3hAiEAgjXZDZOsv1rAGtXDG4HE2P0TNhB1oxUXvadF/vAqZTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5985}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.16.0_1635551254989_0.7862777834536172", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "80507c225af49b4f4ee647e2a0ce53d2eeff9e85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-shiCBHTIIChGLdyojsKQjoAyB8MBwat25lKM7MJjbe1hE0bgIppD+LX9afr41lLHOhqceqeWl4FkLp+Bgn9o1Q==", "signatures": [{"sig": "MEQCIFFB5Qi81Dc+6vmKqaJO+nNwfajyg6SA0lajx77IhUtfAiBhLha9GyHKPqVliGyDM0lK7sMJHDwA7/A0vE9k9UZtVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kSCRA9TVsSAnZWagAA5O0P/RNz5jysXYINYzhbYMq3\n31nTi/K6rrvPEN/TBEK8GzWe58JwwICSvA0SNTRxCSUqCxhKlWGhAfPrzSpJ\nKdsy90PAjhHgBKOM1YYTqRX8dK6zWMwU5Rssdd1cW9P0pXqrytttj7YpsD6i\nn2H2H9Df9mzeMJo2T6VW/KduE2DVu17FeyCHuoU8Z38wSS+hAJU2S5AUtgJo\n6PqV4BJKVOAeNCIEyeGzd73LgyVy/2Wz+LJqXNlMpztS0mSKPghl1Io86V8c\nP7LIkVaK0zHHiSits6DHQDzdnqRN/BuL18RZaUbxEq+Tm7ww5IC5eRX91fho\npHwD0p6JExc+mejrcC4ijIYcTIm8J0HSh1Iklz/Q8ueNYuZRPj4+pRB69EFG\n6r6kWmiulR3kZ2jDphzG8mQjKX/gDty1ruqxzAo9KGW07fnCTposoNMxtDUY\nFIRViTZA6HRt3oHtOkW8KmsFpZh+Jk4A1zjN8GzmMcehEKcDEriATBPhHfhE\nAiRgPiv/FiYIlham/X/z4JaIpqUs9A7afwgmE8STP2UZCJc+0nVce2OyXUKa\nDPmYslNH1DshEl0xz/kcYoshJnOQX+sBAxGO8N1HDHHSyTgzNKN8NaS/WGBS\nVpBpt8dkfo0W1drvp3BsF2XygkA/d4n+BcsztYcNc1jsgFrnP2fzdpZi7b+a\nw7+x\r\n=dNf9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.16.5_1639434514020_0.6728171402252696", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "da8717de7b3287a2c6d659750c964f302b31ece3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q==", "signatures": [{"sig": "MEUCIQDwPRXldhQxY5Zmvow2vR38hS8/o4MPGL4IT7oZVLrYqAIgdVINflALVH337h5MQxSbBe1pg56Pq6EhLJok9iAXDiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk01CRA9TVsSAnZWagAAFb4P/RdFSeEGHma7wCkgWFwm\n6/PFN9HjHeoyZLn/RIw7/L+09o8jht2/0wMveya3yueLtc7nZln/dfG9GjeT\nNSEhUn2MU7EjYXsCl71Ad8PR32gkuuaK2VX5EA3l1t6bAi9s4nMraPEQ34Ul\nIbqhAiKeWBAnrYfVZh4Y2bq5SRZvuVvojQ/983akB9CX0HPnM1s8Rqm3oRZd\nUaQl8YmHLrQBceUxvdAfqyzuRUV/CPiC1WzoUrFSnQ9PH2lwxobn5PZVek4Y\nbSnmFvHNeU9s/t5Z9CMc97cs1mnZ4PT1oHQ47TghPadlq7Y/FIfaaP2OkRbJ\njHxQJlR9z3qqxvic/2OMrYqYfr6BL8hM0zTPmU7945qGjL38FOnbQ5cDZX2i\nImEIJJzsac45NU8HFIkWU65XWf7NCLUxK1hZsbizk3Gq/70yb9AwUVSTdmKR\nkymvPwoDq7zb79B+PSgCnMLAiKEel4SUiNnZAFJOuA/rNal0e/94X7RmDXcN\nAxqu+UVzNhx1gXjQ0T5LUcag60L/ctjg6d5mD+yVeoYTf9rdI8959EdIZAvy\n1fGjjlxa6KSLBnoM0mKC4lqW7s8BWiOsX6BpSn8Ncqc9kW5IF4//EO9adnqr\nWQ3/pVH2AmZXdP+JM02Y6bLDb7Cr+nxXKfxgcCdWtcGErTTECUv5k6re2YSa\nSFnl\r\n=316/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.16.7_1640910132932_0.24291787221892203", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "0d01fb7fb2243ae1c033f65f6e3b4be78db75f27", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-XNRwQUXYMP7VLuy54cr/KS/WeL3AZeORhrmeZ7iewgu+X2eBqmpaLI/hzqr9ZxCeUoq0ASK4GUzSM0BDhZkLFw==", "signatures": [{"sig": "MEUCIFbq61payzzhTbB36L4ze9qw9LCdP2k8xlOlSK7l1m7uAiEA8mlQd3s3sLKCC82wIxhkR20dX2A0IizNYSgsUSPIehY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugntACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbDBAAnBgGqa3yEo7O2jYuWyNzDxEeZbVB+Mxtj4JgNUgJzpuu4xJI\r\nY1jJRziZYBzd6ZNEAwU+eyNN3GGPJTSmuG4gplblujJ3yJxwtTr6vq2sv+9v\r\nHR+MmE/EP19ipTvu8ZToSdCbUTktZAbOC8wks8TRCRXAiPPlfjRu0F2Ij6BW\r\nyw48WpfBNfn+BNbKQoAtVh40uKCMHkiajPNbEWNNmuAmF8g3cu/Ec//swiej\r\n+rLt32iVKlRUfMQB+D/APrfQ/6Hrrlep9erHbEZFS+LkLMkKMTUxoT2fWngl\r\ndX7+RGhohTU6qwv45rH+rrgAyoIU70m5uisp0cftkGlDDpYPDl0W3O34Fndz\r\nP9XZX/UWvXe3bmwcif4vZKAQ/g/vJW51Okofh0crEwyVc2VEkRl2LUk3PKrW\r\nyHTKgdNQ8qxw9Qb9heqILmB4IMyVKXxQN4CwLyeKF9hta7HvcgIuYU8QkdS/\r\nvrv88ABTdQpPYiPhBiJHcyRarccOL31feXyl3tuxerlROQarPFSMVCL/tL0M\r\nseGE8VWacE0wxZhsbelNGq71cbmme0li1IeUReqD9mcapns+IXepiNszq2qg\r\neMh/hCxzWbo5tx1hPIPPmjupLNfqYZWvEb+BHc2AeB+xCZbTWIa737JeUGrY\r\nPmTeJpidKWHT5pA+feV1EaFZs55u/r5CzOg=\r\n=AsOF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.18.6_1656359405465_0.8986624920494144", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.18.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "1ecfb0eda83d09bbcb77c09970c2dd55832aa246", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz", "fileCount": 4, "integrity": "sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==", "signatures": [{"sig": "MEYCIQD+GT37SvrIbtPrGm5xQgXoYH/2Y3b8V7tfZBNJ9wgjdQIhAPG2PuwXa+NFOgGDuGs7aZ+K0KxdoSKKmGBOIoyGbXI2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSPhAAmg65NurYDRJis0sabLJORLm8P9q4AEw5R0bY3YzgNSYs1T7m\r\nNbnfPE0Bfh3srBGHZBWvAiC8DdODM17G2W7eh9yKWTPfaeUNt6OH6QO+Nc8m\r\nYTwqa5Wj7WtupsIThfRCKiM9oNpe4kelkUB+LeSHrGP64lnBqzmoFJi9A0P8\r\nT7JsBE586BsC8ORO2k5+vUZhkE9do53UegLMgdXKktgWhwr7g/4uFyXtpzWN\r\nCO01Wp7hZz6d+dDklKGUz/F3CjH5lWajI4rJqNIyo/5GrerzcAVF4GGwF8qo\r\nrvfnVCnIPaFwmtxhsrXxWgssHHyCT8zSCbyxl90nSoN01MCgslcGZ5k1jkii\r\nkfCS7uOFEy/9/Y+vF/SM0g/HhqJ85OK/7y1fccCaJsJQyPOAlSJxrRmvFYE4\r\nzi6yq6y3bCQrxtrXaLE0oqm13jHcPZ0iCq7/pl0cLWRStt0n1SCVmQ+tGDvQ\r\nIi89XGgLU+Hqgq5mXxW1v9iVt1PtoDlkTVORlVN7cEHkYMt3YTy27aDS0Eik\r\nKlpd2ZRw2F1Vi1ZZl7BNRLg2ccj04qOj+sRKC4HqhF2H5ct8uYR0cZf4+EsX\r\nETZtU/u/lU3lj1qQt/unqQgPdUcR/gkIfmPDVFmg8WP1T6fG4NfVVMdWvXkG\r\nmuwvyqlDg+vvCOc3Q+CR8TwXoxyeACZHS+M=\r\n=d3vm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.10", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.18.10_1659379601929_0.5465346667329376", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "0049dd5ca872bf7b105c7c39e9494e65500ffd4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-lTfVx0/PnMABzuAZlHI/iUiewm8M73Cuwf2yRGuhvbZKah94iBVEnL6XU6rmd/14Wn3PKcVwtZng0BUmKLFfcw==", "signatures": [{"sig": "MEYCIQDocucNoSQAbeQ7Libf/6uWptQAxKWngzdcw+3cXoY10wIhAIpwyoiUkwMzKqxfRhqYlSPap4VR5s2bFkwseQBl+8dd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr46g/+IYpt2EsNidvFKHshc1wipn3TMooxrWbF+LMwL746rjc5MCGS\r\njH8N1K/Hw8S0TfmZlQBp33x5ol3LsV6ClLuJJX8Yy3NrGeT31437aHZtfXR6\r\n6+crDxFNc/LJmBo2WDcEONho4bQALxDmL5CYhVGz/uiJ2wyoEtDlDRIwGqOh\r\nmSkTsbbsrvTkzF4txKCOHnrOehl+KKUjVaVXD1mD140Rij4OrSG+pi/1Vz1h\r\nYHYF1Y0F+LqrbdpdLH/Ct45EKsafZgVnqodaVLgySLrCftvREGyqE2oA6UiP\r\n0MIb5/tz04hjWGGmc08nbWaJsclqBni9o6hBr54rovJGfxUQigOS8UuXl83I\r\nWiM3A9Tddzaea8V70RnlM+I+OsOlby4Ejpp7knhWgHWdChFABJQQnDmX9vRx\r\nEbnUjTyWf69rJmh/tvSJSC9vDmqnumpwa3Ay3galHB8d2vJn7E8QEPsObnV/\r\nv35CH5Lh2eNS2pgf3Izc2Ngfr4PqsGWdCste8eE19KPpauyMi9ikAY+IzzQ/\r\nuvR3xFaubs/0Gtjw9y++AgR6Xo/1uCAuxn4D0S1gmTTww6qLha6jHA9KljWf\r\nd5XFTmT//4TyD2mlpGxO++2YA6wXD0/e+dnVb0T+0kvA6JXA6xsPtp68Z2XI\r\n9RqTFMuZ0gnkmC1smuPyNEFZ+wLyQ8sf10w=\r\n=kSQ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.4-esm_1680617371639_0.9606284496558908", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "b53d61a8498ef5215d9bab9e6edf861c02b6bf62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-SaTbfzl5dbAdgMbO/XEIsfAXN1Jjp/8PsBrlUO3HW7pEk7abCmWlUI+L3cZ9PAPwRIfvD0WLSs5Af8RGpEoWtQ==", "signatures": [{"sig": "MEYCIQDfO8QE3bf4rnVCq9BTvoiAQ2dNMujyhhiWTiQCIqKMqQIhALV0gUwfT/FQHd49ukunWmdOkaz6KgTYE8Ue/+VNXPrY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2ig/9GoIG3V7QHJBIjNjC4PYoBvt0T5nTfkLC8c19aVOwbESlYCCu\r\nKfO9j2IV9AuHqSoiWjF5o+QqT+6f3tVLDYAJHiKZWDqgMhbRRPAjm0q/mgF+\r\nnUGTkc+U8XL37J8M62Mkju902iy45ZQffytu8XFSR54lgMgMkAGDxPMj3t6g\r\nSrVFIrUThT8vUwNM73AHUbLaWEgskkjRXnUkSfM1KM15E8fm0c1qQU9T9yEv\r\nM4LQ5m5/ryTE5BFL3vBTy9VtpTiT1dENE7YCBh6kn+c0qAcfh6C/mlp7XfpW\r\n3nT9Etb5coNLPefs9uoWpnRCqAk68bDdhNxV5RADQTsGgN06HUwMIqpYwmjp\r\nOnTaZkkwmJp8aK2uCzmNOThbNKR0q6EOhvSKSHPTwWJu1Hn40dXYEdmz45Ki\r\nv4maLyAV+MCSVx+b2spQtvyDhKVkNjY2uq0fHqsHjr1kv9SQbD4iveov9NZR\r\nxYtgHKhbClBgRbGvUD6XtANOFrTuQKo2iqbEnZD6E8kVX3qZiJX5iGVBLYVF\r\n7hZU31oiYJK++QqnKVarjY36D6+HYlaKqj+dgZ/awwcyW9OX6rSg4l21S/Rd\r\nERNjNLr7tKzV/OHMX+YIZ/qHHknGMROZOoMKZIOK6XAyscdXg8MpI3uTsjVk\r\nFZ3YrCPmk42Be5GM1f2RpzO718QiiwAns6A=\r\n=kK07\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.4-esm.1_1680618084133_0.8634899132256446", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "1596f61b32f59d24e2493e33f2c343fff12f9095", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-IOzcwV/fyFQXN/qaCVKkbwS6tDkum65QvVeau1zmH/FViyzIO9nNFQhC7/q6GvKS+ywVKt15EuuxDbJyF5KXlQ==", "signatures": [{"sig": "MEUCICQMoWeIXth5JuswBCh8/qIRgyvT/SsGFCq2KlgaHofUAiEA3gmPs8ZnY4UWbhay/uyBDFNCFd8lZOtOjgSoRtIY0QM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDadACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/nhAAlZl8IJm5SmyeplD1cbtNoxulLGMweLidBuwPLYaDZdiV7LQ4\r\nklEAk8DVTf6BujG7xumjB0WcAq47pgkfrEMW7r5I3qHIl785eD3+YpJjeFwm\r\nvm3rL9tCuOwueic5QYIwGBRBtAMVMgFplIyGLE6sGDVUsMho1jvaZoMvihGj\r\n67E+d07Sf04KrkGeJqBtucgRgF7OM9/HeMJOOvqabJEhfCbkb3S1KOh2DQaC\r\nTXeI4mmXA0hQdjHAAwjAQZfDWxMTqROJIaPHZHPnxacciWAPVP0jM5R88CRe\r\njUYx7kpfV3/vji6fUD3ySfUb0U2FYVUzRHIY43zcDdMM19tn45dsdNICb/s9\r\nMYQFcxVq0JX+8CFoKtRMKET/QYopEgW+ECTssHAapTd1RWHozd+7hjz6wtP7\r\nJ/BG25eqxZryj7UDb1wtEJ235x0zjdGlz3+DNxbzujqZHFvngvoevf6Y09z8\r\nh58LqBuWzqL9BoB5o3w39RlsV8qWOBCL8SWpEU5F6Ld940B4KJWh+oQj0zLe\r\n33bZwlj+ZtzFY7caMSaiyZ0TFRLDRsOEtOnRf77FiapkbiJF2ez0LdEFDyrh\r\nVi1ykjxj+zD07aMeTEJrmkO7DyvY+d/sOu781wylOow2iQyGAdvQ9g48uYs1\r\nQvzt00iRJlmVh2lZUaS8jRQDaP/hVkRDYsQ=\r\n=O69g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.4-esm.2_1680619165269_0.0173341068858881", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "d13c01c72290bd43ff1f76fc122f6c97032a19a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-jjPLc1ejLKbWftz3M/FjBRLmHFEDggSU65/TQFOcibfyDAkSsQyX0AdH2ZbTxuzJdo/f5iltUYRu5CFurjz64Q==", "signatures": [{"sig": "MEQCIEnohNxjh62XlR4xqFwhQXP3LIhekdu6FMUxSPdhfKWjAiA8K2kmTt8yLtvFfPrMY6cZ7Rn57MqG2Vzk82DImGeh/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4YhAAgzj8xe137yRoSUj/AhnQ/b6kd6sFTz311t8H3FK6lInBw7GR\r\ncgAFFuI1jNQnQc75bjKSBEW0xjAkh5c3FwyaZs1rUBBH+rvTuTRABLd8JhST\r\nhX3bHtEDcslyVQYXYqxdyYLOQqjeSpJhDGi2yFd6w5OfjlfedtgMs/HU0fut\r\n6LEq8Bly6VewwMpBu97ARGggVpg1fRyDfih3anBIARoDUFm+KuQSAXY3eM+C\r\nYQuw0ALMK1VnXkRq4VBBJuBEmRtxaYgeM3+kgcB4ugmDYQd0Vvi0LRE5MCiz\r\nBNMc4L8zCuM+bO/JIKUAprH8zPVD+O3a6IQN1J+yKzb2NYSPipgOLDboX1BD\r\nJpGAmdBWDWf8zlSe1pIcG52EGxUqdA4m9xhQHP3Xt84NR6obLM1Qvcl3PEih\r\n6eSR+FQKoqVSJNpzsToTBY9NKqKxxNVkHBWvWH1JnWNmo0dulVolSa1+8UeA\r\nVk+XX6HEb6btEA+ydagpJUnbcsGtqayLOzrYqcpFWOrl+QamggDYd9gcGXVW\r\nzwhfpmzAPF+wl8BDf2Ojl/2650/I05f/N5ED5VAVXY3up8LTDLXTqQv4IgdN\r\nnFkMqK81JwxE1Fcg1okVoWngHN7wfYfkwVY+T3wzoTXzDYc9ljDU2wSd/W8V\r\nYp+uFkuxFCOt+S48Di/rVag036F6FkGlLbg=\r\n=DvH9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.4-esm.3_1680620173633_0.323733966500628", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "284dc775cc743fe8482f3c3730ae0d432660fe0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-euwdiqVb9eC+g09fblGaeu+UCgk+3soIEmw28dDR9aDiWc6RcaOc9HoXjLnuIBVspRNHR0YFQwJzOVpE3U5oog==", "signatures": [{"sig": "MEUCIEp9YcObsVEz1yFtD8XdyEcpTqj2tBbLvYzbNrj6VXj0AiEA82bm6W+es+UfC+TifZq01OsL1VLdFST+J/tTRJnmMXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp35g/9FhqzYrqcLK/ML+qyv9V39TuGhG4R19q+1EV5tq0JQs3bhbvI\r\nFwNYg/3aTukQYMJ4lWpsc5S0UpHFKcrliyq3S7UkTkE33s967VB8Bh8gBcwm\r\nl3ztfH9gXV30bJJ/BRZLUxZOg8/+lvuWjzXkj73OSMhtybCOORzli1amwGh7\r\n+kgDagVaChYAXOdvHpT6e4wpXSs9+rcKIOCTABFuAVSnU59tiFcOyPZngfpt\r\n9xmFCKHOdhwsCC8SG7qEZjsCTIHO13qPE1Djbc8Ab+5AoC1JaXUffoOcXW2X\r\n4IqqwsE7jAzure5ZhEx25sDFv4fIMZV3oVlCwvOPZFpe3Nxawj9wkZ/ptYXP\r\nT4Gf6EQIXw32MNkFHy8qF3PT+gu3gLJr1yoqh5SfylLiQZJsC9wR0prSARF3\r\n4oV5eNidYoJvga0BpwW5ll69hgHYvdd0aEo6dk6zh2vLomePM2l1UGgJK9xq\r\niNKH0icWseXHC1kYNe5DNMVRpaSlEBRZFcQmnDmhnTSqGDtB7nOMIQW5UpGE\r\nxh5XXJsGkre5o1In9NLIxUC6xMj3fhbNyQ9MqjGaC6Ba46TjeCc4xNdLwlJp\r\nniEzbWf/gwPtGggw56XWK/JyR1eU8OlqIQQVBCKnescHiAHxBXFqmUxEIXfI\r\nkZMHo1UaGycy5/5tMRyekcR+HM8XyZzynZ0=\r\n=Zkk/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.4-esm.4_1680621205930_0.771699001035542", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.21.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "1e55ed6195259b0e9061d81f5ef45a9b009fb7f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-LYm/gTOwZqsYohlvFUe/8Tujz75LqqVC2w+2qPHLR+WyWHGCZPN1KBpJCJn+4Bk4gOkQy/IXKIge6az5MqwlOg==", "signatures": [{"sig": "MEQCIDB3JxwXz4vSu/IhxJwD/BrYU4DSqAs+PEe+dXt2bByHAiBFpckmFiJZoMSEmqdGrYcMXxvR2yyAHtq0BKhJK/qnew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/3Q//WECEIb7isrS11NckPMyVWLvwUP+2ZoEMG7YMFi/qWFv0dKN2\r\n5uLsr1txKKt+RNTkQkG/qfnEMnhuhWZgVF/tv37bIqv1xgLgVyEL2CQOMuq7\r\nC8ODC6oTMNtMqFUjUA4zRSrd6l38NniWqGIejuwp0wjyZSUgPrCHHZ0SfSuu\r\nS0qTmABVRzrJHWl4o8mQZLisVWKoXmKcB74VJdbHaXoBMvGZvzHSTkCxe24y\r\nUecNKCALvBqFsxpm8HK0/w9XpNRE5fUXVF7b8y30ORPZx3HNiih8tnCWHcV+\r\nXXhJttpQEv/0raju0Hhaxf40HOMJBdYhRJwHdHEeMAwfhvJDGpRiQsMzWwBA\r\nTo7LnBeXr6A+aZVN1Bol0aw4O641qa5jPs+xwaGbNTfywK3gE3gTql+SYIR0\r\nCti83PYeJaKi5i8Sc7Mq/Y/cBiGEh1miVnvIE5bNl5am4FCskQvGJ+bZtnVP\r\nYgPrqloL+WqZ2jLt07aekFlFUh4+Il4FYzpcWkQXbafzeX0udCmH8MPELVci\r\n+jJwf6JQcZrg/qMVO9qroaGK1+bgd35DLjUBNPkI6RyHTA1KvDD2qXYeqa7n\r\n+yKktxx7Bli9iRI/1BCeUnDtCYQT5y8+OKDbXYVEJ98fsC+zvIqh+KZXCt8b\r\n5ifftxoRCkpg9azYNZHd4rQyQQZIEkQMaD4=\r\n=YIHr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.21.5_1682711421403_0.5764582171848327", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "ce0c248522b1cb22c7c992d88301a5ead70e806c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-biEmVg1IYB/raUO5wT1tgfacCef15Fbzhkx493D3urBI++6hpJ+RFG4SrWMn0NEZLfvilqKf3QDrRVZHo08FYg==", "signatures": [{"sig": "MEUCIHex3aA7srC3HQ9tyZ9JxEI/JBV7oPzLDrGjhILxC7P5AiEArhoDpW2z7JR2vxQI2oUpyNi0KtjZDIDM1EwTEHBjcio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14029}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.22.5_1686248483919_0.7304928628185892", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "12562b0c6836341d7be883dc1ed558f226414979", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-EhcNQnrjy/9ge5Sxm767OfK/bdKGRBAnJCpVUHHn6NlLElkqYfwEuOUb8xq0RHAn3YGJ2N+6asw1GR/9kv4alQ==", "signatures": [{"sig": "MEUCIQCSku6CQQ7/TqoFBDhxShFFFZ/MUc8F6UMfXUFhgBN5CAIgZqWxW6s1lpelxw9kJLSeAqiQNZlPuRZ8v6CJQhNhVJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.0_1689861599589_0.5599746016569971", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "8b32ae6c69d668b491126dd8c1c0b9e6d3dea75f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-eEkbE+wQcncJtkchvdfnPvPUrEcmr+x9uPSDwUwSGXx8YuF+pSUQBOuJz/gqyWg5KGf9U5UUVu/prSFi0bSi8A==", "signatures": [{"sig": "MEUCIHQ013W+gi3PXztk8WU0Ry86MF93hsMmXijXyiOo/AIKAiEA2IIZvPw2bEnp3Bqdh0zVfTeP0bg0oX+ThvSSvuMJI80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.1_1690221125693_0.13534791888620368", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.22.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "c723f380f40a2b2f57a62df24c9005834c8616d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.22.10.tgz", "fileCount": 5, "integrity": "sha512-lRfaRKGZCBqDlRU3UIFovdp9c9mEvlylmpod0/OatICsSfuQ9YFthRo1tpTkGsklEefZdqlEFdY4A2dwTb6ohg==", "signatures": [{"sig": "MEUCIBamiSW85ZFZyUBFKpDupO5zO/7uN+V3FEVYgu1PkyM/AiEA97lOsV03a6jM2qD03/QQGTshZ2E4L63ercT7vICnMI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13925}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.22.10_1691429114384_0.3060192724751374", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "9b54ffcad87a8a0696f57f2c883396a288178a6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-dHN74m9DZdUobWCrvNJbCrjoWJFmkiegurlrMjl2OPJhe/G6iTTMhV9blOTLwnG2PhXRK7FMPaWmEB0FMdT1Gw==", "signatures": [{"sig": "MEUCIFJF1NoAOcrovU856whtqOG7wOVGRU65RrVWCQp1qHiHAiEA9j2zE/KDQ93cDhIZQd9FrlNWnU+f2fqaGkldWPQf4e4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.2_1691594099093_0.6127528922877103", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "df905fc513d7b93166287189f0d0b2a8e364e627", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-zzZNEWuuAEHg6C3rC4IVj0Sz5u7Y3BmI6ZtX4NiaPEsg57Q/kZsZ+/d09MI74Hn4dGNmIbyKS0RV86kd2VNexQ==", "signatures": [{"sig": "MEUCIECgVTLrGOVtqOexm5GL2WAoh9MacG62kjdEZVEdiWVYAiEAzJueudRp5U2p5nTsr9t5+YbrEb6l2uayjGDKDVVGWSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.3_1695740222207_0.41139807929398264", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "6dfaa144b8660d80c9c90a5a38f7b590d3e15c30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-A1Tq+RwD5tw66/a9CBWrGmGgMKvqzmqhw6fZa8Ecmlne5r5lYDRmh9bhPImqPyE3bEvW2CsS85XqE+9AfZ/U7w==", "signatures": [{"sig": "MEYCIQCssNfP/WeDodSoAz252T9OgcIgYsv6X4SPQQM/OEUFsgIhANsYmk58Qc+bcZpSuawh1huVvNe/y9K7V1X+kPGkH0Vw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.4_1697076382397_0.3471591649824515", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "1f66d16cab01fab98d784867d24f70c1ca65b925", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-OMCUx/bU6ChE3r4+ZdylEqAjaQgHAgipgW8nsCfu5pGqDcFytVd91AwRvUJSBZDz0exPGgnjoqhgRYLRjFZc9Q==", "signatures": [{"sig": "MEUCIGgWHag5xdVbCMoHwKkgYUFxNmZNrwmhqgc5pDGWLA4XAiEA0fGLqeWaAi6QgazkwuddxOp2i9TziWge6pV9uHehAlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14005}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.23.3_1699513442563_0.9618795062228835", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "f77c26d3825da1719be57af06f73587c0ed75d88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-lWFWuYfZI93ePEKFj/LkcLGfCgU/LaQlLY8twbDfqxK+NNiunQoDy/RdTjRHv+5UUgPC0BgaTwgmmCjPDxGtsw==", "signatures": [{"sig": "MEYCIQCBg2tX+WXjQFPQ5z6L4eGuEG7DjHqZ+07jGW7+m/bN3wIhANDj0EfMUVXlHzRmgO47xsaSxZWxs6jRh1klGtVGxRI3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14011}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.5_1702307939097_0.6308023987146147", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "6ed69bb2c9e7726b750c63899a10a1e31bb960b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-URl7m1t099FJpY5a8K6iwaNUvIyklhRk4e1vIU0PHxzbEaHzVGYkhoR9iK+2PdiH6rmsAjfuE0R8bWv4MKgdOw==", "signatures": [{"sig": "MEYCIQDxUBZOcveMDrTmlqrjClCYtkTEMRKwRenGNrTUYGRh7QIhAOy2Z01V70beTnfrjLFYlINRwi30qXDa5ROZ6QNyA287", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14011}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.6_1706285650463_0.03698502012205762", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "fead62aa5d52d0be8ba2f95cb5387456f33b5440", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-mNs+DZsh8cbArXj0UuObPptyAT+6gX4GzK58vU04gGiSb3RMHOZNVu/plK2HQiPQAjRewRSEcoPuXOqcLvriOQ==", "signatures": [{"sig": "MEUCIBdi3YIhcUctL7Vy6knq0Rs+7MtjxE16JijJGbuVxQTNAiEAg4Q1I34F1m/amxyXBE13kWwxz/pk/OsoXrQ/Z4nFH9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14011}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.7_1709129099535_0.15760246151361756", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "fb3fa16676549ac7c7449db9b342614985c2a3a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-RlkVIcWT4TLI96zM660S877E7beKlQw7Ig+wqkKBiWfj0zH5Q4h50q6er4wzZKRNSYpfo6ILJ+hrJAGSX2qcNw==", "signatures": [{"sig": "MEUCIBMQPTqneljHP0ByL7oNSrvqKW25MvXNQU13ndRzEH4EAiEA/V19rha6c10T2disOjgRUSmFsd8x6tz7lxrwkPAhopg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13936}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.24.1_1710841747828_0.723785359057318", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "24e2ebf0017501f405cd39fb412305d0ed5fbced", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-v+FFvdDlXp0gqvBTZOy4cOZDcRryQwlSS+5khuE7Oh3zfXy9XRoD3eB2uH2EIrdwPIJvE1gKqAbBhMtJ8J2CQw==", "signatures": [{"sig": "MEUCIQD2fkHGOl91nXmjO14z86xTMasq5+HTLo5MnX5L/i8faAIgRgT+f08J/0ZB83cTDBDFEFvdRKnOvK9Mr3u17IwuQ3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13925}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.8_1712236794325_0.7211409669590951", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "c8ddca8fd5bacece837a4e27bd3b7ed64580d1a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-bKl3xxcPbkQQo5eX9LjjDpU2xYHeEeNQbOhj0iPvetSzA+Tu9q/o5lujF4Sek60CM6MgYvOS/DJuwGbiEYAnLw==", "signatures": [{"sig": "MEUCIQD0LKBKa6ghfFExaxeM3drOgt+rPbxKMyDNvGdbFREISAIgBJOYl4obp2aYAkQVuK6lBKlryvy2w6fxiaFB9jmuqEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79867}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.24.6_1716553476942_0.47215292242005624", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "251ca2fce3eb2a5802271064b3ca1ca0084d85ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-saCLyuHBTlCDof0OkldqTNm9f0bANDO3T29eNDoSfvJO/7OaqqbANH4C+fTRa4NCrxfb6KlZF9EuqhQXpxbSsg==", "signatures": [{"sig": "MEUCIQC10J3TigMJv9YHKnpJ1KlpKhTC4vW2ctwEac2z+G3XwQIgP9Y0xJQ6t0omSO0htCETjo3riuhvOiVSpi8pUck85Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80125}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.9_1717423461580_0.9320866166306356", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "4352a9ecff178fa37755b3bc0ebf86312e4bab68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-8k6/G15vXM+Wr7QIljqCmL6XXIGU7J6e3xFppTrljPFwSJLiaLtQiKu8f0ydIyhqZMz7IJbBUOobIihVGe4DuQ==", "signatures": [{"sig": "MEQCIB2gD7YKlsjvbO3EkiJAR4UZCsi+9uxw9tVPoT31aJzDAiBmSrWA1DNOfCzZWVs5v9vKfIVGl/YTvlKAtS9D/32zCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80132}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.10_1717500011922_0.2274530444614702", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "2023a82ced1fb4971630a2e079764502c4148e0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-U3ap1gm5+4edc2Q/P+9VrBNhGkfnf+8ZqppY71Bo/pzZmXhhLdqgaUl6cuB07O1+AQJtCLfaOmswiNbSQ9ivhw==", "signatures": [{"sig": "MEUCIQDVdQ/LyU8HPHb6IfVmR3ETQI2zLdb7x3Cm4NkwNyONSAIgWvs0bQjLtqRUbfP7vpGOoBbjqFNjNLj5Y93hYTmLpYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79822}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.24.7_1717593326709_0.9304915815387582", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "89d13688aa8bda4994cc7c30328ffdcc2095bcb2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-kOXaFPfZv4VqxtlHq46UVXFCkXeq7AIT99Untf4qadJn2CB41XrOxHF2JecrUvV2lFM4jjtqnhroYHf7wsji7g==", "signatures": [{"sig": "MEYCIQDUA4F/VeWVjfoGgvka0S6HWeYvxX6ZyhX/GF3k5n58JwIhAP0qzgdLgCXOYM+AQcrfhHDpiWigoTeAzhfoOdmQ6It5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80021}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.11_1717751736554_0.4467685099235743", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "760fd90ffd8cfb8de1ec499a99d819576b186a51", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-zHUfoKSYDK6KFKsmZzZ1H/KvENR2s7nx6Z4b8S6xBYWsI4updDWuVu3fYClo0NvBX91SyNnWt48MVFN9972PoA==", "signatures": [{"sig": "MEUCIHyUKczUtWv7zH/Ra39qA7CMa1CmV3IGCdP6D5UC2Gj8AiEAjXfyD8VmTjgVtK/0J8r+J/YyVdExeXmsWEqu8/lTNIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76817}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.12_1722015214100_0.4707747815231771", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "973592b6d13a914794e1de8cf1383e50e0f87f81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-BN87D7KpbdiABA+t3HbVqHzKWUDN3dymLaTnPFAMyc8lV+KN3+YzNhVRNdinaCPA4AUqx7ubXbQ9shRjYBl3SQ==", "signatures": [{"sig": "MEUCIQC0eegfigPSZ4bcqnaioGF9yv2eGP5uJMa9PL2o9RccBQIgcHVjJgmJ/lwlBH2EQMjiUN3el2oSTRoELXgY0Ric9aE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84360}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.25.7_1727882095049_0.7525975015645472", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "a75ef3947ce15363fccaa38e2dd9bc70b2788b82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-s5EDrE6bW97LtxOcGj1Khcx5AaXwiMmi4toFWRDP9/y0Woo6pXC+iyPu/KuhKtfSrNFd7jJB+/fkOtZy6aIC6Q==", "signatures": [{"sig": "MEYCIQCGAW6pzfu00ZRaYDUswgZvGjzedLD6J7UGbK1Qk6z0UQIhAL/SjyS1VmK9nkMxsTcHYvdSlvCp1JmFtmvEglCczv4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13899}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.25.9_1729610474129_0.9594696370760591", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "910fae961ff23fe65ca20b1254b7a705bb8384d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-pFNVhpJRWSLbVSEd9SzRJ9mf1EXZl2/08HdQDCMuyV21q8AqB3bzwh4bJBLsQlFvt9ACok6DkZ5gjgywJ8oh/g==", "signatures": [{"sig": "MEQCIFbh8w80C005cjXYgvl5sm3iiLfDo//Fmqrnu2Q7mj89AiADmEPH/OkW4v085KY+wfTs0tNgQGY8L5DAf7XyejAoxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14226}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.13_1729864455836_0.881538147657212", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "496c0cf65c5f54b178b72cf6f85dea3da78fffd3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-VEpBHxeveaA5ob+sJiTHD4yTVaIrRcubjmtWRzLZ+JqhAa8RUQmzlv+E2as4qfh94oivJ7aKakcYG67Jw6X3JQ==", "signatures": [{"sig": "MEYCIQDt4JwfsZyzi+q+aymD8vylpU6vcGCVF8OpM+oe2oO3KgIhAKkW2wnuF76lNK4BvB46t3f702AS0WqnCTjZGdCBIgjD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14226}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.14_1733504047489_0.7153565675406186", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "f1de8a089e0d616fa9024cfb242eb55b5e40f6e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-jQ+UIK9V40V06ABSybmZqPoCBOxRDrJIesdC9q1hAujUQlfWqJ0SNwnHW55EiCAHwzh3R/NUlAcXmaERwEDfwQ==", "signatures": [{"sig": "MEYCIQDJSz1GuxJe+5kg0LxtA9mpxSWCluojkjlC4Gql+oOqngIhAIlFQ+e26e2ujhkXWP6hjf8eTUIFZoutf05AwBumK4lw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14226}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.15_1736529872914_0.7584118975874898", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "88e0021aa07d6a58ec7d968570616fb75e96a486", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-ybvSqptsItorgmfV4AnKXRjIAeSlPOOUAbhDcSpGWQHnauBgupgJKlstrMKzJhRXtdCAPiMmyq/lZktmZu/8qg==", "signatures": [{"sig": "MEUCIQD7/6rRZGjFfocJ8In2Ce47BgQxuPUNNWGnkClS/2hOqAIgZ8Fk3Tfe7QYIHhUaI5/EAjOkoZUMRq7/ZZh1NzQj88s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14226}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.16_1739534349326_0.8692548628917032", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "401f4fbdc1470af33d0eb60d0b4a909da24fdd41", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-S1CnT2d3l++K0XVj6JUPWtfn567oIIvcDOTJCvQ0pnAduRgdr6EhjAt+0E76ot3cm24zGpztvPWYJMaYeX4D4w==", "signatures": [{"sig": "MEUCIQCqz9C0N3HqpL805UWZ6TSlpPbiqkRDrYckZ6Qo6Ry8cAIgNJNrVNHMm9y7Ou8D2r68VqGZgqJOAjm8hx1sGjEAtPw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14226}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-alpha.17_1741717502446_0.15841042379970105", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "3e3143f8438aef842de28816ece58780190cf806", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==", "signatures": [{"sig": "MEYCIQC4iYZDEk7REM2zTPUTlqP5mOMcnQO85YiI2qdIVi7ZqwIhAMUhE44q/C8Q8s4BWw89BrBgvWpRPRALN4LB+SgF8yVN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13899}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_7.27.1_1746025739039_0.3503899173099432", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "dist": {"shasum": "336bf6f7b1a9956e4d6a09f66f2705421bd0df52", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-niHrCNMFoWBbRUQ9yAaFsqHZaG1LSqhqpc0vLjDGCXbqcfG/7a68Iv2BKDl/uCqwtXzAYxJ6ApmyqcetdZI9VQ==", "signatures": [{"sig": "MEYCIQCPfoBMg27+f2JF2m8bGaXXAJaNwTZdRRz95WPFcya3tAIhAN5oobYH/jKAWOWZBxqmiOffw7mlls6vu8Zj5sjQUE0t", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14202}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-beta.0_1748620270733_0.8802174420261384", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-unicode-escapes", "version": "8.0.0-beta.1", "description": "Compile ES2015 Unicode escapes to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-unicode-escapes@8.0.0-beta.1", "dist": {"shasum": "64afb67f1cd41cec9aa902e08dd3aec4e8fbeca7", "integrity": "sha512-7lZ4O4ujzbB6CKOcGPNIA2nQ869b+On/pt3EigP3jEw5e2d1GazaBt17qgjd840oGYf9wI6YW8sArDzxf88c2A==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 14202, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCID1D2OuSZlJihWfzey6jNlG9ZNM/L09uSYH4ijRCy1VuAiEA5BaA98fG7/ji0EWWlYbIkd5zdSytpW6rlyc3+RPeTmI="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-unicode-escapes_8.0.0-beta.1_1751447062317_0.6106838235965661"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-05-26T21:43:22.316Z", "modified": "2025-07-02T09:04:22.738Z", "7.10.0": "2020-05-26T21:43:22.661Z", "7.10.1": "2020-05-27T22:07:31.163Z", "7.10.4": "2020-06-30T13:12:14.464Z", "7.12.1": "2020-10-15T22:40:34.544Z", "7.12.13": "2021-02-03T01:11:13.673Z", "7.14.5": "2021-06-09T23:12:18.142Z", "7.16.0": "2021-10-29T23:47:35.112Z", "7.16.5": "2021-12-13T22:28:34.193Z", "7.16.7": "2021-12-31T00:22:13.080Z", "7.18.6": "2022-06-27T19:50:05.643Z", "7.18.10": "2022-08-01T18:46:42.129Z", "7.21.4-esm": "2023-04-04T14:09:31.777Z", "7.21.4-esm.1": "2023-04-04T14:21:24.274Z", "7.21.4-esm.2": "2023-04-04T14:39:25.440Z", "7.21.4-esm.3": "2023-04-04T14:56:13.786Z", "7.21.4-esm.4": "2023-04-04T15:13:26.123Z", "7.21.5": "2023-04-28T19:50:21.584Z", "7.22.5": "2023-06-08T18:21:24.165Z", "8.0.0-alpha.0": "2023-07-20T13:59:59.785Z", "8.0.0-alpha.1": "2023-07-24T17:52:05.874Z", "7.22.10": "2023-08-07T17:25:14.537Z", "8.0.0-alpha.2": "2023-08-09T15:14:59.265Z", "8.0.0-alpha.3": "2023-09-26T14:57:02.382Z", "8.0.0-alpha.4": "2023-10-12T02:06:22.590Z", "7.23.3": "2023-11-09T07:04:02.726Z", "8.0.0-alpha.5": "2023-12-11T15:18:59.297Z", "8.0.0-alpha.6": "2024-01-26T16:14:10.619Z", "8.0.0-alpha.7": "2024-02-28T14:04:59.675Z", "7.24.1": "2024-03-19T09:49:07.995Z", "8.0.0-alpha.8": "2024-04-04T13:19:54.491Z", "7.24.6": "2024-05-24T12:24:37.122Z", "8.0.0-alpha.9": "2024-06-03T14:04:21.734Z", "8.0.0-alpha.10": "2024-06-04T11:20:12.114Z", "7.24.7": "2024-06-05T13:15:26.917Z", "8.0.0-alpha.11": "2024-06-07T09:15:36.695Z", "8.0.0-alpha.12": "2024-07-26T17:33:34.282Z", "7.25.7": "2024-10-02T15:14:55.276Z", "7.25.9": "2024-10-22T15:21:14.316Z", "8.0.0-alpha.13": "2024-10-25T13:54:16.049Z", "8.0.0-alpha.14": "2024-12-06T16:54:07.654Z", "8.0.0-alpha.15": "2025-01-10T17:24:33.114Z", "8.0.0-alpha.16": "2025-02-14T11:59:09.485Z", "8.0.0-alpha.17": "2025-03-11T18:25:02.663Z", "7.27.1": "2025-04-30T15:08:59.194Z", "8.0.0-beta.0": "2025-05-30T15:51:10.935Z", "8.0.0-beta.1": "2025-07-02T09:04:22.483Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-escapes", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-escapes"}, "description": "Compile ES2015 Unicode escapes to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}