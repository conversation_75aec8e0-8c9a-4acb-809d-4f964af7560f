{"_id": "@babel/plugin-transform-literals", "_rev": "115-f1daf1587e295749930c668b5283201c", "name": "@babel/plugin-transform-literals", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7d206461e912eb8b7e17eb006061353fce6d7513", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.4.tgz", "integrity": "sha512-9Rzu6tRF6uxRdzqo0XRRqXyFvZ8NDO1i+jviQ0uCRjmMyjs24YhUk7is6Mf1nozVHmpIesFDjMpeLgFAqOjc4g==", "signatures": [{"sig": "MEUCIDS2pMkC70877PTfvOK0Qlxdw+plWHvqHjGArOcF5oPaAiEAniiEMI5d9p40K0nGpvXYwAQyZZ+wfHIfqas8DHHFMFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.4.tgz_1509388479440_0.18133961246348917", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "bf600fd91e4841bbfb1bc61281e2c41b1b1d4ed3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.5.tgz", "integrity": "sha512-VUAAepR9zEOYtOZ1nYoo1PrHxYpMu9rs6XzQw2r5iGIPF4d7FPvODvo7SdIqwk8/iLSyPOiPNfCO3T3ACewjPA==", "signatures": [{"sig": "MEUCIC0VQQbk6oEe76mYDsHGquxokaXrWRfUjLRcCwoLCn2tAiEAzUuPKBt/oHiP4157O/f4IeRX4XJZnOjRKrHwwpTU8VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.5.tgz_1509396978759_0.2534939267206937", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3ca38c724cca742b61831678e95e9f75a6f81892", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.31.tgz", "integrity": "sha512-6WtzFJyFLtswx2Uu57iaVymxXUF7qDCNhNMEBL49sphSr5hL2R2cjfBogI5/uhT2+5mCeBeeLCYBEhC5f6BiCg==", "signatures": [{"sig": "MEYCIQC3TLZRadt0fZeX5CenmJzgm7eTccVEHKsrlo8UP/atNgIhANFnQ0yycA+fXsVK6r7WrWbsCPqPqJNcwo3k0im0n1t8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.31.tgz_1509739405228_0.5107488450594246", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "844178f9a21d4ba5e591a990f68ac51e540e8ff1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.32.tgz", "integrity": "sha512-uBXC+JcYALYHkXRZZD49mwD0n1ufWu8lbuHEIjpPvEncp63zklVgV5q1XkhXR7Dm1PWDCuEy8Aj6I0La/PBSmQ==", "signatures": [{"sig": "MEQCIBp9dvcXTnvR0HCaPq5ymAHZYzNtYF+rkJXT8X+bJPjtAiBcMPn9D/9UEiNBTt3XpiSVJ99yplKT0rBBvlsm8k0KyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.32.tgz_1510493595788_0.23157140659168363", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "eee8b8011c8ae0f3de9ad95dc98f6120928e242f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.33.tgz", "integrity": "sha512-3+SgSmblzaYa4bMyYEKUHsklhYkRPPYL9M/Cu69opV7YbNof8Iz+zbL6CuEOwVoD4p6gzTck02PwpvrY8bYF3Q==", "signatures": [{"sig": "MEYCIQCrsFsiandZJLreGXi20oz38CIN7gLm4pey/BLeTdMjrwIhAJph11bsY25187vZPK8Uwxk8ei0ParsiGXNpw6GeBy9t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.33.tgz_1512138497839_0.06934757810086012", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ed84b3e52721ec241fee60935f5e7763ea5744a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.34.tgz", "integrity": "sha512-rqyb71ffA6Lqlt29G0QotDTt8z/vGghGJyftrG88+9/M6LC+azaR1yzoaoV2UQl+YsErEtm+OHfD+1F7aCVCNg==", "signatures": [{"sig": "MEQCIF3GUnu7P96TW+dZHQf+ElnsgrDXtRuU56RKfnNYx0XmAiA0WqvU8NpHRws8R9BB7/06qcq7XN7YS0CQUa/TGrtqLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.34.tgz_1512225559130_0.25821343436837196", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c0634fc137702afd7ae77829a41be45c4fa530ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.35.tgz", "integrity": "sha512-f98iHJ3fhO5OPP2kijlQ7+kNibbUsSkEVQ28CAA81JtT4fifZUfPgA0S3Piao0fNmxIY6JwSpUH/i+8mRWhfpQ==", "signatures": [{"sig": "MEUCIBWr6GtiNN55MAH65cmDwSoytIiAvp/x0SROmmDm/sMrAiEApzutWvleOOODegh6tl03M8Yv9863U+l4NGbtEArJVKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.35.tgz_1513288065816_0.26296937256120145", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "60d1df041aa70b8c81c812010315c5a78450b31c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.36.tgz", "integrity": "sha512-7Ao2jQa5B4+ofN2wmZSM/vK5zQKyg5f4BUReQOcy/ypXrdvU+Z/YeIsD4lN4Tr32BTGVkVRwAx35KNy4Xa5LAA==", "signatures": [{"sig": "MEYCIQCBTG+R55R1ya2b+NRNXZIn6fSd00qOFcz6UjzpFU6PZgIhAMPCAUMawOzWi0xV/urfcloNReV6xNvRS8jKoHU21lju", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.36.tgz_1514228676181_0.6485294848680496", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a416145570366aefa7eb847134f3e1866272abad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.37.tgz", "integrity": "sha512-yOUav+BEjDL4zefJUydYU8eIZ6ZhB3GrXpVei8spZOpJxy0THj7UczMQnGJ1xUFG0f+6r2M+2dFejhgT1HRB0Q==", "signatures": [{"sig": "MEYCIQCo1fDFaFlmy1/lZJffJUt0UYIqUUSUuTNMOSeSu+LecgIhALEZEq9ufF+U10U3HRKFilfYXyD9ScpKrE3bwhaCv5kb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.37.tgz_1515427350693_0.8496027139481157", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5ec8f714bcea508073476ae7d13e108ac9bd4f11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.38.tgz", "integrity": "sha512-pNx7CUnGOR74fkpvzIho5GECAgaf0m0px7GZ2uzNYGMm5nvUT3h2bA6ASjI0YQ7FLHQScRdrHa0rBvD7lSIZFQ==", "signatures": [{"sig": "MEQCIFHbdCW/cOLIvYPTnvgzkzr5Rq503az8JsgJeWRbl4RuAiASTRcTv2YrXlEcJv3//gAXKhf9WilzbBQDhtbI9Vl9yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.38.tgz_1516206713258_0.17641270370222628", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "5c6a097539a5d20cc285e97d785d418d6aad305d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.39.tgz", "integrity": "sha512-NSrPpvHPDOkLd82mLlNNbxtt7umILbyWK88ArgR0W9yHqKHEHCdZ5zpD9xWOvHWk8Rie+IIq+CFWgicpPwzTxA==", "signatures": [{"sig": "MEUCIQDrJAWZMpYnNdvBKc6eYz24KcCrpGW77uV2RowEzXtXPAIgagOhPXDYV443KG96OwfWJnMh2WUAFZdX8x14rUKq71w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals-7.0.0-beta.39.tgz_1517344053467_0.025173750007525086", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a6bf8808f97accf42a171b27a133802aa0650d3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-p7VlTod2r7srx0uKVrKqMJR1f6iyvDAnlLdTEDGrLHpP9pXXvIc/bP8xZTxVPn+IziSFh6FvOzHXXLMtnRKnow==", "signatures": [{"sig": "MEYCIQChc7BRoFxvs1D4/khy0QFr7Czj1CKA0LqHsLaQlQ/JZwIhAPXQLbKnKntLeB6H7R8ZZKwGc4VzITGVWLchlyia13u6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1924}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.40_1518453695951_0.7839444992045188", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "64cd9505667e48acd17f89ccb04118e2e8f74315", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-OljGPbV41N1WJHOwm4yoVtVAhFhDOmOktONzA3BWIV1EDZ5sVEZsUveBq7vLClrZ3F71NZTQerfcILruPiW3FQ==", "signatures": [{"sig": "MEUCIQDW5xGO3qZAWztrgLjTkyOK7vS0RO/PUlYWaXB49yQIVgIgIp56N9euwzaNTDJEvqpyMpO1YAzZwRN4kEj4r3fA/7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2159}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.41_1521044769185_0.14962701168813597", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "61a34a82d757be4ddf937eda4b2d6c36b63b9c4e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-mWEayRwUCY3/u8LZpcdR96TiWqofP60jjfVZUJ6agK6ifwEBgamv1Db8syIwUCjaZww5bjyZqX61AmP4fx3dvQ==", "signatures": [{"sig": "MEYCIQC+MtB+SHjAQq8ZBtNbCof6wf7eHMnwIu+K/XdSCblSEgIhANA6V7COz5O9qUUoajXyYYyq5DKzKi9YvnmGQ4S6xJbF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2159}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.42_1521147043925_0.16700487718873802", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "45c33a38d71b4eb8400fafbe16cafa0c5da508b5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-rx+p0S/oHXfuEXi40gXCFJNcDgNI850fxjGBKQyOlLx6ueQdhZmJcSWu+x/8SFGx8ZoDD3JEFb3K1a9pYuFR0A==", "signatures": [{"sig": "MEQCICLUuL5WW7AefX8S9hJ04vxNU6n0GwKynI8JlHczm2FtAiBlTjMxqDs+7C3xCmjxOJzyE2KJiS4eIgm/r2FMUJFC3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2216}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.43_1522687705880_0.5305332850315279", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8c85631ea6fd8a6eecefdb81177ed6ae3d34b195", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-h6KxHCj14tYj1dahXlfs/JP1fMzMHdmVCapM4UjberhkcAj4ZkZpmdQbN2odaQRT1DX2hA8eQWsmeKJw2Ifq8w==", "signatures": [{"sig": "MEYCIQDBjAv6ww4sHluqkAGWm6SRb5OvyT1skoh8D9p4uK0o7gIhAJ+Ufc8g19zAWqt0jy5MWlkYzSAUHoVKDN7XTzbRvJF5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2315}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.44_1522707607080_0.24922804264611642", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7ab54998b2c1809c014191f18f12dc400c9a39f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Kfm+Jqz5lEc5cR0AEVULG93Zmkla8mfoOoOk8zjdjHoiszGr8Z8MrNFBukAXngXG+N4vlgMaonmdI+Yoa8SdFA==", "signatures": [{"sig": "MEUCIQDemwVKcjZ5fqYmBAHW/N29Axo7A2A/qs9ZHUWCfOFgiAIgZJZC7MtLlRRcLZX2ovbWRtrNchPO86Ko6jSfjY8oZy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1kCRA9TVsSAnZWagAAT3UP/2ABZVJ/peZt1Fu2Qir3\nZc4b25yUbu0y2gHRYj4u6SJ/YWuXgumWkEfpV68iYeJVqq9gjvznHMy2yXDL\neYuEeez8FAMYdjsbSitgU13C+FBIOLUrN6ofBaTdihwdQywXedhen0JF3aB+\n0bFEJTSVeW4T3ma5V+lz6o9ONnssMr320sc6DZ2ZpuSGw7NHEsGLgjfjUNrk\n+N49kmrc0knE+WxkI8Qx+HxCJ39WyZhTKbt7ziCwZ83fNj6OrEM2U2Nr9X9P\nwjQ4MQHVsPdxOwV1LegHj5JPksy1bmwDIUvR6STIUG9WanV4NSqPMFC+zY04\npoZAQRn6QwSD3TH4Mp0OUu6aQrJVyCfY/RaNbwGQ53TfLFSsR8ptFfqyXAkF\nQXFALw433X7IyEKr4/2DZlRVt4tOuiE375WekwGEGBjzPJEkEP1vZ7PjpEuZ\n8jLt9BgaMZsdyHiwPa5Qo46zzYElhTHbnt0UJeZkxqJPL8o5ILgtZSzV6sUi\n0v4CJE2KJQq1U3EM/5DAel9HYL4syk47yxnY0gUYy9MKCpeEMOVID10HiZJ3\nksmBcKhw3/kNTDrXtKCNq5VW4L/mZH1oESUQKUMaplX4USzTEpn/YPuTCGKC\nSMyTHoqvIadCfBeMlxOIaMLpGkpT8lha0CPc+bY3HVBYvzazFd83qtY8rf6J\n9vgm\r\n=4gDq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.45_1524448611595_0.24170790769791006", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "84f5bcfe914b9fd4385c0ddf469f9ed403ee68bd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-Uuo7pRsBkrLrDg6XpOAMfwhKw56SB5qVBniUVM04uf8wf92S2Z5tSPNNfn1iTgphuckAO9vg86l2XJ0Y/QD4YQ==", "signatures": [{"sig": "MEQCIGf4vQDxFs6vZSe5JOfNpPsZyInev1l4mxyxhUQbQOSrAiB73vKTYb+lGF+Srql/u1LKCPM4UyEyI3U95itf23vyeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGTCRA9TVsSAnZWagAAcnEP/i1M5zGFspYRTDRAUD5g\nc3Esnm05nEo1y+7FeCD+TtkIizKK63xxXwktlU7boVmJH8o7m/hCydTucJYA\nWaPb5pwkZng324hQYz0DLeSGLXlqJ6MfIRJDB6p4je5juidH65Dj6ZQbRLwG\nDZdDoUh4aMm/axycBhkLs2/cKPVVnDMOkAhhflIS5OMsM3/8OgkvU2rBzOoV\nqvj3Ua9DA2s0NRXozUvbrqh/7wL4AeLdfqmzH1/1ScDkYTlkSGcItbtyWyBp\ntBnZ1kJ+2Yrgcjnl8jrUxx+j7ODCfggJFPNeZNgmvxkPIMfybfwPJa8LA8YW\nWelfy/05fIbtajh5U1d+6ZQVgSaVDMyFX9FuAiar7o5oV+rKbEiCUeD1Xyl0\nufkzdLb5eVpGvVPLXKJYcrbQWiRbeeEvWv15KtwE6XrXeyEF0uS9WHKmXu+O\nn3bBfjyYslbUcr3r8cynjegunMuLb+HPUpwJ7HMGR/VLFbktlqlQcpwx4VzR\nP+AOIETq3gkQgZVa31rAHcGqfk4n8PilKYGQVf7+Vct/J7Py7XeuzdmJuioy\nzEOJQfDWq49cPw/s8PCAWdbyDQngMNlbDzliR6D75DyVGrJGU1d24cSxhKPX\nYHwzy8QqWu9QrwYq5ugSxcD2xj1SKv6L4+AJqOU4veD+ATgI69tOsm5RkXvE\nCo/e\r\n=oL3R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.46_1524457875607_0.3277987178578263", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "448fad196f062163684a38f10f14e83315892e9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-PxBw+52qWypwR76YfS2FlW4wZfp61SjIyt3OSPZeWnf0zVQWNVrlRRunJ7lBYudDYvyMwStAE/VynZ0fHtPgng==", "signatures": [{"sig": "MEQCIA25hqd/8MyQqnFwDCkLUdu8FJvG7eQaJvChFcNehL0gAiA3iQmbCuSJFcmA8FmjmokvWLdGo2ba93HXrqpg0VQDwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUbCRA9TVsSAnZWagAAdRkQAJ7GJQpfkhdjRd8T0G/P\nDlLaLXdzdCAcAWqkYiQCKX5k5lz1BPv/G6nTOVCn2WyEc5h+dQvZap50MwpX\neb10Nv9FenIAfSdcebp8d1fX9zbw4/L9L9avVtb5A9dKlDdoRdSQW2BMWz0b\n4HFzj9ka89WroAS9znMPMNxKLwGzxNEEud73ciAYMU0VvVASFgf5mNQU7RrZ\nx7ofkOHk1oyq9I1KAxQ2idO73FkOn2LrMqbykGXttA9mjYRBz40n8w4s2m5o\n9FONuJNzBUjDQDqO1nZiM4dIjBEuLTSmFtk9xeEB2RlgP8ykX3jfulo3+Kma\niTod5/Rsl+IHchurz1/ofhiwEUX6VRWT1UUEjNNcwAecedrO4wCZkctVx5Kq\nr4k8HGa8WminOFXVYxQ9RXX0tAK+hCrnHmSfOMH/pBxrxZ7B1VGtcfPfDGXZ\n1kwVMm+l77Cd9dwEWVUpcJ//0JiiCLrUUgY5gD5u83+BRF5uA5MI6XpiOD2o\niplvRzzKh7hfgKsxKOynOXqWR38S7ejQRsO2H5719ScJIl419D0BWfjg4VtD\nkCCVzl1kzvD5MtcqykpZ/pW+J8eqzJSAdv/ANe1K3l5mpFB8EZD3SMv6FyQo\nNhV7wwQFoURhfAeBrIHW+m3hoy582oBnLTbypNGUUezEtToQTV82D6x+2XW7\noxgr\r\n=Kg1/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.47_1526342938962_0.7033621977302797", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3d582d501f612782a6e70839264bd3af4454200f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-5SOXkuWm2DmLSFxygIkEqZlbnRF5CxWecS0izPhVShi3Nac8/1iQHZg6tk5oY4ZULOW4RmqPabkaaGg3zHjYFg==", "signatures": [{"sig": "MEUCIQCCeuQIDQ9dVno4FIcaNm4ezVbGrL1zyeLemeWsCq5BrAIgaXwuMr+yx6gH6Tly23vLmSP8wk1Jx4VK8oj6Fdol/9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxD0CRA9TVsSAnZWagAAA/gP/30T3UVrKbIjaVRRZKwF\nHzKF35fn0/kx6R9e4CZbAMVd5NOs67u6V87mVPZIj3wyW+S8udmAIjMDwps7\nxYRkPQ9fj3LLdzfh0GY2fW/kREHVlPMXq3YQ2LxoN/Ih7z49qu8AJFdSsJu2\ncIWAZug8S6w6Lzp679889IfMWXXIERzbxOacOoXEqb0mNVsk5ZkgbwIDrNC/\ndErZ6eeU2d5zAOHSDMDyc/fYaxfd4r4qwyhmAiDwEldlIZ+B3p2+GV+JIZ+3\ng3P9gT4T4cZlFiD4s30jK8SQ8L68wCamkr5o5v1PnaAP9mnEUesFmPfGw+lv\n5LyJpDqowFILJVvFMevlkASCTJnY4FDg4yGf8YytoFy1JA4umfLZmxEt7PgO\n94J5fYbufUnL3qYY8chgLSqNOX1CCB51jldRfkwI+vPNFF5w/qNGIsD9ctQ7\nwnixqlKWSllyz2f9O/M2DXjSsvzeYJfK/CZ66mFfjV6j5Qhha5ENH+Yu5sfD\nN0ASqGHQK+syOIwVCt+j8+68OqSFsJW8ot+ogaJQxv8kd/+EAybbMvslbh+e\n7ysuP8zRYTOMizdZ2i9n2wLNZofgB9jkQTAnpJHP1v1HW//HN7/AO6g+OCeK\nNDXdNid0iNX2ofHPPGChYk/1psyru81nWfBofBEtCd9WuTOKwh1vWwSnhNz/\n2goR\r\n=4UgN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.48_1527189747566_0.08343752503812807", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "07c838254d65e6867e86513eb0f22d5f26b0a56a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-HcxmXImP6WZmfRFunMCnp36VOMratENk1ZgErw4R9wp8lhp7pZlrzO6ca8clsaYgZhsf/FQ2SuAKg727dKluHw==", "signatures": [{"sig": "MEYCIQCXap5OBUZz5u884gErKS/7K5uFXMXHFE+AnNTZENIL6AIhAMQ24YRGHZWbUFGomvkk3uyux/XjN9gSFQgTf81fqbit", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDODCRA9TVsSAnZWagAA34QP/3Z1C5KaewcEh1bqFAny\nwgbqXioTXTNh8Bqmfa3J6lgc+cetEfmRjPM6Dwn+wT7vp8SEhUlpBlbE/4S9\n+TW5AIhy8NP6BLj4ZjKY1hDvVseBEU0W8OkhQZL1MeSOzs2QWECX7gi075/Z\ntzom1k/NSHBuXQNNcQwrHxQwJoafbXibZSaGZ6hnLqBGViOTpCiaWJhXHqf6\n9rz3+atV3ZSg5/hvzSkyZod88UrddqIZ0rXLo3AVXfvkjIyzSgrkn6siCHPc\nqk/SOIyrP/uCm2uByOWm69Lf6KIWybSHBkU+A4ZC+hkISznDQg4MmRyCUVxw\nRtKypiuMN6pBRe1eIlwVNa1gWAXLImnNmaYwLis6KA9AvscC2LyQPPLDxuOk\nDWQMyMFf9PUViodyeXdqAfUirWWOmbJqnvoU6P8Bx9ZlfO4B95rDUREhCcsp\ne4m8tKZDKpGTvWI8/CRsTwPugZv1H1bd+koNr1ArAQhj7MGQg3sEm701Pnq9\nKhcsRDBI1XCPhVGoSMgfMI2k8++3reld5OOzC0RV/5Nhy8rvTp7IHRjrQW62\nuGgtbN7nW5oT0oYiYV1FYXU354bSGYeIrd3Sn5vPn+9oBOoEShJND/VfIbhC\nreR86yOYmRono0VFaxs/HGU1h0029bY0uSh9D60KUCjT6zmlOZVAuHrXbh08\nDm1L\r\n=vjpT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "07c838254d65e6867e86513eb0f22d5f26b0a56a", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.49_1527264130775_0.9025863775267979", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f059bb756252edd0c2d44de59010b2ed88b40083", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-2Ox/OKDxGQVUEgg54+u9Xs/9Vn76LFjbQr7mUn0Yv53BLIso/VNPNPEfI+vfz2t3i2j/oGwWR75uOeOTTtlstQ==", "signatures": [{"sig": "MEUCIEmQqMRl9J0ZjTRBIK+1P4CZH+bteqBDhdjh/ud8TdsXAiEAoYqDOnzrY9XlWaAhiKyH+W3cwGPvvma30lAksRXYU2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1753}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.50_1528832837372_0.25095076840004893", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "45b07a94223cfa226701a79460b42b32df1dec05", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-HxDiLyEbdb+k4gPPpPnK1bP5yM3K5dA1Erx/W/TTMLR5konPco1UpnSu+QMi3NRfdPpeU61dXbP/QQ0Vxj4BCw==", "signatures": [{"sig": "MEUCIF4FrI7Oq6/Yu03fnZCzu+xUqY7YyvckP0XyNQNYlkQ3AiEA3xYIYuLLQaqCwePbQxslFP+AtAFeIq5bfWFsa3HEDw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1767}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.51_1528838390084_0.666687156716165", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6e9861a8698700dbe27b2eb9762c98cf51e8e76f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-LyWjloiHMhu12lk+ajhjXvDiD5XyEva3oPDVaU3MeCgse5dUSz4XfbVdh3lqwDIDPGISY4gVcxjXsBuCX41JOw==", "signatures": [{"sig": "MEQCIFauRwkdUUtA4ZQICblnyi1rpj+6GH0DZ+jNmNqj7S1GAiA93KHE6yhj9wc72pi32XVl9eztcDW49bSr+hWuSentvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.52_1530838765615_0.36303312270344534", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bec4f144e9a96ef5121d1430c7ebe5fd088657c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-1bWy5iRSQngH9klvojOdMotFH9PWY6aRDWSiHddIsc54VQrKz9NH6bBAwhf+2Jt+SJfCUbAaGuleeZkfMPZ7fg==", "signatures": [{"sig": "MEUCIC113k0kJT5FtMLPG5MacVvJy3a0Na0KyG2BGsLlPeyiAiEAr2EfGwL8O35KvGb/p/m3Z/6/FcudLVpwulwKdKIwYrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.53_1531316415660_0.5193319021858833", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "70f07ecc2f3b7bc9f542a578e82eec18a5504098", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-ShGy07AxPdfx4a8IHntNZcJgZOgSdXw2z1WEDlrkeUeOHkN3YwAohqxDEPPN8E4vI062D0JIJ22u9bHRvyhitg==", "signatures": [{"sig": "MEYCIQCweHcp818FVBe9uTjG0l6jPHgrblwf2oWygWZJUYi7/QIhAK+9mBxkFr6ykMb5SGqX4QAMaZ4ufdGAwp65kmHTCxs0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.54_1531764006696_0.06726994740423264", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8bc92cd24e6419301ef3867e4667b77aa6374e11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-N33MJ3DltoYhZInuDPbJlFi71QNhAoXCHUio1imt6U+onQick/EXStEqZ/IpIqRAMpAHE+tHW1r/tLd0A82H1w==", "signatures": [{"sig": "MEYCIQDDLwSNTtb4QnGacy51bxOX2/5gYtCs4OUfgOrByLzy+gIhANg3Abh+W6Edej3CPGeHk6s2+8ZcKyZkP4uljGGYF9QP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.55_1532815637342_0.032123170193682826", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5f6d3d0dad64c52df64fe8654a8e6d6fd884d60f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-uz7Hcui2qmf1fA8pl5CsLz8KjM3HuUbEws/59G9kaMOrSIMrGSfeN1zsthfFSJDpFQLwq5NZ0+lPIvuOwE61bA==", "signatures": [{"sig": "MEUCIQCz4OmYqHonNvamUw9QAqgWui1upkzLR5xIGoUddv6JtwIgMzC0QNrClwX0BYeWGoD0z+X4d3nFMHoUkdQ9PAflz/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPvnCRA9TVsSAnZWagAAmG8P/33pXHZ1XCzwiafu3Aoa\ni+UsKlQNI8+x2EO9q4DOzErEt3KlO3Fcc4WaumP6f4QHKGUuZrToLDYtG+vu\niyDe2Y7vA5HLf0bEy06eyeFqybusS469fm91y4ZvQ2QZwsWwfySsMMkaq2a1\n5wYNerr/CmRXhvEF+bWFizJ6foiv2lRZ5zNaWHyLTOyvgLcMBLu8+AO1FcQ1\nW9J/LxCa/fPkfOLaEgMfmwYRDSy3MrSAe/MmqSt2vSPm1U0AEyUvZRd4xamq\nQtkur/u9qnO/mOliCUQWCGxnFeHRnwT8/OJiGkwqP0EHP5NSyMXF6BQ/SroZ\nSWzs9GZ7OyrFsWXH5oNVe0HWHv2EJShFHtTrflfoTnXyurEOoWjNIWgMei4Z\nzn2WHFXrjLAzj6w7wkSeZjDbGz0ha8yueYOf8NvBgV3w4s9RPPIIlCVf9FA5\n+UCoLawVMT7R0rEJC+wUiRF1sarClZnUCCSerUEJwCUZWe2jsyAG6GDRQ/nG\nfBXMtZmP6axntqENKMvCNKyiUMhMEtJi19VX/madno4us+0eKydQWa4nWaLn\nfZDOPI0sCVmqUFhBP4Q1fOXyPdTvIVBxG8nT1uk3iVmGl0KQWrFCWZo5OJz7\nnzNvT1h8SrW7dxSV4HH7hxSWd+kyohqo3u9eOzLc32ZXTmpVRM1kjSN1ywXN\na0qH\r\n=I+Km\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-beta.56_1533344742991_0.9886615609317828", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3703728e0d1e4943132f9b866d172246e2e592f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-LjxBczuTlXMkRHmGPp+lof+qnNmnalI+TknBFZ9AYQKgvRKnM9K8Sl9K17bzXgVv2pP4ptkCzIwq2ZSsjfDQwA==", "signatures": [{"sig": "MEQCIDRAITqxzDZXOtcK+E0Blk7LoHFrETFeu4WPLQJazWmJAiBNiaE7IDRZ+SlP6FZPTw0en4pR+ciKpGfh44LkF+4/Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGScCRA9TVsSAnZWagAAD28P/jjbQDsPzaLf1VaMyhyJ\ni0y4Y82uHcCLWirmbkwYz8uDtZ67DWftwIMCFV690ca0eRtlJKGgC5BLtbMJ\nLpLv42GEVXUquO6Jg6OUedCGmHN2TCvv6oeSfHXKRZxkqSp4PkP+UCu4IpFw\neO8LxkfO7FS+e3Ic1t2KMHVI5JJBo8H8esno/Z0fWT9nyNdxNyNUjUOAyg/v\ntSaT3ZO5SlP/EdVomcqn+QLNf1pdV6OGzviPRRGLAHsgMubPYBFmPr0NvQCo\nyANnB8yGIk/F50hNEJnYEoLFgAwG3h1nhi1pj3cacgRmW74EKNkNQhxYJHzz\nvim43SYfhfD+KigKrZ5pB3JW/VDzz6wr1Z4K0oGuY5gL1HnJFeJaqCM5RjAi\nAwTXToA0FD+wIdxM9OhDeswXYNtSPNX/3qI5YpXXYHW8Fr8P6PYdajh/nuSx\nOihNomfGEOTp1X+9uLZfPm/zNa8aBNWPq2in8qD9xsdzcaHprxdF385+7Dyc\nFXFHDb5yms/PMlWiabwDbOO3CfmH7PyjfE25QeQ35dixhNa9YdDW+xn0fFUh\nn5s3e7anh+DlVfM3YP8pF5z8pB7Z1YNw0GcdwUtZsdxniQo+R0aY8ypxuLTC\nS1bBAfmOE+hOScLkgdiDI479sEwvzhiNLGFk1ySiXJ3TZGM2ObG25FL5Tzp4\n3edq\r\n=DaK0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-rc.0_1533830299633_0.8357062616541395", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "314e118e99574ab5292aea92136c26e3dc8c4abb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-iI468X7shsmB/oIPi8+UfMcOpcQPEsMAz5hDc0H8dKBGUWbPcAlyQpC8CaNDZ7y1/7lK65wtvXs5OGTQd3OsJg==", "signatures": [{"sig": "MEUCICBDlLkawVUI9ATlQIQ8R/TM8YCKsD5Zbj0EcraR2osDAiEA4eks4OGxWP2Qu7LcWrGxb4/HyKXuyXoaXTESySjRGg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8fCRA9TVsSAnZWagAAzkcP/2b8bqxnSK9XWD/bhHNp\nwcMdZSmDw7Ytumk3TDfh0KY+jZOA75uM3nJIKofc/9pieflB0NtDEJ/yC9Uv\nXVwkikXo4X8JyVQ+osP4CUIqt2bEOIX/tb4HDbTvVY8BdDoYqPEhmrutVkr7\na4Q9ss+ndlxlc0m5GasT+Z1Ieqq4mIm6WZlBl6zy8JdDip+blA5RXS5IBu9j\nhbeQ/00kZxK1xMVrkxlxMbMiS6n2BIcvx0rkhmPmomimezHT5N6HRfarjzsP\nUdcvqiZUMOLbonv9ru997xR7Ao8svCBgNWdqIELO893Sg7F7nlzg5mqQvkDz\nw3a0LWP2OnfRgGemh51AGUA9mP2q4EvXMfHEmDrtLQqsY0p9SsO+rGkTGMvN\nEjD9Dx6msrCwPfzUtseJ/TEQyNIczxjDuH+IA0KXIHeScXDMcKFGuIKg/ENV\nl8OKYPJ7DikO32nCeFl6By3vgVfoJ46CwHetLpxQnwemvKVvcY0t3au9cGKe\nxjc+/C7KefknVqwfUMaa9ihOxkX6ng962mCap0nhoJfwwsNljFK+1EdSt+cG\ncowwhjd7yqQMBs6zUwJMWdzk2P/oZ/3nQGaLZvMve+CUXrxUv7T7wmdgJlm0\nMknIRWtyR7XRntfJiRe/Nt4lanBWxEz1g9v4QemsMuEGJhh791LLyx1Gxd7J\nzv6w\r\n=k2to\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-rc.1_1533845278751_0.32944008357687626", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4475d70d91c7dbed6c4ee280b3b1bfcd8221324", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-lW0hoPqS4WhYgSQ0ifNk1tHn+e4OateqWXaM7BW7wZEU0dtP+RQ0x4z5Xghc7u82ZA4IwPN7mS1i201I7eT+dw==", "signatures": [{"sig": "MEUCIQC0Y23XJAHtdlY+N4hM+b1Ax5LNUgFRHr+rOV7Dnwh34QIgDBYTsCrOQoidhRoZEiO3kRyd8uAa1lYcUb3WXxZt+Qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbKCRA9TVsSAnZWagAAdRAP/2mMw4KOGzcrmHWDIJKJ\nLRuISkwMxkcI06VuESRgY0eWGTIPVysWjJAgOdCY4Zxd9QWkDmkYczcCNN8t\nh5snF6ID8V6vtboC6JKylHqtV7me9/WzLGbZJCMQ+3/5ow80FJJ8aDwlq0Wu\nXf8QK+co9RyQCOEUrUC0IIJFHVBXb+v/62qvdju5Zav0tCGoqVyNHxBSuoL5\nQaKBXVHjYezksYE09xKcqLyK3qPzJ0GjEMPAQiWC/0RWoJIkrnOe/Ez7XByW\nFajH5uY1WWd6cpd6V1VIfK7PJUpG09qFqBqALqMAZURzfuHfPKmmZ50iNbE6\nAi/Lr1QWGJ7YfzIeHtFLo7ywH3MpcKd8xAZWCSPSRJf1cBvPkbGEM8zZFmy7\nIEdCOtdMrLlFVOPskHru5d9RbM8bjK15LNjBShJGFXe6wyn2b9UMbo85kqvE\nC8E3MksCpK1kBM336hJ+RnkLRvlByvqcSpU/rTRk7uyZxDJqGde+pDyP9rBI\n9FfhrbX0YQ0gWk9XCqcKpo/i8pIvzewRo98lcvzdjL3bZcUhM1h1BCH6h2Y6\n+HkTS11cIDzDL9B+AKyLZM6TrAbQyuOJ6nvQD9Xyaq/18J2Aos7JIS88Invf\nS6HTg+2DIQ5D6Q3dsMhOaQHwGdFipKSRgd/GxPz8aYDMe6fC7S3J6V0bNFOH\ndbyt\r\n=396+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-rc.2_1534879433606_0.5092060062758519", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ec4df668a5065935aa80327ffc8265570361f8de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-H5neMOdnLN0Ho7pz4jg5gpDApDma/w5aY8HDvQcljdFqxlCwEAcxb7Fh5fFJtViKbT6V09hdD6g8FeU45osyVg==", "signatures": [{"sig": "MEYCIQDv0YGe9AUs0u7a+z4kFX+X43EqSGFzf2Ok9a+xr5OCdgIhALGdQJXN+RQnvmmgW172XFsG869+BGtrRfTRlRuJVl+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmACRA9TVsSAnZWagAAttAP/AnoBEKKOwe7vAdugDl6\n3y5MpsMa1oNvNo6wSb+xlXe4nmcZTeHd8zi2LoLbGQloIvhhr6h33ezfVTJR\n7nWtDd5QdEc9YWgJ2chLxIr0xrhxlZCJUXifPdKCZ0GO4W4ida+eTGQxSEWm\nwfkjGkbMeTaVe2dW3TC3SyB5OubGlKBGmZ1yWj6JuFAT4ONNvnwQotJU0h+P\ncWwHKqIyGzq9tNJ5uYZhulKAxSHwlPhzCrtbR0H111S+FHnCx0jkD6RYQ4ZD\ndhPyZ6rqtFAypnrrLiFMnyLZBnITrEowx7IU2Hhy1+oEwrlQoGFS6S5wxzsP\nUZvx/eo+Hw9XbMuGR1DTUVzqrgxemfUO1nrNZqFqZ0ULZTYyfg9TEHO1qyxX\nTfCCbGfKRUO1Uw+/2eJ5dRvv9kYbVyfLSTU624UXiZntXfUaPaBW3ph21+Q+\nxShCCkVFXzsvFm+Y9DWues9uwZ7ssOIGGKJfsk20fG/IhWWh3QnG9s833fGU\nD4l6rGRvlaADQi/b/nOQIfKMprXrf4p+0oRF/ycsdQKKo/1TWc+ClZbmM92o\n63db1lxFt7D7OXM/R9kxVm2k7tAN8uh5hWdw8DAGjzMeGF35AelS31wufePa\nNZdW8a4vXCTVqM/kqDrm2jQDMI+v8oHYUcj4B8gSjc6xY6UPogadaN+Q4oRe\nIoWA\r\n=7ebX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-rc.3_1535134079827_0.012249409050186122", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-literals", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d75e69901a0ddfbd8b5cc4fac6f2b025c8f517d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-umyI+/phV28VAiIICKsVSoY+HR1QwOxBlnJ9BRhNkF53OSPWDcBFv+yS9PhRWdjOyn8S3YcPAVKvhEdhR2pavA==", "signatures": [{"sig": "MEUCIFl4TmTDlmOo6GTP2+GEIhyLmKDcCxt6gd0BbbyFbTSWAiEA4kYBiV0HyFHbBnyVhI4UccstIuQd4iJtDaXO57mIIyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpfCRA9TVsSAnZWagAABqkQAKPugAznkKu8i8g/Nehg\ne5AfTbL8+zE3yMSgcb/9VNPyLq15HTFc3MQBbuuTXAhPRKyOGsLQ+ikZOUwY\nZr1RLpUzrXpkI0Ljt+hmjXDwagpGnj0TFowRw8O6kmE3fRDmko/B5zBfqYa+\nvThKzr7sjah5vgZH7wjs1REDYWB3mAdUoEJp/HvzVn/5S+molK2IdvxIS1is\nBfbpqCJUPXeJjyKLqFh25y7YXN1yes/1TV0Q3UviKUQHfV+kw3pAkfEummbN\nYxBxAH0Sk0zuzUnjp857ta+xVvFbdzihAjJHt9md/k6CIZCcPiqJuI2Y8MBC\nFszkRacxy/eEo2fAG6HMq0+Os1ArbknRejW5h6HQ1wxKOnYX93EZ/JYviffO\n6IeKodivlRdW1mIW55NldFKYnA3vpYmtij3QpHSfBrfCLnnhUyZzDtNRcsqk\nYjD29KntkWNxCkEJjo5pQod1lzuYkRmPtQzT19cdd8pbNxcW4BmEkPCCSnbI\n9LKEquB5YkJnLFK8fOhOllMXjwAgKdBEVpzxwzH998/cKyfAJEYQaFJ7J2sR\nUtumvaEFmuYZDm7uD+rm5CBymLS9x9QdGF8Kchp6XlX9S4hXqORPS6kZg5UZ\nEgRBtcAiGZJhUYvtiuuOvTX6bNIjzWe//a4okyEM1ipH3Fn666hwPClzxdt1\nG8tY\r\n=Eb/Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0-rc.4_1535388255036_0.8485480919773392", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-literals", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2aec1d29cdd24c407359c930cdd89e914ee8ff86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-1NTDBWkeNXgpUcyoVFxbr9hS57EpZYXpje92zv0SUzjdu3enaRwF/l3cmyRnXLtIdyJASyiS6PtybK+CgKf7jA==", "signatures": [{"sig": "MEQCIDLzUjBOzHDqfTPjfoiDnfI3tAavRrj39pqNGB01uDWhAiAeDHAPlccne7tA3aDn9cUZWFhanOVfr4szm6nR0PFkCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBuCRA9TVsSAnZWagAA17QP/RgGt/UIGWm5UEq+nV9w\n8GxDKA67c53yVk8CNsiAeKmmerEXmuVCz8WAXl5ZoTG2w4qjLdMbvOfqzYD3\nB6v+luwRhL2G9STbYJcUo1J+E5moqrkurMmxYExDm4RotTK7O3GwnUseWXKq\nBZ5itXNERIX+43dv1LJ+zEMx3RY4EfHnxFV59iSUbHnNH5227k6cXCpuhJJw\nNKaDA4Bhlrn6usG8lVurQiW7hJMor265FShXNZt5YD7YryC0kNLB5Gi6YhCh\n77ZqBD5ruXyS3wXEsDksprArwcslA+8AkMuYf4d0xZGBLsdoEH7JK4UPOm/m\nL5McmYqzLvnO4ftu/e6IPcXlxTHePBiDqTmvHpH9Vr+bjVK1fsHIWTk0DLvt\njdy+juEiYgenBfxcwpaHk+rlthyG3KWfK7gEju5Txi1PQxe9V6BK4gWnRUbJ\nD95IwVcv8L0b3ihQF6d5xFI/cBFNZCUByAzFBD0XBxRkyw0UeWiCDq+cl16W\n06VL0for9co81NIawIsvZMWeyM7sV3DhtgiKuDUNrDat/kRV+bEGLI/BDgMD\nhkf0Q1RskOPJ3HMWctHm7RU8mx058/DgW3QEydBrx+uV9VgS03aPUpms32Vm\n4guyt/oOPiH27OXPaKYauiPerAZlZHpiAa5oAOxxzp8aVQGv3SuRMkNOkTR9\nHca6\r\n=eAZx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.0.0_1535406190315_0.9816401427865937", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-literals", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "690353e81f9267dad4fd8cfd77eafa86aba53ea1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-2ThDhm4lI4oV7fVQ6pNNK+sx+c/GM5/SaML0w/r4ZB7sAneD/piDJtwdKlNckXeyGK7wlwg2E2w33C/Hh+VFCg==", "signatures": [{"sig": "MEUCIQChMC9QALsFpaI4bw8Vay9sp4opeEmc9Kvq1WMnkn5dDgIgGhuhI1AJ4GdHsC9eYoptHHOeR3I4Kpw452B2eBKivww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2GCRA9TVsSAnZWagAAK9MP/iNBj8YFXZzhB8Kq2nd3\nDfGr6Khw3HscL3HM7frx+6liCfimJqgKNxxm4mMUegQT5TLAg/N7lpjbBI6t\nWZA2SCrO4c0jUYvYeG5oEvwCG35H1Folq+ak4xAYFZ/GjpO6k8Jd776rKqbk\nSnisVe9esScvG8/KARZnIW/4oaKzVHeKt3xYIoetk3OmEWZGGTdVyiuVkmMx\n9nik1bY1WV6S4m/mGjYZ/Qex87ot7rmu913Uybegzf2SS/CUwhRpCjhQxJPJ\nlktu3hS0UBoGj27+3kKa6OuouBmPHToDKjSF0VZLJ+aniC7XfFkFdBh2/Lea\nGXkoPP780E05+4c0tanuSPDX841ObtjT2WQGZ3vovJVAf18MBbup1dT108cf\nv533+X2DtB6SGc75H7v8Ljw3UXtfQciEaWe2A0VXWD47XQBqXWh/4ShqEbF8\n3MwcoSA4qz3t+aIzyr45Etd72XT03NHp9zKEVYwvMSC6R71BP73ckFleQ0z4\nYyryhH7z/x6d7pwRASNX4eSl58q6aT9+Lb2IaGKgxztm5awhYntWdb/7rCtp\nNAfPuvBJAHwXpvpOgLmiFHdB0joK8FWAiXVOZGMAU5AJhdY15Xia61Q47gBR\nkoBnDj6qDRDfH48IfKeqiwldrDJvof5LBtv4JaYN5Nj0eLx0eTsUVF1V4FR6\nL6lr\r\n=UP/J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.2.0_1543863685509_0.4281042504358339", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-literals", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "27fe87d2b5017a2a5a34d1c41a6b9f6a6262643e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-X2MSV7LfJFm4aZfxd0yLVFrEXAgPqYoDG53Br/tCKiKYfX0MjVjQeWPIhPHHsCqzwQANq+FLN786fF5rgLS+gw==", "signatures": [{"sig": "MEUCIFFaqrt4J6uV4s++Qa64Tb2cVdHq4HXaGXpDzxKEX6AeAiEAsh0po0W2aCF2wcv8OG3/1NGs/gTVqWRyZE3BmBj+ogY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/8CRA9TVsSAnZWagAAA90P/igG/Jc+sIhimt0BlxqU\ngtVo4OfmUUfzE2ZU73OgYvIWKF5gaayvy2Oa2MAUFULMi2DL3JgEeMwc77YU\nX8OV7SmHVvdisAGj7H62PhmV2TU+TBLsQhXATIGUYqi3m6HF4YNVcQ3v4Mwy\nAXqvsWD6xjA56nRwIJxepLjh9CCcyFDfTgCev4G53BavFWyn2O2XqyHVyYhe\n7MrcUuY4sipu+mK0dB0wjieqcc/Uq0uggWJltXrSvFt1yz76mh1k3P4rhWUx\nOzx4syCb+bOFy6F4yBVN2NCF/P/0BI3d956SPj2S3oeb3+w2KBlrGimSemJE\nSsyqe1b/PImsc9gGUAmyXnUox240yjggc2T1ar3YrhgeQ7sfjVHBkS/kB1IO\npbmlEvKIFS8vBri4LNnkpi6J0VNh7XYIZcNpkEZqdm++5GHgkpLnZxPHzLZV\nD/Skdmfo8FDcZP+tC7LMhT7i9I8XYsas0RQSzQJ5itCN/qfRkE5kt6+xRKZ6\nL2EXORJLWFtrsAvgFTvxNw4X6FYuJfxoXWXmGCdkocw2yWgeB455tnGEHsCs\nuo0NLfd92eKGHokgcGshu6rjLhqcAptF8jlM3EXPwu3qSRWMbO63teGHMv+Z\nYXf8geIMVDm3juCCr9OL4FHL1MAA1bJ5kcV57wTmDsL8/VQXtlUJiy35gEeX\nnfyG\r\n=bgWo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.7.4_1574465531980_0.19436262823100803", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-literals", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bda7a4773293ee9b687174eb4e1f91fe37ed576f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-7UDPKG+uVltsZt98Hw+rMbLg772r8fQC6YJ2fNDckcpAXgIWqQbMCmCpfYo0hBNhdhqocM73auk4P/zziQshQw==", "signatures": [{"sig": "MEQCIHiPmUKohkIcUpiLblnepRk9fm8QlHPQQWzdv2PahBEjAiBJnT3QUM4yb3l3dEPDm+5VhGXXulOIkKhdMdCCLfPbbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2882, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVgCRA9TVsSAnZWagAAXRAP/1nahSTcCwEJl7nhFnk4\n37f3mz0//Fr+A24jG3WaIjK55g9ojC403+bsgXiQSQVYWLCLz8Mr4iiarolU\noCw57NC+hyzzWCcQ0pL6KDedc4YANNorSSdQaJs0RNZGAGSeLR1v7YgPibp5\n/hFkFqQcwZM2SNKM4FJ+shRUXE8LIZRDE9y64UgaPrbxWMNJr4R2nwRq9hIN\np3oOOV8QtYkNjg0SoOziXsJpzvGwL4rdlGGz9LA316RvDlCGiW2XkqUeF3EU\ntJKrf7Bg3IPlScVpUqH46RFzFA6I6cPL7bxy0J6/xWWdxy6eWLuBVAY5hqdl\nqt/IGCtt0XDfzgNGdNg9eurBOruvU1WsLlWg7+GzmhC5gmDy20JsXo6upmV6\n9E9u5m13ObT4XfbVWCH/aCnYdm0IUEOt1NLvYJ8hy/sjUlmZDKRAugxc7Sip\ndA9m0zB6WmQ+pZ2TVMAyXr0EopK3PWcU5e19D9x1sytdHAZxmrEUYduR5PUz\nQDoqh2czhaUN8q6AqAArDtR28/wIardjv1t0Ujt83+3sknGil4vWZSFQbAyh\n9Cb66vAG5c6BzdcUOSgShPloQFFKMHgoEQV6+lLr0xsgyEec+TWav6i2L7ME\nkZeaY+LfBEkVlWUqifxO2anxEd85MosbgjKKW5ngcDOPbDx249lBsvqj2L6X\nPJnA\r\n=tAH+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.8.0_1578788192253_0.03125669555237165", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-literals", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aef239823d91994ec7b68e55193525d76dbd5dc1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-3Tqf8JJ/qB7TeldGl+TT55+uQei9JfYaregDcEAyBZ7akutriFrt6C/wLYIer6OYhleVQvH/ntEhjE/xMmy10A==", "signatures": [{"sig": "MEUCIQDNhVAwF4G57ggsBG94+uhTKaxuz7TjRJL6MGtfSROuAAIgZ9zHDWBqVrkLVr4z6aVCFoow/iB3aNYaPc2Bln3UxCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQOCRA9TVsSAnZWagAA8gsP/ihvvppDDVHWi3YKYAmg\nuyOfZVLcGAszFAHQiUPRL/yEDzxmyxzR9hsLE8RQ/J5dcOEzsa00SQQbasEd\n4jiBYwA5gZahtT3L3DtF87meWUOtkMPsJ47+NVzl5GtQlHg5711cL3c22eG+\nL8ZMl4x6y2a/QzrxlCeELpv9ZoPCo5G3GGoxR6lyXPDTgxuHNzEoVrNiMn0m\ntBwO4dOncHa95iTdEQI8Oex5HIAvUwUIZgT1tg4ThxC0nQY2GMeeLYLJ4r/W\nhqsbPEFK0iLRDY1VZN3P2C3MUQltqrbP5Og2Ye5Jd01Po7lXoMt6Ssr1W6LN\nkeNFmO0QwymQAyv1K6A3AnhDtifwaXx1uV/1CmPuShoABkjJA0A3eUQr6ars\n/i5mG+rp6d9iC5F/H6mPTHHdmhjf3TFSExvCZ7XvCzhvkA42giWU2m5/H3LJ\nwFJ7JIKoz2Y8O9njZKOlzIA55h3Vqy7tzle8ZGPPtaxbg3ppmXbv5hn2bswg\nTQnD3aa/iPvEepB35f122McF2LdYgd2ODFakkRezy3yjR56VxLDN3uC8h/fg\nI//Hk0zx2vumO7sOcqG9ELLBV0WyBrUdcTZVWLsy643v3MOrddHLeyq5hkAG\niqqH/YeZChw7H7CsHEWF2WfVPkd58meaqlu192BKUGnN0IEqfkB1uh9jxeLC\ng6AD\r\n=ZYVN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-literals", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.8.3_1578951693857_0.8855426810867251", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-literals", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5794f8da82846b22e4e6631ea1658bce708eb46a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-qi0+5qgevz1NHLZroObRm5A+8JJtibb7vdcPQF1KQE12+Y/xxl8coJ+TpPW9iRq+Mhw/NKLjm+5SHtAHCC7lAw==", "signatures": [{"sig": "MEUCIBjybU2kLaI5GAWEsYjEzSEfbFMVeaT3CsfcjD6pIttlAiEAyNGiwSoPhcTXVinnjR4Hgwn30eiPlxdOls3Y3fjZGEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSYCRA9TVsSAnZWagAAxFoP/RXpRX/NWMtqsShnCkJB\nTVVz1SwLJznBNJoUlcFNUMwmlnqMUX4BUcmBMLXqYSFA0GpNa/8DHrei0E1m\n9dLKwZceQljRAKMWzwqyonfq4rJ3XfXsTINPbIlNXERcIDSvKvusNFkA11R6\nVxNVt7jx166Hj6LUVYkBty75pTDDlODo+fnEcTAlbS9jh7A++EWoFhqRJfIU\nWjXuBlt4OPrwmqxfFDIE06oYX6LrPFFj5fvL5acpeN7RpuEJw52yDZ/fyMNp\nYamEA9M1KVdxoSYl/41sNAaeKRAx8YDO0cbVPsZ9CErI1frcyfGh7W+MhjEw\n09sswqcPFG9dh713J7T1Cyl3n8BmO9RiiUU5loldpnO/xNvJC3giL0lSWvo0\nV1+kdKM4/XGXWy1ijO9y++JkMOuDsNdpIYSCnLaUXvLxYxVtrZZoGvTSxX9T\ni1KBe5KwMfINt4as0nA8gtoifOJHiBp+eQrVYRGnYYe+BKklRhvoN6qJnK6M\nQhrVyHzI6zZXVnu0AwByF6Kau90ldL7HX4pF+rVFVW84iRuqAFYaVfzcG+LQ\niZuIf9pg7OR59To/w0qAiW+KS8rvRDPJ+RxbLkbOiSSWp5CXHC0elL4Q0zrG\nx/7vvoPJMgoOIADBZPrKiPmiMQJQqUv1vpw2pn6lqojU9SiH0jmkaSKVG8kb\nvkUS\r\n=ZdIE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.10.1_1590617240528_0.7713044412924539", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-literals", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9f42ba0841100a135f22712d0e391c462f571f3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-Xd/dFSTEVuUWnyZiMu76/InZxLTYilOSr1UlHV+p115Z/Le2Fi1KXkJUYz0b42DfndostYlPub3m8ZTQlMaiqQ==", "signatures": [{"sig": "MEUCIQDVhV1R3xOvC6Qbi1e0TfdkJmqi6wk6pzpHKrUFj7AYWQIgQWY99D9PN7gll6ZpqDjKvT3NF0/nNq3T9oO6o3n0vc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoiCRA9TVsSAnZWagAAP3kQAI0vwZj47V69iNyYc56v\nwPRxhpuQNpx9guE8jtCQpXLZ44iiEmcNiMoXh0mVo8h2JDcl/yEDqEo6PPUq\nlLO0+MQ1/r3H81HrMImPxxpZYc6TE/QB0iYSDtt8Z7/OoLWQAMQhnrYZ7npX\n941cw1bknUwG/Nn8IZoW5T4Qq8LDxbo8BJeBf612Ui3nwuTuhfu1gZyTMmEU\noqmUdKibrf+LHsUBonHUWLcFRKNl2JysQ3MzPcFwDFLOqabrdNmI3vHOfrtc\neHkr53cdBnDkqUilr+AJv6nPXzHJOaXTHPegmAqD1Kr2K4H8f2mvO8TfoMsy\nz68nuO3M3boA6GODc5VUhKhFR8/1RRjUW5rvDxUt8Gsl5fpWZWqA3K5Ol7KM\nt7xKcAjUIzUKOdxthKrgA16gKfs4kETrPfPHviz1wy0q5vjz3BB8nda/39Yw\n90lQ2aGAnMX684mbmaPoxPbeFXibkIXTzI6WhusPgN6l49MUojKTidSX7mKS\nOg4EkoWT86XYiZLlo81rHnJWK4TPkO6rvpvnoIMToDzDgRUsu/RQqBiyOXPJ\nMWEszBO4VshrlgVco1ps09UkFguItyHp9C/H0k8KOjmmR8OV9GyHASAkfaUb\nXb1J0I1KFlPI2khcJLzgiKmJFY0tyJDm2OXZOBVK5BkkE4/H2TVMjufavFQI\nzExL\r\n=lhc8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.10.4_1593522722489_0.958420083113007", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-literals", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "d73b803a26b37017ddf9d3bb8f4dc58bfb806f57", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-+PxVGA+2Ag6uGgL0A5f+9rklOnnMccwEBzwYFL3EUaKuiyVnUipyXncFcfjSkbimLrODoqki1U9XxZzTvfN7IQ==", "signatures": [{"sig": "MEYCIQCm3ujXxpQtmXtWR1dTbm9oGVgKxM2/rC5d04DSmYbw5wIhAPCTgVqF+7GG4gj+j01LylHLlpX73eFYTV1apDTZRWVI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2853, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/LCRA9TVsSAnZWagAAag0P/j6Fim5//R+CLlNaGQKt\nyLXnuVHFGMieizOluqhO8XplORvOGWNbOCZB4Ppegeu4/DZrdiClZpp0RGI2\nVpHzuony9wtj3bPddEiPh54hUjhqXqK7M3DFKeflxcxSs31V2BrVDVP85TFV\ndFqR4odpHn3fGUU4CxMUT0enL5ZXkWQ/ulHLNWCXsBXMe7j/+OHS7na2mNKQ\n7Q272dmiDT8vhxZMCMyi39jcDh1k7VX0xvbd6XMS5MzUh1YIl1Kz8RWD/NQJ\nmyrDoEuwrvj7U/YoZSAUwvYSxxqL0n32ptEkbImc60EEeXOVNcSOCdFdeJk8\nk/q8a2RdQMa2yO1rvEYF1KUX2dvRnxrIzvVCNQ0hHgO5WPxlyANs2A9qfisd\nR55d5Fq3qSyjvEn4KgvbdMjb9+oLAfLDeWSxT4LxfGP3I2lEU4mfdcE0I1Qq\nMG6BGSvhNUmYRS5iUZ1+ayJCdxBCx0x4LnvSoZI7o9AKclvhhf478p6/zdJO\ndEaQagg387NtPWMfyK+FA0lI/EVmwbMHdQuP45hp2zhRkuJ4fV21/BmXAIb2\nFrnaP+w7UD+VTG4fUNq46Cdvkkj1zuvLrwzsoFkqDhpboSNm41RT11xboRR6\nm7sXcBy/Q6RWntHQyDcGElE+msyfNhc6p0UpfCSiXJ+ciMcvMvjgnDv3jtb5\nZBUa\r\n=1ghW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.12.1_1602801610823_0.9749524364774893", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-literals", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-literals@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "2ca45bafe4a820197cf315794a4d26560fe4bdb9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-FW+WPjSR7hiUxMcKqyNjP05tQ2kmBCdpEpZHY1ARm96tGQCCBvXKnpjILtDplUnJ/eHZ0lALLM+d2lMFSpYJrQ==", "signatures": [{"sig": "MEQCIHaGUM8GISNSmOFjuJiGZboNlUe33/SwjewvJsK7MS1VAiBVrlO54YixkFNiVl2/GjAtTVwa0Sz5k2980H5452htlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgfCRA9TVsSAnZWagAA2RoP/jWd6LKt6Iphn6FkLvr3\nu457MGci97xxK4cn7HVOsHO9S3OnVwyQI4pC3eozwsVOlAyTj8zitkZD9JrE\nZwAImOcdbXReRoXNYXnixTpgOZ7QSWTN+k9zG+O574FhO4sSteUD7fqsXfC/\n9cDxjHzFPJ/QBTuI0nGATmhniWDXi7V7glQQcCiNYGTfWDaAeB7qQb2le/wf\nSUTesMpdXzjAgOA27ZOUtervov6UKfLRe/0K1kaxJ4xYXHsW0NXD/nQC2Bz4\nBFu1ZcavAmKNtKD23NEYAE4uGrNLuIBbfwzewMzinS0V/acvkfk8lOfH7VwR\nlXiA5m90jvRHwuWYlvr1Ey8p+B/xkdxBsa9NJFkQ1gCVk/v9FILOBCpkdMfI\nAWUva12o24HaXkXkf9dQtn5S1W1aObEV8bJmKtbjmHY+RM1GznodFZZ0NpVz\nGqaWApipCXk+vnlQUHUJREattfhfaCzHFU3mfaCuYZsQy44FQTAmpxt5b/+h\naDldNCYw+1/CeW3EExPrJG1eYc02wBS3vG4Padl3PFrhphLEOkVpHSKLfPLu\nt3JDeSFKVmFBmczbGunHIr/ULJXubb1RtpAtYKZvvNv1E+IcQo9UyjL1sWp7\nsOvNfeThwPifSAQ//tO2WRt44gr4LoPyZ3if5rWLIRvI/pDjR6fxQDoAVK1k\nz4pv\r\n=TDf0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.12.13_1612314654816_0.5469088762214593", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-literals", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "41d06c7ff5d4d09e3cf4587bd3ecf3930c730f78", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-ql33+epql2F49bi8aHXxvLURHkxJbSmMKl9J5yHqg4PLtdE6Uc48CH1GS6TQvZ86eoB/ApZXwm7jlA+B3kra7A==", "signatures": [{"sig": "MEUCIQCCG+s67QBfw51RcnH4jgKe5LvKsntAQrNIaZ2w/0VlzQIgJ+T1H8wjvTBEvwbS0KJ155kebw3AY3kfLajcXrcoErQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrDCRA9TVsSAnZWagAAeuQP/jQun1osU4t8F6ZTxVyh\nSWmNd3UVoEtIWDSdWpDOdSSwOHYvl+gvYuh3Pq6DIvhnw8LUUTgD6SccK1ls\nTiZiiwbJClT3eJx0IYNYM2XDvDgFzyi5Z2KNusfM6u97jDFerOzV1Q4ozC3r\nlmZDJrZRqlFXPc8RhhY7+IRQ0hcC2tk/lXbKvm3rBuL5AZpkKmbDsE+Kg2G6\nRa0TWb4Ap/hgmKldNTZsJpaPnzAtXceJsNBULH+ZRbw91qhnarTOQIvwNGIA\nHRwoDOMAtrKoH2mdS2s85KrWwMsQb5TQnZjlp+/MfWFCzWMP+39usnzHTZ2Y\nm6p14LdZGzMsN/4cggZwnLywIOopQb4rMTy6nlQQmjIEGoTOQmTmqgFHrRJP\nvu7YYfkUcJKK1GMFhflnw8fWmrxtVjOgaU5iy4tT0wwjYFfzSNtdUd0cxE4u\nkXZUs/hvACDsVzXDPugKaV+8tSjxjMDFYixztqzdTS6b/mR8fwWv3/z7weiy\nDJZXOLlBKAeICSLgDKh6wGauhFkNIUzts7B1glSNkd7Qa88+lGAG4rKIRni0\n8l8Q+b0AAgPiJjuu1VbhbidVL+4XZbK22SCQqgltJzl/n7nJeNDNc+0/Vb+Y\nnNokBEGtmS+jTEGhJNxV19X1S8eWiZyYQNcXpBu1B+XyAXwLDcZmlqiUUjGt\nushX\r\n=pWdK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.14.5_1623280323201_0.6897658256932384", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-literals", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "79711e670ffceb31bd298229d50f3621f7980cac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-gQDlsSF1iv9RU04clgXqRjrPyyoJMTclFt3K1cjLmTKikc0s/6vE3hlDeEVC71wLTRu72Fq7650kABrdTc2wMQ==", "signatures": [{"sig": "MEYCIQCCL/DVOL3uJ9/YFePeYIU2P2+1N0tWLIwoEeXIQDRwWQIhAPef6+BvO2hOxL2cGyUyBoK6JEh8iCDFTxP6PHPxXtB0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.16.0_1635551250432_0.8123033817453533", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-literals", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "af392b90e3edb2bd6dc316844cbfd6b9e009d320", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-B1j9C/IfvshnPcklsc93AVLTrNVa69iSqztylZH6qnmiAsDDOmmjEYqOm3Ts2lGSgTSywnBNiqC949VdD0/gfw==", "signatures": [{"sig": "MEUCIEo6822y0HgXb/tqOCXF5RaH2VXvoAkwFnkVyVzXzRJfAiEAoDIGu5k4897Z9DlP8Lo3p2+3mJlFflQL7U+jsg91bwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8j/CRA9TVsSAnZWagAAI78P/347OHawIh6WpJKECWTV\njERVQxmp76uIWK93EjxtqVECT0r8ymEZ0/QUCSpSnkEmJleOt42olBe/51kD\nNB4vkzpI9JqZszBLCl/X253SVgMKXDO9SV1xvMeFZOrTLv8ZsgDns5rCNUbo\nei/fVu08TW+7TLxiWJn3iLgQTDVwBe0mFY7agtzgsv2dAdzx3wwq4u7HxUBL\nkxyRbzu/PPKa8myIcqLwdYkmlQJt3puHiVG1+8a1QMWFIbMTKV0+XWEZ9fI6\nJRsbQ2/p2ykDEclO2btXtuTSheIjNR/a4z8F+iyjRo1AdqW6D/QteYG1VAwL\nToY4GeYfeajqw/aLfexLUtGKukeyjzs9pYOAvco/wdQrnOUHWMpqHTu90G84\nXlv2DgJYjL6cfmuNvMJfMbkewbLgt3u8zabVWj8Vc16HtfH2v+dg1VAyxtF7\nF3ZcpT01xPyve7g198xEHLvfLRQ7dmlaNodwWysfSeydzdiApbEqiksjDpSW\nz9I+qVtLAGmNX8vdowfJQbXjJslpr6bCHAH8V64+Wedwen+ydPi6YDctBZso\n57qpOsqvNFhYs9VytYbaEAVaARjLw64yw8x4NbTiGtwLh4Fo2hqW2zeKBrFV\n4wH204n05v6bA0oKDcQvZt0CjFF6v0d+VrN5X/JLdkNSGgj5zQDcoCS2kxfy\nYJM0\r\n=Voqx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.16.5_1639434495802_0.23696287468595534", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-literals", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "254c9618c5ff749e87cb0c0cef1a0a050c0bdab1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-6tH8RTpTWI0s2sV6uq3e/C9wPo4PTqqZps4uF0kzQ9/xPLFQtipynvmT1g/dOfEJ+0EQsHhkQ/zyRId8J2b8zQ==", "signatures": [{"sig": "MEUCIA9cbVwzh/04kmPIjpGx1S+PBVDHrGTQ4pJ27nNrNsazAiEAlPWWryg5R1uhOAKYKkx02D7ro2apwPG9LvPwtKLstmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0mCRA9TVsSAnZWagAAeZQP/Aq7wuDFiHqALGJXkCgI\n3w9SK/xqdj5r2XTDEvb3Vl8qVmom2kOhDPwkSpnEYwapT1hRhCNxbBIYfqXL\nWoAbM5dVzIwxckHLtL2xanKQ6p3eiczr0Z2vEHKwXI0ttFyimy0Xt6rqd6kG\n0vO6nmysPmhMb4vLQjNDbPf/4cCzfZgiQVFX86rRhUQ61PAJcyUXbPII0mxX\nUGmarHkUWIBaTyAWcI47K92oQ+cdxkUSHWjFQnNuD08FUP52vwGr8jD+K1y0\n3AKOCYhYl9TmB5ZFZS0HpjyurSofZARHUtgWSinHGD7h2p2xp9OgFxsMZEs/\n5mnmX8+GQPPPVF0+NOzViezTd9bDSgQJdrkODyv5r8u43ez/iKsrhZYqVIbb\n3yr5sKdrlWBrGXuLiFdoshSjLC76H3xYQ4ZCgWnQBUTnjGDskHZpvCPWAbBQ\nr9aujuOKXyLuj87A+sNfx8iaxhcIx+6tA6V7Uam6En1l2X0HLaG3BUg9qRWF\n2uEto2Te6hDwFSlU97mv9ny50lkzDPDxhbhnGBl6mkNngbcYVBSVv3AcyqzP\ndBYlxlhxmSReTIA/yvWwvD13tspNag5YbBdQuxUfVpt6qXuGerqxG3pA2n2X\nymDBpDZi5hCVxip5R4jwIphFwvFGdv93pkWhsWLhOYOJilqAhKlKb6BUE2B5\nsgMs\r\n=iudj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.16.7_1640910118278_0.42782032179821505", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-literals", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "97131fbc6bbb261487105b4b3edbf9ebf9c830ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-8iRkvaTjJciWycPIZ9k9duu663FT7VrBdNqNgxnVXEFwOIp55JWcZd23VBRySYbnS3PwQ3rGiabJBBBGj5APmQ==", "signatures": [{"sig": "MEUCIQCdKAjMqRz5psXTmRlDr06YnxmftFVmG1+dMH5yp/QByQIgJQzZe+7RMtImqIZyOEVaJUpnWxdiLj3pMlsSKcHXG0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqblACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOvQ//YmPoyuH5MdWXTY2ck3N+0eOn29MH7PjzBRzIp4ISvFh9Q3r1\r\n2By/8mJtQvVcfNZLBLjGLaQZLB/U5rsQacYe5PbjDcXyw1aQGjRWJyg7ewHg\r\nIDbMrBW8NMRGXStr5HUU2dPSCoDbkYydJ0ODIV2ZewtO1SixWhc5sk3YsnY4\r\nadXBK0Y9DrN9o4V8D0Vv0+LEkfru4RefOvykx+nSI9WCgfV6hyrs1ZE/4pv7\r\ndNBDKAuuQGoDu+zf87ulg79gtdMZaq/D4wjUiqqMxP11ifXbx8tVLPOnL47u\r\nQgxM1xLVsoL1ZLgki6XGXsO7/WAfUufyFHmHo4Qs9NGwLHdOd7crKRoul7VR\r\nbvB8YepCaYus8VM5ES4uCR/gvHQ/Kvbtj85vDEhJBbvQRMMrXvz5AETLTUCr\r\nwlApWiZUoDbKgayjrrNq6p19N57KEjEoWbN49ZaV5ftBvb7Ak6m+XBwm8Ubk\r\nRnGThTUxgfSm6cAgakT2rVDDOhBxmBr3tNdtNApevIWItlvAeLFlPRIzHmBS\r\nyIlzKVeuITrjlPNaCK4lOUluYa3QzqrAE1hTE2KSZeAxab39OJSkX9zDXlYX\r\nC37lJH22aSvFyn8Akl4LlxtySvDRbq95SprtAPEcjW7QttZo5It2JOx4MtNu\r\ntmDmXnrZUReUHYN2ycfhTK2FVCks0WjkXbs=\r\n=dcXZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.17.12_1652729572966_0.5540112456096069", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-literals", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "9d6af353b5209df72960baf4492722d56f39a205", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-x3HEw0cJZVDoENXOp20HlypIHfl0zMIhMVZEBVTfmqbObIpsMxMbmU5nOEO8R7LYT+z5RORKPlTI5Hj4OsO9/Q==", "signatures": [{"sig": "MEQCIAW/t12c0kjSvMYL0jJ3padovov9DswWvRBWrOXubu+YAiApx4EzXSFQeCTpP0CJYzypu2bLbOSPtmRaGO1YT0vvUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCQxAAm1RHNTmbuc0kHV+83ViUeS2i6O5KHxXYBbsiK7H6/wsXAVZX\r\nhKDhQRMQFgubZbnUZHdPjetE8YwJZQqL/4odlNkA7GDH66NJqiTmr/8QL5T9\r\nhzcIuPev1K+toCAPGdHk1BwPwJag6arXYhXmXrhk/T2d7hVwTrvAnsUKM6Qi\r\ndLwM+ZEYeFZfvNHqER5B2eMNRo6sthkdwdBt1ajyJLp6AysEgdUCxwnGn3xJ\r\nNNvymiY85LCNIxykv/gyjABKVX1l2E8wQ2tE2h0nGkr9KLiiPsPss+BTtku6\r\nsJnk5XtSR/gR3RF7QkF/51sGK1zBIBwvVgHx9dGBeYozSL+/utEL6PKWWtIK\r\nSN0tYS9ILw9BKRR8ng/SBH6VoZuYI8JP/pU2EQEelau3tS8wuPhhMaWyc/bz\r\n6H8e+TtvYrqywv/t1YSpCuOMXVx76KmkNRPzcgjzMkQbCKr6gBFFfyP9Adlk\r\n/pyprhVUsXADlYoufTmgo3HwcSaI6ZMT5yNRDUEYwOFZwtKbWqdu7dGvCjs1\r\nm/O+zBmC0spvAYGMn0pZ1BgwBb+g3opHltujIjhMX0Kqs/ptteGDuKZyJlqd\r\nK2E3pntUP5jFgyT+J+CB/gnCZ920xmB0ajOFiaVgnw7K+5BR4H/VoMur9dUi\r\n63/FaqS5MHfXWSVAO816mD8KGbCLUMrX13E=\r\n=ZDa8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.18.6_1656359400185_0.558261280011866", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-literals", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "72796fdbef80e56fba3c6a699d54f0de557444bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==", "signatures": [{"sig": "MEYCIQCFzwU2ei5SA/OjVNDfLMt0U2DfAQwNbJMfhm26/27UfwIhAMtzWNoorZ5oeNpYDPF3ADmy7SFw1F85SGWV/4ik76VN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBhg//Q2cm8rlwbKHA63fWfJDmEM1RcuZbmrVUVLzxY07gInuHPIAf\r\nJ8EbEEF6ja7/wn3SAc1HbjiLERo5dspr0iz0TS8QKAjy8xiO6Pm4k43mww65\r\nFPkQQQaDmu6XE4D8a5ZzY4Fxt0hqNz0YW0Ojie8TbPM9e8uDwz8R1sDYjv7F\r\n6WQ1m5BHBIIXwY8Zg57PVL34dnH1VJ1yhbRLBOmMc5WfV2fmUQhj3weSvXTB\r\nHdx+zPliChVhN3upMVWIeX/mVWgEZBIfMbbqCRv8lCow9pqgLCj1+yu/PY+Z\r\nFoKm7biw38FdhdaCXkJO7XI3kZyHHct+CYjc8mRX/UN0AUuGZk5iIL3EeV8Y\r\nMJ4Aaz4vlNWsWcauVYUl4yee2V4yreJusq1rJATYmd0dxXYIFuS5aZewIc/N\r\nLYPLQYqQCcjQzMhKCIG9hRR662knd+XaLSKgbSqxQeizBsqiiYZVibx08Z3l\r\ntTCzfYejaiDa77O9uI/+XGSVjIJokNy9Sq/B+BHJnTWapmoAomKvTd1jRvNl\r\n3YdU1hWYbnUcyVLWiTQqxirWdcC8BRUm4xZD+tfSx17SHMnoD0seuNb2GaTr\r\nbOBVuKAQrKsM24PDcCzgNvfjfozp+YAo8tpxGQzwMcpYm8rdXMYGdo/Xt1mq\r\n0+108unscEvN7QQI/MdIwCoSykwvHCQavdU=\r\n=T3H6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.18.9_1658135852747_0.39315215246666235", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-literals", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "599abc550d80037c96de273bb4e9f01e04167021", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-rg9wxEqxP1ecE/jL/kiswEW6ZhnIkSzpbPY9HXTOEmFi6Fa+nBBt4C04CcC+5K5j+/Vnd2idf98Z6AZ94BBPJw==", "signatures": [{"sig": "MEYCIQD5eKEGIzzkpKr23d2/UEI9tzfjcR1651epzdYNEnyohAIhANe8rl0dOyePfAmfCRgJRfw+gaa9HYv8fB7Y9XFcSd3T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGxhAAowk+DOXq/RrkVGmKtvq/wp6IO++4DPfIqxcvVhDbx+pLf0lw\r\nhtIFbtnPjz2G6M/V0vMRs5yy/P1MKwdftOtw0MGINzmuTPIbsqDG4I39bMIT\r\n0Wybqzl3TsmkLTLJG00aoi/d1edAURXN1uX/dWOEpdHnqsi87f5RbBbfeUnh\r\nviFdjM/DxqGCeZeuniwZ1M1kRtDGqeX66z0nnhL1v3uGztZBeQ6O9SMRfH2y\r\nWPrjGFBto87TSjXtPHY++wIMmgClFb9Diq8gWoD0V4zdArfbNhC4nRf5VmzZ\r\nmtq+EWyexhw1H4GESMphG7tVHKZGJZFXBVmV/3kcfGufoEjG9Cjkh3vdnVBL\r\n5l1zkttIogC7wxR/iGsKFyfFdDHbiYcOwqRHf9vdwo6IiKTX+P5mnN6J/0Xz\r\nTVEYyvUBHlsa3JITxR37nLyiYvJQ01yBJ3EQwa5XWpJJ/AZF6W8J4ptj6QXZ\r\ndfcnGJtsSIpC7ZGtFqg1jmxWynGPtO3OcI+bMuiCSrsWEUhHkuXdRIElime2\r\npI9HSUMq1W5V3poMUm8Pw4rrHznfzbxKMd7dVfx1bcllzqB5uR8D4WdI6Ghc\r\nLpSLgxqz72sqhRY4r5A7G4ZJTHAO5Giw3BfpDOKGWDjAmtCOROqv51Ekci5a\r\n4hVXnix2Wg39BE4YsuAbjfOQNjK+H7nssD8=\r\n=1zlU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.21.4-esm_1680617364003_0.6787367904840267", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-literals", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "fac714d44695eec093268bf0c6be0e5d4bc8b509", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-pQ0A89okPNEefjEvnInhCrpz0euy68OEPpCAIbtXcJV7bNmndE7J5zFCigz7pz/SKpump0KbjMHbDsEHvpB7aA==", "signatures": [{"sig": "MEQCID0L+9mSabO0YDkVvfXJCNFUO8RejNoPTIij38em+wKUAiBXNN/E8YJIcUWjIpWAl3XW9aukXsMXhwk/MLwnhUGJMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5LxAAjsrjgWLWGrTxjFjtGmoLf4zGlVo0AOwi4BFSPXxkgx46yxm2\r\nI3n3qXy2bxrzmbVO3wZ1D8o4nPMkIPSRytp0B8HehvnN+4GZiQGRiSjTDRGi\r\ngy4+VIsNI+QBcDGuSqwaMhcrKB1mwhNU3BEGsZgnVay6mAQlbYKRMEQRTQa3\r\n0G7Yi6l+cC1fU0XlOYOGbn3SuUORmcYgght09+uruZGR1AvmezUEoI+5sgjH\r\nGaixinPzAN4RlFDwAs3QbmnkSiZ4XgMjVqN/96lKDCTKIu+YDV/BOt/Hlc6y\r\ntMbhzTMdLCimL9kzcooqRr5uc5+OQzmNzduSMij+Ls59ZcESJigdX2y4wOzZ\r\nDj25j6lUlqAtcS4Lw5eZk/JdjXZsbPAyiWWW0YwpmSVKL+Pg3rYp95wGLS4B\r\nC8B+1FkXSa98dv+/umBXsxClYTkFpBysAMckXre8QNtxW3fSjpKJdlapFSJb\r\nsWphPvBYLSL/6wyS8iv9JAnkoNfrfhpQeL+BtaUocB4Pl11b5dmLNfq4w+mh\r\nybhTA4RXh2qh6ZsbLju9rFX0oI+bRpadq6HGvbxOKnlxpN4VvYjyHsocFiSN\r\nUKsMIczhXwKzg6LpE0V7qzABu6maap65acpgQHxEnpY/VXAsMw7Uwb9NMbff\r\nQXZaxQFqfW9aGoDJAwt9zYQKOvjLR43eELw=\r\n=RydC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.21.4-esm.1_1680618072596_0.9973854892873788", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-literals", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "8ef0ef81afef5f87a1fdf5092f77e45db92d5c1a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-BUxeVeN8qtHMlZeFMbG+fMFc1Ewm2QtNu0McbvhyVFePBxKLjJcvuWMEVELrDt2BGeSRLCY3EJ/lB+3hG0hpCA==", "signatures": [{"sig": "MEQCIHCMxjzgqceflhn/CZRkWUFDE9gY0sHsaSmKR7j1LNodAiAXAcAqZFMd3hFiH/Uk1Y4jt5b74Fl3tgbOouLtK1fHDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZhhAAjgyzTvdeMhGBAn222uJE37k9et1wKi064Ag2NWmEREeZzxO8\r\n8Vnfcjbb+pWfzjr8wOpyXbganO4kUye5TOGxMsFcB8CyaGSUX/6XPytC8qno\r\nlbZYtjx5B309ksFRdRmVYDNqa7/qWghotYXm94PJZppa8Jr53hXWWtlK87vK\r\nqCez3TlpXBIdF1K93Td2UqF0oMKsuSed1glv2oMBD+KeBHuCLVnILCfVQ5Vo\r\nYCwwiuvrpPk3YyBjadh+vbd7Z4DgoaodODWNEnfWlut8NlZRCrP/3c+JP+nH\r\nEsokIrqYqWzBtxL2ksQetWYVR3lqGlt1ago1FS+aYJUlcXkbXqEnArqFmqyL\r\n4CjikiBGqJGQ4q9o3hmHzaabYnjbyv/QuahWYDYUSes+xGIbmSTvd/hgxJ3I\r\nFtmlUdNj6oS88hhwHxbCPJDZNTOn4y/LYdxsKddpQCuza5P7/nN3HoUEXfui\r\nHEcfeyexsYGmSH0w8EOccg4SV+fAGoIktQ/VpbMA/GWwAROs0AtfYE0+c8MO\r\nXYzRHxQxAg+tRbzendASKf5JhHZUCa5825Kn0ruievGnxjKVa5faIZS6UIYa\r\nAovtoBdvPPnNZ3TyS64M5yQzowj6gxmjasAfQM7vTeA2KvIUWxHsXZ7MSB6c\r\nyNhiywaX09CeqXsG8fN2cg4opxifMEbLPAo=\r\n=oocp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.21.4-esm.2_1680619156043_0.31796809646214763", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-literals", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "1728dcb592d3f4130c633d139a13f836f492ba36", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-k5cXWHpvrcbsGR4AZ/t0R685RqIE7oyExvGYFCAHqE8zespePw33caoSnzB6ub0nblD5TGCk1P60wTBUpnP9BA==", "signatures": [{"sig": "MEYCIQC1BGvch/cRWKX4tW9XbbYqYmSfjC7l84DF5xAqOy6lwQIhANPB/LfyPuSorxVW6gRFnrnmg+V0VDnMtsS0BdqO6A6t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2/A/+PMSwPE4y69Lo7XHY9BJCPBNEGM2QeYojkyjwMRMLpGad7S4s\r\njHl+ayDlTjs7KZKdYm02OhicSTQu+mn+vGJvyOjVXQZTyFcoLW/xwO4mkelm\r\nDJA1pDe9K9dBtcBydHg+FSB9SpxT6pnCc0zwDbG+9hdzzr+I9k+rZZ42C6D3\r\ntxQfvOBki+mIfvy8Fm5bZICzcyxGEvu/9TVp2QEmCk8nuSBjxIIIDC1iJnCg\r\nijc93AKjTao61W2aineDWHYNe9+aqbPE1fN0npnhKiHkgx2bJUH094oJnZz9\r\noTYbacvmm+SEYin7jCiPdN6V1HgjKB+gYeGR/62PNt/HU2Hf71c9iiV4ce7R\r\n+ZIFhuvbCjJlFA6QCUkVmX1P9t0BmZyv842R1momNQ7x+lvcxhrs+0dj2zhn\r\nGvccQ5vlYABxr2uwzaj0IBui1FZZc4g3cp00M27RT2DeOixJ7mGj692jfGp7\r\n4SJMJZj1poU0QUj5a624mgWK/9w8aM3PkwwCedeaAAkpZ/N0Y9Zg77nBe5/3\r\nQhcpZLwuwRzXGT2LsaKs8me/b4K1z3Ps9kHVRvZxHGnziQ9IADi/Tx88XkAg\r\nkN0NkP/ykgNDVlXgJJHcWvpX65jOO1ZVPRTedeACDOwO5q8B9fPi3T6wkLy1\r\nSNReHIHqud2RZvqSjn2QB2P217JmIptILEo=\r\n=CnIK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.21.4-esm.3_1680620167137_0.04891389231933485", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-literals", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "1aa0af653afaac504f075d96d2803b6d6f0222e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ruUhl5PK/vJFEdFfaVq5lbE/DRbFcFlW1tGUaW6DHLVvldRE9HWl32v4T01d8mfML01uzCCyBQ0H+UpQ5iHzFw==", "signatures": [{"sig": "MEUCIQC1qWwDEBaMsFbqcThxro8HW7WOKF7pudxjBRCJeE0oPQIgEFWO2/yvmfs0uDGJ+8RHNdpKaG2qNfcjxqxHKMp6pSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzlRAAhrezgOAmE/Q423vo/vam8y3+0k8mUoNND3RVlakEY3MUnqw3\r\nsXblmQTMrMc4BB+AiM9NONyUpEfMnj2Ss4LTpJ3UyBVeKQgEwk9NffuxL+YG\r\nc8DHpqgPICnU5hnZoRjldxKCG0xNfWlG9+SIZODk5plPpLT1R1AGqkHLTVTE\r\nRrPacLrgOAG/9FIhonlF6VTg0vz1JFxsfSHbB1mxqfylUDLT/7qnf8tl1bkL\r\nCz/5iOjN7PS4Zf4Ap+DDQiPY8uL4RJ9yGwZASgJ1ptqjKdKpFbsbjPQke+dW\r\nF86CWiBAYFIv1nMkWvuarpjS3+1yvWQnyumyPxkvjQN/lRIAZPAoqJvcAJ8z\r\n8hPvZdMPbsUcJBb4o2SYuM7eycnu0e0ZcSQvjvkRtKRcpCtOsCcIaVfInjtT\r\nsTjIin5jGyggkAvlbBhMACqpFPbnImmRs6w7bckvK+1BuYPGoLN4CnjMjGsi\r\nAkQ8OT1vlWIV+kpv1wB0Kq5tBKijoM4+OxQLR7K0XsXfSqBVwldtSJNu5gjc\r\ntKX59+X6leLm3ytEwdoo03sFBvhmdjYqxZ3EEGMU3w5ZBHacKI8cNI29L0Td\r\nzDJIDzStI/juPBIYEdd1Qc1IB1RVj1oNLPJdPGVfXDrDntw7q1KOcrS614H0\r\n4ASYhtgQZTF/AY4+jCJ5xdox2AnyUEvAWXM=\r\n=t0Qn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.21.4-esm.4_1680621199707_0.8726129129029365", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-literals", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "e9341f4b5a167952576e23db8d435849b1dd7920", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-fTLj4D79M+mepcw3dgFBTIDYpbcB9Sm0bpm4ppXPaO+U+PKFFyV9MGRvS0gvGw62sd10kT5lRMKXAADb9pWy8g==", "signatures": [{"sig": "MEQCICUbP7GhBUVheMzglo8g3n+fFzqGPeSsmWWP6OSdEA4yAiAS10qvEjUROczdLYyluU4UMi8BO332iVpKCRRD86d20g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4622}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.22.5_1686248477346_0.5746431112022836", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "7f8fd325c307fdc9eeb7e3fa625d95fcf21cd931", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-rsVEisZmQrmtqvApBrzbYQ3+OPNpjRTs37HouvmzZI+TQ9z8WftHIh9G8xuDqY36rWgA6DKNIekJBre4gVM/KA==", "signatures": [{"sig": "MEYCIQCARUNBLLtH/41wY7vlXQg5Rt/E8vxsHzogkSaerceOHgIhALNoMmo+0WBf7AQP8vedtAC2/LkommBE30848cvJRSHo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.0_1689861591526_0.7566185661071427", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "baf7435200f086f0a3420557bcaec5659da6527e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-PCl77784SjNjWBaf4s2mRr9FrBSNm8hai8WX2p43IpCXFVRTSm/+9ZdBykb6H9nWm44KJy/zTM9k3c1bEYzq8A==", "signatures": [{"sig": "MEUCIFED8p9wVqe6AZvPDdCu1BVL0/XG6DrqKuZMPkA54HNQAiEAykJLvq4YqVmywfyf1fsdRxYSqQdE38klTdJHLtAEN80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.1_1690221106975_0.6871835154245873", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "e7db62d70956bbd685bdd358d83be71b20b253ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-oqvfs+pifmKPxQDbr0wdfomxIeUjrqorJKxA5DHAsC/ITzjJb8hmTWVH7agzv07s7xMXrgmLl/+hfI9HryWunA==", "signatures": [{"sig": "MEUCIFOmFGIt195qkD7rTaF9j6+qasV0L/Tc+Lusc2cPKJZQAiEA1facxVoywnuRp4WIx1vqyOYfO3ZjsT7TU7G6FhJuaFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.2_1691594091306_0.2626849769457069", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "a8f35359c626d344f12dd73579b017d966403764", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-lELaeo/EfM4IXLO+6qeg4/EcFJ3fdBW7g6r1meZFkGL3jPDl5WKI4W2RxTy+rUWTYurNa20t5sU9euYTwvptdQ==", "signatures": [{"sig": "MEUCIGkVfKn/VrxLUxaLlcz1QwrcJLdYg6DNi2eSj1rZDhnUAiEAlX8d8A97FEz/XDn2ZPbQIRpi7n9BaBam7whDPMTCcDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.3_1695740207825_0.4379757555125896", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "6bfcc7deeca0cbdbc77b2d11428526ede4b83aea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-BqAreld1+Qb9vQBp81c8ILU1AMLVuRoyhcpIxZ1o5ckr5Kri9p5Nwno+RS2QPD+Q0A/W8yDWnjNOasyZj5qNSw==", "signatures": [{"sig": "MEUCIAo6fElNEat+MZ6gkd/fUexHuZtgMnjFeGVRGRAY4K6rAiEAkUD5G7WU35x5BFjZBIvzQoFvgCYk21JYq4e6tNK5PhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.4_1697076373951_0.4751608465297492", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-literals", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "8214665f00506ead73de157eba233e7381f3beb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-wZ0PIXRxnwZvl9AYpqNUxpZ5BiTGrYt7kueGQ+N5FiQ7RCOD4cm8iShd6S6ggfVIWaJf2EMk8eRzAh52RfP4rQ==", "signatures": [{"sig": "MEQCIGFsXAbpJeHHfOrDhfyXdSOLmyCSKVkQrtLmvsnO3vWcAiAzkcr420HP6Y2pUlk+jdaFOk0k8gvC920dfSsEUQkMpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4702}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.23.3_1699513431813_0.7927390931570448", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "0684b2f5df9e3668def00b3050367d5aa8ada462", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-OJGJFKSQZERa1UWfBaBfVfnSMXA+r5ZI8lLIPDkgDSo5pKN7Qx82jlHU8RGSWLVqJ4NbOqQAbapRncCB2z/nag==", "signatures": [{"sig": "MEUCIF9Y9it3PrWfMkM7TQZXTNgFI9AwJqfT+mEUR1p+lsUrAiEAjWqoahf1LUABZHPW/UdjVBR++8pCuiBRnbS3uFzt4ho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4704}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.5_1702307916958_0.18898971914745744", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "3a5ef8ea54b0f4d6a401cfe34aaa3fc1af71e8de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-BPT7En949HinGRyQoNovD0t6onQxE+88KF/EgeN+AflJcXYfu3+bOPWPlxSd/AiZUP+3ebw1Wi7NHcj0lcni1g==", "signatures": [{"sig": "MEYCIQC21kw297YQZ8oC0O1MHgSEQjgQuGbJYxUe5DnGRAwYQwIhAM3O1+8+GzHjvNQkY/6Se/rkDkfsw+RSmX/2HrtI7aaV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4704}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.6_1706285637687_0.9624414249920556", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "3926842844886b38020fd8c6c4c88d46b0bdffa4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-I00XNeayPDEcn0jW/wNIGng9iTbA2bQeHQ4VOI5QFnfYgS11cScuKMEm4MdobkaYhXx5D93h805QP/P+JEK/gQ==", "signatures": [{"sig": "MEUCIQCMKkjL0arvDZ/aqz6cABTXuWj3vO0ZdcuM0jbxnnCw6gIgbCnwCc5gPHYJzenQgL1Fc3Y/RlgGpRsTvjroUr+9qt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4704}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.7_1709129087079_0.34830894336125073", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-literals", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "0a1982297af83e6b3c94972686067df588c5c096", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-zn9pwz8U7nCqOYIiBaOxoQOtYmMODXTJnkxG4AtX8fPmnCRYWBOHD0qcpwS9e2VDSp1zNJYpdnFMIKb8jmwu6g==", "signatures": [{"sig": "MEUCIQDw79E1MeUuEh/xV6LsiRu7tJYxqIOebXV55PfOOsd7cwIgcrwF+Yuztn3uVGJgTKcUW2pThvA6HJlyc62/pBu1mQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4633}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.24.1_1710841718891_0.19627011249575532", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "338edb5ea73fdb2590e852fd3ba26498cfc7a997", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Qxt6xhWgEywJNRDJLuD1Z9A4bCEBcgXciT7LkBwbiAxAZM5SX+FWVcwpClanqw4r4snqRfCrneYXxVN+EG2K6g==", "signatures": [{"sig": "MEQCIC4tcc16R/EIsw+Dy3KFulcmOUCBtmYw0N/CKv/rqQbGAiBjf6jmKNvucLMHQPXp64BT3a+Z1qnweMH+VgR0v13+yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4618}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.8_1712236786780_0.5395673956748266", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-literals", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "7f44f2871d7a4456030b0540858046f0b7bc6b18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-f2wHfR2HF6yMj+y+/y07+SLqnOSwRp8KYLpQKOzS58XLVlULhXbiYcygfXQxJlMbhII9+yXDwOUFLf60/TL5tw==", "signatures": [{"sig": "MEQCIBuMxeFNjVj367ey7eR/+tv/xTFnPSY+dO0iI7+RnXMVAiAUWT9XhQ45d0P0P1We31VKs9kPoHAboWEgKSW3fi4CMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70542}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.24.6_1716553468304_0.4175976925480718", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "c509f567b2874857fa5b3aae38dcd4ca6e009553", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-bVIkOPHvbgxRaamt58yEhJ38eihDI5Izyqjl3HH6OWDquYYcVMyMl5bEWUmAhwWn20DrG3SzCfWyzfNGJtAYSg==", "signatures": [{"sig": "MEUCIQDXgmORjIWplLC5nrzSOUfzrh6YqfLNGOHhSobnR+e2uwIgDMB8vDdrZD7MgYPDCojSH1VhQQpZ38g8yKOipR/0UG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70837}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.9_1717423451576_0.9474246957188026", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "3e42744f82ed17593e845cbc62eacc907fce13c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-0QYNwbWR8gfff+AotNuOEiu+KBUQSZqpM0tpUEgJMtXjDKCIk7KWO5TKsEAT9Wp2vuyvK8iHAHfit+JttslYwA==", "signatures": [{"sig": "MEUCICRCYnld1kGeDBt1JOB2E55Rh0o2oOD6lEsPLbqYuLbvAiEA4ixMqR9S1mtnCrzGoBLhSSwv8WhiCfi8EYVQ3OdFW+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70844}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.10_1717499999287_0.06778872019773163", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-literals", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "36b505c1e655151a9d7607799a9988fc5467d06c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-vcwCbb4HDH+hWi8Pqenwnjy+UiklO4Kt1vfspcQYFhJdpthSnW8XvWGyDZWKNVrVbVViI/S7K9PDJZiUmP2fYQ==", "signatures": [{"sig": "MEUCIQCyH6f0quSOF+C3LWPzDqzsS+5mc0LLtkaoTQbVBvX3IwIgG0Ws7ye7X20u7wMKBx7U9dUJawVNElLS8qywPbjxOfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70538}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.24.7_1717593318493_0.23464340075670354", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "791f067a34b6108db22bb5d975895c53f72bf679", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-I5+ZKz9mErf4k7GvMbGubqN0+aCd5lbbHuuYZ7wLZTrbVoyn8IRCBHrLVHRh8aKF9I9KDQkRFdTPmrEsxQDTtQ==", "signatures": [{"sig": "MEQCIC+hOdmcSwtn52Mv4DyZWgpI31HkKSxntrbJ1XX/vC4aAiAqWJpawg/GXNBKFkWDEqoJpSLtbgLX/NmOMILTqB0tiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70733}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.11_1717751727972_0.2417023030641472", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "db7be2ae87223da750b17493180ad63b7a809bed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-2QKnx8uO4hwIRwz2rgfOVSRBoBahGBYYqoKn+XjOQX0xKSFv/+in77tNTZ5FezmuYcnLIfZt0CxY8ikazWmFWQ==", "signatures": [{"sig": "MEYCIQD2+0/PmjSboQeHNN9MUD90Vlj9FO/4Ahlez4k1124uygIhAPpAImAuw9AMoZ149sekXcZmj6Xi4Q2Pe5TK5Pf0f7ab", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67529}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.12_1722015204832_0.4853940051440846", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/plugin-transform-literals", "version": "7.25.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "deb1ad14fc5490b9a65ed830e025bca849d8b5f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.2.tgz", "fileCount": 7, "integrity": "sha512-HQI+HcTbm9ur3Z2DkO+jgESMAMcYLuN/A7NRw9juzxAezN9AvqvUTnpKP/9kkYANz6u7dFlAyOu44ejuGySlfw==", "signatures": [{"sig": "MEUCIQCToEmoHDLKRfompnZvwj8aEBbiU6rx0rd4iUorNm1csAIgZ/PvIHyTgIpy56eiGod5v7VRU+XxotJH/MZFCfNtjto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67212}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.25.2_1722308087876_0.8216486826357288", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-literals", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "70cbdc742f2cfdb1a63ea2cbd018d12a60b213c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-fwzkLrSu2fESR/cm4t6vqd7ebNIopz2QHGtjoU+dswQo/P6lwAG04Q98lliE3jkz/XqnbGFLnUcE0q0CVUf92w==", "signatures": [{"sig": "MEUCIQDa1w0DONdpHBg4iYGVF1iyWhYFuIJ7slf+A8sBhmjWvwIgaPvDTBTm0rD4E9VZY0vCyYNJ+l/l1iERhf+5YTPOhKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75070}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.25.7_1727882084251_0.6845665857390497", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-literals", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "1a1c6b4d4aa59bc4cad5b6b3a223a0abd685c9de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-9N7+2lFziW8W9pBl2TzaNht3+pgMIRP74zizeCSrtnSKVdUl8mAjjOP2OOVQAfZ881P2cNjDj1uAMEdeD50nuQ==", "signatures": [{"sig": "MEUCIQCD3O1yeF/12yn2eUkCyGvHb/n6nvC/1/BZ0Lp6/s9F2AIgRaUCTcjjl30BiURHvRiAqTvK/hFiLeO6oVwBCK1hsXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4627}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.25.9_1729610462595_0.5196009160643045", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "c2922213ea5a948543785dc5d0331e58f1490e4b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-eOh8nAp7wXcc7sIFfSdei7en4mBcSHTKUFjLfItcmv2s7YrsViSqYYIdl0Y5hzQ5EUq6uof5qSrNYqoBMd0IJg==", "signatures": [{"sig": "MEUCIDj7U6QB5NeKvT7/Lorg6KEyDKU6zAgMflmef7OtH6m0AiEA53D6kP9rXIcQVY2kdjfnSqqLl+w3DdnFaZVMrp81r48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4950}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.13_1729864446403_0.4336657907080266", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "7ffb397dd95892cc3c0062331268c93a70025175", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-Yncm7yYW8GjlWvpUPWaSRg18rp0jAtVfZDB4HmKlZad5Tp1FinurqMNBJOW7bHDPH+8eOVx9E4kd0r0A8y+nGQ==", "signatures": [{"sig": "MEYCIQCHxk61HvwfaOIH6ANVkWfo/X7Ydt/QS4TgFpBTdKHi3wIhAMaG9qIshMDojshONRzqx5gCP84kwR/VOqKcVKMi2dk0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4950}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.14_1733504037297_0.19279959437498362", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "f5f3a11243605979757286ce4bbdb11bd59cb679", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-HOFj2mT21hsis1E5zCvWnS6EIN47vgjcli78hUks4mwmfFZX2WXvco2KXUhIKZj80V9JLHHQTGMiUebSMdEsag==", "signatures": [{"sig": "MEUCIQC4eQ9cBS1of8xuc1XfHsmrPy4FgRz/3OZswTMsY5gc4gIgAI7CfuyM+DIiv7iMtnpUlBWIDF7t9k+HmlgUDKoXeXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4950}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.15_1736529862560_0.007451056151475344", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "f74cfb599468d0085d3ad0be8c55ae997cacc207", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-b4HYL5b6wiIOUvRgIt3INcvX2M/j/WHT5vB1uGeJiXuv0XX7oCy7zMMUhCuGUPT+Y90aka1tqbW8P21oN1LRGQ==", "signatures": [{"sig": "MEUCIGHhfEGu1KjmklrqpZ1OKnpMKY+m49S/xyhgY90agEhsAiEAlRJziCUeyMNIqP3Zxb/hPVAhDzGP7tEegW1UZ2Aa0YE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4950}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.16_1739534338961_0.22122682769265478", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "dad65aa8e907ae05dd8d4eab4549a004b4e0abda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-EvgzrtmDIXtW/xGmitk7sntVKIlB20KtZZbFgE2A/ujyNi2atCZtIcsZUGncEtX13hi+y0+LfjlaNke51SKSlA==", "signatures": [{"sig": "MEYCIQD6ogDV7KZMF2peo7+hEzCrR+pnnZLUHW5qHh8IjYZXiAIhANdKvdda7hx7G2xL8MSC7DQ8DABgxbZownYuIIaZ9LMy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4950}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-alpha.17_1741717490383_0.1653507781850143", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-literals", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "baaefa4d10a1d4206f9dcdda50d7d5827bb70b24", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==", "signatures": [{"sig": "MEYCIQC3o54aZWR/LUPv0NZzmq7PMllnogSltRqqnnWrviCImAIhAIq+iMGLfgI55ynsmNDRKm1oNfg1ik3Vw9ECPARSBki8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4627}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_7.27.1_1746025728778_0.902241750554613", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-literals@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "dist": {"shasum": "1825b3a2620fb0f925329c1aa87b21a75a14ec8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-g0IiR8SEQCSMmSyO3MhFcDw7cZi9rCSCN3RuU8YGDWofj/lFwtgelyVMwF8nmjo5xgMaX+Jo7xJ3wyj6i2bGKA==", "signatures": [{"sig": "MEYCIQCqbod1PaGgo/CZCSZOqnxD5ik8RKy5++2Q/liklXA5MgIhAJP056hC6yh9SrSt3RF31jwh45tsUFThveyiWrwoZDm0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4926}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-literals_8.0.0-beta.0_1748620260602_0.8836176899662092", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-literals", "version": "8.0.0-beta.1", "description": "Compile ES2015 unicode string and number literals to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-literals"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-literals@8.0.0-beta.1", "dist": {"shasum": "45a6633c51dbe17751a86ccbf29e63e94af714e3", "integrity": "sha512-ottI/F2lWYeq4XPc2xShCIicxDXVxAmAL31jZcksj6x8NyavOjhTDOAieNTXsho9t5C35xeKwQ+xApFt9ATyZg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4926, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA3mrW2sTXv+DXkBhC69UrCxR+wNEvUVWa/hNBNG7Hx1AiEAu6OoSZ5IwnyPA8AoCvj/Y+Zy6bCCMoudNHln52YphHs="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-literals_8.0.0-beta.1_1751447053744_0.5484847388211378"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:39.511Z", "modified": "2025-07-02T09:04:14.159Z", "7.0.0-beta.4": "2017-10-30T18:34:39.511Z", "7.0.0-beta.5": "2017-10-30T20:56:18.824Z", "7.0.0-beta.31": "2017-11-03T20:03:25.301Z", "7.0.0-beta.32": "2017-11-12T13:33:16.696Z", "7.0.0-beta.33": "2017-12-01T14:28:18.814Z", "7.0.0-beta.34": "2017-12-02T14:39:20.013Z", "7.0.0-beta.35": "2017-12-14T21:47:46.485Z", "7.0.0-beta.36": "2017-12-25T19:04:37.238Z", "7.0.0-beta.37": "2018-01-08T16:02:30.886Z", "7.0.0-beta.38": "2018-01-17T16:31:53.323Z", "7.0.0-beta.39": "2018-01-30T20:27:33.596Z", "7.0.0-beta.40": "2018-02-12T16:41:36.175Z", "7.0.0-beta.41": "2018-03-14T16:26:09.250Z", "7.0.0-beta.42": "2018-03-15T20:50:44.016Z", "7.0.0-beta.43": "2018-04-02T16:48:25.964Z", "7.0.0-beta.44": "2018-04-02T22:20:07.379Z", "7.0.0-beta.45": "2018-04-23T01:56:51.880Z", "7.0.0-beta.46": "2018-04-23T04:31:15.685Z", "7.0.0-beta.47": "2018-05-15T00:08:59.049Z", "7.0.0-beta.48": "2018-05-24T19:22:28.162Z", "7.0.0-beta.49": "2018-05-25T16:02:10.883Z", "7.0.0-beta.50": "2018-06-12T19:47:17.424Z", "7.0.0-beta.51": "2018-06-12T21:19:50.136Z", "7.0.0-beta.52": "2018-07-06T00:59:25.652Z", "7.0.0-beta.53": "2018-07-11T13:40:15.721Z", "7.0.0-beta.54": "2018-07-16T18:00:06.735Z", "7.0.0-beta.55": "2018-07-28T22:07:17.391Z", "7.0.0-beta.56": "2018-08-04T01:05:43.112Z", "7.0.0-rc.0": "2018-08-09T15:58:19.707Z", "7.0.0-rc.1": "2018-08-09T20:07:58.847Z", "7.0.0-rc.2": "2018-08-21T19:23:53.707Z", "7.0.0-rc.3": "2018-08-24T18:07:59.970Z", "7.0.0-rc.4": "2018-08-27T16:44:15.152Z", "7.0.0": "2018-08-27T21:43:10.411Z", "7.2.0": "2018-12-03T19:01:25.632Z", "7.7.4": "2019-11-22T23:32:12.082Z", "7.8.0": "2020-01-12T00:16:32.421Z", "7.8.3": "2020-01-13T21:41:33.951Z", "7.10.1": "2020-05-27T22:07:20.638Z", "7.10.4": "2020-06-30T13:12:02.621Z", "7.12.1": "2020-10-15T22:40:11.000Z", "7.12.13": "2021-02-03T01:10:55.029Z", "7.14.5": "2021-06-09T23:12:03.361Z", "7.16.0": "2021-10-29T23:47:30.571Z", "7.16.5": "2021-12-13T22:28:15.982Z", "7.16.7": "2021-12-31T00:21:58.447Z", "7.17.12": "2022-05-16T19:32:53.115Z", "7.18.6": "2022-06-27T19:50:00.382Z", "7.18.9": "2022-07-18T09:17:32.892Z", "7.21.4-esm": "2023-04-04T14:09:24.146Z", "7.21.4-esm.1": "2023-04-04T14:21:12.776Z", "7.21.4-esm.2": "2023-04-04T14:39:16.237Z", "7.21.4-esm.3": "2023-04-04T14:56:07.301Z", "7.21.4-esm.4": "2023-04-04T15:13:19.867Z", "7.22.5": "2023-06-08T18:21:17.513Z", "8.0.0-alpha.0": "2023-07-20T13:59:51.718Z", "8.0.0-alpha.1": "2023-07-24T17:51:47.138Z", "8.0.0-alpha.2": "2023-08-09T15:14:51.468Z", "8.0.0-alpha.3": "2023-09-26T14:56:48.041Z", "8.0.0-alpha.4": "2023-10-12T02:06:14.097Z", "7.23.3": "2023-11-09T07:03:51.973Z", "8.0.0-alpha.5": "2023-12-11T15:18:37.175Z", "8.0.0-alpha.6": "2024-01-26T16:13:57.827Z", "8.0.0-alpha.7": "2024-02-28T14:04:47.224Z", "7.24.1": "2024-03-19T09:48:39.060Z", "8.0.0-alpha.8": "2024-04-04T13:19:46.916Z", "7.24.6": "2024-05-24T12:24:28.476Z", "8.0.0-alpha.9": "2024-06-03T14:04:11.796Z", "8.0.0-alpha.10": "2024-06-04T11:19:59.433Z", "7.24.7": "2024-06-05T13:15:18.666Z", "8.0.0-alpha.11": "2024-06-07T09:15:28.116Z", "8.0.0-alpha.12": "2024-07-26T17:33:24.967Z", "7.25.2": "2024-07-30T02:54:48.064Z", "7.25.7": "2024-10-02T15:14:44.426Z", "7.25.9": "2024-10-22T15:21:02.810Z", "8.0.0-alpha.13": "2024-10-25T13:54:06.597Z", "8.0.0-alpha.14": "2024-12-06T16:53:57.453Z", "8.0.0-alpha.15": "2025-01-10T17:24:22.762Z", "8.0.0-alpha.16": "2025-02-14T11:58:59.172Z", "8.0.0-alpha.17": "2025-03-11T18:24:50.584Z", "7.27.1": "2025-04-30T15:08:48.982Z", "8.0.0-beta.0": "2025-05-30T15:51:00.776Z", "8.0.0-beta.1": "2025-07-02T09:04:13.914Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-literals", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-literals"}, "description": "Compile ES2015 unicode string and number literals to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}