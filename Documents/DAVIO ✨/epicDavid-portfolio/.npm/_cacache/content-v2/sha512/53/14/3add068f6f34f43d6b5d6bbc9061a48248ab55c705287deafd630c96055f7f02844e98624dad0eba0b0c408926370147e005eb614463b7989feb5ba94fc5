{"_id": "strip-ansi", "_rev": "61-80aa091a3c7079e8d0d82e393642bda1", "name": "strip-ansi", "description": "Strip ANSI escape codes from a string", "dist-tags": {"latest": "7.1.0", "version6": "6.0.1"}, "versions": {"0.1.0": {"name": "strip-ansi", "version": "0.1.0", "description": "Strip ANSI color codes", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "~1.x"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.1.0", "dist": {"shasum": "1bc16b35788d9bdaaf0b9ef0d9e35b0b59403f8e", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.0.tgz", "integrity": "sha512-2NGHiYkECyk5yzM+qn6FFTX07zl31OH8zufkGjJHoCUz0em6JxK0IiXct0X9XQYoWZtyUJtLj9O1htWDJUUWgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNnWlqoGWT2QfLoHeEAp/PXhNCWzFOsaIB8pURep3SLwIgR2sa0tnZZhwX5b5rHQH0stSGpwpXBVbQVV45zgupVNo="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "strip-ansi", "version": "0.1.1", "description": "Strip ANSI escape codes (used for colorizing strings in the terminal)", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "~1.x"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.1.1", "dist": {"shasum": "39e8a98d044d150660abe4a6808acf70bb7bc991", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz", "integrity": "sha512-behete+3uqxecWlDAm5lmskaSaISA+ThQ4oNNBDTBJt0x2ppR6IPqfZNuj6BLaLJ/Sji4TPZlcRyOis8wXQTLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBsyo1Uuh7ELdsAFZ/Q+WMbdRgKWe602iN1rFSfS1lBIAiAK+UCJ6khc7tmAmaZPxlWoAmtR2a4NhoKosWs4H0vG0g=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "strip-ansi", "version": "0.2.0", "description": "Strip ANSI escape codes (used for colorizing strings in the terminal)", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.2.0", "dist": {"shasum": "e987d2c9128aadd85b9b9bfda7d09d9751e5e53a", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.2.0.tgz", "integrity": "sha512-BbWY/sXA9PsDX4Rv4gE1SSIp2/RvgdYN1XLEdX/WmpHI2HgBett0xGW/H2sHIuVCuiXA6NqhPGYHOG7UD8lIjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIqVUBaTo3bJUzL45Oc+Cgi7ooLUzscPoca1ZGDQkUZAiEAy44ZYgyqgxzCsE1QJQhYN7sy+jCxZ/aGQS5ygtLvjoA="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "strip-ansi", "version": "0.2.1", "description": "Strip ANSI escape codes (used for colorizing strings in the terminal)", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.2.1", "dist": {"shasum": "09218505ccf1083a41460150571355efabaa126e", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.2.1.tgz", "integrity": "sha512-R68IjmqLgh4z6EFuOtgi+dpN3q6seBFdj7Xdw8z3gCBxvEzAEdunZDPEQvxNfbnD9i79kHLH36kZpMyGT6YjQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRouLs0K92Fxdsk2d1/jUaEpbc6DBzC5RygtmrCWMjqwIgK5eTbTpDNJ6kmSojeKhzpH1FOl3qvu7GgyNIhlzCOUQ="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "strip-ansi", "version": "0.2.2", "description": "Strip ANSI escape codes", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^0.1.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.2.2", "_shasum": "854d290c981525fc8c397a910b025ae2d54ffc08", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "854d290c981525fc8c397a910b025ae2d54ffc08", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.2.2.tgz", "integrity": "sha512-1uWiYlJgR31DGid87j930IpH4YUsxX3hOLvdGmnUAm++BWpEkWXMWYcQCUDXUNdEsRA9GZevI4q8UszDfozHxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF7BsNeBO/LWEMf97vXd+sgvIBkTUYpBuQFWcjgX8P2BAiBBm7Hk5sM/kKd/34dXHckRzMB99ahJ/dEBDAe4PCwmxA=="}]}, "directories": {}}, "0.3.0": {"name": "strip-ansi", "version": "0.3.0", "description": "Strip ANSI escape codes", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^0.2.1"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.3.0", "_shasum": "25f48ea22ca79187f3174a4db8759347bb126220", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "25f48ea22ca79187f3174a4db8759347bb126220", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha512-DerhZL7j6i6/nEnVG0qViKXI0OKouvvpsAiaj7c+LfqZZZxdwZtv8+UiA/w4VUJpT8UzX0pR1dcHOii1GbmruQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDoCQub4jFG0Lwjk/zDKHw0sVsD4vtO0+FrnbDxom/JgAiB+rPpojJbxT/E4fb/Tj2GUodqj6iHmhC4+taxLGhCZVg=="}]}, "directories": {}}, "1.0.0": {"name": "strip-ansi", "version": "1.0.0", "description": "Strip ANSI escape codes", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^0.2.1"}, "devDependencies": {"mocha": "*"}, "gitHead": "6fea2ef935f1ba10d43e4c4d9814af328803935c", "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@1.0.0", "_shasum": "6c021321d6ece161a3c608fbab268c7328901c73", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "6c021321d6ece161a3c608fbab268c7328901c73", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-1.0.0.tgz", "integrity": "sha512-3S8K1Sk1jMQRBBg/6snsargJuyOsZGPqQ/R2AWYjuaamfmW1Pvoaup1aO9aTp+eVHQwO8p9y4LACjes7yHaDPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH7hvpfymJRMeCTt+L1PSV/KmgNBI+Z+7cguiOqY31nfAiBQSw37P/xv1FSa1sCyeEB2OqUd9g2wQZHjif5xo+xQYQ=="}]}, "directories": {}}, "2.0.0": {"name": "strip-ansi", "version": "2.0.0", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^1.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "c5e780acc07532f5d651cfb6ea035198095c6c74", "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@2.0.0", "_shasum": "fa8d69432e97674746f55f51d076ae78b18df13f", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "fa8d69432e97674746f55f51d076ae78b18df13f", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-2.0.0.tgz", "integrity": "sha512-gvB0HQXLFIBkRAIlK98dOnXG/fEtNREVj2f6q61+H2GlCH7uFKhZGCdNFPFUA2XbJx9MyZPDwQHEfhPbSTLIlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICajvOg7VzIM+lOdUW9PFDL0C3NjBDKwZo5XLe8R3UQaAiAQM2myHoxr9ynh8kq+QdWLi2AMhNFt+JCqR9fqQzXb1A=="}]}, "directories": {}}, "2.0.1": {"name": "strip-ansi", "version": "2.0.1", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^1.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "1eff0936c01f89efa312d9d51deed137259871a1", "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@2.0.1", "_shasum": "df62c1aa94ed2f114e1d0f21fd1d50482b79a60e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "df62c1aa94ed2f114e1d0f21fd1d50482b79a60e", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-2.0.1.tgz", "integrity": "sha512-2h8q2CP3EeOhDJ+jd932PRMpa3/pOJFGoF22J1U/DNbEK2gSW2DqeF46VjCXsSQXhC+k/l8/gaaRBQKL6hUPfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDI1c5lmGcbfzoonXY+DdFUAlpPUMImJyFzhEO0u48BpQIgMSnkpV57gfbe4AEyrHHUUqIx/PkQ42s1WPQi/FA/9eM="}]}, "directories": {}}, "3.0.0": {"name": "strip-ansi", "version": "3.0.0", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^2.0.0"}, "devDependencies": {"ava": "0.0.4"}, "gitHead": "3f05b9810e1438f946e2eb84ee854cc00b972e9e", "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@3.0.0", "_shasum": "7510b665567ca914ccb5d7e072763ac968be3724", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7510b665567ca914ccb5d7e072763ac968be3724", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.0.tgz", "integrity": "sha512-leU/wDjPxUZLHqEy4mYnNETRx1jAO1BJ6oTgn9PQWeAiO8kfng2LBq1iXYsN8UYPPRmAizc51nUY6w2Nv12Hog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ3YeqqIpNJftJUsBUYSYy6D/Rdcy3k09cVp7vKLcAUwIgdzEJ6OAzrt8vjYIvtNwh5m9QztufQ0K9wQyKVjzv5lo="}]}, "directories": {}}, "3.0.1": {"name": "strip-ansi", "version": "3.0.1", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "8270705c704956da865623e564eba4875c3ea17f", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi", "_id": "strip-ansi@3.0.1", "_shasum": "6a385fb8853d952d5ff05d0e8aaf94278dc63dcf", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "6a385fb8853d952d5ff05d0e8aaf94278dc63dcf", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFaEl9LHs88NUayvguLjCAIgHNA3JIgshtdDJnck1hKOAiAFQvDk5H6FJsNz97+J6huorwnQipRsxxDZ4ZmKkqL4Hw=="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/strip-ansi-3.0.1.tgz_1456057278183_0.28958667791448534"}, "directories": {}}, "4.0.0": {"name": "strip-ansi", "version": "4.0.0", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "c299056a42b31d7a479d6a89b41318b2a2462cc7", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@4.0.0", "_shasum": "a8479022eb1ac368a871389b635262c505ee368f", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a8479022eb1ac368a871389b635262c505ee368f", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCHb1Csy8k35kjhKC6q92oiFt2kuQV/8WevQ9+AO1edAIgYgwiSmWPF1eN76CL+Wfe3JZrRsrG1KfL3LkykZgVr1s="}]}, "maintainers": [{"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi-4.0.0.tgz_1497986904730_0.4528853143565357"}, "directories": {}}, "5.0.0": {"name": "strip-ansi", "version": "5.0.0", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^4.0.0"}, "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "dfab6777144e0292c7b4be9969c180025d7d2d97", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@5.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Uu7gQyZI7J7gn5qLn1Np3G9vcYGTVqB+lFTytnDJv83dd8T22aGH451P3jueT2/QemInJDfxHB5Tde5OzgG1Ow==", "shasum": "f78f68b5d0866c20b2c9b8c61b5298508dc8756f", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.0.0.tgz", "fileCount": 4, "unpackedSize": 3412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbr9G4CRA9TVsSAnZWagAAQucP/R1vcZ5bj7kPYixt9IeB\nT/gUsBdQ8Q8qxCcSAZSg7im+U8oR5aX84H1wSPKgRKC+QQISmPz+ZKFdQfvP\n9S8A2u8bcblQ7Z1FgPckh3OqqX/tk1m1AnD1NY1munfEYNBhsNwAGyK0jrBd\na/caDWoQ6oZuTt/s4tGomj+Idr/OY1b5CFPgG+8IyxzrjOe+EGKQnN3BtaaS\nonaUce2eTShorppFbz4D3EzKkX8oHP7aAAJtahmOBWhEMees5gG7maFcYEyR\nzvLfmHfWqCudAtJUlrxRa9ofg7WN1drz+xfXNq2O+EWSNowogKMrOkktd6BM\ndFGwg2U8Q+bgBP3I8lKj2Bh0pcm2ZkKwqz1nZNQm3/eBlm6viKjkvAnIw71s\ntjUaz9bvw69himQ4m6D/lEs2SEyw8bu8ZqxEy1OZWlJcp3j6ZeyKqp7BY3W3\nnbSd4XqgYbTRKW+ui7DzKcjOpfQAFMTaD8Lnzj3fVp2bjrhoGNSkmIGbkzZp\nPsQTbrsxMqVyAaJ6WHZnmnpPQODOCI7hZJUDR4U3AFO+vPvi4IZnC6yCJzxO\nViOFuehtQl3G/L9IDctQ2uo1ELsd5M5oguuZrA1HiU0MjjNsqbbV8+HhGCpl\npUuKdGKHVK4VBbortFYOYWmJ9+mJBamLzFXaURBpSp5B3H1tVPiClF396/BA\nEGrL\r\n=IhIN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID17YW9H3VF2pdcQNbR3Qr+hRrgCQ9ZrBHCcuu4nTAi2AiEAxl1Z02ILPB60QnzlyunFd/JE4T0pENucKP0wSg5AMvo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_5.0.0_1538249144142_0.452622585951427"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "strip-ansi", "version": "5.1.0", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^4.1.0"}, "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "581fd4e47612f8c1611b28a553dc855fe176c902", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@5.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TjxrkPONqO2Z8QDCpeE2j6n0M6EwxzyDgzEeGp+FbdvaJAt//ClYi6W5my+3ROlC/hZX2KACUwDfK49Ka5eDvg==", "shasum": "55aaa54e33b4c0649a7338a43437b1887d153ec4", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.1.0.tgz", "fileCount": 4, "unpackedSize": 3671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgh1ACRA9TVsSAnZWagAAzVsP/3g5xkXRghz4K3k7WZwx\n7HJsOipIiVC7yGdYJubN/CcutejHgsUdJmCbJY6kOTIAL/+bu9yPdGrj7A+m\n/2Num8Dgw3O529TLC0PkEpqUR1WDqIwnJPXZIE15XCJkaTMeJQshMjERA3PC\nxT3WXBEQ8ZvPDZ+aERER2cMqZ8J/fADtj4vKu+e611Fhy4aeQxFckUvxIyB7\nbQK5oVSGXVbJoKZ2YxIBmiqe+sAKDhS6J2N7QpMY82undBQPtABdVabt3Hoc\nh/tUF013UNd2MxpobcMCRSSlte3/Dh1Q/kX47W3ZMKPXOinn+28+Y2OOV<PERSON>AN\noaFMdoGNTXd1RQZRJrCp8oKxAk44Ugvhl44P4KPhv3OvYN9FLzgk1UQLemWz\nsDV40VPp0irbSyDCZ8MOMhczaqL8nPZlCOCLgWbx3jYIHPNgR/+rvvrzrKSu\n2R2AEaz68cLayFCvI9tsmxnr3AxilxbPJsOFjozMSYpjmNb5TUT9Uz5URKwn\nxmO6zwLwKThB4JW/IUcbQSid0+yQBVEag/3VqwYQER0lYD4gdID7GKoy/t+Q\ne3kHmST1B3V3wNgX3A7zBOPUizyAWMnamKV+55TCKBMsaMWfK2jhOIOxofhQ\ncYtBGeDS/FSaLkvFPi9R0GOVg6X0K+uBCAVwB+sug1Gt6LeMgFUE47wURlkI\nUa+J\r\n=WC7w\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDd+KdcxDjn+n/NnB7GRKcTLqTMkAPuO/uoT2pAVcwB6wIfZscePwWSKlPQ8swpIrFFk34y0Ckt/Kog6kGOW86jhg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_5.1.0_1552031040383_0.20289783992388477"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "strip-ansi", "version": "5.2.0", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^4.1.0"}, "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "b9c492921b72c48f93568565dbdc929cf63c20e1", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@5.2.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==", "shasum": "8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "fileCount": 5, "unpackedSize": 4171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj1rbCRA9TVsSAnZWagAAokAP/RiU1bcOVYwy9qLpHlnF\nnR33B+BKYs0XId+BEunXYZZ7iFLFUmo7v7Topp1G5gaTnmEfi5B2foX5Gbmq\n1baLKEDUXzd3tkM4AoLDY2V3NiQzyqBb+AP6J/7Y2dNRv6l2s+HKAyppNDbK\ndfQuBC8QAguLpTECuQlPuRvUjyw1307OTmY0/aX2S17WiChnIAcai6yCJIM1\nNk242zAMv187S7QP0UAqrHLqgM/CoOg5TjBa3mu6/JSLdumlNPMzJbAXVYye\n0fiaRRKNelZ47cQL2kkZwhiJJW3B9lLneYchYtDyJ9TrNInVhradEw4hzeER\nEXsvImEa9MjziANjU6RERMnOu8rqtgwikzwzWcAqiMzZ0zQw8Dz14cB6w3Z2\n7k5EnQbSbXka8ItCqtIg7VEAJsP8LyrcBJBUgk1sdsU/laFKfbHpNoHQgIxM\nXGjvMJiKIpe95/GLwROQ0T3UG2vUHGjFjgFABCKODbl9rt/6+hRoI5iHMJKA\nTPxIOBZU4gcnhwkOsSgQtRk4rEvVl6uxtc24ZTNHEyDcVoKk6v0Zb1y+9xTt\nSd/NI5/KCp28cEKC63sFJlLGvz2BIPjE2B4838Xqem3g1jIm409ewRrud8eV\n9xpNwrHKxkJZN6ts/x2MLkR0W23+DunhD6tMKmUuNS60SDBduZlINyh9IEGl\neZM8\r\n=9U0k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICc+u58igTYTNEWaqcpDh3fM1tirpDD+RrX7PNrCshqIAiAnbkhCDhnyt8+G5ztDafS3ju6H5wnpY2v3T/AnXwrxpQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_5.2.0_1552898778714_0.7914614281989267"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "strip-ansi", "version": "6.0.0", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^5.0.0"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.10.0", "xo": "^0.25.3"}, "gitHead": "59533da99981f9d550de1ae0eb9d1a93c2383be3", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@6.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==", "shasum": "0b1571dd7669ccd4f3e06e14ef1eed26225ae532", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.0.tgz", "fileCount": 5, "unpackedSize": 4029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxlq1CRA9TVsSAnZWagAAp2MP/3lrauee3M2/7vTL382n\nOQ5m4UAoMUVVUMyP0qJ0ZxvGYXzJL2GdQO79m/6CxS/pXJ/9itV+C6Mzdf9P\nsk93LW7L4BvjsoVWfS66/ZLeN65Ae8KrBZ2WC4qGgFLU1wSMGezgYxIKNg/e\nQfdHY0GkIR4q/Ls1zdu2LyAKz45Xezhb4Fh1LJe8uma+TDGGrjuGKxarlSfi\nwt5hyV3nwklhEdpPzmg8igo1KgmwYtXZ5KFC0jAG8pxPIEMXHc0AO8vdsNLH\ntwl/U/XIFFsWOnZJouqbx2CmGnQcMSp0B/IU0JPa1ltG2uq8xzs4zqdLkunB\nEDqefhFaHsC995UF0Ge30aiZjejv6MZKpnaIk+wSiJ9ZbEftlRIpkMvo7I0e\nFGw3EXMsldSvzNZjNnV+J8CQ1nUPR2CNxBcmxWKI6xomcKzQpw7JMjC8yyEP\nVl6cAbG0A4PJeS0OAeuP/EVZunxU341bjhvS9UgsUyUDxZtLwmtFCjdTGqvz\n2Kp0YpzZxO8GNlO0Y7ZNZII/5NwPGxwCHE4jUJ45IdeCf3w6xRSXoyGfUAIu\n8HY0IypSsf7RV+rkQAG1ghWseJpf39YLHpRhsG7oQgls8B3rpJIikLQ3zudT\nEVSnDtng76W0wAjMcGckYU6eupoxPcjGNA3ZSfAgZge/nWc87SIbisPoOdlV\nM1kE\r\n=4vOM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEWuVgJUbXRHKiCwBvGaMrRhTdlIXp1XjvcTtZCOmRq+AiEA11i8p49GsLI4n8reFqryuqxJDTKkG7ynfJ2cD1bUIRo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_6.0.0_1573280436501_0.6687217429641616"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "strip-ansi", "version": "7.0.0", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "funding": "https://github.com/chalk/strip-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^6.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "316ff53b074b4b1465e18a35e5d792b27ecc2865", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@7.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UhDTSnGF1dc0DRbUqr1aXwNoY3RgVkSWG8BrpnuFIxhP57IqbS7IRta2Gfiavds4yCxc5+fEAVVOgBZWnYkvzg==", "shasum": "1dc49b980c3a4100366617adac59327eefdefcb0", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.0.tgz", "fileCount": 5, "unpackedSize": 4090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeSnGCRA9TVsSAnZWagAAi3UP/i+3y6k232AGkS8pt3Xq\nZ92w4RKlSluF7mKKBhIdI1H2T0B3dCne5OBJK8fFk8N0TEhqgGKiL9aMbX7m\ngAgSyjB0pk5LOc+wYjgAHTF4H/4KZdn2F7yLm5+pt8EV4fhZYwega7I/jOJs\nIlCSN2KjFYh6ARBpFNb2DpsN0XG/arM3WbguwwI7QtDPabP5Wlg9fcdKHweK\nMVUP/ZS8qIDskk3rXZmmzVdh/dhHZm1rrrgvz1DoC1SG16OizxUQX+yRFYtK\n6/Ho3EB3AC/WrDSeozT84/76YJW6o42XbHGJIVhBBwr1lOI6I7zf1hJQpica\nm3VFpHRggyyDfXo96eG1qA5NbrqIrG4ExuQTYdRKTkqpZIliQ4apOH3nmBzS\nZn0uA4Es7kodNicZ1/SYit5c2X5R4n1Q5HPINWaT07L659VqBY3FuqfJVxSF\nivMCjMGiCeh7fpuCAFNFnJvGFvZlEWG7Q5Dtytw6UPlAAT+bZgecHZsIn6Fi\nB1jGMgZ3MzV93n+KGhje8kbei5p8KP+gkK48nAx7fmvVyoGjzqQFZpPXG5jg\nkdvd1z16sFoDWgGql+zMjAIeG0wiWYm0GrtEhhda1gClzTXw+PssMZpzZ6YM\nmXJPVElXXlJ+bTrMGZyMPFLa9EYCkVAW3tv6TpFol7wmiP0mM7dGFDxG9Tst\nZ8Mo\r\n=zyq5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICFJ67nkD3arSe0aDPGTXY6sBYzCWnbSFIq0y8WwatubAiBSwWZ3C0R25cxdueDI+G4bU3BLSvk63EHFxkn29u+8VA=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_7.0.0_1618553285783_0.31328956863503676"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "strip-ansi", "version": "7.0.1", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "funding": "https://github.com/chalk/strip-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^6.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "dd40fa7ced678f14dfb43eb9b62b8e7313fb7011", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@7.0.1", "_nodeVersion": "14.17.5", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==", "shasum": "61740a08ce36b61e50e65653f07060d000975fb2", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.1.tgz", "fileCount": 5, "unpackedSize": 4090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh28O/CRA9TVsSAnZWagAAK+QP/R0BPwbqYKm2cKZ+Cus/\nkbU1maxjoVw7zikegwh/3jUfVA81v5YaxfkvrAq0Z+C0NXC1iJ3zlVCXTzDi\n0jcnNH6n4nV7P6PHYRuK+opNRD1nrsr/8oyowR8NkWPxJVrwaY2ytc8t+qYv\n+gKWENM7MRdcLRYlKI3PQ+hD0ViiZv0OYNqR3AmVOWRNAqSN5OprG3Q3piNM\n42mS2bdT5APpY8H2JfT4c4C5EVLXmJS5mAHuFFY+PP1/h4gnTXBLnWCePbpO\nXN5qT+bNCocXUt7WI2+gf7IL1QY9G+n2NbPMH/xBP1+pOYY8jBZ4kU64obvW\n/7YcHKjoFukd/Rq3Ud2Soj75GvABnJ6VKfZhhMLOq2n4J3wR5lFfRcnYNpNG\nEhXbQHr9C0bjeW0obOGy3ZP5RL3gm9G2h0N1C9tTkxM9MsqlBw9Wn1o3x3cd\ngBJnPHwJTm/X/PmOvFRsbf2/CykAZMYjxkNlqdV1YrJLeYYu4ZWbkxThPWu3\nSKek7ChwkIPCeOVg6Hhqd76CXbndzjNTxT4eb5/lnQypALG680yB/AVFdRlI\no+M7eP8z5FF8i0BFV2bU6+b6ldsLUzBAlxBJyo7BuCOX5XSJEHExKItV1XwW\ncVDenzuNgOrS5t2wbE9jH/8pinueBAJllC1bi6CGBvR8jTSlWhabkJkuEnse\n6q5t\r\n=C27g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn4rHfbYwQwEPLxQRIm4QfxT9rWZDiINZP0MqM6LICKgIhAJTz8zQ3KFbr1ayTgxAO2X4LKInMfqQutoaHj2sTNW0W"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_7.0.1_1631350246326_0.79864745235274"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "strip-ansi", "version": "6.0.1", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^5.0.1"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.10.0", "xo": "^0.25.3"}, "readme": "# strip-ansi [![Build Status](https://travis-ci.org/chalk/strip-ansi.svg?branch=master)](https://travis-ci.org/chalk/strip-ansi)\n\n> Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string\n\n\n## Install\n\n```\n$ npm install strip-ansi\n```\n\n\n## Usage\n\n```js\nconst stripAnsi = require('strip-ansi');\n\nstripAnsi('\\u001B[4mUnicorn\\u001B[0m');\n//=> 'Unicorn'\n\nstripAnsi('\\u001B]8;;https://github.com\\u0007Click\\u001B]8;;\\u0007');\n//=> 'Click'\n```\n\n\n## strip-ansi for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of strip-ansi and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-strip-ansi?utm_source=npm-strip-ansi&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n\n## Related\n\n- [strip-ansi-cli](https://github.com/chalk/strip-ansi-cli) - CLI for this module\n- [strip-ansi-stream](https://github.com/chalk/strip-ansi-stream) - Streaming version of this module\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n", "readmeFilename": "readme.md", "gitHead": "59533da99981f9d550de1ae0eb9d1a93c2383be3", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@6.0.1", "_nodeVersion": "16.9.1", "_npmVersion": "7.5.4", "dist": {"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "shasum": "9e26c63d30f53443e9489495b2105d37b67a85d9", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "fileCount": 5, "unpackedSize": 4029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIaIvQJAMRlSerQ8cuHIzWxWLJPS6LkkF/9tMpiAXTvAiBY3tMFoj8tB0mc3dm7Th97E9p88NfZfjOdr/HKu8apLw=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_6.0.1_1632414881633_0.39929061070208505"}, "_hasShrinkwrap": false}, "7.1.0": {"name": "strip-ansi", "version": "7.1.0", "description": "Strip ANSI escape codes from a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "funding": "https://github.com/chalk/strip-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^6.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "1fdc531d4046cbaa830460f5c74452bf1f0a0884", "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "_id": "strip-ansi@7.1.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==", "shasum": "d5b6568ca689d8561370b0707685d22434faff45", "tarball": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "fileCount": 5, "unpackedSize": 4321, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLcWbjmD7JZAQ0csSDb2mo3VnE7r9e8bQPjsVd2SmvmQIhAKTReWeeAy2ZNI3H+RItkH7tubYl4uZ/Jg4hN4TzQKAB"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-ansi_7.1.0_1685267704153_0.8565336218552002"}, "_hasShrinkwrap": false}}, "readme": "# strip-ansi\n\n> Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string\n\n## Install\n\n```\n$ npm install strip-ansi\n```\n\n## Usage\n\n```js\nimport stripAnsi from 'strip-ansi';\n\nstripAnsi('\\u001B[4mUnicorn\\u001B[0m');\n//=> 'Unicorn'\n\nstripAnsi('\\u001B]8;;https://github.com\\u0007Click\\u001B]8;;\\u0007');\n//=> 'Click'\n```\n\n## strip-ansi for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of strip-ansi and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-strip-ansi?utm_source=npm-strip-ansi&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n## Related\n\n- [strip-ansi-cli](https://github.com/chalk/strip-ansi-cli) - CLI for this module\n- [strip-ansi-stream](https://github.com/chalk/strip-ansi-stream) - Streaming version of this module\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "time": {"modified": "2023-05-28T09:55:04.460Z", "created": "2013-12-07T23:30:14.926Z", "0.1.0": "2013-12-07T23:30:17.673Z", "0.1.1": "2013-12-08T00:12:22.401Z", "0.2.0": "2014-03-26T15:28:35.327Z", "0.2.1": "2014-04-28T10:05:45.285Z", "0.2.2": "2014-06-03T17:09:40.794Z", "0.3.0": "2014-06-24T19:33:15.394Z", "1.0.0": "2014-07-23T22:31:46.786Z", "2.0.0": "2014-08-13T13:36:36.745Z", "2.0.1": "2015-01-16T18:48:44.952Z", "3.0.0": "2015-06-30T16:53:11.137Z", "3.0.1": "2016-02-21T12:21:22.998Z", "4.0.0": "2017-06-20T19:28:26.510Z", "5.0.0": "2018-09-29T19:25:44.295Z", "5.1.0": "2019-03-08T07:44:00.512Z", "5.2.0": "2019-03-18T08:46:18.955Z", "6.0.0": "2019-11-09T06:20:36.658Z", "7.0.0": "2021-04-16T06:08:05.929Z", "7.0.1": "2021-09-11T08:50:46.669Z", "6.0.1": "2021-09-23T16:34:41.798Z", "7.1.0": "2023-05-28T09:55:04.358Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"tunnckocore": true, "lenage": true, "recursion_excursion": true, "scottfreecode": true, "mojaray2k": true, "insomniaqc": true, "kontrax": true, "usex": true, "d-band": true, "zhenguo.zhao": true, "houzhanfeng": true, "edwardxyt": true, "soenkekluth": true, "flumpus-dev": true}}