{"_id": "nwsapi", "_rev": "41-7c6a692a8f750aa35c341aee36cb3561", "name": "nwsapi", "dist-tags": {"latest": "2.2.20"}, "versions": {"2.0.0-beta": {"name": "nwsapi", "version": "2.0.0-beta", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0-beta", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "c8249f1493b98e55be782deb52d83d2f282983ca", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0-beta.tgz", "fileCount": 247, "integrity": "sha512-RgmxLxJzkWBcptLaD8RRKnckwUIRBtCITJ0OIoHW0n90FRAJ57JyXf18XJHvzlJrxDqZNVNoe3TX8UOd6I05Xg==", "signatures": [{"sig": "MEUCIQDbEz06wvOYdPtZMtw9d/j5NXib5OK0lMiTCLUNC3YJ0wIgOWO5j+TLE59ibAyKz8u451SGtAbnK9zDwe8FLh398hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15246890}, "main": "./src/nwsapi", "gitHead": "eb88f3ea5e68facdc68ad4bcff63b4e77555e857", "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0-beta_1522255842289_0.9632574185976395", "host": "s3://npm-registry-packages"}}, "2.0.0-beta1": {"name": "nwsapi", "version": "2.0.0-beta1", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0-beta1", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "bc9e3c76acba5bbe74adcc502379e229b8a842af", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0-beta1.tgz", "fileCount": 309, "integrity": "sha512-jFpvckBwhFarpdCTOQ3Uwk2coT1Wh14FT+w4QGaFHaXHbNyJLpjUg4DJHDWxWl0kIpG8A7z0GyGF9XpzyAQROg==", "signatures": [{"sig": "MEQCIGghkSXCmS+4IIRRsSFjyWIfJKLhf3cRtdDtCVLs/LvkAiByDhZlcEwipdfJK+hlFZ0x8kFSuIkSTcd5A/70PoO4jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16102748}, "main": "./src/nwsapi", "gitHead": "c6ac5bd06c778e4b0d40a6a19b221993fb9fe49f", "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"npm": "^5.8.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0-beta1_1523528051625_0.9146108767287287", "host": "s3://npm-registry-packages"}}, "2.0.0-beta2": {"name": "nwsapi", "version": "2.0.0-beta2", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0-beta2", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "8cfa736127dd949be4c24e5c0d94e88d1014ddad", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0-beta2.tgz", "fileCount": 345, "integrity": "sha512-NFDYaU2bqiHPXeV943bOcqug2cQqm1Fs2NrZi5sTNGbcwM8h7C2z2eT0pIe5ZTKDVR7vsUvQtDxCbQdOvB5Y7g==", "signatures": [{"sig": "MEQCIHhHchrjiS2HRuoJlR87Z8glMauxjMKfjLNliv9DIHXiAiAvQ0Xyg+wOUJK2gtHPzCxfX7oRFxf4rD9Srq+RCV0qvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16051587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1gWuCRA9TVsSAnZWagAA3OUP/jgdJ2hYEYgeU4eDP/U5\nnRthgwalSEBRvafU0HMMwVdXV4nphoH55N0kRIyQwT+aFfQ0dAqNd7+QwokS\nSmLSs3s4fTKYFjorun458J/bRXaeGtr5SkiqDe0zSpSTaqzB3Ez89rAJnqSo\nDvvS2c973l2MQ4azy+aM63qhGcH0rjEa/2HruD6gR/Bkxjj6DKxNTekHZVVv\nb1rKSdNpJ3NVaQ7NNfCHizzrFz/JBSv3ilBR3uVtSPBmlpiIMQgp07ij5A9Q\nm1LUE9drIXHpmo3uI0W3cbk5hkk70H58nOK19hIjUkB7awhqI/WfDTwq4lem\ngDlXfRi5At3ikpnQE3i10mg0Y6sSsH+fd0fLmlBWSvx7m8D/W0/vnMzswFBx\nmmaQTWUwQQ7zOIa64c2Yujy0YDHlz7bHFSrurrITinZFORIT/W2LTjXomBun\nVNs4px6wj9xTxYgonPIyO/DZ/XOUOowyfHTZiQLIMzwGcMCgTOAHGx6HrV+8\nUUpoAMRoMfzJJaj9ubNasi29/JS+0Jo5Up60nSfyL2edJcCHGo+VbHXVIrGt\n1waS++CiN+7BpGEnryYdTQN1Cpgka+u9meMBQtaic2KFHiYSDYm8hP70khd9\nnUE5e3vx3WGtm64OKuoX/zdDvuMTuxIoepGI7fKQRlFetdpuQfZYMu27BnSb\nzwhk\r\n=qKVP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "ed639c9df3df6ad6e63415671232d09fe31e3e59", "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0-beta2_1523975596250_0.6480012059860623", "host": "s3://npm-registry-packages"}}, "2.0.0-beta3": {"name": "nwsapi", "version": "2.0.0-beta3", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0-beta3", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "5a63de0285680e143a149223378060accb072a62", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0-beta3.tgz", "fileCount": 263, "integrity": "sha512-fQtW83lF2gFrbK4pmNTNNaC+WXymgio2R37lzUqGLNA6LtMfSoLaBxkbCEVmsv1aSSmLjo9hKZzCg20TaU7ZPQ==", "signatures": [{"sig": "MEQCIE4Qlx05p66JthCik+Y6rVfzsu6v9uDztqwsf8BiDOfIAiAB0tZgMygW/xZIFepCnOr9/ErmJzuQ1YRNU4b1RtWSkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15853037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+dKuCRA9TVsSAnZWagAAxg8P/Rfb6asgxOj6Cd8bE+xN\nvrjOtm+tHcvG0guBpDnVMgM/FJnQ4FWi61wn8KigqMPveKepb0uanoDnGwLF\n9INFQ7BcPQxsMn4zEYue6Hi2enFklkJhi/xwRfwM/7LMruTNmu/G1ScR6xEZ\nN/9C4TRdUibPMBIRS4JXIFiP42R+n+02/YULdqDcZk8GHHGPfL3OBnYWvx9y\ntOMdsbB0bcnuntKY0cgnqKSzgKS7G9nrkn3X7X7aILJDkzFcPTC1+wv4M7km\nKzotNnrnG8hom3NRUmbj1yOIhzPHFq7rRfWJlYmIpeZcIPfgf47qhZzPwFwd\n1o+ZO0rd9WtUG0M8NlhPe8RnCVMY9BnJkq5ALTNT7PjPIPAtwyxGuxBHN0go\nZLUz6EixhF8FVvMIX3WITGlJEM+zxtWmWXgRdDlQvoRVJJIZpx+OmoyDGPWd\nM7WGPBMya9eglKeul8Go/d2Qz2wFSyTqhF6aoiyhTcePR/kuo/Tb1UES+5VJ\nnoel3lSAzV81q1WARZ1LyOrPO6IIb8K9ggKbHDdqj9SlSuVJnfXER8F8sybr\nY2Tvlz9dPR71cUlNCAJFfPz56FVWvMXgggJVPGoIFcTbJFLLTdedefgXHI3M\nlPU0+dm3kwG9d3jBkYU7WNMoXP3t6nLooB/YnIzPB+x8PYbSu+HG+XmknfgW\ndg5j\r\n=stWd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "94b278a3706b15f08c1312543ac5d7dd423c1b87", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0-beta3_1526321836728_0.8826461054666797", "host": "s3://npm-registry-packages"}}, "2.0.0-beta4": {"name": "nwsapi", "version": "2.0.0-beta4", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0-beta4", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "f66f9b1dfbb52f25e9e37a09a55068259e947e3b", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0-beta4.tgz", "fileCount": 273, "integrity": "sha512-3jJ8a9p08v53hiUOXqUudFgaD/t6PQEHXCcv2Zuqlxziz1cXqFG2Ii6SNDdyGr32cZekUMURHE8FwdKvMEJhnQ==", "signatures": [{"sig": "MEUCIEUue7tQlj9Gtu4Vn1JVvPKCWh6oBJ+pJi2WENWmYy2RAiEAnRUMKv6ZS84AsvXvf/fi/1+6qqD/dvGadU4+SiTz9rY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16459253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/KVICRA9TVsSAnZWagAAb4sP/jjwckL799OvfqPSCNBb\nLg8uxDWawUx3yCxa4EZqp+nn83YzUXH3TJrm4SHjXzeGuOhGpVVFGmOqKpcB\n/aJbvQ63jnnw0NMeAJkuyZcDGqQmwoNgc6ynuSkMKFQAVs7uEddjvY5SrCNe\nZbUohF4TT3kerHVkaTbyAtHUhK1B0J2PAAVZXYT2QXXkNoQ7h3SEUBHFNIqr\ni4tLHI4m9NEfz0CLNk7ph9+et153Mp9yJtHeAROB+/TpiN7LJvWg6wLxcx/8\nvFQwkYfXfYKhuITI0MM/Erle+8HROfml5Q/BZQHv5R0YDS1rkS1My7lhypx7\n76XAkA1djDIGCZvsQ+ZuUG8sPSin2COhyomHbtvZbEg1Oyn6i4V0/HmBkiYz\nS3Xmq5NLauS1coGwbItJOyph8jSYpEDIkwvsSqUw1VVosTRMUJiim3zG4Y7W\nyeiaCLhcLcQNe3ewxPwXdTgxIewzDtIbnjuptEv7xfbloLRaVnprQoCMXbsU\nAz1zXd2J9bwF5TSFVjHurBhpfk+5UJvHJ7WVlkIaVBKZ4A/gSgHQ7Ap88nSP\nV2yl9RzHZdS4mm9/Ebcd36yeUVOSaIpDapD/zS0gvVJVZ3wMB1Xj9daE2p8r\nxOw2na+Oey+pOqSYAU9EycTSo7Ea4oxPQCgrbX1I94iJ39EkXrHt7S5PnWlO\nYe7B\r\n=8XPk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "d833e18a2abffaf480b3818e58eb5cdf0109d8f3", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0-beta4_1526506823383_0.15330216617181014", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "nwsapi", "version": "2.0.0", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "nwsapi@2.0.0", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "7c8faf4ad501e1d17a651ebc5547f966b547c5c7", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.0.tgz", "fileCount": 263, "integrity": "sha512-9kj1oCEDNq+LHDAVPGDPg9+qRcBcpXb1IYC8q89jR8xJvOC2byQwEVsM3W1qQcSPVyzGGaXN7wZHnXORCiZl4w==", "signatures": [{"sig": "MEUCIGKSHnqrGQZJoGbN7qXy4J7HVpzq3cYcrUp1dLNIdywQAiEA9knCsG6j+OLH8Mi0uzzdYkqbQ58EIftRCBWnjGJ6zuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15853647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/d5hCRA9TVsSAnZWagAA3pMP/Aiqy4SdWPaQrG/Mo5qv\n64F7nkUDDwgbf5PGxzCPpdl570GxiV53lSPF6AxPVRdBeBPxcLhZdFxkNeUi\n7bh3RsH5bAQ4Csee8LKEs1gdEKuiYJxvGx+gOFM9t9N1xXqxAoGEH1LRkxmP\na67H6nbCk7Zv4hRF0Q5IkOITqSCMR3GFkww4jafoWLjwsI3AZx6wZ/iZJDOC\nHA2V2+8eQ+q/i5NMjZm8Eo7kFUWmEjykEhLESVyUq7Q3SB58gZ8ky+9eovF1\nC5CH6p+q/bziQMjQclXVY896VwH1dpP0jtG7XZwuSmEYdR4LToUZPYe4Dw/k\nr/71GWZcx3UDXkcI/nceyBGvJcLIHpsLd/NtQwjy10ygdGzE2QXUj7TU3BD9\nIs13N6e6Qa5cyS77ekVBxyiIc7hjCkuWw5IGeD6lfq1mdkw5hljK2h3MbHfu\nSqtyKtJPXxZm4mQbcYzBghy6uck55ymGzEpsdYKv8P/Tg8HHoaEEX0Rug4mv\ncmb2wGQHe2gt9m+qwEG9zhKFAERHHkGH829MemlY1whzkYJEFXaA35E93Sp6\nx08frc7L9W4vGJhL0KCmzjVuw4Kzim12R5YTps9aUuPAxKM8dvxaKwA4dsT3\nnFotYmtC6p3M4c7c7oJ3y0b5eC+bVVnpGWp0MpfcHfeUUMJPvrNHRx39uTbR\naO1W\r\n=aFlG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "13fe14b487099f3cd5622097ff93c4538465cddd", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.0_1526586976568_0.20926553751610166", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "nwsapi", "version": "2.0.1", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.1", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "a50d59a2dcb14b6931401171713ced2d0eb3468f", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-xOJJb7kAAGy6UOklbaIPA0iu/27VMHfAbMUgYJlXz4qRXytIkPGM2vwfbxa+tbaqcqHNsP6RN4eDZlePelWKpQ==", "signatures": [{"sig": "MEUCIQD2BByHJRKtRTI5+ZNRlFFpPnhaVLFZRQFcMc1J4KL9NAIgCDI+h20RjRO1I5grwpQWx4y4ShrIPC5hg9R/pL+2PiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCrDnCRA9TVsSAnZWagAA1TMP/31H++Uv9hEMYbc8Jkah\nuVSFiHi26tqCR7x4HYcmg3j3c+/aBDmfNWVkAe+2jrE5hNuDo2P1W1kO+AIv\nSroQl3I54EBA74Pt+I57J5zha+LbaK75Lm9lhDS1LFlXIV5YSWc59TiHiuSI\nTit72c3pU55jEYkW2RxRG/lqFDPV+DSu3Xs/PXDN0Xt9Zd08FN7P+Q44OQ/j\nzIf6ZzGjIEe4JiGlRwqd/iqzWOn6Y9XaQ+aDy0Pp5cERm9lvOmMckIW0cHyp\nlnxuwNF25H5Ro/n3GohZQYbkJnc9imkavLUYZqJaodh+PoMQCziaH75p6C7+\nASAWJnBN0I7hYPWFnZ4gnw15QNx+zLt6XbPTUqLzeysnJVmAjAnUbn0StRLk\nvBGQH3rMEImOI1zhw/pKioCLmccacVI4Zrbi7TGd677MxR8ebEHoz5GCmZz8\nRtBajBGkgR6iWKCAmjHCqUW6YeUemk2KDTnubK82tgdNbLoQ59UAqGOBn0zs\nUMeKiNK/eIFUg8rX2T5Co1hXUDDupVzRF9hphmffsICjKXp6bPK8zMcgp6yS\nLivsvZwjQuTcIT9tLFGo414d8VADKu8oHP1q0hxiB5HPSY6/CD08mOfKijFn\nehLXdApA4NGB9o5eNC1gmlM925G+hFcJbwiNRCzYIqc8yfbphOXOSKomP5Fs\nca7s\r\n=5kZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "fe3d726fe2e274ed2da2d2152822f2c8e4ad6639", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.1_1527427302310_0.5777793741662738", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "nwsapi", "version": "2.0.2", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.2", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "33a0aab27c678d4dfdbba6a7f84b1c627fc4966f", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.2.tgz", "fileCount": 6, "integrity": "sha512-wAvMV1QgoYPvqfcAmX1M86SyZA3SnNT6UNvjqO/pKHjIZzhMpPHjCTOWdOfkmUknjnvjDq09wDiBaHAzXSLiSA==", "signatures": [{"sig": "MEQCIEy/rGz5WgG3aFkhIKurpFzIIbcqiMLEcHrVYHLQNA22AiAaPeJu2PmiZhdsliEhgWju4+pURUB8ooZ5W99f9rM1aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF810CRA9TVsSAnZWagAARP0QAJfucPGMSSIc5icrRuYJ\nBhCYucBj90QQ53JwPV37in/6/m6BVO3/v4dnZxEMqhs0/+ANvw55OdxA4pVN\n/uMew3EEGzErODg8V+lTqkYTEItFp9OsdHbe61r8ryoQd50X6a0YuwHlE4hp\nMNyjwwWo5vLXKtGfRf8AsQINeleqx/fPiA93627afEwK+SUDn8IDgIhI0GRw\nETwyCKM9KSiyACXmotOGYF1aahY2uN6T88XpUtRQ70HIA4FtCJBiFQ2Gj7Lg\nyZdJ4+1D29f2nu6oxcjYntOMPHAxF910IJeM1Ozh2NxFL+axi+hZsLi6+Yma\n2Vad2munCLU6w5BrAqM+ki3PxoA3Rhl/eQdbwfcx+zl/H5MZAUqqkPyH4iGS\n9ukZPdWpm6AkeHBZLPFLWFjb9C9X6vIWnOYoRBUUn2UiAw+xqVjqa4TJrz9w\nXpX/dCTK3thYEv+q+gXvJOJ9thPjI5Rj6hpwtbdQUwn3LMkcpY+00tdrKL6f\nYIytmkyEyMVHCoBd6sckC/i+MOEQ2Dbgx57TJo0EleE6tvBOpQCKQk8qDDKX\nFOcSitqWqBV8TrB1pFN/zWJI0r2QAMA4KGAPt1yxbXrtpa4VCLHxSTWuHLs3\n1sTMtzQiUaxaWbU1oaPRE+AeEUwTEdwplZQK/c8Efsmg2bu5PdyqeQuVMoFU\n1oHN\r\n=MfH5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "743a9db4f98e1f379bfefb2829c1c4b219502fb8", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.2_1528286579886_0.7208015294641923", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "nwsapi", "version": "2.0.3", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.3", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "3f4010d6c943f34018d3dfb5f2fbc0de90476959", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.3.tgz", "fileCount": 6, "integrity": "sha512-zFJF9lOpg2+uicP0BQKOAfIOqeTp/p8PC669mewxgRkR1hGjne8BMUHk4wpRS9o5Z0icA5Nv04HmGkW31KfMKw==", "signatures": [{"sig": "MEUCIC9eUwPGVg1zbZVCJ7RQnkWRovAmheDGwRTmMvAJK/KoAiEA23sv28Fj9qUT8k4fGZGLX0t/DS2v0r+2JY1mZoq037w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHArSCRA9TVsSAnZWagAAwRUP/1z249yASE0m+qRPO0W/\nt65fXHjnCHt+EV886yPW5iR8Dw2OkxaeH9ZkWTWCUX1MzzoOsYt5U/WmmVyD\neTKPBl7/vP0JGmsenL2lmaM0TfE684411kTisZ3/zkrPlj2FwLCazszKga9U\njtqlrlRNiAiBECtmZPa7kV4lQGIkfMw+p3QzODZG0TOYMv6G5Ybjw7NAfrv1\nfJPNj19LuxiTYfrSQ/0/jc+h9tbkT4Ino5rY0R90ygQsidWBw4EHmX8GsLx7\nzSn1OafYZrSN6UCwM9SNo5fZVjdCcafZ89/KZXgZOw1S9Yj2LXooba7x7VAt\niUzOOj57xTsaMVJazlvIsaWHSm7FWL3ujs3/pXtEaexeBvs4VDN0k7FVifDl\n8TDa3OJlgEFLISQGSc3hOlBrMZkixV0P680+zyAAFLDuhJRQbCqrmJqkqz9q\nfVYTFgIJIRYaXB6+8mweCbFS+mWDqFLmAYu30uJ8Yxkg33+0/QcfNtDkqrTU\n16QAJI+Ku4bbThi2S9HuQzZ3w58boG9DjcRCYeVU4euBo4AF5yUKS2Gfc+/8\nmpeXaR5OyLwhA3MK7HdOJTIaJmnErBGyjF6mWAObr1GoceIv6nO6P/00oecS\nTw1yGPeVBP7DUnBTFD51fAYtcgScXcWLC7r/3BEAL0kt3CuBDRlK8hrKOYf5\n2pov\r\n=n2XA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "4eee3b2307f19f8334188246f9c5bfc654abf9e5", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.3_1528564432683_0.8463168543168451", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "nwsapi", "version": "2.0.4", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.4", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "dc79040a5f77b97716dc79565fc7fc3ef7d50570", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.4.tgz", "fileCount": 6, "integrity": "sha512-Zt6HRR6RcJkuj5/N9zeE7FN6YitRW//hK2wTOwX274IBphbY3Zf5+yn5mZ9v/SzAOTMjQNxZf9KkmPLWn0cV4g==", "signatures": [{"sig": "MEQCIC1Jr6wzj72c/KXLPJ6yUyz87DIQ8kW+i63V3lPMv/XRAiBOozECOSqPMp+SIPK3l2azQIoetxNyOkyBidGIfyRktg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ6nqCRA9TVsSAnZWagAAYv8P/0OJKmV94Zu+7qiDD67M\n0T95K/ijwr6VnjTr8OjpekICDC1DwstDqC7tmxVqQ8MCvesaPCPSGC72XX8W\njwTPtdP7pqnwdcY2hP6WP2bJX8AdRWlrLiLofXli+aKYxPlc24CDsuEgZTP+\nONNGKQyV+MIVzMkeH/VQI1zVU0n7pVrBZJihWq5zTJ2VTuxG2tt3qAz2W+Pu\nuD2j437VNOQtTX48VSHrI6w+d2nMBB39UcTBzBoYlpKlaotICN5NF1R/R5rp\nabNlbMoqVjUjpjOskvy7mvw3pB5RlaaYdmq0eh5xd1v9fv79f4HjogXz4Zda\nrwCG46ca7r4GwcEs35hn0vEgnyt6ud3sAPWlSEQ22G9BtPF1wQTeS2sX2akT\n2glX7EOsk16VmeyJWqq/vatc3zAui1au0w4USdPr+ZUUbBSKRt4t6fo5/GZc\naWGd1lehvxcrd6omou4qKmJMcS8lBMjPr6Sf2AQaQnHnS2ZEqBMNDyhTNgNc\nabwHAUyy99PIfMG1/hU4j4d+mUGt4VIqoXISxsJviWtxBKmA6nCo6h7QmyKC\np8j2ez53WYIpCQAo7e/XkbSjR+/5EdOLf2kh749FidZ2T0y1q0RO2fnglLNl\nkkZqSs4YnEHecO7Coitf71UbdygRC+a3BZXH6qmZRlgnlY0k8zgREbi6EVKH\n0I7b\r\n=MoHL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "920d7d525d716a399ef8d18180b04f7f48172d8b", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.4_1529326057382_0.2647436288379168", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "nwsapi", "version": "2.0.5", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.5", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "3998cfe7a014600e5e30dedb1fef2a4404b2871f", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.5.tgz", "fileCount": 6, "integrity": "sha512-cqfA/wLUW6YbFQLkd5ZKq2SCaZkCoxehU9qt6ccMwH3fHbzUkcien9BzOgfBXfIkxeWnRFKb1ZKmjwaa9MYOMw==", "signatures": [{"sig": "MEUCICxTU1nD5w528T60KRBZijYR3Ljh9bDxOF+XSgZ3ScG4AiEAndRZfhvdQmuiItn0CRHubVJKZWzm+dJx6zMMGox0tSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSSBHCRA9TVsSAnZWagAAn9gP/AoQzFIztvphvA6LDrbm\nB5QIVAhhHbxvi1euZLSqsT9t/3jEKATGIsqyAuBRRBD/gueavOBrpuCMozF5\nHUy5cJNx1V+87taEHYHEw5iR7W91KCdOoYRQ69vHBOY/muCxiCkTIwepaIuO\nUdqk/RPLLTdGhxqEX65T36KnwnwTP7G87UuSByMcz4yAWK7U1HWRIms7WPb9\nKQpnZJYoI/M2Lj3WykxgjEuTlnq3GSLey/z7BOmW0xEvfP1/83aD0RM/zl47\n4MuI+/bfWaHGQ8TK+vQlhicEkVQYpdUVe6vs9QvhpowUnDFfdvoyKNXdJ4CG\nd2Qk/fQvGZdlaBoJArSYJNMopC9xLM7Cy5+XzNLYo9MC2BqCVvphVPdZuTSl\nCrwE178O7PmxssI7Xmjk0wjBtTXFMNElbDSh+BzD/XwmjAM8+DVvtULR9RAo\nvqglO8iHb0iGsS4CcCUKx7ThvN5Bj8pzhh/11Ifpj1coWBeeTxeePhABPUoE\nHn116c/9kvYg+dl1VORk4ovalcRFN+MHyaoGcNWLAMhzA6KXCOWwG5j9kY0M\ny3QeXJ2rViMxFU4Ftix41A0hOaB4i1SEe3sdUNH5fAnIE0WxwRUlAZnH/Eix\nej1mEnA5wJZR8TNeHMk20EYifUNsExddj9GBqInCigYfBIuq+wBFEkHZj47D\nTbgr\r\n=xoc7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "fab0f5ab92c86efce2d08380a6d3df0509c80d97", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.5_1531519047860_0.8991093724587012", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "nwsapi", "version": "2.0.6", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.6", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "5fb7f5b828b97fe1de47eb2a6f8703036b6cb71a", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.6.tgz", "fileCount": 8, "integrity": "sha512-Gue18WcHPYOmviCH3v7wbbzp5uE3RfUxGh/i2i/M+LT0Up5HMtcAWmnj7RtW6HH/YMEtDVF5/nMH36DISU5bpg==", "signatures": [{"sig": "MEYCIQC4d4ESl07hxf1If+vaeCi4UAVufvtzJpycdQvErYxU3gIhANlUGgBLO20mZ+1aa/mASgds2WgjAUA/TpS5wRqXfPzj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTyyvCRA9TVsSAnZWagAAaPQP/3837RVVmE8BgofFVOgg\nbJtU+uqNxpJvJlbZuK8EuJSYeRRGJa5JGeLC1gTfxHZdirpj9eZS5ajfuoUD\nczr/mFrMepUO+03YM3KKWqtpsiXgE7bGbPAYVlXDnvSxH08u/9wkQD6MtqFY\n/RVSNGw24chpcq/Ffq0R8MaVT9oLqYo33XJgWwQ6PQ4not6H3/wWKA+w8lC7\n/vDRUlY/FXkk6cppZ6GeN3Ej6Yt8IXL6gFUqkzxFZ7t4WIRln8xAeD3XBbgA\nJfugWTQLuHH3oCXDilKTlaDqWPCFke+KWHOfh7Aw7VmmjjAgYVXHUfOYopY8\nFLgGMd7Yp9rpFE7S8VEpRqzdLOgh/oMBBLRl2FOTYksB0IRXV/zx2s5RLIKd\nF/KBIhjA9RjqvDPOssRqy99AEjyFBLdidX5tbROuYIUzwh+U0R97KSe6/QNi\nBo+r1cwo16PkwF9/20ypNabw3EtvSCdTXd1nr5uzlCMZcgCE/1MneEVRaWOt\nFv3hI+MMehk9SCDTNsK24C3+O3RfRVLC+8fHvyl5+5IcW7lkAxyXL9hn9lzr\n7qJtZFZA2N4HNGq4g3sebyUmp2o+GRRgmST2Em5qPSi0EI7hSsQaTsbQQkmt\nJCgjrX2BYFKiNqStNzcBfnLQkn1jabGzk/huzKUtVvtfE7yryLXQwZ8B+reR\nInWC\r\n=OpcR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "6fed8332c99d2f6a0307434efe06324689c43602", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.6_1531915439008_0.6200134379196525", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "nwsapi", "version": "2.0.7", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.7", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "6fc54c254621f10cac5225b76e81c74120139b78", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.7.tgz", "fileCount": 10, "integrity": "sha512-VZXniaaaORAXGCNsvUNefsKRQYk8zCzQZ57jalgrpHcU70OrAzKAiN/3plYtH/VPRmZeYyUzQiYfKzcMXC1g5Q==", "signatures": [{"sig": "MEQCIEk1sok52+KzbrhhW9raqg2eCT542ydhvES2wlJbg+mKAiBuXVWhDNXBZ+kA1+m+ixeTMy7D2TVApNpSkt0pSGcuzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUkwoCRA9TVsSAnZWagAAVh0P/AmqWlFkUX3vXPxQUVyD\nVHQKv5N4tlH12KVAz2mcfOBFxIPk61S4qjTxf4wJyIFFAgNO+gTH+br4SU/v\nlSeGcVtpttACEkJ0y55f28H7AfxXIfbEXQJNebhM9NER0oPQguJUnJAmldDt\nVjF9MNI/kB1VQ+vhXc910p0ZrJzKeIlIKfMm1HRG6tpZqz6aN7XIxNXVUY/2\nrvsX5x8wGeNNuXcIV+z/2ruFTQdwE+QtECV5U2cz95LrL8066FKpTgVB/ZsH\npDIqtVHKc7/lhFT8IsjawZgPqOV5dTa3q1XVF0ZN257RhbMRoIhVlahPpcfl\nv2iD8i6o24lF3JK58uHYCiSV9x9oxVTBc9csIcAz7qLdWXnGaG8NpqWIBZYi\nVqFHzN/uhdkZK4kFvUzX4PiC/Oat7JLKJT5fWrv091l8u7Zx8WkHazNHrLjE\n3qDBf8tbM+VMvsh+NnlyJwc8aUg4z8qdFtqr1PWOYrHOLkQTcVi08/vUPDEz\nYIof4JrAezZGYuKr5E/6oPN5XXGycMBRI1w7/IKJaCCgrTKo5JMkrQFnc8AN\nP34QH88vTu2ZaT8s3ERZxhbxhUgRata4CWg1iqFcUF4JpMll3syzLKv0UrEj\n0UCnkxdy2XOIVYuc0tUwaIqPl7uppXbThJQIwICEmStN1odRMsjlfnxrDLnw\nlqQF\r\n=TtYQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "7a598891df76e99cb90e8f2170a181824c56b494", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.7_1532120104900_0.7852382582748572", "host": "s3://npm-registry-packages"}}, "2.0.8": {"name": "nwsapi", "version": "2.0.8", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.8", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "e3603579b7e162b3dbedae4fb24e46f771d8fa24", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.8.tgz", "fileCount": 8, "integrity": "sha512-7RZ+qbFGiVc6v14Y8DSZjPN1wZPOaMbiiP4tzf5eNuyOITAeOIA3cMhjuKUypVIqBgCSg1KaSyAv8Ocq/0ZJ1A==", "signatures": [{"sig": "MEUCIQD3wOQI5rvaKiPsc9y8w2SeVNlGENz1kLT9izvDyLZgAQIgHt0b5jSt9S4yVytoXUroVCka/Dr1UW3IyY9Ua5JQZQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYbJkCRA9TVsSAnZWagAAeUIP/i9mqvfs7UFjjMS2/mCb\n4etNEmcZX2xhHV3Ic9zHck+OXXSqyJZy1Xw8qH0LMliZKkPiQZKNYbrM9urq\nX0EbAgRKLVRhBrhUjHsKxUMf80gNebG4Ptw2zDKM48W3keWruX6pL196lEMC\nf2L5Gay60/IXFS+s2/RRvDaDzAvwZFP6yL9ZSjhC7kFsLBf8hjb/zRXynj7X\n3NBZQy2Bj6P4RM3nqByfr3qb4tD3drnibyAZJ8tGbdN9Xsfj25/E15MH6uqM\nNCtM7VcQayueJ76BedzulSrY5ukqzxYShOnvazUXOKMdLjudQTu3BDpU1GjB\nGESWU2USUo6aSR44TpGUyQ707xtcQ1Gahp7h1V9Tb09ghD9DYjTgazRHdKW0\nIOx34jKDRVU5FfsiU+m5BUpdQ0Li9bdm7CetIxoRvFzSTtre0odi0ZN9XWT+\nOxREu2oBXtAiWv2tnwd1dUdMTVUoP8Yvw/Q6Szt84IWiNjA5rESudmKp61sI\npvLBMeykWI24mCfjJbfNgegqUDd/IQ9KQOFvlmaJaP+JHOMUEBt94LB08zsc\nHVjHP1gXumHbcTBa4TYBxLIOiIqJS4agoJyMuTe1+sdsppIJNM9FtyJM5bbU\nUKEtRCf7QSmowZezkncijd31ueGWEY//DzVGBFkH9nDfOhDk8CnFUxBwuRYJ\ntVVo\r\n=0/Hn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "2a77b80d478a211c4784cc5f4f1aa3df0bbfd6d1", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.8_1533129315805_0.4452870544739356", "host": "s3://npm-registry-packages"}}, "2.0.9": {"name": "nwsapi", "version": "2.0.9", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.0.9", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "77ac0cdfdcad52b6a1151a84e73254edc33ed016", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.0.9.tgz", "fileCount": 8, "integrity": "sha512-nlWFSCTYQcHk/6A9FFnfhKc14c3aFhfdNBXgo8Qgi9QTBu/qg3Ww+Uiz9wMzXd1T8GFxPc2QIHB6Qtf2XFryFQ==", "signatures": [{"sig": "MEUCIQDpFwKmjFPmSuc6Mn6nfvL50dJry9AOSFnRWwSBC6JZCwIgaICo5jA2uY6rj7F68aBaD85sl4r0xqmFzA5+8vXn0xU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbi8lvCRA9TVsSAnZWagAAzfQP/jvWhXIsivtzR87l66cr\nppvfh1PQ0y+YK5M/TsfogMswPcXf5IEm1ZCY24KJhx8hEx2pG+dqVlwkdM8g\n4P/1+on9xNeKVPZU/3f3GV8cKNMpvB7BTUVQDp9/TM9xZRY5/NTnVtV+d6K9\nLkNtjFxqSBhVs9DuqjdcyhvIiyuM50BVtPB3QQclFRfcfEFiYd6vWrRDjemS\nSce/bjWdpBAyZS4WdQF8N5XCOXjvGFEM0mbztFIlhcSMrlA09zOtf3dFpC/G\n4sghkAVpcNVRu4/TOXNdlfmYdachRjHmU++As00F1yxy1yGh61lkwtxzQvFr\nPlVhg7/SkQUdEXY/1DlHMCck9xZ0dROErA5HEK5F6yxLG0+sv7YLnWZxWf+8\nAT/mPeIxglmZ5MS9xGLlCuWZUzgGldMUboDjwyZLf4OcwdQOx/LVj0bpXgww\nRSjSAo/wY3ysueCcNZhtJNVzzlXhn9x/i+0YF8hC121elPAPPv3Eomb5HvoX\nxb2exfHRj32LfvfR4hDgHNueyrFFH5zKya3Z4pICAVzByNBfsaMxUFQsNH6t\niJzzmevQ6iDnoMenzp4lNYq/BlZjvqysSO3fbbAN2zSIphYYIDgy/EAqM/up\nnyFE7WSNPjKVQbiQAeg15K6R6cv9e1qkCU0XTRZNgogKO/qkHTSunaEt+sAB\najCc\r\n=kcCt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "c43244c222912b8ad928929aa87312aef0037a58", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.0.9_1535887726669_0.24363355298768075", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "nwsapi", "version": "2.1.0", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.1.0", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "781065940aed90d9bb01ca5d0ce0fcf81c32712f", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-ZG3bLAvdHmhIjaQ/Db1qvBxsGvFMLIRpQszyqbg31VJ53UP++uZX1/gf3Ut96pdwN9AuDwlMqIYLm0UPCdUeHg==", "signatures": [{"sig": "MEUCIQDS4HvGxzI6HqUFgy2gNzjze3XGmREXkE1QeuEWvuJ8tgIgIzfQCwStQY9QgF3zBBbztG4qYFB6JpVK3xvqcLWv2jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcV8K9CRA9TVsSAnZWagAA0z0P/jVLpNnXrPdZvmWEm8xQ\nxb4H0CBOnQYurdWphK+wVu/mxGfMOOx2A0pPoE8E117jIyBfXmZc1PR2Jxz+\nk5tOh3a+g700qOyxlHLq9o5WSb/MpbkRReULsfVaJwkqGm9u7YBzeBIwEIIX\nDGCixyl/PPv6IW8ON6QyiZcsQ6FUaZWaFrM6KprFEreUzgaC4IgILryN/fMz\nvqbqOqRTbH5SK2WzjBtl9xKC5bcbv37o8U9/jYx6a8svM4EtUWPtG8u9sou5\nMdjjh+T2QH5pCQcISQhDaWq/2UDwuURmH6WCopHwZw4YP5lZIPYK0MbdIqLi\nPIPNW6tRS+awet+6uKRE2bstRoJleV10HniW9Cf5kueixlOVXDYAG3mNkrUd\nbc1yly7D6O3falz7ltEq7DDvHa2RZzQtZtYQNczD5BuHkSQh8ndPkbDzW5ZK\n9W/sopgOWaV40VrIcSJJvxQlb4ihHMebRu4YsCA6BHgQ8f507p+ASKvQdTQg\nDGyRojHeZ3F2HDtGeZKdEzo4CSyOQXrodK3Ykf8+cIYMUWXh0MLwGKShetUR\nl27HVoFoTryrsoO0Y6BvUqPeJcOVGSqYP92ooEMeeaPqTTn3az9ZE48BnCx+\n6rBqzi3zTHWY/v8eKYYsAwQmlnKmWyDxOi+sqItz1kFr010FCzX6ohizmR5W\nBye3\r\n=+Cjv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "98ef4693a1b4047f1e3c00cda383cade255b2d27", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.1.0_1549255356813_0.32693772857624004", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "nwsapi", "version": "2.1.1", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.1.1", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "08d6d75e69fd791bdea31507ffafe8c843b67e9c", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-T5GaA1J/d34AC8mkrFD2O0DR17kwJ702ZOtJOsS8RpbsQZVOC2/xYFb1i/cw+xdM54JIlMuojjDOYct8GIWtwg==", "signatures": [{"sig": "MEUCIFp+k1reIv/D7zaCZi2s98MN06XJvsDKyizsR4/N+PMJAiEAkGZCg/Q3SS3OZLxs1h43zZZu2if0NnBl6pLRHWaSLKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccwLQCRA9TVsSAnZWagAAN/cQAIA4jFTMWmZ3jLolyTEH\nGZoDg0kpX/Jq2BI/hP5Vlkotk43LDoHkbAm/QbZDhxGWWe8vuHgklYFjb45k\ne0CnAN9NLQTPXhPoNxdJVRwLKaIFA+/u6CpVUeTNHVWqxxlpvBUXagW5Msc2\nCAVt4lBVZHatQvyxZF0vC2IxQFeVCWwK4zg1JL6XTPqbS/TvlHxrjhM8yMd9\nf1p03MjoZrnPq83aw2uADEfVpI26SkRJc7x/LsSBr1gi/r7SL93FB5noCo/7\nSY3xRyq8dJ0S8btSOlNbsJtE8bP6GQNnQ9vbbWP9Pj9I7UKiV6mdoiES/rfj\nP8gXI4krmm3e1pItZd0oFbRiasfnuc4TLXdihevMadMG/OxeV6Mrw/2hdN7Y\nq1mjvaJatWqRYoxP3+6un6v6Zt+SaUE/fAkj7AAoXD/9epTRbzSoeHRHcNpo\nX4vl9KorDCLZSd8iDGwEk2KTgSJDOjxz3/GGvB7naN+hGZeyF8SKL715zWXl\n9YFcFBoseILjTFewuZ/fYPHxzVW3Ymx/Anzm/6nZYrK/wBP2OJerGNqJH3Sl\nj0h6dCcwep4cgALGm4fsUd+NJTVsE3BPDkSKFhEZG1wvDpAupXtMgW+T7X1c\nnciLST+EmNdOg8//dN1XuTf1HL1zqGT1F8Zju3TSyrXM+ZlIBC0nB+CK2jzF\nwvxM\r\n=Cwnz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "8eba2f82ea1873a753108dc0d6fad2651bd6d652", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.1.1_1551041232215_0.48948902732167343", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "nwsapi", "version": "2.1.2", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.1.2", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "c360ee8745aa318d8a7c8c8095decc4e373338aa", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-TQOQNxqEdxVjwgwNZyvKDF0vALmzQKZJEZwE3fZWDb7Ns5Hw6l9PxJTGKOHZGsmf7R6grsOe8lWxI43Clz79zg==", "signatures": [{"sig": "MEUCIQDMLiP0xjkhRi87gJrs9Sq3AZvPuvkTPMh43+e+N+bXZwIgALLjOt2C9RuBPGwrO4FaEV1loce+ADck5x/ao3lWzlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnUFjCRA9TVsSAnZWagAALgMP/3eFrhKHFPE2xe8vY8aa\nYxfG+49pHrdU55X3bzZ8dA1vJTuy+bCnR0tGo0PmBru+Ddkc79w1Hp260fVO\ne3ZVoDIydqUwj7Umt/WnGrHIgsTS3H00cHyxSE1NAYhPcZeWz4wWAZvL7R8l\nWef9r8Q2jnGNiI8ddNg1plpxBLiYRQuTXH5aeDlYe9NwBluD07yXWVvXK92W\n62Je81SfE09gioEqXQhYMb5jBULmESndsyhprTvT9JjusqgCzVb8cX5p7dhT\nX4k0oZp1sPQPuyDFdy27l6kPU5eWd60mqNXoSeeN+KZk6lJ8mtimRFD1lWqf\nCsFmsuS2ST7E3Zjjs5aK5dphH1ssBrHq6qJhsxhlgemop+R7kUdiPnl1P8yJ\nhbDPNoRW0iRVaA7vsex+l3ZHcgfZrCKzhJpC9kdCiglTlyTLgy+1FYGqDmGs\nLpKmZg1wCe648RexNL5Gq+mrrwkSqogOpCD+4eOE1xjoq1r/54tIT702ghYg\nGX/O9XQkIGFMF17MhLeun4QlvuuznzIvmGViXx0+r/DvDJPoC+KgmSfTPQT/\nuXmgPDGHqQZk8am6ChaCzd4t0P8Eb0gNA6kSQOyaXBk/HodIQyrkaov5nlwF\nAMq8zf71obK5egCLH8pf30AOntPEKSW5O5/p/sRACXslOexNklR6jXErBBlC\n+S5r\r\n=pIlH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "94e3b799ddb8c032fcc13fda9b58658a2230dcf2", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jsdom": "^14.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.1.2_1553809763084_0.3905821393987883", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "nwsapi", "version": "2.1.3", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.1.3", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "25f3a5cec26c654f7376df6659cdf84b99df9558", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.1.3.tgz", "fileCount": 7, "integrity": "sha512-RowAaJGEgYXEZfQ7tvvdtAQUKPyTR6T6wNu0fwlNsGQYr/h3yQc6oI8WnVZh3Y/Sylwc+dtAlvPqfFZjhTyk3A==", "signatures": [{"sig": "MEQCIGD5p6YbZ+jkvXubtAddg4I38wUq3LY/JuGRr/Mq4SLaAiA4KXCYc+oIf+50KL6w4mrbRoBbk8pWIw5VNZ3SHQroLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcn6ZDCRA9TVsSAnZWagAAZPsQAJdu9RgKKqaxWlk3n+Wm\ncCS/RAI7EwepnPTS+w8sWh0cOfyohzSHKz1SnAD4nOnElQrSBj/EE1jWPGGM\nfaTaAXmnZVTqVpCRs5UNBy4VO9x+YCVug4xZ3UsS16Xx2g4iyWeDJMHKSUcp\n//vMX9H1o6sYOxqEGgKhWvsyyPXW6E+pESEWp2mFObb/BCC6tH1sZqrf+nX0\niPhnbM3tr62mkb1Ag5YRoPixhOGQnyQPkttotYIjEBkF0wAEpYOhgPxhlC7I\nz4Bt2daUH0dQ4MTq4T0T6dIzxzpnCJgTTmMqLRL8jBeWxwASCAVuSE8moaNG\n8ZtPAj2OLcUQuXyObaerGxqVHB9VqYVE3zmj3USS537vku617hf5TV1kUZLq\nKZBpiu8VB7sZeCiEZO2Wt5tXdZg5Pgxller6r57tjjJPDmbjWoUoF25YOiBa\n0UbGdpFKsr36kcSEA6I1j0dDiTw4EMMOH7fNlew906egonOcN45GQCRlOzab\nIvyQdHN0OzvWRKaey2a/d5LSvumux9gZSH/6yiZIvYKwlk+3V2aijJeeESwW\n3mushDg/BOiIo5GKp3ogilJVr1bND3dwCIbrJCZITwUoa/senplp2MhLdcqk\nQB1S8H2obmrv7Pr/wYCwH7sqTQeEvjBxnvNGb0z8QwJOlGrcLg+CCuFDQVVT\nL/WE\r\n=rIFc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "36b2d6437d723bbd1bd6e14c216ecc66f3cd7840", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "11.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.1.3_1553966658631_0.8062257611067447", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "nwsapi", "version": "2.1.4", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.1.4", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "e006a878db23636f8e8a67d33ca0e4edf61a842f", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.1.4.tgz", "fileCount": 7, "integrity": "sha512-iGfd9Y6SFdTNldEy2L0GUhcarIutFmk+MPWIn9dmj8NMIup03G08uUF2KGbbmv/Ux4RT0VZJoP/sVbWA6d/VIw==", "signatures": [{"sig": "MEYCIQCToHOEfR2Hm1pzvAzf7Z/hQ5MsY4PM+hhKjzDflhtMsAIhAP271PZo5G0oe2BUnhNMkaf8zanRvA0fpX+BwTC/XROP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcx2HCCRA9TVsSAnZWagAAdC8P/i6uK6lXi8+3xyiYEf4u\njksSnz4cLChiY2ZLgbDmgal81GRNS8z1LpjIS3FJWKLfA3TaTx5m4G03Ov+b\nEfQurge108kGWnc+ZgKj/pmvxrviPmdOe3gR0vfHFHw3dbtuh9zdWxNWEvOQ\nDgXqTC/M9odXPUJRA1wGyfZafFv1382iXyugeNMAE4XwmJuXsCsWlD2rvGz4\neFq8RRwUxn5tHS7x0nvK4s8C/CEq6ndgInr57IS5refBWC0DKGflWy1j3L4a\nQP6ltM58WAVUDBcZ4HelD4TtGNrXked6cANCf+aTYdIFOGkoC7md6zGeqgzv\nAmZLVNUWYjtBd1EpnUS3oNY/QqepNWF1SE2G0GVRBB3s3eiu6t8put8tuyNs\nVYh2TPzv8JWB8oJyzApmt0KdyDsPBG7oyeFfHyZn2Q3A7KonmNz5mBZb7eXy\nh9R7fHJd7JpbD7Fxl+xYOF7XpeUR/SYSehgUz+mmYa9l0+P6Og/e8waI6TlA\nnSSz0QKjC8FqHTy8vg9xAXGmBfRR1i2JiwKlKDP1rhsde4hUqooutfqvt2kC\nJiHChpvWBMgvTkyYwGnq2nr99JvTJsS6wft+OgaTWq4a2kwho7QE37RsWM/V\nVxId2xRuvVrtrlYfikQYdwtaZEImSHtwakEM7zkzSpMX6cJs/4KP7rdDJaIy\n0WPF\r\n=mTKW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "4077276bc5ed1e2b56f12bf409b328e918d67784", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "11.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.1.4_1556570561372_0.919770704504693", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "nwsapi", "version": "2.2.0", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.0", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "204879a9e3d068ff2a55139c2c772780681a38b7", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz", "fileCount": 7, "integrity": "sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ==", "signatures": [{"sig": "MEQCIF4J3TqKaCaTub/rSp/lb1DdZc1B4wiBpdlJC2ixl6nyAiACFo/gNvUgq1CXg4AFtJVgoa4sk9S81SnAXUTWhBrXEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdv2RLCRA9TVsSAnZWagAAFugP/RvxgAQ94xUE7hhaqGZI\ntFYj0dRqn+RGQwE578X03DY0bPODdcHvBxf8v62h+vWvwiCTMEbVAcPeKONh\nZCMljdMm6L2ecqnGL1NT7ggnnbJtG2jHOqeFCicyjHxMAq6V7/M8V1ePfZGQ\nPCi4AQK6tp0oOZ9UdJZl0I4S59CpITvuCKO7nFkJxIyGn7RglTqj6AVBpDch\ny/KokYahKX8wX50QrXrpcvb5hrEpWn7n1MVp1YTuUGN2OdFftQWxR+jQMm54\nmo0Q7Ap6Xb71+ARpy3fy2YOo/ysn3atKvSCGEKP56la6qvMcSclnv7I6kWEO\nug+T+Vc8aDi6hVgB5gnZP0BhCQzpvpLnf7fUotKMWWthbUHX4PlcmzHMbx9u\nTdZb8ikVm51vT4eR021OwPHhSBuofB34vdpM8QOdCznzWsDAmH41sAavA2O8\nQuNGXbPw/1NshE0w7w09eBI4aEZR2xhsmbE+MzdQ+29PlPapEnotUrdQW1bh\nqTOpAJnMo8NZobKZbiPCKeAzcX35dDmIo798KxKkInygNI+cdls9YqTELPC4\nZa8eFMjMEYDQfToFRkDJD9pSUVU0mlUnz8KA0E9E1fANRrcSc31hVwpVtmKS\nLcAycH/prx89093geUUxztP5JLws8RPn31Dcwj66ETBxqBAZbSFBXLkzltzg\nSMnW\r\n=+LAg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "3e2d52b9bf98e33aaa099d369523b8487f8fb07d", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Fast CSS Selectors API Engine", "directories": {"test": "test"}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.0_1572824139478_0.637305875073402", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "nwsapi", "version": "2.2.1", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.1", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "10a9f268fbf4c461249ebcfe38e359aa36e2577c", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.1.tgz", "fileCount": 7, "integrity": "sha512-JYOWTeFoS0Z93587vRJgASD5Ut11fYl5NyihP3KrYBvMe1FRRs6RN7m20SA/16GM4P6hTnZjT+UmDOt38UeXNg==", "signatures": [{"sig": "MEUCIHiHzjyQlRJb8/tu8Oq07rdK6fXsHongU8W6274VP6yfAiEA8EFSv3V+NUYjetv1kZXprVr6kE9pvxrbyXH3QQcMUI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitub2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwsQ//W2gZyC08rmSeTcK0ptGDm3cmAkRfgWvo2/jkKYbUig8vgkNW\r\nMVWwzoDVRNndRRSTNE0dNOXwaZpR0Vab/x7z2kouil79zuyfspx+P7AvBk7m\r\nReICcNN+9lBBKDXAYnILeJfp/eIHNJgWD7+wJuQ9aYYr8NPs1u1cybPIf3cQ\r\nUyxXJlP8FC0nnFelG96ClflqAt7hbmnv4cATmr8W7owSizKtY3s64W2fpjNA\r\n9r4xZFsrNa8IWB0XJbE1ctpIC0sjiCBEQ0SWH892Mh8qQr2KLPwfN5CeMYGT\r\nW2btHRGfnSE8zS8FrQBtmaHBf45Khknd3wQ++vl1tx1kEPWVprvJ2qsfW/9q\r\nUnshWoWCynD3wXEuNTDSVNin+HqbvR2bZxnfUu56RPQDc7M26aTSFk1syqwt\r\nF8oE8Uv7PZ0kQTXWd0ZFyVANTRVMsXMXwfx37uXmIgvCoHEfCFROjOZRp3yG\r\nndM6f0HJCoSkPfY19FG9lNKPgvjK8SKfTdRObUwrxI1xuifgh2W4UtxEVzrz\r\n2p7+0OQ1e+1KiwLFEfUf900FONTUpyZIAaIkjmi1ZCoa++lLF//st4KTu3l6\r\ntWD0vmzQMpMZwl4DSsuUHOeDTB/aXkgcYLyrL2BKKlOTE/vEWxthnYdHf6n9\r\njUgWeEKdVwEEOypfLNGOJYLGLvPyICqC2NE=\r\n=hDuu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "357be52f7830462c1d6d804b0c6076cbe6b573b2", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.1_1656153846645_0.7682283631454252", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "nwsapi", "version": "2.2.2", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.2", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "e5418863e7905df67d51ec95938d67bf801f0bb0", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.2.tgz", "fileCount": 7, "integrity": "sha512-90yv+6538zuvUMnN+zCr8LuV6bPFdq50304114vJYJ8RDyK8D5O9Phpbd6SZWgI7PwzmmfN1upeOJlvybDSgCw==", "signatures": [{"sig": "MEUCIBpKvvhcQ8W/94Ozb6S681q30w8j48VAIuo3FCK1M/YBAiEAwq/elTKEb6t+oD3D8FlbR1uhNSBnlmobulWjt5FOUBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEnU+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpiyg//cSsasBfV03RLeWLGEmmmU8HEnjzMnmxIlXrBAUUgcv9sREt2\r\nfgOFLTbHcJ6oFNzfsz0ycu33SYS0SO6OmUKnpRE4RlcNjRquRl2exLDmhuGe\r\n9/DOUgPzG2F43g8f4kMhWUrOS/DgJzF+8ohOGfG5Gx9rSxL5PgQ4EVglzn0F\r\nSTA4553MrScddfHJ7iJAAIr6+RYak1zlrDS1RL3f9DuOx6Jdo8SCXrNtW6ea\r\nERlEDoBjIIWqhb65yp9INEGfTmzIP0P1egU5n1Hrws2ZhLdjI20Ggd6Jjxpz\r\njs0n5Kmm/tDaHsKzlSTbeQx+30u+QYUdwjcE8eL3C/J+POySYe2uomc7xk9v\r\njOCsMjCCrPC3tX7KLMmHIRFK5Dkt6KYLe7jP0QeVGJ27QZEPyWOxh9udKv+0\r\nFIZP3HO3jWN3GVBwv9No9WNRdil717v83UdJF3OHBT503UgAsGS+AO+5ogSY\r\nYo18YcXg1ZMFUIHcx4Tw/rCY3FMhf7furMLN6cggVJjmjRy1FxwPgh1Cdbt2\r\nEp/A3oyYOSkc9sob5DqF1hwQdUghQnufoyoQXlQutdpdw4hEqhWmResv4ke4\r\nfgUjd/8KIdKY7hl3zlCBn0WbxppXtI8G77WESEtoZdFWHb9xnDbhNOLl1XAu\r\nbcySysNVp0gX7zaiGZsAI8MiNq/AHlQEmFQ=\r\n=yra/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "410638fc214f9d8cd8a403af67f0d0ea852f13c0", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "8.14.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.2_1662154046745_0.1779625328519443", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "nwsapi", "version": "2.2.3", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.3", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "00e04dfd5a4a751e5ec2fecdc75dfd2f0db820fa", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.3.tgz", "fileCount": 7, "integrity": "sha512-jscxIO4/VKScHlbmFBdV1Z6LXnLO+ZR4VMtypudUdfwtKxUN3TQcNFIHLwKtrUbDyHN4/GycY9+oRGZ2XMXYPw==", "signatures": [{"sig": "MEYCIQDZVKzvk2Xt10r7XRadCg8vvVoyTzSJXA1W6pCw7Z3A7AIhAJd7yz67RnsXFr/JZdin2Rp2D7wfnklZUUxwq0odQj8H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkME4VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRrg//USTI9PbeB2LCJRubbs9XL87uIGH3XP20WD5HuxPipZqMMZ8R\r\nxC5XijoPM3/WwkaGqT8SeZWXH6RJBXFY47pI0OtBF7hwwoD5g/ny3qNNf1M2\r\n15bgAW6Vilc+dCpjxt8GrUCWNnSg7DHpA3m+/K60g1+j99SsPtWgpUE7jAho\r\nDrEniVOtpMEeWuANe/Z4fJKR4ZIpQ8Wo+RbHU9pII5+Ue/4kgIMx8HhdlyUs\r\n+fX+I33ET+r2ZdpiIiyRygA3MBazBvxFqYBxIG6F7VI+1XSfbK5X0BI7wFGg\r\nmB1bd67l1PRuTyTxPJRl+dkxinZnrf9+RO11rLCnhXmIXyKOf/+0jAQ4Dlkp\r\nIerAf/1ng1eVrpWulTQ8wI41xBS23QG3J0HGffw/v7fNDoj79o+Y1EYFOp4N\r\nTZDEI4moIObRiLueP4bm+iELFNbK8s5hf04vHvVsVB+S1yvLFGDK6CRglWsJ\r\nZRd26EHlCMdseJuVIjyCfF1/FowPFfiqz0/Z0tGymmqvGwEObBUdr/7B7gpE\r\nvkO6mqyWGt2gxm6QXGoKvUYhElCJcablHObtIHsX8TXe8TZNhf+xLHf+Ln1Y\r\n9gZv0NR4g0M2YKJMGkp63pYrAWR7x1A/hOfiDvxZ2tthpN6snYxjPlpoXi+x\r\nac3VYgzrhKk24rkAci0FdHGotVQoJZ2w8vA=\r\n=p3pL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "c16b71d92a692b2fcd30149f4a5e54a8a841cbc2", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "9.6.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.3_1680887317329_0.32307731757318914", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "nwsapi", "version": "2.2.4", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.4", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "fd59d5e904e8e1f03c25a7d5a15cfa16c714a1e5", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.4.tgz", "fileCount": 7, "integrity": "sha512-NHj4rzRo0tQdijE9ZqAx6kYDcoRwYwSYzCA8MY3JzfxlrvEU0jhnhJT9BhqhJs7I/dKcrDm6TyulaRqZPIhN5g==", "signatures": [{"sig": "MEUCIGWyWruxWEHnF/wLPpWmpqMT/T1ebJMo9BgCiw/NI8pNAiEAqdVhhqZZmQGMrP9Xpm+1JjLhhVD1r3D9qJb48XBXvDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNxaSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo50RAAm6rts2rQqiG1mZx/1gZnHmLcs/jHbhDxlYTbQBRXpVItg/IB\r\nS7AGkN4CSLt6lPxEYcNQoLaZfdbMZV55+mMLxGST1r0b8co1IonraIg4k/Pr\r\nheFdzQdkFieGRLIAXLKjySrx6MM487z9XiJUUyvHVSKjVSILGu3LOTsTCto2\r\ndgTY99gHS3PCaeA7moGPTtmFJ0S1dKbV/UZup7qsXVayfZ9v2xBNwntnLFmr\r\nwEKlJ0IwB5wItx9sxaTlZwUa2O0W/h7Nk9QS7F5mi9xDsvhV9D2wQfIgWh2i\r\nip1YfOel6iXw3ZJn7vMsjCIT2y4JipQOGqxkHts2sRkHMAgdzuVbzXtOsKt8\r\nby6vm4QvO6dEWxYL60UoRYNyM1zmi+C7GOI/+BsISQDaONScrlXIsBEmcqV8\r\nhTsVTq8/0L7s2MQc6LoAaoDa4GDc00pPrn4xGeLOuLw9rkXtRYKTXPWA/iqP\r\n3y6NaTSNjd+fwT3N2bKRpnfx2FDdnBsErnwElhhZdJ6nXYUWTm6cE0Qks6C/\r\ndTKJKuQksxvLXr0vGDc58EiHRN06m5O3hsQimfblXrXJn+qPSfQLDXEEMafF\r\naytfA/8yyddV9cOzJXJcp7OYc5tUZCjAC7z3HnR/l5mW2KyQ3hiHtkV/9TWY\r\nrQyPKz4l6Qm3DUY2/yw65nQuvjdRcSo+lM4=\r\n=GOr8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/nwsapi", "gitHead": "8d71cdfcdeb85800d7c0578476e8bca31d36285b", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "9.6.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.4_1681331857896_0.22476090563430895", "host": "s3://npm-registry-packages"}}, "2.2.5": {"name": "nwsapi", "version": "2.2.5", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.5", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "a52744c61b3889dd44b0a158687add39b8d935e2", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.5.tgz", "fileCount": 7, "integrity": "sha512-6xpotnECFy/og7tKSBVmUNft7J3jyXAka4XvG6AUhFWRz+Q/Ljus7znJAA3bxColfQLdS+XsjoodtJfCgeTEFQ==", "signatures": [{"sig": "MEUCIEA55qZUZNeN19/9vJlbr0/ehLLD/8jeLQNShb1RYp5aAiEAuPBmcEU2xbaNIhPj5JZzA1GaKcqsx58tL2lg3aQW/D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79986}, "main": "./src/nwsapi", "gitHead": "a38a48344a9edd29d3df0bc6ad8d654b46c231db", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.5_1685281689787_0.797002462849812", "host": "s3://npm-registry-packages"}}, "2.2.6": {"name": "nwsapi", "version": "2.2.6", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.6", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "f876bd7ae9509cac72c640826355abf63d3c326a", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.6.tgz", "fileCount": 9, "integrity": "sha512-vSZ4miHQ4FojLjmz2+ux4B0/XA16jfwt/LBzIUftDpRd8tujHFkXjMyLwjS08fIZCzesj2z7gJukOKJwqebJAQ==", "signatures": [{"sig": "MEYCIQDMaQiYnoNoAQWDBz8K4UqW2zTr/bRZt9i9GQkeAXQbCQIhANa6WclBTmm2EiHNc/fUe39qAA7kVFuCOIOvnCcvTMfq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144699}, "main": "./src/nwsapi", "gitHead": "32da18d5ca9c39057ff8083c87ae0ebf58dbe977", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.6_1688402547481_0.8105283030009809", "host": "s3://npm-registry-packages"}}, "2.2.7": {"name": "nwsapi", "version": "2.2.7", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.7", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "738e0707d3128cb750dddcfe90e4610482df0f30", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.7.tgz", "fileCount": 10, "integrity": "sha512-ub5E4+FBPKwAZx0UwIQOjYWGHTEq5sPqHQNRN8Z9e4A7u3Tj1weLJsL59yH9vmvqEtBHaOmT6cYQKIZOxp35FQ==", "signatures": [{"sig": "MEYCIQDU77CkLb2dx7nNxEMS1C7BTaI5LryZTANmEm90zBO3QQIhAI32vbvhZvCYoOXLZ8yFulZNTQ38GbXqT3QD07gpai3A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209141}, "main": "./src/nwsapi", "gitHead": "26f8574d7f6cbc3c2947e034928a921511e58853", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.7_1688634672741_0.9001296013982445", "host": "s3://npm-registry-packages"}}, "2.2.8": {"name": "nwsapi", "version": "2.2.8", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.8", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "a3552e65b74bf8cc89d0480c4132b61dbe54eccf", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.8.tgz", "fileCount": 7, "integrity": "sha512-GU/I3lTEFQ9mkEm07Q7HvdRajss8E1wVMGOk3/lHl60QPseG+B3BIQY+JUjYWw7gF8cCeoQCXd4N7DB7avw0Rg==", "signatures": [{"sig": "MEUCIEREzgVvLKz35/ppHKYqxU07RMLzocXMK1/urlqwleZlAiEAlgl9+A+9j3+55nDIEtcpMULjcmCmekUVUalx209pQKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79743}, "main": "./src/nwsapi", "gitHead": "17abd7e36d4c099f7e51a14b372c5e5b42a2b914", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.8_1713623895750_0.6108846369338663", "host": "s3://npm-registry-packages"}}, "2.2.9": {"name": "nwsapi", "version": "2.2.9", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.9", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "7f3303218372db2e9f27c27766bcfc59ae7e61c6", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.9.tgz", "fileCount": 7, "integrity": "sha512-2f3F0SEEer8bBu0dsNCFF50N0cTThV1nWFYcEYFZttdW0lDAoybv9cQoK7X7/68Z89S7FoRrVjP1LPX4XRf9vg==", "signatures": [{"sig": "MEYCIQCt1OFpX84uSNcbADKO2vZuAoiikkpW5PVjuaqWlZ/tJwIhAMQtlqHD4ju0XpFkbypXJqXriQkVYAeqVYgw4a8tWEPf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79743}, "main": "./src/nwsapi", "gitHead": "7e76a2ae563bfe27acbae3c1162edd6a128ee405", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.9_1713730135073_0.7575888685982115", "host": "s3://npm-registry-packages"}}, "2.2.10": {"name": "nwsapi", "version": "2.2.10", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.10", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "0b77a68e21a0b483db70b11fad055906e867cda8", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.10.tgz", "fileCount": 7, "integrity": "sha512-QK0sRs7MKv0tKe1+5uZIQk/C8XGza4DAnztJG8iD+TpJIORARrCxczA738awHrZoHeTjSSoHqao2teO0dC/gFQ==", "signatures": [{"sig": "MEQCICu520dAe3t0p4ARmpToRfzeeSpwV8Ck7VoJ+pF2zXC/AiATJBSzUm3xoLMk+bhaC3VQ01h5dfWYir77QPZWHj5Cwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80696}, "main": "./src/nwsapi", "gitHead": "c6060f777fe1a2c110bd6c32c5216136b9f76aeb", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.10_1715578844066_0.5680261325067708", "host": "s3://npm-registry-packages"}}, "2.2.12": {"name": "nwsapi", "version": "2.2.12", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.12", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "fb6af5c0ec35b27b4581eb3bbad34ec9e5c696f8", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.12.tgz", "fileCount": 7, "integrity": "sha512-qXDmcVlZV4XRtKFzddidpfVP4oMSGhga+xdMc25mv8kaLUHtgzCDhUxkrN8exkGdTlLNaXj7CV3GtON7zuGZ+w==", "signatures": [{"sig": "MEYCIQDfS1vBZ1BDu/VAhTcS5n0r7spfF2yl8wQM/FKwEQu7ZAIhAOYu4MQD/kVwC8O0phSqXYf+yMnZl3xdG8+XiEz+Q79x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80639}, "main": "./src/nwsapi", "gitHead": "088475fbbdefc9d10fbdd7f914d92bce96aa9d0f", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.12_1720631831243_0.10524760775196174", "host": "s3://npm-registry-packages"}}, "2.2.13": {"name": "nwsapi", "version": "2.2.13", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.13", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "e56b4e98960e7a040e5474536587e599c4ff4655", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.13.tgz", "fileCount": 7, "integrity": "sha512-cTGB9ptp9dY9A5VbMSe7fQBcl/tt22Vcqdq8+eN93rblOuE0aCFu4aZ2vMwct/2t+lFnosm8RkQW1I0Omb1UtQ==", "signatures": [{"sig": "MEQCIA/N/kvPeNeFIA1phyEa07N8ORtO2yHB3sG87Pan8xIKAiAfA9ilgfMDdg6D+2d5EqUzOldpgFxaTzd/hnWOj5YQYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81630}, "main": "./src/nwsapi", "gitHead": "ee69b43bfe979552161c4e4b7e73c7c70d2efc2b", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.13_1727473046460_0.7688415481880635", "host": "s3://npm-registry-packages"}}, "2.2.14": {"name": "nwsapi", "version": "2.2.14", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.14", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "c7101dbd9d05a88e2eb59d66d3e0a744448802da", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.14.tgz", "fileCount": 7, "integrity": "sha512-5XcFrl8snuCQTJC2SYIW9yhhMnILdMw9dlGT+At11P7jqDuTafp6/uc3lAXsMOmftER3Ntb+T3cHiupOtj7Lgw==", "signatures": [{"sig": "MEUCIAdDJ5EbVH1j9VTrWgyx7N80fIGTUROhrWRiCGDKv/PwAiEAjoQl9/xzAvffFkkxdBc7BDssjg7rKCcx1L0GTHtwPFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85805}, "main": "./src/nwsapi", "gitHead": "3eedbf2dc3d0cd63a31962f18c76edebdb551c09", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.14_1732870733468_0.7878460215398131", "host": "s3://npm-registry-packages"}}, "2.2.15": {"name": "nwsapi", "version": "2.2.15", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.15", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "f32d5b3f7d3ecb0c6cbfc1c71ba0160988e00d8a", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.15.tgz", "fileCount": 7, "integrity": "sha512-NWK6i6f70uRJgsqyNTNMQ/JbCzd8zQoFnOKdZpXqfyq0YFBQgCTFjDIXfAmkEJyV+/GzSrCCrz2iVJmJK9OX8w==", "signatures": [{"sig": "MEUCICy9xeSkyis98KciSRG6qPCl5XTQULNWl88S84516zPVAiEA58F6ABEIqIc7S3qyG7RavcBumWJjMk/PmCc4LiFdd3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85806}, "main": "./src/nwsapi", "gitHead": "ce1089c21c6c206b4951626ea5599f5ccece20a8", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.15_1732886481470_0.7980616814897588", "host": "s3://npm-registry-packages"}}, "2.2.16": {"name": "nwsapi", "version": "2.2.16", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.16", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "177760bba02c351df1d2644e220c31dfec8cdb43", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.16.tgz", "fileCount": 7, "integrity": "sha512-F1I/bimDpj3ncaNDhfyMWuFqmQDBwDB0Fogc2qpL3BWvkQteFD/8BzWuIRl83rq0DXfm8SGt/HFhLXZyljTXcQ==", "signatures": [{"sig": "MEYCIQCBmnonFdBbS2bd77ydzMGIAesdgkMz+zK7+2i61g62yAIhAIAHHVoToUUe7ITZCsyoHkGfHxZ2pjwvR/k5jT6kfns0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85848}, "main": "./src/nwsapi", "gitHead": "37ad99b62941cc8421e682786a6118d774c2bfec", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.16_1732897111425_0.4821946945855624", "host": "s3://npm-registry-packages"}}, "2.2.18": {"name": "nwsapi", "version": "2.2.18", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.18", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "3c4d7927e1ef4d042d319438ecfda6cd81b7ee41", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.18.tgz", "fileCount": 7, "integrity": "sha512-p1TRH/edngVEHVbwqWnxUViEmq5znDvyB+Sik5cmuLpGOIfDf/39zLiq3swPF8Vakqn+gvNiOQAZu8djYlQILA==", "signatures": [{"sig": "MEYCIQDH9n1p27I19esoc4iDQZcdw36RNCPcJVXaVYCxfvbaUQIhALrGnzaUhAfuFyarSUgNeQ5wX2RYq0qUsSx4maG/153E", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86515}, "main": "./src/nwsapi", "gitHead": "f6f79df62eccece06c2ad0ac29d486e65c7e3bcc", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.18_1741002164144_0.8423559042150981", "host": "s3://npm-registry-packages-npm-production"}}, "2.2.19": {"name": "nwsapi", "version": "2.2.19", "keywords": ["css", "css3", "css4", "matcher", "selector"], "author": {"url": "http://www.iport.it/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nwsapi@2.2.19", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "homepage": "http://javascript.nwbox.com/nwsapi/", "bugs": {"url": "http://github.com/dperini/nwsapi/issues"}, "dist": {"shasum": "586660f7c24c34691907002309a8dc28064c9c0b", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.19.tgz", "fileCount": 7, "integrity": "sha512-94bcyI3RsqiZufXjkr3ltkI86iEl+I7uiHVDtcq9wJUTwYQJ5odHDeSzkkrRzi80jJ8MaeZgqKjH1bAWAFw9bA==", "signatures": [{"sig": "MEUCIQCrP7xQkWL8ww42JNYMQFFdM+d3AEuTW69PnpJD2FinNQIgHo57/2FfFyhtf90zUpx6jsvLUqwVJhj/pdBR+eq97bU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86656}, "main": "./src/nwsapi", "gitHead": "300cbe2f625ef2217059e0d3df04dc6649a52037", "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "licenses": [{"url": "http://javascript.nwbox.com/nwsapi/MIT-LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/dperini/nwsapi.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Fast CSS Selectors API Engine", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nwsapi_2.2.19_1742300488275_0.9300397937862519", "host": "s3://npm-registry-packages-npm-production"}}, "2.2.20": {"name": "nwsapi", "version": "2.2.20", "description": "Fast CSS Selectors API Engine", "homepage": "https://javascript.nwbox.com/nwsapi/", "main": "./src/nwsapi", "keywords": ["css", "css3", "css4", "matcher", "selector"], "licenses": [{"type": "MIT", "url": "https://javascript.nwbox.com/nwsapi/MIT-LICENSE"}], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.iport.it/"}, "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dperini/nwsapi/issues"}, "repository": {"type": "git", "url": "git://github.com/dperini/nwsapi.git"}, "scripts": {"lint": "eslint ./src/nwsapi.js"}, "_id": "nwsapi@2.2.20", "gitHead": "10076e3987f7d754e5d694efd31ec713583827ef", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==", "shasum": "22e53253c61e7b0e7e93cef42c891154bcca11ef", "tarball": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz", "fileCount": 7, "unpackedSize": 85858, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCWs67/l3ycOglJ4D6ba7N5lBKAHWrsexlt0QQLfem5PAIgfM0DxS299FPKy8vmact/raK1sFgBbqe0Z8Tjjqchw38="}]}, "_npmUser": {"name": "diego", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/nwsapi_2.2.20_1743143080766_0.009796784298611971"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-28T16:50:42.289Z", "modified": "2025-03-28T06:24:41.170Z", "2.0.0-beta": "2018-03-28T16:50:43.076Z", "2.0.0-beta1": "2018-04-12T10:14:12.038Z", "2.0.0-beta2": "2018-04-17T14:33:17.276Z", "2.0.0-beta3": "2018-05-14T18:17:17.114Z", "2.0.0-beta4": "2018-05-16T21:40:24.280Z", "2.0.0": "2018-05-17T19:56:17.274Z", "2.0.1": "2018-05-27T13:21:42.427Z", "2.0.2": "2018-06-06T12:02:59.979Z", "2.0.3": "2018-06-09T17:13:52.818Z", "2.0.4": "2018-06-18T12:47:37.653Z", "2.0.5": "2018-07-13T21:57:27.941Z", "2.0.6": "2018-07-18T12:03:59.105Z", "2.0.7": "2018-07-20T20:55:04.983Z", "2.0.8": "2018-08-01T13:15:16.341Z", "2.0.9": "2018-09-02T11:28:46.847Z", "2.1.0": "2019-02-04T04:42:36.956Z", "2.1.1": "2019-02-24T20:47:12.332Z", "2.1.2": "2019-03-28T21:49:23.254Z", "2.1.3": "2019-03-30T17:24:18.819Z", "2.1.4": "2019-04-29T20:42:41.585Z", "2.2.0": "2019-11-03T23:35:39.661Z", "2.2.1": "2022-06-25T10:44:06.784Z", "2.2.2": "2022-09-02T21:27:26.889Z", "2.2.3": "2023-04-07T17:08:37.617Z", "2.2.4": "2023-04-12T20:37:38.101Z", "2.2.5": "2023-05-28T13:48:10.007Z", "2.2.6": "2023-07-03T16:42:27.664Z", "2.2.7": "2023-07-06T09:11:12.930Z", "2.2.8": "2024-04-20T14:38:16.000Z", "2.2.9": "2024-04-21T20:08:55.266Z", "2.2.10": "2024-05-13T05:40:44.233Z", "2.2.12": "2024-07-10T17:17:11.409Z", "2.2.13": "2024-09-27T21:37:26.696Z", "2.2.14": "2024-11-29T08:58:53.625Z", "2.2.15": "2024-11-29T13:21:21.662Z", "2.2.16": "2024-11-29T16:18:31.678Z", "2.2.18": "2025-03-03T11:42:44.307Z", "2.2.19": "2025-03-18T12:21:28.485Z", "2.2.20": "2025-03-28T06:24:41.002Z"}, "bugs": {"url": "https://github.com/dperini/nwsapi/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.iport.it/"}, "license": "MIT", "homepage": "https://javascript.nwbox.com/nwsapi/", "keywords": ["css", "css3", "css4", "matcher", "selector"], "repository": {"type": "git", "url": "git://github.com/dperini/nwsapi.git"}, "description": "Fast CSS Selectors API Engine", "maintainers": [{"name": "diego", "email": "<EMAIL>"}], "readme": "# [NWSAPI](http://dperini.github.io/nwsapi/)\n\nFast CSS Selectors API Engine\n\n![](https://img.shields.io/npm/v/nwsapi.svg?colorB=orange&style=flat) ![](https://img.shields.io/github/tag/dperini/nwsapi.svg?style=flat) ![](https://img.shields.io/npm/dw/nwsapi.svg?style=flat) ![](https://img.shields.io/github/issues/dperini/nwsapi.svg?style=flat)\n\nNWSAPI is the development progress of [NWMATCHER](https://github.com/dperini/nwmatcher) aiming at [Selectors Level 4](https://www.w3.org/TR/selectors-4/) conformance. It has been completely reworked to be easily extended and maintained. It is a right-to-left selector parser and compiler written in pure Javascript with no external dependencies. It was initially thought as a cross browser library to improve event delegation and web page scraping in various frameworks but it has become a popular replacement of the native CSS selection and matching functionality in newer browsers and headless environments.\n\nIt uses [regular expressions](https://en.wikipedia.org/wiki/Regular_expression) to parse CSS selector strings and [metaprogramming](https://en.wikipedia.org/wiki/Metaprogramming) to transforms these selector strings into Javascript function resolvers. This process is executed only once for each selector string allowing memoization of the function resolvers and achieving unmatched performances.\n\n## Installation\n\nTo include NWSAPI in a standard web page:\n\n```html\n<script type=\"text/javascript\" src=\"nwsapi.js\"></script>\n```\n\nTo include NWSAPI in a standard web page and automatically replace the native QSA:\n\n```html\n<script type=\"text/javascript\" src=\"nwsapi.js\" onload=\"NW.Dom.install()\"></script>\n```\n\nTo use NWSAPI with Node.js:\n\n```\n$ npm install nwsapi\n```\n\nNWSAPI currently supports browsers (as a global, `NW.Dom`) and headless environments (as a CommonJS module).\n\n\n## Supported Selectors\n\nHere is a list of all the CSS2/CSS3/CSS4 [Supported selectors](https://github.com/dperini/nwsapi/wiki/CSS-supported-selectors).\n\n\n## Features and Compliance\n\nYou can read more about NWSAPI [features and compliance](https://github.com/dperini/nwsapi/wiki/Features-and-compliance) on the wiki.\n\n\n## API\n\n### DOM Selection\n\n#### `ancestor( selector, context, callback )`\n\nReturns a reference to the nearest ancestor element matching `selector`, starting at `context`. Returns `null` if no element is found. If `callback` is provided, it is invoked for the matched element.\n\n#### `first( selector, context, callback )`\n\nReturns a reference to the first element matching `selector`, starting at `context`. Returns `null` if no element matches. If `callback` is provided, it is invoked for the matched element.\n\n#### `match( selector, element, callback )`\n\nReturns `true` if `element` matches `selector`, starting at `context`; returns `false` otherwise. If `callback` is provided, it is invoked for the matched element.\n\n#### `select( selector, context, callback )`\n\nReturns an array of all the elements matching `selector`, starting at `context`; returns empty `Array` otherwise. If `callback` is provided, it is invoked for each matching element.\n\n\n### DOM Helpers\n\n#### `byId( id, from )`\n\nReturns a reference to the first element with ID `id`, optionally filtered to descendants of the element `from`.\n\n#### `byTag( tag, from )`\n\nReturns an array of elements having the specified tag name `tag`, optionally filtered to descendants of the element `from`.\n\n#### `byClass( class, from )`\n\nReturns an array of elements having the specified class name `class`, optionally filtered to descendants of the element `from`.\n\n\n### Engine Configuration\n\n#### `configure( options )`\n\nThe following is the list of currently available configuration options, their default values and descriptions, they are boolean flags that can be set to `true` or `false`:\n\n* `IDS_DUPES`: true  - true to allow using multiple elements having the same id, false to disallow\n* `LIVECACHE`: true  - true for caching both results and resolvers, false for caching only resolvers\n* `MIXEDCASE`: true  - true to match tag names case insensitive, false to match using case sensitive\n* `LOGERRORS`: true  - true to print errors and warnings to the console, false to mute both of them\n\n\n### Examples on extending the basic functionalities\n\n#### `configure( { <configuration-flag>: [ true | false ] } )`\n\nDisable logging errors/warnings to console, disallow duplicate ids. Example:\n\n```js\nNW.Dom.configure( { LOGERRORS: false, IDS_DUPES: false } );\n```\nNOTE: NW.Dom.configure() without parameters return the current configuration.\n\n#### `registerCombinator( symbol, resolver )`\n\nRegisters a new symbol and its matching resolver in the combinators table. Example:\n\n```js\nNW.Dom.registerCombinator( '^', 'e.parentElement' );\n```\n\n#### `registerOperator( symbol, resolver )`\n\nRegisters a new symbol and its matching resolver in the attribute operators table. Example:\n\n```js\nNW.Dom.registerOperator( '!=', { p1: '^', p2: '$', p3: 'false' } );\n```\n\n#### `registerSelector( name, rexp, func )`\n\nRegisters a new selector, the matching RE and the resolver function, in the selectors table. Example:\n\n```js\nNW.Dom.registerSelector('Controls', /^\\:(control)(.*)/i,\n  (function(global) {\n    return function(match, source, mode, callback) {\n      var status = true;\n      source = 'if(/^(button|input|select|textarea)/i.test(e.nodeName)){' + source + '}';\n      return { 'source': source, 'status': status };\n    };\n  })(this));\n```\n", "readmeFilename": "README.md"}