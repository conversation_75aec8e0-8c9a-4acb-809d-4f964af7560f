{"_id": "diff-sequences", "_rev": "95-7af497e7b1b95099df49edccda7b33d6", "name": "diff-sequences", "dist-tags": {"latest": "29.6.3", "next": "30.0.0-alpha.7"}, "versions": {"0.0.0": {"name": "diff-sequences", "version": "0.0.0", "_id": "diff-sequences@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0b3376e68c02ff5bbca4bb0d0bfe26ab67503bf7", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-CSJqGwmaBDVmXLER4qTnUa9f2pqh50hIxTXAlqQGzqaEioGAusydP1cLnirxpLERmxBQGPPzJ0rqxCKPeBPZeQ==", "signatures": [{"sig": "MEUCIEMuGVMuGnu3HRs70/hhF/rbg3wMVKBreK7Mie0ElkjvAiEAzMJ2IjI7DAcEfUlUFsf32WAzbxpSPaSfE8SmT6Mx93c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "5.6.0", "description": "", "directories": {}, "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_0.0.0_1517992034571_0.7324709934672862", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "diff-sequences", "version": "22.2.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@22.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46a0e57335062a1bd4d1a8b88dd5aab4207307e7", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-22.2.0.tgz", "fileCount": 3, "integrity": "sha512-R5ikcR8Evb/C4fZapeMSzTIaz2TOO7G+mxuzq/HymSoGO4B3NdxSrN3YijlCOeOfuDHUwUD3g08087Rdu0FFow==", "signatures": [{"sig": "MEUCIQC9JLBkSiSf1pDMVtjQ18W0OWAu5Lui5jAdFR149f6DuAIgJPkSXtkMLkKjFx9X+6vm54vlnl9xHemM7uz2oTpafC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43111}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_22.2.0_1517999151975_0.5374213453134911", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "diff-sequences", "version": "22.4.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@22.4.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48d99ea376fb3f049215ccc8606ff4154f4516bf", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-22.4.3.tgz", "fileCount": 3, "integrity": "sha512-sMsQetU9mXqUEYpjCOavAKdEs0w1Qg9sbRlfVbaZUFnkJqm4iEQj0H4nocx8PMT+AnPKJW8C+xzU7LuO0s7c8Q==", "signatures": [{"sig": "MEQCIAmGnVn94ZUUhZWXW2SIUxsOt61v5YLIg+ppkcxtImuWAiASqunqh8fTXkJ2dVApuEZaeH8kJsDg6pk1W2X0hWnC4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42649}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_22.4.3_1521648478627_0.22926297299456455", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "diff-sequences", "version": "23.0.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@23.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7c0ec0a9ad5c7ce4395ba948a8f040489c88ab2d", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-23.0.1.tgz", "fileCount": 6, "integrity": "sha512-kirRd4R8LoeOUV9EBafmU3qCOy50Xbg7ZrGUnk0bWgWTS3BdNyV8C3Wdr5QuCCZB/FkuqrPQVD11H1REJuRvDA==", "signatures": [{"sig": "MEUCIAMkjLooHUYuD20anK930tXfJSSxwJPF4Fg6qGCNbjcOAiEAwhgbeK9rCbCPkhq9kOs8G3JPkdS2xJHCIq2tf0vofV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs8gCRA9TVsSAnZWagAAmtIQAIAQbcb0nn5ULrGHqoYg\nBVRynElLS+qktZy/mCCyE7b3Wy70TbxzIeyMpHDLLtwiGxiEbBEcL/Ti4gBO\nc7dMrUjmdi6NHUCxrxf2xcDoQF3YLC9rCtGH8bSVF2m4u044jWFMuHnMS5l2\nKQTvQc99iezsQjGaFcJi0LqV2UZjmd33WwYTQC0gyNiX59aO14wHR2PkYWUW\nwElfOaszYhUJYYlUOOq1tnztct6Qlyi2dMogqI0vC2Rm7dAv4jaWQTBsGyFx\nPitdrGB68S/O5QH6eZdVHPHXFY9MRZoJ9yB2VVzsk2yxcJGtavQ3aDQBDlCR\n2iKhfEbpKjqQFvEpVgpk5cSoh6dfg7vWz6LxmQShoUDpPYiQrVI7+/95znlv\nMTAzx3WLvgskKiqEhbKOo0y6R6/D2DB+TSdx7KVacZIB7VnuHz6NZ/rFtngp\nZLryN/HVgtWNS9ia/RDZFzL+RFQJC1vEy1QEWmy8j386Numckf2cRdyaB+29\n/1bxx+81jtQPW9C6SXEowKr3EmMVjKzbaEQY6aLM3Jo9ul+WcDEMVCm2rfLD\niZwYSI/K8w547RQUVJu2L01Nsi5mNU0JECN1yOjUQlwGd7pWZDZwesIrUqGA\nZ1O77BB+M2RBgifZemZ+du0EeeFDsbN90hxO0LGhxX4QuEQNqKjHSIFfoADW\nX1tF\r\n=yTEV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_23.0.1_1527435037960_0.3578688685807707", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "diff-sequences", "version": "23.2.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@23.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "77b4ce1229345db8744a8be5064063d6072c9678", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-23.2.0.tgz", "fileCount": 6, "integrity": "sha512-oHJQ2EqKDLZBi4RNDHh9ueAU0HzDGwX8rPeoTJrhlW0eMwZc/SYqEj02zngiBl7bFyDsaDVRQsPjqxMAL/2Kpg==", "signatures": [{"sig": "MEUCIFf5ELSxHxM+wqopq0nVWv0YFY1eHXcAxpjcMTuu3HW4AiEAiRnXNfceGkLQaJCdhziekbXKo2GRkvlSYmqk+qsmuLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43645}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_23.2.0_1529935507744_0.33531794695212747", "host": "s3://npm-registry-packages"}}, "23.6.0": {"name": "diff-sequences", "version": "23.6.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@23.6.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "313fbfaedd00565a143521a4ab05e007e3db297d", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-23.6.0.tgz", "fileCount": 3, "integrity": "sha512-JgoXsgVauu207PGb8Aw1wd78EHTXgBCxyf62G8InWaesEMacGseOcA6u+3chhf9Z79sRC7eRvnZzpvpoAGNwhw==", "signatures": [{"sig": "MEUCIQDN0Q6WPwVjgF1r5Wy1OIkaJEDBkJaJ8vSbjmZEC4tHCAIgGdiINAh+ls4LB+tis8WYyRtYWpU8EsNapZL0R+TSooY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblmbHCRA9TVsSAnZWagAA3BAQAKRb3UAL02+3dm0exuPQ\n3cwQkjOvwcIpNErXYEFQlIEnocO6mYMZ9rxX4wdIKhEt/w/78yBCC0EU8pda\nUJoNlZ5c7giw00BeWqtpwHXP59wtIwqClC9+uDxG5CkKc57U1wdMxNSr0Nvv\nC2p2a2QJ7RDQbiySGRDQMlDgWSu4PCrilJG7USHjr8/dYvjNJRZ6XjSo7FbC\nbCwcsX0yJ5wnwT1zkEfu7lEihH+OeL/ZBzl7OOgtin+MaJCBkRqmdyqmYRsZ\nNwdlV8OGBWlGF5x2QKA77mraYhPPn4sJ1zpHLntHzPV89S2WcTp9RbAZlPns\nA/2Amct6OA4cgabMnq+THlbzfXSIwSmBv1uIpsz2OEwOjS2wrk4dupL9+PhU\nRH+tLd+jqfxSLkusmwfjiabsPhGvoG4GT1nM5J2/Nk3VVUE/uorjrECRuwda\nYrixZBSJdF5qMfy47Xf6A+emr7MMFWUA1nsGzqXmmW0uR2L6XOq8ouQBLUvm\nH0yvqNAsKxhzMEIMks8LeGp7K+VggiZppqytaaVPUbhtnmXgh7qNPHmYnhZY\n/SHj9gJZUcU21M6KXpzqNISXDS6Qh+nGy/RGmQlB+Tg8sezaRtkGP8iQ65l8\nXRS4rmcOpIUvJpCxwcMlniyBlVPza41DJVQ8qEDVCFgPZmmOdnGoZtGp2ThF\nv3A2\r\n=bbk7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_23.6.0_1536583366712_0.4829199435875795", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "diff-sequences", "version": "24.0.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "797d848ddb4751ceaff3413ac70f78f942e1f39b", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-WSWafrDvGnCpR1NbH46d7NRWGwZNdHNP5ckXg2UCo4RtoIjI+xY5rQbPmx8etsyXMbDKGQoXpPMQLkaS4+W05w==", "signatures": [{"sig": "MEYCIQDHiq5TdtN8+IuK3e/sTLfkZyHY1/6Ew2VVmwM4WaVB8AIhAM1pD5Tk+IfZNGiJdW2x87t4Y1ZBy79gH5NvJmuVtdeq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycooCRA9TVsSAnZWagAAy84QAI01TwymvV2Cbtz0ErNZ\nqB5Ae6Ob6kVM7uyKPJL/OrD8tZW4WgDJKlMUc19Fio3DUkJqn+hQseGULq+L\ng3Z53L3bvqIVJYg6eqHORFa69vbP4NK+BPKeQ7ANuKgNNg6nSBr9qbw1lkHx\nO7e8QtIe56Gu3rHY9WWP0YU2lkFjR7V89Rnjm2daeBdtj7SpGhwa5pS4lltb\nFaen03cjEwJPtWcL4ZoUm/Ho0VvH/yJBRRWUAopp1++/z05LOC/O2ABqzm/1\n1De/3Fs+ZWzj6quPBMRwnWWpjdgrlMMz/nkBFRwFZUVjK4TsZ/q6/QlEyvOM\nGfdBoROo03hbHOp5eIDGrmOMt30XDkmaQlcDpFLLwQyx2JZH+fB+iXGNbtlV\ny6jToMW9p95fFvT1rw1OD3ivVshRb/QhYzPRFomtwJ5T/ZrgGbNxtj/C2dNo\ntUrNxL1A2mUkDoR5DirWnvYPk8mgUzLu9+81FSdbdCZhQ77i4DgAsBLnc2pf\nJHK9CtWZjC42GUYxJYVPlOTQTI5OTzmeGiMaWcwBYGTVJ5xELwje+amHmLKr\nasvLzsXMsnv6pT3a0lf6luHJfzIeSkbJNv2zvOBnlUuTqfL8e7ieCTvWzjM1\nBN52ufhJC0Y0GV4QDpjkW36JJbzrNtGHSrRod9fBOFUob27mcC8ji9wO/ARt\nD/1H\r\n=/Fhz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.0_1539951144113_0.6432423922570261", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "diff-sequences", "version": "24.0.0-alpha.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0cb8546f71e6a3d157cb92fe4b0a178e36c48f70", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-Zg8RcfuLbNlw2e3gPBldnhRR98543N78jaIN2YYYT2OS4wX0AqmEzhCgCTfaOrcu0V8ZP78t5qGRKqHQpqWlyA==", "signatures": [{"sig": "MEUCIEqWaRmJA872j2V5Y7xtRlVCUQaeG7pqaRiPgKZ+nP39AiEAqv/8yzQpkXt5MyGA9fX5x1p1T001CmsAOAwGar0Q71E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5ACRA9TVsSAnZWagAAvR4P/1aeInJFdy/V3N7d0j5D\nePVzoqXoZ5wFuDARGn49GJ7SCor9veXMe2qS46/4wvDFVXT9IVhF/WSo8dYV\nsUzii2Xa0YMGsyd/EVPIEGs+IT23XsiGwgB5Vj8ndJUkjCkD9kvvziTsvieh\nu9JJlgga1ACZfrBj0JpEoXHkwA+oKkq75jfwwtVrkkQarowmbKv/S6BvyrVW\ndggNmRsWddskXnx0mCBkObLzDnrQsXfyQEUaUSzAFQOo5tecHgXSl3sPmVi/\nJWrQZfaJ5bZUFaUr8bKCh+DmRS8CH5zfOsphF5elSpe+IId8ufCTeFTS9qRK\nKXEmyy/mycETUF2rdBTfyPAGJv8JdjSigXA5vp15fQfR7WvfYPaHgpCGjKdY\nILDTVPldiKosM+y8yib3kWBnhftky0ijjsDvBrseWx12ApTRluqaewLiqjeD\nsDsuplCMlUhbrsd9fxxc+u3960F8ys1R97QGekxAeizPJD8SlF2lB6A73OSr\nm8h8a39PaJY+AIQX1L/cvX8XDi1a++lSKmkMJnTBCutr/CFjFAFycqtKGx2p\nh7+FdEqsHVD7x8fI31jfc+GeIZ4GQDiGH0oFYrIIrxE90/1gidIjo3qOwjdN\nwVa0dz63bRCoAqO0rB/G6bsXjW/oA45bo8s/dere58RwzEP+oi0UlSu7+tgS\nwQdv\r\n=zGhA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.1_1540222528051_0.5059020296169037", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "diff-sequences", "version": "24.0.0-alpha.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4b23cec46266dd8bf61fe57b1cd1dbd1b6a37d6d", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-3uur43tBpofbkM19o+Xjy541RN/34rkk4dBeX+5tB+wpvEowCCalUJYZYo3OPzHh5/6iWx4Ahiq6AhUhtbA99A==", "signatures": [{"sig": "MEUCIQCP5EZsoy1oONwQFcCFO1bNrmvidD6IknN4pGj57iYSkgIgIfvg80w3GGZBfAUH7M3oX4HaRJxmtdVRF7qYxSMni94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0ZuMCRA9TVsSAnZWagAAI70QAI4RTnkpXJ7Ecqgia9cc\nLGQM0Xv/+kSPu/udzwcSoALmK8rN1CLWOpFNButebChp2KbzzIKB4SUi5ACu\nz5vEva3jLsazD7HX4yc0h8t3Up0dSujqtWvJ4XeozXpNeO0skdtebvj6r572\nUJmr6uwBEvnFvwYWaidZJ5tf94ffBMl8qPGXOmZkwzDJe7t6WOf7rggCurQj\n1qOfcSGf2KiEKqKS0apiUPkpuY6cH3hIfMlsnirZkZR7oJY4zFepYfXM7V+N\ntY9g/0PVAHLtAzWG+BfF9EtqFgUK+nfYo9fEtdUE02ScEBo5tLyEhLvXAxlo\njK4GeiVXrtF5vcoa5Uxe4SKdam5BEGOenlfcoBNEkOh0TBVvTg35BPQulirF\nTNcKTRF0o1alf30p/M6PZwfd+MMsBwyHAgbfCoOfPvPJegBItrrAlFYB+2h2\nk9aMOD5ToyzbQfUZH5ptjjjQTpUx6BShO/lokz/hm3ErgW3TeEa48O5DYmxd\nQQ+psttFH4G4023gSfEN2kWpilimlG82D84lQbsO0/pdkp2pDQqFfE2u+8Lf\nlIKqoZn3G9Z+pwwbPeH4GAXfcCh1rNCoI6Mc4POqKAal2tBsmBnr0EVF6aIU\nEZ1A6Ew0f+dJvIJc/9hGNoCD8pUd5dprhhPFjsCKdLzAZd+ejuyO4rzUxRhe\n/XwS\r\n=Dy+h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.2_1540463500064_0.5225365388805607", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "diff-sequences", "version": "24.0.0-alpha.4", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b77b2500545c5450f73c7cf79692d433941e3fc3", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-DCA33T4dWFk5C8q5eayR6zmkQbAxWO/YJT/QDinTyrakMuVDuDdoQbxfN/bOOp3eF645e9C4bMxdbIVLAtrcwQ==", "signatures": [{"sig": "MEQCIGBdzAYtXTG8aLlYIa/tMwdAs2s7phCPO7+mH4GyXU9CAiB5iKqLmRZM51IrQLaTO0GuqLcRAZjObLP+vTlCwU4AMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00G6CRA9TVsSAnZWagAAf7gP/0t/ghZyZZ+L0GLGfOKb\nOSE0sPp4va2KZmqR++BJqKkredJGeaITpL1p2coCfehLrhvBYexR+MMpi/0p\nXllKTuYRb/e/UrTjrfao+NkKZPM6hRgT01V6YFEZJVgylkS9V7o6BiUvya1U\nFUTR/OROaXs+QfGlH5WfW3jc68MobxDZavH1AeGxFsLAzWKq014IRqLryVqY\nCMCNjYD+9ONiLg781Bv5pQnZTBjz4Zw0ex+5ZCSgruVgKKf9EbXT2YB6rieO\nkhI/bU5gai7NFUTJbISCKQPkMaf6NI6ZWZbOX11sfn5GYyzIplktMgRbzjKT\n60LL39wfOcSNbIAprKnMlPIgwkAR/X40VR0vdo5oqiZnv1lHdWKPpMGwqHIK\nkWzJ8/XVO1LoGU6+XS/o8WD7ynuWm/umbgUUcY8NB5CzwhcZemtvlZWSHNO0\nUIOrqQN7s1GIWq/AE1MA0u/d4pPyjZErJyFMK5oF2t12qGnpz+ykMOlU2ad7\naS9fKrEe+8DVUQbQEZdUQdJ+ABHJXsKnKQriZC4xsOU02dh6ru8GQ9gxIJNU\nOCNWkm3LsT/MD4o1sV3eRSWOoLosy/VEmN8z74lOl/NVAF7chq7sjFBFpJOW\nHUF64AfiWlJN06l7m1iab52vioRDKyKjovxPYlE5dI2r8+BOCFEm1ln6fRyu\ncz3M\r\n=VPkC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.4_1540571577591_0.5130622776524432", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "diff-sequences", "version": "24.0.0-alpha.5", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f6a3201d5cbc05c4ec85a8782918e40f7c070cf", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-08H4gdY5M1CBc3wJEb5IJJhLbVKz4o4ZLkraaCGTuKiEKBXqqnEm8GfABNVScBvX9XIMeYsSm9ubh4KxG5u6Og==", "signatures": [{"sig": "MEUCIQDBqQTrTSJXnyfS8K8mkkUQHAo3M6ucaAvDPl9kNm4WRwIgOIBvFfVfmH5Ryyvm1Y+h++7UZDEsuuGpmncJjE98aYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5Ye8CRA9TVsSAnZWagAAjNsP/20MiL+/8bHRVgl7ZHHz\nOZKr6fZfKw3UL14Vr9JiHlckvACoUMvuHUoV8nqWCOI/G2rb/gStEwGDEjhZ\nkP6dJnWMnAkF6c/P8CzAHY2m8Z2zVvRBpBC36+o5Z4wc8eMucdWhI0wJ9Uqs\ndMH3bGsBXICvEywqeGX4qBUvw8VUvlQ1da8L0dwqM1EQD93OZShmhfQKUZYi\nWTkdNBPMFS+tMWOsu2srPenOfzZv/aHi3PwIcrcYQRqlWDT7FqEga2M5FrEN\n8OGjho/tIRyuN164aUpzdQlmA1PedYBgw4Z6NPBhrYkySGxv9g6T48lKpzkN\n2kgLK3+L2YDGHVxlSzzmVuZSWJuXrXJSzAA9c6JgVsW8elTag5BQ7Gp9guDn\nq2BRILj5ZtAjsuELnSY5gyB559uxPi0AU4036OLL89bbm4M5RVm0GqAhWC7x\nQ6mVDRhe0issPHCkMx1V2d51LWgnN+Qs4/4TdMzcdzt0KTCElwxUemNiw61K\nac+UsB2hF9dMa0+JS9WYJSN7pwOU78n4SerJ/6XKpOvGICIVhBWSh5kdxb7j\non4AKnwF96tbmMi1OdxD9LXgltynaMTetQZ/tU/17589bIqiiDrZOlSKb6fG\nAHkbyWt1SSTMvUv7rMsbgr2Tmpzf3FuSXjiJzHkDl8EcmU8gfZjXHoXHVTcd\nCMrt\r\n=+41o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.5_1541769147515_0.16601082038693682", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "diff-sequences", "version": "24.0.0-alpha.6", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d1421aad3daa4a0c412b8653582283be34cc652c", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-iJIwG97VUgRnhNWeKi3Q3S+C/Ihdbb+6xA+YwQLrC0I0cBeQrsnhGZPAjKMKWktFJMaxnrFEY7OKILodbhF1/w==", "signatures": [{"sig": "MEQCIFb4g5V1XxA1cCRZQJdxTmxQRU5daQ2jD6gxgnYURBWjAiAPtHYQ+41Rn/tRzo8C+VoCXGsrUcQakz7E46ZZ4dFryQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5cimCRA9TVsSAnZWagAAWZAQAJIhzq7q5CVszM/AP+mv\n7E8Ue+GQKumAqxIvyXNyfvQnh00XhNAHEyrCTx0vD8548I6jH77WbbNeGXf2\nYsoRt5v/jHcvCDJDfLEAi6prF5YTmQL4uk7hS89KiUdajLRXJKiyGOX8i1PG\nEOWUE+sah7WPxt4rb93J9fafL5gJUhs1syBBPY2aj10G/d/Y+8kknrpwYI7v\n83/PGD5THUePNkd5tYeTlYpSRN2YTDdjKvblHHorfpU4/RVeEeFBb/y31dPY\n/0c0in9iAYrKEuw24mx3b9hDz4T3OxrlZ1JHzbVflreJYV1FaUA/ISMsGos4\npn53jCLwgr70bs8Pl+ymph95zfpYI+eH9IdTAU0IXynKa4o6Ttt/UYnEdwuH\nzVdUo48pB3qSNWcEyfw8T3bp8v1aUrfWteLI5uCBA0J1+V/Lo/EhJSeQfvbO\nFkjSCkm6q+qN5LAm0AyuhrkV+aRuEmmbD9ay1JPLzBgWlHheqkPRnCb3p+rI\npCcYBAQcqLnfQfU2Tg3rkPhdgv68MMOdCu8KjOEA2EXxyEIYdTUPnVOQISG0\nf6fGA8X3kq+2hbdP77KDzf2rQ+PNoTbu6OO9KYkp6mSJSYj7S1BpwGxAdcQR\nPbgaa/2q0xGbXP9TUobso4BvBdjO9oBra8rlb8I0bIS564MweoLxV276krzO\n6vIL\r\n=Oi7G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.6_1541785765740_0.5487019499237025", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "diff-sequences", "version": "24.0.0-alpha.7", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0dbbe9eeb8cf2690682502750c0f14ff2484c372", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-Qd1jtHjq9uBfYIP67WdGl3WOm+Klval/VjmdJUah4xMXKVpgAwMR7F1GXsvxOCzzaV/2Mub8sksO0/nno/LP0w==", "signatures": [{"sig": "MEYCIQDlbe57oiavQa2fWh4zVHYIRKoSXhewDONRuKivD/aj5gIhALidFnQz1CpIiDhT9JB7DX0xAfgfj+UPH/q6k8tGTfts", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DDCRA9TVsSAnZWagAA2KcQAIPqnmYL+RC+I1Dxs1Jq\nLfGNmcFB7DdIbvaWuL65BWgW4wV4riFNbPGtB1z9cTAv+YW5wSbfvy6wBWO6\n8pbRNe96ozCPkehdXvez9+3OHPz+9LEcCvA6PbK0l8PbD5VeRZiC3sJYgOrp\nGgQ/3Hfqs0KcL0f3kwXuCOTDcoWIRV7wmUMnETpn4Gdn2JPyk96GjE/p/30c\n0+I3QG1C/xkEdrcs9tixU01spzyVNVuETcyvGaCYXaZVH2jGIaZuvAqhssnG\nGQhWQwuyIiZHYYIjtB7HDnWhEUojeK/OcN4neQ7XJywXbvIMvtBezujo+B1w\n3yELK/8fbIM+ky+9rGJfQ+fbR/J5PshGI+OAlKV/F1HAcrhFyHYHCItYTj+5\nSK2nuz2fx8EKsh1CU9GhADPKA8nkepk7dK3vlmKQ6wr1PN4lNOMzzxQFgwuQ\nvGTUnKk2IM7uqcgSG0QLGp8A1hvmnz+Ep53i4rfzjqSahU42bkWXvMwofecH\nR8sEq8SQyvngm0uNHRSFmLklDL2mcc6dHNYXlLIUkRDDxWzv0YsGEZdpc8Ix\nqMIPh3FwTOI7gKzqvDwjIzTswhyj+pExua+caRrs1pMLW1pGhzkGPv35nSKk\nkGf9L6hQcoDSJDQs8fgm8yY8Bc3DWLCtEPiryPsXIDDl+XqwK7BVjq0IhE5v\nR62f\r\n=7m3e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.7_1544544450385_0.8767994851184364", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "diff-sequences", "version": "24.0.0-alpha.9", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.9", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fb1791b7ef1734b012c80306ba06735f38684e0", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-37umEX2FPEYkrIkTrgo/8jcTTUC44BwJFVgW3N+2AQxhDFvNdw/wXp/o61sDJkyxaQgkY8OsbN0+PowXWrWLeg==", "signatures": [{"sig": "MEQCIEfdL6HxRAg3ipTuZoxQAf4Z288VtXFjVeOgPw25DbA8AiBGWgSz9MTzD0qR3tPilc6Zg8ILowMKk8z8FNMUd03yVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlQ+CRA9TVsSAnZWagAAy2QQAKQD3KnPT4lvfbclk8SH\n31TmfEA06KG9F2WNZzuQNOm5oaMazDpuUWbzoj0q4UkTt+BmqaidhkP7E5Ru\n2ORYAGCy3DAhkqA7JYX0sAYYPD1Tbddh4Uc3uUWxduPWkF1f8dE3iLO8Y8Lb\nfpGy3FXcdxDpAJknFjwUHXRszedICuigFL9RAwRScJ6RAsaNjBZ5eaiRk40+\nvWBZgHpk40X/wB+rLfYwZTugQ9nQPefPP0fv6E7tAk7WZCB3YqQhdkMJMRAp\nh++sW1icKsMWXQb69pfXSZv/CvY7u8472hj8gQHhBou4vZ3KUM4Jw5CFhbxC\n/sEUYwU/pAxMjrBYc2XIEe0oynhs3dPgL92fcrthGtGCf8a29fvSrY5Fh1wk\n5qAdo1e18/jh+TM8hYoHqnSFSHFhJQuAvNR085C9sFLtNX9M1sLoBeEgNpr3\ny5yTe94FmiX1+97QR2i6Qc7nBFFNj1I1VHGPqFCO+wVmvrKMm/BMOJJg+NsV\neizumTcJbO0nzK0UN+DhtQFyqga5XItnvO/0TjSHIipTKcWduy6jQ62bZy+P\nGHJu87l2Hyq0Hsb+akjjFPx1TswHQwZ3P5hHYn2yo90cDp2XQX2kA7aXSKyJ\nCo5fGQPWv2ZTPFYGU1LcwsoqeiEW6IlGR6bhfWm/5LmVal4/VfE+rKLlhXKZ\nE32Q\r\n=UhEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.9_1545229373441_0.056509693246487025", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "diff-sequences", "version": "24.0.0-alpha.10", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.10", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9bb880f84078eb01aa4a72d9782d138e50c84cf", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-LCvx5HWJMtDYuQmzw3hRz1pESpcXDXqQNW7b0uyoJzD+RFdPII59arx6xv5AepZjRf8BZ6Yr5WIHJZFKkRC2mw==", "signatures": [{"sig": "MEYCIQCXjiZ/1UKY7M0uksXJAJYXONR5CBeEHBuA+4lbmbwXzgIhAJhY1NT+07KI1ofadHMSy+Jampp0pTy3ENlheVlO4zOK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNijLCRA9TVsSAnZWagAAJysP+we4w/XgctdYhJxNrSio\nt43tU0D04ANNMcPuNIeK5LY9ZHitIzoMcT7K5aTXOee5f2g+6D3xfOP+CMMX\ne04eJbT1atOOo6//Q8jlU02oyD10FpflbOyAnNh9FFavSL3m6CK+3TRgqCU1\nXTgp19WqztjP2zYP7cGO54VWy1VZaqMJ7jQB2+BZ7TwVRI5nlcs1rwMnUaHh\nDastI5jKGlvoTT+RXsflGnbWt+uLQjxqeW5mWBWGWoy2x4HU2VKUsYh/y5T3\n/gfff9oRUVeK6uNfICwPkG465wZawvlqqCfyu6whKplLQTAgGeztuVkfJloS\nbeYXTQLrncUcXgEzOaxgc+e6rPEuGO5xFu6Fv0LM1GI6wNDM/lygSL33mMp5\nrvvRWVn/cqP26beE3qY+KCyV/iqROijXcfZwU5jM1QicknMDnyDnUuB59ssp\nQTghPagMhS8QZuFWxU33beMyn73a+RbWX43Y9b09x0++10T/dKpanTtVBZJz\n2KKz3SVxoVAHyk8iB9v9TPtgGX9W8lOBbFEZpmlDboRbYTVhgKQdOAGxscVS\nOlTFNXm5PUA8fqHyZuFGG2WlwoPu7Rc1lGhUz45BuFsaw3IJoRvJBD13/M5C\nF2HbHJ+5WB3ra7Brq0HJDLj1nvDJ8kZlGBnXE3PETX0TxVwRAbpmzUK6MmGj\n68Lf\r\n=fuTp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.10_1547053258708_0.6037156508529322", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "diff-sequences", "version": "24.0.0-alpha.11", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.11", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cb6bc9ebb9c4bc3f5e6bafaac786b326124a3fbb", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-lsJ+sPk7bPNH6pNhoKaAn7xAvIV7yfJf7c4lrs+gGAgjrj/e3AgyFYpFQJxhsDPBHJ66R2VAxfAbvdxyOdIFNQ==", "signatures": [{"sig": "MEUCIQD8hU/IagLLJwTu10JQbEdswsBl6i3gQrvPpSqvAaFL4wIgCjBeFn4NOp9ApW2jcYlQP74ESaVwEhSUt4E5GkDv0r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN5HVCRA9TVsSAnZWagAAqMEP/1GVp4EP4LWJ3eBnHJ0t\nUcJWKRM0dtsOcHiP309xLPuqEEixHDJH5O5r3AsgqrNiFNW0HHx0WW36opzx\n//H2Ilr3bFkrzX10LY/JcSwQLU4fsPZ0x4Qe+H55icOLyDk9251O57qizONT\nyi+MODzwwGkXCBLaZuweyy4NHMi/9xtgjEysE4nJhZqdVaWxX5HUOpbw17Qi\neRHstP+kvPtdiv3s71mECAO40VxwpcWQeg5AwzIpPMUij4mcOUq/VT26HM3e\n0UTWvyBy9o7dT1FfycHuKIjMnlMu1vMIJtm2zmIhNoHqKwWRBEX8aXOwbgow\niINwv3Xhi/pLHUQR6pMPaNttbA/jAqdkEaI+lc+GZkbJTxvrxYZzNd9QcQH/\nkL69G5+DAVpFLN9gU+5sFBeIT1P5GTfHe92yBOsFpLsAd3jN/KgfB+kbHDW1\nOi8OO9ptDhCbuw7uLsZuxq9q5rDFU5ecwBfEqIAp1cGBsXv2H2QyUI67NUkV\nM/f8LhkKYsITE7LqR4dJGP0SCClIW14J0b9KCowYGXZK5cPJyZODOW0AVLyt\n2NDYRpuX5IBGDMgbWINgzlyC/IGcPQIR6C+10A+TxjVInsm6XFOlOYv2uTaG\nzekQ55sPeMJ5dnIvxWnaPIINx3ArVuJZhLCESacEQLmGTNYdrR/AI83Xb2LV\nQ7aC\r\n=Qa3b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.11_1547145684380_0.5361225083077126", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "diff-sequences", "version": "24.0.0-alpha.12", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.12", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "88cc5aebf11c60b5bfe01414a10e434d4e45311a", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.12.tgz", "fileCount": 4, "integrity": "sha512-xPxZbI1Z5Vey4ejIePUiufup2tTz9shYxZGrqQviEsB6hqGOJfpSUbTkthKhP9pet38qIdZOSu2N/3EVDY+NKw==", "signatures": [{"sig": "MEYCIQCuHt6MvI+eH6nvR1GujlEDdMYIQrblSLxr/lGxd+4PSgIhANS3pbW2HmGYlEb4WalEiPtSL2lNMPEk5hIo5oor1Cc1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK77CRA9TVsSAnZWagAAL18P/3x/iubYIrfILK48DhMn\niATKnjerkmqsy/TqLjujz5FgzgO1mLLtW9+qIqu7w3NeQspSQu8qiVZsG7lW\nqwcgiR6qBwJoho+x/ELkKWvUkTsGiStF2hSVjDr4LZrMraWvIfcQZ/s3VL/4\nSkYeLAKDD4mcfYA6Q1w0G5XKNwpQOCSHzi8Dcc21fdKm5QFZjLNShWGYXnUM\nF9K1K5K9rt8+zIcL0cNtdisSZ8pQ4FP6bWjPosf5TF+hOFCfAaF+3mHGgar2\nhJUrEPftROjGNrRltveSxMfAZ2jTu4vzEDMEl6tq45NV+tlCZdHiucOabkPZ\npDX1TuHAfN6g2wUvertAn2qBXohIML26gp90ij0b4+DtctcFJgLTO7wBX4pS\nVIwMb0kh0lRsE7kjOhgdfENKUOWGI8Qze+oF2RRuuoxYASusarFUnVyX/y5s\nfz8nzoou9r+OjaedDkWkQPdl1ecJguwzfxc4/xo5Ki+QusU2Po+5cZPro0kF\n6VUaQOLYVN3ZIDFb6mSAUnvE7X0G7AGH9NjNKG7K4yWQCTj3vqAwxiY71buB\nGonZlbDTcPjZWDfTQOzHcq5yEIQlTsRle7exnpmN7ACbSvchg1tYmRQkDqkQ\nYd3YyUci6kqm7VzIIhW4WY+LL234lQhDGainniJl/5JBJ3NGB8xcwRgc0e7I\nOdqF\r\n=UDWW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.12_1547218682692_0.19852421640191076", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "diff-sequences", "version": "24.0.0-alpha.13", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.13", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f76ba219d05c5495f71a78316427c4b9c2cdca5c", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-kDzeQ7m7O7STxJcNK2Cjlt6kn7hxzvnulmdxZbsLOA9GN3ogQ4+V807ocMmX5ZyVF+5wyNtbFACJDqseg8RLJg==", "signatures": [{"sig": "MEUCIQCbEmawqNgJTchrIYmZ9C2pdJMirVgOzE8I2AkZanVQWwIgLjNs1mkCsdp7mAR/PK8IxGhANuJUykaLL0SOS/CT2Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUECRA9TVsSAnZWagAAXdIP/1Cws2YqCyzvdEMXvhha\n+5ONZMalv/PWpwZXDERs7xIG2iDK3qNUXML0inrE9j0ERlU78ynheae5hR2O\ne0Rxxr7n59vyg3E6U7OQYSt3cz0WEZndQiQu0hvBrA0Cbk6iQB6Z1V5Kinnv\nUGEkfWUgXL+gqLjcMp9/qd+hgwXZuW6B25jJ2U2BsDXnLJ+fgdpUd2TZIzc3\neSrVfdTMXu3o1tZvwAQzPUlEoJp0pesiYiFK7715wWj+oRLYDziyXwUgX0S+\n6zPU2K+cXYDMGNdPfSUNzn1qdtraT2bfuVZBSfcbZsCaM2p0DZM4dMT+UeeX\nqoXJtikasugVDZMexyHjgY1EhpZ9NwfxMiAzlhYXCZiv4ZCVG/3OOAzWYZ40\nckj/WAntcI57xMjLwu+C0AWNIL+TfTcxPgRvz+6BzKg1IT4CUglJM6ze3eeX\nqboCDmU9TznRnKtsDA12z9ga4+BpWkcd1aV/HOU1weIuQWpPNea4wUMd/Fy6\nUnGzs8e7jXOou8k08H748bM5+onC3PPH3jUWqCIoM/x+Bb9LrB2dS9O9mxNu\nPTzeLQ8jZpA4bGgN8zSuQmXqTsF8ZcCSOGIGFGsD8V9WvWnYG/ZuuYJwAkXu\n41UBUGp8sVv8Sy04Nf1+adEKaOHvi96fJrPa84u6vldkx9V8U7zlVqn8ywUq\nElyP\r\n=NqU3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.13_1548256516166_0.37449123475823987", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "diff-sequences", "version": "24.0.0-alpha.15", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.15", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "29c6aad320390ac8b9f5ba48b99939fc7c4815e7", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-acw6spdcMlLaPN4+h84FUelAjABSDTQJklOCe2cZgykVUlcUOIC9/IETuVC/1uCU1Pd6QcmloBSNSBBvoi9ysQ==", "signatures": [{"sig": "MEYCIQDAeWX2DjZyJcVET5qBlsX1NxIbjYyQvOeqb60SgJE99wIhAN0yMVvi/zOkFDAhKNrr7sl6P4KWlBEwylgGIaX1TAsJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftRCRA9TVsSAnZWagAApzEP/1gwnieIQ8M+bA<PERSON>aOuJP\nB1iRiY4g3giptsLACU7UB24UEFvfKGrtMKSIKpW9+hbNn4EZtLFRCi/Iq6LM\nnoEX1xXLu6ZcBWDS3p2pmcq+40eEpcHbatF74i+g7k/7uYhcDZCRs3CbFoFm\nKK16me0bAZLKfBACfjCh/z8S7BaiSmjOAzBqyYi2SZxDj79V0uXNvzIgjeKr\nSBVrya0Ogv2Yzj9juaEGBSzMRQ0U+X0MtEOcR3kaWds1PZI5/sKt2XXRktSs\ndpaRnIqrbT4SHWqSWg1+qE0z7H4U2HClumA137QSqeE1xD2W/RI3f+C9MPYU\nKWMmg4s/xGe4HfAfMhxkgRr10BuakTPycjAZivMhcyX8ttEKBDjDB7Ydzd2D\nGSicVBwSx0MpkqQ+XAzMxlRO2vAnRNIq9WAq49LBxQhvM9CkMNI7kNxozKOY\nixb4Hc9n6H301iVWNyUz92qnW/SnQ7ds3JU4ruFYW7mG2bsKGZ/2kF8RePNe\nO+sMQ3MZUQwD9Sv8inpL/WejOZjyS9XRJACnvS+p2wDG3GEMDvElf00GS7Bt\nZB0N7WTdNmN/hw8Psntx3/HDc0ztVeQ/VThfS7CA4+OEBvUg/vVM4eVAGrpV\ndG7g7H6e4u0DbkMDuuHhir7Nn8U7W+wdofK66ogPDIuLPrg+U/9EuD8ALv0k\nPcrN\r\n=1VVY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.15_1548352336663_0.6978139683358666", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "diff-sequences", "version": "24.0.0-alpha.16", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0-alpha.16", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7d80b224c276f74807f6149f4f57aaadc0f695bc", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-htraczXyXMsFpucX7QMlB7PzTPjl/kawKgx/Erih0QiQfZjXgU+RTWxRdjKZtK7mxMnsoV097WB0UKxAldb8Jw==", "signatures": [{"sig": "MEUCIEgE8TtpJVyC9a6CoYiGU2zORsDJyGJDqXXpiASnFWdQAiEAiNPqFORyFarCidxRYRemXSbIm/smubNu+JbbhjXpW1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIcCRA9TVsSAnZWagAA7jIQAIXz6ozsAOZXurPUpVDq\nIC8R/84Qt6q9Lf+LGbSFmYJZQrFgYWR8UQx660rx4wJs/bgn6S/0OZ9QzVSu\nAGcCdsZBD6ZdBUf/Z+mMKXwU3vF401pQkV9wpSHU7w4hZsxQ5D3AqU7pIGO6\nxuY/cGInyZ5NkFzX5gQhjM3xvqNhzmRaL96l0jJVnvBjXZ4XqYLP+IInQOd+\nx6D/N9pxWFsFs77Y3aaw1WKC2eqZvaos+v9Iq45WJAjTIdDZDlYKNZRMmDnu\n0axWhtEVrroxaOfTCZb/VLv0C/q+DuSsK5stFWPc/uWwW3Ab9PtE0WVY1W5a\nki+C84Hxz8KgGRjHqhxCZgZv8uf/BOoY3u7vCR+sDXcnngkyIp5da/F6x3JF\nlC7mPZgabP5zVPsM71h0JXnq/1kbKG2/0x3ShwoJSRfaGhEfbiwlu8YUKZ7H\nn8c91Xane9KUfcrbV6zMq3eRaBa+B79V/nZHqCaqjdNjF7ouOQxT/A535l8t\nyJaqmDo3v58h0f3d74Kvh+jS407DREEJmOVWm7y0uRAg4oDH+NrXl1eAidj9\n3fozWKXuXUlKR/lzmJ03aYiMOYCpOW35wjgcql4/FHZKnVkA1XA4MFdw9ZrL\n51IHAX4Cy+YU7WXB6Ff9/Ws8G5Or8eQCMmNC/sYIJcImNo9bNvdmcf9H5ZOq\nECgE\r\n=GI1X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0-alpha.16_1548423707835_0.8804922598482618", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "diff-sequences", "version": "24.0.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cdf8e27ed20d8b8d3caccb4e0c0d8fe31a173013", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.0.0.tgz", "fileCount": 6, "integrity": "sha512-46OkIuVGBBnrC0soO/4LHu5LHGHx0uhP65OVz8XOrAJpqiCB2aVIuESvjI1F9oqebuvY8lekS1pt6TN7vt7qsw==", "signatures": [{"sig": "MEUCIH9UMjHTUuJmKFx1ZmQ1kZJcasmyExT/XOW0xweicyX2AiEAppSOPI9bv6ZeO/C4O0E8ixIkyz9DqKf0gmyQq1TDSI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWLCRA9TVsSAnZWagAA3tAQAJnyHBEbpsLIiAwqstsQ\ns9mFveNztcceGZzwd1vIzNrjRwLsi0/8UssRxvUd5XNN04JF30OqhkukbbCP\n+vgkwuPX36UjGmcCMzywHwvK8jMdWPaZjvGR0Cru72aomgR8cIKtpLtChH8X\nDakpcwBDF79T0lhIjfVlraGjc2QmXeynu/rMIQC+tPT82dCseJbXuzEkTPbE\nByUKXaWy+GDVqEiVMTNgwwLXECl19u6wJinyGxc6zEdGp/WuY9/T/RxGeZAi\n8IxnP/gOjNZPl/W5GJjPAfeqtZJZI5mlW3SHT9kz3yoQbDWcG4hnL7u7MQem\n5/SlaERtSHeH7neLZpwma0lXSoCv/VzhSKLWtUEdFFNshKGvGi61+Kybxw/A\nWWxT/UnpMS5nA22Ps77sj47GC6y2TBmxqUJAFMGvbvqV8mNB9Kr+sQ/Fm0Jh\ntUX9nK8GiqNSz3/QrdvzQmCbEgqK/lF2hI/QwkXEzX7b0HN0bbDZcVV/98wL\nkHfN1IZb/qDNyrRSt1ofqUsHMFK8gga7FZN/bF5vkXUNj8KDr3XRpfg3QL2q\nWEg2oAv0P00FOYSRb8GabwfJrp0Sich22JDwk8hR6MrJMxAxAHvFwChtWub+\nYqbp1ZnHiMBFMaerocr3j9L/EWcRpiw5SrcRjBhU9FunFPMIUDgtZP2ywjAR\nRwEp\r\n=VJhd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.0.0_1548428682371_0.9163471860444559", "host": "s3://npm-registry-packages"}}, "24.2.0": {"name": "diff-sequences", "version": "24.2.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "605025e678673636d82c49eda94a9cebcbf4b648", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.2.0.tgz", "fileCount": 9, "integrity": "sha512-yvZNXjhIhe9DwBOKfdr1HKmvld95EzQkZOUjlZlPzzIBy01TM8oDweo2PT9WJmaHd8+J7DC8etgbJGHWWuVJxQ==", "signatures": [{"sig": "MEQCIHT5Wrub2llhSS9Ef9zJwx6nsEBiJJ4RnlczF5FFPkJ7AiBsMCZoQMHWItPdbVtDKEnmPHgUrr47azyQZR2fG9PvHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcflwECRA9TVsSAnZWagAAfDEP/jwxpZSfrrCD0oz5QCND\nqKpDsTLAS7jDEnRvx33IT+R7FS35G+w+3V62oRWISp024HaGEfQsuu7bH1FI\nCJkQuCNMsulkgFpkcIDtHHA9HIKlWy67ZG9NQZplljqQljoX3OEeFoV/Re3G\nq2U22A/mPHUGCkg1UM3dwY7nf2cCzYtacdL05IJ5PGTFDIGF0qZgFaOjjacJ\n0x6KidOdfb5DiIxTYegNYdbBR+6ELKO7aTFJeMWkZMT82Efs888lpHe9fFv0\nV4X5k1Z3o1zLGeKu6JdCv+SUvz8B2V7z+2grjCDZtSxlQBz0JIT21KvYmkCl\n1u/ZPh5QhIBYLrs98QC0uFI6lKPkp2Mx683Op2hfoG3Bd+vQSvh5KPpvSBRl\n3uDgbfBuGTyMpX4cbY1BYcGVHNFCU9G1e+haw/aw7ygi6mecp2hKmIzvdrJo\nBR15By7ZIJcwvitXN3nqg8C0Xna4hhmirylG9a/wkoqjyJL+NiCcdlH+i15f\nf4IcWphhqYqdXA6tOn5999spSzwajkxka5WM00mQkAxD40humG7Crz2S4on2\n7x7gOh9tLW/zhPEDrqIooSJ5EJZ7GPdS77Ukz3QtFDlHtAaTu6IjoX+1hrjO\n7MA9htlnXFmbdsCLA6zwIgMQdQZCYmlP2CFw3knDNU6YGwV6MXMb/igJrP0T\nTuOe\r\n=7jyY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "d23f1ef18567763ab0133372e376ae5026a23d4b", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This version was accidentally published and might contain errors. Please use 24.0.0 or a newer release", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.13.1/node@v8.10.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.2.0_1551784963744_0.9274550559945209", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "diff-sequences", "version": "24.2.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.2.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "958be148e0bbb42462eb3594f4a92b3d8019d418", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.2.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-MMeyO36PNBG3nVMpMrwkeOMt3nMshozRd6F6OEtbyOC1kozdEuToWF5nMOlCitO1GANJw9KlVnxcKCWbTDI7mw==", "signatures": [{"sig": "MEUCIEDP/InAExRScP2csK/A9rueZq6ftILBW63A4A62xTTsAiEArAiwd+IPcnsx/Db248GcORuUvCV2S5/GpmXFCVeNpBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfou+CRA9TVsSAnZWagAARZ8QAKUP/OV7I1kTn5/LdMVt\nu1UNXtZOjpXEX4/NxBYq2WAd+usOOvEYQ/ABdjQrr3l6GUh1H1Rt4jzXruFY\nJ/Spa5Tsaa04I80KFreAdM8hPGzXW+maxMJRKPEjNwa3Jd78l7xgG+y/cwGW\nDWk2BgAb5hL1rNZm51EO1hfg66LdrJJTM1sPxzLSMlKZrHdaGPcb0gh1bqoM\nsnZ+9g+PB0fm58czBwSscAyI5IujLcDiQTTUh3RfHCiXgRnr5p2EBLSpXyDX\nSpLyi/ayl5BIKk0UZzbdjAoi1cHM/IMQkXgvFMDbx/Spz983M84bGi9HJZ2Y\nd1wrajfzqJrOVNK5prgze8mWri7BkfxrB2s/+gQN0kYtgYsJZM4suMZ8c9UV\nXIxsjaK4Pg6SNJfbgg2O0At/+RkyrTlOlgfHNtIxgH5g3BkYFUHA/4aq2Vyo\nHSUYUkj9grp67e4Z6ytkRkOdcaNwEdMPW/EmljvBbOaIixz7bqxf5hxT6glC\ncB7jcIxeSrXejQtJc8Bi8WYszuUmjIzt6cTNQshjWYWXqX8xowOgyDW6fYfd\nw7Q5+gzBI7qSEsalvogp4AgGBPe3QaRHe5KmT70SWl2az6QXNhCOzcnj/3/3\nUlx2fTfk+BPCYy+95qW2DHXIAwMKgr6rWXVrJIBZJRq0pPlZcxCNVdAA4AS9\na3Jh\r\n=VLfO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.2.0-alpha.0_1551797181846_0.29220347658793466", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "diff-sequences", "version": "24.3.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f20e8a1df1abddaf4d9c226680952e64118b975", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.3.0.tgz", "fileCount": 9, "integrity": "sha512-xLqpez+Zj9GKSnPWS0WZw1igGocZ+uua8+y+5dDNTT934N3QuY1sp2LkHzwiaYQGz60hMq0pjAshdeXm5VUOEw==", "signatures": [{"sig": "MEQCIH7g/1+0K9Pt+9Sfibq0rbVgrv2GVTe0Fsn33264DfyeAiBL8uwwhd3im1kqhxNF9eURGN4QhIoHOxn+dU865/divQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAACy8P+wRhord2dcPOpPSLqtYP\nNYE8T9akbqMpr5PJ727ZEAvyhT2rfHUovSFzZVzlE/aLqfKhtOSolpozPWXR\n25LZZj2DXsStGcUrR6VfKrFnQ2rVKNWn+Yg7HrsvYdfakA/Ti/2JpS7Ble1n\n4du54NyGAg8r1NLhUqaYzI/898dytQl/YMaS/wEXMBrEsLSn/2K3E26xFIkY\nTTsbwzXpluC+QXy5EOUA/UuoWK4hjLt9obbzeZUMrYJLodSO7U8oB5O6DpM2\nzW3B9wzEnlN9SY7fDDYzL+l55tMnW1ZdY6Dqnd2vBrZI95AmdJIOa17k0OYn\nvw/5P+vO0oOmBKT969IKO2rZX0zSssTg6P2H99ZmY3bA672IPl+ppfvL+wiS\n7f5kP9Jdr7ZnnKU5hx3fPmQF3PtiCv3nafTGzBnYOgagyAhUBzDl8Q9pfC1F\nE8mxUEaGRvtjvVGngrE48eulLmLnOk2K9SJkYENEaf/yYjoKKd5BntUQgUph\nFbd1g1RsE12I754QlAOE5cS2YURfcfjAPFpSzT6WGB1C0cpITABP4Nl57wKo\nnMUh8zWVPpTgJTAxij5X/f/1DomUxsG4yhJ/xeVhwTLFDdZISWOq1h+GTpRx\n7AtjkUtf3oJhBmKTgfPpIsl/EG5mZgPTxb4ji5kelc4X58rEPIb6llE0QBKk\n1qhS\r\n=Dlia\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "8.11.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.3.0_1551963558692_0.7852251620556123", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "diff-sequences", "version": "24.9.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@24.9.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5715d6244e2aa65f48bba0bc972db0b0b11e95b5", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-24.9.0.tgz", "fileCount": 8, "integrity": "sha512-Dj6Wk3tWyTE+Fo1rW8v0Xhwk80um6yFYKbuAxc9c3EZxIHFDYwbi34Uk42u1CdnIiVorvt4RmlSDjIPyzGC2ew==", "signatures": [{"sig": "MEUCIFVilRVEiSDTw0NAdhO+1sN76E3W8D/la6+BANkOQh2QAiEAk6oKGJ4X5khepop8plCYxdjAHLlfkyDgH0wIdXLlY34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAFZ0P/i1sacmf4y6R7vno4Mlg\n/lkFrIy5NnIuq0SiIkQr3J3dS0pViQK2WurJgw+m29Cf2Dfwl3l9nlhUAc5M\nGTuWDrFlIys04sgO+7gqvnZBYCxdCVoXSQKKof2KSD0urDkZiI27g1jYEYEm\nZw32R5YlCy0d5uOXxwTqTXZ8ESFTYOInR/kJdWUjl+8WR+So/aBptI/HUn7e\nYxqwl0FKKTVrW5gPhPD0mloCkUHRGy6QwlHg7eYDv9xuYoJrF0EWK6LSY4qo\nIGNiuXWAwY3gNWe9ttRIn8ystrsE0fKwIk3DlfbWJIgAsUkqZSIKSc92DcN0\nIX6YUEKmWfTLo/6J/8/ys4tlpMzJ+dXkSApCbddRC9DCUVsFeg/rgnwi3wCP\nRtbg9Nmxp4JmF4+GXbM596lrjBKhJFEvOoK9AzrlOIt/fdgg7Uer/jgKuc66\n/KeAOx04zwhk7yM07R3R613TupR+CvLXX0RXRcfHek6MKFkkWcuujtrE+szs\ngNJWznSq977vhIYncY0OkVxHaswy9632HiQa+cQDAkVSIpGC/rFdYWdDUjRk\nF0mHJMnZSaJJTtSTD/lj1tLxBRvXQa0uqsP44azcf1swlPjlTbRuX0IJxL9k\nB2eCXtGkl1aQYns0GwgVWgd15ys5JGNe45x6UUOOF/eOCaNmG5YXH6XSioyv\nRaLB\r\n=4z7W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_24.9.0_1565934946970_0.6896997727763712", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "diff-sequences", "version": "25.0.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "12e39d89da8c559b4eceef169260d9175683509c", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.0.0.tgz", "fileCount": 8, "integrity": "sha512-6KdBSVCp69YOkwCFmAhmJ23A05e4VSrDpnx0gRHkkLGHotSw/r7gSZH9sqP41Z5iAgeXE8UEZNFGO5sH90vXkg==", "signatures": [{"sig": "MEQCID8sEnqiOr5kNcA5wyJS/JG3wMC8ZFsPoGbOqlTpLy7AAiBJ9YwVMJABGxx+N+X+mHY81qJmqj7GB1zCpkJ838Tniw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAAREsQAIcqBDjNo1DJMr11Tn5x\ntD6N79oJsYGYRQGk5JAOTjDmnh3ONinsOY00gF8kuapWKRhXmeeK87tkQAGr\noT120oaAItEFxyBufHL8xsrjxlBB3OLJL5ds4qnfZTgCAbvvtChprLaonXMU\n/kCfx0LZLib1w4t41soSW1+lDsbpjt5C0WaNEmy/aKY2ce9QLl3fWcgTFMK7\n4m9exYeFoY66gWi5F+PX2WgG/Q4dpNgNf6suYZtuJdAqdEdPw8fk5BlT3Nag\nyLU668XVVSY+bmiuflqoznwEa/yV1f3LOkEc62HGb+33pl76KmRdmycyJqOr\nVmAfWGXq0ZtnBMZTT8nPqRqaKIAwtcTJLzSJz2TP7fLaMxFvqbKXYqM29RW1\nCHyEn0ss4w/rQ2SEjfbjiLb7LH2F5d/W7XIJyM9ZQioNHUsQ2gHVVG3fL7Dj\nPQAMKp6xFF0t8A4Vq2snZf/oUTNveNkgJavV67zpSsC6AQl47+t1WPcU0I8P\n7VOBn5Jzr+Gnsif7DLEsZ4jJY/llaJ/mOIbPHh18+rH7MOtnHvSADHvmW4Zb\nJkVDzZwNpA+SAe+InboWVjkDGddDGhUIhY+wmBLUaEwlTijrwx5J/MyETtKn\nC3UXgKbL5NFKheEAcLBosdUO/g0eu4iSb3DFdunDUhMvsISS5OycC1IUV/9t\n1GzW\r\n=SnMr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "11.12.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.0.0_1566444224445_0.703563251151955", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "diff-sequences", "version": "25.1.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd29a46f1c913fd66c22645dc75bffbe43051f32", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.1.0.tgz", "fileCount": 8, "integrity": "sha512-nFIfVk5B/NStCsJ+zaPO4vYuLjlzQ6uFvPxzYyHlejNZ/UGa7G/n7peOXVrVNvRuyfstt+mZQYGpjxg9Z6N8Kw==", "signatures": [{"sig": "MEUCIHGh5zeiUwvGQExyNbQoTVpVt/xUYCMWNV3eF1rJYHaXAiEAnLNlYS7mfY4YrHpMNAwKRcIyVg7kySbjqray1yLgb8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56BCRA9TVsSAnZWagAAEZAP/39bKZxTi836JbnjCiJR\n/4syUdiMmeY6r0Cjbi1zJQfSO0fcyrzAu6YPgsZP/ZZMkKDaEOQ9apLe5qNq\nVm+Mvm6e7MEZIXsSBXqUWAnn5h1EX2oCopwFo1td17f5TbMv7iWnZ4mM0fJJ\nbCfKD4P6YFrFVLGO3rreNWWE9ATk7/BEIwWp60IELVZC9Ana1De9x1qnBWi7\nLA7UHudcyEf3zs4OBykJYENjFWfqoNNRnOzIgYQdxFph8v/YyGixKRHg6qsn\nzclSzF2Ufy3Nm/kWUAw5vL/Iz3EpB2tGsxkwkfPBr+swZtslaojJtNvasQbj\nhVnDqK2oiSehc7OgujUJC/QXP1nD+xNqDd0xQLJPjSgL2OKvdRcPmq2MDSBZ\nuUKQZn6hoMf74r5warNG6fVHDuaQcFATbxkxARyyZzkKEdWYNzOBBduVvVLV\nQLNoUdenE1mS6SM4bSLgW27y2FXII8jvJ7FWhH6bZ7T5KVfj4Mhw1qkn4KuT\nIDSCLDl09N975QkKxZ0f+8oSIO7zZOp7hjBXLKIR4TY/aBeGrX+F0HSlH1qF\ng53dTpjA2XwZQL08IV00NXWXOlb08AFa18EfrsqCaG97mHWrtuAH4Deb3aOb\npha8YXzTN/yf96GuchfhkQttDnrF9cuMs/ukHs6WnJoz92LIbaoor3z0SpGC\n2LEt\r\n=Aw47\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "10.16.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.1.0_1579654784507_0.9473791596126149", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "diff-sequences", "version": "25.2.0-alpha.86", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.0-alpha.86", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c7970a3f6a3b61908865223adf006fabb4987d10", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.0-alpha.86.tgz", "fileCount": 8, "integrity": "sha512-KPD8O6FVrpMWU3oUWwQLCr8IY3QkM1nYvdh7g/xk2U/kMBFSiJrafiS5noIyDlH6syjNDsmRFuCJlx/Xamyhug==", "signatures": [{"sig": "MEYCIQCcMqiUvh0D0lfSYhedBjVCp48Gl95sMCUg3xWdwz9qbAIhANJh9BLEqWscUPQFNVPT73ElSGcNo4wVGWyy5sfaFxoP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAADu8QAJ0Faf7cW/uQ4E1kzP+N\nRs5U4fHLQT+FPpXWycD25uxCHa9E9hlrUuViW52/lmPqY5UFi48R/LRiOwNn\ndZT8El5bkUo/lAke3JnIm2LcAwFS9vsEbMTn8RSCYGWW7BLtVZK558BtqFDY\ncN87sor8Wz+LM+cmzZPwtZE6Igrpk6zcMOkAch4zev/7C4vFA/TqiGbNbejW\n5B7b03r2lOWpq8sI+Q+IDCTzapJoBecY1O2QHGo0FkUTlqRVa9hxQRN1shf1\nED0eLUNabxc6sfHT2izFjrbp5WZne7TXPX3AnI0SmfQA5lv4cLlwVjXiNaXQ\nS+QZd9a9iZ+y0Dm1OtlCREiP8r+SXlenB/KB0FS51oY9cLIoMo3aH6Xlaefh\n7hZYZ8G7pCyjutTSZ4HrSE7uKGZZZVkdzHym2y4Rtqicq6XH3zg/B6btmr3H\nJhpu1sMiPqD+y0WAGbRC6jzgNQsqDsepTlrpMVl7cQZacDPQ80fBidKSB8EQ\nTnizCBsE4IhJLsZOW4nS8CXKbdDOdHXI9DVNcG6V6pOvdgXpfuahB7+ATMsB\n0BI42kVG2TQInE0MDDSn9lpMKbrMXZgWUxNPB20seouEwueoR9X5m85a98aw\nek+S0GGtqYFF6peKCJSAnniXC9cWs9fFSv5xzR6QQjmDjSBX4rgEKGEqbbon\nImn9\r\n=xm70\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.0-alpha.86_1585156571169_0.36378894271963924", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "diff-sequences", "version": "25.2.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "180bd89ff45c490b175de6dbb1d346db7b998a94", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.0.tgz", "fileCount": 8, "integrity": "sha512-qTbUrz80F9q6rmEZjUoK2/SQTwgaOvnE5WjKlemKuod1iuB4WlSjY5ft2VUXacsqD9pXrWmERMPLi+j9RldxGg==", "signatures": [{"sig": "MEUCIAapp34xPByEn2yblxSXmisTO+xn944Qub0vnCyh/56LAiEAx3DiKOsz3b87f7G+lZyktkqmmBKNXUU0rYzqm4AjBR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53140, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5umCRA9TVsSAnZWagAAk9QP/1DL307vfoH6Z4IUvlyC\n1Wo3BWe1rSpVcetjY8P0PU8W55SycMQ5pClJFyyfshLcUVrafPmr2ZECQyvT\nS+B8MboD09GbbqgAxWzvS4AFSGgCr7mC34xH9iEhx+J7JM+pHV2OzWy1kdHN\n8EQGSc+eUXcxbzMltLd3x7gGTzNpbWob32lze4BFCctc4DxNCYpYhrJgpjVe\nxKckKDR1lg0ZvlGYuUtoiVvaC50X7NqOBq/KYlJqzdzDZF2W+Wnbn1Xig+ru\nVQyuIMgczb9YtlKQkZKwII6i7SLjLVjk61LZk2ys3BGS4ilL6TyK/o2TtszD\nMTyw264yBz4zenwmPmahT02HcvSqydfJXPF6C7B07GxRPS1pCaOG7G2CTe/5\nIWleMmv84w7vGNdRoG5KQqwUXirrgmzWFND86fa3YGMU1cvYwE0Kpz1S6yeE\nmYHU4jhukoFyhIuJ6TJrZV9birk6UFN3dFgF2F6EPOEgb27zZlb8wDWdXPMn\ntfKCweYFIKKvJKSaFCy6bMwx+sVNN2kl6jS6qPrxJBb5sIFopKKF/Rj7MTLU\ntOZBjeomDDPQ9aSa2N1AuXj3m9+YKDuNzSR5pMJp6/r8gFMEazC7O+GKnRxi\n1KRkMtkwlmvJ4/+VqxsogTrM1/j/a+lKOBViEPPE3jeI2lbteALyMod6J7m/\nwPWn\r\n=+fbg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.0_1585159078401_0.09206383670667995", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "diff-sequences", "version": "25.2.1-alpha.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.1-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca1ca72782027ba61c6c6b679a4e58c9d1216469", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.1-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-5eqQQuJs2xADzUd8IS6WlancVmViz5LJHIrgXr5wIvtxfgCYhQPO0GHsVddoWN+Mifa0EDj9QmBNM4Xg5wZxVg==", "signatures": [{"sig": "MEUCIQDKvEFG6AJDDvxf8d+IIncmwafnALPa/UyKdy+yxe2TEwIgRktLAUqoOHGYbF0vWfo7ybztQs0AoHiB5BOYKDsPy/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+mCRA9TVsSAnZWagAAMa4P/RFhA6/mc8md5mckpNYK\n8Pt7Sy7RMW+IM8ukn1hDsxnqnQ/cQXFQNFYlMtM9koZ6FrBGofWcBGM+v/qo\nXxE7NeCWasFQRokIOheqELHouA9XzqzyDNTq4en5DITK4W015lGKWcyh4Yxz\nv7hQklmj3GlC2602OmNhtR3i8xiei0iZojbMmX63akBqDVaWCcy3AMOssgeI\nOl9YHVbdXyNodPPIWdz3npQ/7RoqRmMjCsuLasVVBFuv9xpjdbPJBLQHfDk8\n6wIiPxS0riLMwnhYoIYo7UG+bITvpkxvzFJYgxBYLdbxEFiJb7JkMheyFA6Z\nXxnpUDegLGkxDMclO6orT1ZK9xCAY/D4P0gxo88qiM7g5YVxq2O/unHeUVY0\np4rO3Y2p1Bp2B33iJeK++zNAhMYn1TFB4l3CpTYb3PjmhBQpE1tTORl2yXvH\n5r0Bt9VCz2KxzI9JL74vaVfILAOqIu5V4wNa/tjn8ijsphpa5TIaT64cHaIL\nTm9mqmbQRSzvAVj9/avXvp7jZ+A6aBg45JKRt4JEglegoI9iwfozMNUNEPBi\n5pNGP9BJX9imHqcdECUYaHheuO9iFO3KOnC/kqxj4lfDIck+87EtRZ06nqC0\noyYzJqlkIBiB+t9zXuIP1GMXKr8xO+WtEN89cab9v4SCk0O+eqbO2180QYnK\nqTCA\r\n=yKmJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.1-alpha.1_1585209253663_0.6680817370295136", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "diff-sequences", "version": "25.2.1-alpha.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.1-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da2510f09903ab730a68ee8406bb39f971fd32c6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.1-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-tVKMc/E+HzEGxTnvAUrHgaSDeiC5syxtDvkAhG+W+K7sXQhb9wvUa3t5Rg2EdIxEcmfbvCcY/J1jSC24kM9tjQ==", "signatures": [{"sig": "MEUCIQC42LC98LGTGKxqx8HNxKL/uysH8xo7WgX3aU1XzfC9ogIgaq2NZBs3DfF5Z967MZeaxKH/pJ2LlpQGXYdIWF1I/rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNuCRA9TVsSAnZWagAAkpAP/iHyAlw/YEaaUDMjC6tI\n9X65L3wbWV6z0LPfHB4z784+p+JBSmVSry/b28toWWo15KKUnybQdGuCbrDs\nWJ9uDIm20ZE0L1Jqs2JHwbu89jbOCmNJLl0ZWoOjNDmvRUKqMuaVVDynWjV0\n3dCpwewM6z3paXncuxKmzyba5wGBs5/RclI17GK8wu+JUasW9p0T6Hx+ODHF\nzlKnToytaK4GUbv195emrgWyIoFF2g6iwIeycxSncMlWlpJtQRa4renBUTaw\nfZISJ6gK3CzVWI+6THWbX//Z0fXH0sPqN2NGRfn87dbogeAE4vu5wQcq/Q/t\nuLFPfX4u7iw0VuNtxE2zLW/ZyC+QkgawXlwnrGi2pUCC1bOhUiadyL6EYezV\niphr4RZvauvfyE7FcGy2oWxMgi2S+i/pPOxFQ4++9ErFR8DupPaa7oIucIHb\nDNOPqwrDdURdDzjoP4NWDnj9O9LU2sFGS9aUL7KQACTCXSkLjXw/X1QBmiOz\ncfr3cmn+MnSMBIv7DIhwMMmx+LPdGTQPz98fiQ6iT1qXI2gD2Me/VNPo/IfH\nUQNfnfptRPY1Jtwi5/U4YDvk1mOhWnCWUVOCVw4DG2kvfyXj/9CGEb6HXOqv\ntG9V6pnvc9QrvN57GL6i5j4s4l07sxDutFrd09fLbQPerTkeWVatpQmE95QP\n71or\r\n=4AHA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.1-alpha.2_1585210222069_0.9125051342980572", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "diff-sequences", "version": "25.2.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fcfe8aa07dd9b0c648396a478dabca8e76c6ab27", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.1.tgz", "fileCount": 9, "integrity": "sha512-foe7dXnGlSh3jR1ovJmdv+77VQj98eKCHHwJPbZ2eEf0fHwKbkZicpPxEch9smZ+n2dnF6QFwkOQdLq9hpeJUg==", "signatures": [{"sig": "MEUCIQDsvKH7jW29d8EcEf3mACQoDzHyRDpu2VjlZ21WhpDeggIgFQHocZmHeHiuc2E6oIt+Od7Gq4glhReKkW16eLIEodQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9RCRA9TVsSAnZWagAAzFQP/RxZEg4ccsozSMkSlKay\n240/gCm76MUHjqB88FpJLEA3RG+gr8RMeKD7KhC2J4i8lOF+6/LAMrQnpwyB\n/Ap118y0XRVmLuvEmz/FtXuR9MBDPlATMkOiQVJbBZZALczacT5xS8v01fFF\nvAfvE37uhgroSYKY3u5l7ue5LKhkJOPSOEKpb9YWnbM4ajMsv9kYyxoQv6Fq\nzYXT3VdHAvm8LZbQ1Gs3ETs/7lCkaBum5qsHLB7vF4eEy/75l80YcJ6S3f7F\nVLT7yPVT3k6rRfLxsin5u1l2S+lZoXdmTEyRfpeuiKSeGnXhDhfbPnzU+spZ\nlilTyhdlKvICs6kS/Mr/GeeOoqEaCVI++WbqNWI02HMGR5N48WRiv1Xwlt0j\nfU94DmZZarvuUlgbKrVm4exisqxTDpyc9W0VjZDglVVXEyuQF//8PZmLEI07\nlniD72oG/rE2ZHQoATsDpvCFNsUUZ5WXYV56HrqI6yiH/97RE5rhUmF+1k4d\nEjEUjDpDDzZtCo+tcpViKBTGuhHwUXibfAFYtD75NBe9KoXSdUbsX9KUryMo\nZKCvR+VARimEGKfSHDB1rboFvyUhu5qNIZo4LO3HZEp/GsSSJW8YeSXT0rbx\n4NEUKT5XK77XPhGzqLpePO+7sod/GxMs7EhnVsU1kICH8ew4Ssrhc0Hpo3ea\n/4mr\r\n=TFGs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.1_1585213264802_0.602644697730381", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "diff-sequences", "version": "25.2.6", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@25.2.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5f467c00edd35352b7bca46d7927d60e687a76dd", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-25.2.6.tgz", "fileCount": 9, "integrity": "sha512-Hq8o7+6GaZeoFjtpgvRBUknSXNeJiCx7V9Fr94ZMljNiCr9n9L8H8aJqgWOQiDDGdyn29fRNcDdRVJ5fdyihfg==", "signatures": [{"sig": "MEYCIQCeUtOlbvlxfaofhPs+IaQqS2c+zlMVJm31ibJpf8bn5QIhANb7QZFa40Vhys9bb5bcFafrPkFLuOa8Qa8J+DxQkbSN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb51CRA9TVsSAnZWagAALNwP/RI4loe2UetR4jwvapxQ\nJ8v/B7DFwE4VOHBKbZ5LgoHSMBE5nIvqBqZNMGvy0+18ufl6O5ORlOlO/CyO\nzBCv+sce4ywOtnUlp5d2YOLLBZs3GbZIdbktPfcVideSIJdJYeGy25UYWFMF\ng1cAFvofeSmb2mjUCfgz+3LJZUyGdvA9rmQu2KNnkuzBjHQwqxnMR2bXcx7m\nmWQrXFodLYuaJChOTGbRnNGLw7RlXIiXNIp+QtJ7+GsDecVHmTYjrSsi3uE/\n7d/ugvrC5IT3RwXAaU+Wpr+aekAp+8N8vDCjhpRyqztP2VSITCzpzmNlhRhT\nyEDWORqczigvT2ZXC6fzc2FBRxsar8/Rj3Z4Cej7EvC4iRaMRYYitfqSacKE\nH9g2PouV/vSgL+9oqC+a2JbLDdZwZ20gIcd/jb2erbdv4xqa2NPvAL3Rcl3E\nKNDvXTdSLk0GYrJavUvkYopsiwH5SqAnUEQ3mDguy2dFvqkTQUZ0FWOVlH5t\nv54My4edMkhOudf+Df6Q2WamB4JYMjVT9IPTRu5cmXS0AhY0Ft+iry4h+0A1\nyPvZLg81HQ3UeWoDkIdt/zv7gMlDSkz+97pdvAh03iI9g+HAKVHF7QcoMioV\n+RC3WMA4dt0xAzuqxTjQLwyruv8yp/L33VUefbCv4FPmdM/PnzD/1uj7tGre\nhuYj\r\n=EPwJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.14.1", "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_25.2.6_1585823348872_0.44856025691436185", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "diff-sequences", "version": "26.0.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@26.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf0049175dbebd590534fc38705de6323e64d208", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-arNQZtgy/BIy9d2TBnOyHZc/651Tay0fya2UBYkWAKFpXSS4KhlaZ4YGA25Rgdh7UGPf14tbPnf7QrwOYXkwMQ==", "signatures": [{"sig": "MEQCIGo5xtoNgWuVIVV0vlCb1PI47fHI783MKDPMyt1R6OyXAiAbNx14DWtz8Z4yN+LJrzoaWhpUHPAuAjz9/7BRpDNysw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPECRA9TVsSAnZWagAAu4sP/3klpklGvdBg5Alboaqh\nL4TJlxElktjIQ/0XNyfct0I8SNSlxyiXnH51PAFa6AwbPezmKPnQj2RbaJgf\nSWSha3wuqqmCNNYiXt32HGOOvHBBRCZL6mBFJRCb8L+pAAL32d3bIUclF8HL\nIG2toqmIkM4tubZ0jF9JJB4k0fKPUlYfY/1YrZa05CKSDiHJkj3B7eoZNdOM\nKJ89o3hF9ALPBZes4eKj+heN7uFot8bnByU9wapTnA+soz85fTIGmLXfRsLD\nKNs+OgB+qRY0Pjd8duOAZVgRhKH1bGVF78rfhh1sl3j0YKltEiCIPUjwTbaS\nLX2js7gqgT+t+SY5nzrU7BKuwy15bHZcSOmcvKRe+vzdEyeu/bqXZtte5Ir1\nBO6glU5V8FD3PseBKLd+rOQWxGhgmF5pGn4jj8D8gOq9GthH1n7DIqEd7KRO\nOZkyGaipyJKe1U9pgEEPpfkee1D2f2VPVizl9+EuY4TxRJf8qHEA9V4ctgr7\nxrmb/187onNpD98e/fDtaeFNhBUuwzQaRaVZikuD/pX3P6Vlyv9/3SnTJmi0\nvLZWfLKE3d94ck37W8sxpZ88VuiDieXszWxlF/AQzJmmPHdzg8wqLBsTFl34\n8H49HPfiiCDp0CR8OkMd5zOeo8ZezypkjSLSaT3cAwwyEZW1EIuEjPa6orFV\nWptk\r\n=YQbb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_26.0.0-alpha.0_1588421571517_0.9402255981559462", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "diff-sequences", "version": "26.0.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@26.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0760059a5c287637b842bd7085311db7060e88a6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.0.0.tgz", "fileCount": 7, "integrity": "sha512-JC/eHYEC3aSS0vZGjuoc4vHA0yAQTzhQQldXMeMF+JlxLGJlCO38Gma82NV9gk1jGFz8mDzUMeaKXvjRRdJ2dg==", "signatures": [{"sig": "MEYCIQC7TJjtDpAzLXCczGPr5+1GXMKNyhwXaAZ3oTFVmCTCkwIhAItwOSL9Tz7IX3wXnX6t/7US6bAZcKbluEiVYfPqI1Ay", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ+CRA9TVsSAnZWagAA6qcP/3zgtiE/817nN7I8yK6e\nc21XI1uo+5gUedmO9cLM+DJkuy30vfzt97zUdhLQDD3QkMHQeGwMPcZnQDkh\nZlCeeFWq70gYfUwWxesXddLkMXXPA5ndQLIk5/pzibszPRxqu9mifmN75avL\n2jbSRiY0RbqwffkOUzaeaPVphPygi9+4crEOeN8f6pGqFx8rsXjalm8Ff5Fe\nkwsrkfrYDjvXRgHTpdiie4w70MGTiR73j4RUOwRl0crY3NvKSYwwwDsjGG7v\n8WAAmcsiQiGAa+h9bSRxJ5bsXLxHoRPXPmTvfbGGzhnuVrVe511IiLwpPbZF\ni0Cu9VndwskyWZ3/aFXFGlPMK0eyvui9LPR/2XAJKpFjbfWrt2BB3ni6bKPO\nj6DaNmATbY+UuXhCPOTIPjNDzZHU6DOCL4GutxkE5DQKJgE4cjEGOHzXoDSP\n8gwepXI97pFxl4gCLKRbzclX3yIpv9BvVunHrcjDh0wKvWONAlezK9JY+Gry\nucjRGFgCcI9XFDHBeq5qE92MpQ4tfrTD4rQiV1pyE3kGZJVUA+YVO9tbp1sR\n06qpyA3H9l+J6vaM8loSItMrieuqEhXO6p3rFW5nA0DT9wkyePRHNkjLcdGX\nAzOfRk8AcR82ltm9f0FksMOunjkQKUwlskM2mHFOdkrynZ5GfdcV/YmzMuf1\nzH8E\r\n=W3d2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_26.0.0_1588614781423_0.7492404900089518", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "diff-sequences", "version": "26.3.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@26.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "62a59b1b29ab7fd27cef2a33ae52abe73042d0a2", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.3.0.tgz", "fileCount": 7, "integrity": "sha512-5j5vdRcw3CNctePNYN0Wy2e/JbWT6cAYnXv5OuqPhDpyCGc0uLu2TK0zOCJWNB9kOIfYMSpIulRaDgIi4HJ6Ig==", "signatures": [{"sig": "MEUCIQDG4mzEiSIyNmMDoA2kk29xPEiItUqNgQiP0OUVKT36RgIgNVeQ2qbL+PnlKKqIdjnKV/v99tUHJTgzDAYHp3rNGTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAeCRA9TVsSAnZWagAAVh0QAJcPcgYbqEA8ELyqflHl\npM3fHpiuru4sWRaN6I+CXXVKsUlOsU7hWh3XqnPo5UYq4lyrsDG+e6zpqeOG\nUra8Wiu34kRx4JxauBsspNgi4oP0VqXNehnA0lTjgcNT6qS/Pu+5GowR7BUO\nR1ZGjHD0HC07MTUU5TbU0ZUDGfsiZ3jYJjsPdm5AbS9NTgbBsU99zwr27rHO\ng3GY8mwqLu/VQHuQzMyYyo59jah82dngriZQzuXBvwsclKqaCuZbtlOp+M7M\nU5Kkz6oigfXx+OGSbu36Llsd0X/QSdw8u0LF+F0Of6Qu+sxCxes3UEhevN0Q\nkGRIS0lcbfuwnA74WWm3aBWQJEzkuT+TYgrjVYsUL5mdi5CMTcC8FmGBulgs\nUREDye5Dg1dcNqp/3NR0BaSXFTmJcoUAHh0YXI4H0IYprUfgdKj/tA5EduNH\ntqe0dCFRTtrkbcUBIV76qlaI0t//o/fxsusrxy419yzDJJ2cJXrcAjTInQuh\n6xp6lZnn6RTAshg499kUIyUufkbrnQZZ3k3eOLuOY4kKQRC1cK6gJYFY5EjZ\nmsADfe+KbKME0AKypHrEydsBZwXCyiFwS1JJZ+mgHp0QN/HLqHtmQH/7BOcN\n5voMdgzF9yUiGI/ARapNsParzmt6XPsdFwG4C+YSV7WhsPZcMOlMORznMZXv\nD47f\r\n=f32z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.18.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_26.3.0_1597059101667_0.3306323827723736", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "diff-sequences", "version": "26.5.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ef766cf09d43ed40406611f11c6d8d9dd8b2fefd", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.5.0.tgz", "fileCount": 7, "integrity": "sha512-ZXx86srb/iYy6jG71k++wBN9P9J05UNQ5hQHQd9MtMPvcqXPx/vKU69jfHV637D00Q2gSgPk2D+jSx3l1lDW/Q==", "signatures": [{"sig": "MEYCIQDbj0lCWJMhjAu+33DfUyV4y5UmrIdkJ7KUBLBe80wDgwIhAMkWbBSxuyeV6mVS0wD8Z3/2W5FBj5GjS/AKQPqQJmDR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucmCRA9TVsSAnZWagAAPI0P/3hY6pFUwmj5079yVVEq\nxIAffzyMxuHr5KOEyq+qGCuAY7fN/N5pMsQlHkvERHQQayeCLae/zqtwY5G5\ngY/mBWaG5t0lF/vtpxvjLI2xSNHxE1EMlQTV4MGZSDP7onxUD0e6sgMSFqY+\nq5xszhJfUiDduUrhmtaMG0XkGkEeP0NxJrQ6LhzfjrEp33sdlIPJl2PJSka9\n+mK+I/0SWTP0/JICES65n/s6c0I3at1XsHh9Yzc+mlBC/gmxjhbp6SjWuhPv\nAseyqTF9tuO4EyKPRS4r2M6C8cISNIWWTwYyg0FBG8VKldFjQuLJjPqoAfaq\ngEpooU6o+PR+GrSnTiRwbN3OAEC99y42vudqw33H+Rj6aJGW/8J572osD8e7\n0jLTTydIckmv3ULDV+eUqYHjBoYj/k5JGmHtXid0RFKhAOYnX0A3snfRxTGM\neebP0Rvctwsz+JYBuxctv4QSjLpc9EBLhPWfpYIHGjOzNikDCPmR9o5ayGLg\nV7SWEZyUus2QIGe1Tw3V88/zRh5Qe0PWbUexSKikCL7x8CMnuaVSDFNkIVEO\nZuSBU9tk3U8mgePZsgy9BlGYIRbkQyztttQ2h1FMHRmuu6H4D3GCX0NO0jId\niJkhjZYOI74BjoaCqdAyR81Kb9JaAsVuszM3RWbl25+R5bl76m7zIlSTmjz/\nrP3T\r\n=kjkm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "12.18.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_26.5.0_1601890085902_0.17535582257312332", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "diff-sequences", "version": "26.6.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48ba99157de1923412eed41db6b6d4aa9ca7c0b1", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-26.6.2.tgz", "fileCount": 7, "integrity": "sha512-Mv/TDa3nZ9sbc5soK+OoA74BsS3mL37yixCvUAQkiuA4Wz6YtwP/K47n2rv2ovzHZvoiQeA5FTQOschKkEwB0Q==", "signatures": [{"sig": "MEYCIQDVMYK0IT6SrQ44q67DzmBdh6KX2TBh9OLrxr3qzLRPlQIhAJSX2Xi3EFs0RGnXVeEc0SwJo9zmLoTzzMN7XtGeJkUF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADBCRA9TVsSAnZWagAAPe4P+wTW/D3RWiAdpLt5u941\n26PyQVg2Te8FIeea/Vfv+imE/kMOObDgkEod+Lp3IKugMwgRAgydxNyjtFZs\n6Cg3EnyH96UeL3AoWtI8m1zCIQBSctgzxW0Rc3lIVuRpCSDETF0Oj0uUt6EF\nmU3QDGTEYTu8s46ApJeTFtUCeAQU3s3dI3egJaFWE6UpLCmVWlAK4NjW3mAU\n0ouCniHjd0lgsmWEKrjj4ReAWJ5OMNbzC5j0SyE1SSx0qARBwr0jtgx7piul\n0sh9kKBIl2rcBTBquresojOk0XcERoxSF4ng0lYDAWmQAYVSYH1H+ryqu9a0\n8O4nH9IhdJEs7NQUKb4hcWaTpJUG/d0H1sbDIgiw3VxkEYNYLJvzu/nxo5Ca\nzZTEmkDxH1oxzhOk4zYvfUuy4segm3dz20+NEQABxV8GUz9/yAaKUbDP5bd+\niHk3KFQsTeLIOKTJeVYSBEAh6joAN+7h9mAeDdZlpLUwjJW9//pkW+aFj6wv\niekZiiaPLpd5PwrYhemV1TweTj3HUfD6JFezv3ca6kNSmMeU6k0VkUXuH6Wp\nEjSNYwjRXSmDLvpX7o1BlY0LUZGTwCOB0QEDHrzFAVWWPWXuR82B+fyC6La0\n3sextHuCw5gXOYBfEAmfcPBPFqFgxO1MKhBe84iYyb+OFcPPov2Xfj+CxEU9\nMo6J\r\n=znz5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "14.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^4.0.1", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_26.6.2_1604321473425_0.9153700936172822", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "diff-sequences", "version": "27.0.0-next.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f0502557fd02bd7596f9c42ce67bf7d022559e8", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.0.0-next.0.tgz", "fileCount": 7, "integrity": "sha512-57AobYOk6dK5sIDqkULZyHSbxA6JLMhBEZWuiVuwuNJafFOkgz4tez9DaKylgOPXX+/5YCI1GZSp+8+ctthy+w==", "signatures": [{"sig": "MEUCICJWn0z59dhPcJcvtblfFCDvgKG+C6/1M2Zd38BNMYGYAiEAlMPhcW3OeaS4qw9j8voHKAB/UL/L1CJGGpAKueJ/hb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8J0CRA9TVsSAnZWagAARz0QAJ3hFy8SqH+sNjz+BCMG\nK35I7DzUv7AUbVVt08XXWakuuvoysu+xZ0wZ8SNdoQkxmWE0TvwaAQoaSXwe\nGbp3rxuNYtRunc89VrwL1zm8/5QbCB5yY4M2MwwpiQszf020/rOsEDS27U62\nPeJMBVNvy1t6j/mH0kdntOuevCp9VY1aVNQrgPSMLMIQjxf3NudaOISRm1tg\nzngVxVo6gdxmLT6L5Zk14CxfwJTgp98eOkT1hUZaI1K33DL9lXdCdJvT6g+6\n1toAwy7maz6z9MX1hDbe+ixs9vfbBZoRMWhYDM2SD0whU+r+ORIpfpMYKdI/\nWpMkFQ3gaFPXcOHhz902tjuDKD3Cq1EjXfpLTqI8oVBCqaekoqJZvSb2zoyo\nCsMFTVYPlrPwz+mtwIZD3JHpuknaZYi1zpoEZVNF0OFYGQyodBq50XuBjWfV\nuTV4pTbCy1pg5idp6oV838z69x6eAIgSfOx1APTFnD/PDVlLZPc4kKtZ79uv\nuicutFuywUXCUwN27TDNyi3GrcnIZaKTjiM0RAw4JqDxJspabZUm790Yhe2D\nQlRwoNzKcnxuqSCUKGQ+b73R6d4fhj/6QVprydRI9NpTsxiLEZXokA4Jckl6\nQBaOXBvS0VzEkhaObUzbPJrhlt5so4OwBwDOU9bFRJHLwt8AKxqZqko2SuOs\njLc+\r\n=V4xU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "14.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.0.0-next.0_1607189107821_0.17805614845269968", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "diff-sequences", "version": "27.0.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9c9801d52ed5f576ff0a20e3022a13ee6e297e7c", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.0.1.tgz", "fileCount": 7, "integrity": "sha512-XPLijkfJUh/PIBnfkcSHgvD6tlYixmcMAn3osTk6jt+H0v/mgURto1XUiD9DKuGX5NDoVS6dSlA23gd9FUaCFg==", "signatures": [{"sig": "MEUCIQCDP1Qb8AWt2RWpu2RUsNbBnFCBlLe4HDvfnPqycDeYPAIgcKVJ0iRztYfgrAlh4ISYjgXevji0qTl51rEMd1YtXaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAAuAcQAJKyAo/ylq/UCgNsQrC3\nImrlMy8bHW5J5oyaw4rME0bqjB5WaYEXQ8sstPPHRiYYugpkP5isjkO2jp8F\n//w6WX68PmYMvQF1qGltH6uMdo7eSv6t00O5J7jkePpcEB8js4eOecE5cO3W\njWNdWPh8IHlZATW/sQ0bRQPJ5BaGlpZtbWTP7hTzWwCJUthRncJjW/KUL4if\nANcCP4VlRImpFVDffLj7+9QBPAIfubJ8zO9vKswRiLvMXBiBZrWFcNiEQaZt\nAq1EClT01k5piqbHTaQ69ei3rof5FaWD1eoAoj0vJ7qpkSbTlcP5tgwrFMeE\nVOSbCE2lBs1pyZGTFvTgSxAjN93YDK5D5uUxwzcdnCJShCSrjBSYYDoTXEfP\nmrHZ0Xx/TKZKXfvpvYcX+Xh7uSC2PgFS9AmTKdOP9u8YhIyHh6DLiQJVzISu\nX50I83813sUHd8gjp4pSN9HWhB9K6t0153+6ZBnqmqk1QCgEdfBcCkwtihCF\nUFBMPbVoNkS0ynm2NSmHiQPPaapdRPYE8W0SesBaectonkUQlw/116teZw7l\nroRKY/52+6Cj+E4OXWZlwl+0iW1T/+JUnZc6wevimZNUpKJLZb5PBgRCkl7c\nCw1Dm3LYpXN5/+iNQ2wmCBCQ4U/SEhMUY40d5oHrPcs4/1SfR9qcVlapmoDF\nd2uZ\r\n=cTgb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "14.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.0.1_1621937184053_0.7682171173613457", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "diff-sequences", "version": "27.0.6", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3305cb2e55a033924054695cc66019fd7f8e5723", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.0.6.tgz", "fileCount": 7, "integrity": "sha512-ag6wfpBFyNXZ0p8pcuIDS//D8H062ZQJ3fzYxjpmeKjnz8W4pekL3AI8VohmyZmsWW2PWaHgjsmqR6L13101VQ==", "signatures": [{"sig": "MEYCIQClN8ypme3mnGocC9b6BKAyJqp1ik2BipeB8m+Vk3YWSAIhAN+7vX1lSYECD2Zh9zwjKyj3HgGM7aMVjrf+vKw0tl6o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAA8S0QAIMrFQwrPrCr1Y12clq3\nOQkhq+Ejk5+mWB9OzXGzbVJwYItydxMRu25qCnMXVpn8Y0QBWGF7fnOWhM04\ntFMaFa07/1YsmzNVtD+d5hIu627D5PniKhrmTHutGDuflBmGK+L7rDgZKivC\nE/iA+PDDJldb8F7yLkiRVVaBRKv4evTHpOjhSdOB0AyGkcldxymzmYRfcFop\nmaN1wuQ79t8tX/KGQpa0ItZMX4+Ke6RJvb18IdMLjv7kx53sz+PQt2UDN1zg\n2ODKWNwlKCgbRohgcBLAkeITCRlsmqFUHw5eGjXjqnZQh6RKN4bXwzeVEbgo\n5zfK9lEqWznnw4Z3ld0uagpNUvvGl+rkpiT67+P4tLWHCNXNI8Zf3U08dFOz\nVbdbuBU+9cXaYrzfgzIfalNo8EqpQbo4rKGFryY3kgvmAbwExwqXgG0GZHeh\n0bJPSQKKSx2KJdesA8dXejJMlyqxp0Omorp2lMWiNEN+mikWwOZuG9DLGqWP\nM2SMLfSSyHM8PWa7WpB9bCDglAIFNTfmvm3F6jJhm9V6hh6RoC0j62JmB6t4\nq2YiszZKho4YTydNGrNZqFwor+B6vZ6yHFB9XZTeXXltCIOVHKh3UOs0ehAy\nviQ9TOpN17seeHD8trVsJVfoBjB07dkqaqLwkGUkPXF1N5AWymKDFxJ+aMWo\ndFfe\r\n=myQ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "14.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.0.6_1624899930869_0.696743839206365", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "diff-sequences", "version": "27.4.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d783920ad8d06ec718a060d00196dfef25b132a5", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.4.0.tgz", "fileCount": 7, "integrity": "sha512-YqiQzkrsmHMH5uuh8OdQFU9/ZpADnwzml8z0O5HvRNda+5UZsaX/xN+AAxfR2hWq1Y7HZnAzO9J5lJXOuDz2Ww==", "signatures": [{"sig": "MEUCIQCJakbLpEeGE7wPcYlBe0fSr2e+gvWOXSQd1L8ytJdvEAIga5xpmZYhljzA7SSHciDlJ/S4oSWiR4xt9jGRvz3+wfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAAnAIP/jA6RV8tWxQeC/CjPb2X\nySukEDkYuilM8LF+2lCQgC93B4zAf0LNSVm4KcOLziaLACu65KYWfB3IsxjV\nNOGRmqT2BrxvT3z3K5V6S28Evijj6BJTTYoRpBfHoxxPOJ85p++b3pf6Azhf\n52S8II17/0cRhDbWqwym71+5X12+WbpQt5rAigMaYkzJCe8gwMKIkIxeXoui\nq8Gd/K1gqthKK5/UQIzVXX7ecTVkB47jCeKPZaZfkGFR060fK8RSZQQzMx+4\nhIarv/Hc/tL+FuxTTSor3llbvqFJtRFVw/9vnutz3ybRjo+7uzWZSlP0L0Qt\ngdhtCOn6/1Egq7nz+oJpiufm9ypUC+8FR7XpkkSKCqacTRKH7eGExKomo5I4\nDHNX+AYfJ4p0Dfoe1mazf+8XKl519VprgZJ6X6yybkwEl7yOwNurvSaMu31Q\nJG/nwk+kxhot56hJlKovzFA5E9oHvCTQ7wmjwMDfXbZVh8gpxKtWjl4Hds9p\n+ToeJyXDK4ndNbGrLhoMwY2NiIHv0ldhV4QokPfoSUsHeAr9YbUbJNQWtuOl\nzTyWwGHj21nCT4NOObqtokqc0zyOUkkgWAj/h7Zy98rXFjpdWePyPiRFW9xT\nr/YpJq9SRAw6PVOIh0h8yxWVteW3nmdk7c8UHe0KLHWbZqk/kDF2aRQNzDPf\na9BM\r\n=QVtr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.13.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.4.0_1638193014711_0.09706689969055016", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "diff-sequences", "version": "27.5.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a8ac0cb742b17d6f30a6c43e233893a2402c0729", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.5.0.tgz", "fileCount": 7, "integrity": "sha512-ZsOBWnhXiH+Zn0DcBNX/tiQsqrREHs/6oQsEVy2VJJjrTblykPima11pyHMSA/7PGmD+fwclTnKVKL/qtNREDQ==", "signatures": [{"sig": "MEUCIQCPIAsLvcfbWzDDnPJ1FuUi6R8q24Ui3hcswd5HG5cBQgIgH4flUYk/LfKQATgfhBDbFth8cpQwFu7fe4OmYRqHr2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp2CRA9TVsSAnZWagAAzs0P/jLddD75xLSLyygz9iJ4\nccqpXRZSamEtbstMNiICVaRmtFRLYZNFU1MWu+0lFFSWXNPOwlqFaAGF/Yj6\njpG9jbBcBIkzVbvOBylwvlme2iwrnbPCL5RsD3RAV1s7oDQRWO5qZlAQyylL\n1JUi5TbnxtVD6ydGT+KKaHfzC4tTc3QH9WJJV+8LztrK9f614eb57qdQazjs\nq5A3D4AbLBhS55fJ13YAP/BLX0goGMX3Lif7j92zX358Fkm2VXKvW4D4wcnk\nOLiVCdAQu1rCWr9+cg5XEoqpiKS/Vu1a1QODYC2ZPObqZ6U/pMY4nHPoZHDS\nwSZ/DXG/Kq+p85HFaWDLAnie+vY3HJwScIL57D8jpZmQEN2YtlOLJg/XT58N\ns8PphLbmdLCacb8/zz3VLZPvYUvPKZByoXNNgjQnvMGR95QgMSdvIwURFxih\nMLjSFUHlwTjgxczKp61xkiHiMCv7soXzHttfvxGHaV4IexrYNSoxz+sdkVHr\nz4smZuCI8cNiT93QsU1EvvjjqCINLsKjsU+DnZZAs0n8bbxBtMl+qk7RnpLW\nVKK67/Qrvu2Cqf1zxPDOWdOZgRj7nXnadF3dCivF9/tNpZR1ZJdVRr7xglg7\nWl2WZndubaweSpOf/kvxDzUqNyn8WG6zhiX7AyGhTh+Vs/8HQzGOQ5G2WeJs\na1Po\r\n=+Fq9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.5.0_1644055157827_0.465702729865906", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "diff-sequences", "version": "27.5.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eaecc0d327fd68c8d9672a1e64ab8dccb2ef5327", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.5.1.tgz", "fileCount": 7, "integrity": "sha512-k1gCAXAsNgLwEL+Y8Wvl+M6oEFj5bgazfZULpS5CneoPPXRaCCW7dm+q21Ky2VEE5X+VeRDBVg1Pcvvsr4TtNQ==", "signatures": [{"sig": "MEYCIQCS/j2nXAmEhBUjDirhkHhF+pMERAmvGSTgxt2RrWIm9wIhAPt/rb6R4dpaPlYXWxKpRU54uk5oRpoKL6GoQGIIZHRJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAA73cQAJcYoaDA+POBYFSASvYZ\nkLMqbZWo7RY6eGkYHTz79WnSgqIRnK/V+FHeljCghkQil479ah46UOWJeHwl\niq8o+Pliiu+xbAoyM7mT60QGbDsOBxzS+qEN7oWlgO6TPF4WZbaqXMgb9dX3\nAGeU9qp89EVioo3QjRkPw+WyCMuJmwLHJ6tEzNRqsTJBca2Xy2Q2JLFr9m1t\n6R1+PgoEPkUgY1+xi8mXrfxk3HFcscAQ2q6OzUdeGXqQMJGHmf5nGm4KbBNg\n26CZgcKGTSTWW5JHLwh965OEEHu1vKN3YdaonTMPN5kjkCGLSuXn5XOhmWA/\ndIYSTnfe54hEtFsvYlq6FCA0sblGMn9N4IokXmwpIaYbyz0XsCKbvpa1HJBE\n83cF9t7lVRQUBG+z/NCTiauk/DtyhGBsoizPZI5uWfg6sk04Sm7bEm5Mw0xD\nbnofMABkNqS1hIV0mvjLPMV6RJ1Ar4yLjjmVXqRW+if078KBRS6fc0JE/CuR\n0O3tAIgCwJh0PharZcRnvPmrp45ww8yS/QjDQ9dgYPmPRcCF8IYwOwWWmGGp\nteOGNHdExT8K3c4Wxw94QqxShFk+avoM68c/rwAepKFf+Gw2vs4BqVaMdnnn\nud0iF4USWzr+vVwNB+e9d4X89kio8BXq6IPdK6kQVQZtHVy3YWNRlWG/4p61\nCUKW\r\n=U3Kb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.13.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_27.5.1_1644317532688_0.8602030712062132", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "diff-sequences", "version": "28.0.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a85967b8a6ef6404e8650bf3c1ebdb255206ebeb", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-REr6iLEx0G1inAVYVL1haT/FixI71g7pmnJzhg4gO/t2MaGC7SH68RNa2zldVD8ssedXBtQNoM4oqUSeKCiWiw==", "signatures": [{"sig": "MEYCIQCF2E79uZUCCq8llqmJc0ePZ9UJdMBWcKKid1cXOuHU2gIhAMsBMD37K7PPJnJS+1JlE2jJTvX217A9sCI6QSmM0MJn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAofIP/2WvL/PBStPsxkwD/hH5\nhicncgZfTg79x6vWGfa7ndUYVBAME4iBXQH9GxvDDbj6AyBXfUP/TAes3cIO\nbOtFVe9Nu0QNQXhgnRCmHq6r2AQLekUcz/6PHmyUX3g8XONfWrlwCMGJfpE1\n3XfPNnKY/ieItRg1NcDLILtRjKwoDQyIVxbCT/3TlX1XWlNnZaSOX/dn/9CP\n+GmcWsIN1dbo4FU62OEZU90IXZvMz/uAiJEnIjQ8m4FWrlkytgYfFeEtcxTo\n2LQigvwYaUsyhQD+l3qwYPqi+6Ztzjsb/HieJTODHNYp0ah/CJzsRgat2n3k\nuQw/iNc7YkfleBNT0l9HThNq1lkX5dl0OGeXt+P8+1V42tzSbbb+p5gBC00U\nB/w+gPHSjS16EhGwOchVUjS675CtQ0Ivkpv21rQyi04RYGUlaSWFkiFKqM3N\nYx9wdlwKmISLACxa/85XiGzr408xv7ZceufvRU7n5sW40pANkPP/b2FWBSKy\n8WBOBBpnI/wymOKQ8R7m28m8IQrMsRhqhSEoSWguTP9EO4i8vFBBBLqI+0va\nUeDSZhGjHfQhTZZ3Lshwh+HLOEAd+Xve2naPQ2qMqkGbC3T1ybr29SgdwNsd\n1KM3p4er0EjiNBXdkAvMH2i1X8duVKA2cIKu5pLkK+LXWDMjc+wBwIbCErze\nY2NU\r\n=xhfV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0-alpha.0_1644517046427_0.2647410282139955", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "diff-sequences", "version": "28.0.0-alpha.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d6cea7495a69137a0d21fa0bcf9a9e4aba6ab354", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-PZKPL0e4KOwECemRHqy7JAs15JBVUOfA1+Vfz6A2AGC+i7X3cOwyTc5sTwbM+Nx+OIOIA7/zsWJu4F4UE0o3lg==", "signatures": [{"sig": "MEUCIQCgeCxIakfbubnBKizN5PXrV7wSGMxxk2zgfAY7L8oSkQIgcT+7waTkHDJWj+O8sSvZjHlt8tQKDw19dUAQn+oTS2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4RQ/+JS4cDbHbFmJomm8ICMB0pwgcsnqToNv1+zFu5l73J4grVuUQ\r\ntjLmQn6tu0DelzSyKwpVD3N81A615o8zjSxAyDWls7PkKNM0AIHMQOkLkNug\r\nbGElD5OENxQeGWKpzeHQ7PqBYOTrMD5wT8lbGAAZ+MTunmv0HcViK4jjaOUK\r\n8ElLfh3gQTRd6pu9uF5am73BftquNifPHAAkCN8kVaMn7YsqDKlvy9/w4Qaw\r\no9vFFGlYw9EeYCZOVv6JBD/bZ1X+SqxTDzvX1OhNAWoafpy0u3A08oWfVG1+\r\nyRtmc/Rp4cAAbJJVrsrnIXQgoLK0cIb2c3+EqR5UeN8K8SkqvY13PAKTILLR\r\nsoxSUWLhWZlNIybRsF9a+B3oe8kEr5eoqyT/6hyvIAdy3ilKu25IMiidYJ2x\r\nV5fzQGPyxYBUHh/WlW5fzf8H2vcljn3/8g9rYirSs7+kpiEy7I/ufsLN/z0R\r\nQc+n9FTH4LDSp2fIYW0r/4t3/8PhfRNpNTXP+IvOZ2C0ee311FVMHYKr1+4n\r\nnps2Ws4rtrukRv28wk2ADTu/+On6d+0dVVMXhlvCduBAP6NH4NYiLw42ey48\r\nJuCEtfbieN/9Esy0xYE/1ImEwvDxJxOsycjNZ5TTLagRf39hGcuiSzyuF+nQ\r\nugU1+57locZAMe0VhdYFcb9hUg4D4euu7Uk=\r\n=Nn1a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0-alpha.3_1645112540718_0.6913026721557378", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "diff-sequences", "version": "28.0.0-alpha.4", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d652a519ee0720abe2579d1569b8f07b3e2e0641", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-DK5WJN3LP3XoMGZZqaphPi6mHVuwUHzYWSkzWnVz3OskgvkngB891MbEo4KlNn0TDIpR/ITjM9Gm1df1iXhMIw==", "signatures": [{"sig": "MEUCIQCTXOUxW4WXzX919sy9gp1SnNwgrXjmaTCkk8cust9MzQIgNVYSnlnKHdrtdaP5DpiLMz9TCHUkPeDkGWBTuJQvqik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreNRAAgVJsvwWGlJ48mrQu8U9n7xi05BqkEmCIojAkrqoLU1/7ZtAd\r\nKl9eb0OcEE/FejKFqn59aap6e/KJii4yHeEbp6greBJ/doKtNuI17iXYHH0Y\r\njS/LWrwlRYjeMMCv4f4p6GgA+fG1ySa7ypcVgE4Zdg82SQhxVAEC1G3VkrTL\r\nmWUimgXu2YzapaMvyJDPpWgLGdyuRIx75Yl8bF8CYTyagqgmILTevHwW3QA4\r\nVWUe2ieNw83gfwT29cr46shYrR0HqyuTHoFgonNn1XxoZhWpyO5lYWaQ6lL/\r\ncfRLW2Gz30jYPWcCyM4dor86bMcz/uz91neWq3FKNZK6pHOaxMNirRPFfj55\r\nzt4c/Nel37eHy1XN8ofhFyriwTl0dsmE7Q9kYSDeYjpMLXTzBMZBPykUsRQU\r\nOC0qA7ano1LB6G+MW8iDoHiGmaanMcdxuF0W6/lYj8UnwrdajTk4YpHTPJP9\r\nTlKPflGSGMcY4pLC/LEVIOpL6Ob5r5rp6DvuR+cJTBdu7K9Fh1wL0CaJppi2\r\nSrNoEoUyYc9vuyyhYsI0LE0WvotAehz95N/3v38JmpakrA+GOjTHvadt0sZU\r\nZbns8kbgeXgHsdgvmZcV2BBoH0jZqDH+34HA1DSfUnTW3k4dRtaTXfvXDw4Z\r\nLtZMKqRA0zJiKqXeq+qGTahPeIlwmEx3lQE=\r\n=qjXN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0-alpha.4_1645532034420_0.06373520305908609", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "diff-sequences", "version": "28.0.0-alpha.6", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0083a9cc62e72d489475508cda49b8935c900392", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0-alpha.6.tgz", "fileCount": 7, "integrity": "sha512-ahVUZf+at8+da+HVdM0zUkmHxESh0PHCe5KKO9q5rib21v/DrzBpy5/33RiV79tLBP+dw2q5WpceIZYq3ipanw==", "signatures": [{"sig": "MEUCIQDB0AI1pnaMFujPG3ob1eN3ANthbDfQ2naja439X+68gQIgGl/3Pj/s+06g4tX0lMfeLyBGIZHllGKnM1sGnsv8z00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBgQ//eoBtIiHy/i3pJ5W873S5kcJyN2Xwu6mtnEGT7qeUFz8NOPJY\r\n8+Oq7zyKGLIX49+doGcd6akDJJNkJfSAkzDYDPT3ZvpzK6GkCnqufMQosdDZ\r\nKERVeYYaU5sMfEDMELZ5ABrLSXnk5NIo+Iar9yjpPHkg/whn3ujJQ9+t417v\r\n9VpsClsfP8LcgPkeB0az2CXk+tujG1fv6XG1r1ux0kTNwiS0mbPFvqRSL2ON\r\nb+QA7EitGwDTQUVpfCjgcpsrOzA1WxyfaPKqkf8t64jwi1xUR/+DVsLF/nnd\r\nY1JTEEtc7BFvcid5PJGAF4pMdDldT/4ghan5Ub1nclfcuU3PXWMbPPPh41wg\r\nzkZGLuOng6XKjdT4KdxP28UP3ZiZJFH5Kk8eVSEGHkz8bQTXV2yGqC+CHhIl\r\nnlQAZO/eOM7PLfYUntb19BibrUDurgYhBAndZTjuf/lGFVO7JnfaEb4OuUkl\r\nOx7nUiX/JF2rgS9NXIvK55aVzliLlcPu3IE37e5pwQnmun4sikpFLQ3ehBWx\r\n9HLyUz7EPnQX93jJIgk0JL9PNm/XdbnM84jizP/kaCMpKcTByiXFhIwulara\r\nJFSlq3PiMzJlNiEOkiKfHXIPbJsorb9gHvXlc1j8rm7JZ+8PYscvJNQPdLad\r\nBldHOk2MfGJPrwqCi6DE/AIj/B6kvKPRB34=\r\n=LJ5e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "scripts": {"perf": "node --expose-gc perf/index.js"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0-alpha.6_1646123542400_0.27084416232972774", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "diff-sequences", "version": "28.0.0-alpha.9", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "329012d40d786479331ba0166f061caa2c6755ff", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0-alpha.9.tgz", "fileCount": 5, "integrity": "sha512-mz2hYDMYsxxTo9c1CHtuGrXI9N9Brz3mLskJcj2DnUmhGLCaO0x754OnJ8fL1mn6t+XzC2aNY9YltMDP2l4CRg==", "signatures": [{"sig": "MEQCIBcUSmg0n5DNYE5R9W8GZxf29diysgpJIFlFnxiZsfyvAiBvxvjVuTLWtxJsI40mQ5vpo9IH5QYBqSRQmA5hX/YMPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0QA//VEIHGG08MzqqQWGmWwTo4p+1DGkJcVYVZtX/tvK9FwgjMSun\r\nMcIo1krvbKEe/0bIbnka6mNA4X/HuSBzpd3tzZDprvryTkRXnELLrsFqNcZj\r\n0Fe8JwwAMIsvTASisNCYxxfTkdgurmFz/7GpBAHmUhbAgCZssOc3ugX1ZIxR\r\nkqnSG3g4Or7zfc+HHqyvArnmFfWVQAOEg+29BOZ2XVo3bzyUf4b9DgNctuvI\r\nLjXf4ccuZSEXpujGN/gI39pE0dVUQDtO2ZUKsKD+V6ioVEE9u54SxoILECHc\r\nmfRlIeFfyptoX6uzWC4ZJ0A0VaTdHsQ6L/jA3C9c+5yV9+jyoD/bmfkHtkgy\r\nfA5CaW6pjiV6QcM+byNX1dCLsOZtqTm4i1kkhLJZwbkT1KWsBO3EyHC6mQc4\r\nKjUccndqiZGnlbWC1CZXdMTgT4s9Do4H1Azt5YZf2G4gtr3CsQP9+JATrcRm\r\nlZyRhxEnzixxCVx7WdcL1EmIgVfbjjihvOiSPUlJ0bMXSmG+xzpaVlFpH7mh\r\nPhfI3UA5JbRHoaL1elbJdQYIOwcnJuHfWBnY/RLc3htRgU075Z/moMSQBW2O\r\nOoQZXM3HWxv5jaW/JY1yiHhZrsBLI2rQ0HjM7NNq3pQnS7DUSYVvnK8R8XqT\r\nQShhgA7SLS05TFliWYlpbsVsrdgtayVCSZg=\r\n=cTHL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0-alpha.9_1650365953773_0.6725265028221681", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "diff-sequences", "version": "28.0.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06d253f0005463bf203d96fdbd6296c5f1d1888c", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.0.tgz", "fileCount": 5, "integrity": "sha512-GTIQPn2pPa1DMoEH70P9yQgYLcGW8bjPR5EOL2JO9/7DQHX+9tTFJee3UmlGWuyUvIqMgpXXssrckLubiEUZTg==", "signatures": [{"sig": "MEQCIHLn8jf7tcNG2b0g0t2w0jPNsDvQ8BHycrZa/25tvw2mAiByj19WoQl9a8tNXwJjj8Yqb92mAnozs7j6FYZh50t2ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6ww/7BZaYgOe/RR2QfplF1eRa3Agkik3n/BhdiK9RpiGN5smua4zQ\r\ntAvkkNrtN3J91y5qA2vrKpQ1U7PEit/eJCVAR1QwhewXrEKGIYXLhXspqx/7\r\nE5tzOHiQyQFYIGy5+/o60bqMrOPYoRiw6O3KfmO9Z240zTx6wUC7UevwoRsN\r\nVszOOuYW1+vxDTnkFd1Tft+ZoEHcl2O49gtTFP2owxuk0LLStj98b5QM53PF\r\nSui9NSX/e+fOh4Sy9EPNtCFps0CsLijg2jm7vvhX1PNLl8ZS3JTqzr+t4QFB\r\n73Q20G+x2D668w/0gHSkS9ZW9xs8EwPzUHfLlvJae+o/U9TBamyg/VSaMKYb\r\neEs9uMugmZNRMp1/1UHlX4LB/v1aUojMw19Y0nj/QdLV2OxIkKz/FzDw1/CL\r\ngrJpBVGwmI/zShxMw69rvxC79z/VoftLeUiBN58A0JghUwliNVpwFWAnoOPR\r\nD02xCsXY2s4/kfaZBU4Rr4tFMWWhi2txpXKyxGz1jRFyU8iYf14zejxQZdg6\r\n703MvxI6GEWz6nruTEDSPWehv72Bvu8KFEU6u0CHCZ+0S5CPf++aQ/lK7hRK\r\n73vYFSLH6ApFuVi22t0SLqTv9lX18LyATjJow+ha4jFZAzgMxmZqTb9wzNB7\r\npLJEQAYkaYq0sIUc2xu5e9IOKcFiyLhcw0Y=\r\n=uwyg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.14.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.0_1650888482261_0.8115378916877636", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "diff-sequences", "version": "28.0.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "40f8d4ffa081acbd8902ba35c798458d0ff1af41", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.0.2.tgz", "fileCount": 5, "integrity": "sha512-YtEoNynLDFCRznv/XDalsKGSZDoj0U5kLnXvY0JSq3nBboRrZXjD81+eSiwi+nzcZDwedMmcowcxNwwgFW23mQ==", "signatures": [{"sig": "MEUCICctQyKfw3m/7H3Ktv7ZfViC+V6RWvcBDr//xnRPla8kAiEAtgvLajOEfgvmZGoM9x5qSkTNsVIRX9jHTc9CaMAxw7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo59g//Y4XbypuT1KSEDuHg9/zIh6VVl35kPMxOA8e7/zF5c1zukCvS\r\n17Rggh+liIw/4rG2yywTFkMEZoA0Xzj7O+mQ2gF5zA3GIFonkEbvdnF8hfKj\r\n7jId25yN4XQucVbQKn2fynNQU58gxBJ95evbCdAOxhvwkDnV39a26sAEn0jk\r\nolFr+KamSJETcnuGlEeqNndijzWsq/JK/yvFR8Ms25jMCWlwwqSd9P98ly2h\r\nxoM6WzBf7lETCaUsBzNvFi/8iUtCJJsD83ikY/t6arUZy71H5RvQgzpWmBwP\r\n/sTBCms5Pspix7O2t+rIRuwdUvJWUi8Dukf3MVnI66Um3Im8AC5A3pWpMpHR\r\ndP+RaH1XZUPt0BLPJk/VfJrrlciquhUHT13LrmXVsDsllcd2lkZiQ7gF6mO3\r\nFgQ9mruj78zSyKogY5iuGKH5rKzLNWiC9EQHAi0Ybyi4SoMcNhZU4E7uSrZD\r\nDiF140jQPorlU84DXVroUhFO61nHu8hIbw8QuNCOkY4Ofuj3sUGYHZllnioB\r\nVi567koiS6vccokOKkh+xpZpv3bH2l7AbV0Yv28NJTXXdMqfg8zTBmm6eu60\r\nrcdwHlaIRarUmmd/x1A0nK0OtezbRO+Oe2kueQhqHEW11DSu4qVoBVUG6rC0\r\nWaUeZLp9MjfZgA5FCHmXUXcxHTKvD4coOMA=\r\n=5wp8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.0.2_1651045440175_0.46656987751356693", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "diff-sequences", "version": "28.1.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9989dc731266dc2903457a70e996f3a041913ac6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.1.1.tgz", "fileCount": 5, "integrity": "sha512-FU0iFaH/E23a+a718l8Qa/19bF9p06kgE0KipMOMadwa3SjnaElKzPaUC0vnibs6/B/9ni97s61mcejk8W1fQw==", "signatures": [{"sig": "MEUCID4Vsf/MWEaWqhniLcTU6dssZUfxhx2XDj5U3G2IwsPdAiEAybsC3EOa8FXQow9p9cbKAPXkB2gvxSsAt/izqy0SOx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbixAAggPbReQu/DtW16KQg3dY9da/v0NRowgv4XTPdkKbUn/32wcP\r\n9rxOjgQGyxRuT8KcgpELPZhlImW4OGBa/nxf8CY3dl3Z/QCK8hHgA0OPkcAT\r\nYEo7bO57x6nZafgmClrGQe1LfMgqGFz689YLiomkCYr4YUpKzDSVU5AxkppK\r\nQNuQOj2+DrxPCG8rQKx6U533xToUwI0V1pO9mcaZmlCj3yetjpfOWLJef24y\r\n5ykj138lIBZ92B4hMAQGawGqPoE+STSvk6h2VfYhXKmiiKkYQ42JwA4fl/od\r\nSziFZ1MlMF756P2JKYQcWX7xVeyI1V1jf1oYkdJn+GvSLwzRaVpNUPW8w8yR\r\nXu65YAV+kvOeA6UX/BkfBA/C7Hn0f2wF1+kEmDTgF6QWH/VzqpL9BjCer1Qh\r\neM3ywaJf5fz4uVQzrt/lxkN4egNCpTpf63+VM5f/KZnQaq6B6YeW+Azi+r8N\r\n+ZXh+s29+PxOyrK5nIaeVsKDOgeiZ7MBmzJoiI7mQcbUX3v9EgrsMqUlKFPl\r\n0Xb+hpZ0RKkYAUEunS/LLK6C2ZKR+pEOhx04Uo+umJH1NBenX/0qYU+qPsTL\r\nDRzdWpnkbRv6rdJ64DQ3vqfQ3R5Uj94KLjItqyUpRFXcoae6rMuh+Q+v6vAg\r\n2KDwJHL8Cd0+gnMsa9ZCqwPEBWkFhshiE48=\r\n=nRzb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_28.1.1_1654582175367_0.998049636018808", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "diff-sequences", "version": "29.0.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63c9b0a4969e7597020a7df7231d09c52a26afd6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-Msg/4Xt5QZRbsEw+qNWnH8Fr/VaAGbvabAw44SqAorT50/8hvznoGUir85esed2Afn2FMiF+LdmbnwqzN7OEZQ==", "signatures": [{"sig": "MEUCIEAozaO8sLvioaxk8ZrmE8Ubv88LbzETZuN2V7NhCz59AiEAvbBo4CBcJj1A32y0rXUcyA005fCAFt5A9KkhlvMMzGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzgA//VJlrb2e7V4e9dZ+Sm1g3on8mp7ps9nSZm8yQNnBT9QsFhauv\r\nsXIgVb7JXr8dZY/OFre2ThVm55DbjIe6KxWVZpGTG0uSiuW5R8BC98hnM373\r\nrlKDSpUjPg7Fe7chu4FFaOokULUIQHHvr50SZ60dPLMC2s4ZazCPJcAV4ViN\r\nbb35478VwuS+VGCRPFVHM3a398eaRaAnr+2PnxMIkAKXoUBK9HnwvXZFjp8A\r\ntkdPLQwEXCaoh9DS9PcauMjO46EfQpuWgMt7vwGgZCUbCXY1qvWexerhiI59\r\nkDA5jfVGoIm6Dts3+Dd72UGoLAZeH43aodzesd9827xdz7LewyM396dzy3CZ\r\n3CuyEAs4+3S84VghF50Z2CLKxvAft3+gNw1BEopbWm1MlmyfBTsrDvtqwX1N\r\nzjwLdhM2tVin43giL4UkhU/5/f4u7pH/+x8LjLrwXFufs/BIIC1uJJCKpM76\r\na4yb664Ys/u9BypT8M7LJ/wrtpZli30C/s1Kr1QH6WZ1RpM9WD7zOXfzlTz7\r\npg36/7Ixhrgbz3uq8TcpDVUt/y3lzA5AIu1giKx5fA9pLOLtnXIvPUYZicvY\r\nrNv/jNh33NLYEYumJJoNA8/QrNQ5sQXBgJm1hmcuU/1jM6pEls6DTfFG565e\r\nw8NlcCN/DGCqOgAUBvcs4vP+sXJfVLoY5zA=\r\n=Dsfs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.0.0-alpha.0_1658095626526_0.11460747405906546", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "diff-sequences", "version": "29.0.0-alpha.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a542354065d04e2f3a7d29d72b2c5d4cf4313eed", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-koX9LsVBxkPTrlsPI3cVr0AJr8ewVPDWVDXcwQW4plQlLOQPQvfcIKOPtkEq1f8pFVy4vrUarc6lL3Hsc3qp6w==", "signatures": [{"sig": "MEUCIQCMgQwV8gSvtw+zZzt6Ue3O5QeJ2BOXKjCvfmVD9Tg6YAIgX/xchGwePegGszIu7uoLo6xUwlXWmrxZlad2lkslOmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwehAAhbf/9bB9hKTlX1FXatbzEGA1zchZhcHzmvIzzBaYw2dSNsXZ\r\nJRaSB9wIoBC53azTsNAtsYH7/t7w9Rcr2rrv1HtbU2U7q2FcAXVYeD+fpJbq\r\noJfAZvWXZw7BNDjZ4GeKnsf35ZuRvZjUnP7QlnUyAyNeLNqAKPasyqv0fxmI\r\naJg5W4HPH+NFBkMFw91oydtgKUYDzYZp1AxM27fCb0GFOx1lxSyOvzmF4p12\r\nIp5qiaiq/zkPMw0olIVE5YrF+M8X9ovQ4vK2HvaouXs8nEwf/EEdINxt/2X6\r\nF06fQz8ZjpWvfLKkWvpHCz38eY5Qv0Y4N+ODguteuMHN66F55LCEmDkIBw1P\r\n3dkb9yimacQb3wkb6L5/RP4Urv4QSMCkgYAu0mi16cIFYqbpT3KXRxhp5hkZ\r\n5dByLDzEgZVPTcw1JeoIT2IBeGRY2WSFjwFA1saIetpeA2iNcFSM7/JcGZxx\r\nrvfwFxTceQHKQ5Wqni92TBaNFPo2k4MqQ/7wfkzpG4Ko4GsUqdfXqimtsXrb\r\nwSPrmOtkh2BwgvQsoVg2i7IGh+TV4hTUuQWN22fzB+Z797PX0M/qwCC7aJd9\r\nvZYlZmJRec63dra5ocylt7cANmHBbl24bY42WNwTDA478r9ZSOMJ2y1zueq6\r\n0KESfKJyM7tohXG6Yeqnpz3/ijbz1VR18m4=\r\n=W99i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.0.0-alpha.2_1659742345248_0.5103392827851343", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "diff-sequences", "version": "29.0.0-alpha.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e27332f282e5142d4d03804ae6778ddd90dbb3e1", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-+1kCbnF4gWfTIuhznRtta+aLwy2myGELtWlS38WUNcXg98meRVn4PeE8QuM1wQ1yVEwM8E3FDANVZRDekAQW6w==", "signatures": [{"sig": "MEUCIQDLHRT2atnprr/vFWX9fSX7keRlAF5QT2vzrt91xAYGiQIgb5Gbfqyk18WCuNbuwLpVGuBjJ9tW1T5G8CypM7pyLuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFcxAAkrV5KYxBZYwI+OpuQ9nkfXZYzOvdQkFlQi5AL+fZrP40p2RC\r\nJuw3zDh1ZtofHCYs72U5SdTeZRzmumZO89V19Omxy5L4b6yjzDaZQ7T87fKe\r\nso7hJ+EqMncTiYRqM5EqKVRFBWNm1BRaBVxuTeurtcawzugXPeBwcQWei9co\r\nNyIv7ykp2IoaIeKzq6g7nV8ZnlQE5xgkJHz6oFkWkFd6DY+G+GoaBetQ7A36\r\n+vSoalmmqXs/QRYJuUd67h5GP7k0XvTMV8TEb4j9cPHVT9fl0H66uVayiAV+\r\n3IWR8jnUr1rVYB/uimlOiAPkWakaeC2AI7uMr2njUa0KqaE2/3cSCiIhOQgw\r\nMfXMKmEZt+Zmsf1tF3864N6c2ewc0XRJfqqkaAUMW1KSq5ppUJwr4xe95NlJ\r\n+rles1DSCcQ2THLwTTZtYvGpQ4aPB1E8/8VctMa8dJkDhxg5pmzauoKYToyZ\r\ncf68pdZBfMKFZjjJ6RGmekBmk/SBqu0yYRcBdmcN7GSAJ4mMFpZsL88g9ZCt\r\nGlTY1mIymUlZ5GNQ592yLJWwKShJh98gJrOsvuFd+q67I/+NoQ+KtONDjD7D\r\n5baaN9FvnzbWsj5fBcwFQ/JvPcv7JlNx/QQMsGwzkIbiK94WH5Gojsc5xq0E\r\n6/va018tB/GQe4PpevSHEW0mtadxZ+gESHw=\r\n=nmUT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.15.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.0.0-alpha.3_1659879691877_0.6277185356988579", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "diff-sequences", "version": "29.0.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bae49972ef3933556bcb0800b72e8579d19d9e4f", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.0.0.tgz", "fileCount": 5, "integrity": "sha512-7Qe/zd1wxSDL4D/X/FPjOMB+ZMDt71W94KYaq05I2l0oQqgXgs7s4ftYYmV38gBSrPz2vcygxfs1xn0FT+rKNA==", "signatures": [{"sig": "MEQCIEmsh6ewfR6EKMuaU9KFcOcCl/LRf4gmc7O6zMoFHL+ZAiBX7QgDL3KUa+/uO79CSgn7VNbdBplGi13DAOa/akJvFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZlA//Q1Kkrq0gY9aF91RTgQIBtz4tZ0XKE8BCzUY9Ep9bLnpB0AXZ\r\n+nJ4LqXPbSHXQGiFa4h6IUIC7InYtGZLc4xJVpDQwVQIEb8P542xzQtj+coF\r\naXv7COUEvABXaL4GRADS+ub+mlnOVCaJb3LBmLWiZt5wrc0WnP1QBtGzFq1e\r\nMpEDaakVwiINrNSHMBwLLIVWxX9Ze2VLQD+RtJP++B7s+QPVflGOcx5si5vp\r\nWqXeAPpnQTH5Uj4RK<PERSON>5bCKSci7T59/u66i88E42a/piHjlqGKXW3mGoAOdlb\r\nJ2wWvuzCvZeecLbg8HkGAQ95W2j7H2wGeVF9karrODwyibMyfDLWNtsGc0XG\r\nbscbJKMoOXkhkGwB4gaKgO2I7LIFBSxBZqDkS9SpH9QC0GdQD4Ve2+7UBwHZ\r\nzAui8pyohI9UPnsY4aw+3PSdWt2RGmVm3OjbCy9FDIpgoPCSa2DQjLSCrYXu\r\njr1A99DIgSwTXo3g78rLSjR1+/hegqIaTKt+jns0EGwWhCYwh7Z2yfXOjZoS\r\nW9OTtw2aiLGkjYI09h4zk3BqDrfRhRowuvKnvkAR5ZtYok7Ek3sVCnamT8F6\r\nM/nIdOPl+NpXN9Eit1sbkK7b+6EIdGMbtVAwQLs2J2apG6o8StQbb2ftFgAW\r\nysjGJkNpq8QeIkSXh4+Tre1Y0FYV6d2jJmw=\r\n=pLgB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.0.0_1661430804495_0.6604604596238781", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "diff-sequences", "version": "29.2.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4c55b5b40706c7b5d2c5c75999a50c56d214e8f6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.2.0.tgz", "fileCount": 5, "integrity": "sha512-413SY5JpYeSBZxmenGEmCVQ8mCgtFJF0w9PROdaS6z987XC2Pd2GOKqOITLtMftmyFZqgtCOb/QA7/Z3ZXfzIw==", "signatures": [{"sig": "MEUCIE0iiYJ5SvfO1j75DBWAliN/L6N5yLFTe9BrfstJHv1MAiEAwpDRr1m9k3ddszydfDSQ9M0kK/rbRjnaeOCRt6ytT7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrv6A//QVhv8GTo74dsQwBX5r3vWXaJoM01P6Pxhl9yR/LlRgV4YTyX\r\nicZBY7dU6//1dhUekBnzqbuUJDdJe5tmbsWE29GYB5w4jbG4sG+p1jS82e6X\r\nwEcKCM2x5YBYd7nOx0+IVEExZV6KvXcqEHQNO1ySXBydS8cmcI9j74Tru13d\r\nTERiQFQMO192czXRP8C+gJJX27U0nQ3crSKuBXYonMT5P/y3JASMGlaDGk++\r\nLSE5v5etOVyOEE0XFAk2WP7Mr/suMHQL7pazIL04A7jwXF7IEUdI+JIwI/au\r\ntShZVK+B4j3JKdSs3CwEymnQTJQJGCGQR1OR0qJzLiABn7ZSzMyA88gb5q2H\r\nljf7oK70Kj1lLos2LofCd2W48LdVdZHHhwwy0NTFJaFx4i+/1jQacWN5Ips8\r\nMXyeydNGrYbGI8dGes0RFTKD6j7jjRQqzOEzALKUX3a+Sj53Runi3Hq4gf9d\r\nplZybd/JnmYsSU3nUDZESpNe5fqCJERX2zEwQZ0csCWa9Tl2ek6g1i0crvjy\r\niWYXDAg1MK8liuWiqAgW5QdPUIDy38BTkFubdAvlhX35TbVCKUyrTfbREYuF\r\ndgwvhcosZzWDSGU+jx2cHE5Uc1Yxg5yUzPkdjyhMGmUCM54IDA7p3wu2Djm/\r\nPVQ/MPkeSMIQnucTjGaH+7e7jaadGxHW37k=\r\n=JkrT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "fast-check": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.2.0_1665738821198_0.05435777744290804", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "diff-sequences", "version": "29.3.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "104b5b95fe725932421a9c6e5b4bef84c3f2249e", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.3.1.tgz", "fileCount": 5, "integrity": "sha512-hlM3QR272NXCi4pq+N4Kok4kOp6EsgOM3ZSpJI7Da3UAs+Ttsi8MRmB6trM/lhyzUxGfOgnpkHtgqm5Q/CTcfQ==", "signatures": [{"sig": "MEQCIEPGHMWI8w84+aJt9vSpfdgpZ6D8XBQ5HcCxPuCGp3PIAiAGD0sw4Hr6me0OeKvT7diIrxnxULyGrYYo1DCZlEnpnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuXw/9EA3TRimS3iQ5p0g2RD2rm2W3rT9ilViPJtgAOEM28612mnwa\r\n6ry+4J0t3o8j4F5opSAZWIoZpLSPiODUWPUu3E1Dv+PD9PAksjahl8ZYfoA3\r\nJkYpGkVR2cOuLoIs9zlwXjSPFIbGnhsHd9JgHEQ0S80facFZ79tUpTsq06Ce\r\nfEH/359W61HhCDSlo3t31ITB+UL9wPau7DNsUOadFLzUS91CN6qo/4JGyxri\r\nSase+UaR7nZ6jZOk4B4sWHmlRAWsT4MO9lfYUejFnXfZSI7LZuAKQn8Ja/h7\r\nvl4gixqSFxgUBe5x2zV3miBBQLYPdJejTCOhRC37G1Mw7kgfe2UXFKQBYeMl\r\n4IjL2TBOS1r4ksIcZSCK9wfBsqaDDAfr5w3WShg+iQOny5CRVPX2sWpXiOEZ\r\nxIcsF8Ba8XQVazK6U7IsuZBRPUz17NXdHkBuzt8DNH6rqZ5H8aN1DzFH1hqq\r\nv4du9E4y25QCDKozFshbuan8gvvFI2gSO844yOuM4R6peiDx/hZToLDrXyKf\r\nw74XdeC6sYLcOAJ9A1cJWGIcOn1780Mnc7kvQX9BXYzI5uIZmYFcigLHG1up\r\nTSZvZp/7nLZHGkoU7+fVi1ZEA8BueOjqd4P8nn497qVqRNRBymCIAe6VkUV5\r\ng0E0gQJ3u53rEvEkOR7kVZWQ05Wn2sumd1M=\r\n=E07V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.3.1_1667948179328_0.7857559123306008", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "diff-sequences", "version": "29.4.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "711fe6bd8a5869fe2539cee4a5152425ff671fda", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.4.2.tgz", "fileCount": 5, "integrity": "sha512-R6P0Y6PrsH3n4hUXxL3nns0rbRk6Q33js3ygJBeEpbzLzgcNuJ61+u0RXasFpTKISw99TxUzFnumSnRLsjhLaw==", "signatures": [{"sig": "MEUCIC0aLFT7DTC3yEWw8WV0JbRxZOQ6gOWfuV3sDV8NMI2HAiEAlLKmEySzpi//5hcKOb3cXqU7pQQtnNmCY87dQItlXEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocbA//QD7CDZiGmP0XkaNPvXyg/hXazseJ2USvm1G8aG18azlqxvu/\r\ngJT0LRInGHgF9WLkCapry5of13LcAbo/LZKn3YrCs+EAeQ0rKLNe7gT+AEPW\r\na38bhz9t8ERvmov9e7ZL+RGPU+LVdKqZ1gtMYOc2YAHx4JB2t6vUl9qN6ivq\r\nArOeIj5xsKK3btdYlJJjgzS1momYeTUtOKCFdrazjbOKx88vduLbTbKZCP/7\r\nlmfBr8w74dwMhgjL3NFziALjEmeafYcDpedGzsCKdRpQk0DYrTPmtKYj7OyI\r\njJ/R01O3jSS1VGnOSQVlFWEC21ODfRFDkpzuuzgO10O0Z0dxgpRKx76yaUhu\r\nLNe3tYTlaZ7tl+r9icwJ5s7hwzZijGTQ1Oar63eYvaZdwhNqRnxzDH00/o5D\r\nX84/GNvFbYD+9/qr4mp9D9bTUDMc1AaNndHw3c3Cob36AtemDKQqUkhBlCsz\r\nR6VW6dCGyqwNObtJwM0Azpo9AcaY9wLpk8fsXOkGW37q9bXOeNAXx2J6H4wz\r\nS2OnTbMy/1uiRS/brhmvaAxFm8w+g5AZsz5yQQq5aLbiz6hiJL5yq/ecp+aO\r\nIabEYzjquLwcIyGZfugVHn814N2LZFZHl16PUZhAE44aHaaT+7iLSiLaZZtb\r\nhYC4wsxw2qz8TJww/ptHds7b6oWlSr0JnA4=\r\n=JzBj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "16.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.4.2_1675777521692_0.4266724773528614", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "diff-sequences", "version": "29.4.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9314bc1fabe09267ffeca9cbafc457d8499a13f2", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.4.3.tgz", "fileCount": 5, "integrity": "sha512-ofrBgwpPhCD85kMKtE9RYFFq6OC1A89oW2vvgWZNCwxrUpRUILopY7lsYyMDSjc8g6U6aiO0Qubg6r4Wgt5ZnA==", "signatures": [{"sig": "MEUCIHQ5V4E/gs65osyCMTXAhegNK1w3JfLtGUvPH0uwLVv3AiEA6Ty6pfR1ntV9nnlu8CdAE50hWU77adJpV5iHSgVNBKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4zA//e6nZ60nhoBsAbBlXFQG9JpMgCql7OGyq7kfTpT5q2eKlv49Y\r\ntClbjvQa8RDJNN4W9Xa5FCkERs09ZCaJ94DtX00hNRmS3oU19EpeJMnW6bJ+\r\n6LiVGBIHrjU/eKtMSS6ZXe808NHt8uypiQJxAaVwATwyQahZDJTGv7l7l6Fo\r\nnkkvDVXVqzxOSIv2s34RPHjAb5nW7x3lu+lsHju7hb/7klNLl8+9emZhmO3T\r\n6Upj35K61iW7SOrcNecIG09Ut5dsQ46V2jo8AiwipeNI/SY0r6a/tQQ63rVp\r\nT02VHxy/aE/RYnzejj2Ht0EWL+J7cZpMnRhcDotQRHNMhvu5lJAqUCx/wWpW\r\n4+pbbGrmI9oG7dlZ6HDVUq2pObsSfGHUG7bWtwksnDISzZ414K1ZGrOgvMX6\r\nyGXMDYZNCQb0TrMP5+HeBwEEeBbDvyzsRgKg950vuv5URU/ck9MVnjC+UDZ/\r\n+7JnjzgZf8HNmJSXsKvrWP21v0Ky58ckdtCyP38L+lPonKeE9lz8Ho9GDeRl\r\naTF8kWxzaQloa6OuE2DVQ44l0KJEQJC+ZP1zriBJF8j64xCxrjHsIr30+v5l\r\nH6iyiGNFdNJQXXLaXidfitHqDwY2MphRPwy+wUsXZeTSgVi+Mgd3UtDPrd1H\r\nsyCrDcccxsPgwMXVC1iqn/TjqpO+A7MVBCQ=\r\n=B3GJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "18.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.4.3_1676462236317_0.9159740967010261", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "diff-sequences", "version": "29.6.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4deaf894d11407c51efc8418012f9e70b84ea921", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz", "fileCount": 5, "integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==", "signatures": [{"sig": "MEYCIQCh8rzv+wFVJplOSjDLCcRRWk3m5thyVKK75mWhYrx+FwIhAP/jdlPQMB+pfFqwAfaSJJXPTxvLIhDTo7NFeI34cM2L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45957}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "18.17.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_29.6.3_1692621537602_0.9551322841086527", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "diff-sequences", "version": "30.0.0-alpha.0", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "55b1d44740ea6786c0451882978f03c95284f581", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-RbT3IwjpQR2MsWIU9ikjn6kZuo9IgtANHWJ3X0ov4vnHObD1BSQX3ZtyqqIq5oTb6DP8xD8YXhRKR2l1thaByw==", "signatures": [{"sig": "MEUCIDznkIRFVPoB5/POY9BNLrWl+EPEEzr0vCJsyR9HmbGmAiEA5ej/sL3x2+kAO42UZMGC08DRgJcYGtFqKq0WbtHMXiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45543}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.0_1698671619575_0.24652076764896202", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "diff-sequences", "version": "30.0.0-alpha.1", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "88a95e2e493ac79456558c46888376cd070fb025", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-mq8+SfDfvkkyD23+6qVWjkbk6n8GSpwHoQP56LbdDhcclOgdYF4OIZO0PTGC2KHyb+mKfRns0JfJS3YCV6ACog==", "signatures": [{"sig": "MEUCIQDSZK98Hz1fBAezFDKrCCBuWxFCodZxBYqpGxcXKHvI8AIgMnbh0IlP/opAkU3TnrGp60VkMpUVd6AmY6yBpEOW6Qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45543}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.1_1698672766010_0.4029926200190441", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "diff-sequences", "version": "30.0.0-alpha.2", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e7457cc887e495a5f9a4e6478e4ecf428bb5acb5", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-gatKDj6z4lFPU1mFkGr3BIyscQLWkrKxzS9JLph7K8etf0qLyOeW6/fEK0XRs5j5DIUS4WdrxuQ+LCMr3UFOiw==", "signatures": [{"sig": "MEQCIDeb63TBARqJU/yq6jjdzSrDDoUOKeG1v8EYBVfxs4ynAiAUPPK6KsIntWlaZ2++mZpZHmLBQmjdRpGhIm+iepFyHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45544}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.9.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.2_1700126894341_0.05463297810203871", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "diff-sequences", "version": "30.0.0-alpha.3", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "07e7a50a0d73f69f8343151ca653aca85f76aab6", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-yaGzjI+ifv9vL61+Lyu4k3i2G6/4wWyAbOees2JAf7Qh5zD95bF9BKbrog5tTNj6EDk7AVy7K8Aj2K0Z13fq6g==", "signatures": [{"sig": "MEUCIQC+eQKVdwwlslhmKyALlNETj9EdqxJuxeRbXptbS3jU/AIgAX8JikjWv2rerqQ31maTfHFTWoEWcaGp9zChz7Y0N0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45548}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.3_1708427327880_0.9053451012319795", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "diff-sequences", "version": "30.0.0-alpha.4", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d3ca2fc09f49f89c103736d2cfc54bc8f875ab72", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-6QR1dEtY+srnwESRY+gE9ZwU67/widIC4OUkPusHMiF+MBg161VemMnCS1TAMJqj8ILH75LYdUYprTj/OtW6bw==", "signatures": [{"sig": "MEUCIBIQ4SOD21Q4UWUqd4OQrmeXTLWUQFElBPOwJQNfpPhfAiEAnY8tmgezTDT6NM4DVMfAAclEUOVouU2hVcJQCYwMPcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45592}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.4_1715550194247_0.3976567849341146", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "diff-sequences", "version": "30.0.0-alpha.5", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "5188c6b846f09b9997f8b6c7a4dc813eeaa5b3eb", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-zztnoTBNChfe30emTHZw382eoMZlTWS/t4R88HU2UCmiw38dlgTwglI+xqaUhZiEg/CnA20ZRVBaBIio3TazIw==", "signatures": [{"sig": "MEUCICrgRO3vhuEIm2o0oFU3bpjHqckBdrWvu67ccGI4mLnuAiEA6KRajwhsbLqEYCRMqnUTeYUTiDHt9bq0PrMDd1do410=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45592}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.5_1717073032967_0.8057109884322537", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "diff-sequences", "version": "30.0.0-alpha.6", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "dist": {"shasum": "a24a7f7b238ee13718e7d7411d6fcedc6ee37e96", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-DVGt3/yzbneMUTuupsMqyfSXMnU2fE0lVsC9uFsJmRpluvSi7ZhrS0GX5tnMna6Ta788FGfOUx+irI/+cAZ4EA==", "signatures": [{"sig": "MEYCIQCnGfGoq79MC6ehZ0kfeCDuQJNE8f0Q6xOI00rqQqRyQQIhAP6UuvTGayHIUrHCl9b9EqKjdj6vjvrMT04AQ5YMXyVc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45592}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.11.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^5.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.6_1723102975997_0.40456223766462274", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "diff-sequences", "version": "30.0.0-alpha.7", "keywords": ["fast", "linear", "space", "callback", "diff"], "license": "MIT", "_id": "diff-sequences@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "dist": {"shasum": "4d93fe1a7368ecb7140b27b292391010136c5e2b", "tarball": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-T8qx2cdaE0DW3nMpgf0jxOvSECOIkvPpjEeTs3qVT/+gzhZGLKjex4lc+dNiaLA92hDwmt6AG9WO/j2RUlNQJw==", "signatures": [{"sig": "MEUCIAqto8lMDyymG3BrakposT5+xxFtgFAMJOBdMAlNxmUiAiEAiRaBcfWfCK/LZLhLHpoThXLCfpU/PZNhebDhuyZCrU4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45581}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "description": "Compare items in two sequences to find a longest common subsequence", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"diff": "^7.0.0", "benchmark": "^2.1.4", "@fast-check/jest": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/diff-sequences_30.0.0-alpha.7_1738225703100_0.10576198278999982", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2018-02-07T08:27:14.570Z", "modified": "2025-05-28T18:29:24.093Z", "0.0.0": "2018-02-07T08:27:15.381Z", "22.2.0": "2018-02-07T10:25:52.632Z", "22.4.3": "2018-03-21T16:07:58.704Z", "23.0.1": "2018-05-27T15:30:39.544Z", "23.2.0": "2018-06-25T14:05:07.827Z", "23.6.0": "2018-09-10T12:42:46.921Z", "24.0.0-alpha.0": "2018-10-19T12:12:24.277Z", "24.0.0-alpha.1": "2018-10-22T15:35:28.176Z", "24.0.0-alpha.2": "2018-10-25T10:31:40.220Z", "24.0.0-alpha.4": "2018-10-26T16:32:58.114Z", "24.0.0-alpha.5": "2018-11-09T13:12:27.668Z", "24.0.0-alpha.6": "2018-11-09T17:49:25.919Z", "24.0.0-alpha.7": "2018-12-11T16:07:30.526Z", "24.0.0-alpha.9": "2018-12-19T14:22:53.561Z", "24.0.0-alpha.10": "2019-01-09T17:00:58.838Z", "24.0.0-alpha.11": "2019-01-10T18:41:24.557Z", "24.0.0-alpha.12": "2019-01-11T14:58:02.904Z", "24.0.0-alpha.13": "2019-01-23T15:15:16.318Z", "24.0.0-alpha.15": "2019-01-24T17:52:16.859Z", "24.0.0-alpha.16": "2019-01-25T13:41:47.951Z", "24.0.0": "2019-01-25T15:04:42.491Z", "24.2.0": "2019-03-05T11:22:43.927Z", "24.2.0-alpha.0": "2019-03-05T14:46:21.974Z", "24.3.0": "2019-03-07T12:59:18.867Z", "24.9.0": "2019-08-16T05:55:47.104Z", "25.0.0": "2019-08-22T03:23:44.608Z", "25.1.0": "2020-01-22T00:59:44.659Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.294Z", "25.2.0": "2020-03-25T17:57:58.537Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.797Z", "25.2.1-alpha.2": "2020-03-26T08:10:22.272Z", "25.2.1": "2020-03-26T09:01:04.931Z", "25.2.6": "2020-04-02T10:29:09.129Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.636Z", "26.0.0": "2020-05-04T17:53:01.539Z", "26.3.0": "2020-08-10T11:31:41.843Z", "26.5.0": "2020-10-05T09:28:06.060Z", "26.6.2": "2020-11-02T12:51:13.528Z", "27.0.0-next.0": "2020-12-05T17:25:07.936Z", "27.0.1": "2021-05-25T10:06:24.230Z", "27.0.6": "2021-06-28T17:05:30.968Z", "27.4.0": "2021-11-29T13:36:54.911Z", "27.5.0": "2022-02-05T09:59:18.000Z", "27.5.1": "2022-02-08T10:52:12.838Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.583Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.835Z", "28.0.0-alpha.4": "2022-02-22T12:13:54.567Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.607Z", "28.0.0-alpha.9": "2022-04-19T10:59:13.936Z", "28.0.0": "2022-04-25T12:08:02.435Z", "28.0.2": "2022-04-27T07:44:00.301Z", "28.1.1": "2022-06-07T06:09:35.547Z", "29.0.0-alpha.0": "2022-07-17T22:07:06.730Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.355Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.107Z", "29.0.0": "2022-08-25T12:33:24.673Z", "29.2.0": "2022-10-14T09:13:41.466Z", "29.3.1": "2022-11-08T22:56:19.533Z", "29.4.2": "2023-02-07T13:45:21.847Z", "29.4.3": "2023-02-15T11:57:16.478Z", "29.6.3": "2023-08-21T12:38:57.892Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.917Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.303Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.524Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.019Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.403Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.118Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.316Z", "30.0.0-alpha.7": "2025-01-30T08:28:23.257Z"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "license": "MIT", "homepage": "https://github.com/jestjs/jest#readme", "keywords": ["fast", "linear", "space", "callback", "diff"], "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/diff-sequences"}, "description": "Compare items in two sequences to find a longest common subsequence", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "simenb"}, {"email": "rickhan<PERSON><PERSON>@gmail.com", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "openjs-operations"}, {"email": "<EMAIL>", "name": "cpojer"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rubennorte"}, {"email": "david<PERSON><EMAIL>", "name": "davidzilburg"}], "readme": "", "readmeFilename": ""}