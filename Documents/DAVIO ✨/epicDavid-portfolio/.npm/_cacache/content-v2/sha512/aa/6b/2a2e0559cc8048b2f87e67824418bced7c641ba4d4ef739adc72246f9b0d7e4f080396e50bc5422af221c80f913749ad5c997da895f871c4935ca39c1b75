{"_id": "update-browserslist-db", "_rev": "20-037d461dbfe5a2179d70e970353b4f26", "name": "update-browserslist-db", "dist-tags": {"latest": "1.1.3"}, "versions": {"1.0.0": {"name": "update-browserslist-db", "version": "1.0.0", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "b40e6d558e0b39ec4b1638532e54b8659a86d467", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-3Y+NfVggCCf5OmVvHx9vI4TqOp0gqiSUHcwyUr1I4n+KQ7CruV1z8kRx3FbPLpp6vN/wR6w1mZqe20N1ozobzg==", "signatures": [{"sig": "MEUCIDm5/rctqdmhbHpa/WKgRE36U1/f2T2PHMAgYsCVE6O2AiEAxtVWZU/8YxZWRdIhwc8r7wFRUTQZRG57pTiAYQMFbpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiseZFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorChAAkibvaxobpQvlDfJDFcX7971rtTN00rQYNg+HyDI84Q5RTcrC\r\nCu9qU2jdmD/VOY0nyhTVKjuSddPq+IK30Gq/sLJ0tO0EazJ+kuPdfgIbaYvw\r\nb4NinlHgBqrjFIYBeXCu6nx1ZuX42kLOqIWRLtCZgAKOU8GrcwtdVeXib0fS\r\nNagCArPTW0gvwWCzYXLxq6sdrbQzSQNt+F9em15UhT5/cQ/95CMucEyLIQ6b\r\nTPrh3+nzjVTrcR+l+w4IVWpiIOri9srhtrUuNxV9VIo50Jc8w0DPKncjQlC4\r\nOceaep60ueN/gLEfm5h/MeNESGWNKZV94KUhoIpZhhLPzW+xLbUO4CBb15YT\r\neDl61McRl0ScpqA47oUtd9s0AxtplXp9Ckxofc0HjzXuTsPfOYOpFGWQes/9\r\naETKmwHHbk3PYn1arneb4V+ncxbbiLPbPgZpAvl5/y9K+Zu7WYNo2aEh+dza\r\n5eGXu2TPxsEdFhuPf+EZLmH5uEQ1VV7dgm5BVyPX2Q5v38QiVlZdS+1kpVw4\r\n+I7TXEU4XP0s4rXhJicsY+TvRkXW+MfjME0jJ2pHeuQZbpNEOEVG1dNlOENX\r\nSOKgQc9VTzWfVe1hOMPVBsLebPyfmOWgDQK5Q6W+3spfRKmvLUW4xpm6Ri+b\r\n7Yt243hraDeijhQ/AebRnqPZASp5WT+Qi2A=\r\n=qexj\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "5da23192ceccfa436353b6b6790b72f2c4970357", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.0_1655825989596_0.499179932053452", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "update-browserslist-db", "version": "1.0.1", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "a0351feafc34b84536269b2a488c1694a0c07933", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-mCGnDiLaRV8H+6X3bCVfBf+l7c7BitGcwmf9feRaLPqkGXKgyRbhA+RlDLTY7fvXH3bPOYu24giQe1EtJm4ThA==", "signatures": [{"sig": "MEYCIQD3079CQXoVIgbcHGi9Bo/x06WH1gRDF6qoKRS/Dak2kgIhALqlB2gSKONSNqSSUq++0iA+3aMvHm/4mOHnnvMXigFU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisfi7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj2w/9FfCI9SjhuXXJT5cSEtglM3jqwbW4juzt/AQFshYb8yXNKra+\r\nXYY4twg/g8PIUt27JIjq7s7XEQ24d5MCn4n8f7vWEPoAZYIX8NfZEyjyv6jj\r\njwAPsIZR6kJuKV0f4VaSP+BM8Wn74aKh+qJdKqxKeLvvbVHcDFRW12Hc5Loc\r\nF2/dX+GfyBrI1KM3Bb4LrFVSEyiYw/mS2QslbFYxqwIKqBpk16GTxF4WwVBq\r\n+Tlz7q0XjWcXDmtVwaODhWDTKLygIip9BbNOl9dJarDxjjSLNaAsge56ik0B\r\n/NcX5tSf2YizHbwCEmmnJMLIv/IEkF3Cq48j0jxxVQyEZ3Q/YfsAv7KWwWeI\r\n7VdVFxsjfhNH76o/Q0Os5JQnLrxzmARwmk5/jIzQ545cGY/nXbiBCteqp0UW\r\nGnx27aOtRBqztZ5HL9SkCSrvUPZDx3mLBQ9mejrtqP6nQWGD635tE0nwRr2W\r\nhabbAKbqd6BXbRMX6TX5f5RF7xa00718+RmdtD5mohYd4X4+y6fr/MDNc6QJ\r\nNQ7NpYEEQ53QJKTO4AKyRiTOqkqppk4UxdBil6D0d2r3RwzKAfRYQriIpYYS\r\n2m0pPWS7/uK155ieWBcgvupSwkqTGO7S2rOfv42iEB4zqUekXHyiGLMzlKAx\r\nSeB57SAY21J5swh6Ek1QQGXYMSm6psCjVQ4=\r\n=vvGM\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "4e20da30a320d6bee7888d9ca89ef352c4e3634f", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.1_1655830715497_0.41053555494996896", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "update-browserslist-db", "version": "1.0.2", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "2c7c0efce9fc6b247fad11a0c83a77f561a0b1e5", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-xYGETFLNgMWaR2NKCUcqz2Uu4yKgirTNaMgOPBjlRAj8G7EMRRAe0rGVZvHT3Y/9iR2bkXGxI4cnEnneW1xJFQ==", "signatures": [{"sig": "MEUCIGZcLtSxFL5IB4KzVAwKGD6sBvUZrlTf1zGese+2sXi9AiEA/XUd2oimgHyAid+g4qrxuh9srXzC2prBmP9QiG1pHw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisgGiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqp4w/+L9je6bPvIAZEOv+YYR/tfEDJQLvEZh7cm0OYDz0F50BF0GTF\r\nobvsY/Op4X7HLoPQcdJ/VaTAZm/g+zP1qOz/eHTkAVISrcel1Jlrpf/g2AWo\r\nN+iCtiH9p970LXfy1yV32cU6mY053jC5nCBBhl0L2XqHFoCDIT+mvEqIyqTJ\r\ne2rwCeejvnTxZumnIGjLz9l0fCSIRMx6gSdOfYDdIHRXvXlVGKC+HBYn4f+L\r\nsNJCUPw9zXVbCPuS6FkdXrX3sI13gnm9gpmbMVkMhBAjZRR94JSpXnXnjcS1\r\nmQ6YHtX1IS1c/HLrY5EnIE6Av8t2DL24vebWZJNFeD2OBM7mUEqJpI/fpjlx\r\nizsSXFycSiFKWPRp8oUUUP8nDlpTQ+sNH/QZ1pt89NARy0Jj/MOEGyDJiPFa\r\nieycgqGPuaQskyXzQEDHt9NpGtPxsQiB/5B6ITDkX7bgNSThu638qyHTAUCs\r\nZQaAv0k8cv0Lu3uimK868KwKGX3Uwd9NM2gvL0RHDOT995nRw5IMK8me91jP\r\nHy5Nrki8NQ3oH9wIwKcydmTNjD9seYYrjfVMRAuv0KmKJruihKP5avWmHsjg\r\nOMveK7BnNQvjtV6erjL38ovv6ggddeWPXhfTJXK2xg56CneSSVqY8jVMdN35\r\nk6XdVMbcLl+O6xLMBaX2VmoqqcqHh4JNG50=\r\n=bCJF\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "dec329bc47797f79914c1bb09e29a83c7a9cbcf5", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.2_1655832994441_0.1966459744004725", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "update-browserslist-db", "version": "1.0.3", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "6c47cb996f34afb363e924748e2f6e4d983c6fc1", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-ufSazemeh9Gty0qiWtoRpJ9F5Q5W3xdIPm1UZQqYQv/q0Nyb9EMHUB2lu+O9x1re9WsorpMAUu4Y6Lxcs5n+XQ==", "signatures": [{"sig": "MEYCIQD6AWfjCPEKZx3WsyUS2jwNYKgmC4v2BlBYIcQEFpHZKgIhALek28Ge/XwrPpuJmgWmaAV1G8iv+U9RjK6SsH1M7XsA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisgKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe0A//UlghYNk1/2LGU0osGBwrpllMSMnMwoLeUFcMOqANng2sQmHQ\r\n5/8l9S1qHxUNVnx4bXX95tIHwoUNp4SlakklCZ6D42o+BpVLl7onPH/sY6Xt\r\ngqOUVDLq5IVg/yJMozELHMv4PWawWFmUdflJwsmtmZuD2kGWe8BkVduGB1La\r\nH15zBvJ4kCfb9aLrHxselMvaIyKMRAMJJUbWO0Bqc5rsJaBW2ZBaMseEJ73G\r\nwH+QdTKodL+/t5vjDfPV1AhgvAUog1y8t36l00k+yXCAZY9sAfAoi7aMZ7Y3\r\nixOUdCXAjtYabL9lhM+ksTL876VIDCXQWsPB4SzF/8ifkag966zfWognb07x\r\nAnJhv++Mg72K0d52ssDom8kPK/cHQ0r0rAitF7gbv5qAJnphD7H3Te8Bx/C+\r\nj9eABRaumsJipmxDkaODx5Ixn/YrXvOT6bCpIDTtvRmsNz4RFmVL/kOgPuEn\r\nRSY/HZRHvjXA5FuvVkYWwJMopbDhhQxBu33yvyp/27AJeUJSf3KN0lisApZt\r\ngv9BT9mnl48OHTq4OQovTnHFg8scduUja5APh2slToFUaBYtHWxdzCZoKuAQ\r\nwQmMikR3fvo1Kq2gPz7E8fMI0Lv/45oHqO44qD8mjH18+QKu30fQTuGdw8vV\r\nRAvV6cDKimtffnKL0PuKjs21Lsurv/h3WKc=\r\n=WEbn\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "e783cc0214d29caae4e2921dc9df46d3a378cad6", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.3_1655833216238_0.8225166537941184", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "update-browserslist-db", "version": "1.0.4", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "dbfc5a789caa26b1db8990796c2c8ebbce304824", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.4.tgz", "fileCount": 6, "integrity": "sha512-jnmO2BEGUjsMOe/Fg9u0oczOe/ppIDZPebzccl1yDWGLFP16Pa1/RM5wEoKYPG2zstNcDuAStejyxsOuKINdGA==", "signatures": [{"sig": "MEUCIQCqxwAGPR1jsBN+KaBOuCJpz59Z/rD1i2kIgAXCXa+UgQIgQX+Yqth8KqXH1o3icoe3IPdWuuwhrIPj7ilRP0xU2fM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitGj5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgOQ//fZeC1hH22UApUdnrVpBIiKbAdHDeSQAb1bLatKh1EGbUzImU\r\nWVuhYkUfwsj+zKLk9h5vid61geI5ox9jIuqVPoxlj8oeini4ozh0l3aXisqE\r\nf7XhfKldM5jrX3kdmcXXPJOL8IO5ZChVwqgxOLA72g+nRL1f7IyCveEjH9/F\r\np/oSMNYt4XYY/Lxp90EWKhq+BSsE0qOiO4IZkhrxiBRQvI5CzqJk1CVCYfIY\r\nsnHxHCpFM3qDkSC39NgWbrgID+OuCu86Th5udQFjyNnLv+gBPWlB9sBFMU98\r\n9LDrJIL4ebtJNpdZYGd3KpXfIJwjIriswrTPhKjDQBCgUM1wDVEsOOiMjz1z\r\n1VKvV3mVkKTTLQCZX47GF8ln0zSE2Sgr1lO55zGSySKVc253DpaHrYHIAr9D\r\npQloot9XwG9g41RApuz5+bT4Hvvke/fjd/cGIcEAu2j3nwqsBiAMPRpmQnbC\r\nkrnZWTvM6Q78VwPNfclOva/cphK7GhlNqLS2ggB3cssJkZ5VLXSaO6EspcVg\r\nf817oBdybWc4hzSNRWVEuB3Z2NCZaHJpoQlp/b7XViJIzX2kli0LvFQqC6ev\r\n+SUh2611E6gJWatLHJfEm/uB92hrzdG6C96Vj8CSv4pZ5ma0cxUNmywy9hbZ\r\ngCefPhShSwni+22HFSVxpGn8lKeBHBQgZoM=\r\n=Po9i\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "f38f55fa32600df93738f9cb40de876d5a18db15", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.4_1655990521558_0.6721344951234394", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "update-browserslist-db", "version": "1.0.5", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "be06a5eedd62f107b7c19eb5bcefb194411abf38", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.5.tgz", "fileCount": 7, "integrity": "sha512-dteFFpCyvuDdr9S/ff1ISkKt/9YZxKjI9WlRR99c180GaztJtRa/fn18FdxGVKVsnPY7/a/FDN68mcvUmP4U7Q==", "signatures": [{"sig": "MEUCIFGm6fmnVT60TxAzfXYDbvfB6521hyekYzjhqLBivsq8AiEAtPfiSq0ccC+kiOzyHCahNfxNdhF6y8X0tUjZugvFs1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1ZfkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZEBAAiymQImKNQw+9u9eZhU9/54JpR7DAJwYZEaJr91+AOnfYzeyZ\r\nd7GagzfFdcBYTgC4ouLZC0v0Zpj9kp6fCvq+IQT9884KEDrGkI7gnp3y10CD\r\nUyk+ZFZ28RZJ2gFb/YfIj1Td3JL7plLrA3A2SpjGsaBvgunjhe8HI+APN96Z\r\nIQfQSA4q4omXbFZ5sM148KU8mLroe3mg6owcfsr6D/iy+l/JZDUmSxv0z6IP\r\nUmf2fy3B6QozD2caNZhA92Ykn6T6FyJ+QIq2iq45qITdRskJtsCA/EItul7g\r\n6CJ+AfqP57w87LphmQMLTwJn1mrIKhI8yfCj/ROx/p7MoKguZIJoIyJ8DhLG\r\nfGnmj3sFl5Gh0EkdxCI3UMpxY2ZqJnYm3IyI6V1wuWwaJ2jsVlAqALHaIKVe\r\n8gZgPH2DeIfuTImW2l8YEVURxSRPIdJlm33rZiX7gEvh9PH6FbcUlXEXmOCv\r\nAJv6ybsvrwcZB07S49oQXICzLw+18VlsW6+uVrgFCFrvv5tlS1RwsOkZotaG\r\nCcguiM2lNUdjrBAvIDhrVKrmmgmhxWdaz93BK0kvqDHz8nMbVYc9qgnd7cnD\r\nXarf42hjmO3L0wJDSB5KNtODlUPFrKjNl2/aU1x5Wy255z6nsVleCn6b/zBe\r\nPYVs1mI6Dj5F+7fXBHCyeDPI5aY55CCFbP4=\r\n=v63P\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "75e7cf7303e286dec4e3f1841765855650a34abc", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.6.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.5_1658165220312_0.7676966784005661", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "update-browserslist-db", "version": "1.0.6", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "044fddb5c26989628da5cff7a82ce1472152bce6", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.6.tgz", "fileCount": 7, "integrity": "sha512-We7BqM9XFlcW94Op93uW8+2LXvGezs7QA0WY+f1H7RR1q46B06W6hZF6LbmOlpCS1HU22q/6NOGTGW5sCm7NJQ==", "signatures": [{"sig": "MEUCIEhSiutoieknMMNthppzWc8fHGM0+G9Ahc8g8GLBYuAVAiEAhgzrZLj0i9w87IbGpnQ2LXFzVPTRv7bpvOawZifoFIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjERNYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokfhAAoz0aSjP0VcF+JQsE8Uo0jBaMzTjnkahGgaDqBZVZtb+atcrx\r\nkYyqPmegXe0yKZ9BzhetBiF2AaOgj1I2vUn+FKAs+Zf92dxzIkm9H7C/OaT0\r\nJdrVknApRWYGvMtl92dmRbSiXrUSX/0YQVOhwwY/07R50SFfPoCrC2ZtjOPe\r\nazBuXtp72DofAjcf5QqFq2soivryBKVHLthnLKEmgIUDDllolnQW7oj2igwC\r\nGNLysKero4xRaDY6UnJmcFYnZOFzyfLAVYbSsyoOvfbTPcc0PTZmoUVhODdz\r\n9lsxnQVh8Z5fynDj6O3SWrhJ9FH3HGV2FBMHttaKOIDvzrKR/BRtLcZNdTFp\r\n6+3hwlWf+LE9hN5nT/bY0eKbRhT5w3jtL/XvjH+uiTHgUHEo3EF46WvZQapz\r\nN1ueTK7VuGz+OukC5cluirjdOCnIrPBlslDgzez9/h6AJUEvb2eLCk2C6dhV\r\nrERjMieLox6lHmEQM8szEb6RaVzEfIC5B6WvOsaAhdI5Z4KqXJeu+sobrx2w\r\nbri+dUvcxaUHtUf6FVYh8wAIHGYi0OxiuV/YoJHoKyZ7HZYZX4qfp4/cYJRu\r\nX4IE3MoEJJvX3zaYlURJqS0vaP6Z64WH5Z5CFUroV13dWsgx2Qo+hj3cykck\r\nW5SC6PaQhL322ihp5NgY13qTlhskWLlRrVI=\r\n=abuv\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "caece66aeb955d87d636dfa4fb589b93e4a1e61c", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.8.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.6_1662063448584_0.11298894680764793", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "update-browserslist-db", "version": "1.0.7", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "16279639cff1d0f800b14792de43d97df2d11b7d", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.7.tgz", "fileCount": 7, "integrity": "sha512-iN/XYesmZ2RmmWAiI4Z5rq0YqSiv0brj9Ce9CfhNE4xIW2h+MFxcgkxIzZ+ShkFPUkjU3gQ+3oypadD3RAMtrg==", "signatures": [{"sig": "MEUCIQDp7qEZHjrMCiH5C5W0aWuTj1oJY+5tJFaHAaHxcHMlyAIgB8wyqyTyIo8GqhfbxaOyv1jaEX95IcnzxrtW+RyQCgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEdepACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorVw/+Ly2GtL1JdCiE/vmtY8767grGjujJtiWAf+JYWq9aUV5dgb/l\r\nIoQ7U6EZtfn2hgR3UW+fOj4aFqCYgiSbfmUUpUsiShSbkNJT0J+waKlCvT9R\r\nL+FNRe24bTPkixjaVIh3cEn5Xt4NI7J8QmHC0gFyqsx1qT4xcMYyY610Iltr\r\n11R0szf15qMkUEowXspjGL9ST4ibmeLwyweVCTLJr5AqXyxeQ4BTlMjkVor/\r\n65/5ekkIVwURDwYVMsae9W1QvMElj2GkB2o356lKcbKqMgBClPYwOHwCEkMh\r\nQ3GFRfexf+b0RAVC6muxWBPNDTUtzw+AJq+QbiBuyAKPvm+gVPF6JhiFXWD0\r\nAFT7G49e91jexIkvQd5X02uI3miaz7/52Ukcel4pdFeXELFCFsRIRO59+Uew\r\niBxZkXU3Mq9AH09pNUJqN6BeCDHOjrMDtNDTYhZHv5T3TQJwFmbBT4QMw2F2\r\n92Z2cRzjQUZPQ6pSvVjRLDUdrQd+8tLepkotj2fuYHIshMqNrsfFiC8vWBQ7\r\nu65492aGtiPP3yw2PcAt9F8z8TrjbxOqTvBrIF5Y3qogKzp5MyZttd7ZqPA7\r\nG3X9b4bUwxzR59jHGctoXv1fAedcTd3/TJ5Sh/3NKX8ZWOMZfHy300Vj4YFo\r\nxzKWVyx92FxonKZtdDsofG+bkmApq+5ZGw8=\r\n=DnCo\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "c9a2ef544b1b145a40733f7619e67a2f064a42cd", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.8.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.7_1662113705020_0.9788954300993493", "host": "s3://npm-registry-packages"}}, "1.0.8": {"name": "update-browserslist-db", "version": "1.0.8", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.8", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "2f0b711327668eee01bbecddcf4a7c7954a7f8e2", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.8.tgz", "fileCount": 8, "integrity": "sha512-GHg7C4M7oJSJYW/ED/5QOJ7nL/E0lwTOBGsOorA7jqHr8ExUhPfwAotIAmdSw/LWv3SMLSNpzTAgeLG9zaZKTA==", "signatures": [{"sig": "MEQCIA1Slm6Alx4aUEF5hfWF4IrJGwqx3hnOg+OC3xP/29pOAiBFo8rzJsXAAnD2kdBKmITZo/QzMjakkteL3lUJQUJKlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH0qZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou0g/+LvV2SQV89MxQsJLWgA/HwXDcBfoLl4zZxXAdzYum16g4QuXz\r\nwEwiGStIwawAIKJWTY+gKfrkFdLdt5OAwEaCT9ZKTOP3EzdZEWDHkMJvyk09\r\nECtWFM+70JJmvOeioO5f3pPn2Zf/ikAPjfVkCquy6I0lHXDhwLjBT/tuWAju\r\nEFwCSFNoKQPOf1YU9HyJBgEY9Lo1dFy3HLJ3rPnwav7WI12wijvkcgbtS+gm\r\ndTeMPQ2WOk0X9zNO/faqM9ga/fz3Ws60H+DmJz3Dx9FyXsmsQ5oNhOj501uV\r\nqFReX9IktR5ddWJcuT3vxAqxvrSpgKigoiGn5UmfHTC9xKGESpSaj/uEJxDj\r\nWps6m8mCsmaJz2hHQpuD7PFrQ9Bg7pTTH3fb9N/hpcm5nnJxmdhuwvEhSalj\r\ntT0uHEgkiU9IFLVd5wcJj0A7S74hWs8WdJTw+fsq30Ntu7uqyfWC0wV9cPX6\r\nIsLAbE1pbqXTB3qy/f3oJd8C79GMxcda5lviEPbHh1W6wE6RNYRhB8aD4LVg\r\njv5DoYtG3MhIYAGhX99LoByb7YY2H0WvZBZFIHsmG53N+O54l4QT+v2RLWj1\r\nPho7AQZv18XMdhCAhzWl6KFZLsPtxd6BxS1G4oL9/4V0V57NXwCk3U8X1fDX\r\nFlo5AF5T5hPE7xiONDhwjrvC63JnGBqwcVc=\r\n=SbtF\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "8c84e0bbb3f4215cb46e81d11c9d5b52a565e671", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.8_1662995097152_0.48643929551889187", "host": "s3://npm-registry-packages"}}, "1.0.9": {"name": "update-browserslist-db", "version": "1.0.9", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.9", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "2924d3927367a38d5c555413a7ce138fc95fcb18", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.9.tgz", "fileCount": 8, "integrity": "sha512-/xsqn21EGVdXI3EXSum1Yckj3ZVZugqyOZQ/CxYPBD/R+ko9NSUScf8tFF4dOKY+2pvSSJA/S+5B8s4Zr4kyvg==", "signatures": [{"sig": "MEUCIQDD+kEOXY1bwZ97+eejksTwfbljCZY2z0vAgkGvhbmRZQIgenxdvkVkNSw0tOSNU2/tp5Z7ACFm9KgXVp9Q2IH+nW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13445, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIHGTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeHA/9GawJg1PtNZFDNTQjxHa5to9b+C753QWfvJo8kTapow7QMzMb\r\nPdpSr+SYUNzewzj4oB5Eo08UzH2XbwW4ac6cQBRp2KIs/8y2Ptg2E/rVNXnD\r\nkQJkdd5GWYGBV25F7ZjZy5v4q4/Oy00sV/6+eZMOPQx3Q+t3I4kIDCzPdJtZ\r\nvsIOyR1PZFigP+JtTz9w9pgUIkVUcNb/Frq2yHw8j/UcnObnFbk0r0+3L7UE\r\nMbH9EpsYvqs7Bit00vAYFvSQarjqMuKl70D+Qkq6IwU7tYT6w2FzYZ/P+I+y\r\nU/gYNhHDPsfRc5f1ElagbR3SuaVBQcPFo2xZUUBJSYYSceHtvaI3D8A2WtEZ\r\noLINsXQnimpmiqy1Bd0ojqyJWGe/RXCTDKyaNbov5ZYX1QQK+1yqDeXul48V\r\nbjE2l8nsLPs3hkxmtTJ+GmRttb+Wpi7DZtRWwja+808Xq1XPgy9cF9KSuz/+\r\nxc8gK6SXMod4qt9NvFc+tFIFSFWixfDCp5muq9BiF/Y2JeujfHD9ZNnMHS2n\r\n4GXZPhn406N0wXN4HD30yfiNkK6jQhQaGBQjI4ZHdVQRfD9CtACPppd4T7Wn\r\nUileUjdBYRowAXVq09opxzRp18qDaV3E5RRBsbaKjFZE/0riM1N4Xbfhe7tA\r\n5OIVlUg+0VcSYMMwoNb0raXBm0sm9tN1Dx0=\r\n=PHNk\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "dd50e379d141a0bf4c259a2bc07d3bc1e68705f7", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.9_1663070610804_0.4097355980665436", "host": "s3://npm-registry-packages"}}, "1.0.10": {"name": "update-browserslist-db", "version": "1.0.10", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.10", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"browserslist-lint": "cli.js"}, "dist": {"shasum": "0f54b876545726f17d00cd9a2561e6dade943ff3", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "fileCount": 8, "integrity": "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==", "signatures": [{"sig": "MEUCIE0WA8i81fDJ2wiJD0Gbj31BrBAQ8LpRdLGjJ4Jt0GdGAiEA69VYu+dpFQdGLqhfFMyfQB30n3nnHgP/pumK3ZKyx/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPbgHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrh0w//Z6zXG/lgz/mxTTYkkCLJXjAWSbeCUyRimfYHZB5j09B9YAmR\r\nQEtb1h2X7OyECjspS8+ymYE+yD9Ciz56J7smZihLGvE8BTGEO8bT57v0AILl\r\n3K9dILXznHypnCSpZbI3yFvqWaPkCdjEcKZ+ioi2/xLbuEMtRi4P3M+Ud/Ue\r\nN6Dz4s6dqxkvmh86NthMVvlYEtk9gGSBJEI41yc6VV43CfzR176zYOb3h40E\r\nq+x15wj1kvjvTOOa9PrbK2rcPS/8fGXvG039+p4F0nSiATRwV3mQVRH7enQO\r\nuVkE8EuDh91+UyhLiXkn51M5QZGT8iWVTh2GxAkljENmsupQjgUhglOwJ6A8\r\ntbyV2jBPxsoNAWgMBQT1ms3NI9H9nyYYbRoVujXgsfgRbnGbcZhUpJJL62Hz\r\nQgn2iVpijbs0D8B29G9DpXJjFXdUA3zLyJ04zDXOgyppioIRM46vThJcE6KP\r\nVO3WsbjbrxzNYmbJr0sqmQ44YoQAF/zhcmVGiKtxAf06zl7mRqXet1tCagDp\r\nk59ZHbw9MV1esJU2HPm0azgFeTPbRG2B9zIE6wA9MXVwHW+pfquEmXmE2C9D\r\njklw9920lduIoI6DUZ8TPvQxfadEXjwyOROq0W0ePBfGpnJ0KurYkrsHXZvW\r\nMsUXEn3pX6OU4pZwVQ6SoRIYXC/EUGHApPE=\r\n=u80j\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}], "gitHead": "597c6d12268e9c55368c754269872786662fe4b0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "18.10.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.10_1664989191557_0.4333030178359685", "host": "s3://npm-registry-packages"}}, "1.0.11": {"name": "update-browserslist-db", "version": "1.0.11", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.11", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "9a2a641ad2907ae7b3616506f4b977851db5b940", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "fileCount": 8, "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "signatures": [{"sig": "MEUCIQCdSSanXgf6ZeOgOtxXDWSVP9GgpT4fABQicvrnSEuDdAIgCrDYlHTmSBiPtdcyU/ogg08fbfDYk7GGSC2b1Y+B2Vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOvF0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCMQ/+ONwOaA1Ux5gOhDMd0D6U30BoLfrL6rBuAktXWYVAsmV34MHF\r\nhyIkloRIc5ZI6heJCUO1Lm5EXy5vg12nFc+9Y0vkfY3k85kqNmx3j0dQtaZY\r\nZP5qtwfSHDpBM8DT7XirnlE/5FI0OM5BKzYZzV2d+8mYTendg0NvZCdJKz1X\r\n1ydvRBWiVLE2//iRkqo3bjDeqsZcb5jCV8pQBMzRrUb3HOi2JB7U5KiPUrfy\r\nxxfP0wXuoMMPKntwuoU+aXhX9TDJ9lwFLKkltJSvEvynCUw7bhgO9fKSxatG\r\nh9/ijSb1gYZH5bq1iCAbTMH13HUgsaAu615tfjt9xC/O1qxwBHG/XJmkJCR4\r\n3aLqcxwIwLMiBa37mADReHr3npdPyXHvsOxmI5ygd2JUsgnShVv4pj0piokX\r\n2bNdfwv2hQ6YcETQbJByOvNPsH7wTSeWW7wlCJsGM36uk+PbSGd111LSBEvH\r\nZXQIiJnpnbo9nXz/yF/rpc/BnNmh/YuDi+V6NkwCKw2tGakRCXluCIKY72UK\r\nsqjo3bhtZHz4DNUiVvV6kK8oJcyjJoHmiysXZIa9mwoLeJjVD9hBemnR7Zct\r\nc+n0LtWJmeZTTT9NRxPYG98bUIDtheg+g5h6C/xmYYIUTtMgv4cw9WkoeDJk\r\ni4laYzxH3icSH26HgGX3iFy/05MM4udHT1w=\r\n=/3/P\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "7ec485b186d8ae53dd4536fc0605a5073e6ee5c0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "19.8.1", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.11_1681584500398_0.8863628228607172", "host": "s3://npm-registry-packages"}}, "1.0.12": {"name": "update-browserslist-db", "version": "1.0.12", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.12", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "868ce670ac09b4a4d4c86b608701c0dee2dc41cd", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.12.tgz", "fileCount": 8, "integrity": "sha512-tE1smlR58jxbFMtrMpFNRmsrOXlpNXss965T1CrpwuZUzUAg/TBQc94SpyhDLSzrqrJS9xTRBthnZAGcE1oaxg==", "signatures": [{"sig": "MEYCIQC+D733+wyUQvKv0qvb36neXWtwszsZWPFS3UPWwynToQIhAO6r3OBPDxyIdNJi1Wsi8Hz9017oBQagqhRCnESVkjvi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13857}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "d44d38023e5168a42cc462b8bc3488a3ddd4a917", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.12_1695237559257_0.762138335387043", "host": "s3://npm-registry-packages"}}, "1.0.13": {"name": "update-browserslist-db", "version": "1.0.13", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.13", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "3c5e4f5c083661bd38ef64b6328c26ed6c8248c4", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.13.tgz", "fileCount": 8, "integrity": "sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==", "signatures": [{"sig": "MEUCIQDSuyMg3eIXllF55C0fNo25bKfPmUGijME9loBthA1ieQIgXLgRGeVgLc3uR2YjaNT3aaNy59a0sUS3lXBmiJF4wkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13855}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "a727d276a0a0f0b6a8432221a6014dc524502ad1", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.13_1695305432459_0.2569614501441022", "host": "s3://npm-registry-packages"}}, "1.0.14": {"name": "update-browserslist-db", "version": "1.0.14", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.14", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "46a9367c323f8ade9a9dddb7f3ae7814b3a0b31c", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.14.tgz", "fileCount": 8, "integrity": "sha512-JixKH8GR2pWYshIPUg/NujK3JO7JiqEEUiNArE86NQyrgUuZeTlZQN3xuS/yiV5Kb48ev9K6RqNkaJjXsdg7Jw==", "signatures": [{"sig": "MEQCIENjr1YbJPVis6oMu17t33bwGeq3/7NYAQiar7zgt6MPAiApkn4W9jnBuJ1qLMFliViSI0X/4Dc7VRl07h2Dtv6kLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14016}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "39a5f635cefe192b36b0b4a604919056e33d350c", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "22.0.0", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.14_1714570536787_0.17920921288368086", "host": "s3://npm-registry-packages"}}, "1.0.15": {"name": "update-browserslist-db", "version": "1.0.15", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.15", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "60ed9f8cba4a728b7ecf7356f641a31e3a691d97", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.15.tgz", "fileCount": 8, "integrity": "sha512-K9HWH62x3/EalU1U6sjSZiylm9C8tgq2mSvshZpqc7QE69RaA2qjhkW2HlNA0tFpEbtyFz7HTqbSdN4MSwUodA==", "signatures": [{"sig": "MEYCIQDv09SW0F4V/+D6XZnc4tsI9Yxp+h7E6oJJ0tvK0Jm0hQIhAL2MFv6Z6jtgesQFy5hxcGb/dUmyQBlBd5NOGe0IGD2+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14327}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "eff40597e6d9b5487203f7d5a789b894675bac40", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.15_1714771066775_0.3616066604016148", "host": "s3://npm-registry-packages"}}, "1.0.16": {"name": "update-browserslist-db", "version": "1.0.16", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.0.16", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "f6d489ed90fb2f07d67784eb3f53d7891f736356", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.16.tgz", "fileCount": 9, "integrity": "sha512-KVbTxlBYlckhF5wgfyZXTWnMn7MMZjMu9XG8bPlliUOP9ThaF4QnhP8qrjrH7DRzHfSk0oQv1wToW+iA5GajEQ==", "signatures": [{"sig": "MEUCIGB85LbJpQOTrGl6rrc/nADWLvl05/BZ+vIv8wrlZ5FoAiEA5OmBbrt8HdU6BlzmmQ3AtRjzNybFJi0E5x/7XoAGBKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15515}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "f85ac35d3930d1f9c9919649becf29ba6cb06713", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.0.16_1715688042611_0.5119956006134896", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "update-browserslist-db", "version": "1.1.0", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "7ca61c0d8650766090728046e416a8cde682859e", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==", "signatures": [{"sig": "MEUCIQD7+ewOd1sJry6BjaooTgSq4gOmbNtqtZTzTntC1UMrrwIgB0yJ4pBMOfVIqqkcZMZSX4t6PwIXcjVhR8d8KL2cf3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14760}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "8b08f0071cc68f484106222d907c422756dd71bc", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.1"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.1.0_1719959881203_0.4636199592497239", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "update-browserslist-db", "version": "1.1.1", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "80846fba1d79e82547fb661f8d141e0945755fe5", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==", "signatures": [{"sig": "MEQCIAk7AYvix+/WB/VlNERz3Hot66yOnT/dM7NaWZXsoYuZAiA74bAf5l4Yek7kHirdvE1ljXvhSjRnfhxnZEbzDprmiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14751}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "85c8186412d5b9ff9bc77804f041add6934238cf", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.0"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.1.1_1727429554702_0.4513317129007315", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "update-browserslist-db", "version": "1.1.2", "keywords": ["caniuse", "browsers", "target"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "update-browserslist-db@1.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/browserslist/update-db#readme", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bin": {"update-browserslist-db": "cli.js"}, "dist": {"shasum": "97e9c96ab0ae7bcac08e9ae5151d26e6bc6b5580", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg==", "signatures": [{"sig": "MEUCICxiBhjnz0vRK16ELhSYvHjkKomnDvKR9tBE97CZe4ZwAiEAl+SyDgO8XHmho1fOrcsa4Pm63+kLt+BiRlU8yNWGm04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14682}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "4cc93a0474bf69971343c97f78e3b366eccf4920", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/browserslist/update-db.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "_hasShrinkwrap": false, "peerDependencies": {"browserslist": ">= 4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/update-browserslist-db_1.1.2_1736335220155_0.17666754124491524", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.3": {"name": "update-browserslist-db", "version": "1.1.3", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "keywords": ["caniuse", "browsers", "target"], "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/browserslist/update-db.git"}, "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "peerDependencies": {"browserslist": ">= 4.21.0"}, "bin": {"update-browserslist-db": "cli.js"}, "_id": "update-browserslist-db@1.1.3", "gitHead": "91ed867ed84c84685d9b788d31f7fd9390f08d81", "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "homepage": "https://github.com/browserslist/update-db#readme", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "shasum": "348377dd245216f9e7060ff50b15a1b740b75420", "tarball": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "fileCount": 8, "unpackedSize": 14770, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCym7osKh9Idt5tJisY2L2rKhOxMjqNeW3n7N3U/35XNQIgPOXFYO3OnO9h7f3SKkE27LPQX/IGdBC5u0yqF1XgINg="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/update-browserslist-db_1.1.3_1740591162778_0.3299264878336139"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-06-21T15:39:49.595Z", "modified": "2025-02-26T17:32:43.209Z", "1.0.0": "2022-06-21T15:39:49.759Z", "1.0.1": "2022-06-21T16:58:35.722Z", "1.0.2": "2022-06-21T17:36:34.765Z", "1.0.3": "2022-06-21T17:40:16.387Z", "1.0.4": "2022-06-23T13:22:01.780Z", "1.0.5": "2022-07-18T17:27:00.512Z", "1.0.6": "2022-09-01T20:17:28.779Z", "1.0.7": "2022-09-02T10:15:05.243Z", "1.0.8": "2022-09-12T15:04:57.302Z", "1.0.9": "2022-09-13T12:03:30.980Z", "1.0.10": "2022-10-05T16:59:51.694Z", "1.0.11": "2023-04-15T18:48:20.627Z", "1.0.12": "2023-09-20T19:19:19.425Z", "1.0.13": "2023-09-21T14:10:32.629Z", "1.0.14": "2024-05-01T13:35:36.985Z", "1.0.15": "2024-05-03T21:17:46.943Z", "1.0.16": "2024-05-14T12:00:42.759Z", "1.1.0": "2024-07-02T22:38:01.363Z", "1.1.1": "2024-09-27T09:32:34.873Z", "1.1.2": "2025-01-08T11:20:20.310Z", "1.1.3": "2025-02-26T17:32:43.009Z"}, "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/browserslist/update-db#readme", "keywords": ["caniuse", "browsers", "target"], "repository": {"type": "git", "url": "git+https://github.com/browserslist/update-db.git"}, "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "readme": "# Update Browserslist DB\n\n<img width=\"120\" height=\"120\" alt=\"Browserslist logo by <PERSON>\"\n     src=\"https://browsersl.ist/logo.svg\" align=\"right\">\n\nCLI tool to update `caniuse-lite` with browsers DB\nfrom [Browserslist](https://github.com/browserslist/browserslist/) config.\n\nSome queries like `last 2 versions` or `>1%` depend on actual data\nfrom `caniuse-lite`.\n\n```sh\nnpx update-browserslist-db@latest\n```\n\n<a href=\"https://evilmartians.com/?utm_source=update-browserslist-db\">\n  <img src=\"https://evilmartians.com/badges/sponsored-by-evil-martians.svg\"\n       alt=\"Sponsored by Evil Martians\" width=\"236\" height=\"54\">\n</a>\n\n## Docs\nRead full docs **[here](https://github.com/browserslist/update-db#readme)**.\n", "readmeFilename": "README.md"}