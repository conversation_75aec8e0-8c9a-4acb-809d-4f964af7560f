{"_id": "@babel/plugin-syntax-object-rest-spread", "_rev": "69-5f9401d0c2c63fbb7abca5db5827c044", "name": "@babel/plugin-syntax-object-rest-spread", "description": "Allow parsing of object rest/spread", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.4", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-008HTZG3V7pSHEBioYBGw78vMDWo0lODn2UW0gHrohsJbN8zC5OLzGK1qD9Yol5zwxtTNTjR8457YKFXtAfg/A==", "shasum": "17d80ee018666762ff5c8be7fc6321c56c39df55", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxELOW/qLJlUiu0HeGREKxQxUgW+LA+HrnyQ67B4J8rwIhAIxXZakfpTG4Dt9AMVlbwhWFugRqjgKSFtXUVNJ8FTCn"}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.4.tgz_1509388465234_0.08848231029696763"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.5", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-E7dwmPLRkewpMx4R7fOoig1WgH9uA9AEpCe83B+OJpAGxP1CmnUAcnaJzYyiN5PjsB9pSaJ4dAlLVRTup2xLOg==", "shasum": "ecb16222aad6b252b53d171bcb51f1a5c6cffca6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDD8RipS8BwSbOrP/jREJu/rpIMFvnYeOxG6u9Vdq053AiAPhGDkKiYio4l6ENR88u8y0Lf76XyWzcZf4AUxPVTIVg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.5.tgz_1509396966724_0.31019354378804564"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.31", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ININ7XXAeMrW1BOOysATWjNOIPd6OP97KV1OTZhk1clhDHPuqKhPuLmBIkXTomV+rKVBMPmOV/EalX6k+VLLtg==", "shasum": "bd0f67210b3022182dc50d155393ccec720ca039", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDji5siG1PVNPHZY7MyVVpqHZIlmGiK2OFWFG1R/10cYgIhAKDdIiCVBYLXshHbmva1Op7F5zzcfoObWiuRJkNl2M6R"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.31.tgz_1509739397424_0.9966794229112566"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.32", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xqXOiFUnziFWIGTqI+tqEU4RL6qTJqpwJh89LoOi+QksH3/YrKvIA7pHUn3Jd0z2QUeZa2XaqTKRjzLX+gmZ+g==", "shasum": "fec4d07e8f9aa5df7a2ec5c4db636a18a0f7d35b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPx3qY80hNYScqWZgv8chAxHMC2J0OZ7kN4n09KZbphQIhAJEaHuYF4gyiJRyprT4smTMpUs3S98Oqj4YBlnjBaQ3X"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.32.tgz_1510493585993_0.5818288330920041"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.33", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tOhAuTjCzZ9k1SKQELL1YS/I3O/7Vh7r++xBFVASvEv2AdHitE2P34MGY4hYQclKx0bhwr7I26s9h4WBLygSXw==", "shasum": "e0bf775cf97f2db046bb1761f20974b7d3c43ce9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBGjUq96PxKMmxNY/TGwM45PHzLbnad/HXtHupugYj0PAiEAxg06JdhFUAZd8K7p7UhjljL3v1IYUEA1O1UU9w6aS/g="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.33.tgz_1512138487585_0.925034228246659"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.34", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tnSX0iX2vkKI6Alam/3Q4m8cuzuS0HeCIgQHVMkd6aVnIeHYJOaPwjCGeJJHAOg/lPXpc5sGecGI2cu2q0abiA==", "shasum": "01883a3e6e29d842e88c54d146addd88eedbe0e8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHcahI6vi3UHakRWs5q26wHVx3Pc+sCGUxVtIAC1vDX1AiEA6ngqoaoz/BFnrQFashERZ0a1T4+FH0Dvvphe7g+frCQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.34.tgz_1512225547532_0.9074567903298885"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.35", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-iqaA0VdD6ajunwXpI92kSaxgrfP/Dqo/LQ+Q33qhfSKvLH/00WfeCfhoD3M0cLzo0tB6STrh/IUCDJAoiFTW7A==", "shasum": "da521551c5439fdd32025bd26299d99fd704df95", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoIR5YUdrvyRAq1WAUIdgl9FSf6c8cXwrTl6CWzqa/LAIgA1EeAOpk4W/5iYUSJQoZiRsb8/GSG0Yx968TtzRrPEM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.35.tgz_1513288055877_0.8983839151915163"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.36", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-m8GVq8u0zGT0qJd396tHYckY5VduRVSpVaf5XS4gVYHjLmwwMDSQl16GwFmv3yS6YUi6TO9ldBqsPNFz0nbxZQ==", "shasum": "b39d32a342dc2cc18cb973b80536e94b44653d1f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFV1BX5ytMqGQj4vGLgtxCrfHI1aVSGSx3Y4S8iC7GgBAiB0tUze7zrR38srD7y06hbbJdDU3TNv8cAY56+7H6embg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.36.tgz_1514228666077_0.21674165758304298"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.37", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-p3jG3QXmtrQ01DS/Dy3MqF2065VR6tnDirVatdx5bLKsHKHf+TPVevwxgAo0nOWx3IFCVjRyrRsWL2Z7n3bqgw==", "shasum": "52a6cac2234ff859e21a8ced4d794bbe43210561", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCP04hJk36JK2o9Hi6XF0G5z7f2xforgKf/sc4zvqXryAIgZYRnILxYxsszeM+80a0Dpo9dV/JZFPE1nOqdvaimrwM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.37.tgz_1515427343758_0.9554078795481473"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.38", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ecDBcMwYJaZQaj0xKJJpjvVS00HCqzvXFjdb2WrmTqjp9lrv/hlb7XLsSQsEtscbyyWgYasFkccjp5HVPN9Mtw==", "shasum": "a7288d08b5f3a3bf823c76f11118eb43dee74d43", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBqO1H4zjMwHPFDZQK4f8QvGyLKqrmuuZTBt2imbDfK2AiBA5nCI8iqNtcl05RJd2mTpUDvHMLm4zjk9UmPBi2sOuA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.38.tgz_1516206705183_0.7285593848209828"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.39", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b2GAhO6TTobqXqZFaxOB9kA+xFiZlH89v9iSBbGDwQATWj97ufPpaCc+xGmavM/ByzdadiZ7RZvbj+FrJ/6cdg==", "shasum": "f19f0761ccebf1d579197705e2efda36e1a45545", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdkCKqmsTWMs8yYg0QnZH0VpVPFa0xMl0zp9WxKr6o2wIhAL+RhOJXEguRFXCWM24vFq0Le7zVpz4nXz3hyAgUPxpm"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread-7.0.0-beta.39.tgz_1517344047713_0.3281521419994533"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.40", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LY96LEXC+qxuiOcoqrkrsyEUaD95gS7AQE7nZJ/lZBGG14h4cJhc+T0FYdJpVKqhqNuEqVHsJV9xfCYHI4Ksug==", "shasum": "d5e04536062e4df685c203ae48bb19bfe2cf235c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1236, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/uqrolMCq7/6yP1eUBPjiFxayhqoVfUecFqbHOGFLrAIgFggi9P5yC45gxS/WzXHNNvd8sPRzXoWdj4qEiaeHFxQ="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.40_1518453685994_0.6362326256530881"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.41", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Qo/nAlIxT01CvnvpiwNfu7PgC3MugCAg3o+6v7QGC845/Vyv/5jrJ6NmQTXEhUV9F9trs2QKU9msZ0gYnU4LYw==", "shasum": "e15b4ec0c9088f0ccd8f161c583545166a3df2c7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1471, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGDFVJBQ2gNE4LvO+NfBQ58grcCnXwtOhzaKh23G9j0iAiEA36xBOmpmvc3wCPBww1fhP+j1+PxgJ12EbkrQ/ksoKCk="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.41_1521044757883_0.9620492257314106"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.42", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Zhz6MdSpEviY3UFh/DUlrvf/Tn4wWosHXrnR52PBRtP/8ESWVaFuk57xWcBon3jJh0z5hYyRUr+D0wR7W7ZmnQ==", "shasum": "aa789865abe78a4895d4a0be9de4d34b1a1d5063", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1471, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFCbqN/ShYajDy0CSCsoWmUx0riUCp2OtSaFV7VyMrwyAiBBcb8981Ix2cXt1efywrj38rE5/IHB8420UtMXPcJUzg=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.42_1521147033136_0.8560034361126854"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.43", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qKxna2P3sMUskDZO8dQwdNs/Opu9Y4+uFyOwUVxaOIoPODIIvnePE3Pnbvk04AU8powvpzyDec3QArrk2COAZA==", "shasum": "af76c48e092b68c53d065c35fbad7f5f53e76229", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1576, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMXqbYcEOb8OlU9HPENsilxE0B592w2JYHv14OhTsL8QIhAL6btcviiQZsupQfh4zgVqsCimAqdoHD/49zvfQPqO5Y"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.43_1522687698062_0.7078025967116397"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.44", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pVk9nFH7AAmn9Zor8Rv6g/JVXFwBW4FSFV1/3W84bw9s8yc8LZ7KqrCCpcHgBACVWJYnAtoxsaf7FFcQFl/z7Q==", "shasum": "c37d271e4edf8a1b5d4623fb2917ba0f5a9da3b3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1627, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDG2S/sEZh4RR9UIxdOGYazvHQ3DvdEWk5y8kFjQ2s8jAIhAOZQb36ZV7lqpblb9JjjQAx7rtM39Q49KHJLqL2hdhCL"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.44_1522707600960_0.04819537927398554"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.45", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yksYn4YPMmyjTB0q45gzelBUFUSGBZLaZR+6C9pPf+JFvLYr1j6vNwFPwJ8RYgWVUhP22gleh0h1Xa7bJdSlkA==", "shasum": "9e4bc23941fd3ccbe9a1264ddea8c59a4b9bd21d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1HCRA9TVsSAnZWagAA9ckQAIsFwokB+x0aMRldallI\nzS3B0maxuyoV9EfXISaFpUBA6iSi78jFiu9jMbkWKiYsMZmYoUNpJJfmT7uZ\nN6AWVRP4fxL7Vx9XPCOkbZJGr4JDhaPxtrOBHmxFOVXezuXWpT6fA9p8izKL\nnha+dZL+3HAGEeFr6q4UVKndecYkaLSjmWOS3sNXIHzPTXZQ2dpKBoKdsAh6\nZuooajuXBfJ4COmzRBMk9XdLwlfAF9KFO7+WPm2FJTIQgVL+IKXaBYdsP3ms\nLE0+PGcKDnnJVrRiv8qpY+jb9JbC20V+c82p+14i/U7lPAkt363xK9zg9Xv0\nD3gRmFxBHmiu0vZ0xrxvboNrQm4zGdGrSP6swhu+GB1LLLY0Xz8tO4pl/w2B\nNX4u4mUflxl4x9sXP2YdOY3oIc/U7Kl0Gkb5mfFlV4WSqzKO4CEAFw1P18DZ\nPlybgYNZXefKwE5zBfhF/LlDIARCx0kglqhiVroqqS5ZWgcdWZvC5YDeosFR\nF8DcBdl8Wonl7D0hcl1zQIqqrZU9kcea06EZIT0z5cUfbApVJJSDNTgw8fZJ\n+5jk6UV2WzwQS14h6q5xMuMb+0Ty3WTmuU8gGO7y/FYI4bCQ8q/JimKtpzVA\nNAX5kg+9eUfmjooNwFwDkJFJmpuaUqiUXmmGhR5C4aFgmVXkTeDEBHSgVZs5\nVulN\r\n=Dyva\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC4W/xdUcUJ6mUubbSlU0bznfxrvMgyM5v/IkflhMiyoAiBmdMEBc6Qpomk8iQExJnmNnEylE0RhTkXLsOYXtsxAIQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.45_1524448583463_0.6495392173759571"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.46", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MMv6WG69jmcSLXdUeHvoev5RkuP/QuJZwCB4jXp2gtss//avs4Sns+t0VpGKTf9umhvRq44HFO6PVjVG85F+/Q==", "shasum": "03d46637f549757b2d6877b6449901698059d7d8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF7CRA9TVsSAnZWagAA6WcP/Rg77iFECUVd5iuhY0p/\nYufSkibaTj4OR9rzzzcJ6xM67vOC5I/31LveB0DZRtJ2lGgkpX3j1ip7xxwu\nSkncvaOHrK+JuU/zuRSmEkoll2lYUQrkjwhTPpdRmdEbnyrJJaFcQYoNk9oi\n1cEJ3D2Fu+tApWotldA8e9YyFYQEVlkH04pgRm217i8PFOwj5myIJW5W99PA\n9PH8LTgxNOGbgBztRqMUwqsB522eVw5gT6VA6spH9nA0cpT2/YcJyd4nFcsz\nTickBWzWd5GnSVk26piVqrqfSl6ODkTmWdHjSoWXZA/bdYCs7W1y6kD1+tWe\ntstPqjyxSU4zVv9LfcxB3Wvis8vNiqmmDtqE2E1d/8KCb7QAOoYOC85y/f75\n4gXRt54a8OV8AasJuvIWfe9a52V3QHVSQA0KkCbMb/32j1t+Hpd9PiGO3HU4\nA5cS/K1pmvf7B9hu124bAPrTDIVA52wMCkWLOTJ0q1AQIA9wlLUIl4JXQq7s\npReNTewvf55KIBEghTQt83txeNhfNai+EkTIdsH3B6NCCiX9yBNgvwfqX/GP\nRQqNuQT4nk2GZd5Boshpa6ANQGk9+EZKeblDW1pTFi20Sqy8NWJFbvqUnCS+\nhkKu++Ori26OLgXLPvX0t5PmlvpfmzPeDxv9TG1FmIJSKzq8PtjvLGDAAcjA\nBzpX\r\n=0TjC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrEnWhw0Xwzwoz29DVrVPLOWMCWqoXfPOPkrekuSkbTAIgLgX4VVMcjev0VTzS7SkxM0TnQLkIhOvbZyOxOFZuJHo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.46_1524457851470_0.006897415977507704"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.47", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UOGQCmzvNlZMQOuys7xPiTa2EjTT3xHuhUghcdJnYikqGV43obpIIaP+VDCWPvIT8g0QDIvmRWx5UefvkWXN+w==", "shasum": "21da514d94c138b2261ca09f0dec9abadce16185", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT/CRA9TVsSAnZWagAAkUUP+weKFXyHKclINJc/l59N\ngbGnZUlp2fqoociJjg08mv68bnD2O5CxHL63HV1A1E040VfKK2+CiN+sOArz\nfZakhDPocYITk2/MnuKjRYxA3vL769VxM0qhmLhe4npewRCfFnYifhMSMg89\nlbHHx+jzc61Tsg1Zad6BvPPHn98iPM3GczPe3CSprdcbtC3t1OmpWhBidlTo\ndCKzHmnYcewcpehIXKfDyQqdzkno0s092e8swzm9A0QjVu1rnktvmQFp+L9c\nOdlKr1heYgjCXJFhQuCAWIf09DCJjRYupuNWmfZ9P2GbfsojxczzJyzADaew\nMSlW5eOyFgn2ycQLF1xhOCM9/rgr1U0eq0/Nxz/5WtO35uUYZPtSHvBYyDNL\nraMY6muudqkwb9OCDRHSsRgsJeFGyv6WRwwtpswOehrUO2Ucb7C3aWuYmLHI\nsfhEaDucMeazfSQtPiQ9W0P8D5xt/TNFqX1hroioVJhecortVuGHU3scdcpC\n/EJWZacfMvulLSa01BE7rJASliRVw1qXOtbUbTj608tk1ZcDM1luU91WZnoF\nxbnOQLx+evRfQFsQPkfQjN/517p9kNjyzuQAsNHrSm+NHBL2t5z4OkLp9ew1\n18pmIQtGAM2SECxekTx2IX5qVn9ASQUH5fxEeSpK2q7B/uXMxv5QZ02TLCsL\nd9yw\r\n=MfsD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFjsB6uqewo7+wYI030ndoTafXhFU4n2jqSDNeOFFsyIAiEA5IHkDWN9kRlifNCwLWNIzJ+8wecFf5/1Uc+nPE+ZeYg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.47_1526342910576_0.12574529251380717"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.48", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xsezu/icfJxRpNxWG3AvvCyueKdpEZRVaPEISoSYPSiGqsFAaCozUu6XBTwDG2htcFcNfxHAdyar+TllJvQdxA==", "shasum": "cfb13a90ef0e3b96d7461036db0b6bb699e83b82", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDQCRA9TVsSAnZWagAAuAkP/AxunQrCcReHkF3dBTBb\nAXVG8JAple9MXpqZ5zXRnryfiiriB+Lf5RFyPDK7xs69l0xiaa2+ESO+4ny6\n/3059C7TrT81k2prHYRxQQEiJ07XmNPmwwxEa3YUcq5oR73VSerXQdYJND3o\nXddvOirPNnPcRcP6yPvgf4VbewXxAy4IBFLtsVhc0uYJZ9yZtpG2dSfLSrPY\nGxZfxHtw/R6iPAVOjpJaCgE6xQ3XvxXD/o2h0pFUZ74wdJqFTtgUQyfv2l0P\nUvPjABexyFe9BbmcnCjJaBO0XhfOsCCGePNiGcimwTlAhuY/ku3UYoQyjHIW\nHPDngFopV34z2GdMVTMXtV6fe44vPxZlsPV3bn8YHYfp5b9+EPf0Qc7hOn7b\n4NUCtTS/88KjZCegDo3EF7lXgWxcuG788eYCUsCnXNsrUMtwZysNHWpB3zfZ\n28rBMyowNXJ+FHPQbNEQX7oJSwc88/mfujAeyXOrLKNGZEkNpHvly3gjA/I6\nYM9EPq+wM/HSsa3AKREX6rlol3QgqUdxOjRW38eHwxgzaUpIVnC4AT3EPTA3\nj3p0CDiwAF1wDpFScV/3Zg4EGcmmyxVbC3hvdRrzC7zq1YWu+n9HmFxjNlmY\nZ30ZdlQuDlYAHCBpBOIkWge7Z4e+v2EfAehWsFdJAKTAvh+XX9g4xnff2+S0\n6kJy\r\n=e/3a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExCj1Gp+8d0KQQupJRKMHMphjEflFiEd9YZNpVMG9/oAiAoRBBsJEK6vcrW4gEm5QVQmqtil3502xX3Rhy2x746Ag=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.48_1527189711351_0.18737908289922545"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.49", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.49", "scripts": {}, "_shasum": "4784b3880823ff12e742c26b41e9857f701d639e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4784b3880823ff12e742c26b41e9857f701d639e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNbCRA9TVsSAnZWagAAPpkP/RCBHNuVwEFGasfa55Ln\nPdKM6TR59+yWvrzsUvYssWAaZTBbXHsRJnDhTfGikKeepRhPvov6QSck/ySV\n4Iq5T4yH/q3X56Td2ypWIrXtqWTI3W/uoQpoY7HVOENLPEzf3vGDHBVED4up\nn2mCR8zPT/UwKJCohYc8fA75E1n43Qn63WMUToh/3JddKG8qgk7SHmh18QLK\nPE1vxjIv8ksF6XeTEIfResAT3py3/HXeW031v/TU8KC9uM7jH9+gJIf7KOmf\nVavoYE1VYfzZGr+s3SbTkbFo2B6uQ9djNnPKXNpBOo3OhFO5u26CStL0/853\nZQb8LdyHTodhmzPsvwhhvHgSQ6xwLkjnRscKoMMXVLcoJ8kujNruJz2Gts5E\nZ+2S2OE+/Q/AhmFVwStYLYO275Hc3OV59Gzt3epBvBeo2abAhf+orz0OEi5w\nucv1YK4dHjNTKtRuQ48I+1zo5/dXRK5dHFGxq61njvHbLB+t/B873pw5ZJox\nSerhnPZcqb7tBoJpUHF2zkhOl6nZ7Mcz6IsX8CZ0LJv5RH73LMwCedlzhqkp\ndgxMsmr4nP2nt5sWe2tAT6FdHeLC5pvMVSHOPSHw8QCTtfmn1T17+Bm1Wmi2\nrk+4y7GAu806nwVEw+bSSS8bd51qcbc7HGAblzBtslQBbGmhFW74xc6nmvlh\n1K2D\r\n=R2Hf\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-YTSbWPHmIdNBknmE50y4LZKD6eIYFtOVx32l30d3QN10M2HSmqUHn9MCOuWCr9hWGXvbyFxHhm5GweGWrjYJAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF01RG1tLQDZNmrVP2Znb/ObSPPinYS50TxVgMYYJ6enAiA3P1awb8gj/9IgpXiCXAJphksfvMdNlzW0u627T4F6nA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.49_1527264091333_0.041454833470112806"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.50", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.50", "dist": {"shasum": "15f42b6ec38800d56a9efe15089dd20e47569aa4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1462, "integrity": "sha512-cUvHWgfei+kbZrNyCT38mH2n4v5BrVihuVFVREMcTgGi0yRTd9PUFD8F6ZLf7TK8inPxgUJ8rk14KKQZT2VtjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICP24YQr1cpMWjz/dlrI979YQMFMY28B9qmcb/D2Q5yDAiAVXfOC/izn7JIHrbcrx2/iWbDs4AScIwPAaY6h6S7kOQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.50_1528832828809_0.2744896851881846"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.51", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.51", "dist": {"shasum": "6d57a119c1f064c458e45bad45bef0a83ed10c00", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1476, "integrity": "sha512-621ne3cAqpGU/LuXKKEk5LihJ9jCdJN9PYSk4n0Oa7JE+HL+23BVc8KtGYo+M2l2spHEVShNBqeCNVs5OBH6Xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHz8eygmQSL8zv63s8VzGXDEActNg5CyP3SSjEie9EpKAiAUPg9HnmBafFJ/ppqhwXTCQd4R4z4EOAjA+Be5lCjFyg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.51_1528838380518_0.22249632698117372"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.52", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.52", "dist": {"shasum": "6729807874ea6cd9fd2104c4662637724441524e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1475, "integrity": "sha512-0blxUnr8XP/JpTBQQ1Uzh9daMuU+aacTd2JpIhC3z/EpryKFhetdbfxc08yvIdIg5YLovdApr11D6ep7NHuf2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnbX1iCFBUOpsk4ypwMRACcj+yzUBaiJYlSb5EyFtCuwIhAIgQv278NfET+BeqFpdrfr07mBmwed4dDOVtf5MeKOVB"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.52_1530838760513_0.27832302561372724"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.53", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.53", "dist": {"shasum": "9dbd768c3f109f02b24fba17365969fa25eb458c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1475, "integrity": "sha512-gL+x1C5FRC9fHboiHfCdGVrLvW0L9G8fOo0zEDWPpojlbqbhVBb4YFlxTyMK2j1qFgmoTj7G27u2ENB8UmRCBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXxY5FnERxZVR9znsm8SE/AdAlbgiQ1pLZcBXHF5k85gIgI6IyuOCbZ715tQ0fzaCI8CgF0LYpjEMNDzGfWJS1AEM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.53_1531316410610_0.2062099126140693"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.54", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.54", "dist": {"shasum": "e0f445612081ab573e2535adbabc7b710d17940c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1475, "integrity": "sha512-APvCVUK6snum9Xh12mwu/F6MFJcNthmAQiq4Fp5wmVr3spc737YXMZroE/JGR9uAtsrLJ0OBkrlex1upboa3PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwYfRd0qjONDC6JFGjhMJYSXCFNvRUFiA4OEO7r/zkswIhAKNietQR/2n6trWJfQly6FJqJ0gCy9Kn3Ge/4AkHPsdG"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.54_1531764001435_0.00515892959330122"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.55", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.55", "dist": {"shasum": "990ea47e790d7d9a9d28469c6bcc15f580bf19e9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1475, "integrity": "sha512-0IaviVd1KCCIEYmYl5GT65gKFCxOwP4KqvejtNEVdeEpZCoxSmrEjTqKz8zIXRTiKkt9QvrocHPj4WXR3vzJtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEV69l+26cYuqgXaFgeqnz/aWLbovmOo8cxb+NJjPWllAiBpvX3QuAjWRBYmtRAUMNezzpRlCFT8VlHd6f+iXRS7wQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.55_1532815627283_0.4246883690144656"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-beta.56", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-beta.56", "dist": {"shasum": "ebcb1406eaadb1307df02b2ebbc91fd069729f73", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-beta.56.tgz", "integrity": "sha512-rDqe3TN5cZaUg4zi3Kzfq5qySS6IcEs19WE7GHlmelgQ1QXy9d/tsPEAWHZTLrG4mjbbEFJZdLvAi+LSGdhJAQ==", "fileCount": 5, "unpackedSize": 1475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuwCRA9TVsSAnZWagAAZjkQAJtUnFGOldm0VgboIT6t\nkDqeQQQFtnGL9BJkXaoHjZEfT9hrtF013OsSgsxpRGEkL0GOriMWFjEaLi5o\nJluG+HSwBw6pbpJe+ZV3OUzXaE7sQFuYkTpLNcEnAA1B7hDIbY8sD/9pPdil\nnOldMarL+OaEn6oVBUGColoQUKV2HTK3JxdCmYTXpWbLkr6Axn9A4SybbOB4\nq3eQbqvIvVL1rAL0hO2sFvu/+pq6CC1QES5sENnMwIIolx/FQBdiR1gmXO9W\nmodvSkS7hrPn5mCLalUOu3o+YS/tOSwYH6twbSYHlSkyrZTGtxxZjJqvltHz\n4sLWnKTzTiqprm55ct6MvB72RHCzm5wa6MEkRytwj9EQdDpAlD+pu3LDnx1L\nk5UIMsPtfK6BO/FZQAGfquTE4KuP79JsrIpaNJI+r7ji9fJO9KGSZ3h5dq2T\nEdxH1WJwzb6IW4C6LDYnEIpdXb4uvUCsLM+dKgbFPCitgLaxWTWSrdKPBsBp\nLvKetIdLLZ6yoLWX6ldYXAGZkBrzLKx8ZeQvZH34+9HSgXUfDD+Ys+jCo2Vd\nCY78P80wZ/kRqIbrANj7dEpqUIGsFQ9hblz7kAk8GOQO4orSchKwiPIzSfYM\nwssFALo23TPzzFJL7NM+nGnfPoRQyJeVUbVbCbRs0HjXfXHrKhXdAB5FUQKl\nl3px\r\n=LDBa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8iAfx68EDkoryJyaeYejApUGgtTlJ42R6/nbzA75EZAiEA23r5mFAAWfxfh7/jNDjy/98ilTqu+t6LhWELGrvGfjk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-beta.56_1533344688316_0.24950788502664834"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-rc.0", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-rc.0", "dist": {"shasum": "6f0c4160045057dd9019d9f03fd4c5ddfc86ab11", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-rc.0.tgz", "integrity": "sha512-kl0R+PI0nhTe6/aDAOc5Lt8NZtxhlTgWp0yqrbpon1w9kjXaQlXSkqW2Gw7jTUO+Ktn6oHEtVFAs9v7tkUPYlw==", "fileCount": 5, "unpackedSize": 1466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGR9CRA9TVsSAnZWagAA4AAQAJpOflkbbIrLqQZaHkNy\nAm7qocUdc5WhPzZs0Z0CMArfGPT61ZpmlI5sgcOy5O74JEH4fMbhKIB07T/v\nozmzjiFRgjclaFRu48vujEJyha46/GRWO0hteqOHPpFpQzl8BB1oLLFJjp5I\nYfnuD2EYKk1F8via0V8eEGCLFjXZNTla6VUTf/yjrWu8ZF/0oURqrKHJv+uO\n/2xTwxbTIJyzJtAQchFbdskSiarQQmgJcxcw1fJu0U3iYi6+JC7GvtGpfCGp\npsLQeqJxChvYUA7eY4LSB96RIPZVlmmcCQlM3qy2B591ZkM9C6rD5ZZCC5dI\nVduED+5dCPRtLKRLWljD/T80tj5aBGQbwEOSrrX853ikWRtUeitkmG+6VB2i\nokjSgkjqa24JCXYRHCMgciyrhc6uh6s6Kw3FPIbvp6F6m77nf6wG6sgrO3EX\ne5xoXNrcm6KLpsxpdTCOCqhOc1BIOIFOX4WclKP1kGsCKj9vLXcM6q98ERln\nUl/yvn7SuyC8Zg1jeM5BpZl+MzMlWgZzufoY1oNiOvRvH4A4hkOgBALXXPH7\n96o8/CZ7N1qpa0o9gD3DDqD00Vil1ShnO0b/jVxt/ZThqTumJTEJjZNzUj9g\nSzC2wcE4ppkhu7ym41J/NBL+nINiEbr9L2W9uxvcbJFKMj097Ge9IF2WpZYY\ncipY\r\n=YXQg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICFlWhvUxCWMri5Qs8M2oTLhbg6w55cN25JsF2J8U0SsAiAxDvJlXarqtYrAhRCgcvqw14FWtFPJYrErFUZWWOB/fQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-rc.0_1533830269609_0.12461108025785084"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-rc.1", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-rc.1", "dist": {"shasum": "42032fd87fb3b18f5686a0ab957d7f6f0db26618", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-rc.1.tgz", "integrity": "sha512-stOESgG+lc68DSFvXrqoH5dW91ZtedDoR40g9wJ1ruLahCdr9X5hVLv/ddf/g/1zzjevq59A1Q+xdUREhEnrvQ==", "fileCount": 5, "unpackedSize": 1447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8ECRA9TVsSAnZWagAA250P/0dfZB81b3FJaakRXCdS\ni8lWGOAT5lK143Df1LFrKVlp+JHVl9x5Zu1LdoTiUszdlDRqOihN07nF0xQH\nsIxzqRNHdti1fWhRbaunzAFyn9lqhl4WJxzHan6kU4z4YOdM10+gjIuRrZqa\nZ75KSUjtIMO1zgMr337Ae1LwzKoz2n2cu/rJ6bqqG9u7yidpdsxDGIP0Ug2y\ndmburM6BgmmRXrlwp9aon2OJILwxH/IAqCjjTS20Kz01dMCqtWV1lK81eIWI\nZ/ys/7uJcoWVuE3bYmopqSM1kYVoU8qd2XioQWr3qLBriDFoDBnVXP9/vKp7\nhxlo5+qY86VvnsLwMxe/jR0IJ3lu6gdSJMBlILZMrYH2L7e3Pm+p0M8W0tgE\nVSSEfGDcoTLUvsNlBOYAiXAW1V0fs0S33iGH5eVJ1t3WW9ieGjJrahg1boGy\niZZ8B/kbhsimo79dWKgRpEvERFI46u2C3ekB8eVJ4gwzsGU9f2oeTokS9gbv\npIyn2mA5Hg6xE88LEqtey3GWuNfEFdDvabbnQmeUa4X+9uq2S2LdjwijHqeg\nAW7fLO+jYCDu2PT7RDaPoBcFeQ9R5xoUsqjhjMaWKVad9B6CJr4iyShB59s9\nlkldArXD7B9RuJN9ZKB3raO7rBTBw7/6ZN1Dsd7sBwkBez6AifCDMhaTkg8d\nUd1x\r\n=guP7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGPCKgaWXTdpl59CulhFj/ikAFyxFStzYefubP1o7oTZAiBqFyNqbyTsGEFTVqTlpqzrG95sbRdDSk0v/Sxv712Xng=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-rc.1_1533845251837_0.2875081023197532"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-rc.2", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-rc.2", "dist": {"shasum": "551e2e0a8916d63b4ddf498afde649c8a7eee1b5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-rc.2.tgz", "integrity": "sha512-TcwJSyWkA5ruAuWhJvmj7N9gmiHTGAz44Rqr6Fgkf6fhdlEYcBHRQQvYEKcYyPLLpqgWCqFvfiW7HgfqkCCe3A==", "fileCount": 5, "unpackedSize": 1447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGazCRA9TVsSAnZWagAAxeYP+QFP6ptgAjrvi8SMQwSF\nhL8MJ4sjmJYbpD11Fj5FQsgUEGJ0BNuCytbPhiML7DltvPdwWsICfHOX5C/r\nzPBtfKjVfUtpNdIxlC0jPOWVaA3OANe+XG+169TEEGL+D34m/FlyFKQYYebv\n8sDZLz1vnHo2heiuTndlt0Njc3Q+TQWMzWWt/tibs7nsDRCLkmbBgOWGJEnr\nK4mvdhpw1zBYqjFI8b1vHFYqll1gVeuV+PfriircH1KFT0fnGAvGhCRvMhpj\nWVVrt/4qW83VlLvDuvPhboooYhF3l3joQOTRBKptoikwFCRe1ZL9o4bZDrc6\nJqFJRfGbQ0P+OInUvQhaSctmzp4TKTRPOEkORX6JYTFL8+t+uPRxJgFWxG0o\nZCoc2IcPHJedS2BBSjBStP1OFlvKm/1mdlU7MdcaaWIm3v6LlEST+GcEvvk7\nrIxpYm8wgY+B2tw5pX1/Jb1QnZf1ylJ8xYvzyiYX30b7f1XvoLsrLP36bo1A\nJZ+L2P03KYZB0C03Bup5WcbWr+e25MWatZDjbnl0UhXsQJ0dk13Kg6X1A6kO\nV0mPU0drJqt3xOPuIOP4MobgTNWXNLYa0Bv3FNiTmVKXqqskTYdsBznpS5dT\nMIJTE3/OqwMtPV79hhefbHV5RByBVu0cMBffWZsDUbqa719KdDDAiHZlNE1I\nT6j2\r\n=sDL2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDjmkQlpn4XrBRY0+tf4AjasNPt79uoYY3+kvMv3KepYAiEA+ZxWykpg2k1z+8IAWblYNecCQNnb29R/dd4PlrFrzIc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-rc.2_1534879410999_0.902624681376305"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-rc.3", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-rc.3", "dist": {"shasum": "4458bb8b61849a81de4a90f98f4cb4f87d1d95c5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-rc.3.tgz", "integrity": "sha512-8DPa55UeIT0RU4CYb5M3YDdJWscA7oIbBsqgJ8Oe7qDl2spc6PtNmOUY71Pr5cbyH/6KTqexF5BI7p77eTiujg==", "fileCount": 6, "unpackedSize": 2546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElfCRA9TVsSAnZWagAATLIQAIM4MJy2QhtuCXIysfTJ\nCZQidb3MYbVsJb1C+w8AoeXjq5VSXbY+GrQh8L9xE1xkB/RddpqNIE3k6oNV\nWP+ZX+VlMgrHqMcJ1zt3zYkVWBnFLOOrL+Efxmv+JNVrK9SxFotZvEDMhrs8\n5fki9NpX6wFo6a8YH2Y0S0iz65ud++7aFf8IgLdu6q58BLqgq4gIDlNRum9G\nIEPoGGktkMgtnd+qTF63gOAlg12jBpD6BRJVICLW7M3lwH7IHM6dWmTjaGvS\nz2qGKP/bljZQB8VCAEZjkv1GsTBtngfzFae6BwR/YcvyM72ub2uvVM/uYHU+\nAhMcgeq1VmjR9NiF3yz7fdP74R2WqpPP7vYbTzfLPK0MQYlH2lzsL51Hip8S\ntgfEHvSvPHdgL/KYAS6qyK2zar42A6a1rHNaj2MT6/IdEVtIO1Of9IcH5VXz\naGRAL+VGvxL1bHuKpj1KPSzDxoKUi8ieWh7NIvJOFjdxJZLaf9FYMraq8p0n\ni8ASHGxCR+LTaqfD+fkp9Dnx+Fd+sLVRaIOpfM93gIt/qYfmRPGMAleo5169\nKj4+GxcNSGYOjYDBIZ8kOZQ0o1mmikDydNEF0qSWSoVfw4gvBN16ayx0BW8a\n/TuVX+MpbUKq6oBd6gOcHoeFINVKT773238FjRyof43pf/aOmMon/nRzJokS\nhBid\r\n=1QiZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFqN9/PLkltDhex8qKxW00h1xeebrm15n9S851EwEBRwIgGT893YvVd0W2JSD03ca6zZv649S4kHdVruHio+JKz5w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-rc.3_1535134046889_0.7495172775823411"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0-rc.4", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0-rc.4", "dist": {"shasum": "83f588168a1374882085e1abbe10910d3fa3964c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0-rc.4.tgz", "integrity": "sha512-Oa0QrYXrf5ZWWckgM0psTWnrQiCxcieNPyyspSvbBaUqRPbPDnoJwPb2WVfm3pGIyxDsEtAg6WcXCUuS2Vge4w==", "fileCount": 6, "unpackedSize": 2548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo/CRA9TVsSAnZWagAA6oUQAIXfR73gpPuMd2HfrVct\nrBKDbag67jT13QNPNw7qo8zVZInblRCbHDmIGnH74t5hVkl0zo3oHTpM8F65\nzXYR3mZXCXFT7Z3BTN0cnGloSe8dr2NRmfmVF1LdiBLYg8n35pqhLxcpvxpU\nFfQSbVM2W5Mk35ATfSg/VIhx7cMWzk9mZ9gMZJDLMkAE5Sog3UNTJUyFMEic\n9l8xcDa8/2kKqO9nYhK6Sby3xd1j/3uBgb/mU4DGx+zBe4k1bQmFmDfW2G8s\nLREjEqV7IrLtLkIVZa/QWt1Ynpf69KQOCSKMQfEO7JeyhndEhQqhPFk90cga\nH+08lcNkglLPd/GZo1HNnsOVA/dwrNtwegLY9PY9TRkeL/qsKKCGeiXU13Wt\nZn0CbjeZjp9B0llBM+eFcpNHl6763m66H+8OizAFUg0xhTs5cCs/dF354V5t\nYtbxA0DgL3nPZlGyhrvb+CO0aqtBTm5q4uX8QCvKYbGZc8gxnKglKT1S7Kud\nl3IMu34imF3KSRGPfqp5BtmhA04MfKiotfafM3Zxv87IEFg7H+zB5uMtLfJW\nmiczV0FXb2I67Rg9GuwsvfaVVZ2V16zn0JLPoeC1ixo6BtsIoKGoUEahIdn4\n8/V/CM42NxCFb4ZkqaMQj4fwTdesfit74zfTx2skoLL4Xe0WVu7TUY3VsVqW\n4/R9\r\n=YsNA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKSqr9F+q6jkvCD+jMG4cm44yKKkCNglfNxdQGDPuFpgIgZ0bTBCj4UajKBQqHj+Ok29m/yZp/8FrsuwNQcBHtYk0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0-rc.4_1535388222484_0.21502498135006887"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.0.0", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-object-rest-spread\n\n> Allow parsing of object rest/spread\n\nSee our website [@babel/plugin-syntax-object-rest-spread](https://babeljs.io/docs/en/next/babel-plugin-syntax-object-rest-spread.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-object-rest-spread\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-object-rest-spread --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-object-rest-spread@7.0.0", "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0.tgz", "integrity": "sha512-5A0n4p6bIiVe5OvQPxBnesezsgFJdHhSs3uFSvaPdMqtsovajLZ+G2vZyvNe10EzJBWWo3AcHGKhAFUxqwp2dw==", "fileCount": 6, "unpackedSize": 2533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBWCRA9TVsSAnZWagAAb5QP/iGS15GQ2lbSnvT3YUEn\nfzlE5mi7ZnqMzuZdrWx0ikjy9OZqdHCr5gyAH3Yugxpsynv8+T6iLYrBP2Nw\nU6g+GMuydx3nAi3CeoLpN8ZJ2kBA1dx+eaJjuHQwvNUG+AYzG4n3Jk4lgF94\nRU4Cht6zgQb7ACiFvhOI08pA/J06Z5t7Wk04asjAAqLDBAJzjKAyICNkUbkv\nQMq50tLoBGHN7VYLwCuw6rlfj77XJ532xhiaaYkmjI5k2BG2d5hgX8FN8ObS\nIShMn9NtRFi4+L2GrqvGhRV+62Z2n71npVr7nOeVnOWhWm304fkzPMGLHKMT\nQCKWps1/2FS7qtW4g6S3guA/W7tUyRxecn3uEA8JoutRkZzQ0nISXDVTyGO8\njWFdjwIqZwtz+ropvQwL0fIilMo94GPt8mvNFWW4YDtXb3ZF8VIHWWNPKzcu\nEjsWDLOjGECvVXhhr/MpuTexl7u8E12ksOiBQD7efAX4vMsXrFmq7gUsjGCc\nIjdjD3nTBzMlyJsoO+nOmE94KCH3IYd4fXOT6BUeJ80Bo6ElfsFTEUD0U+nc\nFT/qugGyYpJqCr8s2nMmDLnE+0GzmmKRTkVfRoe7MpBNXg0ejn2y66aeh41z\nWE15AX4LBWYZYt8/HRForyY93rZsniHWLNLTARAeTi+mUOoXCrm/Yxsi9wmK\n+72p\r\n=PtN6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/BOyMQKjINB9l2/6/t+kU8iHtBFhv6wrAzUkNoM2ExwIgUIBZbzNh3lsYRazQUrz3mU2tHu92JUQ2yXccucW35Mg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.0.0_1535406166288_0.6191897163633457"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.2.0", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-object-rest-spread@7.2.0", "dist": {"shasum": "3b7a3e733510c57e820b9142a6579ac8b0dfad2e", "integrity": "sha512-t0JKGgqk2We+9may3t0xDdmneaXmyxq0xieYcKHxIsrJO64n1OiMWNUtc5gQK1PA0NpdCRrtZp4z+IUaKugrSA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1rCRA9TVsSAnZWagAAGeQP/1t2kWTajbJKWOZ7q1fY\nf0W94fsWEg9HA061R+AU41H6n029ekoqgJUPJeAHhYgq048SXSpQ46A5rres\nmBINLWtas0ehm5tT1Z5QjiSGY6lMbxsE6kIRNmP9w4wYNLzBprm13YU8iqyf\nhDWodGbSlAbctH2IVyZxWDYTBoSND/2dWP7TN5x9qbbR9zkfmZFCnm6A8mo/\nNv6xSYqe2L9m9yveIfZ2sX9s7jPi+wD6Yq8Q+V/8l9kCRw0Ao1t3t0G9br22\nR4HTS7gy8Bitbvk9oTNhRrlfn2zGJEZLDKlT/K6jD67UkaMyS97/G/JyFnHC\nbnu0Q7BKDMUhR4CdoT2g/sB6WoMlsk1WhZ1QJdLnaLQPfX11CZasj5CNHQ0g\ngWrWGjK2AoI/Hzr3IqLFTVRSvJIHHcpvm2QurJoG9etvOE02JDENgoK3VaE4\n6C58FhGtx8zSFdYmTkeSXclWuoDQK+jtz3AMqywMH4bpRtw2woz7teMWtKmz\nuIIQBxFLa82ynd35OUtEwklDFFp8za75g4aPZffHoXiyJjYqoifMu+57FcXv\nAr2XiflQwYXRbydKHwKvew9SJsfqSVha233/FRvq0UXnMvNJ06YaKdpXiMml\noJjhpJZaZ/T40sjTaL3oFLygWJMNhMLSu2KzRmsz0jJ+zHcBuzDcqHDEFJAW\n4lMs\r\n=d7Pk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICRAivLQEEgG3MLuU2LGzbG645f9IdRSqevODhRGovfAAiEA6DgYqYYcaTXv1u4XqGmOSrNi42m14v8qsj6wdbSa9b4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.2.0_1543863658744_0.8021646706687353"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.7.4", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-object-rest-spread@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-mObR+r+KZq0XhRVS2BrBKBpr5jqrqzlPvS9C9vuOf5ilSwzloAl7RPWLrgKdWS6IreaVrjHxTjtyqFiOisaCwg==", "shasum": "47cf220d19d6d0d7b154304701f468fc1cc6ff46", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/tCRA9TVsSAnZWagAAr/MQAIkwwECuy81F/w6UXOtD\nMNfLVivZGIysAH8AhLdp9zJWWyKt+zjOSKysyso3vTkyabKPwupNsW6rRWWy\ncD72RqwMxwgJVa6cPfLgoSU/CqD/NV+EmDitZtbFC5v53bdsA7ORcWq9aq60\naL5vew1M/7QGfQ37IZvv/yLcbeIkQota8CQSpcluHnNk0RxW22+OeBSUBu4c\nUz4FyTXRolTuh5RL4EK5k+detRoI3N0V4YpRZ5OniPbIUdVLQ5rNxQ0FvUQk\nbV2fz7I2zN4gbQZkgD1re47GF0Dw4XEO/T98UUBYMHjASZ2JviPR8yl3iKiv\n3cY0yvQUkivMebCXZHzAh5SKXMzoA6TM1AhIzycMFkNQzyv1bh5OIXp8w9M/\nZdK9xyGeAfCtcGSIiEuHw63OEZvsFhwh0av+pqf4QJLYQrwu1Y2FBngrv5iU\nsdW3I/it54FMto5bpec2bpE76e1Qt7bJyMhC6Hy5k87SKic7HRPhirrIQ017\nwYPWtdmIeHkTiHs2hctA8g+GbX9ROnz4LEEfRWrGwkUIjlQrRg6kulyZ73BW\nvDVtPeKJIGCh4DD6dVJ+Zf3emISrPl3NNo/JEL/UIBkDRr75GlNRUfLSZkIf\nUDOwDa/hTMdMibOYz2XJDUgg3Tg/UJLtDzgg0gqGJbu5XrLdxle1f8yVm+tB\nnvSq\r\n=8O1R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLX6Bw38JuDorSdtO+BG8IksDdJT4C2y2npSO8WZbieAiBRQtRUy4iAPUUWSCHpKBMjXLdG9vlFsQMD8Hj4HFzwHg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.7.4_1574465516756_0.4891575632283325"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.8.0", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-object-rest-spread@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-dt89fDlkfkTrQcy5KavMQPyF2A6tR0kYp8HAnIoQv5hO34iAUffHghP/hMGd7Gf/+uYTmLQO0ar7peX1SUWyIA==", "shasum": "9b37d580d459682364d8602494c69145b394fd4c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVTCRA9TVsSAnZWagAADB8P/0cv/D9IAyxliRw6PBHz\nBMHlekrYdSztVqin+BdeK7YeULCDF10L/C9AYLDrM4iTezafMKTNR5ptS0L4\nm9Q7jRkPbws1d646WSfv5TzEqpY7OSjslIT1yoZOJwey9SRtNSG6TCdSPPQJ\nu0Ged1H6CjuRPS6r8EBoe2+GTjnu2heuj1mFjENOZp7RIzfoljchL83xGavq\n5HFadaMZxMwVJof96TBbaAtI4SrIMcsjf1rhMdAGj7qSA72+raLrbSzfev2J\nHUWqIqcOd0HZ4t4VswQxybtqCG31Z3kCbMJ2pqnVs9cRhw7+E0toZXUa7fLv\n9gfcunqrqQT3ZMSABelTAPyG/bQxeW5GLG2pPlkCRByWPtNl5xE7PQGAoYcw\nfZTkeIyE3x4HwWZ3oK9YPSshDSVhaql4RvIPfMQl7G+ZASqM+LQZ+ncjQHlM\noqPkyNz/tqY+yk910txHvZnlzyr5MiJvyQJYKYXK4T6VGQf8JGOVngDo0Drq\nhw/AiDaGeqdeMaQNfuC4KwNMEXyeWuDT8tdYkQD2fq8/gcg46V0OWqDDSYqq\nR9TE7jNVYaBPczguO2azk910rOdiAwUjQ6frUn4GRncaKirtMkUW6ixpMx9m\nGsJIZBsbfhvLQ/pNU/25kftbw/qU0LN5BJGBd0e/SlY0ZO79uZe+tUiiUdjB\nCaI/\r\n=p9Uz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk1UPXWy5PVLFx0HBKbjUkQtVqD5sYxi0o/AI/dA5PmAIgBm/7EDCsNF1Ta6NsMf/YpNfVguh44T/Vzn5Ud79oF1s="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.8.0_1578788179300_0.6049495221329215"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-object-rest-spread", "version": "7.8.3", "description": "Allow parsing of object rest/spread", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-object-rest-spread@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "shasum": "60e225edcbd98a640332a2e72dd3e66f1af55871", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN2lCRA9TVsSAnZWagAAEBoP/jjBf+/Yq0mk91bjSsuT\nlmP2y9vTfX/vIzFYndbtDExQzjqAwrcnDleuyQAPUIHCwJjZstlz/zzo2HoD\nsPGLK8iuLugcFdfQBfVbhA2t67raIi2DCht5J97TeEZzmwe4TLfNWiqEjuhW\niwh0TWaS2BHo8OKTEsqV58J541IHaRcfW52mGFRw4Szfl++HN3ZyJhw2+2gw\nsMKakBV9X0fl29KsTxTgituCa7nLesa6ptuzJFAnd4q5+Ypp2Tq9xnIaoWDI\nIIyiO4/YhT7IFesgavvuS2dEUAUK0gRgyKn5xnSC+m0e0XJTwK00SRSyUjBU\nOJm8NVn432gnClaBsESt4Vde4FHK6tUdIhCCdLXTQvZ71Buk+fazhJ30+JM+\nvUenf0/64yOObqeLcc2ikscK0EWEW9yvCaMEzZGy/iO9QroEvSWURIWwTvgo\nbbP/5OpxPmg0umdjqTKaNY/DoujS7vZB/O/cMejfy2MiAJl9lE8NgiwgLTUG\nFmZA89Fi2yqVikutsiRhigJXbOtNT6ghTt3kCvLksnnIReAZIv4un3++H/9Z\nirzD4X1NAvXdidCfB/jUbzYE3BrnOr77crgZYKe/eF5KQC5g+rGfgymZIU+v\nn1zhhA9uPswmkoa2YVSMpVhSb2PLqpuz5HihY1yAaoLItolD4hFM2kdcUft2\nk9qy\r\n=l3Z4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIHDf9lWKFknJ4TFo44cVmvPF63weidAfOhkGo7hE+n86Ah8y9eYoV8whZKD3uRX8f0cf+LEboZ+3EAfY0JR6z46J"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-object-rest-spread_7.8.3_1578950053372_0.8510427699007401"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-object-rest-spread\n\n> Allow parsing of object rest/spread\n\nSee our website [@babel/plugin-syntax-object-rest-spread](https://babeljs.io/docs/en/next/babel-plugin-syntax-object-rest-spread.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-object-rest-spread\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-object-rest-spread --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:04.083Z", "created": "2017-10-30T18:34:25.289Z", "7.0.0-beta.4": "2017-10-30T18:34:25.289Z", "7.0.0-beta.5": "2017-10-30T20:56:06.797Z", "7.0.0-beta.31": "2017-11-03T20:03:17.496Z", "7.0.0-beta.32": "2017-11-12T13:33:06.879Z", "7.0.0-beta.33": "2017-12-01T14:28:07.667Z", "7.0.0-beta.34": "2017-12-02T14:39:07.621Z", "7.0.0-beta.35": "2017-12-14T21:47:35.944Z", "7.0.0-beta.36": "2017-12-25T19:04:26.981Z", "7.0.0-beta.37": "2018-01-08T16:02:23.838Z", "7.0.0-beta.38": "2018-01-17T16:31:45.244Z", "7.0.0-beta.39": "2018-01-30T20:27:27.764Z", "7.0.0-beta.40": "2018-02-12T16:41:26.035Z", "7.0.0-beta.41": "2018-03-14T16:25:58.007Z", "7.0.0-beta.42": "2018-03-15T20:50:33.176Z", "7.0.0-beta.43": "2018-04-02T16:48:18.104Z", "7.0.0-beta.44": "2018-04-02T22:20:01.039Z", "7.0.0-beta.45": "2018-04-23T01:56:23.524Z", "7.0.0-beta.46": "2018-04-23T04:30:51.546Z", "7.0.0-beta.47": "2018-05-15T00:08:30.623Z", "7.0.0-beta.48": "2018-05-24T19:21:51.417Z", "7.0.0-beta.49": "2018-05-25T16:01:31.389Z", "7.0.0-beta.50": "2018-06-12T19:47:09.027Z", "7.0.0-beta.51": "2018-06-12T21:19:40.573Z", "7.0.0-beta.52": "2018-07-06T00:59:20.615Z", "7.0.0-beta.53": "2018-07-11T13:40:10.679Z", "7.0.0-beta.54": "2018-07-16T18:00:01.505Z", "7.0.0-beta.55": "2018-07-28T22:07:07.347Z", "7.0.0-beta.56": "2018-08-04T01:04:48.416Z", "7.0.0-rc.0": "2018-08-09T15:57:49.666Z", "7.0.0-rc.1": "2018-08-09T20:07:31.905Z", "7.0.0-rc.2": "2018-08-21T19:23:31.085Z", "7.0.0-rc.3": "2018-08-24T18:07:26.954Z", "7.0.0-rc.4": "2018-08-27T16:43:42.546Z", "7.0.0": "2018-08-27T21:42:46.374Z", "7.2.0": "2018-12-03T19:00:58.893Z", "7.7.4": "2019-11-22T23:31:56.935Z", "7.8.0": "2020-01-12T00:16:19.442Z", "7.8.3": "2020-01-13T21:14:13.517Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "license": "MIT", "readmeFilename": "README.md"}