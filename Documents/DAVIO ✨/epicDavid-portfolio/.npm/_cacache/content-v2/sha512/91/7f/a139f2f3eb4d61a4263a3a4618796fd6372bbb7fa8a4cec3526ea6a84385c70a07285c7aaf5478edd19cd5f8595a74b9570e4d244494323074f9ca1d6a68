{"_id": "dlv", "_rev": "13-ea0a3a5b13f438804d30117617461b05", "name": "dlv", "description": "Safely get a dot-notated property within an object.", "dist-tags": {"latest": "1.1.3"}, "versions": {"1.0.0": {"name": "dlv", "version": "1.0.0", "description": "Safely get a dot-notated property within an object.", "main": "index.js", "scripts": {"test": "node test"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "f98e516343303da1147c8674367f00c1cae58fa3", "_id": "dlv@1.0.0", "_shasum": "3f27ce3ce4aa6fa647dbe8508a3c2d8125e96d0b", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "dist": {"shasum": "3f27ce3ce4aa6fa647dbe8508a3c2d8125e96d0b", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.0.0.tgz", "integrity": "sha512-eFAoXFTMUt7HVTcDE6Jc6NxkCYyy7ohc30caEr2L780sc2L6zLmJ6CKPWWo4ts3svDWUjvl6TY8SNKG6q65ukQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTMGUnUKHPJeorlS+qmnh3fMZ/qRy+AnprV0acW+ZMkAIgSVwcfp91wZGFAhaLKOuQz6EM/Ob9gPYXEpeNbLxKG8E="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/dlv-1.0.0.tgz_1470270193989_0.7065422770101577"}, "directories": {}}, "1.0.1": {"name": "dlv", "version": "1.0.1", "description": "Safely get a dot-notated property within an object.", "main": "dist.js", "jsnext:main": "index.js", "scripts": {"build": "rollup -f cjs --no-strict $npm_package_jsnext_main | uglifyjs -cm -o $npm_package_main", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"rollup": "^0.34.3", "uglifyjs": "^2.4.10"}, "gitHead": "6bd5bd5726800d3776c5a9257e9238346ad79241", "_id": "dlv@1.0.1", "_shasum": "26a0f66162d4526c859ed45c883534225b5e0435", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "dist": {"shasum": "26a0f66162d4526c859ed45c883534225b5e0435", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.0.1.tgz", "integrity": "sha512-Mp4Qq6AXEVYIfiv7/slAeBa03sM+nCYU+uXvb0QjT69vEk3vVLTd56EM8DOnsqeEnxHIuaAx5U/EGpHrH4tfdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqNFFxlMzRypb+5PHVwRAnaxembAR1h6X0YgOyzqhUuAiEA1dCmcIQ0pgjm9oTbKdQAXOsRX0UFYIusfsgmTOpGF7k="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/dlv-1.0.1.tgz_1470272016367_0.02268392569385469"}, "directories": {}}, "1.0.2": {"name": "dlv", "version": "1.0.2", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "jsnext:main": "index.js", "scripts": {"build": "mkdir -p dist && npm run -s build:cjs && npm run -s build:umd", "build:cjs": "rollup -i $npm_package_jsnext_main -f cjs --no-strict | uglifyjs -cm -o $npm_package_main", "build:umd": "rollup -i $npm_package_jsnext_main -n $npm_package_name -f umd --no-strict | uglifyjs -cm -o $npm_package_browser", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "license": "MIT", "devDependencies": {"rollup": "^0.34.3", "uglifyjs": "^2.4.10"}, "gitHead": "886103562e4466f31f0d491cd8932720542d98d9", "bugs": {"url": "https://github.com/developit/dlv/issues"}, "homepage": "https://github.com/developit/dlv#readme", "_id": "dlv@1.0.2", "_shasum": "83c9209ced992af8489b688ac8aa207f2530cd1a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "dist": {"shasum": "83c9209ced992af8489b688ac8aa207f2530cd1a", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.0.2.tgz", "integrity": "sha512-Gxp3+Cn0NVukoMvmQnNSDVROyc2S9CVlfe2mto4HijP+rD2IrgUvHlQxEPCIH4dTWWcQFweK7CHN2z1X9MbGEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESkfnldI7h1W10Cwu8pCvBClm8AS9dEXMTfZzDje2WNAiAE4mWs1D72js7kqDkTnM1vUAJutgvHCUetFot3laj8/g=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/dlv-1.0.2.tgz_1470279985686_0.9652429856359959"}, "directories": {}}, "1.1.0": {"name": "dlv", "version": "1.1.0", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "jsnext:main": "index.js", "scripts": {"build": "mkdir -p dist && npm run -s build:cjs && npm run -s build:umd", "build:cjs": "rollup -i $npm_package_jsnext_main -f cjs --no-strict | uglifyjs -cm -o $npm_package_main", "build:umd": "rollup -i $npm_package_jsnext_main -n $npm_package_name -f umd --no-strict | uglifyjs -cm -o $npm_package_browser", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "license": "MIT", "devDependencies": {"rollup": "^0.34.3", "uglifyjs": "^2.4.10"}, "gitHead": "465f3b5c57c72a924c235ead52635262fb009fc2", "bugs": {"url": "https://github.com/developit/dlv/issues"}, "homepage": "https://github.com/developit/dlv#readme", "_id": "dlv@1.1.0", "_shasum": "fee1a7c43f63be75f3f679e85262da5f102764a7", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "dist": {"shasum": "fee1a7c43f63be75f3f679e85262da5f102764a7", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.1.0.tgz", "integrity": "sha512-HW5R5UYbnz9Qq/OXYHl1EkZ9bngBu3yg6+zDDzr89U7RHhKGyCqER3k+Qd2yZpgmBGf8tnVAEiLHeHELgS3wng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCxDFy4kHyl0Lbp3cErNVDSgL5JBT1wOdEWN6uLw5tDAIgVIx1I/VDkOXLVLzxeQ7lbqST0eUM+FJP+M8BQqIFA0U="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/dlv-1.1.0.tgz_1485891985481_0.9088218901306391"}, "directories": {}}, "1.1.1": {"name": "dlv", "version": "1.1.1", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "module": "dist/dlv.es.js", "scripts": {"dev": "microbundle watch", "build": "microbundle", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "license": "MIT", "devDependencies": {"microbundle": "^0.3.0"}, "gitHead": "35bc1591cfa7921b598413f6778a2b4bee0f5031", "bugs": {"url": "https://github.com/developit/dlv/issues"}, "homepage": "https://github.com/developit/dlv#readme", "_id": "dlv@1.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b/kUB0D6RgRGG69h5ExsLnUAwfs5Jndfk1pU2ao7/9mVdsxpUBlkFdTkNJThXw1jrLXpUbIIg+h3um5zXi6sFA==", "shasum": "c79d96bfe659a5568001250ed2aaf653992bdd3f", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeQuRJ76qGNDJPxheXl3+7XqA14Unf7eMJgf3DO1gW3gIgYJATjtzF52ZHlznZ7/jhpyybRDONnat991h3c9QwT14="}]}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dlv-1.1.1.tgz_1515889284011_0.6087712778244168"}, "directories": {}}, "1.1.2": {"name": "dlv", "version": "1.1.2", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "module": "dist/dlv.es.js", "scripts": {"dev": "microbundle watch", "build": "microbundle", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "license": "MIT", "devDependencies": {"microbundle": "^0.4.1"}, "gitHead": "66258e3ba1f9d1b52277dd902e8e90c625e4b354", "bugs": {"url": "https://github.com/developit/dlv/issues"}, "homepage": "https://github.com/developit/dlv#readme", "_id": "dlv@1.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xxD4VSH67GbRvSGUrckvha94RD7hjgOH7rqGxiytLpkaeMvixOHFZTGFK6EkIm3T761OVHT8ABHmGkq9gXgu6Q==", "shasum": "270f6737b30d25b6657a7e962c784403f85137e5", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.1.2.tgz", "fileCount": 9, "unpackedSize": 5327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGD/hCRA9TVsSAnZWagAAdJsP/j1PhryJQ9vWDl0XDCl3\nzlO4F4Z3lhUCjt7puIK3jLnai40NI6Rhi7J8YJP829Q8Naq0SuRqb3PazbKO\nKXeCb1Q+kfmqHjQFhM9ryWIIdglD/DIFziYhARnUePAbACFBDH/RCtnfCvun\ndo6kHegyPQUzuwsSMTkb10Tbc5kd5ApwUF2yyX0pykWUiPEZyEK2ATdtsAKh\n7PMiMQabKsijSjKIM5JProDaUG2WoCS4QVmPH/mgeo9/yRr3c24jSEOwtf5C\nE4vnwzdCjFF/y5kAocyfz+zXXU8kO8MB56kvG/LXfKLunhhzOyMDf8H1HLDh\ni1QZbDT3I6kIHEYRhEvmSVU78w2paHjFCaR2oHIcxfyuF6HTv0QsN7P3YN0L\n7Q94NQVLC1B6X0iyrpU9PRDyQtWtwqVG/TpB2FUUrcgFBFzazSJP8LkzT6I2\nMSQIW7YKNcDPejCws+2Wq+xhYDZjPY+nJhZAOKZDoXMh+uD+Zkc4dfVmiW8u\n2lT91etHcgfyxhBQOtxZxi30U3xCzFmzFOersm9xugaXpcFKREn327u35H/J\nNOiJ+HjbXApM0EvzZOFI6DfWoPl+FnUKPfhde37kDts5LJwYCWi4TTS6Ctkf\ntCo0N/U8Xpc36pQN8iw2KdPniuaJI7ni252rNiHXa2BST9N52RgMoI0dxZQ6\n26Bw\r\n=Ekua\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJyAzIG8pNXad1oVhv8G4RDo9STBbjcN4bJq+BB6OGrAiBYZkMiLBxT0/wI3EfuA/UzqgUxEIK9IU/cDKlsqQbI6w=="}]}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dlv_1.1.2_1528315870656_0.8448732983003138"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "dlv", "version": "1.1.3", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "module": "dist/dlv.es.js", "scripts": {"dev": "microbundle watch", "build": "microbundle", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "license": "MIT", "devDependencies": {"microbundle": "^0.11.0"}, "gitHead": "e636db817a96e4ca4710b407163fb992748b3b80", "bugs": {"url": "https://github.com/developit/dlv/issues"}, "homepage": "https://github.com/developit/dlv#readme", "_id": "dlv@1.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==", "shasum": "5c198a8a11453596e751494d49874bc7732f2e79", "tarball": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz", "fileCount": 9, "unpackedSize": 5802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5UsfCRA9TVsSAnZWagAArEMP/A5e6b9uwg27f4djhXDm\nMElDlGhS5IayiNzWleFfR0OdR7twgvzy07iqKMOXlOpi8twnig7dvpLtgQbW\n5G2dRnS5CT+BW4VMrkUgxl4BOOhMEQoU/Jskp1/EK7TP2NdZuKt5IWCRnpBg\ne4xNnkcax04G9PcVbcRVH/booVL3RafrlkSKHqRuiUCu9DUiTODo5U1/KkSA\n/kfcBSeBY8YCyBMkRC/vX9W0AFW+9CRVmT1Mw7jf2RId5KOoQ0LaVYV7E9o2\n3WBfgq/dw5+HVqUZxYHsA6yI5J1kfKr+zMMc/izgwBALVbYsvn4gHdlKsgaU\nqWvMCzqKmXyXLFGu6Xkrd18ctRjSJDSXulNEMVHfXWMA2aUaiRJtqXMlexKT\nis+5XQSr2FfgT44qOSIBgT7pjRW/j+5VTiZfP/RuM8LLJgdvJjZzl3HrxQiC\ngmg9tZiBCEJj4N4/prFVs2yomd3atqkPZ73iFS6DSxHC46YGnu8A3o5jGgPM\nprUESpYeRg9I9fnE5U0N56VDsBzgt04I+xOsOGy5Q1zo8CZCYhi5WWk90o2P\nYrdEufpdQNjpjbob38PXfpk5KUpL3fH3gnLqeeOwVra1F+lxljT2YktSmG8+\nHluYElq8do0SC2MmSi2ObWhpmAyaBk/UnSBNvZ/0TWQAEA5KDmE2taEWiWPH\n2uQf\r\n=oMCz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfGq8XT9onC48GeK9SJS/JYP/Lm7Icy3FHOx7Ewx0iyAIgEUVeonbj9sQ4rg7CC1Rb66ANvGPv7kUw/H9AHvoEhME="}]}, "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dlv_1.1.3_1558530846846_0.17059745612482824"}, "_hasShrinkwrap": false}}, "readme": "# `dlv(obj, keypath)` [![NPM](https://img.shields.io/npm/v/dlv.svg)](https://npmjs.com/package/dlv) [![Build](https://travis-ci.org/developit/dlv.svg?branch=master)](https://travis-ci.org/developit/dlv)\n\n> Safely get a dot-notated path within a nested object, with ability to return a default if the full key path does not exist or the value is undefined\n\n\n### Why?\n\nSmallest possible implementation: only **130 bytes.**\n\nYou could write this yourself, but then you'd have to write [tests].\n\nSupports ES Modules, CommonJS and globals.\n\n\n### Installation\n\n`npm install --save dlv`\n\n\n### Usage\n\n`delve(object, keypath, [default])`\n\n```js\nimport delve from 'dlv';\n\nlet obj = {\n\ta: {\n\t\tb: {\n\t\t\tc: 1,\n\t\t\td: undefined,\n\t\t\te: null\n\t\t}\n\t}\n};\n\n//use string dot notation for keys\ndelve(obj, 'a.b.c') === 1;\n\n//or use an array key\ndelve(obj, ['a', 'b', 'c']) === 1;\n\ndelve(obj, 'a.b') === obj.a.b;\n\n//returns undefined if the full key path does not exist and no default is specified\ndelve(obj, 'a.b.f') === undefined;\n\n//optional third parameter for default if the full key in path is missing\ndelve(obj, 'a.b.f', 'foo') === 'foo';\n\n//or if the key exists but the value is undefined\ndelve(obj, 'a.b.d', 'foo') === 'foo';\n\n//Non-truthy defined values are still returned if they exist at the full keypath\ndelve(obj, 'a.b.e', 'foo') === null;\n\n//undefined obj or key returns undefined, unless a default is supplied\ndelve(undefined, 'a.b.c') === undefined;\ndelve(undefined, 'a.b.c', 'foo') === 'foo';\ndelve(obj, undefined, 'foo') === 'foo';\n```\n\n\n### Setter Counterparts\n\n- [dset](https://github.com/lukeed/dset) by [@lukeed](https://github.com/lukeed) is the spiritual \"set\" counterpart of `dlv` and very fast.\n- [bury](https://github.com/kalmbach/bury) by [@kalmbach](https://github.com/kalmbach) does the opposite of `dlv` and is implemented in a very similar manner.\n\n\n### License\n\n[MIT](https://oss.ninja/mit/developit/)\n\n\n[preact]: https://github.com/developit/preact\n[tests]: https://github.com/developit/dlv/blob/master/test.js\n", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "time": {"modified": "2022-06-15T05:59:57.182Z", "created": "2016-08-04T00:23:15.527Z", "1.0.0": "2016-08-04T00:23:15.527Z", "1.0.1": "2016-08-04T00:53:37.724Z", "1.0.2": "2016-08-04T03:06:27.142Z", "1.1.0": "2017-01-31T19:46:27.370Z", "1.1.1": "2018-01-14T00:21:24.083Z", "1.1.2": "2018-06-06T20:11:10.799Z", "1.1.3": "2019-05-22T13:14:06.959Z"}, "keywords": ["delve", "dot notation", "dot"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jasonformat.com"}, "license": "MIT", "readmeFilename": "README.md", "users": {"developit": true, "petershev": true, "evocateur": true}, "homepage": "https://github.com/developit/dlv#readme", "repository": {"type": "git", "url": "git+https://github.com/developit/dlv.git"}, "bugs": {"url": "https://github.com/developit/dlv/issues"}}