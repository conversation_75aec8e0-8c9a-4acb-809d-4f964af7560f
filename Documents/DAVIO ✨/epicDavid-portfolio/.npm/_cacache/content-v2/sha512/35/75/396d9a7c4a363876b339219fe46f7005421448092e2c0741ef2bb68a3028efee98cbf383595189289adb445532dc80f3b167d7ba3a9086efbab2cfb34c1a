{"_id": "tinypool", "_rev": "38-3ebaf5cb7998d7e14b26a513d13f9509", "name": "tinypool", "dist-tags": {"latest": "1.1.1"}, "versions": {"0.0.0": {"name": "tinypool", "version": "0.0.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "dd9867f6129f7fbcc5d2847c43ac82eafb7bd532", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.0.tgz", "fileCount": 8, "integrity": "sha512-vc9q4p5nhfpSFoZ1IzaMReQ2/b9n7NvSHVqmLnPgRkmmg9sglqpSIe+uq8UzZ4Hy006NmF+cronk2pjIKZM61g==", "signatures": [{"sig": "MEUCIC6NYPnrKpw2xQCqahzfA+CjemDZV+TWEv99eIBfLR6rAiEApw91rDwln2x1/O8pmbKAuTw9bkQaeQXSJfIvVcSUFDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvDRbCRA9TVsSAnZWagAAz+cP/0ORe+RG5mBkZD+80fro\nc2epNXBahiSURF9EolI6h+PF5vv86zPGuFRzESPDz+w9cpVNFi2e+9eGVUA6\nqlE8//hIREdljPyBX7zh00G3yRjiiDyMRJOmmZuvW5/HZ4OEzm1KrfdFVDIk\nXTIr3bKNCbnKeCYqwcvHoTzigd2T5fzFZeMh7KH8gDVU3mJxPm3mttctqXrI\nPckD0z7B7wyqv6sxb1yU2wCS3V02LEMsvTEOFmtkxigC9SLHTrvJcQ4N371s\ne6MGTXIP7yu8ea80MOqDyqUoLWkxxNZqB2aszEf7WoajdAGW59++7Xz6q/Yl\nZiSdPNvRsQb5A1na1Rr+LlWT2OxZPN8cxIvE/saf0vWi1BcaP/w+dqJnKw3I\nijtr62FniVhXs4YH0w3pJHKaYvNcZIlDoPjmmBA2tYzbegjN9e4FXuASCLLF\nZjY/eEXeqJ3wGFBtxL3ZG4Q68GzovX29myPd06CH/q03gWkBqqEFiOr6kqc0\nXL4l9ZbDm8w4dx+DajSAqgbSCmAQcFr0wpzb+bFDCPdFMJHva2fCbSvegUtb\nbtC5+SV7RAZBbi6XUVkWC94vNDpsRPN8ImDFG4fhnNM3Fg6KKbMF8Y4shpSD\nCFngUTZkBvy/fnyW6RuTFdJ7BMWOv86wK27IyhVZRJ8QB8brW3XmsUlKowXO\ndpEd\r\n=O05F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.0_1639724122877_0.7049932522160747", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "tinypool", "version": "0.0.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "0390bd65aa876f21be47fb1350e6b2e6b2916d63", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-/b6TM43h7VB9GM+Sz6XARrXYEILsZP8rgeBtjxxbzSPrDYCvoLR+w9GdZeeI2Jc9zP6bD5mTfOtg9tbAN+eBZQ==", "signatures": [{"sig": "MEUCIQDMf9q6rXnxi4zRtKu8p5nPzfOEuRYfsSIs9JMwgugnOQIgD86HHZQECo4jx5ToLdwKwdXvvsQ8yBQM12Gz3LdgCBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvDkTCRA9TVsSAnZWagAAN6QP/jrSz9h1rQ/xxKTIfVgs\nMYYNoci7apBXHeV+M7d0Z0ThCeN3ZSUX57qPw0cs9c1tMzknn5gfLon6ef6q\nCRvcziLVJ59B4y9JKdP+olQIFk4ElqA3TIwVABWAVm4VBm2yvvlNtl/x6PhL\nQkV+WVQzcYsMHDATre0eLwtTXxY1nr6jy9E3GWF7AOUEG1M2/GqlDOOKRwSn\n1QCLHGj2+5ZMxW5pie7jYoZ5mMqU0jTMfASGdvQVgQ2Mhr4sanq2ZdJKWJec\nnXYMCb7rt9KP1RpxmguG7EhS87BUitQ+drCLliqtFmtcGb3NX61/3IdPlFar\n80MqzEgaIOcIOns/G9BZ3RGc74xTws/t2qr2SLVKS3vP4uKqDf3AnqoJM6MQ\n4+AMf4VK1LgpM/4gq3NMzVG5oWTt/NFb3cXBOSKitz7t7iGy36RoZfTlfhxp\nb3nmE3mG/WbFhEWIRb5U0KOE/D+nBu5LAnhtc+gixrB0TkArzNSQIHYwyGUb\n7xaNdr/qLHKT98ZJuBeSFwG5tvCLDrEMfoOdSW3vLkiu7PBHneWZUxUr26j9\nISevaSqXac/P7/0zzai6I4Im3FAx4IKHsXjriHTw4pkeeyfUQrPy3yjW103I\nY5BAwK9X5nnCYkZ82S/FD+dKcPQ4tQaAeFGjiya/WiPHf509yX9ouuL6tAsX\nugpo\r\n=12t4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.1_1639725331435_0.6055163898430529", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "tinypool", "version": "0.0.2", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "d813888d873d266bebd7d072fdbd289b1e864466", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-YLiwS70MqV1ttGj89hp1NotngN2xSDxE01EmOhSl3MMazvuY4uZjBg3dMXRqZ5/SyM27TjU4WrMHtwSlqzCTIA==", "signatures": [{"sig": "MEUCIFo2vfXisN/jCAtwIGPRgnEoAr9K/Q1nT/VWegDjgOdJAiEA7ATm/Y7D53kaPhIBCCn2eS7/AhspbxKzrQpnIAjlDzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvErcCRA9TVsSAnZWagAADHsP/i+2sc3g5mklJZEO/Kxz\nBx1cH4gULqWf9VH5oS+A9WlpLTmXI6AXLc9v2ncqFDNUc5pFCl53+c+gvLe0\nKs4DI4wdQvzaaXyVkhUdwWRuMk4jBqpc+F1FJADUWq86iu2+RxvMIz09hmRF\nehAOlZ+5hpke0Fa4F79oybFWsWmC60zICoJBS6EBYgj2M3Fv3JNBRGkv/1Dr\nKB3rMFCIEZKcnSpZZPptYekbJXlKJLLwVgqoFXreCIwjQU4EIatHFaZWdfci\ndD2SEWNb+nvcsFbTfqu/QpGYG4VsitgL2HMToFGcKAa1ST68/tQLSNYH9RaO\nayWi5G5p3U/pquT54C1bux65vSwfpi8L19CKOLdj6oY2caU00ODBlGpDrBXh\n8qCnsJ43BRXw76CxOFOKA2a1t603J0tQ5DETA056dynXd4YDvbgvFDPppSZs\nhiZxn7HClVHQxZvlly9l3GyznjaZTdOsP5v4yQNai8p0TauJ+bHncKZx6QF0\nB3MZzewzpZ1MczHP6ftSR/D8q6t8r+r6oc4ucaA09CJshuWPSeuGimnquFe+\nPgJvon0jtYMg0KJ3yYyTM1wmyQdKoAC0AEDnqvbsyJwXCj+zlA2B+dM1GaV0\ngFHMOguBC0DN8Y1BqzcYoGS7n2vjPsYOWaYHbnhQMW5CYQKnNrBmLCySnwz0\nOe5F\r\n=whWz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.2_1639729883943_0.4107636161395327", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "tinypool", "version": "0.0.3", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "7f9b4b8320ebd4a2d94cace8e3b40581e85cf9e8", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-nxFpgSXY/+ZHrwHhlSyY8qzyk4N6+IyZvsaI63/zXUjg++UtCqr3gB0t/CDoCvVKC7NIzzGFEhrAXLm0l1dglA==", "signatures": [{"sig": "MEUCIGKUTTQWR6Ul0QFCHcCC4cRIKsU/4jn6ibJq6OZvFUcTAiEA0Fe+phamKj+0+1r0YRpoxUNLm8LOzYVNWE/R8OLbsls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvE0xCRA9TVsSAnZWagAAiF4P+we0d4lY1vu0Ngafv6E0\nLyTTRlnGBYKUcPoEoezuMN2WoxnwZhOexESm840ioK4uOZDABrmPzwWqCJXE\nA9427YxGDglRLAMa6A233wz1nittIOtqMupohVgTI/g894fxehz/O0uiJcG6\nak83+H0wvEkugC1ErRW9EUGqHRwrKaVRfIRhqS11nbG5qY8xFzJhUV01cMcW\nyyOGLdG5GmzTFS5PFQiwM8qn6MHsP4wUJlNPUoh8GRRy1O/rJG7KzPYVS0Ij\ng6V/SDqaxN2im+cTtp6SGfWkJTduwUEs5DddIZ5t+06s5W66a1oy9SbCnuXl\nHpa0ZtM8ZOy/MpK6/HFqbBZ0J7EnRKqY0DiFYrTNeQkf5zthp7Hy17GpgIw7\nwESVJdiLuqcV9oA0mzCWVMEo99qIEOhzgj8ncZ0DtVAnDHuhgIpNk4BUlpqM\nwjLeB0+pUoVoqvBu6vgdLjY5pzyaIFeOC4r2mXHc9Y/f3TIsX/VQNe7OdGWu\nBncMb0e/Pxk7X+zlEyvygi+SsMhXM5PXSWeErjbJPAkm4d+f5Do1/Lrklgoz\nHBn0cUz25miczksVqC539lO0xVRqgZmlIor2W3u3G6+T7YY5VXmltC1xygN5\nYX1vXTGivBE/iuRfgyHYhVH2931DGdwTOTJQUjPNtSysJxFVSmDKrycwa9ri\nI8Jc\r\n=t5fC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.3_1639730481651_0.28522973702210064", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "tinypool", "version": "0.0.4", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "4567d6fce8790dc46eef2d633022112f453a1621", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-TQkHhh1cCDsgB1S5DYZ86ljDLlSH+IQA8YkcsuFyD8J1KiQ5mQxL4JvMN7AuNN1Le94ykMdjHtRpcVHRCXonsQ==", "signatures": [{"sig": "MEYCIQCs5Dt0VwUeZnd8lc5G8wlbOkwyNWO68uQ9IkcR9jfyTAIhAMuX5y9ROD3xnucL02C1fCbAi9evSffepUXuQQWMbA9a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhweC8CRA9TVsSAnZWagAAbM8P/0QiDnjOU1uIbdbFZQUL\nHmeM8hT1e0y79wMwMkMO+u9JeCaHDbvxDyX8w1nZMEu/1p0b5HpFb/jZjac+\nVB+CFCXt7ByeihLPi3hxTbRd6ADSmMbAnJMx7NBIZkDi/OM25r1GzcgTLici\nhdXh0KHTozLxYLQt7yu55ULCg86RiYYrAv/U0zAkeWDkPMQKMq7SGtjBEfTv\nRXciE3n7NOPCefi8tkl76XlFKM33notuddKRfQM50ozTR1z5py4GbevzYlOq\nioWByLSBBfQbUEFSx5wMN2LWVP2f8W/PlOZh7cKFHLKMutLw1VSKgbaFfYIR\nKCmXz0hj6ggM8DNDAbiYQw76zP2TQwD7hob5hdL+pVHo2njbGQ/gfqWAb+s6\nKQ3Gql2LA150CsaChpL6DiKXpqHBn8KkXwDOGhOAl23n4KuUcN/XiSpjMxXr\naFFplki+pTjeX4BC2f3qkPxAZ80BhThYWqWlje61cjaMDg4huUe5AKrSpRL7\niITQ97hMFklhh3F9lcvk73RuRHFcY30PHai+l9AQMNsDvhw00WbyuvG1rP7p\nkXP3vMFvrB/gd+PBbPO0M7XYwOC3fuRjbPTY0STGiiKWk5mzpCCkrRQ7N1Mq\nvTBVowkt1dw0Q/BwKUxxPuaWjFvp9AgUuO1CzkcXDfcKBKUq/ZfDvuILBe7H\nRJbX\r\n=K6E+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.4_1640095932816_0.8890177251153626", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "tinypool", "version": "0.0.5", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "59db30019a61c2b9a3f8c9fe893c19e7573b9de3", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-5fUjvd0Dcr2a4b3gSAtg58cE0bDX9tc4zsRWEVGxhHUSN1DhITtRK9myC9wFzaZ+a/pP3bJaGIHXBqlrj6XgZg==", "signatures": [{"sig": "MEQCIFj+knJ8aB8WuEJFKKB20WUVJhZtmABIbLvvTVsUHR6vAiA5qJSxrC2cz8j1x7GbH2juEAFPQLQqMWQtdAjNKpEYLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwfJoCRA9TVsSAnZWagAAcr4P/i9SwHrxAG5Whs4q6VRH\n14XSJ80TK9aSIT+hhf52Yy4gLOeTXIhWAiZx5s53bSCSwQLpgMxTMArd8YGN\nME5xt8dUPyuDSUMppXGSYAbn5NDixGC/1vxbzyCQBCA3y+BBT5Dz2tS564hB\nF9/FdGOw1lEYIbUWrvc9FIov4L6UC3Qh/bGJCjQkb1tIrSO8r2aD9GscqmZt\nWDNEQSHb6/lM5bl4ZWtdOMyA5gDhMVYO1f2+hJa/UxVEKg21GF2D73Yu8iqg\nInH4C83fVEkau9YDjMJQzvxyhc0TMxkxiUKFwXjh6rKyjEs2EnKlMrdcYQfE\nXEJdKX1L29f9IZUCQTCXbyPRchiW7UDV1nsIC068KgMWYJ+nP5M4w+i6O3fG\nSkQouB95q3pttQwFZXNynY7ckfTh45SQcGWczztbSi1ZLwA7yLRSatm+S6pF\nL9eXuxBkj+nWroG4koATGHPoor1pK9QhxyY1tR0pTQCp8oW/LkGyB3lJJr9A\n0+rKkR6LHvceXEmD/p2Torvr3RDIH2IN+K2HEuRdODwG1M/0bi5bxOEZbkbG\niG7K0jYJzSV993t3wJwn8qFGLIsNn8rZgfJCT1vqtlYFbpDoPgIVTXIBz4OU\nUwnl6p96P7v+mn5KUm/WRGveP4UsyAqfUmXrDajJD6aouchW1OwoRxK6PgZ9\nW1tZ\r\n=YjIw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.5_1640100456831_0.2211059550053045", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "tinypool", "version": "0.0.6", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "19ca10329e7f60dc9fff90128e3357e895840405", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-E9vY6eq/Q7fUlSt69cY3y7qXNbVOFaxP+lAEDMKMObLrmO0MmEdUPyjOMgepoMDw/ps/sazl7WIQglnUrunnLg==", "signatures": [{"sig": "MEUCIQDskF76sfHRrTXV+azrTIG0gNYTIKF3EfeTyKM4nns1FwIgE6zYRe+HWOLb4IIaXngRVUB5dA7bvIgO7hCaly0afZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxZYzCRA9TVsSAnZWagAANSAP/R0MG/lFwg6UnTMGdh9A\nK8xzd56njhtEeuF5t8PHxPhQu90PL11Q04WJU7/tFhWBefVN6Ipz9u198kl+\nOv8lQj/BXA5KBplRuoAdbDkc3XhR+asIFaA48QFq/o0xtb/If6l3SzJQYSMf\nsYCdM0Sz3cWvleN8msZCJ9mhEjScxN+Z1Hb3WJCcyBwJh/e4+OyelCtTHVVj\ni+OBzd3pooAJj2DZuZWM/XL0Q8q2lqK+mbHQgADBNldY9SH3uCjs1jx+SirQ\ntopuZjKlnRtOO3s6Su7BtvA/VMUHac5jZ20sIaVSPDJl7Ma0kHqclDsHollE\n23DMJZu43KM53kszQeDz2iGDkJ6Mk2cVur2ABJVJnLx5MgRlmNeZWOcu8FVF\nNQjTcF2ZUp2B18OcQa6MnYmHihJT4uljv+thIiFAfQmWPvTCgGEaWxrtCxtf\nuS4aBxaDz4WRD/F5ImYmfRUbephoo2MtITn8w1bvwk0SrMbaF0SyLTjTIlzS\nm9Nq5jXxAlNfOYTnuSHoYdTj3I9ddceWNFkd8Ucc9LC+rNfmXtIbpHLeu1Xr\nYbZKe3tGE4bvBaY/ZgfKfR1lJGWXQrOofbSr/g3RdSpDNOPZ5Z4JctY5eqj3\n+0OoPePhSsi7hjcs/c8nX1ssoNiGDipQSWzNiiwy9/frdM/p+e4HHHXF4Xaw\nJBx2\r\n=5Nf4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "69734b39ecaf39d843bba3479193a90f152cbf3e", "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.6_1640338995614_0.6535033972166893", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "tinypool", "version": "0.0.7", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "bae83f9d66235d73bfe1d51b8f998ab52b47d93c", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-6uY3ELRzVby7Ae3WPi3MiWUW1FYtZ5Qm/Dl3EPucQsN86ySihRBcvh8+/oZ9eRksjcLYguyh7E7UEs+Ofc59qQ==", "signatures": [{"sig": "MEYCIQD2y3iiuqzZ/CxDH0IYFlMxZv6dk9cwn2SbRaSctlXe/wIhANlttv0/gCOuzNbRrHkJddrBA8o9FvPuUaXjbz71FXkW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzgG2CRA9TVsSAnZWagAAtScP/2yQOzrs5uN4C2bBj4LC\nqNik1ty2FZr08905MwyWtTo6q2kU0rJq2rx9q+Zn5df1sdku9imfj5SRrve4\nM7cbz3X285PQvhbWRSIjFSlF2eYaZ06VvDb17mVRr3Aa7cTI/JRNu21GcmE0\nylqccpYm7h5CRn39xEDHjOhM9eZoB8fdxgbnJttYtMC5CC+MsEoHLhBc9zM3\ncw0+CLY+iQrYi+lnlvT3hGE+HFCmI1dJX9ZudEdUjSRgjw17EkFQxJPO026r\nwOx1RHae1AMQyfCNrqNQtzwOzYB1ZdUQgR+O5A2v1zpEBAZzX7vtdL9tyJcG\nXz0fGe0mQvtzZ4hDDwIuSO1+H+nwyorXGMp8vdfGf+TiZT0a/btuMqdJYeoU\njBtjdiGHmnWhjbjEocx0RMDgZjfGRh6/UgGqlcHkemUl8KyE+Q4FWn9H3ZTp\nwZiVFDc0/IEOnckEw6v1uRmEPIiQwML/0wkh4T4fT9MV2D6UWk+5mYGEELEq\nwM+ebK4It3oAsdNOqryEAapvMRVM+P1xPFmmiMRC+fktGWil7Z+6JEVTub2g\nKnOtlJ9yfualXko+Pj8O2KYyORNtsuFhQuw0wN+gTHLJB4SQQaoZjxd6QJKq\norUZQi6AYVw7Y8b+5f9MtHjPKPvuhkR0+qXqUtwnU2GLQ2LwUBlnwQWjgwCv\n0nNH\r\n=W7nk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.7_1640890806564_0.8871553305581588", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "tinypool", "version": "0.0.8", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "8fb92d9154a805b7a01c44fe45e94da8b0cfc785", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-ik10kb2NRfGdfYquYHno9J6BJIE44P4P5RF2W8P0DAqR9V+Ifm5uq/wNmsm//kLq52G7YszviVBVd/17yHjD9A==", "signatures": [{"sig": "MEUCIFbTtdoIFB7R0iYylW07jyiYO4WCEmyOs1cpdN/53LmiAiEAl6gmA0pAmwvU+HSnUgwjQeUHKluQFM+D5F+7DMpytVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0zEvCRA9TVsSAnZWagAA1qoP/imW6uYN9fs3AUuM36z/\nkCLGkciUdigQ9EjanMWjTZsDVO0PpxPHbv6DWsxVCW1b4sabttCEApdydJSe\n09P3XAYpwhpdBON931nc2bj8TdhCINq40EdkJSNJlZykqKpSHO42lx6xwSuO\nGOcB+D9Jx7EXXIY3M2kOVUJl2962ihpnsCjQO55YWrZLgHrW21rCkKhK5fKn\nE/0eADp0tziRXiwC1ZmLdI/AwaVvmHcQ7AcZwAksan0xLe8xSf90mNwDy9jq\ni7sSsDOK8qOwc0YFQQGPnuKofd75vroJeTlPCJH2zODCw8A582ELMFReG1FH\n8W1JYNscFZONIWPUjZzLpkNsbRIF28QXHGlBgvsxjFBSGoZtau2Nbs/G1vuw\numcp6Rncd9dGCbSGYW+k3Rv8yGH55vWV0cU5dAryHYOEIHtvCl7KY7j2FKA7\nam3eQlWGiPlqWJFsItBWkReMwWoxPZpguTtygmAqDVdj5G1m/YbC9kDenAWC\n+yfxAm0o5THkftQNuSywF2QwwMBuwLBvVKUXkJ8N1+z8XPV2+8PkzZ7stcDH\nW11qTBXom6I5JXeHORVLk3Z5+zlFrEExeuHfOoWODCxYn+7I9qDKlmr4yPjg\nEfOBp4EC/7WTflFfRIUIxQz6L0vUoxGcLD76+CFo9i8gRKtVAP512VPEDw46\nNt+Z\r\n=UnEH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.0.8_1641230638979_0.48102187723871825", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "tinypool", "version": "0.1.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "a0023181f7a8c094453698fbc9d9bef4b3f1a263", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-eQXAsrzFBbd4kwSmxE0g64eo/l0St7iGmkRsJVKJ5awRHUyMeSHxMD9samPaWD6fCL+pufv37TEfVcnQdQXAeA==", "signatures": [{"sig": "MEUCIQDw0vBM3uVOJeh89RAJ7Lao5MPIbzeOIEYn3KfyIo8VuQIgYer69jxHwWBCCwZkF9hxMNxwf/OzufeLQ1YMAZcIqtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0zHnCRA9TVsSAnZWagAAkSQP/0U2d0aeDzZK610Xnys0\nMUiGmZuGDvJZsLpEZaaQN3J1ZR9Jf58ViGQEI9vluGsRZt6uHE4aAI+Gf1tl\nE81WKvP8NotCFAp+olYIImsjUWhStB81WORJv16TjpNADpQEl1DjKwdpWTJL\nCr46ucSHSzXYstgjhQzHBw3veZx36VR62qh7wINYKcZE0E+of6466fQZWRIx\nG1p7W4pLrUjeAAETE9M0FA5ETTglm6Vlw1E69hgqxdax/qyUd2xqmwMer67G\nLaHhZ6iBZPmgeiOHwlGcmVv1+9PsiK3My5ojAqefRiFiVzsU5MoWIWVHDdNW\nNfu9ehluQdDR4GuMt+2DScmBLmphpLXZ7D9Yx5fvBSHJuwfNZ8B/79KIVJlq\nhWNrp5C5csHpURijvo6He+F/UDT1yPM4q6v2rIOaeoHKige1Ko9TXxJmjllk\nmxMl/pIGbxBwI97ze4LpGStA0ocLXRHeo2dAfb8N5ScOAD5sAml+qdTFPmg5\nP+QBtt5KRKg+9Mt88P9lCihjBA0bWKcBgV1TQnYhx4xRCyrJDsi6TQ/91uO2\nC0zAgQNVFUO7GibK0waUDmVs3zwaEvQJgvPZU0m/bEnh4Tn5rh9Tum4BNera\nDU19I6aFrVHJyWl+kQtZvi8sMOgjWR6J/0ZvJ/lelojSKZeuXZ1t2RM9HMgl\ns7Ob\r\n=mje/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.1.0_1641230823056_0.37639214166203283", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "tinypool", "version": "0.1.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "99eaf29d030feeca2da6c1d6b33f90fc18093bc7", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-sW2fQZ2BRb/GX5v55NkHiTrbMLx0eX0xNpP+VGhOe2f7Oo04+LeClDyM19zCE/WCy7jJ8kzIJ0Ojrxj3UhN9Sg==", "signatures": [{"sig": "MEUCIA6XZSwtib5DzEUyv/7cZFNqlypNkLoDTRzk3kL5fGimAiEAnwmHW5lzQuAgbxNJOL1Ij835YfCSDM+XEeKqZyjsRFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1HqtCRA9TVsSAnZWagAAjjcP/Re1+JS7i6yH957RsArj\ni6R4F+ecAFW09FDdarnY/XK+4wTjorzEzMbp6VJJ9fw4+WusnMFF0GSfJ3c+\nxH+dEFXa4gTTmrFbIAT4h0NHAVxxQoA4EE4hsoqw0o3n+NdxRQuJjXY8GZ/N\nt+ZEHTUdiggk/kVMRMvodUYNiuP8XqOjDdAKeFDwQmTUCdNfcr46+moCdJdP\nW4Xl5Uym4fL+lCj/OWQyzso5tIlLuQIS3ngWABH0B8tcYMHuZMa9fjScqSoW\nIXMvy74awfUIuTfx/yheS8eP2whTkuFfw1wr6HIeweUDMHDlYmWPf5CfIkPx\nnFzGbe5GwWdJWSJY3ocV7LJpLklf6qKxa3WkERKitumFl/Q88ntQ90lj9VIp\nxk7h7t0Ir3S6GZnjMagcHz6aHGdme6EIbZdaEGhAlC1wilisOEEpOdRIE5Ic\nwzNcdyeq7GjANC2zLUV/Rz0SNlBBFuiNQp8Lx3X6P91Xfe7+0k7C3g2HBy8w\niZNZi9E35Qw6lSoLWkAknJSvLf4QFvDsDXYmlcgylm6zBDYbyQzit9acrPHj\nFnNS9lmLs7gT+orRPMU04+LuCCzapNxApm4cWFXGCl0bgdcT4idKDYs0YJEl\n5NaFqazOFkodTLLSNMCiLiz/vaEiNOyu9/13dIuxpmKncSzetZtGLW74CuxC\nMDC2\r\n=127U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.1.1_1641314989287_0.32641226077520624", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "tinypool", "version": "0.1.2", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "5b1d5f5bb403afac8c67000047951ce76342fda7", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-fvtYGXoui2RpeMILfkvGIgOVkzJEGediv8UJt7TxdAOY8pnvUkFg/fkvqTfXG9Acc9S17Cnn1S4osDc2164guA==", "signatures": [{"sig": "MEQCIADp7JcR3TKuZVJlu8ya+Jbc04gmWzR/pCnxuvsRiFnoAiBmw5iDlePDrqhlmkfmx+tHiE3Q4vsaApnjM+F2a0EaYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAtmSCRA9TVsSAnZWagAAJVsP/19wi502GruelMuBiJSl\nBMXTMQCbc2bYcPaKE2WF77bXbv9eCdScBiE4tiR4hqjAcQd46ytwsFMbCiP8\nvVvyxjzKO8luSmnBKDRpW+BSqlPlxmRaIRn0JmGwpVWOhgsaMvuEuzS7iKMQ\ns12XlHiRm9jdCPq5aRr/PfEZru2itmMv3du/AboDunoH6bek/02QXpt9MwWG\nALvBLEJaDcv7cQX87CxVaruDqeImTbW3lqb806YwJGNW2LlxUH0zsbZUIiiQ\nqHL68TH0N3SmRZbLN+3YqRMMI9NtRXGfuwzoZsz2ZKUOtW4iEjJwYP54sWcI\n+fiU4rGjh4iT9qrPtKhdocQvq3SfyZDGPUoViYW6IKdO1QhDhRg9/LZHRCgs\nLciZsrzmkD2oLQqi67agZhdGXNGPBbEp7m8Eodgg105QZtyYPVsG4+vazGIq\nRfDx0gmUPyPUw0kSOWHRRrrG4SFVwRHYvIjHK7hoxyjb3tpalRdcNiqvGsgK\niiIbqjj72Wjvy5IRyrwSrSWi0viJH0J3H18V9Y0iQIFh9k8IfK2YyJJTM6CW\nXsApGvs/bal/h8r9HkEbXQU+yStzMl4Y2ki4vu6MGJkx0mFsh+Nfb82Wx9tB\nay/uvVhY6RmgOpe+DCT1/f7kSNHaPDrauOH7vY7EbxodIYVHTqS5qg/tmuSs\n0B0E\r\n=9oz2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.1.2_1644353938655_0.9321422332361597", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "tinypool", "version": "0.1.3", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "b5570b364a1775fd403de5e7660b325308fee26b", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-2IfcQh7CP46XGWGGbdyO4pjcKqsmVqFAPcXfPxcPXmOWt9cYkTP9HcDmGgsfijYoAEc4z9qcpM/BaBz46Y9/CQ==", "signatures": [{"sig": "MEYCIQCUamniEddIhQyifBjQi8cTw0cenn5Gwwa+JECVEC13aAIhAKmmCGG/EeZkd5h+L+H3a58eSWfrzeIF9I3nunVFap5z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ4d0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgJg//asfXF/7L7PVsgVrt33wa1SmVEE7n3naknTqsPs5U0Wkg9T8N\r\nmyB/oxWJmVu39GZKbfEWt/yEGfeKryul0Z1QkhyhdqW8jrVvYaVBA3jH9Zeo\r\nKFcGicH7b5gaSrdxyRv5fQ1Rlp3IJ0ccpNrXckEKUi697PgQ/QosFqbfBiOa\r\nrH5pDrtySrJAVO/692FC+p8zhPplPm1oxglZ8ezsKdheFseKDm2wT5HNIfjH\r\nDKPDNuCqoUXo+Jo0qgM034OyGayLIvwH/TAxoxwf8/fKMaN3vJ80mM0R50ON\r\nBxUTGKahMqx2SC5iBNxTk8+nuSrbu5t+xmEMLrFKkct9ihXK6U4OaqpWunof\r\nB77Lrdw59Ld1LH7+KlRSyu2E01P1PkOXKlAdwwGRZ6N7tZQn5hT4zSsz+2qa\r\nI/VSLdCCSX0EMyu9PzcoVqGhPM9En19CBXP4DR8ccbQ4crLa8HRMTT3vFADr\r\nkDi7i/TJSSos9syBpHVv+YYEx2LRC+OBZzwhpbK5GH/jYw1bhrK9rPWQ1UBw\r\n/XjG6NvqYxm4YkkfzS5URn0oAZR4JeeYCx/5i9PtnDwf/iptXBdcsF6vnlbU\r\nbWP4UZI8mEEwyBPqHQBa0CFfALYXKeS6+osZTzawJo+6RTxgpAq8O1ZsOuGp\r\nLn3KK6Abh3I45a2Ai9+PZSUsj8MTSmVDsLk=\r\n=xDvu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.1.3_1650952052707_0.4703955507939579", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "tinypool", "version": "0.2.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "cc3988f5827e0418d700d0b6327193e8250d2821", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-lFrNAMh9pmj/ZDXmbnnsTiCYSmrQdPbCmWSXdHKpXs5xSMkuNBiD6knVYqgOuGg/ngs9J92vcYKsTEA+7t6eEA==", "signatures": [{"sig": "MEYCIQDaG9lhvlsf2R2zebe9bTi6KF5GFqai/RuqcN5AedsDswIhAPDvWqZLNSQ+rLKrhZiJ08w0QQq4Gx5Bx7wcOQUWWvcZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis9u0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjtRAAljt0Tk96+HjRTSH7ebSNU/hAFeRVO75ZwMt5DlxyGdNCcszL\r\n2MhVFN1sLXWoqLsZu3Lth1RCyJrLqJXImQgICrYhyV7XylnqHg8ldH2lF7cP\r\nMpzwjIh8vM/7AMEcmb1PjfZRonVAtJ5zm91vmmD0fRkQSGDvyRXVXEK/Etb9\r\nA5TRdKTVATZcgusjm+EEZQODYEK8dV1Y6CXY0pgBFNxHJi2P9DIxwSxjL7TP\r\n+9X/UeFip0X2nUx/zjtqTLauXr3baw7ijx8maRupSLqcOtHMmA5FWWeFTUHs\r\nJl+SC1rQPloC7OfKopgvYIripMiO0K7jkOlmY/MRqXsDcVSbuepro4ABuXQw\r\nGhtW5LGEtMyM599THeX0mWGpKuvodwaN/SFegXwm6sZB/TiCMVNwPfuyupu+\r\nH2f+FrZIYwWC7Oy+KScZwtHp7d5pj5Ro2YIh00kQPxDGEMLfJiQkUHzclFXG\r\nSjoL4Xb11jFo47+5p3jp2Y2nG/bcQEQlOS7c5WKi/Y+tDVSCL1ATRwhosCtW\r\n7rvC1VNDvurFJRiimqtcba+NljgWzOtXkNG6abUHmKxS2zze4YKnKaF9F1dK\r\niRR59N2uFB1IPOXQBG1yNofN0KSJMZE8xsq1EWvVTOJiw+bgnoe+ZwcWK+nV\r\nE/eNQp+XkNB4odUIdpl3PfMDR7H9fjWbjBw=\r\n=d6e0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "4534ab43ce47a1e50d5fae5439afd5de1d81565f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.2.0_1655954356095_0.5375503342224328", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "tinypool", "version": "0.2.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "7c3347514de36113f224212590de17f04fdf0078", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.2.1.tgz", "fileCount": 8, "integrity": "sha512-HFU5ZYVq3wBfhSaf8qdqGsneaqXm0FgJQpoUlJbVdHpRLzm77IneKAD3RjzJWZvIv0YpPB9S7LUW53f6BE6ZSg==", "signatures": [{"sig": "MEUCIQCkGBSe9ad+reTPD1+AnHvA+CCkS3AaQJLyuJ/DiqiR4gIgTAO6/4dPZxjnd2eeuVlGgTOBpXWT0dSiXczgAP4rGF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitW+iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppTQ/+O4PVpq+HN33AGR1j+H7XXFGP//+/7v8basoJx+o23PuNiUwv\r\nCp8HyD5QRCiQ20sPHSAyLpkDWv0pWwuae6EnOGVR32AxZLQ9hORO1ww+qzmK\r\nc8pO5dDX/M2hVi3/300wqLWoqEDpAIx4CeEdYwwDGDQxBGZ81V/WckLBjFy0\r\nBYQ9hr8Uem/m/LeAFu4h4eZ/rvm+cR/aaEbTMF7vrcZYS1P7p2V06JDZh+lx\r\nX5QT/qPAassbskBEpEtMx6TuLnNxuAhK6aJTNyGiTze0hPd2lc+P5cNEW6TL\r\n27ku+o3Go17oXvemvz0GYeV1wVRwjrQZGDOhiaFua1IRT1rjgn/LyIC9kQdN\r\ntyhX6+U/8rr3Fywdq9vPPJd5MTfqJvrnhcszn+xr7lyZ/NTocOqMAzYBkEik\r\nZSV/nLlgj8knm6Q7DpHCLKDI3POlw8kXmJ6+MNidUlgqUOD6Zl1cfODR3+H/\r\nuTXp7Q8K/rfxWzmTBTx9Agb2pyVi+1ockc+Cf5/ufQ/JoAKXyHM1SBgwCJsl\r\nI+b8aTqsGH3cQ7fwOIPhKlU2PnKETD80nUCWF6BrOpwe6EhxOgPkuDdHRCbu\r\nsimduJxP0XS8wkWzr7J1lKd4f1AHoVnUMuF3ad64ZmytzHun9GotCBYDXYp1\r\no7WslQlALAIHhle5iUy/sCu1OWhkdb8MxhA=\r\n=U+TW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "ffed11a55bc35931dfde10f6be4c7cf9c6f78a41", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.2.1_1656057762464_0.6521650410934536", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "tinypool", "version": "0.2.2", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "9b1da3a4f0c8c44c04e8af8783d9f27f1795bda2", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.2.2.tgz", "fileCount": 8, "integrity": "sha512-tp4n5OARNL3v8ntdJUyo5NsDfwvUtu8isB43USjrsQxQrADDKY6UGBkmFaw/2vNmEt8S/uSm2U5FhkiK1eAFGw==", "signatures": [{"sig": "MEQCIESRbSfRK6o21n5DEo67RxD2JsIvlFie/IwczlUTIY7AAiA17pmzURt7hvWGbr765ubF4KNifyllBFP4aip8Qdpidg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixR9KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSag/6A49i8LojI51MI9yVkUwVEOZ5kwkMrZrCnjyGxRJZGY+dBtws\r\nPduNDG00YF3wmTgY6211v2+EhwRyOEdy49gVwTOcYj2oD5LdywIJ5Dw9rOOt\r\nP8E2pxu7QSejzQ/q0LalK+juS8bLXKsjZlgQTUMhyUzkKao9PI/bwN+MdXYa\r\n1h1oaxYkagAG6OEYPIZf2QqKFdiHo1Q0JdQLgeL6XTG7622dcR5TXYXXsIrS\r\ngZJrf085ljCBhwRS7XTjW4ZCes3OSZSPMHuPiLpk1fdVNqdak14O3m5gSs7E\r\n7rSkKd1NSTJFh8/1dIcfaw6RPOnQirFxaPUhARTSEH8q37FPNnm3VwdW1fMn\r\nBYcVJdo7JwjlhJsTqsSmkrhXvSkIlKpInXa74WvdfwpNI3dkne5ALVCn+5oF\r\ncde9y8sWx+ZrCI/vDntunNXI1JZR6HejVvNfbdinS4wk2NtxbhdGbjLXbjYp\r\npFVZYywjAv/hcb3iJpWQgfQiF16oXHraFHhZffw0R1q1S0/Yx2CIwyvF+PP2\r\n0DL93X/4H7tfnuz5BgpMDTy4R//187t2Kq5SGyDZ5bCy6A2id3+DiivxbaEM\r\n4ToZ4cBiyh076BjJU+Rx0qgnz0Su/s/e9NE+0zk/BuqcWoYhqvV6HrDBa4A0\r\nYJxP91Lumc2lF1zhrZ7iaX0nIz5LDrsXyrg=\r\n=I5nJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "9783b07708fb0a21641e51a4bcb9c39321f4c1ae", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.2.2_1657085770215_0.652849696932648", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "tinypool", "version": "0.2.3", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "487cb77935e5c1b8913ae9d0e76e3b362eaf9bd4", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.2.3.tgz", "fileCount": 8, "integrity": "sha512-B<PERSON>bzsKIUzn6HlvwOJkRpl3ykim3PHHZWcfLX7dDisio8C+mXbjikKD7c8XmOBmZEKz7dME45ikTCfCMFvUf3zw==", "signatures": [{"sig": "MEUCIATVGcHVABsX/09G940cceVfKmd53e5pfAEU21svL+mIAiEAyUxjnkBsNZA80JjFVp3TxGEYZCorFE14Acsrc1btGMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0RRtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpF1Q/+JiQNzaV5SvGIEqsZksnYNtal1ctusSFs0Aa8TbL7Fafc52IE\r\nOwH88uysXBZLTjXicBGBHmD8TX9e8zMWuMYGsTsG5U+DYsh1QEWQ2swok8gM\r\nxwzPXO54aMthkbURcOhjUF9vFmigL9hvDeVJplZkMCAH+Z1C5DVKXfvb1d9r\r\nv0/lJFRCiHMMnI3A0pCYyC/EEItyuar9B0ZhtIMSn3vzVhSiPSQy1fGo7Pe/\r\n/7HvJXnjYAw4IVG2Z3chz5s+3Yl2nIVy1z72pN/F8t4hrfS5dKlozo3tFnP/\r\nIXdTezMcKaUo7cjKpkZMEM2S7DYQpV8kSLwyseB7pAneulo0DEdYaHkcQCp2\r\noIbONtqo4fDjIBmfAO1QVHpBEGzUzMAbkfxEL0ZItj3uXilncwZMpuiujKXA\r\n9QnYHkmK8ykexmS/KcFLyDYXSXMLm058v5H3AD25yrx5VZfUdpIhbcS5lWTp\r\n8WS0wjy3QQYk7Ivt12mmhXRfzZjAm2+up2ev7JsiYBLNbjJ7oB9NSTAYxxhM\r\nmVgQRTMaIfjSPG0JOPUN7fFS1VYdHlCibVy1tUZiGeynwUZ6BekiogL+UPSj\r\nYV9gOoRixcAmH6po7eU49fqgCd7QR/HpJeWeUXURqfxqD3OBJwXmtwzFlDGO\r\n73GiLKkwlx3K1UPMiBEp+gNh8dP1SqB/qr0=\r\n=2Lzi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "2c42dfe7a4bcb178b28ac520d2d4fb3da87355cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.2.3_1657869421582_0.1477293562147557", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "tinypool", "version": "0.2.4", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "4d2598c4689d1a2ce267ddf3360a9c6b3925a20c", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.2.4.tgz", "fileCount": 8, "integrity": "sha512-Vs3rhkUH6Qq1t5bqtb816oT+HeJTXfwt2cbPH17sWHIYKTotQIFPk3tf2fgqRrVyMDVOc1EnPgzIxfIulXVzwQ==", "signatures": [{"sig": "MEUCIEaT+JzETuBV0H8zIcsmlqkLoiP+ZYrmQTG661LPTICiAiEAsASApE00PR/ZpvvDC8EcStlHFWEoUl7X1G4NFulOQM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0qtcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgkQ/9Fb2TiKCk1nQ8WDCC+nv/pPultv/20cA1xqoF2TAnztJdGW7W\r\nXI1+hdLTDwVCiA5PfUkpunE4DMiBC1AwGlOAmQfWym551zTYGkyv3gsArEIV\r\nZQGraGRW9qLLOU9f3oi8N1tWT9vBKfO0BM6fmn+NmIMQL4Y+A6ZW1DkiOlgF\r\n3IaLrtWUObxBREZ2550CygRstb64aX+u+8D9nLjmnPB3uEfasxn/1ZpzfCCn\r\ndyjt4dO1XMCujYMfV2ApLxhgdwWq9SzN+35HLIt58FoEA1whgB90o6FkX5wH\r\nfffVZumUml81iVxP8bktAjEs29B1kr17X1Wsshbl0xRCNtrzNI4W4OY8frbG\r\nLGTu513NZHx1ZTpeo+8yu3H1vXp2MHdwDPM/G6AxAigJLM33dcbGaHnamXaF\r\n63aIvxFHe6ImPD21aXnpMVNk0mmI4KFuVxvPXvu6T4p6HmSpagOvjhkEME2B\r\nCRg3hgwyOCYkaZLPd8Ntc3gx9lMia0C7a7VpFN6G3KogrJBQsi8ZKKS5ElQS\r\nlCRoA74I0MVrsD4Kwmn5zlgrmx6Jr3P+azA6NqEYKjUrUr9OcF4VHU6c8vb6\r\nYXMtxuqSBw6moJPOJ+v+mHjokTXKfw1h8LkMEl+XmJi/5CvXWuKu+QJpcy2a\r\nGFn58ePxBMnCS2EiUIaImjAF9+alugeO8zw=\r\n=saxt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "e16c918dd186f7e028d33129a97764c81b665025", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.2.4_1657973596162_0.1262813926060833", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "tinypool", "version": "0.3.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.3.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "c405d8b743509fc28ea4ca358433190be654f819", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.3.0.tgz", "fileCount": 8, "integrity": "sha512-NX5KeqHOBZU6Bc0xj9Vr5Szbb1j8tUHIeD18s41aDJaPeC5QTdEhK0SpdpUrZlj2nv5cctNcSjaKNanXlfcVEQ==", "signatures": [{"sig": "MEYCIQCp44Wbne+VK1sT1TeoDlvWPOzXylpNJ5Posu87CoKNZgIhAOI1peTXmMMN2vfWbEM4hksJNcxPv4O6qA6fkbqgo26t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDuvTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmHw/+JotsJzWhyrRcpe8wFeY2GVi24qPWr10YzpZqjZJrVw2CgaCM\r\n4q/5Y8+zhb3KQtSWKwrKl6X5rN8SgJ8BDY5ZSYaLlKMo2mv+4JA15wKeGN65\r\n5NfzNdyC3qYVBSYtZrNCK9oBS0wnuDSwH7jjzf2WEdx1ha13sHRCJzeY3knE\r\n1VMrfq4odkL6yQpfSQNBpB/BszEJTVQYkHo+9UlIKRbTCzpW07MutwuK96B8\r\nGq6VyoXEiuVqdxBq0exdrBCDv64t4GlaUD8kFgAawIj+kD1b1MLARanyEtzR\r\nMoZNX4UjkGJJVAu5yJj7jJCCmtTJIYDgG+bq30rKQfh8Ey+wlaRRs/mBKoPd\r\nigyM84u4/0Cu9bm3ih9K2SLWXK1vG5XRNBwAeWowtA7EkvDaROB8AkfTEAue\r\nuaMe+xXsfibpV8Sn91N8HR72dwc8PciX08+QCaOEM5rG6bxkldASNipCZYwm\r\nj6p6nI+rdcK4VG6cIDXIbERWVVl7cKTqoAWbTbedkitdlr4Kea59rx0XO3/6\r\n7gItl8v1o/QW1pJdo9WjquL39+ojWe0N+dpH8qQDN79Nwy8o10Yq2RNC3iiU\r\nVkd1luUbvQHlNk7U0MouMtiSwhoXrtcqXIt7RdKcfHdZHvkZnfqnNHOMj/kK\r\nlL3wEzThHqUuhRMjYq38ehukak81AxkMIDk=\r\n=pHDY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "2129c6e43f589a41841fb7e0d337bde8da94a676", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.3.0_1661922259526_0.6357248171648426", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "tinypool", "version": "0.3.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.3.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "a99c2e446aba9be05d3e1cb756d6aed7af4723b6", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.3.1.tgz", "fileCount": 8, "integrity": "sha512-zLA1ZXlstbU2rlpA4CIeVaqvWq41MTWqLY3FfsAXgC8+f7Pk7zroaJQxDgxn1xNudKW6Kmj4808rPFShUlIRmQ==", "signatures": [{"sig": "MEQCIHM5ngp0vl/M7naAtjJ/eG1mlSjAHHOQilW9zUX4lQ6UAiA2ps/nutEo9hghi5RC/1Q4YfAOaa4P1ztvXQUU2YZFrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0va1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqt6g/8DOo4lot48e5MuXEKfMS3hcKsPjWW2mvRf2hwN6ta8QaFeogj\r\nxzHmNUotf3RCAbQg0KDou7aSouIZrS99DyeYtzk/hpuwUXFY6NxObBL450jo\r\n+dDxu/LJUioS7e9v/rqjr8k/s4pa5HGPRaNANo8xmLssfCDg2BHxsc114pCo\r\ntu28xod2o/eDh1qGAYsugxPDT5rXOrR6J54yxmVgKrEe+LxooLSNSMGRTObM\r\nlBL2igS3pepo7nMMV09iTPNpF6fnxuEHZWSOiXwNNtjH5TZqtrO39SXiKqE0\r\nDnlS0aEYnysAc1+MPS8zMKWf/YCtqSsDTL3+h5w9NtDvmsxQ+mz/078pXjdi\r\n8RineVcd3qS8gp3XvQPlCCNv1WW4fWLW1uyobGmFnummacMY6zzXDkBpTTSQ\r\nnCTSCGIVMvtuJqPhyicWjry12Ph+U0GKcjniYM9Wy5yvqHULTjAybQtDv1BE\r\nGCeUoR2oLJ0SZtmfdBozlmHs44NGhHvcSRhBGSguTaAlscdd3yvD0261gWVb\r\nZuHW/sG8LWCbD3TK+XMZBk075ws4te7yxK5a0oWq55x7UUiVrxVzJ8rmSzBa\r\nWnBNQXoBaGNNVh610Nya3hVlBBlvVdNESaNLku2h22GUtjQizFmXLRVGWg74\r\nPp7yprncsvCmjszKvZaUCAQOLRI/dNicaDk=\r\n=SDoG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "8c4b581634fbb6005f3516935b43baa3adce90eb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.3.1_1674770101111_0.5280508356595224", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "tinypool", "version": "0.4.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.4.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "3cf3ebd066717f9f837e8d7d31af3c127fdb5446", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.4.0.tgz", "fileCount": 8, "integrity": "sha512-2ksntHOKf893wSAH4z/+JbPpi92esw8Gn9N2deXX+B0EO92hexAVI9GIZZPx7P5aYo5KULfeOSt3kMOmSOy6uA==", "signatures": [{"sig": "MEQCIEvxiICFvOgS83yjzyXTWMc0MXRrqHbXC2GX8yRq0kU5AiBL03SXOGm/On8a0+4fgXbALqhmIJJoFRTC29aUaGI6sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFMmBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSZA//fLdkZSj21X+22sMc65q6qL4p6+r0ZtZGVyzLEU9DaAoRzDJO\r\nzdG2G33LOlo2EtN4kuRlvy6emSIT0Kv0yK2CxD7s63NXAs7mNzKGR1BGsTNx\r\nZWxmAL7hrxAAtXH6ziGLguoxdQCb1arHSNkyHIrLQo9yPo9EK4qvf44BT41n\r\nhsExN7MAgt3Mi/Dy5VdyYcEk/+mWD5YL5AVaSC+ZM1oqCxEpHOWXQ42+HWKs\r\nckTs7NZurgPhlYyzvVTXewu4rkustsn72bKxU+f42xK6kkcDbyfxrkqLJqX2\r\nFKTqEWk2BsrkxVqqHn/5GIFcBT+ET7h/CfnTC7iYSyRwvAoVSn3+OZVLfpmE\r\n1geOQjYYYTOAGjugCTYig1TSa1amC7JM4zcm3ClBktsYyHQ0gylpJ25mkKZE\r\nn1oM51NsB8U2M0e0EuyDvHTFWwSntGjVHMihtGdv/2Cumry/z6QXuzCT3HtW\r\nniICmPK4SbJpjNdxVdOH4KbXukDMYlemTkWTZIlCMeYFoS01FgF6IBFP8O7R\r\niZGhhmryb2DPYGs79NJRhUHRWUrK+LLTHD5W2SygOIdMrb7z3x63Ovp4ZVzq\r\nZBObwdMqKTqtSw3lfblHxZrT4K8tJAcsk4hS4kGoYNws88gy8UrC7zC1QFOZ\r\nAoouLvvjg0jr/qDP8l/cZJAXBzPGjwbik8g=\r\n=9nXP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "69512a11a91705fef9ca9dad0297d4f7c94f068f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.4.0_1679083905724_0.03840399187081078", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "tinypool", "version": "0.5.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.5.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "3861c3069bf71e4f1f5aa2d2e6b3aaacc278961e", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.5.0.tgz", "fileCount": 8, "integrity": "sha512-paHQtnrlS1QZYKF/GnLoOM/DN9fqaGOFbCbxzAhwniySnzl9Ebk8w73/dd34DAhe/obUbPAOldTyYXQZxnPBPQ==", "signatures": [{"sig": "MEUCIAau0ncilqiJEqTmLXp4LSKB/df+S3xaX/JJPstNMfX6AiEApvD82ovoBL6JpeKY6aXOcQeH02m0clW1nGkEA+TzrOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPgQ/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrreA/6A1vPDtHmIr0n/VqFiIPSfbb9mik9w/txQ7OHyj35DQvUUOlC\r\nsYXp5wdEO656Mk+ukYBbhiddsPSfAuqkuOfj/ChxtepuyuXOEfe1Ndqc3BOL\r\naczkPqxrKjNDJil1qLk5P+5a4GAd9SeewERxsyDg4LgI4+ds5myNrZ1MIOmt\r\nO1dJ0W+meviAtjiQBLxxI0XmMAzigIJrMgg5OHj++wqQJzZky9WmxV+YFsk3\r\nbZ6H+GjhXn3/1Dj4E0ETbvxMqsGpVq5IHL60BeYrxqQ5jJrLtKh8x/0Ywx1X\r\nt9ApDkd3VukwFpT6wYka0oyt4WFgDX8jt0djWTpJTlq8KTvRXnc4CK+Va8Tu\r\nLsuDbm8FPq7FBlusbwAn41YvYa3W4f86oabA/M+yI3GTcKxzC9KyUMJPlNXv\r\n4WXeGtXW2O81XmDRFVYf3ffj1BFngMbKP+n3dlXkRvmT0JLRTL034EMY588K\r\ndV7SKCMGAb3Nw07roH7xN10hvTmXRZEl65zMOeibNusYvRqdc/CSi2+wcAoo\r\nGCYt7DNlJkt/A3oaecIsKmyQlK+6Xv0T5wiKq7wbJvE2YrWiWpCH29HAPNVB\r\nSf8irttKOGVsVK62bocbBjWbNXzrEtwjpBjawCVaeXzAsEvcCD5+NZD6iHLW\r\nlJBFYYV2ojWdb3n1bwPK85IHfMCn8K1r68I=\r\n=7dca\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "5ebac6185c4fdb64cc4c0a36367640098b6f3ccb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.5.0_1681785919161_0.16900059143287494", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "tinypool", "version": "0.6.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.6.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "c3640b851940abe2168497bb6e43b49fafb3ba7b", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.6.0.tgz", "fileCount": 8, "integrity": "sha512-FdswUUo5SxRizcBc6b1GSuLpLjisa8N8qMyYoP3rl+bym+QauhtJP5bvZY1ytt8krKGmMLYIRl36HBZfeAoqhQ==", "signatures": [{"sig": "MEYCIQCiVGiAPidrmufsPYsEV8ve1bFmJNuOMGMXjXU4CyTgTAIhAJtUPGlxiyPrlWpbLOWTzMXzqn71v5huKAUmsGf9Oiqy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42131}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "1b6ca55257ba4f784d485c20055e6abaae937516", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.6.0_1687985030837_0.6742816703681878", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "tinypool", "version": "0.7.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.7.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/aslemammad/tinypool#readme", "bugs": {"url": "https://github.com/aslemammad/tinypool/issues"}, "dist": {"shasum": "88053cc99b4a594382af23190c609d93fddf8021", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.7.0.tgz", "fileCount": 8, "integrity": "sha512-zSYNUlYSMhJ6Zdou4cJwo/p7w5nmAH17GRfU/ui3ctvjXFErXXkruT4MWW6poDeXgCaIBlGLrfU6TbTXxyGMww==", "signatures": [{"sig": "MEYCIQDcMhE9oBP/AuQPHj20592G5pT9XS6muqhLJ+O4bniilQIhAPDR+rWzLFulvVr4UNVeCW8dwGd72JmCCVgJoIPekY2r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42772}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "6286b8126d9e418d9ff4d07df46fa54c5c8e2f87", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/aslemammad/tinypool.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.7.0_1688589970103_0.6060533382781861", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "tinypool", "version": "0.8.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.8.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "225f2ae54223f4aadddef4cf85d5c6f96100d500", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.0.tgz", "fileCount": 15, "integrity": "sha512-BkMhpw8M8y9+XBOEP57Wzbw/8IoJYtL1OvFjX+88IvwzAqVhwEV2TID0lZ1l4b5dPhjzSFQrhWdD2CLWt+oXRw==", "signatures": [{"sig": "MEYCIQD5T6iJAjWUdvqaLMMwyF1kJgURNnDzmJuM4peo9yB0tgIhAOK++E5naKwJVDuZF48bQNrPwK6ZYrRHOKeYkWc1fwLN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50868}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "c5386d6029dd185a835c19a359040c7854a2c891", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.8.0_1692194267706_0.9262263609604677", "host": "s3://npm-registry-packages"}}, "0.8.1": {"name": "tinypool", "version": "0.8.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.8.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "b6c4e4972ede3e3e5cda74a3da1679303d386b03", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.1.tgz", "fileCount": 15, "integrity": "sha512-zBTCK0cCgRROxvs9c0CGK838sPkeokNGdQVUUwHAbynHFlmyJYj825f/oRs528HaIJ97lo0pLIlDUzwN+IorWg==", "signatures": [{"sig": "MEQCICqfUUEViyTwsUtaCGxhzn74+qqC8GYjova9SQSyL7LnAiAtmVaXe3NJJoClzJ0yCVnYaC9sED+AWgmZ7zcarcw05g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51485}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "a5b666924a6dda428d38341d79404c953b2f2b8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.8.1_1694399523690_0.8755057847939431", "host": "s3://npm-registry-packages"}}, "0.8.2": {"name": "tinypool", "version": "0.8.2", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.8.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "84013b03dc69dacb322563a475d4c0a9be00f82a", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.2.tgz", "fileCount": 15, "integrity": "sha512-SUszKYe5wgsxnNOVlBYO6IC+8VGWdVGZWAqUxp3UErNBtptZvWbwyUOyzNL59zigz2rCA92QiL3wvG+JDSdJdQ==", "signatures": [{"sig": "MEYCIQDCzCbNd9mSzGhWOvrYXRAm3fAlI11so8Kd3nYDOAOgoQIhAMwxVS/K/U+0t6iS7XqOVcf/4ZN8+4qKtF0/jwTfAa1f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51552}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "6ca7bb480a9d0c54dac04eaab5e6523d300f1791", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.8.2_1705588251831_0.8462953091341501", "host": "s3://npm-registry-packages"}}, "0.8.3": {"name": "tinypool", "version": "0.8.3", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.8.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "e17d0a5315a7d425f875b05f7af653c225492d39", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.3.tgz", "fileCount": 15, "integrity": "sha512-Ud7uepAklqRH1bvwy22ynrliC7Dljz7Tm8M/0RBUW+YRa4YHhZ6e4PpgE+fu1zr/WqB1kbeuVrdfeuyIBpy4tw==", "signatures": [{"sig": "MEUCIQDDx2Q0js7syxQ3sG+WZ66viO1tK2ngyN6JP05BnAQPJAIgP8RrVPo9NU3v03LIWomc48PjDJ6iGCkUFre5uhPA7Kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51649}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "c045ecfda880202b8a1d4a581ed286c91d9b423f", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.8.3_1711389926503_0.2770892479865368", "host": "s3://npm-registry-packages"}}, "0.8.4": {"name": "tinypool", "version": "0.8.4", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.8.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "e217fe1270d941b39e98c625dcecebb1408c9aa8", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.4.tgz", "fileCount": 15, "integrity": "sha512-i11VH5gS6IFeLY3gMBQ00/MmLncVP7JLXOw1vlgkytLmJK7QnEr7NXf0LBdxfmNPAeyetukOk0bOYrJrFGjYJQ==", "signatures": [{"sig": "MEUCIC4Nc1BUzDBME5a1wV2tqU+gsjPHjq6EHEpXOfaWMwFWAiEAnwNbNtXf8IiuxPHaqt4kJ36vRI2/hnLri72ktW/VXyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51798}, "main": "./dist/esm/index.js", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "gitHead": "f86e82927371b54ba26577d818b86393ccf902c2", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.8.4_1713158128160_0.7989043952877202", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "tinypool", "version": "0.9.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@0.9.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "229528824f8cf067fba97b912b8d490df754b36e", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-0.9.0.tgz", "fileCount": 15, "integrity": "sha512-/aMLccuigz3ZZV8pv/LvOVkOzOfcKkz0V2d5JfHhXUSlp0JJ8h2lAjveUZFTKqII9L4iJh4jod5bfZxx3mditw==", "signatures": [{"sig": "MEUCIQCpp5bepjo8A/35kUrSIBcgn+Pc4HBjgD6Rt7q8L8btrwIgXy5iu9ckZ8fHmGZFFjW9naM0WgNFgO1u0otxIQNsb9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56275}, "main": "./dist/index.js", "type": "module", "_from": "file:tinypool-0.9.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"dev": "tsup --watch", "test": "vitest", "bench": "vitest bench", "build": "tsup"}, "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/f_/jbch6ql93gj29x20vl_cmvv80000gn/T/67f4811bac98743ce2798e25e090ccbf/tinypool-0.9.0.tgz", "_integrity": "sha512-/aMLccuigz3ZZV8pv/LvOVkOzOfcKkz0V2d5JfHhXUSlp0JJ8h2lAjveUZFTKqII9L4iJh4jod5bfZxx3mditw==", "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "20.13.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.0.2", "vite": "^5.2.11", "husky": "^7.0.4", "vitest": "^1.6.0", "prettier": "^2.5.1", "typescript": "^5.4.5", "@types/node": "^20.12.8", "nano-staged": "^0.5.0", "clean-publish": "^3.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tinypool_0.9.0_1715274185663_0.46310939917118255", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "tinypool", "version": "1.0.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@1.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "a68965218e04f4ad9de037d2a1cd63cda9afb238", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-1.0.0.tgz", "fileCount": 15, "integrity": "sha512-KIKExllK7jp3uvrNtvRBYBWBOAXSX8ZvoaD8T+7KB/QHIuoJW3Pmr60zucywjAlMb5TeXUkcs/MWeWLu0qvuAQ==", "signatures": [{"sig": "MEQCID+7jef9mh2O5lNzXL7eqz1C+tDkUxFfFjW60C9f277iAiBe9ezQEy+dr3t8+74MF4kMyGgqnewqmVpiFxWYljuoSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49741}, "main": "./dist/index.js", "pnpm": {"overrides": {"vitest>tinypool": "link:./"}}, "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "gitHead": "bb1c3507b0a47af63696780ba7729c07aba52ff0", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "packageManager": "pnpm@9.0.6", "_npmOperationalInternal": {"tmp": "tmp/tinypool_1.0.0_1717430883987_0.7158965395167294", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "tinypool", "version": "1.0.1", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@1.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "c64233c4fac4304e109a64340178760116dbe1fe", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-1.0.1.tgz", "fileCount": 15, "integrity": "sha512-URZYihUbRPcGv95En+sz6MfghfIc2OJ1sv/RmhWZLouPY0/8Vo80viwPvg3dlaS9fuq7fQMEfgRRK7BBZThBEA==", "signatures": [{"sig": "MEUCIGOHZBN4QATINOmnO1/WJwgY0KX+7mzrWm5HW3qBidAMAiEAqGT798HE4cmgNe8RbXsBWJGIvQO81yb28UO5MAhv26A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49910}, "main": "./dist/index.js", "pnpm": {"overrides": {"vitest>tinypool": "link:./"}}, "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f5c56e1a046e4907f06a5b3e1b7dadecb929adf", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "packageManager": "pnpm@9.0.6", "_npmOperationalInternal": {"tmp": "tmp/tinypool_1.0.1_1724048557574_0.7502459617196413", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "tinypool", "version": "1.0.2", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@1.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "706193cc532f4c100f66aa00b01c42173d9051b2", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-1.0.2.tgz", "fileCount": 15, "integrity": "sha512-al6n+QEANGFOMf/dmUMsuS5/r9B06uwlyNjZZql/zv8J7ybHCgoihBNORZCY2mzUuAnomQa2JdhyHKzZxPCrFA==", "signatures": [{"sig": "MEUCIQDPQ6SRJLV534RML8PuvbtAvaXJz65Bs1iGplJyPcSs1wIgX7uIENELL3+ucH+REwi4s+etz2Ckro3Ygd4Hqf1zMMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57335}, "main": "./dist/index.js", "type": "module", "_from": "file:tinypool-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"dev": "tsup --watch", "lint": "eslint --max-warnings=0", "test": "vitest", "bench": "vitest bench", "build": "tsup", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/f_/jbch6ql93gj29x20vl_cmvv80000gn/T/ce6bda6f624ee10cc94b260d89333d77/tinypool-1.0.2.tgz", "_integrity": "sha512-al6n+QEANGFOMf/dmUMsuS5/r9B06uwlyNjZZql/zv8J7ybHCgoihBNORZCY2mzUuAnomQa2JdhyHKzZxPCrFA==", "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^8.0.2", "vite": "^5.2.11", "eslint": "^9.4.0", "vitest": "^2.0.5", "prettier": "^3.3.2", "typescript": "^5.4.5", "@types/node": "^20.12.8", "clean-publish": "^3.4.4", "typescript-eslint": "^7.13.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/tinypool_1.0.2_1731595733681_0.8974484644523435", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "tinypool", "version": "1.1.0", "keywords": ["fast", "worker threads", "thread pool"], "license": "MIT", "_id": "tinypool@1.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinypool#readme", "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "dist": {"shasum": "4252913ec76ef8f728f2524e2118f3bef9cf23f4", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-1.1.0.tgz", "fileCount": 14, "integrity": "sha512-7CotroY9a8DKsKprEy/a14aCCm8jYVmR7aFy4fpkZM8sdpNJbKkixuNjgM50yCmip2ezc8z4N7k3oe2+rfRJCQ==", "signatures": [{"sig": "MEYCIQCECC1nww4WMDIWTm38G0dq2xpiNhOhdgsPOfd4w2aduQIhAKzie+yXxsaom032tw+P1wVY2eBeF0cszio1I0kqYxsQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44005}, "main": "./dist/index.js", "pnpm": {"overrides": {"vitest>tinypool": "link:./"}}, "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "a1d50e8f5f0a95e5e8337db4749d09022a22857e", "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinypool.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.0.6", "_npmOperationalInternal": {"tmp": "tmp/tinypool_1.1.0_1748672141263_0.2235324235414844", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.1": {"name": "tinypool", "type": "module", "version": "1.1.1", "packageManager": "pnpm@9.0.6", "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "license": "MIT", "homepage": "https://github.com/tinylibs/tinypool#readme", "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinypool.git"}, "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "keywords": ["fast", "worker threads", "thread pool"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "pnpm": {"overrides": {"vitest>tinypool": "link:./"}}, "_id": "tinypool@1.1.1", "gitHead": "565655f8c8956b3779f54671e6679b07f08ea3ee", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==", "shasum": "059f2d042bd37567fbc017d3d426bdd2a2612591", "tarball": "https://registry.npmjs.org/tinypool/-/tinypool-1.1.1.tgz", "fileCount": 14, "unpackedSize": 44149, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHf5vWXSGBlFqchk5ael0d7sGeaB6KBmxwElI5CzNG08AiBS8MFxZ1ZLgBwotK+5zV5tYcqO4fzoXehAbu5UrsXwcw=="}]}, "_npmUser": {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinypool_1.1.1_1750094264416_0.3299200271093292"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-12-17T06:55:22.876Z", "modified": "2025-06-16T17:17:44.763Z", "0.0.0": "2021-12-17T06:55:23.016Z", "0.0.1": "2021-12-17T07:15:31.585Z", "0.0.2": "2021-12-17T08:31:24.176Z", "0.0.3": "2021-12-17T08:41:21.990Z", "0.0.4": "2021-12-21T14:12:12.989Z", "0.0.5": "2021-12-21T15:27:36.979Z", "0.0.6": "2021-12-24T09:43:15.757Z", "0.0.7": "2021-12-30T19:00:06.703Z", "0.0.8": "2022-01-03T17:23:59.127Z", "0.1.0": "2022-01-03T17:27:03.222Z", "0.1.1": "2022-01-04T16:49:49.409Z", "0.1.2": "2022-02-08T20:58:58.783Z", "0.1.3": "2022-04-26T05:47:32.884Z", "0.2.0": "2022-06-23T03:19:16.257Z", "0.2.1": "2022-06-24T08:02:42.670Z", "0.2.2": "2022-07-06T05:36:10.425Z", "0.2.3": "2022-07-15T07:17:01.733Z", "0.2.4": "2022-07-16T12:13:16.370Z", "0.3.0": "2022-08-31T05:04:19.638Z", "0.3.1": "2023-01-26T21:55:01.286Z", "0.4.0": "2023-03-17T20:11:45.877Z", "0.5.0": "2023-04-18T02:45:19.343Z", "0.6.0": "2023-06-28T20:43:51.112Z", "0.7.0": "2023-07-05T20:46:10.292Z", "0.8.0": "2023-08-16T13:57:47.953Z", "0.8.1": "2023-09-11T02:32:03.947Z", "0.8.2": "2024-01-18T14:30:52.013Z", "0.8.3": "2024-03-25T18:05:26.685Z", "0.8.4": "2024-04-15T05:15:28.370Z", "0.9.0": "2024-05-09T17:03:05.882Z", "1.0.0": "2024-06-03T16:08:04.202Z", "1.0.1": "2024-08-19T06:22:37.757Z", "1.0.2": "2024-11-14T14:48:53.894Z", "1.1.0": "2025-05-31T06:15:41.493Z", "1.1.1": "2025-06-16T17:17:44.588Z"}, "bugs": {"url": "https://github.com/tinylibs/tinypool/issues"}, "license": "MIT", "homepage": "https://github.com/tinylibs/tinypool#readme", "keywords": ["fast", "worker threads", "thread pool"], "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinypool.git"}, "description": "A minimal and tiny Node.js Worker Thread Pool implementation, a fork of piscina, but with fewer features", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "a<PERSON>erk<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Tinypool - the node.js worker pool 🧵\n\n> Piscina: A fast, efficient Node.js Worker Thread Pool implementation\n\nTinypool is a fork of piscina. What we try to achieve in this library, is to eliminate some dependencies and features that our target users don't need (currently, our main user will be <PERSON><PERSON><PERSON>). Tinypool's install size (38KB) can then be smaller than Piscina's install size (6MB when Tinypool was created, Piscina has since reduced it's size to ~800KB). If you need features like [utilization](https://github.com/piscinajs/piscina#property-utilization-readonly) or OS-specific thread priority setting, [<PERSON>scina](https://github.com/piscinajs/piscina) is a better choice for you. We think that Piscina is an amazing library, and we may try to upstream some of the dependencies optimization in this fork.\n\n- ✅ Smaller install size, 38KB\n- ✅ Minimal\n- ✅ No dependencies\n- ✅ Physical cores instead of Logical cores with [physical-cpu-count](https://www.npmjs.com/package/physical-cpu-count)\n- ✅ Supports `worker_threads` and `child_process`\n- ❌ No utilization\n- ❌ No OS-specific thread priority setting\n\n- Written in TypeScript, and ESM support only. For Node.js 18.x and higher.\n\n_In case you need more tiny libraries like tinypool or tinyspy, please consider submitting an [RFC](https://github.com/tinylibs/rfcs)_\n\n## Docs\nRead **[full docs](https://github.com/tinylibs/tinypool#readme)** on GitHub.\n", "readmeFilename": "README.md"}