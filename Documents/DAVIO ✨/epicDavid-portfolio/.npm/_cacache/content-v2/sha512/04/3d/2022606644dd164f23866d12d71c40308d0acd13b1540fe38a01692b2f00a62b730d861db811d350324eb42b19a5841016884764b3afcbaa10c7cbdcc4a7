{"_id": "regenerate-unicode-properties", "_rev": "36-4c0ba933601cfc72f5b7db75000b6dd9", "name": "regenerate-unicode-properties", "dist-tags": {"latest": "10.2.0"}, "versions": {"1.0.0": {"name": "regenerate-unicode-properties", "version": "1.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@1.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "9185051b0bd742a822d77cec603bff86939df7c0", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-1.0.0.tgz", "integrity": "sha512-I7sJjERvWRZYQf7n43Twkl/miaqWf4WL2GfDqiDBPkxitDQNnkTu9UfhZL0AgvC0am/RWLfKq5kcL9eHBjM3tA==", "signatures": [{"sig": "MEUCIQCy5x9iHBeC8CPuJPcLNpKjXRkhZbPrmYy/eTq0nTsT9gIgKdbFBc+DrvrzmRm2sP+3+dwHICervsWmsnqA1XMJ+UM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Bidi_Class", "Bid<PERSON>_Paired_Bracket_Type", "Binary_Property", "Block", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "9185051b0bd742a822d77cec603bff86939df7c0", "engines": {"node": ">=4"}, "gitHead": "8be21318f4001e0ee7a26b5a4ae13d5b4f396b7d", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"regenerate": "^1.3.0"}, "devDependencies": {"ava": "*", "jsesc": "^1.2.0", "mkdirp": "^0.5.1", "unicode-8.0.0": "^0.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-1.0.0.tgz_1464523154065_0.29613757971674204", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "regenerate-unicode-properties", "version": "2.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@2.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "3059ac9fb34833af50a6a00980cf45c319a91786", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-2.0.0.tgz", "integrity": "sha512-nwsUrHTxYAgA6AyKvP3DBfRL6SrAL0/IRL/EXg/NYvwupss/97KYZK5B2govf5C/9GUGxKhgGtZ99v8fJTYFuQ==", "signatures": [{"sig": "MEUCIQDnfIbxY2iSAKm3nkG8lWhDBR94/53iu/yVtB4C1wL/BAIgDfDuDbpLSVmzJNBapNYO4HRTNL5Oqz2cnyiKOPxbvHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Bidi_Class", "Bid<PERSON>_Paired_Bracket_Type", "Binary_Property", "Block", "General_Category", "Line_Break", "<PERSON><PERSON><PERSON>", "Script_Extensions", "Word_Break"], "_shasum": "3059ac9fb34833af50a6a00980cf45c319a91786", "engines": {"node": ">=4"}, "gitHead": "38c03e42a05bbd7f3b229ddfa2e3fe6b164feb25", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "*", "jsesc": "^2.2.0", "fs-extra": "^0.30.0", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-2.0.0.tgz_1466536603806_0.8539975841995329", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "regenerate-unicode-properties", "version": "3.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@3.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "b6fe9fb32cf09d7f89a0e1231a4cef601e356918", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-3.0.0.tgz", "integrity": "sha512-Zuqa8A2/HDkz/i5EHHq/RSl4P3rYdQZLJc1MvdB0P0yazE3s/7Afby8uxn5tbZxpx53DMmTlFMQ9WYrYJ2MCAg==", "signatures": [{"sig": "MEYCIQDIIr/Ewq/xKZC5wNAsbFa2EUWpupGlMTg9AiCGsKIRbwIhAPKBxhi6nlL10rNpyU1K5wyvSMRu0x5QzQ6Aa3xtxEO5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "b6fe9fb32cf09d7f89a0e1231a4cef601e356918", "engines": {"node": ">=4"}, "gitHead": "ed2e22d497ed9301cd77e2e3c1e263e18a5a5906", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "*", "jsesc": "^2.2.0", "fs-extra": "^1.0.0", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-3.0.0.tgz_1478082511885_0.10755640943534672", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "regenerate-unicode-properties", "version": "4.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@4.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "0ad4383b4c177d369f719b113ca24f634ec7618d", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-4.0.0.tgz", "integrity": "sha512-OkDADQFqwvperEyyGl3m4usLIZzWqdPIdLkwX1Frrjg417KEsiFn5ciVUOa4kWMO/H8HBEA+F4DuxiW2MVlXww==", "signatures": [{"sig": "MEQCIFmm1J9110weXn2b0kH7RC19kGigc1eMtN0FdPLgDwFBAiBChVf4ooSjIMlzTSZ6ujnpckesidGwI8Hk+8vxgHJXaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "0ad4383b4c177d369f719b113ca24f634ec7618d", "engines": {"node": ">=4"}, "gitHead": "a722dc658361a5fa113e9886082b160bf423f053", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "*", "jsesc": "^2.2.0", "fs-extra": "^1.0.0", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-4.0.0.tgz_1478084281827_0.727209473028779", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.1": {"name": "regenerate-unicode-properties", "version": "4.0.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@4.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "190bf56c194fea8e059601dfccc587b77665bf64", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-4.0.1.tgz", "integrity": "sha512-bj1LGKV0sSznl8A4G6aEHR/AEpWZQ2trJFarFm/DQ6BRUJflzGGfKG5WaToAZAYDb4sIFob6Vq20V3Jodwe8dQ==", "signatures": [{"sig": "MEQCIC8FdS19Y7d1vk2X6wx8jcsHKWe+8LxsAGV3uS9Y1PZMAiAHCR+i361BT1r1a8MFBSm8Hj0y1hrEOrgrvLQkzBnDWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "190bf56c194fea8e059601dfccc587b77665bf64", "engines": {"node": ">=4"}, "gitHead": "8528e1885d36f8ac018b298e6924e968922a7c51", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "*", "jsesc": "^2.2.0", "fs-extra": "^1.0.0", "unicode-tr51": "^7.0.1", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-4.0.1.tgz_1479491684723_0.1599332825280726", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.2": {"name": "regenerate-unicode-properties", "version": "4.0.2", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@4.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "d581970f8027c429799cccad2dc7e2285b99621b", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-4.0.2.tgz", "integrity": "sha512-B99vF0ZXvCQX3KlbD823q6Fh3TYUp9mmv5lmG+RZouPye9ULDvg4YrLYIA06aVkZfTlkP5kR9I8lTA4QWJMbjg==", "signatures": [{"sig": "MEUCICd4UxEvm45AALqb01pg0DXrFmWMUAVlsAWen4tPyl+CAiEAoaZjNRYuDNmr39yFO/hQ14nVSr2E6nB98W9UTKKORf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "d581970f8027c429799cccad2dc7e2285b99621b", "engines": {"node": ">=4"}, "gitHead": "c33b350d82dd47bdfa43cf3a50f58341f0d5d664", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "*", "jsesc": "^2.2.0", "fs-extra": "^1.0.0", "unicode-tr51": "^7.0.2", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-4.0.2.tgz_1480402977648_0.6135022311937064", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.3": {"name": "regenerate-unicode-properties", "version": "4.0.3", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@4.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "78bdcd84c07c95204ba45ef7bce4f742324ef187", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-4.0.3.tgz", "integrity": "sha512-rMPoIe3jSXOtSdRIk5jYYnli8GQ5GeD+i0xt8NHy/PcFihCLAahb0hqngzuycMB9BejLsXb9/Ydxm0F5V5+B0g==", "signatures": [{"sig": "MEUCIQCwZ3axjD8WBC+bqCTHF4nD9r+aYLmZmrI2QWvV3bXxnQIgQUb9hrT1bBRbA+wc+lrJM+cUgrW4g/HYNlCsi0ovqhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "78bdcd84c07c95204ba45ef7bce4f742324ef187", "engines": {"node": ">=4"}, "gitHead": "5cb5932927fc8db92613ec1d4052d56b7292bbe4", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "^0.17.0", "jsesc": "^2.2.0", "fs-extra": "^2.0.0", "unicode-tr51": "^8.0.1", "unicode-9.0.0": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-4.0.3.tgz_1488999483622_0.083790750708431", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.0": {"name": "regenerate-unicode-properties", "version": "5.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "fce5efeeb6f520b800614908df331444b5e53983", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.0.tgz", "integrity": "sha512-kcg4CYw+C/FR/rP2iqtQ8H1kTu8BBT1wZPScEuTLGQLq48sECcM/QW2Cx3Q53LFvqahyOdDhQf7WluGDc2D94w==", "signatures": [{"sig": "MEUCIQCW88eML2X14jEza++1POVeFpt2h3jH2HwhUjmX8uBpjwIgJPIpofr5YuzcWJNlpBswsrJiidqCsszDFFjngnWy0No=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "fce5efeeb6f520b800614908df331444b5e53983", "engines": {"node": ">=4"}, "gitHead": "46978a3cd21f791cf1d8725cb72350f9453f62f1", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.2.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.0.tgz_1491904511296_0.7979893481824547", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.1": {"name": "regenerate-unicode-properties", "version": "5.0.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "db4e8a7ce3424591b347ac3c48e49a9f8ae84613", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.1.tgz", "integrity": "sha512-I5N2El5bvNbIsZQAO8wTAVYrTvpVp7fbD1T972pGm+UisTzPOXNkJ/2h1I6cpV8Q0uBNxPbYYv3qFZxMKRCaZg==", "signatures": [{"sig": "MEUCIQCCgcMUru09hYi270YhrzJRuFhtIk1A7cIBqYJi4+3VwgIgCOyxmJWDO8OQu4j7lb01FSME/RxZtKXlxB1jz2h6tpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "db4e8a7ce3424591b347ac3c48e49a9f8ae84613", "engines": {"node": ">=4"}, "gitHead": "87500a14b40b31b35b965a1eaca51757b32f3280", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.1"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.2.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.1.tgz_1492003051370_0.7020239958073944", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.2": {"name": "regenerate-unicode-properties", "version": "5.0.2", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "4809dba35b52e7532f99e2d48d4ac6f53968e1b0", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.2.tgz", "integrity": "sha512-Y8/wqQFpGRWi2r0ie/RBpI6yUbIxtCO5326jaIesL9A/JPSpt3FwAnTTY91kCZKWnPfvmNX3X500srT8uh6Ylw==", "signatures": [{"sig": "MEQCIB189b2PqGCLHnuz/YGCXYpHwsj65hgZlzkryTaa+1uvAiAVObtI2fV84Nr3IgFzOOhGqR1F4rCZPwygaI2zikTH3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "4809dba35b52e7532f99e2d48d4ac6f53968e1b0", "engines": {"node": ">=4"}, "gitHead": "f530085cb9dc2677f52a00df7011e4f720015ac8", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.2.tgz_1492070432024_0.5635801467578858", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.3": {"name": "regenerate-unicode-properties", "version": "5.0.3", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "6ded7d4c5f378fb33146f92a97565e3ce745ed6f", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.3.tgz", "integrity": "sha512-q3clBDZBPt830bn1eq9shvFGvhcHfQK1PY+X+WYAAQYCVaqi8WGd6AC51sTt7q1f0eVeXBrq+P0Cjj0jxWzdOA==", "signatures": [{"sig": "MEUCIGxxEpgotunOnBIBJg9hf3m5Rz9i36VJV51cuIkAKZ5rAiEAoS62hAluUMFPUtpo/p0LFoDsiTf7PIode3oKwbK14rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "6ded7d4c5f378fb33146f92a97565e3ce745ed6f", "engines": {"node": ">=4"}, "gitHead": "d4096fe2f7093aaa7ce463d5c9fb92f988642e3c", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.3.tgz_1492163645505_0.13891776697710156", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.4": {"name": "regenerate-unicode-properties", "version": "5.0.4", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "d5122a6e2e79ac440dd8835edd97669cf68ca758", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.4.tgz", "integrity": "sha512-DCE7REOQeaLb4i1SzOzSsImw0kaPDBvzM0lSriZBEGDBesG7JveYhuO7Z8XWManK7czrXulMs7C3mSwGPcVnkA==", "signatures": [{"sig": "MEUCIQCIJfL2q+WRq6GNGTp3Kdk5gJMGK2WCIlRQp1N+XH/HggIgLKwoKn/wRA/GUosPGfimcWYOGTpkrj718qzKMRzWSRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "d5122a6e2e79ac440dd8835edd97669cf68ca758", "engines": {"node": ">=4"}, "gitHead": "207325bf083c9fada42ee911f21e73e108bb159c", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.4.tgz_1492240988045_0.05017688497900963", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.5": {"name": "regenerate-unicode-properties", "version": "5.0.5", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "fef139d191b4402a983898424dd1a3dda50eb089", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.5.tgz", "integrity": "sha512-9AYC639lKeuvjOo6g6YAYUkKKWe3ZoT8IE47NvFJymlP2ecN7GHI9DA/6Ud3G4OVsXWClGZd7oIZTwQVunjkBw==", "signatures": [{"sig": "MEUCIQC6ruxDh0SrUy6zgUd6KehnEe+icOD6/BJXFUPDTunf/gIgRqGfCUYgOLC2l6/aMV37eBot+jDs0O/qDgqWzMoNSqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "fef139d191b4402a983898424dd1a3dda50eb089", "engines": {"node": ">=4"}, "gitHead": "08e07412b78333fe40d261177fe8d368a1121149", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "unicode-canonical-property-names": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.5.tgz_1492254199640_0.6279726920183748", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.6": {"name": "regenerate-unicode-properties", "version": "5.0.6", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.6", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "d59df5010b17d678c35f68f4c34c2ee92752addb", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.6.tgz", "integrity": "sha512-I/u+HcL+4ueNmjg5IhGVXZ1B5LMDu+Jom8dC9J17z4Coxp6CJMgCKhnQJ98VPNFz0iTfMv2fVuWsTDKeSPcCnA==", "signatures": [{"sig": "MEUCIGFod5Cuwc6SCWvkSK6WgCgCzUUH2H+CyV+H3KZ0D/SzAiEA7FT9+0DB8V1+f6lWGTh4Yz7CmIMUkfSC8qYrc1fxBAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "d59df5010b17d678c35f68f4c34c2ee92752addb", "engines": {"node": ">=4"}, "gitHead": "14f1dce11b492db9a48cd1ba8dbb327bc2787809", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.1", "unicode-9.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.6.tgz_1492261384652_0.23247056198306382", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.7": {"name": "regenerate-unicode-properties", "version": "5.0.7", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.0.7", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "3fe692da96944fadced7e332340b50f8adf6371a", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.0.7.tgz", "integrity": "sha512-OhLSiU7HE/+s30I2DR+MWbJNwz4tbrgKh+2v7ZEDyED30xrvkKwcl0vd9v4ybLlGWcEy4LGO9RCz8te/bdmKZg==", "signatures": [{"sig": "MEUCIQCn6bat1Z5GQGAYD+6kEE0rx5ou8GuQwQThidTdlvGIDAIgDQMEDZXAaOw46PehbU8IwmEwivoMXVoTOYLnsjXioWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "emoji-version.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "_shasum": "3fe692da96944fadced7e332340b50f8adf6371a", "engines": {"node": ">=4"}, "gitHead": "c77698a6cdd1ff6f5a1392c5fbbc369ecd7347bf", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.0", "fs-extra": "^2.1.2", "unicode-tr51": "^8.1.2", "unicode-9.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.0.7.tgz_1492276236366_0.5014378326013684", "host": "packages-18-east.internal.npmjs.com"}}, "5.1.0": {"name": "regenerate-unicode-properties", "version": "5.1.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "3188fe7a0e626185fc436291ed517f6e166569b2", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.1.0.tgz", "integrity": "sha512-xqlmGBklpiDwqLSkXK4xz0rvr3iIQqMt/HsqA3GWei0wpIcybLoujLppewotKaxzV4KwMcMlunj7c89PIeyv+g==", "signatures": [{"sig": "MEUCIFzhNy8SIaSRTlNCcyyVd2oDHYYnumCNLT7o5v5hv+I4AiEAkYlSQEi9XPePG+X4y/6guEDV+rgaA+NTJfQw7UgivvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "emoji-version.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "a0d0e31991572d82cab4ff184c113ebc91505268", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.19.1", "jsesc": "^2.5.1", "fs-extra": "^3.0.1", "unicode-tr51": "^8.1.2", "unicode-10.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.1.0.tgz_1497977975836_0.10501958522945642", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "regenerate-unicode-properties", "version": "5.1.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "f5b947b5b7514b79ce58a756659724fa9444c06b", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.1.1.tgz", "integrity": "sha512-oaZXgyhHR9VYnYFzPRdO+k7tHaUXO5jPqfFYnaC5kIW9NQ0kVwb5rt8x5CEC0LCZ7WRIUy7zIrCF7R2xIbjiRA==", "signatures": [{"sig": "MEYCIQDJvJv86t7KPaxdxoiuySmVTonZg0V25N+4MykKqVPjBwIhAKFo3yq2PdjkS3+jCjPpmfMzMLRJDb7VM+79+14cYpqw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "emoji-version.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "60b95fea424e2b485a17920c6ac6b5782c34ea49", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"regenerate": "^1.3.2"}, "devDependencies": {"ava": "^0.22.0", "jsesc": "^2.5.1", "fs-extra": "^4.0.1", "unicode-tr51": "^9.0.0", "unicode-10.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.1.1.tgz_1502962113183_0.1648625903762877", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "regenerate-unicode-properties", "version": "5.1.2", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.1.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "5cb08fd4a9e6b35c152b96598a2db16b6aa2bd12", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.1.2.tgz", "integrity": "sha512-4JahRsXyqiq/uSHOZjcWSj5CYVyLryQ5LDmMgMrudlx2RU0BS3W9LNqxiX8alzOL6cp4DeDFYtBZSRb3SB6YMQ==", "signatures": [{"sig": "MEUCIQDL/Qv4NaJpkBOys2wuDbdPe0wTJeRhfEB+1n0ElC/A0gIgD+BS5fs2sSpOXaeN8994b5M/dGRJMxNcyWFVyC2NJ9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "emoji-version.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "8e8c2b693712ca02aaed650420827be77ea907b8", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"regenerate": "^1.3.3"}, "devDependencies": {"ava": "^0.22.0", "jsesc": "^2.5.1", "fs-extra": "^4.0.2", "unicode-tr51": "^9.0.0", "unicode-10.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.1.2.tgz_1508690587053_0.5847247275523841", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "regenerate-unicode-properties", "version": "5.1.3", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@5.1.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "54f5891543468f36f2274b67c6bc4c033c27b308", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-5.1.3.tgz", "integrity": "sha512-Yjy6t7jFQczDhYE+WVm7pg6gWYE258q4sUkk9qDErwXJIqx7jU9jGrMFHutJK/SRfcg7MEkXjGaYiVlOZyev/A==", "signatures": [{"sig": "MEUCIGvMH1NcV6+pefHE2MNn9zXoRNpIZZ7l47cszmt788vUAiEA25elgjRBN8qydtzZr+eXVkvfGddlRJLVaFY/adwQ5ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "emoji-version.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "06542f42cea6cd474e89780a54790174deaef088", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"regenerate": "^1.3.3"}, "devDependencies": {"ava": "^0.22.0", "jsesc": "^2.5.1", "fs-extra": "^4.0.2", "unicode-tr51": "8.1.2", "unicode-10.0.0": "^0.7.4", "unicode-canonical-property-names-ecmascript": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties-5.1.3.tgz_1508691588827_0.4505042415112257", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "regenerate-unicode-properties", "version": "6.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@6.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "0fc26f9d5142289df4e177dec58f303d2d097c16", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-6.0.0.tgz", "fileCount": 391, "integrity": "sha512-BvXxRS7RfVWxtm7vrq+0I0j7sqZ1zeSC+yzf5HS0qLnKcZPX541gFEGB39LvGuKHrkyKXrzXug+oC7xkM1Zovw==", "signatures": [{"sig": "MEUCIQCVzHeBDGHSP0pEd6ec+swMl/gbnE2W3GlUxQJdVKcexAIgFAZHmwRAz7tygHXZILNovRlSk38UEwv0QfJG5MBsbew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa92Y0CRA9TVsSAnZWagAAHokP/R8+AaL7ppdPvH5lAix5\nbBHS9PErRgTZ6RNsPgSYhk94AKdeaLmhOPemhw+VnGzL7kem2cFAfSK7PePa\nqvwfPn1jsKWdzrE0D5p5Wkq5V9hN8cx4KkpK2R3orNsnTBD0W88YyehFJ340\n/K0s5KYNObKuA1I072FQ2xv1ZWkRj6y1iswsEQ6Te8GuXkRi0gzvwzAnJ1c5\noxaKugdfcnA7KVJFv7Mvb6E9ShmXy8oPHNrECjHc1k3xSzchlece3KM2UnnM\nxc5xSKNM34llqklrwRzZG0BhQ9Df3TZ9fvHP2GSJ4ISjgLdhNn9umVe1MpSD\ng1JUYPyWTWi41JYDcIhKXFFhXNHGWAOVzwRpNxaTdnYSx9GhDSTF5+jitmM/\nxiCrsVovFHImZ/r+dBBj6/pCj6OUb2T63+FiVxA+hK18CAlysVnBr4cZBFid\n+Pd5VZYsyZTwKGiyneqr201nnAJJsklG6hWtEbn158VTVnzV+wINnPQOFKzx\nRdvN8KWyaReE6sN6kX1c859d2mBtaEYCp2iz9q2m9nbLtv5r49RL+W9a1jus\n4sgfndOaJppfqDiQtPWsHFDfFIwQZJyZ8O0oIbQAbQGlSK3cSZUr5OWPvrPS\ndiJFHRhKB8bYVK5IojLyI6Wtya4DdF44NZ+l0Td+w9o6L2LmKX1L/pMp46w4\nLS8Y\r\n=Uu3w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "9aeebce36f716521f27570bcd2529366f737b7b1", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"regenerate": "^1.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^0.25.0", "jsesc": "^2.5.1", "fs-extra": "^6.0.1", "unicode-11.0.0": "^0.7.6", "unicode-canonical-property-names-ecmascript": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_6.0.0_1526162995594_0.891715212160785", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "regenerate-unicode-properties", "version": "7.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@7.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "107405afcc4a190ec5ed450ecaa00ed0cafa7a4c", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-7.0.0.tgz", "fileCount": 392, "integrity": "sha512-s5NGghCE4itSlUS+0WUj88G6cfMVMmH8boTPNvABf8od+2dhT9WDlWu8n01raQAJZMOK8Ch6jSexaRO7swd6aw==", "signatures": [{"sig": "MEUCIDRyfyn61Bdeq3nf2HQmy5fsYd8kKWNlQAYYx+R79pFMAiEAkf3XSE+DRGWOsfKcE9fPXVuuUw4M5g7v8rfqmfWsiYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 367723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGS96CRA9TVsSAnZWagAAB6UP/3fwRVZlANze7DHMJqJQ\n3U+g5Nm957yDFX4Q2rUPvZLPTuLZJtAZRQOvOQIJP4Aq7tElCclPJbkxMqG8\nVYzO5jxvkPANPLCJv/KZZ9HBZJRRlTqR4GtEWy/eRw3Lb/YEDhsDmr54T6wG\nbkGzcjiLF4QazKCa9ZcyIFGaI6xWidof6nm7BN03LYoLsYbDbd2tIubQMKyc\nYTGKRRRObsjSGFQp/is2Lge1PjOsIwI+y2a6UQ9/+QcU8lLpX+zp2QdbV6mQ\nt/halpP3eJ1EPif/4vXZ/UhC+9RrCQmS/XDgf58H4ymmGM9yvPol54is+RCt\nzAecSA6ZW/D2rOmCP9TYKNIh3hSgHTRmzxbPpmaHVQXtV45296+FMsaBfdKd\nRaEm0kCyicsp1DRNbcYQ6So4H5VOBs4fM42VwFLWB86C8g1yw5GetGb7MPfI\naAxMDiSIFWWq1HoktZp19yqFq02q12FYvm4n/Z4IXTuAXjaLfq61tvMEoCsA\n+L/oSH4e53mTYHBdW4RvtN5ZwTDEIb49RGJB/6YdJgeOaHB5j7iZQWHuFaVE\nnW6dNrCmnWEn6AEhFmhVlLgi/g5LbPhzjEheEBtONJrKf55APECxyp1iDkV+\nH6Gx5oMZrpBZ43W+DbWOdO2gRka8blfgUY5PkVRFMKoXtYGbB070Cy8KNiWz\nBSyH\r\n=1Qeg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "engines": {"node": ">=4"}, "gitHead": "e2b75524a7f97be54210889b012000ff77f74cec", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^0.25.0", "jsesc": "^2.5.1", "fs-extra": "^6.0.1", "unicode-11.0.0": "^0.7.7", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_7.0.0_1528377210097_0.9300077072130188", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "regenerate-unicode-properties", "version": "8.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@8.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "01fc6d6c286fcc932c30f7025ca4a9928bbbb68c", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.0.0.tgz", "fileCount": 400, "integrity": "sha512-tlYkVh6F/QXtosuyOZV2SkOtA248fjMAUWjGf8aYBvQK1ZMarbMvFBvkguSt93HhdXh20m15sc4b5EIBxXLHQQ==", "signatures": [{"sig": "MEYCIQCZIWEFYVqTfHWg212nxBseHNcUgqdM+fbOIhH59ZqQAgIhAMxGa6yoXdpAcCbAJj0plJnYGA1DkudbRefeVH0NpDOe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfmywCRA9TVsSAnZWagAANxEP/2vSjSA8m5322SRXgJnF\nP9LhsIro67VdejvyA/J4QgDlei1s6th01b8LaAJxcq9hcoViSjO9QNVpfOq0\nIQ60u+oA6hrFyvuKcAu6/vNIyoOHAJui5stUnMK6F5/152W2KJyFsosVv1F1\nc0TVVZ6+rmWWAkNzm3vd5rp+uIUqfkKAumzHIpeCRxl4dD+zj0CyJiRsLmSI\n91P1sMIQh/evAq4cX5nkwMCOn8lQQ2I3NxRCd7e/cc4hAqPqDUP8WR7lR6UT\nU3q0bpvEkwWAyoiCk5lbMyG7i9vLSvgtm0Nc3UWiOV0RuHi2Ndv5L05Sc4tW\nnp+3ckfPUFpXLCQxWIhkb50aZNrGs+42pCil9ZtWY8hGQB2npcSWxBXESEx8\n3TyKo28yK9TI9n02cemD1RN7uqkKrw2XB8EIYIk+StV7mTSiqr88QbGTThR2\n4ie5S3trj7ny4wxYXTLRpa/7qQOjXi9M7SOxrOSwtUW0xvR5qibjOhDSaiQV\nlzrP0OUVSSiqGydLnfFTYjOPKP5jZsIm5sYzjNTRO7ukDBEyVtKO0HFahvGE\nvtBXxTus3qGPEvEjzTCLTkgRKS3R1Z//Vx1rhfI41wlfoJWZJjPKbLa4+ZIj\nymqyhs26iiL86vwINpn+DlI91lp91ivxsAzRisB42W28dN9yP7rAkNUGCN9k\nF8t8\r\n=YG8X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "7f3e84789f5f1ece7bf947a42ead1005b068ffe7", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^1.2.1", "jsesc": "^2.5.2", "fs-extra": "^7.0.1", "unicode-12.0.0": "^0.7.9", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_8.0.0_1551789231556_0.012262543443307461", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "regenerate-unicode-properties", "version": "8.0.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@8.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "58a4a74e736380a7ab3c5f7e03f303a941b31289", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.0.1.tgz", "fileCount": 400, "integrity": "sha512-HTjMafphaH5d5QDHuwW8Me6Hbc/GhXg8luNqTkPVwZ/oCZhnoifjWhGYsu2BzepMELTlbnoVcXvV0f+2uDDvoQ==", "signatures": [{"sig": "MEQCIF/iZsQ7vF79GfGw6valhUpnWZYOIPOo/BGmz832yjqtAiAp0IT6yT9WjGcr6JPSt5BNjC4nAhqy98X9TyxsXBDKbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf5/KCRA9TVsSAnZWagAABQQP/2mkECHoBzFA353sPByt\nHd0b38+3ss50kcIZLjWhCTuYv8zyklFe1TglJ3jDz+nDweF/PyhHcbgsnqHd\nkmhcj6wdSTPWNEvSw+uYeEuykjWPmT31QhStgcZf27UVGFzBNQK1VGrA4pbP\nHBDCYL9zywJELFmli/FvWkk6mPse2tiqV5JJboyKbdXnFPBmyZQWMQHJHqco\nneVTAPZ31bylHqMOXpTv54fmY/2bySuPnMlj2Ae5BK3ATKo+58oOBrDubSUX\nD4dQlMw83oFpfGurN8VAJl1z4DFW9K9itlj+JwPM5GkhY7tQJ5d6gRIfCh4x\nUotVGiHPML7yTQyFxZyAJcLMRKJrzG/ImWfmO+AJYip/PW41uJZ1w00nYXYk\n+PklrszwiSIDLIzm2ypluNJvxNYcPpsP4lBcPpY6adKQwuX4EbfB9nZEattl\nV6Pwx7P9woxlBrzjlcgXb6vJ58qyj6cNKcuQuWWQvGx1UP2gUAYErCPYDO0w\nfHLM5pnHwSxjCNpEES/D/Bx9YooKlbBeSlp0w273EO1DEV4REfisUyzKHiWy\nuLIgj3bN+da5+enzjUszXJEWnPCSNaK5HktI6UNpDhwnxSXQllE1AYgOUjUS\nLSehPSlCd1A2pIDlDCXiWtiNfNy9PGW0zPy39CitT+exZoSE+t3KIVvFFf3g\ndM/k\r\n=ZK3K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "93ec920c1a40a47223ff2ddc6e68935f634ea87f", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^1.2.1", "jsesc": "^2.5.2", "fs-extra": "^7.0.1", "unicode-12.0.0": "^0.7.9", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_8.0.1_1551867849740_0.4324910756765037", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "regenerate-unicode-properties", "version": "8.0.2", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@8.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "7b38faa296252376d363558cfbda90c9ce709662", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.0.2.tgz", "fileCount": 400, "integrity": "sha512-SbA/iNrBUf6Pv2zU8Ekv1Qbhv92yxL4hiDa2siuxs4KKn4oOoMDHXjAf7+Nz9qinUQ46B1LcWEi/PhJfPWpZWQ==", "signatures": [{"sig": "MEUCIQDafevbJshJWk+Z8Mlbq8FJEFpypgB9Wgj/2lwoKe0QRwIgVE4/UPBAfZT2ao++AZaOPal9FwPrCUQPlA4awhDyvAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchp5RCRA9TVsSAnZWagAAiAUP/0b5+neWTn+w+BPCl9t1\nwFMKAK8obduakYcZ8FHj4cxwO7jw8AwpZ1Zg92FggIFxOFdSjm7lf2UJHlTz\no/ce/r0ySdW5oltW/OTZDPIfK6U0V2vyQYqvquEcfjJZ7dc+DNNe/HJuZ/Qx\noGxQFzmtc6TxitHJlyOFrcq+S9g357d7yipI7k6mmLd5Gx6sOSkgaoLYNuya\nCGZKYTJ7Xy8nzZStGMZYBQi8sB6bQHd8ZLIUx6KZvJKWWWcxhEz55mFS6mQc\n4dnzHD49MOm2/8D9hsx+ACR/G5l5U7ZgaN9fG6qZqxIGpxNIJXCo1ts9qk3z\n7Iw0qRMgJyeqJMw/NAYVmQcCs3aIHhgAVht6EKVNcccy9bGWQxREaJQ1Y6K7\nSzc1lOxdSuLgyhGyYih+3PSUZpQiNoZ+ZFpMzfklTm8ryJCt+jwFhnTPE1qc\n10+0OBrZ7Ypd/bW2RBMuSlsEogRkvl5aPmSB8CY8oLtsPy9vJ426d8CsoGrB\n2R6oI6vxSgmj4CKSXnHzAKLDH8Z/BbycPotuCrCJEbCtyVcUSZIbwzJcCiCD\npx9qUxdYGCk+uyftpTvNcwNM9t1teCBHx3MtGvXQrFq+QgTp5wNKrcAYDg0q\nWozgcbHDEIJGRsXU1Dy6F1qHhb2o94bjCTaiASEGB1ptFFiSIC0A0Dsx5OpV\nfjex\r\n=8cG0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "2a813f14edaa21291a8022d7ccbf4241346dd685", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^1.2.1", "jsesc": "^2.5.2", "fs-extra": "^7.0.1", "unicode-12.0.0": "^0.7.9", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_8.0.2_1552326224687_0.9612796289049503", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "regenerate-unicode-properties", "version": "8.1.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@8.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "ef51e0f0ea4ad424b77bf7cb41f3e015c70a3f0e", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.1.0.tgz", "fileCount": 400, "integrity": "sha512-LGZzkgtLY79GeXLm8Dp0BVLdQlWICzBnJz/ipWUgo59qBaZ+BHtq51P2q1uVZlppMuUAT37SDk39qUbjTWB7bA==", "signatures": [{"sig": "MEQCIFhJD2PTa+sbtHKRiR1BOAihHSn0BhrOcS2buDeHo8qVAiAm0VpbKC/sbupRKTOtJ4uTTJw6vRNZzo+y7HM3zfjLug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2b3SCRA9TVsSAnZWagAAmYYP/RDc4P6NhsuDbqMScdew\nJx6v0gGXYOIqK+8KomcWbscoAzkbOPKTZq4/2l9C5gZN086mstYNPqLAC0p5\nHjAmjBiXVybFpn1HhWk5/kXgGj23QG4S79LOLjRvCwA9jM7xiupk25ROCBtX\noaQlqr60QZQG/yAG+YULHi4E1ll2p8VWkS+UCTV2B6rWZpuKXftvREMgaXj2\nEoLkKTWeW1zko7sRpTPPxRd0K2fA0wwSP65s79wSNg9W3gaxmZzl8Q0DeYVf\nV4yzv1cKngwWjajVIpSHIlGnYNItTXB+Y+4PiH8APJaosDzrF4nHTnO/5RkN\nUxegzws8AKA9BH+hdbq5bmf7RZoo0WNTdXTZeCPbtcuMJdZt2oVkoauv5kKa\nf7FXmi82dX1UkBwooq9fwuFFsU/aySe7pyqY0V3jKTdVduiDBkHrdEh2TBZU\nip6mQ6sSC3WPetj+toiN2InGGFvLBEonIA1RoHo78/UTsXjQm94Ve3iAVaWj\nyOyV1gmJxBOccjt26zIrX2P8YZlyoSBY+m61bsRstJeWWrXxbld7HMiv957a\nCZejguSoijzYanxpuHLULBLLjta+Vkbk4JRnFlqhf32jikGc3VrkaUbm/eSG\nNgaQn28/ajc1lpC6Fr7bnkGQtjLw7IXO6DhZDmi/MFIte/K3A3ksvb4k8DIc\ndOUU\r\n=rtm9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "934c917828c2ffd32262a742218975746a9a6ac2", "scripts": {"test": "ava ./tests", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^1.4.1", "jsesc": "^2.5.2", "fs-extra": "^8.0.1", "unicode-12.1.0": "^0.8.0", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_8.1.0_1557773778108_0.14372563632471747", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "regenerate-unicode-properties", "version": "8.2.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@8.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "e5de7111d655e7ba60c057dbe9ff37c87e65cdec", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz", "fileCount": 408, "integrity": "sha512-F9DjY1vKLo/tPePDycuH3dn9H1OTPIkVD9Kz4LODu+F2C75mgjAJ7x/gwy6ZcSNRAAkhNlJSOHRe8k3p+K9WhA==", "signatures": [{"sig": "MEYCIQCBGlTNPSqvL8BSww2pdU3FffU/F+oMzeYfs6FttVhxWQIhAIakZXO9FsCeRJdSf5VBwuj+FdiIE6ZcIjsS5h0V8DF6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 390608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaRyLCRA9TVsSAnZWagAAlSEP/2EmTOrn2mn3RN+8kUL6\n9GW2X0w6sIDhM73XggPy1cSj8V7a4ochSs3dri9Q0IspuBECNUkOPD/WXmUI\n1cozDM7IBHtTQ75+tlhaplXk0IQc4tCDJXvG+0TPg2I3xaMFgd+vjR6myyxk\nXfpDWuJ41ejcAqWdM+loIT3wTs3FWc+jyfw13+Vg9BpRwobK9y0lB1t9DoGP\nEiO9RQQBr3ki1sWmskxIssgMT/Fp1qSA3LGlL5onhtDsP4sjLA0NHcfz6dbn\n1Xl9IfTWlOmJx/3o1dWx3gH+8MhegRDYSO6vodJf5DeMnzm8MpSt2c5UH+dO\n8hEmQGC8HeBUAGht40sv/iFZe5abz84968uRYiHp2WvbIsjlwrTDinduf4uL\nIyh0LvGhqC+WFukar+wUdkjoB3Pqv+CE6h4HiwpAYMOwSmQUpx9kni1eX2ks\noDFmKM8UWfNGj0F05nR31ITZmUR+FfLlp21/hjv1bAJbIKCKPsKaCz/fQzSV\nuIS6uJqm1OEtDRIidYK55R+YjXGZDL9p1ONmryVfFD4QDquPNX0WwDoBhepK\nchSwghuCo2vijLQigWxC7mf0zph7SE3AXQykbAEvCP4Xj/O+ZRRGqLCRPtq2\nkF0Rq1Fwuchd27EECuX4/ijY6cyR9DkPa6Qm1iZ5yn+BJpy6WzGJjyctBf3p\ngP9j\r\n=pUZx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "67a25a62622995d07a4e20fe415c7431a3071d85", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"regenerate": "^1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.5.0", "jsesc": "^2.5.2", "fs-extra": "^8.1.0", "unicode-13.0.0": "^0.8.0", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_8.2.0_1583946891051_0.36050068077526154", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "regenerate-unicode-properties", "version": "9.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@9.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "54d09c7115e1f53dc2314a974b32c1c344efe326", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-9.0.0.tgz", "fileCount": 418, "integrity": "sha512-3E12UeNSPfjrgwjkR81m5J7Aw/T55Tu7nUyZVQYCKEOs+2dkxEY+DpPtZzO4YruuiPb7NkYLVcyJC4+zCbk5pA==", "signatures": [{"sig": "MEUCIQCmdKKyWeA/muU584oVa3j3HOM2iYfbi2+KmkRdXVdcuQIgT9fvK+GRjfKz2Au0BSM7JaSklOsPkl/DCiGOi7xgtvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIBdCRA9TVsSAnZWagAAricQAIjbRUVyG+30MRVqFW48\n/QF/2/Dsq0axE9/+XJk8EXjDIb3V9de0omCdZ7zbjPJefmhiZ6P+6B8rHbiX\nlnIc/rEHhzWgh1+PTdRtk+E6u/zGxWm7muBeSVTY1zXcSbugDiqc8tuxD3Ts\nYLpk2EvDCCnpGFAyYzkLVhyhErrqEWH2p8/eBmSa6gvV1hgorPxSPr8ZeQ19\nDbgn5MkMxMCdFtHGPN+ExLcylC8qZto1fLYvyGJtrvd8biWq0MFCKNgRWBoP\npfp9BeWT7zr+8QaFeRhGkgBr+PI6PP7X9kmXx4Xil0iURYyOoPPLOOH4AH5e\np4Rrvhfrx8qphCSo+Ewywgb9ntaWOO9VsZebHJviHpUhSAPmiyeYVyKLEbg8\nVd4BF/BC9MGhxzXcNUerEQkSowIWlocO/IkEJVu1tEoeaaMNe0T8fifDWn1F\npkLPx7qK1bpYEA/PnhkO+kKCQde5Hu5wqMu8k6NoLrav9r5iczQOaynq8bRS\nmbFd9QuOKMAQXe7yri5425daLwqHxdb/xvqqWlxHanznOC/6Mx59jcEhkAcK\nFDfm0FsJl9lBWz8xzPbGIT8y/+k2aacVYDAXDgW0W94IRbGg70axLOzPechs\nEd0qNsxrIVd3hIJ/jJYOr66xDVnW+LoliahqGSZDJvBysYL4CuSa3dopEKML\nRcas\r\n=hjcw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "46edd831a72a3bec66a5cf3ac2225307c846bdaf", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"regenerate": "^1.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "jsesc": "^3.0.2", "fs-extra": "^10.0.0", "@unicode/unicode-14.0.0": "^1.2.1", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_9.0.0_1631617117193_0.11945911471034587", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "regenerate-unicode-properties", "version": "10.0.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@10.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "4e45fd18ecf6055622ed0de855666a5b2ce4ed0e", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.0.tgz", "fileCount": 418, "integrity": "sha512-c4l+uJaj6fCxSC4mrJKTTxYqCn2JKpcMR7Dywad3kFhB5gGeSgHBYBuqMUpZFO1055onWIYDQxa9V/q7gRoxvA==", "signatures": [{"sig": "MEQCIGbeHpIE3ZMB9l+gMlxDbG3kOBJPZFEYK2uaB444OHVZAiACAGBx5U0ezANG4L3PmEAghilxqYV01d0TWLFfLuYcRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzKzXCRA9TVsSAnZWagAAqlsP/jUa+zhgsBixhpxlUcPu\nFjVIkknhPty2tLavEHqeyUIX2YvyOqpW/FUlIQ2jx7aRaHHbbiE0LsLV95f/\nYXxPn/AGhC7eq+IweubPjeJkBKc+e2AE2X5pus6mJmQ4j0URWi+xrIj5bwsB\nSR43jJj6Z7saivMFyaLPNJvMHD/3D+Ony4HIXvRaHzABxWRR3UClXbmlmzaP\nleB2LRUW41AzWR/H2FIT8vuU2X/uV5UUULv8ulMDnsvI80Wc1TM4JWEnDRvV\nksjUQ66ytNyAQL4uaaikrJXAkofXTOyE1Aq//aUK5KZf1pFr0ja9df9SC3VJ\n/kQEDcWOFYBjIyMsLm037xI6ZiplhRDzhDhAZH+LFsuaBLoRb5CVjLFFuTIh\noKoIL0eHS2EsMM/yPy/UUyfbvdOgRahFyhMK79Eg1jLKLCkxgtBAL7wQ0Vyx\nV4zjmfWwLbAeS+DKi+ekEzl2JZxXJltZnCVQOpc6eGNw7ro+imJL/niiNmp3\nFKVkWXeX4cNK87+qhRwS9vrfPg18R2zWtIuDithpZsRhNiSUIczlvy7osPT0\n003AZ5KcyWbFB/5rax7EFqFFpsQqTUlevFaQmMxmdP4Ozpg5c5Xrv7TJlLLi\nmaRplSvnNYuckJitMNkw+Qt2VRnIcVWuUMPG3VmYxdnPTY1+S/0J+iVI9Wak\nPt1/\r\n=+Hch\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "16a0ac4212999501582602fa4c9a51c9b3379b39", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"regenerate": "^1.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "jsesc": "^3.0.2", "fs-extra": "^10.0.0", "@unicode/unicode-14.0.0": "^1.2.1", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_10.0.0_1640803543600_0.5342984149909222", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "regenerate-unicode-properties", "version": "10.0.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@10.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "7f442732aa7934a3740c779bb9b3340dccc1fb56", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz", "fileCount": 425, "integrity": "sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw==", "signatures": [{"sig": "MEYCIQCR7yuBxZtZ8mBQT6hNbNy1Xz17N8ClGkmkPYMH7pHwmAIhALm3EaCan747OrXj7Oc9F2vm2Fx4gUpTAwFcnb9+t/bA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 571479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzWGZCRA9TVsSAnZWagAAXREP/1ZZpbd6XBGsWjqfR0xO\ng0hUV4wT8+d57fbAcbVuVgy3A2j+K5AUjNSBxYj5BpQHRL3aWjPnv4frEi1l\n8iYROmtIGhRNepCqEo38uMGQvCeFI8Re6nt9sat9xAYORkwKGbaZK4rzriES\n3PEIMjrjXgZz3cn1IuAPNd+najTyB/Bvi72yLTEQu7Rdc1K4xt++ksqQGXq3\nyl3jbUCDe1Mqhv5OFE+Y+78Ar/nLun7l8tuzragOy2wkumcIDHf1hdL7pI73\nU6G2XrvvufwWhr+DEyvj++I47SWAW7dhLUzJZjwgVRATAp/2OVBFwFuZMaJt\n0cBVKaYNc2AJQzocWDhcMDLBA9mXaInma/uIVMWkd6qMN6TNA2lSL77HdC0K\n8meDVbkeiyB/10HqeIqOzRTGBHbLeoylRszPWlnnNFCRL7Je1oY5qB7ViGW7\nHyP6AV+uKszDSJESvYKSLtsqh3I4ek8yHe1nyC5Qv7+V9Pm+YfEyliupmmw5\nXJVXbDVFzFoiuURwYaduMeFPSgdrV9gsF56PS1nkjIKdZZd8daG/+OfQ06Ap\ne35CCZ5ia6NNUaEJWVJt7S+cs8ZSsxS70S6itv2o6sQOTicST+hEPiznxL2N\niMYV2mw1p4Jcdqi18wjs6du7U3hkJ6/BSvVrBmpa3hMcJqs4SUovfg/KdV3O\nXrVl\r\n=4hek\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "1f502b550a6425cdcf52ee34bee1a1e44572ed2b", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"regenerate": "^1.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^3.15.0", "jsesc": "^3.0.2", "fs-extra": "^10.0.0", "@unicode/unicode-14.0.0": "^1.2.1", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_10.0.1_1640849817117_0.25811274800253514", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "regenerate-unicode-properties", "version": "10.1.0", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@10.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "7c3192cab6dd24e21cb4461e5ddd7dd24fa8374c", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "fileCount": 429, "integrity": "sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==", "signatures": [{"sig": "MEYCIQCDkHpHxZZQpY5/gT5UT2EWsRw+6YPVV/ZtOVqJU8ziUwIhANzvIXRPKv2/65222N7nBxLr8kYIVJpOykOeWwwKbU4Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 576671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIcvGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomzhAAoo1X9kte0/jhtSmft8Yh+HxopBDYv/ghJuFgfvH9NEp+iM7Z\r\nYSrxdZO6V6FMCXh09tjVFBPI03al9TRMxaJiuH8iwV0YiNwC9h+in1CgVaV+\r\nReh2kwatGsIy9u3jURrW9rVV+BC5pOD7/Y7QSvtsep5cXgJjOmvSl7L0hUxQ\r\nczmKrT11jA79eYapU4m07ETwg1kvtrr1ZEqroYf+M2pAsmRBTQ/0PTtK7+6I\r\nFN4CcSYlFHdCVZwTUbgJI/APFZviH0pIuuF5rYtrmOh/BeKnOaqA3I1jpxaB\r\nUm1y8dBp4U2FmEcTqgdf6ct/7bE5PB87elBSVInoC/vfB7/ahmBJD+drTQ2+\r\nkQAgpbBTNbqB8skembVQxIWKGuJzBed663eGOj3bcx86v4eiNH/Qu3eRIH+m\r\nI9A3gR3bdxc6fudfuqVoySwlA894r6NHB8zGzvprFvF6N1GzCDuwnozyO1mf\r\n8ssB8I0aas8HOkoF88Jm96U8dZUROQeEQGOPecXeJvM0ErT41P/18lfzvRUn\r\n3H67GjcEqHxEdBTGNI0t0KAvDHXz73dbUavvG0im9k9+CbP5VVJLNop6bURy\r\nzGlNgFEeQN2AUz9x4DSRzs1pEiJoj7VtUqL4Jq+LOjGXqCbvQw+FUC8pgFES\r\nZI5xBioG80BYpUjLiWGlQv7GqLjdrv2nO5o=\r\n=07OE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "9ccc7ac031c4b043936e2af26fc89cf684ffc71e", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"regenerate": "^1.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^4.3.3", "jsesc": "^3.0.2", "fs-extra": "^10.1.0", "@unicode/unicode-15.0.0": "^1.3.1", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_10.1.0_1663159237936_0.9400254604343332", "host": "s3://npm-registry-packages"}}, "10.1.1": {"name": "regenerate-unicode-properties", "version": "10.1.1", "keywords": ["unicode", "unicode-data", "regenerate"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "regenerate-unicode-properties@10.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dist": {"shasum": "6b0e05489d9076b04c436f318d9b067bba459480", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.1.tgz", "fileCount": 429, "integrity": "sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==", "signatures": [{"sig": "MEUCIESdzf11DGQycexvaCuO2cN00lrtjwscdHRVN9oAYcEpAiEAyeCzi/PL1LNOAw1unrI6kgOCPN1ovVoW6jrxO0TR5IE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 588581}, "main": "index.js", "engines": {"node": ">=4"}, "gitHead": "5057b6d09cc2a9349d123fe9816ddb3c565ca706", "scripts": {"test": "ava tests/tests.js", "build": "node build.js"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Regenerate sets for Unicode properties and values.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"regenerate": "^1.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "^4.3.3", "jsesc": "^3.0.2", "fs-extra": "^10.1.0", "@unicode/unicode-15.1.0": "^1.5.2", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regenerate-unicode-properties_10.1.1_1694758752296_0.32188473232443093", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "regenerate-unicode-properties", "version": "10.2.0", "description": "Regenerate sets for Unicode properties and values.", "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode-data", "regenerate"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "dependencies": {"regenerate": "^1.4.2"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.6.0", "ava": "^6.1.3", "fs-extra": "^11.2.0", "jsesc": "^3.0.2", "unicode-canonical-property-names-ecmascript": "^2.0.1"}, "scripts": {"build": "node build.js", "test": "ava tests/tests.js"}, "_id": "regenerate-unicode-properties@10.2.0", "gitHead": "9c79055733e78237aff393682f62539d4a28aa97", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==", "shasum": "626e39df8c372338ea9b8028d1f99dc3fd9c3db0", "tarball": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "fileCount": 443, "unpackedSize": 601545, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDe11Qu3ddCI8BgumIYeL0J3u6POOWB6qpEmzN5FjEpGAiEA5lmp5C5FJx+S1T1CvCLcFC/+M7+zNl5FV8ReUHr5o/Y="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerate-unicode-properties_10.2.0_1726132061835_0.7771690061702183"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-05-29T11:59:16.722Z", "modified": "2024-09-12T09:07:42.176Z", "1.0.0": "2016-05-29T11:59:16.722Z", "2.0.0": "2016-06-21T19:16:45.417Z", "3.0.0": "2016-11-02T10:28:34.318Z", "4.0.0": "2016-11-02T10:58:02.357Z", "4.0.1": "2016-11-18T17:54:46.665Z", "4.0.2": "2016-11-29T07:02:59.683Z", "4.0.3": "2017-03-08T18:58:04.217Z", "5.0.0": "2017-04-11T09:55:11.908Z", "5.0.1": "2017-04-12T13:17:32.095Z", "5.0.2": "2017-04-13T08:00:34.087Z", "5.0.3": "2017-04-14T09:54:07.530Z", "5.0.4": "2017-04-15T07:23:10.139Z", "5.0.5": "2017-04-15T11:03:21.728Z", "5.0.6": "2017-04-15T13:03:05.331Z", "5.0.7": "2017-04-15T17:10:37.158Z", "5.1.0": "2017-06-20T16:59:38.094Z", "5.1.1": "2017-08-17T09:28:34.194Z", "5.1.2": "2017-10-22T16:43:07.255Z", "5.1.3": "2017-10-22T16:59:50.014Z", "6.0.0": "2018-05-12T22:09:55.791Z", "7.0.0": "2018-06-07T13:13:30.183Z", "8.0.0": "2019-03-05T12:33:51.803Z", "8.0.1": "2019-03-06T10:24:09.919Z", "8.0.2": "2019-03-11T17:43:44.840Z", "8.1.0": "2019-05-13T18:56:18.335Z", "8.2.0": "2020-03-11T17:14:51.190Z", "9.0.0": "2021-09-14T10:58:37.304Z", "10.0.0": "2021-12-29T18:45:43.801Z", "10.0.1": "2021-12-30T07:36:57.346Z", "10.1.0": "2022-09-14T12:40:38.105Z", "10.1.1": "2023-09-15T06:19:12.484Z", "10.2.0": "2024-09-12T09:07:42.000Z"}, "bugs": {"url": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "license": "MIT", "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "keywords": ["unicode", "unicode-data", "regenerate"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/regenerate-unicode-properties.git"}, "description": "Regenerate sets for Unicode properties and values.", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "readme": "# regenerate-unicode-properties [![regenerate-unicode-properties on npm](https://img.shields.io/npm/v/regenerate-unicode-properties)](https://www.npmjs.com/package/regenerate-unicode-properties)\n\n_regenerate-unicode-properties_ is a collection of [Regenerate](https://github.com/mathiasbynens/regenerate) sets for [various Unicode properties](https://github.com/tc39/proposal-regexp-unicode-property-escapes).\n\n## Installation\n\nTo use _regenerate-unicode-properties_ programmatically, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\n$ npm install regenerate-unicode-properties\n```\n\n## Usage\n\nTo get a map of supported properties and their values:\n\n```js\nconst properties = require('regenerate-unicode-properties');\n```\n\nTo get a specific Regenerate set:\n\n```js\n// Examples:\nconst Lu = require('regenerate-unicode-properties/General_Category/Uppercase_Letter.js').characters;\nconst Greek = require('regenerate-unicode-properties/Script_Extensions/Greek.js').characters;\n```\n\nSome properties can also refer to strings rather than single characters:\n```js\nconst { characters, strings } = require('regenerate-unicode-properties/Property_of_Strings/Basic_Emoji.js');\n```\n\nTo get the Unicode version the data was based on:\n\n```js\nconst unicodeVersion = require('regenerate-unicode-properties/unicode-version.js');\n```\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_regenerate-unicode-properties_ is available under the [MIT](https://mths.be/mit) license.\n", "readmeFilename": "README.md"}