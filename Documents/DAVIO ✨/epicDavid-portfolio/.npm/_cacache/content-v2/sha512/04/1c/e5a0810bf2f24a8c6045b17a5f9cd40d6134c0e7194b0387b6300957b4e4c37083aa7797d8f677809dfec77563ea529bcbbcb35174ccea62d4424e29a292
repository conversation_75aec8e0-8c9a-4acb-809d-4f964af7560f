{"_id": "@types/jsdom", "_rev": "917-fb290aa8ed54000c5b9ece0070dc2a7c", "name": "@types/jsdom", "dist-tags": {"ts2.1": "2.0.30", "ts2.0": "2.0.30", "ts2.2": "12.2.4", "ts2.4": "12.2.4", "ts2.5": "12.2.4", "ts2.6": "12.2.4", "ts2.7": "12.2.4", "ts2.8": "12.2.4", "ts2.9": "12.2.4", "ts2.3": "12.2.4", "ts3.0": "16.2.3", "ts3.1": "16.2.4", "ts3.3": "16.2.5", "ts3.2": "16.2.5", "ts3.4": "16.2.6", "ts3.5": "16.2.10", "ts3.6": "16.2.13", "ts3.7": "16.2.13", "ts3.9": "16.2.14", "ts3.8": "16.2.14", "ts4.4": "16.2.15", "ts4.0": "16.2.15", "ts4.1": "16.2.15", "ts4.2": "16.2.15", "ts4.3": "16.2.15", "ts4.5": "21.1.6", "ts4.6": "21.1.6", "ts5.1": "21.1.7", "ts5.0": "21.1.7", "ts5.5": "21.1.7", "ts4.9": "21.1.7", "ts5.4": "21.1.7", "latest": "21.1.7", "ts4.7": "21.1.7", "ts5.3": "21.1.7", "ts4.8": "21.1.7", "ts5.2": "21.1.7", "ts5.7": "21.1.7", "ts5.8": "21.1.7", "ts5.6": "21.1.7", "ts5.9": "21.1.7"}, "versions": {"2.0.15-alpha": {"name": "@types/jsdom", "version": "2.0.15-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "408de5de83f9143af1e08a84e5ed1938c6caecde", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.15-alpha.tgz", "integrity": "sha512-VNKIOekxESp7uzyABsfm+Lmu9dFEc19c4yRHVzRbjvbJMpf0Djxxz/M/uNspxn9GUPG3TluUijI27oW5Umcdog==", "signatures": [{"sig": "MEUCIQC91fEcsd2L3Gks0C1oq+Lii+cjLDtvVW6YBuHo44BxcQIgPd9oZJaoXxvEeefv52DGSdM86mry2tnHF6hxpsmtcVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "408de5de83f9143af1e08a84e5ed1938c6caecde", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "_npmVersion": "3.8.2", "description": "Type definitions for jsdom 2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.15-alpha.tgz_1463462673218_0.3508024059701711", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.16-alpha": {"name": "@types/jsdom", "version": "2.0.16-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "45999bc313164f9034958d1bf5558aea821995ea", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.16-alpha.tgz", "integrity": "sha512-msUrZiKWnt46FjLxwlPzQRUGVJ/GZCvX/+53TM6O4KjfFNmD+aff39Z2ohb8mtxaxgV128P49kjTyQAMIitY6g==", "signatures": [{"sig": "MEQCIA+fkWp3W3vb/ET5twDt+YUNt2P+MjTMXKcpGduaPde5AiAkSP5pf5OcJ7w3YIIVYrWj84Fzeu7Hk0+3UQ1fIsoVNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "45999bc313164f9034958d1bf5558aea821995ea", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "_npmVersion": "3.8.2", "description": "Type definitions for jsdom 2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.16-alpha.tgz_1463693225740_0.7611105116084218", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.21-alpha": {"name": "@types/jsdom", "version": "2.0.21-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "dbeba1b6c0eac6e3b6b9a5d38a9d7a38fccc8247", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.21-alpha.tgz", "integrity": "sha512-oVQ0oQ6CecSpTVXXGBYWvWehP1xi6ywFEDmwbrTN4U2iQqlxWnWGQ1YspAEXHrzLSDk1+S9pDtk8GaIiP7Otdw==", "signatures": [{"sig": "MEQCIA4Y8AIe0y5g5e8Txsu97Yd2sm+Z95aSk5l4sJcyDxj2AiAJ9y7AtA88rJMtdpBQ6ibM1n5jElaOEey4nWcJOtS3/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "dbeba1b6c0eac6e3b6b9a5d38a9d7a38fccc8247", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "_npmVersion": "3.8.2", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.21-alpha.tgz_1463774348840_0.01720568141900003", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.22-alpha": {"name": "@types/jsdom", "version": "2.0.22-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a3308a226665fac168c2ed6927f83d965818493d", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.22-alpha.tgz", "integrity": "sha512-FPbPRoyJhr1WQk2jaQXEawSTyImEvm2GqmFhRd3KNqTlpCGZYfm3jsjerD1wXll7tcC1MR259JdLw1aAprV+zA==", "signatures": [{"sig": "MEUCIQCLDJm/3FLwukEYjXXQ2rcv8fRWLkf6FhyF36g0JXeaXwIgbOxvt5H/aZ0K9Geu76wpUIS18XrbDsK7UFCY6KWLcdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "a3308a226665fac168c2ed6927f83d965818493d", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "_npmVersion": "3.8.2", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.22-alpha.tgz_1464153443681_0.7471810253337026", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.23-alpha": {"name": "@types/jsdom", "version": "2.0.23-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "55c3c87476a5156e1071aead26d9bc9e294bdb02", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.23-alpha.tgz", "integrity": "sha512-D3z6J+A/kEdEmnut4IsJkDVx8SEM4JXbgt5k6cHntxSKZL5EwC/gzvTTClEF/SitnUMqfD3Nz9WsHnAZFB9Eew==", "signatures": [{"sig": "MEUCIQDN3gGmoqc6J4QorjWGJ3wzM9sP/6mPyX85G/nYvlKB8gIgCuexAp782EMSqI1DwntmplTXeutgM9AvrjgLKNdE3Vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "55c3c87476a5156e1071aead26d9bc9e294bdb02", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/node": "4.0.*", "@types/jquery": "1.10.*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.23-alpha.tgz_1467402746222_0.4659261454362422", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.24-alpha": {"name": "@types/jsdom", "version": "2.0.24-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "580dc6b1fc1f3c409736887c0974a818369dd9e8", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.24-alpha.tgz", "integrity": "sha512-zLysh8eFmigKkqP7Qy32+E2mp4DmOo/5CdMCXPgohyYNrzKbA64MOAvN6KV//oKK7WDhSAx3b3BQmzfBAM9pQw==", "signatures": [{"sig": "MEUCIE2036xgq2I4rTxrgERaIgwTQOCVfKMSQnAs7b5sjkfgAiEArdHIgu6DYF07+0f3FGOEGUQiot7zuC/2igFUs69l5l4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "580dc6b1fc1f3c409736887c0974a818369dd9e8", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.23-alpha", "@types/jquery": "1.10.23-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.24-alpha.tgz_1467414913109_0.5433891706634313", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.25-alpha": {"name": "@types/jsdom", "version": "2.0.25-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "99da51acd89739edba71ef3245eb491876178c5e", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.25-alpha.tgz", "integrity": "sha512-MeCVE0+02jjsgUcPm84NxxStw26hkHfUsE8+y7F55PvJDtfeQpkIt0k71w1dvzbx8XQWalCCa6ak1XPNEnwPMg==", "signatures": [{"sig": "MEUCIQD3lsZLPMCWvgbkX97qePQarlqKbQn8vktN8UepoaxjLQIgMxWjRm5pc1mAgvkjTrKmktI7KnCqRqtlFFeJvfnBKnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "99da51acd89739edba71ef3245eb491876178c5e", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.24-alpha", "@types/jquery": "1.10.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.25-alpha.tgz_1467427470851_0.3839398785494268", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.26-alpha": {"name": "@types/jsdom", "version": "2.0.26-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ef11fae7ca48147922bde5096ffcbfe19f641629", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.26-alpha.tgz", "integrity": "sha512-MQKFJIcstYfF6kdibsCQtbP2rmBnovG5MgI98lAI0C8CpIBiikY9FzmActwwqrtOZDPVodZwVWzrZmiP1M4ANQ==", "signatures": [{"sig": "MEYCIQD4zyiquWnpgahrhvjRw12W/pPGyd7b9NF+ZdqqxlmCzQIhAMAJV9fda85TrIeSAmd1OPAIpe/E1qEpbGtJ/B8a0H47", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "ef11fae7ca48147922bde5096ffcbfe19f641629", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.26-alpha", "@types/jquery": "1.10.25-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.26-alpha.tgz_1467592789227_0.2744633057154715", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.27-alpha": {"name": "@types/jsdom", "version": "2.0.27-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "4fdbc71bf9d6867627fafd284e50f3f3e5314f4f", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.27-alpha.tgz", "integrity": "sha512-UMBUozGHlFV5pBSgRNeCMEALqIlaKI4DryLWrHUovofYNuxCktbcW/F7inFQTm1/fntD2zAZylE4wC+RP/esKQ==", "signatures": [{"sig": "MEYCIQDdZ2s6UH0SWVTt7vh710qytmpjI0o1qZluh3+GtvJnzQIhAIADG7DydD986hFKFxucUW0T4tbs4zK7e1IkE8drS2Hn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "4fdbc71bf9d6867627fafd284e50f3f3e5314f4f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha", "@types/jquery": "1.10.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.27-alpha.tgz_1468010255624_0.39613280538469553", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.28": {"name": "@types/jsdom", "version": "2.0.28", "author": {"name": "<PERSON><PERSON>", "email": "https://asana.com"}, "license": "MIT", "_id": "@types/jsdom@2.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a7ec3958f4081356174c4282971d92df68af0df6", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.28.tgz", "integrity": "sha512-qFcIq7H71p8vrUel4zIsiven2IYEE2mZVXyGXX4jCFl2zoCPus5SVD6nUe0be1B73q/hf1SpEzytwuzd1MIrSg==", "signatures": [{"sig": "MEYCIQDuaZaHbnPyj+dPXf3qJx69sSbkSO+gZkyZZGfrZDrMpgIhAM/gmYaWpC78R3A/1Ke3dTA2mtYFaHTy3oIu0KtSdUvR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\jsdom", "_shasum": "a7ec3958f4081356174c4282971d92df68af0df6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\jsdom", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.*", "@types/jquery": "1.10.*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.28.tgz_1468508942252_0.23213178222067654", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.29": {"name": "@types/jsdom", "version": "2.0.29", "author": "<PERSON><PERSON> <https://asana.com>", "license": "MIT", "_id": "@types/jsdom@2.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b4efaa56b24b8b14a5f435a4badc6770bd0f8755", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.29.tgz", "integrity": "sha512-rK5qJyXMtDl9rOxN2afyU4Hm4Z/T4Yt9PMk5OEMZkIKnXtMrKjkCdfDdPs5l2MEhkzLXOGJ0cB2TfTWfw07RPw==", "signatures": [{"sig": "MEUCIQCK+Ia3Z6X73f0nHqBcpSCvEsRor9qhWOsJ0XG4mwBeggIgMiZkDjc8EGnpSWXdkhx/XaCGEFYK1VJvSLATFvaD8ZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom 2.0.0", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.29.tgz_1474307048597_0.44528607022948563", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "d8e7ca3c9b7af72e5e734387a86ce861b01cec66e622a6d29afcfc7d06ac781e"}, "2.0.30": {"name": "@types/jsdom", "version": "2.0.30", "author": "<PERSON><PERSON> <https://asana.com>", "license": "MIT", "_id": "@types/jsdom@2.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "37f8aae61a33742da062fa2b9f8c7b90016725ac", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.30.tgz", "integrity": "sha512-Ilwn9cwqNlPk54dWlAEcKdH5IPlU8eau6S8tiLq0r9O8AWqfBrQYG+Z+2UbD1NPuwA+Ew1SfrBIdNs2ON5xAZg==", "signatures": [{"sig": "MEQCICUYvEK4ECUm1m3+K+hToiQdETkI9Tz7+xoCoUMdDa+uAiB+R/EG8iIGExm7IAQwZxHvk9+P4m0Ku1oQmVeXK12EVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.30.tgz_1487010640701_0.7416092241182923", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "75062a538f0175f8ce8653cd6d8e51d94ea5f03290a28d9011c53d87d02df8ae"}, "2.0.31": {"name": "@types/jsdom", "version": "2.0.31", "license": "MIT", "_id": "@types/jsdom@2.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://asana.com", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "ca0728210276571761e28fb9f974ff77098ec937", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.31.tgz", "integrity": "sha512-QIPoa8oXsmMaOF+zE2k4w/x07JJF3PS1dGy0h4eSQdCc+Peh/XUe4mYXRYXI7XY2DEAdjrAOnbAz52lBeVzxgg==", "signatures": [{"sig": "MEUCIAzkiSpA7YDTu1SS6KBJKWd0QLpPVzOA9lQOUC/osth7AiEAvmTnh7ntJhOEWbCcBP+NCSkbWyrifJ8zzngBhYySoSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.31.tgz_1496367352827_0.5221550099086016", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fc90759f122f659fc5b2ef6b94c1ec18ecefc0a2e41e416863358d7bfe0b8870"}, "11.0.0": {"name": "@types/jsdom", "version": "11.0.0", "license": "MIT", "_id": "@types/jsdom@11.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>"}], "dist": {"shasum": "7ce7deaa1843601ed043f51a0de84fb940574184", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.0.tgz", "integrity": "sha512-EnJsE9OeDLK1aPOXYwh7R4AJ9X0kcWNIXlMpvpFQvasZOKBgYcEUILrO1bh+8dm+quozFabnPYAOQMA46eN70Q==", "signatures": [{"sig": "MEQCIFmWDbtmH9/WkIpxqNdOZOXVzaxi1F1RLJfabv75YCTVAiBGNJtF6y3BVNji4R86AtV1wj7T0fsMhcFPe0R9xRJ+xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/tough-cookie": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom-11.0.0.tgz_1496409988409_0.956883120117709", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e4a5cc7d3925371eb47b9042b76093f7fc4bcc28006f8c8bd77d7fa9464c86cf"}, "2.0.32": {"name": "@types/jsdom", "version": "2.0.32", "license": "MIT", "_id": "@types/jsdom@2.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://asana.com", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "e6caa025893a4d6806bf4f66d0f414634f01bad1", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.32.tgz", "integrity": "sha512-xaHlMIzlReyciMIWGJBnkEdHngCOEpik2ojt9tJFe7rD+QiObCIcmr9/tAqxn7l1jflQ3wEIkh7+gt4ls5n1Dw==", "signatures": [{"sig": "MEQCICN/JJiYWcQALuuc768uCvRZdhxVhGT+3u6ESoycgqnYAiA2PEcwPa1va6akY7QIVzztkwweastnMLuQ/8YVYmFozQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/jsdom-2.0.32.tgz_1497557783885_0.454697233857587", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7c7b5258f8d28615ea008c83f7b599255ace1c9a41a6efcc80072de122ce3b40"}, "11.0.1": {"name": "@types/jsdom", "version": "11.0.1", "license": "MIT", "_id": "@types/jsdom@11.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>"}], "dist": {"shasum": "6de58d77a47eff95fc5b687ad57141eb9632e1af", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.1.tgz", "integrity": "sha512-7C2fXDVHdW+E9JPh++CabVkUbcmI+aI9CYUY4kzPn3UUgnOEk/6vsnBnGv0hs9JHR7mc3+6VUSLdm7SHdg4/lg==", "signatures": [{"sig": "MEQCIDEOuTcW/CydevaB1Rzh7qBPNtGVjmbWVfI4qsTTCO99AiBFDKEH1RrdqwtHSV2Klct04a7D92e2sfS4MehrFpl3lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/tough-cookie": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom-11.0.1.tgz_1498859171018_0.3719696050975472", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a31c37cd3e0602cbe4e6a265651af374d8d76eb1677f8550a99cc5b7fff370b6"}, "11.0.2": {"name": "@types/jsdom", "version": "11.0.2", "license": "MIT", "_id": "@types/jsdom@11.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}], "dist": {"shasum": "57e14d79f35b486670eeb79d04d05158509d2318", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.2.tgz", "integrity": "sha512-jteTb2GhpeBc284FcpDbngQ4IQizu0Z/AQT9zyk2beSiqSvC0mxtX+SnQZj7gULgWcjLswUdlW/LfMqx0GhyNg==", "signatures": [{"sig": "MEUCIQDFcJvcUFeKsr84kPxMw+YAq7TwK2igK5d49qtVmktD2QIgONo2PEi57RkTBi+VSN+4zBtxu0PH+t2HXZkIoww0Jyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/tough-cookie": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom-11.0.2.tgz_1504216341259_0.038546370109543204", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cd9bcdf5d7c5e03813fa7489c91b69878daeca2420c48665252726a184a41394"}, "11.0.3": {"name": "@types/jsdom", "version": "11.0.3", "license": "MIT", "_id": "@types/jsdom@11.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}], "dist": {"shasum": "f973264511c82a286fcac999068c4734100b0491", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.3.tgz", "integrity": "sha512-bgiT4R2GssebXR+g3v7F0ELzapLuLWyv0ODd9ou9+4Ajx/Spyn5CR5/LqLosb4AXIGAtDTUfbjYL2DOtYYdlng==", "signatures": [{"sig": "MEYCIQCiWyWVkBLWbL4oAusnexasddZBeVWK+O/l7NQMxWoCYgIhAMNp5G8EtN9N9Ysv6tLNGm7sosRRiRGIHq0OvmUEDVJB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/tough-cookie": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom-11.0.3.tgz_1508956825497_0.18252689950168133", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "390cf6e7875b9b367eeefb26299847e87c3364eaf6b4be555e2de1d3e5a5a630"}, "11.0.4": {"name": "@types/jsdom", "version": "11.0.4", "license": "MIT", "_id": "@types/jsdom@11.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}], "dist": {"shasum": "a645db4704cf7e4f811f583926ad12525c719400", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.4.tgz", "integrity": "sha512-lthMj4kw7Fzs3LjBhQ0+1faAfDrN9GFJZO5Nf/xO7fppFfxqnnQdNR28n0xMGXsx8fTHOPliE1NTkAW1bVLpYw==", "signatures": [{"sig": "MEQCIHPfHsAQsQaNb/KfZpYLIR6uDD8LlMEYB/4h2/gHgLSxAiAv4VoCUov19ZdGvzzzPDIRRd7fMG8gKcZAcMSGIY+kpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/tough-cookie": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom-11.0.4.tgz_1510268107902_0.23516276176087558", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8274925fce1421e7e398e6ed60e6a418567bfb208738eba216a2c645e44cde68"}, "11.0.5": {"name": "@types/jsdom", "version": "11.0.5", "license": "MIT", "_id": "@types/jsdom@11.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}], "dist": {"shasum": "b12fffc73eb3731b218e9665a50f023b6b84b5cb", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.5.tgz", "fileCount": 4, "integrity": "sha512-j8ANkoDARupBS38xxQv2vu6qsUFZrlM7sSNBPhqe66mM7cjcqAEDUphOG7nE7/fePslJgNcEPVNTYFlkFVogyg==", "signatures": [{"sig": "MEUCIQDIPwr+0P7dV6BTJwu2glQd0weTalVZmqkDOWZf5lOw5AIgLjnUW2EP7kg9upnijBvLvXIO2hfO+L8Yn7677jsrmBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF25hCRA9TVsSAnZWagAAKxsP/jfYkeNZ8vmCbz8bfnrK\nB+GkSVjK9R2gbzF2GoLU070miYrlsERpam9cVmkjeTZtyDwculjvOduXkqS9\nWAVbQffzuvdf0YRJ+ug+8XArW1hMmYpz20T9L5f0EqnD/8PqwaI5skmg6apy\nWS/VUAdkLmEdA4zxQtOK0hS1WfiMTSO+Ns6b+91mS67qRaZL56vxc3HfwupQ\nen2BvLN9udfJweZBjG4kOYJDG0EDEKPaxOOjELnm4yMj2MSfbIr4Xa8bKSol\nwma5ShsMTwzfZzNhk6n+ajz<PERSON>ZZflvD4J40v0UzhqqSRpnNq1klwl/Uop+QJ\nXHizSAytxXNN10+9FEsWF2vVtRpCmE5Vfz7mGQeD1xoR60gEeuhkDcMPEuP/\nmG305e8rw35siIh68IrkOmBAyNI2ybxsA9bbeGYgdUrTfu/qdFjXGThdghaY\ngjYsWN9DGs3596fEKTYBwoF2p7OqL2QZehmznPZ31uQhF3ZB8F4lx6ZrKCRy\nW7074/BAEYtZNviZ5YEHJxT6k3HwRd05DbmGAQquBKzkVF7S0dcnFmLEEFXX\nhVcng5vqtU8yv7/nRhxziWhnLc16DznGQ8/nCbmbz0nKpwbK4QCaFBKPvxrB\nAZlMZov7OQdRzPfTkwQx5vlNx0DizLnGzRpc2Ov3TPjLvtwiBpl2Da5Go467\nC+gj\r\n=FLHq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^3.0.2", "@types/node": "*", "@types/events": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_11.0.5_1528262241531_0.24045533265678865", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "55ee2c7377aa535bde0d7add263f21b10233635ad424e7a77bdab915a31261a3"}, "11.0.6": {"name": "@types/jsdom", "version": "11.0.6", "license": "MIT", "_id": "@types/jsdom@11.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}], "dist": {"shasum": "20d6e8c83f15fef0b40c39b3faebe8bc7587d86e", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.0.6.tgz", "fileCount": 4, "integrity": "sha512-6sw9iNdXak3Dk4iN1osdZeZcV8cTe2SNIVwbnt8MjFFAI8+nBWDNcDfjUgZZqycF3Twlr7xXlKntnOrruxNRvg==", "signatures": [{"sig": "MEQCIGkOcHyo2T/3BFIvmLojtBI1LS/24aaH5qgx5zGyB14cAiAe8iB0TJUak571Y5XyD+oRgQKTZcnEOzaJmo+rm5ohUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKZvQCRA9TVsSAnZWagAA2F4QAILFDxCvNlGF75zDiXLk\n3O5Led1jKSKGOOeNEOPEdmWVMYu90jjg/JrKgLUREky5uBndv1Pp3NdKBexp\nMzWEYk2t/AOl/d35a6ab83ZfwZ9zpeuvBRGMcJG/p0CoAnql0DptbXk/FzLN\ngEWHvvEtGKaj4cqleGhW9rbPjpjY5V6iSPSrgdYeCFnj5kZz4nBXLiw811uC\nj6exO5MNkED08g1+Nir4VQ8U1luv3X6fS/EmVrkB2YwDivaLq9Dm+enBchf5\n9et8NObYDwj9QRZXblDdNjBgkCFIBP3ZgL6a4942+tcsJ/Om9z8sx3ZLOWAI\nxirKMlFs+ohrDruC1brue3NHZkX7E3cj8M8z7cPzz0wU0P3FeOHzWL0HCFWJ\n8iETVAPAGM7Dx/ClLdHBX04AzAANAuohU/5TUDMgpaM6GDc86aSYgisakN72\nHY4ZfWpoVrd24HmX7sZ8Si9X5ZBL5vl19xDpPWY9fZ7QTP6wUXXkrRA1899G\nEtD+wlrNynMfDroBNm9jOoFsMiE9F3ZTeZTM7s+47x4FjIeqOlmMITx36TlN\nCemTArg6s6GLQacvwiaKL3IZ7cWyKhov8lwOZOJzzDBLwcIrmQd0mBO0ywB4\nMyDYp7enL6MRrACBdricFgkwH5rPtJ2A22d55keGRgiRsDyTxyaT61o/UYDv\nfZwG\r\n=oCBx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/events": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_11.0.6_1529453519593_0.7602614141691488", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f633a100b8e9df550043f8d019685f8323014049090f0032186ca9bbb60760be"}, "11.12.0": {"name": "@types/jsdom", "version": "11.12.0", "license": "MIT", "_id": "@types/jsdom@11.12.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "00ddc6f0a1b04c2f5ff6fb23eb59360ca65f12ae", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-11.12.0.tgz", "fileCount": 4, "integrity": "sha512-XHMNZFQ0Ih3A4/NTWAO15+OsQafPKnQCanN0FYGbsTM/EoI5EoEAvvkF51/DQC2BT5low4tomp7k2RLMlriA5Q==", "signatures": [{"sig": "MEYCIQDDVd2hs8CmxYp4h7fSjnvFsgWmXl9fm5x/Bb5C0Osx0wIhAJ1h/0L2gUQ2ZFtWMmk5ffhoks9CKcQs7X5IHic4APs8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmvVMCRA9TVsSAnZWagAAGycP/29jWDOx7pd5lYMu52Vb\nAcbY36i8RwwTnSEKNDenUKOQHBE3blDgl9ujhNQqk8gs6pFpNfZeME48ov+i\nPpvOAt1G7uAHhupIOiTx/ifYs+IbwCM2vauRD/EwXWf7QnL+A6zDmgNZptVX\nAYY85So27ADDQpZhDMcILw6/ttWWacaFJxXFOB7XcOJAuT4qcRnc1gIL8Jwh\n6/xKDDU355HQEz8epvQkRYYWOuKZm2uinHNJa1CINviMmuTmn8AumBSu2ngZ\nuxwbsPjRtY4Yp+wpuWcGjqWN2havB7ZiNkwDj74Aa/peyn8uTpbjkctZtU9S\nH/HKsRiSqL6/H8No0TWvAzuu7rgS4CqIL0g/gAstBRqpwGouyIdcp5Pu39sV\nUhGQSfOcVnhCLB6aLktlNfRkN3u3mnhzufhhYx+HTCWyKGvO5IGtKyL3kwsv\nKiwTrcbpI7VXb96xEOon9qj18Rd2iycvbBIf0gRowdM4FLKIoWq5RKDAPFXK\nk00+XuMHtQ6orjUoloGt8JpU35iLTCP6EYzwMDIAdi7c4qSefEYTRt8bcoEf\nbBvlNHUJlfMoMfeFtrAqU8TTWWsSwq657ciygEbdYR/tpiHEx+HSpbGtplCR\nF4zFwP64BnwZLLEsiY+g9SO1W9/YXQ6L7Dv5W/iAuNISyaL1uUDXoQHMnKAB\nGhus\r\n=W1Db\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/events": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_11.12.0_1536881996274_0.7490662695409331", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "265ac95f4e764ca77d279835bdcc626132f09b4e9b252962194561991f371b97"}, "12.2.0": {"name": "@types/jsdom", "version": "12.2.0", "license": "MIT", "_id": "@types/jsdom@12.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "e898ec448a8f9a7418f0456d57b72b6dd51599b4", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-12.2.0.tgz", "fileCount": 4, "integrity": "sha512-8LQPkE7S3IuuJyHOIghXf+AjpcfhleEG63z9pveKFJ+Aqu9h/PXefU4gF6PSvCyOeNzlfFjMWO1Vtctowq2jRg==", "signatures": [{"sig": "MEQCIB54vbF2QGACUb6AtTeqm7evJHLb9Tc6t/zKTB5H9g/rAiA9q8tQZhZY9qwqI4E+/ClAfadK90v8/3vfJdEinHgbBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvjlzCRA9TVsSAnZWagAA6M4P/0EPF57ikRkwLMIeCjzt\ntWeVjps235sMiqYsaBPT5zX7hRiIJ+xYs80R+SaGOqGPdDkO/WXh+iYwmwF1\nHBA/aaHFUqlUUwvL58LPN51OP+CrhPLbRmucpFsCoC43Qyw42bzdKGbihm+z\nw9jtM/G1WrkPCySDqT1tUOn7JXdo8P+tCOSiwVKDEuuKMo7XcflCyPe/GABl\nIvKw8VnUazqZ2Xe0OpqucIiPHYJeKnZptB9Li4oVWj28zbToFir3n3jSfwCt\n8Kkr7eMREVKliu6Rvu86mZFix/VNlYtTzc2NMpaSjJTJotKUt+7ypYVtr4Fn\nrV51qjuTV2P2AqYApm2Hr1mcK2kcS8gB2eBbHlwzkbUeHosVKEsEz7x17xbT\niovY3t2lKt/LdY/rKVo2s3Osav6jc1LhIFJw7otNjru/01YiBscovL/cB2G1\nOw9eEnWGQ/3QkJK9fLLb8c4BVjMdzYCO7mL/+pk6pftWdb/qwBwMRqXjYMhJ\nprqo3dkLK92OB3og5CgW4qSC5GQPozyrV47mxO/Bov/d4dvHALTY/4dji/HM\ng7Bct5jceAoT9BupcTNl706FDpyNY7wVrnJ78IKf7OzKXw81G6K8CeaqklZs\nhKJXHdtwuiJeUHdJzcR7m80X31dMHzMpvyTt+2hdLFuuTMTpO+bUkgEDmf/o\n98qj\r\n=49zY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_12.2.0_1539193202349_0.6849457457007033", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c4f2211010ecff0a6245368a4b89e7e1b4b8ae72a3e14769154bd3a9779e59b6"}, "12.2.1": {"name": "@types/jsdom", "version": "12.2.1", "license": "MIT", "_id": "@types/jsdom@12.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "0525dc8621cfbf235bca298b0f9c96bd0c6b2e5a", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-12.2.1.tgz", "fileCount": 6, "integrity": "sha512-VnLP1qW70OkzpMVuFsJPhxeIzEW1y+t91Fa2rE+b3UZ3ZiTwB28pYrdNj58wa0AQ+dV7eIBcdMFl3ql9C+cc9g==", "signatures": [{"sig": "MEQCICdM0oFFplI9Gli82T1KzLfq5Js9VMi6rZJA5tkepaP+AiBYd4EGwizry+aNIwZ6/au+lAhB+mevCjqnsNQ02FRPrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIbnNCRA9TVsSAnZWagAAtaQQAJIu+/kM21K9YO8EXTC0\nU6Iv3Cc63jXviCpE6M3C0Lrwe9x0vWXBedYs26CgZi0/BETrf1w2sAI1p9Ss\n1kqvOXy2xzdPUBDC46CCDYEZ1Z9GrvOJLjqTJSAxuhyHszihIx1WKZPFBKNh\nEnuNg9iVj7tXXw6ZsMlZj6o7HWkJxISDTcmRaMor+33h6lWUQn/obnw10396\nkfQJpqSAw0OuPHDmZvepZn9QtnWzrIl8AD5Jk2+WFyaOBem20+IBFwRmPDw1\nOfsj9u0ZGA/mSDqEKANWV+59TxostkSMwUKKh7j0LywiZ/6zSFRPCOuDHM6z\nvcKTNyrX5o4cUBZjuJunoc8rmybBHygA2FQZzxynKmOFxal4Yo/ucgL2Ea+8\n8VfVnmQ2e8oVYakhSHf4GjkFymmUk38Yi8K9xRMxIcc+KH3clMmf3MgeQ4B0\n4pATlJlP/b2cqjoSmIOnFUnP+1s5Xy/CGiKo8Zs/X8W4o/tU4VHH7ycIGs1R\ncN+kvUYljS5j/nw17hcGD/EN0DqA89saFZTQEUoWRUa1cDY9RncXcfddo2H8\nLSjeq3XENod9kr+jzf6UJ+ZDKKJiNKaxNOX+EUxrFKlAUwHDlulO53NcqAOa\nFpUjBL4gh/2uOkiedXVtifP0lOhSHiHjuWRWdNyusA7RNtbRUzdgNzjBdOVg\nmoJ/\r\n=fVZR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_12.2.1_1545714124576_0.12907640141386145", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3edd112cd0aa3b20a94c9351202439296994256235732965458a4d4d311aed25"}, "12.2.2": {"name": "@types/jsdom", "version": "12.2.2", "license": "MIT", "_id": "@types/jsdom@12.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "3a3f7606e11113f78ed6659544d23de8f97864cc", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-12.2.2.tgz", "fileCount": 6, "integrity": "sha512-+k4AsH9TXsuvucs6EiMxxPPWiXOGyM48P+jL+IPDYhtJ+L7Cl5xbN3Vig6z8tzvo9Lv7mjLGv54j3D/KExQl7Q==", "signatures": [{"sig": "MEUCID03NyeOQmZumfNC/DxSC8OHwDdQSyLJeJGBF9BqTWgJAiEAkEWQGSx6wNZh47QHxxMrNbuc/VmHjhYjQ+UxZt+yDX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZGz/CRA9TVsSAnZWagAA1/AP/RIwldUIhRQQJxfwMwRZ\n18N04UVNKcGDTiRukZXd/k/zdIx1VCoQMyEVu7KcHjFvNx5RwmyN4NnlcnWm\nmbgMOBumXw7mK4xZAFw6z4vQpXU5MivxqWrq7lmoxPjPqluTYbMFTHJy058J\nE0EC83rdOiv/D5S4Oew1wwWnFkqsgmbSJ/q3jaUVgu7qtnX+gV4kAckYQ3mE\n1P6we/FidtGYl1Cqv/NqX1shL/nwb3gWcrVtz4IvXt/Fx+PPBB3ullVlUNBa\nNvEAtjsjWYwCWLpQ6d1ACkwU4UR6t1gAEIB2Shy/SjagcKAHC36NRY3F1Xru\nfeYz+exTBUMPQKCaDLqIjXB5JcGReAoqN/okHSE8XP+xOb3qZoZAojREzdb0\n9jE/rp0NIfV+IuzXOAxdBmGvVHiBUAArcOt8CBeDSgmY6YKwdPGUFkKAeCPF\nvKc4vvb+4IX694/qp15v3BDB14+JlWVIRfdB1DSJZPgUo4RPE4u21PTKiN+8\njYEfla1xyE58byyNNJfuj+0HY1DoUseDbSdkucI5SNSOqTuiSoWJV7Azaeon\n7p0R3S/5SWvAbVeLn86LILbyENcjFobUwt1ahJFk/QVdKSHKkJ7+O12Y3zLh\nK5NFg7rMNPununa4kjflpQyjDENy+K9PFb9QR71myjt/vHkfEubm76yzjZ51\nzD8h\r\n=UpYN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_12.2.2_1550085374291_0.7691212800069345", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a128dcf5759fb5687daaf4740e9b1cd8ecc804b884c8a2b558a1916b0794ff34"}, "12.2.3": {"name": "@types/jsdom", "version": "12.2.3", "license": "MIT", "_id": "@types/jsdom@12.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "e5f037579d1bde5b6e52b9b74cbcb924eca9a4c6", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-12.2.3.tgz", "fileCount": 6, "integrity": "sha512-CxYWGFsBs0VZ/lU9zxfWk7zYfOlQQuSs5yzk34KW/vQQJGE8OGOYxJnqj9kGHE1SBZGVJvhXs1TafIpZL8RvXw==", "signatures": [{"sig": "MEQCIAP5zjynisn9HGzEtCuuDaOLskl5pfKQQCZdt1M58tpaAiAavEGruzZKndjCXM140Qu8R3z50YnCHGw6HxLuI5NHLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceeGhCRA9TVsSAnZWagAA9osP/j4QA7ixL0XicegN1IIr\nk0cTz3D8Rr4dBRoFNClkNcWB+QsWCyjkK7kxjAbQBJPlnB7AVo+98Cg3QFhg\nc8QtoWwO1HKUqlQF2oHwwuK/MeY6dycO8c5Ehry3h04M3WpPbtUThJJsoW+C\nwj5CT6JT3ybNs+/hKEZnvKldND4mUf/JpVASfFxT27/0Rc1v8VR/PWaurrJE\nrVpSp0vl9vBTKeK5iTrpNhjJzg7E77JDHuw2Fmh6ei2ahzwkQGmnxZJ+OQL6\nrLcUoMCRSszWWxnG7Aul7nqlyR9o6y778xziXOE0FooGkQsnVDHK7detGdza\nazYI7Wk7Q1QFr93+79GrwKVOsFliIR5Ul6vg9cNyW2FsT9z6hMZvlVMmyxYz\nvte1ZeK1fhZJxUdM+IszJc811JFhWYhsMmHw9PKvV/jrGQhIoIVdmY+2+Qq5\nKvQVzgMgN7VmcCrJp/a016QmKjavbJ1HVAouDFt87yNgpWX4k52vt6YKeu5c\nS0/FOVfZAvEKDmq7yVFjPJFPV+WVTfXUkZ9Gu4whx/gynodTjjq++1n7SWzu\nfgBcHH09ekK7evdo3EPHKsZHQcGC35EcN4ImkKQPKH2rerJQ74DEs3SpGaW6\n8EhouLSQ4U7fLNUs0AY+/Cv4VtxVe5dAH08NrioX///w9U1LoTbroAimM6zn\nik42\r\n=mF4U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_12.2.3_1551491489157_0.787886225300767", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2430fc1eaba56d10117de4882da3bc4305865a6c9eedce7b62e365bd2821893c"}, "12.2.4": {"name": "@types/jsdom", "version": "12.2.4", "license": "MIT", "_id": "@types/jsdom@12.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}], "dist": {"shasum": "845cd4d43f95b8406d9b724ec30c03edadcd9528", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-12.2.4.tgz", "fileCount": 6, "integrity": "sha512-q+De3S/Ri6U9uPx89YA1XuC+QIBgndIfvBaaJG0pRT8Oqa75k4Mr7G9CRZjIvlbLGIukO/31DFGFJYlQBmXf/A==", "signatures": [{"sig": "MEYCIQDOc9YUb3K+9VzGCocyMRAmwHgZKLm2BZhXQZ3tX8zmngIhAJw8f/R+amLZoCoQohQilgcaM5UEaw5z5DAwK6PuqRrw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27404, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE6vLCRA9TVsSAnZWagAAfCAQAJzsBm6+g5ATfHO3ndB8\n3sbpkX+TtJBhi0dKOtLngPEpWqfIsquUV7XJoRpkemc9l1jfmbLK59vGTarH\neqgyd2ilU3ystlqqmhwvhia00EJ/Gp61AFyvDxzo3kDIoXIq+3ns779vv88W\nRRDTzBN6ah8wK2C4loBqUYM9iTTU541BgQ2DzRAcCfQ1yZaS+39oSi6s9dyT\nLb4hOohYA3+6aE5S+aUQvrH8Cx9AYK/EW0vIzk56aNvbL3uwhrEudNhF8/uB\nEPWVXlaOJa9wXbYbz8APqDkrhaiOnNYUEobLjGOBG49po2XjjH76xS71bq6t\nACFR3SDbkn8qnIR07rUEKmZemLCKZj1+nEXgg2uVwVLuCUr45cGSDoDS5vP4\n4LQ0YhbNazTAGRC3qJlz78CalLZDQ+W9stU2PMDpHyeiSTzqwZmRZU/gidSt\nNy4Et3RtFeyCTMG8VAgsroBHsY7K9J7bINPVzZCAflfw8FycEv1fFJDyI3yW\nnAITJfny4RX9pm0YtggL3BdeimdzRTgPjvYYAkrhfY6n1NbS31c+DXR20V4m\n8qyaiDhMCBkZQvyQ7/mWpHjiu8TUjMqDGLvcbcM6G/rTN74vVW/5HBdE3rO3\na7rry7a71Yn3AN7DIxYd7TxJ+sslQH23zAvrhwJ9MLPWiQ3liS7KlATxFy6Z\nvWkR\r\n=W2PU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^4.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_12.2.4_1561570251341_0.6572317105746917", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "da9a44ab5990c9d72011684e8c43d7eef32bae93b0567b33ce11d71a98908a6b"}, "16.1.0": {"name": "@types/jsdom", "version": "16.1.0", "license": "MIT", "_id": "@types/jsdom@16.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "89d0ad0707eceb223c0db9b76611f8d487dd7d1b", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.1.0.tgz", "fileCount": 12, "integrity": "sha512-GiBD8K4tFb0ah+rFAqkoP4tCc6DpCy96lITCCCAf1yqgvmpWNHh4S49sPIBXn4KIfvbVEcDRK+jgDHAbfVFdOw==", "signatures": [{"sig": "MEQCIGIFqqvOnFLnPolCIaUBCjB1LTbZF4D6ouFBp3N2VMqVAiA2Pqmf4oXt06oNuYCGbbHygbBzezE2+wLjytsQdFJjzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRtm2CRA9TVsSAnZWagAAmEgP/1ktjl884B2+91B2Dxe2\n0xXJqMEoknRMqWSiQg63yGmIq/KwOFGNmARRxshfeKCSf/9vLYjYhy1Q461j\nu8kN4L8bsx4Ae5OmLxPXMYG1rtJ5anWSf/DFzSA8kyc/4bPzaBMj5BMCdJhH\nm+woaINgD1H8iUPYvpCssiBA7gGNQye/khUBYdFt09yn9sT+mgTehMahmvcB\nP+WdNVPglYZJXvW57SkyE4LW15adZ8klF5gDsmziYuILrXMrZ7b+RCXwpPPo\ndHX+rwcNyzfTE5EGVKw4heyhfNmCBJKeTx4cG60TNKMXYOE2tkQacDAcgccQ\n59fOJQWU1djptzEVuAPO91foybfqXJBCno1GxWWG0LEWn9oleYoVVD6A9vje\nMBeKUHeZmFTMDOtZKP+m6dZI3T5JRuULTqcSeGBEfT9f5tmI9XQvqo0XUHBe\nH4AiqbmPWeGj0wFkKAJQca8cHHo77jST8pTk+mBn8tEqauoCMFLbTS9nAS/i\ngtK69pCcTTIudDsMgm+ImSnfx6P7oJNKUSm7WQZJvckqvR9LfsBWRJ9yFDn3\nJ9BzCrJu/uY8rCHK9QSsdUvP5v6R/OA1eA325hfgRlR4b3r6lZgIeRjIExlD\nRYTBSIwWbFMlgEMY18y5TDOfmA/9FE82UeSh6KXN8YaEV8AMQlHZR3o97JRf\n2bp1\r\n=PIvG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.1.0_1581701558437_0.2799955573929356", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a475c83e224d3de24af7c76aa2c8769b0ec52124fa7fa1320ea02ce6397a7a5a"}, "16.1.1": {"name": "@types/jsdom", "version": "16.1.1", "license": "MIT", "_id": "@types/jsdom@16.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "e3ccef8f8f1ceaf7dabf69fb24f721d0ce403c49", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.1.1.tgz", "fileCount": 12, "integrity": "sha512-SDajC/tyIZIfagPOuDqkhrI3C6z4++2XxaVWdPgMADDslYtpZJXlwZGsOlQbCAX9wbQs2eycIwAFYpbbzRYzHA==", "signatures": [{"sig": "MEQCIA3EoOZ7XYBlSPQJ2atO4Gg4yn4QoKi+ZXxI/fJgwv6GAiBsu9wlYtJFETI+Q4tDwPWNsvEEYl17j4out2kepUZ1Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20848, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeNjWCRA9TVsSAnZWagAA8PMP/it9ymnTOeM/290bydOs\nYYxFYjjMHn+gL+Bx43UdBM01778fgdF6Tetg8LEfufUoZaHXBCqqnWbv5/77\ndEJ6GuJ5sJt1PsTvjD5swyn4ly+Lx/8di+3NAS4eBfQ+APzHQEmMEp5VN0LO\nU0DeTRAXlmsVx+PsBtmMOST2ndIL3guOX40qJSzd3SUB5aru/BuKMB/EvNBa\n9T0EWF7l3N2j1C0lBXuIHdAB00zChI90iAzZK3fVvdyLMpvHIG0XbaZQ9IN0\nHW8Xyt+GJNXjYFH0pnvV5JRseYtKKiIZlxKox2pKC/vjdhS8qBb3BMmVi+55\nhltEAIB/Cxn3PcGfYevNcgmqd4wPdELjNajbmEEUBB8Nv3fDc422gwG0Y8Im\n85t93MENjFVyFbqAizNCfgfk1v+alPiRQ+lMOaVo/4eL0nIun/qao6J4ioD+\nfLCvtxoLkiAaJC4y/TYpBfiMnafv1A+OTmpbJ5tWVpgK1CxefSr5oHwNRdbT\nxQZvX0qRZ8PovZmcSJG2bwE/9HaRuErnK+C4exjm3BaLt3KjtdWBMPYHlL8T\nZe+T4JBL4/seP6dcxTmwbF/tR/qGhm91XPlBav65DVV3j1oA4CTOYLffsgSR\nyJZwn69izrr/WWHXr2Mgqm2HYNC0mwglrots21GaO6dXnCovuLm4i/x8NN5B\nDBhh\r\n=PM7t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.1.1_1584978133674_0.9951230870022796", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "669d925bef64daa180dada7df60cc6f31d8cca241a188287ede5272fe0f01b4d"}, "16.2.0": {"name": "@types/jsdom", "version": "16.2.0", "license": "MIT", "_id": "@types/jsdom@16.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "5a371a2efc1e0feec02c4bd2b3ce689e6ae691a2", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.0.tgz", "fileCount": 12, "integrity": "sha512-I+qrFCzB97VlGJgvzGEwcUqmajpwqwTalmMvVfgJqFRVsY80lcCEQEGLJuVvge+whQyB1xcqTstAdxJshVVq0g==", "signatures": [{"sig": "MEUCIFpTdOudNhrZc76h5i71Dx0FFWhnBg41GSoQAv6ujEjTAiEAtTp9xtaUSTc/zPSZBU8Zpyt2VMfVLgULk0HANwWpWhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefiQpCRA9TVsSAnZWagAAtLYP/2abh+xiPBQ2MxhMdf3Y\nElZOaLkflc+k2YcJDJ9xwPyiEDpaU79zFIREED+tjGIFBNYNEArmlNy4EAwU\nSqqVxVWtUmNilT/kVtXvpadth4C6sEFPXDCh2RK/WcYrlgIiTL5Y0302xn7f\njt9gc/7Pxyk3H835PnPUCRs9nME2VDm79F5IXzwv3BUbvF1rVIaUMl0p83IK\nbNRrYVDj1L6vdkUGC4VDbZF77ndsfu9GXfAwqxofkEdwa4dYJZC9cz1fK/vD\nAFBy8Z2Lm+VUXpdizD4VafZJhlNSUeGC5vtzZAJc+1z6WBOdcpiDhIQ/1/4u\niCK95MKHVoIBzq7pIk/rpDTMLez8iDeZ8gUY0zwF55JZww418pr3yzqa/yH+\nAJXMsGrz+OQgEtLHJR8tEYTnT7o1VOOnAgiEf1oL0OjeERwhaNXGhdnQBcw9\nmXf/bkb5bEg1SM71O0OwjLTqKeWwwnLPN8VOvd9zhJ8Z45+a359+E4nJBd/t\nG2A6ik0MzBD6DOgXx2UscyeFRLtabiZDBq/zGASm36F8xRlDtwfejvURV5oP\nO/uWOfZ4N5/BZTplLPxfNnyTrsLRhwK5E2FkvOWllWObXdjSucEB/ITnQ+IZ\nJxitxr8QFH3C2Fvk930XGVjLpJ13fFlZ0Ef2SGB754HbyFRHan6HqqP7Aey3\nC54+\r\n=BK1M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.0_1585325096916_0.6075362876445147", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "faa8b1fdb18b1904aac57af851b806972afb942fc2d87288db93e985c1ea8b85"}, "16.2.1": {"name": "@types/jsdom", "version": "16.2.1", "license": "MIT", "_id": "@types/jsdom@16.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "9e6eee6a578f74eed5997558ab430dbc11aac753", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.1.tgz", "fileCount": 12, "integrity": "sha512-KCEq427OsWfpX7FRyEMb3i2XIuz8Pt3XPls4nmX0iMTDJWsHD4Kzoa3v4Uv9c9IDf11ALeHUtPcyAjTz/HV03Q==", "signatures": [{"sig": "MEYCIQCisJ1UzKfiRAbam6KWSliRLI+vILdoDexNwXZoCqpONAIhAIknQT5WlQbVgGL20TOHxbG+KuEQtSKe5h5dXoPPZyGA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekTcFCRA9TVsSAnZWagAA3ygP/j3x3Se9mA5+bTesSy7f\nOpFemOfsbdmsHLYdYJueJl5ejIW1I+RErzxElA9X+0P/Rm+MuXD7iZ4S+iQZ\nduRWeZgSHQayGx1bRYiRZGuNXGb3dt8XokiAuMrx+3lGbrfe+8mRMMizcg1a\nCx6qkSsFbTIMZZW33HOk757PW6AEnQvzZFcCYFPLGUP8o1clINHQNO/C8yBk\nrValBu6xE6mp6VLMF/ve7a0wNpO17Nau+IxE0fw4WN6cCnYT7C6eMkXdY7tj\nSm0HwOpu85A1QXCE2Yox8GUpXlupGL+x1RnwPD7GKe0ysnOdudepLmUaVhe0\nNIXiJorszTi5h+JewvGSoO5f+VoHcHld6j1Qf1ntdb2nQ/lcL/ZJFoVSwvc3\ntLYPNuXsMSsQJ7VlL8642PDUBKIGU+zMq4EqrIvJWbsmJEoeWAOeXmIVUKlq\nkzNGwn+/ljdsdboKwoONnKSDd/Udj7UWwypwq/T9lPWgttzKBQxNPYH6x6nR\n/bIBRR8iOFTxec4UKjLiRQ2wWMO8ieAzURt8BDhmuVfGdD+a8GX7xNtlRYsi\nyfDWAMvaH3E1zopSKUkn//0xOUFzDOA4dFv3RdH0rOEHPF/sufJFHgRJJe2o\n2FevDAswkjeDuNHAu5Ekt3brMwnq1yyY+UE0ri4VPJ4nQvW/uGnYTijl1u4M\nYDIX\r\n=APvR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.1_1586575108621_0.011045535766409875", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9c58d06e81a4b31ddcff366f447b9b435199a2652ff60a47a0d782933541f9e5"}, "16.2.2": {"name": "@types/jsdom", "version": "16.2.2", "license": "MIT", "_id": "@types/jsdom@16.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "5d820a9578ccb8ac20afecdc141479aa77167dd0", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.2.tgz", "fileCount": 12, "integrity": "sha512-QP2IQ5d65WtIMfQwttqFVFgMNikqeWO8mwUCfLrHDI4ijwb5qGhzPEm2ajWr2rlcCnjWeoYeb7Jx3+mtZGWMBw==", "signatures": [{"sig": "MEQCIDTZHJGWqPsnUw609jSHySsiZMI3GDaMBU8Qdc/bNnjtAiA4cgAO5ffHezSi/lN5IthrAoJxGBsoWe2aTaKzNsiF7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevi/tCRA9TVsSAnZWagAAvT0P/0I4DB6J5B7Uj58eeait\njG7SkqwWduCp3haR5f1YdjolaOGISaKU9V4J2oleacJY3n/oyS0BUj7wVRoB\nwg6UKUnZRhgXZOZaBYdvLf9MmadE4qLbGvbp3C97Pce9LQEdQfokVck/JCFm\nfYXR6S4a5BTaUUu7312hHeBnJXnmmp7+40TuSYNim/KwjtMdk6g9nzKfzBVY\nuGXvgy2+76BYn00Mq3BxhxQE4Gz4lWVLp/QeVoalVIHLKqXJ/nDg0xbcYiBZ\nFDUboiDY8WqVm9bXJU3D56d16XwVzWMjnm8ShfEw1nH3TrIkg3y8cqOK9ca+\nz/D9bLMcf9UQli1gnI6LzpEhewedu2ycPsoiitRx5JYHeKqbhpkiJvj4o5gF\n3rKjpO+012ojK+Rzy31fkoNqUJZIkgWelJnPT1hRPuxrIMsy78F3mufvOA7X\n2oaoMD2Yl/SguDJQ5kW7RlHJ2W3L8zNGgWLFtDN6YybqDwQ5yDrIQh2cBcem\n2M3xxpmNk1HbJsjABmA19H+LlWnQq3JMSu0LFU9uz2TPo9ghNGBvpIwaPhAK\nu2rdalhAhCQKPYLwNhGGaicvpYU7A4uyIf8KejmJzdZCRimYYYPoICqQJBCc\ntttyGxRXLB0Tg6zF1EQ7l2e0TjFk95OB4NI1zHJwCA0fmCdkxuTWqtp3UpS6\nlrJu\r\n=BDVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.2_1589522413137_0.1522515710026049", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "37c9fed8812c155870fbefe6e7469548500688bd5ff78270f6a500c90154fdb5"}, "2.0.33": {"name": "@types/jsdom", "version": "2.0.33", "license": "MIT", "_id": "@types/jsdom@2.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://asana.com", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "63791c54d6153212e487e497eaa9fd393e25051f", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.33.tgz", "fileCount": 4, "integrity": "sha512-iskHLVVSXdvhE+8gkOd5g1lbZ49IP5BPSToHoPmZK/ZVvpoLT1iKtkKiIh+KX+w8hcU4cIfQ9F04rhcCRw6gxA==", "signatures": [{"sig": "MEYCIQCJne4h3S4pyHBGlLQ5JXcgVEHI6tlsznLVVxOd0mNxIwIhANNFJEf6IoR8jdPf8crjxsrinyV5PulmSMrqJJIt1RkH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevjAACRA9TVsSAnZWagAA78sP/AuaJeOGfrJRPgkgbcMJ\nD+ZW9PrW4MnG4Sd/mdDkfZ4OuRB40agkThTux6hDWGaDwLIycLjkKqGYttlU\n7eGQPTXwPlD+mPXMMnzJX90QpyokN0Zan2TBv3IjlFuHCBtdiaoQBaVzo3Tk\n9+e+b/Ow/964Hhok4BUb69IjzYUbbQ5rnYr2tQeY1mlYZ3ErEoCZToIbEQ7F\nCCT6slKW1pFZTs9K+t8/qsSfyoLZfY0l6yt/XAGpwFqrxyd1E+gqbCw9Wpze\ng1N4G+GCPWQN/6YuMKh3U2hUgE79+qI9z/w7gHsq5JaY9lrWrDu+8oJUATEL\nkMWfIfwjlx9R4CyFJJEV5nQP09pbhE4tLRpysYlKIltBDbqMal2HHrsIVEUw\nhY1VxbjPSbG5jKrmtJDKpSd91wSYnHNa31W1Hj8uLLnjo5Z8vDdWCRSWye5q\nwKBIlsk2KHJ0K/5c6qWc+9K7Id3bmo/rH15xUMG8a/Vr8TZ0dzCn0sItdPoP\n6urdq6oA6ni3zLhSbRlKnCUe8aQ3UE5IFpC/px4jaUZZG2mV5fJuWbRLbXUo\n+QJRIUb4UQrWXXSEpzRWek6S3NZbw+BA5sCDOCFmzfFPvBAcnCSE0RmKK6Kw\npekt4wKRrrBzOFjSl3fnh5wNr7Xwip3U1MS5y20gk1NfYCDFeHOlfmDSHHzK\npeYz\r\n=A5EY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_2.0.33_1589522431899_0.045759291404581015", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ddc642bba4a468377f86fa2b3404653f2adf70199a304c5596f98fb5fd2e2d14"}, "16.2.3": {"name": "@types/jsdom", "version": "16.2.3", "license": "MIT", "_id": "@types/jsdom@16.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "c6feadfe0836389b27f9c911cde82cd32e91c537", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.3.tgz", "fileCount": 12, "integrity": "sha512-BREatezSn74rmLIDksuqGNFUTi9HNAWWQXYpFBFLK9U6wlMCO4M0QCa8CMpDsZQuqxSO9XifVLT5Q1P0vgKLqw==", "signatures": [{"sig": "MEYCIQC/s5ceHFS5JdDQxR0BIf6/hWgEaWnnO8rkXfK2W2qTOgIhAKsZ1uetCBc7iYQhNf1e9+SBH3N7vmLpFLwZcDVIu6DD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevu5OCRA9TVsSAnZWagAA5x4P/3Ksaohq9f5rWCiclULH\nqVDRMJoeA/8qzPFNfoCMNHlN8T1ufxWQNzIWFSM5YyqvpwUrojjmBpeghOHM\ns21L1Vc8fj2emgXWObOaJDQtDQHPdRHqnUogAnB2v6g7fH+MkjoGXcpyGOED\nqOgxXwRzVl2cLMbB5Hp8KfROZukm2Wflpnnx4HQj76+XCbxeTshmkkrcqnoL\nMdNAivigVHOo03ZnrI30trkrbhnRlJazt0+T3u679aj0zsCp8VF8SXYHjmpn\nRZ7/eWSj/3eZ7pdsDO9WIfmgd4ooKofhAf6Q597LQSjtS+1lJlOpNWP0zRDV\nFx1W11JzclVBm5FK4la5nImQ48rRUZs/1/fuAWqBoG3Y+aUiXd4BDAYDb9BX\ng2T0MdLejLrHp3RPRED7XD9Wn0g5yf6Yc/qd4I24hkN0k1zQJ03jsK9D8B9I\nr7D3CHYLBa6IF0wuo0+3ZtI2ZgSWxieyNVaS5MYO3g1v7G0V09Mu1uxPREwy\n6WDRAxxBdAD/K0jpbwVSRfleAIS8oEi79pTYV+nrflPvBetuHZc1EC+CD68W\nCgP6Bptgfewy22qaDWmgQ3VFjW7nrpiZlgCiGAqUlYmEjiC+yLg979rMyYdZ\n7o4NSnDyFk3QXr6fz7i/UTmLZmBBnmMXwpO3+b5b/ALG9x1b1Zgv17xjC84t\nTdMg\r\n=GrD+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.3_1589571149603_0.800477651482759", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f260e6c29422dd101f6adf538cc8318dc58a8d40739c0c5b03fb42efdbc12035"}, "16.2.4": {"name": "@types/jsdom", "version": "16.2.4", "license": "MIT", "_id": "@types/jsdom@16.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "527ca99943e00561ca4056b1904fd5f4facebc3b", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.4.tgz", "fileCount": 10, "integrity": "sha512-RssgLa5ptjVKRkHho/Ex0+DJWkVsYuV8oh2PSG3gKxFp8n/VNyB7kOrZGQkk2zgPlcBkIKOItUc/T5BXit9uhg==", "signatures": [{"sig": "MEQCIA18ObeQVyhhhuXJXPzT4KEuzZACH/M+/boNjbXki+2RAiAGxQrJF4KnSorXz3g4H9QGEBA04VijN+xHWpqlpNmD6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTtuWCRA9TVsSAnZWagAA/PcP/3pyOWRm0UGpaq+fEsSh\nWXR78lORloA8jLRmALT9jKYCVwv7cz42GoDsrJ4cq+Xn+oSCBveC/GDOERWY\nq0lrlV6Acs4LpykFdX+wbevMqPo1wd0idDWlPAoqVzHAyaXHJ89UkT7N2wW7\n8OmUqN0sICJhtQSqzjy+iZDnxYQVIqhEM2i9CQNCrqcJBwMdZsd12PgzqMBT\n9pu6Jc8hJMSV0AhxL9Gmt7pnh6jY54UhN70WDBybJ5TsFZGTv3JVOJxoBzsw\nm0Vxka9IRZ65qtPLgSgMnq2w3AX9tVk8n3pbzwMsVUmQ1dvlXt9FK89nTok0\nAteAbfklGgrLdWRE6XfsrbLCE7IOKoRTrzqa+GEi8X9ELpnCxNLTm+aaBK5k\nbXEitMdU2YA90E8XzY1M32X+cEGtRyBov9iqnYNbN+vnRqDgCYgKJfyeL0Mw\n8n3bb9IwUJMJQGmpEGxp9Z4CMLEbpHEmn8oF761p427ODgSntzOICsDffnHj\n7AISEGX4NSdy7zqxd8omcHXtX8uOUKzT51TIw8CA6qWh0tHQ7zJoC3LLFUGx\nLdl2HJ3gCPi5nounZBmzJ+WfH0NDcEnMeVQLO2blTf1oXSjt/BrruqRO7Q+e\nrBuVUZzyu1wUjksEUrhv+eyhzJRaWEaGoUEj8dBdRHF6ar5Fo2rdIIEMo5g/\nBOt6\r\n=Qixs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.3": {"*": ["ts3.3/*"]}, "<=3.4": {"*": ["ts3.4/*"]}, "<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.4_1599003541784_0.6032556141525665", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ec549123e341354f0bc27b899021fc9f6268abd9b37c0e714ddf576c2cc552c5"}, "16.2.5": {"name": "@types/jsdom", "version": "16.2.5", "license": "MIT", "_id": "@types/jsdom@16.2.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "74ebad438741d249ecb416c5486dcde4217eb66c", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.5.tgz", "fileCount": 10, "integrity": "sha512-k/ZaTXtReAjwWu0clU0KLS53dyqZnA8mm+jwKFeFrvufXgICp+VNbskETFxKKAguv0pkaEKTax5MaRmvalM+TA==", "signatures": [{"sig": "MEYCIQD4MTFDjL7/N2wQkIsuE/rVwhfjWL2+8TfT487Lq+D+QgIhALZf2lKGSJdP0Ra5hawoYS7d53i48L07QE6qKdPgT6N9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmb98CRA9TVsSAnZWagAAr0sP/jHR+SwEpsNKk/aYDXbm\n3nPuwqr3E5kBqOft4rJS60l67cYGoScFdcYkyjQQMBq1wp4c9tnrzDqv5C6/\nz6OUavxhyowDCzo2O1yNl0YuUozWmI9vNhwYnXKKJPEcAa1qS7v8zn6Izi0L\nuhXec9T9sPaHK+b3tUfegouEWDhBaLV8IxE2nO0efpkOfwXt9ueCQyMO5mHW\nolkGNnYkz/plAhaSFbXxXyAcPAl1ZY42W/HnWQWr3ZLAq7TNwY2/stAgzgIy\ncv7EhsgJ30QObZWsiPwgCZIZdYqClNi3gBAOWwHO2yBGSaavj2Lq7Gd8cgJI\n49mLpYlBxr0WB7/RCOez9cdlJRU6KY0e3qwrSmY5J2jXCiGoolyXgpp3vkw2\nZ2cfLeWlRLrRKslDLwMJSw6qcukiAIO5s3JKt00fBmuDdog/+8kGvMsjvwLP\nqewlV1w+ibyhLOxC/P8Kevr9WHUDyQV+yM+jE0/EehQAVD4scdAHV6iRJbaB\n+ToLCbAuNjTyl2qEdt9UxId0J8lekp7hGx9ZipJK3j+5yqcgQFzzisr9KN3k\nwVCv1tPD2FD9qdg5BBnnE+kgRLIeVk89nDd1mY1ps/sP/XVrqjkMvIlWDjPJ\nNj+qzUfd/Kvhs98joVf62TrozLXe0ElhDKzPqA2H5WUI1tZw1AAKrkzd0X9g\nwO2g\r\n=iMMr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.3": {"*": ["ts3.3/*"]}, "<=3.4": {"*": ["ts3.4/*"]}, "<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.5_1603911548334_0.31544137385791826", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ed5637b6fb3fcaf107db528ecf77742837dd263f36ff8d9bfb2fe0fe10687772"}, "16.2.6": {"name": "@types/jsdom", "version": "16.2.6", "license": "MIT", "_id": "@types/jsdom@16.2.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "9ddf0521e49be5365797e690c3ba63148e562c29", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.6.tgz", "fileCount": 8, "integrity": "sha512-yQA+HxknGtW9AkRTNyiSH3OKW5V+WzO8OPTdne99XwJkYC+KYxfNIcoJjeiSqP3V00PUUpFP6Myoo9wdIu78DQ==", "signatures": [{"sig": "MEUCIFYxWGQN3XKScj1RmY0E/Pn8Xpr9uAXVxjWH+yFoOdRfAiEA3xI7YButOJKSnmSy8CBN9CqkylcXfnaURfg8GK6M0lY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/elQCRA9TVsSAnZWagAAn3UP/RrpvxhN53ufG4Ibh7FZ\nb5vzYl/1eMeXmBJ+3+cQ2tp7QaasYJi1wXDzsQZSgMrYheQvgBcSboJSDVev\nu+Dz3UlYQRA6KfU0I41jxwBTeE+++uWOl62ocMoTrcPhYDlhX0ERtVQXKwPW\nkUJ8GD4v8e+dDnw4ebwRus774nzSDoKoSz0tNdMvhFZ1RBK6ychCPph0hBFI\n0zWswacrBefzSMQikHIU/Tl8HDu8/jyW77SANz0B7iMMrBZBhVtC6RJxLv8X\nsGoLtPmF7D6Xg8+mIHXm0SjOdHkRT1Jr6dR3RpoqfmdL73CJB/4FE4jwxQSK\nsXY2y44C1gQtXCrJhI5buhDAwoiKtpFKzZSov0b42iZBosaLnf3GwCRZirPx\nHtg4ZSrkhVElySBsJXV8vpNYE1X9jdX7w4GmcStr3JY8IcxJeetMrJSZaM7o\nklb2HcdhYCqOnIwcqLzhax1FkLu/+qAbZSrQBAyHqVv0iP5DXPiugtWQ+lSG\nmbIFais6+TAB03Extyaxj6jVYgXN6oHPv4vKsV09okGI16tvCG1l5P1PAqwH\nn02W7qntG9eTotX+gV4U1GMZXw5quDrG/IbvtmsyptvwRwsX5AJr+IPiWCS+\nqE0puHxrFVK9JVE64kFJzfwPauv5AY7w4DLxCnRUD8YYL27zoaFLsIzBnFHJ\njbut\r\n=JQHu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.4": {"*": ["ts3.4/*"]}, "<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.6_1610475856270_0.4001155274581114", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "509de63c12aac4f4a22054fa85f95e2af9d27d52900e3bdd3cf8c6e81f485a4a"}, "16.2.7": {"name": "@types/jsdom", "version": "16.2.7", "license": "MIT", "_id": "@types/jsdom@16.2.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "27d2f77d655a3db15f7c3f104f1a6d15e3112938", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.7.tgz", "fileCount": 6, "integrity": "sha512-jJ0QDvwZxAO+SninBaQdW6najEs1dCZ1uMsXFBTitwfAtz+0wfDZWd3GFEqkL4flD3IefB+VGBcrN9HbRdAdog==", "signatures": [{"sig": "MEYCIQDAm/w/ib8ifubE/tyopRiKqZUZZwkIASnTeNpW813DugIhAJ3VZXA5mRHh8td2G9HV4mrhM5Sq4/sXO6j14WJmZ3Ci", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgR6hbCRA9TVsSAnZWagAAXwoQAIckWxKq7JmfgtoarDQ0\ns+jVclyOB6p87VWNSKvyF7Y4opxriUh/lOCzzLkSZyTwauuF4kAk7IGcK33g\nCqECzXnEP9RNprDFLhvSx+nuhrzbaemu8sNraJsDn+1hjV0G/S0WBlY5TTzf\nfgaWBgrLef9yZKIPgDW5Y2QzN9P4Y/q/cStTPZTM2KoKLGbJAG/fTmDRmKvp\npjmo8GUC1ViLXcxZxDrWoNEaNwiD4KOpGNwaUjcpNnNUrki3negfGtdRBk77\nFcLxSNcHv9rPc7qrFxxdpPzBDV+Sq61GVctSnEnrkY4nkkS9mJJAL4lid2ht\niTV/p7KvAXBJc/omglwiaEN93nF/lSCATaEJ6DG/hEW1uDkNuH6Lb6aMDYaV\nOrVH5YnY/RBobngnzk4/zuUfPgYiJBjShyl45Bw/lq9udB/r65Cw5NTHzjRd\nbuMt/o1MdpLwo8M4nY6Ouq+sX7YOxNxoDzuDfk2ePEzpdpFJwZ2tudC7tNBq\nqEpqvaS7Areib91dJsX6sTWpdUY73ZG/YTfn1R5WnemJVumNsW1jS/AOX3u+\nyMI/1xPC14wKreE7dqlVrN9oifB/bzjlR9zaC+KpCsNyNejzC6V8N8xF2pp6\nFJRkY8pqcXEql9UXplIMW/H7WtifPn+Ctpn7Y/5n+EEtZ2zRC55RG7qNoRUY\n1FqU\r\n=aRZB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.7_1615308891458_0.008881862642265093", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "94b6bf71d02e83753abdcb3e125ed6bc3ea04135e5c2dceaa91eeb1b2be5c17b"}, "16.2.8": {"name": "@types/jsdom", "version": "16.2.8", "license": "MIT", "_id": "@types/jsdom@16.2.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "f2f06d64ccc9c3773669a26257155504bb0ebf69", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.8.tgz", "fileCount": 7, "integrity": "sha512-s+Ll9m1ZYrIPzXSXUeN/j+0hGjTXD4+ecKB+xBAv2fdvEuRSv5VQJz80+Uth+V9lKvZG1fV+C23EfQpDHaRXMg==", "signatures": [{"sig": "MEYCIQCABSq327LnzAIYESmzk8RFT6ByXx5rs+NBUh1gayIddgIhAIKBVR3kGhjQioknw1oL0Wf4YjQ9gylW4dq9cQluTrSd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21323, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXgVuCRA9TVsSAnZWagAAB3YP/Rd1/3hn+bpSArbLN5Iw\nWBUmg1/QfeEv852O1B7XHTbcK12FVlYut3+LK+zn/2nRoenqyDxvz0EspY4R\nrpBpHkkfnDLTuKFI0oOd+/YjjmFcutF2PkDG93/RupFCodf1FuKe19cXvSO/\nBxVQFzFrOgwd/MAO0+WfKZAfoZH7vi3AcRL56qnl4DrPTr8UOFUQEtfa/4fj\nvWeyKP91KyoAKI0AtcYkhWr13WK/NfFUZr+s0b+FgfFCPi81buC96RcIYs88\nv99q5nUkrChETvS7VsmvKqiR4SCc7857NCcbH4JBVmjZIrNS3lrxh6MzHvpl\nOsVRQDhb3hGYjEvNcYPnh4NoA0VjCaVtoEqvxovz6TizOVBKqIOFrNJOZBLw\nDjg1EEQTGpO3y2poZYpqhdET70A7vfijmTxMUbjQoMpbl50g19m6UTulPbPW\nZONxHBWiZhii1h4adw5XjdFFAo2IksxPA0zRIAL6VoNXwtJ6wNUgr/aFBvXT\nX4jl5VpvrG1LKPvlmVLBl1WoOimxBHOPDgRi0WNXp3VilIhOj6Jh4/7inhg4\nwzN/tuSaImJbZRfeCrwusIKeKpDtzSxnl57E4vqj0JxXcHcToUjhQvI9yhpC\nJwwhxtWLpwWMPSTlQjBmhACley+LLcnNdw/dh2JZLyg3BPlrGf0qdLjMwcqQ\nO3hu\r\n=EWN9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.8_1616774509995_0.0032570271481142665", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cba6efe276ebe467247f37a5fa9817c162a27766a5cc1ffbb3b680c4d3b5525e"}, "16.2.9": {"name": "@types/jsdom", "version": "16.2.9", "license": "MIT", "_id": "@types/jsdom@16.2.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "9c219e5c387f045aae8b80ae4d4cf61d098c15eb", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.9.tgz", "fileCount": 7, "integrity": "sha512-7+HFvB7RIrU4KGazrn0/K81yjdg1hpwshM+nbt7CNlbwN1TUq5sBFMCfVglBYYv29l6ZTmXXFEB2FnxLv5gDcg==", "signatures": [{"sig": "MEQCIBBpqqGBPg2Zowqzk8ufdp6v9UvPoajECUK5j/KiMa/3AiBQrBx9oT17+PeXOOTcag3XyMW8MhrvxJboXwbKppvJUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXzOpCRA9TVsSAnZWagAAt68P/1KRgY45TrvyMfWLcg31\nuB4pl9mk0V04ie0sQQr2OzqAes/VXKZAlWWX8ecdqLX09CYun24MJZb7AiNC\n9jLVEzYo4E5hvs6WueckhtPkjXNLm3IMhi/Kz5D3FnThFuqXR4aypWzRzHRZ\nWagcitB2HVcizCz5VpD23l0+By2L5nmQsdlIkuxFWVZtaJQRHHyUJX1eO+RF\n2hguOdRu+g3kJ0EytdYsqYeB/21ng2Kh0VPlE6mBEkTX+CE14VHoTENuQ5B6\n9JFwqiaXYDCydY62rxzTtjJNwNDqISRnvacqoUesl6EjNhbcPSPOEfuLBBNK\nDJSDy9v79TRs8tOCy+SvbcVx27jxZwwUqc28WhMLAU4cE9llCrGCaKZBvyHa\n40XBUcjuN7BSECIekpboi1mXgLFpKVK3WdgMDX5Qc6HQtw6LPOcyu/NuUhUq\no27uisNdxnFdlhXuUmjWpCvZB3ZkjwNG+EuY68CVpD2sh6JfQ0hb2q6k/Ix0\ng90qFb+087/vchQtBUVWs/r99AQ52gDjljKHFxFdMJGqP5b9Z6EyI6AKp1D9\njmbnlwTK44JDJhAWY0uI+1ElHQLxe2Ob8Kd05SlSIbJ2OJHuV/eUhUNAkAA3\neMpbuiCTWfd2CxNfOMQHNsQl+PrVK33vgUsxf/k4C27wp7hVGrtYCURxY+8q\n/6eJ\r\n=lJEQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.5": {"*": ["ts3.5/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.9_1616851880916_0.2208308864976667", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3f235a7670fae30260300b2bfbcefad665b8325c8808534a92226cbaf82133a2"}, "16.2.10": {"name": "@types/jsdom", "version": "16.2.10", "license": "MIT", "_id": "@types/jsdom@16.2.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "c05ea94682d035943ae2453b79d56178496b6653", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.10.tgz", "fileCount": 9, "integrity": "sha512-q3aIjp3ehhVSXSbvNyuireAfvU2umRiZ2aLumyeZewCnoNaokrRDdTu5IvaeE9pzNtWHXrUnM9lb22Vl3W08EA==", "signatures": [{"sig": "MEUCICn/YyQIpPiT5/l5u0kWUa5BTsL3m8Qy0ZaUTLF1ea6YAiEA/rbQkcdxG43i2ut4FXUHRBq9OzfrV7g8Ao9+ZnX1shI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZ3EbCRA9TVsSAnZWagAADAwP/ApYblYJiR2Iq4bAVvCS\nnjXG4qA1u7mKrrnA10iaUO6Jr3z/RHj/t0/SE3Sk1znPXdrCJ5k5EhLQiFbU\nu+7ZqfXsVeDTabpQ9IgBT6jL8ZZSa9j8TR4EV1WsdFJ3+/7xy+N7gxhSmP+g\nUkSpZA9ywk+lmm/80u+ao7gLtQYkKJFyrooNiYnFxZEGasI64zKnm9/RA2fi\n1de6CNJszEGNx3CUi8HY56tkrhpXPXrpUNltkPXjsUdnUeSGV6r5KCBZG1Io\n4/2JzoZfTjhtM9MQV1isLpjVDXow4+0HBi6sr9QLSC2VO2kr4U1jGsZrF7kp\nUMz5fg7NmDrR84xLRYIP7timhvXjDwVKpdfPaOEVBHDkUP2T6CQYNyW1JqV3\nYd48mSeTUbAUG7UJByhaHbHL2ImmjMEw91kw5PRm6IvPDdRuYJWQcKCkF9mL\nFY3TXf2F/s9ZeZWea+3OiyN/mkHF1lFjUnleliJ+mdchPbCGjEdqonOK7+5t\nzg79FbKAmh1Uw7LxgFeNwXGwfpJpRjZxnfo/o8xaIa70+QhGzLkSFdVQrhFk\nz0Y6l/JAVoXYN0ap2wEf2V23hHzp9a43jixkCqJ5RmhLEGLEnUvIBeBSJQsP\nzr6eCgNhZmYPg0dPtU0b1QTaxSAneUhjKZkATAPTtdaMd4v3gP32u1tO9Z/7\njmfM\r\n=sbWW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=3.5": {"*": ["ts3.5/*"]}, "<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.10_1617391899273_0.3032186142932929", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f091bb05b909ecf601fda087d6605bb7ca4b463510ac2e9c606e3d452d1a18de"}, "16.2.11": {"name": "@types/jsdom", "version": "16.2.11", "license": "MIT", "_id": "@types/jsdom@16.2.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "e18bd29e0678bb9a16ee74d3c0fb9788e8a4286b", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.11.tgz", "fileCount": 7, "integrity": "sha512-Dvtx0zFQOJqn06VgjRur7P5CeSNUOaQ5ivSSvdAdA3sBWzL3kl9OWApQRExKnS3XN39OaZdeCHpoYxVmX6FwCQ==", "signatures": [{"sig": "MEQCIGC9A1wAj6jtfLIbDT/Y12qoyhTtUTq3drjSi1DeFgPgAiBqH/vXP/2uSFSE0oTYkXoaSY/QaiEu7stF2yU8lKW2Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgv9qwCRA9TVsSAnZWagAAaUAP/3ATTxwth/pHGg0VDNOe\nZfdHQyblr1ybi5JfZ0AcpyJ5k59padvFiEo7cirIqhMuj15BSydfVU9v7zoK\nccxUVCKMqJ+hu5fwjM6DrFXWRO8eBfvybNZeI/WaDC7OT+HGS9vS5vfvII33\nQagIu6VAZRQuONWItHDo6M+D1HQNB28Jn+/49JoM53/Alh12Ww1JmJ3N+Rha\n0v5+8O3RuNe/TyQRVkS5Y0HVGmat7CWbOxqPGXGKBFsRd3Ne9oN4mP3+38Am\nQy4tCemSK/pX6nNCZnOEfWUyWjFrsYLq4+GzZTXM2gzCxX8OWoe263T+n/gr\n67+2qpk+Lf1LbtW0woLjHWurEUGVkp3XXNZu6pqpnB6O0xiQDhUhZTsbRQ5m\nMWSTqMub6nn8a/SZT1aVOLGMytiFdhtBdYnP84mb9Rss7T1r8EX+YBXFhwt2\nrxslIBNMV+/h1/Q0vUvxuuNWPYnT1qZ/O08XHppRGIgdXOeMCXIWceM+lju/\nnrp7qgqzgjJ765DNwEMI95ck9qkiUKqKrzc7HHufqzyQxG8cRpB3RgM+wVx7\nzOjZxvVGCzJg4qpOz09xaTcEt1kUlG9QAzOke8+l5ml7jJFnMy3GTbyzgVRb\nIgXHCSDIPapf1HKfqV5pDVOfSKrsO/MwjxyYhkBMEbQue18JGMFJOqiQSFbB\nd8sX\r\n=3D98\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.11_1623186096760_0.822779081000353", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "910bb893b6ea5c8037386d352e4fab949eacae99b088417fe8579d763aca98bc"}, "16.2.12": {"name": "@types/jsdom", "version": "16.2.12", "license": "MIT", "_id": "@types/jsdom@16.2.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "2d47f5c9f7e40b4d5d9b5f0534c736dc475f9569", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.12.tgz", "fileCount": 7, "integrity": "sha512-rD68Q2XSKBYOl38Tb9jbPuqZeTtpw09dFeuKWi7yhlZVfY8rV4xnQmISNQQZfhkQJGpJWp2qCmmrWjhiqDFNvA==", "signatures": [{"sig": "MEYCIQDvnD8iVSwFODh0SDSYS/vc1NfKx28G1Rk3qwWjHAaRMAIhANx+je5YYiNo/IEv3UL2t3YeCH/lvjb+ibUDTtp0468o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1fbPCRA9TVsSAnZWagAAkK4QAIw5omsPJpj3sR0g7iKJ\n8RjV4IyBYlxR9sSma3zaBKSOUxsg1PQRNKI2d38K8d+JtTi7E3e96K+1j6uZ\nhHPDfnTdMXPqADES2VjrSbNP7UcOAc269AvDFQI/ME+mWC80XoiLvTZh/dTN\nU2r7L0RYaJFLNFLqXZ/RBR984LCkUQ+TxBtSJVXFDLqVl8uOtPRAJWxSd2Rx\nlgh2YN5WnaT8A6OvjvKNJk62kJ/FvY8JJ3ix5H4uCVnWUuB4FXTcve/pZ1iZ\nRAytSzZIc4j7j2x6SOJLZ++BQEw48c0KEZ9IHvxBFzwDP+r8Dml88q5SGjh6\nQuopDHjFUZBmkm0N4UrLXrjaMgmNVWsOt0oPq+YNk8j7b5J4NYaHfqLNtE6x\nOBmUh3RCNnsPxuZfixLSGwN6Vl6rS6Hv5QS20Dg1/Kv22jH6/eWVFkthd8Vn\nXlbll2r6ye92QQokocku/y5mKIqjqIr7PRZ75haf5DEb4bobP/4UZURnlPyJ\nh/0znN8z/SD7JScvGB8gZiaib4sfJtdWDXKm8sPb43P6V0p5xTj+DrTJy3yI\nmdzy7mWzGckOzDySZJ90bRs/VkT1GTGMOMk9xK3twS/AzQ6gKs0oosnm4+1X\ndZ/hMvtMmNyEDbLYKIYPSEGPjKfkE7x9V6+4ZJvO0Fu9vyavlS5dG0DWU1hm\n0i1s\r\n=G26X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.12_1624635087357_0.5733895344383475", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f3ff5380388ecc91427b07dcb2b5692510db0dc6a6f12dfc6998ccecb9049335"}, "16.2.13": {"name": "@types/jsdom", "version": "16.2.13", "license": "MIT", "_id": "@types/jsdom@16.2.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "126c8b7441b159d6234610a48de77b6066f1823f", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.13.tgz", "fileCount": 7, "integrity": "sha512-8JQCjdeAidptSsOcRWk2iTm9wCcwn9l+kRG6k5bzUacrnm1ezV4forq0kWjUih/tumAeoG+OspOvQEbbRucBTw==", "signatures": [{"sig": "MEYCIQDothpc6hr1f0ogVag+7Guln71AuFju4iYVKBr55xuGOwIhAIKfc5M19nRiqauOVjflFEDR3eGDZ1WRbFONv3ra0hCc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5M8JCRA9TVsSAnZWagAACAkQAKFvJYMY9DIvM8GusNwK\nxJ6t4KRzVGcj+IWcVpamjKz+FiVx3c3KkpJJG3tMWnr7sQLGk1qIV7L17YJH\nv+4nQWvkpVfMoT4TmRZv2gXrsY4ztbj5LFQYBeLREmDHNCAXMaUQt64kCmIE\nMQKwvFEz+cWGh1QAMaHvnM9x3hbg9ckovklOidjJ0xyEPpcCF2XxM6j+TIb9\nA71ZPANPp0e9s5p51ioys2Nh++5cSqV74tgYxxsZLU4tRBtvm0gg+TOdUSGf\nKmod9i5LKAwcHuTzPt0DyQ+X6dAKhBYWQUjWg8gEIS51YXI4JGyZcKTkYTiM\n8F6xqMNcG/4Cqdq2kZMm0NVr5noOs2/jB9JOGRHD9R4GdKB1v6FJgmLyxf51\nn7y1H1iKP+TvOVzpoJFu2N2oerEUii1W/PB+GUIlgv/8cbbS96maX6iV5LcX\nQqT2ivHc2TXumyf4tEXcg/uKIlEpMIAJLsTUfh6gGaChdOOnYZM2J5Bcwp3L\nByvo79h4Ynyg2ecekhLpAbBsC6VJppKqXA9cg/KpZJD1E3LkunBOxup8W24d\njNWMB6Iaab79wRX5b1MA7IXNTvjVMCXfqmngqpX3+W+en6RF8w7cexS1CTWq\nIA3I2X+hyyq62UoKWd3SrwZ3r1AIjANsEebjB46fW2EHkL323jFD7o+LO7Tk\nTdH3\r\n=S5Id\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.13_1625607944816_0.5215392563411958", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad70ae88db3c3934f967b980e8cc66f94a8cacb3cfaf9a30f31ebc7b11e7c5e4"}, "2.0.34": {"name": "@types/jsdom", "version": "2.0.34", "license": "MIT", "_id": "@types/jsdom@2.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://asana.com", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "e70ed6d3005bcc376c114c9fadce14bfe676c8c8", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-2.0.34.tgz", "fileCount": 4, "integrity": "sha512-JS95DFv8fLraWvX6kSluHhinOnHrQVsGdCPEKBqX1W9WitSSoAalv5EdJqieVlr1H+S0RdWUL+yJxDYhfGajPg==", "signatures": [{"sig": "MEQCIEZTMkXwfHFpzlkY1y7B1nJopVMJmFheA5rVCiMyGQj0AiA4IqlTfPUryCwz6Y98BgnQkjmsUij0uElMxa66+W3HNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5M8VCRA9TVsSAnZWagAACpcP/3E7LiZwlyGVSrRg9vpp\n90D0lc5mh91IPHSHN9HVw1oWQ8yIUpoWx+uCOlx2GCK2T6gRSy9HBxuhwFEy\nC0Hp2HhTnXyvgbpBDrt24g++ixNfRdeljKv0vmo2HQ3if3+JNqzbE/Jq7J8A\nuHP75RfoEqySM+V2vxBlH7O5KYBrIMEizFVG1UmE5Op2OVfcjD/s+nGeFLq9\n7ocjVhxFmTt6GpwuN40m2eSV2UwoN8kMzS29T+evx5o/psOw+igzGmYAmo5V\nQ2zZSb6/Ld2w/AjX0S38hqrjcAylAJIEnRNNJnbLTCLP/321RU5Bwv5xvqid\nlQ0pAkIsql3QhDk2wrDoov/DlA5PrKTtNPrIiqHyCJ+PlcnLUYtDY9IUR/yk\n9xeKuOVpXHF/qZlXtfm+soF+h4t0i5BFaIzTa48eo2isS8EZ4PgJsP9cN3r9\n2lzfHpTYLTaEgnegDAF6bdw80wrsJ6HCIbhu7bKID3+cASF+XkytGA4sNkmy\nAO7TjzBwf6ioGFpObVPBj4ujgbVnPB2tOpZM3oYqJuKcYiV/JSRzgOLVgQPc\nKZGYp+bZGzoKtB0rtkkcMvD6Ic1U7hI4QW1Ip/cMGmRu8i2ffugVf7oaYT0Y\nbZ7EfYIUT7k6XHv28RGbnOeABCUghqWBc4nJrdbpb8bFIagelU8xgq986mgs\nbUSR\r\n=3uo8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/jquery": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/jsdom_2.0.34_1625607957481_0.8419251327229382", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9540458f66eead967947ff51cd6c5c359119e3a8735d26b6072fdc87d1acb4e1"}, "16.2.14": {"name": "@types/jsdom", "version": "16.2.14", "license": "MIT", "_id": "@types/jsdom@16.2.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "26fe9da6a8870715b154bb84cd3b2e53433d8720", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.14.tgz", "fileCount": 7, "integrity": "sha512-6BAy1xXEmMuHeAJ4Fv4yXKwBDTGTOseExKE3OaHiNycdHdZw59KfYzrt0DkDluvwmik1HRt6QS7bImxUmpSy+w==", "signatures": [{"sig": "MEQCIG2tC1ksATi5hxLaU3SpRX2WQbcjfFdKZZTDpV3pnjATAiB3rRT6qknu4qDjKyG1zDCMnOiEHWIb2NLaUoVmgM5IUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwD9iCRA9TVsSAnZWagAAscUP/juuGHOngJbzFfLPeBn0\nv11XFxbZdnxOpfvXTDPQhcnmJA7Yqbos+fVEZFBj5Q/seQEyUgbd7vfysWVW\ngStutMqtRMX7KIW/Fj8ABYk6Ps8tmJL2VNqR7sjwRhCbPEEvRuHhTGJM6mTp\nT3YlhfCtfzlKI0zbYElmSqBfLJSlytXcajBlzvR7lrzue821oLyTy0WY8VI/\nXxUN/AH3ARvryfdks875SQERkAkjGyHBfDs0lE9FOL/Vsy7IElYrTdsB84j3\nbyvLxcLKIQrdBpP5vUB5kP+/leym2tkPkV3mnhzVv39KL8QW0wuvP4A9Myr+\nct3mblrx7kfyyX4uo7hpZgFOxqSEWMUYsed17UgOrqLYQWBNZdcIAq6RZgHN\nD1fi1mQQKiAmViZaGO/5cMvFirCPfFHuS8ToXe0A5eltI4x1wAa0UTN23HQm\n1MV9yeIhuxNvdd0ksoeztBhWPsYQSa9wCRSehfnKdICrkVU0/y/tmrVcyow6\nf90/2yGfU5dZXeyrwAEebLM+9Z6kU/9QLzFPwIOy3/DFMhP2hBFomlwdxPEb\nnxBn3d0GTimCVKvZ/OopScyoxRsPv3hTVIPL5amFMguLO0bSc03y7ks2A1s8\nqmc5+SBUcPYZVYD4WT24qtylSqo8kF4c593N6MDiolvkwAS1my0Bb/KsyBas\nWahc\r\n=Y5PQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.14_1639989090338_0.1431031124339488", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ef776179be2c2846dc0509b57425e1c2ea9d85ffdd241212812db93cf3e00ebc"}, "16.2.15": {"name": "@types/jsdom", "version": "16.2.15", "license": "MIT", "_id": "@types/jsdom@16.2.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "6c09990ec43b054e49636cba4d11d54367fc90d6", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-16.2.15.tgz", "fileCount": 8, "integrity": "sha512-nwF87yjBKuX/roqGYerZZM0Nv1pZDMAT5YhOHYeM/72Fic+VEqJh4nyoqoapzJnW3pUlfxPY5FhgsJtM+dRnQQ==", "signatures": [{"sig": "MEUCIQDdxACxrt/my9rqCTizFZYV+Gd+LXy4K4aopcE+S0gxigIgNP6bZ/tK1ib010wYzy74PTrOj+JeShuLEZXFKEClYuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1/XeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1eA//Q8LiMgeMNxqiWXfd/9oYK1wujRjYmgsD/PkGXZOkhNeKQITm\r\nV7C1YdhC18gtLw1mzYY9iqDsv4y8IUbDBqdymJQQy2EenmvedotFmhb5gL+A\r\nqMgiOw3X8awIYvXyzvswo8HGNITtGL4QWJPbOHOU9es/XfsWgt7FVwmZokBL\r\n+/BPNuNN4Ol5bCHnH10CuxCYx4CESbWnJFEBWWx9mspAI/4bjSB82F8X7Tnm\r\nCkn1C36H7jEvhdwwz9FYmyhaYKXpiw3+vYud0siN/BLh+B7p1y4NWOlXVT4Z\r\nwQakl+ukj6HA/9RDlyl0a+mZLH85G9JYUmWh96kQFvmrcUtU9G5EA64QBY2e\r\nbtz0MHGrtaHc1+0KSzF8p8RyZTPtIbhbKlX1InlQNEBE8/QkAin7DBLvEamR\r\nXmIdabMFnxbk1cfxoNgaIWSg2rfZ5NkGhpsEFsWboeNhXoP2DMMzZkO/guDw\r\n2dK2PE9X/VDDjoI8hIVSN4IkLHRA+zLfloiKmtHGxUos1ZyhmwDBKZiD3m/4\r\nvE1f1k3xKiw17wSjzTfGHBGnpsK6qmSczEcMvytVBVqQyomR9oTqdS7UpvhX\r\nBzRgtHdNtgPZtm2wkfjGEEXso/mBGJPyJ/F5bBUXm/QcaMzEV5VjRaKkWjOG\r\nldVjBqw+qWvlC2bUcEhJ9LNma2fcPkriRwc=\r\n=Q0zC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"@types/node": "*", "@types/parse5": "^6.0.3", "@types/tough-cookie": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/jsdom_16.2.15_1658320350525_0.7963706740866174", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7348b29fae4689b9d47590c95102ccbd3ca0004e9dbeec73873788b248d5c073"}, "20.0.0": {"name": "@types/jsdom", "version": "20.0.0", "license": "MIT", "_id": "@types/jsdom@20.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "4414fb629465167f8b7b3804b9e067bdd99f1791", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-20.0.0.tgz", "fileCount": 6, "integrity": "sha512-YfAchFs0yM1QPDrLm2VHe+WHGtqms3NXnXAMolrgrVP6fgBHHXy1ozAbo/dFtPNtZC/m66bPiCTWYmqp1F14gA==", "signatures": [{"sig": "MEUCIFuok5+QWXjhEWy71QahLhaKM8GYesg651a0FxOnjFaeAiEAzrl1I6BjpHY/t8v6TOfd+7CFBXYBYWp6OnueIRc0w9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3G/5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm/Q//ZwmwU37cdOctOO67WZ6N6XegKBOUnBT779XSfOKZCdUTDu+H\r\nZBZG/Ok8gVR2Tj6Xux4EPLDKTWR5Mh6vsDnQSDkFJKNxf4hamVyed2RtWkvL\r\n01ZjMClKHf9Ro5RxLxD4SHV9xuF8Jbs6iW8vNIRHGAor1l42DcqTQp2f3xYb\r\nFSMGvhhI+WjRRH3VbVOEqqXt8SIG5NvUTdbBDVueAaQBdLDhyHcVMoXxYB4H\r\nC5YrPgS/vLK7iypj/jEsl22j1lWlqLTIRJDdxjKbJU0vzVnNJ6Rqzr7k2Cmj\r\n712qmE1wzcbswhmHJlNaXFfbbhQUPvJAmTl61pP6qyXNUXEKmdl5Tu3GH3tz\r\n6NQ2yh/TkgXi4VQVJamWZ5RgJDmyohreUZAPaZ57Y4pcoZcu8r07HBPr5m/w\r\nsDD4jQZ/y7+nj8JJd7Rx38Cj0JsYfwe5I4R4wOw5UN2H/S3X24kZPd1MJivg\r\ng2QJPzPHkgjPG+sn1Gk3Q76FP8J70HJnWYY92oauPUokZKHkLCvgWiQ/D+7b\r\ni9RkCEyPXMxixhyQBGdM9yGq+wQ6UiOXMby8a+UOqVIPXil7c6HXXYd6jgA8\r\nfI7uUIZ7sRL3A0tT+yB5CEsqKKTswOAHM3xrg9b5eekYhO/Imn1t7Bro0x7L\r\nOLdABtAp5Q3m9QVVTvyJHzb3yd0Ram2mqeY=\r\n=+rC/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_20.0.0_1658613753293_0.10969942500184238", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4bae16471aa39f2f5df8bfb6c676dc7255c9c17fdc7138b8d69bd821e02f4e61"}, "20.0.1": {"name": "@types/jsdom", "version": "20.0.1", "license": "MIT", "_id": "@types/jsdom@20.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "07c14bc19bd2f918c1929541cdaacae894744808", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-20.0.1.tgz", "fileCount": 6, "integrity": "sha512-d0r18sZPmMQr1eG35u12FZfhIXNrnsPU/g5wvRKCUf/tOGilKKwYMYGqh33BNR6ba+2gkHw1EUiHoN3mn7E5IQ==", "signatures": [{"sig": "MEUCICveWa8Xsy+qE02p5f+6YsIBLk1K/NGhgUhOE8wTwhvsAiEA18CQ9Xsr/suLDiEQTUxuri0sexqY2OEFOqRvrgOOzZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22239, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaWxBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZFw//f+Zc8wk4PzpGJ2qiixPzyuvIwntue1jk4yqYnVPHke5hmUFN\r\nQw/napnQpiuxEXzc+Bk5eyBxAn31WsuEbZOqq7xvCQlvtfRq53anqQedh4Ub\r\ngHJf9Ajl2VxztuC9BHKuwcgG4/as6mb/p6CPnTXwiv0d/OyfvITRdp/uPjy4\r\nTL4HSrB8+1jxwtA/ulCsnbckkdiJ5O32Z5aIyXholqchbDcg4JGtpKxb1TSz\r\njBVenV3IDuVadwIeMYifLGWSIyh/5huYWpGMYhVI110CszartaO0t/4/A4tm\r\nVCIhWfcCYVPGc+Fj1Fyud5n371jmZQQIfSJSbzJDjFI6RxEaNhKiS7JA9Piu\r\n3iI4P7nowCK+AA7Jc432ItZ6trxS1u4LCWChORsBsExeyN2kNgiSDBdhnw+e\r\nGJsY0Gd/yzwTp7wbLRhwjc5VfjY++kkijkZ2fDEG4++eEIYcoP75qVDEpI4e\r\nydritG0Q1WqGIVZfScrYIo4ujhk5igdJVMw7GkP0cPVMInR6Qlt1zYkGF+Ue\r\nxVM8rI+gwAkb/d7S73j/vBDEnmgOxXA1aUO9wHx88MPCCivnKnTKs7Xr+B+Y\r\nmjzgV0diWAKupd8qOjTC+HqiiUhRMT800vpBOEfXngCDBIYPGN/5qqPNwuGc\r\nl9IXnabPffrxkP1ZSUyZFYFDPbJdfsKTvL4=\r\n=xA8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_20.0.1_1667853377365_0.6630464582034397", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "91b555382b6af4d833822849e1e5a6aa4ffd52a22666c65e2917bf76520f1326"}, "21.1.0": {"name": "@types/jsdom", "version": "21.1.0", "license": "MIT", "_id": "@types/jsdom@21.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "219f15e3370da3f85d18fe02ae86bda7ff66104a", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.0.tgz", "fileCount": 6, "integrity": "sha512-leWreJOdnuIxq9Y70tBVm/bvTuh31DSlF/r4l7Cfi4uhVQqLHD0Q4v301GMisEMwwbMgF7ZKxuZ+Jbd4NcdmRw==", "signatures": [{"sig": "MEQCIFZ05U4NSdpee74VHRSBKpiLlZKLv5BHVGlRF/B3i3CXAiB9SlRH+W8ssmWkZdZOKvOAp+BwKPHyc7W6lq0Smi6Thw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4MIxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreEhAAn7tgwpce//78IE8jQIjo7rViggvNebihxGzW2CQGzQIyobg+\r\nK4K36OCf6OlDvPgoc+xycZjjM40xeffxrIfaiJHAUtSmwn7N7oXYRBJLcGxg\r\nLj7AC1eU4PNeWnoSjZerhdpOlPId+adM8g1ZW+tOvgCL+ES15TS2J2YxdFNa\r\nu5zgjXHCXBX2Jqe546OAwVcN0OVuDurgcyHH/3u9KCLs7vr9ZBotLWLFDPeH\r\n0p784RLoVLxpa5GMHLoAwOphvS7FR4jOULqsiYXQDou+KrbA6y5f0S0OzIas\r\n7FMVUyXK0Qs3ZN/8lD4hJBf1iymngSgSzblStjUxSTCWPubfqL37r8EKHOWP\r\n9Fnua7UzxdBbBZzDkpl3iy6r1ENvwctREibNapcXalN5CTyaonN+hWBBKOdP\r\nhDJPV/N+n9Mj48+XVaJWQaB4EaxzcOyyfr4lkS7D8iQmZQLwDNugWLoApjbX\r\nmyrOx6758NnDhzugL3HgyGdvWhTduenAFV+2exkJXnlmwFpaW78hlaCn4+4J\r\n5H+fmV5lHgM9mPC8sfoxlfLH2aeKtQf1bN9bfuM/71lXZ1ESh3RbGnb5zzYE\r\nxsZ0tHUVboVt+bjBXTwz14MADdfdIHx0N8ejinABpAv5GeWyv90wSjmCmMNu\r\nsCx8squKhwcZjn3CRuyY5zMm3vjmukhKyh4=\r\n=iWJt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.0_1675674161031_0.873741504707249", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "529b675ecc5f9f257d069f16291e355975265f72743d2a236545775372e2b7e9"}, "21.1.1": {"name": "@types/jsdom", "version": "21.1.1", "license": "MIT", "_id": "@types/jsdom@21.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "e59e26352071267b507bf04d51841a1d7d3e8617", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.1.tgz", "fileCount": 6, "integrity": "sha512-cZFuoVLtzKP3gmq9eNosUL1R50U+USkbLtUQ1bYVgl/lKp0FZM7Cq4aIHAL8oIvQ17uSHi7jXPtfDOdjPwBE7A==", "signatures": [{"sig": "MEQCIDROFlr+v30DPJMKDVrp6XelZ1vpg1btObbCiEz5l1hHAiBWlQi+gbBuGEsumh8hPPoYhKr2riZGhtOGb8bdAyYUKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG18sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVOA//bIkx5+UqP/U4m4h6t9fGeV7TQ1Z0zljjU6W3o20cZeUJsMn0\r\nvTXTphiVkWbDXCo9HizJLe6lieNV6hDSgtaOid0C21e36nAU9gEEqbWDuqV4\r\nYhI/hDIR3w5ML1EefLJJrHiZI33eesnDk6txpSbGCrAG32FVX3LsDRTup6Hs\r\nhuZV60epXobrQa3XNPWWRq9yB0lL+aMTm7Hd36jVvbxsRHBY6011uG9HfxE4\r\n0G2qxHjxRA7OflyBe0qUj6KL0tUyk2mRwgy+JvPHEHQnyiVFvGHHXaL4l5C6\r\nzYZqUipT125xexJiOG0WDqtX+5oe0dWScZ/97nLi7nKKwis36JAo8jctfkOW\r\nqxpxcCKTSvLkyKG+GNU3o94/BFy4eDX7ua3Art/15Nsf/rxIcL7FDPBtKVQV\r\nCisDA0bhW+NsPx+voI82xS63hUcmJQCld2NqA7dHbsip/UHHdwTsi46jqVLd\r\nrtBJk7LN5pWcCYJ8lsnj76/2GmUCvcyapY3ydLCzdVwDapwhETf9ylccdTX+\r\nxkcWKV0KO2WePXTMmIBqzOaFoIdtZoprr9khj7pHXLR36jw1WQb5IKTK1yMW\r\neMKRkmawhdXTypOMNHhOe1LAhFDZpF+KISWTqG4LYCglnvr0L0OU6mw9+rp1\r\ncOsEaqBdAdbB6LNgBlRFmn2XnMTDF4ZdUwc=\r\n=Zwl9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.1_1679515436135_0.5566386853114391", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "daeb30f9d7788be6aaff31989bba37fdb1ddc844ef5e7a5205e0078edeeb3cdb"}, "21.1.2": {"name": "@types/jsdom", "version": "21.1.2", "license": "MIT", "_id": "@types/jsdom@21.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "d04db019ad62174d28c63c927761f2f196825f04", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.2.tgz", "fileCount": 6, "integrity": "sha512-bGj+7TaCkOwkJfx7HtS9p22Ij0A2aKMuz8a1+owpkxa1wU/HUBy/WAXhdv90uDdVI9rSjGvUrXmLSeA9VP3JeA==", "signatures": [{"sig": "MEUCIQDlP4jt9aLaNIt9rIvC4kcC1V0tfJ8wuacQgsHEhUig2gIgYOIdn9lBXdriyvV1aXcPzg1QY+6uSh8rgdpgs//kzLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22221}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.2_1692728222941_0.08195186026089862", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "71e857621bc6cb443633eb61cc89eb604a6f909bd9faf3aac4155b2b442b1d25"}, "21.1.3": {"name": "@types/jsdom", "version": "21.1.3", "license": "MIT", "_id": "@types/jsdom@21.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "a88c5dc65703e1b10b2a7839c12db49662b43ff0", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.3.tgz", "fileCount": 6, "integrity": "sha512-1zzqSP+iHJYV4lB3lZhNBa012pubABkj9yG/GuXuf6LZH1cSPIJBqFDrm5JX65HHt6VOnNYdTui/0ySerRbMgA==", "signatures": [{"sig": "MEUCIEh30DpTpJAABQxGuDW8FcjrxCIDPU7XMt6zx0FLraiPAiEAtaqx9oqssPgbYImX8IFgRhC/8rHmDeKvdTV992KxfFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22263}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.3_1694811480820_0.8061085683394844", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "74ce8008252130e6bf91f5c9dc83fe05e554e9ea0cab6b931e57001eda84a833"}, "21.1.4": {"name": "@types/jsdom", "version": "21.1.4", "license": "MIT", "_id": "@types/jsdom@21.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "82105c8fb5a1072265dde1a180336ca74a8fbabf", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.4.tgz", "fileCount": 6, "integrity": "sha512-NzAMLEV0KQ4cBaDx3Ls8VfJUElyDUm1xrtYRmcMK0gF8L5xYbujFVaQlJ50yinQ/d47j2rEP1XUzkiYrw4YRFA==", "signatures": [{"sig": "MEUCIQDLNq/hkfsuzK91I4MNpz8P1l1ULgubiP6Ts2REfUAKIQIgUehZIQrbsQ6PpWc+IiRwKlcPUykeJTJTfIrAMGJhuZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21852}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.4_1697609264057_0.9230930376138755", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "895956fde53fb6bc6049752148ec9c38a68abdf1d5a9ea1e4d4b11d9deba6c35"}, "21.1.5": {"name": "@types/jsdom", "version": "21.1.5", "license": "MIT", "_id": "@types/jsdom@21.1.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "b5d0bccd2436a2bc166dbe235f1dc43a1f922d40", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.5.tgz", "fileCount": 6, "integrity": "sha512-sBK/3YjS3uuPj+HzZyhB4GGTnFmk0mdyQfhzZ/sqs9ciyG41QJdZZdwcPa6OfW97OTNTwl5tBAsfEOm/dui9pQ==", "signatures": [{"sig": "MEUCIELO6dWAiR7xPazsMysu+ARSa2AWMNuutNQAgS4Gkbq/AiEAjX8CB5Ep3Pjp06dM4H6Z0IxI327x/VLSOjxTw0L8Lbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21852}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.5_1699346685511_0.47481270566172773", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "581e1e1d4c2fd29336c5da09542a812c62ef6d4cf6c20076d3e7ec76d09e469b"}, "21.1.6": {"name": "@types/jsdom", "version": "21.1.6", "license": "MIT", "_id": "@types/jsdom@21.1.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "bcbc7b245787ea863f3da1ef19aa1dcfb9271a1b", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.6.tgz", "fileCount": 6, "integrity": "sha512-/7kkMsC+/kMs7gAYmmBR9P0vGTnOoLhQhyhQJSlXGI5bzTHp6xdo0TtKWQAsz6pmSAeVqKSbqeyP6hytqr9FDw==", "signatures": [{"sig": "MEUCIQDOqXMrwBM5922K5Z3Y+52mLKqYpT8qVIEIqo1nlN67pQIgEmZMvQwp6xrHF/udHVRgu9/riKe0dfjEWIPlhRCjDA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21888}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.6_1700526072435_0.35105236969953024", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "367c61c03f27a6233d260f9ad45a105daa2a6430fc0cd81549c4edbef166c9fc"}, "21.1.7": {"name": "@types/jsdom", "version": "21.1.7", "license": "MIT", "_id": "@types/jsdom@21.1.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "dist": {"shasum": "9edcb09e0b07ce876e7833922d3274149c898cfa", "tarball": "https://registry.npmjs.org/@types/jsdom/-/jsdom-21.1.7.tgz", "fileCount": 6, "integrity": "sha512-yOriVnggzrnQ3a9OKOCxaVuSug3w3/SbOj5i7VwXWZEyUNl3bLF9V3MfxGbZKuwqJOQyRfqXyROBB1CoZLFWzA==", "signatures": [{"sig": "MEYCIQCGEjvECvxqRjevggeyLBa7iRbNAWSY5Xhu6eAeR7DriAIhALd07cZB7xas7YH3H7ED7sFl/lIiFb2EENALFeK0CnfD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21873}, "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "directories": {}, "dependencies": {"parse5": "^7.0.0", "@types/node": "*", "@types/tough-cookie": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/jsdom_21.1.7_1717088817492_0.1270775714607344", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ff2b3302adf7f1ae40db6101f0c6d5f7ba972ee3864ae371e975f9545d93d8bb"}}, "time": {"created": "2016-05-17T05:24:36.982Z", "modified": "2025-02-23T07:06:44.866Z", "2.0.15-alpha": "2016-05-17T05:24:36.982Z", "2.0.16-alpha": "2016-05-19T21:27:11.126Z", "2.0.21-alpha": "2016-05-20T19:59:10.914Z", "2.0.22-alpha": "2016-05-25T05:17:24.170Z", "2.0.23-alpha": "2016-07-01T19:52:28.597Z", "2.0.24-alpha": "2016-07-01T23:15:13.710Z", "2.0.25-alpha": "2016-07-02T02:44:31.418Z", "2.0.26-alpha": "2016-07-04T00:39:52.419Z", "2.0.27-alpha": "2016-07-08T20:37:38.447Z", "2.0.28": "2016-07-14T15:09:04.934Z", "2.0.29": "2016-09-19T17:44:11.614Z", "2.0.30": "2017-02-13T18:30:40.923Z", "2.0.31": "2017-06-02T01:35:52.930Z", "11.0.0": "2017-06-02T13:26:29.309Z", "2.0.32": "2017-06-15T20:16:23.989Z", "11.0.1": "2017-06-30T21:46:11.122Z", "11.0.2": "2017-08-31T21:52:21.388Z", "11.0.3": "2017-10-25T18:40:25.586Z", "11.0.4": "2017-11-09T22:55:08.074Z", "11.0.5": "2018-06-06T05:17:21.614Z", "11.0.6": "2018-06-20T00:11:59.660Z", "11.12.0": "2018-09-13T23:39:56.453Z", "12.2.0": "2018-10-10T17:40:02.461Z", "12.2.1": "2018-12-25T05:02:04.726Z", "12.2.2": "2019-02-13T19:16:14.389Z", "12.2.3": "2019-03-02T01:51:29.346Z", "12.2.4": "2019-06-26T17:30:51.457Z", "16.1.0": "2020-02-14T17:32:38.523Z", "16.1.1": "2020-03-23T15:42:13.841Z", "16.2.0": "2020-03-27T16:04:57.094Z", "16.2.1": "2020-04-11T03:18:28.729Z", "16.2.2": "2020-05-15T06:00:13.338Z", "2.0.33": "2020-05-15T06:00:32.046Z", "16.2.3": "2020-05-15T19:32:29.764Z", "16.2.4": "2020-09-01T23:39:01.896Z", "16.2.5": "2020-10-28T18:59:08.427Z", "16.2.6": "2021-01-12T18:24:16.398Z", "16.2.7": "2021-03-09T16:54:51.627Z", "16.2.8": "2021-03-26T16:01:50.147Z", "16.2.9": "2021-03-27T13:31:21.073Z", "16.2.10": "2021-04-02T19:31:39.394Z", "16.2.11": "2021-06-08T21:01:36.898Z", "16.2.12": "2021-06-25T15:31:27.529Z", "16.2.13": "2021-07-06T21:45:44.975Z", "2.0.34": "2021-07-06T21:45:57.620Z", "16.2.14": "2021-12-20T08:31:30.485Z", "16.2.15": "2022-07-20T12:32:30.674Z", "20.0.0": "2022-07-23T22:02:33.434Z", "20.0.1": "2022-11-07T20:36:17.541Z", "21.1.0": "2023-02-06T09:02:41.190Z", "21.1.1": "2023-03-22T20:03:56.349Z", "21.1.2": "2023-08-22T18:17:03.136Z", "21.1.3": "2023-09-15T20:58:00.994Z", "21.1.4": "2023-10-18T06:07:44.239Z", "21.1.5": "2023-11-07T08:44:45.748Z", "21.1.6": "2023-11-21T00:21:12.586Z", "21.1.7": "2024-05-30T17:06:57.669Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/jsdom"}, "description": "TypeScript definitions for jsdom", "contributors": [{"url": "https://github.com/leonard-thieu", "name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"url": "https://github.com/palmfjord", "name": "<PERSON>", "githubUsername": "palmfjord"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"centiball": true}}