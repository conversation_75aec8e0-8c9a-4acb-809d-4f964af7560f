{"_id": "@babel/plugin-transform-modules-umd", "_rev": "119-faf6491622f5a7a9cf3de695e7118934", "name": "@babel/plugin-transform-modules-umd", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "90eb0973bbd4ae82880d1d273f984df4ced613ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.4.tgz", "integrity": "sha512-9DsBLct9QIbmdIWlczTJu9yawJNGXf76h0tfi/SArx70+VJELehVXxZEFKmanqno/r7zqzC6fa+gMOSwtfMiOQ==", "signatures": [{"sig": "MEYCIQC06L8T88RPZOK3v//MZAbdGBQxy+9Z85Jcfc4HHVRQRQIhAMgRQqKrPb8573xXUVBcIFpLw8wl5A0ksKhmWqAHF7aT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.4", "@babel/helper-module-transforms": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.4.tgz_1509388556091_0.15537641756236553", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f69f68d7ed33db82660603112470f7a4a5a5ba52", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.5.tgz", "integrity": "sha512-zSt6fHjg9M33Bb955YqjnMD32BZ/zmLT0iaIYKIjFJJ6BygfGmKIANKRn4nYhy/xd/X4D8tZenZhrFmKbaNegw==", "signatures": [{"sig": "MEUCIQCyDjjaQp/0UCY/O3hRcz/b/M3vg+3nCID4DCL+iKYBRAIgOB59MMG1Q3RbxpqNbqIxgneI1wB3ZudHO4HsA5PoTWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.5", "@babel/helper-module-transforms": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.5.tgz_1509397054510_0.8438116684556007", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "77442041bcab86bea0e61bf2c705f672926240aa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.31.tgz", "integrity": "sha512-k2peRidDzQ9lc1xPPTEHK/lwBLYO/Z5moyhoPilhIM8e3dQ3enjrewUJFMPtkOl7/SumiLizq0euR3W7Aud9LQ==", "signatures": [{"sig": "MEYCIQD8Ib0r8awZxOO37X7E9g9WfomodvZm4+YNqoe+w4UN8gIhALWgLROmDWKwvhM5VPzvNhiiiCdwnEuccRWMbWj4rXZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.31", "@babel/helper-module-transforms": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.31.tgz_1509739455782_0.25404730765149", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b1e59f96e44d047ff2d9a1cdfb7de008ac342359", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.32.tgz", "integrity": "sha512-z9KLuONpgUz1iBbE2iWCfT1y0GF+aqideaqXlemLg4oqudd52EBfopMMicKh1/2KuIQEG+CQljoURTikanUYww==", "signatures": [{"sig": "MEQCIA1Q9j2u033D4KWX9GaLWF7JplNr6+nHj+lZn6ews/4uAiANdfQKkmaW+mgluMWCLkBvHI/IvcOBKGJlozro8laiWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.32.tgz_1510493642755_0.40333725954405963", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9c6c1f4805d75b661c239b0f1df96d63483d87c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.33.tgz", "integrity": "sha512-HKy6XjUn/xNF1y5Ug1z8iR9USQOpSzl/SBoVC6dVmeQartiav9IMcfwFGF6SDdpi388/zcrZPPE2xBWjFbifKg==", "signatures": [{"sig": "MEYCIQDZmgMtTS9+uimChAiVqCuZFPfQJM7MyEcanvaoUCbNjQIhAPrj8xJ9L4k3WqD6ZMFBJxgE8Zs/KpyGUR71K3QN2HEz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.33.tgz_1512138560606_0.22978757810778916", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a9f662404cfa39f8e357fa67fd25a483147c9374", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.34.tgz", "integrity": "sha512-3mcs1onCT78fymeGPzxhOriit1nMNnR1iIS6xNatS7w2tsldDY87bEUUhfEC1xUbuSEIKk8QeEoKth0o2sPU/A==", "signatures": [{"sig": "MEQCICGWCeihtm6H4CtDv9OErhrJIyLf6PmjuiIOl35Dc2h+AiAW1lCScpNe6q8t3lTFYKpUfX6V1UyU0D09E6JqVICPJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.34.tgz_1512225618139_0.9823362657334656", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ca3fbd3f00752310a75c9a97f21f37c1df00c902", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.35.tgz", "integrity": "sha512-dWLdxQVQGbgdKWkHkgZuIGVnNiaegqzR3aURgIYIVL3qF2Vo/PnKXNNGnWOSBTSq/lW1oVRi+crj0XmcQR0r5w==", "signatures": [{"sig": "MEUCIEV9DoduX73K1g1+BHFQk4J3wyVQXK4emvLvJxAGOoeQAiEA2+acF5VpWI5E04aj8U5CEG/i3ROhQN4qfIwtuHUmGxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.35.tgz_1513288108790_0.021865101996809244", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f9b354478cda5321000e803ccd57d7f5332a17bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.36.tgz", "integrity": "sha512-d6WWXCwA91HKxFXpgahlG1DE9UH1JQRWkIFmQjsbQ75kQHWu9WZQ1dXwo79SSmeCeVe/p+gV+4Phb2iXt4J6UA==", "signatures": [{"sig": "MEQCIETwaAHkWeQsySe8M7Xh4Ke5P4PGT4fqO/bKLCDrahyBAiBrOKApM1kxqvdQfxd3V8sdvSSAEaoZx03X8rPpjMb+iQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.36.tgz_1514228741147_0.6634290714282542", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "937ac9cf4f00e162d4a5e860487ee89c389c2907", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.37.tgz", "integrity": "sha512-hR/rQrbs6mdojLWEn7qSjdJd1Ulz9m+wnPdomKopam7jT7LQZbrorH0O1ULlvTnPsT4UAMSq8fCqC17gbgvi/w==", "signatures": [{"sig": "MEUCIAY7sK3DPvjrE8A0/P+kEDxik8bwyENkWZfEypGo0xKHAiEAszrUIVYMSsjLCWXcnH6LH1MBGvhLeIZF+0j/NP3/UM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.37.tgz_1515427424347_0.07125174440443516", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "72647506835faba43f5df2b07629482f85a86218", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.38.tgz", "integrity": "sha512-3Y83CIc1fybCmdYe5zQQ9yP7v5989Gg9x/0SGE0s5Uuwo5g8hj+t2nahvE4b4Uq03NYYnmClkhXi8Va7wvM1Rg==", "signatures": [{"sig": "MEQCICjSiZW5kFpaun9l7S+sez9iVt09rVabrc7B7l5nlkp9AiA09tcXMFyO4K1Q2IsUj5qjaEHPVU6467iZiywRjiSeWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.38.tgz_1516206762338_0.4703825581818819", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "25a68675091c39a5fbf72e0d76870f3b248d452e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.39.tgz", "integrity": "sha512-YIKLakaV4hbX1kgI7f0FMtp4sB2Q75M/vSg/LaVPGdrD3DQ0rgyAujMEA9/uE3rgPT8w6l7Z79AC7aBe6CazGQ==", "signatures": [{"sig": "MEYCIQCo227V2oIOjIZNTzUuIQAeMIbpOA+ZpSSGlVlYp/HkmgIhAIvFELWaPeBj7DSVTXluGr41/3GnyhtC5QnjGteWp122", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd-7.0.0-beta.39.tgz_1517344125882_0.27203660691156983", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5bd4e395a2673e687ed592608ad2fd4883a5a119", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-LHKqJFwo7x/CeEwjLyUE99SlG/kbTl8LS1DQ26fWctVnW5JuPt3hwYrggnmo1L/g/dal7EP2IL56+UezDMpJUQ==", "signatures": [{"sig": "MEYCIQDIjFSitkTwwNGbtiU+HAcyliTkO1cFBsxzJLMbgI0kBQIhALgau3BfsrtENceq0qXer2vAt2sOOWEBYvc6KyowLsou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12217}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-module-transforms": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.40_1518453767834_0.8379098410734458", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ba74d9d23e7c4c7f74959226ccacb4e57c57ba57", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-Kr+Vwh+gdJYYiTBWnYqyMk9sMEO0W4uIXvcHIE3MBT04VK9VokXAEmnncVzhASVfDS5Vw5HSpEwz5G1iWT5oTA==", "signatures": [{"sig": "MEUCIBQT5kdcsBRU9yyBQDi/BRcYkUHEsbnLe29lp9+xtnv3AiEAwDwf8OTFpB8dbwTs+acuF+07qX6DuF2mxbgNNITiFQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12425}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-module-transforms": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.41_1521044818197_0.7078211638393874", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2fbad368c83471c76f8dcace98492e4e3fdddc76", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-yOA2qnk4snRxkdgKDp6xN+by3V3z8q14cDf/aB0j36Jv2YqpDWUWMMWLQjabCEE/l7ptePBS4qbFH8dffCm4Tg==", "signatures": [{"sig": "MEUCIE+bA9bO7Xg6nUsP5oqCs2YV1PpUeApwrYDFD+IUcuC9AiEAnrvQL27rP2jaxpvW5keSp5Okn2YuHXHXT1aeqESgbHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12425}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-module-transforms": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.42_1521147129087_0.8833481383001791", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4b7995cd7910b28235bb9b00209369faa7f6387e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-bzq7seeH4cpaBDHIpLVulM9yYhoY3uBYaQLCWsidvgoFsom5Be9tTm6+ACvvyYIiK2tLDY7HWXwD4/ykVsYEKw==", "signatures": [{"sig": "MEYCIQDuQaRejlneGatV+c+dTx5lmCZcQ5DLJV3Gk8imFAF4PQIhAO4/V/MCS2+Yi3HJQLOQ7OPIInotJ9BbDePWFy/oMKBO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12097}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-module-transforms": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.43_1522687738820_0.6542721393685726", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "66ca82476b72bfd1ce2d410ceaf2e85c1639a616", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-thm2inPP4aSdYRUauR2gVC3s0g5UGK1kUns7DBpZQBSY9eJjAr5LqmXhQh1Szq79vI1ZRZTDoxrTkdFqubAALA==", "signatures": [{"sig": "MEQCIHRhudMCXZdGbOCaSeJbecBrHXwlIrfq6GBJHADpAYC/AiBCK/M/gZzIOTTAsJp3ZudVFfa1qJ1TQJT5gh1Oy4gE3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12984}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-module-transforms": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.44_1522707639293_0.5228746126480175", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "48b51dd053f13dc4e4eadacc9237f572b0b2f385", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-i4LLBJVXaSjn/8UDfl/2AANdHcVP9QcaoS5dNASxMhkdEit9ZmBhoHYb0ETcJ9j3MRBr9+hFMd0TmLgWMJ36bA==", "signatures": [{"sig": "MEUCIQDPvMbOOs7AniPj1GaJxqFbfCQyhk7OW+bXUm/IG4hbywIgTlKkzHI6VbBa0oWn35ZujvvHKcA9QhOIpDwAU/MiAKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3MCRA9TVsSAnZWagAARJ8P/RNVJeYlapYnjoQ939j3\nztnFftO/I5wncFlHoIBR+E/f69ecvb3FxBzJgW3h35+ZxgkYeX55HH0ih8al\n7mL7ljter190RJiYZJ+wAI54VDrV7XICYWX3bsxB6WMbV5XCPzdLcSQS1h+Y\nZBNkXmoM4xjWYF1HXW1yBRuAQAra0kLDdwGM54O1BfdunG3oeHZsys12Y7Di\nGIOrTY5ZF2pRQDXVKinGDYjnztsrRiZk5veSl0bD/0Mln3lWdM+NHH2DNWIj\naamQjpfi9hKMk0aX0Xtub+NsbBnWO0YSU8WmrXOepqcPg39o0if46xLftp51\nm8wUr8tY1onmdBgeky7vx5ISOynQsBLxZfhuU6NqXsnOHH1xnaqOp2kJitaD\nw32xmZwWdKJiPWqQR5th/1ep76N5GTtVuIXmCn3bwQ9Zi9e7TmotWEI0EEDm\nk68beZy8TV0git9Uclwi0x2rvc53IrUQaCg61qoxhhBlkePz+SN8fqDfNJhA\net3i7NUhHiQFvqSD99IYD0LiIbzia4n8WaLIPUPSLLJkdjMb5Sh1EYjBXoT4\n/eXyDllwffPRxl/tcxM4kck1IJ/S/9TwUyf1K8LNzyP2xKs/UxsyZ/Ih4pVt\nHmeP2RHFQEBpwZfWdnYa3roRDVo10QdwAGEXI0bILzxLwyipHl7WGgJ0OUHf\nvghD\r\n=B5yH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-module-transforms": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.45_1524448715685_0.5272666429518451", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ad0ef488a123f479825c1ffe75c5bba9954a449c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-t54bMSIY5xtb8uK8aM61xZmpjQ7FJ0M/8EiCFDdx7rHff5O9eJUNEGqGaly8ZTbdp80RqbzMkyiD1V+TXYQW/Q==", "signatures": [{"sig": "MEQCIFdYO79RCGotrCQeN8BmVBNQy6x330lbfnjDkesN8yxTAiAMq+0bdFvLlAuYrDE0rGfQXW4xvcg3xhJCCkbZF9CaLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WH4CRA9TVsSAnZWagAANbwQAJPsny5XKs5tSjSz58mj\nuli6RBfmxLivphwPNj7DT4IySlYQStEPQiBmCHA3wTMQfjgNT6g7vGNjPth+\nd30Abxed8IUYcyfLkoR8KOZoy4sWF8UhEu1tKtYoI2UpP8AbxUD6NZv4RsG1\nWlClIT1OeK57jaGmA/AsnXyVwwf8L3bRpEF5lPqX0E7VqxVO1KIeviBWVz2O\nwDzbRsrRT29JLQBKMkDEE9gwOc8TWspB5gFyMnLkE4pOkiDB74/n5v0eWkjB\nLzGupHKx7UUmkInmwGNcQmZp+Y2f1F1PT2uLorWGjww3M9Exax4WFqeCQkPK\n+0cNpqISCRGoGocmNAwxVRBaZC8fJ0e0Gl2Zv4zPGA2HmaQmflHxlO3cNp5C\nJeU88UIJZNVrDP4mRi4w4vb+Z5989+HtSeQE+tLxePRWzUUsFqeE8IGKERfV\nKyiEf1an57eW8C8BHtJrxdMVCSdiTCGOejMJbKGAZGmdJipPHQqtie/LsOWQ\nPy5m9Sq8Hqr4UdQCOJEybAOf5ImopVx6RfoUx3XAUwq+faRLqhQlGn0WVerF\nm5Rh5k4HC7yg5b5qlkqE3ztXh/KcV3m31kJC6Qx5vy1n/zqXZ139+oOsTKJS\nZShcQyMmO9FwhlO2aszipGe18kSrhnvaL39wCMbJOB0okZbXP8uWtxkxNRFS\nHQiJ\r\n=34eC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-module-transforms": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.46_1524457975546_0.43198838108674", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6dcfb9661fdd131b20b721044746a7a309882918", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-rG7KioAFCLxZ33wNBqUoxPhtXOmVEvnZNIy9wv0fSbNIQr8lO1avZ7SeBL3OZduNvLocqrESt9Xhh1nzb/zOvA==", "signatures": [{"sig": "MEUCIFA+ReopHDVj25saQXPkDTfuKDfzQ4kddpVAMbBcHRcXAiEA1EKZczo/Vjr+TAwx8yZpzpB2ScQHzqMZCYcyMXQ0kV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+ic2CRA9TVsSAnZWagAAw4EP/jY4m3724/mZAK1U7OBO\no4EAc3GQOvde4NsNOwBy51ZAlVK/NDTmg89XGLMnuXy+oubhwKPAtf4Xt7Jt\nXLJODu1FyMML9A495FkpuWQyv6oArsV/YLiWjQLvacEAp2IH/u84cTm6Hdga\n1KkE5+XjqDQSkGwbH63D4HJq4+rSKeHdA65BsZSB+/tI4MSLN8Mx5WuHeV6e\nS2CuK8+QRpRsB5RQUVKXg+JlKYdyOXGKqPQYzQ3fZfmLwi+THTQHv445Gulx\nDHHdXOY3l+HcDYWmF3o6AedWS5Y4GyU7of1gJGeoB6pECv+AjBH3B5Y0yVRi\nIMuPX6dFxtakvtwrM1AGaZdkfcSI1zoUnWAzNbUtx4c8qoD1Y51zvoW9Sdj+\nbWD6Dpk8BYFaB/+v4z8rZp6sTz1Sd6yjcONSrYiHlwhBwCvJJ0pbNlhdf+EQ\nELf+dM5W+rs0SnCnbHEyNC+Aywg6Y98C2IGCcqGXGb3FS0qXIMdy/uSKPHGm\nNYEz7m9M4zSj20FPc4GT/tbXEnPaNNkuJEGSXEZ8RAiQaqYmEPP9Rqd76UPh\n6+F6XRtwddloCtGRnum5n5bZfBsfLW7hM4d4x90o+2ZOb2dsfvst9EZhZJBE\nTzvQp5oX2FamLkh0cpyFQVDKbBxwIHSeKxUPgF7O0HIpqfFygy3LImIFR9QY\nX8Di\r\n=oMzx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-module-transforms": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.47_1526343477109_0.01924168298140394", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "00308c7cf7dd801873447d86f08804e258846a09", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-l2waC39Z3yAupnMYVs3W7bXHySoyoBcUD4ojLjBlj78Vn1PoYkq7+38MmtPuGBusX3ZbCGK90vur6EpE3SBjqw==", "signatures": [{"sig": "MEUCIBqaGWYZJ5JfK0lZiP7XSWyuZ7a4LWmiPQtNw+rA/+jqAiEAkWQnEb4uV3iDV+Fjjjzh3pNCfhmWkgRZNSXUjHxg3DU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGCCRA9TVsSAnZWagAAo9sP/RwBx3oRD4gTrOF5juaW\nVKb4DVc73+JYXgJPT//nEstzlDS9yeRLR+ohon02KKY8VrnWt4SZ7ry5XunZ\nVDyF7xUjBWdhtNM1dj6z5fXy0d/H7T2ekKpFRBcmWmzYE8Llv3yUT/Wjc6AS\n/V8h7y9ooFq8VRvn+hNh8KG2+mSKlTxnbF990LfSK85fBCJpaqC3YzWRYIpr\nHwAxhjmvAkzJyNCGwvzx6RKxzlrjzLQ/muh4XyTfNosU5Br6N+RpuSbl5H95\n7BZKWU9F6dKnbn9mr/EAJKi/f2DMxXN7aZCEYzdJs/k83j7IFN6eOOkKdm7E\nH1BOA5H3Yo3G0Z52YWvzc6l5HUIcRMuOqkjpKlch8OIGELc6zcLvh6/z7tx+\nYfkjiyDKChOzHZEC00F14e0xb/olUq/hSJ9AKZu0SX5SH7Z+h7PLB0j9ZpI7\nWdDsmNaRTCV+d6Qocm/x/ftdZSrWx/wMhk/3II2XtXlPcUOZ0a0sBwsGWRob\nAc/4Y1VpGb1fs3r19RNwcPJNQQzPEbZIx74fBUlvp+ahisAZ2xN5BJDnreaP\n+iugbM2WTp3KDdwYmchlS0uJyhyeWAFCJSVO2YAg93rT41K8S9mFD8ukaOas\n3TU9DGwga+U836mGOmUGDVo42w65qke1gt4qZ/3sC5VhUufXuaO+c2OAL+0o\nOS3g\r\n=kc1W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-module-transforms": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.48_1527189889895_0.5589106668969153", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7048ca5a77189706f4b3e96e4b996eb30590dd63", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-aQYA37WfBganZHKUdH6tzoXywW3ipdHo/NAMSR8JUcuN04FWOPFANKYKPvkC690Acz1BV8kOyoCYYG5bQaSzRw==", "signatures": [{"sig": "MEYCIQCOFAIxtVRuYtutrfWaXe6qJg6SYmYlHCbMFgg8WrJ+sgIhAPU9WfGIyjXsgJjbf4VsIIX1F0tanG3V/K+VPaPrewsQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQaCRA9TVsSAnZWagAA9nUP/2AVFEGc0cE7VrteDVt9\nofdShSHJBeL3SA1qJ9TsCV8qawpn9Hum+O80yXRrYRgzbsMVm93WEzjFQJsj\nTngB/lO5MyZC9SBkpQXnIenBiPrcMrFPORnp4d2diSZJKBJ3OWs9qLRCUF0E\nQTi5uIc6/L5j/cVkmdOteKpaJjfpy0UXxB7e4y+hUDVYYzq++VZkj0fX60yK\nbBwlr7GRcsnLlp+m7x/ohm77C4YzaZjhk9PwHw6065+7IGSsMU7ex9sEUQuO\nPW8K3L+UIeiZlCouMmLd3HVx25iIh0iTL6t/hfc7x78pupGranM5YDkcAE9l\naQ2LvOmrVLdpJxtNwKCXAgiO4nZ/nv/A/K9pZzBX1TMp+oSj8Io6z/f+u6H8\nnvljwOqVI3NlOOnYIVtuMO3Q/3gN9X7LC2I2t1LTQYtjjcK2Oz2QaOImPwB5\nIfr0r88s+1okBK/aTwFFmdbjD9IUysg0LV2k46LRsLHdWlHU94YkztM+eYyF\n10hRfc9UVXTPATGn0GP5L9hktlHxutlMpy4ZfRpYBRwBdV//kvvsldYXy4eH\nlREpb+jco0B7GXpYWMIhYChqRxhtmZrBjOSZFko11jMGSh40SyycykcHChBz\nQYHRD+ViZMuiHaSJD9sYUVvSBwaTFwxYlAp6zTaX5kPc88/LDfXwB1t6tLiW\nNaC/\r\n=B5mU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "7048ca5a77189706f4b3e96e4b996eb30590dd63", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "3.10.10", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-module-transforms": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.49_1527264274290_0.5186776491912692", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "651a3a8e88a1b0773ecf2aee227548f4c14706b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-3mdAnOFRbmZTCUOrl1Yw5ZaDAr371FZhFAA6hFBukbdsL6hHZHvpWdZaibnvvN/1vJw411XcQddnz1hxy/Tu3A==", "signatures": [{"sig": "MEUCIGtcOVpmYleTfOXzHxhjuqbLD+N8RNQhKd3+B5zxvODmAiEAnWowshKig+6mHYCzm2TM5P1VV44/csss+UVhrdYlSm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7887}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-module-transforms": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.50_1528832885244_0.18246724442031992", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ee2ef575579d96e40613fca6e6c8edb5cadb6c6f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-5QyLg4sCxlo3GwpTqu+rVxpiGshky4LY8pPJiaNqkYiWKYbuMu0V4XdOYqQp6/VVuWcLAboP9kQt+cXCOcB4sQ==", "signatures": [{"sig": "MEYCIQDP8v0cyYtXRF7dFayOhaRVq5bdMqV0+BF1N80muxOy2wIhANTin/DTLF6yhEanHlG5WJZIDmlyJtYmJ1dTqnZfYskE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7901}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-module-transforms": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.51_1528838443604_0.03128981381162621", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0c5f7e98eaabb18b5ccd500b5f7d23ed3c2840e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-IpdJhzSMWpdwIMQrTqoU6Ffaz9Vdtg+F7A6yKAYPeLuuclRUSRi5ud4wDke07NUSY/2FB9rrE+4WdTnRI92cfg==", "signatures": [{"sig": "MEUCIQC6AiggUeGgdr+75HhjBhLmsY13m/8kGJWTMds5eJk8LgIgZSHeh4i52aDLyvg+Dihu+EjFpGfgr6z3qK6nZTPpPhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-module-transforms": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.52_1530838788209_0.27538512363239787", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2a36abe40a1da676e43a1c3071578e27bd2d679d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-TXX2R9rZQJbxyJ2leae4N+pT73t6Niolosa0WyZpLTGBmjKrFRHm/vpCm3v+tqb0GZMXbMQ/vzCRlMcQD8tPTQ==", "signatures": [{"sig": "MEUCIQD+TimVEid1+e4BgfxWS7O43Bx+RmzCciEdjHZ7raiyjwIgHePNvddwZaTVa0NjZh0/EzE1DKyf3Demf/q00MYBnhw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-module-transforms": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.53_1531316447770_0.890238685752875", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3af0e2cf8f533b2984a8ca6da316246850c3aeda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-udiLoJmLg1ySVrcRcLJWK89SrA4CjfVM4SgAO8mpqbMK0KD0tcl5wCsF5PAOgH2yvhJFjit5eOZ8sSUZKOd6Nw==", "signatures": [{"sig": "MEUCIQCc0AgvX7wpy7505Kr1VqfAzrTgRgOvONZX1pMHoWBtnAIgAW9727dMAoTpUab2T8mE4UXasSnJPTksV2JqGUZ8EK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-module-transforms": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.54_1531764029123_0.5582706800640909", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "028c96f64e89313657c6d5f5ff0660fc99f6ee0a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-esX8+1OBJuS1ep8sOFHNDt/ci4DlPvkN0/tKpn+7YMgu4NONM1MHx8dx2YfKGLbZufseYm1WvP6JuZ1lpwSlAw==", "signatures": [{"sig": "MEUCIBSE6owrGzur6kgVLek6EdmJK3Y3YKuTa5d4DpUr1VfCAiEAkrmijRGrTWQIgvtphUG3zwWA8ww4mzt5WZA/NPDUQtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-module-transforms": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.55_1532815674229_0.9443410520788469", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1dd72699fd8fbafda4c259318771d97f91cd8410", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-0ItEhFLodomK7FH2FgK+UAphXd95UKyj3SXcRRhbUifvpfnqv0QFgaffI01iEyFnbxdXKpZqESHZR1fV/w1igw==", "signatures": [{"sig": "MEUCIArZaCif5cFsLMM6QgkUZozyo9pwZg6JOVLjtXfz2/SxAiEA6FiIOgfXnZTpK8KYvGuyClIGWpVN8ymM/76ulFgHhrM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPyhCRA9TVsSAnZWagAAuNAP/19DUbAPoJkQcnidFdci\nUZBCn6EmmTbtDJHdtNTQPR/Gzsk+vju5UhRnMWukwnQijr2vx9GkmB4rdrJy\nM9/HhQN63xuJYNzy3Kmxdzj1p+oPjCw0asfSbNNXxf1Bfja86bStT0fQAJGl\nPfyBTnLL4HrEc7FJr+ATQECuJn9b25Zm9OSddG5SLWOLwNX3R5oqUGERINQh\nDI8CeKQCMILxTBT/h1UaY7cN6pTx9VBY9ldViTYQuPpfkCxb32nKRateI0ll\nSMV5d0pLS3a8lK8vQLszW5GRyvzGEjbFNJfzHW79H5ya1jcrjPuV4+AHHsBH\nBxIo2S8Cyk+3uiITvQ22MOK7f2+XMcET22j6SecUhL3FnWpYeF/T8UJAcxGU\n6K2TLG5RjMEEzHzdfa53qUPAuLDLSQpBo9DoGHtagqM29Yo3UcMfV2Jg9SQ3\nh/ySsLEciEgsjxImNzE2H59btO2yOfixRTtfvLq/QKKzaE6Hzq/VDiHzbmI7\nQ3ChgujQml6H/tQN0pcM9t2KBA1xagCqBBCWTLZWd1A5CZhX9UxQtZxeuOzD\n9XncVPF9ZXOO/Ka2pjUcmUQdKOXFBb4TicYxYXeDwP81Rlk7Z4n81EScVKwm\nt5h0ELIEhxQn6NpzZyiW/VPLsQjL5EQ46BHsD1dmK3fY8DGe4BvZtzLTdWX4\nm2yR\r\n=4F6D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-module-transforms": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-beta.56_1533344929127_0.1512682223299846", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "086fff36aa6c4acd975b3f7f46cc52a0d43dcbda", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-GbkFrGi1yfyBD941e4PtWuBYLKECbkRTSiw4FZHG/QOkZWY6d9JHwuIpHjzcFRPrNr9ZEpSak8e9jIEDHZu/OQ==", "signatures": [{"sig": "MEUCIQD6sPFCR0lybWq+QHMpsQf4SqJ9iyoSFLB//clRYunWlQIgWtYoJFF3Tfv6o85I7KFhTQ8cA8deKDDRkHrYlirqfVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUHCRA9TVsSAnZWagAA+6IP/R3CIqN1lSZ4hziFmO/T\n54QySfjHv37oPfJwhHJqkVsM7xONy3RD/+ZN1JojunRe5f+r9N32JRbCJvBQ\niwnDahrXKO/qOjvqktto8ymD5FDpkTihVN3nY08Oa4UNEOdhyX8+Rm3X7iEE\n/4e5D3TUwzMkzolWkj/GCO+xOmbp1sZc/bEqAOZwN2I7XG+JtQWpIbapdR2S\nKkc47WenoqxwvmECwAwNY/yi//phFT4Uo7CZ3PbkSBd1O6IhpjVFV7EM997b\nlQB/zwIvcLarwgBzol7O/1YWivzhkdhq4/lipBu9KwaooYiMafuXUn5xxCpC\nm/mlliMP36i0voCD8uJ0lUmhlA+Rf66rSvuTK8EKHK1DtbkI8FYxbZbkWuBb\nZGhETcNRogJq3aurJV/DPit0jeLDYuMqekUiGG8NCmUXCXMK/Rxe/2qBDrQb\nraliOnraJqh0V11gCLKXCAzJeE5vZtCoYY48WMMH8ZrF7EeModWgwSIF5BIU\njyBm1aHpoFfHtBadzMuxfj1l77UCwFiZ4Iym14xy+Weh+tOkbRpw1EBax8L6\nbO39FvpIbmfntyAN34sBcku7Iswr3x9n3THpLV9pAcyxMnlm+DdYWvmIqGUu\noj8BRmEO/1DyBycn7cNeEuUsM1mZy9AV2Sryic7Lbz8YmXQ7FQ6Big5538mV\nEQ2R\r\n=BuVe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-module-transforms": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-rc.0_1533830406948_0.4358534686811233", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1a584cb37d252de63c90030f76c3d7d3d0ea1241", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-wvhxd77dRxyQGSEqfSRfe6dEBDy7Q13MaC1RKLX2H4+SQKZPvGuNr0BS0CEJ3Fm3uSEZ7potTBfRO4YNAygjXg==", "signatures": [{"sig": "MEQCIBWsyDeoCRwsT3ux27K2+UNPv24PZArSz4lJcU+hTPnWAiAHW4fWXQN2YRoH0aGUqQfE8Nu/khpW9OwLq3afhh4+7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+WCRA9TVsSAnZWagAAYoMP/2tZeo2GN7Ha7JWPNh6L\nmSFzerA5gTLGZsM9DbZv1Lxim1Ckf3pRoXZnGZ+0zrtNRi1txlAfidFcW6Fw\n8sYhGkuVeg6ThcDW/GKaaf2UydjEXJEpuWg8ADCZt9PDqevQdxCZyWv97i6M\nOpjtJ93XYMcC5boYvFI7jeJ+/k92mQ2v6OQRER+oznIXSwoUXjipFJf6Tz/7\nhHkR+f2EbwCUfzoa1wZTV+FwWWXe/BnFlfyw9p0zY801OV79Mek/lWnGtuQt\n3cnHx/yWaStOgqYFYe0gHyqi8Ad3AIbG3rc+12H38ULl2Y4CBaV8tE81MrK2\nBazPPciySYI6L3OkxpYzzzye0DHP8LSCHREv7ztUpgBCIuFJ7j98V8s97NGU\nikCOcn2UKtTRBsCfgvU2leaboX5zq5Y2MiiQ9F9fHDxZwU6mDkov5pPzY4qo\nRw/JRWEm7wbnooMAUMpwnXtri34rJUoOt9U6UINxd8Q2zFosw0k68VDzMShW\nhJkCMiOvfKQpUsNmRpGvi1Il87nzpiltzEvd+1tbM0fef4YLkVnxMamQmahZ\n4PQleukOg0GbyN19g4J1A5CeQHgw5wjpEjVoO8IYK/EDPQf1RP1cb9+SLXO2\ns8oT1upPg+OZWo7CJXiipX43kCVcueIVFkRD6Rt/9IQoHKPMLCAad8J2sDoz\nDodz\r\n=5LsC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-module-transforms": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-rc.1_1533845397676_0.32666855193221966", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0e6eeb1e9138064a2ef28991bf03fa4d14536410", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-4gnDpcZ8/F9eoYZlQKAvh5wzax3UQFmZEBMKJabl133iTLb0aXkd+yVJGmn7Vu/+Q8wRH4kkH6Kc2pbsvlbnHQ==", "signatures": [{"sig": "MEYCIQCZMZFcjTU1s5pqnFf1O8XlzSAgUoWS3+1uIK/D55a2FwIhAPH6HbVoBhxWVRBietHDtJxZVt70PDghchbi4bCoJNiH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdKCRA9TVsSAnZWagAAhXIQAJDODTvRcmeP39XhxwL2\nmdUKwPNdFPogYTy4guVvRhKS983PIkiw5uCH2HLwNVU4hC5UQPQqEYwgqLFW\ni84ibD1EhszCdmVo6p569AWJZQLONHMS6FE2N6hPgeoIvhNHhPO9+Wdgd84l\nUfnkQhfoG1EQbbQULvYfz9elIllcHqwtnpxY8rITe2okjV1m2ok90k8dinXm\n2DsSPpiJIPaaMtzBC4NyYFGKubfWFDTlrg0AruBLwxeFTbicY0WywgZsqQn5\nI7374DMM2V83ti/apIyLEtif85LJzeQoDlWhFDNfK2hnhdz33+HpI3/4w8LU\nzauV9vD2IJQP0ZykhBXJN98f1vdD3JO2T7Nn2LDk41knCoH0DVYrSIgna+UY\n2X3ACPXwaKEi7Hr8uVuAUEhovLBo9ZlapZ/vlcVjt0lpj0GKwkljONRPdRkF\nrW02jib3Lf65CHCrU52PxcD34/0HKSonDwdgouNCsM2SAX7z/CMHqYUAiQ/J\ndF6p+BOux+RJf7IxcnNaX6rPNgRHDgSGPYFXfvnQ2XoXL9VlnRMYZ0YoEBHl\nMhQTyOU3dpmnt6A0dD5pXNZC3T4KbmXG3/t6LuzyPeh1ZI9DuYprQt8GMeys\nTEEsq/f+BmTNqCyvqhPbyl4TYGjN2aXgsw5J5UWZu9+Ysgc2aCIQWPlSF4Cn\nPmA7\r\n=t3s5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-module-transforms": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-rc.2_1534879561706_0.3027945157219636", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "35d04236716708b620dfca12f009b961b531f1d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-tV0HyMKQz0Lu1JmF460xB+R8xrC2NUSlWaaU/mIV6BsmCsoVxDmgrfwqP7sAYROJqaGAXRQKWURv5A699U9+JQ==", "signatures": [{"sig": "MEUCIA6A41Esw1ZejICXtP3e/FCqJSxSi/LZGyULEKPiYMttAiEA+7/rfgNBWs76Fl2bTrMcZ8O/vxjHb65figTgtympfK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnrCRA9TVsSAnZWagAAFfEP/jwZ8sVn2DlYXpMXLCMr\npbLZduZXzBjH+ObBUybK1YCB/ncs6jTq5G3R13RUoxm1qx1H0n80szskYlzv\nkw+lrbbjv5YySTMigO2s40RJK0d6QchVaBEFeXU6hbVv1uG0Alzlmig5GLro\nGjPHt8xg8dUiPzpR+lemWWOGA63/Omh/pKiRITKYrWKPPcpw8Fw/GYjalBOL\nLrt2vbSUcU0swR3tDdHvg6CspkTKJ/jvaHTP3/bx/gzCvrCm2RUViERbiGS/\nPO24NHJ9VVPDBrApxrfzru/E8HWOmUA68xbnRqBpATFCv4ksyztsinoEvIpK\nqBN17P+yqBoisFiFehsrpE5V41BLkPtjCzKeD73FxNosPihRVHepmbIwlrp5\n6TWx+QZsiLByEi8FTSHIH9LVCvaLCrNZYQhvl3JAWJ8BJ3bmH/zYPeYPXiLt\niRdNQ2OpBqwM+U+dImOL9hOOTCFHnsZkn4d2mVtJg5v63JXAeoWXwrTrMLsu\nQ5B0OMVoSoqUOFxQxelnppv2IMboJXxyUNsKbcriQ/nDBKMfGnmO8Ako5wCa\nI64gqJjfmECPELJuk/5frtzmc/EcAmHaWFzrQ7lDLZvN8l3acFjS621Vnzi6\nu9NLhPSMT6CsbJE+RKXDo+Tl/Zv1jEggxbIrxJ6r1sRjxz/RdrEt/HaZFkya\nwSuh\r\n=4ZB/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-module-transforms": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-rc.3_1535134186363_0.8188853652271841", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5ad45d398e42cfee3d12d9cd75336c1be0954265", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-FdLFuweOUDWrSh58uv+wCBgtHyNQUQncR0Shxova+G0vdmXcZBJKVmaWX+iEVEEPnEoAWasvJMhDpVkYD0CnDg==", "signatures": [{"sig": "MEYCIQDE2o6OQrNxOK2g4Ij7ZlDmuPPcbGWAxnbyL2VKjJ11YgIhAPwgk20xkhRNay1nCGGiHxaYD57MCsjbunU/Ii9ZuiTf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrkCRA9TVsSAnZWagAA4XEP/jiFsCPq6eQfEscdM9Kq\nQC4C80KvbjLzDDXyZH3LMZZR/GSR3aBtUM1x8l/ZFFypXtAua5VDtdlLZ7d+\nHo75vQIakDUUy+GUE9mWLe7s0Qqq/zgbD/9/csi9aWM3uOAl7MLWIRwlzYbF\n5Wpmzdhwfq1IHRv1d574SbrLYAzzmqR8O9DYDgIdRfVDdEBy7pH/8VWjNG0l\nMew661STRfhQFiT1e+R1yVAcBaxZp235DIzvSw0VAE2MwhMRMGpP3y1mPX4U\n00GGRsITSZTN26wl29MMozfitKgeU2Ob3vdNyQzljUjjSysUqRLV+I7dciAe\nl58/hQaXyuxHyyY5CgZwkWOCouLO/5F9eE0pf45ZnkIxOGZOiCLC3D8yDIgP\nB8OQpqO1dKUgwn77e0stJE2CU6CzVi5ng0OTt4f39DmNtqdycVPQSJxR6Swh\nZ0y37oue5Y0nHPIrMVcPrAJrlzkNcJcULAUSAgthmjLUINuX28lSNCPLo2tu\nKA373bseX60j4rhXhLnrOmXwyUD9KsujtpIzol/JGTKwEq1en/W+KEwL+teW\nXeTplI5VKwwSiVFVQ5U7FlYyyimFbjAryVXrTkBpUnRvgWpwXxl48f2UYqFC\nmihR2Yj6iy/FFhKie/nxfWYzSxUguefWsY3A0WkJZXLY/SrCykafumPuXLUR\nF7qq\r\n=3VsA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-module-transforms": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0-rc.4_1535388388392_0.6489379240145117", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e7bb4f2a6cd199668964241951a25013450349be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-EMyKpzgugxef+R1diXDwqw/Hmt5ls8VxfI8Gq5Lo8Qp3oKIepkYG4L/mvE2dmZSRalgL9sguoPKbnQ1m96hVFw==", "signatures": [{"sig": "MEQCIAIZNRDiIfuThn1iL/qZIm2PHcp/sgnlsw4xbtDRk9/mAiAKqSwykL5nn8KBz9rQTXsRrXjD4g101X0w+rE1jzMXLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDbCRA9TVsSAnZWagAAJVkQAIowj0CSXzPQWbgZt3rV\nW6yOgLAaKcDazOzJn2h10suEpg1vBRL6TaYZvZsvOCd5/WPp36h4E0xTHkN2\nW2Tn0czd0ZkpjcY//eo+xgaNSxWM1UnJfYswzy7DJAIDiO2KXPEZNt3/Cr3O\n1UwEVv/yqzt0tKKrP3gNUjX061R+pqtppzPKhtJgiWw29nfq3NLWecFZZoTi\nIAB189GiGXpXIRax3KnbQ7GE95lEuzt3aOCOPk7AzDGgaueKErG9PziOcKB2\n0Weu7KtvZlrtvk4wE87ktUp6h8hWA+9zjtCT0/r+hHBXb+IItrUfnAEBJcDH\nlYGQ9i4sE7J/KGIuqgSVot7GLPo5/6MdW8DVKdIOhb6r+KP4vVCzYf8KIGC7\nlttIeHOPZacUoeWB5APnyRIAkAePGtXehcZbZryXbn1dRYBxGH4rH5mQOnog\n1nl1BiisWvusC6PwVjHEb25KEdjw1QwLpm+4N9lLsEzDOxZ5Gxj6IYx5Yyoh\nqzrHRXjmXhoRM4WwimLf+8/D+NTIQmLlmU+DXtGUBjlz4XESLT/1lLK0Vfoz\nFH5Z2H1nLYQmucoRXq1evLqIT7Av9MCJC9Cim/hskcpvKE3Ng2ALIYxTzl+c\nSBbbtI1CNcJCFN50ytnz1pEIUkHm+SMsWDnmspX7ca+26vXA2QW1OfeLV6lM\nUNEr\r\n=Ljk/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.0.0_1535406298964_0.6925653507266452", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a29a7d85d6f28c3561c33964442257cc6a21f2a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-enrRtn5TfRhMmbRwm7F8qOj0qEYByqUvTttPEGimcBH4CJHphjyK1Vg7sdU7JjeEmgSpM890IT/efS2nMHwYig==", "signatures": [{"sig": "MEQCIB5KY1aOCALRMsdJLDy7OSxCmuYlx+yikVkxWmQtlOg0AiAHc6AwVkLh+guy5HM0PMYf7PdH779QpwjINBulyoI5mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAD9CRA9TVsSAnZWagAAt1gQAI78/V14AnECh66bBp9D\n9CEi9ynLi/qhJY8/SLshTJ6AfTtoZ+BHELFfeiWj7rDEg8/Ma0GcJ9RWs6HH\n46NmiMJgDIQAjleT9sj+M8rQ+Ws6ES66uEyUwcdbvBZ05CtUBuXq7jnDjMjG\nyCUqqGeVeeTtfh0hmbRsvRBj54JdQd1g1QLRYEPXXd9v/LnfNJ1uDa5n6inJ\n1RPvPZlh/q2nJBpE4EYLr9Xe5dqWnVQyfBuomQs/DdGAjCO/CvQak3vgPt5T\nbn1KiRWuc6Jbnh2Wj76p+ohOElkKBh2yRnFlx1NIBeQIfsdhCxy2d8Tr2S5g\njpWXTkM3u4gQgs4skYtvgLSaWqP39+hdRvhbHGcwOitEirVPeC2a53uzwbhO\n9SJbH9s/eclikT5yFvGWKf+XqTkcZGPMdj/vFsrBAOTLg8D+Hu3lCYK4FDmx\nVfrJcwLbPnOcLuI79qbFsAlabuZ3mglQyxMx5GdOMWrsigpdXnAt0SfXujRC\nkwBzsJ5WPZj+dxgmcEq+qq+DTVQsEhm3VPp10WASDpHdP1VKDSULDK7kdKof\nNNdvvY5eEfGomg4xT4ia5GV6CgfvuZMezgCBMgWa7V1AHfqb72Cwbro7Gwae\nUTmm9bGYLdH+KwmtJ9ocTG5/lR8DKmBNNhFFTuOi7wShJswMl8HHn8MnbQos\nocxx\r\n=WME7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.1.0_1537212668346_0.46001257872112666", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "7678ce75169f0877b8eb2235538c074268dd01ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-BV3bw6MyUH1iIsGhXlOK6sXhmSarZjtJ/vMiD9dNmpY8QXFFQTj+6v92pcfy1iqa8DeAfJFwoxcrS/TUZda6sw==", "signatures": [{"sig": "MEUCIQCILaINazlkyi668bAWlRej+g52cM+nStZ5STTHnTw6VQIgM3n1Nu0gyqfen2GfcGGVIf6WPrmEYTHLA7nAxgecUdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2MCRA9TVsSAnZWagAAsw0P/1rq/bPCu/Y1qL+5Biw5\nOfHKDYQDDN9UNPqsqwDpSG0yW59aHPy3MENd83WeGJBQLL2vIxij5jsxpdR9\nU08lmovPMQG5uAUnn2/Mn3DMWuQcR0anuM8EXocBYcRofuK5LDGycsfBEVg5\nnIcH8yPsv5Y1LnNCNdm7Q0PIYOp2zX6Tkunnjrs9cNUwBqhEwWre0OxdGAfc\nOjSvgu/iytcC5Tik50MDFlne4LHrU3KjuiajMjY6xfGhxAMFrnsxNwb8l2Mg\nF0mKO0atyzUG6Pj+4iVlq2HUCUJTTNXSLdMNs5E40tsk851FOkNGVLfpejn9\nhyWLG0UdPYn9Tjq7RSmw3Ra7ZyoYpm98NGGR3WHdE+dPmf8y/bY/dWxdQgCL\n6q2UIwzeLWQhJjviIPB9O60N7GqlyGlS6joUcoLSbO9lIXZyLbY7EtyeXReB\nM77YkpHCExiHMqyB9BiUkspPYpP2hQeks7eQ8qmcRYi06vjOO4+YPkYtmZfq\nwvcZ76u0nt9RpZYxEWDeg350Vpj7h87muLvha5fWfEtGu4J+rRj9D4BI4vfK\nlsVNK0Nyf9WTvPl1RRzYJRTiYRGM2ZtkejwtQFV/si7IYoeEky6669/jKpUs\noc4ZzwmcgTmaM1qii8DlG8eB2leNpnKdB0rsf5brWVI9YO7CUShi5tTrQ1Dh\n7uIG\r\n=YYNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.2.0_1543863692031_0.07471315493480546", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d62c7da16670908e1d8c68ca0b5d4c0097b69966", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-u7eBA03zmUswQ9LQ7Qw0/ieC1pcAkbp5OQatbWUzY1PaBccvuJXUkYzoN1g7cqp7dbTu6Dp9bXyalBvD04AANA==", "signatures": [{"sig": "MEUCIQCzKxmBvbJZah5VoayxDbDc/G5pH2ij8Vo585GuSQAJswIgOiYOGdRL41SqoXZ+FdkxmoDBVVCdq8zIAAtjw8LulDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTBCRA9TVsSAnZWagAALN0P/R+eWXft5R0d+eRw6MpO\ny0/TRQNTEticIP1PomOvPXC3pecVRS1u71EKWJxAm6IRQGQ0O1USzs+NI9/5\nud2EuKyrhk1NWSiabnc7RzdbbS2SqPuVgyBYcbkfBJK0pd4zHx3LHECmV+mm\nv21d6dC0IA8tIDckv7EYRLcSRckgw8nF7wiTfl6GZacW5FG+xko19DvBHUCd\nN9mLoY6ZRJZpLxKUodG5Q63o8QrONL1+Qzs/MLQjQMyXFGK2Uo/Mhgi1Q0uY\nTFilqaLcKmWTzyvC6Xg7taJm8AzA6gL9JKJ934BG1BJUnNY+H//T7c89lJ7T\nUghS3Svv7540nHBbeeqGNrxIeHRdJpJnfblsh4YfUO1LI2tyO6EIUvuQv61M\nKRZaQegfWbu1Lq1bD8z9kpjJTZTgI94UtZe5QZSgmz72TEweKB2iNJgw+/ki\nKuBohrXF/ReDYYzfGV8EvMEfuWHNg/AjvBIanmlwFUfHi01/Hl+1vOEvZRYn\n1tBVf6SqT/xjrFO3CgqkWkFvn/dazLCDeRPyDCcU+Wyvh/msPFXAqFGaQ05a\nwuq5U6G8HZ8LGrsyx/4uvePrG3/uU8m1QBiFO5xsXgB3QWrkYFi4Vo2xYQFc\n7a4TbtFd465fjSE53SoxOosASq6A+0r1+4Yq22TbChd/R43wKfu4KUFsKvr5\nN/H0\r\n=5QZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.7.0_1572951233342_0.07795183039217446", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-modules-umd", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "1027c355a118de0aae9fee00ad7813c584d9061f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-u2B8TIi0qZI4j8q4C51ktfO7E3cQ0qnaXFI1/OXITordD40tt17g/sXqgNNCcMTcBFKrUPcGDx+TBJuZxLx7tw==", "signatures": [{"sig": "MEQCIEBJeQftBzjUvtnZIiUoyMb8to2fpYDgiSGwijlnl5GbAiA6R+yDkB1Zd1be62lOQ6gt9OZyg7sbcX187WKzzten1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBeCRA9TVsSAnZWagAApUQQAKRAlU/NiM1pvKUZZoUf\nKST/nFVGE3JisOpHkIrIjk5ur5/6hkZPXwyMbixoexd8EOOCZQJaKI8hshQQ\nYl0b/C8uSkJyndJyb0uGGOF38mtCKSvVTQhrPfTqk5XI1yvkgFYxNcQcbaNz\nBvoODbWnJD7w2vNVPih7K2BNfoRfY1d0MiasS+5ksiXEDRXJgOA6m2RfRTnR\nAicIFa4wbH7XRUl312DfqPwB2PjViafpDUG1XFh9CgXDnEGQsI/H4nBBaVUM\nm/fat9DVAwiLHJ8RIR0/AdmKIkttC7F5dWybNZFlzAsjUtB4S0UhMF3dqHes\ns1MRnFzl6nPiLM/KDzpGpgIBqXlVHfCTeeTUAufnxGC20DoVvTrwhvbWzAkd\ntDQqFpStwJkg/OljP7j63hmI5OhK6+0b8wXTHMyH7lwOLFwIs1FYAztjqJrz\nStYl78UqP39BvWFjuaJ8IsvBBaKI4RjJp+bbQaYPRTW//mar0l9jNRYuym5E\nPMEfhVpuvfOXAMC4JroABJ4wZFFSYeYlAy2MdIBOzrG/EnmmVLpC0VdUluhg\n44dxDxxwrVyKj4KgWPwS65yxxN98cqMi8eq8wGKIMP5VMW1PK+dBOzQ72Y5d\nw6j7arUfGZ/Ijk//DUNxgwzOElDwjNZ/LSFiwt9v1UgJ7+SwcJIi/3gofYEB\nuJDm\r\n=VVFP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-module-transforms": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.7.4_1574465630165_0.6411305647639631", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "edc1a7a587a31a185070421f97ae3798b15df9b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-lAwNfXwmfTy7fl2XOyoVpMXnLkJANgH0vdSYNFcS4RuJPBtHfunGA+Y0L7wsHmfPzyVYt8sUglLjaWtdZMNJNg==", "signatures": [{"sig": "MEQCIDJhWAkN7HfywEhhnXIKLqx/isTxxkzFBfTPJS/U4UIeAiAMziYe6Os6IbE3BMNjS5a7dNBrsHHd/FwOX9vxE93AzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWaCRA9TVsSAnZWagAAQvoP/AiAcqoE0TqR3MuVXI5l\niZ+YpUwB0VI03CswaOwsluCI5FmSDMgw0VV8MyvWlATx3Bm1WCAHKLschsTC\nF8v7QyQIIShnCga13QyBrr+dB63irUpRTKITj7cnTyw+vE38KLX5XMR86G3Z\nzF8P7DLxrv5veWitgh7RbMTUhHYgGOFa1zVsR1+YkX9BOJQ90KYblVqjJKta\nazJ0wv2/2mMzMqnXU+RWNpX3AJcUDLojd3SWMehUGPolyhTTZnjxOAh99n4M\naIt7gaY0EzUpqGnqZLKBBn1lkrBg6gLwvFxNU9U5DP9dNvS5sbgHa1iCmGmp\nJJTqVlhN1MY3UI2R67Kjt96RNpdjTBYj/wemv+WAxRvNtfyc4mXxpw+GSm0E\nCEbzbru7izkD9S1MO3U1aPAI6U47QPiehT6D4Sdm3YKphmowApuA8gJ1irnZ\nPHGUCorbUWFBIr2uRRz8jbP/db8VvT8tygYn+taeYt6pwJXQrGDtUkWTPLFW\nWsrZx2MDYd968Y8au0RfZBvVk9m56yHTIoFbmRIf8BpV89e9spVva1ne4QhV\n4I1D4L83norvmEwJsAY8o4C+KsUWq4Sxsu5X1wToZ11NPuR8yHA40BAU2765\nBaFLiIKzNN8XEVfHoHTyVCzJG3fxkJwr6WCVIX6WsnHNU59BgQgSEQASJDgF\nQPPK\r\n=l433\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-module-transforms": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.8.0_1578788249674_0.6822510354904348", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-modules-umd", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "592d578ce06c52f5b98b02f913d653ffe972661a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-evhTyWhbwbI3/U6dZAnx/ePoV7H6OUG+OjiJFHmhr9FPn0VShjwC2kdxqIuQ/+1P50TMrneGzMeyMTFOjKSnAw==", "signatures": [{"sig": "MEUCIGdhtQxUBv4KUIDqQo0HLWmM6zy0BxKdvyyn7fAgedK8AiEAw9DV6cex3FnkANRL9bh93vXtrD7Xr50euW0BcXvOueU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORHCRA9TVsSAnZWagAAZU8QAKKecxtsoD8ctRDMyfbf\nlIiDL3wWuK9X9KenFDvKCzsMtMXO432fJoqp7gGp1zKh2cKukR4wH50GMD3N\nb7sjs5/1LInM6mwmDf7wJ1nvqNjpu8YofC1Sl/bycGe6MXH1Zw/8P/jE3adf\nzwz9a/WVzeJJLA2m6TtKVTS/k8O1yYlSF0TpzNCtisl7ejQyWMnKFHq7ASdY\nrx2LoxS/E3olFdZVvwHSUOUl/fYROsw/sO10TuaG92qC3mrXmdL9/iqbwfbU\nzGbNnK/4SUY+jLxThRRzNKbLhSsvVYX4zmHqWSYDXDtJn6DX4IoT3JHEjI4o\n4hfcygIDERGfg4ZLcg9UbAhoN0QGe40t1JlF6cZgCteoyJWOwnckB9+rOJE4\n3naowxBsFu8rfKeBi2kahxkN92Aun6d9Xpe+5kxQeUAm096cJ9UUH9xtYP5n\nmJMaYshu3T/eJ4BDDaqySm80s/w5v4J3fiIPyrtWeeaP6NpBtrngfe/V0NPC\narCM+iQtkLs84hzmDzHPrQpDLhhXLi36HVKlILnoBUQcDndPVEUWKdFwJPbF\nE+l0SSuZR/s7fLnCGhaN7V+Me61TXe5O0ymaIX58HS15EqY7rO1H+G51xwE1\nUYCFoNCj9UuWu8cIHCMVmSBnzUV4VSEYyknLC7YcMTS9dJNUI920GNUFkxT2\nsl03\r\n=qBul\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-transforms": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.8.3_1578951750832_0.8436470214269827", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e909acae276fec280f9b821a5f38e1f08b480697", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-uTWkXkIVtg/JGRSIABdBoMsoIeoHQHPTL0Y2E7xf5Oj7sLqwVsNXOkNk0VJc7vF0IMBsPeikHxFjGe+qmwPtTQ==", "signatures": [{"sig": "MEUCICv9ij/rVXDUlA4VY7yaHUeVlwaIw9Rqjp4cOflxOEisAiEAmH13RSWwWI8lHE1PI5rImAbIAKE5Fo8+lQYi/a2Q4ZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8753, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPBCRA9TVsSAnZWagAAIW0P/A6xPeI2NRTIz6NM4NF0\nZnMAanVMC7pQcm3lHqPL3Y1VEfL2XxEk4cUNzEJZjIqHHTIuVyE4tu1/S3AO\nb39gAnztdZ1477DqJAx630uRH7QINXgh7LSB8XIoKknW7bzM3Od22hMitgY4\nZUSYACWlAXFhsUYnpEAfKucD8xJOysJspiCEbkBeN5hzSmQtvX0j2jg7VUJi\nkfKJEZheh1XtKBO0NSIjkgd2WO6xQZPkldu8cfJlP+PPZ9vLburVQGbMMq0Z\nEJwAoJnAl+SRmgw/LzY5gH9vx5mo6IURv+zepAPUqlGoLlURUzxTO2jtb692\nSv1yqomdjMLCuitl/wrMJkrGJdGKQEWV1DTSiA8unmlE1K4I7IiIVluDyn0l\nTSGZY5vMtqdKt7KGcm9IUr/OT7sSp7ijqCIK2cbk+91L4l1sBmigoA3siWvm\nFzJzQMu7zdYGn2xoS1eWZMM0b9ormn+LObd8z0nNxxXi5jkr0Tg73yhJ0wjo\nC7qsRcrIu7Hw8dbMVvmEPOOrNFF6cieLaZ1En9svHf84TETVZ4zo0g4kPyDr\nR5pLGjrhrSxxeqXEgpWh2SAt4TBzAjBzA3C0BP2zNK4JpJ2c+GFw1inPI+ht\nXHWg1grTrnAi0QOK2Y2/UicjLmIFZ+1TFAsI01tvK3zm6YzzbXf3nUSSbv+0\nHGIL\r\n=aWKt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-umd", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.9.0_1584718785367_0.9108334163311502", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ea080911ffc6eb21840a5197a39ede4ee67b1595", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-EIuiRNMd6GB6ulcYlETnYYfgv4AxqrswghmBRQbWLHZxN4s7mupxzglnHqk9ZiUpDI4eRWewedJJNj67PWOXKA==", "signatures": [{"sig": "MEQCICQvNsTD/9fQohTYo5JK68ARNpRMNkWojwd+bpcN1jR/AiAJLOTfEQBe/oRGJDWBB2uX/C4IXNSmfoYqukBIhBXGmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTbCRA9TVsSAnZWagAAqbMP/1W0TV4zApg4iK7XOOmf\nDGwUUb/dXhlnSsRtVuz+eMLC6aOx13n0PbTyhtHZuGA8yGhzmEmKUrqyjMwR\nkxznhd59ZdJ3ujejhHl3gTRX4DaE5CcqL2RCreYyIbxRsMFBTQ3UHpoBEUNL\nGi2Uf1X7deoPBkebscuE0ZKVq0BtJoIR3DXrqqci2dx4iX1fuxItA1yJeKHm\ni3nKZyuH2XC+ohpY30fl/OJ+BZQZ/QDqCN71lyUJuXMXyGk0t7iVy+H+vuNi\nV2V7GgnZIUOVrYbOD+lS2kt+jAnRyjaPFtGEwhC12CCJuKdgoTTEwNCStjKB\nkijjuNAozophHcnYqi0/b3CaJBQUXb5zt0L88czvgzIHSFyj+SPLIaf/4gz8\n+qBnqXv6hFi2jzTRbPrLzfKdvfOv0yCiOP2D7YsqjHyHapKOd6e3AZupG+9d\n9XvpDTz5VHsdGdGdEOg3k8voi6uk5g0lpsQJOdOiZuCvB61t7dElXhiBf6N5\nnGd4FuXYVbkdSNXx5hPxFT5IWDGsJE8zPIEWc77ysYd4rymRXANrpCtaTQ6k\npGvkpmKU9zIVCNQx3+UaJo8XgBIViqs+/gDwQ7opLZlRCjnMMsxeZNetAwkQ\nbWaB9ImHGOELEnsRw3qYwJiMT1+7TC1EEDgGkLejbx+qoRc3nfrg2qK+7nUb\nCWXK\r\n=GmXZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-module-transforms": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.10.1_1590617307532_0.755672529886918", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-modules-umd", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9a8481fe81b824654b3a0b65da3df89f3d21839e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-mohW5q3uAEt8T45YT7Qc5ws6mWgJAaL/8BfWD9Dodo1A3RKWli8wTS+WiQ/knF+tXlPirW/1/MqzzGfCExKECA==", "signatures": [{"sig": "MEUCIQDlI3s/WseFxFpMTpbmPnZXYI8CNMc51Ors/Gr+MgceIQIgYqih9UF6W/5CIIhx7vy0o5KsJe0bSukE9xhUitptTmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp7CRA9TVsSAnZWagAA+LIP/2OERJWicZJ8j6hwDT8I\n9DxKn9nd25zjcFWIg3RVv0BJGDtJWKxbsONajd9Tlap4hs3x5OZLzPIjtgVW\nWJdWzb6k5v17t20Jelcn+BEaWe+Su1g3uOVdN0IsgkoMOyDPQNwJCyo5wmAL\nE9Rf6DD72/j6wFJwexmcQERxZPNVDvTrBBSQlVEQHyLygYCA82TkzBfSJmin\niYzW1+enaSj+kVAnN4R2QUfVPiUuGQrhaFcWQp2jbNtca2isqyW+jFSY2Dce\nm90opuNgCh89F4RXq8zXeqk6mdJp5vwsP7zfxJ9nKysMPuXpmu40IIdzMRw8\n7L8qc3pNbprGpb/FdLVNwARvRAb33709hsuE5XAi15BwagceR7ng6/o70AZ9\ncHKtdAtavuPW6SHhNUJ/NJ9iJQj/Jme+6gx57xZ1MuyX7C9J5E4+GMEMRW4R\nskVu1JtkBUdIb3HQXHUGkqjVN38CHwVrpvocppGnSq1JXrwEEHhwwTr7awnW\nO12XYioRY99TTdybv1KOal2wG5xqO0OCl+25/SkxgE14R81ibOcv2f1ukS2f\nT5r9NaERAAFvUfy/6/Lw1Y1Pw1uo3RQmLz0ENTPnGyyTCN1ZgSc7pOIbl97k\nECjJBIDQtr9as4DTtN7gUo8amgo5/UHRRuLpufmtUYcK/MfZBpZCFk4rIO/O\nRYcR\r\n=nK4U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-transforms": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.10.4_1593522810718_0.36688426444156996", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "eb5a218d6b1c68f3d6217b8fa2cc82fec6547902", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-aEIubCS0KHKM0zUos5fIoQm+AZUMt1ZvMpqz0/H5qAQ7vWylr9+PLYurT+Ic7ID/bKLd4q8hDovaG3Zch2uz5Q==", "signatures": [{"sig": "MEUCIQDyLIH6k3enQRdichoOs5tP2RoubAlj4eeIfWR2hoeLOQIgAffdBPMoUjLWQX2OSZ0uGvorJTDvgizU6fjL1OwR/2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNBECRA9TVsSAnZWagAAUJUP/R8nPErcrU1FFZQkcmY9\nGV3xgwceOqy9uN7Q/mzBj8SswJnj3Q1vaFmgQ4tyQSMnyXTZdJ1kELYbRjrR\ncv/MPMG0h0Mo8P70sJfLjAteLLax6sljQqCe6J81+ljZ7LezW7HhJ+XwCdgJ\n9uQWKE0Tvi01dk3sbkcfsX6QMCe1qoPUzlHCpMzf0cbhzw/NwnUCn9g4ZuO4\nBDaJErfbhqJtAhMcn91vaBehXGx6eGZVBuMNU8F52MNGNPaQqdyTX2BhhMvx\nBWS0Uv6yFE82tJXw7LMys0DWe/oGoTea4GmwTDW9g/swNUdVe6X6Vv/NKPSr\neClAM4S8mIcUmj+fXLwo2d1xA1Br0qhwRChZY65enNlURPDRauSrYcyui3ze\nT9ZsVXD7z7nLW4VWgizsM58SdjfR9CzBloFZABtijBlT+KD91hc4pODOrNry\nGxDQQv1jmtNm7DnoiExeuFUMEFZK/j2/f9Sl78Wa0zRPPi4nsGN8Zt3Vqt3/\nbWKLyPArcDXqahsuVZvQxlyyCxBkLTcgPvWoiIeCW4D1DPJuvanEKy1R8Xof\nC0Jt2v/6Q2BOJ7QKjsFphbCQai2JsAwFWDySyM0XmOaGjLznUlMzHgYqsrQN\n8mRnyk9xtu+07EOjt2GUJKsMvLYg0F68RL9QT5UiDESgBoJEdoJv5lbQv7S8\n7w4m\r\n=v6ZX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-transforms": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.12.1_1602801732130_0.214156775605151", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-modules-umd", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "26c66f161d3456674e344b4b1255de4d530cfb37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-BgZndyABRML4z6ibpi7Z98m4EVLFI9tVsZDADC14AElFaNHHBcJIovflJ6wtCqFxwy2YJ1tJhGRsr0yLPKoN+w==", "signatures": [{"sig": "MEUCIEp9/Zrjc2EHW/5fhX6sHP9mVF4mTDWJi395VMpGCxzMAiEAkCVE1qXPWrxjT9KAJPW4mhtQf5rEXhl15M0xNo5k+Mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhzCRA9TVsSAnZWagAATj8P/RIdPZguyhYKgXdXxJDs\nkxTrv9CVfu1PMBx3lgkGVm09+XBvqu87hBSPgFsLLmSMI7wN0aiutlTsnH3t\nNKwC8JUK99tagxjYVZMNxaYINt75JWyrUT1xVLg7bsIv5GSqkKJtbZT9MFs6\n2J/6qCKM+iZAGM0XlW9mFMYWCNHyn55Hap0N1kgD/lwd227AhfBw2frYS+NQ\ng9dAHW2ZolkJrfifZFm2U0w71cbo6Tf+IXfMizdhFTaNn3WPHEDqGv+7GJ23\nuD6lX9svAke/yGQLYLgmJtb49TULfuRArWMhWQylOHNkeVm0ONWyMS33MFJp\nU8izvh9oISVydbnEAtbjXRsCTYY1i0WMeYBfdB00vj2FH8qm/4T87uQhzH2Z\nQtz6ixhrZ0qn8skKe6diRRpZg5maTg0z0hJlBmml4X0P8ZGaxkxWxn1AQZeZ\nbi1q2jP7iGt0v1D0P6ZScfKLqb8ISQq9CW5GpvnEUDxZ/sCKcI5fxgieAQbN\ngb1LwPosvf75/rJn3mULiWvPS6Q6hSLqL2Es6v/fO//CtKsqIvIe6w0m2H5r\nnhOWw+0Q5/HOKys0JsEZHqMiIKj4I6u/aLoKybRH5FhfF2seFjZ8PTYd/pEl\nPVCqgAmzLgFm9tvKmLxP2Ot7zSkRmUq/9pQqb8AHiPMj94OpvePRWmJtn1yF\ngacy\r\n=5Z87\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-module-transforms": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.12.13_1612314738993_0.612010403378193", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.13.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "8a3d96a97d199705b9fd021580082af81c06e70b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-D/ILzAh6uyvkWjKKyFE/W0FzWwasv6vPTSqPcjxFqn6QpX3u8DjRVliq4F2BamO2Wee/om06Vyy+vPkNrd4wxw==", "signatures": [{"sig": "MEUCIDzWeEBAbCai7Axo0MNkQSjw6yRg1B4z0xuBGM3C/Rn4AiEAzeqjJFJeRqZFIR51LP4BQFovW3EowVwcA+gJn82Cq3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDU6CRA9TVsSAnZWagAAXhMP/3AYleFwAG32HDlXtNdC\n7kDPSu33kX+akwmg6/tAheZScX4DyTF7q9c7k/QrYgBUUHzt9N8zp9I9hLEF\nmgAQSZFmaDI+7DX+oXTFw+qteEmTSdy2YHMOyzUipXWzSRqF5vj3DyvGnWY7\nQJJQQh+pm1MP6p6F/ZGGgBV8dJbUaVqEhRUGNT84QgmBaCRkRTo6vny/CLeg\nLZCGhU+Br7e2I382+WiUrxUrh8Txc87P8SkJvwin2/8VzpyM9hyxHKfmEsfB\nCByijiYjTPDV2ODdjj4CvnJKlXSeVO6ieCrCcmZxAzf+BoJ/dsrj2r/hR3dH\nWOz8aSZHEIOrDB9bYoPvL4wFo7kgzZ4fN+EGGORT/ys43befxlrvX7WZPISS\n/eEMBbNybf1KAOb+9ED6JK6ITSQWCMAFtm+bxyaPcI0X1zlRRaIl5X4jMYCo\nFx/0RQY4+BnI4rto9tnUDXVp9S1ERbVJEHeQAdLqjKfBVr51GGQTM/HpVSK2\nMXO41/NsVYdQnKlOOC2ma53JSadJ/T/xJVZAd1CWnVkOnEXRrWK5YQVM6Q7Z\ndiBv+UuvmoysqXyWEG0vIQrxJKCY3AX8yAUn41jcBjb11c6tfrB4w1vX7qhg\nwq7U5IYCjvs9rWmJcndBMfJHZO45VYuTN0lu4BGX0bpA26EVfpfgCcL4E6b4\nlBmt\r\n=ZDpJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-transforms": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.0", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.13.0_1614034234242_0.7028421558304951", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.14.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "2f8179d1bbc9263665ce4a65f305526b2ea8ac34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.14.0.tgz", "fileCount": 4, "integrity": "sha512-nPZdnWtXXeY7I87UZr9VlsWme3Y0cfFFE41Wbxz4bbaexAjNMInXPFUpRRUJ8NoMm0Cw+zxbqjdPmLhcjfazMw==", "signatures": [{"sig": "MEQCIFa1bDj0yImAkQPiZd03Dtc7RCEC3EYp6Jjlv+nloBQaAiBnnhyqKF/S6dOq8VjhUSRDIaGjTGqdzGokDu9rBdattA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKrCRA9TVsSAnZWagAADFAP/23gdUq1DLo9hSzt5y2Q\n8CoLVVgShd9krXd0NMsjimllitVYkFImjv4H41PuGHr8sA2MREk1jgIi84Ql\n+mxZS4U4WE3jGkDFny1U9BcB4HLKaqRYhnseICcJ70ABVzUuaOz+J7STgtIc\nJ01YR0KhltDbBzgpl7BjCiJrThSwHZwy4uATPSrQDPW/bK/eKCi1IQRW4Iof\nlc7meljCW2cV35u8XcGB0/476EIl1WffFmoegG0r1igdRM4XhPKbZOBCJQUJ\ny3KMb3fD5XfEOhlyHBXMw4WUSS5vzsHqwiJ+Kr2GGCgHw0kjjV7neAxjPxhS\nan+wBZ9DYEGfQ8f3k6OHynWklWAWP9Rx5Zvub0ifCHV6qqJBUasmo8Dds/D3\nEgDp9M0BHWMHqy6cruNK5bh0IMxZMyaqG0/Nkx/5dgdi7DsMCtuD5cmxPBl1\nmqtXnkID1/S3PN+6JkWjvLIw2g4O8inlGcE+cW2YpoNsA5kjSOHyNDSc7LEM\n/GsPcVIvsCmjZ23yQIO/dfFXFjFi5v3O9a2AaWDWFl31oLQZLYFci6VjDyKw\nUqmh7TrFSpq34gXDXD512gwvH9bi3FsSsxzYr505qNHt9Oh5HtcZcm//ngae\nDM10B1pjKRZOQB/815B/4kwy/wIa0MikYW86l58kDqVH4uADqXahgK6LK7bE\nh1c6\r\n=VIkb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-transforms": "^7.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.0", "@babel/plugin-external-helpers": "7.12.13", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.14.0_1619727019006_0.7830602067335992", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-modules-umd", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "fb662dfee697cce274a7cda525190a79096aa6e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-RfPGoagSngC06LsGUYyM9QWSXZ8MysEjDJTAea1lqRjNECE3y0qIJF/qbvJxc4oA4s99HumIMdXOrd+TdKaAAA==", "signatures": [{"sig": "MEUCIQDKhCnxLI8Ip4nVBXxT3HcQAphOKPN+nBFD+Z4L68OpewIgFToLdB37LhvTwD74lBDX+eNuzhKmzBQFNSINQYLeSFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsUCRA9TVsSAnZWagAAm0QP/1OHlyOCikw2xCT682pd\nkUUCTgac4C5PrSMGx7RsRRNmit1whocRvSezKxPiE+fSJQReV+2s9eloAa4j\n7hspYWziaGizmFUm+aiCo9CWUR4HSw4EFLQh92fQGJHCkU874QI9IwMS1Y5O\nuZG738NWboxOb6qGZgV6/GzYGBQonLGeXDVkKZO0c6f28XbDMs5OLHsBIiGS\nIe6gi3aET6j/hbtlonLtdrbNnK4kzSTGYIgXKgpeuXr6NfV8tlr1dl3ZFx5e\ntDlsjzRLIZFAPbMDl1fuR2NBLoG6WrumGcwuo7I2QG+BLWoy7+zzk7ldlYf+\nf8DQWsSIbA7WvAJ8fO9vnAO9cvSIwBoNDTwILfgc+OmuH9EoMZ2uY7PqkQ1G\ncMNvphcWF9QGTjT5ntw9vsYTm6+NKiJ05tswwRPmbNgDXa+tm9HwfGE/CYJ/\nDd9CNXoXEiPVyR4owQU7TxL7evXgRyfJKWG4JYH1QnFfzd1BNd5Xk+GfBow5\nYcNXMEjk/HUf+f6eSAEZHQVc9WIQEw8HZROFi7snhYPBvfQkYDLMbISBREFn\nKzjJhcmfLaWZn3LxQGBiZ2iQJoe0l+BX57r59PNEDK1GtThpPdhKNvNIgmsr\niJ/k1inmnCOLb7YOtdifJ+RmlE0VjOU2zbiQ0mgVGEfMi12MFmFxau5T9SUN\nBTyo\r\n=2nAm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-transforms": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/plugin-external-helpers": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.14.5_1623280404249_0.6057359933552169", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "195f26c2ad6d6a391b70880effce18ce625e06a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-nx4f6no57himWiHhxDM5pjwhae5vLpTK2zCnDH8+wNLJy0TVER/LJRHl2bkt6w9Aad2sPD5iNNoUpY3X9sTGDg==", "signatures": [{"sig": "MEQCIHCZUaUalr09Knbr52ns9/tDwQiCiEwk8fW2pwh3A3frAiBaWIYllbQ0GS3gXrUAV5SJtnPJigqe6WAMUV7TqXjsYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9366}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-module-transforms": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-external-helpers": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.16.0_1635551280429_0.8957742395058823", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-modules-umd", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "caa9c53d636fb4e3c99fd35a4c9ba5e5cd7e002e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-qTFnpxHMoenNHkS3VoWRdwrcJ3FhX567GvDA3hRZKF0Dj8Fmg0UzySZp3AP2mShl/bzcywb/UWAMQIjA1bhXvw==", "signatures": [{"sig": "MEYCIQCzCKWDO5J2LGbRXDcwERkkGiAffKgzcos+uAknKCwDiAIhAKQAnyGBRf2Xj0zmxyk8yRKzoNEpo4nzRFPS5yXQvENk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kmCRA9TVsSAnZWagAABVkP/A3iRGHGAVCsiCSnz8HI\nx7OdzEUPXJnHmnPRp8PAFA2YBGMkzq6nYsNYlSjsUk9Iw9kmNcpHnvQhmhLI\nU2Yn88TzlprOPXMmCErIeC7bUAUrTYYZx+44zvnHG5X8dB5qveIeefX+HrzX\nGIw7bd0tSUXSTccMisFW9F3gTUXiBZSykPuxNEYFlEHAo8LegKX+XbFUb0gN\n30zngCiicTrBBz9hT4Wnw2AxjoCVwfoJMQLeeRQy664Up4OJZchEH7Z/97Z3\nfnHIvwUnbWMZZ54hZDu13sDM5H9QpvXPMFf3wtzqG9nf2vpYjrxfyDkRQvN2\n47GNtl0xkXd2Mywp5CYmHEx6JAZ0qhZLr9PpPvFSUDmqgCt9XJC5gy3BRs5K\nWaDzzJy1gPm36cRJpn2KgFbK8wRBnZ+hQavYrhH7Zy2nFZUHHxLlK90x36HR\nDOaleZmu02ePTgGqgkhc602eWXLwuy+8EHSvv7dLqNy2T7BdNPfiwpp04XJv\nBelxY9mAYnaJVdGsYhWlAywXfiDT55TejKG5kP24nVe/bNPpJQzOOU6bbcz/\n8bFL8/ixrjTBBly7ZOUhw13je/8m0hs2c1Bs4NtQGtvV/dLjJ5Tm0NiczAzX\nZf1hODHxuHWK2F2wWugkWnRAP5Zsa/5XWTE6N6JfFBppMyiUoQ9RIUckGppQ\nBn8i\r\n=IQXG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-module-transforms": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/plugin-external-helpers": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.16.5_1639434534867_0.13664897458485492", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-modules-umd", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "23dad479fa585283dbd22215bff12719171e7618", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-EMh7uolsC8O4xhudF2F6wedbSHm1HHZ0C6aJ7K67zcDNidMzVcxWdGr+htW9n21klm+bOn+Rx4CBsAntZd3rEQ==", "signatures": [{"sig": "MEYCIQDO7pLSB9EUk6cQcFLOEaG1xvqHpqGLmAzGggRi2FjnIQIhANTGIqtNOvSrF3NPSaaNhErY+vZHyjs80krr8nBqmzML", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1rCRA9TVsSAnZWagAARbEP+wSVjUyZDjP3zBahDAqR\nsE2kXX6jv89V02U59G0xLkWw6rMlXEcPX/vvz6hJNcb+Kg5QX3/7o+JSUPJe\nQsfs65R7vw9fDTDN3nD5h6cZNVOCU2X72hR1Pq4Xs2Uwi3bQ2AMsqp7CgYYv\naTSIF76GS7ove2Uz6mRmNdHCuTYurRIu11hUNpyd0ARfhwuPsI+XKFsrjf87\nMkpyE07chLF82qGTcVN5xm25uoeogKDoYrZ5nfe7sp0QElsJzWqQIEucwgP7\nTryMh2eX72GBAieCy138f9uwMUovO/TvdohWh8p4y8Q4nvZLeoODiKZXfOSs\n939WdDY/jrdo6fybG/9SmAK9dKeauTO/OpKUo2e+wu/+yB1oh4OCjnlh/bNY\nDjzeYAgh3p+vMvq6InYOHuaNTTg/eksEWRHU7Y1PGHqsFvz0INPftYUAozu7\n6p8Mu6c/G7NSBGNH4tk4CcDeeFEQuVa03LyC5oM6f59r8CnnunixCE9qoem9\npc5Rc1yMBnEiOaUkjZZr9JVQq06pHBifet7mi4PfwlpvhHPtVhunywM95Nkr\n7sdH/0wXjfF7jYzsXzutZ8dtrnfkieooUq0pGFvh9hICE6BKGQHQ2mIR1aJ1\nVb+/sH1dXbQaMCiIcQP7z/Er0YooA5pN3EBqUdTgQaTfm4DOHTaETh1M19Sc\nNvku\r\n=wkuT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-module-transforms": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-external-helpers": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.16.7_1640910187199_0.9959475583953663", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-modules-umd", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "b37be3ecf198c1fea10e6268461729ced05644e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-BnsPkrUHsjzZGpnrmJeDFkOMMljWFHPjDc9xDcz71/C+ybF3lfC3V4m3dwXPLZrE5b3bgd4V+3/Pj+3620d7IA==", "signatures": [{"sig": "MEQCIC4ITS75x9Q5ue83UEH50im5LPLmUx315VKP0G0Qa55CAiAA7Kr3kD/M4IdVxqJejh2EJXvrKLK6a/fe11kCxRNwQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHVA//azvRBVWdMhIwZFfa2zv/oKE9OsFyZ5BaT4jxNdDlvcjIP5OO\r\nIxhL1QUJZt+5dOOoT6zaanuwakNUtiqoZsVK4nWSfhFPZbiogd56oQ2upei4\r\nrVrDo6CSQaszlOfDYGbI62hNAfmRlokCrH64tuWcXffKKTDECji88vdyXdtf\r\nZe/SdJHp+okxX2GTNc5hnl0+ZRStoPbqG7TG2HaD/mDvwEQSLyufIC/q9X+p\r\nPG/lbSavtEZHAmJcLgBoGFyPn73jxEzRt9852qJTZH4poCFFwKM4rhHlHMYd\r\nyn7Tm/CKYSaezBf/1d6fb6qkRrsnrItNuTLNlYTqoHszIRu9eR5A0QQwAY/l\r\ntyYYg3XZ5fgnyzsWilc0g9buFUqJiFdSmJEjclKVekPDUSokF9T06RQz2bvO\r\nTNEMJDjV8wJcQjqGKyPDMlIIQ57WUfU/Ber71xtz3+zNlwH82XbefDHg6ga9\r\njiN8cnzfMHOy9cvstvrZgsDUvKxmAzMlWnR52JKZ0ZxusSbKH+wHinfNgj/x\r\nUM4OXsmaTLIoXiCTNUAaXzwe8DJlLtncO7JiApLOxrEiprjvqJCeKYJfnD9X\r\n/wpcfaWdVQNueQAQ6/gIXzdm5phR+aAUDluko3so9s9p030C3tsEVJX15TF+\r\ngPpOkT1Jd0b174fioL6qJM34Q0UIBeKMOsY=\r\n=Crkc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-transforms": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/plugin-external-helpers": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.17.12_1652729598853_0.28569848941901044", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-modules-umd", "version": "7.18.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "56aac64a2c2a1922341129a4597d1fd5c3ff020f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.0.tgz", "fileCount": 4, "integrity": "sha512-d/zZ8I3BWli1tmROLxXLc9A6YXvGK8egMxHp+E/rRwMh1Kip0AP77VwZae3snEJ33iiWwvNv2+UIIhfalqhzZA==", "signatures": [{"sig": "MEUCIQDD4H5u/ZmoN2MERPFBX277gwKu2qgYxFmG6PEXcO4wkgIgX9zWTkd36iGtwZtkJSqlyaehnIJz7sLYdnYf+i7TkBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmRg/+OmPg5gvg+PhabPvGYvsWjxKkMVPeTABf2wjnt8j3EVmU68W7\r\niQHgk88sU3NznNJgte8FLpnyXiXPJmGeW/WqKBUgyrmflR1CyOpnDTpdHkls\r\naIcSNXlJcT+fguzrm4fLVvgZbK5uu5fl0Su1EhdXFp3s1RxjUEGXjwQUbx3J\r\nRhHJG/+qqSLcxRF3hPdcuXKWf0COurILd/ZEPrN7K3z6CHKwNBBKgZo22Hx7\r\nDZbywzwd3xr<PERSON><PERSON><PERSON>ZzzjnXQ43nm8oQcmOfxrc3oY9RAVRoAGJoJI6sycrB0B1x\r\nnVRwLKW5bAE25os+r26rq3y69/+cCsU0A6QAZeX0LZzIUjaBuJ8NPHqcuf0r\r\nl/ZoAPjxarkls/E5foKZcghQmiq5nqgXtBI7piYfzHyg/RKN2TgaaE8hgwEB\r\nsF8MoEP/kIOJtJqmhSB0P77bCSAN1U4kJk36QzYym+t8hzO80PVo1U32/QQa\r\nzP5n5l1iI2HhacYG6LaNUNVGqnifwoqU+h/k/kqvXsJwebQUUV2h65i/zg6z\r\nXhaCNawcF3ob6fnivNvPcccZk5v2OL+4+045eYFBp6N256aOVq9afpaiaPxY\r\nm+lSlvgqPV0JMAt2CZJeOWtqvCIVTPVho9dSz6QBVBu+DPdVeFcowGAxLd3T\r\nQjt1LQ+SHxDo98/sc1jfrRM9OiQ5yb00IXE=\r\n=awtI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-module-transforms": "^7.18.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/plugin-external-helpers": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.18.0_1652984201371_0.4520500820808966", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-modules-umd", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "81d3832d6034b75b54e62821ba58f28ed0aab4b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==", "signatures": [{"sig": "MEQCIEliDUqJ7qaGATkrOaFhcsM3va8iFohqnd7Ibj/NjotaAiBqoo8ut8N1FSjabNcFfs6xgesmItoG/genNonGnR+6RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZpQ/+PrWsFyGCvtJKBc+5Kz7/qGvrggoBXm2zoYAGrGL69B6muUVr\r\nkfFwN6i21D/a8sxaqnJkYvskJekHs7QpO3PvBqaZwWUzYErCByXYGDHgQFxy\r\nsQ53l/n5DkZT5Be7ZSHM9+BHIN94TVou2zwu4pUXGRwnNTXM+KXCS9AujNbT\r\nLroJUGMKjgW3ed4ktZ3I6kuaSLpqxZkrfMy2U42SlAfl+kmdiBSw0hDglSqf\r\n3A3vZwO2tsIbsypsWP6BrCzmJEuI/2l/WZTFige7vOB2rzLk2uObeU3Pndg+\r\n/9loiWo6sYJwNgj0Kpxp0D3bhZLpaSP1+UqSgpnnipKaBLVVHXyJQCn8ZEAM\r\nZFwgFOvzEkxMqkZaTvyhYMioDO4Y0f8O1/YOwjjjkqyKnBCGjz3YNFMmh3NX\r\n5pJU7KTNyzaMVk1+iQ/6kbpjrtcwto5+dK8T3ZvbQx92UMBjyg5NreDdWMYa\r\nxJAjd6301kewqR8QAXCtuamCM5tJY4gYDx3fO8sLFP/1i6/JlidY9jeeP+4W\r\nsO/rOHZK/E2Nh5FRbVaD2AcJcFFIMGcezGbPkyyN6/zg26n4jX5QE2WEsWzy\r\neQRezT3X5aRN7LdaNSX7t7KNNuG+1NDrzDY8BQU55rCbvlCYg905IR3A4iwD\r\nFCij3ymXNQRuMLcjwntRVI8zFfUtXGl2WdQ=\r\n=D0dM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-module-transforms": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-external-helpers": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.18.6_1656359441651_0.07789639961456674", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-modules-umd", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "9c7587ecdcfb0d94070a291920330da7b5afb7d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-e3rE4Ri5p9vZph+qctrDAnA4HdTapPQWl8JbVFJnIjvGJcIrHT5359+suMXnrhKLtSqb3UMkGG3zDRh8ufSgXw==", "signatures": [{"sig": "MEYCIQCAnZtoEL17F4ECrcWVx/z6sswCjy78WtgWcXYXXcm6igIhAM/BhX5af04XW3cY6w0ljIfzZXki7H7hvxmLhrot5fas", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Kg//cp7hTU09iP7XN7tigcrLm25KfsR5C5vrwY35B5JIockCiiYk\r\nHTUE0mg5SRz5RXZSyBSw4l7eD/mNlbkfn9fOu5jAbH5EqINIo/hes1n6rO85\r\nQTiPbjTtYQx+UJTlgXBZzs3F1MU1V/C7hEl5Zu3/7ARibzhPh+38BDN54yTP\r\ntG5kCJkfxcQqTpv3FEL027JiJ7FZ3wHEcaMTIF7b8XsF8Rbrf2AlEpiolKYk\r\nUOtT21aVW/MRmxl5zTERk2yr9Ww2SzS+qvAu2t/oPuFyEG8B4CMG0ezf40ZA\r\nNeiUWC54CcjgHwTfUtwDS9raCujqXgxqQyzr8Pa/cvoWz/og+EnKmWXkYjvy\r\nCak9v07jPIuF77GAM4lnSqDcLtJonXA98PdOmWAhourJRSakVIkubqlFGbe1\r\nODmAhsxM5AUgeDGvMM+GipBaysptGdW+iaJPc4jRYaPXhj3V9OJvpFvIZvxC\r\n7tPVqsupPv9LppqWZOfMptDWiL0JbPR5QPPLIQ9Kh4+PpupREVjqTV0MRcvR\r\neuNkdB6luFi5+srBkmd72H4Juo1aF5Jeang4s4tYcJ6r7DDcsXvwcQW8iU7F\r\nR3PAs4dbffTfeuUid5PsWqyapinsh+d0CN3DkPXmnJ9rNiKekY9rpBHqvfgY\r\nMdNfthiZz/EAQxvoMj9Kj2/JZSZk48cPo9w=\r\n=eDv1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-module-transforms": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/plugin-external-helpers": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.21.4-esm_1680617398330_0.06799611282446105", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "eac7a68191700e8df1506077ae5eca956a3f8505", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-Djv2l92GExBx38gQNMTW+QvGrMMljIkckGL+bOoyZNvOdR7hCK6EZu19jvry/mhx8lxNsy1hRV1t8GLvUyLxWA==", "signatures": [{"sig": "MEUCIQD3JuXHolPgEz69U5WqPI2Xu7kCWi+Wm/JBox4iad2N1AIgNej36OdNpK+EOLUvfcW+VS3Smccng1Az7In24w7gUco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1xRAAg+gJPxBKVyPuh5eeCFAmcDhIzdu9Dd8+8Q5S3IdEPqD/XWVq\r\ndTRpmpH2aJMI0painE3O+0CkZZtMHA3/rAAQcENRsnonG9N2thze4BW79hUM\r\najkxeys18Eou8cE439t3TZI25Xj/hS7KnE6WtPBPAhnM0INunqwqHW5JuOZB\r\nkQ1U4fznq/SD73jVVxnIm/Q/Vfvxu/96lZS68D7TibfPyy/a0fEQEnIzNCD6\r\n3LxJCekOOvIpU5fA+4g7qHjjWIpDb6sHMVwuoT0VDRLT43/9+xTh1k2QmzWe\r\nr9kHe0VmCvuKPBuBId80+4hSZNQVzcSOvVbuk93hv73Gt306Ld81px0/lP0h\r\nYichjP3CfRpM8DN0E0L3SBjes+hw20d6NDwXfxundhtgnDZesURO9+pzpe9z\r\nKLUaUEenqce1uVXtFl7gw37orDzVdEOGNYUOH767vxGG0Qh000HPibuKvX0n\r\nQLfAbKThAnM/eVrPaz5TE0Zk14ZbqOjdkMt+4S3ivQv4OR53q7SD0qNVF43i\r\niE33kM21pKeLZXNe6XI2SVJEFD01dRNyDemk0vZ6WwuVJZgdhswIbuvPWyYN\r\nF46l1fAfNY2lkz/0nc2IyX23ySk8Ju1g0jqV2c2uddsSeAnS/9OiNgBoBgJy\r\nDzkPUIAg8BnQ66VZX2vuf5EhJ6rwKszRZtE=\r\n=AgNc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-module-transforms": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/plugin-external-helpers": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.21.4-esm.1_1680618116004_0.6906558117443318", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-modules-umd", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "c64e25cff90f17e718de557f75a9694a5c241231", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-lik4dsTprtafPqZ+duir2gUxCTxzwFDNbxKpTetNMX+TQTSDHb7nzgF23qcBCu0oJbrwtCia33QIf/G8aSzKOA==", "signatures": [{"sig": "MEYCIQCjN/GgbzhnQ8p0yRYsYEEdV2qSOY5Zep5xQ0pZ+Dq/ZwIhAL/1PuAaiLEiF7kG76kzr092Dv9mH5jYdwlcMjYKnN/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/GxAAm3PxC+TDFm/D7fv4IY7QEbcNetjanGz1dnhpvjBQx8PnPMhU\r\nBbW6VRSjtPfpQSRMh0t2+l4S2w98VNdPqT3rEgk6bQ2izNv32Q7+o5qlqE+t\r\n3tQXwYbybeyIGd8GUfZ5cKIrCkvf+qKXXMdvx8+fRoM/vV2Mw0L9p2QL7fEX\r\nXFhG3ZcG3lGLx90bhJYONWkONj/AXs0iwgNSKs2d7N3gXmuHAfILX2Ff00bf\r\nnmjW/ceiRR1LM7v7km8wS589vN7m6GevMlyocyTsf85BvXkZez13qP2Ebyii\r\nEvMndwfAn9uTSHhgrdMFj0jkbqbEPQeEGM2wCCajoEMXiLyjIA8uaNDk5v23\r\nGqUGNN5aVVOrF6lT8n67VguvFc6XtTFvpFPXhj+wEz1tCwu3UdJ93ynBkhap\r\nCB/FMldA0Ot0UzItzhcy6XLVW3PRpC8pBQmTjPOk2Lti729GG/os0PnJsyAy\r\nmgakku5w8G464KiC3dLAIYK2w4tugP7NVeGMocBrKDmM17zQ8m0wBs4ZALt0\r\nRz/2vc4gFBUvtNnhvBDqO2ESCWgvVleRst9rbWotXkr2o5qoqXxjxSS2STLE\r\n5EhzJ2ZQR1OBWBsMLZV3wvgldoORdW2UUaCVy6zvWfSuE9NKKrVNvcdIck7a\r\nU60Zlts+1ayBIQfNMGL+TwMLC0/wjSB3G7U=\r\n=pESC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-module-transforms": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/plugin-external-helpers": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.21.4-esm.2_1680619201638_0.562237309924948", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-modules-umd", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "4333dc1c2339c9ca3fb4a1f6af213d04f712ecc7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-Fac5x7jzzp6fFKTl6w/FGdIfSF0C6Ki5UcsIuRaG4AkX23GyCAMzqRUouvNlEO09siOVyXXTWwvMun7FLynV6w==", "signatures": [{"sig": "MEYCIQCQDhDViKF7zR7QyAUaZPd4hbEGgIXKohExxNYAf2ejBQIhAMxykP9XxNVER+rxaCycX3MIpJOFurNhE87aoG2r8YNj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmon/Q/+OXhP5iRqJg0JWIXvZTTtidMxoeX5rbR1+qj0focksdkvduPb\r\n6q95/pk37smI0kFdfj6c5g1mfSuFmYGWQRQ1fvhdIPPOjkKe1BkfMLXQdIwX\r\nrj2MoYQf3P+j9jH/mxG9K7spZKkRNvCt/gLQYKXEKsq2xhOA9lc5jUliSF2l\r\nVXmXENOKuojd357PlpjETGlIybbDdZ6bl48j2rUYOZo9q3dCaDZAhLHw8uUo\r\nil+F9P5Xc6ChW51DR2N0qWI+hKQOmKwN5qOUS6ZvXdwEPxanrjeMR2s6QTK7\r\no8u6cWfegAVNN8xATkDGIM2BmBk2CvP6URnF/FI4P8xyqhaVr2wh7vIEckiD\r\nQvBBf8tUyMrzzOjtuXP9rJZ2L9vjjIcTvPhJsU2TASdediF9HGDaogF1S41c\r\nKGlxVzzOlcdu+TblPqq513kEfqQ8oSidO+OKYxGfefPmrqFdJC4viksWPeTU\r\nWxlPZSNpe/VLZozRxPUujitF8vdN7edl0rmKgw0uCBTT9okn+aw+rl4qeaD6\r\neXZTK12fLFhdK0aVvFVFIgmvQh+AO4Duo1ruJsUGI8haQBWiKjVTc/iW4G7r\r\nJWY9/JbmWaWfP8L16+sj17XLvV/cdGs3QKai45rv2ACmOuQBoCaA2KK+BtiY\r\nmhb/jUDNMl+bd3asaS31z5NwXa1TjIa+uxI=\r\n=qQii\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-module-transforms": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/plugin-external-helpers": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.21.4-esm.3_1680620203752_0.8585678103757617", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-modules-umd", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "3bfd4e846a6aab2ad11076a8fd15ee73bdbbddfe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-t/uDU2UlAFdIn4GP0jQh/Vk/TkCdPmXtC2LTp4JUDL4PpJenO0yeqE6L80RahWWqBjOT5KbuR+FhEGwSkY+2sQ==", "signatures": [{"sig": "MEQCIEUeC0WBl7EgI3epPioPqoqcZG7GunsvGlqzmqUg7Z/oAiAG3auKK/H8u9CLpqiwTwKOnSaGaJEuD2TP3RzzCie1zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIlg//cKKXjWjb6CG9WcSccKPDpEgxrOwdoH4eFXSK2wogcgP+okZz\r\nOeevx5tz4X3E+FzRaAyCm7iKAp2tEej591+0bLqtaMqwhS7A9DGQGwbWosDK\r\n0plDlZTLn+D3MvMRQn3rFHDgqd4/ky+aEmgPTh0aX+tS/ZIi2edxCS4FKAl9\r\nNOczYRx/Gmoe/lTZc//+y4/7EYkC45A/5cGBUS8OW43Z3v0nB40eDk3gChNx\r\nH48ct4JK18ccuFZ7EqBtOx09O3Ksk/yO3RFwZONHC1IGX6LPODiGNb3eaSFu\r\nOaIJ1t690NadS7i4osrQ3tsTrLZyRaB/P3Phj/IG9/8JZaKfvtuNe/rRPq1+\r\nEH4ii5jwqrF2kiK9x1/JgOclrPnTfvPXga/tQ9xjGa511WGscSwdv+BWcYrx\r\nhRIEV5XnplJXiYTcydTRGomUDt6kUG41hQCh6WuFujHYFhalbJ0ah16CnQUt\r\nor10Iwk8MjnTLxsi/K1WH2p/z4hpyPsminPNeSzwzV4qq7FDvO3QFXoufRgR\r\nVNBueTP/LzM4VFDYA2ogiKY2oaF+gPrFzloJoLc2bC9inThPe+ZCYe3Xweab\r\nz5nZS5At9UfwdGe2LMW5F5Gt9fJM6gjwQFQ0DqaCchppExWnjHKTQuhqTYs4\r\nULkprhhVhnh6r9fDnSSvpGz+haE8ahesOSg=\r\n=dj7g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-module-transforms": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/plugin-external-helpers": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.21.4-esm.4_1680621231698_0.8751559051722042", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-modules-umd", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "4694ae40a87b1745e3775b6a7fe96400315d4f98", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-+S6kzefN/E1vkSsKx8kmQuqeQsvCKCd1fraCM7zXm4SFoggI099Tr4G8U81+5gtMdUeMQ4ipdQffbKLX0/7dBQ==", "signatures": [{"sig": "MEQCIBwQO6znctzsTCA2GnTocYtbAiU+KiVsnOBd48omPLV5AiBR/66hbpN7QlvUO//twY7LDOQXWTc3+nbkypoRtXsP7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25574}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-transforms": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-external-helpers": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.22.5_1686248506764_0.1967822774432828", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "84597828ccacedd44c31d02f8c70a80d3fb96da4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-RL5RJJKzsUW5vHN+Ka/Agi1mSDxNDz48wwi2/X6bLY8I0LU4Kxry8kDc9FUmJng3aTQVCCRIHydEE0xcHBJOIA==", "signatures": [{"sig": "MEQCIH+fgJajzRNW0gYkqr6Xtu8M16CX4/nNzbGsTr4PFp8fAiBMcPUpxSpYn5aGvQBmcy4Gbp0J/ukfVvTNGipLZVBLnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25293}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-module-transforms": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/plugin-external-helpers": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.0_1689861621664_0.505165073105952", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "b39601d80c6da7e930db86d373dfb74583adad1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-qzS3b2cqEkrud1VHeQfiNeswLtkZLL7qcT6ReLWv1JCejLkYkktxg5owOKBxB4Wr9Q6nuylS7IJvpqTwM072Tw==", "signatures": [{"sig": "MEQCIAi772pCPMhq7KcABTL1MQoPFntp81z0115q6aMHjrFAAiA580QRQNs/5rQjUplCBc7ZLaaUuqGy5YKag2X0pvXp4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25293}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-module-transforms": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/plugin-external-helpers": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.1_1690221174906_0.9779409900324656", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "ecf6bc568e3db2f005f39ebec3274b75e5869c38", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-Q8P3HtfI2nKBeprqcxWPNeE0y0MUd7HC5rZLEzJ9VwunWRi121H0WWeJ6dUYFZk4+x8rxFiJXbqXSPOWCuNzLg==", "signatures": [{"sig": "MEUCIHhutbJQc5GF25PuRbRook0hWFDSpANIumhqtstaL+d6AiEAwitWR2Sck1O0kb04AN4Q+py0nHjdruQwNyCRGb4AVpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25293}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-module-transforms": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/plugin-external-helpers": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.2_1691594118020_0.431937726557992", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "5043c917318c69a5d6f2588a1323e5c0d1ca7ec1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-FTLg9ZbanHB0Q8FnrACe39yfImLegLiX6C5SltPiobIZqQJtTLaf2vADrX0kugbwlk0vN6gL+H2huRZGt/iCvw==", "signatures": [{"sig": "MEUCIHQvVejPB4YlyM4GvMlSX1ikT93pSj1WWlfT5TbNoJK6AiEA7jz4N/Dcdfj+aGB7gIIa1MbqjycL3UCUBSW3jail29U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25293}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-module-transforms": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/plugin-external-helpers": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.3_1695740250700_0.7749626671968097", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "34a912abc84f86b2b70c4e412fe134f58f9ee80c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-2DJSa7st8fEcYSi1Nt3S4Ypp9k+DCymiI+DFOUj6piBxBFV6DWMEAunQSk40xHJJoYNWyud0D0EBKdeBHaatMw==", "signatures": [{"sig": "MEUCIHkkQYK+XWoPB4vmahPrP1fd9XKX/vMsYb6dyRxBK1GXAiEA1prptaUZ3tvZ4zrHUzyt0tBKaCjO1WWZX6VLf67NEhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25293}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-module-transforms": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/plugin-external-helpers": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.4_1697076404035_0.43337139780330536", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-modules-umd", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "5d4395fccd071dfefe6585a4411aa7d6b7d769e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-zHsy9iXX2nIsCBFPud3jKn1IRPWg3Ing1qOZgeKV39m1ZgIdpJqvlWVeiHBZC6ITRG0MfskhYe9cLgntfSFPIg==", "signatures": [{"sig": "MEUCIQCDGvlwtee2X3HcEypLVSY9oRgMI1AqzmVD9lMkZUxmVAIgdzVEGag9QJWykRrJbf9ZOCwKMZmDg3OWhW0PG1Qj648=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25653}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-external-helpers": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.23.3_1699513453548_0.16100028612170658", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "9b894e07f93db44fe11cd9f8bc2c138f81534382", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON>+Ze3Yo8j45ALLCYZDAruvhVnS9EMYQHo9PdWDhcKXvf43IOTmgb+neAUM2NGGvVuOy1mnC1lIPXXAFYV33A==", "signatures": [{"sig": "MEYCIQCOxQy/I1EZl+q1aralTloLafau0iql+UUiU/iWakXzVQIhALDXdsBRzvEXoAUux9F8Lvpe61DMhvI7vZlV03ibK7qc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25406}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-module-transforms": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/plugin-external-helpers": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.5_1702307975838_0.06197824371545124", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "1d6e75f995287d15638518927c136097801b24ea", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-sBd8ZnDKcHwpNwf0X/ppQjkDTRWeTKFMKGLdYIiU0HNrorkh6mmfIh92mHp9b40HU7MqGXwty0kHlcyY1IDSHg==", "signatures": [{"sig": "MEQCIAtxmujOrq3dhpCQbqViN8esVPRKWi2G4KhaDCLTuDsyAiAd4rUMe2CJDWkdv0vI9gTOsi3y8mFiM9luegxvRMNWPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25406}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-module-transforms": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/plugin-external-helpers": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.6_1706285676019_0.4308374913070101", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "39bacf648b566c8703062089198d1129344801a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-sgNX2L9yoijfVfSqgvc9KoETxtqu96Ev777HEqz8yiY+hDSXYWiy2f5lQ/R78CwfGwlEwhv9PZKb5GrZXzENUA==", "signatures": [{"sig": "MEUCIFVYTSTorU5d7BlCZ+1ET712tSqyVKSBw/NX0tFoj958AiEAvVCyBv4OQKnYvoB1z502nMC4Vvx66LJCzek6b4ZIMYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25406}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-module-transforms": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/plugin-external-helpers": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.7_1709129136721_0.15871841179756907", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "69220c66653a19cf2c0872b9c762b9a48b8bebef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-tuA3lpPj+5ITfcCluy6nWonSL7RvaG0AOTeAuvXqEKS34lnLzXpDb0dcP6K8jD0zWZFNDVly90AGFJPnm4fOYg==", "signatures": [{"sig": "MEUCIDGg6qf895pZ5ug1Jkvs/eqktlajLuoXE7gQI+1v64mDAiEAnnCGaz8PNj8ZmgSLrctIZ7TAsVieYpM8Cq1bMDByPZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25584}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-module-transforms": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/plugin-external-helpers": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.24.1_1710841723948_0.6166305952789028", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "9fa94922c0b57b043a681cdc671be85b25766203", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-WKwChFe2rXcaN758untgBjityNC89Zi7CrCVkXcAmHh22pwLWD+lnthdW+75GssgTJZDqQAJxAbsBvufI4uOSQ==", "signatures": [{"sig": "MEYCIQCUjQAqk82U0RfFuCZTkEIlrM5sx9JEdJu17yExOAPmxAIhAMJdJOJa3KVYWLmfrZZ896hL00rE3UT1roznKQRi8tPP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25320}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-module-transforms": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/plugin-external-helpers": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.8_1712236814923_0.838309134072448", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-modules-umd", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "c4ef8b6d4da230b8dc87e81cd66986728952f89b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-esRCC/KsSEUvrSjv5rFYnjZI6qv4R1e/iHQrqwbZIoRJqk7xCvEUiN7L1XrmW5QSmQe3n1XD88wbgDTWLbVSyg==", "signatures": [{"sig": "MEUCIGzQX8n6cJvXZFBAGD2aJnbT5GGX3WgxBxcA5hsXmF+5AiEAvarLdpyG0VlBA1DmaSvzruC5QnGLzyNjzUHuF9UB8B0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93076}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-module-transforms": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/plugin-external-helpers": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.24.6_1716553505377_0.43221769833231805", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "c0d4a13c3f5a1e370cc76acfbab7bf8874169b39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Q1H51FBQwZNd/m6J4iNqXdN/tNS+Sk0S8zLBPuIVvOCtzDaqwqc1nr4UssrLBefDxQCyvhyxuJMIR4nS2rrf0g==", "signatures": [{"sig": "MEYCIQCZlcLPUQ+/Zq7oQhyqsVzlETuIUdBgPrWAlepulj/xLQIhAII0qW1RevOCkBbDryojFeEPBeZXOlsPyJHI1LwAYWDa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93471}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-module-transforms": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/plugin-external-helpers": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.9_1717423543819_0.12296299267708077", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "0053be300e61d490a8f48806c9f1568bf7bd5305", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-XQd14aQw//UMc1Sod0StBOfkBYJX2fFoSSHphOhEXTwH3lq+rjBSbC5mx4GK0SNKeFkbwbB8FHKg5fs/35ViVw==", "signatures": [{"sig": "MEUCIQCF1/qbY8orE3a9DGM0wGBghxOmkU9B3ynpSquiWN0+lwIgKQsgZV5BdmnIpOcyjmGTZPEZlersoo+NhpC3uKNMwLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93480}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-module-transforms": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/plugin-external-helpers": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.10_1717500043432_0.7360338705549085", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-modules-umd", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "edd9f43ec549099620df7df24e7ba13b5c76efc8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-3aytQvqJ/h9z4g8AsKPLvD4Zqi2qT+L3j7XoFFu1XBlZWEl2/1kWnhmAbxpLgPrHSY0M6UA02jyTiwUVtiKR6A==", "signatures": [{"sig": "MEQCID7HRuAC/Nf+VAwxFnjLPa/OdJvZ+dI9GfikkEzKH/+FAiBXZfNgics+eTVoweAzrENw5sAGbeXsmI0C5y2A1qHw/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93003}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-external-helpers": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.24.7_1717593356861_0.9956777735376043", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "3f57a87e4eb7f1e6184e5ea73254cfdbcb4c21a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-iD6pjmjGRcE7aWWb2Egoul9KvvTymB4DIYCJadxyuf6GlgtyB54b14kwz5IHJzCPHH5uNzeHTOfk0Ksr+jx/Yw==", "signatures": [{"sig": "MEQCIE+OQtL8OoNvh8twJmyYJtDAzov163g7NqpbACbJ0ka5AiAlVfFgWJi5fzneSFHvvy1gA1eYJxgqcFtnjxfk7DnNKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93369}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-module-transforms": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/plugin-external-helpers": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.11_1717751767524_0.6889374727351116", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "416954c113d3633901a49876a3053e8d5420fca4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-z/Krw7JQ2Yn/QVFG2DAVxdVEgq9abEKzTbhPceuRdBfyhkSnFGKFy9XmiHkClqLvJRqAE4yOcfPDI7TNk8N+4w==", "signatures": [{"sig": "MEQCICZJRQ208K8avwTWczpppaav9HEZWpq7dJeu0tjzLzX6AiBSoVQ80k+7L+UtSb+5OTD/0SeUTo/1OnQQFzAkeXewzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90046}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-module-transforms": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/plugin-external-helpers": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.12_1722015242455_0.25241803149065545", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-modules-umd", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "00ee7a7e124289549381bfb0e24d87fd7f848367", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-p88Jg6QqsaPh+EB7I9GJrIqi1Zt4ZBHUQtjw3z1bzEXcLh6GfPqzZJ6G+G1HBGKUNukT58MnKG7EN7zXQBCODw==", "signatures": [{"sig": "MEUCIQCP02HTl3FD7VtoWBkBk8TYOLFdL0k1CsYE1MsnWxmNEgIgLXq5f5Rvc1jMT3Y8d54R7cd40zrZuAGAek5sa2ZKemY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97501}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/plugin-external-helpers": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.25.7_1727882132684_0.6961575884892277", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-modules-umd", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "6710079cdd7c694db36529a1e8411e49fcbf14c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-bS9MVObUgE7ww36HEfwe6g9WakQ0KF07mQF74uuXdkoziUPfKyu/nIm663kz//e5O1nPInPFx36z7WJmJ4yNEw==", "signatures": [{"sig": "MEQCICsALwoHBdt55N0PallqNg6WqLF+4TDMn8C8Y/MgmgMnAiADQxDTwSobgDRygwh0gDmM96fg7I8IrS6nhLrZm9OauQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25547}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-module-transforms": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.25.9_1729610508490_0.021140217845903697", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "ddfd8df386f63d4b248254938e37a77c0e419664", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-4vAhXD2UFsGMKOCm7yc8YhQqopSQHPhVqc4RwOTAiU9RvWL+gB7Mz46XVPHa0j13/I9yWUOQa4j+fOAs3BvLDQ==", "signatures": [{"sig": "MEUCIQCNuf1R64XhD52iAHa1vDVLJdNMIFoG6qBMSoMisz2mkwIgVwozf10cllSvyya86e/fF3iJ97FBJiNzhCioIsL6pso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26037}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-module-transforms": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/plugin-external-helpers": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.13_1729864487490_0.7677406139527718", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "2918e4ffb73965b6b6693fa3f42466e93464bc36", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-KFLx7CibHmnGVDftQSK5B3jlQ2PDSDzjoArzqLhC7olLK5AZCx210UyyOW/37y2OvI7kwsptBHrG2R3LWH4GoQ==", "signatures": [{"sig": "MEYCIQDsqysEy8ohhRtKUhpsjBDObczqUhe1N02EWWxr840ANAIhAKngTX9jZdJ3ozZh3F8NaDXT231b001y+Qr9kI97K75K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26037}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-module-transforms": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/plugin-external-helpers": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.14_1733504077534_0.5155284613968061", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "2d67cea5447a765754b6bda1d4b54dee3aa3e8b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-/ZMHk7zHQuyrfXk7u2D2v3AG3/NBW+AjN1cumtkzS6CYP7nDPiMS1KA4+p++4j/BP10l0SNWMgwcZDHbIeVyHw==", "signatures": [{"sig": "MEQCIEVdwcVf8DY8v+oy8OZ/6tmPbjepL3DZTV3bJS+LhNc7AiBFbqgCQLvlzE3yxloSze8hjf5Udl83hPHQQIVWxi60Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26037}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-module-transforms": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/plugin-external-helpers": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.15_1736529906823_0.21904687329452255", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "dd2c6a35dac1322bdc2a9da81ff9646fe8e3f77c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-a1VFL6oRrpwantZLY8iRvzswjwX3xfIEeKbJdvsexrM7MHBbZMtSFghoS5mjxz3lysICIgNkOMFIBZKUp3wr1Q==", "signatures": [{"sig": "MEQCIHHFVLS+VWrUZcaELajZW9GhT8cL/ObNk3SbYNKLHLHCAiAqXYtuOEvZ6J9U+lGNvg0WGu1BxOXRkbDKOKRdYNGh7w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26037}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-module-transforms": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/plugin-external-helpers": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.16_1739534381355_0.17273328286028167", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "5cf07468b1c6d14f20e1c48d19d1f59d18451611", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-99c/vv2y4OL5zSjhlLMU5ZmiLzrZWuEN31Bvtt2lmf2n1QLg7MF7t8mN7Xfg726kAEKdDyE6jlW1KEqJDCmcZA==", "signatures": [{"sig": "MEYCIQDYinbZRllnlr0W9oxTyKEGdVQgeaWa4ObdU6Zd746VNwIhAIBaJGnblCotKI8MBOhZFgqWc/20gwlAWBPvsLwFOMvv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26037}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-module-transforms": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/plugin-external-helpers": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-alpha.17_1741717534742_0.8726934453560709", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-modules-umd", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "63f2cf4f6dc15debc12f694e44714863d34cd334", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==", "signatures": [{"sig": "MEYCIQCDCwm58/mahYms6sIsULOEN0vBWdqdfvLIb8TDbwZt2wIhAMo9zCXCNNXxivlMrdOkD6JmJ1bCqnVLZp+tC1rWnV0y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25552}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_7.27.1_1746025769823_0.9827964176984763", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-umd@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "dist": {"shasum": "1b45ab7d00630eff476a7dd4de3602412a0694c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-v0wcoLbXJzSSsn7DBDZu9XxTwXeT3pJjo5iXST0/NLKqZ0R8mQafGvkTt3k4RXhjLmE8PhXPzJfuzwzftvg4tw==", "signatures": [{"sig": "MEUCIFsJn55WFkEylWocDicFoqKFZhrQMemw4fzO+M+RgbniAiEA2HXEDdVeLDkV/wbSLN6cWH5+RHiZEcH+YA38nl66ZNY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26019}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-module-transforms": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/plugin-external-helpers": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-umd_8.0.0-beta.0_1748620307052_0.48240004231299327", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-modules-umd", "version": "8.0.0-beta.1", "description": "This plugin transforms ES2015 modules to UMD", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-umd"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/plugin-external-helpers": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-modules-umd@8.0.0-beta.1", "dist": {"shasum": "8e9bf0e22bdad2e61a2e33f6714179bf98b3e0b2", "integrity": "sha512-KSPNiTbkCgkkMVULhAGwVXl8210lL+5P+p9C+CeAWxrTZIHpi7IFgicyfrD00oA3zES4rOdC8Y89NAPk/igI2Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 26019, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDdG9JoJIxrZAF4vwmPc1G8lZLUEVnH2EFDPr2FKmEsFwIhAJfARWkOByMaWMyV76APtiHdOz/QOjkQrwhj6Bdo9K9i"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-modules-umd_8.0.0-beta.1_1751447089221_0.9079656110627343"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:56.148Z", "modified": "2025-07-02T09:04:49.618Z", "7.0.0-beta.4": "2017-10-30T18:35:56.148Z", "7.0.0-beta.5": "2017-10-30T20:57:34.564Z", "7.0.0-beta.31": "2017-11-03T20:04:16.875Z", "7.0.0-beta.32": "2017-11-12T13:34:03.776Z", "7.0.0-beta.33": "2017-12-01T14:29:21.498Z", "7.0.0-beta.34": "2017-12-02T14:40:18.973Z", "7.0.0-beta.35": "2017-12-14T21:48:28.980Z", "7.0.0-beta.36": "2017-12-25T19:05:42.639Z", "7.0.0-beta.37": "2018-01-08T16:03:45.101Z", "7.0.0-beta.38": "2018-01-17T16:32:42.418Z", "7.0.0-beta.39": "2018-01-30T20:28:46.116Z", "7.0.0-beta.40": "2018-02-12T16:42:47.885Z", "7.0.0-beta.41": "2018-03-14T16:26:58.256Z", "7.0.0-beta.42": "2018-03-15T20:52:09.175Z", "7.0.0-beta.43": "2018-04-02T16:48:58.867Z", "7.0.0-beta.44": "2018-04-02T22:20:39.456Z", "7.0.0-beta.45": "2018-04-23T01:58:35.730Z", "7.0.0-beta.46": "2018-04-23T04:32:55.690Z", "7.0.0-beta.47": "2018-05-15T00:17:57.237Z", "7.0.0-beta.48": "2018-05-24T19:24:49.967Z", "7.0.0-beta.49": "2018-05-25T16:04:34.383Z", "7.0.0-beta.50": "2018-06-12T19:48:05.338Z", "7.0.0-beta.51": "2018-06-12T21:20:43.683Z", "7.0.0-beta.52": "2018-07-06T00:59:48.242Z", "7.0.0-beta.53": "2018-07-11T13:40:47.836Z", "7.0.0-beta.54": "2018-07-16T18:00:29.166Z", "7.0.0-beta.55": "2018-07-28T22:07:54.297Z", "7.0.0-beta.56": "2018-08-04T01:08:49.179Z", "7.0.0-rc.0": "2018-08-09T16:00:07.071Z", "7.0.0-rc.1": "2018-08-09T20:09:57.756Z", "7.0.0-rc.2": "2018-08-21T19:26:01.770Z", "7.0.0-rc.3": "2018-08-24T18:09:46.514Z", "7.0.0-rc.4": "2018-08-27T16:46:28.466Z", "7.0.0": "2018-08-27T21:44:59.022Z", "7.1.0": "2018-09-17T19:31:08.501Z", "7.2.0": "2018-12-03T19:01:32.203Z", "7.7.0": "2019-11-05T10:53:53.454Z", "7.7.4": "2019-11-22T23:33:50.289Z", "7.8.0": "2020-01-12T00:17:29.807Z", "7.8.3": "2020-01-13T21:42:30.965Z", "7.9.0": "2020-03-20T15:39:45.502Z", "7.10.1": "2020-05-27T22:08:27.629Z", "7.10.4": "2020-06-30T13:13:30.826Z", "7.12.1": "2020-10-15T22:42:12.289Z", "7.12.13": "2021-02-03T01:12:19.131Z", "7.13.0": "2021-02-22T22:50:34.349Z", "7.14.0": "2021-04-29T20:10:19.150Z", "7.14.5": "2021-06-09T23:13:24.423Z", "7.16.0": "2021-10-29T23:48:00.583Z", "7.16.5": "2021-12-13T22:28:54.997Z", "7.16.7": "2021-12-31T00:23:07.350Z", "7.17.12": "2022-05-16T19:33:19.010Z", "7.18.0": "2022-05-19T18:16:41.539Z", "7.18.6": "2022-06-27T19:50:41.844Z", "7.21.4-esm": "2023-04-04T14:09:58.571Z", "7.21.4-esm.1": "2023-04-04T14:21:56.146Z", "7.21.4-esm.2": "2023-04-04T14:40:01.793Z", "7.21.4-esm.3": "2023-04-04T14:56:43.894Z", "7.21.4-esm.4": "2023-04-04T15:13:51.957Z", "7.22.5": "2023-06-08T18:21:46.907Z", "8.0.0-alpha.0": "2023-07-20T14:00:21.854Z", "8.0.0-alpha.1": "2023-07-24T17:52:55.106Z", "8.0.0-alpha.2": "2023-08-09T15:15:18.253Z", "8.0.0-alpha.3": "2023-09-26T14:57:30.866Z", "8.0.0-alpha.4": "2023-10-12T02:06:44.242Z", "7.23.3": "2023-11-09T07:04:13.763Z", "8.0.0-alpha.5": "2023-12-11T15:19:36.001Z", "8.0.0-alpha.6": "2024-01-26T16:14:36.183Z", "8.0.0-alpha.7": "2024-02-28T14:05:36.858Z", "7.24.1": "2024-03-19T09:48:44.155Z", "8.0.0-alpha.8": "2024-04-04T13:20:15.106Z", "7.24.6": "2024-05-24T12:25:05.558Z", "8.0.0-alpha.9": "2024-06-03T14:05:44.027Z", "8.0.0-alpha.10": "2024-06-04T11:20:43.613Z", "7.24.7": "2024-06-05T13:15:57.035Z", "8.0.0-alpha.11": "2024-06-07T09:16:07.749Z", "8.0.0-alpha.12": "2024-07-26T17:34:02.836Z", "7.25.7": "2024-10-02T15:15:32.862Z", "7.25.9": "2024-10-22T15:21:48.683Z", "8.0.0-alpha.13": "2024-10-25T13:54:47.713Z", "8.0.0-alpha.14": "2024-12-06T16:54:37.698Z", "8.0.0-alpha.15": "2025-01-10T17:25:06.983Z", "8.0.0-alpha.16": "2025-02-14T11:59:41.537Z", "8.0.0-alpha.17": "2025-03-11T18:25:34.938Z", "7.27.1": "2025-04-30T15:09:30.000Z", "8.0.0-beta.0": "2025-05-30T15:51:47.242Z", "8.0.0-beta.1": "2025-07-02T09:04:49.387Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-umd"}, "description": "This plugin transforms ES2015 modules to UMD", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}