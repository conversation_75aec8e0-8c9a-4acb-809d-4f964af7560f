{"_id": "@babel/plugin-transform-object-super", "_rev": "114-c8901eb2397f4bd715ea7563ad3c9523", "name": "@babel/plugin-transform-object-super", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "76f28e9f4d7dd9f2d1accbe533856c726e83d645", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.4.tgz", "integrity": "sha512-L6l3JL/5l3CTrU4+iX/2GXnGP7SjmcGU/U8lC/caVKqMW45MflNoCsiTOrT7NDsfXGDlaAeLPySDX4QVepwO8w==", "signatures": [{"sig": "MEUCIFuGneDVnwlYNgtR8F7iiCezq1oH3oiYMZEVMYpdjhAsAiEAiN/XeNgXJk03BvEBieyLtcUq/nsG+vrcW1QPvV5kBN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.4.tgz_1509388579943_0.27549241832457483", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "78d23d268dd3181ad3be304f0dee64d44e75650d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.5.tgz", "integrity": "sha512-HtsVFL1NZxKaq5BUQSE8+fNSuJHJpXdQUxjx5box2zfwEFgIO66T8w2CnGu88KycMdBpPAXE58VYO2PucauXXw==", "signatures": [{"sig": "MEUCICj0x2QF5vlM1Zh/u5dE/H8lsF4QzjntZIHcF+Fn4nF8AiEArN8vuWqxY/ds+3GJUCiW58Uw7te5lHue0HTwFmt/Vjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.5.tgz_1509397077000_0.6984388311393559", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7626b60052c821ce2e83ad8ac0bfb142504975da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.31.tgz", "integrity": "sha512-eqH8BbjC8lsxUthONwYKav5KniCOL1HDunNJNH7ygHYn9PwndDqgeu829PUqhTHYMcX8rHhSa6S6lqmOlSHK2A==", "signatures": [{"sig": "MEUCICmTE9m7VjR1Zogd5te4xVKV2bCF6gB4uaGHujbM46EHAiEA9bOYQN0zrB9i1ox9ImjjqCDnepMzL6Zdeaafwmz/Uec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.31.tgz_1509739472002_0.195983971003443", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "21871fa8a272c00846bc908776ec952d9ed5037b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.32.tgz", "integrity": "sha512-5rcRZ9fFt8EXSz4r/BdlQi/LlUlCl07RBb0rm3VplAfN/+g1G1rcYCyivXHuhibVkvrdSAMbYZrQv955+WuRtg==", "signatures": [{"sig": "MEUCIAuLYeeHje5sE9VWi/4QpFrrpSuXYM8EpMRImaEk3W0ZAiEA3zrboiPJ93YpKvaXxmuA99oak0ZQLGga/vMfUvWQ/28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.32.tgz_1510493647867_0.600631708977744", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "88738ab0a397e45b89293ee352ae6aea4864494d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.33.tgz", "integrity": "sha512-jYr3EFLz2+h7iPKQUuoNhsYxToS3d8LgDSJ8+KGE5q6G6Qy3D8kwqnHzZEzNtbVEQwfaTQfWhSXDWRmbjQWouA==", "signatures": [{"sig": "MEUCIQD6XYJE+8XqhkNIgolHpN1EN/4bzeHCeDl6TSBqdVeLVQIgQkWp1A3ayyuUWEhPo6frM2ZnaAizY8NCx45ii+UOMM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.33.tgz_1512138566626_0.03969195042736828", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "02dcacafbf2b39ca71e7b93e5d8e1e6cf123b042", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.34.tgz", "integrity": "sha512-jiP0H9E0rjLVBKYHiSxO47iFyyKZQeSvViXFHipw93W7Fk+nCFZmtM1dl4Vy8KaKd4ET8d2zck83EcPU6flPtw==", "signatures": [{"sig": "MEYCIQCEERnwA+v3q+bI/t1HW/kwEu+NQrWidEpGGGM0dx5+EQIhAI8upwBDdND4+kVHmoGEUlsLibK+ZLqXPoKbtKkwlL12", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.34.tgz_1512225624973_0.8304942096583545", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "768dd11a3d336a96b42fde9f2c4437c19ab548b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.35.tgz", "integrity": "sha512-oEPs8cPd5vAYExYWjBECl84ZOjFUj0lLGs0vrUn9jy5nbrhGQi9mjfKs8kV0kmoXsFLXD3LLrsJj4G4JPndvMA==", "signatures": [{"sig": "MEUCIQDUHkS8uoumySuQqkC8SFDjz7Km3rLYB2paGFKqVk9Q8QIgcnk6MUz1ZML42jgrMLuXmhcer9ahBcz+DkEcyrumeT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.35.tgz_1513288112819_0.9506645807996392", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1de718f2d94b5cabe5c53b951b388f86cb6422c7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.36.tgz", "integrity": "sha512-+wY8eQ8H7uQx2Zphkw6TRqAfRitgfcSgPtyHMpM0mpB9+JH2x1PZkd4s0E05/rcFHVvt3Dnhswe6braf2aZQkw==", "signatures": [{"sig": "MEUCIQD1HHvudwWer4S4hRiGZHWek/r4ONSPxC/osh7Nu5YhzAIgK6oA8XGT25Yl198wXGu83gwmvDSGHQWOMx3j+QFn9vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.36.tgz_1514228748956_0.6044170560780913", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "43d12ac6435e5656303b2f5572f4560551c0de4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.37.tgz", "integrity": "sha512-otRmMOGmEV6p3beBd9ydc20ElvEr2WTUdxGZKpjtacLfu1p0Aph7ISp50S+ngvxguEz5/1udB9W+7R0dHeKnWw==", "signatures": [{"sig": "MEUCIE+iTGTcrZYHN+eEUG7XCuDbv6FixkiKNV2rtX8SnqY3AiEA3sPYSCyyJQB1dcPTZZmhKzd8omrPWq8xjvXnO5HqEsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.37.tgz_1515427428263_0.19192941766232252", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "11e537ed0f64d6731a82f8bfb882c60308924367", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.38.tgz", "integrity": "sha512-aPhC<PERSON>+JFKt4/o8q9tLQUIfX4qQrFcsHzax5dkO7Xj1VZGZ0RZnJbsR32k0oI5/XhOVnyppLDAvkDOcEo7rm1hg==", "signatures": [{"sig": "MEUCIHLEpW0vidIiztqPf3R+nuI897WuNxNVBHc05i3EYt+PAiEA5h7+LR94GhEe4pd4i3hjFweo0kZ7gqIejtPIQOOTUpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.5.1", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.38.tgz_1516206767391_0.8416997399181128", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "4db6dfffe5b70bdb068ecc5c69394aef24b85d8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.39.tgz", "integrity": "sha512-j1mtd/zuBU7yWLleS9YvVwSr89glnkaExoKPqEcth6DJ2FvMmvE9T/gfF2JjIk+8hVqSwdLXG6z2beu3/4qYMw==", "signatures": [{"sig": "MEQCIFqgM63xlKr/p1HgapvtWzNyvSwDWEBEBu9eDWMhYT5qAiAKa4kRJZzYM/tX4tIrgtvGjwx2fpZD92l1wAmqDLQYLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super-7.0.0-beta.39.tgz_1517344129067_0.6074545131996274", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c64f9ba3587610d76c2edfdd8f507a59ea3ba63d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-a9kXy4amuvAz7eFuntXiyjg0eKXej1FH++xQg37ugh24zozD0cmfr3pvRbYOGlmbmOeZWJnlq+O6X8BSfLSycw==", "signatures": [{"sig": "MEYCIQC4WGN0otwOvUr7g6rVOuRqFX81HwwYVq1zf2l0yvI+mgIhAMKo6Z4jMxRNG+uEle7YuPPq+bRWq77oNulVTYtr1CLp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3973}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-replace-supers": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.40_1518453774978_0.7647130311311789", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0fe352a9136431803778797ef5093b0df43c8057", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-XdeDcamgfLi6ADEDtpD5ddrGfxAvmzySAc9I5vKZFHxD8zxxifUUna/eO7KTeG31NChblvXMr9d+9ARlV6Epvw==", "signatures": [{"sig": "MEUCIQCgVP69QZLWGNpUj64K7gjUCINS92Bpe1QIl+jtqjSHqQIgPagaawwrBZHp87HAdpCuxaTRW8qWx4HbCbsaViJ7TMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4184}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-replace-supers": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.41_1521044825972_0.7468122535396404", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f19ae6007ff675ea0f52499d09f73ae9f96db1a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-d+gki0bYQtlleX4LnvpwjIkjmZScKQuHhDsD3N3nHLpwxbANp3YGtIfuf8dbY/PIbUKtd55/ErCvdsyuGlxAlw==", "signatures": [{"sig": "MEQCIDzHwhBJU9F1Dzjs0ZtJqvcz9/ewmL3qT2DoRLCjAhD4AiBaqzdiT90PSeWTYanRvtMOmQVEgKvpiSYSD37KaOoUPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4184}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-replace-supers": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.42_1521147134563_0.7294373423022009", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "19843f6a004f4482f183c39c3efe1e9388c9f4fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-imp5t442IOMom9j/r9vlENuNBENA2l1dxH36+MQllMF0xO8fCcHTtHmaEMShi144sZYkZxqyMBzuPgY4/5VA6Q==", "signatures": [{"sig": "MEUCIQCkPJOIfJQ4FeztbRFgEmJ5FR+A1ou7ftjQMq+bOF23qwIgX14lRXUB5wXSeu9VVEGeSTX+0O9Qf1g8C2t1P2ben2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4012}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-replace-supers": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.43_1522687743316_0.061065143359342766", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c1688a7b38c4de8af269ff5c618cfd602864a39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-KlcV7gSBppnANHxCk8Hxca2PCrKOAoSTWj7HxiGCwrOcRJeMYPIiBayExGmfN7Ymvt0EpgSvL8bwyOPMk10mgg==", "signatures": [{"sig": "MEUCIQDvxEGRRplPZhUEilzki0y4JjfVfUMdpN0RNETdr3ZUxAIga47KCfFYHcxzJ/yyTpuUcD1rZfMlOuIz5FWjpsEYI0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4554}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-replace-supers": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.44_1522707644276_0.7791519088706949", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4283c46cf7f55295573f69f3bb331e2b9740dbe2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-kiBbo4c71Iwr5VJa1P/ZbOXbpcDymoaEPqIRGgvY8wEawiMrsxy4bwPQk9IHggRiqGuzIKDu9DHfy843qf+DPA==", "signatures": [{"sig": "MEQCIH+PlUJvWe+lHfRDXvb6IqoK3ldRfLcOg8hAJClVygeVAiARRj0D7ui7a/aEnK5kHF0B8nKDJcmpYFoTFQC2RVbYiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3fCRA9TVsSAnZWagAA1kkP/RgSvdpvSyiAAHDvI1PP\nhuA2/HksUcXE61SgZENb585w19fS3zkWd6U+os/AzBKh+HC7e3eWcPfppzbW\nHApdJjLm578ro64eKC69cneJ5vnHSEhmcWDJ2NAxGr8jo6ptn9CWcRkaLtV7\nHMpSBrhADC4xxoTFORZrxKGmN/117becHmF/iol+Z5V+w9Rj2nodqwVDTUT7\nIetCWr/wf2rVQ4Nd3VlqpMWrlpxjLcFj7/QqUOOufr8tN2rixd7oarNgqeIX\ne/wMA8fvoQQRpvfObz3hsg9qW+NdWolnBVnUlUqxVSUPZjcSujbExLWTtaND\n+NiZDs82LHQeNHimVpR8Km4EGlm+sGfXSg14EY1wyv5BLXS8CrbYyWgnFnIE\neio4aj1v5etTr7ka0gjlkvF7sB9LArsat4b0A8WA0FS2q2bfiiP35D7CVK4c\nKdoVx5dVIJCRWzHwVqYMl1AVgTSVBMF9PwAb5apXhjyxyuTL3rSdvwSddlqH\nT8D2KXyfL9ZSLTlHTO5Clztpj+Pnjveu6stCGDzVlpGeTing413V7K6LjqpR\nSRGul8bKbXOYrd4Ucp/cUiLKX6JLusAAxou4WoIni/eIYLmW7tuW+px6K2ch\nyRADRmmiBVfDAK/hrOtVGsksLLcDEhJVTDzXaI7MHCQ4cnXrgtwPr5WTeUqT\nyBhb\r\n=mGNI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-replace-supers": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.45_1524448735037_0.09545972786134715", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b5376fe93f5e154b765468f1a58a717717f95827", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-T+TV4rRuTubvjqEwBF5xib3vnfJgjQ62qJqHprTaJDBtF0ofpbv/A6x86EEr51iPymHc84eM+F1zyFEtJvKYwQ==", "signatures": [{"sig": "MEUCIBpyilZcdl4uRBWMeMtD2xoIkUibg5hrcnlx7eV2zFSjAiEAkFddFLwh8vX2eQRycqOrqeRSYSRKwgqVT37JIBEQkLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WIICRA9TVsSAnZWagAAOEgP/3uppXNEmmvhOkpoIIhj\nvu9JRATS0vFrW9eLEaGRUB8uU7HMi/HGeJbb6Oh/+YJHpGBDPvimR3CGxa6F\nFtlmKLkh+GdIYWaf2yrTo2Tz+w50zXbBZL88edV9HUJD8Po9BCbCdPUn4j5b\nxUNZHMSf7gk5M6/EVjd9ikuRzjhdkvfRHbeOPrWPXpYTvWLh4mI6QWlN+5W1\nTDZjP8+EejlHMoTwpSHYZ2unmrGmPFEZpKjlds/iCJ8tUtG4++S4Jy3Tkmma\ncpq+65Mv7l0kE/utm2gyF9OUIc6gbOP291QY5O3nCuV8cazQYd0CK8vZMfMi\nDf9lSdL+aTWhnuX7JYwRPK+zXe3Z59DSq1UDtp1M8nQ6jsy9mNkgbgTUGUW6\npvIwJy6f1d5xfhQyfxwirleIAH/2i/45csyf/I1mutedwHKC2/b+efRg7y/A\njuTd9f3p0RG+t2TCE6WBhI66+sIeEVVsdt02OTUDJbnYw2q0vEoxuKfmIbvJ\nQh6AP7gKTmYKehvnMZOvjhu0pFVXMZEp3xRo6euBrMrXTSKyIX5Z61s3QZKh\nqTCMhpSkp6niO2ryVcZ6s1sNixjNmirjKaZryHlB9qLX3547M/u+e3DjfJKF\nn7AIR/TWMwkcQ39+1RwPPNpzBPndpwaiVllp6mrMj3TgBY+1N/AAQjsrofwl\nvT1d\r\n=DstZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-replace-supers": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.46_1524457991589_0.9389978687167397", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ca8e5f326c5011c879f3a6ed749e58bd10fff05d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-JN6ox2rH1xe6hlsd6/7d2kPjZSA97wH4oOdNOSaNqaFGEFuaP/Je4+ojHMgyHKU2nx9QHNBCTxHEj+ko+Ij6HQ==", "signatures": [{"sig": "MEUCIGkoNA3MutExgWhkBROdNnPawTFBiSuGLCf4zZkC83Z7AiEAgkCaq+uZs1iHlDe3iR9hTUrSrfBvuOjOx0S2miFWg8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+idFCRA9TVsSAnZWagAAnqUP/R8I1aI0S9jMT6AdBuAX\niaBcxG/8B9d2hDRK39bB4C34vET7UCkRNedK5fz8yE6Ser3jmoM3GPIQsqiK\n3vKGw9Vz8c/9lZa/RF9x+zePiAoyjg/Ee7ZhhGHU/61AN7r2sC4JtaA07LM/\nZ7QJgOm+JMwv7NhvJqlYFKDtAcJaXUPGDKBX6TNm+1bpdkuKaexeNehOSwJ/\n8qqE5YymC6U4lPBcU3M5//gXYsFuWRSuV459v47CVHEuPZECWLaSal9hnlwJ\nEe8NUSr4ao2e+XYF5rO6gT5HqrSbYor3C3dt2XrWNqAYbCfB1X3Z28N86YD9\nbxt2WSxZJPAGsq698Tg/xAYWAGSjNcWzLJftGQIPUBCRwxhIvbbNB7oozzDZ\ntlowTaGTak8guP9dlIlt3XSKgfEBMT9voyXK45x1bJV+SCTVK9AwgLwtz9Y8\n1fBrQV6i1ST2/05+7Qz3gshztGoSVbvXLLzyCkkrRtdyvpSe+wsYXaCUAXk+\nWeQs23WBkH2TSBe3RjWOyrZTuILoThahOYZx6se7aDsLmonmNP7iP0pCANn3\n6XRKqD09mB32abiShpLjFcEXh0/euQTg83SMqzwRDJw4xwsp2qkObY18SSgJ\nSO1PvLt00yo5wDaVsI7UW3DnZq+x1qqW1ZBBdc2jN4wNYbpjRjTP7OILzRaU\nMJmB\r\n=8t7B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-replace-supers": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.47_1526343492629_0.8739694968494125", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a8cceaabb5dc5cd94a25d3280a55b4010b4ded10", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-4lykgbUgoGWvLPivTl3E5desyh5mIOjA0op8fOgdXjDiTKIwR0v+1mFfgfIWdv7LGMSam+inNVMLp+MvnEmNIw==", "signatures": [{"sig": "MEQCIB7s7xNVxOHVYZBdIrNUpJteBVYxwMoUbfMQmOZsrCjNAiBjqL+n0EYPNbDCVBvMthiQupgYdXDOnWSw4MJKuyTvFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxGUCRA9TVsSAnZWagAAMmcP/A+4z6xULQsulHtfXyaf\nBJqH0PEB71RS/4AvLSvK4EUlvbSDVQOENVoWImtDtVeWHHqdQPFkW6ZlqXby\nrvq+jdt9Jls8T8ps1hl/HNEkJR7Gdng0utTYepodLa0OCcA05n3bCDbU5S3y\n7jF/tIM9LSmw41mve1PxrQLbaFS1AcMsCe9v4wOMMxK5kEn6hly+2cRm+qT0\nTJsqfm5mUExVbRUDBvCMOm7QEFzzK55qsOIPeBzLSc5d/SfpwHrgBgurvuO7\nnKUIRsKiva3MmafuVTKb9fxEqLcZOvxx94sLYOkrPpsknSHUPe8LVnDILdnE\nYX/dlb2MHnOfUfmhxo0jCUziSnnoogfCtb82vfGFcqisXeGbuDqlQub6N9ys\n85zqHv8b2ln/NJ33Z6WO35sKUKuaeOeaU0VptecxyXQdnM6xBN1hzi24uzHb\nZfjv148ZwVWdScZq508h7JAmK/L3qUcd4OgGjwN6porZ+K+5LRYwixUa2KHU\nS6WPPqxDmG9fybM8/M16GJmigPD1XkuJvtorQjvyNW0OwjxjOw+sf2ugrT5K\n0jwCo5B8877/SrJDKN1Nv0jY9haf0q+WKEO8CyeDkpSyHy5nUW3HEmZj/tan\npFtcfPrda6oKNMWgcOaFkfVlfbydeGeg5lnUeXc2BruTN2s889L7jhqPJ3mD\ntPLW\r\n=9sHd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "5.6.0", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-replace-supers": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.48_1527189907536_0.21121768626569137", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b302f55702847343c10ff4fb8435cc3574755fe3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-q+Oinb0hUSsgKhkn//zLnqtng5JxA3Zqxr2kUrfy7Y/EWzbRnZxdChliEwRugmKAF/SMbA2IcwQhtBudaEpLHA==", "signatures": [{"sig": "MEUCIF+wu4VclqH+QhtQp2dAkA2SoeAlCWmmAn4ocRbMpyCrAiEAjefGlfANYgq6OmcP65oFJGP+AX/sMTH+kOfFw6Csw8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQoCRA9TVsSAnZWagAA80IP/RNjXAvVqeMoKuzueFne\nZw3NKsJgNQqqiq1JDafSsdc14e0aF3qeODgfi4Ku8Z63dNtNlLxczNzWJbbL\ngwy1r/AZyzekse3YkMSvuLyEhf60ZWd3mOuC3CzVZ1AVASfhOr3kJgxRK7Jn\nRpn+JU8PrEy0C/5rbn8sPkkojqXI3M2W1eTXu3VC9j53Z4aFtsAxxHucc8hc\nxqGkQcYT+yGWLfTimZDaLJiDBQ3rg0dJm9LaZeb9D7likm5lyo5XWdXQDL9K\nuYh9COFDCV/3opjCOd6JhFZVnyCldrxbICJ0NAXVx64lAiVxRmsNe4Y+eIGR\nAnAl2lhvQ6f0D0KFCjp9YAVeqDUxoUES4Ku3Hq+md65QQZ5uh9RNMJp/3Dsc\noH31MYIKmKPEt4VrRkXpaZ3DgLxLDMQ7y2RzcTISGm4JqQtRFfSSph6KL9hO\nH18iMQGdCrp5GtcShFyxQY26gfycIzn5A5K57uKqzl910omj2da6IkS+4gVw\nz/7O10zE0TQLmtWVfxrbpo+340yzGIXOc4sgxHi2bLJra6tT4Q/VhwmvxReX\ngMWsj8U7bzfD9H4h4SskhQJzLq4K5lGtVt9rhEpsF0euZy3pvKWW3mllbx2F\n4RlObdcWd/o6TLshBcqZIuONcvikf8iiOyuouVUaaCvmY+7NVS9a4sv19gvX\nSEVT\r\n=tnPs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "b302f55702847343c10ff4fb8435cc3574755fe3", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "3.10.10", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-replace-supers": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.49_1527264296089_0.41540450405827056", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1cf787d01613a4813bfc82b6e9d59edb7ab4af34", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-x/mt4VgXd4a9TQ8383TbRJiH0r6Ku6NqMrDJuQdzTvygblQwH4siDJUH1g3joZmQ+1FQ6sfqYRmg+qnd5HrnbA==", "signatures": [{"sig": "MEUCIEmgiclsBgPvNVePhklLJo6+6Hu+WOq/VhnukDO6aNbWAiEAykl5/5cIxlrPixIVe78kVLnl5LgNYFphs7foDKM9pkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2702}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-replace-supers": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.50_1528832898568_0.7370439942711342", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ac18e88bc1d79b718bdaf48a756833cdf5bdcebf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-1+uLn/yAE8Mgn32ZDRVd53SF0869GW9W02Z4YAmlePhSbvICJTKuGkD7rh8D3qARFj5TzrQE/LXRme06fxhpeA==", "signatures": [{"sig": "MEQCIHV5FW6fN+STjQFcPc1ijNn9XQWMlaB1VIT31nC3vluBAiAtsJ/rXAWrqtik9Lw5CNmbGJAQAY27gBZcqsZ2KbZaOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2716}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-replace-supers": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.51_1528838461703_0.11352197919528195", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "06354288ab303480da2fe3a68186d4e4582a7dbf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-Ktm/meTpuUWFtr/zJbxk72qvcLQKzymhXBezHSw+MUvMdob+WwOzWFsr7Y4VCRpIsPJUTwRZWGIkR41DRCPBLw==", "signatures": [{"sig": "MEYCIQDXNb6Nn71N7w2QZ8IQ88/0LKLoJqvsSOL+QDMcn5Vn1AIhANGwN4kvq9MAsg1kr/voFUlfnIKBz6j361z+4GgTtYh/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-replace-supers": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.52_1530838791231_0.7845759028436807", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e2c4f06edb34b3d7a4b2757ba18829d0df2029cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-ko8FH0QnK76kZoNmP0KuZPFRlp2D07oLEXFylwpfOw6v2xmwqxajGiL51qrf0fhS5CT7zFbkHDmZljPfOo6Tzw==", "signatures": [{"sig": "MEYCIQD4V+sxwBZF1fuDizXN47MhTDmWvFQgNhAQCZHJUlzjtwIhAIbgwRhNiazgqYm3Kazbdh6Lcyo+hVnLe4KuKBgfSMRk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-replace-supers": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.53_1531316454283_0.01627594631988294", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d25fad66eff90de03ee62f8384f0af57bcd065d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-OdIaC9pnFW9G33pNiN+lB+/vqo8y69e9+F2WWczR3u88gMqxSA2bl4ZVenwZkfBwgZfS5gUb0LFOkxqRzUWrRQ==", "signatures": [{"sig": "MEYCIQDFyWXGKqiNQHmhGoaRMNtZc1JfXLj4VKqIZ/1p6GD1dAIhANRlYSZv5DU7zjk3tG3H5jjTDdRCHvFpB8lrAPY8drYE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-replace-supers": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.54_1531764032357_0.7502410807115827", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b518d13a90352128191514d7d5db8e5a78c9992b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-fjwLxMFk+fhOx8mvCMyNBZfoc34KQhy6jDRaGIbHVNjWBDWhYU76HU7kv+W7tlsoACCD++1yNXFSRlBRvyYLbw==", "signatures": [{"sig": "MEYCIQD+GoEd+3S6H+pHrS5EiPQ3ooo1SMB+/Jjh6dEwW63HjAIhANopwsDm8ivtb3jd62TsCRXnzzX28cCZdbfC44I4ARSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-replace-supers": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.55_1532815717180_0.8922644269980213", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b02d5d99aeffd9770e2a5de95260bf720f90ebab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-GB6OyxC16aIPE7lm13xfUsoP+zoqCn9PaocXzlGbQbotMoQ+qkmv3Ymq/Oy5YQl3kl5xrxX0JKNuOhYUjhG4YQ==", "signatures": [{"sig": "MEUCICwPkLxhMGGP1ng3vq5/vex0Zx5qpuxa1q1gazXpr4gxAiEAhKXmgj7SlU7Hx+2PmdeMlOJ2BpMJ4fO3w3+xlzAuo+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPy+CRA9TVsSAnZWagAAYRUP+wVye23NTlf1G0evOCc6\norsMnrvg3tsugHV2EhC5VudXQkMAiOuQj53f5vPZ/SxFBXAd9wtwgsAI9Q8/\nHq+OIAOkHZcjGddqAS5UPBaTMPBijOvn+4E57YfBqazWmqVGijqE7w3csOpU\noajmW/Wn2K1dmv8TwnnxGcy6lTsB0+DMNokfrp/wlJOMKdb6DvsfYXkBIQOu\nvmn7s9Xo0b0AkEaxw1UT6si9TyfEqY9f+MNB70dwg7XS6FG7g2NYVgiZ7JcZ\nChw4nPXIO8KhFDgNsz9JwAGaxbagCWyMMbVdnegI18sQgdIrFAhDRiDI3mi1\n7Daa2QguHKrG3ouvoZEKSa7XoKgDdOIacBtvfs/ndFKWnFIfFlwj96YGD9R7\nloSpMUzEl0WdzlwAO9Z/99UX08DlbLd9TUixhXPNqswmzsZkibNSQ8hVymj1\nj7J/+qn7EqOgsVsRhi0an1XyVpIKA8gxp/ALWxpjK95gTFzQ7H7cpA6BKvN9\n2RvV7oI3wdza4yzBIuFmO6Zt053VwVmer5fIVunSQYNSlEy89BnfgHgcoI6C\nivrXdfb5MfsaQNwBpjKUSb5/2/Im/pDneIT3Koo/RWwRP+c3s73qEVCgO2pf\nYSDjH1oReVkrhq+GUZNE1rYXRXBlL6wudP9ItX/7RLmCh97LI+VBGy+RclTL\nl1QD\r\n=rdO4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-replace-supers": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-beta.56_1533344958006_0.008369515652117743", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0590e853dd655006038892404db7736fdd262699", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-hdRlROtVdWLY3qxYwPF6HyfGWHCEz1exXoIOdD7kAhwhQixqdSSFsdmi8hliZG+35tVz7FpMq4F+L5UdpIeN7Q==", "signatures": [{"sig": "MEUCIFIb6EAD2tKPQBB+MFSCBtsPgUIXj2i+e472TanP/4pZAiEA1O3uZ6JI/oJAuUXeYW7DtmlYaiiVbei3Mf/OgglH33M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUeCRA9TVsSAnZWagAArRQP/icVOy/DxYXLX+v67B4I\nIoHluwbNHFgrOhxg5nH1NeoS6h5iRBOU6d6KdxP0uHd2fXsX6DXNyyKXgF+u\nn5s9W7SLNKEtMscQc2X0Mqw7i5/uq7G7uBXKXUPqpO+R4uVxoKgO6WS5oXHQ\nphvTsjcDAHuO3V/o/0HQqf49aIKNy8a2TJJTt2NludXKYZeiLg+pqzb23Ewg\nqKpRukv1cK7c81tvJQnsZd/cJ+6wRwrXr1OqOuWWvgS8dFmFyab0ef1Rr/na\nTcxtb5YFHHUszxoLTIM/NWLPorN8FGIYSF+k3kd7XmPTFfwK1lSn6aoVsYU9\nX6IowRmiO1YQIegF6v5v/5VgXTg4/imD+naQtKVr1x+46OMy1YV8IpXlcaH9\ndTV6KTNaF2M/c2kDDrgbNSSlMMgKm31BMeUA1+4dBu+sok9kC0LpmiaWCxPZ\nn9AQVcsZk0oQwIn912sjCyKtoVxdPeZx+RVXmEP6uuwcM+AvYfn3l8vNgMwj\nYJEoJ7YEqhdCnrDHpBE4OgsG55qGZDsroP/zKteO1ZhgD9u8Z2INUQ/WmInm\nkCEHoslM+Djm9Kn1NHvx2Y4DADpOOsLZsNEGjfzKQCnNJeamd6aAEFhKdiDy\nARcJvRVY/5dVhF+Bb7OIYLXPjQcX/2FgFqKuKcLWgMgJ8gJP3gCuJyduZH+G\nH2Jx\r\n=JYom\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-replace-supers": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-rc.0_1533830429875_0.8176093338572821", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "03ffbcce806af7546fead73cecb43c0892b809f3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-mwoid0Rx+L55NupRE9xs1JAgFRz0JIYS/JR0aqBlLOQwBY1KrbrAtQfNwHQobwZrP9O24VBRfViMsiYLh/UV4A==", "signatures": [{"sig": "MEYCIQDpMpKu+Poi5MZhgGZZlapkyaMDSCkCEa8cUYZTYA77bgIhANrkuN5ZwE4WXg3zcZLdL3v93wFln6zYMOszi2LT7rUr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+lCRA9TVsSAnZWagAAhpQP+wYKy+QVMQxAoBPh7Bt/\nCzv8uiYiMG2KCwFtgLpHsfFbtZi//4bfYZRgODLJBP/JXJTHosvJtIsR6JoC\nbyBuXcXiUebmxD2jvB9jagvKThCwtWsN7hxOqLHoZTI2H+DIVCgreVwHrwcC\nQg+KMMdWnp0hg2U3k5udhv48OlSCR7fMUx7I+j6zHRC93D+Nm50DPPS56y7F\nF5J9NWYRaDo1kCE5ZgUx+nvH4e9HiXFMYnNWdXFMsL0OGzs3oJ47Kb6oQOJh\nGFegq+121AL1NveX1UmEY1wSWjEbuwC1PL2ABEVhESbQPoxE9WHtCFFu5X3r\n61XJvz5RWUdVQAlhgcwurWRiBmpr6KdePUDETXbYQ78T1iB0hQ0bInZoENEh\nYOQhpQjH46YioBIyPBdaUB/8s/5yM197g6wG7yX3ncc9MFyzQ0vaKpOuaKnx\nVEhgAGO2hnpkEhk5xprcOKoA9JuW9jHHykmgZMfcvvsk/M1DjNxRS74shsoV\nDIdlFPkfya88cNrwf/NGWzaim9feCPgVG69B7RRbaiV8/duhVpbq+n2W8I7v\nsEMPQJU6ILjhyZrrRu0RBcLENdJqLEsqIVAV0g9Bv0DuakxTloLzHIP/TpmK\nT77nLeTM0vUQwlGFjsb6bPV+qgYliJ18CCZykCsB1Dv2GjjM/HSlptKLpZUB\nq9MD\r\n=uvIc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-replace-supers": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-rc.1_1533845412979_0.18436515493904682", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2c521240b3f817a4d08915022d1d889ee1ff10ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-VuV9E57/MQLLw5uQ1fiVhyC25gTTa+rNUVz6DEzInIV81VFm1KM0U5/b3FgTqtLMMHp/dFDExqwkHjmvlWpdzw==", "signatures": [{"sig": "MEYCIQCxTZmarlqJZfBfYoB/pS7YtOBmZtbV5iaH+eeOVB8PSQIhAIKA30+EPsnZL9iJFf2UlgBKGhdFAbAN3yR1VMWRtFn1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdZCRA9TVsSAnZWagAAa3UP/2zRzeeRR2bnEoVIprem\n2YxRNU9hno1qdLGXlheAJoDvHWjhi5rN3XcG7tMWoEqMF5vd/9TzJtPCIhTB\n+5pO8yn9l7vISQn3zbvCJI1lFoZoj8Lw8CUWTKFFe/91W9MxfvxKqQtXnSJU\nXOcOglthCQM5N3aVcLbQPkKRRKCD9Nsx9U3TnX6nsrBYpZad8uOJclakwJzl\n4T4U4+oh3KF1koQXUq/8c0/72uSwLqI6lkk9OUcKDEvJT4KJY6CucZF+Bpfx\nXk5XHdjhMbAWxE2+Stcr69VbNsmMSVcY5Ph9h97vhddYhzWOS8u9CG0/FtKe\n4/WHG+D9OL2nPp2KvwyeN0SEKaFNl+bkPvxbE12sDimQQzKsGosKlKLL4oyp\nQOz9UDz5UrSliNVS4SWC9jcrvF1kp62DsRg5HqFuos0l95NeK0+fmVA3Fic1\n7zQeoE5WHndSjSZK7uZenCpHpYFynxQOe3nsqBjaJSk7gM+wg6GteYDoStyQ\nFbRMNCiqN/5PlGN9J7CyLJARW90GpkI4Kq1QtVFOdu0TnbtY9MuGxRT4N64z\ntIkQ/ZnzxM6nO5chbVi6G+8kIySZ3lh+TMkX+5xQu+CCE2gpF39ske1bAJVC\ndCemqyGnr4vbrlJwrWr6EWTdpj1lLlGzRDtCtiPkQ6avVwE9ywRI1UbKK/Hy\nwmVB\r\n=yF1w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-replace-supers": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-rc.2_1534879577335_0.17928088515995944", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c938c9e97df14c3d340d4be90087ee0c71eb871", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-0hah7ug5YFT26kAUbDnlZTsQqUHTcwNSnPp6xw2yieBbkspIOhrhH65gcMgG5bSQE1bi/ld3BGWWf16mR4Smqw==", "signatures": [{"sig": "MEUCIGdkhY28qa9xsAMl/LZcqfjqAk/kPgn3O8/E9Ghzwe0EAiEA48BErzrR16HvkLt5QDREtP6bNPTJa1+6obiXVJ2Aw2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEn5CRA9TVsSAnZWagAA8icP/RwizsyyieM5kNXXPWkF\nM/HK+U3LhlRtPNIBgVPzJm2B/QCTXGXiN27zDIAfq9QXWX5SUcugKXmAVKcd\nnjThEgeMOmgTgyOG0ncuuSy5r2cXmVCCD7fjhy8eAQukdD474v8+m6BsA1Wg\nhKMbXIhN4wd3UGieL3w4F0kdipqakfPla9we3VOn9RICsVBf2UGpXIpuFP/G\n8Yyn5Gr0tjGKuu4YJprzf8odcBJHDkKX7tgEOCZV6Lxs13geZKVqYA2jTl5C\nf6FdQVIh1T5ZlED2k9DtgV2cd1G3uqnUNEL2u5b1NxDiOj1g2yAPPmTJK5ht\nj4taaTaOMH5qCDDqhYqbI1ju4n7L8dQBZ+6Hw9+g0A3aV+wh0SEeDN1hR3lI\nfrkoeL3bytcCa77rHkqieRphTXHC54s2Lk8FZWcdJSEejto/V+bQ/G8RuVUd\nUUkEx+Qp81ovIW40n/nWLHg0/aKL+nZxd7o1UDOUron9HsjiXngt+36BmkkE\nZdtxpg/qakMTBCCSjCweUPYMBgMV1x1I4KYBS/jQknU/+I8za4RoiKXLFJKC\n5YBb+GUojzs9oydH1n35dc3z5Xg2qfKgY8pU+Tq9mAsWMHMZE4Lmrn94lKKb\ngEpbry4vDyAkgPXqQe80ReyMWEwZabbaG+gv0oT5UXyiPQ+QIPJyG6ZNVOLX\nqagz\r\n=VG0e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-replace-supers": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-rc.3_1535134200539_0.19291017229204543", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c5e497f31ba2cf8b75320fb30676867a94531d03", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-AK6VYy7jSbsU614wNs6P3lunCPcjrWKYicQO+1o6bIq+GbCOpLNfDl9E3mmEeayf9pIJbRjzcUyWh8bkwuRrHQ==", "signatures": [{"sig": "MEUCICc1oAPqFHMQCOrnOs+4smjMZB9JgsLMKImwZfFwc1lXAiEA6hGQo1Gl1eAvTHgA+20mYcAkXXt1PExTr6ALJllAmCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCr7CRA9TVsSAnZWagAAafcQAIUqhBWnsDUCdYVqMdqk\nXS5t/ORAINVB9MZ6W7fEVugk5bsPU0X151t90yBY6DR7/i8NnjF0U/Jz3hpT\nRYie+D7A/+tzQBtq29TvMfLkmAO22uSAKD7HMq06rMUc8xZcPG8ieQq4pQMp\nJwC4j91QGw0/p9DjFNfm9gs21wzcQWrZr+XUxqjzP9SBubqNwlv295aCmqzQ\nkd39qAh0tw2oRE88YAMZX05KB1va57bCib1OkvKOKwUIX892CmXOTiqNFF67\n67AfOhCfS6638rwBexAKaiJMcXSSz+3g0ACuXRlkeelDcL16wlOsrracixNn\nwpjTyk63LNlXVpd0k3JtoY0W8Vwc7V+NNEoN8oe/vip/MgmWKFEcLn8SlhMp\nJTYDX66VLWpMIU8a3D7nxLzCqhXmDPvwHDFaLlDsy8L3RV/Z/X/oMByVIBe8\nmk4ygcWZMT7i5BObLSM25XjdqaGsvGjW9zz/kPfOT6QjnmhMn9hCnZGKXKK6\nQQxq6llllbAN+jUNfAjUwZmP0mgcZqoqTDRtBsZJEYFDOe/4Ov9Rz9S3iKHL\nIImmLPzV8r+a6yGYzVTZggR6cnh8eJIzmOQIxEI+k89eKbCYLIfuRp22tjbT\ntSSvQPBHEMbbwV5J7JXOvTF05QrXI9sOyuvaWfN6c2p7RgcXYl/sx4S+hL2V\nSBGK\r\n=CQ/c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-replace-supers": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0-rc.4_1535388411120_0.5224288556252414", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-object-super", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b8587d511309b3a0e96e9e38169908b3e392041e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-BfAiF1l18Xr1shy1NyyQgLiHDvh/S7APiEM5+0wxTsQ+e3fgXO+NA47u4PvppzH0meJS21y0gZHcjnvUAJj8tQ==", "signatures": [{"sig": "MEUCIBO5DnO2CgGYxvhQaEMPmsqXb3FDoTKHfLzNGdM/aRGFAiEAgM20oTqbwATooWuMMoENNU4rI6G9XWJz+A1WJXSmMvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDsCRA9TVsSAnZWagAAFgQP/RyB+kx8uAesaLBy2iGF\nrUibFHz/DPC/cr22Scoa4nNDVyN2iuCVxmWcJvO0hNdxHUvzPePCLveh/s19\n/jKPzX5QqdsP6u/1f3Z5q5VKJQ4vJJx0UvccXEw6ApaxqdCkEBkyIR8ixoGv\nic81RPXGEO24CUOfZBcr93URXenTIbY8YR8ioneDgYV0e2hq9mF+9tp9kUuP\nxT9vUANBqNP/iLBFPTyuFNBw3bFqtpOkX8iF0kPKvbSAH5yba18L40kZfFq3\nUkcLoCz5IzHWfr3Cl+9O6P0yvNioXLYOyH1WjCHFG2Rvc+g0gwgfSa6ouz0J\nBfz/IQyxC7/CDOQIy/AWk6nbLvkc23CKsIi9Rc7pZ94+goz+vhO57G10K4J9\n4kYGBm35ATMz+/3r735KDTE5Ugqp6rix64+i37ZoT0DqIMgB/ROYykw/hmgz\nGjxydVwZf5rJn4VcbuMMA0S6T5FJcQkEu07Xith0duSlqjWnGW/f2x1WzEQJ\nqk7HwRk7gCK3QNUq5n10M0rxIyhYFMo4YBlf7JHomx00dlKMxzgsAElrj3Cy\nrQsd6ySq1Y3CzmFFH+ohMdfL+4TYSndSdNDeZoCe8KZY/ct4qQ3uQjRj703R\nDaLzi0VCSqJS5aCioEZ4OIGq+Ay76+kqqKL0tU34vo9j7xlVF0VB5TZGUx+u\nJar6\r\n=EM5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.0.0_1535406315736_0.249914461627889", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/plugin-transform-object-super", "version": "7.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b1ae194a054b826d8d4ba7ca91486d4ada0f91bb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-/O02Je1CRTSk2SSJaq0xjwQ8hG4zhZGNjE8psTsSNPXyLRCODv7/PBozqT5AmQMzp7MI3ndvMhGdqp9c96tTEw==", "signatures": [{"sig": "MEUCIQCXWAwHqWnKUJXMhRcHzbD3KXwAgpCl1rAVtHwhZ80rOgIgIvpueMXBxKm7IOvDFahQ+dKIZ0AoyAYvjpG7n10G610=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAERCRA9TVsSAnZWagAA6CgP/jkHHwtvIL9bbEKZeBoG\nfOm0cE+WR+n/U4zQ6wxfJ1kL4VW2pCf5qT6uXZkFQdTHjcDlrAtjE7aoP3zO\nU01qwqlvjX6DBIkFAOC58d4anPKjsNGAsnGgHfalFi3ogVB6LjvAWFPg1RtD\nia8pRpLyqtHEmKOAeoaHQc/vidghRGJ5BY5HODfGGdSokm/8F55x/r6MQvSd\nysutYN5cf/Ww0fjH9qh4RVDyURJzUahu/ASIAqmR0LegiCuYSudoJmwYb5w1\ne6Oc7DNfsWsIczB+h/Q28OyIrop8OUaGUwKEwmjqYuHx+ARDuglCGtVT+3bF\nKLP4qM3Y6G3FEUTD/Q2aIZgBMrlnmblqGPTx8bGXDmeCFs0Eyd5atgKLHs93\nWNLNdqX8vvOLO/i7fVpRYBTwkVijOnIgp7KLXa0xEmqdUr+zqW8sEaHmi2ky\nhRWrHIm1bRNzXbp59AukFNeItKwhEFuD9vQrzEP5e5xR6Kev1JQWgBc8wyDF\nJLytk05LCiPf0uhVbU1RITZdJW9o76H3OBoGRAi0sqWhHOpBWAyIYbepp6gZ\nA1F2gJkIeS+8UiWlVvp1Crsccs5sd0XZiAzz/jSvS+EKOZ/Sw9M30J9V12LJ\nKYASNymcWcLAL+CFV7BoJ8HN6QAeI2HjZdi3rIYhI5lWAuKG+pOW1y3UpJ+n\nnAfo\r\n=zp66\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.1.0_1537212688650_0.08830880913238004", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-object-super", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b35d4c10f56bab5d650047dad0f1d8e8814b6598", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-VMyhPYZISFZAqAPVkiYb7dUe2AsVi2/wCT5+wZdsNO31FojQJa9ns40hzZ6U9f50Jlq4w6qwzdBB2uwqZ00ebg==", "signatures": [{"sig": "MEUCIQDUADPIrxQlUFVjf9rUNVQ00y9IOUoM6fwj/gaGdrAz7QIgdNnvnmBPLV3UmQUGqIrWwn73h1jZrca9JpxRDZFoHKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2PCRA9TVsSAnZWagAAfOAP/iZXwO3x8lGA7UsU+keU\nQ6awrl8oQ13tfvfCqSsikrtCAuHAutAAVkEHf9MBszB3b2GIOdbyN3sz8fmB\nh5Nf6fATufMCKqwWHIBZEg+L36NmG6NYK2ugbKWOGx0gZxZw7aQgyE905l5P\nLLZxjnC3HwRkxIyI48pcopkFMl1vguNWEI0MqiIiARQYrr55E5M6MOPFCsis\nqD7KwQNER5f5YYFQCoRC70w086NOErvy818I0KBYJ7qy+xrvFw1xdp2RBONm\nRnYjTzYtv8xdSIN5UgYI/QEa2LNU3ZTPLo1lcsapKYzppAC4vKHf3TS3vNEu\nMshhd7Wg7N5/ntEQsN7fcqqXoNVHEFAsc4SHNUYcFoaH4iIeIt6XEfJv3viC\nsSFEiaiUp8Sg4vPV1nclrCuFe557v34uMp/gzZLi85ca5tPgZnK4WX526Jqe\nzm7dRXg2hTbiwt/Lub1SrSORrJXEFwf6ZQcjJiMSu41czLy1Qo7qaLxYM2g0\nAPiwC5eeQ8lAwEs2RGDKlrRtdwcvmMc/tbW6ZCn07wk9aZqFkZB3UiK/N5Qr\nApYWqsXGcJnNu6nQLXZW61pqqpEn8Hju9hUgXLAPsso6IrJr3xR/Gd8n4DW4\n3B442Le6iUUHBb+jsGKlNBYedbKrVjbqe/5caLo2zW6hmImW3XfXHV3rbSRP\nMGvq\r\n=tVMz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.2.0_1543863695035_0.8450878763742469", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/plugin-transform-object-super", "version": "7.5.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c70021df834073c65eb613b8679cc4a381d1a9f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.5.5.tgz", "fileCount": 4, "integrity": "sha512-un1zJQAhSosGFBduPgN/YFNvWVpRuHKU7IHBglLoLZsGmruJPOo6pbInneflUdmq7YvSVqhpPs5zdBvLnteltQ==", "signatures": [{"sig": "MEYCIQCx3fhc9l9D2x0qlBGi4ndtr5AW/YRtGhWOlg6mP/QUjgIhAMvhd47ZHkK99LdPu9hyiM9pDL3pYEw6cQIxe9OgygFs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FxCRA9TVsSAnZWagAA3UEQAJ8UKvc4EyP3otQIswnU\ny/DB8lRxNNvr2hTui2wdS0QCptndUSEjqAA5ndIFDqnW78WzHpunf+odu28J\nmEMlLklXI2dbr13N1rVDGlUlAdQCrSTfHU/+RMeL7LM2fkC0dhHZiefZyfrE\nTZCqYYcZnReFji0NRt4+AltxPxJvlqGxaOs4ofJanUBFggcuhw70zXQSmwOz\n4FT6pv6ZFXzviSAVwvGI3/ubajmNgYAmamrUZqPaDfvcfitIwMDPiIiCjETI\nJ5nc+a5uA4VK/57FGsZmoOmi5N56yw13KLFBsAhgb453wLHioITue4W88vV2\nsqIFkX5Y56Q4rv16yTW3jb9euDtYrEPmpdftSARdNY/hMEKJDEHzAmY/ift2\nc1ARfc1KH9ZBBYgcVTAoQfOVAo2FOnSXhx7K3A7zeGWwcKy3VWXO89/kdIF9\nzRX4IJDLXylnxh1zp25bbEcd5alee9myC0U4/qakaC13u01iVkYx8uXO/5Zc\nnNRqYgxewNWMz/OCU1/dYJRZY0/RkD2ZO0DZdqrz+2YwT6WP2aF6gkwlrXfL\nDt9PxX3ZMLGBcXeA8hVvapv+sMQyOV0kauqeJ+TUnLzqk/besQzXHJIgWt91\njNxugdgK9JAeoyF09TMCL7IP4BXMwz1gsnC0NSUU7n5huVuzWNLnouWS2svY\np5+z\r\n=q/Ra\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.5.5_1563398513017_0.023871425464892182", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-object-super", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "48488937a2d586c0148451bf51af9d7dda567262", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-ho+dAEhC2aRnff2JCA0SAK7V2R62zJd/7dmtoe7MHcso4C2mS+vZjn1Pb1pCVZvJs1mgsvv5+7sT+m3Bysb6eg==", "signatures": [{"sig": "MEYCIQDTTLVGP8VrkoVHYjayU0UpkWATZguXsKpSmD6v8cg0XQIhALYF2rvbMPXO5VcfK33JC0cxMOTLw/zDtXeMAQvcfo9z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBjCRA9TVsSAnZWagAAJv0QAKDFFyR7XczaOCU6SWxG\nf9oiTGceP/DrLcjrhmrllfVbKruf60UTOFAfcUTVWm5sSLet1TCcjxKoRUwx\nScFgnKmBdVh5BavDeRUN20omKm9uN8HPF16tew7O6jU4wBPdtpbtbv5mkTim\ny3D+1MDgX3gZhQOMg4dyF/Smeb9m/KxTYpKsxZV2rWU33C6g+hBnnKW5Y9oN\no3pckhyoZ9y3k/yNhZYXuT2rwSVHGICo8kitskms3pupogIVeG4xP2ujua8Y\nMBatw23Wk6WxTy6N/X3bT40h9pgzJZ/d4hBa/D+ILLMWkjBPZojz4+PWy3j5\n9qmgsvS7TjEsSBDSEDuq1g1qhHiSgKaGvG1N2mAFOx+dfW648qBWoqeqm+Jv\nGrDoMyb7vg/5kOqBivyGznqgbISNXc+p9c0rN34IgN8fSGzDQ12e1n/abaNP\nDJmXgg1YIpET/QhhPtTLuyIJ4xyyNEWZ5PN3/Ctm7B5L37NxW4ue2lzDsbuk\ndHrPGBulbYUHW0t230lR3sHQYknNByVI3sSQhz3Auo1ckbft7SELUo47kRz6\npnmTqCGThP6pAshwpMlM88NC++3zLtRmQd1G/GgpjDjzH/AcUqjrl99V/BpW\nTftaChIfex5jRbJ1Z0X4qeUSVtAyeWaD6jjjLlXB3hvD3G373y/X36H1LVWk\nGaXf\r\n=CoVG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.7.4_1574465634927_0.15281784942880283", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-object-super", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "aa35d295dd62b84bbea2e155e0b3a2017eb2f4e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-2DYqQ811nRlFVlni6iqfxBVVGqkBgfvEv/lcvmdNu2CaG+EA7zSP1hqYUsqamR+uCdDbsrV7uY6/0rkXbJo5YQ==", "signatures": [{"sig": "MEYCIQC2D9zh4yeM+vsAnEgb4x6v90i7+9pfl4iiJtCB8EAmjgIhAMYYzicWALQHNMtmxsfOHUxAquWGnQbx0xPO1wEI1NRW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWiCRA9TVsSAnZWagAA84AP/3A3IIsEwLYmW3F8u57D\nmPXPAcSTDd/EKkSh3zf8EhzUlUQbEwvRHfgD14BYVR6tmR94cnNZv1VaStlA\nrayomJ4KiEqzgxBs3fo5cGdL1zMGlQpTYyTzEOcI4S7IflQuIIzJQL3ysjN4\nt/fbv0Swrcy2L46iX692MZygeM0xFDzRZeuCORMj7qV/B5m9NsElTkpAyFV2\njAiSgkEOd/f7Z53ZLoniJlnCq2qpbXqqxnxbXL4Cpv8IY9pQujTNSbRqgtln\nlxKxybuPOpkrojzO5ifIJftoOc57ohXIDYlRn/P9otJE+ngA6OCjGHqxnzGr\nJNEz/R08nyGg7tgjiElQmHe3Wie4yHnSI7Wqg4MyFD7WVOktB1+PvXkwgqaP\nMcYB9STUVUmz86okPMXfW6TomkafFeN6py+r6NdFnMOvk5ZfQYMJneO3vl1K\nepAdAmgm6zh14hXOuHj1WU8PZOKpXjHWWAVphHIyjI8WMmIHDb8Az0z5e+Md\nO8817BYpdvJ5XSy2AyP8K2u4vJAdUe9/JQiGqRs0G9V5HHdMhWCV3YDJ59Ii\nemJvKcWFlB7MkMvnMj5UaVUU1ykljmdyI6xVk8oFlAW48+E4nk4GJ/kGUHxR\nr/HKg76DJq3XH5uZvWM3/+FSY54elcyu9Wu5w2z/v5KPXILYWg+HYkRdQmyh\nikD8\r\n=ELIh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-replace-supers": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.8.0_1578788252942_0.18585491405078192", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-object-super", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "ebb6a1e7a86ffa96858bd6ac0102d65944261725", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-57FXk+gItG/GejofIyLIgBKTas4+pEU47IXKDBWFTxdPd7F80H8zybyAY7UoblVfBhBGs2EKM+bJUu2+iUYPDQ==", "signatures": [{"sig": "MEUCIH0hwqT3PL9CNluPAqClMzudjGPxGtbz0cl5PtMT2xBHAiEAq2jckVjtP5LbuDAB0hetjEAOUNfuO1RDp/dn3koarhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORGCRA9TVsSAnZWagAAK/8P/2lt2tr4L+hAOaPzw6JY\nT1Y9JieIjVGTohUsyV8S5DKatIil+lU7tde70WEdkOxflasgdLCfWbJ1+Wnf\n6WVvAFgcbaZn3GQe7cKGpKiQA7EodsUB2HrQkOuzLRqsv81MTCWMjA3uD/mn\nXfwAbQ2XNJtWTriefO0tm2PUI5yLbfWs/mYA6eViV0SZUsMsZDQwknM5bqvu\npbBTe3MxVldmyHtZy8ikH0Mtg62HD2rLEx9AvrYibghdA7Z4u45iZvN2ID3s\nwKDYDl77aI5O9IajBSRoD7fb8UHt5qYQH7oGlnaqk16Co+nVhVZL8ESFB4P3\nqD/9MA/NNvU1q4s5tyj3xFB3GIrVSaUvhWwpnSTsEMHnzrNhpp2Wutc81SL9\nRieic+SRjzZYcrug95Qk9SdAR9sJ3nT7n4UHLfEsPSQ8del9pJnOuZQt5s27\nLTcRybCQHPGvLd2RdMeW2Old8RdquMCK46wvzQr+CaHwRl5XpHSHqWtP28GR\nUG+Kuy3HUXMymgWE0K25q+pqB9JIyiGshRb1fgiMSe+8/8nOSwosr6lfhJw5\n6WpJYBsWJ6Y7zjZmpxemmll8An/hC30svOKHvUVqLDEQj8jC3ObuYYe6ewf9\nY5zecyfCf+gQf2M8+RDJO9cWA6h1X+v6EORkwcurDMiDOxbgUn7UtTcOUaX+\n0i0d\r\n=K8Pr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-object-super", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-replace-supers": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.8.3_1578951749762_0.40991806065959", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-object-super", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2e3016b0adbf262983bf0d5121d676a5ed9c4fde", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-WnnStUDN5GL+wGQrJylrnnVlFhFmeArINIR9gjhSeYyvroGhBrSAXYg/RHsnfzmsa+onJrTJrEClPzgNmmQ4Gw==", "signatures": [{"sig": "MEUCIAjU+ou+qMce/cfwzrMI0GGYMozMdSAMvPhVlhTdOVaYAiEA40MRdbDFE6sNUChha069hxtctrnJ/jbRHw61fVHeS3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTWCRA9TVsSAnZWagAAwo0QAKHMDGE5VhzpR6g0rVi9\ngeA/4eYRrRCO8sm5YiihwcQjokrgdWBKCM5bpAiG0ZipXbOg5SftWNo9pYus\nAdeVUKU0hF2aVn9TQo5UOylFQWPtRopikkXACu4atDy2igrHIe8mW3O17dDb\n60GE6qp4dxji3gYazDagqB8aH676St1VyvwhMTV5qjyVBY9gZMBGrGk5BEGO\nTL8+QqQpWO//8zNS+bkvy23eax6fuCt7tA/lwP0ehNYd9IQKemVfy2Hideri\nY9MO/wF6y0S5SQHu4lmtOsUDXs1O81NFEKpTRRm3J+dtj70k+iBAXtiz9pvo\nug1qBbTc7R7oaAPiVkVXjs2AFgDcU836PNQBDKlVmq2mWtY+vNk2qMnU3aUD\nMuRVpP6A7IEzkt6xszUfku3+PGu1URx+X3Z+zbvJgX9XB0xhi9Ah/Y+Kd853\n87ZSF0H3Ius0B5kRZ66YIO4PA4Abzz3Af0EOmqqb7rk/iOJJKiDpJTbSd/AR\n2R0vfB88XOuX2GLTpV6E9D0YV3CIk2SytWt3aGP/b8quDR0g6aJLdKbozzjH\ntg7sEaKplXkcHiaeeK8qBpR8FfkoRAYq3C025flILWPgQ+HZqVkfrUeoRC8i\nXmC1VD4dk5S/o80l0+zTxH+u+JR6CErLtGTG5eZIctNi4ssgZutKfleXfW+w\no95x\r\n=5HcR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-replace-supers": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.10.1_1590617302233_0.5192335229626788", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-object-super", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d7146c4d139433e7a6526f888c667e314a093894", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-5iTw0JkdRdJvr7sY0vHqTpnruUpTea32JHmq/atIWqsnNussbRzjEDyWep8UNztt1B5IusBYg8Irb0bLbiEBCQ==", "signatures": [{"sig": "MEUCIQD9EWA6rGPxew0p/zn3gHsKE+I866k9TAuPhEA+v5IStAIgD0o52hzuWqDgAKUZHHK270nAUP76B87eRBWPln4jQHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp0CRA9TVsSAnZWagAAxVEP/2caJAnRLeUpC7Qzx5Q5\n1f9CQ4o5COVpDd4MrypTj/pK7o9PIXP2R4J8AGCPE3jC98TrzibYp+RJZ/gA\nZ3Mc+/17R0bWTOxgzrwJvxTV9XZUhue29LGv51pd6dpzjrFi+KYA3sujiGkM\nMZDncLUEiY36kbKT1SX7rgcqDRr4lFXJyTP2C1J6wMz8dk549StRHecb/Nhz\niDczstLXLWqvROBt8UwFdci4l5I1FmK2oedXrqlSk2DNlWmTWCBcJYDIJlHG\nyBDK/4hbN0KBIqZueyN6+C2pna/xJXH8n4N0RsMYf2OHyeLgF/W/hyiY7/MX\nxv4fjVr2Idc8AOX4DZdBEYxLxlXh5Q8hkKqScjFCSM9spkD9STnzdMRkOc7c\nFxwAn1g9pnr58WAN6qqzhnIGtHECNRI15gt6OKutaz41061hydgK835i1SXw\nOqVJIks+e43HRPsiJejiPxjo75m5HgDI+7USp8qzOrTKsyAvqvryyEI5DjM4\n2mKaenWZAxxRGmt80LPy+182dFBigOAcnutpWVpslEQ69koATmobusjRX7QZ\ngAlh/Yp3nZCnW9M8MewgRw3TjjNUMkUP0y5YxMCuOrl3R2L+C1WfCaWU0p1W\nKdBTsqK6rahe2X6z/dAWa8SlHXLpxDSUhEY35aqVWQNQVeQAYTfPmdbn3hW4\nUGBp\r\n=UtR2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile ES2015 object super to ES5", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.10.4_1593522804196_0.49898723682287827", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-object-super", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "4ea08696b8d2e65841d0c7706482b048bed1066e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-AvypiGJH9hsquNUn+RXVcBdeE3KHPZexWRdimhuV59cSoOt5kFBmqlByorAeUlGG2CJWd0U+4ZtNKga/TB0cAw==", "signatures": [{"sig": "MEYCIQDWnfiummxTiDEOYDP8yALiKquvABNrfakgm5kYYQoYzQIhANaMfRDEBzCA4NeIBcE94oP5zNECpV6iJW6ZSZ3vIOcc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAyCRA9TVsSAnZWagAANU8P/39lnFkxXB1mQgEVsPfb\ngRVvmlKarKTmD3zNOFdk2CFzdR3OMKxb597ET/dLj8uArosWVCQcG6F5OBOd\nN6vRQPiofIDkzB/SHN4nU7lWN4u71GhCu35Ne+12W9j0xyTnk7OL+XectI5t\nLz3zP2mcv56XRRjjWalMUUVfep1lZDpYbxu4iLJMW6jSXmyHu9gfV6WOw7M6\ng51jzLBo46Vz175R9ZKzuj+wt5b+/vpnVIUjG9lDgErk3ewirq1mRdjXjBbG\ni34TIO1kxKSwmhEncJBYK4U1FyV9t20o4SQx0pUEEvLFPDkVcBqqEh32e1wU\nJPF13B6QxUXB5hltL0XLk5TA1zO7Rqt4L6bu0OkWD5qnk6oLHMd4NvmeP+iH\nnYolOQANzVT3aO4Rriw7rkoJEPTEbv7NM7crk4fFB8Dxa8LOob+F7s698iz8\ntOdvr3AfSEVEAf1UblM9iryPmiFpcAPF770P/YCfe8MOI5WM48Pa4dToNmpW\nSQttTZrYXEKluZJ/xEr4zkY9ttxCpUpnWvH7azFMNZKBWMBWBWSrQVA2i0yW\n0mj/4eYAv7cFStQOyTpDrTX13FeVznP/Gy/aFFXunaXBHq1iVIseQdnAJFEe\nuLb5WK+OFMlbG+Mctq1/kjRg8ensWcupH1A7+aRUGOipU43iS2kZLxFY5eSp\nIc0Q\r\n=7Kyo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-replace-supers": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.12.1_1602801713955_0.7084019091051414", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-object-super", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "b4416a2d63b8f7be314f3d349bd55a9c1b5171f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-JzYIcj3XtYspZDV8j9ulnoMPZZnF/Cj0LUxPOjR89BdBVx+zYJI9MdMIlUZjbXDX+6YVeS6I3e8op+qQ3BYBoQ==", "signatures": [{"sig": "MEQCIFOq3LcCHyxqep3nQDKcYN2SnOLCGlOLAwKMLCwoQ2WQAiBoqbM9MxWGzNR1wyInx6vDvoZJ6dYaWdD6D3xuZN3E0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhkCRA9TVsSAnZWagAA3m4P/22axZnPNIUmp7RBCNiL\ndyLVUAm7nFTVG2saTmNnKSlZCOLinhJyHwvJvZzVxIHocv0M42uAIJisjrKQ\noq9wke0ESVGMT0U8XdSkJQxfPP7fDNA85FaFzOWEQhl84Mludz07j+CoZDUY\nVAVCEzyNsx3B9Zho21XkHxKn21LUXh0DVzYD7I+ZoO6Kt/893g5Vinl0ENg1\noFiG9sFMNXkbeJRaKi/TyAVnKugQ3RdJnwcl+0LSSBsyQu8NgV//e7Imora9\nPvKqMHaved/fL6Ypaqf82mf3cXacexBf/MY7TT54XFLJ1ENfQN5AgHCbWz0Y\nVj30jaS5r7Esf1UpTlm++apziwdoY5s1iARVp8fN5o+0r8Ftg0gZM2YIpNr/\n7ioO4q5ZAi/Hr6uPbxr85TLrQnafp3DzdSbtQ9XZPg0k55EwTwhrMFt23pYR\nr0jg92p+NTnJCyHecjk47asA+rxal1E8zm0AfQ8X8JNTfONwp9rWZSNNBrWK\n7VuOHlBGpdErjBdhvV5Je2/K2nFNLa/0S/Zm7u6KoShTNhIjEBtnF5XqRVhK\nA4WY0dn9g21puadEGPJfNbx2+4MhpmQoJ3ztGSf/3hg4gngjBQSuKCAUlYts\nAGylvBiv198KUgZlD9bHlMplBc27kdP7sboiLuGDK/ryhTohoh/NZLAbEpwE\nS14n\r\n=pkZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.12.13_1612314723606_0.184976920947012", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-object-super", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "d0b5faeac9e98597a161a9cf78c527ed934cdc45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-MKfOBWzK0pZIrav9z/hkRqIk/2bTv9qvxHzPQc12RcVkMOzpIKnFCNYJip00ssKWYkd8Sf5g0Wr7pqJ+cmtuFg==", "signatures": [{"sig": "MEUCIQCacU9Rtt1cEmeCUGxEWlExRhJlTwu5UjMCbEWUMA6HyAIgcdf7gTvGNknm4RIbWrYkVczAfxuQGeW2DeJUXl6jliw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsICRA9TVsSAnZWagAAzcAQAJ9P26tYHDgEWaDc8Glx\nJ3ek63eO6C8ywA8AjcpIbKOqeDuYCRt/S0Ca5dlANyDMhwsGnocq2m96OVkk\ne66mam/zkQBwaht0wzQhW6uaZ8B3sDZLskqpaeTnNMkAM9bOXqA38EzN1EC6\nzkym7IhqOGHHfbPH5pDaIWhdZa9Wbn6OvpnIHBZOOKF+SFwTdwas5BpQi1zk\nSYvNKVFOGeKxX1wSMoPM4YumeUHGlIiZBn4OP7b2Ox0gqoUrpuXI83Ohyunb\nv1T4vpfEr/OAKqiW6QUjKlD+NiJ9tthzzE+8KCuQqK38rKvrorFvV43Q7MiX\nb2lJWWdrE7AjEGu/KXEzkFLBoeFiX66Zh7dohiXRtpOq0sRF5NzhfCxqJcjq\nkDAknGEeT6mlTtui/qpv5YQH07umcQi6tAR8tSZ18LFJB7x7M1Ux+OpBrhrG\nxILGhOTMKMHeGsB40QxczEiZzNtO1s1DI/jur6RV4nyW9qW1e+EgWHhVo2rm\nYJ6S3jNRZLIP040RlxswN7neCmsxi04LOPEQ0uFM3awV3TAqGCJtGcgDFIOE\nW5BTceiA30dztpQ8ZkXH5oz2IxfaRagKEdy/VJy/J4z0n4TZq6bwyOk/43aP\nsv7O0h/w1H6YmnvnDmElHWuvkk0DB3E2qBf+/cJJg0ePr4DuDp0g+q9cUMb6\nDF3b\r\n=7ykk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.14.5_1623280392537_0.4774655764576161", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-object-super", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "fb20d5806dc6491a06296ac14ea8e8d6fedda72b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-fds+puedQHn4cPLshoHcR1DTMN0q1V9ou0mUjm8whx9pGcNvDrVVrgw+KJzzCaiTdaYhldtrUps8DWVMgrSEyg==", "signatures": [{"sig": "MEYCIQCD+66HSMehK4QY/RFNUHUtvrCyv+NwCjxmZAKzXxWNtAIhAJ54UMg+Cg45Z36ci97K5VcQJ4URaEr6sGQMFHiIbqNe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3654}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.16.0_1635551276764_0.3879988220796957", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-object-super", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "8ccd9a1bcd3e7732ff8aa1702d067d8cd70ce380", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-tded+yZEXuxt9Jdtkc1RraW1zMF/GalVxaVVxh41IYwirdRgyAxxxCKZ9XB7LxZqmsjfjALxupNE1MIz9KH+Zg==", "signatures": [{"sig": "MEUCIEls6WY4N422BcZOVTR5KWj/Dn3c6P2w+2dQZzrVu/aVAiEAyfxXDPHVJML0WN5N561z3DqG06dKiECVws9mcDIoPGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8knCRA9TVsSAnZWagAA3x4P/RcAmY4HkEtYhX1ZjJqS\nVNGTLIcSzltHEr4Ujv8Jlgq7eWtSfhRxfhBXYpZiwYFTFXhPqIP/1txjibe7\nvd9yYscOY3FUmer4ynvaa0Fzf4x8xeBaGZGDIAbcPh8izCvOTUmxDk6hXRxX\no/lPQCk5OQhB6pWzFJA36VMSMbLAH24YvFIbrx7jGQkeelFb36w3rz35SUQa\n+6EdoHWgXQSeNN/Ay25vEXA8mh+J7hnUejNZaFs0/4OdDPXaTzWfB2ioSsNy\nnflDzeipCneHuxLN5on17woD+dR2EnzlMXXWudOIFq2mnDe9yhvmhElQ/x2H\nqCYpFRlf5mWXeEdBraox1mKlIdh1kVkzAIxOVMOkfjPh97k8sFIQDQ7HRiXU\nVVAdFM97MTHrO1j5YDKhBDRD+y8gzG4/c6aqbh363ulvK2zmpxO+kXk2Cn0O\nYLEXle0JIaxDHNfTuLOiYyaWBB4UxToM6N71GDf8w3oiVdSUK8YIFygGcMne\nfRp7Qv95h0oULjlTEvnKMexypohms7KbqoE4/OGfDoQm1ISPrGsvrcBdfWc3\neUQeQXa7NYb8rP0mgVXNCbEEbS2YWmeNblfVcEMHntD8FVyC34w02Oq7l8C7\nbRuq5oAE2TeLeUScXacqD5KNpNdlFxiHBk4UAHyt87N2J6PayavHuCfnjCBG\n77og\r\n=y01y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-replace-supers": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.16.5_1639434535119_0.9853373553363365", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-object-super", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "ac359cf8d32cf4354d27a46867999490b6c32a94", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw==", "signatures": [{"sig": "MEUCIFeJvvn4mjRPm8JRYQAv6ufnyNbW6HgbKSHczAd83olmAiEAqbVO7q96pOWIONz+fqRVuUCJlPfECOUwx8K/WXqEyaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1sCRA9TVsSAnZWagAAg7gP/jMP7phaFjxyiRgRtbSS\nEl8sxciPdc7huGp8yZ+7wbQJ21Y9SBcXnf+xd6vrAPt2k7zJL4fde55zGDfp\nRd1IRveounGepU+b3BJXQrk9CeXM/yxx5lIscEcBCVR01FJtOa5Czmy1718H\nmbBtjap7RnCgd/uJ2vRFOl/Uoy6Stcm2PFolB4H6d2qgRzBj8y65Xt8KIku0\nZyMoAUWit4z2cQcY+nJXQS96HKzRxq/ggwCjnsV6tTizHGNQxxvl2Xa26EFK\n57byS8ymymC/9G6jZ/g77rcGYCE+eXkHvXXfhMLLtHmTCmyEyIkeQZ0ATXtr\nrGMXVLav+C7a2qrgm0DmeM4l/5vARAG10A3WV7K7Nwn3Vm5jTzkNvTxxpzAh\nZXaf0UyuW/hVy2ygGJAx5eNQIyLetWKsl+pHepkhp/il/If5GqvnJ+98rgHs\nryDogY6SwyZ/SUG9Bx1V/FDmzLDH+ANomV5i6js/Ji9vuWG/kA4fTqGxja8P\nmIo5pzOo5JKb7hNgFrVkCBPVafvMsZbxRTnEO6tTw7K7eX3e0+16iFBLPkO2\nAu4DYx7Uf1BI3/GTECthkEPcn33M1GSh0RTYY2Au/62U4P/5Ku7nq5C8vM4Z\n1un0Og6RYpXTI71c+eABwwytJGZJ/nU+7kNQp2pyiuPn63zJcqjHboSP1yeg\nblvF\r\n=8dA0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-replace-supers": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.16.7_1640910188494_0.7713940204786374", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-object-super", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "fb3c6ccdd15939b6ff7939944b51971ddc35912c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==", "signatures": [{"sig": "MEYCIQCloPV+qYsUd3cYig4RcFuiIcZn9y5GiEr6C7VA0xRsyQIhANc+REqScupj2NOiXzUjPPKf/v2iK2QVFxTsMJOXYzOq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEEg//TTVxLnmKYgt99HnjVcIvQhrDHfJbu2z+MkeRnAy8ICxaerkn\r\nhkVOcCxlI9Re99mqsSGFRP+gpMtCFF3n7qLVaCE9OyUvLSY95X34Rac6SZe2\r\ne7pnPzEOBXM5yBUEOK/58v/SaOVQdMdGu7HqMcKVMe2DvDMiH0KetXxW8bq8\r\noSEOPyI0nRiqzw4Ia9Hsld+dzxP25HA2/VPnhX3PuT39JGFCA1KMEvJF9gDL\r\nt4LlCSg1I7Xxwy9Tm/z0o4hnNVFW9EwlvK6Hq6lyiuWaiadIwVe5WOND//yj\r\nMG3VWAYw2PlshggFqgutnPk31DUDYmCQR5xIC4n7MjsADB7D6fOS4aG+PTfy\r\nlKtSgEPWp+bR629/0C0wc8EfsGVTJhP9yuz6DfLL2ReeBrbo4w+YqS/VZhVx\r\nTNRTxsUMujRdR9m88eYh/st4IcKIE2LkPK2PaiP1nSDYd6ZziC2i0XlTrU1b\r\n7PG3ONPKS8Xqv0zHKCbgCL/No0JKK+D4VltoAuilJuWhRIgyb2ZzRDN3V3tU\r\nMUqO1SlcFSNIoexpcp2UCHdVJRW2cAik92kay7d/KfKl6CSrXdSTfW71G0qz\r\n72Fz/emeGKvU1z5r/LfgrNzs3tjbQyequ6zkkaj3vRTws0zKZW75k8QCEBAi\r\n7PTKV24l8LaY6eut62zJRCWtKKEJOgGugW0=\r\n=7hqd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.18.6_1656359441884_0.9324936658192957", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-object-super", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "5aad8d3c51623ef62259586132e1d62fb41b8c19", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-IOHBLdV3+rnB316K3uGRt8UXHFaJkeUZMt6jJM3kLeW7AOMsT8MUQKfW9nCca2Kdy4FSK13Hfm5q+cw5mbxPfQ==", "signatures": [{"sig": "MEUCIEa0qZQKh+F77nv/iI1sdvKUReSfkkyrfcgR4+ppyjmOAiEAqR+uD9MK96GR7gUL+misgUbtDBdhv5GNgcAg51vFPoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkHQ/8DLbO5ibS7BbdjqeSgYduWsVzstJJukNkWt42Pj8IQh4acH8D\r\n/wrvrp0gH03NAD0LDcea8fFwkJCAOeWRtU3+6U4HJ4iH8UvGDlhD7wQq058X\r\nA3fQd3gyKv9iHSpBfKY9w3/WvD2VsV01JYbp9eKko73jm28h3yau54c260s+\r\nA67FbKzHIDIzUHXkBPaxo2tuqezvl/d1Bp1+GIt4ybhBlTOP+38W3xdDkitX\r\n1eIUeC8LwJoFdJKeWbeuOFfTVVPR6t7438l6ohv7zgZe2fjw3fPMVF1v13Ag\r\nlll89mldGIm8Bd+GLArbBcQUr35q0fr6hhwG9JcOI+C29AH+HfHGSYTyobyW\r\nVMcwy9P+pgYWppk/muUONFXJR+VPtuwE+yRz/XbjYy5fFoHi9dQqwsyHOm2q\r\nxDdi6BUPMJsyIIA3JQNC0Z4f4EnaHc5GR8n0l2Ul6igFolRHNQf1teoApqD2\r\n/2nICX0wIBkwcF6XEpLlSuiYLVhvbTvWoDzysUmq38ehtEap/9BvZAm0rgF2\r\nohBx+VWAM6GT+1HiU7WrwCGvMzVvh7ewysOJozpHQdrtylYD/bnfcMQQgk8T\r\nheyLZ0KpukeDtguIMKfGitI9z5A88JRPoOjIvWOc4CXTyC00ZxBTYZRwyaDW\r\nsQxcUZJylwbgNkU0OcWgVcsWv9q2Soxe9wA=\r\n=SmF8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-replace-supers": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.21.4-esm_1680617398404_0.26194116877025575", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-object-super", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "7e11e0c9a0bc96ad81249552970870cc1df313b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-AFGS2pvBbCdfRyQ0J5y1psL57SWBzinC5bcJD5KtUgLKzwrfRiULczeUBkjk5KeddXJmi14dXjBoJ5o6dcbP0Q==", "signatures": [{"sig": "MEQCIHuTdGNkXRELVp/sVNZudsrsahuYbWtZwABdnEs68PruAiBboaVvr8C7RBEaRk+Na5sBZrqjhvP3OxNY+93lQ8dkgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq37hAAk60l8kfwT84UxI2INUUtOhfihB2EArNeZWLXLF4p3furvZ1Q\r\n9xxXaujTWK0MsJHsXs5w0mIDGJ5xEmhZGFsI+gO2ftENE9AGpHC9ot26Anb9\r\n2zAGgLIllvLkd9B5ydpV6W6R5DjNOhgoxl8yGx/vjdSTnIsNwNBI3WE8X8pB\r\nzJWPK86zAbVTNCCHAg6DDUMJQ8bqp45/jNB2Tt00soINJVrtSa9D8iIB6AYd\r\nO4M2YfS2SWC7QjJ0e8qnBhRr+DaYNQsRFDIFyA4zF8uZqpiVjpHhVP49VfZ0\r\nRKhyf566oQzqblUluPG7PQCoartyaJ1rPoA0+xxcKHDeCTkErvLfKEyebsnZ\r\nPQ7BMmoriGuRy3pUUVYskhOk5n5omSWjdcdJkA85/r9iJuOAGlNNhN+BIUeA\r\nSlXHhzXAVPSzMQMPF1MAOoZGUuXQsYkXNDzQ5CXZie7QLQUyC6+hHtwWSgli\r\nlLXwNAQL2bhUJBsxTnGzZgf/VXkOAJemDoxmbMBLAR+ugqJ2I2Fb5+7+4jYG\r\nXSQOLQ1oDSg6rq1mf4W5dnhbH/qEpYXPGw5KfCdGGG/rwju+7FqweqsVt1Vl\r\nnIt0gltWLJl/e+H9w4hF+n55Qys7cDv8AMloyhUIp4Y29fE7EfqLgUncj/oo\r\nW/nC+30Py22YqGfOFnowgonYWD6i4moFTko=\r\n=HT7P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-replace-supers": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.21.4-esm.1_1680618116084_0.8004807518636401", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-object-super", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "e77839b043ee7a84468d7ba2bc7b9afd2a03969c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-r4wIkduAjuS/cuywxqOVfX8FydfSpDVGoFDlAW1c7k/S49p3tl9fVDD+JiHpekUxIjFyIxBY8sP7yc8nw9/m6w==", "signatures": [{"sig": "MEUCIFH8JvbbaAFAXHJaNNKK03aMm7OlQSdO/Z/Vlk9WjtJJAiEAhB4nFCQlOQ84BojpSCcSWBdI6qMkyLvMQbxCAv9fb1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZrw//VUN1U+sH8JNh9m2q2SPpjxKHiWJ+G59roiHb4FZHGI4tI1E2\r\nYfaT5gcTc3tmFdv4/ro9lKAx0E7PDvsnEqKW8C6XzV9BACLqhUIyZLu5soRw\r\nQXxjUSONCeRcofe6YSFuUGODW6daXIdOr6iM8gZQQJwmhY6R8W0jy314RHrR\r\n8Y5+xEBdHj6STJHjiyXYb/SbTJoKzpw9WLjwzz/At3KkORnDWZegrFMBUmcO\r\n1uRAR0oe1Oj2w/3mSgeoqmqLsR6DGAfbHAGXE3PwM8sVVVEJzzKHJxzjnZ63\r\nUjgdJoGXekzlwLFKo5i+qDGF/Kr/QzO8rfokZ3H9eLMP1EZNj8CCHbIHfVtF\r\nifZ5UmXoqy+u8X5MjXjq+gySX/Q3OR8sB9NyiFoQrPQ39NAozzNS50Xted+q\r\nCDwEe+w3P7I1IgGWfGktjXNRVLTHD/ogdQBKbYebUvqZDt00OeNTkGcOHrz9\r\no0+dfN0E+ewqHUBp7MqEcEkTVtZRpnH4t3l5EQuMsCujusy+QQ28sZFYdAnK\r\ngsWSr9s12SQMWG4KuVV3H+93lR0VMC0VMimbEJuseR8wJiJo3eRYVAaFZi9s\r\n7GKYzGIDdT04oFDe0Lj7v4lmP8BB+iZ0k36fJWXyEm4USb3ujw/mXoECqlDe\r\nvTmEodYJ3c082j1Dv7gW/5ul3Imlj8D9FAw=\r\n=Vyw6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-replace-supers": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.21.4-esm.2_1680619201561_0.5317733676115362", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-object-super", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "701bc72ebfcc4676f79912ef3026d03ef241786d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-WA19l8ve5hTb+/TdEnET5NTj45i+xc4WqWvilEBa2ujbRSJPbmp45Lz4l0xIDhnnpmSivcDgQOdCmjkbK/FiMg==", "signatures": [{"sig": "MEUCIAICTOGEJFOZiTZ/8G2/p3tCFcj0l7Uqpf3hLNLygJRWAiEAsxGsmcPRdNYt49V1LraI2K4KIkACro+MkUkb+Dfk5Gk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq61Q//XmqIFBItI3nMqDPe9ZBW+gdxn50fxCjSHHG5Mf6Prvhro+jl\r\nNamK90MWW7gRpgA2ZEHnAHS9EI4eoBcc/eQa4VLV3iCHgVRCvTwHxrXZt5C3\r\nDbJ5xlQCPQjyw1zYhg22z8a4BklifL/ClrbHZj4hgLnaNtvCQBIiB7WFWtgW\r\nupIBi+EoWPXP4cBjcZwDbJ9ge5vl+ih+PiTGYhXV7RGnb2/wPT+UzQ9xkm0l\r\nIYZEMOKOC73ah0aihAif3iTRWEzx/4t+rQRmmyODWGWIRg6hkhf5VJIDpDzG\r\nLk5t4T4ZTNNIZ7T+hWD0H9qlQdptgWiVYf+phbHeEuOpbm2XnZzJFXSqdELm\r\nnan42emXV24Wkgsn3n4F5e9Bp6FrhYWPP3uiiFy7+TqcU+FIQqGTUoneG5Md\r\nf+a2ZOya+L8PtsqscRFbtN74+aqjOaJ146VugzXmIHcKKdM9+N/ZBj9+y7uc\r\nG9G5ObloXr0RceFDEPvcstzIpi7ZCjchU1iRaFs/G9RcpCcgDHNVD/DOlK+E\r\nhdximD3+DOkiTHhiSmKDUbyO1Tyxarkx+uQlosIJRoExrcoVf+rfGhAlSqAJ\r\nzjQTNmw7TfFj/72tVF4tlA5Vmv5og7mp8WHivACWMW1seyCectyl4ywloZSX\r\nwtqQLhGGj4Op+TJ/tnnZ9Xxkj9LzAPopoiE=\r\n=ux8E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-replace-supers": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.21.4-esm.3_1680620203718_0.38183724795276874", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-object-super", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "a8260fb28c90356c26672b6db438f95e9655d7ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-5sUUMUf0w/eN2R5I9a1UCUEqDrw9m/G5BMi0lTaApONYuMZ0nwKBfg8WZCy4klhXqw8RaDiUXsj+ATCmLLIMtg==", "signatures": [{"sig": "MEQCICDySM4pttsHQdwTvkN61AskVhxfUxS7h8HsBTb0D+PAAiBdhjNbl78HBJwAK/HrFJqKSZ72+jOXnUrWwqC3CRDdow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFOw/+KrFmzJAi/eMpKRiwGYKwXCX5jLGd2nNPx3ABVzFl+xsB768P\r\n0bqWQsTxBFa6xbwZ6wK08i7Rn433bHjFLKHwKPv4UcE2fYud4xGm63cwqWzV\r\nVRfP+WgfviCwYWjUBtw9iWwXOJawAecj03PXlJSDI/fpHiEU4tEEAeiphMpA\r\npcVS1EMp6aIRh5snBFviFmCsMZeHD7XusYdnJBLSbIgBDWNFcRBbLjkquF0v\r\nz8voumzb7uJWquCVjnxfqBOpJ2E0Yk/icRhgJ52VgPKUm2+LaDOG/VCFw94S\r\nd03+EEysUcEj0RC3+vrlomgy2yG+w7WeCDx0n2mz8zyhZ5HH4v8VtOculY5U\r\nMdXBs7MQBcmNKtE+SXb+zGGYDoJOnC8VUDzpG2E5ILy2ddDv5mIy9cbSNFpF\r\ntpiB6Ab3jhqD+uhyHpcZNM5l5iHiAnl2O8PbFmSW6Wf/WbJApa7SQNC5flSo\r\npY5CrJnsw+w+lUo12E2pd9mw+7g+lcDigrQmb7cEQHDUMjxglgXwqYSoSXIU\r\nKDNebKYqCLeibLZw/WcxAvAYHirL40QUlk/M0dz3mCNPUdheF86WLzZgZ8p+\r\nCUZyMGzeNtDqXwhbCMRXa/ZqtIE8ZEaES5tEPND0z9e/mhtfJWVTPS1XPBOR\r\nwIt/t49HjOBrLcwmY45tlz5Mriay/anzcYo=\r\n=JTNK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-replace-supers": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.21.4-esm.4_1680621231774_0.05851619551074139", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-object-super", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "794a8d2fcb5d0835af722173c1a9d704f44e218c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-klXqyaT9trSjIUrcsYIfETAzmOEZL3cBYqOYLJxBHfMFFggmXOv+NYSX/Jbs9mzMVESw/WycLFPRx8ba/b2Ipw==", "signatures": [{"sig": "MEUCIEgdXF+JuV/ZBa8AgFs32e8TYR5XSxT5lQwgzeDAQ0guAiEAqJC8kCuWi58kBnT/P1j8zRln1xObin0K7pbW8eDQR4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6661}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.22.5_1686248506800_0.09565360911778154", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "94c127e2fd26a0046fbc99ea9ef1a892ba416a85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-YMxJ4ESf9p0aeRU1WO7avoY5tHA64CNIu0vUs9K49muR8vrqpTzrc487H31dKGYFSVbHzOv1meqOlGjbUiV+3g==", "signatures": [{"sig": "MEYCIQCpVvCa6ZifbGPt+NtqVI0beLZMbQlzFe2nNFNxx/m9PwIhAOXDba/FP5/en5r3Bq5ZSCG2UWTZCq+2ZWsfAG7vyO71", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6560}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-replace-supers": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.0_1689861621824_0.7901774033346103", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "e4e38357874d012ad78ea343bb6b4cb7d88e924d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-cUlIKs15mqWTcKL3h4X6wjuHj2UbhMMSxi24B8g48BWWOrOK46nXKnztwB/6SHHtRUm+9sW6vtvCR/CB8VaE0A==", "signatures": [{"sig": "MEUCIQCFEsyC1yy5VU9ZH1aTisv+As6uGRdwxx2Q4NFPFeVbvgIgZvx7k6lwcIddYKTlPxnQPY7lG/0NvILqL8x1MFgLaIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6560}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-replace-supers": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.1_1690221175038_0.7446432226366708", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "246b07fe8229e00aa5910370cd30f5d797741686", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-G2H6Yw17TlMbFSZJ2Dz4yjY70L+csMYWm/39d+IPRwawoe03N1gWBF+quDqaNoRLEQUXc1Dul+YcXJVqkW0mmg==", "signatures": [{"sig": "MEUCIAxbTWA/LoDUnNtZ6H+ZnG763dK1S4QsrmpIhZH8gE4JAiEAh8hFpQow41Ublcb8+dUBhuXxFTTHxmIkvJ8QwQmulOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6560}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-replace-supers": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.2_1691594118122_0.3613027528562709", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "10f1d52bce0029f602fbf7bac3fe7801cc76bec8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-1Cn/Ef/DfXIp7UudZ2HMrDRuIVvumTu0mLM7oTseKQwOg4yyxFjO2O206EwtOmp871qPwVng11BAlTotp2koyg==", "signatures": [{"sig": "MEQCIGIavGR/F2ADVRB0qwCNarvbWJ7iuwLe3u8xfW9FP0YSAiB+HQxFYj7Z+BCF5crGRpUrXU3sLYWZw/9y3/s2pgRASw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6560}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-replace-supers": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.3_1695740251955_0.7818900109860747", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "6cb8e86e864f0d0a376ab9503d7e472a48fdec68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-kPg4++ZMkzKLQO/Ov+fgHwS9p9q4s85h9ELMsOgmcLpQZEHSq1F8gFUsfsob5P6zmDW9mstwL3eVagustNHd1A==", "signatures": [{"sig": "MEUCIQDyFJ85Whkc92xqMWicmRCTpiZmg+mlNGnszCba1uttEQIgYt0GIwk5JVwz/ruPx3o/bupkH6/eTvokvXkXrzfXDLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6560}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-replace-supers": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.4_1697076405785_0.3542763549516672", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-object-super", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "81fdb636dcb306dd2e4e8fd80db5b2362ed2ebcd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-BwQ8q0x2JG+3lxCVFohg+KbQM7plfpBwThdW9A6TMtWwLsbDA01Ek2Zb/AgDN39BiZsExm4qrXxjk+P1/fzGrA==", "signatures": [{"sig": "MEUCIEdcf/UuC1auTxIbRVClWcg3R5SHYn73zBFke+JMuJNdAiEAjxvjEbb3SjuMLXNmbiIgrnM/Z6rPjXYGSzumIT/MUtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9297}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.23.3_1699513434148_0.9181872169353567", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "838615d8d247731104a7563b47408afe47208e8b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-NwpoJYybwt5ISqq31fLKCGCV2OsgtbMEgCHIDBZXBCQ4+N+uZ2iqF785FZgkxwFZ8/qTv17nCuL+5JNmHA84KA==", "signatures": [{"sig": "MEUCIAD9Ls81RY1eSMMSapo7M+fs8nO7eeIhy0y55k64CnIgAiEA2gpOP3F4FTAZbR8IMiZb7Mi8/bQJVYMvo7vLHbek8+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9322}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-replace-supers": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.5_1702307977707_0.2329062815079952", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "4cbaf5e998ff56baea57e598715ce06542c47243", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-vYBceE/u067T4FxnUSw9AN2ATdulNOyThsaqycF82Pv5oQ2Jyff2utqJYzBe+BSPcx7+Cn0fG96H0ThA0cc7xQ==", "signatures": [{"sig": "MEYCIQDR23nqS/7DPUlwm2/jR9/MgyO225Gv4LboTHW/vlwI8AIhAOxf+BVZ4ch+hCkbR5nWZAnakT0GRk2aX7ahCuYbuGcZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9322}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-replace-supers": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.6_1706285677258_0.5142013278193789", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "9151c7c6c7c330eb46de809bfd38d1d18a134ba2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-//j1avX5n74/kSs3VQS/LiGCX5CJpn2Ve8CZxAH7zZ9JZBL9G260dqSpm0Q1KxZENwsak75nakzzScIFL2C/TQ==", "signatures": [{"sig": "MEQCIBgUsR60U58d07vh5Pe/fQMmXtWd43NZua44+UgVAzw+AiArhsRkpo5pC20CeIm3JF89AO74gJQykLyHYr7WTKHzQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9322}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-replace-supers": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.7_1709129136947_0.9732878737393724", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-object-super", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "e71d6ab13483cca89ed95a474f542bbfc20a0520", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-oKJqR3TeI5hSLRxudMjFQ9re9fBVUU0GICqM3J1mi8MqlhVr6hC/ZN4ttAyMuQR6EZZIY6h/exe5swqGNNIkWQ==", "signatures": [{"sig": "MEYCIQD+7VAYj3Lui6JY4iuSafD1u6szTLlMtMvmGlnJY9FhvgIhANLngbhYNbyAfRRlVtuAxluZLOTYW01OfFBCflwJWYjX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9227}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-replace-supers": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.24.1_1710841769668_0.9178820236068073", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "2e193bdea79334ddb673416fcb2d550c0044498d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Jp9Fd5pmIyvIY5cFJTgq7HXSUvNHhW4wLXzaxgUBPameDYtyyzG/es00Iv7iShog6vCUh6HVz+mFoEYL2oVBCQ==", "signatures": [{"sig": "MEYCIQC00w2ZmSnwK6sXijJyG6P0we7Kh1ZR9wWT3mhOZaFXugIhAOAu4y7nMuP4e5ZMcNmPEjP8Skj3u+DoyfqgjcPpVBSL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-replace-supers": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.8_1712236815022_0.20934722124002314", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-object-super", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "9cbe6f995bed343a7ab8daf0416dac057a9c3e27", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-N/C76ihFKlZgKfdkEYKtaRUtXZAgK7sOY4h2qrbVbVTXPrKGIi8aww5WGe/+Wmg8onn8sr2ut6FXlsbu/j6JHg==", "signatures": [{"sig": "MEUCIEnsfxqCvZa21JMuEYxViatS0KjxdZUxdmxgAaGVbF45AiEApVTaN7ufXumh9es/Oy1ONFBc8ou1TkPYYJSt7K17bSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75381}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-replace-supers": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.24.6_1716553505633_0.7897560878873098", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "f40a7220b661b83201b6e3d061ba62ac1261279e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-KqvgO7zjIVSg2j1RwQj0wvvB7P1K26cVQS2c8wP/Us5STCBvaMD4DlZZlcrd8ElWHvdO1c2tita8Y07y1PK/ag==", "signatures": [{"sig": "MEUCIEorYmW6Hts+JKIe3HesN+O3LQLXVzBObe9aHtMZXBL/AiEApqmz5mvO+PiI2n89SjH3DaYlvY3kwwNpWe2yct52+9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75687}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-replace-supers": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.9_1717423544617_0.6209887782540353", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "b6a28e824839a7072253c48339aa127bbdeb03db", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-6pXYFMkuYpQNZ6M+yGp7HMpwIq1FG3a293gifUvY6xH34IFmv6tJe3zSHf/m+VpZNxBYpBfXEKPVtGbRY3F83Q==", "signatures": [{"sig": "MEUCIQDYx1khAIFZTO1UitIHWBRfPUYSRj80HBIb9qbiHCVoDAIgV/Y8tPN9v5aexRFPMJ+Gm0gNhQl5x2HQ9duuszThZMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75695}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-replace-supers": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.10_1717500043492_0.49640681877181225", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-object-super", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "66eeaff7830bba945dd8989b632a40c04ed625be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-A/vVLwN6lBrMFmMDmPPz0jnE6ZGx7Jq7d6sT/Ev4H65RER6pZ+kczlf1DthF5N0qaPHBsI7UXiE8Zy66nmAovg==", "signatures": [{"sig": "MEUCIQDekFYuLPbX8soeCdFjRCzc3cobMC6oquYJFCwIJylBqwIgFZSyYSRPEQCXMd3wifhZivMNtEyGyTmM2a0os4EhsUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75364}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-replace-supers": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.24.7_1717593357193_0.07190026794313198", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "6624a1b9145afdddca9638a7e4dc5a96255b060c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-U6T<PERSON>udSFwXvonm0bdyik2FMZFradZVIlwSkEA4BR0D9GRZ0DIBAn3+E8eFoi0nXi9US8MnUGKia1CaTZ01xgg==", "signatures": [{"sig": "MEUCIQCky1kXjDhzawQ0/jfkMpHxP58DDWhWa85w1US1pksw4QIgDH/Uua+vQSWT6yVazbnYiAtHIohOpXkNf18H6UYG7Ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75584}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-replace-supers": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.11_1717751767581_0.060741736710100414", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "e56d3a005d65e69cd88fdc95cd0c595dd7b6fe88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-y0NYzoNJxpFaXctyot2US032DDSKTcCJZcNAmqToHe+/kJ5qNDoY3AM/8agGaLpKay4fv17gk+9zsL01zKjSIA==", "signatures": [{"sig": "MEQCIBb094BtrR2LETyVrbwo3+ohh4BXy3p4c/lWFhoPqxHCAiBybRtO4xb/s1wTuET8y8U40kQfYQ+1DXNOdFXxS1iFqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72367}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-replace-supers": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.12_1722015242959_0.0987267812024859", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-object-super", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "582a9cea8cf0a1e02732be5b5a703a38dedf5661", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-pWT6UXCEW3u1t2tcAGtE15ornCBvopHj9Bps9D2DsH15APgNVOTwwczGckX+WkAvBmuoYKRCFa4DK+jM8vh5AA==", "signatures": [{"sig": "MEQCIE8vzDRH5vt6o/TVR7U7Y8LKxFY10dj0eiYVTNcn9boZAiBJ0ePaJkMtO5KeOREGrh3whbsfkhAmuVRrVc7nrXOExQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79889}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-replace-supers": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.25.7_1727882132544_0.041360910756173874", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-object-super", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "385d5de135162933beb4a3d227a2b7e52bb4cf03", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-Kj/Gh+Rw2RNLbCK1VAWj2U48yxxqL2x0k10nPtSdRa0O2xnHXalD0s+o1A6a0W43gJ00ANo38jxkQreckOzv5A==", "signatures": [{"sig": "MEQCIHuCfUJhpLoUFNem/T0MrFZqPO3Mn71GKHD6nKKQVj5gAiBTLDKUoqzddCbSR8rFmIW/XPpFW8NcHxbzQNHRz/qlDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9218}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.25.9_1729610508316_0.06725525977249425", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "ba0cf1844d63213893f0f7d32013ae7a5a8f2323", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-PCLxpW6/NRv1+rIPYz/3rVIiej3uIOffKd+QGtSrsaqpTS8BuJW9G6yYC0emHJsW5Fd/zmKqCB0AIN+G2EkBvQ==", "signatures": [{"sig": "MEYCIQCpoRLeDR79UK518u7PzHqM1iq3BWjprRUTkR5ufOqmzgIhAMqyoSgjQJ9foVLUDmM2r69+OmYECKRCK9HkA6TK6r//", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9566}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-replace-supers": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.13_1729864488808_0.7575144692289237", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "11c85a5b6c15b4c17e63886df807dd49acc736fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-meFqiJUXNXleECOYmLvuuOUbjhj9kLQb1ZGbBzUbRc8g5mN+9TXcK197yleVsC6WJQjDew8UR+XshELF6feMlw==", "signatures": [{"sig": "MEUCIG+2egPiqfdfrUZqUbMKJFuFj1vSWyl6PJYSyiYAtOdvAiEAzToxYEpSg2Qi2ZM/pTqfKxTsSRXfTBOoTsXi+Ksk+8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9566}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-replace-supers": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.14_1733504077637_0.6221104412768408", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "9c3f832ff3e13d0da73857a017f69294a8280373", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Wu8nBI2ZjfbWkTX1Nw3E+WYdjAQaxKH33TX7Fn6PPwaKj6cxXLcQSVhIHBLjJJZFEjUIOnowclk85Apxu05gvQ==", "signatures": [{"sig": "MEUCIF/SwM3DnDRRWZkl5IVrG34VOT7oHdwI0OVyOqa8CeukAiEApnY95I0htKOGsl0a8Fuqzur+ZGNwMXZWCmd+YVx/xAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9566}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-replace-supers": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.15_1736529907387_0.0538138639432455", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "9153d5e43ac66a938456d6d932a40e7348662de7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-bzkaWpLrYsNDMjw3QtYwcuZYVX2rqMgna0/uCyfSq3YJ/zww3oMreRffR3HMOxFRvuxA4eV0OR79TrZlUiHeaA==", "signatures": [{"sig": "MEYCIQCApGrI7mnBa+JKW+4+PwvJt25jnxmVtJGHO5AqYu5s/wIhAPaDrKQ23VVJKJDsGFo/EGv63H59XkYOJMGDoKJFfC3A", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9566}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-replace-supers": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.16_1739534381412_0.8199946496335031", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "ef8ef5a23b5ea53d7ce21ea298e30055281eee7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-i+zDNYlRThplqXY4GkTeFOIcBNdF7WbzqW+ifLaZdhm1rvhmSLGHS/WvqOAOVs6Elx8EKuBlahI3J2xzuqeS8Q==", "signatures": [{"sig": "MEUCIQChVLY3uuiN7x1JeNqkmJLbMY7WWQWTr3YnrnHpSfhWhQIgTIoVMLnBWprhbfScGio/Opv5eYuDVFFB1jqxvhK/JMg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9566}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-replace-supers": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-alpha.17_1741717535939_0.7599144354156906", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-object-super", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "1c932cd27bf3874c43a5cac4f43ebf970c9871b5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==", "signatures": [{"sig": "MEUCICihcjPkaK5QN1TDBZjAkrV4IhLohQrALYRjYUGKOnt4AiEAtfAkx+Ne9fthSZxW3tjUSMh8qQCE4e60CvLj8gWPX88=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9218}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_7.27.1_1746025769748_0.45308643796040404", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-super@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "dist": {"shasum": "35dc1a8e4e1a01e080ac029ca804ad2e7256ac55", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-XDztRaeT7nQn74fN0NfhtUbckEwgLM8/+AN8+YbuYBxtdprO7EHcmIqDiiaberfYr4NZloaHUnqT2UDTYLRJ/Q==", "signatures": [{"sig": "MEUCID3b6pRDEc2TVffjlkhbdVPtllYgKt92Y+VaSJGfu6t1AiEAiengorrEw47nU4ZYhOVhhUyiebuIWSu8raV02e1bnwk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9540}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-replace-supers": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-super_8.0.0-beta.0_1748620307457_0.4489252339229832", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-object-super", "version": "8.0.0-beta.1", "description": "Compile ES2015 object super to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-object-super"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-replace-supers": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-object-super@8.0.0-beta.1", "dist": {"shasum": "73c74229de6e3863fd5174b3dfebe0dd471f4e30", "integrity": "sha512-cZfSOTLhxUJbVQxYpHVkO9ykSC6RqqXuz1KAMAgWZFhoxBNUa//xWTuw7iP7qlqPTvJB+gZzXPbf4u2M6Zx4wQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 9540, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDrlnhfX5yoyGK0h2XjWZ7kGVqaZaLwj5dr62VR4c9y9AiBLoJQ+LjnWdEdBpS10XFH6o/iy6fXi+hqzh0BHtcL8Zw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-object-super_8.0.0-beta.1_1751447089614_0.2883933348545815"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:19.997Z", "modified": "2025-07-02T09:04:50.027Z", "7.0.0-beta.4": "2017-10-30T18:36:19.997Z", "7.0.0-beta.5": "2017-10-30T20:57:57.051Z", "7.0.0-beta.31": "2017-11-03T20:04:32.056Z", "7.0.0-beta.32": "2017-11-12T13:34:08.767Z", "7.0.0-beta.33": "2017-12-01T14:29:27.473Z", "7.0.0-beta.34": "2017-12-02T14:40:25.032Z", "7.0.0-beta.35": "2017-12-14T21:48:32.888Z", "7.0.0-beta.36": "2017-12-25T19:05:49.869Z", "7.0.0-beta.37": "2018-01-08T16:03:48.333Z", "7.0.0-beta.38": "2018-01-17T16:32:47.562Z", "7.0.0-beta.39": "2018-01-30T20:28:49.128Z", "7.0.0-beta.40": "2018-02-12T16:42:56.988Z", "7.0.0-beta.41": "2018-03-14T16:27:06.063Z", "7.0.0-beta.42": "2018-03-15T20:52:14.601Z", "7.0.0-beta.43": "2018-04-02T16:49:03.456Z", "7.0.0-beta.44": "2018-04-02T22:20:44.341Z", "7.0.0-beta.45": "2018-04-23T01:58:55.166Z", "7.0.0-beta.46": "2018-04-23T04:33:12.176Z", "7.0.0-beta.47": "2018-05-15T00:18:12.706Z", "7.0.0-beta.48": "2018-05-24T19:25:07.628Z", "7.0.0-beta.49": "2018-05-25T16:04:56.164Z", "7.0.0-beta.50": "2018-06-12T19:48:18.647Z", "7.0.0-beta.51": "2018-06-12T21:21:01.749Z", "7.0.0-beta.52": "2018-07-06T00:59:51.271Z", "7.0.0-beta.53": "2018-07-11T13:40:54.350Z", "7.0.0-beta.54": "2018-07-16T18:00:32.434Z", "7.0.0-beta.55": "2018-07-28T22:08:37.247Z", "7.0.0-beta.56": "2018-08-04T01:09:18.112Z", "7.0.0-rc.0": "2018-08-09T16:00:29.941Z", "7.0.0-rc.1": "2018-08-09T20:10:13.146Z", "7.0.0-rc.2": "2018-08-21T19:26:17.439Z", "7.0.0-rc.3": "2018-08-24T18:10:00.626Z", "7.0.0-rc.4": "2018-08-27T16:46:51.187Z", "7.0.0": "2018-08-27T21:45:15.837Z", "7.1.0": "2018-09-17T19:31:28.782Z", "7.2.0": "2018-12-03T19:01:35.166Z", "7.5.5": "2019-07-17T21:21:53.117Z", "7.7.4": "2019-11-22T23:33:55.115Z", "7.8.0": "2020-01-12T00:17:37.959Z", "7.8.3": "2020-01-13T21:42:29.920Z", "7.10.1": "2020-05-27T22:08:22.373Z", "7.10.4": "2020-06-30T13:13:24.292Z", "7.12.1": "2020-10-15T22:41:54.054Z", "7.12.13": "2021-02-03T01:12:03.810Z", "7.14.5": "2021-06-09T23:13:12.640Z", "7.16.0": "2021-10-29T23:47:57.079Z", "7.16.5": "2021-12-13T22:28:55.290Z", "7.16.7": "2021-12-31T00:23:08.649Z", "7.18.6": "2022-06-27T19:50:42.025Z", "7.21.4-esm": "2023-04-04T14:09:58.570Z", "7.21.4-esm.1": "2023-04-04T14:21:56.748Z", "7.21.4-esm.2": "2023-04-04T14:40:01.746Z", "7.21.4-esm.3": "2023-04-04T14:56:43.903Z", "7.21.4-esm.4": "2023-04-04T15:13:51.925Z", "7.22.5": "2023-06-08T18:21:47.038Z", "8.0.0-alpha.0": "2023-07-20T14:00:21.962Z", "8.0.0-alpha.1": "2023-07-24T17:52:55.178Z", "8.0.0-alpha.2": "2023-08-09T15:15:18.303Z", "8.0.0-alpha.3": "2023-09-26T14:57:32.104Z", "8.0.0-alpha.4": "2023-10-12T02:06:45.964Z", "7.23.3": "2023-11-09T07:03:54.366Z", "8.0.0-alpha.5": "2023-12-11T15:19:37.921Z", "8.0.0-alpha.6": "2024-01-26T16:14:37.434Z", "8.0.0-alpha.7": "2024-02-28T14:05:37.080Z", "7.24.1": "2024-03-19T09:49:29.825Z", "8.0.0-alpha.8": "2024-04-04T13:20:15.223Z", "7.24.6": "2024-05-24T12:25:05.813Z", "8.0.0-alpha.9": "2024-06-03T14:05:44.785Z", "8.0.0-alpha.10": "2024-06-04T11:20:43.634Z", "7.24.7": "2024-06-05T13:15:57.361Z", "8.0.0-alpha.11": "2024-06-07T09:16:07.728Z", "8.0.0-alpha.12": "2024-07-26T17:34:03.111Z", "7.25.7": "2024-10-02T15:15:32.764Z", "7.25.9": "2024-10-22T15:21:48.549Z", "8.0.0-alpha.13": "2024-10-25T13:54:48.978Z", "8.0.0-alpha.14": "2024-12-06T16:54:37.794Z", "8.0.0-alpha.15": "2025-01-10T17:25:07.620Z", "8.0.0-alpha.16": "2025-02-14T11:59:41.602Z", "8.0.0-alpha.17": "2025-03-11T18:25:36.111Z", "7.27.1": "2025-04-30T15:09:29.922Z", "8.0.0-beta.0": "2025-05-30T15:51:47.645Z", "8.0.0-beta.1": "2025-07-02T09:04:49.797Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-super"}, "description": "Compile ES2015 object super to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}