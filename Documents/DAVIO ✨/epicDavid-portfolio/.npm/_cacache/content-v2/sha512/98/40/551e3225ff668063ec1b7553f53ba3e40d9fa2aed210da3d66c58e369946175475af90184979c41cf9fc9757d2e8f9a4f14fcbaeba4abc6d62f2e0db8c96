{"name": "@vitejs/plugin-react", "dist-tags": {"alpha": "3.0.0-alpha.2", "beta": "4.4.0-beta.2", "latest": "4.6.0"}, "versions": {"1.0.0-beta.0": {"name": "@vitejs/plugin-react", "version": "1.0.0-beta.0", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "devDependencies": {"vite": "link:packages/vite"}, "dist": {"shasum": "943f05da8b764cab319856763b4dfb7f7f733a8d", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.0-beta.0.tgz", "fileCount": 17, "integrity": "sha512-cnpqTkSSVuoWY6EFiwxpqVCw8zq1rDevI+O6bDcj8zo7RbUa1M8n8LBMq/uw7yDH2QSz4QUJ6R/nI8Lcl4oTpQ==", "signatures": [{"sig": "MEYCIQCpfV/Yfx0UBpGY2Z21s3WLiHvYBQTzHer1ecCyrL756gIhAMgrn4xHB9JT/wfhjtIq7quWDQrQCc4C0vk6tygI/ioS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51012}, "engines": {"node": ">=12.0.0"}}, "1.0.0": {"name": "@vitejs/plugin-react", "version": "1.0.0", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "devDependencies": {"vite": "link:packages/vite"}, "dist": {"shasum": "1f0c064e69ff67f9926f31e328c9cbb00fd873f0", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.0.tgz", "fileCount": 17, "integrity": "sha512-74mG6KCv5/5kLM/b8UnQyyY8oG58cEXcwnOETc6p+h0OZgl9lKCHd+0SGHOGh5zPKB0TpuAxxC46J5pVyAyTgg==", "signatures": [{"sig": "MEYCIQCNp2JD/7oQ+5A+FOho9+axHDKoPnpM6wbVqM4a9ePYZQIhALZPjehHCD670gCRgwWLI8z0V1ibSXGMho217sWh20Yw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51116}, "engines": {"node": ">=12.0.0"}}, "1.0.1": {"name": "@vitejs/plugin-react", "version": "1.0.1", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "devDependencies": {"vite": "link:packages/vite"}, "dist": {"shasum": "3cd13da4abcbbfa7b1b57aeaee6b34ae1bc17821", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.1.tgz", "fileCount": 17, "integrity": "sha512-BJ4Wq31XtOup5ysVefXGbjIFIGPo47p7T8HxPyOsGFV8AlnodkwHEUaV+kQnj2WOqsupiyEgPahltC93yBf5sg==", "signatures": [{"sig": "MEUCIQCvoOt+r0Ypjy2QPluM1bJA09+1A9JKG1rzY+m83f3v1QIgdCRVu2mUvnA5LSQZlzTtCtuX7eAL2QJJYINNuHNw6cU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51371}, "engines": {"node": ">=12.0.0"}}, "1.0.2": {"name": "@vitejs/plugin-react", "version": "1.0.2", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "08147baff5bcc0c66740209823169103d73437b3", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.2.tgz", "fileCount": 17, "integrity": "sha512-nhB4LkAzhOy2T+8fsYuK+4DQWTueMvzfC0+MMMaX9qVvKqGQMOgz1nF3qTMl9ht9ZzhDnR++4XZmHWI/MytgeA==", "signatures": [{"sig": "MEYCIQCmbih+HQOrGkqca3VJQL8XC/ujCbdyZDYINyCzl0rMHgIhAIgLRFCFo77/UjKVolcbKNBvw0uTTis2chFdp/RlwgQd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51528}, "engines": {"node": ">=12.0.0"}}, "1.0.3": {"name": "@vitejs/plugin-react", "version": "1.0.3", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "f590001325de5a6699a791e80524626059e5190b", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.3.tgz", "fileCount": 14, "integrity": "sha512-rwm0gW1YVXP2S5I8mkC1f26taObR6NZcdCwhxPuZYWasMkk0sljY4CJ2yc/HwxYanBOPUb6Dd4p3KEUdyyEfvQ==", "signatures": [{"sig": "MEQCIFn0beh0kTDP+uTEGt6Z+iexUPi4wmMBLjqnl7AU2M86AiAsdNxOqj5o+Vq0ygK/frA8d1yVcYjSo0LfxCGIbJlEvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32706}, "engines": {"node": ">=12.0.0"}}, "1.0.4": {"name": "@vitejs/plugin-react", "version": "1.0.4", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "b4e934915205f2b05998019549c853bb1bb1f6c1", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.4.tgz", "fileCount": 17, "integrity": "sha512-38/w1q2FON4e/es8WnAW0ZOa/RIOoOrpeWNUkgY6+u+M1eQZjyWWI0piLRM6fbDnm8Lm8Qtged8A7OZ/YnkNtw==", "signatures": [{"sig": "MEUCIFnss/vGMhx9kViggqKoP62UziMJeQzgAGOeMrsRFtzqAiEA3sJZgGn5p9S0slW0+lLjXN8gOy3/Wm2pOLOj+XxWhz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52124}, "engines": {"node": ">=12.0.0"}}, "1.0.5": {"name": "@vitejs/plugin-react", "version": "1.0.5", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "8da501137078c8cb791cf2611f23de7b96ad5494", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.5.tgz", "fileCount": 17, "integrity": "sha512-n92p2fMlo0FZED+y5WYt+tM1mnQsVxcjZ8HuTJhv9gU/nW2zHwEoCemU78Az0KvF+bgafv2AdU4VEnP24bppvw==", "signatures": [{"sig": "MEUCIQDcr/9Rz1Ej0NhxiHSGoRi7zfdn2TY74O2bRZb5AS2OkQIgCtHG1ZxJpTtB+1BvJCk6yf3O3HnNoKGQ8xSg6A7j4H4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52796}, "engines": {"node": ">=12.0.0"}}, "1.0.6": {"name": "@vitejs/plugin-react", "version": "1.0.6", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "edbbe70151ce8b154ed1eb6a6f1962146ce1112b", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.6.tgz", "fileCount": 17, "integrity": "sha512-wzbvi6w3w8yLDjurGwsZ0+fGW4zTSXnUzGz6wGeMrsnn14iYSyilqr3J1K2KbZ83Ramk+VBChh3FmpB09QpoUw==", "signatures": [{"sig": "MEUCIQD9INOa+EKtSpHB5UJQA8RKCq6TzbGQeSMRlZQqy/XebwIgcBs2uw7QLGjqDOQjhdns4zyQwSGnrE48kEPdDEg2y0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53789}, "engines": {"node": ">=12.0.0"}}, "1.0.7": {"name": "@vitejs/plugin-react", "version": "1.0.7", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "d542003afbae875f86fb89f3811a0f7c0c9479f5", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.7.tgz", "fileCount": 17, "integrity": "sha512-dzxzohFOAVVXpGlFn6Uvw2xaSLp80Vjmg2e5G1XdMV266vVKrcDqg9CWP/AiJiXuubNUdgy1k4E8dNXI6WCyhw==", "signatures": [{"sig": "MEYCIQCx4Y5VDC8/r27jICcXaba2HawimIJeHl4ZQssevunFlgIhAKtADmH72M9Kh6biq4rs7pVWheDChiAG8Z1YhjXNF0hX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54188}, "engines": {"node": ">=12.0.0"}}, "1.1.0-beta.0": {"name": "@vitejs/plugin-react", "version": "1.1.0-beta.0", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "e938b7e8800e559ac70df73c063d1e0139b35d30", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.0-beta.0.tgz", "fileCount": 17, "integrity": "sha512-iqfgnhSenxp+LknOV+bIc7U6JRYpYXzlvcJoCP/om/2eUa5hjvxcoaU3gvr6baVYLB4w/8+URAnhY0zU+lKIHg==", "signatures": [{"sig": "MEQCID1uWWiUG3dyfk48y0UuZk0E29f4NbGTogclYfhi/F5rAiBba7ZM2B1jkNXQ4jV1qLMDJINmV1g6bgPPNBJATn9K/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54407}, "engines": {"node": ">=12.0.0"}}, "1.0.8": {"name": "@vitejs/plugin-react", "version": "1.0.8", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "98163d107fbc02b2c22c299657d4911490bb8820", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.8.tgz", "fileCount": 17, "integrity": "sha512-Rt0tClew2QQokF03MoiIXTMMTIVKTH0eYulN9i81v/LitH4MfDHzIgetPzKvp+uCO9Fyb3tfYNKCw6l2/zW00w==", "signatures": [{"sig": "MEUCIEuuXir0eyefJ5ZwO5rlgip5F6QJtcSWti1WqcOP3xDWAiEAytInOnMxgha5zqTnLQyU9Te1jOMsiYJJA2Q+EXLz6RA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55860}, "engines": {"node": ">=12.0.0"}}, "1.0.9": {"name": "@vitejs/plugin-react", "version": "1.0.9", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.15.5", "react-refresh": "^0.10.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-react-jsx-self": "^7.14.9", "@babel/plugin-transform-react-jsx-source": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5"}, "dist": {"shasum": "3166e82cc986512c2e0411138305468488704e86", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.0.9.tgz", "fileCount": 17, "integrity": "sha512-1iTS/c3z4QWj8aXIItp6zFMI08UQEz5+fGvnahSCFOSIfazKDlCTEUUQJP23zoxFjeKOF+M3/WA0ZatcHUVEqg==", "signatures": [{"sig": "MEUCIQCsYu6OeMln7WB0+S2IbKq6/h4by/EnUmHb695dkfLpBQIgB/RvJl1OLI/aHQL2Qaab0yKDgm7/yPumLVOjGOQizJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56202}, "engines": {"node": ">=12.0.0"}}, "1.1.0-beta.1": {"name": "@vitejs/plugin-react", "version": "1.1.0-beta.1", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.0", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-jsx-self": "^7.16.0", "@babel/plugin-transform-react-jsx-source": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0"}, "dist": {"shasum": "c75c972efd803f60ffb8ce7dd5324d5e049fdfa6", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.0-beta.1.tgz", "fileCount": 17, "integrity": "sha512-2AL9pUkgUdTLHfkV4j5/cUznFqg2RjNj2g6gR/JfAb4oMPa7aZkDUYr80wMHKTwjOZyUEbTI1QhuSGzeOQ5GCw==", "signatures": [{"sig": "MEQCIDoebPoLx6nF40QD+97C2vBCE97oobOK6xybbOqaU1NyAiBp+W4DF0eyP5bdWpLB4XXsmbcue3iUQ5jbA4N6ySZFyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl/rlCRA9TVsSAnZWagAArpUP/0ALtpRYeYs7eWNUJ1ex\nijnk7/CP/SAVh/p+6inaKEfid+9smQlTztTPUDnR7vEWGjZrRGu07Au3aBa7\naSK8mlxYMPTDvLcmgBR+2eORJefFEDV2Tp1wzlACRSOC886in3Dv35rYuaoC\nK7ZT+qBIbd38M6/sVgc7Z546XjF+j/IuMwWa6nEHZXpa/nf62/gFuIF+NJOJ\nOPD7VD+GSaevMVUXl98hB5MriCh96QpCzuu87yWLmhEhmTkAVYOuDfBVVdok\n6p+hZWPprDIX1DNe7W4X/UIJpd7gGWG9k8AcUeYaPlPutpwXboHe7580Nr39\nqMnV7LVjlXyaHhwZigSHOcSQ48bhaARfbYYQpBDw3pDI7xIVYQLpoM92SJE4\nXBYEJRH6/DwGzBDXHdlnYIC0va1fHgmLFrbl5SJkqTVtpsPciHYqYNHf6QLv\njgWf0B016Z1MiPkaH7Ba2WB1ZxYGeybQYPOZi34SzHoEFAhhaHwoSH79rf/9\nZGbZ6RaPNg0c9D7waiMlqkT1NxO/oj7xBRE6949t6JVxNz6gIScT8idjUS47\n0Hvl5YR1/1SOA9K7V7uVHnC7rg9hLJisWwcvHMR5/Zi0VbkG/gOzJw/SUDG/\n51jkaz86ErrMXHq+d95ZBkqu9QrRINSCBLqBsalMxmm9eFpMG0McNKpHEKgk\norob\r\n=maaD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.1.0": {"name": "@vitejs/plugin-react", "version": "1.1.0", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.0", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-jsx-self": "^7.16.0", "@babel/plugin-transform-react-jsx-source": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0"}, "dist": {"shasum": "8dc889a3b37d475c4c5196bfa9dd6052182d6a1e", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.0.tgz", "fileCount": 17, "integrity": "sha512-hRAqG6/lYf0mfDm/1r0U81vwPWMpyi87e4bFK+LvVQQeIgdh6TwvfuF20waenSGSumz6JH2bfk9DbxdgDLRp/w==", "signatures": [{"sig": "MEUCIQC/0C/NcTY3mkttAPJ+qCFwaB3ba8z7JHFvPPYNW/Dq5gIgcs/BA3LREirymvXS2u5WBMl2vp/jEHXUC3TePq/tZQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm3mHCRA9TVsSAnZWagAAm0YP/1iBHrW3rv+4oUSGSAdu\nxSyULF848IWagIqUeZZyJexAtYF0dy35W87VRoa1Af6fzsSTvQaj4monlzip\n+JAMq/0sOE5bZHdlZZXgo2/VNuxvk+R90JWA6oYnerkWjnIQN7GEolrBU9e0\nP77KIvzktIVcfJHwtdUhy09DCml+APDO9hxjHyt/gvWJwtQJO9UwimdlJoTl\n9119apCE4480D56X8DdajTU8B+FybzJDiqqJlZekBgChp7N21H+Wa7847i1N\n81tkDFMYHlxpScmc4KRpj/pAIx9tkRAoheyQ+OIMCIQ98VQsQmXe/9Qj0GfM\nzBSp5K+lQa4IElOkczQgdVmEO8vYUNo/4t2NQRIf4kkdcnUXuiuUUzpbIopW\nTo6im2cZDAbFAsKaEgUkt/1/VgP74rV3nBSs8sAHhNwDMTsbdA+QCq/2KrOt\n4Ie7m90t+vEnjlFF7A1OP3rOI1pEiinm9qVjFSqHp0kfC7DN2xlRDnl8fZ7f\nJAx+rpNOCuhUErThGF59nPFyfUtfAKZjbCsVrB0FMzMuSak0jNspUt7cyhlr\nUE/sY6cxaH+Pdw+zeZwU0qEcnPJXloqJJRkZdDxVNrcJPa8uXphV+zn8K95Y\nmB8FK/j5CT76dhRE8VCcZeEVsWhNX3Xn4i0CULiwpNCPeNAB2cmoOwScVDd8\nILAP\r\n=IlNM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.1.1": {"name": "@vitejs/plugin-react", "version": "1.1.1", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.0", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-jsx-self": "^7.16.0", "@babel/plugin-transform-react-jsx-source": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0"}, "dist": {"shasum": "5a242c64fa0a588e5b203938e5bd6d05fa25edf2", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.1.tgz", "fileCount": 17, "integrity": "sha512-IJSRD4culdwQ6cRK0D1mstV1vdvYSb2HK1JQ1FDo6Hr7j5ppWJEwBC2v/Gy0h/A1lMmi4AnXACY/d10EgbQNEA==", "signatures": [{"sig": "MEUCIDRVMYw3d/32EGQcu0LrUVc62GCUc0yXDvg4/l0a2lT4AiEAzCWhPoq1Fvw0QeJe6RMrpuP2A3FS9l005i1pimpCxog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryPhCRA9TVsSAnZWagAAouoP/iyBwk2PVFDyBgaDwo/y\nZUEAfDwuXTgaviBQ0ptUJM/l3lPD/B1sZqL/3p4K8DiowFMJenCWn1WEp8S9\n8xufDg4XfVfmQSVn/EVSltzxPK7mdSMC8v7Akf/h06sjKdk8S5B5TyEV2xAc\nIFbwgZYDlMQ3D+Jo8wE0BLaZhra8jq8zwOcWYVvdO0qd44X7WGB17kSp8y07\ntmq+qtaGWhboymxP0QoLOZ45A/uciVs/px2tIDvCu/x1KYwXTEc2NvnqPEmR\nlJXinXmsm5OZGvG0WRQyEhhLK3fQjIIEUeuMJkSNpJwI1B/KOCp29sZYKwji\nCjUVxvlnMzLdo7FVOWUoW04dglFQWmW9+jp2pmaPU5EAmPU4i5sQghL001Vt\nnarTfGx6bfPPXGL2mqDxpz9lEJx3ZcuWoQQBDM6ztB2sGQGgkciRC2mcWhkA\nc8oHwBqGkALSf0Jrd5i/3oyGVRk7WmthD4mRDbzExz9CRM9kCRuW08xNgTCJ\nIS89zYyGcLaX4q+OHY5GhOP9bX0q5rsoVGiZ4aP4Ha3x+tJqKaO1Mn5/LJFi\nt4k3U8VKpPLI0+W/ZsqR+wAVPGoS8cfkiTfrsmr3y0ebA1OMZlmGuiu+6CXA\nSnfBjRZH/BoY5/N9XuTWNMKcKQtNS+rEILE6toEr00ldw3w2aoD19YWImCM6\n59O2\r\n=CDW5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.1.2": {"name": "@vitejs/plugin-react", "version": "1.1.2", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.0", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-jsx-self": "^7.16.0", "@babel/plugin-transform-react-jsx-source": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0"}, "dist": {"shasum": "104b97580b29cae03884b9bfaa2af62da762b1c7", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.2.tgz", "fileCount": 17, "integrity": "sha512-VCA1yTbZiAV5Om2smp3z/8lhXN72lWdROIs6IVnGupcDUFVzEMP05jE2rEawMuiEjLB6H175NTxhefOFRUeytg==", "signatures": [{"sig": "MEUCIFGwXZNsP/lIiQ/LE+xSX83bl18POPRskJ/ielE5R2DMAiEAzRBNFUpk3JXywMqZB5E5pXhZTMN5zeTHFYkX3K6AN00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtyN8CRA9TVsSAnZWagAAsWEP/24CJ/7vBty4yo7/288f\nJnajSrQTNQFx/N7N/5qILgxN9NsPkuOlIbvpv0cLTMV5ygT0Us08jJ7vBmJ1\n0BTadIjdsoR/GpSddz3kqmAHNQGg8VP8SOletm17QogvPZafw3wqrSMo7c7/\nUigr/rkmPYE+alqkX42PDlMuCVMXS6Xtvu2zvRK/VHgfFzo4DRDaSNBbeouZ\nW6E/FW9+Coz2UIsEimGU5d+POerJUNm16mpMV3TPFBKWRGT42nk1nBA3GpBO\n8PEvOWUTdpHKyNuXLSB67ZWWqeS2r2xBuNiTFo9O2BSDiVYCKf9YjhXBjPEJ\nX8MsQn5C3d8icncsYvnrMkAnVBt+9uQqN95P5B60POZZXnZnyS2oWoJnRF1U\nHViWJqnSpYn/lmPF8VVeu7JI0ZWO76lZ6jh+oYN13D8G2b8HU8WnjMsokFBk\nLu1l1WJM6t81BO4T5drn7sflLzZ7mpF4QIWQgsRoSZNpFR73RJzLUK+P6Yws\nLjqDv/A2x5hsZvaj9sjBUwTPDbzInS6RNVgsPzm138CU0K+8Ft12svSM2CWZ\nTIgZEiM+9uesYgOYk9ZaadjMDJeHrsmC/r62hj+AzubvVa/xUaIxVi8nb+rS\nY58eKjRxVft0qkisJwg7NrmyiYoqhJanor4ynJji6Fr8mC6bHm1qpgsCHoWi\nAmIq\r\n=33w7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.1.3": {"name": "@vitejs/plugin-react", "version": "1.1.3", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.0", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.1", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-jsx-self": "^7.16.0", "@babel/plugin-transform-react-jsx-source": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0"}, "dist": {"shasum": "0a649db2ea4637fd188adb36502b59da05ff6303", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.3.tgz", "fileCount": 17, "integrity": "sha512-xv8QujX/uR4ti8qpt0hMriM2bdpxX4jm4iU6GAZfCwHjh/ewkX/8DJgnmQpE0HSJmgz8dixyUnRJKi2Pf1nNoQ==", "signatures": [{"sig": "MEYCIQDKEu8gUk0mWeYw/DV6DkRNxKIihfuctiRjGN0CaICJXAIhAPRS/zdCD5p0npy1Q9HS1SDgLW9e92ToDm9C4nJonFuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht7XECRA9TVsSAnZWagAAYxcP/3WPPWtxE0AODfB56W0S\n/sHysZtTzXkRaG+lCLfSydFFKLgaIVDXMtt4JiXV96DFMMCuU6s3S3gU9YdK\n/lcwCFkuu2HAXIsoub6uF3meJ4TEgekXjzetCqvC1I/+CXN0+uhpByubbJUR\nXxxQuJYiZeGppiBvR48oTnpWfS+AKb2uLs7x2dY3u6cULqVdEekGxQqeBbvL\neVdJgxzTSHdydWai6b8xF/N3CEu0YC/g3fcL1PdyjZ00uUWkYqoIRZF6Wt6s\nUKP0Zd8s6rsVnpt2+CzimAawuq+vWOp0DjLfLD1Mp7/4Xfon/ZXAuhlrcXiP\nlsra8kZVD/Jllp30JDhWSv+u/O5qEJCwKumw31Netfk4apRNQfz4Ctm4HdTX\nsRfcbOkna1guicC9oUIG/aP5qn1LzojgrdIC/skz7WD+qqRHXVVelJ+Mj+je\nPMjFux4huc2i4iHUIO+K3RDEOsomOT+yqTjKbW3yMwpbZrxWD9514YwMP89N\nD6YPmc/VKodYQSNN/MRe4j9mtU4rvU5bje9xxLFQj6tnFE64NRcB+gMlIh2m\nW6ZndFKJ9HlccTMM9R/GBGe5GoQezsb90I8rcPAuFKaB9FxnJIqE+f3ICUdk\n+7NvjKWQec9eIHfIA7xbVScL2xzrqpsLWPuNG691iDubd3+beoiyTxolk6GJ\nmXs5\r\n=W8DE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.1.4": {"name": "@vitejs/plugin-react", "version": "1.1.4", "dependencies": {"resolve": "^1.20.0", "@babel/core": "^7.16.5", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.2", "@babel/plugin-transform-react-jsx": "^7.16.5", "@babel/plugin-transform-react-jsx-self": "^7.16.5", "@babel/plugin-transform-react-jsx-source": "^7.16.5", "@babel/plugin-transform-react-jsx-development": "^7.16.5"}, "dist": {"shasum": "a3f3f49d11890bf73bc64d545ce8fe7f5d506e85", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.1.4.tgz", "fileCount": 17, "integrity": "sha512-cMUBDonNY8PPeHWjIrYKbRn6bLSunh/Ixo2XLLBd3DM0uYBZft+c+04zkGhhN1lAwvoRKJ2FdtvhGhPgViHc6w==", "signatures": [{"sig": "MEQCIGNaQByOF0c/1zlJzb2oc0JVkOoLYrj+RDDtkpbS16GOAiAAmRvAdmYT2aGIsoJM/vVpZQdOGXIYkOTP2pPIeziGdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1LQcCRA9TVsSAnZWagAAVWEP/2+A58Chnj+mxygX37sC\nOuT0FW0dhy9P05ZryhUsOcM2+d4BfpWqsv6PurD4OXqAdRzY4B91++slJeN1\nHKHv33bNRdqvrRJ4Evvi4MsL2RSjYuf8OueGDYYJ7glKKQIO9Zqx0JNWzKeG\nwDEYw0+MfwFlfeaYmk2lnDMW+6+iYyXrVwNzbBrIGghMaxim36CV99cvhyND\nNu/ZMcXXLw1GBFeIqlOnl15NddxRodrNKjDQbTpPctCFb87CBq23No53ajqS\nFXBams19RU6ZcX4Iq+sKuSbfsRaFx4m8b0QkDbTpkLDJdExnE3I5Ah1Uvs68\nfWtHjREa4E3POkEykMHTW8Te26VnuC9yUZ22qOYe1Ueq0bW9j5I9pU2PJE3U\ne/O5oul52LOB3sE1lziHIYGWpvsqRfeWlcvFleNB8zkuRiUDdT9qkZEtSpQ/\n1v43arNimVSjaPB0WCp9Uuf+lfDlADRgRAVPojsr/Oi91FO6cCLPqtcZcrfJ\n3xgxyjzhLl95psjrBh4Z6zi94tovOkpavZNzaT1yfmE83pZbHHzzsyvlVBil\nbCNxdp4Oyems1yvEVjQiCiOWHdR9o5CS3dKNCvuZpc8zr50VqwbMUpx8gBH+\nzqUOoRrV3xSUrd+AGBtwbVSt21VeMZZ3wy0i6CEDxTg5ApgTny/iUKpKyQJc\nZfJ4\r\n=FjIV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.2.0": {"name": "@vitejs/plugin-react", "version": "1.2.0", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.16.12", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.1.2", "@babel/plugin-transform-react-jsx": "^7.16.7", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "dist": {"shasum": "4cfb4c0475e93885e56d66ff15e12ef4c34b0af0", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.2.0.tgz", "fileCount": 17, "integrity": "sha512-Rywwt0IXXg6yQ0hv3cMT3mtdDcGIw31mGaa+MMMAT651LhoXLF2yFy4LrakiTs7UKs7RPBo9eNgaS8pgl2A6Qw==", "signatures": [{"sig": "MEQCIDFJBgpHjV7qGJzyS97s7hNGpze6KVgq3r1j+01ZcGAgAiBEUECM8CPkulhxbfelm340oQ4gIwCAuqcm50854skvaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA1lOCRA9TVsSAnZWagAAWfsP/istwc0kj0Msc6zG03rp\ncZzY/KpDtmIZuWyKd9iFkpRVcMgVUFJ0jUCbw0Su52kgnP+0hidpXy9vrIGc\nk6BRlDsgI7jZ03OLHcMVdocFLoqSUcolDx7A6Jbn/PggAMpEQNMOYRH+Wh2u\nZJMNA26dMFNQCurVat7EWXSTB7FlvLlK3H1zxihynqHIh2N0+yotgTCGXzUA\nB2fa5iYDhun1u4wGKThVEw4tHhTK2StH8c4KfTAdSxIUVOdg3k9tgSM6VjHi\nCt/esdWbcGeQdapGAYILzGXIinvVmHLkdQcft3hhCA/Jlhm+tz9AtUFmsMix\nusqzqyjZ9ahC4gcegSF0ue5hnpExMVRSTwzBcT9GOVYc0BRkRaSJ28EU9uWB\n7Ip8MX+Xpvelo2hdKbA5hrR46HueW0wM44c5LxRkgJ8KlZdz/80S/dmlR0+C\nS06ZsFzAe2lh0jEaYCqhjJRCF2V0YqOgAxZBs2Qwg3VeBTvxPenPELMHXSYQ\nTDzoYQ6QPYy6NkUn9t2tD5+YvqAv/K7jucsjAStgx9cb6nvbiXh4FZSs9pII\n+h0+EOYS4ufPzJGdq1Nlj5IEpe7rOhcUJWts5cnuQmtlpsadSe1bvKaTYl+b\nC0MDBjR+kEB4iewnXsnJoggLUjhBova8ipP1us28ezR+2Vnj3SOv6dcAZ6Qe\nthJZ\r\n=DRTY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.3.0": {"name": "@vitejs/plugin-react", "version": "1.3.0", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.17.8", "react-refresh": "^0.11.0", "@rollup/pluginutils": "^4.2.0", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "dist": {"shasum": "efd4b4383edc12780cd8d230c2daa9d266690bb9", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.3.0.tgz", "fileCount": 12, "integrity": "sha512-H+yIupjUE4a+E4oeWUv4xUJIMR0DWBIMUG/DYgvj0J9Vu1rdHAlJ5JdbI+N1KDUD7Ee2fZ1DMPZ/NBg6mXtoCw==", "signatures": [{"sig": "MEUCIQDkAVHPMleuy6uJUENoLUR/CSekc4IprKkvLLCVcuUKiAIgQ42BrtKyLbc97kg0lsRPqISqSsENiQFwkTNFnSNYBmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRE/uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfXA//bR9pLiEG2LkbqgHXyEZUBp7Zl+gfboAI8q5hbXQdkduBo0d+\r\njBSEhFdYsGPlWcGyGhCVhxPWxmLghshdktF6YGmEJ7YetrQTfLbcjuCXpqd8\r\nWeB9ZRIuNI3UcQpql0DOu6AEey+3bDWogncYqCQSBei3luuzgzX1GRuqQoiA\r\nto+e3H6nZ4GRoUyKCdfed7TGdnh7ZsIL0BHcV6Q0vGvuh3YL/yg4VQ3oxc3B\r\n34+j748YFhLInbmH2xL9bre1gRaYrdTyOnZJIyBtyzcY1ukUlacO9BFoAvRq\r\nGlJaSqfLMQ2LM2P+EQcqXTfAA5ut+JpqjpfEM7ylbaSZDDkiFx/swk/K0MNS\r\nXRU+ONUqbP7hwXHWuzVpjOeC4oP+KjaiR4DmB2uD/f58lvUAO0uT1VtymNK+\r\n0IAj62MsZNzzy+RwVQYiE7axwx/HzJyPdn9Dri0zXo8gXKHAnnrStGVSjW/U\r\nS1mmTnNO7rR53743ifMl1jwXIw6JoaMmwWGd+PBta1U5jhLR9U9noqh0GOWx\r\nvLrxvmxeDwgPWcKJv6w6lZDUIfXUgAVbBjuKaLi+VCSfTElCB3yYWcsMUHDc\r\n2ph/DDcjBqX9tjOWds+WIefBAAlG4ECYi2T/uvzJvNpik+E8J8D4bgj7fd9o\r\nA2f/wbIvzWMt7MoEw0crXO9CqGTNWiIQDyY=\r\n=s58o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.3.1": {"name": "@vitejs/plugin-react", "version": "1.3.1", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.17.9", "react-refresh": "^0.12.0", "@rollup/pluginutils": "^4.2.0", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "dist": {"shasum": "bf008adf33e713215cd4a6b94a75146dd6891975", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.3.1.tgz", "fileCount": 12, "integrity": "sha512-qQS8Y2fZCjo5YmDUplEXl3yn+aueiwxB7BaoQ4nWYJYR+Ai8NXPVLlkLobVMs5+DeyFyg9Lrz6zCzdX1opcvyw==", "signatures": [{"sig": "MEUCIFB3JD4ZKAn7plg5D1FK2UrCM0ubWPGn1EwV++Eq3DT2AiEAlSao9RE3cbuGC6E5if68Y5LKzxHovZHaf6QGjNhtDd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVtYFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplGw/+KBXvrnXO8L1yFzSQaB1mAESG2n76+hwoTFvRwdGkxBYcz9QV\r\nbkg0hVlyxBxxmcG8BivZZeZZd7zOvYBalkfmIyy5cBblRoUYCos+J3j3RPTh\r\nutRDIy3UE7F6uTEjvrLivYQ27BxfM60D+f7EF+VyQ09UPNQTbfMy/DXrKFmi\r\nfbffhLn4C1+58edpoyztj+dWZXf/8gPGTKqykb41CpPFv3AB1V/fp7EFgERQ\r\n2XlSF5HNwiXcNmiViTNz/C1R/F0XcFQ5ZeEwFWPTxIBQthJm+TxqqKtLc8Fd\r\nELllpUuxtW9WTw8ALhbYQDvrbxoPZdWQoAVg8J2e4HPJeorb6AoPvQVj2Wxa\r\nK7Ywy8+GTt5Gy47/8YQzHCq5lMho3mKi/gZk7NE4/tGoX4VrnDSo78/YtI9W\r\nf5Xznwb9z0D+zFDtHDVsbyA9K502UDenm7ueP+BA0PE9hA0mUW9yU5H8gQA4\r\nXeyXNjtYYBNFJ0gnRKkzP+IFRTFK/eWVIFfDpWLcA1VE55zoNMQiS9L52143\r\nYmR0dTG4m2WHOul8qOpSJlABwWRNGy8fM/x8RvSPjWRWu0qGqGKcVWcY5TEF\r\najp4n2Z96J9ZU9LFtWJIIRA5b4c7XOStT9YqYSJZXk4GUmsR/BI8h3TdwsB4\r\nnzW562/fF1iySk1938IsTlkhH5MJBxurFIg=\r\n=21Dq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "1.3.2": {"name": "@vitejs/plugin-react", "version": "1.3.2", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.17.10", "react-refresh": "^0.13.0", "@rollup/pluginutils": "^4.2.1", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "dist": {"shasum": "2fcf0b6ce9bcdcd4cec5c760c199779d5657ece1", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-1.3.2.tgz", "fileCount": 13, "integrity": "sha512-aurBNmMo0kz1O4qRoY+FM4epSA39y3ShWGuqfLRA/3z0oEJAdtoSfgA3aO98/PCCHAqMaduLxIxErWrVKIFzXA==", "signatures": [{"sig": "MEUCIQDNxAeX0JaZMlYDehGCkyNy/aLF1ODylKtSwtmH2X5iMgIgLb93vuYG4RhkMmg1k/jq8O1KBG5jWeqb4ZKRDWZha9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib+RDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7pw//Rbkaekmgy2pjOv9Vwc9aV7fiGakN8s5r5qO4Sd7qx71isThp\r\npEC6r62qAtcW3m/DI41ff7DOQmKPGaj+i8JDCvjSGDG+WkBu7JAwO1XDTn0/\r\ndh6fg3By6YXr6baNB8IgZ48yi5S26nRxF+oL34rsQStmNZgtXk3wPFxYXX2I\r\nFfMpUDSzlWeK5KAnUuz9HT6FQwDBvS9OPWpdLX3+bpog/VJP5DOb+KXO3CW3\r\nIs3LrIsavWLkMn00aJoVJp2j1d0SNeD1+++4yV+oh2z3cfC1qikgDYR9g88+\r\nEOVA+uZ/deezXsxNvo2BOI+8cDuPRisOFI1YXJNiG0P3/Brv+WxS1zPqc+GD\r\nChNrbWahkRahLOXZ8Jy+ZniE5j8qR0PN+jgcCBHWQdOKcMFLOPrQW9qgQJK+\r\nvHguQ0hYnq2BHjJYVK5We2+c1SwJD+bI+ufBWKuRTTh2Lz8JsWf037TmE08a\r\n8+WC9RVATLqW3c4xeRExVMKAKzG1sdnt7LVsKB1FmtmbTtem1hGwEaLaWg2n\r\nQbMZUflddBV0C7rjlRI38Njr8Og8+Q/US9GnTFcuP6mYNPvyslg7N+HflkXp\r\nku6wYMCMo/NNg8AiCxRlVb35FwuUfLLpKaViz+YhZehwkXDT3aAbBozXdP/y\r\nECaaL+7TSUD6eSQcUjJKdiYFC5sieiDBUfM=\r\n=+QaG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "2.0.0-alpha.0": {"name": "@vitejs/plugin-react", "version": "2.0.0-alpha.0", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.17.10", "react-refresh": "^0.13.0", "@rollup/pluginutils": "^4.2.1", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha"}, "dist": {"shasum": "56990f435bc251ff44589b8a36adb3eb68940df5", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-alpha.0.tgz", "fileCount": 16, "integrity": "sha512-WzJmQQScF2qSbuDyLVwf7yoYFYBFwtvZQleavORuoqec6wC/58oyn3mxV5mjxzg28Sb1bHh+T5jifqcFpLHrNw==", "signatures": [{"sig": "MEUCIQDAglVNSwVwg1i9sitFS8QLZzeg3kJAg1Pum2vzI1LPcQIgf0Q+fkjzXqlh9FEHWtoAeLS9IZs8h6mylwomSWH3aL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifls4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiOA//ebb+I3JvQ4ctrIV4PyqqV0cjYxUL5gGapoaP/0Md5X1svdLP\r\n2OWhdVVVML8hXTdAw2qCIXDyk/6oCIlw1zTw0wk9VzlulsPfvFbVTIvwR8E0\r\np+YwPMPtT1aKh3+7BjG41iLENLr/TiOvd+BTes2xs1/NP/5nhm0bkt3GSFA3\r\nDiRGveQKyKvH3LU96o1g+yDMDOlVWP46lWBzbUVILPZpIEv/VN9dYLnIqcUK\r\n97tTzuHFCBxsXpGLf78m/FKMOHNU3DQNvYNng8WtRGrhiale9nQ9hACsKDh0\r\nohxmxjAGg7Wtfnh21wWMtsrMILydfxvUog3InfZAln1X4kjcm7Qgy8Wy1HVJ\r\npOsUF1sBrAiilG2k2ZmFCPpa5KIEvHOSbHiSm9yGkph2hrDYM0EKBPX7px/z\r\n1NERSAd3WLD5EFcztZtWHwoy4FRtIPXr7xt5NkBSYzRavROY5+nGYlqOCic+\r\nQosX8mW9KiP8Zn9V+ccfDzNLzJpxYt+7KlFBCiSXA9WhOxd/0Q6O7ixP9qFv\r\nemX5nRc+55yRHGFO8wKDcuNd0p2Y5f/UvVLbzti5yqdEHO7y7tQM6AvI5c/i\r\nQvXkbt2bqX4+o3vp7BNvjiTVf9xq8pngNjOdQ9d946pJg9HBngWsrXMcbnbc\r\naOyS8YX164jV/dolCXBcq5I2bLTCNJBPK88=\r\n=Hi+k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}}, "2.0.0-alpha.1": {"name": "@vitejs/plugin-react", "version": "2.0.0-alpha.1", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.17.10", "react-refresh": "^0.13.0", "@rollup/pluginutils": "^4.2.1", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/plugin-transform-react-jsx-self": "^7.16.7", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha"}, "dist": {"shasum": "1124fa2b60a3e1dba52b251d09677f111f563807", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-alpha.1.tgz", "fileCount": 16, "integrity": "sha512-Vks60nBAt7lt7fOViqE/SgaMIWcuQS5jWgPGlHaQrX2ASzdUNPJlIwA9fDXFAPfeSrV+ChfnHQIEPbssjlm5vQ==", "signatures": [{"sig": "MEYCIQDATeoSIL1XN5hyCderyfqsBuWol+My55Gr/DlnB67oFgIhAN9Qmi+xF/qfHRNCi6BhTTMqGHxRIRIpFOELMElTN+kW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihZ3BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPag/+MvJ4RTg7+Xv2GjNecP1XtVXJcpTeZLmxBPcyC3N6haO3BzHi\r\n7TcyI7G7oxQ/3vd16CaasRNeSrkUbIz0lP1g0IP3SaJ91b4AwW+R4SgkJyQ3\r\nEulwuw8lAqzhGmiVBZWzf9eJHS841n/Ac5s04zh5aq384FqKaW305a8QhfaH\r\nD4+zSSsn9LSieqNwuKIjfk72t5shzS+KGW+k55mE8gss8sbUHetkpCApmbu6\r\nfWxUryPKa3qpA3iByDq9FYrqhJ4QMN09/cGi6jFTyxga6pSBaLPQxu9IYJc8\r\nbDRqJCWfVDR9xak6M/t1kdeoQXTQC70z8K+vCc8hF1kSg7Z1iHoBEMgRU3K4\r\nnWnzyMn6Qd6fBBnOFePGuUPhGMhDwdxkyBdbImEbkxgBwciKwpp8JyEMjFtH\r\nDpkQxqCjMt7EkM9odxELmf+EmzSWe9wY5OqifoVis8pjlBSgsXFYMWH0oeAB\r\nJsLfQrcffVooZkgf7rrw2inIoioFC9Dy97p8AQEXrez/NHmVK1kvemCCjtQ3\r\ncVoeLvQd1ruX6zMsPpB060lkW3gm4fC75sPQCL4v7acz/rBePHYPRAw3m8lN\r\ntHTzrI4M9fyqqiCFrbVBYAhikXncbUBGtNirYSiSvJESIgJc0azcAm3sKX89\r\nx0RVgRtcEn2Uc2ui6jND50ygZ6pSbq/+Hv8=\r\n=tYsG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}}, "2.0.0-alpha.2": {"name": "@vitejs/plugin-react", "version": "2.0.0-alpha.2", "dependencies": {"resolve": "^1.22.0", "@babel/core": "^7.18.0", "react-refresh": "^0.13.0", "@rollup/pluginutils": "^4.2.1", "@babel/plugin-transform-react-jsx": "^7.17.12", "@babel/plugin-transform-react-jsx-self": "^7.17.12", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha"}, "dist": {"shasum": "fc1cc9e7aa1144c2b87ff6bc7c13f75c460ed2b8", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-alpha.2.tgz", "fileCount": 16, "integrity": "sha512-NKJIeXMB0t6Eyu9YP8HHU7RMtUsIGr8vTRYeHYqWTIKE23cRTIShJfNXdz7tTz6oE8RLllCoYoz1Ldj0d8dbCg==", "signatures": [{"sig": "MEUCIHJHnWFpJ2UteZjMfVaABOW87UeQGlDmuYqAhj5wXwSJAiEAhC2rh7Qe96DNCdkVb2jfOgczQPj/Z0P+AA3k+acmgA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij8PAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi4Q/+ML6An8gUBZPgQCnldTwQs+tNa17VSqbvA7YlVyNiNEBg77Yo\r\n4eSGTlyMxgK92Kuq14RHwDttebe5jV2C6OHjMmVN8j/ukIqjjXR5PaUrRxbO\r\nRDqBQHFYumip/tjity1WHH5IoDCWmZkjvnRzRrU+7usWWz9WciJFG4+DK6wD\r\nQtGm6qm9rLFUGswW4F6CfrKngAndcpgSRMtxv6tcia8l3SjzPBM/QcTORr2K\r\n55D0YutY0dA/STGZJBfKho1jTROTiboIK+RfulyovIFvLwW8IdMdAysheoL0\r\nGhAYP5ca0s9GlcsnM7g7nUAyZPrOV//gf52kbGDMeyr7FYnV3DMUJshiiFTY\r\nt62dCFMDGo7eSdcLx2kkuN2Ap7+wYrmkCd8MEzVD9S09IM28vqEbE8j2kXyM\r\ntwxYSGUC5H0NRTmFJwQ+cBYk1W1Tys1MNgrZ9PUX2Fqfab6LW3jls9gyKjsU\r\nrH6Vvmh0LL2ThjGiz6A/rdJqsOz2i59iqgEXSAFIxa3SYJyNhXXDjlkyv2q5\r\nlHUIPnBGwKXQ1O+er2jd1M5sGfn0lzcKu5QKcZk1uH+ZfX9JZVperXP3MtLh\r\nn6EESPBXqGQd3/IC8/I/P6dtOOu9Xw3USVz3eWqGMFPwj+q+pZ+yr7PoI8Hy\r\nba4MRdbeGwVM46i6fTn5Uy+ghZ9y/yBlC4M=\r\n=mV+J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}}, "2.0.0-alpha.3": {"name": "@vitejs/plugin-react", "version": "2.0.0-alpha.3", "dependencies": {"@babel/core": "^7.18.2", "react-refresh": "^0.13.0", "@rollup/pluginutils": "^4.2.1", "@babel/plugin-transform-react-jsx": "^7.17.12", "@babel/plugin-transform-react-jsx-self": "^7.17.12", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha"}, "dist": {"shasum": "f6da0b2e27e0051d44d5489749fe6db101bd7bab", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-alpha.3.tgz", "fileCount": 16, "integrity": "sha512-RqSeSTLuFxd/+0BmEpZifQvZ3VwrgJTO722YwVewkTbnL3tem5nYPXPgofyyI3Oa2urhwVHgu2IxquR8HdMC9Q==", "signatures": [{"sig": "MEUCIQCxEBBFEhgTB3J4RVTfqMrWLwHU/4KUuW3hdre5Hw3QOQIgQmpYAs1p1pwQiBYG+cIEQcy2jAz3jYjIHO/3pbCSBFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipeG+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCYw/9FPpmv448EY+xzkxGJ4SP/GFoS2+pd0f8ZC+OvxBrXGNuSDXL\r\nk67yq+7CuCOmH26IzQQQEhuet8wWwK0AHEvATycTMbGuRH02rN/eWGgHQW23\r\nF1vxD+vlCTJdruq8iKKVBjBf6aK7vKLfu84gy9qN1Rdrd5P07WLjcFOiwaFB\r\ny1dGmD/ozFBcN2pOzRjsIfsUdW1fycVzO8y2+tjmPc9SaFM9ghjQdNGnXKnE\r\noY2CU8g7Iuwu8T90FLzBvZQBqWJ4qvZOnsv0Wf75snENmygqdufzbfEX0G4+\r\nOnP5ZiYxfhgWqc7MdiUEY6963Nugw6d8ZkGePjQDCE4sOMv5SPlI+eypNdti\r\n0N2mTjfLwOAg3FQqsZjZ0VaqQ+pqWVYa4NpZSmEka0QVoJQ2BLIDQKYkaaVF\r\nyTj17nLCCmQ3cgfQGiDSV++awu+5rknCcoc3H0Bel2ldp17nGYFqc0wGBnan\r\nV0BQU0S8c48z/XexvMBT2YDOdH4CVmG0lETBtJ9BpfWfzvya5I8wr7dtKeO/\r\nvuIwW1mlLTJ2UFIn1WlM6oD2vjFdoSC/U4yjTpNAm9wdK4tMNT43Wl6jx3Qn\r\nFe6nn1kk8UN8AQ9kFh5DnWPrWAFLxEADpWts8BbaZurXh43pkBYUeTlbXNct\r\ndUqOAOecXPyv/EdRnF67HGibUmLsGmjZs48=\r\n=+Mt9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}}, "2.0.0-beta.0": {"name": "@vitejs/plugin-react", "version": "2.0.0-beta.0", "dependencies": {"@babel/core": "^7.18.5", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.17.12", "@babel/plugin-transform-react-jsx-self": "^7.17.12", "@babel/plugin-transform-react-jsx-source": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha.11"}, "dist": {"shasum": "8683ed41861d0f14ace463731d219ec74badc3dc", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-beta.0.tgz", "fileCount": 16, "integrity": "sha512-Tx6shZAW/1n89IJ56gejZuBSJZ80ap+ErNKFGKFh7NbHxJeZOmt/+uRRn7edvaJ1e2R/eo3NbF0alxCbqm9XVg==", "signatures": [{"sig": "MEQCIE2xS93midtaFsINlRX0rgp8MNrHaA+WTywXD/9R+OVUAiBdN6qLPeoWghvRRqAltWraCWw9t2p45EQvjb0OKaPI3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisczMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ1w//RplHE++b+2HfW4en98EpSx4ygjBDHArSe5IAE48Tw0vFLQLB\r\nmHIjtH0qlnIB+UwyCpZAouVqhs1+NrGOLCJ1J4ECHo9cOMcBEoMjqma3Lh6P\r\nMx7kxsrNImPDGJcg2Al7smMZ9m3wdaVtNbU8vR4huStMFB4sUhrqCyErT5Ol\r\nF3+AGIEP9wwklvmZMddRyJjBWfNhV5WGGRXgInDnqPcI9YwHg08mcSkB+a/m\r\nuiS6XnM4mJm8cYepU9oBUh900ze8ZyWwd62XmQ61nHYJHjLtNxbPVt+ypTyx\r\nAApkVJjG6Q3elU1G5AAzUW/Cizi3axaTx2I6UmmeHpmWdCBiIvKsYiHMQI5B\r\nDoAPI2oDeh33rSUrtC3oCo35QBLzMhG+rINY3fSsl3kZxIOXJlk3Loo2hRa+\r\nKA2lGgYVIqJkC7ASEEYEoOmBtPJLwn9pxLiKwD3siphERb+Ui0j2/yu8wL2I\r\nHQ3veAk5YOnVp0Uhnfhw9rkXTV6GxsFPLA6UjlOIS6dAfBraAEJtkzjS+Ct8\r\noaR2Zr8AqSAEH1XOr24K5JUdlrCsMN1GuPBboIWKrl/BiLPkw3ZNog382+EC\r\nz9W/SAAOp6Te7N2dUHisQpZqCpF1PORHMr2SyCeDESpI8Yi+GkibNVTlJRFl\r\nScI30zC6mcyqgCb0JIeP4luDvlV1TIyO6tQ=\r\n=mkVH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "2.0.0-beta.1": {"name": "@vitejs/plugin-react", "version": "2.0.0-beta.1", "dependencies": {"@babel/core": "^7.18.6", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.18.6", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0-alpha.11"}, "dist": {"shasum": "30912e08814b5961ad5cf7c08f0f7421e45ccea3", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0-beta.1.tgz", "fileCount": 16, "integrity": "sha512-5IFnQVWGIh4pAYG62wEUcNrlZXGVGwGrvFqW85CESsapU7PQAb92O0orfDfUiLxk24y7zXCLCaYP1jegSbzLYA==", "signatures": [{"sig": "MEYCIQDyqHNC03CFYN+pDVsnu1Gp4XW0XYB+PG1qA3xGKSeXMQIhAMQtDS79LUuQwf5jbE1WnMcuQKPGGT7W4/8ZgNN9mqi/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixT/rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp14w//XjJp8+4TWiMKY5RX10gLRtyBxoCBF5lWdaHCE2+gtRAPKzrm\r\nvl+W1IPfjZKOa7hg1Y6qHEr7iLVVAFx/+PLaS0Db1hgQa+VLMTssxTNdcQkX\r\nFdECoH+Z5eUReFKI12uOC1kOfsTi9jGHhAUNwbEcbcgujZ6FAqeVGCkMzMZ7\r\nJHCaNZ6AkCXoeaWFI9CPPZSOjvoNOrTtuyvDMowKu+fk3uL+o1BMtfZSo/vj\r\n0+DUIIFYyc809/cmFCKFKhzjFT/7JZUxgQqaUwouUj4G2oFPSYZcgFmsfYdT\r\n8rZZUzCe6L4BZeajzoRQsFmII17WWNEwq95Wu64HvSXVagOHnyHDFYWqDstf\r\n8w/ddfoKv48XUdVbMVRKAYPob2etqzXeAqPDIzFe/BMOL/6XcGKXWAZnG1M5\r\nvXuhvLUkL2PW4LTM0hC4f3HfW+hbrED2PWUiN27kp88L2VgN7CYiAONC0Ujc\r\nfncDRiB0fvkKoPgMyLpYIcU01QYVn8z3SKyVCylyFRuALnx16K49YZ7DDMLP\r\nakt8a+Hru6RxQlw51SHGR/8SEHwDtQYiwk8137GrwEC+ibmeCgEEjn2jWfOp\r\npGtvcNHSjNsylkjl9iLWRnnqm8v49ItxX01ZDKRYa5LhggEZoj0f0BQNeQ5v\r\nttlY3NlQmasIyFnG88fRqalHaG+gd334EJE=\r\n=Ld+x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "2.0.0": {"name": "@vitejs/plugin-react", "version": "2.0.0", "dependencies": {"@babel/core": "^7.18.6", "magic-string": "^0.26.2", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.18.6", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "12decd097773a00620e44b780b1d2c00df101449", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.0.tgz", "fileCount": 16, "integrity": "sha512-zHkRR+X4zqEPNBbKV2FvWSxK7Q6crjMBVIAYroSU8Nbb4M3E5x4qOiLoqJBHtXgr27kfednXjkwr3lr8jS6Wrw==", "signatures": [{"sig": "MEQCICze4tSiKltMqiNSeNvwZHC5jshuh8xt9LLWAOXarv0+AiApa52snGmhFvXxONmbvAwoOq/1Wb3AVpgap0ffBoLjXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizrsQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouXhAAoK/ccktTJT3pSje7KSLp3wWNRCAu5NyYqaOE4CPsiNwkchDk\r\naSgMqwnXJBiLhGvLMkCS3t3kL+F8VbGTrTUaLgTBiral+u74C5Fl9mh6a3Sg\r\nbTeTscccN+Ux4igyJi4RvJHBucD4VwzHpiCuIpZQR2ecz6p+PvExz8L+zWue\r\nJ3kqvqDTPb0eeTFtAUc1ZADHGbMqP9Wcnd9y8VSHV+jBnB9mFnnfzpZzvMND\r\n9ORpALJx1XF4t7zSMf2+ltrPoslz8D0ym3O30Ip6EBLJkYVv02yE75aNKO//\r\nud8nnNkAf3nIbG6PdyFlIUEvlbRNpc3NYJ5gmL0ZqUbcLj39wEX2Guht/ig0\r\nF96TRajv6kFJrcaI6DoC71wUX9HlFFQ+nVUTbNsOcKirX5yzUc+4SvAyycdW\r\n8cRfzyl6wVclxux5Maeft+IWCK63ENBnntn3wfjYQVamLH5ULr6STlgo3kpA\r\nrQB114Y355A4o85q9NFi9zojdzLXDYY7bXlGHh82IdmFXIDvn6fIZO5+AG8C\r\ncQvfz2TUomgC0RXROLdSegIQZsNnBX7DEA00Cx9TEDgA+jNT91nuWovyLegO\r\n8SI47T3kJLer7aTc+wPxf6kAydYvBLu9Pzbu32KUPoMHlK5CoEOwXqrVz8EI\r\n7VQgnBJ/HGau7MfSxW/xncZ6XafO+GvpYgY=\r\n=juEA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "2.0.1": {"name": "@vitejs/plugin-react", "version": "2.0.1", "dependencies": {"@babel/core": "^7.18.10", "magic-string": "^0.26.2", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.18.10", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "3197c01d8e4a4eb9fed829c7888c467a43aadd4e", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.0.1.tgz", "fileCount": 16, "integrity": "sha512-uINzNHmjrbunlFtyVkST6lY1ewSfz/XwLufG0PIqvLGnpk2nOIOa/1CACTDNcKi1/RwaCzJLmsXwm1NsUVV/NA==", "signatures": [{"sig": "MEYCIQDQ+64JhjBzB+PpkR16NAFvg2fO8rERpJoBaZkBtmPIcwIhAKoIoPWHE6b8lmSFLSrfCz21wssRr7UD87riqNlDtfkz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9WMSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPOxAAiCnWs4rjgZ0I0gBWD3F6yXHq8/TwFl7BLAtOIP1tLfjoLzPP\r\nh1E50CydDrx7TzWQxm2Dq+4VoDtEVCMELA3OzPn8Gu/MuCDgzqLFzpYMriX9\r\nLdo7LWSOF5dldvgvsYPT760pA5DBjzuvZM82Bme00wSR6aVR0sVENlhX0TzO\r\n22OLqzXtp99KcGINqPdn6rwOlpU3Si/t5fCYV53pNwSf2Enk/qGlHeioD861\r\nexxq6cW4h9ftM7EMo6tX8lhQAEugc7IiUQE/EBXqOoWkXO9YyRMesbGpqnHG\r\n+S7DBlUZxkEMv485HraOxki1//EQL55Fy71DA6080t4NzTyp4HjAoDSjjPk6\r\nbckNA+IDwTk69Xb9rKm3vpfVLeSMAFKNvLy2ATV32G+TlGk/A7+lEl1j7U/7\r\n2vxHhckCtMmBmAV1fxFCoa46ofe4lZi0RfGBm4hJ0FOEbMShNlV8PRW2BYxz\r\nZlxGgIBdKDUrpfWwdIdm/YWexMuNmmZ2b0cP3W11Z7SdUoh4qT1x6UsOXkE+\r\n+MhCpTGpW0X3rhjmSw6eDhy+a9BO+bVLkG+sfz55HSEru7YFVM4LC418zrKK\r\nWaBYj7Jsvc9Q4PfHktiUxovACs+DkwQ0Y3p57xcBpolknVnoYLUGDFewd/nQ\r\nlgZCUCVdsdt4KlplNKYuK0FFMoOZq+uhIR4=\r\n=r+wg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "2.1.0-beta.0": {"name": "@vitejs/plugin-react", "version": "2.1.0-beta.0", "dependencies": {"@babel/core": "^7.18.13", "magic-string": "^0.26.2", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.18.10", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "8e81f3024b4bd18f94aa2f4b193cff6bf5150ef0", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.1.0-beta.0.tgz", "fileCount": 16, "integrity": "sha512-srXAQPOrcge+ETW42vVXfutFgULi2t294L2tS6nfAqS2I0yM9hZl1oz3Ub5cSGo6CJWYHh0Zg7V8N9Y7/S1HFg==", "signatures": [{"sig": "MEUCIB8Z/9wKDt65MK9O3nmW8qHj+VsRsJwnls7XWE3s06pfAiEA7m1/VIvAsB3ip9yKAYnbf0weVklUKiI2IHECvbzGD2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDMaHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpS8A//YDTTx0mOaVgrjO17lRK3hfy8PNuo0l2LoRaGBnHbT0V7Acp6\r\nPAmBhIaZRQTBsSPi/v2t4hWkNC0joZV0SdpI9zhor9uYwowDSEcvYX3OZAlH\r\n1z8c0fuZcSzh3/nEt572Pmu1aRZg/frAlecDbyEHzQ7h7/BNv42L8+10jSF9\r\nnCZcJWten9jfC3R6+Q1l0dYD+dhGE2IJcqyCg83tg2aL9Vm6X6UYj6nlHJJF\r\nN48qUeMG33SXhJVg1iRjg8SVntIOll7uqVM3XRap5HY/UnPVO82P7vfHfKXj\r\nzvxk8OxsAw+WJHSFSmvh6RcaMCbrj0HJheWrSpfnN0NuuQxDrSx7+Kv5DYoB\r\nKRQsZFvzdmrfvKl7hLH5PU3q4JSf5Ug4CQImhu7GCOaF+u+Bi1fRYcYoZ/ie\r\nOgSe1OPxe7popMwRjtvpmxVbwruSL+cW89JUYXAzHpxLIfc2Epe1pD8PdRMA\r\nw+tyU2EWZkiARQZAIt4qqqujV9pWcvVrVG3zH6qVxPmXQGxOCIWSE0PrpOPS\r\nTbBYp36+khjgt6MlYu9zOLpnCS4O82V8Q6WraavsIBcOBIT/DEe/mNby+Kgv\r\nQTlIwAz0+uP6iTmpS2GFrqEgPMqspA8qq4rACpRjzTFJLZ5H23zs/mVCV+us\r\nTOn/uGr+hZmlJcIB1MbXUGi7MCOYem4mIxU=\r\n=Ukil\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "2.1.0": {"name": "@vitejs/plugin-react", "version": "2.1.0", "dependencies": {"@babel/core": "^7.18.13", "magic-string": "^0.26.2", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.18.10", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "4c99df15e71d2630601bd3018093bdc787d40e55", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.1.0.tgz", "fileCount": 16, "integrity": "sha512-am6rPyyU3LzUYne3Gd9oj9c4Rzbq5hQnuGXSMT6Gujq45Il/+bunwq3lrB7wghLkiF45ygMwft37vgJ/NE8IAA==", "signatures": [{"sig": "MEYCIQCLAveanoHtlJMn9c/eADttO7wApQ8Xk/6Joj+08FIeGgIhANSwePn1mTFx0Qp2jIJhW0FeC181ogx4xoWm5P1w682p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFcY9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroyBAAm/GkJjpaXZzlqpOQJcCh8F81QbxtJ0cxMIVA3Ef7cMSsTpp5\r\nvyHznaYbUQNmUyXzXpUttSF4TtiBY/imp3NF61tYNaMA0LRs6q7w2C1xCqqJ\r\nx4s+7tHC0S20OMmy9220wuvl2eTdEqP3FiUOL0g3WoF2j/nguSyJh20mSEtx\r\n7a87wKn0Yf1PXb1geQilgCLjtpuQ7VTkih/HGP7pt8bZXfiLEfcipvr7Wsni\r\nqETR8nmIqY21ujKaZ+nblRkD33LVMtsXjifV0c5lmPDcZMOhRPhvgxmfvhqP\r\ncDDYeSghumBJhaibXZ/CLUadr4MP/KaltSy6I/lgz3IWnYCxK9dlUVzlaEod\r\nNlQeXiqps+ZJs1EdPfCVquReMcrQ0a7CmIRxYo4mcp5gofSK4jWAmiMbBjvx\r\nys93Fxv4/I6ZhIHouz8fYp1k0II5EFJcfjC1TzRfy7wALea9Y6m8hxvzi32O\r\nxiGfmgBD2QSY4sneJ5ZJjMNYGx7RA8SYypoRt2ZpuNXCU52OSI5Tt/81MaSn\r\nQyQqgUVXPYmtAqaJZTgzzddk+A1vvps6ZHsY1fy3r+kR8OSVaOosPbk5JJLu\r\nGW3hiJiLQCNjwcyaE00R9Wv+0FIuULIQaFit8raTl2yosx7XY3nj0S06MJLx\r\ntS3XZM6AXHtfQmQwuFKZUiztRVgli+KnEXk=\r\n=mfZM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "2.2.0-beta.0": {"name": "@vitejs/plugin-react", "version": "2.2.0-beta.0", "dependencies": {"@babel/core": "^7.19.3", "magic-string": "^0.26.5", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "f593ae2f4e6f0468afa9641ec5c741822646f32c", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.2.0-beta.0.tgz", "fileCount": 16, "integrity": "sha512-jhYTn1AHeYFOsjkRkcSxfQyd9mofTZ//QRt9hMETK5iHiFGzYH+ByNTULhwIVfrCQzME7DEW4WMPhjWOHT0k7Q==", "signatures": [{"sig": "MEYCIQDOogEk966OZy3DreGNLpVNypmvSgqv+7P3jW5ITRSQnQIhAK+/K5T91q0zvnLvOCMGA7iB85CeImw2ebQui5jvccqj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPVeRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowaA/8CjM6xJJWxYllaz0d3rF0UjPJ6y5j+/zk8iuoargrC2taPVEk\r\nDlswB5PRacJbdNlsr+WCrxH1ycjLgTIh5Ie03+FVgFGkr6hmRfxtRUXPc4Q3\r\nJCxkVGRXkbem9aihUEOwNDL1hdAqosphetKsZNiZc6lcmcdt9BJwlOGaW0Mp\r\neZ4lgc1Vw23q70ToGBXj7nEjiy9LDGWYKzuXBFklCZCx+XcGuIGSqifADSeX\r\nJcWiIU9zmaF2V4kKjH3OUzejqzd/aRPsjGskMoYnUdRnbcci2oJeTDLLPLF/\r\nhWsK5zeeOeTflMcCRNGo2Ay/45K3aFLekJdiHGhwVoqyZVFhiJ/xq7L5OLmJ\r\nKWtH+FBmKmVyszIl2l4DfA/3rqntHVDjhwpbUWyeeSE6K+REgXPzEqDF6Ho+\r\nEifU2l95kn4pIe0dyRJtWp08Lvm5ekTPje0j9xMQQwiZgiAi3w+cDJLUsL5d\r\nDT54bxC575hKNyG+YqokzA80qNrcpubpkT4ulV3ez6tTLowCfuvApo15ncjB\r\nYDeNzXgFXyg2LIv0eJfk30PmbvM1Wcu5pjonfjJIuvT/SSHVyFVXypB21OR3\r\nG4OYGEeMKiipunnuZRZk6BkdaV3OpwGOahJwBL2psjTuN9bF3KCbNi+tjzJw\r\nXzkulOoC4wtHYJpowfKWGRR1g+NXk2aH3OM=\r\n=fFgG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "2.2.0": {"name": "@vitejs/plugin-react", "version": "2.2.0", "dependencies": {"@babel/core": "^7.19.6", "magic-string": "^0.26.7", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "1b9f63b8b6bc3f56258d20cd19b33f5cc761ce6e", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-FFpefhvExd1toVRlokZgxgy2JtnBOdp4ZDsq7ldCWaqGSGn9UhWMAVm/1lxPL14JfNS5yGz+s9yFrQY6shoStA==", "signatures": [{"sig": "MEYCIQDDL+cTNwCF6gcbPzuTs45s7nR+8A9f2Ob4tOxoAAy8TQIhAKwb1gfGCRy85CCWUhzwQhf0MnXhg8HqzvD9rL7/V0m5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWTNcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMyQ//bO9H69MqkD8Ng3cJ1g9gOA2C3S4r47a8g/9aOW1Rc3A1f1oJ\r\nSNO7Q1j1zyQJIM57MXqmFEkhmKwVTbaNc/ECV1zmc1sD39AduyIZIVyqc/s+\r\nK+92TUfSCrMS3FCKew3YpqMaA0TwdQu25ROhEVHkJZ8v3nJsHiZF13oI1ioJ\r\nYgCWrJUHrxqpoubb1m3tFrFSvZ78rfBF6zJ4uxfEp4M/UqQI3+BkjyXgnX5R\r\nZ1D+50VRZuO/y1TVpRvF1wXMoKjaqH4di06mqekQv9IQihKOqg+u+zE4SrRW\r\nJGbX77slLmLNZSxzjob1JrOUjnQOP/i+NiZ11uCSE/6dh/z97ouBZIBJdBcq\r\niNfX+NfGIHWdoV1tJAxa68QM6LgGVdoisT06I1b8hTk6aMB85fz8lrU0CBLi\r\nfnS06P37FuovOn/3ojdljgCw75puHTBex5LeqsFEh+DnDR90luTFLF4iy0Mj\r\n2IdJ1pdb2Cpur96SMnWG+sVwh+Yo0v3TMDYGLTYzvhPLiDoiu/2WMWTZHTDl\r\nb1CPY45ZtcxuAVakCS/+m0WMDLRdeHRQ/4B3cePoOs+w4q7TVJBCEn3yH/sA\r\nWHqsyn9+unWIWTOEcox1faW8k1kTZc02jMoF9H8NCuf5/ciuGbNjMvP58wAL\r\n6MmwaJ3lNsLhIYLyEvlzzO9zHcFXGgLoeFk=\r\n=mS03\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.0-alpha.0": {"name": "@vitejs/plugin-react", "version": "3.0.0-alpha.0", "dependencies": {"@babel/core": "^7.20.2", "magic-string": "^0.26.7", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "ddb09882098f16f14889fd54b068e62703f9690a", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-wOwisvB23xFKMguU+vDEaao7oop/JEGEMRAy/3OeYEyjPXklV8wkMNMk363lxxyWWyU89i7J8MM/80tcPxce1Q==", "signatures": [{"sig": "MEQCIE4aSiW4UCTrzmD8dH9MVSAf7qVVz1A+io0M84ws3V5KAiAaZ9in2q2M89j0H9qRKITBDv7e3QpoxcmCllRPnJcGiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjamNpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpipQ/7BfLA8Xfu5H/MxsIASVY7qYDdaD09TsQLaVqmQEYubGUA+6nU\r\njDi2t+oLcUF1TBdEscWbjXIFZBDxLOumhRvzkiWQ2VxktgN3LXyY0WS0nsZM\r\niQkglrOHKBNP9bchXRtJE16BeuR6tufdwkYoCIo6UCT8IZ2IbBBflTLVbuVm\r\nwAdibCRgm88TKG1sei0kWq/nZ/VcOSvma2PN9O+xVHhEKZI7rj6G3CWmAV88\r\nl8h51RcPvVcB+rwV2Zjht3ys2lz+kFVAfvjliaql7WKzGQtjhObFAOWALKob\r\nd7Wv6QRPc31YxJe82TYamp67gz9cu87tyY4Y+MdHmKMDE1Y2P6v4K2pHDfBL\r\nrBzkg26JE2FdyYADjTq5kZvbM6TI8yAwhgQIuv1ulf/iL1flnLORtWFHdjEE\r\nARv9RsVxBxeCTKxJ5zTPzEEaZ/EqTrKLvSHEpnuIQg++VD0W0Nov75czqs14\r\ndEsH5r/gPkwtybg4o7A70QF/scTsxuY4wT4rux5kmoVtkiiZxbnJGGgO7MVv\r\nYWM3k5qRijYxdytoQlYsvy5ijkM2DoAOQ7F8Pl0sDpWSD08UT0fP4X7Gn+hV\r\nFdmKVBlxhw8fvSMBKeCQaf9xT+ff5O5pLovNLeULXjrfEzYmO6HE91XwalPN\r\nbI3IW4HgYMWAgkg7JyoH2zaCRPGIE0YpcIE=\r\n=0LH1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.0-alpha.1": {"name": "@vitejs/plugin-react", "version": "3.0.0-alpha.1", "dependencies": {"@babel/core": "^7.20.2", "magic-string": "^0.26.7", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "d078b97915d01b85ed1f2dbe46132a7633770754", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-LuO/LC6HRiGDNMWUSCBs63eGr7jho1l8APWhKDhb+/AcMRyl/fnCGo+S22nKjZ5a4bBLuSiy0IG+/cFp35AiHQ==", "signatures": [{"sig": "MEUCIQCM2fL8ksgL0Tf9JOdUzsY3javBGTOYxe8n5qDyyVuD5wIgViCQDlfxS28rHIfX/OgcecrmvnXb5e/Wwyt4M2mMETY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc6w4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLbA//YoYNGB2PxAfnFuuZfXcSwIYk6JXIQrkB5shBPi8sQVSZVRPC\r\nOx2s8zeh5pZU0MxekqW2CaF+Egaz0E7dxsN0iahe+2i3GHAFVqDFhES8m/WI\r\nMSlgE75eOXDZcjQOuTs2DvAuBLhAA1hAv4Yu1hv19K6AJR14eeQQITVbHuvM\r\nUTTQ33UMobfCUYIIdpebdYDzRxqRmldbyVkdAN1eerR/y/ZzvSwR+PTvTVJV\r\nS4S4Hnl7/kCXj3vi7lI3dhA6rl0/L+MbPeobEqtdxJlZPmOx/atTayD1ZIvX\r\nMGVpJqTWFp3Yxxtv5EVa7UrWMnkASSgXBI0bnNCrA5W+VnHbXJsxUcdXcvGg\r\nkdFiOuncu4X1+3FfOGIR8dS0SaYVF+iXsc2RiJHxrUtg47chakLNuFEcD4q2\r\ngja/JBUOhiNhVH4PD3nU5zRkl8RlPUiJa8rhE1NyiSjqpu8UPjH3ru7E80Zs\r\nmi+mNl6cRFioAB4OHH4C6gCVlBGSlI3Ng7gcqh82f1s05D34WtcA/ne07Ke3\r\nQL2rAAP1rpdGJgzva+xSYwGW7L0No9n71IMeeTxsgn/VDUq2UOOsBXtcq/aL\r\n3gf4Uovc2GefdVjCp1ByLdT+GsISYSyXA7zrk5aTIz7a416gX6O/EyC1qwJD\r\nad9Xqd1MxkLT8j3//cmznAP2wylxkYQeSq8=\r\n=zI0L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.0-alpha.2": {"name": "@vitejs/plugin-react", "version": "3.0.0-alpha.2", "dependencies": {"@babel/core": "^7.20.5", "magic-string": "^0.26.7", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx": "^7.19.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6"}, "devDependencies": {"vite": "workspace:*"}, "peerDependencies": {"vite": "^3.0.0"}, "dist": {"shasum": "9b0701cca54d94b86e0de7ec96f3d33fa560c499", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-VObQvgDSWuEyxsCm/7gnMAzO82v7/JW1Yki6gvgFishqY4TMVHtm+3k7m6ci5zEBI22d+vfXh54mOpVHE+VgKA==", "signatures": [{"sig": "MEUCIQCAgtNf7FGsxdRzYvqYjK44aD7WJl37G4fptAzqTekBoAIgJNof1aa3Kjto9iOg8rO8lm7I5ItmelT5Y7CNi3b8QTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh4y7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP9w//VrVqKUMSOOFcg4LPoRwLv6+3ygqBQrF0lP+9j5AFH6RzTg5V\r\nPyCMIieat6kmhkYKGMwAnfrg6HBJcdNORB+U3DTQsMsz2Q5DxbT7tfhUG8bn\r\nQswwhyuwt57Xji/UuM0JnwAbXWF1HdPYjcO48JZ30hJ+O4fb+FOAQWMsYE9i\r\n0iByOfzseZPSjXvTJaJ300JuEqFBChyx0oeat9hlEhlMTThy7vVr18ny7dnZ\r\nlVtPfTFEu69jaFRz9kbwdYHh8VFFyy8VEdAAPai7FOCYbWT3eX6zdm70Esy8\r\njShMh5Uh3Nv/aN0mOuTUoUjw+BQXb41gPkKylvLobysezKxBbEKOzgx3xZk8\r\nwRyDlNhv4GjNGav38FIqBLmSVJY1P8iXZr2eDtQ1srOhPphRxanV05DdiNb9\r\nguVOR3tcaOCD0nPToU+SuszgPMKbiHVC4fxnF6nilqCsfCOPkMOx7kmp29Zi\r\nUw6sbFVHF2kxaHXQjv8v/GvGy3F+uRIC9c9bhRu0UUcxmmn4UrT0SdzkZhRJ\r\n81GnsuN+tQ/ldtL8fJIgTxEpst1UCcbB2R+aeXL95G8isCKg0n3gM8KzCyA1\r\nlUMUcN8MKwzgZ88aIv3ga3AYqfxEb4WU2kcStII0AUT19XAzp2/0kzkbTTki\r\nE5nkQED/b0LMtOYMqVJdU6Rl1ip1u8CDyKs=\r\n=exXm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.0-beta.0": {"name": "@vitejs/plugin-react", "version": "3.0.0-beta.0", "dependencies": {"@babel/core": "^7.20.5", "magic-string": "^0.27.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.0.0-alpha.0"}, "dist": {"shasum": "ded53b12ffb772ed1029e75664d27292237e78c5", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-t8zyzMbXgrPvNpeKNn5eI/JJekYUVGklbzjmmelCePdlt3unrwZRtlsUWKDaH+8owbY7+I6bIpa4S2lwgkjXZg==", "signatures": [{"sig": "MEUCIHh8x1gfBS+sWAuDXi10VjWKLveX0CEVnhwX1vGlzOlfAiEAojU/BPAfj+bny7uH2wD3vWBr+PieRhL0wyKmL9yNLVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37202, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjcI7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDDA/8C03eb7lTrV4wpyxUeHGEu5a0jl7h7kN7/SSMTF8kTansqFPU\r\ncEBVYwmTafME09mMqecS1FFA0Kcida8rswTu8vTc/tbOUgVJCTnpx36mdxvJ\r\n0JBUfafr/PpL2WNi2BVXqJ8Io/RIHr81DSV04NHDc0xfKODwHuuzsbOj0joW\r\nvRzLSYa+3NiT1P+K4yMaS5T7j9TwTi316lCE9nV5iUfWVEK51j3BhVEl6+oC\r\nEsMKLbdpk6PzRmNfufVlQDGvv3i7ict7jxi48Dw+pvPnhkt0W2qtEb9BmgIq\r\nW+XmdXKPdNlq9aZ12J7Xyg6SkhX+Q3x7gKZZPF45xXUwZwW49Zn3ycKXOEvR\r\n4opnnjTvo488IN/VT7Xclh8U9x+XVqRISsXzn2l23rO8xqDzvUqRxoOYDQ1G\r\nh6ci0MKAsj1FjkbfQx0R0FlB7m4yZxZ8bGqxZ1nItG3gV1iwO7SkhTfdLZFT\r\nZCigcYhHu6+q1+02KBP+IDmyklJjglQBZ8XZiPWXq8hS6sVa68TDQdFqNkA+\r\n68Bab/OB2J56dV+iGh7joBpVetSu8FDDeltUgjjQssnEPZZxwnwKT4O96+uY\r\nrByxeSTMZIxjNsLDU6iNHxzFfUe8Ic7vX53zwoHuvh5FFW4r/aiiuFdtF5/V\r\nrGdTcodQiCXnzxk0oUo7fGE+Vku/SFtseXA=\r\n=0Rdk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.0": {"name": "@vitejs/plugin-react", "version": "3.0.0", "dependencies": {"@babel/core": "^7.20.5", "magic-string": "^0.27.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.0.0"}, "dist": {"shasum": "f36ee1b2ce958dd11ac63fdf746a3b27b0d258ed", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-1mvyPc0xYW5G8CHQvJIJXLoMjl5Ct3q2g5Y2s6Ccfgwm45y48LBvsla7az+GkkAtYikWQ4Lxqcsq5RHLcZgtNQ==", "signatures": [{"sig": "MEYCIQDO05z+hOl1vMoOu6btAXZB0WQgSJxEtxyaIoBFoyE33AIhAIYn6cmwcsV36wbTwkS3R9Ark2kknhGoK0Dlffep+Y4I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkv4YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA7w//RI2dIMcQdYZVsvANYjINomX6FXzABLAp2wxe59fNz67xUzyl\r\nIkUNir9QkPScB9Newmd4sU/+rsCUgzmJsRLvMm5o13kf+iEOAjKb0SY+p0wD\r\niVzQH6RcbSAlsa0iUpzQinaHXiF7a0xTdl6YXjpVCwjJAbsrGwwadTl+y28U\r\nN4l3OOyH3tduqaKfHJWhrGCCKR+kurdLskxHssBCPk+gOJZSVedJT+gSuOoF\r\nkVOBdq+AMVPa+oi1fbsqayUl/3gfOYUplJMnRGW4ywy4dfa2eqWjLyHFsZ3q\r\nUJI2TtXKm9CyOiGJOdmOGJTtigEfdxbwfhvfG2234YaUSupBmjT4qoVh7fRX\r\nR/Xlx1s49RRXc8SPnwDZd1UJWdbg2knWnqik7Cc2pseoSdg9bFx/XjEeFK0L\r\nD9EZFpeDiczEBu3xDlInuTGVGSKNHD0dqlMkyaacYJ8OiRRNK1kVb4JFjKxE\r\nrwgCLhaEFu4B6rfb/11S3qCgAqgBMsL8nCLDhbcvlNxeca+Wm0Z8mYljz+QY\r\nJfshttRbDsKJqEoe/Vko82zgKa/x2g9OltVuVpv+eMduNzYoMQ0tVNc6LHHH\r\ncuto7rtLB+YWNW441NK8jrjl/i6o4cPjXePrll4d/qQ/ZjpqJHFxbjIT+SRC\r\nXDbHTXMDOSRjSv2x07x01mxfhtORgI2iXII=\r\n=YVkg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.0.1": {"name": "@vitejs/plugin-react", "version": "3.0.1", "dependencies": {"@babel/core": "^7.20.7", "magic-string": "^0.27.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.0.0"}, "dist": {"shasum": "ad21fb81377970dd4021a31cd95a03eb6f5c4c48", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-mx+QvYwIbbpOIJw+hypjnW1lAbKDHtWK5ibkF/V1/oMBu8HU/chb+SnqJDAsLq1+7rGqjktCEomMTM5KShzUKQ==", "signatures": [{"sig": "MEUCIQC1+5eqJT+dOtrw8/NZhtiJ8Pj/8Bd3ONZ1nQXnM2v1xAIgURsIU6fiMOiILjWuDiqKpQ3dTClBu16398PxoaOz+3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtp1+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHeg//ebuDDuqEsJvSvS96TEJETYCHcK0KG27dNCHzBOcCws94t0bV\r\n7irZCx+oGoRXXHpsPAh6vsukeFCnLP2J7OEP7XOLG0Cs0aPPGgoClFMCDaej\r\nMCSAlbiKoxAXJmltpLyXZxzWGdNXNYq68DzNnrr530ueF4jfitzYhQTjpyMN\r\nO22RLE8EYGc7QTJv2gK3tHPUvHCOfd0YVqewrU6inYZVhPEM8YEtkaiW0vsT\r\na2GJdvI8uMuwSgr7gX4CtTCW92c6VXkyB5YHDBssoJDBrxaFYRqUrSqVEkrQ\r\nI4Ioq4eqI98RBn768Wh40feOTdVgQ/AFjNsbOIziFi8v3EMef6/UcteAxDOf\r\ns6MUkDr7ZlDnYGDQO5smKe5Gj6634R0OMUReMipfA9mUreAGdCxekE4h1ROM\r\nokNVj4NPWq6kbF47vK7nviTuGpArq4mvrV5R/+ro9XqFSKnfupw28Z7yeiZD\r\nvgcbZMPOEqhvuz6p9nhPvg+zsn2KiXpPz+UKQ/LWns/QkpWBBvij9DVg0msD\r\nLmxBTWhzmcMnHuioFpP1aD3sEW/IYKJ/WmQquVVM3ZuUIQ1Sx+59ap0eZ3ur\r\nU7cTZPjG78ZDJXZqI9t90IgpSYLEs8WOKqnHG2WIo3kDO1mfgNGAT8OzmKFC\r\nsGmL5rZbAnQnZWOuikmxv8YnVs2ouOze8GY=\r\n=lRag\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.1.0-beta.0": {"name": "@vitejs/plugin-react", "version": "3.1.0-beta.0", "dependencies": {"@babel/core": "^7.20.12", "magic-string": "^0.27.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.1.0-beta.0"}, "dist": {"shasum": "0858e583eea2719e7b6ff035253d5bada9ac6d6c", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.1.0-beta.0.tgz", "fileCount": 7, "integrity": "sha512-EjDfiXNv7LkCPYKT1GPxBNOgcCs12s8tJ0B0mlgb1Gsy0D44MF08fcTgu+Tlo4XuhE9ehQL6dyAsiI69YM3rHA==", "signatures": [{"sig": "MEQCIBzaEDQJq5iADCJwG0XyrSw5Q9iywssp7rXVycVLgIznAiAFj0xYvyOtlKa3KMADxJMh8DJypr/gDe6xY7H8vjjwwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0TZ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoihg//SqDnuNaRnM0dsPIJVG6RJZBOV+TzV+DNN/8Poy8RHC0ikVcm\r\n7W/tpO9sVaHXnJmkt/Xsm83+60MBOt3RGwiEaEdiWWuCCJNlDvfzpz4CTY0r\r\nFxd2ZNoe2GvmS0uWBuZ20UN7vqr/sg05qki5/AOpfmNaS9Yt0CPm/5kMCraf\r\nV+0U1MnDIK0QBom8gftjuw1VE+WsBNh0J7fUnNwHZVPWfoVAEEjcGzjIOA7p\r\nneP61PGo5n06jx9U42SaGaDT2u+bPmJfRHvNAhUxfwklOep4vgdQXh0pnAwp\r\nAjmzyo2ZaPpBjnZwV8T6lc1Epygg4iLeGavhufb3P+ZY5GqXHZEMjxkbPi7V\r\nySjcC/oX1avZvQR+JS3tVqattx04Que6L1/fn1AFabQbKttdCHp0A2Gcc6xi\r\nOc8iQS3BvTFHG5si52axXBFQhoejVOfOdH8IydMXRFiiJlBogkB2JWsKZsQZ\r\nF9XK1kkNzau5g3Jfte8y/J9KqYfr+DhGtAE43x5szr+g1rN/TiWBqFJnOkT1\r\n+b8LU9YBJZ5owAysfAUVxHaIJxgTVUsSet3YGr4buFI8rxFyK9TwEVRV2Ic5\r\nEhLqVXhDCJ1G0IovectRocQktZ75Yw5VbWfdX5mWQPD7YUzSZ16U6/Vdb0M/\r\nw/iP24dlYllcyY8oPCEXTkulvjhhUm4/mg8=\r\n=+zqs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "3.1.0": {"name": "@vitejs/plugin-react", "version": "3.1.0", "dependencies": {"@babel/core": "^7.20.12", "magic-string": "^0.27.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.18.6", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.1.0-beta.0"}, "dist": {"shasum": "d1091f535eab8b83d6e74034d01e27d73c773240", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-AfgcRL8ZBhAlc3BFdigClmTUMISmmzHn7sB2h9U1odvc5U/MjWXsAaz18b/WoppUTDBzxOJwo2VdClfUcItu9g==", "signatures": [{"sig": "MEQCIHG5/uTjTjYd2OZcJqTN5Sy7CUVwcRmTqj6WPHiAjX+jAiA81jaxVmERyDP58jxthvDisX1p5ed/9ZyLvl5Ht0mKRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj257jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWYg/9GhlEliRrq91Xq0BgCG9WnYzOmUt+8u3dqJGZ9KzmtMN58Ggh\r\nWYqi6dAvwmtS7KXp5rzqQjn+IXaqSKyW4fN0gIb2aGnWNGAJAzn3fQ6xH0yD\r\n/u02rS/uGcSQR2Vs5Jnv+JVgwz80XdtfZlturgU2nN4rEu2R+gsciKZuXiWj\r\nIYyco+m8itjNm+Ye96eHn6NqUel4bnZUvwEgPnmCLo8dak2GBZP3AJMMWy6D\r\nOX2sCbS3T5qci5FZgAnUqMqHxX9NGR+Z4e4OxSx7jj+50wvuKziNkSKcK18V\r\nB2yjLHsWzhOa+ReT5m/1lAr8EQx/qTNtOIaDCLpFJIwvBv02eYbcmiCLVm64\r\nr7PIyP95NwcoVUM/se6xdzpbKQJxzfz26Ku87txjvHFRsyXHYUAMpUzboRYc\r\nADuaARbZ3Da4JFU1hC6FMo2agrLJO6c0Czoxg4Ybn8gkOuLXL04NAdaj/LPT\r\nL5ThcluSsxwsmsIYZS/uDT3aXYYaTA/5guc1hgX+V8ayDflIGsPXTrglJXPJ\r\niHMCV1f0bDvAwmBV/xscE1N/4GnBnTKbMb1QAnKkyH/NmIF2Tk5xUno2CVRi\r\nPb1lBb0OPACge4sDiCCbz4OOjQuU0zEQPEH0YQo3xasXa3fbqYv82lp1SN2C\r\nOlNtetKAS9dcEM0sCiPlLUMlEyDl08A3jGU=\r\n=ccCZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.0-beta.0": {"name": "@vitejs/plugin-react", "version": "4.0.0-beta.0", "dependencies": {"@babel/core": "^7.21.4", "magic-string": "^0.30.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.21.0", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "d71ff24884382a56b5d04c413a9e7049c98d1b7e", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.0-beta.0.tgz", "fileCount": 7, "integrity": "sha512-ewpr2i8Dobqs3SKCEG+wIB2yPasITsqeZw0Y3yJSwOuUGWTjlX79SYemQ+iYtDz/uZ1OOXSMX5akxYFwBfQRYQ==", "signatures": [{"sig": "MEYCIQC5m8Lvhw5omiGv1SbmxYPEOj+zloVMVYKeIJW/IOW+YAIhAOLfur5fU8535G0H7RkdMWyTHnu3C1tNKP9ZhJMvqHj3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLgfbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmryhw/+OpnoqkXbqZmdWAdl++Bbl7jmNUULT16XLOjrOFQ1lXKPuQU8\r\nUKDN1Z0AEMh1odfRLdz7sgja/hsbRv63iUMZePK30H+Gl4yqkr9Lh/yKP1vU\r\nLrJfmbyCnTYtLn/XTlx9c83ANVmCCNxL3S5bp2yM5OLVwVhC3uy9b7RO9ROq\r\nSvEk1QmCMjomcxvWEDGJ4ycKCzWjGBsJyi9eaUFqqTNKWkdwLaslxBXhAM6h\r\nb1BkL1xsa5uGeI2C0Uq2Jcu3xfDh/QvWAGAO8yLHju3JNIpcQEDRpoNw8Sit\r\niEmH/wwiXVUF0+nCdgMyMw/gGdG5WR61GI1CNxnS4kRkv03H5d8XRFYgO8e4\r\nPdGyUSpuCTMpr5+iBkZyGT20hhBmI6e8rYAdfehvu7sP3oXBdOFEafPsdbwC\r\nEjGiKRzjOBXQLAsO5IxKrCQbAbn/wvW1fK4m6laMAPsHdZCJNOgcul78xr2l\r\nfumI0jwr6zw/hQslkGQiy71ngSaOCDzRMb4UcF9IKgDgnX+IgmL5emvIY8Mo\r\ncdknpf+Mo9axDxruxeBprUlNEVTtLydYE2HJMGXOuxa6ywgY2kfvQqd3CAnP\r\nHCvF+EAah36yQOp8BrwzaIBkE9KHQ+qqhTz2T6xjL3ZSaFQ6BDlPFztc6A+4\r\nCzKlt8itaudCrzhQZozYrqe9lP44+EnYRgk=\r\n=sAbt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.0-beta.1": {"name": "@vitejs/plugin-react", "version": "4.0.0-beta.1", "dependencies": {"@babel/core": "^7.21.4", "magic-string": "^0.30.0", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.21.0", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "a53f888619f621550c4fb2ff9c5d67972cb4ed92", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-gEZYZ2iv8cyx9QRmEijCXrd/9OE+icYcZD5d1ThuqmePtIj2R6wMsxQXcf2CAJWIh4SLy1JfDVt2v+8E2loKmg==", "signatures": [{"sig": "MEQCIHpJ//OL/OUhnEgnGbBuTwNijbScfesIfXf2cTOHgddlAiBGQpmI2fzCUrSqpRLJTnDmrG5053FKAvaquPVw+zWjSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPcHTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4WQ//YFRNM0xAZBD23TBwM/UaN3YWwVG4J+2cc4hp7QExW3CTXug6\r\nH5w5Xn+BZTYaO373xHid+Jr4gptLiBlSK5s/FB8Pk1D11sEK17JACDXjeYuM\r\nuMYBSB7KEUsAnnVSyY8EUckTb+9/ia8adHq1V12Im8kmuShQCUKNr3Hd5Jsb\r\nY3raLUnufWeQv0jKzRQ1IyE9U6EOCb4mcjQoQGU9clJs/kox4myL9wUwNsh4\r\nKlehzhPA23aL1MdjHdo43Gyj1lBMZRMkzYRtw+//X/E3TtcQsQiSCStAQH2a\r\nKKvgf28hqqElnhb6m0bb1FkD1nGHCVB+H0izmUwJrqeNezg13uGVCyqbdHkE\r\nBCsb+Rv1Zehhg2vc6wW+YsA5K+RdKyNTkV/6utWmG0t9J8CFdcOg8NrLt4LJ\r\nYUGw/p8CsddvpBKhjnzZOVGoOppt+3KQn7DTZ9+6IuVYa6OBQyYo+q6mUn9E\r\nb4wL6hvixxU2HanT/3Nu0zSsYTxYmUn+seCmBUqkKpjL1EPS/xM3QEJYkQWN\r\n6KZaCybcp3tW3EgHgrryfOnwc9FGD/Geynrbi/yiKYhEGE1wgUxOrxjdOEPh\r\nHWGJoZZbk4NwjlUrNIswxk0jcDEc776p1VG2YFoD5H+OctOvoxfFC9+OFFVN\r\nFgmbrE8a/ftA17tSNPBHo/Oq//4VOMvTZBk=\r\n=FoBM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.0": {"name": "@vitejs/plugin-react", "version": "4.0.0", "dependencies": {"@babel/core": "^7.21.4", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.21.0", "@babel/plugin-transform-react-jsx-source": "^7.19.6"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "46d1c37c507447d10467be1c111595174555ef28", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-HX0XzMjL3hhOYm+0s95pb0Z7F8O81G7joUHgfDd/9J/ZZf5k4xX6QAMFkKsHFxaHlf6X7GD7+XuaZ66ULiJuhQ==", "signatures": [{"sig": "MEUCIFELEGqAHXQW0ptgL7iAnbaULHe1Z59AMiofvrN0rFyjAiEA4syt7yQ600nYnMAGOo6IVYPxYUY32G5jpduGq5lyPJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQUk0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTOg//aBx81viyj+YH7DYCYjKi72sOdPgjWDqxRHeI/bZDnKc1NrKE\r\nPBioTtiIKPv95W6T3KLYETZXz5faL2h7rrr8xZezlKWxZqr6p/eLXSuiwqqF\r\nbkzft27ePDHJunK79wsF1zq8R8u5pEFbYhf1Oli/kSdjfRO0dudJMCcYTjS0\r\n1cY4g4vOJNLcglrgs8aAw3M74XEkx3xEz9YTQm4/wxA6pM3/ttu7/q462hON\r\nvbL957fzAP6g7lJxdqP9Hq0y9QWV7mtsKGMHe+xRnNqQ/bRK2utMjg/ZkW9Q\r\nFKcHYIGXsxRs/JIwkEXK5G5JZoDUXViZk5DW29f2R50WZdAkjbZ1v2gbapb1\r\n9lg3boqzOna/uU5dB0YKs5UFxAKafXLz/UtggOo4lmcAdszzLW7P5nq1+W9J\r\ncdp53ksOUJs8CEBbj+S47w6e61sGpL++N6tBaa8aNyG1clMFgkbzyjwocpOm\r\n/h0FHo/gPJICTfyIN7NbXztI7o9cREKy6dYzXo4enR1gAFCN5c8Sio2uIwJc\r\nSe5rgqjhQlfOw2jX91lZwvRyXQahHz5LPrG541pdSQj0YwNpS88MDAUaHuI2\r\nLtpBKVFc39PgDBnf6UTP5pVHkZXK7pwyMFhRJmukTk/GkbbIxUsCKHRMTlMb\r\nrbyH6a/euV/OgXRXsozAM7k6QKRJ+qjZer0=\r\n=vMRO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.1": {"name": "@vitejs/plugin-react", "version": "4.0.1", "dependencies": {"@babel/core": "^7.22.5", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "793aa790633433558da7ac0a38c58ddf47dff518", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.1.tgz", "fileCount": 7, "integrity": "sha512-g25lL98essfeSj43HJ0o4DMp0325XK0ITkxpgChzJU/CyemgyChtlxfnRbjfwxDGCTRxTiXtQAsdebQXKMRSOA==", "signatures": [{"sig": "MEUCIA65SbxbCvwcXPEQYwZmHHb5EkPwFRXIRr+pWdYkdOcRAiEAietywf2S+Mfk+/7U8wz/SEowp4Iedj/NfVYGkXxvQH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29678}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.2": {"name": "@vitejs/plugin-react", "version": "4.0.2", "dependencies": {"@babel/core": "^7.22.5", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "cd25adc113c4c6f504b2e32e28230d399bfba334", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.2.tgz", "fileCount": 7, "integrity": "sha512-zbnVp3Esfg33zDaoLrjxG+p/dPiOtpvJA+1oOEQwSxMMTRL9zi1eghIcd2WtLjkcKnPsa3S15LzS/OzDn2BOCA==", "signatures": [{"sig": "MEUCIFuahkbXwQJirtvVKZ8sYjgSwNm+8HxwlpVKd9AEmUkWAiEA/kcRtexeGno4SPO+ZST4gl4QVp7tArsOC8neQ2odoLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29678}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.3": {"name": "@vitejs/plugin-react", "version": "4.0.3", "dependencies": {"@babel/core": "^7.22.5", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "007d27ad5ef1eac4bf8c29e168ba9be2203c371b", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.3.tgz", "fileCount": 7, "integrity": "sha512-pwXDog5nwwvSIzwrvYYmA2Ljcd/ZNlcsSG2Q9CNDBwnsd55UGAyr2doXtB5j+2uymRCnCfExlznzzSFbBRcoCg==", "signatures": [{"sig": "MEUCIE1UpYWKvBQhnsxpJd89J4eyZUfKaS9GDfRHgoji/zT/AiEA+aGNRTWbKcc2YhHpzMEvegTL2NjB98RjE7Q+X0RcNls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29420}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.0.4": {"name": "@vitejs/plugin-react", "version": "4.0.4", "dependencies": {"@babel/core": "^7.22.9", "react-refresh": "^0.14.0", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "31c3f779dc534e045c4b134e7cf7b150af0a7646", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.0.4.tgz", "fileCount": 7, "integrity": "sha512-7wU921ABnNYkETiMaZy7XqpueMnpu5VxvVps13MjmCo+utBdD79sZzrApHawHtVX66cCJQQTXFcjH0y9dSUK8g==", "signatures": [{"sig": "MEYCIQDGE+CPl0BK5q2VZRqX0nqPi9SYXD3Zdn4YaSVwbI4d7QIhAPfD6178JPgWBT+Q24DyzYTEJS5m/eLzHG5Zqzf8UfvB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29484}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.1.0": {"name": "@vitejs/plugin-react", "version": "4.1.0", "dependencies": {"@babel/core": "^7.22.20", "react-refresh": "^0.14.0", "@types/babel__core": "^7.20.2", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "e4f56f46fd737c5d386bb1f1ade86ba275fe09bd", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-rM0SqazU9iqPUraQ2JlIvReeaxOoRj6n+PzB1C0cBzIbd8qP336nC39/R9yPi3wVcah7E7j/kdU1uCUqMEU4OQ==", "signatures": [{"sig": "MEUCIEuFWbGWxeDKStN0dNpR/x6lCctWmmjc+o0RRNu7DoZ3AiEAuoqYxnvbd11rKnsyQG+7cQklugyEjALAtq3TLCqVdII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33869}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.1.1": {"name": "@vitejs/plugin-react", "version": "4.1.1", "dependencies": {"@babel/core": "^7.23.2", "react-refresh": "^0.14.0", "@types/babel__core": "^7.20.3", "@babel/plugin-transform-react-jsx-self": "^7.22.5", "@babel/plugin-transform-react-jsx-source": "^7.22.5"}, "peerDependencies": {"vite": "^4.2.0"}, "dist": {"shasum": "a10254dc76778027407d01b6ddbca53b23852a72", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.1.1.tgz", "fileCount": 9, "integrity": "sha512-Jie2HER<PERSON>+uh27e+ORXXwEP5h0Y2lS9T2PRGbfebiHGlwzDO0dEnd2aNtOR/qjBlPb1YgxwAONeblL1xqLikLag==", "signatures": [{"sig": "MEYCIQDtbhOkhHzBRk2z2A0PyQZZjVwafR2CHBW7pXYZ5osjsAIhAP18vAzpHICPMiS7qyqK8GU4+4ylWVD1x+3SYDxrMfkJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34166}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.2.0": {"name": "@vitejs/plugin-react", "version": "4.2.0", "dependencies": {"@babel/core": "^7.23.3", "react-refresh": "^0.14.0", "@types/babel__core": "^7.20.4", "@babel/plugin-transform-react-jsx-self": "^7.23.3", "@babel/plugin-transform-react-jsx-source": "^7.23.3"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "d71352b1a443c09c7aae8f278dd071ab3d9d8490", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.2.0.tgz", "fileCount": 9, "integrity": "sha512-+MHTH/e6H12kRp5HUkzOGqPMksezRMmW+TNzlh/QXfI8rRf6l2Z2yH/v12no1UvTwhZgEDMuQ7g7rrfMseU6FQ==", "signatures": [{"sig": "MEQCIHjw/1nw/XBB7sRERiuLe90ECOskBWHeQIeEigRQCvhxAiArX+ocykY7Rvww24rG4HePDIKBX2HPhzjMPBXDC5wLjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34282}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.2.1": {"name": "@vitejs/plugin-react", "version": "4.2.1", "dependencies": {"@babel/core": "^7.23.5", "react-refresh": "^0.14.0", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.23.3", "@babel/plugin-transform-react-jsx-source": "^7.23.3"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "744d8e4fcb120fc3dbaa471dadd3483f5a304bb9", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-oojO9IDc4nCUUi8qIR11KoQm0XFFLIwsRBwHRR4d/88IWghn1y6ckz/bJ8GHDCsYEJee8mDzqtJxh15/cisJNQ==", "signatures": [{"sig": "MEQCIDr6iGP/i4O/MfdLHoC1ZhipoAFRIXLsJHRXa9qEKzJXAiAz9iKG8U92c7X2bhM7AKiNOKhQweuPHpVXVcNod2RQZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34108}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.3.0": {"name": "@vitejs/plugin-react", "version": "4.3.0", "dependencies": {"@babel/core": "^7.24.5", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.24.5", "@babel/plugin-transform-react-jsx-source": "^7.24.1"}, "devDependencies": {"unbuild": "^2.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "f20ec2369a92d8abaaefa60da8b7157819d20481", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.0.tgz", "fileCount": 9, "integrity": "sha512-KcEbMsn4Dpk+LIbHMj7gDPRKaTMStxxWRkRmxsg/jVdFdJCZWt1SchZcf0M4t8lIKdwwMsEyzhrcOXRrDPtOBw==", "signatures": [{"sig": "MEYCIQC1G4rkhVPYTwEwJ5T1ohVwxOcmeb3fdhedrmleVkiwtAIhAIT+v47y1CzaYYae37N8UmxdrlstNWQIOjkGriGGDhPj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36030}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.3.1": {"name": "@vitejs/plugin-react", "version": "4.3.1", "dependencies": {"@babel/core": "^7.24.5", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.24.5", "@babel/plugin-transform-react-jsx-source": "^7.24.1"}, "devDependencies": {"unbuild": "^2.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "d0be6594051ded8957df555ff07a991fb618b48e", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.1.tgz", "fileCount": 9, "integrity": "sha512-m/V2syj5CuVnaxcUJOQRel/Wr31FFXRFlnOoq1TVtkCxsY5veGMTEmpWHndrhB2U8ScHtCQB1e+4hWYExQc6Lg==", "signatures": [{"sig": "MEYCIQChcIZbG9JKVuUnW4LYD18lLmK9O8UegO8K6RHnzumyWQIhAODyftU5/GYMFhuD1A2gyKy5OY8IY0ZlD8XpbkRl+7hv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36512}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.3.2": {"name": "@vitejs/plugin-react", "version": "4.3.2", "dependencies": {"@babel/core": "^7.25.2", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.24.7", "@babel/plugin-transform-react-jsx-source": "^7.24.7"}, "devDependencies": {"unbuild": "^2.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "1e13f666fe3135b477220d3c13b783704636b6e4", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.2.tgz", "fileCount": 9, "integrity": "sha512-hieu+o05v4glEBucTcKMK3dlES0OeJlD9YVOAPraVMOInBCwzumaIFiUjr4bHK7NPgnAHgiskUoceKercrN8vg==", "signatures": [{"sig": "MEUCIQCLEo90WItQXeXeFbWzp0tg8OC9s+Jrm4fJZhseUSuHKgIgIQGi7VFASxc+rxo5nPCAkAd6dL91yZhqfxt00DokFYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37285}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.3.3": {"name": "@vitejs/plugin-react", "version": "4.3.3", "dependencies": {"@babel/core": "^7.25.2", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.24.7", "@babel/plugin-transform-react-jsx-source": "^7.24.7"}, "devDependencies": {"unbuild": "^2.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0"}, "dist": {"shasum": "28301ac6d7aaf20b73a418ee5c65b05519b4836c", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.3.tgz", "fileCount": 9, "integrity": "sha512-NooDe9GpHGqNns1i8XDERg0Vsg5SSYRhRxxyTGogUdkdNt47jal+fbuYi+Yfq6pzRCKXyoPcWisfxE6RIM3GKA==", "signatures": [{"sig": "MEQCIC3ZRm+FgteYtfKRO/3hDdHmC64Si8iooOe0rvFs/wVhAiA/CQOBalMU8sKiXL4CWZPesDWhDMGuy8zQMOaMJwKkkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38013}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.3.4": {"name": "@vitejs/plugin-react", "version": "4.3.4", "dependencies": {"@babel/core": "^7.26.0", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^2.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "c64be10b54c4640135a5b28a2432330e88ad7c20", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.3.4.tgz", "fileCount": 9, "integrity": "sha512-SCCPBJtYLdE8PX/7ZQAs1QAZ8Jqwih+0VBLum1EGqmCCQal+MIUqLCzj3ZUy8ufbC0cAM4LRlSTm7IQJwWT4ug==", "signatures": [{"sig": "MEUCIQCHQWSfKpFuMiNmtTV3kY5AaecEYl8XZKsCn+m6kS+1lAIgTQjUI/FOSz8Wsk9oiEzgaqxt23R6oH2Tax0Y7XR5a3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38259}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.4.0-beta.1": {"name": "@vitejs/plugin-react", "version": "4.4.0-beta.1", "dependencies": {"@babel/core": "^7.26.0", "react-refresh": "^0.14.2", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "96f273eb38f8aeff5ccdc4f5306ef9650515440d", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.4.0-beta.1.tgz", "fileCount": 9, "integrity": "sha512-hji9QKz1xheqJ7vO6pmDFxtBXo7atK9zfnYqmtpQqJVjY0RIWFv0dSwK02HcsWiYSC41vjsmfqyF8UQ/sip/jg==", "signatures": [{"sig": "MEUCIAhKQWzs+eo5MqsL/NzWlHiTjGQ3W/pBGrpLz0CaNJWaAiEApYKe+XlIsxZlOM9xFO1Yfqy3BgzsCerO9nhJS1SOQh0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.4.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 56173}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.4.0-beta.2": {"name": "@vitejs/plugin-react", "version": "4.4.0-beta.2", "dependencies": {"@babel/core": "^7.26.10", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "19635749daab3f649b11d644524fae4793d2b2b4", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.4.0-beta.2.tgz", "fileCount": 9, "integrity": "sha512-NL6dWF6LbbpbwSB2IvqbxmoqXd6j9Yi0+AL5GK6SWH15fqoK3wUXvdHbnRLFhc7xBIf1tDNywZAyGEG9hJTB/A==", "signatures": [{"sig": "MEYCIQDkPhW8IadCh8Wxr4xXhH7PtREJbNN65I4XOII7/wLmMAIhAKGO+02YqsaQvT4QjscfoQ11nKCohwSmZVqvor882z7j", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.4.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 57882}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.4.0": {"name": "@vitejs/plugin-react", "version": "4.4.0", "dependencies": {"@babel/core": "^7.26.10", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "a658e563d08f3907dbceb3be1bca5272665e6372", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.4.0.tgz", "fileCount": 9, "integrity": "sha512-x/EztcTKVj+TDeANY1WjNeYsvZjZdfWRMP/KXi5Yn8BoTzpa13ZltaQqKfvWYbX8CE10GOHHdC5v86jY9x8i/g==", "signatures": [{"sig": "MEYCIQCciDM/PEg8fCEYj0ojjwsqx9nwTb3oLocZGVSuBWU6CwIhANGdQvMwQeUkcv+mAUg0KsBiyJ8ET/Ii4WM3e6/bz92d", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58401}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.4.1": {"name": "@vitejs/plugin-react", "version": "4.4.1", "dependencies": {"@babel/core": "^7.26.10", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "d7d1e9c9616d7536b0953637edfee7c6cbe2fe0f", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.4.1.tgz", "fileCount": 9, "integrity": "sha512-IpEm5ZmeXAP/osiBXVVP5KjFMzbWOonMs0NaQQl+xYnUAcq4oHUBsF2+p4MgKWG4YMmFYJU8A6sxRPuowllm6w==", "signatures": [{"sig": "MEYCIQCTH/ncPBlxy9buJc++EhDYYjiO69fXXMnqhEwUCPEqEQIhAM8LLMSU0xm2dSegOck77mX6Eeh3cApVbRMMSdzlYXN0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58402}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.5.0": {"name": "@vitejs/plugin-react", "version": "4.5.0", "dependencies": {"@babel/core": "^7.26.10", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@rolldown/pluginutils": "1.0.0-beta.9", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "ef2bad6be3031af2b2105b7ab2754f710e890a32", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.5.0.tgz", "fileCount": 9, "integrity": "sha512-JuLWaEqypaJmOJPLWwO335Ig6jSgC1FTONCWAxnqcQthLTK/Yc9aH6hr9z/87xciejbQcnP3GnA1FWUSWeXaeg==", "signatures": [{"sig": "MEQCIBOqROp/5NwkuCXrW8h0/D98yaWje0V7vI25JeGc1Z4hAiAt+V7Nw1QozfdBvklthnK+4SDXZbxMuAggWnHJ+jp5eQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 61550}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.5.1": {"name": "@vitejs/plugin-react", "version": "4.5.1", "dependencies": {"@babel/core": "^7.26.10", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@rolldown/pluginutils": "1.0.0-beta.9", "@babel/plugin-transform-react-jsx-self": "^7.25.9", "@babel/plugin-transform-react-jsx-source": "^7.25.9"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0"}, "dist": {"shasum": "19432712467ad3b81f24c85d695a6febf8d4cc11", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.5.1.tgz", "fileCount": 9, "integrity": "sha512-uPZBqSI0YD4lpkIru6M35sIfylLGTyhGHvDZbNLuMA73lMlwJKz5xweH7FajfcCAc2HnINciejA9qTz0dr0M7A==", "signatures": [{"sig": "MEUCIQCCXPHdA5UPeofpdw10i3LjIhFs31sB/QQmowYJuKjEgQIgajc+s+bx8iRwvxQ6crQjsxbNaXMLEP0vvBpVGaL9NEk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 61552}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.5.2": {"name": "@vitejs/plugin-react", "version": "4.5.2", "dependencies": {"@babel/core": "^7.27.4", "react-refresh": "^0.17.0", "@types/babel__core": "^7.20.5", "@rolldown/pluginutils": "1.0.0-beta.11", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1"}, "devDependencies": {"unbuild": "^3.5.0", "@vitejs/react-common": "workspace:*"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}, "dist": {"shasum": "8b98a8fbcefff4aa4c946966fbec560dc66d2bd9", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.5.2.tgz", "fileCount": 9, "integrity": "sha512-QNVT3/Lxx99nMQWJWF7K4N6apUEuT0KlZA3mx/mVaoGj3smm/8rc8ezz15J1pcbcjDK0V15rpHetVfya08r76Q==", "signatures": [{"sig": "MEYCIQD6xUDccViFU26+Y7A1ckrvOa1tVdMh7Lv1yURqjfCf8gIhAIGOeK8acBw70uYydeJ/Gfa9NlD+kZ9bVmOIl9OzvfMo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 62948}, "engines": {"node": "^14.18.0 || >=16.0.0"}}, "4.6.0": {"name": "@vitejs/plugin-react", "version": "4.6.0", "dependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.19", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "devDependencies": {"@vitejs/react-common": "workspace:*", "babel-plugin-react-compiler": "19.1.0-rc.2", "react": "^19.1.0", "react-dom": "^19.1.0", "rolldown": "1.0.0-beta.19", "unbuild": "^3.5.0", "vitest": "^3.2.4"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}, "dist": {"integrity": "sha512-5Kgff+m8e2PB+9j51eGHEpn5kUzRKH2Ry0qGoe8ItJg7pqnkPrYPkDQZGgGmTa0EGarHrkjLvOdU3b1fzI8otQ==", "shasum": "2707b485f44806d42d41c63921883cff9c54dfaa", "tarball": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz", "fileCount": 9, "unpackedSize": 63656, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-react@4.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCZFAuTBwXl0q8+6rsiI2flwNkhktxFhcNtN1bVknTDVQIhANLobXY9nswvgP4VJs+bdFHol3VzEhymVoQW7TGlhH4r"}]}, "engines": {"node": "^14.18.0 || >=16.0.0"}}}, "modified": "2025-06-23T03:29:16.622Z"}