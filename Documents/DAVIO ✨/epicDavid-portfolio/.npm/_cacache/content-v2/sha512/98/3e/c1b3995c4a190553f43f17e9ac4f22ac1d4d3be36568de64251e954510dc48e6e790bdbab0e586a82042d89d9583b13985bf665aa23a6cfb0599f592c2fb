{"_id": "abab", "_rev": "18-04f86c409e580d830435b66cbfbca865", "name": "abab", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "dist-tags": {"latest": "2.0.6"}, "versions": {"1.0.0": {"name": "abab", "version": "1.0.0", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "581865c4fe8c15c26aef3c01285bc184132bb72a", "_id": "abab@1.0.0", "_shasum": "744a3c2624484fa5c2393752053580aedec71b2d", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "744a3c2624484fa5c2393752053580aedec71b2d", "tarball": "https://registry.npmjs.org/abab/-/abab-1.0.0.tgz", "integrity": "sha512-Qxv+7HL8Im5hQpfx/SMTQlNoGfeKFgeh58JZGxUPhamFuiEDEW/3gAM3GTkldvl8nSRennIEhMS920/gqaEeXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWO6Z+iywE9rXeD2LK/LDMuZprQIoSZT3wH/MxwJcNGAIgVye3dWkHBxGKPTgXp4tL6NQh/1SAJawpEY/p43QVUw8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "1.0.1": {"name": "abab", "version": "1.0.1", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "jscs . && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"babel-core": "^6.1.4", "babel-loader": "^6.1.0", "babel-preset-es2015": "^6.1.4", "eslint": "^1.3.1", "jscs": "^2.1.1", "karma": "^0.13.10", "karma-cli": "^0.1.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-sauce-launcher": "^0.2.14", "karma-webpack": "^1.7.0", "mocha": "^2.2.5", "webpack": "^1.12.2"}, "gitHead": "7deb5d159e5cf31399522778beb01ef81b4cb655", "_id": "abab@1.0.1", "_shasum": "44e1867bb948633aa9b4c45ada44f0bb2dcce392", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "44e1867bb948633aa9b4c45ada44f0bb2dcce392", "tarball": "https://registry.npmjs.org/abab/-/abab-1.0.1.tgz", "integrity": "sha512-BpfFOfdIQSz60FrPdzYusXQw7uw7m/ZiSXpdF1+vjqoAq8O/IJKyIox/FnjG9PdkZvI46uSo6xr2ocKdr2r9kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEoRXMbUTNBo4gMPeFI2uXHh3pQqw+TYrrSM5O3chO/eAiEAi/skmCsSlsuE61VOQ8yA8eCxQJQQ31xmZThGvplq69U="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "1.0.2": {"name": "abab", "version": "1.0.2", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "jscs . && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"babel-core": "^6.1.4", "babel-loader": "^6.1.0", "babel-preset-es2015": "^6.1.4", "eslint": "^1.3.1", "jscs": "^2.1.1", "karma": "^0.13.10", "karma-cli": "^0.1.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-sauce-launcher": "^0.2.14", "karma-webpack": "^1.7.0", "mocha": "^2.2.5", "webpack": "^1.12.2"}, "gitHead": "0797773407fc683fbb4d8dfd14347060ee02a250", "_id": "abab@1.0.2", "_shasum": "f0413a3ff7aee92e73f90f7298577457fdcb0f32", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f0413a3ff7aee92e73f90f7298577457fdcb0f32", "tarball": "https://registry.npmjs.org/abab/-/abab-1.0.2.tgz", "integrity": "sha512-MzDjoraGQt8MIcdfLw4RgTY4dAyH8hkIle1Yk4KriziOrkC34IjG7RPy/bneiZ9RAfUlfQWEkmAgzSPkfWNahw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDV6W24/d1o1Eme4yOQJdYX2BCEr6ekrgYl3QM8wYE90AiEAtr1KETY6IojzwbJHCYbAvoK11C6xRS9LFyNsJ6H7z0M="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "1.0.3": {"name": "abab", "version": "1.0.3", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "jscs . && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"babel-core": "^6.1.4", "babel-loader": "^6.1.0", "babel-preset-es2015": "^6.1.4", "eslint": "^1.3.1", "jscs": "^2.1.1", "karma": "^0.13.10", "karma-cli": "^0.1.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-sauce-launcher": "^0.2.14", "karma-webpack": "^1.7.0", "mocha": "^2.2.5", "webpack": "^1.12.2"}, "gitHead": "223c06f29e0e4d4f3bc11164f762898474158c3a", "_id": "abab@1.0.3", "_shasum": "b81de5f7274ec4e756d797cd834f303642724e5d", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b81de5f7274ec4e756d797cd834f303642724e5d", "tarball": "https://registry.npmjs.org/abab/-/abab-1.0.3.tgz", "integrity": "sha512-Nr2I36rULSLYUzwP7ssOYfvNKy38ud5CAUZCyPIYNArPdJkVzk0wI8VkB6vLsHSFZXoOM+Bp94iMNrdhGU+ZMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEpL6kFOMy6rFVNd0lkiv2nnh05BxTumwZ37hsDSAHUvAiACFXMtTqAfvYiexcu7VKy4yj5A3DUAhpRP9O19ljU0MQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "1.0.4": {"name": "abab", "version": "1.0.4", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "jscs . && eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"babel-core": "^6.1.4", "babel-loader": "^6.1.0", "babel-preset-es2015": "^6.1.4", "eslint": "^1.3.1", "jscs": "^2.1.1", "karma": "^0.13.10", "karma-cli": "^0.1.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-sauce-launcher": "^0.2.14", "karma-webpack": "^1.7.0", "mocha": "^2.2.5", "webpack": "^1.12.2"}, "gitHead": "c98068b06b4321949a8195408360ca84140d795d", "_id": "abab@1.0.4", "_shasum": "5faad9c2c07f60dd76770f71cf025b62a63cfd4e", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5faad9c2c07f60dd76770f71cf025b62a63cfd4e", "tarball": "https://registry.npmjs.org/abab/-/abab-1.0.4.tgz", "integrity": "sha512-I+Wi+qiE2kUXyrRhNsWv6XsjUTBJjSoVSctKNBfLG5zG/Xe7Rjbxf13+vqYHNTwHaFU+FtSlVxOCTiMEVtPv0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOEI0azZ18fGfBsezBA7nBbKwbJpqiH9iRgJ1Jvssx2gIgYXwmYKQwvIFm/AfpmSRCecInde8OOiZewyqCQ4GQOfY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab-1.0.4.tgz_1505957653831_0.4382179556414485"}, "directories": {}, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.0": {"name": "abab", "version": "2.0.0", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE.md", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "17bfbfa3cf0dcffc06af34f494080feadc20543d", "_id": "abab@2.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sY5AXXVZv4Y1VACTtR11UJCPHHudgY5i26Qj5TypE6DKlIApbwb5uqhXcJ5UUGbvZNRh7EeIoW+LrJumBsKp7w==", "shasum": "aba0ab4c5eee2d4c79d3487d85450fb2376ebb0f", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.0.tgz", "fileCount": 7, "unpackedSize": 10842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2lJ9CRA9TVsSAnZWagAAmosQAJQWewODKUWIFjrtpwYQ\nv3nH7836AR3NjDtxULn0EaKjyew82jm1blPxB1kQRnQvNuEBcCWVhEVn1g81\neVLjVKHrHAZVLSFaHfC1WmlCbFzGDDwQwXUXblDsZX4TcT/wUgkc0Zbkryey\nQ9KmIFRCIyFcT9SHxi3yVHr/K9mtouQoiMaUle5tvlVJR6ucqBZIQ/yvGajh\nGA901VoxGPkLIYMZ3V+apOwjbR9Ddh/tDy4gmPK+UZ/xp3tmu2qpcftvK30r\nhdfbjmqdj8hI88JhOpP+lBr9SuVpvCfRCLXvDSBn435DM3SCXcIZZrpnCvce\n93BH1Y0pMpLCfVnp/doCTCr4PeblvzzvwDfrYmRENIGLMYgKLaIatmcTBLm4\ntj8xlM1FUQRpPx5hIYUY+jFKjEBllmXqDtJUcTeVDQyIjA28D4qmdgOMxyLJ\nf5vQ3VQ3tGmTDoCdYkuT5UumCeKEFWdU2rBrwl1QLreOYQgmekR3nCQhnf3p\nkc1apX8qNwjyqjMmr+o4KpPS80DVP1F3kVIBNvFp70zhMMvVdSGsIP84yaAp\nveyOLCYK4vpFmg9XUqKKP60V4hj5XpU1j89QrGkonFSDxx188I3Z722zs8mb\n2BYNPKlM//ZUCxlSAcsqmIUrnL6I1i8ZeAjophNMm+wVMQrQVDctml/75nXe\nyMIs\r\n=P/l5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhlZjSb+PyDtH2/L7srOsdJ2woMWyM3QFbmiOP22nLuAiEA04JkpXkOC6PulurAGmjn12L5ishXysWwumsrmENY5yY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.0_1524257404375_0.18873675120103783"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.1": {"name": "abab", "version": "2.0.1", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE.md", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "fe373a7a8bcc241bb5dbadf17d52ff837faf0036", "_id": "abab@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1zSbbCuoIjafKZ3mblY5ikvAb0ODUbqBnFuUb7f6uLeQhhGJ0vEV4ntmtxKLT2WgXCO94E07BjunsIw1jOMPZw==", "shasum": "3fa17797032b71410ec372e11668f4b4ffc86a82", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.1.tgz", "fileCount": 8, "unpackedSize": 11109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZwX1CRA9TVsSAnZWagAA1bsQAIP4+sjfHRbLQJDt4t3D\nZlFya7qqx/bmwJLDXps7ncO9xWPMMIx90IVg8tGBZ4t/XZnVV6QftrSPWBF2\nnfT1l79ycXK6jNLfq0aNnxr2nWz0mEDh5jUPgwgxA+rFKUMpzq0yx+PJrdfn\nm7QZijFKi0b1jdVgqQROr6EhMHOXGjEwNxU8JsEsHuAZcbA3q0hjzcqx3Ase\n6MpJDWwYOuuXNwM+oS7B+0TIHbaFDzLH6jEnR3/g4/YuaxHE41FXdVcRsQI0\nEzShKNMPxfBgqf4SEGXtwx3Avq108VD2bW9F8GE1Z189gl9xPbwKAvXmk+iT\nvlAX0fAgKDAaBKxt/wGSEBAiGnJcnO3euClOvdhpupCKyzUr88dB2KWaf4H9\nvESB5Quz6Kg4VSM2/90cwfH9Np+DpBP/iexZ/90+bR7kGpEvlP7+el3uCqvm\nOM7Z8YdzwRJB1ybhhpeFbs8kUQGsx6dG6aw5YLe6l/JRiNiYpxlcKp6Fp0mE\nA8+IPRyVzmVp2aeLG51GIq/bFRWX1V3NHgpXh2IcYIztzohXAWnkZbANZobp\nkq14hAiEik7cA1/AAX7B1s5CWatuLPca6aor9ROCDI2+W44FMH4yDmPiVbGY\n1PR76hMW3wRFG/dG3Xo6XBB+98fMkHmJq5zqyCQlbiewtbI2+zfc2HYTxOu3\n08P6\r\n=4t6k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKX6Ppio2n2suZuDPEA06axjcdEPilKhD+At6c0OgH5QIgaLM4PrP4KC5qMwO+DaXLu7y0wdFHSzIBL/aexm8+dc0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.1_1567032821013_0.8430686163930352"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.2": {"name": "abab", "version": "2.0.2", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "4123c6002cbc94fa8454c938c7ea32161ad201a9", "_id": "abab@2.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2scffjvioEmNz0OyDSLGWDfKCVwaKc6l9Pm9kOIREU13ClXZvHpg/nRL5xyjSSSLhOnXqft2HpsAzNEEA8cFFg==", "shasum": "a2fba1b122c69a85caa02d10f9270c7219709a9d", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.2.tgz", "fileCount": 8, "unpackedSize": 11153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdip3ICRA9TVsSAnZWagAA2EcP/1PegcN7N2O8YF3S9uOw\nouZDR0LCk8kq2J2mU++hJZiNJQKW2DQhfp6ec+qC6ecCpQfr7lcgf9J+12ho\n4FUjQ2QYQt/ZHOkfT969vdFvY+5R8ly17qquGS1fDC+O0r6gAyJZea1PNJva\nHRa0w9466aJ82hHPyBmpJelShCScWmJpoFWbR4DlyZEgiN9Y8c5GvAnFKo1w\n0N7bllzD2pMRr9CSsXbJPGY2oAcHLA0/YDN544eJw7fRDa0hS/x+RWKKP3Su\nWjTpJ3PUjvfRONLywPY0biVVgZXqDqn93SuP6wQXUUZCDRmuPXjlKk2QYNBg\nsBYI94aGwKxH7ICBAb2dqpGNvGv3hSnaDkkBKk1yYXi8/6j+cJv1DRnU2Fqg\n9yJpA666YxIe/FxYVKIo1TMSrk8uPX18co3suwy1kQCiTJgnNDm6CRTe997/\n9PrMCuTd/1oCpTO5Cl0oZNsA6RbySqExkA405+BUFJaPiB6QCWifGknGE4tT\nKwtgfF2zVIUUVRJkLaOXD5Fw3JLu4O6p+2X8KJjAPG5JtWSM+uFejzEoemZy\ndANeaLxDP0e/jT1GREOkYZvGikFZAx+ux9WMbvq3Q6nVhSDyQbVJVL82Qu4A\n0YZBPJ4z1LPe9diEYxph5Mk5GcZdRFnWkXMhsNRSlJFcLFAXJvCxEfsV+PX7\nNJO/\r\n=6tjf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEW5xax65TAL2NTsJZ5XAXKjPmy7gvLT4Z/OScpo4x5vAiEAy1Jpbbos0spYXXvr86Iumks2M6fwz8NcI0tzYw4lhxI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.2_1569365447695_0.08806393990755024"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.3": {"name": "abab", "version": "2.0.3", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "b435e96d7e3714502f59c932370dbca22f0e6b2c", "_id": "abab@2.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tsFzPpcttalNjFBCFMqsKYQcWxxen1pgJR56by//QwvJc4/OUS3kPOOttx2tSIfjsylB0pYu7f5D3K1RCxUnUg==", "shasum": "623e2075e02eb2d3f2475e49f99c91846467907a", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.3.tgz", "fileCount": 8, "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy137CRA9TVsSAnZWagAAOZ8P/3YiX/pvDQsk2fYE0JP7\nL2C6Nta4U2AAt3Pj36gyNGcSSSDKGTjyjoCma32N0pcXhOHou8DKnwRukNZR\n5ngF9DxWQhBQCHISDagpsA+Nc3d5Fj4wSR9uAC7ERKv8kUr8ysyTDt8Cy4vz\nhqRK0LQOR96Yd09icjsBOqc6ocfAaVej8TIyKfa4pTgdJqDZK+ABZPTgN0qG\nZwGlpabaT23RBBeAZkDaVvqdVPTfcBuMHbfB8JgkDuDXae3rTh7NmnrFdLZF\nMg00wlRxrKykBEy/0nuGo6mPaY6ocQ3xJZfHSPw5uFfWK1CJRysNdIAixbk9\nzuCCj3ujJw4vkHbWm2BIzg70yWdEur2nT+LHLSHJUIq1bH7qekQ1DJ5I8sfe\nTdPHNE9lHuWD4PQQ062JCzv72+7khsIF2g0VbKLlfgxSS4H76ldxSwu2JO4+\nfmCZCSUZk4BlWQu+pHwhGr2KXbn5O3UF06C/xmm624XIbFHgyz5RZw6d80M1\n4WpirYU9YBMb3wiANVqawqLvy/GhJbNkTiUB/zGhmSRz1zvjlB2PhFpqCSxC\nvygZOHfNbdcjSujf9hmPW+5rxtywSljEZzSXgQm6V4vKH24kjTDrJatMjHkV\ndBgdor25V+VqhyBlV8KmOpdpddFQIouDrhUAkm2WJOyE/2bUcKAo9H3eFGBO\n4MrG\r\n=gyU5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDE0vveTyGUMtchTGS4hWwc86hDxtU8nnfss/8fjVvebwIgZ1dXkpRkN0dsZ8Hk1GYToxtWK+MCIWgWDDjGXiTim0E="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.3_1573608955095_0.963108963298025"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.4": {"name": "abab", "version": "2.0.4", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "80a801d748c0c35a0629e22fd7818716255f6065", "_id": "abab@2.0.4", "_nodeVersion": "10.19.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Eu9ELJWCz/c1e9gTiCY+FceWxcqzjYEbqMgtndnuSqZSUCOL73TWNK2mHfIj4Cw2E/ongOp+JISVNCmovt2KYQ==", "shasum": "6dfa57b417ca06d21b2478f0e638302f99c2405c", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.4.tgz", "fileCount": 8, "unpackedSize": 11263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJPY/CRA9TVsSAnZWagAAXWkP/2mGiIFn3pTEMWVz9BCA\n6Hbv8jyFVBMHNA9Asa3eWk5u82hJXE+olpaeFLbDpVWf+qTzKmtMYDFt8wgE\nopQ6Sbi4UrXyrD63D7kgJFQt45LrGNEBzyRjCARUDEJhE/79LJ0/qbaxcTej\neRoQo6Ss9Nhm50Ntbiyj2BXzg1t6v7B2Hbl5Gb5auR2bz28GlIFrkNEbcFti\nL5nvSnQJTt6glInQj1+RmGa36BDDSJ34FKznOTYb+i7KhdnAHQ5mpSt1lOr8\nEwIcRwCawe5VJ+ZD02o2h51UcROR6WC48PJbrGH7f1Op6JJdKPVXs9oxg4ao\n16wnhYqQUHS0q0h9hXRZDhkPDCXJhrPf0bL2kKz0PnZRI4vmIeghcdL2lNiC\nWxiLmYoQqbXnl+8bXKeQow7YmW6udCGhVW5bMmx1chxInhwXT6suCYdreUiZ\nIcLStJ0epPYt9yjZrZ60LSv2mc2NPVQsLCYUCMHWRG640KrnrvrT+wuLHHu0\nAuq2OmfE5/m3+PGS7pnL7KYuLCm/WFkMchDjyr3cBuJcd/FcBYXA7Do+jhbI\nIEVXoZ4VnpEsKeq/Qd3/ijYyfo18KVbyOQI9Ytq73E2CZxs7s91HYoEaKmfu\nmWDAfqu668TNd1usxppcMMdB5SpRaECuIHGUZtT9FzmGBmDyYfpTglflNvao\nP4pc\r\n=Pkbr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeuiEZ1o4Ntg5F3ScEiVsjnk+aypyBochbSzOXKXfXAAiBm31Ie3VX0zhSqsgz5GPhACzzcnzeU4lfE88FGS7ZX9g=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.4_1596257855104_0.6649911814561462"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.5": {"name": "abab", "version": "2.0.5", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "gitHead": "45e09f4a91ee32c809a45edc6a76ef69c71a6b63", "_id": "abab@2.0.5", "_nodeVersion": "10.19.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q==", "shasum": "c0b678fb32d60fc1219c784d6a826fe385aeb79a", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.5.tgz", "fileCount": 8, "unpackedSize": 11133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfW8vOCRA9TVsSAnZWagAAkgYQAJZcfyLlrEwMzJnTSpGi\ntdzjXzI3jKysI4Y8/06skiQGakO/9wfcJE3N6UKApBdf2z2prRgZggAHHuxi\ndL8U8tEWmhEjNGK00es/jWbqBFpc62g823IolrNlPvhy4LQe/49G3599Uyet\nl+B7oxDb4+PP4r0pPXXngtYFuSVGuGiy44HP24t24IblofzqKcIh9vh9TTJD\nItPD74Fqe9KPLAm+vJzBa6YN6RRjZO8VPNg+BHGoYG208bnoiO1bWXeeivsc\nNilkS7EAop1wetgsvfqOmsHPe9Jz97ljspJ2zwmw6H5eX0KykpWM25YmXfIB\nj/Q+9uwYN9EaXlqKVJcHiQulKHvt116XIwiyXE1vgD5lEJwWRQHRIyLjkgLW\nIXOpilY4bvtL8OuDh/WWtM/wq76Y+hpNFL90BXhzA1ETfWuXGYadnWU5jp0j\nNucimRaX3ZMy0NxKRXFgk21DY3MsgONXtKlOeP8QhZAtv5iVLkgplg9e+y2o\n5L7I0uzZINK56+bYeQWbVzwLUBR28AcBb72Tfu8eOBsbEXv7T0PcRZfC3HF5\nf/byjv+Dy3QZpX36AMptJvnysjuJDCz1uFlifo3n/fPP+NL4rVciY0D8W0Fg\nCDw3Co0OHj3RwSlXVXKCsqTRl6XBY87X/OElN6+Wrq0VvVGYErrFfyhvqhX8\nrM94\r\n=7TRA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDECuq5M7VJT5jj/nlE84phYULkTiJkBfEFtAhUC9K6HAIhAMY8TD21g4/x+3Chci+OB7Z6XeJvIb/KU1VaK8rfRIPT"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.5_1599851469496_0.7216035947127744"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}, "2.0.6": {"name": "abab", "version": "2.0.6", "description": "WHATWG spec-compliant implementations of window.atob and window.btoa.", "main": "index.js", "scripts": {"mocha": "mocha test/node", "karma": "karma start", "test": "npm run lint && npm run mocha && npm run karma", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "keywords": ["atob", "btoa", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "homepage": "https://github.com/jsdom/abab#readme", "devDependencies": {"eslint": "^4.19.1", "karma": "^2.0.0", "karma-cli": "^1.0.1", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-webpack": "^3.0.0", "mocha": "^5.1.0", "webpack": "^4.5.0"}, "types": "./index.d.ts", "gitHead": "063700eb38c7c4c74530bb7505ad41d097adde54", "_id": "abab@2.0.6", "_nodeVersion": "17.4.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==", "shasum": "41b80f2c871d19686216b82309231cfd3cb3d291", "tarball": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "fileCount": 7, "unpackedSize": 10444, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5z8gylyNlNasBZce2GOFuigJ19F4aPM7SeG/rVxR54AiBp/dltTd2lq6RpQi6OfAQQ5oQKn/K1VeHUh1fn2vBqhA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiW4tKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3Aw//fEe0wSl58Y/h8yEM7yCPYYrOboIkJLtTZ4FptLYQauJdwc0b\r\nCwVhE9H46A5l9uVdYkH2qbzdnWNmvuyPIKnr3ABA4MZ0AzIWIS7hBZGImgms\r\nlUPSZ6yV3ATdBLemGQLaY7fKKvGvYiXnBrffqgjvM/qEHGrDbKu4oy1P230a\r\nk9VYZ2/uBHtJnkn9H4w8Q/J5I4SBty/kvSCt6BqdaFxVzCijECn1h0EncsV6\r\nSuvp5BKUimSbpbE9gO2H0w/Tn1l8Uwhw0Zf7XwOC+mmtH10nBa1TLihxhz0+\r\nMGDBTzZTRLS4A6+NNHB8r3ZO2WU/RtV1IUN/PHcj+NBVyLk4IGUGzNNC+tVY\r\nROQN4B8YTvyhk/8SuS/hRqWkUVYBwYUrCII5SG/LM3L1AwPeZH/1RRKb1GOL\r\n82calvHjzcM2vJ5icz93llMj/iroUmowtvznnqDosDF7mshXN4GnCyLw7J6F\r\nRlS8naYm4CGyetM6xJGDUQbmM8BOpSr3rKnrqDZMM53ZrD5yMR4qvXl6IIaE\r\nLKJx2RnIhI3GyKbXkqH7GyAH/1xNDMo7VlF5Xv1vhoNiEGxW688r7QfLCy8k\r\nSYCtWePiMBX7FzHKP94uWkQEOH2jjcjHfeZCFokh1ayoibWSOJ4bxJZHbNY8\r\na+dJg+eGpNAcFHkpECnBBgMF8D18KidARXg=\r\n=a7Jp\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abab_2.0.6_1650166602300_0.20683237636254548"}, "_hasShrinkwrap": false, "deprecated": "Use your platform's native atob() and btoa() methods instead"}}, "readme": "# abab [![npm version](https://badge.fury.io/js/abab.svg)](https://www.npmjs.com/package/abab) [![Build Status](https://travis-ci.org/jsdom/abab.svg?branch=master)](https://travis-ci.org/jsdom/abab)\n\nA JavaScript module that implements `window.atob` and `window.btoa` according the forgiving-base64 algorithm in the [Infra Standard](https://infra.spec.whatwg.org/#forgiving-base64). The original code was forked from [w3c/web-platform-tests](https://github.com/w3c/web-platform-tests/blob/master/html/webappapis/atob/base64.html).\n\nCompatibility: Node.js version 3+ and all major browsers.\n\nInstall with `npm`:\n\n```sh\nnpm install abab\n```\n\n## API\n\n### `btoa` (base64 encode)\n\n```js\nconst { btoa } = require('abab');\nbtoa('Hello, world!'); // 'SGVsbG8sIHdvcmxkIQ=='\n```\n\n### `atob` (base64 decode)\n\n```js \nconst { atob } = require('abab');\natob('SGVsbG8sIHdvcmxkIQ=='); // 'Hello, world!'\n```\n\n#### Valid characters\n\n[Per the spec](https://html.spec.whatwg.org/multipage/webappapis.html#atob:dom-windowbase64-btoa-3), `btoa` will accept strings \"containing only characters in the range `U+0000` to `U+00FF`.\" If passed a string with characters above `U+00FF`, `btoa` will return `null`. If `atob` is passed a string that is not base64-valid, it will also return `null`. In both cases when `null` is returned, the spec calls for throwing a `DOMException` of type `InvalidCharacterError`.\n\n## Browsers\n\nIf you want to include just one of the methods to save bytes in your client-side code, you can `require` the desired module directly.\n\n```js\nconst atob = require('abab/lib/atob');\nconst btoa = require('abab/lib/btoa');\n```\n\n## Development\n\nIf you're **submitting a PR** or **deploying to npm**, please use the [checklists in CONTRIBUTING.md](CONTRIBUTING.md#checklists).\n\n## Remembering what `atob` and `btoa` stand for\n\nBase64 comes from IETF [RFC 4648](https://tools.ietf.org/html/rfc4648#section-4) (2006). \n\n- **`btoa`**, the encoder function, stands for **binary** to **ASCII**, meaning it converts any binary input into a subset of **ASCII** (Base64).\n- **`atob`**, the decoder function, converts **ASCII** (or Base64) to its original **binary** format. \n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-27T05:19:44.048Z", "created": "2015-09-01T22:49:15.325Z", "1.0.0": "2015-09-01T22:49:15.325Z", "1.0.1": "2015-11-13T23:21:16.230Z", "1.0.2": "2015-12-30T22:40:50.762Z", "1.0.3": "2016-01-12T01:24:59.282Z", "1.0.4": "2017-09-21T01:34:13.957Z", "2.0.0": "2018-04-20T20:50:04.550Z", "2.0.1": "2019-08-28T22:53:41.111Z", "2.0.2": "2019-09-24T22:50:47.819Z", "2.0.3": "2019-11-13T01:35:55.331Z", "2.0.4": "2020-08-01T04:57:35.236Z", "2.0.5": "2020-09-11T19:11:09.611Z", "2.0.6": "2022-04-17T03:36:42.573Z"}, "homepage": "https://github.com/jsdom/abab#readme", "keywords": ["atob", "btoa", "browser"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/abab.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jsdom/abab/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "users": {"jeffcarp": true}}