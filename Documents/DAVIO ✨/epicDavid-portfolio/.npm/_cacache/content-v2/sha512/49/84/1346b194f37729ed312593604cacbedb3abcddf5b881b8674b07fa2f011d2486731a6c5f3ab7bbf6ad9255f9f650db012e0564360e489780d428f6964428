{"_id": "npm-run-path", "_rev": "23-38eeb561422c87a6d4faee153096ed1b", "name": "npm-run-path", "dist-tags": {"latest": "6.0.0"}, "versions": {"1.0.0": {"name": "npm-run-path", "version": "1.0.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "f5c32bf595fe81ae927daec52e82f8b000ac3c8f", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-1.0.0.tgz", "integrity": "sha512-PrGAi1SLlqNvKN5uGBjIgnrTb8fl0Jz0a3JJmeMcGnIBh7UE9Gc4zsAMlwDajOMg2b1OgP6UPvoLUboTmMZPFA==", "signatures": [{"sig": "MEUCIQCH4e31AL8g8U3OSVHsJDEA4wO53v4PBBZe39QaUpXDxwIgSi1LZVhl8cU8/B4OsHW3gAl3UF989xQp4ocmSp9HE2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f5c32bf595fe81ae927daec52e82f8b000ac3c8f", "engines": {"node": ">=0.10.0"}, "gitHead": "8565fe56a35dd58bde38d20409339ba2e0200bd0", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/npm-run-path", "type": "git"}, "_npmVersion": "2.14.12", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"path-key": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "2.0.0": {"name": "npm-run-path", "version": "2.0.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "b860993b6c0d2a243f370d8d24dba198899648bf", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.0.tgz", "integrity": "sha512-QLqEEWRSpaJc8qzvU+fQSzl6c/UUYcSUz3xaUEpObNl94FDkWx6oLKMpATBh/iH+iPqmHiagetfPIk/sE2j4ug==", "signatures": [{"sig": "MEYCIQCQ2XC2hGMMt/7ilwtZAWbXN76EtyNof1y2ahdtR17iyAIhAM1mCQHoJgYs/0UthZRjajcWM6QVvk6Ii+GDKnuDHj9k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b860993b6c0d2a243f370d8d24dba198899648bf", "engines": {"node": ">=4"}, "gitHead": "3511ae423a6e89fe381aa602b92b36e2630740c1", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"path-key": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path-2.0.0.tgz_1474785287361_0.8598830895498395", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.1": {"name": "npm-run-path", "version": "2.0.1", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@2.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "0c89ba91d4b4c3e3f3f9579a9b733cc9ab429d90", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.1.tgz", "integrity": "sha512-iz+CgoQfVSIpEzpXK8+d5VU2tOD9sdK7oJaUkyTgYR08TdnGIA+VRJVRYESf1jnxTXrMshDqUtD7pUcKRiWBig==", "signatures": [{"sig": "MEUCIDDOv2+6Z0cjDMwL37RDFWdG6M/OJN5c4yexsmlrMrMLAiEA+AHtWEUOrujKKIymBPj2QQ6Xurf828tgKaJ8JkiLh/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "0c89ba91d4b4c3e3f3f9579a9b733cc9ab429d90", "engines": {"node": ">=4"}, "gitHead": "3bce80f6128d30bd75e7fd7123d72b62c5e1f0e7", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"path-key": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path-2.0.1.tgz_1475038953258_0.8133386990521103", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.2": {"name": "npm-run-path", "version": "2.0.2", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@2.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "35a9232dfa35d7067b4cb2ddf2357b1871536c5f", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==", "signatures": [{"sig": "MEUCIQC/fKm/MZ1tMKWwf9TaBfZfi3K3mnBlxP6BDzkRP8UeCQIgRjWe6LvzaiPg/mFUiN0+XVu2tojqWtylx7RlO9HPvR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "35a9232dfa35d7067b4cb2ddf2357b1871536c5f", "engines": {"node": ">=4"}, "gitHead": "4d956312d5da324c4eff435af7d80797f04f09e1", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "6.6.0", "dependencies": {"path-key": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path-2.0.2.tgz_1475136638037_0.6285470693837851", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "npm-run-path", "version": "3.0.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "9bbd1824407d510ef3576c5ebc8b418dead140d9", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-42wmNM/F44tq5pDfDDwYWh30JbQokeUn79/6qNyoEvCSOMy0Q2iHe7unLRzRWQL5JLrOoQT6WzSuc6ylvEMmNg==", "signatures": [{"sig": "MEUCIDOFeP+9iTqyWiMNnPXjjrT953uaAoC7knt9bdGwtBleAiEAlyJhzUnZJOV9lDB+xfMiOiwM85DPhK9KFICjwhrTRTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchSxuCRA9TVsSAnZWagAAOjMP/iWz+r6+vdCG8uP2gUgf\nMxz6yTYrUTjsr7pUIZjHRsd6pxTF1sgLWnxi0fJIDbizKR/t+MhaC/cIbVUA\n0iLYc0jK2l+U7LyZ1HSPWzQ7Btqu7omSnW7NCHhk7voVzUT/BqKyJbQfEtaw\nAxiabuc9FOiEhyICAHNM0DOmJlApRdtecgo9Kw9M8dORyYZxgdVn7aZkPsc8\n38mPs/W7IZz1qJRv6hkeWvCag4J++/UGKgCINgqAkIW4Y5/Kd+ChGe+u3ZSt\nH1/Q4TQ6TCbC05dai28W1RxN0tp8NPsaMNH7PGy6oYV7cK6LTwV3HUfF73vJ\n+1ULSW2yOOQ8I9M2Zp4xQEeC0dGV1jZF/kTfaDrJnrpExkzbmSUPIKYh3zyb\nSTZ9X71gaNgupLfpi4pADkGCvIoNBCAhbof16Tj/pcu0hFs9JFCzBMKy6di+\n4JZBc+KKr1tkETyUlExeoloEFAdl3iQvr50n/wHLhggdIMNu8TkNieiYIOxm\nEDfSGKnuipETR0+LWQ9E+WOVghRYk0NROeSUqFO3H1+p3eCDzNv5VKyVEbnd\nIHEvP5e6bU7kYQG9P8q8zaotiZFWGPmJodFtU9Py3BJFJ77b0MEOPbBESWTA\nXg0fjgtJvfhJO2HkokqgbqjxfMnbe2+q+Ld2BiIu6H+gkmQBAMxYIARXFJOF\nAbEN\r\n=Njue\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "74fca6345b0ddc06c8e4e9e5587e1e2594a28e06", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"path-key": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.3.1", "tsd-check": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_3.0.0_1552231534136_0.9456079331176104", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "npm-run-path", "version": "3.1.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "7f91be317f6a466efed3c9f2980ad8a4ee8b0fa5", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-Dbl4A/VfiVGLgQv29URL9xshU8XDY1GeLy+fsaZ1AA8JDSfjvr5P5+pzRbWqRSBxk6/DW7MIh8lTM/PaGnP2kg==", "signatures": [{"sig": "MEUCIDEelqXmZjicS1G1al/xLeoqsGLn6Qyg1Lo+euH8TNNqAiEA3Y7UXLG8slJPQnCVufupRHbStClW9S39gSu1s0Dwvv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpvcoCRA9TVsSAnZWagAA150P/iIbvxbp73CDQ1XgmbAs\nkvNvUNhWqmHQ1wpfCde9qkIrCcrvYjn1p6tDXCL3HZXZQZATkB9KSOn5XlZd\nZmpfnHZvvoUu4hfVTQXIZjHoNvO2VvxOj+D+9Og/wzM6cJtAOmz/C3XBXR2B\nI8nWdzq1typyfJjVCYruvlVh6OuxX/Fx+9aGveNhSPXeCklDdqyOjgr9iR+W\nXsD48hHc7EfMr2N+vE5c0ErC/Y63segd3H+Q85RN3yVSFAWGlvjlUyL54t5S\nyPe7KBdmk8lprre4rVlaHlM7X1w5N5MmGZW7uYH3v/c6y0ZObXYZ+jBU/TRp\ncihsdaCnr2rtx3s8R8q21MVqUAWBxg9CdBrvTA5PRIH0V0+mlZGUk7bf7PIO\nD/n3rALpnchlPj8qmNgp1F5+CCPcHFQx+qrLEDH9FRDjrqOdHIgUDAsNIMd4\nlNsI/4p86ypVPq2w6h5J8IB9MNkLxYIj+Z5ClVv+toYoc9hB0go7rvYB6S8Z\nxSp+e9f24ZxlI+6fEpv7nzONq2R4TujY6ZQii+kFMnQ0FJGfe+TYhcyXW09W\n/VEZw757MV1BMfvItTCuMwEtpm6PN2XxVj0z5IPKiqG627NsFhEoe7GTLtyF\n2I2kBGlaNgxD/zWOGV5j2i0UpXph5G8SLDTFW6eBnNbIGzlPfH9RxbKfqhdd\nW2Gl\r\n=1jCu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "1f745c451448a8bbf2044211071793a1444edc31", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"path-key": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_3.1.0_1554446119558_0.8857482387067892", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "npm-run-path", "version": "4.0.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "d644ec1bd0569187d2a52909971023a0a58e8438", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-8eyAOAH+bYXFPSnNnKr3J+yoybe8O87Is5rtAQ8qRczJz1ajcsjg8l2oZqP+Ppx15Ii3S1vUTjQN2h4YO2tWWQ==", "signatures": [{"sig": "MEYCIQDRrLz2OOJX50oTS51wj8U1xXU50pBC/wya5HlAR9mEYAIhAOG0L86+Vx3yd6BLJqW2G89pQBwjIwnN1qShbdEpFtuq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpGhwCRA9TVsSAnZWagAAu4MP/3O2zLIocn4M+WLugen4\nJjMoFm/N0nh6hrX6O5HMxvS/Tx09QcaxfWMogW07db5XztoBHJVgitF5t+8p\nH6ifQ+ohx08Qz3EVkCcZCgfrtKWrA3m3TVII0WvyVsDkal1+Fjw9xw9JsRSq\nZHBmg+wRHTfptEdOnOQ8yuX3a1FfKqmsaVOU3vDzOMWEv6J40nN+cPLgBTyk\n8AdR7m7vwTCIxg1bURKtH2BdqSjc6CbGUpLPeWw+SvCBSMCL2f6MuPZtGFjQ\nDyuKhIJHBnWKyjYRT2MC8di1nGMgjgUDgcnwJ5ftJsfEM2zzTYqN6VuW/vZN\nwLFG7dSgvyVcfLeeCV+xOxosszGzUcYyEUTvmow/M06gUxWjUNr3MBaQQ6uc\n6iQRheNDNxprm4U44VfcoxPmCFn8oQ5dFvCD3Xwfy6kB/oXNwiaTE1Cdxiuh\n55NSlxG4iCk/B8/A+qFCGLtF68Xceiw7x/fy/DRA4ODI0e7zNE+469zLu1bu\nGzTjviwR8Pl4qBTyjZVjOx0gNbXebt4li8WBTvYjZkR1/RxEk5XKgJZo6kqx\nXiZQ+nQBYPyG6lxwS+pvtmWJFuEZEZPvQ0Uvjsai7L0F56ETABGudYFUp11g\n+IHJu1OABj7GhVCSB9ZmoAAjqtS/rHKfqJQ3FXzxTwbGRlYF4ucArGJZtQkg\nT598\r\n=7DtX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "879ba920b0c06aed2c6c4952aa54aa7d2684fece", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"path-key": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_4.0.0_1571055728173_0.006643689593938573", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "npm-run-path", "version": "4.0.1", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@4.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "signatures": [{"sig": "MEUCIEkOB+zQyQh5lgx4GWEBUmGppdoCz6ZFKK1o8KKALBk/AiEAmCGsr9BnV9N7c9PeUlg9O/M7k3I0ZnPg54WNv7dHzW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/+hxCRA9TVsSAnZWagAA2WYP/RRlICElf+0xwthqYMBR\nejdSe9zlveJk35MLTa6vCb86QuNi9OuTyeE6ltAqRBLbtk4fJZ9x5hI4LqEk\nisGpb+1gk212mzxhKJvC2dLxMBHXZEWycPLVrLxfg96F5vFtd09xL0j9Vlds\nf/YvoPY08ey4z4G1oIugpnSC+9Q/abtdmTB6ZNKrIJWONktNSYBDIbnoJkJ2\nNoVKjEb8uuHoNAbFo4Hbp6H1flPbuwyLMw8xkbHr6hjiTVNkxwmQL9O43bK7\nfBsHe3WwSRbypc9kcya2VzqsTenUQXYYshTMF69cde7r+EQIhd/lR0c+/gD1\ncFNfsYGJGjyP+pudKqwJNSaUCvNxRi+PkZ7H+NKmhn9vGD8lhKv2e4N9SdVl\nN3jkkUHkR1UXgUOPzVKXSyvQexjHgGUQXle4VNO/jIQORjfXwkBOQbWsZd0N\nWevYJxKyrp5Y3KQ47YEj7AqJT1tLCbUmXmD0UEmNJ1ibPteiuc7f8k1tjuq+\nOSKu8v6k+qGB/fyEqkvbDPxNu3mJGBVKnBWj3UMu3d8KZL/Cwce/c9bIXCPF\nI2DbDLtffXtwyW75fvshpgtNAF605kpkoKwYHMp9/S+teDxxYKVN6bDRilzA\n9IOdhL+CJG4KFuIHB/uEypgMJYGtXtPWDfa4tax5EBTQC5wasub1ZuMB70Go\nWbdv\r\n=Skp3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "09c9017b77591ced1723bd71ef9f3b5e72349268", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"path-key": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_4.0.1_1577052272701_0.6308620576315229", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "npm-run-path", "version": "5.0.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "428f70be83e4bf600318e4389e11f46e52afa741", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-oygO3ivdej3/FTTWCQz3VJhwvn5yqPU8o/sUAgxjh2IY/4KButbFSj48mTbSOATcbvZkVmBphXy0zq63fh/b4Q==", "signatures": [{"sig": "MEYCIQD7lovqzvlMchKlkcO5hOZNOcqrr9AXBv4XUlACRD9wIAIhAN3Qp89leHclBbzs2v+Hjm0vzCa3MIfvpLr33SPe0EQ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7879}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d3b7b900d0f706a308c0675a2ffa935fe107460a", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"path-key": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.45.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_5.0.0_1633420130853_0.5974753006149274", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "npm-run-path", "version": "5.0.1", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "748dd68ed7de377bb1f7132c7dafe657be5ab400", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-ybBJQUSyFwEEhqO2lXmyKOl9ucHtyZBWVM0h0FiMfT/+WKxCUZFa95qAR2X3w/w6oigN3B0b2UNHZbD+kdfD5w==", "signatures": [{"sig": "MEUCIQD47ZJQ21LvquWcb7Keuu+krRYURL5ZkH6ry72DlXbeDgIgFrm8vTkEoFotqM32OA6VAM2mssv4K/gGLwnUdCKEP54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh21Y0CRA9TVsSAnZWagAAZjkP/15sAWsFE6yqrk7fmKh9\nFqQRr3fdPmPtwcPFuHc3CnGQVDHfC+nUy3Osf3JhcpdXOpoArBb8NjFkLc+W\npI6RdtJmlK3W5q0GN8p11ulpjU2gN3v+9KASXMD3kI22S0CMyCmDgumnrBBa\nb2jiXmbQcpaK2ZmBI6sspXpg3bfHbd4gb5WlRUQh+siNZNzuK1xDj+T986ku\n3DpZfvAUCw5eKrde/zEMOAPqiw+Yh9hBVSAMaLdqIoUEj6ZkfXuOiy9b+/y8\n3ZYkD4JtGvDx9reT4JM36n+cafdtkQnhWp9b/1JN6y5gILdvI8BO5bQBqYEb\nxzcFAJbZ2x6PqOim1VJRHqPBKn/HgG0b2+mdPj5Q3ceirTA8Arfj4IstL4Bn\nxggI2DicV5bljSNvLaoWIYz3hO+dP+lLhMn5tqopuTQDchxn3/+O+b5afIrr\njMItoI8AqN9OE22+KhZzHzhrHhcYv1rz5wQgLsQRRmTyWxmf8bfvzwDBKSIx\nJarMISlL6C1KeuwuGcgr/GWttEWH98aw66N54MtzvTfxpQDkEdehfsPVDidT\nPxHzFHXjQofpbDH6kOTQb2KqNIhIXJuAPjK+VpC5gOAdnuYBR5r1KmgahRfz\nSIpKw68PxRMSSDkW1P1kVUPLeDYjCAZU1+asaqKoHvLYaUYAbZyxxkCp0YdI\nr4x0\r\n=kqg7\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d44e51582c1c66321cc0d3811396d0e6fc44a92c", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"path-key": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.45.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_5.0.1_1633420454843_0.3625301046160667", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "npm-run-path", "version": "5.1.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "bc62f7f3f6952d9894bd08944ba011a6ee7b7e00", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==", "signatures": [{"sig": "MEUCIQDcmMVeka7HaIdF5xIdWvEbGRK42dgFUTowU46drp32QQIgbXO4DmKiwXPpoOFjVhKC+wuFYvpblujEDskRpTvZ4sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA0/GCRA9TVsSAnZWagAAKUsP/2CX48OjPuQ9V80YRRSk\nxSrcFuzZV3Jts1hvst6DrkHZtvid/oQJZuI2pANbzI+svscVq6yw0rw2zJHy\nAjNCAoxLujUX22o8YQQG96nAU/uH25CiE/2lY2xlwCTv3d8b3EDNPrLEsS+R\nDDO8YvYnLxBKBAyaIWT19t4EeRAns0SZ9FnS/fUDgbptslmyHu1e/wIVfxif\nkrTz4VoF9/maAZK+zbRglKiPidvi5NqkD21L7dod4Czn1/YahHxwjVukFI95\n5iMFgY+sYvoEnYocuWPZQ5VIWLlCj0NCPLs6cButgrfkmrCkNMSR1lM7ERix\nmhb6uhwWwbTTFQQfsmuz5HNOryrfqMeE8FdEwrtvbIxYxc9ShTpCTI6wAoMR\n6dgLaxQY+3ekwj5vqksN9gkJAFIdOJce60/incdatKGfOfmrv7nSWtCikfx1\nnchhGcLL+y1waUARIlgbSzFoV89DqAU3c8bvwVO7iZTCdT8lGgpE4hBIrhVD\nE+lPNNjHcWF+nVYs9Itx92d3X6U70oD280JXRoH1NW82GzB924bFvFMUEeEp\neENNtKmbfaX6Vv4XcuQc6EZ9DZfYyumJAIdfshiQHTEn8d2lCJfxcKplwJwi\nDW5cM6cQ3AUET/jI69afO5UMZhU3YjmwISSDoy1qppoxs5rNyDggg0Of6LvI\nXuRg\r\n=md0c\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "99c4be75df5f4bba6cde86c4397440681428eb8f", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"path-key": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.45.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_5.1.0_1644384197963_0.8250067700723449", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "npm-run-path", "version": "5.2.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@5.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "224cdd22c755560253dd71b83a1ef2f758b2e955", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.2.0.tgz", "fileCount": 5, "integrity": "sha512-W4/tgAXFqFA0iL7fk0+uQ3g7wkL8xJmx3XdK0VGb4cHW//eZTtKGvFBBoRKVTpY7n6ze4NL9ly7rgXcHufqXKg==", "signatures": [{"sig": "MEUCIHTBnELFg/JSEdLhP/RGkku9nucZKIopvDk5hzCmJeFEAiEApLs+wHMMa+0/4snzMkQfW/6Vm/afZGYfFb4MUAoczkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7739}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "8462fd251b08a18e5e77e94d6202963510f1403e", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"path-key": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.45.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_5.2.0_1703174562252_0.4742530686087243", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "npm-run-path", "version": "5.3.0", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "npm-run-path@5.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "dist": {"shasum": "e23353d0ebb9317f174e93417e4a4d82d0249e9f", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz", "fileCount": 5, "integrity": "sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==", "signatures": [{"sig": "MEQCIEvjg8Xjkhg7NmHY+6DqBEU7d1+SgDjHOvz6e6twkl/jAiAPU8+vmy6RxMJL0/I9ANUKZYolatj5NrD/YkixAtSgIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8471}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "92a572f0c39fe48467c9d294e3d6a27e301bcfb1", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/npm-run-path.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get your PATH prepended with locally installed binaries", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "dependencies": {"path-key": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.45.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-run-path_5.3.0_1708655887043_0.5454260993223214", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "npm-run-path", "version": "6.0.0", "description": "Get your PATH prepended with locally installed binaries", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/npm-run-path.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "dependencies": {"path-key": "^4.0.0", "unicorn-magic": "^0.3.0"}, "devDependencies": {"ava": "^6.1.3", "tsd": "^0.31.1", "xo": "^0.59.3"}, "_id": "npm-run-path@6.0.0", "gitHead": "dbea877d781b7da037d3f89dfc921aae174feb28", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-9qny7Z9DsQU8Ou39ERsPU4OZQlSTP47ShQzuKZ6PRXpYLtIFgl/DEBYEXKlvcEa+9tHVcK8CF81Y2V72qaZhWA==", "shasum": "25cfdc4eae04976f3349c0b1afc089052c362537", "tarball": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-6.0.0.tgz", "fileCount": 5, "unpackedSize": 8644, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgNFWmjAhjS6OP86Brc1ia/lxtPr+p8rj0POkiO1yxTgIhAJnmmBYsZJ8+4owWz3czUpARiH0MdRTdL91DsGbr3tV4"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npm-run-path_6.0.0_1724661609638_0.11857341848677017"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-12-28T18:54:14.293Z", "modified": "2024-08-26T08:40:09.963Z", "1.0.0": "2015-12-28T18:54:14.293Z", "2.0.0": "2016-09-25T06:34:49.794Z", "2.0.1": "2016-09-28T05:02:34.870Z", "2.0.2": "2016-09-29T08:10:39.751Z", "3.0.0": "2019-03-10T15:25:34.328Z", "3.1.0": "2019-04-05T06:35:19.707Z", "4.0.0": "2019-10-14T12:22:08.330Z", "4.0.1": "2019-12-22T22:04:32.828Z", "5.0.0": "2021-10-05T07:48:51.022Z", "5.0.1": "2021-10-05T07:54:14.961Z", "5.1.0": "2022-02-09T05:23:18.121Z", "5.2.0": "2023-12-21T16:02:42.414Z", "5.3.0": "2024-02-23T02:38:07.190Z", "6.0.0": "2024-08-26T08:40:09.814Z"}, "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/npm-run-path.git"}, "description": "Get your PATH prepended with locally installed binaries", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# npm-run-path\n\n> Get your [PATH](https://en.wikipedia.org/wiki/PATH_(variable)) prepended with locally installed binaries\n\nIn [npm run scripts](https://docs.npmjs.com/cli/run-script) you can execute locally installed binaries by name. This enables the same outside npm.\n\n## Install\n\n```sh\nnpm install npm-run-path\n```\n\n## Usage\n\n```js\nimport childProcess from 'node:child_process';\nimport {npmRunPath, npmRunPathEnv} from 'npm-run-path';\n\nconsole.log(process.env.PATH);\n//=> '/usr/local/bin'\n\nconsole.log(npmRunPath());\n//=> '/Users/<USER>/dev/foo/node_modules/.bin:/Users/<USER>/dev/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/usr/local/bin'\n\n// `foo` is a locally installed binary\nchildProcess.execFileSync('foo', {\n\tenv: npmRunPathEnv()\n});\n```\n\n## API\n\n### npmRunPath(options?)\n\n`options`: [`Options`](#options)\\\n_Returns_: `string`\n\nReturns the augmented PATH string.\n\n### npmRunPathEnv(options?)\n\n`options`: [`Options`](#options)\\\n_Returns_: `object`\n\nReturns the augmented [`process.env`](https://nodejs.org/api/process.html#process_process_env) object.\n\n### options\n\nType: `object`\n\n#### cwd\n\nType: `string | URL`\\\nDefault: `process.cwd()`\n\nThe working directory.\n\n#### execPath\n\nType: `string | URL`\\\nDefault: [`process.execPath`](https://nodejs.org/api/process.html#processexecpath)\n\nThe path to the current Node.js executable.\n\nThis can be either an absolute path or a path relative to the [`cwd` option](#cwd).\n\n#### addExecPath\n\nType: `boolean`\\\nDefault: `true`\n\nWhether to push the current Node.js executable's directory ([`execPath`](#execpath) option) to the front of PATH.\n\n#### preferLocal\n\nType: `boolean`\\\nDefault: `true`\n\nWhether to push the locally installed binaries' directory to the front of PATH.\n\n#### path\n\nType: `string`\\\nDefault: [`PATH`](https://github.com/sindresorhus/path-key)\n\nThe PATH to be appended.\n\nSet it to an empty string to exclude the default PATH.\n\nOnly available with [`npmRunPath()`](#npmrunpathoptions), not [`npmRunPathEnv()`](#npmrunpathenvoptions).\n\n#### env\n\nType: `object`\\\nDefault: [`process.env`](https://nodejs.org/api/process.html#processenv)\n\nAccepts an object of environment variables, like `process.env`, and modifies the PATH using the correct [PATH key](https://github.com/sindresorhus/path-key). Use this if you're modifying the PATH for use in the `child_process` options.\n\nOnly available with [`npmRunPathEnv()`](#npmrunpathenvoptions), not [`npmRunPath()`](#npmrunpathoptions).\n\n## Related\n\n- [npm-run-path-cli](https://github.com/sindresorhus/npm-run-path-cli) - CLI for this module\n- [execa](https://github.com/sindresorhus/execa) - Execute a locally installed binary\n", "readmeFilename": "readme.md", "users": {"yikuo": true, "aretecode": true, "flumpus-dev": true}}