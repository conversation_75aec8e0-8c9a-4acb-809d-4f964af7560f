{"_id": "eslint-visitor-keys", "_rev": "37-8c14f014c0321cad82fb544fd4e39c7b", "name": "eslint-visitor-keys", "dist-tags": {"latest": "4.2.1"}, "versions": {"0.1.0": {"name": "eslint-visitor-keys", "version": "0.1.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@0.1.0", "maintainers": [{"name": "eslint", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "f7ed2314b90c36bd4b84a18e26c86cb4b4d04675", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-0.1.0.tgz", "integrity": "sha512-h+3ctwf0epIY9qaN0mq1ZoHSH19zSy98pzS1bYKDgxkJMRKDmo9n4r7lPtHD6ZEyzBIvXFV7e/Dg5ci/zvMPoA==", "signatures": [{"sig": "MEYCIQD9z3UatbdZSLr4NSrnRc6F2DaxT8pIP05D7K9YmYPdUAIhANmxVjNgwiDQpweZ55qbt+O4ZU1DjMe8/ytLgw5QTEKd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=4"}, "gitHead": "1888d3834d201439e1972e8da71946753310b18a", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "pretest": "npm run -s lint", "release": "eslint-release", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "ci-release": "eslint-ci-release"}, "_npmUser": {"name": "eslint", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "8.8.0", "dependencies": {}, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^0.10.3", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys-0.1.0.tgz_1510908773288_0.2083733738400042", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "eslint-visitor-keys", "version": "1.0.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@1.0.0", "maintainers": [{"name": "eslint", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "3f3180fb2e291017716acb4c9d6d5b5c34a6a81d", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz", "integrity": "sha512-qzm/XxIbxm/FHyH341ZrbnMUpe+5Bocte9xkmFMzPMjRaZMcXww+MpBptFvtU+79L362nqiLhekCxCxDPaUMBQ==", "signatures": [{"sig": "MEQCIAl5lBC9812drqdX1JU5Vo53PHVe+HsxgCdnoA6InMg+AiABndZ0oth/A2FLRUcH2BGKIVf2dJU3Xt7gEFkJiVqC/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=4"}, "gitHead": "7f35599b67605e4b1ddc84740077e15a84da5a36", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "pretest": "npm run -s lint", "release": "eslint-release", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "ci-release": "eslint-ci-release"}, "_npmUser": {"name": "eslint", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {}, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^0.10.3", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys-1.0.0.tgz_1513594527044_0.7528314294759184", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "eslint-visitor-keys", "version": "1.1.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@1.1.0", "maintainers": [{"name": "eslint", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "e2a82cea84ff246ad6fb57f9bde5b46621459ec2", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-8y9YjtM1JBJU/A9Kc+SbaOV4y29sSWckBwMHa+FGtVj5gN/sbnKDf6xJUl+8g7FAij9LVaP8C24DUiH/f/2Z9A==", "signatures": [{"sig": "MEYCIQDCsQNo0JEoMyiR6XDuUCXYQxApIWgyBvnW7gyvWfjACwIhAKTeVGUZk4na/qFtZbw4lIur80KUA17uQ+Tx0aDw13BX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUsT8CRA9TVsSAnZWagAA85oP/RyWgGWzGds87KuKknDy\nxG+DGniUHjXPXD3z+YBeLy/Vev2/r19D3PAT9f4/FI0YdB657jKq1dtup9s6\nTQFrZr1Pihyixr/2JpsfTgVamTOkLTEmqXBXjsWeX5OSKH7BOoBCW1LIGkW+\nezTR7SpYlAZuG0dVnRnz0yxH6U4NRx5GmNoumzv1I6Xp8kpCHyUi92DT63/8\nf+vDASK8ue3Xl30OrsQr2Ll2+p+icCCwi82BsOaRfmktVKIMP9xbwJYbaNKe\nenm6tyyZWNVlLgeM4g40LR4UR0kEw++bRMk2D0Jqm8z/q+wui09a1mQSli6s\nzR1RYksscPNEvcUOLw+UUDCT6svKD0TFP43wMSnYOb3KLOPDF1pGs4BX788J\nkjDPot5C/veQdw+CQ4P3igrquo8Oh3YrD/cIdbHsE4BmJ0bwpHWp9EBzrUfF\n0mYNPMg9pIvok7pzTe+/JDFVYJMb3QHJGbBTULqblGQp8B8CvnintZ7vdcZL\nf9xO/sDw6zFwLTjCfAVximjEo5DdmFtF1TYpi6n0OKjOv1Qy6kkdTLQpUSg/\nfW08tG5rZ7ZVWZlwoVi6XPSRmld1dZ4FZJnEBprquJaROYPKwqoqgGXdDEsN\nDNguKAqxHyW4IBc9wD0J9ogiz2gZWfcuwBqkmsEokHpqCF8jug0NaP5bUhow\ne8kM\r\n=saZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "0516192692d32b22509d3b34705dc13ec713f996", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "pretest": "npm run -s lint", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "eslint", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_1.1.0_1565705468147_0.9677851640877779", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "eslint-visitor-keys", "version": "1.2.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@1.2.0", "maintainers": [{"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "74415ac884874495f78ec2a97349525344c981fa", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-WFb4ihckKil6hu3Dp798xdzSfddwKKU3+nGniKF6HfeW6OLd2OUDEPP7TcHtB5+QXOKg2s6B2DaMPE1Nn/kxKQ==", "signatures": [{"sig": "MEUCIQDFv9aihNF5ouVv6Moy6CU5aF1PlJs+abpHtTxW+paFjQIgf/qKBGbGe6oBFuMrcnTyjVGBRm/sTpZMXsEmfkAClwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2NVcCRA9TVsSAnZWagAAljYP/A6BOPYIxVvy1AxIyJ8+\nPrE9/LqSuIacIDq1gvIiWMS7bjxeoHpb9eVWkebRqzr9DilCtcoQxh22rkdm\nwKFFtG5zCbAdgIUwsREabQjqUs1xlBZLZ8kMXC+WLDpceW1qd8llY+xJnnpL\njGxLgEyaK67BFAOFozhKHWg+Gig8w/1wGsEiqKXJlUMY3YYs5ReX4s7+sK73\nmj6pbIfppedcp79N99yOPVUDycAOHn2A1k2i/X4wZNS3wgWkYWgRuye/VoT0\njf37mdzQG2Lxyt1NbKXYDbHoa9CumlJTQ3Gv1rB+KApY0ZqwoA8xqLVDidLG\nWe3f9syw3K6JWwZlYC6q71tCOXveoVQKweRZVdU01pv0TjW2SOxE1My6bpke\niKY6Xyr/w7oNk/nZAHvHwCKK+4/DwtMFG9+1csAC9+KC1AgwPDauuVJwzGq0\ngIdWhjpHy/y58ktFnK3KSqx/5Ovz7Np/WQn+v9b3XVBgrtFpGEDfxJLZl7Gt\nki4Rf4iVH1NxKbwzbLy4LJ1Cr6vpKR4Q1HXSt/+GFVzXoAEUi/wo2MbP20pi\nxD/rco7JRPUIcLmLVqsqAkym8jRgBzUdzyFmJ0iFt44FPfHudw3855XLM7uQ\n+KIFHto/JEmHRrTn8s0n6Zggyy6BzxFIJ6vcR17sr5yN4ZX/U1Xcgs5cCzPv\nxpC9\r\n=y3dX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "d15b95435c7732b8ade7b607d3bb3242f95a8c4f", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "pretest": "npm run -s lint", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_1.2.0_1591268700227_0.8416030607944931", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "eslint-visitor-keys", "version": "1.3.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@1.3.0", "maintainers": [{"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "30ebd1ef7c2fdff01c3a4f151044af25fab0523e", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "fileCount": 6, "integrity": "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==", "signatures": [{"sig": "MEUCIQCRmsSb3NyJ4U/uXPgaQ0pXJjN9k6B5lhuokkF7RAwgqgIgf5EPEmA5uHw5IiEvfNasNXcx2+vD1CVD0nz2F27X9rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7OpmCRA9TVsSAnZWagAAEnEP/2H481yww20/aVys47TC\n6MliS06pGKGDCsJloq+YvyyJElytxY/aII2iOc2gsacqj9k0D5EclYR2aGme\n53r0goqFI+j57rv3csfyT8ZZ8vAlRlLXkWkX+k8INOsoWJAIaEm5VpcXH8Vj\nZioKocrHrElCKwrwW0spS4amSz37ofc8kCtsJzFuydWApFJpqr8//TzU57Qq\n6el/natbfcilwW1VYh4pUbvrLPYxJr4xClty7o0Tzea4WrE76dHURBowzpU+\n6gmSKpkCpz0XfBfkOQoGq28owCy81qJ9i2sD8N7XZ+N7JUUZCZ907zDg1VhU\ncNNHfJ9/WuPPfwscBDI6gJ1hPblVgrhwUtK9+sL3VFdo60fcKFUF2yEtY3Su\nklyjKHxiaL4SQNCSu7eD4sERih6t1pHyAMnGoRQ2T1AOeoPvtQurzqCFqTUJ\nszAYZRtdYUXA2vjDiQkMU2vputzt07sBy6r2ITjo66xfMZJtUL9GATmMiYR7\nKlGqUhGDe/3cdZqWhdDO60mtCSncWKAs9xuXlNdmSEw0G23I2az7iL578nmb\nzBQ3oKkq/HrmriFDskSV/yL0WTpwCyIhYN0yo9TJcAJGhjoD/Vi8hXMd/zhY\noxWub8SY50W7BSJEtUmxrylZBwTl663W5OrQMiQpLP7AiK1dNJOowHP7gZ20\ngkk7\r\n=UcLn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "gitHead": "80a3ee826297902d8fb777706670622536889eaf", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "pretest": "npm run -s lint", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_1.3.0_1592584806258_0.32240890612923456", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "eslint-visitor-keys", "version": "2.0.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@2.0.0", "maintainers": [{"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "21fdc8fbcd9c795cc0321f0563702095751511a8", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-QudtT6av5WXels9WjIM7qz1XD1cWGvX4gGXvp/zBn9nXG02D0utdU3Em2m/QjTnrsk6bBjmCygl3rmj118msQQ==", "signatures": [{"sig": "MEQCID8a+6uNIG2cpFaPmBXXA6qyz829kehQEn1l1nPO2jJsAiBPDs/xJjp1Hsk2le0jFyQAWeMN+It+l+BdNGAxbh+EPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNvMmCRA9TVsSAnZWagAA2QAQAJySEfcwC6+Dqyd+qNUh\nyZgMwkC2PK9X/Y8c8wSLfAt5OA8dkeBvn09XmnIQ6yYY69TOrFaIZ1vkSNyk\n7s0ZSxO3IzDeKktLh8p52usa6XKzYcYvEBId8sj33ppOH4Tz8VHUyHlxlbgv\nJASO1JXXPN5VVVWTbcvk4LsgFXCzLjCsBHUE9ckDSZI8iIr6gb1dxsvIabb1\nr9vg6G+CGznYTQpYER+lOHAcRmYYcK1y/LX9NFeShplqjvDdI0O3MPRnBhWQ\nl/2cdiwwZS/3a6rgpCbzpXB0iyhY6jMTHNgaUlnnNHLhUk6jpLd1i4aSTQ4v\nNpFdBNW5CXrxy6xqxaosjRcJIbcl3iFKZbS7W2MBxJ0Ui82tk3PzB2MOUCE5\nCzSW9jrbCR7RZsWPUHYEb6C5BhisSU3LEVeCKT7vQI734RR6DX90c38KKdhV\nRgLPvJAXNMaLdzzJC5pW+Z9qCUA0VrK99zN03SPxBKkjWtdMr9rmbyyl94Oi\ntyeQvUi1JMBItukYlzb/HbPDqms1oR6JfXEd+wWccR6cB16RfKgKYo0ndzRp\nhpm6I5FC0rlVHK/8Anes78LBteyHpS7qzFGQc8BdCs4XaaJ1tZghXXOxKejm\nTHyqeJqLkeT7skphk4ywDlGB0Bzf3jzlxGW+IVrgX2CFl872aSzkSakFG9QO\nbnzJ\r\n=niOm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=10"}, "gitHead": "abd411ba452cd24fe0473cc77a3067ff56e82cd9", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_2.0.0_1597436710184_0.41598997638384483", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "eslint-visitor-keys", "version": "2.1.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@2.1.0", "maintainers": [{"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "f65328259305927392c938ed44eb0a5c9b2bd303", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "fileCount": 6, "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "signatures": [{"sig": "MEUCIQDMtDVDreFnTFIZ/p/uChb5kLwkMeknNWDis14+lhJeqAIgHUWt6Grj0Ww28F8RTAqkX5yY0kb91Md+YfAV8k6EjOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkJcBCRA9TVsSAnZWagAAVJgP/AnbtIJOCK8x9mwfZw4w\nTth4LuKfOgc7ZSEa240yuCLkDGfRcA2LoFpy3jd0ePpWXBXwWdQNR5b0IaXo\noxHeqbiTFa3Gzp8fIfypKVqPDNwu0AP7145f7kIUYKy4+pGDiw0b/qleIzx+\nnfir/9TpYqJxLMnbqHspd5j8nLlNs0ltE4jJ9XmmTL3ifSDF1+9FtnS8kOmY\nY+41TwqGnJgNpSbDaeDsl4eRqLhuDBtIyd2IML9//ewRer0r9lCjwn17P44L\nSCzYPn2iBuWpZSCELF+tFvxzgR0RXgf5bBS7qWGV92IrSZ6748S1SbTbJl0y\nl6wqnDY8v2yODkgwibBCZBCJUskD2z/ZLyn1sRoyT+b8I5FlSkbbtfeIcdKY\nwUb/LnuKxjDbioQgwoMu2k8cm5gy6KhUWBrG8de2VV414FuqVA2/CSJs8OLW\nsHlHBQuT0qGIeCe2UJCS57nvJ6BUWt7aNzOKHcVVlWiJOC6UEg9lIaPCqtdO\nQLA/DMJ9lcsqFL0VfFJzLWl/C8f7dy6sg0Hs8KeHZw1evWR88PHvM2MjKMEh\nzlbguQONDHKSn8j43FCRmYtbBKZ4NafiHdB6MvLO0qRtZvtk7iteDji1kmc7\nVpc/WUn77YwS4ovqJD4+DBHe66yerj6zwEu/nc57+VrN7R3qmulGHdRTQ8FF\nwcWW\r\n=UhcB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=10"}, "gitHead": "a57a860297638e8247cbd20ee9bdef7106fbe995", "scripts": {"lint": "eslint lib tests/lib", "test": "nyc mocha tests/lib", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "mocha": "^3.5.3", "eslint": "^4.7.2", "opener": "^1.4.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_2.1.0_1620088576583_0.6872678056853923", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "eslint-visitor-keys", "version": "3.0.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.0.0", "maintainers": [{"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "e32e99c6cdc2eb063f204eda5db67bfe58bb4186", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-mJ<PERSON><PERSON>a35trBTb3IyRmo8xmKBZlxf+N7OnUl4+ZhJHs/r+0770Wh/LEACE2pqMGMe27G/4y8P2bYGk4J70IC5k1Q==", "signatures": [{"sig": "MEQCIHS6w2PFuG1bZ2161wVDY3RjkgXDks9Kmgjfg1zlb9rGAiACThrpZeBnIt3mSBT/vMYegPCFgUWYmxQcwXdV7WC0Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1M0JCRA9TVsSAnZWagAAgC8P/0FQ1eT1YyooYf1b2ulw\nz+wViE2p4mFX7XoVLUbQyZ7zB4agv/Y1jAuihUipgh6cRt2Vvs0lf32uXSGU\nyZ6TduQh5Dn61O+NFhT76PeVolTa0fve6v1i/q5DEoMk3bl4vkqDH84lZZ5j\nLGTNPeoKGII5OzQ+Jm+yrTYvgn3BzxoZMtNZHcIacjvhP9kV+IjzZv7pp/bX\n9OG2hTXI59CXIO51f3qFrZ+1PGcTyMDC76MMqOdydXiiXfGcH1Y1t1JCJLtw\niOlSSuyxwEYQXsgMgcrgg+/p1rvSO+0u+PyKVKwd+mlnahpa7eWVmgjYQsPk\nIOj3fmidouTHiGlYKidb/kFzPjaILbeH1Hn7qpNuhXUpRyrQk1jvDCzdyxWt\nFOIXxoTD22OAt6OavyZoA773bid8Z5WnLRAOAxc8ahj45i5FrDvlK4Es3BDJ\nGZM2b7+HT88VysGOeJshg/vrVQ/G+awvpvPWQl3jJ/Kl6HM4JC1pQYKtVGtQ\n1Y9IEkDmJBSTi/pPAoiEklRX4m0msFnACmHFzOhZlAhYXv1XC3K4mgGG6lk9\nuviMvgxnhiIv4DUK03zhwNdT3I4nQoEw75guNQOS9sdtpBE/7foYrwFW88WY\nWXWzy2tL7LgpQujpQ1AOmWrn9GPDK7Y9qJgbKZnd3a5ldYCjz5qqOb1+2hKz\ngOmJ\r\n=EUUk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "gitHead": "04f8dc38a40fa938868f5aab1b11b419851b1cfc", "scripts": {"lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js", "build": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.7.3", "mocha": "^9.0.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.52.1", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.0.0_1624558857322_0.1314509038601066", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "eslint-visitor-keys", "version": "3.1.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.1.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "eee4acea891814cda67a7d8812d9647dd0179af2", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.1.0.tgz", "fileCount": 6, "integrity": "sha512-yWJFpu4DtjsWKkt5GeNBBuZMlNcYVs6vRCLoCVEJrTjaSB6LC98gFipNK/erM2Heg/E8mIK+hXG/pJMLK+eRZA==", "signatures": [{"sig": "MEUCIQDScxLblQB1bnfAUIWE5SYtBS26RRfLhKPL2q2UqbpRMgIgQy75ZIoCyX/UhrLTJnTuZ2e6EtYQo8SQCq6p5buq6PQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2qQ5CRA9TVsSAnZWagAAu4AP/i+1npxqtlM+yiOS+Lq1\n6p0P3bvHQGCDFR+Yz7zwqtvS4uvGEMOUidKqgJ6OHId+ynUWuIS+W6EYKLQc\nzANHPUemfPa3nr+eGFmyBc3/6JKF5/xSSn3brqypBDOxWn84t1/YCmQapd8V\nDilfQzjown4+2Ox//xZU72kuPaPejT2gO0R+kZF/Gxrmg0R4k9Q6i2zlPWq/\npyCCSAmJtksIOTd3CwDV48xVDGfPVb/UgeyP71Q2VOAMMUfOQLsSSLvOGJEY\n6+1gZNM5bn4xzccuN5CNNr4sS6U33/Q2qSk+LptYWyXiGJ+/TCnoUg2cY0I1\nbisbCuGLVJ8bX3dVNpD+EPzGZQq+lKtW7efSOHTYJKNX5aU6EfpG65FimXqq\n+TzNEcjaWt0kNj2ifoF43ZqcLfbQOBcKZLvOGIhD2IthIejbUQFN/GfL2b3J\nIGZnm4jUJv8luFtRDixxsFd3nYa5Q7oLnsjjd7jLlltwm1aQBEyM+eTAG8aU\nMSjRjgOad3IhAVcP8H2SV3F07y3R2E2eAfadNznaxAEDcuFWpJYgybT6hk6A\nrUr+Wkwd38w3ryQcXHb4N967YW84//PmHd0dilwOPsGBARoFya1v6Iiq6BBj\nQykC20KL8I3uoQrxN2uoeJQ4S/DQVqH0STSD0BKt5ZC9rc3E8BjLtufkEv5z\nF80d\r\n=AzZt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "gitHead": "44027a31b4936234086eb4b175623d8231501012", "scripts": {"lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js", "build": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "16.6.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.7.3", "mocha": "^9.0.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.52.1", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.1.0_1636378167014_0.5781732247836089", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "eslint-visitor-keys", "version": "3.2.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.2.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "6fbb166a6798ee5991358bc2daa1ba76cc1254a1", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.2.0.tgz", "fileCount": 6, "integrity": "sha512-IOzT0X126zn7ALX0dwFiUQEdsfzrm4+ISsQS8nukaJXwEyYKRSnEIIDULYg1mCtGp7UUXgfGl7BIolXREQK+XQ==", "signatures": [{"sig": "MEUCIQCX7joA5T5LHetGIS8qVvg1x245TQk0N2GVdcptEz9q+AIgJQotgS2+P5xPWUMtDL4Jxt3fkmcx5Vaz8XoVlwES+3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh41MZCRA9TVsSAnZWagAAEfQP/08lWYhGTwIduMRZlr+w\n7kxp8Ds2MIvmBIHaI1xgw0f93gcsl9qkvHxrsDdEV+BYe5FcgFFhOAmO8AOi\nOwcaMCQCzSQ/lfHpSkx0AgGxWFBMoUJgxmUAuGU2sz2ExHnvhOo5O4X6ReKk\nc/Pgnxrdw3HUjgA9hdfcABaqu8OQEs9Xlfze3vnIOqXqxxhXS21oP7FBt3lF\n83hxpS7dZoClb6Tm31gACNKROWzHGGLfakEqckqASjvEk0q19V96K/PvpAzV\ngKutm8zM9Nj42rqsxbX33EDo+uB+HKuNlP2yXq4RUuIMNJPeRMhVuwZiBa2Y\nA6ZGOeI13rC+9FGHRyqCg5DelwWjzaMODm8dr97gCfPjV8GSfahH/EMaijr7\nVf8fPogOQjlYQil7Fu3DbK7hyxUgQ79Z8ibJHYlOc2ClpmOe2PVkPl2DzbeP\nsw6VWzrWZlbXT5wnzXSylxMr4DmiKO+FTR0mieQXKUddckDv5DfCYtY3n7iH\nMTWtLiqtTxMScvIZgC2TeX1IF61hg8JA1I29kDai3RMskLSPz30it80m0NdN\nuewIT6AB8bgIzXd+WKdtQpoZf8PFqJGanDoyjhk83ki9u0yNbtjyLwOU82ql\n7FddNyTI++x6dEwm6qbFryB83FpNhbfYYMhcZUSXBya05DWI0XFbIj/vqOsl\n7wYO\r\n=7iGz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "gitHead": "67c0a8b581d8d8400c89e7e3330fd92a86982654", "scripts": {"lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js", "build": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.7.3", "mocha": "^9.0.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.52.1", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.2.0_1642287897743_0.6844920780742723", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "eslint-visitor-keys", "version": "3.3.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.3.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "f6480fa6b1f30efe2d1968aa8ac745b862469826", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz", "fileCount": 8, "integrity": "sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==", "signatures": [{"sig": "MEUCIGB2VZoR320+pCk4AK22uWHrc/wVGF4GwC1JE00x5QpdAiEA5kTFjlL3TUqCPmx735lybCPQBJqsfvk9iw2wOWAg/GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBt26CRA9TVsSAnZWagAAVuUP/0anDYOF8pRpgbQsWkbz\nfFtERvSX/85BSIPcCJi9TVARhZIfaE6Jmy0n+ZOD9e6u21dQY+EgIBGZpYoM\n1mv7f0WzKG/5LBXwwyWcYv2Sb7ojNbeZbqwpLTtf98xwG7LOuaMPOL2YSrH+\nD1RS6bXBZ0J05rB9HNDsFhiVQIo7irNcRc9m8hxtkPkisBCObStOP3qjUCFO\n2Q1XGizThcm+HQo8/xXfILggrj5xduCVKsHrQRe0nB6NPM6o4ZedP3BRgTTl\nqvQmUQ+9xpIX5+JoiDxfEJopZQFAt7DMSAt1hJAJwuXkoF6UF+YKpF3DJr/x\nNtm7BvMA7XGeZ1LsCqfn4neLqU8c1PJqEWM9YfuZ+5AcnUViDqX4tyBgqDgD\n/27mkioj48iiKoIy+SZ8R5aE47FX5dOXJ1bIVpWN6vKb1anN12QPY5y3N5EW\niNpImMt8BgiqqhHGxj3D+UsMGgP/eo170i1vI7PjdLbf7VCmEMPN4Agz/exg\nKe1gf1dGyBgevAIcLW49L6wrltGmc7BHnwUDjCITVQQUlbXVWHDbZm3r0kQ/\nsJjEC2cJqy5t52IDfm2JQ25MtqjyqWnRVdlasQAfVBlqXyPtVQ9pZ6d+Qj7b\nCb9gJHmQ35DJzog9GiEsKFeTo4ZOkInpKZbX35DqSLqo+g3eSxuvfMh9WIoL\nlmlC\r\n=etNr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "gitHead": "4bd964896e38036cc6aa85b50b2d64e345deb58b", "scripts": {"tsc": "tsc", "tsd": "tsd", "lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "build": "rollup -c && npm run tsc", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.7.3", "tsd": "^0.19.1", "mocha": "^9.0.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.52.1", "typescript": "^4.5.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.3.0_1644617146797_0.5577343956196392", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "eslint-visitor-keys", "version": "3.4.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.4.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "c7f0f956124ce677047ddbc192a68f999454dedc", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.0.tgz", "fileCount": 8, "integrity": "sha512-HPpKPUBQcAsZOsHAFwTtIKcYlCje62XB7SEAcxjtmW6TD1WVpkS6i6/hOVtTZIl4zGj/mBqpFVGvaDneik+VoQ==", "signatures": [{"sig": "MEUCIQDvmqdpoL1kvDp6veKA0w9vRfN4QfzQzIHX0bWhBG04/wIge6SJPTMu9TR8ZWZjs4vl4rhGZ+Ase163nGKdB9SlvR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIhHGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDXhAAkVGlUJMxQls4Awp1tyPvQ8uTDqmTynV+rosYSnxynYFE06fg\r\nkTT9U1vpkJi7pIgdmpDgjsKanFKCi35hk6+aVzJ80vPair9nqa9rQHgFuTyd\r\nTTRtYkhmL2P/tJVlpsY8jRBdeTRAedE8gAS8x7ZKCwXUXt2OPSN/gPVanody\r\nvPtkR9Uf4PEad8VZxsnz7ZSyXw8uhHeKmZvwC1krOxJ78s2VvjKPrzhAo4Nc\r\n0nHHRm9FkKoSit5uDtdcv2TPW9JdYYiabZ4XlLKuemCYW5mT7VV97VJBnVxn\r\nmKZ0TrI4U5nrXSDU27NnVsDMsoqryMfWWyCkH02Ggr6eblcCkTOrW4LQR3iw\r\n9WS/StCzaMSc6a8TOWRGw0cEtXukYclMf99VZZ2JJ8ghB2RRY90+sQCSNMVI\r\nbAPXVcjAU8yGfo6iDM7HKTgey/q0mY6O+CkHVzZwx3on5dGGKi+NgUbZ+KqX\r\nay3au8CUKXTHmJst2DuDtzjgnIhHxszEoNCYjocA2clyc47Rb0q49KRrhbzB\r\n8UnE0RNIjC/rjmyIubqENkW1Su4Rr8Xymlb07X+Z21DkFAYdjA2O756mWyFL\r\n0qSUdq6hkIa85UJHTIxZV0+I40WfcU9u6G3TI8cUS9tNgjTjwEtAYj9b84C5\r\neLQw/T/QuSpIfg/gKO+GbYIYhf2+0iyzXPk=\r\n=hzGs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "2435b0cc66aae3c97d65040870e86c96933c6393", "scripts": {"tsc": "tsc", "tsd": "tsd", "lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "build": "npm run bundle && npm run tsc", "bundle": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "build-keys": "node tools/build-keys-from-ts", "build:debug": "npm run bundle -- -m && npm run tsc", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.19.1", "chai": "^4.3.6", "mocha": "^9.2.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.70.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^4.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^5.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.4.0_1679954374644_0.5733591334149044", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "eslint-visitor-keys", "version": "3.4.1", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.4.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "c22c48f48942d08ca824cc526211ae400478a994", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz", "fileCount": 9, "integrity": "sha512-pZnmmLwYzf+kWaM/Qgrvpen51upAktaaiI01nsJD/Yr3lMOdNtq0cxkrrg16w64VtisN6okbs7Q8AfGqj4c9fA==", "signatures": [{"sig": "MEYCIQCJpyxVg2Jtv82WFwUi1STLn/Tfg3L2sLkvKany5l495gIhALE14c5sCfLx7aMs59o+W0UxFQs9E2d/hPKEmtgpNZLI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32264}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "2df6ad87f59f5ba4f5987242eb52c6b54186d388", "scripts": {"tsc": "tsc", "tsd": "tsd", "lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "build": "npm run bundle && npm run tsc", "bundle": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "build-keys": "node tools/build-keys-from-ts", "build:debug": "npm run bundle -- -m && npm run tsc", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.19.1", "chai": "^4.3.6", "mocha": "^9.2.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.70.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^4.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^4.2.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^5.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.4.1_1683311760845_0.06395236533456683", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "eslint-visitor-keys", "version": "3.4.2", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.4.2", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "8c2095440eca8c933bedcadf16fefa44dbe9ba5f", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.2.tgz", "fileCount": 9, "integrity": "sha512-8drBzUEyZ2llkpCA67iYrgEssKDUu68V8ChqqOfFupIaG/LCVPUT+CoGJpT77zJprs4T/W7p07LP7zAIMuweVw==", "signatures": [{"sig": "MEYCIQCJw5NbG/yNVWQVX9MzeYVLjQvVMFEh57yI0EoLgnUMLwIhALqWi8jteRbYj2hE1sXu79E1U0IjbC8YiDkuojj6W2Tm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@3.4.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 32201}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "2e495d1c4370470d182bc14b89126a6582332ad9", "scripts": {"tsc": "tsc", "tsd": "tsd", "lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "build": "npm run bundle && npm run tsc", "bundle": "rollup -c", "prepare": "npm run build", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "build-keys": "node tools/build-keys-from-ts", "build:debug": "npm run bundle -- -m && npm run tsc", "publish-release": "eslint-publish-release", "generate-release": "eslint-generate-release", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-betarelease": "eslint-generate-prerelease beta", "generate-alpharelease": "eslint-generate-prerelease alpha"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.19.1", "chai": "^4.3.6", "mocha": "^9.2.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.70.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^4.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^4.2.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^5.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.4.2_1690557365843_0.5553119133349631", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "eslint-visitor-keys", "version": "3.4.3", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@3.4.3", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "fileCount": 9, "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==", "signatures": [{"sig": "MEUCIQCXuehxoxaLBE6Gn10Ex5LxGiWCxu5Ae23MajUK+fwkMgIgaibD1BGUMKIYOlDyvfqxRU3oqKEgxUWbOhl32vgYdI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@3.4.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 32278}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "d08d617d9d824301dbbbb9eb8ec4e7f088469d83", "scripts": {"lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "build": "npm run build:cjs && npm run build:types", "prepare": "npm run build", "build:cjs": "rollup -c", "build:keys": "node tools/build-keys-from-ts", "test:types": "tsd", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:types": "tsc", "release:publish": "eslint-publish-release", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "release:generate:rc": "eslint-generate-prerelease rc", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:latest": "eslint-generate-release"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.19.1", "chai": "^4.3.6", "mocha": "^9.2.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.70.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^4.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^4.2.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^5.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_3.4.3_1691765394153_0.27926685523734385", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "eslint-visitor-keys", "version": "4.0.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@4.0.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dist": {"shasum": "e3adc021aa038a2a8e0b2f8b0ce8f66b9483b1fb", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-OtIRv/2GyiF6o/d8K7MYKKbXrOUBIK6SfkIRM4Z0dY3w+LiQ0vy3F57m0Z71bjbyeiWFiHJ8brqnmE6H6/jEuw==", "signatures": [{"sig": "MEUCIHevvwA0ZJzGDA/tapDpc550XzZ1fk3iXmGYPgbimlXEAiEA20vVOe8PM1iw3dmPjDxYmUZlcCSiyhe+Ky/YCFlUm5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 32276}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "b75dc7d1d827bd82d55379644005f17a1db9c749", "scripts": {"lint": "eslint .", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "build": "npm run build:cjs && npm run build:types", "prepare": "npm run build", "build:cjs": "rollup -c", "build:keys": "node tools/build-keys-from-ts", "test:types": "tsd", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:types": "tsc", "release:publish": "eslint-publish-release", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "release:generate:rc": "eslint-generate-prerelease rc", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:latest": "eslint-generate-release"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/eslint-visitor-keys.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.19.1", "chai": "^4.3.6", "mocha": "^9.2.1", "eslint": "^7.29.0", "opener": "^1.5.2", "rollup": "^2.70.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^4.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^4.2.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^5.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_4.0.0_1707517904242_0.4374571175016917", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "eslint-visitor-keys", "version": "4.1.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@4.1.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/js/blob/main/packages/eslint-visitor-keys/README.md", "bugs": {"url": "https://github.com/eslint/js/issues"}, "dist": {"shasum": "1f785cc5e81eb7534523d85922248232077d2f8c", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-Q7lok0mqMUSf5a/AdAZkA5a/gHcO6snwQClVNNvFKCAVlxXucdU8pKydU5ZVZjBx5xr37vGbFFWtLQYreLzrZg==", "signatures": [{"sig": "MEQCIE9ivToSBxI8dnnpd44iyjW2rl9+nd5E7kqpuRTsD/EFAiBJVnmeMX2RXmWFW4Ej39QvcBcrdIMGEXTvbVi/bs1vtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35721}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "6e899c70236294192402a22bfeba509ec8f72566", "scripts": {"test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "build": "npm run build:cjs && npm run build:types", "build:cjs": "rollup -c", "test:types": "tsd", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:types": "tsc -v && tsc", "release:publish": "eslint-publish-release", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "release:generate:rc": "eslint-generate-prerelease rc", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:latest": "eslint-generate-release"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/js.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.31.2", "chai": "^4.3.6", "mocha": "^9.2.1", "opener": "^1.5.2", "rollup": "^4.22.4", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^5.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^6.1.1", "@typescript-eslint/parser": "^8.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_4.1.0_1727448931763_0.14126902026092258", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "eslint-visitor-keys", "version": "4.2.0", "keywords": [], "author": {"url": "https://github.com/mysticatea", "name": "<PERSON><PERSON>"}, "license": "Apache-2.0", "_id": "eslint-visitor-keys@4.2.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/js/blob/main/packages/eslint-visitor-keys/README.md", "bugs": {"url": "https://github.com/eslint/js/issues"}, "dist": {"shasum": "687bacb2af884fcdda8a6e7d65c606f46a14cd45", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz", "fileCount": 9, "integrity": "sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==", "signatures": [{"sig": "MEUCIQCpx5uJDF+dGFl6Xa4Aqfkxxdaw9FisNBgimKkJ2f18yQIgEtXYNCFL5iXHelh6OxSIzY4x16aFWHVRvKoC98GL4pY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@4.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36136}, "main": "dist/eslint-visitor-keys.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "funding": "https://opencollective.com/eslint", "gitHead": "844078a1df7736019eee52665b67393ffcfd4c18", "scripts": {"test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "build": "npm run build:cjs && npm run build:types", "build:cjs": "rollup -c", "test:types": "tsd", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:types": "tsc -v && tsc", "release:publish": "eslint-publish-release", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "release:generate:rc": "eslint-generate-prerelease rc", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:latest": "eslint-generate-release"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/js.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Constants and utilities about visitor keys to traverse AST.", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "tsd": "^0.31.2", "chai": "^4.3.6", "mocha": "^9.2.1", "opener": "^1.5.2", "rollup": "^4.22.4", "esquery": "^1.4.0", "json-diff": "^0.7.3", "typescript": "^5.6.2", "@types/estree": "^0.0.51", "eslint-release": "^3.2.0", "@types/estree-jsx": "^0.0.1", "rollup-plugin-dts": "^6.1.1", "@typescript-eslint/parser": "^8.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/eslint-visitor-keys_4.2.0_1730229899449_0.7978560229924121", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "eslint-visitor-keys", "version": "4.2.1", "description": "Constants and utilities about visitor keys to traverse AST.", "type": "module", "main": "dist/eslint-visitor-keys.cjs", "types": "./dist/index.d.ts", "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "devDependencies": {"@types/estree": "^0.0.51", "@types/estree-jsx": "^0.0.1", "@typescript-eslint/parser": "^8.7.0", "eslint-release": "^3.2.0", "esquery": "^1.4.0", "json-diff": "^0.7.3", "opener": "^1.5.2", "rollup": "^4.22.4", "rollup-plugin-dts": "^6.1.1", "tsd": "^0.31.2", "typescript": "^5.6.2"}, "scripts": {"build": "npm run build:cjs && npm run build:types", "build:cjs": "rollup -c", "build:debug": "npm run build:cjs -- -m && npm run build:types", "build:types": "tsc -v && tsc", "release:generate:latest": "eslint-generate-release", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:rc": "eslint-generate-prerelease rc", "release:publish": "eslint-publish-release", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run test:types", "test:open-coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "test:types": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/js.git", "directory": "packages/eslint-visitor-keys"}, "funding": "https://opencollective.com/eslint", "keywords": ["eslint"], "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/js/issues"}, "homepage": "https://github.com/eslint/js/blob/main/packages/eslint-visitor-keys/README.md", "_id": "eslint-visitor-keys@4.2.1", "gitHead": "ab3d59382b03d03a4e57def3742f94da16d88738", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==", "shasum": "4cfea60fe7dd0ad8e816e1ed026c1d5251b512c1", "tarball": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "fileCount": 9, "unpackedSize": 37151, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-visitor-keys@4.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDtoKC9F91L/onSryuIvzUCA8GH8oyG1SMQOXjNix4dLQIhALeD/XmR8LAgFNLyuk8AfXHKVlguxslNZVlwuvZADslq"}]}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/eslint-visitor-keys_4.2.1_1749483952365_0.6448816080637285"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-11-17T08:52:53.415Z", "modified": "2025-06-09T15:45:53.017Z", "0.1.0": "2017-11-17T08:52:53.415Z", "1.0.0": "2017-12-18T10:55:27.110Z", "1.1.0": "2019-08-13T14:11:08.272Z", "1.2.0": "2020-06-04T11:05:00.390Z", "1.3.0": "2020-06-19T16:40:06.400Z", "2.0.0": "2020-08-14T20:25:10.300Z", "2.1.0": "2021-05-04T00:36:16.797Z", "3.0.0": "2021-06-24T18:20:57.467Z", "3.1.0": "2021-11-08T13:29:27.161Z", "3.2.0": "2022-01-15T23:04:57.885Z", "3.3.0": "2022-02-11T22:05:46.978Z", "3.4.0": "2023-03-27T21:59:34.841Z", "3.4.1": "2023-05-05T18:36:01.011Z", "3.4.2": "2023-07-28T15:16:06.173Z", "3.4.3": "2023-08-11T14:49:54.383Z", "4.0.0": "2024-02-09T22:31:44.490Z", "4.1.0": "2024-09-27T14:55:31.960Z", "4.2.0": "2024-10-29T19:24:59.637Z", "4.2.1": "2025-06-09T15:45:52.557Z"}, "bugs": {"url": "https://github.com/eslint/js/issues"}, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}, "license": "Apache-2.0", "homepage": "https://github.com/eslint/js/blob/main/packages/eslint-visitor-keys/README.md", "keywords": ["eslint"], "repository": {"type": "git", "url": "git+https://github.com/eslint/js.git", "directory": "packages/eslint-visitor-keys"}, "description": "Constants and utilities about visitor keys to traverse AST.", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# eslint-visitor-keys\n\n[![npm version](https://img.shields.io/npm/v/eslint-visitor-keys.svg)](https://www.npmjs.com/package/eslint-visitor-keys)\n[![Downloads/month](https://img.shields.io/npm/dm/eslint-visitor-keys.svg)](http://www.npmtrends.com/eslint-visitor-keys)\n[![Build Status](https://github.com/eslint/js/workflows/CI/badge.svg)](https://github.com/eslint/js/actions)\n\nConstants and utilities about visitor keys to traverse AST.\n\n## 💿 Installation\n\nUse [npm] to install.\n\n```bash\n$ npm install eslint-visitor-keys\n```\n\n### Requirements\n\n- [Node.js] `^18.18.0`, `^20.9.0`, or `>=21.1.0`\n\n## 📖 Usage\n\nTo use in an ESM file:\n\n```js\nimport * as evk from \"eslint-visitor-keys\"\n```\n\nTo use in a CommonJS file:\n\n```js\nconst evk = require(\"eslint-visitor-keys\")\n```\n\n### evk.KEYS\n\n> type: `{ [type: string]: string[] | undefined }`\n\nVisitor keys. This keys are frozen.\n\nThis is an object. Keys are the type of [ESTree] nodes. Their values are an array of property names which have child nodes.\n\nFor example:\n\n```\nconsole.log(evk.KEYS.AssignmentExpression) // → [\"left\", \"right\"]\n```\n\n### evk.getKeys(node)\n\n> type: `(node: object) => string[]`\n\nGet the visitor keys of a given AST node.\n\nThis is similar to `Object.keys(node)` of ES Standard, but some keys are excluded: `parent`, `leadingComments`, `trailingComments`, and names which start with `_`.\n\nThis will be used to traverse unknown nodes.\n\nFor example:\n\n```js\nconst node = {\n    type: \"AssignmentExpression\",\n    left: { type: \"Identifier\", name: \"foo\" },\n    right: { type: \"Literal\", value: 0 }\n}\nconsole.log(evk.getKeys(node)) // → [\"type\", \"left\", \"right\"]\n```\n\n### evk.unionWith(additionalKeys)\n\n> type: `(additionalKeys: object) => { [type: string]: string[] | undefined }`\n\nMake the union set with `evk.KEYS` and the given keys.\n\n- The order of keys is, `additionalKeys` is at first, then `evk.KEYS` is concatenated after that.\n- It removes duplicated keys as keeping the first one.\n\nFor example:\n\n```js\nconsole.log(evk.unionWith({\n    MethodDefinition: [\"decorators\"]\n})) // → { ..., MethodDefinition: [\"decorators\", \"key\", \"value\"], ... }\n```\n\n## 📰 Change log\n\nSee [GitHub releases](https://github.com/eslint/js/releases).\n\n## 🍻 Contributing\n\nWelcome. See [ESLint contribution guidelines](https://eslint.org/docs/developer-guide/contributing/).\n\n### Development commands\n\n- `npm test` runs tests and measures code coverage.\n- `npm run lint` checks source codes with ESLint.\n- `npm run test:open-coverage` opens the code coverage report of the previous test with your default browser.\n\n[npm]: https://www.npmjs.com/\n[Node.js]: https://nodejs.org/\n[ESTree]: https://github.com/estree/estree\n\n<!-- NOTE: This section is autogenerated. Do not manually edit.-->\n<!--sponsorsstart-->\n## Sponsors\n\nThe following companies, organizations, and individuals support ESLint's ongoing maintenance and development. [Become a Sponsor](https://eslint.org/donate)\nto get your logo on our READMEs and [website](https://eslint.org/sponsors).\n\n<h3>Diamond Sponsors</h3>\n<p><a href=\"https://www.ag-grid.com/\"><img src=\"https://images.opencollective.com/ag-grid/bec0580/logo.png\" alt=\"AG Grid\" height=\"128\"></a></p><h3>Platinum Sponsors</h3>\n<p><a href=\"https://automattic.com\"><img src=\"https://images.opencollective.com/automattic/d0ef3e1/logo.png\" alt=\"Automattic\" height=\"128\"></a> <a href=\"https://www.airbnb.com/\"><img src=\"https://images.opencollective.com/airbnb/d327d66/logo.png\" alt=\"Airbnb\" height=\"128\"></a></p><h3>Gold Sponsors</h3>\n<p><a href=\"https://qlty.sh/\"><img src=\"https://images.opencollective.com/qltysh/33d157d/logo.png\" alt=\"Qlty Software\" height=\"96\"></a> <a href=\"https://trunk.io/\"><img src=\"https://images.opencollective.com/trunkio/fb92d60/avatar.png\" alt=\"trunk.io\" height=\"96\"></a> <a href=\"https://shopify.engineering/\"><img src=\"https://avatars.githubusercontent.com/u/8085\" alt=\"Shopify\" height=\"96\"></a></p><h3>Silver Sponsors</h3>\n<p><a href=\"https://vite.dev/\"><img src=\"https://images.opencollective.com/vite/e6d15e1/logo.png\" alt=\"Vite\" height=\"64\"></a> <a href=\"https://liftoff.io/\"><img src=\"https://images.opencollective.com/liftoff/5c4fa84/logo.png\" alt=\"Liftoff\" height=\"64\"></a> <a href=\"https://americanexpress.io\"><img src=\"https://avatars.githubusercontent.com/u/3853301\" alt=\"American Express\" height=\"64\"></a> <a href=\"https://stackblitz.com\"><img src=\"https://avatars.githubusercontent.com/u/28635252\" alt=\"StackBlitz\" height=\"64\"></a></p><h3>Bronze Sponsors</h3>\n<p><a href=\"https://sentry.io\"><img src=\"https://github.com/getsentry.png\" alt=\"Sentry\" height=\"32\"></a> <a href=\"https://syntax.fm\"><img src=\"https://github.com/syntaxfm.png\" alt=\"Syntax\" height=\"32\"></a> <a href=\"https://cybozu.co.jp/\"><img src=\"https://images.opencollective.com/cybozu/933e46d/logo.png\" alt=\"Cybozu\" height=\"32\"></a> <a href=\"https://www.crosswordsolver.org/anagram-solver/\"><img src=\"https://images.opencollective.com/anagram-solver/2666271/logo.png\" alt=\"Anagram Solver\" height=\"32\"></a> <a href=\"https://icons8.com/\"><img src=\"https://images.opencollective.com/icons8/7fa1641/logo.png\" alt=\"Icons8\" height=\"32\"></a> <a href=\"https://discord.com\"><img src=\"https://images.opencollective.com/discordapp/f9645d9/logo.png\" alt=\"Discord\" height=\"32\"></a> <a href=\"https://www.gitbook.com\"><img src=\"https://avatars.githubusercontent.com/u/7111340\" alt=\"GitBook\" height=\"32\"></a> <a href=\"https://nolebase.ayaka.io\"><img src=\"https://avatars.githubusercontent.com/u/11081491\" alt=\"Neko\" height=\"32\"></a> <a href=\"https://nx.dev\"><img src=\"https://avatars.githubusercontent.com/u/23692104\" alt=\"Nx\" height=\"32\"></a> <a href=\"https://opensource.mercedes-benz.com/\"><img src=\"https://avatars.githubusercontent.com/u/34240465\" alt=\"Mercedes-Benz Group\" height=\"32\"></a> <a href=\"https://herocoders.com\"><img src=\"https://avatars.githubusercontent.com/u/37549774\" alt=\"HeroCoders\" height=\"32\"></a> <a href=\"https://www.lambdatest.com\"><img src=\"https://avatars.githubusercontent.com/u/171592363\" alt=\"LambdaTest\" height=\"32\"></a></p>\n<h3>Technology Sponsors</h3>\nTechnology sponsors allow us to use their products and services for free as part of a contribution to the open source ecosystem and our work.\n<p><a href=\"https://netlify.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/netlify-icon.svg\" alt=\"Netlify\" height=\"32\"></a> <a href=\"https://algolia.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/algolia-icon.svg\" alt=\"Algolia\" height=\"32\"></a> <a href=\"https://1password.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/1password-icon.svg\" alt=\"1Password\" height=\"32\"></a></p>\n<!--sponsorsend-->\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}