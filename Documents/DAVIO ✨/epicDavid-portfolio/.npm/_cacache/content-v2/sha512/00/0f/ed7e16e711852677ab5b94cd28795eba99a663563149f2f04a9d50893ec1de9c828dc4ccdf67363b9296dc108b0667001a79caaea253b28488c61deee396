{"_id": "@babel/plugin-transform-unicode-sets-regex", "_rev": "31-6b02b6a8b684dd5bfe6df4e5f45338a2", "name": "@babel/plugin-transform-unicode-sets-regex", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.22.0", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9f1ee21b79a90c16c367cdc0d8fffac1cde0f242", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-w9ZRKNaJAk2vOhY6HTF7nmr+c5vJ//RCH7S0l4sWyts1x17W45oa6J3UYeZ/RXb74XHm1eFfLjqzY1Hg2mtyaw==", "signatures": [{"sig": "MEYCIQDhC3G4bOK7XmlMOoe5a0/5GsV1K56m0vXcuC27cxChQAIhALuegXcGpcqeXytc8m6gzPjZ1Yf1jaOxiyv2cwo39UjV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4680}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.22.0_1685108741695_0.8980780189361108", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.22.3", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "7c14ee33fa69782b0101d0f7143d3fc73ce00700", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-hNufLdkF8vqywRp+P55j4FHXqAX2LRUccoZHH7AFn1pq5ZOO2ISKW9w13bFZVjBoTqeve2HOgoJCcaziJVhGNw==", "signatures": [{"sig": "MEUCIQCw9bDQYbucinjGBueBUtJfMOsOci6TJdnlX/bYHNhlcAIgdbnpxr97h57xr8/d4TxnSTXnDTfUXyzOqR4+lUqZpcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4660}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.22.3_1685182262907_0.09259389687488606", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.22.5", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "77788060e511b708ffc7d42fdfbc5b37c3004e91", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-lhMfi4FC15j13eKrh3DnYHjpGj6UKQHtNKTbtc1igvAhRy4+kLhV07OpLcsN0VgDEw/MjAvJO4BdMJsHwMhzCg==", "signatures": [{"sig": "MEUCIFJC+p8itAwUgTkEPJHS114H8mJASv1++UB/hAe6ohrIAiEAyCnvMhVwUQEN5fzMFkbhczlFQn5EOTxrEnZXcNsAZbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4660}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.22.5_1686248499750_0.8621403638506473", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "04b1c97d97a465953c02efcde978e9e7b421761d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-fmPEzJutv3W6LmWexI+gsAy/zk7pcMuzMg3LgQhHhz+8Cb1W1fu0P7oKPoRLrCSD/01mu3dBa9Uz+vp7Zscjuw==", "signatures": [{"sig": "MEUCIBxnmgUpFY/Kem6GHxbOzoLYMgf9xN/Za430Mmfa4Lg1AiEA0X5++/nk9lFS4cG0MSz4xzAGBNpQrbLhvyy/maWiVns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.0_1689861624618_0.6004096660236526", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f512d31b0beec0b2ca1dfa01a239ee9cc3e358b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-WCDFQ2FQHe0tNGKToaJyHWfAcEuxw/1n+xCBH4myj1I+KG4PBO9Yj3NAzn8vqpkHqwebA5aipF0ewLhbWbpqMQ==", "signatures": [{"sig": "MEYCIQDY1WPDrCiNg1GLeIw5sePYWE4EEGpH4SAhUSQAvxeG2AIhAL7HP2HnI6ID2yM+BYOwzaAO+AZ1ezu+Sh2GT6MYV/bG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.1_1690221177451_0.507512856672659", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2142e0368b2647b19d9656552d50478391ecaea8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-HYdFXCSiWx+2H+L3g8i6HBygtaD0gGrEHu7xAdbpGU6U4mbMspYrrnLnBoMEmHFCR3yJHPIqFmu6Slqd4zWqWA==", "signatures": [{"sig": "MEUCIQDZUVP7rRvsJ+cPlVvU2FLIOfcvZ8jZjgNDiaPRJCDq5wIgVomcoAsvlq3zeCoHItw20DxcanyxVB/GRiAuFap1cLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.2_1691594120560_0.9763309600935877", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "8ecc83ed568c87e8a982f4a4f3bda40dfecba5bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-8t6MBPoFjxHCFFQveFIu6Sbf8/ceit0hzMRshcl2gmCB1dO3nVMzEzQEjM1jp/7m08lOl/FxiOftWbIXjMaloA==", "signatures": [{"sig": "MEUCIQD4/ebV1L2K7g2j0/1VvOQI25LW57jMmmGBvp88vyGKkgIgbGc2+l0ipY5EqgKmx2NokHR6m1v7cZs9jwiv51XmD1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.3_1695740253989_0.8175199814987213", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "1502baa273d65f0404bc44cbb72edc9d4423c9f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-zHYE06DtnAd6OJ8Vc9KgRsQ0YE9Iog5ifFhv2UDRwAvUyzoybsw55UQgx/RbIYxV7VK8qydRjFmuXy7DilBFnA==", "signatures": [{"sig": "MEUCIFlAXvXL9AMrrmoSf/LJU/gF63h4YZMOMxzieBmYTsn5AiEAo94wPRsUy2gmj1/gUcnw5JI26ArkTfoeWnnSTJ3Pn7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.4_1697076407156_0.17700110182909", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.23.3", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4fb6f0a719c2c5859d11f6b55a050cc987f3799e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-W7lliA/v9bNR83Qc3q1ip9CQMZ09CcHDbHfbLRDNuAhn1Mvkr1ZNF7hPmztMQvtTGVLJ9m8IZqWsTkXOml8dbw==", "signatures": [{"sig": "MEUCIQDXEmf98EKwmc/DhUcl5UGjNgZ5W7+bJdECQWZECXpj9wIgRVpEMZMi0jn/6I1LWjLBH5/WplcDE/IEWQ1loh+smA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4741}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.23.3_1699513443956_0.5233935478080278", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "8345e387c47b3e0d7cef2c6eb246833c087e8331", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-WQWQUVqQRCXl1Z48uhPd2pruylc0euWx7pQ3xiRc1LFrV7szdgKNHsw8kfCHIXVsT+NR913PRF00oO8203NRCw==", "signatures": [{"sig": "MEQCIGCUH9f7JbUfGNsKeSg8oyRTxWD0+fyg+o5YppHpXstgAiAqwBdPfmUm3Fzk5/qNCioRKHPa97CbRg1aYbc9yKhXeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4512}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.5_1702307979295_0.9366700775694667", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "1a2d097904fa61bff2e5f06af3426604c3230bb0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-AVxPDydysWyRyvoVBFNeTqsw+IDD90m0BNA9csLRWLpJmfFT37xKU/Zl3NpDl4pkFXPpKXvojQXJLexWyV5Nqg==", "signatures": [{"sig": "MEUCIDNUykjdn46fSw0MiyeHGRHJnk46bxPx+BIKdPzi4JuNAiEAhKaHc/AIwGTU3e2ww2gxDBD7+A72sYvJc5HNbjY9Svs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4512}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.6_1706285678363_0.3952119373077674", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "ec6b1bb3e2fa084afc6558d9444e060f273d9c87", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-9DjUOAA0qIFdog2/dXLgGd1QEw9krmAOvWuNongNVfIX3uhoxuQSPVYdyUT3jx6MdkCiUrl9SoZmRKY+UFLk1A==", "signatures": [{"sig": "MEQCIAZ3IQAAmebf5AUPvg+cyXOHI6FgnLBmWreXun2oN4KAAiAF4KsTMZG6PlC5qT0fQcL4CURij0ahC0ElWfBjN/3mKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4512}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.7_1709129140580_0.7065384519691122", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.24.1", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "c1ea175b02afcffc9cf57a9c4658326625165b7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-fqj4WuzzS+ukpgerpAoOnMfQXwUHFxXUZUE84oL2Kao2N8uSlvcpnAidKASgsNgzZHBsHWvcm8s9FPWUhAb8fA==", "signatures": [{"sig": "MEQCIBQ0JJnIj+RF5kUrWPXfcKThDxJOsg2Qwfp5+JNhiV3BAiB57QDBv9K1yL1cHu/j2B7LR8KN9YN/H3K5Pyk0xkFKXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4672}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.24.1_1710841751134_0.10872077048056594", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "48951fc8175f4b9ed1df8895f60a0f7d05bc5a90", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-XzhiaP2+tSykk3bLoJKMWagUd6EbM9JyO3ePNn+qdQy/L8scq5uOcGRoTXYtM1PyEt5Iusi1m6W2Oxy5OAKADg==", "signatures": [{"sig": "MEYCIQDj+dicAla8eR07DIlQlyi0OAZ05gIRAHHZqhX7IG0h5AIhAIiM7IBRdYyKC31VkVsGJIUozyX+oSs3vDIqmuwwtEvX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4426}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.8_1712236817371_0.5119448662336397", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.24.6", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f18b7292222aee85c155258ceb345a146a070a46", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-quiMsb28oXWIDK0gXLALOJRXLgICLiulqdZGOaPPd0vRT7fQp74NtdADAVu+D8s00C+0Xs0MxVP0VKF/sZEUgw==", "signatures": [{"sig": "MEUCIHFiXuxrfEDvI5Cj9+jODyruw1OLP7t2uL/daRKJo0pCAiEA5M/Hm1qLyhHoclI4TyHkGFfLLXBsLQqD6BrYM2NuZ/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71117}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-regexp-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.24.6_1716553507911_0.7703454156614713", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "90941b476b23bfe058c9e273572c7d5045e2f2f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-nKlBNn7IfutETfCRRwIex5bNJduAypTqDep+3nhNchH/I2ShORIzQhLS1sfYhxzhiFabzC8/f1TsXGz66Jm39Q==", "signatures": [{"sig": "MEUCIQD/AR717sYZo/VS4WxO3bX77WfLEWRpLnoNN3sC234JjgIgBQF9l/m8QAUMwIF/1QWdX1gU0KrgwA+DXDW4FzXvbgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71089}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.9_1717423492153_0.7279323533293487", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5ecfc686c9be5a19c76219888f144fd3a2cd24ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-Kc+qfGEAPw3KFwT6VZCxhh20R8B57lehSFFrnIgmjKi7Wbo/k9qjHLjpgybx+ExGxNE7Ygfdxx6QAPEhObQm4w==", "signatures": [{"sig": "MEUCIGhRKMEDpvMdvz6y4sUuKfEnLdq2Bezw1aIXo9GKFSeVAiEAkUzqFzoIV9RzXJPBk1MgEZfP2n785eRdF56FMRGtyKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71097}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.10_1717500028793_0.4150315925088093", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.24.7", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d40705d67523803a576e29c63cef6e516b858ed9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-2G8aAvF4wy1w/AGZkemprdGMRg5o6zPNhbHVImRz3lss55TYCBd6xStN19rt8XJHq20sqV0JbyWjOWwQRwV/wg==", "signatures": [{"sig": "MEQCIH5LDHYk8U9bM5ugZJGTxFiclsPmXbPpr/AspT1i/UInAiBA7Lyl2k0yqfILnnIb/GAAVC4aww1h4LB/vPoyQhjS4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71113}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-regexp-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.24.7_1717593343253_0.14991158852351005", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5413f1b41eca7974799a5b3f50c48fd30faaf280", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-DC09wHLb1Rn3lk9IpSZU3IVeXKIJwvtPoIdecngIozA3zDDcCUwnGqbsXerKJvFVyCUhFk9iRHH37KKpTUSQmA==", "signatures": [{"sig": "MEYCIQCQY+bQytB5s828uO0SeDOSVQpESqeG0YaDB2Q2bhc/ZwIhAKZDQx3MTLX3dYH36TcxqxDLAse40FOMlu/V4u+Ic+2S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70986}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.11_1717751753316_0.1865228700136481", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "0ff768473b7d485df8d8b0c81341b702d30d1810", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-V6XVN1eWuP00YVayKdjNL4ZfCXmAJMNHZ0ki+cspkuhZ9IBOew1v3OBfqEDlGwpo4XTZ37F4HkJd4/kW2K4zRQ==", "signatures": [{"sig": "MEUCICnSHTqMWlAqjranQPg3GVJx7UmOBwV/JJVLikPmtpCXAiEAkIhaWgdsVPktnG9XmkmqOD7xrqYw6uentI7tP+MEHog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67765}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.12_1722015227383_0.6323176909868284", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.25.4", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "be664c2a0697ffacd3423595d5edef6049e8946c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.4.tgz", "fileCount": 7, "integrity": "sha512-qesBxiWkgN1Q+31xUE9RcMk79eOXXDCv6tfyGMRSs4RGlioSg2WVyQAm07k726cSE56pa+Kb0y9epX2qaXzTvA==", "signatures": [{"sig": "MEQCIDQCY7IVWAd/180EOyLjF3gCMkNlXEJq0oP5HU+DXDyQAiBXOuDNY2kZNOZLXHRD57kUiaSkc57RhPNi3QY5Rodk5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67986}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-create-regexp-features-plugin": "^7.25.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.25.4_1724319267576_0.3256854531183231", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.25.7", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d1b3295d29e0f8f4df76abc909ad1ebee919560c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-YRW8o9vzImwmh4Q3Rffd09bH5/hvY0pxg+1H1i0f7APoUeg12G7+HhLj9ZFNIrYkgBXhIijPJ+IXypN0hLTIbw==", "signatures": [{"sig": "MEQCICybkgZqQ8Pb4I9KBmgNRILyTIGLvCw0WE5wRzdeGZ8yAiACzJtVvECw/b6Hr6QhNH5dT1mdip0PFiqNF4dL69wJzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75789}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.25.7_1727882114309_0.7314152766888584", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.25.9", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "65114c17b4ffc20fa5b163c63c70c0d25621fabe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-8BYqO3GeVNHtx69fdPshN3fnzUNLrWdHhk/icSwigksJGczKSizZ+Z6SBCxTs723Fr5VSNorTIK7a+R2tISvwQ==", "signatures": [{"sig": "MEUCIQDZUMcTxgELByXoqUe2VEQRbQIOGx3UXw5A5gaZnXZ+VwIgVCtXtDF6i9+mCI0UGq/K6UcjY9jGjIM6NDJacYzfBRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4919}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.25.9_1729610489564_0.7692070581145594", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "0653a3f85e6c85d175f81a1f7a815d72546322bc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-Ge9tQwYmdAHUoDauAcD3K1G79NcPRHQ+14GDB4uaWGGXCFa7doiOusAjQ5Ik/x2JryQ9uQvZj6kSnyMj3IqACQ==", "signatures": [{"sig": "MEUCIQDyaUcCE6TpH49vQ0EOQRRGhBiQz2pg7oyJF7c+Y4FQfwIgKYet1dyafFmB554rezGsc9AdlAh7gL8XqUKvND8XXH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.13_1729864470957_0.8926940803600842", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "fc45b9076be14867fb6d40fb1f0e55b580783610", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-s9azVTWUIhSETLn3KK94XCHJwyyZqhWbDZ1/5syMBFW9CfizK5Vv7Or8ynw4qQ5fhXlRLbYLKWxbBnz8yZa2MQ==", "signatures": [{"sig": "MEUCIFvN50DJ1E1MBrUdVU6zgEqysX+n2/+CwvYwxkKvMxcMAiEAojcL7+CqfXZPrVt5g3vgdwBaLiUNfdXI/Eov+qNsE64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.14_1733504060089_0.4504508402253238", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "e0ac33e3bffe49116c1ae1982addc7f36a74d792", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-X80x1XR86CVrpUTIOHMzmsGQxWdQLCZI92GGdnzuUK7QwMMCHqYiDGFE4g+jssunTnYC5GO/dd+JBrT4ty7bTg==", "signatures": [{"sig": "MEQCICeOfMSh0MroP0VK5BaCeukWnCg0m18MHfY0etU5+1sdAiB++wGz4Uo9v8Jephk4+/I+bDMP2BVaYBLvaXAzhr5D6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.15_1736529887575_0.20416284332525403", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "dedd3aad2283fb915570f283050917ec883a4cd8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-ubQd1G4uB9Nmq6lxOGIokqS6C8Kxe745eCwUirz/Hkjp5TzXBtWciNFvuSdsjr8/saV7VnztM4XzGdaOCusgtA==", "signatures": [{"sig": "MEUCIQCWeyxnpsPKmYcXkAcOD2gCkVTa3oKKAX+LYNDDj0fZXwIgV84cNMDKlsMI3jOculz2pJZrhv/MYwNNW0lrtq6rFSk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.16_1739534363562_0.2700637863434292", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9ebb2c4fb813d1c2fa27dfbdeec1775f0aa9f480", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-PsEjZgdrYITyxQO7b6VLSjCIP8CrMooUVKHyicwveyHKKCBLMiHKg/tspnpk4DnhJUhLhG0sPlN8iINNdiTZ6Q==", "signatures": [{"sig": "MEQCIE+wONwbIVr6EEIFm6WgvHlmly27awrb8aLUwkPq4wgoAiBtaI5jW8H+jgTK2aESDxxlcHHlJW3RSBXMLUzEG7hygg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4761}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-alpha.17_1741717517273_0.5245840764590344", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "6ab706d10f801b5c72da8bb2548561fa04193cd1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==", "signatures": [{"sig": "MEQCIBVRN6piyBpASraRKUdd2ODir94O4T7W7anf7glkRJJBAiBLfQhvzPAvocdtufSaJwRh/pLcojVf8+lRbO1OTV8b9g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4919}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_7.27.1_1746025753474_0.5181363772209302", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "cbdf302cc760eab25a903c61ed9d2b478799ad4a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-bhVUym3Z/MdK01vKXxkGAwIfTlVOGsYYcgVOIUVpjaZr/4jN24uhGlyeYJ2tDXQqTaxxaYWSSdoy4m7gM0iQtA==", "signatures": [{"sig": "MEUCIFP7u0FUKxYRv1oZLMacH1+wEmh/PdCVsImFAJwHWD4cAiEAthu/2LJTtoqVWM/8IVChXaT1GwelvZSh53DWSbPMP+g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4735}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-beta.0_1748620289669_0.4848437872388167", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-unicode-sets-regex", "version": "8.0.0-beta.1", "description": "Compile regular expressions' unicodeSets (v) flag.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "type": "module", "_id": "@babel/plugin-transform-unicode-sets-regex@8.0.0-beta.1", "dist": {"shasum": "93af7af348908494d8ad1ba648a6c288d4d6aa49", "integrity": "sha512-QNd8J4c6m9TNRfXNWZTOpBAD/tIusg83t636Qle5O0GFPehknFFLjsunAc4bLLSKnC0liPYk1QVk532phoivAA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4735, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCyzPVnsDWElOnhkgg0YzKc2b+tqUzxvMyKAjRKQPfkmgIhAN4/iifgEzDzJu5PQptibbHaD9RXxWKt6uVKkSoZvId9"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-unicode-sets-regex_8.0.0-beta.1_1751447074142_0.42812324677468605"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:41.627Z", "modified": "2025-07-02T09:04:34.600Z", "7.22.0": "2023-05-26T13:45:41.939Z", "7.22.3": "2023-05-27T10:11:03.071Z", "7.22.5": "2023-06-08T18:21:39.921Z", "8.0.0-alpha.0": "2023-07-20T14:00:24.826Z", "8.0.0-alpha.1": "2023-07-24T17:52:57.629Z", "8.0.0-alpha.2": "2023-08-09T15:15:20.700Z", "8.0.0-alpha.3": "2023-09-26T14:57:34.169Z", "8.0.0-alpha.4": "2023-10-12T02:06:47.408Z", "7.23.3": "2023-11-09T07:04:04.192Z", "8.0.0-alpha.5": "2023-12-11T15:19:39.465Z", "8.0.0-alpha.6": "2024-01-26T16:14:38.514Z", "8.0.0-alpha.7": "2024-02-28T14:05:40.785Z", "7.24.1": "2024-03-19T09:49:11.305Z", "8.0.0-alpha.8": "2024-04-04T13:20:17.573Z", "7.24.6": "2024-05-24T12:25:08.078Z", "8.0.0-alpha.9": "2024-06-03T14:04:52.358Z", "8.0.0-alpha.10": "2024-06-04T11:20:28.933Z", "7.24.7": "2024-06-05T13:15:43.403Z", "8.0.0-alpha.11": "2024-06-07T09:15:53.456Z", "8.0.0-alpha.12": "2024-07-26T17:33:47.572Z", "7.25.4": "2024-08-22T09:34:27.735Z", "7.25.7": "2024-10-02T15:15:14.504Z", "7.25.9": "2024-10-22T15:21:29.764Z", "8.0.0-alpha.13": "2024-10-25T13:54:31.199Z", "8.0.0-alpha.14": "2024-12-06T16:54:20.242Z", "8.0.0-alpha.15": "2025-01-10T17:24:47.758Z", "8.0.0-alpha.16": "2025-02-14T11:59:23.741Z", "8.0.0-alpha.17": "2025-03-11T18:25:17.439Z", "7.27.1": "2025-04-30T15:09:13.657Z", "8.0.0-beta.0": "2025-05-30T15:51:29.833Z", "8.0.0-beta.1": "2025-07-02T09:04:34.328Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "description": "Compile regular expressions' unicodeSets (v) flag.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}