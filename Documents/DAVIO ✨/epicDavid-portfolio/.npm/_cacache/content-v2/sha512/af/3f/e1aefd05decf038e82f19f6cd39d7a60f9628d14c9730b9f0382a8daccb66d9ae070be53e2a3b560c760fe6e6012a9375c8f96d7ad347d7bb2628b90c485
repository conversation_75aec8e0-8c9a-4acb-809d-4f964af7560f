{"_id": "@babel/template", "_rev": "132-a77ab838f97c64bd37f37ff57056261c", "name": "@babel/template", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.2", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/template", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1c6f73a1893ce929287354e2ca5c9b3cde0d759e", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.4.tgz", "integrity": "sha512-1oCfesMjxhJFfIq4eEI9hOR6nBleUjhMzPWeWwfBc9ZLt9Wwp9SnEIe1yegTQ3F2dv1qQaFCPz/bYSRDH/KlUg==", "signatures": [{"sig": "MEQCIAD7Q8Y3/Oo8tWOrvaG5Y+or9NFswgDiD+qLWjx0SVagAiAuZycbyFf3oqRF6dA5Xr+exKkfQXRMPG4a6aVofrCgIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.30", "@babel/types": "7.0.0-beta.4", "@babel/code-frame": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.4.tgz_1509388533643_0.49645234481431544", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/template", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "400a58ee90230d77d5cd7f2fc5820b5d2f0ae18d", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.5.tgz", "integrity": "sha512-Qi13rtAl+X/nt6eJlvASlxvk2nQoLMtYN4iijG2ZbMCDhLkWcyIVzwNFIUDwQtl6g7oz1rifCKVMXyv4b1i5xQ==", "signatures": [{"sig": "MEQCICZVaJigWGWCtfo6BGiukGHGHRFmlkqM+w8iMRZ8+dfQAiAb4uLsfEct4mDyO0oHqg6DHzIuA3mkf5CHP4FcO0oBYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.30", "@babel/types": "7.0.0-beta.5", "@babel/code-frame": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.5.tgz_1509397030264_0.0010916688479483128", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/template", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "577bb29389f6c497c3e7d014617e7d6713f68bda", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.31.tgz", "integrity": "sha512-97IRmLvoDhIDSQkqklVt3UCxJsv0LUEVb/0DzXWtc8Lgiyxj567qZkmTG9aR21CmcJVVIvq2Y/moZj4oEpl5AA==", "signatures": [{"sig": "MEUCIH3rYnMnjIqzyv9XEIpFNql9DKVLQzS2F6Q4c4y2OiJbAiEA3BcYRPR6oPj8GcvrsclLdn1ap1MOcF0Z1b++dpQXA8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "@babel/code-frame": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.31.tgz_1509739439952_0.20948258810676634", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/template", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e1d9fdbd2a7bcf128f2f920744a67dab18072495", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.32.tgz", "integrity": "sha512-DB9sLgX2mfE29vjAkxHlzLyWr31EO9HaYoAM/UsPSsL70Eudl0i25URwIfQT6S6ckeVFnFP1t6PhERVeV4EAHA==", "signatures": [{"sig": "MEYCIQC97w65nn/btPjdTP7AxSkPJcxxJhgTR6TdEOUY42JJrwIhAJqkt5vXqc5Brn4GxkhomBhW4Z3VJHbqymlxxlKx3Euo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.32", "@babel/types": "7.0.0-beta.32", "@babel/code-frame": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.32.tgz_1510493625345_0.10954077425412834", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/template", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "55c5fe149ac005fb65f42dd6534e4b4729dd3ee3", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.33.tgz", "integrity": "sha512-fJptoRZlfyfs+LV6AVvAlq/m0j5BqU3Y8+5diD/SUCHvshJlkh5+9gW+QFVzxLldaqDkJVUEj7rv5DZ9QeyVbA==", "signatures": [{"sig": "MEUCIQDRS3O2rPYjF03v+r3qhQpKJcbAi9/eeaSg8/0Rle7i1QIgDYh3JczhvbrNSvEZSJWfBXWCxQKdY+JIgt6wFBd+CSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.33", "@babel/types": "7.0.0-beta.33", "@babel/code-frame": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.33.tgz_1512138535117_0.30046806181780994", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/template", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "35fa7eb6c540f4619927ec1c1b113719ecc59446", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.34.tgz", "integrity": "sha512-dldLn8DUQEb3aq8bIiMcmffKIKn4jetne3cnzS2nk0LBSm/8Uhh0yQc4REk4uRs0Bz24iUBdlrb/9cjDqTDgow==", "signatures": [{"sig": "MEQCIDZfVNZmwLki6Cju9tt3+AiiVIRIzic27ADQzL3objZaAiB686QXIu3QK79pEbHGt/aOk/pyGrPQA2WSwBAczWU0Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.34", "@babel/types": "7.0.0-beta.34", "@babel/code-frame": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.34.tgz_1512225594978_0.3722605281509459", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/template", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "459230417f51c29401cf71162aeeff6cef2bcca7", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.35.tgz", "integrity": "sha512-NLd3Dfs8hmkxPvaD8ohNtEp9WXp48lxpW//6fXcT9bJWIO3isrH3OTYL9kjX7xFPPasJ1E9bUNSaPFUUgvPZSQ==", "signatures": [{"sig": "MEQCIGy9INyWmStr/tEW0Lra92OfYKx5C19k4VZ6hvqEHfpuAiAF83/VqMa5PK2Rd2Xt0JkUY/VjJVwgWIdkBwcAfUBhcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.35", "@babel/types": "7.0.0-beta.35", "@babel/code-frame": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.35.tgz_1513288093571_0.5508315046317875", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/template", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "02e903de5d68bd7899bce3c5b5447e59529abb00", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.36.tgz", "integrity": "sha512-mUBi90WRyZ9iVvlWLEdeo8gn/tROyJdjKNC4W5xJTSZL+9MS89rTJSqiaJKXIkxk/YRDL/g/8snrG/O0xl33uA==", "signatures": [{"sig": "MEQCIBjiijjRzJE5icozv7bw4X74Wm4vbKWWAo3ifUo/T//YAiBAR7GeaP3bFzSzcGaFS8CuOCAbwRWqfEzZScaVoVUjsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.36", "@babel/types": "7.0.0-beta.36", "@babel/code-frame": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.36.tgz_1514228716223_0.6133986939676106", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/template", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d51a5c8c7bfc1ff08f3623231b7d388a9e9b3570", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.37.tgz", "integrity": "sha512-5naM+sRnXLQ+cfOVSjsqdaqVn5EPh+M4Uv+qJR6G5dtuos/RNINp23Y2etDjp/BmMfEalrLXqAaVD3oAscYTKQ==", "signatures": [{"sig": "MEYCIQDYFOeW8CsQXcq4yUg7KPF7dQLxlBJoio6rqzoCyJqC8QIhAI37FKulUqp+6ES467eTSeKla4dtWmRsBoHvY9PAGAxX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.37", "@babel/types": "7.0.0-beta.37", "@babel/code-frame": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.37.tgz_1515427376433_0.8377085751853883", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/template", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8a2d403a01da320beb8333dc6403500fa79e8597", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.38.tgz", "integrity": "sha512-ygOSe1+ekKVkn5zxCH0rqv5sZtvLfC72yPQSaXLTHtYSYdAyXNqOW7q1ua1zJll9glk0UWYAnUEcehQXXc3fEA==", "signatures": [{"sig": "MEUCIQDsfx3DlYYt+YcT3Zu1MUsuH3lKGcgE2h64LmMuPlP4wAIgH7cMwtK4iv6qXhRMmLnQaCnbiLZy1lv3MfDMWEJaoCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.38", "@babel/types": "7.0.0-beta.38", "@babel/code-frame": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.38.tgz_1516206744542_0.15441198414191604", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/template", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "98bd7b132d99f73547c473f2862f481ae84981c9", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.39.tgz", "integrity": "sha512-EDVszuqo0ZtNeC6j0yFrWIW1Sh5v+pv0POnj1xjPHy6gsg1U5IYjcc6uVarFgTCmtWsjFB5KSRqxC7N+YYcwCQ==", "signatures": [{"sig": "MEQCIHF3nwIv6N4n9H49chXLWdWkjQkuPXLOWqC4rSF7JYLJAiAVDUvdmxs3e4ax54XDWdRrlex8T3uZ2NUsEt54LFpOWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.39", "@babel/types": "7.0.0-beta.39", "@babel/code-frame": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/template-7.0.0-beta.39.tgz_1517344073244_0.39974867436103523", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/template", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "034988c6424eb5c3268fe6a608626de1f4410fc8", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.40.tgz", "fileCount": 10, "integrity": "sha512-RlQiVB7eL7fxsKN6JvnCCwEwEL28CBYalXSgWWULuFlEHjtMoXBqQanSie3bNyhrANJx67sb+Sd/vuGivoMwLQ==", "signatures": [{"sig": "MEYCIQDLZRGlusYvlJb1FTOMeckt4S0nh3a7GiFIgcysa3PB8gIhAK5nCXo/YNQlOzLzPb78Jaf1MyWhDhMMKaC5NMo9UrWR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25051}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.40", "@babel/types": "7.0.0-beta.40", "@babel/code-frame": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.40_1518453745453_0.31040931355775325", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/template", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "628eeb93f9b5b423a252d3b6183f12e09505ab55", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.41.tgz", "fileCount": 10, "integrity": "sha512-EiFgrLPJQ64+Im0+fg9fHcos0eooRG/iuTM6vlF4X9b+j/9Z/VCZejbvgqLYtuseT67GKQHEeEoXM07HK6in2Q==", "signatures": [{"sig": "MEUCIC0y4QoljqTV9kyHaIL+/92KE2UniU6bLCpvvtotFDB7AiEA3hUKzi4p38H8Dzs18xcStRcCxdlLg+U7HKNUOAJ9+qk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25053}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.41", "@babel/types": "7.0.0-beta.41", "@babel/code-frame": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.41_1521044797116_0.34970186656805136", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/template", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7186d4e70d44cdec975049ba0a73bdaf5cdee052", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.42.tgz", "fileCount": 10, "integrity": "sha512-EK7YdTe47j4VxlwNvz5bnlk5Jx/wWublnqfgOY2IuSNdxCQgXrLD34PfTnabGxywNSkJkcSo6jwr2JGT+S48dA==", "signatures": [{"sig": "MEUCIQDSww4i/86nDEYwoQlLzkeKPYyG81pR2RH9MSmLOezCgAIgH1i3tPHZa9wub1uNhtFLukajFpPEuI949OTWvFyt9H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25053}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.42", "@babel/types": "7.0.0-beta.42", "@babel/code-frame": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.42_1521147110420_0.6918832603367278", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/template", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0d34fa1da16835b23dd136b942c753ad97540c24", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.43.tgz", "fileCount": 10, "integrity": "sha512-EnFZw/OGzr3P93SMZdJdcCrrFmfeerPpaZA9YDK3g1OT6ho7+3dvAxu+tDJ8DJEAFfDh6XRvg3R8LjqeyZKIkg==", "signatures": [{"sig": "MEYCIQDQh46DgMa8UsmPDX9bBSvg8iqUHyqZMAb55hXAeQI77gIhAOGgWqrTAviQwLab7A8KSgyqYTEd2ynk8ulZiAFeccir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23861}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.43", "@babel/types": "7.0.0-beta.43", "@babel/code-frame": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.43_1522687725462_0.6520247797386718", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/template", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f8832f4fdcee5d59bf515e595fc5106c529b394f", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.44.tgz", "fileCount": 10, "integrity": "sha512-w750Sloq0UNifLx1rUqwfbnC6uSUk0mfwwgGRfdLiaUzfAOiH0tHJE6ILQIUi3KYkjiCDTskoIsnfqZvWLBDng==", "signatures": [{"sig": "MEQCID15uTJYoaCgP4sg/JTSizakIMeItZ1DVcLJntx1Bh4BAiAQ5gRdREjThCYE0r+uU5Pv4lAF+pRAgPaeocS+0kHM8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25804}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.44", "@babel/types": "7.0.0-beta.44", "@babel/code-frame": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.44_1522707626275_0.13392116826667722", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/template", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a12ef8d61cafbdb0da0d82210e8bcd81533c8487", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.45.tgz", "fileCount": 10, "integrity": "sha512-2MYzewkv9DMmvm3Ub+hziMGg+/KvN/Vv9eXqq5KkzJB4OgRlacE/cUV7qXSkEpbDXSClkzHEJXt+oy+uJgCIbg==", "signatures": [{"sig": "MEUCIDWWX8/bKQrTw6pt+FaHDSGT1jIEbBbSwSTWnubRAKxXAiEAoRC3LrbW6k3myCeXAHB0FeAuOcdZN00R0d/IyIeG0LE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2lCRA9TVsSAnZWagAAc50QAKQaTBfxtqJiHBAWI2It\nOu9vwuGFrQSaa3oj9zOazdIdlCgdw9boVcaCI5vrlBHMkidyz2nzMGBwz2Mm\ntNn9IzhSiyNLd5Alg4wea6DIqTXrPDhLbkt4IdbLBtyrcUsh4r1ERKtIMjTC\n/MrDPFuU9Wm9Xl1NqUtam3y7C0Tl5BNJCfxu2ArCdzJXtEvSMtx9py3Fio+i\nfU60Y61N1DEwhMsY61dbbB2JzleAh3N/cmZUqCaV/8GRDll1UCPlHY3Uv2DG\nlHqPj8N1nUOjav+WUndJBf4fwY26YXjA6pEH1SW0Px1dRI1ejP60cC1gCnv1\nbo9WD+XQAxbxuygJ5+bG+e2yZ004CoWDNO4v4Pl+dEhlUZZV6De0bieScn/j\nr7MrIMnusg7whVpD+SWBH8pEXjcGAMdwtpx/apCkGvcjmT0W+rESWzGG+qHf\np2AGD2mNd3VHC8VvRfMxRATHZ42uNWx02PHNDpBR8FJqHrjZoj+NlRx8E1qG\nHlQzKLtvUy9vFr/1Q8fKfzC3x0d7KPQgEHGH+hvW1+sNshfJOI/xBa7QAmjS\nTNYtLqpYukCYFHo4brQPcWzRR+QHwBCPy4AIprA6peCqy/GNNLXs2/niE7G5\n5jQToVc9CjtzhkCjD9Zw9NzeQHhc7X1fF9RX03IwZIRxZcVki2XVRDqdkBlF\nNBmu\r\n=XDmA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.45", "@babel/types": "7.0.0-beta.45", "@babel/code-frame": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.45_1524448676972_0.49216790410003486", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/template", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8b23982411d5b5dbfa479437bfe414adb1411bb9", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.46.tgz", "fileCount": 10, "integrity": "sha512-3/qi4m0l6G/vZbEwtqfzJk73mYtuE7nvAO1zT3/ZrTAHy4sHf2vaF9Eh1w+Tau263Yrkh0bjVQPb9zw6G+GeMQ==", "signatures": [{"sig": "MEQCIFO8p2ERAOEgXo6dpz1iN2Uv+l2ihOPGGchnEyf7nm/zAiA0k9ypCDzX5/fw0PSDEbvSAxYw8fdm5C/N6R8dcEw9uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHQCRA9TVsSAnZWagAApHEQAJgAiL61mZdiR8xTRXkR\npa6Nx4ptc6FpDkBfWJH+kHu7C/0vVugorXKAh9rkAyJG//Va6DGwalF4wL+X\n2F56DFlC/dCvJFt6PteAbUJa7QIkQOP7OHhDoOQMQVdbYoiEeVlQisWEZ5bC\n6pyU8GszrLnwATXn3qX6PAkdp3FotpDuF9AedDRgPWsWj21Mxpm5h0DmFiNl\n2dkCOdfNWF6enSCI8RMHMV8Akb0yHRFjfMuWyUEwj9eujzGWoiJPCI1LU8I5\npVdfnL1pix/8764nV6i4XtaYwJLsaUmuXhG95iPjc9IDGONGTIbkv/JFpBIY\nyHKlYwH/C9glrgJp5Z0I6rNQDC8m5iDqVxnS1Esdb8P9taigY6IxrxrOShT0\nF8PBr72jlUDAeXWMJYivpTPYiaLnVGmZUcE4kB2SfIPsmWj8eXVE8v+NUO9x\nWobTfUffaZSWSRL4vFmLmscT+6iHsLscyLYRUGOl/YXiYA7VEExcDuZv4wA9\nRn/Xw+wDrq7t8Hnb1XukpyWmMrZqBaQfz8dN8CRYmrTAtSwIrNPpvuqBLVKe\nD0QU5XSK2PUh29E5a6JOZhvSZ2oBeuEVSAsbJtK2TetaNGtwJEyqJ5LXpKkB\n7pdBUaaOr1O9LhrfHRmIIuOwgjMcaARl00qSKuRT5oyTXXKInt6dgElDofzg\nslYB\r\n=eQBn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"lodash": "^4.2.0", "babylon": "7.0.0-beta.46", "@babel/types": "7.0.0-beta.46", "@babel/code-frame": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.46_1524457936325_0.8401393552076999", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/template", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0473970a7c0bee7a1a18c1ca999d3ba5e5bad83d", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.47.tgz", "fileCount": 10, "integrity": "sha512-mAzrOCLwOb4jAobHi0kTwIkoamP1Do28c6zxvrDXjYSJFZHz6KGuzMaT0AV7ZCq7M3si7QypVVMVX2bE6IsuOg==", "signatures": [{"sig": "MEUCIQDJcPkkpZpWcVXbj8sGZjNv5ucQLE9WUFN2ZfsIepshfQIgSjAl1Q4Pzmlb43SvhlL4RyOvGlMyCMbW7XcJa9OyM68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVkCRA9TVsSAnZWagAAA58P/jU5M9DB04JmNIToMvon\nN37kZBFrjqHe2AEVdCfoN/FwNVQqSHJPkMTAAMqg6Z9ePIOUjPSyn+LsDLXM\nkajjSNLQ9E0VQMJP2Xp8vughsQRcAVlkuZMfwiptTQnwxihXXNcqvUQ2XIMw\nsvpH7OX/XEsny/n8buvqbbsEcBGOl80LAiW0AIU2UjnV6E7tA2N4MkWZEKAT\nYlE/63DtKXzLeIsvFIPqucRo+10vFFMiaZ/O0MVgfybeNUtOagdwA6dsytoI\ncwObI7px9auJx9HQH5pAuBP8AIl1QIeBFif78EjJbAyqc910gOC/RUeT7Wpm\nWyffwWVYEBNX2/U2XVtdy8c4gidjGu2+7c7QPz3YuDqWpVNaZJ+8LdKi7wra\neesPKkNFWMOrKobxLi/dGlziBVhWshrhvuAhQfVxom36hxLbV6qQ6pMx6N5d\n8TUYljl2ivD8JnYZ2V2wq8RjbHt0IqDje4G6FEit8XX4zigocCDIB3CCNqOB\n2XCoupLC3VqC37uLgskfZi8yAy3wgjIHOEePAGeBKrmBlOeHYt2BDH7yL9k0\nlS6e3rFZZWKvzcOdHF+YkdDpBc61pBQuFjuMwMw1mrO+vY+WMduP1JRenY2c\no+WWyjcHFBNyRkyNwvYczQ1r8ObGBL7ZP5s4zmvz9mk2L3bTNoev0pEUPznD\nA83R\r\n=SSyF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "babylon": "7.0.0-beta.47", "@babel/types": "7.0.0-beta.47", "@babel/code-frame": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.47_1526343011762_0.3708117451654258", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/template", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1792741eeabdee09687d24dbfa47fc5437aebd66", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.48.tgz", "fileCount": 10, "integrity": "sha512-mFrSP6UZR02YGsqlnH/8IUENCnqs749RfujSGQ/CqeJYpeZ73PAJqk1Wg2khl+Huv4L4LW8dJh7qOQ/CBkRqEg==", "signatures": [{"sig": "MEUCIQDFETdwssaYhuc6FzfkJZnDcY7dqcYGGOsHsmuOE73DKAIgGBMoy7qQO+rmlsI30nrJxOxWIqjxs5QpKtwGpsWoxB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFZCRA9TVsSAnZWagAAn6kP/izm4QcjEdbwh8mfukaM\nv2rjVayIGn0op63s7HzVGv/5jq9xFKUvE61TBSRJBz3dYEV5d+N5/LNa/dub\nlk++n4gR4jeTEEKO+9MECj9zgMbC0lVs8v9tsBfoTVvtjJt3RjpvYajDOmSk\nhasS/62wgz23So5MHKXhKvKw+PfuTlUyB9Tj/rikvsUmZ7pTCx8rlCFKLiJA\nSSE+JH02t1dBtJAdri4C0GCGGgfGsF/1TGes9sVNgZVL1eseyHuG5B87e5Cf\nMRaflOTWHfAvfy2gHDt9dhiHmmVbalx7vh6EBg/E24vWRn8+N103He1DS/8+\nGmfU94lmlqcZxFIi6UHsEB2O+3AAeXidQkX4+JVKVPHO4eHAUaUSUPN9JAwA\n8Ac/NoYJXciU4AFp1HSciDo+i795E651PccNvYFC6nd3S/8/avz7ZlkUmJE0\ns//znjx8CazwCVFMbsrBoTczOfDAqVtAWIlqpBsRXaADQ+3S4QMRFik3sghG\n79ql4DfrgF8WVX90+JWeKNuTQqtKB9m/KRrZ3KD0lgEI4zlB5E8st7io17K+\n8uLEjXAMXFk61uqNykD6+YPoDev6BbbqEHf0Rav+TMzS/+E4mNewj0cBxtw8\nVtY+3l2PwU564EaXMunr5Gho1R1gr57OLDiQvHcQJwEDnWYZ4kxDlUirRAPd\nlRy9\r\n=Kveq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.48", "@babel/parser": "7.0.0-beta.48", "@babel/code-frame": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.48_1527189849329_0.03685678742695586", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/template", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e38abe8217cb9793f461a5306d7ad745d83e1d27", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.49.tgz", "fileCount": 11, "integrity": "sha512-kUPxWKzgT4yRfjkeMW/18T0eB76ROpfrV3JozVrQmr9TVmB/GZs7zty7gA1Y9rb7nwZKxYC2piyTrR4DnF+5+g==", "signatures": [{"sig": "MEUCIQDoxkU66xSuds0UJAZzDcOVRBiJUbxTmFyHSOFNlUa89gIgGNdKZsFP1lxz7lQ1FB7GWv7god1mxFWIAXWP5QC9ntw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPbCRA9TVsSAnZWagAAhccP/1H/lfkogQ/2SRlGcWYg\nnDqhnKtKjqzcejsrdE4E2ElP25nluEIQ0hHU6X3FFVw8lrEkh/He4MWzKwFL\nz50k3sQUFIQQ2Dnjn+J/wmwGlyAqL93zXdOJLZFq733tooOc+v2LmyHnFWdK\nZbyCBnDtRaMb2Tzbo8tR/j6sbL9VDyAAvl7uaZm6QEiH9rG7AbCnlXzD3ugD\nz5rY5LpYJyAgLHVTO3/5nmZoewIA1SqUXyqccv4BIbfQ/T7ee8Pdr6+vJPp/\noqBPAAOpj3aAIC/+XnA6MSd6m+5F4EgvUjefzzJVVw7YCFXFbQVt+RdLHQKQ\nJmyzy9Ht/kEtDXHVEPhtgIRKVJ64zjZRl5GrM7SVasb8b7DFgU0M8rAlJlCm\np9w9Ra6/CHU4+d8EAAyXyyBcJSIuvg1NqyyOiJyTKnwOiSYaA5Vkq2pWXmEG\nR38g6Bk7skWqw/7CoEaZBnBhFIfHYGG+X5Al2NvgbSyx9kFDWpbN/45hErhj\njuOVRYug0geEBtAYlg3XoK46N/oHV+OULDxEunbTUfy5oE/18PBRrixZSmdK\nhAOrqQOvso0NJG6I6+rxzLJLwLgRBcw6tMcdpLLO5vBEpr0DrVnrBR0DfEU7\nRvQC1dmqG5BbahUtjc/UFP4A+grWK55UTnuIsiN0GRbVSJIJtqXaQrT7byNN\n6IGh\r\n=Lh3h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "e38abe8217cb9793f461a5306d7ad745d83e1d27", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "3.10.10", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/code-frame": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.49_1527264219632_0.2897897284488067", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/template", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c534b70d3510f2bca7bccaafbe0885220edd9e47", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.50.tgz", "fileCount": 12, "integrity": "sha512-9h8v3lTlcnfo57M7FVWq2ZaaJry9ZtG0uxMB7ErXvWmfj3zOHKYIIYZV+T0Ecx10jtb+yY3VKBPPUIjgdSRV/Q==", "signatures": [{"sig": "MEQCIBgwZNHWoQHllo0k29TuOp/ldIzPHRrinXejuzTa4JpGAiALqchYFYwLajfOntHhXmUskO0iZ+V/0v1TtREC75IxqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20160}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.50", "@babel/parser": "7.0.0-beta.50", "@babel/code-frame": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.50_1528832866686_0.0033106983590218686", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/template", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9602a40aebcf357ae9677e2532ef5fc810f5fbff", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.51.tgz", "fileCount": 12, "integrity": "sha512-vFaropWUPgRrjBySSlOwhTBPDiKClblPrX3TBPrDnL4zyN17Rr6JwnmpsazK8juLwv8Qsf6fdOdDl6eNVh0edA==", "signatures": [{"sig": "MEYCIQCExnDOu5EKlJ6moadjvYp+QpsN5ByAX9OexJn60A7iPgIhAMATIW5SrUrRGeBCZ3uwPv481s7Dx37fNwG2sg1mTTut", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20160}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/code-frame": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.51_1528838424893_0.7294445366796298", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/template", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "44e18fac38251f57f92511d6748f095ab02f996e", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.52.tgz", "fileCount": 12, "integrity": "sha512-XSqa4UhgqF8lP1eqrPqYxqbmW3qIqmSyyTm3CW8RvosU/5aGP0RqQ5sGm500SHDAQ4KlzhXPyKC2L8yNIv4D/Q==", "signatures": [{"sig": "MEQCICjbpAhpJq4zzc+ENYtcASL0nsWyDPHZNbQ8dEMoRR8WAiBN/MlR4VrwO5qsu7mNCPDyEA4ZDBNS7F6iCCfixQ8GXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20159}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.52", "@babel/parser": "7.0.0-beta.52", "@babel/code-frame": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.52_1530838778757_0.19550432741747947", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/template", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3322290900d0b187b0a7174381e1f3bb71050d2e", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.53.tgz", "fileCount": 12, "integrity": "sha512-MCZLPfGfNBHdE5wNfY5eK1hpY3fyq8zq+NfbfFCUtIzHl7SfUzHzH8rKPBXSB2Ypetq2sBHdDyslSSgnG0Watg==", "signatures": [{"sig": "MEQCIEwvCqSrn2djXzvcXvrhEhNwoHxu18D9Bvp3XrmbaHt4AiBD3nvK6W7cAzXydbyumLzUqmIi1bZv4sbKqWKk8CEZ/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20159}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.53", "@babel/parser": "7.0.0-beta.53", "@babel/code-frame": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.53_1531316438299_0.17394379011193295", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/template", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d5b0d2d2d55c0e78b048c61a058f36cfd7d91af3", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.54.tgz", "fileCount": 12, "integrity": "sha512-O2mMamX6TuGftCRCtIhmIQJxLNghsUjrOK/VgMxTmc1B6XYg0cp2jqOxF1kY0j8xiPf59J7ejGfqEtm55VpnFQ==", "signatures": [{"sig": "MEYCIQDP3e9rt1RvZX/4hrs1pe/+DTLKzq58IKvAUxf/X+zk9QIhAPNO/uor+PeknekPOg9LhlVkyDB0RzjfSxxVJl//vQqM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20159}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.54", "@babel/parser": "7.0.0-beta.54", "@babel/code-frame": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.54_1531764020734_0.35224206075566555", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/template", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c6cab0e2722ba5e33fe034073b6d31673aba326e", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.55.tgz", "fileCount": 12, "integrity": "sha512-CyXscoMFwh6IWtGixSRon+cFHG36WRDdlQxECAeQ3GLp9EBWb59caK2xV00/saE0r9HAKtolEa2LQuVkuRvqrg==", "signatures": [{"sig": "MEUCIAfPORFyThzx2RBqvIeQZFkAl5WUc2afhvGGm8J9rJvhAiEAzWEqSDqpAiYk+1/TWjy5sKXHdNA7creeO1gUkllWetI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20160}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.55", "@babel/parser": "7.0.0-beta.55", "@babel/code-frame": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.55_1532815663521_0.8432258365467495", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/template", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a428197e0c9db142f8581cbfdcfa9289b0dd7fd7", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.56.tgz", "fileCount": 12, "integrity": "sha512-rsR9K18h0oiJTUmS/ICYREbV8qhPTic4SIqDSkzv9xOxupt7dKj8hWmZQLGPySO5x6cdn8py039o1wPQnsEGHg==", "signatures": [{"sig": "MEQCIAOlkQ9mwSu/cktbkm3B5tEii5idY6YLr74OKLZMB2jaAiAPMQwC2c2dBO7+/R9Eve3Pcdzh50k2JrpRU2uNUZIJ8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxiCRA9TVsSAnZWagAAzcIP/ROFQ5QX3wsAeADjRw2D\nw0keliPE0e4iyiQLQm+YrIHmshjBnPiB6FZfchAQHBSDaKDIErECbq571h9D\nDNcoWMBUxWdIYEAKcLwcS4ZKZYT3bfbeCafVdFsD4LNhEzZjVdOJAlWcEwt3\n0M7CBR5Yq5fcFy2g8IjKJ/MBgfxUHi+ppTY/Z/kt5C6MuzE/4y71PXGdjAhc\nUUGROepqh8sMWRfZJiU9NUQ6P+5f80Z1DsSV3QJjUikDGt9ESSgj47nBaUyN\nDWxXpjPCMWqEKvdyY028yno6SmYeSaoAKh0C1+/OBh0pM3iNUSbgadvoRLyp\naTFjFEuhHma/BV++v9uQ6t3QU1Nr792cr0sO1zZC8+4c1qA72vasiRVDu/tD\nI8QqHkgkhhU3+az0vNi4fgQZWYS2GAKdP6IuNiJZORZJZsHdYluLlZCYf0FK\nij/C58vgp84d555cmKhY9BFSTzlRzzw0w6DFEUB4rlbfEZKJZsEDTZ4UOo6C\nxgLpfp7ZeRMO3eoJyeBY66MAbqmW2jk3kbwKBLDYcbRzJuxirkEI/fcFO59d\nm+BIF8dEr/LjUpyYtShPDwFDy6jRY7ZVeyWXPA3GyoLOE49eMo4vxjdlzoU1\ncnRgX46MPHG3AkzsNKihPI6Kfh/L1YXGLPYX3ZmBugNFPtVwWnBqa4D7AM8x\n4hH9\r\n=Ccdw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56", "@babel/code-frame": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-beta.56_1533344865690_0.29637059352641737", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/template", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ef2e7d5bfe1681909dab445a97dcc097d68bf11c", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-rc.0.tgz", "fileCount": 12, "integrity": "sha512-xyCTQ5PlSUn0SSwp0ea7zf9xv19UHTB2UGHNE8INqNCIlUBYK+P57DikxBE6cmc5bQTsw/O8uv777RYrTh74sQ==", "signatures": [{"sig": "MEYCIQCpXJ0j3C0B+t3evysuZnNvHUHnpHHJiW6dead7hMJ4cAIhALgGmH+Wv2Y/qqO/hisSJe44GXs6kSN8amYX1FLCEZwH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTcCRA9TVsSAnZWagAAvRMP/2/+RujVIN2/6+HJi3yW\ngSYXYr2JD/D+kgRWeF1+JXtX7oXRJN5v8DTbKYm9EleV+VS5sLPFCbhRugsi\nDZZElHl975CUCEdNv7AZMNW4KkC1E0eaxkSr6fcQajuKwmIFOBYHPgsw69l8\njS2QQSMyMM8exhpIpRvjUSdB55/oz5IGpc3aC4SQZRCGKyNYnS8vQn+VOheD\nqRFEoXckaEGv0hA7jMOe6x/4kzu8VZpVPB8SWMYKefY9sSqJoDP3ijF3z7Qq\niDUpaf37fg7XAlRqNJ1xXotX5Q5i3ikLkB9mhkRH3WoQqihPm4ba3sgks3r6\nEkLBuHY4ajOAjpNLQEXn70tlYXdXybl6KLV7aNpf8Xat6sVqbGoLGmgWwFqh\nQ3zXmNbYu/pzTyHPDfC9SlTo8QcagBQ7NKd0mWRrJYvl/lELVxiv6iO42XwZ\neDPVBMIp8GzHfCNRrfqqbsI98vNZYk0r+kZS4/DWibranGBpyXK95cY+vAA9\nDuqNTgI/6c+1DRcBevslvHqXTXSH7OrSQ/5uMeIA4NgOl48RBmmBz3UBw2ps\nN+Yq40Huyl+T6E2T2bL9g188C3XeK7yzwr0MobL+h6tU10RJkG9bdkVtwVKp\nc6OgzP3ktMMM1elhKvqkYVfPNX3mRWD0TbZtMssXgeVbTrZB2qdnZdnk14J1\n178G\r\n=fAQ5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.0", "@babel/parser": "7.0.0-rc.0", "@babel/code-frame": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-rc.0_1533830364084_0.525186667452183", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/template", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5f9c0a481c9f22ecdb84697b3c3a34eadeeca23c", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-rc.1.tgz", "fileCount": 12, "integrity": "sha512-gPLng2iedNlkaGD0UdwaUByQXK8k4bnaoq2RH5JgR2mqHvh2RyjkDdaMbZFlSss1Iu8+PrXwbIRworTl8iRqbA==", "signatures": [{"sig": "MEUCIE0qHWwR9I0PtLiAdZ3X7Bo5tW450yGjznuECIpox8QlAiEAizjkB/rD3krOeAsCTc/r+h5zGtP/K6AbhZuaNvRo2fQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9nCRA9TVsSAnZWagAAP0YP/iFkr6X5/f1HbPrSRYX/\nqvj2hQXwYpHdd4PnPwjcrS7Nklr53OkfCK76CE+sZwSNkYo7+MMH4dNRXRDB\nwm6F6O1AE6ZJhaHvcxpgHxxmcFKoR5GP+TW5xU6Y0Kd9i1rdmXWExRsytgur\nxvby5+T2X7iCsJctdFIrKjw+lFup0/UZIrekuRH3kG5s+47URVsVY5Udfv5A\ncS2lp7MdbuwMkl8dj1TTq/0d58tssIR/8rxihd3lO9XXnIMxLJhlBvHP93FT\nTl35oFOeFYi/sZIVVoaF9gxVJuLgrFPEEfKGSrkEXpjWn7vLmeOyaBFnasd1\n0ipIQ639XJpQLOZ5N3BMW1Tk6w0Z24Fp/j98chMf2LtrMXAxignl0GRFGXqR\nou/Owh7EfrdaynFmR7DAt/+mW4ExDEDXPhWkap/oXrvv0i3DkGBbJ49N31rv\nan/FZ2KZhtWz2Pyuj1rjnXdsx9yzWSlF+tKXHIQNAVaLHfc6SCSeFBE407y3\n6H2nA6OJReZAA1bYLh6bAu/s9BVrKvPp+Jnu78SOr4NQSAg9oVSdgM4f/jyh\nZmxWmMufekGcYOT5ywUdK1xZIgIpeMLWXNsiEyr8o0PHR/opyq1lT3oO0aVW\ntXRyNkdt8yd0Y/m4RKeb69lMCvKrIMFocVT2khq0ejO5oqZeFhEsAQKCOy9A\nXng5\r\n=sdF4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.1", "@babel/parser": "7.0.0-rc.1", "@babel/code-frame": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-rc.1_1533845351039_0.9378156713155144", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/template", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "53f6be6c1336ddc7744625c9bdca9d10be5d5d72", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-rc.2.tgz", "fileCount": 12, "integrity": "sha512-CryGZ01Nko2/g8gkYiiPc7x9ZinrX59/BTWMZV1sDj5cAeia64vhyNnXTcNeim885IdGOdYyia1PNBWKnFxuSw==", "signatures": [{"sig": "MEUCIGECpK2g+IuLmRfPbhLQ+cNBpQeamEuHZ9bS0KNLIWe7AiEA6AlMIsOhh7tb8kYV1QnE22lNM2h7VYJBK6EU36UZqNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcgCRA9TVsSAnZWagAAaIQP/0EBwIisxMfzlO5scvjq\nwm4hoQ0gO8dryAZj6C/gvnPl9QPeU50oti9DjYJb84LQ+evdaXocOA5IRrgb\neiqUg+vz9afondv2tDOK7ceIi9ILy1p0fH7Tf1m1h6F1coelQ2uNWUYouTwW\n9ul/Tu8hy+PR3QEaTujSz0/b8/ALImYO0u/V3/NogMj1IfqmrNQ14OUlwdn+\nYfdKx95OQoXLTbAoVp47vNSgeAkTRXsd8Ha/LmQXDVIvEgsXyBE28+/suT/Z\nrB22RHloI9hbJqJa1FTskUEupDvsexVi5Ob3WIUUeU8gY9Nw7NUBGGXQlBeT\nV5nTrV7a9zXBIIFDGlKev8M1jmiWnYKb0oRJ42lMjLWHAlnf/4V12fGuavI8\nDNno73tguexa9IdSbPnqCm3Q1e166xhv3u/MImiN2szU32jzyUOAKqNZtbI6\nDcEmkl+hR7AL1GZVYcozUVpR+gPdzdcdolGr7B1LFP64aDDLNE4vJfnwUbGm\nUkosMzJljDHbL18IOaf+qb12FcxdqANabJCbPGBHl2JVYcR/9vz2FTnqFbOQ\n3VFxu2amHhB5PdwkQYydbRxkXD3eMM/sP6TR6RzEY6TdDkbZ4TBfSfvia0rB\nTc4f2TDj+Ar+Xleq2kgntZOWZHfl+vmADVo5BaZK0qyjcVV9P/O7v1ydj2rz\nv0hD\r\n=VoJ0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2", "@babel/parser": "7.0.0-rc.2", "@babel/code-frame": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-rc.2_1534879519759_0.8641797599570287", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/template", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2ba7d00f86744762632d06a0ffb0494f8443581f", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-rc.3.tgz", "fileCount": 13, "integrity": "sha512-UaP8IpJkOuo4w7dUl7Wn/zvW3HTp+OHWX2Rve3xF/TnzFni06hOUEzb5XOZqvAkMNbchPA179OUQuXsobnPB6Q==", "signatures": [{"sig": "MEUCIDp8R6qx0NSiyKAlCeXCfPZuOI2XRXf3MrbPmZtdsLZRAiEA4VoewumPWLEIru/b5Q9KIdEk4R4eYxsm9BSU/uQvjcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEm+CRA9TVsSAnZWagAAdqEQAJQiksPBLwkFYQMZUDlA\n0yPSUP/PxTG+mE+lL/X3bTLzswGDTMQnFl62nsvmwIC7yaAEHzu5wiUwRMmb\naoLnKSIdkE9DMgeCgYu8ymm0qk9vMXFOO5ILwzGZzwp2vl6ODFtonsceAMxa\nyesh0JwCgaQsxvcxfuO13WAiPE39M1eecOW1xwO2+ZBqf8WC05xu4SFS/0Zs\nOvfTTW9NoZaRvXS7yizAI+0GuWRpoBOEoXHI2t5imibVjjJxQ1DSUwoo49x7\nxIYUwwfeKLtKiV2DOvQZtWNn5yXvm6n1kvt4FTIXUt+bCGpMrAm0OCsCwRtE\n8SNVsBNsK+qiN1Qzy4HvSsGIJnAnsNrReU2zmRG2USYEln4FPHjvQ/azxiDY\nk2ZxfMjv8fHkWIDzFtQavzIacB3gJn+jZ4cgNgwlbIbLFIcogi4eGocY2em+\n6YGMF0me34mg/qDyuvhoTXdYd8IStKc2LpPEQj2KPv4UYb/Ojg20XZ3fxSoY\nmabjet0KY8nq0M1u8n+8sDulb2ajYIy3ByEyxs5eL0b3/NUyx3JwqxV/FCxK\nRWNzs2VvATv9G+BtT5z0jkb7Jz9oHdRIi7NUQj48DOJ74yCmjyNWj6F7HR0h\ngG4KnkC4YP4TD4xyh3qevHcJmlSLfAnC+zVgOLca2O1ojmf8DCeFAg9Fs+T5\nVLht\r\n=ZOKq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3", "@babel/parser": "7.0.0-rc.3", "@babel/code-frame": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-rc.3_1535134141269_0.8806934820999559", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/template", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "695b6d9bf90ec193fd90d533a7b8b3b904f21eae", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-rc.4.tgz", "fileCount": 13, "integrity": "sha512-zI5LcbmvFwGYT8/5t75sj7OpzYizm7PkJlpAujDl50NE1mFxArfq13maUEmjRmjfgl7jeUlbv0YyuyRkDot54w==", "signatures": [{"sig": "MEYCIQDo2WlJaSLYR5b+wv3x8hiUdb2vPHP+8gDBm0kiG8OS5AIhAKMACBCr0riLDNysN4UlME3RjZss4SIE0q77u0AOOUGY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCqrCRA9TVsSAnZWagAAMTgP/3fm6bsycUkG9qXBQAOa\nBmuMgy4SSsvFUzeJ7BqHuRDqS7o2R8REoRukFRflLyVycbmTdeg5nEVEr6ST\nAsruL/zz+oP/+lEO8Bs8/Dqx5Qz89X82N0jKNbg/JEcH6DZO3nUHnCciUp5v\ngjAL53y4C9lVRmg8Q2qedN2BZJ1eSRPxeQAbVHaXTs4MR09ffjPWbYC+OEb0\n2tvokdLFt0LGNJISnA4+5YB+NTr7pJampoPQQLjlwQlxkk458WTH0jteROot\n5HSvz8fPmdu7Wc8+PLTE++KhO90F859YUHqZFX3gZoYDHu69R2Lth2f+ncyb\nxbW6FXj4Q/6Kzq5lQmt+DKHZNlrBi8rDsT+mTaqMnhVP6QgfTYPKSeZexX8w\nIjoosGFXC/P4PSF04GFLnC+PwGDRj7Xp30qJ4wjUo4XSXp7iZ1y1zztqm6SO\nW73W97yanYW6Eh2ew2BPtFDUtSvlwgqjrRqrd6sL84iVWre5s3IhujopA2/h\nQK4QGQopE3CzVVGzxfFwHCLJ4kp1EXWVkgkYO95Ul0OaqJKomYgVvHV0DsfD\nQdCk2/BsK4QBka/96OwP7LTU9cSi+/WhFIC0xnTDLM6fObGmZ3cCdkjZUrEd\nzq4ZyWcLmKP1KlwJVrcZUn9K5Enzz5OVABBHXiUKBJYJc2Qg2VXD9CnWqTFn\n7K2r\r\n=wMqF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4", "@babel/parser": "^7.0.0-rc.4", "@babel/code-frame": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0-rc.4_1535388330679_0.7331847851545827", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/template", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c2bc9870405959c89a9c814376a2ecb247838c80", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.0.0.tgz", "fileCount": 13, "integrity": "sha512-VLQZik/G5mjYJ6u19U3W2u7eM+rA/NGzH+GtHDFFkLTKLW66OasFrxZ/yK7hkyQcswrmvugFyZpDFRW0DjcjCw==", "signatures": [{"sig": "MEUCIBRMjrUFgPh/X17aa6Wb81sPnZ9/5VZjoic5x1IQ/BnkAiEAg/mZ/aCH9BXcP1jua4f0yGxdNcS0pQpBPyzsaMKp6DQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCxCRA9TVsSAnZWagAAqrYP/08K3klbnnAE3oHvGTTH\nmVIU52SUa0zSmCJuRsjpEUyCjToRcs+mLFr1M3qc0jrXASKti8J7irY/a+yW\n/Ew5S4v1qQ44uyMRor7RYSy+h+x+kv4/4NsID/Nxx/pJbVwSgGwhWLUKjAjS\nt18MwlbSuljqHskvBjvsZQ3k1xF2M9oO6k0Pz4VOSBEe1joKHVtilcAv9BQe\n0PeA9XZZFFSzzNW7QfpPI+ujFbLb11rBGDOoZ9xf2xOMQN0ku9Gnd8QMya4q\n+CXgwf+0Iud+sXRxUqQpLIqQit0kJWZw2JQjRDxrBs0V8sBfN4kYPTa2lb4L\n4Dhlvey9eV+baS+H3pqaPpK5LTToMBU8oiIg8gTG51tqfHc3GkT+ts+rPCMM\nrVEASI5DER6YeyOVupuxz5hOtbpmtTqfezY5J7v386+duU/sG4+SK8CoO3iA\nE93BtG9GYQbmwtXSnLhFZs2oqjT+3ZwthM8iVZxy6HGVPA9ApfnfPWUfrsyh\naZvIUYoQKjZdXpw27UllwZgG5oP64Hnq3T6+Ag22BEwUhewoZ3WRXbH+bg9D\nCVQZgF1fHtLwE/PFCrcdFUeQui0VoMu5P/T+GCo+JFJMJ8Hx9X7czYXvcGGg\nyckTMARLaN+Z/aIAbmSWQnu2Z4MCEs59V4S7cUxL7YP0naxNdWhPwPk7dY+e\nZryn\r\n=DwqQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/code-frame": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/template_7.0.0_1535406257342_0.38945552281265083", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/template", "version": "7.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "58cc9572e1bfe24fe1537fdf99d839d53e517e22", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.1.0.tgz", "fileCount": 13, "integrity": "sha512-yZ948B/pJrwWGY6VxG6XRFsVTee3IQ7bihq9zFpM00Vydu6z5Xwg0C3J644kxI9WOTzd+62xcIsQ+AT1MGhqhA==", "signatures": [{"sig": "MEUCIE/JbmmE94tfK8XqOee9abHiJ9CtiID0GbkF43J+KC1bAiEAg3r8+XLRi0c9ZAMQc/Fg+JgCKo9fSMVeOZNZ1ljXXXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADVCRA9TVsSAnZWagAATJ4P+gKR3B+BiCvrFm0UEXgm\nb0rpVWZunjc+XNuCb8jEsdXcQ5vgpNgf1YXjLsynNOEtqR90GPqd+udBOhu6\nksbdlZIjGY3NaBOZPLeQllWyHLMHANmnAzUfR383QnbSkOtP5YldflBBn95h\nf9j4Wdm/MSszX5wpgY1WHdRFFl9WfamRUhL7FYs0yj0lAPvwmxUEHFtuiwxC\ncSA0RU7y5qvBI6jP+JtVrEEj1zsqJaWMNfp+qCVX1+LjQQSjrh4qVhMZMB+y\ngz3QMqjOO86uDmhIIO+O2L2JpfNVk68cGMiNtp0poUgUzawPjiigIcYx2hxs\nO/dNoXMeWcTKNNeqDPm6E3s4461koEsTZHprHvu361K0ic+BAUNdszfTZokq\nHe3QFH9ZllyCtY5TCAU1hwCJ5ZxM7YW6uXEFrDCL0wmn1WKxUhKhRqWCXd9m\nGt8FK+0lYOA6uBiTfb3+pekP37BzQVNYNNIK59et9k9OZjjEvy7VyGWWsh3d\nql08LJ9hVv+a5ElZJFLsU+568DTFXzE8oDJw45fZjhXOUHl4w0mUqHcxQEhB\nFJ2CexpvW035Pq0A1ABlAvP/guc/0IHsSfryEAihsLv04umL4hNbldY14HjL\nGOo3aVpdU1shNar+UiwqF/H8Tw5sFaDEx+Uo1aqe0HxLhbh1t2t3XDFUi3bb\nSWQ9\r\n=PEyT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.1.0_1537212629037_0.16583121514978583", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "@babel/template", "version": "7.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.1.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "21430719f1c6cbbbcf274623b42bbe96fda896dc", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.1.1.tgz", "fileCount": 13, "integrity": "sha512-TnZ0OJeQ1isSbrZewCV4Y3qzVtC9mTsXlmGPSMkka6AN5DNBbIxevqeuhuDwosTB6QqaKUUaMfkOyAcp6Jn9MA==", "signatures": [{"sig": "MEUCIQDfBdm/BgNtG9qYa5P8LqmTRlOLP/1dQz0HkTmwxbE0YAIgZMlK7Bwfe076qu7M8iMAEnkqFiuRZwFPijZncOWpP+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbroj2CRA9TVsSAnZWagAAmu8P/3SKzKGBsR3IEsZnXKjq\nzoDaCPKV4+ZZtXdyskuW4owHFiPd/uElh0RoIq6EZPra7GiSk8YL0Q/IEQrT\noLhMPIJ9v9FS+kZLrjBBDHSx/pIgAws3+IhioiWt5GDw7Bjoz8vcVMw5qzCC\nQlhiy070UYP2706irdK1oIgxuoROI7CRyhdW4ePYvnD7aOfua06GJBFOYIig\nWQckBTIVSC+UKh9rGLja+PVMX/7zin4Y6btHRfqW5jIpi1SuXZzJrd8/e1RR\nGqhorkqBCmANVa58lJSo6fgsmFg+SHzybKPlog/JvoOGhwjpISHvpbej9tF6\nqLhHiyUBEi/NQhTz9vQsElKA8EIPi60nel7fO+RGmsBXkFhaLpyr6Mx4Gp91\nzcNInqj5U+RVZwXwMgedz+L0AWX6NnZWJdPYQADayflu6nkQY5qg+0ooxGf2\nGBsFXj6eb4CCABXc5T8D43Ob2FjNrhP+uCdqljTGACg7a3Xr2+bIOxaznBsQ\nsu9ZF02OG99s9YK3IfhJAXD5mDzGAKXTEfHdeWiU2ZbnUtVvLcvb3We0ru1s\n+Tx5rvzVXY3UTgYDTzImdrZhBJmW2lk0pQxibyRtsiecDHmBDam/z+TQAmvB\nbRrNjk7wZ2ZI+alRVXPTmBJnhNj1xFCxTbfXi6PmL4p+BA0Lw7DMkmryjtdL\nYqSw\r\n=bFRi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.1.1", "@babel/parser": "^7.1.1", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.1.1_1538164981973_0.7493697494638516", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "@babel/template", "version": "7.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.1.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "090484a574fef5a2d2d7726a674eceda5c5b5644", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.1.2.tgz", "fileCount": 13, "integrity": "sha512-SY1MmplssORfFiLDcOETrW7fCLl+PavlwMh92rrGcikQaRq4iWPVH0MpwPpY3etVMx6RnDjXtr6VZYr/IbP/Ag==", "signatures": [{"sig": "MEUCIQDBwzyAjyfDG5C/XDayxI2hrcSU9t/y3jFxg2/cLilshAIgSsyS3q3V+DMskBJEhZb1KzG/eEZ71XpRV6XoGT/e6V0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqkOCRA9TVsSAnZWagAACg4P/j9X7EAfQD8szYle5cb8\nFvRmbNgAf3yP0+duA3yVm179nFhUfNrghlebdsHNIGIH+c7Q8PmAkwa0oT8S\ncYx4uQpj4e+mTT2+yHQRjWg6J9Vi5QEJyfz0OZoHqKjgyapKjcCLVgbGsMh7\n/k7QGErCA417QpTXSW/0xemMo470LL/xRs0uVBxKerLeRytbhIAMj9Y6dJO6\niRvPnHScZl/07ru+Sh7LbY8C3+G+5LVZFAxfZgSPqkDkpn6wCYTHGayQ+UGf\njDmlaXRu9LdgXk9fDCZLukHCWyWenhzHC8QMafQFnvuCRgemRc1FBzS014D+\nvfrPTcOC/oT2UQeY7V41dxJEYwmqsfnGWtSsUpEh+HrtRj5OwFtEHIZYcJsX\nj8RtaQo/0FHrRNSbeCBvg33LV9389laS/i+pu8n1qPP1pm/XRFP+s4dbkpHY\nmE5W4JR7opJPs8p4Eta07bzczTpUrulLOFZyy3xeI0mkuva2I4Ow8VM5M722\nvAMzH5ucncRXONsd+CHY48N5/gGh2QLXmltzW87IWuS6MHY8hayHDUCcJEqG\n0KZeLgPBv93rB/pq5u9GpxdGHPnCIy76021YNMQeYJajB6EewAMAYN1/fzY5\nqy73fLUFkh5xtI/FNhGHcg852hhgt5KtkMrVY+fzzlWKUGhiVdQUcZQfhpOF\n/oCB\r\n=iSOU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.1.2", "@babel/parser": "^7.1.2", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.1.2_1538173197451_0.06801225069014816", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/template", "version": "7.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "005b3fdf0ed96e88041330379e0da9a708eb2907", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.2.2.tgz", "fileCount": 13, "integrity": "sha512-zRL0IMM02AUDwghf5LMSSDEz7sBCO2YnNmpg3uWTZj/v1rcG2BmQUvaGU8GhU8BvfMh1k2KIAYZ7Ji9KXPUg7g==", "signatures": [{"sig": "MEUCIFu6mj0Oyl8Uu05v3j/lZeGBWJERaOmPJG0Sxkd9ilCbAiEAi0bcYOcYoKAtdLpyJiFDOnO0fUbkE7LhXY/FrgnZtjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHzCRA9TVsSAnZWagAAd9QP/2aTYMyyPzt3+5A1Hm2C\nJY1T4i3VyFydv/VmjGErWqy6M4pZcbvddbYjRi5qtNNFBnqx7QYx7Q8aM4ym\nCC83UpRPZYAWl0DCi1Ajgm62nk25jE6bqMAjaMaD501J3g/I7Zrto8PlomYV\nlPK+oEEI1zSi/6MS5rDf5T660BD5AI/1LFUZjohEx2DG6Ac09NNkx3cB5y5o\nHHwopFrooMv4SyXPPGlZO9TiLS/78mFLWQXc6vCQAnm0bPVS+2jGXsRdiIMs\n4/uUC1Lgx5ylzqLZL22TW/aMZwxMDWFIuL5ZhebLgEQxuTUhEAHrpBemT7U4\nL/O4DqUSNA3NaOpeo56QACOsnXq1jYSNb/AJM5roaDeVnmwWpWeaAZM4BzYH\nnkgnWD6q4PotM1RcE9zgqZngXRpd+8VIB3Y7hTQsyFXVdQjGuYM3HHndkaCR\nw/nj/4HmHta2V+VPu8SYcxfkz5mJatBCbXncxATcDIOVP3uPl7BfhIINRNl0\nMmh1TeTjfPcF8lfJ+qFODNqTXnkPO1Vux1gVGwvNtGG5wP9h8lee89tKqgK1\n9kUBuUTszRKZQ5cB+1h58aAeZTIvIHnWibUX1RM2VXDi3KukxrcCUS8tghF+\n7ZEgtwoJonHcadlSYkAuB1+Kn90wUUB+B6oogHM31XY5oyjmeTY+9ihTNA6r\nP2w0\r\n=60bK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.2.2", "@babel/parser": "^7.2.2", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.2.2_1544868338742_0.08358106317967606", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/template", "version": "7.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "12474e9c077bae585c5d835a95c0b0b790c25c8b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.4.0.tgz", "fileCount": 11, "integrity": "sha512-SOWwxxClTTh5NdbbYZ0BmaBVzxzTh2tO/TeLTbF6MO6EzVhHTnff8CdBXx3mEtazFBoysmEM6GU/wF+SuSx4Fw==", "signatures": [{"sig": "MEQCIApr8JkiewtHkfn6L9VXiG/ftct28+uSd3H69J3GSDiIAiAotmqpL0fgMLl5Ebh1yzhk4jN1I1lc5Oub3idedKmg0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTRCRA9TVsSAnZWagAAp/wQAJsEZuN7tKei6nAnJmkH\n8VREykC1Xnu/JU2LtXeAxG+nzz+/kTmaKrNdPQlEAWnYFoS0MAzdP4rqlXUd\n/54+h72HVpmJ41OBC5pR4wIzQwxutv4MNAsvbzkt7Yjf5iGIUAiuVE32WmLK\nrvsi2fRdB+8HGxbS3L7VAUUgh6wiRG+fP/E+khqMQq2Z5P3SGVkOGF88LaPz\nG7C0bxAfsuJt2CCwFfZ022krMNi/l8UTyaydo5/+KsuqAqMjyjXpYJ8a+G9Z\nkejSCe9agSGGYpBsG6hV3gIFI19fBa8Yf2PF6PIKGZXKkwHZfIsu1SbpDaag\nWBw6bX1ID4ExzfEGlsLCVHEw+6aVdt2sDowQ3lhGv/TQHgLlsE1HP9kS+TiO\nP9PK2rZ2OifY5W0Lzd99mHXTqUYWATLWxr+ouqDW2smny42ZnJTB0Id/WK1f\n0EQaZI1jkG73BybjSMXnPHQx+tesaQsCmL+57kv8iPDsAO8zyIRyysFwqidV\nTzJ/5IfbKKubjFLjMj5J2EOiaO3ycOaSg0/vsT0oIfztAvTbRQawmf/0+3MW\nPLqsvfqB3n1mVuSRkMi2mgfceFV7zSKPjWkLXRyOeblS2HwVQNAhmKmYqx/G\n9zb0Ex2QuqGgp4y2i3WiUFQ6zgiUMn8DVkDXCeYaN7auIHbMcaAZ5NkU+51W\n9UCL\r\n=yRDZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.4.0", "@babel/parser": "^7.4.0", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.4.0_1553028304479_0.7451371334071337", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/template", "version": "7.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f4b88d1225689a08f5bc3a17483545be9e4ed237", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.4.4.tgz", "fileCount": 11, "integrity": "sha512-CiGzLN9KgAvgZsnivND7rkA+AeJ9JB0ciPOD4U59GKbQP2iQl+olF1l76kJOupqidozfZ32ghwBEJDhnk9MEcw==", "signatures": [{"sig": "MEUCIQClC0FzpZfWlsCHRQ76Z7QTvDFZLClrGVqApITJtTxWIQIgK8nneNqxoBtK0mb4daMNKCglqCq4Mb/Mkh7HpKzMRkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JmCRA9TVsSAnZWagAAW5EP/1lvCz5/KaQV5IyG3RTP\nXThFdEueazeXmvDxET593LUjajeWmG1R6zfr+kTfagEqZ/aI3Z3kSkZ3Ckwv\nf/tp54PSyU7vD7ImROyxytNJtNFUK+L/QpMAauR3nrrUkv6GFW/oZI+SnXob\ntzl2n5oDc0I74DTenBRqa9KNiU+iLTJZtBF1PIuOpWMuhTt61EPz0cCCaJA3\nPVoFbrktSUra12hLZ/lMljxCxm8MBlD/cGf3iFo0Eqq2N1WZP7vlLhCcinrl\nTe2yUeKpbxoGLrXWT9p8GWKOt7D+gGN2Lk9LZAaTQHFajX7ZCC45Sb3i6b7E\nsx6894PprKmI6fm6bOBhP8PwEexlyRIMBdPTu1//hqxX9YNM9GF3m4DvbC8Y\nGLK/xxVwIVIVzRSysKdp+Iz/kfW+Va4SSpDyg0K7c89827NT6CNyAVYTIau3\nwj67crGUlXP4HaG4KeH5StNRWw22tCipzlQm0IBzOPnbtDGg2iuYuN5mr1B1\njrlu/CMXJKFsu42103hHINS1tOm1Eaeijihv3LnhOTRMnYB0qIr1vAdYlepF\nkw5Xup/lLUimoSplSmpi4Fs5BQslh5tf5ykPetxzAHRU8p9xAIgojasRknsF\nDloGg7NMrETNyejGfl+JbAKqN7JoaaBpj4qKDGeE0dfp65MUXssMemrHVeQw\njM5S\r\n=I1Xw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "description": "Generate an AST from a string template.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.4.4", "@babel/parser": "^7.4.4", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.4.4_1556312678207_0.7036806719627691", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/template", "version": "7.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7f0159c7f5012230dad64cca42ec9bdb5c9536e6", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.6.0.tgz", "fileCount": 11, "integrity": "sha512-5AEH2EXD8euCk446b7edmgFdub/qfH1SN6Nii3+fyXP807QRx9Q73A2N5hNwRRslC2H9sNzaFhsPubkS4L8oNQ==", "signatures": [{"sig": "MEYCIQCN2a4Wasqn0W1FpYszSsJhffu/LV/yEw/ePsoWoiMrswIhAI5OMpDhL2J6B+cAi99+Ct190/yd9bVJgD8VtLkkAmm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcpiACRA9TVsSAnZWagAAN84P/26ByWaBKvvaiiOS+vlV\n/Uk6KN4Jw9AWeaG8SJc5G7FimqPco9O/is91it3cjHToyjRoSzoKqHgqN5HU\ne+9M525VI/TM2ZRQBlohLBIo63Hr8H8+Ptx2OL/FVSFKInXdnycZFfvv6c+f\neXKxDJxpLTMZvMXW6jeD8v0FmYhhwFtgn9XeYC+XoarpghC2pPufHQiNJLzw\nsK1hdIJXBrDRbHq2GhBK4a7uBawyU5SIQnmhhbUfKPE1ywvI6EFU9hnmTlqH\njC1s5D/dAl1XcaLK5J/eZdfxdtDl29iwOZN7uzXHY2QOvYf2gOZQbLr368hM\neitLc1kdvJpwhiirKedNYt5cexLXPNRO+usi4G9uxc99uuyCOhLSapTuH0YB\nOgBq73Iy7JUDCRWqT5r9jLSYjOLk+1sq16K766r/32vlCNW4nQPhutXBcJmc\nbWd4RIE7zebRLt50yeMZlaFzmY9uoaYfXYp75rOmbMCV85z/3w2uTi+dumEJ\n5ufsDtoh7/0/ywvp8Zf60JgjR3P7HyrwO9oBy8pKK6dh4c91iziFgfCtnjDC\nHzVaaXUo8/MM4NvGH4c8P1sx+FDuEBMWdoN2mNwc2VmUKyHhd/SYemwHkw2N\n9EdEvtchbync2u7XazElGBcSZqMSIglRrBKN1d0nhloEP7kNim8h7GNkjKRw\nBD7z\r\n=QqL0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/types": "^7.6.0", "@babel/parser": "^7.6.0", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.6.0_1567791232093_0.8906835466423977", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/template", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4fadc1b8e734d97f56de39c77de76f2562e597d0", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.7.0.tgz", "fileCount": 11, "integrity": "sha512-OKcwSYOW1mhWbnTBgQY5lvg1Fxg+VyfQGjcBduZFljfc044J5iDlnDSfhQ867O17XHiSCxYHUxHg2b7ryitbUQ==", "signatures": [{"sig": "MEUCIFY3XLnpkSyLGUxHppt4eeLAFNyVp2Gef8HOVRgDFXotAiEA0G7VIoLNecnMt3FRwWYBcGX7EhIAspQLNC5+S66wPT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSpCRA9TVsSAnZWagAAE2EP/jo+j62I0HKANiBPt7lh\nWZIvJkT66vvmb2EXbwfGIag5wKVKioP3eLSHy74CthNsffv5THuRWrINt2dk\nCgGmAckB+o9HJT+/rqMErO+ffbv/uaPRT5kottqyRvuLZX9OYUGCsQ5vB1CX\n4v0+j9n3A9aZukCT2m8eU/kO0XBDibjr6lKdZujXm0QDJusxwk6ZChvRUxS7\nRO2dEB3bbV7PDsYYCDiSF/TNKkbKlzRayvmn3fCYQPFyabKQMx/0ad0JveJr\nOkKLrIziolIeBMYnaoy5XM7A+s/g66DanEGaajPrzsTbH+hXDZicCDAN0JFp\n+OBRivd2SXcmFb74pJflmmiw3bTkZb7IslVjQd7zak6hXuHKiZ9vmYMr+8S8\nf7uSQVZnB6HWW2WC+joEw5AbBq2SsOZBPYIn/sULhxmQIBsnycE3hQqK4ohu\nVDk06Ll+DyVFccju4tXlSONiNkLYmUh1D60BXUu/EqeK3L6+7SrAy+dglkw9\nb+Hgej2oYztCuq4FkNznHVS6lrR0t3vna0/APSWHFEtw3QlEcsjomvpniyv3\nZMYFmAG9Y7uHV4fjKrbOhbq4pj4d4eMZflS2y5rb1srkqh5Nap9EWX/p+Y5F\nLRRUBPEnbZyDmUpNcSH5A5P086XwsFJT7ulgoCC9MWZ153CPAx+QvaGHSn55\nK+Fb\r\n=+W3g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0", "@babel/parser": "^7.7.0", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.7.0_1572951208701_0.9047429993918386", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/template", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "428a7d9eecffe27deac0a98e23bf8e3675d2a77b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.7.4.tgz", "fileCount": 11, "integrity": "sha512-qUzihgVPguAzXCK7WXw8pqs6cEwi54s3E+HrejlkuWO6ivMKx9hZl3Y2fSXp9i5HgyWmj7RKP+ulaYnKM4yYxw==", "signatures": [{"sig": "MEUCIQCxqYVo3eRm3ryNEA4EEYhMVLXDLHBpOYC/bAb0xP4XrAIgNGvY3WKwRg8ArYrEU6QgJPAZ7rcCX7XnQ1Ud1Wlt5Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBICRA9TVsSAnZWagAAmxEP/0urcRtJCz0lRyTjgY77\nDxNuHgmgvePkr/H1qpxNNUvfibXbh+ef9dE2CH6hD0a/IYVtP1pkrvZ7v/qQ\nxyYkFv42OOqAXaGCGrcwlehq/ZfEvLNLh8HvLYnu0BrMjKzy4qPdFNm3ysDW\nyMxHg2dPbanGhtcA0JPRY1npkU3htSTtYdnior+eCjVvCdVGH4UuJnLd73VN\nRUlmED6ZLwbfTY1DbwytW9S8gSRIiSx5zgHyy1zkQlwuOFk0G6VE/isTYm1i\ncyqvYFOtG9hvkR1c/dHD7yCiSOf4+mVoVRgV78IufI9UFQqtD6gubXvq/iNv\nBujmIIH45eqooC6Ykn/+bjji6ojEGxgd/4s3ubWJ9XjtvjrmkdjTbjYamp1p\nlIeiRNtBXXBh2/VMKPDb5xD3WvP8JuA1M3/rfZF/f7bX6x++aOiLY9l4OCPe\nYKMhOprnzaAcwQggbCwSLQKGLjorvxcopBGPHh/jBWXZ9BuIsbxJkchkbPd4\nv7Y5rbxk1YbKiF/N1s2JGUW/+9gCBHKYz5Cghym42OPcOPrFLS7mfZwcnGol\nfHgPVr/itZLuhIWene2sA+iQ5voi6ehfA57kXOIpx78UTFLBlxrNfbqcmqFO\nH1Zs0gDlozPqsc37ig2UkalnACGFZcl+ZO5W7WuZLgKeJlxLiVStU6PTArNp\nQV3Y\r\n=hEWd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4", "@babel/parser": "^7.7.4", "@babel/code-frame": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.7.4_1574465608116_0.5263317892392974", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/template", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a32f57ad3be89c0fa69ae87b53b4826844dc6330", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.8.0.tgz", "fileCount": 11, "integrity": "sha512-0NNMDsY2t3ltAVVK1WHNiaePo3tXPUeJpCX4I3xSKFoEl852wJHG8mrgHVADf8Lz1y+8al9cF7cSSfzSnFSYiw==", "signatures": [{"sig": "MEYCIQDCXcuQhA/KLfbR3k3lhz4aCybyEyjcUZe4cN0OkcVEAwIhAKtOdJU1bGPKKyS04rUPCKY7NB0gLlGfRIg3LM6zxYYi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVzCRA9TVsSAnZWagAA830P/RoiUb9otU0wQv3pa3wb\nWXmh9vKU7QWInvzitSQ9FVB3OlStph75oeSg9FGDg18qUlQOJToOO4MKn5J7\nicSSDo4uqZHUdsIdeonDJA9FeKChd3N7ZAY82zlKStubQuMuyq45EVzNkwt2\nQ2DeEtk6hnVDmmV7ZQdYg0u9y1wpknjmWrLmNwcgajiYROS+xuZ7DZrRl5RA\ntspi2ejBc2PZCVbLt5LWBHy1/sWyeypNL/FH3rRsIZBVQ0tnBssaNMqaT9UF\n9s60w/k4GQz4AfAw28pvAFNAHCweGDiAZfEittitrhbCi185QfQfPkt+QIl3\nwBrrQX2jM4RsWLDKO694+ZW1QWXWVftSGVTjimftvtAAmkTRAIWvm9HEY9iN\nQe+npI/SqFcRNyXAMur45yAs4ux1WSkghfkd5pO+5kcOFsDXsyp92M06ih9a\nm8L5L4LnbnxSi31D4Mmd0eJVo6uwNCZPzDfKAkTUjmEbSbyrmTYpNK2cUA64\nwP1C1Ah3U81rvTpFMCxQkVbzzW5rHhN7OPPV/WrwsLem5mds2XD1N9mm2zwq\ntIIgzFnK0sJMUvlOTux3bSvDZZO+zT2G+R64rOIfyL2vhuJLikI4k6ve5kD5\nYymY+ct+fOes6e/1y8OfgRNdFG6Yu56DN3P9l+Cz0VD96wAa4MdmrUjVv5RK\nRW0Q\r\n=LifD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0", "@babel/parser": "^7.8.0", "@babel/code-frame": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.8.0_1578788210916_0.39301972791909723", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/template", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e02ad04fe262a657809327f578056ca15fd4d1b8", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.8.3.tgz", "fileCount": 11, "integrity": "sha512-04m87AcQgAFdvuoyiQ2kgELr2tV8B4fP/xJAVUL3Yb3bkNdMedD3d0rlSQr3PegP0cms3eHjl1F7PWlvWbU8FQ==", "signatures": [{"sig": "MEUCIQCAur2YPK2P4rzA4UWFJ32nvG11aaztNa5BsjGvAC4eqgIgZYiIXQ5OjZYQUm6I85OoQV2YFOmyA6iSaT7knTOMUo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQrCRA9TVsSAnZWagAAbQYP+QBkZfCgNceYTgS0y3SA\nC49PHfpE7/OO1B2t+20L4QDzSSnrrra+25RWcRvqFoOmr0PSCUU9FaHKus64\nw6cREpKHQCwej5hDXVE2j9xm4oo5XNpzWQ9lMk1xQXN3O0IDidHGaBdBrMuC\nJFHeiuPPH7zZ/z3/7YLTxW6KqpuLfbfao/MbvF7/DeJIMIa7Mw4hwuppa2Mj\nOG0tWkrz4A6bCxMXW0JV0PYQSrgJfnIxruJrAkv+Fxtjk6qRNOHnYbOj2d/D\n78R94QFP0/b6iaaAccjAeJDIBowkV1TdqSm0U/0WSJ6910ljhHjgeh3e9CVx\n1+iLJMK49P9Tum6SYY3UyFKiEhOSpulxH7ana8BpMVc0kmLKSisA0td3kdfz\nNnlWrBh6LjhX5TU9Rknihe3v04RmBq7orViLtR0H5n51pJVLGrwBmWQwqTzx\nT0e6VJFnYo/eXgxpWd7fbvcGJxJJyPt/Al5RVVO5SWbIpWK1hMo4imtydixz\nKZkmjulx9M6ZLZeZRk9+aesbYTXF3F27z5uwNOQ3SqQNTHgUWdabn6uBtRsO\n65e3KmRAPXedY8awUxLruG9I/IUdXEVGBaav0PXR1xasC8PYaLV0lYygU8Zj\nNVhNWr6PIBi2dW3oFHExFwlDF2lxjuk2X6qxb57NkQeFOq7Dgf14JS3MzE7B\necyA\r\n=j8sU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3", "@babel/parser": "^7.8.3", "@babel/code-frame": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.8.3_1578951722808_0.18120116454852164", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/template", "version": "7.8.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "86b22af15f828dfb086474f964dcc3e39c43ce2b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.8.6.tgz", "fileCount": 11, "integrity": "sha512-zbMsPMy/v0PWFZEhQJ66bqjhH+z0JgMoBWuikXybgG3Gkd/3t5oQ1Rw2WQhnSrsOmsKXnZOx15tkC4qON/+JPg==", "signatures": [{"sig": "MEQCIHTPGgoKUI3bESxvJUhZAANg5KE70OJyqTS0dtlyt3/KAiAcbxXMYPsBHU+zpcDBCE7dAdAuwDlsIBD6+FzXdpM3Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RNCRA9TVsSAnZWagAACZwP+wR0bMzEV1Z5u2ym89m2\nIDfDtg2f7KUuDgqD3sj97bYq+h8qGvSDgNSmdH8RguKq9N7WUHmw4gIBKnfy\n+/Xhq8tCObZHtMXO2O6E3GMkW19Jym8etm7GFpZaBAf+7By2uqH/0/f2CIhg\nIdbtbKhqCu6IHW5y2tvB305zSgsjooLNmspph5i+Y1FNzRSzUCTp/neibY33\nEePg5/Lo5KKbmKRb4KtVQ5q0BlN75srIvwgGgfPmgwnaDSpwW2w/jJ7ftIZd\n5/YiGO3J9J27Vx1MokwypDOQy7dpTINR1v5VbLaQo7b5w1Dz5Zcb5xPdwcTe\niQSgvcU6c75Qv8hyY/PKdE6eHBI9kRqubybvVA+9gDKexCl1ijhwU7vSaDrb\nEpR7PDTIVOKrPMBwTW3yN2szB5ZO1j29/QKfRkls3zq3yQI3Z3Jr1MdtVXkc\nVCYuHGVpNCKtliY4yorMcOy4ipfP2Oqlirdf7amX1B2dhan4WvTWo1dVaPQd\nuNKHiQM2pcZ6QMsmWHNZ8v1hpOA1JM/AvpLQFmxEQlQ3gNM1NT/7E2cpaU2z\nTtC2clJrjVq77K1xCekLqqnanT5hGKmttIVvfg+XPm8UhudglL9bTgi/M4G5\n7W0xSUAmXYjzkepBCp/zETPJNi/IL8K608kbBdijYMuX0qWrclFTraL4ESLL\nqrF6\r\n=DwHT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"@babel/types": "^7.8.6", "@babel/parser": "^7.8.6", "@babel/code-frame": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.8.6_1582806092994_0.12577470391090428", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/template", "version": "7.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f15d852ce16cd5fb3e219097a75f662710b249b1", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.10.0.tgz", "fileCount": 11, "integrity": "sha512-aMLEQn5tcG49LEWrsEwxiRTdaJmvLem3+JMCMSeCy2TILau0IDVyWdm/18ACx7XOCady64FLt6KkHy28tkDQHQ==", "signatures": [{"sig": "MEYCIQDPUXOpWc9IupDDhb5r9I5lkklmtHAod99clcUBh3Y9TQIhAJazOIsFCbs/TKyvEimBCqqdWf8Rm4vogqkhO4Hg2WGk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2CCRA9TVsSAnZWagAAM4IP/iVEoMDot658Yx8WgTyL\n2OwnETj7MspK5GQksNtDo1F5sgxmUDjYhrgoAHBGN111At6whM2e3st/+Afl\ns0Bsq+zrLQUXyaHDyA5WEl8lcB0Y12l7bCYFBnpX4Oi/EPx3gjjsFZ7KXEok\n/+8Fex5jhJdZTIeQb5FpofNmEgSWbL0tqGH5hDqUOPfetuXVDACLx2dxoOUH\nORQ2BRETMQ+YN1whsx6FiRQyUFHX1XP8HbMTf0/sfBMLHCoTS8bZ8uE8uOcV\nwL6RuKac8sYhynwVi6s8CKXzRaxVfqAFY1q6luIIjGEEl/eG48a75HtjhCBN\n8j3MN8NkKDTejJCJHCNc4MRh2+JtxxTM0oanHyslJL1cX8EUOeWoakdn4lcD\nxzqhS4SKfsOJJXE9B4xOnEtuCE/V8BQDLDoSFG6410lTZxR3zBVYmI9goJO4\nmy+tqn5TEBTaVrEr8oOGwsvtM6gyVd3PsakHyNu+lfWL5unzrWmWVML3MTUA\nTeSHUiTQI6i24IH7l4s1+QB5fNKhum0CBW0uC1ZnEFibUzvrMtBekXMxVIK3\nZQx0uDLaSUzTCBPwi8C2Jie9BkqMjE1fRxEuAVbL0IEfWLpEmXixt0QWlYeN\nFmrrEtN+rIQOK6AUgkr6R7wTlua5+kYUcK4aXy0kc7VKeQX4PxY5oRSSnwJJ\n3cd+\r\n=7ZKl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-template", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/types": "^7.10.0", "@babel/parser": "^7.10.0", "@babel/code-frame": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.10.0_1590529410194_0.654548207323411", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/template", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e167154a94cb5f14b28dc58f5356d2162f539811", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.10.1.tgz", "fileCount": 11, "integrity": "sha512-OQDg6SqvFSsc9A0ej6SKINWrpJiNonRIniYondK2ViKhB06i3c0s+76XUft71iqBEe9S1OKsHwPAjfHnuvnCig==", "signatures": [{"sig": "MEUCIA7FPDwoezG9MFvJSTQ4rkmfrS94fuABWLkcFsFvqtdkAiEApo3JPnTWKspEmqE+QVlqJozL8itB/m0NK8yLRrE4ZO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS0CRA9TVsSAnZWagAA9s4QAJuMJChHvxAJIa3GxjMQ\nlegs3V08fRLE4wqVPRWFwCPKefrzFctNVk5a26Shi5kzVJ6hsVeDGqTsKTSJ\n4/4edyAVzHybM2O7elRZ2hqCadp8c9YgNIrnaVSsyBxgHi+sr9UhgnLYQ53M\nBAA0736UnGywWcTDZSxEDEJ5RRSFgKKg7oiukC21HARSFqsM/VOncAGPbyOC\nbOin7KyHLRUo24i1kwEZL9CtyPFZIZaflv5KkGHPtkXOLD9HQ+0f6kuonPmM\nEMSr2AONh1IAW0OWb3214C0rXnbhfPiomg0ml+Sngeb7WvQoH+2Lx4iLRW0l\nRVzwZG3HZ5SaI4Q9CImh08OxHEWy5SpgElEyu5j61Gt2lgqqEEzi21FZmjlZ\nsk0AoybNjAgmYm45A+RixtL8+G25nF2M7MoExieAytAg9LV6tuCygaY2ILOD\nQ8qBT4O7cGEV4xPrj36a/JGyIlfOesW814xjVeaFt0++PFmQ1J1aMMeU1ivU\nEgctB8Jjqjb0C5iVoTLceHl5QixvdFMuI6b+216zAuMJh2LvJYuBwEbRkPK5\nUKakQ5zxAccBe5FuQxhczWfmtdfT9zUt93QQc3FW/onFGVb5XJqr0VrPgwNg\nSpSO4shkJzqsmAkHqr+j7iCQvWWYb8SHbz5X1o47UeyWTTscJpkvK42Y8AsM\nDAs1\r\n=8/1o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1", "@babel/parser": "^7.10.1", "@babel/code-frame": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.10.1_1590617268270_0.6780331120459875", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/template", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4d13bc8e30bf95b0ce9d175d30306f42a2c9a7b8", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.10.3.tgz", "fileCount": 11, "integrity": "sha512-5BjI4gdtD+9fHZUsaxPHPNpwa+xRkDO7c7JbhYn2afvrkDu5SfAAbi9AIMXw2xEhO/BR35TqiW97IqNvCo/GqA==", "signatures": [{"sig": "MEUCIQCMjEtUnGVfz+FPYFut+fniRHRlcolUkg6QrS9fHy+vKQIgELaIRiOyNkx+UZTbgCfv5Do1Bc6WZy9Ho09Ki5wzgps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYJCRA9TVsSAnZWagAASlkP/0fa1yFJBXIeSzwyVlt0\nhBigvF2WpL9kx8kqmZsE1t407sR0y9wMoO9S8ZmmWkyYiujP+eByUBf4WCL5\nPRv2HiakblPw2K7v+YQTaw4Rp2hM9IUZ5mSU7v8tGdqyAeO+EeAw0Ugw2MXr\nmeGYL4GD2jcvaD65MxcZa9RCDI2DAWK8S9CfwcPrtNiUFoeosqTxNSCTQH5c\nlpInDHB8jJf5T4/WdX6mXE7y+pZnQaynF2CzrB7KHTfm+ZC3X6X7BldeniAp\nlqfPMJtjBf+cu45wdDPODtQsRIv5MQ5mS4v6grSqYDnDVy8wHIMti1CpBGJx\n0s6g4TGCJ5e8AUJ1gHztrDbnsoun27MfplT2A3nvBm4cZyXTjPed9XmqQaOw\n5vR9OwWP3H9m8Gi5awGqneZxyCvy2fC5IFhtQyd9D6/K0zrWNsktoaG5yghZ\nfpaw6uh5Emto0nENbHC483eLX+XpqMUdJoeh46h/JCNzskgmdVbWTvJ1cVkK\nUJRcOiuMw+1AMpm666NgFfZcUZpyANL+iToLNXERq23VkKtzPtnr0CIwNTxY\nFvMsSlV/0DZDGn2oRY1bOv2gzh8IVz6BPvG8LSJaaZDlwBw5BOJznLzQWqIp\nxCiOg+2RYVo6cEtqibWqDtxn5CnPDixxUsWTppYi6X0zJDoMXjgn+DAphrHf\nKB+B\r\n=zMuI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.3", "@babel/parser": "^7.10.3", "@babel/code-frame": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.10.3_1592600072718_0.6810434029044472", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/template", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/template@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "3251996c4200ebc71d1a8fc405fba940f36ba278", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.10.4.tgz", "fileCount": 11, "integrity": "sha512-ZCjD27cGJFUB6nmCB1Enki3r+L5kJveX9pq1SvAUKoICy6CZ9yD8xO086YXdYhvNjBdnekm4ZnaP5yC8Cs/1tA==", "signatures": [{"sig": "MEUCIQDjncmIOr8bPlayAujpH7L6Y+QTRFfvqIcjujK4Mf2TEgIgM3+9OU/lpYtDkWX9EUCg94Ikl6lHz6Dh9QC9dKNUMJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpMCRA9TVsSAnZWagAABdEP/0M5zdn8brFCWtdi8GfD\n7wzOhNe4YtS5CzqnTiNRhdTQ7P/891ZaS0LD8BlNbLa3mRaMW2e+bjrXMh/h\nKJzVINfDmD1SjgN6vXFcSctt0r7hOd3uAC21edT2bckBG3vKvHqV68qMHJOM\neFJsPFrflVtmaLgqFQBL8NHWWLY3JkJaV3Y+qR0WC7bXKfC8s6vrMrPgNgWv\na9ig9keIXP+dzpLXHWLvVX53geal0tdNcBVnipfd0awuBynybzqjGYD8YfnD\nENE0pjbvPZrw3VQBMQ4hz853n6xyJUfoM797bbZ9yYH2M7gKygI4s2I3/J4M\nWFXl7H/2CVB2LYaYxPzeW6XWVbGUycF/HESEHSYa5QpnhRK27Bh9kKhWpjcW\npvIy8ek9kVKecQoIaniSCjIhhjbQ87thLDfasRtF824MClnz5XJbwgQgI22f\nyB81a9Wjm07bLFLpRwfKrDy5KtxL0OCTFmsvx4S7DYMjqEDkgXNBZWCB4Au7\nOc6XJNkcDiZ69UE9jNkjNfjBN9EcXwvmkbtYpnooDQYGKeNj10of8rBhpywU\nMaweS6wUjPK/h2JIFCwAZZiEtLAQFIs/ONRPyg22kK4oSU44VZ5tXC6+5/V9\n1mzqHrf0b2ZoXjXXqOPZb67GQBCgP3MVKbPBFDAuj4z4nykejtTDoGGA9ABi\ny/7d\r\n=+j9v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Generate an AST from a string template.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4", "@babel/parser": "^7.10.4", "@babel/code-frame": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.10.4_1593522763822_0.08416775203985671", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/template", "version": "7.12.7", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/template@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c817233696018e39fbb6c491d2fb684e05ed43bc", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.12.7.tgz", "fileCount": 11, "integrity": "sha512-GkDzmHS6GV7ZeXfJZ0tLRBhZcMcY0/Lnb+eEbXDBfCAcZCjrZKe6p3J4we/D24O9Y8enxWAg1cWwof59yLh2ow==", "signatures": [{"sig": "MEYCIQDvsTkmHQMF9qjwYv/sU89YtKxpw5tymCNQ7xuEvFawigIhAJRKmlfKtIb3O+kiu3GOH4Adzw9sDFiijNId0BOIrdKR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+zCRA9TVsSAnZWagAAwocQAIpVEkfT9efDNui4oyjh\nuDkduR3fWQOCdEKpNQRuz8+I7S/WWZSOeUVzSbd/4iI59KXG79HL6vfmY0sM\nAyArleDKmW9W8gTJscs3aXB+Bx5FC/ZQ+GzgJStX7w+lLOkhBcSwJIhRtysO\naymqITRnFczGze8749G+9kEpkti+yBzspIzBHtFuIWCx1zvmsi+CvEGh1wE8\njgw6mNloNZIut7g8tfzAp/cPHAoC+etcwWx+JGIdkL6oJTblVIrIncBgUeIS\nP+id125b46lLOMN/EqrN6+5DfKLUW2QekZw8FT1E877XqMpRObArBbO3CBn2\nrHNudhKpDEUFfTurJv/KNcgLf23Nc2kcd7oCGGPXcZWP+gSpUZvc6oiUJ4Q4\nAehN18csrPuWwJYGa4RGhEsLnJ//8sQzV6Fhqus/DI3taqg6e5TuKpYeRCnT\nH1eeOp4zX8VedUj9vJotCWldB7kpQSMKJpjvJEuRq4I3wfcxmB9PQtWbARq7\nSH7jpimw8zgzxIza7AgegkktcvibR8woEh8EPIfOR+csWm3WU+Ps5JmkGGsr\n5EQOP2F+n++oI7Yb9Rhq3IWSHSyd2R6fwOsKrZPGhoJGa6AvUVNMR46y58g0\ndmuAJC22Npn1lkF2LQq7jdNPNq1uhVD0IMMVzxC7C7hKGF4LHWXdMd1yg1qq\nesJ0\r\n=8Ll7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.12.7", "@babel/parser": "^7.12.7", "@babel/code-frame": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.12.7_1605906355330_0.09688804058103662", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/template", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/template@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "530265be8a2589dbb37523844c5bcb55947fb327", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.12.13.tgz", "fileCount": 11, "integrity": "sha512-/7xxiGA57xMo/P2GVvdEumr8ONhFOhfgq2ihK3h1e6THqzTAkHbkXgB0xI9yeTfIUoH3+oAeHhqm/I43OTbbjA==", "signatures": [{"sig": "MEYCIQCOGiIoR8ur8NWZOdANWDBWefP+qwrFWw5t6jXraaBw+QIhAIKkTQER1o1W809iqL/WT6zN9zfV9toIfuuUwhqX5HTz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhECRA9TVsSAnZWagAADRoQAKUrycVdH9UOoRCKfFDL\nGrG2PACPtEIEmJGP92GthYJnemz60md8x0XCviBLwBiz3RA8HOgujNCRiMhg\nfztuSNECfq4WVRErj/8oNlAwfse0V1EW8r5rekwJ4XVuOwFvvZLuMGaio7i/\nIED6TitGS3sn9BGHLWLwDiZZthJdApHQnPuemVJUVwRRHv3v37GoYSlmbqbJ\n4VPOzL3QJI5DqcVQUbh/BdgTdK9IK8Vi6tK8//Q5dvgSaX2Jvx8uzrVv4DkZ\neN0B5QaKjykKW8qaXskB566AILpCUIK/qDWMvciqVOMBvDxg0yZyZote8SDQ\nXfQoe/PhB2RLPdgp2fPKDwF2+pdhwXGpfGQggupK57Njjmk9hxDLJ0XKGhw0\nfLp0B3C8gBqw6Jf4LD7OHPrmmD0F1+wnBfMO7OPtLGDpG+T+aPasALcSXFj1\n0HrUIbYfjikfcFFyU8LN3J2kSjZ/Av1/lHTzanM0HPVY02FQpEX2LvPCnjnz\nbz9dsE3inV9r58wQoYE0N4v+JaRAwWDqGSB9NRLXnRbmsYurcpqRNLCQpeqM\nz1U2uSpb6qn6LpoL7IphZAUgHBmJEdfVKDP3bxPUOBHiIcL7Ddiv4LzIJqlQ\nr4ypYDJ/j7bdjSUkrV5KKwaTv8jL5hk7eIFwWE8jF//uZBzJo7L67tm2ipFY\nb4Zn\r\n=Q2rX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/parser": "^7.12.13", "@babel/code-frame": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.12.13_1612314691690_0.2997042808409187", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/template", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "a9bc9d8b33354ff6e55a9c60d1109200a68974f4", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.14.5.tgz", "fileCount": 11, "integrity": "sha512-6Z3Po85sfxRGachLULUhOmvAaOo7xCvqGQtxINai2mEGPFm6pQ4z5QInFnUrRpfoSV60BnjyF5F3c+15fxFV1g==", "signatures": [{"sig": "MEUCIQD9tGkoYLEUtaTwUPU68inj6pApeOfoyIEKCNGU33LUIwIgDIHQaLm+5s4ZCR0ijXpqaehZ5Nq5ET6OVZukJ254iaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrwCRA9TVsSAnZWagAARi4P/iyNOV/dbePZGfk/AzhP\nAdedPkZKSGGbbM1z1D7ogZfZCnoNhbCp8UW0rdH5HiOJz+NtN4UKKK6/MYqC\nlUiiBcdv+9t95p+1A3WfsbPe4Vvqttvhp5PM8oykp0LBQUlY1ib1SbGx2xMz\n+NnQBzgV/sCT2WDC54p+QWgPgXP4usji2LCAaK3+pTAPGgQ/O7XPp1eqEunD\n13HckbrWCd2ZZNakiRl0aqs+x08y51hFtWFUcrqjlpiKkstHyHjKdt6JNa9P\nhZb/TJ3tsOEQ+k8l2uZZvX1/Mnw3aTk/impzK/uxYREXuvCbkomRfi9Fh5AQ\nKKenCmBxTQL1iKk/B9MKL1JfJ2LFb4hFpBLL4lQLBg8v0P+Z01HyOG+si+lf\nxcEzoGLayB8eIMiFgmIm77gOK3QFmNVOld/vPe/VLqweVs0sRMTlff7Qs7nC\n+ZtPDZyPEJxcUjd/52wjuSe1N57ya6mUdlSdfjFVqRBj6Qy1JlG52/Des91X\nqxcN3F9zz0S7SJ+v4gm42uRcKPOi1UG/u04n34BbFdWUImy/1A4dZvyGTHEI\n5vCFRbMwoKEoDr/R/qwFCe7gQ/2CWV5G0ztLgzwEz2tHlo/tLiyc9kVsDShl\nh0aNMIWiq6Wrmf0o65Gb+A6c6u/DLr5OjZU128Tl6qICbRpYjBjlh13gFmiA\nuyhw\r\n=lLcQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/parser": "^7.14.5", "@babel/code-frame": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.14.5_1623280368760_0.034582737498955574", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/template", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "51898d35dcf3faa670c4ee6afcfd517ee139f194", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.15.4.tgz", "fileCount": 11, "integrity": "sha512-UgBAfEa1oGuYgDIPM2G+aHa4Nlo9Lh6mGD2bDBGMTbYnc38vulXPuC1MGjYILIEmlwl6Rd+BPR9ee3gm20CBtg==", "signatures": [{"sig": "MEYCIQCV/Nhq+R2PYMoOTKDuA4Ac00mLyWvXPYCLkQgUFKBZiAIhAMj7+aJkQh9hCAzCwlvLGMMV0VZ8izURrxyG77WH8pSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSZCRA9TVsSAnZWagAAD8oP/iBm+/JR+Fm08pP+4nJw\ntqe2NPmIugWl1uwhaHooCosK5PhudNi48DAnSIzX7f+ZXAg2aPMHuEzeaz/E\nMxFf76cHAGcq4Pkmz9hw59PjlxauJVJ0EdgJsgASgwSdc90Cj1FRg/df0ecu\nuDV/FW8M4zdwZl6ZKcFrt/DFuENviki4a75o+j8N2tEsuS+ikB1xvMDlv8Yv\n7z4jCxiaCO98ZmYxDOIvwnyAe6QtaYo6Mo6qlcA6FumhO4Exyv2Mb/nUYgoV\nYcubAaclPtoiTmn9syUvPsazYDFkHlZJSsTZIRHX+nRIvYDbgx1/SK1w2A3i\nW6vgZqPQqb+JtTTvoGqpYXJDkcw/NbFKNbAimt/01i4X5CfVMAJA9yyQdLXS\ncFGchdkMhhxy4Cdkwoj+edF9KN39qT9+UT7klTnQWfpUzwiOxO7FLu2o3XuK\nUv4VjB2HFygLCrbSp/g84jUm0ZlqRZpHbQHew63RStm95quXl5OLDG//pPwZ\n+G/Ji2Mg3/c/ocQfNk+DpfWE0qktAKrTpxwKsWZCdiJlDi1dS8WXpWWxIzck\nh5mxKzP4BCkzSl1dcfc7lUDFDqbP95TsWb41vFb7afuNIGDQoPh4oNVS3EUp\n6yO/N8wIKAyLG8WS7Q/o72CAslRTme5Q+5SKKWwaa+8QYTHrt8Vq7/6BMK1I\nDE5u\r\n=w702\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/code-frame": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.15.4_1630618777175_0.5057245920775382", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/template", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "d16a35ebf4cd74e202083356fab21dd89363ddd6", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.16.0.tgz", "fileCount": 11, "integrity": "sha512-MnZdpFD/ZdYhXwiunMqqgyZyucaYsbL0IrjoGjaVhGilz+x8YB++kRfygSOIj1yOtWKPlx7NBp+9I1RQSgsd5A==", "signatures": [{"sig": "MEUCIBCBwWlL6J0HxXXwiYyctwp94KyXD6EIo4wkeY5/rqzWAiEA4cYyxhf794M71DxKLuRnlpSa1ocsFgPQOUyD6M09yd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHECCRA9TVsSAnZWagAAUSoP/3Ksjt/GjbViGs2MtKDG\nqOPBgTWCihbHO8F5OAQh91PJeD4PJPDNKJNLXhHaUKTBi5JJwgHbeLp41t9T\n+Ul2vz2OKgIHECw1VeaHgz3yLwg8NiZbkBcjYubV+jvNvng6NaNFNM8sEVG7\n/hwRstmLvLUhGwIlk7JQV7Cx/fweRnpKHaI4DFaOoe97lruQHOfRqrWW5igG\nZ/onk0QntxUjXlCTmORhsL6ZOvzRAd3TL3AmPKXUd93EiuFHs8d4KJJ4ZuQc\nyBMOYqSgrnEvQrd/jhA8Ru+xaK0VZxs01XhcOy7pislVJfSihR6NHaRwgpai\nlUbUlDRfnYcszxJHay+HFHiHhXCbt49kCPainsmsU98lOR4QyfJk8VwVmTsP\nHim0tAW7FsaJAWiv9MOUUWZ9nsqkat8XallTyQVSydzlY7MZo9R8uDuHrjFX\nOd6sBFQUV8ceRV3lpSBCas62D1JRmv3URUCpVNQ+1Y/qxWHcOoW9k8vemkv5\nJTX+mRpQPeriMiw2YgBFPdhUjBDaQxMY4djHVFhP06ia9Ge65mh44npYZFBv\nBvAw9wvgwzVxx/poXdkG50tYQ61OHbe3cu2iVWYzdiB9y1ENGOGNCi0xeVo6\nRoNPv8kuVDHgiNF2FP6b6k3V18S88X74f7S5YG7QqPOVTx9sQMSQAYGKFsp9\nXt4t\r\n=RgfO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/parser": "^7.16.0", "@babel/code-frame": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.16.0_1635551268960_0.1965501718284306", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/template", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "8d126c8701fde4d66b264b3eba3d96f07666d155", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.16.7.tgz", "fileCount": 11, "integrity": "sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==", "signatures": [{"sig": "MEQCIA5M5/TmrvNa3SXKTjWlcBtcyPNrLa5ZkRdu9qh6U4X/AiBWeVOSlKQ03W3QbGqJ5Vhv1kcT0kn+02wSd+Yp4E/BgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1QCRA9TVsSAnZWagAAL+0QAJ/ZqtGZVnCCTrZVITtx\ndm4VHHgdlYPyWhfDEVuVI7tSBUBOIwip/t1eUK2KzIzkoP/tWGkk+N4iTZAt\nGC+oHrj17RTWtVm2wvH+z9fKfTDjpnqJO15aO1NELGU3t4T3eLHwysT+xNwG\nbBaYgVMr2pEVQSZvL2jUqGEhGi4yhYltRZyJGGoVeyxAN8ywfB1+J757ovX4\n4LirWPZlV6i6l67qd4YwZIa9iNyeV1OqprQ5PI8XF2iqBH11GZVL6QMBo0/s\nXW7W/MKD1cr3K2K7NJB5eVHVLalPOcS3CJQqgZ1AaUzXpHc/9zuChiY5ZxvX\nUK/cFC+p1WtYm9jN8ykvbpKQDPrBtonHDK6xrg2kQYkWtJE2NT81AO2oiZge\nm596hyWNtCjDpQEIx/RrwQSqIxIIbRWhGnypRnPKEEN4kyclli+bYLnVgiEm\nvyc5/eMAfe0+gb2DN3HhI1FyUVbBO0/mxOFG4KgDi+4yNxyxZpHqPBN/r6XZ\nu8kPSeYCxoBoBPRI+0xCNmxi8F+8devrqJEj2Wh3Mz2p/sxKQ02ZK0r6tEPK\nYkEchiVnKlH9Qi6gil7HXzACxpDC9k+j8ZERVWUkfT52yBHexDKW9uRh/iHM\nxYyHn9ZW3njWLSvSCrU5lYCHnD+LzV9nam43XBsR4YHA070ffHZPR/gFoFrd\njrIm\r\n=TML8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/parser": "^7.16.7", "@babel/code-frame": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.16.7_1640910160088_0.14647672717666227", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/template", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "1283f4993e00b929d6e2d3c72fdc9168a2977a31", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.18.6.tgz", "fileCount": 11, "integrity": "sha512-JoDWzPe+wgBsTTgdnIma3iHNFC7YVJoPssVBDjiHfNlyt4YcunDtcDOUmfVDfCK5MfdsaIoX9PkijPhjH3nYUw==", "signatures": [{"sig": "MEYCIQDOZPUWwW5l4YmCg97c5QTjoc14Nhxv81OxzKKDN1csjgIhAM/8K7WgvC0gYUwosMUglIAiCeCuHyfoifLiTR4ByKw8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugn+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnjQ//QjZG4nwQOx59mcrbzjSoHZJ+bKsBqVPvpsYgeMpn60MjvN+/\r\nIHpkvm370w+xUKBjoEK0YsgHZxtOwNUU3F87Kz2ddeiw8J8IG43FjZ+HBvTN\r\nM/McJWFijDTRxH0xLTkME3cmD1Jbg7xclO0L0XWpmIhTNzf3dQ8cNKIeHbBc\r\nsW1xULb8gIqeYXIN38mPMvF2u8Ax1UA4h2K7A5MHYaeqAb1O5uArx1DXq60i\r\nP08aRh5PtxlQCRSw0EzaDRMN7tZsQBsRQElkmaNx5CQqH5QpBq9L2WzLvzDD\r\n2QZx3iU2ohnoINDleFf6Ate4PxoR4cWXYa5guPHGl5BjOjLJkbKTrzrMGDk5\r\n3RKYj1P2J/i49AYAKCdIGKrZTFZWBg4DpM5HhXxGxG3TUyhdJovp1Tne9KGt\r\noBbX/y3P10S1N0Zaugmc4yOzs5Gfm4ku6vVWMrfAsrU1iTXC1OS7B9l2mVB6\r\ngNxP57BSBmsAqTAFAcd9fxg0KWukNSq/urLO3Ak2kuXPJX6vTHGyHYIpKHVF\r\nQkp4MwvMmqBCt9TN1QD/OWNPsk3GBTlylPOxGq/0hzcW3hRtWS07SCo4tVa0\r\noe8cc5VXhnZyBtC4yZW3DFryQdOGDpoReDtMQglUS1e1+1GJcOhUXZcvzLB3\r\nzIdYJo6WbFe2Ciu2O2w4TquV2rF/J5PfC94=\r\n=aoi8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/parser": "^7.18.6", "@babel/code-frame": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.18.6_1656359422407_0.5096210653995918", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/template", "version": "7.18.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "6f9134835970d1dbf0835c0d100c9f38de0c5e71", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "fileCount": 11, "integrity": "sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==", "signatures": [{"sig": "MEYCIQDBAvwj/Lk/u45b6IlNIj+c79ozXYSUINB8N93TDGFnSwIhAOouf+lCKWHmNE0xKjHQJBwiNL6qK2G63/+taU9O0FUm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5xA//V7eWXHNTiOZmdEblcukukry4JT+DnUVdhIvwConHLDx4Mwpb\r\nYWBLjpkeR0yXjp1GvbbhYgwT/hHCegz4LhC9jDnXMfIis5s66Kz3z153XLri\r\n4TuXeg02hRVGjZGoVGEiaOmw4EMmZ+/b8lOAxM4K99hEhRlwc2hRM2sjs2Bz\r\nXJqmoBZM48MYv6ZFnOMQ6ZjBgjPwULv/HjVvmSNL2yD6fVxJc1qcUPR5VYDM\r\nGTs+QfDKHHX6wCLJbwZ5a/SJJL/AglZzlv4nGXWN17W9TI7fb/H0jW2pTtTx\r\nlosWslJNLLiTxYT0KGox4e7uw1suwAWHDtIwovBbgqVsm4sM3UWd1RahW782\r\nz9l/jksaogNtxjMD9m8m5jhDVz8WyZ3sNq5zJy2gVKaop00mB0nWgZW7ejFV\r\nJVeCD4e/6iCEs17uPMQsHbxO88N8p1KouihjCmvgJc3q9EaMjlhW37srywPj\r\ngwP+ShTMQ/oH97dzGKb7MpXJtVNbBqZ5Xu9TCRooXecpBtB3aUGa4AntwMhg\r\nfMxYcAilevk+/xWF4Pg6nwSAh/AA1XZMJ03iaMTL8m9yxS0JYcSkiv3OhB7I\r\nJN4wgoHkxazCQuuEEp8Jl3BIgPjTCddHyKxlxwBdFwFTNK5IaYkGJ8SBaHzy\r\nDVjQJ0VIl0f1nv18YESJOTiWQf60I0kGjP8=\r\n=oEDN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.18.10", "@babel/parser": "^7.18.10", "@babel/code-frame": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.18.10_1659379605264_0.9964845599949959", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/template", "version": "7.20.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "a15090c2839a83b02aa996c0b4994005841fd5a8", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.20.7.tgz", "fileCount": 19, "integrity": "sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==", "signatures": [{"sig": "MEQCIBmLUEr2wKO3mkHbDG1UZZRkeEaZHZ4+3UObhdlXBERYAiAD8zf1J/eukXl/7jH6urqCJIWEjbC164LtDF0KJc5CUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNQxAAhO3RrAFpGBxkpGgZL884ri6fzOVPhaOXcz+wLBoBxxowp5gT\r\nyEDH89TzQCkQ+/V0Jj1aJMBtTMApQAoCHfdE7kzPEmrpTODmtyTtJ3Ndp/OM\r\nJFdWTIR3DOkeDUN77rpZlfKZwYNnSo++ozY7mUs/MlJEDhvU98N1grBjrN84\r\nA1FI8MRAms2rnkhdaXWi3mcQy/p0B5+BVaMuk1iMg732gKSTDcjP6byfpNo3\r\n0sFoEzXE2Fq1NH6msS1GGb4zOJia75grw53v9ixCZkV7f8dZk6qIWsqhT4aZ\r\nYF+quScJ4W26uPmTt17bpkI6bMAr8yE0iAcbyUIuUiRVcSNu379IWow7XG3e\r\nLaRhd1DYm2yGKPAB/P1o38dtCq0CpAL4b7kq/bxxUnOL1+AbFVwIbL0JrzCp\r\nPmP8iadE1iy4UD8iGz3bJLeGBBNopATy3/2ZehYyBxBeMeRUH8AYYP7Jwtpe\r\nV3C4KElHH9Ek4BKy7vewWXOzTytWEA40s6O475fYImHkkPnXoysc4oKj5y3B\r\nDVwfhrvMTuY59qUK4GW3AI57/aaHw2vP5ypXa5IidHbuCAdQBTW8QbTLmssa\r\nah09+7PJvKcHZPD6bFBDTs6nXbep7yXPuTgcUao1Lr47vBXVpZIh6crjBWrn\r\nb1XmeQvtVD5X0vBAsbpfeEeKqnsuBbqy2A8=\r\n=xiec\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/code-frame": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.20.7_1671702333890_0.955805895013732", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/template", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "731712e90b8510bdb448b94b7b9ca2c1e6ec1ec5", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.4-esm.tgz", "fileCount": 20, "integrity": "sha512-MsV7CTAUQ9z0Tiqk41IGZRLDie0QbYYn09wi6YOnoaeiGpQH7DD0ACJiuo+rxSG2rj4WSzCfP5GlP8RatfSDHw==", "signatures": [{"sig": "MEUCIBZI+T0W17E9qgdkXe6zomyuAd4aumzsqznfYC4xHfa3AiEA0bF/mgtUdS1B+jkQ6ZM9oUAcKKsua2KnMA4wOwWbBL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwYQ/+Jm6l/67M+rUtqFLQ5YifVAXYlD5jQ8ze+Yf0lFiYHQ5B00zD\r\n3uLK+U2J+xmj+eUxK1ovBy9HYambM50Y4LOGT4jFXMgV4BJatfticZEiEJ7J\r\nsD+CQCwPUgNq2Fbwe32uOPD4Gu9LHTBAT+pzg7x5Yfec0fkEPcBahKIp5AdQ\r\n8mWm0EchUBzJK70G5QxupPzFQCpw2GwA7PgLUEjvnCz28FUSF6GKjX0Bb6aw\r\nYaT0+ZUxkVPJDbfgWLcE2ROmp64HPD5ufBiIr538+t3GH+nTpn17HfSfU3hm\r\nC6vnBh9R1Q2ytG1CYsfDJg4WVs7TDtXPkusbbzV0LNS/7uC7F2QJ/vHdBkBO\r\nVCHp4VPl+4dZL50qjXAxaZjBP9q9Sz+pqlB779Y/xpek6GIC0IZc58L9lEP8\r\npRTdW6lM+wrAWeo91AB5JOM+QHGxvLeDMsDC8HPRWHtJti9EDXI5UXoG/0BU\r\nNWYs7guvCdAdJ5E/wh84VcqfN/Z05sm0T3q5q5GxmZqzw/8evAjCniwMHVWE\r\nGW6d/AHtwsbL6kxhcD2DmooxZ4V2odkBYEGQQJxJs0AGObyHd/f6TQbrYngl\r\n4rcGmkqRJenjj9re1lznz5WSohOPS6GnxUmUGj9s3DtFs2zlrMPO/LUHQ76K\r\nI7UhGvDrOePtNCUMHiba5HjbFNz+JWwV2Kw=\r\n=KbmS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/parser": "^7.21.4-esm", "@babel/code-frame": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.4-esm_1680617385916_0.6131871937495765", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/template", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "b03f5ed11017087a2ef41337b6f3cabf2f6acae1", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.4-esm.1.tgz", "fileCount": 20, "integrity": "sha512-S6tw6gO/RqeRcDXp8tDxwYCau5babRnUcVrPMFGv8ltjxkNNM6Y56u/srvVWJNVLPfgxo5D5QTzDdSnJLVwHkQ==", "signatures": [{"sig": "MEUCIHk1MzjLk0PrV3EIgoc/mbKU7PNNXMzdaLYiw7otYF4uAiEA/GdAsGlC2UH8g0+txqW/Q36cyca2mDa9sMOLhz6Rs9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV6RAAio4Q1JOWugbjcy505vK5QcEJ2o7EI7reMTmVI+7o3qJW6vWF\r\nbT0IBBrxlbWnCLtNXAYnrXe41Cdon6seMlYchAh9ksBv8uXik34vxH4tySqW\r\njS+xru3h2nV5tmkDkeHXM1j3ZY1h6mPAl7LN8tMeyQdUAKiEkO4Op9d373vT\r\nLxTXRpaQdreYvaWNeynghI97ieW9q5VGR96H4TixEN8v1ZLqVxlF/iGdCM7V\r\nol4LCL5Y+i58RqlqpqB/hZbHh5g6QuwUjhnh3C1mNmo5DAXSIrNrjQh5tFjQ\r\nBSgMPrKv2nJFHaBNh2UvuWg/YNxvuER7REXTmw9Q88eIQqEgmqcJJ6dCEiTK\r\njR1dEHmCaeM2IawNzCs3peSQ/+vl3Du3P02ghA2bD0nqTOSbQYT/pHkU+K4k\r\nLvqTE4izQ1hefuQHNS47Mib08mSlj06xHdlHFKKryG4e5FwQSK4mJUF6m36s\r\nyxW+M/WqvGOyTkSzcrt4baS96SIt+DdZQPFJKCub6Ht/CTsEOwGB6CP1Suyv\r\noSosSxwYuPTtekCyOqgORpMW2b6m/nEI0PSCLscBJhHzGRzE9kEfjWoLuWE6\r\nEdalFuFo8SilXVM/kML14xLoYtnHwDRXFBRE5GVr6vOoQiorOSZyzSUD2WV/\r\nZsZQ07N9jPgIYv6Jgm/D6HCJUNbTMjA4To0=\r\n=5Jna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/parser": "^7.21.4-esm.1", "@babel/code-frame": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.4-esm.1_1680618103227_0.37075686714192835", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/template", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "0635a3fa79184d7d49fbce8fb2397c3117e8d051", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.4-esm.2.tgz", "fileCount": 19, "integrity": "sha512-MYWDBKNIzEufIsfxF/0LU8WkU8XCISUj70U3gWDmVQ/JT2YmODbvvgtcRmA9yowe9XzJ7PGf3e0/NoCEPGnGPw==", "signatures": [{"sig": "MEYCIQC97LKbl/jK6F9Kpj4tDH+fMgB1ci/k+VAAqDoyUCBn1QIhAIKOrExwVaB/ojBZ1DZt0wvWHeqaiqAIRrBeEU5yMfC0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDarACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEUg/9EfZsKxZ2tj8Soiq1wEnBnyyfLXdq0prDtmfBQp58Bnnow63U\r\nzrhHY7HW/fhE1yJlUIgoT5djOBBIyRhrryUTo8WTeIsBAONvBfYUhb0O63kZ\r\nIzPTgR3CpmU4eU0h4GOf8w/GJo1FPuOtutpkaTUjZ4kqhYruOu5WaSCoJuok\r\n9RoK7AyiMpUVMBh8gNuM5bfSCCsbTIlgeQO2MPHdSSRb3QXwWq73sUhKQ/FR\r\n1byi/8fu2h9XYNrsX7ADiVGTltjKJQbnX/ROgJmV2Wwffo9lY5OkcLyOricG\r\n5DrToVmVBul4hkzKAqZONx0ovPt2ob07xZjQsBr49EDX210jJ3XB1r/4MtI5\r\nNE8j9R6LLmWDPPixzZbndV364sY0APopeON0YYrESo2jpKdWd7dHtODJGfLn\r\ne1DEqfGHWBYrwjlNnTXz5eBpEqtAL5WTVPiTLQjWyCDlzlySn8esLgsrp54+\r\n0cj7fZAXsbzCa0J7JKrOQBM5U6f0H0V9hdoJ5NrjpNVV72WidkJhihPV8zzy\r\nNyAApirrFY8Q7ofEi5UxRLbrbAWz4hTFmovo0tDGprfWNK44VeZYUZN1yO3/\r\nBH/CImVArEqxtlaVp6RQ58HqFF4fgUxTTBwJaFqE19in0Zrj8jVEmlfMtmhn\r\nHeVJk0eV7jT883r3KLfcXrzhVY6rU4dGO5s=\r\n=lyhI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/parser": "7.21.4-esm.2", "@babel/code-frame": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.4-esm.2_1680619179738_0.47565168515146294", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/template", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "a45835e684ad924f2c3dda65fe1a289e2adf4fdb", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.4-esm.3.tgz", "fileCount": 19, "integrity": "sha512-0v9DbfQ2c5OYBflHoaUbDNQsbK8DSBuAaj6w3vSpLK6r2kpMI37mazZK/fo2SANdt/aFwn6qVsI8ztSM/ZxzmA==", "signatures": [{"sig": "MEUCIQC//lk1rs5O4MiWVOlKBZIUllGGYlr6MiaKpXj5KKWDqwIgNClj1oPLmMprzOjan9hFKTsEJT811ivzOWyqV6WdBqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPCw/+OKsqWN3RVI8sphhdleoi/nd4HyNMy2gTLBnzZhM/VKgQUEMo\r\nqHXjKa5wvHCYjiqxv9Viy0fjno27M2BYq6VJH7qZgOnv7L900pGrSCGaG1q7\r\nQXyk8yI0Bzi3rtHOo/Mj2fYWLn7rWKJqVLnMgy5bSg/S7dmRCS+eqYUL+Wb6\r\nI1l15GU7/Jv80jVM/GvKuB93RsPJjGtSL5zgOHheuqOnnDhOB+jmn1UkMafy\r\nFvuTG8H1kF3CSTx8BQ3XkN9iuJ83I/8JUfAxKXlIyQBGD210h71Y8+s2mG+6\r\np8/xEJJO9/yteKcaA43dfJ9euV36snXOaFy5ZCuWEHFTi62czmaRpRCvjYAd\r\nCg31b2CgbKodwZd5zCwQkgXnWwMYvI+ZpW37IWCa2K5XCDHw6i38tzLkh9q1\r\njHrkDPFTe80hz68kdpN9mYH2htFac+Famf231twVOzn701d9nKpKn888V0pk\r\ncdD5+0danOSZi36rTSJVLXwfTK+eBJslI+6heNTWCN2qG876UJQsXpq17F0r\r\nQxYiVrK8KsSBoeTVHP772YRDqzfOIMSOwinwN9lC2/SttiF/kcznuDyJPkSt\r\nr1P+FdXdc+gMaKvmJ2jaQH9jNT0T0yIZwW2jafyoJ6tV683ktBuygBaqjxJE\r\nm8TgpU+B3F7xn2CAm0DaQ83cgPbcT4cG39M=\r\n=nLFd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/parser": "7.21.4-esm.3", "@babel/code-frame": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.4-esm.3_1680620188006_0.30314706345235787", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/template", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "453f1dda94575433360b99da6c42739c5ca6888a", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.4-esm.4.tgz", "fileCount": 20, "integrity": "sha512-/H7EQ4Eu47sHNmCUUKFjvgz1wcdcWt1UbCqnjIvSrus5cvaEnm71/2k4byHa4FbvGJywynymj90826zhwAWlNg==", "signatures": [{"sig": "MEUCIQDCsjaPus0u8QRPhlEBW5L7Tj14M2lnEhgLABlxg9PBhQIgIizy7DAiiKFVNCp8+9KRsdX7oiuLrXReFcERD5dea/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIlw/+PYmYVyujxkO8Fqm6gQbcRfks6nOPdomE5uYAk5EYjLNFqHdw\r\ndivtz0oN17XJADWK8saQB/0r9SuHDP7Z3G5IwKo3EQ2NRy3iWvcjKB9PFDJ1\r\nnCwI1Y/rrRwqEoGhx0MdtnowqVwQnL+eIIbp6+vT2qwcDMYS5VPO9F6TOZRc\r\nnbLwjHZb67uPXeV+BjYd6YUJOVsO4zrU8Xp+DpumS8XNfgK+J7GtQVmw2CP3\r\n3ecvNoO/QREbqTC8SiHRZirAGigTjMl3flEFvCC5zow32vQfke+K8FVTl7n2\r\nEW63HhQTgczrkyFlhF2Z88eV0Laygt8cYwYn0EbEGeD8oaIHuvgkRCk9UJn4\r\nHwgmBv0RgSEkctSQNHU43RBca+OY1nB9p+gLFOTbBrvXA6SMqBIUjOWDcRbI\r\ncikRkVtok+KRpJASRnEXX/nmcxf5TtdwaI9FyfJ2flxavEHB6nC+iynyiEJm\r\nZ+VrtPrbrMGOsqCY5kcJnGbPXJ+HrS05n3C2LR5u9eToklR2IJagv4HiJ05m\r\nWiL1BC2ooJImZH8krSMMg16BHlwKwgLTMUopWEIW307h5UKgz5A/UvlcZSXK\r\n4tQuKsKNTGEImpMGrUSSG3Ru7c83Lt4uWbE6qMdHSMtJMd+zYkmAN9lKPZHz\r\nAJjz+DvXlPlLLLyG46mluYGE+3gL9o6zwWo=\r\n=VCU8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/parser": "7.21.4-esm.4", "@babel/code-frame": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.4-esm.4_1680621218717_0.2730631537131072", "host": "s3://npm-registry-packages"}}, "7.21.9": {"name": "@babel/template", "version": "7.21.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.21.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "bf8dad2859130ae46088a99c1f265394877446fb", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.21.9.tgz", "fileCount": 19, "integrity": "sha512-MK0X5k8NKOuWRamiEfc3KEJiHMTkGZNUjzMipqCGDDc6ijRl/B7RGSKVGncu4Ro/HdyzzY6cmoXuKI2Gffk7vQ==", "signatures": [{"sig": "MEUCIApgPupZQa/YEsm8o9zmY5oxYHN4sHfa3T2jZUB996DQAiEA/Cg06lsNJWa1g/4ufljRUrNhOFHormjOK0YHmS6G6FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68994}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.21.5", "@babel/parser": "^7.21.9", "@babel/code-frame": "^7.21.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.21.9_1684749741615_0.044159592247818846", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/template", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "0c8c4d944509875849bd0344ff0050756eefc6ec", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.22.5.tgz", "fileCount": 19, "integrity": "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==", "signatures": [{"sig": "MEUCICzeCWxJAOGBTz2XE/l7gDqhbxt7X3QfkyPerwZcoaxzAiEA9/zzkptjnBh97G0xWC9xrOfMrBq+Pzhf0XhpxObwamE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69045}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/code-frame": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.22.5_1686248495614_0.6074635057434561", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/template", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "76a582278e4aab060416914094bf8830db10a20b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.0.tgz", "fileCount": 19, "integrity": "sha512-F8fXClPBbJiZPeJZx+nGQad8wVWz6QL4DytTJk5wm8JthTExC+Tjr/wpzam1KawEik72o7GGYQuLbEUCbTGcFQ==", "signatures": [{"sig": "MEQCIC1k56r1P8p4joGp9KOdDX6mJQIJlJAdBAtY8WilzKwFAiBRBG77LFcr+pbHgGvKsdNuKnYGtXBr6/oSxzOSW8ZsVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128551}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0", "@babel/parser": "^8.0.0-alpha.0", "@babel/code-frame": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.0_1689861617497_0.2952277289040497", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/template", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "beff179a65eda4e6ccb1bff5e6be21bbf8045d1c", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.1.tgz", "fileCount": 19, "integrity": "sha512-dj/zFHfhS5Htz6d5FsO3si1FSWdOa1930+LlqW2KeXNY5ut7PKIc5nJ4cpKDgb3ynzdQbz0fwpmnVIRlv0pdCQ==", "signatures": [{"sig": "MEUCIQDbz3ACPjppTn3SYG9FsxNIHaYPjhznprhdbWscrkOxQQIgQSB3wJhODOq0qO0MNT2hAKPJt4LHd8MXKP+yxkZ/uS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128551}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1", "@babel/parser": "^8.0.0-alpha.1", "@babel/code-frame": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.1_1690221170815_0.3193626084649577", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/template", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "719b3a706e9364692e32d8be462a7fa9f9045dbf", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.2.tgz", "fileCount": 19, "integrity": "sha512-XSwu2Nu/SNhQY52iFLOTlCZxlgMUWCrcXv7dhj9dnz5I7wTiCgInfzlh3jIq5wE4TbgdfQAKSLfxywGbFNYxxw==", "signatures": [{"sig": "MEYCIQCKjeKCvMKtF8TIDBHX2DyrIBKqwTc/32gbLW7SWE8QeQIhAMHIEFMl3AKQkwB5N+bN5C+bzDSozlQjiwlNowBdhWGV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128551}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2", "@babel/parser": "^8.0.0-alpha.2", "@babel/code-frame": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.2_1691594114387_0.06557443984919731", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/template", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "09576efc3830f0430f4548ef971dde1350ef2f38", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.22.15.tgz", "fileCount": 19, "integrity": "sha512-QPErUVm4uyJa60rkI73qneDacvdvzxshT3kksGqlGWYdOTIUOwJ7RDUL8sGqslY1uXWSL6xMFKEXDS3ox2uF0w==", "signatures": [{"sig": "MEUCIQCyxzpOyyPx/6JXXjDGxeyD+83+ZDIbnLjddlekZ3QKUgIgCMgQIJwEamXI6Pdb/ggDk33+O2ZCu+4VvSMpeRY6KGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69156}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.22.15", "@babel/parser": "^7.22.15", "@babel/code-frame": "^7.22.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.22.15_1693830318652_0.8097690830980684", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/template", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "352a0d357766ff54dce6a2c6eb8b7af94a1225d2", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-E/rTIpPkQ278L2yCaQ8u+WU0/6TdGX7gaJcJ8cXDZO2x6nINVZATavAmx0XLDeQ/GqCUYnfh8un98dfTldHZkQ==", "signatures": [{"sig": "MEUCIQD055wwupZiIUVRHiozG2VMbVb9UQoKpM/qrvGTpxWPrQIga7qC+ZzPM+Umraom5k6cQykJvLTn8C88fpUOn1Chbcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66353}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3", "@babel/parser": "^8.0.0-alpha.3", "@babel/code-frame": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.3_1695740245769_0.767090484080305", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/template", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "c041e38d0e04d1c74263b520463c22fb9cec799a", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-B0htsMkRDXeZT2wrdJcYDcW35l9IfXadv3T1fevkMTfI3rnBrb84E6wdleEKLdbkMsS2wM9n6FKt23pQZ0hEAQ==", "signatures": [{"sig": "MEUCIQC6Aru8b7PJbk4VAldOqNKv3ta8co3t8KJ4Qk3QlXAgMAIgbXQvqT+yKHSVgL3Wk5q60iCwSvmOsxuXzgqvG8nGheE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66353}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4", "@babel/parser": "^8.0.0-alpha.4", "@babel/code-frame": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.4_1697076399883_0.6609043030369368", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/template", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "f7eb9b1a22effc1e4288a492a1e73db5ce936451", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-YhAi3napojKO4lD5Mfp627fnReKpGEvZN6yU7IPWI8ib64d/j7ReHX4WJW/sMyHDyZqRHtlWaqsJ+l7jUUDqzA==", "signatures": [{"sig": "MEUCIQD4Ao7Nf2o0AA3Hn9OkeNdISy+NCdp00lW1Cxi2BVqqFgIgMikyPTcg6xZo81dc9Wm94thcox68eNgGbwWuQ9uJjmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66353}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5", "@babel/parser": "^8.0.0-alpha.5", "@babel/code-frame": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.5_1702307969113_0.12194600552628376", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/template", "version": "7.23.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "f881d0487cba2828d3259dcb9ef5005a9731011a", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.23.9.tgz", "fileCount": 19, "integrity": "sha512-+xrD2BWLpvHKNmX2QbpdpsBaWnRxahMwJjO+KZk2JOElj5nSmKezyS1B4u+QbHMTX69t4ukm6hh9lsYQ7GHCKA==", "signatures": [{"sig": "MEQCIHvigFyX5C3Ygy+VLOGRA11Un/bRqcYjRurY4pbSkfAbAiAxFjs5gnn2Cy4XrZbum7IeAfSwiRQEI79K+j1FMKqEXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69027}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.23.9", "@babel/parser": "^7.23.9", "@babel/code-frame": "^7.23.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.23.9_1706201873282_0.5250661562866343", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/template", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "297ed62932e9b27b604598692694c9619783963d", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-K/EP33UFTLdopfhsy1wraInMn0MfhwyAB8+qMCPwYHu/U1mpox43i7z6IrnbtYHPH8RxUWrolVHRt73qiGmIqQ==", "signatures": [{"sig": "MEUCIQCBS0z8cRQQtzqxYym1dJeLIgYPnIhh8xuqCRys03e5XwIgIdH79PpQnv254d7r91lf8lrsI3SikxRIEd+owbMwiuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66461}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6", "@babel/parser": "^8.0.0-alpha.6", "@babel/code-frame": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.6_1706285668408_0.41085167473024686", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/template", "version": "7.24.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "c6a524aa93a4a05d66aaf31654258fae69d87d50", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.24.0.tgz", "fileCount": 19, "integrity": "sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==", "signatures": [{"sig": "MEYCIQDHWotcVD4IcBOeME7V3fEFm3jIvrhZTaxgyaQx/P+ATQIhANuLRMfNlO4iXBYZHBSh1z1nouGNjwZY80/DvPsuhHwk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68860}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.24.0", "@babel/parser": "^7.24.0", "@babel/code-frame": "^7.23.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.24.0_1709120863661_0.9936643311416045", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/template", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "2fd979901557badd52d0bc65357af670acb6d511", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-lbg78WoTM22uJJxH+w4uP+8A4KAvH+sqRSNHBelQEgItSFjHhLaq6f031edsMRQeRN6ybpn7/n5U0DJKPCZWxQ==", "signatures": [{"sig": "MEUCICaun8Muoas7hHhQtIkGCM7iXxabgsA/nSj2qfngNEX/AiEA1loPVKW+tSRDkFaLa6NCAMXDH34sBGOwNEnisLt1LpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66270}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7", "@babel/parser": "^8.0.0-alpha.7", "@babel/code-frame": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.7_1709129128486_0.8859307889196886", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/template", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "ad4559a150c12d3d839fd5087f5f0b75b9c93626", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-n7ZG5zBYo7nTQi+HErnLzKjaaJxWyIsGGn5Su77O5Qsrt3/iMuWjdNYTlGe+xpG7e8Mhm75Tr7iFe/gxz6FJFg==", "signatures": [{"sig": "MEUCIHBVnoVkv9M4bnt7ml3RdZWhS91kRFJPH82IzBoRNOxiAiEAoQSMA49HpWBFHbTipv4dcAoVtijmtCH2/X1iODuefUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66270}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8", "@babel/parser": "^8.0.0-alpha.8", "@babel/code-frame": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.8_1712236809502_0.1401994680776788", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/template", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "048c347b2787a6072b24c723664c8d02b67a44f9", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.24.6.tgz", "fileCount": 19, "integrity": "sha512-3vgazJlLwNXi9jhrR1ef8qiB65L1RK90+lEQwv4OxveHnqC3BfmnHdgySwRLzf6akhlOYenT+b7AfWq+a//AHw==", "signatures": [{"sig": "MEUCIEW2P3/b0CE0t2ayMY3P55JNlR/jP330qtf9KBuHONtaAiEAtCmVQYguUIVcFsprvJPk1tVzmg0MhQ/mYLgFqh2KvpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68988}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.24.6", "@babel/parser": "^7.24.6", "@babel/code-frame": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.24.6_1716553499996_0.8812705477417464", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/template", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "7e2579faf898271bb003e76d1c3b9a649c83b3d5", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-M+GY6ECycJ93Nel2ulgKGW0h9MWuM3eNL9emlwBoxnSOSbinvZ+9PydqFksO1UxK7BPj4l770N7jBXTOmigoQA==", "signatures": [{"sig": "MEQCICjyUB1J6Qq4ytrsNkdKF9rEeDs+r/TNxJJtLD8XxTIbAiBTc6bHD3JRAYzs3qMei0vEFiAPriM5Z/MHYyu/xcG95A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69644}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9", "@babel/parser": "^8.0.0-alpha.9", "@babel/code-frame": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.9_1717423487746_0.9201808248350596", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/template", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "7e533c8bbd8dec66c5a185ca0bbe96c87af9982b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-2zNo2awA9PUyR2N3SGQ2s95bSwX4q1fmoQxHSjVjRy5Ano9qUqpkWMcv9BawsVK1VeviRIfyb7Kr0pL163p5+g==", "signatures": [{"sig": "MEQCIH9v3npvSLK0p5EK0nO1jzxwwlWH6AAXyO/H0cY+w7J8AiANDUgQ+XE0E937UNaH7sk2Eu2cgKuTNPoo3SGLqzIB8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10", "@babel/parser": "^8.0.0-alpha.10", "@babel/code-frame": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.10_1717500024233_0.8750023268512122", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/template", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "02efcee317d0609d2c07117cb70ef8fb17ab7315", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.24.7.tgz", "fileCount": 19, "integrity": "sha512-jYqfPrU9JTF0PmPy1tLYHW4Mp4KlgxJD9l2nP9fD6yT/ICi554DmrWBAEYpIelzjHf1msDP3PxJIRt/nFNfBig==", "signatures": [{"sig": "MEQCIBq+CPkNF9Zzc9IkkWzqpivZbFRJ2oFKwy1dBGmpN4K5AiAPfex8+W8KStpLKbfCHCdxji8hFT8C7BTLEQ50grZn+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68988}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.24.7", "@babel/parser": "^7.24.7", "@babel/code-frame": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.24.7_1717593338465_0.12372760491387003", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/template", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "6b83aaa3439223e2730ccebe5fa8bdd510ab5e75", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-5W8/Vu8LFMqUUc7/r+Zy41Bz7ju8FeORFQT53WEIEP404mJnJml7g4mM7WrSk0C/LtJAgnfCUHZfUOlYk+jVgQ==", "signatures": [{"sig": "MEQCIG3sM53F8RBPKVVWRMG3XIeMEux4v9aYnjEhOTOYe74BAiBkZ0GjxeNhUUzATa8atu5gjNnxlJA9lemiTyCBeWkjYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69648}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11", "@babel/parser": "^8.0.0-alpha.11", "@babel/code-frame": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.11_1717751748662_0.9223377451786319", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/template", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "e733dc3134b4fede528c15bc95e89cb98c52592a", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.25.0.tgz", "fileCount": 19, "integrity": "sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==", "signatures": [{"sig": "MEUCIGonAG+e69jfLttWugPhcrdgdIm6YcCIIfUI86wcRVotAiEA0TpIYm5JrSHBMJjyCFrQgqCAMboSXb9iERX/W2/dZjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70356}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.25.0", "@babel/parser": "^7.25.0", "@babel/code-frame": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.25.0_1722013165732_0.49367654062889765", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/template", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "4479467483bbb13966442e6600df2cfba15a1374", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-n1m4qrdPCBeqLdZst2r3HuETN9txoMtqBmDezCSH34yOEgQ0No8nN78XUgV2lohrzrJxDAiRR5uaVZqRpE/WUA==", "signatures": [{"sig": "MEUCIQDK5aGIWcXi+XSKujCr0hCfIEyyWsdMQTrQYBMHtC0D9gIgOUS0q3VcK+2+P0ivDCYs9C1eXBb9nN8rv38J7kmuvPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70968}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12", "@babel/parser": "^8.0.0-alpha.12", "@babel/code-frame": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.12_1722015222763_0.20384274341309072", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/template", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "27f69ce382855d915b14ab0fe5fb4cbf88fa0769", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.25.7.tgz", "fileCount": 21, "integrity": "sha512-wRwtAgI3bAS+JGU2upWNL9lSlDcRCqD05BZ1n3X2ONLH1WilFP6O1otQjeMK/1g0pvYcXC7b/qVUB1keofjtZA==", "signatures": [{"sig": "MEUCIQDfFTlfqXtCMvJAnbgQj20110KLSZmW8TqgV/AMmOixsAIgUgvXR7Ro49h7QjtqJ4/THro+unfQb7yqET4+8NY1bW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130538}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.25.7", "@babel/parser": "^7.25.7", "@babel/code-frame": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.25.7_1727882108585_0.6421275484488895", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/template", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "ecb62d81a8a6f5dc5fe8abfc3901fc52ddf15016", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.25.9.tgz", "fileCount": 19, "integrity": "sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==", "signatures": [{"sig": "MEUCIQDWhCodCAZ2A1fpll1cFtGUF45KoaAtBjoLwZBM+uU95AIgUBpTr7xOWmdep54B+jOPepzhPK3fhXTnTD2+D5b9ZpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70338}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/code-frame": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.25.9_1729610483942_0.316760415132201", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/template", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "7b3e59312104b8bf94b0c2b0b89100bd89bf6034", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-LWTTYgBl2Ki+NOUQcQvcGcqO1FZl/FNxbZvGSJ5XJQM84PxFSPUDkoNFCvYLanz7uKKu62q/QYRtqu01WxFxdA==", "signatures": [{"sig": "MEQCIH1OdJwUX7GGCm0v3aXuwdTd5/aonFAQVeWiFqIGbWz+AiBtz97HnLx/9NIJNMNCjGkdjrOocev2LJsrl9Mx5qqZPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70934}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13", "@babel/parser": "^8.0.0-alpha.13", "@babel/code-frame": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.13_1729864464859_0.8956010105233423", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/template", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "bbc7798f4184502c762a69d06cf3f9888963dfff", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-XAMUTiV0t/ilePx0r2POLPmjVCAIhoMYoLkYzpYQ5l+yICHCkAgJRxxAa8t8E7fpbgZcq4U9r0BL+w5o+4TbUA==", "signatures": [{"sig": "MEUCIGTv6gftWTtlvxoBVYTzizg62aeEpMf4U0p3TRffy6QbAiEAoe3htAXPF9SR9LHtJwlxvSfbY3xidw6jcSpUqw+CC44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70934}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14", "@babel/parser": "^8.0.0-alpha.14", "@babel/code-frame": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.14_1733504055255_0.6842280876548845", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/template", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "168fefee3477f052f38832f50776a19d4e3eb14c", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-3/Dy/6rRyXwE955dHpahqIX2aEJedt/+7NWMIcsxIrMW9QXQQm2L2VftGMjmSMLJZCJzVMkz5/sg6yd/A4jQYA==", "signatures": [{"sig": "MEUCIFmS+Dmp/0lEcGaJUjKKhOe4dfoIeZt1wax3rPFZVeIKAiEAn2s2K0JvgDBBVZrxWqc2ro29e/Q2WDZIUMTcugQAIEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70934}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15", "@babel/parser": "^8.0.0-alpha.15", "@babel/code-frame": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.15_1736529882227_0.39422306214930747", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/template", "version": "7.26.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "db3898f47a17bab2f4c78ec1d0de38527c2ffe19", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.26.8.tgz", "fileCount": 19, "integrity": "sha512-iNKaX3ZebKIsCvJ+0jd6embf+Aulaa3vNBqZ41kM7iTWjx5qzWKXGHiJUW3+nTpQ18SG11hdF8OAzKrpXkb96Q==", "signatures": [{"sig": "MEUCIG7CXFZvOOmV523voPUqVZJjSpQ4r700AMtDnbIi75jRAiEA9VyaeNqHU7WCqbSEnE1vg9PNOCVQBCXjoRo2CPIyfks=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70351}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.26.8", "@babel/parser": "^7.26.8", "@babel/code-frame": "^7.26.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.26.8_1739008768069_0.3996507394742628", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.9": {"name": "@babel/template", "version": "7.26.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.26.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "4577ad3ddf43d194528cff4e1fa6b232fa609bb2", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz", "fileCount": 19, "integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==", "signatures": [{"sig": "MEUCIHzYhkGSJgnnAhWcwB6JyLoywn+iZKe1vy9+mLsCBGvrAiEAi8V1KHX4qIekKKceKXhS47tffeK/2WwtmqUke5KBxcY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70276}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/code-frame": "^7.26.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.26.9_1739533687448_0.13477549788136312", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/template", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "274d38faf828fc24ed9d50f84740bafdca4d81c9", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Ujd0cao6Jo0plGFdGrhtMIgjZm6u7gJ00gyq/onuTH8ggD6BFFANH/cguSo79OUYCIsa20rSCFB2vbjWSEAu4Q==", "signatures": [{"sig": "MEUCIFLAL/wfw+vxV/N2pW7eviB+LT3ZCaMPmdxUXe5A5CTzAiEAiaY8VmsNKD0PVvms3lRtM1OktB199U41Y7v/9MKHdd0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70935}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16", "@babel/parser": "^8.0.0-alpha.16", "@babel/code-frame": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.16_1739534358411_0.9825634533192469", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/template", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "a132ca57cde3a54734859553a78355880bdd6b9b", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-b6CW6G+aM4+DDjG7FGVHEh/12H24aQaxCOH/lxRlCfp3Vl2OjILynJwEcUfUWv6BO6O33/wK4VP1B8xU5UqsfQ==", "signatures": [{"sig": "MEUCIQC6lWLM7bwe+ewgpmqdo2LIhw+LfvtUqlg2OjJAB4dr3wIgT1C1jWqpsMoCm+y1b1l88fBq58VXK0R5igxwc45fcsA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70941}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17", "@babel/parser": "^8.0.0-alpha.17", "@babel/code-frame": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-alpha.17_1741717511207_0.1957166541928521", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/template", "version": "7.27.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "b253e5406cc1df1c57dcd18f11760c2dbf40c0b4", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz", "fileCount": 19, "integrity": "sha512-2ncevenBqXI6qRMukPlXwHKHchC7RyMuu4xv5JBXRfOGVcTy1mXCD12qrp7Jsoxll1EV3+9sE4GugBVRjT2jFA==", "signatures": [{"sig": "MEUCIQC1ZI720+oDg4x5Meecei5e0eWz++nhbcKT8svsq1JQkgIgHd2uL69UIBz6A9nePC3ZZhZ/XUP3Y9h+lryeJQmGoVo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70675}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.27.0", "@babel/parser": "^7.27.0", "@babel/code-frame": "^7.26.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.27.0_1742838109114_0.524415054860131", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/template", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "b9e4f55c17a92312774dfbdde1b3c01c547bbae2", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.27.1.tgz", "fileCount": 19, "integrity": "sha512-Fyo3ghWMqkHHpHQCoBs2VnYjR4iWFFjguTDEqA5WgZDOrFesVjMhMM2FSqTKSoUSDO1VQtavj8NFpdRBEvJTtg==", "signatures": [{"sig": "MEYCIQDekHWu1G86SzkyMt7qj8im5qPjFcQbNwF72eMccEpaRAIhAJz9k7cSlhxka79aK9gIkK/AlgWZdM6cYkWLkOkKQyhE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70675}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/code-frame": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.27.1_1746025747937_0.25386794409985836", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.2": {"name": "@babel/template", "version": "7.27.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@7.27.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d", "tarball": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "fileCount": 19, "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "signatures": [{"sig": "MEYCIQD8VydbLlwvKdWDLzB7ujDT8XP7rE+TrxAsB/8b6Ci/0wIhAKRr9keHmHe75fRzGtxKdxH+YJOXGbYGUp5K4rf410WA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70763}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/code-frame": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_7.27.2_1746545628782_0.848305448657614", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/template", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/template@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "dist": {"shasum": "dc9019a69e05236eeef68581699280fb492e2dcd", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-rEjNhNa3wKLASwZQEKecArXcO5qIgwtnvjUvwdK0j71MRfEt8BFbKC10xUcWVlIgZMrwu6OHxMQbYJPnwmHwFQ==", "signatures": [{"sig": "MEUCIBPF4NvOrJVWvcuKDnlwxhoOCJ44bXHRAfESnf+VwC7yAiEAzCxW3HyYsGVfR8Yt1f44qmSEDkxj+ia4EdvsA5eqTGs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 71340}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0", "@babel/parser": "^8.0.0-beta.0", "@babel/code-frame": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/template_8.0.0-beta.0_1748620284495_0.456375349358384", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/template", "version": "8.0.0-beta.1", "description": "Generate an AST from a string template.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-template", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-template"}, "main": "./lib/index.js", "dependencies": {"@babel/code-frame": "^8.0.0-beta.1", "@babel/parser": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/template@8.0.0-beta.1", "dist": {"shasum": "d5dafb38b7124467feacc7921c4d5dc7aa55840f", "integrity": "sha512-ZWM8s0FhLeKQvYhabtaXL7uJpClUDKC0RWBd5GMaY9+zGEc+xJBzps6LgHxFZKik26PnW3x/Z5y3eVi4Q/W9Rw==", "tarball": "https://registry.npmjs.org/@babel/template/-/template-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 71340, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCQ9VSWHKVHtCsCAvhKiKNbqSg5JgP4gq2npeyj2A6CtQIhAM9hZdliG+x6tCL06mUyq4DwNcj41IhKyXTB0xaTXwk8"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/template_8.0.0-beta.1_1751447069077_0.6859015011156218"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:33.711Z", "modified": "2025-07-02T09:04:29.510Z", "7.0.0-beta.4": "2017-10-30T18:35:33.711Z", "7.0.0-beta.5": "2017-10-30T20:57:10.361Z", "7.0.0-beta.31": "2017-11-03T20:04:00.039Z", "7.0.0-beta.32": "2017-11-12T13:33:45.470Z", "7.0.0-beta.33": "2017-12-01T14:28:55.185Z", "7.0.0-beta.34": "2017-12-02T14:39:55.211Z", "7.0.0-beta.35": "2017-12-14T21:48:13.698Z", "7.0.0-beta.36": "2017-12-25T19:05:16.354Z", "7.0.0-beta.37": "2018-01-08T16:02:56.542Z", "7.0.0-beta.38": "2018-01-17T16:32:24.609Z", "7.0.0-beta.39": "2018-01-30T20:27:53.383Z", "7.0.0-beta.40": "2018-02-12T16:42:25.501Z", "7.0.0-beta.41": "2018-03-14T16:26:37.157Z", "7.0.0-beta.42": "2018-03-15T20:51:50.480Z", "7.0.0-beta.43": "2018-04-02T16:48:45.508Z", "7.0.0-beta.44": "2018-04-02T22:20:26.488Z", "7.0.0-beta.45": "2018-04-23T01:57:57.075Z", "7.0.0-beta.46": "2018-04-23T04:32:16.385Z", "7.0.0-beta.47": "2018-05-15T00:10:11.879Z", "7.0.0-beta.48": "2018-05-24T19:24:09.406Z", "7.0.0-beta.49": "2018-05-25T16:03:39.692Z", "7.0.0-beta.50": "2018-06-12T19:47:46.796Z", "7.0.0-beta.51": "2018-06-12T21:20:24.948Z", "7.0.0-beta.52": "2018-07-06T00:59:38.857Z", "7.0.0-beta.53": "2018-07-11T13:40:38.388Z", "7.0.0-beta.54": "2018-07-16T18:00:20.811Z", "7.0.0-beta.55": "2018-07-28T22:07:43.570Z", "7.0.0-beta.56": "2018-08-04T01:07:45.770Z", "7.0.0-rc.0": "2018-08-09T15:59:24.133Z", "7.0.0-rc.1": "2018-08-09T20:09:11.125Z", "7.0.0-rc.2": "2018-08-21T19:25:19.823Z", "7.0.0-rc.3": "2018-08-24T18:09:01.331Z", "7.0.0-rc.4": "2018-08-27T16:45:30.727Z", "7.0.0": "2018-08-27T21:44:17.393Z", "7.1.0": "2018-09-17T19:30:29.158Z", "7.1.1": "2018-09-28T20:03:02.198Z", "7.1.2": "2018-09-28T22:19:57.594Z", "7.2.2": "2018-12-15T10:05:38.929Z", "7.4.0": "2019-03-19T20:45:04.692Z", "7.4.4": "2019-04-26T21:04:38.296Z", "7.6.0": "2019-09-06T17:33:52.250Z", "7.7.0": "2019-11-05T10:53:28.805Z", "7.7.4": "2019-11-22T23:33:28.291Z", "7.8.0": "2020-01-12T00:16:51.065Z", "7.8.3": "2020-01-13T21:42:02.911Z", "7.8.6": "2020-02-27T12:21:33.135Z", "7.10.0": "2020-05-26T21:43:30.312Z", "7.10.1": "2020-05-27T22:07:48.424Z", "7.10.3": "2020-06-19T20:54:32.896Z", "7.10.4": "2020-06-30T13:12:43.921Z", "7.12.7": "2020-11-20T21:05:55.452Z", "7.12.13": "2021-02-03T01:11:31.852Z", "7.14.5": "2021-06-09T23:12:48.888Z", "7.15.4": "2021-09-02T21:39:37.380Z", "7.16.0": "2021-10-29T23:47:49.111Z", "7.16.7": "2021-12-31T00:22:40.223Z", "7.18.6": "2022-06-27T19:50:22.582Z", "7.18.10": "2022-08-01T18:46:45.502Z", "7.20.7": "2022-12-22T09:45:34.079Z", "7.21.4-esm": "2023-04-04T14:09:46.050Z", "7.21.4-esm.1": "2023-04-04T14:21:43.400Z", "7.21.4-esm.2": "2023-04-04T14:39:39.905Z", "7.21.4-esm.3": "2023-04-04T14:56:28.144Z", "7.21.4-esm.4": "2023-04-04T15:13:38.915Z", "7.21.9": "2023-05-22T10:02:21.779Z", "7.22.5": "2023-06-08T18:21:35.789Z", "8.0.0-alpha.0": "2023-07-20T14:00:17.794Z", "8.0.0-alpha.1": "2023-07-24T17:52:51.077Z", "8.0.0-alpha.2": "2023-08-09T15:15:14.569Z", "7.22.15": "2023-09-04T12:25:18.841Z", "8.0.0-alpha.3": "2023-09-26T14:57:25.939Z", "8.0.0-alpha.4": "2023-10-12T02:06:40.112Z", "8.0.0-alpha.5": "2023-12-11T15:19:29.318Z", "7.23.9": "2024-01-25T16:57:53.506Z", "8.0.0-alpha.6": "2024-01-26T16:14:28.565Z", "7.24.0": "2024-02-28T11:47:43.818Z", "8.0.0-alpha.7": "2024-02-28T14:05:28.624Z", "8.0.0-alpha.8": "2024-04-04T13:20:09.716Z", "7.24.6": "2024-05-24T12:25:00.152Z", "8.0.0-alpha.9": "2024-06-03T14:04:47.877Z", "8.0.0-alpha.10": "2024-06-04T11:20:24.371Z", "7.24.7": "2024-06-05T13:15:38.649Z", "8.0.0-alpha.11": "2024-06-07T09:15:48.894Z", "7.25.0": "2024-07-26T16:59:25.984Z", "8.0.0-alpha.12": "2024-07-26T17:33:43.024Z", "7.25.7": "2024-10-02T15:15:08.776Z", "7.25.9": "2024-10-22T15:21:24.173Z", "8.0.0-alpha.13": "2024-10-25T13:54:25.155Z", "8.0.0-alpha.14": "2024-12-06T16:54:15.426Z", "8.0.0-alpha.15": "2025-01-10T17:24:42.486Z", "7.26.8": "2025-02-08T09:59:28.281Z", "7.26.9": "2025-02-14T11:48:07.640Z", "8.0.0-alpha.16": "2025-02-14T11:59:18.679Z", "8.0.0-alpha.17": "2025-03-11T18:25:11.428Z", "7.27.0": "2025-03-24T17:41:49.282Z", "7.27.1": "2025-04-30T15:09:08.154Z", "7.27.2": "2025-05-06T15:33:49.009Z", "8.0.0-beta.0": "2025-05-30T15:51:24.688Z", "8.0.0-beta.1": "2025-07-02T09:04:29.265Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-template", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-template"}, "description": "Generate an AST from a string template.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"colshacol": true, "flumpus-dev": true}}