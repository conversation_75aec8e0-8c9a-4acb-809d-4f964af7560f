{"_id": "@esbuild/sunos-x64", "_rev": "95-d88a468626c1cb7721dac5bbb7cafcfe", "name": "@esbuild/sunos-x64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/sunos-x64", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/sunos-x64@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "1e82804e8e3b6467287c77a509053a2d894bc0ac", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-xTjp8RfJsslxgJOogkR7siakysilEXWNP25UpCP7gcc+uDMc/8oqlTzzIM1+FozH9EAAYNfujmuNB0nSM00VTQ==", "signatures": [{"sig": "MEQCIDcD67NHhPxhqOmoi57bUBHed35Cz8NvnzF2cXLJnayLAiBnnx3RiRTy6D1Xcyo7GaCe/ArQwxuC3I3jd5P3XxQ9wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8614362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoWIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGCg//SnfCm0SUGRgc/awLrFP5hQkEWWBPQ2/xBFo1sN5VY++79lGM\r\nPAUGx36iXBchkXxdT8UaPGyb9OUziGmqqt22Qt4ToUbhtzenRJsWIBCgUon8\r\nLg+qyH1bu8OWI+MkngbL4DGFTE7zonFSZzEbsy/PBQvgLcc+Zv8+QUgzf7Br\r\n1VOyEJyRzjTfA957MtMHcqtDQyxZuTPX3xI8Tmxo5G4ourQq4EWbGbIAYmLz\r\nCGE7VcwRrHoZDEG7nqVFkCLvv2GBV+qX1pCT6nkb/jZeG10JM+qFSAEwiECM\r\nnuNGuqcqUfTngeJuvspKu5MTtTBpvUlY1Xyt7B1uSMUdN6nq80xzwGsWDpuP\r\ndWwkUe5YHKlYbSeKz3senGzdm+qp2D862UX/KVA6JthxeRDJkE4RmJPXfhJS\r\nZiQej0mrtIELguIqnowQwzAnsrcGuwDb9cAxoLdclBvVetgKpV/KTbHntwnu\r\nfUuZGplJ+yIudVUgrp74oX0cXDktguXljwwmJrhl2WIaa1vb4MGzV9HuhsIe\r\n0dQ/yNtsXUa6rh90kz9TVGVm2xEeQrXRW679fbauoa/gpBFjkVQow5hdC0t9\r\n50hEwgll923QJnRlsi5T3FMB6CSZVesKRjrgT1Mmju4fUescasoo0GGRigjG\r\niJE/UDbn+211QPpBoMRqa+DPkMtiXIQfx/o=\r\n=9nho\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.15.18_1670284680260_0.13775520307881073", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/sunos-x64", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "19fab3e80b2882c7d0a3231cd9ef12bc31838ae8", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-Olqs6oAqyRdm1/MyzE4OQJVl9sF6gUMXatDtd8+cgzUHc/UcMprspRogGSIngck910o964qMeY5YIcayggo20w==", "signatures": [{"sig": "MEUCICFWexKr0b4ag7Mou6QE/D4Fy5oYr45zMcXZZKG9oFLoAiEApt/XPmkBLKRLIEzdhZgnKn1wQG7OhlcnPiCSSAEc8WY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8614361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPZg/6AlXZDaaIf8hOxCbq/Ed8Tw+CKz0reBmbnk09GygnaCIwS3f5\r\n9SRfo2gbXLKVevuAq6gIeYoN619mixH/EUBULaw26UIzLD6ht8iOb7U/9ib7\r\nFOKO4MIlZpTYa9j3mSjyNXxI6nqrC2VAX6p3cYlg76k9KX1phcs91QFnmotL\r\npCGaq/ksmCyV4BwnJug9SoES3Jzz97Sgpxz6aqAzZDxOUfyxaJ/NGgUg7fZp\r\ner/Ki5g5D/M4nWanAyNYgpEwSjPZHuCK2d8HqX0gCi4XmvVnx+FkMh13Ig12\r\na0+mndZ63CCoiB3l02Fw1M59BGrtdcIzCXLt5mOPMb1V93GzOcsXv5PVnjTh\r\nS0C2LTbpSxt4cJvzMcnV8oxci4wpXH1KYpw+rbJaCF7RQh5GNju2usgRiucd\r\nDaWpwBjN3Mh0NJ2N+0GkCanXsOh9vaBDgDRef31iUBj3UDU9f8qcwKmPFZkL\r\nC64pIypvdg4TjMsHm/lsXdjvBKuXs2f3/LLY9Pw+hIDFYzOuovdCXO1eViuP\r\nMM+em5/+x8e0p5sW1mpBWkrmfdH1FpIEERZHYSVAGD4H2cFMIa2QH6BHHCLM\r\nsJhxovLn9csbRSohvRC72M8wMn1CWsSZOcpSL210EBIGF4mMpmkp8/QPY6Et\r\ngxsSrRCPfGH4vJ0h5ocdt3E3JcRnYCNqQno=\r\n=8L7x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.0_1670385285000_0.3282850943937541", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/sunos-x64", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c8d84eb61f66d3811051b9b5c4b63e9ee126217c", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-KgfRBLjr6W9iyLLAOU58lSJ7/6W7H+KoDV27CGpEv0R5xR2LYMAE2SQ2sE0r2CP1rDa/huu/Uj1RvcVZ5nptqg==", "signatures": [{"sig": "MEYCIQCoI1pC+qfd0skLHj06OcI9hBN1X+62THOZOYe1MTj1bgIhAOOKyojTvJ7C+F1CKx8BulC4bSohva9bsl5IdwWnTM8V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8614361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBr+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTUA/+PEXFS8FBUSD7iGNSuNc1NBmp9YtXubi8Ig+zRU4ZadRGaSq7\r\nUCit6FHcmhGSQUSmTReBu8H5jUhnDIy8n1OU+/ShlOXwnL4OJ00QdIohe9HC\r\nZu//nv/3bfllgIpUxpmd5SwmsaMCK3Nx4vLbhS39mCiks+wi9iaIC3HXawTK\r\nlH2cU7IdPEnLOTH9V7/JMJ6WCTVte7QcmS4y/G//hEPk/NIEqfXJA2U/mRyf\r\nFD1QXRw8RH9O93ggBde4jZBS4KCWzF8jdSIop/yczErtc6rrxrp3s8UmZrph\r\nv0ffJGujwMk6Fk0PJu8QEHGkjOl///j6w1+z/jiPfjpidn0xrwGnlE/n1zYK\r\nE/laFXLKn3PfEFWkYADHtv5CO27QG8ZIxBLM084dihHiNmCmaH5WOCScUR14\r\nR5nwvN8NPibpcoBd0ZlPEkKzEErvE2OOLFldoOB52C0tEW5yWUMWBJNLWiML\r\n/susbm3Wm2mYsvYJTfhHLDSzT/CP5xc3SrYOy/SGpo2ARLPXelCP+wgTP0lP\r\n445+Zdk8z/T3yjIJ81UyPXX9LK6puOduhqC5XQTF/FDdtwZo5WcblTWLfvEO\r\nE20wS01+wH+UV1qlNesnOz/tnSrRXR4n9dezce5MyJY8Uzxwf9lpggFacNVB\r\nyf/iRfzw/3YTUaQeT289xeu2cfbBxqvffXc=\r\n=z5X5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.1_1670388477892_0.6435911235029401", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/sunos-x64", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "d57d9b3121b028dcee8d7ec556b514a0545872c9", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-1HsQLVnjhlscekE8H5Xj49xPvd0c74eoZEjh+OUnr+x7vCXdTVdFDgao9QM0H9zfioxJN1ZH7534LwxEaAWaIA==", "signatures": [{"sig": "MEUCIQCUaTqLDZgyWft4RoQW6UJJ9PxuYvhN7Fs6c6i+xH3h3QIgSw08ea2GCK9bsXiwg/QWx90JIjiE3VvA1e02gYy3F0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8622553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqChg/+NlDGGB9gkxsa0wltyBKdLvRxG0ox23RqZ3KFvCdYslOudBAR\r\nPwXGZ3rIPvo3nS1m8x4x3mBa0spbv5c7OiocNFfBwZSaeMfO6zwjTs/lX2cW\r\nvpPCoGjMLiuaOCCGMgMfbWTgv86/C2UOz4sJJZrL8kuH/M7l32ahKExlGcbF\r\n6H7R9Idfrp1EXhKL4Q8fvSnLZZ4FAuvnFQjAYYFuIwIOI3hhQkHCkTP3VwVL\r\nGziPVVUGZalhpWUeTZHk9GyK9jh5N0BT8YdLkp4dZJ+AUdDjccX1nEqvYJbR\r\nh4t7WJ/jy06l+VKtUwzDMw0aA0XH92fmZc5KXZxI0CV0nfE9nuwysjqcLjLI\r\nTpoqbOYhhJeeARty3AgEbQNg+On6QfJHrUIKRF9IHn3G6BBiy286ilnoOhB0\r\n+ixldM0i3noN18NLCZmsL10UeQs8gHN7yVdV+QMVuqc+bkxO9xJs0+8Tu4j/\r\nIS4EXwXZw0UhL66mBRpZCV07aWludwYDXAycPZ7JSyPgVvxu8rZ+nAuWLj0l\r\n+fpjfx7M9F1ILnVEEX6vQZ+mEPDigq7AWsPr1mBj9Gu/j74JEPmZPwHh495X\r\nsOIkhntnzWo7zYSzKKy2sy6agzM4O+g66R/iLU67pd4TD/4qWzacOXCgidqh\r\nULzqBcVgDh7sRomvqV7ERir3RSM190d/leY=\r\n=Yz5S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.2_1670482773548_0.8986918013860115", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/sunos-x64", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a838f247867380f0ae25ce1936dc5ab6f57b7734", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-SF9Kch5Ete4reovvRO6yNjMxrvlfT0F0Flm+NPoUw5Z4Q3r1d23LFTgaLwm3Cp0iGbrU/MoUI+ZqwCv5XJijCw==", "signatures": [{"sig": "MEUCIBz9hS9oVmZNm5/SqNOKu14g1eUVfogNIzd1rjg9TnpsAiEAkGskkXr2SIkjFYO6VW32l2EBZ+gnPoq1vHh0b96qKho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8626649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkUyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFNw//RVbS+uEtsrlq5eX820rKrmvdg3pWaJ/NAUxwks9LcfxG++L9\r\nOr7ak/8xbWzNQlnD8TVqSDC0kG6Ud3RufUjS9bNP5C6bctqO4JohgG4oIEIu\r\njP2ba2uoJCxZ+r+ARmZejaJTBIi0pvGVJQJPeq0UvEk/ZQkkClkcYR/zf4DL\r\nEvjQ+5hLhyYGYbPDDj9q4VUUnJfQs98eoYOK01/eU/p4hkvvTYLuJjfkjofn\r\n1C8jRgks5y609j50CIOOTyDMnwFFIagUqybTsnkWSoI9+9Slsay23LiL7hnk\r\n/9keOlxog8Ph/H6BXwNY1GNQe+Ql3n4QgaMMWdcmTYl37qaJMMP2i2oddp+Z\r\n7MiLIJZ5H7kLX6NN5AWB9T0xLKkdxrtXn8hLoeIOzgucB2w3C5mZ63s+nWdy\r\nGag28GfMsxBC5EGUZU0/sjC03VKFELrBuwGZ7hSlcnuXnTnVzr2HEvdX5qDL\r\nxU8C1DzW3IF+nyJmGx0ktoF8KLvaqRXkz/BfkHJnZ9h6U/uwu3DZderKvkTl\r\nWzzmqHNBqy6HoZkalWKpwdLUn4nHHDCyrZpwZEH8OYOW6ddLI0LNVcWtz00L\r\nooC3/HG7suQznFs0ISR3O9FsYy+gUGsiuvY5ntrApLdSTELuUONg9yCv0JCg\r\nOoAOS/3qoxhShgims6nVTX8WJ7zA4GlYW7A=\r\n=z/Ee\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.3_1670530354120_0.4416489489232609", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/sunos-x64", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "0b50e941cd44f069e9f2573321aec984244ec228", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-08SluG24GjPO3tXKk95/85n9kpyZtXCVwURR2i4myhrOfi3jspClV0xQQ0W0PYWHioJj+LejFMt41q+PG3mlAQ==", "signatures": [{"sig": "MEQCIBtJJ/V+OJMPCK5/X0B3mrDGft0lEPYgDsYoXFIiRcP3AiAOTlaL3xoToKueKrQh1PhnFI/gu4Nb/79vM4IyQcop8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8626649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAH4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNdg/+OogSdmW44pTa9DAyTGe8dLw2clcTFhp5KJ2+benSGJsazk4r\r\nUUKdsTEDq7zneHxHYB7pxEDyHUmuvopvvdi9Qg8SouVhjV6vDqE0zNXUasDo\r\nqQoISY58dNwWMZ2tAD6kCXIudSujF2A2X9s4Jdgq0eRCvKpOzLpKMxBR1lpl\r\n+LTm9/Hp3UBvdk1SQW+yvy0GOA/eIap9ILUMh7B6aOJpCUsPpwNP6DYWKNzV\r\nGpwfe+A5xTbyFT0zCJMxywOcmazadILTNB//Bad9zROrBz/0If3d1NRGanVL\r\nUS2dM6aoPvuFYLX07SR401BrkQ6snGmW+zGjhMxkJake9b6LloqqDJimAudB\r\nsMww7Bv8FOYsgFhLJF5EL+w83XGJm9hFf4FSqvnWwnddeGt6Lc/6D7eipuCK\r\nhjXCoXroqTxZb2yLwy3k/W7Prki0xaJD9USon2w08xk/XvtMXZWs+MBqHXPk\r\nkFfJepqeAxYM94s3auFJ2KTcvIqWT5xqf5jy1A4SLnn223yEezIA2lyn03Yj\r\n9VUMpL/J6VadZP6qo0lUGaCsQUZYFzBFpDr/ylPq8gk+NxG4Hw44oxjn+Tad\r\nH3M7oen5HFjnMR5XQfha7Fm/FH4ohhsWERphOv5D+Y/wdNveyS/s2K1+aesw\r\nYue2Eolz8VFxS9VyWpGdbIdwSUINhAtkiu0=\r\n=rSgT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.4_1670644216205_0.5871634705581514", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/sunos-x64", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "9f0da4e9c594bdbe92c27fe1d3d1effb8f036ad3", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-Q7+HbDnW52LLW8YIU5h0sYZ23TvaaC0vuwiIbJUa91Qr77NKNJCe8stfunN1TRZo+6OwGpM3MrdUcUVUfr5wuA==", "signatures": [{"sig": "MEQCIAECcxOrGtPTsR6pWs3aAf1YTpH12l2npQiz5c1Xl7vTAiAthy166hvol99SBZ7Mv5KFPTgq1E6wjcRLyrgtP1cRKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8638937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLqwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDNA//Q+zRwhxfzwBdMSS88Df/ys5OlF5HZXmx7a3q8iJKvI6Jiss6\r\noW1DP17mZJJ+muDzYriE/rFN2FRurNh3CwkU1jxRLlUL/wnh9LjQqWlOETTO\r\ngY5G7MS+yYkUb5DCYTXRD+iPAd6j5fyswrWyhiB3Bv8Iel6kf9Q0goFTNerW\r\nsFIze72Gsx4pG7wPPx+/D2wlnq1G72KwKpCkKc9e+M/WS1AtlAoOYd+a/9jB\r\n7YZ/BFfs+CAJFdawg/rCzQlEVgbUacOTOFBrbAyjawiGrzpqgbl5FoDMMmRA\r\n3zkLQZEwXoYAomKgyJCfCRdf1UTS/SeqhSxjZkL5t0BrDSegRivy4iNjuBXz\r\nDh0Jt9zuL8fcZygIYx0TuVks5WLzEsNz7dfnLvg0PKhRTSW2iwlT3Ks1wfR8\r\nES3bTQ5lEI7YA3R6qWtFOJ9DFdhvWgV8furxqFoVPSaSOkabvQmssXdR5MpD\r\n77THiR7D8BPBEdY4v2ZPWKRYtEQ7SJKMLpdE+g5WnpiAypGgbUoHsxjN3vbR\r\nBf+3N5vFazCkvLWh/pLbEp/GiQXnUXdRgZeO4HOhk9lDZrSWGm3lrsIQekkY\r\nLFk5sUyKI5RqLy6maefLi/hsJ3cGqhtsN+iiyffDi7Q0xMOzwfDMxCHTUqSx\r\nltE8c1zTkdS+A7FrqjKMc1fx9bk0TGYsZNY=\r\n=SnGm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.5_1670953648492_0.30630918957020814", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/sunos-x64", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "3ac4275f948653a56d52eca7483f8d683bdd14ca", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-u0hH+njKsZCz7SHRIIkqnOCWITFL+uLaXB7ro3SSztWcx7iB//Lpg/2lkPZ7sZ1lVpO0nmaHWApZIbvMTCwz1Q==", "signatures": [{"sig": "MEYCIQD8kLTVvSNYMr2NAOFWCRWKUl4NTqEK7f8A469MPdqXdQIhAL4L/XvOIG37jjKgsO5yCoW+yqgjqbi5iT4LHE/yR8Ev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8647129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV2yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsMA//QTyWsPAv6KgfV3XIe4bdrlRI96XwSqhMIrvlRi0B40zSf/F2\r\nVpJLnALgSseyXpU/GonmCTv4pDEUEfCWjM4Q+EW1eVkft8AUN/xDSBuXjSnx\r\n2zzNb/LydWfJYQg4IHOIGV2Lyyy7l2h+0QsPoMmox8hscLbA+iKKV2Ma35jP\r\n7qS5Mh+uiGu1VERpYkFAatm7TqaiR/Gdq5wUFByRmx0aihMPBXKdLi7SxvjI\r\nLoLi3xgUGqeqmfNMniVPcHpTPl9LEakplwqJjvk0Ych+8sMXyJi67EF2aXdi\r\nqV218nbeViqOZfc9B6/NEEDAdD2SpqOaGgkWML7ihXPV+lDkpLSfmpDJA7eL\r\nj8jKM4s/dmQlhaYQvubf1qnGhTL95m19CUh8OwyNRKwqxB7jzwHlwxP3+a/G\r\nud/IP0UCzQIwj5AdBzueJuafcCpqJzW/0N7B3u+oU2X08cQzjnw2RKCHxIJn\r\nHji0WTbweNzCoWn68vPrakWs6RpQyz6qYdti1g+wpTYgDIM4luh0GMdPlTFH\r\nS2EGtpxIJyTlisqWyipBnE1krULrYgmCxzXPL1661TPX+4yZp5YwBG7TOKb1\r\nLehmXwdi1o052AKzX8ngLBF6LmOA+UfInTSdkcsGnWAOfvI0Pz9AeRmpNGEX\r\nQLKC/GQpTcpV7s8e4+EVGPc9Q6/Y51fX64g=\r\n=8RXj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.6_1670995378522_0.18692877130837537", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/sunos-x64", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "11c4cd341be1de93cb5e3bf096f3b63ae1497626", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-mFWDz4RoBTzPphTCkM7Kc7Qpa0o/Z01acajR+Ai7LdfKgcP/C6jYOaKwv7nKzD0+MjOT20j7You9g4ozYy1dKQ==", "signatures": [{"sig": "MEUCIG/qHC/2yLqbg4GPYQlYU9+U+HWfbjSiWW4CB/KWWX2OAiEAuHrLBQkKB6ovPBOm8RHJV2EPmTHRL3TdJ2SjU+I7QFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8655321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosKQ/+JV0o8DeYHMtkUhAgYYGzCbPMF4Y9DgK1u/soLMJVQNgE6JLQ\r\nhqhudSwC7J6xy0QoL6TV8XpLlzYjjt/oYtxvNm0/oB6rmL6m9AYTrX2m51xd\r\nK9G9RgcCIVK98zmR1oL6A61xHxQizHhnR56D2FhYuhSPOK3kPv513WKjxk53\r\nNp4Uj2BZxXC3GVOqwM5vY9cvSj1a5zft3PJOyDmU2FJ/Jqhzh+vqUAcpqzB9\r\nLU2J0zEEPx4OwkdLiMi6HFFwFfuwiN8EWB8OZqIciQUBcKR5WrnJh9kZgMNZ\r\nqt0EfqtF0EC0/tB1+QkyOQaW+D0RnUPiVDwYyHiksWQ19iNf+IKDICweya6c\r\ndxuc4odsB9VcrHlyRkTQlvHf2D2iBfBLqaTVw3XTObfVAgajQTPJrSoecWx2\r\nBlzJcur0isNGHA4bB7SX66CWx1eJRaZZGTThSv/yA7F2WvuAN/A8DBxIIKbX\r\nYdnY1PvFWwou8GP1ROkS+lSfqpzJxxnt7IlzoAnfGkykH7GO/JrFGHKC5jxJ\r\noOsZhGbMoZbtREz4CFxXPMx3t8XZ6n5a1WQMvI3FELvTu00PnYd+JdhrAB3Q\r\noPEaKjP9/tJ1SpcrXTqXGaamohWwoNh1DNwEkxJsgEuJsW8z80gtELkNHzsu\r\nh76919+Rw1wYIIHEtpohh34sE4KwAPEPv1Q=\r\n=y4L7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.7_1671058003467_0.4812691784731056", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/sunos-x64", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "463cb5d29997216391eaeaca25ed8549dd464f8e", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-eKg0I3C5z4NTF396Yo9QByXA8DdRS7QiYPFf6JHcED0BanyLW/jX8csUy96wyGivTNrmU0mCOShbeLgzb0eX7w==", "signatures": [{"sig": "MEUCIQCidusvGWdmg4fBUU0EsAHbT9XpGO6ZWRjTCjQBy+QdRQIgaXONoQaPLzKlKEf2QZsLrwyMoGfonXDe9IKftwQ7XfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8659417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQFsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh8A//Rvzh2FuBbSya/ha15yjqoXA4yqu1gHwQwixRERq6eLmt2RKy\r\n6XN+aNMBk0VbxgoxC8CI1KB00fs/FuPWWekG8UVO95Oea1ec2eMVEHcIXsJQ\r\n9IYszmkHXMbo9Tw4Wt3fDXzQUWWi8Wc1ICUwsZg/slGbrHjRyQv0u4BQTdyq\r\nuCdUZAI+mYdz0ntrxTEUMAmEA7Oy3zXl0ouNX7/MmVzxc/N6UrZTqdyoH9KB\r\nSEXOvSRR2d2hgRVghZ6gLZXn/pWLswFwii0LOIxo1Zo0axdO44KQSel6m/F3\r\noHB7qhdXLsqPN8s8CHp1rj8ig2CkxY7EsNAZ/kOFupx83Y8BVUVuqhy7qiRd\r\nvAHrfD2tsguix8L9mZM6i+0qmBIU/6aN/u6Xu6rQg5evlrBDZF5ImhS6XqGG\r\nnDatJXYuorvu6oRGbnUN6HkVbhVB6j0wLaDpiJ+Gv1xZyZzw282BP9Sk4eR1\r\nnXHwz4XMI+BIV4nEh+ZsJwsJhKSsAD5Scy1G41Ickr7dYJjpD1DAgkhDaPee\r\n7R3feppYYuVXQuNQ96GrWp2o9mFvSIhsNMPboWVMsrJPpLZun/XurP4lyphU\r\n6JCLSGividWb1kDZckzHOMi5rpm13wCgqHuIcDZ8ONH3xtvr1mhIMHDfg7H2\r\n47xySxJfFB5f8LZ8Rr/W9D4HPCZjXGG30uQ=\r\n=J8Hp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.8_1671233900530_0.8676032754670526", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/sunos-x64", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c132603a19ef79c0d7bd95afb09f41618ea8dda2", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-OetwTSsv6mIDLqN7I7I2oX9MmHGwG+AP+wKIHvq+6sIHwcPPJqRx+DJB55jy9JG13CWcdcQno/7V5MTJ5a0xfQ==", "signatures": [{"sig": "MEUCIBsug0Dwirm2BP7x++jDNBeqrhW2f2wEUNMOLHDoJHORAiEAv5omIrjOhWSdjtCdo70jaMwE928iXevO0sthZaX5J4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8667609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/FRAAo/+ITXPXi9ATkRnbGVv9ie+chblJm2zkMKTsa/LhAcStbhxG\r\n5jAA/Pb4+lm/+VQo+1utrnS5uFeafzfGnQIltNZBTHUCXLpFR2oEEvKd7ikR\r\nxVbbB25e3W/+iXWoFg6RLclDVfsNPC7vSihkmeG7+vt/ZGbkUjXiLpgcjGRI\r\nPSKVAkTDrrUW2iWs7XnfmT/QKyIe9SJLd34OLQV5M1rV1Sx1IqkqMpupyTsP\r\nh2ntNjZLA34n5H9NhpyYlZXofgL6utJKXKrzXjbJmq+L+gObLyx6keN+cyXK\r\nlLwdL0yg5JOZr/5owsOWfbOQm+hK5B6WDLtnEPG2CwF1xO01QXPD20X818Hd\r\n00ALwpN0Q6iGIslm7v/6TAjf0XYXOS8h8bsH73ZrfBXlKlm6FBmQ5A5Yg39d\r\nbfpallmJw+LRM7nvVTPmAEWIslLoqnR02gZp68iXqQ9EICDSJ/s4O+XpZ7m/\r\nrIcRidEuVJM+XSLxwdKqSt3qxWH0ODnd5Jd3CVwivQEMasxtU3hq3bfZ46B1\r\n4kwxwLvSsZPDg5REDpsJmOgduoO/8wbWZRmJlA7v4TyKmpEMweiaAMpIaum6\r\n3dNp84wf1qB879onLOov3iajDB4xTNkvbUPrHxvc+MsrzsgUoLcJOaDgTYxr\r\nnyqLWGZMeR7Ay2aAh5BaQ9+6mpTmmah479Y=\r\n=Ae0K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.9_1671337867744_0.6074611891608739", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/sunos-x64", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "31e5e4b814ef43d300e26511e486a4716a390d5f", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-z+q0xZ+et/7etz7WoMyXTHZ1rB8PMSNp/FOqURLJLOPb3GWJ2aj4oCqFCjPwEbW1rsT7JPpxeH/DwGAWk/I1Bg==", "signatures": [{"sig": "MEYCIQCCIRf9TWf59NgCP6u1z7Pn/3QKvLOOOhQWoCcluOCZ5gIhALlDMCMzQCov7X762grREmnEIBI41jDtCjiKTJFsky/o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8675802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZlw//Y6zCHgqVol1mHXmQnGirWqErEykvbGa92Ydnkq2RtadEB4cF\r\nRNCtBAW5/VAEscL/GxtJaZAPp9ZRqSUWnOr6xirawyZT2CnmNnm68L+ACo8C\r\nAz1NmrIw09kJSfrGDnQq0+l8g5JBxUk37kCIs8zpYxqsQhX9i6EYOqVGOrE+\r\n+IidfE7bH37gIg0hlxUJlgXrnTVJl371R6f/+IMCoOZltnqF5xl0FkkUlKGc\r\nxFIMv9KMYRlmrespeJvXjYq7iZ6wrr02/wFQALUpr+oMmdFHjqpHOnMLIRD7\r\ndMz1NTgSkxj2Mo4p8xcQBNcqcKohkZkZFMWIVhIboMVQ4Ua2+MwCU5eERF77\r\nQdBNq+Tsd368qe/PAEEXHgEs49qN2blPEQG+s1kc/OYxuFysCZgU7rfzcjUE\r\n7wE/Rc/7jJ/e6+veo5tc2H7erEd8Urpg7q1lTXcajcPP7zkm+2EPokg6o4v6\r\nJjVtS8i3qx8vpbNZ/lz5Ra044mfgNMz4EStNU1vT+xX6zKciS5lCx0wvMk02\r\nwGbDQlg+s+J9xPF+7fE4GZb0lFE/N2N7aiiI92KL+i3eL8U90n2OKqqKbJZd\r\n3ZCnLBwc1w0UUJqlAWQM2HDgPEDclyrIg6a1tTQ/R2O1Q4lTdk5jAx//KhmR\r\n3yow8bpUUR3dh3gV2LBqr076/OzLBK2feJE=\r\n=jbQR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.10_1671492375941_0.9839357950903886", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/sunos-x64", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "9c83b5335ad9146d0f424ed75b2615b6e414cbd7", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-shxBLdNJecr7KxuyZQ19idBU8x7Mq7m+N5Fj8ROWMWQbDdjSjlBPxz7EZJIxSh7FUgSMKl7qSCCVaczXrta4MQ==", "signatures": [{"sig": "MEQCICizZlMX2hkTT8ZPr5MnYkVck+KVJR2fXMTtmRbWY/bjAiBwST6JR/GEnKvObzetsUgSh7JLU8UlgtW97nRSz9LW4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8675802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkyoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpePw//f28wy1E6Zt0Kiop84repdBR6Y6AAwk+i2beaFppFLfiXJ1lg\r\nxkEqPzwLUSgEf5BzVg0JvXAsn3xHzct3973S4dnid6uNT+nGo5ILtoD401xy\r\n0fv4oxWSbBZXKRq3WiLd+HcANOGlQvoyjqtHfWh5fBLuoLCWf0HhtxcZXt76\r\noXg726GKObY5+zTn0UKVwJ7F/HedD2+iDSUSZbd61OvEwsIGmTVs4uiovAek\r\nEJ0S1eGLpGyD2l9qZhFec0/tBsj8gkwnnFeEpG1vebuRbxFi4PFzjJ/J4qnv\r\nfpZ0/27yFTol7cR/yf2hpc65hZM685OzHlIGN5HkoGbZ8BMdT/82aWaHH/Ed\r\n/HTPQYafso/QVVWdLzmtGqHT0uALnmFz5lFoTaPGtkebZjkmgQ92OBy5JV5r\r\nl9yuKwBPdVgmsJqB/HXgQWlTF5X74gDZ2gVUSNN/Fak+5oYSVxzbr7bObkdw\r\nOLXyjDtI+7xfIjeOaGjHlAYwPVqDzgf7QdWasOvogtTMprCAj8LHQRIneH7R\r\nQlJ/hOefrmf3Ujp5KIANRaVqAh3Cn1RbMB535fJvYFaHFPaMwlyFtMCXXSdg\r\nORK9uhY0VpIguu4E5ilbaI6aRUOxZlG+RDigOZWN+CUAmtdt+2bN6qyp7ULh\r\nKFUtDQCrmuquRIyF6DD5UVatagsBGnu9rqI=\r\n=QcK3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.11_1672105128216_0.7042413835687371", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/sunos-x64", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "3ee120008cc759d604825dd25501152071ef30f0", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-+wr1tkt1RERi+Zi/iQtkzmMH4nS8+7UIRxjcyRz7lur84wCkAITT50Olq/HiT4JN2X2bjtlOV6vt7ptW5Gw60Q==", "signatures": [{"sig": "MEUCIQD8HvRPUmT6dYda8WCxGu9aS0QCH7aTzOmeVXxY6dOZdwIgPq4Y1XL/Lr1wegh+tdvvqykUMuttN4iQqdVSQ7gNfuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8675802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6QwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM7w//cAoSWNdKx3OsqrCWCLcanoqVn8/D7ia0NUlQ4yvIsYYFz14g\r\nt6qJWQt0IM4C4XeR9lkbpSXtjQBuHELToq4Ev3jl+HZgTCORpuai9I+13K5w\r\nS4gilK8wTbOiFxmcF5QWOpMG+tQOQKKUG7fGzUyrDly3BdosB3fYDEqv8Mdd\r\nLWUW98djW8ivChae0NWBI6vfcTSSgASBP5cMnenuEPFTqjWiO9XyzPBwxcuu\r\nikD3Lc8oi64DuBmdJA0AZDZkaq3/xA1SJWLKyzEPr1wA2+JXsdMhqZqchmtr\r\nNG3CGE7Nbx1IEwCNkuDOgN98Oo6r8WHheFgttkjAnI+K5HQi9ACtFt9dYfjV\r\nlbEAyhbB9nyznG/BHxRj0rrP0aN3k2Y7Tp3p9T2Uk8u+mWjL7kR6j/8Zkstc\r\ntM0UlqfxZGX+x5TdNe/YPKxrR14FG+yk7WJkf31X0PTB4ob6bk7ZBBnrYHfp\r\nE0ObTw90y00cQmtrYVwns/IFKmcKnSaPmVbVlGOhEUYgPu1lyMQGXGO11B5E\r\nrhfETgj69xCQ2INf3K/lXRYH6xBf/An5UUsrY7jXTrfUDBb+WqohMBxD2/OM\r\nR7E7Hsc0foN2TpBF3x75fcqLQsC56naQSzr22tFKPNAKEUxyJHyzEGzbk8w4\r\nx+ecwDiZNrMo1mkhqYps6Gq3/ltvD5PFkZQ=\r\n=kKNr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.12_1672193072140_0.11696301547579302", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/sunos-x64", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "592caacab6b2c7669cd869b51f66dc354da03fc2", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-8wwk6f9XGnhrF94/DBdFM4Xm1JeCyGTCj67r516VS9yvBVQf3Rar54L+XPVDs/oZOokwH+XsktrgkuTMAmjntg==", "signatures": [{"sig": "MEUCIQC40/3eR2xu4HhOwPqqE2kp6gUfjV8AKNkHV0hAMp4+6QIgPcMT/JMzge5XqeO2ziHCyLjErZcogCZs2sMpQ6odosE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8679898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2rBAAjBw7BDUUFxX5HRgyRdxkcZeCncptB9Q+yFXL3tWH51aXOELr\r\nf7jxRcKnRV2MNHC9vctON2+t/nCaKxn6qwfP7w9T5xgqAyXdSWH1ceiZ0sKI\r\n21w2uFrpyF5tvanNnfNLr98yz1DOxIco9J13nAG/oVrxn9Fu6vB9Mes5bevt\r\neXkJ1d3mjElSvU/xFTWhQ19LUO/czeMOoCAo8xGBTuFb+SZFCO/zgDmCZNA0\r\nW5tBE5lHIU55Mm7FBZrNer8Ao7FZaIGoomPmwJGcaAUMmdC8OSfNxhmU8bdO\r\nGyuoRtXOrOAD/XtCRIlN2p0nQr8zHS2rbn0i+xm5GfYrrYfnixzeesrgzmCN\r\nrrSsfa3rdBQVFp1RZsakbhr+CukHguCl8eaoYGyzicldOBNdBnKTkbwE6psd\r\njAspklgf/D5EDgWWrrnNxeZVZ1d/8/1RVQqud/4PMFPCHlLAvpEEQaoQGH8r\r\n9EsofPF9yuuJgIPlaRDzubNjzUOHe98A4z4QuoiOqCCXy2IWi6/4SjoaI8Dg\r\nQy6RPyVD/loay2hHXujhhZGmUAsdG4dRSb8CUalAiUw1rHoLBy3rKOF/90PX\r\nQDUXnyYkEnX2DKBZmFdT55CZXUmYakX5xtyX8JXRxl5MapuUgHS0ofvm0t9U\r\nlvsbe7mruS4efgMDrnXKEbvfXsUqK7QJfSw=\r\n=nr9M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.13_1672700228334_0.9806139076530236", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/sunos-x64", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "41e046bb0849ae59702a5cfa8be300431a61ee3a", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-2iI7D34uTbDn/TaSiUbEHz+fUa8KbN90vX5yYqo12QGpu6T8Jl+kxODsWuMCwoTVlqUpwfPV22nBbFPME9OPtw==", "signatures": [{"sig": "MEUCIQCgcrFu7J8iF9A651gNLNhWu03x26gsc0YCgQNSatF+mgIgR+NZERsfeWB+JiDbXKNHRQIA41uZpcK/6HrxxjDotds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8720858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd26ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP/w/8DFPKlJcXq6OhMTfJam2j0vjp2qS2O4J0yRsu5ToCiWXWbIzr\r\n6UDJE36zXvOWbM18Z5uhMRZmCkrqUqvl/EptHrsQwYGwQwTy5DEL6XcxfXSb\r\nM1e9pLt90cFIhcKU9peH6zGZmLexbNgRCAJXGzr/dNo3D7pc+diS2E1DMuw+\r\nTzBLJnNkgJCX0LwBeEiEOJSafCiyOV3MScVCScMNNXu4dzzm/TwmKpsTK28J\r\nRr3oposZ+/l4SyYFih1vinHlmD5RRKpDFxGbAgrYh0ZGgaKYT9SbWIuUnkZw\r\n+6zIuSZ6Kho4Jq3m+XRg2dM+8Mx4eqyNAEPpABLxl0pTAgMgLnfu/uR0wosX\r\n2h741bbZJYWConXsv8SotLeoSFDIRSXGImaAO+m7tJW7pOQry9xBeuiJ2Q5f\r\n5iwva2ZogZWH8lcW4eLECv25pUWCX7c47O72JrrqyccBl89ovMeNuWfDGvMa\r\n3GySrmKkRbXYeHA08ZlGo01yCMDIv5flu+a3hn6f4QgPz/9Ht+FdtRSp1DSG\r\nK7Lty/n+NFtkSMvryBDGP9YCeMdag1d0g97er6j8qZJjzisIJOJwQKm20Ceu\r\nvFTMewy2EX/OJvz9A0cBP6wMOXtWH4NWOAXBDUwrAMJT8qUPkKXoYho2Tksa\r\nfJ/m49P55uC8n1GVelvcpMHW7KiKscLLBDs=\r\n=jHy7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.14_1672863162608_0.4919999674843101", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/sunos-x64", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "91174f7058dfc6cfafdf2251330f6767506db7a7", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-8PNvBC+O8X5EnyIGqE8St2bOjjrXMR17NOLenIrzolvwWnJXvwPo0tE/ahOeiAJmTOS/eAcN8b4LAZcn17Uj7w==", "signatures": [{"sig": "MEUCIQDHicT6xpEzG4OQetqX+eiLHuVhJsB1qluyxJ0aZ5o6QwIgHO3D7WpvIX7rxyYSbXVjxf0xYyLTNqSPhhwhif7r7PM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8720858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPKPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrwrw//aNlVP8v+jhXmnfIIMFkX7Qz2K/yNJP5INuPFmj7UJHyBZYsk\r\nX3gy0QljQg4FNddJUeuV0DhxyNxkbErGSQ4eXmJ/9/RAbPxsTTsETkmnhqDF\r\ntaaWonO0ad1y84cmpbxZgu9Tze1gQnOITY8ZBzwT5AKrm51ShLczN0UtcGzW\r\n7ox8KzRd55t7yrWgeEWD6IRm+iA8/TKz2Hxcb6Ov6J/D5+8LNngC6BmzaCG2\r\nttKYcF1dssuohVNs8JIUpvyIZUFU9rZ+TljuMSWejzBqn1S6SeLeyHWmhjYJ\r\ndg+sj+JfF7PyDs6GfTLCa/5DHbd+Geq8XQ8vvXUEfDC3Zr7d784YgqokekSo\r\njWDIPQpftculse5M1cH6LHvzzP4NhGtv+z8+D66rmyHdXSoCBvgVgG9n7Pg5\r\nWJwFI76PkLZrCV0I13mrbBM3yALCCYA6G5Edt9D8KO1d7PEwYYXwn66BYWqy\r\ni2NfIzWLcuw0qw5bItpLLT7JKzQHX22hqbuJEiGQCF84iwcNXlgZBOT2i/Ii\r\ntpLEOGpDtla69AyCxYuyaWA1sYM5ImLJnLjwCkoaG6gkqFPvTjLS4GS2fokc\r\nvejpR+LB11YYZCMOo17GgW2VHl36QnRFJxGY8kkgRCguWwBzbyXwJqe0SEs0\r\ndDKqs2VjsX7THoVcUCBBA3mfOwtjVz8GJR8=\r\n=af/k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.15_1673065102988_0.06437296482700416", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/sunos-x64", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "996bcd2603cd345733c3aa5f52bfd5b8fa7d1d36", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-nHfbEym0IObXPhtX6Va3H5GaKBty2kdhlAhKmyCj9u255ktAj0b1YACUs9j5H88NRn9cJCthD1Ik/k9wn8YKVg==", "signatures": [{"sig": "MEUCIQCp31hT5czhZJfbpGYRGgedNGueZQGTx76d3vnH/nD+0QIgEM2RTxE2yQx7s73ZZVzS0TyxEbZDDoaKQoxCejPb9Rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8720858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0cZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxqA/9FAI0k/keV0BgjBqTST8DyMz65ffw9BUBoi0FxApyQDoqKMSI\r\nH+rlJwKIEXWNZm0P5jZ09lKnk1Ha3DmdEM9mloejmKLmxlcK+o+STtZHRbdT\r\nva7RDEyjbeOREs+HP/PAGgkXS8iCNd5kYF+ZisnvPGG8Db7SlcuCAhaGK27V\r\n7uIrgsIMr+qCU1KpivNY7E//LtuAKgSsbMwh84ORUfignRo9mR2FLznEjQXU\r\nhZGokdRs8tVo998rqo8bp6Mt/1PNG3Eopj/uTYA6CnAY7M+qNL2YgPTL20i4\r\nBfNftIftD+on2vo1jOxnlHXNhygxeZ7uqSpFmmpwYgD5B+JAFBKqYPGxk3Cc\r\n59Aqt3Wt76wh18SLUi/RiU/gONp3RihjKJYI8bLuOV7c9f2HqLyRFgpTj5V2\r\nObxMWo67ATfg62R5xwJAUYmuAB38rWMcpXJPcPYqwIpzi64Gibbplc8dhppB\r\nSIRL60oLgv7ZFcW3Ptb2CArhD7gioXQwjyx8PAjbASUVUmpUAE5e3kcEYGq0\r\nHv1hzZRlH9SHceZmzMHpyVpSZcX8gtZdowccLvwc4gr3bUnuImYYh1Yy/rJ2\r\nWgK9F45ijBSruwwOOcXQxzdK5FmN4l+JieHflZgJ0iYwKD6t0Jq+H2IvkcC3\r\nRZfkc4V5Jz7yumx3Nr+u0L7WrjojB2kSvq4=\r\n=u6Fw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.16_1673217817485_0.8782161336363754", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/sunos-x64", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/sunos-x64@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "abc60e7c4abf8b89fb7a4fe69a1484132238022c", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-xtVUiev38tN0R3g8VhRfN7Zl42YCJvyBhRKw1RJjwE1d2emWTVToPLNEQj/5Qxc6lVFATDiy6LjVHYhIPrLxzw==", "signatures": [{"sig": "MEUCIQCmp9gO8/OGbBoF+O8UGZxfcBlaCjH3qAcOfv2Yfa4E3wIgWW7Xb/MRErxeI5T0Ja37KaNHaOdGVFvCwPKmiBPjuLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8737242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqYA/7BbSkD0lNCGjCCHx+EomHWI1hTNu0QsWuwsD6FveVGpGOsOYr\r\n5VSFL5MlRe5kaAYAi1zcDdPQNGogl09PqEd2CL26kDoyV1wrfeaA5C0RKoKz\r\n0blvKZYiaW3CP7AexddOZUPqH0Jypypfez7e+l9E2XBtzQ51I/ClY3U8XzUB\r\nFGaLuIaFb6px2g+Y60lcChwc3hXi53P9pyyZhfj/uz7gmKcqkXYAWYdbpLTi\r\nEG1eKyo7Nk3nrypgpVZDqmV2rJxSoYcdDx6lvKUdXGrCDAJ9sUujLtN+oNJS\r\no4cJKXDQsD76GmJyjcu50Oh9U6XTcFhh0/VzhYWwMUgirNZfypoXcqCAxLKl\r\n5h1Lkrv8MiQkKWr1Co9/AsrjR195HU2cZ0rt2BlX23MfAHXTHrgLcf4u4YHY\r\nI4nOgtm+wtoaWGZWz13UilPpoRnlTeTpj+5Mz4RNKreTPBA+6danQLdG1krA\r\nIJBSzuFoxNMLhU3Mpf0vATfqNPrKCaqbpCAzPh/SI+sAsfj3iDbrYn3iCya1\r\ngIm5lybRiCuL42E4Nyv25TTjpA/cKX4GquRG+QGt9rewAMpzA9lJk+KUTKgA\r\nSkIwhZS4x1lqcFhamMr9CG+IueP6DnBmY8VQUOXTRobWkiLRCFSjmZavIKB4\r\nHd3juUa6lCI6zNHEzmZkKKCvsaKSDqAQnGw=\r\n=gIJn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.16.17_1673474263875_0.20341875005581023", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/sunos-x64", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "4a77dbf1691ce2fd9aba69f58248d46f3e28f98b", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-K64Wqw57j8KrwjR3QjsuzN/qDGK6Cno6QYtIlWAmGab5iYPBZCWz7HFtF2a86/130LmUsdXqOID7J0SmjjRFIQ==", "signatures": [{"sig": "MEUCIQCUQNbgvQwmOFA14kiqvXsTu1f9+AK+V3AlBMNJK6CRagIgB1lKVDDzzTN4nh9YEK8l6V6m9NGEijzvwhC2KuiUW8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8987097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIUhAAiUsikyFpY3l8cDvLlBKa7YHOqnMM2wCtrw9V86kFvTNxePug\r\np+BFY+Ce9PiFvskQqtu0wyLEJhY1Od4wPPt7gssbq8g255poA59lnw+uDCR9\r\nqsogfnwqld+RLFvrfJRfuCzq2tdvL0YN2HObaq/bMya75B+piqbuKlwCjHe4\r\nfRtqaXe40aNPa4n7cH/ce33T2mmpuQkGqWXfoNNjD/UIRupAreC9JNsZtka8\r\nafV+//WuZ+7pQfw4UM0BOwSklJXPx59d25NvjoZLuW81ykeb5FgZVNsiZTG4\r\nj2vpdQLgIcZ2dGFtB05kmH6I9VeOV/Q0bkKqyj23IEx2zAzHpGCkY9o8ktnM\r\noDQX2rQr0rVEEPNUAyYFemaBHqldmwEcsqbC0rn2s4Mw2YnuTQkAbFGUgvmD\r\n7wExCbDU7R5wGYh7bGw+E/sM0edFh+bAEt6sGom40IcL88K5W083JRpAgxum\r\nMPHi6eXiCso9WryB3bFRi6bjfs+OEzB42DmyaAR207sEbPk6ObG1HROE4MCZ\r\nXjhlm50WsWjM4FXgIPvFQ/sys2otV1HFA282v7wZQB/ipg1iNtZq3DQKqOuW\r\nKQeDJvw6V6saHclYUCybN9/WgYlUAgFMbRF1blHyhp3/WkJxGzdx+CffMQ+f\r\nxWhnbGQPXd/QNRqiRGX0B7lOGqfzZA+G2q0=\r\n=QA+4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.0_1673670805083_0.4728561400202107", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/sunos-x64", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c9fa2ff0ff368092c82c6e5dc133882b43f96568", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-36tkLA020bZJ1pjr3lflHe0lUwJ6fTmp+MabTFW2RZ5+G3c3QudELm4TvxN/HyJqJgWAFE3/3aT7oeUUiak1rw==", "signatures": [{"sig": "MEYCIQDPbP6DoL281cp1S1TaoxFuQLhYmS6QIfvweya3S+hFxQIhAPX2t1iXh+q+Qpzj6UACfwXIm4grUblPOGHvOagJdfOz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8995289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZHiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTPhAAi3EPu8byVWolLuirNvLJl+EiC5v0Z63+mnvQr5EYv2/2ljop\r\nnrJLYXNvJDumNakAvsM42X5CbhmoRMJ+KqWcn/ym78bFDgwy0DSTqrMVNGQP\r\nCO1mb3gZfSa1cVBwzrtm0PAkOsRYT6fxSWIYRh052M1rkO1DZDSVTqgzjPjC\r\nf0r6bAMdxeISoDVJ5sLq1Vj5XciuqmxhYEHN/N7cPTPRdXw9FZbA96qQZBs+\r\nA3/mG3aSOiiUDMsO6EFz17D/U9i9hqLXCYLPWNolNwYl6kDw/EKpI9rqmA36\r\nryY8s5ZiO8MaxAKijivpUSGFfHvy3517QMpgbtpyUxS3hY6G5JwgsKMI5ajT\r\nRWY2FkDD7UyF9c2LyNrkN/EjcLyiC9ZA1E3JeeJJ6Q5CTAX80Tm4by3qTJZe\r\n5aCr+X65Y4Q+mb0Zvc4mO4m0bLelTkz6CfKE5uBVPgveBH8OI9kelNNoehep\r\n8tu+2X1U7Y/79VlPbXafrfKcgS8wS34YfNKULYhjlKwix+fnc+ugsief7rHi\r\n516nQBuPAFntJbEcjsHILtrJCpoEIlNonw7kR/KcctndXpOT1pijUSAlMzr2\r\nluETHE1wjiv0ISJJv+qlP+NtcagsuvZ1zWW9Kj2PEklAHC6mSsOHxKB85SLR\r\nBrZVeqYf+c650zK0xe6t1QYX8D9X38Nmb/E=\r\n=7RB4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.1_1673892322473_0.35637097109118643", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/sunos-x64", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c45c5b6fa406af451e3ebe2ba610bfaad106d20b", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-WAyg4dBTUsAPJ9cRnuQ23cwJWYRhP4e4y0M/l2+EpRjWW+g1MNAXKQQNNhRQ71zc8UixRVrqj+43ReHeZC8mJQ==", "signatures": [{"sig": "MEQCIBPu55Zt9ZrlNkfIrKWLVbjTnAYf71S2vJtud7LiO05gAiBmREtO1jyeVQhI4BTLuLIsEPKjZKRtHVQpOrLdjaEVCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8999385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkKiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHrg//QwdV5tzMWYNWpzvDwKF7pM5VfRU0lA9pc1fz2aHwhDSA7vxs\r\nLEqgHZoo07ZlEf+57v6DB/fLYibZgDCMvmz5xwglXL3bGodVvQHKmhBLk7gX\r\n+uVDDYsHuYG3xHBeKNe9FXq+ruebHmYoZzN2AJw2BzNeFGFi5Ag+4Hrrf/br\r\n+9gGt/VR45XfCPVWSiBovvaHNgY6P1Uc80Sn1hM5IaplbM1J6gagO7x14sO9\r\nQ81fgB3NiTbKZk4cUQw4jviD3Sdi9B+UUFLgaZ05CMC60FoMbTdoDeZXje3x\r\nzG6WrCZYrixHcx578QjQbnwLuY7/SvFcGybNnFWJLOQP2oS1FdSBCZtVdsqK\r\nZjJR8szfGMKsX2qEYtet/FlJatQYPQfn16VgtyiIKSWltpLF7yCxn1QH9NPS\r\nnZ+fSJOPRtCKnSLRM+Xl5N4Kcy+M+UMeXhEjOey9fg8KfOtEr5AQqhNpxSKP\r\nqiJguXkDks4VsWFM3aIJj4IB6t8t/A0/ETMvKa0Vi6lzkrnLrwsQ2ibYshOi\r\nxFfPI6v0hhOmVJtUbeyDLb9pbDkJ92ahxUTGBs/+kvmippOet67/XZIdHs/y\r\nsC3zBfiiNLHo5sszC4iklcWBCasuC6+wC0p6V70DizoARGVZrlwYdR1w/oC8\r\nq+r8YT1vtDwlvMOgb3Bfl+GlHqSwT2G1y4g=\r\n=dR1V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.2_1673937570163_0.33496916152828526", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/sunos-x64", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "f1385b092000c662d360775f3fad80943d2169c4", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-RxmhKLbTCDAY2xOfrww6ieIZkZF+KBqG7S2Ako2SljKXRFi+0863PspK74QQ7JpmWwncChY25JTJSbVBYGQk2Q==", "signatures": [{"sig": "MEYCIQCFSxcc8C4Jh9XwHSn1JH9w/3IhVsoIBTbpQaAoEUJz7gIhAIez/usRQXNw/y6hNgjqpBj0kMjuED9j6rQaEHUR4Vsu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8999385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXIg//fB8CAd4wcrw/nxKwtItY+NbzVDzaK5UK2r9nmxLx+2wQTI6M\r\nmXi9+IYo0qREnZL82tO/M6MGr67PsXqj/nZtE5an5gDYLVnezLKtJfBAfoXP\r\nSDOHemSeuo9rEjcvhAFa+jyMZRJcgjeOYUXrB04c5ZQNB3cSD9lBR7HpXcQw\r\nJOwLh3H1QZpWyGb04x1K3hYtF56G6ipijKAJ59ea7kVoDTCylT+jamiPOUmg\r\nyWuRXaDaRGJEs2wZDnZ4VKINVzFk1ch1SedaUpSuYJVNsx7+kgeIPQjUd5at\r\nVB1nlhpWap/fx+1Zi5i96a8a9Y2+3OjNBDplrs08f3qF9eO/DpEMokxKPZeI\r\nPwf5ejRuDNHtLSzUxqnRiKf7Lhy81wDS8bkqIixC3SuaoZVpjgmIug738/2h\r\nWSaYIf3D+gQrX+U+qixAlCcHFzuQTUurhP/PKnPTzrga4XsM+aXbZihhVCyL\r\nerHCNIUFy4ACm5tU+LsZja57uhojjNeI2EE4iDlH2Y/Mjgr38CImz9aF3YCd\r\nZrLLkQq6UCB4bswNXU7oj50rpBop4TSrm+Iea+JYdD9R+dZ6YEQQfuK4Xa39\r\nWMG+bucbrVPnvLoRmDtRCqZs2a29mXCgIJ1anToguF1HqhKB1uvhRQAHp2F4\r\nduBbHjbZro9Fbcrc5v1vE2BhLhz4IpmNfks=\r\n=eUQU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.3_1674069250712_0.10066492773003155", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/sunos-x64", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "4c6d2290f8bf39ab9284f5a1b9a2210858e2d6e6", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-B3Z7s8QZQW9tKGleMRXvVmwwLPAUoDCHs4WZ2ElVMWiortLJFowU1NjAhXOKjDgC7o9ByeVcwyOlJ+F2r6ZgmQ==", "signatures": [{"sig": "MEUCIQCtku0gRdvEb7H0xvxsd/KyCvNDwF6uvb2C0iDsyzOWBwIgB4zLuVPQNjjhr6c5h49mmxuZYXO5v5Gce2myc/7Cl+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9007577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0xQ/+LN+CtJThOSILNjyrIvz6zYRenqeJhO0vWZchA67KnZKdbXH8\r\nGviR/bln8d/MeRmOVDuo6tvS68niMkchcxYXQRABl8H8Gk21e1uO6SWNCYmh\r\nOP0EA4DrVeEqMVENya9uOJsDRo6pyCV4CuaitgCvU8MCZaJUuBwHzqGwXkvx\r\nW1my3AAiRs09mmD1jiNKsxoLYDcd6MUfxs5iqJHE6CQ28s3HamcmXxZo8CM0\r\nWpGaez1NDKPFxtWuH0jm8XVNcRd9BQlF0GA4Sy1omAffuYGhOGMLc6Tm9tkp\r\n0IGPpivcGt6/GM6Sxn5CtJPyHNFlWGsnx15NGIHBq882km615CrjlubYzJy/\r\nTzruXQUcH6//P09COT0BlGP7E3wjCPNgsdi707ftWTGs+gkEtee8M0AuFuqr\r\n8In5KKnI79sObIPavtsDQAd9InptOV/Wvej1eO8szjM+e/3hKu9+8kITl3k3\r\nCZmVSNfmImQNczLVFLWMy+eOuU/YyZzBpn+CXYrdo9qxazSidv+Ibxf/YzWs\r\ndCCNwtQX7rArrBw2IIsYlsIwsqGnn7HH4hEohHO7f9P/09n2vL1t3omZXrvn\r\nYqnnwtgyCdMYgJQ7gybUuOt6M23NXhPSORqFPyhNiKY6eMn25aZWldVFnT79\r\n0vytjDSJof88QHHYESkIjz+raCTGXTzudJg=\r\n=Rq2d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.4_1674368006906_0.5397854286538342", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/sunos-x64", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "b83c080a2147662599a5d18b2ff47f07c93e03a0", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-ItxPaJ3MBLtI4nK+mALLEoUs6amxsx+J1ibnfcYMkqaCqHST1AkF4aENpBehty3czqw64r/XqL+W9WqU6kc2Qw==", "signatures": [{"sig": "MEQCIHfTSd1TskpJBdHJj+Q8hBVy25LIjiQJ9lwL8Cppc0wMAiBa7XKT+M/qTJ6kaKygcAT2v5k6W8UDrnJGcgogLT8gxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9011673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsqQ/9FtVJ0m86zRn5Xt+MTnLOkV+VbFJwD6hpAhqt+ERa7hZ+mtcE\r\nAb3hC5kNJN0jVwcEjqcFdhJPvVtozvCDlL4+NAQgWihfpzI+rWYuWkgHT5/3\r\n8hl5vX2hno+R53PW5v7eB8OyaCIz+9k/IcyBzhDynb+4zCUTUKnrCTCsJmga\r\nU8CGNVERGozV2kmS3oKkAICF7YDMcnNj3MQ3Nyp474vXPCdkPjkNYhrH+Qxb\r\nEPCk7CG6iGWBe2a7DC2PFmEL3TU0254ggbaJf2DsssVm6PjXijL8Tix0MtlQ\r\nI7x0z/lrAOpKgcrZVhKlvEc/HaLoaPHjzQbynB4hgc3Yrb+YX9tJTb/2q0K+\r\nYZ7CbyVy6xCTle7ZUx+vWFyRvRuGcWyHbHBhEgVIP/5utA6ZYFfkmbezaZD3\r\nWjaC/ZoTXwkw/MDmYVfJVKNnvw9jUKDn7/JeTaNhPyqFKivzUnRJ2bljKQvy\r\n7m4+G4aMnUOpViRORH1iipAZGLZ8S8Uu1zL4/366goMm+M9f2BXu/bXNdTZx\r\nXtCazLd7RDi//tYK7+vere0NVto65ME9qo47P0SwkYGGCpbidv47cKypYdT+\r\n8Bn/jJx1P1s0eluscTjBYElmpYIAdWo/4PQJFifXrNDFauZ6OwI9T0MND9FF\r\nWJEAjprHTPIJZNYKn0SCHUPNTb7a7T8oftc=\r\n=LTWl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.5_1674837450807_0.8997450728948964", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/sunos-x64", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a6838e246079b24d962b9dcb8d208a3785210a73", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-gnMnMPg5pfMkZvhHee21KbKdc6W3GR8/JuE0Da1kjwpK6oiFU3nqfHuVPgUX2rsOx9N2SadSQTIYV1CIjYG+xw==", "signatures": [{"sig": "MEQCICrGnronP9SaQ9XC7MFKtkL0F0Xd4X/XV5+9a+biedXuAiBzFffXjuyQqYK++TcGcldEXak+d5s/WU79FI15fLc4rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TIuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0pBAAoJGhywb30HVnV4zEZfhnDdmNHEzXTgnulfzvIgt3ZJ2g2JJ2\r\nqNeUVHJNcu9zpwNqJHvffTXqlWhfqHRRDGDYQDkc3/tS5t2pUexudJTob9pG\r\nVpTBopybCeYhiarOllODfvKHrVBgNQ8pXzKXF0+NN+PYMLUqZt+1FVc/TC4o\r\nBN6Wy2kdYf2fMJV0Y2Dx/6vtJ9JfTA7DDG0R7AHGxoULY1Jh7RUi1UvUjrM3\r\n+xEPOF3+efgNYPLylS/LrfcKeNyWx5V5AJhONz3IGIzXfdRwJTV94N2X+/nP\r\nDznIS1ya5NFMwHtUh4MNbifAoP6noxEuWXoPVGIFfRw2znJSrObNCbxiDvgP\r\nplu72HrHGCT9iAwjJbT1a7SvPjHCTbul8ntB51vcGjeF11TV8h7cZRMetf+e\r\nqOqI5uZ9aTsiUYZ8qzzPKYnRh053lPD+mx+y2Cc2N9YgdxZmx/5TSLUkdyly\r\nmeTO1n1dwmU8klHnPh2iKcsP5NKivnOs2qV8KVcd/c0n/WLyBxEpAo8pwyb0\r\nAoxTHJHvTFmI3JAqh5RWgq23m2y9Wp2WzFAqZqTNaosBMktmD4TO9A2c1bKs\r\njEw7WSjPgLuQhi7ttoNPUkHlbSrI43yMjerOCrUaIRg+7f2ogZQQBUZh248A\r\npEV0Y/yQTyjyxmibb8a0H5Sbds0O7qjwv/o=\r\n=HcKi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.6_1675702830286_0.979344816792338", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/sunos-x64", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ef97445672deec50e3b3549af2ee6d42fbc04250", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-phO5HvU3SyURmcW6dfQXX4UEkFREUwaoiTgi1xH+CAFKPGsrcG6oDp1U70yQf5lxRKujoSCEIoBr0uFykJzN2g==", "signatures": [{"sig": "MEUCIFYIJzsIdP3FH/ydNn+J4lSWIE1MT+6j7pFKmoWUZTtfAiEA64+goQFyE74rMdoHPN4Tp5mnsV+Pmnyh+uH/5gUWJQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHeA//V+TK1av5NI6FPkTcOJSYx+lqZ07BY7HiqItvotLH/kygGX+C\r\nyjVqicEX1SLDl7tN8fk3Mu8bBr7dLeWY/zLHg8iH0zkA4cZ317Q5Zu1PfCz+\r\nfCj8x3vjd3N4GPN48/E4QZ7I/LzHtm5XfiKQDdYxMjSIAM3NFlyNX1KOhHwK\r\nKamm8+cOec6ih9kkbx7C1PVZxpKsbdm7FRF5yo+Rtaf5TOyKRGQesgz5eisT\r\n6HJnF1Gcluy21HAMhgtNYlNuZfoGVrPxDO5MwrkcMC3NcLiVcyRYogSrAS8l\r\nAleoP/K9aHDKokeQYs/Z7cGsr/TPf/TlcRg5+Chse58574oIPZiKycTMf4E8\r\n8i0Wo/sdDStjVruKQKcXee36IDC9V7n/QkD2bFAKUbBg7BTg3cis5bf003+k\r\nZO/SQs3sm/bfAlU9ah38NJ2bjpq7EMOm7q+puG6picJa+OROXmuz9H8bWLAw\r\nr5sVZIglKpa4XOmApdKBQfFm1+LAta7DxTcOe/f6eTm/dULmchuB4bqKfj/8\r\nkUTFU9C5L5RtpmbKtlepHq4OwAuauxPG/eZwJI7igLVUchMxjpRnvRyZb1CY\r\njNgf97MmX409A6U3N0/izKv6BmM3thpsuP/Uj1kqdsquemeWRGrLVSMoy5++\r\nvknjHojBH9ibzicAs0TqMeSU30SLw24E58Y=\r\n=XBJK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.7_1675981591695_0.8595871420154402", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/sunos-x64", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2cfb8126e079b2c00fd1bf095541e9f5c47877e4", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-0xvOTNuPXI7ft1LYUgiaXtpCEjp90RuBBYovdd2lqAFxje4sEucurg30M1WIm03+3jxByd3mfo+VUmPtRSVuOw==", "signatures": [{"sig": "MEUCIDAqBI2oSgpG1ByA/T05R9jvYxhaVn1sYBJNuqCDwmG2AiEA5nZNg0wrirnLVmAjYr8nKT/bnQohXm6+o6Pr6zpFaPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8991193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6doyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3exAAhpUvhCA6MvddWbHsM8q9gCEts9Vc7jorcjVgiFfMdNdOrSpk\r\n3K3/c2dHL4JE3V0dkZr6gk15M1ev3qEBnrfV5UKAnaZZqQ+j4Ar8frsINNFg\r\n3bb8WRriKH+dUYaszY3fXs6fh5bzUqBhfXoglP6IvKcv+TjWNN+Z85VYN5V6\r\nkHDN0hNcT+t4mbOp0Mpi9YH7vb/oUPChIBywjUlfeOOkkD3g4D2gv9YVZQjw\r\nKr9BaZ4th/GA7aEZeI/Me9sngtO2pTwG9erCvptm5voVY8+Q/OfDm/rRjBSz\r\neSj4Nxj9Kg7yTpD1o0s/b8vhEYx1OY5ovn0bnabB5ZqMT+Ipla+MsWfD9ltX\r\nfUU1zcjwd0YcQed20dPnXZIg3oFw5NNMtj2bj7k0PlxZ9KgLfgyW7p06aboK\r\n3ErYQjKayZbDPXxG9oLtARYP1h36+8MHE93tq/inm5FbIftT3fxETc3yIaT0\r\n89DeT0pUI5oRQdnEpgDy24Syg0YJPXYSxbT+M1k2nNQx19oi3rNHtSfFiPva\r\nZpyK9IgnalZ48JIfVfHJ9tge+WZoel9NirJYdE0GM6kovqRYCkavswC8cBJf\r\n/JfltxQGmw1veEN3dPqdAwNwVGmA+cGAACovIKNKzcd1UrRYHLdbZ3Xfd3CP\r\n/ssY4UELbBpgdxEFbBuUN0yT2WR9ptEmVyQ=\r\n=hs8Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.8_1676270130179_0.19189562731889653", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/sunos-x64", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "fa0e4b67b6213423c7a57f2d4ecec19c6f4c3012", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-f89/xt0Hzp7POTDJYSJvotyFXatxXBGXJyFFTQGJW+NTYhFHaMcrrb41OB3L8sfzYi3PSlM3pZnwlEk1QiBX2g==", "signatures": [{"sig": "MEYCIQDBn2PuRpL6L2wrznOJYYOMSx+r024jzevDXfhwOGwHMAIhAJrBGtsxmxI009GuvII1Nc2KBBEmP64bV65Oc9db/CvA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8991193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mAjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/lw//UTa4MwAXms8lC/RjMQJCthVH+fD7djDkb5Sy450wa1xqJ/2T\r\noPnuLGfpqh/Smn3xONMk3cUoNfZqV1p6RhBPhm0ZabfXmpqisV037eAPXPBk\r\ncnkJ6DjvB4z4eV2cDeUANibJC05uNzMcWWD0u6vWT3+gF8HVhgFRNj5o0+PQ\r\nYbCxIiysgmQwaYpRHFth5pM3UoumOUCWI85u1swex2FJFHjbnZLEO6Z4Slmc\r\ntApqadnGPT777zzW4oGM0GBViht8JDNHbVEQK7jBaO7fvhQ4X/WxDObfCTjS\r\nwmaJqLDDeGf0X63ATmAhhwJ3ybc08w30Oq7F/W+Yo+1ALMsJP/ySeW9R7BKn\r\n0K/Fjn/pAFrDDOoYDhjsiYbhRNqHRdAddiPeYf25wYSkXwPnIGhO+r+vQZQH\r\nviPkYLPrqEu+QpkaJpqFHfwxtdJc7alqBEE5CDLX4zBExDaTT4TLaQnclS3/\r\ngCYAyWqD0dXsNmTrF7J1BDHoPIKvjWz80H4H20JoNtnOyoJkHKT5a1wIPhqD\r\njCoqwul0xD0mclttdxDnrhXcOC5o5m3TxaOaemIuF8kvsCVq/hpOuPK3Ne+1\r\nqDKZXO4zdHC7NyoJWks8D3OssMgKYE/BIlUmGtHm+HJS56pjXcsRGyFnoYKp\r\n7LlCeYJqcU8KhsRAPtVix/xTyFi9ikpch+A=\r\n=yk/Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.9_1676828707241_0.7030239996729026", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/sunos-x64", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "8deabd6dfec6256f80bb101bc59d29dbae99c69b", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-ERNO0838OUm8HfUjjsEs71cLjLMu/xt6bhOlxcJ0/1MG3hNqCmbWaS+w/8nFLa0DDjbwZQuGKVtCUJliLmbVgg==", "signatures": [{"sig": "MEUCIQDrXFzMbKkiwBNeOBRzA1uQbkCo4B+3rQ0skByBeMLMKAIgOA4qKcXvNFyiGsYjXOwhCUUNkJCARgvvpn/oGl2E79o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8991194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87PgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbtA/+LzhnQrzWRU6oX6cq3w+2XVgxmUGAUAiaMrQQsinDC0i18ji8\r\n2tJPaq2rUOewRHTtnqtW9RJPKgb66HxolYnQ602bf9JejIdnUFPZgwV9n9J5\r\niECQ78NS8Mlr9dKc0guJQiUifqkFD8tEaEzuFotd+cWZSIkOhczvbR2r7+zd\r\ncGosmB3Ch2Eg9kzKYtPWUNxixbkUJAtqBMvcvzQ4ZMMiVw1y828Fms49WloX\r\nY+0Oy32/lN5fFPFXrAmDVKKug4+qE2WRLuouObi0CIrHsISLUMqdlMCnfSJA\r\nunPA5JTNerM5Pa0+ARK4SGn6BXK48bz+0g24enp6nJJ3qr96fNZRLKbRmm5J\r\nDghYTJWSo6SATLXcPiNqzEjhc/HU4gCh0s5tnlT38f2Th0dCZmyAO1rferEd\r\nSH/bJMD50mAgLieVUwLXyGYBHbfAFoOAqy1im8VOXlRl6T0DQFbzcJc461cw\r\naeTOJB3dHuq2kIkxf9yYPhQy+svzxhX4UPL2CvygYUcJdoKeSoHWJXnTrQPv\r\nDrn9RdbxXpp+RPBaMXwzEScD4qUg5jsEZno3dEXk1/k/Xr5VMpghhnxSQW7t\r\nId3WbMfEvwzLZsUDpGODJnWfkJkNXTXFwM2RM2smoTt9uPwDpxbybSKj37H/\r\nycf0B1EkclyjdalMY5wOU36RXvqXxyEmX7U=\r\n=vGEi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.10_1676915680644_0.5747526531326528", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/sunos-x64", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2ea47fb592e68406e5025a7696dc714fc6a115dc", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-1/gxTifDC9aXbV2xOfCbOceh5AlIidUrPsMpivgzo8P8zUtczlq1ncFpeN1ZyQJ9lVs2hILy1PG5KPp+w8QPPg==", "signatures": [{"sig": "MEYCIQCBelzYO57PVyvteg3zHePk3pYIEwBt/hZV2z4oD0i9UAIhAP1cPPH+DQ3JEWfD1p/amSMmFknHxBe0R7MiRwBNZcBc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8991194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAnc6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIIA/+O9qxHu2mDdZVgUJyKWIm83YCGI/v5FiP2YR8vxF3FvhaoRLN\r\nE/mij3xIZit+OVwySWANwjQ4D6w0ZOig6wy/+yTgUATpj4JNH358cGMvpV1O\r\nX3MsdF4r3iNjwwxTY1AuxtLN+p+OLt7Vqx/WkmE4cCKeSkfsn3YIlWaVzldT\r\nnT/zSwbsf5xY2NtLvHkmq5WgBdpWRL0DSSiK2ZoiSaSCvxDrZB3m6B6e568q\r\nzigfJ9uFVSeB0GYNxC9rnMH9wKNbOnaGh27UtOaw9rJD305oZjI5dq10odcV\r\nXtO1KMCctxg0b1CHwfNKl1z7LfUhNUiXVdH5nwo1WsBftygTLvaqt4mnIn43\r\nZQylq5TYPcWoiACwRg2Fm/7gIMx7X96ciik5vFC6FESFKmf+IzSdocoxwGSm\r\nq19+7MEMA40OVRwqHzSodHLF6HHc72W1T4f6QUc6qHtZcXavSAVnuItxpuPd\r\nKUQdvwXozoWaxVTWdmu4qgaOuDNzlsmEBH+ux/iDSk7Ukpt722nkt1zmNcYF\r\nZJTfl1EyVd45TLsQ61yat9qEv0qJ/wX+b7L57pm8Z/ixdfqnpEM8b+w3MoVH\r\nA1nMmxR73vXGxOVwm5aOLDyU4g9WmrCE5J524p3OKSmS/ULRNfYvtxzNcr9B\r\nAGRkG0bHI3riipq8W5e8DLSbTRBdefTDUb4=\r\n=bqLY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.11_1677883193959_0.15995276445150663", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/sunos-x64", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "cf3862521600e4eb6c440ec3bad31ed40fb87ef3", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-qbHGVQdKSwi0JQJuZznS4SyY27tYXYF0mrgthbxXrZI3AHKuRvU+Eqbg/F0rmLDpW/jkIZBlCO1XfHUBMNJ1pg==", "signatures": [{"sig": "MEUCIQCOLSnDnN4+lvyf/qJspdiaPgQLj9MYyYw22uAhPaGVBQIgEmlVUTjglctYOY80U+1+2VcTV+NycQooJe24gajUsCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9023962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAWqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsYRAApRQSwqc1N5/AyUSaFWYtEJsPJElgZFRoQ5A7yFFWiu1+xR1M\r\n0ZB0oEgIUShWoy6YRNlMFGUzW+dhBHmMFg139OIMDW20SddAdzHSV/TFPCOm\r\n4xbr+wiSXlOdGApnC0zTLE4YwDEEv7b6ebmLVRnNjtRGd2KRJl2fH3J/t7kz\r\nbMico890ipU6OeADFb/R5qj3ZdDdnZ27DS42S+8ZykKUjc6ZQAw7TnJZlRM3\r\nwridttmnI2EM/4mjmQqXNoz5WrlVkyLczmA92V78Nh+8/jft1nRW+pnLh3Z0\r\nL0W9zw8OCu+GUMwRh8YwcqEhrtmccpXsbGLF6vqerBZGqyfukyQEeHN0lebu\r\nsGfQlq8H5zqUQeFa0MD6+8ZBJhBTufQJqUfoqzcZ+koIYqgHYZ5xaHebAxu1\r\npvE2zPxLfT6Ox+HkvuU+8zlfDH0WGt4iNOjrnlJduR0Vn0csTmJw/6x9epPn\r\n232hq1wQ4audEpPm5i5yumiGXwdycBl7e7qlT8FQspBv5ViEX07auMepacj7\r\nl1R8qDQBS36imNWBTn6tFeHNTzYUnSUrMGWTNtPbF9GIRew5i/Kgp+2/rt24\r\nl1KzErLa44pQsfWFQkhoY3ML0JYfJhElaZGXL0WCfxzfpU5Mbl8QM7y27EYS\r\nrYp0bAAD+Vf82RHOTmFextLIL7CM/VKIpyg=\r\n=/R2l\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.12_1679033770311_0.9336618927769262", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/sunos-x64", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "716382743f17c080b17a782346c33c976e10345e", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-ALbOMlTIBkAVi6KqYjONa7u2oH95RN7OpetFqMtjufFLBiSaayRuwUzhs2yuR9CfGT4qi0jv6HQDav+EG314TQ==", "signatures": [{"sig": "MEUCIGi6NYCn2j9FFDjeqDRq6SmPrVbcDtopLSXGEh0dDjjsAiEAn8LyKyWKi14h5S/HCZGJGb1yRPLjrfT8I9OLfzHKfZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9028058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfJ0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwpw//c+hJrqWBntPd5s7D1DR5DYawZwPVcvMbBqxyLeaPZWEPi48N\r\nSKD3N8G/84hvD61ySSGS1OLYzdBDfjT/PHmCdv9/XK+BLoBd1SK2ntUxESU4\r\nk1VloCSU/dLvrtffYmY8dMx/FiG0Y99D4ZM5WsHu7q4D8UL/XVp5ZoOcFyK4\r\ncv/DmGpDd/lTuD+CpWKgGfiz/VaIPgFb9hX+0YzD+sMfIZoOqvygUja+R/ui\r\nW6CV1wDxRx56woVtOXz3KKqgOl2mYAHnEW+G+nHR0cxR2Q+6vCcbTdVkyJQi\r\nHTBnT0q46XjG1Oq4Qky0xem/0ra0gFNWAXGKFBJP5veicdJ3MSxa8BEzLcDX\r\nhHzRhhIVE/x5yGu4gK7AHrKLtiMQqLVDilFSzeJe2CRb0oMf8wscI2CdHZYl\r\n66A/xdIYY2XhJRRpS3hm1pUVM7jLe+1JxIF3XAwBRp/7/vaypQN1ggcHDITI\r\nsI9Jvcqk50fCG0opYAzQ7cLnJwMj8HzTyEvTVG62dAGfwpclb9gnRKKKuvei\r\n38KjJ7vKD9kOEAyYEnG13K3fOKjWf1BHZG4dtOgxtNsBPKg5nBhkb2OOKDym\r\nrCYz8x8Cz5VpE6BqPaWhbY3nNzq55l0bqQxgGaOSUUeBimmiw9/wFNhR8l+J\r\nqr7x26d5icR1M0junzT7YuSf9bavvYEiPB4=\r\n=QPC6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.13_1679684212316_0.7567637110470506", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/sunos-x64", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "5c7d1c7203781d86c2a9b2ff77bd2f8036d24cfa", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-nqMjDsFwv7vp7msrwWRysnM38Sd44PKmW8EzV01YzDBTcTWUpczQg6mGao9VLicXSgW/iookNK6AxeogNVNDZA==", "signatures": [{"sig": "MEQCIARt9dyI6bJHJUlCErzEEoKm0+r5GRGgGLXdktAGBopuAiAcTM0ilADz2AxsNi8AARsSBk6q1lelPcivIus8YPSSog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9064922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7I+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxXA//X+AIofUWTyhZGj1oSJpqNLf4p8I4oWNXj/NZAw0n6BOhGW7F\r\n5SlYlT6e3ei4+XP/w/FtVCWu4S2IwM1gBgPwszFkmKMytv2y920aWKXfDXoN\r\nl3J4IEk1AAKhyNYzUnKOfK1xEeB1gMqAmOm2Y0917q6QTYFCGOvm1N9aqCAO\r\n0PltZzy2jFQGQHtG1McfVcjOKtxt0jUfOPtPx1I+qNDbNwH7g7wYjL8Z0wT0\r\np50u7lqtEjrw5QInmB8ISmynu33BTRrw8pqKBuSi7HG//XAbIQkJKoiMbthR\r\nNXJ5y41dV6DJSESmVZvXUBENixFjboi72RBVS949IR9jP2waVVkEld1rtvj7\r\ncTzcQ9bniTo8yRW1TNb2DEYOMjeGZn/oourQZMWDmtrowJMVCKyc5K7k4hBr\r\ng5qNBvPNtn07cxQ4KvUyWhZENhJpE9LNxA1WM4mkt8xAIi8J2Ubsca7t8ID/\r\nvulvCQygoHE3Wl5pRGyHbRu9Wf2uC60+l1UUKnX3r+08Ii9PjZM4pTbPWjA7\r\nE5DjDbGjmEGTaHURuFQXrvUt4Fyzp+FQ0yDCoBA/V9D8OtutGyGBHeXhwtQ8\r\nKt2iiYu83YYPhw35J/RvaFurZZk4FosnMpga5jyc/Y3zkIaEV/wIC7YNfATv\r\nqjEMZ4QL8SRFHGwUytHOMUYZwcsDXHmnRnk=\r\n=CW6Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.14_1679798846041_0.42282011073471093", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/sunos-x64", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "6694ebe4e16e5cd7dab6505ff7c28f9c1c695ce5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-U6tYPovOkw3459t2CBwGcFYfFRjivcJJc1WC8Q3funIwX8x4fP+R6xL/QuTPNGOblbq/EUDxj9GU+dWKX0oWlQ==", "signatures": [{"sig": "MEUCICBoDk4/ApQeoKwZYrohTLwqPzZvZqsM9q6vncJRD0+8AiEAkrM3WR1m+Qz4iNCvlDle82MQbcf2pe+0UbiU4IQ8CE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9064922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm8Q//aVN342g8rD2xXsNIeDvGML2kEExsNKdKUMD4GapuFfzXpj+q\r\nc9S88k+Ff/+MN5qDnUvjDpsvCcAK3SmxYudbCd5SIoNs1wyIj6SgFY6GGOjq\r\nnEssqiPMtaIIZNKdQDMEyaDNuBz0nDhBj91DtR71YCnpXv1ABO3kvC/dc3OK\r\nQ70I461FZN65rtgZq6Sn8cx3DOmRHShF7ajoUB8IMO2HI7l7DDjolRniIB7a\r\ntlJV2qqbgKeDo/234h1W0drJXwaYbmBeTNbyk452emRWrHwVIjz5Gake1ZDe\r\nq6q5conGFEZJX3yUCl1LWJZCYs1//Zv4NmppBFr8sa+mpfpSlhXLZJ5Zyojz\r\nLofhAShKAlFjhj4mRNbB1rPu3AR587K3MMlvf/DO1/j2AZi0LrKei7aV4+43\r\n1G1Bn0rmYyupPVUq9BhsDc/g8yfSIAhX2UEJRnRsw7y2KZHSXp3df1XbAaYI\r\nchKte1KJMXuBYnnwyIlDyEMUTFOVVJWphe2LQtrOd1sofrf52kWdUK9YRPzT\r\nI9VPkmFcr8A7/O5VM92u6/iKszenZcsQcu7wVycYToWacFbMw4nm+jVuuhfY\r\nZtAZ95FIED1oQG9nEv3aRMWpj1Q9tmzFRks49+ka6INN8etupWOqPL3G8AB6\r\nD8inH+z7e1lpLC1xkfVsTtmzmlkWaXiGB9c=\r\n=th7v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.15_1680387993025_0.6750753570552399", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/sunos-x64", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "16338ecab854cb2d831cc9ee9cc21ef69566e1f3", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-jlRjsuvG1fgGwnE8Afs7xYDnGz0dBgTNZfgCK6TlvPH3Z13/P5pi6I57vyLE8qZYLrGVtwcm9UbUx1/mZ8Ukag==", "signatures": [{"sig": "MEUCIQDxM/rqAbIzOkr/EHqp9b6unrfLZikEMrkTvvC7bi2+pgIgDXS5IkcLTVOPEk7AUF4oPJcZn0ZF7Lmr6IilnNvAtcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5HnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy7g//bWgKvathqWbXy7vz+16VPsBxe6G/QVsC+y3nEaybAjGkpgxl\r\ns+b6GLN3IzkGxcmcgH7kF0UP6a+sB6cjF22RHK3X8/6sBQTWxb1jrZ7scPZx\r\n2ZR+OFkTbtVJJFWThx88v/Hn95jfW2GxUqhcDXkMLV1jARX8YPzSphBIpHJ/\r\nUIFAuSrjPCAtEO0E+dDq+G18N4hjwIElv2NfE1O4zGkJtknr5341wMjor2sT\r\nhPaMlS9KVR8as9b4FT140LxyPP2ZhnkdbAcV5pi2HGqBbpxRFCk6chMYX9SP\r\nkzdu8j4dIif6rMb/F9GtT4gOFJXZ+Slgg5wm4qldMe98zBLIcyButjXOSP65\r\n2vXYlkx7EfiMNMXdc1G6x12bkqcxSIubDzTDTouyK1nQRXQ/CZKXeDlbIPTd\r\nvaAIJZZbKvTeMaRKMB352hVB6dOCq03goPK2qX9uRuwlUAmh1zlJDiYJzjds\r\nGmCyh47d2f6ImyJBO2CQjlHJKnwrdej/2dAa4TZM6uKTQmLlr+T5sn+SkY3u\r\nS6XEHgWPZjZTnKRd55GFkUTVgQh5Cyen8tbe7RsRrcVj2aShMLk1xIH2iLFU\r\nuatQi9z2QykdU42hOYNRr9PcTVhaBOEun92LgOw3sC+i+RjZyOjkhxj7WMbY\r\nK16BqlhSvbel0CC4xJGlgXW2trLQ7A6bfk8=\r\n=hHjy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.16_1681101287421_0.9117500954752624", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/sunos-x64", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "10603474866f64986c0370a2d4fe5a2bb7fee4f5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-V63egsWKnx/4V0FMYkr9NXWrKTB5qFftKGKuZKFIrAkO/7EWLFnbBZNM1CvJ6Sis+XBdPws2YQSHF1Gqf1oj/Q==", "signatures": [{"sig": "MEUCIC6So1eBgIyy0N0tM/Sm5dwKAFjUgLovJ+BuAFfFLUpCAiEAtgXOWLIFUSFOjqUGOhPmpHGkYlNj/a1F66Nx0M/BOxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Vw//eGbtR4bof3zjOC6JHIMc+mWWNvPOjJUV1QTZuHK4hlmyugMc\r\nQSfm4Q91wlFw3VP+1lbLJXA3xBllXolVwYi8d8z0o5/pg3NaZc/qWvdsQx1h\r\nWYE7xaCW28J5X/uk/6EXFKAfYeSGq/cYllnAK+XdroBF93vq4/cB/4cVfm+4\r\nX/eQJZhVgzbW5yy70ZVDOTj5BtkE56ZsGk/lCZPE12hdEWdkaDgtTyVuEFg7\r\nHp6s12+IvohkjEQVPiRH8cNSrQkUc3wgDC+syH9OJAEVfTLzaWo1LshCTJPM\r\nklUz7WsGERwD/7sPOFKWjwoqyLAblGaoT6qg18NalxqW6gGGKO6VKMG9TrAx\r\nPFzCPtV1yigzusE4cCBlu8rfWAjryhvNFMVkdP9BEkd/NN+LMhdVmhDTDS7j\r\nCif/To5iIdgC+IKjbJ/2w0i3qayzQziba33ub/Nw4DZsyL/Dj5ADjp+pV8Jq\r\nLkLUq/0DdwDp7ezZNNpSnlNyGTw1dnexazx9Hi+hn2F963XQyQXCWJWSG5r4\r\nV68j0zZNdqcSAnc7AvEPP51UzDLxFU1wjzQQiTHI+hS6Nd70ee6XmwfmFGSO\r\nVgYFILMie/9HGupNU+C1SjwVWBHhs4nveZnSCYwUEMo7f7gseEv8SlumIUkr\r\ndhfzG162RwGNPba6WYNfSceib+ZRdIQnhLk=\r\n=az7x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.17_1681680202067_0.30075180876124175", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/sunos-x64", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ecad0736aa7dae07901ba273db9ef3d3e93df31f", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-Rzf4QfQagnwhQXVBS3BYUlxmEbcV7MY+BH5vfDZekU5eYpcffHSyjU8T0xucKVuOcdCsMo+Ur5wmgQJH2GfNrg==", "signatures": [{"sig": "MEQCIBAUR3Wvs2h9cey4JyQ9e/0GBGNYCIrM/K/BqfbKV6M1AiA74Lm3sSjdPaiOCUPQ0xk8XrO0azTqMyIOhZdYSiXJoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9077210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq38w//dwFo8G8MiNmEoogvVL6lucscKIGMRcE4dNr+RXi2aMpSiOOX\r\n4O5k9Q+r0NbZkRm7djTfD3ge7ba2oToww0Hy+3U0uGrQGAjaEUZ9bEOA6bbl\r\nzHgyvBpReecgvJfv5EQjsNjyqLS2k0WW0Rej40n875gav2vS5F6ilekzeF/k\r\nwVWO5yzubo29BhTkBEzOpyazjnN2180R8N0OrRrTkravu5S7NmGOk2T/B4zz\r\nh4D3iT+Upmb/HxwkQXM391+7gomPn73xpDk09wcMUZ5N5pnVcozy2PCERdc2\r\nwlKbsKifItnIVMtMmwVPy2yXYhIPTyAtQ5ZiS3sjkcDiX5uwNnvK7CGrjkej\r\nmOScpBf18vEQqdAbj4UWVZvn4GTKxDhj+9v2ygZynZBeTa5AeDVS49HC23V6\r\nDlZ3kFprrkaJxbM5z1bH2XCZmwrIsFR8MQL/Yh6otQoGrSDkG3sfyy7aYWnS\r\n+UX2mRF3qPKsFhKRpMRXEirLjLPhN4zPJhN3Ct52AAUJ6MBOIr0cTVpkKzNC\r\nSMFAuJIBeDyJLnnCmudF3vbHWvr4mVupc4J/bJQXioYgLDl1P9ju6kYWFksr\r\npCDqvMMd6GjaCaBD1an5L3JMbNao7zcwsjEhnIU1CZ1NVDjEUmC0JQUkhJP9\r\nJjZidquUL4jI/itrqWnRTrv/JoIl/kxpX6s=\r\n=ELeJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.18_1682196071528_0.6684294759823632", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/sunos-x64", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/sunos-x64@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "722eaf057b83c2575937d3ffe5aeb16540da7273", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==", "signatures": [{"sig": "MEUCIDGiziLLpCjpsiL8ayLNo7HRmZtDxGyclGsjJFJt+ysbAiEAkYV0ghr5ouD4rvM7sGltLXgWK9Bs0my486AvQnANA00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9085402}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.17.19_1683936375669_0.9756244418818607", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/sunos-x64", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "1f3ff9aaf941b13cfabbc93f3e213a8d38a0c50f", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-kl7vONem2wmRQke015rSrknmc6TYXKVNs2quiVTdvkSufscrjegpNqKyP7v6EHqXtvkzrB92ySjpfzazKG627g==", "signatures": [{"sig": "MEQCIHttHkO/QxW9ews9CWQQ+FvsgqoXyAK7cx0kf4jRuERwAiB7I1ShgOiiiX/r4Adix5XSl2BCCxJou5ZqymBIj//84A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9089497}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.0_1686345848140_0.29816322758863323", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/sunos-x64", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "e3f5e5b7347af74e9ae2a16ca045d737c077a987", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-6lTop2k+GMkWlrwMy2+55xIBXKfXOi6uzWYypXZZP8HxXG3Mb5N4O71z2KzisVNJtYK2VlQRNbmGtUzIQIhHAw==", "signatures": [{"sig": "MEQCIDdMEt4jUcYyBHiZ9timYgQsUd+eT75bWYmz8XwR17DrAiBG54M2HDa9bMeTJJiyepchZRjxFtaPnmK125eM6ziP5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9093593}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.1_1686545493893_0.5210122185843256", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/sunos-x64", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "7c2d98f4d23d251a02ad01f6e2fe887c899ac8c6", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-SMZPTACsvpKYAIl9o8nhnmMn6/lp62iMeV/2EBMtj+sW6dXwW9b0cLjihkBv4PG1CCRlwWKPZo43imqZxC95ZA==", "signatures": [{"sig": "MEQCIEzz21u/ra4nwOG5d+/3A+2loHASqSde1zNOByr4TH9xAiBrgqtZdKY/RqgHLo0Q1xklKpxhQpAuupfHzkYbS0b0Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9101785}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.2_1686624011678_0.2241133822050143", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/sunos-x64", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "9e85422620b8972f02489ec04329492dd04dae13", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-Fa3rNQQ9q1qwy9u2cdDvuGKy3jmPnPPMDdyy/qbn5d395Pb9hjLYiPzX9BozXMPJDlCNofSY7jN3miM9gyAdHA==", "signatures": [{"sig": "MEQCIHymFDg3zdjJxrmLJ3EKrinFTbu2e06DgHdvPVfgKkR7AiAQUDXno99XKNbjd1RqG7j0wlH6pZHIMBxSrCbn1KIf0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9101785}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.3_1686831638790_0.9501960334197412", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/sunos-x64", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "f0aa5f8c3929a49e721faca4e7491865324a6bb5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-acORFDI95GKhmAnlH8EarBeuqoy/j3yxIU+FDB91H3+ZON+8HhTadtT450YkaMzX6lEWbhi+mjVUCj00M5yyOQ==", "signatures": [{"sig": "MEUCIQDSAESG/NtaTtH+5/hlQRsyZLUkOp/PMo5B+ArwYfKx2wIgWgLWKZnrIibHaZIMB5yKJ8RI9Lg2vYNg+evgtbkLqb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9105881}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.4_1686929859512_0.28427680973634706", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/sunos-x64", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ff5b4b98b93a46eb45f648331a5303ffe38bc2fd", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-ngm3fVv2VxufI8zH/Phk0mYkgvFjFGnS+l7uxxd20mmeLTNI/8OXDJpNqTUbvzJh3tqhI/Gof0N2+5xJbqEaxA==", "signatures": [{"sig": "MEYCIQD25pgZ/WG4Y3iSq5i5wDwc0tqfTLOzRl/yE1DBF42HJAIhALlGPyXiciQyeB+xSRATHoyGwf98h/wSsnxB29utEOF+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9130457}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.5_1687222340589_0.6353486584740731", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/sunos-x64", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2962c270d457b5f8ec2fc4ad1afacc7efe913fef", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-uxk/5yAGpjKZUHOECtI9W+9IcLjKj+2m0qf+RG7f7eRBHr8wP6wsr3XbNbgtOD1qSpPapd6R2ZfSeXTkCcAo5g==", "signatures": [{"sig": "MEUCIQDS/Tvm0wGf/veY6ggkppo5X0H+rz8uCsfHxbvNL1BTMwIgEkg01pNxlOL+cb2KjDPPOcF5+mdjKDj4MKB9rRXwMLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9134553}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.6_1687303480221_0.38165129796747044", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/sunos-x64", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a79c5f91896cf787e8ee4c11787c148482a18531", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-lBhsHaM6EYCmzQCj+xeFev+dgqTgpSRqF7qXrxp5V4waFuidTBbWgqSXY5rsLRNLOyMMCh1cA+RqF8UL30oOJQ==", "signatures": [{"sig": "MEQCICUqLct1zjK6uwJY/ODApyHXmpZMpVKmvIliLLdFBkIEAiAMXdNVMdXcGWbIB2Y+PX273xNauta6y4IhKB00QuIklw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9179609}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.7_1687574772622_0.8469454379995691", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/sunos-x64", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c1de112a361ee14cf37f2e4606be88fc27d9c23c", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-DSgYAFzvRisJQPxtTsUTFJ/Kr1KYZxxrKGfHPMnW2f/0KxOdLwRKbzWeG8g15gSBcDuDCZXnuUSFyu3ZyqbCzA==", "signatures": [{"sig": "MEUCIH13ciH4ttp6paiu7SO7qWV3ygHPz3kpLynCBGYXn+wNAiEA7NSzaEmVh5P5XCdjZtVip3WzUar7fPH9InPX0aFGCJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9179609}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.8_1687663136075_0.5964214268407455", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/sunos-x64", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "9442369e234d7eb53d06fb385ad3a745af365790", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-W/eHabLCXdki/8H3jmfE/ClDuh3bQQKpYfQHGQ7lQync9W72ZdVr2y1iWfEVTE7ZK/DQROo3GyfTkx5HPBZxmQ==", "signatures": [{"sig": "MEUCIQDfaoxGx49aXRtYoB2+Y6jf9zCMpK4oQiWtxtxfp3nIjAIgENOmSxR9y7IlU6Dun51wlGCDPIHSLHg8/Xq5zWR64/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9195993}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.9_1687757260171_0.4886124202486142", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/sunos-x64", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2ad291b87225f82490d3ffd7c8b643e45fe0d616", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-vIGYJIdEI6d4JBucAx8py792G8J0GP40qSH+EvSt80A4zvGd6jph+5t1g+eEXcS2aRpgZw6CrssNCFZxTdEsxw==", "signatures": [{"sig": "MEYCIQDqglcPfaVHggNVndxZUi/rju/P7bcCSSg/8HlocNOf8AIhAMbZP48fu+Wg4TLWOIIAL4eZxo3ZQVQ/CQvRnt76NUyq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9195994}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.10_1687814415445_0.12264607664089433", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/sunos-x64", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ae47a550b0cd395de03606ecfba03cc96c7c19e2", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-kxfbDOrH4dHuAAOhr7D7EqaYf+W45LsAOOhAet99EyuxxQmjbk8M9N4ezHcEiCYPaiW8Dj3K26Z2V17Gt6p3ng==", "signatures": [{"sig": "MEQCIEWskkx6bAwKoP5HEfatNyFWWSI2y6aew7uXw40V0PDVAiB/99omxN/I3rhoxQsSaKkpnxrXSnAowCG0sh14VcgOiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9195994}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.11_1688191420881_0.9299738860806093", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/sunos-x64", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "4d91cf84062dd0db5e9f8484dcaaff6443557839", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-+RkKpVQR7bICjTOPUpkTBTaJ4TFqQBX5Ywyd/HSdDkQGn65VPkTsR/pL4AMvuMWy+wnXgIl4EY6q4mVpJal8Kg==", "signatures": [{"sig": "MEQCIAQIx5Rxb1T0W3CUeKu483D5ZujeaN7L+gYF7F0S5qNOAiBaG1HidYYbWBdkASKR4zS/jCIZfELdOQ3Ks7Woiv+suQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9204186}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.12_1689212028969_0.2700434791759818", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/sunos-x64", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "b368080f42dbb5ae926d0567c02bcd68a34c5efd", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-X/xzuw4Hzpo/yq3YsfBbIsipNgmsm8mE/QeWbdGdTTeZ77fjxI2K0KP3AlhZ6gU3zKTw1bKoZTuKLnqcJ537qw==", "signatures": [{"sig": "MEUCIEB3e3sn41m5o1YQEHAxR+Jr4h//HLpqNm1/NWkz+IruAiEA7JLgRoBFDI4uS4vfuO5XZTglP94HcGM/YCAKYQGTdMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9200090}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.13_1689388619056_0.6402246094542074", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/sunos-x64", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a50721d47b93586249bd63250bd4b7496fc9957b", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-fYRaaS8mDgZcGybPn2MQbn1ZNZx+UXFSUoS5Hd2oEnlsyUcr/l3c6RnXf1bLDRKKdLRSabTmyCy7VLQ7VhGdOQ==", "signatures": [{"sig": "MEQCIGbxXA3o673zcSnJorbtJH6253bv029wV1vaU4rGBJbfAiBL3h7FHaT+/YsJKAFmR7lUzUJe8yqqKAuPylcsxhmk0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236954}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.14_1689656405155_0.3368975431748109", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/sunos-x64", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "dbbebf641957a54b77f39ca9b10b0b38586799ba", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-qkT2+WxyKbNIKV1AEhI8QiSIgTHMcRctzSaa/I3kVgMS5dl3fOeoqkb7pW76KwxHoriImhx7Mg3TwN/auMDsyQ==", "signatures": [{"sig": "MEUCIFq83X/8cTS6TUpctYmuYwjejzHs7olzN9daQ8iiPl0TAiEApZjUNmp+T5Zmd2O2XUAojJfgGuletZ77vPrhfU/Qy3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9249242}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.15_1689857581473_0.2580805257508152", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/sunos-x64", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "998efe8a58374b7351ac710455051639a6ce6a05", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-ZoNkruFYJp9d1LbUYCh8awgQDvB9uOMZqlQ+gGEZR7v6C+N6u7vPr86c+Chih8niBR81Q/bHOSKGBK3brJyvkQ==", "signatures": [{"sig": "MEUCIG6X/xZyL35q89AXtbvxcXvpl+Xid4XsGUcKrDmUEPxcAiEAzxtWKY2yACHI6M2WF4BhNitMU2gYpUHAAYgISp8br3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9249242}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.16_1690087666965_0.741260897926314", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/sunos-x64", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "74a45fe1db8ea96898f1a9bb401dcf1dadfc8371", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-Y7ZBbkLqlSgn4+zot4KUNYst0bFoO68tRgI6mY2FIM+b7ZbyNVtNbDP5y8qlu4/knZZ73fgJDlXID+ohY5zt5g==", "signatures": [{"sig": "MEUCIQCmEoklVw6+sJANiWNYf8H1zJxbinYlC0bFPgOFtTvkewIgMn0nnIUfeMMH+hO/6ztu4Y5meT88S5JNOfHZ2bHLB3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9269722}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.17_1690335640245_0.0835112306869994", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/sunos-x64", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "277b2f5727119fe3004e673eb9f6ead0b4ff0738", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-MPogVV8Bzh8os4OM+YDGGsSzCzmNRiyKGtHoJyZLtI4BMmd6EcxmGlcEGK1uM46h1BiOyi7Z7teUtzzQhvkC+w==", "signatures": [{"sig": "MEUCIQCd5KduCgbGGeaITAgY4+1WP9s9BJWJp1g8XpPgrZeaWwIgfMY8OY8NKzyN/hVRi5X6qP7u7d7XmSl61FTK+ZJpezE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9290202}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.18_1691255175293_0.8141858875267176", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/sunos-x64", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "e2de98bd961e04f76f6acf5970263efc7051def5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-m0/UOq1wj25JpWqOJxoWBRM9VWc3c32xiNzd+ERlYstUZ6uwx5SZsQUtkiFHaYmcaoj+f6+Tfcl7atuAz3idwQ==", "signatures": [{"sig": "MEYCIQC08JuTOhA+oQ5OxYEt9XjOjnR2+qKKdkbHFJDdyv08ewIhAI3iX9yq36Fuk9fO7OROddx64xvYNxU3JDLzx1OXsrTA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9318874}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.19_1691376663539_0.5410995289509601", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/sunos-x64", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/sunos-x64@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "d5c275c3b4e73c9b0ecd38d1ca62c020f887ab9d", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==", "signatures": [{"sig": "MEUCIEqsxO9AZ3Zbbb/4zqxxQhK39ZSr2HZvDlvJYvh8KJmmAiEA3eYGhvilQMTsxlHOn201AurvljQTq+moInHy69X6wQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9331162}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.18.20_1691468078526_0.06531010212464827", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/sunos-x64", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "8de27de2563cb3eb6c1af066b6d7fcb1229fe3d4", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-bKZzJ2/rvUjDzA5Ddyva2tMk89WzNJEibZEaq+wY6SiqPlwgFbqyQLimouxLHiHh1itb5P3SNCIF1bc2bw5H9w==", "signatures": [{"sig": "MEUCIQD08SFB/z/vhVhjexrxqkmyRdWNadIdaFcw16FdJ7izkgIgQwzQD0yWmKzVOEna+hdNwAQm9rS2nvb/RyLh/z0Sd6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9384409}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.0_1691509869806_0.25938566021486475", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/sunos-x64", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ee66c195f07527eb098d72e3a298398513128f67", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-29yWBN5XfEjXT8yoeVb8cXfN1jAQLB+uskog1vBMhFR+YWOYvsrwPnh4hspETC/JnF95J+iETrvxgOUlICTWIw==", "signatures": [{"sig": "MEQCIAXRXOsaNqCfJ09fqbZQklpSwM3hsHojPJZV3nhFOEpuAiAUHXD5mZXXBOSmRc+7qTKHELjPgUM3+syrfLsOdJrPcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9396697}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.1_1691769435002_0.03470805963138712", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/sunos-x64", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "52a2ac8ac6284c02d25df22bb4cfde26fbddd68d", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-VXSSMsmb+Z8LbsQGcBMiM+fYObDNRm8p7tkUDMPG/g4fhFX5DEFmjxIEa3N8Zr96SjsJ1woAhF0DUnS3MF3ARw==", "signatures": [{"sig": "MEYCIQCpCVy5yufxstL1xivcRaF2/bHyiTCPDj9RryiIpffT6QIhAIGEQxUgvYzDwwIi6ZYW98YTSK5ud240cXQRUiu806Vp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9396697}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.2_1691978287346_0.02254703120004331", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/sunos-x64", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "b2b8ba7d27907c7245f6e57dc62f3b88693f84b0", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-Acsujgeqg9InR4glTRvLKGZ+1HMtDm94ehTIHKhJjFpgVzZG9/pIcWW/HA/DoMfEyXmANLDuDZ2sNrWcjq1lxw==", "signatures": [{"sig": "MEYCIQDQUJGjwhGSto6tUQTmdjue5ezrJgvb4mErhyCzhZYBbQIhAJrWmk+VzSoGGGuqgtPZ/hTsUUXLnyIE/cgXPNpwg9Wz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9404889}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.3_1694653929715_0.7358411014225477", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/sunos-x64", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "f63f5841ba8c8c1a1c840d073afc99b53e8ce740", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-6Xq8SpK46yLvrGxjp6HftkDwPP49puU4OF0hEL4dTxqCbfx09LyrbUj/D7tmIRMj5D5FCUPksBbxyQhp8tmHzw==", "signatures": [{"sig": "MEUCICZ4ji2zaTiIojQJRK0sYxoVgW14Fyo1AxYCfaR1eCSLAiEAuF2FJuyQPoBH7ldR3hM0fPsE8pqWiOBhXDTESHKkFOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9404889}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.4_1695865591572_0.25030251992454233", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/sunos-x64", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "9efc4eb9539a7be7d5a05ada52ee43cda0d8e2dd", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-dGZkBXaafuKLpDSjKcB0ax0FL36YXCvJNnztjKV+6CO82tTYVDSH2lifitJ29jxRMoUhgkg9a+VA/B03WK5lcg==", "signatures": [{"sig": "MEUCIQC5Xo2pWxESB03R5rMMi+BDv4nYOfS8BPJWsOyxx/fvjQIgMzQj/b/ed0KVFmErBS0Py+bg6CfSFquLPAGW9yh1U0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413081}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.5_1697519419166_0.735987689059802", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/sunos-x64", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "b51b648cea77c62b1934a4fdcfee7aaa9de174cb", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-M+XIAnBpaNvaVAhbe3uBXtgWyWynSdlww/JNZws0FlMPSBy+EpatPXNIlKAdtbFVII9OpX91ZfMb17TU3JKTBA==", "signatures": [{"sig": "MEUCIQCAL1dPIrQn//1OEBhpxd+WfE0hxVsqcyzxz9PQQOUSYgIgffNktuFwAvB1W6aa8BPrZ/qtYEJDxLkDFRR9lIbc808=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9433561}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.6_1700377877465_0.003961058830196151", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/sunos-x64", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "1650d40dd88412ecc11490119cd23cbaf661a591", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-JcPvgzf2NN/y6X3UUSqP6jSS06V0DZAV/8q0PjsZyGSXsIGcG110XsdmuWiHM+pno7/mJF6fjH5/vhUz/vA9fw==", "signatures": [{"sig": "MEUCIQDRp2Jzat+d01MOM+ZadRhkWVhWg07ONWcW9fl6fBLJjQIgHZmXFbYA0zTopW2YAIWqQjudGV0Hm4fgmw1AttakITU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9470425}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.7_1700528434119_0.9840870105489143", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/sunos-x64", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2a697e1f77926ff09fcc457d8f29916d6cd48fb1", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-9Lc4s7Oi98GqFA4HzA/W2JHIYfnXbUYgekUP/Sm4BG9sfLjyv6GKKHKKVs83SMicBF2JwAX6A1PuOLMqpD001w==", "signatures": [{"sig": "MEUCIQCEsiX34GPpZJ93pyNqi8FUE2JfqEpDMLctrahb0plVZQIgOQtJSbsB/E9TWVs0N0bVeNTCXBkO7dsMAKCYYpHxiN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9470425}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.8_1701040059857_0.32814556853193255", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/sunos-x64", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "116be6adbd2c7479edeeb5f6ea0441002ab4cb9c", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-MLHj7k9hWh4y1ddkBpvRj2b9NCBhfgBt3VpWbHQnXRedVun/hC7sIyTGDGTfsGuXo4ebik2+3ShjcPbhtFwWDw==", "signatures": [{"sig": "MEUCIFT7T+/FLoB07LwkD7pD2eiEZEjnMdvAst5QRCdY/HVqAiEAsQybyZv+mHCKeOVWdCFFgk5xN2XUU6ov+FdpXdJbQTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9576921}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.9_1702184950873_0.07764216894697928", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/sunos-x64", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c16ee1c167f903eaaa6acf7372bee42d5a89c9bc", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-ZkIOtrRL8SEJjr+VHjmW0znkPs+oJXhlJbNwfI37rvgeMtk3sxOQevXPXjmAPZPigVTncvFqLMd+uV0IBSEzqA==", "signatures": [{"sig": "MEYCIQDHx4l2iG2UFoVwgFZeM32QyXLUhBv4Mfa1bUqmCef6fgIhANqkrTMCMqbN5vs+MAb2U2OM4uLEQI8oi7u/6lWQoQIy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585114}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.10_1702945273517_0.4957210489927184", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/sunos-x64", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "e320636f00bb9f4fdf3a80e548cb743370d41767", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-Hf+Sad9nVwvtxy4DXCZQqLpgmRTQqyFyhT3bZ4F2XlJCjxGmRFF0Shwn9rzhOYRB61w9VMXUkxlBy56dk9JJiQ==", "signatures": [{"sig": "MEUCICjSioCHdiP4sL1vr0CfSXlgeTzqjpihweuDzIjyDp3RAiEAvQbj6y1fEacnO1SLqFCwGZOGFzsfaGgp1GNMBNkOLTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585114}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.11_1703881895961_0.20169644477621862", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/sunos-x64", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/sunos-x64@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "0933eaab9af8b9b2c930236f62aae3fc593faf30", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-HKjJwRrW8uWtCQnQOz9qcU3mUZhTUQvi56Q8DPTLLB+DawoiQdjsYq+j+D3s9I8VFtDr+F9CjgXKKC4ss89IeA==", "signatures": [{"sig": "MEQCICgnOEf0URR2ocC6EUXdHzdKpue3b5VoD0cA5MDkhiCuAiB9tg5Jwvca8TzHjXRf24Zmw6UT9a4RBOtkKHtxN8U5SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585114}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.19.12_1706031596631_0.3436268173622117", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/sunos-x64", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "ea4cd0639bf294ad51bc08ffbb2dac297e9b4706", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-GDwAqgHQm1mVoPppGsoq4WJwT3vhnz/2N62CzhvApFD1eJyTroob30FPpOZabN+FgCjhG+AgcZyOPIkR8dfD7g==", "signatures": [{"sig": "MEQCIHW9m/dF8oVC7GSrXOqmdpCzosLzkb34VsMPeakEaMDcAiAc1cfbbL+r2V31JPbik21JEq4WYVU8O2yliAzqRnz8UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9585157}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.20.0_1706374155435_0.8135516518889183", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/sunos-x64", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "402a441cdac2eee98d8be378c7bc23e00c1861c5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-ZnWEyCM0G1Ex6JtsygvC3KUUrlDXqOihw8RicRuQAzw+c4f1D66YlPNNV3rkjVW90zXVsHwZYWbJh3v+oQFM9Q==", "signatures": [{"sig": "MEUCIQDlPh6NnQx9j4H3jDizFsgxPkMqsAbmfB2ljG4ZRS+ziwIgD0qImkl4HuifiISCP+QTT5WCyyFfV9PBc39MgkoU0NQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9597445}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.20.1_1708324665123_0.0300732344202157", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/sunos-x64", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "7df23b61a497b8ac189def6e25a95673caedb03f", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-2UyFtRC6cXLyejf/YEld4Hajo7UHILetzE1vsRcGL3earZEW77JxrFjH4Ez2qaTiEfMgAXxfAZCm1fvM/G/o8w==", "signatures": [{"sig": "MEUCIQCQr0W1gXLO2S/oKmCl7EGRAAaTW9T38VM15y/EISxbFgIgZvtU3hMl+xNUuolyfJ3J5cwL+IxIt7m9NrEGYPGasp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9601541}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.20.2_1710445762633_0.4871010609540971", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/sunos-x64", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "c77ce1d31d5fd3827da9d0634a0f2673611c6254", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-3qAZFC752nZZQOI+OG4KIawvLfdD5yMFCeIFz0OhedMpYgq9AOKygW45Ojy0E5upBqns2fUaMFk1CnNSkvJaYw==", "signatures": [{"sig": "MEUCIDSkoLFv2uZSdN86Eh1GiOLrMA0d14TkVY5TY0peGu6sAiEAj4y+JmoFazZKA/HgS+PleHuJX65dtV3JG4UYc6Vh7AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683461}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.0_1715050330164_0.44285463139635595", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/sunos-x64", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "f53cb1cdcbf05b3320e147ddb85ec2b1cf2b6cfc", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-CWibXszpWys1pYmbr9UiKAkX6x+Sxw8HWtw1dRESK1dLW5fFJ6rMDVw0o8MbadusvVQx1a8xuOxnHXT941Hp1A==", "signatures": [{"sig": "MEUCIQCp7Qd0h2GI6gTAdylreUdwABVuX3973R6dwESQCIg5hwIgPS5pGhuTTya+Ro8Ed3ZCl0APzYpDa2OHVATQUiyezLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9679365}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.1_1715100879625_0.22273189467314736", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/sunos-x64", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "fc7dd917ffcb2ebab4f22728a23ece3dd36c2979", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-90r3nTBLgdIgD4FCVV9+cR6Hq2Dzs319icVsln+NTmTVwffWcCqXGml8rAoocHuJ85kZK36DCteii96ba/PX8g==", "signatures": [{"sig": "MEUCIAcaobi2xUsTxvv0gDfHCpj5AVaG/nSODJs4p1vr5907AiEAvCnKgMMj6qgdHERMofgrLN6lTUEc2nzA7qYdAjsyf/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683461}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.2_1715545963542_0.8178303393994748", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/sunos-x64", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a1f7f98b85bd221fe0f545d01abc0e6123ae60dc", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-W8H9jlGiSBomkgmouaRoTXo49j4w4Kfbl6I1bIdO/vT0+0u4f20ko3ELzV3hPI6XV6JNBVX+8BC+ajHkvffIJA==", "signatures": [{"sig": "MEQCIAbK9/KhmibvMQnqyXkvCrp2d9HYmaVLDzf1zxq5Xp81AiAm3+pE2OcEnS4ItB79vEa4/N7OxDBBba6z3yKbF5EkmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9679365}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.3_1715806335599_0.17695015573333883", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/sunos-x64", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "8d838a8ac80e211536490108b72fb0091a811626", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-hsKhgZ4teLUaDA6FG/QIu2q0rI6I36tZVfM4DBZv3BG0mkMIdEnMbhc4xwLvLJSS22uWmaVkFkqWgIS0gPIm+A==", "signatures": [{"sig": "MEYCIQCtORJ//i/xhDgwVyhDs7arp/csTMrY+jhVRmdLQ7ktxwIhAIZcRaVPOZlMT36VZPREbwHuK3lvpMu12oTtirInRA89", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9683461}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.4_1716603038442_0.568434892658022", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/sunos-x64", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/sunos-x64@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "08741512c10d529566baba837b4fe052c8f3487b", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==", "signatures": [{"sig": "MEUCIQCHeAH1I1oUiIS8gQaUIaqBCI+xYr6/qO+DtsPmJxYkwAIgW8uoJNKix//X5EMuO+g/8nyD5LGXPJURBDtDeNeyEWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9691653}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.21.5_1717967803949_0.24702344088270367", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/sunos-x64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "2be9d2459ae181ebedb6470e4469349a27c4f060", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-zY6ly/AoSmKnmNTowDJsK5ehra153/5ZhqxNLfq9NRsTTltetr+yHHcQ4RW7QDqw4JC8A1uC1YmeSfK9NRcK1w==", "signatures": [{"sig": "MEUCIQC96aik5Lj6Bvg9R3ta4yh7PvAEa0P4bQJ6z1Lfhb5oUQIgUSYX5o33cctX4ZxcOVZLiVx9RGrHlHWXRPXUr9SWeT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9949961}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.22.0_1719779906031_0.6131281574061613", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/sunos-x64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "58f0d5e55b9b21a086bfafaa29f62a3eb3470ad8", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-BFelBGfrBwk6LVrmFzCq1u1dZbG4zy/Kp93w2+y83Q5UGYF1d8sCzeLI9NXjKyujjBBniQa8R8PzLFAUrSM9OA==", "signatures": [{"sig": "MEQCIEk5gxZ6w4i/Tf2UqKWzg2ixIbiPm0j20IcYwRPCiYLjAiAPN4w98pwEqBI+Ls8AExi2ADjwvGiZ9EFxWFNgPMMyOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9949961}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.23.0_1719891259597_0.2940797135909361", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/sunos-x64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "adb022b959d18d3389ac70769cef5a03d3abd403", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-RBRT2gqEl0IKQABT4XTj78tpk9v7ehp+mazn2HbUeZl1YMdaGAQqhapjGTCe7uw7y0frDi4gS0uHzhvpFuI1sA==", "signatures": [{"sig": "MEUCIBIwK56JgCGHNitFFcpgIxAPFpJi/YcCZ7wg98gyg7TBAiEAo7doexgcKLxbTcreQdQGOdtFM6ovVdWkcLxfSkuMJPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9949961}, "engines": {"node": ">=18"}, "gitHead": "****************************************", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.23.1_1723846433465_0.6086191049112413", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/sunos-x64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "8ddc35a0ea38575fa44eda30a5ee01ae2fa54dd4", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-jVzdzsbM5xrotH+W5f1s+JtUy1UWgjU0Cf4wMvffTB8m6wP5/kx0KiaLHlbJO+dMgtxKV8RQ/JvtlFcdZ1zCPA==", "signatures": [{"sig": "MEUCIEtQgfRn9S3dSkL/HCWqiXBqu90wGhhXF1HHW6519/wWAiEAwim5D8+p9xyJ54LFaDIfXCbF2Rol6XxXd7RKno4KVn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10167049}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.24.0_1726970820411_0.863140334862067", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/sunos-x64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "a6e3b3ff72ab74555626a2f44e27ce300f6a2d5e", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-a0VfBsFPrlFKxzXuJ4nP0ia3jEbzBk/JW2wEW44dwr0RDOr/Y1+d+EJgT6L3h8y9X8ctig7ks0rWlbjkPn6PcA==", "signatures": [{"sig": "MEUCIQDSGfz+cWthSVtHExjAxaODIAvahWEe+iPMkifoshpolgIgbrXU1XCjlfoQ9v8u9KD8XxtIl6tkAA4jsOb5yLQCJuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10175241}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.24.1_1734673333970_0.1321319767307232", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/sunos-x64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "8b7aa895e07828d36c422a4404cc2ecf27fb15c6", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-hTdsW27jcktEvpwNHJU4ZwWFGkz2zRJUz8pvddmXPtXDzVKTTINmlmga3ZzwcuMpUvLw7JkLy9QLKyGpD2Yxig==", "signatures": [{"sig": "MEQCIHHaNlPbvAyA438OTbh5CHNLxGFThgGVTFscocE73x6kAiBpfUDQPdDMfs+jtj+Ru1ZBgaCVRZttEwqEMKlTR+rbyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10175241}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.24.2_1734717453998_0.06966588091369652", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/sunos-x64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/sunos-x64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "86ff9075d77962b60dd26203d7352f92684c8c92", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-bxI7ThgLzPrPz484/S9jLlvUAHYMzy6I0XiU1ZMeAEOBcS0VePBFxh1JjTQt3Xiat5b6Oh4x7UC7IwKQKIJRIg==", "signatures": [{"sig": "MEQCIEltwOavCWkZuxr2Yl0EdqyY7+zkEBCcRFZWG3rvONPtAiAKJ8/AYl6vWP5lpfArNfmMoMsB5kY5O+t/DEGpEcKPuA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10232585}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.25.0_1738983779351_0.9151934836874591", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/sunos-x64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/sunos-x64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "89aac24a4b4115959b3f790192cf130396696c27", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-2H3RUvcmULO7dIE5EWJH8eubZAI4xw54H1ilJnRNZdeo8dTADEZ21w6J22XBkXqGJbe0+wnNJtw3UXRoLJnFEg==", "signatures": [{"sig": "MEYCIQDFHhW3XdjRvFBIWf8w+PCisqxnwmU5To40xdLjJRKbQAIhAMkjhvaheioDA98pTYQaS9yAN/E5bJizjTfIgEGfAOnY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10236681}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.25.1_1741578373546_0.5102427965246954", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/sunos-x64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/sunos-x64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "49b437ed63fe333b92137b7a0c65a65852031afb", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-cfZH1co2+imVdWCjd+D1gf9NjkchVhhdpgb1q5y6Hcv9TP6Zi9ZG/beI3ig8TvwT9lH9dlxLq5MQBBgwuj4xvA==", "signatures": [{"sig": "MEUCIQCLJlbPE94X2AVdGMQS70gefIhl9aMeBWQCuobW3mpXnAIgWVWOGlYlRYHHb+uuK8UJZ1JLfLzTUk+xUa2MfXU/BQg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10240777}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.25.2_1743356022323_0.004430748671030882", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/sunos-x64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/sunos-x64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "03765eb6d4214ff27e5230af779e80790d1ee09f", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-R<PERSON><PERSON>hm7d8bk9dMCUZjkS8fgzsPAZEjtRJqCAmVgB0gMrvG7hfmPmz9k1rwO4jSiblFjYmNvbECL9uhaPzONMfgA==", "signatures": [{"sig": "MEQCICIpniuSH6Tq+agVhIzc5BBHCXkg6MvNXtEpJQL+/YLIAiAdutmtBWUNo9FsOXr+JlpZ9dMM5SrsdACdjTY2dB+PLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10248969}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.25.3_1745380627054_0.3732236228899568", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/sunos-x64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/sunos-x64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["sunos"], "cpu": ["x64"], "dist": {"shasum": "cd596fa65a67b3b7adc5ecd52d9f5733832e1abd", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-Mw+tzy4pp6wZEK0+Lwr76pWLjrtjmJyUB23tHKqEDP74R3q95luY/bXqXZeYl4NYlvwOqoRKlInQialgCKy67Q==", "signatures": [{"sig": "MEQCIDwdi6q3QtAtm2TV8pI7UXHii8x3v3GtClYh80lKh9j1AiBfkVFFjqzsHkcwqmyt1yh5Hc5tjTyyWRlX///FFsdUUQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10253065}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/sunos-x64_0.25.4_1746491489248_0.3300985225473825", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/sunos-x64", "version": "0.25.5", "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["sunos"], "cpu": ["x64"], "_id": "@esbuild/sunos-x64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==", "shasum": "a28164f5b997e8247d407e36c90d3fd5ddbe0dc5", "tarball": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 10257161, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDOZ55b+ykmBdvQxtedl9l3RoBF5/lwVSaM/qFGEhazyAIgZXDPTR69xO1eQ37H5dSeka2CnONGROutz1p8deGW5pM="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/sunos-x64_0.25.5_1748315620124_0.3866071790055907"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:58:00.183Z", "modified": "2025-05-27T03:13:40.543Z", "0.15.18": "2022-12-05T23:58:00.625Z", "0.16.0": "2022-12-07T03:54:45.317Z", "0.16.1": "2022-12-07T04:47:58.219Z", "0.16.2": "2022-12-08T06:59:33.817Z", "0.16.3": "2022-12-08T20:12:34.416Z", "0.16.4": "2022-12-10T03:50:16.518Z", "0.16.5": "2022-12-13T17:47:28.822Z", "0.16.6": "2022-12-14T05:22:58.827Z", "0.16.7": "2022-12-14T22:46:43.724Z", "0.16.8": "2022-12-16T23:38:20.821Z", "0.16.9": "2022-12-18T04:31:08.069Z", "0.16.10": "2022-12-19T23:26:16.244Z", "0.16.11": "2022-12-27T01:38:48.505Z", "0.16.12": "2022-12-28T02:04:32.416Z", "0.16.13": "2023-01-02T22:57:08.592Z", "0.16.14": "2023-01-04T20:12:42.863Z", "0.16.15": "2023-01-07T04:18:23.238Z", "0.16.16": "2023-01-08T22:43:37.724Z", "0.16.17": "2023-01-11T21:57:44.121Z", "0.17.0": "2023-01-14T04:33:25.375Z", "0.17.1": "2023-01-16T18:05:22.719Z", "0.17.2": "2023-01-17T06:39:30.439Z", "0.17.3": "2023-01-18T19:14:10.996Z", "0.17.4": "2023-01-22T06:13:27.164Z", "0.17.5": "2023-01-27T16:37:31.070Z", "0.17.6": "2023-02-06T17:00:30.542Z", "0.17.7": "2023-02-09T22:26:32.028Z", "0.17.8": "2023-02-13T06:35:30.456Z", "0.17.9": "2023-02-19T17:45:07.465Z", "0.17.10": "2023-02-20T17:54:40.929Z", "0.17.11": "2023-03-03T22:39:54.208Z", "0.17.12": "2023-03-17T06:16:10.611Z", "0.17.13": "2023-03-24T18:56:52.639Z", "0.17.14": "2023-03-26T02:47:26.273Z", "0.17.15": "2023-04-01T22:26:33.347Z", "0.17.16": "2023-04-10T04:34:47.684Z", "0.17.17": "2023-04-16T21:23:22.312Z", "0.17.18": "2023-04-22T20:41:11.824Z", "0.17.19": "2023-05-13T00:06:15.917Z", "0.18.0": "2023-06-09T21:24:08.436Z", "0.18.1": "2023-06-12T04:51:34.161Z", "0.18.2": "2023-06-13T02:40:11.934Z", "0.18.3": "2023-06-15T12:20:39.030Z", "0.18.4": "2023-06-16T15:37:39.774Z", "0.18.5": "2023-06-20T00:52:20.840Z", "0.18.6": "2023-06-20T23:24:40.437Z", "0.18.7": "2023-06-24T02:46:12.885Z", "0.18.8": "2023-06-25T03:18:56.382Z", "0.18.9": "2023-06-26T05:27:40.363Z", "0.18.10": "2023-06-26T21:20:15.710Z", "0.18.11": "2023-07-01T06:03:41.159Z", "0.18.12": "2023-07-13T01:33:49.264Z", "0.18.13": "2023-07-15T02:36:59.309Z", "0.18.14": "2023-07-18T05:00:05.358Z", "0.18.15": "2023-07-20T12:53:01.756Z", "0.18.16": "2023-07-23T04:47:47.182Z", "0.18.17": "2023-07-26T01:40:40.529Z", "0.18.18": "2023-08-05T17:06:15.545Z", "0.18.19": "2023-08-07T02:51:03.766Z", "0.18.20": "2023-08-08T04:14:38.788Z", "0.19.0": "2023-08-08T15:51:10.092Z", "0.19.1": "2023-08-11T15:57:15.308Z", "0.19.2": "2023-08-14T01:58:07.606Z", "0.19.3": "2023-09-14T01:12:10.058Z", "0.19.4": "2023-09-28T01:46:31.884Z", "0.19.5": "2023-10-17T05:10:19.385Z", "0.19.6": "2023-11-19T07:11:17.797Z", "0.19.7": "2023-11-21T01:00:34.401Z", "0.19.8": "2023-11-26T23:07:40.191Z", "0.19.9": "2023-12-10T05:09:11.185Z", "0.19.10": "2023-12-19T00:21:13.898Z", "0.19.11": "2023-12-29T20:31:36.214Z", "0.19.12": "2024-01-23T17:39:56.847Z", "0.20.0": "2024-01-27T16:49:15.690Z", "0.20.1": "2024-02-19T06:37:45.395Z", "0.20.2": "2024-03-14T19:49:22.932Z", "0.21.0": "2024-05-07T02:52:10.446Z", "0.21.1": "2024-05-07T16:54:39.832Z", "0.21.2": "2024-05-12T20:32:43.757Z", "0.21.3": "2024-05-15T20:52:15.891Z", "0.21.4": "2024-05-25T02:10:38.659Z", "0.21.5": "2024-06-09T21:16:44.204Z", "0.22.0": "2024-06-30T20:38:26.319Z", "0.23.0": "2024-07-02T03:34:19.896Z", "0.23.1": "2024-08-16T22:13:53.720Z", "0.24.0": "2024-09-22T02:07:00.724Z", "0.24.1": "2024-12-20T05:42:14.265Z", "0.24.2": "2024-12-20T17:57:34.319Z", "0.25.0": "2025-02-08T03:02:59.573Z", "0.25.1": "2025-03-10T03:46:13.815Z", "0.25.2": "2025-03-30T17:33:42.587Z", "0.25.3": "2025-04-23T03:57:07.256Z", "0.25.4": "2025-05-06T00:31:29.640Z", "0.25.5": "2025-05-27T03:13:40.366Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The illumos 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the illumos 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}