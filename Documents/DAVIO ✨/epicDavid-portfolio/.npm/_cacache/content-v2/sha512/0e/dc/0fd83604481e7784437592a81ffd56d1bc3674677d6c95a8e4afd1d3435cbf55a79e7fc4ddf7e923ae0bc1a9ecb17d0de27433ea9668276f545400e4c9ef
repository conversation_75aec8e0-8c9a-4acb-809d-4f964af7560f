{"_id": "run-parallel", "_rev": "50-daf9a027e86222812ff461a28f45d2af", "name": "run-parallel", "dist-tags": {"latest": "1.2.0"}, "versions": {"0.1.0": {"name": "run-parallel", "version": "0.1.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@0.1.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "e947a17a3bffae5a088f8d299b3f4f446b63e0c4", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.1.0.tgz", "integrity": "sha512-zwyw1YEOguOQVFzto6FTx1PdeisMD2L5sEMfG5dzgcrEejaYUnNsSQ6OkaILDZ6tqzZRatc8cSnS1gC1PSwJHw==", "signatures": [{"sig": "MEYCIQDwx24jFePMAWcoWlBSNSrYyAk9973b5LWWjjMaGS5NlAIhAJZz6c0Gr5A+GUN2rLDV3Nme5EC2azNbY6EYXAqoEv+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "firefox/20..latest", "safari/6..latest", "opera/15.0..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Run an array of functions in parallel", "directories": {}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}}, "0.1.1": {"name": "run-parallel", "version": "0.1.1", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@0.1.1", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "17e393e7387f0a35e187a45b527549deea7307d3", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.1.1.tgz", "integrity": "sha512-6fNlVhrwHZyJwioEaZ5GWTXNXh72qnTeDe7XLiUAr7jJ0V/c8u6KtyaoiUxhm+4kU2wgnTtjIm7Zwryn63iANw==", "signatures": [{"sig": "MEQCIC6Xofi9MbMcz3Zq2PCuRxPwAbBooXJ4XdPWaF3WF71+AiAAgiSN1A35mImcvLN5mEZ6F9wUHCAJUTceFflEeJS9gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "firefox/20..latest", "safari/6..latest", "opera/15.0..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Run an array of functions in parallel", "directories": {}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}}, "0.2.0": {"name": "run-parallel", "version": "0.2.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@0.2.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "5d018e89691c1af8bf150973b9a1f22e4cd04797", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.2.0.tgz", "integrity": "sha512-E7MJk7bwP3KPfq1ldjb2Jh4ULV7OO/a23IpdqegklThUEwmBDIK+gDxXqBoMBwgjNHh0uItUwUf6da51V9gpWw==", "signatures": [{"sig": "MEQCIE3drMfHFSrNdT3l9nSZQ8MT5GpS9P8N1W3JUXJ9SMGUAiBItWCDRPuZz2FED7vCsWOfYmqmfVY2hIjiGJVZDZvZ0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "chrome/canary", "firefox/20..latest", "firefox/nightly", "safari/6..latest", "opera/12..latest", "opera/next", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Run an array of functions in parallel", "directories": {}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}}, "0.3.0": {"name": "run-parallel", "version": "0.3.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@0.3.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "2912228d34e1e370c70b9f794036de1c3eeae715", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.3.0.tgz", "integrity": "sha512-fYp5n5QFbxsSmGtq+e//fM3pKw0q+axkoGh3VH7REBMAP2pLpU66VHerQz/nReJbiIjNQVpfDfI5VSO15G43fQ==", "signatures": [{"sig": "MEYCIQClKlmBdXr+KY9Hmx3deb6dZg3uUnwoBu284XsUneolSwIhALjOzgNU4VdFuz7LOvHexWsui7Omks4Vd0rr4WoNBWm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "chrome/canary", "firefox/20..latest", "firefox/nightly", "safari/6..latest", "opera/12..latest", "opera/next", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Run an array of functions in parallel", "directories": {}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}}, "1.0.0": {"name": "run-parallel", "version": "1.0.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.0.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "1dd0787f994d17f8d1bbe7e910e0cc3036800cbc", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.0.0.tgz", "integrity": "sha512-ye3bjudEvqU0nQxz/xM6Mz+RU8uo+pm8vdTD3AwuAQg+/hF1fWV76GVbXjt1wz9hFm8uysmZITNf+UTunc/m3A==", "signatures": [{"sig": "MEQCICWgZzsj7TE691oGIg2DB1aYV5w4/m4Et4RX+uRfIceRAiBslzag5q2Sh+5O2LyLeYre5KV/ZSlGxuTWTOy1i3UHZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "chrome/canary", "firefox/20..latest", "firefox/nightly", "safari/6..latest", "opera/12..latest", "opera/next", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Run an array of functions in parallel", "directories": {}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}}, "1.1.0": {"name": "run-parallel", "version": "1.1.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "6c51c3b7e06400a39f38e34aa1e76463a015e67f", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.0.tgz", "integrity": "sha512-4cDIfL6b98ijT6xlVcPFXNuqF2ZWWZ6xuPrK+dMvqSxv1/Mj+OncDXWnAl2+O9gnLmormLIsLvfhLs/c0e1v9Q==", "signatures": [{"sig": "MEQCIEZWKp4A454ergL7p2l3wCPuoneCz+QHBRFjUrogyqYzAiBneLsGr9t3x9jq5m3AeszyxKsW6xyyA/uv0ucQ8NYN9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6c51c3b7e06400a39f38e34aa1e76463a015e67f", "gitHead": "e456882920f3ee2bcf8ba669ec4a8fd579630e57", "scripts": {"test": "standard && tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "chrome/canary", "firefox/20..latest", "firefox/nightly", "safari/6..latest", "opera/12..latest", "opera/next", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.7.0", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "1.5.1", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^2.12.3", "standard": "^3.2.0"}}, "1.1.1": {"name": "run-parallel", "version": "1.1.1", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.1", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "043c1f40e5ea94485f6858b79c6ca08254d0720e", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.1.tgz", "integrity": "sha512-5IdyV87k+RyYENG9GClriqfCUh6IlV2//vWpFjSYVRdUe8jB+0B4W/79znBYqlPSnYh/N0cABrRNZhgPFhr4kQ==", "signatures": [{"sig": "MEQCIHN4mtzj1cPGOyJjrejoyBNqotQFV2pPb0tr+rbOf8sjAiBtaAUAWoBd2EoWSNlkj4wBrDhOzJEVJe54O4X4AhSA/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "043c1f40e5ea94485f6858b79c6ca08254d0720e", "gitHead": "5a58a11b8a33c03ed6a8d865670d0a0fb22e2def", "scripts": {"test": "standard && tape test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/25..latest", "chrome/canary", "firefox/20..latest", "firefox/nightly", "safari/6..latest", "opera/12..latest", "opera/next", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "standard": "^3.2.0"}}, "1.1.2": {"name": "run-parallel", "version": "1.1.2", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.2", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "199a63b1bbf76dbce26f3f5499e5e4f993dae8c6", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.2.tgz", "integrity": "sha512-ffdoX/IydCSRCkgctu8EYTuoaX1iY7TpQ2bXIHsADV92Vyk6JroqOJhjk8BXFMfMvj/TpjsxEzFB5h+lRMN8Qw==", "signatures": [{"sig": "MEUCIQCZ/zyFm6B79vxPcw+YtHyVcl29K/DeK6pq4A81ik2aGwIgboTLaO4nyZhVLbHYVnxkKB4dLUpoVB0niFhn8sPo/cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "199a63b1bbf76dbce26f3f5499e5e4f993dae8c6", "gitHead": "4b1ae3c9972604852d3484a2303cf68d50d9db00", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "2.3.1", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}}, "1.1.3": {"name": "run-parallel", "version": "1.1.3", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.3", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "c8ffa1a42bc05d4b526878382f6860a1baef629b", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.3.tgz", "integrity": "sha512-9+FJGKKcZFJgKct0f/1MCU6sTkNoq80G5rPfGMmRgUSZserzji4hdIrRMmFKXh8sDDtuYeNRnNf+i0o2WzrLPg==", "signatures": [{"sig": "MEUCIQDjAalHyVgIi9hp/iuAHx0nRL6j0gyOe4W6cCsRMio3zwIgVj92UkwURuzqO6+zg0qbwxm4Tvo/hOsaNNEKuBc96pM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c8ffa1a42bc05d4b526878382f6860a1baef629b", "gitHead": "5012c842f9e476debe856ad07f80e88428b3bf7c", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}}, "1.1.4": {"name": "run-parallel", "version": "1.1.4", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.4", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "b4fc05ab632a5e767bdd691b4aabe7b8acc9abb2", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.4.tgz", "integrity": "sha512-LTel3I9NB0BJuhORiQRA/K5IuA25bgW1s50hA9miB0eHjcIpKaAjEtXjCDrTBowP8a+zrfQC8Dginygkp3GEDw==", "signatures": [{"sig": "MEYCIQCLiq+SYWk96qc2GyFXey1vqcZ9c90yT3jW5fDp0/0c2AIhAMGKJVnF7G8yqvGK5aTNK0R5r1KHmVNaeLKdKXSI48QS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b4fc05ab632a5e767bdd691b4aabe7b8acc9abb2", "gitHead": "259a254dfb7788c1a022093e484d19cae0a91591", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}}, "1.1.5": {"name": "run-parallel", "version": "1.1.5", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.5", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "feba6ee55e0941ad7925ae520af633c43ad2c52c", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.5.tgz", "integrity": "sha512-KHFkb3BoRAhWrtSyHNOMebDmzTITDgMx+CDtO4uAT/O5Omr4hHxHaJSU1XaQKim+2hhkKi2NejPCdpkJBy/Dlg==", "signatures": [{"sig": "MEYCIQCBJkKOk1B8UmSRdMy+gtW4lMHT28uMyYbJXdqJyPA7ZAIhANPk4CySlfpekTxelTLuivOzPbhJTX4qQukaHSDe33Kj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "feba6ee55e0941ad7925ae520af633c43ad2c52c", "gitHead": "066b249755c7a81275e0cb722abe3bf00891aedf", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "4.3.2", "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^6.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel-1.1.5.tgz_1457663877572_0.2544198150280863", "host": "packages-13-west.internal.npmjs.com"}}, "1.1.6": {"name": "run-parallel", "version": "1.1.6", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.6", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "29003c9a2163e01e2d2dfc90575f2c6c1d61a039", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.6.tgz", "integrity": "sha512-XmkpA2hWubG33ZcSAtFujmcsg4dPy9u1nwB0AtuHNEVF6SoDpZO2ldD24PS0Z5l8/C/sdaztIqPpeLzMugG7/A==", "signatures": [{"sig": "MEUCIGpVjhe7Twf7VBNJPs6oTH5LrRrhVcOztOHSxSu7CgHeAiEA7FvglzxiTRLUy6Tlz5Y6UIKIk85YV3DIgrvBmF5NUWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "29003c9a2163e01e2d2dfc90575f2c6c1d61a039", "gitHead": "f9ee5e836569ec13712889a208a0a730069e22af", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^6.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel-1.1.6.tgz_1459234996201_0.7449365314096212", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.7": {"name": "run-parallel", "version": "1.1.7", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "https://feross.org", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.7", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "d8f40854b9e19d18c2e0e70180cc05cfc86b650f", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.7.tgz", "fileCount": 11, "integrity": "sha512-nB641a6enJOh0fdsFHR9SiVCiOlAyjMplImDdjV3kWCzJZw9rwzvGwmpGuPmfX//Yxblh0pkzPcFcxA81iwmxA==", "signatures": [{"sig": "MEYCIQDP1E5XTLEGJH9D1IUA5/F4Otg5MdbmuFzyAt8Wq+qUDAIhAJS84PhfKqDhd/hAi8n+JqHbbHEHh0Yu0d4+ILVYQG2F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11231}, "main": "index.js", "gitHead": "96e96f5123a6e5549e41066c8eaa73cf81bab04e", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "*"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel_1.1.7_1518577098220_0.8462576925327134", "host": "s3://npm-registry-packages"}}, "1.1.8": {"name": "run-parallel", "version": "1.1.8", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.8", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "70e4e788f13a1ad9603254f6a2277f3843a5845c", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-e5t1NVhr5VWmD9V9U4KjjSGkf5w6CcTPgw11A3CfIvkkQxlAKzX3usPUp1NUQTmpOOjU+f9QRICU3tMbKwn9ZQ==", "signatures": [{"sig": "MEYCIQCcagpOgMx2LknBN20lyDERNSfdEfXwZJuWm68AwivQhgIhAO60NsJ6vghsAGP9yXUMDpF9WDoGqz2huvR2ATL7jBXn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10563}, "main": "index.js", "gitHead": "7cef4073cc786cec37cc039c59e34cb826a66388", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.0.0", "airtap": "0.0.4", "standard": "*"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel_1.1.8_1521286836627_0.9694094734834844", "host": "s3://npm-registry-packages"}}, "1.1.9": {"name": "run-parallel", "version": "1.1.9", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "http://feross.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.9", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.9.tgz", "fileCount": 4, "integrity": "sha512-DEqnSRTDw/Tc3FXf49zedI638Z9onwUotBMiUFKmrO2sdFKIbXamXGQ3Axd4qgphxKB4kw/qP1w5kTxnfU1B9Q==", "signatures": [{"sig": "MEUCIQCI+sAPSevsnh6ATUcYJ2ZPks9eSkag3Tttfwo3mDqSrQIgfEyB6k3AoBiBd/bNKfk1C5IfXEIJLSq9CChMCeX4WhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4ScfCRA9TVsSAnZWagAALFgP/1+INLnEF7wZhzCZYGgt\nAv4wmMOi+6e17OSm7MBketwTxmGs74yxTi/DGHAH7qNwy9s2lKbJMcLTG7Qz\nFfinjAr1E5LFA7fAr1qeexRxcMSEMKlOPm4V4dDx6WmW9JpJVSQ4LDUFhG1C\nJFrlpxjB6ppQ0sqE8UGq6Y2MiuLp1A9mwn3/9wkZ8rka/HxOO/nMIsaNrupE\nkchduhq33CWJwvOEXFEQlzgn+gthfQHpYvryNF0GEODIv+TI9dvXK9BZaTAN\n0cq/M4JGIoSFy4p1rCjus12mWrJSs1rRpMfSp3rjntZjGDJWS0KBdkDLtcfn\nSA59J3S2WcJ6S6JeMqa16siSj6egY3b7J8TIkuUay+TcaYT9SjDK4AmmE5wN\newLKNnNu3O+sHXFw29IDMlSVPebimBtbkc1SElfNc5U9MeE+tKroXhQ4NYcs\nUz3gJR+GHt2eU4+EWS2UlxNbJHJ+DVOFF8QmJoT3Y7hopaqHrYYdLhYDCwl6\nU6AAVIt10Us+5nGZS0WmGrw/P+QQL30I7p58UFmJipg9PZNaEIgJRBApynky\nDMkDE6ICS82Pu/jv3d7enWE42GMgdc7mh+cp8ZQHkqWAD5YxGoWRIJgt3wAx\nky+bAmtR1mV8ZoqiCaDf+EAPXazquX/8SHzad41n3RxyB2qktQbYCDE2Z3z6\naS8I\r\n=A5eY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2a6d9dc8d18ea24a413bd5bc949d2ed3a22f6c7b", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.0.0", "airtap": "0.0.4", "standard": "*"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel_1.1.9_1524705054493_0.12399995551668175", "host": "s3://npm-registry-packages"}}, "1.1.10": {"name": "run-parallel", "version": "1.1.10", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "https://feross.org", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.1.10", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "60a51b2ae836636c81377df16cb107351bcd13ef", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.10.tgz", "fileCount": 4, "integrity": "sha512-zb/1OuZ6flOlH6tQyMPUrE3x3Ulxjlo9WIVXR4yVYi4H9UXQaeIsPbLn2R3O3vQCnDKkAl2qHiuocKKX4Tz/Sw==", "signatures": [{"sig": "MEUCIQDKWqR2lnCiRDkDymfFpFnwuhQOXTC1J9/whLILvjDQSQIgQPT43vYlxB4GjhSpjQXG2AHimk8xBG7PZl4CtukkPj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfl5a2CRA9TVsSAnZWagAA5o0P/Ai6QVS0yTu5gcSlMs3O\nlOPX4izXHJkvgiLiWngcjct+m7n8fYxV631L2RPRFz2DnJgszlq5/l4/3jNe\nkLHSYMhVXD2r7HEPsVyuR6THf+X6LCssllAu7VN3buhhQGDZqO32vU91+Iuj\n22StAWkKxvCsbkwGiT4hqvdLW/COKrFIevwfoTm5PbTxjOMXYZOGI4K0NQy1\nTXGOeuAEQxf+O6S5xJpBa7rb5e4gyVOfiW+NbadMvu/v2Xx+xh7AIAOXqtFv\n89E1e+vI7YxOcsG8IOsOf4+XYAUttzd036oIUvUhvdBVq7viZrlcVEpByUP4\nnNzDcntrqrg8xz9mQECcFuH+fu7aHdXrPuPdnMoacGvk2+LuR2/PhB+x3+jG\ngw/ATk3wpk8lI0b68opRRSb7GLISCUaDhrlhWSmwbslTu7MnIta+WWX3LdSq\nMTVXJS0NGuXNnLZGBWdrvcrfL93RCBKlqolfs8COG88bmFqXoOPGrvoeRYvt\n2q6hfqG1VlPyfb8PLDjm1k09EfQlPjOfgblm5zhS8EmSZJhC+Mak/bgxNxwl\nF6n5eml5PTtst0y2nwsKSu2lZigfQkL4jo7VbSzPbZSq6KQo0fXLzETHSK4k\nLURrYjswlQ2tIcP15m2+OpOye0BhEIZdoavtbDgpF045C+r48GbOaMGkgncj\nJR6o\r\n=xfxk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": [{"url": "https://github.com/sponsors/feross", "type": "github"}, {"url": "https://www.patreon.com/feross", "type": "patreon"}, {"url": "https://feross.org/support", "type": "consulting"}], "gitHead": "bc2229d932eaa2133fe1eb2ea309d33e905e5dd9", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.0.1", "airtap": "^3.0.0", "standard": "*"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel_1.1.10_1603770037829_0.5038677423250195", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "run-parallel", "version": "1.2.0", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "author": {"url": "https://feross.org", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "run-parallel@1.2.0", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/feross/run-parallel", "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "dist": {"shasum": "66d1368da7bdf921eb9d95bd1a9229e7f21a43ee", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "fileCount": 4, "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "signatures": [{"sig": "MEUCIQCgDvi2EAyyPczvNERNPxsJKEqi5qRckS+/St3IWvlt/AIgSoKR0VNW7NejbLX+LOEMP4zuiZxRhph0kvq0UrVime8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI0vACRA9TVsSAnZWagAAYFIQAJv2prhLf/SBLhAXo/8h\n4if5q/RPTJqUXPrB+Egulc3R88y1da4SYM7QmObhlb8v0uSAMDH2JNFB+tPm\nPxpruymc+bfpzXVaxM5qkNnNo5srghc5P5RyJi6E98T4cvbg8V0RlFMOMBCl\nWuVl8W6FLDsezAI4H0IibYptzph3xy7Ptq1I9iaKAQT/UIaDi+DmtfY3jvjQ\nlfdsG4zf5kY1LkmJPmzNNLMp91Q753tUBb2SOaTo8Xe0fDB9l3UjB+yno2Wj\ny+NPpxAuF34l4aXsKHKs8B3n4nehoBSuZlScrXjm14aGokJlO9vONA+9yDR4\n6SHH/4F4Fi0C0Wk3BG7gwA81NnF/FofZxjf6if8Aza+KdyfDllI47NgEvlc3\nf9N8AQVfxDYSnVggXeVRu0gLKSwysgbHKrD3eUVRU+5GL0QlRulVHb0IGq/p\n+zZN5tpErop9ghKI8BTkPWlQAtaQr3mCrQzflroH13E+GdWftMdirn3jMNmd\nL9UvChuu0no97ZtCu2Tq1ItZOtfqHRuFwWTYDb8ZzflhjFpvtm1k6Ko8sXUy\nLGDQLvesDVrNXLTpqLaTinZFSUnXhL2DAUMr50QidhvT/ePR22yb7Gq4d63x\nUasyPiePaqGErq0jGmTg5v7w4oMc5iPD76l/g2Vo82vfK+kfi8qVs1hguTAK\nNxX4\r\n=NyGQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": [{"url": "https://github.com/sponsors/feross", "type": "github"}, {"url": "https://www.patreon.com/feross", "type": "patreon"}, {"url": "https://feross.org/support", "type": "consulting"}], "gitHead": "8e514fee209d56707cb6c232813b3d56280dde3f", "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-node": "tape test/*.js", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js"}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Run an array of functions in parallel", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"queue-microtask": "^1.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.0.1", "airtap": "^3.0.0", "standard": "*"}, "_npmOperationalInternal": {"tmp": "tmp/run-parallel_1.2.0_1612925888022_0.385874538653765", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-04-12T09:25:52.411Z", "modified": "2025-01-02T08:58:07.736Z", "0.1.0": "2014-04-12T09:25:52.411Z", "0.1.1": "2014-04-12T09:32:38.171Z", "0.2.0": "2014-04-13T01:42:33.036Z", "0.3.0": "2014-04-14T05:16:08.258Z", "1.0.0": "2014-04-29T10:20:12.100Z", "1.1.0": "2015-03-19T23:27:44.645Z", "1.1.1": "2015-05-05T00:54:06.698Z", "1.1.2": "2015-06-24T07:34:32.911Z", "1.1.3": "2015-09-22T20:08:20.241Z", "1.1.4": "2015-09-22T20:35:54.230Z", "1.1.5": "2016-03-11T02:37:58.003Z", "1.1.6": "2016-03-29T07:03:16.628Z", "1.1.7": "2018-02-14T02:58:18.278Z", "1.1.8": "2018-03-17T11:40:36.775Z", "1.1.9": "2018-04-26T01:10:54.542Z", "1.1.10": "2020-10-27T03:40:37.986Z", "1.2.0": "2021-02-10T02:58:08.152Z"}, "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "author": {"url": "https://feross.org", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/feross/run-parallel", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "repository": {"url": "git://github.com/feross/run-parallel.git", "type": "git"}, "description": "Run an array of functions in parallel", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "readme": "# run-parallel [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/run-parallel/master.svg\n[travis-url]: https://travis-ci.org/feross/run-parallel\n[npm-image]: https://img.shields.io/npm/v/run-parallel.svg\n[npm-url]: https://npmjs.org/package/run-parallel\n[downloads-image]: https://img.shields.io/npm/dm/run-parallel.svg\n[downloads-url]: https://npmjs.org/package/run-parallel\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n### Run an array of functions in parallel\n\n![parallel](https://raw.githubusercontent.com/feross/run-parallel/master/img.png) [![Sauce Test Status](https://saucelabs.com/browser-matrix/run-parallel.svg)](https://saucelabs.com/u/run-parallel)\n\n### install\n\n```\nnpm install run-parallel\n```\n\n### usage\n\n#### parallel(tasks, [callback])\n\nRun the `tasks` array of functions in parallel, without waiting until the previous\nfunction has completed. If any of the functions pass an error to its callback, the main\n`callback` is immediately called with the value of the error. Once the `tasks` have\ncompleted, the results are passed to the final `callback` as an array.\n\nIt is also possible to use an object instead of an array. Each property will be run as a\nfunction and the results will be passed to the final `callback` as an object instead of\nan array. This can be a more readable way of handling the results.\n\n##### arguments\n\n- `tasks` - An array or object containing functions to run. Each function is passed a\n`callback(err, result)` which it must call on completion with an error `err` (which can\nbe `null`) and an optional `result` value.\n- `callback(err, results)` - An optional callback to run once all the functions have\ncompleted. This function gets a results array (or object) containing all the result\narguments passed to the task callbacks.\n\n##### example\n\n```js\nvar parallel = require('run-parallel')\n\nparallel([\n  function (callback) {\n    setTimeout(function () {\n      callback(null, 'one')\n    }, 200)\n  },\n  function (callback) {\n    setTimeout(function () {\n      callback(null, 'two')\n    }, 100)\n  }\n],\n// optional callback\nfunction (err, results) {\n  // the results array will equal ['one','two'] even though\n  // the second function had a shorter timeout.\n})\n```\n\nThis module is basically equavalent to\n[`async.parallel`](https://github.com/caolan/async#paralleltasks-callback), but it's\nhandy to just have the one function you need instead of the kitchen sink. Modularity!\nEspecially handy if you're serving to the browser and need to reduce your javascript\nbundle size.\n\nWorks great in the browser with [browserify](http://browserify.org/)!\n\n### see also\n\n- [run-auto](https://github.com/feross/run-auto)\n- [run-parallel-limit](https://github.com/feross/run-parallel-limit)\n- [run-series](https://github.com/feross/run-series)\n- [run-waterfall](https://github.com/feross/run-waterfall)\n\n### license\n\nMIT. Copyright (c) [Feross Aboukhadijeh](http://feross.org).\n", "readmeFilename": "README.md", "users": {"bret": true, "meeh": true, "zeke": true, "nak2k": true, "kiinlam": true, "nichoth": true, "dzhou777": true, "koulmomo": true, "maxogden": true, "moimikey": true, "tphummel": true, "tyandell": true, "magicxiao": true, "tehshrike": true, "flumpus-dev": true, "tunnckocore": true, "zhangyaochun": true, "diegorbaquero": true}}