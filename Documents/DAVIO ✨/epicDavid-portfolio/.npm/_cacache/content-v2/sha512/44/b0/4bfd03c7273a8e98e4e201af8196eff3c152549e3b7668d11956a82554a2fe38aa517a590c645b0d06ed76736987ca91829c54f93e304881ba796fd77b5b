{"_id": "mz", "_rev": "108-36eec0c06f2a24ba8e03552119e983b5", "name": "mz", "description": "modernize node.js to current ECMAScript standards", "dist-tags": {"latest": "2.7.0"}, "versions": {"0.1.1": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "dependencies": {"sliced": "0"}, "devDependencies": {"function-name": "0", "bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@0.1.1", "_shasum": "f13b4321284c100f7947fd29dc34b6df288babb6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "f13b4321284c100f7947fd29dc34b6df288babb6", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.1.tgz", "integrity": "sha512-AXKYg6eCfFgCkgLf2yYYZoZAM60xxCgbGsaPUtxOggx0VcNqGyAIN+7S6nhZq1WEcV/NFldcZRIoy181loxR9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEASXRrRg3CDBQ6lz4e2hIyZqvilWO0fQEpV8x2mwgDgIhANBy9kANsZIa9bZQi7RWVLhhMBJ66wCdQFz7+zuD+3aK"}]}, "directories": {}}, "0.1.2": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "dependencies": {"sliced": "0"}, "devDependencies": {"function-name": "0", "bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@0.1.2", "_shasum": "d5d0a076ec280ce4defeae4b9d9346e3703299ff", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d5d0a076ec280ce4defeae4b9d9346e3703299ff", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.2.tgz", "integrity": "sha512-MeKWJ1Si09CPjz4gusjIEZIsA2usW6C4w0J4bJ4sN7vpdtm4awXlwFzDVWw56EvutSDUiEPhsFPrFHONgAT3dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFC8Wtn4Ydi6YlAdB3JBfjqfQtquM6b50gT5uYEgR5oVAiEAkmB3/kZYuMLF1YKi3IdSJ/oiJeLjTglL+KVAk34UB0s="}]}, "directories": {}}, "0.1.3": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "optionalDependencies": {"function-name": "0"}, "devDependencies": {"bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "dependencies": {"function-name": "0"}, "_id": "mz@0.1.3", "_shasum": "521c2fcf64a44280f9d8d90561f277842e6f4246", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "521c2fcf64a44280f9d8d90561f277842e6f4246", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.3.tgz", "integrity": "sha512-QLfLCTD15b2wQe+w/feX+bYk/D1G+7ur56TUxP7X0Y+I2X6mpS7xBGkhpzQFwLlXSX/iYt0RdMtAVsmJbWbcoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFjd332Umay1RciC+u2bmdDmJDV4Qf+F60fmYIXLrL62AiEA2NIBiM07d+/iUiJvJu8sC7z0khDYA9x8bNJUesq0TaQ="}]}, "directories": {}}, "0.1.4": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "optionalDependencies": {"function-name": "0"}, "devDependencies": {"bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "dependencies": {"function-name": "0"}, "_id": "mz@0.1.4", "_shasum": "503174ca58214fe7f2cfa15ad5e50966f909fa80", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "503174ca58214fe7f2cfa15ad5e50966f909fa80", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.4.tgz", "integrity": "sha512-ddiYzrtCQWeETf3rStPeQ8T6Zi1447LxFy3cbepu2SgHyyYgR55a+8jM49V2rx0BichQifN6o6/cvycJznYggg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsOkZjshM7Dq1V8NgcZPxehUYl84LlhnzqrXGO85t3OgIhALm14cdWsN+eqInnsxRjDd7NK1PHwBOg2YS0JmqHv5h1"}]}, "directories": {}}, "0.1.5": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "optionalDependencies": {"function-name": "0"}, "devDependencies": {"bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "dependencies": {"function-name": "0"}, "_id": "mz@0.1.5", "_shasum": "d0bff79012a520941ba2bf831d1ac5ddf5a8b7da", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "d0bff79012a520941ba2bf831d1ac5ddf5a8b7da", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.5.tgz", "integrity": "sha512-S9kMcV5ra9PiUAQMexucmzuiR7+ms4eIStRD+vzLB34BJonBACglwwATIE9PyTZpnXjSB0jXg9tkixPl1CpTIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICw+KkABXVmSOBjujuoXuP6X239eDhoyrIzr+QhZ5ehwAiBJ/7zTXMA5EuoZoW01InuwrTuio1iOwj637COvmku/2Q=="}]}, "directories": {}}, "0.1.6": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "0.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "devDependencies": {"bluebird": "1", "mocha": "1"}, "scripts": {"test": "mocha --harmony --reporter spec --bail; MZ_BLUEBIRD=1 mocha --reporter spec --bail"}, "engines": {"node": ">= 0.10"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@0.1.6", "_shasum": "1c19d293677adb3496adebcfc2fdb009c0aea6c6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1c19d293677adb3496adebcfc2fdb009c0aea6c6", "tarball": "https://registry.npmjs.org/mz/-/mz-0.1.6.tgz", "integrity": "sha512-FpyGFxyEilUdxwgPFelVuUzi4rZdRvmIs7elrVq0hua3QKHACIyCchH9UsZNCK656Aelztv+euUQB8+GYKxx2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+99mU1NPOPhLK12QQhDiRVS16PY3v652LxacdsmIQTAiEA9OSMUITf/u7qgykIlTwSx39vV8Pm/qC7NWmpeOaNAtA="}]}, "directories": {}}, "1.0.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/normalize/mz"}, "devDependencies": {"bluebird": "2", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec --bail"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.0.0", "_shasum": "f9d7e873b8bca8632cafad1ce649c198fe36e952", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "f9d7e873b8bca8632cafad1ce649c198fe36e952", "tarball": "https://registry.npmjs.org/mz/-/mz-1.0.0.tgz", "integrity": "sha512-/XL2I/97qaecLQCYM0JWat7KvdDSQ5hgD2Q0vvHfxRqK4LSqO71NJcNZQU9m+2MIxe9abyS6rYPy7Xh1+k40Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2T6zjUgiWc/I/Fi3YK5RKezTA/9iyyCSQAtDspO0sMAIhAMIi39ZYLaNMb59Z0xiDC/MJLS2+UwIPpaZ+LOujyL2u"}]}, "directories": {}}, "1.0.1": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "gitHead": "e6bd4cb4d0e0b3176638184a38dd54ca9f6f547c", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.0.1", "_shasum": "5ce1d3fe5cb3267c9c3141fb6a070f8d17f215d8", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "5ce1d3fe5cb3267c9c3141fb6a070f8d17f215d8", "tarball": "https://registry.npmjs.org/mz/-/mz-1.0.1.tgz", "integrity": "sha512-COBCDhDP5uB3GR6wp1P/rZaxQH8jDe+aR4pA9f6wv/u5+/l8IZ9RoNf2+eshUBuSrHd6bZYNMS9VR6wCIAK5Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/XTAMlC2p44PBtmsddVgPlDimkqS/bwcXlvmjCddRRAiEA3iC4X8OLvKR8x2/nGXw2T81lb+iaNKHuldUG9mYDBcE="}]}, "directories": {}}, "1.0.2": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "gitHead": "33b8c8483bd1813979da192c75cf318d4fafef8e", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.0.2", "_shasum": "1c861e902ed75527399ca0d95152b9726aea73ac", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "1c861e902ed75527399ca0d95152b9726aea73ac", "tarball": "https://registry.npmjs.org/mz/-/mz-1.0.2.tgz", "integrity": "sha512-nWyh+vweagu5Z3rJ5bQgblEVGkaCqMtoj68mh1rZ2cv3r9gqShcwmq8rC2I57HRS3cxYOFar4tMAXZ5q6tKuMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDowETRjLFLUQJdVu3DwUmUWYz4Q8Hj0vT5YdMl3j8UXAiB7EA/5MVHhEkYgSzA6SD4j0GnYQ1pzcsRJwdGc2gJe1Q=="}]}, "directories": {}}, "1.1.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "gitHead": "1419ae9758289b250f62f4622abc92aafa65b7cf", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.1.0", "_shasum": "fa63f08f20a1f6985cc1d9a33efa8db608cd6b9c", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "fa63f08f20a1f6985cc1d9a33efa8db608cd6b9c", "tarball": "https://registry.npmjs.org/mz/-/mz-1.1.0.tgz", "integrity": "sha512-ZtuzRX3dDY7Rh61KY4hRk4OYFoe3qZ0ZBKcbhHB+kJtnbDeHd+lWqjVikbLsHiT7sRMo3aGtPFdoEKH84by3Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0OgFqytpf3wRa9wdHX7hhd9E2O3iagS/MHt3hhdTrUAiEAoWwZTV4AJK3hchSNX1vkvcSQTr0QQZ5VmrVfsGiisOY="}]}, "directories": {}}, "1.2.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1", "thenify": "2", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["child_process.js", "crypto.js", "dns.js", "fs.js", "zlib.js"], "gitHead": "92fd0779d79cda4fd116aae7fbfb0986b97dcd97", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.2.0", "_shasum": "efc279d54f32f54e6169cf0bed838c22fc417fe8", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.33", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "efc279d54f32f54e6169cf0bed838c22fc417fe8", "tarball": "https://registry.npmjs.org/mz/-/mz-1.2.0.tgz", "integrity": "sha512-PELC8ts0fDU+tAr6HpyZWvshPmXDUkLbk8oqb82Hio0URcPYOAlvNVui28DvO5OW42Ab/7uydDv2kL2gSJjXNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjGG+fJH+UpnkigJAStpro8KJxzjWYo6Q1p2r4ejQXtQIhAJIr0jOL8dfMW3n0KfbKYmLtwt+Q8M3dv7ndnzx+Mx27"}]}, "directories": {}}, "1.2.1": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1", "thenify": "3", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["child_process.js", "crypto.js", "dns.js", "fs.js", "zlib.js"], "gitHead": "b64e55565e3d1f7c42eb155ec3cf5edbcb508977", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.2.1", "_shasum": "a758a8012cb43b59e209596a0e124cfff87c6923", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "a758a8012cb43b59e209596a0e124cfff87c6923", "tarball": "https://registry.npmjs.org/mz/-/mz-1.2.1.tgz", "integrity": "sha512-Gi/7fWHbsV/porPOIYgcXuf+5p1fE3k27eH0JeyOIHz+KBFQm4KnoNrYYeLZ/AY6bvY2pjFiGoS2vboDbip9gA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbvn0VglRwFywiggmPDLE/Qc/OrU67NQyJgczQHRLNFwIgbB7PlR9MMJLVRNk7iXmIhGH03MBybDcp9sOuOeOg+JY="}]}, "directories": {}}, "1.3.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/normalize/mz"}, "dependencies": {"native-or-bluebird": "1", "thenify": "3", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["child_process.js", "crypto.js", "dns.js", "fs.js", "zlib.js"], "gitHead": "dc40fac61dc98743890641b003fe052ca860d949", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz", "_id": "mz@1.3.0", "_shasum": "06f093fdd9956a06d37e1b1e81344e27478c42f0", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}], "dist": {"shasum": "06f093fdd9956a06d37e1b1e81344e27478c42f0", "tarball": "https://registry.npmjs.org/mz/-/mz-1.3.0.tgz", "integrity": "sha512-x+R7YSsEySSpV5uEB+C47JTmxv+YKKNsW3W+hjvq8NbLn8ntLgYXGrR5RjQ3Fs0e7Chw8Rp/1e5eo0n5LP76cw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEsKh3eHVG9+p5l0vwCdg5kpPRl0h00Riw9Rapr5dPqdAiA/pb5XXGe8M58j6MxjD3OEaBn1jAVRhEMGJOoALCQb0Q=="}]}, "directories": {}}, "2.0.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"native-or-bluebird": "1", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["child_process.js", "crypto.js", "dns.js", "fs.js", "zlib.js"], "gitHead": "c360ae1da35674880258c06b86a44acd951e0fbb", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.0.0", "_shasum": "327583577a9c5fa4bb2ee08d0a558f1f33487bb9", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.1.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "327583577a9c5fa4bb2ee08d0a558f1f33487bb9", "tarball": "https://registry.npmjs.org/mz/-/mz-2.0.0.tgz", "integrity": "sha512-n0ndcU8yXX6P2e+rI3WpU70HxRvm+ibBxkRBbluuPKsqKkF3X56w1aO/VgUzYxPbgz60TlCBakJRPC5wJLEgvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFG5mp1kfTH59t7LGFCpYQAOf9ljnpI30bIv5Cm10j/mAiEAsYN5Xy3KPn+dmAS01HnweHdtD/lngQiLZLDeLOe7kuM="}]}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"native-or-bluebird": "1", "object-assign": "^4.0.1", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "088d77085ea5731518f1fe617bbdcce82f08459b", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.1.0", "_shasum": "939834ed7e132ef0829312d5a168ab8d4ffebe4a", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.0", "_npmUser": {"name": "linusu", "email": "<EMAIL>"}, "dist": {"shasum": "939834ed7e132ef0829312d5a168ab8d4ffebe4a", "tarball": "https://registry.npmjs.org/mz/-/mz-2.1.0.tgz", "integrity": "sha512-JIun7eklg0KjvqfFUv/FV6xiBhxp0Jo/CHVbO2QSBRqz9C6QZSCcn0sFerwpec+L+AnbJicV6g1tm5x+8HhxHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS3NTlWxXipEJBMtC2Y0cS41W5H8JSy4Nz9tSQOUpNjQIhAPG5plLz2zm5K1C3czWRC2MYP0glqncWN43wjKE7hP79"}]}, "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"native-or-bluebird": "1", "object-assign": "^4.0.1", "thenify-all": "1"}, "devDependencies": {"istanbul": "0", "bluebird": "2", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "a1e3a8d721ad873c07915ba0da39df9d992670a1", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.2.0", "_shasum": "530dbef09c2b27c58fd752345132c94c47f2015a", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "dead_horse", "email": "<EMAIL>"}, "dist": {"shasum": "530dbef09c2b27c58fd752345132c94c47f2015a", "tarball": "https://registry.npmjs.org/mz/-/mz-2.2.0.tgz", "integrity": "sha512-CFqBmo9WcIkIV6gZWI4aag1/t+KSyUSNCn3r4Jv0AQgi31R8WuMKP0H/Uub7Wufel8TM/hpNhrG+CJkmBPAa7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5vjI4+aKzGjdYe5A8X2S/aepxXPGgmP/JD0ClUWxKrwIgI60RWt6u/i6atpvCNSkMOYW3NALfLtIZchzlVeSPIOU="}]}, "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "0.2.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^2.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "7bcba44f4a9c42e2863807a47835d630899cd367", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.3.0", "_shasum": "7c026dc58b78ee10980f7286e170fe3190e1a3bd", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "7c026dc58b78ee10980f7286e170fe3190e1a3bd", "tarball": "https://registry.npmjs.org/mz/-/mz-2.3.0.tgz", "integrity": "sha512-DPfiQrGsk1pZc7DOL3zL0eepW98u3VvgHlunxjY+rO/6xZSlgyMlJ+peji/phwELR5KE/Ihd8zfcVhusDTdihg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICyfF7tEt2fquLuB1tECgY7S5RoChNhwYQumXfWCLXMqAiB/0ckS8gF8VOKcIHB3yO2d5Kwl2j8S8qyCPvpIVe8PYA=="}]}, "maintainers": [{"name": "coder<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "directories": {}}, "2.3.1": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^2.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "a74ac5ddaf0e7af6dcfed830090fc656f6de7329", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.3.1", "_shasum": "782a3c5a0bbab114acb92a505de831a5da1c05be", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "782a3c5a0bbab114acb92a505de831a5da1c05be", "tarball": "https://registry.npmjs.org/mz/-/mz-2.3.1.tgz", "integrity": "sha512-KQx21Z74CKp81YNSfoYUt/tB6M+P3AUYtb5M07KkO4S6rwVHSe0w10Q0U2AGwrotNLL+cWzUtzgovvF1yLN4OQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOD6/Lahjb6JOSl1810v9rb1rJt44IzaPe9GdYQk8dRAIgMM0SqS22oBpBmkgAVW/iHpxmTOBSA22QGd5l38Ph9VE="}]}, "maintainers": [{"name": "coder<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/mz-2.3.1.tgz_1454354151487_0.5965575035661459"}, "directories": {}}, "2.4.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^2.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "1704ccd4b630961deb437f79098cad0d46dd25ea", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.4.0", "_shasum": "987ba9624d89395388c37cb4741e2caf4dd13b1a", "_from": ".", "_npmVersion": "3.8.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "987ba9624d89395388c37cb4741e2caf4dd13b1a", "tarball": "https://registry.npmjs.org/mz/-/mz-2.4.0.tgz", "integrity": "sha512-jFOHo66xqlkj8WtPLVPamL2p/jasu2cI+7iSNaGh0oZ4Y9LHdUbRdZc2SW13jhUaJn83S48kgmGfLfF449b7Sw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5XYI+wKoLC8kzgv2OAcO1dbySXFYIijefAtI15MIegQIgeaZVu9cxTQMYL51yFr8BJPu7fmbz9cgdJ5jiOyK4F04="}]}, "maintainers": [{"name": "coder<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/mz-2.4.0.tgz_1458793874138_0.43763329135254025"}, "directories": {}}, "2.5.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^3.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "efad0c55ad5a2b82d6860a4cc90183eb46fb2dd4", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.5.0", "_shasum": "2859025df03d46b57bb317174b196477ce64cec1", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "dead_horse", "email": "<EMAIL>"}, "dist": {"shasum": "2859025df03d46b57bb317174b196477ce64cec1", "tarball": "https://registry.npmjs.org/mz/-/mz-2.5.0.tgz", "integrity": "sha512-jTqXIBzwjbFIwluxl7vtU5BbzQjPbhSsugBkJfqL9i3fx3Tl6N0VI/+AI8vDROzTdrzdjGBazK7n5yK1xCNE7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO0wjEZs0QN3JK2XZAE4QndgKOsca4wnZOwbAbYRhvnwIhAOZvsg5aBZkPE5O/hpizkiS2FYPq/DG7Lex+kpkErtjD"}]}, "maintainers": [{"name": "coder<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "evancarroll", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/mz-2.5.0.tgz_1478274395349_0.3099418692290783"}, "directories": {}}, "2.6.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^3.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "10bc91f7f0bb861cc940a078037be64cc166c144", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.6.0", "_shasum": "c8b8521d958df0a4f2768025db69c719ee4ef1ce", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "dead_horse", "email": "<EMAIL>"}, "dist": {"shasum": "c8b8521d958df0a4f2768025db69c719ee4ef1ce", "tarball": "https://registry.npmjs.org/mz/-/mz-2.6.0.tgz", "integrity": "sha512-8js8Gn0gxv5D/dqve1Idd6LfvgxRB8eGFNWUq4skKiQKTzqVxdUQFAYPmot4jfbcyD5vMWsqGPqv24ZTN61vPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYQF8UDtivxmMvUVhNbgEUSQWP4FRiM5akgdZSmg9MswIgLX1vYX3Iw5vTZ/ytM4MytqnxVPFdECr1ZF/5SAMqUzM="}]}, "maintainers": [{"name": "coder<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "evancarroll", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "linusu", "email": "<EMAIL>"}, {"name": "rstacruz", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/mz-2.6.0.tgz_1479826013632_0.6499268061015755"}, "directories": {}}, "2.7.0": {"name": "mz", "description": "modernize node.js to current ECMAScript standards", "version": "2.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.0", "bluebird": "^3.0.0", "mocha": "^3.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js", "child_process.js", "crypto.js", "dns.js", "fs.js", "readline.js", "zlib.js"], "gitHead": "f3d4c7933ab03ad60353bacd70663614ab4482e9", "bugs": {"url": "https://github.com/normalize/mz/issues"}, "homepage": "https://github.com/normalize/mz#readme", "_id": "mz@2.7.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==", "shasum": "95008057a56cafadc2bc63dde7f9ff6955948e32", "tarball": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyFebhhcqKYI2GBbaSQVYP193bmXbcWZSqY1zKS/LDJgIhAIOqxFKhifxSZFpXPbSfekQMou4HMQFWcv8tabhX5Qjx"}]}, "maintainers": [{"email": "<EMAIL>", "name": "evancarroll"}, {"email": "<EMAIL>", "name": "coder<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rstacruz"}, {"email": "<EMAIL>", "name": "linusu"}, {"email": "<EMAIL>", "name": "dead_horse"}, {"email": "<EMAIL>", "name": "dead-horse"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}, {"email": "<EMAIL>", "name": "swatinem"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mz-2.7.0.tgz_1505325455363_0.5184209432918578"}, "directories": {}}}, "readme": "\n# MZ - Modernize node.js\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Dependency Status][david-image]][david-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\nModernize node.js to current ECMAScript specifications!\nnode.js will not update their API to ES6+ [for a while](https://github.com/joyent/node/issues/7549).\nThis library is a wrapper for various aspects of node.js' API.\n\n## Installation and Usage\n\nSet `mz` as a dependency and install it.\n\n```bash\nnpm i mz\n```\n\nThen prefix the relevant `require()`s with `mz/`:\n\n```js\nvar fs = require('mz/fs')\n\nfs.exists(__filename).then(function (exists) {\n  if (exists) // do something\n})\n```\n\nWith ES2017, this will allow you to use async functions cleanly with node's core API:\n\n```js\nconst fs = require('mz/fs')\n\n\nasync function doSomething () {\n  if (await fs.exists(__filename)) // do something\n}\n```\n\n## Promisification\n\nMany node methods are converted into promises.\nAny properties that are deprecated or aren't asynchronous will simply be proxied.\nThe modules wrapped are:\n\n- `child_process`\n- `crypto`\n- `dns`\n- `fs` (uses `graceful-fs` if available)\n- `readline`\n- `zlib`\n\n```js\nvar exec = require('mz/child_process').exec\n\nexec('node --version').then(function (stdout) {\n  console.log(stdout)\n})\n```\n\n## Promise Engine\n\n`mz` uses [`any-promise`](https://github.com/kevinbeaty/any-promise).\n\n## FAQ\n\n### Can I use this in production?\n\nYes, Node 4.x ships with stable promises support. For older engines,\nyou should probably install your own promise implementation and register it with\n`require('any-promise/register')('bluebird')`.\n\n### Will this make my app faster?\n\nNope, probably slower actually.\n\n### Can I add more features?\n\nSure.\nOpen an issue.\n\nCurrently, the plans are to eventually support:\n\n- New APIs in node.js that are not available in older versions of node\n- ECMAScript7 Streams\n\n[bluebird]: https://github.com/petkaantonov/bluebird\n\n[npm-image]: https://img.shields.io/npm/v/mz.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/mz\n[github-tag]: http://img.shields.io/github/tag/normalize/mz.svg?style=flat-square\n[github-url]: https://github.com/normalize/mz/tags\n[travis-image]: https://img.shields.io/travis/normalize/mz.svg?style=flat-square\n[travis-url]: https://travis-ci.org/normalize/mz\n[coveralls-image]: https://img.shields.io/coveralls/normalize/mz.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/normalize/mz?branch=master\n[david-image]: http://img.shields.io/david/normalize/mz.svg?style=flat-square\n[david-url]: https://david-dm.org/normalize/mz\n[license-image]: http://img.shields.io/npm/l/mz.svg?style=flat-square\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/mz.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/mz\n", "maintainers": [{"email": "<EMAIL>", "name": "swatinem"}, {"email": "<EMAIL>", "name": "coder<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "evancarroll"}, {"email": "<EMAIL>", "name": "dead-horse"}, {"email": "<EMAIL>", "name": "linusu"}, {"email": "<EMAIL>", "name": "rstacruz"}, {"email": "<EMAIL>", "name": "dead_horse"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "time": {"modified": "2023-11-07T07:18:53.850Z", "created": "2014-05-08T07:30:52.707Z", "0.1.1": "2014-05-08T07:30:52.707Z", "0.1.2": "2014-05-08T07:33:02.348Z", "0.1.3": "2014-05-08T18:14:13.189Z", "0.1.4": "2014-05-12T03:02:39.858Z", "0.1.5": "2014-05-14T22:08:22.336Z", "0.1.6": "2014-06-10T09:48:53.190Z", "1.0.0": "2014-06-18T23:35:32.387Z", "1.0.1": "2014-08-18T08:31:08.659Z", "1.0.2": "2014-10-16T00:33:40.887Z", "1.1.0": "2014-11-14T19:30:41.510Z", "1.2.0": "2014-12-17T06:44:14.907Z", "1.2.1": "2014-12-27T20:15:21.559Z", "1.3.0": "2015-02-11T19:16:52.457Z", "2.0.0": "2015-05-25T00:34:06.007Z", "2.1.0": "2015-10-15T17:28:35.681Z", "2.2.0": "2016-01-24T13:15:06.076Z", "2.3.0": "2016-01-30T21:25:03.743Z", "2.3.1": "2016-02-01T19:15:52.027Z", "2.4.0": "2016-03-24T04:31:14.565Z", "2.5.0": "2016-11-04T15:46:36.988Z", "2.6.0": "2016-11-22T14:46:55.566Z", "2.7.0": "2017-09-13T17:57:36.315Z"}, "homepage": "https://github.com/normalize/mz#readme", "repository": {"type": "git", "url": "git+https://github.com/normalize/mz.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/normalize/mz/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"aliem": true, "tunnckocore": true, "hoitmort": true, "julien-f": true, "program247365": true, "qqqppp9998": true, "nex": true, "burl.bn": true, "hal9zillion": true, "niieani": true, "makay": true, "onestone": true, "shanewholloway": true, "coalesce": true, "antixrist": true, "programmer.severson": true, "boto": true, "jakub.knejzlik": true, "hyzual": true, "freaktechnik": true, "derflatulator": true, "laudeon": true, "wmhilton": true, "kontrax": true, "icestone": true, "rsp": true, "superwf": true, "nickytonline": true, "rahulroy9202": true, "garenyondem": true, "bangbang93": true, "grreenzz": true, "sensui": true, "wozhizui": true, "davidnyhuis": true, "adamski": true, "larrychen": true, "pratiks3": true, "fanyegong": true, "yournian": true, "seangenabe": true, "geofftech": true, "vidhill": true, "daizch": true, "qualiabyte": true, "andygreenegrass": true, "zhqgit": true, "shundai12306": true, "isayme": true, "nfrigus": true, "sunshine1988": true, "zhangaz1": true, "capaj": true, "jaggedsoft": true}, "keywords": ["promisify", "promise", "thenify", "then", "es6"]}