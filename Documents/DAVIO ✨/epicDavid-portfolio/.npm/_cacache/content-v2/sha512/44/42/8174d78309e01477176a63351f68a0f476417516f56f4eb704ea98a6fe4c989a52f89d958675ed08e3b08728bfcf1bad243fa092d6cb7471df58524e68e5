{"_id": "@vitest/snapshot", "_rev": "123-84f22f8cae12c668cb9b1f0e167d1d25", "name": "@vitest/snapshot", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"0.30.0": {"name": "@vitest/snapshot", "version": "0.30.0", "license": "MIT", "_id": "@vitest/snapshot@0.30.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "462e5fe7bfa009b03f4b53c1903ce27d47f249f0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.30.0.tgz", "fileCount": 12, "integrity": "sha512-e4eSGCy36Bw3/Tkir9qYJDlFsUz3NALFPNJSxzlY8CFl901TV9iZdKgpqXpyG1sAhLO0tPHThBAMHRi8hRA8cg==", "signatures": [{"sig": "MEQCIGqgkDP+CqtTTDwaBLo5xRleDaMF3njAIxRTgNVCIU1aAiArdQo20b5x1ibuGmA4gxDXuGY5TqdYNwWDvDp2SiLknQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMsAMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGDw//XHyWYUQ/oIL3c6hv0av7wqG67scbp8drtA47OrETRmP2qMya\r\njCrZJEMq60B8BPWRg9QWZ4EOEeZmfyPfODHc/rRTPvB1lRsk5Tbx/xCRozBw\r\ntVpwBVvup4mbAUypna+Kxb45PgkfGCVqg2Ew6L1lx6RPdzzcKK8dEUV+CAJm\r\nHR/aybVyxHliqSz7y/NyP5Vp4IBXnmlDg+EomNKEMD3DeYLBPwyLzt805ItG\r\n/Fu5FpCaFC1E/RNC4dh4S7R7f1pFmN6UjgVKKVHxJIUCAbNCA8NB8QdOI6Xb\r\nUTRLPhvZbg4bETfOLgAZ2vP+Y2FnH/99TCLy/wHxXsXdDdpkjxPzyhPk1nIg\r\nEsoYU4zccAdR2ici9fTl52YCWbBZi2gxT6X0EBrKTAa+VE7qyQtXBSI5mc9i\r\nel1DxpEWT5B/BsnrUz7ijRwuEw1StuJgqVK+7kbc6+A2sSVSVaLv0/5BtYnj\r\nij7Jvg77bFqsiXdRJgcfjXXCeF1ICL+rTGNEikO/Ak71Ee12Pk7LqVQKmNqr\r\ndP7y7hoeUBNqTNDkvL6F+LNc5ezfRYi0r2lrAuXid4RjWR85qlqogSqz04wq\r\nmkOD+3I/RggkEFYU6rpHQfGhIKR7DIQTINTu5Lm2yQispTaeNZcR9DnvruKx\r\navtNc61E8QSAfMfDg4+ky2ikJ4BmxcPJNpw=\r\n=XENv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.30.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/28ee9389875ed08f2da33d93ecafddee/vitest-snapshot-0.30.0.tgz", "_integrity": "sha512-e4eSGCy36Bw3/Tkir9qYJDlFsUz3NALFPNJSxzlY8CFl901TV9iZdKgpqXpyG1sAhLO0tPHThBAMHRi8hRA8cg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.15.0", "description": "Vitest Snapshot Resolver", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.30.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.30.0_1681047564299_0.07478178455116957", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "@vitest/snapshot", "version": "0.30.1", "license": "MIT", "_id": "@vitest/snapshot@0.30.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "25e912557b357ecb89d5ee35e8d7c4c7a5ecfe32", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.30.1.tgz", "fileCount": 14, "integrity": "sha512-fJZqKrE99zo27uoZA/azgWyWbFvM1rw2APS05yB0JaLwUIg9aUtvvnBf4q7JWhEcAHmSwbrxKFgyBUga6tq9Tw==", "signatures": [{"sig": "MEYCIQC4fdr7ays1/dZsrTVqtQBm7GqwvGym3F+4w8uZLotQTQIhAP2czyQ0U/oD4LjqNjrQB5D9SJweUaR3eN2zh2dqapTM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUQEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojpA/9FComV4SasNn3Ky4ooggJlHVFKBwv7atuvZcnH+ksk2jezQkS\r\nQKZuYChZThBGkhK5sJzkUK3PmUR29UCTqRDnOTw4q5JgITb0YoJrQ+4dcVSv\r\ny1RUtvHHQo6G/t+4BZzoB4Q7/BGBZKEzYyBIVS8q0qflXVJI8oU6fXSsPTq/\r\n3gdKWLHqKPXWIkJmoekVZor6637KNoq1FF0bhopojoZAPSYDGHooHG51xMlR\r\n9x1GVLYWbFsUgUoSfjFSf0s1ecbezu26Zpbfoo29r5K5QVnisCmRyxsXwW4D\r\nfmehHCQGylWp7wDE2AQXd5LpaPiIFlJ98Yf2MYNlFRxiUJ6jGsXxGrUG7xPO\r\nLUmuImqX93ovCK0H6m0N2wCesCaR4Q3GqWcax74IE1glYYEiW7LcNXpUhnyW\r\nGzOjvi9HAUOb90yR+P0En16r8vE086cKj1yy+S5xFLgFZ103IkQ2iBaC3Tgi\r\nAJBkMtXkB0J/AsQVQlAmzgdUSb0WQ/Y+htbJuJVfdUu/D7YNucq72XmQcAtb\r\nGWyQdQ4vKwxPvQkMHUKHC1UUkIPPcQdHUeN5m+9wQGsy0FmS2AVX2tNrUI0L\r\nFbWk1M6DOYZoOlmtpWu+bcD/gOX4tzkXc0X0bMXMLKO3HO85QEXVFiWLQy4B\r\n8zrHgZGP3BitrZ9LcX+FsWR0aNTE9Mjmbcg=\r\n=xMtK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.30.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fe88b9cdb94d9df4a533d12a496470de/vitest-snapshot-0.30.1.tgz", "_integrity": "sha512-fJZqKrE99zo27uoZA/azgWyWbFvM1rw2APS05yB0JaLwUIg9aUtvvnBf4q7JWhEcAHmSwbrxKFgyBUga6tq9Tw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.15.0", "description": "Vitest Snapshot Resolver", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.30.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.30.1_1681212420165_0.38539544163302963", "host": "s3://npm-registry-packages"}}, "0.31.0": {"name": "@vitest/snapshot", "version": "0.31.0", "license": "MIT", "_id": "@vitest/snapshot@0.31.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f59c4bcf0d03f1f494ee09286965e60a1e0cab64", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.0.tgz", "fileCount": 14, "integrity": "sha512-5dTXhbHnyUMTMOujZPB0wjFjQ6q5x9c8TvAsSPUNKjp1tVU7i9pbqcKPqntyu2oXtmVxKbuHCqrOd+Ft60r4tg==", "signatures": [{"sig": "MEUCIFAJTMwKqsl80HnDTV4Cp+9L6jRclAa2WRmeXACxLO7IAiEAwSJrQfcUhaJjNv0RT4YEtjA8Rcfvux2IGhQizBXsCe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqMyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIhw/7Bukf+gGVq8yZOKZFTzwofgoQQGI+yryStDn49nEQeBII21wX\r\nZ14iJZtzp/JjidqsHa2u1Us7bxoPx5L6XSp1wdalbAuGhL591d5jhChSlSkh\r\nTSF6/a00oaQkDPGEgzLTVHWob11FBnvUwY36xXY5ojPrJz4Koovm4h+i+8dg\r\nqzaWYuEhkvk0+prI78WUjZMSxs66FdGZOO1VXMMnAB4YKnoxvq2Ua9nliSK/\r\nZefqP9fGLlk1WpI2iG/V5YMQ8rukTVT7RAxfCi5KEuIQAojcgWdTjABEX9ao\r\neLRtjYAXU+2Srdf+YhH0I8j4J8P4U2V7VIICJBMxKQJvgHJmerOngmx+v2N8\r\n+/nQm5wsKiJNlEMoT1qIDJjaGWD/yE7boltlooR/ZkLaK4CtNEouiHHSBKk2\r\nwdxQHMh89lVV90DflCFZgmKfP/1lpyjHRC2XXLNXEvoAY96dhJs/kOXzNVG1\r\nHtIB/yyTGA8dno5Cn31ba0/7H+CWlwdRpdVtY0nrxLXlviYs4S97wNyAzhWR\r\n/GqTuQaGGjjSRwxG7XQ631gmPrs2KOYz7YEH/4P9C2K/WKpwBhvI9/U9/D7P\r\nhbjr5cpM5fxLqicjJ9y2M6ZXyZPVTKWmDYc9kJsMuT6lQbOvgNQxz9YJdEIc\r\nI7cudxrnYGXWWD+gShRgQY7TkoiCdbrmqq8=\r\n=nNxk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.31.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/703b5f7082a741b6235c27cdacb6e5d6/vitest-snapshot-0.31.0.tgz", "_integrity": "sha512-5dTXhbHnyUMTMOujZPB0wjFjQ6q5x9c8TvAsSPUNKjp1tVU7i9pbqcKPqntyu2oXtmVxKbuHCqrOd+Ft60r4tg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.15.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.31.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.31.0_1683137329856_0.7028323685047109", "host": "s3://npm-registry-packages"}}, "0.31.1": {"name": "@vitest/snapshot", "version": "0.31.1", "license": "MIT", "_id": "@vitest/snapshot@0.31.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7fc3f1e48f0c4313e6cb795c17a2c1aa909a7d64", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.1.tgz", "fileCount": 14, "integrity": "sha512-L3w5uU9bMe6asrNzJ8WZzN+jUTX4KSgCinEJPXyny0o90fG4FPQMV0OWsq7vrCWfQlAilMjDnOF9nP8lidsJ+g==", "signatures": [{"sig": "MEUCIQDIJ+TdpA80O4/OgswLR8KzsgMhSVUs/nTbnKb43i/+8gIgBNdZTECu1cJkUL3cWcr93EwCti60h/Yo78t+ehu8nmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.31.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fcdc2ddcfe6dea944b568234ba896d37/vitest-snapshot-0.31.1.tgz", "_integrity": "sha512-L3w5uU9bMe6asrNzJ8WZzN+jUTX4KSgCinEJPXyny0o90fG4FPQMV0OWsq7vrCWfQlAilMjDnOF9nP8lidsJ+g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.15.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.31.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.31.1_1684333429027_0.018351982312200033", "host": "s3://npm-registry-packages"}}, "0.31.2": {"name": "@vitest/snapshot", "version": "0.31.2", "license": "MIT", "_id": "@vitest/snapshot@0.31.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "061d6d3ec013e008c5e895b60ca5e1bde09b6151", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.2.tgz", "fileCount": 14, "integrity": "sha512-NXRlbP3sM5+KELb8oXVHf7UWD+liBnSsS+4JlDVPD5+KPquZmgNR0xPLW5VEb5HoQZQpKTAFhtGf1AczRCbAhg==", "signatures": [{"sig": "MEYCIQCVRpCaLcuaqCv0C/5jQBzGpvGNJesTvAEyy6WRDGZohwIhAOE4cjOowlHnJgKzH6uPeDH2XB+okNam/hRx9JUJbKnI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.31.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c6980747fccfb89125c1b03d0cd67cb2/vitest-snapshot-0.31.2.tgz", "_integrity": "sha512-NXRlbP3sM5+KELb8oXVHf7UWD+liBnSsS+4JlDVPD5+KPquZmgNR0xPLW5VEb5HoQZQpKTAFhtGf1AczRCbAhg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.31.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.31.2_1685451934217_0.8256774065431414", "host": "s3://npm-registry-packages"}}, "0.31.3": {"name": "@vitest/snapshot", "version": "0.31.3", "license": "MIT", "_id": "@vitest/snapshot@0.31.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cddaf637c07ee232303da8003f1f2a718eae656b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.3.tgz", "fileCount": 14, "integrity": "sha512-CgcmlZyP083oPUgd83rwLZ+qoT4KdvLNsyWRiJNgHoJjR0r7StVY+LlLAfdC9qA70G8O+hJeug+Yn1u4H51OzA==", "signatures": [{"sig": "MEUCIQDPwwuuPYej2cfOzLzuLQVSqymSKhV6l47dIsZSaDS1GQIgKy0SKJ/BvpzwWoUJT0tV2Sjmnlv4kF7IA8uhwTrgaQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.31.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3cfce6cbeff076e942c57196efa7bf94/vitest-snapshot-0.31.3.tgz", "_integrity": "sha512-CgcmlZyP083oPUgd83rwLZ+qoT4KdvLNsyWRiJNgHoJjR0r7StVY+LlLAfdC9qA70G8O+hJeug+Yn1u4H51OzA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.31.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.31.3_1685544586130_0.546096575750749", "host": "s3://npm-registry-packages"}}, "0.31.4": {"name": "@vitest/snapshot", "version": "0.31.4", "license": "MIT", "_id": "@vitest/snapshot@0.31.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "59a42046fec4950a1ac70cf0ec64aada3b995559", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.4.tgz", "fileCount": 14, "integrity": "sha512-LemvNumL3NdWSmfVAMpXILGyaXPkZbG5tyl6+RQSdcHnTj6hvA49UAI8jzez9oQyE/FWLKRSNqTGzsHuk89LRA==", "signatures": [{"sig": "MEQCIFscyRLIZjDYYOp5PQywAPPRqytR+oVfRdzIDPh7F/xoAiAdoE1FEA0XuAbcVF6bD5+m+kWx8rlH+yiN3hlIlnXkUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.31.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/0dc39ffdd9dbae0585171804780b752b/vitest-snapshot-0.31.4.tgz", "_integrity": "sha512-LemvNumL3NdWSmfVAMpXILGyaXPkZbG5tyl6+RQSdcHnTj6hvA49UAI8jzez9oQyE/FWLKRSNqTGzsHuk89LRA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.31.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.31.4_1685613335944_0.3180882983380737", "host": "s3://npm-registry-packages"}}, "0.32.0": {"name": "@vitest/snapshot", "version": "0.32.0", "license": "MIT", "_id": "@vitest/snapshot@0.32.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "49c6a08da12ab903ab4582386c97d70b293bc644", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.0.tgz", "fileCount": 14, "integrity": "sha512-yCKorPWjEnzpUxQpGlxulujTcSPgkblwGzAUEL+z01FTUg/YuCDZ8dxr9sHA08oO2EwxzHXNLjQKWJ2zc2a19Q==", "signatures": [{"sig": "MEYCIQD7YON5HrJXO6qBUgTP5UwIAP2bcHJujDyo1Vxb/2BuaAIhAJoVGOlgN7HFc3lrKUcql9H8v4EBaVvnLvcnGnBcSekW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.32.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/528795fcf21aa4422433cf3bc3d241e5/vitest-snapshot-0.32.0.tgz", "_integrity": "sha512-yCKorPWjEnzpUxQpGlxulujTcSPgkblwGzAUEL+z01FTUg/YuCDZ8dxr9sHA08oO2EwxzHXNLjQKWJ2zc2a19Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.32.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.32.0_1686071076359_0.4781546803520109", "host": "s3://npm-registry-packages"}}, "0.32.1": {"name": "@vitest/snapshot", "version": "0.32.1", "license": "MIT", "_id": "@vitest/snapshot@0.32.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e2f6722bdc3b18615cd4f9ff1fa6ca3ba624413f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.1.tgz", "fileCount": 14, "integrity": "sha512-6QsxowDyv6Gm/McSD/6eXBgL3zMSIxBvxC50E7ZYJ+DNfJACJ1w6pykhbnBh9L8igwlFFUPNC1hj+3WD6xF4QQ==", "signatures": [{"sig": "MEUCIQDW4ZST0WJcq3yYpmET3KrOssOBSYwEE4kHNrkpKtMGdwIgFF3M9nCJ846XWIAkO+D2Qoe9v5xBcii2n1xIMBbgadw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.32.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b2840fb880c737b34b2ad9dfcecc5dbf/vitest-snapshot-0.32.1.tgz", "_integrity": "sha512-6QsxowDyv6Gm/McSD/6eXBgL3zMSIxBvxC50E7ZYJ+DNfJACJ1w6pykhbnBh9L8igwlFFUPNC1hj+3WD6xF4QQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.32.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.32.1_1686918193294_0.639131017081229", "host": "s3://npm-registry-packages"}}, "0.32.2": {"name": "@vitest/snapshot", "version": "0.32.2", "license": "MIT", "_id": "@vitest/snapshot@0.32.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "500b6453e88e4c50a0aded39839352c16b519b9e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.2.tgz", "fileCount": 14, "integrity": "sha512-JwhpeH/PPc7GJX38vEfCy9LtRzf9F4er7i4OsAJyV7sjPwjj+AIR8cUgpMTWK4S3TiamzopcTyLsZDMuldoi5A==", "signatures": [{"sig": "MEUCIQCRfa/EDX0cCcPYCplXeKwZZejm/LhP7WJNkTuFWuJo7wIgZB1QTbJ4cp/xoOAi8W7qdOCoDb7/DPVmPKakmdmHd90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.32.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a2f9e8d2268598ba0f3ad5b1ac026014/vitest-snapshot-0.32.2.tgz", "_integrity": "sha512-JwhpeH/PPc7GJX38vEfCy9LtRzf9F4er7i4OsAJyV7sjPwjj+AIR8cUgpMTWK4S3TiamzopcTyLsZDMuldoi5A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.32.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.32.2_1686931564319_0.2985529461341012", "host": "s3://npm-registry-packages"}}, "0.32.3": {"name": "@vitest/snapshot", "version": "0.32.3", "license": "MIT", "_id": "@vitest/snapshot@0.32.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4e43ac739de37daa14e7912f7e0c814ada623126", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.3.tgz", "fileCount": 14, "integrity": "sha512-xNm3aD2gzkxmIKQbST7R1PZoJ7XG/rsQYKG8I8p5CTth2749w0cQQ/45b4dMf+nbwxNI0EEATu1r3szds2Fo9w==", "signatures": [{"sig": "MEUCIQDe5c2q2w/v4L1lSwgFwSs2QAzvm6T7ujdzkEN0P0TbGgIgZ15WFYxJGGg6g6Qe8jXLaPOw+trgl9x/JPSp9pQGqxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.32.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/bcc93b08e54c7bb25a87cd34cf82a717/vitest-snapshot-0.32.3.tgz", "_integrity": "sha512-xNm3aD2gzkxmIKQbST7R1PZoJ7XG/rsQYKG8I8p5CTth2749w0cQQ/45b4dMf+nbwxNI0EEATu1r3szds2Fo9w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.0", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.32.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.32.3_1688373341568_0.6490979902893732", "host": "s3://npm-registry-packages"}}, "0.32.4": {"name": "@vitest/snapshot", "version": "0.32.4", "license": "MIT", "_id": "@vitest/snapshot@0.32.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "75166b1c772d018278a7f0e79f43f3eae813f5ae", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.4.tgz", "fileCount": 14, "integrity": "sha512-IRpyqn9t14uqsFlVI2d7DFMImGMs1Q9218of40bdQQgMePwVdmix33yMNnebXcTzDU5eiV3eUsoxxH5v0x/IQA==", "signatures": [{"sig": "MEUCIQCCfArRdtjrKAn6Wy44xrVO1H4VXPGAq9tb4jwRGCyLMQIgNOxB3+vMyU/nWCNtcmgJcZoG1KUUnwhHELnbHNuiK48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.32.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/65c01a25142091ee5e6019386eca9d13/vitest-snapshot-0.32.4.tgz", "_integrity": "sha512-IRpyqn9t14uqsFlVI2d7DFMImGMs1Q9218of40bdQQgMePwVdmix33yMNnebXcTzDU5eiV3eUsoxxH5v0x/IQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.0", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.32.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.32.4_1688382362945_0.5290065812466906", "host": "s3://npm-registry-packages"}}, "0.33.0": {"name": "@vitest/snapshot", "version": "0.33.0", "license": "MIT", "_id": "@vitest/snapshot@0.33.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4400a90c48907808122b573053a2112a832b3698", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.33.0.tgz", "fileCount": 14, "integrity": "sha512-tJjrl//qAHbyHajpFvr8Wsk8DIOODEebTu7pgBrP07iOepR5jYkLFiqLq2Ltxv+r0uptUb4izv1J8XBOwKkVYA==", "signatures": [{"sig": "MEUCIB68oR7o+0Ln/gTKI3nAe5FK6ZQVTxXgLBvRpBoBAboiAiEAgX0wAMheRpJXeQZ/3iCAVFuGVTkVfoK8PQjWahFrUcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.33.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c5956f24949249a675269c7c1dcfb076/vitest-snapshot-0.33.0.tgz", "_integrity": "sha512-tJjrl//qAHbyHajpFvr8Wsk8DIOODEebTu7pgBrP07iOepR5jYkLFiqLq2Ltxv+r0uptUb4izv1J8XBOwKkVYA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "8.19.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.33.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.33.0_1688652687666_0.01657739013946613", "host": "s3://npm-registry-packages"}}, "0.34.0": {"name": "@vitest/snapshot", "version": "0.34.0", "license": "MIT", "_id": "@vitest/snapshot@0.34.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4330c67f1686810b5657b20931d21dbe0b7e2577", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.0.tgz", "fileCount": 14, "integrity": "sha512-eGN5XBZHYOghxCOQbf8dcn6/3g7IW77GOOOC/mNFYwRXsPeoQgcgWnhj+6wgJ04pVv25wpxWL9jUkzaQ7LoFtg==", "signatures": [{"sig": "MEUCIQCAuJj9AuNldn+VV5zf/tWzeWO1sjsNpaVicmYP+tbycQIgewzO0jho67tCUD+ZH9O9DKi6JL76K2a+EBioY4lQC1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105365}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/116dbe78975bcfcb91538c80b06565f5/vitest-snapshot-0.34.0.tgz", "_integrity": "sha512-eGN5XBZHYOghxCOQbf8dcn6/3g7IW77GOOOC/mNFYwRXsPeoQgcgWnhj+6wgJ04pVv25wpxWL9jUkzaQ7LoFtg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.0_1690904514051_0.837375407334342", "host": "s3://npm-registry-packages"}}, "0.34.1": {"name": "@vitest/snapshot", "version": "0.34.1", "license": "MIT", "_id": "@vitest/snapshot@0.34.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "814c65f8e714eaf255f47838541004b2a2ba28e6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.1.tgz", "fileCount": 14, "integrity": "sha512-0O9LfLU0114OqdF8lENlrLsnn024Tb1CsS9UwG0YMWY2oGTQfPtkW+B/7ieyv0X9R2Oijhi3caB1xgGgEgclSQ==", "signatures": [{"sig": "MEUCIQD2yzjCQC4+Hn2Eit3WI6y16SmbWO3H9H52vIEf6dJWfQIgWimKoyGTZkd9DnhvGLQZ5SO8LskSnUlWVhYjtX2Euew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105365}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4f08e92bbb177ba61aeafc44336fef58/vitest-snapshot-0.34.1.tgz", "_integrity": "sha512-0O9LfLU0114OqdF8lENlrLsnn024Tb1CsS9UwG0YMWY2oGTQfPtkW+B/7ieyv0X9R2Oijhi3caB1xgGgEgclSQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.1_1690908829147_0.6427037395997186", "host": "s3://npm-registry-packages"}}, "0.34.2": {"name": "@vitest/snapshot", "version": "0.34.2", "license": "MIT", "_id": "@vitest/snapshot@0.34.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fce1b89aa1e836e3fd0229c03ef4ef2f69cb1409", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.2.tgz", "fileCount": 14, "integrity": "sha512-qhQ+xy3u4mwwLxltS4Pd4SR+XHv4EajiTPNY3jkIBLUApE6/ce72neJPSUQZ7bL3EBuKI+NhvzhGj3n5baRQUQ==", "signatures": [{"sig": "MEUCIQCkMYBL6Zn3AF7PGoC+lFIVpBT2czkvFg7Z2q7JUtkhDwIgYO/+1f5OHZNyTDhwM8SMPg7XhZ2WUM+lSqcZRCDw9aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/af47cc5157245e78e03572612d014388/vitest-snapshot-0.34.2.tgz", "_integrity": "sha512-qhQ+xy3u4mwwLxltS4Pd4SR+XHv4EajiTPNY3jkIBLUApE6/ce72neJPSUQZ7bL3EBuKI+NhvzhGj3n5baRQUQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.2_1692266996363_0.4853287161623112", "host": "s3://npm-registry-packages"}}, "0.34.3": {"name": "@vitest/snapshot", "version": "0.34.3", "license": "MIT", "_id": "@vitest/snapshot@0.34.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cb4767aa44711a1072bd2e06204b659275c4f0f2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.3.tgz", "fileCount": 14, "integrity": "sha512-QyPaE15DQwbnIBp/yNJ8lbvXTZxS00kRly0kfFgAD5EYmCbYcA+1EEyRalc93M0gosL/xHeg3lKAClIXYpmUiQ==", "signatures": [{"sig": "MEQCIC5oGR2wbed9nu1gmmSmE30dgQ5Aam8PwFzygmQ5VK6pAiADY31jPyI0Ny5cYFGgn4PGl0+cUA8pvHrtRUREqQvJaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fe479978e5ddb5bef5e079db628bdc1f/vitest-snapshot-0.34.3.tgz", "_integrity": "sha512-QyPaE15DQwbnIBp/yNJ8lbvXTZxS00kRly0kfFgAD5EYmCbYcA+1EEyRalc93M0gosL/xHeg3lKAClIXYpmUiQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.3_1692948614258_0.2495215576120553", "host": "s3://npm-registry-packages"}}, "0.34.4": {"name": "@vitest/snapshot", "version": "0.34.4", "license": "MIT", "_id": "@vitest/snapshot@0.34.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ee2c732e5978438f96c669aabb9eb66eb7f3ff46", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.4.tgz", "fileCount": 14, "integrity": "sha512-G<PERSON>h4coc3YUSL/o+BPUo7lHQbzpdttTxL6f4q0jRx2qVGoYz/cyTRDJHbnwks6TILi6560bVWoBpYC10PuTLHw==", "signatures": [{"sig": "MEYCIQCSlX0D0AkNtqO73n8hjndafNE1ZHPafhfKkuRo6YYVUAIhAOHmf9JEQzv+Fz2PNceEx8bcDqErMPlCcK93nk4p2zvA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/05a5f90c96adab9c03b6d1da17a4d1f0/vitest-snapshot-0.34.4.tgz", "_integrity": "sha512-G<PERSON>h4coc3YUSL/o+BPUo7lHQbzpdttTxL6f4q0jRx2qVGoYz/cyTRDJHbnwks6TILi6560bVWoBpYC10PuTLHw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.4_1694169265640_0.048144833437582024", "host": "s3://npm-registry-packages"}}, "0.34.5": {"name": "@vitest/snapshot", "version": "0.34.5", "license": "MIT", "_id": "@vitest/snapshot@0.34.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1d81fce3cdc9cf6ad57e86eb5e5eecefc71d1e02", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.5.tgz", "fileCount": 14, "integrity": "sha512-+ikwSbhu6z2yOdtKmk/aeoDZ9QPm2g/ZO5rXT58RR9Vmu/kB2MamyDSx77dctqdZfP3Diqv4mbc/yw2kPT8rmA==", "signatures": [{"sig": "MEYCIQDTICE5qmpF9bHR6plo/5E6u/EgrgMRcItuL9TZRaIdAQIhAILEhJQUpYovW94hcbo48OGWAu5o8V3CxYW2HmkDrTdC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106683}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/972d6d66b68300f0195646cda769cafd/vitest-snapshot-0.34.5.tgz", "_integrity": "sha512-+ikwSbhu6z2yOdtKmk/aeoDZ9QPm2g/ZO5rXT58RR9Vmu/kB2MamyDSx77dctqdZfP3Diqv4mbc/yw2kPT8rmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.5_1695304233643_0.46377385348720623", "host": "s3://npm-registry-packages"}}, "0.34.6": {"name": "@vitest/snapshot", "version": "0.34.6", "license": "MIT", "_id": "@vitest/snapshot@0.34.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b4528cf683b60a3e8071cacbcb97d18b9d5e1d8b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.6.tgz", "fileCount": 14, "integrity": "sha512-B3OZqYn6k4VaN011D+ve+AA4whM4QkcwcrwaKwAbyyvS/NB1hCWjFIBQxAQQSQir9/RtyAAGuq+4RJmbn2dH4w==", "signatures": [{"sig": "MEUCIE2T7NNA4lVmarg9ebRJcGxzNOFWRAamAg6n0CUPsLjaAiEAscnnsh5t3veEHZOFYlsaalJSTP39478XWCH4JSl7tqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106683}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ae870043b85af818f172177cb0aa4cdd/vitest-snapshot-0.34.6.tgz", "_integrity": "sha512-B3OZqYn6k4VaN011D+ve+AA4whM4QkcwcrwaKwAbyyvS/NB1hCWjFIBQxAQQSQir9/RtyAAGuq+4RJmbn2dH4w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.6_1695972822720_0.0714403405198667", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "@vitest/snapshot", "version": "1.0.0-beta.0", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "177e345c670343d86ba66192eb21a26e1ae29ff1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.0.tgz", "fileCount": 14, "integrity": "sha512-yjBZ<PERSON>+DHpqdkZpQ0LDYUCSSTlF293HsTKKLkLAIqmn0Y4XdVV7qgVEebDA612sqsZ+c9lJZ5JP6n/YkWwlGaPQ==", "signatures": [{"sig": "MEUCIH1G9ho16ZH0WGexl+RxpmmRlR86pz/NLIDDdfa+wpHoAiEAwk7hnv6E/kk3xQ2IJgmmRV0K8/nDy0DwJlrycRiQq/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106658}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/85e49b148ac781fb1b1c968453802197/vitest-snapshot-1.0.0-beta.0.tgz", "_integrity": "sha512-yjBZ<PERSON>+DHpqdkZpQ0LDYUCSSTlF293HsTKKLkLAIqmn0Y4XdVV7qgVEebDA612sqsZ+c9lJZ5JP6n/YkWwlGaPQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.0-beta.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.0_1696264823442_0.7705640126056397", "host": "s3://npm-registry-packages"}}, "0.34.7": {"name": "@vitest/snapshot", "version": "0.34.7", "license": "MIT", "_id": "@vitest/snapshot@0.34.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "05af0972e7cba232a88db499f97accdbdbd8d145", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.7.tgz", "fileCount": 14, "integrity": "sha512-deb30aJWcASuv3H0rHIve9U0y9fKrHXFWIAsjroW1eq5X4+ue6nkcrRCxmh1CHL3nrB738PZXotZdnVrC79j4A==", "signatures": [{"sig": "MEUCIQDzhYpNR//Zl/zN4899/uvfZeSLEAfqjvM4V9OjbwV6ogIgEV6ji6kqNwa2OEyxYVrjzK2IlcDQYqPneteKOXmJWkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106759}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-0.34.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/74baf3536ef92ea41ab7164f6b801258/vitest-snapshot-0.34.7.tgz", "_integrity": "sha512-deb30aJWcASuv3H0rHIve9U0y9fKrHXFWIAsjroW1eq5X4+ue6nkcrRCxmh1CHL3nrB738PZXotZdnVrC79j4A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "0.34.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_0.34.7_1696266216479_0.14950394956080526", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "@vitest/snapshot", "version": "1.0.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6d12e8323ef993a05709771c93dd0414d812f489", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.1.tgz", "fileCount": 14, "integrity": "sha512-3ZBkBJ7O0zLvvAdARD56Srfdk/sgROdBFCUnayGv1/L4ZkwB4GRRl+zuZdT/GhNDT1Q0NMBntbwyIrCSASpY/A==", "signatures": [{"sig": "MEUCIAG5SG303cisdeKrxpJQ9+wwxs89Axh10X/xMBtBHvkRAiEA/kCBGCBSW4YWTqPY1M6cHuGRkmAPOvuTfOf4pZe0x50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106658}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/acce5900209927a0cafb733aeb29b88e/vitest-snapshot-1.0.0-beta.1.tgz", "_integrity": "sha512-3ZBkBJ7O0zLvvAdARD56Srfdk/sgROdBFCUnayGv1/L4ZkwB4GRRl+zuZdT/GhNDT1Q0NMBntbwyIrCSASpY/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "1.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.1_1696332689985_0.6097870573107895", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "@vitest/snapshot", "version": "1.0.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "086c2ff26686b839afe394f51bbef54501a7351b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.2.tgz", "fileCount": 14, "integrity": "sha512-ldL0EnnFr8Pkh7j+sY6ffWXN6UlQCEmMxvxaq/REgf7SKy09L2aJtqnTH3PvcCt98JqOESrd1UpcCRvQml5gVw==", "signatures": [{"sig": "MEQCIAmUKb0d3sX7FMFJhXZf7sL8JNAR77A65d4zbiRNxrMYAiAMj2gOfi3PcrFhfgW+mzBWzB8LJJ0B1R7uC9lwmTZNVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106668}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/289106b6dd42394ae11ee6b115b4b128/vitest-snapshot-1.0.0-beta.2.tgz", "_integrity": "sha512-ldL0EnnFr8Pkh7j+sY6ffWXN6UlQCEmMxvxaq/REgf7SKy09L2aJtqnTH3PvcCt98JqOESrd1UpcCRvQml5gVw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.2_1697182462946_0.8844574353530841", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "@vitest/snapshot", "version": "1.0.0-beta.3", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "121e5f926cece32fc4f102509b91d1e4eaffbae6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.3.tgz", "fileCount": 14, "integrity": "sha512-fun2daxMGuXo2wBi6ItFgUFl0xnN4v2hVXvIYGvoJG9SoPobfPlQ79MO8gMKOt++rWlUXNj7J23KHcoOyZ2Wwg==", "signatures": [{"sig": "MEUCIQDAoL/LWQ9VupbXB9Xhr0lMlX+oVuqRe7vYM4VWvisKlQIgP1AO29OecNaebM8fGpTCU5k5KrJBmQXVebTEERq9O+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115014}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/676a4ebae14bfcf4a79c91453830efef/vitest-snapshot-1.0.0-beta.3.tgz", "_integrity": "sha512-fun2daxMGuXo2wBi6ItFgUFl0xnN4v2hVXvIYGvoJG9SoPobfPlQ79MO8gMKOt++rWlUXNj7J23KHcoOyZ2Wwg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "1.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.3_1698410745916_0.905462927502751", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "@vitest/snapshot", "version": "1.0.0-beta.4", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ea9015bdd099b48f97ebcd06001a7f179dfe6731", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.4.tgz", "fileCount": 14, "integrity": "sha512-CzmHLGo4RNEQUojYtuEz8wWKp9/p3hvXskejRRJB1iCRH48uWROmoyb2iEQUhgpQw/+WwI4wRP7jek5lp48pRA==", "signatures": [{"sig": "MEUCIG+jutq0pqG5ewBGRO2bLVYEgPCq51zERvXZ4y6khUOTAiEAiNXqfb2SKwVpWyWk5WiSiwjq1nDCMExNaelpGpo/dYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115410}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "import": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c5af99f897faec48faa282348ffcdd40/vitest-snapshot-1.0.0-beta.4.tgz", "_integrity": "sha512-CzmHLGo4RNEQUojYtuEz8wWKp9/p3hvXskejRRJB1iCRH48uWROmoyb2iEQUhgpQw/+WwI4wRP7jek5lp48pRA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "1.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.4_1699524820744_0.7106854476593352", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "@vitest/snapshot", "version": "1.0.0-beta.5", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ce18b75979003f28a19e472289316cb3ae43ffa1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-fsWoc/mokLawqrLFqK9MHEyzJaGeDzU5gAgky2yZJR58VSsSvW+cesvmdv9ch39xHlTzFTRPgrWkNsmbdm2gbg==", "signatures": [{"sig": "MEQCIFTPK3Tw5V47ygqFzZS1xYwM/f9OxhtdJ2H7ttrPPJhLAiB7RedvEFBEQLb2jENdlIT0UQNvgxJ1bvHVMcohWLRhiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65445}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1ccad8f3337d4120d8c42d60a17f9fe1/vitest-snapshot-1.0.0-beta.5.tgz", "_integrity": "sha512-fsWoc/mokLawqrLFqK9MHEyzJaGeDzU5gAgky2yZJR58VSsSvW+cesvmdv9ch39xHlTzFTRPgrWkNsmbdm2gbg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "1.0.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.5_1700300675726_0.4368136450789202", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "@vitest/snapshot", "version": "1.0.0-beta.6", "license": "MIT", "_id": "@vitest/snapshot@1.0.0-beta.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f43ab9481f0a879474f0d1ae1190ef735fedf69a", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-znkf7R67aanWHjAfx+/Yj5VwIdUHU1I1akJFL8lguiu5C6C12D4ICHeMjRBc7e7BrIguBWZFfqPx/ZUSxqMTfA==", "signatures": [{"sig": "MEUCIEckDYreJYVH8lWjT/DRjU4oQrZLg0N2upjVo6Ev0kbNAiEAy1DSSzyg52U11ta0zTe8oOstsTno/DcoR6hZXu2Tnk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65445}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/40387a761d91d04bd3ac5a3d34f4fd54/vitest-snapshot-1.0.0-beta.6.tgz", "_integrity": "sha512-znkf7R67aanWHjAfx+/Yj5VwIdUHU1I1akJFL8lguiu5C6C12D4ICHeMjRBc7e7BrIguBWZFfqPx/ZUSxqMTfA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "1.0.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0-beta.6_1701192439703_0.6200962881165251", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@vitest/snapshot", "version": "1.0.0", "license": "MIT", "_id": "@vitest/snapshot@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2a7104b85b932a2dd7cf99afc3a2af33983cd5ba", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0.tgz", "fileCount": 13, "integrity": "sha512-kAcQJGsaHMBLrY0QC6kMe7S+JgiMielX2qHqgWFxlUir5IVekJGokJcYTzoOp+MRN1Gue3Q6H5fZD4aC0XHloA==", "signatures": [{"sig": "MEYCIQD+g9FlgWAIJYVtefTmyB4AMcpjGwPs31YM8hcImYuhowIhAJ328qqrOLK0J25smPufutqxHAXC6PZB7lTMyga/cu0n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/593cd996645e50cf3a37109812e383f9/vitest-snapshot-1.0.0.tgz", "_integrity": "sha512-kAcQJGsaHMBLrY0QC6kMe7S+JgiMielX2qHqgWFxlUir5IVekJGokJcYTzoOp+MRN1Gue3Q6H5fZD4aC0XHloA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.0_1701704788167_0.6745980693455744", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vitest/snapshot", "version": "1.0.1", "license": "MIT", "_id": "@vitest/snapshot@1.0.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9d2a01c64726afa62264175554690e5ce148d4a5", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.1.tgz", "fileCount": 13, "integrity": "sha512-wIPtPDGSxEZ+DpNMc94AsybX6LV6uN6sosf5TojyP1m2QbKwiRuLV/5RSsjt1oWViHsTj8mlcwrQQ1zHGO0fMw==", "signatures": [{"sig": "MEUCIBacrrQF+5XrgZdEn/mpGoxpoZm/pxXWzCiDBnsiO5dTAiEAsnli5+70UyfQc6btDDI6JWteZaFuBBCBQsTO3/E/NhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/88931da8dcbdc1596d48f1e2f9893537/vitest-snapshot-1.0.1.tgz", "_integrity": "sha512-wIPtPDGSxEZ+DpNMc94AsybX6LV6uN6sosf5TojyP1m2QbKwiRuLV/5RSsjt1oWViHsTj8mlcwrQQ1zHGO0fMw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.1_1701713093921_0.5082855605079388", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@vitest/snapshot", "version": "1.0.2", "license": "MIT", "_id": "@vitest/snapshot@1.0.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "df11b066c9593e3539640a41f38452a6b5889da1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.2.tgz", "fileCount": 13, "integrity": "sha512-9ClDz2/aV5TfWA4reV7XR9p+hE0e7bifhwxlURugj3Fw0YXeTFzHmKCNEHd6wOIFMfthbGGwhlq7TOJ2jDO4/g==", "signatures": [{"sig": "MEYCIQCGbVnO+suk+6A9Io7FUvdlFbjmkMXgTXUCudL5nzHtbgIhAPMFhGDXoCA9Jom5B5luFqrg6OsGgfJ3M2UCRD82kYYR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8629bdcf4688080867c88df0ffba0e09/vitest-snapshot-1.0.2.tgz", "_integrity": "sha512-9ClDz2/aV5TfWA4reV7XR9p+hE0e7bifhwxlURugj3Fw0YXeTFzHmKCNEHd6wOIFMfthbGGwhlq7TOJ2jDO4/g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.2_1701943985292_0.21841387614540664", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@vitest/snapshot", "version": "1.0.3", "license": "MIT", "_id": "@vitest/snapshot@1.0.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "98524b3494a0a50db3a5b5effe82ce5640dcc01f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.3.tgz", "fileCount": 13, "integrity": "sha512-2EQwVEuHusEXr0SKuFiI1JVlysSrUceejtusr6vK254tusAz/g4//QrAiD1b7PMdcUKM8QmdgWvqCMaYDsWyNA==", "signatures": [{"sig": "MEUCIQCrqeFPTEyz2lqGqybmZGfv9B+nn8fMC+VRpY6u/tgpxgIgGIQm9mu8ZTUhNHXlyez3HXrDpp3nXa+EeFdyqOy/WY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/19b5fc821f939ed9397b3f2b4ea637cd/vitest-snapshot-1.0.3.tgz", "_integrity": "sha512-2EQwVEuHusEXr0SKuFiI1JVlysSrUceejtusr6vK254tusAz/g4//QrAiD1b7PMdcUKM8QmdgWvqCMaYDsWyNA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.3_1702127151850_0.17958885874465036", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@vitest/snapshot", "version": "1.0.4", "license": "MIT", "_id": "@vitest/snapshot@1.0.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7020983b3963b473237fea08d347ea83b266b9bb", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.4.tgz", "fileCount": 13, "integrity": "sha512-vkfXUrNyNRA/Gzsp2lpyJxh94vU2OHT1amoD6WuvUAA12n32xeVZQ0KjjQIf8F6u7bcq2A2k969fMVxEsxeKYA==", "signatures": [{"sig": "MEUCIH4wrkKAWzHM1KlqRxeeOFWCLBkG3aqnA0xZcDY0c556AiEAji2YL8FJkOQhI5JrPmzaGiFLrEeZe6ZU6cSBJBwPnLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2d1ec70fb9c1f3537cce148e34d9f859/vitest-snapshot-1.0.4.tgz", "_integrity": "sha512-vkfXUrNyNRA/Gzsp2lpyJxh94vU2OHT1amoD6WuvUAA12n32xeVZQ0KjjQIf8F6u7bcq2A2k969fMVxEsxeKYA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.0.4_1702148723526_0.12919940519888118", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@vitest/snapshot", "version": "1.1.0", "license": "MIT", "_id": "@vitest/snapshot@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b9924e4303382b43bb2c31061b173e69a6fb3437", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-5O/wyZg09V5qmNmAlUgCBqflvn2ylgsWJRRuPrnHEfDNT6tQpQ8O1isNGgo+VxofISHqz961SG3iVvt3SPK/QQ==", "signatures": [{"sig": "MEQCICnIne8+b+lgzOtM8c1SrS8ZoDDxC8JMadHrf1EIuH+jAiBys13dgSRUnpxvLms0sVXIK14fcq1/+zEFeXqTnBPBug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a6a5f7df32071692e52cd3e46aafd29d/vitest-snapshot-1.1.0.tgz", "_integrity": "sha512-5O/wyZg09V5qmNmAlUgCBqflvn2ylgsWJRRuPrnHEfDNT6tQpQ8O1isNGgo+VxofISHqz961SG3iVvt3SPK/QQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.1.0_1702994795395_0.043586185388876375", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@vitest/snapshot", "version": "1.1.1", "license": "MIT", "_id": "@vitest/snapshot@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "40261901102e131cb09f23034884ad2c1c5af317", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.1.tgz", "fileCount": 13, "integrity": "sha512-WnMHjv4VdHLbFGgCdVVvyRkRPnOKN75JJg+LLTdr6ah7YnL75W+7CTIMdzPEPzaDxA8r5yvSVlc1d8lH3yE28w==", "signatures": [{"sig": "MEUCIGN0qXzfDyJmHfq94ykpRAZMZDyKPepqjPcZtX+cmM82AiEAyoQKxMCkR9qqbGfN57qBOnaTXuaMPBi0soHB699kX24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/293e845fba43e153448a850a8f37d8cb/vitest-snapshot-1.1.1.tgz", "_integrity": "sha512-WnMHjv4VdHLbFGgCdVVvyRkRPnOKN75JJg+LLTdr6ah7YnL75W+7CTIMdzPEPzaDxA8r5yvSVlc1d8lH3yE28w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.1.1_1704029883382_0.14686395739458558", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@vitest/snapshot", "version": "1.1.2", "license": "MIT", "_id": "@vitest/snapshot@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5628084d21423a55fb0be8e402ecfd1fd1240b51", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.2.tgz", "fileCount": 13, "integrity": "sha512-hXXd5KjURGt6GCrmw55A+PNIlrOaE6x6KcdEANXac76xmvVbJZXSiNVJ1JuMCiyvLLTzdpPnrgWyCX9/CepFCQ==", "signatures": [{"sig": "MEUCIQCnLoSB2TWO07fdaFH9Q4CLGelQ4Kv/mdNT4PQGU51XkgIgItbC5vPaVmbGNJZzvz8EfwEBvppWzpTE4V0ItFg7KaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65629}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2dcf7135ba8690b6b7c0dd785ef225ff/vitest-snapshot-1.1.2.tgz", "_integrity": "sha512-hXXd5KjURGt6GCrmw55A+PNIlrOaE6x6KcdEANXac76xmvVbJZXSiNVJ1JuMCiyvLLTzdpPnrgWyCX9/CepFCQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.1.2_1704387527410_0.3558410425078582", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@vitest/snapshot", "version": "1.1.3", "license": "MIT", "_id": "@vitest/snapshot@1.1.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "94f321f80c9fb9e10b83dabb83a0d09f034a74b0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.3.tgz", "fileCount": 13, "integrity": "sha512-U0r8pRXsLAdxSVAyGNcqOU2H3Z4Y2dAAGGelL50O0QRMdi1WWeYHdrH/QWpN1e8juWfVKsb8B+pyJwTC+4Gy9w==", "signatures": [{"sig": "MEQCIFcuVHO5l7ygeZUBTDXUBSZJAzbbbOHFpZ6tLcHy+OebAiBhNHVSC6rrjN7dLwBR4NDmU4sGTZiuy+oEovmgOkSuvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65629}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/58dc476f9925279d69d1079e78604c90/vitest-snapshot-1.1.3.tgz", "_integrity": "sha512-U0r8pRXsLAdxSVAyGNcqOU2H3Z4Y2dAAGGelL50O0QRMdi1WWeYHdrH/QWpN1e8juWfVKsb8B+pyJwTC+4Gy9w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "9.6.7", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.1.3_1704442897667_0.09414055040272018", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@vitest/snapshot", "version": "1.2.0", "license": "MIT", "_id": "@vitest/snapshot@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2fcddb5c6e8a9d2fc9f18ea2f8fd39b1b6e691b4", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-P33EE7TrVgB3HDLllrjK/GG6WSnmUtWohbwcQqmm7TAk9AVHpdgf7M3F3qRHKm6vhr7x3eGIln7VH052Smo6Kw==", "signatures": [{"sig": "MEUCIQC3x1FeN9d5nG5wEd9volI5eeUOMRTzS/Jigg/aFVQCUQIgS/DKnOiXXp0plHNB7gkchEMv1f/96tKoj2jU/Tto82I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/cb0a98d3c88a8a676e683d2a579f9dc1/vitest-snapshot-1.2.0.tgz", "_integrity": "sha512-P33EE7TrVgB3HDLllrjK/GG6WSnmUtWohbwcQqmm7TAk9AVHpdgf7M3F3qRHKm6vhr7x3eGIln7VH052Smo6Kw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.3", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.2.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.2.0_1705075646587_0.9897588099760108", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@vitest/snapshot", "version": "1.2.1", "license": "MIT", "_id": "@vitest/snapshot@1.2.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bd2dcae2322b90bab1660421ff9dae73fc84ecc0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.1.tgz", "fileCount": 13, "integrity": "sha512-Tmp/IcYEemKaqAYCS08sh0vORLJkMr0NRV76Gl8sHGxXT5151cITJCET20063wk0Yr/1koQ6dnmP6eEqezmd/Q==", "signatures": [{"sig": "MEUCIF+T+fdDd9DBG2xSQEyOyaNkDOaBQQQP+4dUlvIjElfIAiEAvNX4ibjwOVR/bTJW3j1H0WJj6dCqRd5tI4Cm8o63y8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/651ce9bbd5626a6d69d1bba424d26c23/vitest-snapshot-1.2.1.tgz", "_integrity": "sha512-Tmp/IcYEemKaqAYCS08sh0vORLJkMr0NRV76Gl8sHGxXT5151cITJCET20063wk0Yr/1koQ6dnmP6eEqezmd/Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.3", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.2.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.2.1_1705508647220_0.3856951910448212", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@vitest/snapshot", "version": "1.2.2", "license": "MIT", "_id": "@vitest/snapshot@1.2.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f56fd575569774968f3eeba9382a166c26201042", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.2.tgz", "fileCount": 13, "integrity": "sha512-SmGY4saEw1+bwE1th6S/cZmPxz/Q4JWsl7LvbQIky2tKE35US4gd0Mjzqfr84/4OD0tikGWaWdMja/nWL5NIPA==", "signatures": [{"sig": "MEUCIQCZzXVII7OSDQcRR7D7VrtRbHNUdctYlhKEAYcDce0WHwIgCNX+eUBDGsj84rTa+NbsjZmgODsQRFJMah0VhNDFDGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/e9c500043d78ac4930c64b6f5717621e/vitest-snapshot-1.2.2.tgz", "_integrity": "sha512-SmGY4saEw1+bwE1th6S/cZmPxz/Q4JWsl7LvbQIky2tKE35US4gd0Mjzqfr84/4OD0tikGWaWdMja/nWL5NIPA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.3", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.2.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.2.2_1706286356319_0.9277615201433447", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@vitest/snapshot", "version": "1.3.0", "license": "MIT", "_id": "@vitest/snapshot@1.3.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "016b34289d87ef0c64f4cdb9173086c2edf1db7b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.3.0.tgz", "fileCount": 13, "integrity": "sha512-swmktcviVVPYx9U4SEQXLV6AEY51Y6bZ14jA2yo6TgMxQ3h+ZYiO0YhAHGJNp0ohCFbPAis1R9kK0cvN6lDPQA==", "signatures": [{"sig": "MEUCIQDO5AI6gOpp542C/u/vvw/bULNYE9JjRpZ9hqvyTmL4WgIgedbYxQlkcW7NOlRUc0xQwDtFNEdb8cmbreSPHtIaU4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66700}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.3.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/58cff26d43843936b736262e224f8d43/vitest-snapshot-1.3.0.tgz", "_integrity": "sha512-swmktcviVVPYx9U4SEQXLV6AEY51Y6bZ14jA2yo6TgMxQ3h+ZYiO0YhAHGJNp0ohCFbPAis1R9kK0cvN6lDPQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.3", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.3.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.3.0_1708104551449_0.5825409176994356", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@vitest/snapshot", "version": "1.3.1", "license": "MIT", "_id": "@vitest/snapshot@1.3.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "193a5d7febf6ec5d22b3f8c5a093f9e4322e7a88", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.3.1.tgz", "fileCount": 13, "integrity": "sha512-EF++BZbt6RZmOlE3SuTPu/NfwBF6q4ABS37HHXzs2LUVPBLx2QoY/K0fKpRChSo8eLiuxcbCVfqKgx/dplCDuQ==", "signatures": [{"sig": "MEUCIHV1zpUwsVNSYcs+KeVZ20Fblbr8wEuOu+uUGGP8YPfyAiEA5at23sI1UQes4UTkRKDzWTA702g4z4Ue9tGmuQNsZzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.3.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d65fc2b0a8f852798bbcae83eadb0e03/vitest-snapshot-1.3.1.tgz", "_integrity": "sha512-EF++BZbt6RZmOlE3SuTPu/NfwBF6q4ABS37HHXzs2LUVPBLx2QoY/K0fKpRChSo8eLiuxcbCVfqKgx/dplCDuQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.4", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.3.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.3.1_1708436914294_0.4423767426861709", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@vitest/snapshot", "version": "1.4.0", "license": "MIT", "_id": "@vitest/snapshot@1.4.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2945b3fb53767a3f4f421919e93edfef2935b8bd", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.4.0.tgz", "fileCount": 13, "integrity": "sha512-saAFnt5pPIA5qDGxOHxJ/XxhMFKkUSBJmVt5VgDsAqPTX6JP326r5C/c9UuCMPoXNzuudTPsYDZCoJ5ilpqG2A==", "signatures": [{"sig": "MEUCIEEAIBI3MaYPmMOzyF3HrRNIe1RZNs2XGTj2RG4bB0/fAiEAuz68ILqJeVJhtqIovmqhH8Oim3361XtrTOt0+4NvSRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.4.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f040dd17f3f2943bbc8dc8dcaaf77f75/vitest-snapshot-1.4.0.tgz", "_integrity": "sha512-saAFnt5pPIA5qDGxOHxJ/XxhMFKkUSBJmVt5VgDsAqPTX6JP326r5C/c9UuCMPoXNzuudTPsYDZCoJ5ilpqG2A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.2.4", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.4.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.4.0_1710498655958_0.5701556757548201", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@vitest/snapshot", "version": "1.5.0", "license": "MIT", "_id": "@vitest/snapshot@1.5.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cd2d611fd556968ce8fb6b356a09b4593c525947", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.0.tgz", "fileCount": 13, "integrity": "sha512-qpv3fSEuNrhAO3FpH6YYRdaECnnRjg9VxbhdtPwPRnzSfHVXnNzzrpX4cJxqiwgRMo7uRMWDFBlsBq4Cr+rO3A==", "signatures": [{"sig": "MEYCIQCgEh2nd1mdqudVxgPY8kM+iLdG/1vdHjCHNS3HQfq9VgIhAIkF9FeWd9tnYCBeoaAvzPlN0ZjiXvzdo05l0LEk1/oa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/aa5e71688c0e36c262c659d28b7d60dd/vitest-snapshot-1.5.0.tgz", "_integrity": "sha512-qpv3fSEuNrhAO3FpH6YYRdaECnnRjg9VxbhdtPwPRnzSfHVXnNzzrpX4cJxqiwgRMo7uRMWDFBlsBq4Cr+rO3A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.5.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.5.0_1712857686580_0.5661579567146633", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@vitest/snapshot", "version": "1.5.1", "license": "MIT", "_id": "@vitest/snapshot@1.5.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "56f459f9327567378ee111384fadcda91d8e6be3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.1.tgz", "fileCount": 13, "integrity": "sha512-h/1SGaZYXmjn6hULRBOlqam2z4oTlEe6WwARRzLErAPBqljAs6eX7tfdyN0K+MpipIwSZ5sZsubDWkCPAiVXZQ==", "signatures": [{"sig": "MEQCIFY4uYL8ljwyQiDN+yiUUCortI0b+jcVctuzlJ/hES8+AiAmnx3yFYedrZkVl/v3QsYJ3Yh0uD5exYZOzpc4c1nrUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.5.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/01b561a991558ff69f91f3c050dd97ba/vitest-snapshot-1.5.1.tgz", "_integrity": "sha512-h/1SGaZYXmjn6hULRBOlqam2z4oTlEe6WwARRzLErAPBqljAs6eX7tfdyN0K+MpipIwSZ5sZsubDWkCPAiVXZQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.5.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.5.1_1713957752135_0.5747075067411493", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@vitest/snapshot", "version": "1.5.2", "license": "MIT", "_id": "@vitest/snapshot@1.5.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d6f8a5d0da451e1c4dc211fcede600becf4851ed", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.2.tgz", "fileCount": 13, "integrity": "sha512-CTEp/lTYos8fuCc9+Z55Ga5NVPKUgExritjF5VY7heRFUfheoAqBneUlvXSUJHUZPjnPmyZA96yLRJDP1QATFQ==", "signatures": [{"sig": "MEQCIFcNCfQC6CnJJeK7sKj0YRNZI/ZNkHF4huKilIFkMbpJAiAeTnM5q+p7hUNVoAqfTX5d+EvIKLo0acRIhuN95WZi/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.5.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f2963e4e61ccdd2e99e327a1363d7d5f/vitest-snapshot-1.5.2.tgz", "_integrity": "sha512-CTEp/lTYos8fuCc9+Z55Ga5NVPKUgExritjF5VY7heRFUfheoAqBneUlvXSUJHUZPjnPmyZA96yLRJDP1QATFQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.5.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.5.2_1714036329546_0.5544561325090249", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@vitest/snapshot", "version": "1.5.3", "license": "MIT", "_id": "@vitest/snapshot@1.5.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ffdd917daebf4415c7abad6993bafd5f4ee14aaf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.3.tgz", "fileCount": 13, "integrity": "sha512-K3mvIsjyKYBhNIDujMD2gfQEzddLe51nNOAf45yKRt/QFJcUIeTQd2trRvv6M6oCBHNVnZwFWbQ4yj96ibiDsA==", "signatures": [{"sig": "MEUCIQDwmif68crclsukmJSwAnomzoxvo0mjvHOZXoXoBEZ8sAIgG11+D5E/ayV4/V4LXWrf9KGw+cbVY+AAaKtar4hoxHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.5.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2f5b0334cd12ee251f0c2f2bb83ef603/vitest-snapshot-1.5.3.tgz", "_integrity": "sha512-K3mvIsjyKYBhNIDujMD2gfQEzddLe51nNOAf45yKRt/QFJcUIeTQd2trRvv6M6oCBHNVnZwFWbQ4yj96ibiDsA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.5.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.5.3_1714466433682_0.2630711304519071", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@vitest/snapshot", "version": "1.6.0", "license": "MIT", "_id": "@vitest/snapshot@1.6.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "deb7e4498a5299c1198136f56e6e0f692e6af470", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.6.0.tgz", "fileCount": 13, "integrity": "sha512-+Hx43f8Chus+DCmygqqfetcAZrDJwvTj0ymqjQq4CvmpKFSTVteEOBzCusu1x2tt4OJcvBflyHUE0DZSLgEMtQ==", "signatures": [{"sig": "MEUCIFFUAtS4rXM79ZQDi9I7oIGXhy/ebLPrHF2UC+DQhBYBAiEA4wluq5K4ltyKCbzZLq7mP2R+WDmNCtLvrpQcJDPQA4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.6.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c5bc0d9d773052c18b9dda985c5c824f/vitest-snapshot-1.6.0.tgz", "_integrity": "sha512-+Hx43f8Chus+DCmygqqfetcAZrDJwvTj0ymqjQq4CvmpKFSTVteEOBzCusu1x2tt4OJcvBflyHUE0DZSLgEMtQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.6.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.6.0_1714749743824_0.15952566601732943", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "@vitest/snapshot", "version": "2.0.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "18f841d58b9c0014a6ef9ca394f97874386b5fa6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-DbzrV6fQtB1jPvlL/eW+bEYWY5MMGPcTr3isvrJHo2NtTt1pntm01kj9IbLIFyp5ew1gxqFQ19M0YowwiWv8Yw==", "signatures": [{"sig": "MEYCIQC+JolREeOFiOrsvzTibJVe2lXGuFKyxwGDcW6DkclQ7wIhALMAaTCpDURAh0RiOxWUNTxWI68MpkOkaJfQhgPgWjgS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66873}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/aaabda5a1fe279ff0f98c647312c0762/vitest-snapshot-2.0.0-beta.1.tgz", "_integrity": "sha512-DbzrV6fQtB1jPvlL/eW+bEYWY5MMGPcTr3isvrJHo2NtTt1pntm01kj9IbLIFyp5ew1gxqFQ19M0YowwiWv8Yw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.1_1715265154754_0.8432561642515781", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "@vitest/snapshot", "version": "2.0.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9510cf3681913ed5463b263998aef71ca4b7e4ab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-011YsVfsi1i4s4Csptj+YqDmt4jr5tZlhe8uOVRdbICfuqg8y1xe2d5QnBVgxrJTXa+pX16z6aSUjU+oUTV/ZA==", "signatures": [{"sig": "MEYCIQCytaqb0POixRyZhiDgnOKE1WvWuJvbm+pcTQCyTwGC1AIhAKg8jrFa6G1nRkImEeLoQh/CQTRbArwYkEmlOxYb/GHD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66873}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/181e9f2d6248368c7d8a9fd8c96b3410/vitest-snapshot-2.0.0-beta.2.tgz", "_integrity": "sha512-011YsVfsi1i4s4Csptj+YqDmt4jr5tZlhe8uOVRdbICfuqg8y1xe2d5QnBVgxrJTXa+pX16z6aSUjU+oUTV/ZA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.2_1715268692227_0.7226925703942928", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "@vitest/snapshot", "version": "2.0.0-beta.3", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "99d384bd90bab46c74edd83211d5356078f6a77d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-39Clmuu7TQjcOBMs+gbWt3T93Ud+gxa3iTaI0DaTgXjQZLu03qeAhYS6iOyw257A6Nt2+TSGZhTlqm3wyYbl2Q==", "signatures": [{"sig": "MEUCIQCd17coR/BpomJnrMgOGecl4IlnsLY6TEibqKrZQawBJgIgB057fxVn6Kt1Tc+m+gMzP0yIiK1SEia6bGdd95gvhSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67029}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a924e3f536a854d419923690bef3d22c/vitest-snapshot-2.0.0-beta.3.tgz", "_integrity": "sha512-39Clmuu7TQjcOBMs+gbWt3T93Ud+gxa3iTaI0DaTgXjQZLu03qeAhYS6iOyw257A6Nt2+TSGZhTlqm3wyYbl2Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.3_1715712288916_0.6136287166861605", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "@vitest/snapshot", "version": "2.0.0-beta.4", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f16dbc64ae63496a2f110775e15ba3f58cedc5c1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-enIHNZ59Fgj75jrCrFnvn1dSm2yybkEsW0PboMvEWDAz4JO7CdzrvOzqSns7zMWnkPqgLB7//Ts372me0aGLOA==", "signatures": [{"sig": "MEUCIQDGLqe3gPjcZFkX8wqJa9RBv8nPTHFkU1shu1A7J+1BIAIgM9PHStZeJ2pjD9r27Sh+PceZx+iAx+Kag4kCe9eQrsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8f15a81dac293fc2b5cc5248763b0d6e/vitest-snapshot-2.0.0-beta.4.tgz", "_integrity": "sha512-enIHNZ59Fgj75jrCrFnvn1dSm2yybkEsW0PboMvEWDAz4JO7CdzrvOzqSns7zMWnkPqgLB7//Ts372me0aGLOA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.4_1717330562916_0.2840120316060095", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "@vitest/snapshot", "version": "2.0.0-beta.5", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ed2c1ba86a68744726e13a91b0d7e5684fc669e5", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-+KkkOX/WoUxUP1UNwhPLvIjNJ5YFGODS8giJWQQ9BnAp2kotuEsmehhUia59rzLlq42Ql+6YTohACj+0fVCxbw==", "signatures": [{"sig": "MEYCIQCYZFboAd1tLJOzAL18BdKErxJ7pRb+mVdLd+JMetnr1gIhAOYA++EyKWXAkPAifvjodJZcqYzdGjVKGJbLLswknmhU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6aa72de7e8a4ce1687beda854c511afa/vitest-snapshot-2.0.0-beta.5.tgz", "_integrity": "sha512-+KkkOX/WoUxUP1UNwhPLvIjNJ5YFGODS8giJWQQ9BnAp2kotuEsmehhUia59rzLlq42Ql+6YTohACj+0fVCxbw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.5_1717331275741_0.4372919031632396", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "@vitest/snapshot", "version": "2.0.0-beta.6", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "229a5f04816a9c83faeb85b8ce4330032762004c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-fHtm24q05OLGI74z2ICRfJ0FvhdXlnFZlmzwDV6mdO2Mf1t2GQLrZ1+622X5uhSGEcwKJm9VSuD5jJJ45FyqYQ==", "signatures": [{"sig": "MEUCIQD0HE+IDDeV77mMQA396ZPP1KHlzZEf5vh4KmiG+o2X1QIgCFOSPSzaP7ZgR+KgkAeT//9VCmQBxgQJ9E+6E39PcI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/36881cd28144cfd51abe408b1ad8f199/vitest-snapshot-2.0.0-beta.6.tgz", "_integrity": "sha512-fHtm24q05OLGI74z2ICRfJ0FvhdXlnFZlmzwDV6mdO2Mf1t2GQLrZ1+622X5uhSGEcwKJm9VSuD5jJJ45FyqYQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.6_1717355852833_0.682383559348722", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "@vitest/snapshot", "version": "2.0.0-beta.7", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "92b57ab48a07f358c336f091030e283ff8fb4ebe", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-ilFSwedpQ3d9Ue136a5iVRboZ7Pyhwm1mSRHeUHIkVg5gAW/XqgeewQKvn1Gl7MqmFL+mfCnon9r9o2wC7pfWg==", "signatures": [{"sig": "MEUCIFJKfS9Qf58RoU7Gow7je9H5g2Lux69k1yZo56SS5LqdAiEA9oDP5BrTRbWzSaXUnnX7FOThVb3c2fGp6CagSzI1uCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/042062eacf56ae65c0ac994bee27abda/vitest-snapshot-2.0.0-beta.7.tgz", "_integrity": "sha512-ilFSwedpQ3d9Ue136a5iVRboZ7Pyhwm1mSRHeUHIkVg5gAW/XqgeewQKvn1Gl7MqmFL+mfCnon9r9o2wC7pfWg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.7_1717414556195_0.29656935178734156", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "@vitest/snapshot", "version": "2.0.0-beta.8", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.8", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c2cfb8cb4b0b245bc34d08823fc9dd2b0b0e490f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.8.tgz", "fileCount": 13, "integrity": "sha512-dUv6FE1qlGvLhLiLqTr2YcxohX/eevobaRsGJRll/NqUot4ugB46N1pTxx+VCiyVETuTkr+hjOWJvyxfWpXtdg==", "signatures": [{"sig": "MEQCIDZbcuu6Uu1RYvif/MbBrP8RTCerpqHk2aDWCF03c3GPAiBJOUH86h4S7LcaK5MlgHQ/1N4TLYhzcF/d9Z+mkx/bUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bc11cfc88131f79f97ad4234bc053821/vitest-snapshot-2.0.0-beta.8.tgz", "_integrity": "sha512-dUv6FE1qlGvLhLiLqTr2YcxohX/eevobaRsGJRll/NqUot4ugB46N1pTxx+VCiyVETuTkr+hjOWJvyxfWpXtdg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.8_1717504772718_0.032635185320724514", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "@vitest/snapshot", "version": "2.0.0-beta.9", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.9", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e2eea75e7aa6169ba7b46cd26d0ebdb47b86b313", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.9.tgz", "fileCount": 13, "integrity": "sha512-ZVyTIN4WnflZs5GEoWdU7/24zRNbzgLH+mAVvANs4wNRMAcDhdn+MkqXTQlKSzARN3WhGrMfvx7KXwO8pK1pxw==", "signatures": [{"sig": "MEYCIQCjIAAmL+kCK1pFZc/JBweC7aCIVZ+tER25WsMzYmivjAIhAI9zb0p4Uyq7GSRzkUq/90oVYYLAHl4Q34XlAMR6brXu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/39dd03baa8a62b63c117203da2cd8707/vitest-snapshot-2.0.0-beta.9.tgz", "_integrity": "sha512-ZVyTIN4WnflZs5GEoWdU7/24zRNbzgLH+mAVvANs4wNRMAcDhdn+MkqXTQlKSzARN3WhGrMfvx7KXwO8pK1pxw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.5.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.9_1717574469647_0.5799732646241973", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "@vitest/snapshot", "version": "2.0.0-beta.10", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.10", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "87c72e8cfb87b51bc653997a4396ab089f8e38f2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.10.tgz", "fileCount": 13, "integrity": "sha512-SvvnBwcGsxI++T0WF8Py5LGCzJE2s5x2Bs6hCTiGVZPqmCrmCLVPeRQCLzYyX3LVRUHgkqgfmP7grUHDKusMJg==", "signatures": [{"sig": "MEYCIQCGZhcOtReSPN3jJaAqhY0iZfxbYOAOSR9IwWYV7aAy1wIhAMZrIVcpAJdi5JhkK2Euo5RHGLVldB2eh1v6V3RHGDAp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67221}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d16b264f6805c74409662340b95f4cf1/vitest-snapshot-2.0.0-beta.10.tgz", "_integrity": "sha512-SvvnBwcGsxI++T0WF8Py5LGCzJE2s5x2Bs6hCTiGVZPqmCrmCLVPeRQCLzYyX3LVRUHgkqgfmP7grUHDKusMJg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.10", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.10_1718194304337_0.5928176249132089", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "@vitest/snapshot", "version": "2.0.0-beta.11", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.11", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b77326f1e19d48cdf2e0b42a1e0e72cb1f40c0cc", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.11.tgz", "fileCount": 13, "integrity": "sha512-F3LRgdEy0o3mFea4E9KpPyrYVoHhaXjOyMHz9f4Ie2OFZ89BH+750lX1Hp/69MgLwfaIziQP1NHbxOuAmXYU6g==", "signatures": [{"sig": "MEQCIGfhp6Yf00+x3mQuowm3nHF9XEFfhfh7JPY+9y0fKI+7AiANhOL+gRLyPK5FN84uOVhqeTV2MID9QymnaZii5gqLUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69169}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.11.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0867273a63e21422f19bf941dda371e6/vitest-snapshot-2.0.0-beta.11.tgz", "_integrity": "sha512-F3LRgdEy0o3mFea4E9KpPyrYVoHhaXjOyMHz9f4Ie2OFZ89BH+750lX1Hp/69MgLwfaIziQP1NHbxOuAmXYU6g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.11", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.11_1718828047916_0.980296892986084", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.12": {"name": "@vitest/snapshot", "version": "2.0.0-beta.12", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.12", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "510f24f9ab72a98b1c7fab3b556650fe11da3a9c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.12.tgz", "fileCount": 13, "integrity": "sha512-NBqn1rTNQ/e3Dsw8LnniHgeZslgIxg8UvSfje/QV3hJLSoLMLbKLopHmK9T2FQA0hcibAaq/TZVyVrBoX+6aig==", "signatures": [{"sig": "MEQCIA1W6T21ND7sY8Habc9XZ8Sonn5OGJkuaH/jvMVtEIhQAiA71/oGCDeLz988eTfrXQt84d7RQCG5Rgjz3PpFhy4iXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69227}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.12.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0a0d87598c9759a51ce1027ef494e145/vitest-snapshot-2.0.0-beta.12.tgz", "_integrity": "sha512-NBqn1rTNQ/e3Dsw8LnniHgeZslgIxg8UvSfje/QV3hJLSoLMLbKLopHmK9T2FQA0hcibAaq/TZVyVrBoX+6aig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.12", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.12_1719346584600_0.9798445663871365", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.13": {"name": "@vitest/snapshot", "version": "2.0.0-beta.13", "license": "MIT", "_id": "@vitest/snapshot@2.0.0-beta.13", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5dd8997a5de96238a917d8e08e7bb14e68ecaa2b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.13.tgz", "fileCount": 13, "integrity": "sha512-avQ7e6LoRKjmA0Fv4Iw9fxvFf6I+XtoCObTl0EdCMdrzGUMSPjFx1fYz5NaCd7iuGW3fQrBpmm/TV/Sd9i8gtw==", "signatures": [{"sig": "MEQCIGSp0mxWgVayXbPCgMkWlbN4y3Vdrg0YugjdXLDnZTOCAiB1Hpt5IS12juXKYrLqxf77hQRoQbARH/gPX57ewSbkWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69318}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0-beta.13.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ee294b812c7a90990f12ed6341d0120e/vitest-snapshot-2.0.0-beta.13.tgz", "_integrity": "sha512-avQ7e6LoRKjmA0Fv4Iw9fxvFf6I+XtoCObTl0EdCMdrzGUMSPjFx1fYz5NaCd7iuGW3fQrBpmm/TV/Sd9i8gtw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.0.0-beta.13", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0-beta.13_1720101830055_0.518690053196553", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vitest/snapshot", "version": "2.0.0", "license": "MIT", "_id": "@vitest/snapshot@2.0.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "728d70f22de9e32e82b6ac41ebbe54d8574244bf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0.tgz", "fileCount": 13, "integrity": "sha512-B520cSAQwtWgocPpARadnNLslHCxFs5tf7SG2TT96qz+SZgsXqcB1xI3w3/S9kUzdqykEKrMLvW+sIIpMcuUdw==", "signatures": [{"sig": "MEYCIQCmEMGMY+EN+lf51zRxg9hYN7Pd0rkdzbTEZAPu61tc+gIhAJ1XQ7JHvW9CZjY3QxxH9J+VmyhDXBooQGtRn6XVZZfn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69302}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1de945c9d850b90deda202a77e8bfdc3/vitest-snapshot-2.0.0.tgz", "_integrity": "sha512-B520cSAQwtWgocPpARadnNLslHCxFs5tf7SG2TT96qz+SZgsXqcB1xI3w3/S9kUzdqykEKrMLvW+sIIpMcuUdw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.0_1720438770495_0.9805920141794378", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vitest/snapshot", "version": "2.0.1", "license": "MIT", "_id": "@vitest/snapshot@2.0.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "67112ac546417657f7802456d329f62ead07deab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.1.tgz", "fileCount": 13, "integrity": "sha512-rst79a4Q+J5vrvHRapdfK4BdqpMH0eF58jVY1vYeBo/1be+nkyenGI5SCSohmjf6MkCkI20/yo5oG+0R8qrAnA==", "signatures": [{"sig": "MEQCIDamZnXVah3q+PDHm0Zp3f/btQYvjR9MyL3N0AoYimIQAiBgX12JF2fs09YJ9OjNO1Uyl4HvKv3F/c1GCG3xpND50w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69302}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/de33d177ec5711c6af4bae2c1f9f1796/vitest-snapshot-2.0.1.tgz", "_integrity": "sha512-rst79a4Q+J5vrvHRapdfK4BdqpMH0eF58jVY1vYeBo/1be+nkyenGI5SCSohmjf6MkCkI20/yo5oG+0R8qrAnA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.1_1720452787619_0.7243135719226288", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@vitest/snapshot", "version": "2.0.2", "license": "MIT", "_id": "@vitest/snapshot@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "91a8b847d82d92d06b9bf70b72bb9f21a4a416a1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.2.tgz", "fileCount": 13, "integrity": "sha512-Yc2ewhhZhx+0f9cSUdfzPRcsM6PhIb+S43wxE7OG0kTxqgqzo8tHkXFuFlndXeDMp09G3sY/X5OAo/RfYydf1g==", "signatures": [{"sig": "MEYCIQDVpswh+eiGc3XOedh97AzKrUsUVL6rBFiF2jSnDzkIMwIhAJE+QMDhHgJHdbGPh20uSsadliAXt+UAGj3GJS9WBBkl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/59e872cf203544eb878b323d78130c05/vitest-snapshot-2.0.2.tgz", "_integrity": "sha512-Yc2ewhhZhx+0f9cSUdfzPRcsM6PhIb+S43wxE7OG0kTxqgqzo8tHkXFuFlndXeDMp09G3sY/X5OAo/RfYydf1g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.2_1720626400430_0.2989034654402556", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/snapshot", "version": "2.0.3", "license": "MIT", "_id": "@vitest/snapshot@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "31acf5906f8c12f9c7fde21b84cc28f043e983b1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.3.tgz", "fileCount": 13, "integrity": "sha512-6OyA6v65Oe3tTzoSuRPcU6kh9m+mPL1vQ2jDlPdn9IQoUxl8rXhBnfICNOC+vwxWY684Vt5UPgtcA2aPFBb6wg==", "signatures": [{"sig": "MEUCIQCZsI3it5DgbKx509IJ6+CyTwq6SikHd0xpo4wk4geBiAIgH0EOq6z6e1ELZoZL7ydjiWVDuXcOj8fPFlbmRVe1g+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e9e5934f68189dcf2a28c00c8d3ffce9/vitest-snapshot-2.0.3.tgz", "_integrity": "sha512-6OyA6v65Oe3tTzoSuRPcU6kh9m+mPL1vQ2jDlPdn9IQoUxl8rXhBnfICNOC+vwxWY684Vt5UPgtcA2aPFBb6wg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.3_1721037817591_0.7914146890153315", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/snapshot", "version": "2.0.4", "license": "MIT", "_id": "@vitest/snapshot@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7d7dea9df17c5c13386f1a7a433b99dc0ffe3c14", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.4.tgz", "fileCount": 13, "integrity": "sha512-or6Mzoz/pD7xTvuJMFYEtso1vJo1S5u6zBTinfl+7smGUhqybn6VjzCDMhmTyVOFWwkCMuNjmNNxnyXPgKDoPw==", "signatures": [{"sig": "MEUCIFFE2Rob7S8bKTozQfF1n8THsc9PSfFwEyO8WEv2Aw26AiEA7oage/vA+xuW/zfDAnqWCtaVFJ09a0JE/Dlr2FGQ30Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c50af0148772c6cf95713f7f77b4f0ec/vitest-snapshot-2.0.4.tgz", "_integrity": "sha512-or6Mzoz/pD7xTvuJMFYEtso1vJo1S5u6zBTinfl+7smGUhqybn6VjzCDMhmTyVOFWwkCMuNjmNNxnyXPgKDoPw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.4_1721639611754_0.5437677090179844", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/snapshot", "version": "2.0.5", "license": "MIT", "_id": "@vitest/snapshot@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a2346bc5013b73c44670c277c430e0334690a162", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.5.tgz", "fileCount": 13, "integrity": "sha512-SgCPUeDFLaM0mIUHfaArq8fD2WbaXG/zVXjRupthYfYGzc8ztbFbu6dUNOblBG7XLMR1kEhS/DNnfCZ2IhdDew==", "signatures": [{"sig": "MEUCIQDx9z9ce5026Id9etdYlro78UtT5M191wfrjIQLaT5WkAIgBTAmQO8VI/G/jPeSzHHQC9sgypTUyk26np2bYV/QckE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/484e9b9c0b340817ce4644df7b2dc6c0/vitest-snapshot-2.0.5.tgz", "_integrity": "sha512-SgCPUeDFLaM0mIUHfaArq8fD2WbaXG/zVXjRupthYfYGzc8ztbFbu6dUNOblBG7XLMR1kEhS/DNnfCZ2IhdDew==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.7.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.0.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.0.5_1722422404743_0.13271584270658643", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/snapshot", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "74c356d24f325f3b76b5989f8a8ad6d956448844", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-O+xAqshSFXoeLfzlKkYN+7CwWJwyujk5awjuJOpXrceF5RMgoIy7GZ/o5KKbloVoafOIokmM8WHGbszGBe5Csg==", "signatures": [{"sig": "MEUCIF9R6/vxqzF6mNndQ0Mg55CJNQy/TgJRsxxWphrvMYgAAiEAri674bzTEIfay9BzNKGa36tRt99WxzijV/mpxeRH5XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6f338a09634fe7781b6c1abeea640e26/vitest-snapshot-2.1.0-beta.1.tgz", "_integrity": "sha512-O+xAqshSFXoeLfzlKkYN+7CwWJwyujk5awjuJOpXrceF5RMgoIy7GZ/o5KKbloVoafOIokmM8WHGbszGBe5Csg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.1_1723011691385_0.8597385894975573", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/snapshot", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "74f883b35983a9f87ae750b2ffec19a434e19ce8", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-J48yhZ+CFs7X7mGeyzSY4SLYIkZpiKCgUkG17WFdV6dZaNKVV6aLKS3E040d2+kv7YDY9gJVuA8Pe32s2eiq0g==", "signatures": [{"sig": "MEUCICddUq5yxAGxwY3UiSjehJWli8BfzQirlWez2qqyf4X/AiEAlZQy1IcD5ERy8Injk4u+YAOYlro0jJev4hYz7MMcjF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cb8bbba336e7a1df4aeffdf8dcff7717/vitest-snapshot-2.1.0-beta.2.tgz", "_integrity": "sha512-J48yhZ+CFs7X7mGeyzSY4SLYIkZpiKCgUkG17WFdV6dZaNKVV6aLKS3E040d2+kv7YDY9gJVuA8Pe32s2eiq0g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.2_1723017415805_0.2501163401036448", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/snapshot", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4598861cf92adb39ae9ecd852772e3fb5441628c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-r0a3lMoDCjly3C+ep3BXcymDVs4KmwaWSddH7LQup9zuL3LmwrPyOq9nFCyyOMgsyPSRuzboGJkOsZlMAZducw==", "signatures": [{"sig": "MEQCIHpXpd0KnmYHUtsi13nJEzAfLR2d8DkXRXHaQVfvs8WdAiAQGTWRXvG+SU47CLGBWYYjD96RHMSc4IST8QODdWk4Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8478ff5c8d45792e5b1417efa0900dec/vitest-snapshot-2.1.0-beta.3.tgz", "_integrity": "sha512-r0a3lMoDCjly3C+ep3BXcymDVs4KmwaWSddH7LQup9zuL3LmwrPyOq9nFCyyOMgsyPSRuzboGJkOsZlMAZducw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.3_1723018628114_0.6947933108768121", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/snapshot", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5cd9c8d1bdc10d3818a3b3c1a962ec3775f8463f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-wVMeZOUenmWBGKLGqcVFupRJtb0Y1SGafyQtDr7JlH5D/czHlPye89C4eb83Jp77luQFJKghmRtmr6B/oHokLQ==", "signatures": [{"sig": "MEUCIEmkES5Egg0af6nXeGwQb1FZnYFGj8GOhQEvIWtQIgNgAiEA8mF7wlUjDHjFUFe4rZ644eDnkOxyaPC9m7hx+HIWQZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/292a5b13a1df6bed5d26d804fd6679fe/vitest-snapshot-2.1.0-beta.4.tgz", "_integrity": "sha512-wVMeZOUenmWBGKLGqcVFupRJtb0Y1SGafyQtDr7JlH5D/czHlPye89C4eb83Jp77luQFJKghmRtmr6B/oHokLQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.4_1723030977377_0.41154219616033405", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/snapshot", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "047cc73271ab94b698a1e6f58651feeaac8e4355", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-XlDzkkRN7o4QHHF5/1R4HL82wbzQWvKBpRedNSvSr0BKZCZEWOXdOacOjOTDh8IHfJ+ACD9N/gaIObbBGop+0A==", "signatures": [{"sig": "MEQCIAiQqYt8db/0jJXmLr1TV/CJNX7ZKTiuAZ0JHz53jmi4AiB1ZIxKzIe6Ph3yMdULS/nR0th4sSOyRqk69HS1K16IZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b65c6f12177323097160226f5769ac5f/vitest-snapshot-2.1.0-beta.5.tgz", "_integrity": "sha512-XlDzkkRN7o4QHHF5/1R4HL82wbzQWvKBpRedNSvSr0BKZCZEWOXdOacOjOTDh8IHfJ+ACD9N/gaIObbBGop+0A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.5_1723462524775_0.00577993314246017", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/snapshot", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "402d5eb106b40482388a332d5ea5b75d3754f1b1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-AuQc8NcbJhhUWxrVWllQStsN0A7cA5P+69d8ImszzOtInqtr6yHFziu4n9Xt5e0dNggS9edNYajkOS24m7YsKg==", "signatures": [{"sig": "MEUCICZelfrDhQVC3l9AQH5sLzuIkt0tW+qjhSN7GA+7THBhAiEA5jhOaJmlPC2XfSVcd88YZISZ70dLeEta4k0n0TRQ2Zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71944}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1623a1a3e91cf53ed99b8ba0aa3206c6/vitest-snapshot-2.1.0-beta.6.tgz", "_integrity": "sha512-AuQc8NcbJhhUWxrVWllQStsN0A7cA5P+69d8ImszzOtInqtr6yHFziu4n9Xt5e0dNggS9edNYajkOS24m7YsKg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.1", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.6_1724159918020_0.7518409933573973", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/snapshot", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/snapshot@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e4ee571773fbc4ac42added09a6356da646aeda2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-hxVlKPGFgEjJgWSSX5dH4ip7l8dFRB7o+7iruvssQIi0WmUbCIQYgz75FflFO9H3dIiuyafdAT/Dh38sAFjX/g==", "signatures": [{"sig": "MEUCIBc3pne41t8y5VvNvR41jryJ4dmPoWfbzeZJIcQn8HrRAiEAiwVDmxruxwkzgbjldRNUGGhrKywiwCfTF1mzgdzJtOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72310}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e5f29a4157657b1423e11ca0d28201b3/vitest-snapshot-2.1.0-beta.7.tgz", "_integrity": "sha512-hxVlKPGFgEjJgWSSX5dH4ip7l8dFRB7o+7iruvssQIi0WmUbCIQYgz75FflFO9H3dIiuyafdAT/Dh38sAFjX/g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.1.0-beta.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0-beta.7_1725894804101_0.46814049760213017", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/snapshot", "version": "2.1.0", "license": "MIT", "_id": "@vitest/snapshot@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f02a970874d8669b79bd44479b1ce5e5c547a2a3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0.tgz", "fileCount": 13, "integrity": "sha512-x69CygGMzt9VCO283K2/FYQ+nBrOj66OTKpsPykjCR4Ac3lLV+m85hj9reaIGmjBSsKzVvbxWmjWE3kF5ha3uQ==", "signatures": [{"sig": "MEQCIFEeiiUP5N120iUY/gnGpVKobeANhCP/SykzUguI0hX/AiAEmCO7TElJIk7yRHMnfB9P54NzXq0xUAkJeNsZNUiqqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72289}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3771f161957ab62811c4f20cab601ef4/vitest-snapshot-2.1.0.tgz", "_integrity": "sha512-x69CygGMzt9VCO283K2/FYQ+nBrOj66OTKpsPykjCR4Ac3lLV+m85hj9reaIGmjBSsKzVvbxWmjWE3kF5ha3uQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.0_1726149810950_0.11073309346605664", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/snapshot", "version": "2.1.1", "license": "MIT", "_id": "@vitest/snapshot@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "38ef23104e90231fea5540754a19d8468afbba66", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.1.tgz", "fileCount": 13, "integrity": "sha512-BnSku1WFy7r4mm96ha2FzN99AZJgpZOWrAhtQfoxjUU5YMRpq1zmHRq7a5K9/NjqonebO7iVDla+VvZS8BOWMw==", "signatures": [{"sig": "MEQCIBOeP22mqz6bRpUbusxH2tuZU2a79gc0xWvZVnm0dQLbAiBaO6EfQwj8AUlv3fDgRkV1wmXc2a86onygZvAFbmlPcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72289}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/73b1cbc51e6f64339da4f2483d566a4c/vitest-snapshot-2.1.1.tgz", "_integrity": "sha512-BnSku1WFy7r4mm96ha2FzN99AZJgpZOWrAhtQfoxjUU5YMRpq1zmHRq7a5K9/NjqonebO7iVDla+VvZS8BOWMw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.1_1726241564703_0.48487343772350733", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/snapshot", "version": "2.1.2", "license": "MIT", "_id": "@vitest/snapshot@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e20bd794b33fdcd4bfe69138baac7bb890c4d51f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.2.tgz", "fileCount": 13, "integrity": "sha512-xtAeNsZ++aRIYIUsek7VHzry/9AcxeULlegBvsdLncLmNCR6tR8SRjn8BbDP4naxtccvzTqZ+L1ltZlRCfBZFA==", "signatures": [{"sig": "MEUCIQCr35BOlmPo7D+peQdPqNV9O7cv95BWtc6/IWttDtfSDwIgCd3Yy72b5XkexGrRShhxqwro8290i64i5lnvpYxZyKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85249}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f6bb68def927c68cc621bbec39c8329d/vitest-snapshot-2.1.2.tgz", "_integrity": "sha512-xtAeNsZ++aRIYIUsek7VHzry/9AcxeULlegBvsdLncLmNCR6tR8SRjn8BbDP4naxtccvzTqZ+L1ltZlRCfBZFA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.2_1727886012809_0.8062786681480738", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/snapshot", "version": "2.1.3", "license": "MIT", "_id": "@vitest/snapshot@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1b405a9c40a82563605b13fdc045217751069e58", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.3.tgz", "fileCount": 13, "integrity": "sha512-qWC2mWc7VAXmjAkEKxrScWHWFyCQx/cmiZtuGqMi+WwqQJ2iURsVY4ZfAK6dVo6K2smKRU6l3BPwqEBvhnpQGg==", "signatures": [{"sig": "MEYCIQCZMsWDvJXE9rlPka5pBsvfURJueVgJmufT5p7W+ozxoQIhAKlTLcfRmV5a5Y+lHTbJskirzbvx0RyRF8x7tuOVbBMB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85249}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7e86d359e9045e781ae287963c268e6e/vitest-snapshot-2.1.3.tgz", "_integrity": "sha512-qWC2mWc7VAXmjAkEKxrScWHWFyCQx/cmiZtuGqMi+WwqQJ2iURsVY4ZfAK6dVo6K2smKRU6l3BPwqEBvhnpQGg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.3_1728903937388_0.25973173032831354", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/snapshot", "version": "2.1.4", "license": "MIT", "_id": "@vitest/snapshot@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ef8c3f605fbc23a32773256d37d3fdfd9b23d353", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.4.tgz", "fileCount": 13, "integrity": "sha512-3Kab14fn/5QZRog5BPj6Rs8dc4B+mim27XaKWFWHWA87R56AKjHTGcBFKpvZKDzC4u5Wd0w/qKsUIio3KzWW4Q==", "signatures": [{"sig": "MEYCIQD3nVDDa0936I977dGhPoViZ/nwHuvSpIREXgW24qnLmQIhAIu0vqCWjePoz7n9i4qbzxuH4T4RvYXinf+LbkWX4n+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/59554c50bebf7b237db6e3d56ebe0d7a/vitest-snapshot-2.1.4.tgz", "_integrity": "sha512-3Kab14fn/5QZRog5BPj6Rs8dc4B+mim27XaKWFWHWA87R56AKjHTGcBFKpvZKDzC4u5Wd0w/qKsUIio3KzWW4Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.4_1730118445862_0.26301398363896", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/snapshot", "version": "2.1.5", "license": "MIT", "_id": "@vitest/snapshot@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a09a8712547452a84e08b3ec97b270d9cc156b4f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.5.tgz", "fileCount": 13, "integrity": "sha512-zmYw47mhfdfnYbuhkQvkkzYroXUumrwWDGlMjpdUr4jBd3HZiV2w7CQHj+z7AAS4VOtWxI4Zt4bWt4/sKcoIjg==", "signatures": [{"sig": "MEUCIQC8/scFXBiQNhgsoFJk/87Xk+yS8y/mrv393s2HlCyVvwIgYDImYYH+NiDGw4MA4MBpqfV7cxCbRZmeRAMkefe35PY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/406074cf7997e0bb2b439ff66c43f710/vitest-snapshot-2.1.5.tgz", "_integrity": "sha512-zmYw47mhfdfnYbuhkQvkkzYroXUumrwWDGlMjpdUr4jBd3HZiV2w7CQHj+z7AAS4VOtWxI4Zt4bWt4/sKcoIjg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.5_1731511453836_0.8749493382727764", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/snapshot", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6dddab14f81b37d4f4641fbde9fbeabbe9fde4ab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-Pa208PgAYUmEK4+MKrDNbXEf21lly5OgS5KPemZYHhFaUwQXcCBndJikgn0N2JPcsf07IjPekIjzenes8ezNhw==", "signatures": [{"sig": "MEUCIFgok9NnsHYCN642gQdoNo1l+nppFwTqGRTUbeEd8q2CAiEAsbHuvOz6ghBG2OQwEoIiHp5b58vsaXaNc9ykVpeHCbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85343}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/99886eeede15481501a5b482890dd6bc/vitest-snapshot-2.2.0-beta.1.tgz", "_integrity": "sha512-Pa208PgAYUmEK4+MKrDNbXEf21lly5OgS5KPemZYHhFaUwQXcCBndJikgn0N2JPcsf07IjPekIjzenes8ezNhw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.2.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.2.0-beta.1_1731518245420_0.9964158539730927", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/snapshot", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "89ff8d9b729317645016e41237424b3b2d4e2120", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-fOXZlA1enCyeGvTiV/ZFFDaI9vPadKu3E/Q5LKVcXBqCPHDnRDpa6UNP6N7l74CchXj8y0LaIewkFxsq3FpBSg==", "signatures": [{"sig": "MEYCIQC6X6FFfEGkdE8gR9ksXRkHlbutsMFKF/6wm0p6DUaL0wIhALs3owGhe2tmQMk1iYOf6A6uo+5e2TJzVMiryO0bLG5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85343}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b4c16b05cd8ce1ac1d3961346f29c20c/vitest-snapshot-2.2.0-beta.2.tgz", "_integrity": "sha512-fOXZlA1enCyeGvTiV/ZFFDaI9vPadKu3E/Q5LKVcXBqCPHDnRDpa6UNP6N7l74CchXj8y0LaIewkFxsq3FpBSg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "2.2.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.2.0-beta.2_1731939496032_0.3522067679774399", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/snapshot", "version": "2.1.6", "license": "MIT", "_id": "@vitest/snapshot@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "21740449221e37f80c4a8fb3e15f100f30e7934d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.6.tgz", "fileCount": 13, "integrity": "sha512-5JTWHw8iS9l3v4/VSuthCndw1lN/hpPB+mlgn1BUhFbobeIUj1J1V/Bj2t2ovGEmkXLTckFjQddsxS5T6LuVWw==", "signatures": [{"sig": "MEYCIQCncb74Z2PMV1azYs9OXeEsyll/N7kN+H8/Y6Hi8BSZYAIhAIHUA9+RcPKWseK/MYKHW2IoNUXh+zh4vTZKuz6K78IG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c4bcf422ee1122aed09aa32d467dbc08/vitest-snapshot-2.1.6.tgz", "_integrity": "sha512-5JTWHw8iS9l3v4/VSuthCndw1lN/hpPB+mlgn1BUhFbobeIUj1J1V/Bj2t2ovGEmkXLTckFjQddsxS5T6LuVWw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.6_1732623844210_0.10124180473468836", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/snapshot", "version": "2.1.7", "license": "MIT", "_id": "@vitest/snapshot@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5f7afa9e5fd5c8444e376ccece31def1cbc8ec0c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.7.tgz", "fileCount": 13, "integrity": "sha512-OioIxV/xS393DKdlkRNhmtY0K37qVdCv8w1M2SlLTBSX+fNK6zgcd01VlT1nXdbKVDaB8Zb6BOfQYYoGeGTEGg==", "signatures": [{"sig": "MEYCIQC1rGER9AP2AYqBi58689GKnaYeo2M3Uqn//SZ8IlNWyAIhAP1wa5Q53XGpXpMXzWUkPfso9+t84fl1pYSnd61NC+Cs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6dd100fe6b0968a14d3fc72471490373/vitest-snapshot-2.1.7.tgz", "_integrity": "sha512-OioIxV/xS393DKdlkRNhmtY0K37qVdCv8w1M2SlLTBSX+fNK6zgcd01VlT1nXdbKVDaB8Zb6BOfQYYoGeGTEGg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.7_1733132951108_0.7325688361689184", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/snapshot", "version": "2.1.8", "license": "MIT", "_id": "@vitest/snapshot@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d5dc204f4b95dc8b5e468b455dfc99000047d2de", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.8.tgz", "fileCount": 13, "integrity": "sha512-20T7xRFbmnkfcmgVEz+z3AU/3b0cEzZOt/zmnvZEctg64/QZbSDJEVm9fLnnlSi74KibmRsO9/Qabi+t0vCRPg==", "signatures": [{"sig": "MEUCIGa49qVKINSSX/fRRnjIuRsjeO2LAEX5B2X4UJTSMtNJAiEA0DMbJ7pWEN0J884c8EI3BONmlIiXpKd6XzqhXRrfsHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e97dd83cdfa5d0c29a89083c49d058ec/vitest-snapshot-2.1.8.tgz", "_integrity": "sha512-20T7xRFbmnkfcmgVEz+z3AU/3b0cEzZOt/zmnvZEctg64/QZbSDJEVm9fLnnlSi74KibmRsO9/Qabi+t0vCRPg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.8"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.8_1733150787068_0.41619794590363", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/snapshot", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b4c45a5005a1bde0893e65cb9b218def4b5bba1d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-ejTp3wZrEdj1kBGfrrY3stJNy25b4+qJvIkVKfzo7xgorsC+qI3r36RmqMQoAxUKwniNN6eDHMG01txUZsP71Q==", "signatures": [{"sig": "MEUCIQCHm15PdEKx824UGK1iOsQt5NKKpQSSwDYw7tsoBr5yPwIgOvkfiF2tYadU5O/AKWqfEfbIk1Sqp4QtEJupKYyaK7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86592}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/be5dd6511ee9649c2736641382485df6/vitest-snapshot-3.0.0-beta.1.tgz", "_integrity": "sha512-ejTp3wZrEdj1kBGfrrY3stJNy25b4+qJvIkVKfzo7xgorsC+qI3r36RmqMQoAxUKwniNN6eDHMG01txUZsP71Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.14", "@vitest/pretty-format": "3.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.0-beta.1_1733420028184_0.654626276055845", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/snapshot", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a77471e969e9cec2ab43535b0c7bf8bdf100c7a6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-6INaNxXyYBmFGHhjmSyoz+/P3F+e6sHZPXLYt2OAa6Zt1v1O91FoGUTwdNHj2ASxMQeVpK/7snxNaeyr2INVOg==", "signatures": [{"sig": "MEUCIGOp0M+lb5dAmuOi/6BGh3k0bOwIyWEQKcm52IQQj2pGAiEAmGQsCnbfLVRCCWziCsjAz+pswAKj2MxG9O1hh7Xx4H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86583}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b30c297f3f56f7e52dd3379a639789fd/vitest-snapshot-3.0.0-beta.2.tgz", "_integrity": "sha512-6INaNxXyYBmFGHhjmSyoz+/P3F+e6sHZPXLYt2OAa6Zt1v1O91FoGUTwdNHj2ASxMQeVpK/7snxNaeyr2INVOg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.14", "@vitest/pretty-format": "3.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.0-beta.2_1733826109327_0.7271783009823756", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/snapshot", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/snapshot@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b508ef51fa2e51fd4cdcc4876b67e00ac8067081", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-jsWdfQWRcbI1WIpxi2X6jUAAjJY898iK4P/ZzxgkBPFrPK894HXkCm3xcF/sZHF9nHNa61ZIgfgddnH0blkiFQ==", "signatures": [{"sig": "MEYCIQCItaR1bYxpjkT5WaffKPZD+WMeuzEs3R4C2ntGXU9T1gIhALnHcVhYCbUO9OxAwMUS4bkJAq+/MGXcL+CRLoA8AmQg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86583}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/07fe36325ff04971bcf3c0e04f015fe5/vitest-snapshot-3.0.0-beta.3.tgz", "_integrity": "sha512-jsWdfQWRcbI1WIpxi2X6jUAAjJY898iK4P/ZzxgkBPFrPK894HXkCm3xcF/sZHF9nHNa61ZIgfgddnH0blkiFQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.0-beta.3_1734712374294_0.5267731313013795", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/snapshot", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/snapshot@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "06456397159df3328f20760cae87b4431d2a48db", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-z8WLahOEDpRkPf6OvOYhjK6Mhl3Z4U4m536kAxUeFD5If0o1e2rdvSzD6oNPfs25rKs+UT7dla+4MSFU1JVjPA==", "signatures": [{"sig": "MEQCIEi0JYIw2HWe/AtO9YJ2T6C3KcxMGl+Mh6G7y0DjBFa7AiAlYgxNryK901H+VSekTAVdocAuncAulH/Iyac2LHTatQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86773}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e26067ec4702ea91c5eb024e5bfa327a/vitest-snapshot-3.0.0-beta.4.tgz", "_integrity": "sha512-z8WLahOEDpRkPf6OvOYhjK6Mhl3Z4U4m536kAxUeFD5If0o1e2rdvSzD6oNPfs25rKs+UT7dla+4MSFU1JVjPA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.0", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.0-beta.4_1736346250754_0.5413616280852733", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/snapshot", "version": "3.0.0", "license": "MIT", "_id": "@vitest/snapshot@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e11797c3bd35a57fccb2a2667fe36ff253e620cf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0.tgz", "fileCount": 13, "integrity": "sha512-W0X6fJFJ3RbSThncSYUNSnXkMJFyXX9sOvxP1HSQRsWCLB1U3JnZc0SrLpLzcyByMUDXHsiXQ+x+xsr/G5fXNw==", "signatures": [{"sig": "MEQCIFNzFOoMO6/vcAM123D2f6l3h3SaUFIbGgwn+OcwaNa3AiBaMYW951DWfO+06hl3RMWy+XsYPZYPO5ZEkcKaGu3Q3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86732}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/094bb6477f347f69948e09eeaecb7266/vitest-snapshot-3.0.0.tgz", "_integrity": "sha512-W0X6fJFJ3RbSThncSYUNSnXkMJFyXX9sOvxP1HSQRsWCLB1U3JnZc0SrLpLzcyByMUDXHsiXQ+x+xsr/G5fXNw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.0", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.0_1737036467697_0.6323535597420213", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/snapshot", "version": "3.0.1", "license": "MIT", "_id": "@vitest/snapshot@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "02493577c708ff9c90b9bd77c4055ca348e8d196", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.1.tgz", "fileCount": 13, "integrity": "sha512-ZYV+iw2lGyc4QY2xt61b7Y3NJhSAO7UWcYWMcV0UnMrkXa8hXtfZES6WAk4g7Jr3p4qJm1P0cgDcOFyY5me+Ug==", "signatures": [{"sig": "MEUCIQDxJDS2S3kIeh+t1pSgKI7DzVvXoBiKoqwArezqvJ0eGwIgJBKyy0yaWKW7ZGi3tEQtEaobKrlAEsXYpK2UBlP9IT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6a7d7ae95eaa585a5abdb7581379dd03/vitest-snapshot-3.0.1.tgz", "_integrity": "sha512-ZYV+iw2lGyc4QY2xt61b7Y3NJhSAO7UWcYWMcV0UnMrkXa8hXtfZES6WAk4g7Jr3p4qJm1P0cgDcOFyY5me+Ug==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.1_1737055975826_0.24386880703542002", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/snapshot", "version": "3.0.2", "license": "MIT", "_id": "@vitest/snapshot@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c3029d298c905f40ec3f238cab1130c87175a47e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.2.tgz", "fileCount": 13, "integrity": "sha512-h9s67yD4+g+JoYG0zPCo/cLTabpDqzqNdzMawmNPzDStTiwxwkyYM1v5lWE8gmGv3SVJ2DcxA2NpQJZJv9ym3g==", "signatures": [{"sig": "MEUCIHMZv0lhBYRcwClTZIw5SFjXl+XpbQ1JnX4qLqgheQ/0AiEA+36mJi2Nb9kPBeLwy5eQFrpQtlYe8PlFaBEHQ7W+lgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cb769a01e84b6cef9e2ae81c12d3b84f/vitest-snapshot-3.0.2.tgz", "_integrity": "sha512-h9s67yD4+g+JoYG0zPCo/cLTabpDqzqNdzMawmNPzDStTiwxwkyYM1v5lWE8gmGv3SVJ2DcxA2NpQJZJv9ym3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.2_1737123985854_0.039368594746991725", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/snapshot", "version": "3.0.3", "license": "MIT", "_id": "@vitest/snapshot@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a20a8cfa0e7434ef94f4dff40d946a57922119de", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-kNRcHlI4txBGztuJfPEJ68VezlPAXLRT1u5UCx219TU3kOG2DplNxhWLwDf2h6emwmTPogzLnGVwP6epDaJN6Q==", "signatures": [{"sig": "MEQCIEcyxNHilSSqEDvUawETpoR7ZqyvFbOPRhGakqJOAKGuAiAmEV3U5Ed2mtY2OtdzXYcS3qftyW2D1S/s93gjnRdCCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0860d47205cd502042244ad138c95c16/vitest-snapshot-3.0.3.tgz", "_integrity": "sha512-kNRcHlI4txBGztuJfPEJ68VezlPAXLRT1u5UCx219TU3kOG2DplNxhWLwDf2h6emwmTPogzLnGVwP6epDaJN6Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.3_1737467937695_0.03315820300994843", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/snapshot", "version": "3.0.4", "license": "MIT", "_id": "@vitest/snapshot@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7e64c19ca1ab9abb2f01fd246817b5f0404798fd", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.4.tgz", "fileCount": 13, "integrity": "sha512-+p5knMLwIk7lTQkM3NonZ9zBewzVp9EVkVpvNta0/PlFWpiqLaRcF4+33L1it3uRUCh0BGLOaXPPGEjNKfWb4w==", "signatures": [{"sig": "MEYCIQDKyp0P+uauOrFqgqkdafiI3foCHlaNUsinvFJv9gcdKgIhAMkR9DkPhYaPct19X7d9fB2aDhydXM1seGw3WfTInTlt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86894}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ffe146bb94f7b618bd668663d5d6aa23/vitest-snapshot-3.0.4.tgz", "_integrity": "sha512-+p5knMLwIk7lTQkM3NonZ9zBewzVp9EVkVpvNta0/PlFWpiqLaRcF4+33L1it3uRUCh0BGLOaXPPGEjNKfWb4w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"pathe": "^2.0.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.4_1737639714179_0.05272680173396216", "host": "s3://npm-registry-packages-npm-production"}}, "1.6.1": {"name": "@vitest/snapshot", "version": "1.6.1", "license": "MIT", "_id": "@vitest/snapshot@1.6.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "90414451a634bb36cd539ccb29ae0d048a8c0479", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.6.1.tgz", "fileCount": 13, "integrity": "sha512-WvidQuWAzU2p95u8GAKlRMqMyN1yOJkGHnx3M1PL9Raf7AQ1kwLKg04ADlCa3+OXUZE7BceOhVZiuWAbzCKcUQ==", "signatures": [{"sig": "MEQCIHudkzY8XxOEA/McshfGVkP7KiefpbCnwiwdzc0uDb8yAiAhPF5rL6LmRvBfJTdDifho8Vvazw1lX/qaRbv0F/0W+w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-1.6.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7b3ebdf98afd037f401a2f3ffc382ba4/vitest-snapshot-1.6.1.tgz", "_integrity": "sha512-WvidQuWAzU2p95u8GAKlRMqMyN1yOJkGHnx3M1PL9Raf7AQ1kwLKg04ADlCa3+OXUZE7BceOhVZiuWAbzCKcUQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "1.6.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_1.6.1_1738589779432_0.9954340026455999", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/snapshot", "version": "2.1.9", "license": "MIT", "_id": "@vitest/snapshot@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "24260b93f798afb102e2dcbd7e61c6dfa118df91", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.9.tgz", "fileCount": 13, "integrity": "sha512-oBO82rEjsxLNJincVhLhaxxZdEtV0EFHMK5Kmx5sJ6H9L183dHECjiefOAdnqpIgT5eZwT04PoggUnW88vOBNQ==", "signatures": [{"sig": "MEUCIQDM7IAxUR6xYu5491Ga4pTcB89YR12eTAK4yIxx/ds5HQIgQ/bfAk4hOwrEBBZk4ZmBNGYR/74JUWC6WfvelXc1//I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-2.1.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/14c834adca2161047c61ab294a31e500/vitest-snapshot-2.1.9.tgz", "_integrity": "sha512-oBO82rEjsxLNJincVhLhaxxZdEtV0EFHMK5Kmx5sJ6H9L183dHECjiefOAdnqpIgT5eZwT04PoggUnW88vOBNQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.9"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "2.1.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_2.1.9_1738590265194_0.0008379738536583581", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/snapshot", "version": "3.0.5", "license": "MIT", "_id": "@vitest/snapshot@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "afd0ae472dc5893b0bb10e3e673ef649958663f4", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.5.tgz", "fileCount": 13, "integrity": "sha512-GJPZYcd7v8QNUJ7vRvLDmRwl+a1fGg4T/54lZXe+UOGy47F9yUfE18hRCtXL5aHN/AONu29NGzIXSVFh9K0feA==", "signatures": [{"sig": "MEYCIQCGyhgUG0IJGZ8rBj9qM94PdBnINd6C7cgjo7tcYF2XlgIhAKNETI8v7l4FAUgj+ud453+JIC3NMRPC424zpPIjUhVm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86894}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7c44d76383bd80e518489f96462df420/vitest-snapshot-3.0.5.tgz", "_integrity": "sha512-GJPZYcd7v8QNUJ7vRvLDmRwl+a1fGg4T/54lZXe+UOGy47F9yUfE18hRCtXL5aHN/AONu29NGzIXSVFh9K0feA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.5_1738591333628_0.5201351408729382", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/snapshot", "version": "3.0.6", "license": "MIT", "_id": "@vitest/snapshot@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e962319e487b2e8da7ad39322b5e0b39ea639d7a", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.6.tgz", "fileCount": 13, "integrity": "sha512-qKSmxNQwT60kNwwJHMVwavvZsMGXWmngD023OHSgn873pV0lylK7dwBTfYP7e4URy5NiBCHHiQGA9DHkYkqRqg==", "signatures": [{"sig": "MEYCIQD6J17BZDfxjr7FsJJT9sAf3MtMcA5hi/AKNkxhV8NC3gIhANh9cJ20RoW0SetStnocJ1azQU15obaV2P2+NZF3v4XX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88931}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/03a47a17ab1bb81323620c45c9ced742/vitest-snapshot-3.0.6.tgz", "_integrity": "sha512-qKSmxNQwT60kNwwJHMVwavvZsMGXWmngD023OHSgn873pV0lylK7dwBTfYP7e4URy5NiBCHHiQGA9DHkYkqRqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.6_1739885933590_0.7168637437321259", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/snapshot", "version": "3.0.7", "license": "MIT", "_id": "@vitest/snapshot@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "df34e3c5820bdd54bba8919291a182df5c6b8c6f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.7.tgz", "fileCount": 13, "integrity": "sha512-eqTUryJWQN0Rtf5yqCGTQWsCFOQe4eNz5Twsu21xYEcnFJtMU5XvmG0vgebhdLlrHQTSq5p8vWHJIeJQV8ovsA==", "signatures": [{"sig": "MEYCIQDGnYsRjSvYLuGu+p1yEaWC6x/z68NRxhU1e7wqE6MioAIhALr8NX6JQonwKsYqhr8HY/wq6tleF7mWuGhO5he2ek+h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88931}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6501a8ba769bff574376231c2d6df497/vitest-snapshot-3.0.7.tgz", "_integrity": "sha512-eqTUryJWQN0Rtf5yqCGTQWsCFOQe4eNz5Twsu21xYEcnFJtMU5XvmG0vgebhdLlrHQTSq5p8vWHJIeJQV8ovsA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.7_1740419460313_0.3103270693653495", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/snapshot", "version": "3.0.8", "license": "MIT", "_id": "@vitest/snapshot@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b65d738c00ff052a323125ad7dfb001927049c78", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.8.tgz", "fileCount": 13, "integrity": "sha512-x8IlMGSEMugakInj44nUrLSILh/zy1f2/BgH0UeHpNyOocG18M9CWVIFBaXPt8TrqVZWmcPjwfG/ht5tnpba8A==", "signatures": [{"sig": "MEYCIQCqbdfJR7xZCMgUGNa6kKZOPkRq8jh2jacrVwDZHRBE/QIhAJRQjUWHysFzPt/YiXUf+qmLOO0BNX7MU3gYxX78QhXu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90001}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2d66915f3cf30db18692224c1774fcaa/vitest-snapshot-3.0.8.tgz", "_integrity": "sha512-x8IlMGSEMugakInj44nUrLSILh/zy1f2/BgH0UeHpNyOocG18M9CWVIFBaXPt8TrqVZWmcPjwfG/ht5tnpba8A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.8_1741274187800_0.04071135237803869", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/snapshot", "version": "3.0.9", "license": "MIT", "_id": "@vitest/snapshot@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2ab878b3590b2daef1798b645a9d9e72a0eb258d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.9.tgz", "fileCount": 13, "integrity": "sha512-AiLUiuZ0FuA+/8i19mTYd+re5jqjEc2jZbgJ2up0VY0Ddyyxg/uUtBDpIFAy4uzKaQxOW8gMgBdAJJ2ydhu39A==", "signatures": [{"sig": "MEUCIQCNS6AaLX1Gnp6/RD13emQQG0lgVKd9adiUOF7w4jD/kAIgUW/FYhc73I2UjHsVPqQrelBKc78cQFIjSBIgiNjuqNw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89265}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.0.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4cb9dd21ee033e991d60529dd20f1459/vitest-snapshot-3.0.9.tgz", "_integrity": "sha512-AiLUiuZ0FuA+/8i19mTYd+re5jqjEc2jZbgJ2up0VY0Ddyyxg/uUtBDpIFAy4uzKaQxOW8gMgBdAJJ2ydhu39A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.0.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.0.9_1742212768421_0.9362827967918286", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/snapshot", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f050b18c76b224fb27dcdc7da033dd65c0daba7f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-9sGHyh+G4kNd5FAyLYKh/klIfq4huIwDDFUvYJty4scS60sAz2vBTUmlfQlSQQq1HyiktW1OPCO973fMOaKlqA==", "signatures": [{"sig": "MEUCIDC9futPQYbh2yAA29L7B14GTg2JnWdmfEjKdQqg2C7WAiEAjD4xze6dEp9Rew8Emr3R7O0BGvnq+IyQzdthbrIZ4N4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89286}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2effb1c3149db0c88e3765e7cb79f0ae/vitest-snapshot-3.1.0-beta.1.tgz", "_integrity": "sha512-9sGHyh+G4kNd5FAyLYKh/klIfq4huIwDDFUvYJty4scS60sAz2vBTUmlfQlSQQq1HyiktW1OPCO973fMOaKlqA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.1.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.0-beta.1_1742213862374_0.11190522476886278", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/snapshot", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b4459119f5e6d9a0378ed9118c4798bb6b3b2247", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-HXK4uKd8j5ldZ0rXfrAOpnIwHiy060znuDmpp+sMde4OoQqhpYhUHZUmqbDnF2hQes+M9TwBh7OsH+k0Z9RVYw==", "signatures": [{"sig": "MEUCIQCBusYWp2ed8WFisCjygT41iGs3O3o3ao2tff3n1DdFpQIgE9BSn7O0ATjl9pH+XQkqKA+X7tNgl6372n7F1Q5u2Bs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87624}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a3624a97f1deb8c67ed10afa09e1048f/vitest-snapshot-3.1.0-beta.2.tgz", "_integrity": "sha512-HXK4uKd8j5ldZ0rXfrAOpnIwHiy060znuDmpp+sMde4OoQqhpYhUHZUmqbDnF2hQes+M9TwBh7OsH+k0Z9RVYw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.1.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.0-beta.2_1742545707425_0.8418594448160241", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/snapshot", "version": "3.1.0", "license": "MIT", "_id": "@vitest/snapshot@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "65c8c879dc6307af2bbda5fb821bbe9e9765166c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0.tgz", "fileCount": 13, "integrity": "sha512-H5xN6SMxLkNVvYvPaeN/FN4Y0e6ZgReaTVvHd1a7IKAUyKjF+Ttbb118PXxCXYTLQY45LKcMVNioG2TDL0votA==", "signatures": [{"sig": "MEQCIDihjfslbPFvV/eBitZTi/BnPpZdhoKtNioCLFnYfK8cAiAp5Ki86A9ZEXXvpi+bbke0P7qnVvTcvmTcYrk6FuOu8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87058}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3n/td5k6fm13gd722lls3bsyvlm0000gn/T/a52c0f12f591c268c39cc23d37b66310/vitest-snapshot-3.1.0.tgz", "_integrity": "sha512-H5xN6SMxLkNVvYvPaeN/FN4Y0e6ZgReaTVvHd1a7IKAUyKjF+Ttbb118PXxCXYTLQY45LKcMVNioG2TDL0votA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.9.0", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "22.12.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.0_1743416059702_0.24225875115067375", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/snapshot", "version": "3.1.1", "license": "MIT", "_id": "@vitest/snapshot@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "42b6aa0d0e2b3b48b95a5c76efdcc66a44cb11f3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.1.tgz", "fileCount": 13, "integrity": "sha512-bByMwaVWe/+1WDf9exFxWWgAixelSdiwo2p33tpqIlM14vW7PRV5ppayVXtfycqze4Qhtwag5sVhX400MLBOOw==", "signatures": [{"sig": "MEYCIQDYJLRmGaViGv/edWog8Hr2vD/D8zSQyCp1GUpUnrtt6gIhAO0ZOaLwY3gq32YD7gxb+n0YFK1RBoM1Ior8xZ37pCQb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87058}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/16db479ba39053c8006e419756153d3f/vitest-snapshot-3.1.1.tgz", "_integrity": "sha512-bByMwaVWe/+1WDf9exFxWWgAixelSdiwo2p33tpqIlM14vW7PRV5ppayVXtfycqze4Qhtwag5sVhX400MLBOOw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.1_1743416350167_0.4410828465382741", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/snapshot", "version": "3.1.2", "license": "MIT", "_id": "@vitest/snapshot@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "46c52a417afbf1fe94fba0a5735cbedf9cfc60f6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.2.tgz", "fileCount": 13, "integrity": "sha512-Q1qkpazSF/p4ApZg1vfZSQ5Yw6OCQxVMVrLjslbLFA1hMDrT2uxtqMaw8Tc/jy5DLka1sNs1Y7rBcftMiaSH/Q==", "signatures": [{"sig": "MEQCICkMryP/97KOI8nKPxh4xPaNlfQN2rDmASioJaXRSS7MAiB7ZLtdyJhAT42RIJXjqTTtgAgkWSNJe3PJLk+HL9CXqA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fad2b9d83ef71f51d3f321183202ab0b/vitest-snapshot-3.1.2.tgz", "_integrity": "sha512-Q1qkpazSF/p4ApZg1vfZSQ5Yw6OCQxVMVrLjslbLFA1hMDrT2uxtqMaw8Tc/jy5DLka1sNs1Y7rBcftMiaSH/Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.2_1745225924293_0.4845583630346961", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/snapshot", "version": "3.1.3", "license": "MIT", "_id": "@vitest/snapshot@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "39a8f9f8c6ba732ffde59adeacf0a549bef11e76", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.3.tgz", "fileCount": 13, "integrity": "sha512-XVa5OPNTYUsyqG9skuUkFzAeFnEzDp8hQu7kZ0N25B1+6KjGm4hWLtURyBbsIAOekfWQ7Wuz/N/XXzgYO3deWQ==", "signatures": [{"sig": "MEUCIQDe5gvhBnA5eK0NIdMd2poOz76A1MZ47ctvIwdqTb8HaQIgZPi5yysp2tRB3qz474e9XvJV5muqbl3VhKM+MWEidsg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/33f277ab1103b2439fdddf804e3be272/vitest-snapshot-3.1.3.tgz", "_integrity": "sha512-XVa5OPNTYUsyqG9skuUkFzAeFnEzDp8hQu7kZ0N25B1+6KjGm4hWLtURyBbsIAOekfWQ7Wuz/N/XXzgYO3deWQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.3_1746452714945_0.26257676465450186", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/snapshot", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "186b3de4f35850bf881045a0d010d3845659038b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-VY9ngpOSnpOc4QGePhjHOGW+bqLxw6Fsi6Hzlbrq4ApufoBQtQXVQ8WrTcnPgLHS+pK2zVM5rPbs5sHu55uMbA==", "signatures": [{"sig": "MEUCIQDGs1zuk2Q7S3RDgKWYuB5faeCUTxenQ16T1QV9p7UwhQIgJAbd+mcaZvep0IixdqtwxHqI8BmG4JYwQe5JJGnoPL0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87077}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b9b243f8438a2c6219f10c6a6bc6d544/vitest-snapshot-3.2.0-beta.1.tgz", "_integrity": "sha512-VY9ngpOSnpOc4QGePhjHOGW+bqLxw6Fsi6Hzlbrq4ApufoBQtQXVQ8WrTcnPgLHS+pK2zVM5rPbs5sHu55uMbA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.2.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.0-beta.1_1746463887578_0.6833501101214079", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/snapshot", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/snapshot@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "90e6d33bb0e835c528d4b0556a7c2745193188e7", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-UT//cec0kpLqzm2D0BhnI4FjMryHIlmewpX8fH7bQrmcSmco8/H7NwSxOI1rfjWeerLoIpUKlKbcEEvW0GqK2w==", "signatures": [{"sig": "MEYCIQCWew3ogcQkNF8G1eJXCNoyn3D0hEVsushqMWzcgdgvmwIhAJX0jvfslh9KrWyYKmk29hrHvi193eSjJEy6yBvSbAwI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87077}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0151484fc0c0b6e25c007bf6f8ef81cc/vitest-snapshot-3.2.0-beta.2.tgz", "_integrity": "sha512-UT//cec0kpLqzm2D0BhnI4FjMryHIlmewpX8fH7bQrmcSmco8/H7NwSxOI1rfjWeerLoIpUKlKbcEEvW0GqK2w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.2.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.0-beta.2_1747658334759_0.5341127048177183", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/snapshot", "version": "3.1.4", "license": "MIT", "_id": "@vitest/snapshot@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7897d4960a3cf617fb0f17e182cc15c7e3e4ed3f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.4.tgz", "fileCount": 13, "integrity": "sha512-JPHf68DvuO7vilmvwdPr9TS0SuuIzHvxeaCkxYcCD4jTk67XwL45ZhEHFKIuCm8CYstgI6LZ4XbwD6ANrwMpFg==", "signatures": [{"sig": "MEYCIQC5Oi400j16+wBQAR5orU8o43BDieF58do2yRM0S7xFFQIhAJZEt45U7ZB058SjfE5uFA9cV1IzZ6aqVlAtWfdSXhHm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/195b811c6c9584c96fbbee2bbf17b830/vitest-snapshot-3.1.4.tgz", "_integrity": "sha512-JPHf68DvuO7vilmvwdPr9TS0SuuIzHvxeaCkxYcCD4jTk67XwL45ZhEHFKIuCm8CYstgI6LZ4XbwD6ANrwMpFg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.1.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.1.4_1747671845373_0.9906940029556595", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/snapshot", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/snapshot@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7163837a2f3862ddcb807e360fac6caab07d680c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-bLGgmpl9knKv1EJde5THe4eGqmZpb+Vv97Kq0t3lP8uYN1viID4lVsg1swSXtIAd0F1/anx4Uiu3Bp71hmFlow==", "signatures": [{"sig": "MEUCIG4jv25118wAcRAFO+2ibB4TyZOqaXlyTfhh6WmTGUBRAiEAl/L8UTW5OmgBYHghrp0ZLMDmnfmJiVv9IAVHXGfzr1c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92655}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/65733ac6909b3ca5da5fdbd3b025eab7/vitest-snapshot-3.2.0-beta.3.tgz", "_integrity": "sha512-bLGgmpl9knKv1EJde5THe4eGqmZpb+Vv97Kq0t3lP8uYN1viID4lVsg1swSXtIAd0F1/anx4Uiu3Bp71hmFlow==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "3.2.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.0-beta.3_1748442526072_0.14671114984781086", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/snapshot", "version": "3.2.0", "license": "MIT", "_id": "@vitest/snapshot@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "98ba12e3eb0ad39beffb9c44747b7c4d42f9e25e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0.tgz", "fileCount": 13, "integrity": "sha512-z7P/EneBRMe7hdvWhcHoXjhA6at0Q4ipcoZo6SqgxLyQQ8KSMMCmvw1cSt7FHib3ozt0wnRHc37ivuUMbxzG/A==", "signatures": [{"sig": "MEQCIGvz5qUIcNpvTvH/E7HU8o8fwydg+1nRlYuitaF0N+zwAiBfVUabwDmCzp1x2HmY/jxSHlbnJAIhSMH0F+JmbPlS/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3161b40e6e4dff1824c7fff76d631e29/vitest-snapshot-3.2.0.tgz", "_integrity": "sha512-z7P/EneBRMe7hdvWhcHoXjhA6at0Q4ipcoZo6SqgxLyQQ8KSMMCmvw1cSt7FHib3ozt0wnRHc37ivuUMbxzG/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.2.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.0_1748862656164_0.669765045193792", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/snapshot", "version": "3.2.1", "license": "MIT", "_id": "@vitest/snapshot@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "40a31ffe0304f93a7b6099194e9130e59b8b4a71", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.1.tgz", "fileCount": 13, "integrity": "sha512-5xko/ZpW2Yc65NVK9Gpfg2y4BFvcF+At7yRT5AHUpTg9JvZ4xZoyuRY4ASlmNcBZjMslV08VRLDrBOmUe2YX3g==", "signatures": [{"sig": "MEQCIEVVkPCsnLcl8LQXxHCRV99VrentAVsJk5oDRUUtWs+AAiAxtj2vqUSdD1u6mrQ6sg0dkbKw/r5QIR46dwGbWIBLrA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/24daae3172ab9699323507170d761de8/vitest-snapshot-3.2.1.tgz", "_integrity": "sha512-5xko/ZpW2Yc65NVK9Gpfg2y4BFvcF+At7yRT5AHUpTg9JvZ4xZoyuRY4ASlmNcBZjMslV08VRLDrBOmUe2YX3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.2.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.1_1748970452664_0.16055353955527907", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/snapshot", "version": "3.2.2", "license": "MIT", "_id": "@vitest/snapshot@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9a97cf61d821ea7d421d14e0134c5469bce9e83c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.2.tgz", "fileCount": 13, "integrity": "sha512-aMEI2XFlR1aNECbBs5C5IZopfi5Lb8QJZGGpzS8ZUHML5La5wCbrbhLOVSME68qwpT05ROEEOAZPRXFpxZV2wA==", "signatures": [{"sig": "MEUCIEKRJrLCYMJHROydPO8QmcGburB/2243WKB9MppZGdZHAiEAiFMb3GlYUhJCwBR82GEmlxWI2MFeurnbAa6KfK8sE0c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/68970707a933d39555e43dbbffdbaceb/vitest-snapshot-3.2.2.tgz", "_integrity": "sha512-aMEI2XFlR1aNECbBs5C5IZopfi5Lb8QJZGGpzS8ZUHML5La5wCbrbhLOVSME68qwpT05ROEEOAZPRXFpxZV2wA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.2.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.2_1749130945306_0.949881791166151", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/snapshot", "version": "3.2.3", "license": "MIT", "_id": "@vitest/snapshot@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "786dc1939174e1ac6b674d6fd3259bd4ea35a804", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.3.tgz", "fileCount": 13, "integrity": "sha512-9gIVWx2+tysDqUmmM1L0hwadyumqssOL1r8KJipwLx5JVYyxvVRfxvMq7DaWbZZsCqZnu/dZedaZQh4iYTtneA==", "signatures": [{"sig": "MEUCIQCU5eO7eWGG23YL01nrtPU1pQR85Fn4Jc84+SkK8+qevAIgaOXCW9mZ1qq6Q2TmL/6JujiGTOcLGSw0wg4IMZ7doJc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2e6a9c05feacaf7233c70fd62b56e208/vitest-snapshot-3.2.3.tgz", "_integrity": "sha512-9gIVWx2+tysDqUmmM1L0hwadyumqssOL1r8KJipwLx5JVYyxvVRfxvMq7DaWbZZsCqZnu/dZedaZQh4iYTtneA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.2.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.3_1749468759510_0.9816145789388646", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/snapshot", "version": "3.2.4", "license": "MIT", "_id": "@vitest/snapshot@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "40a8bc0346ac0aee923c0eefc2dc005d90bc987c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.4.tgz", "fileCount": 13, "integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "signatures": [{"sig": "MEQCICEklsWQpguwR3OHCO6Yb90/XmDsYdOOlt4Qx+GRdvMWAiAQdlYkhcPefOHH1yYfZO2+iEHVIU3frlocYFfpbDwKhg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92318}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/80581e9f74be13b06919a52707e553e1/vitest-snapshot-3.2.4.tgz", "_integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/utils": "3.2.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_3.2.4_1750182851634_0.6075460796164387", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/snapshot", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/snapshot@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2b6efbcbb9e6ef00359a78e2a9021ca14e30d70e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-4.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-ajpvzWEaDU/lmi0tyP3Qa3dtY7+hdJACKBZPH/XNvPppxGbCmFISdhhMe72Zujuj3q/pugJ7yqX1cj0i6zLo1A==", "signatures": [{"sig": "MEYCIQDoHlkYA0EFCgX+rDLtVKyFp4VH3YzY9vSqNjpY6isjUAIhANdOC8bsgbt7wJvginrxjEaQ/LqNQZa6f5b4yyT5QIUl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92377}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-snapshot-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*", "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/d63f43081d637c6a096081da95d2c460/vitest-snapshot-4.0.0-beta.1.tgz", "_integrity": "sha512-ajpvzWEaDU/lmi0tyP3Qa3dtY7+hdJACKBZPH/XNvPppxGbCmFISdhhMe72Zujuj3q/pugJ7yqX1cj0i6zLo1A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "_npmVersion": "10.8.2", "description": "Vitest snapshot manager", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "4.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/utils": "4.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/snapshot_4.0.0-beta.1_1750433326089_0.3175536132459711", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/snapshot", "type": "module", "version": "4.0.0-beta.2", "description": "Vitest snapshot manager", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/snapshot"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./environment": {"types": "./dist/environment.d.ts", "default": "./dist/environment.js"}, "./manager": {"types": "./dist/manager.d.ts", "default": "./dist/manager.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"magic-string": "^0.30.17", "pathe": "^2.0.3", "@vitest/pretty-format": "4.0.0-beta.2"}, "devDependencies": {"@types/natural-compare": "^1.4.3", "natural-compare": "^1.4.0", "@vitest/utils": "4.0.0-beta.2"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/snapshot@4.0.0-beta.2", "readmeFilename": "README.md", "_integrity": "sha512-gFGfWU62QCkIqMIqWCrVO9KnTSLEJN/vgBgk/K9dDyVdyNwYtyDSYffj9QuxQrgq/hkeHKGmK9WDfywgZ7O+Dg==", "_resolved": "/tmp/48f819d0ae1d2367aeecd8ae8d3e343b/vitest-snapshot-4.0.0-beta.2.tgz", "_from": "file:vitest-snapshot-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-gFGfWU62QCkIqMIqWCrVO9KnTSLEJN/vgBgk/K9dDyVdyNwYtyDSYffj9QuxQrgq/hkeHKGmK9WDfywgZ7O+Dg==", "shasum": "aa783b4a6165e2d6888f5d38bcc4dc0ea8ccdf5a", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-4.0.0-beta.2.tgz", "fileCount": 13, "unpackedSize": 92377, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIA8KYbTzgW14nDPC2A+YWancuLIAo8qr0nFwpZG26GAwAiB1Ei6L8/RiTMZzkzNTARtMPBofLQX7tg60b6oflLFIgg=="}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/snapshot_4.0.0-beta.2_1750775105121_0.2526106206264924"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-04-09T13:39:24.245Z", "modified": "2025-06-24T14:25:05.771Z", "0.30.0": "2023-04-09T13:39:24.486Z", "0.30.1": "2023-04-11T11:27:00.331Z", "0.31.0": "2023-05-03T18:08:50.116Z", "0.31.1": "2023-05-17T14:23:49.248Z", "0.31.2": "2023-05-30T13:05:34.404Z", "0.31.3": "2023-05-31T14:49:46.316Z", "0.31.4": "2023-06-01T09:55:36.103Z", "0.32.0": "2023-06-06T17:04:36.589Z", "0.32.1": "2023-06-16T12:23:13.462Z", "0.32.2": "2023-06-16T16:06:04.479Z", "0.32.3": "2023-07-03T08:35:41.792Z", "0.32.4": "2023-07-03T11:06:03.146Z", "0.33.0": "2023-07-06T14:11:27.868Z", "0.34.0": "2023-08-01T15:41:54.208Z", "0.34.1": "2023-08-01T16:53:49.447Z", "0.34.2": "2023-08-17T10:09:56.614Z", "0.34.3": "2023-08-25T07:30:14.497Z", "0.34.4": "2023-09-08T10:34:25.796Z", "0.34.5": "2023-09-21T13:50:34.034Z", "0.34.6": "2023-09-29T07:33:42.935Z", "1.0.0-beta.0": "2023-10-02T16:40:23.878Z", "0.34.7": "2023-10-02T17:03:36.663Z", "1.0.0-beta.1": "2023-10-03T11:31:30.184Z", "1.0.0-beta.2": "2023-10-13T07:34:23.226Z", "1.0.0-beta.3": "2023-10-27T12:45:46.114Z", "1.0.0-beta.4": "2023-11-09T10:13:41.028Z", "1.0.0-beta.5": "2023-11-18T09:44:35.943Z", "1.0.0-beta.6": "2023-11-28T17:27:19.947Z", "1.0.0": "2023-12-04T15:46:28.357Z", "1.0.1": "2023-12-04T18:04:54.192Z", "1.0.2": "2023-12-07T10:13:05.521Z", "1.0.3": "2023-12-09T13:05:52.093Z", "1.0.4": "2023-12-09T19:05:23.703Z", "1.1.0": "2023-12-19T14:06:35.564Z", "1.1.1": "2023-12-31T13:38:03.543Z", "1.1.2": "2024-01-04T16:58:47.615Z", "1.1.3": "2024-01-05T08:21:37.870Z", "1.2.0": "2024-01-12T16:07:26.762Z", "1.2.1": "2024-01-17T16:24:07.378Z", "1.2.2": "2024-01-26T16:25:56.535Z", "1.3.0": "2024-02-16T17:29:11.599Z", "1.3.1": "2024-02-20T13:48:34.437Z", "1.4.0": "2024-03-15T10:30:56.120Z", "1.5.0": "2024-04-11T17:48:06.735Z", "1.5.1": "2024-04-24T11:22:32.327Z", "1.5.2": "2024-04-25T09:12:09.717Z", "1.5.3": "2024-04-30T08:40:33.842Z", "1.6.0": "2024-05-03T15:22:24.056Z", "2.0.0-beta.1": "2024-05-09T14:32:34.976Z", "2.0.0-beta.2": "2024-05-09T15:31:32.403Z", "2.0.0-beta.3": "2024-05-14T18:44:49.137Z", "2.0.0-beta.4": "2024-06-02T12:16:03.096Z", "2.0.0-beta.5": "2024-06-02T12:27:55.916Z", "2.0.0-beta.6": "2024-06-02T19:17:33.021Z", "2.0.0-beta.7": "2024-06-03T11:35:56.330Z", "2.0.0-beta.8": "2024-06-04T12:39:32.868Z", "2.0.0-beta.9": "2024-06-05T08:01:09.791Z", "2.0.0-beta.10": "2024-06-12T12:11:44.532Z", "2.0.0-beta.11": "2024-06-19T20:14:08.078Z", "2.0.0-beta.12": "2024-06-25T20:16:24.759Z", "2.0.0-beta.13": "2024-07-04T14:03:50.188Z", "2.0.0": "2024-07-08T11:39:30.708Z", "2.0.1": "2024-07-08T15:33:07.760Z", "2.0.2": "2024-07-10T15:46:40.729Z", "2.0.3": "2024-07-15T10:03:37.747Z", "2.0.4": "2024-07-22T09:13:31.962Z", "2.0.5": "2024-07-31T10:40:04.881Z", "2.1.0-beta.1": "2024-08-07T06:21:31.553Z", "2.1.0-beta.2": "2024-08-07T07:56:55.961Z", "2.1.0-beta.3": "2024-08-07T08:17:08.257Z", "2.1.0-beta.4": "2024-08-07T11:42:57.565Z", "2.1.0-beta.5": "2024-08-12T11:35:24.934Z", "2.1.0-beta.6": "2024-08-20T13:18:38.192Z", "2.1.0-beta.7": "2024-09-09T15:13:24.349Z", "2.1.0": "2024-09-12T14:03:31.198Z", "2.1.1": "2024-09-13T15:32:44.892Z", "2.1.2": "2024-10-02T16:20:12.989Z", "2.1.3": "2024-10-14T11:05:37.556Z", "2.1.4": "2024-10-28T12:27:26.075Z", "2.1.5": "2024-11-13T15:24:14.007Z", "2.2.0-beta.1": "2024-11-13T17:17:25.593Z", "2.2.0-beta.2": "2024-11-18T14:18:16.208Z", "2.1.6": "2024-11-26T12:24:04.386Z", "2.1.7": "2024-12-02T09:49:11.291Z", "2.1.8": "2024-12-02T14:46:27.266Z", "3.0.0-beta.1": "2024-12-05T17:33:48.396Z", "3.0.0-beta.2": "2024-12-10T10:21:49.553Z", "3.0.0-beta.3": "2024-12-20T16:32:54.527Z", "3.0.0-beta.4": "2025-01-08T14:24:11.155Z", "3.0.0": "2025-01-16T14:07:47.927Z", "3.0.1": "2025-01-16T19:32:56.025Z", "3.0.2": "2025-01-17T14:26:26.066Z", "3.0.3": "2025-01-21T13:58:57.905Z", "3.0.4": "2025-01-23T13:41:54.358Z", "1.6.1": "2025-02-03T13:36:19.609Z", "2.1.9": "2025-02-03T13:44:25.401Z", "3.0.5": "2025-02-03T14:02:13.807Z", "3.0.6": "2025-02-18T13:38:53.825Z", "3.0.7": "2025-02-24T17:51:00.500Z", "3.0.8": "2025-03-06T15:16:27.956Z", "3.0.9": "2025-03-17T11:59:28.737Z", "3.1.0-beta.1": "2025-03-17T12:17:42.568Z", "3.1.0-beta.2": "2025-03-21T08:28:27.697Z", "3.1.0": "2025-03-31T10:14:19.909Z", "3.1.1": "2025-03-31T10:19:10.361Z", "3.1.2": "2025-04-21T08:58:44.472Z", "3.1.3": "2025-05-05T13:45:15.143Z", "3.2.0-beta.1": "2025-05-05T16:51:27.775Z", "3.2.0-beta.2": "2025-05-19T12:38:54.966Z", "3.1.4": "2025-05-19T16:24:05.578Z", "3.2.0-beta.3": "2025-05-28T14:28:46.269Z", "3.2.0": "2025-06-02T11:10:56.484Z", "3.2.1": "2025-06-03T17:07:32.856Z", "3.2.2": "2025-06-05T13:42:25.496Z", "3.2.3": "2025-06-09T11:32:39.694Z", "3.2.4": "2025-06-17T17:54:11.820Z", "4.0.0-beta.1": "2025-06-20T15:28:46.265Z", "4.0.0-beta.2": "2025-06-24T14:25:05.336Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/snapshot#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/snapshot"}, "description": "Vitest snapshot manager", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}