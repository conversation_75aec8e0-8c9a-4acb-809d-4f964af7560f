{"_id": "@jest/source-map", "_rev": "79-d6dfe3b1e8728a6a6e149910d70e31a6", "name": "@jest/source-map", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.1"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/source-map", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/source-map@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2716fe722545fe4ab41aab692f8e65f931e6c1c0", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-24.2.0-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-QVmBZ6fOze/l8TnIssFl3+IvduHOYjWneVGluHwcuHL/Oz1uLBWpHkjLe4mB4OTkZ94qedw0s4gTXlYAR8QwLA==", "signatures": [{"sig": "MEUCIARF9H76FCtssd9Z9kCQqLx9g33A/7DrIeEfvesR0yYrAiEA82d3Egw3osD0mLPNx4ud54HKPo+h35g2RzBKYD230kE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfou+CRA9TVsSAnZWagAA1r4P/i7BMSj0KKNNto2kLACc\nOoz0AUagfprbavDv0GXVbs7UcOBBoY09bPqbMA1yx2VLaJwOjF1B56n/NOsz\nijnT4hgU7XOst1LbrrUOi+oo0iaasLVqRS6oPXS+QxhzLa2SljhFXIk5K1bJ\nDNkQdzJsnh7UXk0KTYQb6rAGwqRIe3NanOSKPC7GEKySkbocdO0i7dhj5EQL\n17CMnRHHmk8v+Qrn3pZbnhe+R1lmmCNVHDBXtkSUJZBSgx4sSHD14ciB2e7D\n1SbZjRjRm7hdLF58Jng1b1aT8PHW6lCl1A9exkaetJ2AbaQ+aDIL+SSmPn51\nn10aRmidxzDbPbfCQxsRJATReMKbn51SijR3tUK06YVdhmVrMzmh/8/Eebz+\n/VaOFQP934JwbaXDWRG8+HzT5+XARjWnAKjY5PX7fuVr3/FF0BhmbTf2lFxO\neAvA+15MyvzQF2JCY25mVGHUrSxdO0qCZ53zeytWLN1cKbKPaDoliPm1G5J6\nr76+0wxrja3clHWaHMVwCZwqeYrWjOjIQrRvWYpzdXa7mC8xZoGXshgBBcAi\n+oUcJmtuivC+I32zg64W6GrMr/3/jO4Gc3ydkSPa4ziE7zo+Ti3tbecoxJLJ\nqNFQDc9pAKp7ufNRsDLV6q9DG/vZ+uF8nz7sZBc9ofhIiNOXIn933TPkpM8+\ncG8X\r\n=FjiW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.1.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_24.2.0-alpha.0_1551797181902_0.03935818670870139", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/source-map", "version": "24.3.0", "license": "MIT", "_id": "@jest/source-map@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "563be3aa4d224caf65ff77edc95cd1ca4da67f28", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-24.3.0.tgz", "fileCount": 12, "integrity": "sha512-zALZt1t2ou8le/crCeeiRYzvdnTzaIlpOWaet45lNSqNJUnXbppUUFR4ZUAlzgDmKee4Q5P/tKXypI1RiHwgag==", "signatures": [{"sig": "MEUCIQDX6b5Dgt+y8gW5J5v1z8q507Tl3u1itKnuowKmr8lU7QIgF/PwLF8N53y8uqx1BmEvIyNsNzwZ5CIhPNa4w4etM0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAA+4oP/0F8H5WHmeoS0Tfho3q0\nVuCOQPxDIz59hsnWij7Sy0ODpijrnzQ1p93GeeycYsbA4FZcSp5K+3Na07Zb\nGNFihCYaBAuWtzb70rDK+YjUTXUmanc0uAoUY3NDH/Kb6Cz0gGVzz8adOI71\nt4t19O/yY48nUggdQYHx4g98XBVLRhl6WlK8unM+NZk3JsOx7ziDT+KnPJmR\nZUySlQh80++1cewnjmEBDsnBR7Q+CBYzAUwoxmosaqU3//YzHvo3TcSSW17s\np8ybNLyk88E222XGM5wsnxhCK9e0SmRSRgKx1a9pIY2K7wHAlPeJQDn6hRz5\nXA0YRQUKgJUPeXfjGfMoRHn4rw1/YW8/6CuAv83Zk7KPEfE9vYEaZ4Vhvlwx\ny7ASpVztqCMCAIUXCnyPBMZFXKeYquA+9MySJSj4mqvoUHY0fKo7CboUwN1D\nvnLRA8d9PmX43Kg7i2OxYw2Jq+0H4TxSuL96w+uBWNQe0Hv9cjSSSu8lGBIP\nACe35yn7grig2uJxOxkL1GXu4xUAVsrb4p2WIB6HYUevoUyvRHZ4aKhI/SFH\nhiM+L/QSCvKfLzKNM6cIZqP5MfLA1g2spKxFNfirPwQiBCmYwt4oLNPAAjqY\nQ1H/WnKTTSW6wdpeyXi4nr9GeZab/qzORRs6UmAeahlE3wAzAA+WBciN2UKb\nqnx0\r\n=1COO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.1.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_24.3.0_1551963558552_0.9381989381336389", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/source-map", "version": "24.9.0", "license": "MIT", "_id": "@jest/source-map@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0e263a94430be4b41da683ccc1e6bffe2a191714", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-24.9.0.tgz", "fileCount": 11, "integrity": "sha512-/Xw7xGlsZb4MJzNDgB7PW5crou5JqWiBQaz6xyPd3ArOg2nfn/PunV8+olXbbEZzNl591o5rWKE9BRDaFAuIBg==", "signatures": [{"sig": "MEYCIQCmIaK8qe5nt4Gn9Kp1xi1CISoK5faj1v659kxYEhd+PgIhAKvs9In+oW/ftMuhY7/W+dCPKGItzIXa3viwoGnjMcGx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAyZ0P/RbwI1s6/w0dU9hjRanf\nDGWksO0eKYxPH43SWa8SaVE/G+mh7MRAMYvvtrPuIMOY2/i0PJOl2ObrsPDW\nMg9w0O2HJ2nspjUrmN5koPeVo+L8BUVLamn1Tb/o/yiB5wFBnqoiwglY6Vsu\nOL/q9JMZks6FyWEBrJHKfAifzOL1BjMK60UX8xI1BFQ1KZk9fe+l9aOY/LPZ\np9JtqnUw5ViSmRH2tNcr2kDyCIZ6Hx7aS36I8U5UpJhEfBet12W8DPUFnZfD\nuHGxQOSfu8scQHvCXIWKptOv/q3r1wGqzpmHavhz5n63XGorc6QwsMevkYSk\nqmZe1z55kDA5jSNqOUW4qRLH/kxbbP0OdYLCJiMc2L97mRKQc2sl2Llk7J2i\nQn0aorcGoUJHpMAqFgk0u0EkXg9S2WZ1L/cOJZQKxMo6nSI6SteREjY+L1wH\n3HdqihuCAwIlKtEt4Puf8FJk53AeUuAsBT7a0sewwBrLr6/e/AD3SgIExxh0\nAyo+GcIpO0a0JlsmUlfdNmNOAWppeeBjDbHpGUL/GqqVd4adPgqqqQy7kds5\nebpMr1Tn5m4X1GJl6ZiToOztOr43b5UMhQg6xv+0I9YyJy5eqINg2HhapdkQ\n6ERYI+eL+42wyL5I3ZM2KCepQ6aFhmh1ryRoIjNVc45Ph0rRI7c6keWm5hqS\nEKo7\r\n=in15\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.1.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_24.9.0_1565934946397_0.5373345027405911", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/source-map", "version": "25.0.0", "license": "MIT", "_id": "@jest/source-map@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a094c89cf5fb21e06e18d12f3203e8de294188e", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.0.0.tgz", "fileCount": 11, "integrity": "sha512-L9BMRO692tDYYt0VmrDKDD0AKOIugTOT2qAAzYiA0qXzqqg0a573+HqvP9CXg1l3fJiMIhEkDHiK6V1SfnVO/w==", "signatures": [{"sig": "MEUCIBGszeXyxZNdClj/M8w3Z1FrQ+uDx83+rsoJt2Ex+iXHAiEA81DN8Bitaey0HBm3vWB/KknVkkUhiZ4Tw++beguVeVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAAgv4QAJvKHe0kbU2LOL36ZBWG\nz6OpVkRRoxsgBAhjnXRro/1vDbhl+mzYMSSZIwbE5Yt7dTOqlDLdc4Utnr8K\nvmaAbGOeRJUf6euySL0ZRz5rtLFhBlar2u2PCCqIVv6RLL29x8RJMr1L9HsN\newbgAsdmErkjlnp/5kfTp1AoHCyA+YL3+qaeydqe08S20f4au0/H5dFCWgJc\nCB50zldZmZiEyV6q3fKegehkNyxZr636QhFnWLi7iTs1trwz61tlrHd+b+um\nX00vubWL9t4sIWB9cPaHC5OIEdTfiIm7h8D9tgreJPt7GT5gf35du8qlF3yI\nQOW7v2c9tnkE1W73N+OtEd7CwkH2boSjCg07+OB7/MhXxBOA+DHR7J/DBOpR\n777SB3gJqJfPNuraFJHJE4qJFmERSOefUGjKd3ADBKarsVOrGPSLfKd+/R85\nmVlJosb6i76ZkdT1oXE6KkixELLx7fxRW9QcYEuvohc3BKEi47wQxlKlymXY\ntjOXQ3W+ottH/BZpZ1+v3dYpIUWpB26o2URa2hRKYGSS7aWbt0zlrWmX+x15\n8pS/2QyyZqZ6zJzbH34zoaXrp0xDxRoLDux6sGJSCrZ72r0mX/pdtljgl36G\njgkfR2VlCAmC3Q+KCn7xak1Cw/UoZmsxUKXDMot4xXMPVid4MmAzI0EO5WBj\nE4Lx\r\n=YnWK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.1.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.0.0_1566444224232_0.36438331765266474", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/source-map", "version": "25.1.0", "license": "MIT", "_id": "@jest/source-map@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b012e6c469ccdbc379413f5c1b1ffb7ba7034fb0", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.1.0.tgz", "fileCount": 11, "integrity": "sha512-ohf2iKT0xnLWcIUhL6U6QN+CwFWf9XnrM2a6ybL9NXxJjgYijjLSitkYHIdzkd8wFliH73qj/+epIpTiWjRtAA==", "signatures": [{"sig": "MEUCIQCpmh7LTYVVedLMgffOEG9jfSOgOtY3lksex48MVR8FeQIgVLNFuu4v+a92lbtD32eTg79wPhuCdvFX+9mDdhLediE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56BCRA9TVsSAnZWagAAyUMQAJJKZsxgO3pFlMEJ64Mt\n1iZZpGYO7mfqL9mOCTOBrXejGhkMIUV7IYB61szSxpEgo1m8Rjh5TXmCB+El\n2Ozj8kfTTwU5rgWmQt4+9z07he7/u1fbDzsXRF7w8raRrXA53U+xSvX0AM8p\nCHk2+j2hGiaQ2EfBSj4PRNDoPbNHsY8kSvXhogfzWbn7wMW0eTUjuYbRT5iq\n0in4A7qlQyONDLCLe1+HxVbkCr3KHTRld1LIMbc9j5Boyq2LN+yok+9/XIAK\nmhFN3jmGV4X3MlFPgk0zMxZw+eBY4mMixHy3BDBPbtbiI561bTX+oIMVxmjO\njiOyPzoWJKLPR8LGLl9TH9DwziZyG3dq7SUGOc6/W3ZqUUx4C1BzNtWRUdjQ\n7dV1YwgqCzYgPjV5oVfoDm0SgARvqSkIgvG0fCPCFNrgrQbF9+kwROOmSxae\n/pWtqJMqL6c6z32VmVHvd/DofJBlD8AVOqL+3xV0b1VS01Ob4W8QfgnEyEOI\nL61uYrfVJh/mJTnftNLoDwCMZmU08Phd2lNtfv6teY56a5nADHBUfsjvFywW\n3Lun/FUDvfVkowllQJhoUKmXGVIIsxLzIy4sheJv9NnreFnivkrqtWC8eVmh\nDPSKb0yUsTSDitcEZI+mCHAKyOT9d1VutUV41Geb08PgHj6uSQo/rT5JSO7Z\n7PTW\r\n=2DL7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.1.0_1579654784629_0.2423139838678674", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/source-map", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/source-map@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04c2bf8c2f2e4bfe3e97418081b8a3eed8e101a8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.0-alpha.86.tgz", "fileCount": 11, "integrity": "sha512-AKgM2rIv2KIafJPqfLMaMTNY34Jh6DnNtsmj0yakOEsOUMb2Xa/1rL6zkMmfMMRYbqZPotbhZefcHuusUblvnQ==", "signatures": [{"sig": "MEUCIQCSBqAFNAs91gODP1gnU4wgWae3G4ivtKn2oDJMoRf76gIgELYT68ISD/kAnqW1vjQgP7SCOhwRhC19r6sdjECrb64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAACnwP+wcCiZVG7x9cPSdPHWtw\n71HIJf3gm85mGWnmYVV2RHN9c46bPFlTWtLDWtwuhlSeznLOssSUkXCO2qUF\n56tmwIY+RlN+ay1jdpxdR+jhG4KbM4SZ53JAqDyHOqWlfX9RyFzIWvXwo0MA\ns8cKzbmBrQLIBRMhG8uUJdr0XTeRAoqkDwjzYt7vJDkrH6M6UY1HVjJEY1FZ\nQeGoPFDnPILorpo6YqvrSg3e2ehiB8Z1Upua97EprCv3/Cbe2CGf/OjGK8Vt\nShsmETGsHlY0LVRNxzzLLrsYjXNYjNCzba1Hga6Fy7E/9l4jSSTm2bjBq4uS\nYekU4ULrSOr3cZrFu40k4gWvG5qiZQX+6d1aDOkQhi6+tR7+2moPy/L5ZX8M\nOsP+4zxhLfBNjfKzgshlDkA7GwAf1QRLASP3HkFb3/GHjWPxYhYX4GlwduWn\nQ0h0NU819kIpl2BcPuTPQPkfFcncZdJ5S5JbD7zRkwOaKt2qws2UgJCmdGwT\n2sP2L3gg7u3F6BdPZHMxwjtIrdZk3DfTRDTQ4S/lJgh+/1JBTF+RTDHzWHhx\n6N8fqyAGt9bwfr/He9TchS7HzE7G8y1Z7w4ss2RAWJCWp7XgiXBvovEDaDOF\nRqkKpgqcFRnt/RWSp8djaE5nSotWIDeX+WrHKwgxwStx/TUqdpsS3EDgGJV7\n8K9v\r\n=sHXy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.0-alpha.86_1585156570927_0.9903880180565388", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/source-map", "version": "25.2.0", "license": "MIT", "_id": "@jest/source-map@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2b2d4e587a85fd95dcf6f3bb757680ac9f662bda", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.0.tgz", "fileCount": 11, "integrity": "sha512-KX8sYbzd8h7Nfc6dQjED8FzzR6o3QocpJllrBmsnb4BuVN7k2pLeIc2lRrUPXrPiidUwvA1H/AeIgGWNVacZvw==", "signatures": [{"sig": "MEUCIQCFM8kT1TDzHVdQ+aYgclhAyxAQWcH3r11vGUsGhD4mOgIgDx2i8Hic8H4kaRRvt5wBH9hl7LyDPpPd46SLUJFoDK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uiCRA9TVsSAnZWagAASusP/0H+XYgJCiQ8rhtrwJK8\nNTDCY+JVuHRAsxEJxy/U80Pe5C78lTjOo/rwXmKrIP9Tfpr5q4pQeYA2zZqd\ncA9M8bX4V8x3xxGqe6QQ9hFnZsjhWnus+LfYZwv0HTBDSqmRhs99mxjt/zAu\nctesJHYtmwj8BRvqLRhSAjtxegll01TjYSdTOtusSOwRqS7wPRKHUE5ngG1g\nHRZbPdNU097NGPNnJebwSLvoDlarj7r6wJxVc3l8SgRr/WX2SWNjXgWeu9T8\nCEve+0B6QTto9bfgy1MhdCe3mIPwV93AWUdAqUEBvj9KTSYd61K78UaNEVT3\nUMSBLHlPy+VouwWLUYWKQm8e14D2b2DBJZD6WbiaPZpz4Dl9VqHl6ge7xMQg\n9dpMg4pbbBOupj/zD9yHPQjnwvOvXBKET4TFxLuB+QqQP/FnvBcjmkHTZOhE\nEDCV9k4vwYmJUQxHguGv9ar71VgphWx+yq2fxViTP3suzSO1bNG099bXDjvT\ntBO+xF4jRZsRKVzAT5HI8nF47vuBlMojDdiRLSAnrOn1uNJjZ2vHlG15y/+I\nig7Eu0/Sljm/3yXLj6FP1NcIaCgjH6Ea3S4amPpa0SeId1iGJCahjqKymFZw\n+J53uxK8lgkiPG4OHQ5Dar21h0o0zN+9tCLqIOutl8qiqy+WhAuJP0BKqiok\npjmw\r\n=2FGP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.0_1585159074299_0.8213306616493432", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/source-map", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/source-map@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8af91a82d9448156e412f7a0eb93f7ed50fe77c8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.1-alpha.1.tgz", "fileCount": 14, "integrity": "sha512-obvF4N+XbwdMqwM6ELprpRnHaGR6w1clcNauoLJjHosHGUGK9qxrx5cCZKs+KWvtl492fPk88KUuRiHSHwkOcA==", "signatures": [{"sig": "MEUCIF+dJnkK7M65A/2LPLvJ+9ROCUr4AVzuxVRKm7gGSiRUAiEA5frvPLSoOMZxxr/knQ3F1j88xH+kK2+pGLdRbGpjLqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+lCRA9TVsSAnZWagAAw/UP/3yphp0o9Tb4WIpQhY3T\n1VnEOeg76zihn86xJ2SSpiQQszjBdvy4d38Mb41HVrc3Gi/fjwSVWdkOb6J9\nt542PPpVT4ydyEJ0K/aBMLTOSeNZ4wDk5tsU78iWTvvoTvXBwDJPzDv4Nnqq\nqB+AglcrE1v4sQsW06bM6kxRgB3Frj0OAJN1FFATl7dOcygPCLXKYtwxWjIw\n/HmGypJHPcc77++AWCiBOHjwt2Ftoalm8V7F1GTh8xWyEyDlENzHVAAwQ1fM\nStxSvNsDZwHc9lUMa39vSLmtWQ1LpE26MX3cD4xHRJopKYXRBRYs/FrEkqCo\n6TPP7NTRe3j1yB1hdsihhmUURz6pqhDPLNLcmxl54XMrv3301i/nHa+24uyc\nDRgSSKoVMuSZTv8JA7S3U400772wYH/ZLLfSaW1mjg47EDgK+khNdL1OUypr\nidoAVk7iNeJaFW6XXNz11vq9VX51rwCyh5Q4KHuw6ytbMnz0sEiQCIhYQZx+\nr1wK4alSjN0s9ZXRx2jw40jQiD0DCj7FNosi+Nad2+yEjbNw81fp1cnBiwOT\nsdnYSOUs+tRNEBTvbwau8obe+doZWr2ViGgctOd79olM68iUGTkrRP2PkSAH\nuD52lX1s0eBxB2K/mmusBrsY2WkVn+gmteNcPgK13g2QjDRlP+Fa33oTKbsV\n30U9\r\n=STSo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.1-alpha.1_1585209253468_0.6947178585673868", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/source-map", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/source-map@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7bac0fc6b9f89727c64116491d03ff41102f48e2", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.1-alpha.2.tgz", "fileCount": 20, "integrity": "sha512-qtOKII51QmCsZlV3kgyePzeTGnRvrdsQMQ+UVtW1ubEpWFvTxfdIvsRG8wvH6b3GnM1YuwgPpYU2NUEJWfRe5g==", "signatures": [{"sig": "MEUCIQCH3d+assCCIX6iEAEPX5/lUbkvrFo/FE/fLgbOzPQjMQIgYlE8En42kV5oP6dumcpnJFOWXneUPlHWAWSl5hvHR8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNtCRA9TVsSAnZWagAA/KEP/3T3EkSqG7UQTWZWmL3g\nF5aDQLnBJyBHcDEJRdjbSCW0JCGSMKGDVuxqaSkjkBigBjxDAMS4KgBqLLcM\nE1PwR8JD7MWRphXA/LKIGeMEP7BkqqgdMfWU5XE+ywXSjLOOFpsKAbG2FcHm\n+I8tpGKpIqAq1N1SJAyz/dxfWsUrdm+6eKFc3TeOw1rfI1829f0VxLjFhHse\nhjG83VrIE79c81oDZltalzVC7wGX6WjHF6P9lmcz+jaGvNASXf+YY34cXH45\nPxeRpQ7rSJbguUsednAnkJxNlgvuSLZj2XvxHYYP6XQNm6MYVKTkw8kYIOiM\ndOCfV8fv65XBOxOrvqzkL0yAo3HbXP/2uuEBXJzxKf7KkcwF9UubANBkz1RZ\nTPQ1I3fZUOYAUiEQN/K46xtxodnV2uX8vUWAbQJTCPWPPrydteRz+aDo/Frd\nrfG6Awv3GrC4KV0My97FvjDMXm8ZsxhK/F6wIRYuGK70h64upT/iIrohMZ1w\nbjJzu2TikBd9YHvXta16p3mAUNTmZIXcAsi8NMjeDRo/GCnrOVreO/2/Ok2B\nlgYXog7rYKUiximUV8k/v2kVj8qXWO2aGu5h8J7vjZupvyV2Hk8l4++vnMCR\nXOeeqOPjOqWfV8YdIpC7XbDtX7Bqg0klqzO0pc45y62yvquEQI/lercJ45hY\nTrWV\r\n=hObM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.1-alpha.2_1585210221169_0.7143579632813457", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/source-map", "version": "25.2.1", "license": "MIT", "_id": "@jest/source-map@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b62ecf8ae76170b08eff8859b56eb7576df34ab8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.1.tgz", "fileCount": 14, "integrity": "sha512-PgScGJm1U27+9Te/cxP4oUFqJ2PX6NhBL2a6unQ7yafCgs8k02c0LSyjSIx/ao0AwcAdCczfAPDf5lJ7zoB/7A==", "signatures": [{"sig": "MEUCIDRhVlGoAzyD+kNmgXryDVjxpmVOoll1FPKRvNHvPtVXAiEA3zzPmn3V6s830flyW4kgf6pYUPS1xNnsSXVs9Jqmzrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAfvIP/3SLcj9uofS/h0y/+Pf4\n0sNTTx8e1/YTnqI7c6e4qaCDx1CmbltlVj7xVCSVrTgJO4a2alCohnDRxigM\nx4d81Ppwa4+/ECA2SKxr2baSblyydRnClKCBGMTVB+miJ7MUVWD6aHgfyCJa\n1kYXoq1JuwCGi6tdNBL7iVhJdDcdH9QOcxErlzGloWDcOebRNtoYPPG9eepS\nqzi/EpVO/vkDbYoohnjwXRapus+jkLwfyShyQNnvTHYVDpWdqhpIoG0bi8cj\nfyazj6YbUfWb8y3Yw+MPrLLEHmXuXXCqmhUbkH1gEpbGj8zsReRDPDhAVfpV\nb0lGhSWXwuuEVUGzjjYUYQSELI6s9Ne0jUK9tXVfuvH6Bm2SEiuiuTH9M65D\nv877jO2WKDO8pNjaxgXC/Yc7b7e2v18zzXNu2GyrDz8xTvOnTAdm4aEt9ioH\nVEkdcSKWWASHozWZftmJM7ZccjQNLrr4y3wII0QmQWGtBS3oEb1PpiMZtokn\nNBsv8sVAyhDgtUgfPxNRVdlAhI/CQg0XYOPB5QHh7jRjdWCycHy8hazh+bkZ\npEOioLOE6786Ky6MuzFSUr48CybbsvFawUoKi/WKbIGpJj1w8BM2Vko0Orkd\nZnn2SC42xMKr/uHZbb+kXp5kpual9NtKNASjPN5Cp29WZ+FmcpuWihg3dgX9\ni9mm\r\n=EM/b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.1_1585213264337_0.8378366210587569", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/source-map", "version": "25.2.6", "license": "MIT", "_id": "@jest/source-map@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ef2209514c6d445ebccea1438c55647f22abb4c", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.2.6.tgz", "fileCount": 14, "integrity": "sha512-VuIRZF8M2zxYFGTEhkNSvQkUKafQro4y+mwUxy5ewRqs5N/ynSFUODYp3fy1zCnbCMy1pz3k+u57uCqx8QRSQQ==", "signatures": [{"sig": "MEQCIBvVCGyTYAUxOQ2PXceenDpNmgiPiBIIw8YlqFjmPNF0AiAeSAm5KYCYhUUic4p22krUp4mzr/0dTZb4EouzN6NDxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAAr+YP/1e5ofOPhrwWKqtS5OGS\nVQHksK/aypOIbSJeqLQ5RzDH8R9l/YGtofAVNLmzoIo1ZQ/qybafOnVC2JUo\nsiS/Qd7yZ7GhY4O5BuSBq5j+54OoF4J8PZCEEI7lvORV1NJc37B9r6xO2yzt\nAkghcMaEu8CBNz2PE+FsYB3Prz0JHrvRbHQXRckrmTo77YhW6y7eVhrjj73B\nV6IuzQfnm2WGRxfISQpTDxjhyrEHTEaMUBHqAVMq3MkHwhf2CurisFSncndM\nzRf97vaZZUrSl9NlPjsHP5ANu6r5eQhQNMRl6Xn01cVM/sy+a+aJ8ItAeImH\nDeRhBwzmSTQfd4XyaGkkww6lehiyanzkSXVxPs4ReXP+DQCQ2KBDZ/QahvRU\n5GX2fHqfgu1KC40pS0O6dR5jJAOOSYgx9bjm9RdzGxsFxBOxUWGhAhdqRR9q\nOze2uBu7bIG96GyIsXu0yVuBD4Y8KSDfoZvn+56awABkm+GIanMwi9scOmAj\nfT0KsnCMqo/gr/9wGUlThtn7SP+xt8qArGW2uYVf5qJ/w6onR1q7TVHq+t4Z\nT0zfn9uDACTaiNV/tnL6QBauhdAnWoj2KeapYsGwUo9zIAVEHLr4BdcueFML\niEYiypvhy/Nu3XcETYrmCYsXP/6Q+FTwg7wqOQTCDGDje60hE88ZwnZsK/hB\nFmk3\r\n=eQtH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.2.6_1585823347925_0.4053011123138819", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/source-map", "version": "25.5.0", "license": "MIT", "_id": "@jest/source-map@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "df5c20d6050aa292c2c6d3f0d2c7606af315bd1b", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-25.5.0.tgz", "fileCount": 11, "integrity": "sha512-eIGx0xN12yVpMcPaVpjXPnn3N30QGJCJQSkEDUt9x1fI1Gdvb07Ml6K5iN2hG7NmMP6FDmtPEssE3z6doOYUwQ==", "signatures": [{"sig": "MEQCICeSADa+ZuD0SPvsEDDAPwGlrMFSf3fOIFf/jPeJIdD3AiBDIH/joBq+RSKmlTRyySC3UFMzo6usM9XoEmC5qsSVIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfJCRA9TVsSAnZWagAAF/kP/1f/GvjBa5BkkiBaoACl\nLlN0JQ7KqTCE3T6jso/ZDXAlJoUIBnbUS8qwyPkiJb1zQamSf4zAG+nJjoPu\neOcaI8XQZGEbUsDXc9jno9eWGp4mBI2mruhjI0VFBEoA9K8xrfAUEjleuGMN\nbU5KA3NM4BTKfJoWoQwamGf7CI6ofSHzJW5qhnjL6U2/euUecpXl4KX6b4eW\n8+j9EW8jKK4E2yKRYJ/FPf9OH3tNpWdp2tf0Bt7K6qhcmMM1EDhg+HmfWAVF\n2AOqTRw+H6/QXUQya4FlROuqRWcbsUhtIoeKB2TTHWBOZ14bY7q4WnKogoCp\nNRGGvi/VstPvQCP8m/3/UblbCXKwraUcsltHjqAeEwckfPHKb9gIvhuyKrS4\n9ZIAiZpDRv8gGM2QdcaQ2b02ArMwW1z2xsBtj3ee6JsrXNDipI0laIvFFYm9\nVoI/RhBr6c+SpkSutxM0FkRbHqKef4mMIjUmlTlJI6NpxIRW9TjHzp/EoVh+\njDzxjXgG0KdyILbhiTdLVUL42EOc8036vDYoKINXiE8aJzjPp/0yPJAZjXhs\nzWWHaZ7IAeqNkyklcLfsjIpCw/rSAFIg90hJnPI369RA3/t7573sKsj48v+y\neoBu2aDrPQxY+ZBd2ej8o5yDfHArCv7/Z5TOivj+9WN04mWyaD8wHSJQDCXr\nhNbX\r\n=G78c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_25.5.0_1588103112677_0.33561206099674523", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/source-map", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/source-map@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20271e5276a1c847f8fb6471845ae3f94472c7f8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-dmPvCJJLNScDG2/RsjXk87c/LryirHyc4iF7ZGSJ/xq7wBHmNALud+itybuuvdBfJYQZlLcXlC9wavka094OjA==", "signatures": [{"sig": "MEUCIAe6SZD4xR+5HllqeEC7i0+9SQqSfv5Sd95RYCsfCE9OAiEA2jNwtBExL4v+u2Xw70o7Cv1sS7h4EoOlYZt/2dCjtO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPECRA9TVsSAnZWagAAhlQP/AgfWF1nFEA5EXkk55+R\nk21y4hY6KqlrezvwqqnlpQltqqMPVxJPCC+Sr1o9SRT/HhtDwTYUCilA8t9n\njE0p9xUlQOhVJCUDKZWDAeWpqSTfTCl7hMWkO/aSsSwUikdcFeMqO/T4jCcq\n2lQAqka/2GAPSJyJP9R3fIjd8qlgDucOfBYlaVElkAMzi99F1I16fe92KfPa\nQJRfNOE8fAesQWNqgsgMN9N9hfZoytKC+kaNAtROK1vi9QcELbWat4MuZBMb\n+yAsiZ70br4fQ0cU+SLj4ksJVkQP6LFDKBmh5j6gl7Y8nHy0W0pZk9q5af6F\ni1oWIFCLuSmTU1f6fJ9Kj0en1o1A1MGeCrgX/3VasaOIM1M+wbhv1nFeGRHn\nVw98xh/VjG4v79Lgk8elxmS5jvEi6Q37cFnDBH6EaWZNS4tMXF6pLrqzI627\ngmlxrZf5pQdil2TCFky+VhSCUnV6rLr4m7GdUKtM6BgptiRe9RNAoVjoqDS/\nmsL7T/ljVdZ/5u6DpgvT9tnBdNeYVpEbsGI2aVZ6mHa6FAgkxr4reuDBt0KF\nIIeMzQFPktTCYqySbPWzmL7s0C/IB5uM41OdZfxda/tA55+dFrn4cg8stkDW\nEgLPZ9lvkmE02ZbRskgmahTi5/R9U+SC80p6X7po/WUSmdPhW1XhfbZag4An\nOzft\r\n=ip1N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.0.0-alpha.0_1588421571777_0.5086362370234909", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/source-map", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/source-map@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "075f34a8068480db5b4d765f1362e237a22a2c7b", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-5SctHlNL6hpLdLSLejyg7T3EOntsMd91N+Gy899T4qJody/vh6/2PzebdWLCyKa0olW8nWN0jLaaBZaSBNRVng==", "signatures": [{"sig": "MEUCICbw3A0DjSI+f30r7LrGNYw19z2GMzwDzATxtsm92y1oAiEAw3bmhQfgjXSJC8Hvz9FBwkLDdzQILsub+aLY4Yrb/eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHcCRA9TVsSAnZWagAA8ycQAI4tKhk6OL4unXfuISXB\nhxCQjt8zJy4NyOjvIw23mlkzA+huBU2BHB0p1K8nOQ67c8OSsGLzyW7KY3j9\ne9qLUrMFJPsZYP0sJfPRxWgL+/3m+eGuXv6ksot00Tk4k6tnpUqSr/oGbw5g\nxV5GAYwJ/apwN6hE4I+Cp5SdgMCeKf3Q+8qkdmq84M3HVw0zrh0hQgmHLdyV\n9UttUH5bAr7/62ul/iHsO0ICfEyO5kzrwBrUV75pR/QXgRimDKNcn45ExA86\nj5ZmaQAw0MPbwrAusFJ6JXLk6wsa3KPtY6QpCD0lNZPD46rh/B61x1vLDPPk\nyzn4Al6Mu8qZu7d4amNDb7f28GZnnhSJGBnvKStm+JFamfBSthAcJuhW1pN7\n02T73bhagBBUT8WzPWXAoz4FGFrFMLFvR82dBMB5YFOwheo96pScsMO/B5xk\nQMr/LHzjvyc4LK1HDa18Qq3ebKJyzJa30NwCbM0/qTNz0c5/urfV5P+2iQLx\nj5homp84SSin40Rrvv9ej/AJEG6f/Xpux4qzkLUPfESXdXgDLcwHOnnbO0ep\n77zD1ra14Wfswyk5wOurXw+OfQBE5hYT5EVBhIzO3vJ9F0qos89MOGDfOzGo\n/b2oAbwTujGKDX4vQkEgrNMV13YbkEutPeXkJHiT6rsT5LlOb8l5NP4UYLZM\nTLAD\r\n=kfAh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.0.0-alpha.1_1588531675616_0.24695299058046838", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/source-map", "version": "26.0.0", "license": "MIT", "_id": "@jest/source-map@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd7706484a7d3faf7792ae29783933bbf48a4749", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.0.0.tgz", "fileCount": 8, "integrity": "sha512-S2Z+Aj/7KOSU2TfW0dyzBze7xr95bkm5YXNUqqCek+HE0VbNNSNzrRwfIi5lf7wvzDTSS0/ib8XQ1krFNyYgbQ==", "signatures": [{"sig": "MEYCIQDCrf6AIdeplR//aLFc5HDZWkqdn+fwkzXuD2On+QCcTwIhANr+ZMEG5gm9fI3cHyuRlCqru7vF/OOI+z5c0ZE/+0p0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ5CRA9TVsSAnZWagAAjGUP/jx0fJPEsZW8LyNjmn7d\nIWSx2OnpR/cIABR0Foyv0jDRIWqGrrYEIiM5MN/MCx4ca9qu/nTvpY+Qq+C/\nXHRfhspl4WvU6cEX36lM7eMj+rLARnTxK8vQrEVBdLeYMc3Rtj1WC2YX2QDC\nrSQ53d3CC6fKXMnnI9AXf4+gJhAUnF1l6jLD7Bd5boQECBxKHVYo+FfZqt6Y\nl1+BKJg3CkaFiuoD9jnL5bLk5Rt29sCwFK3QBpyLgV1w5MtL4y1g6mMQUeH1\n7Jox0ToTOdDOY27qN2fVS3yMtOVcZWQM62d4IJtAL1Y+luu+K1SgGKD/uIER\nw74uY19oqgZSCuFPsftfqzEa/hjdPuOKMFxd2LY31cPm8G19rSTHfmA3gBQj\n8xQm+0CFis86dVQ5V611tDfdKt0XydI2AcKbS7g6Rn6WLEDOiNs3TDxQktHJ\n7I8ukzgC8VuLx9aaE1BT3kUrAumGhc/eXnEAM8LrjWlwPycAIgY7v2jWQVgb\nYfzEfIMy7m0P820CiTjKuJiO9sNXheNI2lFIjbCAsrlqmD4vKVSe7ZuI3G0M\ndbh3NYv6I8OmDBbNpwRq7wq3f1Irb+QadsR6Dj+DpvrssaMprrxuNBrx/tAA\nXekJAuSB2tN4WhbIPQROXtS5OVgvSFhzyWl3LFAnLfJvMm6roYcc0QU0n86K\nWmo0\r\n=dMbN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.0.0_1588614776802_0.058412396656639665", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/source-map", "version": "26.1.0", "license": "MIT", "_id": "@jest/source-map@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a6a020d00e7d9478f4b690167c5e8b77e63adb26", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.1.0.tgz", "fileCount": 8, "integrity": "sha512-XYRPYx4eEVX15cMT9mstnO7hkHP3krNtKfxUYd8L7gbtia8JvZZ6bMzSwa6IQJENbudTwKMw5R1BePRD+bkEmA==", "signatures": [{"sig": "MEUCIQDuH90oeJLtY35O5eH7FKpnRwXHrCdppErNRF0pCBojPQIgJIcFNf2GzdeoS8zdEJwRVdKVjz6mCRClo8dBWNXya7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx3CRA9TVsSAnZWagAAdjkP/RvK3wyALsGhZvBgzSlY\nrRYLHDu4eiHWiY6okeBawdQnRpROQmZCHxs26GEu8axkg4LPV+8QbMSs3ra6\nFVKrH9aj8Pr0pgqubKhy5NdNTn/7zN43VPS8w40GpmOCCFLD4/IcX5Ex8F3c\nNd8+bDU3nKKvgXX/nxCtll6zy0FQPXvVx2gzQHv456wp+Q6l0l4MlOgzK4lu\nUvnhO/zEVPgklaodPRipL3UShMhY3jvIvsJJ8CNWJRTSUibLs7+utHiWl4tL\nSJIdjPc+eAkB6/Uy8QxGnwcrkv9ksc25q6VQcH+CT5Jmi/CUKLMj/sc7Y/sS\nutNm2TQVq5O47q5SuPW6kGlzUqonckDpTzVw1nE5wJkc4GGAy5iyDzm6YNxp\nahdVlAndzST0f2Oaq8ijKoB/hSedNsPcvqipIUZTY/LevKOFE4WsI118Ay1b\nwDJPPKzbDhX3ra25GgbpTXBwWExJWnsh+DbRCa4M3QYlJE4bTp5shUyZz9u8\n7R3OmBkp9rC5iU3ugVnCySZwokUipcB0XpkPvSmqUi+JP6YxwfLjIyzRTup9\nkt0X3Ms7KydqWs9V2maaKedxxKXJSRGo7EaMEuMihnyZtVDwxJDQhIVYaMZQ\n5rS9+no4oPGLo79LUIClnrIYTJrZY0hWa27qv+crUFfb0IXVwGhfc9SZp7jj\noqyw\r\n=N6v/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.1.0_1592925303173_0.9596464336630273", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/source-map", "version": "26.3.0", "license": "MIT", "_id": "@jest/source-map@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0e646e519883c14c551f7b5ae4ff5f1bfe4fc3d9", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.3.0.tgz", "fileCount": 8, "integrity": "sha512-hWX5IHmMDWe1kyrKl7IhFwqOuAreIwHhbe44+XH2ZRHjrKIh0LO5eLQ/vxHFeAfRwJapmxuqlGAEYLadDq6ZGQ==", "signatures": [{"sig": "MEYCIQDG09HwH2T4JRdglhlsNoKdNBKo/Az4z0ChLzxDtWkZlQIhAN7ymRHCRiDvVpYpxulqXwcPum10/oQadug6+yYc6nxB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAeCRA9TVsSAnZWagAAzTcP/1PvxB+955uRtIq2HtEZ\nOS80TXORlGJMKbNLdieFGyV6+v0FPEqtjTPGaR4X+ssoLH08KsvLttPOVs5c\nm6OFB+qOIOs14/jKTSXysGB9uTwS7C+8FO1cdRZtm8yCnbU4f1A27JEdWvDp\n3hakLxdanxq1uKtBP8jlNeG0P7FDLfQbRUph2vc94B2m2W/Pv+81KfBM4D+k\nhezyfe/K77/XrN8gTSairBjw7IKBRqJfYhQa9zZxzyFFfzxY/6hajGbNy07H\nuYvuPoHIM70r7Y5SVT7vc6kQYpKhFfYqnHuFKIwd1mG5sGMyiwMEz/1NMt+H\ndu++WvROrvggectYWg+SERoTzNBG3/juY74RMapg/DSzO1+S+NDVriiUvtXG\nd2NMz445aZr0yYIFs2sC/s1B1INfmwWJiDsVlBLAKEt5GFA6tJIoLlqr0pEi\nVIJfv8tykWiQ/Co5ykayaR8cYWOQGP0MEXEQIHYi3ZExF2eP/8CEsqUFkKyt\n0yBqeYxPPyqoLw6PqBKdv3PNlV6dsT4nN9Z9wF56O94vWgTB5NwlLWyhtUcn\nH+pCRQ3e7w0dfvIrAM+hNEgWCtkDzaN+Dce/LS07hSuuCFrWNPFDzqkyWX8s\n7uLpiLTxvEjnNbV0TIGvX4lTjW6hoPonJhCRB4qlS7TxG5f5/P0N+Y9V312o\nr3W4\r\n=5zrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.3.0_1597059101646_0.737793662875915", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/source-map", "version": "26.5.0", "license": "MIT", "_id": "@jest/source-map@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "98792457c85bdd902365cd2847b58fff05d96367", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.5.0.tgz", "fileCount": 8, "integrity": "sha512-jWAw9ZwYHJMe9eZq/WrsHlwF8E3hM9gynlcDpOyCb9bR8wEd9ZNBZCi7/jZyzHxC7t3thZ10gO2IDhu0bPKS5g==", "signatures": [{"sig": "MEUCIC4EJC2mNg8AaF0TUOzjQQBeX7L5IdgRSCgobEgXDLhnAiEA8JJkJFZRDPYUSiOKeR8rcmeY3ibZrBGIgOQ2hlSa2Sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucmCRA9TVsSAnZWagAAfA0P/32ruzYt6VL7Ohp2ODQ0\neXgKoESfg//pIpD/Z2b9y8Cp7xnyhtL3wP6RunMdgmuF8wejM7MBEDqq5M8a\nEymMP5RzgqvAAfNDlLzo0iVyGtBcweoCPiQqhFwlt2s6EpZhyrUvYh5U/Hw8\ncemA3uiCtG+QCSFR0TGRNkkYxde9VGZsTlWPJ79Em19Ip2ApnpQWpaBl+SOa\n2+/a+j/iDP+VjznYLWavs0RKsGNzZiHGP4W6dIhBc7anvZpgs3lTm13qKXa+\n+eq/PaXRV8iVN5JrA5tFhPITxC5j1qgFgNLO6VeGZpRB52OkG2J0h+YuPjPI\nDESRcDxh3gnBTqef4LjQoGuRp+QMgh6+UVPxiWKOxU1EKL7m+Cf2qbWAaCeK\ntT4nKpQ4uSi6aOFV7jK9K/p6/w9Kx8HpIq8qrOffZjvJ/p805O5W2j2a2oP0\nepzFn3+SXGO5nk5z+0nb6JglBArcuFN89yVxt9CjUchfxxwShV/l/PWUuScQ\nC05Xr2mQQrWiTmUnvW72pk7YBQDKt+uqlNHHxy5/ZM/MEG95Y8mUqfEQvaxX\nazFu0qMsuGWqmd9Y/l/uZoFuN7SqdVmsLtYBqDZG1WhyUs6qHiJ2Nzdg5h1H\nLGL/t8U93VNODD73zkLNMDCVPF3ii3kUDBGKKvCDCnqcZZ6hjF3EWoy5byLZ\nbn8/\r\n=L2pl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.5.0_1601890085994_0.8985216267818958", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/source-map", "version": "26.6.2", "license": "MIT", "_id": "@jest/source-map@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "29af5e1e2e324cafccc936f218309f54ab69d535", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-26.6.2.tgz", "fileCount": 8, "integrity": "sha512-YwYcCwAnNmOVsZ8mr3GfnzdXDAl4LaenZP5z+G0c8bzC9/dugL8zRmxZzdoTl4IaS3CryS1uWnROLPFmb6lVvA==", "signatures": [{"sig": "MEUCIDrdnSRzb7MX3dJubZor33IL2I7nBX9R+mXAP7VUXW6NAiEAtCmRcZ3hzuBp4B70xhWrU6bh63PtCVx8o7uWdRVLfSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADBCRA9TVsSAnZWagAAaLcP/ApLAGd4qFFmpn21+3jA\nrWiSjd2tBgM3VM6SgF8HjEC/G3/wYYDO9+1rZffY+ynaNhrbJZx2l+qOjuay\nKrLbDSYMhVYAa5TjHxdysISsMSOJxLqtdDixRkWvIYFEXA9N8D7wIo/ubNfN\nSsTVOknuZYwEKrE9uzLgTq9891taho3AUKeJSnkmyCMBghqcU/a7G28yO3aa\nA34f/foh9SgKtTX5gZFCwCFgwL2u+Nr4elpnHmwYxVRTnmvAgFWp20AVc6Cm\nd3vgLBUYDQ1rkJ6rp1QZ+oIvOKArcgQvnNnnoP30eSv+VIHKVjycXunfRORV\nVIdp8u+BmFTMLav7zZRMRiBIvYXQdeKRwdZ7ymNoU3rpfNInOUSW7dx98ft3\neVf5bV4gNnEUpY1aSVZEraBaggJMTquXj/nSFHihQukrP36NB+xX/28pbduz\nrTxzF6W26qajj2qCTSbxg8yBHfE29xnnCoVZY5uD0Vvdyzbmesgm8UvNa5qj\nIb3VsShUBprlbtsRjykkn72MfyKEe91STcII7EePaED9UE9XV7ttIhWv6CaS\nyKzzeePCKXDcf6tQTWt4IAxK9nIiKo0oMWYlZixeJYaNBgoxeHamMaVGSHfU\nKg8JoBjf+W8PBj0sxZH4w1bca7fNHkV9CNn7UXGD0081wVs158N+RYEs+zBg\nnzw5\r\n=C1kM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_26.6.2_1604321472952_0.08852750373168772", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/source-map", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/source-map@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "37462f71aa4a8b90f4efac73c48d297e481974b8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.0.0-next.0.tgz", "fileCount": 8, "integrity": "sha512-pp6PrnUN1iI1BqKceIVGTUYbXIVVw49GBLgVEoFNkDfuPZgLuH2aQFhnzyPzQqDyWzv2N0gLX8AnixjcV7P2VA==", "signatures": [{"sig": "MEUCIQDjIqEYidZl6rL2J/f1HdlCezeMmNDXYXJjSGaHdHHtrwIgO+GylDGLVs489Fa8xDKPdDCe5e6zPtoO2t4zpRNymkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8JzCRA9TVsSAnZWagAAOo0P/jwmH3/3i/Ga5WCHK3WD\nIoMzPCKOB0Jq4G6oewW4A1MEAhWYexN6qrJFs3+YaDhnJFcRkoOAemvtxzZx\n9TdbnL8xo8W6hGMBxRuGJ3WFjqaE+IAAFQezGH/kzpQVfF7awGjf6mrWO6m9\nSgzY9XK0gMlNx5uBb0pnDkByB6bT8LHwIpl6ExDTr09NnTQz/TkyAAz9IlOo\nxVpe3vGBbslS2A+SWNqGITpNxjeU71bQjgc0zvg97Y/Yu93Yq4n33OaTELuC\n3kM9J2df8zfrH5WaMiVE2XM1fIF+fFVriSV7wbWumMw4+qwuXyIit/E1bZ7p\nFq/lnJpmbFQk45HQJ/ESNgDd0fI2WObWAtaifajEMislDUEs7cwjNcMkcjW3\nNXB/8EJy9s8S+dcTv2IyzU34qDecrFOO+nyzdDmKBHoDes9u68FCHf///5Vo\n1r54+2LOhQ3aft48Ls2qEiBZbBFtm5jazEXlkUidfpnXT4NlY/efxTxXThvD\nwwPsq9oNH2ojRSLe6B2k1xjeYEbM3xST6w+9YBz8sZUa6X0swSMAEcqr3yxr\nGB8OX4zCm6mlyw5NtpWrIKOPHfVKrVIU+8ex63zfr/J5G6UE/hjZ3HJG4K3z\nyFr1EqEMqQQmrBfQ8IP3fxzE7LyBTq6m6uFtkBgc+c+uPWmLSyz1DVDx5Naa\nKThr\r\n=iJ/Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.0.0-next.0_1607189107234_0.8198940673470865", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/source-map", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/source-map@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ef4498c1641041cfb90b28bef51a59877742818b", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.0.0-next.3.tgz", "fileCount": 8, "integrity": "sha512-cup/B3GygP6Y61tL+owv94ftGs1ndnUsk8bYh8Ud+jF3nMRY2mIv6dwL6sXhh/SW5pEsc2OyRI4oKRgNFxvZBw==", "signatures": [{"sig": "MEUCIQDnXu6Qh+gvjt7lfU4vjv73ikE1drOO+WCF/4Wl96mrawIgSTjuL79lAB+CY0PUdtSKAb+2ULQ7owTSfUO4lN5NsgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWmCRA9TVsSAnZWagAAKYcP/32F1wQHRsP7ns+0Vxnj\nNH0GYiSap4P/8OztL+ABOUBeGHhn5f63sMWHjd/bWDIfwVU0pBPegLbHJTNw\n1tiEoQ/ZBdkY1ZZguNC0FEkZBh1UbDW78tp58WNOc+VdwglBeOQXtncJWXbf\nkBWgtIwE8t4e7djSnHP4BQvYGRJ+wl0ZTgxt8WX9liBFP8x5YkK+B2iH9see\n/GaSXQHqGIbUsy8mVmAuXJVo4Yh9goJkea2z1x8aozwoc4+3dmv3zd/HyCfQ\nG7PeTt1LVdr9+wwcyivEVB8tp6ldwsyquMv1aBc0lY3P8H8TRj7JAwhgQz+G\nNsY/SLabafaDZs8EYAMyPQ8f4JMY8H9ugXzvshz6Jv9NR9PTNLGJWifPoGyL\nxLOiRRxsVH4OIGn5FA8+vKZZeT7KjA6jmJiq4RQuHJzcg0vLXC+EuLfxp1Fv\naP8/JK13qsEjpj9NRtTRQorFtD7i3oWPwA1Mx9bqc0zhLNofqmA8Q/3ee1rW\nNljGNod7Buc2acOYLFW06k3uZhSoa4a8zTP0nkhfqHKRwTgmQD4LpL28jvVB\nRMFKcqH2nyfdbu2X40nfZznpWE9AYOEJmnGp9TfqYvP5bKr/FAeyW3pDvObV\nRZQ30I7aX1oY8wjxVgiYb1Ls/kzM5+/bgnXMUZqbBbfJp7OYtoQ4ns7fEBXI\nZLsN\r\n=2ujJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.0.0-next.3_1613686182114_0.06221354235676313", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/source-map", "version": "27.0.1", "license": "MIT", "_id": "@jest/source-map@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2afbf73ddbaddcb920a8e62d0238a0a9e0a8d3e4", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.0.1.tgz", "fileCount": 8, "integrity": "sha512-yMgkF0f+6WJtDMdDYNavmqvbHtiSpwRN2U/W+6uztgfqgkq/PXdKPqjBTUF1RD/feth4rH5N3NW0T5+wIuln1A==", "signatures": [{"sig": "MEYCIQDhUZmrt3gFhNeCS24QXEV6Rdgm1z4ZcMXxAKJyn6LS1gIhALAXPwYoWUDsateo6BOFN+05NK2NpnM+cw5KF0LqyLe/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAAGcsP/33yE1QR97FsT09poKBj\nFPrbrTPds+8ygJOzMzkvBt4Pq6FEco5wPzSnvEsna07CVJvx7lc32tMHPtoR\nURJfk4JLVvS3MON9UOr+TENQ1AuuEEhAgLdQZmfuVsuaHghIYeJoGJoMIzAY\nVjmlGwcfNpGW+0f4Glf15IUIKLA/YC4qwpwatxQ0o0HrxqW3QJnmzNPnJ2QV\nl7E/Wqr2KUPI1wqDGPh/tduF2qJanntyKxcNH5Q7oTYS7G+MgUQUT+8Ul7IY\nEdzxsQXKHIFZ2aO4TZY5DmWB+pGyf7iEWoYTI4oxP10Wg62qRoTi6MvQPCi+\nkmFuAyAJnFFFV8f5AdJMA9wh/nlD3jzXmzcy1JCaR/a0mpKscK1/1+H6P/+3\nP2mkyqX4y9iVEwO8DEFv7qcZ+dceY8qdyHAEP2vOAc6XtPvLMkp638MY8iwu\nEMuW9YmigxzWYqrWe/74sNS0nKuXwh0cIE38EAcvjXnyafFdeAtD0Af/WLP4\ncLwW4OGbq5WSN4smlQM5Jx+cgL3GHn0maMgGsXqaXM8dcK0blfbuhs0VU5mv\na5xFQKBhVmUMXZkraQ446ogEFlBO4a0Re02QaggPyzyIuBmwe+LGEOVyAGcv\n2Is6y41qBlhWyebgd5sI4F8MsWLHW3ywxtksQcjCWolwnicOPK8qz9gosmLV\nQGTd\r\n=OXdn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.0.1_1621937183527_0.2009314549196246", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/source-map", "version": "27.0.6", "license": "MIT", "_id": "@jest/source-map@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "be9e9b93565d49b0548b86e232092491fb60551f", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.0.6.tgz", "fileCount": 8, "integrity": "sha512-Fek4mi5KQrqmlY07T23JRi0e7Z9bXTOOD86V/uS0EIW4PClvPDqZOyFlLpNJheS6QI0FNX1CgmPjtJ4EA/2M+g==", "signatures": [{"sig": "MEYCIQClLKLqPjWWQJWZhNfTcwP6Cdags6AEaTyjbk/V0cLXHAIhAPCgZ21w/dA27jKLc6pRGBNp55i8w05ffdQTbMSEfaqr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAiyYP/0GksKORnzlBhd5IhnJc\n2ieoVx0gZl6Ay7+LzR/xsZLLYlCqzMXMLN/S96PnSUGl2WaO+zjUCsuQXHPd\nh6nqmeYfJqdhsLCdUkmNa5XSPZmDpkqlOvtWQ9A5b7CIqTg5zJ3QQfO/DIBc\nn3zAXw36B4RtKg77HFxpm1CM2/Cy1bvl/wRNzyNwpvAG0QuAtX+HrvpRdvMv\nM3jmQUhbhyD0bPs8dtxkpl8H5jajOgD4NkBkMA3sK67z1BKRZ3vxBce15sEL\n3dFI4GxkZK3P5ba0pqnygnJUgiHV1SHaaZDl5ik3pxsLpclW6Y2Ym7x05tJ1\nhRmLoyrh17HRSypYe6tziOXQwWKOqGQVFu/E1O+aP4C9CUAqZXkwiSr6j9lT\niQrsyYRjzSG2k7EC75AKY/a1PiTXXvjUPrAuubLnzPtqj/n8fPt5RE9plLHx\n0Rn4idQf3hx9KMTIJYKIn0+u4sLfVoVYEXtruNgPPg2018KAtc+gIOFONktf\nsLS2LZzLuPCjX9IqI69skeBNadxAKV8Uv2q8z+L1xPq1w9WEf7x5bDLN7PjJ\n/gsaMWUZXoId1U0bdU2vsKC8NQLXe9f3Pqr9XHNj1+JU5BSekZQqZdznYqUC\nlgRdorMMA3Iv0e1MNGCB7PVOxEs+eUSVHJzNumT2Ggn0PJzzL5tJOUwkOVOf\nXvA3\r\n=lrNE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.0.6_1624899931447_0.0922859645017311", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/source-map", "version": "27.4.0", "license": "MIT", "_id": "@jest/source-map@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2f0385d0d884fb3e2554e8f71f8fa957af9a74b6", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.4.0.tgz", "fileCount": 8, "integrity": "sha512-Ntjx9jzP26Bvhbm93z/AKcPRj/9wrkI88/gK60glXDx1q+IeI0rf7Lw2c89Ch6ofonB0On/iRDreQuQ6te9pgQ==", "signatures": [{"sig": "MEYCIQCwRBC+iForNAqfQ8wOiQu1JU6ez2h8I0+QYKvyVv23fwIhAPhCsi7Dm/Uu/4UqbZnx8fTvgKtbhlrWSt0bbRwSgPsI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAAGFUP+waP35cOJVXNBsEd1/Tu\n+jZbzHNonnll11nCNnSsugNr6rGptJi04BXjrsw2s9qtPv+dVqIgPhlRxSCe\n36SQmYymaXxDhJLEtx/4lmicdTr7An6a6uYAda6oL2taTTW8+tN1dQ4y3YSe\nbazrBM7xLSK5J1bQ7sCj85lr0/bsVSE5Li1Mc+SU85e8g07CWWEXnBEAaa6a\nRexuqvP1K1lklYHUOokodwSCL6HKYsaQe1/mY21FqkPnYTi93mms79ySYWUz\nU+yZolkHXtZwp9PazP79ATkryhr6F0dS5j/Yj6z/6YE+1aE/a3ERlHRW09N3\nxv2LlYaSlYJ3bpeV48mfF6QaSJDHkBJ1h/zmcbRmGihGe/laUq+A6haI63jy\nLaUlrCDCSyKrJGD/DwqGOHrbJgc2iqgg5euwJGLwyOyr/N96gdKVrH+ZQ8MK\nX5U181bmJISCk4Ciz6XBNICyzc3xx9nt2vuRsVun/9fi/Fk/P+Xorok8rMwX\nCjTvQeMANUis2/PsMUZpohoBHUSH/+gsEUlQKLvIJlTSVB2KIxWLZJHyRocj\nhpEaeN7jCp83sopq1UwgPYuaJBFhC09xaM1Nqz68ZvBx3XddhbjU8hSWXnmh\ngPay200lqmdco3SksQJDJgfGPTpg0QnFfA2p6rlc/6v9PFN0AwJHCCExA7lE\nhzkE\r\n=SJuH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.4.0_1638193014606_0.2961215402393622", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/source-map", "version": "27.5.0", "license": "MIT", "_id": "@jest/source-map@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f22a7e759b8807491f84719c01acf433b917c7a0", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.5.0.tgz", "fileCount": 8, "integrity": "sha512-0xr7VZ+JNCRrlCyRMYhquUm8eU3kNdGDaIW4s3L625bNjk273v9ZhAm3YczIuzJzYH0pnjT+QSCiZQegWKjeow==", "signatures": [{"sig": "MEYCIQCzRvhwzNL7UQK7Hk6/HdlWIM57aVf2DROWI62IT84ARAIhAJgfeJPQ7gbJMGVj7waUw3TC9SByH1WJA+AYa+g9YTcU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAAo5gP/2b9TqhPDOVoiRM4jZnX\nae9IH3WIKJSeT0prV8aBZ1wEcAsA9F+0qomfAjPS5Kr4tmVWX/Ef2XgrDUKV\nNKBTs/fni5FT5po86iTqC4SZ4+G8O+/J82CYZY/pJZ7V21r8n18ST+ULOTr9\nL3eSUSlSS6r3jOvLZN8cEboYuxt5mwOL6fFmYHXhqe2R6lF29XpbC20P7NYK\nLQ+TVid3JZ7gRN+6U7zWExlheRHaRiSH8GTj6GsmMWnQstbBRfsNB5XRMgYG\n5m6LOwAfcUuOc4C6l88VTVR1rncTdSjBkW0rXRF52wdwoVb5M+3UJ/coMRdu\nrDcbmiIbkwd+5pVgHv83h1hHUdE0XnRPr0hJYpCE6Q9+sJXLvt+4pfcEXz0V\nOC5a0jR1IlSSIRzNel5Y8r16XGblmBATxfyplyiFV/ZPFU65PmbEhdfF8mAA\nNw4N/inyxea/h8EdiGyDBWZwy3uxNLZ1l7oe4xRr5skiJ5OsqdAklFj9NdZ2\nX3Mcu6cR38FUn0GoEbzzWwrM3bXsoD+5uDkPUckCFnYA7GCJl2BRScJjDou5\nN5VQ1qMBrOXVnPxvupx17+seyqkDE7Hkgcrr9SoNEbrBBgW2MsFCFk9UlGs3\naQYWObuRe+TXwpK9w7e4O69Gzc/doqCbRicKxAmFaWf2w9PfwSG0JJsq0k3l\npM8Q\r\n=p+jW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.5.0_1644055157669_0.5089972316397422", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/source-map", "version": "27.5.1", "license": "MIT", "_id": "@jest/source-map@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6608391e465add4205eae073b55e7f279e04e8cf", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.5.1.tgz", "fileCount": 8, "integrity": "sha512-y9NIHUYF3PJRlHk98NdC/N1gl88BL08aQQgu4k4ZopQkCw9t9cV8mtl3TV8b/YCB8XaVTFrmUTAJvjsntDireg==", "signatures": [{"sig": "MEUCIQD7y9y3KFT8GgTUcYCq3oWe29f6jDU8Q72UUCozVh9TzQIgGTJlK7bpZYlHLsVCYH5CKVIYeIWKadSlfmPsg+CCoQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAA2IEQAIjvHlIP2aALMEzQJpku\nS+P60ON0ZXkjpNR6LsjKGK1/ZEhspS1BGyRf/Xnos1FGe1/HTn1V8B5GoA0V\nT2Ot5zrGMgxcH8LRwk/Y3qqQLz+cJdELZ5era87cSASuj+rtMBgSdBfrx406\nGsKPiMGWYYpIYB6L5fOrmoIvCtEfm4rje21RxgVZKvDVBQGPDh0llED1U5Hg\nx+mcjdHCStUVKHRITC4h4ObpQ6GejJPQ4hu2tkllmmHGoM+45ycyljm6At0d\nVJmZx03Z3Sdw9dheGqBjCqsDJ68DP+nqokog3nEIFMinKXyjh9CP3cxBPysE\nwSMIR+D7+kb7I5DKKN+UTlGHTT1NgJyRdYCLjynr4/qkTOyzyfgs3YyUcMEL\nGA4GFRd6HSCvibFqYBXksvMzq56kPXQ8DDoHANCOvTC3emNJnUDa5pLS1MZh\n3OK4tsHRuTEccehuvsz2Fcl7bwFBoZ+UpHjsJ03W2LdJvP0Wlhy3xskQvpCH\nvhuAQv2QlJWbeexNZTy0dVGkk3mFnTqVsjf4Kb7SMiEWFBARTiwbhJ4dJ37p\nZ3NWjaj6k43uLoj3K2a5NVSGCXJYsEruGHbgJ7czlQdUEufI/B/uA1AjRCoi\nqFS3ykOsOZ6F6khuLLoNNyETTLcbbET5/ZE6kiFzlTIlr05KwN2FYHDT0038\nc0Zu\r\n=AS72\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_27.5.1_1644317531888_0.1353643056909617", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/source-map", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b72e7bd255d8cde8ce7823ae5d1155c95d819f6e", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-j4CHVZrEQDQLUdFufwiwrNz+ZNdrxe69ypQJBjrMEVOQF8bVQvWQb4TCp+KKV0J/iXDqFwkRpppAutJgzswc9Q==", "signatures": [{"sig": "MEQCIAVTa1+DMZxP8GypSNmXzKbJsv64OmcuTJKtdVVBbGwIAiAavzniPuRfgykkGaze8SuXPqGJbfLBB9X+ko0y82NXjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAA3k0P/3haJSkAe/EoJVtOFRQk\n5UM+dfd5AVQI2QpEH8Ymn4GUUrt6xvzkZbaDTQ7ABZKPpAmDncwSPYukZP8u\nIOG1lB+QogNkrFbpc1zWvizuNB34/UUuL4On6N+dCs3s2BqTumwuV+nz+rdN\nntMLaP0Ec/LxSEwDmB3UsaDdnllfpR2M/BbLSMSt8b6WDG3ma0HFasUye6bN\n27j3XRpTYyXz81Zghm+0xdUOX9ced3/WCbAiDov3VchcWIhFORaZYYY2Yji5\naQFaOlQE6SIkf/ME2DdjmAZmt240y0MrmlF1c631DXssLTrbUGPkyoeVEd6G\nzSsZQqTKVQLXf270inFYuo4OhoOieWx4QPHdMqI0dQlF9CeDcp3wOPcpE58Q\nXtTZOl5DxHKp6qPOSu/Eu+CII9f9F4W9FVecdPNCylp73GEp4AbeEmkwVjhv\nrHad6EuG3PRrNdn42dHPn4rYa5CwknFaZOvJraJ5Jt9jXRRWWPhG0rCuBPFc\nIOXyCee0WU5LvnBtfR7s2SYTykbgdxqSDg9ZuNZuPm2RGfYQf0w9H9AacHoJ\nU0V3D7ycujSz/24PcwYIE7E/KX7bp4vxiZyBYgPUemxtmEH2RDt1V0zOpS8n\nC2Rwep3eWTLEWmvUp1WFKiMh5gQ6jupiASTs6xAoS2PaKOyOB0Ve8R+jdrl3\nP3j0\r\n=Q7Ca\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.0_1644517046235_0.6586426475556093", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/source-map", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f14b7bb024dc8f6b47bb4de3ade88d379c61f62", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-eX7vvyDTjNRwXtbxrLt2KEIVMF7AklNX2501i5V/vWJMYyMKWOsI5DAd+WlzX1hNnw2UaZa7tfEOpV0j6N6ddw==", "signatures": [{"sig": "MEYCIQDn7oo1FonRgW/octEUKnYBgmSCfb/DjESleAo1IIhEkwIhAPK5GQUoZZfGDRPM2XSgeBdxOOtItgG/o+7aDKcr65xV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruuQ/5AAUvonh+GeExiDb+S6KdjPsOD4lgfh+alXkSXjR1dLAfPac+\r\nTFY30igfmRWJo+EtQtvr27VNQq4rpzannAqJuPZlWKO6skFa2AUGHu6wH/UK\r\nGgzFktqPODMUsh7v/VlXmSAE/6UPcYR0OSXSoiq9AVFuT7IkwiaaqC0p9FzG\r\naPK/Go5E2iuMI+sw/L0UX9BaJauREEQ5tt82PF4yiUyF0ueFDpKJpYGt8yM7\r\nIc5XJOvjIXLwlH5hlZGTKgUGBfHIpV6EQrWJ0E7DBCsxFBWU052p4Ip4h+SP\r\nXp+9rFL3lnyRHLld370zYBpsl/991dRTmbo0OavcHoxG0UZtkixKv5ZiGtkK\r\nwSqcR12iaHq0RyGSPLtPCJGRPh1DZYwqqKd83l9m0qJ+nQ54MfuxBYDg3HWV\r\ncN81Ag0RNL0Mz6+0LX98ZPctycPv67CvFAH+t2qd6CsZ3FxSv0oWA2HsBjyX\r\nU4MkNVE2oL5skgZCJYPwrVcVcONzO8hPqRbciTB0+05PUvK8QYqkNGPfRT5n\r\nVme+Txocw8U/aBAlnGfc8v5jw0F9ItzEatvr3yNYj+Jynvc9WJw0lfKTBqlK\r\nwovg86ngeNKoG5qMjUBZ+fNn+cOgNj4AbsUlU/oclIQ+s6yU60dpM3gN2Xsv\r\nG6O2TReaL3LWEiApZTU8vTUpFH5pZoCTtQ8=\r\n=bGt5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.3_1645112539925_0.38528488702680397", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/source-map", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "84884a506d004e2b097b996a5fbc313800f1d8db", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-5tcoNBafrH9qCnA4RLJw8RHLper10bnexGHc2VAzux7HFAeRDLLQfqYtqVVqFB3ka3yS1TvCF2S4M37L56Umcg==", "signatures": [{"sig": "MEQCIBE8IXFpN6kCzMqMtJdVpmNI+pn8fzNFqRvdP31LlB2YAiAXbGfDIJy3I5HXTxnFQW4373+fLlMSgq+n/BtEdxITYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKrw//RbgysHpSmM/lvF7YxY/M0l0QBUocgIcCAdbi46k5eEXlawuf\r\nezQx6pn2M+TBusCZ9r+PqyKG8DHP8ALpK3Uhh8aV0sYP+dpZKcdsG+4bjHP4\r\nVO55bCv/GoL3WtwvfFqO0Nf0QBYMn/lv8nFfmZkyylxPC+VaodaZwSI2wtdm\r\n6ih5q01PmNcKpyTHuGdIXTqaP0R2bepYFMm5CD0yYLfiJEITiK7WWiM7tNn7\r\nrKIrEGF4yF8EpwbYtper/Ut1hkbbeJOPzvSHegdOqup+YrrUkbftTZrcE45J\r\nX+yGEbbxabWSXhIFJ9WSZZXtFW2lsoJhiiu33kLuSXl/F7D0lNVo37UIYId7\r\nIbpjG86zlq9yr+u3MUPqB4wJiao69kkVN58R/P53rfCaCQ3keLg98ynCy0fA\r\nHKDnNCuj2Ll5rwtlV8kaa+TM62Yq5a/ySTT0iL+CMDOw99co22gwlZRKvvQ3\r\nqjme3N3z4KSSdEDIGc3v9ap9kcQ7AylQ/ZLtm7mWNhDjus+lBfiZ/QDNwV4h\r\nAlmvp69v+mZpIfGQDmG/ZC1mbgCS9EQZGnDdi0CWjKXri8N5D0zKSxaZj4fh\r\nZ+z5ZaevVA2PT08buPPzHjRDjl8BSPjdI8NWMhV2O5VtF3j5uC050lzGB9lf\r\nrzaYURoMUOK0sh0tD4Tob/cFNswjsLlxFOc=\r\n=Ubmp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.0", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.6_1646123542302_0.862866177531231", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/source-map", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "411529b185a6eb195cf2a76635a19bb3e772364f", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-Tj+a+9Ml/HczK33m4cWmw01gPEIfg3+S2RfZOvXbcGTMqYx66/FYxHoXLR8PAW3WgXmnWX9uSI73gdyw4a+auw==", "signatures": [{"sig": "MEUCIQC6IzxqKMOtAigasnjC80AgoFTa6yrZyjLMNdHi5V0b0QIgCU/NFbQ1yuIJm95KOtwQi7Fbtoq43VlCNVkt+UT0rII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZDw//YrdliSyPPrecPldiy2o3mUKSMuPIFNhDub0dvAR3GRkZwF+a\r\n/OUsmlJgYZQYmRhLX0Kl6uZ9EgbyDEjgy+pv2t66LVn9tUZTMWZoSh8z8alf\r\n2bRflShcVxLisdncNc/EoBE7hDj9TojoxUjBu1E1nTxIEv6TghjiCGVKu063\r\nT+DT9XWnDLp2I1d0yFSphYZpbuPzIdsmuUdYHO6DDHXQzc9aGFN5bQ1rNxbO\r\nekGvm3SDXQr5zMiVFIOt4SjFV4fUWXQm8AYl90NVhADGJFP8sBKjOcHWnCA6\r\n8/xnSb5Y2zUtlJrKOPE7xShiVFjbBOGx7F5D2L0eFIekG3GS7IVu22K5SOnG\r\nGAZ0gzJxTUZqmgeVbqdmaDE0lCv3VI9aBmOixj6uy47vsBat9DdRl57T4dyZ\r\nNm3A+j4PffD/0bNBsPGwS8oKdZHSyEV3EioV6JN3fxm929dBfwN1h8n73VSQ\r\nIWvQicktpXnENWwdeCI6J05M+pqscLgr2kmAmoieVz34LR7XovuregZrbO+q\r\nxPkQDZDsAg4WDFmJDBsqqICwTjMu6CWaT0baqHhQ0GrilmeX0gtWYa/jx/Io\r\nJEtXd5rmGNLu3gpLcgt0pt8FbXe5X9gJJ+6nXYtSVLp+fLE0LKH0ZPiMg4z4\r\nPfhUVv/UWGdZ2IvgRoWxAS90a2HXh08Ymzw=\r\n=p75v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"callsites": "^3.0.0", "source-map": "^0.6.1", "graceful-fs": "^4.2.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.9_1650365953659_0.07939096594482886", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.10": {"name": "@jest/source-map", "version": "28.0.0-alpha.10", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70fe8afffb5ac62b971a7b145dc642054e3a632f", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-SuG06njxXjR87z7+uXXCmVozJ3IV85OTMl2Il9C04yLys2dJe1sFBGbmXqwt6gxZXVnKRgQEAyPy7KW9nCKq9A==", "signatures": [{"sig": "MEUCICQtNVeeEBA4e6kgtGgMtU0on7oWPYMCGNRzKrB9JDVEAiEAwuQqH01yCdN/Wa3b/Iqduch8ff9mKmI7tk/V3IC9tSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX7g7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqCw/9ExLeyNL14ta9abY2jsjfSqpW6T43CpuPNIkwOC1oa5kEaYCS\r\nYYyx95QngJz7Gliqu4mLkc0uTKK8DSs2MhD3r6a6YVQSPiPTNHL3dhwJ0d6D\r\nkHdSWOwqDX8Jk9J7qW2UpUydQ1JiVtA024EbJUrNZ3NL4anj1+d8nzApSAqX\r\nftoTyEKn6YG7VTdYCxuwD7cnYhkrGB6s3g5TfCaFwgrXHMx5Vg5/G7Qgr4Z/\r\nQXSUxAaOzWj42sZrC+7LtH9k/taxXAaKIkTIEOyYsTtGCZyDVwUXqvW4ICT/\r\nail1P2DtAZFObE+KAEFc++c6hQPfs9RfWmIJC8twGqZ3dontiRbjoEOYQ1zw\r\nuGTK9rxcBSuTSVvrj2ZeXmhGeEalniegx4g/hgUDbTNPFNNVc6AlnfnEM7ST\r\naT2ibt4JtkS+iDxslUKOYIK2fRw1k0gxnOMFVn2igv2DjfBF2wl16SbkUbhG\r\nYwRu9IW84RBdZJuutH+hQrY2weeAO0+1JHb4KQo6yl29SBU4gA4vHg5BzHVT\r\nwaf9sMKK5RmYtq5+rB8TzruivgDJd+8HZ4nWhWQCdugLYmwvwvF8uaRrmRPn\r\nlmaClTcvQbBhZnRBw6FWg0aNCxQbo9mWLJ4WFCIjGw+FluOKtQbtmMT/hbEF\r\n/WUmL22S4/elQr+xbSTZjdj8q4NEz3nR78Y=\r\n=XVFs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d5eda7f23571081cea24760261d209ff3e01a084", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.10_1650440251190_0.9827523407631129", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/source-map", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/source-map@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d738e3ce0f654ef11b673b941a1923485e8995b5", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-H2VCdwRd9kzA0ZlRJQijMbI2+pEB20/NGXT9pOAwglErLRJQCN82YEa3aE3oTsLlqpqHC9LbVOpq4Ijcmw9eeQ==", "signatures": [{"sig": "MEYCIQDiTbK5oe1hWov8yHVwxHo/Q1rudqfHEE/aBr+T8hWDeQIhAMoaIPTyn4vDANtzYJfzVBVABGVw8+TnCqznMmZKRT8V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNNQ//cc/aBtj1nA7STovpfbap28avQRB618ePbx67dwPEDFId/xBh\r\ngQAgEqipu+vuEQeVBuqlYHleOfojAQElpcK2Lggc+MA1xFHd3yeLaaMLW2UL\r\nmQxPZlqPbQ4SIUBDUBuwl46H3DUmLAKV6enor4L/Nfl6zZASJRZlIG+jn4Xc\r\nzdmTySEo9tiuV0BXzawBB7kMbosmxv5BwsGUSio3tOxINaZxEDgnK7yF7d0w\r\naNHHEYK+UZgDn6jHuEA3MvMcD/jbzaJp6wj/n8Kfga+hpx7leQNVbY3fh8Xi\r\nuHsKjJbZ7bzsCNEQF3DfJ8LQL3lOei1v2yUPDAUAAYLzhYTAadG0oJUMv+f+\r\nL9pHjDEPUGuxoRfYjLIwALf9WKn9y859r2BHZo9TcXOOpNcmoV4+J3RZ7AwY\r\n5NzLzrHB4dpcjI1bPzuCQ0q9KnjT2TZcKxfniU8mxplcXTbO6R56f+sCW0XZ\r\nvq5okNzIZljbgfUMuxt2XPHval59TQ6ukh8utdSBW043W0mYjhB4X5DDW7C6\r\nhIizaOJc4m0xJmoexnK3br4tW4gBqsXhdswAZsRKqiMnSKmxMDQpwo/ya/NZ\r\nDYQGs5jJwSiMFGZj/oTmW+o7MiB0CK0opZWdzGkA5uGOsv5q0Kdek7hD7WKX\r\nFx7iB1I6o3C+rX3jBz+cFlEfwA18ToYnG2w=\r\n=T6w1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0-alpha.11_1650461457760_0.5720303410734111", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/source-map", "version": "28.0.0", "license": "MIT", "_id": "@jest/source-map@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d026fc1cd7e95fd7ce788d0b27838d9796da0a74", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.0.tgz", "fileCount": 6, "integrity": "sha512-yeD/Y94j6UJPiaZTG5Sdww7pbHvEc7RlTucoVAdXaBaSuNcyrAkLlJonAb/xX/efCugDOEbFJdATsSnDEh45Nw==", "signatures": [{"sig": "MEQCIDsYju4VWAIJJVKxoMBYtVFwGUxNkq5mt4Yht1DGbgpOAiAc4Yt19x+gSqAsgwNv5gHJV/xW044/iWZkdhfQ37//CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohSg//f9lmamtxqmyisgoGE8QK/S4Nm/awzYyG1w7kXYJTFdAWyb7K\r\nReZnTF9GPZlWgKQH9IyDFFGxhCuKJbBSWuTrwBO0DW3LmedHfXYXmir+Xv4V\r\nbVjx4utS9aCSVZ9UKexHqGoUQvTY8sX9ePgZFgwJwvLYMovI52455pUplohJ\r\nvGRn7ICrnyXe7RdiJc8QdhRBi36milqLmojhR82hUAY58rv0AJpDRbru65Jv\r\nnaKPSGgsi08PVNC64o/Mme0WzEkNB4ap0tobYObEGGMgRNkkF0XJ6i6i7wgo\r\nVvNpS93Yx/s6/RvQuKd2Xa4T0zFJh1xMolhnb2DImb0Hh+FF9FCA7X3LLBWT\r\nHVVYTjr/eyM97HYy8228axX3NrxjMMgvl3uJES+6DjCdx6Ysfrpfu9M+uAxx\r\nnWdQpru2VJf09UsQYdirGFRyXRKDubiTs/HwA+XLZOHbsh0IhQsY1eXKKXL7\r\nRlj3AkZDDfcBQkXRoZj+40dkUB3sLe6wstC5/RDmrwn0ISCLHtYFyEjnbH8Z\r\nFnxfnA7tsNkQtdUi4pwpV7qPLYOAIVd0D/+KykTGjVvgDxUk4Kfkg43XnhLT\r\n9+qP+utMTEJxr3OSknXAeVlLa+xiaaVIwe4QYVm9ZHaIHD9Txq34KtzgXpsZ\r\nHN6gpZ70IFfZTaQnhcCVHchKNqKYe67ZVWE=\r\n=TbK5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.0_1650888481775_0.4291839895411973", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/source-map", "version": "28.0.2", "license": "MIT", "_id": "@jest/source-map@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "914546f4410b67b1d42c262a1da7e0406b52dc90", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.0.2.tgz", "fileCount": 6, "integrity": "sha512-Y9dxC8ZpN3kImkk0LkK5XCEneYMAXlZ8m5bflmSL5vrwyeUpJfentacCUg6fOb8NOpOO7hz2+l37MV77T6BFPw==", "signatures": [{"sig": "MEQCIEwvxdSRB54wujopFEYoiCFNg6VpT+AdiHC5LCEX3jMAAiB6GlI5ZdHktPNOktB8vwW6Cg+elKckGZd6aKUvcc742A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquBQ//fe6aPCMWqhZH7yp+dvvpxLQNjzuCxT6rxJi/u43sBXNpOXHl\r\nG9D0zjFVQUvWb1mm2+mN0mMoJnP7QMXnUqREoEb87tYTe9KC2icgxz4Ovqg1\r\nOUTwFEp8qaIcla63Zbvy26whwhb0ZqmlB8HeNZWpOC8+Cr77p9bhW31mYClP\r\nEJu6xQepdzGt91vJF+kpzI81O8CDocBgzftdUR+4Z2pKi9JXQqphJIwatTY5\r\n4YzULnLaDUtWzMEgq6p8g3bfuGTTwmNUvgzp4Rn7PVokLPeLIgs6LJMYWZax\r\n6DByzvSBNYpryywGGIRJAZcdGQuda1KQtRShAUwb3MP8O/MMcQUfQtbSp0xc\r\nFbx340rYnEC5KwvziXFwzLTiz914mrN1KvHKFTBBpzX3lij7UeS9Lu9b8nJd\r\n02YtLLrWojJOWXE+uPWuMxxIL9ctSlvYktzB/SRygzbvIL9/W40kXtBYjmMf\r\nr/+iPkVja6iI0FFpQrDVIq0x8SpBpVN9zgRyTdKPYI1RdtajOjTBxRbrn044\r\n+lihM9t8KpYMEVNTo8z+0nfEn6rM/gUwJg1aZbbx3ORGSNBc2FWdIsqUJU8W\r\nHYwgUlwmr1KvV44MdHcjeSu+d4e24KMTzlRgpsXf6RlhYYu7YNvV0jK7FYuY\r\nLasJ74s9ALV5LpgV6kOx9/gaUZVTRTEV9ks=\r\n=qTkj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.0.2_1651045440083_0.22803320271603966", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/source-map", "version": "28.1.2", "license": "MIT", "_id": "@jest/source-map@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7fe832b172b497d6663cdff6c13b0a920e139e24", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.1.2.tgz", "fileCount": 6, "integrity": "sha512-cV8Lx3BeStJb8ipPHnqVw/IM2VCMWO3crWZzYodSIkxXnRcXJipCdx1JCK0K5MsJJouZQTH73mzf4vgxRaH9ww==", "signatures": [{"sig": "MEUCIQChdh0l7o+12W5Pn6x1ju3i5ivRftrzSiP4XIS+sdwMCQIgJmTA7kMB66VmcUVDPCNvIxT/Z446mRJ5kESkLDn9OrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYWQ/8CpDgqXNsF30aj+IAlzGZNCQInZKyPoXyf2qGVO4yP4wHiJ0+\r\nu7i5hzhHVK1XQirU/cRm3pQTUoij7qgn838LsWOTYfcB3p0Ekkd0vXnsJ0cX\r\nQC5llKt/8kwvbTdshHsYWtvMxfbC1yBKLTYYKykjrqti30cZt/7gD2N9Co6+\r\nfvmxzg8Iy6+zaScfv80wvOZrCR/0NePUb7GfZg3T0fNz03bc<PERSON>ei+5I0g/Ojd\r\nk82av4taZEbiO9kNop3JsuCKLl+7bA5WOM+gwms8o7Oa/SqXWDopTtOWuUUl\r\nNPjc3iLnVXN6HXKn830xN7QLIKWmFmRjNrLMPgZSU/Am9uCNsmCuyRA9IKIH\r\n/fp7iDIV3suaNcXG2/Jcgooqsy0JWP/gVbNHYImD4AzQbSiSvUA7VDxGKIzR\r\n4yGK+J6hJjggVlKpcIPIe06k6Ge2vhq+7HXfRy/MOwin0ud114Qbp4HJsffL\r\nJs/5dnjDAhMFH/8oA5wlaGhnxZ7tjITIGTudYqyNQWcmfc56OYk3w8wdfy/I\r\noQHTy5Efj7aUPMwYq16r9S8YkahU+u/BbzmA+Hx4nOL2NAgPRssNPsB62JEi\r\njgpoyjWDgQi3x3gPOudACrwFy9k+343iflbYYrnBSgNlOgT7h/0ikj7Wa1JT\r\nBYolkNWVcU3w1BQybtuXhNJs8N1zyZLKtPQ=\r\n=lnlF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_28.1.2_1656498836893_0.30501308180575704", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/source-map", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "18b7d490554234339a0c5ea6301bc6d0a2f71293", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-ewwPj9IME1GevHIs5Zg3B+hy1RGK/YQdzs9jhRDV1OtW+3haVvWKltW7Uc//Ba8SPQzZGTshC1GkDTnHtSk2Xg==", "signatures": [{"sig": "MEUCIEIlXymdp0UbFXFdWJ3zg8sZrO84r4mOKaC2nMhL4tKeAiEAktb1nUc9CjwQIA0i+Uw4OYilm0W4he06ElEEWf4mUoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5000, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzJg//bo1sQTmrgTfw6sDYm6WnJ7ODrqIiFmsFzAalHwaTVd7HaA23\r\nfS+6IrL/BhzE/hqZIA3jp3J4zB8JufDEq9TYk4W27hTadYGOZbuYm2bfVxgr\r\n2mVko9VnQ20qzA1uPOydYhBFt47HWH72IUkfAti2S3aPn0NbOyictWag9Yr9\r\nVQ+7XqMlfvdSkNRuh8umbxNSLl7VqW7DggVItvptsYFwTHNp4rrVOt3ycMFa\r\nFx1d79HYDJ5v663NXfKu4gP6k8hNlvJgtkuNi3J7CctIloy8RryTcV6Md+/2\r\njF/DUUA92gZVrnNMdpQ+F3SADee/W9rRkFZZ/HxhUBdt21MZFFxrP6Z9PcNC\r\ncKGcIZZ+lJ1Qy+hkx9ro18fbGoRr2HTB0cp5U6HYexbpKz2GEOdagx88MgA1\r\nNZIrIkOrg3urFlXETF3XYEdkVkBGqHNhh0bLKn/wxECL9Q7CB4Rcl7L9if1T\r\nr0GpDYUcvjDs4EHVbT/4bYKEh+SYUD27SEVP9vrKctQlTA5bB+BNZIrQ+RWs\r\nGHY9JYukgRg9vmkQN2vF4JSVjUky5Y/O1veg5QQ21FRBcm77PezZXM5fz3dP\r\nL8sfp5QFOXA/6PpDEd3KOO2Z+hOhwK0jc3J8HkRyDkE5lVJMfVH5XI0Ob5LC\r\n55Nu4OfE5nnwRDFbeQWEiGvBqYBfNJdoz/o=\r\n=F2nT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.0_1658095625715_0.595947775254273", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/source-map", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "095f8ba6b8ff2d08e02d753150b11bd52ec6aa86", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-Z6/rw4SwmcAt9TR5eU8k8i3UzW5RxbqXHKccsP+JB8GN2ZPFUd3ZIwq+hND29tGWFChJGaXcX0BtUXiQwfF8Rw==", "signatures": [{"sig": "MEYCIQCEdM6tcAgH99541jOMYVAd84b8jkhWHtYBz7LIsX5eFQIhAJC2fK7lRghGUDnv7dFkHq58AdhWukkKZdxc5B249I9D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpMA//bjNNdKuYR4rglGSOYN7BBOi8KAOJrVkpH+qFw7LvkOVcoGYw\r\nLUV1c0Q3SmY2uVbXxedC1z6QFugGMW1ZbbaavyD8nZmmqEzttdA28qn4kii7\r\nCFWdJVsaNeV/HfQIgK0HuZXs6Li48MK9hJ3dknZ6v5tO0tHCn5qi1VWV4lPO\r\nS20QcPjvVh/QBg7oRxerUZESYeaTG1/ikvHwscBEZ6VjERPXrhEqr07kgwLn\r\n03xYh5HDqZoYtBH60jNZGX87D9JueV+D+OHTJ0Dnc+Vr/defXBp1PI78Rrh4\r\nTfRbcVNFC+LcNnAtzc+1Z7U0KxejLRtiB7t0bYju+R6LMKI3Fb6osy4/WE1S\r\nYLEsINjsDGRi8tgih9Z/ZvFgkK8VYER3rFxkn25/uk0rCDY/NqL2X+77FQqp\r\nUQi2qcIQ4xpMjI45Zm7OqD0Sz/zijI6yaCOs7tqo0nRup6aUAubaepHz5JjL\r\nJ7a44uth/YkbXzdkpcX2ytJveAKd0pWiCIAK+CdEjr6eQPuyYATovqybSjTr\r\n0L/9YKGvmXVFscOcsQXtab+VANFC2F3UtU9lIMWcHVep6XyQmlRey9GyOvVH\r\n/+dYxDCTfMse1Oo8HUXPaFGVBFO2rmkKePtmrVnA/Cirkg3uXnEW03KzzNzG\r\n19MLrczA+iSLw+LQHmzSSD4FzqNt5zNhVBg=\r\n=hFXI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.1_1659601408325_0.041287430345031906", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "@jest/source-map", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "869172f12247026b2ef314920578f334ebddc3b0", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-C5Em1vHu77XnNoP8vS+Ra9Rb8Hy80/iEBF5H9RuU/lzjxFdV13gTFyxRd3/AMe8U4u4otNCKW30Vf0WCYX/F3w==", "signatures": [{"sig": "MEQCIHK9P0Nhds6vLpUuUxwhnsjiNlJ4asqhtpnl0DdRYhvvAiBFknbVQ/XmJfE/eUNuFA/pefUXlLwbM3DwxD+vAKT6nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrvtw/8CwLGgsNQ8Urix020YrpEwt0JK5NFtiKLlv12rxC77SKl+4XR\r\nXrxeXNu2Fqz3W3YREx1ITpSsEXV9rElIXkfgtZJrXT0hTAwvSctVjHbucEa0\r\nxegNcnS3l53uDoARg9lgdmTVwT6Xw2AYKLGL+JYUsRZzzJFFP7PyYowr0hSi\r\n1QenWd3+BWGxaAcChfFYrrDzy7S+q2O6wZCaKP/M0+ua3hbVzo3Y0e3ZmJ82\r\nC6dkh+MdhsOgvLzopJ7Rq8vyAQl5QSckQwvJ1W1V99M18Su0vZG/NsrCouMP\r\nMW8H9kU92yY9YShkr8GqGn867044ACFUftixguH36URcSjlLG1YwIdFrdmfR\r\n0iDGnMSZMAbw3it0D1G9gyybJ1Xl9dq4Qv4+qtNlY8fk6N7WEz7j0jQbxzvZ\r\nBDknfP7dtuBiizqrh0H4k6HulWCuZ5EXtp9GBghnu/A/QjJvVFbshpFBo47w\r\nmZ490bNzt+VJSaGhIVjtX3w9CcxGC70jB86UluX3sIGo4/L0mE1X+YutA5Jx\r\nEbdgJoY7LuMoc/+oBse5xuVDCGwb6apMfBJBY9PJLP+49Yhh5IwdfMg+62yP\r\nEQHLlKtrpLEVJjVFZuxi8Tuixsag8EguTOFlHdIIhEVdGLb9bKaaJQe2YrI0\r\njNce/vO0etAZQAVii3uKRrmOPoyIAuAWws8=\r\n=eZNr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.2_1659742345140_0.615928967924563", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/source-map", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "54cba00f644921da609594b1791489613572ab45", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-WDLmSNLm6haMN4Aw5r17O4VJNf7UP+qmICyGXjd6ZGr4ITWQ6q/JMQU/a/yoUkvUpK9PLhrmYt+4YtnvP1JXiQ==", "signatures": [{"sig": "MEYCIQDpyUo4eREc0qlPdLm3j0+4I2RSIEOkQi/Z+PvUXSDHlQIhAKOlr+yNBNDz+dzU+IY4ExluZMHI1yTr+LfiDbx4aHOt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHYw/9GV++fePS8kA7KRFbdSmWHlKwZd3Sx/S+k40dtVjXqp/+LoXR\r\nW/n6KQXxcp7SvhiL5HQHNqI6irCRDUb8dzKczg3e0An0CTR+6nq0qD/g14hy\r\nLEkYF2hDvknBYPi2aG4b0nM1XbUbnmhIS2WtbM9WMmb0LomwkRyGme93VTrF\r\nf1jtHEuWAdiLwFWDYbuGw6p6OjNlxvgUYMS8kuZgM5abiA79W0o7NaMErFY5\r\ndNWEy+sU9LZRH1At1mGYgc8zY2anJ6b2zsZ4zQbntY9NkM0/VUi1q293YLaI\r\nV2EkDY2akPzm8mcCJbuQHuM7rRH7ddNLpJgHcBzWnYQRcynFsgzLEnY8IPba\r\nYbuf/JlnXb5t+O6AWI6kjbhD3txeRJjbpl9BbwpiX8Hkhm+/8/4l2SulidtC\r\nXnbhlEmu/cViiHmA+xW1GvoythmGCmHBn9T0oRvBaL4cxZh/o1zNMrXD2ZgI\r\nI37QH4SaMTfqN3AFPk/71Y26M/9PgyJJrXF2Kr8Hygu+S2OLjTYp3mOyzIIn\r\n1PsniALjS9dSSC2KFe9EDYOM7VIDZ5fCFmFha9jE3c9+5gYuenoJmctp3R9/\r\nL8RYv1/UpiB9jdtrAXjDdDQigriOfgP6GzhJbYuVNpsO6FyeW9lpyzTJvbcf\r\npgQEX993tzjEwrMmLFTj4TiuaF+9sR4LVM8=\r\n=zHaR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.3_1659879691821_0.8174353421046818", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "@jest/source-map", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b257b26bd90e2d04d01ea9238be23f818faf567", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-CkdJt84AWgTmBYChib/H4FeXYRxi5YDyhRJzsE3Dj13GL84jKSqftBgzmS32fZv6HsLTle/BgCS1J+xRdrVtKw==", "signatures": [{"sig": "MEQCIHcswn9qbYcG7GWUXImbqUEP1bpYuUWs6gnRd/Ou0iN8AiBXczbh54eV7iVaiSOmbcR9fglju12KgiwZwczCtqMJSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8ig/5AeYnS3yfcL1pOgAjCaPzhR+tyRGkLl2QUJnZbGXPYRCifnb4\r\n7mZOXbyTsJITGVUrk7l4z0dTMVC3Rp3idlzjc111OApwHQcM7r3hT1Yp4Egn\r\nMs0uAHLzQ8y2BDEFwrgn+Ir6nsUsCtLBMDhT2sV7g2Y+/L0KVkWCRGuRjNLO\r\nELhxFUHicZ8pqf5w0HDo3ebm+kpuXvNFZu0hHbcfOmPAFfOjJo77ef59S6y7\r\naKbmk/uttVsX5JVRO723EoYrX8NFRwZU+za2mZu2O7zrbzCzIrJAqOD49Mrk\r\nOtRZ0yT7uR+jr7JTGf4VojEVTb1tMAoRQzH6iHb+p3NUMtiNBosv+l9dyL2S\r\n07TpbqiZ2Pmh7qUHiJ458Mav2uHKMKtVFEE20i3imGG0Rt9WwRN744bXkNUK\r\nLGmQFoOhPv8zhZWB7iRJ1qbJJTHvNWlPehwdZy6rW34IQjfP6Igcn3o2kqkC\r\nbLwzB3+9xF8j0fDivwkvUAeeIPPBPvIivh0VBu8DAdJZ6SuhuxZah43Wzixa\r\nuYjjUCc1HUgSb0uhf+UtzlEAupYqyCp8ZjtTeSx7tP07FxsSCsrDsu0+IHLf\r\nkyP6LbM54DP33KOH4KxzfsSKI7tj4KOIlt0azPVTXPeMyNuaWx8cww6lPEf8\r\nQsdPPsh49n6BdcecFGzZ6Bh6+UCbDIFpuc4=\r\n=1Z46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.5_1660225257093_0.2028711058973609", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/source-map", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/source-map@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "172a51575760d60cbbca7e33d5ec3a146bddb6de", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-yDBcu4BrnrAiLMWXMCeY4SylG+mdUHa9J0tqeRCQ7123juCTlzOp50C1IKP8BQmyhFI7suh3HJuymClydjwIpA==", "signatures": [{"sig": "MEQCIDwXkFb74wyT9xzSrHKHziTS6vDHFifZNZtbWcK5lf22AiAnK4Nc4z730DN79qmKnFoDiC5tR2yK7G//QsLNt3Pk3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopoA/+ItSE63OFYneGDEDdsemayT81haeqgID/Hr7xvbicN/Eow9pR\r\nzzZ6cT6cGTnpTqNP0cdaj/UjnVr4Og9mZvNskFS4TbdQLL7g0SNEWkDIzNDl\r\nZp0ZUpkqRPOohWgGGunO5Jyb8rnEhnwDoF8ntm7W14Aaa5ZAlZ2Csd3CT2uO\r\nVntenCyoqLQsdJ4CN1xuJYP3xr4TbZLvP0Lg45PSptI62qddVni+lkFPLplA\r\nN+9DHF0AyAzwUL2UWjo9U6437lpVVJUHvwmkztetRqHU3gM7laIaeu49vPRZ\r\n1KMCcJgdBlTeqDhw2dkZN/53drC986Gab1aX6YOhOOGKt97Pw5Cc+UwyBuFI\r\n2lH1F/AHLOP4nEf6U4X41EowtrTJEm/Mjo4UtGkAPKrO2I9ONR1G0+lmEbD6\r\nf86rYGqe373tBJy3vOlssN5AMLHxTZyFPl/Sh3FCv0hF9j8qLJVkH7IjAGx8\r\nHJjvVbrCecLlg6DfpiXVxrMvYwhbcEMkuWvsbjOpHLNBG7/+27uaeoe2Pz95\r\nkkxqCLDyVhNN1YYr1QdiKJHs4j3wYRHsfROLvf30aHSEIuoZjPiJ3XDlK8SH\r\nKvxFvr27s9/Q/WQiSlzaCrrj2JFPSBZLyzmyfdZEEu8xckKEshla0+oGisX/\r\nQlz593Y2K9tvDu6BvRERO6nFHkijPMeKFu0=\r\n=Vf83\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0-alpha.6_1660917462545_0.15698797882851978", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/source-map", "version": "29.0.0", "license": "MIT", "_id": "@jest/source-map@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8d1518298089f8ae624e442bbb6eb870ee7783c", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.0.0.tgz", "fileCount": 8, "integrity": "sha512-nOr+0EM8GiHf34mq2GcJyz/gYFyLQ2INDhAylrZJ9mMWoW21mLBfZa0BUVPPMxVYrLjeiRe2Z7kWXOGnS0TFhQ==", "signatures": [{"sig": "MEYCIQDlaUkYj2lyy6SNU876nGhDvqOpMV1vJqkh7jc7ohgncwIhAIaBLNeGyDVY30T4MmSm+agli6g7C3CG1NTImn9vd+B0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqNg//ZXmbjdrezfHV2WVWnlUguMmZBKQC8dy832QlHyWjuoWlQnkS\r\nDbY59i4STbGjNBtlqTzGauE/jHji5edaQk4/YROh+VuZxU5fLTYNEN9KQOz8\r\n2qEruOSig3bO2RG6icqs0i5ArqXjO2//g8RIokEgBg5yrnMChJjwfY997fpu\r\n4KSKyd9aw42C/vKIev+sgcvHbuUban4pEAB3vH+jSASrM1e3UCCDuLnXkI3E\r\nG3+Ez+MHXMDjrnZeTt0H+fN8RBL9v6xUDPOiM+dYuKm8yj2gTCTmvA3ppWoF\r\nmSceQvgiNy06fdQiS3M0glBx5EaVtFMEv5XyYbXHJPxIxXKydbP4/OuOIEEY\r\nhxZtNCeBngLGwrUgkE+x/leWGzpri7vQAcZzNZQvScwiZAyegRRegKTFeInN\r\nC7AP7ua6ssJ6ylHLmMpP7iedXsSN0HMO323aFnteiiB58hS6Dw/GbDbbISoq\r\nrPSjhFS1AW4lWuwrx0WgmKhY5bStG1MQwpK2vpegOIJLL4tmOjdZhQbc5r9s\r\noaj4SusQ0PdcQzQTo+glfx2ozJJ4eRxvnBjVDa1i32m0Kx6hJpnzXcrUuGyQ\r\nLPoiDiAWPaxUOMQ6TUTNdVp2YbnaubOGWtAPzH90XC7BsnIBkrEdfas/izaq\r\nDzodtizo87Xl+m1/rRugcXmY2NDCg3Q4Vmo=\r\n=KppQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.0.0_1661430804506_0.9528563417828744", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/source-map", "version": "29.2.0", "license": "MIT", "_id": "@jest/source-map@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab3420c46d42508dcc3dc1c6deee0b613c235744", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.2.0.tgz", "fileCount": 6, "integrity": "sha512-1NX9/7zzI0nqa6+kgpSdKPK+WU1p+SJk3TloWZf5MzPbxri9UEeXX5bWZAPCzbQcyuAzubcdUHA7hcNznmRqWQ==", "signatures": [{"sig": "MEYCIQCEv9g0W30VHKvPEjnO4d1cL0UDiyckXDgkb7Q8NArKVgIhAMX0tbBaB9WCZw7hJ8ufKQcT+KdKndAEnPsYrgsVriXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp70RAAi7kJ7JP0Niw30jg6eM7uZN+hru6G3BZ2cfR/IZi/61dv52VA\r\nbkyaSOXfBd0vC7A603H5kSDIQzOai/9YqPLhdmLYAqXxE9nuy4QwKYNzNFli\r\nWpxOeVasbCnR9yUK4upBZ++Z4KEKwv/NgDofVNJWp/34868TWC2W7fhcRKcw\r\nRbYCrYVNMnc2QxJ+aXC4TJZbyQ0g3C9FQ3ghMk6FR+qcPZCTQkLaraAWShE1\r\nGHK2/yI6YU4wzRX8rGZvD4D5Rdhbp37iEvQwmlfpwf3o6vpE1nim6kp0iNjL\r\n8OHGZ0K+Rvq4eZ3xjNhF0saT3cxXzwPpwIzJgqOrWHpd8TCw+ZEI1v0oMN8z\r\nGaeLXwE+AJaa5LFV1Vc4lfyF48AhW27TirgV6qrnYytXFG+MlF7u90DD8lTt\r\njb8D6F5naF8AM1cc1NrYfmjZQrR9R0IOL0OwlK+D6g+JG3/VqutZdIovu6iq\r\n8eE02XVYY6wy5Db0tNAbBB9cRcVL1PDMQ+muYtlMCNo2iGouDJQZARMNWcCf\r\nsVlcyyaQzf4WtIlG9pie3TqHoKSzsctqzvMXxG8cHnpvvI88TGYU1qfeYBj1\r\ny05XsqMDJUKFo4QHTGW++HEIgs/9YJ80kxtvUwSefshbxM28IqM2AiFgGL9Z\r\nz7a5wwZD48eTpr7vJ7dSejvxuWJZ4oNEGnM=\r\n=LOrd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.2.0_1665738821221_0.586580521050174", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/source-map", "version": "29.4.2", "license": "MIT", "_id": "@jest/source-map@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9815d59e25cd3d6828e41489cd239271018d153", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.4.2.tgz", "fileCount": 6, "integrity": "sha512-tIoqV5ZNgYI9XCKXMqbYe5JbumcvyTgNN+V5QW4My033lanijvCD0D4PI9tBw4pRTqWOc00/7X3KVvUh+qnF4Q==", "signatures": [{"sig": "MEYCIQCpE51BRFN56amz8c6hm8GnQM4GVk6r/WAWQt6DCT27KAIhAOeopj04vgyc3AXoh17LRFDzPSdTij/CZVPI9OlrHzhy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHqhAAk1BQgMBR/VrUHlVTASKfbCy44Gk9hSO0YbYOcN9F/DCk4V4L\r\nf2Y5QpXkhC6yyvOZDvpmOwPJxNxbOPZ0B19xRG7KxtwCfwLF5qKLMb5qY94x\r\nAoyEW0XRmOiBsxLxT7pkBXibDaDm/XpT5uogQNvKHBa26iN7G8xNo4fFoL1G\r\nA2aBldCK87Ff/xYgpt2Mj62E+lMd+jDKI/eArsKGHFWhyU9c2dKIPtb+keYC\r\ntbYyMqplUVPsayarvfrU29IVn4UmGPjSGdGTnhoGflIIstCg/tHqzz9fD36V\r\nyaWQuvpMzieZJvh3g3w/PX+oEeDX+EeP0wXBeqvtwf4HtdNOn246bK3FiKol\r\nwimkvWO3KEOaixH0XbiH7oKh1a32UjTeEceXJK5cA5isoQ1jvyl6lFMYfX9o\r\nAtU3YOYnzq4Szx0c1/Vfb1doLfATVg75fuRQUDXCSnMFTD+jKVkq0UF/xi3F\r\nSERptOpdRHM8pPNrfDi5CRR0N+KEnEGgMjFVmsTWzBWOOK5rPmwVHTL0ogdO\r\n19gNWOekLBGLBbJogeObO38r5mVDcEaOVMJOsPbcPibu5pu5gnB+VcQZR6wj\r\n+iIJzs8DQ3d4QHupicWqvOaGqc4/hVLg8yU2eoQQdCkZp5B6hqg9qIAGdJ+a\r\nzggLGFPR6eT6KenTisICDs2LAdyBo7xNlYI=\r\n=YE4G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.4.2_1675777521656_0.5020141531986402", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/source-map", "version": "29.4.3", "license": "MIT", "_id": "@jest/source-map@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff8d05cbfff875d4a791ab679b4333df47951d20", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.4.3.tgz", "fileCount": 6, "integrity": "sha512-qyt/mb6rLyd9j1jUts4EQncvS6Yy3PM9HghnNv86QBlV+zdL2inCdK1tuVlL+J+lpiw2BI67qXOrX3UurBqQ1w==", "signatures": [{"sig": "MEUCIQCkAMUV22FeYF1v1uz5pLYaWdIg0TvjEFcmvLY3kLgitQIgPBwX217XY4I7LNsVbDX4rqvxkae4TSVYhU9a81va6ps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwphAAjiLa5ZqNIFPJrAZvtBU/P1xtD4wBjHI9+1daCqVpkpOVImW+\r\nkmC/sn1in9QEEvWGvXTOOWPcOoxfSRUnVVax2HYL56loD4RzMEiEw57Bf5YG\r\nSSoI9ESKcTBJqxezM8eWVf4Thk2GWV35jvguW1cMAC/A9n6adWM1Hv9ZuKPa\r\nPz4I9x9dWQPgQj6qc0j/6pQ078PHERTx+oq00f4RTmTOcGPYeEwAG0j6KQOU\r\nNX0+IsHCZNr+ISSGNmD7K+Xnb92e7C2PGQldF/dFD+9+dEOSGYk/cpqmaa++\r\nLZ/D1nB0dZ13fDdNjnlHks+/syFIAr9w8TZHaIokIw421PaDroCT3cZt8hAE\r\nrRou5PazxROkDxP+lZzJaSGQ1RSwGPkY8F+fyD2JyLG9z2MtsIt6xZZVwDTK\r\n9WafHq862etal2dgNzDL1l8RKhZBu20PrWDMulsTZwEZVwrqBOgpQY70lyDF\r\nL1lTsrBul2DcmpO/4PwojdgunXMWeiZQ+1sPFdjy4mwAfyqWIXkJxzYB5UMl\r\nURFfq2iIzjM7TV+Ctg22B6kqR8eDq8EZxzdU9PXCHu7NrOSpJS7HBG6igfrG\r\nqPrAYsrOqdPUSu6Chl5EHYC2ry20/fPV6Fml/xEeicJ8sfcHE1I5e7GIw0AT\r\nbyxNl+mpFqCSEddTWTjEm5znip1Zk14kwro=\r\n=AwRq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.4.3_1676462236437_0.7638013846262302", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/source-map", "version": "29.6.0", "license": "MIT", "_id": "@jest/source-map@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd34a05b5737cb1a99d43e1957020ac8e5b9ddb1", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.0.tgz", "fileCount": 6, "integrity": "sha512-oA+I2SHHQGxDCZpbrsCQSoMLb3Bz547JnM+jUr9qEbuw0vQlWZfpPS7CO9J7XiwKicEz9OFn/IYoLkkiUD7bzA==", "signatures": [{"sig": "MEUCIFD363QC8VnpBThRXn7jSwO5mC6H3p+xl+CjsF4DxCB5AiEA+w/vZNOu6JxRW+29TQze/5O775UPYY5Jp60kzOqZfoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5068}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.6.0_1688484340383_0.5183880175434006", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/source-map", "version": "29.6.3", "license": "MIT", "_id": "@jest/source-map@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d90ba772095cf37a34a5eb9413f1b562a08554c4", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz", "fileCount": 6, "integrity": "sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==", "signatures": [{"sig": "MEUCIALqch0gVAV3bBFgwnh8DdLkgyRN/uaF00EjGoQMbXwaAiEAznmgvZ3csleerg6F9uARVcvigy53xJ2DYHA6O98kULA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5066}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_29.6.3_1692621535778_0.25486001449113993", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "@jest/source-map", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "2a676a016f4f3bf0f35cd4dc8a8481c6d47624a9", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-koT3ZFvI4sqYEwZKXrP3ERBKNpkKGLMF6+DiVlhQi9bKh8BABWTmBbyeGjKALtDAu+GGeejn+hf+kX8oKcOmXg==", "signatures": [{"sig": "MEUCIDdkic+ftf3sdCvlZM653Fy3PCawArX8HZvJgUESSttTAiEAo0FwWhXTMr9CVX11Zv+DEp1cbp0/0oVGY/1+86ZbVQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6887}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.0_1698671619527_0.12456549919119642", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/source-map", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f54992626b5696ba7cf0f0e83e8dd06242e3de26", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-q/5Vl7Ce1ukYUiDwtY1OWsazkHjHBGPcDoaxkJR20yMdo9s0phdmM1e1ZQhMpZOr8Xhb0Oad7SxmyJcrcNi6Gg==", "signatures": [{"sig": "MEUCIQCdL0ephu+QJGd8dOhMorw6o7PB/qHaOcRNmAg/JH20+gIgBpW1U1C83n7Rov6rWmejidU7BNV9zRB0txZjhbYM3OQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6887}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.1_1698672765999_0.9407141030291761", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/source-map", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c31d5d629a06690250beb6e9817ec1b520d6e14c", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-ne+xzSDUFYq1ZGCu80J3rMsCbXuFMCAGDdOcggDGZ8Gyyp1Vb5PrVyJ489062zWPJ6DIkMtLN7JMKmBJCmThOg==", "signatures": [{"sig": "MEYCIQDrf5oxplSL0ovUy7WcOAeeJShyWmJu7yBIv+g8SmQcoAIhAKEODpFKq4qncjZKTfEGeMJgXJHMOZW2fNZen4Maeadw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6888}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.2_1700126894374_0.362406324541116", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/source-map", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "aa8933cbc78fb8c70310ef476de3a3cdbb618bec", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-OBF2rx9pkcbhoUfeXI7GPB6Gd+Olajj/VMGv9w0VTtAJ/KL6s1kmFgefYi6bzGOlB2Z/UWNR65oY/JWAEc69fQ==", "signatures": [{"sig": "MEUCIQC5N7sijd2Sz4b93cGVEQecBuygIKNsVK5+83Hs8jZwHAIgb8imq5kWEy3UIcIqLRTESxR4ku8VnBbQZNl/MbR0huw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6888}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.3_1708427327868_0.1862528964554817", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/source-map", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c2da2b092d8b66726aa49219ee19a00bf48ae9be", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Fpn783ZD8VQVTZg1s+tB0vB6/x7i4/Y2Es1HySA4hv1hIDN/MLrY1feIAViyBk4P1BTSdOsIXr1HKFC6P4VLog==", "signatures": [{"sig": "MEUCICDMkqaQh2xKzWuzM67zyXGq6TCIPdeF6jQ5EWINiB24AiEAgc7K/OD3lQszbUluFuqzuIwIgbhpCoC3/CaDANiHRPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6932}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.4_1715550194280_0.6362520092189503", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/source-map", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "cd7b8ae1d4dc09f8244eaab2645254a75b0e6ba8", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-Um5aULXncGNoj3FTNobPoV0Cj8glvEczqSN3AkyvLgprEnjGNWyQi9NPPPcGWBwHmaJBWCUP8+xeTbGalAB9Og==", "signatures": [{"sig": "MEYCIQC6ar56b0IZtGErd56AMmLzye/xHQODjR3xVbAUrAoVgwIhAKju2fNgy4cqlF+fhTnZNau0xT9MCS+oJ5ox2G3CDQ/P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6932}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.5_1717073032852_0.951385758458021", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/source-map", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "07909ae9721f4ffc5ae142c014c0ac910e7358af", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-7rSrxehVyzqw5O+F2ds7wLAm9f6QxqYsJU42LNyUpaFlJqtWz3PeQ2Wu3DVoPzGu0C66EhDHKYmeN0mXnRDZmQ==", "signatures": [{"sig": "MEUCIQC15tsfb3GOw1CZHx2ZXH5yU5W/PFV+3tXRbsengBozWAIgG2jytMzskkr0D1JqUx4bu3oM8Q8YsyUg6NyCsGoYpKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6908}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.6_1723102975834_0.5996607730753039", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/source-map", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/source-map@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a50ded4df349757241373c5308f863a17dc28172", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-zWk0ZjV2tOBhERTJagxjrRfCxb/X5SecekXJIm0U9vWNuKNKDmK1TQJcQP0SdtqM2aa5bNdjub3lg29l7E8EIw==", "signatures": [{"sig": "MEQCIH7mkTeFBXufNAdNgNciOVwgq3vSukGHU4aJRWsDUinQAiAL6PCGN/X/FvUu2/wCpSqlZWWUL/XPT6FqHDlNTbzagA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6909}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-alpha.7_1738225702624_0.8819582387705724", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.1": {"name": "@jest/source-map", "version": "30.0.0-beta.1", "license": "MIT", "_id": "@jest/source-map@30.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "8a22f94a44ac9de18ff473f1032480f07924a7bf", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-fFJ2N+3tDZxtgK+8YwJ9R5iNDhdjdTjWd2/yPYWWFNPlrh4poPsr5Bokx0XVo1JCrT9rBE9nQLWyTe429KtbjA==", "signatures": [{"sig": "MEUCIQCRRmcQAhzV/mjRizv/79nHfAvsMFw0iA6RIsFqIpH/XwIgbHt/+Tu6/h6BYJ3SDcPc2EOlkvQnC+tM6J8nCGtQMIY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6874}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ca9c8835e3c74ec17450cac43c7cd3e8bb5747b2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-beta.1_1748306894966_0.7038183030827072", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/source-map", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/source-map@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "99fbf531cd528d86891075ea938e22cde27d29fb", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-+GtRb90owt9sWHSR4vsjBS1ImpyfTYgOHNnz/0VV/dyRSdM12AKjPYuo8l5xdDdXaWJkVfaoUTYWJJj4Ch174g==", "signatures": [{"sig": "MEUCIF1SH8i7V5Pvd3DGuWkgAiZV+5XDfgg3Yvy5PDBGF8JHAiEAhHOCW7xHDW8ybAf+6Z6bCtOGbaJzRlsLzOf5zcl+6FA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6874}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-beta.3_1748309255016_0.08651613939177771", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/source-map", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/source-map@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ff94529111cdeaf24e09f83fa341b07f44d3f174", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-bER1adKBfIF6V5cZq21DzZBYIQ0h8nwhdj2GozWvJ9ZJVLSlQ1WDsl+s8aVHUF8Jg7/uoo/CP5MG0LoP0q4ZvA==", "signatures": [{"sig": "MEUCIEA87kkHEjSCmm148w16BBPZ6by7m0fjKs4HDhUj7Ct7AiEA9HKoCXN6dPyi6VsBu3mgJaV0nWLPv26VWVB6/2uH3GM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6885}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "@jridgewell/trace-mapping": "^0.3.18"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-beta.6_1748994636648_0.8185843667217871", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/source-map", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/source-map@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a7fe1fc770620be17ae99146a1fda425b4017815", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-7W<PERSON>Z<PERSON>wuPF0JXsHX5Sn7R3A28bEykIEbbl7otboPVt0lJKTJEDNjlzNA8zB3GwGKk+1HfSvJcwSAYYEe7KrsKEg==", "signatures": [{"sig": "MEYCIQDJ7kjLgu1kk4J17asdFlMx7VLBh/ZZ4eWuw/BR6+VlAwIhAO70lhBwQ/O0N4kFm5CoMIP9RYRd9AX+Xc0OE7wP8NYu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6886}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"callsites": "^3.1.0", "graceful-fs": "^4.2.11", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-beta.7_1749008134303_0.06959339535246767", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/source-map", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/source-map@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ed70996a10f9335abe2a49e169328495ad1a6ae5", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-jx548XWpodWM6BSvcsZuK0XQu/H/1xL0D8ZwR9Prwt2SVa20R07hgY4DTZJ+OarzmU7cs/l9Ik7EJRG5sP1vxg==", "signatures": [{"sig": "MEUCIGF9NJMkJTSf/M8v6rll41Zyx2KMX1NsO+s2nf/5lrIeAiEA7LQi2+iaQGaTNZG32lx5X5SkOlWl72Bwq3+8qc7HuQg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6886}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"callsites": "^3.1.0", "graceful-fs": "^4.2.11", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-beta.8_1749023581385_0.6178340350990694", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/source-map", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/source-map@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "f9707d9be6c34f1283edd504b774f09ce5821304", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-vjfRDnPVjmbKMldM5H3nT7Gb9BTJU7+Z4X0lCauKIsIK6nzjk4QAbziqEpUzbQ2whMXmn3SMy+M0yfBowypkkA==", "signatures": [{"sig": "MEYCIQCBzI8qCyZTMbtQqf4eyGyonYEcaMGi4NE9W4kTIPcERQIhAMegaW0HcUN+aOQNmWrkCMjcvEQ7O94WEUrqPJAlfqJe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6878}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"callsites": "^3.1.0", "graceful-fs": "^4.2.11", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0-rc.1_1749430958510_0.8823170249775178", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/source-map", "version": "30.0.0", "license": "MIT", "_id": "@jest/source-map@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "f1318656f6ca2cab188c5860d8d7ccb2f9a0396c", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-oYBJ4d/NF4ZY3/7iq1VaeoERHRvlwKtrGClgescaXMIa1mmb+vfJd0xMgbW9yrI80IUA7qGbxpBWxlITrHkWoA==", "signatures": [{"sig": "MEYCIQDTWyZieALuIuo2Xh4D7pBJg3c0Vfc6rrkn9SeSbuXcIwIhAO7A48OmNad5RMAl7HJHcE0AiAiRxBGP3FV4T7j0Thsn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6873}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-source-map"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"callsites": "^3.1.0", "graceful-fs": "^4.2.11", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_30.0.0_1749521739728_0.21252365687370878", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/source-map", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-source-map"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "callsites": "^3.1.0", "graceful-fs": "^4.2.11"}, "devDependencies": {"@types/graceful-fs": "^4.1.9"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/source-map@30.0.1", "dist": {"integrity": "sha512-MIRWMUUR3sdbP36oyNyhbThLHyJ2eEDClPCiHVbrYAe5g3CHRArIVpBw7cdSB5fr+ofSfIb2Tnsw8iEHL0PYQg==", "shasum": "305ebec50468f13e658b3d5c26f85107a5620aaa", "tarball": "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.1.tgz", "fileCount": 5, "unpackedSize": 6873, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCcV+exmgUa8xBHiuH18ecFgoqZkYmUdgyXoLbPItDP3QIhALIyzT6siTkpaOzk56SYq1oR1bHaoHAYMVkfZFgoULau"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/source-map_30.0.1_1750285877130_0.3751820850110219"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T14:46:21.517Z", "modified": "2025-06-18T22:31:17.609Z", "24.2.0-alpha.0": "2019-03-05T14:46:22.035Z", "24.3.0": "2019-03-07T12:59:18.704Z", "24.9.0": "2019-08-16T05:55:46.549Z", "25.0.0": "2019-08-22T03:23:44.451Z", "25.1.0": "2020-01-22T00:59:44.806Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.151Z", "25.2.0": "2020-03-25T17:57:54.437Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.607Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.412Z", "25.2.1": "2020-03-26T09:01:04.458Z", "25.2.6": "2020-04-02T10:29:08.065Z", "25.5.0": "2020-04-28T19:45:12.822Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.871Z", "26.0.0-alpha.1": "2020-05-03T18:47:55.716Z", "26.0.0": "2020-05-04T17:52:56.905Z", "26.1.0": "2020-06-23T15:15:03.304Z", "26.3.0": "2020-08-10T11:31:41.804Z", "26.5.0": "2020-10-05T09:28:06.146Z", "26.6.2": "2020-11-02T12:51:13.084Z", "27.0.0-next.0": "2020-12-05T17:25:07.396Z", "27.0.0-next.3": "2021-02-18T22:09:42.233Z", "27.0.1": "2021-05-25T10:06:23.781Z", "27.0.6": "2021-06-28T17:05:31.689Z", "27.4.0": "2021-11-29T13:36:54.728Z", "27.5.0": "2022-02-05T09:59:17.810Z", "27.5.1": "2022-02-08T10:52:12.039Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.441Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.124Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.697Z", "28.0.0-alpha.9": "2022-04-19T10:59:13.838Z", "28.0.0-alpha.10": "2022-04-20T07:37:31.356Z", "28.0.0-alpha.11": "2022-04-20T13:30:57.937Z", "28.0.0": "2022-04-25T12:08:01.944Z", "28.0.2": "2022-04-27T07:44:00.242Z", "28.1.2": "2022-06-29T10:33:57.122Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.890Z", "29.0.0-alpha.1": "2022-08-04T08:23:28.558Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.395Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.009Z", "29.0.0-alpha.5": "2022-08-11T13:40:57.252Z", "29.0.0-alpha.6": "2022-08-19T13:57:42.738Z", "29.0.0": "2022-08-25T12:33:24.624Z", "29.2.0": "2022-10-14T09:13:41.370Z", "29.4.2": "2023-02-07T13:45:21.866Z", "29.4.3": "2023-02-15T11:57:16.636Z", "29.6.0": "2023-07-04T15:25:40.530Z", "29.6.3": "2023-08-21T12:38:56.009Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.738Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.217Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.533Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.046Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.445Z", "30.0.0-alpha.5": "2024-05-30T12:43:52.996Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.047Z", "30.0.0-alpha.7": "2025-01-30T08:28:22.799Z", "30.0.0-beta.1": "2025-05-27T00:48:15.144Z", "30.0.0-beta.3": "2025-05-27T01:27:35.317Z", "30.0.0-beta.6": "2025-06-03T23:50:36.830Z", "30.0.0-beta.7": "2025-06-04T03:35:34.536Z", "30.0.0-beta.8": "2025-06-04T07:53:01.555Z", "30.0.0-rc.1": "2025-06-09T01:02:38.716Z", "30.0.0": "2025-06-10T02:15:39.884Z", "30.0.1": "2025-06-18T22:31:17.307Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-source-map"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}