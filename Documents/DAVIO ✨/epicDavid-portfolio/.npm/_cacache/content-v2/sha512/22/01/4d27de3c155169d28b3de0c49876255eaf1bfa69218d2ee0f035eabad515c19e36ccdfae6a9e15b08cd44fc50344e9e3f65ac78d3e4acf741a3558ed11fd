{"_id": "twin.macro", "_rev": "77-cdeb7a4cb6798ced3b99bead9571b9b2", "name": "twin.macro", "dist-tags": {"latest": "3.4.1", "rc": "3.0.0-rc.5"}, "versions": {"1.0.0-alpha.1": {"name": "twin.macro", "version": "1.0.0-alpha.1", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^6.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "tailwindcss": "^1.2.0"}, "gitHead": "cb4f0a9d8629b920a7400c473f103816483f15f2", "_id": "twin.macro@1.0.0-alpha.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-nJcVL9kQClr4vkD698bMQ0UF5aKCk/nHHRsULqBP5vJYq4g7qkuxvjVKNdA3nPDEMhMEZhhYMMfpW2a8v00ANQ==", "shasum": "a671e4b7ce3df7ec7bf7a325f1885cf7cd23e70a", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.1.tgz", "fileCount": 5, "unpackedSize": 403168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTRclCRA9TVsSAnZWagAAXRIQAJ1xmz7f+C+9PhzHfpji\nH5SLPwBsliV6RIYANiKb5tgQtTDGkxHEfaDDPTwIDg/Hgq9adeA8IZF4OvYR\nnD0WSAeQ+d+19phz0nZ9LXlSKigf+hbrM1b+IxAj4ojBORLFGIYqgoU6VTF9\nBF7VINdxpdA2n9AZ8VfAFpmCzSGg6bvSrko0I0SnMo55SWVCzxjeeZ6zJF8U\nBpc544dU2VDY2ZsdEZUReeMDQz+xaNs4UwZNU1M0TMpi2dDaiNdOZ2+Oeqji\nbhMzX9ZqpDg4Df9oXP41uqymrdNK/eIPoymQhf1OcWQ+znRhsR5pxq79pPOJ\nX7+fU3rG3I3gCIIi0FaVe5bk8kPqs+ltAjgoOfDFxsUWF2EOaroOciSnjjZy\ngedEPPnCkVTmGZ9s66OM8ToB65qKTu9PixsfvSUdALxrwiCezu9eW+01SKYZ\ngkhrQacaHLPcP42PrdPpEigMtQn0blaw9+nq1aN5OtKYJkwPlf16BISMQSvn\nArIpC93kWNHx654I/ybLpgwO13CVFPQLrXD5BAp+/SlbpY6j/r8Mj5EJdN+g\npi/rHZ/dHKmRtnrt4ZIFVSUp1+lbPqNDmZTQNoMZUuTuT2pi9tchTk8Xyl0P\n3Cwg9madJQGxL34bQNJsa/DZ6pnL1Jt4ugzCeY8uqD1VbzfLlU7nTLTZiSWv\n+faG\r\n=fg7q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpyQc/TTRcReEoCrPTvRR7cgXKjK2JjiNXciA4G2EYBAiEA9vj+QFfHs56QPyPbI8kQiOQicNnDhrPJEolEUkratug="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.1_1582110500399_0.6224248930947689"}, "_hasShrinkwrap": false}, "1.0.0-alpha.2": {"name": "twin.macro", "version": "1.0.0-alpha.2", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^6.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "0adc9b1e7242b4157c7626b82b6cb8a8a809450b", "_id": "twin.macro@1.0.0-alpha.2", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-TXwwBhMlg9YkGhTxyeae8wH7BIRgj5f7HjTg8Js5DuGYdqrLebd6keG4SYkZlUAbDtbVSM4jnCphobkSkWwytQ==", "shasum": "20d18cf2fff428773649cf0fe2cc07a2101fb266", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.2.tgz", "fileCount": 5, "unpackedSize": 71122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUMsZCRA9TVsSAnZWagAA/+MP/11PgwoAv4qBHzvEiBU0\nb6cBepkoSMbhbjJ/1lZoQWVXyWSiH7F23ZXfbkD6GiwUwzpvzdCw/9htfdBi\nT1pAaOKAnOyiiWo7QiX9VHCRPuw9AV0tuuVZ/klQiAM3+fsU+Zp55PcNsY0J\nG3+8vuNIkMYbovyJnFR/T3J7qf4WK2mxNv+Iw3KGelJwoQepGulL6unwopPT\nb4+vafPBiZZ8Fsn9mMuoDwCCv630QdG5olafJw6I2kc8ceDItOJ6rC8M0Urm\n3RcOXWwgVwNmwvyRhbsuO1cVtaDvIgoHSt/C7kX9eY+gteZCUzc878XRNVHL\nN+Vm49wJk8tYhNjPqSB4g31xb3/sqAdtOdV5dz5cwa0df5bt4nWwhc8XA4va\n/6t0/PBLFgbpbE/2Xk1XyNO01QhI6JtCyR9kcAfVoTNgjlQMyiZZQMyB+Tdk\nkitz0XKMeDg0QLTqAwGfKdkKLJhxEq8JmvDQOzUOVSSGUNJbtUqiyBpOGXlz\nFObyKS1e25x1/49hmS1E+yrBhcpQMhGdZ+VhnPr6b3eNYn78axeX33rf9wxb\n30GpdF8cGRzpemzBBCOYsJ5wz0A3eAKByBkaBYOr88O8WSXpifmziLtrceGk\nKPUtNomuKLtu4rsTEIesT/PxPE4+X2TYOqbu6ciqRKpVKC5dtpGwYHSBPIOO\nS5hK\r\n=UHCe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB/P7L7ZTBHvxdslkztyXJDGXOBgzMxXoA2USPnMyxTXAiBOMLGeTmqPHAcYlTc89ZbvsvszP3zu+rWuvo0oK6XkLw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.2_1582353177203_0.9353310151754255"}, "_hasShrinkwrap": false}, "1.0.0-alpha.3": {"name": "twin.macro", "version": "1.0.0-alpha.3", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "dbb2bd588e96065e2d3ca4c54d45db01fb726b05", "_id": "twin.macro@1.0.0-alpha.3", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-EwhrIGgpFE/tzfq13idzVVRfLaxDdcxLd7Ssq2IcGJg5mHh+PE6Gkn46CX++7KioqyKb/Bclz5/mrFmhAcRagw==", "shasum": "7ed4bfd3e3e2361fc3b7d050cbfc40894a08cdde", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.3.tgz", "fileCount": 5, "unpackedSize": 70454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVDCuCRA9TVsSAnZWagAAjYkP/jOWKdVxCVK7a1NjlApq\n4RZ2ok7Z1ng3YkoI7ksi6rkaE0h9kujeVpxUxJat5ZxD1d68E5e+Hlr7MSA5\nF5DXK0GePvWmqOiZo4wyc3jiEbVbhEoyKbFmK76DsYPwDWks4b4/wkgwkgFt\nqFmc7yuMM0qHf+FCTtDLanVlqzeu1Bm0yRHPKZS7FQDKnzg31qeDf3zBhTo6\nsyhLykvguv9SvqiSrKlLH/ajVFoxuHp8DGMxB7e47q7J6NmBAHtpD4N22xr0\nSXX9QmOhOhjYEDDMlvU/4IIspQ/G+MpcLhjUotkLco5gWvyzUZvSamA2y+Mw\nbaLFv+gmiAW8+ShJKzyDmFgKsIYSGWaatiPBBqS7MfmcE+b711VgC+MVvROK\n3dBz+HIqDsMXHFHstz8hdaH5ZXKn5XWDCK1ZUBEOPaOvxBLsN/PWr+iRRla9\nVGsKr2zLK2Krm+eyPBGHMRE5zUSV5o6rbekoNJP4c7tvddrBSfnin/27puwy\nAXVydLxf253rpU+6lmSfldRUXnwmaqWCi2iRDZOuoSxCHNSbfwSqRJASxOM4\nBynZ70ZGhfg4wBUcFEDK/Hf8kflVdJm1jejqsDW8lu5AUY1J6Ao1rXfs/nIM\nrISGYhTQXFYo5yHirnISlxkcaCm3sr1dWNtwQ69p0aVze2NsvOBnioJnKGWZ\nA8Fw\r\n=OmUo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAeSDpYsMzJU5W8o2d/Y2FwYGbL6VE16Y3v0+Kem2NPDAiA1ZDC5szSygphEfDBgiWU9a7SoxCjfwInS1FYm8TQLog=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.3_1582575789830_0.9722787146267275"}, "_hasShrinkwrap": false}, "1.0.0-alpha.4": {"name": "twin.macro", "version": "1.0.0-alpha.4", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "84f994f6cb7659aaf56745a002ac35c1c9587bc6", "_id": "twin.macro@1.0.0-alpha.4", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-K3xvvKOgzE9N7iSpQZ/xPT3jz2o75PA+XX6LarXVAey0XdI2EhGxKu9yf2qwCqu9Vdd1FcJXsA+EqrOBQZPYjQ==", "shasum": "cb1b11217bc849c8d83c77be3bb2966cc2a0fd53", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.4.tgz", "fileCount": 5, "unpackedSize": 73389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVknJCRA9TVsSAnZWagAAND0P/jDEWeBV0bal/sMKk++o\nev/0ZCxSh7EpkNV7EsiAjIlcD9mO8UBZAcB3+Cmf2bbME+Egw3j67HnAR0on\nmJT3mJNaucZv8Xp+3S4Umkz5s5xH+6BFwkhLtEPX7jdj/CwcBahg6adYae2b\n6FZFs1igCVDUEviKVvTg8jSyuCStA3rIF4ar4XrAKqoAXtIQHKZsRkbHNWGu\nzskMVnyezrrh3HEzP1w8D4AJ+2k4IgDFfxfjLv12SmixsMw2r/CFi9tmnQ6q\naYLXJQn8DCdHV59n4DF2Hwhvu2YYlXCLQoZNCacalwnbqMJclep8DwO4UZ4H\n/VBZuLeI2IF/01ltB/S1HNWO23DZewBDp/tWkWNXz5WfXgTz3vldWrppGnWU\n249HSIQfwczctzRtA64XLiEYsXvKyh8KBFxS2Uwo1UvkJTLtqKaUdfIMWCza\n/X5fz88Qn6S0i4p28ShVDMX8Y3V6//abIUb1bRSU/S3aisNK3wdIh9bG0KG1\nyZKwzuwHcuRM5qsryet82Qf+dqEQQQrJjPbYz11h14rq3nsQ2zo8uDOg/LdD\nFlxiDrpLNJkK9YZvHjf0+viPTEeNNUS3wbOHKPP+yNpdUbvw/BkK7oboyeSS\nfVxzMNgqQsovwc8X/iHKIMpmRN6OaC3p9FDgzJ5D+KVgCgOSa6SxtmnSJasD\nj2G8\r\n=TZps\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnvW+1jM/gEOJ49saxY0WVyi/xPcRyhGv1zvKf9+wrEQIhALIqRlBPdpmCeNLWUK3fKHrgarGidMrzsXrDCMdDh7sU"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.4_1582713289272_0.34518039386656385"}, "_hasShrinkwrap": false}, "1.0.0-alpha.5": {"name": "twin.macro", "version": "1.0.0-alpha.5", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "925152ca39c2270584a2ae14aa1943369e4665a3", "_id": "twin.macro@1.0.0-alpha.5", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-NxsNSjm/lLwIIiXglHQHmOOqdW8BhFT1OghOcb2pVpqCzJnOFrUV+BNI9MgcbxE0KDXEUNXFjNE9dZ5MWLCo/Q==", "shasum": "9398c5cb095ab750364ee4322ebb995506cacf69", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.5.tgz", "fileCount": 5, "unpackedSize": 95479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeW5wsCRA9TVsSAnZWagAAPlwP/A7pTcmRtQXX41Zkrdo8\n4cjZ/oN/fAnNyctm3oLUSAUY2Zn/HVJgdv6JAUH27BEL5/ung4oBB4ApQ+IY\nKDe<PERSON>Z+yupHtxYHC5EuzzJCQd4jl5SlCgwcG6Y2/T90q/8W3AHhGbKjrmJPq\nn9MrviM7e71qOohMHyTOWnx4P1owGC/WVvfApu9Hm8fTKtrHeB2UhjeDbQA+\nd43DTVAR9KRipB/OMlQlq7zMLZ2KEkWU0LpsnScrZsxwpvBcxczQpcawVtW/\nOHbMXsRrmCyDI6N0Gip2D3Fm9s6ydrRMHw/WkFhZyG5OeBfxPNQtDZJwcYh5\n+XypeOwu0JdvQeJRKvAkH+E0HNO8E9AfIc+iU+eIOwV5BH3cgcPrm3Yr46aL\nF5IO9SXX7zJeC1g1XsIppbfTx7jllA3ckQZusey5eYJFUXdkGi6/nyDaNB65\nGeGd54WZm2LV/2XZpLXLIxFbkC4agCS7WcPEppcIVU9stOwDKk59it1MVa0S\nXUQmCoIOCzLAANTmtUtiYRBT72GLAneOPqaC0UMW2Y5Y7RP5ZxH7O1C7C2w4\nUWE7bvg9FUIU21EmzhGmJDQ8Ldp9lG2W5yxcRAtmLKsIHyLrQ8pSFXohaEs1\nqWCzbCyoEvz4DkWHTiDU/4cQCUJGoH1Ur0qKX2SgTc6LJO9AwNyjBzcHN9rL\nE5U8\r\n=Im3M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJFiV73qs6vDJ5lzmVLXk9gbAE0B3YSVBhLExx+eP60AiEAgVkEaBgIC9gAoeNs2maRmyiDWQcU20Ac4yQw3gjd0xs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.5_1583062060017_0.7983000567302765"}, "_hasShrinkwrap": false}, "1.0.0-alpha.6": {"name": "twin.macro", "version": "1.0.0-alpha.6", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "bbe3c77b7aa48a071753947ecd5d098b49ef11ab", "_id": "twin.macro@1.0.0-alpha.6", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-PQLEh26+CZeOHBSiune4WWaCREBpr5wQbw3KAGzbbkJtDnr9r2Dc/9qNIq9d7xXnBsPfRbN9OSM9018eA+twgw==", "shasum": "62bbc55983dada65e774928dee62cb91952e6788", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.6.tgz", "fileCount": 5, "unpackedSize": 95688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeW6OSCRA9TVsSAnZWagAANUQP/AuKOd4bT6fdnM6m8eay\niwn1ZD5f73Vkd1f414Lattiddkdlgy3jl/hm8jXhCm13TIc9v8k06lcyPwAD\ni/tpxX6krJexXD2AFfZJOfooF4phg4DVjr6nM5yGGriUZJnpqQt5USFiQ01b\nR2X9w21BYo0hYkc10xKPpq9/ox2uUapkVGxn/PdYb68qP3JaIXtRbpNPdY1i\ntWgGjFJsZ53OwQNR/CXMHR1dFxpl/AB2UfmsdV3dQxS/E3U63iVr1E8N086A\nxJMD8981/KbiNhhw14CGlkWk027bEqE3KH5Dvz02RE6JVrikdFQXgpwzMKqG\ngKHGMk545nMQuhJ0vXssvxfBDkAW6EDL/+aqLzqfL2bG34M6qcUcwpZ6FQvH\n3C+FpOVl5InvzhmisozzQ7TnD4Kwh1nPyO5hvc5NW/CCLpeRwJaOZPlsd2R5\nogi17TchOT07Q0PRunjbEU+BBXFVXWUL35XOs7mATGmTYfozYPnjv+W2sNrx\nEkAbgGB3Z5wGJKDGAaWf8ShXmI6yd2yDz+ldyR6YsCZ7u+mlX2hfSA0ZRx/7\nVTYasECMOGMj3sV79LaleSitFlyDMZF2qvVAH4qWLyyMjVJch7QTmIi/pQlm\n1wp5FJQu5oMbtgF2uPcz1oLhHp+atBeqTr36ewja0pGUxvh4TVZE3rbfeHPO\nk/Z+\r\n=29zL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQNSZDFNa/Yvt4a15SAob6ECxSfQpvMmlVTwtIDhtBXAiEAuBYyCVRJY/YDd9BAuXeYgrHjYV18r3z4hhZjqJkAYgE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.6_1583063954371_0.6358146787544321"}, "_hasShrinkwrap": false}, "1.0.0-alpha.7": {"name": "twin.macro", "version": "1.0.0-alpha.7", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test": "npm run build && jest", "prepublish": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1"}, "gitHead": "d2d4e39336d85efb70a7bae1b4e22130680c754e", "_id": "twin.macro@1.0.0-alpha.7", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-vAKrVk5eNcgofIBq4uEkHap2KKIjD92ggQ5pkHaA9FtUyH7BiJelIvjPu4/L6ch+WdgMR4vPnmJTRf0tHJEhLw==", "shasum": "2a3c5c5dd3d98eb7498a75f47f1ccb02fb52f0b3", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.7.tgz", "fileCount": 5, "unpackedSize": 95688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXBbVCRA9TVsSAnZWagAA8kkQAKLNmiWUR5P+PzCnpRyE\no6wUm8J4ZcoKmRD+yzurEO4fWArpL+Ayljh33kQBCAZMZugIZR5UJjMHRxXi\nHfvE72Mz+4PvFn7ei9LPUPtjOtFJJ49EECaMf/oKvzahTYYnV4MTd4f8Cx+d\nKHpwuthPyC1hlctF8VxGonbpQyVGLflq2Q5ip8zvi3bnqP1U56dvsWr1NIIu\nf7CTsE5t1N4K0d+u+/s2m9UOAYbQKIwpIZBvg+kecXW2jlMBGP2AUo0Zu0+1\nuhOhftVxZogEz8WpUz/IkFJvP5BSV+aTUEgjqqYWV+5U9taXB9zcCzXiAjCs\n6O6tPyYNNvO5aQzAO1fplnN49HALg6EymwDS1HULlbc6volG0FucAgWItA/R\n4vcS5uM5W0lFWJjc+pl9cK9wj2ScRFR+ryjMu0o/av0Fo4Qy+7tD4lCp9qEB\nM3QdtMk9GjeW4rmUWuBN6vHXfKImdLWB0R6ZURtgrLW9B0Q/OyniemV3BFTe\nsZuDmdkn6R98JzJB5QTLNLuu2KyOIze2BO6zAtaGT7HHuu9nPva5OQl+ha+K\n5CEmH+G2aBruuykWdJ6XcMHfvHIEOtlJbRad1GFRAVNpHyrddF0GAxPadO6f\noArj0G24dC5aRzgvnglciGBC0lDa3UlULVJ6wvu0dfXntrsIG4HDdlePherr\nTG0B\r\n=i7P9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNOhbsqS1T/QIGZd8Rn2WRL+dHdNnylihTFvD7TUuEcAiAKE/drAv4t9QjDqXjgjT/O5mL+X1Y+qkw2+doi1QbIGQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.7_1583093460862_0.05240058178309703"}, "_hasShrinkwrap": false}, "1.0.0-alpha.8": {"name": "twin.macro", "version": "1.0.0-alpha.8", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test:types": "tsc -p ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "@emotion/serialize": "^0.11.16", "@types/react": "^16.9.26", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "babel-plugin-tester": "^8.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "typescript": "^3.9.0-beta"}, "gitHead": "27bd97600bd706f70068fa40ed6956dfff5ea6f6", "_id": "twin.macro@1.0.0-alpha.8", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-Gb+M6u1eTbxuO91uZewLtvAzyUDrPIbbmvk4nTqB+JIddyURE1x7i2jegkm+SyvbhHPlbfKDMTS3RHIZvedmTA==", "shasum": "7b475c9c8cb341d16b58640001e1be022253be1b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.8.tgz", "fileCount": 6, "unpackedSize": 106310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegIINCRA9TVsSAnZWagAAICwQAJfS2DRgUjADpzDNfI1p\ncCKsPFNi7Vt0oNBjiT9tk3qK5Vr+waegVZuxT9Kt9i7LPIwE3uIQWjWt0mUD\nnxAtVuFJxo05sTdQhJKqTb4I4JqRhaljEynOScAib+yc2Mfn1kt+/gT31GyA\n3eFsJQzcgFNFB9r8qCQbcV35UtxpowLIxtii5+1zJS4OMtMemCUFEr1o6YDp\nFiBriegdc/kCgdi/bwQtyoNRVnk9bqIbLix8XSpVkIZpIoFSO9D/DHcWIiuL\n609qDQS+/Ww5guCywHb8Yt+i3EV7nR13IGIftBlruneq/puJzIllHdYf0WWc\nt03lmSV87hK7Yq1dqtigCWOd9skXNI9UnbVFwpIezf6hYRklQEfRyuV3W1Rw\n1s08Zu1FBqQp4+gGG5R15yWXrJCZSxxkQinbTqTD6jKFt5o9YvFoYYLU9OI7\nWFLJ/v77q356csPjvDuHC0cVibdav11pxNVtVnm3Qh2V00RHzGobZhguMTCt\ngD5TYlyUdg207QfTMapUCMBiTA4EOAvWbdGWs8phXT6Tmne4Ol6yWNrHuYOC\nZmgyDT37o0c4sygEHduXATSwYYXtkkDVS8dwok9Sf+9NO2C5CNvBeZcwDTR6\noHrFmchkT+iQxOge1hz8/vfC+Ci0ehZQVetrAePEG4F1SickoDR4/rJp/Mpt\nkNzS\r\n=SstV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTEpCdpq0PKkgCDnOWIp8p90R7W17YeO1UK+rt/pPWlAIgE3Ozpr18STpHZJERECyfXA0ShzlrxtN7Q2oWqv6AQg4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.8_1585480205079_0.03308379506118109"}, "_hasShrinkwrap": false}, "1.0.0-alpha.9": {"name": "twin.macro", "version": "1.0.0-alpha.9", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.8.4", "@types/react": "^16.9.29", "babel-plugin-macros": "^2.8.0", "chalk": "^3.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.2.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@typescript-eslint/eslint-plugin": "^2.27.0", "@typescript-eslint/parser": "^2.27.0", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.0.1", "babel-plugin-tester": "^8.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.10.1", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.1.0", "husky": "^4.2.3", "jest": "^25.1.0", "lint-staged": "^10.1.3", "microbundle": "^0.11.0", "nodemon": "^2.0.2", "prettier": "^2.0.4", "typescript": "^3.9.0-beta"}, "gitHead": "e84b6c22032ddcbbcac1e3cfcc6ecb3b36632cf8", "_id": "twin.macro@1.0.0-alpha.9", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-FpT2AeL5MkKIZ07JDm/fftv6ZlV9EtuSDwlW4QGq7KNN3rc7UZ/lhWpEZKGpDyhVZKjj3GY2QioQmOrlwwub3g==", "shasum": "b2245cf9baebbc6a339348aa13aaa89d42954157", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.9.tgz", "fileCount": 6, "unpackedSize": 107960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelBISCRA9TVsSAnZWagAAHvgP/Ajv67YQ9+aSPns/U17k\nqzTQWeYS+HZVJqeWarmSyTRsDPK7o87LUM2NLTVl/muh5uH1XzKpnJjqpSab\n624tJ62LZZ8/AWFgq7Z5KCPnUO1K7R2drokPbwJ0rt/F0W0lgFXB+LrKRne3\nI9mepx70eFptoBOQnL5/VwYmJB92XKAPT+66lWqyQkZ25BUTJe99G/6sk8rE\n6sLc4hlX8Q1QFHpiWHRlyvs8Ye46JDMNGTsGHqNV7lcgFtiilVqS7R/OnlFT\nN3K24A7aUFZ6eBBNctRI5EOBfUNMdRGIxSPaRfOg/ga6Dt8LMbt4xD0Pa7Ee\nlhcWtwPYoHXha0J5SaZfOCOtjrwvJ5U/t4jtFxrJmPuAeUh5bE8FlGoO+Fzk\ncFBJQkQOY8+CQR0s3tAzDHar5GaNJN7r3eaMsoBPW6uGNB7ObFIvEbUShZtD\nvXbl0a/HLlFUfUCjyrAu15tjZFthUWof0zUr0IFsXSi1OEpXOGRGPGZh7hSL\naIVohpSejGQQ20725a4gloA/YEhSz4962UKsVFacQ/zdQxR/LL9cTzRFTkqa\nkEHXsq1/PQx04PI+XPBB2lfswiOwLHa2O+Ey+qcmR8HhZ+6y56vjkXmZUnGT\nZdn2v4OgvrgmO48IszerLHckbtnmh7MF/BjzARjlOskJIu/tSoH70n6MElTa\nof8P\r\n=COv2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrT49oDstaeSDm7U87/jP+MEoKGQ1xxEAVTuAW5oNoswIhAJdKRYYugDAmtg6yExNxGl0pGZL879xtYZCrn6rZm7ET"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.9_1586762258017_0.02614084926486049"}, "_hasShrinkwrap": false}, "1.0.0-alpha.10": {"name": "twin.macro", "version": "1.0.0-alpha.10", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\"", "build": "npm run build:all", "build:all": "npm run build:macro && npm run build:util", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:util": "microbundle -i src/utils.js -f umd -o ./utils.js", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "@types/react": "^16.9.34", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "dlv": "^1.1.3", "dset": "^2.0.1", "tailwindcss": "^1.3.4", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.0-beta"}, "gitHead": "2554a234e8d6318885db47b0e4610c8db0ebe1fc", "_id": "twin.macro@1.0.0-alpha.10", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-AxmkpMiz2D7iMG2JE3GnKkC8FaOXPHsagA782Hv184cS7iRImrc2njSFgq17Xx/WybJpXf3p8q0ZLVG1PlUIGQ==", "shasum": "ae1a250db9c2584c429f52984b42c6e511360c56", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0-alpha.10.tgz", "fileCount": 6, "unpackedSize": 110921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJen+fDCRA9TVsSAnZWagAAhCYQAI7gou88sQMKpANkVJ9H\nitBTH9lbeaX9UA74eE81cLrKxRiq7XXj4F8rJ9YeYCjVD7o3BAByGRimvubE\n5lXWhbigOvAtuX5pVuciy191tPpvJlgeQoDJOAlqC5YUquvFNk6Yn0fFX7AP\nHDvkke14jkwV7sn/dsClYOKXKe5cQQmhJ7qfEIre2pbKXgv7+nj+JD8YfeDV\nSbWyQB2u/imv5AM9vPxZ5Ko1BQB3sKlvw3Be67K5SiHKzoT026rCk5fNupE/\n1jHohtFBAfH8Qxvi+G1xPHtMDcFHtqzXPcSF71451JA7Rv6Fa04x4sGilM0+\nv6lemIwGylyMtPWyP/M5FvNHnVT1Wnkqtp9vRXayS7M1JC1DUCfALAi4lB0J\njXapEbod+Hwj7IlPG92ERMGY8NK/R/QmOpp8m6gLkvn8EAolh9WeR3Y2ecPO\nkiZaTzCQh48azAa70HYQkZY9UKl7VDhFGhljUqzrSr7TA9FjOtHSfvaNn+w1\nh4gkK7Pemb/A+JJKrNMtpXgvQMf3vJUEbGkXtkrqlHXHiYjIhhpPA/M2FwtE\nTGIw99LMeNBKRenngS9CpoErLs6Qn+2TYjyj2mkML7PiIjeftqO32HRxkA8R\nkwrfPi5+fCzpBFkp5Xto/gBkRiojGqvj9DoZLnMAzJ+LKISqyUt4uP3EwW6G\n2mte\r\n=/jyJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFsAvMC2MNzx08z/LTdLW7UO5YKr/RRz87Bpv/xlR2kmAiBCoyOipsDiHY8jzQMhTMK08nKTMHjgfWWJjhX21oXXhA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0-alpha.10_1587537858552_0.8129432433777501"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "twin.macro", "version": "1.0.0", "description": "Use Tailwind classes within any CSS-in-JS library", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.1.0", "@types/react": "^16.9.34", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.0-beta"}, "gitHead": "ba3c5f50d1e9e61def603dfc96ac026a4f24ccd6", "_id": "twin.macro@1.0.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-POnHFrm12h0uJ+Mhu5Lv/8q0euX4DP+o3tGq/wpolDEzRpn5PNBEd5qDL3hyuPSpxyn63MoKeoFI3j6uOf2uDg==", "shasum": "9d58311c0f8f05b8ccad19f6e7d0ec379f2ececc", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.0.0.tgz", "fileCount": 5, "unpackedSize": 107527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu/yaCRA9TVsSAnZWagAAtGAP/j1HfmupoJkdh9dyFRl8\nad76bLmO+NXqnmHSP89rthPVJc0sdOzksJbtTuOcTUeilcOVSjfi4+rgbgSn\n3iyly5OXhp+152GU+YXui76WKzukPlfCb3hM4J2pmHclr5IYsWk4ztBPOeRD\nA8ZMmtscjQGBjauj1Wc8WMvPxeVs3UVAu8j01x5mqklZzhghBS1G3FI3wms4\nGlguX2SEV1Iu+kKC8kv6vukAuI5XmnLfou3pM+QFQiaZMCXhMy8uIOnl4EmT\nVYQ2S0tMs2X+wWfK/XiOKa3ZwXk8bTCRxifgSZv3cZukmdmjEvgAxCI8vNXG\niZHqVPMoeq7+/e2XzD4AtLgTR0vbmd0SuGe2Wysweh5uSrwmyW7MCz1CcFBn\nIuuSFRLH66a+NcIKetOKUiYtF/+GPAJQR1s5TggjlrFmKpNHkkOjocMyLOrn\nwTnUd+cXZgdIbP6256PKo7e1/f562TIWwXVBrjpZfjlRDq6dAj9bCMjr8Pnn\nrpoenfqPQv6JiwRIXYKyimy8zqf8nR0aA9Cg4xIDJLxDbB/t3pKiH2OJrVAo\ndoIHssYVSqVsDqdVE/rnzOZCVeTrAd+7kzsBSFrbhWSrRd1Ulk1RaEBk7cVA\nXRTCjt8sCFZc4+MTI3NuHfNMXREGPESScLcNSQruEjpkCOCECOD82k8M3IFB\nDN5f\r\n=pBCM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFU+lLH6HXWjmvIq0uq3O4I3Yjxzej0DKDkMYRt5OAzJAiEA77p4nkD+z+minXwbJ9YrHdi27kBWPgxf/n+gEWy8I0Q="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.0.0_1589378201184_0.35328878747442793"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "twin.macro", "version": "1.1.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.1.0", "@types/react": "^16.9.34", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.0-beta"}, "gitHead": "31cc308b52590255320d3cbe3bf34d03f23b9a80", "_id": "twin.macro@1.1.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-1bSItXg2qdaV5vNHV/ZMwaXHfoyfGwJO2bo65+QVfZKGBOV1eTsxrOtlnJXIOLKkl8lzZPDRJZX4YcgSmXvAug==", "shasum": "41b4ae13b995128faa40a6ec330878bd68cda2c6", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.1.0.tgz", "fileCount": 5, "unpackedSize": 113269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexJXFCRA9TVsSAnZWagAAoSoQAIkpRyqo3TE9xirr+dGd\ng6vE8BDndnONSD6Fyttr5jMwMM3zyo6hqiYvt35E+2X7brt+Nxyn1CLS+4Qa\nx0b1qwTW5UpJW3LZc05/00bN8k7rU/ZPyxAFX69FdSy4C43aZwNN1DrWnDmc\n1Pmz/7fKYBPGkgP+culLEIaM4TQnYbjs6bT2XmytK8pCy0O6IHSBQXl03/iS\n4owTPDsuQkGgteZVNppo0E3Bo7ejwlSuPMA9X8zs1FMCOB+7dvwvnf6Y1uGl\n6M7PB5LL3ZSGg4y1flyo7LSSNwQQBFY3ECxYJPkFGnTkWDVgBHueouJG5avt\nSY7iJbv2hQ8xgh8AxGjjaGBnynQr13iH+vfEtyms1VoRry7MFzmIICWblxYf\ngB25d3rKusF8apHDMfBVMfcVNBW5RLzeJm8VyZDLpR6h5GKl+yoV+qPjtFo0\n0K00ACPzpx5wXHXFDCsZGQVIZ7jwBjqxyDSXlmwgABydYxNazVd2cMjrYE+6\nWlvCSUnOwqz26dr942Qe+JwYrTonuulWBrcJiAa15PsUZEPdI2jz6l9cEjhc\nMzwgttL4YI4KCXNGMhzpIqvoeVyWPHXEnSN0hHLTYkxecE4vIu1F9dGCTeTm\nlKdxbxckEQ1oQTwBkF5WClHQzYh0WKn150RQ8zyhi7WQClWyLJRA8Oke0FBW\nF2l9\r\n=yB/g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDh17B7XEIN5rDnS2qFPRtsL+QV0m8TzDPBuSqQjiNKvgIgSPZJGRZ5h+vX+oKOm9YvtsPSkXDcL7TcDtKXdUTs8iE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.1.0_1589941701402_0.3844043768247474"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "twin.macro", "version": "1.1.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.1.0", "@types/react": "^16.9.34", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.0-beta"}, "gitHead": "80efbcf8a61d8e95d246744e500416d8315e4e37", "_id": "twin.macro@1.1.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-47krXZ6T6Q+5thXLCn83yA41zBsg8QsIK6jeq+pVHcJHoA0Fptf5tGDT4a3nJ0F1dHhIKJIybG+YzygqL3w3Ww==", "shasum": "e21c559197193bff86e1b332e2b4db5462352f87", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.1.1.tgz", "fileCount": 5, "unpackedSize": 113317, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexa+XCRA9TVsSAnZWagAAWWkP/ixqRTYw1Ye0pohhmOAa\nOwo/4Qxg5LHrsdv/BlYlM32lzTWRut+yg0bAVwaKrW9vjuGhE6VCQ8LrNlG0\nW+C+9RJcsyDjo1aqOc7rZ1xg2lqJAoBa5m4222OHdLKxNZKNUkSHKYzOSWka\nwpMn9o/Go6tbR1y5YXmQgkFemAB3QxXiTj8dxSyq8oDWgBc1fUg9NZ9aBQwz\nDlYbgaRrzH+aGM8D6YrUAFxyFNAASEgK5EEr/s8TR2k8AKieOEfAU8XADHq2\nr+PffBE3YUND3PXN9SdX68a1aGhzUgq1BZDxqFXlSg7jZxQTLA1XKewj/Xi+\nJn+PG2FHvYRo1YzkrCmFJWx6QSJqLHQSRTFOc57TdKQFTiSfr8sEmU0EHpvg\nZJ8GPHJvrIxbuM895sDswM6DYPNqxgSLKLQyhXhUuXbexDCFgSzaef6JizAY\n0okAhkiyI1RJMUBE0Z3AQLXizwe4iQr7bUMiuwG2DLiINLzbfTZY5Wd3Zm7+\ndQbgNDqtdMK3iZxwtvKBWdFZzruXVUnUusX5YFYWfiWiFYvjBvIXa7x2mujC\nojaMlmgTqEQH3oFU1icCppOskpnjC7fvubUxN7+dZJwdcewvct0hFLf2MfQB\nGGtF36n7TNjCuZQvBVKsFmg7imwza0QnJvYzgggRTT0mWD2k4IpcqWMXAgwF\n0s6f\r\n=O2ZV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDj4TGrnF3ztY/cHS1V0BjMTWtZwpSZ0R0fxpEIErqASQIhAN5jyEpOzHGQVbBTrhxiAPDulHpbyG1JLQYEEaGaIRDq"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.1.1_1590013847073_0.4099141677735407"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "twin.macro", "version": "1.1.2", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/styled-components": "^5.1.0", "@types/react": "^16.9.34", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.0-beta"}, "gitHead": "666f72f66fd7f9d3e5bbdd23a36434c0d09b7ad2", "_id": "twin.macro@1.1.2", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-SkRqIiwA9URhe+u87mIwcYNErbsq/lqNXHjTHDwTjMuO19HBHUltMqJio0maPxy3eY6VwA0zMgfRL0QyocLhNw==", "shasum": "5c79aecd281923549a79b9f69c652ac4906450d8", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.1.2.tgz", "fileCount": 5, "unpackedSize": 113284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexgxiCRA9TVsSAnZWagAAk0wP/3DYKuKdhbJwJMDE5Z60\n6ppdTwFsOiDkCa39TwZfqlEjrzGKEIuSs4jnQRdbenyWTUckdr/Hmfc4er6c\nDN2SAgTfTP2go61I6Rc1/N2ebR/ZzhfvozPIc4FqOpOsYeo0qqmD2FGWJsZW\nPVFS4gAC9RyKdpy36n3hSG7DF3KhkOaZ9pmjwGuHmfH3g4wivf9yZivGPm1h\nre7E3grpPI5f1MrGjiAeEJw3JxRdKz4/Kla0tQUsAug/H55ddW5qNYj8IbSB\nFJe8gEcBrpWIYJC+EXM5gudPjjY02Xc6nGcbgubfutBu8g6g/HlKhTjsNl/o\n0GizlKfPGSCAvj9p4GP11SeATEBZiCAZncmBDLQEcjCnh6RGpFjPZIic9f/J\nBuEdh7kDBsQBMXm7WZapOTSI9yydUXwxRE/cBiRPlSXRNLhyDFtLbqhmLI9U\nv8eNrAy6lLMjnrov3FpQN+sRnAeT3whx/wo09usKmrBXJyzY2DZcgKdYaBvj\nKDDbMAQeVWodBBXb16UZDiVw0V0kedRRzcWerq8t7L5C6XgnOpcAoZsPQv+S\niarSGG6iG9F6VGBpN4EYQAAiBO0pdvmJbq7FgjMbEceW+sZhW4Q/LYZOU1PP\nJLjj8DPC1+auO8vSx+c7IgHkOW4W+yorUhibjsUDzocaklce3ycSq/IkMb5Z\n/icu\r\n=roxn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCf5OIWDWP1fcEU/FaJi6toOSvmGIGnFaNsoihjlYgECAIgKFuGLGbJY/lG0esd9C0aSpgfCO9hYq40eESYooOxHBw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.1.2_1590037601980_0.5236424767732355"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "twin.macro", "version": "1.2.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.2", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.3"}, "gitHead": "de2d98118d785ba9014ce3d48e7564ed5b728a73", "_id": "twin.macro@1.2.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-IphTV5Sub9QJi3+4Ns+CzBqfJywEBTZfAizyyrvbUwx3NNmI22NuWonioFXpfzIFhu2dmpsWv3brvE87xcuygg==", "shasum": "8dcfa76cdb39d6d2e185a5bd9e71bcfdcd374e40", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.2.0.tgz", "fileCount": 5, "unpackedSize": 97145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyQiuCRA9TVsSAnZWagAAhkMQAKPhJuSB2MqwP3MKpFMd\nOqiCKd6vdEyFTQzxXhWyQvPJHvZHIYi8PYNqBHIkmW0r9xsMjIL5d9MrCfX0\nydORc4P7HZr/YnjCcFYjCi88ejQmMBkg7kbMV/QOd+S6Hl+rw5ZcB/Wl9HoU\nyRapPHCwHfk6rvI7w18LXDAq6USamQaHRgvTJH0SNB3fKDtO1VSqha8FCVX8\nhrG3ZS1H27kP/suQlcaFTrRbP68tjHTir5upkLn16QkQrdz9lHlSWllz97PH\ntwsdWRG2UWd+EwcOSkKFNVPkhkpeQQ05z+DDjMqs34W+wfmMK+8c/6/qn7WC\nY5zs4+5R04b+G2xHw9iduddhMhMKY5b2aHwO6dwv3yLsgC28SuGKl237EgNa\njzmN6wNwhAOfAbNULczhX0s3ACSIi+Thx7Re3HTf6Z2u2D/WYNbItWuALE5Y\nF/A37msmmon7F6kfzErfBnIUUjGUz9pYSVXfnXc7v5tzvOv7jzEsOL+Ds6AF\nsNVlS/6PWAhqJdlKrfujfuLE/ynkr5N8RrMbOjOPbET6BI5NohB6Fk9WHth5\n5L5+4EnJpP0v06VzMPCbRNhP1v5FmgWgDjgOI+HyTeRf4UMEsd2iL5aETk05\nRA4u+oZc44VYxUId3eJ1MrO00woAALsobJQkXLbfWLmNypto2iB2tKbR1BrT\nLlXI\r\n=yTOx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlgkjTV1Jo5Kdjnn+9ne9m8oD/1jxzfxX2Z+mviBY/uwIhAOeDSpBuGQXzm/OIRFoO1+DAyp7NGrkflgNspWjiFb0n"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.2.0_1590233262201_0.31894055895374196"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "twin.macro", "version": "1.3.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.9.4", "babel-plugin-macros": "^2.8.0", "chalk": "^4.0.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.0.1", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.2", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.1.7", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.0", "typescript": "^3.9.3"}, "gitHead": "35a502a53ca50e776f90b34cc2433957cad67938", "_id": "twin.macro@1.3.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-Cl/ZuG+iUoxfM6KmYR6tswIOk6wXVojSxGXKkQsxWPqoSyGJ0Os0wcSFdOrPySluCqewxkD3aHtBGNpLnDPXFQ==", "shasum": "70cce2d9988dd89badb81828452ac6ee6d70fb7a", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.3.0.tgz", "fileCount": 5, "unpackedSize": 98753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0ktfCRA9TVsSAnZWagAA5sUP/RuHXmWCYwviCTmklxDW\nTIA33B8cTyR3fybRTn5MJs5TflkX4xjeI9EaZZSgaGllz58byIh/21LVVTf/\nsKzN8O3qyqI88C8eZx6oxXqYskRRbfjUyNTKrEly5+KahEhfDSHJuP6fHh9o\nd5uMuwtQb2fFg6+LTVlra/9u55nzbQScyffSkLaeQfieKLdRS2WoWP/2/mAB\nvz7ZUAKB51DdJCXBZVk3ZhEV0XJ5XJD3wqiomaSkn57Wtc6KHr8mX5+QP8b6\n1lVYjrtn3jX1N5FvSgM60QPgpQoGnX2mtdn7YSlVu+ImjhYAtxWVLbcwBAcs\nDwEj/wai7YJ+HwUuufIxoCdEKHSnS6Kdv/bwps1fD1H7ume+eRD1LbFKFehv\nRWBBShov0/NtE8FPS0ovJ+LmNKzXD3tM/mORfyMb5S+Cz37jI38kyc60Ih5b\nP1vqtg4Fx2FqLKSfZ0IOMlKttrcRjNAPaiVkrg2iHl2LWB0GXVxorU0uAB0p\nO/j4dUEdGLjW8yMXjjQe1Q9imEZmPEsNEIaWjzkKiHmN+npHB6Uqk6IHEZUF\nrrLYlgh8PB8CUtaAiB1Y+4JCp6XQSE1XekJf/Xd6ShaFGJoQSzwzZEGRewnK\ni5lQ0GagFk3YfUDLV/4EtZggRZBbKpwwC3zO3SuuVg6CFgR0seFpT0Dtqh41\nqlif\r\n=hqV3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICorAdPSVVFfqvIAHGB90KEzyo+xIXrsxESPEshnYTwgAiB4u41GXChShAnXeK0pZpsHNB7xGJG0r2OiugzYSMR/7A=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.3.0_1590840159035_0.11672501466270391"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "twin.macro", "version": "1.4.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "typescript": "^3.9.5"}, "gitHead": "b47fe0a364bcfc7a30f695fd3a79eba81dad3f56", "_id": "twin.macro@1.4.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-cmC+BzcQ44FduDJx5RORW7o5xcoDEFpuE83EBtdgv5S5yKZ3QM7PTN0zYqwxyUHs+hZ5Yh+JLXHxKvmobpGMAQ==", "shasum": "fe90720cfd6aed4a78ff3d59e0b1555492774b0a", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.4.0.tgz", "fileCount": 5, "unpackedSize": 104670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4M91CRA9TVsSAnZWagAAXK8P/jIyg2fhPMmtntYjODt6\nogGGDR5inVw63vbfWQjtVw9FSNFlJsVCxA2N5iKjwc5Zw5yZ8+pC9J946L+M\n6RaJRnTjMPSSy8S5sBUN8wCpfbZSEhdUOkimKc+Y4rQYMGyXUyWVvwttf2wA\n89vLZ3W3orQHN2REKsjQGNuiVcwQLxFkjcebjePU34BvoRbrmVYEdlzGYyAv\n9r8i9ZHO8A7WIKCReuJUwXhKQFRAXX1XvKJlj+276luVCKwS6DBgevQUNODk\n6r9CwouXw9SKxk6xBwCwWMjhzCeVwEuqKDedcyw1xMJybp2FpmtgdoDmIpRD\ndnvJfo3/B2d+QXLMA5L/BVTfm0yvcAsDXLNLv3teBpwOB+XbvIC62iYiRFzP\nQhIXprMFHVGmaJo+ZrKAJEOJ+ngju5D0Q4KRm+ZcQTY1t53ZZ0olVDxgr6ah\n+FYyOS0T9d7mU/YEk3LSaPv9eiXNhwxHzTQ1e4d6yZyWmLfi17XgxtiIRGGK\n7rD93hDlgfI/hWeFSP6usSJG0v/51zlv2OkiY7+Rv7n1+3CGVOmr0L2OwLJP\nwMAD+3jcs8xysG2O9yxVTGLE2b7ugLP1/3IbIWh/WbB0pFb1hFygOcOiLSvN\nOMJUGd01q0rGg4eUqj7sZPht0SAnXI3n0S7zfG6ubj6YYaIl/BBE7pXKEFjt\nN2mz\r\n=g1x/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAIchvJ+MvP/uO3DYedJ43UOp8mgVEloDZayR3XTbVpmAiEA0AIVLZg73Y18nbTW6uQjOmnPk3mCgrlivQkrcUEznPg="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.4.0_1591791477020_0.5140217008636709"}, "_hasShrinkwrap": false}, "1.4.1": {"name": "twin.macro", "version": "1.4.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .25", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "typescript": "^3.9.5"}, "gitHead": "18ec7d02407a1714f8715d0648a646236c9b4af3", "_id": "twin.macro@1.4.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-/hUXvyQepce26FvgfZmMl7IEgwxKy57te99FuECxeRUVjrX31A1F27lt+SSrtFNivoCN4Dk7FimUx5jqjth2jQ==", "shasum": "48de95e52c34d068486610889a68d44c766bac1a", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.4.1.tgz", "fileCount": 5, "unpackedSize": 104664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4rbOCRA9TVsSAnZWagAARoYP/00Cpop+F4660QGc7wr9\nNPccMLsmnkw0/54VpItZnbWps+astpZ/43RfGIbGsTrJFHY+4Eysy5Nh2Tec\nrySx64/+lsbTkyLFxOwUnV3nFdiadWMC/x0qPryNZIU+ncVh50GHqIWRVFXj\nIpSr4iVO+IioKf3i/+yQb+fVeFdZ1nRZQj4JUil+Pp0zQy5calHu0m3x9/GL\nEyt8pBq8ysFj1xFQzypDZLFRfKNn5UO22vmB3xBRaodPr1lc6aQa54EeBeM8\nMaREChrSwvflJqrl2pqBe7T3OkqCJ5azCB3gYW0iCadstp4qs+OMSo2Ppsuc\nbib8DmY+eynVFSa0iUpQo4Yq0wPe9lc8zJMoHuWobmjJzW3Du8/fqfDChLqY\nO8gNjG5LNi+z1ssQ8pily1UMys50YC21wnTAGxh5EuhlibYD7ZYI+oSjCGGC\nj+j69DFPZdFmoz9q5xImQii9EX9wkzqVluLZ3fhdnPTt60YZr1amO/wHbKcx\nRyVYgqBUFp0YELUNoR9jnC5RF53PvOSamPJVdYnIzfWsXqENoFkpxChIk35P\niLPPq3p5buOBrU9BWU2RVCw+NnhJZvfhacssR8I+WBRIoJ1zAj/JZ6B45P7c\nMcvzcNOHRSQiSO5aCrUR85PqYAxmmOvab8OGE3aaEisyi0zLXdHcYKfX+klm\nqib1\r\n=iBJj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLTK9e1WPWY627W6N0cCni5WAEUOfL9WMXar+IUYiWZQIgO3tFOoqxyIouLIHPZWF6Ea59XWdLHWyeVP6Cd8pYMx0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.4.1_1591916237537_0.18308526630121857"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "twin.macro", "version": "1.5.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "582ac454d2403e9be6bc9acaa99af035c134e20c", "_id": "twin.macro@1.5.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-LDdNwobwizgOXVwxLObALq1j+u9USFrGXpKPQy7UCum+y/8UQBxCnI+JV7NCRq4a/+3o3ot8TY0hmkIriuZP+w==", "shasum": "8ef869ee32e2e73b0f9954d412b1d7fe80519d70", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.5.0.tgz", "fileCount": 5, "unpackedSize": 110044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfASJTCRA9TVsSAnZWagAAuicP/3MQpCrhO7rW6kNHwMCt\nZ1yTlat6MhqHWStLwEQRFEv6WBK0IA19wwz8hzHetQnWHNvutpXcsw7fpW7u\nft4cFk2MZpk8gA9tWkWMlmoaVKxg9z6SOLYVomAWyBsk/baOL48RHYgsFQiu\naByNksUFYCHUPT+FbCAxiOaR4/GazUaVrA18CYR0NYSiqZqEwOAlOYrXhcm7\npLykM1gTmXJLGON56YJiCgKU3/kbP/rZZKDgudntcMKBShgF9l/ZcoVq8mej\nTy/0VoV9maUgZApgzpHv7sjy/za1cqBrNQieJZQXy9lrwzD2animsCaDGXOE\nxHSvLRH9+kTpmKeSgKGTnjKPX39rg7TM+hyzM86SYEY+s/oFAjyWSkH3mIX4\nl1dXTzUHBO2Khegul0oJR80SPweWK7D4qM3DXUXnVZAOfmvWBSv6w6e+64T6\n72WpPeOvSuYL9igvhLd4LBXlAL+MJJWa6B7DqHWAOwCgjtny85axt6RnL4tY\nIBTzSbYvWKzPFOzuPrlcMBGTuuhFbboEfCI7/Eq7XpcPuH/rLo+UEuB4GfmH\nhCKHoRLMzoLNtAnOxTRbj/GSBttW8J8cnmbz8oB+Yb7889xPsA7v9+tbtiSc\nzEeWmffW2C9WNfThl3+WS3HyRurE1qNtQluTKdsh9uJdhxsREECS5GyNJGkY\ncOT0\r\n=vWoo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGU381fBq5155Ce90BtiRAi1llNMDEle6lQ+hAT3NsOOAiEAvsrr44RALUOcNAp6e8FAuthjRaef8vTWNPrHQ0HNfwo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.5.0_1593909843158_0.14685290680864127"}, "_hasShrinkwrap": false}, "1.5.1": {"name": "twin.macro", "version": "1.5.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "b7f4248d1ad9d83c16a5694518ab45ce8009cb2b", "_id": "twin.macro@1.5.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-LkKU7i2t7kyfV5vhSeBsYVDUMlDiKzYVL79LRq/1hVOMqhcJDPxK8JOK9UJUNoJLhrD7ocTfH1QpQsNPFp3baw==", "shasum": "fa2255284a7e676ece3eb8eea17a66e6f3ebb4c2", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.5.1.tgz", "fileCount": 5, "unpackedSize": 110628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFMe6CRA9TVsSAnZWagAAkowQAKDhQG1n0A+rGEwFknWe\nDzIBiA721fiLAu6VDFaAhplF/dbpKHSPFu1uqwSciKAqVkp634a7Z5s2Xzex\nmSgi7GyG5hkXIxFLpG/clSidxl1iLA9EyC4NP3a4JOjVDm42czim6N5TrBy1\no5vF2Kp+lDRw9a2znNa25+EXfEXleHQdNuhWsvS7+XTCn7DgNnVTmOjAvjZr\nQUBmPOfZwV1UDbh3GvunClmgzFHZddTp0zB/+hlmOrTekQpcXIDiafK2p2Bx\nWGjpR9S7Q80UzHmN1OYGEGfavMQUF3JJhlwDMpB3YUEMTlLeVAWAGkesjwXT\nhPTS0KioKYToHRGXNgj+P7H1/KUihJAIsVUL5UczpmX5cRDvAwsi5YYGi6f5\ncsMXHJtVFdyyfdRwjXLehodaKSFXnqn0JVyxuay1+whnCVJDii2ckMGyDZnw\npNBZkxKbJJLB/ETXKZWmKzOneANIfgvY3QS8+2VjzFu9O1J56cKAUnv8t86f\n5QfmYjxb9Alx2OhrEMLxDOgYu0DY1tUO7pdMfyUAumu4ohyuCdF9/sh1BH1H\nFqBr4qHV94IN+Yeu+sE7505pxZkWzP9ajqwQmhHk6jyCqujuXbSSwCypUJ4Y\nIxBBgPqIe5fiquOKzabhmznssw+eWDtfGjx5f4xg0mo1ogx0V/SVRxDsDmSb\nLjkz\r\n=ScJ0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9AzoUt+1Npec9ukrysDB5QspEOVTpk0ZNPgKO92ZuPwIgXUgLX+0BiPAbA96dXMknrvdeXYmTvVvq1aaubBM5Zmk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.5.1_1595197370001_0.5115306120905658"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "twin.macro", "version": "1.6.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "f63e21d0563abf5639469dad0af9679476335cba", "_id": "twin.macro@1.6.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-R3NnZ/fuLnaEaJGqgdwWdYpXaRqeipwjEx2/g70yAEnt8x8QvwLeYqqFobiR05uLMSWfr2nFXFDJicN+6v22EA==", "shasum": "f73ef12d47eb6f336ff6ffdc1561573397e1495b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.6.0.tgz", "fileCount": 5, "unpackedSize": 116403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF5UACRA9TVsSAnZWagAAEbkP/jRtt91Jgqj1ru/k3ZJD\nrbMOU/VO6YWhvRsMaOOSq6+Ksr4sslrbNCI9qL0tGWEyaU4Myp/6CDIrRkje\ntl5IgAWe+UUO1OFzAwBws/fNl7WLS5HyWr06KUbGD9rQ6fwiBm3Xi7/YwN6E\n6g2B2IIK3qS7a/HCIo/1h0ylK8N4cFfA0/4+T6v7rGk6z679VbwsLcuw0HS1\n9QkQdGGx/S/awdwk2yQ37pTcayEdnkLPtis2JaS1FRE65f/SwbLd3RA3wHJ5\nfSUZtQX2HztTByzDeSJo2NMRzPIKbDYCPswr6/8yONIZM5ztdC4q6mMYNdll\njiu1B6G/0qDkISZzGT9dmgWWtmFFSRRw2KMhZ50LZ3IAEFr8+TzA7e0CXm2R\nsFq8F44Pz8epnKtS9J+eTJ+nsJIe5GAuU8Lju7Tot7BUkL64PNZerKD9qBKY\n98xUw96V/L5teHy3hRitX7X8u+IZap2mkQ1dUPv463zGBMtUJsPHL/25Edb+\nVg8zt3Kj6lQzDXSFI7bXeGxqke4X3/s/KpShGypXdozcSR8mEKP0MGJSSwD0\nQGyyUd5atLZRYbarC7M8vkoJDxpeaKzG4CzKXy7p/NKW7wcPGziloETv/oU3\nQzMK9anUO6sJbTrWZO95R79tb/C/1vwPnDa5O51vf1T3rZw2gDVWD4sACVtP\nSVlo\r\n=CHc3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCd7tq1imLSy4ejHuUj7Xg3/X0Kki+xQtU/5rqrAqjmPgIhAKtEFaycUjZ6xuLS6mPSNaTBnbcONyXxT8sz0qum4g5E"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.6.0_1595380991969_0.5879193586815965"}, "_hasShrinkwrap": false}, "1.6.1": {"name": "twin.macro", "version": "1.6.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.4.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "2df2eea79bcf241f33ad19f8575f7034aee6a0ac", "_id": "twin.macro@1.6.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-ckVxmDgyShGHye3ppptCA062Tqc/h3DmexGSi0eKt0qd92fwDQwOA5GvzQ7Fukc63UVJM8ko256qxjyzACovSg==", "shasum": "c1a68eb66d68604e442862989c023bef669f044d", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.6.1.tgz", "fileCount": 5, "unpackedSize": 117881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGWjqCRA9TVsSAnZWagAAC0kP+wdRc4oRNAH5WMgcc8W3\nmasNgmcXc4ihNPgsYm8o8kaJ50Ed/SfRGSIvhMQEFP1WT+wGgXbR8Dhp9wY3\nYKKjYkOMO34uWYO0JSw8j6pjfzIuK0J44ZKcsgVQbKBXfV5qd5H05EP2v2ri\nv/pwTI0+qWU+60Bknsy53fdNSfmM5F/3kWe3CY94dJ+MbodR8Wkfz0JaafQ3\nRDulrT30rLRAcVfzD8KWESfQC5RqajdNacWYZYZa/wv0cXF+pN3oOpE/lGkd\nfs7SYtTB1ziyke3RNTmyUQjy9/Jz1JE4U1IlOHGyT0bY2jXcxUtp8n28/Lwr\nseJNHJ1TLv8PEMj9cl8wqcXVEyggFiGgG80TxZ49T+Y81aIbeccfuXxNhC2F\n+oNGMYZ2btJHOjQBhvR5HENJs+B5vayQXrmBkrTz24iiZiOiRl2QhZ8yVnUW\nNxhZxTwGX8Li2Cm8oyM4Hus90eu3T12qT4ksmPrSeT49BhKHrqPsj9sC60Pz\nYwdIYuaJ+siiuKK10N65DtP9IMuwvHchrlHefK4A9w+0a0kCypRNG3eDlHSt\nH1a4LaGVagAmYlr6v/CjErupkqpRSqqCJJDjsjRi8t0SnGurTL3npQGOkZjH\nMOv57TGM6doZCf7Fa2/I/O5t1gDqRw2HrMEYjqJ4uMLaQBrwiJ6BFXBsmpjZ\nko9P\r\n=2iVx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0NQZScmSOdnuc6hayVWGpW4H585lHZN8ZYs7MBFgRmAIhAMMhbiAIoDvELmkYEQ22p1D322iXnWtrTlGjgNT2QRS6"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.6.1_1595500777767_0.7820765060996031"}, "_hasShrinkwrap": false}, "1.6.2": {"name": "twin.macro", "version": "1.6.2", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.5.2", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "2a1cd18bc055ea9e5136db236bee7de26a49109e", "_id": "twin.macro@1.6.2", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-P/I11Ee5UkyC+HLc/suit+Fe1p3wKi9VJqNHG5dq8vmLrCLlzuWS5wj9yvLg5yf3JybFpI/21qIA8sQnKVGAMw==", "shasum": "72f26f6c6dc7d1ee8f6e2e451dc97baf89b5cdb8", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.6.2.tgz", "fileCount": 5, "unpackedSize": 117917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHS2yCRA9TVsSAnZWagAACn8QAJu0QAPWnmqahhhTHcJ/\nVoleGf4euKvPCOVKHZulQc2RVcN32oMdZ4Y5GP4wXWaIxg5GnDRsfeBDKIvj\nSbS3yTo1hvH3JrKLhcVJfUdi2Oaw3bFNj1PFuE/CMMmaSrgA1jblmBuhoSBf\ntc2J/Z78ISL/nAJr/uQeBpMdR8i/JotGojO01R+yuv8HLlkZpRX9zo06BlmL\nqFSXS7OtGhWxS+bIkGxRXUEjxwNGIVoEjaie2SE2zYinAN0VvUd3xTkq3hdv\nky9VeS6q8AFTeTEhwiRtGO6G38Cr8/snFEROwbDsy+Ez2Q/CQ6DCh/ur6rIW\ntolq/oaVMtw0/d8Ymw/bOK8DdliXka6AvcKgSoTBgCr0RZWlfXAEC5blBaDW\nw7fspquKhd2oc5ldKka/eXVv7oXI8Ira6SXQhFgkK811tIFHbeMaV1QEIFDV\nqCCqXfcfal72bwoKW/Ok2u8R/tapum75hPrAO1t2f30Wj0Q6qfKPHrKU8YQt\nK6V3h8yw6rW8sFBXmA5mBbetr5TWrCb9F0aJDUj/i7B87W9jUIUCqvLApY0Q\nRhZl4fpQVeCaj8Ogl3oVmHK2oh6jWWXgnrb+e2d8fRB6La4dxK8FC6TLiwKi\nOdEAChHvU3OSQelEiLbN5KyBdWUKUEBD7MPYCKg6paCVhA/68NJqeKU8iK4P\nh2u6\r\n=F7jt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB/OSoU6ycY/QnGE8ONgubao7dN+1xEEgKDpST+lB+YBAiEAjthIzy9Tpy+BlDecI68XgWGdtmunFQkFz9hDuZgeB3E="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.6.2_1595747762148_0.13817701483254696"}, "_hasShrinkwrap": false}, "1.6.3": {"name": "twin.macro", "version": "1.6.3", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.5.2", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "158594503d561e05dbb728c6dc4667bfbce7b257", "_id": "twin.macro@1.6.3", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-BAaDGwcbKjVDtWNgrgkKbHMMSFhkEyDi2VcWsLE5zvQspPVW6MCDMZfRXXQ21Oo529ZrxSjYf7Qw8Xu8VPmctA==", "shasum": "dfd1faced1cf14fedef737b8cef35a4dcb49308a", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.6.3.tgz", "fileCount": 5, "unpackedSize": 118051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHqmFCRA9TVsSAnZWagAATmQP/2LORizy7T1DRPsgEDx1\nz/SELbH/JWyQxWTSMkmUeIRdhtbSjXjs2NpZDiSB+9q/Ec9e0oZAuy/HJLsJ\nkvHVlZ+W0YIqWxVUWDXExuNetNQAGMqfp8XCrTJ+bdbr2po3Qti8fSyDBtD1\nZFVQTvWGcYLA4XLEtG3LWdr69b8aZfqOuS3swjIE6mGCWblVKgVFaIDXn4YE\nyx3neHUi3+tnb4cHHXmlqd8di1R9eyhjtRx3b1gdRMUKHzIbltTRnX+k8nKe\n84Kv66Edxu1mUxnwkX1TBKLUmrjHKaovSNmHJs+VlfcYpwovPrHKRKG6L57C\n2ohb5tAfSDSQRKZM81XdAAa8K6jP4vceqUZ5ZiAJE2+YF5duckyt9uIaQZOg\nEIWHbQdNvdg4z4yZPxX8no6R5m5tvPcEx/ifgy1yvJrDF7iUHnuaFeQguWHQ\nW0hvzzywb1ng8soiV4K/7RaZEdHm0Ygy4bqdyUV4iT0Hg8k7DsI21lxXoCdE\n5MsptqjzwtnCDLVDp34+leLgdGHB1rIjlyhAdXFeE8dQtEO1zTPVuOJo73Ws\nzxyhbq8AxZPmCIO041rQfetXV6Fr/n6l4faIFRaysbDUxXk18WzOmqejyyGq\nItGy22qoc6xVJC3wC3SCE/HxOvDDlczaVwkRHuFTlAwoLC9B8B/71GEyoZFk\nF4h2\r\n=pUA1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGcUHxP5l7KlEWr6XndMelJbrFzSvYx6LDL+UYi10QfJAiBL3Xpp9ws3AMulTdPnh4pq1WEFQQ19QsaXu7A8tCXDEA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.6.3_1595844997042_0.3644277092002284"}, "_hasShrinkwrap": false}, "1.7.0": {"name": "twin.macro", "version": "1.7.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.6.0", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "37c5e5c87dd9b783950db0f6e193b7da6e01c77c", "_id": "twin.macro@1.7.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-hnfbPkWlRXi/oc79aKDHwpxHzejx9kEyHylFOYpN8+IyiUiquwBG37TWT5Rv4qX7Tf8aqnZsSQKEOR63JTd23Q==", "shasum": "0b6a51faea14dee5c70c71cb0ee5c1860d285f5c", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.7.0.tgz", "fileCount": 5, "unpackedSize": 119829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfILveCRA9TVsSAnZWagAAv9UP/AnO9KAUQ1TjxUY+Vdff\n2BAkSuRxxm0K1Yd3inBA058Azt+8qd51G7g/t2fszVlibcCkCtNXQRkzOHT7\nf/0s7Do0W78SCjzOYjHJqP/eddXPcfAvlR+2ZNdIzrjGDGz9WjfI+jfDMpnb\n+le8ONFLv9KQzlVxmelRJqjE+Yhs8cFpX4ccTt5j/nSXc4i+PkaLD/+ambzi\neiqieJHw8hENf06yRtbzh5HHKZ1wQkuzt7CEUiB73YhjU/6WagrN7W1tB4Cz\npEmRYcDr5YJwksl9NGrLYd912U5e+P9YodKL3CFxean3bLZ09SCNhjL7Q1qd\nLakH4FyHlsW7v6SJ8rHsmn3Wy6j/s7PLASBDgy3u0Mzantfjd238OErNzqXy\nGcqxncmzmZLiiq9rAlqB+fpG7lVCgaqE2/S78yCm5lqeEbWI1rwWlb4JCA9B\nmscjHto6+0A6fGmB4eoMHjVmnGUmFyj6WBx4+1Kfr7xhQm1AdTbghYhxqJ3n\nvbjUZXfTR4MbX5Ojvzpb8QjCPHk8cF+XXrN/oVc6pvCjzF0b4hQ193VSd2Yy\nOEcm0ce9+zOdqV6dzz/pfbOHAJQsTO7VO1UlA/epM8TCjd7FPdb1cCwS8akd\nV75RbOXDsp0PIQafhxgZ2tnzd78nPIb+TGbMfMfYqTKxja25Aupviw9mKAY/\na5Tq\r\n=Rqnf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMxXagBJhlRDCCOm2aDGYHz2urLT7cCjdz7oRFs5P9gwIhANUbrSgryV714vV/6NBckTPG+4DK5VKZiMvMOq9GmjML"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.7.0_1595980765833_0.6548173015438861"}, "_hasShrinkwrap": false}, "1.8.0": {"name": "twin.macro", "version": "1.8.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.7.5", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "60f88c80fe9f75152afcb0814a8e8c9d8ca2eb7e", "_id": "twin.macro@1.8.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-60nHBjZ3xl2tdYSkjc0xleKIbuXST7LwbgyEtJD6Q877DCOUtuKIr+AOdi+fwjPfG0br+3bzLyrdGNHn8MI3Ng==", "shasum": "349f0c5186a108bd4f557169544f466a21066495", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.8.0.tgz", "fileCount": 5, "unpackedSize": 124677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSv9uCRA9TVsSAnZWagAAVzwP/2ytKrCQmU/NDGDlL8g7\n49fq5vaqHVIeft/0avvQiTIzib7WkCXPL0dPNvUITNk4yjhDMQUJx5b2fvu9\naULM437frAaiI6K24Z47NV49laOhnKCoJC9HlCm/Y5NBshOTy0oJQ6oXSSap\nevLbkaxq1TEZ6O3emcj4hS7eEShh7FN4OhE2Qh/9MECB8oRhdvZeG/SgWZFp\n6Tro9EdROH7Io6pgywlzpUhbS7mtX1vBqXew5piHdYKv/qjprKGZNMVWN6Gb\npNde0CV92QPTbzU2jtpC6FrhJMn9smW17psQyf/XNiVjpx07ktRM/93C/TRD\nuNutGjboURjCUUwPEQ5Pm7+s8if2MfemsrkKu0Qu1W9dBLoSXuw6Y+ddev2m\nl1mszXr2KJf2YsoBL+gF5JxRp6N5MM9SRKOef4WqlWQ//kEsGbJi0zRd3GH0\n9eJ1NJKZNyOOlTn+4EUKCSmU4YNP3riWRcASAn0WF1ZfYGiFpzjcUc6V7NG2\n4/JiCJe+/Jeo6q6x/UCScHXCNqS5EvdWE4oMlyr7un/FBKLtyhoP6p2Xc90V\nYHACKpSRVPxMj1Dz3v+iAbplCJaL2RBoMPxbBCKFvK6keGvszPkaSZv/s1Hv\nyuRfIKOzVl2ovVenKuKsD+ntsVSGcLktN4DSawNm6y0Aeg+MwBoMvzDRTVNn\nDp76\r\n=jssR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDm6Id+O+WGI6q+n2sSwuJu+RQBBL71oY7ec1ZcWAacAiEA4Y38Nb11wQmJ4FMWFJwKLNa1viS5jeEjEL6vbQ2mpwo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.8.0_1598750574166_0.3098547498256723"}, "_hasShrinkwrap": false}, "1.8.1": {"name": "twin.macro", "version": "1.8.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.7.5", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "05857d45fb63c8c443ca92112ec74f1861643959", "_id": "twin.macro@1.8.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-xejgYBx5QHfsuDg9Ci1PlpAmB+0m1MDQd/EXw0EwqRk2whe7ZVgbKq9LahPKkQbHl6gOZuSc46cWn2qz+DgjZg==", "shasum": "b3e1ef2a38db04a440acf0c49c98c77265302c8e", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.8.1.tgz", "fileCount": 5, "unpackedSize": 124761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUj1mCRA9TVsSAnZWagAAGW0P/2LecofqTnbGOM+GOfIc\nVLEqYm/N3PVY8PqNheMYjnamg/errN7xc5YVhVOaMfixVd1wAWa/3RhF7PcJ\nJHYnrCi6AvVMr0VnfQ1hFoWwZrrVfhtJA7VdebI4aHMJcZ20nHtvhjbmOUb/\nfel93AmFVWT0K0te4ktEKEkJhD3OovNOPYyVPVlZvP0DC7DYB3K5VWoY7Izw\ngEfetJG8+7iIzULIADgv3w7b73Q5GvtgrH5VxBtKVdyoijZ10iAG3xL0Sx+O\nGLb6LVQrvya65zM/GN/Z6cQvgfs0OEqd/RoZ3DXfAIkz6svGDu15Ztc3IDD3\nYJ1Vw5A98aFwKCaPov823BKclX0JPgX5k45ARuY9x6oy2Hz/eEMJcf/ozUh6\nAYmVWXgpvGWCBCtz4hJB447t76DPPc/EDvcKFL8hgk48/NvokzCiebc+GHSW\n/p/DSGthwEEfa3C+B9GIom9Y5VZfArFp6aFLgqZ3e163FeP+Lwlk41zwCkJp\nkZOkQonlJZbwPwOyB0Qumh2jWiPJ2afzGzC/fo+YRowEiuHx8KgW8fwqA7dg\nXqFhYERvaIaCi8VZbzUFzFtqZkzAun5tgGnWDrsdTiCjDo6qhIeUp1+vthpG\nMy4RfKwji/j6GsMUbEg6jTQtO2Cy0K5wFaxH/8MQa96kvJ02FADYvweXIgFm\nKmo8\r\n=zZrN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYeix//4Xt6RZbkqHzYlfeMPnJCX5pxmDYBezHSxREcQIhAK077HSssUGGl3vGzgy5ZaFNxUcSTJarN619FLN6gvlT"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.8.1_1599225189945_0.9617893952786725"}, "_hasShrinkwrap": false}, "1.9.0": {"name": "twin.macro", "version": "1.9.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.8.8", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "640d06ddab5f66f4f14b3855cde9b0250cd189dd", "_id": "twin.macro@1.9.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-7KnO8/mkyDwmLpNeDf8WfpB6HjkGGylstwZFfTOUkoSH/Uu1/RpLTxxtDolrlocG/uH4aZ2WlBprOTpe7VaMaw==", "shasum": "a55c7168014e1bf7fd3f6f138f6f34cd50e254f8", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.9.0.tgz", "fileCount": 5, "unpackedSize": 132900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXd9sCRA9TVsSAnZWagAAtBIP/iW0e4lQqB2ecSfWpF9d\n6J09aDT02GM+Pk1vrqP8Ep6OB/FLJt9YVM9X8CzGrM0rZpqWzhKjyuvnT41q\nm9/an9U63q9s40zTQst0grLlj4XyFMTVNy0uW8+T4B7PYWmLSnGKkjylVWna\nZM7Tkxf2npiQ7TKupzxCOshdMY+0iBVD7QHyo0ryZi9ofEv/0I22tzUItTQs\nyjNFLWRjGdEWhqOMSjTV1ErhcUacFRjXll04KMPdY+wqK35I9b9B8CRqvO2o\ndaZHtyjgzP+TCIyb5fPSZJy0OGvf2p7RmVB+xFxgLXD3UPk/nT+KKz3ZF3uL\nW65gLxCwGwI20jr/XaJQkImhd6a+eaFBJW8QBublm+GrpLX+QeSSDISkQgmh\nP8UQBX6dHMVIz/neDJcVcVee5HYhjlj5ow8XOHqECKHLJdbTyQTK+9stijoL\nm4HFH8Ur0x1jgzzfdTDGQRCBHQFbS7Cs/ulJueuRr/TZO3oPR8s076FfgXIy\niFf1dXi1btaZCma9wcoUoP5zE3Zl4pTfqakLkRq/DkZQ4kLaQj05Kpex+qHf\nLQ6Yu7K0KnAPYD627zY4eXN22jh4gMeBVSK9ngGA7tGNBJ8ugAnOsvQu7YmK\nAoJC5XzIWz+bOcxYYTlM13qa5wopDQpUOVgfxcyRCkCvTM2oHu5lnTap9nVR\nIxx+\r\n=X0wQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZflQ0KRiDSwq0vmHyyFlx+71zKsIhdqGH3IB4ZrkmJQIgTYF+ItQ8gTacwKPUDkTZhTlNaCw5K2wXDyIkW4NtV90="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.9.0_1599987563811_0.0542265384876266"}, "_hasShrinkwrap": false}, "1.10.0": {"name": "twin.macro", "version": "1.10.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.8.8", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "0b568e18bf472bdb3f979882c1961fb714bc1b0d", "_id": "twin.macro@1.10.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-+K9xvBvlx7iQ+CRatqNO/3VVV2+D+rbxexViKFtkjlICd+7A9hp5/8IOQ3SUPTQp80Ouist3Zcs/89quSLaoZg==", "shasum": "b13d0934457d4cae54f1504a3a2ed744ed603960", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.10.0.tgz", "fileCount": 5, "unpackedSize": 141768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbJ3lCRA9TVsSAnZWagAAu8YP/Ri4Vbd+RjUIpO9y3ScA\nRIfaM2fLlKejCYRBW7grIeotNR+vzkBl+36M4TlO11H3EdJnlrVQDdayU48a\nWSWxrJtM3dbobsfkdZAWTG4KZHtUKlkFbx/2b3epnODEkh0JBjfZY/SBRkjw\nW3G7/GO1Xso7OoYRYlTARVmroy7SU4F6D7/wDINVbpsO3+JO7EsjuyrcbZJv\nfqxE3RwkJNYouprLcJ8EFT3Af7zYZqfxhvsBQdUE9yv0MRqpc7tLrKkF987j\nqpRTW+RFfR75qHCsS0e5/O1dhAVqoZmEEFj1/vGiLwavuCM3b6/LXxc8Una1\nKlZsuehLQHWMBt7hG7sPnAHvEFID24XIlAM7LEv98Ra3fuXbd8ybxbgDmsWg\nMYYo3aQu9m12ZLSJPvdXVziqDG4mTi4vYz8HRTRbIKKGuBfpgzag+NwTc2d7\nzBT+A22iGFupbHqMBYF+epLrwWd1Xq0kT1eud1aLKK23gnTKRoKWLDQO+T8F\nl50jWNIH4/fsyRVHMgVHIaMWyH8n1OPUnw6J97jP27Ly//ktDa/hUvvqls/z\nUxOMj8uH+jOD9xBFi9ELKaPDgB1aQu0mUefBrdvtifuBDY3Sxs4xZEHzYO+R\nahFtfZS7nPYZffJvZmhlkU2TK95yTS3eJs2f6apddHU97gSAKY+4EbZOvMWh\ndPGh\r\n=Q0Sq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgL8gBl9cb7R2LTJMAciffDkUiwtQiBnQNkvWzmCR2tQIgPYndipqxnv43H3VlAwHvFM0NLCVv3asGis4AhBQpLkA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.10.0_1600953829039_0.5388026157131014"}, "_hasShrinkwrap": false}, "1.11.0": {"name": "twin.macro", "version": "1.11.0", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.9.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "d04065cc7209ae427b6e4767bc02e2f7a0e3eb07", "_id": "twin.macro@1.11.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-a9RHMqDpD+Q4/SJOo0cgjlHhAH23h2Hx/HAcjNMTx1kyb/hokb4Yvhc4+KVj4jcFyNM4P+KHMuQ6tA4KMD4XJg==", "shasum": "0ed352262741e7b53fdc117fd2a66f2dddfe613c", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.11.0.tgz", "fileCount": 5, "unpackedSize": 143710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfi3CMCRA9TVsSAnZWagAAnOIQAJERjDfAg+YN2+LOuj7Z\nxNKS3AkBHWCV9DfxcCVbAtSoKNx6qsy2MOyNBX4blMSfUV5y3bYBiSxHfU5S\nqGu8VE+xZISWMtX88f+JwOsYz4ZQ1uix+n5cwcOSu93u1L4aryspsJ7hgOoL\nLgPv5fX9Iln9BosCDg3xXTu3Uz5fMuWmCecZSIEcgsa8Z97y6cLP8f1t74qr\n3JTfGwQG895FOqFVX0juvjQDUBCoy8wTjgquUqRH2ll7XZW3Bu8b8sJ/gz9X\n6mspaCBxKIFG6HNJx1N0nTPCY019ueV/sRuv5E2ke8hKa91aWVp3yRoQ3R0x\n8vS/ZA61jn9hAbPT/LoH89yiqu+4/gieBMN4u2T7mrY9ibKIdZOK9sfRA5bU\nxep6zyf1+JWtcE+1DZC7V5VYSgwlgnE4K817X9SItvnC75RuRFysieGZQBLp\n6+X3SvRvzbZWr5Glyir39QhFhTPBY2jjL7ar9KJiIYxaQqDHhLsGVtJQrLrC\n72qx52jvrcOhzIuIjeyJSowg/eXWASg9NdUkYlsud7ExQzi5LxdxvhUpaoRe\nhPP/Zq8Uj6EVGUyiY7xVs019B4iJmm8i2qamE79ZrB/8rpkjs80L6xytlOOc\nibvjGGTj/xRB+K8xvjTZRbCzQHyi/FcKmxQDH/1kmsIiKvUabnXQTvjitmgL\njTqq\r\n=+pZ0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgDgj32giZ/6a2qox1blDTJ8GRymnVeC3R8nuytSIyUQIhAOcijidIrRqHWo0V+p2GH7cl6WYyGippsBqo19oLiRIA"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.11.0_1602973836349_0.0931011064305316"}, "_hasShrinkwrap": false}, "1.11.1": {"name": "twin.macro", "version": "1.11.1", "description": "Use Tailwind classes within css-in-js libraries", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.9.4", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "6e186f2a0301a155a545492273774c7b862adf56", "_id": "twin.macro@1.11.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-QSCp/JaDD2XjnkwHOd/fjmFF7us80YegPZw3wQofR42vnKyfq0GvpUeuxHZRvNnsgQq/d5AsE+cFc+1s0Da2Mg==", "shasum": "dbad8aa9c50916d8001ae56ec3388ac1bcd2cfc0", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.11.1.tgz", "fileCount": 5, "unpackedSize": 143710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflotGCRA9TVsSAnZWagAAO98P/3TapdB1xG5LqU8clj2Y\njSFqaTlqG26iwh+Q8EmhAWvpqUjfHcS7ljz76kMRGLV/lZ+SL9fi73TsWWau\nkK2WO2GLsOWKwu4PlzigKvOwePnYUqWcZVsy3K/3pHfBRiqmEJ2bPiSqhLDp\nFhWSnoPwHtYsOt1J/vsaLFbcTETRnWo7HN8nPNYMpKPOAqww6J57FA+mB0FX\nrXiuA56KNih96ZDWpuCDnTqZuARKA20a9EpL6Xsh4Hxqx2gLxFdwyFUqaXWq\nE/mNSapsZmrD7TMrhQVs0kPYalzxVNbiFqRwx617pUwqmdom4q6fIDxrGF7T\nk8aPT+EyNPyVjcKezoSsVDfUaxz9wgkX3aoiJ3lLGnX+YdoCE+LSy1jqcWdH\nSlHJ6FtTeNehFyuhKfb/6SK6y0HxgyAEH/wbUOmoP7z+V8mXxRvaxeThAQZt\n+QxI7NON6KkttjB0kXnCuoBPR1Zy/EibUJsUKhhutBAbPHmRYjj4u/Xx9VuF\nzvpZmOV5ycSDWcINOnDe93OWTd1JmVsCAanGAtsUA5bM/b2TqbKHzPoU5zTn\nizpkEk9cILjcLZUoFpVNcYjCRag9y1hWFxReH1K9jwbGQLmLf8oxtIDH21mc\ny81r/h4rOdqAfj5DMzUaFomGWxazYJPBP+mwMD4zmncfdtougm98tMdf0mcg\na906\r\n=d7m4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDZ/QYRzhyz4oPPl+KWr2x+P721pyKRFvQA4ser71zhCAiEArymZxQPVHCQLbIJ6uk7knI3drUDZAKwoKBP7VE4asKI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.11.1_1603701574239_0.14671335865071455"}, "_hasShrinkwrap": false}, "1.12.0": {"name": "twin.macro", "version": "1.12.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.9.6", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "68ebee66eff7630bda97a9a8f16bddbd932fe3b8", "_id": "twin.macro@1.12.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-KGRed9d42MkZDIS4wnCvUp5PsPnRx3eaMpvTAOQTz7/K7ArELhg/+SrbJxniQWh6QtRm7/LotlzRfvVW3/qGHQ==", "shasum": "3917743cee5e0693df9679ab182aba4de962be45", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.12.0.tgz", "fileCount": 5, "unpackedSize": 147588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmUnTCRA9TVsSAnZWagAAQS8P+QDm2rQ24IK9jvm7bvp5\nHOQMaogl4KF9ki7MPqi+ic2UL6V0scgvNLl8uOvgfks+O8xK/NYz7y+RSI/G\nUFQGs+dAWOvhov1rWJZeq0bXM1H7ECet5E3U9lbed24dkUdBSoQcz7rgm/nz\nMzueo8anAlRkaV6Wl4edC/w7ZWJb3uhZWMzieLtPFb+dS0giVd6STDdwu79P\nr+eAmG8oobzHrl3iie3fa9t9v1nYRSLRQNEqxcEKwE4ORVWweWsu3ouTrBIo\nWH4dx4T1jfIYmAhWu3yPI1wk7J0sNfyf79c5GQSUAPrNTATip4cjl449gJGo\nwCJX3C6uSDH9MjMHpGEpxmm86Tau2a07HmL1NPzCa1EmgCzF/WKv9HBzxT+L\nW5DDiJuYURAM8/flPJjCI+g0Vfj9nBPvI2h1vWY8AoPn5cFZ+c2l5cIm2bw6\nzptguPxN4EyT9+x+mi0A9HM/JrJDuWZzBgiBewVNDTPKkyMLSC/fQp/5KXjV\nj4TIMTNzFMC2au5K1bSnmmR2OIdZ1R+eWpOHvNFoCMQ3g1He47+SxGub/I9S\nRo8pnGjujIKWb1/DLS4MZTJvCkaZb42X0X/zXQU9cli0O3Aoj5R+ZnmrGl2Y\nyUpjYRuf7D9e/5EdWQBPDZD/ze/93pCwJtYw7TdbDOOjd98sB8tDA+Hdlo7V\n0IU5\r\n=OjaT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEJWf2pM6V9fZl2+0Da9xVEAD4Pv79t6SHgxSNr6IfdAIhAM8cSwr/x3a+8LMyrzqKE4VsIOY1ol3FzThfsKbBkyyM"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.12.0_1603881426742_0.522015794326244"}, "_hasShrinkwrap": false}, "1.12.1": {"name": "twin.macro", "version": "1.12.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.10.2", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.2", "dlv": "^1.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "string-similarity": "^4.0.1", "tailwindcss": "^1.9.6", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@tailwindcss/typography": "^0.2.0", "@types/react": "^16.9.34", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^2.29.0", "@typescript-eslint/parser": "^2.29.0", "babel-plugin-tester": "^9.2.0", "eslint": "^6.8.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.11.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-unicorn": "^18.0.1", "glob-all": "^3.2.1", "husky": "^4.2.5", "jest": "^25.4.0", "lint-staged": "^10.2.9", "microbundle": "^0.11.0", "nodemon": "^2.0.3", "prettier": "^2.0.5", "react": "^16.13.1", "styled-components": "^5.1.1", "tailwindcss-typography": "^3.1.0", "typescript": "^3.9.5"}, "gitHead": "6337a628385ca8d3c942966cfd81a4e5b554f71e", "_id": "twin.macro@1.12.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-QCzUhIyXYHfZL/lABPpeL7V68/TWS6gaf+5a9Y8WRHXcc/cC4lwO4ZjDo7M+LC25Fw2/EJQWCkvUwylx8xjKdQ==", "shasum": "1063b76133b3656b94358cceb04716937431bb39", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-1.12.1.tgz", "fileCount": 5, "unpackedSize": 148401, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsQZgCRA9TVsSAnZWagAAfqAP/3gxRKbkvfgUaHHaoveD\nDIBlNEZw4khwiVKP4Zf4ZUT0doZiPtNg33HtQfucX2ZTx7u6l9r3PZChKLJJ\niPzpQx8gPou4uM3frHmW1/EnZHDuUeyFV4qGnzmDVvVQZMH+S0yLBKxHoJ0M\nG2D2/SgHTMeEOr9EuhyDnceYotoLIHeF1TQTlnnt9MgoO12NC8Qp1UVdbUcu\nGKbZo1+0r7DhnQWFbeqzSkTIVviOdreqMmtA//ubEXqFpfT26/6dHDLvy6wZ\nR7MXlGMbq2z3Zl0cQbiz+5dBNBM4+hfZIH+NjPtGEJB2tWtrXUlOz+qiwmj6\nB2BCUb3SnSKbclAGxLA177znpRqR/i69wcoQryzNGfspFeRXLwBN2W7Sqwnp\nPYNm4DWFNPcfFdShMkr7b6Fp+WkTT7O1bTvt1tsikZnBJIl1vlbcQeyoAvOb\naaSSJ0ed4uoRM7etFCuntXS4Y/JxE5ziWDgGN2FL1o6d4UpN2S4d2tmcbdsa\njdmmLW96zRozywt/fRfs4+m7iasgTdwVwt/KpoKL4lv/tU6CbQI0847Wf5B1\ndWwwfSe1aLRPDkoBbMLjecQTUqAdng/xhqQf7ul+3uLTc7fni08/gvJqyfxy\nJ8my20aR+NHuaQU3unutZthIb4i1/A8MPAMQrfppMf4Hh5QxH+I2UnkTAXmH\nSgbC\r\n=nVpf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGKwQk/Hk/qXfrkkRlsN6U+sXRr4o/VhfvWVJSBYwYvAiBhY+b8nDQLF/QFwY6Ub6BDzvD2fNnTx24MVQ8E5hDkzQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_1.12.1_1605437023939_0.009254143465537323"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "twin.macro", "version": "2.0.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "1292057cb309758cf76864ed5d3ba953d785e427", "_id": "twin.macro@2.0.0", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-pit1eA79GTWfVmlxSf+rJXIdhE5gnKi5gTUkoM3TED7TayvEYb/zULRWbRjI4dtqgCcJDgbvseNBiWJTDf9wwQ==", "shasum": "b0d7df891707d9152692649ce2b577b55f320747", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.0.tgz", "fileCount": 5, "unpackedSize": 156793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwxjyCRA9TVsSAnZWagAAkZwQAJDjIFghUh1fFGUa+pv1\nVOtkZTG0XigNdQ5dHcFAHnV1+FD/eaVJ1YpjXiYC52xZQi6xvbzwUHM2t8TI\n5bVwB2jG/AGBN34/Y7C8G/BK91i6s6CcJ40qrhAC6OpbZQ+V8Rm6gq6r8SMo\nUzrLmCWpFsBlowiXPF3cXut/zxx4eNKQOT6D6+KWfKEHAwFHxLWU1eNoZysC\nEzZIDvrM9qz4VVhXQrNW1L3TdiYTCsBoxwCH6yEQ+WXIU3MZom1/sP56EO3N\nl39Xa0z7XRaB8gsiTybfv4f2QCfrO6T8up0sKh4x5Lto204SiOAqtOYoH52o\nBTQgRWDppVc+EOrpHPRLRI4VX1Zt6Lec3fnxwQQP7v2uTmAJicmcw5bFThVm\njfQzSmA7ObQcFMfvabOEWjtWfJ3ORNu+Bx8d7ukimlAc9bRLI6lNw4eohQIe\n53ltvhk91soii0ZWq1lT1sE4JLz4tpW9DHo4VxvZGTuyNQqdi0fXIMxNh/No\nqQlNN5WL+vlXAqoyvItaNCirOe2OOEJb1Yu1MHY2uWTqlsX+3i5eEoIxjKV3\nLOetDF8+yKT1aBKKAML/GCUXUoaAylqzoL/1b26hpAgZxDMWGMvlnDU3xekb\nI1ydmukM+/p+xs3FK73tNNyLw/ExdutsK1XLGH1vYDyR10nP/rf7o8vSvLqL\nD1z0\r\n=BVBF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCk6XEgutmLDM6pSdbtHCaSHQek8TbTU8C6yFyYh0tZ9gIhAPjyisEf9tT65iErfEJLIudtFXdz6eHyQpXxfsXk67S5"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.0_1606621426272_0.7547248029163938"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "twin.macro", "version": "2.0.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "cc716f416dc279f6edcc8698ada0ad0d049684fc", "_id": "twin.macro@2.0.1", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-yX/+8Nlf2fNKIKs2G8aY9dUnyvnbQNY5AV7b4BlBDv76cD/sKtZx4ev4z2S8DINImpVstWYv1WHecqiZhxPtYw==", "shasum": "2d517a1d2b9e3aa706b68bcb9cc143b01852dbb4", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.1.tgz", "fileCount": 5, "unpackedSize": 156746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw3vSCRA9TVsSAnZWagAA+F4P/iFcoMzUavLVZ6jmuSzZ\ngCTgG4gHokIcIaUn3+dJAfbH3nL0n2D2pz22Ln/vzlVxWH3xm33MhcdE2HDB\ngb9mTMuZ9XySoebk8edqbKL+2nXY8aWZwwLZOf/OYwUZC176gVcUGg3sMIwy\nj0S/aV+ezCiqIKN5a5KbPQKLrcPgPu34BqwNLnIkRX+MbyEWNYdoshyxYhEG\nCStPbTCFRS/JYy+nmLUiBQGmTfGKn2v7s5HkO621qgu80MHQrOcagYchcp62\n7CoAnJ7BZiIr94uYO/r8mk1Fv8n3wy4F3JXOy8K7c571ifP6cLZBnppE0osI\nAUneF4YPqsehrf6dcodBjhtTHSI04+IlY7tG9guLzjuRePlF8ueDocCTmXCX\njoy19h48aFSfudEb/9ISQRaq1wL48g4tkUJpyrUUfGSsxP4TxkBmMgzYsUYP\nYr6I84UTLcNqn1tnbAmuxoPmep73QF9lJKqybG+H/FkZm+/3awCz1AGs2bFD\nb0btoTwdb7n/Yd7uFzS6wBmlZAdVuSduOott38o8+EpE157R9Nt1bXzXQHhf\nWWuDtA/sPYCckMaZvi7XYDNtElfIV5TyThIkrRXFXx6MHjMn07iXLYKQL+Jw\nAvLyNhoysKtSafzkxQNaJsqv6AjG7OBCm1gMLMz+VKoZEbeowpkxONQhtxup\nY32V\r\n=KodM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdBuMryOuVvCEvZ/Dpvt7wNSMFALddHwWgU7cfqfpSRgIgUpkobxQZC6lMR0fS68uPGHboJlYkVcBoL6+/j32cm2M="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.1_1606646737808_0.3301362763979374"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "twin.macro", "version": "2.0.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "d7c812377dd5503e4de1ec09d15c5d152adfc25d", "_id": "twin.macro@2.0.2", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-sy6I/YokG80plnbYrsIJhkQeSJsKjxoTWouGrWkn+zvSG4Kq5EJ92wxDUpU6pgdCa1Z+O9mfT0v8YokKIOD1TA==", "shasum": "6c7a497334d505bf5bd64f0c47ea1c40e06da466", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.2.tgz", "fileCount": 5, "unpackedSize": 156748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw4+WCRA9TVsSAnZWagAAwL0QAJOnhn9+2jYZ0LAiRC5b\nCY2/uWExNUfoCr2zRCi68wNV9y/BYNag9NAtOW/y9NiT42ku/4AQ4PVN4jEY\n4NRjXe0DD8FT1XdCybybikulG//BtvIGK6tUn2CVVDJWGJnjNHl4vmUgALMD\n0qbjVlHZUUnax3cjl+4QLBi6KAivsXjmf7ez+6Wm6TwdlqrJUeebr1aXfm3Y\nSDRk015bEICvS3jlZlb/WC8m0l1wo0hBp5gL/wZ1PmmFGslpidnsmzz/JNnE\nWMdFnglgiva0VMlbQNs5fDTMdK6jEdoXHSPzL4JhnzFVadvRrsWHiMlZRz22\n4ShQ/ZzVf4ZVWOXcA3aoSE1oR/aNTrelhtuoNWvJtudNO/MSx4O7N5iHxu+7\nKc9KRQMy+HDbeSGtC4bkEPb4T+tKLpNCXJpidJKpKThggJJ3bylbEKdKreuL\nl4+4kxOVEdNxMV/ewKBUR23GErOzjSNESJfWZtHWF3lT+vzvAXd3G6JpXfbp\nEwwSDWN/uA/GRW3cfO0f1TUFQ/oAjmfWwNQqb9fLEdCQm0iCjL5GeSU2MOa0\n7oyGNISA8fH5IdmFvO0BWyB/DBAfdURieSA4ecH+cW43X85F+dX9KBGv7LLN\n7mqrYrJ9Jlps1t/IZM65vGrld2FkKIyh6GosdEkqw+bgCbVRcD/Wao2TIahz\nEloH\r\n=UczT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqhv7QU8UEvAaWGuF5oQy3LzTb1QAplRLEEANtBojWKwIgCv1LAez8pgVwMS5y7k0pXvAX3Jx0WXYOBDKMApnJT6w="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.2_1606651798172_0.9900804399478906"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "twin.macro", "version": "2.0.3", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "peerDependencies": {"@types/react": "^16.9.34"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "55043eb4930a6e1774d8ae2ec20cceacf6c60da9", "_id": "twin.macro@2.0.3", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-zH5JEc+SxW/siMcThbxyXwMQ5U2otH7hNqEUv8ijq9Bwq2CsES36W0p+sqj17ytI/WxJT7cXQ75VeTvvDczIgg==", "shasum": "fb87fa5153b6df2ae2acfbbd54ba109614bd49ce", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.3.tgz", "fileCount": 5, "unpackedSize": 156610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxAglCRA9TVsSAnZWagAA1LgP/jLXVCJkvx6dt16XkPhu\nAvH1Lqgl+lcd8+dsnKtQXDuSQWFkSebebkbi8bb/FU0MtA6pSXx7xveueqYS\nMtKeEVPuVXWBy8a/aiAcv1IRueVL4XHlzjDgoCE/4A0Vwlg6l/6JZ7XBYYfd\nDV/KCp3ZFP5yDCnUOejga5ecJtftwmAeOVLwzdqhpl1t2aBmIvSoownixsO8\n3igB6eMtnwpgEbDi/f88USFrVLOg2V4NURsTkG+g4NIh3cd/4gml1XLFYEe8\nWsiRu33CIM/+ICK1EMEpZXhKA97xRiMhHrVRXBnFeaL1DISpMXpSJ079kx/g\nkkC+ZZwbkQlkWCBPi84vvFfOmp/naMhJbgXRLdlI7n0w2cLt23npkwgl9Lbr\n2v9gGzEnrbB1lq9rpJBuXVVdw0J8N9uwz0FhILLkC9OQkt12BJRW0Xchi2Ic\nffmPWF/VBkhz/pxsKVbo7GWzI+HZTdzwzX+T2iWJxy5m8djDjY1zZa1kntOR\nvg6HQKV2fHc6nSQU0wLBWY4KWAx+jF1EwTgeSM/ABVtCBoVq5a3e74Zqo1vJ\nnkvWrzfQssKQOJsUIfPHMIJwV/B9Yt8hobcnmpVNb42j2kmVLgnQOyKR3g9/\n71kXBRia4Nmve2NasP06fUPy2tgTw2VXfPlLd+E0fqNiX0jiH2wEV4OFKwg5\nxDhG\r\n=tX18\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSoxsx/AXyM5hk+sI5gYe0RpIIraZiVc4fLRdFdKqpkwIgeci5RjJ5zW+EZVikwdQFqw/OduBwkUbJFnVrgElE+pU="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.3_1606682661420_0.7105272702724761"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "twin.macro", "version": "2.0.4", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "acb0d7351b7001a6935ca090ede952f8eba4a779", "_id": "twin.macro@2.0.4", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-SNA9fnimCtLVUJHr/L3VtgXNY23EZRL0abW/xx0LDf+jGVMDR3ZE4SKm+4eWqzXmLYo6O0QmksULlLA2G6TzAA==", "shasum": "956aad223e858667e63f8fbed80b72cb1f6cdd7d", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.4.tgz", "fileCount": 5, "unpackedSize": 157427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxNSLCRA9TVsSAnZWagAA2EUP/32Ylbmi3rgU/tu4B1zg\nvfIHJz0bM8aXSpf1VkohHUa1lbO+CiwsjXlPVtiw939IBvzu5GqALItetu+V\nX1F9QTZGeJti9BHqizXoW/ysTq5hYqDHqOnpHTp/9sv93+mRySE9KNWcGcdJ\nW/Of13hSM0GEdL7QaNx9xZw7UaRevZOFRza7QOxFR6VJr67goXWtQ+dO5wgE\n1eXCXA0U7CqwRBLYoIgysJ2X8vo/G1S73buoyboXBhMW3D3XZwkYlUfmMg7O\n74JdEQ/UNjIButqiAVt1r+7J2OB0kb3F+Hhe9WaasWQh4Eys53ZvxGHZfanA\n9A7JrGUcpAN/x64KKRfc3hWVRkgmi9OFWz7AEKNFlyDQSP0yK26jU/DSWhYD\nRfR6+xrrRcaaHDvtW8Vf8Ocpvf5XJqoi/yeFjVZ8eeEjmfI1qkbs0KNByH1E\neaL6Wsr/mkim5K8Dxhe/UqTybGjYtHa4X0gzYjpq8UToTvoNMLPZ2RtMGMLq\nxsjjOr7Tj5itMqAUT7EMFl7UYNNQBWXeAA7IXKhB8GHCuTDxDKs/1Xl9UKEs\ny2eQNqTEfR+fLSllm/xDy+t7C/uNVAQiqFzMfwifj4SFaJI8RrnVIIHHB+rA\nxwrst2zhX1ga7nTabt2kHW09hggb4RtxiSYecTqpcmZFa74ipFHZGyfjEOtD\nufZD\r\n=QcGh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBZWMocbihnh2/tvnVVBHaqdeNVQK3rWXVw2nroQmSWVAiEA2GMtyIP3rjLhFy7z5QPDmas29VEzsMH5XPLvsO+Xow8="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.4_1606734986395_0.4196464538234572"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "twin.macro", "version": "2.0.5", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/typography": "^0.3.1", "@types/react": "^16.9.56", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "f754a401f5f45dc1d884b788cc8729d4e20cfb9d", "_id": "twin.macro@2.0.5", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-bEmJI0tu8rELQZOZ/1w+oE+gDvuxhIQVhzhnIqHZWb/kV5AK2f1CY55PvJ4CRsaw/GVY0x4xt8kKM79ZdtFfbQ==", "shasum": "56482c463e973dc51afc0abf0a04e0745184b708", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.5.tgz", "fileCount": 5, "unpackedSize": 157913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzp66CRA9TVsSAnZWagAAut8QAJS+us8sb3i+ieBlcsgo\nStLokMhizzmv5zDAeTKorjydOEA8TuYUF2LFt5m/yXvrRJmbfnIG40LkP8LG\n2Qt/58O0Fso/cw1I2Y/Np5spYAI4vBQV7mB29lJZXhl7ot/uuMEfuWH6dE3q\ng7YRfLRiPgBqq226EWE1dl18eR6v+RBLbIopxr01T6pQdmIlKdSKDYWpp0Vj\nCuDNW75rMMBn+GU52ZEYfTZKY18/U/hMdHjPDktfWVNBc79b6U1flFrEn+8z\nNM12AkT24Rvp6W0/UfhQyfCJfMdP3kDpPvM3qOJKFcEQ0r+YX2psKuIdo7zO\nPltyqZns4efOOxSoLMSKHGBE+PKfCT0eCbh2R916uhwpbzrVUnerxwJhEJfb\nze7zyVI9nIprFD/mH6rvWf5M0jk3MmWlvY9xTMgAkFTY0xv4aBMnBM0cAzrE\nsYvSB4i9OcMnA+b7IC6wDuPAXXOtaFu8eOYwP6lIlG6j5QbOa1guzFD5Rikg\nF3be3oFZPw3LHfteECFisoePnC1mALIXG3wCKlDJExGaghhSeFwZj3j7EXua\nMNlXiGt7GiuCGoIx78kysLQ7kugMVqhyc8k9baKF2v1woIwujcFNCQiAdIoQ\nTbJshLTYK7GYCk2iRre9vEgXisZ7SjB8UCcbgxqdkoPBXuj4El0L7yOOxQW3\nhuMz\r\n=bMpq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0YTStpYt5umly0MNIvJ2vGGBMyGQ2R+ZJ/XwRPtsw5AIhANpfFEQBRpaWXKm2sEVqR4dG5BQtCOPp3cOVVA33D2CJ"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.5_1607376569544_0.23826159840890848"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "twin.macro", "version": "2.0.6", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "8642553b7057586e380f5ed0f92512543943b3a5", "_id": "twin.macro@2.0.6", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-q+o0A6pPoqKiZy8pNoorYMuDOE/ekEEfTthDqKzl17+U67IZFJbW6J0PrnGSxJ37pMlBBwWLpmP8z1pWKeouMQ==", "shasum": "1f48e859426088c8084c54b294e5772b25e07a33", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.6.tgz", "fileCount": 5, "unpackedSize": 159337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0Jl1CRA9TVsSAnZWagAAEEAP/0Nqekem8A9+oRiP3Q4B\nlIkWpI2NTWMZcCoqFyjPClKUiAxUBzfwENIJFqBzxacH3dGBG+BSMe1PH0jG\nOUg7XUEMfkEvTaiVQiU1GTLnzomB++2KXtfWWAbZ96GkLjpbcbVQWUQqOADC\nK82V+mnZNpwxlgVm3c0FgzDNUT+VTMT1OEwTDiiEi3q0kMLQYemlwTNaxPVz\nQ1hcE1UDLxLUKt0qIZcbAlIsYSAAKsjoYHDU7xmVXvxffCl6ALNzeLZVu5qn\nfM97A6OBewiBdeaaQliYe66aMf23vvukqeCsm8f8pNtSf+NUdHwmzGDDel6A\n9e6kTfRNwxpWDnFBf2R/7U3Zfsw0nFNN4O+YVHXyr8swQxQ9azxJnG2AZsHN\nr5qNQdZXfkTQWy2vknlh598OHJ0ns1x78z0cp3jBaL0RyN13ptSrEscKcLfP\nyLOrSfDTWi/iQohrlyjfWEnFRIc3Xu2E0aGusdkhiHZpz6yr7XR9WZG5DuDj\nkrMC5c/ZIyMSXETjy3UwiOyYIdJ6THy5ds+siSWBjTYuaCXvzTmz8+BsQcq4\nAdocZz/8vgZo+DWfFHObDer2c+wRas4bGc86mmzqy2W2/kS2UkiIH5+9e0Gy\n40+jZ7CP2R0CTYo3r538ks0ZChBe3AayVT2ifU5pIwG3NExHWlOC+PQW4ALu\nioVX\r\n=Z5ok\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/FSVHtJHBR/hBb7TG8Q2xCnjirN8z888iPv60J9IaOAIhAKA22PQQcYh6JzXo6WV5EtW0/7gZii7/Kp9cZSPyERQZ"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.6_1607506293204_0.12458494444529467"}, "_hasShrinkwrap": false}, "2.0.7": {"name": "twin.macro", "version": "2.0.7", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "e014f704b6ef0bc3b3d8351613fcd407970950df", "_id": "twin.macro@2.0.7", "_nodeVersion": "11.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-AXLs9WAf3njusUVDMS6EGZOpxatsBokgSF6xTie/2hZ1MVHlDFz2ervL6O1FqiaWtu7Pi9/8HJLJILAmAUwlGg==", "shasum": "b8a6f770c95032e0a532e47143b78ee3c2bbd55b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.7.tgz", "fileCount": 5, "unpackedSize": 159235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0pJOCRA9TVsSAnZWagAAa4EQAJaNenUEvIRc3rV4/gFX\nYDgBNnhZlZBLwThvTrNJ7PX//zoM7IlLl5eLAeQGEPu21gzNAsOTPdzaaRNF\nAJdHe5GjPAYKiv8VqZjrBXkiLI3qEyFFaRRHJ0J0MO0zhmMdOQ/iAI8W4DXF\nLHllUdHaM31/xwCGEojpnlw00xpMSnlsqezBWlMHSy0GX5Qru7+vZDw8Qpkj\nQ2+eMHD2Eufv/Z/x8EWbluAn1DUK7EHUjXWLb4fBUXeBFoNT7h21dhnOHoRu\n6zG6nKavElRzzhtghMyjkQGAlnQ8qOuOUJLBLQC3AFq/Uj3Hh3B96PzP/SRU\n9xMDq2gpjzVL1WvuTagZU6DcGx//qNX5obYUJs9dh7wR/i/C7yZh+zY2rIRJ\nwyLC7kcjiTuRTkDDGdfXTD8USh4vJLq9EmkA3uyOxWfgwWRgnEVXBz+7h7Zb\n1v41YVEng5+DepxJwhv/DHAC58A8k0gO3C62FdzTr9BnTgIPDax29aP1fe0g\nQSdp87yIZPpsXaHL/00o1BIKXt80dcpgZag2TT9QSAupJFmwmhQ551/300pA\nEDUm5iOktygT0lf6YIEKZKIvsLGp79z+u+JgbS6bIzyodAV8J/kz0LL1YAw8\nVfpYomV/6I5no2j8NVtHgXLd26vqwhH4AMsvImNuFwLZ8+zniwCugtCiB1mH\nxShu\r\n=881T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsJEBO++xuMoNhW76nuOgctZ9WzaXHGc/OgdYczhgnwwIgHg8H2QAcR44MG7tBGynplzkr4XGecYn11RMZIG043gQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.7_1607635533923_0.8432886321056978"}, "_hasShrinkwrap": false}, "2.0.8": {"name": "twin.macro", "version": "2.0.8", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "2accaf8be957192048904062d5ee795f1db45101", "_id": "twin.macro@2.0.8", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-5yY9QNv/R0amIH+JaPMqr35wbkJyICvrGAANZmaXZHNMMmRDfqH/XcefrvEw7eHKC3GremEWAh5rVcGXePd3gg==", "shasum": "ac27579051e8c87f3163ecddf04febf731ed45b5", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.0.8.tgz", "fileCount": 5, "unpackedSize": 164384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9ZoUCRA9TVsSAnZWagAA/rsP/30iGndEVhK6bH53t7+h\nTXHRMwzZ5acpH2GeGguOZ/9DjKznvpfKIL/0e8VChSC84JIwDCnRJEIkvTui\nXQBkVftNXnkVBn7ONcWa1vWLQ8U4lajJTqETnyev/ZYkrI8HIwlGBsDvK3nj\nkmdzCNanlXOz8PD7XfpM7RmNQU4aMSsUwcf+yY8eZQpqnzaNvfNqSya1eYh4\nZxKRY3ic/sDW7yZShbj8d10onjrGZKr+6kvHgesbNiTQ6+VOBXZp0M19NHeP\nYkjHTnyEhhL6dzWIPq/kUver7WlmEFAcRd5jIgydGTwlxHEbVXTBq8nelYLs\n2bbMNIDJ71QT0xNNhnedZLPdnDu4XUGy4HsULk2u/R9Apz2BwUXOg1nxFEUF\nADJANwx8yHpIaITmx4v4KcRl+qa3vVLNeJAgUMEsssxv/14vT3CxHCEDGMjj\ngXVNDdWsf4zcsOiKOqGqS2vN7HQB8dgu+cauV1+I70CAKIoQP5AQ2QF8Vqpo\nt3hgBbQVlFwgG8w2TDGFidom0QnCXzqVwKigkL3JEzHz8//qrX+rmjEcViuO\nBRuQxuDWK9gM1UIWl6L7476KGLYW5KdyuoZKo81c1BceFnYAQsCDG1jVvkxr\nitv8MinsPg86S1k8sBGq0I+blIT1x8O8J+03JwrgZ9ez6eQzNRGyh1tXGXlW\nQL9/\r\n=s6hb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOdrsid9X8hO7U6fdrDcJQe8eoN/72YbwVxVw6yoto/AIgbAEOPoOq9Rf0NNfxXgOGpdkdpoT1zUOjwazX+5I8iD8="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.0.8_1609931283944_0.7909457988179416"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "twin.macro", "version": "2.1.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "1194e5e215e9dff041c7ab73e2db35527428896c", "_id": "twin.macro@2.1.0", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-vV73kK2EL3JO8iTkc3GyAJxlDwVUUznu6df6yG04rK7upqbs/oR30CU7mYq8WMrgSJA75Sdbt4yiFBEVA6rHHg==", "shasum": "093e82d349f72c5a7017d9b33d187e5b7323e475", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.1.0.tgz", "fileCount": 5, "unpackedSize": 171529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgB/lvCRA9TVsSAnZWagAAP/MP/i2j1CAGYwp8crZ54OcJ\nNn9BJDjah2mZAS9MNRSoE7dlEvPAXZk3H69ppDt6XznpEOMxnVbkTD7i3Diw\nicsqtmozkh2l2t5UNGRhMIqji22KLA+8NozLuvdICM7pDLFZX4cGJQnx+HsD\n/sLTlHGBGUgpO/ehpz1gSW+ThT1HGlYzjKM3O9zrgo9qs+9fgCiYyAkDoHBy\n5LDMM7mx42LoXGrk5KGAHNV12uT3GjspL2dtXaVOhjNdh8qsvLTaLvJcN5vh\n4etZp1i3ummMZN+v4JpKRH5LMs7IJYqUCeyRZFjCNoU8D1C18YQiUttPDlkv\nCA00ClCJwm10xwP8ffvKRrWxEWXYQW6gThy+C/8EGI8bLnn3cSIiE7BymGlG\n0gmTJ8TMEzZn9UOUg4Ebkmk1AfgwJki9sxybKStO7GZhU+BnA5EmzVmLy02M\ndao9PWwvtGwzVsPWIpXEN9gQSRK2FjOW8Ov1Xyovl/+0XoFza7E7fOYwO5Qz\nl+uu1wk1OCk3Wh9rFP5Z94lZaLh1MXDS7FCEkZ6FtZ7llic+oSa5XEWlcLWF\n8EdVCzSlvs0VrjRzfI5vFJDZWnBdqAjMVql0QskSgMbRFVUuvE19ohsDXjzy\nyHIcWkZ7TQco7JrAmjUExrA3SehAVwEYAlZlKtFw1ZNTB3wHoJzbFKxWWXlY\nrvFt\r\n=Jn70\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBzRW9QGHoDpzco+kMUw5T+ESodkwGWLqLpWRc4bU48RAiEA6aw9fY8Jo2gD0q9hM1JBtLI+Uu1gnOv6J4rNBKAhW94="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.1.0_1611135342568_0.007561904398307506"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "twin.macro", "version": "2.1.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "b0fc308467d69a42366290808f087f31d2d18862", "_id": "twin.macro@2.1.1", "_nodeVersion": "14.6.0", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-luotfMXdMsgdQpRq5yS6oSQEJqvLjM7/amAavWlux1z/yQ6Dh3Y5P56FQJpD+6dcIKRio7AcIE8jx4lrpzkLJQ==", "shasum": "387b64af2094b4f987455e258df0a0584ae9fee4", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.1.1.tgz", "fileCount": 5, "unpackedSize": 171406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDkDvCRA9TVsSAnZWagAAhQsP/2TrS1s3cXNoPaDytl+d\nbnpupyZs1IlxcPvDQMLFN/3WYjlC/iKf6ieBCJ5gprGo1JLUvTjJgd2pzsDo\nS0bGx529psOkqYZR6fcd/L6uCHZcX8r8fQ7G+W+8vrZprtrDzHnKoey5y8Aj\n66uVH6gzzub8g3+aQA9MTO2RBjLwNPQG8rd0PpySI49tjKA232vrTjtAzetT\no1Si9ZKkYsOAelVTBkRq+MYeQFLOMham3CfO5BUZiZRyjgeF04Qm1KJDYlop\n9UVE1Ux9cgxsdofuw63tRVUBL9Y2wizlDotvU7DxvqgsTVTYXn1H+hwvh+JB\nhB5/U81yWR1HylfEyPq3NxGWIrDQgSZAlljYXNDM+mAZr6S+LG9jRPeJJgPK\n0fidRw55fXqzMsM4W4txtDQa9+YEM/dsjMOKFx/Su9pnYA0bW/q2VOQqHNhk\nxMIP6G8mDnfI62+0Hy0VhY2wX6xxpIzuxtcyXKfoKW23zlWdsdT0jS/l/W1T\nl9I0GeovQPwxwOYeh7yH+zyKHJyOa2VOQHnfr7pI1M1yorGjYLTwCTtA/QGy\nD69U047WTrOuS7ezwi1mNQ6Ue7ROoj15wzTU5gkVUfoXlJR047s9X5NuQBIq\nKPEczQaohO1qsPDcq19I+2KDA8pDigFx1rUsj02HA6N002S+S0wAWlsWPGo9\nOLCx\r\n=Lmqd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYTHKD9h/a8mqGA5XN6ai/DAG7iRJBtLNFugGlcPNCSgIhAOF/7di5u3Mv+DyXPJPOkyDVtbMQRtLIQe6D+NMD5hsB"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.1.1_1611546862538_0.6507963781940511"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "twin.macro", "version": "2.2.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "d036997bb10150dd8eaab055a28371cff5f6eae1", "_id": "twin.macro@2.2.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-SBtviYQHAs29ZPGqJz6b2Cu7pNPYLefkB/ukPTpYHLkmb750mGvz7E/hLdbbxft7VpNPWI+l+9MA/uVyfaGPog==", "shasum": "dde81d5fa0a19240d1eed0e7469be3e5564c54c2", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.2.0.tgz", "fileCount": 5, "unpackedSize": 179525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFhZiCRA9TVsSAnZWagAAAvwP/RbS0abZZephCky9QtMV\nH8GPSPrlm+XQFtMN8LTNIVzUxa3UYTSg+Numzzu50wpDqeYY1RdaxGmZeYFq\nMMb1pyQuJ8aAXE5blIZ0nyay5Jp1oJhbmOSu94XIcdvLnTbFGUyMFZSjj+Qw\nt3Lz8P7EWhff5FO4XR40VmBMsOrCNnJmku5bcZz0zu92iJRyusGBiLL9bWu/\ndWesYSo3U5CJJTyVXQxjAdfvR2zkLElT/44wHhEl2IFLyZPsN3ymSE++inNp\njM9EXXjW02Jo8kYtMQjBtI9RxypjK2FL/Ek7vPJklzsWqO8gVoeSDcuBLQO8\nHrEcDBv4On7FOV2FJKHdhcmbbvGwmKUGm46xXw2knW6BPwE2t3WJ6JiVtzgy\nvENq30zrqgWJ/od6dWdU24RE1B8GpR1vMwHiBakcCjWTP3sPugAuoMsrt9eF\nRpYgg4cVsd5zqvBSe3GpJfx9uJtttAYfeh5fOP9i1OsuC00/Mz5bCLU84fqD\n7WtabYHDJezGc/Yci0C6fgPBJzyQ+wYo0PuqwRSqXf6EeKSoB0QDu6civx2z\nYq4fWhhoHTuZ+++jSm4CctgE9Rj1yqtQ4nvhfibzrrtsGk2FZvshbTgUr+KH\nYroYQqr7uQlL8gpv+LhS2ZiuCkPzna1kcTccRv40IapF5+OTxd52ElHeDwAw\nqAyc\r\n=dMje\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9uRbT8X5p7NrcSW6OWJ5Xx3acj5JsunLnrZy4GtJvMwIgDtHMGLhdHc1utUVfDjgLhnGdaQlTgpuZujkuCmz52pE="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.2.0_1612060258346_0.9360513510374158"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "twin.macro", "version": "2.2.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "36379b754093175548303558d578c44b9d172223", "_id": "twin.macro@2.2.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-0zWnheSAYIykwVC8TLIIz0abfGCtC9XIJLluE+LYbKEh+XCSMIGCx6QXBx/CGQ2pGeUFsK4JUVQWhuQsTzcL/g==", "shasum": "4b3fe36ac9bfdbc3111772443f48d598fe952ca1", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.2.1.tgz", "fileCount": 5, "unpackedSize": 180367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHizACRA9TVsSAnZWagAArXkP/1FydJi5mGm3R0WpWWCu\nAbDD4yjLse0CIyOiKIr3M89qmdL3lnk3isz8s2aGlUU5tUJ2++BBTZZCLK7n\nPB+atMmDyinWfLVI/FkuuRaXzc2bluW4fXSfWmvpJR+uvL0ZAhHykpnY/zpJ\no8LjhRhEIgkuBWN+ET7yq4TuQ9lLCKCp7IvLhWlORm0NR4CPn8JUfofT9wKi\n6P2vtPsW4TVTAmp/dF++oOg+2iU4ZPlDjaXHDMQYc9wEVTI3kba5Wv166wSG\nWHm/RgoYQ0uIyjnShmLKvX9HDwMGXwMamLxMfF2xncaM+Id/lGBP3+PF5wO9\nn39Znib4f5rh0Q/lSYuVtA6I3WIqeblfBlruGxOqqYP0HDGQNfPnTL12ME5b\nuMCl3Qrx4TgnkoNfFkI7NMO5PPEXmSPKIcGc9aS6MJwGyDCSsYW5BCZVtgzm\nZD2QTYb7N410wMDfD1BthLC/o44GI+0G8Yt31fhr/14xA4VfVM4TB2+3kcB6\nTrNx8EW7yz6ZS2P+Eb6Or+iyFMjsqpKXWUgoVvuFv4v/yFDEiuYFz3aBvJsb\neBMr9vadKJx5u9qMbq8QO0Lk8jaMZ7HHzRAVZzc1uclW8Ewjky03jz91yqNw\nBzTOizRqW+E8LtgaxOXJTmrcKD+ksMZlV7Q/xSyda5AvUlywjWerJs5USbs6\nq4ER\r\n=I2Ls\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAU4oM6EZeFSPd+3cE0h5zIZDhr3XknVK+bfVAc/dFj7AiEA3JT21mGROCTL7Usd01SCauOEYnjm4o/zQnK/kXHybvI="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.2.1_1612590271672_0.011797069412874617"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "twin.macro", "version": "2.2.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "ab7569d34740173c988a82918a6a253ad975d7b3", "_id": "twin.macro@2.2.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-6OWcfB+wP/SwtINVMc8Y+yecBNDQHf70iXf1J8/+GrBsxL6AJSmYoR7FwBmi3wxdqjjL0kmwSKcu406lHy+lgQ==", "shasum": "996bfdfadea20223f76c1a7f1eac83b22e110ea8", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.2.2.tgz", "fileCount": 5, "unpackedSize": 180355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgH0zUCRA9TVsSAnZWagAATmsP/2UqIcNaqj6miutVyi7l\nTlo4M/N/tsULBkh0PCjFAMtLaqSw/1SefqANJw1ShiYQVoIeaW81K2YXD+XM\naBd9F1m6kZjIU96JXZA78/MPpcXvafj8YNquEHcHdQ8Y2Kqaxx6uAjwZzRk3\noyPTmdTWnetRpUq7kunQG4Hf+4w5igdtqTAL9vdGA6tWbJllK9/HLF2P9IW5\nNCC4u1VphEtvZhwB9yZ7snx/rB8AXWZM/OZ8JS8/yK5uVGApQFRG1f7b7CvG\nwuZvnhBhTUXYdwU8hjt6ScU+XzjXiY4FtYFJyib2gSkPTakZxJEbwMKjRflV\nR8kvYKCTvvwJO4NEhEf+y8YfdsaHqr0PiGjPsG+x75ZMmJK4Z+g3ykVby3Il\nCPbZ1CzfIyncSZMgjL+Aq8fVsXZ691+fdo5XJH4m9VV+eNpS0mSQwLagEI7d\n6X4Gy2l2SnmO1RL9uzP6C57XQWTysPnB3cKhpiNJr2QKPW94Il0aLtBTt8gz\n09gdPPsb8ARCBFEfwG/SjshZ9cvfMzha/VpTcKaSyrZS82iL+eZCtfYitXSS\nNTSA5/Lr9yRWNn/uhwFvGjJ1GezvRexnLT3ADl3pYj3+RJVpHfR6C7Cj55CU\niKGPYhXwb7mlBYwBulPdu8KTYjG4uK5nlb1LimJSpHgAcU+8Z1cMXw+cladz\nYoqS\r\n=4nkv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHNeObK4vqCnz034daan7QPrLYkKmGCjx0En+1CKcLtQIhALqFH9F7oRgxdP6kSFiE4WrnIIEtULYXl2PvYgcoooUy"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.2.2_1612664019882_0.05773789213735214"}, "_hasShrinkwrap": false}, "2.2.3": {"name": "twin.macro", "version": "2.2.3", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.3.1", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "99ed8f610d49cefe993de1736b90e6fed04d69d0", "_id": "twin.macro@2.2.3", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-oB75fYZ9ilWBGYbTdJG+1t13M/gsz4JtgKXhS04lvNxltYwtp9dcY6gfJHFV+id9Rad2aw5NQKMxKQEWej8+Mw==", "shasum": "54f5917270eee74a9db7c353d4a4c60c25f25a35", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.2.3.tgz", "fileCount": 5, "unpackedSize": 180418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI2E7CRA9TVsSAnZWagAAMRQP/3D8dzC7zZgh7e2XYpJ4\n8nk74uvu3dGk1olr5PqAbJ104A5XLuq7Gwzn9uHFN0KX1gFgac87R5VVQxxM\noEuLqVjppySh+TRoRyGJ+zhI8GTni1K17GNst8cAO70eXZUpT1qoO3W5ns1U\nuVOSn2PQSc03yrmECjzxuZuTp0YhapHq1Mcsm5oG5o1Av2BXx1Wr1NOqk4+U\nFsgwzfnxQ3WJZJDlNFrmXQxn+rogbVf4HQeyoI+a5kl8wxlzlMuXwTYHSkV6\npJ3Q41IbAThgWiO/XACrTShVZoQgXWZEUbVIla31iUR1zH5e27EETK66rXmL\nbKkZYxoe8AYEyJC02RDujHdedSnA2MgwcEhhhbvR+nrONSvv8+CcE1HCdY99\nCRnuTPS0Vm23bWWltZkVjjXe2F69d6VwoudhELIcRggcd8StI2ZlECe7W2tY\noVTs1BxpHniqwAzs0/OFd8N2WYPF3Ljl2MqjMlslOvOblblT3CGMR13JuWYE\nYf1cHszjnyQGYthjfkjh7+q3Zn1fyGfwYl7tpPKkGjVwQHp4k4g4eCDLbpbN\n7ji0g6+Dsfcm1VxNrH3TfzeUvQMtSAE87rALaQQcWDPOIswc5YR8mst9XL/b\n6ZJB8zcwIGYQWp+ZSl9NzM9rCxwrkan767FGUG2wrlommWqXBvkrqKI4gH2e\nEPT3\r\n=hoYn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClSWRsImFPoeG3ezKuQMkLkgn2VbJOEQoj3jkORkLsDAIhANXe5dXSp7K23u1zPl2c6PRKBBhnBgN/TOQvd9/GrpNS"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.2.3_1612931386992_0.80474817384168"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "twin.macro", "version": "2.3.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "b4ca80de163b72812e7ca81a410d72ab3e2eafc5", "_id": "twin.macro@2.3.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-uC35EK6JCeRPZ8NA6ElY/v0oj6kcwRciP1pGV/3or9kO4MOe/FQwLovE4MSTBI6HpPSJtbwjc11Z/4t+fAzsmg==", "shasum": "0ce26e8dfb14e44ef6b75ccea2ddcd67a82bbabe", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.3.0.tgz", "fileCount": 5, "unpackedSize": 189032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP1r5CRA9TVsSAnZWagAA64wQAIx9iJn+05FFHigKRKVQ\nakQCwVV8esz6UJ5i1sCigA4TD+QYMzNWtlJWycG0GPy5GS3l9Lc2wvKd/Svs\nDbb0G427/G9pG2HQ7cKrb1oqXagaJ/ZzXAIpSnk8GrCLUc2LZeSH1cfc5b4V\nwbN+H8D6+bcOr+bhRdHamXmag/P4VisVkfAkk64VMbtZIjTiFRakV5v8eiOD\nEWuPDcI1Y1YcriybU5o9sNk9oxvNIB1bA6N32OcHPzqFVHVKQN3n+iLvejfZ\nvtd3RnzfsHp69ZYD10q3uu7mFG/jgZQcd92j4JSNCg2WsyxjLkJDhE79A8DK\nbwqL7MTDY20wAQ1euN0J70AinheiPkTl+5WyP2HGkWtZQBOOQTIn8Gy/nyIs\nKmTnABV12gO8rSjiMbAKDcOwNv9SvqxaK+IXz2qTJDU+2s7ssujv6xsLvTbN\nH6jFWDuR4F8EO/9vZ6xPzZe3rp78tmm7GrbPYRDT8Xhg+sITiJEs9KyEDKP1\n+ZDUPRQ5WIAC3hel36H3mnUXFlV/jEZwRKBjhxAuZeozVbIH9yPINo23c+S0\ndAvSDlQhW05JJ56mz+X30zHFKUIjLn1mHQL08QlNiLVPbXLaNeftotgZ2EYA\nv0L71By/UqYuZq3jlanOIGTNhC2P69mWWYVRu0tGI1DzIuajmMCDwvQduNo0\nI9Ad\r\n=GHba\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGwZtL+mqFGpKYtjPJI0AkaHI/aUPlczIbAeb0RCE6TiAiBAQM8u9rbP6ssZt+XW96soRnTJitJNnASK4pp3UuDojA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.3.0_1614764792500_0.7873829797320036"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "twin.macro", "version": "2.3.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "23cc8262337f31310fb175b46cd4abe74e30bb65", "_id": "twin.macro@2.3.1", "_nodeVersion": "14.15.4", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-laS6+TN1tukb4xQ52B+ghBlKG/bq6wdud2YsZfBIDzu+pT7xTkM3leuar8MwVFhar8O0fUuFxorZyesYDaZqfA==", "shasum": "11b821e071b91f28e508cd9d9f3c1bbc9a419366", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.3.1.tgz", "fileCount": 5, "unpackedSize": 191790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWb5MCRA9TVsSAnZWagAA0iYP/AwXXecvLjYIha+BIbWq\nkKFzJEg/N87TACS5m9bNBnqeJpmtsVaqBoP2mf8nPtMDSgM+cjEnlnGCyYzG\nWpm2fZTuLSiDZZeAu5jlS6XQ7sI9SipIt0I1btONnM0tJIRmODoCdnvBbvq6\n9N4CrqFpgk2Jv1I4rm4YjZ/YJjVh0lZfvh3VkV36ZBYKX1zPxtPBLEQsIo5A\nhDEuLVMPlfS8WEbrEe/xlsdjg+KIkBQrOEfvyoZAGSP7PbhSQAIHcUgZ+sya\nUQpaUS1317pRwKPyJ7JLo7v2sPVkWqV8ru/1P6kpFSSb17xwL9HKeCb/Wpg2\nO/6i89IprUhnF3bbsfPAFSZZWvzGXENQ1SQWigoW6YbggRYRRiZ/BIDuc1o6\nOuYJLqzaZ2Tj2hAassPeXxrablXTQuJaTZWeudjuGxaOolrmm5X4HM2GnQLk\nrUT2q5C7iLagmT2q5gjCcJLfV634sbRkeM2vmM/S4vlUbuozLEkUpFStiNSy\nsz+5GllpAuS5NvszYMNIVl6sMw6MnyOLRp0bVgKG5hgEORhnGCN8GQEfRWd2\nkh7iCxCVktBJf3q1rXl5rftLN/BbDiFcIO1WymfeOFjjF45hc5Z1Ji1SgHlo\nk4VqvyDJVBZQJYr/UcNQwfpi3uVkwMDRODn3LTEoM7A2Eayx7Vctpf5f4KHd\nFEuR\r\n=kfm9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEy9HJumsdtGCf392ClZzNu5rCgk3QIUzNBL1++SVSS4AiAVXXB/9r8o4KyOnqIrgYBzkfkn6TfWWMRCHZWJ2cfbPw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.3.1_1616494155921_0.3821450617193616"}, "_hasShrinkwrap": false}, "2.3.2": {"name": "twin.macro", "version": "2.3.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "16911c0dc75999e7f33edf12039f042fdab50b94", "_id": "twin.macro@2.3.2", "_nodeVersion": "14.15.4", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-tR2VHr/7r1HxGWYHSs3+cxhUsHd9DDPaaJ4EFZQ4uBeDhP+0irMbRXCPxAADvTY5vWW89pEK1F+ka3F8SY+2rw==", "shasum": "c36b35e44a2cb3618fce1122986a9b7ee610c41f", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.3.2.tgz", "fileCount": 5, "unpackedSize": 191787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYi4ECRA9TVsSAnZWagAA1EEP/2uuTiFZEfYeZFj3sbUF\nUYz23Mt7Sl+QVnbfnVWcGXA8xb4QiZ8i2wBpkbNRS8Eorfzxcd74drYzQfLd\nDEj/FXDb4DYmWcuJN44wh79tvQ6wTyFMPVR++1PdnuBrm+zj7LqZjb7dledL\n9HQVJmFtA9Asd/yH6ovBwd1EfTFHYsD9zOmPMIVqQaW9nRdKSIrK6BUv6uBn\nF6aIvrcWJQCoJQIrsqe8Iork/i/cENec/ri/wP1VldO0XU2p33nLn9g/BpN9\nP0swcl7tzG/4x4Gakc5UFjtRcmTLOat91e3/XB42ym0wk4t3oVdhsE87KyKB\nNOUurjTOChDKIuCEj31aI/prtuPu/Cwe7BXlA7LaUy8TsfuaEkd7OvuR8V2+\nX4DjJy7dbygSWT5grRk8z/8jD5L2po8L4vRqqhJl9hOL0QCrZkK0+xIn3fVt\nIVgk0LA+2oKLEx3TjjYD9kDQ6EvIK5aH1AZrWxK6lVn2cduhzLhIRdKRlyOS\nsKXhAurtwXeFQnfa/vTnjndu6h89h4FlhrCoMfjSMa/fRrqBfXmTGtmQh5uR\nUuPF14+4ZwTPh6DHCbY6Idej81XfxLx9wLZcR9kTX/fyVO+LNLHYDom9rn5+\nCxswxmVP8O0amRukgPeHeB230BetlFOTozfof5AzJbt7b1CVbcS10co52OOG\nElX0\r\n=QC24\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbgRACJEq2FcRn/NZmVTL+lNx3y2zth3zvgAY9Q1j+RQIgB7TlRAc1ata0nmR383kEjArqIUlCMtzZuiByBK51mGY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.3.2_1617047043993_0.11318638121960078"}, "_hasShrinkwrap": false}, "2.3.3": {"name": "twin.macro", "version": "2.3.3", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.0.1", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "d4679f3a1542fc39a45672fd82c4086645b43e64", "_id": "twin.macro@2.3.3", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-98avi7wzfjNVR5qQcxRQwCg9CCLgdWhinilmASC+IXbkR/y6h5iHSp9uXdNDPMta/Xz5wyk0MJ8xA2zhhHMzZQ==", "shasum": "413d0bf7edc6a1fdfd6e18f10feaefd01aeac89b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.3.3.tgz", "fileCount": 5, "unpackedSize": 192907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdsegCRA9TVsSAnZWagAAHycP/3FpEHl66+C/v29sxfdG\nlMK3xAvrq773aMjrMg+P1Jv8yf7MT2hpOqkk3uUPDWtWcyuW0LNzW+l5pWar\nVSqtqaKPzXS/g3c5DtJYJ7wTGePQ6MROARJrNbZj4HyN8/UC1xp+QcoxBSjp\nkJb1bRwL7YBHdvKGqTRbACtLQPvDE5aegrde35rGoxSFmyzgQa/fILh1CyJS\nMOgvTWGEAHHXRMc2jgTi073vPvsEo3UJFVv8beD0HEmCSIC6FQus9dJDA+yo\nr3ZD1z3dlAKdlaFkTjBO3bE2aJI1uuzl5J2qVpI7p+8oqN/I0EHGAJfe+Oqm\ns5dg5yc+KkWK/N1H0hxDceX2Xn1eRAgmUqRmvBKNtk/NysSeUopB0hLjzZdO\np7Abe940T1FZ69iB3NR/Ziudt7wKRBSnNmJLWZTKUKmto1HZOfB22YH5jWsy\nlgUJOQEpGuYoycSFBzEE7YkhWxjHvjfnjuWKZKkAg4ls9Ut5tVSNMc46kUJN\n+NIlNprys5mavaJI0GQh6r1vbCZ2CtKBRpyeawgnIHrBQHcPz9HhLDRGxmyh\n5mm0TA+dKHJjui7UTT0wYqNB0YwVogegbx14gTdwYUyHvaitkWfmyCn9zWBo\nSi/BPv4Q8gI/YIsPN8U3uKud2wRqBu8rs5uPAVcvb69hMU+iDo5bsEjnwpyP\nBUYt\r\n=mX9V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAbY8T+ubMTwkEitnZ/I0UKm8Gy/v35S9Ecr8LIolQMfAiAAkyxzJsQMrV+JB4cwB4luAae4InCboJkcwTT3MUksRQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.3.3_1618397088359_0.6042424474195631"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "twin.macro", "version": "2.4.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "ff9397ac633f7a80b839a4e7c6f405ddc9e8abef", "_id": "twin.macro@2.4.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-1iRIn/cKvsyGkZclyeRKVP+UaDIqCPcQrT5nLjIB+4ZxumY3CRRA1Q3QYBu3Mpo2+HrnjThJcJk9iurpa05XQg==", "shasum": "ec78a7ce844d94f6a6001be233a8903c2dd523da", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.4.0.tgz", "fileCount": 5, "unpackedSize": 218095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghocGCRA9TVsSAnZWagAAHV8P/RrS4fhV9Cvhn5ToMvsa\n3a52zc7ruuaP0Ji601Eiq5TQ+x+z3IP2FtvSY1VpR9ZBeXIrf1w6UB5zpz5L\nYdJvKdjttXDxR40NEVL97KYzfKIVh6qw16fGDYii4e387A6VBRYe0xwen1bP\nVRmk8lskm00W9QCamaoLEAESCdBJl/hdV4MaMy2QUldog++sMLDMvE3/baYB\nk4WXVlman8bK9wcjR25wW8XvbmFu93S1bBrXb5e2APVInpoIBUEY9FlIDT5e\nwv06HXw3VjpyPQZUWF9UC8GlyqNOURkdo3KLydb8ODrPZJM/H7G2JDCUWyuh\n3X08ZjeHoyhYt4YXnhONtY+wppKmBG1J21vCcZjLn+jfZqPnMwBu0dJeHOzu\nFQK+B2WgTbfYvRZrF5/iimsVCAlEntpyfHf02VNexTTb6leLPR2Y+hgY+m7E\nz/WpMshlfphBQz/9Ct4aQtg6msrRbJOnRW3/QeINwRb8oVCqMS7Pp+rGznxa\nfOAFK+WQ3mb8Y+YloZ9aWko3NHMfZRABlqQUXsifHdfPDMeRMn7s8Rr2H9z1\npHzKFYSA/9gN5wrH9ZCB5kZ2WOGzNOJLkukNh12qNeToefyxD2R1a2VhRyGX\nuGGWEMjzNUPjiG2oROk5RnE3xWOP/89N90c9yaoQSIDCVNnIpL89NpM5HQPn\nHRXc\r\n=Bey1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFk9DoGMclP2w1F+6eaj0+SILcNcDw+n0JRt6tJDrr1QIgGRrP/TSl1I3u9Y2hEoQtBOAot669Isne6/9425631c4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.4.0_1619429125419_0.03145216798073047"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "twin.macro", "version": "2.4.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "b095c359538f3c8834e8293ca02ce0273f911f45", "_id": "twin.macro@2.4.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-HaEY2R4e41QvL59oTqye3+959R6YRFtLRAvGIB4sLj48Ye+01CnEgQHF1PrM9Jkmlr8ngc6wffbVUWEbju2NVQ==", "shasum": "0a82f4a9747f38fa7f4bee5e5ebb998614785f6d", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.4.1.tgz", "fileCount": 5, "unpackedSize": 218034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgioyUCRA9TVsSAnZWagAAzioQAI9ngeGcCQZ9q2qQ8hby\nAsZIH+sDwUmQK4wzi9jeanbu1wCl709c6ukt3MmiaWoWS1mrSsBVHFalde71\ndPZ7T2dPlnT4VwGTHaK21+5u/fAG2ezNzJxMbB+do4brwdvLeUu+Ei4aKkV1\nMJeGOHyuZxlMvco6rs9lgNfg0pkbC8OTuLhxeUE3g5BIli5OOjBUbcMo7Umn\nZDeFghGJiS8PMrFjL9AGngduSyYAz39aPie966hAZCEVVl79ien/hdnT5R8F\nO7jZejsIdelpO7iedy+Fs21Bf+bm3Ed4EAtlcnyy+kXPfXRK4cT4mM/Db07R\n4OtNDLIuRvGdhe32PaF1zluTJc8gM95/H9dabMH/6lDXEEWjG5bdry0t6fHP\nGO0PQuw2PHxaxeLs3EJ3e7tmysKPUVhXbx/em9BZyuq/aBdio1niRlIpPY7E\nd0noKXe3j4i0/JMu3y209v8NDfgAgalQ6CrNMwd6lu/0CG4EP5Z00TREawI6\nJiKr+peEQM1SoNKe5EgSpG1i9S01EB6OeWUPKUJ9WUter11drPQHBbEZzBd8\npyzOr+HCAArqD2Kg7FmdxwcPgkEIL+SMsi+1jcXbRqdqtbI7YDOg5dsOMyx1\ntZlEJuU1qj6P1z/ARuu+VEizo3+PGBp4LRO27beTR1Ff3czV3ZkHp4WoL29z\neiPd\r\n=DLA+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDRAcHQzLe7SgqTE8Phqe8QnxGUzUqgg6oCWHXi8yPgAiEA2LXD18p6rN+yeBM2pLif/9D9UoD8dmULTBHiXALqoQ0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.4.1_1619692692416_0.24291829328772163"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "twin.macro", "version": "2.4.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "c3051adeb17fba50f90ffc564a5d85b5fcb874e0", "_id": "twin.macro@2.4.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-O+7JGabRqJHQbO+N+dsd1wSWHMbJj20C9QowaY8ncs5Ix640HQbwj7q/qm3yeYK6CiCPZsUcNCJVv+LUSSfWIg==", "shasum": "bb4de5de408d5cff576d45be43149485e83b72e3", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.4.2.tgz", "fileCount": 5, "unpackedSize": 220202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqEVgCRA9TVsSAnZWagAAZCMP/1GvhVGF5lbmwC/JTpCy\n5YLwNtpy9TY0CvMQ1QO0ZPMznrd473X/53RxZRdIUwlElQ/pbFDghb2O1jku\nPoq0b3OLGWKydMPLfqo1cSHd30O+QYom19E5GjwbGiDvsP18rr21kQ4BbnMS\n61bFYTQBuvMOUI/0iJYHjmjAebSr4DetHIne8NtlTwdpivYqmCgT9g9yuokJ\nNQ+O+FQgdcbCtpmZ//Lf2ulv3eXoGqjQliwJVBUTd65GSDQ4nMpDZWagt+lO\nfTfKIVcZXbkD3lkGrN49k2cpEJdnAmXY7BEKtbGio672ZJve9OGpUNuuHoyL\n2hEJcwh5053Zays/6709kokii8eNYKOKr9mDjbl5rQ07fFgv7h4N+vb/2xpX\ncUqoNjLVsdrsEniJ3DSXHNs7zyDrx+ThcnOmsFGplZuKdLAj/LvsJsTH6FGF\nNz8F3eMn4NpS9NQZOJeQp/X+crET9zLiseqCFlhc+oPD4fPb06R8OoGxnBL0\nmdUvKP5AaH9MAgZuTEwP+QD+TiAKkzEZT/Ura0PQHHvZtKuYoHQS0yVTbDAz\nGZoyp4XqxkDK4Kx6HY5GgvfwlJNWQtIVk3CdSeq+UZg9kcgQnRSUsBdmiYU0\nZ2fCztY54LIjMrRmJXF6ch2Q3d2XMEIetahSUyMEcqdnYZcmm+GubAYXEfTT\nhvdk\r\n=eQ3R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIChEuTV33/FQm0HZEAbcN78+4/++OSbgWOwgkMJ7Mvp+AiBwZo9u+M77x5U2ur7tMKKI9NPlgddbQj49+mAigQbBLA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.4.2_1621640543920_0.8306362856118223"}, "_hasShrinkwrap": false}, "2.5.0": {"name": "twin.macro", "version": "2.5.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "27388ad3f856023fb527de46eefd6c3d7fa19939", "_id": "twin.macro@2.5.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-yDkwwJpjlGRk/QGOaZeCKAECUuo2tQJk6Xh1vyyEdzInOQe2IKF461eFay2MZiDcC4Y+Xaz+5iIM5+VHVtRDtA==", "shasum": "bd0575cc7680a8da4209f997aff26f5b8223f7dc", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.5.0.tgz", "fileCount": 5, "unpackedSize": 233747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxGUTCRA9TVsSAnZWagAAIX0P+QDEylD/ZwRWVZgZ5/pU\ngbhS+iTloMHmKWtf0zpWlyAKthr2+eqv0hqOlyhG1wrkd2bh8L2rlZEeGuqz\nPvOyM6Q/nUWLnAJ7eqaNlOe+V9ZDDxWsJ2YOsSq/YLGVKLt7R+0f/PrrlG7M\nY/uZjRyJZftIQOaEUT8sbD5CjESdBxtQl+92Od1iunt40W3b0/XsyL6F+XFF\nwMrAFEWVWIlXf4//BFoRXOxqKhPmh/uOMIDav7toKbbUugFJBQRwcXlLt1E1\nKiRNKBFNUMem76GYy1DZEu1YFAYOXCXMNzb9pS1agnOMF0qE0WpWr/li+0R/\nnd/cglH2+cIdkBje7mVkO4sKif/efvPOZcs7tEFwZcW6LUM4UkDMwlj1d16A\npuQKQ1Ofz95zoq9JoL6B0rCN5u6NSW4z1WcVgurFpmbNdhhBmPOiY3aJdwZf\nR+ejwubKR0/4IspjYNFUTsy4HrAlm0gLehOIosmTSwxwZtW5O8aLzCmwmvlC\n62eLzZ9lFEDww3tpKrw9BBHOeeiapiGcOtCLdLp5FwwP2H/bgUCIn1WLhZPM\nLWLB/PvLn5p67ivKb6Ytr9s2LOQTf4s9Km9LiEvVAPMTW6cEF5VqN7Kt9m9H\nqoFn6CrVe5f0BWaWjmIURJyTJuSPNbrhb7fTufTzefvNplbco8imW2S8M/uX\nf64R\r\n=Ouoe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDdRnwPA3Uj0R99RCpoG9/CN2H6EMDL0q+KzMAxjTAWoAiEA+UsKJ8n5VyKag9GgFGI8ZsFsk8nLd/9iege+J/bL8MU="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.5.0_1623483667137_0.4808144375411605"}, "_hasShrinkwrap": false}, "2.6.1": {"name": "twin.macro", "version": "2.6.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "70e3593341c2c74ca246aba4745952be4afe198e", "_id": "twin.macro@2.6.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-GDneAn95SKAfnyZyCAsgvoi4q33vOQnS5B34T3jHK1qUhNjv7ZXbbDYKx0yU450TOmzcPng9oq7bzTFlnHt4zw==", "shasum": "d9bc03c7edd9c869cde66e4a9f0874c76acf9836", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.6.1.tgz", "fileCount": 5, "unpackedSize": 243031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3/uLCRA9TVsSAnZWagAA5fkQAJjuV2pFL0Wz5ATeBsew\njuZIYKHaU4x8avOO0We1+UNgnk5LFtP7JokDDOU+Bj7XmW8NufpEyYcjtfZR\n4iuUL4EiI4PGMlGECZUWPtXPOhBW1m18t3LFg5PNeuxUsSu1gBJpmtUB/byi\nalvOUaAoWRzY6B8MF9gskzPM2jADcaR+Fr9OfFqqStlhtATwdgNdBM/OOBth\nCv4W1G+jFMNOHh+fvF+DIbuLcP/i9/UcHqLCILLh5vpvGEjlg9FDq3/mD9b5\n9DgjnQOonyTRDYEWBIgJ2+fjIUsyHENDMyEfsCQ12OHe1G586+QFRSRL9G6Q\n8qR9o4L2aw+39a5e1Co/3fKdjyGzk3nIwi+sFfomf4s0B40+nY4nKQ7++mM0\nQ50KnpFI57K4sipBPQ7+ucD5CWl21HgvetktY8upI0SeBizsoueWcMhyic7N\nIHfdnxfJexiRE9/1CpQyutNFFTjp2iXnwJfkT00SVNAtdPjn+++IaetWgnN0\nE4XdJBBu+j7eW4NeXHj+NgkAoC4k8uSmNGqPkSjfe6fGwLcsV3UGTDVMyTI/\nXUlOSgRRoTBssP5m9cIHmYwrVEDtIdBn+oR5HCIMirv0qReL7Md+hPp3W1eI\noTMyo0bQJfHA8o/q39lZmv23p6PxCzAZvGKZFKu+9LI3gsn+Qtg+Vsbiaf+X\nSWyx\r\n=L3M4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/uetmoXmzvtq93/HpMjvWyurmUPCKAZjYkw9GGle5CwIhAKCu5p3GRHPLAjlNNHHo5Ai5TKOOSJNf53h7wzWQY+r4"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.6.1_1625291659342_0.6108781331831632"}, "_hasShrinkwrap": false}, "2.6.2": {"name": "twin.macro", "version": "2.6.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.1.0", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "898b50e1b1f7046b9fd507be9e78a7bba1f57d4b", "_id": "twin.macro@2.6.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-4SL+Wrydqh7RRfYjuLh2ftNLv4rmRHXq1xpj1gaLuQkCwsgdwSbwMJrXKDh+WdX2yboXJ6T1QWv7/VIXX/+6mg==", "shasum": "e230ca54cffd9f0de04ce0b74031d611d72d2bc3", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.6.2.tgz", "fileCount": 5, "unpackedSize": 243256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5rG8CRA9TVsSAnZWagAAzdwP/1kDcSPzn/4yVpyXsYjw\nhsr/1tWQg1EEPeUtjwgjuRHRPXBZH3b6os7F6Dr1yA5wsGigmbQ6XK8fHisn\n+Ur96rCQUqXsvjDKKNpcLeub3LKYmAUvPhAXX9QuY/odlFWacsdsYRJ5XSjG\ndqZNEbCUa2/n/P2GOjnNg2EQZWiZNYajr7EBAO0FrJnZN1gkEu5A+N14H7oo\nfGz2OQWJsl+xA/Y44nbKXbSVI+qjozwaPRYTgg4PPyRk+hvCL2FnxZQ+N/i9\nYsWgoWrKKOBgCvpr1pBHU89X/ROTnwQrZ851o9bijEhs+d4CJ4DK2rVL69Fg\nad+7gSmMKIdEspyGMuEG3GSOvI0qOuXoVqwiq/Gf0n0oGVtnWHFbo33B1ovm\nhhsGHYZfEszphW53ODSS7okoy+AhfLyiO/RQ0aybCLTiM8gO0ivy4zJHBytI\nScUYIhTEy7z40OuZCrxDqu6QOT5Yg2MYxZPU5OO7qgzxZld9lZMC8uVVwePk\nx4jO/sGUs3EjVh8WIz4hnge3VfYM9fVqX4l3nLsyiW/ys7JqeoQffdl2KrIg\nrXi1jdZyS+LVxXYMx/Cd+DlhKBrT6PyM5QiGM222My1YmiAlURInpIAjlcj9\na7S7g5RJ5T31dmm224ChzoFlXFy7F+xxKkj+GGv36zaUIJ03Y7n/0wNLCLKI\nFQD0\r\n=/OxO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgXdevJpbCbapaXCTMqGs6x2tpk7287Awh5bwVBZNWEAiEAmWtnuCSS7MAhSmBXlNBxoz1p+y6i+RMOGldlbgX95WQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.6.2_1625731515655_0.1556758683408508"}, "_hasShrinkwrap": false}, "2.7.0": {"name": "twin.macro", "version": "2.7.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.2.7", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "ac7b194a9a0b7e1deed1feb5f42c1bff6060020d", "_id": "twin.macro@2.7.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-7RictvVsITP3Odm7zd5EcZ3i8+deBuDCZBAwnm/H5MSixaKESjvEFQaHA/T8nkHb6vKcp/2f/6+txRzXLj//gg==", "shasum": "2107e79339e24041de1f72e0f045ae321041f007", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.7.0.tgz", "fileCount": 5, "unpackedSize": 268888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIfksCRA9TVsSAnZWagAAbx0P/ih3WzEHXzl9vXqBM7+8\n1wQ5AJwFsIJpyndynVtdIj6HhoB5xD7kvIr6P8LmGhuNPTv319jTseCG+OD1\nNPxTIYOkvt6rFNxKQ5Ubw/RFsKbMlW4xL6SdfpQ8ihuzN8vy8YbwVCnPfAAH\nWbJsFuUKfAqAh+jeqHal34Prunt4G6uF4ald+G+bu8i9nR/9J3NhvTrtOJNT\n30O4XdopnmomHP0Hg+tOIttWnfMwPctsjMCpN6XWPTh7z1Tmt5qb1+m4jUOz\nixuwWRN8ujMUTPTH8+ZzyrapiMvcFAMQs8U+rCwYHa1EjgjQrIEGqScZj015\nIJWzykNdOXWLn1sC2ukSskmpV6eBalTUjMbEW2sSdIugMS3PPqTcV6702Ak6\nSTE06A01ctbKZAYApOsLK0Y7X9SkKjMBHnA5jOVSSefeQxhvHjwEY/xSB5Rd\nrE1t1//hXRAAwbnG7CqdT5YBHiagv+sRLjvmDq8o118dNcsHvpUVNp4zEmOC\nFT+GYdm2i4g/xbLBql6OATkeHK/h+JjKNeCFdpKflG4S4GRdvkbMELoRVEAl\nbIJdZ85GbrSDZFm2BzP9UVX7wVwdLP7DtRnp5vvP82MUL463x3c1NW9U2BLe\nmWkJRQoqpJilYexNJO+bInULRLNq45nQ+stSJvsyZTXWz7JNxjG8sS3vDDpj\no4nj\r\n=qT6c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZOJ3kKVJvcHOmQzl7s1HvKpZ9ef+85EU2mWvAo+omqAiAptR5XKQrIGCH8It2/4hl9ovf+UzUWk0fCSDy7UOjpRQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.7.0_1629616428585_0.761954900650448"}, "_hasShrinkwrap": false}, "2.8.0": {"name": "twin.macro", "version": "2.8.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.2.7", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "6aa9082969173ed21fcea9df525d2a17ec8271e1", "_id": "twin.macro@2.8.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-iORyfVXFlWcVebDF3i0iQS9Ek+9ybsiotTjDBPFg1iG4gWJe4sbudd8iC0gysSep/OWVBcKZ6dKzo3kmVKMqXQ==", "shasum": "eabd894cb00cc9251856ba093a057199be922bf1", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.8.0.tgz", "fileCount": 5, "unpackedSize": 272407, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjf2jUFZpCB81hD2HVwWq7XiuHlg9MRbzPCGkv5+fWxwIgYBdvSqLIueqzsvBbQe3Cy5LasIBFNQYPpJGRlV3CjUc="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.8.0_1634089385213_0.4249416643813346"}, "_hasShrinkwrap": false}, "2.8.1": {"name": "twin.macro", "version": "2.8.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.2.7", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "ebc25b3fbe519fad276571ec522421c131b30d54", "_id": "twin.macro@2.8.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-rsAc9Ll+9JER5xwbHxtJp3VQghlw18WzNi0lDyKBtLcvpreqRBtPjQjvP3NRey0tYkYF0GgoDWZoQtlkGcUKPw==", "shasum": "674c75a47b0416b9d129681b2676f20507c4697b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.8.1.tgz", "fileCount": 5, "unpackedSize": 272552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCHEDFe6ptO4HRKZC8KzwUL29IQ/an6F3G3FtKNGhhewIgLLrn39DywJ80mWBlekgr8ka73O2SD/lqVTEQCv4h78o="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.8.1_1635300686852_0.23286076287150248"}, "_hasShrinkwrap": false}, "2.8.2": {"name": "twin.macro", "version": "2.8.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "nodemon --watch src -x \"npm run build:macro\" --delay .2", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "test:types": "tsc -b ./types/tsconfig.json", "test": "npm run build && jest && npm run test:types", "fixup": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=12.13.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["glamor", "emotion", "styled-components", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.12.5", "@babel/template": "^7.14.5", "autoprefixer": "^10.2.5", "babel-plugin-macros": "^2.8.0", "chalk": "^4.1.0", "clean-set": "^1.1.1", "color": "^3.1.3", "dset": "^2.0.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.1.8", "string-similarity": "^4.0.3", "tailwindcss": "^2.2.7", "timsort": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.12.1", "@emotion/react": "^11.1.1", "@emotion/styled": "^11.0.0", "@tailwindcss/forms": "^0.3.2", "@tailwindcss/typography": "^0.4.0", "@types/react": "^17.0.0", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-plugin-tester": "^10.0.0", "eslint": "^7.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-xo": "^0.29.1", "eslint-config-xo-react": "^0.23.0", "eslint-config-xo-space": "^0.24.0", "eslint-config-xo-typescript": "^0.27.0", "eslint-plugin-chai-friendly": "^0.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob-all": "^3.2.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.1", "microbundle": "^0.11.0", "nodemon": "^2.0.6", "prettier": "^2.1.2", "react": "^17.0.1", "styled-components": "^5.2.1", "tailwindcss-typography": "^3.1.0", "typescript": "^4.0.5"}, "gitHead": "1e9ec1aac855803541d1b84a8c3c92a1a10cc18c", "_id": "twin.macro@2.8.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-2Vg09mp+nA70AWUedJ8WRgB2me3buq7JGbOnjHnFnNaBzomVu5k7lJ9YGpByIlre+UYr7QRhtlj7+IUKxvCrUA==", "shasum": "7f1344b4b1c3811da93a62fa204fe08999df7a75", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-2.8.2.tgz", "fileCount": 5, "unpackedSize": 270591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr/VWCRA9TVsSAnZWagAAIDEQAJq6QMPXrCealDr1w1gd\nklQYoB8TokSSzQMTfYuy1bVsl+bdxGLyIzFBDNJiJ0pjjWCOWuKVvUEPCCRb\n+0u5L7k6o0cL34tZWQRc30lNrhX1Ua4aDpqV+kCzjA2h2mGU13kGvn4aLXHS\n9YVc9NjDmMqYmqMk4ZrOh38KDGygTDqqke9/rDVRei+DVuu1JsCsiMd9D52t\nfCdnUlZAiRyBm+prcH8un1BJsj6wtYUG+7Sjj/11EkPGTk9ZbxN563WREg06\nmRjaGUArgCBbDmWGFK91pgDskLKyPYKOQVmnfKVan/ojAhiiYmvxOsTXtZ53\n0S8fj1V38dqV62ocSyX6fo2M/J9AsDUn1f8N/doboK9UfeFzp4OX2y2rvqs2\np1Ig5LokNNe/b46MXc+/MLaPgT5SD7zNGIqIpevDWkPlUQXs/GaAOdsg9ILU\nQ2Nh9Rtenu1x2/59RrzoUPTQRRNUTvGpOJZxgPDir1eAUboDNetc5zEXk9O9\nOjBLIa+OWIh/+GQPEb/DdHKAlaxjJ+snyortyf5jVeyJm76R6r9NGzfQVlD4\nRw1FBCbMn+IXCLchNO+8b1g6nGB1S6xUGa8XVCaSheeX0ImyQoSgNBTHMBwi\nUMc/FxxaUUByYXmzJzshSfKXZiS3IWRVs+tTO540tvdr2vb4g7NECws6Z6JF\nKA5A\r\n=wZfs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQeDtmygAVFPx7by2mPlJt2ANop/VluLaHN+etxMfGhwIgMZS9zcP2W9ZxzAucZ9vweNo53kG18RZfs4fncSVKWLY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_2.8.2_1638921558403_0.0378885343251103"}, "_hasShrinkwrap": false}, "3.0.0-rc.1": {"name": "twin.macro", "version": "3.0.0-rc.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch src --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.js --watch tailwind.config.js --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.js --out-file sandbox/out.js", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:accept": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.2"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros", "glamor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.17.9", "@babel/template": "^7.16.7", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "clean-set": "^1.1.2", "dset": "^3.1.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.4.12", "string-similarity": "^4.0.4", "tailwindcss": "^3.0.24", "timsort": "^0.3.0"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/plugin-syntax-jsx": "^7.16.7", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.2", "@types/react": "^18.0.5", "@types/styled-components": "^5.1.25", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.1.0", "eslint": "^8.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.40.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.32.0", "eslint-config-xo-typescript": "^0.50.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.1.4", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-unicorn": "^42.0.0", "glob-all": "^3.3.0", "husky": "4.3.8", "jest": "^27.5.1", "lint-staged": "^12.3.8", "microbundle": "^0.14.2", "nodemon": "^2.0.15", "prettier": "^2.6.2", "react": "^18.0.0", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "typescript": "^4.6.3"}, "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/rXdOJG5.png\" alt=\"twin logo\" width=\"300\"><br>\n    <br>Twin blends the magic of Tailwind with the flexibility of css-in-js<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox →</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n- import tw from 'twin.macro'\n+ import tw, { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-7 was not found\n\nTry one of these classes:\nml-0 [0] / ml-1 [0.25rem] / ml-2 [0.5rem] / ml-3 [0.75rem] / ml-4 [1rem] / ml-5 [1.25rem] / ml-6 [1.5rem]\nml-8 [2rem] / ml-10 [2.5rem] / ml-12 [3rem] / ml-16 [4rem] / ml-20 [5rem] / ml-24 [6rem] / ml-32 [8rem]\nml-40 [10rem] / ml-48 [12rem] / ml-56 [14rem] / ml-64 [16rem] / ml-auto [auto] / ml-px [1px]\n```\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [See setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**🚥 Over 40 variants to prefix on your classes** - The prefixes are “always on” and available for your classes\n\n- Prefix with `hocus:` to style hover + focus at the same time\n- Style form field states with `checked:`, `invalid:` and `required:`\n- Stack up variants whenever you need them `sm:hover:first:bg-black`\n\nCheck out the [full list of variants →](https://github.com/ben-rogerson/twin.macro/blob/master/src/config/variantConfig.js)\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**👑 Add vanilla css that integrates with twins features**\n\n```js\nconst setCssVariables = () => <div tw=\"--base-color[#C0FFEE]\" />\n\nconst customGridProperties = () => <div tw=\"grid-area[1 / 1 / 4 / 2]\" />\n\nconst vendorPrefixes = () => <div tw=\"-webkit-mask-image[url(mask.png)]\" />\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works within many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n- **Laravel**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/laravel-styled-components-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n🎉&nbsp;: Fresh example\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippits](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "8c4f23bbec13553c34ef9f2db0fb8e7c6a0754e0", "_id": "twin.macro@3.0.0-rc.1", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-YlMnw0J2d8tohKTVDn8/aistKL5cDg7+PIG+VHX+gVKwWnJ7vFbTN1wR5bS8ieUvaiMxGVSIKogEGDj6pt54SQ==", "shasum": "cb95515476695e330d63d829e1ba1e1ad2a34f37", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.1.tgz", "fileCount": 5, "unpackedSize": 356628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmxFNeCdXe+t/cKlHj3qlBjNJdfJxfho7+VTwM2T2MzAIhALS67ycGlNTv3qNLYYH6/lY/AbEBbMTz71iBBpC6vswJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicEhwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYKw/+MTGsd+K1m+nS78G/AZPYoYEF42O/VEIp6DCDKRNYpDIlhvg2\r\nMoXg4PDfk4TVvJe8m329jAAR7IpNotOgZAeRotYlIjFoRL1auO+nRoIvXYwi\r\nbEszhf2ETVqnFITJMjzDlSBGKPTiDaLcybBRwftncRlzKaqqrJHhQd8Msi++\r\nTITJ8HyLfS8bqD+0NCALBSxO1n9tFPn0u9GQtYzehp3327SK5luMnQCxJ5/D\r\nhgujvG43UVXaOwNAbklxlazQFsA0DrczF/ghU1e/0A0uTJk49r2WF5rNfUmS\r\nrxeD8/r6ne81BWA2lY64QTCSuQE5VitcF+Gm9ziL+W4IveYc13ty3KEhNX5Z\r\ntmmvQPebAqC7hnXXW6unRhlS7EM1nGFwrMaeeZA50JmsRYcxC/dCEHssydm8\r\n795vtobryrlqrdmlFOfCX/5ckAwqRK1OzpEt/QqVCTmtTBfYZijVi+0/odfK\r\nYsTgv6EjR7icJ8c2pvai1MQ4Jg585KJgrWtspiOPqYgP6cwVGtkw6QfQT0yH\r\nbZPPWcElZ4KsexSWv+5prBWotZTCkbbYtBuMVRQCwkZothCyCZnPr59BehYv\r\ngQItBdrnuY61PyoNqzQemPF4+9eqQYkfUpzDGvRecfsWhYNJvqC7Sq1qHEX2\r\n9fqjfFaFwdvTKn2MKG29xKCJ4469PKPEK+8=\r\n=shQs\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.1_1651525743982_0.34462085335663395"}, "_hasShrinkwrap": false}, "3.0.0-rc.2": {"name": "twin.macro", "version": "3.0.0-rc.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch src --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.js --watch tailwind.config.js --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.js --out-file sandbox/out.js", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.2"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros", "glamor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.17.9", "@babel/template": "^7.16.7", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "clean-set": "^1.1.2", "dset": "^3.1.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.4.12", "string-similarity": "^4.0.4", "tailwindcss": "^3.0.24", "timsort": "^0.3.0"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/plugin-syntax-jsx": "^7.16.7", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.2", "@types/react": "^18.0.5", "@types/styled-components": "^5.1.25", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.1.0", "escalade": "^3.1.1", "daisyui": "^2.15.1", "eslint": "^8.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.40.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.32.0", "eslint-config-xo-typescript": "^0.50.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.1.4", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-unicorn": "^42.0.0", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^27.5.1", "lint-staged": "^12.3.8", "microbundle": "^0.14.2", "nodemon": "^2.0.15", "prettier": "^2.6.2", "react": "^18.0.0", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "typescript": "^4.6.3"}, "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/rXdOJG5.png\" alt=\"twin logo\" width=\"300\"><br>\n    <br>Twin blends the magic of Tailwind with the flexibility of css-in-js<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox →</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n- import tw from 'twin.macro'\n+ import tw, { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n- ml-11 > 2.75rem\n- ml-12 > 3rem\n```\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [See setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**🚥 Over 40 variants to prefix on your classes** - The prefixes are “always on” and available for your classes\n\n- Prefix with `hocus:` to style hover + focus at the same time\n- Style form field states with `checked:`, `invalid:` and `required:`\n- Stack up variants whenever you need them `sm:hover:first:bg-black`\n\nCheck out the [full list of variants →](https://github.com/ben-rogerson/twin.macro/blob/master/src/config/variantConfig.js)\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**👑 Add vanilla css that integrates with twins features**\n\n```js\nconst setCssVariables = () => <div tw=\"--base-color[#C0FFEE]\" />\n\nconst customGridProperties = () => <div tw=\"grid-area[1 / 1 / 4 / 2]\" />\n\nconst vendorPrefixes = () => <div tw=\"-webkit-mask-image[url(mask.png)]\" />\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works within many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n- **Laravel**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/laravel-styled-components-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts) 🎉](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n🎉&nbsp;: Fresh example\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippits](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "7884a9b043bc273b93015ca768e629bf2bc90ec9", "_id": "twin.macro@3.0.0-rc.2", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-c1H1afBUoacFv0C0kXEhgaCvTcjM38Xn4Tg/48UAgfX8PiMF7AMopy2xmb41dXhiy0nNNy86zv9kh1ALyVX24A==", "shasum": "a420dc8c92223513ecb875bf9dc213a5076784d7", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.2.tgz", "fileCount": 5, "unpackedSize": 365884, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIELmZZskRrnYJZ4CfbsP/w6+CpSdVLWGyLAJcqa8lhUFAiEAyRl+5RvW4SWg3Tc49DInEoq2C+fJyn/tQ8IhNwlmVsk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilrpqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqt1Q//QZJvY2ZiNw8oPVLe3vwWn0NGndyrHWdCRnRTUqKhRHXjy/3c\r\nvEl51RzTpULtD0WporFvF3F8k4v1B3JIg7cWfKPG8FYqO7mZ8lHM59dUh709\r\nrI1sP8x0y4RNv6aI4S+gqJOMVi3xP41sC/5/s/nhFw/CNXV4IgdczA5OiNr0\r\n/bus3lAGp6orNV7mpcYmkoR661OJ9Qn5cgJwQGgtZFpsDoZS2SquXs+ylucx\r\nSq8MmTLk5zx8QBzYp85bvC31Kx8jx62N9X7NbfUzZU5E9SUuHX6bDLFU0+ER\r\nPEyBEqOGGqGe2UCF4BRyFd5ZVcBURTEblnbnAS14X1RxdNBTiZwwbRmJrFzb\r\nFQ/rLlF3janGR75EWatpsxq5JW+lHp5wvDYudQZPAgm+z2J9zNLM+M+qH0Wz\r\nGWkrXy5/K4CX3/8jn2jbsBVRrhj3IKUBIaxtRHkiJGHLayLmdPFYAjJbWs1R\r\nVWB+EGlLyX/Zu8rlvEvVe9fJ906FTvg4ayvjkQbXWIPByqpAi6yqXb7nRL30\r\njBLFuPBAmgZPeeAB8H1daQn18ly8e4MJ5hEZyLIc+EUQJJONpVaWn05wu6AK\r\nKFouPylj1T6xeX6l9XBfGXWedo5WP0Sf0Fph3G54iSNdh+mEkmoowF0dKMgn\r\nZWqntVU1WB4oU/O4LAkq+qkPiAvY12bOgoc=\r\n=GNc6\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.2_1654045290198_0.35888113733695803"}, "_hasShrinkwrap": false}, "3.0.0-rc.3": {"name": "twin.macro", "version": "3.0.0-rc.3", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch src --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.js --watch tailwind.config.js --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.js -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.js --out-file sandbox/out.js", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --cache --fix", "jest --findRelatedTests"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros", "glamor"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "dependencies": {"@babel/parser": "^7.17.9", "@babel/template": "^7.16.7", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "clean-set": "^1.1.2", "dset": "^3.1.1", "lodash.flatmap": "^4.5.0", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss": "^8.4.12", "string-similarity": "^4.0.4", "tailwindcss": "^3.0.24", "timsort": "^0.3.0"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/plugin-syntax-jsx": "^7.16.7", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.2", "@types/babel-plugin-macros": "^2.8.5", "@types/react": "^18.0.5", "@types/styled-components": "^5.1.25", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.1.0", "daisyui": "^2.15.1", "escalade": "^3.1.1", "eslint": "^8.13.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.40.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.32.0", "eslint-config-xo-typescript": "^0.50.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.1.4", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-unicorn": "^42.0.0", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^27.5.1", "lint-staged": "^12.3.8", "microbundle": "^0.14.2", "nodemon": "^2.0.15", "prettier": "^2.6.2", "react": "^18.0.0", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "typescript": "^4.6.3"}, "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/rXdOJG5.png\" alt=\"twin logo\" width=\"300\"><br>\n    <br>Twin blends the magic of Tailwind with the flexibility of css-in-js<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox →</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind v3 classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n- import tw from 'twin.macro'\n+ import tw, { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n- ml-11 > 2.75rem\n- ml-12 > 3rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works within many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n- **Laravel**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/laravel-styled-components-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippits](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "c37fbb02517c606a136c6e2e1bec78ce27f04e5d", "_id": "twin.macro@3.0.0-rc.3", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-dWI9QLfoB/m2NAOvu3B2B/e0AhxdYVD50bxWshtr1oGOO3PJdfHRZWbev2H/sx3MsAhuKrV71XUKeahnATDXqg==", "shasum": "9d92a5593f1b2649637e150f3a2d4661b0ef9e7e", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.3.tgz", "fileCount": 5, "unpackedSize": 366417, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfEH/WCgPT7U15sxiNkuf+GFlY7DzkP0gToBkUBJ6TKAiAVuaE4w4YgnrSSxsKkihHULjZComqPLh74/DQ/SpfrMQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirt/mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcyA/+Ir1az2SS/wqeFN3vSZT//CD4/aZ7FYRNa8x9nxOtofR9sxBM\r\niQT+etVmsQDKOW+RhfUiUWZ7xrhFpvlaH9miMZOuusGo22mtwM14QO4c2MaK\r\nNzowpmAIK9Dc8Eqlcqv+S3C0KbPGIdcgzvKVl1LGOletHRMBBE/iqWoaT+nk\r\nY5oIJsQUT0CelTHxen9gxLjsBqah183+4x393SW6tqjF5FgagqXEPHgRPdsT\r\nbiubbWw7Ry2WTIs7wwSDryF2xal6WG4gFxC/4S5FCm4C4SVpIudx1Q1NdUA7\r\nVXThBE5vxzXYzOC0oAT5BxW/GIYPU4/iBO1iPEUip4yT/7Ax/jfMQCs2eKQD\r\nP/NS8eBBw060Z2943HEVSSODDa+jTlJKapByp+gj8Cs5cb2ZRI2IfUqGs2nR\r\nqnhCtbZe0XSuo1b4/q/f6NXAgs8JdPua455fpLiDHKPIbPDq5rwTscVNWZ+T\r\nISuVkue6M6AFD7g9oOrprKuiPsUugOmuyAZnCZf2qiwNUmWYww9qaP4y/epm\r\nrzQjqDaIfjTO3dEza16I2oe2m3Ts29U/dKdnGl3rwh8n2Bz/AxoWEL6uT/vC\r\nyg1zAPg8maoRV0O4Wz9jAMVPFKDPSm8I3VffVEctay+PXfLg83DDOPy8NlXg\r\nHH5FqtTKUP9AcBbiSx1p9tsMWFHvhLN5k+Y=\r\n=fMBx\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.3_1655627750117_0.21028007106162372"}, "_hasShrinkwrap": false}, "3.0.0-rc.4": {"name": "twin.macro", "version": "3.0.0-rc.4", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.1.8"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.4", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.17", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.3.0", "daisyui": "^2.24.0", "escalade": "^3.1.1", "eslint": "^8.23.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^43.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^28.1.3", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.8.2"}, "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/rXdOJG5.png\" alt=\"twin logo\" width=\"300\"><br>\n    <br>Twin blends the magic of Tailwind with the flexibility of css-in-js<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n- **Laravel**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/laravel-styled-components-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "fdfe59d52fb977502b96eae20e756e38652b7641", "_id": "twin.macro@3.0.0-rc.4", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-fxbue324+xVUwenBsbDcUpn3ReC5i7MYPyPQa7utMmdkA+L0Ef2gOyKgg1XLDVP81VOHZQWLJwcbfbJu17HsVQ==", "shasum": "a071a0a76fa8ae889d0d69e828d1e6be15721ace", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.4.tgz", "fileCount": 5, "unpackedSize": 119866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLtwJzbhJOeQob0VbGNi7BkW5XNMe7f+CGuBJeTtIxXQIgdd0ZLe+hT/FoWXwyzVfmX1wapJnZipT5lg7q/3khqmw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjG8idACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSZw/+OKZTM5xWH2/pozBxs+1XFOTMKJPW8Xws66hM8j88Ql3edtk9\r\njxvXqQMU5NjM6Kbaflv7Mov5pVpI6UsT/9lAjd1OwEJ73uuxzjjzha1MGlVP\r\n9O2h5xYAtw9x8F1OlV3I2AlLcgUqGnHq1oo4OJquFycKFqxmn9zFfWJnIade\r\nXVIMj8RrlWitlBYzobpJjiUN1HG2ZCcgAqhnI6ypMaOSbl9m8Nm4vQ+GL<PERSON>\r\nxJhxFspOakdRq48BzeTs/KMfqKU9vPL2irefHHHMbFA+ZBJVGanwMM+qX6Wd\r\no8nrAH0IE7VxOVH/ZNchnxVWK4sI8AMSb9PXJ3CBaVdi2n9MvqA96Yg+cQ3S\r\n2Gos9pUT81qHbdccyqt5eo7kIZxnbMYJdtVFOplWHWy62TsACcf6zo9Pn4x7\r\nKwvOcxUYmcfsHkwUJuPL6B5xBBjyjNUHY2OPFLlwjSLMaBzPWkbCKdMKD2nn\r\n70w2mgF0runQ2SDRhtsDDA2I86wiKZj90hnhSgGA3md/KoAu+NPeP6xETogi\r\niGnyfWOkqGI/zNlu5vgJANdJZWKOZ1Cblm+UxDO4ND8sXTRlpJacmbB4Dyca\r\nrDYjC52zB6cRQcZCMg0C6/6HBqwM3rQkdfIRaIq6Snmj9v8zUNgJxVZT11gi\r\nnV4FyJnF6btST3JfWpLrS4GklTL9ldkg61Y=\r\n=r6E6\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.4_1662765213054_0.8406433307165586"}, "_hasShrinkwrap": false}, "3.0.0-rc.4.1": {"name": "twin.macro", "version": "3.0.0-rc.4.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.1.8"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.4", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.17", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.3.0", "daisyui": "^2.24.0", "escalade": "^3.1.1", "eslint": "^8.23.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^43.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^28.1.3", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.8.2"}, "readme": "<p align=\"center\">\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-light-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-light.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-dark-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-dark.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n</p>\n\n<p align=\"center\">\n    The <em>magic</em> of Tailwind with the <em>flexibility</em> of css-in-js.<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "99937b7bc110b9d480c53f9055edccb467bb98b3", "_id": "twin.macro@3.0.0-rc.4.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-OEPit//0mKHZwTzDfgvOksJ6Y5HwOYptRJlPP3EgRgnA2uLqTtDsnr67tokSaK2zsccnflbJ7+UWTQ1XsuW7+g==", "shasum": "fff079274bfc51778fda138d5cba04d14365477f", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.4.1.tgz", "fileCount": 5, "unpackedSize": 120039, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDow2opyOZwQMwsH8LT+be51dgrrofRYKrgy43tA1sG4QIhAJ18/UUPG7/x6ZIZmIrCpqafTz4BPAYQtN02fcmx9eQ3"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIkElACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou7Q/9E/fFz9xH8PcKOCtEmt3XZmXsj91q/s3U3u/1M2E/RvQ2xNBa\r\nG05oVYsbiBCkuHP1C71Ji/zHNi/GPqcCTkImGKOfwuw/5KrZIH9TqespDzhB\r\n9wn8hizXOtFMuTGefqNx7qrEIPJ6GQT/Ua7tv0Pi+vGHQKGnOYNhENwu7fuN\r\na1hLAaghCKyZvN+cWwyIbs7KlmqMGpkt2bmhwx9D+sOPQWlHYnYnvzLCDgyu\r\nSpVzRwIO6Kr/4d14BrdWGBe4YBeDvHx8ZvV4SF1tOEba407K2IMsgrNVw8pf\r\nuBDZqGnaF3e08rJvrSu7qmIL0W2DThR09oOH0JuZUKr7lyqR6USlSwBvzVXR\r\n7CmY0aKB+NF2cykynVsx7e63pcxXtNzUFE7o/sbgU0me7LkgQGqz80EZBR1E\r\nYmZnc77GUCdEeZrJizQw8O+PGUv9MZXLT4zsfHBgeT3UwG/sGveoOLm9odG8\r\nrBDMmuyFuBAUDUWHJhfKmIZy/906afiXAxCYUiD15H8AqJuNCDz4r1B1l3Dj\r\nLLfB5HjBDzFz+NulqZnHW3sZgXL6lAzzXpVz9NL+uzTCvR7A292izFu0ayNi\r\nqHKk77kDUtp/zV5WAhU6Z++DfMqkdNllDJOltCeqqhrYT0uRJ/vPVdTAxb4K\r\nvCq3Z5r6Vb91ij4efacLfL1vgglcSe0w3+0=\r\n=ltNi\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.4.1_1663189285498_0.4697042911984928"}, "_hasShrinkwrap": false}, "3.0.0-rc.4.2": {"name": "twin.macro", "version": "3.0.0-rc.4.2", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.1.8"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.4", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.17", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.3.0", "daisyui": "^2.24.0", "escalade": "^3.1.1", "eslint": "^8.23.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^43.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^28.1.3", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.8.2"}, "readme": "<p align=\"center\">\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-light-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-light.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-dark-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-dark.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n</p>\n\n<p align=\"center\">\n    The <em>magic</em> of Tailwind with the <em>flexibility</em> of css-in-js.<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "4b3a63331a552dbd3f933099961653b7f3aa6d0b", "_id": "twin.macro@3.0.0-rc.4.2", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-eMXh9uhj8UO22zvhPBfC+gNt5yrpSGFeDU0+wzoN4wO+lEYQaWogvum7l0uQFUspzNFih3cWsgXaGxu4cCTkHQ==", "shasum": "07b4b5922ce658dd0564479afa2d63168f014233", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.4.2.tgz", "fileCount": 5, "unpackedSize": 121829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBhNfAMvoV+joWbwXl37DBqN6qyx5XJSwnYVNcPpCrgNAiEAzC7XC5uudqfU31ixSxEJ1AihxQT8PA5gBDuuzR47VvI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjK7QgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5rRAApDYo/50/DaRnEFvSmYbaAHzfq07nBaufguTqqn9bOFlz7mkc\r\nE8FOwWvlT3sogeXm+cynn3iHrNVev9p/DzSvuGOjbvn1a3thB0zBPmO3Mj9P\r\nYmwYdfY2qcXegPC4ubP2D+RNf+vEkk7ssQ6PjlWFUecC6XB/pcNdq52Fjr9B\r\nB8SGOeBX64EHnitGJUwJwqSwzb4JNJbm2vbdG/q6ytnq7GtKRuYEOW3SKx/R\r\n84s/vGv5D3SM/Sl6dvXhR5Y5zS1pUCkmH92AcsyBtthI8caicjBx6bUoSTf+\r\nOawAS1w8Hyn6oy5fGLtH/2dPGUNbXxGeXUpb0a6gWK/5FX2GXhEbLjrYtjMf\r\nl3pLKfMlspsCRvwzzB+a4lsyssGon4D32k9FDi0VV1k7EBSjCfUj2Ls3zVVx\r\naHZ9owV6vv65fW5JJS/9CwtPJBMAeoHFQ0tWZPz4V0zuH1vk1jgO/ZUhjS4+\r\nFqioNy40ekLdZcdae6zR9zeCl6E9HLSwWIWI9/xKViVLJ6OQbR0iwuip/Um9\r\nman/sedIhUFFPXk7C3YB4fitfdYSwk3AEbgUzbCnIpXJY6uduyudTSofHb/v\r\nXRl0GQnARofV29QfU3YIOtPavmnN9G1mEsIUQ8e8TQZPW7/nZtfr09G/GhdH\r\nt/lC0hcmnNnxus9vOAeFuuGnWAGzZNwSmKU=\r\n=sI8L\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.4.2_1663808544459_0.2511451978182986"}, "_hasShrinkwrap": false}, "3.0.0-rc.4.3": {"name": "twin.macro", "version": "3.0.0-rc.4.3", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.1.8"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.4", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.0.3", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.17", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.3.0", "daisyui": "^2.24.0", "escalade": "^3.1.1", "eslint": "^8.23.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^43.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^28.1.3", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.8.2"}, "readme": "<p align=\"center\">\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-light-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-light.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-dark-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-dark.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n</p>\n\n<p align=\"center\">\n    The <em>magic</em> of Tailwind with the <em>flexibility</em> of css-in-js.<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "566db868b2f070324d639f88a3c38226e32e8821", "_id": "twin.macro@3.0.0-rc.4.3", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-AhXBDrx34p8h4JcatDF+ffldHNYvexthPj4XPFG+N9pYup523bhsWfFemwMIWSv6u6OGW5s+g67JzU7Ks4NrCA==", "shasum": "ae4a5bc05927d66645ab116fe1b6f7f17e8d65f3", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.4.3.tgz", "fileCount": 5, "unpackedSize": 124680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGSNC2I+6bk68erydwBoNjmkm0rApRVT11pRo96uhNjBAiEAhKMsMqDxso/lp+yzqv6xLWI/8t2lfJRhjdSEHlUm70M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOQzlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZWxAAnSjPnryNFD6AVLAvoKY/6mNsCGAXNpvtIcHgn2MMwzGkAoYY\r\nwn0SgDn0B7Fy/JhSoMXEQ2x33ylEZzlMFmY84tuqcKzMCB5rI4VNJ1ypk43O\r\ndKIW3LDQMk9KWasdnDlhg4u8MgzOcWtZrh8smrP+21HmsNTMO4nJNkl02cJX\r\nz9sEXf0kX95GwYNpNvkmo8+X8o+F5ABGvaZzdRoV290SJt8NFsLZ+0MlNxG2\r\ntp2V/MVkgBfMAXD1hVWSZ1GatmrOJrR5fVKo1+e0IsEpL4CCnH9300FxLtXk\r\ntJAoCmBBbqZs1DmMhirx9I6hs1WrHfgHcDhpW3N0nlikaQ8nF91aY3nblAGR\r\nHyG5IVg3EtcUXiwR4kUGwLLHiaSxUXtceoAbzgCXQ9nMRNvQuolGxOnJ2Bsh\r\nqeGxoILSeLTNerUFTCHUj8p9bn3STuUIEWH25MTSWOmjs+HdjQSEMFUD+hC4\r\nNkDTCwdQ4T3+rjDCm0cbYI+pUwX63eALpRhSv0t03hjN+zxTQTMzjYc/n6BG\r\nM5SlVfiyc+0+9C3vJYtsvMGlWggM0VfJyNlarsfml0lHFJPlZui0lJxqtARO\r\nGWrZxu0G85vUNup/6FzuEA7wbsbxAaKPjfNZ9njxsBojHlKSR2/ajOJoMWR5\r\n4ifJPizx0IyEiX0XCMUKJUSipYxXyWG5Weg=\r\n=BJM9\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.4.3_1664683237624_0.20394726725469936"}, "_hasShrinkwrap": false}, "3.0.0-rc.5": {"name": "twin.macro", "version": "3.0.0-rc.5", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.1.8"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "@tailwindcss/typography": "^0.5.4", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.0.3", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.17", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.3.0", "daisyui": "^2.24.0", "escalade": "^3.1.1", "eslint": "^8.23.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^43.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^28.1.3", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.19", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "5.3.5", "tailwindcss-typography": "3.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.8.2"}, "readme": "<p align=\"center\">\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-light-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-light.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-dark-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-dark.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n</p>\n\n<p align=\"center\">\n    The <em>magic</em> of Tailwind with the <em>flexibility</em> of css-in-js.<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    <a href=\"https://codesandbox.io/embed/github/ben-rogerson/twin.examples/tree/master/react-emotion?fontsize=14&hidenavigation=1&module=%2Fsrc%2FApp.js&theme=dark\">Demo twin on CodeSandbox</a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript)\n\n### Advanced frameworks\n\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion)\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md", "gitHead": "80211db49e229dbd81ca0636faf42a4960873683", "_id": "twin.macro@3.0.0-rc.5", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-VqyWCcG6d/euWXaxH4LYVDF1HzzqRK6MjxRpwOgHzSW8jekw9U4bpyQlqJfQAEx4ztxHABZguyKVhyYsMuUhNQ==", "shasum": "cfd0643a1099a4bc14730b96778596f3e389949b", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0-rc.5.tgz", "fileCount": 5, "unpackedSize": 124678, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8PH4sue9A0PdznuXtULDRC66YTzW4jLM6jQNfbyaaRQIgMwTNhyhptV/TPCMR52AATG4++X/ghNlOU20XemS/g4w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOQ6gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXVw//Ybq4q3xnu7FZ3jdchv4PPVzU5Xq6GifIRNBCPMc27KocgI3k\r\nZtOKwEdrT3a2k55jD2sVkSi6fxKIpaCL8XNSaP33C7wBqk3xdqYTzulQV+ju\r\nGBh5S883rk0J/RAHNg6kdL1iFyTk3LqQ5i2raVBNsbcgHx6yc9zfhKHeOKNN\r\nKWhWbS+oh/7LHZC3ol3IPpuolUukPGvSY5Y8EWNPMZ68dAonGZaPBK9cmxTN\r\nESLpDGIivUBz4smZvQ9lNF33YTAy3P078RvOeWjXn1/xCq59iALr6lOrFDea\r\n4+T/GVSGaEDXxeY/PHa8GtMv6UCCy4tWBrYgJfazmF3dlp5ss/WEzJEP/18p\r\nufhBVEQ8Dt25FYlYy3RvFWbKGpaRYQpxTzV1huBN8t50Av9DwzT1Ufrutdts\r\nT/gRtxEzmMQxkz0Th3fdig0jFLVSWpFLNBCa1qPlWPFZ3Ce5n1qimWnodeJi\r\nM6xfVY/TqSsG12pKCXr1bL97NI6Byfk5v1lTnqQEGdQyAmXQifVPRj5+yF3o\r\n887Jbq8Qtzx9mocEiGd7oGyzyyD6deYcNxkBkpmWJnqIJ6I6aw+6Ru/sRaEe\r\nlCpKliEUUaRsrfiqvahmgm/QEB2Xp21trSpeJ3tLHuo/wrafbkrPdNb8UdiY\r\ng4pE7EIa09SA8zJTSaAeiwIkoc4/IrQv87Q=\r\n=HHpQ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0-rc.5_1664683680208_0.5518419184876979"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "twin.macro", "version": "3.0.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.2.2"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "7a309e14c7bc8518edd4a46c24ba51366a80f40b", "_id": "twin.macro@3.0.0", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-NZHhRmMaDDgJL3QDqDKGQkCMHd1gd9q2wFXYUz4nyRcGPhvOaxbR3SaQS2bi+oc6NZGqjlDjCtjRryafgaszHA==", "shasum": "f36a77c0fe8dbb3b18e2fa4895d0046cf238cf77", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.0.tgz", "fileCount": 5, "unpackedSize": 131733, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwOyvzZ0FOaB3I06adCMxtDyBF2kjlDucZs1uvSxUY/AIgdZ5K5dgpunSqIIbjxqsiRg/7smrXnbkHtzTeIU+yugU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjavelACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogSQ//W4B13vV3ey2Pyu/fk/F/GHM0m2aoS/X4mWSc8sKcX35KIMt2\r\n2Vd3cEEGGxQKGDgqVNEr5O/T0rffVb3PsZgMI5ZVFkXEnLqmPwG2/ucqxhFb\r\nSQQCUDuMJICs6zSR6mLkkDjs/+5ppk6gGb5B4MS1XtFgTBTTpGXEZ5sQVtAd\r\nBgU6lpQlgcCRw314aJ9rwMX7ukt6aCPo0NSuwg1K+JkTY2SUKYQYihuykNFZ\r\nXhmnXbxOfRsxg6jNgG4lRU2MReRD/6or3hPqtUQPXMWSjDmlL29CRvpmefIy\r\n2dgvlfdSpeB+lFQmIbpwM3ZEZyj1UinMtRAc7CbavKpoHSkZMtjnQo2SzP/1\r\nCfO2I+mBVmUKiXlB09lTogIB+WvCfvLyOMq5wsoAw4o5KLimqdERojSxkMZ4\r\ngIXOft6Qtlkfi6P/iAriuOkATRZ8ktxbYUDtDJ83jcqWDBv6JL87EB6Y4Gbp\r\nMstKTAKTKBv7nf58DXiXM6HMHeb24MgCCGVHGeMEWEkN27lcpI4QYPKe4iGM\r\n3P0dADkh/2mUzBh3uwTNKF0pTEmwxbNV2di1PUKK+XP6sSQGsjXRZp+w3kuZ\r\n/ekGO1fgK6nuBQWXE7sIhXIXv9docpBm8aOXg6czsSfUNM4NvO/WqmPfIAk7\r\nOmfu8Vop+aboa6qesshiWoOejB7gk4IfMiw=\r\n=cWyI\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.0_1667954597229_0.276922713769129"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "twin.macro", "version": "3.0.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": "^3.2.4"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "9f0922d8e5bca5aba11d793903c390028da70ea4", "_id": "twin.macro@3.0.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-0mpdfQYpfCyPYa37MC/WKHCLQrwlqe6b2bM+uKaku5Iwe6bxHvEhcfXp8E8TyAdTupenYTvI6nHZvrh7msSFkQ==", "shasum": "21fe8577d566838ddca532747ef5993fa19c1cfd", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.0.1.tgz", "fileCount": 5, "unpackedSize": 133288, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHo5dmLXMP1VhOlyWNozVMDIBAuuHIowDV5Ujgh2gEaKAiEAuSPr2ULGoUxKN4UaUSo8mWwSdOiZgTB2CaMKmKfFc2s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjeWOAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC9xAAgWItYgm5qenVyeFmchblH/nx5f0nLY/b7LKkAOewnmNbEs8K\r\nZVNJ+26HowuLxaLc0N45gzNWcHSPzhiy1WGXkZvYP1AArSKPdaBS/AAUeuiz\r\nH8Gd5GrbP4Y//3wCP9oGbz3Y5Iyx1MR8JPvjThIfilntvfVuPaRV5HDUdQAo\r\nnjt0f/VMPm0fG1qQTK300Dk4klCO3EWXsw1SRllxamFNMg8IxdBsbXkkRLZR\r\nRG3WjBM0wxzFyrPbXt7kG7Su517hxs0IO1Jw96MaDlkp7TnXvT6yWSPWXOxy\r\npLCrmAe3amv65+RG3bmNp7HjbR+dawUDz2w8X8gIT0bK0sYgn1PxV4zj3QI/\r\nI4zeMSphj4jQ38TjRJrF+qquslN0IAZLtXuAf8ld6pk6CWQmI5BPiF587lJe\r\ntgXy/7aIraXpR6mF56h37hD/oaNLQ++8PNWdrvG1/JcpzQtyW0LVN6t+p5Hj\r\nGIHkATekJs3NbmdqP62HAZJGQ1p4++r1aDRJr9h24fEix9Pc9HMy5/7UnJrt\r\n1y4s93OIqC4Af37Od9jRrI4WNqNL2pqFPc3OEAiN9J7rooeb0c9YWwKNjzT0\r\n0DFayo46NsZXKW7FtaL72PCVXLuT/I+6IC75ZPlBqL3WpSFyLiFaPdLSvOHC\r\nxdusNBkVOb38ANNp9PVUMoDPvmPFrQi7f+I=\r\n=QnlJ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.0.1_1668899712053_0.8886199403580579"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "twin.macro", "version": "3.1.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": "^3.2.4"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-import": "^0.13.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "6f8b0aae18bdcca97f6e96dc1203747322eb534d", "_id": "twin.macro@3.1.0", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-7DSR/xdvSaAqhTM4JptBgI+1IzXQR5v+2hp0CDMmHMjlm44IBEewU8MeqPlXmFgQI06zpLyJMaKRbJu96drAJw==", "shasum": "2056a83f7e4043c03587e664477e92feefc8df0e", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.1.0.tgz", "fileCount": 5, "unpackedSize": 137297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsQUEguMQUjLYxyg5tGZzmRWgdQbCUuxYdGFmfRUD2SAIhAPhvHNTPTudgN75s6u1iNvnT5lD4bnKaaU9ZUB66W9oH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjj7S4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWjw//QrCeQNf8mR0m8TLsIrcEZS0qK6ECGx9VFbxgR+wzav4M/S0q\r\nErp6i69MBfIwwUb0eBVIak7GhQRA20hDujNAUGzKjaRzUxPxqNwdHQC0W9um\r\ng43uWYujq/800WD3kwOEZhFN4rAmbvX+HwMWDqQQ5wPIqeHR/nhMY/cTKjQy\r\nGKmxnm/Rr/UGTdOFzjCVFj+YnKVe0hnKhfHgw9g90Ow26NLpxuW/rqKvveBM\r\nhDSSDRAzaan+85K6TRhQEqMscvb+RUlqfmSzOn7RJRP38mItdiTftzBxuE6+\r\noOneZR6ABi7fRdFH8eR8aIKcglvSyfUW5OiNnixkpbN4FzbO0pR6rJkI0Ld0\r\nhY258G86RU3RE8wTVUQvHvvkWBd4hn+pqNpmVTZHbmw8slJlqyz0NBIpTqY4\r\nJZDzfrcCt+fI8lGW3PRP9tTyRlELapVjp1OnxF1GWf20xjwKIBKMyBQerbVF\r\n/3pCgz9zsHvouQia+aaIJzfxrcJdlEmJKAM8GcdVsqVKTR7GC9WbWl0pROiT\r\nEExgoFj+dK7IR42J2H/mYRkEEOkr0ktefxd7zwfwxIydRItPLP7Z5C0oN5hd\r\nv7ka6GeOz817elH6SKZ9lIFkPpJrpf9OFTznrzUCgAX2p7oEHu1bcvgAryFs\r\nxLvzGm+Sv4yVOHQVMMik715f0G3f6+I3/II=\r\n=eEaC\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.1.0_1670362296157_0.26059895588025483"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "twin.macro", "version": "3.3.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.3.1"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "import-fresh": "^3.3.0", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.7.1", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "4eb928ba6c88205b70f949d606b952a9a1be2fb9", "_id": "twin.macro@3.3.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-74KJWNejkDhzVQnJ4tf54bEdkhvc/FpHtF3xm/pnR9KtK78+kuPpc+A6zwcBctOe3Yr1+bYGrYVMKMU1x0C/Eg==", "shasum": "f8e1b73d87de95bf3cc8b2809289754a85fa820c", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.3.0.tgz", "fileCount": 5, "unpackedSize": 137538, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCP8QGN8Ctgd7idqLcLsTbdWqQCsV3ajpyybAAYAMKHIwIhANQVVaeePlsJj86QGxSHHW4kuU0roqu4EZOMX7JcsKMZ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJkukACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKJg//edZV56qr5jUB/QAwZHCwmMvB/BY5Z1LXAsbsR/6fGOKd8AL2\r\n4qiUBSIjakPxp9r6XB49tlvjTUIwoWabSIHLWqDtRI7nvHd0Z1Adtw8Ab2iT\r\nyCdAYmjtT1A5ED4992c0etAn6ER7//mKTEj57kEkyil9ZgZmf88uZVHOgLL9\r\nDhP3rHjDGvGLrrxxgQE7TG79KjURIsWJ8Y8XObm1kX4HYhiSGg7tI6oc/1uj\r\nhaoRjxQzi2jYOknQ4c+Og37TkW0T4Sz4NIfkcTPKuUVZOfjO8fH0tdSnGON/\r\nmAkUxeUw1O2GOkAqhLugnwLcOCoJGnTFIlMpX1rQZBNszVF5g5x3Q2ZXrNrK\r\ndZbb0qkeO7jJlEolX0F5dBqV+u/BgYPuATaz2KPv9h5B379rUr1QhO1Bhy5e\r\nxjgDoKdwpYwEWubczfU45Kg6TYqquAdn1xk1pjCorp0lcs6vfgE4uqeUXL6E\r\nsxIWJiD4B+4OeMqHcGq7LsUG3NjH4rn5QAikr8ohieg8XqYmudV0s2Y/jT3R\r\npSIT8M16d221BAD1Wye83xKbLp/RQxQoOTMYzAo41NosfCQVvU7l2XM2448R\r\n+xXz1A33Rgykt1I06/bzw4e6Fha/pQxRHjLbQDupNU3vXX+EbtWUd+FpmCGQ\r\n0bZUAq4gH5u4mGVPEtQmudaY6MT8SAgp/Kg=\r\n=bAt5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.3.0_1680231332682_0.9670816412037377"}, "_hasShrinkwrap": false}, "3.3.1": {"name": "twin.macro", "version": "3.3.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.3.1"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.8.7", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "53033a445ab2375dc8e0c434613ab493e7a5dbbf", "_id": "twin.macro@3.3.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-V6Y1D2em218nvOp+a0iWBCe7kAOx8AEVh5F2NnEQbfyId29S9zjIAUo9gW2qTL6Fn03JAbGgrCZNHqp7alcsFQ==", "shasum": "293db55f325250331a56522fd37248c5b4bb18e6", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.3.1.tgz", "fileCount": 5, "unpackedSize": 134325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUotLl1wjnwj26WnfYAID6nzoTMaARQBBah1kkWOgKLgIgSge8p85+aDNIIKHaBywAoNGp+9mpGMzbv3zjnMCHZlI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOJVpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMYw//dwYNXBrscZOuiYPZlOOqGFN4STsq/Y1sAOCWyjYPpOpb8ULA\r\nQQVsVrlQPSb6TIO7sEzKRRMueyjGuvjGVDNJDKDQZXJbKZhlebhQVuub6eR6\r\nMC5FtTnhWCtiereAbKRkIAWGunDVRrz9hQlJnJ1qR8wHrimKH1HUpC27cv8R\r\n8OYonQcBXIYifd+wNKbKniYGaKv+lrbZeaRChW16bqBy9pe5MoNweNhnuly9\r\n2MmGI247E1ioSn2N6i/XIcWQyDFjHc4982L061JQFfbBRBgBre73S8mHTOyS\r\nz7tSjJRWy/ZJ87MZGLFg76rc3YPoukvORK4MmKlQerqqpVbEiPNdYuBQGehY\r\nGV2vKXVMEi6NqVRJPPYwix/0m8lOINSvREKw3cU3V3kliZquF9yR1SuPo9MW\r\nZ07jTPWGBdLEJyFkyiH9L09fJQS93ULu8JNdLtzm8jGnLWhFKkHgOrPS/Dap\r\n4suk44yIuMUi5Peg917EU7MnngE30ZZJsoc8e3Kx6l6mFTfHZc7kDWdBrib/\r\nFanO+KbBCCk8/KOTY1aU2YspKqAUT18At/nP5zn8n5/LF4/oM5edmC7q/DxV\r\njhEVpVtJhz25PuWKoSKhVibdk1MyNQ5SKT8LBYliCjvI8K5zrgKn88J19LVu\r\nrHqYG+3yniQ2ERNc4ZnwqqPbrkLLFPjB8zs=\r\n=6Mqy\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.3.1_1681429865501_0.6710523675953322"}, "_hasShrinkwrap": false}, "3.4.0": {"name": "twin.macro", "version": "3.4.0", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.3.1"}, "dependencies": {"@babel/template": "^7.18.10", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.10"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.8.7", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "b1258e71defc4de70e381d569fc2254f1094affa", "_id": "twin.macro@3.4.0", "_nodeVersion": "18.0.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-sOZN3PAlUpA/5ZITFkK1UjYhAKqKUGLrAbqviVagmd/B18Es6faozVdUceJTpMklD/dAjxhHCMPP2gwpJLCbyg==", "shasum": "810db7ec59cc3b252f7791c4939c586deb5c82cd", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.4.0.tgz", "fileCount": 5, "unpackedSize": 138350, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkfe6HuRCHJFfahaNldjM3A/YiRrbDMBcHta+DENxnJAIhALpspKhI4DLXnNkQQc0chjwdzFCUIJmw/nunscwIXWpr"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.4.0_1690274000139_0.9610748762424086"}, "_hasShrinkwrap": false}, "3.4.1": {"name": "twin.macro", "version": "3.4.1", "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "main": "macro.js", "types": "types/index.d.ts", "scripts": {"dev": "concurrently npm:dev:* -p none", "dev:macro": "NODE_ENV=dev nodemon -q --watch 'src/**/*.ts' --watch package.json -x \"npm run build:macro\" --delay .01", "dev:sandbox": "NODE_ENV=dev nodemon -q --watch sandbox/in.tsx --watch package.json --watch macro.js -x \"npm run build:sandbox\" --delay .01", "build": "npm run build:macro", "build:macro": "microbundle -i src/macro.ts -f cjs -o ./macro.js --target node", "build:sandbox": "babel sandbox/in.tsx --out-file sandbox/out.tsx", "test": "npm run build && jest && npm run test:types", "test:types": "tsc -b ./types/tsconfig.json", "test:update": "npm run build && jest --u", "prepublishOnly": "npm run build"}, "nodemonConfig": {"ignore": [], "watch": ["src"], "ext": "ts", "delay": "0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=16.14.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint src --cache --fix", "jest plugin.test.js"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "peerDependencies": {"tailwindcss": ">=3.3.1"}, "dependencies": {"@babel/template": "^7.22.15", "babel-plugin-macros": "^3.1.0", "chalk": "4.1.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "postcss-selector-parser": "^6.0.13"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/babel-plugin-macros": "^2.8.5", "@types/didyoumean": "^1.2.0", "@types/dlv": "^1.1.2", "@types/jest": "^29.2.2", "@types/lodash.flatmap": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.merge": "^4.6.7", "@types/react": "^18.0.25", "@types/string-similarity": "^4.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "babel-plugin-tester": "^10.1.0", "concurrently": "^7.5.0", "daisyui": "^2.38.0", "escalade": "^3.1.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-config-xo-react": "^0.27.0", "eslint-config-xo-space": "^0.33.0", "eslint-config-xo-typescript": "^0.53.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^44.0.2", "glob-all": "^3.3.0", "husky": "4.3.8", "jest": "^29.2.2", "lint-staged": "^13.0.3", "microbundle": "^0.15.1", "nodemon": "^2.0.20", "prettier": "^2.8.7", "react": "^18.2.0", "string-similarity": "^4.0.4", "styled-components": "^5.3.6", "tailwindcss-typography": "3.1.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "gitHead": "5ee856c257a4f89b6218e4e3c26591a757c05ba1", "_id": "twin.macro@3.4.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-bxGKTV4u/iGcQqHIugPaW5YSLJ5rIr56ay4Pjcr2Mbb037k341bQ+eWT8z3F7r8ZGTXjTD3uiuxos+qQRy4VjQ==", "shasum": "1b1919d4c539e4d83481ed29682bcc6adc0cf87e", "tarball": "https://registry.npmjs.org/twin.macro/-/twin.macro-3.4.1.tgz", "fileCount": 5, "unpackedSize": 138386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMaBcukDfrU7nWLugAZC4PYzRPr36GT/GhEH9j/E2MhwIhAPoFfVSZKTKI4FWrwMKd//TMoqGeMTUS+Qfbguc6EL6j"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/twin.macro_3.4.1_1705642373342_0.4696581738660657"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-02-19T11:08:20.398Z", "1.0.0-alpha.1": "2020-02-19T11:08:20.558Z", "modified": "2024-01-19T05:32:53.736Z", "1.0.0-alpha.2": "2020-02-22T06:32:57.343Z", "1.0.0-alpha.3": "2020-02-24T20:23:09.928Z", "1.0.0-alpha.4": "2020-02-26T10:34:49.457Z", "1.0.0-alpha.5": "2020-03-01T11:27:40.161Z", "1.0.0-alpha.6": "2020-03-01T11:59:14.493Z", "1.0.0-alpha.7": "2020-03-01T20:11:00.956Z", "1.0.0-alpha.8": "2020-03-29T11:10:05.364Z", "1.0.0-alpha.9": "2020-04-13T07:17:38.287Z", "1.0.0-alpha.10": "2020-04-22T06:44:18.693Z", "1.0.0": "2020-05-13T13:56:41.822Z", "1.1.0": "2020-05-20T02:28:21.574Z", "1.1.1": "2020-05-20T22:30:47.229Z", "1.1.2": "2020-05-21T05:06:42.078Z", "1.2.0": "2020-05-23T11:27:42.296Z", "1.3.0": "2020-05-30T12:02:39.156Z", "1.4.0": "2020-06-10T12:17:57.141Z", "1.4.1": "2020-06-11T22:57:17.711Z", "1.5.0": "2020-07-05T00:44:03.366Z", "1.5.1": "2020-07-19T22:22:50.213Z", "1.6.0": "2020-07-22T01:23:12.132Z", "1.6.1": "2020-07-23T10:39:37.875Z", "1.6.2": "2020-07-26T07:16:02.306Z", "1.6.3": "2020-07-27T10:16:37.175Z", "1.7.0": "2020-07-28T23:59:26.046Z", "1.8.0": "2020-08-30T01:22:54.306Z", "1.8.1": "2020-09-04T13:13:10.087Z", "1.9.0": "2020-09-13T08:59:23.944Z", "1.10.0": "2020-09-24T13:23:49.237Z", "1.11.0": "2020-10-17T22:30:36.495Z", "1.11.1": "2020-10-26T08:39:34.399Z", "1.12.0": "2020-10-28T10:37:06.872Z", "1.12.1": "2020-11-15T10:43:44.095Z", "2.0.0": "2020-11-29T03:43:46.473Z", "2.0.1": "2020-11-29T10:45:37.959Z", "2.0.2": "2020-11-29T12:09:58.380Z", "2.0.3": "2020-11-29T20:44:21.537Z", "2.0.4": "2020-11-30T11:16:26.601Z", "2.0.5": "2020-12-07T21:29:29.725Z", "2.0.6": "2020-12-09T09:31:33.399Z", "2.0.7": "2020-12-10T21:25:34.080Z", "2.0.8": "2021-01-06T11:08:04.086Z", "2.1.0": "2021-01-20T09:35:42.737Z", "2.1.1": "2021-01-25T03:54:22.702Z", "2.2.0": "2021-01-31T02:30:58.530Z", "2.2.1": "2021-02-06T05:44:31.887Z", "2.2.2": "2021-02-07T02:13:40.096Z", "2.2.3": "2021-02-10T04:29:47.252Z", "2.3.0": "2021-03-03T09:46:32.787Z", "2.3.1": "2021-03-23T10:09:16.070Z", "2.3.2": "2021-03-29T19:44:04.196Z", "2.3.3": "2021-04-14T10:44:48.588Z", "2.4.0": "2021-04-26T09:25:25.592Z", "2.4.1": "2021-04-29T10:38:12.636Z", "2.4.2": "2021-05-21T23:42:24.175Z", "2.5.0": "2021-06-12T07:41:07.290Z", "2.6.1": "2021-07-03T05:54:19.510Z", "2.6.2": "2021-07-08T08:05:15.809Z", "2.7.0": "2021-08-22T07:13:48.790Z", "2.8.0": "2021-10-13T01:43:05.376Z", "2.8.1": "2021-10-27T02:11:27.092Z", "2.8.2": "2021-12-07T23:59:18.539Z", "3.0.0-rc.1": "2022-05-02T21:09:04.176Z", "3.0.0-rc.2": "2022-06-01T01:01:30.398Z", "3.0.0-rc.3": "2022-06-19T08:35:50.281Z", "3.0.0-rc.4": "2022-09-09T23:13:33.231Z", "3.0.0-rc.4.1": "2022-09-14T21:01:25.696Z", "3.0.0-rc.4.2": "2022-09-22T01:02:24.644Z", "3.0.0-rc.4.3": "2022-10-02T04:00:37.839Z", "3.0.0-rc.5": "2022-10-02T04:08:00.376Z", "3.0.0": "2022-11-09T00:43:17.528Z", "3.0.1": "2022-11-19T23:15:12.211Z", "3.1.0": "2022-12-06T21:31:36.345Z", "3.3.0": "2023-03-31T02:55:32.863Z", "3.3.1": "2023-04-13T23:51:05.686Z", "3.4.0": "2023-07-25T08:33:20.353Z", "3.4.1": "2024-01-19T05:32:53.513Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Twin blends the magic of <PERSON><PERSON><PERSON> with the flexibility of css-in-js", "homepage": "https://github.com/ben-rog<PERSON>/twin.macro#readme", "keywords": ["emotion", "styled-components", "stitches", "goober", "tailwind", "tailwindcss", "css-in-js", "babel-plugin", "babel-plugin-macros"], "repository": {"type": "git", "url": "git+https://github.com/ben-rog<PERSON>/twin.macro.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ben-rog<PERSON>/twin.macro/issues"}, "license": "MIT", "readme": "<p align=\"center\">\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-light-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-light.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n  <a href=\"https://github.com/ben-rogerson/twin.macro#gh-dark-mode-only\" target=\"_blank\">\n    <img src=\"./.github/logo-dark.svg\" alt=\"Twin examples\" width=\"199\" height=\"70\">\n  </a>\n</p>\n\n<p align=\"center\">\n    The <em>magic</em> of Tailwind with the <em>flexibility</em> of css-in-js.<br><br>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/dt/twin.macro.svg\" alt=\"Total Downloads\"></a>\n    <a href=\"https://www.npmjs.com/package/twin.macro\"><img src=\"https://img.shields.io/npm/v/twin.macro.svg\" alt=\"Latest Release\"></a>\n    <a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n    <br>\n    <br>\n    🌟 New: Twin v3 now includes full Tailwind plugin support and more<br/><a href=\"https://github.com/ben-rogerson/twin.macro/releases\">Release notes →</a>\n    <br>\n    <br>\n    <a href=\"https://stackblitz.com/github/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript?file=src/App.tsx\">\n      <img\n        alt=\"Open in StackBlitz\"\n        src=\"https://developer.stackblitz.com/img/open_in_stackblitz_small.svg\"\n      />\n    </a>\n</p>\n\n---\n\nStyle jsx elements using Tailwind classes:\n\n```js\nimport 'twin.macro'\n\nconst Input = () => <input tw=\"border hover:border-black\" />\n```\n\nNest Twin’s `tw` import within a css prop to add conditional styles:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && tw`hover:border-black`]} />\n)\n```\n\nOr mix sass styles with the css import:\n\n```js\nimport tw, { css } from 'twin.macro'\n\nconst hoverStyles = css`\n  &:hover {\n    border-color: black;\n    ${tw`text-black`}\n  }\n`\nconst Input = ({ hasHover }) => (\n  <input css={[tw`border`, hasHover && hoverStyles]} />\n)\n```\n\n### Styled Components\n\nYou can also use the tw import to create and style new components:\n\n```js\nimport tw from 'twin.macro'\n\nconst Input = tw.input`border hover:border-black`\n```\n\nAnd clone and style existing components:\n\n```js\nconst PurpleInput = tw(Input)`border-purple-500`\n```\n\nSwitch to the styled import to add conditional styling:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input(({ hasBorder }) => [\n  `color: black;`,\n  hasBorder && tw`border-purple-500`,\n])\nconst Input = () => <StyledInput hasBorder />\n```\n\nOr use backticks to mix with sass styles:\n\n```js\nimport tw, { styled } from 'twin.macro'\n\nconst StyledInput = styled.input`\n  color: black;\n  ${({ hasBorder }) => hasBorder && tw`border-purple-500`}\n`\nconst Input = () => <StyledInput hasBorder />\n```\n\n## How it works\n\nWhen babel runs over your javascript or typescript files at compile time, twin grabs your classes and converts them into css objects.\nThese css objects are then passed into your chosen css-in-js library without the need for an extra client-side bundle:\n\n```js\nimport tw from 'twin.macro'\n\ntw`text-sm md:text-lg`\n\n// ↓ ↓ ↓ ↓ ↓ ↓\n\n{\n  fontSize: '0.875rem',\n  '@media (min-width: 768px)': {\n    fontSize: '1.125rem',\n  },\n}\n```\n\n## Features\n\n**👌 Simple imports** - Twin collapses imports from common styling libraries into a single import:\n\n```diff\n- import styled from '@emotion/styled'\n- import css from '@emotion/react'\n+ import { styled, css } from 'twin.macro'\n```\n\n**🐹 Adds no size to your build** - Twin converts the classes you’ve used into css objects using Babel and then compiles away, leaving no runtime code\n\n**🍱 Apply variants to multiple classes at once with variant groups**\n\n```js\nimport 'twin.macro'\n\nconst interactionStyles = () => (\n  <div tw=\"hover:(text-black underline) focus:(text-blue-500 underline)\" />\n)\n\nconst mediaStyles = () => <div tw=\"sm:(w-4 mt-3) lg:(w-8 mt-6)\" />\n\nconst pseudoElementStyles = () => <div tw=\"before:(block w-10 h-10 bg-black)\" />\n\nconst stackedVariants = () => <div tw=\"sm:hover:(bg-black text-white)\" />\n\nconst groupsInGroups = () => <div tw=\"sm:(bg-black hover:(bg-white w-10))\" />\n```\n\n**🛎 Helpful suggestions for mistypings** - Twin chimes in with class and variant values from your Tailwind config:\n\n```bash\n✕ ml-1.25 was not found\n\nTry one of these classes:\n\n- ml-1.5 > 0.375rem\n- ml-1 > 0.25rem\n- ml-10 > 2.5rem\n```\n\n**🖌️ Use the theme import to add values from your tailwind config**\n\n```js\nimport { css, theme } from 'twin.macro'\n\nconst Input = () => <input css={css({ color: theme`colors.purple.500` })} />\n```\n\nSee more examples [using the theme import →](https://github.com/ben-rogerson/twin.macro/pull/106)\n\n**💡 Works with the official tailwind vscode plugin** - Avoid having to look up your classes with auto-completions straight from your Tailwind config - [setup instructions →](https://github.com/ben-rogerson/twin.macro/discussions/227)\n\n**💥 Add !important to any class with a trailing or leading bang!**\n\n```js\n<div tw=\"hidden!\" /> || <div tw=\"!hidden\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\" }} />\n```\n\nAdd !important to multiple classes with bracket groups:\n\n```js\n<div tw=\"(hidden ml-auto)!\" />\n// ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓\n<div css={{ \"display\": \"none !important\", \"marginLeft\": \"auto !important\" }} />\n```\n\n## Get started\n\nTwin works with many modern stacks - take a look at these examples to get started:\n\n### App build tools and libraries\n\n- **Parcel**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/react-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/react-emotion-typescript)\n- **Webpack**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/webpack-emotion-typescript)\n- **Preact**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/preact-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/preact-emotion) / [goober](https://github.com/ben-rogerson/twin.examples/tree/master/preact-goober)\n- **Create React App**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/cra-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/cra-emotion)\n- **Vite**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-styled-components-typescript) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-emotion-typescript) / [solid (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/vite-solid-typescript) 🎉\n- **Jest / React Testing Library**<br/>[styled-components (ts) / emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/jest-testing-typescript)\n\n### Advanced frameworks\n\n- **Next.js**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components) / [styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-emotion-typescript) / [stitches (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/next-stitches-typescript)\n- **T3 App**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/t3-styled-components-typescript) 🎉 /\n  [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/t3-emotion-typescript) 🎉\n- **Blitz.js**<br/>[emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/blitz-emotion-typescript)\n- **Gatsby**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-styled-components) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/gatsby-emotion)\n\n### Component libraries\n\n- **Storybook**<br/>[styled-components (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-styled-components-typescript) / [emotion](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion) / [emotion (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/storybook-emotion-typescript) 🎉\n- **yarn/npm workspaces + Next.js + shared ui components**<br/>[styled-components](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-styled-components)\n- **Yarn workspaces + Rollup**<br/>[emotion](https://github.com/ben-rogerson/twin.examples/tree/master/component-library-emotion)\n- [**HeadlessUI** (ts)](https://github.com/ben-rogerson/twin.examples/tree/master/headlessui-typescript)\n\n## Community\n\n[Drop into our Discord server](https://discord.gg/Xj6x9z7) for announcements, help and styling chat.\n\n<a href=\"https://discord.gg/Xj6x9z7\"><img src=\"https://img.shields.io/discord/705884695400939552?label=discord&logo=discord\" alt=\"Discord\"></a>\n\n## Resources\n\n- 🔥 [Docs: The prop styling guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/prop-styling-guide.md) - A must-read guide to level up on prop styling\n- 🔥 [Docs: The styled component guide](https://github.com/ben-rogerson/twin.macro/blob/master/docs/styled-component-guide.md) - A must-read guide on getting productive with styled components\n- [Docs: Options](https://github.com/ben-rogerson/twin.macro/blob/master/docs/options.md) - Learn about the features you can tweak via the twin config\n- [Plugin: babel-plugin-twin](https://github.com/ben-rogerson/babel-plugin-twin) - Use the tw and css props without adding an import\n- [Example: Advanced theming](https://github.com/ben-rogerson/twin.macro/blob/master/docs/advanced-theming.md) - Add custom theming the right way using css variables\n- [Example: React + Tailwind breakpoint syncing](https://gist.github.com/ben-rogerson/b4b406dffcc18ae02f8a6c8c97bb58a8) - Sync your tailwind.config.js breakpoints with react\n- [Helpers: Twin VSCode snippets](https://gist.github.com/ben-rogerson/c6b62508e63b3e3146350f685df2ddc9) - For devs who want to type less\n- [Plugins: VSCode plugins](https://github.com/ben-rogerson/twin.macro/discussions/227) - VScode plugins that work with twin\n- [Article: \"Why I Love Tailwind\" by Max Stoiber](https://mxstbr.com/thoughts/tailwind) - Max (inventor of styled-components) shares his thoughts on twin\n\n## Special thanks\n\nThis project stemmed from [babel-plugin-tailwind-components](https://github.com/bradlc/babel-plugin-tailwind-components) so a big shout out goes to [Brad Cornes](https://github.com/bradlc) for the amazing work he produced. Styling with tailwind.macro has been such a pleasure.\n", "readmeFilename": "README.md"}