{"_id": "escodegen", "_rev": "144-f7956e5f8675e73c810ffde39d602e09", "name": "escodegen", "description": "ECMAScript code generator", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.0.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "_id": "escodegen@0.0.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.7.4", "_defaultsLoaded": true, "dist": {"shasum": "8d8f398c0ac59e8df11385866bf578e0c449502c", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.1.tgz", "integrity": "sha512-uYSzkaX5dSbtmnEar7cnSVhHlKaLFA8vk36SuhWOoPt+F4niseJT0vrYcpxcLQvVOopF3e+vrqWJHtSBEv2sGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAf2RlANXDBM9+acyxYJpX6sx4Ew/xYKu6FGiRkdy+LqAiBXDTceo+B7nflB82Yp2vXR68ov/gCSkNJ+vGDF+Lr93w=="}]}, "directories": {}}, "0.0.2": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "_id": "escodegen@0.0.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.7.4", "_defaultsLoaded": true, "dist": {"shasum": "0f475f93756447862e52d14907239a9e9dca1fdc", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.2.tgz", "integrity": "sha512-gk0m1vgkl6OmueMpXk8ZvpI1Z4K/xuj1Ns1daxk1sEMJq39ygsmcvwOdubBRYAoTwXnl6393lUkHd+HXfuMy4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3pwSgeqQRi+q1CTcDe9rW/XgYF/a2dH3DSsIzepsq+AiEAlps1r1DvRz+QF9KJ5uH02maDKPIGO7yuAlUuXAmHpOI="}]}, "directories": {}}, "0.0.3": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.3", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "_id": "escodegen@0.0.3", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.6.15", "_defaultsLoaded": true, "dist": {"shasum": "7aa5718c3e1b2456daea8f715557466a40bdc3b3", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.3.tgz", "integrity": "sha512-95OAJlQt96PdexOHy8XTzhMf+n2Vnn/QViz6P9m0wqhjwpd2nj8JJdrd+FcXSjU6IGaHtQ05Znu+LRqeOp8VQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmP/qIhmmz8CLEqadKmzK58guTBSp80aEvfMhzcB3owgIhALYE/tHD/GI3r/ICV6/JUGFqJp9x3t6if7oah8be/wOz"}]}, "directories": {}}, "0.0.4": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "_id": "escodegen@0.0.4", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "170b87ec38e47f160062c836a4beef14617fbc4b", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.4.tgz", "integrity": "sha512-+hrzCcZmJ1b/wKGXUnKaUp0rS9g1TNLdj8O2VykbxQ31ZhgLLEfVhgoEPmw8aMfiRdmz7AhFCLmAIiwE2O0Ghg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDV6tPF6mj4chBKIIYCGyruUn+m8OLZjjaobwOOwx9TTAIhAKxzFfQJQeWflbyqKRy/NwSnfXgs7V5V85TEF0J+eCtj"}]}, "directories": {}}, "0.0.5": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.5", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "_id": "escodegen@0.0.5", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "90959b349513333031ff2ea377ecd467529d460b", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.5.tgz", "integrity": "sha512-mVVWrZe8s9Sze/QkszeiizaeMFsYF2m3b0Vo8frdFnfBNQ6ULyIIvMr3oA57lwIONDXRHA9atMkX+8xILd0MTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDsrPxmiNfWnpa5wUc2AOgUUckrYEbMU8H0UzEx5jSTMAiBGXKgt5zwp8dMNF2EwrGqqIVBLCmEc08ZGzijzpWCSxg=="}]}, "directories": {}}, "0.0.6": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.6", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.6", "dist": {"shasum": "63eadaea293ec2a99e1c411b79c98d5d01326be1", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.6.tgz", "integrity": "sha512-Me1BanArucRWfAWjWvPKreToERv7RVPRnQ9XSk+gHnc6zz5xcozPZngLDnivJX9BSO/Ia3DDyjQfZxstsTmiww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHRggeO92KBCC6ALOOGvW5kWbobUqXma1TVfn+9FovyAiEAmyDIZ0r7DMIl6OjzmjMxXuTFSO9hp6lqZ9RtxH4yIX0="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.7": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.7", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.7", "dist": {"shasum": "8ca53d6cefcf315839b5c05c0f23db423fa5d9ee", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.7.tgz", "integrity": "sha512-DP1uzv+7wtvwgAGmBLGOYVTuTwUAdm66YG8LXduroU4MEMRmbF42AsKgwBv7JwKZqqV6/bQNswVAYbq72qdRLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCk7bVVbstO9Nr3pjMN3YjfBh8fnls+HefBojHeaO03AIhAONCT6vSDWAoOIhvD0Q79ucsFzSz2BhtG18yKfmagV2X"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.8": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.8", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.8", "dependencies": {"source-map": ">= 0.1.2"}, "dist": {"shasum": "b21b9acc94b9605a2ab5284e5cd7385e8ef9c802", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.8.tgz", "integrity": "sha512-uLCRu1ry8QTWkNLCcximuxgKMiUsD/IklwtAXvjLwfNbhR43X7fjdB6LuUQ+DhlqCrcrOdNE3ycxTbpeH7MAgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCY9WvncYEPK9c2nEVfIERX0X6uoQnys4K731ZxH19v3wIhAPIpy+OnW4gAstQleskdDC1aohk1PBuitjVbJcq99urR"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.9": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.9", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.9", "dependencies": {"source-map": ">= 0.1.2"}, "dist": {"shasum": "a3449ea1290dcb235f15469f310b9f983497f2aa", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.9.tgz", "integrity": "sha512-jjZme9ZY5W0Ro8mQPYdpAoVT0OZOsTseoRbABgadlLOXiNQ9+bT5Pg6Si7un8j19x/pT8dskUKUV754fAHIXnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVzqlnMPe3HQWOuRD25kou8uCCkKlQgtB6ilRMvIUISAIhAJ0Yf1uQpSqT+wiCe7aqFgvRv/WwLMCmotG82mI54WhP"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.10": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.10", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 0.9.9", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.10", "dist": {"shasum": "80a812c2fe7d109236be9e78705916e6df69e6f7", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.10.tgz", "integrity": "sha512-zl4Xc05lD3dPueROVpWVtRHgBX2yHfsI/2+lkBZ70fTI2PCrAvlTr4/dldrCdMcawSC9InI7vjKB6GYqVfLWyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZf+4iSF05+JXkVE5az0PPGShbfyLa5P3ylwSXeeaOCAIhAIHf/DGi5a5Wz6O9wiJUPq8U5qy3oO0fJC9CllAg6dw+"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.11": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.11", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 0.9.9", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.11", "dist": {"shasum": "0bf3ae177ea0c6bc8dd63460978e0cf8fed99f25", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.11.tgz", "integrity": "sha512-NgYtccnkqqv7BkzRSJv/n21VOpxptB23XbWjV9hNj0ZBwjFu8hsPaD0eTM6Y2VvyFYUtvE6wfBqQ8tHlPZBvVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEleysmMwjr9HxZP3MZS2uGDPorXwAxhk5vo6Y1k+UR3AiAZcq/BNa0ZdXQj4vhsU8ueTmJt5Y1C5kx4mInGkyBvfg=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.12": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"escodegen": "./bin/esgenerate.js"}, "version": "0.0.12", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 0.9.9", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.12", "dist": {"shasum": "0fe67df78b9459b1308a3d93acea55bff8131e02", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.12.tgz", "integrity": "sha512-JBhqQpxOgPvejn0BdAU2fOms8DnrYPPWyQj3W6XXdVf6MkLX2nPH5EhoCyKRHmMSP0x8WM2z0Gkzg6+u20/z/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBnf6RNg42K4XOdqcJ37DKaGR5gPJJiIzwZw5/7w75KgAiEAjraxvge4gh2a8sJDwb+EtCrrdqgdNaTjUy0ha6osZxQ="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.14": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.14", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 1.0.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.14", "dist": {"shasum": "e05484bf41df24cb871299475088fc634cfdf885", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.14.tgz", "integrity": "sha512-JSBQHHFVub6OWSlnJBZdyoh6T95Q1oRJKpOkC9mFzYL6dMXXJkThJrMDfpa9Ck4/sqjr7qRus6AQ3qACl4vjlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKLiETr7NHujgXlFipKD45whovCEK9ycNBgHIYc9IBeQIhAJfkS2WayH61RnGxSkXt6SW0sOVshIf2evwZS/2BX/CE"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.15": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.15", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 1.0.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js"}, "_id": "escodegen@0.0.15", "dist": {"shasum": "ffda9cb26b70b34f7cc19f1d88756539afb543bd", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.15.tgz", "integrity": "sha512-ALdTVKqhEj6bDf4pC0fXWRiUvhyJSJ0Ic8AdPuMGKckoa3gMXG+MPduI8TlDoSS84swDkfXeogXapbkErAHvDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEea9TbZjsmA65+ixL25HirXm1CqiyepKz/OaC6SOeenAiEAxZWH+xC24BSngol0jy+JHmHi2VAQuKVlMRaHNwPlLwo="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.16": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.16", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": ">= 1.0.0", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.16", "dist": {"shasum": "81410ecdb666e816c6b9cb707722272952ea7c9f", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.16.tgz", "integrity": "sha512-M7kZPKUZiaRfzblY0Cw7UPj4Mb78Ke5h1IfA4BXyEKyByMLcb5Ocm0XgX12iSk8/FmqMZPpcALYYEazNxC8LdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA06tEE479HnAQULDsGLxN/RanIcstNzdS9FvAdjVbCuAiBVNtGhLaDT8zcIK7Jz8iJWzz/Z8If0yTksBe5x24Ra3g=="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.17": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.17", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.17", "dist": {"shasum": "1e78d17df1004fd7a88f2fed3b8b8592f3217f9c", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.17.tgz", "integrity": "sha512-muX87X0L2dvVJ4ZPKAJLQlcyg2qhl6ps8Ow8vOAcuoFIrZLtXNMGfjnD19LSTrZ1SBHQsI+tvD8YlsJqHhfXrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxSgDMr3Rqjz9oUN5Q1MtAAEk6o0rZhHBmrmLvxkDpSAIhAIKuMM2fty7bSuAwdnW9PdRt/HrSvjd3lWX89nCDzxSa"}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.18": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.18", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "falafel": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.18", "dist": {"shasum": "4128e5a45b12accd16595297c9484b228f05424e", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.18.tgz", "integrity": "sha512-lkNL6mTgTDtpuR90EAjS3oBAkZI9wPvbC0s1ziCXG4WVxxCpcS8MF1C44B6FKoLyZXzHn0qH29sgDmLUyj2bdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC10m0WsmUrEXpBx6x1+iVqxQOIvmfdUrF3M/GTV6XuJgIgAngeq8E/2u/RdW9Ta/PCYm9j0ybLFjhybfpnOKppx74="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.19": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.19", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.19", "dist": {"shasum": "f05f86dfedd2dc96e35f53d3bc2cbcc61996e6e6", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.19.tgz", "integrity": "sha512-REz9y+YRLsYLyipygweKuAycTV8zKlghKOBFq63iB6mNfnwq74XEbnALMFmq6FJTcctWPUqJJcy6dpcqi/fQgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIESp3Aa0ClNlYzS6fHd18StYig99TBEr99pTnK84PPFxAiEA/n/zxIVZ6zkVNDuFLwt3yHHnB6WzNyUUBGakDdgCLPM="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.20": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.20", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.20", "dist": {"shasum": "82e208fedece972fa5898a80d325e30b091eefe8", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.20.tgz", "integrity": "sha512-VMsRne6eTN6WRSK/DTnzHGUyvMxh/wbLjneokomyBYGGRC5hikUPqnw4hkjnZ0WB2wt0A7ioqmJbcpJCyiqHRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdvzUL1+/vVDAbRhKzc4nHXFay//u0BF3/IJdZkzbTwgIgbB46xDZYOlmFHjAygjcFMj6ZmQrxwu5LmglhFgV9XXw="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.21": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.21", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.21", "dist": {"shasum": "53d652cfa1030388279458a5266c5ffc709c63c3", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.21.tgz", "integrity": "sha512-BQL5g+BqyrM5HRAKt4Q4YuH9CqiEcIHWSJ8fg2PRNkGkXn/LgzeDCZzDDSX4UiljSAHgXaHgOZREQ2xOigbLzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB7UrDjWC0YRRb9eV+Bntqer/45/8snGMg9L7l2c49i2AiB2F+20Lt8A12jiFWFWV+Q8iDcasM+DVkd1OIafVWWaPA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.22": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.22", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.22", "dist": {"shasum": "4c9ba6e9f353aaba3eccdb045e93f9543cbb739d", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.22.tgz", "integrity": "sha512-HHdarkaFTSG1QhSV7mwM6HO5hr71T9/cgWaJuUcpt9Sx0rTKCPmK55jVpZ/mCaxSMxC7AAUCY7aEe2S31vjGag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtUPxTZFFGNI4bBQFkinRuN5MzE/NW9nn+rRqrt3m7sgIhAICtCI5iYR+nfGGxVh6GY2EX0jr02Z+zY+OAQyXVtE+p"}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.23": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.23", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "_id": "escodegen@0.0.23", "dist": {"shasum": "9acf978164368e42276571f18839c823b3a844df", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.23.tgz", "integrity": "sha512-IKgIOsZGaPEj6oD2MDbXsB5zC5U9y+D0KnnJqym7POG8mCGeuGKh1SF7ECxXx7ZFJYStEz4inNMGFTmCmaSlew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkk9Rl3hdmo9acs0gn9IEyos5AduxZYZMwQLO4MMtchwIgXx3xfPtVC5nUThUzZInXCQO3vFIHxaKHqYYEvQdErEY="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.24": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.24", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~0.0.4", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "browserify": "*", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.6.0", "grunt": "~0.4.1", "grunt-mocha-test": "~0.5.0", "grunt-cli": "~0.1.9"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build": "(echo '// Generated by browserify'; ./node_modules/.bin/browserify -i source-map tools/entry-point.js) > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@0.0.24", "dist": {"shasum": "b6ce32abbf9458249c40983d9b866ac0457a8a1d", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.24.tgz", "integrity": "sha512-AXKDA3i/liprBN2M3bfdXKyVbP3m7YgA7RNLgnZZap5jLN/yA/wsRYkZT/946FNHoa9oo2kYTueUF7pjYGvNDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9AvPLJzfBBAcAvog6ukUkxfLSomXgZgbwD6i6gs3TMAiAujzIfyt56hBbIx9e9Ry8/XVq9NGldaiXmaUe/XIIPYA=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.25": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.25", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~1.3.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@0.0.25", "dist": {"shasum": "7a177194df3d0ebe4d39a952351e3f0fc977b77d", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.25.tgz", "integrity": "sha512-i+X8/Avld+sK8KZv8dk1wnARjo1ySMSU+Nd/3BwhyVFoRV8vi6LHLK0S5POTs7dl1NJOXjivVjT0HENlVgBCWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTyi6bX+ae6bSVlQ0FdQNRJu0Pyj1JSTaISt9Rn7E3ZAIgXll0uI1h5MRKn3+MS5o7T53xvcWDGMQsp7VyrAaZAjQ="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.26": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.26", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~1.3.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.js"}, "_id": "escodegen@0.0.26", "dist": {"shasum": "9b353e7c5a7b7c35fbadb8e4ecfcd8d1d218547e", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.26.tgz", "integrity": "sha512-rAWF6r5EE3GiB3Thfg+wIQnG9pGPoxCJEPC951Uk5vfTJrBDL7UQLgCeE0TQ/1HIfYrArdOpK+22cRNWDP5w5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMhc9RNhYlW2zEOaOk4JICKDrxK3WjGFWMiW/J1HdaHwIgGrfMOHDbmnKtoaRt7EYo7slefZD3b3CcoGa1K25QIr0="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.27": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.27", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~1.3.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@0.0.27", "dist": {"shasum": "3ba2c3834cc5c1823793ef46a4554d54f4b5a810", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.27.tgz", "integrity": "sha512-9tQOszxIfr2BnsHAiDuFxqPyb3pftSVs+PikPSlufhHSdhit8xtYKU08+ahjUnRfHFldCbOfAi9MjdxKCV8Z/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzGo0MXx6vE2Tq7ftTB/4O+nHlgia5Tygr7ctr0dTe0AiAoYTL9PboyeEw5hdNJeRR96aCThGbZp6iGRXyugYyGjw=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "0.0.28": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "0.0.28", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~1.3.0", "source-map": ">= 0.1.2"}, "optionalDependencies": {"source-map": ">= 0.1.2"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@0.0.28", "dist": {"shasum": "0e4ff1715f328775d6cab51ac44a406cd7abffd3", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.28.tgz", "integrity": "sha512-6ioQhg16lFs5c7XJlJFXIDxBjO4yRvXC9yK6dLNNGuhI3a/fJukHanPF6qtpjGDgAFzI8Wuq3PSIarWmaOq/5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjpAc9l6i6LcFs5vl0wwZdHqwI1uzFfdMQGnAix0JkNAiBwaAxwo6/Z4VFe1FSk8OKj5Ya4iTs3yQIPfzrAalvnFA=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.0.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen.html", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.0.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.2", "estraverse": "~1.4.0", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.0.0", "dist": {"shasum": "8428e45aec9f8d15ccd57859a83c34a91688fd2d", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.0.0.tgz", "integrity": "sha512-EYZFe9xy+sdynf/MNJtLxUD1+N2GplyMkJZW7jcy57k+HlOpYOtgGuw7SC6Cz2oC7n+G1jAJBufSjOOYRJnoTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDi8FqRZtQeLLh3dugvmzkknOJev+GtzHU8kT6ghOlIlAiEA3CSWUGfSqRhXbLSx34Sd7J4MhN73ttXxn0+JCadHX90="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.0.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.0.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.0.1", "dist": {"shasum": "84c92c4a07440271b90e6b78e620973bf721226e", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.0.1.tgz", "integrity": "sha512-DFBGRKWS0ngLNtLiJR4F5MwRZnd2BuOIIqhjh2TR38XxvHP9xf3HCJoZeb4u5IW/XNDOs3Ptk3IyyqrP5/gL+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgQJXyYLdaqKBpj0AEynCKQf6I/s/yZ1G+N3GzHQtNtAiEA8WdiWrUUUmYfCc5EX/kWEX6eniTq9+H2ToFHQiHUdDk="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.1.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.1.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.1.0", "dist": {"shasum": "c663923f6e20aad48d0c0fa49f31c6d4f49360cf", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.1.0.tgz", "integrity": "sha512-md+WjA8K+DJELEYe0n4XAOE0XbUYfw2rzb8T+nhZ19OnQxlh+0jMLS6d+z2oqWugIh3uYKu1+KJh6QKeoogLzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3s8Om5Rv73sNvM1+uQO9w0NV35m6WetZMjMBWJ3RZPAiEAzgDT4UzTSB9H0bnddUEe8z+cSNuPW6XqROO1bkPe1OY="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.2.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.2.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.2.0", "dist": {"shasum": "09de7967791cc958b7f89a2ddb6d23451af327e1", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.2.0.tgz", "integrity": "sha512-yLy3Cc+zAC0WSmoT2fig3J87TpQ8UaZGx8ahCAs9FL8qNbyV7CVyPKS74DG4bsHiL5ew9sxdYx131OkBQMFnvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFN2+ZNNFjCSe8kKilWjus87E/uLph6cPwh8f5/f/lV8AiAbAnRPod49PsD2MmcIAAn7VsRh2r2k8DPaORIXKJCXmg=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.3.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.3.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.3.0", "dist": {"shasum": "9d7b4aa74aa32f300474381c098f930eb1e9973a", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.3.0.tgz", "integrity": "sha512-IG2NoEo/CfBmD9QE9JOQQ8Orn2a54X/FW4yd/hQHPVre7D9V5JvqY1lhMzmkqB9eUpoTxMNyuAHgPTMMcHPjWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAbtX2nx5ZR8phI8gIu6Ef9TlyjXiEJRGYZ4v4IXBffZAiEA9eunLIwZAMZu4pEufPuAc2X9K6Fq44btXyyIpeKBpmo="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.3.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.3.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"estraverse": "~1.5.0", "esutils": "~1.0.0", "esprima": "~1.1.1", "source-map": "~0.1.30"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6", "bluebird": "~1.2.0", "bower-registry-client": "~0.2.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.3.1", "dist": {"shasum": "ed09273487261cf06fb46ef89f415ed960bb89b4", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.3.1.tgz", "integrity": "sha512-kF2kTN0Een4PzL1YQzAhUfFr7PFEr8gZimzLa2vhV0a1FuuSo0/C/il9ejvq2jOH8anpgJ9bu06HKp5T34sOTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwn/WRTH8b37tbjz1q+NirZcb65thmjN91dMvMv4Go+wIgQYKWyWhjgFoyfOcxY+F4ZjvXqU/YqrowteHeFpxrWvc="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.3.2": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.3.2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esutils": "~1.0.0", "estraverse": "~1.5.0", "esprima": "~1.1.1", "source-map": "~0.1.33"}, "optionalDependencies": {"source-map": "~0.1.33"}, "devDependencies": {"esprima-moz": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6", "bluebird": "~1.2.0", "bower-registry-client": "~0.2.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.3.2", "dist": {"shasum": "bb0f434dbd594f2060639a79b4b06259dd5372de", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.3.2.tgz", "integrity": "sha512-YWh8bZmKwvFjMNjhfageNFyWsc0cvQWKedZDd54h628q5LnB81z15ZIGxkpofP+2qOfXyVH61kJNEUZimfFpzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMf7nleUCM6EHz+XQHvuRv35QHhlvUnc9jIqg9t88AnAiB1lG8wlkm1DOsLAK0/7ckcdc6IkfwfE8348fyBrr6hCQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.3.3": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.3.3", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esutils": "~1.0.0", "estraverse": "~1.5.0", "esprima": "~1.1.1", "source-map": "~0.1.33"}, "optionalDependencies": {"source-map": "~0.1.33"}, "devDependencies": {"esprima-moz": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6", "bluebird": "~1.2.0", "bower-registry-client": "~0.2.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.3.3", "dist": {"shasum": "f024016f5a88e046fd12005055e939802e6c5f23", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.3.3.tgz", "integrity": "sha512-z9FWgKc48wjMlpzF5ymKS1AF8OIgnKLp9VyN7KbdtyrP/9lndwUFqCtMm+TAJmJf7KJFFYc4cFJfVTTGkKEwsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDynNls2NNySSIXeUNjnuNh4yDoGHSF7/LH39r0DNduyAIgAvrRaruS33kcFkTafO004KKDNk0VFf0ucKKyejZKogg="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "directories": {}}, "1.4.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.4.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"estraverse": "^1.5.1", "esutils": "^1.1.4", "esprima": "^1.2.2", "source-map": "~0.1.37"}, "optionalDependencies": {"source-map": "~0.1.37"}, "devDependencies": {"esprima-moz": "*", "semver": "^3.0.1", "bluebird": "^2.2.2", "jshint-stylish": "^0.4.0", "chai": "^1.9.1", "gulp-mocha": "^1.0.0", "gulp-eslint": "^0.1.8", "gulp": "^3.8.6", "bower-registry-client": "^0.2.1", "gulp-jshint": "^1.8.0", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "1c4365a80b7ca04a8234e2b9a211f6619f810159", "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.4.0", "_shasum": "4b5a8942261f0fd65ac57523298b78d7cbe6701f", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "4b5a8942261f0fd65ac57523298b78d7cbe6701f", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.4.0.tgz", "integrity": "sha512-dAGB6I7U0OtAZjhsLs0E1lz2Y20O0BVMvuzHgB5QjJrMEfCi5X8q14HRgiWXQYwbjUWVI6SdK+FYq3TYLe9svQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6AQc+H+/StUY9Sbv4IkHLYCCbuW9sfGxCFgXllXhBrAiEAiThvRpB01QuaNsJy/G7IsRD/fKvlL40xQwJIXwaCCxM="}]}, "directories": {}}, "1.4.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.4.1", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"estraverse": "^1.5.1", "esutils": "^1.1.4", "esprima": "^1.2.2", "source-map": "~0.1.37"}, "optionalDependencies": {"source-map": "~0.1.37"}, "devDependencies": {"esprima-moz": "*", "semver": "^3.0.1", "bluebird": "^2.2.2", "jshint-stylish": "^0.4.0", "chai": "^1.9.1", "gulp-mocha": "^1.0.0", "gulp-eslint": "^0.1.8", "gulp": "^3.8.6", "bower-registry-client": "^0.2.1", "gulp-jshint": "^1.8.0", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "87296a9ac34dcaf6567bd7e3d351e4a227b434dc", "bugs": {"url": "https://github.com/Constellation/escodegen/issues"}, "_id": "escodegen@1.4.1", "_shasum": "8c2562ff45da348975953e8c0a57f40848962ec7", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "8c2562ff45da348975953e8c0a57f40848962ec7", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.4.1.tgz", "integrity": "sha512-kFyKYxiIKKWqFyLsCDGeDn7nS+gPV9YL3Mj56nYWb3KDDW4frh4WVz65xE6apFDrfQvCsM9WoGHg0OQLmHPMsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVAzhWtprAVql7pnCN0tsl0M3Y+M5lHLR48JZeRwDCZAiAmwdmwdiJ2E59Etf3Q4D8XgiIanYulPvlYYPOza1UAtg=="}]}, "directories": {}}, "1.4.2": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.4.2", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.0", "esutils": "^1.1.6", "esprima": "^1.2.2", "optionator": "^0.4.0", "source-map": "~0.1.40"}, "optionalDependencies": {"source-map": "~0.1.40"}, "devDependencies": {"esprima-moz": "*", "semver": "^4.1.0", "bluebird": "^2.3.11", "chai": "^1.10.0", "gulp-mocha": "^2.0.0", "gulp-eslint": "^0.2.0", "gulp": "^3.8.10", "bower-registry-client": "^0.2.1", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "0e9d45147a49eba4b68dec70233496e3dfcfdac8", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.4.2", "_shasum": "6fe5e807623b94c01ea8612cf6a94a06b4e532c8", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "6fe5e807623b94c01ea8612cf6a94a06b4e532c8", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.4.2.tgz", "integrity": "sha512-SPj7QlWcY2GYxGOMyc0or+B7zD5ehwuOcUZAWhlY4hz5T3cPWaNu5CJfxH0CEW2CR56m5uzbSaSlY/P+SUQI+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFJaTE3qJ5bLyJ83b0ysaq/iLbCAXWPHbgWiyaaozi5HAiA8k/vGEeS0c3uN02b0doVtnNN63MOELdxxb8WQOSWuqA=="}]}, "directories": {}}, "1.4.3": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "gulpfile.js", "package.json"], "version": "1.4.3", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.0", "esutils": "^1.1.6", "esprima": "^1.2.2", "optionator": "^0.4.0", "source-map": "~0.1.40"}, "optionalDependencies": {"source-map": "~0.1.40"}, "devDependencies": {"esprima-moz": "*", "semver": "^4.1.0", "bluebird": "^2.3.11", "chai": "^1.10.0", "gulp-mocha": "^2.0.0", "gulp-eslint": "^0.2.0", "gulp": "^3.8.10", "bower-registry-client": "^0.2.1", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ee238d803cb10af46c7ce5a1aef9b57cf006c317", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.4.3", "_shasum": "2b2422bf18c95e2542effaabc0c998712d490291", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "2b2422bf18c95e2542effaabc0c998712d490291", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.4.3.tgz", "integrity": "sha512-V1lAamVFp9vriErDwKwfG+knak/7yvjfkOhK712U/DUTearm2Z3HQnmYkabyl6ahiEQ4kYxx+K/WE51w5pjlVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEYuAi9sbgfDeEDvpdHntA/PDFb5xjMaYbOPpKLj0eNGAiBFGPsCbhJm3PZRlheSsbgV8zB6IER0k3CtwGstixeZiA=="}]}, "directories": {}}, "1.5.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.5.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^1.1.6", "esprima": "^1.2.2", "optionator": "^0.5.0", "source-map": "~0.1.40"}, "optionalDependencies": {"source-map": "~0.1.40"}, "devDependencies": {"esprima-moz": "*", "semver": "^4.1.0", "bluebird": "^2.3.11", "chai": "^1.10.0", "gulp-mocha": "^2.0.0", "gulp-eslint": "^0.2.0", "gulp": "^3.8.10", "bower-registry-client": "^0.2.1", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "b63e648cbdbe8c098aac6d48f1b93c6b0c9e0ef1", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.5.0", "_shasum": "17fa592aa0d7b38d89e7de2e94b8200d58bb3261", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "17fa592aa0d7b38d89e7de2e94b8200d58bb3261", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.5.0.tgz", "integrity": "sha512-JzfOZHUQ4HK7OXL/47xqKXaex4KRbhbSoGpw7m4eVGZ8Ih0POoW/zRlaa8zRAMxQ1+bdS1F31GtlRbyxwPSAVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIELZQk/lQKQRcgEpOjlzasuj5VzMQkChqyMvojSWRclpAiBb+hc6LL1s7IQ2YnK0nJwRX+xeIHI1zHjh2BJf/k6gHg=="}]}, "directories": {}}, "1.6.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.6.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^1.1.6", "esprima": "^1.2.2", "optionator": "^0.5.0", "source-map": "~0.1.40"}, "optionalDependencies": {"source-map": "~0.1.40"}, "devDependencies": {"esprima-moz": "*", "semver": "^4.1.0", "bluebird": "^2.3.11", "chai": "^1.10.0", "gulp-mocha": "^2.0.0", "gulp-eslint": "^0.2.0", "gulp": "^3.8.10", "bower-registry-client": "^0.2.1", "commonjs-everywhere": "^0.9.7"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ef3a75be69a7a92daa5650bf81fbd1e4203083d2", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.6.0", "_shasum": "b7dbcbd6586915d9da977f74ba2650d2e82bccfb", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "b7dbcbd6586915d9da977f74ba2650d2e82bccfb", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.6.0.tgz", "integrity": "sha512-WgGX2nT56AzJLONUXsjtEaOEWZC2t1NvrnAdFcKSFJ72MVP0X94kcx2y99iw5cr/9gj7qKMyeNz3npFD/cXzKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjVXm/6zjgSLC3CfKzBiskJk81kMJvOc5OWXTx1KBi0QIhAP3yjGUZupxext/HdxJFpUFhT0y3+3eTErgmBsA3/YAs"}]}, "directories": {}}, "1.6.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.6.1", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^1.1.6", "esprima": "^1.2.2", "optionator": "^0.5.0", "source-map": "~0.1.40"}, "optionalDependencies": {"source-map": "~0.1.40"}, "devDependencies": {"acorn-6to5": "^0.11.1-25", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "esprima-moz": "*", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^4.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "1ca664f68dcf220b76c9dc562b2337c5e0b4227d", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.6.1", "_shasum": "367de17d8510540d12bc6dcb8b3f918391265815", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "367de17d8510540d12bc6dcb8b3f918391265815", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.6.1.tgz", "integrity": "sha512-n+5A2GpcotTT9EGSDrh6Okve4QU1tRp4ktb3CdutEs2e3ZEkY+NImmY+LclV4NVKOITdNsmOldSmqlcd7tlZzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBLmAt4wdhFnmIblpGLPfjo3xRCjvWN08jRSlhfHZYwjAiBBsKujSrQdq4qOBU95bW6PHaDXv5CnsNY4CAUU/SPZag=="}]}, "directories": {}}, "1.7.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.7.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^2.0.2", "esprima": "^1.2.2", "optionator": "^0.5.0", "source-map": "~0.2.0"}, "optionalDependencies": {"source-map": "~0.2.0"}, "devDependencies": {"acorn-6to5": "^0.11.1-25", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "esprima-moz": "1.0.0-dev-harmony-moz", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^4.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "5dabbc5441b396febd0afb9252a9afdfa7051657", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.7.0", "_shasum": "4e299d8cc33087b7f29c19e2b9e84362abe35453", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4e299d8cc33087b7f29c19e2b9e84362abe35453", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.7.0.tgz", "integrity": "sha512-PlnmJBW1Atkkf/21f55xNDTCMbW4MmatvDdRTFg1QMf1IK3S3wX7uTQgqEruBMGqYAEc7jNkCGZc2Cb/vRfuJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9P474+Stb03Lq34QIG2/nSY/KmjfqyHWT3rO4dotQcAIhALlBt/WcD9n7KQTsn/5jfmG6EH1d4nTJRAA+kpWyY9l9"}]}, "directories": {}}, "1.7.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.7.1", "engines": {"node": ">=0.12.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^2.0.2", "esprima": "^1.2.2", "optionator": "^0.5.0", "source-map": "~0.2.0"}, "optionalDependencies": {"source-map": "~0.2.0"}, "devDependencies": {"acorn-6to5": "^0.11.1-25", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "f48fa71ce638ec32943c42c2377a08cefe9d8576", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.7.1", "_shasum": "30ecfcf66ca98dc67cd2fd162abeb6eafa8ce6fc", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "30ecfcf66ca98dc67cd2fd162abeb6eafa8ce6fc", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.7.1.tgz", "integrity": "sha512-2cd7+JUtUEmZVpGmfF9r+uRYXswJAkf85Ce8GvdBa7hSvdjY8hGo+rwC5syAgYzqHpfxNJzLntFjw6879yPbgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBZkGAoUfK5KBnwVGgXHpxvKRxoQMoI3pKlcPvOYjrByAiEA/Zfq9/5xCGEHpVYqEvwH3Lrc9A423UIlfWMUbKsxH9o="}]}, "directories": {}}, "1.8.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.8.0", "engines": {"node": ">=0.12.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^2.0.2", "esprima": "^2.7.1", "optionator": "^0.8.1", "source-map": "~0.2.0"}, "optionalDependencies": {"source-map": "~0.2.0"}, "devDependencies": {"acorn-6to5": "^0.11.1-25", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "0e8280aa061a0dbefb32d277a05015baa7f3e7f2", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.8.0", "_shasum": "b246aae829ce73d59e2c55727359edd1c130a81b", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "b246aae829ce73d59e2c55727359edd1c130a81b", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.8.0.tgz", "integrity": "sha512-z3MQ/gEEH1uEyB/Ueuy5PUAgm5nkJdKemAGB6kh98AkCoTykKBbtGHYpM68Mgcg/DEQTaznkxMFXBXvKA3uKPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHsL9D0IFs3KT7OVyWJh9/LwXJOljhaKeUxzAezWPsH1AiEAh6gpSBM3DHyitodMaVTyMx6ihecocSvY8pVzA2zozcg="}]}, "directories": {}}, "1.8.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.8.1", "engines": {"node": ">=0.12.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^2.0.2", "esprima": "^2.7.1", "optionator": "^0.8.1", "source-map": "~0.2.0"}, "optionalDependencies": {"source-map": "~0.2.0"}, "devDependencies": {"acorn": "^2.7.0", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ba4faabb224b2d5e0080c8e4f964702b699c7d1f", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.8.1", "_shasum": "5a5b53af4693110bebb0867aa3430dd3b70a1018", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5a5b53af4693110bebb0867aa3430dd3b70a1018", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.8.1.tgz", "integrity": "sha512-yhi5S+mNTOuRvyW4gWlg5W1byMaQGWWSYHXsuFZ7GBo7tpyOwi2EdzMP/QWxh9hwkD2m+wDVHJsxhRIj+v/b/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDawtmeB6tlnWhP2qarhKoPJIA20d89Qkmz8DhtkRfgyAIhAOOAGodm1SNR0d27XDXXKxs1Cz2nEwr+OcDdhd9Y6TEh"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/escodegen-1.8.1.tgz_1470506723009_0.12818681285716593"}, "directories": {}}, "1.9.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.9.0", "engines": {"node": ">=0.12.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.5.6"}, "optionalDependencies": {"source-map": "~0.5.6"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ce726e9092acc6bf93693f1d03ca1eb4f119a0c2", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.9.0", "_npmVersion": "5.4.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-v0MYvNQ32bzwoG2OSFzWAkuahDQHK92JBN0pTAALJ4RIxEZe766QJPDR8Hqy7XNUy5K3fnVL76OqYAdc4TZEIw==", "shasum": "9811a2f265dc1cd3894420ee3717064b632b8852", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.9.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICyAfYs72k6aehYvTHsR1hIDx0raueq8YLYYwZnRJQm1AiEAidwxAjOace1WlEWv0aYhJQawHCW0gu54A8bM26wsKIU="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen-1.9.0.tgz_1504888545300_0.6969581602606922"}, "directories": {}}, "1.9.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.9.1", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "f0488e1d18bd87e58063f9bc73578c0c8a8ad253", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.9.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.7.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6hTjO1NAWkHnDk3OqQ4YrCuwwmGHL9S3nPlzBOUG/R44rda3wLNrfvQ5fkSGjyhHFKM7ALPKcKGrwvCLe0lC7Q==", "shasum": "dbae17ef96c8e4bedb1356f4504fa4cc2f7cb7e2", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.9.1.tgz", "fileCount": 6, "unpackedSize": 105887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF54Hk81IjehfalIm9iyUCHf+GQR/yz390Ywm0Ms74jDAiBb9NSZRIb7YmPJ2vLWKbfk0ZZlSrMnsnALwc82epTYsw=="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.9.1_1519667352528_0.816491445196871"}, "_hasShrinkwrap": false}, "1.10.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.10.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "df614634b41565888d520222cdc4335079b5a0c2", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.10.0", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fjUOf8johsv23WuIKdNQU4P9t9jhQ4Qzx6pC2uW890OloK3Zs1ZAoCNpg/2larNF501jLl3UNy0kIRcF6VI22g==", "shasum": "f647395de22519fbd0d928ffcf1d17e0dec2603e", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.10.0.tgz", "fileCount": 6, "unpackedSize": 106062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHgcZCRA9TVsSAnZWagAAqsAP/imWWxXnAyZSCOY2+kdI\nikaoOP5GWRZfmZrwO37LORVsmikUxj1uDPkLmFRwYfW6Kgqmx2E9XrTHT7yQ\nvH2JSjo7J8V0r5SzUFqlUCJfurP+l5W8Ls5U4un/g9v/dixBX/4A80n6oAp5\nNC79/9H9J0SYGPEQHgRH77XXY6XirKyy2sVqbvteFq/NaT+wF+WC9DIwIYgr\ncKvSbRdTTcu5xgAUjDpKyxkYKeTpb69O2diotkaK4UL3Ya4vmEPK+A1dUxMl\nYKISyk75jXxKV++CASm9LkIJ2uFl4LpmhTH7BWTW6XwtPKZFJL32e6X3FXb4\nZwhQAn874LNTNwrUlisfUvOv/v9T1kzl8MQsPOyF6KU8BH9XjXQcFoUKhsVm\nP9ubV5DSSRC0SjBqsBBLOjVLo1BIs/V/VD4+p0F0BnpReUrwlY81hdOiEDYj\nV+Oe0hAUmuTjUbKTDOx+jOwNNPTSYfzKjb6sL5q/BIpBY8LirB070Mq3LMIy\nT9cbc4UEDzD2tRpB2DjOwfzvHR7PIYIYxpVJcPixY2ONdRNTfRJcBwzJCJpS\nBqNuTsHw/nUHuTknd4A59+eh72HFTNBmkHyXxrQ1kxof4FqjXwf5YKfLbxqP\n/r631qY0pTK7/jY653HmBd2kH96THs3K9x9geJk9PHWV2ucfYkLvg7qMOf46\nlkDH\r\n=VA+P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDx+nw2UOn9uVDXxexdYJkiMlGye3q6TSA4EMrp6fa7PAiEA5LIYTBmryDeZOWgHT5Co2cdq0a6IldvWTRgyesjwZvQ="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.10.0_1528694551151_0.3013804094278181"}, "_hasShrinkwrap": false}, "1.11.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.11.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "20a0d3748a25653eb463d9155bbaa3239883717a", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.11.0", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IeMV45ReixHS53K/OmfKAIztN/igDHzTJUhZM3k1jMhIZWjk45SMwAtBsEXiJp3vSPmTcu6CXn7mDvFHRN66fw==", "shasum": "b27a9389481d5bfd5bec76f7bb1eb3f8f4556589", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.11.0.tgz", "fileCount": 6, "unpackedSize": 106127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSioFCRA9TVsSAnZWagAAK3YP/2FxhiCn16ZHrOPcHm/B\ncHz2/nkx3LigCezwo1/sHjwMelxas2Lq1AyJzwKCYYrPT8AtqqOu8uAPtCAo\nDrA8ZDTgva/yQe4onMA1IdWvyGiESxprZ6eZ/B5fgSS5/tyV8eOAc1172iQe\nyGdKAuaN0OUkPGeTxMCLQoHZHJv87I6S47D1GM1/xYNnQ69vb1Vves1bcpp4\nuFhtT9QtDEqus44ELJliiV7Efhkh4yVQGqG5xBo5dxlFL9l3LY/5Y+eNPo3O\nbcKSkHX4sxCF+SQ4B32aRKrTCzlfm6WSvMxNv6eJCidwTh7TKi6gTCBA/YK2\n6l5288G+L7oaewQIfjNfL8iRPT2MMGCw320Vv1Eu+S7uCJNUtq0Ufj/FdrKI\nN0AdkFoTZXcihaPoiO177iu3rm5SJR5HM+hTpcOtlrc2vcvNxIAdDU/Pc3Mw\nfOhF/WSKM8DhtcWeZqXCJ9xImNpRP9ld9YRV7+SZ5XK65YAtoROGOer6n3cW\nXwVfkV0V6l48SDjnAXI4PfALY5lZWR23OHZP/zwVXmOwSB42snMYuLWy1Z7V\nEcPB/MAt7QIN482wKTZ+wLeJ4XTzryWHG8qUV6ZUwwj/XpawjfomIbmXA6+m\nLm6Ifjv+F7ygAu21VbCxS/MruHVrYCk7JGwgTVyfgyZRZWVvmRFTWVey8rLi\ncRa2\r\n=XpG1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJiVzwrRsMhORCZ33nJFOPYcFdnLy2HwP5J8Pj0cPhaQIgDs5n98Q6fCBDl8p/gV6fYqdSkbJZPkD37lEywK+p7Bc="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.11.0_1531587077447_0.5928377937540485"}, "_hasShrinkwrap": false}, "1.11.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.11.1", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "4a93ffc24378722dbfd8859d633746ef5916ce86", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.11.1", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JwiqFD9KdGVVpeuRa68yU3zZnBEOcPs0nKW7wZzXky8Z7tffdYUHbe11bPCV5jYlK6DVdKLWLm0f5I/QlL0Kmw==", "shasum": "c485ff8d6b4cdb89e27f4a856e91f118401ca510", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.11.1.tgz", "fileCount": 6, "unpackedSize": 106130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbBM+CRA9TVsSAnZWagAAnkEP/06UJ97j32aJDg5KEQ6g\nKIL7BbgPL0DfDi+rZrhK1O+Ex6scdcoXbtqW1TjnOgK0xAfoVuMfQYRVR8Oa\nCXTFpSDYer9Sj9wiW7WVR/zwlY8+EyFzMzStSeWxhw+apsP82JjsQY6YfyQV\neE1I/MFnMv3BlKmfmim1fyU7KdT25k5/afNbSiIwhA6aS+z+/JjYOLpqwCzx\nK9BEBUcp5/XB4Or2Oh/yufrYsC8HeDcw90+0NDqypfT5+L6uYOiXcanFMaTb\n/NBAYK4VoG/O9raBkBi4dWKB4OIAEzph4vTQAydMxVmp8luBGNEqsOq/8mIj\nfvVQus1HNKjDDJS4U3VUJolQTFlvBOQ/QBktL6Fw0Xu+lr/6zoUZ2o63/vtb\n5UPXu1vAdDKDejWatzcdyhzfX2fxfmUzLIDMiru/eCHl4X2U/eNRIJc9/GSY\nTEqFohxuv6UrZmHTR0dzBl0vwj8/L90c1YRvAm3enYYLjxqGVZaCSurOoYm4\nX987/jmOsZik9ZPOXJncHf+ZYAqEZPcYcOCMpXHxUQmmwmj+jci+7oPxcqHB\nUSOapFL9wd4H4cxhDVzTWQwXeI6V78TH3Hoc16u8OMtWNWazhetqaUdR7S+K\nWRx4cwhdLcx0t0UBqEe2KhIR0MhE9NAcaB4N+bFRDKUNkoOaOWknBHFoDCQJ\nLzku\r\n=cmBR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/mGsKiSMXD7LCEZNr8Axb3tvG/rl0aOvO0oZEO/ZJgwIhAL1Vxmejd+pl9gWZKp16rNdUAqcal69EK4OvT/5KjQh0"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.11.1_1550586685457_0.004158698030969132"}, "_hasShrinkwrap": false}, "1.12.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.12.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "124e035fa1a3e790b469041012edcb6abc2eca71", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.12.0", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Tu<PERSON>+EhsanGcme5T3R0L80u4t8CpbXQjegRmf7+FPTJrtCTErXFeelblRgHQa1FofEzqYYJmJ/OqjTwREp9qgmg==", "shasum": "f763daf840af172bb3a2b6dd7219c0e17f7ff541", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.12.0.tgz", "fileCount": 6, "unpackedSize": 106414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUhv1CRA9TVsSAnZWagAAOaQP/Am5UZuVB3clrW5XCN3X\nIdMgWlAqRpvesAL0umnA5vP7cvEAreFMxdEdvB0cbxRgkoG/9+R8o0vXRi8q\n/sHSHfn0ytjc0HHZboPXqj6j7s3n9V8jPtRo/38hdXuyeop35llPgJyzcBQ5\nv3mV+fFxECRpXHcRjVxc6g6tqcrMtwqiIOtquEk7vEtUY/9GAoy46ZZ/1FW1\nE0yPfyswi/s3rFkdhGUoZdBYm006CNuO1y7Nkx4XWCJ3Rrcsez2Isg3S0f97\nt+m36OcQGQ9KOtS8whCMNqq/D1Ztwy30jXXPePG0pvgirIrnpHeh+14mdQi9\n1Vd+6nZHWtb0+PQeNevHBk5THfXM5goDdrRx2AptOo20rAu9H4TzwysPg9Vl\nQaozeok/0cShJlpiH7/nc7KOG38CHDEZU8+G9E+ouvQLzmrkxFWQznnwZWe7\nLhrK7o9ug2K18MY/q8zFscl7CK+GZ+oGO8khG70r4sPYf6ulCz4VYBlfFQIJ\njETzjPTIklERDF3FtGDRw7jgb6/YWili967GNnvFOOqyKK9jb1A2S3C5Ks+p\nrLpuGUPuluxAVWGQB3pSVlQxT1hAqadKGf6Fi5630GKsPVWeRjzd1C7jyZ73\n2A56y5eWrhRURbrPPHi63PGWJ5GMlFciipL+dP4Yy1918fNVjl6S/XLuL/qb\n/5md\r\n=pKHW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDs0pBzYODO1WFCnClqWeNBnRejGoVjURCrjOk05kRiwAiEAsieLBcCYAX6EIfa3tCVmY3qDc5/A/+7vN1ICd7SPKfQ="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.12.0_1565662196999_0.6980977686058669"}, "_hasShrinkwrap": false}, "1.12.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.12.1", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^3.1.3", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^4.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "a30ea92a3111837372648e0ab166becb442adf10", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.12.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Q8t2YZ+0e0pc7NRVj3B4tSQ9rim1oi4Fh46k2xhJ2qOiEwhQfdjyEQddWdj7ZFaKmU+5104vn1qrcjEPWq+bgQ==", "shasum": "08770602a74ac34c7a90ca9229e7d51e379abc76", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.12.1.tgz", "fileCount": 6, "unpackedSize": 106423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDpzNCRA9TVsSAnZWagAAnN0P/3epKurBhvzX+BGZHY6v\nE2SsfQcNPJpl52z6V+p1PKE0CwBk4GwHxHF/Aw6vyTB/i4JLOTIdvC/NC12s\nadO6daGgY8ZDEvKLad3YR63t3zCCMGDynkrRWjYCD1+lRCYKdCf3kOHgSTL/\ngOERf/ocsWG0hgCb+SaFQEDJP2O6WZhTauzDy01LxtJqfzzzvBCFsMiPPo6x\nxrAdZuijbHIQF0XYj8SnY7PnOTwDHLX69EV35drgcGc3HNAN5IJxG7mrP+NF\n31pY//eyRgpSe3VC2N01hWK84DCCu0hoU7UezcNW3wlo158hmT0bnJc4cnzO\nFihDPd0N50dauBD3nHAJl5KBKMxeei8ZQuOhwQFc3fL0wzVxPhwXZrzOCwMW\nLz6x8ke69URx8M2m+2H7HJs0X1/1yMWvzqgLG8cNqVWzSKelsjbMGf3K1oQs\nIgSKp4gyWskiwnXAN19t/DEyGV8KWC3+2ECHHnpKXZKg79jRkGvyEVLoATaT\n0NoK9hleh5jRa47bZVxp41IiVWMLBWTRCNQO/EuHrYREr4GuOwO2txyQjW/X\nSWKObOGHTlGfHkyc3Rp1QNmQeZJtZbmB+FVmTwx1DjrqTa0vE8QTUvt9cTjd\nHzDRXG/P51HgBCBNVctKAFunidp0War8TuG0lV/Io5NV+wK9Jyn8AUG1yNEX\n90KL\r\n=8AA3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFo2FYA/jiQrhhk7WyEaGagpPRFoLR3gKvYAHJrVCwvJAiEA4RhRBkUsy9ps+L2HwAQfwK6pCEu6JY5GOMIwffGX4mc="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.12.1_1578015949382_0.6859075650425888"}, "_hasShrinkwrap": false}, "1.13.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.13.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.1.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ba40e5cf26b6f4085d6665dbee1334d32ee68084", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.13.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-eYk2dCkxR07DsHA/X2hRBj0CFAZeri/LyDMc0C8JT1Hqi6JnVpMhJ7XFITbb0+yZS3lVkaPL2oCkZ3AVmeVbMw==", "shasum": "c7adf9bd3f3cc675bb752f202f79a720189cab29", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.13.0.tgz", "fileCount": 6, "unpackedSize": 106567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIoiJCRA9TVsSAnZWagAAkzEP/0YDJQAiuNLJ0gF+yXOC\nXrqfUMykkIiPaVa4bMXr40Kz/IBcq3mcelrIuIK4O9w+0N8qCuojYP8ppMGk\nqelXInlE0mmdye3EMRE+A+o4w9v52wMjqbwuxMuco6k8y3qXsfDKn2E904Ct\nVcXyLA41zjzta0QwE+jUvAAxF6cSyY1FXMYy7LckaiBWQ1NoCYPy4uOsn7p5\noRSrkLduJxoroz607O2Rgp4YXHnFH0Lp6A3xBLFr76kmp9VBgmz6KUPLpc9A\nbWpZcMdwHNrFRxp3qdAL7p7DhDeNUFNXcMPCJBRlypgTsqyD8wo0IT5ILQ1a\ni6za18uc0wzIFyQ9MplNneo0SSr5Ij6PaHWMpgwQK9XyoeHjHaxo9Ma9NRTa\nxKy41Td/I/lbTkNR9wVmV6bEjJi0IqshkdH0/Jga9eWigF8mk7G4C9hJ88qi\nbZ2GhekGXEa2SO4X2qG7F9AyiW8/lepGvBkr3uiwJFLQjpIIHe84cRD4BqWT\nO9j+Au998y+16bODm2tSrrV8WIVMBEks6piFfqwblyVdgT7d9q3xD+vaj+M4\nImSLKm6qK5ydT5g5/2oN5UwcIiMWzDJO/BnzZqS5XShBkEGVYh/no1uMVUyE\nTiNnT84Ua4KN94ijPW/flRfIW81TNvQAJksXyx29LDRKik/YZsDOxcB3a1Z2\nCOz6\r\n=6zrj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKhIr+RfS8e5Y5oXQ73Fusgm1elhYH6lDFhJAeIU/NOAiBmkvwDt5Crmv0nVCOQe/60P/dOIoCDbShu0fn7ju06LA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.13.0_1579321480527_0.76911540384046"}, "_hasShrinkwrap": false}, "1.14.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.14.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.1.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "86d000655235dae368fedeb845b8d00ae71b94b0", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.14.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-/iH5wgftS7OpT3BQiVGp2FDyaXv8nIHENKft1V5KerFnJluszePIfdI6ykMfE0OhRf4IDcCFQVVJALXyH626Yg==", "shasum": "c87dd83ffd4ff285e37d176303305256988ecfc9", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.0.tgz", "fileCount": 6, "unpackedSize": 106856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeO3DdCRA9TVsSAnZWagAA7KgP/3CxvjtO7oaWfjt+mz7b\nHbb1O6LgR+oRkeI8VA4PSslY0gGlTyJzwHhhasXkfq/l95ySo209JwjuuEcr\nHslfhqoiZoO7UemHl2zXScObgMRfamo7rsVXvfdCWOKN8wQsgC2dRDFCoIJK\nRg3/KIv4KrNf7uFviEzs8LQnYqj9RKz13BaUsoTkvzaW7nT5v2csOuaoFflZ\nWurRLyZBbSweE1Gcot96B2aHeZi571mvyEwld8vzb8B4YrOEZGfdTYQS0ksa\nHbBxh4LMkMBhm05wmY9mXDb5llw+qJOolxS3NAZ3iuQdzPRfT1LVZv1ar/0w\nSu1Tyd0g4djLNqyawN0KkAAQ6mWMG04HYvVX2Cl9VvPgOLB8+5i6UT/gZqex\n+TjkGZjSgCfTfc4AITu6iHVdGgs3k/NlpLLziC4ph2PGqE7Xz+9mMtNVIe8G\nSzBtIGW/gojtNfQXu8uBXhXw9TGfa1AMoOSE/CK2g7twFHaj8mh2SOaj/7Ml\nyvoPlUwnBzPimBpwQsgWnnibSOQpGdFI7Xe4t7ZazIn8g8DBNLc2gHBQDG6v\nayh7TE02in/QlIvOKGRn+uNUWNBGsY0Eyp8LXXI5e9DdReX+ulqMR+P2HTk4\nTEG60gmzgkaD2Mk0u4HdLpKHopzbZDt4kbbS48jqQDrtIceRA3jOTQ6htL6K\nBWc/\r\n=Y0+W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkeJPi9yekQtJw5L9/3AifG+uJ17xQrTVnJ4XP0ZmooAIgAweVyKIrg7eq6Bx12EjNFt30kVeLEwCwhZkGLmuTT5U="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.14.0_1580953820820_0.20662967374165508"}, "_hasShrinkwrap": false}, "1.14.1": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.14.1", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.1.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "a3b67181696def5f1f02110c505f48f5e2b4a75d", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.14.1", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-Bmt7NcRySdIfNPfU2ZoXDrrXsG9ZjvDxcAlMfDUgRBjLOWTuIACXPBFJH7Z+cLb40JeQco5toikyc9t9P8E9SQ==", "shasum": "ba01d0c8278b5e95a9a45350142026659027a457", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.1.tgz", "fileCount": 6, "unpackedSize": 106852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeO3UHCRA9TVsSAnZWagAAxmAP/3nO/oCvWpo0A29knlFf\nc+QXoFF5yR6ppnq7IqnZ+SGycA9zK5kLZBOvhU+LBY67IpKAQDT3Zf70JTtx\njAnf4AybkukY0aQwGoLH2FfmyFLlkt6D+K6mvLZbCmXfgsqxx0gC2Tqn6BqY\nUsG5GZacDqOMNLzsW4E5kMNn3AZ3D4U3QxnoOfXjNO6pi2nClQUo9kfWPNdC\nw+iCNdTNy3rG/txWP2ZCrhzObGVrZUExBPumTeZFru9c43m/GDlhR7d1ydUl\ndL0LdUI0E/BWI/kPnCX/kQRklpOvOCfapDHMpf0DdgXIb+l1syaor4FTVNNJ\nINP6YhHsIgfg+nXTgxAsRDNJy1cGJzVi0sSM3gMBZGsjOWFo+CbMDrJeKhGf\nrV3CW7+lAx0rqT1ojmSSS8zbCi+hgQG3u+8bjYxa0AtrsqWuk79c5amzDiME\nGPzMeBoluhtLJvcLVRWIC3MR34mDNFrtIB5ovZjcVB0MY4vTMu4+sA+bhiJQ\nHiKYvjFU9I63LyP6YaS5ytIkDswG9fVqcNIzsIhac+WDXTzEfLhMCLCBj3i3\novJ6q5mQHWhoKuJE+k+rL+EtAdf4uc6UPV1+WdCgTre0xfeaFd7dinXl05uM\naUMh9pDXuGr7Flpu+4sheafvfEzOYfYoJILeudSoi7ddiLdbbNA1LEOp5HHt\nZKge\r\n=hBMg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrNGP+LgLFoZp8HnbPuFV7qHvTYXwGFwTpwq9mhFFCBAIhAMR9xT4TXW6BSU3LwbgkAtcw4ZWfXucribMNFaYrJ/iY"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.14.1_1580954887325_0.8110143156553677"}, "_hasShrinkwrap": false}, "1.14.2": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "bin/esgenerate.js", "escodegen": "bin/escodegen.js"}, "version": "1.14.2", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.1.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "1d85742ead2f995e9fe684b0866cbc2de668790e", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.14.2", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-InuOIiKk8wwuOFg6x9BQXbzjrQhtyXh46K9bqVTPzSo2FnyMBaYGBMC6PhQy7yxxil9vIedFBweQBMK74/7o8A==", "shasum": "14ab71bf5026c2aa08173afba22c6f3173284a84", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.2.tgz", "fileCount": 6, "unpackedSize": 107054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2B6dCRA9TVsSAnZWagAA2NgP/0EFqB6Y8JKfBpXaHoE0\np7kBn+QNT+gSELcAJyGNd4QlDZectwEC7I2EB644NTd6k4Y5z9p49FmvBaJO\nFK9zZ7U+umE2naAKdTXQwJb3gGiUIrEFdfoWvfxv9uHq0yO9hUBHi6RIFjWo\nlHE0xZvEaEbQDV4dIsMchtoVCmoiOYEEZUd18V5At75aTpw7tX0Ul7mcLfgH\nwaAu4T/h3VHn8EP5ZIqHAXixDq2IaYtPW/VYEKL6aQM0dx3Y6W6kU4PsQHVm\nJJKRKUzxkIIdTz4CR95a2tEcH/XkOredPwhe43pFsOxL6+VAYNYYfDQzsb/e\nu5+l0HUQaCj7B9/63EezmtGAM2lgkvlQwvd7nQbmh5F6Oai/nE9erllwiTRu\nOhrNLoUP4qFuvZdvNjQxLwBQiywlfoPppML7W2GvLEGQvvfjFe0oAODlVf2P\nHzk9xham5aqI4S+8P21ysIZD3OpVL96JNgRKxn10alyqC/tXHiK0G3FxRWru\nnZMAWd1o6MSvOY+x5hpy/1yhvyxHUcIAqTvJIORbxOBPifUdmcB7UbQ8z+YL\nsCWTKCx6vrnwllqBxY+QpnYhYkdln1hkMExn+5jEOyS8gsCIGqwJRAo9Z2e0\nbY1sKASXiNObCIYmdrCz0r0GxHRXPRYVaTXGTNL91MCdg3vFaDdOrcqyjxqW\ndnJi\r\n=Dq5w\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIENuvyzyFYP+QMUk98VaOQx2jd9LSN/8L7yYIyV9tt6fAiEA/tV8O0naEbENDbxrga+reCxAk73MfX+GrFf2KdWgxI0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.14.2_1591221916612_0.7480900269191841"}, "_hasShrinkwrap": false}, "1.14.3": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "bin/esgenerate.js", "escodegen": "bin/escodegen.js"}, "version": "1.14.3", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^4.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.1.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^3.5.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "dc2993cd6a4b295b16d38c3f03138c8e89a32337", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@1.14.3", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==", "shasum": "4e7b81fba61581dc97582ed78cab7f0e8d63f503", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.3.tgz", "fileCount": 6, "unpackedSize": 107140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8TvQCRA9TVsSAnZWagAAdJIQAJ/TEdHxavk0hcIf+8Cc\nAMVCB6yKwzZucxKhelvKDBhq2kHhvSTUNJEgSinn0UtCl3JD0+mxXEoq1xbw\nKyIkdQMokOFnndgwdmtBQCBuMSI36fPOWQ3vQOtFOJqx2cJH3rnMyaVhyOvF\npKMMf7lzKRPNDswGYrwZHyAX2++urDAnCp2ZjuGRFQbBkGEs+K32jQ8lk0lR\n0f66WisNgMVgbo5CqYmpTW80QMXL7aRxXG/hfUtKntO0eTiobjGvHhfL50Y3\nyn7X6/iXMR/PiKrOIsBXCI7YD1RdA/9umizLfbcffhBoq6pWXmSOJ3N79hJ2\nk30RXb73gdPVvhoeBZQi0fS14YdIz92WRoreZX/OrMPWXZooaD+e+YB+F4dD\nLv3fEauota5oa9glS+QJtVLcFN2Qi/9HZFWGInBL5fA0GKjCMlE/U8CanOOh\nB6xOlULkgLltiMT+9hGplA3RuHBp+DzYMH2TXrkG2zIo6scikMRwx1KtKdZu\nMOMKgLpp758f4UfX5zFFxKKpLRR41VFDhEYlAZxZzz5q+kmSzznD2QTENJep\nAoTBU06O2Ad1NAt1YfhRK0Wk74Z3A16jqPcejN80TsQs0+wKiR2UEmOvMCnC\nRjO9ejqvKrPAcxWFrFVjC1zPjfMKA3NCcDzRIWTEXCjI47AmS6HX9WjJmKYo\nSRQa\r\n=6tTP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBlHDnSJnrY5hQgGQdwjcR2URUzDAaz3n/wtueyY47aiAiEA59Fa4ZYyS/K6g6GPkqFXZ+IcR0ioREJfdT3KhVPBkJQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_1.14.3_1592867792003_0.6357338060232307"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "bin/esgenerate.js", "escodegen": "bin/escodegen.js"}, "version": "2.0.0", "engines": {"node": ">=6.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^5.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^7.3.1", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^4.2.0", "chai-exclude": "^2.0.2", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "ab53cd5489fd15c3624386465d1f7a0544cda6c8", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@2.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw==", "shasum": "5e32b12833e8aa8fa35e1bf0befa89380484c7dd", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-2.0.0.tgz", "fileCount": 6, "unpackedSize": 107797, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKz59CRA9TVsSAnZWagAAH/EP/0hqGWK0H3dYf+4plAEK\nzstWH92nSsTICKyEPrwYe4PF3mgcYFyuWwveRHR6flCEULxzGPgzmysMIJTy\nxdibEA4V8kXVkxZBDJJGkFr7K86P9DuvrFgPeVonHCu5wPraY5Y4Y1Z0Tr1p\nNrC0jGFgnzAYdxz35dc5MDN8Q+7UF+wYj7hryI4Zds4e1Lx19B7ECVMvkzYy\nEvy+I8gPd9wg1BrLWnnCULfD8LcmJFQ6jAfOJqD6ify6yL7MihluVGUXXrHv\nLdi/6hUQbZFegZCz9AgoIVteOVo4AWFa8EcDGeZVSGt6GZqtQZqRnzxZixV6\nEW8K5E/+ER1iehBLIIoZpNFh0AkwQxpxXAOiE5bA53IKvA9CcobSDGd7DnkH\nlKGrKY7zY/P3i5CGFya5+lapGeC0RlZRne+WmGlVnzeGVm2J1/5W/sxFaW4t\n1Vj6UemqFWHt9gmc686346CjUKmTgrLT4feFC3I9dZXhGxxlS1mXC9cJOUrk\nsyFr/CgVFTwgfOeYcpjy9PM+5TssrHlo/jSwQAlJg5ca5T/XQWd0feeQ1mAn\nQ9fjIc96WFNMjStXW7dGuVmOty7dLdRNRSOw5boADSXcj3P7vqiArnXAMKxE\nfyMmC/TGwpkJpYjmKO4yj5hKrIWkbNKfSgZewXupLFdARfob2k4Ro+aHCwKo\nIVrS\r\n=mgWS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkRtAKIVX5xZ5MSfrMtlxhBYmSIiP1om3cvl1Le6UhCAiEA6PWeVQzNZzkLSF4Ak3jVuBRTEIFvZGiGNjarNPf5tr0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_2.0.0_1596669565071_0.6964441591345811"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "bin/esgenerate.js", "escodegen": "bin/escodegen.js"}, "version": "2.1.0", "engines": {"node": ">=6.0"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "dependencies": {"estraverse": "^5.2.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "source-map": "~0.6.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^8.0.4", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^4.2.0", "chai-exclude": "^2.0.2", "commonjs-everywhere": "^0.9.7", "gulp": "^4.0.2", "gulp-eslint": "^6.0.0", "gulp-mocha": "^7.0.2", "minimist": "^1.2.5", "optionator": "^0.9.1", "semver": "^7.3.4"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "cjsify -a path: tools/entry-point.js > escodegen.browser.js"}, "gitHead": "899cfdf5dc99de324fb6f1087aa9bab4d4bc2f00", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "_id": "escodegen@2.1.0", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==", "shasum": "ba93bbb7a43986d29d6041f99f5262da773e2e17", "tarball": "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz", "fileCount": 6, "unpackedSize": 108549, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzUaziph6n9ktqasAcIxNaE1gRiw11W7NisjOA9eSgLgIgSJSiCmxfwqJud2uKQdSUCqXaX+J8uoiZGUYkwnNQsY4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escodegen_2.1.0_1688069917080_0.34523606176766686"}, "_hasShrinkwrap": false}}, "readme": "## Escodegen\n[![npm version](https://badge.fury.io/js/escodegen.svg)](http://badge.fury.io/js/escodegen)\n[![Build Status](https://secure.travis-ci.org/estools/escodegen.svg)](http://travis-ci.org/estools/escodegen)\n[![Dependency Status](https://david-dm.org/estools/escodegen.svg)](https://david-dm.org/estools/escodegen)\n[![devDependency Status](https://david-dm.org/estools/escodegen/dev-status.svg)](https://david-dm.org/estools/escodegen#info=devDependencies)\n\nEscodegen ([escodegen](http://github.com/estools/escodegen)) is an\n[ECMAScript](http://www.ecma-international.org/publications/standards/Ecma-262.htm)\n(also popularly known as [JavaScript](http://en.wikipedia.org/wiki/JavaScript))\ncode generator from [Mozilla's Parser API](https://developer.mozilla.org/en/SpiderMonkey/Parser_API)\nAST. See the [online generator](https://estools.github.io/escodegen/demo/index.html)\nfor a demo.\n\n\n### Install\n\nEscodegen can be used in a web browser:\n\n    <script src=\"escodegen.browser.js\"></script>\n\nescodegen.browser.js can be found in tagged revisions on GitHub.\n\nOr in a Node.js application via npm:\n\n    npm install escodegen\n\n### Usage\n\nA simple example: the program\n\n    escodegen.generate({\n        type: 'BinaryExpression',\n        operator: '+',\n        left: { type: 'Literal', value: 40 },\n        right: { type: 'Literal', value: 2 }\n    });\n\nproduces the string `'40 + 2'`.\n\nSee the [API page](https://github.com/estools/escodegen/wiki/API) for\noptions. To run the tests, execute `npm test` in the root directory.\n\n### Building browser bundle / minified browser bundle\n\nAt first, execute `npm install` to install the all dev dependencies.\nAfter that,\n\n    npm run-script build\n\nwill generate `escodegen.browser.js`, which can be used in browser environments.\n\nAnd,\n\n    npm run-script build-min\n\nwill generate the minified file `escodegen.browser.min.js`.\n\n### License\n\n#### Escodegen\n\nCopyright (C) 2012 [Yusuke Suzuki](http://github.com/Constellation)\n (twitter: [@Constellation](http://twitter.com/Constellation)) and other contributors.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n  * Redistributions of source code must retain the above copyright\n    notice, this list of conditions and the following disclaimer.\n\n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the\n    documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\nARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\nDIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\nON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\nTHIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "constellation", "email": "<EMAIL>"}], "time": {"modified": "2023-06-29T20:18:37.472Z", "created": "2012-03-03T15:50:31.471Z", "0.0.1": "2012-03-03T15:50:34.433Z", "0.0.2": "2012-03-04T21:31:17.372Z", "0.0.3": "2012-05-12T05:13:02.912Z", "0.0.4": "2012-06-07T06:50:38.250Z", "0.0.5": "2012-07-20T12:32:08.627Z", "0.0.6": "2012-08-30T19:30:41.157Z", "0.0.7": "2012-09-05T16:41:09.688Z", "0.0.8": "2012-09-08T00:47:41.132Z", "0.0.9": "2012-09-14T20:02:33.557Z", "0.0.10": "2012-10-06T00:54:15.137Z", "0.0.11": "2012-10-09T08:28:29.323Z", "0.0.12": "2012-10-10T15:29:24.649Z", "0.0.13": "2012-10-22T17:07:34.681Z", "0.0.14": "2012-11-06T12:04:00.820Z", "0.0.15": "2012-11-09T19:00:45.523Z", "0.0.16": "2013-02-06T16:21:02.315Z", "0.0.17": "2013-02-19T14:17:19.274Z", "0.0.18": "2013-03-08T16:37:35.501Z", "0.0.19": "2013-03-08T17:20:48.325Z", "0.0.20": "2013-04-01T10:07:55.239Z", "0.0.21": "2013-04-16T12:37:56.835Z", "0.0.22": "2013-04-24T16:48:30.553Z", "0.0.23": "2013-06-14T15:09:35.452Z", "0.0.24": "2013-07-11T18:40:22.618Z", "0.0.25": "2013-07-28T01:06:30.379Z", "0.0.26": "2013-08-18T15:56:25.394Z", "0.0.27": "2013-09-10T16:35:45.323Z", "0.0.28": "2013-11-01T14:08:06.476Z", "1.0.0": "2013-11-18T04:30:10.002Z", "1.0.1": "2013-12-07T02:14:34.405Z", "1.1.0": "2014-01-18T21:33:32.511Z", "1.2.0": "2014-02-07T20:48:39.046Z", "1.3.0": "2014-03-09T15:28:54.089Z", "1.3.1": "2014-04-03T04:10:45.255Z", "1.3.2": "2014-04-13T15:38:15.421Z", "1.3.3": "2014-05-31T17:15:05.984Z", "1.4.0": "2014-08-27T04:18:42.432Z", "1.4.1": "2014-09-04T07:01:08.295Z", "1.4.2": "2014-12-15T06:57:27.240Z", "1.4.3": "2014-12-19T10:34:32.052Z", "1.5.0": "2015-01-07T15:07:53.661Z", "1.6.0": "2015-01-12T15:23:45.114Z", "1.6.1": "2015-01-29T09:06:41.359Z", "1.7.0": "2015-09-06T16:40:27.084Z", "1.7.1": "2015-11-24T22:38:48.534Z", "1.8.0": "2016-01-03T01:01:56.895Z", "1.8.1": "2016-08-06T18:05:23.246Z", "1.9.0": "2017-09-08T16:35:45.449Z", "1.9.1": "2018-02-26T17:49:12.583Z", "1.10.0": "2018-06-11T05:22:31.275Z", "1.11.0": "2018-07-14T16:51:17.537Z", "1.11.1": "2019-02-19T14:31:25.637Z", "1.12.0": "2019-08-13T02:09:57.131Z", "1.12.1": "2020-01-03T01:45:49.546Z", "1.13.0": "2020-01-18T04:24:40.703Z", "1.14.0": "2020-02-06T01:50:20.921Z", "1.14.1": "2020-02-06T02:08:07.439Z", "1.14.2": "2020-06-03T22:05:16.738Z", "1.14.3": "2020-06-22T23:16:32.203Z", "2.0.0": "2020-08-05T23:19:25.188Z", "2.1.0": "2023-06-29T20:18:37.341Z"}, "repository": {"type": "git", "url": "git+ssh://**************/estools/escodegen.git"}, "users": {"6174": true, "karboh": true, "charmander": true, "hij1nx": true, "kastor": true, "hughsk": true, "jifeng": true, "moimikey": true, "noyobo": true, "panlw": true, "dexteryy": true, "kaizendad": true, "farskipper": true, "kael": true, "kkuehl": true, "bryan.ygf": true, "diegorbaquero": true, "shanewholloway": true, "laomu": true, "flumpus-dev": true}, "readmeFilename": "README.md", "homepage": "http://github.com/estools/escodegen", "bugs": {"url": "https://github.com/estools/escodegen/issues"}, "license": "BSD-2-<PERSON><PERSON>"}