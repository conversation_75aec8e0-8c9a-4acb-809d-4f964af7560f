{"_id": "@eslint/config-array", "_rev": "11-0ec5d0e0ce8632cb9c79282806b2be2e", "name": "@eslint/config-array", "dist-tags": {"latest": "0.21.0"}, "versions": {"0.15.1": {"name": "@eslint/config-array", "version": "0.15.1", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.15.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "1fa78b422d98f4e7979f2211a1fde137e26c7d61", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.15.1.tgz", "fileCount": 10, "integrity": "sha512-K4gzNq+yymn/EVsXYmf+SBcBro8MTf+aXJZUphM96CdzUEr+ClGDvAbpmaEK+cGVigVXIgs9gNmvHAlrzzY5JQ==", "signatures": [{"sig": "MEUCIQDMHRFnSwleof0p2zfC5atuSbYffjyddRr0FqRuk8UWwAIgVce4FFfE3zg9+45WT+9VwmRf1OBzlGaNgAUJkijirKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112367}, "type": "module", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "12012ffea9128c8c5cc035d3806a128308b26186", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@eslint/object-schema": "^2.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.15.1_1717101631992_0.8434126062778435", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@eslint/config-array", "version": "0.16.0", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.16.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "bb3364fc39ee84ec3a62abdc4b8d988d99dfd706", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.16.0.tgz", "fileCount": 10, "integrity": "sha512-/jmuSd74i4Czf1XXn7wGRWZCuyaUZ330NH1Bek0Pplatt4Sy1S5haN21SCLLdbeKslQ+S0wEJ+++v5YibSi+Lg==", "signatures": [{"sig": "MEUCIQCNmn+2ietwbYcusG8A2/CMeFoi2bgNJfWp3dXrwPIKrQIgG3sQORKC2TapxkKwnHk9m7OdjAtNCveNueVaLSzKwlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112055}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "2a7bed41976f94495f7ba46f459f1de9d3305664", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@eslint/object-schema": "^2.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.16.0_1718202703675_0.8572314010669104", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@eslint/config-array", "version": "0.17.0", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.17.0", "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "ff305e1ee618a00e6e5d0485454c8d92d94a860d", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.17.0.tgz", "fileCount": 10, "integrity": "sha512-A68TBu6/1mHHuc5YJL0U0VVeGNiklLAL6rRmhTCP2B5XjWLMnrX+HkO+IAXyHvks5cyyY1jjK5ITPQ1HGS2EVA==", "signatures": [{"sig": "MEQCIH/auYEggLWMW+5BeCQ9bcSAUtYnq2rep4qbi+CQRNunAiBYUdv3+wU4+3VN3D/Q0iPAxg45Tl0J/GcpYur00U3d9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112870}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "fac14b8cad5a8119dee73d2c5eec8e5f01af2a43", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.17.0_1719418428869_0.9814307206697055", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@eslint/config-array", "version": "0.17.1", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.17.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "d9b8b8b6b946f47388f32bedfd3adf29ca8f8910", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.17.1.tgz", "fileCount": 10, "integrity": "sha512-BlYOpej8AQ8Ev9xVqroV7a02JK3SkBAaN9GfMMH9W6Ch8FlQlkjGw4Ir7+FgYwfirivAf4t+GtzuAxqfukmISA==", "signatures": [{"sig": "MEUCIDzeQ34kP/bL9JJY9Wh90PUfCG0OW4EO2LoVg+PWbLRBAiEAqewhVL/AkjWAC2uf9qPA3LPLu/EC9A9ZdHTv/86+hNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.17.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 113080}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "7342e1ae8720e58d61f0aebdfb9f1407d9177d2d", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.17.1_1721662090851_0.540220447184635", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@eslint/config-array", "version": "0.18.0", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.18.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "37d8fe656e0d5e3dbaea7758ea56540867fd074d", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.18.0.tgz", "fileCount": 10, "integrity": "sha512-fTxvnS1sRMu3+JjXwJG0j/i4RT9u4qJ+lqS/yCGap4lH4zZGzQ7tu+xZqQmcMZq5OBZDL4QRxQzRjkWcGt8IVw==", "signatures": [{"sig": "MEYCIQC8eWna0dDtJiQAHI7UMTA0vAwW49VVFo+mQtgtZPDeNgIhAMxcC/ptYaExYXaeI+qZmzEDeH/YKTHKeKSfK6ggEEOe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.18.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 113218}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "e2a7ec809db20e638abbad250d105ddbde88a8d5", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.18.0_1723476068526_0.34992848374590246", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@eslint/config-array", "version": "0.19.0", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.19.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "3251a528998de914d59bb21ba4c11767cf1b3519", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.0.tgz", "fileCount": 14, "integrity": "sha512-zdHg2FPIFNKPdcHWtiNT+jEFCHYVplAXRDlQDyqy0zGx/q2parwh7brGJSiTxRk/TSMkbM//zt/f5CHgyTyaSQ==", "signatures": [{"sig": "MEYCIQDlnEidVGZxdAgf15xfrlsAbTglTC6+A13/mdrSSY0I5QIhAJtdNTS8bzEeh+93sLwLbq+wA3O3gFfP3nZzlPeK/WVZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314034}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "0dc78d335a98ef680b579851026438473147750e", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.19.0_1730136250589_0.40123285320959035", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@eslint/config-array", "version": "0.19.1", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.19.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "734aaea2c40be22bbb1f2a9dac687c57a6a4c984", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.1.tgz", "fileCount": 14, "integrity": "sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==", "signatures": [{"sig": "MEUCIQDpuxI8zljn1dpm6q4PMeoTXcr8BVn+mJkB/PG5AxZO5QIgFVPempAaHV/TlO804BGCp4zVDPZbqKgSp8+6HbXczvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314613}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "dd8d161c635450f3e37109f833737bf69f54db55", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "test:coverage": "c8 npm test", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.19.1_1733347421280_0.10279817022077453", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@eslint/config-array", "version": "0.19.2", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.19.2", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "3060b809e111abfc97adb0bb1172778b90cb46aa", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.2.tgz", "fileCount": 14, "integrity": "sha512-GNKqxfHG2ySmJOBSHg7LxeUx4xpuCoFjacmlCoYWEbaPXLwvfIjixRI12xCQZeULksQb23uiA8F40w5TojpV7w==", "signatures": [{"sig": "MEQCIA75hiqW/TWB/v+DUrOGchIBgdnfmT6TUMkmgOLu0XpQAiBwTExgHxGPsQeLIpOaNLOQvTdjAdzLBg6BQCMoWPYSDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314532}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "e1cb6037bc237313dbf3f6a7b9f5cd3c3105b668", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:coverage": "c8 npm test", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.19.2_1738344177744_0.5181224534298126", "host": "s3://npm-registry-packages-npm-production"}}, "0.20.0": {"name": "@eslint/config-array", "version": "0.20.0", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.20.0", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "7a1232e82376712d3340012a2f561a2764d1988f", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.20.0.tgz", "fileCount": 14, "integrity": "sha512-fxlS1kkIjx8+vy2SjuCB94q3htSNrufYTXubwiBFeaQHbH6Ipi43gFJq2zCMt6PHhImH3Xmr0NksKDvchWlpQQ==", "signatures": [{"sig": "MEUCIQCbeUDn4xFiXlEcaX5aRoGolFRzdCtHTJUrF04o2O1MUgIgKZUI9Q1g/ceHcewk56YihTzb3ZDss3ApFA9ObqNL4dY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.20.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 318595}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "83e58a1d8d7e5df81c87cfbaeebaea70a40b4e51", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:coverage": "c8 npm test", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.20.0_1742912542400_0.7807306158471459", "host": "s3://npm-registry-packages-npm-production"}}, "0.20.1": {"name": "@eslint/config-array", "version": "0.20.1", "keywords": ["configuration", "configarray", "config file"], "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "_id": "@eslint/config-array@0.20.1", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eslint/rewrite/tree/main/packages/config-array#readme", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "dist": {"shasum": "454f89be82b0e5b1ae872c154c7e2f3dd42c3979", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.20.1.tgz", "fileCount": 14, "integrity": "sha512-OL0RJzC/CBzli0DrrR31qzj6d6i6Mm3HByuhflhl4LOBiWxN+3i6/t/ZQQNii4tjksXi8r2CRW1wMpWA2ULUEw==", "signatures": [{"sig": "MEUCIQDqiU6T0CycF3Wa1gJiFaNNFn77CYouQgdcjY3gJTe+ZAIgV8di6J/UOwGQQVMeuIY3qoDen/SDc3bTZuvGEn9MyBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.20.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 319671}, "main": "dist/esm/index.js", "type": "module", "types": "dist/esm/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "gitHead": "48b1f849476582257e1b6a110c4af55adbbec2e8", "scripts": {"test": "mocha tests/", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "pretest": "npm run build", "test:jsr": "npx jsr@latest publish --dry-run", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "test:coverage": "c8 npm test", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js"}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eslint/rewrite.git", "type": "git", "directory": "packages/config-array"}, "_npmVersion": "10.9.2", "description": "General purpose glob-based configuration matching.", "directories": {}, "_nodeVersion": "22.16.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/config-array_0.20.1_1749674193976_0.5067914065342578", "host": "s3://npm-registry-packages-npm-production"}}, "0.21.0": {"name": "@eslint/config-array", "version": "0.21.0", "description": "General purpose glob-based configuration matching.", "author": {"name": "<PERSON>"}, "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/config-array"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite/tree/main/packages/config-array#readme", "scripts": {"build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "build:std__path": "rollup -c rollup.std__path-config.js && node fix-std__path-imports", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && npm run build:std__path", "test:jsr": "npx jsr@latest publish --dry-run", "pretest": "npm run build", "test": "mocha tests/", "test:coverage": "c8 npm test"}, "keywords": ["configuration", "configarray", "config file"], "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "devDependencies": {"@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "_id": "@eslint/config-array@0.21.0", "gitHead": "0496201974aad87fdcf3aa2a63ec74e91b54825e", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==", "shasum": "abdbcbd16b124c638081766392a4d6b509f72636", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.21.0.tgz", "fileCount": 14, "unpackedSize": 325670, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.21.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDoqp5dvMJoTovrpwM4zBJX1b4V6KcYZ239cWeLnzeS9gIgYUykA1pQMgo8ckwjOTBRmr9+O5E4KZJyQ9XA7tmc8Vk="}]}, "_npmUser": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "es<PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/config-array_0.21.0_1750860266288_0.3817465955402488"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-05-30T20:40:31.865Z", "modified": "2025-06-25T14:04:26.966Z", "0.15.1": "2024-05-30T20:40:32.215Z", "0.16.0": "2024-06-12T14:31:43.852Z", "0.17.0": "2024-06-26T16:13:49.044Z", "0.17.1": "2024-07-22T15:28:11.039Z", "0.18.0": "2024-08-12T15:21:08.731Z", "0.19.0": "2024-10-28T17:24:10.819Z", "0.19.1": "2024-12-04T21:23:41.492Z", "0.19.2": "2025-01-31T17:22:58.027Z", "0.20.0": "2025-03-25T14:22:22.623Z", "0.20.1": "2025-06-11T20:36:34.214Z", "0.21.0": "2025-06-25T14:04:26.490Z"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "author": {"name": "<PERSON>"}, "license": "Apache-2.0", "homepage": "https://github.com/eslint/rewrite/tree/main/packages/config-array#readme", "keywords": ["configuration", "configarray", "config file"], "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/config-array"}, "description": "General purpose glob-based configuration matching.", "maintainers": [{"name": "openjsfoundation", "email": "<EMAIL>"}, {"name": "es<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Config Array\n\n## Description\n\nA config array is a way of managing configurations that are based on glob pattern matching of filenames. Each config array contains the information needed to determine the correct configuration for any file based on the filename.\n\n**Note:** This is a generic package that can be used outside of ESLint. It contains no ESLint-specific functionality.\n\n## Installation\n\nFor Node.js and compatible runtimes:\n\n```shell\nnpm install @eslint/config-array\n# or\nyarn add @eslint/config-array\n# or\npnpm install @eslint/config-array\n# or\nbun add @eslint/config-array\n```\n\nFor Deno:\n\n```shell\ndeno add @eslint/config-array\n```\n\n## Background\n\nThe basic idea is that all configuration, including overrides, can be represented by a single array where each item in the array is a config object. Config objects appearing later in the array override config objects appearing earlier in the array. You can calculate a config for a given file by traversing all config objects in the array to find the ones that match the filename. Matching is done by specifying glob patterns in `files` and `ignores` properties on each config object. Here's an example:\n\n```js\nexport default [\n\t// match all JSON files\n\t{\n\t\tname: \"<PERSON><PERSON><PERSON> Handler\",\n\t\tfiles: [\"**/*.json\"],\n\t\thandler: j<PERSON><PERSON><PERSON><PERSON>,\n\t},\n\n\t// match only package.json\n\t{\n\t\tname: \"package.json Handler\",\n\t\tfiles: [\"package.json\"],\n\t\thandler: packageJsonHandler,\n\t},\n];\n```\n\nIn this example, there are two config objects: the first matches all JSON files in all directories and the second matches just `package.json` in the base path directory (all the globs are evaluated as relative to a base path that can be specified). When you retrieve a configuration for `foo.json`, only the first config object matches so `handler` is equal to `jsonHandler`; when you retrieve a configuration for `package.json`, `handler` is equal to `packageJsonHandler` (because both config objects match, the second one wins).\n\n## Usage\n\nFirst, import the `ConfigArray` constructor:\n\n```js\nimport { ConfigArray } from \"@eslint/config-array\";\n\n// or using CommonJS\n\nconst { ConfigArray } = require(\"@eslint/config-array\");\n```\n\nWhen you create a new instance of `ConfigArray`, you must pass in two arguments: an array of configs and an options object. The array of configs is most likely read in from a configuration file, so here's a typical example:\n\n```js\nconst configFilename = path.resolve(process.cwd(), \"my.config.js\");\nconst { default: rawConfigs } = await import(configFilename);\nconst configs = new ConfigArray(rawConfigs, {\n\t// the path to match filenames from\n\tbasePath: process.cwd(),\n\n\t// additional items in each config\n\tschema: mySchema,\n});\n```\n\nThis example reads in an object or array from `my.config.js` and passes it into the `ConfigArray` constructor as the first argument. The second argument is an object specifying the `basePath` (the directory in which `my.config.js` is found) and a `schema` to define the additional properties of a config object beyond `files`, `ignores`, `basePath`, and `name`.\n\n### Specifying a Schema\n\nThe `schema` option is required for you to use additional properties in config objects. The schema is an object that follows the format of an [`ObjectSchema`](https://npmjs.com/package/@eslint/object-schema). The schema specifies both validation and merge rules that the `ConfigArray` instance needs to combine configs when there are multiple matches. Here's an example:\n\n```js\nconst configFilename = path.resolve(process.cwd(), \"my.config.js\");\nconst { default: rawConfigs } = await import(configFilename);\n\nconst mySchema = {\n\n    // define the handler key in configs\n    handler: {\n        required: true,\n        merge(a, b) {\n            if (!b) return a;\n            if (!a) return b;\n        },\n        validate(value) {\n            if (typeof value !== \"function\") {\n                throw new TypeError(\"Function expected.\");\n            }\n        }\n    }\n};\n\nconst configs = new ConfigArray(rawConfigs, {\n\n    // the path to match filenames from\n    basePath: process.cwd(),\n\n    // additional item schemas in each config\n    schema: mySchema,\n\n    // additional config types supported (default: [])\n    extraConfigTypes: [\"array\", \"function\"];\n});\n```\n\n### Config Arrays\n\nConfig arrays can be multidimensional, so it's possible for a config array to contain another config array when `extraConfigTypes` contains `\"array\"`, such as:\n\n```js\nexport default [\n\t// JS config\n\t{\n\t\tfiles: [\"**/*.js\"],\n\t\thandler: jsHandler,\n\t},\n\n\t// JSON configs\n\t[\n\t\t// match all JSON files\n\t\t{\n\t\t\tname: \"JSON Handler\",\n\t\t\tfiles: [\"**/*.json\"],\n\t\t\thandler: jsonHandler,\n\t\t},\n\n\t\t// match only package.json\n\t\t{\n\t\t\tname: \"package.json Handler\",\n\t\t\tfiles: [\"package.json\"],\n\t\t\thandler: packageJsonHandler,\n\t\t},\n\t],\n\n\t// filename must match function\n\t{\n\t\tfiles: [filePath => filePath.endsWith(\".md\")],\n\t\thandler: markdownHandler,\n\t},\n\n\t// filename must match all patterns in subarray\n\t{\n\t\tfiles: [[\"*.test.*\", \"*.js\"]],\n\t\thandler: jsTestHandler,\n\t},\n\n\t// filename must not match patterns beginning with !\n\t{\n\t\tname: \"Non-JS files\",\n\t\tfiles: [\"!*.js\"],\n\t\tsettings: {\n\t\t\tjs: false,\n\t\t},\n\t},\n\n\t// specific settings for files inside `src` directory\n\t{\n\t\tname: \"Source files\",\n\t\tbasePath: \"src\",\n\t\tfiles: [\"**/*\"],\n\t\tsettings: {\n\t\t\tsource: true,\n\t\t},\n\t},\n];\n```\n\nIn this example, the array contains both config objects and a config array. When a config array is normalized (see details below), it is flattened so only config objects remain. However, the order of evaluation remains the same.\n\nIf the `files` array contains a function, then that function is called with the path of the file as it was passed in. The function is expected to return `true` if there is a match and `false` if not. (The `ignores` array can also contain functions.)\n\nIf the `files` array contains an item that is an array of strings and functions, then all patterns must match in order for the config to match. In the preceding examples, both `*.test.*` and `*.js` must match in order for the config object to be used.\n\nIf a pattern in the files array begins with `!` then it excludes that pattern. In the preceding example, any filename that doesn't end with `.js` will automatically get a `settings.js` property set to `false`.\n\nYou can also specify an `ignores` key that will force files matching those patterns to not be included. If the `ignores` key is in a config object without any other keys, then those ignores will always be applied; otherwise those ignores act as exclusions. Here's an example:\n\n```js\nexport default [\n\n    // Always ignored\n    {\n        ignores: [\"**/.git/**\", \"**/node_modules/**\"]\n    },\n\n    // .eslintrc.js file is ignored only when .js file matches\n    {\n        files: [\"**/*.js\"],\n        ignores: [\".eslintrc.js\"]\n        handler: jsHandler\n    }\n];\n```\n\nYou can use negated patterns in `ignores` to exclude a file that was already ignored, such as:\n\n```js\nexport default [\n\t// Ignore all JSON files except tsconfig.json\n\t{\n\t\tfiles: [\"**/*\"],\n\t\tignores: [\"**/*.json\", \"!tsconfig.json\"],\n\t},\n];\n```\n\n### Config Functions\n\nConfig arrays can also include config functions when `extraConfigTypes` contains `\"function\"`. A config function accepts a single parameter, `context` (defined by you), and must return either a config object or a config array (it cannot return another function). Config functions allow end users to execute code in the creation of appropriate config objects. Here's an example:\n\n```js\nexport default [\n\t// JS config\n\t{\n\t\tfiles: [\"**/*.js\"],\n\t\thandler: jsHandler,\n\t},\n\n\t// JSON configs\n\tfunction (context) {\n\t\treturn [\n\t\t\t// match all JSON files\n\t\t\t{\n\t\t\t\tname: context.name + \" JSON Handler\",\n\t\t\t\tfiles: [\"**/*.json\"],\n\t\t\t\thandler: jsonHandler,\n\t\t\t},\n\n\t\t\t// match only package.json\n\t\t\t{\n\t\t\t\tname: context.name + \" package.json Handler\",\n\t\t\t\tfiles: [\"package.json\"],\n\t\t\t\thandler: packageJsonHandler,\n\t\t\t},\n\t\t];\n\t},\n];\n```\n\nWhen a config array is normalized, each function is executed and replaced in the config array with the return value.\n\n**Note:** Config functions can also be async.\n\n### Normalizing Config Arrays\n\nOnce a config array has been created and loaded with all of the raw config data, it must be normalized before it can be used. The normalization process goes through and flattens the config array as well as executing all config functions to get their final values.\n\nTo normalize a config array, call the `normalize()` method and pass in a context object:\n\n```js\nawait configs.normalize({\n\tname: \"MyApp\",\n});\n```\n\nThe `normalize()` method returns a promise, so be sure to use the `await` operator. The config array instance is normalized in-place, so you don't need to create a new variable.\n\nIf you want to disallow async config functions, you can call `normalizeSync()` instead. This method is completely synchronous and does not require using the `await` operator as it does not return a promise:\n\n```js\nawait configs.normalizeSync({\n\tname: \"MyApp\",\n});\n```\n\n**Important:** Once a `ConfigArray` is normalized, it cannot be changed further. You can, however, create a new `ConfigArray` and pass in the normalized instance to create an unnormalized copy.\n\n### Getting Config for a File\n\nTo get the config for a file, use the `getConfig()` method on a normalized config array and pass in the filename to get a config for:\n\n```js\n// pass in filename\nconst fileConfig = configs.getConfig(\n\tpath.resolve(process.cwd(), \"package.json\"),\n);\n```\n\nThe config array always returns an object, even if there are no configs matching the given filename. You can then inspect the returned config object to determine how to proceed.\n\nA few things to keep in mind:\n\n- If a filename is not an absolute path, it will be resolved relative to the base path directory.\n- The returned config object never has `files`, `ignores`, `basePath`, or `name` properties; the only properties on the object will be the other configuration options specified.\n- The config array caches configs, so subsequent calls to `getConfig()` with the same filename will return in a fast lookup rather than another calculation.\n- A config will only be generated if the filename matches an entry in a `files` key. A config will not be generated without matching a `files` key (configs without a `files` key are only applied when another config with a `files` key is applied; configs without `files` are never applied on their own). Any config with a `files` key entry that is `*` or ends with `/**` or `/*` will only be applied if another entry in the same `files` key matches or another config matches.\n\n## Determining Ignored Paths\n\nYou can determine if a file is ignored by using the `isFileIgnored()` method and passing in the path of any file, as in this example:\n\n```js\nconst ignored = configs.isFileIgnored(\"/foo/bar/baz.txt\");\n```\n\nA file is considered ignored if any of the following is true:\n\n- **It's parent directory is ignored.** For example, if `foo` is in `ignores`, then `foo/a.js` is considered ignored.\n- **It has an ancestor directory that is ignored.** For example, if `foo` is in `ignores`, then `foo/baz/a.js` is considered ignored.\n- **It matches an ignored file pattern.** For example, if `**/a.js` is in `ignores`, then `foo/a.js` and `foo/baz/a.js` are considered ignored.\n- **If it matches an entry in `files` and also in `ignores`.** For example, if `**/*.js` is in `files` and `**/a.js` is in `ignores`, then `foo/a.js` and `foo/baz/a.js` are considered ignored.\n- **The file is outside the `basePath`.** If the `basePath` is `/usr/me`, then `/foo/a.js` is considered ignored.\n\nFor directories, use the `isDirectoryIgnored()` method and pass in the path of any directory, as in this example:\n\n```js\nconst ignored = configs.isDirectoryIgnored(\"/foo/bar/\");\n```\n\nA directory is considered ignored if any of the following is true:\n\n- **It's parent directory is ignored.** For example, if `foo` is in `ignores`, then `foo/baz` is considered ignored.\n- **It has an ancestor directory that is ignored.** For example, if `foo` is in `ignores`, then `foo/bar/baz/a.js` is considered ignored.\n- **It matches and ignored file pattern.** For example, if `**/a.js` is in `ignores`, then `foo/a.js` and `foo/baz/a.js` are considered ignored.\n- **If it matches an entry in `files` and also in `ignores`.** For example, if `**/*.js` is in `files` and `**/a.js` is in `ignores`, then `foo/a.js` and `foo/baz/a.js` are considered ignored.\n- **The file is outside the `basePath`.** If the `basePath` is `/usr/me`, then `/foo/a.js` is considered ignored.\n\n**Important:** A pattern such as `foo/**` means that `foo` and `foo/` are _not_ ignored whereas `foo/bar` is ignored. If you want to ignore `foo` and all of its subdirectories, use the pattern `foo` or `foo/` in `ignores`.\n\n## Caching Mechanisms\n\nEach `ConfigArray` aggressively caches configuration objects to avoid unnecessary work. This caching occurs in two ways:\n\n1. **File-based Caching.** For each filename that is passed into a method, the resulting config is cached against that filename so you're always guaranteed to get the same object returned from `getConfig()` whenever you pass the same filename in.\n2. **Index-based Caching.** Whenever a config is calculated, the config elements that were used to create the config are also cached. So if a given filename matches elements 1, 5, and 7, the resulting config is cached with a key of `1,5,7`. That way, if another file is passed that matches the same config elements, the result is already known and doesn't have to be recalculated. That means two files that match all the same elements will return the same config from `getConfig()`.\n\n## Acknowledgements\n\nThe design of this project was influenced by feedback on the ESLint RFC, and incorporates ideas from:\n\n- Teddy Katz (@not-an-aardvark)\n- Toru Nagashima (@mysticatea)\n- Kai Cataldo (@kaicataldo)\n\n## License\n\nApache 2.0\n\n<!-- NOTE: This section is autogenerated. Do not manually edit.-->\n<!--sponsorsstart-->\n\n## Sponsors\n\nThe following companies, organizations, and individuals support ESLint's ongoing maintenance and development. [Become a Sponsor](https://eslint.org/donate)\nto get your logo on our READMEs and [website](https://eslint.org/sponsors).\n\n<h3>Diamond Sponsors</h3>\n<p><a href=\"https://www.ag-grid.com/\"><img src=\"https://images.opencollective.com/ag-grid/bec0580/logo.png\" alt=\"AG Grid\" height=\"128\"></a></p><h3>Platinum Sponsors</h3>\n<p><a href=\"https://automattic.com\"><img src=\"https://images.opencollective.com/automattic/d0ef3e1/logo.png\" alt=\"Automattic\" height=\"128\"></a> <a href=\"https://www.airbnb.com/\"><img src=\"https://images.opencollective.com/airbnb/d327d66/logo.png\" alt=\"Airbnb\" height=\"128\"></a></p><h3>Gold Sponsors</h3>\n<p><a href=\"https://qlty.sh/\"><img src=\"https://images.opencollective.com/qltysh/33d157d/logo.png\" alt=\"Qlty Software\" height=\"96\"></a> <a href=\"https://trunk.io/\"><img src=\"https://images.opencollective.com/trunkio/fb92d60/avatar.png\" alt=\"trunk.io\" height=\"96\"></a> <a href=\"https://shopify.engineering/\"><img src=\"https://avatars.githubusercontent.com/u/8085\" alt=\"Shopify\" height=\"96\"></a></p><h3>Silver Sponsors</h3>\n<p><a href=\"https://vite.dev/\"><img src=\"https://images.opencollective.com/vite/e6d15e1/logo.png\" alt=\"Vite\" height=\"64\"></a> <a href=\"https://liftoff.io/\"><img src=\"https://images.opencollective.com/liftoff/5c4fa84/logo.png\" alt=\"Liftoff\" height=\"64\"></a> <a href=\"https://americanexpress.io\"><img src=\"https://avatars.githubusercontent.com/u/3853301\" alt=\"American Express\" height=\"64\"></a> <a href=\"https://stackblitz.com\"><img src=\"https://avatars.githubusercontent.com/u/28635252\" alt=\"StackBlitz\" height=\"64\"></a></p><h3>Bronze Sponsors</h3>\n<p><a href=\"https://sentry.io\"><img src=\"https://github.com/getsentry.png\" alt=\"Sentry\" height=\"32\"></a> <a href=\"https://syntax.fm\"><img src=\"https://github.com/syntaxfm.png\" alt=\"Syntax\" height=\"32\"></a> <a href=\"https://cybozu.co.jp/\"><img src=\"https://images.opencollective.com/cybozu/933e46d/logo.png\" alt=\"Cybozu\" height=\"32\"></a> <a href=\"https://www.crosswordsolver.org/anagram-solver/\"><img src=\"https://images.opencollective.com/anagram-solver/2666271/logo.png\" alt=\"Anagram Solver\" height=\"32\"></a> <a href=\"https://icons8.com/\"><img src=\"https://images.opencollective.com/icons8/7fa1641/logo.png\" alt=\"Icons8\" height=\"32\"></a> <a href=\"https://discord.com\"><img src=\"https://images.opencollective.com/discordapp/f9645d9/logo.png\" alt=\"Discord\" height=\"32\"></a> <a href=\"https://www.gitbook.com\"><img src=\"https://avatars.githubusercontent.com/u/7111340\" alt=\"GitBook\" height=\"32\"></a> <a href=\"https://nolebase.ayaka.io\"><img src=\"https://avatars.githubusercontent.com/u/11081491\" alt=\"Neko\" height=\"32\"></a> <a href=\"https://nx.dev\"><img src=\"https://avatars.githubusercontent.com/u/23692104\" alt=\"Nx\" height=\"32\"></a> <a href=\"https://opensource.mercedes-benz.com/\"><img src=\"https://avatars.githubusercontent.com/u/34240465\" alt=\"Mercedes-Benz Group\" height=\"32\"></a> <a href=\"https://herocoders.com\"><img src=\"https://avatars.githubusercontent.com/u/37549774\" alt=\"HeroCoders\" height=\"32\"></a> <a href=\"https://www.lambdatest.com\"><img src=\"https://avatars.githubusercontent.com/u/171592363\" alt=\"LambdaTest\" height=\"32\"></a></p>\n<h3>Technology Sponsors</h3>\nTechnology sponsors allow us to use their products and services for free as part of a contribution to the open source ecosystem and our work.\n<p><a href=\"https://netlify.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/netlify-icon.svg\" alt=\"Netlify\" height=\"32\"></a> <a href=\"https://algolia.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/algolia-icon.svg\" alt=\"Algolia\" height=\"32\"></a> <a href=\"https://1password.com\"><img src=\"https://raw.githubusercontent.com/eslint/eslint.org/main/src/assets/images/techsponsors/1password-icon.svg\" alt=\"1Password\" height=\"32\"></a></p>\n<!--sponsorsend-->\n", "readmeFilename": "README.md"}