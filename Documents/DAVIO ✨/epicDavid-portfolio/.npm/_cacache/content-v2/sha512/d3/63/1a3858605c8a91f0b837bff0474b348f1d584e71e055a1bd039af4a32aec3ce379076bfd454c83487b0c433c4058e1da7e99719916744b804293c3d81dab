{"_id": "iconv-lite", "_rev": "216-49f16670b0e451aa63f9dd78eac01b91", "name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "dist-tags": {"latest": "0.6.3", "bleeding": "0.4.0-pre3"}, "versions": {"0.1.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.0", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "bb686e9e87899523e69c313d01ffae9d7850e1eb", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.1.0.tgz", "integrity": "sha512-IXs/YqMio5O2gCB5gAc9uSuBqIhXtYuEQ07B2GT+/hCbo+l6j+TGeyAQQNGvxaVd2A779bw4VV86kRTtZsVxFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXKVns+dLNESf8D+pwoVv4QDLp+hNFMFlkzn281HRKAwIhAMEy8KvG34QujpSK6hN+YOpqIBEoYWN8hASS6K9JU9n3"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.1", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "7844849646a553d2b65711d4e8e3188c2d0a5106", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.1.1.tgz", "integrity": "sha512-N0TT/dthJLII+xrvRbzWVvDv4GKei8gR7lzEGYTWDGA87moCa7g7+VwRByCbaDjf3YOEUtyLYTbA2fQflhyXCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0gzKXZYu7bQFYlus7A1ImMsSMazWsncRU8ID6RS+WPQIhAKghevgnLhhpxWPwrQ8ZWNDW7ujj++vQYyQ0JheRPxuD"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.2", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "ae828cd32708a17258d6a558c653bde646e84d0a", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.1.2.tgz", "integrity": "sha512-XrdCpkcJ7iVnp9Yr6gSwAbycoDD81cFlZK6a1m4d3ZZSKlp/MPZJPaYxdIo9n9TYH4erH/XsgCeUTQskyWpW3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+nMiZhL8RU+s18zA9yQ/xsipJWyd5Mec4pD5andFysAIhALD5Q4LBvpxjeOTOMfG4tlRcTEw/bIvZyK/9nRRN/EvS"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.3", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.3", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"shasum": "e5b1742382fb90f4900ec0076ac0b868d249615d", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.1.3.tgz", "integrity": "sha512-h6P+/VhcJdXcTuJdC2rmb7F/aJB7C+AywEHDmdgvRmssNrDGljGop4U8ajAKahcT2/LADS7GAk0U0bTMUYcsiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHLiB06MLm9yHs7DfU0tzVPehv5PcqRenJactc6kkYtpAiApWvcXZcbEF0CR11PkJlgrJRHX4ddqgCvAOK/x28mMLA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.4", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.4", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"shasum": "d9d9f7f2902ae56c68c800c0d42822cc681e20af", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.1.4.tgz", "integrity": "sha512-DiB2UCAockub1RHVwDqwCuMk7nYmXsIaA3QDDPDDWhZRNyMX/FvAqK7p+nbYqSVBlLe5B5S4ksm7aqcBFWU6wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHd2kql2ENzoyMl8i+rSj7GiiaMX00Gyjfw7u68/wvg8AiEAoDzpXscFzusvoAUxpgpxY9j1z/W+x2aSkzJ5JtOG1Pw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.0", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.2.0", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"shasum": "235d7ca31fbc40ddf1855bed5eb020f45251247c", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.0.tgz", "integrity": "sha512-g2RqXkWUt7W9zb98QhkNw2H8ntTb617SPimCeWWVbSvzFHlKgzev49r/6siExIGtnsWK2leZO4hugDnd7rQwrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnmAZqmXW89BzlIxRCDBS2ZCAWsgjsog6U0pecQPOeeAiEAqvTVhJoxkZDbdGZTpzjrn5rIUpGEhUwy0p8WfdZPKco="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.1", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ""}, "_id": "iconv-lite@0.2.1", "dist": {"shasum": "011b31b8eeffc57b4cb65521b2a0858ce1ed8bfb", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.1.tgz", "integrity": "sha512-vw547MtbJ5l7L4mNP8XGuqfCOHAWabdb7OIwHSQfTbNGTnr8fgRJ81EdptxJnQtRmUG0Rx2SmWtraZWao6SXMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2pbE4S2cKDXJwYeGZmcr049E2qwga1Eu+2fW276ylFAiBE2DOod3AEq/qU2F+l0zmbWld+3jCysJ1xc8OafZKzIQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.3", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.3", "dist": {"shasum": "f6b14037951e3a334543932e9829dfd004168755", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.3.tgz", "integrity": "sha512-y8spz7Utx8I82Oo1jw3PFKz/5Pyq8u2HjddLLFq+gesvNOGM/HuH0Ypnrx30211TW8SE9lXW9hqcoyuVHKqu2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtB+G/9CBFPhFhEkod+EeB+yrs/i8ntluP7p8+co1fIwIgV67YW1t3GP/qPa2Xk7KtLqtIu/vGW0TwjV4MuMqTcL8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.4", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.4", "dist": {"shasum": "03659514658e27e4d1691a63e7aa01f1dca7f296", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.4.tgz", "integrity": "sha512-j14xF/NLYVcTIGXB30YKjncUGUd9c/xLpJ7xCF3WLq1jew0dPPF7MIBym58wPpw8eATiRlthdG7Ba9kpgQotQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEXGl1pprhXQuoS31YLOeJw5L6W5FX3J3qY65evVIxYAiBqY+1AMPm7kqlQVEG72LyhGS+Zjbfg7+GsyHvLBDYY4g=="}]}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.5", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.5", "dist": {"shasum": "e9f2155037f4afd000c095b1d5ad8831c4c5eacc", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.5.tgz", "integrity": "sha512-02jUjc9BvUMOu138CgxRU6QuDNfKQF7X86meFiXKhOJDjHLwzh2M4PUrTefVvDNxvSC5KmaVLHyNWildubo1ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGJZttqesRRuJHoas5WkBp3+KUJT52qFKmKb0Re17dLCAiEA4A2QrE/Lt6CPVA7IcapCVNTn19W1i16u/VBb6+cbp/4="}]}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.6", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "_id": "iconv-lite@0.2.6", "dist": {"shasum": "f4dc95055077fc0580bf829c3e75c20d55824a3e", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.6.tgz", "integrity": "sha512-XjI/4/S7A2e7F7gib3vEN8NTaYOw3pnubBBPCj4gcTGmhVryf7OKdr/QC1nBXt5j3Ljtm0ncZwC9/z9T7P10qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKSCEcNvhb0teW5ENtk6E6Jxi9Fp2YwdeYCXRNKtk7xQIgA9WH9lwNZotOWKjqfSEe4hUWykz+xio7rAZBb+HcIKE="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.7": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.7", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "_id": "iconv-lite@0.2.7", "dist": {"shasum": "45be2390d27af4b7613aac4ee4d957e3f4cbdb54", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.7.tgz", "integrity": "sha512-U/I1kR5J3PDZf9g3WwDoG4MTj8KfLDYqWv9EtrYDyKw6McL2J87bMqyjYFuZHJ9KE2gI4iAGinleQM66GiT1Kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETW2AQP8MgqfX0fvRXINGaOwOlTSXIZEinF2BjBckoZAiATtOuuuF+GPYj4lN1VBiAOz3SYFDaO6xlpPLCuh6/GFw=="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.8": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.8", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "_id": "iconv-lite@0.2.8", "dist": {"shasum": "8b9ebdc6c0751742951d67786f6fd5c09a9e0109", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.8.tgz", "integrity": "sha512-CfFrPNxtVpJVW3m5wRRuDV6ctKQVHhFdOcj2QJZt4igkmHDO6+LjLsl0cxyfPAgx/wyeI0RXHguq6QmwsyXSog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGGFdTOAPw25887z0LWb8D35ewvby+yG8ZZOBCelphX5AiAZh3//Mxa9hEnqMJxEnrHSCYXcvRCkfffWmDIXgLKMkw=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.9": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.9", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.9", "dist": {"shasum": "5788ae876660ddb663ab68a45fef14922e16998e", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.9.tgz", "integrity": "sha512-Zz+xikNZ3yF/WeJwI6QpLo4ZXa57dK7W5Gz++TnC6gZ0V7L1uliIL5uHWVHbtHFx8ryuu3T2eucTBwa7tSt1oA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUZTfKvDo2+NXbJ8TRVuCqyQHZjrKg1766ZmlL44yItQIgUfJeHhdWYMeqT/uaApKZ2d9BfAqogPGvo9J0l4rvYaM="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.10": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.10", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.10", "dist": {"shasum": "8839fa77a9e4325a51ca0f8bae6b0cbd490f5a92", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.10.tgz", "integrity": "sha512-bo8JrNVjsJKQO4YnoqEhkxy6fTDdsLUrgnwb2aeFpGI2BUaHxzyjrSBK834fmaIxw0ypXQfu5boQDyaaQ4Q4KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBWsooYfAwAHaYGOZRJEkDIaHEHCsZmHPFOXMLFYmdLwIhALU11WotZZjxacJ6ALpK9pax13LMupKnQIQ3hdlmIGdZ"}]}, "_from": ".", "_npmVersion": "1.2.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.11": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.11", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.11", "dist": {"shasum": "1ce60a3a57864a292d1321ff4609ca4bb965adc8", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.11.tgz", "integrity": "sha512-KhmFWgaQZY83Cbhi+ADInoUQ8Etn6BG5fikM9syeOjQltvR45h7cRKJ/9uvQEuD61I3Uju77yYce0/LhKVClQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFOFPnf++uqk7o4dXN0EBaauHYUXXzV++EKGghOUcBVyAiBF5n/deCmd16EqXTN7AodW5SNmroHg0eclSovqYMxpaQ=="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0-pre": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre", "dist": {"shasum": "8ef26bada5b13a311ab299fd53a8685686826c8a", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.0-pre.tgz", "integrity": "sha512-BPM0zFUgzniQTe1+js0AEs5HwVtP19WNGoxiH9ugywSln/ibKyiBkIv3G5td2AzkMRyIEOEelxg68Ub9yuRyOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNbhJ6jEYyvjVSMp40OEot2lsm7xY7DIGTkT+oSkFvgAiEA8Jtv432FEDQkVnzZqSECSj563BTfnlEUd0P9xe1JWJc="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0-pre2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre2", "dist": {"shasum": "be0ec485136c00984825c8de63b0e22f7e23193e", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.0-pre2.tgz", "integrity": "sha512-ZF8EEyTjgWKBVNJBxSl1TYUzwCEFBIHEsUy3CrxrtnfOI08YYrwGTmsOnMAO11sk0sk8gxZbHTDjVmWBhGd4QA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW6+9hneeT+lKluk5wVnj/hhBWEqBzbpR2q7INmPQpngIgKyOC4r2GjZCC/NYrASPjjZbye40wDl4BPwUFHRCJcps="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0-pre3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre3", "dist": {"shasum": "bfbdb354cecc2f54d58addda32d62817da843f6a", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.0-pre3.tgz", "integrity": "sha512-CX6LnbnrxoaDAYlHsst4GCEjVDrWJIdRFur3Y1ZRofTF/JfLBwoeR1jZRr9J6wOcFmDiRuGbKosV6O09swEgHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFyuGl6f9l66stZnoIx6rhowt3FkwuVGlwoRvsrIHh40AiEAnM3zAX1QufU5LsnjeqCh6GazN7C+0S6GaoqjYZvVsX4="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "_id": "iconv-lite@0.4.0", "_shasum": "cc77430093c1298e35aba9e8fa38d09582fcdcb7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cc77430093c1298e35aba9e8fa38d09582fcdcb7", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.0.tgz", "integrity": "sha512-d7/ePgG4u3EjP5Q1bchwAmXzVi31co1iSzExDL+o2NtdGiLKZLO4LIPtWhfcMSfD37hyKpAEPF5PZFrZXhygCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGaXnyTYHmIVZPi7XUaKhTSZgj7XmHmT3b+l9b1ZxUGQIgQ/7Vn7t5/cuYFoIOL0TdkgljmMptoEeh9stkAo8Fqrc="}]}, "directories": {}}, "0.4.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "c61800cc51fb7496754f810c14b66b8e543d22b6", "_id": "iconv-lite@0.4.1", "_shasum": "c9d4621aafb06b67979b79676ca99ac4c0378b1a", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c9d4621aafb06b67979b79676ca99ac4c0378b1a", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.1.tgz", "integrity": "sha512-y6jD7lLVA0FKxT8h1EOMPmpYOwh0Q4gMFVaO49RgKB0RSAL/TVrS0iIt79A8hj9Kw5wJWUAcjsulK+Ij3jOl9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEzZqIFpefr3N5FMWZoTzLKTYVSPdh6tNMWYLclYcSOoAiEA7vACKO0tStyKBYfaSoGlBToeSLJaPvH7ZQm3DhXwoFU="}]}, "directories": {}}, "0.4.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "832e328447c1ea879c329e359a200b258942c2bc", "_id": "iconv-lite@0.4.2", "_shasum": "af57e14c2ccd8b27e945d7b4de071accd59f00bb", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "af57e14c2ccd8b27e945d7b4de071accd59f00bb", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.2.tgz", "integrity": "sha512-4mCaxNpPcUG33G5e0Ct3huMFSgA5a5WLPQoFQOgvHLftn5YK1tSGCug3+iKAE0U9MkJ1HdONl20evwH7IOKPEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBKTJ79GceaApogJsVTzwXVPdfQTLWgymSpe93jExSnLAiAMcs22WCjyLWTP516kDo6b907b+1XTsoNGZ6ntpnrFiw=="}]}, "directories": {}}, "0.4.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "42f4a837055c1277a73468ccaedb5f5eac31425d", "_id": "iconv-lite@0.4.3", "_shasum": "9e7887793b769cc695eb22d2546a4fd2d79b7a1e", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9e7887793b769cc695eb22d2546a4fd2d79b7a1e", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.3.tgz", "integrity": "sha512-fBUZHWVujxJd0hOJLaN4Zj4h1LeOn+qi5qyts4HFFa0jaOo/0E6DO1UsJReZV0qwiIzeaqm/1LhYBbvvGjQkNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfzsLhG+fu0wjMc8OORhKwPnI2tdboI0vP6r/wiV+5FAiBJ0w9kbKzPTrHNVGFSWLWv2fwnpaCfNcrrjAEQAvfdeg=="}]}, "directories": {}}, "0.4.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.4", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "9f0b0a7631d167322f47c2202aa3e5b090945131", "_id": "iconv-lite@0.4.4", "_shasum": "e95f2e41db0735fc21652f7827a5ee32e63c83a8", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e95f2e41db0735fc21652f7827a5ee32e63c83a8", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.4.tgz", "integrity": "sha512-BnjNp13aZpK4WBGbmjaNHN2MCp3P850n8zd/JLinQJ8Lsnq2Br4o2467C2waMsY5kr7Z41SL1gEqh8Vbfzg15A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB/QvhojntI+FD7Wve4DtuuTg10v30IgT6BhH/QDAdAAIhAIKEjGOtMhQlAxg2br9pyIlNNf+9Vo81WRtEiVnnVucz"}]}, "directories": {}}, "0.4.5": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.5", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "~2.1.4"}, "gitHead": "0654719791aa2c159bb3820f095b5da8702d091b", "_id": "iconv-lite@0.4.5", "_shasum": "9c574b70c30d615859f2064d2be4335ad6b1a8d6", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9c574b70c30d615859f2064d2be4335ad6b1a8d6", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.5.tgz", "integrity": "sha512-LQ4GtDkFagYaac8u4rE73zWu7h0OUUmR0qVBOgzLyFSoJhoDG2xV9PZJWWyVVcYha/9/RZzQHUinFMbNKiOoAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICmrW0Lu2nkI31A7nWPpbjP8F4zHnCzbt16zGW1iYRPYAiEAo0GJwBMWc5rqpCc0ZdaOd+9LRlkllsH7aSBqFSs3Xgk="}]}, "directories": {}}, "0.4.6": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.6", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "~2.1.4"}, "gitHead": "920dad2303f7c64d92e771ffd379688e0a0d6fc1", "_id": "iconv-lite@0.4.6", "_shasum": "e39c682610a791f3eedc27382ff49e263f91fa09", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e39c682610a791f3eedc27382ff49e263f91fa09", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.6.tgz", "integrity": "sha512-aop+f6/kQEnzTfi6Rv8KLQMt1bY8/0bFTS5oSiYnwXlCH6eI/gJzL+rGjTwsA7soKCcq/hkeDySFC7PwBELX2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJt4TNH8ZvTJy9v9HLcsgRTM1ScEYdh5X1zBU+jTuD0gIhALphE9vxl4Ug00rNBnjItZAwy/c9a4XKuo6KU+WqCuqx"}]}, "directories": {}}, "0.4.7": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.7", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1.4"}, "gitHead": "820336d20d947159895c80daab55bac4261ff53c", "_id": "iconv-lite@0.4.7", "_shasum": "89d32fec821bf8597f44609b4bc09bed5c209a23", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "89d32fec821bf8597f44609b4bc09bed5c209a23", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.7.tgz", "integrity": "sha512-Js5rATPL/P+t5hmjXLuSI5wF4YqFYuIZkAwL5HZ/FUFwWUy5jQCx4YAGSUOUeHbCSixY1yyk3LUTfgpQZhb/CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC26TIGQw/mH3Apqs0W1zCeh8K254jjioOAefvE6+0cVQIgS+nyze0HbCtLi6OzzTIAG3jbLJXxs20yiG9/kmIOHtU="}]}, "directories": {}}, "0.4.8": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.8", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1.4"}, "gitHead": "3dc7d0cb0e223b29634ecb7bff46910c8107ab3d", "_id": "iconv-lite@0.4.8", "_shasum": "c6019a7595f2cefca702eab694a010bcd9298d20", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c6019a7595f2cefca702eab694a010bcd9298d20", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.8.tgz", "integrity": "sha512-D90rbOiZuEJGtmIBK9wcRpW//ZKLD8bTPOAx5oEsu+O+HhSOstX/HCZFBvNkuyDuiNHunb81cfsqaYzZxcUMYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSQkx36KCObxYw1OViG1BJSzbsotiO/w1YxzMdSe917wIgUYlG5ZGIsXzNrwoTa8BZli4V8m6FWUZeon3PmWOWeRk="}]}, "directories": {}}, "0.4.9": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.9", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "a9a123e22074cb4d8e0392ae037b0e348df1559a", "_id": "iconv-lite@0.4.9", "_shasum": "4d8b3c7f596c558ce95b4bd4562c874010a7df3e", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4d8b3c7f596c558ce95b4bd4562c874010a7df3e", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.9.tgz", "integrity": "sha512-PflPJxTFsGUHI6zE2NbyBzyDhRw/+9HeGQp6nKnY4kT+lxR1IQdZ/TlId0afhPwqhEUs9VTJkzTM7bBBT+TxqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID8lAPuHdZgVnHep+g1Y7r8IXUmvwvyR4Mhscb4yL02RAiB3e5EqZrKn3c29VMIq+rODKx5z9onBkbF/PRqn4WFVGA=="}]}, "directories": {}}, "0.4.10": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.10", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "e8af2b49035abbe4fabe826925764bc20f8587c6", "_id": "iconv-lite@0.4.10", "_shasum": "4f1a2562efd36d41c54d45c59999b590951796de", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4f1a2562efd36d41c54d45c59999b590951796de", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.10.tgz", "integrity": "sha512-nqju2pcdvq9tvfZ4nRL2vzQqJoOzIxwH+IF08DzVULhcHiFgYQJ8XWGl7GV7O/c/TlDF6I0PYZxx4sM8QhAp8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3ZqI0RloZ5sTx/VVe/ydQSJEDWR/ycEtFbg5IqY/JlAiApiq7W4GB4lRNYoSsY7RrXYIemip9Nt2JCqLDwdgKocQ=="}]}, "directories": {}}, "0.4.11": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.11", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "e285b7c31eb0406cf5a8e3e09bc16fbd2786360f", "_id": "iconv-lite@0.4.11", "_shasum": "2ecb42fd294744922209a2e7c404dac8793d8ade", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2ecb42fd294744922209a2e7c404dac8793d8ade", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.11.tgz", "integrity": "sha512-8UmnaYeP5puk18SkBrYULVTiq7REcimhx+ykJVJBiaz89DQmVQAfS29ZhHah86la90/t0xy4vRk86/2cCwNodA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNyYms2k0yV0VWsyQZo9bEMv3SbLppEMEM5MXd52FckAiBIkHE24oEFi/edXvDrFAMvZgnUy70EanchB9nbYUXMIA=="}]}, "directories": {}}, "0.4.12": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.12", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "5f5f71492e287c9c7231009103ddf9e8884df62d", "_id": "iconv-lite@0.4.12", "_shasum": "ef4bb2cb28f406d3c05fc89feea4504624b5ac87", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ef4bb2cb28f406d3c05fc89feea4504624b5ac87", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.12.tgz", "integrity": "sha512-qZa6DPRJZE6Z9GyWPaxRV1t0MzlFP+CXt1ZrdcdLoORmIX+YMkPE7mTc3rgV20Of0gTHYmK5Yaaqbm6pRnNUcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5TkmRBtqEVlQCACWrJy34pZtnrZ21atsYMYD6GIks3QIgMaHywwLo3XLo88KyrAvRagul0/ZmX7DWNiQR9URkrw4="}]}, "directories": {}}, "0.4.13": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.13", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "f5ec51b1e7dd1477a3570824960641eebdc5fbc6", "_id": "iconv-lite@0.4.13", "_shasum": "1f88aba4ab0b1508e8312acc39345f36e992e2f2", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f88aba4ab0b1508e8312acc39345f36e992e2f2", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz", "integrity": "sha512-QwVuTNQv7tXC5mMWFX5N5wGjmybjNBBD8P3BReTkPmipoxTUFgWM2gXNvldHQr6T14DH0Dh6qBVg98iJt7u4mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTsJjLztVE/fR+DeKY68yy7W3pkTWGp7+JAkIybLPXXgIgdg8/fMEVyY9gWPAyzpB78D7lzcC6MSHgNTuW8Q5d2/k="}]}, "directories": {}}, "0.4.14": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.14", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "*"}, "gitHead": "65beacd34d084bbc72ecc260f1ae4470a051cc51", "_id": "iconv-lite@0.4.14", "_shasum": "0c4b78106835ecce149ffc7f1b588a9f23bf28e3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0c4b78106835ecce149ffc7f1b588a9f23bf28e3", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.14.tgz", "integrity": "sha512-ytSDQ5GzXGclH/4/lAjJ6o7hxQQdXZ8CvnglNXDBbHsts5lBz/cmVbKihWlbgaXZWGm3GzSWosKw6r1uMbiKng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMv6ZXoEOkZhAJQOpeA7I+GVq8IlvL6bDM5cyH1Uk7awIhAKUl25VPOHReiSMWbPFOdhAtA5piuWiajrQ/RQwxkL22"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.14.tgz_1479707686517_0.8387471821624786"}, "directories": {}}, "0.4.15": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.15", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "*"}, "gitHead": "c3bcedcd6a5025c25e39ed1782347acaed1d290f", "_id": "iconv-lite@0.4.15", "_shasum": "fe265a218ac6a57cfe854927e9d04c19825eddeb", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fe265a218ac6a57cfe854927e9d04c19825eddeb", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.15.tgz", "integrity": "sha512-RGR+c9Lm+tLsvU57FTJJtdbv2hQw42Yl2n26tVIBaYmZzLN+EGfroUugN/z9nJf9kOXd49hBmpoGr4FEm+A4pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZ0foTDLbaJhVGiQHoj3NZ7QeHqEiHwttKX4SOk16WIwIgf5xXnPmSlsx57Fdw8RFez/gszBNkUAruldfVLfTMVno="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.15.tgz_1479754977280_0.752664492232725"}, "directories": {}}, "0.4.16": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.16", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "bf0acd95103de6fd624d10b65abaf6e91753c4c8", "_id": "iconv-lite@0.4.16", "_shasum": "65de3beeb39e2960d67f049f1634ffcbcde9014b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "65de3beeb39e2960d67f049f1634ffcbcde9014b", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.16.tgz", "integrity": "sha512-1OQ/A9QOJsAFxBvoN6Sz6I7aOghM6vVRcO6JuQ0O2YBOSTAGA2kBGtB11ejON7c6dRA4cvYhRftqpqf/LlAroA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB25nsupcp4luStqK6AI69akdko9EmTkqN7gMrqRpEUOAiEAyY76mK0pU/etWKcqpznTENG5vqFe3pw47+omx9Oh8DQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.16.tgz_1492901339391_0.7763116010464728"}, "directories": {}}, "0.4.17": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.17", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "64d1e3d7403bbb5414966de83d325419e7ea14e2", "_id": "iconv-lite@0.4.17", "_shasum": "4fdaa3b38acbc2c031b045d0edcdfe1ecab18c8d", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4fdaa3b38acbc2c031b045d0edcdfe1ecab18c8d", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.17.tgz", "integrity": "sha512-vAmILHWeClQb9Qryg5j1EW5L3cuj2cqWGVL2ireWbRrUPtx7WVXHo4DsbFCN1luHXLGFJ34vt2aryk/TeYEV8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJxFe7rxppg7LqUX1thhKBqkG5oJ6lzBB51ghSor18/AIgKZJusQFc0hrBrSuhrw8Ie8qtu9Czt4XM2Vn17RSbBtM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.17.tgz_1493615411939_0.8651245310902596"}, "directories": {}}, "0.4.18": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.18", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "637fbc0172247da3b4bf57685dd945b786ca2bee", "_id": "iconv-lite@0.4.18", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sr1ZQph3UwHTR0XftSbK85OvBbxe/abLGzEnPENCQwmHf7sck8Oyu4ob3LgBxWWxRoM+QszeUyl7jbqapu2TqA==", "shasum": "23d8656b16aae6742ac29732ea8f0336a4789cf2", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCujVlOsznkeTkoWXErfSOv40/BvizaU2NZOKxaiWVxggIgKJwuSNv2/aX3GKMi9G19k6E41ocrEqefV5nNSRx9SDI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite-0.4.18.tgz_1497367212038_0.6705294267740101"}, "directories": {}}, "0.4.19": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.19", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "5255c1b3c81a0f276619cce3151a1923cba90431", "_id": "iconv-lite@0.4.19", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oTZqweIP51xaGPI4uPa56/Pri/480R+mo7SeU+YETByQNhDG55ycFyNLIgta9vXhILrxXDmF7ZGhqZIcuN0gJQ==", "shasum": "f7468f60135f5e5dad3399c0a81be9a1603a082b", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgrEzEmsYBjdYvQjK9s6tLPIhvTa4qrsyHP4lmZLEB8wIhAInKWZM8FkhigjTI59Gs6x7L+bkrUDkaeUHDSM9QJKLg"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite-0.4.19.tgz_1505015801484_0.10463660513050854"}, "directories": {}}, "0.4.20": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.20", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": "^2.1.0"}, "gitHead": "9a6ad952f47639d16c1e7273d07a5660ab0634e1", "_id": "iconv-lite@0.4.20", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YyvWZ7Konl8yQCyGdFub5XmVqQonxkFjDoExIY22RA0NI0pskdU6plSyaUnVyEL+RsOcz+LhPDclXsc02indDQ==", "shasum": "c1f7a1dbd98de51f275776575ebfa67433d01d22", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.20.tgz", "fileCount": 27, "unpackedSize": 335838, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+kt2SzVec9PZ5xjvGHJ2Na/AlYNrG09Y02M6jy1iRLQIhANVzFSKFcdA4QTWQG6T4gGmIXE46pZc9ravCETzXHVYT"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.20_1523073755298_0.9205402977665564"}, "_hasShrinkwrap": false}, "0.4.21": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.21", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": "^2.1.0"}, "gitHead": "c679ab26ad95a804ff95671d7258a505ccba36c2", "_id": "iconv-lite@0.4.21", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-En5V9za5mBt2oUA03WGD3TwDv0MKAruqsuxstbMUZaj9W9k/m1CV/9py3l0L5kw9Bln8fdHQmzHSYtvpvTLpKw==", "shasum": "c47f8733d02171189ebc4a400f3218d348094798", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.21.tgz", "fileCount": 27, "unpackedSize": 335343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYslU3EZaPVZ/4cw7lXymW5P1J12lftlGKSXwIWRQcmAIgB2mXex5KImN3QDYixJNUAA//uebPxHeL6RSRt/qHq3U="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.21_1523075472870_0.21793181179943022"}, "_hasShrinkwrap": false}, "0.4.22": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.22", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "6b9091873b12929a605c819a547ce73a916ccd01", "_id": "iconv-lite@0.4.22", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1AinFBeDTnsvVEP+V1QBlHpM1UZZl7gWB6fcz7B1Ho+LI1dUh2sSrxoCfVt2PinRHzXAziSniEV3P7JbTDHcXA==", "shasum": "c6b16b9d05bc6c307dc9303a820412995d2eea95", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.22.tgz", "fileCount": 27, "unpackedSize": 335550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7kA9CRA9TVsSAnZWagAARFIP/jXu8+CmuVjFVeZcFzV0\nw5bKRhVRux7lUMBMVCDwz4BLGb/bob7qnLmwjxbeUuPfcZSwrdk397XtoD0T\nFavSzYqkbojvoGPkN8jo+ee7KX3hwv0Dki953NMWEUy8r5ECenb2BoveHl3u\nnHiGpv33OcjWbZgY7u6MXONvMEMVfUB8z0s12Vd6dbO3UMB6SYWwK+1Ex49f\nVfEBIgC8watumFKTnJBdCGvsdyJOrAeDvdXz3BZpnpDmA7UCl45+VdO8rwQF\nqESLt+YcUDcgfjxgDTr1VLo3ahULAZ7iGkCjFrB8A4+xHq8I448aZDbUoWND\n98dLkOJLNM1YnFULdlWv9PHQK2Dmn2c6MPQP7TvxOCHOIc0xWMfwpl2t5Z1s\nPt4hqaRGz3MAVzCc8/dOq5MWtwCqJXmotTzJ4q4EmdYyu33aEgIp+TO66oNp\nWbqP5zZjW/pdYh5uWvX9CUSO8B6uG951TC5hxmbG+fm07HyVymMWjPKLqi4C\n4e7kr5EjJ/B2o2oH4l4lpeBc/qZ9Oyr4m7bbfL9abMPs/jCGnmDW9UxFXp2G\nHYTYqR+pKynwhn85PpTGOaCJ2zHpAX9rEM8hqe3UUSTK+B2T5x3cNc6iWOlE\nlyWMNavUduZlB4LXTcpyjeRWmiwI70w8T3rxgfCoq/koFeIMLrd+U+VkP0Zl\nvmU0\r\n=ilVm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqwW3/SdPNmrrtEN1f6lzHnBu+7VuAodX3OHz13/mseQIgVZ+87sN8eiBdxZnB6+CaziXYERheEBknj+El2lGayz0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.22_1525563451449_0.750337618259016"}, "_hasShrinkwrap": false}, "0.4.23": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.23", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "d37d558b3caf5a158bf70accb788c36286fffca5", "_id": "iconv-lite@0.4.23", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "shasum": "297871f63be507adcfbfca715d0cd0eed84e9a63", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "fileCount": 27, "unpackedSize": 335778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8NGhCRA9TVsSAnZWagAA5ZoP/2O9a5eki82Fy1DP/jJU\nx3nUceaekdMIiOFVtP3akk0Uvx00TeGIANH4qyLulwW0R1cYJyTP4T3eelON\nYzznGtHEGLlzKUS5y+ICOR+X0V6TujHPDU1AP6QTiqx7lMBiaqdEkBfKncRV\nti9SUSvewr+oj4q0+mnLgC+iGK+Sl3QyPcBeZQM5dn3T8nb1UeutQKsc/dAJ\nEjCju6fZyiUNPTPeS62hLoprTJXeRa6Rsg48alnVIlwAsfEYMSDuEkjnX7EV\nNe0P8AIEBvB4VJ9pcpOlzdp4uHKSNqUKBHfNTDn7yP2U0THhjXMYhlKgP6kx\nY516QoSyj2S4bUl2DTyfK6y9HtC//BMqaWgKKfOjQCiALYLTiOxNWGzXOH53\nl4Vtm4NBTVpEZiQBYnnfFZUb/1tI4rWzM4nJOq3sAVlC4V9NI22V3fw7AkLd\nuC5DCLsfU3l+HV5A7reFIn7WQuoBc2mzg5iZVYU+YDGbO1rj+O/s/Mn2hqc/\nNf0vMyr5CVHPIhFLf8cY4DZeX+IXyGVhbBNFvzGfpRwTel5ksEKAwKsVhJRs\nRkuLwtq2+fmbX0tvjKC31trK+MYXihYfM2fya0JXY9uUEueHYYDLS+G/eUJC\npSASKzTaJFsbzMHRMMUz9kkcVGW631eVtCWXGXBEgLRi3wCiwiS20eth4Zvr\nQJ6d\r\n=mBpw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtrUWuUcdM/oemBnGeBRZrQHGmjfuQlxiNToThusCIiAiEAqCvEgwxbuMI9HqBhu3I9TxdT/WCN6xfUn2M7Ge/SsQs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.23_1525731744169_0.30693622174256374"}, "_hasShrinkwrap": false}, "0.4.24": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.24", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "efbbb0937ca8dda1c14e0b69958b9d6f20771f7a", "_id": "iconv-lite@0.4.24", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "shasum": "2022b4b25fbddc21d2f524974a474aafe733908b", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "fileCount": 26, "unpackedSize": 335941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfcYwCRA9TVsSAnZWagAARlIQAJ0ml9Ow+Z9mHwP+vAM/\negB/zY3a6Mq2l0ediXft4nQDlVUspdYCXCu5vtwO0z9wao5VisC0RXnEoVoT\n8u2OddejmkpVcGuGKfVuTk/OdWt6Z8yHGIPxUoPzHUmDL3cjWxS/dPkdlBLA\n6X5Ix5kcHbR2mDS0moEO1Pjp6zWbFN8vzNXe6HBOhpN1Z6cm2ppNETG7JTTR\n3mPfp1HbEY6BA04avf0ZE+/snD6zM6nGZY5WGnWOiYdaEVi1Ol5GBjTeVB3z\nCRo8CSSS+wq/iggtSoR12lvAcQlsR0T+blzWNJ6w6lt1GuwKsWg2pdJbmGcC\nVIii/Cq7Sh4wFzLYkDkBudjUesKx6AbKA8EcO0/2im9quQpie/AXdpSKV/LH\nTgN3SDY5/90/8L0Kn0YIaYAxnNoHjN7vX4fGeFyzcbViArhud0vRzyp2gnVO\nK5I2z4tiO+lIKxaSSOSxSt7+x1Fkn9zU1/ZJuIiv31AuMaQVDrAbi5GjegLs\nv/UBrWxfvIYsoNX1rnZpf2+iHaRRCSgECcGZz2W/b5DAXhA5YA87KrDPn5Ut\nMEgRY9eehGAz7lzKt0Zfs/zmoCwfe6ZErAxNHKCsnjV6c9uSto8Jya0zfENG\n2Jhrsbyj1N9fECq8U1+E4GLhecrOCJzF7bJi7KeXo9isIZQt+BsPBgeYbzSJ\n9uVK\r\n=GkHx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQHAdkOAe+cyhC4S2NHjFRabJQEFFcBeIdiHLwYeCUPgIhAO4iyNrG6CMVlS8yAMBb4aJxaUWr3eRpuw40MJpbogNN"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.24_1534969392040_0.7324868237182729"}, "_hasShrinkwrap": false}, "0.5.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "2b4125d11a733a40e45a755648389b2512a97a62", "_id": "iconv-lite@0.5.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NnEhI9hIEKHOzJ4f697DMz9IQEXr/MMJ5w64vN2/4Ai+wRnvV7SBrL0KLoRlwaKVghOc7LQ5YkPLuX146b6Ydw==", "shasum": "59cdde0a2a297cc2aeb0c6445a195ee89f127550", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.5.0.tgz", "fileCount": 27, "unpackedSize": 346031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE/HdCRA9TVsSAnZWagAAtwwP/0A7gJ8ks851kXBTq6VJ\nhWlcoH4O4p+EXHO2oSPHqNiBVKme4KJH/eZelsKcLM3DdL4KjQEdGRT0kcEF\nimeagOhpZxIvP5uS54170P/uNpxtSxqFbpdWEmNoK6InVt2mKXbEosaIVIyu\nClFLlD0JftT3jkRe35egrN/2HTbI5tS/Xms15u41L5bgJnB7PAuXwZFBD9Rx\nHolgA2ENPHKXOzxJ2s338Cawonz3L2flW++KJwUIp6SgyFUrfOZfacSS/8UK\nQhaESFlnL17geSZOdgp/8Uxc8ClOFw8cs3lFVTW5tSsGBlVPwxgyEhTXKbXH\nRxPMycpRoulWGvpzfJuW114kco3KGsYpeHiafyQ2RBQUCXIXIfMUxztXjMtW\nNkTPNAwgW/X0P3cuUdx4tEwvHWFQLI4pscTufczsMLYn7cgMJs5pSaX/szYi\nrb14yLD02tBARpKH57PoEEfgustZvSJDCpKpaYycyzBIRSrXIsjBXpprQqab\nSZ9qoKuSJNRQueJTTDg2juSNHjQwucMQ0/5G1O9LnrRaiTjq8mY8Yq5Fm7aU\nu5GRwOZ0XdkqYV7QA7fbSKIG23VgqEjhl2HLESjhvPOHS4imw+/EUyMqyR8U\nb+lWqytvWRiGT0UNTz8swnNH1zvrkatb2MC320/dCK9VGsNJnBMSGNQOBZ2E\n8nnF\r\n=y8ow\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzdhUvQE/LMgoRHBFxT8/lVVG+/P99wcXwc5+nZ5uSrgIgci49BnGRAUq356FxSIQQ6WL1DZ/QcDDnPhr9lZaInqs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.0_1561588188650_0.5881942713503567"}, "_hasShrinkwrap": false}, "0.5.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "c60e647d0d825ad3815d0865e871fabb68a531df", "_id": "iconv-lite@0.5.1", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ONHr16SQvKZNSqjQT9gy5z24Jw+uqfO02/ngBSBoqChZ+W8qXX7GPRa1RoUnzGADw8K63R1BXUMzarCVQBpY8Q==", "shasum": "b2425d3c7b18f7219f2ca663d103bddb91718d64", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.5.1.tgz", "fileCount": 27, "unpackedSize": 346613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIrkZCRA9TVsSAnZWagAAWewP/jg46EbbijEKJ4Rv89jW\nzbhQVRIbKDH0yjRC7e440Eb+vJ8OJZqyIcpkDJhgUQW0KkH3NGoaiVbevBfi\nTUNIM0CuMn8Vfiyb84JyQLEYoJVy9ONgY/4+jnqfDTIm/Rcj7lviNExsjzVe\nazfyGI6nanK31cAw0uiZx/BVr47uvgReaDRoWIgsNeFMLoN0OeOr9+4ZmKIG\nvbtYIxRLv4IJMJUk4LFJwU74Pi90CXNsKq4nEQ2njbl8oyZlM9CqNHrZnQ9j\n/nTy2SJNDfH+XpAv32IuLYKrtle3UZvpa174cZjGea+2D/Mok6hWy+XnSLhe\nX4yq1+H952YjAsaORwUSSIBDOuFo3sUNDs08owexbjXgpPRW1kWSQxFWm0c8\nWKkR20uBtT/FucFrguxClKdH8bN0zEkCRJFI3jUmEz23aOpfBsIGGWMN0xXr\n34u+PG2533SbEVVhdtEZxaaNy03Y1Vn0Havwg4fWbXZoofK1Zikp8x89/gXY\n2WBi26EumiG9Vp6eFOBy82ti/MqybpjT/HhW/vV/DBQ/PCeJhymWtiqFF76g\nFFtIkO8tQJt08mVi0ZHaeA3oekiKwp/5P8IbC9Ii4OFIg/ZNylL55vN9l1/h\nejGtS19hy53dLXqCM+kdcn+vLW8tYX15sHLhyFu9JyMk17guLZpni1Ifyea2\nD/uC\r\n=xZUQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdgSNIeWdCRedVNg6UBl9sdmDvGPsVchbR51V/WI5RBAIgd1oXqGEYyCZL9SksFaZ7XyMqSkQzvUVIIKRfJknSc8M="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.1_1579333912541_0.3489234031838713"}, "_hasShrinkwrap": false}, "0.5.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "c8": "*", "semver": "6.1.2", "iconv": "2"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "5148f43abebd8dabe53710d6056558ba66e089f9", "_id": "iconv-lite@0.5.2", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kERHXvpSaB4aU3eANwidg79K8FlrN77m8G9V+0vOR3HYaRifrlwMEpT7ZBJqLSEIHnEgJTHcWK82wwLwwKwtag==", "shasum": "af6d628dccfb463b7364d97f715e4b74b8c8c2b8", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.5.2.tgz", "fileCount": 27, "unpackedSize": 347266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3fcHCRA9TVsSAnZWagAAao8P/j5oDnS5p5eTy0J3OcmU\nHA+LI3gpV2LErV2aBtP78GW+XVHHMcDkfKjWtWaTnl/RnpW0rDg1fKoGGReN\ntu3MIhOG/BuHNhOjbFtPCAysJs9SojQCrwmZocjpLbpaSNFDgDIhhTL24qxv\npmU6IhDaZ/MCQ9QZgn3ILo7sWj8b0y+AkzABsg4OniJbZ6H5S4TqfO1Hl1SK\nDdIk5b24IF4AEpNYcCGVSibwKJjMd1VqbMml+MidjCCTAKQW0so0xcSaJ7wN\nragAz3XXuSsOYynDpwBZuE3XOLqB0FoNkZDkuWU/tYAuhd0fqSVHC7iwBn5S\nRhFe0WBibB8uzER9F1JFugnWoCtxQlzeA0MCsnVJvpzQS1TBjUM83I67jb++\nZTPqjSs3xkzjiGnxpJXZPlMd0cCGOq4ghS/2+ljQ0dScBwK9c7sczodgC/kC\nvxPVFWnWEVP7oZ9fsfsstD21lyhmR29ONCjaew9dB0+eeeuxJz5x3fCU/rqO\nsDnJXOCQuh+CyM/bLgIL7BW5WM25KNtMiDi+p/o9Qsm/+k4zVSU5c6SUQSXZ\n3LQHZgnSyi5aqZkd1EVSWaM+7MTSZXsU4feKm4DcxWMt3kdFP4Q83JOeNNZ0\nhGttzmPYykGniUtA+EqvZ1kh0EcLAqr7bar3Gq033VXhu9mRcTc7niH7R5gJ\nCszj\r\n=6sWk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7bXG7ayiVZdgecgXejedpJ5gC5OMoBHpBT97dc4TXJAiAhGP8WYYdDrSRlaef5Y7SR2m4Mm/dyZyBAmxzX2NnaSA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.2_1591604998671_0.552415139324663"}, "_hasShrinkwrap": false}, "0.6.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "*", "c8": "*", "errto": "*", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "~2.87.0", "semver": "~6.1.2", "unorm": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "b106faaf15bb1bc66b20bdb81aa687415f54a7d4", "_id": "iconv-lite@0.6.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-43ZpGYZ9QtuutX5l6WC1DSO8ane9N+Ct5qPLF2OV7vM9abM69gnAbVkh66ibaZd3aOGkoP1ZmringlKhLBkw2Q==", "shasum": "66a93b80df0bd05d2a43a7426296b7f91073f125", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.0.tgz", "fileCount": 26, "unpackedSize": 341230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3f5PCRA9TVsSAnZWagAAU4oQAIwTqSukkJccai42Spy3\njE+xQdOopbPDVS2lVCfMJUiWWis76gohbZpVbq0ojI0rl8s2pft9j8HlYRNK\nGzsoq8GKtZAuk0E22HIA8Gn318HC/iN7NfjPcCpsmLYg4lsWoUHLcPZbpvhx\nArIZPdiWkP7x/TjFmWFif2qX4OEzBw1GXbFtYlja9RHOqYuKoNeLp5+iRsGk\noAUWkqQDhfj16Fn8cvqP52EriCqUAJ9YONVFILllVdb9sZfe+IftCh25XUAM\ne/9Q/yI9ocO/vmwSBwHVMQRE7VBfdowLq8q7OTovP0THc+IvrelsWs8dzTxN\niQnEw0n7dyanih5/i58vwv518Lr5CU8440gUes+ErxswIS/4o+Hava6Yr41+\nTIJH6rg4fmIlDmgxrYeodMkbw27sW329ZfCOgx2rg5wKqVd5gterRrv1aVKc\nteOlDPR9efI1uVAwUB9+opv0bIXirD+6kqGCSGKcWPHxDlbzctmjqO+C0U0+\nkUQELQRnk877gN1peOYoO93TVkwbzitjNtvxzs80fMR9xss83CHWeGIBgbKn\nGfmpcNqcPKm/iYRwrQ6B9gxhMDH2PKK5NderdPscCy1ZPmH47t4d48bgrljR\nzf3vnTAF0oX/JvW2VsxarOstY1hVpGCzTCdyxfnWqOVApKXhMtFc4R8/2kZx\nmu2G\r\n=r6+x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsgVZo6FxRmtV12ezIlgDK2KNSpb1dtqGDgjZ9Tje65QIgVfANnY9AJv9MmkchWlYYIzysztDOm/6V2KiIyz+IJs8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.0_1591606862922_0.8978731391649568"}, "_hasShrinkwrap": false}, "0.6.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "724829e8fc39525fbeded0f837da53c13de179ae", "_id": "iconv-lite@0.6.1", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Gjcihg3Bi6PI+5V7JlqWmXyVDyX5UQuwulJcbb3btuSoXIoGUy8zwJpRIOpRSzHz0IVnsT2FkceLlM8mm72d3w==", "shasum": "dcff79a060333879dd83ebc3eb6a217f5f0facc5", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.1.tgz", "fileCount": 27, "unpackedSize": 342323, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+DQNCRA9TVsSAnZWagAA6jgQAJa3QxJ+IGwdnvkb+FIc\nEV2MAn22MLoMx8aMv2j5rRxIFlX1o/jVJP02fwlKZtPqBa0KPBepMUdySpBC\nuaYnuxjwKC5jwHJ5beMcR7T7q6d+M5fffI76JHgNnkbnSIa98R7akiFf6ki2\nO+IWQM7AC7TH2f8B2md1tscQ35pN4j0hj/cKmJE1E+/Qe+c7UxYRH6HtY5QO\n5FHwwbpAS7jJqiSoDzJDZ8oWabB/AzDhueC6+yBToXhF+ug3EnfEAyKKLhow\nX5NjjZih3qMHHn3LrU5w7t3v7AeKAL3FJbGM8vZII99hMIzFaIekoImCrolr\ns+rZX33tk2eoKV/QE16KfR+jNssqg95A/3GcwzDgSRBlKqo9p5paJ3LcEmp5\ncBLwU+BDZiG5JsfEp61PaP8hS/2DvTYFRVwq2uIdlfuIIL28cABw0wPJQOA4\n8bj2PumBXbDKtpZDY7SkWJKn99dWhE7lPigrF5d7jDhsMPYaFFnyojV3oVNf\nVBMW6Oj9zl2/NCp6P0pN4Ul/Wo9D6KzOxY0oEQ1z5Upop+jGP8uyftuWSctt\n3AoHZFYDxIRXgu1iJF2QR5A39I1dk4cXhbPjEAiG7UvF3Pop6mppbKd7D8pI\nWBxhop+6uIXEJ5kmPpM7ynUgyBx57GCLyhAVRXXyEtXNNzAvuFpdcMejMEW2\n1dcZ\r\n=ZnY8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChOk0ejt7g9coq/ypuIWOpsFNdKHifIW89CZ2zP8PyBwIhALyoq+Nsxxm8uPpil8SLkpWsW1hHSH0X1mpNRcTRLUlW"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.1_1593324557035_0.30138755547387275"}, "_hasShrinkwrap": false}, "0.6.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "efbad0a92edf1b09c111278abb104d935c6c0482", "_id": "iconv-lite@0.6.2", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2y91h5OpQlolefMPmUlivelittSWy0rP+oYVpn6A7GwVHNE8AWzoYOBNmlwks3LobaJxgHCYZAnyNo2GgpNRNQ==", "shasum": "ce13d1875b0c3a674bd6a04b7f76b01b1b6ded01", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.2.tgz", "fileCount": 27, "unpackedSize": 343859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBVioCRA9TVsSAnZWagAAK6sP/38nCaBRUQBf/hTRnE//\nf9cbc4OejIUesT2GajDLq2uIUL56BtN0kIe4J7r6bFjZEQGDUk9ryW23X1fn\nI8end6WxmMiWXzNiHqAh8v/B2MBpFRIeTyT4Ycl1w3AXesQsEHNpox9PVKF3\nQKgQEq9t8wyjY9lWg7cWaujdQgol5KVBA2YSyg0drqnjKag5F1rs+hSk868F\ncVaeVrbjX3Dvm/o905P1V8XQpARkDobfudzw/K+hVcRMwc6q4RyFl3Y19Gum\nnP2QbSzVGp8hbABPbWulfkixP6bWbKZ1F5BVakfzLZi1kkbuhA9D5ZgmBnBK\nq+mXoRZAZOjzXtA7cRbCZVtpzs2a74AVhl3Vvqrjo11xiC97tCgnAsUNTUOe\nmgltYFqf5ZrgdXQGCtq3ES8miyTYjFz/TzoUNMSOdCDVrDxkMzdggiMpPPpW\ntan7niWeEpcNDjLZHrqs3b5Ykyd8pJQT19/XtGdMcTfh1NC1+Zf0OS9GIXSN\nGXhDeqb4EGRiNLjlXXoVBSmUlXSgQc3PpeuQlmR2FrZj/20vDhy2rbmwTPny\nhVpBEwhV1LwrI5BF1AxvJeyxesDAsEfRGceg5HKcGLl6n13keh/CwFLzVWh4\naFrTmePS/ccu1n0fSNDo169bCIQTmx1MKBFS1uB1zmx3vomQHFIQTS0RzVrL\n6gMU\r\n=S6N7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGu0qi+jQrn7z7zA6+LyfFXzUPl/mlNoQzUTjugFwqP9AiEA1JclNK+h++BOpDjU6YFLJu0gvs76+joV7YB1RS+5qI8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.2_1594185895956_0.9242056530346248"}, "_hasShrinkwrap": false}, "0.6.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "d13de386c07a1574425811f8f219c181c633e88d", "_id": "iconv-lite@0.6.3", "_nodeVersion": "15.6.0", "_npmVersion": "7.6.1", "dist": {"integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "shasum": "a52f80bf38da1952eb5c681790719871a1a72501", "tarball": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "fileCount": 33, "unpackedSize": 348518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqxbCCRA9TVsSAnZWagAAcroP/Rqrts1/wHN9dJXkXtdS\n1IKvYULE9ATmoW937FdYmVJmSy8Z1WOYvqmcF+W1o5m/Gseo5fb1VevT/bqE\nSsIThcPU4MU9EN6uLTgA4Z6YJwtuzdaCVOq6q6KAYmZSI2Vp4qxf5OYarvfv\nF42y8mxy9NoE6X2bIGZW8yqbcam5TmxwU9T5NDhP3eCTp9d7n+E4uBOf8oLt\np6fDhCw1vOSOkbz2x8Bar/J8vR+JWOTUbiIrXyRA6eAYcGz61VZcqEd1rR5G\nGrNTBVAdPE/MXvwdhiQQ++BEWCwyE3NLjz4VZeRy0P0ZfSPRl0Qbm2ZbdoUU\nMh69bc2luE+6yXom6quHlkmchMNngdt5sNUh0cGnBRa98hLjz0qUTxTloTWS\nXp3fxFUwO1v6jCnihnhUV1R4DCu1OgQp8bJs9JXMsgtvvwjf7mFPGbtxSLoh\nFqCJBWDieN/4OsaLGb/BkdLRK9L1RWylQS+DE2XqgKc4Ag13vesKrtAa1poR\nLWKe5olRcM8YCDuJBhqSMjsPeZcbL/BMhVDcXi0xrenBIbt2DbLYdac4K4JT\n0BiCApA+LH8Jqa/NX43XVVHdnYTC+s9HhLtIsEHEv5U948SzhSKpDerZozbO\nmJ4m6Omg/zo2Q5E1QOBhYmI4hPUw9776hvkOkidQJt66OKmwaynx4nsfdG2/\nt5o+\r\n=//pT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSW7M7mQIAkBr00ylKb0LSsUZcoFW209OCE5NNaW1gbAiAbeRO4nflTwu3LXel4cm/zTmdXnshjnilZrIfaimURZA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.3_1621825217719_0.23976423925317714"}, "_hasShrinkwrap": false}}, "readme": "## iconv-lite: Pure JS character encoding conversion\n\n * No need for native code compilation. Quick to install, works on Windows and in sandboxed environments like [Cloud9](http://c9.io).\n * Used in popular projects like [Express.js (body_parser)](https://github.com/expressjs/body-parser), \n   [<PERSON><PERSON><PERSON>](http://gruntjs.com/), [Nodemailer](http://www.nodemailer.com/), [Ye<PERSON>](http://yeoman.io/) and others.\n * Faster than [node-iconv](https://github.com/bnoordhuis/node-iconv) (see below for performance comparison).\n * Intuitive encode/decode API, including Streaming support.\n * In-browser usage via [browserify](https://github.com/substack/node-browserify) or [webpack](https://webpack.js.org/) (~180kb gzip compressed with <PERSON>uffer shim included).\n * Typescript [type definition file](https://github.com/ashtuchkin/iconv-lite/blob/master/lib/index.d.ts) included.\n * React Native is supported (need to install `stream` module to enable Streaming API).\n * License: MIT.\n\n[![NPM Stats](https://nodei.co/npm/iconv-lite.png)](https://npmjs.org/package/iconv-lite/)  \n[![Build Status](https://travis-ci.org/ashtuchkin/iconv-lite.svg?branch=master)](https://travis-ci.org/ashtuchkin/iconv-lite)\n[![npm](https://img.shields.io/npm/v/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n[![npm downloads](https://img.shields.io/npm/dm/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n[![npm bundle size](https://img.shields.io/bundlephobia/min/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n\n## Usage\n### Basic API\n```javascript\nvar iconv = require('iconv-lite');\n\n// Convert from an encoded buffer to a js string.\nstr = iconv.decode(Buffer.from([0x68, 0x65, 0x6c, 0x6c, 0x6f]), 'win1251');\n\n// Convert from a js string to an encoded buffer.\nbuf = iconv.encode(\"Sample input string\", 'win1251');\n\n// Check if encoding is supported\niconv.encodingExists(\"us-ascii\")\n```\n\n### Streaming API\n```javascript\n\n// Decode stream (from binary data stream to js strings)\nhttp.createServer(function(req, res) {\n    var converterStream = iconv.decodeStream('win1251');\n    req.pipe(converterStream);\n\n    converterStream.on('data', function(str) {\n        console.log(str); // Do something with decoded strings, chunk-by-chunk.\n    });\n});\n\n// Convert encoding streaming example\nfs.createReadStream('file-in-win1251.txt')\n    .pipe(iconv.decodeStream('win1251'))\n    .pipe(iconv.encodeStream('ucs2'))\n    .pipe(fs.createWriteStream('file-in-ucs2.txt'));\n\n// Sugar: all encode/decode streams have .collect(cb) method to accumulate data.\nhttp.createServer(function(req, res) {\n    req.pipe(iconv.decodeStream('win1251')).collect(function(err, body) {\n        assert(typeof body == 'string');\n        console.log(body); // full request body string\n    });\n});\n```\n\n## Supported encodings\n\n *  All node.js native encodings: utf8, ucs2 / utf16-le, ascii, binary, base64, hex.\n *  Additional unicode encodings: utf16, utf16-be, utf-7, utf-7-imap, utf32, utf32-le, and utf32-be.\n *  All widespread singlebyte encodings: Windows 125x family, ISO-8859 family, \n    IBM/DOS codepages, Macintosh family, KOI8 family, all others supported by iconv library. \n    Aliases like 'latin1', 'us-ascii' also supported.\n *  All widespread multibyte encodings: CP932, CP936, CP949, CP950, GB2312, GBK, GB18030, Big5, Shift_JIS, EUC-JP.\n\nSee [all supported encodings on wiki](https://github.com/ashtuchkin/iconv-lite/wiki/Supported-Encodings).\n\nMost singlebyte encodings are generated automatically from [node-iconv](https://github.com/bnoordhuis/node-iconv). Thank you Ben Noordhuis and libiconv authors!\n\nMultibyte encodings are generated from [Unicode.org mappings](http://www.unicode.org/Public/MAPPINGS/) and [WHATWG Encoding Standard mappings](http://encoding.spec.whatwg.org/). Thank you, respective authors!\n\n\n## Encoding/decoding speed\n\nComparison with node-iconv module (1000x256kb, on MacBook Pro, Core i5/2.6 GHz, Node v0.12.0). \nNote: your results may vary, so please always check on your hardware.\n\n    operation             iconv@2.1.4   iconv-lite@0.4.7\n    ----------------------------------------------------------\n    encode('win1251')     ~96 Mb/s      ~320 Mb/s\n    decode('win1251')     ~95 Mb/s      ~246 Mb/s\n\n## BOM handling\n\n * Decoding: BOM is stripped by default, unless overridden by passing `stripBOM: false` in options\n   (f.ex. `iconv.decode(buf, enc, {stripBOM: false})`).\n   A callback might also be given as a `stripBOM` parameter - it'll be called if BOM character was actually found.\n * If you want to detect UTF-8 BOM when decoding other encodings, use [node-autodetect-decoder-stream](https://github.com/danielgindi/node-autodetect-decoder-stream) module.\n * Encoding: No BOM added, unless overridden by `addBOM: true` option.\n\n## UTF-16 Encodings\n\nThis library supports UTF-16LE, UTF-16BE and UTF-16 encodings. First two are straightforward, but UTF-16 is trying to be\nsmart about endianness in the following ways:\n * Decoding: uses BOM and 'spaces heuristic' to determine input endianness. Default is UTF-16LE, but can be \n   overridden with `defaultEncoding: 'utf-16be'` option. Strips BOM unless `stripBOM: false`.\n * Encoding: uses UTF-16LE and writes BOM by default. Use `addBOM: false` to override.\n\n## UTF-32 Encodings\n\nThis library supports UTF-32LE, UTF-32BE and UTF-32 encodings. Like the UTF-16 encoding above, UTF-32 defaults to UTF-32LE, but uses BOM and 'spaces heuristics' to determine input endianness. \n * The default of UTF-32LE can be overridden with the `defaultEncoding: 'utf-32be'` option. Strips BOM unless `stripBOM: false`.\n * Encoding: uses UTF-32LE and writes BOM by default. Use `addBOM: false` to override. (`defaultEncoding: 'utf-32be'` can also be used here to change encoding.)\n\n## Other notes\n\nWhen decoding, be sure to supply a Buffer to decode() method, otherwise [bad things usually happen](https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding).  \nUntranslatable characters are set to � or ?. No transliteration is currently supported.  \nNode versions 0.10.31 and 0.11.13 are buggy, don't use them (see #65, #77).  \n\n## Testing\n\n```bash\n$ <NAME_EMAIL>:ashtuchkin/iconv-lite.git\n$ cd iconv-lite\n$ npm install\n$ npm test\n    \n$ # To view performance:\n$ node test/performance.js\n\n$ # To view test coverage:\n$ npm run coverage\n$ open coverage/lcov-report/index.html\n```\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-07T05:00:30.248Z", "created": "2011-11-09T17:51:01.242Z", "0.1.0": "2011-11-09T17:51:05.090Z", "0.1.1": "2011-11-23T12:55:22.201Z", "0.1.2": "2012-03-09T18:49:04.510Z", "0.1.3": "2012-05-06T10:06:54.542Z", "0.1.4": "2012-05-06T11:24:01.572Z", "0.2.0": "2012-05-07T17:43:01.137Z", "0.2.1": "2012-06-29T14:49:19.424Z", "0.2.3": "2012-07-13T14:07:06.630Z", "0.2.4": "2012-08-24T21:10:48.490Z", "0.2.5": "2012-08-26T02:55:50.688Z", "0.2.6": "2012-11-20T07:39:43.417Z", "0.2.7": "2012-12-05T06:40:33.383Z", "0.2.8": "2013-04-16T20:41:10.178Z", "0.2.9": "2013-05-19T23:16:51.894Z", "0.2.10": "2013-05-27T03:33:58.336Z", "0.2.11": "2013-07-15T01:23:17.021Z", "0.4.0-pre": "2014-04-25T21:50:56.999Z", "0.4.0-pre2": "2014-04-26T02:35:46.872Z", "0.4.0-pre3": "2014-04-28T10:27:26.683Z", "0.4.0": "2014-06-11T03:06:17.844Z", "0.4.1": "2014-06-12T04:58:44.366Z", "0.4.2": "2014-06-12T23:55:05.316Z", "0.4.3": "2014-06-15T04:57:36.477Z", "0.4.4": "2014-07-17T04:53:40.014Z", "0.4.5": "2014-11-20T10:38:59.501Z", "0.4.6": "2015-01-12T14:46:50.603Z", "0.4.7": "2015-02-06T07:41:29.590Z", "0.4.8": "2015-04-14T17:46:59.824Z", "0.4.9": "2015-05-24T12:47:29.122Z", "0.4.10": "2015-05-27T06:18:34.272Z", "0.4.11": "2015-07-03T21:29:49.475Z", "0.4.12": "2015-09-26T21:17:50.923Z", "0.4.13": "2015-10-02T04:10:55.543Z", "0.4.14": "2016-11-21T05:54:48.478Z", "0.4.15": "2016-11-21T19:02:59.113Z", "0.4.16": "2017-04-22T22:48:59.698Z", "0.4.17": "2017-05-01T05:10:12.243Z", "0.4.18": "2017-06-13T15:20:12.238Z", "0.4.19": "2017-09-10T03:56:41.914Z", "0.4.20": "2018-04-07T04:02:35.444Z", "0.4.21": "2018-04-07T04:31:13.009Z", "0.4.22": "2018-05-05T23:37:31.575Z", "0.4.23": "2018-05-07T22:22:24.294Z", "0.4.24": "2018-08-22T20:23:12.162Z", "0.5.0": "2019-06-26T22:29:48.857Z", "0.5.1": "2020-01-18T07:51:52.746Z", "0.5.2": "2020-06-08T08:29:58.934Z", "0.6.0": "2020-06-08T09:01:03.203Z", "0.6.1": "2020-06-28T06:09:17.204Z", "0.6.2": "2020-07-08T05:24:56.196Z", "0.6.3": "2021-05-24T03:00:17.928Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "users": {"326060588": true, "792884274": true, "pekim": true, "leesei": true, "pana": true, "timur.shemsedinov": true, "derektu": true, "redmed": true, "serdar2nc": true, "croplio": true, "kahboom": true, "boustanihani": true, "rentalname": true, "louxiaojian": true, "masaaki": true, "magemagic": true, "cshao": true, "samhou1988": true, "alanshaw": true, "novo": true, "hrmoller": true, "52u": true, "moimikey": true, "staraple": true, "itonyyo": true, "programmingpearls": true, "luhuan": true, "h0ward": true, "leonning": true, "lokismax": true, "mikend": true, "shacoxss": true, "chrisyipw": true, "luobotang": true, "krot47": true, "jackchi1981": true, "po": true, "igor.gudymenko": true, "antanst": true, "byrdkm17": true, "demopark": true, "lijinghust": true, "xinwangwang": true, "modao": true, "rianma": true, "vtocco": true, "jasonwang1888": true, "runjinz": true, "scrd": true, "tongjieme": true, "dahe": true, "diegoperini": true, "tigefa": true, "lianhr12": true, "wangnan0610": true, "marsking": true, "manikantag": true, "monolithed": true, "nuer": true, "vjudge": true, "huigezong": true, "mojaray2k": true, "monjer": true, "cliffyan": true, "seangenabe": true, "lonjoy": true, "nice_body": true, "4rlekin": true, "ninozhang": true, "evdokimovm": true, "jaxcode": true, "chaoliu": true, "0936zz": true, "heineiuo": true, "binginsist": true, "junyiz": true, "xueboren": true, "grabantot": true, "raycharles": true, "lbeff": true, "nayuki": true, "joan12358": true, "justdomepaul": true, "maemichi-monosense": true, "morogasper": true, "mysticatea": true, "miacui": true, "artjacob": true, "xiaoyangyangly": true, "tzq1011": true, "evanlai": true, "faraoman": true, "haihepeng": true, "sammy_winchester": true, "wujianfu": true, "jinglf000": true, "zjj19970517": true, "zhenguo.zhao": true, "kuaiyienchou": true, "jsyzqt": true, "wxfskylove": true, "michalskuza": true, "poplartang": true, "tomgao365": true, "daizch": true, "flumpus-dev": true}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "license": "MIT", "readmeFilename": "README.md"}