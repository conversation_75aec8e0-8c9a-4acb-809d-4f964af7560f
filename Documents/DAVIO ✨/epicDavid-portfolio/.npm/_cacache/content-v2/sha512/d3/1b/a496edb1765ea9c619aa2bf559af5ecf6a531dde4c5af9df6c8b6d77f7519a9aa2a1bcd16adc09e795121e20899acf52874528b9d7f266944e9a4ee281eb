{"_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "_rev": "21-881b2f5532f8fca6c7927ba2e358b5ab", "name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.24.4": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.24.4", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "6125f0158543fb4edf1c22f322f3db67f21cb3e1", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.4.tgz", "fileCount": 5, "integrity": "sha512-qpl6vOOEEzTLLcsuqYYo8yDtrTocmu2xkGvgNebvPjT9DTtfFYGmgDqY+rBYXNlqL4s9qLDn6xkrJv4RxAPiTA==", "signatures": [{"sig": "MEYCIQCX/fexXx/2lTSgH9Mjk1oNy92xKa/q71lvxLWl/4vdFwIhAND4tklyomqAcFg7bzUycFBAzXXAkV/PPPTstIl24d3O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-environment-visitor": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.4", "@babel/traverse": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.24.4_1712163226586_0.4574774425939925", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "fbbab3cf738a67959853ea03311a44353c76d1d4", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-4/dQSSG/b/hid6Pq59zkCBFK04VhS1QX49kJwWsmFQKzoeTwTw+unhYkHTHvQ9LPPZb/a7S6pzP6FlW5DfNfPw==", "signatures": [{"sig": "MEQCICOWXkuWjd/1IW+5wPY1aWcgNP5Xrqqkhv5uKarQLmIuAiAvPT78mEEX1eLo6buR6p2ep6Pe66uduAZwEYTvNnkMXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12040}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.8_1712236774265_0.3405326589219866", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.24.5", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "4c3685eb9cd790bcad2843900fe0250c91ccf895", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-LdXRi1wEMTrHVR4Zc9F8OewC3vdm5h4QB6L71zy6StmYeqGi1b3ttIO8UC+BfZKcH9jdr4aI249rBkm+3+YvHw==", "signatures": [{"sig": "MEUCIQCw8T/6LTettgGaDI3wNFHGdTnxwYTBd+Id+7xkbSa58QIgalJi3ZkWhWC3pCpcJBwUPNe7aD3TjwRtjkwi0S/Vo5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78141}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "@babel/helper-environment-visitor": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/traverse": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.24.5_1714415655673_0.7789000369163892", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.24.6", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "283a74ef365b1e954cda6b2724c678a978215e88", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-bYndrJ6Ph6Ar+GaB5VAc0JPoP80bQCm4qon6JEzXfRl5QZyQ8Ur1K6k7htxWmPA5z+k7JQvaMUrtXlqclWYzKw==", "signatures": [{"sig": "MEUCIQCTDKRf0KJy/NVrtoMhw+RnB+rCpc9CuuDLJ0FKqq2VTQIgfXX1ruwL3KaPtRSYgAB9jtKAHgp4rh1tODASc6IwRr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78367}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.24.6_1716553455618_0.301743409058185", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "a2bf0d25c42dede598ca156ea0cfccc4253cd63d", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-np6bxPHebNqd7FhZjhlObpuU+t1IfYCw5z+ScBKHE5PrK9NdQgiyJo7rtEf6gAnSDGAnZSFrR5NDxhgshACqGA==", "signatures": [{"sig": "MEUCIQCScA45hP7NHwd+lWzS7uLWDMI72rG+qf6gGjtse8bAMgIgN+A3MUfYCbIdTBGmX4vsSsvygx0BKO/8cjzew894icE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78575}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.9_1717423484018_0.43646682912318124", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "83c92cb4c72f03bf311fb4108c55b4579a658597", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-M6yr5gN4pttPhT0lkYOraKNGvoGHzqFS7eHrn6vb4vbsF5Au0M23gxDybqCymlpM1QT9hc1iNdNIKgQFnDuSlQ==", "signatures": [{"sig": "MEUCIEcmogQgzpZNCPjbU0X3LUrpZLjOCw6VtiQ8JZb1nLmRAiEAta4CBjwkwut9mIlXGxFG1WyfXcFyx8EZsi4hgX2cPNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78583}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.10_1717500021995_0.18515795724663642", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.24.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "fd059fd27b184ea2b4c7e646868a9a381bbc3055", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-TiT1ss81W80eQsN+722OaeQMY/G4yTb4G9JrqeiDADs3N8lbPMGldWi9x8tyqCW5NLx1Jh2AvkE6r6QvEltMMQ==", "signatures": [{"sig": "MEUCICyCURz+3jR2Y674zRO5Cf5nMM4Pe4+PDRW/FLjKvS5/AiEAzHdPwzsEAoNIsjxqcxSsdFT7jpC5XKmBb/WpXldtWdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78317}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.24.7_1717593335475_0.425172001476686", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "10157a2e52d10ffc368e54d119d70df7cce9a3cb", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-cPQJvcQQDGZp9rZj/k/8fu4Ic1eXD/+MmGB2+fXsEDDCGOKUZLHkPpJ3KZw893AfnALfybq6DOHbD69tidfgew==", "signatures": [{"sig": "MEYCIQDA63CVHo7Ffz3tNB4lH8Sxu6/cVDCBCyFkEzGYoNsDBQIhAL+2NVe5llL734b8Yti+PrJMctl3o73NwfVIsn4A+56k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78472}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.11_1717751746145_0.835393121008356", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.25.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "328275f22d809b962978d998c6eba22a233ac8aa", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-dG0aApncVQwAUJa8tP1VHTnmU67BeIQvKafd3raEx315H54FfkZSz3B/TT+33ZQAjatGJA79gZqTtqL5QZUKXw==", "signatures": [{"sig": "MEUCIQDHkFLiIub8lGavXgUngywr8lde2eRwUhmvnLjMR1YjywIgRYrZ5l1R3zqTCXhcguvxCE5KbR0BovxYoNzUSHnJJ0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75100}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.25.0_1722013169070_0.5415099031646411", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "925f740ba7fc78767ec721df0fc5682ae5288edd", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-f6Gxxn46DgJ79p1hadxz6K+A3zs/u+rrg8NiGNmYaYK5clEt5HFNm0sTK78bk+OdoqQyLnHj7t3ZAHHMXVpF/A==", "signatures": [{"sig": "MEUCIA+go5T75H9enZNCOLQ30G3tF4CSci3A1sOM77fjjd8mAiEAmTOtJnxPikghGw86ybQs4ojR1uq4sPbD1g2rYiEmMwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75215}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.12_1722015230558_0.892130408164229", "host": "s3://npm-registry-packages"}}, "7.25.3": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.25.3", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "dca427b45a6c0f5c095a1c639dfe2476a3daba7f", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.3.tgz", "fileCount": 7, "integrity": "sha512-wUrcsxZg6rqBXG05HG1FPYgsP6EvwF4WpBbxIpWIIYnH8wG0gzx3yZY3dtEHas4sTAOGkbTsc9EGPxwff8lRoA==", "signatures": [{"sig": "MEYCIQDcnUoWFmyJztHTXaL2vgVZfqNeawowuq+5gqN16sD8OQIhAN4XTkFHRTb8fkiEunmzLdCQasOXlaXyCyw8LKjxuGer", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74542}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.3", "@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.25.3_1722428746039_0.8102831273809465", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.25.7", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "93969ac50ef4d68b2504b01b758af714e4cbdd64", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-UV9Lg53zyebzD1DwQoT9mzkEKa922LNUp5YkTJ6Uta0RbyXaQNUgcvSt7qIu1PpPzVb6rd10OVNTzkyBGeVmxQ==", "signatures": [{"sig": "MEUCIQDWb8DZyhiTUyz1qmkz1QOMhxahq/UCcCUTslGL3V1y/gIgaQ5rvnu/fDRHApS26kDPzOkF7IXYKQCXtg945Kf1Xf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82459}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.25.7_1727882118464_0.23270975783749193", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.25.9", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "cc2e53ebf0a0340777fff5ed521943e253b4d8fe", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-ZkRyVkThtxQ/J6nv3JFYv1RYY+JT5BvU0y3k5bWrmuG4woXypRa4PXmm9RhOwodRkYFWqC0C0cqcJ4OqR7kW+g==", "signatures": [{"sig": "MEUCIQDw7IFlriCL+OJe8y0JXeMNGXD6FQEt/K0RQJOQBJ5fggIgDtRilrwzypv+v9SxkGucLQ3SFIN2cbZ9LTJs54g9E24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11901}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.25.9_1729610493799_0.28406780516476693", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "4e7fc9ed2d24f3c749bfeb82ecc16661c2d04894", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-CwxY/Gtp2CV9rX9dX5nQmgW61zvX+4Jv7qOuLVll2O+u6Y8PI740upjpl4LOfZdGSmeX0q4l/5cJDXXbOpfEiQ==", "signatures": [{"sig": "MEQCIA08KQaenq0CZApAfQoe9etUAmoTdBAtDBNKh2i8kwe5AiBaZTrcqwnHbGwhgvu9TEaAW/+YjrA6Gw2R9oo5nHk9SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.13_1729864474666_0.6850696583350917", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "f2e002909f699628071178d9331c4c35aebdad7d", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-5DDs6WZ5p8J08Hy+FtbP4P+AvYmyoMTmaUiR1kCWjBNMUJm6XeMvpsQx31b5NfD1nYEGD6TowLw7Y9YdHVOd2A==", "signatures": [{"sig": "MEUCIQCjm8GJ1UuwQ/93LDsjJouCIju5U/U8gnK44J0/AZ9tZwIgYFXb7ITT9R8Tj967zT3MpxNHL1PKrdvEEbzBhY30AX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.14_1733504064664_0.6290611927998098", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "4c38573a8c788cbf7e3bba695430742f53a468e2", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-i3yNQM2oWOgJjaglddJolppKeQoSFSlPLoDJ0gJJALg3TrwUxxdayJSil5iBg3uY/PTRm3p5nn4TzOAO63rRBQ==", "signatures": [{"sig": "MEYCIQCEHthbS9BzO8Y3W57Dmjw8na3RlQ6wNnugDjXWuOqxXwIhAN1nwmtQHe5PGXeV62Ho7X2+2cX3xGXAByzW8b9ayyrw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.15_1736529892523_0.9811204757503142", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "9b6557881caff9623ef3fdb8018ca2ebe2599d9c", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-y9JZLn6blxZEPYpCgPfVPbOG0lG9xUFmuzQJod4TTSfg+NRrRfFbxOrOQQ0dUdto40ipQdZTiXEi73MtLTsUKw==", "signatures": [{"sig": "MEQCIH2yqO7FDnr7LFUfFWz+WuSB06wlsSHaqmpluoTbFsNWAiBzETvf+LiMf8Q3hVboGtmr0hOSbsB4tQgvtk0OWNjUOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.16_1739534368517_0.0011644059868618495", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "affbf1cff87d82f9ab3136e12316fcecf6ba99d5", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-GUaM9/X16FaY+vP9ritCjUYc3Pq1QuTxRMJ6bb0+FhUGdKVi0mA4pqYO4Pl4Ke+Z/jkRn6cjohfK4LgN9c1GDA==", "signatures": [{"sig": "MEUCIEThBMdRwHxRCpvYd9M0kcrRKplk6a2868mXSWNmIExzAiEAodfUqhfOdRWt/1kEP+nuKffdwlHaY6mYfV+evgG7OVA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-alpha.17_1741717521644_0.8126730888790239", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "7.27.1", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "61dd8a8e61f7eb568268d1b5f129da3eee364bf9", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==", "signatures": [{"sig": "MEUCIDNkKCVDCUok0MTz8hXO+EPOShe1kGTAso+T2f7Xh6lKAiEA0GJnYz15oOmFuHmbclpx5KNe9pS4fZGmhg305Us4aiU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11901}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_7.27.1_1746025757453_0.7148277923099677", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "bugfix"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "dist": {"shasum": "227060a505c1e53958e5ccc64e6d5397ab3ce03c", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-Hq/rOVFjzT+SxUcNn18AWM+jWZWh9jSRQXpkPUSDOJ2wWzIgLxogT5BgSOGXaJ4SQEYqz8hBlKe3Kl4nYp15rg==", "signatures": [{"sig": "MEYCIQCVknPwJwwh2uzlLeD0ktv6/hAMkaexh/sgxseqRSZd3gIhAP5mnRK8ETA6FS2eji+VYMeNiV1PaNJ5BI7BU10mSsz9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12191}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-beta.0_1748620293992_0.409645264753026", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-bugfix-firefox-class-in-computed-class-key", "version": "8.0.0-beta.1", "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin", "bugfix"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/plugin-bugfix-firefox-class-in-computed-class-key@8.0.0-beta.1", "dist": {"shasum": "59fbadc52a9931ea89d3aef6aed878758b8653bc", "integrity": "sha512-87D7C4TUGWUbk+dDXrJjosZC8LrvsIEZCgMFc/ZmwbKAf3VlHEXGcIlgd2nceX3YSgQAAodMiOc2l5gGv1nOkg==", "tarball": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 12191, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDTE3b8VQZ6Q7iBmdEGHpe7SXB4uR2dZ1LD3O2zaJVy/gIhANdoP6ZK2GkawSFkft45rEi9MemIXb5buPMkuFuHIcYW"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-bugfix-firefox-class-in-computed-class-key_8.0.0-beta.1_1751447077534_0.04342711422859269"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-04-03T16:53:46.477Z", "modified": "2025-07-02T09:04:37.968Z", "7.24.4": "2024-04-03T16:53:46.743Z", "8.0.0-alpha.8": "2024-04-04T13:19:34.479Z", "7.24.5": "2024-04-29T18:34:15.841Z", "7.24.6": "2024-05-24T12:24:15.809Z", "8.0.0-alpha.9": "2024-06-03T14:04:44.172Z", "8.0.0-alpha.10": "2024-06-04T11:20:22.146Z", "7.24.7": "2024-06-05T13:15:35.633Z", "8.0.0-alpha.11": "2024-06-07T09:15:46.300Z", "7.25.0": "2024-07-26T16:59:29.282Z", "8.0.0-alpha.12": "2024-07-26T17:33:50.891Z", "7.25.3": "2024-07-31T12:25:46.224Z", "7.25.7": "2024-10-02T15:15:18.690Z", "7.25.9": "2024-10-22T15:21:33.995Z", "8.0.0-alpha.13": "2024-10-25T13:54:34.843Z", "8.0.0-alpha.14": "2024-12-06T16:54:24.818Z", "8.0.0-alpha.15": "2025-01-10T17:24:52.749Z", "8.0.0-alpha.16": "2025-02-14T11:59:28.724Z", "8.0.0-alpha.17": "2025-03-11T18:25:21.852Z", "7.27.1": "2025-04-30T15:09:17.669Z", "8.0.0-beta.0": "2025-05-30T15:51:34.165Z", "8.0.0-beta.1": "2025-07-02T09:04:37.744Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-firefox-class-in-computed-class-key", "keywords": ["babel-plugin", "bugfix"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"}, "description": "Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}