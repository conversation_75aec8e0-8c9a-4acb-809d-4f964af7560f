{"_id": "babel-plugin-polyfill-regenerator", "_rev": "39-c613647e2437b5fd5c811504042ec191", "name": "babel-plugin-polyfill-regenerator", "dist-tags": {"latest": "0.6.5"}, "versions": {"0.0.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.0", "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a2292283cbe9ffa372f7c0f57ad110ce290520d5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-Qk4jFPO8/Pa5ytMit3cx5NrhW/8SuEUYn/ZlMEqbef16A1X0abDmY/nWWLov7YcwYVoLLbcWTtPLL8Z8QYFv1g==", "signatures": [{"sig": "MEUCIB9KjseWVruM9glKXWgkOhJZGoTEzV3QIxHX2nE7umNeAiEAuYeI0e+NgB62ZWyL6Kc4HZUUFvExqFEdux1zg7hA7TE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejd90CRA9TVsSAnZWagAA2PwQAKDbJhUiBfmkyLl1L6x5\ncpfRaPdFkfmyfCRl2SZ4YXZXWiXuDtcZluQoKBeCXyBMv0lJmNulWB0p0HnH\nat4EhNYiGEiZCC+k2yGMYoAMIAEm+AOJUUnUO+V+I7U4hvdpi2XkHPzrJT2N\nklx5IWqAM+JZANcNZlYWchMQovwMrHzrBSiDd/YIC6WMK6+GFByys0Hj3bjP\n4w2aCX6iG6gvk7481XZ5g5BbgICeSYqRhF/pYfjNIO9cgq4zF7fWUYSsR2Rc\ns9vo+321mfzK4BicQ19zAeeyWzWBSBoqqfhNewJGKa9OPEkH6zjt9rruj10F\n62/Xtc7z6b61RVAKpz42GVb/xWzTqxIk2lXTI+JJUaaRLyTV9THj00eqQuDY\ng6/l7Y0RKFf5Og9ZR46HYt+2eqchuPAFIYTBAJWt0wAJGsNe05Ptk0Dh4Nuz\nHiSqTDEShqOoFkhDSoSb+Um90DBcolH7wAAEWbnKqMjs95sDa8/6RqwktSAb\ndod42oXkRR6P+hU4fVAwS/ZLo/MJihPd//SfWLEg/X8ywWX2qhjQcyr8gjZZ\ndkQgKlAWyw4tKWSDk3HzrFX9s3nkOdJy5tZxTjaK4MR33s4MtrsWaXqQ6Urs\nFInn/V7KPfZs7rKOi2FwknyJCjBVWsgVfFjZjfhFUHjniAjxtubAbPsuiEs2\nk7ID\r\n=821U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "_npmVersion": "6.13.4", "description": "", "directories": {}, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.0_1586356083613_0.6917913073464479", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "fb091b6d03e13b72ea2c67419fbfd8d378efb242", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.1.tgz", "fileCount": 4, "integrity": "sha512-fwRk+rClv7+KSSN6em6QbJjNIrj+3aYngeiZilHdNO3c8dil3+taXtwrg1+9U1t47CQpAJsCdoKNhyhdIxKVpQ==", "signatures": [{"sig": "MEQCIAh3SeqQp18d7cLFlZVYv8FgesssmCm+yGe/0b5+BXORAiARDz6cEKAGrNq05oqZVmTQRLJ0tQ7xvO88M0QkWA0mDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezX8xCRA9TVsSAnZWagAAExwP/jTck1X1MWWtS7GUFdak\nYgrWTOafvMv6pzsuODLM8HK0DB9fh9vrZsWaeO1UMFgADZTcZ7YHAUigDxrJ\naaf3wmjuIlVx2Q3x8BhMFYDt0To7VzmtyPZcoX1wE947HgLt6aWXAjqWen/5\nwy17wjJ8TdTikMivbNjpW34NE3alUqM/rl/O0OYylDkFKPRql7W3Ha9XBhHH\nmXbcCm9vqsDxEAH9sbXeIcXKNLbAJcT1M2U67k1s399zCqag5lFpbVwr8Mpq\nK1dEWJujxNdm+pNDp2vwKv1GVa2JjDMgZM2/FYCpHq3U/D4VPexoP1tGnFJk\nEBeOZhno6V05VJ43R7gJxC1NLjx3GlvvPTepMTPw26UUnt5Ft2xtfDnuYqDl\n65Eco9iuKL/M9FEsTCM31uS5y9cjOT2RoFbC9xcuSJWH99ameC3b0QLXwiCK\ngHtURxq2p3BhmJAfRmqGUrtaBed1KMIMnvNOo+CWVi/3kYRs+XLLIQMtYuSP\ncPluFNMHSk5ePcL8GumI2uaK4C7/up2lukJabnwMElPhCmIPY/xZ9Z1Wow3+\naqxc4/w8YvhaESHDR82okIA/QYAETaUpGPUuNFNfn5KqEgRRAz4KXclIh10d\niH6MS0PP78HUNCBSvQZ9Uhz3MH+mTY8wL6LqRSMlwFr8lPfpfcgoG6nFOTvB\n0+V6\r\n=7CUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "82bf8ef0c6f375d290434a1c083983f0b8c6dffc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-plugin-test-runner": "^7.1.0", "@babel/helper-define-polyfill-provider": "^0.0.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.1_1590525744613_0.884240857368042", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2f8ca3faabcc80c7a31c0529eca381e41ef8061a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.2.tgz", "fileCount": 4, "integrity": "sha512-X4GcSBxXI/ZD6G08P0KPv/iaTldIT45skt47KcOi4PD7aArNvirPYnBcFcYSvjM9llKacpvHc/A/ZtiaCabQ1w==", "signatures": [{"sig": "MEUCIQC+IqTnVESWbo4ro84gSSvFHFro9qWKsk+5K2orau9P3AIgbtqd5loSE+t8b1IWrh7UWAQ7k0A181rqHBcjhaK6G0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezYOCCRA9TVsSAnZWagAAakIP+gIm8GfD07Kir/N3cFx4\nH/w9sVhSNapRZ396lgbN854BBq2rtmKzch67HX10m5KIGpA2dxTnBOdczRab\n0WM1fiMa05u8X80KNK7B0QPlD4xXDaMEPlfoyEhpBEkJC/GOPemg0ACLlhQg\nTIegMB4WuNblu1oALuI7cpLD1SJQDaWXZ+synp9hudj0ku8XFR09mr/1v8Tk\nNsCTC3gXKrkUzaqyzGraQPs1IXxcpEgU8c297rgDR69A0iWtKUY7iYeqHb3a\nJGegQE/2UXp4hl+yChad4h0Q3pJdSJ1MXWwrS7RLTjNxSJYXU6SlF1s0qfF5\nxKZkTkmqBbiGL71s+q5jilTumAnpuUI+4jr5xw+LMymrA2tWh3O8O818mdhV\noQkGMMz7nkP7TU/P4bh377V6e3IFwaUBxNNFp5UwSVqPZopJxBrY7mh1ryL2\nNDuhcK4I5WhrgN5BFoFNWC+yO5z4eMT0K6qdyDrzy8H+0I/Kpcy1oZE2cONz\neVZIe7azMSWQ7Xf5axhah+CnLKi9edg7Dbpfe7V5bO9V1Udw5Hffc98m42PR\nhERdshYNYn8+J9k9Ov2r/6JdkACMa8jnM2rf/2gPsdMkqzCh1609UMpsjXgu\nCtr5PYgPweOKFQBUXC7NIK1gkf95qRtz85pDCF0X4wb60acT4IV5800gmcqj\n4shv\r\n=36R0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f014169a707ba3f7bae1b38ccdc834f0b904b4b1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-plugin-test-runner": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-define-polyfill-provider": "^0.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.2_1590526849906_0.7602377120978365", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "97fd58eba2c5b7558ed7cfdb4802f982f4b50cf6", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.3.tgz", "fileCount": 4, "integrity": "sha512-Twf9jTKNE5UyPKDKcF50hjy8tFaUXMyrr95zSBeqKQ4gjTvAjczlfQMKIY4PCCR5SMvy3+fnJu9SvjP3cylYMg==", "signatures": [{"sig": "MEUCIA+OorrP9VdiYqR9ogDCxwFK0p8K4Zpdc43aEu7Vw3z5AiEAzv8bhVMFclC67aqBwGJY26Jqvvvqachz17R2hNvcIqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCN9ICRA9TVsSAnZWagAANu0P/007thxFsQHFCsvfasm9\nb3ptWpUTzjjVIxnCiwIQVLDyG1Ke53hQiI7NsRj3Cs2GxvJtjgnfvMJVvPGp\nturb22UaLdw6B6W8H00VrOjHKCSgdCYYY4evGvbSQ0jTygosINJuINHqdNxv\npM1Pps0SnkRYsCCSIfftdgJvPz42L+sR3HQfN3wntXz9UZ2UvcT7QKZFjiMC\nL/7ltF5FKPKOvEkz8xLBTCmRPfCZJegciaoVXaBryECCaTY+qsWnEfAeQLOx\nxJ9LMNOOoGgJ2hRvJkD0TrwN59w2tnmQ56wtyuYLF7z3VVEMs74vNwsobKVh\nOJBn2EtcQLyQf7rDXu+*****************************************\nf/+oIud3budqaNZWfh5MUSRUxhF10uMjCMH0ou62pOuFOMlooDEZkz0GwhZX\nZLV4Tm6qlSRDs1z6h4nU/6+RkxPcHVVjFOTZtw5uGKqTtHlA8DesnO0Ma+hy\nQin3HNabJidSkr5inO6fD9bOR0ddZfbSee3KmAmcV5WA487LmTyfvSvdReZf\nYDECjlbD57IjTrGZj3db2ok17raSBaozHkhkPMn0pOjcmY6g7NqlPEc0SJCf\n7gFNS6YHgyvjY5DCuG1ww9tfgbYxOHzLFeC6N4hdPs/mTyPef/2LXh/LEGus\nCZVf\r\n=7mzy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "884798ac0f8dec9d8a4b6fc18967acdaf6596836", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/helper-plugin-test-runner": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.3_1594416967836_0.7639225180952254", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "588641af9a2cb4e299b1400c47672a4a104d2459", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.4.tgz", "fileCount": 4, "integrity": "sha512-+/uCzO9JTYVZVGCpZpVAQkgPGt2zkR0VYiZvJ4aVoCe4ccgpKvNQqcjzAgQzSsjK64Jhc5hvrCR3l0087BevkA==", "signatures": [{"sig": "MEQCIG8QYnIPMAn5Xdzw6ZQCkjoYQ8YKGOzqu0GVE5AHHkBDAiA2+7CKLtmNQZBOvliWtM1n4r+NwhcCGo36evVu0moFag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTYDiCRA9TVsSAnZWagAAnvoQAKK1B9LLkG4kTWlVAPD3\nnWnTQRYFPZYBHfc3QakmjL+iVWzNxVJlO1ZV6H3MyZ4+tmB0IutJgd4deM9r\n2SDX0+hQlAv35NZDiVzk2XlrF2PB9fJGqLEu90GYucjMQ7bboEvL2J16XM/3\nLl0g4l2KDWYLQKQty1khYpA5jH6DjYVVuX23DpPJyv54PKA6NHVcar22dYYk\ngWwagH4AvJYulVp3CsvKACr8mIqK83jCGleJmUZg+oiegW0SFz07pK8OO207\n/23hEfuqtTJzULTXc6AAkh/+Iw7egVRbV0CdLr6WiNra0f4NVnID0oUcBqh6\n+oxT7oB3KX8O2Mp0PcFQyy2rVldzes+B9QppFrSrIkc8Pk6jpgiX6LY5cLkS\nIeBc5F5KAtM5+9Dtj+nL7JoSrlijXesnoRXd0/GJNDOSeqShXlNCSUZ5Iica\nJU6r1KGIMFhoKvLXzinIDnIhlmNq6AJgGudZYjx7cBqXNYzqloGiXIR1cpVJ\no7ey1MRKDD4fzT07r8TYvN/7GH3PcuDLCGHHTMjdEmg/s5L3V5GQFp3iU4qE\nzMVl4oK58IP08+gn/psSHEIUgwLYbnkRFafoGZSh39rlPu+iCUmEDBi+Q/OV\nAUTDIepw5xm7Mf2hgSKZAZTdxX8rYQc62RXsG7vLn/1F3M/cMDcN2h8px+rA\nCLNc\r\n=Sial\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "84d6f5b367b9044dcf25d8539a638531effb5add", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.4_1598914783524_0.08914951172378371", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "0df26fbfdee599be892ed39f1b968229e48717a0", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.5.tgz", "fileCount": 4, "integrity": "sha512-qRFeGzjXZvzIx4JUflDvfK/iYIG5Mjd/JX4/mIGm/jrdSESBLh9A/B3HjKkCvLYAIqffxfIn9K05BkYiYM8zpw==", "signatures": [{"sig": "MEYCIQDR+emc53UY/tk19g3zfTn4k8yZciOdtZgn1EsOAUX+FgIhAN4647lltWeNMvC+ypQ0lfNPgQMbIv/7+GizS1iyCqnA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeFhrCRA9TVsSAnZWagAAq4kP/0frbZp4yOA37G9HGDMw\nTyWMImUg8BJoFox10cWjKgZxkiggex4bO6cooJly4uQN2IQA8xmCaRW/Jc/O\nBZCtcACHDLn5mewPGeroVNRUn0jfTjQST7zUqXK2EkU0AvkO92KKA6QXO/hM\n95p3+adW+sfUuctbduPhPm+E6AHuOTo6lZ8hV0CXWNx2YH6E9Mh6qmGUz8vg\n7wNmGvarBI2UDcL//yyhES232XSZtv1hAp7BMRQT2HD5oicIDiHpkKXx8fXV\n+MkgJLc520rU0M1yu/7zolWBfGbg0txUnlQ4PEDr2AUNg5Yo5fjb/W7c6YiQ\nUR2AYowrPMbx4L0jz0EW6km2hOnSSYoLFPJ0qRylj1Kiun3IE6j/71Bj7RHJ\nnEIDGIdcl4c+2YMv8Cdrq4wsgsDXhIQk/EF7C87ueFuLCNXPaLS8k6gRVWrV\nhitoiaqveYDEX1ZV6DqdElrkX7vyxMtGnyBIdwOnsidSbKhexwELO3CoFiIh\n3iLVUAnOeH0yA/d/uGocExXnazdf6t4SCTzZJ81rPKZMNtAG4DoqDi2nCNRk\nDfwtSGU4bapYidcRusZgBTdDsAE8Ba68YSHZdht4CnZi4mi0zR6wVzXhnQCm\nujPVe6bl8m/eG5qdL6Oj/rKBVeHiacCw5ZfQ090yNcsIwNb0qSaWv29GdQhl\nto7a\r\n=4Y2Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a3e08229b65e7224eee3d822322576ec922a79f9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.10.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.5_1601722475108_0.8486800707178703", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d94a0205b60435c507d0477adeb172f3baf206a5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.6.tgz", "fileCount": 4, "integrity": "sha512-ySA1057vVX+rD7/hkm31XLmemQ1ayo+BQMR8zJJmDxyal6yM6HXCc60TQwzmVzrN/nP+r2sKxjFIVOYKdK7R+g==", "signatures": [{"sig": "MEQCIESQtFVWUJlufL/C1ASWAnHXi9nnfp7Y9cp8HIbuH3dhAiBaDRjFMvYIZ45C4e7OGLffh52Np4Zah7NtUay9A19Vdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqVQpCRA9TVsSAnZWagAA7HEP/2hzzy0EwRswJoIFpxWv\nHdtKXPknqk4ifRt3uPJ8prWokV5u94ca4bQr6+h79iBdJZalQCbgYeNQKNNL\nRbyIqw/fnr/lgHAKqnUGzxGl4TooPuLuXxijD9i10nv9l/tcL5gZiSpS1a0s\nLc3GXekLW1Th2fAGl/yHtJfcKn0SNm/g+7N5yKqSnJjEvKhdCMVvo9avfBTr\nMO+y5hqO+4RIvnbRcZ+9gG0qa2orR5ZeUfTO7sTpSH8vXs1UmTWb7wYSXEeJ\n+R+6DR+BzygG/xEG12uIhmFDeV5cRMnJIqKdViNPNKZwYrfH74MmY6GJ3Zkh\nt8QSvZgNyWUF7A2309UpCkjlJxAH+lst7o4R61sJ5H95YB6j8RNSk79p16n7\n0Y9WQlc8fJXd0d4czCj7jMGbYgHvjBHBE848Q5bHCJ4OWmEP++gAwPiBWci4\neaYGMt1efx4w1KYuG2UT1fKq3ICRd1FRQvoUazgjgEGiofKXuAy/no4nyWTg\nHZlNR4BfJubTVqbbDVZJWRS4kHaeGF3w1ni2bOUcG3ISfbejMDtN/LVUtq/M\nz6/zmaTaVAaFRhgfKwDRTS7nFd05ogqpw/1CTZU20WMYpENbNPJl8G247PyS\nEejcZhKZvzP99MZvakc/9aFiDS7uAO6R7MIDerWEwn/miHv+DK7y+h/BujZF\nzWDQ\r\n=8SYv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ab1ebbd308b1111920327d95dc4c66a5ae4518f5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.1.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.6_1604932649362_0.11488768347083655", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.7", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "5d58d750e27b802180cffaa8e78cf26b4567f415", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.7.tgz", "fileCount": 4, "integrity": "sha512-suMLvdiYcccqquticahaThrNT97OyUpP9D1G7OseQnrYbst1z7bmpMjyrCf/ZL7SK5KdMdc+VgpFUQi98mCOtQ==", "signatures": [{"sig": "MEUCIQDYNT4Yy/w1q9jLQs06D7yLA6rPqK4ajpx6fmBjouu0YgIgAeZ/xB+42xDsrrm2/rulWmXTLc6gzn7aED9EWKDrjBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UOfCRA9TVsSAnZWagAAQuEQAJwAtxp3s6cQ95vFPJZd\nPajN3iwBwRihyEiA7xmZPAUdiHUvlloTN63Xm1aRG8Pwe7i8ucpbiP2LWeE1\nwrruJIksD/s1Niuck6ruh3Cbv717ZUJPSoF+xfLQGOgIayzqXIsQH0D1Ks9k\nG8FGuBrOHxQ0sT9uKbkNXCG/Q/9jGsgwxnFY9NWq3NU7XyuzpGN6rDR1K4oA\nuqGiOiMhJBsYRdFeO2JQTb84zdGZZUP7R2Zjhxph+1yfAkPZPa7g4pqcstFI\n69tm3Av8n+UngtMdUdtShqWAiakuhiaGm2wW6lm9CoqEoHzauV6r/OFhyzau\npagE3l/kEGQqCPsqej59o9AR1ahLbgTl2Ot//paaMsknI8DA+RKo9pGO8GS1\nEIt1fVTVM9ndKvBlSndvY7l0yKl+WxMJ1FwfvURI1I8iEvVOs5ij15KCeQ/K\n4FS27rYxfQQyIvwKT+rVp5PbqWpmjYVzS37K+obKl/0EM+L5qGQxNAnzzNjN\nC0W85UjXWND58whwc1/Ioa8y4pVUbM1keaHk3ZwP0DtKm1STfAbGjGcQV4u/\nmyw8OaIeHjY7pZ26yX83zBTcWiIU7QgKztFTRvaBhTn76PaCIjJjsik2XUt+\nOUqJobquBVKa85AR7kG4hWVZsArXTYNiDuymWTU4+03G10bgsvpN11iY5X5O\nJj2t\r\n=uC4m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "04e366d5948db64ebe1eae82c3df42bb062c5071", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.7_1608598431484_0.8909165228916345", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.8", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "07188d2509f79b9389152751be59434e0004b3cd", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.8.tgz", "fileCount": 4, "integrity": "sha512-DxHM6JvfIFFfm3eL+xarfpHqIztEHVw1MtqVfZlARtf7q6v46kFXcO/A+BmJKBoxanK6xM/kCI7ZYg9b6uVrSg==", "signatures": [{"sig": "MEUCIQCVURvd6zLeWrt0aNvW2C/b4DjYX600H2BXO7CS2Ad78gIgbr9g52wPXMSto9OUL+YLBmyUqMqtpFFiSc4ZfTagcvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UWJCRA9TVsSAnZWagAAxHwQAIOJrJp0W6g1Ka50nQMM\nMlYRDTI+gyrVD0ZX9FZCeDC83PGjnwyEVmgGXoKUj3lwak2WaN2AdQw1+Vrk\nlqrNL3y7xydsIJj3YJkEepuWS89FK0luVgx6qtHgnuMkGVuKYm7qbBJrIGw0\nwPpcow9WXIyvVCfElTBUaRNCoYUl9XQTf3rcI+vvH0z65XEiDmqqiElpsu6R\neRAMm6TN7CkDsbKZzT4wWYz9sIuTSWdkdmPj1zNx0+8rLhkS/odGEDXtckEs\n3Xw+m88+sjiT6qPqcWsCfHkTarlMi4S7I9uUlRgbIIPMdiOwa3X6YxGrcop9\nUFneN7sR7Vu2p4SJlrlbwOFaIoCZlZVMxLJdeF9BHbXJ++oEmsSJ3boirFB8\ny1jdRm1iF/4RSGUdCPFPFAy3nxMnLvDDopJUeytexP6Rm4qh7hvdLE16BkuB\n1ouhyv4BsguVpuVtwDxxkKB8gvq4gv5F9UGXlwqu+EATbcS+mQG0BFzqD5xb\nVRV9+Eedeebhl6OpHZViEF9TgyGIAIJLxkTB3sUal1XP0fYLi08D6qCqrhEy\nGNnG1TVQmqar+EGZ7dPy+3GBarebT98o/wDsAB+XK+CeUXgpHCFzpgjNkAG8\n8zXnB1pW9U9PqOY86429MNwP71UaGf/jXFSRf2wdES+OVhe9h2qmFtXvPcl7\n3Hc+\r\n=XaH+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "762a528b570a87fad72ebc24e7565b354b4bfbba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.8_1608598921427_0.6901730301370963", "host": "s3://npm-registry-packages"}}, "0.0.9": {"name": "babel-plugin-polyfill-regenerator", "version": "0.0.9", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.0.9", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "5c03ca2d0de247adc5748de49e4a250e04021f89", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.0.9.tgz", "fileCount": 4, "integrity": "sha512-zveuWoDcb0vY7zSpkDIeQr/oYRGlnoOPd9bjDdU5P+IhLnbhZps7eKGIvXujD0XzfYohTJdEGn3Fk7tt4lWaDw==", "signatures": [{"sig": "MEUCIQDxcSRH4nkv0oIG+1X0uJ/xy3cxmbHrZAwCqEIx2gmcdQIgO5Zhz5Rgr0X7BMLOV7AZyF6pRJSKGItqmWGBjdlXOwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9yNfCRA9TVsSAnZWagAAdxEP/iLSUBHKb/fbS58zX+6Z\nVsbNdee10jbU55xfFohTF/4qdRPBD696Kfnk7o2xMBI3TqTIB7T9EbM8ryT6\nYxFZXGZGbVK5OLZyS4dMLHIjISRbyHU91u3rh3YaCAUe7yTep0/cpQkI274w\nn2Hc8qy4Wey+nHqsvH2UgMxGmRAycePg6MmegpecsaXTWN4tnHNJ9USEn9+1\nWYtoq3rEmWRKmV09WZatPwM/r4QyAubVju5pR2Lwxy8CscBWO+ddN5dZBS62\nRP/QAO/Y65Dlq4CXuB22VclFvdU1EyZ2NBsTN4Opft4XksTVEDY8idilkxGX\nCHRznUYfV/piZR85rVNYomUxEOGLPwBUVyVYBS0pNhECpgPW7yl14pF6Zp2y\nzwubsKMGr1HTytp6990LV138b6PSPaHdRHYb79Jncbvu7d0A3EwtCMRVhxPM\n3iBqoR31ryo1dWnny2PZHpUNptkR6mQjgu8xG6l++JZKcnHYAPNaOUA8o/li\nKfo0xoFCTtWtYzRYi8++J/rbGBTBlPcfSCC4IzkU+r7QC9B8JvNnkBEKYIE5\nMzAIzaa/8NFBH1FDo5klzIOZ8ueOwdmaYsk8YwIm8y6o/lqmN0FaQ+lBushL\n7hFghXkKoNmkCzpXLSHpRkbt3uv8h40nln3Q6EHPV+8cWvT3U2vOqIKp2GSX\nVyCT\r\n=6gaP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "bb9300ed54b6cc33e09a89764efbb2bd2949ccdf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.0.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.0.9_1610031967145_0.8414286399344075", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ffe0bdface98ccbfe6063aff2548375927153f85", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-7MOUln2wqE+BwQlaCQeFP4R9DGWOIJDEx2TeUG8lxBs84uBR2ivNh8m5421h4rFO68/3WdXyr9HTZIUJXqXb8Q==", "signatures": [{"sig": "MEQCIGTC3XkSYeq66Yrn7AlwdUL6hrnT7pQmCfWrZjoIyHPYAiBhbpeunAtFUIMZ4Q/gp3gP43+3wFaDpYaRd2yDB78Tvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+bkaCRA9TVsSAnZWagAAJ24P/Rm7Vv5AZJ98LbiWGGng\nzVaWKEcuCJsovcwUztCC6lRjrtoVVf0qygBT+unhTJ3+R9G8dw6nH0UsHPkr\n4+OrLv2yWKIjH/NO/+FqzVneNre0TguO3mpXFxmyWgyykfEHcvxFa7I4v2O7\nhetjhalmpKUPWsOjh4r0psdiu4O5E1sF/qGaeoRV481FwnBO8c+VXo+PBzh5\nr5OMcuTfmj3R9y/3plaNR7xAZyuW9J31rULkopQEpENrpj5iRDi2KHbr4fjx\nKdyfd9y6ufDXjQnkFa63VC90pifEIcC5fojb0qUqj3ja6paoxYRhP+G6YsaZ\n4eP1Id+ugyeJo/lvhXv5cuX73TmgtXypy9bc+hb1Rqqe7U9KKb8MlaHkmtvb\nC3F7ZTASUbt5fkNjUf6scRRMnQ+AMmAKOpmYwcdfsvji2lnbR7M/NumPQ/tE\nB6gjJdUOgGZ059j58rExrGvniueHVw9upEwXDLxTqkKS42S2FGcaTxXLvo5z\nynxO+/Ix4402aX2EPeujuFd6g+xi2DsJAZlAcGHqW3muPu/Dw2P/7UuzBF07\nL3uJMURPylEK2kMv6zFSZvg5oeHCK75wP9i3BWuVggVAfZE7jtbXgRGCixHa\nCS7M1NSBRJksCTP1FaIe3jSZ1AC/nusnerSc8LZupFku8DSL9ogI5kyLI0sw\n3ddm\r\n=K0VV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "440228dfbaff5d0632a02f295002579fe862e614", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.0_1610201369940_0.29590456664639575", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3b9b907d1554747ff131bd5c6da81713e342db73", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-QZlnPDLX2JEXP8RQGeP4owNxRKUAqHD+rdlyRHV8ENeCcI9ni2qT9IzxT9jYW1aZrxCgehD31LztlMaA68zoqQ==", "signatures": [{"sig": "MEYCIQCitF8BGwBiPOR8sji9MgbS19H+en0gHs/RVFS0o0mScwIhAPgtvCQ1upLT1DinWgU+16PtecAuI6Vd6QzflKA3NMi6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMwFqCRA9TVsSAnZWagAAtp4P/05R1qoBSeqCRNFqHmB4\nODC9E4Cal0l5R/eJHnCZ/1jZ8M9YJ+KfbYYfYj9XGZ4Ve/lcIBaoEBWng66n\nYIUpXQ/LtXsrk5nA/UTgq+j+0JZeytYQY0f5zuvEAMEe57uFJVHqnN6Ia3m+\n+EKgechOUkjaFhcSleIOjeE77m/iN1kaPkdWmAKs8rvRybtB0TY22/RFZfok\niyW+mo1sFToeMOvzCFByKtDTwxl8PoTxArN78XtYw4yARV+vXxP49UytkRZf\ns2xDw3M+zwRQypefy3eRoPvCUGI/8LiBhkpNisnl6PVePzB46l5xvfKoQ3K/\nSol7UFoo3INMtCbiXN/a97pNmotrWTFOFJXW5IaPgNb7sc41WDmVD2H0UYdA\n7LZAEXro0yLIIron2HXzkIgwXbRAUlwMOBxozBkrWF4E5MfduUgYiJ4s/dHw\nSRjeBRlP4BepWs/PazwvTnI4nJL8cTZYaqH6O+HZ/SFrH5oak7sKGFr+iDaN\n0UBmb1ODqw3daRSZ4KwG8tx+/+WOrmbfsrWd3BsZ694bD30R5CpIRDihD/cj\n1dTVfulvW6nOupMglv2ZdwTSpjN2Hmg1VzXIbMdFlfvUk9zVx2hH48sl0w+i\n60HJEKAigLwIPK+gUq3mReZyK5EdcXFDZZTTwTisQi6MyHnAncW2NmD9Y045\nGRMs\r\n=El0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "57edac90ba049d5ac2386cfaa0be131a68030d21", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.1_1613955433480_0.641425505611581", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e826219024d2c8735a798b5f1b253305ebf1b2ec", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.2.tgz", "fileCount": 4, "integrity": "sha512-BkzNc6Fu1kiFIykZnDnM5bROMqr18xWUhOktVN1q/QiEOxo+xjB3tumi+Pebm1t4FCkDMEkcaqNR44dPA4CO0g==", "signatures": [{"sig": "MEUCIQDlyNwfsJ60shjpbdFZrzKgaS8RzMgyv9W+27Kjqaxk7wIgBO2Oe7Id1Lg6yv2UYVkjcEPDOg9SGIFijmAmQ7I+ZqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNOWmCRA9TVsSAnZWagAA4AwP/jaCmQg9C8waqKN0Cluc\nQu3gypqkZx8nVFYJ/f0GBTAhS3FF8BCt71xFuMiztp+/ybwRp6BNy2h5qT7p\nfViDfwWh+2B3v8ut2DMYW0j27GpY7BEocAYnyYDAryV1S3y0MB4oB4BTfX6+\nI1Vpe9TWfcdlkirW5JN1mYG/lviNdc5HXRedFmhoABzu/Bb7MHVxuz1Zhl6v\nNsQeOH0V4PyyTXTDYsbBCdHZ1CsJ9hlHfvs4UTx4nwcLr8mFUBn0kyd0lSQr\n8ojjbf1aolws7Njl7trspmuCQs7Xgybdubkljgv7MEJ6vmpuySge1uyj2Rek\nEzyhS0mb3qtVsZApJTJeuRiq+WRTdMVvQNiA038rffNcajfdcr72ZzUjLRus\nidXzdKJvACx3Z/bBJ+rSPrcNgniubRz4kHrZv+iDLKuvlMA7dRN08Hw/DZMA\nZ6pp9BJf8e+TjkJrjTrBrQHZPxa2c1pKlQskdyP51G/KNZLquxmhbNQMa6fw\nP0zIyDTZHkO5SNfeM/2zgi4UaGFCFEuMSCzic3hgeTqaQPWDmzH7z9XUDCVq\nBD3jH4+VPhweA00+Gs8ow0Btsn29tlCBIjB9/yF9pwZiWY1UC6nDyWIV2wMw\nYN4b9g74iewIdgvpwu7boK0nS1u7lR6xknr4EOUIyuSiPOtBEnOhyAyDFnA1\nwfuD\r\n=73Np\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "187944845010adf2dc1a673af48d0d9e6d06d6b0", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.2_1614079398263_0.6275420581636975", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "350f857225fc640ae1ec78d1536afcbb457db841", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.3.tgz", "fileCount": 4, "integrity": "sha512-hRjTJQiOYt/wBKEc+8V8p9OJ9799blAJcuKzn1JXh3pApHoWl1Emxh2BHc6MC7Qt6bbr3uDpNxaYQnATLIudEg==", "signatures": [{"sig": "MEUCICfJSvWpSuYDdfib5So8p1EUtzkjtiDTdaiEJleWMAKXAiEAx0SQDU+EYWIBjO6UdYzLUzYJAn98lm3Shr4y9iAAWF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQFPCRA9TVsSAnZWagAACVoP/0JO3h4Ab0OzIPOGxj8V\nNCK2dI8VGFANwKmqrjateY+fkrmj8k1cLUPg3rGH9jS/1gxGofLeMjrwHqaw\nh1fZxNcOBwFFMieCnn/6BUcGhMIQ0z3Gjur+V/b7aIJJXvc6CWNk+FbWN/Es\nau8f7XJK/sl26HRGxCWDYILqsmayKFa5s8QlN6UqVxCEtefXQqiSvVzlf1wl\n7ySZayXlT5mq/UhqPnJreogSiXX7lFtJyArTk6mrkoFdL9sFpiZzB2bega7k\nbFaq13mhMbf2ZApPtGzfjmDPfArCwIoIWX5/X2F7RaAenKEVcPRaJv/5X55x\nLjLYGL6D1JxjKAtUjJzZa5VE/RgB7ZHNhCc60H02frpwJuYpbUBpoEFQFNGu\nmVnsa8YPlc8zVv1vbd2zTlpx4N3pGxaUs6Bk4b9SbkwY/uTJYzeke/9bwnQn\nwO9hWr98PPbDfSLOulsQlqlZGiiGVZI9+0kGf/nC+DXLa+BLZynkz5ZzN8xg\nwRfEAV8/pXYG1gFucBjJr8P4QIujZsNSYHA7Wr2M9k71SHX3ijCqvbFRSH89\n3x//PqT3rxfMj8yT+HUNYoU6gp7yLv3p+PqCKBnxeZt84YoiBM2D7oNd+l/6\nBrxxFLS1ZXyJry8AIYbREOq92hsw2izcCfSxVCoSkczFhpR9LygkDFu0h/Mt\nMUBg\r\n=DCS5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "55f582c8ebc4b8a181a51fecaa92c89158cc7ac9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.3_1614086479435_0.4831628895022768", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f42a58fd86a1c97fbe3a2752d80a4a3e017203e1", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.5.tgz", "fileCount": 4, "integrity": "sha512-EyhBA6uN94W97lR7ecQVTvH9F5tIIdEw3ZqHuU4zekMlW82k5cXNXniiB7PRxQm06BqAjVr4sDT1mOy4RcphIA==", "signatures": [{"sig": "MEUCIQClwDaeM+i+txqIREfO0I+pcfV4+Y5d3VvF5RriBBQUjAIgfjs9I/iIaju8L1YmY3QkW6yvBxKa5JlP8/jENV4gn3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODApCRA9TVsSAnZWagAAkAEP/jFqTTsQg+EcBkbJiqx7\nXFwUUkGIbqI9Zy08J2NCVPH2Dl3USlKvalsQ70bLhfpDYu96hV+a2HWOWgIH\nltGD4gw6858HHk5TGMJgXvouKo6FI8a8rbJ1+a0iKYlYplQ9fO01X7/44id0\nHk0ys5SKbsEo8nnfqJbdzn2C0pTR5ZSsHEDohWEmhYd2SyuYZOI1zyEoeGGs\nL6F9O6zPAzFDVahFIzctHS02K4jWTdu2No2Nn6HOM4t7c90OR0hNs95kIo6I\nBtalrezB9v1lVFdi8yVkW92ebrQG7ckvRLWbrEvLkUCjnKueRqCUDpLOkugO\nYd4VeVvP/PmKz12TTOzZV3R3jznfCkVqkRaDs1+RJcBKYkM4J93ECPzx6dbR\nD+/Y7T/XZDumk/KO6fAb/UlFh6OVAEjfdvsLVAXQVoFk2hgdPEeJ838f8HVg\nfqjO7YHFU2lXvV33XRaIM/1y8HT/9aJRCHumci1CT4Noj9wmY9QfKpOK1x93\nUAi+j+WSoVa8WT3SLkiKtiEKmnpdTZKScRWEBoOUyh8M6ibIhku+x9zwniFm\nEJp4QQUiryTMFd/PFmg4r2va3fH8YXYkY2Apm1iDRrzaCN9iyUNA4Jb7RrlP\nVxdYQnrMIq7yiZ7C74afDhZDmE4MgWba2gGf03PXhK/dXSPog90p18r0GBqx\ns/lp\r\n=Vw2N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6e6c7d2925b0f512397518d9f5ec35c925ea9960", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.5_1614295080490_0.8683408659576648", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "babel-plugin-polyfill-regenerator", "version": "0.1.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.1.6", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "0fe06a026fe0faa628ccc8ba3302da0a6ce02f3f", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.1.6.tgz", "fileCount": 4, "integrity": "sha512-OUrYG9iKPKz8NxswXbRAdSwF0GhRdIEMTloQATJi4bDuFqrXaXcCUT/VGNrr8pBcjMh1RxZ7Xt9cytVJTJfvMg==", "signatures": [{"sig": "MEYCIQD4aCUBHeeV7X6I3huh8W/ZXLzMnJbovtRXqsjocghYSQIhAKQS1CTtv04Mt15WDYiOdnHSO/4e6DDrQ6j9ASfN4zDt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPfmbCRA9TVsSAnZWagAA9dsP+wRO0UMXcLpEbf15USoS\nYZeRWndjeAexMngzKQ+Ztg4Qp/EDWOa2sBRhxMU9mDrq5RHzODOLOzw+kwyV\nIFwDQWVWfSFCWqAZNDNLOjbBUcDw3N3CDrghnxiHlqecrEJkVKlY5h+BYivV\nUsOlwre+Et8t5/AgbS6MH14YmifsSsf7M3ihdMzpRwAe0NcBKlF8P/FP6q+M\nt1F6+fDiHPS7dbhw5b3lxDEdaFOu3InzDJuzYEFw/boaHqK1COTLY+j9lvq9\nCEwcB2RiGMi9QQwZAT/c71jFySmof3O546nDesXr0NPikIvOz2pQC9yAUm07\nSN93frsCmug4fY/w/gOAVYw9EAzo65lOzgzqph3CA89EKTDO8RB7CDyWX/f4\nOhRMy57RY28z1gg+EbcLRcDmqm1o3y2w30bLNhsDNBi5bpidtspsnmCS/U5w\nfClg4jlYRreOwAKI8xKxDYS9bDmhs2XyPFqjc/fWgKWn8elGXrdtYU2OtYK1\nyi9yCXq38vXK2hS5Ql9YKBs53N7Oj5wWbe67pXYl7gnj7OJvNAP2ugmeYZmu\ne2AQ6xSRFEuzyGeWxWCI0mkrwl12d0Vi9r1vkNbgiuvfhf776/Rv/dc9Nzyx\nEFuW7VtVrfK8/L8wLXLm7tYuyjKo92fdohVXyMvu//B2l8o8i1QuZyZA8Fbq\nM7/R\r\n=1Wyp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cca90e89debf689aff23e982f8a30bc3b512c573", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v15.10.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.1.6_1614674331423_0.01780439689385216", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.2.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "853f5f5716f4691d98c84f8069c7636ea8da7ab8", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.0.tgz", "fileCount": 6, "integrity": "sha512-J7vKbCuD2Xi/eEHxquHN14bXAW9CXtecwuLrOIDJtcZzTaPzV1VdEfoUf9AzcRBMolKUQKM9/GVojeh0hFiqMg==", "signatures": [{"sig": "MEQCIDE6DqoEc2Km8E/AMWlugntB/sOdaZdu3wuvVzrZsUB2AiBQsKbtMQj8gAhBRiXS9ioB3Dz2UTdLIbnsQMb9Tn8v8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZJrZCRA9TVsSAnZWagAA4MQP/R9ErhCIyk69RSAg85Gg\nquadZ+0djGK29qEW4e8vvOTIfXsT6pYt4/huOQDb1Tdo5tmelAf3CHmYRJPC\n/okNLA6OVJ4Sge5HhsyCQHdGp0BRlV6FenS5JSs2Fjf8x9Yd1cfBGuOrojqB\nba5owUdBqnsPftmQI7uawG722baMWIaS++fKTjjsSPoIgEIuqWq4GCWz8ca9\nozuNegWm97fBMFiVheKieUPZp6AwoLJ19EruujEcNc3b1aD5aOkQzprCZ8NL\nGImuqcKjdVyPBtKpGQyNH+P7RQmn9gy06etHILgAbHSm4X4dxtLyKCMXq7CS\nS0OlHWtZpUSyccLDEz4oIeN5DDs/RRTjakjOn4UkadTuBThm/96XF+IzZY+Q\nW2awxzFfZ3A7nbwgA4+oltg88E585Vi7dJ5YxRmi8JtCXt21DfirNOw2jW4Y\nfeASSjLfsfaq+IxIvksltitKBKeg7Ns8InC+ztN/4GQ9WccgxMVep/qgjxYi\nvntsBDHLMkaplgt8lHKcumlDRMx9xgZuYWXruHg97CQyg6EB/RQ9A5SmfKBb\ntM/gWEe4O5w6PYsqYi20tN86OrY2rqpSbZW0/OGcXZGbgUgpHXKaLYIeFskU\nRoDUkihs+DRvs5s8w+x04bO/3aksL7cQxaWKRlQMbKlx8lPdMPRhkfNWVMAJ\nECsS\r\n=3azY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "841e713e0002eb8aa167553fc43840e526fa2d8c", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.2.0_1617205977465_0.45162956991231584", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.2.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.2.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ca9595d7d5f3afefec2d83126148b90db751a091", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.1.tgz", "fileCount": 6, "integrity": "sha512-T3bYyL3Sll2EtC94v3f+fA8M28q7YPTOZdB++SRHjvYZTvtd+WorMUq3tDTD4Q7Kjk1LG0gGromslKjcO5p2TA==", "signatures": [{"sig": "MEUCIEkoquhC1IeRqkdOBJArzaCfp/PsAyf8wiDX1I7avTOtAiEAyYsSapgCm02I1BZhPLoH59RGKZTwgoMCfYfp9Aoww8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqDI8CRA9TVsSAnZWagAA6bIP/2AMq1H61eS9z/4LKEOA\nPWwJHbiQrwZJaftFPuP6yU3Nb7H6tATRC0x/cRKrDGo+UDn55NOINRmU16ZY\nvgJQJEjNb8oYz7KBq2gFVeUiUROc9+aRUF1RQyULhkgKfCnplaKzol5DyaO9\ncSZ+gGd/tXH7biUcrfLXBRPBXo5TVJMGUYEK+iTG+c88QFcMr45Al1UdKWOX\nwkWiZVkEXdt3UTaEwBNeofLZPc73WoCKw5XFFTlTPRcYVm0qpQyT6S+cp9Z1\nFhJ52SJsSTCWlzKC6xZin5tOqchJXtPhBvp7sv0OtRtGl/9qDSsncx5vw7aM\nXzH6A5266u1ZHqlq9+i2r7PGR3Con7GTs9bfqtZGAXOD2LcIu5YashR6UUGQ\nHTo8AJNKoJQwwyw60+NcFhveMZ4KKc83eLe6jOelVEmZBi0MpNHwUsxKlsev\n/VVLYnBBvg86z6QdddFlw47S6khMonTchmHS7K1QzCl2Q1ZWRahhOKr/s6Sm\n/7Jd/xj0xjHG/+M/VhDO8H34SF5qW7D9RJhVA7LrEsrevEGq+BsyKHXPlFDU\natZHsYB9H7YCd1AOj+tDVGb3+yiyF9490rRoCOSUvvUtG3gameDM3ZCu2Yx+\nSj7o6bdCLuP4HhXyDxU1UX+xBdNRNcSU63voo3O2Nl2h1AkPRMCoktU0CHQ3\npgQ2\r\n=/foj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a82aa2032d84ece15914846fde5f1544701b0a04", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v16.1.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.2.1_1621635644034_0.2629324315437396", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "babel-plugin-polyfill-regenerator", "version": "0.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.2.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b310c8d642acada348c1fa3b3e6ce0e851bee077", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.2.tgz", "fileCount": 6, "integrity": "sha512-Goy5ghsc21HgPDFtzRkSirpZVW35meGoTmTOb2bxqdl60ghub4xOidgNTHaZfQ2FaxQsKmwvXtOAkcIS4SMBWg==", "signatures": [{"sig": "MEYCIQDPXiqtmILxyJKqRTu3PgPyqNuvgtoh+mrwXevvGZTDBwIhAJn9bD0hz2dCRVHQmpmbM0Xor1ZYkUht4ScPrCMt3oHQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrh6YCRA9TVsSAnZWagAA52IP/jynC1ciPvXCdi0gitsp\nUroNXnBfGvF6fMqsfRFzCDcO35WzzB0TFjtB+ygSccyHIi0VwUnUJfGtwVrk\n5vrZKmuNs7Y9DCvej+k+Q60pb6YnsF0ltmQkZDD97/JZHqWMU10DzbL3cTb7\n3VDieiWXCbBrrHtS5pfdsj86Hm6H1/2drrCBOWqepx1B27I8Id7u6hTwUUl9\nMmBs0T7mwQVuMoQcXRxpvwfsA7Xv2LUhgDDn7gvKVR//0jc+E37AzHoxS+zT\nzhzmG2MAacLdKmXNnukrYPGRLGbPaD4OKZ/bANPlh5qFKYunrz0TUgDQ3UtU\ndZPRVY2DFwsTJ3Y4ExHYHhVbn1rob8SKfi34nFxwrnErpmf0GTlQhY8q9OR/\n4bddCjcWl1F4CA9Al1xtxDN9lLUbm/wC3WaDJKSbMfQbmztj05WASI+2w+6R\n5D+VKd2a/zV1kdnzOBecd72qotBdOJe87YwlxCxj/siFKZnUF5j30aqEyvr5\nOEdE+T1riJg6WkOcd2SVJhx8HO8UyQMCZJ43hWUKs0iRmuY12vyqhbqaeZeD\nelSNWLQsej7BhbEy8fwj6KwkGJ7ApURY7K9vi5AP9GHtPUx5Rq+eTd685N0A\nUyyW8H/3ie/jKEC2YLiJzXUPkxRLoKcT3oubhE4P0++ecfl/nQLy8Qu5z7l6\nF/9B\r\n=wXUI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1db1e16a7e6855094c52a6cf9b98410e3f0e80de", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.2.2_1622023832007_0.09371588949576815", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "babel-plugin-polyfill-regenerator", "version": "0.2.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.2.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2e9808f5027c4336c994992b48a4262580cb8d6d", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.3.tgz", "fileCount": 6, "integrity": "sha512-JVE78oRZPKFIeUqFGrSORNzQnrDwZR16oiWeGM8ZyjBn2XAT5OjP+wXx5ESuo33nUsFUEJYjtklnsKbxW5L+7g==", "signatures": [{"sig": "MEQCIFhZphRMm7LCmd8Ou4qW+0gg6BZRmLzNiyAKpYrrDjklAiAevgzLeSMBbQzpG1ntQBzAp7uZ9oklmnWPqPQTfb2Dnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "de48880e867791eaa94c00ce7132dc259f3c777b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.2.3_1635546017721_0.88160952279895", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.3.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.3.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "9ebbcd7186e1a33e21c5e20cae4e7983949533be", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.0.tgz", "fileCount": 6, "integrity": "sha512-dhAPTDLGoMW5/84wkgwiLRwMnio2i1fUe53EuvtKMv0pn2p3S8OCoV1xAzfJPl0KOX7IB89s2ib85vbYiea3jg==", "signatures": [{"sig": "MEYCIQDCRjtSZYmuPRjZxYhnUE1YaRWU0rMC8ikjPr5W4IHOTgIhAJ7C4XxfAf2D797mzcIubdWJHyzU3tSZeKP27ISHytU+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2kRFCRA9TVsSAnZWagAAM8gQAJ9Oj4dlXv8BjzDaepbD\ny1U9HaJAB6KeTkpOldffi3fUR5zNggsq530eeGK+0m9NfD9HXG/jAWE4oXec\ncPHGHcKPWDGXlC5ozr7rl7X2I0mmNHRnibX01+CP8QEEfAdlOpB+fyLhvLSq\nhWOLilwq1/wSkjZPGLoShr7b2mP/4CuElXmf8YNUdrIgoJtZmuDnNPQAtw2f\nzeK0qIfSm2+L9iUDamJTLkx8tHb37YMjvyEEtXnenNGiWwWFmFTa/a8bHiNK\njLYMwFAlQBK780AeNZkw7WhKjnKiRPrPs5SFXLEzPtc2f+nV0Xh0SFRtPBNS\n54tBsiorixlUJVH15jNvnaqC1GBgeIxW9jlBWE4vFpYaArUHEgYBP+W3KDMv\nuC+FiQ0kYRPcKjbkH9+EmWFUynZFsFcd3x8XyC2gBoHY0L8lSKXXGnB3enhh\nUDkb6+W+6BPU+gr7cQnRMH4SBvYrOEBZZQWCvaNGPg9SHJNxnNhmk7RTTN8z\n73v+5P8sncZrUevB4ic+F/mGMbN9x/mGvggiKpwiSH5iUnsiOYZn3hPAduWe\nX72aguQSQQju3GtLZnRp7qc0gWvklxudedlDn1XabdRpjrjBXkoo1aW1exvK\n0yLXbqYs8sz+ZRw+P0po9VKE0hHkwZXe6jOx5RMIfZmnacSSfgMIyUWaYJkk\nTOt4\r\n=0NuV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "b2688bbffcae0f0c6ef5cd6ddf1abacc574060a1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.3.0_1636799701056_0.9903418558878148", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.3.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.3.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "2c0678ea47c75c8cc2fbb1852278d8fb68233990", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz", "fileCount": 6, "integrity": "sha512-Y2B06tvgHYt1x0yz17jGkGeeMr5FeKUu+ASJ+N6nB5lQ8Dapfg42i0OVrf8PNGJ3zKL4A23snMi1IRwrqqND7A==", "signatures": [{"sig": "MEUCIQDtEU7Wkl+1Jzvkqx64E8CuoCDX2WecLze24dTQqupRlwIgXtd/zqiUyTfHd/zMgyh3gkWLty/KoLLP+ogCR73ZhVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4uKOCRA9TVsSAnZWagAAyBkQAJ/XaVnyrAOVYN6qDI2w\nZCXwQod7C1fMR4V7P1GYs5Emp6opoCoSCNyR8a3HdODzclxw+0eGD4Zj1iiT\nWLz9dzI6l5Bn+abgChxgu1u33LxLI5FMQ6t76dmB4iEkfGI+0sMKOT8tPSuU\n4UmqAqj/v3gRlt7lZH0wVQaQaYprF+JRbkLGBuUVA8XkMG0D3M95k1D8SxjE\nDU6hZ4RkboDd5CyMQuFz2nZlLFb+yzWiO+YOaUiWfjnGvTuu74evSQ7sgDU/\nRfKRGRe5iOJKwody1v9vbESF6LnN7yYjEL5VWzyPlhPiJDeOKdHAm1iKNg15\nuBcXG/Y1/sNo97pnhY08SZ5LgzJOELgEwPQ+3pqfD8loyt+odnTv4/hgbyU9\ne0KjNuDzoi1+FEYgYM9B4mhy7otvmm5GzwyIL5RjYRNl67Jz7A2+4k/KmQcO\nB5A0LIg/RcwvCqSALEz258xElODscOoe6R62rC2zPfzbnQphlrq2zgWcf+pP\nceASAK2eaJNYmk4ICgwDH3Ifg5sUKIB+TR8J/VSZHZfxS822iuVlhZSgGrkh\nP79PbCUJu4XKFOolGt5GDHhSMBGIFL5xdwmdmXjB1ybve2uVap3HsWwT3JwH\nxKjIeMzJXm28B4v8M4nSiLci04rjzBByaGsNYf8/TEuWQGtA9mLvl+aSjOSy\nJkyr\r\n=7ec+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "f5eb1c6d74fa29afdb0a3a533dfc5742557197bc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v16.13.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.13.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.3.1_1642259085923_0.07572641627604715", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.4.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8f51809b6d5883e07e71548d75966ff7635527fe", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.0.tgz", "fileCount": 6, "integrity": "sha512-RW1cnryiADFeHmfLS+WW/G431p1PsW5qdRdz0SDRi7TKcUgc7Oh/uXkT7MZ/+tGsT1BkczEAmD5XjUyJ5SWDTw==", "signatures": [{"sig": "MEQCIEKDUhxWNexwGogeQN074PIml5cqlCGjA4ttuUTDNUCFAiBuerOLbzJH7sAQUikLeSMrauFEHSP7S/Z/DHwYwxiqkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3O05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4iRAAmVwdTwe9dFny+AHCEBdJf/USygSqt2mxqkk+90UP4KL7QyTC\r\nmeQyiael6GMNGR8XR3/PfsIi/knAoiLSuwSHOYNhrqZ7R47ruIJPvkz1xLs/\r\nX4a4vdwcVitwEgfOG7vJf6nBZLMulDI8j0wZMtspxAhrfEQE5LEMAC/tJzrQ\r\nwXDynYuOSn8QBLId/qZ+dc3kFF8nJJGtnHVZ/OByYNe5cckx/yX3X3OqK6Mj\r\nN4tzdNQN1MOEfdfS1BRoaEH8B1v809LCbI4xQMK/mQdlvjVWBvJswXuZwEpz\r\nz7I7HVXKN7Oo6m0tjZikdRYSteFfsGqVEpmWFnL9c0uUwPcNUFiobheb8/hZ\r\nGI2kuIkzTqEDJz05g3Y/obNkimDqjEG/wOhqeHP0hPNg8FVIIFSYjalayBTF\r\nuHaAyLsR50M/OlBrzuzHgj2ER9TRJbyE5QEGVx+Eterhq4AsLxOJagkjYA++\r\nu9a07Wm3JGTXRlKetojzmOR8h+I89c60xuMqSTXtRocMFJR1nk176xQHS1LS\r\n03Ik8v1FA9BiHFUpX/xaOqLiwtmqYW2Xdu9tYIhRzYP4Tn/JxHxbMJ3TZ44M\r\neJzJScqFgp+obBDTI6S9E4KTuZgmd71CEvVBAiNwbPDKjbt4DnDjOHKaHMFV\r\nHIZwM3fXQ6OljcH5C5A8XDgoQMrO2KcP3/k=\r\n=6FmY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "62b9025beeec450a1ff5d61fadcf63963aec5015", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v14.20.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.4.0_1658645817265_0.5395788002431416", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.4.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.4.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "390f91c38d90473592ed43351e801a9d3e0fd747", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz", "fileCount": 6, "integrity": "sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==", "signatures": [{"sig": "MEUCIQDono3w7OK+gC5slDCAv6WhN26fJ7pd7idbXpEe6WoQQgIgSi2tx0Y5KsujiywWLuiuat6IbiUvVxngqnZx+vL1oVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH+hjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMzRAAhT/dpj5x4mAoMw5iJjyV5b4pP44oE2mrxjA+rkhrzuFHX8AT\r\nvUMWBC2U4RMw/ehXfLqELSh5u7pAphqBoF8GE5eTEmvqYUSODQmRwGFpjD1n\r\nu/HiPwEPOxhtBeaBT676yCixeUwumZ92KPCZnVtr0w+UARAdaVN6uaSaWINN\r\nEf1jnHLbuPlAp+Ss9GPBSphYsvzOrNoqLGRv8q8e9A+TFYse2cQWP7x6Ul8C\r\nRrym5o9bmtTm4uodvFvvLZUUv+p9HvNHryFKbjYE85vWJP35YBqi1J4Cp+nG\r\nrAg3jYrIsS4ZTHYa7y6UHjUCgagb3GsGcgQLrimjO8h6FF6pWz0oJvLE/ZhE\r\nHfsf3DeLtmfuhMnrca3sHnC7hR9gqY8cT+AS2zW51MrRd/tDylrJgpl6waFu\r\ni2+FBByqJKcEA7g6Py4qX/PvlagzTCw3zvCXJPk5g7e4mMqyHB9DPc5pG26k\r\nEJpLzsdWyJzgz6mtn/+RO4i//G/JmiPeljeFbv76vuWr4LjB/u3pYgqHoCuk\r\n/c6ImS7shILqyGPqbmijwS43/RUoUwqbLZDOU19JagXHjZgNFYpdwWfZRi/O\r\nTlwaHi5Aa4FD0HzN4BM14szVSUOp8VbmVFHlzlDXULEijL6yhT3kKYENPwna\r\nNHpQUPzHT4UktU6qqUovKpWvH5QShFoXgy0=\r\n=yNBh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "42b9477c199c0c5420b45cfa8c9bb892d94a64af", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v18.7.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.4.1_1663035491198_0.9899658491359393", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e7344d88d9ef18a3c47ded99362ae4a757609380", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.0.tgz", "fileCount": 6, "integrity": "sha512-hDJtKjMLVa7Z+LwnTCxoDLQj6wdc+B8dun7ayF2fYieI6OzfuvcLMB32ihJZ4UhCBwNYGl5bg/x/P9cMdnkc2g==", "signatures": [{"sig": "MEYCIQDXrCgnST1xo5sPJvWw6pA8/Fvx9hc/FdsIeLqQ9xxg1gIhAPgzUwWMxlgpoTV5P9SJDGVkixdJvAoMxwXZ06voYusB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9358}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "391a1f4049fe1d6943ca8e91cf7e2e23f3f1ef73", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.0_1683713941893_0.782290869120198", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "ace7a5eced6dff7d5060c335c52064778216afd3", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.1.tgz", "fileCount": 6, "integrity": "sha512-L8OyySuI6OSQ5hFy9O+7zFjyr4WhAfRjLIOkhQGYl+emwJkd/S4XXT1JpfrgR1jrQ1NcGiOh+yAdGlF8pnC3Jw==", "signatures": [{"sig": "MEUCIBfeR+Jt9VSTGUX0TIX5urWrbuo2FPvjKkOZKROA9WJFAiEAzpvmGIr34LTMR/HoAF+DVl2oucRu2EYcHmes5+YpW8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9440}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "74956db5d547985ac8e60bf1af56f4c61af12e4e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.3.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.1_1688546289223_0.6081603808933818", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "80d0f3e1098c080c8b5a65f41e9427af692dc326", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.2.tgz", "fileCount": 6, "integrity": "sha512-tAlOptU0Xj34V1Y2PNTL4Y0FOJMDB6bZmoW39FeCQIhigGLkqu3Fj6uiXpxIf6Ij274ENdYx64y6Au+ZKlb1IA==", "signatures": [{"sig": "MEQCIBewCIqOoCNvrruPha8TOXLppELKGUn4izgG5BZ7BucrAiAo5WvNOz40IhxIqzkhLgxuv5Zl58X/pKz0JOLNM8H6Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9457}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "0e8cfb85899c8fb01728199d81fd37108e1668ab", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.4.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.2_1689930194022_0.9940320555105346", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d4c49e4b44614607c13fb769bcd85c72bb26a4a5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.3.tgz", "fileCount": 6, "integrity": "sha512-8sHeDOmXC8csczMrYEOf0UTNa4yE2SxV5JGeT/LP1n0OYVDUUFPxG9vdk2AlDlIit4t+Kf0xCtpgXPBwnn/9pw==", "signatures": [{"sig": "MEQCIHxi6is7fNA1zn+F0dUGJlDYmCNy6xmAGUjufCvtcWlxAiBsrW7Qgp0AW3zsdTXT/0iXzW67dEaP1W8mP4BLTT1aDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9457}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66a6819f44a57152798cb3b0a9272c65752bae86", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.3_1697007741709_0.31438586330703977", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c6fc8eab610d3a11eb475391e52584bacfc020f4", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.4.tgz", "fileCount": 6, "integrity": "sha512-S/x2iOCvDaCASLYsOOgWOq4bCfKYVqvO/uxjkaYyZ3rVsVE3CeAI/c84NpyuBBymEgNvHgjEot3a9/Z/kXvqsg==", "signatures": [{"sig": "MEYCIQDj8+ftnmG63u2ODxVL5Nn7YpNW6oT5y+WB5M02f577+wIhANml97PARdEFGH3yLPMZHD9Qn3jcaiYIgX+owAhbZAQK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9457}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "984c56c59568283889c3f0f89e58d370e4fd10f8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.4_1702304408477_0.4858612859458411", "host": "s3://npm-registry-packages"}}, "0.5.5": {"name": "babel-plugin-polyfill-regenerator", "version": "0.5.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.5.5", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8b0c8fc6434239e5d7b8a9d1f832bb2b0310f06a", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.5.tgz", "fileCount": 6, "integrity": "sha512-OJGYZlhLqBh2DDHeqAxWB1XIvr49CxiJ2gIt61/PU55CQK4Z58OzMqjDe1zwQdQk+rBYsRc+1rJmdajM3gimHg==", "signatures": [{"sig": "MEUCIQC1jKFein4X1KQU3SxRBgW9d0mjKp9jaSE0BQeBC0l3cQIgNIa5O0Jp0vucMGtgVgJoXqdm77tp42Too79AjE7XaN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9457}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "9738ea2a12643376a52c9be30c20ac19426a88cb", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.5.5_1705571831526_0.5634488541966673", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.6.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f70fd0e223ef99537fd85babf4366013a576269b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.0.tgz", "fileCount": 6, "integrity": "sha512-XU8jJEaL1pePADgARjMZDaI9nF/hdU/Vuf155O8yXAxmk2qV2O5GLhli1l8wNeUYA/mFJhTC+Iy1J1V2XWopPA==", "signatures": [{"sig": "MEUCICZFCHonkhTqFFyJDko0/BNPBvxg4irJ/SKWssCUzdIAAiEA9vCGtSJsQkA5epze8AcFzww9YX8bKQLBe8+QZnIS8BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9736}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "58703f07c9cff9f27d145215265042094739a175", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.0_1709919060825_0.1115858818687483", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.6.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4f08ef4c62c7a7f66a35ed4c0d75e30506acc6be", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.1.tgz", "fileCount": 6, "integrity": "sha512-JfTApdE++cgcTWjsiCQlLyFBMbTUft9ja17saCc93lgV33h4tuCVj7tlvu//qpLwaG+3yEz7/KhahGrUMkVq9g==", "signatures": [{"sig": "MEUCIQCV2VJicgMW3/a6nxN8vbB6TSmbld5ldrzGIhHaUbiYmQIgdCgXylJvR03Ar1WZPVA5w8rkJhOXdTwu7fpkJeG13xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9736}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1ce88db2507db2ef3d2ed2a2f920a3cf0b9364b5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.1_1710259199560_0.9317059658735485", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.6.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "addc47e240edd1da1058ebda03021f382bba785e", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.2.tgz", "fileCount": 6, "integrity": "sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg==", "signatures": [{"sig": "MEYCIQCiaN/KkyJI2CzzKiTnToDItDObJrNyb1QeEy/XkkjXeQIhAIp257pLT5O6Q9AVSrrDESaW4NPkuKN4fc5wsiir3iRp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9736}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "2da4da8e1a3d87640c88a3cd7e650cbb1c049a33", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.2_1713809615474_0.0032079367357673583", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.6.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "abeb1f3f1c762eace37587f42548b08b57789bc8", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.3.tgz", "fileCount": 6, "integrity": "sha512-LiWSbl4CRSIa5x/JAU6jZiG9eit9w6mz+yVMFwDE83LAWvt0AfGBoZ7HS/mkhrKuh2ZlzfVZYKoLjXdqw6Yt7Q==", "signatures": [{"sig": "MEYCIQD+sMqGIws28OvQXUH0kklp0nMe1dGaecjLrxsDDeNTsAIhAPcVlx0G4R2R/Rl1DoG3ggcJDPdi0+Z998PCvRfUfIvU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9736}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66340fb145086a826c496f008f67488367846c09", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v22.9.0+x64 (linux)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.3_1731351013832_0.6506367256850876", "host": "s3://npm-registry-packages"}}, "0.6.4": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "babel-plugin-polyfill-regenerator@0.6.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "428c615d3c177292a22b4f93ed99e358d7906a9b", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "fileCount": 6, "integrity": "sha512-7gD3pRadPrbjhjLyxebmx/WrFYcuSjZ0XbdUujQMZ/fcE9oeewk2U/7PCvez84UeuK3oSjmPZ0Ch0dlupQvGzw==", "signatures": [{"sig": "MEYCIQC++cGkF6TkQ9GmgeuTvExyrBZJJpgb1RoNf37lWbpJkQIhAKP0Squ8N09dichytpfASfSJNl5R9/EF4gWIQpu70jhC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9736}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "_npmVersion": "lerna/3.22.0/node@v23.6.1+arm64 (darwin)", "description": "A Babel plugin to inject imports to regenerator-runtime", "directories": {}, "_nodeVersion": "23.6.1", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.4_1742390649008_0.3515568382320531", "host": "s3://npm-registry-packages-npm-production"}}, "0.6.5": {"name": "babel-plugin-polyfill-regenerator", "version": "0.6.5", "description": "A Babel plugin to inject imports to regenerator-runtime", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-regenerator": "~7.14.5", "regenerator-runtime": "^0.14.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "homepage": "https://github.com/babel/babel-polyfills#readme", "_id": "babel-plugin-polyfill-regenerator@0.6.5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/3.22.1/node@v24.3.0+x64 (linux)", "dist": {"integrity": "sha512-ISqQ2frbiNU9vIJkzg7dlPpznPZ4jOiUQ1uSmB0fEHeowtN3COYRsXr/xexn64NpU13P06jc/L5TgiJXOgrbEg==", "shasum": "32752e38ab6f6767b92650347bf26a31b16ae8c5", "tarball": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.5.tgz", "fileCount": 6, "unpackedSize": 10012, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBS/0v9b/BZ8zVKMrH1poD0/4wMz+G8pwCDBIS6nKvEWAiEAhEav3rsF3LHCdwiTK+H1CW2qg6N4w5QJx4t72zVwd/E="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/babel-plugin-polyfill-regenerator_0.6.5_1751042269092_0.3969316089609387"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-04-08T14:28:03.613Z", "modified": "2025-06-27T16:37:49.447Z", "0.0.0": "2020-04-08T14:28:03.731Z", "0.0.1": "2020-05-26T20:42:24.719Z", "0.0.2": "2020-05-26T21:00:49.983Z", "0.0.3": "2020-07-10T21:36:07.929Z", "0.0.4": "2020-08-31T22:59:43.661Z", "0.0.5": "2020-10-03T10:54:35.201Z", "0.0.6": "2020-11-09T14:37:29.460Z", "0.0.7": "2020-12-22T00:53:51.560Z", "0.0.8": "2020-12-22T01:02:01.561Z", "0.0.9": "2021-01-07T15:06:07.288Z", "0.1.0": "2021-01-09T14:09:30.047Z", "0.1.1": "2021-02-22T00:57:13.661Z", "0.1.2": "2021-02-23T11:23:18.418Z", "0.1.3": "2021-02-23T13:21:19.577Z", "0.1.5": "2021-02-25T23:18:00.606Z", "0.1.6": "2021-03-02T08:38:51.585Z", "0.2.0": "2021-03-31T15:52:57.584Z", "0.2.1": "2021-05-21T22:20:44.152Z", "0.2.2": "2021-05-26T10:10:32.118Z", "0.2.3": "2021-10-29T22:20:17.871Z", "0.3.0": "2021-11-13T10:35:01.236Z", "0.3.1": "2022-01-15T15:04:46.265Z", "0.4.0": "2022-07-24T06:56:57.467Z", "0.4.1": "2022-09-13T02:18:11.372Z", "0.5.0": "2023-05-10T10:19:02.029Z", "0.5.1": "2023-07-05T08:38:09.389Z", "0.5.2": "2023-07-21T09:03:14.176Z", "0.5.3": "2023-10-11T07:02:21.871Z", "0.5.4": "2023-12-11T14:20:08.679Z", "0.5.5": "2024-01-18T09:57:11.694Z", "0.6.0": "2024-03-08T17:31:01.002Z", "0.6.1": "2024-03-12T15:59:59.713Z", "0.6.2": "2024-04-22T18:13:35.668Z", "0.6.3": "2024-11-11T18:50:14.161Z", "0.6.4": "2025-03-19T13:24:09.218Z", "0.6.5": "2025-06-27T16:37:49.274Z"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "license": "MIT", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "description": "A Babel plugin to inject imports to regenerator-runtime", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "readme": "# babel-plugin-polyfill-regenerator\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev babel-plugin-polyfill-regenerator\n```\n\nor using yarn:\n\n```sh\nyarn add babel-plugin-polyfill-regenerator --dev\n```\n\n## Usage\n\nAdd this plugin to your Babel configuration:\n\n```json\n{\n  \"plugins\": [[\"polyfill-regenerator\", { \"method\": \"usage-global\" }]]\n}\n```\n\nThis package supports the `usage-pure`, `usage-global`, and `entry-global` methods.\nWhen `entry-global` is used, it replaces imports to `regenerator-runtime`.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}