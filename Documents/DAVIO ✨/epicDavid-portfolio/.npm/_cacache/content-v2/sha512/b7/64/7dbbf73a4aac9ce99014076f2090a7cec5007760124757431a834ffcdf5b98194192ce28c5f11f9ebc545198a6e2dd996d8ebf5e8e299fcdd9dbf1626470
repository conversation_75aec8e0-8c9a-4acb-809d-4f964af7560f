{"_id": "word-wrap", "_rev": "36-6895184e1867ebb101f1a82a9f5e4331", "name": "word-wrap", "description": "Wrap words to a specified length.", "dist-tags": {"latest": "1.2.5"}, "versions": {"0.1.0": {"name": "word-wrap", "description": "Wrap words.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}], "keywords": ["word-wrap", "wrap", "words", "text", "soft wrap", "line break", "new line"], "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R test"}, "devDependencies": {"verb": "~0.1.20"}, "_id": "word-wrap@0.1.0", "dist": {"shasum": "a2a8612c24f638391716d38db01afad2a7918926", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.0.tgz", "integrity": "sha512-6Ee7CUc3EEsgjsvywkd0TL5XRWF39abAFHaLT/VeJWDoRFvGlzAX4QpBsnVHvkSiYm9och7Klb+b4F04wbhH2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAivPI1BhOh06sAq+HwKlBlyZjbowqeeyliqtQcsTAKAIgXwYlFJS6DIYRoWpNYZ48ki8Z0spq6DA8axTqIOfgE+w="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "word-wrap", "description": "Wrap words.", "version": "0.1.2", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}], "keywords": ["word-wrap", "wrap", "words", "text", "soft wrap", "line break", "new line"], "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R test"}, "dependencies": {"longest": "~0.2.1"}, "devDependencies": {"verb": "~0.2.2"}, "_id": "word-wrap@0.1.2", "dist": {"shasum": "e47fde96d14ad9ee427731c42b4decb778127b70", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.2.tgz", "integrity": "sha512-IwvtozHB9+xilbjWwb/0uDdER6Hiku6hQCGrgvojVWkboBkjdlLa58IDgNBcHyDrSZEfglogaBnFtDGAJUJNDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBy1T+YElva/jIbAIaJlYUXyxs1DZDNQT0GQvP9BzjfpAiAMGwpiumhJz3TZE2Jh5xxcdj2HMzYy1I3EMGTBI0vrCw=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "0.1.3", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}], "main": "index.js", "keywords": ["word-wrap", "wrap", "words", "text", "soft wrap", "line break", "new line"], "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R test"}, "devDependencies": {"verb": "~0.2.2"}, "_id": "word-wrap@0.1.3", "_shasum": "745523aa741b12bf23144d293795c6197b33eb1e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "745523aa741b12bf23144d293795c6197b33eb1e", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.3.tgz", "integrity": "sha512-N10MyEPL9oFtdKFV/BcfFYZLrnPI3Rpw3NSh2N6xD6WezeeqLssdxXmttYrGmvPt5dTFrlORrujow8jnSimdcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOR2GzA59bhLXkOJ3hiYD08oknqYk80U4smuVjdNCLNQIgWECk/onRa3OY5aDpaV830Fx/UjlsF/il5LECtTwjUd0="}]}, "directories": {}}, "0.2.0": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "_id": "word-wrap@0.2.0", "_shasum": "3c6673ca3d0b26d01dd5deb6e159746e1af5ce70", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3c6673ca3d0b26d01dd5deb6e159746e1af5ce70", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.2.0.tgz", "integrity": "sha512-op/myU3yTuVz4OAJ7denLdVRegMRsGgXN86vEwD/izVkyblZ/L9qLa0kaLPgs/hritFX3mo3rQLW6En7b1M2cA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCulYYZWSAE++qC5OhqQztM4w4C+gfZDUg5JiLoHSQSfQIgMx4yBzCuXyR81aXlgQKx0S7qtJtLCLD/aBL4z8YkmRY="}]}, "directories": {}}, "0.3.0": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "0.3.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "devDependencies": {"should": "^4.3.0"}, "_id": "word-wrap@0.3.0", "_shasum": "15c75db42fc55e67796ee59d42c8d625a40fb095", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "15c75db42fc55e67796ee59d42c8d625a40fb095", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.3.0.tgz", "integrity": "sha512-RT3BNcNy5pY8oqypfifsveNN/2p0apUvw/EqYSNOHAVcjCgbvOPh2ezUo64Rt/PmrRl9naSOUC5whPuQIzmgyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChhhP2kiffZL2sf+7WW7zz6mEOPETPUm3cH2xHWzgrWAIgMABlMLbeub9/Nil1f/KHpBF0JzzLHAc+xmNP7Rspy1o="}]}, "directories": {}}, "0.3.1": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "0.3.1", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "devDependencies": {"should": "^4.3.0"}, "_id": "word-wrap@0.3.1", "_shasum": "7c7b5296ed5a4a9cf234ae3d10bdc1d3a637f197", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7c7b5296ed5a4a9cf234ae3d10bdc1d3a637f197", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.3.1.tgz", "integrity": "sha512-rp7vWtSqI8kG+KRceRK52ufV/EvyFslFrwiSrytAVUZoTRKnC1jBYNcmeK1v2jct6JPgpFXBRGnKx+a0Lmo34w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnhSN8yeztVvOC4hglckfINxMm+5s5t0sRAJe38nJxMAIgHZwmLa2KOTpxAG8EoUIfPhBwIHP+v988fe6NbaVCxJY="}]}, "directories": {}}, "1.0.0": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"should": "^4.3.0"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "_id": "word-wrap@1.0.0", "_shasum": "e8ef895d990bad1d2e5431b274343834bd8ad07c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e8ef895d990bad1d2e5431b274343834bd8ad07c", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.0.tgz", "integrity": "sha512-3HlGqnmiYVeEfffeYSrHC2CvivPwl7EVEsk/nI/ngHRKsrXHohUTjQo4azFoJoRpURQLxJDlScQxV16ONNRX+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDHzsRoobU5cvefa9/9+tzvCuAgLYk7Gb3UHnKG5CYwAiB9Mn9sxOStdIi+yetOZFpKezsYgkN5qcg+k5PfFRHB2A=="}]}, "directories": {}}, "1.0.1": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "licenses": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE-MIT"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "gitHead": "208bbb662a1ac71b8fe143ff13740e2f7eef0d7d", "_id": "word-wrap@1.0.1", "_shasum": "674ad9cd8c941973a042ae79d601ec5fa2e4578a", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "674ad9cd8c941973a042ae79d601ec5fa2e4578a", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.1.tgz", "integrity": "sha512-XBTx1ISiFZEWLRvXIFH1EZ593Zrk12GwX4gE7s37Xk1Am9wuBmHcZMwYciuWgi+NRhHUPg147IBIQ544M/8Xkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICT7oYRFVByWRA96ITXQaYmQKxqOQ/zrKFP8md3uGVnEAiAF71i5gsdGNx8ZTACNRh49eT8HfRonKnWLbUI/rOstXw=="}]}, "directories": {}}, "1.0.2": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "gitHead": "a55d6f38b248ea7a1e64f2426827c0aa645c6e5c", "_id": "word-wrap@1.0.2", "_shasum": "23ef4cfdc5e62c1961aed99781bc4a803d4060db", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "23ef4cfdc5e62c1961aed99781bc4a803d4060db", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.2.tgz", "integrity": "sha512-z2F9ub8xd03t4yK51td/GNmPgh/vBlhguWwuYlWZqMgqjn8ItSE5g3IcmVp2t9j1Nq+NQlW6SbnVXTigdb37Bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0Lov1DFTAW1p/WGGv0twPo0hIKZ8M3VWIwRqtN3O4BQIgZkAlMJzfrREN0ICHE1LtIIqK4SVZP3m7HaL7YfHrJPg="}]}, "directories": {}}, "1.0.3": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.0.3", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert/"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "gitHead": "0b3a63618dfc3d6d90769c6aec2b2af51ad8dff7", "_id": "word-wrap@1.0.3", "_shasum": "84d423b8dde01daf478c16c13e163cb9c78ecfff", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "84d423b8dde01daf478c16c13e163cb9c78ecfff", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.3.tgz", "integrity": "sha512-pYIwXGqZIPNUCL1u3kg75tBA9VAsWrACv8lourByaaAmGan3MmNlEr1MFxdDTDlL7E4ZNyMC15z2ZrdH02Ouug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBexanR8P6RN6pijoUkBBxiVo5nKQmahxc9dRnw3oPI7AiEA3XWHA0THOfb+KsmwB1J0QMLcr7i506n+iv+hY2WVdtM="}]}, "directories": {}}, "1.1.0": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["break", "line", "new-line", "carriage", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "gitHead": "fe906b287d4f1df72dd983e976b6e514750544f5", "_id": "word-wrap@1.1.0", "_shasum": "356153d61d10610d600785c5d701288e0ae764a6", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "356153d61d10610d600785c5d701288e0ae764a6", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.1.0.tgz", "integrity": "sha512-6E0pg9o7nVagpx7xVMlK/gZriE9TLIyDGqPiooYdLCOiYFazQe09vIHRWXHYMupVHBgViR88vgx5OVGeo/HW7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVJHTZqR3Q6qRyv3MjfEEvyXbF0ubVRDPr/EmCeFVk+AiASfQpT3I4f6FGVcSoFDg/WlktC4ztUHA9PAIMntKTpSw=="}]}, "directories": {}}, "1.2.0": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.0", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["wordcount", "unique-words", "common-words", "shuffle-words"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "9ee5ce23601a30a0c47b152dbc435ae28963d60b", "_id": "word-wrap@1.2.0", "_shasum": "ee971b6b7ce9ecae73a4b89a1cfdaa48dcf38ce7", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ee971b6b7ce9ecae73a4b89a1cfdaa48dcf38ce7", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.0.tgz", "integrity": "sha512-+KLeGhwldY/lp9v+iA0ltNjQ1AP1la4ny5rmrTFQFOlPSnNgYfIuHQlAkCAVUyobIgaxygJM2QpA4N0NHsMgeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFOopdDrR0BK9Pg1Kxmi/osLJqlfwvFi88gc7RkqiZxeAiEAnJ6s8mMlYXVcuZz5+d2l5b5m/eq7tzA2FhDAwH8SkMk="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/word-wrap-1.2.0.tgz_1483016767340_0.4582841566298157"}, "directories": {}}, "1.2.1": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.1", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "typings": "index.d.ts", "engines": {"node": ">=0.8"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "2ae779e2089e83d9e32b7c05382c4a1ac2b1d5c1", "_id": "word-wrap@1.2.1", "_shasum": "248f459b465d179a17bc407c854d3151d07e45d8", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "248f459b465d179a17bc407c854d3151d07e45d8", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.1.tgz", "integrity": "sha512-Da7Yu/UaUZ0K7lWKtPdJZ/Zilrag1V1K6IQykT+DEMz6oN7uWYY2QjMgcFB4i4MIsRDzuC6r7zAzwaCHFjBaOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWFBbzlEyQ//jBb8UBeSmxeE+Z+2scJc0Wcx1qy39MfAiEAmS96dt9TkncYYoxs1ndDCd/vE7ZSvlzSWOsQIzuqw5I="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/word-wrap-1.2.1.tgz_1485796135235_0.5473448638804257"}, "directories": {}}, "1.2.2": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.2", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "4579da43b699cd82efe254753b58b5fb85855f5a", "_id": "word-wrap@1.2.2", "_shasum": "8fa78c3bda3e3138c7797fabceae709968814b41", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8fa78c3bda3e3138c7797fabceae709968814b41", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.2.tgz", "integrity": "sha512-zOvQWZh81woequl4nXg1yLyeE3qCid2aSs4YvJa4hdrbKobDoiMb82g4xTdraoXXM9BgbNZGHzECeK8wrMGhxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCtlxf/twT+AOWWvwArXUbIL1YjlKWoT41nQh9cZ/uUAIgENnQKriu2hzn0mUeDto6HvihkkwEQ+XpnXE8k7pl3LQ="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/word-wrap-1.2.2.tgz_1493702845798_0.9076362559571862"}, "directories": {}}, "1.2.3": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.3", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js", "index.d.ts"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "cdab7f263a0af97df0626043d908aa087d3d3089", "_id": "word-wrap@1.2.3", "_npmVersion": "5.0.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==", "shasum": "610636f6b1f703891bd34771ccb17fb93b47079c", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDx27+lBfLp8aSzUMAu8N3rU7IwflMyyyNGN1tja4tTFAIhALhbb46c+LQ2aZ5T0t0nyVTYcyZf4xLYpRmFw392UFKt"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/word-wrap-1.2.3.tgz_1496458566550_0.1288193075452"}, "directories": {}}, "1.2.4": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.4", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "f64b188c7261d26b99e1e2075d6b12f21798e83a", "_id": "word-wrap@1.2.4", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-2V81OA4ugVo5pRo46hAoD2ivUJx8jXmWXfUkY4KFNw0hEptvN0QfH3K4nHiwzGeKl5rFKedV48QVoqYavy4YpA==", "shasum": "cb4b50ec9aca570abd1f52f33cd45b6c61739a9f", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.4.tgz", "fileCount": 5, "unpackedSize": 11829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3FCESa1UWmAxQhJ3Eb42EIIpsRtIU/UZSeLTHm2vrbgIgJpgWYFOdWAc4BvzmmnomiXzw9tY2oNu1BMRv/ylS7cI="}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/word-wrap_1.2.4_1689698898740_0.2274379081243567"}, "_hasShrinkwrap": false}, "1.2.5": {"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.5", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "207044ebda1dd3809d15b6000a48409266536771", "_id": "word-wrap@1.2.5", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-B<PERSON>22B<PERSON>eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==", "shasum": "d2c45c6dd4fbce621a66f136cbe328afd0410b34", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "fileCount": 5, "unpackedSize": 11845, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHGyCX26h8kvIdRX65I9XKJwiBANVGy/41Pnij0VDnnwIgL6K9zXEXWoClMoFWFTRXfFxgpWPtnhMCi0S3OBrkFKA="}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/word-wrap_1.2.5_1690036658638_0.8987296869418162"}, "_hasShrinkwrap": false}}, "readme": "# word-wrap [![NPM version](https://img.shields.io/npm/v/word-wrap.svg?style=flat)](https://www.npmjs.com/package/word-wrap) [![NPM monthly downloads](https://img.shields.io/npm/dm/word-wrap.svg?style=flat)](https://npmjs.org/package/word-wrap) [![NPM total downloads](https://img.shields.io/npm/dt/word-wrap.svg?style=flat)](https://npmjs.org/package/word-wrap) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/word-wrap.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/word-wrap)\n\n> Wrap words to a specified length.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save word-wrap\n```\n\n## Usage\n\n```js\nvar wrap = require('word-wrap');\n\nwrap('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.');\n```\n\nResults in:\n\n```\n  Lorem ipsum dolor sit amet, consectetur adipiscing\n  elit, sed do eiusmod tempor incididunt ut labore\n  et dolore magna aliqua. Ut enim ad minim veniam,\n  quis nostrud exercitation ullamco laboris nisi ut\n  aliquip ex ea commodo consequat.\n```\n\n## Options\n\n![image](https://cloud.githubusercontent.com/assets/383994/6543728/7a381c08-c4f6-11e4-8b7d-b6ba197569c9.png)\n\n### options.width\n\nType: `Number`\n\nDefault: `50`\n\nThe width of the text before wrapping to a new line.\n\n**Example:**\n\n```js\nwrap(str, {width: 60});\n```\n\n### options.indent\n\nType: `String`\n\nDefault: `` (two spaces)\n\nThe string to use at the beginning of each line.\n\n**Example:**\n\n```js\nwrap(str, {indent: '      '});\n```\n\n### options.newline\n\nType: `String`\n\nDefault: `\\n`\n\nThe string to use at the end of each line.\n\n**Example:**\n\n```js\nwrap(str, {newline: '\\n\\n'});\n```\n\n### options.escape\n\nType: `function`\n\nDefault: `function(str){return str;}`\n\nAn escape function to run on each line after splitting them.\n\n**Example:**\n\n```js\nvar xmlescape = require('xml-escape');\nwrap(str, {\n  escape: function(string){\n    return xmlescape(string);\n  }\n});\n```\n\n### options.trim\n\nType: `Boolean`\n\nDefault: `false`\n\nTrim trailing whitespace from the returned string. This option is included since `.trim()` would also strip the leading indentation from the first line.\n\n**Example:**\n\n```js\nwrap(str, {trim: true});\n```\n\n### options.cut\n\nType: `Boolean`\n\nDefault: `false`\n\nBreak a word between any two letters when the word is longer than the specified width.\n\n**Example:**\n\n```js\nwrap(str, {cut: true});\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [common-words](https://www.npmjs.com/package/common-words): Updated list (JSON) of the 100 most common words in the English language. Useful for… [more](https://github.com/jonschlinkert/common-words) | [homepage](https://github.com/jonschlinkert/common-words \"Updated list (JSON) of the 100 most common words in the English language. Useful for excluding these words from arrays.\")\n* [shuffle-words](https://www.npmjs.com/package/shuffle-words): Shuffle the words in a string and optionally the letters in each word using the… [more](https://github.com/jonschlinkert/shuffle-words) | [homepage](https://github.com/jonschlinkert/shuffle-words \"Shuffle the words in a string and optionally the letters in each word using the Fisher-Yates algorithm. Useful for creating test fixtures, benchmarking samples, etc.\")\n* [unique-words](https://www.npmjs.com/package/unique-words): Returns an array of unique words, or the number of occurrences of each word in… [more](https://github.com/jonschlinkert/unique-words) | [homepage](https://github.com/jonschlinkert/unique-words \"Returns an array of unique words, or the number of occurrences of each word in a string or list.\")\n* [wordcount](https://www.npmjs.com/package/wordcount): Count the words in a string. Support for english, CJK and Cyrillic. | [homepage](https://github.com/jonschlinkert/wordcount \"Count the words in a string. Support for english, CJK and Cyrillic.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 47 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 7  | [OlafConijn](https://github.com/OlafConijn) |  \n| 3  | [doowb](https://github.com/doowb) |  \n| 2  | [aashutoshrathi](https://github.com/aashutoshrathi) |  \n| 2  | [lordvlad](https://github.com/lordvlad) |  \n| 2  | [hildjj](https://github.com/hildjj) |  \n| 1  | [danilosampaio](https://github.com/danilosampaio) |  \n| 1  | [2fd](https://github.com/2fd) |  \n| 1  | [leonard-thieu](https://github.com/leonard-thieu) |  \n| 1  | [mohd-akram](https://github.com/mohd-akram) |  \n| 1  | [toddself](https://github.com/toddself) |  \n| 1  | [wolfgang42](https://github.com/wolfgang42) |  \n| 1  | [zachhale](https://github.com/zachhale) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2023, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on July 22, 2023._", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "time": {"modified": "2023-07-22T14:37:38.953Z", "created": "2014-03-30T08:47:04.382Z", "0.1.0": "2014-03-30T08:47:04.382Z", "0.1.2": "2014-04-23T00:23:09.096Z", "0.1.3": "2014-05-19T05:19:32.774Z", "0.2.0": "2014-11-23T15:58:15.450Z", "0.3.0": "2014-12-04T21:14:15.332Z", "0.3.1": "2014-12-04T21:20:23.708Z", "1.0.0": "2014-12-04T23:07:15.710Z", "1.0.1": "2015-02-27T21:35:33.424Z", "1.0.2": "2015-03-07T23:21:20.018Z", "1.0.3": "2015-04-20T03:54:49.565Z", "1.1.0": "2015-07-17T16:09:45.195Z", "1.2.0": "2016-12-29T13:06:09.224Z", "1.2.1": "2017-01-30T17:08:56.129Z", "1.2.2": "2017-05-02T05:27:27.815Z", "1.2.3": "2017-06-03T02:56:07.519Z", "1.2.4": "2023-07-18T16:48:18.971Z", "1.2.5": "2023-07-22T14:37:38.795Z"}, "homepage": "https://github.com/jonschlinkert/word-wrap", "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/word-wrap.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "readmeFilename": "README.md", "users": {"nfd": true, "fedor": true, "jakedetels": true, "scottfreecode": true, "bluelovers": true, "flumpus-dev": true}, "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "localhost:8080"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://2fd.github.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/hildjj"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://tck.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/lordvlad"}, {"name": "<PERSON>", "url": "http://www.linestarve.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zachhale.com"}]}