{"_id": "tmpl", "_rev": "14-1c4ff61963d1b318dca3c3708ec796cc", "name": "tmpl", "description": "JavaScript micro templates.", "dist-tags": {"latest": "1.0.5"}, "versions": {"1.0.0": {"name": "tmpl", "description": "JavaScript micro templates.", "version": "1.0.0", "homepage": "https://github.com/nshah/nodejs-tmpl", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/tmpl", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-tmpl.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/tmpl/1.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "tmpl@1.0.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "d01aa466767a639c2444aacee03db3a05ba201f0", "tarball": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.0.tgz", "integrity": "sha512-aps7GCZD+RWpnnfIlfLfX4wg+/2298u2pJ1xKGX/yreaSMEggfWCjKGZwduxQk82oC8mPh9yrR0qcGghQEMAZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGthDkD4n6uQkWlKEnIx2LB6411UaCtL7W3ak+2NwoRSAiA+UHHLhZjcSI2oBSMILBJjks0Q7AYgI18t+4gBAWgKxg=="}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "tmpl", "description": "JavaScript micro templates.", "version": "1.0.1", "homepage": "https://github.com/nshah/nodejs-tmpl", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/tmpl", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-tmpl.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/tmpl/1.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "tmpl@1.0.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "a29111af505af8dd7292ae01b063878de39b7cca", "tarball": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.1.tgz", "integrity": "sha512-VQZJURFOF9D2+tinCI8CokvflK4/EprzOvWCpENQjcp2/0VrDIXs1nYmtQJakeAQfWhaQrYTy+NAJw12Lna+0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBzvnGyPgBkgxQjAy0+nnNg0bGMpSsmOOMXO+hu7xOpYAiEAuaqbWO9crCrueJzrarzi44EeUdtNhBC/wXrFnNHxjpY="}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "tmpl", "description": "JavaScript micro templates.", "version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/nshah/nodejs-tmpl", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/tmpl", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-tmpl"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "devDependencies": {"mocha": "0.12.x"}, "engines": {"node": "0.6.x"}, "gitHead": "b141e793640248c0900829c35f6b3195243e94e8", "bugs": {"url": "https://github.com/daaku/nodejs-tmpl/issues"}, "_id": "tmpl@1.0.3", "_shasum": "59242f91c3f12a9ee2f31d489176caf16c12d53c", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "dist": {"shasum": "59242f91c3f12a9ee2f31d489176caf16c12d53c", "tarball": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.3.tgz", "integrity": "sha512-reGkNSkpa0maD0+QJcZFsz6DemFpX++DKIG0s089xxqE6R8dfF4XkZuq2932zbizcG+XgVY0rJ3NQF5D829i1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFSGJsTXS2lGCSiukRj8aUDroVFNs5Chz6Riy5IK4ZP6AiEAjzjcXlARoJv+G75uz1KA776Jhwl3U6Eh2B5OqwYjS2k="}]}, "directories": {}}, "1.0.4": {"name": "tmpl", "description": "JavaScript micro templates.", "version": "1.0.4", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/nshah/nodejs-tmpl", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/tmpl", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-tmpl"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "devDependencies": {"mocha": "0.12.x"}, "gitHead": "aa7fef875b8eb2da3817940b50f58fd1d8754022", "bugs": {"url": "https://github.com/daaku/nodejs-tmpl/issues"}, "_id": "tmpl@1.0.4", "_shasum": "23640dd7b42d00433911140820e5cf440e521dd1", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "dist": {"shasum": "23640dd7b42d00433911140820e5cf440e521dd1", "tarball": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.4.tgz", "integrity": "sha512-9tP427gQBl7Mx3vzr3mquZ+Rq+1sAqIJb5dPSYEjWMYsqitxARsFCHkZS3sDptHAmrUPCZfzXNZqSuBIHdpV5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdTsQelJuG4iqVcX/lTjJJjpbFxStTHlSRa/ZlNRHkAAIhAMby1YLyH7NWVPJwE5rQ+dtkWniVUgo8jdQoIwl5wuwe"}]}, "directories": {}}, "1.0.5": {"name": "tmpl", "description": "JavaScript micro templates.", "version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/daaku/nodejs-tmpl", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/tmpl", "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-tmpl.git"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "devDependencies": {"mocha": "^9.1.1"}, "gitHead": "153b36d516b581950a652cb625d6dcd2dd4e1c37", "bugs": {"url": "https://github.com/daaku/nodejs-tmpl/issues"}, "_id": "tmpl@1.0.5", "_nodeVersion": "16.8.0", "_npmVersion": "7.22.0", "dist": {"integrity": "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==", "shasum": "8683e0b902bb9c20c4f726e3c0b69f36518c07cc", "tarball": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "fileCount": 4, "unpackedSize": 2774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNwq+CRA9TVsSAnZWagAAOCUP/1IY2JsoZMLyaf3P5rDv\neh4mMM1P0e5f+4e/aJ/48yCgmC5nt4i/BRXL4dbx21nq3iaobT0+iIiwGMSG\no4DMF/542eaqASicgUevWpCM+6uE0Xh+QoPYmkLFFAcjz5Oln0uXzXt44X/k\nYJS8VznZ8ItNBCPDBEn8J3rr/yxfYpYCDMmJZcxKvsijOJqoLvU8ooRKfAtR\nZfV9pGpaYL3tC2llFpYOyQvMg2RzIEQC0W9lfifvroCSPT6k3pOK8q4B4uYo\noOgjhcy4O/NB0zt+sISe+aWiEOLAq5k8uQtCSZhJMmvcPCnAdEh5IBfxcRvN\nOqSH1x4im6cqegsNe9YzCjLEnwK3aBgValWZVewuP45gArcUMdCcSA9cCh43\nglDs8DTTN0LXAlYtpi/61JFqgP3t+HvasC+OEHlYrp6b87X0OwCPrGpXb8lz\nEmAE7wp+ijAO7eJaseV5Mi72bVIE1nwsTXue7phDQPxe9/bUmla34s6VJiT+\niXOHg8RuKLARsIH3sb55nnvAlzjJHJ9NwUee+gil8HZ5HHZigPFnH00APYm4\nsgLlPPeQFzzDUrR1QaHLOcqsBziL9xZAGPAtcCwMd26X2q/wEJ0iU5cXNScW\nE1pcmGWNVrmd/rA5sc/sn0mze5+zOVyQtdLZWx9QEd/czk4ml9eauH1bsFtt\nQxgq\r\n=dJ40\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrN+Lc3PHVJN7HPaqNTxQXWIZ1V/WcA8JbnFo+1V8mwwIgGKCvlHR0J8kePWcL9iEYmj8z+EcrkfT9SmSopIZ6jCA="}]}, "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tmpl_1.0.5_1630997182373_0.7601906972342574"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "time": {"modified": "2022-06-27T07:18:03.675Z", "created": "2011-08-08T02:26:47.249Z", "1.0.0": "2011-08-08T02:26:49.335Z", "1.0.1": "2011-08-17T21:09:46.235Z", "1.0.3": "2015-03-04T18:10:50.192Z", "1.0.4": "2015-03-28T18:30:45.287Z", "1.0.5": "2021-09-07T06:46:22.501Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-tmpl.git"}, "readme": "tmpl [![Build Status](https://secure.travis-ci.org/nshah/nodejs-tmpl.png)](http://travis-ci.org/nshah/nodejs-tmpl)\n====\n\nSimple string formatting using `{}`.\n\n```javascript\nassert.equal(\n  tmpl('the answer is {answer}', { answer: 42 }),\n  'the answer is 42')\n```\n", "homepage": "https://github.com/daaku/nodejs-tmpl", "bugs": {"url": "https://github.com/daaku/nodejs-tmpl/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "readme.md"}