{"_id": "co", "_rev": "349-95d6357b19cfdc507d44531903681058", "name": "co", "description": "generator async control flow goodness", "dist-tags": {"latest": "4.6.0"}, "versions": {"1.0.0": {"name": "co", "version": "1.0.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0"}, "license": "MIT", "_id": "co@1.0.0", "dist": {"shasum": "e933dc090252deb8e6c20dd93d43d64c303bc700", "tarball": "https://registry.npmjs.org/co/-/co-1.0.0.tgz", "integrity": "sha512-Mg47Q+DXVlbrdRcA3pLXSPrujFflUNKYD3anVm310OvAcNZGlB7M5h2uQVuyed2YeoYFw2djOvZ9xsgs/1gRbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQ7V1zucvd/GLwxYWVZPVIMFnQczxnEycsMrSa8MlBgAiEA7rgSLBjEB72r4Ko4Tn1ltlpxXf7GHu7jvE9sRelgUyc="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "co", "version": "1.1.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.1.0", "dist": {"shasum": "ea07027c8f5230d400e662251924e52d8a234c6b", "tarball": "https://registry.npmjs.org/co/-/co-1.1.0.tgz", "integrity": "sha512-ALCXp3JfYebU7pLnvvtWypVFl7RG8Ic3VwnmULZEB7m0gJOe6ECv1gP9jnxaoJOWNamTTcJMqo41jLr8VAHMAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsjfXADGs7ODrcbM5nGRNmYKRY1HBu6RhowTdYxgRV2gIgEd0DWd2Ix49GRD/kDVuzDCAPexL/JPLh/rFSmoy08JA="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "co", "version": "1.2.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.2.0", "dist": {"shasum": "9af3eeef7c184eaad2456d2b016c0486270cc802", "tarball": "https://registry.npmjs.org/co/-/co-1.2.0.tgz", "integrity": "sha512-y8g+RDafkzAZFuBB/RjPJtxO8T9RUf4aXQovrWwq6Yx3tgf2QZQJ0rZMja8D8kmHDiPh1uzIk9CL+idp/WaEJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMjwp23pSDSDpnQe3zoW3NAF3lYE4/pH62i0CY7O1degIgUv+eP3UO9CM7aU3iRiUFopKbmkluGlw4r+roV5vBKWk="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "co", "version": "1.2.1", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.2.1", "dist": {"shasum": "4ba2b738479a1a33087a755bd3499cbf945a9e5e", "tarball": "https://registry.npmjs.org/co/-/co-1.2.1.tgz", "integrity": "sha512-6EcrIrhTj99c0sJ1h2/iS7cAvSUG7dmj8PV/i8YbNhNiKITwJ/pT8of+IRvToR3SR73jd1luH2wv8vNGGKwx9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvpYEA8TPMS/t8KKK2ZCxdzu6QNWmZBB0XrP2Gs8dkYwIhAJz/pBC6+eIkER+NTMlZ3ZHtxuIsgWhrb6jPT81GVclw"}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.3.0": {"name": "co", "version": "1.3.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.3.0", "dist": {"shasum": "560ba5696f2477b2ce344cb0a3385de4f0a4c4e9", "tarball": "https://registry.npmjs.org/co/-/co-1.3.0.tgz", "integrity": "sha512-oCM0k8+uMX7FBXCRLrMwzmtnNfTemod5UP5EeRh6cm8zlDnxKsWBXmyZxibmpexKqATHzTeUVB8bRLdoJWnd5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxYWV+wR7ima/8IRvO3Am0znpfb10ukPASvp3pyisdpQIgchF4IpMMqTD5fRasj/tRr32Cv2vukCfIsFQHBlKAiwM="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.4.0": {"name": "co", "version": "1.4.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.4.0", "dist": {"shasum": "3961dd2bae1f4666b60472b2f6712a37bc39482f", "tarball": "https://registry.npmjs.org/co/-/co-1.4.0.tgz", "integrity": "sha512-tciNUZE9so13XuovAKmu0jq8dGd2/w58R5e5493Wft/C/3fA72SAc7grkDD0k3NhA5iv0/Jt99CE3u01yZvEgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICUhrTXRDIAxXVSp+YLsNSMt2kNo89x9I0vR1ou+19ftAiEAofbrRQTKvoVNHUDBtNnoGD6wZQIHSjiAtsIHl3Cyx6s="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.4.1": {"name": "co", "version": "1.4.1", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4"}, "license": "MIT", "_id": "co@1.4.1", "dist": {"shasum": "ad24a5c0fb4f832f05b1d24438b812ef7a2600b2", "tarball": "https://registry.npmjs.org/co/-/co-1.4.1.tgz", "integrity": "sha512-rO9uQ0er6ZmW5upFPT36xHfrMnSkXy4g3lNrCN/8opBmMtCxSsfIcYcInEij8BufkcMZA0f4zhBl8LP11scoGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGSX+4Gk3Q5KBqRywab7Whm+dYUafy+ecgAEEKJxEdBKAiEAqvWejEUvik/qD3G4rPfkWAXd6j2kcV8d82raFKMJhOY="}]}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.5.0": {"name": "co", "version": "1.5.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4", "bench": "~0.3.5"}, "license": "MIT", "_id": "co@1.5.0", "dist": {"shasum": "18447ddd69697266d2bd6d56fb7cf59581fc6d96", "tarball": "https://registry.npmjs.org/co/-/co-1.5.0.tgz", "integrity": "sha512-6YSfcCBQ72lfyzOkxob6O3rfw1OXOsKwcPKCDLSfmK/31P+NczwkZ7wNcA/RCkdUrrkvhfN2gF0s7dqcrRC9KA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDtKO09awqmqRqkdjiq/Xtdpx9160y6r5+8A1j5E1q2zAiEA4bYEQDvoHEGU+uCXv6qRNmJ1rJGV0vcoaabCDmAl5AM="}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.5.1": {"name": "co", "version": "1.5.1", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.10.0", "q": "~0.9.4", "bench": "~0.3.5"}, "license": "MIT", "_id": "co@1.5.1", "dist": {"shasum": "920ee4a4ae5bacf108be61cbb1a1468089ef640d", "tarball": "https://registry.npmjs.org/co/-/co-1.5.1.tgz", "integrity": "sha512-FDe2w+zlX11c4q/BAASH8SZc/J2nnxd6R9nsT71xitCSf1pV+ljdWI5XSws1XXOM3v+RweGWXQllpEe7mwW8rA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaFSj7Ze4wsMQUoZVVzL9XCOAGrIrjfK+PeWsllmYq+QIgLwR1AtnU2RFXODpGDW1nRWzbB8FwCWPZkla3kf3RqOU="}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.5.2": {"name": "co", "version": "1.5.2", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "bench": "~0.3.5"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "_id": "co@1.5.2", "dist": {"shasum": "58c6d84e664fc82e363d12f36afbf3dc8d0b4879", "tarball": "https://registry.npmjs.org/co/-/co-1.5.2.tgz", "integrity": "sha512-LZ0TgZTLaiMGTrCQVFgsAWwN6F1u3lViHTxH5B8UY3BKLLpEIjMxuU80odftviPgwDQKpJk5P0UmEEZi0XBE2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICegIDDf5jURuai9vSckqcfixEQz5GWjntH4M8uuEzhhAiBj6RaQzJ5EzH4Nsez+6uY/lBfoM005ijD0MiHFHU41RA=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "co", "version": "2.0.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "visionmedia/matcha"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "_id": "co@2.0.0", "dist": {"shasum": "1761e3a6c650f53c22f9ac7ef6ea3b404789a1b6", "tarball": "https://registry.npmjs.org/co/-/co-2.0.0.tgz", "integrity": "sha512-wHQBxxSV0/Tc87VJtKgqqeAOCb6dN5OhC/G9k0ITmofbrkjoTzNuubl765kU/fa7uus+92Pi+xs5zcgumpzNlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICAdMzUwAaH1yO94jhnODF8SrXysXTzVunjvr8eYoVy+AiBwej0VHnDeUkYew17AAhLpri7KxOncvMRaIuci+N0frg=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "co", "version": "2.1.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "_id": "co@2.1.0", "dist": {"shasum": "6c21ed3acc2539f64840f31304bc20d240fb7058", "tarball": "https://registry.npmjs.org/co/-/co-2.1.0.tgz", "integrity": "sha512-VU4+vxdq4BLUPnB/U+m/hXxf/KOeHS8AZMrfH8i2xRmwunDX81GpfpwNvsFhWiodUrwFE3PXOghgdh2mFqG4LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIElLVl6BgIAbHFf11OeJPNlU+Unz19REIaYXXHv3RuYKAiBQhcgzS5hYCmZfqrub+eO70kibmTcui2rxYhetrC6NIw=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "co", "version": "2.2.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@2.2.0", "dist": {"shasum": "8e48dd1d23e523426bca4613b37161fd9a230039", "tarball": "https://registry.npmjs.org/co/-/co-2.2.0.tgz", "integrity": "sha512-XKaW5aL3FR/VV65l+TU84XFJ0mr8jNPZjmLKqqX5X3detQu+li2fZlJZ7sP6kteT73qVlmkuP739Nqb69afNUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIByLhwiK9LA3wkkGwtRwHsN3SBXe1g1+K5TEiWJHGYyjAiAxGAd2a1Q7mSz3UN7ZYnQVC7c/f7oA0uX6ykanUWcg+g=="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "co", "version": "2.3.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@2.3.0", "dist": {"shasum": "4b71a77a11806982593e73f60a51a89da26bdf9a", "tarball": "https://registry.npmjs.org/co/-/co-2.3.0.tgz", "integrity": "sha512-HWKyS0Go3CMY0H0KQVoSURR02Q7F6pmwkFeJ8xmo+QaGic4dDcUn4G202iIv2OQN1CCGjNAPvBobYBzjusgGXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFiDKJ4GOZLNf5oFUvY/XakCdIa7a0nbxZvOaNCqiPLzAiBtzrsFtuwGwxiNGlqR0s7xM2xxj6L2L3VRDv9FLCsy9g=="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "co", "version": "3.0.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.0", "dist": {"shasum": "402d52a16ad231c0a15741fabbbde5297b637a8c", "tarball": "https://registry.npmjs.org/co/-/co-3.0.0.tgz", "integrity": "sha512-W0qD4BrvHLkIQpq3vjD5xnSyl+eDEtMuM7qTsMGMxTLDB1oiS4mkfeRWG8SheBf6LASFkndPZbCRoWiHNfAf5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzqJRe29yxVxalrauCjiHIR3xMSILfMYYUhaQJta4OWwIhAJHdQRf4DzRaxWzDUuwt5y6MM9vglX692i4Hy+7+W+YR"}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.1": {"name": "co", "version": "3.0.1", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.1", "dist": {"shasum": "72cd4f7a67fd5adf740f18e8915435eadf9cb81e", "tarball": "https://registry.npmjs.org/co/-/co-3.0.1.tgz", "integrity": "sha512-yVjUaArXUbfmxM+YS7Sb9jJ5YLMX4XnSO7s2TQ5NmIF9FBFsmxKEwVPJUVeIUwIQD+UUG6eSaOUL9BjhvQDlrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBMzrgdoPJB4yiQ8itxNY4/TLvI+BZu44rzGsKsqlJGCAiBtUtdMLnrQLgIWkuG8UZ6+AD6BbNMXlQxqKMEirqJcTA=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.2": {"name": "co", "version": "3.0.2", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.2", "dist": {"shasum": "bcc8a0fe3ea37fc69b15805de593d2b0d01732ba", "tarball": "https://registry.npmjs.org/co/-/co-3.0.2.tgz", "integrity": "sha512-yZaPiYZPyV64BEKt+8LoAHP3+wUD02ZhbVIeU5MXy1gOlFkt5GNPzV/DyYXBu8LffSv7uIomzbTLkTBrYVbD0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkGfQnGDtvqkgY8cBkcTtErLv5/bP7JnkAJgkmp6JqiAIhAKeXFmLPt8OTHf+2mufT8dZl48jVQJe8Rx+eLCFlRMNG"}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.4": {"name": "co", "version": "3.0.4", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.4", "dist": {"shasum": "92678b2aa4e03e389f37a94ab62acbb632e0a213", "tarball": "https://registry.npmjs.org/co/-/co-3.0.4.tgz", "integrity": "sha512-+tEujhC0+emUzYNMalJmX8CZTxvF4QV72NWLnf1qyGAUNXaLE6D0aY+nJ29Ct89ZU9X5U5qOX7jvNEMGWHf3Jw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJBCxsDaE30YhdNoUQXclMZYyMD9xMv92jSxhFqE0x1AiA2djkRLqyD/H9BX0bx6hW4/7HYWu6xLyTLS36LLsfgpg=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.5": {"name": "co", "version": "3.0.5", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "0.0.1", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.5", "dist": {"shasum": "39e973d3c0f2392410a4c699a0be22f81096ee56", "tarball": "https://registry.npmjs.org/co/-/co-3.0.5.tgz", "integrity": "sha512-rX0yT9aD/KluXfTw/0xoORT6fh4GmF8rzjbVpz1qPvTxaumkYqX5agSKf3l1BzT3JkRtBzCrEbMW6MvTLMfrbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGiYTEWcfmW/ABu5hhXextPDSbHmTz0GsKhf66FvM5ogIgCBLNCS6Xxws1Jp3XxBkxhxjeFkUvx8o2vyKHJ5V2/sM="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.6": {"name": "co", "version": "3.0.6", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "~1.2.2", "mocha": "~1.12.0", "q": "~0.9.4", "thunkify": "^2.0.0", "request": "~2.27.0", "matcha": "~0.4.0"}, "scripts": {"test": "make test"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.0.6", "dist": {"shasum": "1445f226c5eb956138e68c9ac30167ea7d2e6bda", "tarball": "https://registry.npmjs.org/co/-/co-3.0.6.tgz", "integrity": "sha512-Vj29f/AYywpPtHPhN9YqC7yK+p3rfjv7l/mTu5iOtn89a7DdccD4MYQmfU6R9wGdLXwufDIV07+PjXM0taVKvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXYrZvmd4H6npXQxCsl/NEvfDEXtJJRUh2Rc8j8RB9ogIhAJWKbAqjsCqXAb5/FL0iTL3d8es3eEnOhkOqf45gr0Xx"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.1.0": {"name": "co", "version": "3.1.0", "description": "generator async flow control goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"should": "^3.0.0", "mocha": "^1.12.0", "bluebird": "^2.0.0", "thunkify": "^2.0.0", "request": "^2.36.0", "matcha": "~0.5.0"}, "scripts": {"test": "make test"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/co.git"}, "bugs": {"url": "https://github.com/visionmedia/co/issues"}, "homepage": "https://github.com/visionmedia/co", "_id": "co@3.1.0", "_shasum": "4ea54ea5a08938153185e15210c68d9092bc1b78", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ea54ea5a08938153185e15210c68d9092bc1b78", "tarball": "https://registry.npmjs.org/co/-/co-3.1.0.tgz", "integrity": "sha512-CQsjCRiNObI8AtTsNIBDRMQ4oMR83CzEswHYahClvul7gKk+lDQiOKv+5qh7LQWf5sh6jkZNispz/QlsZxyNgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtl5ekTzbpsY9KKRk+I4oVk5CRfmxGqsj4cCnujuBvZwIgQjxDkcGxxmvnrfkCDCKFgjEEYj00WpcRFXog/jR34ME="}]}, "directories": {}}, "4.0.0": {"name": "co", "version": "4.0.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2", "request": "^2.36.0"}, "scripts": {"test": "mocha --harmony-generators --reporter spec", "test-cov": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "gitHead": "b7edf32688f3e2493a24c34c9db289449d51a6fb", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.0.0", "_shasum": "ff2dd4c24893cbd96978dae957e12edf68c9b5eb", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "ff2dd4c24893cbd96978dae957e12edf68c9b5eb", "tarball": "https://registry.npmjs.org/co/-/co-4.0.0.tgz", "integrity": "sha512-FfZfFT1upA2L6ZL96Yt0XNB+sI/8IyCvy7NKBrm44e1ZxAJscPQP5Eg1OiwjLmrAzWw7vOPWJvwHhF2mqIE6OQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoaFQUdH+YEg3t7p2/GMG7OGlZyoJDaCt2qDvAw07dLgIgM5o2Axp06d0Z4SdowXut0Qy5k+w023CzAshEyONUUaE="}]}, "directories": {}}, "4.0.1": {"name": "co", "version": "4.0.1", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2", "request": "^2.36.0"}, "scripts": {"test": "mocha --harmony-generators --reporter spec", "test-cov": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"node": ">= 0.11.13"}, "gitHead": "2862556119afdf4cbdb47fc73b02cbf83cb576cd", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.0.1", "_shasum": "b48d635ed71000ce5391cfa74ec9317b06f1c584", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.11.14", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b48d635ed71000ce5391cfa74ec9317b06f1c584", "tarball": "https://registry.npmjs.org/co/-/co-4.0.1.tgz", "integrity": "sha512-LX7JPY6YBj5yz2h+L4DIBkq79azfbXI9Dfz9OaMBGp/ufYIKZeK7kTbG+9w37ywWxHzKaLxP6FRnNgJUvclQpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMktshOsbD8b3xYaMMN5hEg7Om7P/NnPd2kIJyOc9yfgIgJ3pokFNLNwjuMGTZz8BRvPnZk1xrP76KmVclWuvdZYg="}]}, "directories": {}}, "4.0.2": {"name": "co", "version": "4.0.2", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2", "request": "^2.36.0"}, "scripts": {"test": "mocha --harmony-generators --reporter spec", "test-cov": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"node": ">= 0.11.13"}, "gitHead": "6beb8354d38a9f8cad66ff398bf0502d1e4b24b1", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.0.2", "_shasum": "0b4c10119b16dc0b8e5141287f9e0ca0460a1822", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "0b4c10119b16dc0b8e5141287f9e0ca0460a1822", "tarball": "https://registry.npmjs.org/co/-/co-4.0.2.tgz", "integrity": "sha512-Y1ilpZtjZTpOmIuNIjl4LvLo07CkP7zjVGnMq0oP81O65GH7G12+eMU6XpY4TTSjqOE0Tal101MbBD7W3GMGfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGanAMbOqI+ziXwC2RynVnxsWu6pzO9wzfFthSZYs9BtAiEA8v8/HDEb6IqXFkxE/rmeXDCNNQaOZUZqAOAE+WVlgmw="}]}, "directories": {}}, "4.1.0": {"name": "co", "version": "4.1.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony-generators --reporter spec", "test-cov": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"node": ">= 0.11.13"}, "gitHead": "b1533f0726b7b556207ecaec9ebf2477e09742aa", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.1.0", "_shasum": "5a454a363865c7d4ae5553ba12a325a9c22bc659", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "5a454a363865c7d4ae5553ba12a325a9c22bc659", "tarball": "https://registry.npmjs.org/co/-/co-4.1.0.tgz", "integrity": "sha512-l1IUMdXfoxZ4cmEiEVdZFAhCqJ2C77Sbmz9Ij6xuXbW5HwaqAX9CwgU9FLqhyJFrek2PjCvWPPjEtENgM5ctVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmFppsCOUesfNSu+wkB1Aix8WbNJ+9nl4774p1GYCP2wIhAKhlcQnDh+sg3NmF8QKSVhciE613Z8bB90RZUKZeE42L"}]}, "directories": {}}, "4.2.0": {"name": "co", "version": "4.2.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony-generators --reporter spec", "test-cov": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony-generators node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"node": ">= 0.11.13"}, "gitHead": "735fd49a98f250124886f57b418d468709b5bbea", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.2.0", "_shasum": "c8d6ebea027746f61de2531ca697db568fc1171c", "_from": ".", "_npmVersion": "2.2.0", "_nodeVersion": "0.11.15", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "c8d6ebea027746f61de2531ca697db568fc1171c", "tarball": "https://registry.npmjs.org/co/-/co-4.2.0.tgz", "integrity": "sha512-4I+vL9mA599cZyPaMJp0vm2WRW4Fops2w/UnfJ0irSy+LSX/vddoZBy0kXGqpmagRSeRfFTpehR4XxtLSDi3Sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCy7s6LvkWgu830SdIOQLnyV8OKglGl3ruaIB5JYCGQiAIgDXwa4D0VmnVS2wdCt+KK4sNltwF57dIz/w+wK/mUwoI="}]}, "directories": {}}, "4.3.0": {"name": "co", "version": "4.3.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony --reporter spec", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.11.16"}, "gitHead": "0591262f791492d40e4f39a8ab415058e9a5f2fd", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.3.0", "_shasum": "e6337dd62090e5808e9a0502111b3922e6364f89", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e6337dd62090e5808e9a0502111b3922e6364f89", "tarball": "https://registry.npmjs.org/co/-/co-4.3.0.tgz", "integrity": "sha512-OJ/hNALy6r343MCht0xP/J1gPwl6/nu/R+LWHCqafo03csAbMKr5nY9B4y5mds6y255AIiY5HT0a4nvQTs094w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5K3PXgUJfgf9Cr/23NKD2fYugvFimwjamm7GKsCB7hQIgaY8euWfSyWPLlg8v868G4rQ8Bq7RS2zt0+vE8PMajzw="}]}, "directories": {}}, "4.3.1": {"name": "co", "version": "4.3.1", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"bluebird": "^2.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony --reporter spec", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.11.16"}, "gitHead": "f3003783cac8e496a124186164c9d7e21a57fa96", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.3.1", "_shasum": "fd7a7206e4a520885af9aa2a21b22bddb5020ff4", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "fd7a7206e4a520885af9aa2a21b22bddb5020ff4", "tarball": "https://registry.npmjs.org/co/-/co-4.3.1.tgz", "integrity": "sha512-KCB061OasdmysmSQy4wXosOTa9CL6mfokPIX6geMisxvJ55/oEuMd95/+XBsU27R2lTtO4ZP2Ruy18KXfqV8uA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBxaZUOA/AwhKybRqNVfEjeJtQ5U+oC+duvy6u0eOr+qAiEA3JGr5odEfl8KgMWDz14GBctcZ4/5652rTaw0tEU2Ozw="}]}, "directories": {}}, "4.4.0": {"name": "co", "version": "4.4.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "2203ee50656b2bba16f72ecce531baf346f75dd2", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.4.0", "_shasum": "09d5978f2e6d73ae661e0d87897b778721dd0dd9", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "09d5978f2e6d73ae661e0d87897b778721dd0dd9", "tarball": "https://registry.npmjs.org/co/-/co-4.4.0.tgz", "integrity": "sha512-XwkL6oFEl0YbaSoa2c37n8kUE5to35PoJSRAglpam1876VRbiaROmcned9dgzbah7JA6So2uMISdb/1zDfVtWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5RUbeiHZ3mQLjwR34xFw0OFjkhIABWIj3EOMzXRnoxgIgfsDtwo6rrYWahEvmfh1B33rTAYG7NvmtwKdfw/zXGdM="}]}, "directories": {}}, "4.5.0": {"name": "co", "version": "4.5.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "9d996f27b9bf62d17a870c8c4370a5c3aa3d31eb", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.5.0", "_shasum": "ddcc31b8ca05d2de6756a53dba88491652b427ad", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "ddcc31b8ca05d2de6756a53dba88491652b427ad", "tarball": "https://registry.npmjs.org/co/-/co-4.5.0.tgz", "integrity": "sha512-P9Mqx2X497M3ahGhSsdGJd7xvTkjynkmwGO8NSJl59KasNxxAojAWDNnYh0Z1/2xk90BEG58KgqhRTThYfdrUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBZJuzpbyzGcy6ZHpxPlU1zUujsHcluZrijJ7FOVkfoEAiEAvA0C3Zla2qUorzzzkYYt/xxaK3oFa7YbQi5q5R2S818="}]}, "directories": {}}, "4.5.1": {"name": "co", "version": "4.5.1", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "a3d6007df3354c940bd7af410e63f31e10c93663", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.5.1", "_shasum": "fe8023c0d9e33aa9dae71887661b82dfb663853f", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "fe8023c0d9e33aa9dae71887661b82dfb663853f", "tarball": "https://registry.npmjs.org/co/-/co-4.5.1.tgz", "integrity": "sha512-K7DIPvTmRyfmtizBAAq/j2a2i4WGQsvAygNCR3OeqrnmULZbhrKjR1k+VK6hpwsygYNjpa2eEOJer5qh7aRWfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKtofjPfptcsZPtxrJHe1OuW0tzkL41bpjlCjzEHgSQAIgWJxP4mXq7Aylscq2gYilf2MJySh6rj7mj1wDIC0a0yk="}]}, "directories": {}}, "4.5.2": {"name": "co", "version": "4.5.2", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "d8b7fda6a11e228e4fbf11f9f14a4bc02f82ef9b", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co", "_id": "co@4.5.2", "_shasum": "5584e85b96237fb037f10eef2e4fe9e2cca50996", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "5584e85b96237fb037f10eef2e4fe9e2cca50996", "tarball": "https://registry.npmjs.org/co/-/co-4.5.2.tgz", "integrity": "sha512-U60u+YLuPnogObtK1J8NvZvc/Y9bRDIUdNrysB/sIukiTO+WlVQEBFD8aDIYtbTZjkiqfyb3OOAyFfAMJSXAqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICHb+RiGDJmyW2QAX/uEOZNk3i4Hs+66lbl2DwwJnE7MAiEAuSSie2kVUeeyyn+UmM+KxqwVN0pYPt3/jsJ9zv4TE/M="}]}, "directories": {}}, "4.5.4": {"name": "co", "version": "4.5.4", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"browserify": "^10.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot", "prepublish": "npm run browserify", "browserify": "browserify index.js -o ./co-browser.js -s co"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tj/co.git"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "222a066375d2a7d69b5b42254d653205d10ad9b7", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co#readme", "_id": "co@4.5.4", "_shasum": "d6483359ccc4e24780db8f6697692c18e9c2478f", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "d6483359ccc4e24780db8f6697692c18e9c2478f", "tarball": "https://registry.npmjs.org/co/-/co-4.5.4.tgz", "integrity": "sha512-wOP0cIJ1cXRVKA7h+JJ<PERSON>jcipz4iLcnXsMaPk4Cf3QTK+r9IXlaHitTEwdDoU+90cUh9hJXn25GYVafsEdG13NA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAnX9tjcgn5MW+BYklIEvK+0h8WvNiYsFrrZkTEtmdSdAiB+Xe3vWzqUQ7cUWfqyP9n3I6Mc0iN/qP09L6H2L3+GEw=="}]}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "4.6.0": {"name": "co", "version": "4.6.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"browserify": "^10.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot", "prepublish": "npm run browserify", "browserify": "browserify index.js -o ./co-browser.js -s co"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tj/co.git"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "gitHead": "b54d18f8f472ad1314800e786993c4169a5ff9f8", "bugs": {"url": "https://github.com/tj/co/issues"}, "homepage": "https://github.com/tj/co#readme", "_id": "co@4.6.0", "_shasum": "6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184", "tarball": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQkJoohXk93sJ7ZCHaLUWzUMN/ns6fMVhX4TKc5TZJXAiBVZ7lO9NIiJ3bmZt5VRoZuuo17TzhpPpqZ+auKtGeG/w=="}]}, "directories": {}}}, "readme": "# co\n\n[![Gitter][gitter-image]][gitter-url]\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Downloads][downloads-image]][downloads-url]\n\n  Generator based control flow goodness for nodejs and the browser,\n  using promises, letting you write non-blocking code in a nice-ish way.\n\n## Co v4\n\n  `co@4.0.0` has been released, which now relies on promises.\n  It is a stepping stone towards [ES7 async/await](https://github.com/lukehoban/ecmascript-asyncawait).\n  The primary API change is how `co()` is invoked.\n  Before, `co` returned a \"thunk\", which you then called with a callback and optional arguments.\n  Now, `co()` returns a promise.\n\n```js\nco(function* () {\n  var result = yield Promise.resolve(true);\n  return result;\n}).then(function (value) {\n  console.log(value);\n}, function (err) {\n  console.error(err.stack);\n});\n```\n\n  If you want to convert a `co`-generator-function into a regular function that returns a promise,\n  you now use `co.wrap(fn*)`.\n\n```js\nvar fn = co.wrap(function* (val) {\n  return yield Promise.resolve(val);\n});\n\nfn(true).then(function (val) {\n\n});\n```\n\n## Platform Compatibility\n\n  `co@4+` requires a `Promise` implementation.\n  For versions of node `< 0.11` and for many older browsers,\n  you should/must include your own `Promise` polyfill.\n\n  When using node 0.11.x or greater, you must use the `--harmony-generators`\n  flag or just `--harmony` to get access to generators.\n\n  When using node 0.10.x and lower or browsers without generator support,\n  you must use [gnode](https://github.com/TooTallNate/gnode) and/or [regenerator](http://facebook.github.io/regenerator/).\n\n  io.js is supported out of the box, you can use `co` without flags or polyfills.\n\n## Installation\n\n```\n$ npm install co\n```\n\n## Associated libraries\n\nAny library that returns promises work well with `co`.\n\n- [mz](https://github.com/normalize/mz) - wrap all of node's code libraries as promises.\n\nView the [wiki](https://github.com/visionmedia/co/wiki) for more libraries.\n\n## Examples\n\n```js\nvar co = require('co');\n\nco(function *(){\n  // yield any promise\n  var result = yield Promise.resolve(true);\n}).catch(onerror);\n\nco(function *(){\n  // resolve multiple promises in parallel\n  var a = Promise.resolve(1);\n  var b = Promise.resolve(2);\n  var c = Promise.resolve(3);\n  var res = yield [a, b, c];\n  console.log(res);\n  // => [1, 2, 3]\n}).catch(onerror);\n\n// errors can be try/catched\nco(function *(){\n  try {\n    yield Promise.reject(new Error('boom'));\n  } catch (err) {\n    console.error(err.message); // \"boom\"\n }\n}).catch(onerror);\n\nfunction onerror(err) {\n  // log any uncaught errors\n  // co will not throw any errors you do not handle!!!\n  // HANDLE ALL YOUR ERRORS!!!\n  console.error(err.stack);\n}\n```\n\n## Yieldables\n\n  The `yieldable` objects currently supported are:\n\n  - promises\n  - thunks (functions)\n  - array (parallel execution)\n  - objects (parallel execution)\n  - generators (delegation)\n  - generator functions (delegation)\n\nNested `yieldable` objects are supported, meaning you can nest\npromises within objects within arrays, and so on!\n\n### Promises\n\n[Read more on promises!](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)\n\n### Thunks\n\nThunks are functions that only have a single argument, a callback.\nThunk support only remains for backwards compatibility and may\nbe removed in future versions of `co`.\n\n### Arrays\n\n`yield`ing an array will resolve all the `yieldables` in parallel.\n\n```js\nco(function* () {\n  var res = yield [\n    Promise.resolve(1),\n    Promise.resolve(2),\n    Promise.resolve(3),\n  ];\n  console.log(res); // => [1, 2, 3]\n}).catch(onerror);\n```\n\n### Objects\n\nJust like arrays, objects resolve all `yieldable`s in parallel.\n\n```js\nco(function* () {\n  var res = yield {\n    1: Promise.resolve(1),\n    2: Promise.resolve(2),\n  };\n  console.log(res); // => { 1: 1, 2: 2 }\n}).catch(onerror);\n```\n\n### Generators and Generator Functions\n\nAny generator or generator function you can pass into `co`\ncan be yielded as well. This should generally be avoided\nas we should be moving towards spec-compliant `Promise`s instead.\n\n## API\n\n### co(fn*).then( val => )\n\nReturns a promise that resolves a generator, generator function,\nor any function that returns a generator.\n\n```js\nco(function* () {\n  return yield Promise.resolve(true);\n}).then(function (val) {\n  console.log(val);\n}, function (err) {\n  console.error(err.stack);\n});\n```\n\n### var fn = co.wrap(fn*)\n\nConvert a generator into a regular function that returns a `Promise`.\n\n```js\nvar fn = co.wrap(function* (val) {\n  return yield Promise.resolve(val);\n});\n\nfn(true).then(function (val) {\n\n});\n```\n\n## License\n\n  MIT\n\n[npm-image]: https://img.shields.io/npm/v/co.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/co\n[travis-image]: https://img.shields.io/travis/tj/co.svg?style=flat-square\n[travis-url]: https://travis-ci.org/tj/co\n[coveralls-image]: https://img.shields.io/coveralls/tj/co.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/tj/co\n[downloads-image]: http://img.shields.io/npm/dm/co.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/co\n[gitter-image]: https://badges.gitter.im/Join%20Chat.svg\n[gitter-url]: https://gitter.im/tj/co?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge\n", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2023-03-08T01:54:22.721Z", "created": "2013-06-06T20:24:53.571Z", "1.0.0": "2013-06-06T20:24:55.133Z", "1.1.0": "2013-06-06T23:38:03.571Z", "1.2.0": "2013-06-08T19:45:46.699Z", "1.2.1": "2013-06-09T00:55:34.844Z", "1.3.0": "2013-06-10T20:43:35.738Z", "1.4.0": "2013-06-22T06:21:46.429Z", "1.4.1": "2013-07-01T20:35:38.141Z", "1.5.0": "2013-08-10T02:18:06.732Z", "1.5.1": "2013-08-11T00:39:21.029Z", "1.5.2": "2013-09-02T17:26:47.678Z", "2.0.0": "2013-10-14T23:39:36.083Z", "2.1.0": "2013-10-21T20:22:10.304Z", "2.2.0": "2013-11-06T00:46:00.108Z", "2.3.0": "2013-11-12T20:34:24.575Z", "3.0.0": "2013-12-20T04:04:19.381Z", "3.0.1": "2013-12-20T05:23:05.400Z", "3.0.2": "2014-01-02T17:16:33.430Z", "3.0.4": "2014-02-18T05:03:07.311Z", "3.0.5": "2014-03-17T15:52:56.713Z", "3.0.6": "2014-05-03T18:35:22.299Z", "3.1.0": "2014-07-27T19:03:17.172Z", "4.0.0": "2014-11-16T06:20:20.535Z", "4.0.1": "2014-11-29T20:43:52.243Z", "4.0.2": "2014-12-18T21:56:52.050Z", "4.1.0": "2014-12-26T21:54:40.324Z", "4.2.0": "2015-01-21T02:07:21.266Z", "4.3.0": "2015-02-07T01:59:35.740Z", "4.3.1": "2015-02-07T01:59:57.852Z", "4.4.0": "2015-02-15T01:14:40.227Z", "4.5.0": "2015-03-18T04:23:19.003Z", "4.5.1": "2015-03-18T04:23:56.031Z", "4.5.2": "2015-04-17T17:33:27.536Z", "4.5.4": "2015-05-03T22:58:14.979Z", "4.6.0": "2015-07-09T22:30:44.562Z"}, "repository": {"type": "git", "url": "git+https://github.com/tj/co.git"}, "users": {"forivall": true, "nickleefly": true, "winsonwq": true, "lightspeedc": true, "mvila": true, "subtlegradient": true, "thotjs": true, "kael": true, "qur2": true, "redbe4rd": true, "hemanth": true, "refack": true, "dexteryy": true, "norfish": true, "walmik": true, "wxnet": true, "pje": true, "llambda": true, "ikobe": true, "syzer": true, "fredsuire": true, "sherylynn": true, "icaliman": true, "ivangaravito": true, "webbushka": true, "standy": true, "magemagic": true, "yokubee": true, "marco.jahn": true, "zolern": true, "buschtoens": true, "adamlu": true, "tchcxp": true, "minghe": true, "manxisuo": true, "phoenix-xsy": true, "goodseller": true, "esp": true, "linmic": true, "barenko": true, "duanlinfei": true, "qqqppp9998": true, "rdcl": true, "itsnotvalid": true, "bransorem": true, "mortiy": true, "jpepe": true, "aslezak": true, "jueb": true, "edin-m": true, "f124275809": true, "freebird": true, "sopepos": true, "pensierinmusica": true, "lgvo": true, "vbv": true, "nashtsai": true, "pdedkov": true, "erikvold": true, "r3nya": true, "nickeljew": true, "cestrensem": true, "zetay": true, "kbakba": true, "mkany": true, "romanthereader": true, "gvhinks": true, "fmoliveira": true, "mmatto": true, "softwind": true, "mdrobny": true, "sasquatch": true, "tehdb": true, "kcando": true, "yvan": true, "po": true, "silentcloud": true, "makay": true, "jmal": true, "pnhung177": true, "hain_wang": true, "ahsanshafiq": true, "gfilip": true, "vutran": true, "raelgor": true, "iroc": true, "hyteer": true, "xu_q90": true, "sivagao": true, "demopark": true, "illbullet": true, "ristostevcev": true, "lijinghust": true, "shaomingquan": true, "shan": true, "hypnoglow": true, "surfacew": true, "shanewholloway": true, "coalesce": true, "jmsherry": true, "jkrusinski": true, "garustar": true, "rexpan": true, "binq": true, "yuji": true, "programmer.severson": true, "octetstream": true, "johanliebert": true, "zhjq19660117": true, "ninozhang": true, "antfx": true, "farhadi": true, "javascriptismagic": true, "soulchainer": true, "xrush": true, "boto": true, "novo": true, "james3299": true, "fenyot": true, "hain": true, "bapinney": true, "holly": true, "magicxiao85": true, "luhalvesbr": true, "qqcome110": true, "goatandsheep": true, "akarem": true, "tonyljl526": true, "vectorhacker": true, "summer": true, "cwagner": true, "freaktechnik": true, "gggauravgandhi": true, "kizzlebot": true, "slmcassio": true, "turakvlad": true, "aquiandres": true, "ridermansb": true, "docx": true, "manikantag": true, "subeeshcbabu": true, "simioni": true, "iori20091101": true, "wangnan0610": true, "abdul": true, "quafoo": true, "faraoman": true, "deparadise": true, "jonniespratley": true, "icerainnuaa": true, "mjurincic": true, "zubi": true, "zoluzo": true, "yujiikebata": true, "lababygirl": true, "jlagunas": true, "qddegtya": true, "jrthib": true, "xtinctspecies": true, "spad": true, "dmitr": true, "rsp": true, "isenricho": true, "shuoshubao": true, "panlw": true, "leonzhao": true, "farskipper": true, "ab.moon": true, "danielye": true, "n0mad01": true, "aprilchen": true, "jirqoadai": true, "mobeicaoyuan": true, "kaufmo": true, "grumpycat": true, "wfalkwallace": true, "fantasy": true, "elussich": true, "garenyondem": true, "dyyz993": true, "kodekracker": true, "sadmansamee": true, "pnolasco": true, "sternelee": true, "mattattaque": true, "buru1020": true, "ferchoriverar": true, "fanyegong": true, "dpjayasekara": true, "pixel67": true, "hecto932": true, "mikedamage": true, "darkwinter": true, "terrychan": true, "stone-jin": true, "arnoldask": true, "cloud_swing": true, "stone_breaker": true, "liqiang0335": true, "bianlongting": true, "hoanganh25991": true, "nauhil": true, "wozhizui": true, "jasonzhouu": true, "techfe": true, "d-band": true, "vinbhatt": true, "superchenney": true, "iceriver2": true, "shushanfx": true, "iamninad": true, "andifeind": true, "xkr47": true, "anker": true, "vickykoblinski": true, "alexmercer": true, "luckyulin": true, "jprempeh": true, "subchen": true, "leonstill": true, "ganeshkbhat": true, "monjer": true, "sammy_winchester": true, "avivharuzi": true, "wisetc": true, "sunhua": true, "zhangaz1": true, "cognivator": true, "bicienzu": true, "codeinpixel": true, "tooyond": true, "71emj1": true, "flumpus-dev": true}, "readmeFilename": "Readme.md", "homepage": "https://github.com/tj/co#readme", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "bugs": {"url": "https://github.com/tj/co/issues"}, "license": "MIT"}