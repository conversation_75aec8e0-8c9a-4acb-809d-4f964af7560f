{"_id": "decimal.js", "_rev": "87-5ee56bb424d6bbb33936edf6bc680f59", "name": "decimal.js", "dist-tags": {"version4": "4.0.4", "latest": "10.5.0"}, "versions": {"1.0.0": {"name": "decimal.js", "version": "1.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@1.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "7a7bade13d3be2a5aa050c4453ce2b4c4ecf0868", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-1.0.0.tgz", "integrity": "sha512-8x6NsNCZGuqmkt2LHiEfAGoWxbjAPpc7IvSiSylYaKugxihUclfqt4lXrwcsNZ48RwX+jKOaWOkAYZZ313GcSw==", "signatures": [{"sig": "MEQCIDFL/zPo+aupu7d9Mpr1gwu6Tsfaud+Q4AlWv0/iepmzAiBQUEHsCnqaF8Pgj9QdzxhxP2sKiSB8vV0bjOkvNwAdMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v1.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "1.0.1": {"name": "decimal.js", "version": "1.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@1.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "54c7dc9947edd328ae1d7e9f744a84b7843b3486", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-1.0.1.tgz", "integrity": "sha512-lVRQ7L5QMvCQpPfbxZ93/fhxYbQTfYNRBNnJ6cqk40+Jsc7TykIKuDDMVoOTrTzhNZG5vBkladsZIkZJR+0akw==", "signatures": [{"sig": "MEUCIQDeqCh6/1PATlve+sXfbZ9h68dYQpDZPPbd+BE0YWKzCwIgSt2AXgYzKbfUN0qiwsrcJpAycVfdnEKdfh4/kLoU+7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v1.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "2.0.0": {"name": "decimal.js", "version": "2.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@2.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "417c3bb70f07292621e69508487b5a836bdcfd36", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-2.0.0.tgz", "integrity": "sha512-mEDguYQ+aSKfehxs1QQc5DCuHlTBUDv0QmwCRZsC83Pl8OzWhMvybn7GA/cc9blnVPfU1kHjNeKOHN4pqENSYA==", "signatures": [{"sig": "MEQCIDojEkwTImwGbHq/rOoqlislP6WYEuhI1tufYiKhDr5XAiBRuah+CFBIOetRDSFj7z/3JlCmVsp4qMsxdqmWfp4NeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v2.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "2.0.1": {"name": "decimal.js", "version": "2.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@2.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "ecc2cf67ccc67f9341f7e37602102ee4fc36c73c", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-2.0.1.tgz", "integrity": "sha512-S8HCGH2XgcMBg2CREWHwtrybDExFYbTfCeNmIDo/yIXJUNlvLhscuZ/s+HAGSZhzV5eyVsUh2Ggy9PqsxS0XCQ==", "signatures": [{"sig": "MEUCIDFzYaiQac6rdAViV8LWbINHSH6yck3WgoJdFECYRI8bAiEA2V80fKrwMPdLdiY/VZ39ob5KBKaJbWb4qCEnvJrCsQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v2.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "2.0.2": {"name": "decimal.js", "version": "2.0.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@2.0.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "65bbfba37d73320bebe189916b04061b75d760cf", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-2.0.2.tgz", "integrity": "sha512-BAZag70BJrsLUArDlfVCZL0aywhUSMfFOawvuRbkfFSAf6t/wEwbx9IrPx/sTt9Ro0u1N7ROXZ0GrhTluG4mPg==", "signatures": [{"sig": "MEUCIDKWD2eaE7mpPfsY2ZAMcwyGO7nHlO3ZF+LGIzub2H3yAiEA55mHX+yB88wpPNjCkDI+Y/tyGdmuxmTsjfweGyccyno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v2.0.2 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "2.0.3": {"name": "decimal.js", "version": "2.0.3", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@2.0.3", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "3b5e151f61f78f6305b169f3bcc0ec24418be6f8", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-2.0.3.tgz", "integrity": "sha512-BvzBg7ZJV/d+nE9MZJ9rO0vMdFFkZ/CyBU387lZgdGP1d6Gu0W8+maNwNlSvzpxt7tqkRly+akBRJ4jgZBsNAg==", "signatures": [{"sig": "MEYCIQCdfMahz5YO9r8X41lkJbGxdUqDCDUxpztOmrgoQ5kdMAIhAK4Bt2oY8k3bqGzFDF5dOcRfAxOEHqRjFFaW0vU+SZHI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v2.0.3 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "3.0.0": {"name": "decimal.js", "version": "3.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@3.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "ef20537863357492a29393c5ba21108e4af22d2b", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-3.0.0.tgz", "integrity": "sha512-b+2GbHZe/WU5UCNea5TpEhyAPklb274kaiGzGgY/YyGWQUe8UUA/R+xK2p7Pow7SdmSf5MnxU8KVO7ad4xyF4Q==", "signatures": [{"sig": "MEYCIQDBaXodsKzlXxsPXKrQRg3pcEn4GpI1K9hHmHCF/s4DJgIhANUDdUEPS/8+nQbJUnIv1NgrYQIV13u3qgS0Xr5x2RG1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v3.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "3.0.1": {"name": "decimal.js", "version": "3.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@3.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "4609a5a343f8b7af429fa76ea4a4c4427c6d065e", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-3.0.1.tgz", "integrity": "sha512-BizDPBekwjZ60Zv+fQyTzxLLdrPa0V+eirCwkSsVbDhx1XbplXzFWBcB6RZEH9QjKGRlLUxSxSugcy1Qmz2PGQ==", "signatures": [{"sig": "MEUCIQD3Fj6pLeqiUlsxTSQ9acXHmL4cM7CPP6FodJFcnGFkPQIgBOfjnnopI+H7/8eaN58mHEnXnGJBc2x4kxPtInAfQxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v3.0.1 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "4.0.0": {"name": "decimal.js", "version": "4.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@4.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "941942ed30eb72fb922b4400d56d7b591ce64cd9", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-4.0.0.tgz", "integrity": "sha512-tU6o+B7BRdPCK52AKjbngfeDHJuLesiGCh/TcIT9rHe68wbScpg7VfQ7ddfr5MUUnoi8bCcTJNcqrSbQR9pnCw==", "signatures": [{"sig": "MEQCIEXUNXux3Jffs4fSr6VErSdrY8GPV14GpmFQwHf0b56GAiBZPyyCa1Avqm0cvKUxt2WGkV6hWHGzx69Pdo7Bt4w7HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "941942ed30eb72fb922b4400d56d7b591ce64cd9", "engines": {"node": "*"}, "gitHead": "8d7a9969de32cdab3075bef0729ab6b050da3510", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v4.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "4.0.1": {"name": "decimal.js", "version": "4.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@4.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "7b15e933f4294219e73d7b01123e970342a1a8bf", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-4.0.1.tgz", "integrity": "sha512-TT70KaGSktNV9t6zGx+xn1ROAmM2M/sQ+X8JvW/l9A8FGHdbLFydEYL6ayTjuaZ2PXjir/uT5QhhoysiuLs4ug==", "signatures": [{"sig": "MEYCIQDs5xxKuvs2/NyB8CjDClVxQL2Seaz4nz2W96iqwtrXOAIhAOxjRX3QA7XGswtV897e0HBPi4QqNldl4WLDhIgeCAeU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "7b15e933f4294219e73d7b01123e970342a1a8bf", "engines": {"node": "*"}, "gitHead": "d136e91d66125171a0452b95123b3b455f472900", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js -c -m -o decimal.min.js --preamble '/* decimal.js v4.0.1 https://github.com/MikeMcl/decimal.js/LICENCE */'"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "4.0.2": {"name": "decimal.js", "version": "4.0.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@4.0.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "3473bbfaa66d5ba48cb576fa9e2cd2065ccaec98", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-4.0.2.tgz", "integrity": "sha512-hUqqVTEE/tioDxuY5+NNMOJaqxESWsZqcRKncyMlhHhQbh+DbzJvwbb3HJUzbien6XIrC1DW63uSTEFqnPlJ5g==", "signatures": [{"sig": "MEQCIBEwfeQPggz8Wzp9MaSqTSZrc8Dcb0/ItQee4lGsR/2vAiAgApAWI5x+0cvjH14bwelVTqmEb5Hvk0xEw5XBjC0r2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "3473bbfaa66d5ba48cb576fa9e2cd2065ccaec98", "engines": {"node": "*"}, "gitHead": "aaa0794f69cf8cde935048bc75ef9c45097d03b4", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v4.0.2 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}}, "4.0.3": {"name": "decimal.js", "version": "4.0.3", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@4.0.3", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "ecbe1f21570d602e75e62a118c700e314a77dea0", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-4.0.3.tgz", "integrity": "sha512-rIE5t/a8K1HOu5nwKPp62eQcXuEuWjYba9im/J2x8g5kIXeAE4nqVzMyhNxMQjGO8s4mmuzv65YJzA+PTyirvA==", "signatures": [{"sig": "MEUCIQCvNL+ewNuJkNeRFvvgHZ4LnjrZHj0cKXU77PK47mZHogIgOAztu+1DxRYrOLxz8ZOL9njUyD8fBq6aUK7pkNANofU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "ecbe1f21570d602e75e62a118c700e314a77dea0", "engines": {"node": "*"}, "gitHead": "bc66aa91a5104408f14cec147fcc4c847c8f8e73", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v4.0.3 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "4.0.0"}, "5.0.0": {"name": "decimal.js", "version": "5.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "07d6ef69299ca27ebd8edfca5b0758e041f32311", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.0.tgz", "integrity": "sha512-y0U5cbLj+zQfYOQwS4PypPSui/u4uniRnb99NtgImf/Pu9+1X5ZaJrfV2wPT6b3tZ1ltnqL7GVfwtlo7K1EpgQ==", "signatures": [{"sig": "MEUCIGuaX/i7wGLRX99vDbkH4Kr2+so7QKVimpOgLeDu4h3iAiEAuA1Na0HE4QFNAfXJSNhDLLkhyLLcHb+fJLysGNSWLyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "07d6ef69299ca27ebd8edfca5b0758e041f32311", "gitHead": "8a6f8fe412fe8ace311724a40b5d59f0343b5f9a", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1"}, "5.0.1": {"name": "decimal.js", "version": "5.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "350223e8134659ae53409e4fe78151631c0ad695", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.1.tgz", "integrity": "sha512-qx0xaMt0umPvHNamP4Et2bKEqlbeuNYLeUCN7b3T+LvLoDRcgBVqoyAM4ydyabj5j45lKkO5+HfZiXBrZbqeOw==", "signatures": [{"sig": "MEUCICQd0euN6SonXfw5f3gcMmUxXOlhgCdW4SIbwCcjeWSVAiEAubDmPtGwY0l3w+NeEclsxWeAGr4a+fDWxrw49uS+0Nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "350223e8134659ae53409e4fe78151631c0ad695", "gitHead": "b8b2ed7a2b0bde8fbbdc543b2f6eed98f1a0c7da", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1"}, "5.0.2": {"name": "decimal.js", "version": "5.0.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "17ba5343f9d57c495f571166fe728add7569f65e", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.2.tgz", "integrity": "sha512-kB5yKzH3+wxTccgJPTSbatOmlRRl2x7ydsU4bVipNUJsUntAzwYByC6GwJThbNTrzRba6CTtJioZUEs7z4TjpA==", "signatures": [{"sig": "MEYCIQDs0z9APa78/uH5Legc5Q+TW/yKxnQ5y+sMZfb1tfKj3gIhAKqnRAnHoyLCZ0spWWL3ZYsvziqYtOMF6Tti+FiVm1gW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "17ba5343f9d57c495f571166fe728add7569f65e", "gitHead": "587b9fe82f9a20dcebe8280a31030f9bb5a8c0f9", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.2 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.2.tgz_1454630921176_0.2635290625039488", "host": "packages-9-west.internal.npmjs.com"}}, "5.0.3": {"name": "decimal.js", "version": "5.0.3", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.3", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "6a4dfe4d79559d6e7118707dd54f41b36460416e", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.3.tgz", "integrity": "sha512-MOmfuA+Nlvk9Q5JggIo6otIBknC1R6b33xqrpjjNulYHdfCvhQYR+L8ms/i2YvRGqzgjplsXsqkcJKK0LhYsvg==", "signatures": [{"sig": "MEUCIQDhRsb1PamYIVTZfE8Tnf96DOHuXOT8rl9u4J+IACiTEgIgTeOuq/QY7AzAv/VH4Mks5gdxD7d4BVmM/5StM0tS0o0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "6a4dfe4d79559d6e7118707dd54f41b36460416e", "gitHead": "ea61253cbcbf56490c1dd5d609e3864e57676b33", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.3 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.3.tgz_1454781279493_0.5571386788506061", "host": "packages-6-west.internal.npmjs.com"}}, "5.0.4": {"name": "decimal.js", "version": "5.0.4", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.4", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "a153dadfc6e30aaf8be6f49dc46c45d2f6b74cc4", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.4.tgz", "integrity": "sha512-LgZ4+QjC5dw3I2tbwaSxsLzKN/XicsVmjEIbtGaAOQrfcCOZHdS94oDHUSCEsrpA69U8XHBDlj56O5Z10I8HEA==", "signatures": [{"sig": "MEUCIByZFGZKDkYBc0UDa0OUg+AF2Cvv9DboHdKtYP20b1AiAiEAt+hd8oDwkxUEpMphofllOsVjND5faae0Bj4Ba/5z1Mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "a153dadfc6e30aaf8be6f49dc46c45d2f6b74cc4", "gitHead": "50f63bec5c03f6b2024a8979cb543179fd1ad678", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.4 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.4.tgz_1455484596985_0.6469057672657073", "host": "packages-9-west.internal.npmjs.com"}}, "4.0.4": {"name": "decimal.js", "version": "4.0.4", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@4.0.4", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "af3249465e133988c30750f77eaaf44505caa5e3", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-4.0.4.tgz", "integrity": "sha512-RAXuxUu5ylfK3qKjeLfYMsCbTOJbNzDHzGXduBG9kS+R3hUq2z0aPA4X0DyqZXr4lLj6T3YwU9ih/xXaacqQnA==", "signatures": [{"sig": "MEUCIHX41NHy3X6klzzpS0MQhX6gr8lq9idmQnIY5c8BLS1jAiEA1zP0yFFvZoBo75rwejCNqyhZHNOY6Ln15D5gYWyLbm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "af3249465e133988c30750f77eaaf44505caa5e3", "engines": {"node": "*"}, "gitHead": "b060c291844e4435e35816d010d88d1fbd21cbb7", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v4.0.4 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-4.0.4.tgz_1455832333045_0.4148755129426718", "host": "packages-9-west.internal.npmjs.com"}}, "5.0.6": {"name": "decimal.js", "version": "5.0.6", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.6", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "3dab1be1c30fc3ba7947e81d12a623e7bc9f8466", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.6.tgz", "integrity": "sha512-d1CjYQB8GT4F0ilTnyPJCqIRF33exZ2h64nlOxhe0ya4jvvwsT+hvAA8+gud+uOU2bpmOEh9TKIs7QOlklxp9w==", "signatures": [{"sig": "MEYCIQDJe/D/VhivHwMj0MuULItjgKuDtmGHWQQbWTp6V0lJhgIhAOabNrTRrnbYwIVVH7vCmtHclhcQmhhC5Zph+hBSpoSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "3dab1be1c30fc3ba7947e81d12a623e7bc9f8466", "gitHead": "6a663287f088de62aecd81e4ac56342ea7462143", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.6 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.6.tgz_1456253218687_0.6440202121157199", "host": "packages-5-east.internal.npmjs.com"}}, "5.0.5": {"name": "decimal.js", "version": "5.0.5", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.5", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "771ba0367299eb1b3f65a7dbc50ed1da00c3a5fe", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.5.tgz", "integrity": "sha512-G11fN/jqb8Y/EMGDhmLce6exJk47ABLnPsjvexjRcepL/8p/DbnWFzB0evLRPKoYGZVZUhUNATwHad20VnCEsA==", "signatures": [{"sig": "MEQCIFZW35fn/2hUDz6R4ZETCAV/rKLsVdifL/ueytcj23piAiAuOyealbD1ysdbTnaxMneTHB0dQGulybVaptFZcDcRBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "771ba0367299eb1b3f65a7dbc50ed1da00c3a5fe", "gitHead": "6effe2db278318c639beba3c10ce6eb0decf32b6", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.5 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.1.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.5.tgz_1456253904712_0.46634548786096275", "host": "packages-9-west.internal.npmjs.com"}}, "5.0.7": {"name": "decimal.js", "version": "5.0.7", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.7", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "d793ed763048b482ddabc43730e4177b07b81df5", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.7.tgz", "integrity": "sha512-VHbcx2JjQaIwrP0TS1WmOFHLZM/2dt/AqdKogIgkTeHV2ok07l1f7BD2XK3h8aZIEAQAdGul7D6SGPtk9cKdeg==", "signatures": [{"sig": "MEQCIDxxkzXUow7lzUkCS1mfExMTk+fwTCXSD3ESa5W+HpiJAiAJ/nNGDYs96C3yaaoJVksJpN2nk1sOkqXbSS9tGi+/og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "d793ed763048b482ddabc43730e4177b07b81df5", "gitHead": "6785556026b80065e3bbb18bbee0e55dd48d44ec", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.7 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.7.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.7.tgz_1456774227977_0.9199237092398107", "host": "packages-6-west.internal.npmjs.com"}}, "5.0.8": {"name": "decimal.js", "version": "5.0.8", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@5.0.8", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "b48c3fb7d73a2d4d4940e0b38f1cd21db5b367ce", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-5.0.8.tgz", "integrity": "sha512-AgxkymD2GaBeif9DrYv2kOGvVmWKWuQr1/TQz235oM4Gic2vo+QfQl/cH4pMBMcpmrU1lyS+fjHvjNdV5MaABg==", "signatures": [{"sig": "MEUCIC43mcRO6S1N1Xdmjmj2gek7r7sqBUepPo6jROr9Rk7ZAiEAlUc2uxgwm1AGB0Db47R+wIagB2CjzUgy0GunB60eoxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal", "_from": ".", "_shasum": "b48c3fb7d73a2d4d4940e0b38f1cd21db5b367ce", "gitHead": "8a631cc3271f97643a6ba401c1641303a17dd3f4", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v5.0.8 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.7.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-5.0.8.tgz_1457564302596_0.7746151681058109", "host": "packages-12-west.internal.npmjs.com"}}, "6.0.0": {"name": "decimal.js", "version": "6.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@6.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "4fd58d0637f36fdbc1aba1320bc3f2cee83ca0f2", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-6.0.0.tgz", "integrity": "sha512-MYIXKG5PAI1x6k+nxMzSlDqIpyszPseO/YAJ1qdMryGB7daq/STOIkWG1PTWAQCvuxJ0xG30Jq8+c305uMTqQg==", "signatures": [{"sig": "MEYCIQDQjmdZnLEf19fIHwNWHh7RiRV5w1jmc/xWEMbz70X2KAIhAPdKDVA8Mu1l+yXmKi09fzS8N1q0L6vfQHvmzavUz0vY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "_shasum": "4fd58d0637f36fdbc1aba1320bc3f2cee83ca0f2", "gitHead": "fb37ca6bde56676d6d5a98df2bf8721e8fa81085", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v6.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "5.7.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-6.0.0.tgz_1467311784281_0.44673580257222056", "host": "packages-12-west.internal.npmjs.com"}}, "7.0.0": {"name": "decimal.js", "version": "7.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "b7d3d684163a3437b31d3643178abe1b619da7f3", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.0.0.tgz", "integrity": "sha512-sso1ZNDkRyW3UtiYEP22vyi6+FEMtFOfO5fsKuUaon2NN/isRAQ0E7M6LTjRbIi6r4vvRaDSBI6YmCWbCSJZQg==", "signatures": [{"sig": "MEYCIQDL/qFstWyKtZp3ZNLO/B0UYOH/YX8jfYPdhw9bMQQ9EgIhAOf5IAumSEBE44qnQx8OuToXBXdadlRVczE7rjR5szh+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "_shasum": "b7d3d684163a3437b31d3643178abe1b619da7f3", "gitHead": "e48bddf45ab07302a12e0e0d455cae125344347d", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.0.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.0.0.tgz_1478711907497_0.6797959669493139", "host": "packages-18-east.internal.npmjs.com"}}, "7.1.0": {"name": "decimal.js", "version": "7.1.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.1.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "348a65d3c708e641738bb391c97eefc15af10ed2", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.1.0.tgz", "integrity": "sha512-7CciWqHEqCvVUu6OFdnqWmAzSCTw2rTf08PmsSk18ABgukEqt44AUepJrJzz+BAljALydXyICJJPZzDZMjuJbg==", "signatures": [{"sig": "MEYCIQDxKefjdh+fbK0RIYvK41P9FeNAKXqmIXpIQQk2wGJicgIhANKjJKiQ/wlsVJZSUggQnPsRnCeRs4E5yhf0AbjnW0rG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "_shasum": "348a65d3c708e641738bb391c97eefc15af10ed2", "gitHead": "3e2d380a41e18b36254de926c03145ef2e3ccaf5", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.1.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.0.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.1.0.tgz_1478736107713_0.40735107846558094", "host": "packages-12-west.internal.npmjs.com"}}, "7.1.1": {"name": "decimal.js", "version": "7.1.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.1.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "1adcad7d70d7a91c426d756f1eb6566c3be6cbcf", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.1.1.tgz", "integrity": "sha512-JAYTBScouq/tbesTv17VT4/Zf4kOguK6LRp9lK8IasQpzC1u4X5z+fNIasI3Le+kBXpo83hyTp6asVgTHIDlGw==", "signatures": [{"sig": "MEUCIDQMDoDAoKV+rCveKpYpBN7sG1fNyhybm66jDrLriOFsAiEApHG8oiqHIt2IYZTsSbgHjVKQKOo1wEoNtILsBt/zg/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "_shasum": "1adcad7d70d7a91c426d756f1eb6566c3be6cbcf", "gitHead": "45eec0b8631758e0fc709c3a8ecfe5775e984e2c", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.1.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.2.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.1.1.tgz_1484076941466_0.4462222612928599", "host": "packages-12-west.internal.npmjs.com"}}, "7.1.2": {"name": "decimal.js", "version": "7.1.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.1.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "af71a1e2e89a1493bbd456ac746a1a92263a707b", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.1.2.tgz", "integrity": "sha512-CrmcXVFZprfGQZ4F0kn0u3GCiNfWAtjcZfAYsoA3rUW7fY3Px/ZynXWzKtrc0hU/x2VCt9EM//JVtDimmVOzag==", "signatures": [{"sig": "MEUCIQCfn3S6MPxJub741Sfv3X8sjaKjqOZxvGeXDIWQQdfNYQIgUXPU96o+TPPX1B5tnxPt9q8RmBXms+IN1+k9xLd0Lfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "_shasum": "af71a1e2e89a1493bbd456ac746a1a92263a707b", "gitHead": "85a499eb86959088f69b72cfefc044b927439d6c", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.1.2 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.7.3", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.1.2.tgz_1491415503570_0.7435064231976867", "host": "packages-18-east.internal.npmjs.com"}}, "7.2.0": {"name": "decimal.js", "version": "7.2.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.2.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "abb95d934f527664256ba213087f013f3cdc4fdf", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.2.0.tgz", "integrity": "sha512-mJRrcOls36faj+KN3jiXSyy2SeN2Pv7YYqr++4sLKaAbeinp6U0nwrrzMIbnIzuQqnUe4h8ZWmj2yRIoYNFt2A==", "signatures": [{"sig": "MEQCIASLY30PslO2lkEulJ+HFsiqW/Gz/efSDVG3kOA6XfT/AiAQw6wtdpkQlmJafUk6uL/JF/9cOkW+WF8a6Hb9c1Zteg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "module": "decimal.es6.js", "_shasum": "abb95d934f527664256ba213087f013f3cdc4fdf", "gitHead": "2be14dfdd74a8b992c58999824efc9abd58240ad", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.2.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.7.3", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.2.0.tgz_1491775718956_0.25847311574034393", "host": "packages-18-east.internal.npmjs.com"}}, "7.2.1": {"name": "decimal.js", "version": "7.2.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.2.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "5766c5424919b3a4dcfba10c34f32c73bc7d3ddf", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.2.1.tgz", "integrity": "sha512-o7TYWYG3qTbfQWndspHL3HaRdIvfGNXncXgoGhSD9jgTEgDZ21OfGGHytwkpGPwKL0CMIfYOtG1PdTw6XitZCg==", "signatures": [{"sig": "MEQCIE+avC09anbyJ5+rbYWdVlmUJNvuxROPLYl9h8WKa/7HAiB2r6bJPmHRRPeLb4skASL0DGTW0vk2hL8cgEhmao1DVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "_from": ".", "module": "decimal.es6.js", "_shasum": "5766c5424919b3a4dcfba10c34f32c73bc7d3ddf", "gitHead": "3566299686993864f1a795954f189cdfbf8e4ff9", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.2.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "7.7.3", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.2.1.tgz_1493925177397_0.2835980267263949", "host": "packages-18-east.internal.npmjs.com"}}, "7.2.2": {"name": "decimal.js", "version": "7.2.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.2.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "aff343b6d7ac14689657622789466ac70a5b7938", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.2.2.tgz", "integrity": "sha512-wNJTphn+waw28B9FWcGeF+sH4g2/S+ghuHrOHJdYE07Zcab9mQUPXs1x0dKpgEqFTVxwucmPRDosxZgM82vdWA==", "signatures": [{"sig": "MEUCID+jm12kKHxS9U1dkpbbKWI4ZopMHkThQP8mt1ySeG5TAiEAmDBuBZxAl4SXLQYnyHom6iNoZ2y7wsCjKf665gIwFZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "module": "decimal.es6.js", "gitHead": "32f0a63095c680bb56875fe7fe407611986a0d41", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.2.2 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.2.2.tgz_1498430500544_0.3914556864183396", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "decimal.js", "version": "7.2.3", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.2.3", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "6434c3b8a8c375780062fc633d0d2bbdb264cc78", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.2.3.tgz", "integrity": "sha512-AoFI37QS0S87Ft0r3Bdz4q9xSpm1Paa9lSeKLXgMPk/u/+QPIM5Gy4DHcZQS1seqPJH4gHLauPGn347z0HbsrA==", "signatures": [{"sig": "MEYCIQCazPwjnMKBF4vxvuhppwnpPDIYKFS9zfqDZ2BjkXxZwgIhAJQ9JZiNlgP7SZ5WkdQwKWZ/BGoZN86HGXTLrTjMVDo8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "module": "decimal.es6.js", "gitHead": "784a38a8dbe04f829bb2bb1499de1189f9740c80", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.2.3 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.2.3.tgz_1498597800866_0.17457267409190536", "host": "s3://npm-registry-packages"}}, "7.2.4": {"name": "decimal.js", "version": "7.2.4", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.2.4", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "794db1dd5e7071e132a59ced9cc9aad3cce2551d", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.2.4.tgz", "integrity": "sha512-lQ+Tr6VKhsHgfzkTLLqxOXDwMHj5ZBdThKsZZo0PeK9H7OOvS8O5hjCNogRElYrLIx7mc1pfN9RiLkZDpVdkHA==", "signatures": [{"sig": "MEQCIAfXaXfKQopQ9dTqDAuLHf0SgWWGX8WqDV4EXwsVSvM6AiBAcSKs/izI4XjUqcTc72sbsI6Yap5D9KPKTvvsGscRdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "module": "decimal.es6.js", "gitHead": "2ed8a040c7b644d20362c27976bb40402bdff443", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.2.4 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.1.3", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.2.4.tgz_1504979673164_0.3485030112788081", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "decimal.js", "version": "7.3.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.3.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "39f6c8dfc39385c3f3c540aec59f7d2cc1b93d1c", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.3.0.tgz", "integrity": "sha512-VVahlspSseBpHAPbf9ofz4iSJNIpaPIzrNUnY3NUoNkQQ3+0/Uh8+Jndz8gzRwOXWKwvbS556lcjqucj8f3hMw==", "signatures": [{"sig": "MEYCIQCb8vg28e/6n93vtWEU6RdvCgPjvNlBf12CP07FQJK9aQIhAPlqA17hOy5GUVY40ljErw2faQoL8Xf/GfyWUYVVhsiF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "module": "decimal.mjs", "gitHead": "433759294a32b1daf97db0b154fecdfdd8395922", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.3.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.5.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.3.0.tgz_1506446677699_0.6502211999613792", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "decimal.js", "version": "7.4.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.4.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "591c87b79a9b4d1c686458821ec1cfcc528e20f2", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.4.0.tgz", "integrity": "sha512-KzTSy3MhbAiOWgE+Z5j51Ya2RytbxCdwgkj3V5tMIhI1J6d0teaQ7hM00i8OWOK6gyETMA8uVoOeK6fpubr/PQ==", "signatures": [{"sig": "MEQCIHnChEGaqUJ2FgbqaCeQp4I4yTfhsZA7n1L4U0ZXgNMfAiALawBqdYTQYTCawU3TKB6oKhDOPYQnSBfwdtNvsEK47Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "types": "decimal.d.ts", "module": "decimal.mjs", "gitHead": "a0b78b62752b98a0b8cec2e0787c068ec896c653", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.4.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.9.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.4.0.tgz_1511653541464_0.4658490668516606", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "decimal.js", "version": "7.5.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.5.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "de645490051880798ff336c8313ace809f674cfd", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.5.0.tgz", "integrity": "sha512-XW0hzhwepELhHeh33DGQX9x1D6lke2THPvTwt7ZMqhpl4+PkWhpXluyjkYwF5UV7kVHXpE0YQ7C3wAjW8+LfGA==", "signatures": [{"sig": "MEUCIQCJvEiCE7wSaBR4LdFSClSjabYMF5ZPn4cYeIiKJzR8tQIgV1f5khhs7mNDVwNtaBYlJtk1tRAd6WRmnmC+g4nty1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "types": "decimal.d.ts", "module": "decimal.mjs", "gitHead": "35a8cddde990fd34529185d017d9dd6b059f5740", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.5.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.9.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.5.0.tgz_1512324671267_0.5417363464366645", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "decimal.js", "version": "7.5.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@7.5.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "cf4cf5eeb9faa24fc4ee6af361faebb7bfcca2ce", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-7.5.1.tgz", "integrity": "sha512-1K5Y6MykxQYfHBcFfAj2uBaLmwreq4MsjsvrlgcEOvg+X82IeeXlIVIVkBMiypksu+yo9vcYP6lfU3qTedofSQ==", "signatures": [{"sig": "MEUCIQCRHpUjtHLj66VWozlcspOd61oOsNh/5mZIcqS/bLb5aQIgFki4bLzjoNGpaR7qFMyS20Wtkl6qLhXpf/R3UyA9Evo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "decimal.js", "types": "decimal.d.ts", "module": "decimal.mjs", "gitHead": "1cbf5ffb44a74f7183c58d39d434339ec209ee11", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v7.5.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "8.9.0", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-7.5.1.tgz_1512339738560_0.4849155826959759", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "decimal.js", "version": "8.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@8.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "f073424c59c217c62b1467b470970e921aea77be", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-8.0.0.tgz", "integrity": "sha512-/lNvPocnhUcDCpIVSjr2L12pD12b5XFCf8wfs6IU+8Tk8cvtOI8UqkH7sJvODZMCBue9jtXmb2Td4UidaGOt1A==", "signatures": [{"sig": "MEUCIQCYHaF7QccuGsF2T8i5ehDXQ+aSvELBibxCqGMJ0udEPwIgWGAYMk8cgylNqrBZXX+HVd9XxkhzuKPHX3iOrh6yhCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./decimal", "types": "decimal.d.ts", "module": "./decimal.mjs", "gitHead": "dbf681be4a13328f3e5d1ed6198397087e87be8b", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v8.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-8.0.0.tgz_1512931146075_0.25741464737802744", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "decimal.js", "version": "9.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@9.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "c76c7067bfb930eb221f5678d83521b5c5268722", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-9.0.0.tgz", "integrity": "sha512-PGAEksEbk/XXpVwKdtC8QqJe2tseGmpwGIoMDB6SBdq98JR4S46ZpA2jRd4AFY3SakMV+mPhm5knUniNbdItZw==", "signatures": [{"sig": "MEYCIQCiFmiNXpGx7REUwom7DkGrb3DBs/Fjiz6NJpztDJTW/QIhAIyb37aPWViFyjx3hrcRdS9Isc9+m0RVywifShTcokgk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./decimal", "types": "decimal.d.ts", "module": "./decimal.mjs", "gitHead": "dbba3d5b184f7574d86cf29f9312cd64a20eb0e8", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v9.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-9.0.0.tgz_1513247485867_0.9035261552780867", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "decimal.js", "version": "9.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@9.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "1cc8b228177da7ab6498c1cc06eb130a290e6e1e", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-9.0.1.tgz", "integrity": "sha512-2h0iKbJwnImBk4TGk7CG1xadoA0g3LDPlQhQzbZ221zvG0p2YVUedbKIPsOZXKZGx6YmZMJKYOalpCMxSdDqTQ==", "signatures": [{"sig": "MEUCIQDrbs6pFkGiYPNXt8aSKzXqRcCdWNuG1bdpCh7qtYlSqgIgJXOx/YZBphecwQxLhaS5ufn9nVCjnL027VlAWeHTZHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./decimal", "types": "decimal.d.ts", "module": "./decimal.mjs", "gitHead": "875f6d0f7aea9e008eb3b6302fde890715f662f5", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v9.0.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/decimal.js-9.0.1.tgz_1513334362593_0.6519628006499261", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "decimal.js", "version": "10.0.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.0.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "f24855f7ee301d0dfaa458df0fdf4d5aaa182fdc", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.0.0.tgz", "fileCount": 12, "integrity": "sha512-oTJ6FdBXq28TSDB/e0CfuXWxZE1+Jj3vV9+6eL73d+YpryRqD/+R4O5kAmZjNHiL2zw9lXrnZR8NhJtualzufg==", "signatures": [{"sig": "MEUCIQD1+VDF/K0RuNgJXcqzll/6chrB4sb3H1idSEB9YdLyJwIgaJOzaUi0rv10m/v82UQfsVLiMmk1mvuNsJZOA0q2WSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485782}, "main": "./decimal", "types": "decimal.d.ts", "module": "./decimal.mjs", "gitHead": "2e75623cc388df7f231c455046c4b125fbf46f51", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v10.0.0 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.0.0_1520722259798_0.13072430429983872", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "decimal.js", "version": "10.0.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.0.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "d04b16b277f0f9af09671cee225c4882e8857c58", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.0.1.tgz", "fileCount": 12, "integrity": "sha512-vklWB5C4Cj423xnaOtsUmAv0/7GqlXIgDv2ZKDyR64OV3OSzGHNx2mk4p/1EKnB5s70k73cIOOEcG9YzF0q4Lw==", "signatures": [{"sig": "MEUCIHN1eBiCOkx8xyXBmtNw1sgaWku1M2mRFX2P9VbaLoveAiEA7uxoyKcj5KqrG9mIk8MLAoIzFtPldj0sWQjbbrj1zds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBt5gCRA9TVsSAnZWagAA6UgP/37d75np9XvahaadGlWJ\nk5/va7dfAFcoegIpbEVkYPEgDJWvHNxyXiIJH8udAAUmMZ1UtyEYhK4Y9lI/\n6uORaesWS5tX9f0SANrHSISqtRGs1uuaA7IwRR4UGHHmg3Up4jqH2CQ2ZdOS\n8bFQg0Eg0+pmY155diKUCzmsQ7I3HbwLZYxHut2I+jchJIPtZOonJCnAey6S\ncpjIBsBA58K+VnV9a0QfrIMJkkCv+P/N4OBzM/PdZ1Y7cc9N5ZGyQYNNXKtO\nmMhcQBd2Y+KjkgYFWbp+/q2jDSnfOVe8aSEi3HDeZEb5nUIxz24R4l5mb70c\nGEOSNxMyNBB0nqB8tVnw6dVHXo2Z7qfHx9/aqGDSYJRdNFVdlMTA8Jsd/8fq\ncP8ViejoGRfo6tJz7DchDczGXudyYPSHRTrvPkcnkfRxFRm2pr7Ikz+hqpAC\nr5/Prmz/MMzUjE8fbOrONjS+cgRJnejabop5qpET5JJQo9KwDiMZXHdNN3J5\nExcgBp90gfJU2J0BeWGj0Q0glNgaddPE2wu3rFZl3rQvxGxTuvsPyapBkWFw\nG8RYbpxY2N3Rh2xMXx9T00cNxAalUgTgiviWfrUZaNw5i5e6DcbM0zX0oDg5\n7ciMmrLuxi57KC6OYs+bzwyDud7GLi+vCACXg4QJvSKfR5wUiVT0wqYDVf19\nMf5l\r\n=a5m2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "gitHead": "bf504624f8e7b83b84bed5d202a7d6664292f47f", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v10.0.1 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.0.1_1527176799511_0.25891742624104497", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "decimal.js", "version": "10.0.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.0.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "50fd0f9184139f543e5b84c97681534292deb640", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.0.2.tgz", "fileCount": 11, "integrity": "sha512-qL5tUTXAWjB5cSBfm0V2a4jO5FaDLumCfwc/0f7WaTOT3WU8pIeq2HHrd98eXHtbey4qFWlaPzfml1JWIoO9TQ==", "signatures": [{"sig": "MEYCIQDAXzVjR0WvJvVNf5SD0rgCYVAhiNReqkslDmOQi3bWHQIhAPa+Evd0fmQ8rQTA0ELuhDj3Ye5rC2WkpGNEH0PClBLf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEqUvCRA9TVsSAnZWagAAjzUP/2E3HjHn3ZO6eGfJo1Yt\nrFhe0Tf5Wqto035JZGpXrtAYzCtqemyW8CL6DGgAHyxx6MyvEIQR224dD0Jq\n9ILliEZughQe0JuHM1QgcPkkvaEDdNK8yO8boQzuMllQsapioJYO+xFBYz5e\nYwZvWCZiJBGye3v52CwSkwM0Q1taoZsgI/NsNMlVu/BnXr8CuUx5caLy5/RH\nHFfydacnExnjSlFfhpTb0uZBB3VoLnHnj1Re9xrVJofRFKRYf3mYc9n2UaCq\nlyrkSdy31FgwAjXKlAMt6SJEmsgjkA6Pad7JY5zh+5DnAYrmapODmHOE1UO8\nChXY/iJdRq2kvaqs90N08YsokrX8Ti9W/v2dlmq7eYG4+cd/U1XEwjFTE7ci\nkxkmRwrxnqD7BkApPDv+o/+0mGPuTBLRR5mvjC/pvFMin7FRa0URuG1WcPFP\n4phJSEAe8Xjga+CH16F7eisg9hIaJJu/a63nwbCaKZfb0B7NsK7Wd6FhGzb1\nwFGsi0jJedf+cYzdZh4aRZZh+3/opFpcN4vESKSbiwLCr57mJ4Bg6ywmRbxw\njouPogSv5G9VaJukeVQnsBJ5OWWHPsC4Tw4jon0QZRAkxczbxHheLD1Wnry1\nTebSCYoZDJxVm0easHsHdSiKlprIzpM9uoisbN4E5x9YuSwsVDp9XgFCfl7y\nA0oY\r\n=C4Qs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "gitHead": "9106f0a010693ff4caee482c1f9f20c957278e5d", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js v10.0.2 https://github.com/MikeMcl/decimal.js/LICENCE */\""}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.0.2_1544725807193_0.0584404101812912", "host": "s3://npm-registry-packages"}}, "10.1.1": {"name": "decimal.js", "version": "10.1.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.1.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "fb541922a2e8fc618f5e6fbb474e281ed3d2a64c", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.1.1.tgz", "fileCount": 11, "integrity": "sha512-vEEgyk1fWVEnv7lPjkNedAIjzxQDue5Iw4FeX4UkNUDSVyD/jZTD0Bw2kAO7k6iyyJRAhM9oxxI0D1ET6k0Mmg==", "signatures": [{"sig": "MEYCIQCH8aozY5frANnntyGqVUfFWVSCMzsBlfxzgt0sJi5d2AIhAPNeaW61zBcYb/u8Z4ydoZb7346T/+XjVUmnCJFjojuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdmIqCRA9TVsSAnZWagAAYo0P/00V6MwuYaMsAqKGlWFi\n63WBb8ySSdsYai5+ouxHyvW+AhvVuR4PNkgVaege3G7MsN+7avJGdlTgLQuU\nmIFy9Ec1of2IVRVMWd3L6Q2TMJTNekvvJtPJPurt0sqW1I/7W1XRTD2jTNam\ntJUU6zJtA6ArA/zamGnwKzcgsZYkghsr+QA+drbyzk1PwO6LFrS35a2oG+t7\ni4J+YtE/CbfUGhOWEY0kHrVhEIXXUEJI9V39F/pPaj0kHM2dmb58jIK7Ie/J\n5ynSctjjcVCxiQhYOL5SZq893dsGIodECLQeEOKGKb0Gh6QBbAzbfJxNM0ML\nuatOcA8TxjvZVTZN5fHzuSVHenyN8n1PcYqbga+WNFriORCmCyWMThlOjhF2\nZlEyGCZ9ffgei5q7LzVcWP5BwOMvDnzAuW4+1tv0pq/EQa/rLwlhblgNq7Wt\nsmPKh1PArx5+06bo9uEwQlvGSAK3OM7UppyLsvKKMhTLpjuVi9Yl6twOjecj\njVvTMAG/JSvhFXxlvBmzb05uFlDuHaVnYJvBEKLW8nyjJaUUXhN5elFSP3c2\n+aftUWM4ZMPiyAhxtHX3ThJTgtgNlMQE0aPnVEUzXVfiXhF2hCEn9SqM6Psv\n7wc1+KhMO+Ag4l7rfxmD96IYmSZMoPvCXot09UzlKzzReeGb7bjqN+pFIYBb\nepL1\r\n=8dOt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "gitHead": "b041339e2a45ab9d97871c7838b19776fcef0b12", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map url=decimal.min.js.map -c -m -o decimal.min.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "11.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.1.1_1551262250251_0.3041117553504775", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "decimal.js", "version": "10.2.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.2.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "39466113a9e036111d02f82489b5fd6b0b5ed231", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.2.0.tgz", "fileCount": 11, "integrity": "sha512-vDPw+rDgn3bZe1+F/pyEwb1oMG2XTlRVgAa6B4KccTEpYgF8w6eQllVbQcfIJnZyvzFtFpxnpGtx8dd7DJp/Rw==", "signatures": [{"sig": "MEQCIA5Cs7LUWKRX6UgE0BGmd6CZykG3uVwyeniXsg60xw+wAiA3KIsDaULfCj0zW8y9EMWWHxRlcX9C2JSGNoxQtQoVmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0v+7CRA9TVsSAnZWagAA944P/iCJxZnjONsVGT8KRGJs\n+WED0ndex4mW6+vZiDVNI9YK7UCDiy56iDYkEAZLDUY6XSkkIotJ5O0OBGhQ\nzU7qx+5m/H4QB0RQFzcKV7Ra1qPlQSbSEzfOr3hpaqt3+Qaa7+mx3T2wGRPJ\n/6CfMUdr7d1CVQJrEe0/VKDLSuh/HOPVJpuN8O7pRxlzl7EPR6mjsHpMAckn\nolL4CpsYAMftJSGZGxQLEFV0HU0brntXAaZdM/XrpF/ez8iQKzwrd3At8PhY\nj5/YDRCzuLo+9wwxd9JxSQUNq5s0X/JRiloRU/f44xOHkQWNQsjRPT6ZYt/B\nk83dWffYJKz15GoIfIHJVQPr8Z06QdsHZ/H4OIXwYiIKta4rSKp3m69/Mkce\nXFDLdQmqnOM9y0DOdpftrx1z0OeJ2lD0U9DN/Z19aoP94rthyXIL1M1NKg3A\nKVe73Zu0adnoKt1o4emjLe5prg9hoxuQIBun65U6xxKb/ZEpvlao8+VguMAy\nQf8kWEGGX8g44CcPqgcmF/lbAO2zGfxxamc3GwHtk50oHzE3CS8qd+59Cqd/\n1KS++4Zk947WquvvivJ8MKeJWe3s14BxwGpdO9gI19ZPcQbmA2t6vOtPUmVj\nEl7RTj5Ei7ZY/oBn43uuoPT6g24Lv8quxlc3ukwJe4wDd2EooFGip58nlIgs\nyk/t\r\n=VDUM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "gitHead": "30828c01a6d12ec4afb05fa5c990c972226142d8", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map url=decimal.min.js.map -c -m -o decimal.min.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "12.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.2.0_1557331899228_0.5704580716550192", "host": "s3://npm-registry-packages"}}, "10.2.1": {"name": "decimal.js", "version": "10.2.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.2.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "238ae7b0f0c793d3e3cea410108b35a2c01426a3", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.2.1.tgz", "fileCount": 11, "integrity": "sha512-KaL7+6Fw6i5A2XSnsbhm/6B+NuEA7TZ4vqxnd5tXz9sbKtrN9Srj8ab4vKVdK8YAqZO9P1kg45Y6YLoduPf+kw==", "signatures": [{"sig": "MEUCIFq+RXFpmk/YkXdcxw1uerILNneSDThWqasyyhILOlmjAiEA8H3eBInEJEox+9VQ9QUsAypl5VTHZ/4uVrq85VoEqig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfclWjCRA9TVsSAnZWagAAXB4P/0VGmEUJpRtgbGgLuR1l\n1Ex3hM0r7hnTZxJZqSiRSRkqedXCN7FBgIp1BJ9ztPGPjnm/ZoZWgGFydyqy\nhz86jVhK3M+a/NVik+3XQ4Y+rVojs3qXVLhdB3NF4t0AInqGNHLnO0LTEMzc\n4OuFJm0OqX3E5oEuKdulSGxfpMtFsu31ldTgLPAgAqyTuq5R1dHCjIc44tyV\nUycE99B9bSbhU2Q2rCNSUv8gFrrO5OodwVKlG3TLrXsp2WSqT/Gh3C3dW58t\nHHCUekiQz+1cpVm4kY+6E4H2ux1g9FqUX6I3aye+l7tMB4sEWZh4qSvr2nfq\n1o2WkAygpvhToL+7/tuNn0cdtcBc6eqDfMTrU2rm67S8Bo1ypTybEWjxvOez\nDwgJz6l6W725ALrhjg3qJGs+dfb6XoazaA0tTr8PYUaWp5FJoivWRyzWlDWQ\ngONKsUrIzuD9SeXp9ALcqCTKu1Z3gVJ3UYkDYRz2QqH6MYNQiOgenWj+6qRN\nTQPP03SdjPJ9y6eEAsf/gIpt18d8nyxNl8xjM+TaoJHvGGnFhg/C/MmvdSW3\nVFLWeEN3Wduv6rIYHS6wY5ZNxdqWFREyS+fNhCeuMMPoVEV+pNTeZjXkQMYB\ngUZPwTW71V7tmQ642DKKMML5nn3DPETlwD9u/GXvj6w//A/jrIDJtJaVJx8n\n/C7l\r\n=m6Is\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "gitHead": "be7d6b05ab534cf1eda0a1d0a2558e8f049583fe", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map url=decimal.min.js.map -c -m -o decimal.min.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.2.1_1601328546559_0.1401542111119951", "host": "s3://npm-registry-packages"}}, "10.3.0": {"name": "decimal.js", "version": "10.3.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.3.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "96fd481189818e0d5810c18ac147824b9e4c0026", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.3.0.tgz", "fileCount": 7, "integrity": "sha512-MrQRs2gyD//7NeHi9TtsfClkf+cFAewDz+PZHR8ILKglLmBMyVX3ymQ+oeznE3tjrS7beTN+6JXb2C3JDHm7ug==", "signatures": [{"sig": "MEUCIDGtWAPvoEcV634CTKEKWVjZ62l1FMSjfc/jD5C3nQz9AiEApXXQ6CMUIDLshU3nCyNX9+cI6pC4FVOuScZlr6l2p0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0hHNCRA9TVsSAnZWagAAL0AP/3qVBBbyHbSS12ghLc/U\njO4KlgvzU0df9w641kDO3nGwM+mnclKuh5CIKbSLGodPE2+8jNgBpzKoG0FK\naKtwvs5+xqRIvYanb+gYOqm3EpS95xXoXBul0Jrsw4QyX0OO7f/XuRtY5NbS\nMBfpxfjB/teNrLsMnJNvkreIS2P9u8ueOrv0H6eOH0jII36zQyKS7zXfCP5G\nFVB5v1kT1E/4wxWyiPriuh55ygg2nLgoUjuLyQcvb0DHzg5R/Okq2LuPLCvd\nHR0gfZeLz3NokRkQj4PmEIC76lg8sdW0+ZB05dASe44/mAAXj32nk8EHT6tr\nqZrXZPBZBUqD2tAr4moaaXxkG0BK7Sp8bYSJvUW0BYt1JJNM6lPVPtssRynk\nGeW3qshPouJPjsKTmxpktrp+YVrpBBwcV+PLZj/PzK6rUSp7gxaCD4/XXroN\n4/tvVOKyZILKX4wqSq5mz0UY4qfJBbc5S2Qc2in4X33tGg7VQPE2B+tIUDKl\nr/f7wgoU+dmn3DJ09NKnncPgxkioMI765qglzek65rlkVYrpABLQs9UVC9k7\nK1dHMBQLWCOWv55zV4Na08UwPr/gAsbxcvfD/SBxihjwa8TA6FwtmXw3XB8J\njKebqXP7ddQf3MbagiYNr7/BBXN4kwNj1HIMijKMIALDmP7plhUY6YTbrUJv\n9QRD\r\n=NP6t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.3.0_1624379852373_0.7722375225684355", "host": "s3://npm-registry-packages"}}, "10.3.1": {"name": "decimal.js", "version": "10.3.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.3.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "d8c3a444a9c6774ba60ca6ad7261c3a94fd5e783", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.3.1.tgz", "fileCount": 7, "integrity": "sha512-V0pfhfr8suzyPGOx3nmq4aHqabehUZn6Ch9kyFpV79TGDTWFmHqUqXdabR7QHqxzrYolF4+tVmJhUG4OURg5dQ==", "signatures": [{"sig": "MEQCIF/BY7igPmDXZyz87+1WAAqeNATeKLH5xp4Ka1f3qqMkAiBovmVVvSNItccyC58ukhZ3X9YritGQmWXCiLS4ukrXyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1cUOCRA9TVsSAnZWagAA3DsP/jXzcO3qJ7tQoq8JCIFe\n0IdBcu/TNy+N7i12qFQnKUa38mizocCAo6JbnNOF1rWUzLa1gBOlzUpiAo9I\noOI2G712iIC6LFJN0nwypnPuCH+Y2AzwR00QG8milGcltFoC+lsiUQQOyuai\nl5ZwFKTqiPfIIMHJImq/0C09nuKwJJbeah7WR2fAHiCFXF3lENMAOjN8Nf+q\n+FkXQGOsnr5btBpWC09WYXOAzp7jzBN+SQoC0Wuh4F2s+fiPcHT+s1YftZnT\nxl5/cOYj4TIPBvTDlDHhzXgTnpkCmdwy2m/nfCn1BgscQrqQFvrSWNFXFLR/\n14xmVCygO8KGJxjXjHnEiAKNyKczWWb8dLwxcuIcqaCKPRdWg4U8ZyAe8t0/\nddP4CbvFuKWIDGoTfzf8Spzu0CYYZ+zD8+bVtpGzU8c7c0YVFnIEUD2LqiRk\nLB72UDVkDcqNfR297l0eiuug/2TtYLrwuus3E4NY9v4LUBiSUKJ9qcH6y3jp\nxUphvttx7IsbLcMHETeYxgnkvp5vKcqn9D9XJtMdEOU6kS5RC4wZoz3ZDK+H\nUJ81K/ai5KAz2XsuTwmCMyN6q479FAg7V6gwH8brljo955fT+6dO7uBurer7\nho8C8du/PDJ/kaP6SzRu8qcoT6ZJyT2SZqhGKRKqe63NubB5yXHu+KMrR67s\n0vx2\r\n=Q5JE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.3.1_1624622349976_0.33135225864703943", "host": "s3://npm-registry-packages"}}, "10.4.0": {"name": "decimal.js", "version": "10.4.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.4.0", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "97a7448873b01e92e5ff9117d89a7bca8e63e0fe", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.0.tgz", "fileCount": 6, "integrity": "sha512-Nv6ENEzyPQ6AItkGwLE2PGKinZZ9g59vSh2BeH6NqPu0OTKZ5ruJsVqh/orbAnqXc9pBbgXAIrc2EyaCj8NpGg==", "signatures": [{"sig": "MEUCIQCTHcfvqplzR6YYAgni9KSPFqukJCgANFh1PJ+U3d6IVwIgZM+Mn2DGDeRabO4qlzFmBpaejeq+1uCt+IgzptJiTMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+TDqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKLg//WaYajnZK+GBorwjsTmKhtxPV7aFdSOnDAIZ71m3a7VrkuXcJ\r\nwZiHNmfGEG0mSdzAHLarTEfOuJ1OFdYH9w2xcWDZH2FhidmGKwgjDP/ZBFg5\r\nLMQ3QT2G8+6rLu27bTrW8SZHSKkYf/bgs8iQarb2WnCUKniUkkwoLJLVhbfU\r\n4BeWwGucHiVVqzMS5E78OSt8jdAyFIr6gfF7KCM4KGZfzGCRZUA3fVUdaX2D\r\n8skUdiFgqyHPUQw+UFTtEFucyM/twQlHgdID9QxanClqvna2qlO+0xa8G0K8\r\ntaHyQktDRGwRmZPnzprRAbUegMA7+K55K2o/5X8ql13LZf7UgVguUEgrsF9I\r\nVF7KWHtrtz/X7oUMzBnSMKtsoDFtyTzeVzoRjXp6qkTMYBDeZkqILuH5zgqP\r\ngQpM1O4ebB9ZfLt4zKt0u1i4+rcTMojPhyGUHkEtf0yeC1Xf3sSBzR8GWsvw\r\nlwk8a95UX9Fpif0eOME4O509fJB5zUiEjtQIg8/8WuB60EaN7MrJrG21XUMO\r\nRQqmni+VUutttwIB8JYbjIACwZmkR7bQhG+1ihLTJE+WJyfjxbxOLh0Vuk+S\r\ntDnELFfrbFg/VxgGWdrinUnSsl/kyFahYyLIteGHy29BsRW72ZIl6kr+p6Y6\r\nMi2gpED5H0XwFGhtD5T0PEKDaAfIw6OfSTg=\r\n=Flce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "exports": {".": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal.js": "./decimal.js", "./decimal.mjs": "./decimal.mjs", "./package.json": "./package.json"}, "gitHead": "77a72453c6669084ca776e11a8611129c2d8e78e", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "8.16.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.4.0_1660498154495_0.42027963012712255", "host": "s3://npm-registry-packages"}}, "10.4.1": {"name": "decimal.js", "version": "10.4.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.4.1", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "be75eeac4a2281aace80c1a8753587c27ef053e7", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.1.tgz", "fileCount": 6, "integrity": "sha512-F29o+vci4DodHYT9UrR5IEbfBw9pE5eSapIJdTqXK5+6hq+t8VRxwQyKlW2i+KDKFkkJQRvFyI/QXD83h8LyQw==", "signatures": [{"sig": "MEYCIQCmjqxAClsJrekZWbULzqvLyMyQ+q8GkRwjtPGC8xvz5wIhANlnOf72QoiybSthvy6LDq5wKttPVI9iKBYqka2d5clg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJPMhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjCA/+NcN3aodr597Bd0rhMqlMXOhcfmmPrrv2gsPE9qQhN3lD+JRh\r\n+ys6jMz/65RTkyjCsRBp7F7LYQTlR4rOMMMRiu1w2NaTu6bt3NdyxxuixoTZ\r\nCL2ekcOjKSpHc+V+4F4eUemjsXBrfhDRh73rfzr2upnKsiMmDEuDTKdaqqMF\r\nLG+9+07YAJws9Kkeq+MYVT8zrX33PvvfN7Xh42bdKRzsqX1VI46CWMJDmGQK\r\nOngZh2BoTmqeD7gK6omowg2oMG4vlhUD11Yr5vL8Tv/aLFmbCK9irtwwmoe9\r\np6Pmqam2xhT5bAsdbtHedo4iGSrj8tszEe1NNk0ObXI3XHdrjfyXsNU0+ibP\r\n5sEQY6JLDazpKdB4iE+WtoHPuRbYBnLTlrZOvcUrifJ9cqWlVMDxZr6i02lM\r\nKa3twjVxnG1ADolDhKoaKZdXUo712pGnCTZhn7qhg5iVH2XQCKFHWEAO2hvY\r\nxKEa55QB+L4rfZeKC33+Uin1hL0dgqWwc3Dh2rgW52zq32S2y0tWegw4vc3W\r\nZSa7PemZTl47NAB6HNZsjLDSdyen0jWt4QaxYh3iNjOt1ZOsnuBAUPw+QgDo\r\n3Ksd/BE9zSgpD1xeCG3GtZHWGWuHtjCn+xmBwvhHmCCtt0KsqNAUhYtyJOQk\r\nWgqkACgYi+ZITrPplPWMZp9WILqMRngznFo=\r\n=fVRy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "exports": {".": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal.js": "./decimal.js", "./decimal.mjs": "./decimal.mjs", "./package.json": "./package.json"}, "gitHead": "77a72453c6669084ca776e11a8611129c2d8e78e", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "8.16.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.4.1_1663365921135_0.4327687749431728", "host": "s3://npm-registry-packages"}}, "10.4.2": {"name": "decimal.js", "version": "10.4.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.4.2", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "0341651d1d997d86065a2ce3a441fbd0d8e8b98e", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.2.tgz", "fileCount": 6, "integrity": "sha512-ic1yEvwT6GuvaYwBLLY6/aFFgjZdySKTE8en/fkU3QICTmRtgtSlFn0u0BXN06InZwtfCelR7j8LRiDI/02iGA==", "signatures": [{"sig": "MEUCIF+AB+ky1eBcqEXR14ZPtYqayPc8M5P8SpjGi4K8deT+AiEA5HPHIuAphhEsTf+4gR+OXT72kEcDLNkn4Zqn5Ls52Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRyFXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJAQ/+M/NX15yq5ruixKu4FE4Y8C/8sxvqegyDD4CRtK0/Hlh8+20s\r\n+guPAltIUEDPO3obi4QszFKvPzSBe1hv+rELEb7vZxovnAzkmFOJ2vypYfs+\r\n/TjEoW45JxMvy1O5gWJMUFa1G4IRX4dy7W7z3LxLY1WuimxxjhTU5JBvpTcg\r\nosU8fVXa+vIAgU6sA05Pe8I7TYAAzs2J4z5KHraDY0uv45LC99RsBkxSEZj3\r\nFKWbO12w8HiD6r4MOqmE7D1gmTIUqPmLaV3YTP6rptzs6c0TjHrg2hGE6b80\r\nh8x37eBP1JeL+edTlADy5NiyBzPI18lvOok2RyK8oRtSSx0Vm1eQ57k3eKN/\r\nZMEcbpMbflSRoHZuvUUDtjgbNgG7rGcBV+UnyPCWPZYvciAYo3VfHzzZ2egX\r\neO4SwGgk8C2qhdksNzI9MyQh2EUSSxhDO+EXUCOAzO6NedCp1iRpanUh/13X\r\ncah+owY7IPlzk9RlOp7zOkx4CAJOixRfJm0y44Hlv8+ftYpJsZYIeXr132E8\r\ni9/WmjTikg5XQpOwMmSl94Daoiuk6K94D4e8bE+c6IEsb4t4N2QZ1JTF6ko5\r\nuMuZrhDC2IZ+VYmUTxPJGEI5FPrKadWukEB4kWHV6GNyz8XdWi78Ssr52W2p\r\n94mX2txsm9qxGnxc82RTGOp5qWq/diuaCMY=\r\n=ViLV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "exports": {".": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal.js": "./decimal.js", "./decimal.mjs": "./decimal.mjs", "./package.json": "./package.json"}, "gitHead": "77a72453c6669084ca776e11a8611129c2d8e78e", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "8.16.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.4.2_1665605975452_0.4596367687307856", "host": "s3://npm-registry-packages"}}, "10.4.3": {"name": "decimal.js", "version": "10.4.3", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "decimal.js@10.4.3", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "homepage": "https://github.com/MikeMcl/decimal.js#readme", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "dist": {"shasum": "1044092884d245d1b7f65725fa4ad4c6f781cc23", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.4.3.tgz", "fileCount": 6, "integrity": "sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==", "signatures": [{"sig": "MEUCIQCcSY7jU780Q0BZLmH0LH/A6KOXjt7WOc55A1oWebnEZgIgHkaI+VEUcR6iLLBF68Dxm7Yevv+pRD7KYsmdrKpyQfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjJqDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTMRAAhyVltNzdyeoiO4VDndKMofkOCawGnnD6KoeNCzD1RdoUuKiN\r\neEAbLwj5klLVLxRHbfwGlIwtdRgvSanb71JZen3TgmSIlmdcfpfskDao8OQL\r\n543BxN3Ni/VaItQaS2SF8WC7RFCTDN4JxQz0F35ThnK0K+L2lMLuKgrJtiSJ\r\nohtseeSvZNdHfarS1mMiJueq6Ds6GgqdP3qHl2HhVHBRKsXYrpMfg+mQfX1R\r\n4MKtu7RFKSXFcOx319mPtDg+NMpBBJOxOu0gqP8qLmiToDxcNciifxHtCayL\r\n3z8arLofsrzIz66HFsBmLwbPU1dBUuPbrRkRz2FDqZh/MTmpWKK9c1VcDd9s\r\n8Lz1/zGy4uUk+lHUv45bfjDZfvXwplR8yGcaMT0zZXEyEUTg3XX6Iew/PTiM\r\neFxP2fa4pVhNdVrdaix/XHO3uPa0AEHsNGeWPCGRugYfkiyyHEosJ3lApmqX\r\ne38U/e31qYngKos/McEuzZw3it4jmzzEJLv7t5AUNoNDzwJIqMTUg+2hIzUY\r\nqn6AT5MmJnzOkGMDZxY8KmTyUq8SINAA835IaIGJQJG3u2kvWySrbdH6StRz\r\nIhucmo1vcH35h3T5JVJuh4S5KFrxi9ROCzQOIxrR10wgeKocUL+wHXfTlA0g\r\n0ijUf3f9NDEmpo7yv+/0oyYgmt+GuDRg0KM=\r\n=7I3K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "decimal", "types": "decimal.d.ts", "module": "decimal.mjs", "browser": "decimal.js", "exports": {".": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal.js": "./decimal.js", "./decimal.mjs": "./decimal.mjs", "./package.json": "./package.json"}, "gitHead": "77a72453c6669084ca776e11a8611129c2d8e78e", "scripts": {"test": "node ./test/test.js"}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MikeMcl/decimal.js.git", "type": "git"}, "_npmVersion": "8.16.0", "description": "An arbitrary-precision Decimal type for JavaScript.", "directories": {}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/decimal.js_10.4.3_1670158979624_0.21807814421629823", "host": "s3://npm-registry-packages"}}, "10.5.0": {"name": "decimal.js", "description": "An arbitrary-precision Decimal type for JavaScript.", "version": "10.5.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/decimal.js.git"}, "main": "decimal", "module": "decimal.mjs", "browser": "decimal.js", "exports": {".": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}, "./decimal.mjs": "./decimal.mjs", "./decimal.js": "./decimal.js", "./package.json": "./package.json", "./decimal": {"types": "./decimal.d.ts", "import": "./decimal.mjs", "require": "./decimal.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"test": "node ./test/test.js"}, "types": "decimal.d.ts", "gitHead": "f1ee2f404d6bf96d59c04db80c1f404742afa3fa", "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "homepage": "https://github.com/MikeMcl/decimal.js#readme", "_id": "decimal.js@10.5.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.16.0", "dist": {"integrity": "sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==", "shasum": "0f371c7cf6c4898ce0afb09836db73cd82010f22", "tarball": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz", "fileCount": 6, "unpackedSize": 284225, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDuB2xOukAodXHBUcVeFxxKsMCDnGHiyN7JaFY4EvVB6QIhAPQ9n2BHeGcN/On+ENkk5+7NkeFiCbvxobN80CSSX7DP"}]}, "_npmUser": {"name": "mikemcl", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/decimal.js_10.5.0_1737665611157_0.7228024853954513"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-04-02T15:46:49.397Z", "modified": "2025-01-23T20:53:31.539Z", "1.0.0": "2014-04-02T15:46:49.397Z", "1.0.1": "2014-04-07T16:19:41.478Z", "2.0.0": "2014-04-10T18:51:35.593Z", "2.0.1": "2014-04-10T18:57:15.643Z", "2.0.2": "2014-04-30T16:44:51.697Z", "2.0.3": "2014-05-08T18:33:34.392Z", "3.0.0": "2014-06-04T23:08:51.951Z", "3.0.1": "2014-06-08T22:18:58.284Z", "4.0.0": "2014-11-10T16:06:52.614Z", "4.0.1": "2014-12-11T17:41:30.596Z", "4.0.2": "2015-02-20T16:06:58.692Z", "4.0.3": "2015-10-07T18:45:58.539Z", "5.0.0": "2016-01-25T00:18:32.528Z", "5.0.1": "2016-01-28T23:15:02.599Z", "5.0.2": "2016-02-05T00:08:43.845Z", "5.0.3": "2016-02-06T17:54:42.120Z", "5.0.4": "2016-02-14T21:16:40.968Z", "4.0.4": "2016-02-18T21:52:17.234Z", "5.0.6": "2016-02-23T18:47:02.253Z", "5.0.5": "2016-02-23T18:58:30.068Z", "5.0.7": "2016-02-29T19:30:31.439Z", "5.0.8": "2016-03-09T22:58:25.197Z", "6.0.0": "2016-06-30T18:36:26.661Z", "7.0.0": "2016-11-09T17:18:28.177Z", "7.1.0": "2016-11-10T00:01:49.826Z", "7.1.1": "2017-01-10T19:35:43.860Z", "7.1.2": "2017-04-05T18:05:04.214Z", "7.2.0": "2017-04-09T22:08:39.799Z", "7.2.1": "2017-05-04T19:12:58.236Z", "7.2.2": "2017-06-25T22:41:42.070Z", "7.2.3": "2017-06-27T21:10:02.171Z", "7.2.4": "2017-09-09T17:54:34.367Z", "7.3.0": "2017-09-26T17:24:39.022Z", "7.4.0": "2017-11-25T23:45:41.543Z", "7.5.0": "2017-12-03T18:11:12.616Z", "7.5.1": "2017-12-03T22:22:19.702Z", "8.0.0": "2017-12-10T18:39:06.173Z", "9.0.0": "2017-12-14T10:31:27.260Z", "9.0.1": "2017-12-15T10:39:23.677Z", "10.0.0": "2018-03-10T22:50:59.848Z", "10.0.1": "2018-05-24T15:46:39.598Z", "10.0.2": "2018-12-13T18:30:07.360Z", "10.1.0": "2019-02-26T23:28:40.311Z", "10.1.1": "2019-02-27T10:10:50.420Z", "10.2.0": "2019-05-08T16:11:39.432Z", "10.2.1": "2020-09-28T21:29:06.762Z", "10.3.0": "2021-06-22T16:37:32.741Z", "10.3.1": "2021-06-25T11:59:10.100Z", "10.4.0": "2022-08-14T17:29:14.689Z", "10.4.1": "2022-09-16T22:05:21.296Z", "10.4.2": "2022-10-12T20:19:35.613Z", "10.4.3": "2022-12-04T13:02:59.775Z", "10.5.0": "2025-01-23T20:53:31.358Z"}, "bugs": {"url": "https://github.com/MikeMcl/decimal.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/MikeMcl/decimal.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/decimal.js.git"}, "description": "An arbitrary-precision Decimal type for JavaScript.", "maintainers": [{"name": "mikemcl", "email": "<EMAIL>"}], "readme": "![decimal.js](https://raw.githubusercontent.com/MikeMcl/decimal.js/gh-pages/decimaljs.png)\r\n\r\nAn arbitrary-precision Decimal type for JavaScript.\r\n\r\n[![npm version](https://img.shields.io/npm/v/decimal.js.svg)](https://www.npmjs.com/package/decimal.js)\r\n[![npm downloads](https://img.shields.io/npm/dw/decimal.js)](https://www.npmjs.com/package/decimal.js)\r\n[![CDNJS](https://img.shields.io/cdnjs/v/decimal.js.svg)](https://cdnjs.com/libraries/decimal.js)\r\n\r\n<br>\r\n\r\n## Features\r\n\r\n  - Integers and floats\r\n  - Simple but full-featured API\r\n  - Replicates many of the methods of JavaScript's `Number.prototype` and `Math` objects\r\n  - Also handles hexadecimal, binary and octal values\r\n  - Faster, smaller, and perhaps easier to use than JavaScript versions of Java's BigDecimal\r\n  - No dependencies\r\n  - Wide platform compatibility: uses JavaScript 1.5 (ECMAScript 3) features only\r\n  - Comprehensive [documentation](https://mikemcl.github.io/decimal.js/) and test set\r\n  - Used under the hood by [math.js](https://github.com/josdejong/mathjs)\r\n  - Includes a TypeScript declaration file: *decimal.d.ts*\r\n\r\n![API](https://raw.githubusercontent.com/MikeMcl/decimal.js/gh-pages/API.png)\r\n\r\nThe library is similar to [bignumber.js](https://github.com/MikeMcl/bignumber.js/), but here\r\nprecision is specified in terms of significant digits rather than decimal places, and all\r\ncalculations are rounded to the precision (similar to Python's decimal module) rather than just\r\nthose involving division.\r\n\r\nThis library also adds the trigonometric functions, among others, and supports non-integer powers,\r\nwhich makes it a significantly larger library than *bignumber.js* and the even smaller\r\n[big.js](https://github.com/MikeMcl/big.js/).\r\n\r\nFor a lighter version of this library without the trigonometric functions see\r\n[decimal.js-light](https://github.com/MikeMcl/decimal.js-light/).\r\n\r\n## Load\r\n\r\nThe library is the single JavaScript file *decimal.js* or ES module *decimal.mjs*.\r\n\r\nBrowser:\r\n\r\n```html\r\n<script src='path/to/decimal.js'></script>\r\n\r\n<script type=\"module\">\r\n  import Decimal from './path/to/decimal.mjs';\r\n  ...\r\n</script>\r\n```\r\n\r\n[Node.js](https://nodejs.org):\r\n\r\n```bash\r\nnpm install decimal.js\r\n```\r\n```js\r\nconst Decimal = require('decimal.js');\r\n\r\nimport Decimal from 'decimal.js';\r\n\r\nimport {Decimal} from 'decimal.js';\r\n```\r\n\r\n## Use\r\n\r\n*In all examples below, semicolons and `toString` calls are not shown.\r\nIf a commented-out value is in quotes it means `toString` has been called on the preceding expression.*\r\n\r\nThe library exports a single constructor function, `Decimal`, which expects a single argument that is a number, string or Decimal instance.\r\n\r\n```js\r\nx = new Decimal(123.4567)\r\ny = new Decimal('123456.7e-3')\r\nz = new Decimal(x)\r\nx.equals(y) && y.equals(z) && x.equals(z)        // true\r\n```\r\n\r\nIf using values with more than a few digits, it is recommended to pass strings rather than numbers to avoid a potential loss of precision.\r\n\r\n```js\r\n// Precision loss from using numeric literals with more than 15 significant digits.\r\nnew Decimal(1.0000000000000001)         // '1'\r\nnew Decimal(88259496234518.57)          // '88259496234518.56'\r\nnew Decimal(99999999999999999999)       // '100000000000000000000'\r\n\r\n// Precision loss from using numeric literals outside the range of Number values.\r\nnew Decimal(2e+308)                     // 'Infinity'\r\nnew Decimal(1e-324)                     // '0'\r\n\r\n// Precision loss from the unexpected result of arithmetic with Number values.\r\nnew Decimal(0.7 + 0.1)                  // '0.7999999999999999'\r\n```\r\n\r\nAs with JavaScript numbers, strings can contain underscores as separators to improve readability.\r\n\r\n```js\r\nx = new Decimal('2_147_483_647')\r\n```\r\n\r\nString values in binary, hexadecimal or octal notation are also accepted if the appropriate prefix is included.\r\n\r\n```js\r\nx = new Decimal('0xff.f')            // '255.9375'\r\ny = new Decimal('0b10101100')        // '172'\r\nz = x.plus(y)                        // '427.9375'\r\n\r\nz.toBinary()                         // '0b110101011.1111'\r\nz.toBinary(13)                       // '0b1.101010111111p+8'\r\n\r\n// Using binary exponential notation to create a Decimal with the value of `Number.MAX_VALUE`.\r\nx = new Decimal('0b1.*****************************************************+1023')\r\n// '1.7976931348623157081e+308'\r\n```\r\n\r\nDecimal instances are immutable in the sense that they are not changed by their methods.\r\n\r\n```js\r\n0.3 - 0.1                     // 0.19999999999999998\r\nx = new Decimal(0.3)\r\nx.minus(0.1)                  // '0.2'\r\nx                             // '0.3'\r\n```\r\n\r\nThe methods that return a Decimal can be chained.\r\n\r\n```js\r\nx.dividedBy(y).plus(z).times(9).floor()\r\nx.times('1.23456780123456789e+9').plus(9876.5432321).dividedBy('4444562598.111772').ceil()\r\n```\r\n\r\nMany method names have a shorter alias.\r\n\r\n```js\r\nx.squareRoot().dividedBy(y).toPower(3).equals(x.sqrt().div(y).pow(3))     // true\r\nx.comparedTo(y.modulo(z).negated() === x.cmp(y.mod(z).neg())              // true\r\n```\r\n\r\nMost of the methods of JavaScript's `Number.prototype` and `Math` objects are replicated.\r\n\r\n```js\r\nx = new Decimal(255.5)\r\nx.toExponential(5)                       // '2.55500e+2'\r\nx.toFixed(5)                             // '255.50000'\r\nx.toPrecision(5)                         // '255.50'\r\n\r\nDecimal.sqrt('6.98372465832e+9823')      // '8.3568682281821340204e+4911'\r\nDecimal.pow(2, 0.0979843)                // '1.0702770511687781839'\r\n\r\n// Using `toFixed()` to avoid exponential notation:\r\nx = new Decimal('0.0000001')\r\nx.toString()                             // '1e-7'\r\nx.toFixed()                              // '0.0000001'\r\n```\r\n\r\nAnd there are `isNaN` and `isFinite` methods, as `NaN` and `Infinity` are valid `Decimal` values.\r\n\r\n```js\r\nx = new Decimal(NaN)                                           // 'NaN'\r\ny = new Decimal(Infinity)                                      // 'Infinity'\r\nx.isNaN() && !y.isNaN() && !x.isFinite() && !y.isFinite()      // true\r\n```\r\n\r\nThere is also a `toFraction` method with an optional *maximum denominator* argument.\r\n\r\n```js\r\nz = new Decimal(355)\r\npi = z.dividedBy(113)        // '3.**********'\r\npi.toFraction()              // [ '**********', '**********' ]\r\npi.toFraction(1000)          // [ '355', '113' ]\r\n```\r\n\r\nAll calculations are rounded according to the number of significant digits and rounding mode specified\r\nby the `precision` and `rounding` properties of the Decimal constructor.\r\n\r\nFor advanced usage, multiple Decimal constructors can be created, each with their own independent\r\nconfiguration which applies to all Decimal numbers created from it.\r\n\r\n```js\r\n// Set the precision and rounding of the default Decimal constructor\r\nDecimal.set({ precision: 5, rounding: 4 })\r\n\r\n// Create another Decimal constructor, optionally passing in a configuration object\r\nDec = Decimal.clone({ precision: 9, rounding: 1 })\r\n\r\nx = new Decimal(5)\r\ny = new Dec(5)\r\n\r\nx.div(3)                           // '1.6667'\r\ny.div(3)                           // '1.66666666'\r\n```\r\n\r\nThe value of a Decimal is stored in a floating point format in terms of its digits, exponent and sign, but these properties should be considered read-only.\r\n\r\n```js\r\nx = new Decimal(-12345.67);\r\nx.d                            // [ 12345, 6700000 ]    digits (base 10000000)\r\nx.e                            // 4                     exponent (base 10)\r\nx.s                            // -1                    sign\r\n```\r\n\r\nFor further information see the [API](http://mikemcl.github.io/decimal.js/) reference in the *doc* directory.\r\n\r\n## Test\r\n\r\nTo run the tests using Node.js from the root directory:\r\n\r\n```bash\r\nnpm test\r\n```\r\n\r\nEach separate test module can also be executed individually, for example:\r\n\r\n```bash\r\nnode test/modules/toFraction\r\n```\r\n\r\nTo run the tests in a browser, open *test/test.html*.\r\n\r\n## Minify\r\n\r\nTwo minification examples:\r\n\r\nUsing [uglify-js](https://github.com/mishoo/UglifyJS) to minify the *decimal.js* file:\r\n\r\n```bash\r\nnpm install uglify-js -g\r\nuglifyjs decimal.js --source-map url=decimal.min.js.map -c -m -o decimal.min.js\r\n```\r\n\r\nUsing [terser](https://github.com/terser/terser) to minify the ES module version, *decimal.mjs*:\r\n\r\n```bash\r\nnpm install terser -g\r\nterser decimal.mjs --source-map url=decimal.min.mjs.map -c -m --toplevel -o decimal.min.mjs\r\n```\r\n\r\n```js\r\nimport Decimal from './decimal.min.mjs';\r\n```\r\n\r\n## Licence\r\n\r\n[The MIT Licence](LICENCE.md)\r\n", "readmeFilename": "README.md", "users": {"edob": true, "zanner": true, "antanst": true, "nogirev": true, "sharper": true, "tracker1": true, "zhangaz1": true, "abuelwafa": true, "brainpoint": true, "burninator": true, "marco.jahn": true, "ocd_lionel": true, "raycharles": true, "pedromsilva": true, "jimzhuangdev": true, "hugonasciutti": true, "rahulraghavankklm": true}}