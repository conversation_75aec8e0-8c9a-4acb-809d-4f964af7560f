{"_id": "y18n", "_rev": "38-ec9ef3aa3be9dc959357c95923918e83", "name": "y18n", "description": "the bare-bones internationalization library used by yargs", "dist-tags": {"latest": "5.0.8", "next": "4.0.0", "engines-test": "6.0.0-alpha.0", "latest-4": "4.0.3", "latest-3": "3.2.2"}, "versions": {"1.0.0": {"name": "y18n", "version": "1.0.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha"}, "repository": {"type": "git", "url": "**************:bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.2.0", "mocha": "^2.2.5", "nyc": "^3.0.1", "rimraf": "^2.4.2", "standard": "^4.5.4"}, "gitHead": "da3facd10d36b380d02dfa14db367f09ff124869", "_id": "y18n@1.0.0", "_shasum": "408ca633ce1cb2e8e1e288acb8c7007dcb28a588", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.36", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "408ca633ce1cb2e8e1e288acb8c7007dcb28a588", "tarball": "https://registry.npmjs.org/y18n/-/y18n-1.0.0.tgz", "integrity": "sha512-p0rJluph22q+Hh6y2e2CFrTIYy1oaxLD4rudaQbU6o575Fd8hGkH0EXXGHfUfIOt3t4kkcZlNM2G0izf6w4zhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzRwrlebnb6DKBJsGriXp1W+6vsGpPy0jR0mqTWyGgcAIgOZh3msEePmGyEgdlBrFsbVk59G4kXnx8Q/WaWtgMMYc="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "y18n", "version": "1.1.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.3", "mocha": "^2.2.5", "nyc": "^3.0.1", "rimraf": "^2.4.2", "standard": "^4.5.4"}, "gitHead": "84becd79cc173a7dbfd97decbbc50aec85a52694", "_id": "y18n@1.1.0", "_shasum": "ba6bce6ebc50bcdb66bebadcd8f2c6fa5f5d6e4d", "_from": ".", "_npmVersion": "2.13.2", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "ba6bce6ebc50bcdb66bebadcd8f2c6fa5f5d6e4d", "tarball": "https://registry.npmjs.org/y18n/-/y18n-1.1.0.tgz", "integrity": "sha512-ZYi7XjwE80QtQwOyYLIlbW7Y8TMaG31E5SXoMqBsrk3HICl4Edgvh4bfGDioWGh9qDS91tTEN2kUNh/O3+B7jQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBixLyZR2MJElBOKMdHzyFziHVOYOvWUz/46F9orO78GAiEAuEbF51997/jG3JbMgiyrdfVteVjw9DlbgBdWcZ34COo="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "y18n", "version": "2.0.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.3", "mocha": "^2.2.5", "nyc": "^3.0.1", "rimraf": "^2.4.2", "standard": "^4.5.4"}, "gitHead": "17c6b1146fc008e4e95645105f1733210733e786", "_id": "y18n@2.0.0", "_shasum": "c4bc87a9add7962f72d208f8d04660abd774e4de", "_from": ".", "_npmVersion": "2.13.2", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "c4bc87a9add7962f72d208f8d04660abd774e4de", "tarball": "https://registry.npmjs.org/y18n/-/y18n-2.0.0.tgz", "integrity": "sha512-pq9/O2K/Rdv5NPmMGwG17e5grO3gWSNrkzMY0XzvfIubvskV+uDOkHUlAA/AsrsxpWZ2rK9JsQmTULe7nWsDPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFsBhZSW+2Xpr9QJS6vWKTvPUbBChADmL2bSCcxdcMSVAiBusp+99+6whTw/3kO0D4ITpB/Ti9ufq/2k3tarnt12Cw=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "y18n", "version": "3.0.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.3", "mocha": "^2.2.5", "nyc": "^3.0.1", "rimraf": "^2.4.2", "standard": "^4.5.4"}, "gitHead": "58bf38fb645839f75f0ccfae16ef819fe0df2986", "_id": "y18n@3.0.0", "_shasum": "848c0149c64003945d62287d6a0a3b91d7fdf6f5", "_from": ".", "_npmVersion": "2.13.2", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "848c0149c64003945d62287d6a0a3b91d7fdf6f5", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.0.0.tgz", "integrity": "sha512-RoMA9LODvhCev/HdfjZMupROmZ2nLIACelUi7jJz2cG/BJw94kgL4+pYyObQlH5dQokks9KRClbNlXRz4ffqUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWH2nTxft5LBLbzuoCAbJaZ7SWHFL8EQYl/ZWGYisVlgIgZNjIDKvjTAo6gz6ryJ/vDD6FWC/jaJTFpkmUtOWqiJ0="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}}, "3.1.0": {"name": "y18n", "version": "3.1.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.4", "mocha": "^2.2.5", "nyc": "^3.1.0", "rimraf": "^2.4.2", "standard": "^5.1.0"}, "gitHead": "a75460b306db0097aed0c7e12362bb222e8de90b", "_id": "y18n@3.1.0", "_shasum": "dfcf19493e8dd9763a3d4bd0fa673de37cf8e860", "_from": ".", "_npmVersion": "2.13.1", "_nodeVersion": "0.12.7", "_npmUser": {"name": "abg", "email": "<EMAIL>"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "abg", "email": "<EMAIL>"}], "dist": {"shasum": "dfcf19493e8dd9763a3d4bd0fa673de37cf8e860", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.1.0.tgz", "integrity": "sha512-sp7msmfHa8Y6fTvDUOWQ4tYkyRNDg5QfZLRz4OA3Vvdz4QJmg7+psnYfNvB3zKIk4tBPgn3u3rfn91qRIIPMog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyTK/F/291Bh51Obw9UEEDcK9hxiERciXs35sD7ySrBAiAe0/jqcQGFG7kltUwETwbjIKatVbJ/Gie1r+O36IGEIg=="}]}, "directories": {}}, "3.2.0": {"name": "y18n", "version": "3.2.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/bcoe/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/y18n/issues"}, "homepage": "https://github.com/bcoe/y18n", "devDependencies": {"chai": "^3.3.0", "coveralls": "^2.11.4", "mocha": "^2.3.3", "nyc": "^3.2.2", "rimraf": "^2.4.3", "standard": "^5.3.1"}, "gitHead": "a92184823afa8cccef4c8100a0b79338f85ab089", "_id": "y18n@3.2.0", "_shasum": "3bec64c93b730d924a6148c765757932433e34c8", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "abg", "email": "<EMAIL>"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "abg", "email": "<EMAIL>"}], "dist": {"shasum": "3bec64c93b730d924a6148c765757932433e34c8", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.2.0.tgz", "integrity": "sha512-AwfXWS7HU/c65gWiqN1lvIUclNoXFParUa+ebDRF2MWcH8ZJZd9g9YtMjQblxzpBpLmMKHuU0yqZuRmBefgckQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMMUKsD46qI5fkqEcDlvAQuW9GV9898OmUoEYNeLmxIQIgWRlV4xaZrCvaHwPHRV8eX/wm64+bM29sabOMw7HHRRo="}]}, "directories": {}}, "3.2.1": {"name": "y18n", "version": "3.2.1", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "files": ["index.js"], "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^3.4.1", "coveralls": "^2.11.6", "mocha": "^2.3.4", "nyc": "^6.1.1", "rimraf": "^2.5.0", "standard": "^5.4.1"}, "gitHead": "34d6ad7bfeac67721ccbcf3bbcc761f33d787c90", "_id": "y18n@3.2.1", "_shasum": "6d15fba884c08679c0d77e88e7759e811e07fa41", "_from": ".", "_npmVersion": "3.3.0", "_nodeVersion": "3.2.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "6d15fba884c08679c0d77e88e7759e811e07fa41", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz", "integrity": "sha512-Vd1yWKYGMtzFB6bAuTI7/POwJnwQStQXOe1PW1GmjUZgkaKYGc6/Pl3IDGFgplEklF65niuwBHeS5yve4+U01Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHdpGnO72/nxWF1WBXdJh1Siy/p71Zlj+cjF03rVImLBAiEAp3Kc9UkwBJiBUgNTHsLXbYjQuVYaChhF3DUKWJlkuN0="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/y18n-3.2.1.tgz_1458191070611_0.9606689948122948"}, "directories": {}}, "4.0.0": {"name": "y18n", "version": "4.0.0", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "files": ["index.js"], "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^4.0.1", "coveralls": "^3.0.0", "mocha": "^4.0.1", "nyc": "^11.0.1", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0", "standard-version": "^4.2.0"}, "gitHead": "45d2568800d6c57be045e76dc4984b2ef3432233", "_id": "y18n@4.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "nexdrew", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==", "shasum": "95ef94f85ecc81d007c264e190a120f0a3c8566b", "tarball": "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAG+hRS2Nlz+U3BYbNCOKiKTWMCNTDAWg5KEIqsQStxAAiA6lcUIFprodpgsZ/Q7OQSlK1woi/Cw9C4ItGQumY1njQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "nexdrew"}, {"email": "<EMAIL>", "name": "bcoe"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n-4.0.0.tgz_1507662196433_0.6819839081726968"}, "directories": {}}, "5.0.0": {"name": "y18n", "version": "5.0.0", "description": "the bare-bones internationalization library used by yargs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "type": "module", "module": "./build/lib/index.js", "types": "./build/index.cjs.d.ts", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^2.0.2", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "335133842bfa398ba34d53ae17135d2604bf3e68", "_id": "y18n@5.0.0", "_nodeVersion": "12.18.2", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-kmhFIXV4iTEaUF09bNkcjbwp0XKUKF5SGmKHusIj+oQOvZyfy3Z+UFkjJWbKhPiHVNExkEuw2UmCDBGELch4jA==", "shasum": "8e056f5aa98abd2bcfa2cf2c3557c5d24ac472ae", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.0.tgz", "fileCount": 9, "unpackedSize": 20481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUvl0CRA9TVsSAnZWagAAq08P/2j00Ww2HPHYfHimXaq2\nC1ALZRCtELgrQPln5l1V0XCvht9/VoxUJajrvu9ff2ARCWzDe2zJZ+5TwONN\nO0dVwfG+idNkarLfSH2iduGDUmAfNsWzeWvbZ3UMQuDffRlG/vTN3IKavLkT\nHEGORY+J4oX9xJcDwvG203JXK1/mL6dyuO0Y2sCW58PgiJVqUgokTHwTCyEq\n/QNqFrRRsudvdp2YvRbVeQO8ez0+LcHlPr2R51EDO4K5ecEtr8vbNAtiXPQu\nGxM9fx4UAx454VwmXhtp8wMeTPQgoy6AdceYjyukooFXneQekrNoHALvUthB\n9rsRyL8t16OjltILG0WZ4nkAKquY5Se4HuuA4YaTwIddPs23Ia+oIhPdvbU6\n927zIuIuIc0jQJ93DLXvYS3YO+bz+1KgSTJEaHvAZIWJujjAclyzxLlFakoc\nukZml0RW12DkaXr+8CqJdvL/hy7to6YUiKd5hG2btql09vUqo0uGjZsSVvP+\nrEM0bBQDVq+NQku35R8uhNjQXG4twsywVAuJzIl8NI6Cn2ZPCJabcHFR8T8W\nm7FSGjhQEbQKnIusZIxAaeJq+A6utAlrmhX3sBhVFvB5QLk7ZEoZVKH2j2aj\nEhyYUIrgt6TD3VSNmD9avq7J6AO6j2cx0/N4CDY4uVQqMJmGffV0KkWgVT2r\nfU4f\r\n=Aj11\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC409Ch+vFVlCAN7SPzrmb/Vezs8xBkbIkngajNZBV/MQIhAPWGMQYhId/5ABPK6f6mB4vCtY/L97Td4CEd4JRouS3/"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "nexdrew"}], "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.0_1599273332047_0.682491923761469"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "y18n", "version": "5.0.1", "description": "the bare-bones internationalization library used by yargs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^2.0.2", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "786ffdc6cdabc15fd8e50240f662ea11e67ef5cc", "_id": "y18n@5.0.1", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-/jJ831jEs4vGDbYPQp4yGKDYPSCCEQ45uZWJHE1AoYBzqdZi8+LDWas0z4HrmJXmKdpFsTiowSHXdxyFhpmdMg==", "shasum": "1ad2a7eddfa8bce7caa2e1f6b5da96c39d99d571", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.1.tgz", "fileCount": 9, "unpackedSize": 20729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVCYICRA9TVsSAnZWagAABYwP/iojZVX1+4wla/SyTgEu\nc642hp7iVvg3eGo3J4yQ4/Pigiy4z1z9/dVXkQ/Evmm+C6I5UZcgMC2A8L4E\n7mkLeldGJFnGz80rPR3hZfxatTqUGFoN1Rr4x1gD4YwcG0koOkDbgsO5U3xy\nks7APhSIAHHG/Oj12Ldrj2d7HZpHzFgCsU/KsXc72APBlHblq0E6LDPa+Ioj\nis9SE7Xis+voKZ7A/Tuvk90T+mYBSFXaMR13cpzdo1AuUEmY5NV815PxWrq4\nXcPGtbcJ73k0d3a28AeyMbV5W3E2l72/TD7tJY0RpcBUO1G8gc4uTVb1LPQt\nlNJLOFrKi3cyBsdHbONXmrTRtGSwwkZr/6skYQ++3iX6WHxDLiRyQcWfJWQY\n5kATU1cHF0f7xPaqW/vyIt+QYeADAeS+a1557P9B9PWeOOj7MHwdDt7lIAHF\nrsAhYAryVZWH7bKavzUMIUZJ8ZeULg6gXcFOU2u2UJ07yy4f+p35U4W3uOFX\nkp//danQq003zVdNh3c5g2PwiJw/Mm3ZHs3VA21/5A8DZrhE6KsMmIclEWwy\nlOfjHYOEfn3xH+USqg1ipl6O2qpLOsMAy0Jw4UsdqB8AEyfKHrGUBvsPeeXK\n7YN1K3R4nhC57CTLtJkf4RVRdXjrUtPBZAXft2eJrunlTapQNAJPjP5kljMJ\ne3j8\r\n=1L39\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/ugVZaCjUDky3n9hvpPVSJo9eRysgh5r1mE1reXMCTAIhAPb+ye2jgbMvFKNA+TX0idtkUSJgLDnsWgRkjWIAE+s8"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "nexdrew"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.1_1599350279463_0.9581207231521827"}, "_hasShrinkwrap": false}, "6.0.0-alpha.0": {"name": "y18n", "version": "6.0.0-alpha.0", "description": "the bare-bones internationalization library used by yargs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^2.0.2", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=12"}, "standardx": {"ignore": ["build"]}, "readme": "# y18n\n\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nconst __ = require('y18n')().__;\n\nconsole.log(__('my awesome string %s', 'foo'));\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nconst __ = require('y18n')().__;\n\nconst str = 'foo';\n\nconsole.log(__`my awesome string ${str}`);\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nconst __n = require('y18n')().__n;\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'));\n```\n\noutput:\n\n`2 fishes foo`\n\n## Deno Example\n\nAs of `v5` `y18n` supports [Deno](https://github.com/denoland/deno):\n\n```typescript\nimport y18n from \"https://deno.land/x/y18n/deno.ts\";\n\nconst __ = y18n({\n  locale: 'pirate',\n  directory: './test/locales'\n}).__\n\nconsole.info(__`Hi, ${'Ben'} ${'Coe'}!`)\n```\n\nYou will need to run with `--allow-read` to load alternative locales.\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## Supported Node.js Versions\n\nLibraries in this ecosystem make a best effort to track\n[Node.js' release schedule](https://nodejs.org/en/about/releases/). Here's [a\npost on why we think this is important](https://medium.com/the-node-js-collection/maintainers-should-consider-following-node-js-release-schedule-ab08ed4de71a).\n\n## License\n\nISC\n\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "readmeFilename": "README.md", "gitHead": "9c8a079a2917b71dd55086233f4272da521fc600", "_id": "y18n@6.0.0-alpha.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-J9CO+Qo98a30YwPMgXt1IetZS4823Y+KzBEWHPQYaO2sWcwtvVascTF0eNdUgU0Me3Efl36PnCygmVPdzqQmJg==", "shasum": "eccd8e2176c2bf786dee53c4881cd88e3e4cbfc5", "tarball": "https://registry.npmjs.org/y18n/-/y18n-6.0.0-alpha.0.tgz", "fileCount": 9, "unpackedSize": 20737, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXBQ2CRA9TVsSAnZWagAAM+4P/16Qogev2yixoICuoNHr\n7M6kQHMOigj5oh4YCavKfO3zm8bpQNEdu21bT2SXPYxMSgVzR8hZsJi0p56D\n68MiFTkXJLWKyjmEIrXprtLYDYOSm8BkKsFG4tiV1tgoYLryWIN8F8vzYD11\nRtB8P3Wu9INr+hbwF9XNUH0JyZfeEtmrabTodZT+/PnHV7pcnnBR45zuFuiM\nHDd4cPR5/2Ngn2+UAYF8ItIchCStN1uOFLBoq86wD98tciM2sGlu1ji7ueO3\nu1TBKXqHLjVLMPvd19ZdlAoAQwclvRk0TA2YBgqjhBc4MnnyanijwVQVKccd\nfQvPtptiSo9tgy9Z/kvXnUrsKPZ3RYus5rtyKNWI9g5jtI5R1QwYCtkTI9e7\n5+hszJIN9GJ0R49E3otwHlpVgppmZ+PEL/xfcBaAovJxHDxZbkD44EAjsBRA\nCs5A/Z9vBFJe7eytdaYPeZMEwbGPNvzZuCNfb3TpK8ZUG/TLxSsLozsVgexU\n3I5424QpbrDZ9LXYg7Ies495tc30yBhdw5TxA0sxgWzPRxaCM7kKgJlN0y3D\nxYPByUAu6f1seQ57BzQ62evhPswBSp0lyH8c1rHA3D7ufNoU1oWRb4Fj7eGu\nK1B1x/83WGMgVuJ5yimeqUDnhrKSHt7aU2sjKrwyAF7W9O1MqDuWz+fpLmIF\n5Zmv\r\n=4f0P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFzpI0NrxcPps5KHCsYn923+7lHs/T1OqQTYvQ3HRsagIgQr8hOuwwjwbo7FqAKcWwjyGi2et8NpeEF94qX8ydZyw="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_6.0.0-alpha.0_1599870005446_0.2345770096577866"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "y18n", "version": "5.0.2", "description": "the bare-bones internationalization library used by yargs", "exports": {"import": "./index.mjs", "require": "./build/index.cjs"}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^2.0.2", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "29e7571ece1cfa8fd5d197c284e384a2fbea79b2", "_id": "y18n@5.0.2", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-CkwaeZw6dQgqgPGeTWKMXCRmMcBgETFlTml1+ZOO+q7kGst8NREJ+eWwFNPVUQ4QGdAaklbqCZHH6Zuep1RjiA==", "shasum": "48218df5da2731b4403115c39a1af709c873f829", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.2.tgz", "fileCount": 9, "unpackedSize": 21022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdh6fCRA9TVsSAnZWagAAsaAQAI9MDo11h0ZWwN9UGjFq\nZOFJyRuR7Er8m5VlrEs4Or5EoKnHm0kFR/baZrWWB0pSgJgQuNyqW7MXaYH8\nqFY8J5wr6pO/I6pyP91Ok6qH5gHfYhzgaH3FMFB9pP46HAVD6y+PW8qX628d\nmhN6Mo1LaLBSrudHpn4R4RRfY40dLaGECZFglZBkd5zSBSJXa81V4sJcuPB2\nCZGZMWpoCE7LBA645sCmchUEPnYyn0qLYInd4rfXtBy6DfrVCRJ+1Kf7l8Gq\nAk0/Yf/iMyzKMURxFt9vHfCfOovdXF+QQjJVNA9MgTUZ8aRjlPRkP73bsexG\nFEBBKsoE3sx9bIFEetQiUycCEqxNKp3oDJ9+Y208rKRK7mWiTQxXT9SS9p4Z\n2M/3KL8zjEJFysH6syIMv+Va52zwb6W2XnVNwUCjX09Fk6XJgqyu3nH/t6vJ\niDvPg0ZzhBMd8EOsOJ9rJfRpdlatipf7vRRfQsU/Tw+K2sZccxMNV1+B+0Hu\nIUlXtiIohIYz9qvcko9sZoDtr6Vm52NGCEaAgmn0gitBlwO5zVxGf7gS9qQx\nkRLLLcRehfPlVSvAg5R41BxIQN2yNtL2T2MnApoJ4W4+Ekq8UaFwFaVvQqN1\ngkXAbUKGH+YIxDVea9OlH4EegJdA9KK8+8+cn4v+Vy25LEJeq5+nx7wngdQi\nOUA/\r\n=hKDI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnixjnn1HpIII7inPp6TtNFjRQM5j2atdBPRvqic7xLgIhAIauZem9YcLJNKtVV5pBDNj00pTbaTXBU3VKv4kcphGB"}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.2_1601576606909_0.7193058628251092"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "y18n", "version": "5.0.3", "description": "the bare-bones internationalization library used by yargs", "exports": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"], "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "d3584d2f1b1d98bb8a7bfd4c8c92614d1684376a", "_id": "y18n@5.0.3", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-JeFbcHQ/7hVmMBXW6UB6Tg7apStHd/ztGz1JN78y3pFi/q0Ht1eA6PVkvw56gm7UA8fcJR/ziRlYEDMGoju0yQ==", "shasum": "978115b82befe2b5c762bf55980b7b01a4a2d5d9", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.3.tgz", "fileCount": 9, "unpackedSize": 21371, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiPzQCRA9TVsSAnZWagAALcMQAJxWfGfIsRzQwm7umsq4\n9yw7cUQUOGn4ltyWJ1UUCZgd3wFw1VcmWYrx7P5Y9efAQbf5djIpFT6izBOO\n04fZb/ULw8z1VHFA1FE5TiQZU6JwbeflfNS1r+p+aNT/RnCm9GY+h01LglCQ\nb92YwoQPnBgq0NavI4DKmNyRKosb58jVCVRPj6aY8Clb7mffdd43L+71RR9L\neH+1mQ9buKCe6hFeAseIR/wIyjlmNpKSA6TdfT8URVphEBHDZHLpwTJj/6vr\nxro1ngcI+7YWSdAPiSGMna+/ilzKrK8R1tWjb/t/gr7x279/3c6xEfftgp/B\njV/U/6TRcvvwj6d409QBLxrPksZkmmfVGb2rPyrcYi6ZwZiMCNRhMea8cUwK\nvyp539i6SC/JQlJMRZL2fqfZ90iV2cwmEXPk5Cj9sCellLPuqwqudl3aca6n\nFoj+CCt4KaBpUbLOkoipqqpsxAeWes9/zO6CtbKm0uuCTa9Nd4HENYdNf+Id\nb6bWO9C3K5zO6ISSQakDPA6Ni+wQMRF3u2exRh/Of5W2UKt3+oHoTOhyh5pr\nJ0X/r1RA1bP21gH/txZZaUWEFNdFmaLOUYEjP4aob85rHOqf9YJV/pUWOtPX\n6tSfYEyIG0qQQZon2aTxsgIhiWvQjlEeKWSJRzVihMMsFI7OuEXfNVo9/0rS\nRiE9\r\n=KOEc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFtzrTzqZXKNq1HTENkeaOR4/Kjyf80KAlN+1fppOgGTAiBmLgrIYkIHGOz2ZNHuWmp6bK0fMTVn50ekreTf9qxYsg=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.3_1602813135466_0.6790940796421885"}, "_hasShrinkwrap": false}, "5.0.4": {"name": "y18n", "version": "5.0.4", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "f91f68f5b7ab163d57900b4b25e4b5d0dc5581d0", "_id": "y18n@5.0.4", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-deLOfD+RvFgrpAmSZgfGdWYE+OKyHcVHaRQ7NphG/63scpRvTHHeQMAxGGvaLVGJ+HYVcCXlzcTK0ZehFf+eHQ==", "shasum": "0ab2db89dd5873b5ec4682d8e703e833373ea897", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.4.tgz", "fileCount": 9, "unpackedSize": 21740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfib/8CRA9TVsSAnZWagAA4XgP/07A3GZJ+GGFaBSlSrY2\nstsFs83Xd3S4QBLgHHFlSkeoAaTgilHqvgiQEhMPzlEzUkjwHIRa0dI18fv1\nxaNTmUC/sOJeRHpSUfGH9ZnIWBP3RHlczX6brrTL+S1P/umJAnkJCrcVJ4cU\nzTVC5zRaISPolwxWpm2yY+qEsGpwWR9TU+Ig44HPqDrQX9JYlCrBnRu/uplP\nyOHTsY2001iGmPFeA+cUNrB66z+vWKo9g9UleZrCPobtNyTct7MaDJzyoi/b\nPCsrBY6Qzof8fWqs0wjKe+0oL3OSri1d61dVJQ0ga7hyZqsqZ8VhH3YO8oNE\nYHigUi94pndxW/Voul/8LyIub5BELMUr5xhfW9txuMNqP6ewO3FG3mqTDwRH\n/gXdIPEGI/c+5XtI5B5/MYdDFmc0obNJF76u+hzGM7iz0eGKDeU/h3N8n9G1\njS5kfSRv4PTqb8M9LgfFlbn3tEqTVHicTTw/2z6cgN03OGxXcnZ7UTqZTQFl\nwaIMGMVnmKjSKWN0pqnWBM4AWVjyxXe71bYbRdvP2UewWELGGrkKj/pZpgIJ\nRqqi80pvjY+xapty6nj21dp/FdoHQZGEZIkwR3ajkA0IROfk21ES8FV+niIU\nhyd9UIG6RKrBXmdUBUKAv3wHAI9C23C2O9AEbxN0V31aXVsAiUrpUxjhQQl/\nwNqM\r\n=lPcN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICd45z4UCqGaTKOYgwSz1pw9COdS+/c233jCqZGYpUT9AiBpCdvs+szdZIfc/mKQfvzUWVMxlZwhByPsa0GE1TgElQ=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.4_1602863099464_0.5549476304057792"}, "_hasShrinkwrap": false}, "5.0.5": {"name": "y18n", "version": "5.0.5", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "e343b70fbe8245c0a02a5615eee1bbd6bcaac1d6", "_id": "y18n@5.0.5", "_nodeVersion": "14.13.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-hsRUr4FFrvhhRH12wOdfs38Gy7k2FFzB9qgN9v3aLykRq0dRcdcpz5C9FxdS2NuhOrI/628b/KSTJ3rwHysYSg==", "shasum": "8769ec08d03b1ea2df2500acef561743bbb9ab18", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.5.tgz", "fileCount": 9, "unpackedSize": 22062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflZdYCRA9TVsSAnZWagAAtyQP/jnPo33XLRtlucweLctK\nj+t+12c1vEa5tFZuotgZjQPu3tyNAeneh8YAdOCMV+f1YjRqe7SJxI4qlKED\n+iKET603NeVA3ogz3bYyjLf3bip2dJ/DJ1n88a3sOn1YNK7HnoIF7/sbRmCF\ny8iskRhiZQjNSYc+i0Ozx/n/7DTF7nxKqdIEe16xC+tybjP3044RvhckNI5T\nZkom5HwFbYHOarvcyEZk5I3AeBhcJmSrPc5I4MUDj7Mn7gsQUlh88VLe1lU6\ngL+xkMm5bkhoA3O/HVa8b0ZDpW0U2UVZJGCsJZ2uUy1A4NaBysCOuEr5qngi\nMlosNORPQX1KbDjLLZx1KVswPLoSiBGngbUNSWGHkFI03rrfFLDHh5G+lRbq\nPcY5rQCBdkK8jojAUOi/blzSVKLoAXKHQpbiSFgWcn4VNdMuK/rrdHmrJyMz\nkOaXhMNXOgn6DA9nlq/vOn59dgAKpwlmf2E15iQQ2CP1sWk7Qmk1wcY6dRDV\nR/djfuO44IOa6svqauAgQ49lFH243dCoCgXi03aZryz0HhZaP1Lw6FItI05q\ntFwsGIrfT5TnOxrPGL2qMcHrDrGusnJBPV+4XglGe8cMW8Ap59nC1B1dhIpK\nAgyU+amJdLvS1KQUiuumIqXNdWYEDlVvibzCmXuK4/ExdCdGqKxgZ9CTKlOw\nuOqO\r\n=hOib\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA3soP+S7MThcC0sLV+KT/w7ssjDTtlhfDkDwAj0K35eAiEA8k8CtjMOlyTRaObrtHkuBUea1gXj0+F6cgCnyn3ga4U="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.5_1603639128070_0.21918731679310355"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "y18n", "version": "4.0.1", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^4.0.1", "coveralls": "^3.0.0", "mocha": "^4.0.1", "nyc": "^11.0.1", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0", "standard-version": "^4.2.0"}, "readme": "# y18n\n\n[![Build Status][travis-image]][travis-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nvar __ = require('y18n').__\n\nconsole.log(__('my awesome string %s', 'foo'))\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nvar __ = require('y18n').__\nvar str = 'foo'\n\nconsole.log(__`my awesome string ${str}`)\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nvar __n = require('y18n').__n\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'))\n```\n\noutput:\n\n`2 fishes foo`\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## License\n\nISC\n\n[travis-url]: https://travis-ci.org/yargs/y18n\n[travis-image]: https://img.shields.io/travis/yargs/y18n.svg\n[coveralls-url]: https://coveralls.io/github/yargs/y18n\n[coveralls-image]: https://img.shields.io/coveralls/yargs/y18n.svg\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "readmeFilename": "README.md", "gitHead": "8dc75802f3aa944bf9a827213969d64834621215", "_id": "y18n@4.0.1", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-wNcy4NvjMYL8gogWWYAO7ZFWFfHcbdbE57tZO8e4cbpj8tfUcwrwqSl3ad8HxpYWCdXcJUCeKKZS62Av1affwQ==", "shasum": "8db2b83c31c5d75099bb890b23f3094891e247d4", "tarball": "https://registry.npmjs.org/y18n/-/y18n-4.0.1.tgz", "fileCount": 5, "unpackedSize": 10681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxYOnCRA9TVsSAnZWagAA5P8P/iI/sSK4+8fUc1ICCECl\nA2mO6qvLQL5986og3flimqfqExSQTdBeRgkQQBVRF3jgFPTS9yELqnP0WFGK\nyn87WKheRLCCTl0kWVGIsdV4VJwRdGe0AspfCU0jeNDAP1ywZvzMPFlxS+Pd\nt3Re8izZvrv9w5kmZFQdlZfusUvdBbS/f6MrS01kNkP0whgaAsFlhsEx/7TT\n+A3I5Rn/DHhmEc2yQx1ur19XQqlz4I5S4lJVPM1Or9kzS7w+UkxygY15A0Vy\n15d4Y91tOVpYlQ8BJA0Tiv5KonZXvnEdE5llOPHKFWrtAn+/IJHSze4lXcja\nUJ3Ah3hJgXNphR4uRSvLwBRJK8weRxGSq4rjAsmVqh+IYzl6DQqLxrcob3K0\nS2Ya9yj/+SBuYcXJPp98V1tOh53lW6sFrNcR4KvEsZhQauZoCDGB5w8qce5N\npyKDWpl+PPs4AIcyHbA7UsoernUefzj4zaISn4LCM0f+XiIf6UbtjX4S0b4c\nPLCngNnixH880sC13rqscvRiScpNGeGbX29y70H/S+sWFWX9t90P8VcxVA94\nxfckt2qTrEXnzm0eBNljLctzimomO/pkCsHDMB9LoLgGzTZy3pFmBJH+qGq2\nV1tpvtcSSK77Hw38yWBB6x0r/VqoVqpt34wE6LnJ8JFpUmgptQpdaVVrgrSr\nJv5I\r\n=EkkS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzSxGX2xT88JC38tkOY3FMEOQmn8VoRrVWPeBhis/TMgIgc4QveU6amA7jzOVwZFIu0xRnkHJHvmCNxFvxH7q7ey0="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_4.0.1_1606779814627_0.7420427256466908"}, "_hasShrinkwrap": false}, "3.2.2": {"name": "y18n", "version": "3.2.2", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^3.4.1", "coveralls": "^2.11.6", "mocha": "^3.0.0", "nyc": "^10.0.0", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0"}, "readme": "# y18n\n\n[![Build Status][travis-image]][travis-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nvar __ = require('y18n').__\n\nconsole.log(__('my awesome string %s', 'foo'))\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nvar __n = require('y18n').__n\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'))\n```\n\noutput:\n\n`2 fishes foo`\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## License\n\nISC\n\n[travis-url]: https://travis-ci.org/yargs/y18n\n[travis-image]: https://img.shields.io/travis/yargs/y18n.svg\n[coveralls-url]: https://coveralls.io/github/yargs/y18n\n[coveralls-image]: https://img.shields.io/coveralls/yargs/y18n.svg\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "readmeFilename": "README.md", "gitHead": "90401eea9062ad498f4f792e3fff8008c4c193a3", "_id": "y18n@3.2.2", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-uGZHXkHnhF0XeeAPgnKfPv1bgKAYyVvmNL1xlKsPYZPaIHxGti2hHqvOCQv71XMsLxu1QjergkqogUnms5D3YQ==", "shasum": "85c901bd6470ce71fc4bb723ad209b70f7f28696", "tarball": "https://registry.npmjs.org/y18n/-/y18n-3.2.2.tgz", "fileCount": 5, "unpackedSize": 9006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf85scCRA9TVsSAnZWagAAJiEP/1w7LDZGx4QGYybO80Oi\nhb/vkfnYaBrMQCL2ApR3Vc1UaHSQh2gso6pR4C5i7SkdY3L0NpBlRotuqAOZ\nfWv5x2fDJJeqmBZFyBaklaTRTUzOoVVdanKx9E3ZGkne1H/x7ZqRmPSV+DDv\nCq3rT+z890kgSDmEl35D8d97imrmfxJdVK4izAAIlpakbtTQkLHIGfeja7iu\nCDxz0kFWjNx3jEe2jKHyEKwMKYvU57RQl0fdYFMa1LTKsYt5rynSMhOenu4p\n2kB9Har39UsBrZQVFemXjeMyLt2F343cngebFo7XxVQyI9m0cFtqeynI517r\nJFxeMFeOnfy8msT+wHn7sLlKrtRPX6pJuEBCIJanHdajjehxXAo0zcuJYeMU\n3uJnjyl1I5DzvvczoyCE0aJeexb6x/4RUvRusnk1AhJuXTm1m0FXDOaIHAHq\nNR08NWiliNkIwaPj27YyqPnC94k4brhA68lAby5SSV/hpRypwIC9n38/Jhuj\nh+C1KK/cuPjWvxWE4TNkKf8D/3Iv+7lacD5RcppVgRntK4V9rs8WQEXsxonm\nxK6k/rvWqLMCWpP9rC3Gy76LDhIQwaH5222NpMGWid1qMMf4mKGqN9igisRo\nNOH+zbfCgUINjzWaVEh6nKq0HyvAFJ0MJofrV+wpOBRiAzDWPRvht2MA1P6H\nkeCM\r\n=BVy7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAV9u4ild2q71rpr9c2FUv9D3Heu7Zh3DkdkQiIN9NNpAiAGCPz32t9O5n+7KaO9wUbhi7RhQb5MPwzcjhkm9wysVA=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_3.2.2_1609800476248_0.15203860144039116"}, "_hasShrinkwrap": false}, "5.0.6": {"name": "y18n", "version": "5.0.6", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx **/*.ts **/*.cjs **/*.mjs", "fix": "standardx --fix **/*.ts **/*.cjs **/*.mjs", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^7.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "0884534e044d1cc1239b021232dc6f358a71fd3e", "_id": "y18n@5.0.6", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-PlVX4Y0lDTN6E2V4ES2tEdyvXkeKzxa8c/vo0pxPr/TqbztddTP0yn7zZylIyiAuxerqj0Q5GhpJ1YJCP8LaZQ==", "shasum": "8236b05cfc5af6a409f41326a4847c68989bb04f", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.6.tgz", "fileCount": 9, "unpackedSize": 22909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgamcoCRA9TVsSAnZWagAAJKAP/2sX4bd3dx2c0oN1D9uh\nmsCLv1Rcsn1+3xDFMoakFzAKcuXhYyeM6rRUu1dh9NUYm7lCSaWce4OJn4y4\nddv+lz+nSPkYeWH/lasYe5PUFbIsVg6NJLjlHK2JyXi5KZDQXUDio5XDV+tO\nPEc9n4a1qJhzsLOb5kUyQBZmi937Airjqa7Rpn5mPxbrAWILEyLNBf12bf8l\nV5s2Whu+cc/3GXAM59j+EBcm9JZFD2UeY6HT9JxAe32+g5uXuK2w+kS7EdVx\nuRr4QBbPmWw0Csx3lj7PPKn2kJOgjWD0BVjpvECi4K8i0XCgWp4xNZbY/Fj6\nWjha+JHlqGQQNLMzm4gGypqmitCfH8GBdpm2Y9TbKN6yZdEaSOtQTwSBdZH1\nZEa8oeh9N6L4sOrkMFrYK/g0tWpX6/AlmbZVVbPxiDEBCSDmaejDErmSJwGK\n/5iRjs7bE92c5axpoPeha38gWnrbmEb6f9M2lQbWZ90vWVA7mCtmb+aMavvb\nlCOxIsARS41Zr2UHgOhHSq+G3a7aHXKq2MzrE2OSbHEY0qoAu/AjF6CZi/f+\ndlrbKuoiJnEKHJeJDapedbMMR9K7OZpjnDL+LU2F1pLIZdHuu7JuTZFgJnaE\ny8JkpXxj84kSp+4xJvsW9bVGNoQIpGBCCRpcMbK6hh6PNoqswFldpflSl9yP\nLp2u\r\n=0hFW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYfh/DyFqhrzmkSbv/4KLXLqsfNG6S/w+loCrmCHNmEAIhAKvcxSCH/jycMOQipLF2VF22bdCweS+1Gq7PpL6UNcIC"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.6_1617585960208_0.7906647956829878"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "y18n", "version": "4.0.2", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx '**/*.ts' '**/*.cjs' '**/*.mjs'", "fix": "standardx --fix '**/*.ts' '**/*.cjs' '**/*.mjs'", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^5.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "readme": "# y18n\n\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nconst __ = require('y18n')().__;\n\nconsole.log(__('my awesome string %s', 'foo'));\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nconst __ = require('y18n')().__;\n\nconst str = 'foo';\n\nconsole.log(__`my awesome string ${str}`);\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nconst __n = require('y18n')().__n;\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'));\n```\n\noutput:\n\n`2 fishes foo`\n\n## Deno Example\n\nAs of `v5` `y18n` supports [Deno](https://github.com/denoland/deno):\n\n```typescript\nimport y18n from \"https://deno.land/x/y18n/deno.ts\";\n\nconst __ = y18n({\n  locale: 'pirate',\n  directory: './test/locales'\n}).__\n\nconsole.info(__`Hi, ${'Ben'} ${'Coe'}!`)\n```\n\nYou will need to run with `--allow-read` to load alternative locales.\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## Supported Node.js Versions\n\nLibraries in this ecosystem make a best effort to track\n[Node.js' release schedule](https://nodejs.org/en/about/releases/). Here's [a\npost on why we think this is important](https://medium.com/the-node-js-collection/maintainers-should-consider-following-node-js-release-schedule-ab08ed4de71a).\n\n## License\n\nISC\n\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "readmeFilename": "README.md", "gitHead": "bcfdd05cf8405593cd3261e25fde4976efd8672f", "_id": "y18n@4.0.2", "_nodeVersion": "10.24.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-DnBDwcL54b5xWMM/7RfFg4xs5amYxq2ot49aUfLjQSAracXkGvlZq0txzqr3Pa6Q0ayuCxBcwTzrPUScKY0O8w==", "shasum": "c504495ba9b59230dd60226d1dd89c3c0a1b745e", "tarball": "https://registry.npmjs.org/y18n/-/y18n-4.0.2.tgz", "fileCount": 9, "unpackedSize": 22085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbQ6gCRA9TVsSAnZWagAA9gMQAIB0Hp9P+JtCe+iYpU4n\neIVvRlgeZvPz/sX32aXQO70RE8oKJal4i3lECi9iA2gWeb1MFHC0iiwQWxmM\ne73ERJ1PPWOEuaozP9I+HGkx+y8IaLeMl0/Hk75hDTkWlY8IY6awoDwVj7/c\nY3d/A/+442k+oOM2HdN8WSN7dbjRiwMp3/0JxqQY+o1njWbUBy77cExXfDOM\nbkwLxPO/El/QzfctyDj2YKHpvdNcBOzaDRKN6vH5V6slV8TfjX+yNyewqjQz\nvS1pONXgRb7Yff8qatzfW1Uts85cQ1kCv7Opt47wcbNOBmcq2vKhysuTyp4P\nHsx7AKV9NudVTsa2vkd4R0w7IfQgdn8V31mENkPEFsUdb7IAGU8TaJJUAl1k\nFrEzU9KAiO6xok1EMq01WLUWny1ATAL6hVJc/gimIKk0ob2FOYff3WdC4iHE\nLantYcxGvpXpK8ks0OEeKtQw1tSwk+zn6n7NAqzrCM4JEm++raM0qn3IqUf0\ntzSeCLGyAUvG2O7kwWHSOYXx9CguvWZCXudHE/hizyU7+ciSL+7pX8bLrsG2\nikcAOGJXjzWD9zCfqnwy3XIc+Vkub7FQITeeF+gaAdjcgTHLv0pjrhpWC+ie\nvOrB+0bbu1c44c3GBuxIwhSeUiRtXYCPDT4VgBJknuowgKH7t3GcEw0J+Sgi\nXpV1\r\n=q3dj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNRSpQmiqU2CH1vd8F3M5fbFSaK3PWHPEqw0ItgRrv3AiEAqXJON/ZV+luaoLjsSNd4VerwEeUSTEjVLZcbR9mpbT8="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_4.0.2_1617759904116_0.11983796019170101"}, "_hasShrinkwrap": false}, "5.0.7": {"name": "y18n", "version": "5.0.7", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx **/*.ts **/*.cjs **/*.mjs", "fix": "standardx --fix **/*.ts **/*.cjs **/*.mjs", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^7.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "76718909722fd1765f85ba4d314f1b2ebd8b1239", "_id": "y18n@5.0.7", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-oOhslryvNcA1lB9WYr+M6TMyLkLg81Dgmyb48ZDU0lvR+5bmNDTMz7iobM1QXooaLhbbrcHrlNaABhI6Vo6StQ==", "shasum": "0c514aba53fc40e2db911aeb8b51566a3374efe7", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.7.tgz", "fileCount": 9, "unpackedSize": 23196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbQ8DCRA9TVsSAnZWagAArsoP/j0uE5XVd3c+XVNb98Pn\nNq2Mx1a/fGh30Lj/mPaOwm2HG6BywRWGhYmO6xmwVTJf/ZYKhk+V050RxLoR\n9kRI8O5KHRT0iPHiHgoEz+4i65/b336SgcSLWIJDHEK/YYLOsjHxZU5BGHo5\n24c89FyaM6pK8Uq2azckQxtWrE54bzRiWe41gQCCZ6RoRnCG8GzUM+aH1GlB\n/uaSRL5AaS0mj5Ew19Evb28MSMw1lG4Ub5BmNpwAawmuntukeriP8nDSklEo\nzcjzC+lfQy42Q0NVyfoC0mdUeGwg7H7z5PrpJ4uj0MRFjD34lFAXgcVFlvyZ\neK8WRa2XrldC9CRaoCPe5LDK+rP3aa0yMFhdDMMXVdqPyjgkXlx47ysaGC7D\n60+T8DfGAx8ZDmhxqn8EIPwOmid1dZg9aVJpi22VzKnFEA7janqaqcs88T2q\nAYr9uXWHDLnzAhm/z3rlAXjJNfVlRBAkWGN9FU0QdaIFUj71EIoXCDHbNALg\n8bFJDNEjzC4SHaIDhe5t5ZaCqDKFT13kowz1ISQ+6jln6yO68g1YtbmK3xCw\nlRZQC0xplDt2CLaa7fDqHJG4wi4cr+A9J9F4+tR3vqEN7cWGTs5DqL5FZbLj\nsmlJ02uS9JtVAgUwnF61NWaiCl3UUHf2PftU0Qbi/7hocbz9GyS91hyX6A1n\nEH5k\r\n=E5K+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeslun2xOuyVVDbxuvBix42mRH0+soZm77UUQuc6KhQAIhAMzf50oOIs6UzP0UUObiW/draOZ2RguW4/++G9lmb5Pc"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.7_1617760002805_0.7333351141980393"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "y18n", "version": "4.0.3", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^4.0.1", "coveralls": "^3.0.0", "mocha": "^4.0.1", "nyc": "^11.0.1", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0", "standard-version": "^4.2.0"}, "readme": "# y18n\n\n[![Build Status][travis-image]][travis-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nvar __ = require('y18n').__\n\nconsole.log(__('my awesome string %s', 'foo'))\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nvar __ = require('y18n').__\nvar str = 'foo'\n\nconsole.log(__`my awesome string ${str}`)\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nvar __n = require('y18n').__n\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'))\n```\n\noutput:\n\n`2 fishes foo`\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## License\n\nISC\n\n[travis-url]: https://travis-ci.org/yargs/y18n\n[travis-image]: https://img.shields.io/travis/yargs/y18n.svg\n[coveralls-url]: https://coveralls.io/github/yargs/y18n\n[coveralls-image]: https://img.shields.io/coveralls/yargs/y18n.svg\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "readmeFilename": "README.md", "gitHead": "0aa97c508ea31efadd2a27f98fed6873eefc963e", "_id": "y18n@4.0.3", "_nodeVersion": "12.21.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==", "shasum": "b5f259c82cd6e336921efd7bfd8bf560de9eeedf", "tarball": "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz", "fileCount": 5, "unpackedSize": 10991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbfRkCRA9TVsSAnZWagAA14wP/iqe8XL9SWtCbOGAoOAZ\nkbCD5JlnBrfniybyuX+UaPG136jHEgEYRfaH4KGH6tWtpLmCcL0haK8ZsACk\nqLQP/G5w4b+QWpLAX2wBzQRMxOeQAyIfFuuwoRoyoAv+8oyFezX0/QAIRxu6\ng5O8c0pWtQRMN7hqV2RsbAL9CokxDuUEFjephKLvo9Fn9X4A3TuBuSzfvUbo\n5CuIeKRnsI7Ucp+nzrZ93PaqBRJhibsXSx44SL3oCfm44+vZCZyvBYuhYr5F\ngR46eCldtVEj9NEiR+/7jYPHi2Ijq7F8Ll9s3hqVujfAq9S+aIZ1lAktJB3M\nOVACLqQFd9ZGyd3Jlx0//SiwFA/U5UbHm4YmuqjpFOcvwJBwboi9ERdDXnb4\nyvup++XQWk0+OYEKgohtCevwkffHp9Tyz48nz5KEGwhlo/T1J7k0RFnKdA/Y\nwbSzh+JfcQKmuVF136L30hD+iP6oQgYPBwMFTpIuz7dlSkob32FMO9aAY0IQ\nOyDeF5exM5DTLOhq0dcZ4SUKK/aJ0u7td9qaaZWxctPZv8RgCRDkKPRPVKIr\neHfzvbCPSvbJbeY7ogCqowSt7OSMGcd6MS+zEUU5r6OX3ePX4oZPeAlwVAyo\nQ8WNDkD+1D061NCuDyatdyyr62qx1bNZwsEoS7p+y6EeEEFcyK0yWTkb1Ii0\nrCkB\r\n=siRq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnkHHWjnL+X5hHbt7TnnNsW9bcnUjymQjk6IXCEasnKwIhAK4hlGypSf7vIAiZFz8Hv0h+v3IxRqN4WzjTi6whxXyY"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_4.0.3_1617818723912_0.7126177310246005"}, "_hasShrinkwrap": false}, "5.0.8": {"name": "y18n", "version": "5.0.8", "description": "the bare-bones internationalization library used by yargs", "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "type": "module", "module": "./build/lib/index.js", "keywords": ["i18n", "internationalization", "yargs"], "homepage": "https://github.com/yargs/y18n", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./build/index.cjs", "scripts": {"check": "standardx **/*.ts **/*.cjs **/*.mjs", "fix": "standardx --fix **/*.ts **/*.cjs **/*.mjs", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:esm": "c8 --reporter=text --reporter=html mocha test/esm/*.mjs", "posttest": "npm run check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "postcompile": "npm run build:cjs", "build:cjs": "rollup -c", "prepare": "npm run compile"}, "devDependencies": {"@types/node": "^14.6.4", "@wessberg/rollup-plugin-ts": "^1.3.1", "c8": "^7.3.0", "chai": "^4.0.1", "cross-env": "^7.0.2", "gts": "^3.0.0", "mocha": "^8.0.0", "rimraf": "^3.0.2", "rollup": "^2.26.10", "standardx": "^7.0.0", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "standardx": {"ignore": ["build"]}, "gitHead": "e2900d1cdf313663bba012f20737385a42c66b87", "_id": "y18n@5.0.8", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "shasum": "7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55", "tarball": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "fileCount": 9, "unpackedSize": 23435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbgCeCRA9TVsSAnZWagAAe28P/1xPOIpeRSlxJ9fnToSn\n7oM9pOZJa9eijf47kkR2D+Djvh6Wt0QFZNOOF9aHFtTtu1OZKvAnsos9tXiM\n12HsrfHxjSaO1P7uvpYt6CLRfzoFXCOakeImNyIEPTg+K107WhkdP1/9JZ9X\nq/cSQfwyzwEuSoMNWA9u6dAKwwvWKojDz/64eEvsoiLP/e/VANGBYHMtipNt\niLMrZkaRLGKzxlv/g2/E+bAbUaG6Qk3f0VEqOjMMLyoGGDqF0pitoXUpGjeg\nw4u8F0KVuxoI54QOkyJzVO6yoN0knXFZHVIX2ZGRaS7kQyM8uHF4KzNuTnDs\n+FQCJfPAVT5w3qPF7i+NDEy2WS7waGkA4K7Drqto325sFLluguNXSpTsiRTT\nzlEEEDtAclNsBZbEt4WJNjS/7AzhZUDMBS7hf1LbwHLjnK4C8HYV7uSiPESG\nzRyysFcqAe19yQo+N1gK/RL+ObuU/raOpK9WLTwZGc2+yCBy7+4CDGca02dO\nn08B0DTKV2ufTRyDY4uUxxO405Dkk5tWHfFBZYe5vC1Li796MDyOTkzGSwtE\nlqBEo1qqyXKw+erstSkV/Delp1klcFBYiRNspLQOTEtf2NsRlG8Wf6Ess4XB\nLLWS4lnHGl3ZZuxznOg3uEvBsEh/uV+sx2pB30Odw6geVJ7xl2Epm52iE65y\ndl5S\r\n=nxJd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGbHwqJFZjnz9AWpeEVMOweV2g8TS4PkQz2LPe9gUugEAiBPhTbPW09lSJKJ92lpEuiLudAZLZIMKiExkZkcVjyDjw=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/y18n_5.0.8_1617821853833_0.037626956652615284"}, "_hasShrinkwrap": false}}, "readme": "# y18n\n\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nconst __ = require('y18n')().__;\n\nconsole.log(__('my awesome string %s', 'foo'));\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nconst __ = require('y18n')().__;\n\nconst str = 'foo';\n\nconsole.log(__`my awesome string ${str}`);\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nconst __n = require('y18n')().__n;\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'));\n```\n\noutput:\n\n`2 fishes foo`\n\n## Deno Example\n\nAs of `v5` `y18n` supports [Deno](https://github.com/denoland/deno):\n\n```typescript\nimport y18n from \"https://deno.land/x/y18n/deno.ts\";\n\nconst __ = y18n({\n  locale: 'pirate',\n  directory: './test/locales'\n}).__\n\nconsole.info(__`Hi, ${'Ben'} ${'Coe'}!`)\n```\n\nYou will need to run with `--allow-read` to load alternative locales.\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## Supported Node.js Versions\n\nLibraries in this ecosystem make a best effort to track\n[Node.js' release schedule](https://nodejs.org/en/about/releases/). Here's [a\npost on why we think this is important](https://medium.com/the-node-js-collection/maintainers-should-consider-following-node-js-release-schedule-ab08ed4de71a).\n\n## License\n\nISC\n\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "time": {"modified": "2023-07-10T23:16:26.635Z", "created": "2015-07-27T07:19:00.467Z", "1.0.0": "2015-07-27T07:19:00.467Z", "1.1.0": "2015-07-28T04:15:40.553Z", "2.0.0": "2015-07-28T05:07:08.737Z", "3.0.0": "2015-07-29T07:35:44.735Z", "3.1.0": "2015-08-18T22:01:52.657Z", "3.2.0": "2015-09-21T20:58:01.658Z", "3.2.1": "2016-03-17T05:04:31.363Z", "4.0.0": "2017-10-10T19:03:17.301Z", "5.0.0": "2020-09-05T02:35:32.173Z", "5.0.1": "2020-09-05T23:57:59.585Z", "6.0.0-alpha.0": "2020-09-12T00:20:05.591Z", "5.0.2": "2020-10-01T18:23:27.000Z", "5.0.3": "2020-10-16T01:52:15.606Z", "5.0.4": "2020-10-16T15:44:59.622Z", "5.0.5": "2020-10-25T15:18:48.201Z", "4.0.1": "2020-11-30T23:43:34.811Z", "3.2.2": "2021-01-04T22:47:56.362Z", "5.0.6": "2021-04-05T01:26:00.411Z", "4.0.2": "2021-04-07T01:45:04.213Z", "5.0.7": "2021-04-07T01:46:43.112Z", "4.0.3": "2021-04-07T18:05:24.075Z", "5.0.8": "2021-04-07T18:57:33.969Z"}, "homepage": "https://github.com/yargs/y18n", "keywords": ["i18n", "internationalization", "yargs"], "repository": {"type": "git", "url": "git+https://github.com/yargs/y18n.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"slurm": true, "alberh": true, "terrychan": true, "arttse": true, "metaa": true, "zeligzhou": true, "flumpus-dev": true}}