{"_id": "unicode-match-property-ecmascript", "_rev": "9-8c8456b56d5d1ec3c07d66000a0f1ced", "name": "unicode-match-property-ecmascript", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "unicode-match-property-ecmascript", "version": "1.0.0", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names": "^2.0.2", "unicode-property-aliases-ecmascript": "^1.0.1"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests"}, "gitHead": "b6245796e041b9d0ec8c4cb3cbef14a4aec4db69", "_id": "unicode-match-property-ecmascript@1.0.0", "_shasum": "160ca31067f7c537b4b555fd32e0301fcb2607f4", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "160ca31067f7c537b4b555fd32e0301fcb2607f4", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.0.tgz", "integrity": "sha512-CH734lmQxtceTWc/kXO9tbNsieV4GlqcANa+xVyJyGAdcHoawT465Cg5NUHH1iI9XgHdHP85bUADk74pN1JM6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMxjjT+a7W3wbcURApKhyOzk1VVTQo2e2MYMXTtifdGAiEAoo+ApVVIxvnmZnSJXPeC9x/hiHWb2khv89mxtL6d8ZY="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/unicode-match-property-ecmascript-1.0.0.tgz_1492261516050_0.678085901774466"}, "directories": {}}, "1.0.1": {"name": "unicode-match-property-ecmascript", "version": "1.0.1", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.0", "unicode-property-aliases-ecmascript": "^1.0.1"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests"}, "gitHead": "bc7869315df889fe2ce1d4c591b866242964bb61", "_id": "unicode-match-property-ecmascript@1.0.1", "_shasum": "f380567fb895cc649828bea3a524fa34f4feb9da", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "f380567fb895cc649828bea3a524fa34f4feb9da", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.1.tgz", "integrity": "sha512-Ll75WOnLAeFNMr72Oxm71ttSxPnGw8f6jXxHg5dMOquEEdIUP4GMTuXu93Ho0fM/58Jd6PbcUN5/2gmtwI2odQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuog6la2G3O252Sl+XfN06Nx2TtjQulyQ15zYRTPjQZAIhANf2nABXR/3AqJPb1CgoMk4yKu5dKpmBV3FrYs6YHwvz"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/unicode-match-property-ecmascript-1.0.1.tgz_1492261853207_0.37630514497868717"}, "directories": {}}, "1.0.2": {"name": "unicode-match-property-ecmascript", "version": "1.0.2", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.1", "unicode-property-aliases-ecmascript": "^1.0.3"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests"}, "gitHead": "e6ff67e4fddca419ab1dda2ac1425755948bedb1", "_id": "unicode-match-property-ecmascript@1.0.2", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-1jUYUJTBqPjcSd5fzO5u7Q0QlFMgGZWyN8DgQL1VkvhLn3Ai4gAyzIB9o9D03PMJJ2SFoDyE7x8aVl3whPjuZg==", "shasum": "92e57a5bf6c4b6bb401c4ce797880fde8ed12820", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFFhHBC9dN514cmxs8ZMjCVEZrYlgCxfPgiZtxniU/mXAiEA+S6c6YgamB6XsCrJk3V1mUkXx39UtdU3ceO2BNwsN2c="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-match-property-ecmascript-1.0.2.tgz_1497978143644_0.038126759231090546"}, "directories": {}}, "1.0.3": {"name": "unicode-match-property-ecmascript", "version": "1.0.3", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.2", "unicode-property-aliases-ecmascript": "^1.0.3"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests"}, "gitHead": "9694e6dffdaa6cb189ef912a3e9bd44aad1cf745", "_id": "unicode-match-property-ecmascript@1.0.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nFcaBFcr08UQNF15ZgI5ISh3yUnQm7SJRRxwYrL5VYX46pS+6Q7TCTv4zbK+j6/l7rQt0mMiTL2zpmeygny6rA==", "shasum": "db9b1cb4ffc67e0c5583780b1b59370e4cbe97b9", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/YvAmDB0XmE401UUsa+7xNYve1fAWXR9dk+IrAxsCxwIga5SU6/vd2lDeQ9GsbU4Amu3ylgJUaEcxIF93SKja+MQ="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-match-property-ecmascript-1.0.3.tgz_1502964942368_0.4754543693270534"}, "directories": {}}, "1.0.4": {"name": "unicode-match-property-ecmascript", "version": "1.0.4", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests"}, "gitHead": "297bcffae3d154e0691b995e2a3ea28f449eab51", "_id": "unicode-match-property-ecmascript@1.0.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==", "shasum": "8ed2a32569961bce9227d09cd3ffbb8fed5f020c", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz", "fileCount": 4, "unpackedSize": 4407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGS7aCRA9TVsSAnZWagAA/UoQAJ2YtVTo92TbI6uWXG4N\nGvNXizlbSzLVMxm9L/Mdj2BrhsPdEUueZbshZ1X16lOKgG5+uSTYU79lHWRo\npiyFrjhlkAjZNcRSBySXQMLoUtaeIWd0KpFozSYBIQb0RFAesUrlGPDdT/je\njG7FdCKL9Pa5yvgILmB9ckt1Dt1DTBm/Orv6YRe3BTxE1BG7C3+PFfDviz6P\nbXei6fMf5jPrXjVi+K3Me/p1chM+DNzcOqRSgMRnQG+QPbGBOBvXUTmoOC2j\nzCzSyq7BiidgsEmjFXWY31sAo9/kFcxa/JFBsw9+Kq7X/Bn5TdMEXXdJjLkN\nmPuGRdED6WrWvThJeGgC1DHKkZc1STisg2PQSlhdpLFeccd1OS24y6DzZ3In\nmDK+QSmMQXAjN26QbcbchXXCOrkPAhN0EP8GUPEHtY2VGKrEmYZ2jytTDnCv\n8hIded19NaN7yHnONZ+RqVcVxJddTq4bjPBflBLVaonbol0ysHNpOvnCWoUw\n1F/Q/HnDvJbrIRGTaW0rpruhGAkv5tXcoqqJ+6NTwjEdV+OTRqZK4xQUtD+j\nuL74rMjzFayuR9ya73RyEww1Yk4SzOY/EjeF2/86LqkK3JLmkTO0ekLKeA0b\nbWqI32uF40jKMsasNAoPBx/P9AYCTAXji8E1L+KZGpJZWQgenX2GFzT9SN72\nmUeR\r\n=DWwy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxQE2EIzemv6y48Y7E0pDnzZlZ5oDTeOQuTE7c69HuUAiEA2vx6v2Qrq+QTcXt0JGWWbF3Z+XOVzWh9UpuGi9UdVuI="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-match-property-ecmascript_1.0.4_1528377049980_0.5348814037750922"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "unicode-match-property-ecmascript", "version": "2.0.0", "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "devDependencies": {"ava": "*"}, "scripts": {"test": "ava ./tests/*"}, "gitHead": "53be375cfb78a107da07410a43bba52db8fae534", "_id": "unicode-match-property-ecmascript@2.0.0", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==", "shasum": "54fd16e0ecb167cf04cf1f756bdcc92eba7976c3", "tarball": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIYZCRA9TVsSAnZWagAAvfIQAKOq68NI75sUFDFmtRuw\n64pSFSZqzh/y63PnSPvotWOf4hMpDIs4KdZ1U/qATIHQoFrD1fJfJdFYSu1W\niIXYI8Y451hmVxFMCyB+cq8Wg4fDCtf32sy5S0RHzFWEZbDm/OcLyydbWbpU\n7jVxgtaf3CH06pQO+ylbzpXQbp4Gfj4jbCQnUZs/RexKvaH8nNFezMPnNjXk\nRzgFBhgO/3AwAnM7y8xQCg66B6pafHbZZDvqNEo4wGybPqVoDgkvmUsrJn/W\nPGcHP8zUjizY9JW2tV7RYNL5HjFmryjjrfxCO/aUeW7Od1BP5QtwGkvm3qaD\naK/DFctI63cspnVk7O7kNOM2/7wlkib6OkGSd4Nm2ZLETMVvEO7LkNuZrfmu\nf1sxuq4B3p2JKOr5gzhcSq+Au8lQhmUqLDuWBrkK5F169BDDlWuO/ZarQUgr\njlBhW4qlix8YUS/OutDTOsZjlHoWkYucDEuE1Y5F5zKlTsZAlnU+EQI0O9cR\nA43JefSaAl6WXrZ8nfBoVhiiXh2HfRTzyqTVplReOIWqiro/pgkqVWM2OX+y\n/4YbQQXEaAJRguCJueKob9LJCtAm9Ka1EN32fghWUeUT8XPm1XgFzLaNZewg\nKulpMDO4zxyPrFp1fm6YBV/dgEVkJkoUcsfb/JP/yQnogpMSYS0USNOlYt+f\neX+D\r\n=0pmT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClOuL94SA7X+0/+QOkSjq81TIeCiFc8M8tzY62ss/RCQIhAPeCs/ZtCUhsG+tTenlEnciyFxVEjZ9j8wfIfzWy2zvK"}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-match-property-ecmascript_2.0.0_1631618584955_0.409966461919268"}, "_hasShrinkwrap": false}}, "readme": "# unicode-match-property-ecmascript [![Build status](https://travis-ci.org/mathiasbynens/unicode-match-property-ecmascript.svg?branch=main)](https://travis-ci.org/mathiasbynens/unicode-match-property-ecmascript) [![unicode-match-property-ecmascript on npm](https://img.shields.io/npm/v/unicode-match-property-ecmascript)](https://www.npmjs.com/package/unicode-match-property-ecmascript)\n\n_unicode-match-property-ecmascript_ matches a given Unicode property or [property alias](https://github.com/mathiasbynens/unicode-property-aliases-ecmascript) to its canonical property name without applying [loose matching](https://github.com/mathiasbynens/unicode-loose-match) per the algorithm used for [RegExp Unicode property escapes in ECMAScript](https://github.com/tc39/proposal-regexp-unicode-property-escapes). Consider it a strict alternative to loose matching.\n\n## Installation\n\nTo use _unicode-match-property-ecmascript_ programmatically, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\n$ npm install unicode-match-property-ecmascript\n```\n\nThen, `require` it:\n\n```js\nconst matchProperty = require('unicode-match-property-ecmascript');\n```\n\n## API\n\nThis module exports a single function named `matchProperty`.\n\n### `matchProperty(value)`\n\nThis function takes a string `value` and attempts to match it to a canonical Unicode property name. If there’s a match, it returns the canonical property name. Otherwise, it throws an exception.\n\n```js\n// Find the canonical property name:\nmatchProperty('sc')\n// → 'Script'\n\nmatchProperty('Script')\n// → 'Script'\n\nmatchProperty('script') // Note: incorrect casing.\n// → throws\n```\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_unicode-match-property-ecmascript_ is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T04:26:43.201Z", "created": "2017-04-15T13:05:18.196Z", "1.0.0": "2017-04-15T13:05:18.196Z", "1.0.1": "2017-04-15T13:10:53.882Z", "1.0.2": "2017-06-20T17:02:24.659Z", "1.0.3": "2017-08-17T10:15:43.185Z", "1.0.4": "2018-06-07T13:10:50.096Z", "2.0.0": "2021-09-14T11:23:05.092Z"}, "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "keywords": ["unicode", "unicode properties", "unicode property aliases"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "license": "MIT", "readmeFilename": "README.md"}