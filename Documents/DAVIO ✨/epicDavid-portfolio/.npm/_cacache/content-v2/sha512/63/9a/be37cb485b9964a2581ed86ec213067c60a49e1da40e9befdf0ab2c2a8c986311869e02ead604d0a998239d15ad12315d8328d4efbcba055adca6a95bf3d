{"_id": "thenify-all", "_rev": "25-9b3c0ef0bf6495967fe31213be87730d", "name": "thenify-all", "description": "Promisifies all the selected functions in an object", "dist-tags": {"latest": "1.6.0"}, "versions": {"0.0.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "2"}, "devDependencies": {"mocha": "2", "istanbul": "0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@0.0.0", "_shasum": "187d4b3a619730b1abf5ed9fe436542e9aa2ddaf", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "187d4b3a619730b1abf5ed9fe436542e9aa2ddaf", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-0.0.0.tgz", "integrity": "sha512-Fso5vtyjtOdB+MCgEoQ9M0k6C1sLwfLzrCoPqlnF0KnRtWmEzguF2haSXOd95CVUKN9jlOeWhcL16rOD4QZwog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDnjJEZWi+OKUBs285Ewo8Gh6fNhD2cfng4EZKZum7hQAiBDklzCe3/2gkoPQEHgwJsZ0rNWwJ54jz8Se3c5kKZBrw=="}]}, "directories": {}}, "1.0.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "2"}, "devDependencies": {"mocha": "2", "istanbul": "0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "gitHead": "e4560648593a3d83d17349cb94f1a3cbe481b768", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.0.0", "_shasum": "3663df4aed595ff671467c0683cd74350b87e1cf", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "3663df4aed595ff671467c0683cd74350b87e1cf", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.0.0.tgz", "integrity": "sha512-rf/JDFi5232wjzcGOzw6p7qc3XBgHec3y+5DYeUQYgQYohQpl4M0Pq0e2o+74xL7ocYUJEQfxel2dLAHdEzQcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDb2K0nKlfywT35fc1OdNaK/J0ut2xyH97uqiyd/ClaBQIgJsly4pTars35WFt0EZCHJK5WyUKrd4/7CioX0J4qNyQ="}]}, "directories": {}}, "1.1.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "2"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "gitHead": "3f78de0fef6701b4bcd1201d9a6ee1a19a632db1", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.1.0", "_shasum": "8042a6dfc19635bd4448ef7e25ce9e1942c9e4fe", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "8042a6dfc19635bd4448ef7e25ce9e1942c9e4fe", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.1.0.tgz", "integrity": "sha512-XmHhjeMa5jgaG5sSqERpUTbXHLWGXCfRQQeZNuf4nQ5N0EfEHaPUXpfhBW4ovfmdJ+PBJOGzx/5uylxJd327pA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAvTbU5Q6iYaq6JAHNLp90FZ5elemCBiUuInPEvuvIYhAiBZYpwsyyF6VSVz8y+9GO6CJkG4mbmWXe7a+I8Jy+FiiQ=="}]}, "directories": {}}, "1.2.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "3"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "gitHead": "6a2c7fd5f2140c3fd369217c182b8872173cc6d7", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.2.0", "_shasum": "fc61cd182bd4ab8d70a5cd40fa897c9e855a7558", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "fc61cd182bd4ab8d70a5cd40fa897c9e855a7558", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.2.0.tgz", "integrity": "sha512-eCBwNWbddChl6PmzPzuAi9wFkBOTyWoyW752Hiei2noAXOzQL4EumqBqOo4oGAgf+wm2FoMeWcpaaRlj0Bheug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFCJPYUfD4qZXaGxAN0/UM+zQbPRgWkbk1Giu3qhZP7HAiEAqVD6GPKpsQikoBGVishuHFNiOm5EA11NatPyvCm+6o0="}]}, "directories": {}}, "1.3.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "3"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "gitHead": "f0ea2cfcb7ec588b36bb9dd25964ce0bd3475897", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.3.0", "_shasum": "14da427e7a7a6f5bde0101335f02ad2fb0e8a531", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "14da427e7a7a6f5bde0101335f02ad2fb0e8a531", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.3.0.tgz", "integrity": "sha512-qKIS5ui4ysMo1ac1U8ZRT3VzK7yvf5SwOJ8EPGoCpPrxyibl66CRcuHmifr0hT5IByRsGrFJJboAG57FrzDHew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFBJj+BXRqq8vSZHVc62OkCvQeU693hz56xc7cnIdLQWAiA9ceVNwXgXwjg9FwLmTFG5dsnWHNvZUCbhpLdOP3UDjQ=="}]}, "directories": {}}, "1.4.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "3"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "gitHead": "70c20380e9b438b1939df914edccfb06e4e30ed5", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.4.0", "_shasum": "e27c776bcc333c4b5711c113e3a2c41509e9f027", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e27c776bcc333c4b5711c113e3a2c41509e9f027", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.4.0.tgz", "integrity": "sha512-76J/AJ4stUXHrmcbRDJlUpvHXj6sOsQ4I8lXkUu8FwKs078adY5OkOUqH3QrLhcPgr02/QtruAZ5j4bxw0UT0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDC3+8TMFhkPYnxepPhXL/oQs4zKA1O+cR4pZYkOGhBTAIhAPAS//Z+3UvUvUQ9Zsx/e0H5wf/vTldqH6USWlsRUbdB"}]}, "directories": {}}, "1.5.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": "^3.1.0"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "engines": {"node": ">=0.10"}, "gitHead": "88cbe85e6013b820f08e50d6081023efba58de23", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.5.0", "_shasum": "17b81fe5035ae515e27254c2e0ddb8df395cb928", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "dead_horse", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}], "dist": {"shasum": "17b81fe5035ae515e27254c2e0ddb8df395cb928", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.5.0.tgz", "integrity": "sha512-V2vjc/MT0/orW0HVcz7FNW1bjDxPxiYaD2qSTXcsEJruA7rvmU9muXXPdLwwUlYhPKo0Mi6iXnVH0JexYPQsRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfXxrXvStuOlUbWBl3IkmMNJzbkYexHiT9Eyk55Y34pAIgTWVfha0zpyslIsCYho8GyxCNRxg0BRjCIGH0ice0nnM="}]}, "directories": {}}, "1.5.1": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": ">= 3.1.0 < 4"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "engines": {"node": ">=0.10"}, "gitHead": "623a42c15fdacbcba1f5db12b27b0a7f4b26045f", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.5.1", "_shasum": "dd96e62e31c2ac3161edfa12ab9afe3e2692e307", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}], "dist": {"shasum": "dd96e62e31c2ac3161edfa12ab9afe3e2692e307", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.5.1.tgz", "integrity": "sha512-Be/g8gwfprvYjOeSTtMjkPfiK3FUW88R1i8xNWydBlDjp9qIFCz7EleOpPpkMhFCNzd/oR1Lu3LQuZpZQTik8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBxyVdFmm9MR7iUTL0Ehmij//Se/CTo1iVORgHYZ7kf3AiEAhtGqf12tIWKCRW6uo3ASusA9WpnFvAfVWtk7BBPRltQ="}]}, "directories": {}}, "1.6.0": {"name": "thenify-all", "description": "Promisifies all the selected functions in an object", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "dependencies": {"thenify": ">= 3.1.0 < 4"}, "devDependencies": {"bluebird": "2", "istanbul": "0", "mocha": "2"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/.bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["promisify", "promise", "thenify", "then", "es6"], "files": ["index.js"], "engines": {"node": ">=0.8"}, "gitHead": "f436113d8076adb9138c222a61689acdef6f6bd9", "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "homepage": "https://github.com/thenables/thenify-all", "_id": "thenify-all@1.6.0", "_shasum": "1a1918d402d8fc3f98fbf234db0bcc8cc10e9726", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.11.14", "_npmUser": {"name": "dead_horse", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}], "dist": {"shasum": "1a1918d402d8fc3f98fbf234db0bcc8cc10e9726", "tarball": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "integrity": "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHYhEWsuUa2X7n++vTV4hKs0gv2OfK3zineEqa/FfprYAiBrdmZcKlppcIPVJpn+PvcVTNQFqm4sbPUf/xDgh2NsUw=="}]}, "directories": {}}}, "readme": "\n# thenify-all\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Dependency Status][david-image]][david-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n[![Gittip][gittip-image]][gittip-url]\n\nPromisifies all the selected functions in an object.\n\n```js\nvar thenifyAll = require('thenify-all');\n\nvar fs = thenifyAll(require('fs'), {}, [\n  'readFile',\n  'writeFile',\n]);\n\nfs.readFile(__filename).then(function (buffer) {\n  console.log(buffer.toString());\n});\n```\n\n## API\n\n### var obj = thenifyAll(source, [obj], [methods])\n\nPromisifies all the selected functions in an object.\n\n- `source` - the source object for the async functions\n- `obj` - the destination to set all the promisified methods\n- `methods` - an array of method names of `source`\n\n### var obj = thenifyAll.withCallback(source, [obj], [methods])\n\nPromisifies all the selected functions in an object and backward compatible with callback.\n\n- `source` - the source object for the async functions\n- `obj` - the destination to set all the promisified methods\n- `methods` - an array of method names of `source`\n\n### thenifyAll.thenify\n\nExports [thenify](https://github.com/thenables/thenify) this package uses.\n\n[gitter-image]: https://badges.gitter.im/thenables/thenify-all.png\n[gitter-url]: https://gitter.im/thenables/thenify-all\n[npm-image]: https://img.shields.io/npm/v/thenify-all.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/thenify-all\n[github-tag]: http://img.shields.io/github/tag/thenables/thenify-all.svg?style=flat-square\n[github-url]: https://github.com/thenables/thenify-all/tags\n[travis-image]: https://img.shields.io/travis/thenables/thenify-all.svg?style=flat-square\n[travis-url]: https://travis-ci.org/thenables/thenify-all\n[coveralls-image]: https://img.shields.io/coveralls/thenables/thenify-all.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/thenables/thenify-all\n[david-image]: http://img.shields.io/david/thenables/thenify-all.svg?style=flat-square\n[david-url]: https://david-dm.org/thenables/thenify-all\n[license-image]: http://img.shields.io/npm/l/thenify-all.svg?style=flat-square\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/thenify-all.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/thenify-all\n[gittip-image]: https://img.shields.io/gratipay/jonathanong.svg?style=flat-square\n[gittip-url]: https://gratipay.com/jonathanong/\n", "maintainers": [{"email": "<EMAIL>", "name": "dead-horse"}, {"email": "<EMAIL>", "name": "dead_horse"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "time": {"modified": "2023-11-07T07:17:11.586Z", "created": "2014-12-17T06:35:41.052Z", "0.0.0": "2014-12-17T06:35:41.052Z", "1.0.0": "2014-12-17T06:39:57.670Z", "1.1.0": "2014-12-19T18:31:36.501Z", "1.2.0": "2014-12-25T08:20:02.183Z", "1.3.0": "2014-12-25T08:22:27.931Z", "1.4.0": "2014-12-27T20:14:37.412Z", "1.5.0": "2015-01-08T18:21:18.499Z", "1.5.1": "2015-01-08T22:28:33.002Z", "1.6.0": "2015-01-10T17:24:24.668Z"}, "homepage": "https://github.com/thenables/thenify-all", "keywords": ["promisify", "promise", "thenify", "then", "es6"], "repository": {"type": "git", "url": "https://github.com/thenables/thenify-all"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/thenables/thenify-all/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"shanewholloway": true, "zhangaz1": true}}