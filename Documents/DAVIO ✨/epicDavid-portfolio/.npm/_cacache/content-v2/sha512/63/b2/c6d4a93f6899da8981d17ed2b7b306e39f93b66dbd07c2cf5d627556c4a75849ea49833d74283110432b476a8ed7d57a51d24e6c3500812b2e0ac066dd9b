{"_id": "redent", "_rev": "7-7d1614d0300a5429d8ce9c1e050d3794", "name": "redent", "description": "Strip redundant indentation and indent the string", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "redent", "version": "1.0.0", "description": "Strip redundant indentation and indent the string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/redent"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["string", "str", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "dependencies": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "97d9db1dbd5894fe1b14e01040da283b8d53409d", "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "homepage": "https://github.com/sindresorhus/redent", "_id": "redent@1.0.0", "_shasum": "cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde", "tarball": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "integrity": "sha512-qtW5hKzGQZqKoh6JNSD+4lfitfPKGz42e6QwiRmPM5mmKtR0N41AbJRYu0xJi7nhOJ4WDgRkKvAk6tw4WIwR4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDit4QQE5cI8sUffrMrSMaSsca8AEK8blCYcyo03WcmcwIgUmiLvuQp9efN+cqqIAUEnrJqBH1QUtQ15mrInp3LRR8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "redent", "version": "2.0.0", "description": "Strip redundant indentation and indent the string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/redent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["string", "str", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "dependencies": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "90d90c6fe45eb7d5fa317c50af03ebac1b7b281f", "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "homepage": "https://github.com/sindresorhus/redent#readme", "_id": "redent@2.0.0", "_shasum": "c1b2007b42d57eb1389079b3c8333639d5e1ccaa", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c1b2007b42d57eb1389079b3c8333639d5e1ccaa", "tarball": "https://registry.npmjs.org/redent/-/redent-2.0.0.tgz", "integrity": "sha512-XNwrTx77JQCEMXTeb8movBKuK75MgH0RZkujNuDKCezemx/voapl9i2gCSi8WWm8+ox5ycJi1gxF22fR7c0Ciw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWeEg1JYRYlJG8hk1QE8IzcYBC9g4hBBAhqYTUAzyeGQIgJVmZVkBwhKoLKDSCcnSsjy0R8TM6Sl402avi5igDinY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/redent-2.0.0.tgz_1468188510556_0.9630645732395351"}, "directories": {}}, "3.0.0": {"name": "redent", "version": "3.0.0", "description": "Strip redundant indentation and indent the string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/redent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["string", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "3c13601b64ced8957f15b6a4c288afcc97a2f402", "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "homepage": "https://github.com/sindresorhus/redent#readme", "_id": "redent@3.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==", "shasum": "e557b7998316bb53c9f1f56fa626352c6963059f", "tarball": "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwqdhCRA9TVsSAnZWagAAhTQP/iMV3aVn9U3yi574p2v/\nVfaS5GpMhBUmmOYqWlK1JNMCydq5T9oFPyN3MSXPWX89NXXFL/YDQ5ApgFv6\nUvZNFqZ3StFp2v+6vEjNjJxgXmY+O3MsSEg44Hly7k4cy+8kjNOiKTEBZbjB\nmgyFAhHcdvFp2k5wQ8fnqsNypkgAYbs0/v9VXFL8EUa6R2UAML5ZhLKOF98K\nW3HuOK6c86I7eJAMscgMw7reVSkU5Gswip7vJL9FocoZdQPGSluY9YIUHvqd\nWc8254GzEFppe24mj0DhFAvEhNPmOpnFnYj5te/om2BZkntW7kK8DSlMzRrv\nGSEC3kXeV3IdVjhvU+qWsTgbSbrqiwRW75eBjHDiuuGYrmZ5TvAmkyNItr5K\nNDp1cX4m4/oya3Hv2mI1guN0dLY0gxAs0Q9PZUXVtHktyXbTpe8N+DtCp/LX\nHQtK3YpHpGBjmlx8nd7Gfkk5Oxyo+d6b+nGm2MLDGQQVMnqw8WFIsjEhV8vx\ny1kJx1Yfwb7R+d29pn5AHmyE1gi8o0lFQcU/qyHVyrm8B6MFkK1zdMX7RNF2\n+AODxu773DJYVIqFFlbrdZLeK9oWh6+c6h61deVIV/zmlcNl+dH5BGnaz8NS\ncDDDXk+QxINOdmYnAoMM3ZeFF7SSA9DP+iABNIVv8Hi/fy1PE9oYK6f7oPBk\npJWC\r\n=9NvM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfdbKA0Ws1jVORq+G29rSFtHA2n4egAXM8zgFKJWrQYwIhANbXpp5ytqjpNr2vXzWzgk9mS8PsX7G9iXlWL9SBFlRD"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redent_3.0.0_1556260705251_0.1406445244526373"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "redent", "version": "4.0.0", "description": "Strip redundant indentation and indent the string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/redent.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["string", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "dependencies": {"indent-string": "^5.0.0", "strip-indent": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "gitHead": "d1e765ad6b07f7a579b891cab0acbed50c01ecbf", "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "homepage": "https://github.com/sindresorhus/redent#readme", "_id": "redent@4.0.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-tYkDkVVtYkSVhuQ4zBgfvciymHaeuel+zFKXShfDnFP5SyVEP7qo70Rf1jTOTCx3vGNAbnEi/xFkcfQVMIBWag==", "shasum": "0c0ba7caabb24257ab3bb7a4fd95dd1d5c5681f9", "tarball": "https://registry.npmjs.org/redent/-/redent-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkE0gCRA9TVsSAnZWagAAPzYQAIJMbcCNsKSazQ/uhLso\nUStMK7zbVwbeYcOIia4LjrCOGIOCcfqDq0OIKOIVul2odDJYhGEcrhrGdH83\nKHQgCWdAhk46Dql86aQVJxZ2zMJdC5/HUNn7coyPxgg0pErMSejREfMzYSfY\ne07/YyORmvxfogIUg5oalwwvfGBWyjNpt5/vJlgAQmlwF7zSvYG6cIu/kFiR\nnDnwcbWBQjB2VOlq9G813WzJZ6PhE3DgS6tQtT6/0A5VpqaFyuGUY+aOXilP\n+qcJkE66w/rSdgNWe9tpwW0uKy5lPz0u2yuQ2WVmEhdt/RzZ2ta3YzCK4mHm\niqSKiuTTUWJNy87640ac1fUD9D1hOTdy6HjKUDoT2YRoelxbY6LPAbOfAKzV\nq07GKilGlZ8NbjE0KPE84XpcYBwkN2UNzia5KzBwKkCTvxrKZad69NiN8WS/\nulH5znmZ4oKQnnYrxE5jchSFEU+1uQVabpAN22CO4CHS/jP6p0V6opjmB/E1\nNgT0LxRvFIIpW6MRKtMIjhdyWuRnHTE2oJ7AmlvlHfUhXNRLmNM3wH8VS5vF\npgUYhMTVFpwAUBfjrIq2UBp0Dh4GGYETZnOYNB90Rkw5OLqtopZq1J3BJJz8\n4XNW48eXVw39LyK9Z1xyErR0PPlkThpQeXFo6ERiiGPeGX7ZpvafX1oscYvv\nO3Qo\r\n=mylM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICE5+HOzjVyF8sbDCGkM1vcNVZOtCu/IgARr6WkkzS6nAiAlVugua/pnHz9exAiveCDnaRXZPQYbm0tjyk61R+0uiQ=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redent_4.0.0_1620069663879_0.3446326247602982"}, "_hasShrinkwrap": false}}, "readme": "# redent\n\n> [Strip redundant indentation](https://github.com/sindresorhus/strip-indent) and [indent the string](https://github.com/sindresorhus/indent-string)\n\n## Install\n\n```\n$ npm install redent\n```\n\n## Usage\n\n```js\nimport redent from 'redent';\n\nredent('\\n  foo\\n    bar\\n', 1);\n//=> '\\n foo\\n   bar\\n'\n```\n\n## API\n\n### redent(string, count?, options?)\n\n#### string\n\nType: `string`\n\nThe string to normalize indentation.\n\n#### count\n\nType: `number`\\\nDefault: `0`\n\nHow many times you want `options.indent` repeated.\n\n#### options\n\nType: `object`\n\n##### indent\n\nType: `string`\\\nDefault: `' '`\n\nThe string to use for the indent.\n\n##### includeEmptyLines\n\nType: `boolean`\\\nDefault: `false`\n\nAlso indent empty lines.\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-redent?utm_source=npm-redent&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T09:41:12.754Z", "created": "2015-09-29T10:54:47.673Z", "1.0.0": "2015-09-29T10:54:47.673Z", "2.0.0": "2016-07-10T22:08:32.052Z", "3.0.0": "2019-04-26T06:38:25.407Z", "4.0.0": "2021-05-03T19:21:04.019Z"}, "homepage": "https://github.com/sindresorhus/redent#readme", "keywords": ["string", "strip", "trim", "indent", "indentation", "add", "reindent", "normalize", "remove", "whitespace", "space"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/redent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/redent/issues"}, "license": "MIT", "readmeFilename": "readme.md"}