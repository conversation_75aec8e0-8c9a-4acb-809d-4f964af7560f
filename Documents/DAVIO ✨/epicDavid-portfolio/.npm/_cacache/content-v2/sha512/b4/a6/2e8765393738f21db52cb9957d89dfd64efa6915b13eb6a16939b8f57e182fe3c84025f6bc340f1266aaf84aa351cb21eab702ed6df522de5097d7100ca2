{"_id": "std-env", "_rev": "42-126f7c632ddc3779709c58fc3ea2cc93", "name": "std-env", "dist-tags": {"latest": "3.9.0"}, "versions": {"1.0.0": {"name": "std-env", "version": "1.0.0", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@1.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "7435477a5900f38088f2b021dd2dd98679816b0a", "tarball": "https://registry.npmjs.org/std-env/-/std-env-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-ZTQOIr/n2vW4pVkQc4IGKzYRLmm6kNTp6DcYYmpdz1tQSJHa5fSqCIX4BgpLEmFXeQkNknKzqknQv/AS2EAstQ==", "signatures": [{"sig": "MEQCIDYYLqhiJ4845RwZrLEVX6eydkvSfrOjOfKYpNRJhP+AAiA+yaAQVnzYEKEW5XkOT8q3kcxdbhsLNqgA4VRHXxYpaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688}, "main": "index.js", "gitHead": "23dd8e33775d78c45d1b6ba4750589aa10a5ab3c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"is-ci": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_1.0.0_1522432945206_0.8187194418005999", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "std-env", "version": "1.1.0", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@1.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "f89ded0c3facd5b53cd3cda288779ed88d3b87ad", "tarball": "https://registry.npmjs.org/std-env/-/std-env-1.1.0.tgz", "fileCount": 4, "integrity": "sha512-IZqsPq9TfPyAJRBLTJXhnTdsT6euL8e9kBkSTgw+K/u9p4MqYlTC0X8NVIrtGLdahQzw8c3NDVnbrvbu34215A==", "signatures": [{"sig": "MEUCIQD6CqhGdMze/U0LogA4rSvNlVohTEc/fzNzjninYpHS6AIgTQRdvG8LeN2jYKzYHTouiEhkrWbeTZx5TUmqXfsxt10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1742}, "main": "index.js", "gitHead": "7c2e2f35ed94c02dacf1908bf3c191d5e68b0173", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"is-ci": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_1.1.0_1522510094125_0.5520758723625223", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "std-env", "version": "1.2.0", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@1.2.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "da93f9d16f04417ea4ee5f5bca857916948e94f6", "tarball": "https://registry.npmjs.org/std-env/-/std-env-1.2.0.tgz", "fileCount": 4, "integrity": "sha512-zFDYHBJVIxhuDZ56ACnpOpsMFK/NjUQehuqeNmOp6nLenNZrzoNVZCxta9XGYp2TjiaUoW0E85/nXINjTdB2xw==", "signatures": [{"sig": "MEUCIQD9KLLRIoVqR7b1KH0hZ0O3ymXXdeFAfCYWB55xpkialwIgaxXTt4NKYDZzDvxh1q6zbWa7oRm8aZzsqCwBYjtyMQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1754}, "main": "index.js", "gitHead": "5cfcaf3ffa692ee0cbdcdc5950d153afb4e00385", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"is-ci": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_1.2.0_1522780571503_0.2154327786144652", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "std-env", "version": "1.3.0", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@1.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "8ce754a401a61f1ac49c8eb55f2a8c0c63d54954", "tarball": "https://registry.npmjs.org/std-env/-/std-env-1.3.0.tgz", "fileCount": 4, "integrity": "sha512-3uwAVIQQYFiDRBoYuHE/5YnEuOnQjEWvL1ZMKSq+BGFkG2IK9ONFAIYn77h3Kzr3qd0SILi9/5Cu9BbZxaeNnA==", "signatures": [{"sig": "MEQCIBrif3epO3BPcqOF0dWGwzPizvB/4REOB6rVhc7/2ZbZAiA6mdpnPB0SGcGHGwAR5EkeGixGxNso32Wca+0WdvdewA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1813}, "main": "index.js", "gitHead": "7758f3ceadbce652942142722a71bd592f4b0627", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"is-ci": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_1.3.0_1522828504813_0.019224049021076217", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "std-env", "version": "1.3.1", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@1.3.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "4e1758412439e9ece1d437b1b098551911aa44ee", "tarball": "https://registry.npmjs.org/std-env/-/std-env-1.3.1.tgz", "fileCount": 4, "integrity": "sha512-KI2F2pPJpd3lHjng+QLezu0eq+QDtXcv1um016mhOPAJFHKL+09ykK5PUBWta2pZDC8BVV0VPya08A15bUXSLQ==", "signatures": [{"sig": "MEQCIEE3BdgauEpxnbDy+DTNO8YRO1eJiSVY4hXDJRMVzFfOAiAbn10qC+JdT9xyv32L4oyMTkjkC3VDz11kGp20cNrstg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNk5FCRA9TVsSAnZWagAA14kQAIOHq6LGFHBqX1SKoX0Y\nD5UuaJRVDN+ILg7rDLbaPKyYAWdHGD0bcp1nDeGzENEMs+tLddbSpd+/0HFd\nXuiIiL5qyvFU1g1Coo035qss8XizkXHuw/ufE7cp77Rpmq0WH8+ZYksMsybr\neB/wnaeRJP+4kJ2S/iHUmhzGBsy1eo2LfThyQJ3ye5wor0SnMzSqOHgpveZW\nuS3h40EOiRBL68LdC9MG2xgdXnfvrDC0D+vHVoEdfVbPzOMMH21gkKO4vdXl\nsQsyXJXUUoOqWs+peqK6d/tulRJOL6FuXjP655NxQO3Ux0Tbo8xI9cbs6bLX\nq4K3I7rsvKaPHcFEZHtyaMIwJrskBUDyvbguVwllOZ+KlafzO6AlU981Zg/a\nMFnG+tQhqWXdNyIMrwfuXDb/jnrCiJRbJtwNPhTKHIYOhi7XxO+eNqwvRMj1\nHtGIYU1Pq2jG8+YLOlQqEkmEvJrd07v7M9hIYfs7Z2eZyzn1XfDyxz64z55n\nJMC9yHosy8z5hRhKWI4RWxydVh0DQYzJw1dqMCTQQfHnutN8qh1eg00UHB3j\nMfIS2gDwcHsBGy68mqo0GyJ9vqqQ72eXxgvhJjmjMjUvFARFEbdt0Bx5/L9U\nZaxSR58apXH6X3qcvy1mF28J+/fTWk+f8azms6b6aVGm9PmRl8TN/1MnXxEW\nFc6V\r\n=EWl6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "84cccf30a55fb2f43ffc8531f6f319d04a1d6fba", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"is-ci": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_1.3.1_1530285637317_0.7386280370691465", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "std-env", "version": "2.0.0", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@2.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "7332a1e12447a6418d6285eafc839f4e30f8114c", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-UoMZLiIzLaJHy0dkIrltX8etC5j9bL1XFOnNTkQe3svZ8lfgOhvXER5QSl7sBJfkr5oWwQRP0pzKa2Gq6uhk0g==", "signatures": [{"sig": "MEQCIEZ3LsHoyXjFAv+xLFukjQDWi/0XIl0TWXjpN25+R+KXAiA9PjrJVhjUKwOYy1D6jSqkLroDnfpkdHOPG7yTVTVFzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2571}, "main": "index.js", "gitHead": "ad460fd76fa96af4a0f08f025a884ea049ff1060", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.0.0_1538690010780_0.8490736636460419", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "std-env", "version": "2.0.1", "author": {"name": "Pooya Parsa", "email": "<EMAIL>"}, "license": "MIT", "_id": "std-env@2.0.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/pi0/std-env#readme", "bugs": {"url": "https://github.com/pi0/std-env/issues"}, "dist": {"shasum": "3ed456079ced5bc7db474ed62919024b926cec09", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.0.1.tgz", "fileCount": 3, "integrity": "sha512-IQGC73Npd/Ycga2VH+WWn/dNvUnBaMwkrWaJJKAXCr+yHmsJj0T04d4Sp2VRcaBjhIXIe/MDrHoLiHoM8iiJYg==", "signatures": [{"sig": "MEUCIQDJ/8O7U9HeSk5kHhd4LljVwv7LXeTXiv24iXDM58bY+gIgWvwPB0fS/Nd9y5lwCdKjVGjs/zmxUrhwmoubwm8erNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbto31CRA9TVsSAnZWagAA/tYP/0MVJpn4lya4AV89WO/4\nYRX6NUfu42vkptrKNEVbSnvIjUOEw2RWc+GMFK1+s1EIcJnG1UmSxsRqBdvB\nYNXs6P+nPcq/x4tbpasBpxF04I+uS0CyIpIjof6/Tz+b2j/cyH5MGxn5a+eb\nBUdU+caSqCA4WHNvDHwq67K8ew8m0LcXFJhkzNS7da5DS2eqwcfQz0h1Y3TA\nQUcvJMsDWabgAaWC1v2W7RlwjZT9x7WmhZgthJbzpQI56vHbbB8xKLoceZbF\nCOytRMBf+tmVtjCZwjHwp9bBxQfD+6toB+VeouLyK3kFtKc7UjR40riUCbJE\n9AIKjT6Nffzgh6d/oRCY1hEzBSXraT9Xw6RPtGEu1vyLYlxyPiJhuAieIdHL\n3NVLA59Cqfesqk129XxXw527E/Jz6oHpdDHw953evMH6vBQs7eHU/jTULWr+\nwTXQ0swx9cUwLqTkWfN17Wa/abV925o5cDd35OjzYDT4pXOiB1aZ43l54UlB\nB4oZfMLWhOYbiWvXlvzvt+h5zt25Fr3v7y+IcxIb7fm3xBJ2orK40pxIYm4y\nrjPMMRdAuDe99y2TyHx+QLZs2pwFAIC7NEiBgYKYoS5cR4yQS4ZeN27fe9b2\nikU2eFogDczNr6fGbzviokM37fcigzEISo4IBKm0ALBZTr8rKd/QVAy321ue\n6aHH\r\n=Nnhl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a26d8a02776d1409ecf0c8e9768ad38e35dd483d", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pi0/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.0.1_1538690548866_0.284040164956205", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "std-env", "version": "2.0.2", "license": "MIT", "_id": "std-env@2.0.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/blindmedia/std-env#readme", "bugs": {"url": "https://github.com/blindmedia/std-env/issues"}, "dist": {"shasum": "290baa5c905cd4d54f970596923d3f3077adaf06", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.0.2.tgz", "fileCount": 3, "integrity": "sha512-c5ighQ15FdLEXYfeed0Zf2qA+YIIW3NcqsoohqaZvBLRhgSiqq+650JDGLAxu3NmdNU/VlWNTnb9sSZg5Xthgw==", "signatures": [{"sig": "MEQCIBRCsMLtZHrdoVt3itGunOZuTpAyDoSKiuBtq/NQN+nrAiBGCG+kzZ5ITE0FgXTFP+efxG4dn6IS+CvfFU8wg53AMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbto6qCRA9TVsSAnZWagAA0VwP/R8iAlrKJ5JgAqBrG4gL\nkyP//LXOY9e1ZC/yESCL3eSCKI63EQHaXdt601ytYSaRrp7p9jR0vOREKb3/\nm2O9CeZaLOqkGWvR2S/jiOIc+whPqScSgthB59WUslrKTMN5j3vShKb4x5mb\nVzUXenTK3inZhbkanOmvUSjb1ksaegbw7lCa0Lz83JxZ75x2Fpc7Pix0nXYF\n0dxwhmbcO80JNERaIrUmeCxlctlqbdkjkAYiwwlLwq/l/U9aQcrLYRVTPT40\n+wkD547bx0XiCdmTw1xzaRPsvJYVCAFHzHpSQmK5GA2YKTSrVZufvv9vhdt5\nbd3gZhyghpWm8DtYhFoypZ8A3zoWIHSyrvQ4k69AxeT453jN+NAtKsfpzSUr\n4FYDFt6icbBqLHBMlB8uwxHergW/sCym8d7q7y0d9XufbuTQlVWXIBV7l1zx\nO0bTrlx/6ghnkU+W8NRTwZ7WRk6af6Yl4joBmOiIRzI35N/RQBgSlJ0neB7o\nMQDGYXdlP3tRbJHi7Fr6c5oV4JV2i7e/tz7mv6+ndLBWPjh12qG0g/Q7sZIN\nSDo17jKnBkiIKZrOjFYTlm+tbar4mnh0RE7bPZRcMe/dL1heJ+pGdmhM1pMc\nlxi7zO5S+RPDyk+Qx28jwnD6spt1J81RN1qSYgDy6oFluuW6y2fu6SC8JEbS\n8epb\r\n=c5Zv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e62c6f0323e1e9616ad0c9abc6b3f0eb23b23177", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/blindmedia/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "contributes": ["Pooya Parsa <<EMAIL>>"], "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.0.2_1538690729986_0.3870348598091853", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "std-env", "version": "2.1.0", "license": "MIT", "_id": "std-env@2.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/blindmedia/std-env#readme", "bugs": {"url": "https://github.com/blindmedia/std-env/issues"}, "dist": {"shasum": "0eab8ddabf6a27c055cc23120d131b4ae90c9b8f", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.1.0.tgz", "fileCount": 3, "integrity": "sha512-Jb0FHRwR0Ca1IZVPjHI8nRBQngzuza8GYp/Gv02PKBrJ3M+Yrj5exDzVIb8Xwf3x/6P/69XNQAMaed65OyiBzw==", "signatures": [{"sig": "MEUCIQC1dtOvMs/6PF5JPVC9bgUfPKmF1gOBZUGnHgXoXeriOgIgFjgurJaY3lTQq8k0a+iGbpmJMmOvzYYMLDRyCSjG6Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3HHOCRA9TVsSAnZWagAAkBAP/RUK3+WmfodnM8SObZnH\nglTghhvD0llaDqMdXusrTh9+U40yAJ6vNdE2op7G4IhdhLN/QtriME5yr3pa\nZZhfcv5ASzu7Xgovkbuo5RGhhCBsz71sJgRzWcPMsWk0FsCBnQVbupdktcEU\nBL6TNEBFwIw6WqhBTvM6AX3o0AVC1Z/fTSpnoJF+rCe9HBiO8Arwa46Of2qh\nJv18qRT5zeP/RBhqiQGgX8L8Tt8a/eE9J7udVRSDj5ggHS41EnjcwEp9Wf85\nqPSsAJJCEmFUs7m9rF7sEyXbN4XZ5lEdl8NPc1krX7oKAkOVYz/dxRDSjrzA\n9aJMWnvhHJxnS88ExTvrX8JdYMYLLUH23wfYoOYavUW7RrXlAq16fvTcn6bK\n0r2UwAzmzETZY3l/3+TqugkvWdEvxiHIKc5lGPuPGPBZmTSfylXvsiSm3MX8\n51bLbaBiisNCthz8X3afihLf7j3Hv7hB3Sacl75Uz7hG4y2wvHFen/SizHoV\nYb5BmyHrhG4vJn/a8hIa9b6wY71oE1wOvQjY/AJDMeVCa7ETbvwqc5iKRKtT\n+xnidt/m9rEEmn+ITTOOjyIu0xXBhITXTwyZ+5Awi+wsijJMpMHk+9dyEM4f\nPSeVa+QmwfImF4i1lKktM9b9I5YSp9COas4QfThJHFWrVvKuP+5w8xUPzj9N\nU7QQ\r\n=+t1u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2c9498223314256af4c93fd31b6a73b4cfebbc80", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/blindmedia/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "contributes": ["Pooya Parsa <<EMAIL>>"], "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.1.0_1541173709372_0.10367277272503617", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "std-env", "version": "2.1.1", "license": "MIT", "_id": "std-env@2.1.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/blindmedia/std-env#readme", "bugs": {"url": "https://github.com/blindmedia/std-env/issues"}, "dist": {"shasum": "b7429cee321390faf39a07f10f0747eec736a78b", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.1.1.tgz", "fileCount": 3, "integrity": "sha512-EPOQ97Zg5xlQj322LohFaGoUXFSr9VY30mjL5hKXWMcRUf5o6bOF1S1dkbFH1vOXTD3PcUePr3v4oj1RTeXgHw==", "signatures": [{"sig": "MEUCIQDw9V1Ol6megWafv6Kra01GvydWEG9SNms6EsIfTtrlBAIgfobWFCIvw582QsEA2wsHeE8+ckfraMqNVE6TmClxO0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb33jACRA9TVsSAnZWagAAgwAP/2MycL9vLNmP5ln0/olg\npkdJ2EzQXsWQr+mjlO+Fztzh4CpKQMEdC0U+uomSjWFb4qYMAXO06kU1MIh8\nDrAf8eMpjzpikikkfOC+xkM4cAWJ6vDeCI2KYVCD2pGRuq0tsIfEwmfAswnM\njjVPGr+zZD+JvT/Ed9hjsScTAPi8l+hNzwPziaSvhF8lvCFX5+UI1DQuFc8L\nNz2zYQxMvgn5Z60XrgFMMZrRdXW1v2ts19g3AB6epr0ACDQ7BDr5CQEZj32n\nCp9aVa8jxNXu8vrqSzgk7mImVgTc8SlvM5bZymGcI9hOEmzokBvy/OgSsetH\nhehTF7FJHsqMPyAZeUqIMGV6zHeqGTtMXf3KykpYBfUdLPQkVyUa5/rg8VoS\nFbNl0RQqr62mFrjFLn3QTGgttmvnGeT4/H9Hmt2ZkphY20l5tD33U8y+uevF\npKdZMsKn4CghLFpgfP+/rl3IAYd6ruMS6DgRWqaT+AE9RWJtlUgcyrGjNMRM\nbmQiXCKJm2IKwEqiVQucE/247lQ1oFtC0SJASYgHKRJT71ektvY5SNOxv9DD\nhsuWeXBTorAsYRIdc684jLsS+h82j4/fQ7m7BbYiyiZVQlSXGMQ6Lj9Z7iCd\nSaWFsbYeYCqNMTptpYuANdGajvUhjc3cuvtZwYMlRtOAQCd8BQiE4sdZBdyg\ntPrx\r\n=r/+y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d99acc852195989bc8ea585177bd0ca4c201e5a7", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/blindmedia/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "contributes": ["Pooya Parsa <<EMAIL>>"], "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.1.1_1541372095459_0.4813007398827709", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "std-env", "version": "2.2.0", "license": "MIT", "_id": "std-env@2.2.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/jsless/std-env#readme", "bugs": {"url": "https://github.com/jsless/std-env/issues"}, "dist": {"shasum": "2aa62be00b2a2745213add7a5c1e9048508d0ee2", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.2.0.tgz", "fileCount": 3, "integrity": "sha512-DLSvfc9byGwR4Yio45RgtMBFMg9KtaRvqzMQJIyGwcqiMX1YzXvkR+FJBWdP+mQUNU1taGgixgmT2KZeTHptVQ==", "signatures": [{"sig": "MEUCIQCVBhCUaIaTN2pUCh3LgK7dU9CY1b6QilICT+IdAoHDogIgKFdk8YG67v9hjL4C89EiKQHIlhdkDDjbo/zr7828mF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5KuaCRA9TVsSAnZWagAAOXMQAKA4+ANBBjfkRPkXivuc\n7bmHXusTYsZG+0kNVbAhAIyUkxq9Ikh97+K4/PGLnLePsLDomm+AsAt8Yms6\n05sa3znkoKUcJjXBquVXP9DYa/wCHyADfYUHGJmw7CcOFvvvuDbiQo3zSojm\nszEqIYzcyCTpQYr41BVUEsi8xxFvZ9q224DWcEbiQYUIxHM8/6fPeVa7tUa2\n9WqGshIKXP23q1bPFYzBWydQIsxiKirSJjlJ+67SXVyh4rKulda3nhRikCZ2\n6XI5pM3LEkyCAC7CNe+caEma/TxIOi89vK4y6+E0dPx0r9a1EH4r4QwLRTFV\nI1Gve9Dh0boYwvOc7NgQRYHm4ELiaTOUDJcWUW8iOJJRmDdshCtAOWR0U6nf\nKCekaBqzGi+2IN/GTgtQtzx6gWVLt5QddH5cvLDGUFFIh087dY3OhO/dlqp6\nsErVPHWlpVMXOv3qh6duU35VwrQ0zkcw9kHlbBm2yVK8MsoxxThiaGKGDENz\nX0JnYfKgss3TPoEQLVOB7ehzmamasPFYKPvb31+utoD4KUJeLi/8rRAq/J+1\nHQX9avwCgqJcabmHS7nPoA+o3RGGnR4qWOxXmB1bBOZZRLyt9Ls0wFOx1yVC\nqaKr2/AmRX2wdjViZ5KMUJBtLgP3AxmwPZn1Yo7ccEKoIFKWoTEfsSoifqBl\nLSe5\r\n=IclC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9052782de727153fda4a2a22bc5ab695a8204713", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsless/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "contributes": ["Pooya Parsa <<EMAIL>>"], "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.2.0_1541712793934_0.867592351431558", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "std-env", "version": "2.2.1", "license": "MIT", "_id": "std-env@2.2.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/jsless/std-env#readme", "bugs": {"url": "https://github.com/jsless/std-env/issues"}, "dist": {"shasum": "2ffa0fdc9e2263e0004c1211966e960948a40f6b", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.2.1.tgz", "fileCount": 3, "integrity": "sha512-IjYQUinA3lg5re/YMlwlfhqNRTzMZMqE+pezevdcTaHceqx8ngEi1alX9nNCk9Sc81fy1fLDeQoaCzeiW1yBOQ==", "signatures": [{"sig": "MEQCIF4t7EuMkW4LJjKDnZOJaR08nqibAmeCK/3jymgBjxmOAiAi5O5EzEEzswSyu6nJyhvsXu2yPp13l4Xptaci4AIBpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5K5OCRA9TVsSAnZWagAAgskQAKHcnUI18D3YVNwHDXgl\n2SMenB27+c8JFMJgfoN5kFG3hup/axDrK0fO1/K8XPOv87ahemHbnamSzKe5\nbw/UphPaoKj3aYWdwzQ4QHDcEgWT0b4EExmQ9XmCRlIhI74XykrcZeTNYfRW\nJWOKLhHEKl2cWa3ZxVGbQkp722trlSXrJycki2H5jLxM3eBiUEQ4hBKBbc/H\n2HnTzQvQzofrkgAoJLubxy8PTcBomddmnLDNuDFtErF/3j/Ril+RFqQCht/E\nbxzoPhxL4Gq8F/epYbf0/L8lwQzwwxVA97IjUp28Et8qAyTdMQb/8HJ7og8D\nlCltHoVY7JdA3yFIBjjcfTpreMWN2IWy5MNJ5XKIJftrFiGbeWki264XO0Qj\nP4hJpUAnNCuq70jJl7EBeNz4wQZlrScThzb/f+/tlEWEQTobEMtRf8i6DeIU\nvefI6ZnQ0CckWI/N4DvCNP9Wqor3oobIUulTtivkrQnuEFUMxR3T6EyGpR7N\nZAjTsJBPhPSYBul6yKBE1MgdN6pkKNcCOa21cCjvS18vgD1drHg4unQgD93f\nakzuh7DQhpWe08JWCXgMA/9vq1A23ZUTBpgohZ0Lm6LDCocw7j8naBn6Qybc\nIjxSVYcvIhVclnCcBzntQGxVOEoZWtcgSWSFJkP2NjGBdIJm3aX1IJx2YT1R\nyp9l\r\n=vYh7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "1e706f00838d82e678081cc1a0b347d4d59bfce3", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsless/std-env.git", "type": "git"}, "_npmVersion": "6.4.1", "contributes": ["Pooya Parsa <<EMAIL>>"], "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"ci-info": "^1.6.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.2.1_1541713485923_0.2446439932714941", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha": {"name": "std-env", "version": "3.0.0-alpha", "license": "MIT", "_id": "std-env@3.0.0-alpha", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "contributors": [{"name": "Pooya Parsa", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/std-env#readme", "bugs": {"url": "https://github.com/nuxt-contrib/std-env/issues"}, "dist": {"shasum": "f161964da20a8af6a11bdbccfd39a99b5d7728d1", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.0.0-alpha.tgz", "fileCount": 4, "integrity": "sha512-WWaDtGMLaG1AqB8n792lM8zfR38xVw8iFIFGBvQRV4L+4kps54gvluqKcOq2Do2FwXNoKKjUJHRL1xgnz2pN3A==", "signatures": [{"sig": "MEYCIQCLseYYabOYYRGjYqRCCzRWOYXZZCE0x9OhoE6+2KA9DAIhANdRBIZnnvkkq7IvBoeMl1DehKoGJnhV7BuvXFn/C1v/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfI+UMCRA9TVsSAnZWagAAWEMQAJJCh1LrO6mavN7lJ+5X\n17Nf1VoULxTopWS8CwION0GZ3HV0UGjm+Xy2tRp10z7bpk99IY/Es4MX4SrK\nUrwiPcc+LvHkLh5nUuNtNME//gZ3L7PLure9GYVjIitx+CCgaEcpL7kg777H\nhhHQapm1BMTL/89EqmWtd0cvJwQEsASZUgUzuSTFJOmN3e50rw6t28YRpFBw\nGmnf4kiY0TKXqoFHb2y1nRjrarS1JTqdDZi6sLiAN1NF8jI3/iCgjLYoBouL\nDeXqNe2s19h+aHRpSZs8EtavN1Q3TGjQVjl3EzV4o7ng7ppAe+IQADw6wW7k\nTXAQgPUX1ZRgbW0UH8mODV3yZxn+LnWNQL8D46LQk4Qzu0Mkwq5dCqQTJmlh\nz/wlhMFDO8CyeoQzBEExmTJP4QUguR2BtSOsWmXtkXOyIoqRA0zJHIUgkqSf\nj7SLFYWyj5fqOZ6/r1fbcD/y+oMVMF7jgv6np5I0stVe2uCcvRtwLoUQuqTv\n4Lk5SZ5xPkXixs7f+if3VXxoBwpSFsCG9N/0FbdlL2fcecR1ixaR0UzGQIuV\nafpZj/nwlOWWlWjjLL9+SpzK976AkqMkhl9leOmIsG8aa71K40dHOVZNohLG\ngiVZx7IZaS94Oh0pdY/dAR8fpvY4s4J8UKkGmYo2YXh599ORzw+U5pG/vkl9\neXKr\r\n=ye1y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "2431f17cfdeb239caeb7b0de378208e754e8ab78", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "invalid", "repository": {"url": "git+https://github.com/nuxt-contrib/std-env.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"ci-info": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/std-env_3.0.0-alpha_1596187915882_0.00736636446260186", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "std-env", "version": "2.3.0", "license": "MIT", "_id": "std-env@2.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "contributors": [{"name": "Pooya Parsa", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/std-env#readme", "bugs": {"url": "https://github.com/nuxt-contrib/std-env/issues"}, "dist": {"shasum": "66d4a4a4d5224242ed8e43f5d65cfa9095216eee", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.3.0.tgz", "fileCount": 4, "integrity": "sha512-4qT5B45+Kjef2Z6pE0BkskzsH0GO7GrND0wGlTM1ioUe3v0dGYx9ZJH0Aro/YyA8fqQ5EyIKDRjZojJYMFTflw==", "signatures": [{"sig": "MEQCIHAxyElEaURnpCr9jqJmHhgGteTocjgcVjr8szs0b//pAiAS5eANqBP7LKunAhTzXxBKDvLQOcme6WWT9fzvvdNqdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLVhcCRA9TVsSAnZWagAAOowP/0nPu+dUSp2+YHjQUk9u\n8K5nZ+LG+F04ACTlVtTJcqYI8UBpMTtNdZlkOB1b8kgVBJYeTDI16S6HfpIx\neXmY4o6XxgXUx8yLGZ2lX7QvbvZaBdEwoNVhriu/7ZR8RGfX1ycU8LB4x5qu\ntETdC0ifj7LetpWqv7IKz5A4sf60FG5WNZwcz+KVk6QM7gcOHFWvfO3EGsib\nQ8GWrcCwv5qtCa9Xh77sBjNuqdziM0QfxfdsXFFDniQYovMsrE/0qWb4V0uC\nIzlj9KKD5wAvK1Ho+o0aaB0juPvouiEhrEveXCOSvOqEprvtnFTG6hLuj0L2\nGyDVVnc6Jj9ITlT3A6d1Fi15+RidufbRUOLuf/qJP2Sl7E9pmCvUZcw5o/fR\n0B5GNQR/OhUsf+Bt7gRO4RgPppA+Atozcj0MedcGYEA8Ro+CUGDVNqO/0iVN\nZVDVpK0id280MP074qcJEZUeDKEJtqFdN2egZDtAmU/TIk/wEFsdmzAVOBTI\njxgioiWYWQuGa2KmISKLDTC7UoqR6vPHhW9eUI5c0C4yUQSEUJJG0+GMfeNW\nLQIPD0SBoBNZnOtER5sR2LHixFdpWKfZwIBO9ls8joUzdGshIKUHfpCMzPRa\n14GTXlf4x6M/OSV2RT4/Lb+VVefCf1OaWP3nyNOfOf3VQ5Jdvmbv/quVu1LM\n3Zr5\r\n=XRry\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "7740f9c566e6ffa9039c27ff23f9cfb209d9c4f6", "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/std-env.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"ci-info": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.3.0_1613584475628_0.0850589946266962", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "std-env", "version": "2.3.1", "license": "MIT", "_id": "std-env@2.3.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "d42271908819c243f8defc77a140fc1fcee336a1", "tarball": "https://registry.npmjs.org/std-env/-/std-env-2.3.1.tgz", "fileCount": 6, "integrity": "sha512-eOsoKTWnr6C8aWrqJJ2KAReXoa7Vn5Ywyw6uCXgA/xDhxPoaIsBa5aNJmISY04dLwXPBnDHW4diGM7Sn5K4R/g==", "signatures": [{"sig": "MEUCIQDt8a8bnPE1jWCnVIlLapWgSz5fL6lTGuH6i0lPj+pIswIgeTfiW9+504wDWNlBHskEVMcBQisXXX0wzYf3TU4IFzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4212}, "main": "./index.js", "types": "./index.d.ts", "gitHead": "cb2c21d85bc008cf318edddc3c5aea03ad038d81", "scripts": {"release": "standard-version && git push --follow-tags && npm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Detect running environment of the current Node.js process", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"ci-info": "^3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"standard-version": "^9.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_2.3.1_1632917674178_0.08455132344101646", "host": "s3://npm-registry-packages"}}, "3.0.0-0": {"name": "std-env", "version": "3.0.0-0", "license": "MIT", "_id": "std-env@3.0.0-0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "fdcdb2164d508e41c0197647e4bce8d90506a70f", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-wJPchVEw8444c2DnoouWTvx7K0/bR33BamW+WjngbZ7JStGpBvNjCUEWjpInv+T6yuQbB1uBsTauMNssQlq/Bg==", "signatures": [{"sig": "MEUCIQC3iGPjyLb7EDW77aq70DHh06dVffK5qR2HIl9SQ+5PvQIgV2ODfAOaVFDF/Evlx6BrC1rNdU9a+5yNfKSqMd8M5bU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9652}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "836a03cf30a6878ca13e56b9fe7858a5dead1cad", "scripts": {"test": "node test.cjs", "prepack": "unbuild", "release": "yarn test && standard-version && git push --follow-tags && npm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Simplified way to detect current running Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"unbuild": "^0.5.11", "standard-version": "^9.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.0.0-0_1635865314057_0.23325161444368248", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "std-env", "version": "3.0.0", "license": "MIT", "_id": "std-env@3.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "8dbd16bd2aadc18992072e2f5839e897f4ee2733", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-GoFEqAGzhaexp/T01rIiLOK9LHa6HmVwEUyeU4cwdSnOhfxpw9IMeAFi44SHWbCErEs29qEh7vAOUbtUmoycjA==", "signatures": [{"sig": "MEYCIQD5K+mdOJbUHJQQJgtCfbFxTzzmAokcRy5qADCyzlmYtgIhAMZ1jjXwdtXQhzH6F9yvsHM+5klN/QaAxaHocnRQOoSV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10058}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "504c485ceb976367a6afae3e914b3d05bbc3d7e7", "scripts": {"test": "node test.cjs", "prepack": "unbuild", "release": "yarn test && standard-version && git push --follow-tags && npm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"unbuild": "^0.5.11", "standard-version": "^9.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.0.0_1635951835692_0.5891853719389271", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "std-env", "version": "3.0.1", "license": "MIT", "_id": "std-env@3.0.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "bc4cbc0e438610197e34c2d79c3df30b491f5182", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-mC1Ps9l77/97qeOZc+HrOL7TIaOboHqMZ24dGVQrlxFcpPpfCHpH+qfUT7Dz+6mlG8+JPA1KfBQo19iC/+Ngcw==", "signatures": [{"sig": "MEUCIQCcJLCss1iJwRatSzMje5siawphn9CuXqfgGFgTc3TvtgIgd6hD2O5+som678eMeKe2kX4j9C78vV+9G7XsUW9Z848=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10122, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh28DMCRA9TVsSAnZWagAAigQP/RhI4KweXNWFEDOjBak6\nDfGBcRJ3jJ45DsIiP+yEzbE0nPuVq3juODp6mCv8CsZ+5EEotyzozWgh8J0M\nfcDMxdWJvqlbEhybgrHuxXOFfuaDp4ck3Z7qXf0s0LaI+mhF+X0cbDqlfog+\n8ZPnCsha7d/lIzKCIi19OwB9LGlikrLtvuufkrtgwPA9dxNQQMsxjjClTfgv\nJLs0C27dzTW5Lrwp57nM+sJNGpNl9IyRVvwubd2K3tncWv8OhFWwntJqz52T\nW3g7ObHI7zaqEcHPdqn13Qjo3ngPuoFcbWbKk/rJaIuP1N4hSIrvaV7/vk8a\n+zOTs1H0DuBzvBF5OsgNEOCzPW/NsKKkr9Vf07xqPOnjLioKsoYtTMpTBngd\niLKxHUksHznDBSChN5ofWgoFqSViu4K+aPCO1Fd3BHRH0ehHW9tmsDL/R382\nUHY7h9nS4VA7B/9Ya6qgnmVu4wqefBDZHSoSoWdcuv3n2hD9QaRUdZud5V1X\nc9sj/r/nYsCjjWnvKPKdn4ri3HPOyKpdMmeaOE8dZ8bV+SqPN+p7UsBGq1cq\nrK73bpzQ+PZKh+dRZ96elgY5IK6zQGZP3+2lUlOhVdNXkOypxWdw7tse5h1t\nuvNjuieHVGITreRo2NWFaPtkh5vmMtahGkjDasX2UQw0ZN+kI3RrSVbZlBUd\nbsto\r\n=SuI5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "01b79e6920e93692a067f17ec758786ff45bec52", "scripts": {"test": "node test.cjs", "prepack": "unbuild", "release": "yarn test && standard-version && git push --follow-tags && npm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"unbuild": "^0.5.11", "standard-version": "^9.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.0.1_1636124560107_0.32235505059648206", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "std-env", "version": "3.1.0", "license": "MIT", "_id": "std-env@3.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "964f14a3761059d4d6b61865b0fb872c29f8cf2a", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-UuqVLejQtP5JmguTsZpi3wE/+n62T+FAY7LyPMUMfcwzaLziMI5WXo0eeeGxblnh71oXQfIA5I2u9a4/XtoUWw==", "signatures": [{"sig": "MEUCIGB7im9GtGGgnYUPZcCBmWdBbu/pFhdwMs4wEGs131ltAiEAtNCdjMEaUnXlPtK6T2SghEO6Y+ya2gmcvTn/gd3Ck4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWc0LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2Jw//djsu+h9TV89XDB0lfkRNrPBF8f7N9WP2KXb+RiAAuSGRC7lg\r\n4aYMtLiyQh0WAFlkppbPnEBahOWJrUFVt8QmhOJ9vCl7eW5+P7bpupuUEmA5\r\nozI7+i3B3Th/w00kyKUbazbu0YMkbFftbYcLPEiNNseUYVBDwRBp2dU739lW\r\nA2DaTTqeqvPPqEQDiSrH+WIyDe+y3MvrXhmLbrNkaPXvj2eu2t/DcQlAB6V2\r\ntO9fqIj7hr7520HzEfTLDu77DE5uFUTLoNNyx0ez0XixmZZCiuDZOmjQgen1\r\n4bi4MUOFo6VvzRBaJt079Q5r2NvtoeFNt3OqngCuCbmxU2+MnEKrEbcUYmKS\r\n6jh1j+UpL+8t4atWRlV5v5p3s7BMopF47+5CRAX1j9hB1k5x6X2RBY8vBPZP\r\nyqvUGU+hfTVoyLU1xNV2AH/8XdquQBdXqH1H50T/2QBTzy+ds+OAKlByIV0Q\r\nAfWzfnGMGib7pibB8jesphTSqwxjV0ja4IXlTkiEwphi/XBWOiW9niKtmU1h\r\nohqf0hZlXZW79nkFmv7+K6c8jCpfQX3d+YvIRG2AlzMG3ANmXQAV6AWz+7ug\r\nh6zKqNCmLStMxQl/fOMzJR+Xj4Nkexg0oY3gI+OE8QiIUCyl4TROhonOs9Q4\r\nF/DY/jlpZOWAYW/9KX8mnWwfsTif3hZ3kcY=\r\n=V42r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:std-env-3.1.0.tgz", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run", "build": "unbuild", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/Code/std-env/std-env-3.1.0.tgz", "_integrity": "sha512-UuqVLejQtP5JmguTsZpi3wE/+n62T+FAY7LyPMUMfcwzaLziMI5WXo0eeeGxblnh71oXQfIA5I2u9a4/XtoUWw==", "deprecated": "please update to 3.1.1 or later", "repository": "unjs/std-env", "_npmVersion": "8.3.1", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@6.32.7", "devDependencies": {"c8": "^7.11.0", "jiti": "^1.13.0", "eslint": "^8.13.0", "vitest": "^0.9.3", "unbuild": "^0.7.4", "standard-version": "^9.3.2", "@nuxtjs/eslint-config-typescript": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.1.0_1650052362819_0.7221165891198826", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "std-env", "version": "3.1.1", "license": "MIT", "_id": "std-env@3.1.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "1f19c4d3f6278c52efd08a94574a2a8d32b7d092", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.1.1.tgz", "fileCount": 6, "integrity": "sha512-/c645XdExBypL01TpFKiG/3RAa/Qmu+zRi0MwAmrdEkwHNuN0ebo8ccAXBBDa5Z0QOJgBskUIbuCK91x0sCVEw==", "signatures": [{"sig": "MEUCIGHe2/ZurfyeAUZCmrgDcdhMxMYgWAQ1zerXgpERhtMFAiEA4HZrkjDqYlHIKWsHBsOwIv7qhhTLWtvpYpmOAN7VzD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWdjbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Kg//TaVBwjq/rtQJ+cMZ7bwRU+DqNAyNG1pKuuju7rDJpQ+aC+zV\r\nTnIaa7hrCF3hwGV403x10ygLKDJQjd4iFPbnD+HJF7xdFmYZhOiKXkskry4y\r\ndkuE+8Fg446gkRUQchBj3Vz4cSZ5Mu1eE+DVW5BAANJ35fDbGhw58KsoRKzu\r\n7ThXAW4XGUXD4fj6qwIIMSA+GXJ3g4DiQ7nDvlwyDTIgYBOeOP30ExgUj/tH\r\nFy9YIW0CRQTLCS+s+NVoK/mMa0KfdAkZEcII9+WiCErcFdB3c8mhAQ6WyhK+\r\nRy5vSgj4D2b2fi2RvDY5sGIiwuCY2D0eBBCqEYVQy/BHz5DHQH+MJQ4YIYuL\r\ndiTjmIXoq3kfW7WVwuLA6v52/48+DcG3SdDZzjD9KxGr5k97l0qBy7Wyoh+7\r\n/bMvca3pYz46/yfsvNHengcHFEj+2FtxvvrVtC4kw3Yx3kvEdU+ciHei0kHb\r\nW6CLXqa8135de403kwbSv1CjpcxFL24zNNVo00Dq19S967BRo0xA1IUxGsxc\r\nCMpmc91aEn5PrcVSgHapDOHUV313YmnmG3WkKfh0g/k6qi3e4riOOcVj4eRp\r\nfIYpk1YtTRGYDVDMbNTr88sEJXkr8HLx17xEdGWTewvEmMVKruyoe7zC2IV9\r\ni68WEKOE20snRyEY0OLikSH9FKcCwp4zlF0=\r\n=qJpl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:std-env-3.1.1.tgz", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run", "build": "unbuild", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/Code/std-env/std-env-3.1.1.tgz", "_integrity": "sha512-/c645XdExBypL01TpFKiG/3RAa/Qmu+zRi0MwAmrdEkwHNuN0ebo8ccAXBBDa5Z0QOJgBskUIbuCK91x0sCVEw==", "repository": "unjs/std-env", "_npmVersion": "8.3.1", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@6.32.7", "devDependencies": {"c8": "^7.11.0", "jiti": "^1.13.0", "eslint": "^8.13.0", "vitest": "^0.9.3", "unbuild": "^0.7.4", "standard-version": "^9.3.2", "@nuxtjs/eslint-config-typescript": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.1.1_1650055387795_0.3958630098867144", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "std-env", "version": "3.2.1", "license": "MIT", "_id": "std-env@3.2.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "00e260ec3901333537125f81282b9296b00d7304", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.2.1.tgz", "fileCount": 6, "integrity": "sha512-D/uYFWkI/31OrnKmXZqGAGK5GbQRPp/BWA1nuITcc6ICblhhuQUPHS5E2GSCVS7Hwhf4ciq8qsATwBUxv+lI6w==", "signatures": [{"sig": "MEYCIQCicj+q3GSfvQWnHGlRZ2mkguBd37wKxMbAZ2X9bXyvFgIhAOQscQ59dKOm8y+zT0FMjImqbNAg89fPmGMp08T9q4Mv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9piFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQwRAAni7DWYkrPsbVfs3pHgWiyCPvjl8Y9jas+zfRATHCW8GrdaCB\r\nGthO/fAFtu+VB0cn9zuEU8MuUUkBZhjyWhWToDSdwCCXbmKcNUAQBKZE8or0\r\n7MFOffM3En4ioWq8Uz4a/vIrpCSeWNJVbb7JZ6wwFI2d4LrqCbu8Fne0yv+V\r\nzVurQXLHjnnZBfTz+AoywOlU8VdJ8tOcDUdRG2NITx0RGuyd19D0nwJMp9Ts\r\nBvddeD1U6KLff3t3ANF1aL6RD53+PAbA1QWkItSuyRz2QLzZUs9J0nvHGls6\r\nMwpQiquAinSJh+tWHjfEtRtJUs9eTANzAZICztpOpMlAfAuY6i90ZF5sJ2OW\r\n1ZEn6UrWdDFw+NgjE2IcS/iMBKY2nGLNiUSCGzksNiDaolv5AoqMg99PxigG\r\nmNyD15LuZWf2agPgMAahcCNgxlgTqKCR83XW/ODFWMiR3H1ZIT2thF/ZPfye\r\nE7RK+ZCnQmruB0rHuDyFdnk1nhv3M20ZY1yOOfMsgK/AezwDsv9BpZWQW2qb\r\n/WY4N6SmAjunMoovGZFUG8RLRrqds6aTUDoXwVxJimedQNzsbtu0KlwA67Kx\r\ne/VloI4vTBqWXe/THhc9/Btf+s0ZSPdtKqtDCWCkS9m/desWXMr0YvyO6vXV\r\ncBrP7m+jbir/r+Qc/e8sgdBN4D1COxdhFNY=\r\n=fXyO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:std-env-3.2.1.tgz", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run", "build": "unbuild", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/4c78ae89ab9aed093b7f2c7b0a8cd469/std-env-3.2.1.tgz", "_integrity": "sha512-D/uYFWkI/31OrnKmXZqGAGK5GbQRPp/BWA1nuITcc6ICblhhuQUPHS5E2GSCVS7Hwhf4ciq8qsATwBUxv+lI6w==", "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.14.0", "eslint": "^8.21.0", "vitest": "^0.21.1", "unbuild": "^0.8.8", "typescript": "^4.7.4", "@types/node": "^18.7.2", "standard-version": "^9.5.0", "@nuxtjs/eslint-config-typescript": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.2.1_1660328069402_0.5889878728344149", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "std-env", "version": "3.3.0", "license": "MIT", "_id": "std-env@3.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "86b5b5d416c5744b3fdeac6893c2b98196fc1a55", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.3.0.tgz", "fileCount": 6, "integrity": "sha512-cN<PERSON>+VYsXIs5gI6gJipO4qZ8YYT274JHvNnQ1/R/x8Q8mdP0qj0zoMchRXmBNPqp/0eOEhX+3g7g6Fgb7meLIQ==", "signatures": [{"sig": "MEUCIQCR+CzHhtRUd9tKbnHbXGrbK7AtN958VgwVge+vfUQp7QIgB39YAs91W5qNqzsMNKdmteMT9U0MZL9a4bSMbr1/AlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSuAnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqB1xAAnTfjrJbMk7OXABfES/1BBcmhjSYJjCrMqGTayDZA8f4+zYXG\r\nlqJiulsqeg4bpdv2NDMK7vpIURimxujy7XK2D6e197Hev7LdoagT4bnzpKpK\r\nfb9JHGG3Kt/H3Mke2mb5/6x9mwsANtYrdq3kJQ3iqadxHGQDOH1DV0fVLS75\r\nWG2RNliTG6GU4JT0NEVoprMZXUdQe4PXzI291uocO2VgnLkRH45/gOErP8ny\r\nIrqF4mEYil3+7QUvDtpQE/TlyT3HTQt4Zh3U/oq0ZYEhbcXGKpYDoHBfb6uC\r\nk2FmjmO4yQORum5GKaVw2lHSTcp5K9PGhH4My4zB08gM52HrocLnCTjKPA4L\r\ndrnVjM5bSQVgiluSiGKKU3Yv2KwO3JhoGWSCuOOI520j4I1mUw1+hh2PbG8I\r\nIg61+52yZ6ifygVxkyW8iqqdWs/XvK67JqO4y7G1RCQS7LtUcabk6vGQ7g5R\r\n1q5vWQ34ts+BdJ/Vw3bnZbAIu2LIFat4YKlv+SUB53UhWCIpAEL5X+p62TW+\r\nsUoGbNNau3KH+7TRR2XLtzGEdUkJwMx6U4uC9VojGqIvOMHLEGg3jDHPH1CT\r\n34zGd5f/P8vNaDyG21jTeMeAD1uWnmHIoqG1OD6QWyWAuXfwHD3eLN4JkKYC\r\naeE/LBJpfhxfNx2naaCeIFFUEFXdDbYed0U=\r\n=/t/R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:std-env-3.3.0.tgz", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run", "build": "unbuild", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/078d803ae53fbd5ecfeb705c3b74942a/std-env-3.3.0.tgz", "_integrity": "sha512-cN<PERSON>+VYsXIs5gI6gJipO4qZ8YYT274JHvNnQ1/R/x8Q8mdP0qj0zoMchRXmBNPqp/0eOEhX+3g7g6Fgb7meLIQ==", "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.13.4", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.16.0", "eslint": "^8.25.0", "vitest": "^0.24.3", "unbuild": "^0.9.2", "typescript": "^4.8.4", "@types/node": "^18.11.0", "standard-version": "^9.5.0", "@nuxtjs/eslint-config-typescript": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.3.0_1665851431191_0.22436384222105876", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "std-env", "version": "3.3.1", "license": "MIT", "_id": "std-env@3.3.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "93a81835815e618c8aa75e7c8a4dc04f7c314e29", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.3.1.tgz", "fileCount": 6, "integrity": "sha512-3H20QlwQsSm2OvAxWIYhs+j01MzzqwMwGiiO1NQaJYZgJZFPuAbf95/DiKRBSTYIJ2FeGUc+B/6mPGcWP9dO3Q==", "signatures": [{"sig": "MEYCIQCvSr2evzmXWWy3zRNKMukYEORjsw97LsISw+r7WHYxngIhAKaYhiTPVvWXojKafMG3SDhlgrTJbgtjnx/6apHU2qiw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcl5HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTwBAAnY6LZnTqHetO15IUswTsxNqgL3SUcJX5YlRuyoj2dL3FwQyV\r\n+1gkSRQZMfvmUvcoKTVvOKQqhV0q25JmQBgfkFyQHHuBB7ChOT0HJEPtdRNd\r\njeQCat2yvOFKE+SWtMW1Rrplb4fHQxECUp6BL/WvMKYF/6oSTeiLUVUsAxB/\r\nPcETbPfj+ok5znJNCtiMhwSBHll/JgPqwbrX2FcTmYGhzQwZUboIXyPTaNWc\r\nDG0zrEsISF8Svoyu1cN18pXArDO6VIpHofvqxbI4Wv09a0xuNrgXnUe4lD5g\r\nqpPIoteSDedCQy+QGMvjJP5JevcJoeQTRlOKrSQQ5JGaNT8u8A1hzq5/J27w\r\nvsDB1VjyeNcvrkakc+/WAsvQwJBMwR/kMXEGKzDnZ+CPPtBwt4NnKhDqwsYI\r\nc3JhRAAOnEnrSv1VuS/Is6Nma/GnccagE2a4+oPdANk2UyaKqgDEBm0bX6/3\r\nL6U7dlnnj1SPsiarbeuwoddFjF/dg6zZvKZvzeFDPZBZB+ON6jLhKPDUqNt5\r\n8uDOpf2H3CGE0n6mF7+ZZF+s2rPMcHHwVvNS9YSyZMND4MZ9FlI4Spf1fpXx\r\nB//OUHJGHZVVGytRWJ908yTfzhTICyBM7oYPn7oiDCdFHKF7GeWYga451AXl\r\nmBgUqOodaig2k6O63vLpJ8VUWvoEjaoqL2c=\r\n=qxO9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "_from": "file:std-env-3.3.1.tgz", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "vitest", "lint": "eslint --ext .ts .", "test": "pnpm lint && vitest run", "build": "unbuild", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/eb828db339a32f7ba46d126f1024b4d0/std-env-3.3.1.tgz", "_integrity": "sha512-3H20QlwQsSm2OvAxWIYhs+j01MzzqwMwGiiO1NQaJYZgJZFPuAbf95/DiKRBSTYIJ2FeGUc+B/6mPGcWP9dO3Q==", "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.13.4", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.16.0", "eslint": "^8.27.0", "vitest": "^0.25.2", "unbuild": "^0.9.4", "typescript": "^4.8.4", "@types/node": "^18.11.9", "standard-version": "^9.5.0", "eslint-config-unjs": "^0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.3.1_1668439623731_0.15477695546406323", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "std-env", "version": "3.3.2", "license": "MIT", "_id": "std-env@3.3.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "af27343b001616015534292178327b202b9ee955", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.3.2.tgz", "fileCount": 6, "integrity": "sha512-uUZI65yrV2Qva5gqE0+A7uVAvO40iPo6jGhs7s8keRfHCmtg+uB2X6EiLGCI9IgL1J17xGhvoOqSz79lzICPTA==", "signatures": [{"sig": "MEYCIQCyGpA/seGELwBoE3N+YhPpekrmh+x8+9eRPFFEJUlx2QIhAP2x/lMu8N2rSiPBS/Wibb+67qEBfYWTjZ5TWTzmCaNJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2/skACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2iA//dYKXJeCVX4i031cLBbDDcaqh1bu3fu/o0QI1F6rt0aqQEz40\r\nDeaOm0T0DefoIE3Ribtjamm/QTj/7uSnqqM9YTvPyUdHqr46xgR/IAdc6zTO\r\nGOBmuOhluaDiH8Q8+nmRn6yl4mPNbiHThNhE8IR3kyJkfMIy+vsNMrBwNKsx\r\nbTxyus8oFmrqbANhtfbCYe4wOFq0WN9nswf8C9Lt7CpF7mti+c8Y4z9mr+Ka\r\nSXMTTZWmyBTkLoHpmLXQuI0biyaclUeAN2YJ23J6BKHyKpyNyB22fFprgmAj\r\nK/azSsVua/iXwDB1mJZTv2eaHaXybyi8G9Uub37y8Rh0o21D9zR9FyfR2FO6\r\nZHE1ZNY1RsQ0NSZHiRmSZZf2NX4fsAHvk6oXRZpXSoqqUucxqqdOn/9V6cvv\r\nEPjDOPZ2HC3E14JHcUFQJA/BNmc0ym/IVGDSm/Tw3eJ3JeQJTyoumC3dDci5\r\nafjn11AJGoRkhMyr7+ejBRVPqVX6CH6aavW3Rdo4F/hHvsDPj61P2+orYXQA\r\nK3+tSZfZwuHMxJKpnMgmKmFsyZ42pqz3zE2zxQez929leOmlax7filobEECz\r\n49tYzJ2uLb9l534hyoWDVLbM4rbw5RoetYxpYiRgS1g/ubuOIttjy2zIHvWm\r\nLfSOOq6gn0quraGMirJATNqzHKKjh6SjhBs=\r\n=D+G5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "99bc0d0faa210b9a091f691b083b07c3b2676d5b", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.25.0", "devDependencies": {"c8": "^7.12.0", "jiti": "^1.16.2", "eslint": "^8.33.0", "vitest": "^0.28.3", "unbuild": "^1.1.1", "prettier": "^2.8.3", "typescript": "^4.9.5", "@types/node": "^18.11.18", "changelogen": "^0.4.1", "eslint-config-unjs": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.3.2_1675361060252_0.9325336673270148", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "std-env", "version": "3.3.3", "license": "MIT", "_id": "std-env@3.3.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "a54f06eb245fdcfef53d56f3c0251f1d5c3d01fe", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.3.3.tgz", "fileCount": 6, "integrity": "sha512-Rz6yejtVyWnVjC1RFvNmYL10kgjC49EOghxWn0RFqlCHGFpQx+Xe7yW3I4ceK1SGrWIGMjD5Kbue8W/udkbMJg==", "signatures": [{"sig": "MEUCIQCuIVNEM6GHLFBnX9XYYMTYSUw0PpQwIePhDg08jrDOBAIgDh0/BFxw8qszPYNFs0dSMMhjyGzNyHle68/TmMFQNvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUtlvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6BBAAlVVJKXi55SSlZiXhejNXy24FwXB4+/ZfhAFaR86nvt3cxJL0\r\nvNAL1uUA5+mHIJzowe1xsgQI8Tg7VXLhhfUVMEttH1G1bhRVac3uzGyVH0Xh\r\nT8YENoIvxTU4xq1Iv0BeqONmD8pHiNAsbJZbOXPgZvMSLoZyQIo5tzFIudAR\r\nfpOi+Sy29pFs5ShQ3ZQu1ylNzZw0uqAIc/YPk2aRc8O8abH7CFD24RzXNvYa\r\nF0Y2sxMgoRS8lk7pdbDrhIUCwObnod0JJuNT1dz2YnyH1Aki5VENPRVz/tvR\r\nMLzANYwcKsPUg7qZniWe0HJAkym76Eyg4lx2qYoH9h5jvaskmrDv96Iidii8\r\nV0fgVMwfuSY9ruaYfSxzgKtkXyW3a21tclY9n9rZ1mv0KbcGLf4YI/O8o1k+\r\naqAbIaQ+J15IZ4VYKLAooeqq/bvXiYSnL+ipaezeN/OJJrZTCyzRZQTUocGw\r\nNV2pecN40rDwIFppStFq+THCVxBuSwLdM24QL2Cna5EYP+daAOKzBa55cWmS\r\npbt4tIUKR+rx+yrcmVDxGJixnEkJffOsavGyRTUtp5RN+eQX+Ilsz+YuU3sL\r\ni4LqB7QXN1D7l01b43k+5UfU5Jufz5CKXq/+LGRwiqEgDJHL27r/LeaQL2gW\r\nd4xu+EqE/P61aqnDZ9FnQp1tYIfQlPXOdVk=\r\n=qnRG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "53ccabade5b2d04ea2de57f2de0d48139bb40dc7", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Detect current Javascript environment", "directories": {}, "sideEffects": false, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "devDependencies": {"jiti": "^1.18.2", "eslint": "^8.39.0", "vitest": "^0.31.0", "unbuild": "^1.2.1", "prettier": "^2.8.8", "typescript": "^5.0.4", "@types/node": "^18.16.3", "changelogen": "^0.5.3", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.3.3_1683151215761_0.3851399645604623", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "std-env", "version": "3.4.0", "license": "MIT", "_id": "std-env@3.4.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "d7dc7e088016f4c22b12a699479ce6c6da31b274", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.4.0.tgz", "fileCount": 8, "integrity": "sha512-YqHeQIIQ8r1VtUZOTOyjsAXAsjr369SplZ5rlQaiJTBsvodvPSCME7vuz8pnQltbQ0Cw0lyFo5Q8uyNwYQ58Xw==", "signatures": [{"sig": "MEUCIFqer4xU145u+r7WNu4fdlQlR+yIwVycodwayjQVAGTIAiEA1znyAxYVzL03DLJ/62QWz7O46fHdkQ3k1He+iqdYaFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22286}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "f6f40a178216cf90f3759d4dc0fefd91104e098d", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"jiti": "^1.19.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "unbuild": "^2.0.0-rc.0", "prettier": "^3.0.2", "typescript": "^5.1.6", "@types/node": "^20.5.0", "changelogen": "^0.5.4", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.4.0_1692358180103_0.577163792239741", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "std-env", "version": "3.4.1", "license": "MIT", "_id": "std-env@3.4.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "f4cc1eb27c621bf17f476b822f9cbd40a5ea7456", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.4.1.tgz", "fileCount": 8, "integrity": "sha512-8ff1ZsX3NZMNEmDZmINWcFb7CIhPGKZO+TSVytJxJmFzZjAHtAHOR2GuIur7QXlgwY9Md7wNlu4Q7TtSvbu/SA==", "signatures": [{"sig": "MEQCICPMrJbqb3aPPekPEbv902yaE5IciR/bAzfGnfg6J+QNAiAmZaEQ8U3KfX9ObnMGhwhF8F2XRldAJzsEu2zzypV/IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22306}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "afd148900cb474479a0695420cfbff9998f88667", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"jiti": "^1.19.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "unbuild": "^2.0.0-rc.0", "prettier": "^3.0.2", "typescript": "^5.1.6", "@types/node": "^20.5.0", "changelogen": "^0.5.4", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.4.1_1692607537928_0.9406669385149344", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "std-env", "version": "3.4.2", "license": "MIT", "_id": "std-env@3.4.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "3e1dbe7b59c79b03354dbc5cd5addcca7ae9a76f", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.4.2.tgz", "fileCount": 8, "integrity": "sha512-Cw6eJDX9AxEEL0g5pYj8Zx9KXtDf60rxwS2ze0HBanS0aKhj1sBlzcsmg+R0qYy8byFa854/yR2X5ZmBSClVmg==", "signatures": [{"sig": "MEQCIHmsflHZX83J0DEpIE/JX0FZ7RPmr/TxeZu4gzCkrS2cAiA9OYbJh9h569d4ce5worCUk3xOx319mtzsyZ43jEl7Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23126}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "0c3da3b48ce46864eae5c3ee68efcf8653e0e4f2", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"jiti": "^1.19.2", "eslint": "^8.47.0", "rollup": "^3.28.0", "vitest": "^0.34.2", "esbuild": "^0.19.2", "unbuild": "^2.0.0-rc.0", "prettier": "^3.0.2", "typescript": "^5.1.6", "@types/node": "^20.5.0", "changelogen": "^0.5.4", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.4.2_1692611969221_0.6914419098154525", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "std-env", "version": "3.4.3", "license": "MIT", "_id": "std-env@3.4.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "326f11db518db751c83fd58574f449b7c3060910", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.4.3.tgz", "fileCount": 8, "integrity": "sha512-f9aPhy8fYBuMN+sNfakZV18U39PbalgjXG3lLB9WkaYTxijru61wb57V9wxxNthXM5Sd88ETBWi29qLAsHO52Q==", "signatures": [{"sig": "MEUCIHe8gJiaDHvbnto9uOUpKfDQYAKUqzYHaFTSqEVWeuFQAiEAmDLvL/kg+B+D7vJFMMt5rGfY4VWfNjXUSpf0LUzHtDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23233}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "614c5814f1233ba3e0de512648af63771548ff23", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"jiti": "^1.19.3", "eslint": "^8.47.0", "rollup": "^3.28.1", "vitest": "^0.34.2", "esbuild": "^0.19.2", "unbuild": "^2.0.0", "prettier": "^3.0.2", "typescript": "^5.1.6", "@types/node": "^20.5.3", "changelogen": "^0.5.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.4.3_1692734236551_0.6981474485346286", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "std-env", "version": "3.5.0", "license": "MIT", "_id": "std-env@3.5.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "83010c9e29bd99bf6f605df87c19012d82d63b97", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.5.0.tgz", "fileCount": 8, "integrity": "sha512-JGUEaALvL0Mf6JCfYnJOTcobY+Nc7sG/TemDRBqCA0wEr4DER7zDchaaixTlmOxAjG1uRJmX82EQcxwTQTkqVA==", "signatures": [{"sig": "MEYCIQDierizNp1ZQndPLnWT6mtEwr1d98oGt2FzoWo0ZcomIAIhAOEiPyA40dBk55whCwJq3WOT+xL+UQfkt0PFx4qHvdUi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23354}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "e1bfb393f3372997ccb4f7baf69c83e4caa7f5a8", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.10.0", "devDependencies": {"jiti": "^1.21.0", "eslint": "^8.53.0", "rollup": "^4.4.0", "vitest": "^0.34.6", "esbuild": "^0.19.5", "unbuild": "^2.0.0", "prettier": "^3.1.0", "typescript": "^5.2.2", "@types/node": "^20.9.0", "changelogen": "^0.5.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.6"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.5.0_1699922487392_0.5942759821949555", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "std-env", "version": "3.6.0", "license": "MIT", "_id": "std-env@3.6.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "94807562bddc68fa90f2e02c5fd5b6865bb4e98e", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.6.0.tgz", "fileCount": 8, "integrity": "sha512-aFZ19IgVmhdB2uX599ve2kE6BIE3YMnQ6Gp6BURhW/oIzpXGKr878TQfAQZn1+i0Flcc/UKUy1gOlcfaUBCryg==", "signatures": [{"sig": "MEUCIQDD7uyFA9V/Y5ZXfDun/SWMtNYGYG1FKg0ZGgrCrRUTvQIgVRN6VPnPen7EBR260h6IY3fn2CQVJ11J4+uqxYPBlBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23514}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "33d70be2412f3a9070d4dd968467aaa21203e6db", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.11.0", "devDependencies": {"jiti": "^1.21.0", "eslint": "^8.54.0", "rollup": "^4.6.1", "vitest": "^0.34.6", "esbuild": "^0.19.8", "unbuild": "^2.0.0", "prettier": "^3.1.0", "typescript": "^5.3.2", "@types/node": "^20.10.1", "changelogen": "^0.5.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.6"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.6.0_1701426619401_0.9504115779217801", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "std-env", "version": "3.7.0", "license": "MIT", "_id": "std-env@3.7.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "c9f7386ced6ecf13360b6c6c55b8aaa4ef7481d2", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.7.0.tgz", "fileCount": 8, "integrity": "sha512-JPbdCEQLj1w5GilpiHAx3qJvFndqybBysA3qUOnznweH4QbNYUsW/ea8QzSrnh0vNsezMMw5bcVool8lM0gwzg==", "signatures": [{"sig": "MEQCIE210kCWBlLomOoQN25kRI3cUfUXZyZb3YUwBcZcj4gbAiArVPKBGf3/JVAVFfaw+fiDXXH71qD0hTJzek4gTuurqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26227}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "d111128088046cdb08fe305afbcb3dff58faae0f", "scripts": {"dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix --ext .ts . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.12.1", "devDependencies": {"jiti": "^1.21.0", "eslint": "^8.56.0", "rollup": "^4.9.1", "vitest": "^1.1.0", "esbuild": "^0.19.10", "unbuild": "^2.0.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "@types/node": "^20.10.5", "changelogen": "^0.5.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.7.0_1703238401765_0.20492791910111796", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "std-env", "version": "3.8.0", "license": "MIT", "_id": "std-env@3.8.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "b56ffc1baf1a29dcc80a3bdf11d7fca7c315e7d5", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.8.0.tgz", "fileCount": 8, "integrity": "sha512-Bc3YwwCB+OzldMxOXJIIvC6cPRWr/LxOp48CdQTOkPyk/t4JWWJbrilwBd7RJzKV8QW7tJkcgAmeuLLJugl5/w==", "signatures": [{"sig": "MEUCIQCkU0z1+BA6m9khG96MMiF9J0VbKq6EPGYgHeTDuTJszwIgFMcrrMb5rUA69ZicdLTGauXmt9uEmbg9JCC2+zhYp6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25884}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "16c5d9d39d9dea34f6b77e7ffc9059bbb638e802", "scripts": {"dev": "vitest", "lint": "eslint . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.12.3", "devDependencies": {"jiti": "^2.4.0", "eslint": "^9.14.0", "rollup": "^4.24.4", "vitest": "^2.1.4", "esbuild": "^0.24.0", "unbuild": "^2.0.0", "prettier": "^3.3.3", "typescript": "^5.6.3", "@types/node": "^22.9.0", "changelogen": "^0.5.7", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.8.0_1731089674863_0.9517317375174028", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "std-env", "version": "3.8.1", "license": "MIT", "_id": "std-env@3.8.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/std-env#readme", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "dist": {"shasum": "2b81c631c62e3d0b964b87f099b8dcab6c9a5346", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.8.1.tgz", "fileCount": 8, "integrity": "sha512-vj5lIj3Mwf9D79hBkltk5qmkFI+biIKWS2IBxEyEU3AX1tUf7AoL8nSazCOiiqQsGKIq01SClsKEzweu34uwvA==", "signatures": [{"sig": "MEYCIQDmhpXdO5pXAtmmtM/u6T9+t9Yixa3qHqw3Mj+YEUTKxAIhAOx1QaTOQFleknUN36VmmpsKbHc7XTDJNO5mfydsAZxe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25971}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "ab15595debec9e9115a9c1d31bc7597a8e71dbfd", "scripts": {"dev": "vitest", "lint": "eslint . && prettier -c src test", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "build": "unbuild", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint:fix": "eslint --fix . && prettier -w src test", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/std-env.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Runtime agnostic JS utils", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@10.5.2", "devDependencies": {"jiti": "^2.4.2", "eslint": "^9.21.0", "rollup": "^4.34.9", "vitest": "^3.0.7", "esbuild": "^0.25.0", "unbuild": "^3.5.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "@types/node": "^22.13.8", "changelogen": "^0.6.0", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^3.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/std-env_3.8.1_1741005780179_0.5238864282711289", "host": "s3://npm-registry-packages-npm-production"}}, "3.9.0": {"name": "std-env", "version": "3.9.0", "description": "Runtime agnostic JS utils", "repository": {"type": "git", "url": "git+https://github.com/unjs/std-env.git"}, "license": "MIT", "sideEffects": false, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "unbuild", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "changelogen": "^0.6.1", "esbuild": "^0.25.2", "eslint": "^9.23.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "rollup": "^4.39.0", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vitest": "^3.1.1"}, "packageManager": "pnpm@10.7.1", "_id": "std-env@3.9.0", "gitHead": "7e8cb7b1000f313a03d7645c82e2d8608c250dd6", "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "homepage": "https://github.com/unjs/std-env#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==", "shasum": "1a6f7243b339dca4c9fd55e1c7504c77ef23e8f1", "tarball": "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz", "fileCount": 8, "unpackedSize": 26086, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDJMien5I2NMDS5rXAMXAnB0EVK/uVBu/l5Zan88jn4GQIgbdJDd+Ekji8HlYpYuPXRICIx7Eayv7RbCCiFLc2eIks="}]}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/std-env_3.9.0_1743719954778_0.8231437598158966"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-30T18:02:25.205Z", "modified": "2025-04-03T22:39:15.163Z", "1.0.0": "2018-03-30T18:02:25.279Z", "1.1.0": "2018-03-31T15:28:14.212Z", "1.2.0": "2018-04-03T18:36:11.601Z", "1.3.0": "2018-04-04T07:55:04.914Z", "1.3.1": "2018-06-29T15:20:37.393Z", "2.0.0": "2018-10-04T21:53:30.888Z", "2.0.1": "2018-10-04T22:02:28.956Z", "2.0.2": "2018-10-04T22:05:30.223Z", "2.1.0": "2018-11-02T15:48:29.498Z", "2.1.1": "2018-11-04T22:54:55.604Z", "2.2.0": "2018-11-08T21:33:14.048Z", "2.2.1": "2018-11-08T21:44:46.098Z", "3.0.0-alpha": "2020-07-31T09:31:56.016Z", "2.3.0": "2021-02-17T17:54:35.780Z", "2.3.1": "2021-09-29T12:14:34.351Z", "3.0.0-0": "2021-11-02T15:01:54.227Z", "3.0.0": "2021-11-03T15:03:55.905Z", "3.0.1": "2021-11-05T15:02:40.303Z", "3.1.0": "2022-04-15T19:52:43.163Z", "3.1.1": "2022-04-15T20:43:07.960Z", "3.2.1": "2022-08-12T18:14:29.685Z", "3.3.0": "2022-10-15T16:30:31.452Z", "3.3.1": "2022-11-14T15:27:03.904Z", "3.3.2": "2023-02-02T18:04:20.446Z", "3.3.3": "2023-05-03T22:00:15.904Z", "3.4.0": "2023-08-18T11:29:40.290Z", "3.4.1": "2023-08-21T08:45:38.068Z", "3.4.2": "2023-08-21T09:59:29.495Z", "3.4.3": "2023-08-22T19:57:16.729Z", "3.5.0": "2023-11-14T00:41:27.575Z", "3.6.0": "2023-12-01T10:30:19.552Z", "3.7.0": "2023-12-22T09:46:42.007Z", "3.8.0": "2024-11-08T18:14:35.056Z", "3.8.1": "2025-03-03T12:43:00.389Z", "3.9.0": "2025-04-03T22:39:15.007Z"}, "bugs": {"url": "https://github.com/unjs/std-env/issues"}, "license": "MIT", "homepage": "https://github.com/unjs/std-env#readme", "repository": {"type": "git", "url": "git+https://github.com/unjs/std-env.git"}, "description": "Runtime agnostic JS utils", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "readme": "# std-env\n\n[![npm](https://img.shields.io/npm/dm/std-env.svg?style=flat-square)](http://npmjs.com/package/std-env)\n[![npm](https://img.shields.io/npm/v/std-env.svg?style=flat-square)](http://npmjs.com/package/std-env)\n[![bundlephobia](https://img.shields.io/bundlephobia/min/std-env/latest.svg?style=flat-square)](https://bundlephobia.com/result?p=std-env)\n\n> Runtime agnostic JS utils\n\n## Installation\n\n```sh\n# Using npm\nnpm i std-env\n\n# Using pnpm\npnpm i std-env\n\n# Using yarn\nyarn add std-env\n```\n\n## Usage\n\n```js\n// ESM\nimport { env, isDevelopment, isProduction } from \"std-env\";\n\n// CommonJS\nconst { env, isDevelopment, isProduction } = require(\"std-env\");\n```\n\n## Flags\n\n- `hasTTY`\n- `hasWindow`\n- `isDebug`\n- `isDevelopment`\n- `isLinux`\n- `isMacOS`\n- `isMinimal`\n- `isProduction`\n- `isTest`\n- `isWindows`\n- `platform`\n- `isColorSupported`\n- `nodeVersion`\n- `nodeMajorVersion`\n\nYou can read more about how each flag works from [./src/flags.ts](./src/flags.ts).\n\n## Provider Detection\n\n`std-env` can automatically detect the current runtime provider based on environment variables.\n\nYou can use `isCI` and `platform` exports to detect it:\n\n```ts\nimport { isCI, provider, providerInfo } from \"std-env\";\n\nconsole.log({\n  isCI, // true\n  provider, // \"github_actions\"\n  providerInfo, // { name: \"github_actions\", isCI: true }\n});\n```\n\nList of well known providers can be found from [./src/providers.ts](./src/providers.ts).\n\n## Runtime Detection\n\n`std-env` can automatically detect the current JavaScript runtime based on global variables, following the [WinterCG Runtime Keys proposal](https://runtime-keys.proposal.wintercg.org/):\n\n```ts\nimport { runtime, runtimeInfo } from \"std-env\";\n\n// \"\" | \"node\" | \"deno\" | \"bun\" | \"workerd\" ...\nconsole.log(runtime);\n\n// { name: \"node\" }\nconsole.log(runtimeInfo);\n```\n\nYou can also use individual named exports for each runtime detection:\n\n> [!NOTE]\n> When running code in Bun and Deno with Node.js compatibility mode, `isNode` flag will be also `true`, indicating running in a Node.js compatible runtime.\n>\n> Use `runtime === \"node\"` if you need strict check for Node.js runtime.\n\n- `isNode`\n- `isBun`\n- `isDeno`\n- `isNetlify`\n- `isEdgeLight`\n- `isWorkerd`\n- `isFastly`\n\nList of well known providers can be found from [./src/runtimes.ts](./src/runtimes.ts).\n\n## Platform-Agnostic `env`\n\n`std-env` provides a lightweight proxy to access environment variables in a platform agnostic way.\n\n```ts\nimport { env } from \"std-env\";\n```\n\n## Platform-Agnostic `process`\n\n`std-env` provides a lightweight proxy to access [`process`](https://nodejs.org/api/process.html) object in a platform agnostic way.\n\n```ts\nimport { process } from \"std-env\";\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}