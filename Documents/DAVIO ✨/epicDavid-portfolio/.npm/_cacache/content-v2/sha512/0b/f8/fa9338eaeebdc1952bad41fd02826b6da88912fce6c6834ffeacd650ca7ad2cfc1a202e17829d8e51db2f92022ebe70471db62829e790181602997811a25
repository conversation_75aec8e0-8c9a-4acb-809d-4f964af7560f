{"_id": "@babel/preset-react", "_rev": "131-864ba8cf4b0c26f26845bd246d207ed0", "name": "@babel/preset-react", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/preset-react", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c86b4e415240a9adade06452e5ea9a31ea4cc9c0", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.4.tgz", "integrity": "sha512-7xwpnURQ4OHUrx5TljJqimFk9e4hUXhcFTOGcB4wBULKrM/OTekImB0URTY3WGdH3i7twa52W+SN7eFj4CAudg==", "signatures": [{"sig": "MEYCIQCA87T1dJXVHNPhJRAYYKqDc/kVZ5gRNoqRY4sfoihqUQIhAIfM3I0vb3EDioE34NtmOm152KQ9Rw9bZxKtXWr0woIT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.4", "@babel/plugin-transform-react-jsx": "7.0.0-beta.4", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.4", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.4", "@babel/plugin-transform-react-display-name": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.4.tgz_1509388551590_0.23689899896271527", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/preset-react", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7004d5989303f8e47b60b21efc17a14769ea7aee", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.5.tgz", "integrity": "sha512-8hOdfw7ZD0c7pB4h61Gw6RAqtJJjB2jk1Fa5eCaSDSVXWfNKMjxWrQQQI+Pquo922FbTQRO7Pwxc3spR1xIdsg==", "signatures": [{"sig": "MEQCIAhKJb35QsorHs+kzATTGXf+EzD9on6NA93EtWZhkHhuAiAnX10UgQqMbeutPW/YAKYGuWOsY19oc75VFokmP7gxLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.5", "@babel/plugin-transform-react-jsx": "7.0.0-beta.5", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.5", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.5", "@babel/plugin-transform-react-display-name": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.5.tgz_1509397050207_0.3133497382514179", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/preset-react", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1ee40f8e85b8cf1493414e6a4b407178708f8f15", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.31.tgz", "integrity": "sha512-nNQ5KLvVUy2qNuxeAfqsV9aHw9UczN8rf7aXo62oCq7oLeMxbwtwNdg5ArbXxvyGHKtLdupJoK5Xfw2VdZXHgw==", "signatures": [{"sig": "MEYCIQDCmfeNMMQfhzA/zLGDfB1K2k8ykNcYTZYPS8fKHkAMtgIhAM9jv8j8x67NEEb6XXqN6+304j9Yxk8DWND7OG4b2zJe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.31", "@babel/plugin-transform-react-jsx": "7.0.0-beta.31", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.31", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.31", "@babel/plugin-transform-react-display-name": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.31.tgz_1509739453058_0.30586674180813134", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/preset-react", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "16fb4f376da3f620f53f19d061b1569fd08a0519", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.32.tgz", "integrity": "sha512-eYH3LDIz0HtkbifOqubK6iYwm+Rgqt/LwsopxgMOL1QYH1NnyuApnwVmAb2MVh66LJbghVkkvu/1sr/y9JUr6w==", "signatures": [{"sig": "MEYCIQCETDqE2btVERyiGz9wiDrJDOFn6UWAd5fHv+dxhaK/7QIhAMaVp+XJub/ixz08r+MW28w5dTSVjZyDVeJ+qAK716AW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.32", "@babel/plugin-transform-react-jsx": "7.0.0-beta.32", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.32", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.32", "@babel/plugin-transform-react-display-name": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.32.tgz_1510493636158_0.6908331394661218", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/preset-react", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7760b0d0aac5b46d2c93514ff992c263ea4f6b7a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.33.tgz", "integrity": "sha512-1307yYzylJ+0ubgReYoX4g2I2oUrSzmLx3dG2iL1an/Hl8pTt1aXfUBBDxFN2JPR2ZAXBfjiNoVq+rwiNjTXFA==", "signatures": [{"sig": "MEQCIDBFBH7LLxXnrmIQMR0cU2h5UHFVu/ltmCMvbUngBsWuAiBCAREFsUo6T2rgS2VWdjO+UGrUxyGaiw+G1sRJ1ojq7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.33", "@babel/plugin-transform-react-jsx": "7.0.0-beta.33", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.33", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.33", "@babel/plugin-transform-react-display-name": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.33.tgz_1512138553497_0.13610578747466207", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/preset-react", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "548d8bca75c70c24a2ec0ebc2bd59d9f18ddfd7c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.34.tgz", "integrity": "sha512-yITAUEZz7pqlXaIhn2nZZIB2wfjT/uSh5XuSMcWu2eIuUCJP162UYA1yqM6tLZ2dBmxcMmeF9FPL8fc1L1FFIQ==", "signatures": [{"sig": "MEYCIQDnD28nfxV5tUZ+JRD2MkOAtEVRzPfKCH0+dd6+IkWgSQIhANP/FQAnIOiqy1ULEcTx9Vma9jLqyUCmMwT3LsjHHChG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.34", "@babel/plugin-transform-react-jsx": "7.0.0-beta.34", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.34", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.34", "@babel/plugin-transform-react-display-name": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.34.tgz_1512225608819_0.37173172109760344", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/preset-react", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e7c6f6dfc4817c9ec23be81e4886fe80cef11ea9", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.35.tgz", "integrity": "sha512-jiy65aPPkzmZQ6Rrj6vstLeTJVa5EcL9g9GAlZmqsm29TlaHkkGoH8+TZsOT2dfbG4+R6JTC7kEv5u4pfrXWIQ==", "signatures": [{"sig": "MEQCIHF8i48Y2CsuwsbOn3vGiFmysvaVeTMoUiVZkg96Vb+3AiB49/AyvqwP0HBAgyGqok0cctnE5Xl+ECJ1eBbFldyF7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.35", "@babel/plugin-transform-react-jsx": "7.0.0-beta.35", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.35", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.35", "@babel/plugin-transform-react-display-name": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.35.tgz_1513288102852_0.09759292169474065", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/preset-react", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c108d34477ab7110839128a1c4bc27a520ac0ac9", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.36.tgz", "integrity": "sha512-WCQ3H4ZYLeTap8hKFnRfMCu9X5xmZknJYpeiVnSEVAdcC+qwV/m43xHXAnzBb6JPZPPGQeZ51C9c4ydL+krAUg==", "signatures": [{"sig": "MEUCIHFMooC2dHJQuiFDCH/nd3J+oEGO1RuKIYjDWE+3fs+oAiEA2oBbt6xl+NIDd6EqakN7nSsclEvoLkOO7S7IrNEpTVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.36", "@babel/plugin-transform-react-jsx": "7.0.0-beta.36", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.36", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.36", "@babel/plugin-transform-react-display-name": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.36.tgz_1514228730726_0.41837621107697487", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/preset-react", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ef7fadf9c68d8cd9559f1dd3681e21ef334e6e23", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.37.tgz", "integrity": "sha512-9S0abmoTidjwyhco4F+dg/4oD95bl9js7RcD0k3q54kPG4oEyo7RUY8W4x4JlCO5e2BGSjMqeBClO3aP1MMLWw==", "signatures": [{"sig": "MEYCIQCmlmwcWh5Tqg2XwT05PWyA/w3oypqzwY388J0ItGbODgIhAOJOTuWjYMuvmZL+0v1PYOWEAqj+7JhS9x5dxnTCtHhX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.37", "@babel/plugin-transform-react-jsx": "7.0.0-beta.37", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.37", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.37", "@babel/plugin-transform-react-display-name": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.37.tgz_1515427418047_0.07117814640514553", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/preset-react", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "eb42433a72314f14f28be7fe2b092c9a9a3294cb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.38.tgz", "integrity": "sha512-rDNMTPAbq9mw3KRnOgl96vakDxV1bdG5jazJDEWDYBQuANB0P8roGTe7STFW+S/Xq2Dyy5kF4iR3Czz8lq7KuA==", "signatures": [{"sig": "MEQCIEK3v4vZlmGnh+yqlLraQrEWKyXcMpun7BYhehRznhEEAiBm37+vX/qUm+Vhe7KBp4yXlJTErztbDpJDMT3WBqjLEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.38", "@babel/plugin-transform-react-jsx": "7.0.0-beta.38", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.38", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.38", "@babel/plugin-transform-react-display-name": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.38.tgz_1516206755820_0.9381921398453414", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/preset-react", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e2ee52660233016c05fc1f211ab20d1141e848d0", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.39.tgz", "integrity": "sha512-XDOeRVO/qxJrfN4fQXzeUUgLujQTxoHzIUP63phg5KB/avnd03H4TiKNgTBol4X6IvS7z/9/p6SECM5mKH5ZUA==", "signatures": [{"sig": "MEYCIQCOepZd0iQQrYSPMd/PPCgi54kojG51ptJWfegV/LNYnQIhAP1Fxg8kD1GXsK4qmWtWZehNLFt+aGbSL8R1nzYY7n3N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.39", "@babel/plugin-transform-react-jsx": "7.0.0-beta.39", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.39", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.39", "@babel/plugin-transform-react-display-name": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react-7.0.0-beta.39.tgz_1517344121792_0.1582356714643538", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/preset-react", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ccc8f916b694c8ea4b4ccbd1584f873caf199557", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-2xhbtnA5+LtNZsQjF8/naW7R1jnM4PVBRqOyyTyhKdmyWPlkzDtzEPAkKwTUlgnGf947933FSdMToTt+c6H5WA==", "signatures": [{"sig": "MEUCIQCA4mlosOpvHcMoWEzJGg9yH0zFwidCtpazmPvj06lbkgIgOy17i6lJlUWSz+Yt1IRJBtnwOfEXdIUgZvvVELb0c/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5642}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.40", "@babel/plugin-transform-react-jsx": "7.0.0-beta.40", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.40", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.40", "@babel/plugin-transform-react-display-name": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.40_1518453748620_0.9435994207906468", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/preset-react", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7741496d39933c6d8048c448849db15d27ab7428", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-VKPpO8I90++ctnfkwJhaL64rm4ACpTwkI6cu5Q4OMU24UO0eHgL4StCR3Z5nLPpzRqJVgj1HIEzG1HS48YxkJQ==", "signatures": [{"sig": "MEUCIQCEzcrI/7oH0qqb2EQYZPZVlI275+hkw0VnC8yMpl8/EgIgdNa7B6Qz/LirGCBDEl6nnniLDjj1+xi/mtZmEp+rqoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5805}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/plugin-transform-react-jsx": "7.0.0-beta.41", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.41", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.41", "@babel/plugin-transform-react-display-name": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.41_1521044799433_0.5026575872744745", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/preset-react", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e7a15ee1ab5305d5f8efd43cce01123e2bfdcc9d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-2o+5MXAzGMnjlZlxsYv08Joh+T4IDd2NIrpeVB+5GgeboU6k0964pObdZxMjB9A2dOweLSCOHwP21JwO6bDUbA==", "signatures": [{"sig": "MEQCIAZda4o8A7oHr+DMWieZKFTJYqVw5qU/HcsVb4i7HRMJAiBtV7DaAMkF4KfBL6Qw6z+r1PXQ4szNZVmZ9HTWTPLkvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5805}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/plugin-transform-react-jsx": "7.0.0-beta.42", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.42", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.42", "@babel/plugin-transform-react-display-name": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.42_1521147114000_0.46882021818377795", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/preset-react", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c9bb0fb68da200b3de36017d3b1de793af3b7acb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-2NwzLuBMLK4OBZSd0q2df1Y/sPzxh63e556USwjcPuBBLtD4rhCbWmjZMUfu9/7VTo6czgP4Imcsef1Fm4lt+g==", "signatures": [{"sig": "MEQCICaAI1w+BfoXzoDTat1tQmlGAKsh1XBwQYpGumjj81YnAiB52vJuxhVXgilQAVvsO80FPC1wGseEyYthdwHd3lvdHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6448}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/plugin-transform-react-jsx": "7.0.0-beta.43", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.43", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.43", "@babel/plugin-transform-react-display-name": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.43_1522687728111_0.8147958285868819", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/preset-react", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ab57e92f41518f871d82d62791c84383cfd9691a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-0V0x3uE3QoEdw6W6gffy6feBaT3GNg6ALmd8dQwqC4SFF3IwO9a5zHK20JqMjMomXobkLGWMOwQlpWRyNGhVtA==", "signatures": [{"sig": "MEUCIQCmOWteT5bFGb11QnLtXgadGcXTExHayrlvD6Su+h0GWQIgR9hEKtJTODVnk8HzfIAhfjtDp1EiNEhnOxgcTpo+qwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6631}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/plugin-transform-react-jsx": "7.0.0-beta.44", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.44", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.44", "@babel/plugin-transform-react-display-name": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.44_1522707628246_0.020103272999985045", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/preset-react", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "25e108bb3fde531a0d8b44f02ddf45923d3d7fe8", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-iz39MJ8Q1C0c5ZBRiTpWt5LXjId6LrF2QlrffZFIdba6kzq1Xvp8Gc2fxX9MqHYQ3hrcu/uFrR7W2igyEjduKg==", "signatures": [{"sig": "MEQCIHbfM3F1QD7TOCwpR0PSSK/Or1OvpMNbzZeXHADf5hcPAiAt2ir+axDYKtNqjqk7j7GSiXewMHG5GI7wCFseG6KeJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2uCRA9TVsSAnZWagAABdQP/A4UJFU8DqlITuDh/jag\nkl9dNdrTMyVP6reRxJgYrUZ+jezSkudWNsQhwUBPH+ujPlhQy9FI8lhLR779\nuDFDwfSyz4TELHbyg3kCXIQML50NXWW7a9W6FCtjjlvyXFP9VGqLA8VpCUv2\nOkzltnkouuwCvUikjLQVD6WV/91YYqyepZyGQ5KRZcWs48vamBgdsfBp/xad\nmCMQjXG6VX0mSB6TsHUyqShKwNgiQsswcA+lkVG8BSvV8ji9s0dMUnydo7YV\nFbY4I0SQCLzrBcVdtqk5Lm6iO314I4Sb4tqvmq7V7ZxWcjhxSwb23BEljb0W\nWlxCeUyQqJeV6IuRJc4P59Hg5ERsxAOJQqI7W6HpX+E0mVMdVH3OMOwKBKk4\nSEIDw5LPGnMLHxoATmb+u8qyO0xo3Ah/p2RzkjWBYHgn91Vvbrmw67YsJYkZ\nxWnV7hxQjpjU9idcu8HfH3WcDi/tHOgSmOXd90WzkqQSe8fqvARGtTCCPtPe\nP6ksM+pp+5hZIIQBLM1t4qQZ9m9juAgH8yTDvpp+Tfjfn8MfijLVr6oH6Ban\nVLhimNkIhVpCW0PdYxMfg6I/Ux7GmyX6MnniJ+DMePhzQE9bNG1o0HbU8dUi\nEfJ+u7MWejpjOGP7d2QP9m9Q2h6xvlYiEh2ehqRdP+VR7xW6szpElqnG1jBt\nC0Ej\r\n=SKXA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/plugin-transform-react-jsx": "7.0.0-beta.45", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.45", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.45", "@babel/plugin-transform-react-display-name": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.45_1524448686001_0.10837828647647019", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/preset-react", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f2c7f05ce0c9f1bf25516f1acaf00ca0dfc1bfa5", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-+Xl9AH/rhI7FTX0OxNXM4oGljpeCs1nwPm2IjD9+mXO/+dWdGpa+yTcjYYpIWAK4KQrSQ+27T6DtJ6fZPTqYQA==", "signatures": [{"sig": "MEUCIFTENjmMIa/BLE719A/1FZrybfnmaKklMg58RH5utwyhAiEAhnbyFWQywikIms9UFL1JPXkrfjumGvEeEzeFXqp/4ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHXCRA9TVsSAnZWagAAuzMQAKP0r51RXUtX/cRKlgFj\nTzuvpoIAXrXx9hDhU3NRX+BsSyIw86lXeYApqAPoCOLvcEjSj6iCsMGKhcM7\nArwuukangkxU8O3Q4AnsOEH5wZBofybBTFA9PPc5LGxL61Y1ZzbYY6NR1PYT\nxt7Se9I4sIEwRu6cMigatGAVBNIrGKrp4ftvkxXwRR8IoefEi5o+S3DOPXga\no2xPywFI4fBqhNJsbPHSLvFieZc5FqNYgUzpn88ykFp4SkuBEDMum9QBzW4E\nLIs3LfpdvgfSxfeTNVYLGKk9crYG8SN20GDabrYaDLSK4A91wn5Cg0OHkqfp\nyC5bqbH3nUM4DD2CmlKnrM6dtPXIrXN2gk0sGXXaE8BwFBk0oJH3YBXXJzM7\nMMUSDLP9EP81fhygt6f2JMZkAwqSVN/YqrEZhkaHlpZxYU8lZx+mve2hpoD0\nnf/sC+dQz665lSNB5a1l9E8oD7gzNyzvHv5365aiISP+Cheu6jTYmN1WUJXo\nrR+Hhjt9hIXP9jSZ8vXGvD1UKyzgPglB96Xxz7GvOvbfwTTRLFodHuVl9IiA\nBLESbDWO0ty9g13E9/7bUW24Mvl6oY7/BHsKn5QKT50J7cSr2Wt9qpY1iK47\nfeuPh3icg+qBEFUgO17FCAEQkO5x+4/wxa562tU2ZFGyz2JGmYsMJe9M42wd\nRvUd\r\n=lfiI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/plugin-transform-react-jsx": "7.0.0-beta.46", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.46", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.46", "@babel/plugin-transform-react-display-name": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.46_1524457942967_0.07993074168664305", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/preset-react", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "888bd3b7e1caffa89cdd639687227c51bd0a2e99", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-fK4B101W19JsOc8E/wqmQ8R7gE1+DQJqIWvpaQIXPHgW+27ezq2JP5j5IBCk+9OTz007l7se00kMvvtUcKQZwg==", "signatures": [{"sig": "MEUCIQCDeK/9wL52rNsXCRywMgBTrTOB7/WXjnXNE2W2B0EF/QIgVl//ofkrYc4UulaDVrhc3xeaWSOZ9oftfBODmmXaKww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icTCRA9TVsSAnZWagAAnUEP/28yc6s/qecm4LL3SgLX\nRs2yGWpTE1ozN/W9DnEHLAYnM1OkrdPQ4B35LP7D3lKR4U9x5WPHSNUF0ztE\n53Cy2NrAlmN19jKNxEfE7RWDzuJ36cbQl9wvozFh9Ct1DpAQ1hBmKLTpN3+z\n5Pkd69uThXO/Dc1CL4vtpig5sQygzzNaa80NwZBPp5pz4XyX0eLpJ4tLk0hG\nxmaEDFm1NDrskc0n1faStFo7LCIvwJG8qKZKiWQKaoX3hjM0UQa8ESkb03Sp\nMFTz6OZxsaN1AyyOcFmTHMqpnLJ5WnQEXoxKk1JzDt6zx3kzsGrC4+94l7ol\n5NCIMXVsTAaKFnvzdTJndvzHABdomGn71rkOi4BVhEjXXJIfCq9o5D8YGJC0\nMFj+N/IalHEaKgjZ4MKTRl10Z6QkZhjjloZzYUc1HmMil3h0/Nu0qkQotfRw\n2m5OftdOD4vMNqViEthpmXz0wCT+aMW/xYvgfZVfEcvggK5w1IJBYulPzx6m\ndXp2wqT/K78/jOtbvL3TWKaNnuAnrDo/qosyu0HqawWlzIipNNcENEJ9NefJ\n1v0YZxWxPAlz4ts/YaTyvKiGhMkSJoD0lPFzcl5kwTTJtEv3+tslGFSeejMB\nM9lBPzA9okEb9VXn51F7t/ESmMclphUOB4krWSKBAo5zHMZ+5Iz/uql1TNep\nzTF/\r\n=eUi3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/plugin-transform-react-jsx": "7.0.0-beta.47", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.47", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.47", "@babel/plugin-transform-react-display-name": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.47_1526343442751_0.2819381949106512", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/preset-react", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bc9e38d9b8d9987ea661c2b38055c2d0d20abccb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-y96O9njnFUzEo31cQ0ZQ52veoOKSOmL71al/XfE/42IvTRdIIxIRtQr1YqcYbFOzFrL7MZtEkMqh5dDu+J6bXA==", "signatures": [{"sig": "MEUCIAj1OdmR8i7LWvlMo7+6d/wdtoibvv9z7OONZj7RopbPAiEA/bl/2QlUjLntR8NsfFlu697x7cJ6/vzdyO3PNU3rgfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFkCRA9TVsSAnZWagAAAwIP/0dqNfpK61UDYGnX5/sO\nOaCKcPoT1pqvCh5fNS2EUMdBmSJd8udr3MQSu4nilDqU6cQTlmy0RvmQYPrt\nsdEAWIS8qiDcspiItTSzCFcy0jdJOVgoEgymn3bOgepFZzWZ8WU+LrYiwAlK\nIc1WD+lEPo5CiZcSLuj/lk9xFKXA69F8BUNLcfwbPvyTx1ZGGIiPKZ6S6txP\nhNRT5rO3py2Y/fQvTtCcmF6G+9JgHvTIdY13WD+Ke8piIge9wglekUV1ZZ0j\n+WDG4yY2/klAp8UfBgesmaUNbuHQznfbPC0cddm2zdyx4BX+/cNX3k+cE6QM\nETLrWyWZbHvLPBR1w+v/7euwJGo/eS11BY+LrxjGAedbu4KuUMURNtIUeF+g\nqusbfdDSmiYi91jJo04XGt9ohvqghLBTuFypmezK6uYznO7Mw92c6bRQnk5X\nSichk/9kFkOUeJ15m0dNe0orJFnwe7Fw+PL7mntBhT7rQTCA72KTw26ssWdG\n+hqGG3BPsIvaCL60locljr0D5X021TA+gJsogRRF8TsMLmhNNY/6i/01VV3x\nP2W8p0DSscXcY0je6kZ9pYijCV8DJs+ayoivipims/t0muKYIUlXKGTvcT5x\new8+YtAXtEgMMAK74tgAh+elnPnodPWmIEwkI27Jwz9MTVgIY0POw+HFlPqe\nWqH1\r\n=xVja\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/plugin-transform-react-jsx": "7.0.0-beta.48", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.48", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.48", "@babel/plugin-transform-react-display-name": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.48_1527189859450_0.6972923305330734", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/preset-react", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0c86770f6e78a49af6f86942f5980beb5feb76c5", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-27dIvgJcOYpFADFyGDFkmFMkzNbs5EajeChD6OyOuEIeXVrajQrB2rtZl52qImGDWBrdS6Tupdczrsvv4cej3w==", "signatures": [{"sig": "MEUCIBOb9p6qmLYB2hpnRMlJWVEI2n4nOSA47t3cPg4D8rMTAiEAh2jchZWHQIzVAfQA0336td0FE7hHGnm7ZNg9wiCyykQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPqCRA9TVsSAnZWagAAlvYP+wdMITQgHRFcFtFxOFbR\nU+qZ/THaVQc30vPTs7uSPYF99v58h/+4GRoJFMPMMrIBoJP8amO4RA+0tjoB\n6j282V+BWLtBgL4qjIzP3XFW+G/KAdqtBMwATG5ZsmZCZCr6AGmM60czLTwS\nmpgqed+TSvw4V8loFHj+zYC59VNNeFzJ3GEO1dVO9Fq/uKMjFA+Vbqiw6PLr\nGK7bbIb0H/fGg4x3R8lIk7Z4PdhrVXxRGGzmvXNcDoOXB2uIaGbGPyCmyZ0g\nbB02VFSgWomq8GDmGep6J6aZtL8uEOMNyxNGisU/omV9zi0LTnw5iyMG3FSR\nLYxEeIaNKcG8Cp0BabkooT3nLsLmPNvksxQRkzO2c5oLLcusVG6iX7QhOGvK\nOkauF2k+j+utOBTCOmGTapFZl2O9D2xo+yTJ9NbLMx2b0v4oMWECTQyFuAlL\nSMcPtLg3nUe1vDrcp527fMnHbdiyu3dERinHxY2LlKxc3h8zjCePi64fYNa3\nN6I+ft7wvso9/rUqDJBIKcHWc7Fp9rETD/ef+8Nvh1PETBqhsTkdvkCMkuE/\nyaMNBkpwLBANVeqNhdqTX9T41uZXEXNSlNqDaGlmHE5Otf06bfZGWvn316Bx\nylgQabwZgyxYoAfvjFYg+5mmPaFXr2RL70tSEjLEcClpZ1aAWslrHQVT5pfg\nlwU6\r\n=La8L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "0c86770f6e78a49af6f86942f5980beb5feb76c5", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "3.10.10", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/plugin-transform-react-jsx": "7.0.0-beta.49", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.49", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.49", "@babel/plugin-transform-react-display-name": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.49_1527264234183_0.7313886851679865", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/preset-react", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1767dbb664d1ff38e818e208ea18db5a077bcc87", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-UfaIxuWaJEgg+sVplemOFXlruPyDZawZj28rDARCBS9smp55SDCnM20mJi0+RJ0DkQWwgqpacFaGfv6VMV73RA==", "signatures": [{"sig": "MEYCIQDzhe7q/ri+AbXIKZBm1I2zmZslNabZVZPrtKV79HWI+gIhAPq4lV5eysJ1TtrgngvHQOJM0oSAhLWwdaYnylendJOD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3529}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/plugin-transform-react-jsx": "7.0.0-beta.50", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.50", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.50", "@babel/plugin-transform-react-display-name": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.50_1528832870344_0.45308173477045655", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/preset-react", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "957d812a86d96c89214928b79800748f51935e49", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-LQbKKRgADYM5qbnk9qJ4eF5KafweY/FdmOyUTlq9CgiYxYO70FI1CNEP8mzVGJcf31HqvPmm3wKVnMUV+WXtBA==", "signatures": [{"sig": "MEYCIQC8gIV45YlnSH3yMeq8i8VFa5zo1qpnWZZ6z9SpBUW3bQIhAKfQdou0mHCvwSCVSzlOMUjZHPuu9Xvdqh561XFEYspB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3543}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/plugin-transform-react-jsx": "7.0.0-beta.51", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.51", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.51", "@babel/plugin-transform-react-display-name": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.51_1528838431463_0.2957119863901596", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/preset-react", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3bd763d744f728a967a6c617c2f0fe8f5fdf6681", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-I0jzxoxVR5+5aM4Jt0tirc6Ys+QfRPUGIcKv7PrRAVh2L9dEXJltgsNM61QL/jBQIF/2LCuVrvYTy4Cw8Qf5kA==", "signatures": [{"sig": "MEUCICpGDnWQgNWkpTwvRNulb72tIlXpzmTXfsq74ac7cw0NAiEAo4mYNokW0z2iDOIwiASg+ETbAEtF0nnj97hchTe7190=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/plugin-transform-react-jsx": "7.0.0-beta.52", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.52", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.52", "@babel/plugin-transform-react-display-name": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.52_1530838781520_0.06433697589786003", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/preset-react", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3920682830a5caef5c019780282578525897089c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-pZOD/jNWimXaH7ia2Tq3BMFfDQpdaN6YoCkbl5MFt6REcm6LJUxCVKZoDb9s5i4NtRM8mHArmOzXSkMaMzfw/Q==", "signatures": [{"sig": "MEQCIHMu0Rc/Pys7+AtwYR/YSMxJcG2Nq3ufj80dtfLOlFpwAiBpn373RH1lWsX3b4QJvRoutTbCKbYiiIx+8K95rRGNzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/plugin-transform-react-jsx": "7.0.0-beta.53", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.53", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.53", "@babel/plugin-transform-react-display-name": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.53_1531316440512_0.20129703041983804", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/preset-react", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "98021037a74b27b46a46d3b28e84a1f2f406dadb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-CbtkA9Y4YGO+NSEB7dTQwHG9fAsnjGtXQuNXuf/Kn0IutDZgUSQHLn4hh2BD01qWocuocHsOj4UI1qFgBTpHQw==", "signatures": [{"sig": "MEQCIHuK97LqgkkLnCT3R/hbTQSQdfKbdEtJLeWzRjWes2GOAiAJ+9mFOaMYHpYcBXtClzJVYXQ3d1lGJ2WVTb8wSPc+Dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/plugin-transform-react-jsx": "7.0.0-beta.54", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.54", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.54", "@babel/plugin-transform-react-display-name": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.54_1531764022517_0.20733721657520343", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/preset-react", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "80778064882852bcbc812ecb67736b4a81a2fe6c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-7gIldZvgC6KZk3Oq4Wbk9lLyV6OgIp8P+ZuYtRfiHgzBeWYSATPo/VWclUx1KSKEv2AIKrHT6bMak62HCj+jyQ==", "signatures": [{"sig": "MEQCIEZ9dmm8vAYOzVcyqMBsDzrpRDbaUQeZUn0GJCEtO4YZAiBMWQ/KO6HrJo25aURNe6sD1Sd/ASnS0iM7scA1dE4K/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/plugin-transform-react-jsx": "7.0.0-beta.55", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.55", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.55", "@babel/plugin-transform-react-display-name": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.55_1532815667692_0.3958650829515509", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/preset-react", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1c0914d968f3ff4d11bf91fd86857b965c528bb3", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-xWj8x03hp8ZDghjlA81Yodx8EF6yjOgqFH5HVJmUAj7jjF1PrsNNCjACcz7mOBtKsOsv95jtKz/Rc3CcLeHqgw==", "signatures": [{"sig": "MEUCIFBk4KNLb+30O4zhDcn0A/kCV1hC1tHMRpNZKCOtvYtsAiEAlP7ht/y6f2i0dqGO5SUTSbBTmEmmPhs+KBH+wBMPwa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxsCRA9TVsSAnZWagAA7AEP/iqLqqB2ERTBQ0QJCzna\n/j7BOL/UuFSvyv6bE02Ie6qY2teZAcWo6OY0XMJmILHAhNIKZHAEQ76GovUF\nJyQh/fxGGlBYY2J/F09F0T0xx9wr79TwadD1WHpTVLU4n1t4aLpd2nKxmWPT\n9VSPybVcgJRWjnrKgRnW6zQoQJZYOwfhiCAvUD3D5ukvMdB4Io1Z6vA1s8Ry\nfpyLitRcdz+n/w7t1sQb1BKwusDDKUMZmM1e0ydqDug7+Df5Gn4euETrfoJ3\nPX1QFX/PWYImacvqnUHo/oELyoTiZbSIEDj0878Msod5mTsJFo+88k4k1JgH\n+n3gwq1RfQLvm+UWXbG6gebMw0+VXrBNppfvolQ3uUcucczOMbUO5io3Hp6n\n8wKhDJfnCuzEsECL74I505L7II5GMBBmP/UgKuAgmz9grli2eaYDDvlpgCpy\n8J+RhI/m0a1SPrfMPDfSo/YDTceO7Y/tl3YiRUPSdJXKUQyXvRl4N1fcXjr5\nvK2v0G9W5eDendebaEYFE7H1pCKv0D6Ojrq9pvCKbkMSwV/JXYRQ1tsFk0GY\nRoXHpI8RJ1aTYWVKX/CuCyrE3J/TK9c/aIn6t+qejTAoHbogFALBskGHIcfp\noqINLnvHSk4agyEKVOLS2UUd+kE1nBPaiZMlnaha1p5ci7KjkJzaHO5B7Dkf\njBwg\r\n=Jtbc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/plugin-transform-react-jsx": "7.0.0-beta.56", "@babel/plugin-transform-react-jsx-self": "7.0.0-beta.56", "@babel/plugin-transform-react-jsx-source": "7.0.0-beta.56", "@babel/plugin-transform-react-display-name": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56", "@babel/helper-transform-fixture-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-beta.56_1533344875556_0.4615942686153329", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/preset-react", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "526e97e56f26ad1c5c4f7db8dfa970fdad4a3d78", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-6dZjK0Cstq2GvG6CH0akRyC6R2GgjrPuIOoGDUjN+M2tV2AuAA+a9/bJFOMEsFG3GMaozoCfrezWM7VOIccsJQ==", "signatures": [{"sig": "MEQCIFXQc+0i7BTYMrhAZIVkkorwLd4lVHLp/8YBpNShtaWUAiBOc6UCI98SYUZgN+xyFnmITMIoGhJ3uvdDzG2cVlGZwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTmCRA9TVsSAnZWagAAtJEP/14i9sIRRcGd4Ti70y/w\nUhceu9vPZT8jaBf+30SEcE7RNdSseqcNG6Ps6kslIyfcHuRSVzGqSMPoqBeX\nQFUP4MjyfoGCpGU/KbkQo9IrAl6vAZ/WhGJ+SEepeWrU2XNZ2zsE1fKnafOx\nDaSfPisJi30w8CY6Br78Nl/xpBXwJjewKLzwfjLNXSZOEOanq57gvVaWEnPu\n29m5vbwI97TCeCUaKbwVT1Ezxrp/5DjdAAEMYwQP87vKIR5LgXL0Kr5mTTdd\nY6zN1t4GsjGo6Vn8FUz83lFIKgc5qxP30PZTRlnruYCPzkrOcjOYqj0G58L7\n83kZY6zBt5MSJS92MQE0re4fCy67nDEcLdjj1yMLRKvQ42FzfACAL1IAsNZJ\niR5o6Kd+7aHVotlWTCe1EUWNZJB+yYJBYhhobmuJcwBt7mMdeUUG2sdw2abh\n7cNofY1paw6SFbIuByo57/HppAO3XfvUlz31mxcK2pz29RIgjGVHJdPRd408\nXvk3cvEX87lm4ZzXTExZpjB6xRpKE9iUB+TZiLJLqULveZc6awJJeNqFgw6b\nEuM6noy++V4zICZCkZF4I2SalHOvTtLn4l/0zcrImo2vShAT+qRyV8XjPN81\nDd319asXBvwFANMNUu0MHonWBG75uVDXarM/bMsp6cvz9fHLhLwToyZFXGZA\nwvRS\r\n=Kbd+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/plugin-transform-react-jsx": "7.0.0-rc.0", "@babel/plugin-transform-react-jsx-self": "7.0.0-rc.0", "@babel/plugin-transform-react-jsx-source": "7.0.0-rc.0", "@babel/plugin-transform-react-display-name": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0", "@babel/helper-transform-fixture-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-rc.0_1533830374146_0.1264776144952875", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/preset-react", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8d51eb0861627fd913ac645dbdd5dc424fcc7445", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-EeXOUywwFJyEWWO5DV5vh3DNTlMR1uDzQ5gWvQ8pt5eAQxaoGLkdoxWkE8CJDiCS6PGMcIWVw8vm6mInmSV+Yg==", "signatures": [{"sig": "MEUCIEXPoIoMj/H+Id9rtBPvsgiH2Gfr9cZ9uEmYp1ZyLfdEAiEA5Hvx5Aj2H/9LKmon6MQai9ZLibGX5Wa2DhlxEUFS1Kw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ92CRA9TVsSAnZWagAAmysP/2qIXxfGnTB8RQkei4r/\nnz0cVRE4MQdt573KpDtZB3QC/SVad5Hkbf0HSqmTT58G0ZJU/A7fI5kMcDEO\nLF3RZQmQUe4bGA9XQG53vEEqC44Miv7S1SlTj/hVuxsgGz7CxaFeABIVT6Li\nCuKhlRvXCvWIEJAWdvoF7pyXEWVVbmXK0XBQqNGwFYSVGhZZOacAhnsFrrer\nUCiZkzE2Z2JxxRDuCm2/7ZtMyOEQ4msqZHCHbUMaFF5h3xLqqpeefXCtHJzm\n83mW2OpvkBcYcCT2LdWRST17TN4cEF2Z0fvpGicdcczsG3AV6WVAqOfx8Ze9\nc4PEy7eFqykvWEpFXbLhtW4UtKr5R1ZaSaQaYu/LuYccpi5tMuENltsEZ6nr\nTJ53XcxbQm6rsWxifNlPVEA8yTEGfcAAqciqCJ3LQrM0tLJEODObgHOeIU0r\njCPBHrHz83HfUnTPUGJM+lhFS0arCEh/CQKCJ+yoEFRNDGcUgsHGyNPhRg9A\notQfuk70Ff3qcxT6RHzXtsnMeBtILkhIqtaUbJmK3CnNxqGQPnKQjtynskIT\n1IkxlMqIYthMAkUqVNg940fgqmzIiaJDsiBphuM11v0bPrY78dbrMu3CIMak\nv2i6R2VAnrNzTzwJpnHpGXTgl6eFd3wx6AVCh3DDwTQ2sb7l66c8KlXixfDd\n+8F4\r\n=gKdu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/plugin-transform-react-jsx": "7.0.0-rc.1", "@babel/plugin-transform-react-jsx-self": "7.0.0-rc.1", "@babel/plugin-transform-react-jsx-source": "7.0.0-rc.1", "@babel/plugin-transform-react-display-name": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1", "@babel/helper-transform-fixture-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-rc.1_1533845365568_0.7951715859902799", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/preset-react", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5430f089db83095df4cf134b2e8e8c39619ca60c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-FjBjiW6l6p5mE0oRaWTzAWnFYGGVi7MwtXX5FYoRBkzIsXq5O/uhwbc+05XIlXbVZlZ/WZbUzCDSZloeQ1xWPA==", "signatures": [{"sig": "MEUCIQCwH1EyXG8To0jHs275XMGKnthw4JaqnV8L9nTTBn0xkwIgcaQAUzB6qAfKEojV+8KurqYbVhnUZxNZR0HKmx29JNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcvCRA9TVsSAnZWagAAXXEP/3Wy6vGR4xzLr8+RoP7u\nVFYB50Hxm3X2URjrcfsZ2kBNq0C+UdokkYCmfRZP3ZTwHKG5CExy7bKxT4Ba\noP8+rAqQ3O44k7S/MzvVRjm2eH3GiTemUsIYdBgEUnNuyFT195BwpG7mwliw\nTvK6LQnrxPmvWc6yvHx/E2owgKBmC7SL7NbvZjdMVAlxsOSdStOw07gddcJp\nGQyf2LatsbHzT+hofrZagOqRUjK0duD3DD+JFbO5p30wWefuoNxWA3YMy753\nERzb1f5l/s9WMzCclj7Yv0jWBNzP3HewEFvOgYx5/pNnpNRcxNKoJmSnIvyR\n824c3JnzJ7txtA9YaEHtKm206+v1BKPtjUxsnjkFSv/h43izhpRP+SNhmklb\nbYOKOe/0m7KryxD5CjYfdr1lKMyXC0GDftpQoOlQoiTCFkRXkQiquBe22qtt\n8iW8T7mJOTcJ/aQH617HtVweZuOspciOuaLnEXaS7/ymTTq0FHEWv4vNZtDA\n9CoglH3t00unl49rUZpmsze9C4/Z3lqR99um2XfOAjArRDA+PhpfcrFP3Fcd\nUqCycJ0TsgvN0OBxojrz+CfxC2ROeL+qewYIcGTqCr6yS+3+pqKD60o4KXyg\nujPORghkaKXrlnOoBscYSOZD0cHR9FZWGuqu1QIuQ8LhuUyajt6ml/+N2c3l\n76gG\r\n=KFyn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/plugin-transform-react-jsx": "7.0.0-rc.2", "@babel/plugin-transform-react-jsx-self": "7.0.0-rc.2", "@babel/plugin-transform-react-jsx-source": "7.0.0-rc.2", "@babel/plugin-transform-react-display-name": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2", "@babel/helper-transform-fixture-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-rc.2_1534879535433_0.5934183676353799", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/preset-react", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e149390ca6042511974df3dde87e72416fc321cd", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-xOe4yQMUsXZQ+q3keMgomH48e85HEbtMmfjYA0e472ZUdQ7AXx16WABrSOgOJBCUaia6pHoWtzDl0Dr4SfaVDA==", "signatures": [{"sig": "MEUCIQDfgSUlkKHYLihWoiQmKvOrU8YdaaF0/qETKD7hMAGtaQIga4GqYFLuFMjgePW1fW7sjNr1cPLvJUKKQJojgdA4xOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnJCRA9TVsSAnZWagAA6mMP/0RqRCYq8Ett6OGWq/wH\n6DAe/vd8gCPnbJ0uGssD1q4Iw5hKTb9207iCpnDRkVhn9XDX7el0FQp+vnjR\nFONLEiChmUtci8R/oRVGOBPbZOTeJeJQIsffaUziC96NjLjhyb4W5AA5021M\nn98WfD99zkFHEpqAhp3hv07HQzlxcuyk0DoAyEL7wn99MblZYg8MtQwRj5/m\n//jcR0YDf0O+rmunxZzbPpVVbWZvc+0akAvuL/PvTT6sTRvtUV1YNpeWlgGJ\n9adyvtDZdLbFsp4YmEWdhuBVY4vX23Rb7LJ03+96hjIXJloUhKfX/tqdS0NU\n6BHpRLGHe9qEMrmFClaKFhke0JFWVedDpGOCSf8Hgpb0SS4NpvsujyPNOozg\ndsSJEf/OzD9gri56sagIWDHPG0CdE4AD/PvRvkAVjCj9xBAr5shoMzjs6QAh\ns+wuoTMJtrVO/S1fLcQbh93tKma/nN01B2pxzGEygaoubdMnBIkuGuT7yUXc\nDJZNg61yll0hLAvGqOw0qmSK7Zv0TFLo/IdAm12+0EYKHjXJm2HQqyJ90U//\nhVRw/HJbkXcWpc4Gg+xDNLUYfrlYBX72zQ8Xb62EFRE10V49/l+U/xZWbpZt\nr23IimXzVwMx4ZJ6AhGxYeyjybmQwCTxIXNA17zUUoAGGXWCT6ZS1OT1BHmG\ncbWr\r\n=3M6F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/plugin-transform-react-jsx": "7.0.0-rc.3", "@babel/plugin-transform-react-jsx-self": "7.0.0-rc.3", "@babel/plugin-transform-react-jsx-source": "7.0.0-rc.3", "@babel/plugin-transform-react-display-name": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3", "@babel/helper-transform-fixture-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-rc.3_1535134152244_0.7592382763328125", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/preset-react", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5532c2382c3ac6d6d6176673934e873e1763b0eb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-yjQ1RNXX/VTrjtRNbcJN5Pts6nCHr1+ptwPG2kHmXnunMiv+sKKKuOLkhdpmp427KVPIJ8ietAHOrVBlxBvMww==", "signatures": [{"sig": "MEUCIQDhWE98fbStRJmtFmMccVJbCW2p2NklsBCWqRthtKWLqwIgbUuYdb+s9Mf0+AFJhgkyqnefV2/LSIDv/0x1Af9OsGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrJCRA9TVsSAnZWagAAkU4P/AhvODQfvmhJgWzTkGyI\ninKk53gjPhPwkCX77MxSRjOZygdDOoBTScIZ4Was45QO1AUsifaZLfMZibF8\nYx/Y35YNyOTfVRNnU7gCUnCmdmrA9zdkgjgSjmCu4uUKkxeZp+nZjvMVqgGW\nDcuvt/MlBeA8aC5GD7NlDCO0ysbWWeT2vsmY5y1Sr0UQJHEn1+N4uHAxZPS3\n7iAiAI38x0LqRwfrYob3FxNlrt8vW46XNknJLzYrTKVeQBjl/ur+qHxlnJTX\naEUoL4b5XNq3AYdMS3qQPbX/kDPcwPyV1XAfgJklEIuNcZjHEJbSLT/Mv2C9\n7p0cwJ1qwuUxhlymN/6NUyjYUCNI5VGTf7itfhSgi39zzUbdf9KmPSYCfywG\nMCV0Jlud87r9UEGyis16t5S/x5kDedx0OdZQZaH2GFmJkQzcHdZr/3Z3tjj4\nP1QeGSwN9m7XRhJMAwU0itWM3ERxatdl9zZysj+jTXp+KQOn0RSXlzoBQSfh\nykltAOH5bT5qoUuqcwbQelmDMWycHjjfJqP1TLC6KrlluJ7UTgO+x365YIPH\nPogszFrgp4bhesFzGKBfFdkgMSXpk8eNvuopHC8SCdpMn8sDOKxkIGSCLTM3\ndNoHUrhB86oZVUPUuULO35H0hDicJvVt95ERd0z6jRNpoZc/QLJffKQ2XtBO\n6aVJ\r\n=PUTU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/plugin-transform-react-jsx": "^7.0.0-rc.4", "@babel/plugin-transform-react-jsx-self": "^7.0.0-rc.4", "@babel/plugin-transform-react-jsx-source": "^7.0.0-rc.4", "@babel/plugin-transform-react-display-name": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4", "@babel/helper-transform-fixture-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0-rc.4_1535388360489_0.9787101850567017", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/preset-react", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e86b4b3d99433c7b3e9e91747e2653958bc6b3c0", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-oayxyPS4Zj+hF6Et11BwuBkmpgT/zMxyuZgFrMeZID6Hdh3dGlk4sHCAhdBCpuCKW2ppBfl2uCCetlrUIJRY3w==", "signatures": [{"sig": "MEUCIQDeK1ZacUGZPnDgpaVzs4Zb0ZXbF31L5PB8GDCRZlI9tQIgIKsHZYEOcciKn7TbW6NO9ThwMejv086HD32SkyFQgNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDACRA9TVsSAnZWagAAShEQAJZto2qJvYZUnSRcZvyh\nw659v2MBAXO/9Jlb8oud5FhYAaKeZ6SVkYHWpFEY3xSSpxjHC+1rm2sjus1n\nM5yxsj9O10SaiYsKIJbPXsaCOOqg46KeVm+CeSNFJdYqZupvjw2x0lzsOAG5\nPld/+e5kHSEv9clcW0k8yFJLGqzz3W5ENSjyyjap0Uvxw8sLYVNHAkMP/r7C\n88eXDiFn1voPyY+rJxuUaUeSOrPFPHk5PRhk3K3fpwWSGDtw0EwvmH/q4z6Z\nGPpWPadXRauhuj2ZcTWL2WhlQk35LpAA4COEDPHhWSpR8qYvrPNUS+89M3JX\npl+RKwkeeIm65x9MJAQQhQ+SdS2by2QJabNx7eibuOH7ZBP/v94hIMQlL9mZ\nYSKg5fQDxP+MVwtpMyCGgFTiwUxvCpny6KWk2Qrk/xIhQmutTi9SxT2z9sQY\nhRp4XECd7zGueQShulflf+5YYrOPRzdd3RvwXl5N4UZIaJZ8DrfzCYw2xw0j\naVoLItTzvhPmMYriHcbEWyiBhOhmA3ogji57lrznEE5K4VxT0JLliEsmWCWN\nyU9PmYoAZq4WJHjxjMn09MRUTW00jjw4CVlFiT+4ClbHVKazjwHp/3KbxlVC\niGlzVpotPdQ+mfngLo3fWvVSDgonqboSpAZd++KxXdoXFf66iyisYkPWjCWP\nZWSG\r\n=yCQH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "description": "Babel preset for all React plugins.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/helper-transform-fixture-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.0.0_1535406272150_0.6524360054340981", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/preset-react", "version": "7.6.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d5242c828322520205ae4eda5d4f4f618964e2f6", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.6.3.tgz", "fileCount": 4, "integrity": "sha512-07yQhmkZmRAfwREYIQgW0HEwMY9GBJVuPY4Q12UC72AbfaawuupVWa8zQs2tlL+yun45Nv/1KreII/0PLfEsgA==", "signatures": [{"sig": "MEUCID3LtF2vGr/g4mhgatitW3iGRx/reYc5McKvFwZkWls5AiEAr9ILNxjEp/deWbOBW53XCLwXsS6StcLgxCsemcZULO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhLCRA9TVsSAnZWagAATwIQAKL18/t/YVzjm3F51qPp\ngpd4i50QUhemDH2l3Tukp4ptG8+bvA9vV8dNk5TDYD5UDpX7JvClZyWEayqV\nA8RNUe1HT5DH/M5GTiB5QJWK+HZZ52Ty+iCgBZhL+Bw3pcpJ/c37Rh9hNZgB\nomsVGHdt94vUJpaeoWtn/nr8HWk5+KdHcWZ9yspHCmmzWQR+iUSfD0Yrfgfe\nnsixg3V1A8SB0ss1A2S4DuY1skPziCPO+6NdgOhvR1D6qynXYol0C9m+sY5l\nfuYFRkMer/UZhfaY/LpePRk/ULgsBd0WVceDXuyQ72ORmm8iyqVEhkfsQWD5\nysTKEsJDsfVUV00+Iy+lp/fRLsRSpNiESD5iaowaqE7sts6LKeQ2X7KsYi8f\nGbjzg+zvBg0hsAIXy4OODIfCU7wMVDGK6uYSoPI4S7QyOxjtCyoddNuGDUzQ\nuOjJiKM/ybLUEGNRh9dvQYA3IeWZKLMmCS6JZt7tLooYuCvnpGPYqiXZD6es\nez4alGSHLYyA81Yc4+8EVvwW+aCpVB3a9YYDPdZP5bGL6Y0lYptKhMGBv0+C\nN/GZc1wW+QUsm7JgFr1l9qBUPrRlseWBmz3sR/20jCxRYxvi/BMFEGrUEY9A\nUFOr+qGGPmd2CnKmit3LqQgFSYnwbfOhimsKK7XPT/csioeldy+1FmHvz7vd\n/fmn\r\n=C9xv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.3", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/helper-transform-fixture-test-runner": "^7.6.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.6.3_1570564171374_0.47956915083121165", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/preset-react", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8ab0c4787d98cf1f5f22dabf115552bf9e4e406c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-IXXgSUYBPHUGhUkH+89TR6faMcBtuMW0h5OHbMuVbL3/5wK2g6a2M2BBpkLa+Kw0sAHiZ9dNVgqJMDP/O4GRBA==", "signatures": [{"sig": "MEQCIBkjZOTudas9r/KP0UoxJCwGpQckndn9Et1PahFbZrrLAiBQWx+/dlseHX5bagW6cRe/GEbFwPF0dscXSCHA90/b6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVS2CRA9TVsSAnZWagAAI1MP/RUkZXOqoqVJc7BV1Ebf\n7LgH8WSidiymC8g49jdrFkZPsARw31z/wxWiaxgy0aqIkqXyWWC5+wKIwYpK\nMzWYVK37+xgRbcAl1JgdEfI/ChlpH4nYpKqh/jU6frgK53dYWbhzk/7Gdckl\n55hLGS1v6VVilmpBr5Ur6qSGHgA3RE0A3JggEL9xg8kZGBbaXjg7WGz4pdmg\nE+4rxNaXOovWw8geaeH7Wf1M1tjjrfm7LE3K2Wvl1sr8En1Wi87uxn0qV4dU\nP8ggR3UZFAI2No437uiSYBlP00wy9iz0W+4/KLmuKIFv60HG14XGL5xM8ftb\nsqfyQ/uVuD/0bKP0IPlTGSEEhlfKXmuCQy/3Fnxazr47KRw3FGWy7nHQ3c9O\nEwZqW/I6/pjPNlLs5BDj1LGN8rj9Gm17Njdcup6EOncTSC7B2h1JzFGKv8kz\nNtlFisbrWYYaa0PLRvOgbrxn9d+/L54lttuewkERXuQuv5Laf/M8Go3r+EW+\nBSkYGH0N1XRzECvn3E9Lv9HuILu06qvTiCKc0oqj6LwzmNVZwQaZoQ3dJXQv\n+N0kc3F3Q9JdNnJMSbwMd7JsOlPatC+vh6lLYR0Q/kuzjpyuqRPT80KlBSkT\nVzQoZvsTcaI8gM7lIj3oCpbGpXZUPg5fnJowKnB+ejs+LgYW24WWSdxhPCaf\nN9R9\r\n=fpAh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/helper-transform-fixture-test-runner": "^7.6.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.7.0_1572951222075_0.4542194133350801", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/preset-react", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3fe2ea698d8fb536d8e7881a592c3c1ee8bf5707", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-j+vZtg0/8pQr1H8wKoaJyGL2IEk3rG/GIvua7Sec7meXVIvGycihlGMx5xcU00kqCJbwzHs18xTu3YfREOqQ+g==", "signatures": [{"sig": "MEUCIQCzWB1eq2Vpb8sTY81tnhEHbfPerX1zTKfmVstvnqbsYwIgdYRLSqRfkBIjriNRPJvOLWl5q7r6cp4R+BED7sPKQoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBQCRA9TVsSAnZWagAAlqoP/3EY4rsFLkYNgL8fW/y8\n55OgiXYLMBb+mDtcMZ/HrTqgg0IFlcI/M9xkP5tLrZhVjkZA5sY3HDv4LAGt\n2mjB6ttueEDmXQEguBFWlTsPQ6OTbAgvIsC4aZFCys18/e7jAYEk4voMlyF3\nhFGB6PlTy2gqa5n5za0l6Ylbh/9Wd9+qqXZGLiXOvWavZYtgmIfLNZf0+WfL\n3cKecjvuwKZiJZbe3UoTn50TzpzoHPVdR88gG4/2MyoVRU9Lr8tA6TJFqTQP\ns7p6K5+oOmNEPSp6eJYnioyXmTdBLgvMntDn89OVGQo8Vdvpi1+dM9Yyb8Zo\nk35RTJTyeS2nz9cIxN1DWiN9BjxUvp1Fy1cgQMlLVQWd1u4HScvdVBWjmsJb\nAEPBph9RRV9mIMq4k4hVHj33o8OO5CnyAyI8WCQ+H2fr0yVBRp/iVSgLD5/I\noAVZEW8tt8SPtylEk5lcPCevML8MXw/cLakzarVUtF2DyyHvtQwDeb9gwSeP\ny5Ha600i+ha0kRHkDWx5/cHZOBsakp4VwBfp0nB0X7pv5BMx/mJXhusm37Ji\nYH0+4JSPZHEro6vinIogtH0RUsRQsDJ/IyXQYQO+xWDhZZcFb6RPqU4a0WlV\nXLjSUA7ycmf236q6PHjI6d4JAsE2z/UelWbRgjFCFtNwMPkzi6dav5ckA8dL\ncD9X\r\n=PFNM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.7.4", "@babel/plugin-transform-react-jsx-self": "^7.7.4", "@babel/plugin-transform-react-jsx-source": "^7.7.4", "@babel/plugin-transform-react-display-name": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4", "@babel/helper-transform-fixture-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.7.4_1574465615680_0.16814355010088855", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/preset-react", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fe3bdecfc94e9b4eb3aa2751cfb284e739e810be", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-GP9t18RjtH67ea3DA2k71VqtMnTOupYJx34Z+KUEBRoRxvdETaucmtMWH5uoGHWzAD4qxbuV5ckxpewm39NXkA==", "signatures": [{"sig": "MEUCIQDfrdYEVgzMxN/uJhkZ8Rtp+D7g55R7m52kKb091i0AIAIgUBrVMAKC4KfJGzmydA0ysumayfZIhQexlpfKjv+US9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWTCRA9TVsSAnZWagAAeg8P+gKsO3bbcx4iBHaolXa/\ng5ReLgCN2uOj9MQ4z0NG+CCt/Gv3ZE1d9oXQxLwqI1Zq3gbi0cvIMkwkbpYS\ndRRMRrIp0McLohod7erJpaWTl2GPbZQCsIFtl1XmYXPSKmvnjXZCa7MfLmvR\nuAIguLiFKjJ9FvIklrVXspCjUkkJQMWOY1T9Ru7/Nc1vdG2MTs2IHPwagQdO\nDhDR3/zFdULw2KY/feih5AbLBU3nRIaN6cY2MyzPRvcgqjd7mAEkiyOR8M+y\n6yZTfQ2VnMClcjozJtgo0pTon5ubeHU1efQ5AtzxdsnmqO5LEpnbpSIhBni/\nTHUDa21aAhAVzX1VgU8v7066wqfz+MLdugZMU9dToSfWCQuQHlR53gILt4BV\nlTrlbE5BEDVPm2TbX0rIXBx6e4iGlJhjVqYe7hgjEJoygQ3WnK9ncJEFILcb\nJLZXiMduDj01wYDFJBxZjrS2JTJnSyCWGRDNfCAAYy92J1uqJBdHB/0J3u6h\nDGf70LuA2ZCATV8uMW3uJnbgmySfkPF6EFJMoVREwG7oyVSKkg0sIjw0SZPn\ntdRpsaFCobYNnYkKE/h++nPZXcKJtFovYbnCFzs+zhOHHKzWBa6LQPfsAkWF\nhvhku1NCaVaM9670E2kpHY+Y4rAbnIyYJwbp3lGRUJPwCQ/wGBI9VwOutsbQ\nJqQ1\r\n=tsDS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/plugin-transform-react-jsx": "^7.8.0", "@babel/plugin-transform-react-jsx-self": "^7.8.0", "@babel/plugin-transform-react-jsx-source": "^7.8.0", "@babel/plugin-transform-react-display-name": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0", "@babel/helper-transform-fixture-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.8.0_1578788243011_0.23101118409572208", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/preset-react", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "23dc63f1b5b0751283e04252e78cf1d6589273d2", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-9hx0CwZg92jGb7iHYQVgi0tOEHP/kM60CtWJQnmbATSPIQQ2xYzfoCI3EdqAhFBeeJwYMdWQuDUHMsuDbH9hyQ==", "signatures": [{"sig": "MEUCIQDVqegtQsFAInLRSH51tHk26beckwFFl9eSSmcdaE1HXAIgKiAljEnlJBWEh3Stm650+fcb5wCq8NW7z7dI6suxbBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ2CRA9TVsSAnZWagAArv8P/iJABqm2xalG67enYJDz\nuNm3AM28/s+kZkCLGguY/yqGAPmJ/vR5hL57lYZuD8ppm64VcXG59mdSWxeg\nAkXkZ41ShwEBWS0NnNgWM+pYfjKoc78NJs6ipE+E9aGudBIl6XfLyIPsW8+g\n6gIStsUuzS2Ho1ctVBbrBr6TsDwMtrsCaCb6pCRe+ozf6iTKTKgrjmzuhaf2\n7g/B4k6bYux4ExT9GaeOHzH1i+mV7IaS3ZPmevvxrCgq/RKVNQTT+8V6Oi1t\nwKTUQJjcm+9bxg0k693nxVl3E3GOWyVkWv6MYj4dBtxbHVypLfdO9Rq0BvU/\nF9SyQHqUleSJj9msn8rfyN52At0uUSo1ugosYc5y9KssyfxytPlvV0BDu2v2\nLvRG9CSA8NBnV2zWqJHQBY2nhPKaL5hLYQ7Hp8RF6Pa059hZ8fGLAAXv4cpl\n1XLVybxYVD4HSITzlVKTtxfgt8RNTf8Uh0qkb+gWhdmIFruaUlwqPPPCdyp9\n7aF80Ayrdn9m+zGlaaM+XDLgY6YOREAkVQchG1STANAvE103onc6IeF+HAed\nnda08/jQ2Y3SLp5+sZn2OZxKitaVpb6kzK/ghtbSKVBEZm1Y/AEmzUJOmGBT\na/xgOfsKaVk6wO4I1aeNEmMjPG7iGK0chlKZHxifFnHgH1WYkXy5cvg0tHLL\nj+5j\r\n=se3J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.8.3", "@babel/plugin-transform-react-jsx-self": "^7.8.3", "@babel/plugin-transform-react-jsx-source": "^7.8.3", "@babel/plugin-transform-react-display-name": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helper-transform-fixture-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.8.3_1578951734407_0.6188357583118935", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/preset-react", "version": "7.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "cdc5b6dd35ff8248047a070ec15d136c88193c9d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-56vOLSR9yTi+5WwPoTfi4L2C2wN76RcVhFZ6DPeJT9lrlaA/wqTNUP1XCehnBxOMhTk5yWLQT7MwxyhejJVnJA==", "signatures": [{"sig": "MEUCIHpHYVagfwdbIdm3SPoKT/CCQXdxNtfBol4h6CK0KgVCAiEA80IJqGbxBd2XTG5pVwAvQ3OrIWZabXmPUOd9826RR10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPeCRA9TVsSAnZWagAAdCUP/ipBY86WW+IkWX0cOKgS\n5GfBypBJ+DjfOQPaqZts8q7eeVe86odzjnEvUmipwmuvc1h9evl3j3QNWLZd\ntUMgKQeCUZB1h4Py1g2RH/+12d9Os4QaPL0cEIo0t6rY28s1+4MI0Iu5xfYY\n709ugHEP0X4i2LUh+cK2W69ZCHmoGY9Bhz3Tsiu7APZLFrrbwDHQBtpKdGxu\n8CgKlwTpU9dlAZabvi/bB1BqpKwszzAlFFK642Luc31qM3U8TSDy/P/jnHT9\nxPpPIz36Gpio8lcurE4c/khYrHFWIe5jF+26IoWLl3g+UMRQ6fFmeG9WK7LP\nOiXzlQEKEU1tJq2pDXRuAoVXuTRVu1bZE1ZxdNjlUcJvBGg/qMkJ2egncwos\n5BGlfEZS+AfKY5ep6juJqAMHXJhW70xgMZaELlQgO/xsgqJohhW/GBS6s7EF\nDRs/Pm7iDitkouRQIMIu4qtPls1m8D5ZSuRY5/LMcAQqVYhDMEZ8Rw/jiCAy\nX2O6WwAJNBnYXHJM3+tyCNAsTG6Ud6j9UCnYwc8MmAQkttAEBkNbbDVb5s4/\naHU30jQ+//9xAETWkQHjFzWS+R7NsVEaF2DhF8JgvRWkpl6z+LrCKrmYvmhW\nwSjYnH/1lCj1zj11tkcOzRc/CFMx/QfzQXx+lU4MARujXTfK0//GmPjGPLEl\nf9Uv\r\n=30qF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.9.0", "@babel/plugin-transform-react-jsx-self": "^7.9.0", "@babel/plugin-transform-react-jsx-source": "^7.9.0", "@babel/plugin-transform-react-display-name": "^7.8.3", "@babel/plugin-transform-react-jsx-development": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helper-transform-fixture-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.9.0_1584718814041_0.34726558465886326", "host": "s3://npm-registry-packages"}}, "7.9.1": {"name": "@babel/preset-react", "version": "7.9.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.9.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b346403c36d58c3bb544148272a0cefd9c28677a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.9.1.tgz", "fileCount": 4, "integrity": "sha512-aJBYF23MPj0RNdp/4bHnAP0NVqqZRr9kl0NAOP4nJCex6OYVio59+dnQzsAWFuogdLyeaKA1hmfUIVZkY5J+TQ==", "signatures": [{"sig": "MEYCIQDDBOoxZI3hgc8wqRYStOpHIv/sE2VNalFs6EbNULbE7AIhAOeSsngwJxaOPMREjTg5fkf3dXvJ6TY3VwI86onJeUfK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedTrrCRA9TVsSAnZWagAArU4QAIa/8hlCoEYiNG3BW9iV\nip6wnddfCjf5rmhE95Zy4Q3BMyWXWZBXIIvMjngf0o9zCPoBYoQg+hFO9WDT\nH00AgauVKCgpyKiY6KObEhgCKvLE1uWaiQ5AbQv40hr1DEak+ludBk0hRqiD\nKu7EKn+TI4U4YtPiiH+d0dIW4SsTxdRDz5f8Ov1JOVowONVh1IjL1s78qS7W\nX6oR4IGWW4xxLz6JfXYKLLy3Xl/bN6n5qMSDvCf3IGCLVlO4CSLjxQfy1Blx\nDV/6NtFJQapMCDutmtYPmZgrzJhDwBskybgWVvozCFIPRnJ5ugDkTr9p0QpK\neH3AARnrLjIlSOEr1jpEeQoDpup2RaTMLXOBoEGVq7gRVdBFxu2goQ9MtPYC\n/79ojc2BVlaEiCsdaEu3eD7sxSc1nBIZCq6ROJzy62ece1EniKbKPXeWHr5B\nforTyEvogT0/EZdOHLIJqOmgt3aYeAgRzTxLwsKj3QkwyOg3E4u8jER1oibU\nBnxuyRy4LuSKubEUSc9pmLonzNZKBuuN5pAGQ23xqztKgZLRuHK5mWDQeDw2\n0hoESZ6qQXxvkjqNbkYk1lTv48cezrsBdyg89bzOPNZ8S3jWowbdw+e4rWy+\n2nz2+14iammApdRaLCfzbeLkTu8mpZCHT8gyiCISV8mpbLl3zct1hXNv90TE\nFkLT\r\n=Z5Kt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "220ce702a1e02f7c32728985505e0392555b0ae7", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.9.1", "@babel/plugin-transform-react-jsx-self": "^7.9.0", "@babel/plugin-transform-react-jsx-source": "^7.9.0", "@babel/plugin-transform-react-display-name": "^7.8.3", "@babel/plugin-transform-react-jsx-development": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helper-transform-fixture-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.9.1_1584741098879_0.7737190878777234", "host": "s3://npm-registry-packages"}}, "7.9.4": {"name": "@babel/preset-react", "version": "7.9.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.9.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c6c97693ac65b6b9c0b4f25b948a8f665463014d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.9.4.tgz", "fileCount": 4, "integrity": "sha512-AxylVB3FXeOTQXNXyiuAQJSvss62FEotbX2Pzx3K/7c+MKJMdSg6Ose6QYllkdCFA8EInCJVw7M/o5QbLuA4ZQ==", "signatures": [{"sig": "MEUCIHfz6SpwsF9Qu4TnOr+Uim7iM8Dqamwri48dyK7cWGuBAiEAwsp2MA0SUFow7/n8yuPyRHSmiyD3JazciQ4K6L2RSzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeecVdCRA9TVsSAnZWagAAx8kP/RynyQxIipHOaGHy4yK8\nJRBkeUZ1H8hN5R/WQqahMupAbNLVq/uxboaOusCktQj7E1tB20Ivf/r9+f2j\naLwaA8X0+AZ4nykhnHTSrJ+8M6GJh9/T4k+kEClo2fxUndBANQpOuZAUzpxf\n/wuf+y6tgsfRYMBFBh2N9cbYVSLT5CFiDKDIDXpwS6jMYVZ0wT0Klilw3wit\nlcaqHDjO6Kyf613P7yBowo9DBvjCBjUxmMSq2LFCMn4dc/y+f6ReFjwnzmTu\njyC5OC2u+w0qNBLF15W1LzUgMVCr/nUkiI8a9YB6S5qfKdt2HzhCT7h1h18W\nI+VU1omgawO48NpsSKBPH+2+m+WLJBTh3E74RTRXPCGivGN+fqGo38o5j95q\nfD71UPB0zXSQa+Y1Vj4VtzZx5TJCxpSkfYh5Uo6rNQZjAZpzayo5qm38OWUM\nVKXAkVxD39Dmf91hU5HV39zmSikNq53N3HKzuWR3hHXl/PLeS/mZbWxrOHP5\nE87+CXTNNFNuP/1tQ8+xGpTEdBk2/wqcP3yzS7KDS9yx+L5a22VrB9D5FmWi\nRNAmsVhBt3oS0dFJcOBBpTLsucAsoI8bfkPlBSts9Zqb3HMveMHuTUrrgFsb\newOMCsZzkTyLWttcbQK7aUXIVsbdN9TtTSfenrwrIBr4J0P+VrCUMHtmswzm\n2XP0\r\n=Ie3u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d3cf5fb5f4b46a1f7e9e33f2d24a466e3ea1e50f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.9.4", "@babel/plugin-transform-react-jsx-self": "^7.9.0", "@babel/plugin-transform-react-jsx-source": "^7.9.0", "@babel/plugin-transform-react-display-name": "^7.8.3", "@babel/plugin-transform-react-jsx-development": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helper-transform-fixture-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.9.4_1585038685211_0.9450210250905462", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/preset-react", "version": "7.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "aa737c937d982037744c73180353c5c639b9ca2f", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-3bHAfSRGTciFb1c7qlPCeGiL1TErUANc5AmjXE5+9/l6ePyLoCvHPxqdk94PUGwTn6/VOZSDDWtkC1cYsaUUkA==", "signatures": [{"sig": "MEUCIDMkhugYAEMjw6SILBCKgkikdedfkjN7x5eSyF9t8CGQAiEA938ZX5JP/VqWTg1iCy5jcgB4i+RkBuajQ7l3pFjgBxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2BCRA9TVsSAnZWagAAGHgP/3Y9kYu35wcGTaMbA9vA\nPNfgzApI2/VwOPgoN8g+iLtRU8am+MrkQqATpC9gtTRwx+X+Y/7398EiIegQ\nhXgKZYJxsizYaMru+JPDLqLVHm7ZNjYu5uSTaF/d+pNO/92VJtjvNgOTnBzh\n22GP+7/R4Zx7rsyKjSUnIr9cv0lW6Isoza7rpd90p2571sfxZIId4j1qB4Ef\nsOlUP6eej3thGkvKnW7++BH5s6Sfna+F7zhN2kdh/0EfuUPp9NsGLbnpVzNP\nhQMKlBFpL79d+3or7KvKNm1TV9th9G7FvHKG4G4vDBvidFS3oq49e3qFwhaZ\nVulyyUnj2V5u0qEUWJ8BpwQfVzvxKVcSZwSDohmP5MNf9GkafN00/WgkpXSn\nNy66+8mVKGfOcvjvu4OvZ4auiP8GFtqc/ugbXZKJue//CTLrH+2/2Od0PKun\nmzsRcHfAx9a2M+rX+IwbZbqTdCfd5fnsrZV3AS8fd/ozavMoVxNnP4tEja09\n2lyRwe0h8T1ucyCJ75miQAJY7Y4DPgW2THsv994El7viUFMnP8XDDLR+Un+g\nokSRKSVaXXUgq8+goueESqsiuQZkfokvcdYno3qrxjrTYZRsphG87JYfAUnU\nC+Z1oJaTffPelFbt/qgc1/mHqNwF/R5Yktn62NCTB0FcA7qh4CjvbhBJb4Mv\nc3uy\r\n=YWgP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.9.4", "@babel/plugin-transform-react-jsx-self": "^7.9.0", "@babel/plugin-transform-react-jsx-source": "^7.10.0", "@babel/plugin-transform-react-display-name": "^7.8.3", "@babel/plugin-transform-react-jsx-development": "^7.9.0", "@babel/plugin-transform-react-pure-annotations": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/helper-transform-fixture-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.10.0_1590529409349_0.12923815583300602", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/preset-react", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e2ab8ae9a363ec307b936589f07ed753192de041", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-Rw0SxQ7VKhObmFjD/cUcKhPTtzpeviEFX1E6PgP+cYOhQ98icNqtINNFANlsdbQHrmeWnqdxA4Tmnl1jy5tp3Q==", "signatures": [{"sig": "MEUCIAQcLgdTagWH3VEjij6P6aG/jPO+ympjsOyToX08XQofAiEAhXUA3m/K/Djth7Idj60Or7+i+W/qpt8TDbhJO8L2Ys0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTSCRA9TVsSAnZWagAAIL8P/Av9YYcZg4Da4bQ5iHch\nLDuw/GRW17PaPpeMdfp+qZ3rjgHbyfQnIJYgnsZaDRpr/TU2hvP7UQOyadqV\n7dXoWVLjWBQ/paJxiAfFq/l0R0YhcW/VQ7PsTUO3NtAVJVZTjMMxhdt55v3o\nHgt756dRm+sQ0urdFE1vHs+GQUtXycMC5Kyxg6wrA0BnVtYpdSUdWRPmbYh6\ntPnszF44HH5UNvG5Q0daA3W/uKqYSn9vx+0T+OLqSjdDhf+U1W3kuiUdA9iz\neTRHcDy6++/wBdryRvkrzoSmNwsxOgKiQitpSYgwTbR840LCpLnK0tshb/1W\nb1z6gNgFpFDQQPfB/KVhWImvvxA9860PsfNlqvNfQD/hSKJ7XaRpC3mAmM4E\ny3Gt+rDf1njZSkOpkgkbWNjvDGULgKiptWLjtV99TSiA4zEw+Wq4icNP4w9g\nq2daUS/cSMuhTb3YnaYQpQEBPiYPckMWShHvzY6LVYwC+XAMss7pvisWvM/R\np8S3IXL1ZQHCt2+jBnsw5VxFgzbQWi2aOD/7cNnGl4uBXSsFVdsI8Q3pfOwU\nzXjGV4l0B2JPidXD55riaLS4uRHWBVM3UHx9Hb+i7y291e6x3em5M7Szs7Vk\n8+cXFogk6Lbz8SirCaEO7s1ezXCx7jE4wySFVeHcaAORbLDkq3m2zZSF8gWV\na5VW\r\n=6Sj5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/plugin-transform-react-jsx": "^7.10.1", "@babel/plugin-transform-react-jsx-self": "^7.10.1", "@babel/plugin-transform-react-jsx-source": "^7.10.1", "@babel/plugin-transform-react-display-name": "^7.10.1", "@babel/plugin-transform-react-jsx-development": "^7.10.1", "@babel/plugin-transform-react-pure-annotations": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1", "@babel/helper-transform-fixture-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.10.1_1590617298395_0.6680538583808728", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/preset-react", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/preset-react@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "92e8a66d816f9911d11d4cc935be67adfc82dbcf", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-BrHp4TgOIy4M19JAfO1LhycVXOPWdDbTRep7eVyatf174Hff+6Uk53sDyajqZPu8W1qXRBiYOfIamek6jA7YVw==", "signatures": [{"sig": "MEQCID2hTxVU5y11xQaFWOmFAvUwvpVGp6NWn85wuDlcUxKbAiAMPK6WXeJMWNgL1ySVLAwKy5lZG/mVf7bxiDO9uijolw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zplCRA9TVsSAnZWagAAgtAP/iO4xfEI7JRAO9DBDCnC\n35cjvERnWFjo6j4p/dEsHVoDnFLPWV0pZ9M+nU0FDPgKZSq9XZZrIu1XbftD\nCo94HjgOdE2T53VCUJwKHWR2PrG1JU3CkKbeUiUNJEZMC47b760oUVCRgSjh\nbMbcxT+RxBcsmMjQ6L6vAfOJ9TK8jI2Xx7pysFODK3nR4ehiW2ryDaBUd9ka\nNIj4MzYxG0E6eab4qd7CMBmuZOfdvHMqPY1qn3mXW4xNRh6mvpTwqC+2Btlk\nxFkrE6IQeJQ0szt9VtMAw1RK63tpvn2x4JsLeWXFQGJexWeKjCRdb/G68kqv\nG4odKqKA9BlTXIrkj9ibuYSMH3FgoPWGNEEUenr2se8i8wrz57pRa7nulHWY\nsWZ++mwV4ZkhFUxDFL48s2uYd+pgWpZj1EvJmV7fGDGYN6+37bZ8yGSWbLFj\nzbI4XxF027rB4JgDM9cJO3Fhz4Lkhf8yOnbF3x6ZVbc1yWGoaiz9QhJUEWMg\nmdf1vVc3dnNIWTbj4dUfJ3HqXaD4QvP45JqM0TTED9eHH788PEYlUSboqx/7\npX4tWvqaNr/AH7pqo2QxGLRiPF4ebLoU87JjDW05tMcm35dCK/B3/ssQ3doD\njQ+KCll1iXYFW8YZBcI06Ob7lvzMHASxDprm/B7Tf1oWZ/FYJexRXfdFYxXB\n74zb\r\n=B3zz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel preset for all React plugins.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-react-jsx": "^7.10.4", "@babel/plugin-transform-react-jsx-self": "^7.10.4", "@babel/plugin-transform-react-jsx-source": "^7.10.4", "@babel/plugin-transform-react-display-name": "^7.10.4", "@babel/plugin-transform-react-jsx-development": "^7.10.4", "@babel/plugin-transform-react-pure-annotations": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/helper-transform-fixture-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.10.4_1593522788891_0.5546696702207887", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/preset-react", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7f022b13f55b6dd82f00f16d1c599ae62985358c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-euCExymHCi0qB9u5fKw7rvlw7AZSjw/NaB9h7EkdTt5+yHRrXdiRTh7fkG3uBPpJg82CqLfp1LHLqWGSCrab+g==", "signatures": [{"sig": "MEUCIQDIDdb0UZwd1ihk1FGvd+9cIZWfg+90RdcP1xNDGyYIJQIgF1TUefRjt0oWj55toh4+UKu/AJEMM4vePwg5sEwW1sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNA2CRA9TVsSAnZWagAALKoP/iEx7p0vkSfU0Axvadup\nQpYGvIfRoOJwKYVWUY4aupeA1T+yhiuwSEsgJaR8BB6o5ihytrSbX4asseAt\np1omTqv3ahBI1fNYUQuNgbq9tmWpgcLk5XM+5Pq5S9x33L9n7zyQi/fLOvwj\nDuQqTkHnjhzH+TVbjbzOOSJO90rXXEtu/U9Qi8y4VFn5ltt1QlDpnlzVkX50\nlwNODBZOuUPOTi3esjlSv+Y5AUTXp8MHLdn4IBqKOM27hXHhgbzLUTzr7SXs\n6T8XNuz3FaaBfsZDyBaiT0n8aBmabG7qx2WSJ+6g1PTcf/qe6Sjof72Aq5GW\nkYfKZp3zwqgEnSb839UrK37T2OXJAPW9uTm83Mnok8+HfRuoNy/lzhLKWmbm\nlJQcAJKodc5ImER/fVoRq8DKKB8BCOvr1PI+I9TBIwh6PvPPkgYtQP7JrybA\nge9n6TYGly+1hBarwsZKIW37fc65HsXFeslqHTAr954LK/ErZyl4Et4X4gB9\nOZljfN+WwKhPMwYRtZfArIBWuF3PCQOsEMhWLRr+V2tqLQaBi1QICz9Mw9Hj\nSpkUTHfKQyQLt4O/4GbMOwrRg/ZGyUs0SdVVmykbeGZ1NX3QHA5ZZNCFDPsz\nAyjUyRUucK7hDMvKp052lymiACI0XVQa/hRr0wJtJlW2rA8RT13pOjcDI36/\n+4+9\r\n=GNZf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-react-jsx-self": "^7.12.1", "@babel/plugin-transform-react-jsx-source": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1", "@babel/plugin-transform-react-jsx-development": "^7.12.1", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.12.1_1602801717688_0.033125829523326544", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/preset-react", "version": "7.12.5", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d45625f65d53612078a43867c5c6750e78772c56", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.12.5.tgz", "fileCount": 4, "integrity": "sha512-jcs++VPrgyFehkMezHtezS2BpnUlR7tQFAyesJn1vGTO9aTFZrgIQrA5YydlTwxbcjMwkFY6i04flCigRRr3GA==", "signatures": [{"sig": "MEQCIGGbpblfUjcgxaiWQ0Bo3YFxyUlJrT/YJHXO09dHja99AiAkLC0+y0awnZzvaO4sUwD6Nf+PTio9GjpzMYVAvE5wtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodr0CRA9TVsSAnZWagAAV9EP/jZP/MXBfJKVAooKTHya\nMokCPjXlf0Ey56hh0lQcIAQS/Vd4/Pa5oyWWdzNy2QSfkjH9wHIngfC1rdiK\ngsqa0txfpKYQy0EeAa+/Y3FWAezHryZ+jfTxYqTq9oJOkIrBUGb63nFXauuK\nszaP4r5VQ+SFcz0gq8yJaW7bsWAFIh8j3hbqRalEIzgdjDmn3bGP/LSP80y1\nVKef47rHUY7vLWr+Ev37pyh08KZcU5jLQjVdfePX58fa/5zzMMVhbj2mdQZQ\nHN/N8w0UuBPWrKtgeuDQ38HXMI6tzn+Ls/uLrq879Ql55DCDS38gkIKaupNe\n359ncCo+SV3xJbh4tsUPezorEVNBw6bMu1vhQBD1c0B3XZtKLwhtFs70mC7I\nlEkPMZhN8FPSFNcznkFWNprVRWtxFISsfcGUzMRg8u64Km2THYLrgPdG0i4l\nir+onp+pSUZu+ti1PiRkSVU7oz/qCh/ZjsqzM2/D3pEn0Zu2+qGi+9Us1uoW\nI03SHz1AG5DP24cKoZTNcXZcKYmgcrOcKsbKRuSEmD575kh8cVKcwNHUo07h\nYPt8X1OOC/frNOX1QHE8sOVPwH2O9582d2d8vrmz3CBVKnJK9SQbbn6Y+VEj\nXO/eXFu1rzfULG9Baemb3PazppdgXSpcu/1PphUKb6QljWV91fwEyeNL6LhV\nIl5k\r\n=Khlz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-react-jsx": "^7.12.5", "@babel/plugin-transform-react-jsx-self": "^7.12.1", "@babel/plugin-transform-react-jsx-source": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1", "@babel/plugin-transform-react-jsx-development": "^7.12.5", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.3", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.12.5_1604442868224_0.04041157901877557", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/preset-react", "version": "7.12.7", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "36d61d83223b07b6ac4ec55cf016abb0f70be83b", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.12.7.tgz", "fileCount": 4, "integrity": "sha512-wKeTdnGUP5AEYCYQIMeXMMwU7j+2opxrG0WzuZfxuuW9nhKvvALBjl67653CWamZJVefuJGI219G591RSldrqQ==", "signatures": [{"sig": "MEUCIFHTrjrW7/+AI2S0RFNrh9OIjROA6IfZRTJD4unywT8FAiEAm/z97W8hh6wgdBwBcufZ+30o7cxAvpmiNF50FvMVgSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+vCRA9TVsSAnZWagAAuI0P/jREh60tezqrb5N2TE40\nxg7z0D3Pbgsyq/X9K0nKOhsPIV5U8UA8pXb+6eOaf5t8SfGfC9OTT7I2HaCl\neixQihqmeNJUvY8ObzKJ1C3LukBygAZk8wga1fOSbVIVCfNHc3/1CDy7i+lN\nN74Vn8+BYVnuSf2CAHrJqgQRFLcYsUSNhKEZDXWqtWkD62tuyNFJbGJ0ohTu\neNvzxAKaTrtPSEbF80snfgBFG5PgVyrkUwxxCgN+DHmzKxL75HTqMHw/vKPy\nUHIotwTLQHKKQ4Sfh2WarjHFMtE7d63F6OXl2dXfJ7rO2tXeUOH5rigB0Kjw\ntiGi1GfSBRUXTNZcTVYZjvo7xbGuOrw+Y+RObdeMrCcNkEG6ny9NIHw2yZ61\naZ0FQ1OO9MYzkytd8sbTt+JWZFQ2NwvxbC8YZEAOR0d1GSVj8rQCZvYaRcUA\ns/Sv6mSqFoP47hDpMguziyYq5ApYVDOIAM29305HL/180nsg5FPLiJofYL6p\nrMaPqFAazHNsjWL1zg+U5gD2kvd5fPEI9U2k2q5z/tQwLXTO2/umXXRIt4IU\n6STfXKVQoyfLKp2kTJqcP37GnDm0rq3RmG1Jxdkx+Gxap2LNt8+zIDdoGrHc\ndGocwGInjPFd3KlqLwPMBysCyil/VOZ4b6EQjxfmToJd2A2Ev+mxw0popfNO\n+a2H\r\n=tiel\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-react-jsx": "^7.12.7", "@babel/plugin-transform-react-jsx-self": "^7.12.1", "@babel/plugin-transform-react-jsx-source": "^7.12.1", "@babel/plugin-transform-react-display-name": "^7.12.1", "@babel/plugin-transform-react-jsx-development": "^7.12.7", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.7", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.12.7_1605906350629_0.21970495574896298", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/preset-react", "version": "7.12.10", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4fed65f296cbb0f5fb09de6be8cddc85cc909be9", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.12.10.tgz", "fileCount": 4, "integrity": "sha512-vtQNjaHRl4DUpp+t+g4wvTHsLQuye+n0H/wsXIZRn69oz/fvNC7gQ4IK73zGJBaxvHoxElDvnYCthMcT7uzFoQ==", "signatures": [{"sig": "MEUCIFPu2jl5g3iGWQSXP6ctam1d+qxymZdzRzL0xlehp0S+AiEAkW6KMtdGGld/PkPx7xtYGj5YRwSYf6ywekkv8LtRsLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQ2CRA9TVsSAnZWagAAu/wP/A9HJy0aVAqJMin+0ZY5\n4nj9lRY9JJz7/qeVuiqhmbMlQQxRHywEsnCcR5UWh3W+oqHLWYTZMYZHsFW9\nQvPfD3DoAp5LNK/FPd01M4PEaeY7AB5XSQiMksVV8wv5Wa2wITCTCJ/xxAn/\nEGX/ubdC6dyXLTrDZpRXiJv3S4oA4+rWOFIykrKryw1Vr+qlCUUo+IxErfBQ\nOCBMjufwHZ4725K+8FNsZaLejRY7V48tzzcc5Sseea6MiZgAeh2EIubE3e2g\nycY6cGNBRRnSKNhmcrBhzF4WsxxJX9KzEAH8eC7UjTsFEpY5yKadYM35XASF\npZP/bT6a1DPi2u5wzTx8EZ+I1iX8PQ6jC46j+WV0x2IpxkSh7A2/u5VXFIHo\nP0D7cNFL5h5/CoyQTwI4qK6eTdlna0tU8WkAh7o2hzmjSyGCUkffTkdpcDf8\nrPC0kjbtSREkDxu/Hsm4uJUmeP5YS+FgAz97Gp7rK4GoJGyeodsZE01fZnHO\nkBxL++OTe6y8I8X58kEjdF4MibSwzU5nlB/yIKiPahNSWDFFQlBElDGhQjXY\ngGi4LeDZ6OcFUAlcgWx1HFzldEcEYQu+v/KNeL6NFCcnwn9I0MmBgXE77LIA\n9FjTipzRDYn9MCmRcjKTXyFnHm2forMxYmJo36RRdgXsZeLMJTXprkwV6ktZ\nrRpB\r\n=Gjz8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-react-jsx": "^7.12.10", "@babel/plugin-transform-react-display-name": "^7.12.1", "@babel/plugin-transform-react-jsx-development": "^7.12.7", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.10", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.12.10_1607554101588_0.563784418206634", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/preset-react", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "5f911b2eb24277fa686820d5bd81cad9a0602a0a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-TYM0V9z6Abb6dj1K7i5NrEhA13oS5ujUYQYDfqIBXYHOc2c2VkFgc+q9kyssIyUfy4/hEwqrgSlJ/Qgv8zJLsA==", "signatures": [{"sig": "MEUCICkkH43gsgoC1eKbHxHRShj1aP1ks8Ehmuuinl7+2i8vAiEAm6ULm4Z9rKxdSkwVW1Oj1c0p/KWCA6mBOsERL+LbJP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhNCRA9TVsSAnZWagAAy2wP/A2mq8syIfRaYwijFu8Z\nJflLUInd6EDE98VGiWoSDxC2PwymMXSUeQO4MQPhEin79CFRO1GsgBkJLA2u\nXMImbjOGrBLxwGMv1S34BbFbuttTNiEBdCpKFA0BujiIS9gigc2WtPz3TCje\nDj/jV8vvXZTh30bdfJdg5Flil0NgPyqwcfaaU3ogs0Fl/4o2xX0TEIUBb0K/\ntZajBom+YetZI+0wmSRiBfuDsqC9rn2zIccEsdY71nVps2ObgpJmtFxiGiEz\nhI8NcEsFD8/5jg4pfGUbn81rx9hXukaNsu7XrUdnywq/rYJjfRiigL7FQGSj\nq3/CcqKrLtzn+hUgKXhq+nL8bwLhG1dwi2bjm+bkaYssDxpffYsRLd9wIZse\n6o2fzpJxkxDxJxmDeEFRewA2hd9dJd0oAiAJgoD0p0IS6WkkwfRjb2m13cJ+\nUso+TGJ1HToFqxy8+qbmTVuc4/SIc8EP5NoEGU6So/hTqJlyWZWOY9oOEthj\n+FvAhH4yGI9w0jYVKw2K10NF3BAE40Sq8nKM6lZRjaURyCzh/WRJlh9ars+6\nmCkyW2haPS/5bkxgDks7ZYRwuvOw0okVq58IxeuOc9cRLyp0NcqTkscQKHAn\nWhbVjy543+O73xL/V/xvOcqHeArFF3aJ41bg5d+njLngZ2ij6xjiyHlZoWGv\nnwXo\r\n=Lo6M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/plugin-transform-react-jsx": "^7.12.13", "@babel/plugin-transform-react-display-name": "^7.12.13", "@babel/plugin-transform-react-jsx-development": "^7.12.12", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.12.13_1612314701242_0.7304173292330951", "host": "s3://npm-registry-packages"}}, "7.13.13": {"name": "@babel/preset-react", "version": "7.13.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/preset-react@7.13.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "fa6895a96c50763fe693f9148568458d5a839761", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.13.13.tgz", "fileCount": 5, "integrity": "sha512-gx+tDLIE06sRjKJkVtpZ/t3mzCDOnPG+ggHZG9lffUbX8+wC739x20YQc9V35Do6ZAxaUc/HhVHIiOzz5MvDmA==", "signatures": [{"sig": "MEQCIBqwUKyrfM4FTYci+4oKvJ3XEhlmRgmXrpliqGEX+Hx5AiBPvca1h8PzoHO+7dFCFMdW+VWdZN5LWrj4pocHonXTzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlAaCRA9TVsSAnZWagAAwfgP/2jCWM1BjoSdY2FqjcjK\nzu8bgPegFMFixTeOa7pR+ixmgmWhhkfcfFMImmk4cuo96T8+jlt43EQ+A1p8\neBQuD/11ljC2nN1aLzrX+4WO1xWqxK0lFhg0RGIzbX5pA7qoLq81GiJNuGS5\nmV5YB1Ge8PGjCVmXUV1nfZC+7iRliFPeZpUaq4qdeQzhvi/EVv7RhkQqJiJS\nPxWSGwYYGQszYMN53BHGF/wu9rN2dxNTMHIKF/16IIDlRuKtS8MQKZlrx/Dp\nsgZyqKCUF3gRRTm+Rb8BxQ4GBY8eukknHElnIUeqjpZGm6Lo/3ozbkR34PmT\nR6dcsqsvHiYWcN++KSt/AS3lA1WJ8HgFMFjXDmzJya2lF7I7y7Erl7fjyquH\nsrgs2B2fVHShqrd68ZXT4nwis7XqxYbnrH4HmmFcxbEo+xMPoY+jj2QUFeDA\nVSZaSY97UdDzvcFeiqkGOG2LQ9yFIruzynhobAn2PT0Om2rpjNgKTgoGbYfc\nbCvlhRepMuTq0z7oiyL/7ZTSe5X/KXyy3fVGl/wzHkxy98Ycx1OcjPxgErik\n4JiCsA+Y024w0Uk7M6Y1nmjKI08n8PyTDOT6W81h2EbHa4f0xREvMZuBXrzd\nBsQUf61h7p100dU+Wl8hX+kjAc/SFVWP3ayTmvH4ie8G4B+XOjrulwRJyrra\n3KJ+\r\n=BXDh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-transform-react-jsx": "^7.13.12", "@babel/plugin-transform-react-display-name": "^7.12.13", "@babel/plugin-transform-react-jsx-development": "^7.12.17", "@babel/plugin-transform-react-pure-annotations": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.13", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.13.13_1616793625959_0.4934756926253896", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/preset-react", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "0fbb769513f899c2c56f3a882fa79673c2d4ab3c", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-XFxBkjyObLvBaAvkx1Ie95Iaq4S/GUEIrejyrntQ/VCMKUYvKLoyKxOBzJ2kjA3b6rC9/KL6KXfDC2GqvLiNqQ==", "signatures": [{"sig": "MEUCIEbQFsSLlcGR26AMO1weC/299b5M0nmPQDeuMJ9ppZ1vAiEAwkFtghrAUb5OjjQtbuKUrHWkqrfd+yK21c/UiOHjSf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr8CRA9TVsSAnZWagAARFkQAJDzClQ8Ohifs66zq1ZV\nqglgveiI1VDw7A3PXxzrWbWmcvLi4MgIz4X8bQvzqYnRhnC58utFAleDtzWX\ndttCI2vdZe/5ln9wMniNMwsR65pdFvQBz9OUe4mFTG0LyxcvUDsrg4a/QMsZ\nbge8VYCPqHb5Pss8Fljr83tw3ivsuYbXlQPWT7cmNc1JyXfjkSGRp+iAATCl\nGVelsdRYa/vqO2jhSFItj+Z6NYYu4bCyaByKNKa5tS96DvGaRRQlQ079bwMD\npIAfUwPmHhtBEY3gkWL/s0kQjcvm6B/Jy2xkObNjq57zPGIp+17xrXET8D0x\n18NZZenQzqK21sNnSpMeCfS4YAbaUxSA4XxE8ICt39s6M3EHtYP+h7s3FnEl\n57bchLNyxhnP4pDYYDIIV+WgIa7R8iAD/P+jBI2kbYMAkubHLn4HiRmAaaFm\n1efWO38WOpWFfzC0JPvXbBfxgbRR1iPHL1Y+c4jjamxSdcaEkrDkeTWxkALV\nxf7/UmrJn4DS49CP3UfMMPUYVG4OJvKkKn1JOckBt9AJT+WQ5AcgLPUYXNM+\nhKaPdnn+/32m5YipTVh9h2TYllqe1Eq9lLYyxONlTj1rAvbQVTGOK2UVqgTW\nAwwOJu0jQONtixDZ2sSA8tEaSf8EQU3JsC6Ploi954L8Iz3+xkGvMp1UTWJk\n58/q\r\n=zVBF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.5", "@babel/plugin-transform-react-display-name": "^7.14.5", "@babel/plugin-transform-react-jsx-development": "^7.14.5", "@babel/plugin-transform-react-pure-annotations": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.14.5_1623280380875_0.6626676546724488", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/preset-react", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f71d3e8dff5218478011df037fad52660ee6d82a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-d31IFW2bLRB28uL1WoElyro8RH5l6531XfxMtCeCmp6RVAF1uTfxxUA0LH1tXl+psZdwfmIbwoG4U5VwgbhtLw==", "signatures": [{"sig": "MEUCIHoiC0uc382NWBjVBbr6rU4bk8m6TF/Yv7cQGLuxtPxYAiEAy8JFku7UMSbwbuq/80sJuvq7qBA79MO0oD/DxUZdx6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11992}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-react-display-name": "^7.16.0", "@babel/plugin-transform-react-jsx-development": "^7.16.0", "@babel/plugin-transform-react-pure-annotations": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.16.0_1635551272486_0.8005565642644048", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/preset-react", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "09df3b7a6522cb3e6682dc89b4dfebb97d22031b", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-3kzUOQeaxY/2vhPDS7CX/KGEGu/1bOYGvdRDJ2U5yjEz5o5jmIeTPLoiQBPGjfhPascLuW5OlMiPzwOOuB6txg==", "signatures": [{"sig": "MEYCIQCFG1HAxn9h2N5lcsSkHM7xRfnyUuXu2LyZCQJviaqh/wIhAJHDncdaRDrdNxvVf41Hvvsy3X2XWz0a2vCvOmMz33zb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kuCRA9TVsSAnZWagAA9CYP/2rWVuh6vyecDUheJl/8\nwSsRO8RtEyYlC2tD63k2JslvxCYcRP9GXj8FU4tOIz78kphSOCX20z0+jjgG\nEo+yGxnAJf4XcbesU3bUjvjLw0ifK3kJXEd7+UejCCOsgzPwxPoDkJJrsh54\n1Mn8kikVQNRM7a7PCndAA6HsnXpH5M5BUjSvauJClgAzhDi7fEDzw7YPHhrT\n8dROsHee/ofhJJrfbcUmKR1L8x+K+Zcw5WbvuIP/yr9Ily7qczp5frTM89V1\nWK2rgvcQ4H6FFqoX04xpCtmG983PP/GNM1qFR6aDchYxva3JiHrvCR5xRkHc\nCflwXpZpail/+xLYWkE1kYOCEMgj25IBy0EwY4MIBb1VvuU7dg+swBVVCkN/\nDjI4abaHtBzQiJGu7+VMHH1hsYkHLccZ2x44+v39+nzGLtk0XUMEuwfIwlzA\nQjmccHi6HIW3HKVnn3NrMW+M0Hv07F4QWUaoEEusPfglL/IQYS7Fc/VAAicC\ny+6VBbaOJm+kVNAXJrJ539XWwqcE9Te1RTPn+NNgGrHzlIVEGFo428PL9F/L\nHuZgYnOuVJ2WhWFjXEka57CIsF0WJLAoxEmqj3fHgZbpD0WVp3DuOcCZjeOe\n3S7nEFLFpgjQPopjQQRL5LkWJ58L/PIt4M93noBtpbH/ofcLZa4tIVbHCdCi\nSyiV\r\n=lgRQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.16.5", "@babel/plugin-transform-react-display-name": "^7.16.5", "@babel/plugin-transform-react-jsx-development": "^7.16.5", "@babel/plugin-transform-react-pure-annotations": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.16.5_1639434541881_0.8129265366779435", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/preset-react", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "4c18150491edc69c183ff818f9f2aecbe5d93852", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-fWpyI8UM/HE6DfPBzD8LnhQ/OcH8AgTaqcqP2nGOXEUV+VKBR5JRN9hCk9ai+zQQ57vtm9oWeXguBCPNUjytgA==", "signatures": [{"sig": "MEYCIQDjMqzC9bKu4aYLpJPyZBppPiMOOGMmxsJWkMEpboESmwIhANeen2F3r1BJ2WOxzUI8d2wLJyqu3meQQs0BpLknIS9C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1cCRA9TVsSAnZWagAAmwEP/2+rLWJ3XAZVt3vF97YX\nvq3weBM7g0ZAPejaasdbxgb5SkikEOxGo8kPYM75nX6a+Jn7Lf8yqzv2XsKw\n9WJqHZSMUkkBe+qgOigSK8JcocEiQIzyiV8DhhyhBcYPDwHDIlOy8LeAib2r\nnydrAuxlzEO6cTAdiazYwtrFQWplFfvNh57auEmtoWSgHsmVTTKI1rekXwq+\nT8J6QuglZMb2vhseIq+Hl1zb4sRHUwrAHRI7UMaWGvlO8aZ4nUDzR5cKBP9p\ndK7lWgTFtpDOXks0IsbKv8eqSV8qmo4dgU/sy26JVcwUOvqNeN0rikSSno3X\nTUE5GqzUz5Yjbq/PAw5M/foVsh7JEPVX6KtoM10f05LZgwQWN7n8UixWBUji\nWfZ77HVl5S1ruaJ/FC9Q3oIGslHIOsS3Ku49yYFepL/sPB8RdWe4hjAYWWPK\nF0LoqZsab5MH0tpcrrViUSXJqhsNRZAKP4ZxuIolI7D8nkFJQcryzHyULRZd\nkRgEvvjFacFqVDWFquBOX8GLXkQw7jkdiGVhTKdjeM2nfbmQVSgFwsTAnuP8\nU84d0tpDwuXgRWX0yKabYGaQ6IpmvnbQPGouJRlQOaEHRSR3UHJyF4GUuABk\n47U63ABAWfI/ei1GON+Gs843EtbtbPSFYnUSumg1IFvgISpz/9kIYHN8lQxD\nxV9V\r\n=7dLn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-react-jsx": "^7.16.7", "@babel/plugin-transform-react-display-name": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7", "@babel/plugin-transform-react-pure-annotations": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.16.7_1640910172406_0.10856239196834228", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/preset-react", "version": "7.17.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "62adbd2d1870c0de3893095757ed5b00b492ab3d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-h5U+rwreXtZaRBEQhW1hOJLMq8XNJBQ/9oymXiCXTuT/0uOwpbT0gUt+sXeOqoXBgNuUKI7TaObVwoEyWkpFgA==", "signatures": [{"sig": "MEUCIQCjGBf06luOuuApaBaqstsQQ1PDYEMit31/3fnvtZWr5AIgSDSLhq7XTrfK71kUEEQXC/amX/MIzrKAvLb8WFEUiHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo16A/8DxsVY20ub/J87zv9sDzGSgB6dinTToi8yHa4aF3O70G1GyZY\r\n5xKm0YRrahzFDem4eOfhSwUpFgQh5g1ioRRfH1LHSBDCRuykhdeb7EQaLUp9\r\ntxsCy6TYsqO/O0ULGcLl9Np45h+By3yT8oG5AiHqcUMnu4ptgiPIHk2NuskT\r\nlOewMbW/bBqZfs6MuEHKISZ1CiXo/XDLSyLLtHcVo9CRgk+wf15F28UCrG6A\r\nJZeNu+fe6E5MqMU0kqzyzzYEQqlOX9DLj7Cy1BKb/EGC4arbi1fNtcMsvcXs\r\n60z+aC5qnDvhtSmPBB6h46FHIOwN17QOstBPW9gZdGpNhZjAPoapIUY0XKw/\r\nuxh1mO+zVUiUJpJrMqJte6Sudv8VNvDUNClEQRd88d+m1aESV92S09RDsKbI\r\nDrWg3X8Aobo5zNJsMWOwr7DTh/wdTjnTcwmbzLxzr1mrjFaMHtfyb2E+7axP\r\noWGadj3Kt3vu0gjNPeb68evb2ZAfmy+fD8m1U0q09+zte/saLAjBqMCTTYun\r\nvKlkcbFk8cDbHJ2Uor64exfoJGJTloTfgYuzlwRkGfGc0kddpRaO2AkLAGSV\r\nbwGlcSCAareURgRAQ5jwoTxYO8LZEV6MAL7NVPQslxLblWtGS/82tHn6ins4\r\nFpQc7+SShrgt+tPd5gIO7JHVDUljsdls/3I=\r\n=tq4i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-transform-react-jsx": "^7.17.12", "@babel/plugin-transform-react-display-name": "^7.16.7", "@babel/plugin-transform-react-jsx-development": "^7.16.7", "@babel/plugin-transform-react-pure-annotations": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.17.12_1652729594250_0.6501085921022256", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/preset-react", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "979f76d6277048dc19094c217b507f3ad517dd2d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-zXr6atUmyYdiWRVLOZahakYmOBHtWc2WGCkP8PYTgZi0iJXDY2CN180TdrIW4OGOAdLc7TifzDIvtx6izaRIzg==", "signatures": [{"sig": "MEQCICjHjoFSfAIoM/UyC3M+TIH3KKMPSzmT92NFnsKd7jZ1AiARGkSjQAEiHTvxfPXztvS9iym83KwAa6KWK0Yb2+/HKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbjhAAmz6wCwMhovpp3AiKMVGUcvGP2ebN8yZlTtKLtTNBW4/SQEos\r\n0LhHkK4MjTq7xVXOA+JCdAouTeOJkSdVnEewa4NIeBLzGv99RzWP9G3gmcFc\r\nPUln5zBoSnYubZ4LosUP76E51osqNl1/eIiRdPFs/WUhwsl0IqNr/66fDT0x\r\nByyfTzg2NzFag/6r57kYHIfGhd0bA8LniqW2/0UntuUmJLKDjoWqyG+sSsMi\r\nMws29XGW6aavcLvZYVNnoBh4hCLUbdbbn4vRV2SevtfVQgfiez2MQD986Zy7\r\nZZYzvQkc4v7G6vyvC1/zP2FC6broxjALRUrtz0WDXsZBGp4N2VWECgjgTvFR\r\nvo1w5ppCq2CtQbRjv9kg9p5ce18biGg6ak5jAKtSqq5lRs6P6GwKZPaBwIz0\r\nffiLLeGrNikp3N9RvgzqBTDivNCW2cgiZfmYHrqD2CQjvHsipm85KqWzpRQO\r\n/CiY8AUBrVdYIm8qqBmA0uTVrHJ8OHdZv/RdNlOri0I3DMdzF/MbVNA0eZY/\r\nRzRgjoKbu8bOB6Ye8iFZjWB1l1fg3WPBVH7pzJJzQwXo0baq2PbXRALiTLvY\r\nrvKWcSP4I2TkMPkI5ddnP+lSiGDnTqS1OjMxxJDLxaLGOwncTeKRaF7ZQoPR\r\nS9Qmcn5EMNamhqcFJQ5k+hjfSsEhw4Ul0Pk=\r\n=3mok\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-transform-react-jsx": "^7.18.6", "@babel/plugin-transform-react-display-name": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6", "@babel/plugin-transform-react-pure-annotations": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.18.6_1656359429226_0.612253330938197", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/preset-react", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "4987a826ff12072b1f96a3227c17027ae7db57aa", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-JBX5qTgnuGWtTglTMkTuWP5GLAEzlLC+eiVI4TA2s827Y3EsHZhsxTHnP0BIPBQzAA5e3hdPn2AOGOZWf6yKSg==", "signatures": [{"sig": "MEYCIQC+pygdXfRsXjz3bwcz8LsrhGllxiUmuZeFwDUUYpc2cAIhAP96/wZZ4c8rZSqNb4yI4UCzAvDhQ5nqcoVumq/obUZN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJig/9FPO9MUDnU3aDWB7e5dAiCwD0PqbsbnwBAZslnngHctc7SLt/\r\nR2n3VhhSDxVFNRCV2pFSdo3qr/Rwurov8yVdiy59SiTMKMACnTn//lXa6qeQ\r\n3YfFLEExheCF5uNNTjC0LR1tXWN8bDUogpbbY/i2gRryrJZngR5Gv8pK2x6u\r\nz+M1zpuZjWgLOs8dXjn3nH2pZe5wbKGXsjy2YWOkwCIAwcbjE/WK+0hjfMbV\r\nqdRnRMdDeg6AOkhDg63KfIIeZBiac1IXNZcLrtKGHIDXhwh1GE81zgUl+aXy\r\nJgK1YburuLLtIhqvXTYd46YE4ERLchZSerr2sQi0tExbRonX8mk+lP+XRQTQ\r\nbfKxlGKWCs1glreoET0vxG2S0J7XlI97/9yHAp+4za+EV/b3eVemuN6ivD22\r\nysPRJmXxx6GxwXpC1BDTXwGcFZnVpQDH+PSN8ocGJP6jYnfVSVOG5FiudS51\r\nnjaJ5kDVRaQuuiQbmvwQM+1xY4RbMDAKAxuj/OMqvIgH1AuwkjKpupPiCkMZ\r\nIXY95zW5sNp4LK0JsIdHw6he/FP2WfbCbDQ/IqN6jbZQzgcMONkf2Z7nTuw8\r\n1PH4DqvdNA89T0EEqMMCxFNJE0CIuGHiTHfNhpXYC7ImKSYQxg0DPS5wox5H\r\nkjSHgpEhQm503OOEcncjjbcIdipD6pxtONk=\r\n=w4q+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-validator-option": "^7.21.4-esm", "@babel/plugin-transform-react-jsx": "^7.21.4-esm", "@babel/plugin-transform-react-display-name": "^7.21.4-esm", "@babel/plugin-transform-react-jsx-development": "^7.21.4-esm", "@babel/plugin-transform-react-pure-annotations": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.21.4-esm_1680617392480_0.02047424455675051", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/preset-react", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "e327bead6cfcfe7e591a3026f76c542cee898c39", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-twhee/ULEO5zMzjhG+NhFmofD3kXuIPZxvuSFIa59gtfJ2pWuTnAGQQvS72Wu5J5pYCVpSo2nEwAwmBNVMPSWg==", "signatures": [{"sig": "MEUCIQDjXKnHbSZBd6nIjK6RI/6VZfZ0nSziBZkZS5FZ0Q26rwIgEwYT3f2wg+1LL0COfU1Ty0jfFpDC/Mr3GFwVmXuBoZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11618, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQEQ//QKT/E40+FIKF1nVux+bVU5ZlDCJ+vihYoAFEDK5GzHyOuVkW\r\n0ukK2Tlf+WIkZ+RQiOjG/z5QUYJmVlGnmmtCJYwpXCwgzxwpmKj8PNf+qpnD\r\niLhmBbHcvwaiEX2ZfVT1bKkdDvpF2YCbwYaJ8xIDatCLbnERDZsKqbTLt3pp\r\nMAl8I9YA1Hwln4BW/3fprFuAOY1Az/w0Xdv348StRxqTfek7cfiXCV0VpHoN\r\nWdYWX1qPPYsQniBOFa2/pm+vlHARyqH8I36uKq7FugfCoI7jM7zyRNQhQI0i\r\nIqfVpoUfpeq5M/Ln3wQAfLxYnzO1INn0ZqhiAXGuQ5Lfr6/qjRdjMVJL7WGe\r\n+RrMK+zf/+j+t7GWDRlswAt4tSCeJ+iUwUQtlaMmzx+W1Zw0hPUu6n8oUATW\r\nD+od9mblpsLFl5wkXML/Ol7aJHJAhcetXClxyD508qD6KebT9N51+DHr8nIN\r\n2L2QJ9STvmsv/VtEvbXTr1s2U2ixq2v24JzPxuHUcT3HCuOy29EfZxw3/Q1b\r\nn0TaIw02lrQAyWEwrYWGnjrccgKelK4Mka1JxZbNBbgypMxgUZP+qtlaRhPR\r\n5uoWNIysbQlIwVZAmeCmgTcHgF/w+ByIse6h7ujR8lJQCxSPkvpFj1IK2lIG\r\ngU5z9BvmOyeE519MXIlRLO+IVZHibMyp+lQ=\r\n=keEC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-validator-option": "^7.21.4-esm.1", "@babel/plugin-transform-react-jsx": "^7.21.4-esm.1", "@babel/plugin-transform-react-display-name": "^7.21.4-esm.1", "@babel/plugin-transform-react-jsx-development": "^7.21.4-esm.1", "@babel/plugin-transform-react-pure-annotations": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.21.4-esm.1_1680618110056_0.9589630038516166", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/preset-react", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "cc366b4420d32bf67b11cc7f77500cfd70d1cbc3", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-32Z+TMULQ8QgdCnNqTfPYPRcASn4p9zbPdwqSszFNd+Yjj9wVQrEucGwPfarVXwhCHPH9PfMeqRLuB59PwEJaw==", "signatures": [{"sig": "MEUCIQDCo8H3No5YvIBdQXCW1YYU00GimdyzdjgIvBAAHt32FwIgfT8WM7mLetNWEVpJDe2diVYmSggDVb6YKp/BK5UYeOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIyA/+P1OOBwSCv6KLNlj7rcgIR4q0/Tp/1nS311i0UP+40/VTHhaS\r\n7nZYwogl+PO77X+RHTf7kJOMIz+e2YPqZJDF1u+4MnzOMImluKJ9QcJxJBRE\r\n6l1uXV9AbfM+zyNB2tQTQK1cgA96Hl+etwkqpCDBMjgkP71AS32R8euzJeH0\r\nHnCV8hPhOoEDl+5O7cx9OWB+6XCdh8DbmnntGGlAm3r295HBuJwBMsknGn17\r\nibFGINqNzMxSaI2tlSfFFu3eo05LsXTp7YG0d0XGK0ZD72xOZ31txv51jKrM\r\nwIcZz3mGuTBLn2oLn82eSwG6ti6dmAtMmxeprSvEbt0MqLt58WCLCMW6rMbn\r\n76INVCpthhyCAMxwe8+PP1rLb6eBbGBERYuCOMsqmh2i2uORSIFCILaj31Zs\r\nGsUIsDJ7YKKps/eF5ItydzeliTQWyaO5bNRvJFvPGZ7LU0NNei9k9896cJxn\r\nXHxqypQHPe1PD6FWgNkoDxxPe5MZbyYRBOdHNuGUT/sOAjJDeYxujE89WkCQ\r\n6MDPpWewy1cW1JujMv0DWVMnW4ognhXZcuqnfWUKUt4UWbhz/nL6DaxqllH6\r\nG5lCFcHVzqGscEXrv9Xcq6AXy1IbijUHdD2cakoSjD6OcF6J4Ejj0eHe2ENz\r\nd0eJdrZGK13ujK/NMQziJwOUha9VEosu6/E=\r\n=/llp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-validator-option": "7.21.4-esm.2", "@babel/plugin-transform-react-jsx": "7.21.4-esm.2", "@babel/plugin-transform-react-display-name": "7.21.4-esm.2", "@babel/plugin-transform-react-jsx-development": "7.21.4-esm.2", "@babel/plugin-transform-react-pure-annotations": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.21.4-esm.2_1680619191262_0.010876789922408259", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/preset-react", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "95914862d3b788aa19d0bf6b08bdb2f0cce5d56f", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-x/lBMKqbBUtsG0yYR9ao9MrFNFBU0CPRJlQWrJGkY9Obse7Ak4ySPCNeVtDa0SCznyJFzvf49DGcGuW1OWeU9A==", "signatures": [{"sig": "MEUCIEjkxmQwRzv/GguIfuLuIN/+VxsTCRr0OGc8xzhZucpHAiEAwbTpET244jLvPulTuOe8ToWVM+IoGDL8TyLjYzov6mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRNg/6AoMM1y4OB5dB4qsCqOYqLhoXS3XhxkhX2pR6n3OoquD6DLNT\r\ngT6aZBjZYQaa0Zk0WNp9jqJJrVgEKspinHDorjFYEOLw6T/H9phunFdTTsR4\r\nYddoI6XApQ1XfIFtP2BpAWgY2rX8b9JX+g/OuJRS2bIXqG/T6REbaLBq9vHF\r\nesQd5tMeFh9pT+PhW5lr3dQtMuH38EkQZcCAv85db18CjVnW3R5Y8Pz+2UAi\r\nxGnNwKCeDRLn2mKgb3xdgPkYWNcPHjLS1ADVglBJ1F5PvoVnt+xJcYp7d5u2\r\nPWP8PMq8vKk0c2YuUfJSY76Y2n4uzL2tbetZFs4y5O2hJY0d9QIUOrSFMdng\r\nTV7IdJ/NHf/0lPnI8ZUwMimZj99zBgmkAfbYD8ll+Phje7YBCRDLo6yMCOcj\r\nFA4ZEYYSjGaCgmxDtK7wkJX44zGqgZNl/NGZvIYwtLT68uZfDbjvRoQKLHFz\r\nT3Nt0PO+/J8HgBardy91PcNgYAGbKCjYQeQwPQyndkOJTOHfZas2dYSgdhzZ\r\n+LQsY+XIR/8tBvWExrBdN9bRtLSaNDQIsbMKO9sGMvwScYMldwSMiSxztTTx\r\nmeUQ00v/900CHBJjZla+uNT3dmelNej/vD/ZgQZxxuhTPmBafQm/yOxIFGXB\r\nT/xX1knOLDBG8oCmbNM+kWViZzCBMzweBAE=\r\n=WSC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-validator-option": "7.21.4-esm.3", "@babel/plugin-transform-react-jsx": "7.21.4-esm.3", "@babel/plugin-transform-react-display-name": "7.21.4-esm.3", "@babel/plugin-transform-react-jsx-development": "7.21.4-esm.3", "@babel/plugin-transform-react-pure-annotations": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.21.4-esm.3_1680620197161_0.7478823923233411", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/preset-react", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f836373230c107310448dbbabc7fc0afdfa171f7", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ufTyJsrybg0W6t9WGj94hCnN0RbnhJmlNddsOCXxmsHuIe0yjgxCy0NU3DzvRWcmTZBEJK3GsfZW3tdVE1qBkg==", "signatures": [{"sig": "MEUCIAuaZGEL9Eqt6yR0PYVXYGn16Knd1F92lY+6CIWfLrwrAiEAl+PLm8GwVMuWX7e0HkS/ozxAAK++Bpu63Ng3FsxDD9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHZA//TvEzwczTuUZL01mZRD5i2px4ceN3ax9cGVkZmFe1gemGN3zo\r\nhmX3UHYMTErP8gW+r9TyzQLNU69nhZDMo3Y2O3eRjJf9kxvokrGqU71PHPce\r\nEukzm2HEQCWnjgm/qbU25K+DBRyhlZE8xqqF2lRqjsYbiQUdgkzxsB/7EeFx\r\nAU8F+zkNOsFYXfMKDnbSw3LQ2lAK7dBhrGOl5rphjmI3NHvo271XNTP9K/4z\r\nd948xovgmqIcBm5q6UbhlAlQRJgIq89zAFzxtKvvwDbKHeE9G03H9Y1X2+tz\r\nsob0/XyH1DA4jnvFkYrZNi+Q828bvEM2M9amkFDEiwi1iuFChLCuuRBCPNbd\r\nLGydl9oKgiD+8fP6sfI/pKm276+uV2v2X1tmofPvXUsiVqTePPg1SNYUW/pG\r\nNG47Rk6F1bN7gr75Te+Rq6rOU2ug3vAk/PzqERZ5eZDZl3IKU+RhbtVy0/rv\r\nUCGOLFIK4qgl2bINLJKSytOgMGPBN2AyAkpISoJb+BJa+SXDUc0xQbyi0POY\r\nKGSrMiWeLf+y/hIkasaCKlKMZCdcKvmLnz7KoimjlzbJdxZ/V1KfEccA1Sht\r\nZDqJFKHB8v85VfrCfy5tUDaLMpHb0KPMvdw3DbX7PW4IQR2qusM1h6O/k/5p\r\n08SXoF4sJhp8OQiXup0O3bOMzQ2oKQsyYjo=\r\n=YwNc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-validator-option": "7.21.4-esm.4", "@babel/plugin-transform-react-jsx": "7.21.4-esm.4", "@babel/plugin-transform-react-display-name": "7.21.4-esm.4", "@babel/plugin-transform-react-jsx-development": "7.21.4-esm.4", "@babel/plugin-transform-react-pure-annotations": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.21.4-esm.4_1680621226026_0.5856674105251638", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/preset-react", "version": "7.22.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "29848f0a9c17cadaf93c3ad3a4bc44c15b2f9f63", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.0.tgz", "fileCount": 8, "integrity": "sha512-C3U/lfsKJv3eQj0AMoXQTPqGgYXaqiJRLD+FPIKpUv7uppWJAifMCn4CrclCCIW0NH0jY1IXSNYgdKZO0gUEzQ==", "signatures": [{"sig": "MEUCIQCVqDv+64nXFdfkTHGRoMv96SAbJ2nR95uQ8wOtj+aqxwIgUPf5ghBHeFhMpDX0UvEY4BhWm52EuKsXR9Og27Kx/dA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17735}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-react-jsx": "^7.22.0", "@babel/plugin-transform-react-display-name": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6", "@babel/plugin-transform-react-pure-annotations": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.22.0_1685108744688_0.8260751924598881", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/preset-react", "version": "7.22.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "2ec7f91d0c924fa2ea0c7cfbbf690bc62b79cd84", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.3.tgz", "fileCount": 7, "integrity": "sha512-lxDz1mnZ9polqClBCVBjIVUypoB4qV3/tZUDb/IlYbW1kiiLaXaX+bInbRjl+lNQ/iUZraQ3+S8daEmoELMWug==", "signatures": [{"sig": "MEYCIQCGMUL3YeIrgnNqtry2Fo9+UGYSdiL6LIBBooj1KV048gIhAK614Zryd2XsngKvO8viJfYWfpRkHxmuFLk4LRaa4kHi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17715}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-transform-react-jsx": "^7.22.3", "@babel/plugin-transform-react-display-name": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6", "@babel/plugin-transform-react-pure-annotations": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.22.3_1685182272529_0.3655815769955666", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/preset-react", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "c4d6058fbf80bccad02dd8c313a9aaa67e3c3dd6", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-M+Is3WikOpEJHgR385HbuCITPTaPRaNkibTEa9oiofmJvIsrceb4yp9RL9Kb+TE8LznmeyZqpP+Lopwcx59xPQ==", "signatures": [{"sig": "MEQCIGQHNyLogmnB9+4mlCSwWZPt33w9yAmg4BxrUVBiVmt4AiBCaOwSWOZ1S2xr5gzZ6BwCQzKqM4/uZUzOyt+n3fEfyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17715}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-transform-react-jsx": "^7.22.5", "@babel/plugin-transform-react-display-name": "^7.22.5", "@babel/plugin-transform-react-jsx-development": "^7.22.5", "@babel/plugin-transform-react-pure-annotations": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.22.5_1686248501008_0.5926416006905397", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/preset-react", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "29d1829a602fc1fec499280ca8638f5b959ecfa0", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-pc7RjH13WibVHsLw8yZ8VITnCOSrDJbcgkgB+cmYoyFy07xqnyRsKlXZi2CIhlx/FyoKCAdBFttIk30nMPyyKw==", "signatures": [{"sig": "MEUCIQCzHV3nftrWP17avgg+WULvluL8IA/dlZ41zKFCLe5hUAIgEPTpz6IqBEqMfayjgqwU0JqK4Hbk8SE5Fmx6dXUdCPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23548}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-validator-option": "^8.0.0-alpha.0", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.0", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.0", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.0", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.0_1689861628587_0.7630748412591382", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/preset-react", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "080ae2f82ad33537de5bf0adf9432a31f14f9f22", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-2wOZKg0ZdGpMXFuC3yCoc0nJkVl8/gjyNLNkqW9I64NwjR9RodwrURip6l4G5dbO832AaFGIBabsh+I/wG2+qA==", "signatures": [{"sig": "MEQCIFCGlYCLSNo2JPqWrELm9N6iCVr+AZeNtG4sG1f2jddYAiBXcWWgd6Y8Py9uv0ZKQGzYtKvQNOxl+UBu6vSf38KbeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23548}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-validator-option": "^8.0.0-alpha.1", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.1", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.1", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.1", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.1_1690221181716_0.04661823231458473", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/preset-react", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "4376f22e6fc528278de9f9011576378219f1a3b0", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-qiMUr7RAuoQKvjya8nBBJlbOlOAwviqTUXZWMWQoD9fxHxk6EaXKVxLI3wXrjj9vfP9ARbXGhkC1+IZj65G7PA==", "signatures": [{"sig": "MEUCIHhf6BmggHOVzjrCpCFxdKydUE6R6HHnCZpUXDEcsx47AiEApgwOFRYw9wGUFABiBeCrlAQLRsp/AbcDNdOMvs0i67M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23548}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-validator-option": "^8.0.0-alpha.2", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.2", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.2", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.2", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.2_1691594122926_0.5092510457064194", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/preset-react", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "9a776892b648e13cc8ca2edf5ed1264eea6b6afc", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.22.15.tgz", "fileCount": 5, "integrity": "sha512-Csy1IJ2uEh/PecCBXXoZGAZBeCATTuePzCSB7dLYWS0vOEj6CNpjxIhW4duWwZodBNueH7QO14WbGn8YyeuN9w==", "signatures": [{"sig": "MEYCIQCjhUSo9xSI21kkxU5Rre27MOPSuT9xluxL7eBk7+5hRAIhAKipEwGZq+SiPT0vL3F3DXpUEPr+zukg0p2g4mFSUAW5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12201}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-react-jsx": "^7.22.15", "@babel/plugin-transform-react-display-name": "^7.22.5", "@babel/plugin-transform-react-jsx-development": "^7.22.5", "@babel/plugin-transform-react-pure-annotations": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.22.15_1693830325209_0.40740517942450327", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/preset-react", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "d0401db74a8b61d4975236a82c9ed1562ee4d072", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-klIUXNx9uY0qFsKbKXNs3JTwraQ8KJ55eMxWBTUL9wCsmfMZoYcblUimG85dzs1meWDQAmM7sGXZw60+0V4gyg==", "signatures": [{"sig": "MEUCIQCrgH382XMDU6IcKMo7tNF30u5YeCWYZY6uVo71XZ9NUAIgTtAh0ddzI6QeFaoBL89hmgO5JWiPqiuro1lqolwvHz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15029}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-validator-option": "^8.0.0-alpha.3", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.3", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.3", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.3", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.3_1695740256722_0.8999495421026247", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/preset-react", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "4cce8de45025c1944158a570e383d484ed3615eb", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-+yqaY9hTYKIoQkkri03U4biqyw3irr2thVJl/iC+nNHi7QGIE1Q7eivMTCE/MXgMfZNKd3gNYaIQIY6qdd5g2g==", "signatures": [{"sig": "MEYCIQDDA8xBuBi13fb+Yzqf3XrF1ksg0kkc2rQWMkl0azlFnwIhANMpOwq+gdq7BtzCZTDhqj0bUqnu8HQfGi2uQq+NLTtM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15029}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-validator-option": "^8.0.0-alpha.4", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.4", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.4", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.4", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.4_1697076409478_0.9194903864423176", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/preset-react", "version": "7.23.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f73ca07e7590f977db07eb54dbe46538cc015709", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-tbkHOS9axH6Ysf2OUEqoSZ6T3Fa2SrNH6WTWSPBboxKzdxNc9qOICeLXkNG0ZEwbQ1HY8liwOce4aN/Ceyuq6w==", "signatures": [{"sig": "MEUCIA795hTjVYbECQmG4LSq87hSzsUa81xLFaX426EnsLhGAiEA1zvHG5xDwou3YOUnOisFdTZJbnab3ls5CXl4jaYUE+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12303}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.15", "@babel/plugin-transform-react-jsx": "^7.22.15", "@babel/plugin-transform-react-display-name": "^7.23.3", "@babel/plugin-transform-react-jsx-development": "^7.22.5", "@babel/plugin-transform-react-pure-annotations": "^7.23.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.23.3_1699513454238_0.12365871853963695", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/preset-react", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "3186260380a3b35c01be523e8445c77674a1c338", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-hIdhJjvyjRXX52Ij9IVKJXFk86sgO7xUUSQc8zuLZjCpFOD1/VkqXyZj2J3M+Xxu62Neavo1sAKiPaTHyDRYOA==", "signatures": [{"sig": "MEUCIQCGT0EjmW7/a77cMMBI2bTl7qgpDg3hQj4Lrw7LodovzAIgGT/BmfwU3b3PGrRWHtgFrtRdDQc/wyd4xwUWTHXdFSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-validator-option": "^8.0.0-alpha.5", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.5", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.5", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.5", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.5_1702307984295_0.5353802769621157", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/preset-react", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "41b9c042c1a4bc6e9fddc1c514f17eb9d433f5d3", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-hoJwGmcxnB/DCt1Qb3zer/x/Ye/cJ4kt8zc2f/+dnt26dWbkhi6Fo0Rsa8UKfLK/E1U9zalCKf9moNHT0r2nkA==", "signatures": [{"sig": "MEUCIDJEEBYydi6gxh6BO3m1oMN1BK6uTTYdowKdTmVkams9AiEAg1vV82bX0n3+GlTRWqyKtYj2tK7h42IBTA//WE04GlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-validator-option": "^8.0.0-alpha.6", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.6", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.6", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.6", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.6_1706285684854_0.49896661917863927", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/preset-react", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "ae843c25be0ed02f30ddc11c40aa7f3eee78bd41", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-qhE/g2/advCSeMQmwAvb56gs1raciV8Gy47mQZh9J1rYl5EHxTW1Ooma04yjd/EVXJWgRmcCePrQB+FKhPfQqw==", "signatures": [{"sig": "MEYCIQC30qHWJ14FpUkBjocY/r9Cmwc4q700IAtkUun8jxBANAIhAOwbzXITcNLdFbUE8zPqWm5eVYOafzhJ7Aqfm5A+pD1q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-validator-option": "^8.0.0-alpha.7", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.7", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.7", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.7", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.7_1709129144387_0.6732160823487585", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/preset-react", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "2450c2ac5cc498ef6101a6ca5474de251e33aa95", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-eFa8up2/8cZXLIpkafhaADTXSnl7IsUFCYenRWrARBz0/qZwcT0RBXpys0LJU4+WfPoF2ZG6ew6s2V6izMCwRA==", "signatures": [{"sig": "MEQCIF4K7j4G8bTTWOmgYljQoPCVIG05JRFcUdWJ1TjQgIlHAiAi5dj6FgHEKEyWn2ZUw+RLwQ7gJn0KROmpwcQCWOo5LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12216}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-validator-option": "^7.23.5", "@babel/plugin-transform-react-jsx": "^7.23.4", "@babel/plugin-transform-react-display-name": "^7.24.1", "@babel/plugin-transform-react-jsx-development": "^7.22.5", "@babel/plugin-transform-react-pure-annotations": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.24.1_1710841770512_0.6525039269176607", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/preset-react", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f5c014b375be2eb0c51c3f079c4fd45d325d7b84", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-lvUzQ+cxzFx63b8llWtsLtPNMl6Aj/3pJWI+39JD2OK2Bk8u3NhKY+da5ewQn5mvu4NTToDXh12NJfsUL7wmOQ==", "signatures": [{"sig": "MEYCIQDUO9xiyqEgNuZHJnID6M0QD0xtA9ytVrrv2+TKnzgBQwIhAObuS22MZMQZ6lOrcDefi2Jqx1OKvebR2bzBk9iYniok", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15056}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-validator-option": "^8.0.0-alpha.8", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.8", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.8", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.8", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.8_1712236819814_0.4343829551939178", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/preset-react", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "92eace66dce577e5263113eb82235a0d45096cae", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-8mpzh1bWvmINmwM3xpz6ahu57mNaWavMm+wBNjQ4AFu1nghKBiIRET7l/Wmj4drXany/BBGjJZngICcD98F1iw==", "signatures": [{"sig": "MEUCIQClR8iCfWQSPOD77+j8qa+yRVC2oQ9UfK+88iWaRfHaBQIgFTdLnHGHK/y78gRsuT/4oWu7s39vjQRaWUHrv2y94GY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79979}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-validator-option": "^7.24.6", "@babel/plugin-transform-react-jsx": "^7.24.6", "@babel/plugin-transform-react-display-name": "^7.24.6", "@babel/plugin-transform-react-jsx-development": "^7.24.6", "@babel/plugin-transform-react-pure-annotations": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.24.6_1716553510524_0.5107830960187467", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/preset-react", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "fd2ad3821a24d68be2aa636d554eb08906d8546a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-EAVrPAXFYe1atAAa6fzpivyw2FrATagvUqAERe+GcFJkQyHo6+4zwfmFHxQGg3ABtWp/wv/BvGgu5OZGF+WyxA==", "signatures": [{"sig": "MEYCIQCpxTycy8Qu9rWKIWaV9HWAbHYw/6QHsw2tbgXzJh9NPAIhAJX3jOL4K7saGyzhI3YgW0M/2RMCVSH9075Cazp7R0bz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83348}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-validator-option": "^8.0.0-alpha.9", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.9", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.9", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.9", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.9_1717423554121_0.007340010544917064", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/preset-react", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "a9b718030275d7cea558741c0178dcf1c6595b5e", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-qoX7spb1Gmeob9ZTFLw1RWNmd18c0lIBSKuy/BR7VJ/4s4UN8VsOysLS+5jGG8FIrtHLm8Y+35Fq2lmlW4qgDg==", "signatures": [{"sig": "MEUCIQD/+J2wq8etDi0dWqvPvkebtg3PItJOpM8GoT3D+Pso6wIgeL8ND3uy+VKyPfLxQE0qbAp7dKKRYHOK9iIZKLQXXU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-validator-option": "^8.0.0-alpha.10", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.10", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.10", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.10", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.10_1717500048455_0.213880069738015", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/preset-react", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "480aeb389b2a798880bf1f889199e3641cbb22dc", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-AAH4lEkpmzFWrGVlHaxJB7RLH21uPQ9+He+eFLWHmF9IuFQVugz8eAsamaW0DXRrTfco5zj1wWtpdcXJUOfsag==", "signatures": [{"sig": "MEUCIQC1FcheYzDcUWoRRefVB3lIQUuC6BnPViCmcSBNCU4aPwIgKcQkkIlmDd7HyGC+Oq8Z8kWZuBg9VmfqMzinFLy2gkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79975}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-validator-option": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.24.7", "@babel/plugin-transform-react-display-name": "^7.24.7", "@babel/plugin-transform-react-jsx-development": "^7.24.7", "@babel/plugin-transform-react-pure-annotations": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.24.7_1717593360945_0.8095467965355352", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/preset-react", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "592512496d8d30caf2d37534a6c0d62d0c41aed3", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-ie7WmizokrpQxN+mR6k6z93AoNCJtDnLibwvN+TsNSMVFvKcqdLOr28PBkz5GzyPnTdvxDoLptWGAo6kFSJYDA==", "signatures": [{"sig": "MEUCIDLAdiefjV8T/qng3PVlt0Zb39TjawSlDMHnyDsNuNgTAiEA9iem4VN4g5hzGo4K03MSiIDcMlg9QvM8ieswbrE2lwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83251}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-validator-option": "^8.0.0-alpha.11", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.11", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.11", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.11", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.11_1717751771870_0.03892430252212442", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/preset-react", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "6ecd8b26b50f9a2f4116cd87d12f12b739f46909", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-Ud6zbUDJnrjEAlx053Rb9MjrabLbsCOa5z+GIj5s3CpvBJwX6LTO1pTCmLNUbDxMh1N1t8XulxSDNd2nMEGGKg==", "signatures": [{"sig": "MEQCIC0N0cz3pxADQBLr1RPaY6wKNeDsm8SCmmII2hMnV0PGAiB9+ILxmaU0j5WFhSpOqC4aZcB4SNu0b30Pm5g8pAf/Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79942}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-validator-option": "^8.0.0-alpha.12", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.12", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.12", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.12", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.12_1722015247029_0.21365783455321674", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/preset-react", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "081cbe1dea363b732764d06a0fdda67ffa17735d", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-GjV0/mUEEXpi1U5ZgDprMRRgajGMRW3G5FjMr5KLKD8nT2fTG8+h/klV3+6Dm5739QE+K5+2e91qFKAYI3pmRg==", "signatures": [{"sig": "MEUCIHXj70Vfp/bVNFMHLt1b+iWKzORt5WjaPeZIIJLFRqpIAiEAilpzOuRF7DfLKAC4A3Ncha5V3AYUzIFBMLPDroaWt/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84493}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-validator-option": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@babel/plugin-transform-react-display-name": "^7.25.7", "@babel/plugin-transform-react-jsx-development": "^7.25.7", "@babel/plugin-transform-react-pure-annotations": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.25.7_1727882138145_0.2388883749334001", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/preset-react", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "5f473035dc2094bcfdbc7392d0766bd42dce173e", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-D3to0uSPiWE7rBrdIICCd0tJSIGpLaaGptna2+w7Pft5xMqLpA1sz99DK5TZ1TjGbdQ/VI1eCSZ06dv3lT4JOw==", "signatures": [{"sig": "MEYCIQCx3xCGaWrpLaedwNfYOH03WbFE5dkr08rX34nQBIeWFgIhAOnVmnPlOMqVU/m3l4D+WTqiMmSeUoW18Dj48JLgm/tl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12216}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-react-display-name": "^7.25.9", "@babel/plugin-transform-react-jsx-development": "^7.25.9", "@babel/plugin-transform-react-pure-annotations": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.25.9_1729610513205_0.308172749315923", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/preset-react", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f8ebb5f5a244a7a3e328f54ce6b96b2ea978f2c8", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-2jtPiOIJ0z/u6F+S73CmWTVTjUXR83gNGRHG3WhQNoBboQlz6QvHTSWObJd5qwD3MSWVaiOAty/jU3Ham9SOzg==", "signatures": [{"sig": "MEUCIQCIl1u9jY4i6CgFc7SJps5R00M86YADVLv2JmX3Wtm4vwIgKTs28Xv8MwCthQkEkW6powm1WhFScBez+eVcTWDMAsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15601}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-validator-option": "^8.0.0-alpha.13", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.13", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.13", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.13", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.13_1729864493753_0.48443421050057944", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/preset-react", "version": "7.26.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "7c5e028d623b4683c1f83a0bd4713b9100560caa", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.26.3.tgz", "fileCount": 5, "integrity": "sha512-Nl03d6T9ky516DGK2YMxrTqvnpUW63TnJMOMonj+Zae0JiPC5BC9xPMSL6L8fiSpA5vP88qfygavVQvnLp+6Cw==", "signatures": [{"sig": "MEYCIQDDTxjdQQ8iqWr0lFDr6r+55EWqcXwYU887rl5VaQak4wIhAPDKAGFWGDrEGzSLnal8LJnvxvzNtwjR9XCKmMiPFBdJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12468}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-react-display-name": "^7.25.9", "@babel/plugin-transform-react-jsx-development": "^7.25.9", "@babel/plugin-transform-react-pure-annotations": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.26.3_1733315735809_0.5473510214647932", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/preset-react", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "f1c6f354dec4970a12c6cd651cabe5a23c3662db", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-t1KmqPLzGbhGo5NWDvwxQ+8Qilr6+P8gtKco2Qi1e3JxLPsqLsJ/TW5lxrCd7eHbJjvawwb0j2/0mwemxN9snQ==", "signatures": [{"sig": "MEYCIQDmtFbjwgA1lbH82KnfYwul4wFrMsJz6H9jLakMds8vAAIhAPHoM4pWF2mrbCMFYpszxGRLC6NgIu9jFZJZTWg6hBXb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15821}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-validator-option": "^8.0.0-alpha.14", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.14", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.14", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.14", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.14_1733504082734_0.6248537233732503", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/preset-react", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "ad402ad986027e79b03449166a4bfdcd04d74d29", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-9eqqILBdUWXnWaoaxoxSryOLuQCnoJ6aU5gesoEARHSeyp7x6C2J1GAnUN26hfADSUhgIEX/cYXa9Bqqtg8qCg==", "signatures": [{"sig": "MEYCIQDa9MxAx1I/5ZlXjRQrk6RFQ3LPwRf8Yjzi29Vkku7RGgIhAIlFtSGdItX9J1TWpN4XRvWRrtldFynFfjmwzrvIigBz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15821}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-validator-option": "^8.0.0-alpha.15", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.15", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.15", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.15", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.15_1736529913019_0.7450961396943558", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/preset-react", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "92427b040318b3ef9973fb507e6888d70b8ce93a", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-P89QnLu3y3xWHB2b36PeJeBQEEn6P+jOH0NZJkU95zqqLN6vJX0OGxBnWghxhRgYPYU0MRzHjrpKQn3gsQBl0w==", "signatures": [{"sig": "MEQCIEkXersMqKjc4m4RL3DinVNFviPiYZ7ZCkfqf51eyOh0AiB9c5QayIlTdeeFWpeKnoVulLHlR4+rHv4cQjjtsQxawg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15821}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-validator-option": "^8.0.0-alpha.16", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.16", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.16", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.16", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.16_1739534386555_0.3040278886855754", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/preset-react", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "5db9ab38477ddfe36676533ef103ca4c1d610712", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-pJhMFPdvleaUv/i7UunRYkeYlLbp+/0steVaIs/WZQqpjohb7fTZ8NBXmvFQBMgfdrm3Z9edugY2pl9vKy9pPw==", "signatures": [{"sig": "MEYCIQDKlzaYHe1zi91VIhFnaHGGLm8U6E7akg9gjR1dQmPeSQIhALJcq2sidcjE91oEERkby2R8UxBSwl4qKHwuvfufBRRT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15821}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-validator-option": "^8.0.0-alpha.17", "@babel/plugin-transform-react-jsx": "^8.0.0-alpha.17", "@babel/plugin-transform-react-display-name": "^8.0.0-alpha.17", "@babel/plugin-transform-react-jsx-development": "^8.0.0-alpha.17", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-alpha.17_1741717540942_0.4766793237655558", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/preset-react", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "86ea0a5ca3984663f744be2fd26cb6747c3fd0ec", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-oJHWh2gLhU9dW9HHr42q0cI0/iHHXTLGe39qvpAZZzagHy0MzYLCnCVV0symeRvzmjHyVU7mw2K06E6u/JwbhA==", "signatures": [{"sig": "MEYCIQDmac9mqHlU/Ll1K/NJE4BwzD3zG8dm/OjCafAgR6JlOQIhAP2jx6x2ciwKv0f+yTpBQEpQ9U9Sc0BWgaIjuaqpUmSV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12468}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_7.27.1_1746025775118_0.5787230374096672", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/preset-react", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/preset-react@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "dist": {"shasum": "43534b0c8ec109fe9e2a76d2671261c1fb470ce8", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-OidjULMlTIF9Ha6yCD3oAStqxPpA04gx954Co8UYDVTM2R7G1MM7n7AglvBtajOTksFePuIPkihfXf8+0CTSAA==", "signatures": [{"sig": "MEYCIQCJi6Di35ib1VCXioyoe1bkZuytyDbTmPjah/tbFIzQOwIhAOiJqhuNzHq9njBwvlG1ZJlX3/djtyOTczRDbWDVzJ+q", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15787}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-validator-option": "^8.0.0-beta.0", "@babel/plugin-transform-react-jsx": "^8.0.0-beta.0", "@babel/plugin-transform-react-display-name": "^8.0.0-beta.0", "@babel/plugin-transform-react-jsx-development": "^8.0.0-beta.0", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/preset-react_8.0.0-beta.0_1748620312318_0.015121468495379986", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/preset-react", "version": "8.0.0-beta.1", "description": "Babel preset for all React plugins.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-react"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-validator-option": "^8.0.0-beta.1", "@babel/plugin-transform-react-display-name": "^8.0.0-beta.1", "@babel/plugin-transform-react-jsx": "^8.0.0-beta.1", "@babel/plugin-transform-react-jsx-development": "^8.0.0-beta.1", "@babel/plugin-transform-react-pure-annotations": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/preset-react@8.0.0-beta.1", "dist": {"shasum": "c7beacf680df613377ad75a10d5ab7639548f3ba", "integrity": "sha512-wHJMe+cGoN9LbZ6oMmZSDfsC4hE1+tJdPibHVsRh049Ou5w5nGQN4+xOSxI2gLU+/nks7mGitryFeVkjn8sH1g==", "tarball": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 15787, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCEsFWJMTbKN8LBEVcEXjYuXobizZTfnwohBd8qFBKiNwIgDhSoULRzvuEuEqc6aePT3rC7KKgcb88iSVXGdiD/Fr4="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/preset-react_8.0.0-beta.1_1751447093825_0.6752638861659632"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:51.650Z", "modified": "2025-07-02T09:04:54.227Z", "7.0.0-beta.4": "2017-10-30T18:35:51.650Z", "7.0.0-beta.5": "2017-10-30T20:57:30.263Z", "7.0.0-beta.31": "2017-11-03T20:04:13.116Z", "7.0.0-beta.32": "2017-11-12T13:33:57.188Z", "7.0.0-beta.33": "2017-12-01T14:29:14.572Z", "7.0.0-beta.34": "2017-12-02T14:40:09.683Z", "7.0.0-beta.35": "2017-12-14T21:48:22.919Z", "7.0.0-beta.36": "2017-12-25T19:05:30.788Z", "7.0.0-beta.37": "2018-01-08T16:03:38.133Z", "7.0.0-beta.38": "2018-01-17T16:32:35.887Z", "7.0.0-beta.39": "2018-01-30T20:28:41.850Z", "7.0.0-beta.40": "2018-02-12T16:42:28.736Z", "7.0.0-beta.41": "2018-03-14T16:26:39.730Z", "7.0.0-beta.42": "2018-03-15T20:51:54.039Z", "7.0.0-beta.43": "2018-04-02T16:48:48.180Z", "7.0.0-beta.44": "2018-04-02T22:20:28.289Z", "7.0.0-beta.45": "2018-04-23T01:58:06.063Z", "7.0.0-beta.46": "2018-04-23T04:32:23.037Z", "7.0.0-beta.47": "2018-05-15T00:17:22.812Z", "7.0.0-beta.48": "2018-05-24T19:24:19.540Z", "7.0.0-beta.49": "2018-05-25T16:03:54.267Z", "7.0.0-beta.50": "2018-06-12T19:47:50.403Z", "7.0.0-beta.51": "2018-06-12T21:20:31.525Z", "7.0.0-beta.52": "2018-07-06T00:59:41.556Z", "7.0.0-beta.53": "2018-07-11T13:40:40.585Z", "7.0.0-beta.54": "2018-07-16T18:00:22.602Z", "7.0.0-beta.55": "2018-07-28T22:07:47.897Z", "7.0.0-beta.56": "2018-08-04T01:07:55.645Z", "7.0.0-rc.0": "2018-08-09T15:59:34.207Z", "7.0.0-rc.1": "2018-08-09T20:09:25.662Z", "7.0.0-rc.2": "2018-08-21T19:25:35.523Z", "7.0.0-rc.3": "2018-08-24T18:09:12.290Z", "7.0.0-rc.4": "2018-08-27T16:46:00.573Z", "7.0.0": "2018-08-27T21:44:32.209Z", "7.6.3": "2019-10-08T19:49:31.496Z", "7.7.0": "2019-11-05T10:53:42.166Z", "7.7.4": "2019-11-22T23:33:35.845Z", "7.8.0": "2020-01-12T00:17:23.151Z", "7.8.3": "2020-01-13T21:42:14.565Z", "7.9.0": "2020-03-20T15:40:14.185Z", "7.9.1": "2020-03-20T21:51:39.071Z", "7.9.4": "2020-03-24T08:31:25.383Z", "7.10.0": "2020-05-26T21:43:29.471Z", "7.10.1": "2020-05-27T22:08:18.506Z", "7.10.4": "2020-06-30T13:13:09.010Z", "7.12.1": "2020-10-15T22:41:57.790Z", "7.12.5": "2020-11-03T22:34:28.390Z", "7.12.7": "2020-11-20T21:05:50.754Z", "7.12.10": "2020-12-09T22:48:21.723Z", "7.12.13": "2021-02-03T01:11:41.341Z", "7.13.13": "2021-03-26T21:20:26.116Z", "7.14.5": "2021-06-09T23:13:00.994Z", "7.16.0": "2021-10-29T23:47:52.615Z", "7.16.5": "2021-12-13T22:29:02.013Z", "7.16.7": "2021-12-31T00:22:52.626Z", "7.17.12": "2022-05-16T19:33:14.376Z", "7.18.6": "2022-06-27T19:50:29.386Z", "7.21.4-esm": "2023-04-04T14:09:52.619Z", "7.21.4-esm.1": "2023-04-04T14:21:50.203Z", "7.21.4-esm.2": "2023-04-04T14:39:51.438Z", "7.21.4-esm.3": "2023-04-04T14:56:37.371Z", "7.21.4-esm.4": "2023-04-04T15:13:46.215Z", "7.22.0": "2023-05-26T13:45:44.838Z", "7.22.3": "2023-05-27T10:11:12.809Z", "7.22.5": "2023-06-08T18:21:41.233Z", "8.0.0-alpha.0": "2023-07-20T14:00:28.762Z", "8.0.0-alpha.1": "2023-07-24T17:53:01.887Z", "8.0.0-alpha.2": "2023-08-09T15:15:23.107Z", "7.22.15": "2023-09-04T12:25:25.431Z", "8.0.0-alpha.3": "2023-09-26T14:57:36.883Z", "8.0.0-alpha.4": "2023-10-12T02:06:49.706Z", "7.23.3": "2023-11-09T07:04:14.477Z", "8.0.0-alpha.5": "2023-12-11T15:19:44.455Z", "8.0.0-alpha.6": "2024-01-26T16:14:45.003Z", "8.0.0-alpha.7": "2024-02-28T14:05:44.626Z", "7.24.1": "2024-03-19T09:49:30.661Z", "8.0.0-alpha.8": "2024-04-04T13:20:20.003Z", "7.24.6": "2024-05-24T12:25:10.718Z", "8.0.0-alpha.9": "2024-06-03T14:05:54.376Z", "8.0.0-alpha.10": "2024-06-04T11:20:48.592Z", "7.24.7": "2024-06-05T13:16:01.103Z", "8.0.0-alpha.11": "2024-06-07T09:16:12.059Z", "8.0.0-alpha.12": "2024-07-26T17:34:07.215Z", "7.25.7": "2024-10-02T15:15:38.309Z", "7.25.9": "2024-10-22T15:21:53.432Z", "8.0.0-alpha.13": "2024-10-25T13:54:54.028Z", "7.26.3": "2024-12-04T12:35:35.997Z", "8.0.0-alpha.14": "2024-12-06T16:54:42.913Z", "8.0.0-alpha.15": "2025-01-10T17:25:13.202Z", "8.0.0-alpha.16": "2025-02-14T11:59:46.739Z", "8.0.0-alpha.17": "2025-03-11T18:25:41.158Z", "7.27.1": "2025-04-30T15:09:35.292Z", "8.0.0-beta.0": "2025-05-30T15:51:52.504Z", "8.0.0-beta.1": "2025-07-02T09:04:54.007Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-preset-react"}, "description": "Babel preset for all React plugins.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"maddas": true, "severen": true, "cfleschhut": true, "flumpus-dev": true, "maufrontier": true, "jacob-beltran": true}}