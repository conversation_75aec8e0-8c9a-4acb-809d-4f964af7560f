{"_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_rev": "58-8268d6f0340b4204abe66066c080d70d", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dist-tags": {"latest": "0.12.0"}, "versions": {"0.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "author": {"name": "'<PERSON><PERSON><PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "regj<PERSON><PERSON><PERSON>@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regexp.js", "bugs": {"url": "https://github.com/jviereck/regexp.js-parser/issues"}, "dist": {"shasum": "768471dcd881c180489b707a10cee3096339b608", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.0.1.tgz", "integrity": "sha512-jzB3FLECcVtNbEEF4xdq8HoGkqPMJSSG++nxtgIe9+WnVqdILze8dM6vfmqSZlIaYCyACkwkekW+070pfP1eOA==", "signatures": [{"sig": "MEUCIFHuFcrzcAshEZXNr3f7jWIa7mGHvJ0pQxAzbZ4wotsRAiEAnuJcf8GX/Vs9c2WWTFf03WUckaMCmUYWV8Ofj3eA7hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regexp.js-parser.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {}}, "0.0.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.2", "author": {"name": "'<PERSON><PERSON><PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "bfb75676dfbfd825dab4976916205afa1f6516be", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.0.2.tgz", "integrity": "sha512-9SQgz8f9m1Mts56jZOFQ0ri93lDICuhoMitKi5SfRcAtdxLa/kH+L2ggeb6cYMjbS72MB3olxjX45aAFqZyTpA==", "signatures": [{"sig": "MEUCICvdT4i5INczhWqznwp+563xbbYRij6dYo6CagV1BvOjAiEA2Z4M2tZwt1YBJkg/l1FP1zo12qVJy2DujDAPiFiXr3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {"jsesc": "~0.4.3"}}, "0.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "299d2a6b2f8606ed39928991f5684adea6f6a8e1", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.0.tgz", "integrity": "sha512-BiMAyvXoMxWnk4i8VLXuirlCEZXuLBMNbegJwnQBZn8HJXDR01jf283P4DCaIEwIDfZbvVFou8lvFQjBB1bqlA==", "signatures": [{"sig": "MEQCIE2GsdpEm2q6FXr/rlQJLagPr3xuNjKZMB3qAOVDgvh8AiBTIH5MKiFfbcPIREnMe5+GL+3VWBRN2MBR2aeTGcqx2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {"jsesc": "~0.4.3"}}, "0.1.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "regj<PERSON><PERSON><PERSON>@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "c25affc02e5cd9753c4d8cdc591f3d26f7944e9d", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.1.tgz", "integrity": "sha512-0459KKWXopWoKc/VnR/xnFRJ2+/UapJSjT5r4oODCSB+qiWFoS48wNq+7jaBOZdjjhnyKuNv98VNQYbtimYLZA==", "signatures": [{"sig": "MEUCIQDqwpkooNVFBG1ftDdTo87/pP7gwlDdZKLUyca8ixgOuwIgKTLZqGmg+SOqTIJc58srmqoTsj1A9Ecx3RNAfil8uq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~0.6.4", "unicode-7.0.0": "~0.1.5"}}, "0.1.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.2", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "467eacf1780f93e22f853ea3cc84f1d41070300c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.2.tgz", "integrity": "sha512-9nB4aMjlp2vsVo8hYvcPfcdhnn7kNIPIbjx8iGG7JtH4Suu+cVFEKXQqYPvmcULYbF9najXJE7ayGgSMRp7sbA==", "signatures": [{"sig": "MEUCIHf69BbawyIo/Jd7AzmNfXI3rwDRnzYmbFxCe0ZddSQDAiEA/69YFC3N3lQrReqzl7hYlPmt+8DOmDsp5lAJzMI3C2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~0.6.4", "unicode-7.0.0": "~0.1.5"}}, "0.1.3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.3", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "aaa87c04c68060b6154319e404071f15bc892d54", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.3.tgz", "integrity": "sha512-6f8cmBDHCnwycmRd0jQO0PkinhbJWt3jFxOVDyoG6HH3R5nDOFbWBx44ab9CFIZnJXg+r31YqcX5JLhHIjy9Gw==", "signatures": [{"sig": "MEUCIQDvXk8uF1hWwpDozu/hsbYU5KEGctwpYrA/dVXxK2VkEAIgPWjGY5/BBfTG1XjA4sMQGaR6aqJfDF/Pmdhxu2V3C+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "_shasum": "aaa87c04c68060b6154319e404071f15bc892d54", "gitHead": "98c40d218d55b120685c63f41a901a5690cd2227", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}}, "0.1.4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.4", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "958289586a3d9447abd42d3d02776fe02c16e906", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.4.tgz", "integrity": "sha512-6UmAgl8nYs+S0ab5tQ0d7MGLjaf7jC/NEcRFEhszD3LtnGd6LDW447zkvoQeu2TFaNeGO6dpcuuZGE7rlMQqEw==", "signatures": [{"sig": "MEUCIQCXlENMXgpcvMbQJPIVVIqOY6A129/PrKjvEH6EX1KiSgIgCnrw6n//qfz4XPJ3NZ5qUVdLsLlrZ8/diTSrDzf2FPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "_shasum": "958289586a3d9447abd42d3d02776fe02c16e906", "gitHead": "ff8d29e6be3c9a02692a25f110163da1b24d61f5", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "2.4.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}}, "0.1.5": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.5", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "7ee8f84dc6fa792d3fd0ae228d24bd949ead205c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz", "integrity": "sha512-jlQ9gYLfk2p3V5Ag5fYhA7fv7OHzd1KUH0PRP46xc3TgwjwgROIW572AfYg/X9kaNq/LJnu6oJcFRXlIrGoTRw==", "signatures": [{"sig": "MEQCIDrgWSO3XkMMMw9Cflw4XalI1zS7TofnwZvne1G8CkbgAiAIRv7tC/wxPr0aqb15J9ElXrKXkIfXjqBuLGAzLn5lvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "_shasum": "7ee8f84dc6fa792d3fd0ae228d24bd949ead205c", "gitHead": "0540a6a12bfe13659a29b217bf82455ce1ced0eb", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}}, "0.2.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.2.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "0c8fbe85ab4a19513aa47dcab31a2a424074bab0", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.2.0.tgz", "integrity": "sha512-/Ir7mBFTA3foRZy69TZluBLDS8tdzp60iyc2QXglcc2113U4twyjjtyRGF68RSSH6NMJg/AfKeKkugL/zKaSsQ==", "signatures": [{"sig": "MEUCIQDTEdNiYiHZxtX7HCRky1lJqHKLs8ZOCvHGrnU5vYJgMwIgaQPTb4sEGSjbsJKjkU9fRxMJRkR/kf31sCtx1K69qrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "_shasum": "0c8fbe85ab4a19513aa47dcab31a2a424074bab0", "gitHead": "5d1ed00db387edd864fcfca95e131ab0974ae3c3", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser-0.2.0.tgz_1464127431822_0.8742114950437099", "host": "packages-16-east.internal.npmjs.com"}}, "0.2.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.2.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "c3787553faf04e775c302102ef346d995000ec1c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.2.1.tgz", "integrity": "sha512-tKQ9IpTLb426Y9bJ8+le9UG3miRf8vLlg2Ds9tF2wZ/5HaFv5lSRijMvy4PZYmETa260qiyyLVBFlfkf9/ATNw==", "signatures": [{"sig": "MEUCIFlEu8WBQJwgWKcNM87qjwrxU7LwT63tdJ7sqs/oyF8aAiEAx3RHWCfQHKjvtvFg5TtdyellUq7OHKfDCQh4r8nC2tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "_from": ".", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "_shasum": "c3787553faf04e775c302102ef346d995000ec1c", "gitHead": "969b73c6d9f10ae99a2afe9850da24226708b39a", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser-0.2.1.tgz_1487879834326_0.9009130923077464", "host": "packages-18-east.internal.npmjs.com"}}, "0.3.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.3.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "3c326da7fcfd69fa0d332575a41c8c0cdf588c96", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.3.0.tgz", "integrity": "sha512-zza72oZBBHzt64G7DxdqrOo/30bhHkwMUoT0WqfGu98XLd7N+1tsy5MJ96Bk4MD0y74n629RhmrGW6XlnLLwCA==", "signatures": [{"sig": "MEQCIArpggtNuDJbity97EltpBok8h1h/Jys6yaRox6Ja1oXAiAGQcMXggRf8s1TmeGVLP+msKmTclIuRLoLlTvxxYLI1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./parser", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "gitHead": "5aa27394ed3beb2d897e12704ba5f1aa38902f2b", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser-0.3.0.tgz_1515834830113_0.14398667868226767", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.4.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "7e6393c568f3b13accd9823073cbffc4d27f9af6", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.4.0.tgz", "fileCount": 6, "integrity": "sha512-SQSLyvtF3ouBBg8r8DbT2v+gNUF0EBBWWmySq+kk+ShSnD6sD3nahnThQyozy6h+TRtdNZ7D6sjVaBUrdRjeBw==", "signatures": [{"sig": "MEYCIQCb5M0hCKzDRK+TuP+TgZXQasO0RqhypnP14rD44uCcYwIhAKre9ReunV5YRPv1hNhk4cnAbq80ITR243ploZjCQ9yE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhWQpCRA9TVsSAnZWagAA78wQAKGyndRcpwbHsienYK/o\n4ccFQIbFNfTxGuTZgldWJMimZEVgVeu9mA3N26zID0FcqJ6sEDPfbxoHLleB\n5od6CpYQ1dyULVEKghHdwtFK1f3BoJDH+l7QcbXbMplh+x5gIdE6zZYgHCer\nCsRtaUtfPHQQ8mYGLLBFqz2JUaEUipzTTawAFEMI3nmuBR3Jw19Bka551Ump\nrQn0sEilKbOQguZ3s4NwWJ+nLaaAXp9RbZYSihTubVO1LF/NXi1IG1my6ZbR\njEtAXjFgnez8K+JyZELblIKnTJoYSb1lH0MMqYLP6sSc8P2Jffm62/ZiBQ8q\nenrU7xtwNumbtKThWQTu2eg63fEjq+Gus0T2tVxW0++xTy8tNQJyexOUCrgt\ntskPWyyiamnkUSKAFNQ6TNXhat/HiC80EZK7e2OmcXfJMl4FKtj/yLZpkJjZ\nnFxCsFjNSymDcWgNnTT0t4NcnUFyGe2+ypUNtYF4T8Wxmnkz0G/zxJgWOPyx\ngFEtoHO7dhDTmwwXwU1v2pavIl3uxURgpDNpFH4znRpir6Bvy+QE9bjvumlP\n2jBRuFJCoFsi292llauRIbqCyMTF8NlGkyqGeA4m2nbd0udEkBlPo/Sxn37N\nSQwNL/WmgYmQKpkO2qhxPhzy3CAT/GGCWUxJiCwewM1Di8dfUDKsmOdd4ayf\n7wAf\r\n=oY26\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "gitHead": "835867ee6c980ab5ee8f3bcb16ce12116386c16c", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"npm": "^6.4.0", "jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.4.0_1535468585229_0.6655325083312515", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.5.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "d4064939c6a740976b0848dd8a39ac4f21b2270c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.5.0.tgz", "fileCount": 6, "integrity": "sha512-5EnJ42b5DqKQKtlzTEnS+9xMpw/3AkZ85xw7Rje3UImbUxNPle4Ek365g3lQbMMPy6zqucUKyz0vb/66G/Swzg==", "signatures": [{"sig": "MEUCIB3+jWYEXEyYBcGLo+8zbLOvHe7O4aAiie3rP0hW9l4RAiEApEV2UAKmzio7+XZFylSlmMbkDswwynx6EA3C68n6Ulo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+rkICRA9TVsSAnZWagAADd4P/3+rUQrSvtReYCv90fYH\n5/2eiAFeD/qjxAIw4QFESO69UgiWEz/z4Ozdw2Sthy7nh1rOCltIsfQbKIHI\nnzKiw88c4gcCzenVvwoHiIzGM/XtHG5jcadJdckQjdhdu15muPFLRPKhQGjy\nRnTVxrkQKJZDNDy2F9UqjAHx1E8UZIwsjCYgI3IZf3IATwVT54s+Gz9eQtKY\nCUBmxxoGyMG3aFT5sHQaU0JAgLf2WbkzWqyKfs86xAwdQHop3kR6h76tvmJx\nhzJd/PDy9UaG4LueT+cC9jpULho882X3bB5l0XlcnJ+/o4IermSTTBqVgu4u\nbInv2bZKVfvRNiRw/4oGBbiCrTsYTI5T6yAlQ23z/zRJoS2QqHd18aIs/6/L\nfD0B9kSdZoEYHjumAzFRdx5G4RzIPqrxwtiy9+NJAFf3RQur5F458ijf1NM8\nF9y1cCKySIuRK5XPGDCBm26ldk9XJngMAusMwJ4rlVakTj/DIHA7I0l4xJjQ\n/ABruhn+Mpsz0jCzygvlKhtqsdVrW/9K05VyZhM2uO0VzyXLoPanqmEUpaA+\najWWli75JmMGMXVbKwLHT20RdkASQ7JjxQPvFolv5y7JoUUWMFmAVk4zg33g\n3Dqzo9x88VxG4i0dHJCggawaZuMK3AmA0FVHyMLdWgrjopClHmRWM93zZKnH\nv+lg\r\n=SNLB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "5aa27394ed3beb2d897e12704ba5f1aa38902f2b", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.2.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.5.0_1543158023970_0.9356287959941769", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.5.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "7466ffb39f18948893aafbc4ce630d3440f88d1d", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.5.1.tgz", "fileCount": 6, "integrity": "sha512-6Sl+0ypyKAs9QU/GKxIX+JL0QIg7+TvoEWge5tUb54QN40JadF0Xa3XIRvOAOWQQ6/tk8+WB4wPPGUjbYV+AyA==", "signatures": [{"sig": "MEYCIQCVDCj9RXY/QxV6toqTSsCqd/2nuGB4XwZnxg0piz96UgIhAObv2Cfl8+gpH+QhvWzUiPmrj5p2foa/XfZV9Yc1qxcs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43586, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+rrJCRA9TVsSAnZWagAABY0P/AjsimQUeCFISJG/+AVZ\ncdhufRpvvMm+uj/pd3O6W5tL5t7fjyDmY5naroW1g0lMc7fxN45rWtBp2/+f\nx/7Dd6RnRDaf6fYWcfODErfChVTw9/f8CfB8oPmY7IonRGFw+1KN84rIkH6R\nUuz2nalde7yamJjO8kX5TD+ZuzzEGzGPESIuX4Q8uFBNRjX+a4GlvYJhso0i\nKf/IvNvyJvzl3Z53sXGyhj3Olmcfx1SQZfHi+KxueqRYvp4v/EbcfWeIjziX\nQzTLcP41MBacwpv2ufAc7s6dK6xEbyixIX4D2CbiwpV7PsRktY1lfMPZuEeS\nEEiCmK8YZz9DwpOA5pPDGZkqX5Y59Ey46mFWrN9NLaFFD0vwAMVqYL1VAoNo\nc3/6eoi5cYe/5cQCOxfhxbDXpC4+ph/2TeCZjvBNITATB8Yj15yHp3fPaRQp\nlewZOB3P8qzdCoscFq3itOS4BNHuXeSDKDrbFN9ZWn25TV71vpG5AAi4dvyO\nBfg/6rFnS23SjokOAssMIEUm6/v6m+SsfZ8PgnUjhwQzNlr6nspLdX1WRLhM\nEa5B+YfhAFd6p4gnozd/jgv6RRLEDDLGyNyTpDfsAgc4cvE648UQ0rILOzow\n/CXqEp1UvqXz0cUAJD9CH3ENaGBVqJBb2nF23Qr/oCNHlcHFg6luvIKUEPfW\nAVAa\r\n=dG7w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "3f5db08421bc775b8e7553882cb5a6e9c4ca34ad", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.2.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-7.0.0": "~0.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.5.1_1543158473224_0.7827077506583551", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "f1e6ae8b7da2bae96c99399b868cd6c933a2ba9c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.0.tgz", "fileCount": 6, "integrity": "sha512-RQ7YyokLiQBomUJuUG8iGVvkgOLxwyZM8k6d3q5SAXpg4r5TZJZigKFvC6PpD+qQ98bCDC5YelPeA3EucDoNeQ==", "signatures": [{"sig": "MEUCIQCeBpRbyMjS+bjbx4zUDCo+uldtf8DXJiPu26IbtS7J6QIgLbdNIQsN/AwunfCr7iSvckXCo8Gwpw4CdoyoJqSBT2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBue6CRA9TVsSAnZWagAAS+IP/1DkNg3P8X5OwLrB0s57\nKpffAmTy4F1oeElTN39rgELivXaqehu8iRozDE3Tx+TbQRsiYW7H07nbYH9r\nsYC+CM4QX8xxtZgDWEpbvUSmt3diWQ2n4qm8D058CQLyRG71RVt7RJRxtmJu\niQcsf96hrixE+hZhcLypb3C0jDHUaEYh5htH3r1D/c3/YFaTNQYob1DPkUYk\nDno0jAxNNLeloVLeQjNzpDC/ztIklypdI8bTHYkXRIiqGg7JcaVI6zlK4AyY\nqfdnfyKDFGSErSCTnF+UiA7zCMCVHVauSutXq3KDufytOvWu4EVnWpQHN54k\ntg4MBOpqxxaq523FQKigur5pN5LerrSy3UvzJjZT/uQ9awRprxtqqkyuOz7t\nqbGiTC0FYtZkGRCPt8qH0+Qdpdsb6ekU0zeTe9bmEg5CKVCymdI/bmHzBst0\npY8TC3XYs/fd8zmtUfBHo23bYNrTSeQBaXYhrNtB81r+VAmp7VKEjgQWBU7C\nlQEM+EZrrCxsSeyiVpLLdWnFVnlVk7/SUpJ+JxV1DhoC/S4W7Y7HzzgpZZzh\nnkc33t6Cv039FQFIOsDJ863askyjtIvCxVGSu1hQDJ6TxHSpk8q9BTaVOtTr\nRw4aspdbLKJkP4gf8ZmKfLcBy8qqcVf/JND/pqF822OAQr48oV9M62TqxdgM\nNT75\r\n=JWND\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "0f12db12eb2ecb67143979ddba275c4aa87c40b2", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.2.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.0_1543956409387_0.8074269988998644", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "5b6b28c418f312ef42898dc6865ae2d4b9f0f7a2", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.1.tgz", "fileCount": 6, "integrity": "sha512-7LutE94sz/NKSYegK+/4E77+8DipxF+Qn2Tmu362AcmsF2NYq/wx3+ObvU90TKEhjf7hQoFXo23ajjrXP7eUgg==", "signatures": [{"sig": "MEQCIDefBZ289gF695TCSg5dXLBI1cBCBB2gjSB2vd9Js/7NAiAMFnLQIAl3LBmCPUtf/ldNRAh5taeFpppsb19RprU3tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8ye5CRA9TVsSAnZWagAAT5YP/i76EmcJkTbwCxj3dEo5\nNDLDMtt0/+0o4yEiHrD2FKKl4goz+P2HW6yLLSkLFkrRQY6wfDJ2aCIFWQP8\no8hxfkHUTaGPwugnalZ/ywnu9yHixaFuMWsgE7/+xIgc/wDA6zB5b2Xvbx+N\nW4BQV/j2xExkseJS2NXf+b5NzFDkuSrc6RWkX2rJ26pTjS/nQs4HdBqGowfk\nPyxKS/jJLCE3J8FbXOFLc3yfpAh/nUzfmq/35tLggdSEzFrgZ8U/OOYLcNdI\nI8GB69vgj30whDG1q35sXVnVXWGTNplc3e0GIkpqNaJoPxsSTVFxbLfwc6qg\nc0h2zi9nez43V/07znhUm605ffIQLVgOgd0mqHiJ0X28jj1DH80ludcHIe4n\ndvbRN8N2RGosPxRd8jEiDrE/CxakklRgYVUbOvs8LDTlRGrKKOfEBMu/HhXs\nmphWHWdZ+tONTbxREGLueDhFCYjaixftaxLdZr3eKliFd/XbM+YQ8D2y/zAv\nnq3SaKgXpIyy7SbIW9fcvNhNNfKBmj0TGkYvLJ2HxOwcQeQsu7P6kbtHp98M\ngDhVXHK70TGsXWb3o5Fv1QKGZ+RRZT6Q/ojILh3bwMbuDUmdOj/hgkAjUQEc\ns4392aoI4UqwoIcmZY6dcDVm8L8wLeVgzfRMELu/cFCyLGCFWY7flCBG7bMF\nPJyP\r\n=Idwb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "9bc130f3b8ca132d0f4223da948831841432a706", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.1_1576216505132_0.9680595638007778", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.2", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "fd62c753991467d9d1ffe0a9f67f27a529024b96", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.2.tgz", "fileCount": 6, "integrity": "sha512-E9ghzUtoLwDekPT0DYCp+c4h+bvuUpe6rRHCTYn6eGoqj1LgKXxT6I0Il4WbjhQkOghzi/V+y03bPKvbllL93Q==", "signatures": [{"sig": "MEYCIQDQ6Q8fe0uTKvVqGmvXEKhUTjZM9YuNwyKxRZp8pBbZJQIhANueU/hsFmHTxfn5IWQ4PSGO1+oeVfEzNWCiV6xQo4rz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/bVfCRA9TVsSAnZWagAAiuMP/27Zpg5ACQDqX98Isyw/\n7w9do3lhzda7IIUsPs+7gg/ACcYNJ8IHYjx1fmx3falrj+s0hCJDVZ9lyKR0\njdGDcBivXHAtU+MFiS+7wUwjYSds5o6S+bUQmvTMMmIh7fiqAFdw9eZ838WE\nHs19oQE+P8yBcdiGlZdkGS/a2ZfnsNTjRDIB6nwdiCc9LsG8gdZLjiTQQ4Dz\n98cwL8rOEZb5WnO/eej8viPVhswoeFR6lOC45Udy9sEMbhecvjpW7n9NzxGQ\n0dd3RbcVrOCTyaFOa21uCHfz6iAXJaVZI2UG74VxM7602qbsrFHWETvSvxNJ\nQg5ncOHcY0mxEZKg2NXQGcBErVxJZvzOEPYRzovfnMk8UwB3HatLr7XOyUdj\nWCHt0256oz9IbwXb9PunaFuNwmVEUiUpKBOnv6ol31F9154VdHSRoK0Nw1OU\n1BL4A7omKOn9R9Rier4DP5qZDWeYtvVUJEfQnjj325Bbhq9rbQHiWL8aqwmw\n/pUP98Ilqdd3qO9f4Wvp6DE5dy8e2lkBSl0LxqVrFGRxunuMGURtyYB51v3l\nrwaRYPwqs5f0fk4tLlpxhIf/ANb+9vcXZCGn8aRbUBEJCJQOAASvGklHZEDF\nmJPOqVCfbEDiUuTlSuhPqX+ZzvNUqEFPN94tPIwWagmDrxgbj7zdPdV9/7FZ\nsTXn\r\n=OGqu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "a6a47cc58c3d3f4dd8eeefc5a062afe1108da3a8", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.2_1576908127501_0.6982810857029862", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.3", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "74192c5805d35e9f5ebe3c1fb5b40d40a8a38460", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.3.tgz", "fileCount": 6, "integrity": "sha512-8uZvYbnfAtEm9Ab8NTb3hdLwL4g/LQzEYP7Xs27T96abJCCE2d6r3cPZPQEsLKy0vRSGVNG+/zVGtLr86HQduA==", "signatures": [{"sig": "MEUCIQDZsiHGXbliAoF2n5YvFXoBTndzccoo4isU597K/lxjbgIgZ/w/dYcLuXNKI9pPx+lC+aTRueg3Wo9ltj9GesultR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRemsCRA9TVsSAnZWagAAik8P/0m+Xn85RKiiGVa+YR+N\nUqQLWUQMseI6758Gm9sCmlFlJiWw4AA2YDBLTRh/bK7bpsDPYisHs0JCf8j+\n23Zx6Ebt4h2UepF4Kw0no/42+DdY8DKmzpqq7UMWNGjfkticN1mHqNitxBjY\nVtbHbYpyHgUunrLzk17xmZkguqQxIsvGvqjh/ezhUiYzWBLwyot8X/FXhWy3\nXUORuPAngXs/8D0I6Zv1MK3dvq0qOqv2gZUa4mymFcmZQeU45tkhxz9dwCEU\nAw2oWtLl2jlBShBrM5z/qZb5ND5MDOdO88DwNx8xohVqFryHUGjKoUTTo3BZ\ngRO2G0nFfm57RyhdfirtzAF/r8Jx3xhxE6hDOaQFc3cBowVgsUg2mOGe/2fx\neHlIDrml5teeFPg5ADD80+L7lyo/TrTZrRLyGZsuq37RPImdJ8hZf59r+Erc\nEyOVh9JJsev06S7GF3soYu8scevqspVGYSyJ9BM89P/EYBlkS7n+ggzF2dYN\n7U3+OhQ70PJrzLsA067VS1/rCCjG6jkDH4t9pa2mW5yDYezlOrUYBp2lwG5/\nnyXf3jU8HIzqhv79mhN6LXQvR7MiI4ukMOTu1WkExIucgZuJLZTkdQKw+6a7\nxWzhgijZm2ClKdbu0R74lLY1gwi3rMgyB3vVItF4pEFK01DRBkJ4hNIoT4IH\nK/i7\r\n=tjyW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "f989518142ec3ec1ced578f456637cba9fbc4ee9", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.3_1581640107767_0.3683851492268364", "host": "s3://npm-registry-packages"}}, "0.6.4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.4", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "a769f8684308401a66e9b529d2436ff4d0666272", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.4.tgz", "fileCount": 6, "integrity": "sha512-64O87/dPDgfk8/RQqC4gkZoGyyWFIEUTTh80CU6CWuK5vkCGyekIx+oKcEIYtP/RAxSQltCZHCNu/mdd7fqlJw==", "signatures": [{"sig": "MEQCIHC8BGDojmEQYCxsiDh9w11JfSOjoSUMGB7/PRe7AqqHAiAn6mUP2IWjJadkBUG0tVIXt5ARc0IO7UTfTr/CcJ3pmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaFiACRA9TVsSAnZWagAAYkwQAJ9hRwI3gTtbblrS4PoA\nOQrkHjVtdutWde4Zln1iYRPnvoiU/CZqFm6JIVhy1DhPKPCaRiLrCgCjETef\nEilsbIpXb3BJHAu4P3lWNbyWu8OU5IDLGDqtUQGYG15cmG6+rDG7HqdqFc2y\nIqO8vhBWt6yNKbsYZqj4/5GypniXNHwm3gPdrL3C219b5w1H/BceLDBSxV+f\n3hA0R47GsUMxENUsYfnLgli+5cP7IyBhj/34upXSYxn9II3Wqnl4v6Ad6VBE\n0F62QHpTJhDPWF3kzMuB47GTpHumuBi1X89/UGN8X8om8SypR9VZ60Ujl1A+\n3tIBAnT+oXXni6Aj4JqzXGmIU7yHXz0AkLzELoBRlzUgNualkE+RdqKAsvv/\nmgVjvJMuPxOHcg3/J7lEnvC1Y6KiqByVyaxng5MeLCAZBGx8fkuSw95G865f\nNRgRpyFkFAc5dz12USR/3+yxs6YqA7RuP+liTorGkbnvo0cF6E1vynDHPns1\neoqO06cWI0KHOpEieA/otic/254HpuJnqJm3P/6qpuInPqNWQW450cwoe+cJ\nkut61BK8ORO460E/of7sHpHFaysmY6HMONYmohz2QA9ffAERbUErs0j/7gZV\nQloLGnW3OJbgA3WB+awt2qb5lfWTIRsCWg3bhG/F6ZiFEHyWowEuPAlbh14Y\nVBNe\r\n=AqKS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "25b7c79b36ee76aa774cd498953291d21c2a6a9e", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.4_1583896703730_0.06428944225634003", "host": "s3://npm-registry-packages"}}, "0.6.5": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.5", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "325c5cf2d4a6afd0b5d509866518d1b8f7678b13", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.5.tgz", "fileCount": 6, "integrity": "sha512-AvWj/ihGRmxs7vRG2/PLRMJrc9vrmPLWr2i5IMixQHvKo6VgAGK0ZcIm/La876FNyAo5tF8s9V03cp+ZbFidqA==", "signatures": [{"sig": "MEUCIHuZY69EWhZszrC77OzHzvTimOlXmxEBJtB1ITv0HlBfAiEA0L2G4DrB+/OSwVf8z7aNGeUBKNc4LgeYn6vCVrSzQjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+zlWCRA9TVsSAnZWagAAAQQQAJhoeohLn0U7Nvancyf+\nGADTRDDgQxMQfPInYLurC/0J+ibjoO5RuG8eF21LNAYA0GbFOQeZe2QS6zDK\nBlJTeAg3xfh7KkzHAaThp4oIh8M9WeCMkVd6WHyN9m9O1BEDfDpjZeQQIbef\nc3yQnM0V5Qu7bgkY9Kh7FYtEmAaNy3BclaVK1TP9Pv1BMhaamAhyabGey0oN\nw38B0fF2MnLtinISh6Js3uJjSl7aqZGzVyKHpM+7AfWFDCxJvxgvOWroI3HH\nUqwQpjPynUmmPEihm6PPVJ2dhzCNFuarNQ+KVnDqrkr0RTfxltqZhoeLi0nu\noFIVA/7QaD3IVPtumOYv748wSCYZeXcr2sPyyuHAfA51SPEj2jVxs+Zs8DWm\n+N0iRSeyA8YKoLetAoeeQXgISZPFeok4JFAqmcfMbWInouvx3djKIVFAaPap\n+ylYctPz+lDQKAJQ3F+aG17NPbGK34up0vseA/Ixpxv/8XsAkixIhyzfrUjw\nJVn0deiLCnOCl5Am4l7jIrNqxwm8g/R6fjER/3kIZq8t1vcTbyslILCl/vn7\naGNYgQfQbKbs/yIkegrU/zSFjkFLVleoDSJQL/IK6kdmG2EoYD0CI/rtU0DR\nR0iYNdLdD4ku0aLycoj2Q4rla9uW5f+PHF9qB2DFJmSW6wXGSjGkpbCirK97\nheGf\r\n=LSO3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "7a0b9358457c033cb163ba16812a1cde55a744da", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.5_1610299734298_0.7820491571668438", "host": "s3://npm-registry-packages"}}, "0.6.6": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.6", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.6.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "6d8c939d1a654f78859b08ddcc4aa777f3fa800a", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.6.tgz", "fileCount": 6, "integrity": "sha512-jjyuCp+IEMIm3N1H1LLTJW1EISEJV9+5oHdEyrt43Pg9cDSb6rrLZei2cVWpl0xTjmmlpec/lEQGYgM7xfpGCQ==", "signatures": [{"sig": "MEUCIEVgX/R6A2ZUTCcXdiYW15IVwvaB9YlU2bg58Jv2d9nkAiEA5xP5CZOr3JDB31qZjBl8IX6uiaGtvw0Uy9+yBK105wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+zwCCRA9TVsSAnZWagAA/qQQAJMqBMd08ywakHhYVuML\nTTGuPFNddcKOWu5C6gnpU8Gf7YoFW1+rgyzhbJcD2Akc+Hw4zVDrtebZZ2V9\nxD5AGFiOL2FnrsxBNP2XSQ1IWKB3A9SN28NCOzqMZOXEylngXYMOrkmXVAdG\nN+fcnLSQiTAbUJJ+qMQo9uBvFddL0v1PkF8mSwbYdEsRP14VeJct5mBkHqpg\nR54KBeSrrDtA3SEk+LkTQiu3EYyP/b8pl59OiLnb7fCOILQJGLI7SZ/lZWV2\nGtgkyeVMlVkpaW7h52HR+hwQEljUOXeq/puM8VT0IlJhzVgq6A7K4Ue+a2Ln\nH+HIE30wWHib2+dN2Dtp4H/zvF+pSnpW4bA+JLJZDFX87oo1jz4IjQSjlNpv\n5Ys/Gu8Y0Gi2rWGdurdzTjwMFDyY9iNOFT91Iv8Q6BLOXWkuSOV5Yyu7cBIg\nfVyfQYracp2G5SSNScoRF6F/HmVvNewHEH9ONGYeKjCqzkNtRB6qUlrjXwRQ\n/l9i0mpn33+1hdisMW6a922vuvtxiUOkT8LhzcWyb/bvxr0zRmYdnd4qxK27\n+V6YRe0p23r/dtlJPJ1PuIFi3UVabedJXwOgAyPaP9Yde2kp1b1aXjZ6AI8/\neWwRhVkup7IvU0BbCOWeHi0tBxSf+JSkzTv51IxI4Vsttm8WjUZ9H8ZBaSV3\niZzl\r\n=UOh6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "321fd60a9126e9df93bf3451502424860f7f850a", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.6_1610300417948_0.3545196121264953", "host": "s3://npm-registry-packages"}}, "0.6.7": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.7", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "c00164e1e6713c2e3ee641f1701c4b7aa0a7f86c", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.7.tgz", "fileCount": 6, "integrity": "sha512-ib77G0uxsA2ovgiYbCVGx4Pv3PSttAx2vIwidqQzbL2U5S4Q+j00HdSAneSBuyVcMvEnTXMjiGgB+DlXozVhpQ==", "signatures": [{"sig": "MEYCIQCr979w2Fgw4a20r6qpCzJ41+lw9xEGY2P02Isc8SGjmAIhAIcOMXveES301j2LiTS1nIasWx0HwirslO6kfR15xHpL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFYXTCRA9TVsSAnZWagAAZ3YP+gIAKc81etya1aBJ+wxW\n3k3xreEnGgXdwpcnxTKUjSI9GQPs7uSXCCt35m3f4pWkCDm1v1c3s8HAKFxc\nHN3RfkXKhz0NVE9Gzx6HWH/H6tqnYeq/cvyYGiXWqG2raLTlN7HatYmWDg2e\nly3hhVO0J0PmUtBRZkUimkB/4Tn2FvoW79Zt4h8/Tw2wt53bjr2rX2efToly\nYn/nOfxudz6B/HrxuMwYZXWOKsYdjjmgYlLcb0SSG17HJTN9WKnyNT0jJr7F\njC2KBigYiTze6XFY+zDmAIQKS1sfaiY5F5l3SZQBuVlwRNUFgCuGv0XBab15\nbgt8fHFq8Sshe+V9y94VOq2/IjzHX9XoMeMfqYtod/H/pLqLCGhw9Sqw70Sf\nMHR5k2v8kvk+i2f8OOXqU2hdi4TZMAg1tz0WYOAOF6hfBX+E0wB2vrFJUsx+\n/K+u48nh5kCwJKRCwixjvvej/90E79ADljlZ02+XdrvCAdyMeDwO1oxEuxDZ\nd2hwsCHkDaNYp+fp76tCbEf9MkgNAqeKIsGShg9qpMa56j0ID9Mf6GXvRW+U\n6xfTP+BStScCv8ua5mys9nvWay7syL3EpliG6l/vFj7FaUCU7iKpGAuw3qOq\nrhclOpHNoq1afvUTw+MNdvdBLNZRZKIbGjuKddAyn5YbffZfkiShDm5EHWqp\nDXB6\r\n=JEUN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "971e752c791afbac8bd78fff809eedca1d455504", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.7_1612023251011_0.6162666568411193", "host": "s3://npm-registry-packages"}}, "0.6.8": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.8", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.6.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "4532c3da36d75d56e3f394ce2ea6842bde7496bd", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.8.tgz", "fileCount": 6, "integrity": "sha512-3weFrFQREJhJ2PW+iCGaG6TenyzNSZgsBKZ/oEf6Trme31COSeIWhHw9O6FPkuXktfx+b6Hf/5e6dKPHaROq2g==", "signatures": [{"sig": "MEUCIQC1T1bn/Y04gaqNWN3lk04gnpvNGNwWBili6Lu908RMggIgHoBJ9p64rxgBtT8IDCtwr5o/pVpo74vYPocSztURb6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWUEjCRA9TVsSAnZWagAAzkoP/RrXI47GJsD3/LuRP5Y5\nIsCNQ0iCxiN43U3hmwfmvmld9Amso4PVhnM2EkXDw87jbm2wHbmV0VBpFyKY\nBnbO55D3HdqKOOn2p0EztWY4b92UtOSOYnYGnLWzHtuGzyFGhITfTuSuJwdG\n6DZiyi+H0tndssZ5+CMzsjJbgmMpNMguJjvOQgr2xaeBP+vDui51N1joralw\nvYDWOk4/v3LfDescTyW/hazDJoog2KSgOSUtLVBcESJLtqOJVCKwIMOOr7KU\nuCY6UtDJINzNICJ2FsFsibYo3mFtOhArFdH7+2R49dWQLjgdPaT9oX+jzNPq\nzZts18UDbxfVJ8OSBwIUeMP/xYaIYhV8QI2tGKCkJPzuijPm3/39ioLJG5bD\njk0WVuu89gk8kP3LJfqTdYXxVCtyidsi7nZzzY7YlrKpV3nrMCb83f0gKJMW\nXvKxFnhSQUnljXkLrJWpsCwwvaEa1PEdAIdJ9JupPeuMdk+EW1owTpBJIOUm\ndE9xAgdEnJDmsZ7XvjyezskxDg1zTzBPapR0QnFpFDZB1yxIgAkxB3yDej1w\ndcOU1dtbxkYLgi93l4pHgPTW0yys1XJ4KOxecYv+rngdALKq64eE0bEcfk6S\neRqq/FIoHl7vz89EJU0EZmpasPZ3Q+sGCMlqNSZWsaZMbqTg9NStBZgIGk/X\npT+y\r\n=eooz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "1c8a984c38ffc5c581ecfec645c6ded4880bdccf", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.8_1616462114528_0.3394142683556709", "host": "s3://npm-registry-packages"}}, "0.6.9": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.9", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.6.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "b489eef7c9a2ce43727627011429cf833a7183e6", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.9.tgz", "fileCount": 6, "integrity": "sha512-ZqbNRz1SNjLAiYuwY0zoXW8Ne675IX5q+YHioAGbCw4X96Mjl2+dcX9B2ciaeyYjViDAfvIjFpQjJgLttTEERQ==", "signatures": [{"sig": "MEYCIQCh2cr0BgSQwLf1KBhS3FsTsLPD1l97HS3F6JOblY6MiwIhAJiv7GyOBlq7c914GCPjBxwwJvNHV4Eub2R2K/w/L3s8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWoQvCRA9TVsSAnZWagAAs+8P/3UUqje49eTFYoowJuuk\nzL9gCAeQtc0cSI5WvEa9EFheokLyF0u+qhMf1yQftfsugUg43Yug5P5WP0K5\npRsV0dUuP6yv2w7eD2BOIfcUK7WlAJKTUcv7mgOvsE5q4/iwUa+wgKCAlNKM\n9+YBmbJEYpTN4fGjrbUptaehIhgS8PmGwpm/RYo4YWaPjyaxZz3MKge4r6s9\n0UJlkUMYJT2c2miWY6GPP+f9T/JLysLMZ2HT0FDPT2TFWCQOCykXY0W5XTDq\nFa304bJC4B48rkana3tl//nxLtmp34W/SYuwU/wYWZvveWw4V0P8I526PykD\nmayaeGm/1JHjDmAs9Htq1KwqD5o14VRhudVZ2FWwonta8GbdYMoeh58/Vm/4\nZyGneumqgQvS9ZqhlDbAGX3t8A3NdVYSyIGLzuL2VF38z8y5SRCs5MndX7Jz\nfIa3OIznBirv1lTT4n+2Kos0C2YJwrPczdOqV3xtMaaqLwKvjmR7UijFlve6\n6FRawdovzKrG6kY3BdHxxdzQtPP9GugrvRWb1CcVR7evHgaWl6yseyIPEsJG\nmWTwl+duo+MDG6dUhrFQDzKSsQrWiUUhEL2BK7MQfmlrr+H27efUdXGEV82Q\nYyOyHSvhRrnJL9IJwqaFni/7kWpyL431WfGo1Oegtn5I2QiKNnnfVda6o5tm\njaMX\r\n=JzDs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "0b0538382414175a5570ff98c7057484e3629181", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.6.9_1616544814251_0.8933384707358021", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.7.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "a6b667b54c885e18b52554cb4960ef71187e9968", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-A4pcaORqmNMDVwUjWoTzuhwMGpP+NykpfqAsEgI1FSH/EzC7lrN5TMd+kN8YCovX+jMpu8eaqXgXPCa0g8FQNQ==", "signatures": [{"sig": "MEYCIQCLY8OBwt8cMwPlBppsKE1RQRXMPN6AOXVeWQAepPHuNAIhAPGj/iD16vtspohhBd3gADJcC98h+EOVZX4IHE8FdiBe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNkXKCRA9TVsSAnZWagAAey0QAKPSAdPqzl1o7En5BkcI\nTMsw+HyTypWb/9AfzHGzMTW1J5kxRA0B0Cn69/gCoc3M88cSKYeCrbbBHaUs\nxOVqI5R45rO1V/SgcISBoXq3WXvrKJNvhDljeyB/doKv0sEkDMTDx7KAHCnX\nyO1Bse1uEjIyFjK0rkZ5YsTiLEOF2/sePSqe+6uo9BLCUTCu2W7TP4KIzvJ1\nXknQUpfT2IQ1eLHSQVAERaSrdYHnQQ2LndFa2fBtlcfwdeEkgrP0wrirn2h5\n+3ZhptUf+LEYnwUKaHZaCZFLZRpEG9q5bXRB+8sazWzWzK38LaeAdgeiIRna\nWe5mh1EByOe5Cve8S2V+nWdhdMiJbHUYsSrAEEmW1LXG31lWbtnrloEq2qCf\n+xBREj7zSL3jQOZLkad1czEegKnlSX9Rj0Dx5QbbpesuyhDUy5C/91QhlwRW\npHyD22mf3uWFve40Cv2jYsClTkCacIW11txTBNlMY4HpuXJOipzNGJFbkS6j\n08JXNSlev6AEDRFWmeX50p6KC8SLt9dHvERegGNQqUI2/18287/pJAuViPPX\ncRt8MKzm3kD8gV373lzq3MdXGMWGn6XayTySehsE+Kci4nLBn0Or6mztM7+p\nhBwGpavMsymwIQN8VQTEG9/JmCMY8AIqP89FEfqk1hel26+KgBpn26CgYHcx\nhhwi\r\n=ab0w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "gitHead": "3f77f40e1fbc308e9c0f66a08f7c64609b6990ba", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.7.0_1630946762038_0.40795974660806844", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.8.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "7aad00fecb177dd5da40f7eed655485c6b2fcfef", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-Ib+1IAt56zjIVqDjyq8ACIU1mbe2V4PncFz8i9En/3062JJaFPkZPC7owK7v6/F6951xsMX1hYCh77wmVylEdQ==", "signatures": [{"sig": "MEYCIQC3DN+8ybTX/YDolF0JF4W5AFOeQ6ZKBuRu5UGU+3XqMQIhAMRFa1LqWDuZ7Gqt5Zhvs7uwC5SYoLuFkWbV43JYGeGF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsMN7CRA9TVsSAnZWagAAyn0QAJXWyq/xPIhoJB2WOXAN\npO/a2MyjPb3jXp3JkantbBUwxOFem2R8x4MpOXTfewKe+gUoDKv81CQfM493\nMh+a9fb1Hgver2+afN0xlELYS6ePbhDBeFbok/5n0BDuwgdUveFSyfD6saWX\nQ9iH95zoxkmQXCcQ+Cu6qkHlW0n5Uc+/MDXFsOV3U6ecPtZB7FoV9Np5GcFd\nu/ZtB4qxvaKabui/yBNO1BW6oOMUQlmyErZf6PVHaUp0amloVs6lsR19hLKd\nGth4T5Ytq1nuBLhYIlH0hADWuiyc1TQznxh+X/ktmZu9nTizAR6Bt3V1aKoQ\nSodUIHIScaZ2NC4VMDiY2CmBPDCsvlRXSC/xAUhAlwT2Ca/MFa7TcGcD2pqr\nBUJo7AWs1xwXpkumZFWCg5BlfWeIKcv9Vqx29VH6gHjYwsQ2cESDdaxugATI\nFRTJRDhqVXorhWu0YzVji8QdlO1HLpHt2Suzo5Baa//1xHHf1NHUXNYDwYhn\nxw3cWyabiM37viueCfi0HjeeIJEF7lu8DOopRbnTPBi01NPfp4KPySFz6Ze+\nasmV7vP/iN944fZZYw1oqcDlZjzSUWz8T0fI7a4Uyv1WXB31dk7DSSUF1xtO\nsgWb/YcpQKktmwmvV5LfUKGzCRC4GYbGlv9Sm68k4kcRoIHGwdv3aclUHPtj\nyndk\r\n=g/6w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "673836f6710105dcffb149a495f40898cdb3a36e", "scripts": {"test": "run-p test:*", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.8.0_1638974331481_0.6540603411725858", "host": "s3://npm-registry-packages"}}, "0.8.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.8.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "481f0b99dec8fa970095e99f5baac68f4d7b5552", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.1.tgz", "fileCount": 6, "integrity": "sha512-5+Qk+a0D5G3uh+nftQjBPuuNXefC3bKZTRz6W4ZOwlv7Bw7WTYU8IzxfMCDyKgAVY+4TtJddtjpPZiLet6NEJw==", "signatures": [{"sig": "MEUCIDatBZPVOD/PMDz9dRUB0iNmkoMLOxnlbccFfY6A1HUpAiEAjFEJfc+6hLpqMgIn2SAaUJkjlHnACoH8EX1iebFAqbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhso1RCRA9TVsSAnZWagAAYiUP/24Ua3+7ItA8kktyFEhw\nmo8/8bLjidbWYsALibfAmFqYNIHh4JdVFyYvBQOVwWWa8Ld1aMbJS1qzKgJK\nhMGlvDrYsGR3sG1zG+Un82FHT97gQI4wAGfx9plhqascXiFRq6NjapCjyqpD\nVdwIgmJGEsG+fjL2huw1cOpmOe7iUEl2v3jQkClev64SPzkRj8+1GN5C4mWq\n8G8/dFSCz8pcDipJEPjgmlQcv1gO9KPviLdioyH9GQR0MQDyEA7J8FJ2kzI3\nZQjgtceV5MZOE+lvY+04CduHqeWucXMxZlgpVlog6a8nEjWgm4AZ2oRvUNFp\nPlP58cjNTRegOltmY9YSp6l4m3h2EOc99O0wbkyRg7rDWyyco+Hs1h4TJcOt\nAMFTVfqXJVI9jdyhS8vNnjw8paOs2wzNkMeNnyjyi/HSp3xpHmg4B0GLHVqe\n9dxaxa4pgkLopgqLdBsoNECbzqcZq1f3YDCEn0ZKs/QLNbX7cVYY0hUeFxza\n4kCl5Px0E7cgif61IyJi3RWfudcstIw7jQDPL6u8eLAkeVLXRlz5pdDkCtdB\nBYOu0Tt5iHIvgHKlPP3EBppXcXEdX8sdc9rPNkcz2MAOzNkjK+kpqvp3lJ7y\niOlXgNYdlI1+AfyTAhq2baqE06edfXPTOjLy10xvZxwtSwhOGHItgb55z/hq\nNQYc\r\n=psWR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "450cb3f5371556d8d49e6813aa95fe96db86f279", "scripts": {"test": "run-p test:*", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.2.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.8.1_1639091537426_0.28988251820687094", "host": "s3://npm-registry-packages"}}, "0.8.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.8.2", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "0eca797548189b06d62bc8685802686d87edeff9", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.2.tgz", "fileCount": 6, "integrity": "sha512-/7yhmlBODOI+sdv3ThmD0b0wkFypkj4Kd+8GIwKK783NnY/1WCW0iAD/c1Z1LYGstXwozejtzHzDqMv6LmJkxg==", "signatures": [{"sig": "MEQCIC4O4ipk7mpe2yzysr78Fvz7O34BAz761Pkz08dWZMKJAiB1hW7wfSRc0VkwQxY8eUsZlzDyzHGitDbVKl3N1zKtXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuOGlCRA9TVsSAnZWagAAwKIP/0eUIXcIGL/mmcZcIt4o\nKyMnplaW0hnf5qX5PDuUEQDLZhNQ8UizSG/erkxe2ejcLlgJiDLeeSoDo+sB\n8FAbMmm1YDV57WGCCXtAIIojYSFn5peOKSt7kXuF/DK0+usI1S20pFT8I1oo\nUzMqB34YvLdAqyNbI2lFrWd8SO5LSAwUpCG4E7Sb6X2xQpzz2Mw03dPmWQy7\nj/bwqPbXKPpYvGLYEA0LMCvEAmLxuyvmT9rP8p5gDnsIYhCS5b8ojl7J90dG\n2QvE/0aAFv+BKY1dYAT+2PZOmrRwt9JKyQ7C7tnf12zUdDwRC7lfWtXVtXuG\n97k2sVD0I1ibuB/9n/AN9T6cAPrjMKnwtXqEmBDqtxjIbKez6w0b+5VMxxjo\n1FgxR6wAC3CGtrP/Ytv4w1LAaPOqCOepT1hrRybKQQWVExOknRWj56DVkP33\n3MASgG+U+gWPfLhts83sAydQlLWOxNn7Jt5wRoxcvLSalP4IG0fTEE2eN41T\n8sl5EG1gQtIXgPG+0cmAGGycl1acelbpVGD9UOyO2siEsPkAgOauPO2HimQ1\nvZKb+Pz9oK8gpZBMDtMX4QIVQ5jW7SFvtoC3VgeaAzH6h814hxAOY2cuFG9T\neIKhB+OV+mifMNNhKAjhSMfxDXGwH97UN/Q+UNilg75aA5fJYzP3lAVxLBW1\nzw4i\r\n=3AkI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "9ce48a994aef98a3db63daac1c10d4a23df6c8c5", "scripts": {"test": "run-p test:*", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.2.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.8.2_1639506341593_0.11462002957551798", "host": "s3://npm-registry-packages"}}, "0.8.3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.8.3", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.8.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "a7046cb5a80c0878a43ed06bebb75f160af0985a", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.3.tgz", "fileCount": 6, "integrity": "sha512-LvWBwKwtGX3miw+1Pz/LyLdPaFD+FahLeCkfomRNqrplOc5rK/kBpsOZHYR067XBmpxMiDDf37esllA3ehqoYw==", "signatures": [{"sig": "MEQCIBnV/BrhSkNK8Y8QvaHji/6e9+oEnmM99eky7AdcxbieAiAZwFc4NAmw6vznkVhGw3TYoU4ygvTIGNRtwSMVmyhWNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxDkwCRA9TVsSAnZWagAAkSYP/1LayviMMN2xAvgFvF0v\nP95cKLFf39CQQo+bth9zwujBPIIkpVmlQPznnrmvjyTSAeflI6enBYyVowZz\nZ4h3OYnE4X78e35AihPngtNK2GnRH3hurP5loh/ijg5PbgjGNaW3wr8V8JQX\n8ECYF8qu4sLf8C6lg2XpZGdgWGoJIMI7R/YboMzwKeGWCiqeFRpPUOqwP/0K\nijY8kgZzCtx8J1MWRAj9Jy24SPCyA9+DcRcG7IkDaoOnNwll1Z1bcfpKzyph\njJPT+kNn4TWHAQj6NYZPGzVk/PKmtMW6ko1BI1W8ZLY59OmDrL+/16sTyYft\nvK6JL+jhNUWQ46ucMPgIMT2F9AazDAkm+9OMIzafnISiOK/d0lCBh7mWaICI\nix1g619jc8ltq3Ixu8uuJ1pGyOCZV3iZnICa0EmOPYT3npoGet94du9tfJV8\npz1jiL8negGkLhS7c87kh1jPdrAFSgK3LsRicXyoRBXG+U7Cgbs3c+6HyVAO\n1itDq4A4Ba+fIw9WH5OAULQYLjP9o0UODjsL2nU3aD034ewO5+hjaTONIoYu\nLzeRACZjQTKCEhc1wLfvzMdPpzcsSyrX3GvJH7WhgzmKETHevuexisdkxY0j\nuCA7DPkOdGE3OoP0I+zigL/fgDQo8nr4JiyM5u57NUUp+OsmpGHtRa8nv86g\nLkpD\r\n=i9mp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "3f6bbd30140fa7ba2b7cf07c2fbbe467f836ad42", "scripts": {"test": "run-p test:*", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.2.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.8.3_1640249648089_0.10986345986642143", "host": "s3://npm-registry-packages"}}, "0.8.4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.8.4", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.8.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "8a14285ffcc5de78c5b95d62bbf413b6bc132d5f", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.4.tgz", "fileCount": 6, "integrity": "sha512-J3LABycON/VNEu3abOviqGHuB/LOtOQj8SKmfP9anY5GfAVw/SPjwzSjxGjbZXIxbGfqTHtJw58C2Li/WkStmA==", "signatures": [{"sig": "MEUCIHP1UdHPoD5hTEEUHc3YQH5CZyW2FaGhc4sPlFfQ/54IAiEAy2DUJMQSyr1U9PQkkbJUEkrjhSOxuMVAUIKMUmtet6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+p5XCRA9TVsSAnZWagAAbYcP/1dyIo4cPRvwRas6V4GY\nCwReMUxjF3M2rJYoXR/X2x1ChjB9JbjJ5IIm6LQGuMHwywPR7ybaT4VNrrh5\nVLFZ4ihOelUuaup8hD2DKsRVH75d8NtCLQV9F6JEPsKDNwrFNsbAC5/04Jv7\n2xIACHtf/Mo3rotAAdyxGNBiQDN07K/9ZZmU3FEoBLeyl3Xdek3mFDY49bAe\nH8eTzml55s8++BunrVftpHJEIvkfVPIW54aAj5FYzlokpYd1ZgNxbMuuZdbP\nwMAMHaBnN0iBPN7O5wyVBgOW/SAX5NPZpVIvexMdiESrPEzLptqj9rejyEMq\nXRl0DdkPPImyzq8QLQmrjnU9GwKlU/zvdjC2n1Ozh4Z9qvaxkAcV/Jgxu5GS\n2/jk75xTGC10l9AcYr1VP3omzPg7qk0D1YQ0DNnB9fm4jrLm2CELh//U0zAw\nERh0qLxIijgki8TvfnZMw6iGAU9NFS5nzifKgWnfsmaU5fDTZWiTLvAwUWS2\n0nR9A2xmnhdK7c9OvpeAulFJRJYK58bWhivKaBX5xtGpxcBGFZg8xlaih75N\nT+Tt1uDwBmpcnFq2SCrf5wMCpkQWx/7vpQAFpPwq1keQ5FEtWgKz+0GUBNjT\ndzA3Del9NY73SM57Esq1JrNKber2aRYSx7cJNelmx25gWoayLwWroiGE8iDR\nkgAj\r\n=reAg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "e66d3d07e5d0639d14d5b599cba1c8afa58ad756", "scripts": {"lint": "eslint --ext .js --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.2.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.8.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.8.4_1643814487483_0.659390156269323", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.9.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "5046cedd7156714c2856a107208490875e87fd0d", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.0.tgz", "fileCount": 6, "integrity": "sha512-iYZJiKJcOWAPm2bfak3jm26JSYhqTQg4kAlbrEnsMWYcm3SIYmZJ5fdJlXGaVVOfmtAgLJz8SqA8aM1tHXs5Ng==", "signatures": [{"sig": "MEUCIQDIBWEV6/ksIOjW9Obw5b3OrMZZqSM/2OKIPjGZWbSUcwIgaepd3yDvNW9mSEH7oWvJ1bX9+SZoefDHuoN5r4xwRCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuMk/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY/A//YomA0HpZj4gDiG/guL5c4S/HNBsQX8UZPQsaAQ+vjr+ONZW1\r\nPxOCbMpMkr3SbPCD8YsLbLXaXyQ1nHLRoFdhBpBV9cZdjTZ0ZhAnNF+AIoqZ\r\nLCrKeL87bVJAI79F+geEul1IG1h5NkGfo5GKzZ495xZ5fj/ZRAAIDwl/5giX\r\nALQtryaCkJfQ54OrwVVJFHmUpMqhwYBJAxUaq8Ob14sopDwLg0jeUo5u67F1\r\nEdoPGBVLHogzcqpYwyWp0lomoWzxCR7nGyUr3OgsLADOFcXYeOswL6CNWqZc\r\ndCoWbPgv4MmyddZ/CPUnJyJMHWGWt1r0uyOXOjNTWQ22SyFe7s0cCPT+aCVk\r\nVIKUI25oPwvNO40kxrjUmC5Hf1zJ8XF07QCtpHGoURPVnlFnw0U+R7XdElH1\r\n+gJKLIKGnR6+SxwdHcYoR8O+YEb4UUov4+twKs4W8OmR++ltW0cB1dJO8fhm\r\nUgvZ1AuMQAGliRr7Y5ZbEpN9K9HUC6RVB1bYNhFADGoo6QEvmEkzAJbsKm0Q\r\nO/T9/fTVRF/Pm+RnZ/QJIwc6W0P0mt9hRNvxv/nfSXnvig3i4DCkz1+mxJ6G\r\nfn08QpzL5BABTKZRJxmhVfIeG7szCUmnBZzCkE8shsMaTrtEdCKCtDj3OzF/\r\n7R7bjPVR36eeXXGQtDrO9AVggudTKDjz10Y=\r\n=w06y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "ace5e556e08d4779bc617e1693325766e9364ba8", "scripts": {"lint": "eslint --ext .js --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.8.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.9.0_1656277310928_0.3864844155502596", "host": "s3://npm-registry-packages"}}, "0.9.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.9.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "regj<PERSON><PERSON><PERSON>@0.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "272d05aa10c7c1f67095b1ff0addae8442fc5709", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.1.tgz", "fileCount": 6, "integrity": "sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==", "signatures": [{"sig": "MEQCICr5n/c97m3Qtp5b7M7agha5BGt/X4JKb98zpb11eLi0AiAitfY2Jfq/6ptsjqvzGwPhh1C/N5G5raJlkzP0MQBocA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyeyRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5lA/8CctKH4mD+getWqSZNCL3/gCG83J4s0YtEIzGZfZGKJmExWCC\r\nFQ5EQ/uObHxDL+a5E91cdguNEmu+c7pAGV4yYNr+yMQQf5f1VLrTXoXBgAiK\r\n1Lk4C3+Dj1An4C6ExPvTyaCl5BhygNBgNA1OIhx6+yj4k2WCmra9NHGq6i/6\r\nWmlf4tyBoXBMp7HnjqdMEF7aScOIysHKePsf81sXluSyg812CRPiO8pSewI/\r\n1JyU0M8FMFC47Z/JyvbusKhVO+kSQifclQaQ5iFROGCviocDyRbraxHqxlee\r\nsQo2QvvDf8ezqlKL75ZObiLCp61tkswHVdRA8QShwn/b4/ho0KTtzjTdEqwj\r\niuXcY28tssRPf5SHNy8dTpDajxur5Hy65rHG5V61ibxJbk5YJbVLql74u9wI\r\nO69/1GGvv+E6FoKEcgmtEYVxBRyzBqtNskJSvwgsXC2wbzX/3yJIuUE2F2Tg\r\nLb7/OM2re3N4JlsAr80+oMm+b5CON4lCXxIKFbBn5jqDqhmj8xSth2yCyPP2\r\nv3Oy327vmh/n9dKzLqEsRCs8pS9intPIc4Trridju0dC9e7OzxFYdb1Q4ost\r\nQocdye0SijORZJuNaSOaWjE9cTHmfM9cjusksES0EFEygOZGGO5NQBEXXbBF\r\ng8KS1gaj6i1wxbC/V9r0ll0ZrigeyjBbp3Y=\r\n=jLrp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "d58221da3833617e28ac521ac4a0001715de5c56", "scripts": {"lint": "eslint --ext .js --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.8.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.9.1_1657400465116_0.21381924837202537", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.10.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "b1ed26051736b436f22fdec1c8f72635f9f44892", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.10.0.tgz", "fileCount": 6, "integrity": "sha512-qx+xQGZVsy55CH0a1hiVwHmqjLryfh7wQyF5HO07XJ9f7dQMY/gPQHhlyDkIzJKC+x2fUCpCcUODUUUFrm7SHA==", "signatures": [{"sig": "MEQCICEh/Xp6dyxk4VTViB/0A+WySIHtbgtpTRRjWfCoAwBZAiBMIGOqLFTBAvqqBqI9uGjEdGY9YZd/3aXsXkEfA7bK4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkD173ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdPw//fDPjeMg7FVl3NRn1kUMyCz52JCSLwLgYKlXUDvTnbcE//aNB\r\nmF8jHtWXOTrAPyXi75swSsljm9pJLyGyQqUdsLzDMvvjh/4iK/e/0mouUoZr\r\nAXx6FHH8ctEVKAweZDQZuB2dGeNOwbvZUQHVVcI6DICuhRAQAMaGfLzqFTQ0\r\nHQOOofZDzhNfsdeFtuQpFkAktADpJp4I89Louch+cZece2z8tGz1QvSmwNMp\r\n4KfpxVUzHov8Vc8D6cTSiFCFHiVavhHKP4cI/hdMARv/w0aXTBAkg1CHHFQX\r\nAEfajZIJ+RG+XwtSZBvBG9yCeUOwQ8tpoZ+joGML1PcTvD05rUrN20iLtlUL\r\njOoxmWYeFrle+m4FpaibUEvBOUecvzVlZlEBd+SkH3tTyO8oNmdxwr9ZFkyf\r\njANXJiu2Wi50in+rp2GOuL3Sb492W1h9zlOjeilNX502UTNKZ4bGN+LEvt7d\r\n/UPGvgqsYVxl4L2MXC5OkbLLAID1r2DST4fkowukuTd2cAfc3Bm3Ii+IUnbK\r\nfTE11rgF/kiSwZyPoh2lzZtaj/je+0AfczSQjOawkaq2x95nJFp1foYKPVJm\r\nN6kpiyM+zaWDiLfG+HrKSkNzZBZX+dGIr6azAlzp7aZgDwn4lKMhW2+vgX++\r\niPnDmB+omfckddcmrzGZeInMrAnENca8i20=\r\n=QJZk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "c622da77ffa1fa61ec2b36e19b17c8abfc3e6a9a", "scripts": {"lint": "eslint --ext .js --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"jsesc": "~0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.8.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "unicode-11.0.0": "^0.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.10.0_1678728950858_0.4621854670377168", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.11.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "f01e6ccaba36d384fb0d00a06b78b372c8b681e8", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.11.0.tgz", "fileCount": 6, "integrity": "sha512-vTbzVAjQDzwQdKuvj7qEq6OlAprCjE656khuGQ4QaBLg7abQ9I9ISpmLuc6inWe7zP75AECjqUa4g4sdQvOXhg==", "signatures": [{"sig": "MEUCIBwYNGDZOkjc0HUWs99sCNpJLwCECEgMx7F4O5bP44g2AiEAliV5n0GM5I3yxskiYzSUCJ4/A2BRj+M9PgXe7jDhJKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68348}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "47eaf14ab217dec60dd148fbd9b2b221e6c70e79", "scripts": {"lint": "eslint --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"jsesc": "~3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^9.10.0", "globals": "^15.9.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "@unicode/unicode-16.0.0": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.11.0_1726621057824_0.7840041288938171", "host": "s3://npm-registry-packages"}}, "0.11.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.11.1", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "ae55c74f646db0c8fcb922d4da635e33da405149", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.11.1.tgz", "fileCount": 6, "integrity": "sha512-1DHODs4B8p/mQHU9kr+jv8+wIC9mtG4eBHxWxIq5mhjE3D5oORhCc6deRKzTjs9DcfRFmj9BHSDguZklqCGFWQ==", "signatures": [{"sig": "MEMCIEIaDPY4Cm/shMsBwv0AiAulc3BvflXLKNG8lE851Mk7Ah8Nhxnpxsqu9RprmS9sP53ZiKSs/AtKjS6kUfr2pLdC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69382}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "0a113bb9cf28353bebe9af4e971c04444012c349", "scripts": {"lint": "eslint --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"jsesc": "~3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^9.10.0", "globals": "^15.9.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "eslint-plugin-regexp": "^2.6.0", "@unicode/unicode-16.0.0": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.11.1_1728160357725_0.2602555087900531", "host": "s3://npm-registry-packages"}}, "0.11.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.11.2", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "_id": "reg<PERSON><PERSON><PERSON><PERSON>@0.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jviereck/regjsparser", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "bin": {"regjsparser": "bin/parser"}, "dist": {"shasum": "7404ad42be00226d72bcf1f003f1f441861913d8", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.11.2.tgz", "fileCount": 6, "integrity": "sha512-3OGZZ4HoLJkkAZx/48mTXJNlmqTGOzc0o9OWQPuWpkOlXXPbyN6OafCcoXUnBqE2D3f/T5L+pWc1kdEmnfnRsA==", "signatures": [{"sig": "MEUCIQCQlROms8znHt+yQ2lB6xIa4McxwbTfz1drDiZEz/MwQQIgY2gdLXXto0b/Q7PaVcWGDvCxf9LDv6JPGf5eCdaeYGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70810}, "main": "./parser", "types": "./parser.d.ts", "gitHead": "ac51d2be146cc7d337e87dcc094abac64d9f7c6d", "scripts": {"lint": "eslint --max-warnings 0 .", "test": "run-p test:* && npm run lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/jviereck/regjsparser.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Parsing the JavaScript's RegExp in JavaScript.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"jsesc": "~3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^9.10.0", "globals": "^15.9.0", "regenerate": "~1.0.1", "typescript": "^4.5.2", "npm-run-all": "^4.1.5", "eslint-plugin-regexp": "^2.6.0", "@unicode/unicode-16.0.0": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/regjsparser_0.11.2_1730045070679_0.9874524462255936", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.12.0", "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "main": "./parser", "types": "./parser.d.ts", "bin": {"regjsparser": "bin/parser"}, "homepage": "https://github.com/jviereck/regjsparser", "repository": {"type": "git", "url": "git+ssh://**************/jviereck/regjsparser.git"}, "scripts": {"lint": "eslint --max-warnings 0 .", "test": "run-p test:* lint", "test:src": "node test/index.js", "test:types": "tsc test/types.ts --noEmit", "bench:baseline": "node ./tools/bench/index.mjs baseline", "bench:current": "node ./tools/bench/index.mjs current", "bench": "run-s bench:*"}, "dependencies": {"jsesc": "~3.0.2"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.6.0", "eslint": "^9.10.0", "eslint-plugin-regexp": "^2.6.0", "globals": "^15.9.0", "npm-run-all": "^4.1.5", "regenerate": "~1.0.1", "regjsparser": "^0.11.2", "tinybench": "^2.9.0", "typescript": "^4.5.2"}, "_id": "regj<PERSON><PERSON><PERSON>@0.12.0", "gitHead": "28addb2105bbef376253843a12b731f0df2621ab", "description": "Parsing the JavaScript's RegExp in JavaScript.", "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==", "shasum": "0e846df6c6530586429377de56e0475583b088dc", "tarball": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz", "fileCount": 6, "unpackedSize": 73459, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD+Bn4CEvhAsqOrhA51I2ieXVvMjRdwFzsSrlZr0Gp4wIgdf7dc4wPMZoqN+BczgJsBL1etWW6OYxaXKlkWQsrFwc="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regjsparser_0.12.0_1730250659368_0.81651340906821"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-11-03T19:44:41.181Z", "modified": "2024-10-30T01:10:59.784Z", "0.0.1": "2013-11-03T19:45:23.708Z", "0.0.2": "2013-11-04T20:20:38.206Z", "0.1.0": "2014-06-29T16:16:45.871Z", "0.1.1": "2014-08-30T12:28:06.125Z", "0.1.2": "2014-08-31T20:53:47.038Z", "0.1.3": "2014-11-25T10:15:40.084Z", "0.1.4": "2015-02-27T23:53:19.115Z", "0.1.5": "2015-08-16T20:55:08.321Z", "0.2.0": "2016-05-24T22:03:53.129Z", "0.2.1": "2017-02-23T19:57:14.996Z", "0.3.0": "2018-01-13T09:13:50.236Z", "0.4.0": "2018-08-28T15:03:05.342Z", "0.5.0": "2018-11-25T15:00:24.092Z", "0.5.1": "2018-11-25T15:07:53.377Z", "0.6.0": "2018-12-04T20:46:49.568Z", "0.6.1": "2019-12-13T05:55:05.278Z", "0.6.2": "2019-12-21T06:02:07.632Z", "0.6.3": "2020-02-14T00:28:27.879Z", "0.6.4": "2020-03-11T03:18:23.859Z", "0.6.5": "2021-01-10T17:28:54.440Z", "0.6.6": "2021-01-10T17:40:18.110Z", "0.6.7": "2021-01-30T16:14:11.172Z", "0.6.8": "2021-03-23T01:15:14.740Z", "0.6.9": "2021-03-24T00:13:34.460Z", "0.7.0": "2021-09-06T16:46:02.194Z", "0.8.0": "2021-12-08T14:38:51.697Z", "0.8.1": "2021-12-09T23:12:17.573Z", "0.8.2": "2021-12-14T18:25:41.867Z", "0.8.3": "2021-12-23T08:54:08.219Z", "0.8.4": "2022-02-02T15:08:07.612Z", "0.9.0": "2022-06-26T21:01:51.081Z", "0.9.1": "2022-07-09T21:01:05.310Z", "0.10.0": "2023-03-13T17:35:50.999Z", "0.11.0": "2024-09-18T00:57:37.991Z", "0.11.1": "2024-10-05T20:32:37.950Z", "0.11.2": "2024-10-27T16:04:30.881Z", "0.12.0": "2024-10-30T01:10:59.607Z"}, "bugs": {"url": "https://github.com/jviereck/regjsparser/issues"}, "author": {"name": "'<PERSON>'", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/jviereck/regjsparser", "repository": {"type": "git", "url": "git+ssh://**************/jviereck/regjsparser.git"}, "description": "Parsing the JavaScript's RegExp in JavaScript.", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# RegJSParser\n\nParsing the JavaScript's RegExp in JavaScript.\n\n## Installation\n\n```bash\nnpm install regjsparser\n```\n\n## Usage\n\n```js\nvar parse = require('regjsparser').parse;\n\nvar parseTree = parse('^a'); // /^a/\nconsole.log(parseTree);\n\n// Toggle on/off additional features:\nvar parseTree = parse('^a', '', {\n  // SEE: https://github.com/jviereck/regjsparser/pull/78\n  unicodePropertyEscape: true,\n\n  // SEE: https://github.com/jviereck/regjsparser/pull/83\n  namedGroups: true,\n\n  // SEE: https://github.com/jviereck/regjsparser/pull/89\n  lookbehind: true\n});\nconsole.log(parseTree);\n```\n\n## Testing\n\nTo run the tests, run the following command:\n\n```bash\nnpm test\n```\n\nTo create a new reference file, execute…\n\n```bash\nnode test/update-fixtures.js\n```\n\n…from the repo top directory.\n", "readmeFilename": "README.md"}