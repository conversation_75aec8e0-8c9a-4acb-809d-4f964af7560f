{"_id": "cross-spawn", "_rev": "150-aba683f22511fbeb213c3db3e70462c7", "name": "cross-spawn", "dist-tags": {"latest": "7.0.6", "6x": "6.0.6"}, "versions": {"0.1.0": {"name": "cross-spawn", "version": "0.1.0", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/npde-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/npde-cross-spawn/issues/"}, "dist": {"shasum": "804a4305ffe717f5f50598a855c08221a227c370", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.0.tgz", "integrity": "sha512-lcdnEjNEBmAlmruqppcmIwmVgzk2PdaDFsv86eTgWdAAkpodKvLkz7aSxVa3CugsiqKgEiIeVMbBoJSow/RmvQ==", "signatures": [{"sig": "MEUCIGLudOoA34l9t+0V3/lEMFdHtMBvMmAobzMMzqqNJ89RAiEA/jGbluPtMcUYdvkISa5XKktzytJgKHz17grDSM8kLqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "804a4305ffe717f5f50598a855c08221a227c370", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/npde-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.1": {"name": "cross-spawn", "version": "0.1.1", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "ec7d08cf044c1f349e7aa21738296f665f8d7b8a", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.1.tgz", "integrity": "sha512-a9r3C5SUqZeGOpJGKskHPrd3iQcy+DOdnGN6u6z1EIrankLnKD42cx5JZ8t28q2jAFCOLEE53HC/fXRNgae9nQ==", "signatures": [{"sig": "MEUCIQCAq9uX4tR7sFgxuUXYFf0exrjasG8+lzGAGLIH8WXKegIgRmDCCA7PAOrJzrV68ReQ1Fy3biXqZ1Ul4dXRgP2fzG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ec7d08cf044c1f349e7aa21738296f665f8d7b8a", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.2": {"name": "cross-spawn", "version": "0.1.2", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "83ba4d7f394d41683253684052e669d14a873f10", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.2.tgz", "integrity": "sha512-ipCzoJ5TIPleMo2E/DC5gDjh5cXgQOblWVAubM7c8RFYoBJtdOExRBENytuSTcrR12pnl0F9FzhYlu8ro0OkVw==", "signatures": [{"sig": "MEUCIF70HWTImGqURB6aHk1mqLtWcdh2bmXYqQyTFT5P9zUeAiEA6j/2lZbKTQ51fF0iYHFv2v6rNFuvTv1X/XdR8cFQ0WM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "83ba4d7f394d41683253684052e669d14a873f10", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.3": {"name": "cross-spawn", "version": "0.1.3", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "08705196268ee352d99edf03c53ce05bf218ae91", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.3.tgz", "integrity": "sha512-NM6ipk8ybcJutkI/lj3R//21jqLy/M7wDUWfoHL8zzWWDnERks25gdsB1IW9Ur9spU5uPkhNVWZzNB5hkpJtxg==", "signatures": [{"sig": "MEYCIQC+TxRp00UqVJwXEPR4Qv/Uc6cXUQJj8cDf0zCXv3ikLQIhALCRcr+Q5OQ6RSOLUO21hHRC7OT539Dpxxq34CfeKQNo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "08705196268ee352d99edf03c53ce05bf218ae91", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.4": {"name": "cross-spawn", "version": "0.1.4", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "216fbe401cce5c1fae8f4270367865f841585992", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.4.tgz", "integrity": "sha512-rPRbyh9lodtgeOAHVUmorlCT3XK1VPsU7/Rr+j7J4/4WjVY7js2uh95o2zoFXS1tSxLmqTvpD6Z2chrWEIew6w==", "signatures": [{"sig": "MEUCIE3Av0g2n17m863oLBP0pzfJy5+WegRH5++C7qN8h2U+AiEAuSpWLygAtxiUILbhQQ2fXlYqqH3K3anxAqMuduAuRT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "216fbe401cce5c1fae8f4270367865f841585992", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.5": {"name": "cross-spawn", "version": "0.1.5", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.5", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "5578f122bebf8476a6b1846ab082adbea83d0a65", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.5.tgz", "integrity": "sha512-allyLtHTiL/JiLH4EsXRAvxu8MS68BSNoIuNc65CMdpLiiLG/BmJymN+bzZzdRLyNb+pPCAAuDkFn0pPLHHQeg==", "signatures": [{"sig": "MEUCIDsBS9DMKID/CIZkN1GOjZ4eW2FlAhcJgzKOK9H68B85AiEA0G33MuGgOOUb6vadCtSv4vgjQ0cw6HiUqvkNpCBK3HE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5578f122bebf8476a6b1846ab082adbea83d0a65", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.6": {"name": "cross-spawn", "version": "0.1.6", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.6", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "73de1551529b5865ce0392456ea5491a708aa809", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.6.tgz", "integrity": "sha512-O3e0SjGCr7BdN5V095/rSt5tjURCEaKBjBhOP7j1K4Yjq9rh4zyzfo3IXOQtXgY/N/SPaarZqLKTxJaV3HUXfQ==", "signatures": [{"sig": "MEQCIEZsSIiamJWVK8dvtm3SPo82PX+Z1YmiVHQXpCO0eiJ8AiB0UAkrZHuWCi46b2oPZK5X2MP/+MBnm6Hr1bJdTRBWaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "73de1551529b5865ce0392456ea5491a708aa809", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.1.7": {"name": "cross-spawn", "version": "0.1.7", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.1.7", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "7e26c368953fd9618215f519056fca17c4c43d0e", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.1.7.tgz", "integrity": "sha512-yJ53LJMJiTh1AL0uL7gD+hAK6WJZ6G7gKXw2xRjkqlPswYSIns7fKVXVYJbPelrWLHEBKI/XhreQREzK4TbawQ==", "signatures": [{"sig": "MEQCIEH2CRTbzM3zt3Og+IpGMa1/5M8BAySpAZ6TQ1nU9o64AiAvWEejKzjX3vOhMXPjVDA/TkhLypJeItA53+rxEdRQFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7e26c368953fd9618215f519056fca17c4c43d0e", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.0": {"name": "cross-spawn", "version": "0.2.0", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "adf1f68b6b0a1fc221e65446e075e06ba5e34072", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.0.tgz", "integrity": "sha512-2jWXTZPeoZPzGNfQuwf8IZFSyZIlrRTZZXA5ISc5LTTSpxrlLRVPQYkjNgsW7LxV/qGahGveikFv6Uv8nCMdyQ==", "signatures": [{"sig": "MEYCIQDAZNvljb4G1nebIPojYYxPOy2VlCzHtduXSmjLNNgffAIhAMTpWTuJuhE8UIh+pm8dHUzEuBGKnJQiXm2K8Juh7gV/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "adf1f68b6b0a1fc221e65446e075e06ba5e34072", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.1": {"name": "cross-spawn", "version": "0.2.1", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "740c2be6ef07e4693fadc863438f33c633bedca4", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.1.tgz", "integrity": "sha512-1oOYUqHnYP0eFkyu4AcWK17/5i7VzIy/s/I5NSYRTzBapL3VyNYLlndcFp+SR++PKXqPk0nj91N08CG+5MdjIA==", "signatures": [{"sig": "MEUCIQDNol6ym+0zHo2GPLPtU3UBXvdiPGzEaJUFoo2+AXEQBwIgNWbdIMXNohKRUbTkUkVz4ysjRqyxLUkUv+XiEb13sPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "740c2be6ef07e4693fadc863438f33c633bedca4", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.2": {"name": "cross-spawn", "version": "0.2.2", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "e843220593b3bc6e7e7c96decb1dafec69024ecd", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.2.tgz", "integrity": "sha512-9yZ3ob+UputtlS1nOPJMFGBDBjU8rscerxGOoE3H3+v0zTYHWkt1dvY8qfpA+vwfFUXM8iMA6VBiydUaOHD3xg==", "signatures": [{"sig": "MEUCIDC19PyTr89j5CX6fWXb6PMlWfM8nWbSDRMsPjaan066AiEAqUyEuTtO/nXfB8UEc+lm91VqAvInz7X/Qb5LBPVHUyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e843220593b3bc6e7e7c96decb1dafec69024ecd", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.3": {"name": "cross-spawn", "version": "0.2.3", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "9b40ef3270fbdfba9a0ad42fec9db2c40027d08c", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.3.tgz", "integrity": "sha512-YMXXn5SjH8KKNENBJz39Er7yh2qi/U/AbM8YuJoGMt6TwTdjO9qOY9A0BgSam3JVtQhv6ygW/Ek7crdDoRC0Tw==", "signatures": [{"sig": "MEUCIG5qXSU6ZG4j0XhCuV/M8KFk5tuex/IXz50y+IbFRCO4AiEA6ltm4XOvkq6/+phdvBEP4OynfhgWMPYPoYB+IF13Fw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9b40ef3270fbdfba9a0ad42fec9db2c40027d08c", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Cross platform child_process#spawn", "directories": {}, "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.4": {"name": "cross-spawn", "version": "0.2.4", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "b28444c6b8da2d6b5aa99bd04f0af2e029e9f1a1", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.4.tgz", "integrity": "sha512-JJV/hkaqyKRJl5WvjytbNjYzr9cya5AgtvnOfTbYkxP4vUKzGcPHH3/LdaM86BrrkAnyKdMe6K3kTOJRJxkIOg==", "signatures": [{"sig": "MEQCIAtYOQuN0q1XZpfnfZlw7TINoABxQMWiSssXG48kZOyMAiA+TC7lmpPLXoc6RfD56AbRc8PZg+PNsxTn/g3fmDI5RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b28444c6b8da2d6b5aa99bd04f0af2e029e9f1a1", "gitHead": "0021b10fbba574e727c92459ff0bfdcc1cc0fab2", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.5": {"name": "cross-spawn", "version": "0.2.5", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.5", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "39b123afcceaf0218ff4c198bfcc665e7ca7879e", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.5.tgz", "integrity": "sha512-3/XUl4E1uDPBDyutGq3E68u6VsH90DhOBxWNg3dHNmuiKKusFOw+BAV9DLqB/BIaVdcKEizzYWI6tU7qgu8uzQ==", "signatures": [{"sig": "MEYCIQCCko6D+OVZsR211YH/Z+lPbeWZ95684Nyy7koHB2+fswIhAPU4a1T0ktu0la7OnL2pTavPZkrTLs9LwF26s/9aGvX3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "39b123afcceaf0218ff4c198bfcc665e7ca7879e", "gitHead": "89edcfa212bb75d5e1afd3e140c3040683ae5502", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.6": {"name": "cross-spawn", "version": "0.2.6", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.6", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "1a3512dfc5671da621f0974f26ed3bfd1555ee91", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.6.tgz", "integrity": "sha512-C+IoRwVofmb+lruuETo301lqGQBnadbwWZKOwEH0zK+60+7S4XN9SgaTx19Stu5LZq8yCt2bH8kA0ia1aYlgVQ==", "signatures": [{"sig": "MEUCIQDUS3+h9Rh3+sJjuWKO7yjzSpuhrjqZjFMF8vxbnntrKwIgHF31H6apNHUwFFDtT2moZw9LXA6A2h97SuUmzIjj9yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1a3512dfc5671da621f0974f26ed3bfd1555ee91", "gitHead": "48f2c14382f20a6c9078edb6945be3ae7a4f550a", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.7": {"name": "cross-spawn", "version": "0.2.7", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.7", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "b2edc68f95413c35dc2a0bed8f0c981c50dc4f81", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.7.tgz", "integrity": "sha512-pchzTvIeCT4Fa6b9K3vv6uRtp4zUZogkkulc0PAqE23Pt4J65M/3Utp1021YlPBc4kUMPF5T0HZv14XpVYpA/A==", "signatures": [{"sig": "MEUCIQCC2It+CgoJjgF7UJEL1hrCO3A63apnr/SrylDYQ/3g1wIgY79T06dEZRT2Iw3b+4BrZctpdYpA6fbJ8P9f+wLu3eQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b2edc68f95413c35dc2a0bed8f0c981c50dc4f81", "gitHead": "a403c3d72d811226c3dfb6c3c408a01a4ad37022", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.8": {"name": "cross-spawn", "version": "0.2.8", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.8", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "0f042e792eaeb5fdb098c4524aecfd5553b8ea57", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.8.tgz", "integrity": "sha512-BvGL7I0nE3hFWQPuR9oS2PIxDvRBhv/2sdWwMoXuwlEcGsLmVac2Oem/U8Wk4cV95yTUTbq/aKbaSDG40voX4w==", "signatures": [{"sig": "MEQCIDwAhjLlH61bYhDZyDkCKuiHQRSZdp0nkwL4lmNe4mezAiA+hjtd+rPQrMEPlD/uy7WqEjwU88zeBxZAtdb51KSFyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0f042e792eaeb5fdb098c4524aecfd5553b8ea57", "gitHead": "f41ba1a9758c1f43a1f0fd263ecd6795f80a5807", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.2.9": {"name": "cross-spawn", "version": "0.2.9", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.2.9", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "bd67f96c07efb6303b7fe94c1e979f88478e0a39", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.2.9.tgz", "integrity": "sha512-jUNffe+x93R0/940d+JrdIl8SROZdUuvlw0HxjR/0GUKGvJEWiTK5rxtKNtP1lgMnoR8383q0orSA6k3eJ+y4A==", "signatures": [{"sig": "MEUCIQDa49MctatwDQ+ey0rYtUGfGePMO7el+Cr6f7GKNpxdWgIgUulWFXh03mT649JAfvcAq5iixcC1a1eDoR27rPlEfcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bd67f96c07efb6303b7fe94c1e979f88478e0a39", "gitHead": "6fececcbd8331f98b4cfd6560b925bc4d8e77f47", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.3.0": {"name": "cross-spawn", "version": "0.3.0", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.3.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "586ca7abec0887ce0600a119990fb3a3c80ccee2", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.3.0.tgz", "integrity": "sha512-oIKVMjvXqW5OoT5PmbGxQX5AOgDeL8MtQXeDSYqvsbsjHPb8MWxYqSrb4PtuETJr3U4Q8uQfCqcD6whFUsGtlA==", "signatures": [{"sig": "MEYCIQCPE7rQmprpEwod6HCe4YgXTceBIgrkCu9qOdOsjzP7eAIhALx5xqQq3MXuYpXxwZSrg2eE6Z6CvS+2r4zKV8rEBoZO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "586ca7abec0887ce0600a119990fb3a3c80ccee2", "gitHead": "a431cdc9e431ae59b994d3ac3dced552e90a4434", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.4.0": {"name": "cross-spawn", "version": "0.4.0", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.4.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "29e97f5098362d39245d795f63834e6aa9b2df15", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.4.0.tgz", "integrity": "sha512-NQPU/z2F6yX9a79yRwLH0cYU8R+db+DJLb3v4wLL0aEK73BVecvfEP4IlJ0ot+psnxQPGqUtGTbB2FWkUV3H0A==", "signatures": [{"sig": "MEUCIQDVJj62CucIHHpvlmmOn5pmtfpeLyOWn6hO+YjdR+o5dwIgJNdqXMPRXy7sj6XcAEv2x1PGB9uXJpQdBTE3tD2zWkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "29e97f5098362d39245d795f63834e6aa9b2df15", "gitHead": "b5e83a90ac4493aceb158b3b5d3274b087da5b10", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"lru-cache": "^2.5.0", "spawn-sync": "^1.0.6"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "0.4.1": {"name": "cross-spawn", "version": "0.4.1", "keywords": ["spawn", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@0.4.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "05b2c16fca761350b492ad455802ea1bea3fadae", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-0.4.1.tgz", "integrity": "sha512-nMYgWL7iU2uc/cD+8QnEJtUjND8ENTj7Ctg2z0cUgO4tJ2BN/iiAhDZ9V7U0BvR8I0dooANxzk2sjCmrB7He7A==", "signatures": [{"sig": "MEYCIQDH3FCDRtLD2eQebm8SB2vYf8+bIIisOoK1+4FUcfl4zwIhAOpnNKdJQu6fnHI7P/qKMSs+YVSKNsjQvShEatebrMvi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "05b2c16fca761350b492ad455802ea1bea3fadae", "gitHead": "3679e6942768de5a61a7c2b5b8064ff4bbd78362", "scripts": {"test": "mocha --bail -R spec"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Cross platform child_process#spawn", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"lru-cache": "^2.5.0", "spawn-sync": "^1.0.6"}, "devDependencies": {"mocha": "^1.20.1", "expect.js": "^0.3.0"}}, "1.0.0": {"name": "cross-spawn", "version": "1.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@1.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "53e3c1e2d7a03206b226cd7c57807a5d4fb4282e", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-1.0.0.tgz", "integrity": "sha512-X+jrKBvpGCVpG1S1MGd0g8knthG06iFHeXgwrYlIljdlepqYKFfu+ASy/msnXl/kxOMJ7wxyOKJ6gUGzYo1Bxw==", "signatures": [{"sig": "MEQCIAsg/+cgU1g6Ve+g5lERtOEdHjHW34g9ydrGzk+F4QsZAiAD/yAFl831vEPHwqRdK1eOkeroFwpREzqD53H44t/qeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "53e3c1e2d7a03206b226cd7c57807a5d4fb4282e", "gitHead": "b5239f25c0274feba89242b77d8f0ce57dce83ad", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"spawn-sync": "^1.0.6", "cross-spawn-async": "^0.1.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "1.0.1": {"name": "cross-spawn", "version": "1.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@1.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "ec68b90d43d7eaf1ae602a5dce7d075b40c472ee", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-1.0.1.tgz", "integrity": "sha512-YmeEGsUNIaHDf+77illprx6Kc4VhMWzqiVuKXIHYMq+nuyhNjZlJP7R9D6kyiSyR1ZmiedoQqz9WJPe4n5KrXw==", "signatures": [{"sig": "MEUCIQD0TW/f4P+9igYPV5JEwudtP4uNpz5nUHi5DkBSs3UxOwIgfIiK4mGt7sMVmpw6r7+x6r4rLRIbBqeICtWeohfmDOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ec68b90d43d7eaf1ae602a5dce7d075b40c472ee", "gitHead": "7e354c58e4bc5b94f535c7d488c7d868dcb72dd0", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"spawn-sync": "^1.0.6", "cross-spawn-async": "^1.0.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "1.0.2": {"name": "cross-spawn", "version": "1.0.2", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@1.0.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "16405a46325fb3e0f4dd271f5d9618e02560d68d", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-1.0.2.tgz", "integrity": "sha512-cC2Z7fgabDNQrEIywaon6x9jhzvSfwuNPpdplBPCGDdzfJ+oCR2UAxrb3W0wCW4lPaKk8OefBqmgGj4ATPdg6w==", "signatures": [{"sig": "MEUCIA6BK6BE4JRpIqy2Z7gTBNMrjJUiKwpF5b6ea0rYRbZtAiEA0HVtdFIlx1Dy1LN1KU8hNiVi1cU0xyptp2E9Xgx3CdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "16405a46325fb3e0f4dd271f5d9618e02560d68d", "gitHead": "bf0d22195571b06ccc882cd100fd7fb7c5a2ad86", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"spawn-sync": "^1.0.6", "cross-spawn-async": "^1.0.1"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "1.0.3": {"name": "cross-spawn", "version": "1.0.3", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@1.0.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "eb4f604ee204c3c555dd9e4b39bf430b69e977a6", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-1.0.3.tgz", "integrity": "sha512-OpavNnNPVbCprPbr390CpsfB9NYkESDDZFptlwT+vUmuTT9JD7royO4k8hfZW0sDdVKbgoRutIqsddNWogA6IA==", "signatures": [{"sig": "MEQCIGZ520j8Dzd6yiVwTPeG7RanLBo7yqg97mDF2CGcWx9jAiBFV3w2OGKA1hrMDJ+WrblRxHn49XJbGTw6pXQCTMkPig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "eb4f604ee204c3c555dd9e4b39bf430b69e977a6", "gitHead": "3c02b4036eeb9df39004bfc3f0ad3d13b668d0e0", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"spawn-sync": "^1.0.6", "cross-spawn-async": "^1.0.1"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "1.0.4": {"name": "cross-spawn", "version": "1.0.4", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@1.0.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "4b60d515f5d8723bb4cde0e8e3d0240517ebffca", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-1.0.4.tgz", "integrity": "sha512-J3bBPtzmiMdJzF9iIMSprPmgx4aFaFVw0MsKjzKIvSmhlOHzliTGRZ6RpjktTq8r61tiIYn/K/qVuyHqCTA75w==", "signatures": [{"sig": "MEYCIQCtwRXuu5eGWQyfWmUU2GKXnXUzXK1Vtlz4A+GsDDbZtAIhAJ0An7ynDHPRGU/AGQgmkJVP+PsCjdBDPxTyEnMbsgYf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4b60d515f5d8723bb4cde0e8e3d0240517ebffca", "gitHead": "d27621d9ee8b81ee4913d9c8377e5e23add54828", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.13.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"spawn-sync": "^1.0.13", "cross-spawn-async": "^1.0.1"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "2.0.0": {"name": "cross-spawn", "version": "2.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "32dc93907e8f80e39830aa3f0bd9f32538b3bcf1", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.0.0.tgz", "integrity": "sha512-XDtESSiBjWNXyhXz/p39MA0haYPjDxExujFPEyq4R7EB/s1Muu6OjZK7wXqlRUghzDb38nOVbom2Gh00Xy+P8A==", "signatures": [{"sig": "MEUCIB6ut/q+T7gOJiOTgfUdMhYSoMiHhHcCV9+nSaNjprxSAiEAoMWNDQvG1ak6Td0b9mHT+0cNL3JYM0uANSim8Od4nyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "32dc93907e8f80e39830aa3f0bd9f32538b3bcf1", "gitHead": "65d41138e6b5161787df43d5f8de2442765e32d0", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"spawn-sync": "^1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "expect.js": "^0.3.0"}}, "2.0.1": {"name": "cross-spawn", "version": "2.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "ab6fd893a099759d9b85220e3a64397de946b0f6", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.0.1.tgz", "integrity": "sha512-7CE7irlnaNjggf4hJ7PKo2Z67SY2paQYrXdfST5cr6Xhnyp7gO1bbWKqX2KDu93duh5i5Btfk9JcnnSvffVPnA==", "signatures": [{"sig": "MEUCIGOGhGNX4f8FlS53Tym5nFChxtS6SgxFA4dmH34d7p5vAiEAzgDhJv7xpAQ2EwpOFXJ11IVcpsiH9IPedsG2Ipn40iQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ab6fd893a099759d9b85220e3a64397de946b0f6", "gitHead": "22cae907b13de66edb5882aea6f1cb405740d283", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.13.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"spawn-sync": "1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "which": "^1.2.0", "mkdirp": "^0.5.1", "rimraf": "^2.4.4", "expect.js": "^0.3.0"}}, "2.1.0": {"name": "cross-spawn", "version": "2.1.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "9bc27f40423e98a445efe9269983e4f4055cde3a", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.0.tgz", "integrity": "sha512-PEMM5Sg35NZJp9JwlYToaVU1xenU//KI4Wuj2G5IeIEZ7SbTtTyQBMS0UKf4NRbNpF7mNwRfusOEGGfQGhXMlQ==", "signatures": [{"sig": "MEUCIQCG8y+ZBhZRtowQ9ApD/x6joHWQJ2dHMsH+bAw1xzmmkAIgYdp1wyW6iFQCsF4ZlOpGK6peiKaGkA7ThJy8i5V77d8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9bc27f40423e98a445efe9269983e4f4055cde3a", "gitHead": "f2baa6dce3606daf543666ac1e5df6e4d29ed0cc", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.13.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"spawn-sync": "1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "which": "^1.2.0", "mkdirp": "^0.5.1", "rimraf": "^1.0.9", "expect.js": "^0.3.0"}}, "2.1.1": {"name": "cross-spawn", "version": "2.1.1", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "c3b85d6a88719068b2b27be55e2974f62d41b5c0", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.1.tgz", "integrity": "sha512-aHdvccf7utqDVJmyt7rtliy2WbJIEoRGReC/vw74/ar7kIjn8sIdG2T7qxHAGz5UE2zx32lRilnmKnOpXsty2A==", "signatures": [{"sig": "MEYCIQD7pMlED5jvbwqOnFSH2ZFYt5eS5o3fmYrMruaPFn3ntQIhAM1dTrV6YVdj8gC4FkhRAXQDcDIzfLcB2TbAqc/XDroG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c3b85d6a88719068b2b27be55e2974f62d41b5c0", "gitHead": "a9b71dd09dd90b8423707b743ae9ced844a7ae21", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"spawn-sync": "1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^5.0.12", "mocha": "^2.2.5", "which": "^1.2.0", "mkdirp": "^0.5.1", "rimraf": "^1.0.9", "expect.js": "^0.3.0"}}, "2.1.2": {"name": "cross-spawn", "version": "2.1.2", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "954ea0346437918e803e03c445cb5c3287abc2af", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.2.tgz", "integrity": "sha512-D2tQ92kuG0jxLSHpJQ6pM00D88fYrjG+E8Khqc1ruGyowxYQRAjU/Uqtg4VMt6JZvem8ZZSPhzbjeFGxwRbW0g==", "signatures": [{"sig": "MEUCIEWrvWuchtB7okLWNFhipnegzSSLcB/9oMfb+U6nbSiPAiEAvKiG3e7UXNqzRiZdZgBWMR/7zyxN11LnWP8kKAdyQIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "954ea0346437918e803e03c445cb5c3287abc2af", "gitHead": "b701ac8cce3b8c21278018d0f2af60860a125c1e", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"spawn-sync": "1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^6.0.3", "mocha": "^2.2.5", "which": "^1.2.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}}, "2.1.3": {"name": "cross-spawn", "version": "2.1.3", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "6b801df157e4328b63e3d4c2c9c00488745726e8", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.3.tgz", "integrity": "sha512-QZq7hyS8kLdC5ixm1shpFTVMllwRw+ocOn0a1I2e2ulwU4Y4XJ8JGGg5iJA7VQkuO4L6H/u8YJKJZ1vSbP+x8g==", "signatures": [{"sig": "MEUCIGefVu9c8eLkgsC7duHW2MUhgSjtcRh1qSH8oTAnFNSYAiEA2e75RBXqdgggxUQbpr+KghVbrpxw4lX/TLb4NZFY4Y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6b801df157e4328b63e3d4c2c9c00488745726e8", "gitHead": "299efd1b11fe8bcc64cdb9cdf8f624b9e56e1bb9", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"spawn-sync": "1.0.13", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^6.0.3", "mocha": "^2.2.5", "which": "^1.2.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}}, "2.1.4": {"name": "cross-spawn", "version": "2.1.4", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "e64441b7a038e929ccc6e24e2aa7b72a96b26a27", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.4.tgz", "integrity": "sha512-OcnmW5gWc4kGmo7DtWwH2fTe9u6HarYv1AQReTWhLcgwCfTg6T0Y8hhv+1dZzwKdv158R3ClUqoPe46nfYwvYA==", "signatures": [{"sig": "MEQCIAC73K331MKtKuO2tHX0wmTVrdJ740KfpiCiBABoX3sRAiAJI0QoWVm5cRMAZKH9cvNiLL9pGz1D2JAjx0JYQi9bXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e64441b7a038e929ccc6e24e2aa7b72a96b26a27", "gitHead": "1831b3228a38722f431156485b01db3be6d199cc", "scripts": {"test": "node test/prepare && mocha --bail -R spec test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"spawn-sync": "^1.0.15", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^6.0.3", "mocha": "^2.2.5", "which": "^1.2.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}}, "2.1.5": {"name": "cross-spawn", "version": "2.1.5", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.1.5", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "09e1eefb7617270f4f9cad41766e7fcbd2c6ae6c", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.1.5.tgz", "integrity": "sha512-nCSxe+cClEBoKX/+k9eRb5yVm8XXnp3PcMTfRulaEMDQm2Y44U2zpiCgyFC+cXHdrH/Y7ZGGGYO5PE7kQZOSfA==", "signatures": [{"sig": "MEUCIQCLvDBRteC8YzAic3vaH1lqPQcd7ZlGMF/2ZBOj0b4pfwIgEUyaGdjuYEcI1adWWMmOHVXhAITbiMpB2icJgtZtxys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "09e1eefb7617270f4f9cad41766e7fcbd2c6ae6c", "gitHead": "a91440123d1d8ec2865cf7643351955e7ad48247", "scripts": {"test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"spawn-sync": "^1.0.15", "cross-spawn-async": "^2.0.0"}, "devDependencies": {"glob": "^6.0.3", "mocha": "^2.2.5", "which": "^1.2.4", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}}, "2.2.0": {"name": "cross-spawn", "version": "2.2.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.2.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "69a59997789571ccb64f399555a5169af79c9361", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.2.0.tgz", "integrity": "sha512-TkfmXlYupP1ROrblITMAWcR6aN3qmAzgLy8n12XLSwZChTrtcttWHRCOUkKs6WBlaVANpIZEXQDiMpXVE1xhqA==", "signatures": [{"sig": "MEQCIAd95COPDJKna3eCDbWqV8aQBsPcSqC0oz8OPBOEXpfFAiB69CzT0hgv6/Jbj1VOFdeN9+eX8zUOPvSmYCA8EErMnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "69a59997789571ccb64f399555a5169af79c9361", "gitHead": "d55c6fd837ebc9bfb64ed73b7bc9e584547d97c2", "scripts": {"test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"spawn-sync": "^1.0.15", "cross-spawn-async": "^2.2.0"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "which": "^1.2.4", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-2.2.0.tgz_1459975736466_0.5801793539430946", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.2": {"name": "cross-spawn", "version": "2.2.2", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.2.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "745cba057f65fb13daca869e4c50cdbda173b45b", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.2.2.tgz", "integrity": "sha512-V6/yu2shcwrnhPLGEXi4aFyygcLm32O/2MO/wDqEDJkpzB91lpZRxS+2hBKONg+22Kh8gYuY/O3/y/FFngoe8A==", "signatures": [{"sig": "MEYCIQCsCI63th/PtR+yxJnge8waSfSJvZgAhTGEcxdkH2aFVgIhALD/DRPhqw4hOCtJudl5U7Vwvd3FBcMWuRv6c/+SEgNE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "745cba057f65fb13daca869e4c50cdbda173b45b", "gitHead": "34bb37ef65c431891f5cc4f033b418dbb24084ae", "scripts": {"test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"spawn-sync": "^1.0.15", "cross-spawn-async": "^2.2.0"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "which": "^1.2.4", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-2.2.2.tgz_1460152414353_0.8628160380758345", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.3": {"name": "cross-spawn", "version": "2.2.3", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@2.2.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "fac56202dfd3d0dd861778f2da203bf434bb821c", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-2.2.3.tgz", "integrity": "sha512-ts2D4OzkIM+W3yYD/JUhNCHX25dWLMP+Vusl0eKssnTGYSnyXPhssdq1wUG2zr87af4bSxavZsocA9+XhrOEdQ==", "signatures": [{"sig": "MEQCICYNLMeQcblqTmVuGJUsXH0kuV3I9z0peM6qH50hEc3rAiBi9y9ydtaJy24SOo3e8NeZMzwbejiQU1bD9anHcx2YhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "fac56202dfd3d0dd861778f2da203bf434bb821c", "gitHead": "7bc71932e517c974c80f54ae9f7687c9cd25db74", "scripts": {"test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"spawn-sync": "^1.0.15", "cross-spawn-async": "^2.2.2"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "which": "^1.2.4", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-2.2.3.tgz_1460574403627_0.18620981369167566", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "cross-spawn", "version": "3.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@3.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "b86d2b005542f5716f2a3fdd437baa53ce4c6729", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-3.0.0.tgz", "integrity": "sha512-kqBx/g5KUM/XfvMx4wNz+9JEa3SXXE1biFmewn/625gnMqyl+8Hjs8b8iXCDCqpApTNaAPRkKv7oCEMtZrqrjA==", "signatures": [{"sig": "MEUCIBOd9yAcquiBqZsiSt2jzN+8E5nRVOQ0hFd1TbTyxU0CAiEAsHBXQpUWCOoivH5L3YeAnSX2ByzjLy1NID9VlPWiHrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b86d2b005542f5716f2a3fdd437baa53ce4c6729", "gitHead": "400d26217319a93393ff29d4295b211bd5650e5c", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"which": "^1.2.8", "lru-cache": "^4.0.1"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "eslint": "^2.10.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-3.0.0.tgz_1463577305117_0.17247679783031344", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.1": {"name": "cross-spawn", "version": "3.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@3.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "1256037ecb9f0c5f79e3d6ef135e30770184b982", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-3.0.1.tgz", "integrity": "sha512-eZ+m1WNhSZutOa/uRblAc9Ut5MQfukFrFMtPSm3bZCA888NmMd5AWXWdgRZ80zd+pTk1P2JrGjg9pUPTvl2PWQ==", "signatures": [{"sig": "MEQCIGGNxzkTZKtW682jJyYprvJtqJR2j4AUOrhoKP3aTgqfAiAfl3spKWNAx+LZ818d2KiscpBS2flzwFZ4YeYY6M+bNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1256037ecb9f0c5f79e3d6ef135e30770184b982", "gitHead": "24df88c465e5828b62c374e58366547bd94629db", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "eslint": "^2.10.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-3.0.1.tgz_1463607513426_0.7439773543737829", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "cross-spawn", "version": "4.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@4.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "8254774ab4786b8c5b3cf4dfba66ce563932c252", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-4.0.0.tgz", "integrity": "sha512-dAZV+Hv1PRxSUrJd9Hk9MS4gL5eEafKhrmsRlod5oHg8aP3A2FsXkga4ihfMFxlEgmMa+LS84jPKFdhk5wZwuw==", "signatures": [{"sig": "MEQCIH5i6boUDnTMdpLDgr0MbZAoAto6yYUmK49rcIRdobrmAiArVi6iri4y3Gr9R2AsX8QdxgIU+LVG5JwUTsia6S4Ryg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8254774ab4786b8c5b3cf4dfba66ce563932c252", "gitHead": "f26c67b14f1f8fc64564aca9eb5319a2dd708e6b", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^2.2.5", "eslint": "^2.10.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-4.0.0.tgz_1464295843723_0.5908118768129498", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "cross-spawn", "version": "4.0.2", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@4.0.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "7b9247621c23adfdd3856004a823cbe397424d41", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-4.0.2.tgz", "integrity": "sha512-yAXz/pA1tD8Gtg2S98Ekf/sewp3Lcp3YoFKJ4Hkp5h5yLWnKVTDU0kwjKJ8NDCYcfTLfyGkzTikst+jWypT1iA==", "signatures": [{"sig": "MEUCIDugSON3SJToSUNWFM4M4jxzxvJqi7bxerOTugu3Nn8qAiEA6dqJOny2h0Jx8tWGYWIcWgsDb77CEgHSP4fowKzSKb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "7b9247621c23adfdd3856004a823cbe397424d41", "gitHead": "674ceb2f2b69ad64b5dcad661b9bf048d6ec4c22", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1"}, "devDependencies": {"glob": "^7.0.0", "mocha": "^3.0.2", "eslint": "^3.0.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-4.0.2.tgz_1474803799646_0.017929385183379054", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.0": {"name": "cross-spawn", "version": "5.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@5.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "655bc986a2aadaa8fdbbe072331f150b8a5e35c4", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.0.0.tgz", "integrity": "sha512-7kmmKuqbXVlKYe+dXLRwYvFDPcBE4zW/Ti8ry2SfaRoH2cvNGMDaP8xZOjw30mPotuh6Ya9QyvjV0Y4qssXj0w==", "signatures": [{"sig": "MEUCIQD1uWve1+y1Vm/CFcsqlTIMNHsh6go/FSH4oECmc8mN9QIgb34mQllysBVbxVKoENdDcL8yfkD+vMRZQTu02Z2iZVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "655bc986a2aadaa8fdbbe072331f150b8a5e35c4", "gitHead": "2d22b33a3fc2300281594b7bf53a3173dd03f62f", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"glob": "^7.0.0", "once": "^1.4.0", "mocha": "^3.0.2", "eslint": "^3.0.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-5.0.0.tgz_1477845143510_0.6192892545368522", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.1": {"name": "cross-spawn", "version": "5.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@5.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "a3bbb302db2297cbea3c04edf36941f4613aa399", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.0.1.tgz", "integrity": "sha512-77q+/Kkp43OBZUppmezGBqwB1qdjGk8y1Kb6zdPaYVz8qKFRdGpL6TRLqJhlhG5RhtGkNnKaeEYCt7b/vtYteg==", "signatures": [{"sig": "MEUCICejf9faUpiE7ZV4K0dpHiK4OHyHeA94lInpthFM/+m8AiEAtPNkvcruE9yYq4Wj0WYxIm49agrCJBBbOIAbKoSn+Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "a3bbb302db2297cbea3c04edf36941f4613aa399", "gitHead": "d74d461bc4543787b955cfac5c006c86da55a4ca", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"glob": "^7.0.0", "once": "^1.4.0", "mocha": "^3.0.2", "eslint": "^3.0.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-5.0.1.tgz_1478303144933_0.1565041346475482", "host": "packages-18-east.internal.npmjs.com"}}, "5.1.0": {"name": "cross-spawn", "version": "5.1.0", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "author": {"url": "http://indigounited.com", "name": "IndigoUnited", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@5.1.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "dist": {"shasum": "e8bd0efee58fcff6f8f94510a0a554bbfa235449", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "integrity": "sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==", "signatures": [{"sig": "MEUCIQDoxwAXAIiUW+89dVk7uAGThBaxzlvrIWaf9tJTwbocfgIgZ2ylqEJD4/CVwsflL2egHbUShGF/ehBFXDCsbekphuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "e8bd0efee58fcff6f8f94510a0a554bbfa235449", "gitHead": "1da4c09ccf658079849a3d191b16e59bc600e8b4", "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/IndigoUnited/node-cross-spawn.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"which": "^1.2.9", "lru-cache": "^4.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"glob": "^7.0.0", "once": "^1.4.0", "mocha": "^3.0.2", "eslint": "^3.0.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.0", "expect.js": "^0.3.0", "@satazor/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-5.1.0.tgz_1488134324770_0.025160177145153284", "host": "packages-12-west.internal.npmjs.com"}}, "6.0.0": {"name": "cross-spawn", "version": "6.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "74af81e878f3afb8d0cba393f70a8baabed703db", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.0.tgz", "integrity": "sha512-9w15WqXp0n1rCEs02tCwdcFGAE7sIaFzVz/45nYRhIpWQCd2NnptEAXS9vQz4RL86n6yS7fmdTu/vTBj119qNA==", "signatures": [{"sig": "MEUCIHhi3XaI+koqGb3FqM10IQlbi7VkL54RmDGGKI/X7bkxAiEAtAAQUoi7z99/XUdXwE2GzyP8HX39yiiNXHDRh9m186Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "e68fbe8bfb740c04ac5c92b3ca8496e66756e30e", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.4.0", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^6.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.0.4", "eslint-config-moxy": "^4.1.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-6.0.0.tgz_1516670635828_0.7469860927667469", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "cross-spawn", "version": "6.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "5d076f26f27ed21d3fe40468bbf664657856b1be", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.1.tgz", "integrity": "sha512-/uv8xFlwhIdmv2xnhlNXd9UPo4SuxF58O6nNB9F4zrLu07io7v1yxAGu5SvIuZ2X6noMK9VlMaXwM4p+DHGsjQ==", "signatures": [{"sig": "MEYCIQDzrrWeOuF1uPS9cWBGfNXMTw4A/kqhyRYy3N8b/wkJhgIhAM3dKG7aLd3W8nVzK+IoZ8gLdLTSOQ98WdNzyp3pnxR6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "2d2440d4271789ecf418e942eee0055352de288b", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.4.0", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^6.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.0.4", "eslint-config-moxy": "^4.1.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-6.0.1.tgz_1516673494401_0.5579837518744171", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "cross-spawn", "version": "6.0.2", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "0bf5d7bc107cccad440ac712dd581819bdd06d83", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.2.tgz", "integrity": "sha512-LIYEDQq30cYVFn9/YcubtPw04LZBQD613G36qhNuH8SbgX4DSC+VFQ74mFLyLn13GT80/zLd5M5edpo8yQI+tg==", "signatures": [{"sig": "MEUCIQCD8jpPnEgFGgxeZytK0+G5MeolTUD+yllQnHpMFYWUygIgTffsEY2aG1SOKXPrrLyFYphFMwUNCgYi+oLpzbDfKrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "9cb84dbd78e72be59fe6e56cac9a95e778206145", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.4.0", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^6.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.0.4", "eslint-config-moxy": "^4.1.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-6.0.2.tgz_1516674434639_0.7491279493551701", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "cross-spawn", "version": "6.0.3", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "7cbe31768ba0f1cc37acc925bf09e7a9a4a9b0ab", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.3.tgz", "integrity": "sha512-iT128x7X4hL1dlsGRpC9t0J8gZDRYKM8N1vM+LWMMMIKlwmkAmvKKDvSFxsdIN5MaASVnJYhApVI+ld+Ea38JQ==", "signatures": [{"sig": "MEQCIA3rUoLHPChrV7WiSRXvr5dy1vkcyXs46c3j96MgROk+AiAodgsZ/gvLAwPghIeiWYI6kR0pV6TlywM3skCJMICh8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "334d705ea39cd1de3c73a57db81b6d32a9a85a43", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.4.0", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^6.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.0.4", "eslint-config-moxy": "^4.1.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-6.0.3.tgz_1516676237994_0.8944167380686849", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "cross-spawn", "version": "6.0.4", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "bbf44ccb30fb8314a08f178b62290c669c36d808", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.4.tgz", "integrity": "sha512-LDYnK41m8td+nBTk5Jmn55aGVP18iYuUqoM1X3u+ptt7M/g9FPS8C38PNoJTMfjoNx4fmiwWToPpiZklGRLbIA==", "signatures": [{"sig": "MEUCIQCbUTXpuDwSi4ertyYrLFwZBdY29RkzR/7PCZO8DH67FAIgD1v2/XjZ0MkqTC/l3fSkN/hE2vxKTAxHGaytFkaLha8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "52e557e5b77bd0ca701814e400e53e89ca51a650", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.4.0", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^6.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.0.4", "eslint-config-moxy": "^4.1.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn-6.0.4.tgz_1517374181817_0.39061956107616425", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "cross-spawn", "version": "6.0.5", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@6.0.5", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "4a5ec7c64dfae22c3a14124dbacdee846d80cbc4", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz", "fileCount": 10, "integrity": "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==", "signatures": [{"sig": "MEUCIQCzs4eQ5rnpA0MHfr5l47ceUDZ2I9sXt2gBOTyKfjlPmQIgEC3maA2aUF66m3HDzODuG0bPeyNv/sbPrgVqNb1Epes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21397}, "main": "index.js", "files": ["lib"], "engines": {"node": ">=4.8"}, "gitHead": "301187a05b7509aa1d6ff35d8ff6d6064f597bc9", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "commitmsg": "commitlint -e $GIT_PARAMS", "precommit": "lint-staged", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "9.6.1", "dependencies": {"which": "^1.2.9", "semver": "^5.5.0", "nice-try": "^1.0.4", "path-key": "^2.0.1", "shebang-command": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.0.0", "husky": "^0.14.3", "eslint": "^4.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "lint-staged": "^7.0.0", "@commitlint/cli": "^6.0.0", "standard-version": "^4.2.0", "babel-preset-moxy": "^2.2.1", "eslint-config-moxy": "^5.0.0", "regenerator-runtime": "^0.11.1", "@commitlint/config-conventional": "^6.0.2"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_6.0.5_1520032526727_0.6098816324025185", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "cross-spawn", "version": "7.0.0", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.0", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "21ef9470443262f33dba80b2705a91db959b2e03", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.0.tgz", "fileCount": 10, "integrity": "sha512-6U/8SMK2FBNnB21oQ4+6Nsodxanw1gTkntYA2zBdkFYFu3ZDx65P2ONEXGSvob/QS6REjVHQ9zxzdOafwFdstw==", "signatures": [{"sig": "MEUCIHUF4PZ86dNvvT+to+EqPA7aW2bOyIhNLhGTDrB88wdzAiEAoIntFswtxSVfR9JbC6rrIxAM1kA1k/DQHqpl2I19um0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdblJ4CRA9TVsSAnZWagAAFL8P/AiiSHDKBIyI1nhWkZTH\n7KThXJ409r5T1VMivrUDq1Hcau/XAcLfyEBPCmZtoGVJzNpFhyAu793brK38\nfoQNZuAWfnOlYB3bnSOsEwXorXAlHuIAehsMfR7a6HGcGuJSnvUdVJlDvepC\nP8h4XBpH+5EN5bG1U0cCAZ65zT7bIFty0cqrOtsXxXDDvFC2+8a6i+2xNK6L\nRuY5jkiGa6DiE8uykqMiaB0JqWglE39PsX3VeWnqX1mxZ+q3ZIxIiRUvtuSL\ndL2L8h8pCteNA0xG2KAb4o5HYtLcfEcz0fQitCygwoIMEpzfspttYCPKGS9s\nev0YEHKJidLlFgZJPj3bCCsyEbUnblK0qLYTLMG3APm4dO0G2y5DUFsXGmw8\nvCo4RsKm3CVpDTxeEV4vS1/NflOW+6mlRZ9hDLuMbC6D3NyqOHvXCD9hjPXx\neTiQcrbdA1neDXb33RgK5tHeWqirApwGZU+nM62EhZQWdLYBInLc/wABa3yR\nqzJh1B+50PKGfyAyWKgqtu8y/TjTh1mOCa9ssorjDIc+giDqsSpqrDHUAH3+\nDCu1/Y5mwV7MK6+vLZIydAlGhuHvywl0v3OR+CtEtGBSC1iMam9+2Lp7KY5n\nK/mMvs04bl6E2qAwP6BKwdxKcvINj9frFOIZxYJfLF5EH/NOp37LgFvotyxo\n9UpF\r\n=lEsz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "0e7cd3d6c506251fdeb1513102adbd5e84c178a5", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "10.16.0", "dependencies": {"which": "^1.2.9", "path-key": "^3.1.0", "shebang-command": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.0_1567511159454_0.21019404432697186", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "cross-spawn", "version": "7.0.1", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.1", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "0ab56286e0f7c24e153d04cc2aa027e43a9a5d14", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.1.tgz", "fileCount": 10, "integrity": "sha512-u7v4o84SwFpD32Z8IIcPZ6z1/ie24O6RU3RbtL5Y316l3KuHVPx9ItBgWQ6VlfAFnRnTtMUrsQ9MUUTuEZjogg==", "signatures": [{"sig": "MEYCIQCGIxpnPtDkLJwoXa8Cgm+RpVuHx/VUi/dRaOsl/AFgbwIhAMmOuEzy3FT5ahwlYGNHC5FkSKa/CILKpfkiKls+F4BC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmwLACRA9TVsSAnZWagAAc84P/005u2BhtQcPvRJcu9tX\nYiQlgIhHGVBxy/hf3igpxwECCRGGNFUQfR68UivSF5fn90NEwXQ+jKLEyirq\n4MGZwtphgNJhbr8D0SsyRAU0iab5l2z3WIOqDPWPTgJTTH4eiKZH0HLaUUqK\nzdfojdLe3jH5hRFoZO+eSS113Z8uBTQsnrXPHg48OXf1o6GfXJhr18lbojlJ\n/LtLolkjnsIRKpArYh8a9HRkSV52kSqsA0wPMRj9zG6V8vIqiTj6WEouhy+B\nExxLXmAKUSQFwFARRupiANfImda81GfoTapKztdlhuKb224gtKxT2fZII9Ps\nyjhDlGKn/AINjcl/s2K+K+zBkOm5QmIkKI0dv2AV9W5N5Ya5GMxWvd9XGGmZ\nVnlKcUw4gd/YIDIo6QqMbsMd5r9bc40tcGBE5RTnJkYaLYJEKAyNZWWBvFyW\nySFKrvSHOSAq3e2+Ysrmm+A4nzCeovUhbrUKvzMe431kjzXfkO07l9Mgb25h\nmzYECBknrU54AYUWABIrTpw8xdeRMml6D+j8I9EXzYE16S9uyCJcswVg2nTv\nGqZCmJJ3QiSfXWpoWJ7oSrdMewRQv2hBNqTRyK1bAtbfcc0w3SXGnjir1M29\nBVRmeVaxa6aEbQTp94B4uf786fSWrvKUCKENwl+tmH03IYqqKx4zPPR/uxK9\nxyCF\r\n=c21f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "aa7f227d0996748bd6c39940a6f4124c99c5e181", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "12.11.1", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master"}}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.1_1570439871357_0.40650321888599317", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "cross-spawn", "version": "7.0.2", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.2", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "d0d7dcfa74e89115c7619f4f721a94e1fdb716d6", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.2.tgz", "fileCount": 10, "integrity": "sha512-PD6G8QG3S4FK/XCGFbEQrDqO2AnMMsy0meR7lerlIOHAAbkuavGU/pOqprrlvfTNjvowivTeBsjebAL0NSoMxw==", "signatures": [{"sig": "MEYCIQCEZJYiAoanT3d7/Q+SN9XhcyGhwf0luQ7BWsKu2QwMygIhAJwk6rf0Tn4TJCStnOTwnNZPlsHGmWgxessSNjuypqDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiFndCRA9TVsSAnZWagAA70MP/RrUpLUr8AUUq+TJY3kE\nBJKfGpPVrATBgdKcCJCbcU69nBH5kXjKH91FyGWoj+3MKLeWAmgyL0TKwFhw\na5HB0Gz6pOT9/itgL7sjBLGm+GwP28tnScG688yMDNFTlAD5FnTyyAReH/sz\ncOyWbaszhi0jpnUJIVcBKXyi0J3sRiRfEMQ48mK8Hmh9DcOx+NAmk8qSj+Mt\nvR3gs1+Sr/htU+DsP9/tQGWzj4FEQlTFMvMr4Ct9W2wrlG9JgrcY92ySwLYm\nOnXNg6ddL2cCwyyhmPG9SYbWW6o8ruBv4T9s9MNtpUFzIiV2+ml2uEEXCBRP\nOzNWTI5uJHcDSx43tmu8mSGpZb+/ava23uj7NjTEZJ/3KqscSdVLkdn7LXQH\n0VFR8gDDy+qBGc5eIZMLyqDmMxuh6Hehhcbooeo+tzG0efRWss/YmN2o16js\npqtNox3NA4cVFqd+O0hHsqV8ZjfJk2y9RVBmrG0VBXaX3qNV2NB8OI1HFT1e\n94FdvAvEClI86RSAvpFkOVhLE0l1uDw0f7WqlpCh6Ol2/C9Wc/Q3c/Kq/6SS\ndDVgbW1Snlf8967kQSugYjqYfV8v+EenpxfB5wjheAn85zNRGd645bLNT9hL\nwz8Td70iKJ7kbDs9O37KA1RD1yfz5QR9rNR8JjX7+waP+BCRuyJxDdik6nVa\nPVv/\r\n=j8hc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "7501971226a7c8b2cf0c1acd1703547e3798491b", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "12.16.1", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.2_1585994204651_0.3715296155704044", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "cross-spawn", "version": "7.0.3", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.3", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "f73a85b9d5d41d045551c177e2882d4ac85728a6", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "fileCount": 10, "integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==", "signatures": [{"sig": "MEYCIQDV3uyoAawrXTO40sTQ71+tY4UBLGXyIxxzykg9MOSZRgIhAOXE5g5AY0aGrmKohMbsOiiiWUWew3zD5tGO9e/D6VNJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJey+WtCRA9TVsSAnZWagAAQbcP/1FGxwkPr+a5gx/ntrWD\n5YJdj1PVM6QsC2wUtn8CwW8GQVt5S4jMEQ4ZWbhBPgsHKyvfC4O5CxwGa7JR\n/ONvY8hrfjeXZZ7ePL0u683K05+wBC8n9anUd/RROEbF/0Ptia7pWyiKGEpG\nTh6btdUzKIijKtXrANxXe/U7dXOtFtg3sEtKKhFGB0EFl2x6vQJi3FlCasfT\nTrL4kCVcGiLFVDFLPgM18iRDG83obqsi0fnujL/EzLSGt1ldBm0aTNyIUWkt\nDdcvuWYTDAH6yCEB/kDIUfBTy94p2Vc5yp76Rp5vMCbPPBWEHszGbHcGqhkK\n0BFTs/e5dzhCDZ0i83YCwyTBP0bF+Jzum4/lckN2m5VYeeWAbfvB5G2jmUAv\nlTXNyPsg03ycX3p6OvSxbdpSzea/SZVYybr59gHHBkL4JgoFo2ypFs0ZVJfw\ncG4A8LF4z6V2DbLVviLUVsQ6KQqKaCTLxMLcfwd59AHVUrGqtzB6EzqYfyl5\n9oYQgZF78WjXTezST2e7cf14ITfIv7voJ5ci9xVh27L0wvkSvJX4SENyn9Nr\ngIvfeSSJndO76mhB0vZ6ArW6S9BV/FspGpZm5zASk+i9tj+ppy19zbNWhm/5\ncvN8TJiui80niDmnM6mHXHrC3mZiIBgFJHp9NoepmUG03GG9lmLCab3CkGu6\nhqhR\r\n=lXVa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "7bc42bc409d9da6ad691df8d1d5ef69003bf1dc3", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "12.16.3", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.3_1590420907115_0.7923431028970156", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "cross-spawn", "version": "7.0.4", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.4", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "36d9cb36c32ae7a0df935f0191f79959962a2165", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.4.tgz", "fileCount": 9, "integrity": "sha512-9<PERSON><PERSON>VPPtLHjPAD7tcuzSFs64UfHlLJt7U6qP4/bFVLyjLceyizj6s6jO6YBaV5d0G7g/9KnY/dOpLR4Rcg8YDg==", "signatures": [{"sig": "MEUCIQCedg4/NcgLqyF1egHMkaxI7cQ73fgPUO6T0wJ02zBpngIgW7RP8NI2X+IpUT1+WWGefIypE3P5lNk5eaUiHdXAwgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16695}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "9b9246e0969e86656d7ccd527716bc3c18842a19", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "20.13.1", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.4_1730976615630_0.07680647525535256", "host": "s3://npm-registry-packages"}}, "7.0.5": {"name": "cross-spawn", "version": "7.0.5", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.5", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "910aac880ff5243da96b728bc6521a5f6c2f2f82", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.5.tgz", "fileCount": 9, "integrity": "sha512-ZVJrKKYunU38/76t0RMOulHOnUcbU9GbpWKAOZ0mhjr7CX6FVrH+4FrAapSOekrgFQ3f/8gwMEuIft0aKq6Hug==", "signatures": [{"sig": "MEUCIQCmzE3Ma1tXcUTB39atFDTxEjWOec2PIrAVnFgWc32stwIgTaw4E38H6R9hX7t89c2ZQu6RfYNjrQyjNdZpaNPV5CA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16089}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "085268352dcbcad8064c64c5efb25268b4023184", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "20.13.1", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.5_1730984427297_0.04158949037392223", "host": "s3://npm-registry-packages"}}, "7.0.6": {"name": "cross-spawn", "version": "7.0.6", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-spawn@7.0.6", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "homepage": "https://github.com/moxystudio/node-cross-spawn", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "dist": {"shasum": "8a58fe78f00dcd70c370451759dfbfaf03e8ee9f", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "fileCount": 9, "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "signatures": [{"sig": "MEUCIQCdXEXhFHQR/UhPwcpnO2Gp3L2/lh5roLTkR5Ozu5uwfQIgHOjOf19lt7Lxdfxw8kjmcjbwtpwAMrLNvYJqvlfkVss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16094}, "main": "index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "engines": {"node": ">= 8"}, "gitHead": "77cd97f3ca7b62c904a63a698fc4a79bf41977d0", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "prerelease": "npm t && npm run lint", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Cross platform child_process#spawn and child_process#spawnSync", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "20.13.1", "dependencies": {"which": "^2.0.1", "path-key": "^3.1.0", "shebang-command": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "lint-staged": "^9.2.5", "@commitlint/cli": "^8.1.0", "standard-version": "^9.5.0", "babel-preset-moxy": "^3.1.0", "eslint-config-moxy": "^7.1.0", "@commitlint/config-conventional": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-spawn_7.0.6_1731938391951_0.24409455021062265", "host": "s3://npm-registry-packages"}}, "6.0.6": {"name": "cross-spawn", "version": "6.0.6", "description": "Cross platform child_process#spawn and child_process#spawnSync", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/moxystudio/node-cross-spawn", "repository": {"type": "git", "url": "git+ssh://**************/moxystudio/node-cross-spawn.git"}, "license": "MIT", "main": "index.js", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "release": "standard-version", "precommit": "lint-staged", "commitmsg": "commitlint -e $GIT_PARAMS"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin v6"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "devDependencies": {"@commitlint/cli": "^6.0.0", "@commitlint/config-conventional": "^6.0.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "babel-preset-moxy": "^2.2.1", "eslint": "^4.3.0", "eslint-config-moxy": "^5.0.0", "husky": "^0.14.3", "jest": "^22.0.0", "lint-staged": "^7.0.0", "mkdirp": "^0.5.1", "regenerator-runtime": "^0.11.1", "rimraf": "^2.6.2", "standard-version": "^4.2.0"}, "engines": {"node": ">=4.8"}, "_id": "cross-spawn@6.0.6", "readmeFilename": "README.md", "gitHead": "d35c865b877d2f9ded7c1ed87521c2fdb689c8dd", "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "_nodeVersion": "20.13.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==", "shasum": "30d0efa0712ddb7eb5a76e1e8721bffafa6b5d57", "tarball": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.6.tgz", "fileCount": 9, "unpackedSize": 18317, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrz4w5vrADmpw9q+NhRU/KxG/Vw9TdDeL8XP5pCnYPsQIgRea9VZV05uFEnD3nhEeMF9IYVJ2tkaFUIzFSdrAbkbA="}]}, "_npmUser": {"name": "satazor", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cross-spawn_6.0.6_1731939694654_0.21490256190668"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-06-30T01:04:42.962Z", "modified": "2024-11-18T14:21:35.065Z", "0.1.0": "2014-06-30T01:04:42.962Z", "0.1.1": "2014-06-30T13:22:52.138Z", "0.1.2": "2014-06-30T21:29:40.550Z", "0.1.3": "2014-06-30T21:49:21.933Z", "0.1.4": "2014-06-30T23:25:24.890Z", "0.1.5": "2014-07-02T11:30:53.833Z", "0.1.6": "2014-07-03T08:47:30.074Z", "0.1.7": "2014-07-11T16:28:05.858Z", "0.2.0": "2014-08-28T22:41:05.210Z", "0.2.1": "2014-08-28T22:50:35.426Z", "0.2.2": "2014-08-28T22:59:55.849Z", "0.2.3": "2014-08-29T08:12:24.369Z", "0.2.4": "2015-02-08T20:34:37.954Z", "0.2.5": "2015-02-08T20:35:35.977Z", "0.2.6": "2015-02-08T20:58:31.475Z", "0.2.7": "2015-03-28T00:03:44.626Z", "0.2.8": "2015-03-28T00:05:26.072Z", "0.2.9": "2015-04-08T16:18:38.840Z", "0.3.0": "2015-05-06T08:02:28.625Z", "0.4.0": "2015-05-06T22:21:11.441Z", "0.4.1": "2015-06-10T15:11:37.696Z", "1.0.0": "2015-07-02T19:01:47.896Z", "1.0.1": "2015-07-02T19:10:06.117Z", "1.0.2": "2015-07-02T20:15:01.537Z", "1.0.3": "2015-07-02T20:21:46.368Z", "1.0.4": "2015-07-16T16:57:22.820Z", "2.0.0": "2015-07-21T22:25:49.585Z", "2.0.1": "2015-11-29T17:30:31.442Z", "2.1.0": "2015-12-06T15:26:20.377Z", "2.1.1": "2016-01-02T09:57:15.369Z", "2.1.2": "2016-01-02T14:50:05.176Z", "2.1.3": "2016-01-02T15:27:58.166Z", "2.1.4": "2016-01-03T15:37:28.977Z", "2.1.5": "2016-01-27T01:15:02.454Z", "2.2.0": "2016-04-06T20:48:59.136Z", "2.2.2": "2016-04-08T21:53:36.565Z", "2.2.3": "2016-04-13T19:06:45.979Z", "3.0.0": "2016-05-18T13:15:07.856Z", "3.0.1": "2016-05-18T21:38:35.770Z", "4.0.0": "2016-05-26T20:50:45.929Z", "4.0.2": "2016-09-25T11:43:21.493Z", "5.0.0": "2016-10-30T16:32:25.720Z", "5.0.1": "2016-11-04T23:45:45.490Z", "5.1.0": "2017-02-26T18:38:46.418Z", "6.0.0": "2018-01-23T01:23:56.751Z", "6.0.1": "2018-01-23T02:11:35.977Z", "6.0.2": "2018-01-23T02:27:14.869Z", "6.0.3": "2018-01-23T02:57:18.310Z", "6.0.4": "2018-01-31T04:49:42.865Z", "6.0.5": "2018-03-02T23:15:27.209Z", "7.0.0": "2019-09-03T11:45:59.588Z", "7.0.1": "2019-10-07T09:17:51.547Z", "7.0.2": "2020-04-04T09:56:44.815Z", "7.0.3": "2020-05-25T15:35:07.209Z", "7.0.4": "2024-11-07T10:50:15.781Z", "7.0.5": "2024-11-07T13:00:27.448Z", "7.0.6": "2024-11-18T13:59:52.129Z", "6.0.6": "2024-11-18T14:21:34.889Z"}, "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/moxystudio/node-cross-spawn", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "repository": {"url": "git+ssh://**************/moxystudio/node-cross-spawn.git", "type": "git"}, "description": "Cross platform child_process#spawn and child_process#spawnSync", "maintainers": [{"name": "satazor", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"xch": true, "kael": true, "mgol": true, "binki": true, "carsy": true, "panlw": true, "pftom": true, "razr9": true, "d-band": true, "daizch": true, "garthk": true, "maddas": true, "monjer": true, "nubuck": true, "pandao": true, "thorn0": true, "yeming": true, "akabeko": true, "itonyyo": true, "morganz": true, "nichoth": true, "crafterm": true, "fakefarm": true, "losymear": true, "ssljivic": true, "stanlous": true, "xiaochao": true, "antixrist": true, "codepanda": true, "madsummer": true, "maoxiaoke": true, "qqcome110": true, "sternelee": true, "whitelynx": true, "giussa_dan": true, "latinosoft": true, "leonardorb": true, "lichangwei": true, "manikantag": true, "mysticatea": true, "flumpus-dev": true, "kodekracker": true, "phoenix-xsy": true, "iori20091101": true, "newworldcode": true, "tommytroylin": true, "about_hiroppy": true, "ferchoriverar": true, "scottfreecode": true, "hyokosdeveloper": true}}