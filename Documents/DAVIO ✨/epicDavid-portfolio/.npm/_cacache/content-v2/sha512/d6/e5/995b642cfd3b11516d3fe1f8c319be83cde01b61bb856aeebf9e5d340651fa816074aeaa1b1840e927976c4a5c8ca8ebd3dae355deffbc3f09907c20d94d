{"_id": "type-detect", "_rev": "39-aa765bcb794ba0a0d205cece1f347bfe", "name": "type-detect", "dist-tags": {"latest": "4.1.0"}, "versions": {"0.1.0": {"name": "type-detect", "version": "0.1.0", "keywords": [], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@0.1.0", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "81ed3ab764cd5139388b67d052eb01610edc1a57", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-0.1.0.tgz", "integrity": "sha512-04byXXEAw57C1oUNTJ79G2I9NFvgOiMCQpl8CLuFxb32p2hwAh7Hkip2LzxJ0vYI2QEhdE39nlFyPEVEn0s+Zg==", "signatures": [{"sig": "MEYCIQDaFHyyZHdE3T6lsNwEEv6Sor8qCon1nVI6uurkL/aUUgIhANTca5AyX1kbAYdjqhal1j9roTEoIc1r1nHbNOmsQBLS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/type-detect.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "simple-assert": "*", "mocha-phantomjs": "*", "mocha-lcov-reporter": "0.0.1"}}, "0.1.1": {"name": "type-detect", "version": "0.1.1", "keywords": [], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@0.1.1", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "0ba5ec2a885640e470ea4e8505971900dac58822", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-0.1.1.tgz", "integrity": "sha512-5rqszGVwYgBoDkIm2oUtvkfZMQ0vk29iDMU0W2qCa3rG0vPDNczCMT4hV/bLBgLg8k8ri6+u3Zbt+S/14eMzlA==", "signatures": [{"sig": "MEUCIQDmsCpVyycrOBMZc1AVOorVflolZYk45itDLz1SXKtDQwIgSAI9LI+ZCGGaFoaCcM3U2LjmpskHxv975Rdg3r8TbJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/type-detect.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "simple-assert": "*", "mocha-phantomjs": "*", "mocha-lcov-reporter": "0.0.1"}}, "0.1.2": {"name": "type-detect", "version": "0.1.2", "keywords": [], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@0.1.2", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/chaijs/type-detect", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "c88e853e54e5abd88f1bf3194b477c853c94f854", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-0.1.2.tgz", "integrity": "sha512-lR9v7XFtwvnGHYwutLHzWTItsWN4r8OvAPSYrGf8F8Wn3eQXxMIjfQrLe8vq5s4MGDOTUot/d4NSnuyIfhCEIg==", "signatures": [{"sig": "MEUCIQCQKucmrDh0OPC8Zf/G+iswduJW1EmOcLWx7exEZJSA1QIgfDItvQb97xINNIXDQ6U3FzpaVbceEOgLHN5y5+mxSlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/type-detect.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "simple-assert": "*", "mocha-phantomjs": "*", "mocha-lcov-reporter": "0.0.1"}}, "1.0.0": {"name": "type-detect", "version": "1.0.0", "keywords": [], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@1.0.0", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/chaijs/type-detect", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "762217cc06db258ec48908a1298e8b95121e8ea2", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-1.0.0.tgz", "integrity": "sha512-f9Uv6ezcpvCQjJU0Zqbg+65qdcszv3qUQsZfjdRbWiZ7AMenrX1u0lNk9EoWWX6e1F+NULyg27mtdeZ5WhpljA==", "signatures": [{"sig": "MEYCIQCuJC2oCa8Mrbu6Ycj1OXEFy6OAhJbE1kMbfRqGXiSUowIhAIddul+IyhCBpsEME88N4yT7ehx8iSHiqIzzHfRM0Jd5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_shasum": "762217cc06db258ec48908a1298e8b95121e8ea2", "engines": {"node": "*"}, "gitHead": "3617862cbaa220d3700cfc9e96c6d3feb49e9587", "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/type-detect.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.11.16", "dependencies": {}, "devDependencies": {"mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "simple-assert": "*", "mocha-phantomjs": "*", "mocha-lcov-reporter": "0.0.1"}}, "2.0.0": {"name": "type-detect", "version": "2.0.0", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@2.0.0", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}, {"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "b5a567196997808f0b94d7a9e089d4585266e3b8", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-2.0.0.tgz", "integrity": "sha512-TN6MqeOULV6IU1X//vz8moHAXP/XcOAt1Ufib2ewTMWEvbblDF65xpRMFBBv19YBkJZOZB5dxuGW7wjYyW5CxQ==", "signatures": [{"sig": "MEUCICqtMJ5k0W0LnHkN9Qd8BJS7b4OhRmSMQysYXFh6u3SrAiEA94WF72dzRzjxgfzPZjPGLIW+Tki2IWLLh1a0nZdw094=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "b5a567196997808f0b94d7a9e089d4585266e3b8", "engines": {"node": "*"}, "gitHead": "695961ee444301af6a8fe1468a8d56d467fd6c1d", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.43", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^0.13.22", "mocha": "^2.4.5", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-2.0.0.tgz_1457897453208_0.4966612495481968", "host": "packages-13-west.internal.npmjs.com"}}, "2.0.1": {"name": "type-detect", "version": "2.0.1", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@2.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "42d3ff2358743068a26ca3715e941c4e65236507", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-2.0.1.tgz", "integrity": "sha512-TsImIN7r+MIwD0nbSbSlGdkTfTv2/lPVQ1liZtViWf2wk9BtU/FhxEdWzzWbnJgRhsnC72o1QUP92toISpxHTQ==", "signatures": [{"sig": "MEUCIFKwKezFm15DafFT6maZtqhCatdvH0cHBysn3QYRQ0SwAiEAquuWsPeyCXdbi8lAulLhP4ogQ880nxib4ZiD0vN4i80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "42d3ff2358743068a26ca3715e941c4e65236507", "engines": {"node": "*"}, "gitHead": "0da5088dd181ec2e3293ab8bd681f6d6aec79351", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "3.9.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.45", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^0.13.22", "mocha": "^2.4.5", "eslint": "^2.9.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.9", "browserify": "^13.0.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-2.0.1.tgz_1463402182708_0.3107253033667803", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.2": {"name": "type-detect", "version": "2.0.2", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@2.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "356ef98aa8fed1bba5f732ab55bf1bf3641b9228", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-2.0.2.tgz", "integrity": "sha512-dUsimd51jZvqVIH4mfGgKmoQ+jJ7oAUtZcW6zbEnQKlzqcYDc5P/CCDRxWJb1PuIl5x0hvzPY15t3hRn2ccdNw==", "signatures": [{"sig": "MEYCIQCCKJCk6HNeSNJe6U4SE21RQSkFBTtUiaA54rFmJkXHhwIhAJds9eZdPNUntG6SapBpkg+giitJZMZZli+vB71Jrc8A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "356ef98aa8fed1bba5f732ab55bf1bf3641b9228", "engines": {"node": "*"}, "gitHead": "70ef308d2b5c10a8837666eee7a021cca37136b8", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.46", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^1.1.2", "mocha": "^2.4.5", "eslint": "^2.9.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.9", "browserify": "^13.0.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-2.0.2.tgz_1469648010851_0.2907430832274258", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "type-detect", "version": "3.0.0", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@3.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "46d0cc8553abb7b13a352b0d6dea2fd58f2d9b55", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-3.0.0.tgz", "integrity": "sha512-pwZo7l1T0a8wmTMDc4FtXuHseRaqa9nyaUArp4xHaBMUlRzr72PvgF6ouXIIj5rjbVWqo8pZu6vw74jDKg4Dvw==", "signatures": [{"sig": "MEYCIQCIulfOAtcUuk532dvxnGmBYRyVwBdgWFktd62vrzxTvgIhAK2INEYhbbFQRn/mQhdsUYiHveZ/oBez1RPC/2C9jJ8k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "46d0cc8553abb7b13a352b0d6dea2fd58f2d9b55", "engines": {"node": "*"}, "gitHead": "638c09a846963343f4c070438a691300f3979824", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.47", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^1.1.2", "mocha": "^3.0.0", "eslint": "^2.9.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.9", "browserify": "^13.0.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-3.0.0.tgz_1475970610718_0.026009620632976294", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "type-detect", "version": "4.0.0", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "62053883542a321f2f7b25746dc696478b18ff6b", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.0.tgz", "integrity": "sha512-UTSAaPPKiJxpv3pB1JqXRGFCjn8trL/Rh9oyPdgWEAgRMle0X0bcVEdb5Go1e/pJ1XOXZv8cHilOA48nsCsuag==", "signatures": [{"sig": "MEYCIQClM4Fgx10wkhhP2yxExB17YjowjlVX9XHDa5C8BTFkOAIhAPZ3rUeEZdDbYYqdGZLIm598q3DuN2vDh7aHYnXnsz+d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "62053883542a321f2f7b25746dc696478b18ff6b", "engines": {"node": "*"}, "gitHead": "ec1ad393dd67605067bbe11044d27684f3afdd48", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.47", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^1.1.2", "mocha": "^3.0.0", "eslint": "^2.9.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.9", "browserify": "^13.0.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.0.tgz_1476108922431_0.6963035347871482", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "type-detect", "version": "4.0.1", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "c2473c08dc6f975232ca7e3317570f187bf7f3e2", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.1.tgz", "integrity": "sha512-Tzjv8aSG3Y1+A9ngGlUIdoiqkX8wanmnClk/6f0o3lQk6oZgi/WiiEqWvFYNV3Upwkv70CoiyoVQ6eIuU2NG0g==", "signatures": [{"sig": "MEYCIQCbpyYiz1yfx0aSwY27AdKnc98YdHhu0Tvczh8YPc2FrwIhAMWrcWOoEFCBVBCbVyv3YvDNxhr62r4AllTpiizHh6xr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "c2473c08dc6f975232ca7e3317570f187bf7f3e2", "engines": {"node": "*"}, "gitHead": "a90cf2a7d590294c1a0075547d49860d06972e20", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"karma": "^1.1.2", "mocha": "^3.0.0", "eslint": "^2.9.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.11.9", "browserify": "^13.0.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.1.tgz_1492493084610_0.5174380470998585", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.2": {"name": "type-detect", "version": "4.0.2", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "60e134a1b49bde16c08ce265e596059351d74852", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.2.tgz", "integrity": "sha512-eNKi6nnbMK0TsfxLNb/o+jYJcN8yTTunn+A/UuVo8i46gN+FVAQ1CRtU7VhvQ6XYACHmaJj/olJQlQHI9UMRbQ==", "signatures": [{"sig": "MEUCIQCeKTDxFel6K4tcitU+uVIkO7bpI8LpP+Dc/aUdwcZ31QIgJwJF+5ZMN4aIcFhFUBhzoPiHIg0tG9DDFeuHf5sL9r4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "_shasum": "60e134a1b49bde16c08ce265e596059351d74852", "engines": {"node": "*"}, "gitHead": "f63b62b2dc7d255c1fee3a67d271387c32567d0a", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "commit-msg": "validate-commit-msg", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "4.8.2", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"husky": "^0.13.3", "karma": "^1.1.2", "mocha": "^3.0.0", "eslint": "^3.19.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.13.0", "browserify": "^14.3.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^6.3.2", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^2.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^13.0.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^1.1.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.2.tgz_1492648487664_0.48155939276330173", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.3": {"name": "type-detect", "version": "4.0.3", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "0e3f2670b44099b0b46c284d136a7ef49c74c2ea", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.3.tgz", "integrity": "sha512-VPPr4dSATjaRY6+8IEKzRj9cPXioQmOkYvw77CzZBm+9bxXJbTFt40DeJybc6CPU7M32R9ZLeM+3leSkn2lc3Q==", "signatures": [{"sig": "MEUCIQCWSGXUUFn/vg1DSDu/q0nKG02Nuxc0XZ4qrL5+jB0tFAIgOLNTj9WnjM1zoipZCHuj0+vBZjHnqvsOIBr0pkmlrfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "files": ["index.js", "type-detect.js"], "_shasum": "0e3f2670b44099b0b46c284d136a7ef49c74c2ea", "engines": {"node": "*"}, "gitHead": "c7895e499ecc0a93c567adef9fa2b33eec13d2ab", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "bench": "node bench", "build": "browserify --bare $npm_pakcage_main --standalone typeDetect -o type-detect.js", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "commit-msg": "validate-commit-msg", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "4.8.2", "dependencies": {}, "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0}, "extends": ["strict/es5"], "globals": {"HTMLElement": false}}, "devDependencies": {"husky": "^0.13.3", "karma": "^1.1.2", "mocha": "^3.0.0", "eslint": "^3.19.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "2.13.0", "browserify": "^14.3.0", "karma-mocha": "^1.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^1.0.0", "karma-browserify": "^5.0.2", "semantic-release": "^6.3.2", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.2.0", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^2.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^13.0.0", "karma-sauce-launcher": "^1.0.0", "eslint-plugin-filenames": "^1.1.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.3.tgz_1492659793798_0.8881021328270435", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.5": {"name": "type-detect", "version": "4.0.5", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.5", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "d70e5bc81db6de2a381bcaca0c6e0cbdc7635de2", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.5.tgz", "integrity": "sha512-N9IvkQslUGYGC24RkJk1ba99foK6TkwC2FHAEBlQFBP0RxQZS8ZpJuAZcwiY/w9ZJHFQb1aOXBI60OdxhTrwEQ==", "signatures": [{"sig": "MEUCIFCQ1f7T10emkL33QfzIS92j+jz5SaSpbfJD9x+P7ymBAiEA8LGNRtoRxQfa4vn+nZnxg7JNjV8X74sTkXeDxHJEv4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./type-detect.js", "files": ["index.js", "type-detect.js"], "engines": {"node": ">=4"}, "gitHead": "94cd6a29a4fa6b30bca5086037eb7bea4d37b4ed", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "rollup -c rollup.conf.js", "prepare": "cross-env NODE_ENV=production npm run build", "test:node": "nyc mocha type-detect.test.js", "commit-msg": "commitlint -x angular", "pretest:node": "cross-env NODE_ENV=test npm run build", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "cross-env NODE_ENV=test npm run build", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "8.9.1", "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}, "extends": ["strict/es6"], "globals": {"HTMLElement": false}}, "devDependencies": {"nyc": "^11.3.0", "buble": "^0.16.0", "husky": "^0.14.3", "karma": "^1.7.1", "mocha": "^4.0.1", "eslint": "^4.10.0", "rollup": "^0.50.0", "codecov": "^3.0.0", "benchmark": "^2.1.0", "cross-env": "^5.1.1", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^1.1.1", "@commitlint/cli": "^4.2.2", "semantic-release": "^8.2.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-buble": "^0.16.0", "eslint-config-strict": "^14.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-istanbul": "^1.1.0", "eslint-plugin-filenames": "^1.2.0", "commitlint-config-angular": "^4.2.1", "rollup-plugin-node-resolve": "^3.0.0", "karma-safaritechpreview-launcher": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.5.tgz_1510235367994_0.8465239650104195", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "type-detect", "version": "4.0.6", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.6", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/shvaikalesh", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lucasfcosta", "name": "<PERSON>"}, {"url": "https://github.com/meeber", "name": "<PERSON>"}, {"url": "https://github.com/jetpacmonkey", "name": "<PERSON>"}, {"url": "https://github.com/EdwardBetts", "name": "<PERSON>"}, {"url": "https://github.com/dvlsg", "name": "dvlsg"}, {"url": "https://github.com/amilajack", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JakeChampion", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "88cbce3d13bc675a63f840b3225c180f870786d7", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.6.tgz", "integrity": "sha512-qZ3bAurt2IXGPR3c57PyaSYEnQiLRwPeS60G9TahElBZsdOABo+iKYch/PhRjSTZJ5/DF08x43XMt9qec2g3ig==", "signatures": [{"sig": "MEYCIQDC5ufk5eINV+8IyU53MnzhLfkjdX57wQUZjay3zpckdwIhAOCtaXY9d1eJhQBHNFelzHtn7eDOnj31wPbWZE1Xh+WJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./type-detect.js", "files": ["index.js", "type-detect.js"], "engines": {"node": ">=4"}, "gitHead": "dd38626838df3f8ebb791edbdcdc23015a50d1d6", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "rollup -c rollup.conf.js", "prepare": "cross-env NODE_ENV=production npm run build", "test:node": "nyc mocha type-detect.test.js", "commit-msg": "commitlint -x angular", "pretest:node": "cross-env NODE_ENV=test npm run build", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "cross-env NODE_ENV=test npm run build", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "8.9.4", "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}, "extends": ["strict/es6"], "globals": {"HTMLElement": false}}, "devDependencies": {"nyc": "^11.3.0", "buble": "^0.16.0", "husky": "^0.14.3", "karma": "^1.7.1", "mocha": "^4.0.1", "eslint": "^4.10.0", "rollup": "^0.50.0", "codecov": "^3.0.0", "benchmark": "^2.1.0", "cross-env": "^5.1.1", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^1.1.1", "@commitlint/cli": "^4.2.2", "semantic-release": "^8.2.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-buble": "^0.16.0", "eslint-config-strict": "^14.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-istanbul": "^1.1.0", "eslint-plugin-filenames": "^1.2.0", "commitlint-config-angular": "^4.2.1", "rollup-plugin-node-resolve": "^3.0.0", "karma-safaritechpreview-launcher": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.6.tgz_1516067130375_0.054129550931975245", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "type-detect", "version": "4.0.7", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.7", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/shvaikalesh", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lucasfcosta", "name": "<PERSON>"}, {"url": "https://github.com/meeber", "name": "<PERSON>"}, {"url": "https://github.com/jetpacmonkey", "name": "<PERSON>"}, {"url": "https://github.com/EdwardBetts", "name": "<PERSON>"}, {"url": "https://github.com/dvlsg", "name": "dvlsg"}, {"url": "https://github.com/amilajack", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JakeChampion", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "862bd2cf6058ad92799ff5a5b8cf7b6cec726198", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.7.tgz", "integrity": "sha512-4Rh17pAMVdMWzktddFhISRnUnFIStObtUMNGzDwlA6w/77bmGv3aBbRdCmQR6IjzfkTo9otnW+2K/cDRhKSxDA==", "signatures": [{"sig": "MEUCIQDxJE5pxlV7jGuWWOq0HK917VwqyrgHsp15BxytCm8IDgIgehtovjkjaeMO0nCc5xz2PwEtbGlWiVkrhEiFZ7eESeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./type-detect.js", "files": ["index.js", "type-detect.js"], "engines": {"node": ">=4"}, "gitHead": "992ebe7fa5d4f59df17c62a7273908dc31e79fc2", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "rollup -c rollup.conf.js", "prepare": "cross-env NODE_ENV=production npm run build", "test:node": "nyc mocha type-detect.test.js", "commit-msg": "commitlint -x angular", "pretest:node": "cross-env NODE_ENV=test npm run build", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "cross-env NODE_ENV=test npm run build", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "8.9.4", "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}, "extends": ["strict/es6"], "globals": {"HTMLElement": false}}, "devDependencies": {"nyc": "^11.3.0", "buble": "^0.16.0", "husky": "^0.14.3", "karma": "^1.7.1", "mocha": "^4.0.1", "eslint": "^4.10.0", "rollup": "^0.50.0", "codecov": "^3.0.0", "benchmark": "^2.1.0", "cross-env": "^5.1.1", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^1.1.1", "@commitlint/cli": "^4.2.2", "semantic-release": "^8.2.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-buble": "^0.16.0", "eslint-config-strict": "^14.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-istanbul": "^1.1.0", "eslint-plugin-filenames": "^1.2.0", "commitlint-config-angular": "^4.2.1", "rollup-plugin-node-resolve": "^3.0.0", "karma-safaritechpreview-launcher": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.7.tgz_1516325676819_0.08597883768379688", "host": "s3://npm-registry-packages"}}, "4.0.8": {"name": "type-detect", "version": "4.0.8", "keywords": ["type", "typeof", "types"], "author": {"url": "http://alogicalparadox.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-detect@4.0.8", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/davelosert", "name": "<PERSON>"}, {"url": "https://github.com/shvaikalesh", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lucasfcosta", "name": "<PERSON>"}, {"url": "https://github.com/meeber", "name": "<PERSON>"}, {"url": "https://github.com/jetpacmonkey", "name": "<PERSON>"}, {"url": "https://github.com/EdwardBetts", "name": "<PERSON>"}, {"url": "https://github.com/dvlsg", "name": "dvlsg"}, {"url": "https://github.com/amilajack", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JakeChampion", "name": "<PERSON>"}, {"url": "https://github.com/bajtos", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/type-detect#readme", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "dist": {"shasum": "7646fb5f18871cfbb7749e69bd39a6388eb7450c", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==", "signatures": [{"sig": "MEQCIEFxImpCxU+MuBlIeBAmerqd0RsGClH5O5G220HhFDPjAiBu9IIino0NDpaZJe49jwAOU5KIoB0BfJu8oPqZi0uLhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./type-detect.js", "files": ["index.js", "type-detect.js"], "engines": {"node": ">=4"}, "gitHead": "a40d8395f06507edd3e4806cb3fe5a878f6a6551", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "rollup -c rollup.conf.js", "prepare": "cross-env NODE_ENV=production npm run build", "test:node": "nyc mocha type-detect.test.js", "commit-msg": "commitlint -x angular", "pretest:node": "cross-env NODE_ENV=test npm run build", "test:browser": "karma start --singleRun=true", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "pretest:browser": "cross-env NODE_ENV=test npm run build", "upload-coverage": "codecov", "posttest:browser": "npm run upload-coverage", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/type-detect.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Improved typeof detection for node.js and the browser.", "directories": {}, "_nodeVersion": "8.9.4", "eslintConfig": {"env": {"es6": true}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}, "extends": ["strict/es6"], "globals": {"HTMLElement": false}}, "devDependencies": {"nyc": "^11.3.0", "buble": "^0.16.0", "husky": "^0.14.3", "karma": "^1.7.1", "mocha": "^4.0.1", "eslint": "^4.10.0", "rollup": "^0.50.0", "codecov": "^3.0.0", "benchmark": "^2.1.0", "cross-env": "^5.1.1", "karma-mocha": "^1.3.0", "simple-assert": "^1.0.0", "karma-coverage": "^1.1.1", "@commitlint/cli": "^4.2.2", "semantic-release": "^8.2.0", "karma-ie-launcher": "^1.0.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-buble": "^0.16.0", "eslint-config-strict": "^14.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-istanbul": "^1.1.0", "eslint-plugin-filenames": "^1.2.0", "commitlint-config-angular": "^4.2.1", "rollup-plugin-node-resolve": "^3.0.0", "karma-safaritechpreview-launcher": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/type-detect-4.0.8.tgz_1517495439168_0.14752365997992456", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "type-detect", "version": "4.1.0", "description": "Improved typeof detection for node.js and the browser.", "keywords": ["type", "typeof", "types"], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://alogicalparadox.com"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/keithamus"}, {"name": "<PERSON>", "url": "https://github.com/davelosert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shvaikalesh"}, {"name": "<PERSON>", "url": "https://github.com/lucasfcosta"}, {"name": "<PERSON>", "url": "https://github.com/meeber"}, {"name": "<PERSON>", "url": "https://github.com/jetpacmonkey"}, {"name": "<PERSON>", "url": "https://github.com/EdwardBetts"}, {"name": "dvlsg", "url": "https://github.com/dvlsg"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "<PERSON>", "url": "https://github.com/JakeChampion"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/bajtos"}], "main": "./type-detect.js", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/type-detect.git"}, "scripts": {"bench": "node bench", "build": "tsc && rollup -c rollup.conf.js", "commit-msg": "commitlint -x angular", "lint": "eslint --ignore-path .gitignore . --ext .js,.ts", "prepare": "cross-env NODE_ENV=production npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest:node": "cross-env NODE_ENV=test npm run build", "pretest:browser": "cross-env NODE_ENV=test npm run build", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start --singleRun=true", "test:node": "nyc mocha type-detect.test.js", "test:deno": "deno test test/deno-test.ts", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "posttest:browser": "npm run upload-coverage", "upload-coverage": "codecov"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "env": {"es6": true}, "extends": ["strict/es6"], "globals": {"HTMLElement": false, "window": false}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}}, "devDependencies": {"@commitlint/cli": "^13.1.0", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.5", "@typescript-eslint/eslint-plugin": "^4.31.2", "@typescript-eslint/parser": "^4.31.2", "benchmark": "^2.1.4", "buble": "^0.20.0", "codecov": "^3.8.3", "commitlint-config-angular": "^13.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "husky": "^7.0.2", "karma": "^6.3.4", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-detect-browsers": "^2.3.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.1", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-opera-launcher": "^1.0.0", "karma-safari-launcher": "^1.0.0", "karma-safaritechpreview-launcher": "^2.0.2", "karma-sauce-launcher": "^4.3.6", "mocha": "^9.1.1", "nyc": "^15.1.0", "rollup": "^2.57.0", "rollup-plugin-istanbul": "^3.0.0", "semantic-release": "^18.0.0", "simple-assert": "^1.0.0", "typescript": "^4.4.3"}, "engines": {"node": ">=4"}, "_id": "type-detect@4.1.0", "gitHead": "9a771dcec525f34cbeacde6236b5a916a8f8d5fd", "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "homepage": "https://github.com/chaijs/type-detect#readme", "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Acylog8/luQ8L7il+geoSxhEkazvkslg7PSNKOX59mbB9cOveP5aq9h74Y7YU8yDpJwetzQQrfIwtf4Wp4LKcw==", "shasum": "deb2453e8f08dcae7ae98c626b13dddb0155906c", "tarball": "https://registry.npmjs.org/type-detect/-/type-detect-4.1.0.tgz", "fileCount": 7, "unpackedSize": 38728, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCks1KtQdM+CI22rWim+S9qzGlsiFu4NZiG1GWPyBemEQIgDK+7Jz560hvwNkbooArqefarCpkXSngugmVzyJlp0hQ="}]}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/type-detect_4.1.0_1721904620307_0.01726216826542748"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-08-14T12:07:53.439Z", "modified": "2024-07-25T10:50:20.663Z", "0.1.0": "2013-08-14T12:07:57.430Z", "0.1.1": "2013-10-10T10:35:00.743Z", "0.1.2": "2013-11-30T20:38:25.139Z", "1.0.0": "2015-04-05T18:22:47.718Z", "2.0.0": "2016-03-13T19:30:55.502Z", "2.0.1": "2016-05-16T12:36:25.380Z", "2.0.2": "2016-07-27T19:33:33.459Z", "3.0.0": "2016-10-08T23:50:12.335Z", "4.0.0": "2016-10-10T14:15:24.553Z", "4.0.1": "2017-04-18T05:24:45.306Z", "4.0.2": "2017-04-20T00:34:48.415Z", "4.0.3": "2017-04-20T03:43:15.845Z", "4.0.4": "2017-11-09T13:07:53.096Z", "4.0.5": "2017-11-09T13:49:28.091Z", "4.0.6": "2018-01-16T01:45:31.370Z", "4.0.7": "2018-01-19T01:34:37.885Z", "4.0.8": "2018-02-01T14:30:41.312Z", "4.1.0": "2024-07-25T10:50:20.475Z"}, "bugs": {"url": "https://github.com/chaijs/type-detect/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://alogicalparadox.com"}, "license": "MIT", "homepage": "https://github.com/chaijs/type-detect#readme", "keywords": ["type", "typeof", "types"], "repository": {"type": "git", "url": "git+ssh://**************/chaijs/type-detect.git"}, "description": "Improved typeof detection for node.js and the browser.", "contributors": [{"name": "<PERSON>", "url": "https://github.com/keithamus"}, {"name": "<PERSON>", "url": "https://github.com/davelosert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shvaikalesh"}, {"name": "<PERSON>", "url": "https://github.com/lucasfcosta"}, {"name": "<PERSON>", "url": "https://github.com/meeber"}, {"name": "<PERSON>", "url": "https://github.com/jetpacmonkey"}, {"name": "<PERSON>", "url": "https://github.com/EdwardBetts"}, {"name": "dvlsg", "url": "https://github.com/dvlsg"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "<PERSON>", "url": "https://github.com/JakeChampion"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/bajtos"}], "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "readme": "<h1 align=center>\n  <a href=\"http://chaijs.com\" title=\"Chai Documentation\">\n    <img alt=\"type-detect\" src=\"https://raw.githubusercontent.com/chaijs/type-detect/master/type-detect-logo.svg\"/>\n  </a>\n</h1>\n<br>\n<p align=center>\n  Improved typeof detection for <a href=\"https://nodejs.org\">node</a>, <a href=\"https://deno.land/\">Deno</a>, and the browser.\n</p>\n\n<p align=center>\n  <a href=\"./LICENSE\">\n    <img\n      alt=\"license:mit\"\n      src=\"https://img.shields.io/badge/license-mit-green.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"https://www.npmjs.com/packages/type-detect\">\n    <img\n      alt=\"npm:?\"\n      src=\"https://img.shields.io/npm/v/type-detect.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"https://github.com/chaijs/type-detect\">\n    <img\n      alt=\"build:?\"\n      src=\"https://github.com/chaijs/type-detect/workflows/Build/badge.svg\"\n    />\n  </a>\n  <a href=\"https://coveralls.io/r/chaijs/type-detect\">\n    <img\n      alt=\"coverage:?\"\n      src=\"https://img.shields.io/coveralls/chaijs/type-detect/master.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"https://www.npmjs.com/packages/type-detect\">\n    <img\n      alt=\"dependencies:?\"\n      src=\"https://img.shields.io/npm/dm/type-detect.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"\">\n    <img\n      alt=\"devDependencies:?\"\n      src=\"https://img.shields.io/david/chaijs/type-detect.svg?style=flat-square\"\n    />\n  </a>\n  <br>\n  <a href=\"https://chai-slack.herokuapp.com/\">\n    <img\n      alt=\"Join the Slack chat\"\n      src=\"https://img.shields.io/badge/slack-join%20chat-E2206F.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"https://gitter.im/chaijs/chai\">\n    <img\n      alt=\"Join the Gitter chat\"\n      src=\"https://img.shields.io/badge/gitter-join%20chat-D0104D.svg?style=flat-square\"\n    />\n  </a>\n</p>\n<div align=center>\n  <table width=\"100%\">\n  <tr><th colspan=6>Supported Browsers</th></tr> <tr>\n  <th align=center><img src=\"https://camo.githubusercontent.com/ab586f11dfcb49bf5f2c2fa9adadc5e857de122a/687474703a2f2f73766773686172652e636f6d2f692f3278532e737667\" alt=\"\"> Chrome</th>\n  <th align=center><img src=\"https://camo.githubusercontent.com/98cca3108c18dcfaa62667b42046540c6822cdac/687474703a2f2f73766773686172652e636f6d2f692f3279352e737667\" alt=\"\"> Edge</th>\n  <th align=center><img src=\"https://camo.githubusercontent.com/acdcb09840a9e1442cbaf1b684f95ab3c3f41cf4/687474703a2f2f73766773686172652e636f6d2f692f3279462e737667\" alt=\"\"> Firefox</th>\n  <th align=center><img src=\"https://camo.githubusercontent.com/728f8cb0bee9ed58ab85e39266f1152c53e0dffd/687474703a2f2f73766773686172652e636f6d2f692f3278342e737667\" alt=\"\"> Safari</th>\n  <th align=center><img src=\"https://camo.githubusercontent.com/96a2317034dee0040d0a762e7a30c3c650c45aac/687474703a2f2f73766773686172652e636f6d2f692f3279532e737667\" alt=\"\"> IE</th>\n  </tr><tr>\n  <td align=center>✅</td>\n  <td align=center>✅</td>\n  <td align=center>✅</td>\n  <td align=center>✅</td>\n  <td align=center>9, 10, 11</td>\n  </tr>\n  </table>\n</div>\n\n## What is Type-Detect?\n\nType Detect is a module which you can use to detect the type of a given object. It returns a string representation of the object's type, either using [`typeof`](http://www.ecma-international.org/ecma-262/6.0/index.html#sec-typeof-operator) or [`@@toStringTag`](http://www.ecma-international.org/ecma-262/6.0/index.html#sec-symbol.tostringtag). It also normalizes some object names for consistency among browsers.\n\n## Why?\n\nThe `typeof` operator will only specify primitive values; everything else is `\"object\"` (including `null`, arrays, regexps, etc). Many developers use `Object.prototype.toString()` - which is a fine alternative and returns many more types (null returns `[object Null]`, Arrays as `[object Array]`, regexps as `[object RegExp]` etc). \n\nSadly, `Object.prototype.toString` is slow, and buggy. By slow - we mean it is slower than `typeof`. By buggy - we mean that some values (like Promises, the global object, iterators, dataviews, a bunch of HTML elements) all report different things in different browsers.\n\n`type-detect` fixes all of the shortcomings with `Object.prototype.toString`. We have extra code to speed up checks of JS and DOM objects, as much as 20-30x faster for some values. `type-detect` also fixes any consistencies with these objects.\n\n## Installation\n\n### Node.js\n\n`type-detect` is available on [npm](http://npmjs.org). To install it, type:\n\n    $ npm install type-detect\n\n### Deno\n\n`type-detect` can be imported with the following line:\n\n```js\nimport type from 'https://deno.land/x/type_detect@v4.1.0/index.ts'\n```\n\n### Browsers\n\nYou can also use it within the browser; install via npm and use the `type-detect.js` file found within the download. For example:\n\n```html\n<script src=\"./node_modules/type-detect/type-detect.js\"></script>\n```\n\n## Usage\n\nThe primary export of `type-detect` is function that can serve as a replacement for `typeof`. The results of this function will be more specific than that of native `typeof`.\n\n```js\nvar type = require('type-detect');\n```\nOr, in the browser use case, after the <script> tag,\n ```js\n var type = typeDetect;\n ```\n\n#### array\n\n```js\nassert(type([]) === 'Array');\nassert(type(new Array()) === 'Array');\n```\n\n#### regexp\n\n```js\nassert(type(/a-z/gi) === 'RegExp');\nassert(type(new RegExp('a-z')) === 'RegExp');\n```\n\n#### function\n\n```js\nassert(type(function () {}) === 'function');\n```\n\n#### arguments\n\n```js\n(function () {\n  assert(type(arguments) === 'Arguments');\n})();\n```\n\n#### date\n\n```js\nassert(type(new Date) === 'Date');\n```\n\n#### number\n\n```js\nassert(type(1) === 'number');\nassert(type(1.234) === 'number');\nassert(type(-1) === 'number');\nassert(type(-1.234) === 'number');\nassert(type(Infinity) === 'number');\nassert(type(NaN) === 'number');\nassert(type(new Number(1)) === 'Number'); // note - the object version has a capital N\n```\n\n#### string\n\n```js\nassert(type('hello world') === 'string');\nassert(type(new String('hello')) === 'String'); // note - the object version has a capital S\n```\n\n#### null\n\n```js\nassert(type(null) === 'null');\nassert(type(undefined) !== 'null');\n```\n\n#### undefined\n\n```js\nassert(type(undefined) === 'undefined');\nassert(type(null) !== 'undefined');\n```\n\n#### object\n\n```js\nvar Noop = function () {};\nassert(type({}) === 'Object');\nassert(type(Noop) !== 'Object');\nassert(type(new Noop) === 'Object');\nassert(type(new Object) === 'Object');\n```\n\n#### ECMA6 Types\n\nAll new ECMAScript 2015 objects are also supported, such as Promises and Symbols:\n\n```js\nassert(type(new Map() === 'Map');\nassert(type(new WeakMap()) === 'WeakMap');\nassert(type(new Set()) === 'Set');\nassert(type(new WeakSet()) === 'WeakSet');\nassert(type(Symbol()) === 'symbol');\nassert(type(new Promise(callback) === 'Promise');\nassert(type(new Int8Array()) === 'Int8Array');\nassert(type(new Uint8Array()) === 'Uint8Array');\nassert(type(new UInt8ClampedArray()) === 'Uint8ClampedArray');\nassert(type(new Int16Array()) === 'Int16Array');\nassert(type(new Uint16Array()) === 'Uint16Array');\nassert(type(new Int32Array()) === 'Int32Array');\nassert(type(new UInt32Array()) === 'Uint32Array');\nassert(type(new Float32Array()) === 'Float32Array');\nassert(type(new Float64Array()) === 'Float64Array');\nassert(type(new ArrayBuffer()) === 'ArrayBuffer');\nassert(type(new DataView(arrayBuffer)) === 'DataView');\n```\n\nAlso, if you use `Symbol.toStringTag` to change an Objects return value of the `toString()` Method, `type()` will return this value, e.g:\n\n```js\nvar myObject = {};\nmyObject[Symbol.toStringTag] = 'myCustomType';\nassert(type(myObject) === 'myCustomType');\n```\n", "readmeFilename": "README.md", "users": {"jcottam": true, "imfangli": true, "justjavac": true, "bluelovers": true, "nelson6e65": true, "rocket0191": true, "chocolateboy": true}}