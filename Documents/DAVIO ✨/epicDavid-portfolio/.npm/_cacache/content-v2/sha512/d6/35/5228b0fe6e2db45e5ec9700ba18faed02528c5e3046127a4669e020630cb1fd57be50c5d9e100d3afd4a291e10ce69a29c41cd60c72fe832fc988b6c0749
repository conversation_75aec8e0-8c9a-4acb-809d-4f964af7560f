{"_id": "redux-mock-store", "_rev": "61-479dd9736dca6c26c4950379af2ac787", "name": "redux-mock-store", "dist-tags": {"beta": "1.0.0", "latest": "1.5.5"}, "versions": {"0.0.1": {"name": "redux-mock-store", "version": "0.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "642b27909fb93de11e4c6f1a767fb83e6c235f97", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.1.tgz", "integrity": "sha512-98nEkgIhYILr3HczXqz7r9w7fxqilNGJTegSI9Oy1GlhqqBDYiEXhwYdtv70YpIpMl3+IdNUATUfg7S0FxDTuw==", "signatures": [{"sig": "MEUCIGpm1kTBZgVNhDx7qDmL253nyexuJZFmpGJd0g4k6hBdAiEA4G5aNokqlPMbG9AfcY6uTgadJvMinjskoYvTwjr+efw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "642b27909fb93de11e4c6f1a767fb83e6c235f97", "gitHead": "cc50b7bdd98db9a7f16524dad61d87088af53c54", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "A mock store for your testing your redux app", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "0.0.2": {"name": "redux-mock-store", "version": "0.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "0054e41e9e0957d3e7edb5ed57e73c078040dd25", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.2.tgz", "integrity": "sha512-OnxIvmEoyXZ6R+/yI5+ysSEJ2IMSvCmk1HZClJ7PqIZg7zFfVTPLXGVLMzeDDkbiWswzAoP8PogPVLleG75hng==", "signatures": [{"sig": "MEQCIANp0xy/7Jx/HdNmlNwrXxO2arTRCaEWZVDaA7zACG4jAiAXqf3khZ9DiGAxTsczgOs7v0djpe0QsJRKNUJ8tsaKWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "0054e41e9e0957d3e7edb5ed57e73c078040dd25", "gitHead": "22fba3850e1dbab7e4a62e76bb9d1343819f4826", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "0.0.3": {"name": "redux-mock-store", "version": "0.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "57ff4804e1ad2680e0492d2b645e80153e2d1218", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.3.tgz", "integrity": "sha512-T/B5I/LP8ErZl8YP3q1nndM6yLrZgRbfUPGf1dCOSfwkrS0z3mW1CKJuEj2P5tVvxDXZ0FdsmXdbHZ9UNgAVpw==", "signatures": [{"sig": "MEUCIQCjBtU+zhgi6kwNDPku8s8B3CVqbDpDdltBsWgPzx6lBwIgP7mqMB5rMoLS39aAR0LW16LFGD8Db1oDGY1hmKYL6+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "57ff4804e1ad2680e0492d2b645e80153e2d1218", "gitHead": "c5534558d7a7189d1cfffc2100ed9519075a6eac", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "1.4.13", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "0.0.4": {"name": "redux-mock-store", "version": "0.0.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "19fe9e6151d43ec0981c60d2c78ad361badc471a", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.4.tgz", "integrity": "sha512-SD6mAfBKZdjmb9pUL26Tx9OJJH94Hh9LGTjiaJy/sD2FGZ8dVUbXh1DDec/zF7+oIsBcJuSQlPJd1dWkedn1mA==", "signatures": [{"sig": "MEQCIFp6BVkmftEpSJDbKvpaFFJZxSGQYkVJgT3BsWLXFFILAiBUNHYEmqNW16eOJkY8kI0oHm8R1XgXh32kNN6TnmPIhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "19fe9e6151d43ec0981c60d2c78ad361badc471a", "gitHead": "461f7ca7dd6f9ae992a9ff054ee6f49540cdb911", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "1.4.13", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "0.0.5": {"name": "redux-mock-store", "version": "0.0.5", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "c2dacf85a55455f56205bde70c8e4a5704e3ebcf", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.5.tgz", "integrity": "sha512-brSnXnoE0lQB1CDh95Pt1+PAqdQrsBVGTI3Fe1jiZl6mxKujkq/ItQXjEf/fVH7BlGZnz7vLxPl/1MjMQljlvg==", "signatures": [{"sig": "MEUCIQDQak+6LLzxTQfHwUBIt3vXiv/SbzA/W3MCEJS3EJBl3gIgEvRRZ65Ke3dBsgAwrCxWOwPHpvzHeapqhkCrrn94NRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c2dacf85a55455f56205bde70c8e4a5704e3ebcf", "gitHead": "4e08215d2ccda75c8b5f3a37d9e8564f4ce247ba", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "0.0.6": {"name": "redux-mock-store", "version": "0.0.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@0.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "e2ae1c97954e457451998a26167c8ebb1074e488", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-0.0.6.tgz", "integrity": "sha512-59Z9Vl+Hp8hVgefi7wbx2cqYgttO9+70a34LBxcZQoPr6K3f8N4gxr1rvO4/nDYcibxt8K1MBUSJlmUAVM7Zuw==", "signatures": [{"sig": "MEQCIHEtx++eYIFDR2wQRpfoPDYGg4AA5TiTyzsQGsjBT+lhAiBA/jXf8ynhEu9tYwiQozByapvnrKwuc8jz3c02HvA5nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "e2ae1c97954e457451998a26167c8ebb1074e488", "gitHead": "0b74cca488220d8731b6b57eabbdb91105cca686", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}}, "1.0.0": {"name": "redux-mock-store", "version": "1.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "d3a618cf8f6252d4c686eeba01613b83fee7a2b5", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.0.0.tgz", "integrity": "sha512-GG3a/xgl+qBp+vMsf68ZbyKRipYhgxggPnrsZz0aXBdaKmyCWrF89EWv4mQAnZI8BIJda1ANRuSZU2+FPbu0qQ==", "signatures": [{"sig": "MEYCIQD0vGg5uig92NLV7b9I12e3ocO1BhWkk7122pQnhbL8bwIhAIKnF3UBiHfbPERW1bB4Y6qg2WmYNqW9XkKv/stywpO3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d3a618cf8f6252d4c686eeba01613b83fee7a2b5", "gitHead": "ac435c3be789885b230c725b1a67502765173330", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.0.0.tgz_1456413085830_0.20753350341692567", "host": "packages-6-west.internal.npmjs.com"}}, "1.0.1": {"name": "redux-mock-store", "version": "1.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "d1ea41a67eaa581dba57d09d645e83d3ffab072a", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.0.1.tgz", "integrity": "sha512-ROMrRMr2fqXQIWlTEe1Z0wprA2G1rS3Bg0Zkn9O9JRC+eKy8I/h7occ0xAbWlIFw+O3HGXWPfum6eJ9gWWd/Jg==", "signatures": [{"sig": "MEUCIQCdDPOJ2cHrAWbeGvuhsO40Qwc1i/jZzTwNa9SWYXRMyAIgd1OzhwSSGJOCvgMPjetfAGxCFuR3UJgzX2L47nVS42o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "d1ea41a67eaa581dba57d09d645e83d3ffab072a", "gitHead": "8165fde7f85da7c8afc09df19a88dce7c7fd3dc0", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"expect": "^1.12.2"}, "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "rimraf": "^2.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.0.1.tgz_1457644000729_0.2878829997498542", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "redux-mock-store", "version": "1.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "5c690e3898f21588c47448cabd8dbcf3bedd5f25", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.0.2.tgz", "integrity": "sha512-jEdljLaNLmBHFd8jtat+eBaL2lbZ9iFu/Iy1GVxKaqit8NAzz6m7evnNyy5qLhczdH/hDiActXkypw4imHycDQ==", "signatures": [{"sig": "MEUCIQCC2NfmUYP3QRBmHvp0OYMuMxV5GjfVUdioGsRqnc3skwIgMBvVb8FedH9mDGsxJa2HLIUavRzbc0YxqCab48zVHVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "5c690e3898f21588c47448cabd8dbcf3bedd5f25", "gitHead": "9f114243924a514f967abc263642fe0b1f6f8830", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.0.2.tgz_1457644313728_0.170200374443084", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.3": {"name": "redux-mock-store", "version": "1.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "e6183e80bee3a3eedb3f4eda9c18bcb197c9a021", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.0.3.tgz", "integrity": "sha512-xpqeLwUZTkEXz9q2NbhgYPNIuJUT60okNB7oIyZS9ZWx3YiP3Mss1+zh0ofXJ8hM93RzY9j3uR1G5ySl1Yen7w==", "signatures": [{"sig": "MEUCIHk4t5okgi1o0b0dqP/amDPfJ15HhXigYNQoOxImQMIpAiEA2xN5UQ7GVjm6HeyoDKQNwVPXqacRqkyfgQH/P3o/Vkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "e6183e80bee3a3eedb3f4eda9c18bcb197c9a021", "gitHead": "13c7e896d039b7190eee0c9c1960b6af90757f63", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.0.3.tgz_1463732675416_0.4099264235701412", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.4": {"name": "redux-mock-store", "version": "1.0.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "b22e27c4194a804246e84073aa8a39b7da6fc02d", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.0.4.tgz", "integrity": "sha512-cRTOdIZwsgheXhzwS4wfzJISLx8a1ZmjlCLOblU1a8DwGcHh1sceF8wFM8Zly9X5E6+4bd7E1cQRFCO3xt0Hcg==", "signatures": [{"sig": "MEYCIQDh8oJ5CCB5ZpawzQ60KhM0mEWB6z6r6ZUfaMdakGJhSwIhAJ4mmYMr+MD/W509+RSSAObx6jT58my7jr8ULuYcPINi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b22e27c4194a804246e84073aa8a39b7da6fc02d", "gitHead": "2e5c3aee167fc39ad9311ca09e054116ad40a144", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.0.4.tgz_1464356197927_0.8029627813957632", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "redux-mock-store", "version": "1.1.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "93e3eaa530269fa107a39a602fba0370a21b0b9a", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.1.0.tgz", "integrity": "sha512-CGdHY7o2ChlmVnuRj1NEjSqxtkArdaRJ9aniAuOkrh1hgfm/gEt6bZqzyDZgi9ttSv0J8WKT4pbN8985DFzauA==", "signatures": [{"sig": "MEQCIE/ENvLiXVS9MLgCiH40ofOFZPOpgDcOXLyBmr02++FMAiAWrC0ky3VNaVQ/AsTLUkaZv7VrutN6Y44c+2hHXlonuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "93e3eaa530269fa107a39a602fba0370a21b0b9a", "gitHead": "02457770f5ca6b5b7f8772b5595bf0b66640579f", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.1.0.tgz_1465127508376_0.7830196060240269", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.1": {"name": "redux-mock-store", "version": "1.1.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "28089dec4563c26ff9b1b2981d5cf7ced7071eab", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.1.1.tgz", "integrity": "sha512-RpVJOX37LcErWShDZ04Wh+iHR7MI0jGKZ3n8A3sb5L3YVm5PqCAfGo6Lj5pWeHz5fwm/jHvzagH6Mn1aTb5skg==", "signatures": [{"sig": "MEUCIQC4DuiNW8gCxYmvCudcaWlTxmPx+XhsPaQlRe4/RVu6FQIgScZ/IuGiy9dlkxBU1Ri57XTupQmRZ3MrlmtxCkbwaJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "28089dec4563c26ff9b1b2981d5cf7ced7071eab", "gitHead": "fa356834551bad05f4ad97169682ca40971fd6f2", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.1.1.tgz_1465999089269_0.20297816046513617", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "redux-mock-store", "version": "1.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "1c2417e2374992f2cb8687031e15422680805cfd", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.1.2.tgz", "integrity": "sha512-k7yLfNFeObL7yE/tFZWCV6ZsOi15M0WvxpdIL6Iv1PJ8zib4mOKWsyhqIGGmJ5X2E1gdsBA8z34N9qFSTJHD9A==", "signatures": [{"sig": "MEYCIQDkyiuhJksIvkgfVnTmwUmwZ0RB6yZKrO+5qyLg0TYLngIhAKDvUv1mgDKmnz0zeTHtCnMdL7NVSEZUKAjO4M+nmNmw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1c2417e2374992f2cb8687031e15422680805cfd", "gitHead": "d21dfe587834f5fdf16d7f4dd3e72cf8c63240f0", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.1.2.tgz_1467235926018_0.2942732742521912", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.3": {"name": "redux-mock-store", "version": "1.1.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "95eca94236a35fd09c28bf2592eb52067fe17e5f", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.1.3.tgz", "integrity": "sha512-Foeq2S5z9HHEqRk+BD3eWDrs5KXYMZBmkGOwLiCYxYOp0SyS1Efz5GzV+trnYaVauM63T/o3aXZXLgpozHRUAg==", "signatures": [{"sig": "MEUCIQDw92OKmu8IYEiBCCBMkwdaPjUQTcfgXGa0hLuS+nViLgIgGWSkZSC4P8V79o/WiCEV6qSYcT3q+V6zeN2xGNviYBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "95eca94236a35fd09c28bf2592eb52067fe17e5f", "gitHead": "fd2ca6a45ebf351a14c1911d98685d407d3641e4", "scripts": {"test": "mocha --compilers js:babel/register --reporter spec test/*.js", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.1.3.tgz_1471534059466_0.9709420551080257", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.4": {"name": "redux-mock-store", "version": "1.1.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "393d33a1beb52af2dc1e3f9056ed9b524dab9354", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.1.4.tgz", "integrity": "sha512-blumOYJqKVmfMsl9RLZow99XXvTqWuxVcFgPEab5uJ9Thl7LnK/yzQT4gnPV3teID22/gCxG+LBKsJrmZUnEMg==", "signatures": [{"sig": "MEYCIQChTrY//9AKoUouZXXekYm946EN/E/M/Ryu3hamENA1ZQIhAIWylGWMzFXVGQdQ+3+UuVulbqXRGZyEGjl9kU5tX6bb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "393d33a1beb52af2dc1e3f9056ed9b524dab9354", "gitHead": "a9a376c8666acf1f02e1e7a81b1c5753badf264f", "scripts": {"lint": "standard src/*.js", "test": "mocha --compilers js:babel/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "typings": "./index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"babel": "^5.8.29", "mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "redux-thunk": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.1.4.tgz_1471534894479_0.5191525814589113", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.0": {"name": "redux-mock-store", "version": "1.2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "c8309b35fe6eafa8ff1f7e313527887709788005", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.2.0.tgz", "integrity": "sha512-nts9Mbd8aomzUf9v/KLDE5Wl09jPxvOSSANy75VK3G0cQeIUaK6S+zONEyGWH1EPBKzdFJqMhZ1aqRQe39Hyxg==", "signatures": [{"sig": "MEQCICLt3fLigopQ+DqucDwccvFKaDqHJxeNiwuNqdmowjvpAiAmESJeD42VVgIM2djEJDtKqEA7yr8D4UMYSv3HzNS+CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c8309b35fe6eafa8ff1f7e313527887709788005", "gitHead": "3c3e9c57fe687c37d1985d34417c5fe793896ed5", "scripts": {"lint": "standard src/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.2.0.tgz_1473179218097_0.5365988884586841", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "redux-mock-store", "version": "1.2.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "630c0e2642927d1417c844d935266b501f2fc231", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.2.1.tgz", "integrity": "sha512-3Jhjw7l83Cv6kxkXhP2ow5utnqgx88glwDF6Nla1NkOo3GQ9yyqi9G0Jyv/Dlfc06Tnxy4vF+MXhoguxX7xiQQ==", "signatures": [{"sig": "MEUCIQDkPaJLNI36cmovCjUa5Q90IW8dQyAWKBJrtNZf2KFbQwIgNTmuILSDsbiQ+zg9AU66Nn5UlfVMeYoNe1NdT91sy0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "630c0e2642927d1417c844d935266b501f2fc231", "gitHead": "6a539e0e74a1fe0b9133214c1398fd11451a84aa", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.2.1.tgz_1474723497331_0.42423485894687474", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.2": {"name": "redux-mock-store", "version": "1.2.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "38007dc38f12ca8d965c7521afee5ccacc234d03", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.2.2.tgz", "integrity": "sha512-8GUqTSfqKVTke5iUMhomDfbtQjb7/kVtnXW83r54jY8ogWS23AeIFrF2ZYMXUm7s9ETYejowlESQ38eC/oxlTA==", "signatures": [{"sig": "MEUCIGOQGvULIikUotOH2NF6shztV5mSYDmK32z7hMrb+DaLAiEAjoBAUIkZloD8UqE8irtT4Kil1IQYWpCB6IdtKSGJByQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "38007dc38f12ca8d965c7521afee5ccacc234d03", "gitHead": "d141a70729032c06022b5cf49020d3647cea2d10", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "[![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.2.2.tgz_1485788867665_0.5175579336937517", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.3": {"name": "redux-mock-store", "version": "1.2.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "1b3ad299da91cb41ba30d68e3b6f024475fb9e1b", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.2.3.tgz", "integrity": "sha512-cdC6Svw93JHGaKfl6svLNreZ0djQJMGIgSK/X0+f0wNK/iA0XoZLzNZfSDji1j0eRox4uZeJNqZWrwJ/JJ7vpw==", "signatures": [{"sig": "MEUCIHh+9+AXQl42hBuJbP0bV14hcxsbccHCfKlzInGrH4nlAiEA+zj556QpvGAmXX3670nqAmVhP3yLI+wDEA9S2HorksU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1b3ad299da91cb41ba30d68e3b6f024475fb9e1b", "gitHead": "65d143a6a3a3b882c57731731adfdcc0e9cac86a", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.2.3.tgz_1491757094078_0.9005527487024665", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.0": {"name": "redux-mock-store", "version": "1.3.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "6edfef0d2332f20576381069d6d889a6d0a4451c", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.3.0.tgz", "integrity": "sha512-TiwaDF4WLX/lJP0v1j4CMYUEfaIftTGuMUOYb7hmYJjLMAdgj2b/LOf+G9QDssNKFOpSl4B8St8TMUzF3hx92Q==", "signatures": [{"sig": "MEUCIQCJcR4XDJLY5mPBjb/ygHIBpTX1dx53+gQPzbtPvMF0IAIgIPURW8cvIueXBJkrE3Lj8DXy6vtFElDnSTujZ7LtGcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "gitHead": "c61d95f0f8bf0d47e4188ff65135161d33de1719", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "8.1.2", "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.3.0.tgz_1505068904372_0.7383061291184276", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "redux-mock-store", "version": "1.4.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "cdc87650f5759f293588fecc9cac2b057d95190d", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.4.0.tgz", "integrity": "sha512-y+SGh/SONWwqs4DiyHjd0H6NMgz368wXDiUjSHuOnMEr4dN9PmjV6N3bNvxoILaIQ7zeVKclLyxsCQ2TwGZfEw==", "signatures": [{"sig": "MEUCIBCM7Zko9joIiPch7CHx1IGFgF8C/tsy8t9ZdmNHba/lAiEAtYUAxQSi6ieDaXzrb8RxyYGmMWgSSFTF81yaBxvTgwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "gitHead": "63ae1c583470aadd1818311231fd63d0f9d4f63e", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"lodash.isplainobject": "^4.0.6"}, "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.4.0.tgz_1514203476901_0.07307229936122894", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "redux-mock-store", "version": "1.5.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "ec51e6b46ce5ea67445fe65fd2fe769469ad36ed", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.5.0.tgz", "integrity": "sha512-8AN6ti8SeH28FZm/mz+E2sj2JoaFCDudswbeFrt4Rnbi4KmI/KXumbskY7caO52zAgsda+DIgKBc60MJJhnhbg==", "signatures": [{"sig": "MEYCIQC7OTOV0mYrnNc52o71lSQLKiHlE5DNkcpVB5iyrEQjPAIhAM7hbn84Bno/guZEMPEgzcLZmJThDjv6WolZhvmu2Dpu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index-cjs.js", "module": "src/index.js", "browser": "dist/index.min.js", "gitHead": "800961eb019db4234222dfafe4d31b29ebe5811f", "js:next": "src/index.js", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "build": "npm run build:commonjs && npm run build:umd && npm run build:umd:min", "pretest": "npm run lint", "build:umd": "cross-env BABEL_ENV=es NODE_ENV=development rollup -f umd -c -i src/index.js -o dist/index-umd.js", "prepublish": "rimraf lib && rimraf dist && npm run build", "build:umd:min": "cross-env BABEL_ENV=es NODE_ENV=production rollup -f umd -c -i src/index.js -o dist/index-umd.min.js", "build:commonjs": "cross-env BABEL_ENV=es NODE_ENV=production rollup -f cjs -c -i src/index.js -o dist/index-cjs.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "breaking changes in minor version", "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"lodash.isplainobject": "^4.0.6"}, "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "rollup": "^0.45.1", "standard": "^7.1.2", "babel-cli": "^6.11.4", "cross-env": "^5.0.1", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-env": "^1.6.1", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "babel-plugin-external-helpers": "^6.22.0"}, "peerDependencies": {"redux": "*"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.5.0.tgz_1516953987190_0.6639699046500027", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "redux-mock-store", "version": "1.5.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "fca4335392e66605420b5559fe02fc5b8bb6d63c", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.5.1.tgz", "integrity": "sha512-B+iZ98ESHw4EAWVLKUknQlop1OdLKOayGRmd6KavNtC0zoSsycD8hTt0hEr1eUTw2gmYJOdfBY5QAgZweTUcLQ==", "signatures": [{"sig": "MEUCIQD5HsP9WyJrizmaTQ8wSDFAh8dIvIGqkKK8Qc0fOlN6uwIgArEdVi+Nn7buxhJTUQZKfukt//aRmApFEwWsrMSy4gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index-cjs.js", "module": "dist/index-es.js", "browser": "dist/index.min.js", "gitHead": "eb7fe9d14b72c01741cd3787f9d373e5e9ba36e8", "js:next": "dist/index-es.js", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "build": "npm run build:cjs && npm run build:umd && npm run build:umd:min && npm run build:es", "pretest": "npm run lint", "build:es": "cross-env BABEL_ENV=es NODE_ENV=development rollup -f es -c -i src/index.js -o dist/index-es.js", "build:cjs": "cross-env BABEL_ENV=es NODE_ENV=production rollup -f cjs -c -i src/index.js -o dist/index-cjs.js", "build:umd": "cross-env BABEL_ENV=es NODE_ENV=development rollup -f umd -c -i src/index.js -o dist/index-umd.js", "prepublish": "rimraf lib && rimraf dist && npm run build", "build:umd:min": "cross-env BABEL_ENV=es NODE_ENV=production rollup -f umd -c -i src/index.js -o dist/index-umd.min.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "breaking changes in minor version", "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"lodash.isplainobject": "^4.0.6"}, "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "rollup": "^0.45.1", "standard": "^7.1.2", "babel-cli": "^6.11.4", "cross-env": "^5.0.1", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-env": "^1.6.1", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "babel-plugin-external-helpers": "^6.22.0"}, "peerDependencies": {"redux": "*"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store-1.5.1.tgz_1517211109102_0.08079403056763113", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "redux-mock-store", "version": "1.5.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "1f10528949b7ce8056c2532624f7cafa98576c6d", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.5.3.tgz", "fileCount": 7, "integrity": "sha512-ryhkkb/4D4CUGpAV2ln1GOY/uh51aczjcRz9k2L2bPx/Xja3c5pSGJJPyR25GNVRXtKIExScdAgFdiXp68GmJA==", "signatures": [{"sig": "MEQCIGdgYTdDi8GL749HmJkVgxvvcELlHd0YPa6PQ3shRVaUAiBSkfG8sA2IOBcR/LU7xDiy/B4Oc0Yra04uRTZm4/7ecQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIWpmCRA9TVsSAnZWagAAkFcP/AkIyWVs8KTATx+HoVzc\nfGIyhFyXljyJAYav3kOQLy4CPhcB1isyXcquXUcwkI1oFvlt4UzKUY0s/Omm\nBilpDqQ0o3zJLg3ShP9D5dJvM0DW3f8dYAElUdYsYT3B5nagGuuvNEnBjLOn\nDxByo88bBHpv7wR3aGtP/vdpSxM+I7OYpqoBh8tCBcnUV+QvscUmIvaIdj0A\nZshwReKYnd8Y6Xn8FXY+Td9xbTrhTR2su3vIyyKfbxprf9OaTt7rVFHOfJjF\nppicKtwCJgcNHqHkCURyNA/ZBpPmsIG99iBAARNajCLQM9sGLwUivNGr31uU\nrynJisrSAqVVVaKavGq38vKeRORfDZN3gi75yiVBuPZ1hlzRjaTEfYzHgM0x\n4Ilk9unw57IwZWQhSxfDcaBjAeA44cjSp7Du91ZBgxzlh8K6HtPHgONAl9c4\nZCoApaoVwTuoU3AyOP/TG+olfyTRmr+X0TS6C2ZH/0LZSxDv3OsBMBgWx0IC\nDAY1H9hd5U0njOZ8DKfO7ngmd6/ZELfeSApPrg+BDI5T+FN3UTBzYnWEw428\nEky5aFnZP63wP8p/w1ssPVrQ+NYSTygNeE4QRuaUA0uY4UtIc/O95zu492VG\nSqq5tmTp6E0p1IIO41C9PXw7a6evc9iMQh3GZTxQOjWqwAH/Z/H65+GFNN5w\n3I9l\r\n=WuvF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "63ae1c583470aadd1818311231fd63d0f9d4f63e", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"lodash.isplainobject": "^4.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.5.3_1528916581555_0.8386380770361554", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "redux-mock-store", "version": "1.5.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "redux-mock-store@1.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "dist": {"shasum": "90d02495fd918ddbaa96b83aef626287c9ab5872", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.5.4.tgz", "fileCount": 7, "integrity": "sha512-xmcA0O/tjCLXhh9Fuiq6pMrJCwFRaouA8436zcikdIpYWWCjU76CRk+i2bHx8EeiSiMGnB85/lZdU3wIJVXHTA==", "signatures": [{"sig": "MEUCIQDxReAIg+Na3zjWsDZkNxwaoqAR7axVqVtXTkifbhD2yAIgI2+DAEWNRpoThxaPUF6uoUzCZ+3nVg1PLDiK7dtB9AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8PJlCRA9TVsSAnZWagAAJOQP/1T9jtrex4vKv2aAJvo5\nGBNqXcgDhMoBTes29KGHPHGdDZzP5ipg7xTdrPXTZMUPSvTXLp2y6q88gqUa\nSzfaIBHrM0ekB9CI3ZPM21gtzeY40fln4nXBlkIndnm2xYfqXBisHGkyyy0n\nNMC+egXCJXrYXQSCUNNg68cJTv+ev2AxsKQw1dCCQK6nR2JM+3tDIdxbrJ28\njgvV978q+sEpdm3C15uS94ZBx/YiofW0XQLfChpT+eL2O3Sr8ETNJAQ/HEPM\nYr0Uk492OUwJpufDvdLw+ob3S0ty9NAf2nbnepBsoORidrSJSDqB/xvVVz6f\n7IyZ9TQZRw7kK+XLJOY0f0DfsNFsvpnQL5bXkftCMmus2L6YkN/lWOQ1Y0R+\nxwgkA7q3eQtc7tnDqppb+/xZbu1ifD0TPcQOBXMvTQMZUntq7s1No0MDU3Fz\nxALGHpsoH304hXFYO4ttkcS+xegpgy0i2TEKnOdVgM86lxVBBgxlNPmFFHAd\nCMvfeM0ktEz3ooFFiYin5OaQwnDcQrJGZ96/DXKloMbEnjVJkEvyaYzg1Rds\nZRDSXgh055VAwcB7rp7eDhhKjnZYnPTKiEqEK2/mJAoT/0+uFGRbFc49Rbx7\nxHs0yBqaBlOb/tE3jsL+2llgxLZx/oPRduCwmqsXS0d+aWC6tuZCWYFzcZFg\ndy0D\r\n=Awlj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6b14754642af5da39e63f1675bfed7221df096e8", "scripts": {"lint": "standard src/*.js test/*.js", "test": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "pretest": "npm run lint", "prepublish": "rimraf lib && babel src --out-dir lib"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arnaudbenard/redux-mock-store.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A mock store for testing your redux async action creators and middleware", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"lodash.isplainobject": "^4.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.3.3", "redux": "^3.0.4", "sinon": "^1.17.2", "expect": "^1.12.2", "rimraf": "^2.4.3", "standard": "^7.1.2", "babel-cli": "^6.11.4", "babel-core": "^6.13.2", "redux-thunk": "^2.0.1", "babel-preset-es2015": "^6.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/redux-mock-store_1.5.4_1576071781521_0.9641253011431441", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "redux-mock-store", "version": "1.5.5", "description": "A mock store for testing your redux async action creators and middleware", "main": "dist/index-cjs.js", "module": "dist/index-es.js", "js:next": "dist/index-es.js", "browser": "dist/index-umd.min.js", "scripts": {"prepublish": "rimraf lib && rimraf dist && npm run build", "build:cjs": "babel src --out-file dist/index-cjs.js", "build:umd": "cross-env BABEL_ENV=es NODE_ENV=development rollup -f umd -c -i src/index.js -o dist/index-umd.js", "build:umd:min": "cross-env BABEL_ENV=es NODE_ENV=production rollup -f umd -c -i src/index.js -o dist/index-umd.min.js", "build:es": "cross-env BABEL_ENV=es NODE_ENV=development rollup -f es -c -i src/index.js -o dist/index-es.js", "build": "npm run build:umd && npm run build:umd:min && npm run build:es && npm run build:cjs", "lint": "standard src/*.js test/*.js", "pretest": "npm run lint", "test": "npm run build && npm run test:unit && npm run test:cjs && npm run test:es", "test:unit": "mocha --compilers js:babel-core/register --reporter spec test/*.js", "test:cjs": "mocha --reporter spec test/builds/cjs.js", "test:es": "mocha --compilers js:babel-core/register --reporter spec test/builds/es.js"}, "repository": {"type": "git", "url": "git+https://github.com/arnaudbenard/redux-mock-store.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "devDependencies": {"babel-cli": "^6.11.4", "babel-core": "^6.13.2", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-env": "^1.6.1", "cross-env": "^5.0.1", "expect": "^1.12.2", "mocha": "^2.3.3", "redux": "^3.0.4", "redux-thunk": "^2.0.1", "rimraf": "^2.4.3", "rollup": "^0.45.1", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-uglify": "^2.0.1", "sinon": "^1.17.2", "standard": "^7.1.2"}, "dependencies": {"lodash.isplainobject": "^4.0.6"}, "peerDependencies": {"redux": "*"}, "prettier": {"singleQuote": true, "semi": false, "trailingComma": "none"}, "_id": "redux-mock-store@1.5.5", "_integrity": "sha512-YxX+ofKUTQkZE4HbhYG4kKGr7oCTJfB0GLy7bSeqx86GLpGirrbUWstMnqXkqHNaQpcnbMGbof2dYs5KsPE6Zg==", "_resolved": "/Users/<USER>/redux-mock-store/redux-mock-store-1.5.5.tgz", "_from": "file:redux-mock-store-1.5.5.tgz", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-YxX+ofKUTQkZE4HbhYG4kKGr7oCTJfB0GLy7bSeqx86GLpGirrbUWstMnqXkqHNaQpcnbMGbof2dYs5KsPE6Zg==", "shasum": "ec3676663c081c4ca5a6a14f1ac193b56c3220eb", "tarball": "https://registry.npmjs.org/redux-mock-store/-/redux-mock-store-1.5.5.tgz", "fileCount": 9, "unpackedSize": 45139, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCGI5MFvwLAPfCVodisSvw65nFn/VEszBsy95CrJ5qGwIgeJictT89Nwq47XAhaNpTb0HrXjchaLe9Jtzl7FKNXWM="}]}, "_npmUser": {"name": "eskimojo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/redux-mock-store_1.5.5_1729511868315_0.6529693176598075"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-10-27T10:35:12.707Z", "modified": "2024-10-21T11:57:48.705Z", "0.0.1": "2015-10-27T10:35:12.707Z", "0.0.2": "2015-10-27T10:41:21.987Z", "0.0.3": "2015-12-02T09:03:54.993Z", "0.0.4": "2015-12-19T08:16:35.689Z", "0.0.5": "2016-01-08T14:20:44.137Z", "0.0.6": "2016-01-08T14:31:00.645Z", "1.0.0": "2016-02-25T15:11:28.462Z", "1.0.1": "2016-03-10T21:06:43.372Z", "1.0.2": "2016-03-10T21:11:56.044Z", "1.0.3": "2016-05-20T08:24:37.760Z", "1.0.4": "2016-05-27T13:36:39.821Z", "1.1.0": "2016-06-05T11:51:50.152Z", "1.1.1": "2016-06-15T13:58:11.484Z", "1.1.2": "2016-06-29T21:32:07.442Z", "1.1.3": "2016-08-18T15:27:41.415Z", "1.1.4": "2016-08-18T15:41:35.981Z", "1.2.0": "2016-09-06T16:26:58.332Z", "1.2.1": "2016-09-24T13:24:58.595Z", "1.2.2": "2017-01-30T15:07:49.593Z", "1.2.3": "2017-04-09T16:58:14.765Z", "1.3.0": "2017-09-10T18:41:45.407Z", "1.4.0": "2017-12-25T12:04:37.819Z", "1.5.0": "2018-01-26T08:06:27.257Z", "1.5.1": "2018-01-29T07:31:49.207Z", "1.5.3": "2018-06-13T19:03:01.605Z", "1.5.4": "2019-12-11T13:43:01.630Z", "1.5.5": "2024-10-21T11:57:48.526Z"}, "bugs": {"url": "https://github.com/arnaudbenard/redux-mock-store/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/arnaudbenard/redux-mock-store#readme", "repository": {"type": "git", "url": "git+https://github.com/arnaudbenard/redux-mock-store.git"}, "description": "A mock store for testing your redux async action creators and middleware", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "eskimojo", "email": "<EMAIL>"}], "readme": "# Deprecation notice\n\nThe Redux team does not recommend testing using this library. Instead, see our [docs](https://redux.js.org/usage/writing-tests) for recommended practices, using a real store.\n\nTesting with a mock store leads to potentially confusing behaviour, such as state not updating when actions are dispatched. Additionally, it's a lot less useful to assert on the actions dispatched rather than the observable state changes.\n\nYou can test the entire combination of action creators, reducers, and selectors in a single test, for example:\n\n```js\nit('should add a todo', () => {\n  const store = makeStore() // a user defined reusable store factory\n\n  store.dispatch(addTodo('Use Redux'))\n\n  expect(selectTodos(store.getState())).toEqual([\n    { text: 'Use Redux', completed: false }\n  ])\n})\n```\n\nThis avoids common pitfalls of testing each of these in isolation, such as mocked state shape becoming out of sync with the actual application.\n\n# redux-mock-store [![Circle CI](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master.svg?style=svg)](https://circleci.com/gh/arnaudbenard/redux-mock-store/tree/master)\n\n![npm](https://nodei.co/npm/redux-mock-store.png?downloads=true&downloadRank=true&stars=true)\n\nA mock store for testing Redux async action creators and middleware. The mock store will create an array of dispatched actions which serve as an action log for tests.\n\nPlease note that this library is designed to test the action-related logic, not the reducer-related one. In other words, it does not update the Redux store. If you want a complex test combining actions and reducers together, take a look at other libraries (e.g., [redux-actions-assertions](https://github.com/redux-things/redux-actions-assertions)). Refer to issue [#71](https://github.com/arnaudbenard/redux-mock-store/issues/71) for more details.\n\n## Install\n\n```bash\nnpm install redux-mock-store --save-dev\n```\n\nOr\n\n```bash\nyarn add redux-mock-store --dev\n```\n\n## Usage\n\n### Synchronous actions\n\nThe simplest usecase is for synchronous actions. In this example, we will test if the `addTodo` action returns the right payload. `redux-mock-store` saves all the dispatched actions inside the store instance. You can get all the actions by calling `store.getActions()`. Finally, you can use any assertion library to test the payload.\n\n```js\nimport configureStore from 'redux-mock-store' //ES6 modules\nconst { configureStore } = require('redux-mock-store') //CommonJS\n\nconst middlewares = []\nconst mockStore = configureStore(middlewares)\n\n// You would import the action from your codebase in a real scenario\nconst addTodo = () => ({ type: 'ADD_TODO' })\n\nit('should dispatch action', () => {\n  // Initialize mockstore with empty state\n  const initialState = {}\n  const store = mockStore(initialState)\n\n  // Dispatch the action\n  store.dispatch(addTodo())\n\n  // Test if your store dispatched the expected actions\n  const actions = store.getActions()\n  const expectedPayload = { type: 'ADD_TODO' }\n  expect(actions).toEqual([expectedPayload])\n})\n```\n\n### Asynchronous actions\n\nA common usecase for an asynchronous action is a HTTP request to a server. In order to test those types of actions, you will need to call `store.getActions()` at the end of the request.\n\n```js\nimport configureStore from 'redux-mock-store'\nimport thunk from 'redux-thunk'\n\nconst middlewares = [thunk] // add your middlewares like `redux-thunk`\nconst mockStore = configureStore(middlewares)\n\n// You would import the action from your codebase in a real scenario\nfunction success() {\n  return {\n    type: 'FETCH_DATA_SUCCESS'\n  }\n}\n\nfunction fetchData() {\n  return (dispatch) => {\n    return fetch('/users.json') // Some async action with promise\n      .then(() => dispatch(success()))\n  }\n}\n\nit('should execute fetch data', () => {\n  const store = mockStore({})\n\n  // Return the promise\n  return store.dispatch(fetchData()).then(() => {\n    const actions = store.getActions()\n    expect(actions[0]).toEqual(success())\n  })\n})\n```\n\n### API\n\n```js\nconfigureStore(middlewares?: Array) => mockStore: Function\n```\n\nConfigure mock store by applying the middlewares.\n\n```js\nmockStore(getState?: Object,Function) => store: Function\n```\n\nReturns an instance of the configured mock store. If you want to reset your store after every test, you should call this function.\n\n```js\nstore.dispatch(action) => action\n```\n\nDispatches an action through the mock store. The action will be stored in an array inside the instance and executed.\n\n```js\nstore.getState() => state: Object\n```\n\nReturns the state of the mock store.\n\n```js\nstore.getActions() => actions: Array\n```\n\nReturns the actions of the mock store.\n\n```js\nstore.clearActions()\n```\n\nClears the stored actions.\n\n```js\nstore.subscribe(callback: Function) => unsubscribe: Function\n```\n\nSubscribe to the store.\n\n```js\nstore.replaceReducer(nextReducer: Function)\n```\n\nFollows the Redux API.\n\n### Old version (`< 1.x.x`)\n\nhttps://github.com/arnaudbenard/redux-mock-store/blob/v0.0.6/README.md\n\n### Versions\n\nThe following versions are exposed by redux-mock-store from the `package.json`:\n\n- `main`: commonJS Version\n- `module`/`js:next`: ES Module Version\n- `browser` : UMD version\n\n## License\n\nThe MIT License\n", "readmeFilename": "README.md", "users": {"jk6": true, "dwqs": true, "novo": true, "knoja4": true, "quanru": true, "raciat": true, "chenkan": true, "juanf03": true, "nilz3ro": true, "bonashen": true, "xuyongli": true, "devalphac": true, "sergiodxa": true, "zeroth007": true, "aaronwells": true, "incendiary": true, "leonardorb": true, "abuddington": true, "coolhanddev": true, "jamesscaggs": true, "shawndsouza": true, "kirangopaluni": true, "derrickbeining": true, "scott.m.sarsfield": true}}