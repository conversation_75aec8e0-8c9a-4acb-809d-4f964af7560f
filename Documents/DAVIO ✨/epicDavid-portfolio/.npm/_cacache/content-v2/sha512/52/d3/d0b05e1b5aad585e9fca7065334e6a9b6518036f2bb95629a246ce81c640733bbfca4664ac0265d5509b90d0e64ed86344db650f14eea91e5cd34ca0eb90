{"_id": "css.escape", "_rev": "16-19bed66bbe46f5ca53fcf83d25406fe6", "name": "css.escape", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "dist-tags": {"latest": "1.5.1"}, "versions": {"0.1.0": {"name": "css.escape", "version": "0.1.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "http://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js"}, "_id": "css.escape@0.1.0", "dist": {"shasum": "00eceab1dcb585b21884ba80a917a3bb27bc6bf5", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-0.1.0.tgz", "integrity": "sha512-ZrsGh2mFymXFlYzT2MXCed0tKPZuUpjnytNnF7/FmbWuDUIJRXLcrxzqZ6lD5UXUlveGegCO3u1be8dNbrCuVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUkFXJtNX2hX9CpfOp4RPRZGWeMhx+IPJ0j2dfrAQQawIga9+vU1TdPG8qIY950kDQHUWKr0okvMx+hnUOsO3Z73Y="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.2.0": {"name": "css.escape", "version": "0.2.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "http://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js"}, "_id": "css.escape@0.2.0", "dist": {"shasum": "966604de1c191d8e1b5ce3e535b4a490631b3b39", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-0.2.0.tgz", "integrity": "sha512-vR9aQbpwolu380oD8IEYPY7cLvsabPqUl8ydWEnRbv3in9qJwfW5dAT5fpB+hxMQuc2KMy5JIJKrARLwJsOwLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQ+MU3hgEEMZ5VlCBmnx57waxJ7t58IFvbc5fO9YLWIwIgLFVLq7XaU7bqhiMC6Jt1A6NgXCiW2uiQ43NieS9yh3s="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}]}, "0.2.1": {"name": "css.escape", "version": "0.2.1", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "http://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && cat coverage/lcov.info | coveralls; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.10.0", "istanbul": "^0.2.10"}, "gitHead": "86412c86956a168a98e3be94ceade2ca8c3107ee", "_id": "css.escape@0.2.1", "_shasum": "096c3a2883c5a0e8970270356aae4fd2b613ca50", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "096c3a2883c5a0e8970270356aae4fd2b613ca50", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-0.2.1.tgz", "integrity": "sha512-k9KT2ECSdII/G2QHkHnEezzSehRs9Q6NbKxiYdzAIZSCBIIL3QtFMNjzhdvyphXhN+QXNSTdjige7cG1/I3JEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKwEniud2XIM4FpuJqBCULvw4uelu+SoCWh8tS1MXTnAiEAqH9Tp76/O6sut6/Svhe9rYg+eFj/a6lqbwX035aQr+k="}]}}, "1.0.0": {"name": "css.escape", "version": "1.0.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info|coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.11"}, "gitHead": "dde779e52571a3efcef1be692714e9bf5f9c4ec7", "_id": "css.escape@1.0.0", "_shasum": "0d053f5fca73ebf86d097cffb737b7b3a7a3d12e", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "0d053f5fca73ebf86d097cffb737b7b3a7a3d12e", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.0.0.tgz", "integrity": "sha512-IAptqlCEwMiyhsysG8Id/9h0lig0LsFkaQDKIf+hO3LsjSvxnl7W3684ukrdENOcTsITkm3H4cW6dvx+Vp735A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBPuwN2rrpRoYzQiMzlFYQEGzYBVq7/mdt9NZDRvJb9wIhANz/bETFwoCr+86L4FgWz27WiEv7PZWiJOCrrsEY5F2d"}]}}, "1.1.0": {"name": "css.escape", "version": "1.1.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info|coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.11"}, "gitHead": "4a4e5c79cc7b32659cf6adff2b37e6e8dfe9123f", "_id": "css.escape@1.1.0", "_shasum": "0a834ddadaca8f39888e8d11967ed774381b35d1", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "0a834ddadaca8f39888e8d11967ed774381b35d1", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.1.0.tgz", "integrity": "sha512-McTCl+8gWIl2JDIbgEgYZahqq9TRvP7MdrIS+eDrLnVjP3guaThZt+Oq59SYfQ9cIk6Uvls3Urt7Hlh0Pk5XmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4naPZbz8caxKdpC6pwJtiOzKoa1K9noLqhfMybP8chgIgLDSnjTvEJsIf8IrQnafEPWawucAl6OJuE8JFAxBN7I4="}]}}, "1.2.0": {"name": "css.escape", "version": "1.2.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.4", "istanbul": "^0.4.1"}, "gitHead": "770c6b10df3c34e1691052ab052a64d08266010d", "_id": "css.escape@1.2.0", "_shasum": "b346cf3b6441ef36a519e2807d72bc229fcf51ef", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "b346cf3b6441ef36a519e2807d72bc229fcf51ef", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.2.0.tgz", "integrity": "sha512-4xDdbN54KudhVTn0jWAymIgvjOSnKqNDfEoJTNcksa4FlyFS+bUBMRO7jTH7ccuXQnLAZ4+avi/TMFxDA7DPLA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjtNUWXUssuN6RmmtA10YnCHH73e/LS/2YrobyPt4ZbAiAqIJtFTb006aF80hlqKVFQoyuElGqa8zQqIcxIHr0svg=="}]}}, "1.3.0": {"name": "css.escape", "version": "1.3.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.4", "istanbul": "^0.4.1"}, "gitHead": "5ce844931b7bfd7ce11558b69ebbe1e22384557b", "_id": "css.escape@1.3.0", "_shasum": "8c9e1acb44fe1205bc64cc7f9330d290ebc52baa", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "8c9e1acb44fe1205bc64cc7f9330d290ebc52baa", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.3.0.tgz", "integrity": "sha512-sVDW3qrG4eh7vLq+XSzhm8So7VD1FPSZvb+L7FPx5Cc8H8wzZxBfQ9t+RMg/CXuRRFn0oyRAiz37uuS20zbv6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDae0uWMqQNA4GDStsYAn/r1aYvH5xZIHtI+3l0hrv3/AIgYso5/2mFtVVoLZlv607oN+mkQF3jHUbjPpvnfwDRL2Y="}]}}, "1.4.0": {"name": "css.escape", "version": "1.4.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.4", "istanbul": "^0.4.1"}, "gitHead": "88db099fc3990bc4a3d102843ddaeacbd91b6635", "_id": "css.escape@1.4.0", "_shasum": "e8c912c516eb8e89f685c3127563c9c490faf319", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "e8c912c516eb8e89f685c3127563c9c490faf319", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.4.0.tgz", "integrity": "sha512-bvBPqEOH4WrKTcZTBgmZRpcoIG+Lz2SqAUoUH24RwcTZRGswdsC1mlsvormkD5xpHkHkimyEQKgG9jEhIirNGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHEm57smK7lKa+T3MgOVvCWes3jV1AAkiENeLTPaF1EAiAiYU7wQakTK+7kfEq5fnB/dzuV93TGqc3wnpNXgFHVcA=="}]}}, "1.5.0": {"name": "css.escape", "version": "1.5.0", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.4", "istanbul": "^0.4.1"}, "gitHead": "ff41bbb0702cdb4ca3e7202cc123b24bd472e1c0", "_id": "css.escape@1.5.0", "_shasum": "95984d7887ce4ca90684e813966f42d1ef87ecea", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "95984d7887ce4ca90684e813966f42d1ef87ecea", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.5.0.tgz", "integrity": "sha512-UZluSVLjHop18iaqSK+Q+h7Abw5eKMrJXdR14cfC4IHtlzlZrSndebyLM2pufM+MtNcY/yw1yYDW6FprF+Rhkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEJ9LAkqLekWeXZ3hWE6eMtldKuVAn3KuH2l6KzT4fAsAiEAhJKayYvURYV3rmI4xRi2zImYSh/V52c7C7U08KObRkI="}]}}, "1.5.1": {"name": "css.escape", "version": "1.5.1", "description": "A robust polyfill for the `CSS.escape` utility method as defined in CSSOM.", "homepage": "https://mths.be/cssescape", "main": "css.escape.js", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "files": ["LICENSE-MIT.txt", "css.escape.js"], "scripts": {"test": "node tests/tests.js", "cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.4", "istanbul": "^0.4.1"}, "gitHead": "b036d330b93f58b1bc87f1097fdc822da58e090f", "_id": "css.escape@1.5.1", "_shasum": "42e27d4fa04ae32f931a4b4d4191fa9cddee97cb", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "42e27d4fa04ae32f931a4b4d4191fa9cddee97cb", "tarball": "https://registry.npmjs.org/css.escape/-/css.escape-1.5.1.tgz", "integrity": "sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDs/IY+XHZlzo7IzXt9tMlY0aDFZRDIekPaFWsj1dSWrgIhAPRDrPLhH7UZ463WWfdNZLOlH+LDhDUTV7wpmXHKPcmF"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/css.escape-1.5.1.tgz_1471982442953_0.7991812685504556"}}}, "readme": "# `CSS.escape` polyfill [![Build status](https://travis-ci.org/mathiasbynens/CSS.escape.svg?branch=master)](https://travis-ci.org/mathiasbynens/CSS.escape) [![Code coverage status](http://img.shields.io/coveralls/mathiasbynens/CSS.escape/master.svg)](https://coveralls.io/r/mathiasbynens/CSS.escape)\n\nA robust polyfill for [the `CSS.escape` utility method as defined in CSSOM](https://drafts.csswg.org/cssom/#the-css.escape%28%29-method).\n\nFor a more powerful alternative, consider using [cssesc](https://mths.be/cssesc), which automatically takes care of excessive whitespace, and has many options to customize the output.\n\n## Installation\n\nIn a browser:\n\n```html\n<script src=\"css.escape.js\"></script>\n```\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install css.escape\n```\n\nThen, in [Node.js](https://nodejs.org/):\n\n```js\nrequire('css.escape');\n\n// On Windows and on Mac systems with default settings, case doesn’t matter,\n// which allows you to do this instead:\nrequire('CSS.escape');\n```\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\nThis polyfill is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T04:58:45.143Z", "created": "2013-10-15T16:58:17.993Z", "0.1.0": "2013-10-15T16:58:20.626Z", "0.2.0": "2014-04-30T09:47:28.969Z", "0.2.1": "2014-06-01T06:46:28.402Z", "1.0.0": "2015-09-09T18:06:22.086Z", "1.1.0": "2015-09-10T10:25:10.328Z", "1.2.0": "2015-12-01T12:13:16.670Z", "1.3.0": "2015-12-01T17:09:00.790Z", "1.4.0": "2015-12-03T13:24:26.864Z", "1.5.0": "2016-01-21T09:48:37.689Z", "1.5.1": "2016-08-23T20:00:45.435Z"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/CSS.escape.git"}, "homepage": "https://mths.be/cssescape", "keywords": ["string", "unicode", "identifier", "css", "cssom", "polyfill"], "bugs": {"url": "https://github.com/mathiasbynens/CSS.escape/issues"}, "readmeFilename": "README.md", "license": "MIT"}