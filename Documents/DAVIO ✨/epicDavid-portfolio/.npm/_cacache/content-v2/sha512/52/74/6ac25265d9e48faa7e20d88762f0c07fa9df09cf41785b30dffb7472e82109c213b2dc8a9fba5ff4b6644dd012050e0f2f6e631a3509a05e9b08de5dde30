{"_id": "unicode-property-aliases-ecmascript", "_rev": "12-8b0aae365245be60feedd1dcffd64055", "name": "unicode-property-aliases-ecmascript", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "dist-tags": {"latest": "2.1.0"}, "versions": {"1.0.0": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.0", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.0", "unicode-canonical-property-names": "^2.0.1"}, "scripts": {"download": "curl http://unicode.org/Public/9.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "d5fcd67c9db43f2fff037c5b7adcc98e8cd4b791", "_id": "unicode-property-aliases-ecmascript@1.0.0", "_shasum": "76efde45c1bd80cc609c267e12edacb1dccd5dc6", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "76efde45c1bd80cc609c267e12edacb1dccd5dc6", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.0.tgz", "integrity": "sha512-rTDu3uR3OBsyjiyMN1UOyfPxHTqebjUjcXnA4aTkaRZ5azZ40hSqTjU4HWKYgo07Fo4lQXh7UuCnHO0I2uhyTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKmP+6QNts1V7H5R7PvgkhfzRU5cLxXR2bJpH+4J73sQIhAN7vK1X9mSOB4Vy96YEO3m0YG+VYzSO54jOnRBKpfXx6"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/unicode-property-aliases-ecmascript-1.0.0.tgz_1492245524259_0.07052018376998603"}, "directories": {}}, "1.0.1": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.1", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.0", "unicode-canonical-property-names": "^2.0.2"}, "scripts": {"download": "curl http://unicode.org/Public/9.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "2099ba37247e95dc6c857cc7d2fb6712817f5a8a", "_id": "unicode-property-aliases-ecmascript@1.0.1", "_shasum": "1ac78f48ac4d69c54f9142394b8a7749c3cfe2c9", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "1ac78f48ac4d69c54f9142394b8a7749c3cfe2c9", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.1.tgz", "integrity": "sha512-6YxSPRtAECqnbUe9iQSZ/sryb32pu13XTggtnd4mncuufkF3KZBgwiLgrz3G9Iu+PYt2vNmOMa6a97O93UxECQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHFI78mv2xzi6orzkhk4+5A3KlAKhCdXpNZYsDFLD5GyAiEA9hfMfgAyD+1Rmb81oKcKOeBai0xCiVMXeVi5tbXxmXI="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/unicode-property-aliases-ecmascript-1.0.1.tgz_1492251316923_0.9671671763062477"}, "directories": {}}, "1.0.2": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.2", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.0", "unicode-canonical-property-names-ecmascript": "^1.0.0"}, "scripts": {"download": "curl http://unicode.org/Public/9.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "6cc7763ac1fe1457b94be7447c758e8e87c5ca95", "_id": "unicode-property-aliases-ecmascript@1.0.2", "_shasum": "b495f544c79c2a85cf3083c907c884ef154be1a0", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "b495f544c79c2a85cf3083c907c884ef154be1a0", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.2.tgz", "integrity": "sha512-B9gnw/4s5+6aV/kfBavVONoUVYKIuxDEqAvacGvpuGCVO6kASOLll3RVZnCDPCfojMkFE7+Kz0+AwPc7K/YHkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDsXnb761kYSJBjbDWLRY3kzlv3ZhyfAZAJu647SwGovAiEApUmZH8TL7jVlh94kK7ZB3lfQZBTB68r48J2Da+4yy4g="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/unicode-property-aliases-ecmascript-1.0.2.tgz_1492263261321_0.059152766363695264"}, "directories": {}}, "1.0.3": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.3", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.1", "unicode-canonical-property-names-ecmascript": "^1.0.1"}, "scripts": {"download": "curl http://unicode.org/Public/10.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "696ce30dedfcb75778b228ac783140104b5e6bbd", "_id": "unicode-property-aliases-ecmascript@1.0.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-TdDmDOTxEf2ad1g3ZBpM6cqKIb2nJpVlz1Q++casDryKz18tpeMBhSng9hjC1CTQCkOV9Rw2knlSB6iRo7ad1w==", "shasum": "ac3522583b9e630580f916635333e00c5ead690d", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4XIH/auyB4irQCk2rece0i/ETNh9Gqb3M9lp1AV2+0gIhAI8LpDC3lKe34D0BDtnvPloB+e8Z1GjoC3yn99Xgio5n"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript-1.0.3.tgz_1497977174955_0.4154967039357871"}, "directories": {}}, "1.0.4": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.4", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.1", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "scripts": {"download": "curl http://unicode.org/Public/11.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "420e1028eb75cf09ebd0c90beae2174304e7c5bd", "_id": "unicode-property-aliases-ecmascript@1.0.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2WSLa6OdYd2ng8oqiGIWnJqyFArvhn+5vgx5GTxMbUYjCYKUcuKS62YLFF0R/BDGlB1yzXjQOLtPAfHsgirEpg==", "shasum": "5a533f31b4317ea76f17d807fa0d116546111dd0", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.4.tgz", "fileCount": 4, "unpackedSize": 5213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGSzRCRA9TVsSAnZWagAAw34P/Akl5dBWkc0oxLALKC+W\nPTypjZhh0moL5O907u6pQ0xizgQ9eDvj+vlHckdAT0RVqlVouPWcQEAMA+5O\nZPaX0XZkH4ZsRhE6Fctzr4NrdetWtiQeTwo+ysAmzMJZt/K9Cc//TSNtWAyi\neX7X715OaK+I5+2LfwgmYP1GhKv0CaX2A9wqOGruuHPr95qYUwjlJSX0Qzfy\nB1VEmbBtVH1Ceqoz6hKew4wcgTtB/XxQkb4+pumFly1RMF8wOggBcAbiTbnm\npvC1pCjjJYd4VOQfu+yhqt8GkUbHnjan6bqj+Yokba/2HHEVWx/QgV4nxrY1\nta3025Yr3y2Q0/HugvkYLgKt/+J76U9Xmu8ZOTh36RYiyztiAVgTSWGeelzN\nXjCDwWuJbfbimoEilrnb/zzCCXTf04OaWFF2lP6ArdaBRRjRJF6GyS/z1vTr\nY7khfvbJ2Nh/PDNmqSbHP1jd4C1aZtjURauUq8DfWJ1KSxK1DW/Ohk+VhIOA\nxqFlZVgvZ8zz0QxN7amzwEl8mZwXwfl8QS/7V+ubXs5646RlvthX1R8yN7vB\nBM3WUZoRFk5/EMGzRDbrNYaECUgtEpKoiuKGuaQbBCwTJvPrYztIiqkF6iDp\nFagYNeKIFA2xJUYIKXAcagLGaIc9fVPMCrHLq9x+Ln/XZMO9mvblYaAwcw3O\n7gd+\r\n=S3Af\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuT8r78+ldhFRImql7i37YoLsSDs/3prreFg5aCxHCEwIgdNbuI2FHh1SshrRBq1A2KHdcaYgg1KywS07eZxfMid0="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript_1.0.4_1528376529551_0.26789073416188014"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "unicode-property-aliases-ecmascript", "version": "1.0.5", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.2", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "scripts": {"download": "curl http://unicode.org/Public/12.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava ./tests"}, "gitHead": "fcb22919348de77018c1719e6f0603af1bd82692", "_id": "unicode-property-aliases-ecmascript@1.0.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L5RAqCfXqAwR3RriF8pM0lU0w4Ryf/GgzONwi6KnL1taJQa7x1TCxdJnILX59WIGOwR57IVxn7Nej0fz1Ny6fw==", "shasum": "a9cc6cc7ce63a0a3023fc99e341b94431d405a57", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.5.tgz", "fileCount": 4, "unpackedSize": 5213, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfmq3CRA9TVsSAnZWagAADbEQAIZbDB6dG3t1CKLpeMHY\nswu+EzhuotKc0KrLqeAuRrq0YR6xwuwFPMP3kI6QY4VJFGUTzWD19dzpK0O+\nTVtmTecRVOSJ7iHBbFlQGRz9lT/aF6s/kWaKgxJEp9vyDC13Z/xHafAetOHf\npwPrFazAzmY0JbRGq4vOfgWFuYDg95SFe+217sdiv9ep7A5NacQ7DETtP9A3\nmH04D3IolPDGHRiGoihpfDMaGBd+UUyGZ2CglYRI1g5NzzfrVr0ZYs6qNf2l\nSEJ3Evdgyjckb6jw5u<PERSON><PERSON>z<PERSON>ZPcVreGBbEuGqNOLN0fpc/1NSmhvN2d9U9JCLq\nr++MA69IhRIihuE7/vpj3QKCEG1XDtyjcuJO2KqA0hbqmjG45t8a9O7EGqGu\nZ9waFXtj6lDk3cN3OnzCiXAKxz+OEJPphYChXDXQd6CKGYa+Vpntj3CyNjtB\n77If4CgxMM5TtUxHn7d/ok4ZSf5cTba3g5E0RqUZFQbQcaWqx7BMJ9CQvH1n\nBHiXO6FR18toxcfEDD0+5djlL5zP3U6AVRUk988zw6/fwL4EZY783Jfoacjq\n5yU6V97Mre4H1uRPB6HDcjh7BVceAGvyBKkxblEBH9143pWoaif67iXKGaNz\nNn8d4np0zqN5EHIDSeqNTK4bxC0+aH4utq3zFPOTL0wg7OryiH0pMfhMh9aP\nj43y\r\n=eHlh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpmH9/DwRyqYEP09J3Z94+SJ4EYdUjH0u8kX0DuPP3UAIgGKtZYvBTOhy5pQXwBtoVbd81rvRl2cvoLxyCXWpALHc="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript_1.0.5_1551788726889_0.11119758280755954"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "unicode-property-aliases-ecmascript", "version": "1.1.0", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.2", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "scripts": {"download": "curl http://unicode.org/Public/13.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava tests/tests.js"}, "gitHead": "280d93611858e427f96d5caf4b8038d5e0e47e89", "_id": "unicode-property-aliases-ecmascript@1.1.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-PqSoPh/pWetQ2phoj5RLiaqIk4kCNwoV3CI+LfGmWLKI3rE3kl1h59XpX2BjgDrmbxD9ARtQobPGU1SguCYuQg==", "shasum": "dd57a99f6207bedff4628abefb94c50db941c8f4", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz", "fileCount": 4, "unpackedSize": 5409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaRgaCRA9TVsSAnZWagAAAJcP/ids0Wzsl8uWxYKL85hn\njgXtzS2ECrDgAYwCy31C1GJyDY0VnOVSuNFzqPmkUyPTVGdtjjb9PoQsHt0B\nZipyTd7X1mZXBPsadQQmb/s4QHvNLy6ywbpjrK3RKHSKGLhcS2m/1nlEYplu\nm51x96fHNmcCRmT4xj1rHBQvp0Urq7OKqd+1gdIVBPR2K83fsfUiurV0D3uR\n7T1vPDj8Yp6di34ABhGAsdQ5KQqNSaQYHTIGRFkXN9+SNm8MskF74OEWSHWF\n7Jcgf4ocCISmouLwjCGsgZfQl1rBHDkFE2khHty6MFPM6jmkNSDNiMt3cooY\nbidF//s4zmKYJHk6zOQqqP8C1HUbHe1IgC6+p/5zvXttqdGoB6MBnE4kkfRE\nJmssRWMA93TAHlPJjX8hbotbKGSmebVIyyGBeeHtmpeh1DsGyqmSqMLjfiNA\nXdQUMV1REUbfgR1UvEWWUN+0MPYb/VGuNVG0Mg4i9pcle2d6P3aoJLPoL9J1\nr2uSkGQIiM8o5rv6pllWVEpEI4iogr3520fwU6wGc88BL9eVEj2qhKw1rIr0\nS18qM9rgB/z8Jn+QUzfX5ImswfHiMxDDZZnT9XhH9LdtLdxwteZJs9dm6IuK\nR79wNMgDafeOCQT/pBaUg06pggbqJopWFpSjYSid16+M3spwfCOrjWq/7ZUP\nGit8\r\n=Ye8g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDF3F2rcZ/WJEjowyM9PV/v1wDAFRV5viNR3PEP1yPDkgIhAM1cHEkrakkeBShs63hhOz7WRww1lR0H5k8fCj9hn1Q2"}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript_1.1.0_1583945753637_0.929154673423767"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "unicode-property-aliases-ecmascript", "version": "2.0.0", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^2.5.2", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "scripts": {"download": "curl http://unicode.org/Public/14.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava tests/tests.js"}, "gitHead": "5547c3d5ab64c20b0e5951a1580760b34102571a", "_id": "unicode-property-aliases-ecmascript@2.0.0", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ==", "shasum": "0a36cb9a585c4f6abd51ad1deddb285c165297c8", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQGHsCRA9TVsSAnZWagAANIwP/1TLjDRxTKSoePLx9I7I\nI9BvVEplc9lRLhr+6jmvpMzO1v8CoJaafNL28iHnNKLlr/s5IWAxRlMrkryx\ncOH7ACpipidwRCoA/9AFvYRegtZ2zYdHp4aF1WZRwy9j7UkNRjkm5XWK6XFd\nLzQfqHtzGE88BZQPx36ogfnLzf1qe54Hxe4j+t+O8xDEg9Ii6qmbUuoxvwcl\n3LUEmb9DGJG5NOauso9D0z7gFbCTfLI0j202MAGiLojrd9dNYhpHZkWHZXDR\nWsjvuuq4Of4KStcwrk9NZaMcqBkwxyViwNcFnyFc+qgQNVRb/xQcUrMXSMWP\ngl457b44K8CorTOVHq5Ea5+jjJMNKS2+OloBRtTrekiqb0xhBL8f/+8iMbyQ\nkSDY45gEfZkXLH0MGqsYa1op3O80v/PRysPtXkfFU7SR2TUB3P5qZmdZcTL3\nRrWE62tZcIvz665a2lPD3r8nP/gEzCxMrdfN1XjwFe2oNE4Ck0vk8k3mwmQ3\nM3zZ5xveGh52j8n+md9kiHJ1n780CZFE059ty2k/isQSDc1GOhJ8QqMcxzvz\nhj5W9tzsO0oCRmFbYjwp2s9BnhLZsi/JjCYi3qYAIoTH6gc3RPFgGX2Qv9CE\nUKCwQTZcQbi+DFlFKFx3aBAYdbqnqSTWLf9DET//tOcUArvOdINek1TZCSFv\nfLu6\r\n=JZUk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICQKnuPdz5RmWZ7XgHgq39Ty0+j7kdSmVjmBHuJiOU2nAiEAlRgixPMQEExoCfXSQDC286+nUMzm14+Cgy9Bwr9FL80="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript_2.0.0_1631609324204_0.08826096045318543"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "unicode-property-aliases-ecmascript", "version": "2.1.0", "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "main": "index.js", "engines": {"node": ">=4"}, "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "devDependencies": {"ava": "*", "jsesc": "^3.0.2", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "scripts": {"download": "curl http://unicode.org/Public/15.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "build": "node scripts/build.js", "test": "ava tests/tests.js"}, "gitHead": "a8d4655582563dbd190f6ce159f10a025a8f380f", "_id": "unicode-property-aliases-ecmascript@2.1.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==", "shasum": "43d41e3be698bd493ef911077c9b131f827e8ccd", "tarball": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "fileCount": 4, "unpackedSize": 5976, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+aB4lsc2Zxc6CJoBGUhq7SOEv3MQCG7tdBuOplJq84gIgMZuLgxVIB2Hw0bTVoV6n30qdk5RC4q7xWVW5CV5Q+Hg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIcM4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrjvg//ZuLWf1+LlpTh7160XMR/FCLjgm1xpGZOWa0XgBRk9HRG3dhb\r\njs3EQJEyQRhZa82SPCcAu7hugVHKVcYVlM2HgSHL0lF7nHfS0bSFkib/0DMJ\r\naTDb8jcHsaAJtsibmTsoMd1Y1zf+e7HBwxC/1JbzbTuj8XSm0j588Vzmx5al\r\ntGsJ/1QDRfVh86SkjgulCp53a1wA5mTg0ZMXZZCT5B8vdvktBzB+UhXWH/+g\r\n+dy4ZZgawxJXFf3HZMpX7FANNgf0jXsc74iBq5T27Zng/xGMrmNPFL5mqFdV\r\nULuJvSChRdUt2IZrdwmdrXaeO0dREjeZiNwOEN5CF+m7FIP25zuM0V2hEZ3k\r\nswPnXogvdH2f0/TchNjGUkMdh5xDpBzUCPQO6CYZejP6pM0oh44HVvKeuIPV\r\n8sbx+Sum/UlyVFILswIJDcV7l/PEYUUgTdesOTmxfnhM8GKzuDQRsFi9JUZb\r\nH9JLxJrXfQr0EVPc8wKVnNHMmPiLrjab6PYgqUCK4t/TY3/KTwkCmD1YfU2A\r\nUbj3PvA1fWo7PPHhJaSUcqFiffPrhgS6kaKE9oJRO4VaXh/Ova3ksq+Zg30M\r\nlrNx9Ml8fD6ut623zPUfIDX5d/2PCaZr2mVnkbTs7vC4NLhglkz/ZtU32eu6\r\nq5cqEgcvqt3Oye/Nofga665/fLCGlO1OaXs=\r\n=iXwt\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unicode-property-aliases-ecmascript_2.1.0_1663157048155_0.9378214855097151"}, "_hasShrinkwrap": false}}, "readme": "# unicode-property-aliases-ecmascript [![Build status](https://travis-ci.org/mathiasbynens/unicode-property-aliases-ecmascript.svg?branch=main)](https://travis-ci.org/mathiasbynens/unicode-property-aliases-ecmascript) [![unicode-property-aliases-ecmascript on npm](https://img.shields.io/npm/v/unicode-property-aliases-ecmascript)](https://www.npmjs.com/package/unicode-property-aliases-ecmascript)\n\n_unicode-property-aliases-ecmascript_ offers Unicode property alias mappings in an easy-to-consume JavaScript format. It only contains the Unicode property names that are supported in [ECMAScript RegExp property escapes](https://github.com/tc39/proposal-regexp-unicode-property-escapes).\n\nIt’s based on Unicode’s `PropertyAliases.txt`.\n\n## Installation\n\nTo use _unicode-property-aliases-ecmascript_ programmatically, install it as a dependency via [npm](https://www.npmjs.com/):\n\n```bash\n$ npm install unicode-property-aliases-ecmascript\n```\n\nThen, `require` it:\n\n```js\nconst propertyAliases = require('unicode-property-aliases-ecmascript');\n```\n\n## Usage\n\nThis module exports a `Map` object. The most common usage is to convert a property alias to its canonical form:\n\n```js\npropertyAliases.get('scx')\n// → 'Script_Extensions'\n```\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_unicode-property-aliases-ecmascript_ is available under the [MIT](https://mths.be/mit) license.\n", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "time": {"modified": "2022-09-14T12:04:08.420Z", "created": "2017-04-15T08:38:46.124Z", "1.0.0": "2017-04-15T08:38:46.124Z", "1.0.1": "2017-04-15T10:15:18.753Z", "1.0.2": "2017-04-15T13:34:22.097Z", "1.0.3": "2017-06-20T16:46:15.907Z", "1.0.4": "2018-06-07T13:02:09.658Z", "1.0.5": "2019-03-05T12:25:27.069Z", "1.1.0": "2020-03-11T16:55:53.749Z", "2.0.0": "2021-09-14T08:48:44.530Z", "2.1.0": "2022-09-14T12:04:08.346Z"}, "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "license": "MIT", "readmeFilename": "README.md"}