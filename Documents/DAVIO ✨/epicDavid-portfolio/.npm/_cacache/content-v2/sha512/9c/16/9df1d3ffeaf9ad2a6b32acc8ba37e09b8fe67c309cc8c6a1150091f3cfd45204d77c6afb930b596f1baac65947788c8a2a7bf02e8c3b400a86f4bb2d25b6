{"_id": "min-indent", "_rev": "5-d8654634bcd642cf736cc7bb8cc0b0f8", "name": "min-indent", "description": "Get the shortest leading whitespace from lines in a string", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "min-indent", "version": "1.0.0", "description": "Get the shortest leading whitespace from lines in a string", "main": "index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/thejameskyle/min-indent.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "thejameskyle.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["indent", "indentation", "normalize", "whitespace", "space", "tab", "string", "str", "min", "minimum"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "867a9060c96c16997b349b565d7ebcab142967d7", "bugs": {"url": "https://github.com/thejameskyle/min-indent/issues"}, "homepage": "https://github.com/thejameskyle/min-indent#readme", "_id": "min-indent@1.0.0", "_shasum": "cfc45c37e9ec0d8f0a0ec3dd4ef7f7c3abe39256", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "thejameskyle", "email": "<EMAIL>"}, "dist": {"shasum": "cfc45c37e9ec0d8f0a0ec3dd4ef7f7c3abe39256", "tarball": "https://registry.npmjs.org/min-indent/-/min-indent-1.0.0.tgz", "integrity": "sha512-9S/HeNUFIrbRzMUEgY7G5lgXL9Az0Iqws+gJIZwngc0ENug51N8H/FvQCQ1Jvq35sBxyt5ngqeOqYl0ILoEAFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGg37XhKb3wEqqYRu3U4XCYUuzp9fdL2nOzRq+J5Ic4mAiEAqpmeXakGaU0KCwDIW+sQtbK+ronQwOt5goelaPMH7HQ="}]}, "maintainers": [{"name": "thejameskyle", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/min-indent-1.0.0.tgz_1509073979689_0.7262920024804771"}, "directories": {}}, "1.0.1": {"name": "min-indent", "version": "1.0.1", "description": "Get the shortest leading whitespace from lines in a string", "main": "index.js", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/thejameskyle/min-indent.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "thejameskyle.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "keywords": ["indent", "indentation", "normalize", "whitespace", "space", "tab", "string", "str", "min", "minimum"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "f0d5c6c309c964b67c41cd8bf28e6f58058fdc64", "bugs": {"url": "https://github.com/thejameskyle/min-indent/issues"}, "homepage": "https://github.com/thejameskyle/min-indent#readme", "_id": "min-indent@1.0.1", "_nodeVersion": "14.0.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==", "shasum": "a63f681673b30571fbe8bc25686ae746eefa9869", "tarball": "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz", "fileCount": 4, "unpackedSize": 2969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0A7mCRA9TVsSAnZWagAA5B4QAIGwDtT7O6jlUY/eBM0f\nbYP9uHambmC7kiAH5rGlYBsH/rMZ0h7CWtUFxj25zOTqPuaQrgtkDaOa9QVg\n6fW46LXzmSxTqTM7SbcNb1QEydwazsE2lCC1PzaJoL50yM/u6yt8OE+RIKTz\nol/RoAy03TI8RzEu5Q6ayGJ7n2IXUxSXLyTBRMI3RFbKcyGTYAw362JqDoZs\nyLxMgQKcroV+5ay3Wy0/1qx0Jdqvdc//q4EbhQlcfyqtrA6Q0qy292HH/r/y\naLaetfNqKeJvVKRJFwMaO45IVERvKACmFy5CW7G8gzRbag1l1IDpke98vcac\nx0OI8w/OKUbhFG79586b5KFKhdm/07NW7afraoo0wq3y97Wcf6brbGD/VhZu\nF0Rbav0pGraa4Yg0c44f4vSeiYj26LKPmIvDbMrlrX96IMvei2eOEVNpoCkK\nbHK5FOWZ7/MD8qmy3pfiLeZPBDJ0l6TnpYRXZ0vdb/qWcNy3S/YtGXNeuDyv\nk9uiWvQSMr50XE8yU9sMhtHlJ6d9bv6eV1vZln8aa9pQoUTZb2Gx+87/K4wm\n81iGUpymLM3VfUgYYk9D15qiHgbFOBunyRE97+Vme+Frx7sEvcyi0b+C+o+d\nuSndXuUUp0/GlkRHUVzDR4z8QgAgb8hPeWk3mQS2f1iIrIOHlH5eBV5BJ8sn\nhDSz\r\n=cJh+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUTEaQrOFPgUoxCEI/tGbXDYgjfLe0Un2KZPWtELjr4gIhAKGrbeEXQvtTrUBqW5fL/ilhjEXp81qvE5ItWdiFpgN/"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}], "_npmUser": {"name": "thejameskyle", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/min-indent_1.0.1_1590693605903_0.7266404794655938"}, "_hasShrinkwrap": false}}, "readme": "# min-indent [![Build Status](https://travis-ci.org/thejameskyle/min-indent.svg?branch=master)](https://travis-ci.org/thejameskyle/min-indent)\n\n> Get the shortest leading whitespace from lines in a string\n\nThe line with the least number of leading whitespace, ignoring empty lines, determines the number.\n\nUseful for removing redundant indentation.\n\n\n## Install\n\n```\n$ npm install --save min-indent\n```\n\n\n## Usage\n\n```js\nconst minIndent = require('min-indent');\n\nconst str = '\\tunicorn\\n\\t\\tcake';\n/*\n\tunicorn\n\t\tcake\n*/\n\nminIndent(str); // 1\n```\n\n\n## Related\n\n- [strip-indent](https://github.com/sindresorhus/strip-indent) - Strip leading whitespace from each line in a string\n- [strip-indent-cli](https://github.com/sindresorhus/strip-indent-cli) - CLI for this module\n- [indent-string](https://github.com/sindresorhus/indent-string) - Indent each line in a string\n\n\n## License\n\nMIT © [<PERSON>](https://thejameskyle.com)\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T20:15:06.658Z", "created": "2017-10-27T03:12:59.773Z", "1.0.0": "2017-10-27T03:12:59.773Z", "1.0.1": "2020-05-28T19:20:06.032Z"}, "homepage": "https://github.com/thejameskyle/min-indent#readme", "keywords": ["indent", "indentation", "normalize", "whitespace", "space", "tab", "string", "str", "min", "minimum"], "repository": {"type": "git", "url": "git+https://github.com/thejameskyle/min-indent.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "thejameskyle.com"}, "bugs": {"url": "https://github.com/thejameskyle/min-indent/issues"}, "license": "MIT", "readmeFilename": "readme.md"}