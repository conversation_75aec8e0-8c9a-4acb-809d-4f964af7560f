{"_id": "path-type", "_rev": "13-13838e640d92383dd279d3362aa356eb", "name": "path-type", "dist-tags": {"latest": "6.0.0"}, "versions": {"1.0.0": {"name": "path-type", "version": "1.0.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "dist": {"shasum": "51b127d4884100f5808256e45d471716ba16f62d", "tarball": "https://registry.npmjs.org/path-type/-/path-type-1.0.0.tgz", "integrity": "sha512-6vHV8A6LCjbZZFxCc3D1BngIa4zlqLX89NKNpMRMrA8fVSVNZmWwyeW8SEvOhd+wAlhEnOm16lArD9bF/daNiw==", "signatures": [{"sig": "MEYCIQDq8ZVG17Svv9YasxS+tjXgpRvF4VLs6K1Od3DbkoqskgIhALIxlGJjQgdnR7eys7jxcxpwZi150z6oNSoneDYxXj1s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "51b127d4884100f5808256e45d471716ba16f62d", "engines": {"node": ">=0.10.0"}, "gitHead": "b29ddf1e71fe346a1034e3914efb70287e2ddf64", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/path-type", "type": "git"}, "_npmVersion": "2.11.3", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"pify": "^2.0.0", "graceful-fs": "^4.1.2", "pinkie-promise": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "1.1.0": {"name": "path-type", "version": "1.1.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "xo": {"ignores": ["test.js"]}, "dist": {"shasum": "59c44f7ee491da704da415da5a4070ba4f8fe441", "tarball": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz", "integrity": "sha512-S4eENJz1pkiQn9Znv33Q+deTOKmbl+jj1Fl+qiP/vYezj+S8x+J3Uo0ISrx/QoEvIlOaDWJhPaRd1flJ9HXZqg==", "signatures": [{"sig": "MEUCICGHtvvv9MIQKBGLxzZsesVrr5IQ7u1y5GRj9csMaJj9AiEAgEg0tgeYXV1/9oYnwCivKiZxLttJxQ20fSOUKLLw048=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "59c44f7ee491da704da415da5a4070ba4f8fe441", "engines": {"node": ">=0.10.0"}, "gitHead": "dff5c2a62f89efe7e0cce600bf38e76196d8b4b2", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/path-type", "type": "git"}, "_npmVersion": "2.14.7", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"pify": "^2.0.0", "graceful-fs": "^4.1.2", "pinkie-promise": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "2.0.0": {"name": "path-type", "version": "2.0.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type#readme", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "f012ccb8415b7096fc2daa1054c3d72389594c73", "tarball": "https://registry.npmjs.org/path-type/-/path-type-2.0.0.tgz", "integrity": "sha512-dUnb5dXUf+kzhC/W/F4e5/SkluXIFf5VUHolW1Eg1irn1hGWjPGdsRcvYJ1nD6lhk8Ir7VM0bHJKsYTx8Jx9OQ==", "signatures": [{"sig": "MEUCIQDURcQzWnC7ccVP71OVWpXJU0hzzsZfBg0cBLg1061gbgIgSC02Xuf8qzwCf0F9MwaMAmAXGyDI7WSB8+3r0sFuR7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f012ccb8415b7096fc2daa1054c3d72389594c73", "engines": {"node": ">=4"}, "gitHead": "ef08bdbd35fa01342ef6d80f2e8eb8b9c2cccc30", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-type.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"pify": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/path-type-2.0.0.tgz_1475051315257_0.8221027220133692", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "path-type", "version": "3.0.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type#readme", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "dist": {"shasum": "cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f", "tarball": "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz", "integrity": "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==", "signatures": [{"sig": "MEUCIQCGzzdSAuLy0CLWOmN0ljtQsB9E8hD8OGxS7BHE6pfMbAIgJeBDuefqqhvANebV164NxpFUEWHHB/jqyg4AeCfzta4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "a83cd9d8ad6560dadaa85ae3e677e5a3b58e3ee4", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-type.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/path-type-3.0.0.tgz_1499554857781_0.09718758845701814", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "path-type", "version": "4.0.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type#readme", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "dist": {"shasum": "84ed01c0a7ba380afe09d90a8c180dcd9d03043b", "tarball": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "signatures": [{"sig": "MEYCIQD+lu5NiqBtsCvpIBUTwtuSNQ6kbmB8x20uiPjCIMqyuwIhAJSxTs44qtAD7XhqegwdkT6iYrp+u1o3ldiqljnmg4WJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch3r+CRA9TVsSAnZWagAAXCwP/i3YXQCag+djGYx7vbEZ\nayJrjUO2kv8l82x9wmSuKMq/w2jXiYvWJd/WKrBOaRi+XSEd/ifBDubDY7xd\na9iCPiB8RIwQ935nv8s/yiGsNlus9hDd1BAS7/g0UvUZ4JTh3oT+aSQdgUqW\nDzGe1pxZXD1zeUuGsOB70k7DXuA+dt0LnK35SjJ57D8HYEDyIanoz6Ykq8iY\ntQnVHeREe4cbDg7CC/A5OaRIM9x22hOZ9HgNHPA7S3uMFRShAXI8peQFtUJw\nGe+4fjS7ZXJ7/3HGtzTd6h12DousvCDkEO8dg6eNARF2ra2krB++gdCEy4o3\neJTgRKM5205RFFSRSRO7kbT/dENs3I2n1Njo4fDNkoVgLC2MrWmSdHDWq5yz\n9unvgTA+YB2Fb2iDr9b9CVm5OhgeYGnYRdrBYYp4kZFDuqttokEVnHMEdbxw\nInX0//sctRcyUjgloIOTgyK9ivqwNEE8Td6rFtUcpstzH/s2N/7ml5uOwU2o\npL5zcp7j6fSmqtV+DoFwhnZqQqqnGp/Gid1sNtQEM7UBca0aRK5RDKU9ssJU\n/OlatxglCGsNv9uJhADn48IItHIVbIc9NdLbeq0JMgY4Rju1GVCT/3T4YavJ\ne0Hf/l5Wg+QYS+Wuq8UGTHoqd9nZJGh4jUClZQWz3G2PJfWwRIMD6Dh3BaCa\nYuv6\r\n=3kA7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "b60678846423629b1293955d88a41f7e1d87c338", "scripts": {"test": "xo && nyc ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-type.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.3.1", "nyc": "^13.3.0", "tsd-check": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-type_4.0.0_1552382718141_0.897837094933279", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "path-type", "version": "5.0.0", "keywords": ["path", "fs", "type", "is", "check", "directory", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-type@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-type#readme", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "dist": {"shasum": "14b01ed7aea7ddf9c7c3f46181d4d04f9c785bb8", "tarball": "https://registry.npmjs.org/path-type/-/path-type-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-5HviZNaZcfqP95rwpv+1HDgUamezbqdSYTyzjTvwtJSnIH+3vnbmWsItli8OFEndS984VT55M3jduxZbX351gg==", "signatures": [{"sig": "MEUCIEvBaWwuRFIFccI2DCalufqLZSANPbEjMBvITznB1A+AAiEA/zNzcqTX01oVROfWAU64eG0hCLWV390N8ZsvvKcvUtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEWIWCRA9TVsSAnZWagAAe0sP/jdaRw/8ha3K+YbSYnZ8\nnPP4T0d9cB0ko00oWEAD9AhDbl64O0/PBtui8K+Ytq8DS9/WtHliu8MzsSKC\nwwZbpMDxqMq5264YxSlVY2UIf7yaBJdvw4Jw1/a7zgHc0BzIZOoXIgk0Z4wQ\npPv4S2N+83emIrnReYDcEp55Omcr1tA7PAjrRlA0SYvmbmrs5xLoEEnT5GJu\nn+eZ0KrZodsdhvHogIp92JqKh78h01pQYqaKSEOKHSdy1G6DmzJHqZuspPnG\nffNRteTpzGNJYynms9pmZxVvuu7WdnnqkWdh5ACg3iHVDFbNHBa3F8IWJKJJ\nQia4oq5nBHyYSrrRIXa4seB+PYbcCCxSQd0NyDUv7QatYWeEUEfyl5ViMEsa\nUuT2HwFMKESHu0HlkgHt0kMHEx+cNoTbLljw22zK7HTqgVhmKIUPyyqCHowg\noKSLkZY6iN21QHu/jbuvSwTHmvnjnBrxCPmfloKCvnyfEDsAgxU7r4rw0KcD\nJKa5dke3Zf8xhwQUKjiCxU0J5e4m8mT8QWfKi41+jp6x4lpd+Z6huOUqE3M5\nZX+kW71dHSgoIAWriLvdMO/JprBIiy3uteHbTQ3kBmSsxOAS0gf3eaPBqqhf\nqK4vkVseZZ2KFbWA0VC0c2O2NOy5mdtxzGWl84HbVyu/i97E+zxVp4VFanCw\nS/77\r\n=09Z9\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "80853698ad527c10fc692e71981345762e22f69b", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-type.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Check if a path is a file, directory, or symlink", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.37.1", "ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-type_5.0.0_1611751958019_0.16773508773368095", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "path-type", "version": "6.0.0", "description": "Check if a path is a file, directory, or symlink", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-type.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["path", "fs", "type", "is", "check", "directory", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "devDependencies": {"ava": "^6.1.3", "nyc": "^17.0.0", "tsd": "^0.31.1", "xo": "^0.59.2"}, "_id": "path-type@6.0.0", "gitHead": "e772400aa20cc9371fd70943e731a36b30cf8950", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "homepage": "https://github.com/sindresorhus/path-type#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-Vj7sf++t5pBD637NSfkxpHSMfWaeig5+DKWLhcqIYx6mWQz5hdJTGDVMQiJcw1ZYkhs7AazKDGpRVji1LJCZUQ==", "shasum": "2f1bb6791a91ce99194caede5d6c5920ed81eb51", "tarball": "https://registry.npmjs.org/path-type/-/path-type-6.0.0.tgz", "fileCount": 5, "unpackedSize": 5328, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSmaOJOxY63iwLzOclSyOhGCb9/ABGoM8u2id3agttWAIgbhtFuYWweFSDewWCIMvInDetScTSStlYhrIY0SCP+xI="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-type_6.0.0_1721991279862_0.010225212537439576"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-09-04T08:35:55.517Z", "modified": "2024-07-26T10:54:40.159Z", "1.0.0": "2015-09-04T08:35:55.517Z", "1.1.0": "2015-11-14T19:39:01.645Z", "2.0.0": "2016-09-28T08:28:37.085Z", "3.0.0": "2017-07-08T23:00:58.771Z", "4.0.0": "2019-03-12T09:25:18.333Z", "5.0.0": "2021-01-27T12:52:38.113Z", "6.0.0": "2024-07-26T10:54:40.007Z"}, "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/path-type#readme", "keywords": ["path", "fs", "type", "is", "check", "directory", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-type.git"}, "description": "Check if a path is a file, directory, or symlink", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# path-type\n\n> Check if a path is a file, directory, or symlink\n\n## Install\n\n```sh\nnpm install path-type\n```\n\n## Usage\n\n```js\nimport {isFile} from 'path-type';\n\nconsole.log(await isFile('package.json'));\n//=> true\n```\n\n## API\n\n### isFile(path)\n\nCheck whether the passed `path` is a file.\n\nReturns a `Promise<boolean>`.\n\n#### path\n\nType: `string`\n\nThe path to check.\n\n### isDirectory(path)\n\nCheck whether the passed `path` is a directory.\n\nReturns a `Promise<boolean>`.\n\n### isSymlink(path)\n\nCheck whether the passed `path` is a symlink.\n\nReturns a `Promise<boolean>`.\n\n### isFileSync(path)\n\nSynchronously check whether the passed `path` is a file.\n\nReturns a `boolean`.\n\n### isDirectorySync(path)\n\nSynchronously check whether the passed `path` is a directory.\n\nReturns a `boolean`.\n\n### isSymlinkSync(path)\n\nSynchronously check whether the passed `path` is a symlink.\n\nReturns a `boolean`.\n", "readmeFilename": "readme.md", "users": {"huhgawz": true, "rocket0191": true, "flumpus-dev": true}}