{"_id": "uri-js", "_rev": "26-fdd92ba413af3f135ccf356912527d42", "name": "uri-js", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "dist-tags": {"latest": "4.4.1"}, "versions": {"1.4.0": {"name": "uri-js", "version": "1.4.0", "description": "An RFC 3986 compliant, scheme extendable URI parsing/validating/resolving library for JavaScript.", "homepage": "http://github.com/garycourt/uri-js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./src/uri.js", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "uri-js@1.4.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-4", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "b4dba71e0e4e4d2e6aed233be639938634462959", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-1.4.0.tgz", "integrity": "sha512-LXtnuJm7EYOxYIde9xoQa2TZWFdPjZHQ+uvMYPEvAaNk1Hq1nH0NqrU7Sk0sH1PBvcSZoZZFCZdIH2hLeB9bgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFoE3+KOVQ+Da9dGk2zu7n5Y8pBe971FyFp2v+GAze/gAiANUkHq1TdDiGCP0kCHaGmArwRWZx0M34S9v9+u5hMxQA=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.4.2": {"name": "uri-js", "version": "1.4.2", "description": "An RFC 3986 compliant, scheme extendable URI parsing/validating/resolving library for JavaScript.", "homepage": "http://github.com/garycourt/uri-js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./src/uri.js", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "uri-js@1.4.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-4", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "b5d67221edc6b4be269146ee34082876ed729bd0", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-1.4.2.tgz", "integrity": "sha512-qt3ta3U2TH9hPzzf9NacVVsz6US29SOyf8M6ovisjDM9poyWsJqbC7BnvJ1n6IGfSU7bSwH4AXZ6BbI7wOcDAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeczvr4DFf6D07ZAioch0GvR1rEzdAwkZAikLRqoem+AIhAKW9wgUfWWrOmvtlzLbnqyJaLRAmIubKhE8o2RFDMRkq"}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "uri-js", "version": "2.0.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "build/uri.js", "directories": {"test": "tests"}, "scripts": {"setup": "grunt setup", "build": "grunt", "test": "mocha -u mocha-qunit-ui build/uri.js tests/tests.js"}, "repository": {"type": "git", "url": "http://github.com/garycourt/uri-js"}, "keywords": ["URI", "IRI", "IDN", "URN", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "gitHead": "ba4ddb63f3319b33f200f51745d25bd98c6c0384", "_id": "uri-js@2.0.0", "_shasum": "b94dbf7bef8780722826a3049808e0e07dfd30fa", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b94dbf7bef8780722826a3049808e0e07dfd30fa", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.0.0.tgz", "integrity": "sha512-fjfo8fIlHQk77HsBnS3cF5z64ynqp4TRKFL++BxqKr6vKCJ1nahp+g0AkSqdmzPjaN+svRIl+Mrm5ebvJdaYDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC62BZ7prvfiGkmgMx0vXUcCKxHTlXi6DEx2jIoW+AFqAIhAJrM+8FMtxO5p3OU9yG024IFdeHgMu/3skOPzx0g8kaM"}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}]}, "2.1.0": {"name": "uri-js", "version": "2.1.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "build/uri.js", "directories": {"test": "tests"}, "scripts": {"setup": "grunt setup", "build": "grunt", "test": "mocha -u mocha-qunit-ui build/uri.js tests/tests.js"}, "repository": {"type": "git", "url": "http://github.com/garycourt/uri-js"}, "keywords": ["URI", "IRI", "IDN", "URN", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC6068"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-rename": "0.0.3", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "gitHead": "9b3168de7f6eae56670f520a34fde8ead0a5b4b1", "_id": "uri-js@2.1.0", "_shasum": "82a70e24394061b7e4fdb4713a1d2a0fb3e65b66", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "82a70e24394061b7e4fdb4713a1d2a0fb3e65b66", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.1.0.tgz", "integrity": "sha512-RWHLpP/nEqJEfA9cb58jLnQEbG/SX4CZ6Kl8oJe7c9DHY5gK2VA0qKkPJ00Nd75E/c8CYhZojohMhFqjuhh8vA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc6vB/kcBykjb4l35AJfeKpYfm0hrigSwXDAAAmRXFKAIhANapV2Nm0AGJ1AQtdTKHks4f9EDDt2q9SztmkpxhlPfO"}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}]}, "2.1.1": {"name": "uri-js", "version": "2.1.1", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "build/uri.js", "directories": {"test": "tests"}, "scripts": {"setup": "grunt setup", "build": "grunt", "test": "mocha -u mocha-qunit-ui build/uri.js tests/tests.js"}, "repository": {"type": "git", "url": "http://github.com/garycourt/uri-js"}, "keywords": ["URI", "IRI", "IDN", "URN", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC6068"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-rename": "0.0.3", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "gitHead": "7c1ac11617a1842576d32d4f8f937f8ffb65b2ac", "_id": "uri-js@2.1.1", "_shasum": "eb3f8505f468969bf92cb79ce8ceaac2ed667661", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "eb3f8505f468969bf92cb79ce8ceaac2ed667661", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.1.1.tgz", "integrity": "sha512-Mzott4fGJhw0z8q6P+QrQzijicqmfat55MdxD48gN9yXe8gBOGhcvc2hRVyNXzFa9xzCAep6CqWS6pbyajqE1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYgT8dPoWy+ROhyW3nxS5AZVKOzHoF2fDQDKw2rWWAoAIhAIJ614hDpMOKk5Xfsdm+l7mokgUYWGkJBM4ww2hhju5t"}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}]}, "3.0.0": {"name": "uri-js", "version": "3.0.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "module": "dist/esnext/index.js", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC6068"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "baf013376a4c467f49425684913c8f39bd60bb2a", "_id": "uri-js@3.0.0", "_shasum": "eb72ada63c666c863aa9b622f03fbdefa0bbd21e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "eb72ada63c666c863aa9b622f03fbdefa0bbd21e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.0.tgz", "integrity": "sha512-y/zj7MThqSBVhsDTz9nV3xGGsHm39Jk8oCTk4q+tKVfx+7iUfm3xHtyw91D1/oBjrmu4vUT4sdlJ14BdFxy27g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB/+ROAxyPvKh7PyoT9spuXygD2OXC6K8PJrm1WhrnUtAiB6ij6jZs7ZZUqlfSD/D4UGFn3oDpu//VcAPn71yMRpAA=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/uri-js-3.0.0.tgz_1490366753842_0.6686989809386432"}}, "3.0.1": {"name": "uri-js", "version": "3.0.1", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "module": "dist/esnext/index.js", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC6068"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "802bcbf3c7c37b5671ffe67a7aca3fd65fd48bec", "_id": "uri-js@3.0.1", "_shasum": "cde6cccb3da47df9e4a0118f17d2a1fef4a1f69c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cde6cccb3da47df9e4a0118f17d2a1fef4a1f69c", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.1.tgz", "integrity": "sha512-gstDj+dNkBLhfSgYms7JbQ17O/WW1rol4UK/tibpRd1v9uyvyuSQLptDU7niq3lyo+swE+hyYD2DbK6WjKIThw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8/zEyXdEcZThe2d79cRwTC+HkrcTQ2Fwozc22VfhvjQIgEifiD+aqmv1l1FqFHZseHx98yLvYguSr4wmWZELVylI="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/uri-js-3.0.1.tgz_1490641808290_0.7421848720405251"}}, "3.0.2": {"name": "uri-js", "version": "3.0.2", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "module": "dist/esnext/index.js", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC6068"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "1fce8f8168ae3eacf85a943a6765a5b19cd18462", "_id": "uri-js@3.0.2", "_shasum": "f90b858507f81dea4dcfbb3c4c3dbfa2b557faaa", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f90b858507f81dea4dcfbb3c4c3dbfa2b557faaa", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.2.tgz", "integrity": "sha512-SoboS4c924cg+wR2vxl8fospPPli3ZmVPIkRpJEWcrGIPeE8Tr3m9zNIyjYKn9YlF8EgiXQDCy3XVZxSFNjh8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsPhfnTZP2VgCEQ66IP+0p/bkEXZPy0TVy2x9Wkt6veAiEAqy+s+xu/oMR/0ueDTRI+p8t0So8OPo5/1+rth2nTlMw="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/uri-js-3.0.2.tgz_1490712042035_0.6057227314449847"}}, "4.2.0": {"name": "uri-js", "version": "4.2.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "module": "dist/esnext/index.js", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "29fdbec1f569971641cb3faacb32f1d14565fb0b", "_id": "uri-js@4.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WxtXcqX2yRvv66qyWxgYWcVl6hKjjrcqVnn+X2l5D98c3MfThsWmvg4j+FZGe4J1hdScE+HzcaFRmrMovAN4KA==", "shasum": "8c41301caaa13a71c5bafa74f9e7bf5a832f9f0c", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.0.tgz", "fileCount": 58, "unpackedSize": 532951, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpECWhNQy4y61HuMhbWlNX9O+qgnqmCSsThR1x1+9BdAIgcdBdCHC7cbwPV9CID4v/0uLBU/5TorkupO17i865fIU="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.2.0_1522551529606_0.5829522831492104"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "uri-js", "version": "4.2.1", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "module": "dist/esnext/index.js", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "e75c90b5ab334bd48ce68601b65a68edd0f5ef79", "_id": "uri-js@4.2.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jpKCA3HjsBfSDOEgxRDAxQCNyHfCPSbq57PqCkd3gAyBuPb3IWxw54EHncqESznIdqSetHfw3D7ylThu2Kcc9A==", "shasum": "4595a80a51f356164e22970df64c7abd6ade9850", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.1.tgz", "fileCount": 58, "unpackedSize": 535794, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB23tsgKaGTsvHKSsMGTo9GRYe/Fie4lZEsvfXGvFepPAiAF6DsL/8O1jKWrOAnzcYnbHnZZXSL5mvImXUWXPsNzyA=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.2.1_1523405887454_0.09829118735527631"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "uri-js", "version": "4.2.2", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp  && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "4f6f600fade03398c08adf2755c3a2ad66d31b3c", "_id": "uri-js@4.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "shasum": "94c540e1ff772956e2299507c010aea6c8838eb0", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz", "fileCount": 58, "unpackedSize": 533198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCIYyCRA9TVsSAnZWagAAa6oP/1XTWz8ftTU+nUe6aXcp\n4iluilj92MViWAwt5jsrv9f0DTowMdg0zKL7sG0rZJfXwgThlvKjqO+vEQRb\nLuf7bCtmHySHphXmWdFysAc0LHRb3eJiDLi8QVtDCzE8501X6F/3HGItBxw3\nzSsd31TFBl6m356HH1nRb/Eiz8W0MqWbJ1/T6ixU4PbUz9DxRx5BFxGTQlNM\nEdFiBR4JWTcCRov+nCmubeUhf+vDwosLGpcdEZePywgaDi3WzX8PLhBksZbf\n42ODIL0OlmHhcJeSmOgnZ4hJJVmu+59mOlLM8HcV46rp/LutEItDFOG90u4R\nDUds8cvcvY8HQ6/E3iNrQS9cL6bKOlUg0OYW6cxM9SzIWYp5awSqKa/ru1mJ\n0sAP17YYOA0dvY3RrkktCV+lthw8nmqYxL4vmL0vGR2hh5KEktSltGTI++nA\nc+0uEt559t2RCP6Of6CCsYeJ5JXzR5ncMadCuTyW1w2JCGKVoZq/bIQru7su\nvY1PxvjMh/fN/hXSa8kAd1uATLyIH9oVd3yJuRIj6zDEAcpimmqslwRX0PlB\nBZRaPS2xW++RfjnLRbA9z6wVqiXWwgR8WOPbgGTJgH9kj5T7MBLxtZd8Zj80\n1uD+BqG8iUFM4b+mtri7VlU3cWw0iVnfDwgGdleRhQ7ImEH4sJO3x5pBJGbv\n3jS9\r\n=55z0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzVuHlxmX9acm216qkyptErKakM7Zqd6nYS+cRgN9CfgIgNqNKn/Fzmi6T5CPy7FM+Tr+4+2gulwIIXDSWM8kh6aM="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.2.2_1527285297263_0.6787805812046788"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "uri-js", "version": "4.3.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "424180cb7689c16d2f977988b4a181bf54bb187c", "_id": "uri-js@4.3.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-Q9Q9RlMM08eWfdPPmDDrXd8Ny3R1sY/DaRDR2zTPPneJ6GYiLx3++fPiZobv49ovkYAnHl/P72Ie3HWXIRVVYA==", "shasum": "e16cb9ef7b4036d74be59dc7342258e6f1aca20e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.3.0.tgz", "fileCount": 40, "unpackedSize": 421345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSv7ZCRA9TVsSAnZWagAARhMP/1k3ermDhttCNsbXd4lK\nxb986+aFwxnjD7siHnQJYGLDyj6iQLuo9oZ0DTTsIt6nHC8evgDOsBsaiamV\nFv41MtC2e97KLTIAbI9kAcsvvf4npNrAwr54ZJ6H/kfyLvRibhfTTmVNDJ4r\nDuosU++iOoO2IGzTWeM4/llbUP3HSMxI+fJsMKawnKhNPFS08OR7eEzr9gGI\nuSfFetHjCziWo2VubTWi1zI2H43sS61zLseaIGHhX403OKndC/LeWXVMzu6l\nCa798t/osiqufMiJcOf0AjxJLul2LGS8j5KQtVvzg7UF5O8e5F6NEv6KuvQY\nz4jj4TD/w+L65bXwM+27b0mrILNIukCXW4g5bQsfSNuOb819e5dXCzfo/YS/\nbX7GsYcegaDnny3rDPAUQU44RV7Cxe3l2n7IMLFwEULuQeUcCVgIOMPSpjQW\njkJESCdD36ORF2k48dKMJwyjcwArW2POBsTJDO8sW0H2/GvZoDt6UhBIEGlK\nUpUMGBXhfgLnqAFyAXk+/lswTizgJGSZTt+q9xLa3M/kMjOgaZCjXeEbhfYn\n8yjzwU5DDLDkRRLIPI1k0RgDLSOi+EV6JOnAXUdCa5TZr4wA/EBfRqw3k+7e\n1ar4ATyJ7COOOvG1fJQVcAidG7FYiZGcVbHJcgUd4B26Q99qiZNoSHfNpt+L\n976P\r\n=oosy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC5q2Be7KKh7EbeBNH5iJYy1P0ST8jt7AqVYzCRhqlImAiB4iipCphf85P20x5O5WHCrhuiLbzN4b1r2ztDZr1eDkw=="}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.3.0_1598750425174_0.25728795153933026"}, "_hasShrinkwrap": false}, "4.4.0": {"name": "uri-js", "version": "4.4.0", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "clean": "rm -rf dist", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "WS", "WSS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6455", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "7cb6f9ccfa441f88de122e782fd34a27e3076057", "_id": "uri-js@4.4.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-B0yRTzYdUCCn9n+F4+Gh4yIDtMQcaJsmYBDsTSG8g/OejKBodLQ2IHfN3bM7jUsRXndopT7OIXWdYqc1fjmV6g==", "shasum": "aa714261de793e8a82347a7bcc9ce74e86f28602", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.0.tgz", "fileCount": 46, "unpackedSize": 435804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfS/hFCRA9TVsSAnZWagAANNsP/3GypTnT3GjWWn+gLvQU\nGekN0Eo+0cz0VPVVyCI72fFUgSN7txFow7/4hRNjkmu8A1hIA9hw2FehonOy\nkf5eKMyGWxAKc6/UcfWtAgdUYRt66rVq2qqJHoHZyocCOGXw32aQTwBtz0H1\nBKzGZpPeXWjz1Y42PFqhWXBNxOG9Zyq0RH24pG3aOoNxky2tLrygAwG1keT1\nXxN4dzPjJEhN9uN9Wig/VCy7fM9L8MFrKducFNpjYem8IGIDfs0uhllGiiYI\nM6H1VTztAJRDAKPzg41Zt/iCMiGMKIQfARP5rvhMFQen+HjFETIXdomQzB5f\ndAhHIoMJbpX9kM0V0fuKVx0lVxtiIuqzqxVaHZJ1qFNTPSQSEPZPX/KyBr2E\nwiQKG7Bn2JMZby3c0Kb2t1ocNuXt1FEC4y+pkl1KRhLQ4S0a9gdpSfS+1lST\nUFqypQ27JRepjb+N4TqUFy1kjorTm7sFkzk4FF7RKL6raFeulcYLGB832XDk\n7Wt2sQR3MyvzvqYzyk2P52MEn4OGVLR5WowCjfqU6bpvOe8uwMsfHuWcA4iJ\n0yBJMZr7lr0bcvMNN7jSiUt1L7wSBKqLjvZ9h17FUqewshlWTKPzZIQE4+KL\nlQff98Jd8A28bmrCW8IpHVfBkxpsVxuyqLxVjHMbNID8LlnByNndfEMZKQSX\nYY4+\r\n=qU9S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUPBtWJ2wUxInOUONbxRA6+rtZq36GuYFxfTCUPvrd+AIhAPeHkvA5RZLJ17Joa24sxcHEUXM3vmn0gn9Wv55IhBgd"}]}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.4.0_1598814277489_0.009253203585056324"}, "_hasShrinkwrap": false}, "4.4.1": {"name": "uri-js", "version": "4.4.1", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "directories": {"test": "tests"}, "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "clean": "rm -rf dist", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "WS", "WSS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6455", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^8.2.1", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "gitHead": "9a328873a21262651c3790505b24c9e318a0e12d", "_id": "uri-js@4.4.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "shasum": "9b1a52595225859e55f669d928f88c6c57f2a77e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "fileCount": 46, "unpackedSize": 469879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+k2hCRA9TVsSAnZWagAA34gQAIBCHrX9EiMnQ1y6Q/lA\nnH3L0pTQb47BwM+2z4HPTX/fzIMryYGjGTlr5M1e1ljQSiaIVUygAxqqD5Tg\nTJLguj9k4nhKsmsHFKmCzTkFK1R7foE/vhS+EZGWYc/bWkE6oAQ8+AX3Q53j\nP2HnbuK/kqWtfoskY43JiuRKS9AU39e03SnmZz3uUDCV/t+lF+cdBlzNAjG3\nesW3dV2ynyOBkouW7WolPYSULti6YXquvpgz1C2QGAkyxXKOJMf7jlYHN7wh\nId1MAR92XuzBgu2sYNzIxr6Qhd/Moi/1yb++otrm90OEC1wBgoXPbNsBzJSx\ndIwWbJ0DIjDnqAhVRzZ90MfC6qxRuCImpDd8feZ+nb343oUb8ofEME/hUG4Q\nPvAwKM8n+2BjNiwX2TyoGvAfuch6GwBrOUcMZM5SMW45ocEXZgHNPD2uIek9\nBMiaC1Ew9h97NDwi1SsB7HMBhUEQhT9cbz3ojeLE3GKfOmUBs4JdwbYk1n3+\nUfMhwoYvhqRugjUbFXpoBRbFBXWYPlEIR5GLcIozABblfuPLgmndZ0kefT6A\nW+WMbezye9qJPb2ARumFOfand+nHea4Grpa87cUzM/Pe+uzs7v9JflFdXDjP\nlymzW91jAE09aIh9rKaQHZCEZA/PeV66BzGPaoDb1sPS7AlMXEpLlzy5K/3B\nGWvW\r\n=SXo/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMs1qpyJpeN93ls+gFlrcOf7srUKNr2MjVpWe51VBgKAIhAJctn36ZpbANRujkBDQxFeKL1FoBpLuff6Dz7QOvxBh3"}]}, "_npmUser": {"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uri-js_4.4.1_1610239392499_0.32518203916709365"}, "_hasShrinkwrap": false}}, "readme": "# URI.js\n\nURI.js is an [RFC 3986](http://www.ietf.org/rfc/rfc3986.txt) compliant, scheme extendable URI parsing/validating/resolving library for all JavaScript environments (browsers, Node.js, etc).\nIt is also compliant with the IRI ([RFC 3987](http://www.ietf.org/rfc/rfc3987.txt)), IDNA ([RFC 5890](http://www.ietf.org/rfc/rfc5890.txt)), IPv6 Address ([RFC 5952](http://www.ietf.org/rfc/rfc5952.txt)), IPv6 Zone Identifier ([RFC 6874](http://www.ietf.org/rfc/rfc6874.txt)) specifications.\n\nURI.js has an extensive test suite, and works in all (Node.js, web) environments. It weighs in at 6.4kb (gzipped, 17kb deflated).\n\n## API\n\n### Parsing\n\n\tURI.parse(\"uri://user:<EMAIL>:123/one/two.three?q1=a1&q2=a2#body\");\n\t//returns:\n\t//{\n\t//  scheme : \"uri\",\n\t//  userinfo : \"user:pass\",\n\t//  host : \"example.com\",\n\t//  port : 123,\n\t//  path : \"/one/two.three\",\n\t//  query : \"q1=a1&q2=a2\",\n\t//  fragment : \"body\"\n\t//}\n\n### Serializing\n\n\tURI.serialize({scheme : \"http\", host : \"example.com\", fragment : \"footer\"}) === \"http://example.com/#footer\"\n\n### Resolving\n\n\tURI.resolve(\"uri://a/b/c/d?q\", \"../../g\") === \"uri://a/g\"\n\n### Normalizing\n\n\tURI.normalize(\"HTTP://ABC.com:80/%7Esmith/home.html\") === \"http://abc.com/~smith/home.html\"\n\n### Comparison\n\n\tURI.equal(\"example://a/b/c/%7Bfoo%7D\", \"eXAMPLE://a/./b/../b/%63/%7bfoo%7d\") === true\n\n### IP Support\n\n\t//IPv4 normalization\n\tURI.normalize(\"//***************\") === \"//**********\"\n\n\t//IPv6 normalization\n\tURI.normalize(\"//[2001:0:0DB8::0:0001]\") === \"//[2001:0:db8::1]\"\n\n\t//IPv6 zone identifier support\n\tURI.parse(\"//[2001:db8::7%25en1]\");\n\t//returns:\n\t//{\n\t//  host : \"2001:db8::7%en1\"\n\t//}\n\n### IRI Support\n\n\t//convert IRI to URI\n\tURI.serialize(URI.parse(\"http://examplé.org/rosé\")) === \"http://xn--exampl-gva.org/ros%C3%A9\"\n\t//convert URI to IRI\n\tURI.serialize(URI.parse(\"http://xn--exampl-gva.org/ros%C3%A9\"), {iri:true}) === \"http://examplé.org/rosé\"\n\n### Options\n\nAll of the above functions can accept an additional options argument that is an object that can contain one or more of the following properties:\n\n*\t`scheme` (string)\n\n\tIndicates the scheme that the URI should be treated as, overriding the URI's normal scheme parsing behavior.\n\n*\t`reference` (string)\n\n\tIf set to `\"suffix\"`, it indicates that the URI is in the suffix format, and the validator will use the option's `scheme` property to determine the URI's scheme.\n\n*\t`tolerant` (boolean, false)\n\n\tIf set to `true`, the parser will relax URI resolving rules.\n\n*\t`absolutePath` (boolean, false)\n\n\tIf set to `true`, the serializer will not resolve a relative `path` component.\n\n*\t`iri` (boolean, false)\n\n\tIf set to `true`, the serializer will unescape non-ASCII characters as per [RFC 3987](http://www.ietf.org/rfc/rfc3987.txt).\n\n*\t`unicodeSupport` (boolean, false)\n\n\tIf set to `true`, the parser will unescape non-ASCII characters in the parsed output as per [RFC 3987](http://www.ietf.org/rfc/rfc3987.txt).\n\n*\t`domainHost` (boolean, false)\n\n\tIf set to `true`, the library will treat the `host` component as a domain name, and convert IDNs (International Domain Names) as per [RFC 5891](http://www.ietf.org/rfc/rfc5891.txt).\n\n## Scheme Extendable\n\nURI.js supports inserting custom [scheme](http://en.wikipedia.org/wiki/URI_scheme) dependent processing rules. Currently, URI.js has built in support for the following schemes:\n\n*\thttp \\[[RFC 2616](http://www.ietf.org/rfc/rfc2616.txt)\\]\n*\thttps \\[[RFC 2818](http://www.ietf.org/rfc/rfc2818.txt)\\]\n*\tws \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\twss \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\tmailto \\[[RFC 6068](http://www.ietf.org/rfc/rfc6068.txt)\\]\n*\turn \\[[RFC 2141](http://www.ietf.org/rfc/rfc2141.txt)\\]\n*\turn:uuid \\[[RFC 4122](http://www.ietf.org/rfc/rfc4122.txt)\\]\n\n### HTTP/HTTPS Support\n\n\tURI.equal(\"HTTP://ABC.COM:80\", \"http://abc.com/\") === true\n\tURI.equal(\"https://abc.com\", \"HTTPS://ABC.COM:443/\") === true\n\n### WS/WSS Support\n\n\tURI.parse(\"wss://example.com/foo?bar=baz\");\n\t//returns:\n\t//{\n\t//\tscheme : \"wss\",\n\t//\thost: \"example.com\",\n\t//\tresourceName: \"/foo?bar=baz\",\n\t//\tsecure: true,\n\t//}\n\n\tURI.equal(\"WS://ABC.COM:80/chat#one\", \"ws://abc.com/chat\") === true\n\n### Mailto Support\n\n\tURI.parse(\"mailto:<EMAIL>,<EMAIL>?subject=SUBSCRIBE&body=Sign%20me%20up!\");\n\t//returns:\n\t//{\n\t//\tscheme : \"mailto\",\n\t//\tto : [\"<EMAIL>\", \"<EMAIL>\"],\n\t//\tsubject : \"SUBSCRIBE\",\n\t//\tbody : \"Sign me up!\"\n\t//}\n\n\tURI.serialize({\n\t\tscheme : \"mailto\",\n\t\tto : [\"<EMAIL>\"],\n\t\tsubject : \"REMOVE\",\n\t\tbody : \"Please remove me\",\n\t\theaders : {\n\t\t\tcc : \"<EMAIL>\"\n\t\t}\n\t}) === \"mailto:<EMAIL>?cc=<EMAIL>&subject=REMOVE&body=Please%20remove%20me\"\n\n### URN Support\n\n\tURI.parse(\"urn:example:foo\");\n\t//returns:\n\t//{\n\t//\tscheme : \"urn\",\n\t//\tnid : \"example\",\n\t//\tnss : \"foo\",\n\t//}\n\n#### URN UUID Support\n\n\tURI.parse(\"urn:uuid:f81d4fae-7dec-11d0-a765-00a0c91e6bf6\");\n\t//returns:\n\t//{\n\t//\tscheme : \"urn\",\n\t//\tnid : \"uuid\",\n\t//\tuuid : \"f81d4fae-7dec-11d0-a765-00a0c91e6bf6\",\n\t//}\n\n## Usage\n\nTo load in a browser, use the following tag:\n\n\t<script type=\"text/javascript\" src=\"uri-js/dist/es5/uri.all.min.js\"></script>\n\nTo load in a CommonJS/Module environment, first install with npm/yarn by running on the command line:\n\n\tnpm install uri-js\n\t# OR\n\tyarn add uri-js\n\nThen, in your code, load it using:\n\n\tconst URI = require(\"uri-js\");\n\nIf you are writing your code in ES6+ (ESNEXT) or TypeScript, you would load it using:\n\n\timport * as URI from \"uri-js\";\n\nOr you can load just what you need using named exports:\n\n\timport { parse, serialize, resolve, resolveComponents, normalize, equal, removeDotSegments, pctEncChar, pctDecChars, escapeComponent, unescapeComponent } from \"uri-js\";\n\n## Breaking changes\n\n### Breaking changes from 3.x\n\nURN parsing has been completely changed to better align with the specification. Scheme is now always `urn`, but has two new properties: `nid` which contains the Namspace Identifier, and `nss` which contains the Namespace Specific String. The `nss` property will be removed by higher order scheme handlers, such as the UUID URN scheme handler.\n\nThe UUID of a URN can now be found in the `uuid` property.\n\n### Breaking changes from 2.x\n\nURI validation has been removed as it was slow, exposed a vulnerabilty, and was generally not useful.\n\n### Breaking changes from 1.x\n\nThe `errors` array on parsed components is now an `error` string.\n", "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:34:03.088Z", "created": "2012-03-13T19:38:32.924Z", "1.4.0": "2012-03-13T19:38:35.437Z", "1.4.2": "2012-05-24T20:01:53.618Z", "2.0.0": "2015-06-04T16:39:02.639Z", "2.1.0": "2015-06-08T20:22:25.139Z", "2.1.1": "2015-07-08T21:15:27.312Z", "3.0.0": "2017-03-24T14:45:54.970Z", "3.0.1": "2017-03-27T19:10:10.525Z", "3.0.2": "2017-03-28T14:40:42.750Z", "4.2.0": "2018-04-01T02:58:49.758Z", "4.2.1": "2018-04-11T00:18:07.562Z", "4.2.2": "2018-05-25T21:54:57.365Z", "4.3.0": "2020-08-30T01:20:25.284Z", "4.4.0": "2020-08-30T19:04:37.625Z", "4.4.1": "2021-01-10T00:43:12.666Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "users": {"dknell": true, "mshwery": true, "firerishi": true, "darluc": true, "hualei": true, "flumpus-dev": true}, "homepage": "https://github.com/garycourt/uri-js", "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "WS", "WSS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6455", "RFC6874"], "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md"}