{"_id": "fb-watchman", "_rev": "41-2d82d67891b6e7bffc67dbc6acde5d7c", "name": "fb-watchman", "description": "Bindings for the Watchman file watching service", "dist-tags": {"latest": "2.0.2"}, "versions": {"0.0.0": {"name": "fb-watchman", "version": "0.0.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:facebook/watchman.git"}, "dependencies": {"json-stream": "0.2.2", "nextback": "~0.1.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "_id": "fb-watchman@0.0.0", "dist": {"shasum": "0a6775818dc611e306083382591c89c16f712d24", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-0.0.0.tgz", "integrity": "sha512-z50p3m7V6ljDQW4upX9Wmk1rUzz6cWbzEic6QAayqUTI1fnH3nPaS9xE6wDWCyuqweY1vKDnMH885uG/nTsmbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCw6r4NKbd9EGUTiAD5JBOMshJ5jmZ0LMPYYYUflwi6sAIhANWn6ABqfc1BIT1J4k2PeEN0CET4B+wMD4iPm9ocEkJL"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "fb-watchman", "version": "0.0.1", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:facebook/watchman.git"}, "dependencies": {"json-stream": "0.2.2", "nextback": "~0.1.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "_id": "fb-watchman@0.0.1", "dist": {"shasum": "a0cfc08a136f11bb94c03b3a4f32a4f602a569f3", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-0.0.1.tgz", "integrity": "sha512-iYXgGMr90JwEDf79m8+uiPOgyIyi7Jknn2mMCBoDhan3NtgxChsktEenidnwJQmRBTgFLYBACq+A9hgr3+F8+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBeXB/MNQjmDO/2fxFwxrranPkYv3uzZoj+e7P5he8eFAiEAhmPP6CLUrwNr77uwa+Y27bcpYUmnkeZ+eDVxZe1988Y="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "fb-watchman", "version": "1.0.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.0.0", "_shasum": "91771cf6dc64725bcbf0702dd405472f36288814", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "91771cf6dc64725bcbf0702dd405472f36288814", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.0.0.tgz", "integrity": "sha512-ruFgs5IaHsNh6RexiDHhoLicswYKNa/biJiy8kYwHrkG5t6XlJ82YhQiBsxsULLQ07uu6zWIafTCiiWzX1XPOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAHKHSJIWlQxyOtbbDzKC482VnVbO46eH4dyjvWunwfZAiAQbUq+2ZPNASxriT1VWHsnFyenH3RK0hoBDVF1eJUvMg=="}]}, "directories": {}}, "1.1.0": {"name": "fb-watchman", "version": "1.1.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {"node-int64": "0.4.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.1.0", "_shasum": "d589252da78b349035b5cc25acf4f650298ba582", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "d589252da78b349035b5cc25acf4f650298ba582", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.1.0.tgz", "integrity": "sha512-6BHR5jRPgAkPQ5F+12tiOElZf5DloM+2q666z0IEOA9FQ4j8VHqTKqsYo7ywiv01yXm8vJHCwxtNmWaYQJ3BDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICpSBPuFS6S0701NBmoTZcMbwVb3rlzMEqK8czLMGq7tAiAcGe8rxWPyzFVswGEPqrO3wcja3Lm30a6A9q/CuXNlWQ=="}]}, "directories": {}}, "1.2.0": {"name": "fb-watchman", "version": "1.2.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {"node-int64": "0.4.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.2.0", "_shasum": "ddd4cc52d6feec49dc9a2607fd7b63af6549b5a6", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "ddd4cc52d6feec49dc9a2607fd7b63af6549b5a6", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.2.0.tgz", "integrity": "sha512-c3N27dcmoo1l+n3laATdqhXnPYXMnUI4ZjZYjtI7X2SGk5rJoosMCNgakdGd2o0UP0mwWK4S64Wx2Yf4u9WlvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGi5gSF7qVVKDh7ADz001UP7xbyQddrdHkaBkl8thkMTAiAnQva9Eb4lpzZnyZWeZWUTdK+BVLqB0KDZTLx6vTc2ng=="}]}, "directories": {}}, "1.3.0": {"name": "fb-watchman", "version": "1.3.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {"node-int64": "0.4.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.3.0", "_shasum": "559721c2be754cf5dd9c4dfbb6697e294299207a", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "559721c2be754cf5dd9c4dfbb6697e294299207a", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.3.0.tgz", "integrity": "sha512-V9vEh01HSiwwvuBTzn53swvkabI72hxrdbW5Rbo5YYTSEIRJiAao94sT83p2gV4IcMnbsInydOkrL4Scfm4NZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCONWZ5LQ8dToR7wd6F2j4A1Vz+RD4MfhE0UIoQRcSkmgIgLPdPOloEPfwT0fCirkxGPo9oqzCr+gy2fivurXN82V4="}]}, "directories": {}}, "1.4.0": {"name": "fb-watchman", "version": "1.4.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {"node-int64": "0.4.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.4.0", "_shasum": "2bef0513654d61e3da8484624e1af675e0cab7c9", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "2bef0513654d61e3da8484624e1af675e0cab7c9", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.4.0.tgz", "integrity": "sha512-ctgXyauLWmAzctzVfF0wYHAzISlQ62wTcI0fa77R/oSGOI5RXGNClmdmF5t1bvr7BirKgcWM8rOaOi/XlAwEpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMl/A1m//UJ5piKxTzM6S3jLvPRjxdB8Q/+Khu/Gw3fAIhAI62cWKGkQgyYNr+P0qopdfT452MqoHTtzsr+pceGtQV"}]}, "directories": {}}, "1.5.0": {"name": "fb-watchman", "version": "1.5.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "dependencies": {"node-int64": "0.4.0"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js", "bser.js"], "_id": "fb-watchman@1.5.0", "_shasum": "8e9bca4fadeb5144e6e144e7e249449e1ce3aa40", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "8e9bca4fadeb5144e6e144e7e249449e1ce3aa40", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.5.0.tgz", "integrity": "sha512-5x9x2mn1/cGVU6qkjUHO5967m5QT47kBTNpPgZ82iaf2Z1igx3sSWZh+esRTqJFQ8c+aFKar1KQO0lMduhxXkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCckxMjSQhDRm1oP3PVybsq2NSOmdpxT+WsRWm22do72QIhAMtsP6an6vwTVGK35Y+wBu54Ym1nXaNAoXFIz7i2+p7X"}]}, "directories": {}}, "1.6.0": {"name": "fb-watchman", "version": "1.6.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^1.0.2"}, "_id": "fb-watchman@1.6.0", "scripts": {}, "_shasum": "c5e1bb0d36e690d9f93f5a139698a68dc60d99b1", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "c5e1bb0d36e690d9f93f5a139698a68dc60d99b1", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.6.0.tgz", "integrity": "sha512-tpiESfKEX2nPSaOUFT4Q9utNRfQvyrt2wGMp8mXG67O3Pv66sybdop0qpfGpfUuuW4AN5xjKQZE11mv2A04PYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7R1awupFVqToNXN9COgCFl37AviMvwxJt6mnEi5IKSAiEAin6lKYr52NWNGH/lBzdnNSDw7BBYtQB7gPHkj1yGbT8="}]}, "directories": {}}, "1.7.0": {"name": "fb-watchman", "version": "1.7.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^1.0.2"}, "_id": "fb-watchman@1.7.0", "scripts": {}, "_shasum": "5754d4bbb035c1f762767bc62871443084f7db48", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "5754d4bbb035c1f762767bc62871443084f7db48", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.7.0.tgz", "integrity": "sha512-5apFgmeyOlf7NLq4z44QqgmPzKsCsjYVvKR5oinBui/1FNXyd4EOfWoeKuF7WJlLhK+QvnbZiLB2DOL+k/ompA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI4mbS9ids9LQAJAZto3XfJo4KcZBGjPDVQR9QhVDarAIgefHzjx3FOuUpvPvpl/I/6pIjJh2uj8rKp+Y6PS2tanc="}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "directories": {}}, "1.8.0": {"name": "fb-watchman", "version": "1.8.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^1.0.2"}, "_id": "fb-watchman@1.8.0", "scripts": {}, "_shasum": "5a2ec546f9ee861e9d4a44b4b3aa04a51e092c96", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "5a2ec546f9ee861e9d4a44b4b3aa04a51e092c96", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.8.0.tgz", "integrity": "sha512-c<PERSON><PERSON>rirZeM7DEzogXPY8gVVUq1O/TerGdnZQ6Hx50UxytmCDj5LzL74q0IdAYH3KVu33jZhiTzwD+ec91poCaMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1u6ZWzbEkA/XXCcz03c3UfvhHJhsm+rknvQItxjloHAIhAMi/giLFusYDezibdGmw0UVUVRYeJ+JYz5/P0XOLvbz+"}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "directories": {}}, "1.9.0": {"name": "fb-watchman", "version": "1.9.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^1.0.2"}, "_id": "fb-watchman@1.9.0", "scripts": {}, "_shasum": "6f268f1f347a6b3c875d1e89da7e1ed79adfc0ec", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "6f268f1f347a6b3c875d1e89da7e1ed79adfc0ec", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.9.0.tgz", "integrity": "sha512-XNCea63U2uFeQ+YjGqcU3c4ZltlzbnfAFtLGAJc7imQjeHLJ3V++iyUooTehQXfLpTEjQRmzEXlmwbx93M+fWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbw/qLfltQNjeUt6DuI+YiafQpjXXtBYCe1NLWMCycrgIhAK8NndOvQM7cUqEroi74eoWJ2ykzrHfIMTXXsxIw45zR"}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "directories": {}}, "1.9.1": {"name": "fb-watchman", "version": "1.9.1", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^1.0.3"}, "_id": "fb-watchman@1.9.1", "scripts": {}, "_shasum": "8ca5a3fb910e3df55ab04a0279284f2353b6a3cf", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "8ca5a3fb910e3df55ab04a0279284f2353b6a3cf", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.9.1.tgz", "integrity": "sha512-NlWQVGsydqYNIiDnr8JCC7S+P50slWySim0WQhBtR1oXzf1BAfT72aSU42FybVRAglVzi8KHT54W+uKNlS7hzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAlg4Wy0fuwWp4u6+vPVFMdJ31jzhL+J+wYzuEFC/0MwIgC6tVjPrcsvmq9ypn7SLt/+U6bbGv43NX0vuYEp5OpHk="}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/fb-watchman-1.9.1.tgz_1485476269959_0.25976619380526245"}, "directories": {}}, "1.9.2": {"name": "fb-watchman", "version": "1.9.2", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "1.0.2"}, "_id": "fb-watchman@1.9.2", "scripts": {}, "_shasum": "a24cf47827f82d38fb59a69ad70b76e3b6ae7383", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "a24cf47827f82d38fb59a69ad70b76e3b6ae7383", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-1.9.2.tgz", "integrity": "sha512-XgitQpaII7LkblC9X8HhfnfuDpyOYSB/Xw8h3Q/gXfMtyL7UICDS1axIlafhwfvKxPjrqnu7EfO7i3A1kH+Rfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDQzlZ9XSyNu9FPMXtOQ+jEwmEC39FgKlil7YFfzjLQ2AiAMtZnsNM3rgiKHecmzgBR4q/9e++2yxdhLyORNGQBqPg=="}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/fb-watchman-1.9.2.tgz_1485558576698_0.48525198199786246"}, "directories": {}}, "2.0.0": {"name": "fb-watchman", "version": "2.0.0", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "files": ["index.js"], "dependencies": {"bser": "^2.0.0"}, "_id": "fb-watchman@2.0.0", "scripts": {}, "_shasum": "54e9abf7dfa2f26cd9b1636c588c1afc05de5d58", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "54e9abf7dfa2f26cd9b1636c588c1afc05de5d58", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.0.tgz", "integrity": "sha512-+6dk4acfiWsbMc8pH0boQDeQprOM4mO/kS4IAvZVJZk4B6CZYLg4DkTGbL82vhglUXDtkJPnLfO0WXv3uxGNfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSGcZHG08T22zO88bdJoNH8TQC/bSUzmTbymcYcrG8fQIhAMBB91FEwyxyXcTCau1ymYX3J+2uu0Xz+UFcfTDl+T10"}]}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/fb-watchman-2.0.0.tgz_1485803244981_0.6421511068474501"}, "directories": {}}, "2.0.1": {"name": "fb-watchman", "version": "2.0.1", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "**************:facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "dependencies": {"bser": "2.1.1"}, "_id": "fb-watchman@2.0.1", "dist": {"shasum": "fc84fb39d2709cf3ff6d743706157bb5708a8a85", "integrity": "sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg==", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz", "fileCount": 4, "unpackedSize": 10832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6qE2CRA9TVsSAnZWagAAGIkQAJLnEqOMen0otGyJJVVq\nsMJp4pRJxN3E1cSrv8BOpGjGar09OVRZLayo1EcTX6PAGwTGUCHVsid0m8rV\nCCEjHtUcKIUrIrG6YbWJiHk7JQU5JU2/+Ijl+oXUhF3L+ykRDB2L8JpVB7iY\nMWOw5aeLrGV0LrgRLdYAU+hQhJ4INl/UeBo1NgPnj9EVd4LE3oFMgmYYZj1p\nscQYJymppHF+qztpAHW46snKi2N7vZcPijlGHdxgDsMXLiwF0FBLNMuQ6ZrZ\nXo+HP6TCAaQeRRQrbk8/qghhXnLy1DPcYexSel745f0LX3GtPdx2tji2YnWs\nWnZ3KjBJAmx30PLJXkNVI2ipsQzcuSl0Ipv5sbcB+ErnRObHuqWfnPcyH4ar\nAvUkL8MYYngfl22tCUDP1lxMjmG2bqmcF5HJX6v7HNJhFldEO4eSG9CQPnEr\nS24+kq70VMxKkAYWBrI4ZMxhm0v5QpUBZ3uh+5v+3KNiTiRAsgdoDYW0UmOt\ngqFTUecfhcr5EBcGQEfx+Wuk78sO8bG2vq2Qr0EGBmyT/EY6DBplxtV9KwuP\njmvolsJnATO/uZkka2bh+RqqkTsRuTerSAHSiooaqz7I5BMGNyHZz0tTNqme\nhuJUv7yopJRqz1xJc5QL2zohAqdJWS+AJtARzz83N2Ev4A+/nefFq2gU6aL7\nZVcZ\r\n=14g3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfAsjrDFNX7mJrT+8gkBGPcKZvnLr0qisXgvvkJa+pPAIhAO7KMtwhTrfbS40L2goWSybF1gOduRir86On/vkzQQZG"}]}, "maintainers": [{"email": "<EMAIL>", "name": "kassens"}, {"email": "<EMAIL>", "name": "wez"}], "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fb-watchman_2.0.1_1575657781580_0.38692033074933474"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "fb-watchman", "version": "2.0.2", "description": "Bindings for the Watchman file watching service", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/", "dependencies": {"bser": "2.1.1"}, "_id": "fb-watchman@2.0.2", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==", "shasum": "e9524ee6b5c77e9e5001af0f85f3adbb8623255c", "tarball": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "fileCount": 3, "unpackedSize": 11008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHS2Y+xOeRLz8dRWTpe3v+3djcs6n2ytz+3KK+RVxBQaAiBYbDUkkrtltmhE/H90axF2+UaxO3v89JSMxVwJeu83OQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjK2uuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfXA/7BIAG0HHsBK/XolMeGjZSDXGhEVqzgJUZtCWBaXlwAtPUVJoB\r\nPLURsjMPYjx2sQKmdEsJUkDAPWlFCLlzYbYwAiwdj5wUvY/VJ+0ubxjY5HRG\r\ni3JukGhNVSXEVuVTMuYDO5cKsALVCTJLJwjpLjYrReFA8vdM53Wkt0dgFX5h\r\n8NnGU2OzqXF9Rn3dntsjV9ANKbx+P2/dqmgozL8arGn5TzOEmNnzddyQIxfj\r\nK12fPgyGKkije3IKqYdojqe+9Hsmn/yDk+ilqfBlq56fUWjvGbTuPfFiuV+f\r\nijqTQ/eqaTranyUyAg1noO3IiWw4yrS8WKluwekQot7mexB9aa/m5zz/nWvF\r\nNlvjPiMRTcP7YCzFw3FWunShszFeUYluTaEuMg1uEljXeBkeV8yi3z4Soh3k\r\n/DjJ06lHq4E5p2Dt8cgZ9EDeROdhpfnHxLDb8maZJae55q/poftAadQIyzbw\r\nWR6QCwK1zFgx4BXjH/YWDAIwyRiJji7UPXJJtye0N55V3WD+rDEufohnNrN0\r\nsgS0aCoqXKCpYELOjR7cNUKV0fCJomfmQEjNcU0bXAfUKkprx3S5/DJHcaRs\r\n+9BzMX2jkdpYvvCtzTR6XEIB8AFpR7f9u838HvHMnWvManzxuUapL2DoLxBz\r\n79OJYPNiir7BYOot0UNl6Jb+16xRILQeasM=\r\n=+UCP\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "bolinfest", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "wez", "email": "<EMAIL>"}, {"name": "bolinfest", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "kassens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fb-watchman_2.0.2_1663789998469_0.4016230653080639"}, "_hasShrinkwrap": false}}, "readme": "# fb-watchman\n\n`fb-watchman` is a filesystem watcher that uses the\n[Watchman](https://facebook.github.io/watchman/) file watching service from\nFacebook.\n\nWatchman provides file change notification services using very\nefficient recursive watches and also allows more advanced change matching and\nfilesystem tree querying operations using\n[a powerful expression syntax](https://facebook.github.io/watchman/docs/file-query.html#expressions).\n\n## Install\n\nYou should [install Watchman](\nhttps://facebook.github.io/watchman/docs/install.html) to make the most of this\nmodule.\n\nThen simply:\n\n```\n$ npm install fb-watchman\n```\n\n## Key Concepts\n\n- Watchman recursively watches directories.\n- Each watched directory is called a `root`.\n- You must initiate a `watch` on a `root` using the `watch-project` command prior to subscribing to changes\n- Rather than separately watching many sibling directories, `watch-project` consolidates and re-uses existing watches relative to a project root (the location of your `.watchmanconfig` or source control repository root)\n- change notifications are relative to the project root\n\n## How do I use it?\n\n[Read the NodeJS watchman documentation](https://facebook.github.io/watchman/docs/nodejs.html)\n", "maintainers": [{"email": "<EMAIL>", "name": "fb"}, {"email": "<EMAIL>", "name": "kassens"}, {"email": "<EMAIL>", "name": "fb-watchman"}], "time": {"modified": "2024-02-13T20:44:39.814Z", "created": "2014-11-03T21:02:29.025Z", "0.0.0": "2014-11-03T21:02:29.025Z", "0.0.1": "2015-03-06T01:05:08.276Z", "1.0.0": "2015-06-23T14:58:14.586Z", "1.1.0": "2015-06-25T16:14:34.177Z", "1.2.0": "2015-07-11T21:57:26.275Z", "1.3.0": "2015-08-16T03:56:45.278Z", "1.4.0": "2015-08-16T17:01:37.400Z", "1.5.0": "2015-08-16T19:31:47.416Z", "1.6.0": "2015-08-25T16:06:10.914Z", "1.7.0": "2016-01-04T21:28:45.065Z", "1.8.0": "2016-01-14T00:02:48.966Z", "1.9.0": "2016-01-27T22:19:42.157Z", "1.9.1": "2017-01-27T00:17:51.667Z", "1.9.2": "2017-01-27T23:09:36.935Z", "2.0.0": "2017-01-30T19:07:26.986Z", "2.0.1": "2019-12-06T18:43:01.710Z", "2.0.2": "2022-09-21T19:53:18.615Z"}, "homepage": "https://facebook.github.io/watchman/", "keywords": ["facebook", "watchman", "file", "watch", "watcher", "watching", "fs.watch", "fswatcher", "fs", "glob", "utility"], "repository": {"type": "git", "url": "git+ssh://**************/facebook/watchman.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md", "users": {"sharper": true, "anhulife": true, "marcbachmann": true, "balupton": true, "nano": true, "shaner": true, "shanewholloway": true, "jamamuuga": true, "sergii_m": true, "rumkin": true}}