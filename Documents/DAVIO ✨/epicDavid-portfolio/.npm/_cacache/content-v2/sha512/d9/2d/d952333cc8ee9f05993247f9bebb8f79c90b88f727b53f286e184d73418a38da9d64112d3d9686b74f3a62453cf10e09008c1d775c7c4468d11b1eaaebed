{"_id": "natural-compare", "_rev": "6-f10261266c4fc2a006c6c5136a94060f", "name": "natural-compare", "description": "Compare strings containing a mix of letters and numbers in the way a human being would in sort order.", "dist-tags": {"latest": "1.4.0"}, "versions": {"1.2.2": {"name": "natural-compare", "version": "1.2.2", "license": "MIT", "author": {"name": "<PERSON>", "url": "https://github.com/megawac/natural-compare"}, "description": "Compare strings containing a mix of letters and numbers in the way a human being would in sort order.", "keywords": ["string", "natural", "order", "sort", "natsort", "natcmp", "compare", "alphanum"], "main": "index.js", "scripts": {"travis-test": "istanbul cover ./tests/run.js && (coveralls < coverage/lcov.info || exit 0)", "test": "node tests/run.js"}, "repository": {"type": "git", "url": "git://github.com/litejs/natural-compare.git"}, "bugs": {"url": "https://github.com/litejs/natural-compare/issues"}, "devDependencies": {"testman": "*"}, "gitHead": "1e0e6650d31f87577a2452f59b379cd1eb226b22", "homepage": "https://github.com/litejs/natural-compare", "_id": "natural-compare@1.2.2", "_shasum": "1f96d60e3141cac1b6d05653ce0daeac763af6aa", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.10.33", "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "maintainers": [{"name": "megawac", "email": "<EMAIL>"}], "dist": {"shasum": "1f96d60e3141cac1b6d05653ce0daeac763af6aa", "tarball": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.2.2.tgz", "integrity": "sha512-0RFYWi+nj7ljSRvWvY29EW63ytmlg0zP7PkpscsffMcqkrD9ZgFQlzX4VFqzijXErXKJ217VbDlIi6nIGnWcOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvwitzVyqX+Vz8fcN0SOndWoKY1EX/29pU+mWOlQeoRgIhAOqSu+fAFicJ5VglDQPdrHvac3dsqM1KOYetDwWGNZ0+"}]}, "directories": {}}, "1.4.0": {"name": "natural-compare", "version": "1.4.0", "stability": 3, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/litejs/natural-compare-lite"}, "license": "MIT", "description": "Compare strings containing a mix of letters and numbers in the way a human being would in sort order.", "keywords": ["string", "natural", "order", "sort", "natsort", "natcmp", "compare", "alphanum", "litejs"], "main": "index.js", "files": ["index.js"], "scripts": {"build": "node node_modules/buildman/index.js --all", "test": "node tests/index.js"}, "repository": {"type": "git", "url": "git://github.com/litejs/natural-compare-lite.git"}, "bugs": {"url": "https://github.com/litejs/natural-compare-lite/issues"}, "devDependencies": {"buildman": "*", "testman": "*"}, "buildman": {"dist/index-min.js": {"banner": "/*! litejs.com/MIT-LICENSE.txt */", "input": "index.js"}}, "gitHead": "eec83eee67cfac84d6db30cdd65363f155673770", "homepage": "https://github.com/litejs/natural-compare-lite#readme", "_id": "natural-compare@1.4.0", "_shasum": "4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "megawac", "email": "<EMAIL>"}, "dist": {"shasum": "4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7", "tarball": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPq/XTtCMobyEA0cLLz+01T37O31Niib31kV74IvyvmAiBNo0BZ6FecUm1NonpCwsOAJZjtT1NP6AS9DDWUE9klcQ=="}]}, "maintainers": [{"name": "megawac", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/natural-compare-1.4.0.tgz_1469220490086_0.1379237591754645"}, "directories": {}}}, "readme": "\n[Build]:    http://img.shields.io/travis/litejs/natural-compare-lite.png\n[Coverage]: http://img.shields.io/coveralls/litejs/natural-compare-lite.png\n[1]: https://travis-ci.org/litejs/natural-compare-lite\n[2]: https://coveralls.io/r/litejs/natural-compare-lite\n[npm package]: https://npmjs.org/package/natural-compare-lite\n[GitHub repo]: https://github.com/litejs/natural-compare-lite\n\n\n\n    @version    1.4.0\n    @date       2015-10-26\n    @stability  3 - Stable\n\n\nNatural Compare &ndash; [![Build][]][1] [![Coverage][]][2]\n===============\n\nCompare strings containing a mix of letters and numbers\nin the way a human being would in sort order.\nThis is described as a \"natural ordering\".\n\n```text\nStandard sorting:   Natural order sorting:\n    img1.png            img1.png\n    img10.png           img2.png\n    img12.png           img10.png\n    img2.png            img12.png\n```\n\nString.naturalCompare returns a number indicating\nwhether a reference string comes before or after or is the same\nas the given string in sort order.\nUse it with builtin sort() function.\n\n\n\n### Installation\n\n- In browser\n\n```html\n<script src=min.natural-compare.js></script>\n```\n\n- In node.js: `npm install natural-compare-lite`\n\n```javascript\nrequire(\"natural-compare-lite\")\n```\n\n### Usage\n\n```javascript\n// Simple case sensitive example\nvar a = [\"z1.doc\", \"z10.doc\", \"z17.doc\", \"z2.doc\", \"z23.doc\", \"z3.doc\"];\na.sort(String.naturalCompare);\n// [\"z1.doc\", \"z2.doc\", \"z3.doc\", \"z10.doc\", \"z17.doc\", \"z23.doc\"]\n\n// Use wrapper function for case insensitivity\na.sort(function(a, b){\n  return String.naturalCompare(a.toLowerCase(), b.toLowerCase());\n})\n\n// In most cases we want to sort an array of objects\nvar a = [ {\"street\":\"350 5th Ave\", \"room\":\"A-1021\"}\n        , {\"street\":\"350 5th Ave\", \"room\":\"A-21046-b\"} ];\n\n// sort by street, then by room\na.sort(function(a, b){\n  return String.naturalCompare(a.street, b.street) || String.naturalCompare(a.room, b.room);\n})\n\n// When text transformation is needed (eg toLowerCase()),\n// it is best for performance to keep\n// transformed key in that object.\n// There are no need to do text transformation\n// on each comparision when sorting.\nvar a = [ {\"make\":\"Audi\", \"model\":\"A6\"}\n        , {\"make\":\"Kia\",  \"model\":\"Rio\"} ];\n\n// sort by make, then by model\na.map(function(car){\n  car.sort_key = (car.make + \" \" + car.model).toLowerCase();\n})\na.sort(function(a, b){\n  return String.naturalCompare(a.sort_key, b.sort_key);\n})\n```\n\n- Works well with dates in ISO format eg \"Rev 2012-07-26.doc\".\n\n\n### Custom alphabet\n\nIt is possible to configure a custom alphabet\nto achieve a desired order.\n\n```javascript\n// Estonian alphabet\nString.alphabet = \"ABDEFGHIJKLMNOPRSŠZŽTUVÕÄÖÜXYabdefghijklmnoprsšzžtuvõäöüxy\"\n[\"t\", \"z\", \"x\", \"õ\"].sort(String.naturalCompare)\n// [\"z\", \"t\", \"õ\", \"x\"]\n\n// Russian alphabet\nString.alphabet = \"АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдеёжзийклмнопрстуфхцчшщъыьэюя\"\n[\"Ё\", \"А\", \"Б\"].sort(String.naturalCompare)\n// [\"А\", \"Б\", \"Ё\"]\n```\n\n\nExternal links\n--------------\n\n-   [GitHub repo][https://github.com/litejs/natural-compare-lite]\n-   [jsperf test](http://jsperf.com/natural-sort-2/12)\n\n\nLicence\n-------\n\nCopyright (c) 2012-2015 Lauri Rooden &lt;<EMAIL>&gt;  \n[The MIT License](http://lauri.rooden.ee/mit-license.txt)\n\n\n", "maintainers": [{"name": "megawac", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:33:03.092Z", "created": "2014-11-18T01:04:27.323Z", "1.2.2": "2014-11-18T01:04:27.323Z", "1.4.0": "2016-07-22T20:48:12.217Z"}, "homepage": "https://github.com/litejs/natural-compare-lite#readme", "keywords": ["string", "natural", "order", "sort", "natsort", "natcmp", "compare", "alphanum", "litejs"], "repository": {"type": "git", "url": "git://github.com/litejs/natural-compare-lite.git"}, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/litejs/natural-compare-lite"}, "bugs": {"url": "https://github.com/litejs/natural-compare-lite/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}