{"_id": "ansi-escapes", "_rev": "37-54739ac06d6e9b915da3b7ac4e3c7bd6", "name": "ansi-escapes", "description": "ANSI escape codes for manipulating the terminal", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0": {"name": "ansi-escapes", "version": "1.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-escapes"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor"], "devDependencies": {"ava": "0.0.4", "xo": "*"}, "gitHead": "59244470a66fa25569347bcf3e9c9968a2229c34", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes", "_id": "ansi-escapes@1.0.0", "_shasum": "cbc220a180a794525ccf0a85058e314403fe89a5", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cbc220a180a794525ccf0a85058e314403fe89a5", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.0.0.tgz", "integrity": "sha512-lUxcCVTMZgCLM7Zc02Y3pOduWTk5p8vqCn7aLxWhe2XMsBzckFoY1iZEli4uobVFLpLQbKAXLnl9vntofgaoIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVt8vqtT6yVv8Bm86NKHggq4B3Md3ZvR6NK0dNUBa4ngIgc98PUYhRBSxSMLz901NRU1lzMSRakT7n6U2ukI+DRQE="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "ansi-escapes", "version": "1.1.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-escapes"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor"], "devDependencies": {"ava": "0.0.4", "xo": "*"}, "gitHead": "2775b221ad6b5ce33a8a7ca1d9b59ba7d923b5c8", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes", "_id": "ansi-escapes@1.1.0", "_shasum": "21608edd3a4fc5a568c3b8d83ed4acc289d5ec77", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "21608edd3a4fc5a568c3b8d83ed4acc289d5ec77", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.1.0.tgz", "integrity": "sha512-aiV6wnePSUBN66glOx+nHsCwkhNc1qd8qyRvAwrQHv94+d2z1amcxrEY3MKyA7uj7/PuslFnf88mSPTa8fFoVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICD+xE/TdzAFo8js8cHZ9KYD9NTUsJIaip7QMVBr4x3nAiAcBs0KCRGkdqQ+D2N8n9G5WE8cAPjcl7Ud8JIsyt5e6g=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "ansi-escapes", "version": "1.1.1", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-escapes"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "3dff027c48a59a377ed44b6d942b1b4f007c326f", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes", "_id": "ansi-escapes@1.1.1", "_shasum": "cc9c0b193ac4c2b99a19f9b9fbc18ff5edd1d0a8", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cc9c0b193ac4c2b99a19f9b9fbc18ff5edd1d0a8", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.1.1.tgz", "integrity": "sha512-6XfH0l7AdJkQHkCwxtEhKY6aARDdMxKMUoiYYzmWe8mZxhDbGvqhGlwf1DnOxHlOR2SCQl0ycbPHg7PtFBEViQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB93wQg0A4sXz+td3Rtp/C6LMol8daoBs0tu5JnhJsPJAiAttRSttbBTnl3o1kZ+SrknQ6DFcEdiIimwxW/Wc1GTbQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "ansi-escapes", "version": "1.2.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-escapes"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "38584c66489a9144f83fd29462b3f0018ab2d4b9", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes", "_id": "ansi-escapes@1.2.0", "_shasum": "9fe306ae90777d4bd58a13c39a1387c14667b46e", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9fe306ae90777d4bd58a13c39a1387c14667b46e", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.2.0.tgz", "integrity": "sha512-Ua6M1k9gGswjIPkOEpCPNzWgXhGxRJd7vtmW4xAGi93sNsO2SZzDVLgjNgF513HTo79cAlCz+bKYqvEcNjVeWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJFEHAcdWU/jPIi7RARLdNk4MXG7uN5AyFFcRi+hPzrQIhANTvxvK35YYLfoWaeWN28Ec3NLu6lSyvzCABWbt4cqG7"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-escapes-1.2.0.tgz_1457170160083_0.6607096220832318"}, "directories": {}}, "1.3.0": {"name": "ansi-escapes", "version": "1.3.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-escapes"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "9ecd532e37e888c5ea08cf0cf87e1c4f6e4bd858", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes", "_id": "ansi-escapes@1.3.0", "_shasum": "070883c337d5e4ce9e124fce2639267f2a14d554", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "070883c337d5e4ce9e124fce2639267f2a14d554", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.3.0.tgz", "integrity": "sha512-TcoTlJO3UJeMriq3t44LlocFkRrqXIH1hDSGl8uCE/C67w7w1o9E20KlFZtBvpxdYkDemG6s5YTl65GEQdrzVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeo7Nh/2VFADKgMsF4JHs38NvWVtCU9FPd4B1tX6P+9gIgRmP9r86wJqr2iNSVM9GuNc3it+fXQUeWGmGYhofVXhk="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-escapes-1.3.0.tgz_1457516031370_0.8470738974865526"}, "directories": {}}, "1.4.0": {"name": "ansi-escapes", "version": "1.4.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "763a11847148479dd315c2b9f81b001c94740415", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@1.4.0", "_shasum": "d3a8a83b319aa67793662b13e761c7911422306e", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d3a8a83b319aa67793662b13e761c7911422306e", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.4.0.tgz", "integrity": "sha512-wiXutNjDUlNEDWHcYH3jtZUhd3c4/VojassD8zHdHCY13xbZy2XbW+NKQwA0tWGBVzDA9qEzYwfoSsWmviidhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIvu2syIODU/4pxR+Dh0R3ugbzyDJKOl6P1911PQw2OQIhALgq8hnUx7Eco8LU+WxXIPX2rUS+8+ZBqasOcV3NED6Y"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ansi-escapes-1.4.0.tgz_1460925437568_0.228597579523921"}, "directories": {}}, "2.0.0": {"name": "ansi-escapes", "version": "2.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "5dcd620fd52165650d440152ce49fb3d3c679381", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@2.0.0", "_shasum": "5bae52be424878dd9783e8910e3fc2922e83c81b", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5bae52be424878dd9783e8910e3fc2922e83c81b", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-2.0.0.tgz", "integrity": "sha512-tH/fSoQp4DrEodDK3QpdiWiZTSe7sBJ9eOqcQBZ0o9HTM+5M/viSEn+sPMoTuPjQQ8n++w3QJoPEjt8LVPcrCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZT8aO9qaSt8bA8ZZbByYaxEA0Nqx+zYZdVm5/KMtkWwIhAMhPc67k0goDd1ktwT5bP1jDu1ATtEfHfisbNtxNPKxl"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-escapes-2.0.0.tgz_1492961578751_0.06489237071946263"}, "directories": {}}, "3.0.0": {"name": "ansi-escapes", "version": "3.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "caedc6d277c5e7c4d3ecaa31ebe0b8b1c13086fa", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@3.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-O/klc27mWNUigtv0F8NJWbLF00OcegQalkqKURWdosW08YZKi4m6CnSUSvIZG1otNJbTWhN01Hhz389DW7mvDQ==", "shasum": "ec3e8b4e9f8064fc02c3ac9b65f1c275bda8ef92", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGcKcRgLUqGU+hkhLi3OFtgpWrinsfiHRVPysaq6u8AeAiEA+xbaSIAOqqXV1tPgZnFN21As4M/9T3ye6gPRqCSYjR0="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes-3.0.0.tgz_1504607767081_0.4022042681463063"}, "directories": {}}, "3.1.0": {"name": "ansi-escapes", "version": "3.1.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "e8a567fa11ede53430ad464d4ec555efdd23f2a1", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@3.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UgAb8H9D41AQnu/PbWlCofQVcnV4Gs2bBJi9eZPxfU/hgglFh3SMDMENRIqdr7H6XFnXdoknctFByVsCOotTVw==", "shasum": "f73207bb81207d75fd6c83f125af26eea378ca30", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.1.0.tgz", "fileCount": 4, "unpackedSize": 7813, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8/3Kaia4rlM6ZNC3e6GYNWhdx/bY0sx4sIn9bbjtDqQIhAMH+s3ueAfGjYci5rckd1k1ApTZ1D/4aXTBx4yMAU+Bv"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_3.1.0_1521965687676_0.4702464505871864"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "ansi-escapes", "version": "3.2.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "e6a876da131e0b62fd6b2f32605ff5bfbaa32078", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@3.2.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==", "shasum": "8780b98ff9dbf5638152d1f1fe5c1d7b4442976b", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz", "fileCount": 4, "unpackedSize": 8289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTnjbCRA9TVsSAnZWagAAS+AP+gI5AtI6qKqDqFK2Q7F1\ntu37a0wWqJ0ZAPzkMClVh8e7yB/7sdWN3dCcNI303ADGNaIs0AGpgu6M1r7a\nJEq6sozt/EF2aJ890iUrelp4uICxyf2uBje/9s2s2kyp4hwztGpp3ZTtgxh2\nLI00vHR6ntsoih+S8SFBW/rZrRZFmutE3/5wmstjJ0Y0c9Iw1fcQz3p/rhoZ\n1M7mltuvebCnNkxrG0P5c04I9uj7Cdye7rp6LAeIGozlUPjO8OFLSiUoVeZa\n5CZjux4lZ2iuXy7MZFgFOrypB2r5L0Uc9nTJ7BYHON8C19hvBVh4eQv0fydO\n/c9cQ56mstByCCwKZOws5WWBHSc3uy6k+mvfXg42x6Fzr1ppZxYMoJAp4em8\nkn8SUNI2NON7JzNB127wiWovz+KsTywyTT3tANQcZlkPvSlaoo6MFIPz1lu2\nSrxn3bWWQaPzPBDsYcSjsrpGVsxFP8vDyqx6aVxe37AzvdUzYkp6WqFHwHZt\ntScnEyymUlapX+NtF/ieXVY6qbo8ld5Ix+tUt6wJc4X81La0ml609nOQbxi1\na7SKooRsQW4erVltb8jR+ZeDRti8Yr+1pEsCYNeab1rC2NJNCybNu0WMEGmU\naPu1v5QPepuiDNPGJKhpYeHNwuu77AnKA5BE3rPLN3u2Nc4rUU0GmP0WDbvy\n/RmR\r\n=snKF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb0cdNztFHfxTt+2DSlz5GhB6nXC+tZKFO7HD57N+bugIhAP5KF6maVvGUA1Ch6rJqid/pSizHGflL6+atmgiaxqHd"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_3.2.0_1548646618951_0.14853970908921688"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "ansi-escapes", "version": "4.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"@types/node": "^11.11.3", "ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "6d2425d0556594c0c4ac94b6f1537b027dbd2054", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yTxlpJLHlj5UGassoMNfTxuGdhDOP44JFmuwzNKgWrDWOujtyNcgeyRa6rDQcd0RnsY7hdESi7rl7cm5F8Y5AA==", "shasum": "1a0f6f3642794833d430c97d75f7d1445a087cc0", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.0.0.tgz", "fileCount": 5, "unpackedSize": 12813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckPgfCRA9TVsSAnZWagAAxWAP/1ml6GN8Q9uPE/GSzDgE\nJoYfVcamatE5H+thFf55lcYskTuq4IiEQRKzTGFZQLT9W5gen2RBsA+s4/j5\nFM51vXrD3m/iodRtRlyeJLAG95ciGPA5zVLrVx4Ekxa+67PREFjtmQKmKRZE\n4TPYZqC2a8rCLrL73fQTEhE6ez72A+6RXzTAK3jY6yWXzkzrrlBCz4/axH/y\nAFvu8ZxzYHwmDvQGVyEDBKCb3rzGS1nOP8xT2j4NSXSUEeEze/XLoFS7z9FS\ndiTE/GlkGapIk7LANg+Ta86Bj5qLONSCJnofBnnhxKcRhZDyhH460Kj2CrvK\nRpNDrD+HpjAagxGzAhiv0NZFZelU61Snp6U5RlmpF+RbHnokXNre3A+gTZl/\nIpGQhv+EPOzqZhOV5SyJ55F8WVAYU7rljYYLljgKEZjTXcFDlDiHREVu8Y7S\nMwKDR3l5J0uDQZi9J/330IlErShFqN6evjr8GMqttCbRbAIOks/5CO+MSSA3\nOyO9xPAw/r7gnomh/ftU5B9KDRR2sSVzOxr1fPhtg1s6ud83xvobQVQCk6C4\nTvb1nPmZ0A53cIYSiTOeywmv5/amGY8mO9GmfQTSW6JP5cxVVllaojVKOCZx\n9Ip1UgHLLeqdwO+8Y44sYKH8ZH9TxrGYD7DiP8HWYkPYfLqArqGrCnokTiP1\nR95b\r\n=xv2h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzpPlLJU64w/WsRj73nolQ29Msjuzo0lppxpgNEGTFNwIhAPg0ETLz3UEgNRdOgpKH1gkQM/CBG+BJ/UZb+iWlM6er"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.0.0_1553004574265_0.0382736518557496"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "ansi-escapes", "version": "4.0.1", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.3.0"}, "devDependencies": {"@types/node": "^11.11.3", "ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "d32406f15c7075d90bf629c562dcf9a4586189fc", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5lJW5pWCsu4epCsw15XA3yMy/QBZMvesTqgZCxhwENAjN4Ip5cG54xDSWWPX+Y03mmJtSPwiktB0n/R7t/0mlw==", "shasum": "b1744fe3d11de9171df1af64dcf8a2acb926609b", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.0.1.tgz", "fileCount": 5, "unpackedSize": 12962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckd1yCRA9TVsSAnZWagAAniIP/itlEaJ47W12KrDoOfGn\nV4UbQ4VAHgbS4hlceVdmOxoeNTy10TdMw96w9FYPxzFWqmAEWVZOv4zHkLI5\n8SC9tAXRjrXiYDgaHhoNybYki4v4OjL1kuB64onpv+SnAAitrmdu2kYd4ceA\nwPsIphH2S1KxygDcMFhq3gulF5/D2jENAuMdtGaQYA39FQk2RSJMNJZXbRc2\ncVXkeVLNv4oDEiaPONOQdgc3DkKBSple1eS+sFE63r4eYUEuZeMau6J22N2r\nJR0zADfkmULr+QIYJIdN5wTLrDzpg4g7B1apWQUYp8n35d5T3NCj8pZCQlud\nbl/kBMOJJU/NoInNhYEjBxbmdaRJhz/tIXUKZh14iyANkMwn0Wyw4LblarG0\ngp5B5PBudrFUAcgcgxGqPYztG9UaG20JUtskClxzE1VfheWXZFRM/OP3R659\nIWICovVaBPzu92boctHiNGapET7DYltyP+7P+rvF6hE6TCHwkBYnurSX4mMU\nBds9fOdUCUO54UFRP1VwDtnjGohtf0AQPn9pUdcRe48WyUlBwh1LJhlcuhsw\nLMbf/f+As3HNKZzh1S+vhNvKwh3ceJknstdeerURz0cA37gPCstCyCy8dSMD\ngWX5XBgYlqP+6bTMLuEaEv2qpq9e+D2n0/1/e7ARA3/+xmwqGWR4zmIGxlQM\nGGH6\r\n=6NuM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8490g0oDHmN+Y7QgpqPKwm5wxt1MsEbCFX2099qi84AiEA01KXo3g+E7sp/nqUVEVFGdLg+3IfSw3bKn5PVvZREzo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.0.1_1553063281598_0.9054858435430257"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "ansi-escapes", "version": "4.1.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.3.0"}, "devDependencies": {"@types/node": "^11.12.2", "ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "aa6240f064ddc1c719c917e841e1351e5c2dc7d0", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2VY/iCUZTDLD/qxptS3Zn3c6k2MeIbYqjRXqM8T5oC7N2mMjh3xIU3oYru6cHGbldFa9h5i8N0fP65UaUqrMWA==", "shasum": "62a9e5fa78e99c5bb588b1796855f5d729231b53", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.1.0.tgz", "fileCount": 5, "unpackedSize": 13128, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoLVbCRA9TVsSAnZWagAAAwYP/0hw+ntphEwjGnvjmPf/\nGjSQOz0EyMBHzL2KRUFdhhUJyf/Dc0hLSbfuP6SQPYIibSbWREa89KWOYUXY\nEKmDnpFAqHH0ZEGcF0N1Q3VOKoaiP2Vfe2cgENLacbtlxXT2AYRf6nvMP12P\n/n4CKKO8OMQu3hdHyi2FguEc8VfKibsa4QuY0i1QWtuCF7PXPzREy7R37EHv\ntxg8IUhnVpROES2i3b71xkUX6azY4IcbzA9J6YCPdKXK6edqBoJqNHKqiXfN\nk43F1CI9bvhCvM3GV+Mi+9bwDmAsmxYW2xuMqBCpBKL21Lj8IA0a4mOFnYqj\nXoesYa4Gd/6Zl+1ZQm06U87C+cLwMpQg4Snn4INexDZ71X64RagWLgfDmyeb\njp4303Qrbd2JoEs5gqf6mW9ql5PMK8vjtsZPyf/f9Jh+Zk3JlIuAHutyBYYE\nG7ZzTal8BkB7pn6npnctq1lGV9BANA/Oq3CZxAFHZj7ZpUpCtfdJCerHYmci\nNKMoRyDwgpQWVQiYarRF1Q43pRh+sYE/1Xdh1icTY7v+CQQ7o+oFqQyq1rDn\nXKZsiaqcXVDMyTeaknsl4JTfnw6FXlYYQYD2/t5kS/Rf5vI7kYzUPnLRzWUn\nzAXDcvySCidi/Mkf6vgMqBeQ2MTECkgBPGgchiOKaIy3ABKMQ6kZgjfWrMul\nvx0X\r\n=nsqP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVbDG6aFvDhaFXZ/DZh9gnZPau1QsHJg62z6vpktQB/gIgOTMBzVOeSVsMGLdpowb/MH2b8HzKZB4bcub/InQSU+U="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.1.0_1554036058668_0.8029870950801763"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "ansi-escapes", "version": "4.2.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.5.2"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "88d0fcfbcb08b40562a5d7fab902603e8fb88af7", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.2.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0+VX4uhi8m3aNbzoqKmkAVOEj6uQzcUHXoFPkKjhZPTpGRUBqVh930KbB6PS4zIyDZccphlLIYlu8nsjFzkXwg==", "shasum": "c38600259cefba178ee3f7166c5ea3a5dd2e88fc", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.2.0.tgz", "fileCount": 5, "unpackedSize": 13522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/9eTCRA9TVsSAnZWagAAPEcP/i06zFp7Me8antNXz4qb\nrVl8Qr8rSgJScFuVGanuwyBMyoSu3RKeBTwEO76i5Ear+ydL3v4bTpm1z9ci\nKF7sF5+iP5McZRlZjC01SCGuZbW5Qe4H62VPEIez+D3LaQSi4jv6m9oXl51y\n7Ibk1WNbSxSCF2SdsJ930tffuJHccg2nCOQjOMJ+C4Nh9qt6kScldJ+lOHZU\nw0K5LpVB23SZNrjcj2o5f3JXXqa0VRnNkcZeD+/HX0dlxDFnbBNW/ljjThRd\nvV7Sp2cukqMyfPDHcmB1QAiJO7LdbRLEfchbBRRDXqQE6uZZENUC2xo0q5WT\nd0ciPHQa3o4RXsNLGvBcJ8Qc2mb5UkXeROHMC2dA40ADq2QNSbxBksIy5EjL\n3OJaozdWrqZHm/x4hLmH9+32nq0EX10nnPomJIJHuI7R4QMaaTaUgv8YF7c6\n6VNbDgV7EtnnleHABWQuZovu28SU3YjbIW4iM0v4umT+jpXuS5fW5/oLu94K\nAEm/02Xd6aoDlIiM8Coc0JEhBY+5yO1nWfgRvaaGRGZ+jNGqEK3P1sz63a1x\nktDPuSdpulG5bYhYBzceuWsmb3ypaFJt3HhNbS8ronONf9uWR0VpMUGzA+cO\nW9Ax0JTaE+IyrmWCWl3a4O0RKdB+KhFjGXykqymMjmeIytnPwDVQcTTpNPyU\nz9mK\r\n=tRgt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDIE/ty7nafz0WAqu1j7iIohVrVVrxOaDHESmwFPqmyGAiBsLdodyVqoiXzd0d2gxWiVyodDf5V/jkuK/bzkuc3ItA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.2.0_1560270739046_0.4091127140783226"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "ansi-escapes", "version": "4.2.1", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.5.2"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "1c3ba8504fb06889717918aece5c1ab70be26c0d", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.2.1", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Cg3ymMAdN10wOk/VYfLV7KCQyv7EDirJ64500sU7n9UlmioEtDuU5Gd+hj73hXSU/ex7tHJSssmyftDdkMLO8Q==", "shasum": "4dccdb846c3eee10f6d64dea66273eab90c37228", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.2.1.tgz", "fileCount": 5, "unpackedSize": 13536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQgBsCRA9TVsSAnZWagAAu+wP/2nJKH3hfsMzFis0otp0\n9b7tv68E+LHt9vsbBWE3sMzTnEhXxhZsDzTlWHD/iG4rWlZ7SWaJ+0cLkiGv\nETMBfNqKpvhNQ5pLmPDhzxfvGer8Pq1BZRjN3l2dARRhtY1NkVP+S/V/mE0d\nHOqm/hJNPHNg13tgUFU0K9hia8zpHaMBqd4i2VIi96fzaW68WjhaNke2Pmpd\nrumwbsL8BeX5K0flYPZ94xdyWw13zEcqPb5pi2gpYhRgouCauDBXR5VJTClJ\nEAOtDVqdmARx73zTU23W938ODAzYcclNhavzNKi+3Ka60pnv9b6eWXLCHZqw\ntvXoZcqwfVDslVOexuT8ClEtkslw2VOjzQT7454Mj4+JZmSf2Ol7/shtawAx\nrM9V52dVcV0d+M/w4sS5Qmxb1JhjIhzeOiLww/rpyAyR8pJ7YYSB7d6vzVQq\nVUWzaQWawX9g4Viyv06aNvVu0AZZWOBqkkz2mVJEci+7501xhVSmAJ44E445\n3ip9o9wvClL9Um0Bv22ApxeU0OQjmGpe+R05t7+kWFy6BhQFt7ruFCABwDpb\nWd60wWP6rc5L1yxAUutblnhZNjHsy3GuGf9UzgMSJv2cPhvGQ5JvBLkGiw1O\nSLE7437ipzsjRYmuoA6DQ+6nLfXTMQJedfFu589MRYhEyIdj/6l6xTDbdOp7\n1bwl\r\n=PvjN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC021PfvfvFc3Cd/CuHzqHLAiZIluNWxrBsbLYMl4Z17gIhAJkzNJI2yrx/66+/fO14YNTwmC5xbyQS2ja1pd2YTZ41"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.2.1_1564606572143_0.5698847544817396"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "ansi-escapes", "version": "4.3.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "01aa744d6568d1daf20d48d071a08c171445db42", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.3.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EiYhwo0v255HUL6eDyuLrXEkTi7WwVCLAw+SeOQ7M7qdun1z1pum4DEm/nuqIVbPvi9RPPc9k9LbyBv6H0DwVg==", "shasum": "a4ce2b33d6b214b7950d8595c212f12ac9cc569d", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.0.tgz", "fileCount": 5, "unpackedSize": 16441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0CnCCRA9TVsSAnZWagAA9VoP/3R2+kvuyS8v5ta9At8t\nkMdPerLPjURYtfGkCO1/4p5/Qo6KvbGvFmO8bYuybN33bpJjaeuI1m2zxS/V\n//b/g8b+kmzvwdeZtkSojfiROeu7m/YNVKj0wZ5Kd0FnAhRe1tYL0D01D5Xs\nYr0xdgyFGzitNfiBTtIjxFthj36TvmRy5zlOwFqT+xcAMLE2rqFQJviicsNF\n/hawimEGuJoc6SafneARRkdA0bUDZ7h2Hx1i6vtx0SI1ASenmuVMt1x12V9S\nwUt0dalsArv82oOVWZxitLKKwuCZ5YGqO9qtlAXDtDW+TnQep7DsG48twlg8\nl4MdDioSr9YvQTt+s9ISG90w+SrvA+SNBXbD+xJNxiarq83GP8U/UuFRlrHu\nZTwJX5r2xJiqP2a1wYcpB4LCxeCjgcc0AZPCIVdVWEbTKzPC6Q5zfvU4bY7+\nu+j2GLryBsg3L2iosDNLTneOtjEMEC202DG75U1F7D8+4xuexYnzZxP/SID9\nvDbVC7T9s2CwZqNBmLjY/yuUA8x5w76cOabG+zEW9qhaE4tc4WROcKXKuHM+\n/2fuQ2Rj3Sxtu+FsSg02J7b/MFHRfBk0Jo+DZK/KXGkSkZX2QsAY9W2JcZfa\nNewUJe2H86DaAg3vMvgjNwA8CN5rp1qKyU4MvFfC7fdHFfXCzagcrJ/MduLv\neRBC\r\n=WD1/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6Vj9y1zBg9cSukma9ES8LbGZ6ezhP0nA5hQTADkm6vAiBK9FP3dg17GFJq1aWxw8x1M/acKukeGPpZdDcSf5UZsw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.3.0_1573923265964_0.6422523404205711"}, "_hasShrinkwrap": false}, "4.3.1": {"name": "ansi-escapes", "version": "4.3.1", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.11.0"}, "devDependencies": {"@types/node": "^13.7.7", "ava": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "2e6a4359b10e4b0320e6dad9857ea04f0decbda4", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.3.1", "_nodeVersion": "10.18.1", "_npmVersion": "6.14.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JWF7ocqNrp8u9oqpgV+wH5ftbt+cfvv+PTjOvKLT3AdYly/LmORARfEVT1iyjwN+4MqE5UmVKoAdIBqeoCHgLA==", "shasum": "a5c47cc43181f1f38ffd7076837700d395522a61", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.1.tgz", "fileCount": 5, "unpackedSize": 16514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeW76iCRA9TVsSAnZWagAAkE4P/102TolOQdfllXs3INMq\nP56Sk9ogCptk0fofbhVjimGWXA8N1oCJrt/YXpQlb/MlyQx++Sn0FbozPpgv\nruyUhvWNfn7Ay0mXd65jvZ+LhnjQksI+s1gJCxiWPRCxmlPIHbu7yGTIlYFG\nE6sVpt/Gn6ZsUZ7J/eqCqNf8YP0L6IBKwrJSu2BhXLFzCtxeSmECcWFsreOB\n8JYpCaKUrFwdkOl9SCIiZEcrHYV+7oOcOeABMN20DI/3eyQlhTbYOWMQrKFm\n/0bCFjLSLtJ/lBdGg3gSq12gMPQGWoCMi4WL8TDvp/7DxAgPT9eklGSvhd3c\n4B+9EW/ukLknvJeJ3n1LooCm9M7y/ML3dtwDYeM5PKyfG4MI7cP09aWoT6dD\nVhzQlY1n4kQVjw8rd9spCOITe4vSOA5ad25nGN7EIpX+9W2txRJPd7zCz8tG\nYCT8E+1sdUHZ4Jke+GvHapX/Wr2sBlrTXtzjOjoSlLrVaN9ijv5OaWXEXRLu\nTJpshsubiK4aDO3Q8g3kx17xzr+WwjhumlsZintkZ7eAuEqoWIs3hFIPRWjr\nH+I+YzsJqe37fVMOXLjHJu2c3csed1Jbg8nzI2vn+j5LlcdNdWEA2lhSSSRq\nMumzYpsBr6+bpNjL8uCIV4t7h9XWhwBJ0EQus/gma+zWfIl5YSmMuIFDzEdL\nR+qU\r\n=81LK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCR1mPxKxS9Ngtu1jFlHViWDO/zt0VsjfRfdlgpGb72zAIhALAQ2zrtdKgopbSZeAYzQf/WtyXwkUHgiZJLnJsVdV07"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.3.1_1583070881759_0.1811155598951324"}, "_hasShrinkwrap": false}, "4.3.2": {"name": "ansi-escapes", "version": "4.3.2", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.21.3"}, "devDependencies": {"@types/node": "^13.7.7", "ava": "^2.1.0", "tsd": "^0.14.0", "xo": "^0.25.3"}, "gitHead": "ca9d5f8eb6ecfcf6ef2d8764a056d865e68bf178", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@4.3.2", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==", "shasum": "6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "fileCount": 5, "unpackedSize": 16380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0GVCRA9TVsSAnZWagAA01QP/iQzi3/Eh7+9gyoeDmRE\nZZDU23YdF16Mau6mjE93XbuYftwgPouvi927JL6TqTY7EVBC+kkgru60x7bJ\nYceDwou5XMnUx6vSX6JIGFUlkWNwgntopQHIY6ezoF25OpLOcmL/8srr/DU2\nch8IpOoDkTH61LC41tGNzy0WsueaxYzkiG0BFyY3/HZoHh9hrTgdBhrdtenl\nQWXSxAwediH+6ow7qdTIf6Dah2lg1964yGHjQy7niqJYAYToNWpGns2zSVKB\nBtA7+hITpd0ntzBtuQmgcVh8D2TGVtMO/QovOw5VqrLdEzHVDD0/s/JxDcGb\n5ZAwaDSY976VuYbfEKikVeGxSCrRdM4QvY9tzS86ghamQhwXUmPuV8PJvxbZ\neyCGER7X4TpKkLyzz+AkhkLjl07Afw9Gbv7UZ9aENhfBQcmT1aUhO2C69LQZ\negXieiqDHinSme6YKe4V6+NoYMse24iHlNXYXwr64XWpCJbjBBgtRGcSvC5X\n6HYOdVSdGGy6SDwwNaH9nV1ZWx1YTh0/uzyw0EK3dBIC/FY2XiBxK4uwIqVJ\nadXzrjt7KXN3Px/YhY/q4GMsej816TK8w1Q/W3tbQsYvVJUr+/0U+PTpRTPM\nfHwPwz/n9EauGLL/bq/2Vz7t1f23nInhfp0dYNynCv3lDvVvAk4ji1GHyo0S\nwGMJ\r\n=TqPN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFbPxdA0PqY41ns7TVLFrFTqlQ4mS6mTyzlW1/zXteYsAiBDoYC+MDX5RBjHm0L1kfw3IODk/qEzLGCxMtuJ2lGN3Q=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_4.3.2_1616593301387_0.23519493102116495"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "ansi-escapes", "version": "5.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^1.0.2"}, "devDependencies": {"@types/node": "^14.14.41", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "2b3b59c56ff77a2afdee946bff96f1779d10d775", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==", "shasum": "b6a0caf0eef0c41af190e9a749e0c00ec04bb2a6", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-5.0.0.tgz", "fileCount": 5, "unpackedSize": 16335, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge8F6CRA9TVsSAnZWagAAM98P/389bXcLEUonFcBVXpZF\nlDPybEWgR7fPSah7T55LMY1QUVMU8oU0thqysBjLT27Mcl655qp3ig/cmyDE\nEC29kBTy/WYhe5vG3l0YtTuZvRBsvzt4x1F1nYMHq08OGTkWuFlJM9LnIfSq\nOYgnGDTcd27FJ87QQm21seY729WjAN90AitlrCtl0KrNatHArJj4kAsxgCR1\naNFnI/J7Ca2plt94vgEwqjkiZXkEVsjbkmvFk/aQqTYQUvqAr6gcYO3uYlQz\nWtdrSny53S3YpKbeS2L553bjLk7dwZcLMVabqP+Cdc9Uslvt7mFFFdjzXp6r\nkG726T8xw8QaiynKZEALuFB20VDEOof7C4jLw27JR7Gp60qfYozR+kNiSnJM\nSj9Er2KHxPv7MSIhBBg0z0dfjOYe1w8DcGoFh6uy5QGstJ6wWqqLCg8X8+yH\nFh7kr8/nPLm9b6kRBQlixPCWQynbvq6yN1NIS/M0UeMfUsVa/A9T165CT4k4\nzEEBU2UaH+Lpgeae4zk6njNtdqVWYSr0hYgZEq0X2V8oFWefixh7rarQH8d9\ns3cgrMC7YH4XMJ/eQUE6B1FfvvtNvEOU+SI5urg4dC+vrUDfCNnrPl1OdxR+\nFHL+Ua2WyQzBU2R0rfZluDMIxXEZ5OzVuQS0xpZY2PDOlHQEeuUjfsbFc222\n9I10\r\n=qaY9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC16bKCwThdb7yMPaGtsMKukYIb9A7lxwxSs9k+y7y8QwIgcLexE6x7lk5gADqEEzZHNMyqCTyAhgyvJuAEDSiVt84="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_5.0.0_1618723193528_0.5788884379631252"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "ansi-escapes", "version": "6.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^3.0.0"}, "devDependencies": {"@types/node": "^18.7.18", "ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "b10a9b8430318cd65a11dc84a1080b3193960516", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@6.0.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-IG23inYII3dWlU2EyiAiGj6Bwal5GzsgPMwjYGvc1HPE2dgbj4ZB5ToWBKSquKw74nB3TIuOwaI6/jSULzfgrw==", "shasum": "68c580e87a489f6df3d761028bb93093fde6bd8a", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.0.0.tgz", "fileCount": 5, "unpackedSize": 15997, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFxjHrMpB+iFBmVUPTo+MZAJHjFCUAIpq5bIc9RoJw3HAiAw9xb9PJtUzpfYll3eb8U9X34I1uf2OY35ZEOrQQT9fQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKuicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLig//eRTgTq5tAG8yzF34Qzo4mgkVoKnamFkH9/F3wy156JzQeQst\r\nbzf23NqdasiEMTs64gZulVDMgGlcVOxgTgW2MmQ1PKkHF2aVetmGuMZ7YxVc\r\nVGw6kowlZU/LEl+oQF8rDY7Pnkh3Xzk6WguZ+U42bjIx4351PgG6x814B1OF\r\n1QCDhCe0yPff3jJtiQcCJE6ScugTJgsYy8dnh0OuUt+W9LNDPoBdX2OERBfk\r\n2+VQIioHrp/CnM7jbBi/wWVhBV1C7qKgeHSF92ufoO40cyzvq7z9eGljLM1U\r\nvyTacyMw4i/eOtvtYIzp2a6o5TZUAwJIf7SdbzjECALSWXRn4ZPiWX+2jNkB\r\nRJsC/VQL1R7lhFVUjmyPfl+YUyGF73WE8EfWLw9cFwcDr4s/DWV09tRSDpUW\r\nmib/Tcq3JW/OTqPhYNqJYg56C1y5eFQ1JFwarfmQAFYBODcbNV04fXqYnFN1\r\njS2jC6ZNn/efq7Lsy3uI2fnB0kDpB9RKsnF0eyOgeuSPvOBcqjFW+Ve+cU0m\r\nJNkFM69hA1/egMkc1RF/v0xg8kxHiM2GF78h4kttZAp3FwYhGDX1n27WuAoX\r\nWyaqvnReNGkllyPGaa7aJp6wp8dxiW42Y1mlkPG0CadYcyf6VCW5ezDGQwJo\r\nF8NYly2YceNft756SuVv/tSSG/JtcvD7S7o=\r\n=r4ik\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_6.0.0_1663756444522_0.05344718371545243"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "ansi-escapes", "version": "6.1.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^3.0.0"}, "devDependencies": {"@types/node": "^18.7.18", "ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "9ebbcf039c1eb663653acaaf557e00f458252354", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@6.1.0", "_nodeVersion": "18.14.2", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-bQyg9bzRntwR/8b89DOEhGwctcwCrbWW/TuqTQnpqpy5Fz3aovcOTj5i8NJV6AHc8OGNdMaqdxAWww8pz2kiKg==", "shasum": "f2912cdaa10785f3f51f4b562a2497b885aadc5e", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.1.0.tgz", "fileCount": 5, "unpackedSize": 16651, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICOvVtgaWmh5fq6OMCMcF/YWGDaOC3GYSKBDTrRpzW4dAiEAqLyw3ERusiOs2rNY993TdoERGptcEx9/PoGvJsrPz5g="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEs9NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2JBAAni9irGk8nUaA460pvzHXSTUPnTmsKxdbNDX+6H2ZxTK/GNj5\r\nG6W2UyBJ3dGrb8L2VPX+fhGlZhwBSUMww0mTQn45LfKCJskJDdAeXwKocX4M\r\nfUvjumaS8lNP/fqdx47/AKYbt97aQ+lcF5tz8giav3l194LESF5i+cxzKqoM\r\n1HGm52KZpfkbPw0YwuVKsSz1ob6ANRFJSWjAqWjRUcAPLJVdWlzZ2cEShgWL\r\nZB49HGt0WkTJRKqwZJHbZc4/3DYkb5CoPDXnQwdzJEZOU3oOi4rMtlByK+is\r\nUPCJuBrjeMsIZXc/xZ1HfzDz87khXOFFyc4htQC5nrsiAeZtRA+b1LZv/0BT\r\nfDE+QuqekzyIVo5ANSS10b0sRR8ZeEaYZrfZ4oIxcKfXSflJAV5/kyZNvNud\r\nBOwfiLk3hqbMiPDTCmzBVVYUQWa69t+X7rOpTC7U4KfP4GBwo9G3Y+1T97TX\r\nBC3gDAC8JCRhcMGVw/XSwel727a7MObT1o0bRGyIp4on5qsQLmrVTpnDpyF8\r\nXdS69yT5U9UVUXkW+QwkPfmjjTK2I9BqMM3zfJAdXNB/RPzJgicg7gB2k92j\r\nzfDAwLBZRrAoYpZUoXQeElbRn5eSoZ9Dk8xiQVeIxPIHvEy2XuIaIwJnimtf\r\nAXePs/r3m1RNk6OTt1VzU9j+oGNgqZOLp+0=\r\n=8liL\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_6.1.0_1678954316955_0.21912162732193563"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "ansi-escapes", "version": "6.2.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^3.0.0"}, "devDependencies": {"@types/node": "^18.7.18", "ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "7f7c97a4b34ff1f0b9b44b768b82755d8df98b50", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@6.2.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-kzRaCqXnpzWs+3z5ABPQiVke+iq0KXkHo8xiWV4RPTi5Yli0l97BEQuhXV1s7+aSU/fu1kUuxgS4MsQ0fRuygw==", "shasum": "8a13ce75286f417f1963487d86ba9f90dccf9947", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.2.0.tgz", "fileCount": 5, "unpackedSize": 17309, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIENadKTxjmZknm0kCgZEA0cEIOtuypCJWanvjxONQN1VAiEAiWnMBzoB+gNBYnVS2CjkPUyP7S2ddcu0e6I4dUZmxEE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRlJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5MQ/+ModRnVXNPa+iO+IZl2mFS+XJCruP7z+Y8oc66ZqNBTgYk2R7\r\nkC1dXtU03FNerfjeBLqQn6dF4rslNMXQ8W6vCVOmNN5GpAgDgIuVP7Y7BIbC\r\nl5Fz+MjpWzKN74/CSTMpnm9lA5TyH8CNqlmUn5hUggQf1k2v6ImOIZL8u3nX\r\nyYbGOpn7J3x1VNZxz0ApeqBrCX9bw23Cu64B7/yLcg0jaIRrMSGJ8yHZDDUX\r\nvcXL2s6/vXU56u/S72ZQmoFZjvCvEUzcasBxaPpeGbnE1SraZuK52Lpr1z8j\r\no0IKavyR5EVcN5gv/R/oEhEMijT4owR7sxOL9VLHgQ+ZBIbl5KELUYWxxR++\r\nFS+gqnbqFVs+edhBPMGF/tAVkLoLWUBIOlYk0yY+8exfcHwrKR91xl9edjty\r\n3rU23hq+9TssIf/75WNiEdor/ZX+RK2K4bHYvsjrGrEnIM7RV1FGZBo2y6o6\r\nJm8nuBhSLj6NoRUIo0YDRaH6kkDBG8vo+p0N6KOgHTr9XusHjNa3/pC+r6Ba\r\nglvJC/cnEP3RqZAITcVfbizDtO3GfsfH7E0LZXU+70YUJUn7eqIGPsjv8YDj\r\ncTh034vi7o2ftsAbSD7yrYvHIYQUjCauietRxX09bxvctiv50trPq3eXlR/Y\r\nyDuthNgV/+m7AbKZ6Y/bRNlhSgWCFYzgPmE=\r\n=271F\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_6.2.0_1682330203301_0.9000876456039291"}, "_hasShrinkwrap": false}, "6.2.1": {"name": "ansi-escapes", "version": "6.2.1", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=14.16"}, "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"@types/node": "^18.7.18", "ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "1939411ab99337ff6e162e21acc607fdcb66c2d1", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_id": "ansi-escapes@6.2.1", "_nodeVersion": "18.19.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-4nJ3yixlEthEJ9Rk4vPcdBRkZvQZlYyu8j4/Mqz5sgIkddmEnH2Yj2ZrnP9S3tQOvSNRUIgVNF/1yPpRAGNRig==", "shasum": "76c54ce9b081dad39acec4b5d53377913825fb0f", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.2.1.tgz", "fileCount": 5, "unpackedSize": 17611, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNl7cwQWBPzbcsDhLh8AE0Eq1kb2dnu79gVnIzrzi7JgIhAP3uzNTSPppRcf9qrRb50by+EofwRPYiJwwqayDUogE1"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_6.2.1_1711262104524_0.1910229875616707"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "ansi-escapes", "version": "7.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2", "clear", "screen", "erase", "scrollback"], "dependencies": {"environment": "^1.0.0"}, "devDependencies": {"@types/node": "20.12.8", "ava": "^6.1.2", "tsd": "0.31.0", "xo": "^0.58.0"}, "_id": "ansi-escapes@7.0.0", "gitHead": "7d4c175412365135dde97f99ff98093fe8dfbb07", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==", "shasum": "00fc19f491bbb18e1d481b97868204f92109bfe7", "tarball": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-7.0.0.tgz", "fileCount": 7, "unpackedSize": 17851, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF8k08PYKkDDks+RekbaYdc0P1kpyO3QfrDTv9H5teWQIgYJewkrcBuYJU3Dc51hm6eL1jXDKaqb9frhpjXccb43Y="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-escapes_7.0.0_1714683256662_0.8927526612784225"}, "_hasShrinkwrap": false}}, "readme": "# ansi-escapes\n\n> [ANSI escape codes](https://www2.ccs.neu.edu/research/gpc/VonaUtils/vona/terminal/vtansi.htm) for manipulating the terminal\n\n## Install\n\n```sh\nnpm install ansi-escapes\n```\n\n## Usage\n\n```js\nimport ansiEscapes from 'ansi-escapes';\n\n// Moves the cursor two rows up and to the left\nprocess.stdout.write(ansiEscapes.cursorUp(2) + ansiEscapes.cursorLeft);\n//=> '\\u001B[2A\\u001B[1000D'\n```\n\nOr use named exports...\n\n```js\nimport {cursorUp, cursorLeft} from 'ansi-escapes';\n\n// etc, as above...\n```\n\n**You can also use it in the browser with Xterm.js:**\n\n```js\nimport ansiEscapes from 'ansi-escapes';\nimport {Terminal} from 'xterm';\nimport 'xterm/css/xterm.css';\n\nconst terminal = new Terminal({…});\n\n// Moves the cursor two rows up and to the left\nterminal.write(ansiEscapes.cursorUp(2) + ansiEscapes.cursorLeft);\n//=> '\\u001B[2A\\u001B[1000D'\n```\n\n## API\n\n### cursorTo(x, y?)\n\nSet the absolute position of the cursor. `x0` `y0` is the top left of the screen.\n\n### cursorMove(x, y?)\n\nSet the position of the cursor relative to its current position.\n\n### cursorUp(count)\n\nMove cursor up a specific amount of rows. Default is `1`.\n\n### cursorDown(count)\n\nMove cursor down a specific amount of rows. Default is `1`.\n\n### cursorForward(count)\n\nMove cursor forward a specific amount of columns. Default is `1`.\n\n### cursorBackward(count)\n\nMove cursor backward a specific amount of columns. Default is `1`.\n\n### cursorLeft\n\nMove cursor to the left side.\n\n### cursorSavePosition\n\nSave cursor position.\n\n### cursorRestorePosition\n\nRestore saved cursor position.\n\n### cursorGetPosition\n\nGet cursor position.\n\n### cursorNextLine\n\nMove cursor to the next line.\n\n### cursorPrevLine\n\nMove cursor to the previous line.\n\n### cursorHide\n\nHide cursor.\n\n### cursorShow\n\nShow cursor.\n\n### eraseLines(count)\n\nErase from the current cursor position up the specified amount of rows.\n\n### eraseEndLine\n\nErase from the current cursor position to the end of the current line.\n\n### eraseStartLine\n\nErase from the current cursor position to the start of the current line.\n\n### eraseLine\n\nErase the entire current line.\n\n### eraseDown\n\nErase the screen from the current line down to the bottom of the screen.\n\n### eraseUp\n\nErase the screen from the current line up to the top of the screen.\n\n### eraseScreen\n\nErase the screen and move the cursor the top left position.\n\n### scrollUp\n\nScroll display up one line.\n\n### scrollDown\n\nScroll display down one line.\n\n### clearScreen\n\nClear the terminal screen. (Viewport)\n\n### clearTerminal\n\nClear the whole terminal, including scrollback buffer. (Not just the visible part of it)\n\n### enterAlternativeScreen\n\nEnter the [alternative screen](https://terminalguide.namepad.de/mode/p47/).\n\n### exitAlternativeScreen\n\nExit the [alternative screen](https://terminalguide.namepad.de/mode/p47/), assuming `enterAlternativeScreen` was called before.\n\n### beep\n\nOutput a beeping sound.\n\n### link(text, url)\n\nCreate a clickable link.\n\n[Supported terminals.](https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda) Use [`supports-hyperlinks`](https://github.com/jamestalmage/supports-hyperlinks) to detect link support.\n\n### image(filePath, options?)\n\nDisplay an image.\n\nSee [term-img](https://github.com/sindresorhus/term-img) for a higher-level module.\n\n#### input\n\nType: `Buffer`\n\nBuffer of an image. Usually read in with `fs.readFile()`.\n\n#### options\n\nType: `object`\n\n##### width\n##### height\n\nType: `string | number`\n\nThe width and height are given as a number followed by a unit, or the word \"auto\".\n\n- `N`: N character cells.\n- `Npx`: N pixels.\n- `N%`: N percent of the session's width or height.\n- `auto`: The image's inherent size will be used to determine an appropriate dimension.\n\n##### preserveAspectRatio\n\nType: `boolean`\\\nDefault: `true`\n\n### iTerm.setCwd(path?)\n\nType: `string`\\\nDefault: `process.cwd()`\n\n[Inform iTerm2](https://www.iterm2.com/documentation-escape-codes.html) of the current directory to help semantic history and enable [Cmd-clicking relative paths](https://coderwall.com/p/b7e82q/quickly-open-files-in-iterm-with-cmd-click).\n\n### iTerm.annotation(message, options?)\n\nCreates an escape code to display an \"annotation\" in iTerm2.\n\nAn annotation looks like this when shown:\n\n<img src=\"https://user-images.githubusercontent.com/924465/64382136-b60ac700-cfe9-11e9-8a35-9682e8dc4b72.png\" width=\"500\">\n\nSee the [iTerm Proprietary Escape Codes documentation](https://iterm2.com/documentation-escape-codes.html) for more information.\n\n#### message\n\nType: `string`\n\nThe message to display within the annotation.\n\nThe `|` character is disallowed and will be stripped.\n\n#### options\n\nType: `object`\n\n##### length\n\nType: `number`\\\nDefault: The remainder of the line\n\nNonzero number of columns to annotate.\n\n##### x\n\nType: `number`\\\nDefault: Cursor position\n\nStarting X coordinate.\n\nMust be used with `y` and `length`.\n\n##### y\n\nType: `number`\\\nDefault: Cursor position\n\nStarting Y coordinate.\n\nMust be used with `x` and `length`.\n\n##### isHidden\n\nType: `boolean`\\\nDefault: `false`\n\nCreate a \"hidden\" annotation.\n\nAnnotations created this way can be shown using the \"Show Annotations\" iTerm command.\n\n## Related\n\n- [ansi-styles](https://github.com/chalk/ansi-styles) - ANSI escape codes for styling strings in the terminal\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-05-02T20:54:16.986Z", "created": "2015-08-14T15:26:46.205Z", "1.0.0": "2015-08-14T15:26:46.205Z", "1.1.0": "2015-08-17T08:54:15.403Z", "1.1.1": "2016-01-10T01:16:26.151Z", "1.2.0": "2016-03-05T09:29:21.486Z", "1.3.0": "2016-03-09T09:33:54.015Z", "1.4.0": "2016-04-17T20:37:19.676Z", "2.0.0": "2017-04-23T15:32:59.037Z", "3.0.0": "2017-09-05T10:36:07.996Z", "3.1.0": "2018-03-25T08:14:47.767Z", "3.2.0": "2019-01-28T03:36:59.139Z", "4.0.0": "2019-03-19T14:09:34.405Z", "4.0.1": "2019-03-20T06:28:01.768Z", "4.1.0": "2019-03-31T12:40:58.906Z", "4.2.0": "2019-06-11T16:32:19.203Z", "4.2.1": "2019-07-31T20:56:12.284Z", "4.3.0": "2019-11-16T16:54:26.152Z", "4.3.1": "2020-03-01T13:54:42.051Z", "4.3.2": "2021-03-24T13:41:41.518Z", "5.0.0": "2021-04-18T05:19:53.662Z", "6.0.0": "2022-09-21T10:34:04.690Z", "6.1.0": "2023-03-16T08:11:57.124Z", "6.2.0": "2023-04-24T09:56:43.517Z", "6.2.1": "2024-03-24T06:35:04.748Z", "7.0.0": "2024-05-02T20:54:16.807Z"}, "homepage": "https://github.com/sindresorhus/ansi-escapes#readme", "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2", "clear", "screen", "erase", "scrollback"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ansi-escapes.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-escapes/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"retorillo": true, "leizongmin": true, "yuch4n": true, "unixzii": true, "xieranmaya": true, "russteee": true, "justjavac": true, "myjustify": true, "dntx": true, "flumpus-dev": true}}