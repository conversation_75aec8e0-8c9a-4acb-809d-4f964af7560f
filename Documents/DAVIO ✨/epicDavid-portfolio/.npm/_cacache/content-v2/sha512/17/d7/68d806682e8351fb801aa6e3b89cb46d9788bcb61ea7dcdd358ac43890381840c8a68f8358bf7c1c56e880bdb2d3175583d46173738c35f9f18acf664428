{"_id": "@babel/plugin-syntax-import-assertions", "_rev": "46-7ad8da2895bc76b571e0b78f686894e0", "name": "@babel/plugin-syntax-import-assertions", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.12.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.12.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "d9a977e496ad7726e67f0731f0e7c26abd7ba48d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.12.0.tgz", "fileCount": 5, "integrity": "sha512-gnbtObjhC9VC5etfSAhQ1imBFU133M56BCTIo7TZQ6DQARgheL4Stsal8lQ9dESAhfS4lumvhIJyL85eJeZe/w==", "signatures": [{"sig": "MEUCIQC87Yu8yT0CD0gCrQuN+JSYzDHNZklbPypKlqYD9c3LSQIgbj73rEyc85naYXGtGswn+rXwWrsO7hXTIp9xN86OmdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lrCRA9TVsSAnZWagAAgckP+gN2gKOpfieZaxGWdPKp\ngZRH+SnLKbfYccGKtjNZt6GFgQRm9BGOwMEen3myb+lnn7XexQip0JMEEe3/\n83CPYoAt6cB9QnSpi29PGYjVQ8DxPHjNe5Cz85Y8XWpsMpY663q9gaFjIPby\nleq56b+UlNbKHh80gA2px2GU4PbLw38pYAd3cR1d4yP+8fm/A2YTbsB6z1fi\nRI3hLI/zYrg8iujc2VElUfN/E+u18/dcPjCgfn3gwz33xx8fJ0E5gWguRy5c\nf3w5BhnZg6jNtlIHGBvV0WYzlsL02Fgm7QoR+8XREhWXv4/uuI0J9KNX5wVN\nuAiwZr38/o1nzVjZDkNwdwXJzfj5TIJ75ERRW4Fd/d8kOap+uZVsBOx7J6Tf\nsb3u2yurIOyvly4AZ6zjyTz20eriMQVT5Ol+MgehT8c2wkZZ2jXLdzpu+mcm\nVAzxWpp2M/0g7tFs05d5qtcN0PnZIW1XNp2tiHnU3MuKh28uaex6bHTHrYA1\nYG6JVZNqpRAmnE0PqbADVfrxHPGhBs/uESskmnrmSg2wk9EfjBNzTk2xRF8q\nzVsIajSRkBOWecweH7LeIWs62icamNqrJfpDpgzKGTBgm6F9GGd1qSHTaOZg\nO+8cNKWXLsufzF5dOaMGcJjnxwOjVxZ4gtZqqf/v2olO9jz1hUJ2jvEF8Zw+\nH7t6\r\n=vZcp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.12.0_1602705770513_0.36833748392393395", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "fd96f8f7bb681fa7dacfe470fa41add68f7f241f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.12.1.tgz", "fileCount": 5, "integrity": "sha512-eyHdg2/DBrQfPgT7URyQacH0BuR2yJoKPkc5yQwRSh6yfA+77A5JRRGxwSOWHpsitWxtimHfPqJ+sZA2DuDJLg==", "signatures": [{"sig": "MEUCIQDztwnozBi49clw1dX0BkcgHLUWYoNTotDHo68LrzaEBwIgXM/pgl/UOP2PbTABeJ5UaQSJFbYuiA+5AwdMnYhr4hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+uCRA9TVsSAnZWagAAzF0QAKA0eDVTGN6jTogtp9Vu\nvKAphpZdG6xcbp51YyJntdVsVBxuxMk7v6gdkPxnHw9MDAoVB1t4xhfmsjMy\nuD2ifrN9nPNJfEottF8QFNAPgEBLBDUML82xpeMcBhcNS+SqbLtLL3PbO5sx\nscdt/vwLLU4WOPuWhK9s/94dXnP+LLyWmk6jdYZgg1c+9GiCF3oGLS9vf/AY\n11i5eSQdH4jM4n4hqSPUXS7BiAyTA46JK4bnrF161GwFFHM7lTxDcthnToLI\ntbGdTA9tKFv9bnjYfIvbWKy4JDbx0IzUHcvfJZaf/BqkL0Aut6TVJ5y/BgQz\nDENlIeUJ7Biytn87GX6QkNvBqFl4zFxIpvI/Ce5uAv4VESbz57/5D9BzlEAZ\nn83zTlnAjweShLDh+zyjAnl9X1cXiRe5vW7iqfs3Tx6kKq++hNe+Y1BMhgmB\nyUDx/mjguUmuqiLpJagq5b/EV8A+uPXexckcBq8kOyqITPry+IWtfleaoynb\nRSxgqwTScIXnDVQsnetUEK/gmOXwBsCxzqy/ypKMCmUSiWxVe0sOs/H/uqKm\nPyFhMFSewXR9HqLd/6gCP7WlKCptvF7QruINovh4ewHhuixfgIfx99Mi01TC\nISWZ9mAF4KL6nubMbE8m8frklQ+K8KTzKA3AQu362Al7FkiTDMaMbPejfUjF\njstV\r\n=j4Cz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.12.1_1602801581828_0.924155265598765", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "86dc6102dddf05781b2c36350b695fcb89d7cce4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.14.5.tgz", "fileCount": 5, "integrity": "sha512-bCaGuphEinZeNtIUohltvmShQbiABsKVpg/tivRLJpAZUkHIJTtzTBjV/kOik1QZhS+G3QXVoajUlpOMOXO7vA==", "signatures": [{"sig": "MEQCIFOOUCQVGpMf8lldfKoS0wUIjSsKSWfbNro2wVJmY+5FAiAhXJhSv2BQqeq9jXo4ZdeRN7ZXkEDUMPZbE8XKwbNOSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq1CRA9TVsSAnZWagAAIOUP/26bcIZtdZxfVM9hpRpb\nA5uYbZ0G+Ky+ggthgba/rhQgdkRwys3HTcQV4XBTuoGmHN64GrWH9AaT8zhD\nc8BeK3Q9yvc4jWlL3KroYBDj8zrFJs+v9CKsimGH0LpCe+zeobpsBaUBCX+F\nuouk81teDQrNcG3L3uw49FiBU1aKynQX2Ke2N/3tWKf5f1IL3tqr9XerZt4i\nsGDR2XNOtg/+8WVmsBuS1OP1SFt57iyOggrNtfVqnqZlHSzP0VKFEP/U1c+M\ndX1quZblODWLLTlIoiLJN2wzff/kBjr6ka4LCacWGDE05cMIL5bz0B63SQ8k\nTzRkfc9+OWDe0LQVMqW0vNZuTpd92c9d+KtI2VPAx9f9JwxbJJIKkifY0Gla\ntNB1AdGqLOIdLH7Nbh5dxLDQv4ZRSksGHLpy6iWmhMQULtdEM4UQb53x7EFF\ndoaFLl6oiA+xPRJ4mFEiEGZ04s6oMbuLAuJVyUbsU26vS51PyfLRei9qGU2P\nWkMmyLsi1nQoorOgH4aw+AZrtGp1rOhbh/9UDrLBzaVjW95k5PdDu+QS3AM3\n3PggZxnPlwkcDCTghOYTe6dgz7g1mimizMzESSktXWjAqJtT+t41dACg/Ttl\nt0SoGFUiDUUz3LA0bh5tgPnvGRiOnTR2OTFAvS4GfbE8sZVU0ADbC2afCZ0N\nS28x\r\n=RjFi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.14.5_1623280309148_0.9651574721128526", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3a207beb3c4599f184bbe89f71ad9f640e4c61f6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.16.0.tgz", "fileCount": 5, "integrity": "sha512-ccAxMXj3lvejgMsNUVQf4Ok5jspQLREGA7l/QWAnAhyNmxI2mgsjAPJMOm2mdsPGuXUM/WGZedI8UnrjlfyB1w==", "signatures": [{"sig": "MEUCIQDUGQDepC2exXeazUoE+SWOdSVHXucuV0t2grsSHKMHLAIgFWkwKCyY9wTeLAnZGNmswhMAORugaPR21bhIRnaWjDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3007}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.16.0_1635551246192_0.010052235002957932", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "abe8015bc9e48e2841b6a4e16f7137e228c024fc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.16.5.tgz", "fileCount": 5, "integrity": "sha512-xv5twB+ViJRju/XXO2p1RJJ2oDytV3faJwRCxP/4og3scU3/NmoKrYZFYtkGKtHPUzl2fcsI/4ZIpYsiQvc/xQ==", "signatures": [{"sig": "MEQCICAAtNJKaiw7A/0iqFVEBhO147RDbtSx/kgl78hOo3W8AiBmlngg65mBDaEDVhw26vU/oi4sAB3oWf59vPzaJB1qEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jyCRA9TVsSAnZWagAA/JMP/A815gPC4KqvCmoVqsFM\nbGUWSueMhw3QMyCW/3jGi3jj54g0SFeXjn0jj/5+3AGctItEyjzYX6+G+Ic0\ns3kO8VTpk4WO/iEq5Kw9WsFWvGp0Q6+0HXe3kpIab+t83zf3kE92fm78FgXg\nGYRh43QtxrrRVWQtNUojhBGgV3Cocs94sUtnyZhHER0MUQ+P3WGBpdMWjgOz\nWHBXUJuN/WlZ/LyWNZuPo/GiY2LOY3EQ4O1BPdSaM2GuNomP4pONpVUeoUjV\nkEDTazv7onVm1LRI59jjgcsMjfLOEMHDOMRKtnDkmzpA7Q2qtZ5OlAWkYIme\nisIG+2k9VTWvv5C3A9N7f8LxMohNdW88klOuIkRF0dWAbVF3nofu+hv5cF5I\nH9O5Uhs2i6AOMS049kvNjhVmTFlnrI3ocTsZEEYKkEwA18535/Tx0mMCDNJw\nv0Wl/NK6Kbhfkz/B17POyKn18UZXLRT77VQRuIjEpe2+b8FMZgutfEKwLVbr\nFMGWTojtTLi8tU7euNhLSdM0+u4hd9D8DG0Bj1tRNftBdW5RKOxSlyai3Y/K\nXZm9R0Pged1qd9ZD+WhU22MnXWlGR/bG3C7dBAHyDIY0O7gHmzh4SPV7I3ZW\n659M/0E1lAkPfGLelJscig/XqT/Qkg79ux3oR1PeJZE5HnA44D7AJpjpeDiq\nmBvK\r\n=SrmR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.16.5_1639434481970_0.7304524555658867", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "78288980e36f1614a4024d45937f4c8ca7185ac3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.16.7.tgz", "fileCount": 5, "integrity": "sha512-DoM/wsaMaDXpM2fa+QkZeqqfYs340WTY+boLRiZ7ckqt3PAFt1CdGmMXVniFCcN8RuStim2Z4Co3bIKdWjTXIQ==", "signatures": [{"sig": "MEYCIQDQ4ypgJjeOEvbE8yFcInjLhiPGUVwiIK6uiBC7g7EF5wIhAOmRomy6oREr61gDKAAMLATASPmer3MULI01ysFhQnAC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3007, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0ZCRA9TVsSAnZWagAAGxEP/06MhniLptNMpPQvY+B3\nthBDXN5feGmnn5YButfdK2d41U/ImL34KIGtIKu3QQUZkA87uQFQJJDrIeVL\n38FAfhe9RqHdLvzZ4G84m0tDDRh1ztI/JD1Kn4X5VGaA415SaMZluAlAG66A\nUVhRuCuhzI460HTROahaiFPkPck4oeIjO8H/EMGhvFiey+0XIPxGDfpyOGfb\nrDi6wX/0+IexTZEggWfDzmzEUTrsZglmLLR/ksuqkJfwYd0JE/ZqnJ3ZhfvM\n5+vBXtbVMIJX7U9HAZi4VcooTcdVxdJ97zbeDkgXAopulE/ln1H1nePXcKcW\nXD2NDKbtSl7K4fx+s8LP6qN0OO8SGDRT9yv1P+aFfpIWbCEp5VgF7kHdnlR8\nhaheqHKi92OXsh/N4HIItREC3fTTOXj1lCcyD3Iz74Ay79Exs4HOuRoVKdQ+\n980qrq9CXzGBmc7NVhuqQ8EJsk2lqXzSKklHYSpGmPJLRRBYmN/WEIlnf/jo\nuxJuEckiPA1d+3UT6XQJLGfoEsXSNmXZ1wc8hdSTGLgu3v1dUQcvibpjKKPw\ni7btOXdtFacwRmBBY5YffH5uITSBTwOeqTtOrtFTsbSFtt6JseOAHeN5ciy8\npe7oJTfKtXMUYc3lcww14fJvMHMcWpLgmxYO9UMj2y4pZhQzMtuZxZJ3LoDI\ny9/d\r\n=57wS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.16.7_1640910105383_0.9503513227954179", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "58096a92b11b2e4e54b24c6a0cc0e5e607abcedd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.17.12.tgz", "fileCount": 5, "integrity": "sha512-n/loy2zkq9ZEM8tEOwON9wTQSTNDTDEz6NujPtJGLU7qObzT1N4c4YZZf8E6ATB2AjNQg/Ib2AIpO03EZaCehw==", "signatures": [{"sig": "MEUCIQDfZDswQCEKbxN3W6s5AzzXYgbAPi/fLEJHI3UEMb2RhgIgf9kUsBfpXQDZqZwhH4il8k2C3LMXO2YJ0kPjt2OVW4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVzA//UIshl+vip58QAApBUyie3lOaLEayAkdYTBCqt2mTV0zHKShS\r\nHxe+YyjH44yD6kpGYpTqiinStBOqlA9Qd/Ho7sAmg6WutmnZy+xe5EmYZX2V\r\nW4/5sTjh70emQD52azQoFGTArhHh+/xKZow/bX44qPi1phD4RXou297sm+x2\r\nQQMl2P8ueDrvRrWt6hsibrdEdfInmGFdUAlbZec2gCEyve3sHumqXckOMYWd\r\n6OgRosenHKZ6C+T5kZX5RQe0OpC2zwdyhyC41wTewtFhM+I+aJtMstgBPsQZ\r\nICnZob+pFJevsl8JwDmd8Cu+Lr680TIGNlMZuqXMCP3rFMi3NH7gnT9bBF4Z\r\nmM8aaU3rJNK2GuvkCauYixYIa2YFVg3voDsGXR4x5ds7lFLGBINZDVaVYRyx\r\n8Q1QWhq9mLVnhHgbwMGrbRTn1iXEylsqXty6fGuSZ1kQM2Co9mU9QBZ0IPVh\r\nEVOHv1aPejp12Js+y+5Cnac2rMaQX2pN49b+LaNtXZMPeACPmYFRrl4pkEbi\r\nNtxFk/XdJM32XEiuxZ1XnQaENavlwwDU5CdjjVsF1h5IRnxSM/OyluQCUBAq\r\n9XVt80XDbyPMjROlO6OZwS/jrRt/L7SvSOBPXaP7VwIbbcRSjKle08PCIiFz\r\niYs/ZpOmPVNXi6wgIPrHpcUAjp8R6aBJUDo=\r\n=3mS8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.17.12_1652729561061_0.3652691855009882", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "cd6190500a4fa2fe31990a963ffab4b63e4505e4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.18.6.tgz", "fileCount": 5, "integrity": "sha512-/DU3RXad9+bZwrgWJQKbr39gYbJpLJHezqEzRzi/BHRlJ9zsQb4CK2CA/5apllXNomwA1qHwzvHl+AdEmC5krQ==", "signatures": [{"sig": "MEYCIQDMZSvrKq115rM62dBbktDGEVjvD7p89uoT9jF4er8BZgIhAMa4RXeh/qBQ7X0zKatIUHVNJ2YxMKm6Lpn3OhTQe3wK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq56A/9Geg7Y37hMrrWYZ2VArZ0t4LyncTcs+6eqaVUnrc8UjpeMRYe\r\nFlZ2PJkWlO2bta/HcFzwT9/UUy6O4wq5bMB/jamXJ6MjGm80ACcTanWrr0go\r\nSNMhT/HzhnTXzfo6tUvKD/12vn77x9FzUas4JfDjscYUPNcwyKeDX1dM2iIq\r\nSywjrH6pXFots3DAyjRmiNgJS27AU5KDRjMmValEohYRSBNo52h6WmOxigTn\r\nI8MHoQ1RD9+WMK18PwvX+d1HPYdqG2YU9+qBRshdAPlgHJxQDfhZE8whk0dj\r\np9ai7gbbUnrEqMLdNd7DwsDdz6aFiguUbFkndoej/rHPkJpxY7O6t4TTw72Q\r\nVOAd0/e1gC1RDyPXyNJHrlv6dfGQXR2zYA3OGfh8FgW/3gHFEr3whsZ0iM9q\r\ntVLBVW0mH1XM7l934Tz2DhoPjnoibg2wUxqOJTfddkwhdTDo/+3rC0/GAinm\r\nhTivX5Q0iZFGp7/35bMvySrTOVi8b25YSKtxa+q5HsNftmUchr1mWDhIJCCr\r\nKTBqg/bolM0SHIatAyNAUG3+09MuV353usQd7addBnVojCqKnLSw8aHowvXM\r\nco4Mg0pekVlcehjdBdT0dVAc5mJPljRYGLT8zp/mi1dL5ZgfgIlXIOwklDYY\r\nI3dh1hpeUrLziazJEJWIJNAsq8kJyeAWNbo=\r\n=NEZ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.18.6_1656359395624_0.49815724871076084", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.20.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bb50e0d4bea0957235390641209394e87bdb9cc4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz", "fileCount": 5, "integrity": "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==", "signatures": [{"sig": "MEUCIAFWst38lOawko0+qfhqJQtgWxebcsT7Ju/T3q/OkVffAiEAomZRTWazG5Z0FAzdpv/04bDMdO+p+My4Ay+JnmJPNRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu8A/+OuxIJobOQxvpzBGwONT24GAV63YpqlYZ+M0nV4nn+8jEv6J9\r\nbZSdEPeM5KvebH4yo+iZxkaMhHHR9yNjtiQvOZpNwl0iUs7b+f1k1LkXFRjK\r\nFA8L3byJSXQWXZPGgYudYx1DZnj90i5qPtDgn91ZUq47OU52TdACZd//klZj\r\nNc0TFF1Kocxn0t6IvXjXvxlFurpD07s+qKkB7y2I2ji7H5YyBHl4MWs41JNI\r\nQfqAWzWXrNXRyf8dlAVFE1hYQa+GGP4EilbLqi+AcI0yIKcDqDIaQEmMoaZv\r\npYzj4OLZkgfSy9A32nWEY+xFFCrz5joTa/iYUm4C9KvHj44K5dijP9289abh\r\n7m4DJCavKpaQ9hj+qzPNc5jrBLsjdP5hYnKlRW0C3cbdxE2Yg/bKZm/tX3WY\r\ngHLNYhz4KgqoLNNB44Z9wy6cH0Ik2W99DeidUB2oDnyK+9OJsMdRdOjOTaXy\r\nnvH2EaTt+gLVePUHMTOB5u+gptz6cA/1U8GxSDnldL7D+YgUpCZ0eKQujyPg\r\n1uPPIaNgzl4zA3l+ttN8h9XLZFk4mvjKuHE6VchHGcuU6V8rm3NrjPMaVbcE\r\ndsWhZxJZ1YjpwSyN0MB7EhbtOdhDsSEjbnn45dLG13fvd9Soqcy4bHLzgRR8\r\nU3FaCcBuUyeh9qHzfY03Ab2ZbXmdBYyAyCk=\r\n=q0Mx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.20.0_1666876750855_0.9892598235095675", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b2ecc3f4661ecff638e22c1490139a1d148dc9e6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-TIR2xf1R7upUeSFP1ky11YWQfTlZ70qtX9Tn7BOAaY/eWPNbjGSY6gdaMr0DdXx+UdZHfwEx44KPcxXLFTZpgA==", "signatures": [{"sig": "MEUCIGHQyntRj5iVWGQFyettaySLAUabZweATNyY/ipfOZ59AiEA4A2aRUEhJv2jGtL5vwzPWF2ebWC06N6Xfu0biYa9pdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIyw//QPaHeRa6z5XX2mLCeCKgIwfHUFaKZM5gJXQU7O2mSysqh/S1\r\nWitT9ujMlht6UFlqrKay9l9Hj+D+82mlD87r8GiFS+bbJb7382ZYKitP0Tlp\r\nSHfYz7RsXbSdVduLCQ21sorBUD2QSFy+MqkdM4yC+iLfgFGRy1du+lM8GLiE\r\nPHk0czulo5pR4YuuLYWVrikG/KAlWEtqRK+gdtx+F5/Ubw9g9TLlQmwP2h0o\r\nQuwsMNGdHVm3ZxcNGj7sknYeRkcIWfmIyFlavS4XE7knJ/iHgydXMt3jFrid\r\njRN0ZY5E+cpw4Em3Ox3ks6TKl+XVLV/cb7BqnEvuWS1PA8Zq3JCRMLl2kvJ2\r\njWYklwMG3e+Ev+ULgyXj3t1AtRjeHyDkohGvAK+J1KoL7qTlk3w/hYECcG8L\r\nw7aF+2KzHTw7NN7+MgxTtl+SPgDZp2a6lEvy4GQDPoZHvNgo1FDYCLbk7pup\r\nVBFadgTpxeaXIZcPFdsY7ulIc948V1j4cjZtXNkwzRN3Su+V7ih/TezbXL19\r\n3v3BmNTUm1vIrNlaOusXGzWOJaNRlpilh/WesFmxx3tX9En+8KDHYHnNcAzm\r\n9915kTQaUysCq+bqFgbQIPr1bKZU6IOPGyfb4UnIg16DX0Gc32yUgqWs5NO/\r\nWowpy2+SBkM2wWqX9QFmRBSXnP04rThOU2k=\r\n=92U4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.21.4-esm_1680617356602_0.9806998335300634", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "aab9f473d6b482e598f9d59976ef8830d1fe4b78", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-XMZdVzxhCBdSCEgzuso1MNBoPGn3wD0g9jKO+HQIaYkuTaRryMN0BietRn7XCkAIqNZtnNa6myC4V6aPQgBYYw==", "signatures": [{"sig": "MEYCIQDuOAmCRKHndiBjo90UszKCluOXVPYmGA0AiLC+bbbdEwIhANFOBTtc80y1dfHyS/B58Cjd1kBU7qXCKmoEebhqZ04z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoGA//eOmA4jUQBuRL0dk+csrnvzU+YIReY48UY/px6YG3oWaHrTVL\r\niSRBKB4LQpKGUZSU5tRnRLp7SL5zu8UpWsUfOw2/XumoDvzZekLQ0S+IH8gJ\r\nRMzoDEwV8h8nHBk8D9zKpEq/kgaZEbanHZ5Qi+0aLVF0wrK915vb2BItBVls\r\nNKMKeWbbYqGduCv8fJrXBK51OmLZ1/WGWZnpCKBpel9zKzC6scufi8fA1rzd\r\nFplbhI8yzogV8e8nPg1XA1z5os94cQntikjHv4jQF54YOm0xDVC32BbpBnN1\r\nAbO4YRCcWJtg+2u0bgKqdCRyc+JHwDKCspBeFGdHTzuLPWn7kmnGOWd1gz/w\r\nzydYf8TFUCGoam+wlwVkhychkLYidJvXLjxMy2rYo/z+jzfJPkPocekU/gAp\r\nFu2Q9Kyxwbfode+k//8rpRrd7LBpH/wSu3f+2xAi29+FyzAvq8loYt9c+vZ4\r\nTo1JcYIdhhdgQA8gCaqEONexr72fmyrfKZhHh/Yzlv+/1EEK+qKOoSMq0cRB\r\nG2fOP/zUx0DSY5ptUnJR94tnmF3Q86dUTmUnIOM7cPG5iFaHSUgVuXLmTnrh\r\nKOCED0/gufADjNYP9MolekFbBdQMCsbdAkWiilpyT18tJY9skUZcqkbBS1ax\r\ntkkhRsQfhZbj+oEaSk1FgIipRhO91utb7sY=\r\n=H5nd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.21.4-esm.1_1680618066812_0.5298194523709661", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "cd25f94ce7d645c8dd7385d55876f5ae7458d3af", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-FXnvaI6qtIeXViiHPPLEvZ+m/CSez/I7H1hEAu8QaJZj/kl4TV6UW4XD1kdLN+2dpQRgqwhnW5i+vP+SwnYzfA==", "signatures": [{"sig": "MEYCIQC7JzDNUC9dwJsk7JHC/GOojkIXWFui7YXDV9pPbjpVigIhAI+LiN7yNVDDKJvsC9uRUR8z1rYm0sLOwuNXBjF3B8sQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaJw/+POWV9zyN5LT55i1d1CoDU9KZl71HhH0xjYST0e3y2DuaQlbk\r\nTBS6HQPLEOMol1Ofd0m0X2O6NtdNoapZsK6lxVyk1f9V2HMy7FK3RLpeHbVp\r\nJUkFL7FuYXI1pQD8TWAWqu45nZ1RTUxG1EJ4eDJdlMfiw3t5yHgG+Rk5fhpC\r\nqNaxHB4UwgMd2LYPNi3Pw1A6pcOijuWko41nkqeb0tXbnWrUnYOlRs8EoTVS\r\nmd6ClUqmma3E5BW2gimn+XB6xqNGjHuSaUBJ75A6IdbPuVdKD3bNYFPeoVnw\r\nv02Rdhf/2uxK1pYg64rKpBKEErrC/a0PntO80JX1EZHBjcE2fPRYTR5BKgiW\r\n8cyYNS77cWcZm11RXcmZZOhDlnGa6RL+SaEhsVfECB+RcxQ5I7Qk+7Rbl5S8\r\n/l0AeU9NXvBLQQw51O8hpl4bxXjbgFwtWon8Nam/opiX74rF5txoIvCLDcMB\r\nlf2aCICXm1ycLIAYBD2KufIcMci6stqLGbYF/91gjjKKa0LetBKsyA18XQCx\r\nKktlcecKYFo6m1Ev15waTfqXUTyECxO6TAe0wN7j1joC0vF8sILDWk/7liUH\r\nA8KYuzcqNSall4XfsEgwNQkgAigYG0IS5JiAvjhaDcgmzlku/y54xtJfnzZJ\r\nkf+m9AH5UjAEAanCUsoY6Fj7ErphIl6KTyA=\r\n=zvNF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.21.4-esm.2_1680619145914_0.6825917871115637", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "51db9a58536d857329f517f88e374f3c46a32656", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-rSjstcjcmYDjJ8mbN1ptLiMRIBQayBPfTl60iSTCsbqnkzaihSzIuo/1Ek5oUrHZQeki3TJRR9pgZ67B/Fx2uA==", "signatures": [{"sig": "MEUCIQCLsg0RavkL02IOdgjQqUNY+63gWSbFmYDKL4GiWbZHlAIgHLrIUnlycO6RrjQdCN2yRqxpDBA3GrNKtysML+UcziI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroNg//Y+Xl4NBYPyRKjelT6Z4S4bJrb4IInLQGSLrbNAgj5kIz5+T8\r\nMKWChWbur/q07wFhVDQdxqwmxcj38wyuG620E7oJZZtM5zJw9ZtpSSP2ku+7\r\nFkIMTQlluj15nLSpnv1mR/q7uTFoIqq4cceVeed8OND4LuUrLR+aWciBtO4m\r\neE5FLi06L56sWDm3/6OA5TU5zuAlB/TN3BdZJRytSfx8ukb4laDp7nZnUPqc\r\nJyAwe0odfxLupzUpEBnMJ4FYdSqSxd5n/X9glFbz7URjDN713lvumJ5oz9HY\r\n4UtXcj02HW4a8aLubNLiGMUFOFeantI6ovSahi1I2NO2bX2Y/Y+p+k1qplik\r\nUQAimOacEscT/eYfzF3Kj0Eh0zsR+BvFOJzvg49js7Ix5rkt57zrFzgddd7W\r\ndBCtnWxm0SYoTkbkDz8gHLSarT7WyGRTEvWIYABUHJPW6QnDLWrZr3fVh31F\r\nV7QOC11uSOMC23kTObijQ2QoaTWzFIYfTCWb0lzbFZbIe/e6AJ5sdVUid0oZ\r\nTvxkD7mkUFj1MAtJl3zTHWu7vZhYNx8AdLS+uvDWnCKe4wpALH8l3blt/ogj\r\nPaLsI/x+SmVysKEOWB5zjMIJnf7L1TNS1VDDz520fD1qmSvLqabWSz1p5tdS\r\nmBfjvA7q3slizky1VxFhNvRdKIiRyHBu/7s=\r\n=r0iw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.21.4-esm.3_1680620161342_0.9585132015076196", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fc1c56a2af0ac4e3d3fcc3ba03ce1a8bbc3862cd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-Txg/yj0WoI4h8pn+SDTodOwzmChpvyPH0gIqwBkBvkCV5T/w9/S5l1QB6rt3cwVrfy02VfRQiLsg/YokAQT7LA==", "signatures": [{"sig": "MEYCIQClGs0pPYqTBL+hRyTwcLTicWVLmi4V46oUfgBksxXjkwIhAN+caY4Ww83/QIWPLcQnD9sPoiUow8JZTYRnFCXBdhIy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDIhAAmdBk6XRFTCUVwcoj4uD9M17uSRjOzBnUq4gNAamQxN6TIAz4\r\n1SpFSoJRoYEYvLs7i1iUR1XuLiWxkxInUfGXzSU7uz92D6TFQOE9p3tdlmLY\r\npYxCg7IQOCtBBptyRtC2xQUYs+ib1oFvR2Hj7Edbd9WH5y6IjaopgwKMR9Wh\r\n/uOamaU2WPxvYyVhe9R52WUUwF4sL4/cywckANov7/fE8zw/F9Rge+uN1mU1\r\ntmVY0Z9NyjZlRs7dhz16jJkA9onPttPzqF7dJ70raT/xVBoOI8s4dw2LUJqJ\r\nAqIcDXNIsf/225rFoLV3RBQA6ImT6BYhn6ca8o7C+AmCMf6njfNKPG09HQsG\r\ntuZGM0cVChI2sACAwOSF5I/FPnXpf3yhNGsJoldvziCQZIzjfTH7bmK9ioGI\r\ngDRp8vD4zGxUQFBOuhsrIWV4aJ/NgZb3cVuJBmtliZBMm9739h1ADxC1XiJr\r\nQvfs0KNlLPf7pgNMJa6/QKL7HX6kxCt8HuQ0xiEPROiC+hAvFxBVHT6HEjkr\r\ngiTfqWQIBX0v+Lq9zrp9d7NGpah7nMFrKW4JYPtd2C3/UnP/WuGDEyOzTC08\r\nhA8G/ZcpsDX9mc0ZrpUxC1GMz4gc4yGRdVJ5hUXkWgpDW8DdNtGRHKQWFDUH\r\nou4tvf58oWePd+UrnTsWorRhmZDUxO/+S0Q=\r\n=x63K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.21.4-esm.4_1680621194099_0.9504313459838132", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "07d252e2aa0bc6125567f742cd58619cb14dce98", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-rdV97N7KqsRzeNGoWUOK6yUsWarLjE5Su/Snk9IYPU9CwkWHs4t+rTGOvffTR8XGkJMTAdLfO0xVnXm8wugIJg==", "signatures": [{"sig": "MEQCIGpD0fB/dKtfDdjxJEN3j96l6cWqUl2aG8IdOoQzcNWUAiA0HT7F8IyYxE9MyNjBkvvU0sQZ7SMu7pRU9FoxCTtPJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3635}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.22.5_1686248470514_0.9628142804506248", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d4357176ff394d52f56d6cad20c20d70b90c6921", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-b/yGNQmZNKPwsc25vFHB0PZEsVl8mB70KQfLIwf6CBAq2kLHWr8wqxKkAHIC9DnlcBMpNYTevjhWTTYmoX+1Zg==", "signatures": [{"sig": "MEQCICe/DIxwMrmQ9bTqbtll+v9+agKejsQtBM+OBBJZpBHOAiAp8TpejqRB3aC8xlWVAk0hr02AKlUnhtrVH/NNGXrvOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.0_1689861584430_0.09023242342096616", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e9b933f5917078c3a0c773a4237e9d96b9cebfcf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-MpU//fbma1ibERKxuxlrHaB8S0FSEPkp1bAFET0IkbmUctInAM2hwAgYJxCz/MCOCvK73digEIZdGOKlH/dZIg==", "signatures": [{"sig": "MEUCIBpdY8BJy84wteKbK55uVktRiFRLqdF+KlGVwv7IHWZWAiEAu0bm8Uc5ksiOgdPuTXVnTZKEMxopmDk38GvLLRrlEik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.1_1690221079867_0.7849253192194854", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "1e39b29d207e56fbf867d6db56172b9e6435766b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-QgZkedCz2M52L86TmXthP+1XqyBfd9ZYbNuIZgh+ZosbEgTs6JxR4TzebjkDpUvdDUcXCGDV4LGvbFBCWqsiwg==", "signatures": [{"sig": "MEUCIQCDxvGMBtKXV4RHvHguIWuC22xO3tL2sHe4jCp2/LWmLQIgXfWSw9jn3uu6rd8lpIiPo04hnGLOezeNHaxfQXiiltg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.2_1691594083908_0.8153023497223568", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ab6d8041cd461816ad5cb4fe2a5cfee67b70dd12", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-76nDRXxHjWw6ZbA9yQ+i46YC5qGSmO9uZAlhvM0ygHUlq2Rnr6EKZDpmVHDwM+l6cEPxSu5V/svxa+ZdW1IZ6g==", "signatures": [{"sig": "MEQCIFHxFT7qWQQcBM4dB9/WH9Fv3CH5rg5wZOGcx8c5JaKSAiBaRq8HX9I2uLINrEvil56+nFgxUloMdudVckRIbTYUOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.3_1695740197346_0.04834370656322595", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "19d004c4ebcd99f3baf4c84567abf0327212efdd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-tquwaXG1A6CLSYZDr9FKAgALqUg91o29nEq5dHOFh/ijRDyEzVZjf7YPQoySeDnLC9DHZ4UXwOtzy30Ra2unGQ==", "signatures": [{"sig": "MEYCIQD6xiF3UCulx5BwEG36wzvQZiSqHxfvWMKFAxE0NlCo5QIhAO85ejWiCr9fq27vFIKZnaUUo9QNQTGAlRlN/ijScBVi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3542}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.4_1697076364354_0.43049623080576227", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9c05a7f592982aff1a2768260ad84bcd3f0c77fc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-lPgDSU+SJLK3xmFDTV2ZRQAiM7UuUjGidwBywFavObCiZc1BeAAcMtHJKUya92hPHO+at63JJPLygilZard8jw==", "signatures": [{"sig": "MEUCIQDJP897CUYKx4JtSSCcYvBvRy2MhcbWxwH6z3f65999wQIgcF3dTNCaMBh4Q+y2/b7NIHismDyjIzyLTRC8bPjpXe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3715}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.23.3_1699513421717_0.691226943205622", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "15b9a1d6f153ec4422c00562e9c4c53250791c8b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-jdXRy6oNgC5fAAOoZJSvSvhEkJ6+PJCnzqX6bypN2tgvCSrQV/XWvAzsrDReT8JBYaGCPC1Xkd0l82w2MIJUKw==", "signatures": [{"sig": "MEUCIGzMF8kbgyRlP5706FEFYKDN9MtxI80Nz32xEy+NJ6/tAiEAjHCntAOLw/L+lr2U4Nr7QP/AlAJrGXTXoQ8CT7MjK3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3655}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.5_1702307907537_0.001609877858270048", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "044ec24c5c9172c988f6c71d7190fe28fd1a10de", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-0ph9GB53zvDNRC3LX9ERJC835ZTxulP+oICTzHwS0MkaSIu4VUZqFw5xsBXPDUsROrD2isoCNDVPFw6e0rki+A==", "signatures": [{"sig": "MEYCIQDgtwbGdfJBgfh29vZ+rkbySxeSUyaoGMTf8+p5YckZQAIhANbiRu8uo1OBvN4fRPQD6Y+DAvokQfu2ZPIY0RfHJcq8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3655}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.6_1706285630396_0.053615884381811085", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f6e31f03091e2e35a056a64ac09baa045ef8887e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-lWhVI1SXozKc3IA573tZeEXoUocEhSph3R26WHvfUq1NDi8tvbuqvD8nepK6/9r7ze9PF4jfYTibXhSss0ukAQ==", "signatures": [{"sig": "MEYCIQD9r8EwxtPIqrZn8mA5JefMKjHOIdDyudAMicaLeUMWPwIhAPHA7UjBY3oUYUMQ1HQ36PG1EsGZ/Xbdqbi9dbqFeCsm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3655}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.7_1709129075774_0.7516782834760669", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "db3aad724153a00eaac115a3fb898de544e34971", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-IuwnI5XnuF189t91XbxmXeCDz3qs6iDRO7GJ++wcfgeXNs/8FmIlKcpDSXNVyuLQxlwvskmI3Ct73wUODkJBlQ==", "signatures": [{"sig": "MEYCIQC9W39+fIAATmtiueTBHJ1TBqK0s839ONSaNCeo55KcYQIhALg+ptbR1ewfuaKmZWzfo1XQRSAWpItoCC6DDtMq7ApX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3646}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.24.1_1710841699534_0.44132302725189776", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ed8f9f0d7fd760e05cb0f842bcfc4eff85874473", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-i9uk49agZ8C0D0o3TGZVqkd+4eE+Ap/wvoYZNBUeCn8vsNZY4G/XirwOrnjiobFTzLikbt9dKCKhKJHpwbK5GA==", "signatures": [{"sig": "MEUCIHr0LNmaACrEoNdTUIeV1xxbLiHTDofMjC9khJ5f4zi8AiEA2XIAg6ZL8JCecD+KNpg6lHzFq0q4tiUGgFWlUiAgZgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3569}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.8_1712236779057_0.0802417235933437", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "52521c1c1698fc2dd9cf88f7a4dd86d4d041b9e1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-BE6o2BogJKJImTmGpkmOic4V0hlRRxVtzqxiSPa8TIFxyhi4EFjHm08nq1M4STK4RytuLMgnSz0/wfflvGFNOg==", "signatures": [{"sig": "MEQCIB5e+f4m4NxUQrcO9VbtFV0kiWk++Y8OeORRLVaJ5+45AiBmaH+FCMdFkg5II/MYGjG3M9EAgNsC+7SOQtA9VySVXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69567}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.24.6_1716553460543_0.47722150095279936", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "26357d624da23ea222f3547d8897c7ef2b05382c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-0BL35pI/I5/0UJtxHz6n98Y8jdWPvHCurwao5wJ8LbTHmqNDfBqqadYCyU9TEMrjeCOQHIdP2pRBmrE9LyR6IA==", "signatures": [{"sig": "MEYCIQDmOz61n/SUPIYpui/5lI/+Bnhl4mst09oUN6oPnGze6gIhAK4ZE70BICkx//rJ73pKRCwYfwShWKHOxGAHljnwUPl2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69800}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.9_1717423438244_0.39793224361333723", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5792feae7a9456f04c8048b55d219b4f8a9bc048", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-gjmZzJnnbpoQI8sWVN3pB5vrygRkcOPKEg8rSxihMnrThg7EHuK1+ao7OYF7yXjRD9dJE+xz8k5w5zCyIhCM1Q==", "signatures": [{"sig": "MEQCIDgeJAQk3A4rVaxW2M1kAfteQGoP60oDaKAzakXbalrFAiAcxM2+QBKmSGnjKdOWqae4L1PBN4QFn10Q5npShA0moQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69806}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.10_1717499991061_0.883149927943125", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2a0b406b5871a20a841240586b1300ce2088a778", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-Ec3NRUMoi8gskrkBe3fNmEQfxDvY8bgfQpz6jlk/41kX9eUjvpyqWU7PBP/pLAvMaSQjbMNKJmvX57jP+M6bPg==", "signatures": [{"sig": "MEUCIQDWFZOsiWBr00ASEEXQK/Eea7GuZmeIOQ1ev06fHL10TwIgfnLeRHqGDtKFcY9OHnQJEMelOBobh2Qei9ca7lbl7HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69563}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.24.7_1717593309889_0.7194020381941317", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "07690ff4f1aa34022ff46656961187f1e23b7441", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-Y5cGu5p9JBGlBAXWFP0CJ2DObE4rPjmniyzEzJc6RWRGeFVsdAaqvfsW8u4OHlK7Wqw1KUvCxk2ZtS9q6dD/FQ==", "signatures": [{"sig": "MEUCIQDg1na5CBiYZV8Og53tnGatw1dluX4J8/2JzyWMoWhqSAIgbKtw9AwamAzXpKzceYk68odPy242mTIvV99IR3GS+/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69695}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.11_1717751720078_0.28893339058360135", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2696defc6f8ab7b3256295169ab00b8fc959f5cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-1gEw/Sbm42fY1jto0+vSQgildGd/lmrelhgfDbJ3/3lSXpAwlEyftdw83Yi6rEaqDYYh5kkKATgEeOXoYu6EuQ==", "signatures": [{"sig": "MEUCIDOkAi6lN/X0gHJHPaxoUDk0T+aaBm1AS75K+wIX8e3PAiEAzR2t5VGySXihud16WeSoMQg7a19rIpi/DMjZ++xFuIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66491}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.12_1722015196057_0.28509931362130736", "host": "s3://npm-registry-packages"}}, "7.25.6": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.25.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.25.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bb918905c58711b86f9710d74a3744b6c56573b5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.25.6.tgz", "fileCount": 7, "integrity": "sha512-aABl0jHw9bZ2karQ/uUD6XP4u0SG22SJrOHFoL6XB1R7dTovOP4TzTlsxOYC5yQ1pdscVK2JTUnF6QL3ARoAiQ==", "signatures": [{"sig": "MEYCIQDqYnK7qLfPo9lWvaBzeg8W49MyaeApaT3spq6X148OpwIhAMEUvFyQqbgYbth3qA/YIlnw6VJvTIowDVLu/nCfnwNm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67795}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.25.6_1724926461167_0.6272388951840904", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8ce248f9f4ed4b7ed4cb2e0eb4ed9efd9f52921f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-ZvZQRmME0zfJnDQnVBKYzHxXT7lYBB3Revz1GuS7oLXWMgqUPX4G+DDbT30ICClht9WKV34QVrZhSw6WdklwZQ==", "signatures": [{"sig": "MEUCIQDwpGAH8tEK+IEzlnVytAPmTabKNxCmHFAE6Q3ljW6THwIgVKgX7UoYZ7tEZzOwikfSp4nWPjoxBauk7B7Km1maAg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75598}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.25.7_1727882074572_0.051999847036791946", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "631686872fac3d4d1f1ae9a406a8fd1c482c7b2a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-4GHX5uzr5QMOOuzV0an9MFju4hKlm0OyePl/lHhcsTVae5t/IKVHnb8W67Vr6FuLlk5lPqLB7n7O+K5R46emYg==", "signatures": [{"sig": "MEQCIExKnt+srmLyAwtxQ3p+1HoELQUjRHfcX9y6HsaLASXLAiAap83iThjabDN0p9zqw+FbLDBahrboA6HRD3MvKhYWfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5143}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.25.9_1729610452293_0.28713800857997285", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.26.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "620412405058efa56e4a564903b79355020f445f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-QCWT5Hh830hK5EQa7XzuqIkQU9tT/whqbDz7kuaZMHFl1inRRg7JnuAEOQ0Ur0QUl0NufCk1msK2BeY79Aj/eg==", "signatures": [{"sig": "MEYCIQCHgDpD/PyxcRs4AQ6KgtejkyMp7PfxDJTDFnuzI6s5jQIhAMj2RjNZlF8rKOImHJfpW/lgumZOqq7cV7EpX8fGr1l2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5735}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.26.0_1729863008003_0.24213403717405946", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7fc210cb36807c63bc3380c532be68b4b8ece9c5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-zygq2WU2L/hn8eX9mzc+pOPT8WTDUEn7wBsgyjlccYs7OcRMYmwJRhOCbgF5jv5fv2hrfxRclmz+Fy05a8FOKA==", "signatures": [{"sig": "MEQCIHCQ0U9+MIrXmWKNJg2A1Kl42Blfaz9Q/ywwuDcf/BLHAiAEnqKD3+aYJ3DyzrkqJb1EEYSppekwHCij5pgpO0mOSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6093}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.13_1729864436231_0.5034862016463775", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "362a073a7970ae6a5e8989893808582bc2c20f91", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-axwtlPesaKzhJhbdpktBTEXhYoBMI95eCVqmW5Use/a/ULpheVCskBmlIIHIHF1PgPBEaMjltONXhxhZ2VY99A==", "signatures": [{"sig": "MEUCIAfAtWI++MOzPJ+hcUl0h8x3u1sAcaAkPr1X4vbvW54zAiEA9H5ZEvaw4jWoPxDciWo6X+p9+xFBvThevCHR0uf84t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6093}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.14_1733504028430_0.40939554229138553", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f707afce1f66e4a739c9375b07fb9beefcaa0aa3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-P2th7tCFrGrkf75aNq7ZFg2GKcIfkhzlL32iV2Rt+KfsAf6plcgqCqlJO/kDqlbdF4zk/9ge5vPL+g8PfEssIg==", "signatures": [{"sig": "MEUCIAkEujUQqgc0RVv+Q5w85iFYzyZ+Y34Bn51XG+DBuUDJAiEAhVRrxOkWCYZsb8GYFMZqY0+Ud94qPGNOLjbzveuuOvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6093}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.15_1736529852323_0.23407117971869407", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7de5aa3ccbb05e73c42ae5a482dc5e3b3e952db4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-15H443aOYLBUDvn0XV5aT1ujpnYZ50dK64sPC8ltoM+z/k0/gCbT/Rpf8mjE2TWxRcYZ1qQWPMtP9RgTACVXJw==", "signatures": [{"sig": "MEUCIQCFzpmYuQEtoHLq7aCHWp7ssF46SWYpdEnGeH4qUi0kPQIgUicrpYXGOElDtRVUPjQmjiOEU5LcGqKU8yxstixhwug=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6093}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.16_1739534329806_0.3379570448996774", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f0cebf3af4920418ccb22c1f1b94d42d8bcb5dd2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-MA6+jtNflAlC/W9b/SWmTbCTBrBk1ZJ7MGQhAoLmj5mgpW6gB3W/xyynFFzp9phQbbYRnhVezU7IvAUIF7zeKw==", "signatures": [{"sig": "MEUCIQDKlNWbyPFfcrE0D2a5pGcE+ZZCPesjFzZ1DXVEjS7h3wIgHyI34aDzidOeibqABddfs1fwBO3h5WYlc3vQIAlj1e4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6093}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-alpha.17_1741717480712_0.21272738867773033", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "88894aefd2b03b5ee6ad1562a7c8e1587496aecd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==", "signatures": [{"sig": "MEQCIAgVowaaTpyjdajN8iBBoW0gFBERz80NUy3qQgeyT/8cAiBKyh2DYihr/Xq1ZquTO9FT88bpKdohtiVN+9eWcwgjOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5735}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_7.27.1_1746025719782_0.4829908319366054", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "eed0dccbbdcc5a1219f27202d37337921572df06", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-mVWhdiHKgjKlkiJs4tpVRAWy2u+a4QgfL+e3jkycBEzbEavH8afcW/kcUDQVq912QiRBijBvHLySJUtj8IH03w==", "signatures": [{"sig": "MEUCIQDN1t5GQvdjsp+98eikxyl98GrvlXif6OTRV+l/ixEVhwIgM0Xc+g7uqGWoNPjnh9BMtq9/Fziqo40jf11IgL2RQqk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6071}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-import-assertions_8.0.0-beta.0_1748620251956_0.6028303817999512", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-syntax-import-assertions", "version": "8.0.0-beta.1", "description": "Allow parsing of the module assertion attributes in the import statement", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-import-assertions@8.0.0-beta.1", "dist": {"shasum": "71b81761ced02a5688277cac2a17dfc8688a807a", "integrity": "sha512-48VT3JCTlBWCnI4kZZ7tGNlrRNDAt4JII2l7pZVKvPMuoAxcwWpJ/UYiWZBx03MSNLtjzcsWFFxh8zfD5rFU3A==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 6071, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIADma3IhQ0mBrm9ph5ANQaDUTKKehyD+XcHjqkCUkLBkAiEAl6Zsi2TXPYoLaBTT73GAQefvnI8vz3Kf5+JU3RJj6eo="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-import-assertions_8.0.0-beta.1_1751447046005_0.8523586797607727"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-14T20:02:50.291Z", "modified": "2025-07-02T09:04:06.457Z", "7.12.0": "2020-10-14T20:02:50.749Z", "7.12.1": "2020-10-15T22:39:41.961Z", "7.14.5": "2021-06-09T23:11:49.316Z", "7.16.0": "2021-10-29T23:47:26.318Z", "7.16.5": "2021-12-13T22:28:02.165Z", "7.16.7": "2021-12-31T00:21:45.544Z", "7.17.12": "2022-05-16T19:32:41.233Z", "7.18.6": "2022-06-27T19:49:55.870Z", "7.20.0": "2022-10-27T13:19:11.014Z", "7.21.4-esm": "2023-04-04T14:09:16.735Z", "7.21.4-esm.1": "2023-04-04T14:21:06.968Z", "7.21.4-esm.2": "2023-04-04T14:39:06.127Z", "7.21.4-esm.3": "2023-04-04T14:56:01.490Z", "7.21.4-esm.4": "2023-04-04T15:13:14.241Z", "7.22.5": "2023-06-08T18:21:10.738Z", "8.0.0-alpha.0": "2023-07-20T13:59:44.578Z", "8.0.0-alpha.1": "2023-07-24T17:51:20.069Z", "8.0.0-alpha.2": "2023-08-09T15:14:44.105Z", "8.0.0-alpha.3": "2023-09-26T14:56:37.581Z", "8.0.0-alpha.4": "2023-10-12T02:06:04.539Z", "7.23.3": "2023-11-09T07:03:41.920Z", "8.0.0-alpha.5": "2023-12-11T15:18:27.747Z", "8.0.0-alpha.6": "2024-01-26T16:13:50.543Z", "8.0.0-alpha.7": "2024-02-28T14:04:35.942Z", "7.24.1": "2024-03-19T09:48:19.689Z", "8.0.0-alpha.8": "2024-04-04T13:19:39.182Z", "7.24.6": "2024-05-24T12:24:20.749Z", "8.0.0-alpha.9": "2024-06-03T14:03:58.397Z", "8.0.0-alpha.10": "2024-06-04T11:19:51.234Z", "7.24.7": "2024-06-05T13:15:10.072Z", "8.0.0-alpha.11": "2024-06-07T09:15:20.284Z", "8.0.0-alpha.12": "2024-07-26T17:33:16.203Z", "7.25.6": "2024-08-29T10:14:21.308Z", "7.25.7": "2024-10-02T15:14:34.780Z", "7.25.9": "2024-10-22T15:20:52.492Z", "7.26.0": "2024-10-25T13:30:08.199Z", "8.0.0-alpha.13": "2024-10-25T13:53:56.478Z", "8.0.0-alpha.14": "2024-12-06T16:53:48.617Z", "8.0.0-alpha.15": "2025-01-10T17:24:12.505Z", "8.0.0-alpha.16": "2025-02-14T11:58:49.990Z", "8.0.0-alpha.17": "2025-03-11T18:24:40.852Z", "7.27.1": "2025-04-30T15:08:39.981Z", "8.0.0-beta.0": "2025-05-30T15:50:52.151Z", "8.0.0-beta.1": "2025-07-02T09:04:06.207Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "description": "Allow parsing of the module assertion attributes in the import statement", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}