{"_id": "@babel/plugin-transform-named-capturing-groups-regex", "_rev": "71-4c2a2c42ff17ab4767486b2f35fec28a", "name": "@babel/plugin-transform-named-capturing-groups-regex", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.3.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.3.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.3.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "140b52985b2d6ef0cb092ef3b29502b990f9cd50", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.3.0.tgz", "fileCount": 5, "integrity": "sha512-NxIoNVhk9ZxS+9lSoAQ/LM0V2UEvARLttEHUrRDGKFaAxOYQcrkN/nLRE+BbbicCAvZPl7wMP0X60HsHE5DtQw==", "signatures": [{"sig": "MEUCIAi1J3yKa9aRZFPStEXQPfhDxAgLJenGDJ5NqwqjSIWvAiEA57jtKfrUE9euvgxRpJAuqSTF2uXv8EnwJOxKNyLKrP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjpoCRA9TVsSAnZWagAA/a4P/2L7FMZJVRQC1vfLFxb0\nmJrDxuH2hsK1TEwvWGoNznj1PXx8j/PB895eOJSinu1jHOqaaf3CXHlgZLDy\nP0Aohoh3zWWqYc+wtRZNmPOtBtFgKCTJrvCC36EeEfudxKJIS1KgPWkMrInJ\nre06nIJ0OUuqHGl7y3ZBNgqb98pC7ryFbLoIbJKsVtCz7BFYitbew163UsWU\nyCO2vnDD26Xu4q7UjoQ6QLv9I5viiH/5KnXNdOaHwdO+3LTH4lqFf5851sUV\nAB8ydP23KmCnroxY6qCNw9k2qz4w+wleoDwoKXxpF6YHV+7uvmPVUf7Cay6Y\nrBX+vleNw782nLofDS6IFPCIAgfio6nvJxEIWBcfCD+imTpcPlqlNGOd7EI+\nCpN015YnMrVNojV8EbE89iP57Xgox78cGqdUBT9lpz4qWNBQt43WQoBmhj9f\ndWCAxgLFmttihJKdgfFgGLq2XBmzXZurCYN1vv5Zaek2FFkLCgabhj5lpKYK\n/dIet5FzRCwwCiyGayKwQRTLJNcZhjRHzvNijEU0CkYDsyvhcL3HdQjabaKZ\nbnoBEAMFFfw4OWHbfFYaJnMjItvKD/H4aZOvJlkQ+6rVOlp6EC63gzATWYI+\ncYnvq90DJL9g0YghIrgEU/PnVD6DDc8HHzVSzBhuC7CYa1eR5MA6i9QyMLw3\nRAMT\r\n=NQ/m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f6ee26c3da4c903818fd61fd9e5e8e1970185f77", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-named-capturing-groups-regex", "type": "git"}, "_npmVersion": "6.4.1", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"regexp-tree": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.3.0_1548106343582_0.18280374658578524", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.4.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.4.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "800391136d6cbcc80728dbdba3c1c6e46f86c12e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.4.2.tgz", "fileCount": 4, "integrity": "sha512-NsAuliSwkL3WO2dzWTOL1oZJHm0TM8ZY8ZSxk2ANyKkt5SQlToGA4pzctmq1BEjoacurdwZ3xp2dCQWJkME0gQ==", "signatures": [{"sig": "MEUCIQDYLh09BSFZrC6ZD/0qJgAqqlTI8ETxYaML0DqOc4yE6QIgV2+XXPVHpdAvlYtxfjtozW3ZHv3LfPMiGQfwaGWCBJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJck2S0CRA9TVsSAnZWagAA/8EP+QB439XuLiumZKuCKYXl\nCH/BbS+Hh1W0ZU1/QePtA6qSiKJDdGghzsqpUCoc0a/t2BnwVwvmDJKeFEng\n5rbaPuEFK6pYADlUJpad4/YuD16pNVKIiNSnvmZl13Pq7Yl5cpCgyEQLEYoe\n0ZBDSfWifphGtTCU8qiTW+pHY09YS8DV8OKWYZHJzl+3zmYHkvbmk0UnxCLo\nPRacwHeNWt3AWjYf4yqnxHortUBKIqL0hjq9dcF/9pnfoq44ipx4zQdWE8Wj\niGHdbDOlKIYwyn90fExxDykGal6jD25m/c4kIsDu9Ix61DO9dbz5Wg8s6du5\nZ2USRJ7aqZHdLWmdJPbzOFpu87T483uyD8p6u0nGJUQcXpXFnJPFYRaUigSV\nDtHghEYdBAiqp7+VcwO/mbzWAU1rjJBVlTB584q4+06pLSVshXq0TrlfbUB2\nGmwaRjd/bfDtORk6KU7rGntxH4Mno+SvKA/HCRVHvYIjSaEWBXvywsWPodZZ\ny0s6wlRJEWsY3D/wcaeDDIUKCrIxi4bg9Ij2zo0X4AhPvmEJapwaTd/UVt09\nk3S+mOQnmlhUEQRzvrNUAKASbIzUF6B6vFMtlf6TSK6JHOiHP2GuF0Kytbz+\naz9Dlb1QYQHOkRWdLAOBkjURJn++1OiQQON8Ot5F4S402pCO6MwcWZJrhSqY\ns6A4\r\n=2kaA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7dea0f23de51af336a2fab0286a73af30cddf3be", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-named-capturing-groups-regex", "type": "git"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexp-tree": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.4.2_1553163443992_0.474048693374975", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.4.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5611d96d987dfc4a3a81c4383bb173361037d68d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-Ki+Y9nXBlKfhD+LXaRS7v95TtTGYRAf9Y1rTDiE75zf8YQz4GDaWRXosMfJBXxnk88mGFjWdCRIeqDbon7spYA==", "signatures": [{"sig": "MEUCICCVp2l8oqIcWuE6carMKP6Nw7YtS1NgORkkF3cgYqYmAiEA3d+EZUBMWFruq3NqCB1db9q5TisnG3rdT1qc9Lw9NGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I/CRA9TVsSAnZWagAAa80P/39EOyIv0KAWYXUOYRz/\n+dDxL9FueXr+HPp8HnfOXbYtaY6nhiJLgxD3hzJty6IJENXNm7H6BgywLf2X\nICT6pHm0bgY1kpKTEOWQogRCobakv1qxzIKjDtkQE3wBKgsSpuEYFN8xNwgs\nfwcLl7sFVhQOs32lMeC0skGruQda1HIFlRcmliu6OzDkcQTtHHZsiJz8+XYX\n026QuNDua8G7un8/EdorNmCRZL/ChMbEdhN75H+vA/gjfdHXxPCv+kUOd2X9\nfD4l8QzmL0vBeA0qZbBlIfbUyKwCfeawFHVLEGisGZqhE03nJ+YJ2i4wC0/v\nUVl8MChI2k7FgI6Yye55pxgEhtaBMXI1k73FX2SOICsJd4t00xYHrcr/1asL\nmiTk68THXBD+U2nSVcLIaZvtUi+YBlq+wk1rPguChdZGp6BlgBYYiRPSGU/o\nn5+YkmsyEIVNf+fok+GVmbixQ974aUPxApkK2HyxtIVVbrmkHsiTCDYhcZX2\nRFNvHUaFQqNS0laziI+0o8gAQoDKACtLcK+dSV2pNEUVYZAavKiRmGe7/4rD\nWPtQ5zLBwDBdgyDmjNLeIn4OSdos6sHzN9giME5zgIIeF90htPIFVyztvI9I\namdsOfVGuBUAQ/wNLedx5d1Cd4rTz/PicVkPQg8fFaR6wAsIogahEnxIfp1b\nNqnu\r\n=kOkT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-named-capturing-groups-regex", "type": "git"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexp-tree": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.4.4_1556312638369_0.8427711205779969", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.4.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.4.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9d269fd28a370258199b4294736813a60bbdd106", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.4.5.tgz", "fileCount": 4, "integrity": "sha512-z7+2IsWafTBbjNsOxU/Iv5CvTJlr5w4+HGu1HovKYTtgJ362f7kBcQglkfmlspKKZ3bgrbSGvLfNx++ZJgCWsg==", "signatures": [{"sig": "MEUCIQCaK+J+lTty8AxIPp4T3lHN3iEZpQJdbHrx0lp8+oQleQIgDMMeWgnlNTBhEfVv6buh5D62bRuZpTzTUvZmIjgK7Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5DlACRA9TVsSAnZWagAAtQAP/R1uB+LLruOaKAqeKmyD\nHe7OFRETc5YqGTifRFSZbhuY21xVDc+Ft/X+f2Yshk1J+Y/x3sywXiD4Mgi3\n5uC9ObmAze86X5cxz4ztMdnu0Fzc/F5S11dXSNIWoJvMOZgkAjN7GVIQmIik\n4Vbao8/RU8oGzxy73VKa+p6oIEIAfUDdpCR3YytWjBcQY6R8VCFkRg466qAp\npyyH7St1zpC2NXi2PZpzYkNeZXrqQDx8D8u3QeB29Oh9hQAzICYFzNw1jlet\nn2E/NEovXk6sPOrrW+RLIk9ak8i/rdkyyWqaELzX49KHgMCam12J01aoMblo\nwjl/Cn1Nnzu7tflqHwe+XS3dw1zKDWE5+nPqHCByYsJ3BDCyvKCBsF8t7nBi\nM/T/9l/TjaZxNu6oUGL1z7PUQdarf3pZbWAH7JmF0La+0pD5C5sOTiPl2epD\n9UsPwih22UuLK4pyI/8J8XuXdYPVOzFBW8dQOtjL8sk9Sw6B9DnD0AnTLVAk\ncXLOu4MnN0hYaNgxnWTIBLXiBzIYTvMTvGO0wITN+tUhSp/lt7smQXJoZqOO\nHaZlGD6en5V+INB/zvhJrp93abKQYB/Hpvs4m4RZg0dNnij8cLlEF+lc7htp\nWh5H3XlIJlbLYGItoBYNFltx29Yl7TrBrm1nt47xND9EM8/ec/pbBxBxfs7c\nhz+4\r\n=kfbJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "33ab4f166117e2380de3955a0842985f578b01b8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-named-capturing-groups-regex", "type": "git"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"regexp-tree": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.5", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.4.5_1558460735647_0.8766965502781487", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.6.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "1e6e663097813bb4f53d42df0750cf28ad3bb3f1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.6.0.tgz", "fileCount": 4, "integrity": "sha512-jem7uytlmrRl3iCAuQyw8BpB4c4LWvSpvIeXKpMb+7j84lkx4m4mYr5ErAcmN5KM7B6BqrAvRGjBIbbzqCczew==", "signatures": [{"sig": "MEUCIHzpAuOnv5RmNWsHXmq0MO5k06tX2kKBz3scEcSdoxXyAiEAtY0IwI63B1Me6UxLWjLdM7GPMXQuG64FzZYvUZ32sVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcphyCRA9TVsSAnZWagAAiLQP/1SACxBe2LdKINCcqS9v\n39Jeu3lUwCQciqW/jmAX8MHBaETqOnLRel88eDN1X/NqmSpDZPvMI0+qqKsj\nm5V55csFZGlTWO44gFDUPg65PlxvS76i9H7rDMYst7Rs+QXhWcppXBnRr1zl\n+w/lJSLsTGBvqjyOMDSi5OKSt/jrID+TeGCgdEf0vVdp418CkMh+0QmfeWmB\nH345AY7AQvRvRL8vluY+z/4cLyGpunUL+V0aOByzwzXhomAKvDpwv85GF6Wq\n0ENEF3Ma5Alk4jwzhXGdWPXcJxfO9IgYejx55D2XFEbXj4thX6f66kGbdOPP\nSaM4WQ2BUCeklo0pmfdVOJ00AI/cP9P1zaeeJFs86LjS+PjFHBYumBAicwZv\nhYm5hCrAB0xX3vL8TMzWAE+0TpNxmt7bwL6xdNSNwPSh4Thqa6QK/vyzcDLT\nXOFBthVjdGaJXGy5Oq+2moxvMgz8Yma37Z75xPmoEFh6q1MaqtTQz25igZWl\n+VKV2S+cvhQZ4A5tUC0tlQ8DxFe8M7j0SUW10gE6k8TsnUts5C44iS3qBrzT\nse9Bhzkl/0vcVWSZpDaX8oYKaE3iUXdFuRqZS5piIzCXCHw2U9WvHe+fnchQ\nMSe/Ip47gadtSWUdcwWHHqXq75+Gfu2cAtOyqnUMJqLAUIZ4SSp97lzQV0Ab\nTD4L\r\n=wuLg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-named-capturing-groups-regex", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"regexp-tree": "^0.1.13"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.6.0", "core-js-pure": "^3.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.6.0_1567791217413_0.12604179266934556", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.6.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "c1ca0bb84b94f385ca302c3932e870b0fb0e522b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.6.2.tgz", "fileCount": 4, "integrity": "sha512-xBdB+XOs+lgbZc2/4F5BVDVcDNS4tcSKQc96KmlqLEAwz6tpYPEvPdmDfvVG0Ssn8lAhronaRs6Z6KSexIpK5g==", "signatures": [{"sig": "MEYCIQCsatWoqBjaXSuY43m/Mg/hzUwAgvBliAvwuTNQnnGKuwIhAJQQ1GqufWxCYd77uMlXfTG33o5zDCssh7a32AkAWcVx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdfCRA9TVsSAnZWagAAP3kP/271MAeqXE9cpxux+aig\ncfQtTGq9BKA+6YqGvZHJ6jid0BnZEn1Tj6CVEb9fvBov+0X2zymTcTJBSmYf\nWM/3psPMqEyPCeAglfTCQ/gvlnLX6zqXvSCAqgggdrIggpK7pVrXZ+gtg+0+\njNFIpVa6QCPfsruONZPnKNkMFwrMAu7pLHu8BBKE9z2f093rG73LH6iyASdk\nCBsYmwVdS8cJfoNkSiXy88DjiIdS9x3saerkmZstHgASr/mRHlcvZFypzOvl\n7Hd4A3g4IrS0gLRFJ7I66o4ZUWeMFp7rmQ0jt9AwF85iWTffnc2ed9LxZpvE\nDWkfg0G7brHAcMuHxZZWGTQjatKap9ZXm3H+YxkCQOJmbeDBAk7BUDJbaSTX\nNQ+szwo7F35dwMWibKxBD1QxmaAIQLIsOpcF6aOXbw2e3F+LwmS22cxkvEVn\nlzoKFxMKbPqHrv31dBWyujhM/V1YDo9s+bfJY1PIRIlMkGqYaSKFyWUiecjL\n8gArUzGchDuOaoepezuLvJPUzvci/bJPuHJn2gz0yHiA0GTLU0XArs+rmTLp\ni6SJT+i6Vq8fkZ26+LcT2VD9XLcDgcQ0RGs+p21/rEYzvUuwG8f3BY+x9tgJ\ndomrxpGafVtZRqxsfwP4AIMOIjkSYFdmwjNqE60jHab8GO9k3ufh/AoMx7y9\nK5R2\r\n=ADg1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"regexpu-core": "^4.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.6.2", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.6.2_1569273694629_0.5817183650391826", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.6.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "aaa6e409dd4fb2e50b6e2a91f7e3a3149dbce0cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.6.3.tgz", "fileCount": 4, "integrity": "sha512-jTkk7/uE6H2s5w6VlMHeWuH+Pcy2lmdwFoeWCVnvIrDUnB5gQqTVI8WfmEAhF2CDEarGrknZcmSFg1+bkfCoSw==", "signatures": [{"sig": "MEUCIAMpLCHUV1dwe7prR/uVlnVV/A/c1hjLJXbLBX0M4hjaAiEAlokH17IzC5wAL6AGRrM7crNW8CrYsfxVpssJwxO1H8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhSCRA9TVsSAnZWagAAXUEP/3xmSYk4eaOE1ie0d0lM\n4uY7BHJ2isX8nJGEdRp1SvJCWpJsyQMReA8XgFjdy/4h+VNDmi/A14Pfalmq\nMI0DPnI/+4qZm7Ynf55zhkHJJ0dgnd3XXX/a2AayY+YFW+w3cptXBuQB4C72\nYIDqcdH46o3HhbhqpVIU6wi9e1a4rvRAVI7MBQOJJBDQGuvWlCHbDmpYS8BX\nMDzTy93qgi9nTqshOP5TgTo6C5IUC0h9tcAedA+EllmmEfldieU7xfQOE4MC\nnEywrCCEwKxU7iy7nruQlT1lJwwtJEPvJqhk8VaZ9FEsoKqlBrA7/7WEcThN\nnZMB/2uY5nuY4rxvoWJ6aKnMEWMUNw9B6S4RaSmnEJVB3e1AkYqNFcIud4sb\n1CsEUfo/nKQh8kkmVA5oV67AHNlr81D+yfmWgvxs7fr1TFeCYSye5XuovLZZ\nfvQ7QRPh1oqroOex/aPBtZHaW7WSlWov1D6OW9QQkCdqAdjfFGps1sBAF/Kb\nZBBGv9gqwXSG41Rp9dVJCbhedjqf1WmAepiW/LGb3ktV3DAJFFk0EPTNROGt\nJizOz+L48pXN6GqfmJFF4ktjKc4FvEfUk+3IU+Oku6OFTD+cdsOliARyD6O9\nffBi3HnniQdbPvFVInDMYnU1hl/0LtxRhZREmW6iJjnIY16ZDxnxJaFbp47X\nJrlc\r\n=5DQX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"regexpu-core": "^4.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.6.3", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.6.3_1570564178278_0.21772278693575342", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.7.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "358e6fd869b9a4d8f5cbc79e4ed4fc340e60dcaf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-+SicSJoKouPctL+j1pqktRVCgy+xAch1hWWTMy13j0IflnyNjaoskj+DwRQFimHbLqO3sq2oN2CXMvXq3Bgapg==", "signatures": [{"sig": "MEYCIQC+ochYSuhDWhU1GjqJpVxZDoVB+UDcIRGKH7vEg3WCJwIhAOJrreQEHII2e22Qc0iIrbW8TL0wb+WuzTxBy+NcrWtQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVShCRA9TVsSAnZWagAACOcP/Axl0774UmkeFkdpj2Td\nGGDGAmh5L0AQ0y5+KGysBGA/5mDuknw1zIkd4Wz+aqtU0GY3m5+/k8Ip5TeZ\nNTKosnJXAhq47uk8V9/Fzj9bE/wAzrbInxiUztgTrlanDCuEWE2dCiqTH+1H\n2/cBcdwK2qFGWYJE7eJGILjvmhQFwjnLaLlbnS+63FORcFQ0KJgdaaP/X78E\nRpsS5AY2rXcGrOruuJCTDExr1NWYq7GAQwrCTjqEjE2f4A7GGeqjhGg9jBHS\n2J3ap8CbWAIZW95w/uULs+8gxaCVf3xGBJwBUyxAz9GAtNkZ+FNKJ7h1pXmP\nHTw6DJZYYAJiFz0ZS88JvG6/I3UnEG7xlLayu+0nJ6ppfT8iyJh7ovG7suQ8\n/NxiXgvhdkzU6fsJ9lm9h6i/kjsFMxdjIHL8iXqDcXsOk5oYQsRfxBqRzBMA\nWl3I0NO3Luhw9B8GzRcO+cqxwiFG4LWQ3Sb5wkllBK97LPPay1yiWGyCiqHV\n6t4PsYlh9l690wW4+SOf57VAFu4uiPVDNqv+DwsP7M1mK2PxVXTdi2Whxq8U\nog6d4Y6IGAHC/F4Jmj32Ioe4Yav09tRBBw4MYYQXhMpkC/ZPsk/+Q9QOHhSl\ntFpMdP1pupH2lce064UsT4GwMo439eArFdKxplSbuGTocNmRyNKwummBDL6s\nL4wd\r\n=vB1o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.7.0", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.7.0_1572951200987_0.8880525309917271", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.7.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "fb3bcc4ee4198e7385805007373d6b6f42c98220", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-jBUkiqLKvUWpv9GLSuHUFYdmHg0ujC1JEYoZUfeOOfNydZXp1sXObgyPatpcwjWgsdBGsagWW0cdJpX/DO2jMw==", "signatures": [{"sig": "MEQCIFlw0HI13q4O+UEiFnGfpQdDqjtviRSxofmDQzb+vHazAiBDkOfM0lZ3Bj7rShXUTPMfpHM2y31PWXB2EispLCJuNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HARCRA9TVsSAnZWagAABrcP/jQ2NlucLDkgbcWSTmIa\nDhyP4/evmVeUZ0WBxyz5/YpBYhpno1gkXnGUAsjJqvUjPfScMQ2wBaA50vl6\n04DU6XSWC7VEylkm4PtOZJ6lJO1B5l1M1lnyv0f+3sqgFdgGvtDmsLV7TbQK\nl6KSDgfMFClxDLNouLS7EuoQghNgeCOFBnmjBt2Fj7dBR9Flzp2ZxNYlomRr\nQ5PFlfVEzpnzuXu/fnUuHHLty+IbgZL+b7aniW/fdferElYehMW6+X/kXh/3\nd8LLqrHN/OMJb7z0f+WnPCHsk1MSh7QAk6hgzNXddS0hJnQfXDT5UHwz0Lfh\nMtGr+kvaKdZUAC/yp9t3pmCrY4susk1kxFyr54vfDO8HnQe6eAQGFoM/fVf6\nfBFpW97Rs1V1QSmzhmAf9YlxuXNPn9GFZacJDs4mK36XDksvshsexcSicKq+\nCtF25kb9PEJYlXmuzE9HW181CzGv7hzeHGzRpiBcGCImWkB0IvwF+aQT69WS\nJBpnTpl2bcCnJpRvS/nsL/7ysv0XWT57ylRi6fMYIQaEVGLXEgB+fe6h1N+Z\nfDDzxB93bPxs+94fXubOIBEDpNjDBRszFMMzTS995KYZoT9bYD72h44xWO24\n9gg/D4WhtMv7+SwH0mL9C3OLCw5bhjOOUuFsV+hCmpfyIta2iBsiVJqnSdyw\n62i1\r\n=43qI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.7.4", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.7.4_1574465553063_0.38698624822842986", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.8.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "718e168e7f5ab83fa7e4dfd0cf1831804fc016f0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-kq1rxQ1HviCP13SMGZ4WjBBpdogTGK7yn/g/+p+g1AQledgHOWKVeMY1DwKYGlGJ/grDGTOqpJLF1v3Sb7ghKA==", "signatures": [{"sig": "MEQCIDDuqjgnAemTIw4NyyIhNFNhVuUxgK+GPHXYeRirhQGfAiBdmQWrfhxvpAwkik1IqvPgY7z6bBbdfi9UMioCsgDWLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVzCRA9TVsSAnZWagAA+2oP/0xR5U6EdnEEGZgwCdbK\nmHV4Hra9sYm19VnLWnrX1BIvXZV9k3hEVfSb6BvjOVKkcN3E9GmDD2N6Y1j6\nynr8GZ9tpw5qtNefEIpZ0MrZOZHWS+RwjG+Ek9VILGsxiX2KvmXRVNprbeCF\nHz+oBUq1P++OKrT9x8NObETssC2GUdv2ar9eDY0yueisenTmVEvScIFPLcJw\nCzwao+M9d+G91GLjOpLSSSW/Rv3GN5m8RrQzd9BMnKTm/5bynCX+8lx9PwFf\nkZ7ZsjJKaEmHN7XQKuRWDu6v2Z24GzwvuUqeBkUWvxlFnaShw4M0xlm7O8vB\nbhsCZ5/SiM9lgbwsUP/4vKHK8a4P/1bI5z7z2DjFvxLatmAvz2fz+owEcffr\n7FV78CamLIlcRGsQ1pkVKYQErj+NEiwkPMS1QnF5BTj8Yh24DwUgf9/yk3j4\n28v1QxBzB3Zwf8F9ypotYUk7AuFjC++hKOTR9Xo7ze+Vn6uXMP/GMnPFkP2u\nQmhWl4tsUyH86eNf5/1WawG0vaPFCAKFciINLvpVm+7YnrIFsEOp5pekx8vg\ncssKhIh/c0+KOeBdt4/xci1q765JIR760V8n80vvd7CVGyFITrd7szV76aZX\n91gs5ggnU6swcMChGJU6uTSOg0WsGSD/uylrrL/IgLrNOlLP+9P0Bn1o5u1b\nU2Rb\r\n=X0yR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.8.0", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.8.0_1578788211401_0.6525219156205018", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.8.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a2a72bffa202ac0e2d0506afd0939c5ecbc48c6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-f+tF/8UVPU86TrCb06JoPWIdDpTNSGGcAtaD9mLP0aYGA0OS0j7j7DHJR0GTFrUZPUU6loZhbsVZgTh0N+Qdnw==", "signatures": [{"sig": "MEQCIBiPAZUJu2Cw5pIBOIQ2p8om1A0x6nH5XT2qBGIzXPW/AiAKbVr+9JnlMpGJ8Grw0qZJQS7buyHfN6MnEP+aSOp5Eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQzCRA9TVsSAnZWagAAspEP/iEB7oYha40doPhlb51o\nCOylWnPqM+g1okxwEp50yL9nlMKHZNvgwEsL3+C9+yPh2sD57auhQo7M7utX\npMLtRRm4lUOLQvGTIH5J5nXkGNefBcuUG5eyDESRQqWzqDRr8qi0I/F3cF5I\nq0ScnkYU8RIgyopNfYdiLACveq4NwQwwSbVhvSZ4ix29LVfjkfPvnQirJCRn\nMWruXUc2d3ANEpruozscyI3qTNhisutsnje4PL9HOrTs5up2o04RLQPY/0To\nPYLp6EVXv6R4BVETtJdZnTNEMI8sL1DTyIpOId7teKVOkjzXZ1XzOjBlwBrZ\nOgGFGYBROWQpIbqWu4WX03SplD4Shiiaj4GKFX5DNKWjRzhB2oZ7QxlhAz1w\niY4U2t20AMF9BeLVbgoecIUIkgMtAwnNkX5MKdqIMoKDWTnux1bCQV58ioYh\nIaV6JS6/Iy3LMA2QyHdkH5cj8CUeBl+KCKMA75t1niL4RonZGnYPPFIrKCn3\nt1chkiyCSnFkGrNYEyEqpBalQyhoFNiXcJaHoOknUDIgj5oiUS7YRuzZyc7m\njqbtnFi/7ax3kM7DqV56erGTxl7Z092F7/MqN2t70Bs2Y6bGuGOx6N1ZCOQw\n9Mz3kM5PLqHptDsRPaXUAZ/m8a56Mpys9VSuUdD7te9I0PLWSrc8rJHAv+fY\nBcQv\r\n=b9Sh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.8.3", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.8.3_1578951731512_0.8685506143930897", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.10.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a4f8444d1c5a46f35834a410285f2c901c007ca6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-I3EH+RMFyVi8Iy/LekQm948Z4Lz4yKT7rK+vuCAeRm0kTa6Z5W7xuhRxDNJv0FPya/her6AUgrDITb70YHtTvA==", "signatures": [{"sig": "MEYCIQC7cucZm0mmUvcJQsR/sU0c0VZ6uaVM37b/Utq211DNRwIhAOhj8mwnjZPHwDCMqEt7FAimohqSFDdtRgq0kX9LvyOt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAAtD0P/jn3lLh1WOGCo2GkCQGq\n7DB5juTJQ3GkevkHtK1+3S4+3soUi/lKGZgooHECFq9MofenwWb6XqfgJPQe\nMrNwZ6HBuWvAtfB6N83Mc5CkrcBt3K7zQQokz5QKP1GXQwCdTXfvJnGpTVxs\ntN0jFcdyMoq/HwyrrG5Rxi6Sa4gBaI1Hob9d6nWFJ5/13TpnE6YN9d8EMx6e\n2v3MFhaSovJxg0z00ziHTAWzcjrPNNAoDoz5/TVSdnhd/tyUprXfGaPcC/qi\nJRuCVRehOlTX+uZpFHoRu/20K0eJ0lzmxE7FfP6Wg3lrZfbeo2VetJCxn7LC\nzlA320yud4vuvsQ/GJ+4LuNohwQm1a58ExmI7WZI2isYy0lGWJ2zy0PL9bZn\nGz42yUKZDmg7cw4yNGLzrZz7/rdCiQVXR2PqW7qwwANp31Fi+olF0tYZrjsr\nis4f6YWEBcC/FiIgirKEInvTiSZFMQEE4Uzo49HcPU0YGnhlCWdsFmX3qeR+\n2MndfDKED273spog64eJNOGHOVy4poxQpbkprM2rqGTRG08xwTtaKxdsWTqL\n9zJQUNblmUwOXDxFDX2ITgyX1v8r6C1lA4vsJ61iPcGRZ2CHlwM7byHG6LOA\nG+EJnGPR9ewhLqfopS9omOWV6S1LznNtZeFXLb8qpivCXYrmDOsQpqtZWEM8\ntpaP\r\n=cj5g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.10.3", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.10.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.10.3_1592600047874_0.2397192634162324", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.10.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "78b4d978810b6f3bcf03f9e318f2fc0ed41aecb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-V6LuOnD31kTkxQPhKiVYzYC/Jgdq53irJC/xBSmqcNcqFGV+PER4l6rU5SH2Vl7bH9mLDHcc0+l9HUOe4RNGKA==", "signatures": [{"sig": "MEQCIEFXbS/u9Kb2BM8dZCCLNk8deMeU1Ja5P+IEAemZZ9RoAiA77atzbzGoCxPBMcmo3ISIrds9XNGNqxaO/8oVUhbN1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpNCRA9TVsSAnZWagAAsdcQAKFax4Rd2Ycoe03DvWlS\nujHuK2Z/5Kg+MnO6faOX+F/1vrpYUthkO5a5KQ7F51GCfZxy0iBJ8uhW1siT\nOAzWQE/F/6mjbO7U6XZy5mecK1VCdtHl65yIk3ao9HxzJbagRJLUdjBpRsJM\n4Brk13fLM0un95Rbhh1gvu7flin33YSfadbV2w7Z+L0DxotT2B93hWExuzpy\nG/G9W1t2U/QXyDuVOgjnYJYvCnH+fYm1841fWgSkdtZHOcqO26GAm07x8KK1\nHBlFJEp5va8OHHLD9Rta4Q+lDVWx/pMX1RVVU5zIrmcPPHcj6tF6l8XhkVkp\nd2Opr4AjiF5mGmoGMzmHOV42TwMV7Llu84fWUIx/73+ud42QyPb2kGslS1le\nrA10mxr41LYKdS0O4HPWqU8UXeqd00+s/d+QWTwB6BuWae0ZclQ/9OiBsy3j\n84BoIPpKeCwD7XuSROHGKxE3FpPArm0yPF+u2KWAU9a8doRxgAUsmdcuUQLI\n0uyMPrBRNr+DNmYE+LAMR8mVTkscvuMRiYwVhztoMrRQITvHxNKYaQFlQAkO\npjKZ+wBIXefvTn5gzM2mt7jMM5YLN2yKV7zUOOxjZZf0AZ/zftFH8AIt+N/3\nNJAkoyLt3vdnjln4mv5C0sueYW9LlWlhhe4dV/jmlwws/N/0zsvxfyQJ4i+n\nrPxC\r\n=/BrK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.10.4", "core-js-pure": "^3.2.1", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.10.4_1593522765423_0.008402689696368926", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.12.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b407f5c96be0d9f5f88467497fa82b30ac3e8753", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-tB43uQ62RHcoDp9v2Nsf+dSM8sbNodbEicbQNA53zHz8pWUhsgHSJCGpt7daXxRydjb0KnfmB+ChXOv3oADp1Q==", "signatures": [{"sig": "MEUCIQDbxGEWfxspeo6ZByCSDyu537klivGM8uS4OY6B+o4xJwIgPJR37knAiqvACzfjdduW4aJZ+3MFoUGp+dzPenUS20s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNANCRA9TVsSAnZWagAA+q0P/ifSQzdC/U/o48No6jzS\nKv1L5w/Y7G5miG2efBFkGl2V0rwCi6XQyzcRdOa1aycHUnJOIv2jnu++HUWK\nf7UJ8/p9StvtYbHFWbNZ2W0NWphzDNKURuj1MmGNPxP/Lrh82BoBd6hDz6hJ\nxOVulAMcqzxhfmXCL8otcQG/4hoXELt89HVeiNwOhve6HGrjyKSl/tA8drVX\nh6JquWw8JZRkS2a/eaOhvPFnvGdzxrkd4efYa6EnEUPgUgyTNiZWs+QY9VWy\nWddjgQB8pWK9OUvam/HAH/st8fLrQBImW3DqFQk0bECaP2Rh9GLGbAzLpoII\nP7HbsseZjBHjzM1/iXDjKPET3j1JtVlB2IX1JMlgm9zza/6q7QFUGZWGuM1Z\ndj6uCEcKRsm17Pf/zdklYRdBYIfchCVWt55G2ID3qhbqt620XeaoWP8+iLt7\nHnjiI18mqsP+2aQYFxFf3RzHFg36zCfhuUt393rJckXipMWUPfTxmeQAWS+7\nHOKQ6sbmqkaJ1AyVBcy5et3YWZcT942LMQtMGwjRkvkcOhR93EwZBOPrZXJi\n8SRd006E6TVV+bGr89stiYhz3Ee3xcYWIy8iZGTjPQuHJf3R2oY+qsRqH9Ru\nh9JEhZanSkAS4EeD3K4NIwRvTZdqzJrrXy+Lf2hXdMAO1TLLq3XTwtdQ48gC\npxK6\r\n=WUBS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.12.1_1602801677167_0.16302766155332682", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.12.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2213725a5f5bbbe364b50c3ba5998c9599c5c9d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-Xsm8P2hr5hAxyYblrfACXpQKdQbx4m2df9/ZZSQ8MAhsadw06+jW7s9zsSw6he+mJZXRlVMyEnVktJo4zjk1WA==", "signatures": [{"sig": "MEUCIQDtC1F1hFMvIhOihy1/cUSfY5KEmbvIb8iEKFZOBLP2hwIgXQpcE4VlD59yjiJeiFtZ3WsEgdGSiVqQFUTf2huiohU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhKCRA9TVsSAnZWagAAiP8P+wRD8bjz5C50Y77ICSSd\nmnKakMD0JZY4USCGCNBdf584X2K5rx7DDZ9/EvHzuUTq4m66qCcu72s9W6y3\nkXoYlARZy2kvNDffK/npg2p2lwNvz+mjoLUMcWFtkVtmhXL0nrrIa4KSU9l2\nbmqbjR8FOU8s7HAgOZFzFaJKIpMKsFFkTCGq/kSqMNU8oXktubNxRXlMpDlC\nDkpR8In7gnQS5B+D/NzuFUbxuIwAUa1L00Roe5uGEAz6RNaxGH5MPz3sqW9a\nmkxbJPtKr34mQFaFTYHFZv/7B4XO7ymKyTjSlMPKjWRSlGS3CoA/tWLtBmx2\nTvTm8sMUw1zNLub1exErtkQvZm0KetbfJ3dcl+oKtp3VjC8dyUdXM6T3lqOU\nkDdhKi28HB1dVdQgKWFtMZ4nXB345ZNFMtm3e6w/wc6dBj6V6Ji8A2OGK7N4\nwhKsAFQSKvRN/kj+twB/yNBquKfodQmBr9nHXRPdfvXwK+8EUH717iUMh/t1\nXstAyVGhsnqE5J5RjcEH2dVZh/nEX7x+vLVUBJ06Ve+6hQ8WH3hQETshSfrA\nVZxmcpdobVSIDfxoL0VT32JFgU7+LY/illwrw2dGEx0Nt+uru/pbejz7RghO\nXCga5wPvZJ4UMj+Kzfm3cN4+NgLxTDxwtbo33f3Q+1Tnh7QHWL2KbH6qXPDk\nOFh0\r\n=RUv+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.2.1", "@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.12.13_1612314697672_0.36790580631672243", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.14.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d537e8ee083ee6f6aa4f4eef9d2081d555746e4c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-+Xe5+6MWFo311U8SchgeX5c1+lJM+eZDBZgD+tvXu9VVQPXwwVzeManMMjYX6xw2HczngfOSZjoFYKwdeB/Jvw==", "signatures": [{"sig": "MEQCIHrR1ytFNn+L11C+x9f48O9KyeBibLUhxVSK5VyCfFj8AiBdxyics08pcK6Y22R8kQIkbkIFeYA6/HAtRilwhNHgNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUr3CRA9TVsSAnZWagAAsIoP/jJGR1klJ0hIXg8bw+ye\n0ClsVKi19kx2bs3j9T2/lhYvmXdSk10MvwU9zc8X7ogaMLaNmwGAWzGtRusd\ngiT4syVQkimJdgwMK8+QTlOpZgFzoWqZaTEkU5yIzpzmkbmbj9JzMPurYBSv\nPhQKyhPqsItC+e+MBCyk3wCSWUotQFQAuUhV1EOB9A91yWlYL2j4DZR2vFC8\nYEtFq8dW6ukxz1KMIrlk5Zakw/Y5kQoTJlPYBNFvO1FcIyXDwhJZzxo/npxQ\nHeEy93Ioo+XA5AhQFot881e6sJRWQWdVtPa/dtLCDsDfIU1xDl5j4SF5twi4\nfwsWPEu03/CEouQmZd/qWi4rj3gC5CsDrdxWTtl9ftp6YawrAUzoCoTXM2Ci\n5vnWLEa5Llis5vlfP3whBf37vohWRQl61fuh7sQViW6KLmhpP21tbkDa3RXF\nFzT8fr0yzZkz25iW3Hz7JH/EHiI2K/9mLgFhWqGdGWmpkXV4S4nFxtdoU3Zo\nGWPKtL4VAwYgfpEOMh7HT5fJ1GZDjdsFtvB8UBNUnotRP4IVc8VxVyYrPBH5\n2kEbV6m/sWIvv5kJOISw5KioJ15EWHTPlVgd6dnLOJbdWvN9xaWg7IJoWOcY\nSUxmGg/qIBDufEp3LG4ItyVYLNtWsMK/R3zb3PKPc3MDxZUEiwV/jwdhtSwU\nDMXU\r\n=duh4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.14.0", "@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.14.5_1623280375506_0.12128379255328237", "host": "s3://npm-registry-packages"}}, "7.14.7": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.14.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.14.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "60c06892acf9df231e256c24464bfecb0908fd4e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.7.tgz", "fileCount": 4, "integrity": "sha512-DTNOTaS7TkW97xsDMrp7nycUVh6sn/eq22VaxWfEdzuEbRsiaOU0pqU7DlyUGHVsbQbSghvjKRpEl+nUCKGQSg==", "signatures": [{"sig": "MEUCIQDumen1WGpgQH6B9ibKFvxN1oeYHugy5YSpcd+Io6jhRQIgfvdCepjYB9l2JOZFtkYdYcJvW9R/7cCyxoMhEUVnm/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0Qp9CRA9TVsSAnZWagAA+TQQAJbBv4gZDZI70z9FuHOl\nNudlvud1A5tXkjsvcrbIlG971MdjHjzNqQgItRqEhYEs+3kMTe0AcH8YuZEq\nhXM9/CqVJGXgkAa0vHFDk1eos/pnDZ/tAGVFSFvXfWAkZZ8dSSDtMOF4lKpJ\n7Vz/1yNTUEfmmPnUIV+XoYjgxkAjxSufxQEPA5GvvMA2QDS1hFpSZ9xy1W5l\nqP7KtsyC6PPikhTyqzwZppd1/jqer1ljjWCE9ZdvElu993gz5WKXMuU9bOna\nBiHZbvDflH4uEzs0FWVlaU8Ezzo6imu0ZziJOHCOB8DzwPfflWb6EBLZ14NR\nctmjNEE7BgIbJE2iWFVSb/qb2pccxlMfDL+Ya3re/qaDTfcbR2KysCJ6czfx\ntLXi2uH8GHfAvjWEw9IZc8g0KA3sR4Zo7GvwJiUSr9Kj774KtloCI51UtUC9\nF5YXseWcQ/uOeyxpMKpcqTZRVVUYFBGdXAl4OSlkwRjydWC1fz1fj0mh9J0p\njIvwI3FdG68qZ9g6TJfmZDuAvR5sm0VDrhPl2Yak39doEH95bgHXTcFYRXA0\nGulXJScr84YW8MbC4CCNgdXbhLVSanjyoyoRDCs42NYlF15vQw8S3kVSrV6r\nsrSL+v/lRhKVcU4hsY+Uf4mma6oxtjNKtbKxvJ4wkwhMcKbaN422bGWOUie0\nHePF\r\n=8Op9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.15.0", "@babel/core": "7.14.6", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.14.7_1624312444530_0.9316533659643833", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.14.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "c68f5c5d12d2ebaba3762e57c2c4f6347a46e7b2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.9.tgz", "fileCount": 4, "integrity": "sha512-l666wCVYO75mlAtGFfyFwnWmIXQm3kSH0C3IRnJqWcZbWkoihyAdDhFm2ZWaxWTqvBvhVFfJjMRQ0ez4oN1yYA==", "signatures": [{"sig": "MEYCIQDl+TIGyUoZye7jpVEIdBIAhEbktpd0EJIJCoyh1DgVrgIhAOm32XlwZmNiPnv1htSBWbgGtJLpeZzrOYE23iVe65O1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLrCRA9TVsSAnZWagAAXmkP/A7JpvIxAoHn/8aCWL55\nsziVwmsmlV6npkWsL68tS7TBrPzUduYkjBFGGtWpx5+fQMESN39W4JxUAS+E\nlS/aoCtp3iipMuLZ52LSnOLfOPI+uahMQLn4A0HkLjoq5R0fAOzzd3Obg00c\nBzELgrJmuFDp2L8YPXDMddxa6GI60qDpDAjCsi2VrLKebd2nzTRY1DhG9ETI\nGMr2qCE9JJK74K+1ZaBUJ1QM70pIAhdBF6DKVa1oJddKn4Rtq2XW48agBF0+\nRIZjhImMGIRivBZcm6IkL3yMTyv8rOv6la1NTqa8Eqa4Ki/hMwJAwy+txY6D\nyTH+K5PZvQ6r7F8yJskXyKxBWHHGNJ0K4anP/zXaKt7dJ410SNZVEcleXd0G\nGUniJ+coYOtjSnYd14tvw7K3neRkDxyX/xhYCU/fEndGD05fvHvAOhomoDnd\nmmMujrU9pLmKaDAE04FX7ASBCfxlM+IhqUrZEOSOwSN4iV/imcdF3gjeycRv\nJJuTs3zhYD8OxRC/ByysrLvL+gCv5ULBakKY++GPx7sn07ivsk39atOnngdF\nMGGRsI5XptWenW7CgqBRbZbaVE9yYxwsCio3gH6OB0aRVwP1//rkVBi7YnMD\ndnnleYj5eDLQk9dn5fPKFzsfoD3wqlUs/R16Eeq+nJD8XEnPFzx/miDNNl87\nbXSd\r\n=hXy+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.16.0", "@babel/core": "7.14.8", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.14.9_1627804395029_0.0854517797514871", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.16.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "d3db61cc5d5b97986559967cd5ea83e5c32096ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-LogN88uO+7EhxWc8WZuQ8vxdSyVGxhkh8WTC3tzlT8LccMuQdA81e9SGV6zY7kY2LjDhhDOFdQVxdGwPyBCnvg==", "signatures": [{"sig": "MEUCIEi2S0GCqN1E37QpVYGhSJjBpsPzyYk6NkMB2jT/qWdtAiEA1nV0P4v1wgingcWnuVhH2z8PlQszVSDYMnLAAqV6Quc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3247}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.19.0", "@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.16.0_1635551270708_0.8460902158923831", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.16.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4afd8cdee377ce3568f4e8a9ee67539b69886a3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-/wqGDgvFUeKELW6ex6QB7dLVRkd5ehjw34tpXu1nhKC0sFfmaLabIswnpf8JgDyV2NeDmZiwoOb0rAmxciNfjA==", "signatures": [{"sig": "MEYCIQD8RYauvJU7XiZya7mLBs9PEXCxt0iKUp2diglK2bbUPAIhAIfW/uDpL0gdey3naFBwzNu/YEUNNY5v17ejSviBmttf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jVCRA9TVsSAnZWagAAGh4P/RZ3oCqQ7xBVAWRSGiLX\nd7why1f3PtG66K8Y9Of7T/10f9gH7InQlXAfthliq+S8xOsmx5Vg6N6o7gwD\nawBFflByYDczBaWDuBsjTEqhC48CHx42St71CYGJw79IaudO34tYz4Man0S5\nPnl3g9nwlKxx6W3ymo3a904hPV7jM1dzMqKUHUqR9T8WAaik0KIzMTBj6CTU\nPU24IVgyyVpH/FpBGRdnXR77KQMkS5F8x453YCL3675I2EmGsHEhIP1q9waW\nJFxoaZQiIV9No6sXmVx0VAV6BC9PTEFdbEyjWSE4d9buMPsNpsK2cOeh7MB5\njJjs4+8GjyandQYWo7TYlbmeLG3KslwXC2VkNA+p03ADG+Mg9SBv2UoDFrQg\ngfN8t0FtGyVSb0hoNA+FV96FHt/0uLEpQX65PG45UjdxUD9HRyT4wJUooqB3\nAz3SX5JEtZ0LWlAusrzHTK2BSOf4V1YvzWq/b1pPZefPwgwEAu2Reo1n13Qv\n6QSniWEvHqbOCyfOwPvUVpWm/aOOoMG/qHmSbtDBTC6p0sjo2ByAPMUV3Xtu\nxETsDv9YSKQPezKN0VomIUSVCXGKB8MxCVwJftrnEYtAj8vGZ8tZQurCDxOP\n/kApqYiOQmXCVSLOJeysex5oKBNuOKxPlxAHz+Cnmpu9nm6aP5AmjUYe+rAV\nex8a\r\n=6ym+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.19.0", "@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.16.5_1639434453806_0.6911266781806604", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.16.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "749d90d94e73cf62c60a0cc8d6b94d29305a81f2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-kFy35VwmwIQwCjwrAQhl3+c/kr292i4KdLPKp5lPH03Ltc51qnFlIADoyPxc/6Naz3ok3WdYKg+KK6AH+D4utg==", "signatures": [{"sig": "MEQCIHNsU0fwRwO+BaoLqYXE8L4vlzQPBQ8WoFEjMnF4Al2yAiAk8HI4yPMcBax+4LKyJUJEbuDen4FBH4ADJv/j7Q2xOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1VCRA9TVsSAnZWagAAho4QAKTKYKAWp69aoWstGVd4\neFZpvHRQDJFfWnPF0r4OPRaLrkl918lnbania66F8GpL1vzyrgsTtK2Vc7kA\nXMwI7BcpMFZ73mggkEzVqnEZXsIeD9MvgqCYE7SXagym6mQwvDfFqaqsTwdE\nY2wdH7+2S6pby0AXBu9gMZjdJKqeRziOcnWOo/Y0uox4CIVG4dBUCgdOvte4\nJAmvOZUoH/SgEmvIj43btnRDSoxKFmnfAKaO1tgIEx8NbWzMbRO38HRzLshQ\nEa38h9sYxPQR6eJjuJH1MiWqnfUmXJtY4WJff7R+RGLTKNs1Ehh78RDylWDW\nPILGpvJM7ctz1bVjWmagYR59+IV7/+8zIK4/79q0Xweakfn849PvYfX5oEr3\n8fU3ZJQDwEMwn+pjRSv7NLdoEb9jXtKMhJVuBXIefmFP+cBztIBeoZ5gClX/\nQJUrTFDPsZRCPy+RENo7DSQcC79jCV5Yh9XBogJif3Fh9t61D8VybyNHIXqS\n+b+zll7MzQN6dZnm2IXZ/QXdVln0366kePGbBR3eFUJsX2Lp1SWYwdjHORhA\n+yD4zh7R55D1j9l5LEpShRAIQeGKZrbTtgGSWh3aYGwCch6aoUAOhkDoQUfU\n6/3YQPOJOYW2wHJ2HcybXWjQeL65cfNgIEr6PH46qUuE3k1wTUSyiD9dGQuK\nWOmP\r\n=rO7S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.19.0", "@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.16.7_1640910165193_0.11030923732805831", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.16.8", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "7f860e0e40d844a02c9dcf9d84965e7dfd666252", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.8.tgz", "fileCount": 4, "integrity": "sha512-j3Jw+n5PvpmhRR+mrgIh04puSANCk/T/UA3m3P1MjJkhlK906+ApHhDIqBQDdOgL/r1UYpz4GNclTXxyZrYGSw==", "signatures": [{"sig": "MEUCICAyuaKI076FKv+BhoSZ2qOuiGusbR5ArmWxvFKxSo6oAiEA4c1Im8AksJLpL4Hjg0fIiWd5Ud7g/pWg7hqK9qx/RBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKgCRA9TVsSAnZWagAATVEQAJNI3QcXulOHIA/+lYZZ\nwPVf47SV5m2FDBdFAQCq6cIhGUtceaaO5GcAZHt9KX5Jl+zxJmJLU4Exmbag\nz9uxqaIOAfshKV7pWrI1IFqsD6ibceEMad8MOTa/Uqi+AVDwAOPdVCYi8IAy\nJ9GsuQEA2HZwV2RnfILkn8SvYoUq53By3s8nO7R/YP/zEEGvDgT6Cvrg6DCf\npo0wfQxS789X0ClZlQfAGppqeQEmdDdVJwCWFWto1padWPrAQzlIAX/5Xh9z\n2SxvNybTI2zPe2sfobUQiMFfbdWuypMHPyN+yin27B0mSQ51026C7E7KVLRy\nm11CU6F8i6+tNKWFGAylmFaiNdZFx9VDvEBi8MmGO+6Ohwc3JxD5nUTIGNZb\nYo0wMkKf8QCloXXwM8vFTQVModGJqVFGzLHT0fIF1rO2zK1A6jO8a1U9agBu\ns+lWVSe1toi2uGFCobWuluD/X/d09I9eAobpFUBESV+/zEwj7cH2/9/Qhv1Q\nKBkHqDlWO+lr1XQaVvsuVy0xJYEOP0NtUy8VdwhhnVrw3NxwRDVB+WqAffxE\nDxkHtXp5ILnHcsfSVh2VWKLELuI/YUe1M7hJbYdyk3dg8XMnN64BZ8Z3bvv2\n1q4dPtdM3sqovk/zirBuqv78+QTdIlr4To5LfBX/mi417RoKHzvTbaiGURKA\nlPZ4\r\n=DF4O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.20.2", "@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.16.8_1641849504743_0.37416032763051277", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.17.10", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "715dbcfafdb54ce8bccd3d12e8917296a4ba66a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.17.10.tgz", "fileCount": 4, "integrity": "sha512-v54O6yLaJySCs6mGzaVOUw9T967GnH38T6CQSAtnzdNPwu84l2qAjssKzo/WSO8Yi7NF+7ekm5cVbF/5qiIgNA==", "signatures": [{"sig": "MEUCIADDcBn+sHYgqWZM2WcWtStCtADo12pE7tWegfDcPZSjAiEA0/DE1A7qQmTyBCRIgdaRZMqqQFdgjHOIbGl/A+4FG1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXmA/9GuegbRNSJeatStS9PrM6kkyR7F+fnxoXPwBwJ4vpUjdkgYLC\r\n6oCyUsBORzBywFlgzhg9AOOGjPRFV+TRnSrq5WTTzmKEkyIDB/QGqZutCYir\r\n7j67iH0mPAvjt73jM+WfWivV+xKJJe8lIB+somkW+tI5lGGBydk2wQcX5uB2\r\nk5V0jLFim9d3Kym7Zl2598uHuLPwxI090u5qRy/0+zKofbZvWFo+BWUYT8Jv\r\nYxljKvkf/S8IAfqWe9HDksGBo6sSAQTBQEoaT3574nBJF287Q+k5MlXyJJ2M\r\nssc21uWhqyDmvNqH4syVLAe28Fm96EQ01odJ6WoFCsiGsIJ1pOtoJdOUYhrb\r\nZPHuWyUazzJJ/nfmARHD8fsM2okkTBd0cXPVESWRc5+X73sch+3a9aXTM7cC\r\nuzbRY82gySCqimuoAzqN79/gLqwCccZ6NC9k6W3KehnvtOPeVWUeQ1QlU1xW\r\ndbN4pK10D45oopCwGbgQ4+358xkN1nBKHFEuQMb5g4goKwxGfQBX0qevGvBQ\r\nFWMAL9/cS6tSxCk3yAAWng1O6QrOngHOMZG0CbvnQkxT/cTIal/BAxIAX333\r\nkxZSgvYf0SmiW+TTmyAYIJCWV36Jh96qF+jiqyMQXU0pawWi0bUlrX8ihwTL\r\nAITmtKCzaKh53uKlsFpwOXXMV/UaFiC7rbc=\r\n=dakl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.22.1", "@babel/core": "^7.17.10", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.17.10_1651250258093_0.8756221244416922", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.17.12", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9c4a5a5966e0434d515f2675c227fd8cc8606931", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-vWoWFM5CKaTeHrdUJ/3SIOTRV+MBVGybOC9mhJkaprGNt5demMymDW24yC74avb915/mIRe3TgNb/d8idvnCRA==", "signatures": [{"sig": "MEYCIQC0y5PgiuI46xNnSPtgzOyezAr1FIuThD9rxJY3oPsNYAIhAJh5qXr1UbgVXFfVlvwlrNP7kO5eWk0RiF43+CnMTp/A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeIQ/8CsyGYZfQ1lqwh8wp9Ueuzg1ENbDjHdCEmd7c0u1VnGmvHElh\r\nlkjreSt1wy2VWdwYR5TBZtZfZf3CWNc0qT5BVbN2ok6a0bfVjWVxETHNl6kT\r\nvUwMy1FUIs6NF5jHvXijlkoHCQZWIvBkhwgQN9/A9sEhHJAzOnTs3N6LP2pU\r\nmM1ykKvtVZwiGqpB9lEwaL5vPwGRoMe4JC2RScK+wpmmWrJ8cAbTqAmM0xDQ\r\nurL7Hv2kOuoPPhainL+IumyKgo/kZn9jh6ZJ8HYWYoYvkK16CnlVgHYJ3BRC\r\nacq/dAcMXcqwqKfVAvsFDJk/lc9H6NnOTpsS5oRpN3NnMqtCYcU0KPzGG7tX\r\n/XTEOgZKNxKiAOHobmvW0QGAR1zUJPsD8nv5jZfYUIxxw18dN4bHAJayKvK7\r\nOjpaaRLXJTZ1Oz0mcJ7q3caZiD2G1PX6PdlyddfOimS46tmm9D+mLVbWonHJ\r\nmyhYWkFNbFgsUDD6yA34GIq5Vuk/cHhK61swBmJVHS9M97WOmAn625Rx5lCd\r\nbYoEelk8WENR2EwLhO8AKHMI9c3KH/QrztANyKPyB9r1BCZ1WnPKInIXnnZV\r\nAMnMDX3nQll9CFoDn2dVcP4fxkHrHFi44yjpv7mfzRKJKhJ9bQE0pJ8gUqeA\r\nWrSzEpobcIlTVB19g8kZHB9SqS++p/fDnzg=\r\n=JDDM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-create-regexp-features-plugin": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.22.1", "@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.17.12_1652729577155_0.04345520667467384", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.18.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "c89bfbc7cc6805d692f3a49bc5fc1b630007246d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-UmEOGF8XgaIqD74bC8g7iV3RYj8lMf0Bw7NJzvnS9qQhM4mg+1WHKotUIdjxgD2RGrgFLZZPCFPFj3P/kVDYhg==", "signatures": [{"sig": "MEYCIQDmXk8QAz/hMJH49HEn5eQ46KWVHgEMr8xK61BoOK2yWQIhAK2ywUmYVW4yDwuybln9r+uKSweAipyGkAY+5XjPCI+j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6qg//bCgFJxktt855UV94Z6hVw86yjf4S6h00AjDx7Z4viRi7kvHV\r\nv/z58skUFvK0TPKtVKOtDd3GWWc4AnJIwNnE6AG73SJuETpldJHUAY4iRAfD\r\nd0YQYJPPWb/4lBiDEypD2lTHmh2WZ6ockWYdj02DsxULSWf6i41fhMCzTsdI\r\nuPgAUFDIXFdy5GaQ0POLrBhppyPtHLOj8R1+lTg5fZvjYuP6p9dGfIDqa60v\r\n90q+kRvDDuf+8q1AEHPI2QajlwYDHN88S1HsRBcs6xEABXl7eyHkz4w+hzvz\r\nSdEw2nrf26XPUQ0k++BUHszIQSU5v+dsYetzms/4PS6kmbl/LvBjjiDhqzXr\r\nQTi0zPG0x9AFpJbKPzsvxHRuVcz9y8OsA3/c1L/mRi6iHyUlWDL3FoK+25aG\r\nEEsu8EtRvHU1y4Klif+0LmD6+Zj9XtnE0CWlV8sl8XtlR1evoSThalRXmneW\r\naJophy+FwXMagxjz60Ja+Xtp0SWh4quCCxGtCR4ZZF8ERuQZm/kjV3l+/vn+\r\n9aUnp4bLfYuPFSH8BfbeR3D3gepbVV6oJpVzsnDGEgdrf8NKQ/kREVvuIbyU\r\nmq4dlQIRMa8q/rVR+N1pqY/6jUeGfFv3Exa3Pt6RBjfJSAw5kOVJot3A61FR\r\nuCVVvb/x6fDW+5j/tM4enLkwq1Au94dhLgg=\r\n=S3kn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-create-regexp-features-plugin": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.22.1", "@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.18.6_1656359426148_0.5038932204540805", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.19.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "58c52422e4f91a381727faed7d513c89d7f41ada", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.19.0.tgz", "fileCount": 5, "integrity": "sha512-HDSuqOQzkU//kfGdiHBt71/hkDTApw4U/cMVgKgX7PqfB3LOaK+2GtCEsBu1dL9CkswDm0Gwehht1dCr421ULQ==", "signatures": [{"sig": "MEUCIQCs6Mjif1wa22J3SOzVbjN33NB3AONuMQEXMUR+Y2W2rwIgIoXmCUJRo3qeMWWzoZ0mLQPPfjhIUFyJj+D22n9Ph8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqPg/8CdkHOQZaonLPKioTgvvNiO/O1G/VvXd71eN2SXprM8a8gAOY\r\nqwR7fffUUKzW61+uBbbHq+d4xIT0vmRBYKxTfuW9NaoXgwa90uPo1suOJdnb\r\n66IrIcZzM0PHsOKhEwi7xV8+LQuvIkLBDeihU+aJKsQ9VgtBj8LxwF+KVV8D\r\nGSmqadHXRPSgvrF8xO1fE52SYNUANh1ViwVrHFWlqI5MX3FwBLZnZeBl/H8i\r\nLu545DmVjjAAghh4lEm9EWfIndMWpqhEx98TujBPvU/sCozOqhehY1IaKUWT\r\niG/LF47G+F64phuBVp2WE+h7vn9TslpktjuN0FHGyYQ501+Mtmz97j5djDbi\r\n3dYw6NMn1QuaRhw+8gBggtYcJ2kIV23bAClDh5dI0PXlE4mRzTnyOihizWdz\r\n+GuVFFgCB1rtEwvOdznzNA7Sw7pWrHctHE475+j4o7FFcaFYRaHsO17ZEhGp\r\ne1cKQ7/EEA7pbzP9fijT0+GwMSct4IuqB/hFmx3GLWDImWNQis0Iu+fgvWpQ\r\nhBJp5mT1yiyZc3yeovqrLkuEJfgQUw7ucEhmDPrnzUg7rtTabvcT4A+Kzaqx\r\nJ7lx917V03Vd4Te1Q+TygqPWAsi2LtZcfyy+c7ILLJ39Bf2S4lqfDA4sr8TD\r\ndElpbEUaNqsUXcuUVDtYCUQcd7zTWYTmmU0=\r\n=mlEd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-create-regexp-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.22.1", "@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.19.0_1662404538701_0.22541588359602094", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.19.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "ec7455bab6cd8fb05c525a94876f435a48128888", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.19.1.tgz", "fileCount": 5, "integrity": "sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw==", "signatures": [{"sig": "MEYCIQDE5EQoc/8XgS+ckvUt9OrulnSGNH+0FGcNDjK29vDeVwIhAIKOd0a9LALqJD1MemzPsScefVl8oICcfYs/BqAfcV5W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBIw//U/is0AOBqwh76X9i6TIjlNQR4V3b0sjB5WEBhJgP28AyGI1M\r\n3OzXTKKd8+bQoCsitqeABD5FUSWuyXvwZzgVgydTy1o7RnEP/scu/W8bOIn8\r\n2UFVuqDTEc3BIATYP+pT7rp9xsgfuTkThGvJdLQdIZHc8SXh9NTb8dWt1Mo2\r\nnudV2rjCqpQiyklN8U890Jj3zGUCs/ZcTVo6ZkfzMg9OdymgT6shWs8YcQh1\r\nr5c3qLnn5WLUuHHD1BOdCqlhV1mf5LmWoc8To4hyhir5RqmttUK3lmQEpK7z\r\no6Y6DwBZbM5Cq+48uK2tydHVZKX8F3Gt139myEazNmYKRhpsJ4a3GexhzW02\r\ngq5tjUSGnH4E0O8queinAJX4Yy1xIwQjCAq9zLuR1imKhuO7JE8a8DQT878Z\r\nouD7PdtM5JqG0Y4/3agnbs6dpRLYyUQ3rekc/7Pw1OTLH+8H1Grl2cRLD41/\r\nFXYyfD6JKXMVkK1d6JhRTOvwGJjGlgewWzE7hRGpwUD/v/Xpptqjui14Agbu\r\nbvsLGvHlXI/81i4hhr6g/wFVAHYTCfLL7DCmHnxm+ghTLsAC0fpeH+93wGof\r\npF+XdX4op1UmwAhg8Lw2MJnKWRJm+DB9TIqlZ738w7obO07wo0WQ+5NSN7zP\r\nYFz4qEL30vPyONsQw59NpTMCb+FfIcnNE/4=\r\n=29VY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-create-regexp-features-plugin": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.25.1", "@babel/core": "^7.19.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.19.1_1663169351154_0.19557293087340688", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.20.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "626298dd62ea51d452c3be58b285d23195ba69a8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.20.5.tgz", "fileCount": 5, "integrity": "sha512-mOW4tTzi5iTLnw+78iEq3gr8Aoq4WNRGpmSlrogqaiCBoR1HFhpU4JkpQFOHfeYx3ReVIFWOQJS4aZBRvuZ6mA==", "signatures": [{"sig": "MEQCIFIK9uv/EocYYpVbK3NP/CVMqDazDB9vDpYgAl2VI5j1AiA6y5aydq5/ILzdYHmxJtf/qmin/STeLtqRodtdMSWKrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtgA/+K5jeDAhnOa9FWgDXanMCvcooyJaK2ow1e4BTja0bYbGTl6eC\r\n4JkJ2wvkgw4/sc3puIPxEfa433fQZpIla+hBkgK6uecQJrKBOxrGBU7nI/QY\r\nx/TtzTe4nGTSpNeprRKexShe9XYnDzWO67LVOD02+ll5J4K34ADxTv2jjzrG\r\niKc78yyrP/BbWEvl6t3AiwIj21CEXvuOLNpKBZP+vJo7G3G7tilVCW2pKVjr\r\nyX241JNgRRoDAS/NCPgf5kT8BWaKUh67NRlZO+Qz7VbEEZ9WTHx2aGwVU+hK\r\nH59X9ZtgT+3O3JB7E9uWTWLIm8ylFQzUk8RRI7gIs5veCnBcmLI2UbadJ2G9\r\nrVgvLyAPjTG2epfZypiDUStnWOu0Eq8ou6548kSuroeb/IkjYglvb5oiWbl5\r\n3rJNjL1Mm4WYnnPyYrHkITRNaHfaWYjw7F4M57Jh7dvXcagA72jlEBuQsMPC\r\nRqqGReoxaVH9Uzrk4VTDr64DiGjdJiwxQ5hosEX2Bo9F9uWrj+9F0+P/EQ0K\r\n2c8FNEsyaV5E2t/GnSPJf9hqDloEfAbODXUp8U3RJlL4Tuvw5jukU3IGXMzh\r\nRyZSwFuWbBGWEXXKlAY/MGqn/dYxuaEo1FZO49abcSBOtdNc61etMC0RfyD1\r\nrOsc4llrYZ4XQJ976DiMSv3y97G9hEpxoqc=\r\n=oSB9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-create-regexp-features-plugin": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.20.5_1669630368260_0.7808051030574787", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.21.4-esm", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4c34a653c6461c0bf9fe69600cb40be780d4b4d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-oWcgwdJ9ud20wchZgHEVHzMs2VCLsN6eBKz5tnciONZlyeIUCYUN82ehKOM+2pJJp/vAQSFv9qKlkEMnQpY8sw==", "signatures": [{"sig": "MEUCIQCMYcoL1ICuJ7lwhnSD8AbVEaAp9Xbstj8tfuHoRhf4RAIgJlArlH0P+HdwhU8gzpsnKDNrRmG+vlDs2lKVTcaKWRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqpQ//Vik5E88nJNWrgmtbkCo6N97CaXC8Mcu/eX31ZnKBqv6X8pnk\r\nRzDG3Oaozu7aHS9oxtZgNK4vZZIA5NUOoLBnrfjK1LjQNSb0Z+SYjoGQC4Yx\r\nTmX9wIvbEwSAw0GgYiXjPNdSTn5JSsDBcTP2f6x4v2ZI6YSvV8g3Veu6IOPY\r\nfGc3kYGl9/OsHeXwopfXOSUEPaUpmmnpSxmsklXazQ9pcXiCKLr368Oy2UU+\r\ng7YDKPvRM+mFt8Ii8MTIOgj87J7FSTh98JgwYcG2yO8gQPBZ6KfhHCWjU6a0\r\nsPu90SNIrDWKqqOIDWqw1+trjaCMcJABICRguoCnmZuoT8rckG+F+EEbEdp+\r\nynq7kO75bLGqjaxDCJsP4sjinS6NgNdowHReEDA0UrWD673tg2JNO67pdM8F\r\ntm27cN48UNB2OekrutE5cJ8WY9bgP2MBjwLS0uT05IuyZ5QDjnA8iC+SWC/0\r\nZUNCcch3pSlgtIboRqQO/+fIkXmBfA7brsN8XffzCgGZiArnPjXosH25zj9y\r\ncMHtI2FZ0YPaC2YVe3qefL/SWIfmWXa0W71/mXg1XAovNPNqcnmGDaYo/47a\r\nEb7jsI+u080cbUql2+WcxfbRnu6fMGLpAS098bonn7aRl8dROkjjPLClDzCQ\r\nvkV6dy/Z9aw/J/qz7cLPOr2hHN8ecKoD8ss=\r\n=IfrL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.21.4-esm_1680617389812_0.0583517571604697", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.21.4-esm.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "db9fe336a461e8d2c0459ade76afbdd18d5c99e7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-1bCO69j8iAn7pPHKVfU/WlN30J5ztTL6k2y250H9bpLvtVnjBB6XZ42JTS+vK1Ya/IX82YKgeVPrCA/xX1JuMA==", "signatures": [{"sig": "MEUCIAXCFZuJuZbv17cdwz0wrVWL78Nuhz6Xrw0n2VlkZBFdAiEAwYGFgWevpKpboPPd5X3wn1saUbg+xi77QuO6Zb4J+Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2gQ//SzQMB6dInHCz9VdLTnzNxwqsySTcwgFiwFP1Gyl/93Ye7RMl\r\nFqKuUe7uSr075IGOYOIaXKfOqjihCZ/pmfUL8N+MihN0cvWXKbAtSwQqmr9X\r\nCsQVJaxW5WtRruZTV8eV8a80w6KGuliRiMVjbTjJ1Kzq3b2/oBe72fhiyjEX\r\naBDaotXYeSgx8iRD3OpLHcAc1BRIktuX5YunkCQUekNW3CVSpjBB7WVmI87z\r\nQXiUxYh1C9S5Btn7fGz284lcdGXleMwKrFOsT1AgygNb6r3roChyr694KGiO\r\nFqReGFo4H5JGKdpy3HRhKEXUQ6ka9nolGhGKM9mXgH3gQtMfqMJeMCeCe/K9\r\n/mEoe1PzWyn71hQrHaSMeOflIWyHKe7sHJZ6ofBnzdU4emtUvqw1BYsojvFL\r\nKDqXWlPxYg22xJBxKLhBKalbz1Tgm2MITh569ANJlUYH+OEofTkVGkg1PeRn\r\nUL/CGc8bk+KuMvBcYnGWh3ekPY8bJREYAs3RHiEW+4EbrvsfoLftmqej1rl3\r\nMGWVa863yIBF9hfSNGvAlInz6EppIKxPgzUcw6amFFZAC8+JEjt/axgfhYha\r\nTDvs2KzBIOM9am8q9lCD/VgCXBsTJ70UnzdP3gZjNm8iNUv6xJ2gkFa7jP8l\r\n4/b2VDhhwr9zH6T5rnWN7IwYuY6AIdV2D8A=\r\n=nLZm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-create-regexp-features-plugin": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.21.4-esm.1_1680618107549_0.5764680285269741", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.21.4-esm.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f1f70ee634695f685bde13de04a87534858506e8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-miHvJ0EnFkYJQM8Tl4NOxUUFfCHKkxE+Mp77WpU/pP0DSXha/Xe8X9mLOJghsBTYGt0C3gildUm7WX85mAEG6g==", "signatures": [{"sig": "MEQCIGn6+vH8JIANLANWX7LlGBeGoU6kkt33P44hjrYMK0aDAiA8gChKn7r9IhieZ5RySYOfL6gGi8VXnfeWdHUP742bbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquJg/7BsQxSCxRjokwOT/yyGKVXLtcY0fkwsI6ySU3z8iRb57d3k/B\r\nlH39Ha35QUE7PhucmeONLd534oqTiQg2wYH56u9tTk56md240juzdguO6Nvw\r\n3imIIsL5hdCTfgilateTcUUrL5i4DS/jv6w3/T+sDRj7Syftij2IIBe3U8lK\r\nRpSo6umn3xiBhm+HgvmFzcB8T9TXZn1NOwBkBckz/553s95srtbTfYftsHUZ\r\n/qNuspo329BgjXEn6iz/QgrY60YIc12xOFbnhalSzXdo+OOFemczc1XdVvcK\r\n+dQ3KMS9eKHvhsC9IqTtCQI5wzQahAaueYDNeTz323C9KLalWXulZ0PuemG5\r\n2LYJgYCgfjTO0LRjODMEMPDH/7qy7xamSR+1gLX+m1fYqHCGwdQagj3NFBpc\r\nCjoPQ/ib7a+QVsFGvbqHyM1UHcjMuoS2ZPWzp2mK5C/msdYqQDKnrKUqvK+8\r\nKWHFlSRguBv/tqEvsxdriBZXPgSt0JOZF5+Oo8uCHbuZa8K2Pj0mRaGqGkIJ\r\nfdJxewznq6YGOgQblfH530MXb0nF8Gcrfqaf2Oqd6irdedlNRjO4oaP/zmLP\r\nfCdRcGzJOvD4nlXqt+9eSxWUYhFdFe4D94Kp5H12lBrD/wBBWrpojH5x1oUi\r\nPhYlDmJO7ozQ9bQf7Z+vRVsm19mhiU1py3w=\r\n=3cYS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.21.4-esm.2_1680619185496_0.8711032710527755", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.21.4-esm.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "00eb9a83087a7f2cb397e4c2fe274b3a66418ee8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-5mdt4tUufjWbhP+QAiNgpq+j0m+reOADcRhMQUvrIexsQeacf3/zvDs6aQzvoRG9/ZsIMcVVOMgDRNoaPXrgTA==", "signatures": [{"sig": "MEUCIQC6Snue1zhYk4nuE0nQOFSTJgPz8STcbp2sustersLq2wIgF6iitywJpYxy2xRR42XeO7fDXSCiiuCeJS3UQfj81u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoySw//fTla4CMF8vBUdOC00meaGEtf9PIME+fp805DIquXykrGExQO\r\nC9fZ4coTtna7NDNH2VQbeB+p2zHMRY/jG4aFXUxtgKlpCBS/r4QvKuAJLpdo\r\nHDrIMtnpfAnEJWhr76JSDf3JdnUyTAjLGeFI4yqMLxIXSgXdPSqqVwLSpqMH\r\npEUkCY2bHDDwy8h1ctq7xpP2ZkX6GJnavEnHdC7VLFpHnFHYgOCcku7WcnFF\r\noQexk3+Hmx6SetgjCjmn9jOrEtRSd9BGTRjGI+92Oi1vv19DE9ltEo3JZiLQ\r\nse1JsSpUhKCT/5/TuR9X3YlswbOWSKMtDXm6eGw9X2iHZkC1nRlQEkP73a9t\r\nEmwopbhpmmB5Bsi95GnMLNvF9WUoe4DQ3G/TjRBd8PXhVB7wQv3enC882gXK\r\nvPLFqQhfbuy1mF/OpKKhia8dsRcqOCMLB0kavMbW3Q59K7K9u7J/345zu0Kt\r\nveDArgXG1USo8cfyy91nHg6/tNHOI++JFnnHkEDFXLWOip+OomaLWymSJbWZ\r\nm29emM34IKR7fW6NM/UGHI1hiHEmW5FIqBy1n7ZUb/EzQsabe/RZpUpax7jy\r\nd3hoRepEKvUSKHGFeVa56/mqAtzce5jBj2cr7O/1hkxnnDSXdEStdS4ER9Dq\r\nsJz+KnuI14U5Imqq/xhqq7ldzB6c2le+ECE=\r\n=07oj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.21.4-esm.3_1680620193557_0.3515839448098743", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.21.4-esm.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a3017621c593412d4379757675668b6b04b2945f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-ohFZqoi1HfuZqNvgI9ITY86/uKKU9PBYwsRciewMjvBsvlMtJnEwHIcMCUF7gRJnRRb82LMZTYE8zxKbvW769Q==", "signatures": [{"sig": "MEUCIQDCyj/mMk3R/ldmSukqOCWHlzZItzJ4cHleM/RFBs5QrgIgRf6LPJ7Dbxq2qaFmfZyxg/3vr7aa4mhs3/cI8wRmHBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8PxAAj95vmH+WN+8Z5SJc3IGbawNlWQl+d4vgK6ymY+IO3/C+eYne\r\nA6gFSo0+zk6q573lluJb4BtT0SuCNkTy2g9kWz/3CWA1I71OYVFWJyUgp77R\r\n5dTjP5p0XE2ljIABRMUxnERACFK6WdfiNt27Ds2HYbN/3l8TJK+ybjOeG7qo\r\nHtlvkivrCB6DrcbQFT/+t2jKCR0w2hty0wS9/CkOO1B0yyDFC7dW4EYZafBb\r\njSkpZPUpfHWW7An/u058Zqzc31xvCOrms8+wlNQ3i9vTEGHDmYbq1ebfnkOU\r\nrzTsxxTFMDI4Byr0BpU6Tf0aKwwxg5kcuT4akmjI5o6SE07R4j3N/Y8Tb9MY\r\noQlRqVEjvnxswIv3HXpg2gf3Tfr7PooYxXV0muLJCWbpTLFF4ZwrbYw0BcUI\r\n0nJbuJYTZf6E9iQB3k32NqWvyy30h24Vm9FscmcxMUd8QtoC373SjkLEo0MR\r\niWE/bCzCekYbs/hpvPITLCeNlUCJlBOth3H7nI/vf8VJaSayiZ98rAc7axnr\r\nxBNR5/Uh0w3wYQtgLEdiZwURv7DqJR1mk5Md0+sY2Tj+fRAswsO6EjbyM1hU\r\nyhv8oYTZ9358JaXMx3kc7ZfkDPDiWV1GP55DH1XUMOQM193YrWO5zuQKXixr\r\nBqHNM7HNWXXRpZi+SOOUlhnkuprSapuhVuY=\r\n=pY5c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-create-regexp-features-plugin": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.26.0", "@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.21.4-esm.4_1680621223116_0.2487539993678709", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.22.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f5f9bf011ea70c9939596d4ed82c38071de4b4ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-3bIivRwjbaMFYuP8OypIlTbZK0SxW3j9VpVQX/Yj2q0wG6GqOG30Vgmo5X7QW3TGi3rxrdYpKuwxqfb5aCnJkA==", "signatures": [{"sig": "MEYCIQCl0m6cWV626+E4YnVZoFVcE3zOAw1qeJBLfdnT412KWgIhAN0/j8EzBN1odNht2yqrUoO3MzJyRZYWqZZQV8omDWwv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4886}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.22.0_1685108739861_0.9779660097772851", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.22.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "db6fb77e6b3b53ec3b8d370246f0b7cf67d35ab4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-c6HrD/LpUdNNJsISQZpds3TXvfYIAbo+efE9aWmY/PmSRD0agrJ9cPMt4BmArwUQ7ZymEWTFjTyp+yReLJZh0Q==", "signatures": [{"sig": "MEUCIQDhAN8g54v0X6P+JwZLZl+1iTNTQTg1z1vjmmo9nSBonAIgYGPUDUqEzzErQKRBiauNzmsMbcVKGvCcXuIJpF6C8Qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.22.3_1685182256638_0.7781587142715294", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.22.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "67fe18ee8ce02d57c855185e27e3dc959b2e991f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==", "signatures": [{"sig": "MEQCIFg/zu0kMAO1okdDSGvF3G9h4SCvV6Az6ejXNL4LFgrhAiAkGrcy9tm0GISCYlHZXYn68rSauSAgJeBOfyBoyRSSEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.22.5_1686248497601_0.7212485149046852", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "76c872a860e18c7af5e81d010a59da556a03a1a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-sQJtkj8oqO4ZHLCk6b/Hf/5QmWJ1x03X7p8f11CQ1zBZWoWHmT25c1qO/kqx6IjYNtmvokOzjnLV4pHshb2qkg==", "signatures": [{"sig": "MEYCIQCELKl5d8GDtRkeMUS0NnXPeZ0JReDNRhjgBugqRwxpTAIhALZBa6xteYK9lf8+RDzQsfgK9J0Ykp31R/k2tzJ+80NJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.0_1689861621806_0.6148389784579191", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9049a84ff434724166301e09d18a0ea7127b71ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-EeTMbqYKr2BggGYzU0WFrQG89aJMq8MyUgk9ORAEs+42ZPCVS2JOgjtyy0/DVPr+7PLlDgt/pIU3ZfGAHp9g3w==", "signatures": [{"sig": "MEYCIQDCAfs+OU31QluwvdU0Zi8CCqn+rV2QTNtRJxzlaUFE+AIhAO3/5WvSMSvOUgBACVPmITmrh3OXfx6PS3OeGeRi/AnR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.1_1690221175035_0.10804424848961225", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "0ef56e9700c4121abed108e197db8c8d54c7d3c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-0vs2t2C3Zx7s5GsC3YpO/TbAui/ZyXKJHgrHkFiedhI6BWGdJ9WUJwMTOXg9o6jBFM6GJj1eMXH70bBZQbnCSg==", "signatures": [{"sig": "MEYCIQCBxtLBvhO/sS42M9l+NzohMqvxRv2rEif2P6DLEyXMPQIhAPXUI1PcQ3AX1gMzCr6FJNY3W7VcErPxNpwe5Ks1YkUW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.2_1691594118131_0.17398482966019113", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "93f5120cead4e39fb2377112d9a50a78ed89f1b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-xuQv+2/f+G68kH9+of9P0NVjSkJtbVUqiJHKA2mPFBHrwBgBAnB8oU0F/ZNiumYB2/yULbZsmk+RRfMaNc+h4A==", "signatures": [{"sig": "MEQCIFxrAZk1Z1YdiTsWjkmaF22U/yUqLrZNhcNWv9DfqknCAiArrkSFwLc2JwFgVAbg2wK2ZKSJccOtw4bKKQUeCJkNiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.3_1695740250807_0.12429110116515418", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b49ea51c617a3affbf69ab1f9f76c38d7964f788", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-XTa8MoBhoAqjQilMUVSkWYi/N3O4ehTSE1YTK6HyrPba8Kx39nA7IGSBQA8L4W1dC4xm4rnSw3ZOJ1JCTKWr8g==", "signatures": [{"sig": "MEUCIFlXQ/9jJNCjZpjihrzVx39qiLqhY1kW7UyoLSItjgdXAiEAlMb30Wpol7Xd5HVh341T1IjTUDw9BP0fszM9Ppi9R8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.4_1697076404076_0.3322610704565725", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "69ce5e0df7c4f9dd3cda5a908cdbf432b7e7b813", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-pTUHOQ2TH4GOsB4O20JjALloxI4RBCXAwXXvMPfvONFNKiZCfPUehpEkYyr4XW9jwuO5d/EJWHwx5nlp3SvA5Q==", "signatures": [{"sig": "MEUCIHXPZN145AfYBxivDbuR1jfpJC2/nCkKo20JRGzq0lpWAiEA2njtlD+QjPu8xE6enqrfh1l0mZnUasSOfGGZjSi0KFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.5_1702307977680_0.24075411413539993", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "89743cd7c93e5062be52238ddb34daf306325257", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-c5KqYd2HLI8NOEqPf2jegTA8OO0gL0bxIdUfbDiEiEQ1epa87yxMXBmCORDVCBSlx3qgUpHj4d8Pj+QitPNa6Q==", "signatures": [{"sig": "MEUCICbVg1qJi0vfdU4f3TuIYSXnW4803ZOA8cv3VjzFyS9SAiEAhdLctYnZvLfpwtDFYNJRNuCt63x3AKT/elbh6Pty5FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.6_1706285676081_0.7111135709408067", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "907dfc95ceb792b3848208eecbcf74cb26afe91e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Fyd3uQqpNe0lYXJaK9bdgfyGgYd9/MvblsxwTjsWZ9mJOHptMvCXAVwmfwh6JBAeZckmfmWS/PxQuqzOGcCsLg==", "signatures": [{"sig": "MEUCIDIWbN4gFRJILS0n8ELLkI6taAPBe2Ss79zocE/MtN92AiEA/n8lqY/d8CwFMV5VCOGqwbOP1QofQVe7PJbcjrwi2so=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.7_1709129136749_0.5124888894225421", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "3cf21e61799bcc8518ae4d6366d9957923c35157", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-8i3Xs8P4Kzf24A6BvW/5fz1hg97HJQCCxpGN2YydSQHBhG8K2Ygkal3Y6bCAJq5saPxlsMEJQ3Iyt3wHOws9SA==", "signatures": [{"sig": "MEUCIQCZGKwBjt5o6ZtK9BGDdZTxi+xL6Bd+DT+q6PzQz0KG2QIgeHNKxQb9H844lhhHe9/Ds0rfaJV14RC5K9oK2vTEXi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4713}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.8_1712236814970_0.9555395020660691", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.24.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "352ee2861ab8705320029f80238cf26a92ba65d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-6DneiCiu91wm3YiNIGDWZsl6GfTTbspuj/toTEqLh9d4cx50UIzSdg+T96p8DuT7aJOBRhFyaE9ZvTHkXrXr6Q==", "signatures": [{"sig": "MEUCIQCBhcs/jR9M+PtHqnJCH7EzGKli9c8fT0gw66UXf8HDqwIgOvOExm7+Ztcgpe<PERSON>+/RzXHY8J/iGw55E5FLYp8AqHfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71231}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-regexp-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.24.6_1716553505433_0.017370053251606565", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "c0e43d52f615fd08151a901b1cac7a6c17222f82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-2IeWQMwi0uuWZE9eg4bjxxASTVjbuPDsIqmroaubLHAkD+coLOuEPuOcz4fof97ZAljem+poo4LmGMI9UC/CKw==", "signatures": [{"sig": "MEYCIQD0ipuU9hUcc2wrRaXdMpDL/XBbnXPwkmJmMQKxOPnj+gIhAL5oRnfJrzdp7CKumjS47Fs1tT+0pYObDlQQ5ZkH25LQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71424}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.9_1717423490713_0.0736334638942664", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "29c7db7ffad280f60f72f029d54aaf11baaec5ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-ztiSYHUxE3FfT19g7bFKt/XJTIS0nFKjW3DXvnf72piLhV0f9blc64V0RdMvd9mfh1uck4k6L5J21kQJJMxAdw==", "signatures": [{"sig": "MEUCICQ8qY5AhrFpGYUkc73ZkUNBiEjmcyfhdKKPsRZf8xj9AiEA8sb0dWFunv2FSYZoowWllOf+AXYa9QsAl7F6XFUcGg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71430}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.10_1717500026870_0.9928974465247973", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.24.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9042e9b856bc6b3688c0c2e4060e9e10b1460923", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-/jr7h/EWeJtk1U/uz2jlsCioHkZk1JJZVcc8oQsJ1dUlaJD83f4/6Zeh2aHt9BIFokHIsSeDfhUmju0+1GPd6g==", "signatures": [{"sig": "MEQCIDfliTTe1RCotpVCe57SB+8ebPeaOu6HgG6GT4t5+ph6AiAQiuwne2pv/iR4/t6zFPvVWTlFmcwWrvFWqALn8rzd1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71227}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-regexp-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.24.7_1717593341137_0.2911790914293", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "45348415a09c479aeac9dd242d3f4c6575dc6d4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-wT4GhQv5rn2dAGE3s1eEytmi84Qh0JjHRJJok8n0G4C5BBBhk8P1iCzmfzsPCilimRYaxfiZpI/W3orF0Jkcyw==", "signatures": [{"sig": "MEYCIQDtEnKP/hdhOBHSUdZPdqTwRWfj0TdBhoar+pHiBRUDUAIhAPtPJEzydwrEIkdK1ZWRLZOzvVwCEXIYadGBOmkd/3Qq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71319}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.11_1717751751863_0.16720020925429124", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5e62f6a9133abc4c77b7a17401bc02e416b01c66", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-vEAVq9LNjxywqKiy8H5WUFYdgMt35q+kcoWP+UlLnjh9j/0o2OEWi14HJ4rUAEllcIbzOHoiN5N2U4qevJiVtQ==", "signatures": [{"sig": "MEUCIGVc/h9fMiii4UyqAlDGf6xDkGpJ97S+BDsvzN4CFg+LAiEAnwMlICHNPAxcbXT3bqLAOHQl32rd6jlgdDqtYJBaFvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68094}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.12_1722015225726_0.9892854720194262", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.25.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a2f3f6d7f38693b462542951748f0a72a34d196d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-BtAT9LzCISKG3Dsdw5uso4oV1+v2NlVXIIomKJgQybotJY3OwCwJmkongjHgwGKoZXd0qG5UZ12JUlDQ07W6Ow==", "signatures": [{"sig": "MEUCIQCZhjxN9wNqzYmGSl8nrOcrAvi6k69eVYa6QFRBOUssyAIgW9pqZHozUoMreVVYG7wpn1DVnK1SZfEKugXvimrVWxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75748}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.25.7_1727882112318_0.11232503037281916", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.25.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "454990ae6cc22fd2a0fa60b3a2c6f63a38064e6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-oqB6WHdKTGl3q/ItQhpLSnWWOpjUJLsOCLVyeFgeTktkBSCiurvPOsyt93gibI9CmuKvTUEtWmG5VhZD+5T/KA==", "signatures": [{"sig": "MEUCIQC5Tv+glB56pBCAJxZVYXcmRWlDFI5nH4JkSWTl5JjCnQIgZZAjbNV5fqrKNtZMI2LcE/pKaCglnfJGsWN6av4nS7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4858}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.25.9_1729610487775_0.15284932101180826", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "932a5556217113807da453f00a2518d95f61bfbb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-WmI7e0pZJUYdXsCu67cKuoAIKOJMm026sIAky4dJJngI0qanbfbR/8DrUsdbHMrJFGqCWEHZ+04x9dIHo7F0zQ==", "signatures": [{"sig": "MEQCIG5HQSWTBQ3zivX0mr+71yWxVzagHAZVcjWDpwJN8k5MAiB88cHK7+cL3AwrVwJygax/o/LhikLPgDXyqyebovwsXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.13_1729864468302_0.8811050275989749", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "1cad8fceaeb6fcc7d57fb9a55c58729897d5f0ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-uZRodgPCOmIheCuvpeEc1DqKTDF9g7Yd5iS8p8tbMul2R7j+vP1EZ9zdb/qhj0fKLR5b2U/WQQq1TSetu8Txtg==", "signatures": [{"sig": "MEUCIADCUsUmDZ1vJ25uoFqLA93V8fpUeCjYth2PzMjAVdtvAiEAmSJHb9BJtlB0G0Vi+29+6OzImHSGbbqMkr/GEyHmdkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.14_1733504058407_0.44484063890609304", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "144219990f776e988baaf1fbcb9f311a4b4a055c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-HYJTqx7kPfHwYl8SnkNij6CADrLLUmnmwVbkbJ/q9KtnkQOe+xWSCMBxzmOeZuQZxoxEgBLWpuIYfLDV7o/1Mw==", "signatures": [{"sig": "MEYCIQCWM+j1Oe3WRnmjaVIG7oNBiBb6OLQ5eGUMJokm0idhfQIhAJmxdDQaLfNLI2aD3H1OwuC3/htiHWdPY4ld0NWvAZ9R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.15_1736529885757_0.5469476625618781", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "321bebcb4b3263fb4fd1025333a38269d9d401d8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-CHgQ4AS4SL6zNavUOCZosKAy6EFQVwC7PTiRrdU/S0WqG6iC30kXK8Mr8UhZlnkk/1HtWqvc9GrPGitxqu4PTA==", "signatures": [{"sig": "MEQCIEd5uyUu3V+Q99cmU3fbCaKD6Dsdw3NTCYhTweNRmiFzAiAYn0dKgFvTxxZrFX1xPS8GULYrSujiQ3RaqNzhcCR2hw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5074}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.16_1739534361962_0.8645173306202965", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "b1cec5691bf6e58204587404e3c3904c8557500f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-19SyOj/X9iCUPxDZWBZkb+XDtCckePkt9Wt8TsSzNrE4hkCaLY79r9JtyBKUrzoABzPjIuNy2tmRecZ9FyQ21Q==", "signatures": [{"sig": "MEMCIBJNU03NfdP4Z1U1NfKff046ZlJXErJ7yZ0nipHw2Qw6Ah86fhOBfsMGWy1queRT1QAJA7RXYr0fZNlIIf6QIjKe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5074}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-alpha.17_1741717515027_0.9679362051295235", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f32b8f7818d8fc0cc46ee20a8ef75f071af976e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==", "signatures": [{"sig": "MEUCIE0mqusdc1FwZP8g56Xek9meO5hybGnI0XB2em+Ld0X/AiEA6hiIAG/NGlkmXhTcR7sChYkNdxWZdO5Wd+EkvILPdd8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4858}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_7.27.1_1746025751219_0.5435419668600301", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f59542d4646eb7dd8d24c484bd9a222a90f2cd60", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-PlRDEanbxUpJnp1faSLENX7GiID8ca0hBEcTvHpsvSaoH2UOp/3wH9zXWzci6vQRziSWR27iHF3VTEVHMVSZtw==", "signatures": [{"sig": "MEQCIAdUcWymq+g4pkXo8arSjysCGc8d/rQsCi15CSgsXO9EAiAhmxN2gxk8HPFTHzm/MM8OSt4R2+rc97mVsSJ6FlYb+Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5051}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.30.2", "@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-beta.0_1748620287899_0.053104492064395714", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-named-capturing-groups-regex", "version": "8.0.0-beta.1", "description": "Compile regular expressions using named groups to ES5.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "core-js": "^3.30.2"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-named-capturing-groups-regex@8.0.0-beta.1", "dist": {"shasum": "ddde68abeae66c216c0715beef13650e000f23f9", "integrity": "sha512-B44V0Zrn8VrvDtOCxy1Ul+Wd8RxIphyVKjMTTE2CqJL11oaHMZJbPy5ceQCVA9rSn9VObGilpsPTAjzSD8u9Jw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5051, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB7P+iMuQ7v9ldhVYv3g5V+AI0RZurl2d4X3fYlG6OhBAiBB8YBqohoDiZKxL27snH4tyh2hon83E7xWyDfM3LxE7g=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-named-capturing-groups-regex_8.0.0-beta.1_1751447072251_0.6846064863152033"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-01-21T21:32:23.418Z", "modified": "2025-07-02T09:04:32.627Z", "7.3.0": "2019-01-21T21:32:23.697Z", "7.4.2": "2019-03-21T10:17:24.121Z", "7.4.4": "2019-04-26T21:03:58.488Z", "7.4.5": "2019-05-21T17:45:35.793Z", "7.6.0": "2019-09-06T17:33:37.612Z", "7.6.2": "2019-09-23T21:21:34.766Z", "7.6.3": "2019-10-08T19:49:38.401Z", "7.7.0": "2019-11-05T10:53:21.182Z", "7.7.4": "2019-11-22T23:32:33.212Z", "7.8.0": "2020-01-12T00:16:51.499Z", "7.8.3": "2020-01-13T21:42:11.640Z", "7.10.3": "2020-06-19T20:54:08.057Z", "7.10.4": "2020-06-30T13:12:45.517Z", "7.12.1": "2020-10-15T22:41:17.354Z", "7.12.13": "2021-02-03T01:11:37.808Z", "7.14.5": "2021-06-09T23:12:55.660Z", "7.14.7": "2021-06-21T21:54:04.829Z", "7.14.9": "2021-08-01T07:53:15.183Z", "7.16.0": "2021-10-29T23:47:50.924Z", "7.16.5": "2021-12-13T22:27:33.953Z", "7.16.7": "2021-12-31T00:22:45.324Z", "7.16.8": "2022-01-10T21:18:24.964Z", "7.17.10": "2022-04-29T16:37:38.282Z", "7.17.12": "2022-05-16T19:32:57.324Z", "7.18.6": "2022-06-27T19:50:26.272Z", "7.19.0": "2022-09-05T19:02:18.825Z", "7.19.1": "2022-09-14T15:29:11.319Z", "7.20.5": "2022-11-28T10:12:48.422Z", "7.21.4-esm": "2023-04-04T14:09:49.992Z", "7.21.4-esm.1": "2023-04-04T14:21:47.724Z", "7.21.4-esm.2": "2023-04-04T14:39:45.631Z", "7.21.4-esm.3": "2023-04-04T14:56:33.682Z", "7.21.4-esm.4": "2023-04-04T15:13:43.298Z", "7.22.0": "2023-05-26T13:45:40.089Z", "7.22.3": "2023-05-27T10:10:56.772Z", "7.22.5": "2023-06-08T18:21:37.774Z", "8.0.0-alpha.0": "2023-07-20T14:00:22.029Z", "8.0.0-alpha.1": "2023-07-24T17:52:55.156Z", "8.0.0-alpha.2": "2023-08-09T15:15:18.285Z", "8.0.0-alpha.3": "2023-09-26T14:57:31.024Z", "8.0.0-alpha.4": "2023-10-12T02:06:44.307Z", "8.0.0-alpha.5": "2023-12-11T15:19:37.878Z", "8.0.0-alpha.6": "2024-01-26T16:14:36.265Z", "8.0.0-alpha.7": "2024-02-28T14:05:36.983Z", "8.0.0-alpha.8": "2024-04-04T13:20:15.150Z", "7.24.6": "2024-05-24T12:25:05.605Z", "8.0.0-alpha.9": "2024-06-03T14:04:50.841Z", "8.0.0-alpha.10": "2024-06-04T11:20:27.054Z", "7.24.7": "2024-06-05T13:15:41.328Z", "8.0.0-alpha.11": "2024-06-07T09:15:52.010Z", "8.0.0-alpha.12": "2024-07-26T17:33:46.039Z", "7.25.7": "2024-10-02T15:15:12.515Z", "7.25.9": "2024-10-22T15:21:27.996Z", "8.0.0-alpha.13": "2024-10-25T13:54:28.511Z", "8.0.0-alpha.14": "2024-12-06T16:54:18.570Z", "8.0.0-alpha.15": "2025-01-10T17:24:45.938Z", "8.0.0-alpha.16": "2025-02-14T11:59:22.137Z", "8.0.0-alpha.17": "2025-03-11T18:25:15.197Z", "7.27.1": "2025-04-30T15:09:11.416Z", "8.0.0-beta.0": "2025-05-30T15:51:28.065Z", "8.0.0-beta.1": "2025-07-02T09:04:32.412Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "description": "Compile regular expressions using named groups to ES5.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}