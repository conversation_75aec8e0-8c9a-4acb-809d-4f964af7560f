{"_id": "bser", "_rev": "14-32ad82bdf16f510c9d1ce8a119ad4a06", "name": "bser", "description": "JavaScript implementation of the BSER Binary Serialization", "dist-tags": {"latest": "2.1.1"}, "versions": {"1.0.0": {"name": "bser", "version": "1.0.0", "description": "JavaScript implementation of the BSER Binary Protocol", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "https://github.com/facebook/bser.js"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "wez"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/facebook/bser.js/issues"}, "homepage": "https://github.com/facebook/bser.js", "dependencies": {"node-int64": "^0.4.0"}, "gitHead": "57f16766487a5d6976aa8105afa25fcc1bf27e35", "_id": "bser@1.0.0", "_shasum": "380ce5c7c2efe37723eb6cbe0a4ad871e7119d01", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "amasad", "email": "<EMAIL>"}, "maintainers": [{"name": "amasad", "email": "<EMAIL>"}], "dist": {"shasum": "380ce5c7c2efe37723eb6cbe0a4ad871e7119d01", "tarball": "https://registry.npmjs.org/bser/-/bser-1.0.0.tgz", "integrity": "sha512-En9IdPCGaRdc5sU6pZsWHGzLN8I2Wfe3lywSgnoFCDrufEEVNCNFge24mTXgbLTCtL+6VBvV/QP1zjUm7KA5zA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIaHmfU5PfgvJsoy6ppmhUnzCu3uQA/ks/53hjyTmUZgIhAJ8mtVO96bqKrTowQRMdU0JyE31SnyjSLlcGV0c2Gdyv"}]}}, "1.0.1": {"name": "bser", "version": "1.0.1", "description": "JavaScript implementation of the BSER Binary Protocol", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "https://github.com/facebook/watchman"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@1.0.1", "_shasum": "fbc65cfcb03c19d39fef47360c41fc0799615133", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "amasad", "email": "<EMAIL>"}, "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "fbc65cfcb03c19d39fef47360c41fc0799615133", "tarball": "https://registry.npmjs.org/bser/-/bser-1.0.1.tgz", "integrity": "sha512-aYRGblHO/pLqRuWAaJiVNIBYTN31m26l2eye1/oCKUYjTeHDbsyaUIiuPz1LC2JNrqZTNQSfE1gUb3HsfNmYIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ9yS957Y6s84F2NftGKOwsMyRPqOvreK5ks9I5frnfgIhAOwvlHKLIbEBK5kuySez0gDIyQ+7YHztxJ/I4CCgxwgQ"}]}}, "1.0.2": {"name": "bser", "version": "1.0.2", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/watchman.git"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@1.0.2", "_shasum": "381116970b2a6deea5646dd15dd7278444b56169", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "381116970b2a6deea5646dd15dd7278444b56169", "tarball": "https://registry.npmjs.org/bser/-/bser-1.0.2.tgz", "integrity": "sha512-kKi2swDowbCsnwsYyJnMkz3N1utuJfnWcvzxVX45nWuumTNEkig97rvLVN60+8OWgAWuJdIyEfTPTZqyPoklwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMTOkJ7MeILKj/QEYi7gTwMoMoNvC2/LaB2B7gm59ZtAIgdzS9aD8JPQIpXfcRngwj9iMRfd0o2/WAXykk5yhepBA="}]}}, "1.0.3": {"name": "bser", "version": "1.0.3", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/facebook/watchman.git"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@1.0.3", "_shasum": "d63da19ee17330a0e260d2a34422b21a89520317", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "wez", "email": "<EMAIL>"}], "dist": {"shasum": "d63da19ee17330a0e260d2a34422b21a89520317", "tarball": "https://registry.npmjs.org/bser/-/bser-1.0.3.tgz", "integrity": "sha512-Iv8lCNn0DhAc8FfECbqgbVWb1NbXisVnLaQruoWZf9KGUED3/Lbc1aUuTJsOli1aQqtQO98AaLscGOapsWtlBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/U+n3zZzD85gWsommtQ5tutvPwlZlN9HH4QhyUobkJwIgNyf6YcrWNjRpynX71VgnCKcukEXYcRDhsl14n333XNg="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/bser-1.0.3.tgz_1485476024813_0.196590629639104"}}, "2.0.0": {"name": "bser", "version": "2.0.0", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node test/bser.js"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/facebook/watchman.git"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@2.0.0", "_shasum": "9ac78d3ed5d915804fd87acb158bc797147a1719", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "dist": {"shasum": "9ac78d3ed5d915804fd87acb158bc797147a1719", "tarball": "https://registry.npmjs.org/bser/-/bser-2.0.0.tgz", "integrity": "sha512-FozP+z0rEpi3AywbeT1QnOrGFJDbC0986aFDR2NlNLF+/WEYdv/7/qb1FVtla+KBWswkQBOA7okWd+85ThWlCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9loftMksIAeK18JumXS6ARLLRqro77RB/kXAOiJQCoAIhAMtBhluniYQVHS9ywqeVzBOiZ+luMWj6vqsrDTOIsNql"}]}, "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "wez", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/bser-2.0.0.tgz_1485803232935_0.3270201792474836"}}, "2.1.0": {"name": "bser", "version": "2.1.0", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "https://github.com/facebook/watchman"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@2.1.0", "dist": {"shasum": "65fc784bf7f87c009b973c12db6546902fa9c7b5", "integrity": "sha512-8zsjWrQkkBoLK6uxASk1nJ2SKv97ltiGDo6A3wA0/yRPz+CwmEyDo0hUrhIuukG2JHpAl3bvFIixw2/3Hi0DOg==", "tarball": "https://registry.npmjs.org/bser/-/bser-2.1.0.tgz", "fileCount": 4, "unpackedSize": 17978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDSDECRA9TVsSAnZWagAAxqQP/js6dItvl0aldWHE1Mib\n9sE14e9Q4LAHsJAtSOAWtGrWItrxnuEG3vZ9DAmgdczaM8JhaW2zeudL/6I+\n4f4bJ1ocWGzfmlLTNhzzVaYD1HwxudIK4V6Y6PMtgBP148cSrb/D1sdezxk1\nnq0lrnXHccmgGZUEHRUfmfQ6xZHByQZuCPX32VoGQafXJrJNGjugKK9iat02\nK8N+9+hOgEMqijXJviAm+uLvxuVhACaFELgr/lJ4j22nzqj/MISVO69TxaRL\nuelbOOACAx2DatSIgOFc/g7rYnbuTBZiw7z+bRcRAg2qWIZzpnMImAEftHaa\nsBVwNpa0SnjDtRNHUJjyBEiqwbKcxhz55Gz74KTI9O5NBilSSfuqd2L/78vk\ncDej6zhwaM48PV7i7vAekWwe4vlcJ5Pn+58FBc1NgSa/BI3FiaDGHmieJDiG\nsE6oX6hcFwt/jz8ZhKTaZ1MhfSVesyte17XtPPf+qpbfOkZQXCD4sjCTow0K\n8cICXHXIBDng0zCSt88IjtsAcOZMavYj3dn2yIl3T01jlUZxVSIrJshnWcxK\nwgqmdX7F80y9Ky9tagq1+YypEx9SNsECG+VEk0JmkTN8GdYgtXFAZ4KtD6+T\nhyP77AbGq2JF1rtWD2+Cb98oaUL+Ho1jvvAqlI2qI91E9ZM78XLikG4HPADc\nEQyn\r\n=fXP4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQN+uYybtYOEBfSg5RoX5fJTu+UDtuQSR4IpmOJispAQIhANzuRS1sbAVJdqrvb2Mrc+n/8uFc5DqDhqrqI2tFcGeh"}]}, "maintainers": [{"email": "<EMAIL>", "name": "amasad"}, {"email": "<EMAIL>", "name": "kassens"}, {"email": "<EMAIL>", "name": "wez"}], "_npmUser": {"name": "kassens", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bser_2.1.0_1561141444249_0.7999954163788061"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "bser", "version": "2.1.1", "description": "JavaScript implementation of the BSER Binary Serialization", "main": "index.js", "scripts": {"test": "node test/bser.js"}, "repository": {"type": "git", "url": "https://github.com/facebook/watchman"}, "keywords": ["bser", "binary", "protocol"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "dependencies": {"node-int64": "^0.4.0"}, "_id": "bser@2.1.1", "dist": {"shasum": "e6787da20ece9d07998533cfd9de6f5c38f4bc05", "integrity": "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==", "tarball": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "fileCount": 4, "unpackedSize": 17986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdryw/CRA9TVsSAnZWagAATkwP/RREEY8DFYdeeOckCeKx\nmtL1wHI2GyGSo9Cf7EkM7FINX3F2XjCFR6RikQ2aL1b3isuVLyaNsqxSePci\ngZgBp9zjKoC38v8SROFDVhxumOqsey+0GPLie67BdACCOIJaZkVxDNzk3xB5\nJp9PRhfnB0eUHI8ZZtupqnrsv9w5FAiW72ZlAo4i6jt1XY6iB6kuLFVPHBWs\nVctbxuiamP1A/ouQ8lecdXZMxuQLUi4jkCNML29X3uT/soswzIsd24W2jqTU\n2bl9mzNNGs/6+C8NsyPLYz9M0lNqfR071bLhoIjkwFMan62IZG3rRVHry+3J\nihCZU+8ZclTZRAPfHyedgYxges/YLr8JJODHfH/0UkFJvl/oEmaZ9+wqDUoB\n1rrqzF5YjA4oXUKv8kR0nvTuhqUuHqkDLmlfbmQMaRitArWiKH87FeSSnbPf\nnaOqWRds+DJXf6sCmaiKaS8+noCtipMp+lo2VyLzrhhpgNT3hUHADjJXfh2s\nJdva8DHJUfX9vhHMGaolrZmjJNKvWJa5pWUVQD9KiySdct6TcjAoGyEfoAJt\n5DuAw7jJxCBNoWoMEvL2n5uuVHx1eph8qHdhhs+nC1ldqr/eZZq3QmJpnVh5\ngOnX2z3wAPH6rtC5P3P8YW2kqLgkCDnv3slc+IJ2GeV2EwakrEAdRx2RCUL/\nQLgc\r\n=i+OA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0u9CfJ4VoS4Mp84tRbnPyiG+Uqjb9S81OjtLgdjyhCwIgIy6rjXrWhIz6w/synGjoL8rl2ux2crPb+s7o3Z4jT40="}]}, "maintainers": [{"email": "<EMAIL>", "name": "amasad"}, {"email": "<EMAIL>", "name": "kassens"}, {"email": "<EMAIL>", "name": "wez"}], "_npmUser": {"name": "wez", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bser_2.1.1_1571761214821_0.21502650063663964"}, "_hasShrinkwrap": false}}, "readme": "# BSER Binary Serialization\n\nBSER is a binary serialization scheme that can be used as an alternative to JSON.\nBSER uses a framed encoding that makes it simpler to use to stream a sequence of\nencoded values.\n\nIt is intended to be used for local-IPC only and strings are represented as binary\nwith no specific encoding; this matches the convention employed by most operating\nsystem filename storage.\n\nFor more details about the serialization scheme see\n[<PERSON>man's docs](https://facebook.github.io/watchman/docs/bser.html).\n\n## API\n\n```js\nvar bser = require('bser');\n```\n\n### bser.loadFromBuffer\n\nThe is the synchronous decoder; given an input string or buffer,\ndecodes a single value and returns it.  Throws an error if the\ninput is invalid.\n\n```js\nvar obj = bser.loadFromBuffer(buf);\n```\n\n### bser.dumpToBuffer\n\nSynchronously encodes a value as BSER.\n\n```js\nvar encoded = bser.dumpToBuffer(['hello']);\nconsole.log(bser.loadFromBuffer(encoded)); // ['hello']\n```\n\n### BunserBuf\n\nThe asynchronous decoder API is implemented in the BunserBuf object.\nYou may incrementally append data to this object and it will emit the\ndecoded values via its `value` event.\n\n```js\nvar bunser = new bser.BunserBuf();\n\nbunser.on('value', function(obj) {\n  console.log(obj);\n});\n```\n\nThen in your socket `data` event:\n\n```js\nbunser.append(buf);\n```\n\n## Example\n\nRead BSER from socket:\n\n```js\nvar bunser = new bser.BunserBuf();\n\nbunser.on('value', function(obj) {\n  console.log('data from socket', obj);\n});\n\nvar socket = net.connect('/socket');\n\nsocket.on('data', function(buf) {\n  bunser.append(buf);\n});\n```\n\nWrite BSER to socket:\n\n```js\nsocket.write(bser.dumpToBuffer(obj));\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "wez"}, {"email": "<EMAIL>", "name": "kassens"}], "time": {"modified": "2022-09-21T18:41:20.012Z", "created": "2015-08-24T06:31:54.849Z", "1.0.0": "2015-08-24T06:31:54.849Z", "1.0.1": "2015-08-24T21:50:47.585Z", "1.0.2": "2015-08-25T16:05:06.492Z", "1.0.3": "2017-01-27T00:13:45.058Z", "2.0.0": "2017-01-30T19:07:14.690Z", "2.1.0": "2019-06-21T18:24:04.477Z", "2.1.1": "2019-10-22T16:20:14.968Z"}, "homepage": "https://facebook.github.io/watchman/docs/bser.html", "keywords": ["bser", "binary", "protocol"], "repository": {"type": "git", "url": "https://github.com/facebook/watchman"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md", "users": {"chinawolf_wyp": true}}