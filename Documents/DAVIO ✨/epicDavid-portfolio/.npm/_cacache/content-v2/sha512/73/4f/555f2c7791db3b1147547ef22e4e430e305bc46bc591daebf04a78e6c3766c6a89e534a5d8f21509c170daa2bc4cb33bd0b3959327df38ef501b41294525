{"_id": "void-elements", "_rev": "27-e02d84c211000c1f125039323a58690c", "name": "void-elements", "dist-tags": {"latest": "3.1.0"}, "versions": {"0.1.0": {"name": "void-elements", "version": "0.1.0", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@0.1.0", "maintainers": [{"name": "hemanth", "email": "<EMAIL>"}], "homepage": "https://github.com/hemanth/void-elements", "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "dist": {"shasum": "74c1c54fd3c0dd6d80dafa6bf7b866d3da0c947a", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-0.1.0.tgz", "integrity": "sha512-uTCghpSmr0RJA0pHpC0SuAvEFp13za+P3pkSxbT1lJuSPBI2YTmeuBB7JtZ/EpUbN8nSwerKihM1U8iaYlX0+w==", "signatures": [{"sig": "MEUCIQDhm9DcyCqYotv6jJgYYM3ulgPjgsnKMQgc30o11fYvrQIgG9QqPGa8Kgao3844EcQozQJYdQZqHZTHflLKA5E6mEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "74c1c54fd3c0dd6d80dafa6bf7b866d3da0c947a", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test.js", "preinstall": "node pre-install.js > index.js"}, "_npmUser": {"name": "hemanth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hemanth/void-elements", "type": "git"}, "_npmVersion": "1.4.9", "description": "Array of \"void elements\" defined by the HTML specification.", "dependencies": {"cheerio": "^0.17.0", "superagent": "^0.18.2"}, "directories": {}}, "0.1.1": {"name": "void-elements", "version": "0.1.1", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@0.1.1", "maintainers": [{"name": "hemanth", "email": "<EMAIL>"}], "homepage": "https://github.com/hemanth/void-elements", "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "dist": {"shasum": "21677450545ac2337c69f5ec8f91092eda4d3473", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-0.1.1.tgz", "integrity": "sha512-mf/nB/Itnyms9Vhtfktj2ki77pXXkV65KnOgBhLBr35pisK7+s68MDffsjZ/1TlE3bPja+eIeVvy1eOmums/Ow==", "signatures": [{"sig": "MEQCIE+8BZzopw9AZ6me1BiDAe4Mo+9fupoCL1segSOyyOhAAiA0A4Lc6zT32pBsRFhdfJf87nxqB8x9cYuzGmpHHrMVsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "21677450545ac2337c69f5ec8f91092eda4d3473", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test.js", "preinstall": "npm install cheerio superagent && node pre-install.js > index.js"}, "_npmUser": {"name": "hemanth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hemanth/void-elements", "type": "git"}, "_npmVersion": "1.4.9", "description": "Array of \"void elements\" defined by the HTML specification.", "dependencies": {"cheerio": "^0.17.0", "superagent": "^0.18.2"}, "directories": {}}, "1.0.0": {"name": "void-elements", "version": "1.0.0", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@1.0.0", "maintainers": [{"name": "hemanth", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hemanth/void-elements", "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "dist": {"shasum": "6e5db1e35d591f5ac690ce1a340f793a817b2c2a", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-1.0.0.tgz", "integrity": "sha512-Ypv2nBd59g63ouzrZaojQXHdRbnLhT1uuZtEXISmymV3MSiA5Qdwn4YxDlhBZoCYTninng14Y2J7pSrg9zq1/g==", "signatures": [{"sig": "MEUCIQCZcWQ3Rte+9pw0hCM6vAUAAE+BGgEz0rt3TGxl1sCMSwIgIAMjO5Dq4lHU7g4AaBa59z2Zdy8VqLihIw/RYkFIoAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6e5db1e35d591f5ac690ce1a340f793a817b2c2a", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test", "prepublish": "node pre-publish.js > index.js"}, "_npmUser": {"name": "hemanth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hemanth/void-elements", "type": "git"}, "_npmVersion": "1.4.9", "description": "Array of \"void elements\" defined by the HTML specification.", "devDependencies": {"cheerio": "^0.17.0", "superagent": "^0.18.2"}, "directories": {}}, "2.0.0": {"name": "void-elements", "version": "2.0.0", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@2.0.0", "maintainers": [{"name": "hemanth", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hemanth/void-elements", "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "dist": {"shasum": "321d42028213a45b02b0c655d7dd1ea205dcfda2", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-2.0.0.tgz", "integrity": "sha512-RSmkREXdnISthsNR5aEbbhGI5YKiCf3rEVMTvAez+/wUub7+1jlAEnX4P1YtfgA7mOBEpzAXuDirKr4lRvKRYw==", "signatures": [{"sig": "MEYCIQDGuXNoboDIEA+aAeOad5jrTwlX6onV8LojubopiRcbEQIhALn9elCxtEj/xsEyY0cxAIljc0kxdTwfwgJBGVnmawq/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test", "prepublish": "node pre-publish.js > index.js"}, "_npmUser": {"name": "hemanth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hemanth/void-elements", "type": "git"}, "_npmVersion": "1.3.11", "description": "Array of \"void elements\" defined by the HTML specification.", "devDependencies": {"cheerio": "^0.18.0"}, "directories": {}}, "2.0.1": {"name": "void-elements", "version": "2.0.1", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@2.0.1", "maintainers": [{"name": "hemanth", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/hemanth/void-elements", "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "dist": {"shasum": "c066afb582bb1cb4128d60ea92392e94d5e9dbec", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz", "integrity": "sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==", "signatures": [{"sig": "MEQCIBikFxrJ+m7kIxtORjozIeRTMzUttduSNjnlU68lH4g4AiBP20oxMGieJI/JVDbW13oUFqu+IsY/uRAkFoKvYyMd8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test", "prepublish": "node pre-publish.js > index.js"}, "_npmUser": {"name": "hemanth", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/hemanth/void-elements", "type": "git"}, "_npmVersion": "1.3.11", "description": "Array of \"void elements\" defined by the HTML specification.", "devDependencies": {"cheerio": "^0.18.0"}, "directories": {}}, "3.1.0": {"name": "void-elements", "version": "3.1.0", "keywords": ["html", "void", "elements"], "author": {"name": "hemanth.hm"}, "license": "MIT", "_id": "void-elements@3.1.0", "maintainers": [{"name": "alubbe", "email": "<EMAIL>"}, {"name": "bloodyowl", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hemanth", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jadejs/void-elements", "bugs": {"url": "https://github.com/jadejs/void-elements/issues"}, "dist": {"shasum": "614f7fbf8d801f0bb5f0661f5b2f5785750e4f09", "tarball": "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz", "integrity": "sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==", "signatures": [{"sig": "MEUCIBiqU4P+W/5vekVjqBSinv2XVcfh2Um9txJ5mn+GCyHcAiEA8UyIvK43Bqbu6u2uqS4Ij19cz5NlwbDt6ENXJlU0prc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "614f7fbf8d801f0bb5f0661f5b2f5785750e4f09", "engines": {"node": ">=0.10.0"}, "gitHead": "b6e48fd72877bde572ef03b9b10df9a3b6f00e21", "scripts": {"test": "node test", "update": "node build.js > index.js", "pretest": "node build.js > test/latest.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pugjs/void-elements.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Array of \"void elements\" defined by the HTML specification.", "_nodeVersion": "6.9.1", "devDependencies": {"jsdom": "^9.9.1", "request": "^2.79.0", "request-promise": "^4.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/void-elements-3.1.0.tgz_1484844536874_0.47423613094724715", "host": "packages-18-east.internal.npmjs.com"}, "directories": {}}}, "time": {"created": "2014-08-29T11:29:35.857Z", "modified": "2024-08-16T09:31:13.999Z", "0.1.0": "2014-08-29T11:29:35.857Z", "0.1.1": "2014-08-29T11:43:05.429Z", "1.0.0": "2014-08-31T05:53:29.283Z", "2.0.0": "2015-01-28T03:31:46.169Z", "2.0.1": "2015-01-28T03:46:50.519Z", "3.1.0": "2017-01-19T16:48:57.518Z"}, "bugs": {"url": "https://github.com/jadejs/void-elements/issues"}, "author": {"name": "hemanth.hm"}, "license": "MIT", "homepage": "https://github.com/jadejs/void-elements", "keywords": ["html", "void", "elements"], "repository": {"url": "git+https://github.com/pugjs/void-elements.git", "type": "git"}, "description": "Array of \"void elements\" defined by the HTML specification.", "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}], "readme": "void-elements\n==============\n\n### Object of \"void elements\" defined by the HTML specification\n\nExports an `Object` of \"void element\" node names as defined by the HTML spec.\n\nThe list is programatically generated from the [latest W3C HTML draft](http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements).\n\n[![Build Status](https://img.shields.io/travis/pugjs/void-elements/master.svg?style=flat)](https://travis-ci.org/pugjs/void-elements)\n[![Developing Dependency Status](https://img.shields.io/david/dev/pugjs/void-elements.svg?style=flat)](https://david-dm.org/pugjs/void-elements#info=devDependencies)\n[![NPM version](https://img.shields.io/npm/v/void-elements.svg?style=flat)](https://www.npmjs.org/package/void-elements)\n\nUsage\n-----\n\n```js\nvar voidElements = require('void-elements');\n\nassert(!voidElements['span'], '<span> is not a void element');\nassert(voidElements['img'], '<img> is a void element');\n```\n\nLicense\n-------\n\nMIT\n", "readmeFilename": "README.md", "users": {"alex-cory": true, "seangenabe": true, "robinblomberg": true}}