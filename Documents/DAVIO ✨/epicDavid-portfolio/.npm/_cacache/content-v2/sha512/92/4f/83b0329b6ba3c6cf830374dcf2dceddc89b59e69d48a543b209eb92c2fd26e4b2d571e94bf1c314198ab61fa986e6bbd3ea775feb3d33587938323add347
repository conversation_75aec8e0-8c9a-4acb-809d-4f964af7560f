{"_id": "@vitest/expect", "_rev": "142-030c515e6a697afb84713e3a20b13570", "name": "@vitest/expect", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"0.27.0": {"name": "@vitest/expect", "version": "0.27.0", "license": "MIT", "_id": "@vitest/expect@0.27.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bd0332b8c63500ff0f047de98cd72d8e2ca11628", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.27.0.tgz", "fileCount": 5, "integrity": "sha512-ZumLAX0EHmrmEc+CaoDrjwbWWslSEpHaBLU0ZV3zm6jsAldM6m97YkpG7ofTEvWdU6UxFzj7DubIff+Ok4UGQg==", "signatures": [{"sig": "MEUCIQCh8h7cPX5dnB20aaftEwMnLNzCveQpObHJT03QemRpAQIgStDum9Wofk2Jg/Hf9N5Hnfnz25Sw3zxUkrzpoy0TmvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvAmjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCuhAAoOj/fHBSQTo1tGtSpUpHKyQbNLbltxni1aJO46QrQE3KnaIO\r\nWrctc7c2HdCDMofzu4JVtlVCe1uy5NCIpLBt/aXFfLolePmMk1VP6lCF4YCk\r\ntwgTy3g0W6yZ+6EPkswTy9tb2n9nxXt6lzLS7NduskNHmMuk9QEYnXaFkWiy\r\nCvhSXHF4OzqW4c2YQnoy6YT18iHCGZoUfxBHjEJiYpnP+HVQK5tXvgAeipNj\r\nG9J7zTbi5JBdu6APiHEwfAkTgNMQG/4Be0E4GqZRSGOazyLLzVjWEm6cvLiq\r\ncH/EkxXADMNTbmbDGLClHj7OVHRC0AajoYNmJyNh1GsDVya6BRrJB10huDuc\r\nwafDaQNFtzTcP8NMyESZqZtKf2SnPZj5sSLgUW6Rc28S6n47spGM+Quygud+\r\nlWMfJnYuc/15N3QhuQLRfAbKSEpuRM3y4BvPH6gLpj4f64xsfkrohXeEbUpP\r\nkLGrekjaQAdsCG2JqJDRefULaGKBEMX0czlpJBKgET1IDLuEblWu9KuftvkT\r\nFztxGcaqVlxcXvGcMmmeoE9OnNsJfd0mYwi3wB0Rxn6n/EBq8I0F9WQOzIwm\r\nHF1NjJLnH5U+0Sc8+wmhDhvnhw+QXAr+JLHX+x+RmRknCjnSP6Uy8qFQjb1w\r\nY7neOH8e3gTedJR6YBFkY8mmN8opTC8XOEQ=\r\n=gcQt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.27.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/00f58dc4a0eeda86aab2caa8626e7d98/vitest-expect-0.27.0.tgz", "_integrity": "sha512-ZumLAX0EHmrmEc+CaoDrjwbWWslSEpHaBLU0ZV3zm6jsAldM6m97YkpG7ofTEvWdU6UxFzj7DubIff+Ok4UGQg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "picocolors": "^1.0.0", "@vitest/spy": "0.27.0", "@vitest/utils": "0.27.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_0.27.0_1673267619501_0.3640380907344769", "host": "s3://npm-registry-packages"}}, "0.27.1": {"name": "@vitest/expect", "version": "0.27.1", "license": "MIT", "_id": "@vitest/expect@0.27.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c3b01c5c64ce99ce7a2297d45e78f233650bf184", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.27.1.tgz", "fileCount": 5, "integrity": "sha512-e7ejbRAm0TyvzEpr7f9R5ujxsnY7+k6ud/zeiipKBhD5PRjbBE1sN/uB4sEs33SgXRFz1Vd1z2iqoQQNnkZJ2g==", "signatures": [{"sig": "MEUCIEBPkmBz8tNnqhK/h5xaLejDIMQeLpSQtuBoGqXeCVqxAiEAkMlQwG5/7Q8V+/o9MRCLBm3ikDhIptaOr/3bHGG+4Bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvuSgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ4w/9H56XqTjdx8twYaELiGEalIY8c2dYa8Y4WA4NTYiy9V1lM3rp\r\nZHsrptw7jn6ahNbudjbISin6hspwbQAAWXDOaoMrvE2UIN8qFiO/RItEIVuA\r\nz77wTSJfHDLFtguPKt0Ef8dgQTUiyNwoY8ZRHtnfieiCYvQKY1w0eGBb/SSm\r\nAP1qqMyEMg64GgmaFYaa6Ee9yKLpIUGbHQGmbz/cd62WDlpD8a2TCrTWBp7r\r\nZ2PKwHR35p/t+adscA9SwmEFdYR9WtUKQkqBuwj77caTQgxc9yZpeR+GPJJT\r\n7ky4uHCu0w0+53SmS75Lkf1hbBsdtEMv/R6iOAyCt6Lh/Kd382hZCic04ovL\r\nMHtW5ilScFLm9V65vBJTiRwdy2f9nY/z9zb8H54Pc2gmqtUo6a/+55qu/TTg\r\naJY0ZoHpH102joaDzgbFVbS2BVvj/T7Sc4kj0AWuNVW/p5EjWo0Zqbr2gOQr\r\n279VytYNbR4wYFkgNw9+MPopqXsKSadRSHy56wzPgvDM8IPaNXFJO5zboAB4\r\n4zPdwxYhh9yceTpIVsRPUir0QyrKhU5gEITwt5anNCZ8pZQ8K1m/jv0f5rC/\r\nw68XwFI9GlFXGXB9QEKL6gledSs7wn/7T2oSZE4PHNIdPnGm4qQEh3uJzPoz\r\n9tR8rXQQB+2DvYbTPnrkZIeog/ly/CJemMk=\r\n=5pxq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.27.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/193137eeb26bb185c9838c484bbb8602/vitest-expect-0.27.1.tgz", "_integrity": "sha512-e7ejbRAm0TyvzEpr7f9R5ujxsnY7+k6ud/zeiipKBhD5PRjbBE1sN/uB4sEs33SgXRFz1Vd1z2iqoQQNnkZJ2g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "picocolors": "^1.0.0", "@vitest/spy": "0.27.1", "@vitest/utils": "0.27.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_0.27.1_1673454752262_0.05836590991953572", "host": "s3://npm-registry-packages"}}, "0.27.2": {"name": "@vitest/expect", "version": "0.27.2", "license": "MIT", "_id": "@vitest/expect@0.27.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "49e13bd34ee453de27603ad18176e6bb01c51fa2", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.27.2.tgz", "fileCount": 5, "integrity": "sha512-hu8tA/MzcaeAji6jtJqkoRwJOOpQME08s6OCiP2oxeUpBglT5MltqolhmM6hGvs84v+tTI/N4PmGrIWPyniHTw==", "signatures": [{"sig": "MEUCIQCe8quJGq8WYM2Jd5oggZhqK5GspL5mQDpSNmfClCQgmgIgBhID8LV4PXcd3lne6x7Lvkc4NBmjpusfEnVKpBxMLHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54328, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxlECACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeAw//YLIsprTVdVWpi1yxS1tQwtmaMt3TXLGUpoDOe3Osn4cmf1MB\r\nIP1A6m969SSHw8c0vLUbC95yKd/F2MaAwmaMbaOtQlJhCjs3Wvb5n5hs8Jqq\r\nI+1cQZ3b3stFs0YncyygfLguU7NdVRc3rd9RqQXHXD1nwZ4ySrb8/VGZyMkH\r\nq9RUSLVlyUFn8N1jI7IUhLiNee/396y3128smflm29lZEsAr/qr7j0ARXHdx\r\nMse7YIVOqqW59tL7tHIuBNIzcmdTAEJHUMoozA8q4TDxOZnZah8nepDnSFh+\r\nMp2z0hy9/qhvUpUdNBRd8/29C2LH4Ql7uDq2fINVXrv8+ZK+NCO9eoFnua4h\r\nQqAC7LChHKGZQWECA0f70DsUagwNVuJyFheMHumHFL2jgbYtqKHtRbow+dzP\r\n3UK4Zmpiye+ELuULsMiHKH4IaLNNO7ympepYMfnReBfouh3vIiNoe5xgUVDf\r\ngFY/xbT8VSSJGubBCgBO1J0aOjjAj8+gaU7TrZKsmvuVfrjCVY+HZqje+fBI\r\na7V5USY10l0Te3LUJ5n7cYjrbgS7YFMnn3qZrweKRczY3XIO8qTevkFBeThI\r\nRZEfv4XL2eg8Qj+a1YjJX346a/XA/D2/DWoC1t/FF+LMiz76kTGCP2M2N46s\r\nLmT0RBwRcwvhKtyOBwrrBFk6q8OGB8we5mg=\r\n=D2dD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.27.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b8d42b577f48bcd8975884da3134b880/vitest-expect-0.27.2.tgz", "_integrity": "sha512-hu8tA/MzcaeAji6jtJqkoRwJOOpQME08s6OCiP2oxeUpBglT5MltqolhmM6hGvs84v+tTI/N4PmGrIWPyniHTw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "picocolors": "^1.0.0", "@vitest/spy": "0.27.2", "@vitest/utils": "0.27.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_0.27.2_1673941249817_0.7630836542348276", "host": "s3://npm-registry-packages"}}, "0.27.3": {"name": "@vitest/expect", "version": "0.27.3", "license": "MIT", "_id": "@vitest/expect@0.27.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c119d6626aa518d979f1a571191c4113b176bd8f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.27.3.tgz", "fileCount": 5, "integrity": "sha512-I4Q+tg2dPzrheTMzCXGDXXpX+35xiZ5B+LegnSIaVwU1nAFdG+OU84hdResRPdr6VfQU5xolXsNTTgkq97eubw==", "signatures": [{"sig": "MEYCIQDbvLalY3o2oS1UN/gAYHMK96PQcNNo/asgWml4w119vQIhAMcvPq1ZsuEioIvNThN3jZDyWspNMP/s+ViWdycrSIQi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzBFzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrrsg//RnWiLfiAe+uxN4QRriGOKaubVOCMBxfLL4w5eAMR8K+e9DNh\r\nafM3/IytOdBB1rsX/lh+k89SeklW5IAH001LfwJmlRhpbkuztvy+NJRLLK47\r\nvhd5AM4MPZwizn3lYRKX9//HLWfxeifsnYa2PsGYXg5AipwT6v3DrOGNiTfg\r\nPx63qLeFikgmI6gqzO47dYkGEDnval5dkIu2JCMS/nbTsEIgeytvoYF/G1T6\r\nKYe5AYEvPw34Zmrd9wON71uLOAZnveeIw9YHJIVJt4rmTuj2IdLh53qHO1jx\r\nP/XAljNCK0l68au3RGSEP0ybi1GHFwPcyr6hVteunTugo+F6jlXGZfHe5Kf6\r\nFsFeUoY8Co54Jjq7+5F4AZ+/l9X9GBC8it4IzeDw0Fg22E/L+qEstD0U/fQg\r\n0WDjk+L4tymhp0XSfNsT5hdiDgBVP2nrb5SMqnoMte0Aq348noBxLPCH2BXE\r\nypGiSyqpb0as7fJSpM5sMEidLuP/+AGuyiBzo74LEhUYxeC3xLJF2DV//7Ua\r\nNSmv0AIvkqhMjTNvE11DZCP7amN6Mt5KXLso/oUjm/T+mIyNs1KqitXqlWkE\r\nWEnoW7GH1GptVIvT983F3nlh+xf76Bm4M6yaTqDak94V/xHhCdcLT1498Ipp\r\nZOOSUjIBoN0TnMhQWlIcoqHx2i3b0+BU7/Y=\r\n=kAP6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.27.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ed323e001f0e4eca07c59f09668af2a7/vitest-expect-0.27.3.tgz", "_integrity": "sha512-I4Q+tg2dPzrheTMzCXGDXXpX+35xiZ5B+LegnSIaVwU1nAFdG+OU84hdResRPdr6VfQU5xolXsNTTgkq97eubw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "picocolors": "^1.0.0", "@vitest/spy": "0.27.3", "@vitest/utils": "0.27.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_0.27.3_1674318195716_0.6434868449681026", "host": "s3://npm-registry-packages"}}, "0.28.0": {"name": "@vitest/expect", "version": "0.28.0", "license": "MIT", "_id": "@vitest/expect@0.28.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9dfb205c920bb76228b22e3d4f4a01a9b0d26ec3", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.0.tgz", "fileCount": 5, "integrity": "sha512-GSk1k9/W8JcVHFhCqVX/MdV9SbVbOV4p7U293f7f9xpNK17c1oA47OErsdL0a5lwJoS82RT4ZieTuE1Q9egeaQ==", "signatures": [{"sig": "MEUCIQC9MBjJsGUkunBpsbXPDyYfUVLYt7YorcD+9KRkekL3MQIgde1SaOTZHxKfxyjW+bg4Jt68lhvlD2dhM51jpygYMeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzlMoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdbQ/+LK0jXXG0Y0iklt1RZzySCszHV7xGskG8lpCrdFv93/ZuhUwc\r\nAUIiiZFAlfJZ3o920A8dW07HhA1Mdw4svSUyCUpkbywfWgBeZ3bz+vLIl3Gt\r\nhVTQr9pd1oOOWBxEqkmtY7pnFCGTGyQ0C9zqLI/3aEQJIeEXlmTWBAZ1KFQX\r\n5LFVxl5Gwru/JLpEEmekrkQP2qnYfG9nhNPSTRosHE3rH7Rz5LYn/OHJUNpg\r\njcL2AMpmyUsmSxLtLNsqy2273aK82Kmr3cyQ/fE9f2dwZtPAix654N3xuBAp\r\nxWWSOH/l5nHK68iCcx1PEoFlGdtQMpLaU1eGyc33flnVm02cg0l5paqczN2Z\r\n2XKpI+e700y0tFZ+3rSPDVdoQ2CxB/KimHNJyq53A8JwOSu1imgIOyPsUVXA\r\nIcbXkCVeC6utGpiKnRr2yKrr41na9cUwu0AH2yzksBPQrTA1Nl3kPXnJCEmW\r\nS1Y2Pz6ucadeAASVEfkbNGNoI6yrvKYyD3W+IejE3DR9PMzfXRjQe5Ja2U5o\r\nFDVbVLaMtwprDvpaJfDJAx+ymwJxvuBiv+0StAbCVCQlMRAC0gQIKB6PKfsU\r\n54EZXtbs7pccMDWaa45EiMka4jBg3mUFYJcUTjZJwD6i3Jf32NAoNRMD5O8t\r\npKpq9qtKHDQPkOdeUEUumYbQwhDLt7RXyhY=\r\n=+uTq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/cc73cb974a810ee7145e612d4b9243bb/vitest-expect-0.28.0.tgz", "_integrity": "sha512-GSk1k9/W8JcVHFhCqVX/MdV9SbVbOV4p7U293f7f9xpNK17c1oA47OErsdL0a5lwJoS82RT4ZieTuE1Q9egeaQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.0", "@vitest/utils": "0.28.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.0_1674466087809_0.38001033781996973", "host": "s3://npm-registry-packages"}}, "0.28.1": {"name": "@vitest/expect", "version": "0.28.1", "license": "MIT", "_id": "@vitest/expect@0.28.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "75e81973c907fe7516b78bcfdc99c6f6c07bf714", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.1.tgz", "fileCount": 5, "integrity": "sha512-BOvWjBoocKrrTTTC0opIvzOEa7WR/Ovx4++QYlbjYKjnQJfWRSEQkTpAIEfOURtZ/ICcaLk5jvsRshXvjarZew==", "signatures": [{"sig": "MEYCIQDTjTwjnFUC26fn7mODc7P/5Oo5ugTUJsFwsSqlPT4i5gIhANuKcz/+y3JAQG97+vqFjt/6eVLvAUkyY61HQ/JYHID2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzliPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsGw//cO7pPweit3v7nwsZp7NY/Yc46n6GIBo0/uOGus+Fz0ya/qWr\r\nvcGwhSEvwu0x4PRRYcWk2i0G4Wf7tUUcgYL6vovkfRysRD6qQiIrOECESC1e\r\nFEW8QXo3xbB746KXCiMkv4ScWqAXR6BhxCnH4BD33pra8rvnkm8j1IpOrkUP\r\n0F8VsiUWABUyZa4jUBjFjzSUvVBISjVOed42g3QlrNUN4Cp7v+mZd0bDJM6N\r\nXWDnVk0mrSesHT+mBHpmvmphr3I625sE/bzhZtlabnZexHFUSEQJ0g6/hn5J\r\nXhY11u72eRzPLWW6jW4JkITm1H30L5OEt9B89PhYWzFCXu9WUYd+Ft/yi6eS\r\nRWozSniKBQI4OTFmqGL2T2KsZiaSuuR2lONFAq/O73+jIkYWL5mCBNgU8uRX\r\nrkhDnl9NyNlBOMjCCI0kpMAXb3yizBLj5N6atdkiL3I9KfOhje/XHZjhE1fi\r\nsF+f/7/vbKpNuLOcdbsf62ZfcsbKld1cLNU1RzJFpCR7M3FmAG1b+Uy/5YTI\r\ntN6lgbFu8s8bjebGv3zZFRhdEnAEoJ0jV5MuPOF6whrDWmSEo6cOVKk3lc6I\r\nsQfCUzBNdDOS7VQ/ZLYaR/WZ6en/NucRM3q8yOFCHeiLAvb3K3Yv+0c9UbGG\r\nk/3S1B1DcjA+Vk12aqa7zBdyFbgaxMjhX24=\r\n=m5u5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ab503316d7c35ac07a944002e1987046/vitest-expect-0.28.1.tgz", "_integrity": "sha512-BOvWjBoocKrrTTTC0opIvzOEa7WR/Ovx4++QYlbjYKjnQJfWRSEQkTpAIEfOURtZ/ICcaLk5jvsRshXvjarZew==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.1", "@vitest/utils": "0.28.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.1_1674467471462_0.9643357834081632", "host": "s3://npm-registry-packages"}}, "0.28.2": {"name": "@vitest/expect", "version": "0.28.2", "license": "MIT", "_id": "@vitest/expect@0.28.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "375af32579b3d6b972a1fe0be0e0260ffb84bc7d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.2.tgz", "fileCount": 5, "integrity": "sha512-syEAK7I24/aGR2lXma98WNnvMwAJ+fMx32yPcj8eLdCEWjZI3SH8ozMaKQMy65B/xZCZAl6MXmfjtJb2CpWPMg==", "signatures": [{"sig": "MEUCIQCxAQf1ipZvLdCkgV47lUZzVQB7ruD+83Bbx4HLZmUZSAIgft94rWYbZV9WRVimGIuk5KuyjFdzzWpZCWuBpNOVxYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0RCfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/MxAAozAe12M1obC7lS1UpVoz1Xf1tqe+KDsjgCXxcl4XzsTzEohu\r\nCVgxoaw2OJbpJ3AutXNvie4rdFqw2J7jaN/TXFZpsDIwx9NiymMiLI/aAuO/\r\naxfPcZ0sE+cPpUN3ZzqrVXQjheUWH0ODhPtoXXYkmAlHLJ97A4hn09VU/aVO\r\n7jj7cCLZK9YYo6a24iGJGACv9e2SuR3hXM7lUQnFHXIw23aM2JTrA0DsW/bA\r\nd92I9Hqyk19D+oFAo3p4egGNO6okvgKaMiR0r/4ZbKCInrPqOxbOTaWeFWKs\r\njAGj9vjza71tTIg9IPmOMItmfcjQeTIzLeMYS7M4iIckj1BJ2wS+3ShLiBDd\r\nX5zU1/GvF+pivqsEwGXwRr6m0f4plF02nXvlDEtMcziwye6KOld764PxakgK\r\n5pdYr6zDDJLeGPZqYUa6S5bXJuq5ZVkYSt1IytUVuXMULS5dvjkb9tmzEUDX\r\nj7m79ZH4+QCVLYC0sTMbXU4eFTH896F9MLHIBUyCJBgWYU6KEU/JbweFeNp/\r\n6ZRD6X3Z4RVB/fZErw0huqnhW6E8CYvbD2NQDYDgq4wJgVMUYbxGMulg3nPg\r\nLOqQuI9y3yLBMxMdE3J1GlWLkGdMTmwDMxhPgHaH6RJcs9kAk5nAoDfp4GON\r\nwuNQ50ppBNeDRo0N+xstQQEcztLNfiQgfK4=\r\n=6V3o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/30/nymxcyb909ggq2j5lwn7b_600000gn/T/9486dc9e521cf73b2cf7ce43cac4bc5b/vitest-expect-0.28.2.tgz", "_integrity": "sha512-syEAK7I24/aGR2lXma98WNnvMwAJ+fMx32yPcj8eLdCEWjZI3SH8ozMaKQMy65B/xZCZAl6MXmfjtJb2CpWPMg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.3", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.13.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.2", "@vitest/utils": "0.28.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.2_1674645663386_0.36495356640870336", "host": "s3://npm-registry-packages"}}, "0.28.3": {"name": "@vitest/expect", "version": "0.28.3", "license": "MIT", "_id": "@vitest/expect@0.28.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8cd570b662e709f56ba29835879890c87429a194", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.3.tgz", "fileCount": 5, "integrity": "sha512-dnxllhfln88DOvpAK1fuI7/xHwRgTgR4wdxHldPaoTaBu6Rh9zK5b//v/cjTkhOfNP/AJ8evbNO8H7c3biwd1g==", "signatures": [{"sig": "MEUCIQD9MNcY8pVJkExjI169VNy8Ub3XyxhYgakYAZ01zjzonAIgZnhVvaPFQBi08973WweCD4XREavwT5y/c53+J0IDgH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj078RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdLg//beb5ApfRInus2jw0usfsE4aHE+DfQ8cgNAdv2Z4+73E8YN0k\r\n+vvKhGXFkfrQq4t/tpRKwoHFF7x6zIXSI77/cr6OQtHlvoy42gQzY/qedwvU\r\n1fU3DfEdOK/JfGQI1dQHsTCFnVmEVlSx+RE8tbsqAgFK8zUjwDvNqoubKPMH\r\nQ1C7M13zJQ6rRItmMN9nCIyAZFAZQhICdPZt8HwksITnPwEylt6g43fvFCkp\r\ndmyCQI+DYmTFJnmSvzIX5QTF5xxPcnDMFCfDM8N/FCENF/Eh8eBLrfXOTjhV\r\nv8KmlCVHyomQBL34djlluEPXTtjgEMeOjgSFhof3lMf4Gt/o4ysxmy8KWcTD\r\noQd7OxxpZco4eiuHzLy4Jcecj1gw/8haMuxonwK30BT6jSyNjxTi3Wq5gdcP\r\n3Fhe7ztgG6D9A6IIM8mxEOl4mlQOxq0u/ARfpB5qmGg6hguniwvpEiw85dMD\r\nOnKpmg8KewX6zzpGGsOMD14KP/Rs60Ill7tCa8PjdBQ3GP4VtG2+m55R9PUt\r\nK98mBfPTdh6dgwr0wIWEvt/IPnh7u5/oz+GbOteERUX7Y73ku4Ch4VGP9toy\r\n7xt4JBLN/5VkKD1moPEhtAaOJRi2KnsuW2EOdw8oYpqU/ti080D6jbKGzENA\r\nzGuCjou4E8Crm/UtxmY+SqkNisJkWoOqJzg=\r\n=9NTl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4c0b76edef8354281be7104491a762fd/vitest-expect-0.28.3.tgz", "_integrity": "sha512-dnxllhfln88DOvpAK1fuI7/xHwRgTgR4wdxHldPaoTaBu6Rh9zK5b//v/cjTkhOfNP/AJ8evbNO8H7c3biwd1g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.3", "@vitest/utils": "0.28.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.3_1674821393660_0.6340648002228815", "host": "s3://npm-registry-packages"}}, "0.28.4": {"name": "@vitest/expect", "version": "0.28.4", "license": "MIT", "_id": "@vitest/expect@0.28.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "09f2513d2ea951057660540ae235609a0d6378f9", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.4.tgz", "fileCount": 5, "integrity": "sha512-JqK0NZ4brjvOSL8hXAnIsfi+jxDF7rH/ZWCGCt0FAqRnVFc1hXsfwXksQvEnKqD84avRt3gmeXoK4tNbmkoVsQ==", "signatures": [{"sig": "MEUCIQDmLgkN46D3Mb57DW6/7MnBK7JhtenKSOZL7M7IJoq0xwIgfGcaCmLEAK3ASrew7BE4xytBY95cem5Dr/t00KykNDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3NxKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBLg/9GUklavXP2yzeE7RrOJFeW3RbnhKKguT7tahkYfXwf9eBdZsq\r\nIIQ1ilE0W26PC289mWxFvu3sdwuZlbMb601x0v2qjlqV53fWJq+p4gIqHL3A\r\nhWRLq5J3kN27twRP+v959E6Gda/ePJHLjBJzhDgyfIG3HZacl6NI7Chwq2pr\r\nX0HQ78jFBqoDJI7qaa1IZnTXOuDMu51iVbUe9ic6+p+UayiZWo+Q4Lt5+hnq\r\nel0GxaGWNMq4yaIAiUf2Mdffkmk24VA3bvPo4sM7ywlYlnIXoXLDJgG1jsmr\r\nWnvoQCldTq8R2HJnHf9T5x8z4KjdHC9sHayShzt638o2rVUSfRYSzb3cayPc\r\n6UyVDqE/85EimOakNfiHkv5lMGmKNOQ193kM8/B0EOOwiO57/v5IqkL8kYTR\r\np7LqbZGpBN1wZKSt0V6i1cdRLTiQAvpGY7Y+6DePpotTYsm9e91XqH96PFw+\r\nv+WF6xuBIyOKncqUkM1iv8aDtz6LO92Xa1xZAG6q96nIo9tyxpVQF/6Gm7gx\r\n4phoXaHpvBwEkzl5rjFeqGVAdIvKuPOQEWvgeOIh7wHS8bAFM7AsAw76bASC\r\nHph1SPVAikvgc4BLKqR/v5wdXPGDT2FzRqPNq3cUIDwHYxGK9vO03qWq/IR+\r\n5QICAEnteBrA7Of0o+CiDheTk42fBcmqq6M=\r\n=pJXq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/89780cce6272b8e5661ed353fb54283d/vitest-expect-0.28.4.tgz", "_integrity": "sha512-JqK0NZ4brjvOSL8hXAnIsfi+jxDF7rH/ZWCGCt0FAqRnVFc1hXsfwXksQvEnKqD84avRt3gmeXoK4tNbmkoVsQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.4", "@vitest/utils": "0.28.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.4_1675418697826_0.2949689941829463", "host": "s3://npm-registry-packages"}}, "0.28.5": {"name": "@vitest/expect", "version": "0.28.5", "license": "MIT", "_id": "@vitest/expect@0.28.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d5a6eccd014e9ad66fe87a20d16426a2815c0e8a", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.28.5.tgz", "fileCount": 5, "integrity": "sha512-gqTZwoUTwepwGIatnw4UKpQfnoyV0Z9Czn9+Lo2/jLIt4/AXLTn+oVZxlQ7Ng8bzcNkR+3DqLJ08kNr8jRmdNQ==", "signatures": [{"sig": "MEUCIQDuczIdMjGlp3HCZHMh0VkvxfBXlsr0a8KwACxXbVsbcwIgEOKs9mnr0C0kPLxvYJ7VxK2GOKkj2Pjs+sYI3EKQX2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6ivSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoD9RAAgosXhvo7weu57hcBFte/+ftIFWK+z754vFoBnfcmKPHkGpA8\r\nydHJhrl6Y8uDszyC4N7b6chXOxn99W5CBX0FJwvb4VlMLFCs8bQjdDHqIuH6\r\nD8dkemGAZVOQtWAf02wyj/2f57TX47CgzrLBl6kf1Wx94PFBBQBZtorxhqXj\r\n8AxGO9glEAyCsK/B99PCqGvFJsIWNwMrpIGiILT0KHo0TjtJt2zqEnaiaG+l\r\nfauHh8jnw5j0Ffwf33lfZcevhFSjD4NcJRZUcXVfWB9G3xvyNKbPLNLxSFRt\r\n+GPaIrjCS4yoIy2b0re0Yjf3iomhf0/cOI+LKZJPALwbNJJMbnI481E8hasB\r\nerpkIKGj/0qBdKqxKGEEzNPOtlm+i4cAsFuxvrrQZOPyFxLCBxfEC3sTFr/I\r\nAjcOTmJJEhc4+AIm17Qp0apZf1lD7PlESkswhf1GtJXa+76B3nWn1J4Jtn4K\r\nr1OlXrRH5hCzsfAa15aI8O4J3Owlm6if3HNHFjVZXM/t7v73hLaDENHh8Ipa\r\ng9IkE1PkmX5Crt2dIbEswdNPKeWh/jziVs82icsnneorSKt+ujeOArvPS7r6\r\n+7AkoIY6O0LZ/qWmJoyC9x3aF21voKfUOExWItHvzlM2P1OhNGLbzOa/boby\r\nHuzqKZA0JC+aYOG+xNBngMrwa1EDPzDuCGA=\r\n=OOku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.28.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1bf784caefcc28dce5f3a5642328ecca/vitest-expect-0.28.5.tgz", "_integrity": "sha512-gqTZwoUTwepwGIatnw4UKpQfnoyV0Z9Czn9+Lo2/jLIt4/AXLTn+oVZxlQ7Ng8bzcNkR+3DqLJ08kNr8jRmdNQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.28.5", "@vitest/utils": "0.28.5"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.28.5_1676291026143_0.48323556783479193", "host": "s3://npm-registry-packages"}}, "0.29.0": {"name": "@vitest/expect", "version": "0.29.0", "license": "MIT", "_id": "@vitest/expect@0.29.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f01ae073513b31f8b278ac344e405312efa6552c", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.0.tgz", "fileCount": 5, "integrity": "sha512-WBuEI1yW16BZV+Py845Em61RsFIt7dyCtgdVWiDu2qizqH6D/Y9x4b6clW4hq7VvMiGRlwBpnt7smzR8jOlacw==", "signatures": [{"sig": "MEQCIG8GolOHRqb66b9MPqSEmgPHyGmkoAZgGK9H8iCJwKfOAiBF5dlQc0pDlfRRnk1lkufeAY42gJ7wisjV3fz/QZKNxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+cYOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBKRAAgDaqSRMsRABAHj5SipF6t5saEUiWqmMnJay+2OINN9A42/Ch\r\nQDr/0B72uVFD9Bf3JvJD2tGXqPHljE/LTnub50ZhlDxqy2WhOia1RtxGve/6\r\niCAHIkkFsYesEeLbEKWXg3m6Olr0M96g7pz04HNV4ss7+qVBUXvQMJzhWSD2\r\nrTrU0U71HpIdDqZc5CofgzTXiUwC98CdAPzsdoK8vKpoPvFS5GNeuDoTwpoD\r\nj1w3tzhEA3sX+iYY+/5AmWWkKHQsxI/aBf2Ki6TfLq9QtZySIsGKDFmlh+dD\r\nPGhjqMS0l+AinKBG2WaVKJUOLmy5CQZ6qMJV6bfKwHDnDDUS+zN3Bu0ZfUQO\r\nBPJBWbXcmpPAnmcR8fYSk1LEhpqQ2+w6yS+Wrcdg6Ey7+mQsqukDEaIQDX6H\r\nxjZMeOUDl4JFZSDAdQ3OlhnnpH3qxEjvMURJRVh5DMC3cbdIpY1+HgTIck+E\r\nwILZMIi4rlnrNYhMXiWGh8gPi9uC9qAJblWInJQMc9JowmCf0CIva8n32dwr\r\nXRmFOCgpBXbDEH83pORfK6GZPwk9kBOwyRXV4ZL9w+swA4jDlLHo+k8DJoaj\r\n2D+shEIk2PMUtXMrwSOl4okx/va7UrGMG8TZplOqotP95SJMP41LdH/hZiAD\r\nK5aXalpJ1lvQRzjyrbY61NZ3s6up2JB6Yk4=\r\n=eKZL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6900bcd1c6b576a42c637054b8de91a4/vitest-expect-0.29.0.tgz", "_integrity": "sha512-WBuEI1yW16BZV+Py845Em61RsFIt7dyCtgdVWiDu2qizqH6D/Y9x4b6clW4hq7VvMiGRlwBpnt7smzR8jOlacw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.0", "@vitest/utils": "0.29.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.0_1677313550142_0.7178508275396476", "host": "s3://npm-registry-packages"}}, "0.29.1": {"name": "@vitest/expect", "version": "0.29.1", "license": "MIT", "_id": "@vitest/expect@0.29.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "befba6278167fc3d4d47a87557eaf41f3b06a7cf", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.1.tgz", "fileCount": 5, "integrity": "sha512-VFt1u34D+/L4pqjLA8VGPdHbdF8dgjX9Nq573L9KG6/7MIAL9jmbEIKpXudmxjoTwcyczOXRyDuUWBQHZafjoA==", "signatures": [{"sig": "MEUCIGtPVCHc8zmWc/Mu18waNBTtAB+hEC96qBfCYOljhmxxAiEAiUQdVSkpBifXIh4SnDcq0Y8RWGsYy5PH0mGDNUyVYg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+dPvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqozg/+IKipFZmFYRkeg312RP6GzHlrEee0CfNeZQpQvPl8AlMl0rti\r\nxkwjzUy6tUVB8gozRpdZd5HY/vpJS/OvV9I3EzUNIME6PAWwfLhhiHme1dAZ\r\nxyPsui/mm/CHa9qsM7PubnwZT2Bgibg8LANelaDPbW3aFkHgyHawk8Lg1iYx\r\n8jKD+BhxPYsEI+HEHZlhAmZfYude0CA0GeWy4YgmIN/JODk8r1dp7IUpACJ/\r\nvEll4N2C/NXGJEwlpMiXV1CY5aDJ7g3lsPiqPlxVzQPHn47GdFFz7VywmAFY\r\nq79GRIoCILiMEdTSi9/KzFkylGZmISgTxmdpV1Dsk3X/G6TbNX9k98U2PH6z\r\nAFyUG6JeGIJgGLtYX20LDKeHjEyExmrKcURLQAXecQFSXMJqSPGt0jd/i/SF\r\n4h8tW0FCkBjEDEl1gevB2GE9rFMiqY6Nw6c/+FhbvP+D4U5yoF9iz8hnP4mh\r\nOTdNprY55mhm3rwnbolxQ6/YGd1HGTWmc/fMvbXFta1FNXwHq/tEofnfr3kx\r\nibPMfj8Mc0aivmdWh5h+LqkPOLG2s4ISd4foLlVdDkOiYsfcMQqb5fmX73JE\r\ncTWEPhpnsGZCWgd+MZMoBy3fli8VC9Ao1aOk8fOQ1f4YrnTkw+ALvrFWQxhx\r\n87lvWiIQUo/tP3T2qbaUgcvyPabavEzadhE=\r\n=Apml\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/186ae5d6ea8160fc3b2560310f677383/vitest-expect-0.29.1.tgz", "_integrity": "sha512-VFt1u34D+/L4pqjLA8VGPdHbdF8dgjX9Nq573L9KG6/7MIAL9jmbEIKpXudmxjoTwcyczOXRyDuUWBQHZafjoA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.1", "@vitest/utils": "0.29.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.1_1677317103541_0.14983891099251512", "host": "s3://npm-registry-packages"}}, "0.29.2": {"name": "@vitest/expect", "version": "0.29.2", "license": "MIT", "_id": "@vitest/expect@0.29.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7503aabd72764612b0bc8258bafa3232ccb81586", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.2.tgz", "fileCount": 5, "integrity": "sha512-wjrdHB2ANTch3XKRhjWZN0UueFocH0cQbi2tR5Jtq60Nb3YOSmakjdAvUa2JFBu/o8Vjhj5cYbcMXkZxn1NzmA==", "signatures": [{"sig": "MEUCIQCG2Gpwz4Owo2ogtih7Z/InkYJnIxTxVXUBmU7jqdH9/wIgavvc4qGvwlEbhHrv9lf5JTNb5sJ1s8vSJYbiAkoCMDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/hpXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBDw//YtJKnSKmK8XHEhxcbY7FsKcvNPWe+UsWWsPp3luTAaMIBlZO\r\nAJD9YvLLu3sj442cmqhHqKCB8No+CKFd1HT5X8zPOAbn7VWntTX8CKcvBJUH\r\nKsh49+4wUIRCwYE4ynuMyQO0vFkXXOoDIxyCbEszDGQmF+RTaJqOznUmM4Tm\r\ngNhkGGNB07kukxOztPmYxp5331HozyOkgCRtqsFFgRpVyuIrneTCtFqeQpIW\r\nHoj4gcZFwS5ZJL3jYwvm9+NXPLPMkp2o2lKgyKeNgsqD++UzYtPUQw/z/BHS\r\nVoV/u4YHw5RccRCqzqX1xyxAJFzwTiXDvfBEWVlD3qKrp0RgzVBmVDueKRHF\r\nOBhtm8CHHQ169hDRELCQKtLy23a8HIhRREkm5LOfh72dmfZf1pXppgQ3htbP\r\n+0dVjkm3C5bBkk9gpwZU4CMECwwtyXYSlSj6wxFGhezhpOLjCMxzePd1ln8t\r\nWacdPOjRP7kI2VBb7HL/Ukbg5Z2kAfFvfWNRFZrvw4kk8tjbVW4PpbMKz6fl\r\nGg65BDxa45GLFOy3mZS5hytjqv4wKqF3Z2cJX8LPjcItZyfKqOkZzwFcSK8s\r\nJNaEcQ7emW/bb6OmAe42x/XgUAJgJc7phISp97F3UVf6P/Dle1D+mXsq7Y60\r\nohMRxzOCSAlLCxtpIYPJylqffWcRCwAvies=\r\n=lKO4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/e58eaf7c0698a523aaa19b814b254604/vitest-expect-0.29.2.tgz", "_integrity": "sha512-wjrdHB2ANTch3XKRhjWZN0UueFocH0cQbi2tR5Jtq60Nb3YOSmakjdAvUa2JFBu/o8Vjhj5cYbcMXkZxn1NzmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.2", "@vitest/utils": "0.29.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.2_1677597271232_0.9222901265610655", "host": "s3://npm-registry-packages"}}, "0.29.3": {"name": "@vitest/expect", "version": "0.29.3", "license": "MIT", "_id": "@vitest/expect@0.29.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4b101ebcbaed608b20c2592cb7d833eb38e5bfa0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.3.tgz", "fileCount": 5, "integrity": "sha512-z/0JqBqqrdtrT/wzxNrWC76EpkOHdl+SvuNGxWulLaoluygntYyG5wJul5u/rQs5875zfFz/F+JaDf90SkLUIg==", "signatures": [{"sig": "MEUCIDbQhCm6OQXfdscPX36+OE0RpewUQYz7OhfKCSSJVx2IAiEA53mAfPCPhHZncODxMdyACW3j0ZJs0oF3HnPOqjvLrx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEiSwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVuw//e68i3HJyHIxla01A+hKpHtozxdZrEcUJ6uduRYwJQRj9aMh3\r\nZRWQ9INiBntjjKZNtPX9YT+B9UGO1ESgbmqClQzKSGBCO4hNxXKo0VrCwtzy\r\n77Cfosf9Ftv48hmwgwGb3dFoi13xEUTIxtbA/fUmuaXw52AQfSBL1a3RcPNw\r\nCEr7KgEbeDrob7KsSW75Oxflm4hjTqJdd/IIfC15lvfWbvS5SrJksqOIQNIU\r\nxyEYwF9/DPfCHVthizLz4V95L7aVkX1DV0+sf3hoLiOwDi0xo9AOq59DUfjA\r\nYra3scODPfUYW+gGG9N/rq5Ad0qYU4gxJndaa55Ssd23cY8waVJi+60Vb7PQ\r\ntZ5/fuFkEg9xMYP7psVr7xLQtiXE/0pdyJyNBY+Kk93jiKOOcsX2IUn1TczG\r\n1vIZVLtZmk9hRhwKB8l0d4XlEX+LSGGtPL3L/XRw/JdLpAu1EFHFnBNN4b/e\r\nWBdHACSiLZr0z85HPXm/GSgSQOdYKrC23ilKNZOXZHHgcqHKVC4+6w8OVWOq\r\nskmUdW0zTyvEgzeliUHGklaRUfb8YMPuBgkvMvR3tsLgw4Mv0QUHSYvH7T9e\r\nW9DDFzUJpjCQjiDw+S2r5O8ywPV9jeCTVOOYiP19JxUjA7qt6wCdiGCPHr+h\r\n400E0L8afyK+ds3Tt2i31C8N8nUM+XH4iyE=\r\n=nTmo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3d25d2fc4b2ca3c449b854f7c1a38ce7/vitest-expect-0.29.3.tgz", "_integrity": "sha512-z/0JqBqqrdtrT/wzxNrWC76EpkOHdl+SvuNGxWulLaoluygntYyG5wJul5u/rQs5875zfFz/F+JaDf90SkLUIg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.3", "@vitest/utils": "0.29.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.3_1678910640583_0.44129316209604674", "host": "s3://npm-registry-packages"}}, "0.29.4": {"name": "@vitest/expect", "version": "0.29.4", "license": "MIT", "_id": "@vitest/expect@0.29.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2787763eebffdc06ac66fc64d56f020a31589b89", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.4.tgz", "fileCount": 5, "integrity": "sha512-U61q5xci/wwspkH0qWUfLllR8TuzYS1caNVu5KHfI2Ef0k+q4Zd1YVHjTSvbspEeTXhjgWBO7Qm3TYTjBxdJFw==", "signatures": [{"sig": "MEYCIQDgRDfOfEdJTptK0GBqEX3FVdEl+fYDsqFoDO883k8WpQIhAMOuhvjWU3tnr6TS/9YFG1GNyDX0LruPllTEFMpoKLK9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGJNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWkRAAii7J911XuBw01h+fEdOy5svUqwrRbmVipnmlRrT5HkYrMAwe\r\nlW+it3glxX8+hoY6m0cnYxvOrzKRRyUk+GfrfRwU6sfHIT9EgcXKWyvAa7fp\r\nnJZ6HLZ/dKy0Fb24fd0ohdbOwA055rNwtAD0yp86NbW/TdqvIqX7YHf9hh5Q\r\nXXcVMOCBLtMWijLMhywQpMxzr0lRSqWHAJH37XbCx5/jWngeSi7vmjbiA43S\r\n3hPUNCyFeyfW25Au/q5Oa+W2qlRs7d+bx8GSamVpxMn4YL0r2bMe7Hnbura+\r\nAiYp1JE+BaYpRDNZh5Jc3fb7FQc8ZeAQoPvvooBfO2sGc1ZvEi7U2FowMZIu\r\nLkmKRKvFbJ6ScLFI02hNiUBXw5gz3klAfiIMYAZetkjmi0F29rzA1jUGiQf7\r\nWCOUq6czZeEvc/pK026gev7p8+aJBgCVhd6qGuYP6rgHzRs/vYH5Ibqcu9bg\r\nvaBYs45DIqCHduwqU/E36XCd0JYOqn2BxKnxTSMSH9YYZeDVB5KAFCKk0Xus\r\nIhUqfcaQ+FdwQyGe8SfhfRuye4wGd9h8WyKggEedkDXE9F8wvzavg+wJnQyY\r\nirfPcGYGyiiTnlF7teGk9O2MzcFXgWYU6WI4neNeHSQai5Yi33P1X5vw6++l\r\nnloUYAqRMqMecpNvEYE7islP6o4sxFJyAEA=\r\n=1osa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ca0fb7b0f9e324743b53b15088aeda9e/vitest-expect-0.29.4.tgz", "_integrity": "sha512-U61q5xci/wwspkH0qWUfLllR8TuzYS1caNVu5KHfI2Ef0k+q4Zd1YVHjTSvbspEeTXhjgWBO7Qm3TYTjBxdJFw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.4", "@vitest/utils": "0.29.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.4_1679319629781_0.522767450658522", "host": "s3://npm-registry-packages"}}, "0.29.5": {"name": "@vitest/expect", "version": "0.29.5", "license": "MIT", "_id": "@vitest/expect@0.29.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d68ef978dd27806846908403650f582ebeee9ab4", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.5.tgz", "fileCount": 5, "integrity": "sha512-gDIafqKvpXlhbbaZWmL/CrJycBo6wT3/67BzsD6OFVTzOgeIOIhg27ysy8bJ3NWlaDqAxvnm+71wfzztIBFU9g==", "signatures": [{"sig": "MEQCIBxpnxYTg7v4Rr2cwk4/PefdmtfhkushNPiOwmSCWh35AiAt+2WXAVe1MD7Jj6RcU4KJBHtiKTTozj4BH7plyEp84g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGm/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA8g/+LruHITOJ0SJThiy6Tj/t7F/Nr67KLunIIwwEQXPdrNruSeZv\r\nmFfjkzW5ZDD0scBJsS+65eX1GyeSuXoZdKLq73uS1gu4I3tSpA/7iPw8qas5\r\nDA8mNbem8sW5W4WC+hMM0/mPufTvV2wRF5c4C3mgFlr9XPgaIWY2ucVPyNfn\r\nwOKrUZQ5gg7Zgld0oUL2T6mq4d/J0SSzMVpvCDZ3bAGqzRb0aLQycVwajeW8\r\nHrzYSI053VguzHLiNbHbFkUF36Yo4B064qYE9d+/FCJB5R254U81cOUHqtuz\r\nKKZ1qFNz9XgMGRoow4KuTfujepQmgSuP8aP1aRBhC38afXIGuFZ5WOcEPcO8\r\nLGbRhPcvCSwXHBQk79C4FV4BxnDhiZAasi+r6jGM8JtJvPOkvoQ+9KUrwtua\r\nbMrpiDoKd7ycyP8coivBEOtCB9LTX5cEFx+CuQmx+QLsKIpdTxwMyNt1YSoD\r\nf0TOB3sUt/CC/hlZ+NLGj3YBRoe/lyRqTuu79+skERlcW/xHp1flSiCyffQg\r\nZWfozLG4L1A5GurIiljv4LfXwC+nysyihNV4lotkmzCUvxA2TOKeVwu9QJ6P\r\n0Az1s95pZngcdQQW/mGDcvdNanZ6nmE3aShoRjMnngxzdZo1KA9Cz62gMlnF\r\nB3IhoRMD5MP2n3XGqSQ/EPS0Zmcoaxbm7EE=\r\n=/+s2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8fb7fc22192ca1352330cce315dfb21e/vitest-expect-0.29.5.tgz", "_integrity": "sha512-gDIafqKvpXlhbbaZWmL/CrJycBo6wT3/67BzsD6OFVTzOgeIOIhg27ysy8bJ3NWlaDqAxvnm+71wfzztIBFU9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.5", "@vitest/utils": "0.29.5"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.5_1679321535454_0.19333323322410778", "host": "s3://npm-registry-packages"}}, "0.29.6": {"name": "@vitest/expect", "version": "0.29.6", "license": "MIT", "_id": "@vitest/expect@0.29.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "33ed1b63139834a1e67da19100a8d0c2c4c79040", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.6.tgz", "fileCount": 5, "integrity": "sha512-K/pGD0qaYjdBOvsJ36K1zo9JGEcbU+8lvvAuJlrmiYl1qjBOpiNH2khtee7f+FgQRyGlQPR9YKJVaj26n78oYg==", "signatures": [{"sig": "MEYCIQDjkAzFyd3qBxM9T4WsAfSSc+3kdK13rA4K2OLCwhFRDAIhAOM5oNMXMEuF9nyniG35M4dciMDiFvq7iHJ2hvpANKGs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMDQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcxA//YOKUbEZr15G0UfJaV8VCrNfiNZLswj6HjGvxvxh7tl0/tm91\r\nVPckJo0u0H5McNt77c4oLxdtSWz1oZQ8jimt1XH7voNnIvooR3Qv6e6XQg5A\r\n/AajReCo5KHRfhSEODB0/GHujPRyLwcblHUeDBCKV5l+sxOf/+gNNrH/FI36\r\nz8jMNmguZ34qF2boIMpS+Z+063bpxqAO0Lx67/btlNjQIzVlIayymzi3IFLC\r\nz/Ue+2MkWuLU6bVbzd7spKsCN+kC16akaqjz9iH9oQhXL8pp+T8SBCrphqhQ\r\n1WTZFTHSOO41L9qszGqROYCWv0VKDftQENaTyIHtGKVqGUvwJsIrjLdXH3wG\r\nZMUzjaWzcMkuIDhvVn6JhkGLvGpeiJGFrGohOC3TGVcB6TvqFm49VekRC/Dc\r\naCATwicTI+Xw+UgNIx6bI1EGOCFWbpvv+ONK7we8Qv79WhTmwTIawjs7SHPz\r\nZ3gv+E2G9QMVVEiPJbPMR3YGHUldykrqIHiMcKKXAVlL0CpsoOnVGggvZQMh\r\ndRUo5L0E87wUkNwx1OIp2nYgu63s02nIE/Nopvn19pbunxwSuy8PQZrW/LZo\r\nw1GfzEn/FzOq/yGwsL7njaYWFWzdLlTGc5WlEnAaacxUJrFEqSO3OnYroZdE\r\nJpXb8q41lZlvvfYTQWVeZyxMzCwCXtjt29k=\r\n=l98R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fb38a0eee72bcc559d9d2d1251e9a96b/vitest-expect-0.29.6.tgz", "_integrity": "sha512-K/pGD0qaYjdBOvsJ36K1zo9JGEcbU+8lvvAuJlrmiYl1qjBOpiNH2khtee7f+FgQRyGlQPR9YKJVaj26n78oYg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.6", "@vitest/utils": "0.29.6"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.6_1679343824514_0.11389085975648916", "host": "s3://npm-registry-packages"}}, "0.29.7": {"name": "@vitest/expect", "version": "0.29.7", "license": "MIT", "_id": "@vitest/expect@0.29.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2d6560ba772b4945ea46ff78a2c9b65e44fe65ff", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.7.tgz", "fileCount": 5, "integrity": "sha512-UtG0tW0DP6b3N8aw7PHmweKDsvPv4wjGvrVZW7OSxaFg76ShtVdMiMcUkZJgCE8QWUmhwaM0aQhbbVLo4F4pkA==", "signatures": [{"sig": "MEQCID571mlory1MlG4o9Nzr6ghluoicbRpnmKJiDjtCv/kwAiA3d+LBy6s1E0SsIm63vjxbCgFNVKvpzXgpCXzjSeui0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMQiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPCQ/9HKcukxFi5mKJ7VTdNyBUM7wEJlbqiUKZvGcGuh6OOxNPAb6n\r\nqXxSQXLQ8G04Wp77uT0ZHeLJgBv+h5PL4L3oFpnUBC+Ns8TOhdIMg24M/AZn\r\nyHwdTLVvGrKihRTyrQPlAW+TkaNvuejlsMaPUDJ1bGEg1FlwKGTDbJFOa5eW\r\nvUrhkabHGCxu2yUXEriZf1mwQj0v978ejMJlfT+JK8fYTR3utQ6NK5sN1R55\r\ncEtAaf60jzA4s9jtzt0HVI/j0Ahz670z6hXd/3LxHqj6bnn3Au0IapnWXfao\r\ndF44bDNR0YC7aVk0va8WxF9ruRCx1xySRqPYsv50MhGz0gPvOzHWqi9pE3TC\r\nfV2jSkDaGuEWbHZnxR48Yu3Te2crJnWDNip8TUwbRd7WxWE9x3h3AbGSQsRR\r\nLAq3MJkjdgTSCss5vV4V+/gOoEQbPGl8YvLwPkqT2yO3RGUJatT0t9N8l9Jh\r\nvfzW33Ezcn2i8uVXI4Ffsyf2vQMh2qL1/3AjkfaEib1e9SrY6VCPKuFEUTxe\r\n++xF3TAnCnWfRL8deXc4HaiWBkJxfMiZhcJNVRrKn7lZPIUpopZav6AVGPgB\r\noWoCBmOrYTFGyv5t/uoN+EnG6n8F3xcel8j3eRWVpRiAYHwxTq81ak9BbZXx\r\nKKxwUOn9pV20sXPscJu+C98knVeFfWhJU/s=\r\n=WCPL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5a9c8ded252de1b5d949ada484c5cb10/vitest-expect-0.29.7.tgz", "_integrity": "sha512-UtG0tW0DP6b3N8aw7PHmweKDsvPv4wjGvrVZW7OSxaFg76ShtVdMiMcUkZJgCE8QWUmhwaM0aQhbbVLo4F4pkA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.7", "@vitest/utils": "0.29.7"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.7_1679344674299_0.6711396643032226", "host": "s3://npm-registry-packages"}}, "0.29.8": {"name": "@vitest/expect", "version": "0.29.8", "license": "MIT", "_id": "@vitest/expect@0.29.8", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6ecdd031b4ea8414717d10b65ccd800908384612", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.29.8.tgz", "fileCount": 5, "integrity": "sha512-xlcVXn5I5oTq6NiZSY3ykyWixBxr5mG8HYtjvpgg6KaqHm0mvhX18xuwl5YGxIRNt/A5jidd7CWcNHrSvgaQqQ==", "signatures": [{"sig": "MEUCIQDH5CRLFM2apP2j2ozM6yJjoWpNK6s5loYTZtgAgAGoFgIgZHziNjEM/PNELfwut8J+HOsqbh0XAoDHWbcOsRPwvW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIuepACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXHBAAm2XvcsFqVVwWVQNyxmoY0QmTtYYTP9na3pkDdWZEtlypAEhL\r\nfsfPj2KY7YQm8wVCB87/IodNA3pAKeBv9RnLiw5VtgGqawNrKh5EbBVDk1PB\r\nYxJAgri9iI+cXlu7uIhONgtK7gT6xfxoywEQqXvf95b+C2xbQGHtGgNQLi2S\r\nlWpDwn3ttjY5QzcQ4kRzmkjf+qOfV70DCW9DGlkzmPnVnq1V16FBY+eVpN6d\r\ntvnoBwA4RNIFKhcgtAh8ejSz1z72IfPJsYmh6MNMbQ26yJRTmx3xo5/t3qQw\r\nJi7aWAN/dez9UvXrP0VAhiyNGdmRsZV0NUUmsFTBkYhEXuzL/zQsva7ydzjF\r\nSBtzSeiG53e2HMg8jkn3SEHciNdvc4cloSwdqZOEow2/QMzgG7bKlj4ZnSIk\r\nVs84GIXG3qy1k7gRy4OIIF2serkJAfF9ZWo+QFi3T+gHkkwMxSbmELlGtfcc\r\nnw17+aupJ+mV6kMYFVt7W6BzwM8RFB0NCoWs7qGX3dzTjwhnrDQdiqUtWWD3\r\nrTGwzUauqN1OjC4dDszcKU0126K5iY4oxZ4cX4yhLop3+WEaos+Gvzj/ufLm\r\noq96yvf18v1eIP9A6N+uDoGElDAf9+c7nVgQiQRFow2z59hvtyRnb+f2C6Zn\r\n3iO+3yHadmsP3nVKhX8bOS2LlVQU6F0G3WA=\r\n=Hb+B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.29.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/14186b2029a4ae3996974dea65980eea/vitest-expect-0.29.8.tgz", "_integrity": "sha512-xlcVXn5I5oTq6NiZSY3ykyWixBxr5mG8HYtjvpgg6KaqHm0mvhX18xuwl5YGxIRNt/A5jidd7CWcNHrSvgaQqQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.29.8", "@vitest/utils": "0.29.8"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.29.8_1680009129629_0.7739974018990108", "host": "s3://npm-registry-packages"}}, "0.30.0": {"name": "@vitest/expect", "version": "0.30.0", "license": "MIT", "_id": "@vitest/expect@0.30.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4c26d4cbe30784b56b462a83eb1b573784ba9280", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.30.0.tgz", "fileCount": 6, "integrity": "sha512-b/jLWBqi6WQHfezWm8VjgXdIyfejAurtxqdyCdDqoToCim5W/nDxKjFAADitEHPz80oz+IP+c+wmkGKBucSpiw==", "signatures": [{"sig": "MEUCIQCZ1Y3JYvzJDOi1Ts1nQb+a5zAd7Qe/5lHRT13SXRBuEwIgDKKgINgIsSVriEwOBt7Py9+cPvnkl6//OotIRKmUn+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMr/3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtPg/+IK963cPKHI6lzZCY2/8EYbfjL++cwDZuPqStrDeX7Bjigyvr\r\njy6sB7k4z56yx2CmhAiGDtZDGj/wofgYueT4txIE0ckN5OzS+nC7fh7/CWp6\r\n48KvPiJjeYfESmbiSrb0w+EuDAbgdk90RvGE/QPqw/aDMVDrFfGYF7CkmUJ3\r\n62j6BqPwajdR0wb/ENNKAgNA/H2gBxi8YPhi/t4ywf6hZgWzgYKTQ8V90Tgi\r\nqrQE50ROAjkI4JrnI3lC/xvlcjZeejo2NYBGs8FoUohOHkm7RlYIWxO1gLwR\r\nfZjAHeNKf4RJ+NJRWgqj2wU9Z5aeZeDYBpNJjW7W4UfihC9Ous0wR5G0EsZm\r\nDhQ2eOnFltxK1NH9S4klFlCnAYlKIcfPs9VhJBiljPj2RkzbPAJ0pOfWts8x\r\nVbaiciSA8SrpHj3TX6ROfe4FhVuxtI+bEkXiecT0VkxCDwDl4oGWHKV7qwX+\r\nNvLVDQW4L3JlHQDALMgN3/CBPVAjFzQYPZb69vm01GjHgS3q+ZEriRCDU1Dd\r\nZizkDBtEIEVJYrwJiN03ZRjlCvELIT84L8pH35FOIvGapGP/QyPLSN9QbP1H\r\n0OEKSHcoYiH2HC3J9ub69wLeBoIISKei2SLwm0Nvx8ANm53hdoRjyGf8pomv\r\ntxhOYovvMHIJvlb7GyvO7l4iE7g8iqBDtcw=\r\n=7GZH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.30.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1b0c13b8d4c6c9abb4be5e9479bf2090/vitest-expect-0.30.0.tgz", "_integrity": "sha512-b/jLWBqi6WQHfezWm8VjgXdIyfejAurtxqdyCdDqoToCim5W/nDxKjFAADitEHPz80oz+IP+c+wmkGKBucSpiw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.30.0", "@vitest/utils": "0.30.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.30.0_1681047543470_0.5516806257648403", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "@vitest/expect", "version": "0.30.1", "license": "MIT", "_id": "@vitest/expect@0.30.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3c92a3fc23a198315ce8cd16689dc2d5aeac40b8", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.30.1.tgz", "fileCount": 6, "integrity": "sha512-c3kbEtN8XXJSeN81iDGq29bUzSjQhjES2WR3aColsS4lPGbivwLtas4DNUe0jD9gg/FYGIteqOenfU95EFituw==", "signatures": [{"sig": "MEUCIQCBf9m9usD4rOJiGoETsr5BCpvMQiSiuvrKd16UR1e+XwIgaDHeghzVKMhBHCkvprZ+rJ5CXWokNhi1+35FioAep4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUP2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnrQ//UIy0JXlQbo5TPG3+B/UTd3WGNtiw39qV0TUA4IQlRFfuCj9N\r\nuzrxBuXb3BnQthKVz1BipZi546jjyMgBZnRfaQLX/BOAMQQeP8wYmHXj9Gs7\r\nFkVSmAFRDvldQhJFQlhHCASNGLI/u/auVeZ4VikWFE53pTIfgrNbtDmrG4gv\r\n9wHy0ryxB25R4rYQglwQFZLVkJQYSdlJ7cY3zrJFxfVmenE3TIq0elo7sQco\r\nDU0bgMD+Z+XzxedFlno/1Idlw54y9/+YczJWaBgAMfibqLHzSksudRdK7ERL\r\niSWJ7ZIy7OriSYhgr0j4acQpMfIYn68zOxqwZeUg3sTC8+tc5R6Nbe1o1Vpk\r\nLSF3uvI6CbHyVT/tYreJj4ZGsr3mWi5sjEwC7lNqFh5oFG2G4MBlaNpc49nv\r\nSR4Tyt1KiANRsCJhsSH06B42jL8n+OFAQmbjEzTDp5IvuR+kCkCvIySWm+cV\r\ntfvd27uRhQBjBsn1REFIxwQBGf74PfkLukrBaLyWEta5/l3BQzS0AekeAqgl\r\nQ8jG/N0PuTBX+WmfgkdCqVqwo2g6xpKzHz9HV9vvhUP8OqVPLWK+G/VYB0tG\r\ndZTUiOSOhTbIcKlt5GgvIqrjHdWR4Pbiy7vkMbVcAFru54taR1LwqlUv1EIw\r\nvuWsg5j2yDXzdP/uNmzkoGQGh6Wh4vZaak8=\r\n=QX/P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.30.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/392cf7a7e3c03893e36f7038abc2af76/vitest-expect-0.30.1.tgz", "_integrity": "sha512-c3kbEtN8XXJSeN81iDGq29bUzSjQhjES2WR3aColsS4lPGbivwLtas4DNUe0jD9gg/FYGIteqOenfU95EFituw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.30.1", "@vitest/utils": "0.30.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.30.1_1681212406228_0.8382300687609443", "host": "s3://npm-registry-packages"}}, "0.31.0": {"name": "@vitest/expect", "version": "0.31.0", "license": "MIT", "_id": "@vitest/expect@0.31.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "37ab35d4f75c12826c204f2a0290e0c2e5ef1192", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.31.0.tgz", "fileCount": 6, "integrity": "sha512-Jlm8ZTyp6vMY9iz9Ny9a0BHnCG4fqBa8neCF6Pk/c/6vkUk49Ls6UBlgGAU82QnzzoaUs9E/mUhq/eq9uMOv/g==", "signatures": [{"sig": "MEQCIAGVtNX23gatrdZa23YcFIzbie7TUvu0HfRmmpkoRrylAiB/6lmyk+/Dgyt4pDMx/GKZm0wQ1KuyekXeArHETqS7/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqMgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSoBAAoXg7Nc2kDJQH2HYIqmE/eYbDmh4fJgrTx9jkCgSGICnwaphC\r\nbUtQjZBhX9dTKxTBCIk7uHEETyTtQHM0TihS++4Clr2f1+7SBRkjW2Tt8ga6\r\n497cFb0LFQTrBFuZ79ZrPUu0sHo2UDKiilRGMI0b62BwXBP8gYmEbMsy8e4H\r\nDP76Qs5EiltuERXHttbBeXoDJgsIjKdwRAK4eTWkBKEAOsTkPetNR93Ohzmx\r\nwvhP8yrvSRitJarJr0yldzO46TTAbMHMy6tIdGwxPMEy2q6Ff07GjHU323wR\r\n0WC2MlA0gom+2UZh5HvUBN+K1x33D098hXecqGceRXi4ZRtqwF27hwoLNclC\r\n7N8Hg/0gi42LeJBDXC38BZK0T/TLneG+MezSeA9HFKHuPi/PsIbZYHWWGnfy\r\nBjO7sYVYepatXqAuXxNoPK1hX5iHqd/ceIXYEACbiHaejxlqn+4pFqjdLs4f\r\nASgaCLaw/9D2b44BtBePc/RdPzv90+HrSiNJLX/gPUsI5Rnx2Sh5DIyxo0Hx\r\nRGmsoTZv1ivLeZGMfb3xTW0KjrFxZPH3ECAQjS5HvneefZ0Y/0FvCTNb9XwI\r\nwdpVcaVHKW1l3V+qAYweRiO+xwNEXH0wEdGNhWbytdtAzr8ZV1i0v4oPMaLV\r\nI3MKx07xadc3kC5d0QL5EsQLehbHrf8y8G0=\r\n=5PPH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.31.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/23148cb685d93ebf318088fadee63ee6/vitest-expect-0.31.0.tgz", "_integrity": "sha512-Jlm8ZTyp6vMY9iz9Ny9a0BHnCG4fqBa8neCF6Pk/c/6vkUk49Ls6UBlgGAU82QnzzoaUs9E/mUhq/eq9uMOv/g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.31.0", "@vitest/utils": "0.31.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.31.0_1683137312722_0.7246014199548638", "host": "s3://npm-registry-packages"}}, "0.31.1": {"name": "@vitest/expect", "version": "0.31.1", "license": "MIT", "_id": "@vitest/expect@0.31.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "db8cb5a14a91167b948f377b9d29442229c73747", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.31.1.tgz", "fileCount": 6, "integrity": "sha512-BV1LyNvhnX+eNYzJxlHIGPWZpwJFZaCcOIzp2CNG0P+bbetenTupk6EO0LANm4QFt0TTit+yqx7Rxd1qxi/SQA==", "signatures": [{"sig": "MEUCIAt19cWyj+WLkC4mbbYwJk8jWHQUqiwG0vrwNp/w4bhLAiEA7F544+PkXPOqUcZThU9mTX6NZSGw0+vwx/cXRLHv7mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102602}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.31.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/19141ef23256d5c6f09f2c4d61034d2e/vitest-expect-0.31.1.tgz", "_integrity": "sha512-BV1LyNvhnX+eNYzJxlHIGPWZpwJFZaCcOIzp2CNG0P+bbetenTupk6EO0LANm4QFt0TTit+yqx7Rxd1qxi/SQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.15.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.31.1", "@vitest/utils": "0.31.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.31.1_1684333415108_0.06664208340071243", "host": "s3://npm-registry-packages"}}, "0.31.2": {"name": "@vitest/expect", "version": "0.31.2", "license": "MIT", "_id": "@vitest/expect@0.31.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a00f3e60948b3680728061ca36f0420b2ccb2c3f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.31.2.tgz", "fileCount": 6, "integrity": "sha512-AOuh2NLN9zJ0SkvsItRkS/W39akYpUvo5LOnay3zEhGSnRgivPu2D3S8QlMij1hFMQcX+dlMilPgJatUHiGQ4A==", "signatures": [{"sig": "MEUCICIsQx/1HeItk9xfPBdE3dbzMKyvlVi2s5Hq4R3F4wwUAiEA+syYjMkFH3JFUKh8cCiA7v0tL8MqCFy9X8pwyE7PoHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102762}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.31.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1d96c4a35f66050ad03ff762533529d9/vitest-expect-0.31.2.tgz", "_integrity": "sha512-AOuh2NLN9zJ0SkvsItRkS/W39akYpUvo5LOnay3zEhGSnRgivPu2D3S8QlMij1hFMQcX+dlMilPgJatUHiGQ4A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.31.2", "@vitest/utils": "0.31.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.31.2_1685451899598_0.8288267037728094", "host": "s3://npm-registry-packages"}}, "0.31.3": {"name": "@vitest/expect", "version": "0.31.3", "license": "MIT", "_id": "@vitest/expect@0.31.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1a1d70822d17e8aa76aec527da0a74dfd79d5f6f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.31.3.tgz", "fileCount": 6, "integrity": "sha512-J+npV64YwKRJN7hA8KVnuyGi5k5mIaq4XmqIxKbRUX0BRBkU1xZ7gZeTwQ4S4dBTcvSvcoxceBNfMTnOdTlg3g==", "signatures": [{"sig": "MEYCIQC0QT6fCfUaa7ubUAg1qCCACFJ4xU9+EOta2BZDD/6ymQIhAJ6uk3+I46b2URRTZkjR4vEPXwkXlEodblWVpoElgy9K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102762}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.31.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3a6e20bd5a27d75bb256ffb0b3d89e0f/vitest-expect-0.31.3.tgz", "_integrity": "sha512-J+npV64YwKRJN7hA8KVnuyGi5k5mIaq4XmqIxKbRUX0BRBkU1xZ7gZeTwQ4S4dBTcvSvcoxceBNfMTnOdTlg3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.31.3", "@vitest/utils": "0.31.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.31.3_1685544573931_0.3204481883026886", "host": "s3://npm-registry-packages"}}, "0.31.4": {"name": "@vitest/expect", "version": "0.31.4", "license": "MIT", "_id": "@vitest/expect@0.31.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "115c517404488bf3cb6ce4ac411c40d8e86891b8", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.31.4.tgz", "fileCount": 6, "integrity": "sha512-tibyx8o7GUyGHZGyPgzwiaPaLDQ9MMuCOrc03BYT0nryUuhLbL7NV2r/q98iv5STlwMgaKuFJkgBW/8iPKwlSg==", "signatures": [{"sig": "MEUCIQDnzz/tpRQulDuM3OqrE30yuW6UorLjDCtITDSc/g9B2gIgPDiGUlABYXr1h0tjPBoMw0DqpY2QKf8Xpu4MAE/i9/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102762}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.31.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b1f517d7b003f2410a6761cca50e5cb9/vitest-expect-0.31.4.tgz", "_integrity": "sha512-tibyx8o7GUyGHZGyPgzwiaPaLDQ9MMuCOrc03BYT0nryUuhLbL7NV2r/q98iv5STlwMgaKuFJkgBW/8iPKwlSg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.31.4", "@vitest/utils": "0.31.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.31.4_1685613318746_0.6669858899351042", "host": "s3://npm-registry-packages"}}, "0.32.0": {"name": "@vitest/expect", "version": "0.32.0", "license": "MIT", "_id": "@vitest/expect@0.32.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5b10cdc822d54cb870aef917462fbf847202e4e0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.32.0.tgz", "fileCount": 6, "integrity": "sha512-VxVHhIxKw9Lux+O9bwLEEk2gzOUe93xuFHy9SzYWnnoYZFYg1NfBtnfnYWiJN7yooJ7KNElCK5YtA7DTZvtXtg==", "signatures": [{"sig": "MEQCIHLcQS2//tsybi9Un+fRuwHooMOYgsjMis+o+xdu4i9PAiAh/tQaYderPYxOxvQV2/rBn23Y5t2r5pdHPm3ncOmw1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104554}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.32.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b45b9f3d03263484fa3ac240a90d6bb1/vitest-expect-0.32.0.tgz", "_integrity": "sha512-VxVHhIxKw9Lux+O9bwLEEk2gzOUe93xuFHy9SzYWnnoYZFYg1NfBtnfnYWiJN7yooJ7KNElCK5YtA7DTZvtXtg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.32.0", "@vitest/utils": "0.32.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.32.0_1686071083287_0.5532958072533716", "host": "s3://npm-registry-packages"}}, "0.32.1": {"name": "@vitest/expect", "version": "0.32.1", "license": "MIT", "_id": "@vitest/expect@0.32.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5b453eb7d07ec14512cb891d6cf85b6cb5a0c1be", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.32.1.tgz", "fileCount": 6, "integrity": "sha512-Ca5AlxzyE7XoQULdtWcoxavYTcB42Y9XvaSg/JnFyU1nlR4+ouIY4lYpEXu2qY4xha8Q5jKgW/sH0yqXqCrm2w==", "signatures": [{"sig": "MEYCIQCOnLalWYyMMK4rhj9yqmqHK56iNrP/Sf+sAVMHeLD9ogIhAJXyr3i7c/ZGAGHZMhlvn+Lk3AY8uqn7NPtpBp8Yilr+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104109}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.32.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/64e4f23945f3cbe30f9275c3752137b8/vitest-expect-0.32.1.tgz", "_integrity": "sha512-Ca5AlxzyE7XoQULdtWcoxavYTcB42Y9XvaSg/JnFyU1nlR4+ouIY4lYpEXu2qY4xha8Q5jKgW/sH0yqXqCrm2w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.32.1", "@vitest/utils": "0.32.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.32.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.32.1_1686918201423_0.7558543300902736", "host": "s3://npm-registry-packages"}}, "0.32.2": {"name": "@vitest/expect", "version": "0.32.2", "license": "MIT", "_id": "@vitest/expect@0.32.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8111f6ab1ff3b203efbe3a25e8bb2d160ce4b720", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.32.2.tgz", "fileCount": 6, "integrity": "sha512-6q5yzweLnyEv5Zz1fqK5u5E83LU+gOMVBDuxBl2d2Jfx1BAp5M+rZgc5mlyqdnxquyoiOXpXmFNkcGcfFnFH3Q==", "signatures": [{"sig": "MEYCIQCRvO97w1HoOmvEbrZsZ7U5AcCXUWjcz+BtDNK1VmgjFQIhANbY7u6zAt12iYnD7Zjy7CAmeDD2f76k65BVr7N/dsm1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104109}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.32.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/df5236064c79a0fc69aa2bf3e21ca964/vitest-expect-0.32.2.tgz", "_integrity": "sha512-6q5yzweLnyEv5Zz1fqK5u5E83LU+gOMVBDuxBl2d2Jfx1BAp5M+rZgc5mlyqdnxquyoiOXpXmFNkcGcfFnFH3Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.32.2", "@vitest/utils": "0.32.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.32.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.32.2_1686931571940_0.06877379441343145", "host": "s3://npm-registry-packages"}}, "0.32.3": {"name": "@vitest/expect", "version": "0.32.3", "license": "MIT", "_id": "@vitest/expect@0.32.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "40e23902d5cfe33c10a35f9b6d0ceea8b19d5b5f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.32.3.tgz", "fileCount": 6, "integrity": "sha512-rVxYct5myVIaQmeCk2ZJV3d0Af83KK2u0NnKsDP5dUJhxOG35+LYw3OCe+lmZ42KQKUTRy8crfWFIG5GL85V0A==", "signatures": [{"sig": "MEQCIHetPr6SPBD/YhZrtp9H+qtxZMImuNyQRBKUO9a10e2SAiBemt+NFXkXKE6oDOYztsZYgtxQV89J3sTjki5plgMMJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104639}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.32.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8436a5c0e2da616dff977df0a67aa59c/vitest-expect-0.32.3.tgz", "_integrity": "sha512-rVxYct5myVIaQmeCk2ZJV3d0Af83KK2u0NnKsDP5dUJhxOG35+LYw3OCe+lmZ42KQKUTRy8crfWFIG5GL85V0A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.32.3", "@vitest/utils": "0.32.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.32.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.32.3_1688373352244_0.3494040872114508", "host": "s3://npm-registry-packages"}}, "0.32.4": {"name": "@vitest/expect", "version": "0.32.4", "license": "MIT", "_id": "@vitest/expect@0.32.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4aa4eec78112cdbe299834b965420d4fb3afa91d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.32.4.tgz", "fileCount": 6, "integrity": "sha512-m7EPUqmGIwIeoU763N+ivkFjTzbaBn0n9evsTOcde03ugy2avPs3kZbYmw3DkcH1j5mxhMhdamJkLQ6dM1bk/A==", "signatures": [{"sig": "MEUCIEzlMTNAJbYymlUYCjWaQcQaoozIskKnsBbP2FM1P8PyAiEAgYGmJtfpzw+BqBLyDm9DKsoga/FWUNdAqEBIL07yC5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104639}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.32.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f32961dff60e22260d8a3d2dca778f85/vitest-expect-0.32.4.tgz", "_integrity": "sha512-m7EPUqmGIwIeoU763N+ivkFjTzbaBn0n9evsTOcde03ugy2avPs3kZbYmw3DkcH1j5mxhMhdamJkLQ6dM1bk/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.32.4", "@vitest/utils": "0.32.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.32.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.32.4_1688382384755_0.08437344573462369", "host": "s3://npm-registry-packages"}}, "0.33.0": {"name": "@vitest/expect", "version": "0.33.0", "license": "MIT", "_id": "@vitest/expect@0.33.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f48652591f3573ad6c2db828ad358d5c078845d3", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.33.0.tgz", "fileCount": 6, "integrity": "sha512-sVNf+Gla3mhTCxNJx+wJLDPp/WcstOe0Ksqz4Vec51MmgMth/ia0MGFEkIZmVGeTL5HtjYR4Wl/ZxBxBXZJTzQ==", "signatures": [{"sig": "MEQCIEtVYoWwMmoDzPRNqcbBbiM7vipWtbIDgFXOloK1P5cqAiAKOQFUQHnJkDaxebMDwz27hCImMQr00NUbnQ7nTWDmEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104639}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.33.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/203cfb8c31e88f11c984d25d0d4ad04b/vitest-expect-0.33.0.tgz", "_integrity": "sha512-sVNf+Gla3mhTCxNJx+wJLDPp/WcstOe0Ksqz4Vec51MmgMth/ia0MGFEkIZmVGeTL5HtjYR4Wl/ZxBxBXZJTzQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "8.19.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.33.0", "@vitest/utils": "0.33.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.33.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.33.0_1688652702183_0.2922127570285866", "host": "s3://npm-registry-packages"}}, "0.34.0": {"name": "@vitest/expect", "version": "0.34.0", "license": "MIT", "_id": "@vitest/expect@0.34.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6f0b8c36e1b30f7d86e224c826e20691b4f47d98", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.0.tgz", "fileCount": 6, "integrity": "sha512-d1ZU0XomWFAFyYIc6uNuY0N8NJIWESyO/6ZmwLvlHZw0GevH4AEEpq178KjXIvSCrbHN0GnzYzitd0yjfy7+Ow==", "signatures": [{"sig": "MEUCIDGElcgH5D7kXTco453iQUlIsKUhDFn95wWCIL/W6U9fAiEAv7qOTY4KCADiZUmqqxBXcugdKftqdJ8eFKp9/ywydEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104841}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/45efb388a11163a7fa70c00080b8c666/vitest-expect-0.34.0.tgz", "_integrity": "sha512-d1ZU0XomWFAFyYIc6uNuY0N8NJIWESyO/6ZmwLvlHZw0GevH4AEEpq178KjXIvSCrbHN0GnzYzitd0yjfy7+Ow==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.0", "@vitest/utils": "0.34.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.0_1690904523467_0.33696376561192465", "host": "s3://npm-registry-packages"}}, "0.34.1": {"name": "@vitest/expect", "version": "0.34.1", "license": "MIT", "_id": "@vitest/expect@0.34.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2ba6cb96695f4b4388c6d955423a81afc79b8da0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.1.tgz", "fileCount": 6, "integrity": "sha512-q2CD8+XIsQ+tHwypnoCk8Mnv5e6afLFvinVGCq3/BOT4kQdVQmY6rRfyKkwcg635lbliLPqbunXZr+L1ssUWiQ==", "signatures": [{"sig": "MEYCIQCNYg7AI59RNq/fVyMbScGO9OEje8lgFc3hQlQb3bUJfAIhAKr5/bRhqaRGxMfP4hsz1xwEeGyJ6ybEltvaPETiAK0c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104841}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5ec2aed50cb68b6623a4ba455e9496d5/vitest-expect-0.34.1.tgz", "_integrity": "sha512-q2CD8+XIsQ+tHwypnoCk8Mnv5e6afLFvinVGCq3/BOT4kQdVQmY6rRfyKkwcg635lbliLPqbunXZr+L1ssUWiQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.1", "@vitest/utils": "0.34.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.1_1690908836515_0.7457950773803066", "host": "s3://npm-registry-packages"}}, "0.34.2": {"name": "@vitest/expect", "version": "0.34.2", "license": "MIT", "_id": "@vitest/expect@0.34.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2bd09c20b828f8f9da625c203d88003a2b69e314", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.2.tgz", "fileCount": 6, "integrity": "sha512-EZm2dMNlLyIfDMha17QHSQcg2KjeAZaXd65fpPzXY5bvnfx10Lcaz3N55uEe8PhF+w4pw+hmrlHLLlRn9vkBJg==", "signatures": [{"sig": "MEUCICNwceAs04XiUbEv6Nhw8ZIGDkBLv1vDXjJZ8hHIRQvmAiEAo7cTuld6mSrv7IoMQuKuuwXcUPMggAALniopKfmVPIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104841}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5d891b70377f528429031f3387cb531b/vitest-expect-0.34.2.tgz", "_integrity": "sha512-EZm2dMNlLyIfDMha17QHSQcg2KjeAZaXd65fpPzXY5bvnfx10Lcaz3N55uEe8PhF+w4pw+hmrlHLLlRn9vkBJg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.2", "@vitest/utils": "0.34.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.2_1692267007446_0.2119483755248377", "host": "s3://npm-registry-packages"}}, "0.34.3": {"name": "@vitest/expect", "version": "0.34.3", "license": "MIT", "_id": "@vitest/expect@0.34.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "576e1fd6a3a8b8b7a79a06477f3d450a77d67852", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.3.tgz", "fileCount": 6, "integrity": "sha512-F8MTXZUYRBVsYL1uoIft1HHWhwDbSzwAU9Zgh8S6WFC3YgVb4AnFV2GXO3P5Em8FjEYaZtTnQYoNwwBrlOMXgg==", "signatures": [{"sig": "MEUCIQC9DsLOs2HlP4o3/05CBZcFZHAEYqsmuvi7Uy3nzqYergIgKbfRE2NIfO6IzaGHriNdLDi7XNL9mUz6wbDUZuNFzlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104841}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1760689f8aaccfe4999ef8b9bed014a0/vitest-expect-0.34.3.tgz", "_integrity": "sha512-F8MTXZUYRBVsYL1uoIft1HHWhwDbSzwAU9Zgh8S6WFC3YgVb4AnFV2GXO3P5Em8FjEYaZtTnQYoNwwBrlOMXgg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.3", "@vitest/utils": "0.34.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.3_1692948626186_0.005482150535065067", "host": "s3://npm-registry-packages"}}, "0.34.4": {"name": "@vitest/expect", "version": "0.34.4", "license": "MIT", "_id": "@vitest/expect@0.34.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f857a83268b9e9d4e9410fe168cb206033838102", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.4.tgz", "fileCount": 6, "integrity": "sha512-XlMKX8HyYUqB8dsY8Xxrc64J2Qs9pKMt2Z8vFTL4mBWXJsg4yoALHzJfDWi8h5nkO4Zua4zjqtapQ/IluVkSnA==", "signatures": [{"sig": "MEUCIA1MNzYxJOtaIjB/FlXpjT5thj79OIRGiVzbskncMW98AiEA3KqIuJDduD7lYH52wunWXqQ7XYqRUBKwaXQetBHGRqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104951}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d06a23c8048fcf7691f5023f572c2dee/vitest-expect-0.34.4.tgz", "_integrity": "sha512-XlMKX8HyYUqB8dsY8Xxrc64J2Qs9pKMt2Z8vFTL4mBWXJsg4yoALHzJfDWi8h5nkO4Zua4zjqtapQ/IluVkSnA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.4", "@vitest/utils": "0.34.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.4_1694169275250_0.7054951536240368", "host": "s3://npm-registry-packages"}}, "0.34.5": {"name": "@vitest/expect", "version": "0.34.5", "license": "MIT", "_id": "@vitest/expect@0.34.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1f58829e746311162220d6580f72d6329efb9081", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.5.tgz", "fileCount": 6, "integrity": "sha512-/3RBIV9XEH+nRpRMqDJBufKIOQaYUH2X6bt0rKSCW0MfKhXFLYsR5ivHifeajRSTsln0FwJbitxLKHSQz/Xwkw==", "signatures": [{"sig": "MEQCICkV5AGCoSWdntm2aKYsm7E4Eyp877/3qCKLfID3J3XCAiBG/QNNW9YMvMZofilJjkT78hyOEnEqJ4EfG7lenwKXHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106173}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ef123ceb9777543a4601ce724741564e/vitest-expect-0.34.5.tgz", "_integrity": "sha512-/3RBIV9XEH+nRpRMqDJBufKIOQaYUH2X6bt0rKSCW0MfKhXFLYsR5ivHifeajRSTsln0FwJbitxLKHSQz/Xwkw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.7", "@vitest/spy": "0.34.5", "@vitest/utils": "0.34.5"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.5_1695304247567_0.3794601616807436", "host": "s3://npm-registry-packages"}}, "0.34.6": {"name": "@vitest/expect", "version": "0.34.6", "license": "MIT", "_id": "@vitest/expect@0.34.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "608a7b7a9aa3de0919db99b4cc087340a03ea77e", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.6.tgz", "fileCount": 6, "integrity": "sha512-QUzKpUQRc1qC7qdGo7rMK3AkETI7w18gTCUrsNnyjjJKYiuUB9+TQK3QnR1unhCnWRC0AbKv2omLGQDF/mIjOw==", "signatures": [{"sig": "MEYCIQDi6QW2lfAFtzOlz84Um/6mGTaFYXAbt9vgc6PRASIU+AIhANSNEF2/n/IpxM1zKkKSq1MTIQiK/tS+dmEHAUIt7nj0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106174}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9787b5edbba58a5d457482f5c85d2d51/vitest-expect-0.34.6.tgz", "_integrity": "sha512-QUzKpUQRc1qC7qdGo7rMK3AkETI7w18gTCUrsNnyjjJKYiuUB9+TQK3QnR1unhCnWRC0AbKv2omLGQDF/mIjOw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "0.34.6", "@vitest/utils": "0.34.6"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.6"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.6_1695972832697_0.3241722035816863", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "@vitest/expect", "version": "1.0.0-beta.0", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "07e516097f26017485ac90ced0388d2ec7fe4c26", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.0.tgz", "fileCount": 7, "integrity": "sha512-1NgozGzQNb/gv5+Bh5j3J9DFXzAV+geDo0KXOiaWIG/DRXJYH1fCcw3veoyL/d5zK+KqV0HRIM3rVQEuIq/1Qg==", "signatures": [{"sig": "MEUCIQDB6U5tS/ECu104eFJpmKnQxrm49o8Hvj8pYR7o9caDKgIgVmtdODMCXw+3ZVCcLimaa9eWhhVqoKO9Mbbd/flkyq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179610}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/e00c3035111280666bb373068c9a7008/vitest-expect-1.0.0-beta.0.tgz", "_integrity": "sha512-1NgozGzQNb/gv5+Bh5j3J9DFXzAV+geDo0KXOiaWIG/DRXJYH1fCcw3veoyL/d5zK+KqV0HRIM3rVQEuIq/1Qg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.0", "@vitest/utils": "1.0.0-beta.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "^4.3.6", "@vitest/runner": "1.0.0-beta.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.0_1696264834230_0.37240205809398597", "host": "s3://npm-registry-packages"}}, "0.34.7": {"name": "@vitest/expect", "version": "0.34.7", "license": "MIT", "_id": "@vitest/expect@0.34.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b07fb9ebfb8fb347f06a9cb33843ee2e5ba7d8a9", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-0.34.7.tgz", "fileCount": 6, "integrity": "sha512-G9iEtwrD6ZQ4MVHZufif9Iqz3eLtuwBBNx971fNAGPaugM7ftAWjQN+ob2zWhtzURp8RK3zGXOxVb01mFo3zAQ==", "signatures": [{"sig": "MEUCIQCoGWTt2jEepGV56rmYCvygYi5k8J/zIMK+CEeWWRpLDQIgOMRCfoVtKuuQODHVf5qB/f5MQPoXhjQmcmAwdcyhqwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106174}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-0.34.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/38441313f6d9c5d416f1081906686757/vitest-expect-0.34.7.tgz", "_integrity": "sha512-G9iEtwrD6ZQ4MVHZufif9Iqz3eLtuwBBNx971fNAGPaugM7ftAWjQN+ob2zWhtzURp8RK3zGXOxVb01mFo3zAQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "0.34.7", "@vitest/utils": "0.34.7"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@vitest/runner": "0.34.7"}, "_npmOperationalInternal": {"tmp": "tmp/expect_0.34.7_1696266223389_0.48174750835018254", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "@vitest/expect", "version": "1.0.0-beta.1", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "97db3b7ca8b258b8f304818a68ee7b187dbceaef", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.1.tgz", "fileCount": 8, "integrity": "sha512-FgpctOcKcHuroFgO5JMOpjk2F0YZvWH1cBPagoQcr0ckJUq3/V7udPxw7w/dBeyn/zqyhGfOzY4z9jLHaEbCqw==", "signatures": [{"sig": "MEUCIAPsRJuywkf9zzpn0l9bme/A0hgI0oRlBl1prVYQGi2VAiEA0AGfj1BuSdvJHhMGgBpPfH2VP3/402kg3dfBggTIQos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179675}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/747aea6eaf460bb82cda8f20d66d6134/vitest-expect-1.0.0-beta.1.tgz", "_integrity": "sha512-FgpctOcKcHuroFgO5JMOpjk2F0YZvWH1cBPagoQcr0ckJUq3/V7udPxw7w/dBeyn/zqyhGfOzY4z9jLHaEbCqw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.1", "@vitest/utils": "1.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "^4.3.6", "@vitest/runner": "1.0.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.1_1696332700124_0.03506059052129884", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "@vitest/expect", "version": "1.0.0-beta.2", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "190eb0bbf3d411bcbf1875c3d1e9d4c1a2d443d4", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.2.tgz", "fileCount": 8, "integrity": "sha512-u3CoUMNn+0SxGzqLC4hdbyoCACduaBmM2AcGWpqdweM7CS8PxfDhbbuhClLmLg7OOd1RjFAGO09x5iWQjX+Rmw==", "signatures": [{"sig": "MEUCIQDA9NYOYrVm6oNdxlAD3NL+7NV86ahzED4OzLM0q1OzlAIgYQ47an10jMiQIvUJ6oyXgc26UbyCo00AQDqlB+AxSX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182690}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/61e0c4ea848f9401c07a7c836747dfe0/vitest-expect-1.0.0-beta.2.tgz", "_integrity": "sha512-u3CoUMNn+0SxGzqLC4hdbyoCACduaBmM2AcGWpqdweM7CS8PxfDhbbuhClLmLg7OOd1RjFAGO09x5iWQjX+Rmw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.2", "@vitest/utils": "1.0.0-beta.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.2_1697182503268_0.8036395778556755", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "@vitest/expect", "version": "1.0.0-beta.3", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9fc5a19a6b53e4dc3fd5710b4d18c3a82710f3ed", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.3.tgz", "fileCount": 8, "integrity": "sha512-fbDKa7aj/xiCcS9X6RwlPXTZ5SNQpvPl0eswD+d3Lnp8gmuB8j2mb+CDUMe7VRg+ah5Cm/kqAc9DzYTe2IcvgQ==", "signatures": [{"sig": "MEUCIBGI6qH9Rk8/iBfT30IW9irDK7uNZh5g0tjkthl6MbnMAiEA54Ape2ynHoFTl5AqHtXvcRwE28cCBPEgDqec7y8WqEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182697}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2562dcf1d46426a188c16023ea1c58f0/vitest-expect-1.0.0-beta.3.tgz", "_integrity": "sha512-fbDKa7aj/xiCcS9X6RwlPXTZ5SNQpvPl0eswD+d3Lnp8gmuB8j2mb+CDUMe7VRg+ah5Cm/kqAc9DzYTe2IcvgQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.3", "@vitest/utils": "1.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0-beta.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.3_1698410750428_0.09743598107327767", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "@vitest/expect", "version": "1.0.0-beta.4", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "435a01c5aba0d7bda843becce4b2187ab71630dd", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.4.tgz", "fileCount": 8, "integrity": "sha512-JOpNEva2AFxfySH3F+X+hT52Kq/ZdIrGtzWYbj6yRuBuxFqM55n/7/jV4XtQG+XkmehP3OUZGx5zISOU8KHPQw==", "signatures": [{"sig": "MEUCICdIadJ/Iz8iW88iChq5Ygfu+88zP+7LF3J7HvmaGKdPAiEAiAwQ7fDBO+AHLtMj8qmrjiFzY0WcZ/z3J1jON+PaUjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183838}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/09ca555ab9151b2c870bbd80967f962c/vitest-expect-1.0.0-beta.4.tgz", "_integrity": "sha512-JOpNEva2AFxfySH3F+X+hT52Kq/ZdIrGtzWYbj6yRuBuxFqM55n/7/jV4XtQG+XkmehP3OUZGx5zISOU8KHPQw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.4", "@vitest/utils": "1.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0-beta.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.4_1699524824832_0.1625641709442034", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "@vitest/expect", "version": "1.0.0-beta.5", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4f98d2845a5e896bc4a95c3d073b5ea0361fb364", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.5.tgz", "fileCount": 7, "integrity": "sha512-q/TPdbXuEZZNFKILEVicojSWEq1y8qPLcAiZRQD8DsYUAV2cIjsD5lJWYaAjjUAV4lzovSci3KeISQdjUdfxQQ==", "signatures": [{"sig": "MEUCIQDL/wUePGc1vaU6THmePKmzLc3VsBAI9JX22pq8MVJqoAIgWOtoUGY9d4Fp6QXs6q1xCls+vhUk1+uLwvbOzGnYk/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136922}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/71925e902e93c05055de605615173674/vitest-expect-1.0.0-beta.5.tgz", "_integrity": "sha512-q/TPdbXuEZZNFKILEVicojSWEq1y8qPLcAiZRQD8DsYUAV2cIjsD5lJWYaAjjUAV4lzovSci3KeISQdjUdfxQQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.5", "@vitest/utils": "1.0.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0-beta.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.5_1700300679329_0.189524087257817", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "@vitest/expect", "version": "1.0.0-beta.6", "license": "MIT", "_id": "@vitest/expect@1.0.0-beta.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13a3c03404cc4b57329f064058f106a85f18c557", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0-beta.6.tgz", "fileCount": 7, "integrity": "sha512-leCesYErICeBQEz0Tzaopad1cnCUBfR1gDk2nFxHCChv7gdYTezcC2YRlPCwA8TuWGCXs8cdyTFO5R6QhUfY9A==", "signatures": [{"sig": "MEYCIQD37eyOYfNMR7brTYH4zDgAmM/jojHhCxgd4qIoNzRiiAIhANLrlh0tXYHEOKR9dyk0ZZyXmSboWNqXhKqnGAaGFtt4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136922}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0-beta.6.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/e8afadf86a3c1ebb430028b74b5d9b80/vitest-expect-1.0.0-beta.6.tgz", "_integrity": "sha512-leCesYErICeBQEz0Tzaopad1cnCUBfR1gDk2nFxHCChv7gdYTezcC2YRlPCwA8TuWGCXs8cdyTFO5R6QhUfY9A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0-beta.6", "@vitest/utils": "1.0.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0-beta.6", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0-beta.6_1701192443753_0.07510432032953718", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@vitest/expect", "version": "1.0.0", "license": "MIT", "_id": "@vitest/expect@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4c12343a456c2fc6fdec2d55b0d8418afb8165d7", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-EbqHCSzQhAY8Su/uLsMCDXkC26LyqQO54kAqJy/DubBqwpRre1iMzvDMPWx+YPfNIN3w7/ydKaJWjH6qRoz0fA==", "signatures": [{"sig": "MEYCIQDzpR8pdGvR5MHNw2esqHr/k1Yq0XG9sAJEu6jRSMqhTgIhAMixfD/l2EoUKWLizI5uWA2iz4I0jmu1S/dKeJ6JaRG6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/47a8a366e95adc252e6fd8dc78f82f9c/vitest-expect-1.0.0.tgz", "_integrity": "sha512-EbqHCSzQhAY8Su/uLsMCDXkC26LyqQO54kAqJy/DubBqwpRre1iMzvDMPWx+YPfNIN3w7/ydKaJWjH6qRoz0fA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.0", "@vitest/utils": "1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.0_1701704795586_0.4132695423808004", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vitest/expect", "version": "1.0.1", "license": "MIT", "_id": "@vitest/expect@1.0.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5e63902316a3c65948c6e36f284046962601fb88", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-3cdrb/eKD/0tygDX75YscuHEHMUJ70u3UoLSq2eqhWks57AyzvsDQbyn53IhZ0tBN7gA8Jj2VhXiOV2lef7thw==", "signatures": [{"sig": "MEQCIBv9waYSz9gnb7T+O7i0HH4XEI7zVoLXC6o75YfnHI03AiArGKeTokG4r80D01d0fVbVNMpKV8oB6mg7niIQUKx5xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3fda2cb7de2c6c65ddc2704388909300/vitest-expect-1.0.1.tgz", "_integrity": "sha512-3cdrb/eKD/0tygDX75YscuHEHMUJ70u3UoLSq2eqhWks57AyzvsDQbyn53IhZ0tBN7gA8Jj2VhXiOV2lef7thw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.1", "@vitest/utils": "1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.1_1701713098603_0.9525473504925208", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@vitest/expect", "version": "1.0.2", "license": "MIT", "_id": "@vitest/expect@1.0.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7fc5ee3fe0e649f5a5e3df1a9744efe0163d1237", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-mAIo/8uddSWkjQMLFcjqZP3WmkwvvN0OtlyZIu33jFnwme3vZds8m8EDMxtj+Uzni2DwtPfHNjJcTM8zTV1f4A==", "signatures": [{"sig": "MEQCIEVmG091sHcIZ6ANj+wUErLcEu4HRKnjTEf/e1arFzQkAiADosAvj8nYdYNDmwdku2Fc0ofMv0rq7TCO1/bX+hGIcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136997}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f2f28d5f87d533adeea319a05407e061/vitest-expect-1.0.2.tgz", "_integrity": "sha512-mAIo/8uddSWkjQMLFcjqZP3WmkwvvN0OtlyZIu33jFnwme3vZds8m8EDMxtj+Uzni2DwtPfHNjJcTM8zTV1f4A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.2", "@vitest/utils": "1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.2_1701943989902_0.3949810184663498", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@vitest/expect", "version": "1.0.3", "license": "MIT", "_id": "@vitest/expect@1.0.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5fb7c9bdf9babaa5a7d21ecffbbb63b865071689", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.3.tgz", "fileCount": 7, "integrity": "sha512-J+JzGw/uvlWI3D3g8s0ewQo7C32nieF5VqEJpmIgAr8CAK36GvIQrV90lChEgQy79iwK3zyQx4UhfMeIF4572g==", "signatures": [{"sig": "MEQCIHHHTKebSPJsdL1OC+YcGWx3cO0WnW5z95Ybm1leH92ZAiBFNvOuPGdtBtrDLmbI0wxLiiXJYdKGYAHwTgveXXGU+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136997}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/7e94a7d94560aad153d117bf41700d52/vitest-expect-1.0.3.tgz", "_integrity": "sha512-J+JzGw/uvlWI3D3g8s0ewQo7C32nieF5VqEJpmIgAr8CAK36GvIQrV90lChEgQy79iwK3zyQx4UhfMeIF4572g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.3", "@vitest/utils": "1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.3_1702127155609_0.15208127623675782", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@vitest/expect", "version": "1.0.4", "license": "MIT", "_id": "@vitest/expect@1.0.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2751018b6e527841043e046ff424304453a0a024", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.0.4.tgz", "fileCount": 7, "integrity": "sha512-/NRN9N88qjg3dkhmFcCBwhn/Ie4h064pY3iv7WLRsDJW7dXnEgeoa8W9zy7gIPluhz6CkgqiB3HmpIXgmEY5dQ==", "signatures": [{"sig": "MEUCIBcMjLninhetw6KC5hU/dbmZTzFQ+j7dwVLkz6TzfBHJAiEAm+KVeudrkpax9gN5u7ikFtPxb3ys7qwimVf3PxrocDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136997}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.0.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/0d385b14172c4c86264fa274986e8ab8/vitest-expect-1.0.4.tgz", "_integrity": "sha512-/NRN9N88qjg3dkhmFcCBwhn/Ie4h064pY3iv7WLRsDJW7dXnEgeoa8W9zy7gIPluhz6CkgqiB3HmpIXgmEY5dQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.0.4", "@vitest/utils": "1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.0.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.0.4_1702148727492_0.8765173756387372", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@vitest/expect", "version": "1.1.0", "license": "MIT", "_id": "@vitest/expect@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f58eef7de090ad65f30bb93ec54fa9f94c9d1d5d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-9IE2WWkcJo2BR9eqtY5MIo3TPmS50Pnwpm66A6neb2hvk/QSLfPXBz2qdiwUOQkwyFuuXEUj5380CbwfzW4+/w==", "signatures": [{"sig": "MEQCIDjWNOue3QIje2GL69YzNfewJTxU8y6SNIkTp76Azd2DAiAc8+WFE0XaV9zn+K0qC8qsOait50CeOUmDtZm+32fSMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137030}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.1.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/33e84c4bc226108184a47a62d1b85dc7/vitest-expect-1.1.0.tgz", "_integrity": "sha512-9IE2WWkcJo2BR9eqtY5MIo3TPmS50Pnwpm66A6neb2hvk/QSLfPXBz2qdiwUOQkwyFuuXEUj5380CbwfzW4+/w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.1.0", "@vitest/utils": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.1.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.1.0_1702994799209_0.5655578649413557", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@vitest/expect", "version": "1.1.1", "license": "MIT", "_id": "@vitest/expect@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6b00a5e9ecccdc9da112e89214693a857564e39c", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.1.1.tgz", "fileCount": 7, "integrity": "sha512-Qpw01C2Hyb3085jBkOJLQ7HRX0Ncnh2qV4p+xWmmhcIUlMykUF69zsnZ1vPmAjZpomw9+5tWEGOQ0GTfR8U+kA==", "signatures": [{"sig": "MEQCIFcWIu5cKDaES3XHW+iE8OVjSh5/1UjDy14mYeWm5OwFAiBlaFBna4HG2ZF28x3AyntCVqvZX494dvgEu6KJPQy0Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137030}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.1.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/608a7652ee48a3bcc46da8835610d65e/vitest-expect-1.1.1.tgz", "_integrity": "sha512-Qpw01C2Hyb3085jBkOJLQ7HRX0Ncnh2qV4p+xWmmhcIUlMykUF69zsnZ1vPmAjZpomw9+5tWEGOQ0GTfR8U+kA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.1.1", "@vitest/utils": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.1.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.1.1_1704029887316_0.6040869502544055", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@vitest/expect", "version": "1.1.2", "license": "MIT", "_id": "@vitest/expect@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "47681eb672c1b534e296ad254a1b0058ff0cb2a7", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.1.2.tgz", "fileCount": 7, "integrity": "sha512-1aOqDLbgkvJ2e1nLQ/5dkUX54V1Alwt2e6M2u03Oy7wGbDYHV5ZLKm1XbcT45h8TMXtc2q/BPtkeIjyRv1oDHQ==", "signatures": [{"sig": "MEQCICZuYtmD75rHUUn9f1++tsA/koqhfix1DSZ7U8WRVnSmAiBf1b5BzfhaWncwO2t82ROyKwa5xldHOxni0BYf5FNBhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137030}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.1.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/362573daff4d8cb83eb5bfafd965e95c/vitest-expect-1.1.2.tgz", "_integrity": "sha512-1aOqDLbgkvJ2e1nLQ/5dkUX54V1Alwt2e6M2u03Oy7wGbDYHV5ZLKm1XbcT45h8TMXtc2q/BPtkeIjyRv1oDHQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.1.2", "@vitest/utils": "1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.1.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.1.2_1704387531237_0.692039020360643", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@vitest/expect", "version": "1.1.3", "license": "MIT", "_id": "@vitest/expect@1.1.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9667719dffa82e7350dcca7b95f9ec30426d037e", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.1.3.tgz", "fileCount": 7, "integrity": "sha512-MnJqsKc1Ko04lksF9XoRJza0bGGwTtqfbyrsYv5on4rcEkdo+QgUdITenBQBUltKzdxW7K3rWh+nXRULwsdaVg==", "signatures": [{"sig": "MEUCIQCyvT430Qsto/mzpytS+/lu/l8aGGJUUR0S5JbjsHvnFwIgXwpFH0r9arINRJY5HaEAA8gcd4EFp+0NNL8lKj0+1yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137030}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.1.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/fb95a2be827577c71c9c07b1b00676b5/vitest-expect-1.1.3.tgz", "_integrity": "sha512-MnJqsKc1Ko04lksF9XoRJza0bGGwTtqfbyrsYv5on4rcEkdo+QgUdITenBQBUltKzdxW7K3rWh+nXRULwsdaVg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "9.6.7", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.1.3", "@vitest/utils": "1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.1.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.1.3_1704442903234_0.9475820772149799", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@vitest/expect", "version": "1.2.0", "license": "MIT", "_id": "@vitest/expect@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "de93f5c32c2781c41415a8c3a6e48e1c023d6613", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.2.0.tgz", "fileCount": 7, "integrity": "sha512-H+2bHzhyvgp32o7Pgj2h9RTHN0pgYaoi26Oo3mE+dCi1PAqV31kIIVfTbqMO3Bvshd5mIrJLc73EwSRrbol9Lw==", "signatures": [{"sig": "MEUCIQC4RSjdYG4NmfyUUuUkwSajS4bHjkJAKfT6/yImgvo6/wIgTJoZ/QsbxJU5JCPbd40ud/UCcvUxh4g2laqq+1ZEMZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138570}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.2.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c94473ad17446af1282cb350e8472aad/vitest-expect-1.2.0.tgz", "_integrity": "sha512-H+2bHzhyvgp32o7Pgj2h9RTHN0pgYaoi26Oo3mE+dCi1PAqV31kIIVfTbqMO3Bvshd5mIrJLc73EwSRrbol9Lw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.3", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.2.0", "@vitest/utils": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.2.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.2.0_1705075652068_0.6814070619105494", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@vitest/expect", "version": "1.2.1", "license": "MIT", "_id": "@vitest/expect@1.2.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "574c0ac138a9e34522da202ea4c48a3adfe7240e", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.2.1.tgz", "fileCount": 7, "integrity": "sha512-/bqGXcHfyKgFWYwIgFr1QYDaR9e64pRKxgBNWNXPefPFRhgm+K3+a/dS0cUGEreWngets3dlr8w8SBRw2fCfFQ==", "signatures": [{"sig": "MEYCIQC8npa3gq9TQhj8WB3SpNdZEQxiJ/QrSEJGhGo9e/QN7wIhAJL/IrmeddTSZvIQrUfa1/AwMUowDzvvMwBXshcU5xbG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138973}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.2.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/bad81ee64e56c3b76c7c20223f1dad26/vitest-expect-1.2.1.tgz", "_integrity": "sha512-/bqGXcHfyKgFWYwIgFr1QYDaR9e64pRKxgBNWNXPefPFRhgm+K3+a/dS0cUGEreWngets3dlr8w8SBRw2fCfFQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.3", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.2.1", "@vitest/utils": "1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.2.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.2.1_1705508651712_0.04261386313276305", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@vitest/expect", "version": "1.2.2", "license": "MIT", "_id": "@vitest/expect@1.2.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "39ea22e849bbf404b7e5272786551aa99e2663d0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.2.2.tgz", "fileCount": 7, "integrity": "sha512-3jpcdPAD7LwHUUiT2pZTj2U82I2Tcgg2oVPvKxhn6mDI2On6tfvPQTjAI4628GUGDZrCm4Zna9iQHm5cEexOAg==", "signatures": [{"sig": "MEUCIQCqjigtno04bUbpxCrbTp3+YQ/yTw0/BQ87AvdkqeR0jAIgYkiEFT0othJ89ClvCocJUQsaKGRwcwsBFf+Cw9ljxLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138932}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.2.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a0a6394a3bbbcf311dbe72ea976b4fe3/vitest-expect-1.2.2.tgz", "_integrity": "sha512-3jpcdPAD7LwHUUiT2pZTj2U82I2Tcgg2oVPvKxhn6mDI2On6tfvPQTjAI4628GUGDZrCm4Zna9iQHm5cEexOAg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.3", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.2.2", "@vitest/utils": "1.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.2.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.2.2_1706286360155_0.5670237163824667", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@vitest/expect", "version": "1.3.0", "license": "MIT", "_id": "@vitest/expect@1.3.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "09b374357b51be44f4fba9336d59024756f902dc", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.3.0.tgz", "fileCount": 7, "integrity": "sha512-7bWt0vBTZj08B+Ikv70AnLRicohYwFgzNjFqo9SxxqHHxSlUJGSXmCRORhOnRMisiUryKMdvsi1n27Bc6jL9DQ==", "signatures": [{"sig": "MEUCIDFV3k0554W1uoWuQ0jvj6ncf8P8hOOLF61AiGVffahaAiEAwNkmRdtfH1mRobrKh7UVx31onAJZ/XpLENwM0DrapBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139192}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.3.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/600277a6a9bf8bea3713046fb348c525/vitest-expect-1.3.0.tgz", "_integrity": "sha512-7bWt0vBTZj08B+Ikv70AnLRicohYwFgzNjFqo9SxxqHHxSlUJGSXmCRORhOnRMisiUryKMdvsi1n27Bc6jL9DQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.3", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.3.0", "@vitest/utils": "1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.3.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.3.0_1708104553910_0.40167625569006504", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@vitest/expect", "version": "1.3.1", "license": "MIT", "_id": "@vitest/expect@1.3.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d4c14b89c43a25fd400a6b941f51ba27fe0cb918", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.3.1.tgz", "fileCount": 7, "integrity": "sha512-xofQFwIzfdmLLlHa6ag0dPV8YsnKOCP1KdAeVVh34vSjN2dcUiXYCD9htu/9eM7t8Xln4v03U9HLxLpPlsXdZw==", "signatures": [{"sig": "MEYCIQCWRwm0gh/Yhohd1wbw7PigJEowh1cm9a3KzcGq0T96WQIhAKxjB68hUgaYwz0UINeXSrDe786rtyxx1yDDtluqUojy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 139091}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.3.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ee26a14d084698145e29bce94e0f5ed7/vitest-expect-1.3.1.tgz", "_integrity": "sha512-xofQFwIzfdmLLlHa6ag0dPV8YsnKOCP1KdAeVVh34vSjN2dcUiXYCD9htu/9eM7t8Xln4v03U9HLxLpPlsXdZw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.4", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.3.1", "@vitest/utils": "1.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.3.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.3.1_1708436919392_0.2168561503895503", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@vitest/expect", "version": "1.4.0", "license": "MIT", "_id": "@vitest/expect@1.4.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d64e17838a20007fecd252397f9b96a1ca81bfb0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.4.0.tgz", "fileCount": 7, "integrity": "sha512-Jths0sWCJZ8BxjKe+p+eKsoqev1/T8lYcrjavEaz8auEJ4jAVY0GwW3JKmdVU4mmNPLPHixh4GNXP7GFtAiDHA==", "signatures": [{"sig": "MEQCIHyW8liB8rRcmILqm7sRt8Uo+FvepStF0UK26c9ZePgTAiBHsQukEsWylfqb36BBIvmVLbna/nnXtvWlxdljaqPtiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 139502}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.4.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6636ab07d8c086fa8c8cc82adcaa6c45/vitest-expect-1.4.0.tgz", "_integrity": "sha512-Jths0sWCJZ8BxjKe+p+eKsoqev1/T8lYcrjavEaz8auEJ4jAVY0GwW3JKmdVU4mmNPLPHixh4GNXP7GFtAiDHA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.2.4", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.1", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.4.0", "@vitest/utils": "1.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.4.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.4.0_1710498663365_0.25679041265143865", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@vitest/expect", "version": "1.5.0", "license": "MIT", "_id": "@vitest/expect@1.5.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "961190510a2723bd4abf5540bcec0a4dfd59ef14", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.5.0.tgz", "fileCount": 7, "integrity": "sha512-0pzuCI6KYi2SIC3LQezmxujU9RK/vwC1U9R0rLuGlNGcOuDWxqWKu6nUdFsX9tH1WU0SXtAxToOsEjeUn1s3hA==", "signatures": [{"sig": "MEUCIBt66z21CsN7I3uaq83q0oYPcApKR+qF0GCKCM4bhtsHAiEA2xz0EjQMLI1DfWfqrizG7Dqgc+2xoFz8f7Ls/ZY6NbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142365}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.5.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bf23389db8ad4bd0f008574880950278/vitest-expect-1.5.0.tgz", "_integrity": "sha512-0pzuCI6KYi2SIC3LQezmxujU9RK/vwC1U9R0rLuGlNGcOuDWxqWKu6nUdFsX9tH1WU0SXtAxToOsEjeUn1s3hA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.1", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.5.0", "@vitest/utils": "1.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.5.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.5.0_1712857689479_0.15406972734740898", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@vitest/expect", "version": "1.5.1", "license": "MIT", "_id": "@vitest/expect@1.5.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a9ad180570a934bd4d63c59175e50ab2c359fff9", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.5.1.tgz", "fileCount": 7, "integrity": "sha512-w3Bn+VUMqku+oWmxvPhTE86uMTbfmBl35aGaIPlwVW7Q89ZREC/icfo2HBsEZ3AAW6YR9lObfZKPEzstw9tJOQ==", "signatures": [{"sig": "MEUCIHE1h2U468MaASZK4i4SDCEDyK8HF/PNmOGX3E9TAPOCAiEA+sjXRhc/kJVcbpLRlu3fF1wTf4YHsrYC1g8f8lTXoJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142396}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.5.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4ac3a304e7ff9ff95e587be8a23afc58/vitest-expect-1.5.1.tgz", "_integrity": "sha512-w3Bn+VUMqku+oWmxvPhTE86uMTbfmBl35aGaIPlwVW7Q89ZREC/icfo2HBsEZ3AAW6YR9lObfZKPEzstw9tJOQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.5.1", "@vitest/utils": "1.5.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.5.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.5.1_1713957754818_0.03331717793405087", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@vitest/expect", "version": "1.5.2", "license": "MIT", "_id": "@vitest/expect@1.5.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "04d1c0c94ca264e32fe43f564b04528f352a6083", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.5.2.tgz", "fileCount": 7, "integrity": "sha512-rf7MTD1WCoDlN3FfYJ9Llfp0PbdtOMZ3FIF0AVkDnKbp3oiMW1c8AmvRZBcqbAhDUAvF52e9zx4WQM1r3oraVA==", "signatures": [{"sig": "MEYCIQDimKojyD3jfo7DzZytz82IlUIL+0Wca1BgRXTS0yS3IQIhAOsKZhItD1kpju/6zvHDW1MqtXLgmnXXb7tbydn0bRft", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142420}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.5.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/640baa684e06db27e685f17f2206c630/vitest-expect-1.5.2.tgz", "_integrity": "sha512-rf7MTD1WCoDlN3FfYJ9Llfp0PbdtOMZ3FIF0AVkDnKbp3oiMW1c8AmvRZBcqbAhDUAvF52e9zx4WQM1r3oraVA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.5.2", "@vitest/utils": "1.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.5.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.5.2_1714036332612_0.29900659272214947", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@vitest/expect", "version": "1.5.3", "license": "MIT", "_id": "@vitest/expect@1.5.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "34198e2123fb5be68f606729114aadbd071d77dc", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.5.3.tgz", "fileCount": 7, "integrity": "sha512-y+waPz31pOFr3rD7vWTbwiLe5+MgsMm40jTZbQE8p8/qXyBX3CQsIXRx9XK12IbY7q/t5a5aM/ckt33b4PxK2g==", "signatures": [{"sig": "MEUCIAO01FDxqkmv61OpZbLpvrcyhuKJOLLxBtn3MuRabQ2AAiEApe59FMQSPy5CLoWBt7RycqwQDukmv3zbfy2+bxiF9GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142549}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.5.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/24ef0d14cb5ff2316f60604d1632addb/vitest-expect-1.5.3.tgz", "_integrity": "sha512-y+waPz31pOFr3rD7vWTbwiLe5+MgsMm40jTZbQE8p8/qXyBX3CQsIXRx9XK12IbY7q/t5a5aM/ckt33b4PxK2g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.5.3", "@vitest/utils": "1.5.3"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.5.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.5.3_1714466436356_0.23056585757573722", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@vitest/expect", "version": "1.6.0", "license": "MIT", "_id": "@vitest/expect@1.6.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0b3ba0914f738508464983f4d811bc122b51fb30", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.6.0.tgz", "fileCount": 7, "integrity": "sha512-ixEvFVQjycy/oNgHjqsL6AZCDduC+tflRluaHIzKIsdbzkLn2U/iBnVeJwB6HsIjQBdfMR8Z0tRxKUsvFJEeWQ==", "signatures": [{"sig": "MEYCIQC+gtQDZwWhUVirPEJM5G7KwwGJSM05OF9OplNKeTc3CwIhAPkM7e8fYTbBeQr1jqmra+3s4DzT+zsNjM3jJAznngFa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142549}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.6.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cfa149e3db6f9db053fc98adcb79852e/vitest-expect-1.6.0.tgz", "_integrity": "sha512-ixEvFVQjycy/oNgHjqsL6AZCDduC+tflRluaHIzKIsdbzkLn2U/iBnVeJwB6HsIjQBdfMR8Z0tRxKUsvFJEeWQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.6.0", "@vitest/utils": "1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.6.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.6.0_1714749747305_0.7411524895846828", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "@vitest/expect", "version": "2.0.0-beta.1", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f03712d4e475dcaf04b579aafc079483cdf0ab3a", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-D4ewLYGneimhzeboi5+bF6zebf3kuF1DixWKT0AcEGmAWb+5pUMCSN34G+g3AVtsm8GF2kGOLEfXyhD+Q6LJWA==", "signatures": [{"sig": "MEUCIQDbinny6QHuQn8NMBGHHzR+CwN6J7Cpx0Rnk809nluGhQIgNBr0bFWVi+n4s/rZeLDw631oCAYwa0Xmb0wDQDDvPIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 143499}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4ae7b5fbe6dae500c6e7cc9e8cc0739c/vitest-expect-2.0.0-beta.1.tgz", "_integrity": "sha512-D4ewLYGneimhzeboi5+bF6zebf3kuF1DixWKT0AcEGmAWb+5pUMCSN34G+g3AVtsm8GF2kGOLEfXyhD+Q6LJWA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.1", "@vitest/utils": "2.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.1_1715265158055_0.6096755406718102", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "@vitest/expect", "version": "2.0.0-beta.2", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3e25206106d5a280271ee9d1d532143480b6bde5", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-mh0wG0quTvIO3VzekEtKzYg+LIZrwS4LrqppGj/ypuuW/UNqgQbzLPsw7PWzzwshWLkPqjzGfa+WXuu3tCaq8A==", "signatures": [{"sig": "MEUCIQDtch32IPChyipW2/agRP7X2XzsCpcWyvGsSowIG/3a4AIgFFyjVdHt4gm2zLYv5bKK3BlmFt1ZjW8mIz4cWv+951w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 143499}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/226b60d80e5268e773823cc516babde3/vitest-expect-2.0.0-beta.2.tgz", "_integrity": "sha512-mh0wG0quTvIO3VzekEtKzYg+LIZrwS4LrqppGj/ypuuW/UNqgQbzLPsw7PWzzwshWLkPqjzGfa+WXuu3tCaq8A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.2", "@vitest/utils": "2.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.2_1715268695147_0.2672703459533121", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "@vitest/expect", "version": "2.0.0-beta.3", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7d0c6e57dd83b3468521d3b0596b8441f41f3100", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.3.tgz", "fileCount": 7, "integrity": "sha512-nbspKRdknioSmLK7NxBTtwRoOjPMbCWesnmhyLG8BMPDAXhse8LPiG1kY1hvpC6moCf0BHUEeiJ5rGIEFxzdAw==", "signatures": [{"sig": "MEUCICpOnARO5v3RVTqhB5ijRI9E05sSjmncrnRjqIT1iMoHAiEAt4rrmHSIj8MmYTcRM6FlCuuxcKe1F4xnn61eUk0Wf5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 143712}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/674d77bf0b672684b852a42cfc59ee67/vitest-expect-2.0.0-beta.3.tgz", "_integrity": "sha512-nbspKRdknioSmLK7NxBTtwRoOjPMbCWesnmhyLG8BMPDAXhse8LPiG1kY1hvpC6moCf0BHUEeiJ5rGIEFxzdAw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.3", "@vitest/utils": "2.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.3_1715712291756_0.5687012279516281", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "@vitest/expect", "version": "2.0.0-beta.4", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b969e29e240b3081754b9feb5f7576c8c01e61f0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.4.tgz", "fileCount": 7, "integrity": "sha512-MHgf9EhuxFBXiCBbOu9XBU/DrFxltMIc4+m7IXMNcqCFE2vTsLhzBitTbDsWhQyEGGkt6opzImfmcfhmFMZRDw==", "signatures": [{"sig": "MEUCIE3IKPezKgX9N2XUzpiLgKWHMrsbZgecK4pW49axTz++AiEAnRilnBwrV/SgDEvz3P+PplOV3Ohh37rs7AR4AKtFVpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7f3a15f005498a9405bda658dc5dad34/vitest-expect-2.0.0-beta.4.tgz", "_integrity": "sha512-MHgf9EhuxFBXiCBbOu9XBU/DrFxltMIc4+m7IXMNcqCFE2vTsLhzBitTbDsWhQyEGGkt6opzImfmcfhmFMZRDw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.4", "@vitest/utils": "2.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.4_1717330566143_0.10924168133777079", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "@vitest/expect", "version": "2.0.0-beta.5", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "19724b63df1e2015e81d839b4ac2dd51b4ba3a3c", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.5.tgz", "fileCount": 7, "integrity": "sha512-Nndd4vXfCMBqkWgVjDguvisTA+tsXHp1eRx5lpLCPM1goc49TKh2wqzYtaIbA4pWTW54JhYXbP0EdhmHk/zoXA==", "signatures": [{"sig": "MEYCIQCtb9R0XwswczHwP63ZRdNjwEj564fbpn9vCMaTw8BYXAIhAN66lk48DyU0wsWQzYvOxMeEXQZJm2ezW4YFHsrS1ERu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/18e7f4bab50a92864187ae15dd94d2eb/vitest-expect-2.0.0-beta.5.tgz", "_integrity": "sha512-Nndd4vXfCMBqkWgVjDguvisTA+tsXHp1eRx5lpLCPM1goc49TKh2wqzYtaIbA4pWTW54JhYXbP0EdhmHk/zoXA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.5", "@vitest/utils": "2.0.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.5_1717331278830_0.7749416732419785", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "@vitest/expect", "version": "2.0.0-beta.6", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0ba6d6561d78f57f6e67f07bb695a2c8c3799ab0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.6.tgz", "fileCount": 7, "integrity": "sha512-GrxrujuLT7jS5hXJSMQnl+s5JQNnylG4Z8jRWAwwkvkxDob5F0E1wnzM25R9GV0q6iUPnAhSnWCpKPGyNrpgIA==", "signatures": [{"sig": "MEUCIQD3u4f+tB/YAA8OMUxP8pcxA83ph+qFEZ0DjbUuZgCEdwIgVt7ZOeM8BxkYW7bGblGROSyZcA8B+lUTqhqdU+OUzDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.6.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/84cbcbc9fe8ff7ad6b3458abd37d8ee0/vitest-expect-2.0.0-beta.6.tgz", "_integrity": "sha512-GrxrujuLT7jS5hXJSMQnl+s5JQNnylG4Z8jRWAwwkvkxDob5F0E1wnzM25R9GV0q6iUPnAhSnWCpKPGyNrpgIA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.6", "@vitest/utils": "2.0.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.6", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.6_1717355855698_0.4617360647730364", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "@vitest/expect", "version": "2.0.0-beta.7", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0d4908e6fd73b5f2048f5ce38646eca0040df283", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.7.tgz", "fileCount": 7, "integrity": "sha512-Gfj4OBaxP9LmOkQ00e4hJJ6Rypm4d2CNXzwKPUplMigCcWtmplDcetYGUM6ogB+cUpRlV42X9Cvn+I2P6OSgbQ==", "signatures": [{"sig": "MEQCIGMJgS2ZPo4PhiY/L3l0F1ez/LtN16NGf2VKS63MMj6+AiAQvxmTl1C1m2DHZs7nCTwlCPAnNdje3UF+dqNIHOgGgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.7.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/87b9692b7337994024f022de7036a62d/vitest-expect-2.0.0-beta.7.tgz", "_integrity": "sha512-Gfj4OBaxP9LmOkQ00e4hJJ6Rypm4d2CNXzwKPUplMigCcWtmplDcetYGUM6ogB+cUpRlV42X9Cvn+I2P6OSgbQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.7", "@vitest/utils": "2.0.0-beta.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.7", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.7_1717414559650_0.6090531574229341", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "@vitest/expect", "version": "2.0.0-beta.8", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.8", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4e1b3608dcb92d07949c872fc8502811601e1f64", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.8.tgz", "fileCount": 7, "integrity": "sha512-Gk6tRRJfkgX0G9bdyvrv70htJXJH3lVgqrJyvxDoFI3kO3ggpkeALpPcxd0zWfRRPi3N9i1+PaLf+/ATVNYRig==", "signatures": [{"sig": "MEQCICvviMsO7JYtMNwI0JaBu4dVV3cvch8Tu+K4LIjas64TAiAerlSjLasAutpxrfk8y3G1hmDW5/0iPFMCgw+Py7J8Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.8.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/273ba09dd36e0aa57088d8f7d382667f/vitest-expect-2.0.0-beta.8.tgz", "_integrity": "sha512-Gk6tRRJfkgX0G9bdyvrv70htJXJH3lVgqrJyvxDoFI3kO3ggpkeALpPcxd0zWfRRPi3N9i1+PaLf+/ATVNYRig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.8", "@vitest/utils": "2.0.0-beta.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.8", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.8_1717504775809_0.24067701839300426", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "@vitest/expect", "version": "2.0.0-beta.9", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.9", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e7335e858d3777caf96628251c4ea34e75a1117c", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.9.tgz", "fileCount": 7, "integrity": "sha512-bYvI/JTVe0KuN0JtkIq+ka2r4A9Mu9+vwx0mzpnc/nnzj1UEBNj7UAjeRF858aaQYLw72gvEior7jh4VSGDVJg==", "signatures": [{"sig": "MEUCIAE98SqXZvURmCWVfPsNBLVduAGNFIpFw/t47VOlLL/iAiEA75uMNr85qagkB3hB5ft2qz9ass24x1jq9daYw5Q1Lrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146324}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.9.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/405e52763b098ec744ecd76952c5867d/vitest-expect-2.0.0-beta.9.tgz", "_integrity": "sha512-bYvI/JTVe0KuN0JtkIq+ka2r4A9Mu9+vwx0mzpnc/nnzj1UEBNj7UAjeRF858aaQYLw72gvEior7jh4VSGDVJg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.5.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.9", "@vitest/utils": "2.0.0-beta.9"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.9", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.9_1717574472603_0.007417130317399767", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "@vitest/expect", "version": "2.0.0-beta.10", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.10", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e9d524010238170de3e36c312a943bf3af6dabd9", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.10.tgz", "fileCount": 7, "integrity": "sha512-/i2m9rj/Hzd+PcJYZo47dFDcrUnU0T4LbdCguugJZ0bvFLtU1oWmn6PxCUTYcwj4TgBWNU9r7f1ngZQId2j5Mw==", "signatures": [{"sig": "MEYCIQDbEE+UUBLjNBUaWbdyCCMAGWvi9kzBVQKzGy4VABvdkwIhAL12Z9zuKKCMXSQxFTZ15YqFd6+4oFiPZNxC1rRtTGd+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146342}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.10.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1c6f47e6db4fcf119054c2e567cfb5fb/vitest-expect-2.0.0-beta.10.tgz", "_integrity": "sha512-/i2m9rj/Hzd+PcJYZo47dFDcrUnU0T4LbdCguugJZ0bvFLtU1oWmn6PxCUTYcwj4TgBWNU9r7f1ngZQId2j5Mw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.10", "@vitest/utils": "2.0.0-beta.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.10", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.10_1718194307163_0.3316084587033101", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "@vitest/expect", "version": "2.0.0-beta.11", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.11", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "325b5773947cc0c98dc3efb6de737627ff9fac7f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.11.tgz", "fileCount": 7, "integrity": "sha512-d+VCN21Ln7KkyvfU13K7A27tQk57WDtp05DPf05lozHT4KUv4y5AQYf3Pm8fV5v/8mM16EULm3jT2liEbhlIaA==", "signatures": [{"sig": "MEQCIBEUpC6zZ+cbWbZsmscOYb8Pzjcn2DDSBWLGLAJqPvD7AiBD7PMnJxLSUD7zRw9C+O7S+X+A+3XxbAzhWSJb/zgx0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148911}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.11.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cba7fc010806d8c565150d1922610242/vitest-expect-2.0.0-beta.11.tgz", "_integrity": "sha512-d+VCN21Ln7KkyvfU13K7A27tQk57WDtp05DPf05lozHT4KUv4y5AQYf3Pm8fV5v/8mM16EULm3jT2liEbhlIaA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.11", "@vitest/utils": "2.0.0-beta.11"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.11", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.11_1718828050999_0.9114724767791968", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.12": {"name": "@vitest/expect", "version": "2.0.0-beta.12", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.12", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0036d77874d026e0f6bed0fa53682ebfd1d4b54f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.12.tgz", "fileCount": 7, "integrity": "sha512-4AoKb3aZRFzFWGVF6iFHuAjsFH0dydQKzEfT8TfCNzx7+iXtVnLJ5nQUC6D4qlvyEmJeGIbbXZcgiSxY4Ry7eA==", "signatures": [{"sig": "MEUCIQC+itgFiZItZk9iURyeutiwuGvMiVA+I25SU4oXOoaI/AIgBVnCWpL+73QjE9ynz+18aZnHULodR/2WpLQ2C5ATnDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148911}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.12.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/12fbfe060aef81831dc240fb4786ad1e/vitest-expect-2.0.0-beta.12.tgz", "_integrity": "sha512-4AoKb3aZRFzFWGVF6iFHuAjsFH0dydQKzEfT8TfCNzx7+iXtVnLJ5nQUC6D4qlvyEmJeGIbbXZcgiSxY4Ry7eA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.12", "@vitest/utils": "2.0.0-beta.12"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.12", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.12_1719346587930_0.05392657941336809", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.13": {"name": "@vitest/expect", "version": "2.0.0-beta.13", "license": "MIT", "_id": "@vitest/expect@2.0.0-beta.13", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "094c133e3e25503ee47390ff6d5d1df644e6c264", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0-beta.13.tgz", "fileCount": 7, "integrity": "sha512-VvdoRimo52gLziVilXzuNxe3VBYIhy+n2wjXrT2IAa1y9P1FxbDHHJzrSVw5c9bLwhxJvIyQIKWE9kFQoO5MZg==", "signatures": [{"sig": "MEUCIQCEpDvLkagEroMkqg9UIKD6eCylaQMlMXQEf4y0tvjSfgIgJoDILHvMQBLHZTJJreaT9SvHgUhX9n6mCS3yPYqiufU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148915}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0-beta.13.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7ef73d71b75bb7a263f452d76c95020c/vitest-expect-2.0.0-beta.13.tgz", "_integrity": "sha512-VvdoRimo52gLziVilXzuNxe3VBYIhy+n2wjXrT2IAa1y9P1FxbDHHJzrSVw5c9bLwhxJvIyQIKWE9kFQoO5MZg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0-beta.13", "@vitest/utils": "2.0.0-beta.13"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0-beta.13", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0-beta.13_1720101833232_0.30666795188340945", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vitest/expect", "version": "2.0.0", "license": "MIT", "_id": "@vitest/expect@2.0.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "49756c4d5e202efdd6c6a4f62603ebf8cbb8f815", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-5BSfZ0+dAVmC6uPF7s+TcKx0i7oyYHb1WQQL5gg6G2c+Qkaa5BNrdRM74sxDfUIZUgYCr6bfCqmJp+X5bfcNxQ==", "signatures": [{"sig": "MEQCIALrzIrJh5Z4f7WwVLuBGjtJn4PNQ5BaJcyETkGAH50IAiAx4smCfnbthmFXJfxLmYghswi9PcW7b2bkREqit3zFVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148883}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/256a8ddbf3c1c525e224cbdd9096aee5/vitest-expect-2.0.0.tgz", "_integrity": "sha512-5BSfZ0+dAVmC6uPF7s+TcKx0i7oyYHb1WQQL5gg6G2c+Qkaa5BNrdRM74sxDfUIZUgYCr6bfCqmJp+X5bfcNxQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.0", "@vitest/utils": "2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.0_1720438773436_0.2097610053630774", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vitest/expect", "version": "2.0.1", "license": "MIT", "_id": "@vitest/expect@2.0.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9e64b1a709971e64652a806c3aa1e8110aa3ab78", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-yw70WL3ZwzbI2O3MOXYP2Shf4vqVkS3q5FckLJ6lhT9VMMtDyWdofD53COZcoeuHwsBymdOZp99r5bOr5g+oeA==", "signatures": [{"sig": "MEUCIQCWih2b1iR8S/DA5wPh8U7B1iTvwhscs1DT/YA2xVbB8AIgEtiHiVw2i1VQI3yhrKtIAXJB1tAFyUyT/1HCMSf7nJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148883}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dfdf7a48943bbcfee8a8db03c9bdfa6f/vitest-expect-2.0.1.tgz", "_integrity": "sha512-yw70WL3ZwzbI2O3MOXYP2Shf4vqVkS3q5FckLJ6lhT9VMMtDyWdofD53COZcoeuHwsBymdOZp99r5bOr5g+oeA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.1", "@vitest/utils": "2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.1", "@types/chai": "4.3.6", "@vitest/runner": "2.0.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.1_1720452790589_0.043030258478732764", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@vitest/expect", "version": "2.0.2", "license": "MIT", "_id": "@vitest/expect@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2eff61dde5fb2574a0a7a32517419b5de7d78124", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-nKAvxBYqcDugYZ4nJvnm5OR8eDJdgWjk4XM9owQKUjzW70q0icGV2HVnQOyYsp906xJaBDUXw0+9EHw2T8e0mQ==", "signatures": [{"sig": "MEQCIChyuBMe32D15gcvUr8fJ3ydjDMeUCwC/UupkRnp/da4AiBSYdxggBxiTea0+aydZK+uV6bPnVL2GIkzfKByzIxE6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148668}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c7b0c767f7b5e1452cbd299fe113a4cc/vitest-expect-2.0.2.tgz", "_integrity": "sha512-nKAvxBYqcDugYZ4nJvnm5OR8eDJdgWjk4XM9owQKUjzW70q0icGV2HVnQOyYsp906xJaBDUXw0+9EHw2T8e0mQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.2", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.0.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.2_1720626403618_0.39906681400452704", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/expect", "version": "2.0.3", "license": "MIT", "_id": "@vitest/expect@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "367727256f2a253e21a3e69cd996af51fc7899b1", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-X6AepoOYePM0lDNUPsGXTxgXZAl3EXd0GYe/MZyVE4HzkUqyUVC6S3PrY5mClDJ6/7/7vALLMV3+xD/Ko60Hqg==", "signatures": [{"sig": "MEUCIQCWHem+4/GC/Cf2SDTuG+CuvoET1YopUGliD0sjaly29gIgZlPyhYVlB9YYTvNsIZidB5KQb6OQL1SK7Bz/PEqccW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148668}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fd93808c72afee7f4eb455c986281c21/vitest-expect-2.0.3.tgz", "_integrity": "sha512-X6AepoOYePM0lDNUPsGXTxgXZAl3EXd0GYe/MZyVE4HzkUqyUVC6S3PrY5mClDJ6/7/7vALLMV3+xD/Ko60Hqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.3", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.0.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.3_1721037820632_0.2077760556792494", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/expect", "version": "2.0.4", "license": "MIT", "_id": "@vitest/expect@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d365c106c84f2a3aae96000e95be21956acc099c", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.4.tgz", "fileCount": 7, "integrity": "sha512-39jr5EguIoanChvBqe34I8m1hJFI4+jxvdOpD7gslZrVQBKhh8H9eD7J/LJX4zakrw23W+dITQTDqdt43xVcJw==", "signatures": [{"sig": "MEUCIGuAqeXJcDKWOsscmoURx/N3AMmV3NoA/SHGfnlJotQ2AiEA/WSAFGcljnALHlTytgB7iB0KE0PTibfciWChExE2Kns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148672}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f37ac5b5204d1d53e2c081422606841b/vitest-expect-2.0.4.tgz", "_integrity": "sha512-39jr5EguIoanChvBqe34I8m1hJFI4+jxvdOpD7gslZrVQBKhh8H9eD7J/LJX4zakrw23W+dITQTDqdt43xVcJw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.4", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.0.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.4_1721639614580_0.6334236385866463", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/expect", "version": "2.0.5", "license": "MIT", "_id": "@vitest/expect@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f3745a6a2c18acbea4d39f5935e913f40d26fa86", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.0.5.tgz", "fileCount": 7, "integrity": "sha512-y<PERSON><PERSON>twuP7JZivj65Gxoi8upUN2OzHTi3zVfjwdpu2WrvCZPLwsJ2Ey5ILIPccoW23dd/zQBlJ4/dhi7DWNyXCpA==", "signatures": [{"sig": "MEUCIBKYTQEBhzg0Zyzb68ep0vNzFDRmjU3uZgZZvU8tjF1FAiEAn44WPva9UZhcnulljxdLiLl/W7OIQuKlc6S9BoeUog4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148890}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.0.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3759c9b1062519cd66da35867455775a/vitest-expect-2.0.5.tgz", "_integrity": "sha512-y<PERSON><PERSON>twuP7JZivj65Gxoi8upUN2OzHTi3zVfjwdpu2WrvCZPLwsJ2Ey5ILIPccoW23dd/zQBlJ4/dhi7DWNyXCpA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.7.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.0.5", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.0.5_1722422407789_0.8478390323220908", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/expect", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8ea133c10cb6d2dd38739a3ccfd0162bb9cb7879", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-PJTIM61d+YW2KRti0Hx/kKb1HeaLE8ZDzYFEs8Jp4eyhrxIy+w+0or0BJIAECqrB1TPc0ykNnRxJsqxLtwGvtg==", "signatures": [{"sig": "MEUCIQCeA/99v4+mZNd59NTJ9jicbs7I3NAxsFpEJycTN2zR8AIgc7dCGEmu7FLwJlUiLQdqj/PzsguSl7YizVCbwXYMQqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e902307f00988d5230790f772894f9df/vitest-expect-2.1.0-beta.1.tgz", "_integrity": "sha512-PJTIM61d+YW2KRti0Hx/kKb1HeaLE8ZDzYFEs8Jp4eyhrxIy+w+0or0BJIAECqrB1TPc0ykNnRxJsqxLtwGvtg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.1", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.1_1723011694138_0.026385052307169365", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/expect", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e759ce454f64c09080248752397b57162b87064a", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-0bLiSTRmsqABp+uSvyb91TGR7DKSYQNRHqmKpsopJzKwvNvMSBv4nXw24Og0JSxUXPfb8u6IU82Yu6EzWpmkzA==", "signatures": [{"sig": "MEUCIQCl4NZ+EVToMyr3yRXmQqdiOrISHj7waT+mdmndhAincwIgbV9rjz8m3J42Si34MFunlZo1PHibd2ZE56kedsOb7AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6ef95c020bb730ef006b2a8e098043bb/vitest-expect-2.1.0-beta.2.tgz", "_integrity": "sha512-0bLiSTRmsqABp+uSvyb91TGR7DKSYQNRHqmKpsopJzKwvNvMSBv4nXw24Og0JSxUXPfb8u6IU82Yu6EzWpmkzA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.2", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.2_1723017418795_0.9848875869107889", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/expect", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b24df9d033b10da7dc8339cc0826a656dd6f384f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.3.tgz", "fileCount": 7, "integrity": "sha512-BD7y8x0gCCCJ65Tjtu05J3OeDDYl8QHRzDD5L8C3rxVptyd+xYz7gI24UmZdmDOK4cFS32/HMSwqw0FgT/iIAg==", "signatures": [{"sig": "MEQCIDdedrhXxPe2JbI7heErsROKNchMZMSb8/3ZPSEgnBtOAiBJTokUo7WZXNI71yW05SGHPyfLSUiK17UHni8wQk8tKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1191c85cab4b55425fb95e54c505f5d7/vitest-expect-2.1.0-beta.3.tgz", "_integrity": "sha512-BD7y8x0gCCCJ65Tjtu05J3OeDDYl8QHRzDD5L8C3rxVptyd+xYz7gI24UmZdmDOK4cFS32/HMSwqw0FgT/iIAg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.3", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.3_1723018631152_0.8325127239642742", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/expect", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "edce2145c6c3236f2e60ad4547fadb22befba45e", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.4.tgz", "fileCount": 7, "integrity": "sha512-DT556QBVbiEK2xQ6wpSpdwKRnyH+R4UNSrYs82ijykshjRnKcA7dCaKzcpAAgb3+kTkU67ZKy64egukhYXi2KQ==", "signatures": [{"sig": "MEUCIQCceB8QwLqau7kM6hjVEn84AzjETmBbOlCrlAXc2zHsgwIgM4lUvc82uA6FAUg6z0FYNMF1CLmZfhbrIz0FWh5YCUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f0112a1eb6e4c1e2e7b3bb555d3ae1f0/vitest-expect-2.1.0-beta.4.tgz", "_integrity": "sha512-DT556QBVbiEK2xQ6wpSpdwKRnyH+R4UNSrYs82ijykshjRnKcA7dCaKzcpAAgb3+kTkU67ZKy64egukhYXi2KQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.4", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.4_1723030980575_0.1405937361837204", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/expect", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "34cd3900aa1f2d8aee00a8f782157faf22cb1273", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.5.tgz", "fileCount": 7, "integrity": "sha512-9jfQ80Ko8OAqh/5H/w3627u0pArBM+7ohYcAxei4gx1paG5GoFJMyUtlUD/ivsVcrL6CXVs65vmOeYXFTzhANw==", "signatures": [{"sig": "MEQCIAh73Y75Z7jR6hKfjlP7u+Rzx3zvZLU4aMNoeg2k127TAiBaVHcjJTAtAMqlGjTnTkyVILW/V8rRsSQ0nauO4sQAbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148918}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/776775b89aae0dc7197fad82df8bb1e2/vitest-expect-2.1.0-beta.5.tgz", "_integrity": "sha512-9jfQ80Ko8OAqh/5H/w3627u0pArBM+7ohYcAxei4gx1paG5GoFJMyUtlUD/ivsVcrL6CXVs65vmOeYXFTzhANw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.5", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.5_1723462527602_0.7219650227551357", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/expect", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "32c1bcb266192df8cf3202498b9ff5f5c100b8a0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.6.tgz", "fileCount": 7, "integrity": "sha512-SnCnyVa/NbOzH8dLEAhKxsm/ehVvi8x6xOs6bzJGdb/7QgJPBSBSrxRAwPwjhr5ptj3kxs62zie/+BR94m5Ajg==", "signatures": [{"sig": "MEUCIQDA37IPaaN4MLfCXAI3V7hfQE5El8wvHr+XrenNSA7iQQIgMtCrrFvtWd1X88WL2GkOEVNxQC5FTi3sswKd8IM5Y7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148913}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.6.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0c98b9c3acb4d71a80525a8a213ef0db/vitest-expect-2.1.0-beta.6.tgz", "_integrity": "sha512-SnCnyVa/NbOzH8dLEAhKxsm/ehVvi8x6xOs6bzJGdb/7QgJPBSBSrxRAwPwjhr5ptj3kxs62zie/+BR94m5Ajg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.1", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.6", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.6", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.6_1724159921241_0.8960428923931252", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/expect", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/expect@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7ae77f874b4933865e7cce715a2e091e4e36adcd", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0-beta.7.tgz", "fileCount": 7, "integrity": "sha512-Y4FgOd2TEpc0aKyr7DFyhZ9YbO/UJjAJ2GPQPjVSZmhVIZV3sAbh9teXLByy8jbV/b7Bb4kf85kywzbttpYxog==", "signatures": [{"sig": "MEYCIQDz76PL/owCysZIQD7d47VEnMBMJseJVxKfbL58FKqqBgIhAK7tAABohC5CedL+TfwMwtt6holPQ4BvF28zREAiokPo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148761}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0-beta.7.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6578fb3f73d70a6c8f28c75bae2bba31/vitest-expect-2.1.0-beta.7.tgz", "_integrity": "sha512-Y4FgOd2TEpc0aKyr7DFyhZ9YbO/UJjAJ2GPQPjVSZmhVIZV3sAbh9teXLByy8jbV/b7Bb4kf85kywzbttpYxog==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0-beta.7", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0-beta.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0-beta.7", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0-beta.7_1725894807371_0.08311405725792231", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/expect", "version": "2.1.0", "license": "MIT", "_id": "@vitest/expect@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "922b16c0215079ea32c0fb286cc7d325a5b6139d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-N3/xR4fSu0+6sVZETEtPT1orUs2+Y477JOXTcU3xKuu3uBlsgbD7/7Mz2LZ1Jr1XjwilEWlrIgSCj4N1+5ZmsQ==", "signatures": [{"sig": "MEUCIQDZ8Ud5GoLUQzKR2fkWbP6iQlXEe3O6vYX7R8eRn8tlbQIgUpl7LeVRdytnRv5FIPSh6XhQgKRM7gWLSwy+/pUGoZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148856}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ad9a7a4c9754f365a9ba4ec0b68086d7/vitest-expect-2.1.0.tgz", "_integrity": "sha512-N3/xR4fSu0+6sVZETEtPT1orUs2+Y477JOXTcU3xKuu3uBlsgbD7/7Mz2LZ1Jr1XjwilEWlrIgSCj4N1+5ZmsQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.0", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.0_1726149814176_0.2353131748184858", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/expect", "version": "2.1.1", "license": "MIT", "_id": "@vitest/expect@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "907137a86246c5328929d796d741c4e95d1ee19d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-YeueunS0HiHiQxk+KEOnq/QMzlUuOzbU1Go+PgAsHvvv3tUkJPm9xWt+6ITNTlzsMXUjmgm5T+U7KBPK2qQV6w==", "signatures": [{"sig": "MEQCIAN1xcvAyqmt5/TrSmFHMUwwNoGPnDknZhHEm132JRo7AiA+czChmciOXyJSi6hJzltud8LjhOAUMy5P9kiIGvbm9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148856}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bb27b78b6394aca0adf739da27b9b1a4/vitest-expect-2.1.1.tgz", "_integrity": "sha512-YeueunS0HiHiQxk+KEOnq/QMzlUuOzbU1Go+PgAsHvvv3tUkJPm9xWt+6ITNTlzsMXUjmgm5T+U7KBPK2qQV6w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.1", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.1_1726241567733_0.5454632772497905", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/expect", "version": "2.1.2", "license": "MIT", "_id": "@vitest/expect@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e92fa284b8472548f72cacfe896020c64af6bf78", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-FEgtlN8mIUSEAAnlvn7mP8vzaWhEaAEvhSXCqrsijM7K6QqjB11qoRZYEd4AKSCDz8p0/+yH5LzhZ47qt+EyPg==", "signatures": [{"sig": "MEUCICrCWaYLaE4jxHRZNv6x02ssKh7QuVDReO9wjUhSBA/KAiEAo7+v9LRhxHPKLyhe2hm5HwRpGT3yW1YbeNqQHoelfy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 148874}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/73fbf3f9760445e78515f62b787098de/vitest-expect-2.1.2.tgz", "_integrity": "sha512-FEgtlN8mIUSEAAnlvn7mP8vzaWhEaAEvhSXCqrsijM7K6QqjB11qoRZYEd4AKSCDz8p0/+yH5LzhZ47qt+EyPg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.2", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.2_1727886016535_0.37200843926228155", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/expect", "version": "2.1.3", "license": "MIT", "_id": "@vitest/expect@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4b9a6fff22be4c4cd5d57e687cfda611b514b0ad", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.3.tgz", "fileCount": 7, "integrity": "sha512-SNBoPubeCJhZ48agjXruCI57DvxcsivVDdWz+SSsmjTT4QN/DfHk3zB/xKsJqMs26bLZ/pNRLnCf0j679i0uWQ==", "signatures": [{"sig": "MEUCIEAEaDWWf8tN9Ox9sjhHXAloM8eOh+AXYT37bFDIUlt2AiEAn3Q7ACl9piaXfxwGTITpS841pY9mB/8ov32/ZPs9280=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 149723}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7b8518df9c347402a74a8680a423bfe9/vitest-expect-2.1.3.tgz", "_integrity": "sha512-SNBoPubeCJhZ48agjXruCI57DvxcsivVDdWz+SSsmjTT4QN/DfHk3zB/xKsJqMs26bLZ/pNRLnCf0j679i0uWQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"chai": "^5.1.1", "@vitest/spy": "2.1.3", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.3_1728903940551_0.8294047310424753", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/expect", "version": "2.1.4", "license": "MIT", "_id": "@vitest/expect@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "48f4f53a01092a3bdc118cff245f79ef388bdd8e", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.4.tgz", "fileCount": 7, "integrity": "sha512-DOETT0Oh1avie/D/o2sgMHGrzYUFFo3zqESB2Hn70z6QB1HrS2IQ9z5DfyTqU8sg4Bpu13zZe9V4+UTNQlUeQA==", "signatures": [{"sig": "MEUCIQCi6qdxsmzOLnKTcWHITH6mJ9MTWovLU/iqGN1rTzsLogIgPX6+c2yQUYIdObGXbjitCF7apZT6kBQOespUtmaXEtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 149812}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/18a3b5e8999bdd501089616fbc1c8d32/vitest-expect-2.1.4.tgz", "_integrity": "sha512-DOETT0Oh1avie/D/o2sgMHGrzYUFFo3zqESB2Hn70z6QB1HrS2IQ9z5DfyTqU8sg4Bpu13zZe9V4+UTNQlUeQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.4", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.4_1730118449442_0.820746393055525", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/expect", "version": "2.1.5", "license": "MIT", "_id": "@vitest/expect@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5a6afa6314cae7a61847927bb5bc038212ca7381", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.5.tgz", "fileCount": 7, "integrity": "sha512-nZSBTW1XIdpZvEJyoP/Sy8fUg0b8od7ZpGDkTUcfJ7wz/VoZAFzFfLyxVxGFhUjJzhYqSbIpfMtl/+k/dpWa3Q==", "signatures": [{"sig": "MEUCIQDb+B6osQJ5lJJGqC88CRWJdwLYuDOXkKitOvOGWenzhgIgaJLyiJzGbh4PQZ/UX6qm6a9pAeHt0OVTzJlAS4QJ3h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165487}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1679d33da723e8142d56594311ebe1bf/vitest-expect-2.1.5.tgz", "_integrity": "sha512-nZSBTW1XIdpZvEJyoP/Sy8fUg0b8od7ZpGDkTUcfJ7wz/VoZAFzFfLyxVxGFhUjJzhYqSbIpfMtl/+k/dpWa3Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.5", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.5_1731511457084_0.9167452560320142", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/expect", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/expect@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3bf49423bf032c8370db8695426207300d4df8df", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.2.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-xsEpc6dbQggtlpoEhWtwpuoGfIJhN9zgGofTZC06a+HWrzp/JEx2xqAKeMNGXBQ0Zjw6zjjg4bmj0KJdiUCWcw==", "signatures": [{"sig": "MEYCIQCwQGTUwuVlk2+hv8w8FOfV51o/UArzfmcGwc+zO58eYgIhAMgj2gnqMRsbY6T/V5NklZa0QHEZu8MXI9LbJiXrlVeQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 169516}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.2.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c3027857eeba64227cb5f0848ba409cd/vitest-expect-2.2.0-beta.1.tgz", "_integrity": "sha512-xsEpc6dbQggtlpoEhWtwpuoGfIJhN9zgGofTZC06a+HWrzp/JEx2xqAKeMNGXBQ0Zjw6zjjg4bmj0KJdiUCWcw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.2.0-beta.1", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.2.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.2.0-beta.1_1731518248823_0.8389609698416238", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/expect", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/expect@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "667f6b76b2edbaf8dfa51e080df6315d7be207b0", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.2.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-E8p3ua0VwVo49KHUO5YUYAcSdC1nFDW4/RxVnhho29hGEMyGUv/JwJDC+idcn2rQ8NlgMsKEWDVIhRP77vf/3A==", "signatures": [{"sig": "MEQCIGVC1hi9yoZuorSMvAPV07/gYxMwIQslmabFNiZ7i+NcAiBQUTNA2XIcYl+OlEx2bV2m0sxMbPgBCeKCynIuD+MWFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 169516}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.2.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bab884bc7d26edca9bed54fa53aff577/vitest-expect-2.2.0-beta.2.tgz", "_integrity": "sha512-E8p3ua0VwVo49KHUO5YUYAcSdC1nFDW4/RxVnhho29hGEMyGUv/JwJDC+idcn2rQ8NlgMsKEWDVIhRP77vf/3A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.2.0-beta.2", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.2.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.2.0-beta.2_1731939498992_0.33548299469154697", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/expect", "version": "2.1.6", "license": "MIT", "_id": "@vitest/expect@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5a334eb9ee9287292fbe961955cafb06f7af7da6", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.6.tgz", "fileCount": 7, "integrity": "sha512-9M1UR9CAmrhJOMoSwVnPh2rELPKhYo0m/CSgqw9PyStpxtkwhmdM6XYlXGKeYyERY1N6EIuzkQ7e3Lm1WKCoUg==", "signatures": [{"sig": "MEYCIQDUBF5GCFgBOeqcypnyOF/lGaxFMrIy2enqt12OERUtEQIhAMvjUBmYQOP1rSyAUIHQiuVMk0za+zbVH1a3Asd6go9P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165487}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.6.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0ae4fe197884f594acd29242bd203ad2/vitest-expect-2.1.6.tgz", "_integrity": "sha512-9M1UR9CAmrhJOMoSwVnPh2rELPKhYo0m/CSgqw9PyStpxtkwhmdM6XYlXGKeYyERY1N6EIuzkQ7e3Lm1WKCoUg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.6", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.6", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.6_1732623847611_0.8502361295715342", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/expect", "version": "2.1.7", "license": "MIT", "_id": "@vitest/expect@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5ada3ec9f3060821c8f57880017a7cfb151a259b", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.7.tgz", "fileCount": 7, "integrity": "sha512-folWk4qQDEedgUyvaZw94LIJuNLoDtY+rhKhhNy0csdwifn/pQz8EWVRnyrW3j0wMpy+xwJT8WiwiYxk+i+s7w==", "signatures": [{"sig": "MEUCIHt4QdDwkUSaXaDNK9VRHaFztJG425ghKRaZAntC5UoXAiEAydq1ghj6VFEc2NtAmTP9HdLWULQcOa33exRuYPJvedg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165487}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.7.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4859b4fb27513c27e6869e3f327bc4ed/vitest-expect-2.1.7.tgz", "_integrity": "sha512-folWk4qQDEedgUyvaZw94LIJuNLoDtY+rhKhhNy0csdwifn/pQz8EWVRnyrW3j0wMpy+xwJT8WiwiYxk+i+s7w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.7", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.7", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.7_1733132954338_0.43520555054483046", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/expect", "version": "2.1.8", "license": "MIT", "_id": "@vitest/expect@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "13fad0e8d5a0bf0feb675dcf1d1f1a36a1773bc1", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.8.tgz", "fileCount": 7, "integrity": "sha512-8ytZ/fFHq2g4PJVAtDX57mayemKgDR6X3Oa2Foro+EygiOJHUXhCqBAAKQYYajZpFoIfvBCF1j6R6IYRSIUFuw==", "signatures": [{"sig": "MEQCIEFb8SogJFQ7QvUySOctYoBdkEeTWS//I0jC6jgy3CqIAiBt082SKgllHsfo/0JsEJWI4LEO6+lVWWcSvqUyHWKbNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165487}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.8.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/85498feff7c96cd92adf5b5a6b0185d9/vitest-expect-2.1.8.tgz", "_integrity": "sha512-8ytZ/fFHq2g4PJVAtDX57mayemKgDR6X3Oa2Foro+EygiOJHUXhCqBAAKQYYajZpFoIfvBCF1j6R6IYRSIUFuw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.8", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.8"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.8", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.8_1733150790113_0.3262322948584324", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/expect", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/expect@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0fcfcd0e8b2cfb903b5ac6c4dd273bf0cd5ff1a2", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-L0pMLAFwU6nxmCzA/cHH47qahml2Tfd1nBhqElzcjHHLiwdpw6q1n01/1q/Oaq1zVjMbw5DGPFnP9wtYwc/F7w==", "signatures": [{"sig": "MEUCIQCv+ddcDCIQv5mUuuxp84ACYECoU70BLIrDTGbqj7V/CAIgCbQ+L5IyFQaPjBcaoJh/BbZQnEWkpwEO9ZyJx3jgll0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 169514}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/27d9fd59cfb3bb08f6c0d4cd555afc55/vitest-expect-3.0.0-beta.1.tgz", "_integrity": "sha512-L0pMLAFwU6nxmCzA/cHH47qahml2Tfd1nBhqElzcjHHLiwdpw6q1n01/1q/Oaq1zVjMbw5DGPFnP9wtYwc/F7w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.0-beta.1", "tinyrainbow": "^1.2.0", "@vitest/utils": "3.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.0-beta.1_1733420031890_0.5490538740873347", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/expect", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/expect@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "93941b619d9cac6253b78e611ff128408b7da194", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-xdywwsqHOTZ66dBr8sQ+l3c0ZQs/wQY48fBRgLDrUqTU8OlDir6H1JMIOeV+Jb85Ov1XBGXBrSVlPDIo/fN5EQ==", "signatures": [{"sig": "MEYCIQDqa8NYb2eDpfLXfVV1eioZ3kPoMaZvFHDPZd3azNr8EgIhAK9CWher7rejSc5UoaXU6yUxDGVaBqqzC3+imMTNhKIE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 170908}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fdf8196232f84d4450c4e21a8784dcea/vitest-expect-3.0.0-beta.2.tgz", "_integrity": "sha512-xdywwsqHOTZ66dBr8sQ+l3c0ZQs/wQY48fBRgLDrUqTU8OlDir6H1JMIOeV+Jb85Ov1XBGXBrSVlPDIo/fN5EQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.0-beta.2", "tinyrainbow": "^1.2.0", "@vitest/utils": "3.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.0-beta.2_1733826112820_0.49116446388043244", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/expect", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/expect@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "37db73fbb29792e6ad7b0e152e6d09bee50410ac", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.0-beta.3.tgz", "fileCount": 7, "integrity": "sha512-KPxQ9FkPT5SBIBonCmDZe2XY6RKmSTl/ghzjAHip08GZXaZs/tp/iyE5ypFSTCoP3kdTc9TPdXpibHjdDmLMvw==", "signatures": [{"sig": "MEQCIBb9JgBd0OHMFQihHnV2PWULmMzS8M5UY/irhAMmf5WsAiB+A1gpBxm13D3q3Qn66+fN7DlHOdbBefiRDNDmOObAXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172046}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.0-beta.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b8f3da7db2432e2cbc65de4d2e0d7869/vitest-expect-3.0.0-beta.3.tgz", "_integrity": "sha512-KPxQ9FkPT5SBIBonCmDZe2XY6RKmSTl/ghzjAHip08GZXaZs/tp/iyE5ypFSTCoP3kdTc9TPdXpibHjdDmLMvw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.0-beta.3", "tinyrainbow": "^1.2.0", "@vitest/utils": "3.0.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.0-beta.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.0-beta.3_1734712377891_0.08677315929804275", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/expect", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/expect@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9d742566d2c4485577a60fa0a262a71feda4b733", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.0-beta.4.tgz", "fileCount": 7, "integrity": "sha512-wBBhMoM1z5M8exSDA8IkCjy4a83T10qwLmFHYjGiLQ3rV8OlexjGNOm28rRokcG+oYgR2+zcH3GU6sl/IKf/3g==", "signatures": [{"sig": "MEQCIE596j9v27bXzYU3oiQe/ukBMMQcbj6FIr/bM2LNuZZXAiB90/VgW6YtHDPd9saxWbBfgYb1pC8AmCiGIpw9URBfXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172046}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.0-beta.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d773acd3e507f463b52036f8a10efefe/vitest-expect-3.0.0-beta.4.tgz", "_integrity": "sha512-wBBhMoM1z5M8exSDA8IkCjy4a83T10qwLmFHYjGiLQ3rV8OlexjGNOm28rRokcG+oYgR2+zcH3GU6sl/IKf/3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.0-beta.4", "tinyrainbow": "^1.2.0", "@vitest/utils": "3.0.0-beta.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.0-beta.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.0-beta.4_1736346254687_0.9392271935875964", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/expect", "version": "3.0.0", "license": "MIT", "_id": "@vitest/expect@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "076ad48eeb36671c0400548f952e4c07df8f5276", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-Qx+cHyB59mWrQywT3/dZIIpSKwIpWbYFdBX2zixMYpOGZmbaP2jbbd4i/TAKJq/jBgSfww++d6YnrlGMFb2XBg==", "signatures": [{"sig": "MEYCIQC3D1cxio8MSbL5tKZyZuh8vMTBESK8rlou6wg+oCsiogIhAIVoYfrZi/RNEa82Y1P/si8RKEN0cXWPuYiDXB8jbKqs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172018}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0167ff55a590b755a9bb64d5d8cbb30f/vitest-expect-3.0.0.tgz", "_integrity": "sha512-Qx+cHyB59mWrQywT3/dZIIpSKwIpWbYFdBX2zixMYpOGZmbaP2jbbd4i/TAKJq/jBgSfww++d6YnrlGMFb2XBg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.0", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.0_1737036471699_0.8350093246649579", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/expect", "version": "3.0.1", "license": "MIT", "_id": "@vitest/expect@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "890f5dbec053c5374903e7f183fc947c896bcd1f", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-oPrXe8dwvQdzUxQFWwibY97/smQ6k8iPVeSf09KEvU1yWzu40G6naHExY0lUgjnTPWMRGQOJnhMBb8lBu48feg==", "signatures": [{"sig": "MEYCIQD8pkt7ZvN0gvrFdrXYBOcTLau2cAZ32fWcuTBXRpqppQIhAIo+xDtxfWweO+yQi755m1SFPNRoxA0oyx0rouCoTpDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172078}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/20a66577cc92e461a38131f172f5a531/vitest-expect-3.0.1.tgz", "_integrity": "sha512-oPrXe8dwvQdzUxQFWwibY97/smQ6k8iPVeSf09KEvU1yWzu40G6naHExY0lUgjnTPWMRGQOJnhMBb8lBu48feg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.1_1737055979245_0.6905514524108851", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/expect", "version": "3.0.2", "license": "MIT", "_id": "@vitest/expect@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "26a480597a80402f972b1ac8478071b5fd66a698", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-dKSHLBcoZI+3pmP5hiZ7I5grNru2HRtEW8Z5Zp4IXog8QYcxhlox7JUPyIIFWfN53+3HW3KPLIl6nSzUGgKSuQ==", "signatures": [{"sig": "MEYCIQCfm4GCsoaOpz19S9tya0hwxnxs291YQ4BQY5bVgRcKOAIhAP16aG4aYuCUuqvgezXOVmM9++iAmugWo72Pn1WfbjrJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172066}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ec09f9a0506cfc6535c0b767d9b5376f/vitest-expect-3.0.2.tgz", "_integrity": "sha512-dKSHLBcoZI+3pmP5hiZ7I5grNru2HRtEW8Z5Zp4IXog8QYcxhlox7JUPyIIFWfN53+3HW3KPLIl6nSzUGgKSuQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.2", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.2_1737123988834_0.9913654238145146", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/expect", "version": "3.0.3", "license": "MIT", "_id": "@vitest/expect@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a83af04a68e70a9af8aa6f68442a696b4bc599c5", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-SbRCHU4qr91xguu+dH3RUdI5dC86zm8aZWydbp961aIR7G8OYNN6ZiayFuf9WAngRbFOfdrLHCGgXTj3GtoMRQ==", "signatures": [{"sig": "MEYCIQCnziiZUyjCYTG3PtfnJlqltU8bPv9yVi4NudlMTDXZDQIhAMeX3HM7GEOmXUwKySL47SzSRu04XCPWKmLbYnbrdD7A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172066}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d76683bb8d39ccf9d4abffbd26351200/vitest-expect-3.0.3.tgz", "_integrity": "sha512-SbRCHU4qr91xguu+dH3RUdI5dC86zm8aZWydbp961aIR7G8OYNN6ZiayFuf9WAngRbFOfdrLHCGgXTj3GtoMRQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.3", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.3_1737467941086_0.23309326473954273", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/expect", "version": "3.0.4", "license": "MIT", "_id": "@vitest/expect@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "95c0a73980e99a30d3994c35b4468c4bb257d093", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.4.tgz", "fileCount": 7, "integrity": "sha512-Nm5kJmYw6P2BxhJPkO3eKKhGYKRsnqJqf+r0yOGRKpEP+bSCBDsjXgiu1/5QFrnPMEgzfC38ZEjvCFgaNBC0Eg==", "signatures": [{"sig": "MEYCIQDIOMpqvvx8HOEz57LXpB0VJ8ymKz5EiK1QFbKWpUCqwAIhAJiCKy3EjOd38qHFaTrxD+4pI1BVkpA39rPAQwkBZVkh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172066}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2810005d1021ab5ff2f8c60bc0bc0145/vitest-expect-3.0.4.tgz", "_integrity": "sha512-Nm5kJmYw6P2BxhJPkO3eKKhGYKRsnqJqf+r0yOGRKpEP+bSCBDsjXgiu1/5QFrnPMEgzfC38ZEjvCFgaNBC0Eg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.4", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.4_1737639717467_0.02327362903883956", "host": "s3://npm-registry-packages-npm-production"}}, "1.6.1": {"name": "@vitest/expect", "version": "1.6.1", "license": "MIT", "_id": "@vitest/expect@1.6.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b90c213f587514a99ac0bf84f88cff9042b0f14d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-1.6.1.tgz", "fileCount": 7, "integrity": "sha512-jXL+9+ZNIJKruofqXuuTClf44eSpcHlgj3CiuNihUF3Ioujtmc0zIa3UJOW5RjDK1YLBJZnWBlPuqhYycLioog==", "signatures": [{"sig": "MEQCIGrSVZFYcJ8v60QUGeoviR90kKTq5io/CXIqRdUWMqJuAiB352M826lwX7m1s9ap9/Nce0o3Gb8JVOvW31j0wUPZxA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 142549}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-1.6.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/116c00e1bce3556bad3cc889c342d661/vitest-expect-1.6.1.tgz", "_integrity": "sha512-jXL+9+ZNIJKruofqXuuTClf44eSpcHlgj3CiuNihUF3Ioujtmc0zIa3UJOW5RjDK1YLBJZnWBlPuqhYycLioog==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"chai": "^4.3.10", "@vitest/spy": "1.6.1", "@vitest/utils": "1.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"picocolors": "^1.0.0", "@types/chai": "4.3.6", "@vitest/runner": "1.6.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_1.6.1_1738589782958_0.5642414163740097", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/expect", "version": "2.1.9", "license": "MIT", "_id": "@vitest/expect@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b566ea20d58ea6578d8dc37040d6c1a47ebe5ff8", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-2.1.9.tgz", "fileCount": 7, "integrity": "sha512-UJCIkTBenHeKT1TTlKMJWy1laZewsRIzYighyYiJKZreqtdxSos/S1t+ktRMQWu2CKqaarrkeszJx1cgC5tGZw==", "signatures": [{"sig": "MEQCID9jTjLT1XjGLVeUJJs6Wdx4Is1UTcR0uicrhRkRIW5JAiBLOLHXs+qEXoEvOsabkqmcBuH8im45rfAY2vOdj4djWA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 165487}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-2.1.9.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/652e9ba0866a11cc237307b1d89c3798/vitest-expect-2.1.9.tgz", "_integrity": "sha512-UJCIkTBenHeKT1TTlKMJWy1laZewsRIzYighyYiJKZreqtdxSos/S1t+ktRMQWu2CKqaarrkeszJx1cgC5tGZw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "2.1.9", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.9"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "2.1.9", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_2.1.9_1738590268779_0.9797957528849417", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/expect", "version": "3.0.5", "license": "MIT", "_id": "@vitest/expect@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "aa0acd0976cf56842806e5dcaebd446543966b14", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.5.tgz", "fileCount": 7, "integrity": "sha512-nNIOqupgZ4v5jWuQx2DSlHLEs7Q4Oh/7AYwNyE+k0UQzG7tSmjPXShUikn1mpNGzYEN2jJbTvLejwShMitovBA==", "signatures": [{"sig": "MEYCIQDYkcPFSOAsGMGPW1o/eoWOTbZysbaLFrUWHzH70RBOJAIhANy15Q3mMkNPIIm5GckR7xxg95VsOZLlV7vN4ffXIKMo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 172066}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.5.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/74dfca4bab5a911b8b63c17036d41989/vitest-expect-3.0.5.tgz", "_integrity": "sha512-nNIOqupgZ4v5jWuQx2DSlHLEs7Q4Oh/7AYwNyE+k0UQzG7tSmjPXShUikn1mpNGzYEN2jJbTvLejwShMitovBA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"chai": "^5.1.2", "@vitest/spy": "3.0.5", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.5", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.5_1738591337035_0.11139586705494753", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/expect", "version": "3.0.6", "license": "MIT", "_id": "@vitest/expect@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "30993c0841203d2243826ee04fb25463eb86e20d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.6.tgz", "fileCount": 7, "integrity": "sha512-zBduHf/ja7/QRX4HdP1DSq5XrPgdN+jzLOwaTq/0qZjYfgETNFCKf9nOAp2j3hmom3oTbczuUzrzg9Hafh7hNg==", "signatures": [{"sig": "MEQCIF6YwRwsLLO5gKcvbsrlBAsqzJAVtE++1GfibdtxVmHRAiBWCaXptrGhL4CLTkx2dBlIMn6lxosVFP17cQfTj94LLA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 171733}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.6.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/efc10bf2729fad4565138e2901da5b90/vitest-expect-3.0.6.tgz", "_integrity": "sha512-zBduHf/ja7/QRX4HdP1DSq5XrPgdN+jzLOwaTq/0qZjYfgETNFCKf9nOAp2j3hmom3oTbczuUzrzg9Hafh7hNg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.0.6", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.6", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.6_1739885936870_0.7725119172428252", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/expect", "version": "3.0.7", "license": "MIT", "_id": "@vitest/expect@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3490936bc1e97fc21d53441518d51cb7116c698a", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.7.tgz", "fileCount": 7, "integrity": "sha512-QP25f+YJhzPfHrHfYHtvRn+uvkCFCqFtW9CktfBxmB+25QqWsx7VB2As6f4GmwllHLDhXNHvqedwhvMmSnNmjw==", "signatures": [{"sig": "MEUCIDjQn3Hngy2z38p2AZYYgj/8FeioqX+oyp/YtP2z9JHoAiEAvNDxmq9GdujUx8MAN1qPRBjLaLeXzNqqkF42eU18xg0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 171747}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.7.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1076bcfe9ee07051fea06467856c409b/vitest-expect-3.0.7.tgz", "_integrity": "sha512-QP25f+YJhzPfHrHfYHtvRn+uvkCFCqFtW9CktfBxmB+25QqWsx7VB2As6f4GmwllHLDhXNHvqedwhvMmSnNmjw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.0.7", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "4.3.6", "@vitest/runner": "3.0.7", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.7_1740419463337_0.7329610558103861", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/expect", "version": "3.0.8", "license": "MIT", "_id": "@vitest/expect@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "53c408180d6476c7363eb976dcaae8e7b1f1a078", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.8.tgz", "fileCount": 7, "integrity": "sha512-Xu6TTIavTvSSS6LZaA3EebWFr6tsoXPetOWNMOlc7LO88QVVBwq2oQWBoDiLCN6YTvNYsGSjqOO8CAdjom5DCQ==", "signatures": [{"sig": "MEUCIBBagk7PkNaQYgaF57GfispY2dTcaDCBYLcbjUeJ7U7cAiEAuPtDW5rRl5WVEjCwG/yyrJxiVeU9JtuW5TLyWLoA67w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 184673}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.8.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b179bf6e5724646806d5d5588b893661/vitest-expect-3.0.8.tgz", "_integrity": "sha512-Xu6TTIavTvSSS6LZaA3EebWFr6tsoXPetOWNMOlc7LO88QVVBwq2oQWBoDiLCN6YTvNYsGSjqOO8CAdjom5DCQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.0.8", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.0.8", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.8_1741274191148_0.6446540061668116", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/expect", "version": "3.0.9", "license": "MIT", "_id": "@vitest/expect@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b0cb9cd798a131423097cc5a777b699675405fcf", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.0.9.tgz", "fileCount": 7, "integrity": "sha512-5eCqRItYgIML7NNVgJj6TVCmdzE7ZVgJhruW0ziSQV4V7PvLkDL1bBkBdcTs/VuIz0IxPb5da1IDSqc1TR9eig==", "signatures": [{"sig": "MEYCIQChjk9EvOu95TGkTn1/cZv/diFwjO3KBNPMndYP2KQQdQIhAKS/VxDkNPpYm4RDfCRyT89qCkzaz+lxEqRVedKVjid+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182346}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.0.9.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e6e770ed1e5cd188937ca1d715376ffb/vitest-expect-3.0.9.tgz", "_integrity": "sha512-5eCqRItYgIML7NNVgJj6TVCmdzE7ZVgJhruW0ziSQV4V7PvLkDL1bBkBdcTs/VuIz0IxPb5da1IDSqc1TR9eig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.0.9", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.0.9"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.0.9", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.0.9_1742212772177_0.6346717752668218", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/expect", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/expect@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "246255f5a24eed7900cda9727114b9e377df2b80", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-LI6R0bD4RgI3vSKnAAmLGgfVaH5SkO82JDdYFhsI1lu9ACToV7L4aAVIy9sHVqU5a3ZrFPOQi2wA6J0shOoJ/Q==", "signatures": [{"sig": "MEUCIQDkboyJBS0uZmAdxN33VNTGR5asyz6QRsA6vbAlTtK/twIgTc4pkKQvkQsYV8xjchooGNd4sw0sCj0xkZ+cFZIiFhE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182374}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9e4f2b50b3aead289b0442ff256667d6/vitest-expect-3.1.0-beta.1.tgz", "_integrity": "sha512-LI6R0bD4RgI3vSKnAAmLGgfVaH5SkO82JDdYFhsI1lu9ACToV7L4aAVIy9sHVqU5a3ZrFPOQi2wA6J0shOoJ/Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.0-beta.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.0-beta.1_1742213865996_0.24032715194669074", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/expect", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/expect@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "135388ad0d94d1965c91b874208fdc8b7b8d662d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-eA+WlBwsbQaAKYKuQ7j7SdnCq5l4t5YffpJDLfuYv7cesM3sDDZsCJabydj0/BZRuHfYUBej6CkqwJ/VFasVjQ==", "signatures": [{"sig": "MEQCIByMzRTA7SnQgOHoL/fkOdyrRGWnH1HcDe/RrwpN+G1IAiBPpFKoOTtBitAs5ipuE4G8aeA0TsERspaS5pdsFRp8Ow==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 174828}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e506ffe38c8dd3a12d5e47ea5afa3d31/vitest-expect-3.1.0-beta.2.tgz", "_integrity": "sha512-eA+WlBwsbQaAKYKuQ7j7SdnCq5l4t5YffpJDLfuYv7cesM3sDDZsCJabydj0/BZRuHfYUBej6CkqwJ/VFasVjQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.0-beta.2", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.0-beta.2_1742545710483_0.8264007870035723", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/expect", "version": "3.1.0", "license": "MIT", "_id": "@vitest/expect@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "75c8cbcb05de3314fd10b4d21eeee57cf917feeb", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-6wNS32NQakc7N9vJfpoSEZW9AXQ1t6Lply4l2/iGGEMjtgwXhYsDH10nCaTq3PcOJms0jtkHcWUQcScjjHz8LA==", "signatures": [{"sig": "MEYCIQDmbYrbSQ3zV2P/GcB++eYiSfsZTzYaFHUFUXnRkkAt3gIhAOKuUc4k7ZxZ7lhmAhexP5CQzfdtbdZPLc8bL7gY+Uvp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 175143}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3n/td5k6fm13gd722lls3bsyvlm0000gn/T/4a9bda591a396c6928e0c1829c6d0ff3/vitest-expect-3.1.0.tgz", "_integrity": "sha512-6wNS32NQakc7N9vJfpoSEZW9AXQ1t6Lply4l2/iGGEMjtgwXhYsDH10nCaTq3PcOJms0jtkHcWUQcScjjHz8LA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.9.0", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "22.12.0", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.0", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.0_1743416084187_0.09197888492939743", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/expect", "version": "3.1.1", "license": "MIT", "_id": "@vitest/expect@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d64ddfdcf9e877d805e1eee67bd845bf0708c6c2", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-q/zjrW9lgynctNbwvFtQkGK9+vvHA5UzVi2V8APrp1C6fG6/MuYYkmlx4FubuqLycCeSdHD5aadWfua/Vr0EUA==", "signatures": [{"sig": "MEUCIQDLPdSusJwHVvdM8FlzQvmHzAAEZ7IBv2qkQos/jrPeaQIgM0jp+Pd5Wx8PaXDYdlSEOMB3UGdViYLd9ssX+j9MH8k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 175143}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3a32ffa0515546b9840a85410465d699/vitest-expect-3.1.1.tgz", "_integrity": "sha512-q/zjrW9lgynctNbwvFtQkGK9+vvHA5UzVi2V8APrp1C6fG6/MuYYkmlx4FubuqLycCeSdHD5aadWfua/Vr0EUA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.1_1743416353034_0.05590304912736355", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/expect", "version": "3.1.2", "license": "MIT", "_id": "@vitest/expect@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b203a7ad2efa6af96c85f6c116216bda259d2bc8", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.2.tgz", "fileCount": 7, "integrity": "sha512-O8hJgr+zREopCAqWl3uCVaOdqJwZ9qaDwUP7vy3Xigad0phZe9APxKhPcDNqYYi0rX5oMvwJMSCAXY2afqeTSA==", "signatures": [{"sig": "MEUCIFXgUTMnge6TW1XCALoJ+Nqc3buATORRWy8Jdh341FoDAiEA/wbkMLChc6VuwXSYvC/lQrWXYgedmr5YQP1y9+oY+mA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 175186}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b21f77d8d8335fc5f3d8f00e56bbaa71/vitest-expect-3.1.2.tgz", "_integrity": "sha512-O8hJgr+zREopCAqWl3uCVaOdqJwZ9qaDwUP7vy3Xigad0phZe9APxKhPcDNqYYi0rX5oMvwJMSCAXY2afqeTSA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.2", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.2_1745225927369_0.5983350021402087", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/expect", "version": "3.1.3", "license": "MIT", "_id": "@vitest/expect@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bbca175cd2f23d7de9448a215baed8f3d7abd7b7", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.3.tgz", "fileCount": 7, "integrity": "sha512-7FTQQuuLKmN1Ig/h+h/GO+44Q1IlglPlR2es4ab7Yvfx+Uk5xsv+Ykk+MEt/M2Yn/xGmzaLKxGw2lgy2bwuYqg==", "signatures": [{"sig": "MEUCIQDvw/oCt0M7bWQ4pYZdLqZ5DHTgbx/yIqzTXDomGWamXQIgAY9KPAdVq+g6idMknWPuMNNZJSHwafBDDfPY5uE5lSA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 175186}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7e0e4809a859e81dd74534453e5ca7e2/vitest-expect-3.1.3.tgz", "_integrity": "sha512-7FTQQuuLKmN1Ig/h+h/GO+44Q1IlglPlR2es4ab7Yvfx+Uk5xsv+Ykk+MEt/M2Yn/xGmzaLKxGw2lgy2bwuYqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.3", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.3_1746452718186_0.7842046396904847", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/expect", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/expect@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7d2dfcc17e3681be55277a16c8ab35513de7fbf2", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-T9DZBBlnSOdiyKMGVj//2qJNfCvQ+fGlFyAwpIXYKLi2OrjeqmuV2DxMVhNe8OrNWAVDCaw4O0pp7mBj/5M3dw==", "signatures": [{"sig": "MEQCIAP9N+j+C8WRDoaAOOumqLNM8XyRZp8wCz7ou1tqf69nAiAd+cnzVJoVA3MyjnEtXG7MCSQ8y/gaKJBGs9zKgbJjpg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 175214}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.0-beta.1.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/49b73cd54f2e15ee9bd8f7cb709cd427/vitest-expect-3.2.0-beta.1.tgz", "_integrity": "sha512-T9DZBBlnSOdiyKMGVj//2qJNfCvQ+fGlFyAwpIXYKLi2OrjeqmuV2DxMVhNe8OrNWAVDCaw4O0pp7mBj/5M3dw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.2.0-beta.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.2.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.0-beta.1_1746463890616_0.8986952207321903", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/expect", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/expect@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "045eb99619eb7ef525a6a985544683fc11a73285", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-f79XIidYvXs0xGsmd9R9I+ljcLGa6doiJXsRQ2UC34ENLOstCM1YRI9M49Dhu88l/qouDCYCt1/0BCbhtB876g==", "signatures": [{"sig": "MEQCIED3rf0hgUEiCTSC4ttJgTwY0x8Vr3wbTvBWMcN/d2wYAiAd1rwkFIokyoPgNVAdJW/7F0rY+lzIrqsPf2rWpa6Z8w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89399}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.0-beta.2.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1a37758981f4015a2ebff89baf950bd5/vitest-expect-3.2.0-beta.2.tgz", "_integrity": "sha512-f79XIidYvXs0xGsmd9R9I+ljcLGa6doiJXsRQ2UC34ENLOstCM1YRI9M49Dhu88l/qouDCYCt1/0BCbhtB876g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.0-beta.2", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/runner": "3.2.0-beta.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.0-beta.2_1747658337690_0.08551583227593884", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/expect", "version": "3.1.4", "license": "MIT", "_id": "@vitest/expect@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "837651a71682e3611c3df7b58b157ba485bd8029", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.4.tgz", "fileCount": 7, "integrity": "sha512-xkD/ljeliyaClDYqHPNCiJ0plY5YIcM0OlRiZizLhlPmpXWpxnGMyTZXOHFhFeG7w9P5PBeL4IdtJ/HeQwTbQA==", "signatures": [{"sig": "MEQCIGql2Fzba4EOcQryJfmfhYk6Z0sRzw9+8SvsP8BVrf/yAiBfo5elBc6jYiE8xP/DSTmFw9CbUKf1SkTNFi52zAXCAQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 175186}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.1.4.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b3e1c39323fe244f24b9bf94dc8906fa/vitest-expect-3.1.4.tgz", "_integrity": "sha512-xkD/ljeliyaClDYqHPNCiJ0plY5YIcM0OlRiZizLhlPmpXWpxnGMyTZXOHFhFeG7w9P5PBeL4IdtJ/HeQwTbQA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@vitest/spy": "3.1.4", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"@types/chai": "5.0.1", "@vitest/runner": "3.1.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.1.4_1747671848666_0.3339711478701206", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/expect", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/expect@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d6ba5a1cd99cf2a8bcde81ee77374ea94126e402", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-kwsUo0cWymcVexKYx6/mxURmXsoJHPSF48FBZKM5tK7ndOSXzkuOm24ZaJeU41HSwh5UQ3KsAivW8um7aswERQ==", "signatures": [{"sig": "MEYCIQCmtyBC5+oy0Ju/3EeGBAIPhjZnoA9JSdkhbrDS6682VgIhAOyHZ1uuxikTT7DID7fqaDD0ovKyFqmGfYjtcvM1XiB/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96533}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.0-beta.3.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ddcf0c507c5fe4021dbf01d1705faf0b/vitest-expect-3.2.0-beta.3.tgz", "_integrity": "sha512-kwsUo0cWymcVexKYx6/mxURmXsoJHPSF48FBZKM5tK7ndOSXzkuOm24ZaJeU41HSwh5UQ3KsAivW8um7aswERQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.0-beta.3", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.0-beta.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/runner": "3.2.0-beta.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.0-beta.3_1748442529113_0.1441653575877595", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/expect", "version": "3.2.0", "license": "MIT", "_id": "@vitest/expect@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1c1dacb9fe68f27b572795732c27a15c1ea5a502", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.0.tgz", "fileCount": 6, "integrity": "sha512-0v4YVbhDKX3SKoy0PHWXpKhj44w+3zZkIoVES9Ex2pq+u6+Bijijbi2ua5kE+h3qT6LBWFTNZSCOEU37H8Y5sA==", "signatures": [{"sig": "MEYCIQC7YwejcalfyJMGUExJJBeAsFDLYleS4weSDuupWRDulgIhAM5ch5VAw0mufWYHIbLRvL/4/s+vIkorNOTmc6XJGAN+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96505}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.0.tgz", "types": "./index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/55eb30d8c958201a1859231a9681507a/vitest-expect-3.2.0.tgz", "_integrity": "sha512-0v4YVbhDKX3SKoy0PHWXpKhj44w+3zZkIoVES9Ex2pq+u6+Bijijbi2ua5kE+h3qT6LBWFTNZSCOEU37H8Y5sA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.0", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/runner": "3.2.0", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.0_1748862659645_0.380278912547497", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/expect", "version": "3.2.1", "license": "MIT", "_id": "@vitest/expect@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "eace178f5116150110cd1041e6d52836603a2656", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.1.tgz", "fileCount": 5, "integrity": "sha512-FqS/BnDOzV6+IpxrTg5GQRyLOCtcJqkwMwcS8qGCI2IyRVDwPAtutztaf1CjtPHlZlWtl1yUPCd7HM0cNiDOYw==", "signatures": [{"sig": "MEYCIQC59al7/N2NQm1mvqNp/pg3qUnjlkJRbOyPTFEdOuizdQIhANTxDts+9KtZyA8hwZDnBIKfKCqvRXnAenzF5wnjf0GR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96443}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1e3a53af12867d8259a561d7b6717ae4/vitest-expect-3.2.1.tgz", "_integrity": "sha512-FqS/BnDOzV6+IpxrTg5GQRyLOCtcJqkwMwcS8qGCI2IyRVDwPAtutztaf1CjtPHlZlWtl1yUPCd7HM0cNiDOYw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/runner": "3.2.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.1_1748970455824_0.43616862917828514", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/expect", "version": "3.2.2", "license": "MIT", "_id": "@vitest/expect@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "187a1cb9de72ca8d723cd99d85fc3daa27af5b53", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.2.tgz", "fileCount": 5, "integrity": "sha512-ipHw0z669vEMjzz3xQE8nJX1s0rQIb7oEl4jjl35qWTwm/KIHERIg/p/zORrjAaZKXfsv7IybcNGHwhOOAPMwQ==", "signatures": [{"sig": "MEQCIFbiMvPTX0oOwjNn5IQOn1CEM94Ktjd6bOnvMbhc6thFAiAdOLC0UAr5pgJN//QUJHOJ3cPBUNpiJ9Av+1BR2U/G+A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96543}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f4f31cae3e5083c24092b2938f2df4d9/vitest-expect-3.2.2.tgz", "_integrity": "sha512-ipHw0z669vEMjzz3xQE8nJX1s0rQIb7oEl4jjl35qWTwm/KIHERIg/p/zORrjAaZKXfsv7IybcNGHwhOOAPMwQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.2", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/runner": "3.2.2", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.2_1749130948754_0.22765232890331322", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/expect", "version": "3.2.3", "license": "MIT", "_id": "@vitest/expect@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "45be98d6036c6dedbbbc51abdeca3bbd1f12450d", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.3.tgz", "fileCount": 5, "integrity": "sha512-W2RH2TPWVHA1o7UmaFKISPvdicFJH+mjykctJFoAkUw+SPTJTGjUNdKscFBrqM7IPnCVu6zihtKYa7TkZS1dkQ==", "signatures": [{"sig": "MEUCIECMl9H1m3P583h55GPxbGqLFz2VxCYxJquzyYqWPiqcAiEA+Y1kYPDfy9z7tyZIiT3+9axFlDAUhMvozAn4xGOi61U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96434}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/fe5df6c6f7b9c70492fe6e9cac3e2e62/vitest-expect-3.2.3.tgz", "_integrity": "sha512-W2RH2TPWVHA1o7UmaFKISPvdicFJH+mjykctJFoAkUw+SPTJTGjUNdKscFBrqM7IPnCVu6zihtKYa7TkZS1dkQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.3", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/runner": "3.2.3", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.3_1749468765239_0.055397066506924775", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/expect", "version": "3.2.4", "license": "MIT", "_id": "@vitest/expect@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8362124cd811a5ee11c5768207b9df53d34f2433", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.4.tgz", "fileCount": 5, "integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "signatures": [{"sig": "MEQCIAW2dAwyZ0VHEHNugl4WRTt7lWyDsuMHCZ/3RhbtpL9mAiBQ6m+Axg89qj9GnbqFNMO9GyV4xgGtnqU/y2xn2gTbYQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96737}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/bd01a82c54386068a90d577f900d05e2/vitest-expect-3.2.4.tgz", "_integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "3.2.4", "tinyrainbow": "^2.0.0", "@vitest/utils": "3.2.4"}, "_hasShrinkwrap": false, "devDependencies": {"@vitest/runner": "3.2.4", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_3.2.4_1750182854519_0.7878346322832672", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/expect", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/expect@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "da60fc316c436c67d9576b6c86f225d1c7850bb3", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-4.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-ldnrsNtnowuYoDX/XQEKRrPdFWWDA0cvDhO09MkIFE7TJ1gKnkNdkzNaq8ZVvvk/fY+R1x5yu/YWmer/VaLQHw==", "signatures": [{"sig": "MEQCIDPTOJ5I8I6n/lCEsPj7hFs+cYXJKQQmRQaWJeB7UfAwAiBxDMVnU2cayC5HlYcEh1SS+TEv70GKedG4YfRBtTgDTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96765}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-expect-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/8666fff3e3801194efada2505768a628/vitest-expect-4.0.0-beta.1.tgz", "_integrity": "sha512-ldnrsNtnowuYoDX/XQEKRrPdFWWDA0cvDhO09MkIFE7TJ1gKnkNdkzNaq8ZVvvk/fY+R1x5yu/YWmer/VaLQHw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "_npmVersion": "10.8.2", "description": "Jest's expect matchers as a Chai plugin", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"chai": "^5.2.0", "@types/chai": "^5.2.2", "@vitest/spy": "4.0.0-beta.1", "tinyrainbow": "^2.0.0", "@vitest/utils": "4.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@vitest/runner": "4.0.0-beta.1", "rollup-plugin-copy": "^3.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_4.0.0-beta.1_1750433328990_0.6659914329657075", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/expect", "type": "module", "version": "4.0.0-beta.2", "description": "Jest's expect matchers as a Chai plugin", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/expect"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"@types/chai": "^5.2.2", "chai": "^5.2.0", "tinyrainbow": "^2.0.0", "@vitest/spy": "4.0.0-beta.2", "@vitest/utils": "4.0.0-beta.2"}, "devDependencies": {"rollup-plugin-copy": "^3.5.0", "@vitest/runner": "4.0.0-beta.2"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/expect@4.0.0-beta.2", "readmeFilename": "README.md", "_integrity": "sha512-ueltEsioQTNsVHCK/K+FiLvvjk0STDywGZK4Kaj39DOk+WrVb3VhXEgGPe7nEMg8lR2m5BwAHsgTXSXbJHqSKA==", "_resolved": "/tmp/08c64b878b493ffffc309018dae3161d/vitest-expect-4.0.0-beta.2.tgz", "_from": "file:vitest-expect-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ueltEsioQTNsVHCK/K+FiLvvjk0STDywGZK4Kaj39DOk+WrVb3VhXEgGPe7nEMg8lR2m5BwAHsgTXSXbJHqSKA==", "shasum": "1c6a7bb0ec7fd6bad186b120267c270bcf1ddd14", "tarball": "https://registry.npmjs.org/@vitest/expect/-/expect-4.0.0-beta.2.tgz", "fileCount": 5, "unpackedSize": 96765, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fexpect@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIQCgl1Xr3tlShnR+0Z1vuKZBpq/Db/N41+/MqjZjaCLFLgIfQeu9FWevL6lPioedoUlfzSgKnOYLampkyesBVKz35Q=="}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/expect_4.0.0-beta.2_1750775108120_0.7818678866309907"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-01-09T12:33:39.369Z", "modified": "2025-06-24T14:25:08.767Z", "0.27.0": "2023-01-09T12:33:39.725Z", "0.27.1": "2023-01-11T16:32:32.458Z", "0.27.2": "2023-01-17T07:40:49.973Z", "0.27.3": "2023-01-21T16:23:15.864Z", "0.28.0": "2023-01-23T09:28:07.974Z", "0.28.1": "2023-01-23T09:51:11.618Z", "0.28.2": "2023-01-25T11:21:03.559Z", "0.28.3": "2023-01-27T12:09:53.830Z", "0.28.4": "2023-02-03T10:04:58.015Z", "0.28.5": "2023-02-13T12:23:46.371Z", "0.29.0": "2023-02-25T08:25:50.352Z", "0.29.1": "2023-02-25T09:25:03.699Z", "0.29.2": "2023-02-28T15:14:31.397Z", "0.29.3": "2023-03-15T20:04:00.718Z", "0.29.4": "2023-03-20T13:40:29.927Z", "0.29.5": "2023-03-20T14:12:15.654Z", "0.29.6": "2023-03-20T20:23:44.709Z", "0.29.7": "2023-03-20T20:37:54.478Z", "0.29.8": "2023-03-28T13:12:09.844Z", "0.30.0": "2023-04-09T13:39:03.649Z", "0.30.1": "2023-04-11T11:26:46.485Z", "0.31.0": "2023-05-03T18:08:32.902Z", "0.31.1": "2023-05-17T14:23:35.427Z", "0.31.2": "2023-05-30T13:04:59.759Z", "0.31.3": "2023-05-31T14:49:34.126Z", "0.31.4": "2023-06-01T09:55:18.922Z", "0.32.0": "2023-06-06T17:04:43.474Z", "0.32.1": "2023-06-16T12:23:21.596Z", "0.32.2": "2023-06-16T16:06:12.109Z", "0.32.3": "2023-07-03T08:35:52.460Z", "0.32.4": "2023-07-03T11:06:24.933Z", "0.33.0": "2023-07-06T14:11:42.357Z", "0.34.0": "2023-08-01T15:42:03.654Z", "0.34.1": "2023-08-01T16:53:56.670Z", "0.34.2": "2023-08-17T10:10:07.702Z", "0.34.3": "2023-08-25T07:30:26.513Z", "0.34.4": "2023-09-08T10:34:35.401Z", "0.34.5": "2023-09-21T13:50:47.784Z", "0.34.6": "2023-09-29T07:33:52.862Z", "1.0.0-beta.0": "2023-10-02T16:40:34.405Z", "0.34.7": "2023-10-02T17:03:43.608Z", "1.0.0-beta.1": "2023-10-03T11:31:40.303Z", "1.0.0-beta.2": "2023-10-13T07:35:03.554Z", "1.0.0-beta.3": "2023-10-27T12:45:50.582Z", "1.0.0-beta.4": "2023-11-09T10:13:45.096Z", "1.0.0-beta.5": "2023-11-18T09:44:39.523Z", "1.0.0-beta.6": "2023-11-28T17:27:23.963Z", "1.0.0": "2023-12-04T15:46:35.762Z", "1.0.1": "2023-12-04T18:04:58.892Z", "1.0.2": "2023-12-07T10:13:10.067Z", "1.0.3": "2023-12-09T13:05:55.810Z", "1.0.4": "2023-12-09T19:05:27.674Z", "1.1.0": "2023-12-19T14:06:39.400Z", "1.1.1": "2023-12-31T13:38:07.488Z", "1.1.2": "2024-01-04T16:58:51.399Z", "1.1.3": "2024-01-05T08:21:43.403Z", "1.2.0": "2024-01-12T16:07:32.237Z", "1.2.1": "2024-01-17T16:24:11.997Z", "1.2.2": "2024-01-26T16:26:00.314Z", "1.3.0": "2024-02-16T17:29:14.053Z", "1.3.1": "2024-02-20T13:48:39.612Z", "1.4.0": "2024-03-15T10:31:03.494Z", "1.5.0": "2024-04-11T17:48:09.623Z", "1.5.1": "2024-04-24T11:22:34.986Z", "1.5.2": "2024-04-25T09:12:12.758Z", "1.5.3": "2024-04-30T08:40:36.518Z", "1.6.0": "2024-05-03T15:22:27.469Z", "2.0.0-beta.1": "2024-05-09T14:32:38.230Z", "2.0.0-beta.2": "2024-05-09T15:31:35.328Z", "2.0.0-beta.3": "2024-05-14T18:44:51.942Z", "2.0.0-beta.4": "2024-06-02T12:16:06.324Z", "2.0.0-beta.5": "2024-06-02T12:27:59.070Z", "2.0.0-beta.6": "2024-06-02T19:17:35.847Z", "2.0.0-beta.7": "2024-06-03T11:35:59.823Z", "2.0.0-beta.8": "2024-06-04T12:39:35.940Z", "2.0.0-beta.9": "2024-06-05T08:01:12.771Z", "2.0.0-beta.10": "2024-06-12T12:11:47.363Z", "2.0.0-beta.11": "2024-06-19T20:14:11.181Z", "2.0.0-beta.12": "2024-06-25T20:16:28.113Z", "2.0.0-beta.13": "2024-07-04T14:03:53.378Z", "2.0.0": "2024-07-08T11:39:33.565Z", "2.0.1": "2024-07-08T15:33:10.774Z", "2.0.2": "2024-07-10T15:46:43.735Z", "2.0.3": "2024-07-15T10:03:40.778Z", "2.0.4": "2024-07-22T09:13:34.736Z", "2.0.5": "2024-07-31T10:40:07.981Z", "2.1.0-beta.1": "2024-08-07T06:21:34.319Z", "2.1.0-beta.2": "2024-08-07T07:56:58.953Z", "2.1.0-beta.3": "2024-08-07T08:17:11.372Z", "2.1.0-beta.4": "2024-08-07T11:43:00.784Z", "2.1.0-beta.5": "2024-08-12T11:35:27.764Z", "2.1.0-beta.6": "2024-08-20T13:18:41.418Z", "2.1.0-beta.7": "2024-09-09T15:13:27.517Z", "2.1.0": "2024-09-12T14:03:34.347Z", "2.1.1": "2024-09-13T15:32:47.956Z", "2.1.2": "2024-10-02T16:20:16.816Z", "2.1.3": "2024-10-14T11:05:40.752Z", "2.1.4": "2024-10-28T12:27:29.602Z", "2.1.5": "2024-11-13T15:24:17.295Z", "2.2.0-beta.1": "2024-11-13T17:17:29.004Z", "2.2.0-beta.2": "2024-11-18T14:18:19.180Z", "2.1.6": "2024-11-26T12:24:07.805Z", "2.1.7": "2024-12-02T09:49:14.498Z", "2.1.8": "2024-12-02T14:46:30.294Z", "3.0.0-beta.1": "2024-12-05T17:33:52.095Z", "3.0.0-beta.2": "2024-12-10T10:21:53.023Z", "3.0.0-beta.3": "2024-12-20T16:32:58.082Z", "3.0.0-beta.4": "2025-01-08T14:24:14.869Z", "3.0.0": "2025-01-16T14:07:51.869Z", "3.0.1": "2025-01-16T19:32:59.427Z", "3.0.2": "2025-01-17T14:26:29.074Z", "3.0.3": "2025-01-21T13:59:01.277Z", "3.0.4": "2025-01-23T13:41:57.639Z", "1.6.1": "2025-02-03T13:36:23.136Z", "2.1.9": "2025-02-03T13:44:28.967Z", "3.0.5": "2025-02-03T14:02:17.233Z", "3.0.6": "2025-02-18T13:38:57.070Z", "3.0.7": "2025-02-24T17:51:03.516Z", "3.0.8": "2025-03-06T15:16:31.411Z", "3.0.9": "2025-03-17T11:59:32.423Z", "3.1.0-beta.1": "2025-03-17T12:17:46.190Z", "3.1.0-beta.2": "2025-03-21T08:28:30.639Z", "3.1.0": "2025-03-31T10:14:44.355Z", "3.1.1": "2025-03-31T10:19:13.223Z", "3.1.2": "2025-04-21T08:58:47.544Z", "3.1.3": "2025-05-05T13:45:18.411Z", "3.2.0-beta.1": "2025-05-05T16:51:30.824Z", "3.2.0-beta.2": "2025-05-19T12:38:57.851Z", "3.1.4": "2025-05-19T16:24:08.824Z", "3.2.0-beta.3": "2025-05-28T14:28:49.279Z", "3.2.0": "2025-06-02T11:10:59.827Z", "3.2.1": "2025-06-03T17:07:36.019Z", "3.2.2": "2025-06-05T13:42:28.958Z", "3.2.3": "2025-06-09T11:32:45.424Z", "3.2.4": "2025-06-17T17:54:14.697Z", "4.0.0-beta.1": "2025-06-20T15:28:49.187Z", "4.0.0-beta.2": "2025-06-24T14:25:08.333Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/expect"}, "description": "Jest's expect matchers as a Chai plugin", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}