{"_id": "@babel/plugin-transform-regexp-modifiers", "_rev": "9-1d6da12cd301cd724ad988aeca0d5a58", "name": "@babel/plugin-transform-regexp-modifiers", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.26.0": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "7.26.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2f5837a5b5cd3842a919d8147e9903cc7455b850", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-vN6saax7lrA2yA/Pak3sCxuD6F5InBjn9IcrIKQPjpsLvuHYLVroTxjdlVRHjjBWxKOqIwpTXDkOssYT4BFdRw==", "signatures": [{"sig": "MEQCIDy3gpfld4XrrCgZpdVoJMyD/26yMfXsJAKIzgHcZxxJAiBhzEAfLcmIVg/CCdcIdVeLcbzgkbMRJ07RkTSNnLa5BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4216}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_7.26.0_1729863009902_0.6507034585866209", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "142d4c51ca0d84cf85eca93356deaf4cea702cd3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-3CJzfaG7ZLE2MW8Qx0SsJUA1Xfb7mDbEaMsgNghdi+0/NJrzNL9XhlL8H+VHG0jPVgj0aZbG0VNJSA84yAjPJQ==", "signatures": [{"sig": "MEQCIH18+LcJgUSxlJj1CN0UC/a1mUFZTk+33fFDZyM/LoAcAiA9QinuRwMEPh4QFH01INEHzl2YWvGUe4PY+SEpriNd8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-alpha.13_1729864469435_0.7283257600767539", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "0b97dd0c3f5f01707bec6ce76ca552cc50f565af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-co5Ef2XPExCkSSUc0Qw+A7ImxROHoAR6orlSGY3452CDXD50HiaC1ybmWHyhRya9xsuUU74p+55uOMcXtt0uAw==", "signatures": [{"sig": "MEQCIHF4QbJjIS1Lnyvhtrdzi7z0j+BaoRAmTZbjrLrSfADeAiAXkAHpVQ/5cwT3ZXCVNvOONLlLuKL+nmFPkt+OITyJ7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-alpha.14_1733504059393_0.4302551882294223", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "ef58c62543388f063065dc79894bddf3283280b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-hh61Hq9kaW5ykkUrmQJ8BNswV57xE1AI3lTo2ECr9e9i0UnOIArXko6iAIcRD5ITHghzyU/ZJtCsdusEyTMbZQ==", "signatures": [{"sig": "MEUCIBG8cQPKFeg/lLs/oFKVjwfIN4B5jwPwyMzhcV669cCgAiEAld1y2r0kUki6bS9sxheiKhlIaCGuIZz9CoK4LUN/r/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-alpha.15_1736529887536_0.07724720605943403", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f51fa2ab92ff0233bd1ce80878e8a091f2fde42c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-+amceiJyfRHHRc0J86+55FuFNiHG6YAwgNrWf/GjMgphXFgq3CvaAybLDI0CG7aWWhQ4Zs+78vFnjV+9tMmSpw==", "signatures": [{"sig": "MEQCIF+VDTymFfwuaonHed+IwI9ZdftRFrFU4PU1scmQdTMAAiBPb4pNajQEd4j75JILxYGIqECd4YKRVmyWORLD17s2cQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-alpha.16_1739534363496_0.0019161638482496635", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4b8252e270bdcd3111771e46af1bd09ddc6fcb32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-HIIv5nlKL2Ienc7mOz7UhYTiEkXVnhLV+C24LMgSTBmnm4yQUN5FTNoIhqAUZjBLtacHFU2PYV7c8ZvPd1pj1g==", "signatures": [{"sig": "MEUCIGyogTKxPvkaXWlvrmO/hgjw/hdCZP9Zo3evciffA2+kAiEA9BytQFeAflq172ATjjIX4y1so9rItGab+N8vZDZHIO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4193}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-alpha.17_1741717516372_0.8850473563310652", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "df9ba5577c974e3f1449888b70b76169998a6d09", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==", "signatures": [{"sig": "MEYCIQCFZgJ2mZ66N4tzsEJZnXGa0qW3F+r8NZIJulMRT0egwwIhAKIrQvk2k4+VjCmhKDgqzV12DmHjAR6mta40BdvHzmQJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4216}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_7.27.1_1746025752655_0.441191562121406", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "13ef50e020be794e73645af6b25fff52510af54c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-a5T5+Y8K<PERSON>+pq7JuNCSdfweakZKhEdiAzaN9H/PJTcVut3eaffCdXL+IRsMAQlkOOAZIJP6LKjc5okHH5WvCwg==", "signatures": [{"sig": "MEYCIQDSR8BZwIpWVRUCzc6fwiV9Yzj2WzQP6vBzhQNI0AcZfAIhAO4u5dHe4SeHES1rsHp2HxSTqrodMmczLPyDSwCT/NWF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4169}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-beta.0_1748620288966_0.25073406047351443", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-regexp-modifiers", "version": "8.0.0-beta.1", "description": "Compile inline regular expression modifiers", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-regexp-modifiers@8.0.0-beta.1", "dist": {"shasum": "db67457857b8c6baf1fd41cb224e41334769566f", "integrity": "sha512-NOIAfJGDcjufk+iRAtpMw7yvRi3ngU3vq4Ca3gZ6aMNBr+RzMBO3mY+WnjrRZAKtTHv7yjIZkYvnJFNSXTXWWQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4169, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDT6rM9oVJfqlgALfZcjQ1QNbuV1Cg00G/a3VsaUE/RnwIgR+s+zii6tRgA/xVV70/w3O42L+hvKMZb2JMb6V9nC8M="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-regexp-modifiers_8.0.0-beta.1_1751447073338_0.05817960360746932"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-10-25T13:30:09.755Z", "modified": "2025-07-02T09:04:33.760Z", "7.26.0": "2024-10-25T13:30:10.052Z", "8.0.0-alpha.13": "2024-10-25T13:54:29.630Z", "8.0.0-alpha.14": "2024-12-06T16:54:19.650Z", "8.0.0-alpha.15": "2025-01-10T17:24:47.716Z", "8.0.0-alpha.16": "2025-02-14T11:59:23.662Z", "8.0.0-alpha.17": "2025-03-11T18:25:16.560Z", "7.27.1": "2025-04-30T15:09:12.841Z", "8.0.0-beta.0": "2025-05-30T15:51:29.122Z", "8.0.0-beta.1": "2025-07-02T09:04:33.535Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regexp-modifiers", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-regexp-modifiers"}, "description": "Compile inline regular expression modifiers", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}