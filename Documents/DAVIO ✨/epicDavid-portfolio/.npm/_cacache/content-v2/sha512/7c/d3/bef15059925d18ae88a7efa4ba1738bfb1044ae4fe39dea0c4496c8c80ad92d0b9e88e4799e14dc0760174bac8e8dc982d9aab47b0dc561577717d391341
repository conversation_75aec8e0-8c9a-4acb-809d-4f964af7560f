{"_id": "@esbuild/linux-arm", "_rev": "95-5f02a7532f4b0f34270debea4f3dc79c", "name": "@esbuild/linux-arm", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/linux-arm", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/linux-arm@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "544de8498fd4a94b91d765beb0f5c89ad416ba54", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-maGhiEr+zgH8Ero3+JY/34fhBXHIJQJfSZuoZO/i1ZEJUPZ9sdDulJlXgAj1daszBThjOyzX2ntWN3fh+zX6zA==", "signatures": [{"sig": "MEUCIQD1w8uxOIt/F5Xh2QcV7FAG3JHul5DkNIArEmrvuyPP7QIgQb1P0JK+y8CeuY3GdQ2aIzmbWC7WclBh5GlCsaAOpOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoKSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmripxAAonWMiqBa/c6G2/OJpEewWbCHfgY3aGUZmTYPV+CBgKnyDGr7\r\n9wZZAt5yNW9ynD2lP7fRANLPCNIE1NhbEwbYheksS0Z/KHuQ6nc1jvlR/kSd\r\nZZlGIwMepA5BRh3QHWCEEn7TwT5afmjY5Ty/1dbyLUYhrBSwwJl8do4oNXlS\r\n9B33UjyF4o81XRcPtVnY8Er8s+lCF9aV+9Z/dIlVlG/H7wtATzOEdWOSw4Hh\r\nEuMGmdyTTOX5A4xA4ylAelW/f98yc8Edbh7baGo1eHam5DMx1JPMeLXCtdwl\r\n8aUI7HWj4VLO/nnowbNScwTFP9XZP2DjzpLHHaCwfhj5FLzbGA7QB3M7X82J\r\nvFRtfxsGtWqM8fQbzW2Y2nO1dwkZuDR9vmTwur+4JxfG1RP0YMtzKkUqbK3b\r\neCzC2jwafDn8OXmEivsJpZXoGC1wb878GAPnjlFsu0a/6akrFFQ4YyxPmtS5\r\nTA2anY0t089VO2VB5OcSKD1/pEVsvB1ZSLpds4c6HtiRSvFvHln6is1itHKA\r\nZaxcR/UvcH5oNljs7bKUoMNMBZyosgchGS1bX9UT0fxFhkhupGS3U1+a9g83\r\nDlayWR9ajBpkF3WC0O6/iJsfp0JDBT0yv2/wKxswENmZUb4mIP8jXFIlcB3j\r\nHZv4bCr+O5Xlz9v4sXdhihO1wyliVSPLpow=\r\n=P/uL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.15.18_1670283922095_0.4753382393709329", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/linux-arm", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "96b890d892a01ff5d80a55de23125550bd2ceda9", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-ZAu3k8WpclM//mfoeWiQLLihL+1MxC9k7AhYgLtPdEJp8rADyHQM7xRQvmd+BO2YKR1Ts+WGC7OCJvo7Pqz/QQ==", "signatures": [{"sig": "MEUCIEiispBdi2a+GKphC5gQgo4c2BzPWtlUR0btujO9iAH8AiEAjJITabz2LYQGRPQXI9A4kcObwQnY4lCVvDMLzlmaQFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsHxAAhOUeV3J274K9fr0k5EW7ueoDaSpuWVYnqbhf5VQ3chVYu0DJ\r\nUG0hMPZ+YBN6snd0CZZu0f+rdi4KI7W+LnQcnpQpGD0cAV/GuWtbccIzsXFn\r\n/d9go41GuxWhHKe14pYZgb/IJjuBpub2TCtTOH4IeqhLqhWZjZJ/dFuth9pf\r\nd1QK1yvhhoVKG/8uOxIa4aFpsyWSwHYrVjmpSyZs8/ncrYknMWWuNGClOuON\r\n1JI9N86LK25L83+HFxZ2bl8yK3bIUhlmVJtaNrC3a0L2UVWhI9heHuDgtfDX\r\nmI5blxWK6Sz8ArOO8TR+PrRyJVnxZ0jcdangjDgajSYiFNwnqaShwy6LqTtE\r\nE/1F57Lr0TySNAhjQuA6EABsMPdsfepP2ZRaPKRtDjpQETXcJR86+WofQGTo\r\nXNWtpQ5WhwYlaf5hqVuKUnvSaIGIdcAjy5QF0xc5nLz/DcWpv6jjxpviDIHb\r\nUOJNoiK6vdsIkVl9vlaXOve1Rr9rmKGGEQC5gD/icBaQpMEvW7gJxA1dn7PT\r\nzy/J3VM0H27whS6sE3tO1PclEPEUxYiEDQgE3ptjqHNM3qg6YWRvSsS4GD3G\r\nT8ORFYDtG2I8YL7GhhzMpxOh3QSlaBNORau89y3QK3bHjzuahOeJD1iCBQpN\r\nW1KLnGO2PNAKV6ZzWgNolBxADfW69Pt4/Gw=\r\n=RkVG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.0_1670385319661_0.39050496361881804", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/linux-arm", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a54ac0c0f69b504134fbe7f173a66432f54331aa", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-ZKBI/JEIjcG9YbERCDR1qPBVjr47+BKGp32Iz2cf00001yhF8mGPhVJse69jR3Wb1RU78BijVKhHPvZewsvAKA==", "signatures": [{"sig": "MEQCICbuxvfRWjEYF8gn4UMu66V+k74Aw0acFh1DDm3LRizJAiBViq9TxGcXcHQxgLKpCzRwEqEgFz09JX0HhpNFGwvISA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBswACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5ig//djjQ7hUpOr2YzKai53RBgAVWOXgj83p5VZqVnLBVLWufZC/t\r\nNz3fxEtEuwQboA8Ggla1/DlV8nL0PZspNTWhRxiX0IXHdxor+bGxR0IrHl6d\r\nXdx4pr+Rf22yMqG6ehkabYB7kIfVPVesmskYtCoh7K4oojVUEjNOdUBmSjsF\r\n7uR7eA+cx70F6VP7k/Gv/t2qwd8kqC+IjLnASV5JRNbCivt6BJxGAdLR8bAL\r\nJ/c39G5clRYrMZyZpv6TyWaRDZBEknQgnqp5CydfMRuFm+OWWrufAqWJfNea\r\n6GofOlFX4n+EcXklv96jIstUyDcZET13cCJk4tHAUymlCe6UjT1xhxjYCjaa\r\nHjAzyHxLxSNzcKC9CF4bmHySkPnPlTpCeyaFOIFjt/cGNCpBC2qYpl9JQZDM\r\nlwrNKwd8RJDf0b+NG6FctNSoxCq48aTJkjaE39QCdw5F4lsHfkZlBCRVgHln\r\nbjCQqGYZe69qc9SnxatgFNrrYEan94I99MuwDRJWVQQu1+GuzWF4aewUrGOg\r\nKAse1eXTHZMOfdj/YnYQXYDNjCuD475WPcJErEEoZHVGchc+aMZQtq/1Tj43\r\nV7CYpMYzumj5Ic6Pk/+Km9vsspuyQKHm8o+IrsT4+Gvv/5j7USjnz1aue1PQ\r\nbOcy1LcLZONfyd1LgnFlJgNu3ahHyEc5CUw=\r\n=XRA+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.1_1670388528424_0.9088275961734764", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/linux-arm", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3a816b6349655ac5e80117947543028d8eedeaf8", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-8n2UozHygOGXzgysim6GifKjv+lW4fs3mlfaoKerwBIOT9OBCo1Q4AjvbtU3F+2AGyo8eavxnj6Xxx0DRTOwiw==", "signatures": [{"sig": "MEUCIQDxYUJlzVhsiFEMPhZlxAkEmgBue1SUPhrUTG5NPb2UfgIgKIRwsMu2w965CogKd7+0707g7lI8Xn3vUfSgyOZiHNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYt5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrfsw/9Eauf/EWhtRpPNyDP3DPSrQ1qZRTmgd12mEjqqk5+TbYtVUS2\r\nRCO4aSr52br246M9sEhX+8EHp2G8UVhFazSQpTocq3CsW3slmC6KckdNWPpe\r\nCUjpfwGtHAH4BPl8i8932BRgWCUbNi2w/NkxePJkXZaZPCh6jkp+ofb8lwNl\r\nXcBNKZDrYlDIwW7SRQrgmDwyJlVaPeWF0XTrWcLYj3bpOBVOITBMKEk++h7J\r\nObQHIoz6CsS7tYbNrCIJsLIicYpGrR1aSfFZTuI0RfzDPbHVuiU3ad4J+pvw\r\nBcDlTGuEkCxasY736Ph/pla0t+5TAmB9iobrOyNkwH7IyC3iPWYDMeQmW79I\r\nkn5tjB+UQNJHzVYzyxn/JdnLb1V+VinbGdOntLYdFyzek4EE7iI1FuTrNFIA\r\n31UWfvzm9eb5SI0DhGhXjtGut7r9PzLgr6oBh6RbhNMhAoU/u2DBUACdpUQk\r\nxljI5C+Haa4T96gPnEoRlCaqnX93MJuJ8zWTOxQR8kbtDDDvOW9osW9oBx8r\r\nPPk+de/cDNVxqQSlSbLjmICtO12ingeH4yHDguGA1sBsD0i+rotkixm5s9cr\r\nXABehWel5bxE+8/+xa4ev8KDPYmXnAK2JbVKBHAslAEQv8oGZN6dUuDSZlNO\r\ngPF8R9LloI/9FghtRNkLEvuLshKiqMjnbzU=\r\n=1pVL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.2_1670482808991_0.7010476533786913", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/linux-arm", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4b3e9f849822e16a76a70844c4db68075b259a58", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-VwswmSYwVAAq6LysV59Fyqk3UIjbhuc6wb3vEcJ7HEJUtFuLK9uXWuFoH1lulEbE4+5GjtHi3MHX+w1gNHdOWQ==", "signatures": [{"sig": "MEYCIQCUsXbtooWnvsUP/RvS8VkP4swF16nl6wL0u9yJunR8OQIhAM+MWowT9CGL20v+394DzqnB5TpGJyAXrry77T+FWFPR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPDw//asQWOyhyP1fy8ogE6xzbF02nrx1HULuN82qNARlAbp05LvrL\r\nTzZRXqH9UYOq36EzKbHGn6Ce3IyZTqbdkV4OYFK3mc9RLrIiwWUQpaDQBtv3\r\n+h5HHJCKM+EA+6sc3gq6aBQlMaamb6cgZpnukRqyCiUHOXTr9UN90za6bMoK\r\nfQa1MVbGQCedFk52yXqk80Tks3ABsB3x7CI/qr1nh2yOmk3CWYCM9aAMRvMi\r\n6X/zlrXqdLtji5jtUTeCdzjn/VJodLoyJreFCfDJXrPLWUPKZiwuriFHjJdf\r\nokFlsB0b2s/hELWF22/jCfI83VWFgPTwo4sm6tEQ7NSBz5FY/Wfk/bFiQez1\r\nwwI/YTUkFHf0BLStJJ7acC7GAvEkjbGp3lZvE3oX7e1R4jTY0C3BmyM/+2KI\r\ndE5jfsrUDbJjRKST0XH75xh2R5BApMi09Os3WzCk+1j5kqOijwVVUkSJQRnU\r\nvqZQnotxMiAFi5h+Av+Q+/rEnrRKrL0Ju0yu8jGYG4mW6VYrQyKKwlVsCPW2\r\nLpkBLvrz6eidGQoNcT63SGiN8LKhLicV8vVXUjf0jByrTurwsVuiP7uZkIvf\r\nVtmNrzrmlEUFXaFP2QWZDVPNv8abBubQM7hnmcnKyjJNFqTXHLLH6reKyvom\r\nUwk4KhWcyyRTjHNKyei7oCPCCAieNen5bEQ=\r\n=MiK2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.3_1670530404941_0.44969179810612037", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/linux-arm", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b37f15ecddb53eeea466e5960e31a58f33e0e87e", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-A47ZmtpIPyERxkSvIv+zLd6kNIOtJH03XA0Hy7jaceRDdQaQVGSDt4mZqpWqJYgDk9rg96aglbF6kCRvPGDSUA==", "signatures": [{"sig": "MEUCIFPoPJ2S2i/9AOoHQvOB5mp4d0nkmnDSqWl9ZD7k1vViAiEA00GXJRnpMbXJsNZISdckZvF/gpR2pNa9EdZZVtZe0A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnyQ/9HdWJeNBgR3Fja7hNvB/LeiVD7d6SItDWmZ2+XwrQqU/hoYl/\r\neUgtJcy/jlNLn2a5Pjqwwo0WcbFMGnH1Aejt8s/1mqyCaUL2piufWOhgCPZH\r\nmAQsbnwEV60rV+3mRsgicCTFpOtDIPGTLcwnntSgH1MucUENXFxGjBcXAeUm\r\nj+BzVR3iiKLXGAc4ABCtcu5uCLd1ULEBkCEYhT4p0wqY2kFEiRiI1ihWqUhs\r\nBZYhHKfqWr2shNt4X4oSuQLLKxCuZxHjC9296zF0EgTK2eav19OYhrzOPG57\r\npHhGEjsc0o9VQ3r2Ul6gDP9yEQjFC10v9rc7YGqocfUpth0oE74e/HtSpmj/\r\nnIWQ7cMlZRXhwRVGMS443DvOdZo1lHjPXamfroDM6B5tms+ePTlQIcZLtWx0\r\nX6FGc+45XKOqs2trR2ApzbwCulylDm1lul/OKXE6EY4MnHvUGaJVgTcOcQZ4\r\nyhfmsBQO+5FykRNBgIOL/IalFA5mZmk6DPSYPKve7l+oMbii1h5wzfnrKDoy\r\nY3K2qRy8BoJuAXPyRIcvlmpqDw2IqSt8LuSYzYDlafXY8F2N8c6o7VKBcS4c\r\nXr32F3q/pPKwNy3hRDh2rsFC6DfNLs5q6K5D/PBY86QD+4MHqxmW6hHpFBKz\r\nyZNNdeI/54/9jSBGLjIXS6DGZ1inoQQzJe4=\r\n=8SLZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.4_1670644252758_0.23940503357633935", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/linux-arm", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4cc5ff0fc093610598f32f7fd5565326c0c4e5c6", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-6crdpqwFjl+DObBgwaJMtB+VWrZd87Jy05gQTERysc1ujnUJNCJzemUcRDT5hM34dzTYThlXfFW32qQy9QpPGQ==", "signatures": [{"sig": "MEQCIHi1V2H8jVxAp7NmXPU276E3K562audlhQW1PUg7GnWBAiAv1oMG9SqW9QYIvvNpMsocHECOqwhY2307Rqrt8qOT7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqANQ//RAD/ajZcM0EDsdgkAkcunJ3U8qBF8fLbnnRgoTyVZJ/XPdvn\r\nI2bYbNEa3ifIBpDMQSrPJjNWHyZUhiHEJjVeFHYBOM2aRGU1n6iu2/cFqLZT\r\ngVSRt3cMv4upsJxuN75dSQ+1pk2y7N2WOBAHhVPtd3UIRuRhelwRm9SI5ZFz\r\nCog7WRQUnVafx0YfcnFbaYot8cDcMalTvIqFY6Wrtx/nklTp2Me1W+z62BHX\r\nVfKjTb/n730HHhDN88DHtGeKmY3kqJtML5DGB4YFAj/W4ihkDQC7c8ZcIccR\r\naPUS4N3B27czQ4/DDM9bVFMkaPCH/Q4BnVvpQIgHfpZX81oJ0MAoyNfzFz3Z\r\n55n2DDa5ibK5nHqJ9F7c87/nZDfNsN1FiPnRPyCS8LpAK+AjtK4t/g57ImLP\r\nA0uRkOeVGOuOKYHCgfjG1zMgOafMJ2gxmxLfPEXANoCKa5hXwpcl3KXn/+Wk\r\nlUO3TTE1x+RIXeDKPQLBjDsHmDK+d+Z3y90YS4H1SVsW6TXyTalgTxGQOsHo\r\nUjChoI0vAK09XjJ9uhSfb0tiJg6zmat3XgFiRAHxCAviyrWlv+BwpXDaPvKa\r\nVKbeY7Vqhfgdneus3kPgUvNGcXWH2YC3ApWOHKA7JJztIr4jdXCMyvhrg5OU\r\nflYExAW8p2eFmpNIJGfc7kqdOrj8h9Hd/YM=\r\n=4zzN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.5_1670953678582_0.049258942963814834", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/linux-arm", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ecb1543b12c27f7e4c7ad67d5e13d9a08d034e55", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-hdw0JS24ToFAnWJJbexr62ZRTcl/yJSPeNZR4fAAJY4PcghgQcnp8lO5MdxBe2QCNz3i5WYCoGZcU4+TBJJMDg==", "signatures": [{"sig": "MEYCIQDDmMfFoX8VlxBRe3V83t9nfzxAIHonfgpXd27CDIxsfgIhAIobtk563lCRJEgiEkG04CZsAH13yz8MX2pVys14Alet", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwvg/+PV/wSlbaFTyezb9jk/mtj48CG6w7MQVquokMaSGpOi3jXjyq\r\n89xoMYGUhjP7HAwOKrlNrSuCoGxARypG3Hu7xSmKl1JxKHx4HppkksRakVos\r\nUh/3u/yJ5pULNRgryeOGan/1V6JIrUcSmzuwk0BnTm/sC5JbD//FmP2dkPAx\r\nPjjNVBoHFWnIHzz5b46Da+oUi0+4sKR6idCr3p8YLuouBwzFjsYqgqp6oDu5\r\nR3tPpuWX09qC3BJmFYxQ6KgqmESxLmGqrsNW55q9TR5kuoMg0tMWzFvvDyS8\r\nqXyak0RRqGDOYCrkX7lEYX+O35qBcsOWgtIS5jzF+VRdQByvxlCbdi9ozp3J\r\nPpRK6R+lka/7WhDe50PhjkJQ/laN4JFAreh3aj6XMAalzRwl7mqrEodDBQ94\r\nR8XcdIaqzZGbERDv08HAG9+hxnmKT7hnkCtU1Gao3ZmDNO0hVKbvL2DSGYWL\r\nam5aWAn/Y5VFvTR2QCFke9JCQ9IUfZREzBhZsETY9Pwc2Y9i685klQCj3HPL\r\nIyE6+OoRSgyF7BppLsGejmQcmDfKws/Y4yX9R5rua865BsKWTffoW6ipDiYL\r\n/g5OS64bHRuRAjAnrc9cNX8ZqLcDX2NXrhAgbZS1E5EQChqkOEZNUHJMPgu9\r\n7ZuierzcmMszFgXCfewtns+3kZa0eM1Ve/U=\r\n=rLng\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.6_1670995411358_0.39692095738169075", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/linux-arm", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2c7cf7244e4b8a6f757a87a113d83d8a0c1f5297", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-QqJnyCfu5OF78Olt7JJSZ7OSv/B4Hf+ZJWp4kkq9xwMsgu7yWq3crIic8gGOpDYTqVKKMDAVDgRXy5Wd/nWZyQ==", "signatures": [{"sig": "MEUCIGpWjOKGqyhjhFl52hWIN6zumuULCILZxL76ahBFyHAzAiEAtjkugOxvt5RTe/Ov8P2eHafevQ2BoFYRc8q9esf9dMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqcqg//XeXNI48+mxyTx+ijcrrNRXwjWU6gfIkNnRMSAf9EKVUkHLh3\r\nt/aQEITBq2UGuQzsc4rCHfZIzApri3hOODmYDWlo3EOj1/XV32Uu6dPm8UFy\r\nLlEEyjRba4SrZcTtHX2FLt2Zxdfy82hntiPJIpe9lectyKdRHDKOUaFRZXKM\r\nPp4qok34yAnd+3J+Udg5bPJqizkZJiN9UgTgeuKHEfdGQDod71IX6KvZ3+Q3\r\nfHQtLlREkro9gcuXdNbLfKjiGEzH7OQ7zbm61251ufVE2PeRkIrel8gd7fu3\r\nP7WH8u7iMffkMTV4tbCDBKK5qj3z1beB1zNRfYf7UB7ea6H+/Kv4a6TKF218\r\nKSJ9tihBH4zKeY+dUhQ8ZiGbeYHpeEJxqpcZns1AkbHdfPdTaDnswnJxWjl7\r\nvikFQt3S+nF3n9sLVdbVHkRoB0Omq14VR7aQyfW0tBdF1j6PoPST4s9lMEQU\r\nYNUvQMXytzuM70On3NC6fkyeoj0PI8qBvc6JRewqaAdFTCxt7HXpHiYmGQKE\r\nmSG8tCHIHcqcxYeHxYKrj204TLtSZwrVjY+aVg1xceU0MTcSlIMjix64jws9\r\ngiS0V0qLChQBURbQMaLZsnw7XjjEMi1CZ8pgD/WD1gLwvmAk+h4j3RduIXu7\r\n4n9F8KsFh0gCB4VRYj3ayBDIgu0qgYyHeBw=\r\n=sdtz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.7_1671058035219_0.8779157089252454", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/linux-arm", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2b17c138f32ac00299bfdc9300c88f57e6bf04f9", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-Hn36NbKd6Prh0Ehv1A2ObjfXtN2g81jTpmq1+uRLHrW7CJW+W8GdVgOCVwyeupADUIOOa8bars6IZGcjkwq21w==", "signatures": [{"sig": "MEUCIDnOoE8o8BZplUcqjI1bIMXbQUhaD+hcJDCc6UrA3DsQAiEA9BihEQLn+vyyCwaRSth50rRQGZxP/ODgCaLYm+aPdz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQGbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7IQ/+IkwmM6X9DmCgHyL1Bmz3EWDvB68esECA+anFyz8y/iwgmt77\r\nJDaihXreQLVvC2kXtmqk/olkqtq6AqcBN+I5s1/Je9BWzKiZDDOCl7vavSsq\r\nSl/rjX/+ourbHcFZKyAiHpW+/CsuIhk667lGMxipW7gb4vaAbGj43IGvgzC+\r\nJEd+fL4stD1KBtvkIzrDVuP81IylnJFENlOl6j5+7BK+75/JyJXnqc0/QLeQ\r\nL90Dx9A22XbmcVY1qSTboVx3hMoGt6msCyeQODE86TeSV1gemStNz3ttonvF\r\nkoS9HyhJkmyt6vRhTG2239I6nKw+ZzmDBDmRmkmggGqQDmeKkm5lxwaGCQRm\r\nOlDxrjXTfwgtoo4YbSvVOPD5BRPPclly/IUuOcDkbosYVPMqFt2Af0fcV7Ue\r\n3gbUWItL/XET3vkWoYqML37NalzoN4Da3QpHqiLBF7mkyiddHdpHQPMl4UUl\r\ndEQfTT29ZN3A4fXJuHrg0/Eu6vdsPIxcGU9UhTtkVs34L41VNqSMJyW8KazY\r\nLluYMHXnIzhhg72obuz7dxj+xn8bskiZF+SA/oO58M/4aEK+7rCwT/JP0QsC\r\nN1xGL+vbatkJj5tYJ7UUMK1gmCEENQuYQOeBUNoV1Vqy728wljQlHye3dBPg\r\nSfg/WROqtbEKkr4vj70tL+EMrKISLtdKPOk=\r\n=9bTq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.8_1671233947340_0.9187594315409211", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/linux-arm", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7704de1c2a30bc68d8f615d3ecb1cf68f001256a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-cNx1EF99c2t1Ztn0lk9N+MuwBijGF8mH6nx9GFsB3e0lpUpPkCE/yt5d+7NP9EwJf5uzqdjutgVYoH1SNqzudA==", "signatures": [{"sig": "MEYCIQDmWuKmMHyhxIwY8VkcHMQOvPENoTEM3UNjh09nPSF0DAIhAKbLDrad8wEpBaKo2FhtHiDIBoPV9a3LG4U78/U5YRwW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpevACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+rg//Xw/aCn46SDpbKF10suxnsHF95dOIxawln/qo2Gex5BzOnzDs\r\nUoSbX8qREW0z2dbKxtrlLXls3bMPwb4jpT1okK4OyNS2YfTiEdwYZeyd3kB9\r\nfmOA5c/AX8v+MFbsxL/NUTHn4Wc79rh3pJrW129jHZ5THMzc2eGQ79GXTTop\r\nD0hzrCO+93d+tSuT/klUeYjwhHcDKF6VnL0WsiHVIcl0h53pBoLBnmD+p9mc\r\n02Cqq/pLOp/XMaEBu3jJUOV46PFfXGr0c4OfTteEAu8Iksgns1PX6VSABWnj\r\nj8KCK6DHx4cQXzxubhdmY33ATSyo116xVf0JUTxtADRI5CcsnWTuQLroAkoM\r\nOE53wjifzFS3eZon8XKulR4d5H4ihOuOeMQ943QfGPBnalPfEgvTG4feCiXJ\r\nO7OdDTSg5AW7AD9Tbjp4Sawq/CtZ4xNmTxvwxsVNpSODaEWZOz2MMJ9osn/I\r\nnqbbPRqoG0pNpdHHxhAGnfIq+VdK0rwhRC/T6DCoaR+MSLR/YHY4XTA397H8\r\n3YINZChNtxhb22J37Ufiuw+5UW4MiRlhiN0RaG4GU2KOBYBuOM3GT+9FeQ5v\r\nbe7v5Z8rTzEU8CA3qxoTngXL1ISxjeF+MQiCVAgHGRi5Ln/wiTMOiLLznfzO\r\nscUKeer5392e/80GzBxnvFmdbT46jOUpEnc=\r\n=nRQd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.9_1671337903686_0.19484724833456046", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/linux-arm", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f32cdac1d3319c83ae7f9f31238dd1284ee6bba2", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-c360287ZWI2miBnvIj23bPyVctgzeMT2kQKR+x94pVqIN44h3GF8VMEs1SFPH1UgyDr3yBbx3vowDS1SVhyVhA==", "signatures": [{"sig": "MEUCIQDcMRlOzcLIXNK8dEf/VKWpUkUA35cCKR2Kuk6vHRNzJwIgYq5BNuqJhGp8vFPVoxPpvzS8PPyy0056wHrIHb6kzzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8258000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPM/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR8A/8D1jQKeYJ/JMIenJq6k7isGWeFvoUNihY/PTLTgxWFLFnuwYx\r\nblVj8Ihvnx1BMFIIE3M1zWrSqc7NbXOkaVNL5YwcXjY0xYJeyeq15onCbLV5\r\n57sePscWRw7QgzlqbCTtqOOl8Rf3Yq5RR0rxYRDLkU1xG40t58x58kq72bRD\r\nnSofaFwCBQEXVbJ6Eo5jbdtH0RWFZY/ZPd9V+IOZk0gWsCLup5W7B74SxGPz\r\n8oKr1K/tLrvmumu3GtAzxwdk4DlbNUd679vRpr2zsL8vRMdrU+YvoI84go/O\r\n92dbAQJ4zMQiu7XWKczuTch5YK4sCYzbItVN94Br1FalBdgFZ/tH4+0GCk8l\r\n10mQgvMXEgvf+kKv4MryQ4gv2quyGpdcxwLvmwveHPSx+8ZI7DjCZG5WeQlX\r\nVvWyYaCi4kV4YdUAUV614ApBOFEpvLaxI/nzRjXykgWDOFcs/GD9PB8b4dQA\r\n06SWEJFwhvvt5lRsqGBUutiulKOZsPg53aEWHZca7cNzB1WgV89W0i/LRw5P\r\nyMdFWhn3lsQhjNvVHTZuBsaqYNFvfR9CmJyixGgL0jZqBrmBZqwMaLSpLfWJ\r\n5xl/wIVC70DNzIAg7lV7nxHGLC6p/4ea/LX+qVtVghtzZMw8/B42aQobo+TT\r\n4loqzmkdqh9VOwbq6W8P1E5U6SWpaPa7Z8w=\r\n=GjRQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.10_1671492415247_0.43207382182717335", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/linux-arm", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "aaac921ae77fe71234487aebed20fb095920b31b", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-Mk6TZij71alyS0FGuKEKYjTZGjUw2uXi07V/AiGZW1b5grTfGx6lpsbQdystgDJqju99Osq2Ix+C7WteSnwrHg==", "signatures": [{"sig": "MEQCIHGS2gEf040X+44U3yCQnS9cYyVxLCVrl4x0Q0rU/vWuAiBJejtgpyex3TMgAs65ipsXyjLg1jMDF6ofgQ3b6gAZCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonmw/7BzdnkScUOODgk6X3sL4gcssbG4zK3XqTzsEsHbCsuNZqm5ye\r\nqL8jRKsEDKMEKc1kKehUmbQdyhfKPcxlh9KLYzMs7i9JWLjdLo01pSqk84fX\r\nO7NnQ4kkyzP5PZoDD7stKPF3tb15mQyVGJK0snCCOSaRq9ZAu6PNljHabL4f\r\ncDqR04qTrppibwSmMxTnrP0sT71m0tPGJiW1NoSSGSWZiZu8ZY2Tq+YaYNIB\r\npCd52WIqdgJgsuVI4mkKPuGETMITcoC9IcSUA7ZMdoV71dVVNFHf//XvtVl4\r\nygHIrkx78RMLSTrugG9BSMVjYStDSlp0Gk7PU/lDvEEisMmdjBHH/kNQXWz1\r\nSbkooilt1T9zOyVEp7nQKKRjRfr5CF3H5LZO4CqhP1fbVfocflvAM05THHlq\r\nflK1QG8iQr5WLX6U24kZOrGC8hKKd7TW5fQOmNhhL0GlIYPSRqnxa/bbZJ7s\r\n/KLakkpe9+yk/GwXqvnHg3ksPO+XcoFAFDjOPwtO3laQQLnM/qtPe+l7pvgS\r\nhFEIMYoyIpZMmzu3uAHDeKmv3mD6ynXCW2d3RnEJpOV4oTwMbIKBuzEIYoJQ\r\nimS+y/hYxL2FGLDA6u49ejvqszwP2HgYegmE/BpoNYiTGjghDooOid83Sqm+\r\nLZEST/hejsCo5qv8ZgBCjazPT5Ikz6a1Z1I=\r\n=mdLB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.11_1672105163339_0.43580953865983374", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/linux-arm", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "945ebcd99205fadea5ee22bff624189bd95c0484", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-vhDdIv6z4eL0FJyNVfdr3C/vdd/Wc6h1683GJsFoJzfKb92dU/v88FhWdigg0i6+3TsbSDeWbsPUXb4dif2abg==", "signatures": [{"sig": "MEYCIQDWgoHZYC3klJhconO3e+hjOzSZ5Zv+TBWxG3evHGxIJAIhAMdZ1gPj9DvEnYMwsDCCi7oL9GKWHLnaegTvhKXz59Wv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6UUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpq+g/+IINGIXAphFMO5qkTPXvdPrlQqgDysgrR9TYnPgZ1XmA0X/6U\r\nUz68Q3gcapRBrKVUI0dIH8J7MO3bN+gcSh8EA6difcPWSudWFs9noOWmOJSg\r\nM6gBtNgZfMqQEO3gcYwsAoIZXexIIUV6zhAYVtpWBmMpl3r7pl/6iAkicmFZ\r\nmprO/+EBStS7D09S8Ay7XjT1D6sRO6PPamcLDppK2xrtnexQss+a+mdQqXve\r\nF15ECePnzABIGYPTp7j0Q/ioUvNMzuB7NY/R3CDFO4f7WmY2tt1jgwEthLNu\r\nOfxZV6HyyuFwb5qZGWay6+OBbtHv34QBviTttKBRYf0an/+1GpgxN0vid4HP\r\nOndjNzWiq8yxHXsS0b75nXXvVRyHgrjjGg8qRfX3S44OlalPIhXuXUxb/SHK\r\nDy2LDeqCE2LTtRK7hv2zSLfN/H9tnoLi3cfi+Aeu9xPp3xug2niUW0yMA68X\r\nV09fMjzCNHXQepyx3ttlSUnSsYcKZO8l/6rjdr1nRMCmrd1KFxii6b3cVAir\r\nFnkLbcob2f6I+TeDVoquj7362kyx8euxX80xOLS1Bv5LIIoAitAMVSnSpCVs\r\nJ316M9YmS6sAV40OBWa4zhPrHTTx7FFyej6QAEuzj87t8JEnfl1/Co91Ilh5\r\nrbLIZ+GkTiXSiXihI/xHI9dpTZ288U5uJxM=\r\n=uLjm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.12_1672193300279_0.37836852196007853", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/linux-arm", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ac0c8e9f3db8d418f588ae250e9c66ffdcf3cd82", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-18dLd2L3mda+iFj6sswyBMSh2UwniamD9M4DwPv8VM+9apRFlQ5IGKxBdumnTuOI4NvwwAernmUseWhYQ9k+rg==", "signatures": [{"sig": "MEUCIEHsgtkMJziWAW0lbFPjrP76HyJMM3LfPguQ1Sr+xYCQAiEApwZx+R8DDUCapT9n2krmPYr/R53Nc254VYsOD1QYasM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6AQ/+K8Rdtgo1jEkyzTq9eMw9PSOCWnWFj8Tub8Ty37qyTfmFmO+f\r\nbDGTWUyCd/pH4VIiW9NO1T8GKm6eAYiWghtfS1cC2ySrXQKBS5pcigWLaBYo\r\nQl0oMp9yu5e1bMRx/F7CLy1UD4mfAvHpBnscanmVvg3k2qGGqWiwhwjuhYva\r\nRWxd422b4JPhZfYpdv5R9biKk+ZdIHeghY8FGLbzeoRqgzbQuyNryZlxFfv5\r\n3Hd/hx0gZqZ8MyN9MUbAdmIJS1sBBtJsn12LuSc0y0yN+D5hcDC/YKrd4UQa\r\nf7HAjZjMgP6X3XEuC3cuHUC3UW0QqBrg9GVaJvHVSt0WZ2uD+a7gO4dogmhY\r\n6ChOi7nYYzq+2k/q1yo5KP3riBiUYXMfwZQ8/1nPUkNr95HcdZZHNhAJhFmK\r\nLuiO6OOmSK5cZxx8OX3P27yI0GDF4BDT7zih7JsqSY4Pb6Zdsx09Mi0AsjG0\r\nn1VNlsAllvsZXejV4Z+uXPy5n+jPrPKqUU+G7x3nwMPEp87N0GpxalFW1bxk\r\nsu4XVjHPswf63zSsyqJnoihaggUhDEiyEIJ7Gtqjoh3aRFrZjA4MWfXTWeS/\r\nMx5aXPUoTKBjXKMgPN7asAGUapLNafpHSYxKDeMGA+0HS+qWzvymA35LeiFJ\r\n3BYW6rXlLj1l6jm/GmXZr8CPyRlYnZUJe3o=\r\n=Nsea\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.13_1672700258429_0.06728728594921862", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/linux-arm", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b239eb7e6cb7df9c34c6b08f4adf113da47e0e09", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-X6xULug66ulrr4IzrW7qq+eq9n4MtEyagdWvj4o4cmWr+JXOT47atjpDF9j5M2zHY0UQBmqnHhwl+tXpkpIb2w==", "signatures": [{"sig": "MEQCIAgdF0mOl/MIUarPIZWNIj3uz9ynKLIGXb2kkxBc9dUSAiAaL1b/AQHFrBMnfJA+BoQU8INVGJGPIvvLY1Z8g1T4Pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo+A/+MFLoL1hKyUs/oAQzxkH8+0zbDpxZCsnGhQ5nQ70br0d9wcRV\r\nJhTiIGKABqKSGhtqeTQkfehDtbk9QgW0g5crn4u1RrNLYi9GibGEnJfoCBEG\r\n2/fexn6/N/55O6gqZhbuhU0zA6ZzSbtV0SW+9aZHPn0WXNd6vBk5L8lkR/26\r\nSJb/tnR5uGVOscsC3IOtpjhJqOXXwXee90ts+124GEgQaLDNYlCD4/Ydhrqb\r\nW4wISDLeAC7zXeHuxAumY95V+ihs8gmqP60Nrahv9ZrDuHQ/SvKZkUrx1QsD\r\nr9FCQ/OyX5dvQdLgL1Bu/xReRlyxn2lMxbVsMPytYM+8Zg/n0zBe2cfhwo1D\r\nNHIH38IlW/FXW5LdFpMVppl5Epdm1Nrt0vqN1qM2mVBJ7nP0FGNwxIP97lMj\r\ndOW5xD0F/AvYbrFTLM8GvJfS0FmEmS6mMKVPBAS8NZ2dEURaRVxWew/cvGB3\r\nCoqJeZRWzBmCFxhjD8ZtO+19RKE1kFOmIgirRe7d81dL7giGdrL4hFSIWIJv\r\np2mM6GkIXGiJ5v1knpi5eeUKIfqe1LXKYfVUqnKhO0G/BqFEODeJY9YV+vBH\r\nXx8HpUJgbqM5DzveAbGz5DhdeiJiMZIdFmOGcFlp0GCNGcmUovUZS+CkYFQo\r\nT/OTEF76keQZe+IZdq4fh+UfVrVR1PSRCuA=\r\n=Mrkr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.14_1672863205340_0.4123835635597135", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/linux-arm", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9343c9d0e18d15ca5b4e293154e4beae2598b5db", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-+1sGlqtMJTOnJUXwLUGnDhPaGRKqxT0UONtYacS+EjdDOrSgpQ/1gUXlnze45Z/BogwYaswQM19Gu1YD1T19/w==", "signatures": [{"sig": "MEQCIHh9WTezfnRScT4I9zrT99FNlBwxmgyPPbJ8lGvovXveAiA5qKk954C6rh206zOw3GoFVPG6dV2xU/SovOmjmWAMbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPLJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJeg//aRiXImeb2gLZw3C/W1kZcOkk9PHjk9MoQ0OoxiFzLcHapKfp\r\nndHaTibLEHwuIc91XGFTLp2HoL9a+lDtmdeAaDIcAn7kBpOurq/20kO22YMs\r\nubKULRA2tMmeUdRB32cnUfR+uZVvLt/YQ5207Chc41hC9s4HVN708ZXcDMQP\r\nwBVea64DNelKupm+H0099k+67iRWi7rAFLUlacS2+AzwjXVu/zmvTiyRl4Zz\r\nBLED883JA2ff9+tqDNhyEltWln16OoXERjGViRvBJ8RhSx/mpO5HWkehQOuj\r\nl/3L7zWqypaRvXmFJ/gIAZI5h8vv85vfYnrxBUu4iUf4l9+Ozu8aTq/qNIwo\r\nLS2KAH5RIVpYyz7NJzxdA0Py/xs+H0i3B+XugNdKmGiHNT2xMsnqUpKKvHLl\r\nuse50Cd4BFJmd2UowbeHd0nGHw4mBv4tn5z3fBsmuWP0OG9XjCUyBTJAdCxO\r\nAOmV3levGV/ht2ADmgGRJMY7UWYI/AHTHAMcjyM7gEjIuv5Ge6mgfVBWIxbe\r\nn84YtGf9iXxrqOnIW/TpAMfhhO5TCnCJlgDE5Yv/JMeKsYWLogfRerlb7CfA\r\nNqIyXobzZ3zhxlVHPVsIpEMSIyTZiSt2QOqOg4MaPmMSBhbMRwnaeOXfz4WU\r\nCIEzmdTPSEnHiVJFyBazGLBVqGG7pEZ7t1c=\r\n=UDC3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.15_1673065161641_0.7836427452657708", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/linux-arm", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c1c2e97e67bb7247e6f60e2644de057bfedb8cbb", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-bYaocE1/PTMRmkgSckZ0D0Xn2nox8v2qlk+MVVqm+VECNKDdZvghVZtH41dNtBbwADSvA6qkCHGYeWm9LrNCBw==", "signatures": [{"sig": "MEUCIAOEjC2EnvT2o2S3aiqhWW2IRx/GgBCegzyO4uOI0cIrAiEAsfhTtmqf3OFI3y+928vhbji98U2DrFnU+Ba5MG/1v50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0c6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqWw//cNrgs06VRm4JbIKbrgSQvDf9O8zC7wDGFVYx+FnmyVaoQfxa\r\n0DGBIfZW03kUw1FuAvqtPWM2tlv5PAJ9BpMVOJ2krn4qJORvSeDmZXU1PJx+\r\neRlOmIDkELeoaJXFWBZ28jTSd18edOU1dfnoqQhfB63K3RXoO27kllT13hIV\r\nWu4kVz2Ouh1wNfmzVOpqeA9gQl/tDG1DfYX2dzS2pkHPSWE9aDUXEUXs2g5+\r\nVaqRDWrxwHJ6HzbOFYvVjg3n/CM+DSAASkX5WmM8PVMxiWFd1P0HNuabwiYD\r\n56AbiI2sMrVLik9TMKk3uFrtvzdcLbL+TyLAFvWl+p0ngclxuR+InLCXtyIV\r\nOkGanddEyMyB/h2UdazJVZHWt4I/xf9p9eJAmWN82U0AVmk5EDchY7E2H3P5\r\nVvyH4SJ0+JnsyEuJfxwHcSmjqZFyC3f9u7piU425wxDvPdVWk95xtV0OPm/k\r\nsFYXeUHpHIt0Ug67a7boJMUvKvkUfkm+N+34EkjKbBHMcjo2K4RM2kZ1stHN\r\naPlC6fv8b4jtTqDvxn8bn1d421+LPx1Hpwc/XkFVUEdqzs5CJzjs7YcU/Ioc\r\nGR5GXgRAziuA/0txxriE/0FWOSi0/mXeC81MWPTvBHR501teZ+8EdVuDISO8\r\nNFD5xGf++UOVD6lHWR1UFnRTkr3/hKIH/oY=\r\n=f5W7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.16_1673217849060_0.6302539305250039", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/linux-arm", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/linux-arm@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b591e6a59d9c4fe0eeadd4874b157ab78cf5f196", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-iihzrWbD4gIT7j3caMzKb/RsFFHCwqqbrbH9SqUSRrdXkXaygSZCZg1FybsZz57Ju7N/SHEgPyaR0LZ8Zbe9gQ==", "signatures": [{"sig": "MEYCIQDYLRbAgIYQb+OxtWky44pOueFXvT9Rq+powW5FtnocIwIhALNgmPJnmPF217NwjVobd6FvO3LflHbfWQY/S5AbY+yW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp35w/+M7hicS1uj8Pjr08m/11EFAGuVFFFMVMUcnZWaHXsHI1FLS2w\r\nubxJE4EmornmSU31AtjEiuGpA/F72d5pxE4b22xlN+dhtQlZJqtjFxhxYkG1\r\nPoY2okaPhBEcj1zGPMCaGmjtcf89vNhqYbWm3Bpj1+zQIvp/mOdAOe4ThG0+\r\nNVNb5hqwS+yxA2ycxiixZoHtPj4R9wb+iYsPwINbZt39JMolU2BPf774uAGa\r\nU/zMB/28aqkb0WzWvlA3uIR9mRxeFe4zz1XpmHORvHUJQOGosghYtqU7MEnZ\r\n3t8O32GIPbeSy3YyG4yxKJqssmABCgazOvQUnptu1TCcnpwWOB+65z0juf5X\r\nkWRPo0/ecshhuQnI88+2sQqde7XMo4J6y6GHGDSsO3aWGsF1jujyLdo1mLDm\r\noYnUOmM4rOVeDGrNDa/nVwNJWk5/CKck53iu7p1gIqVM+xj/OIu4riC6cpca\r\nrO4uxKDHzewf//DJgX3jzo9CxqobvIz07+IiVrXDwd5LcJiG7tqEfVzYEer/\r\nvufrJl2nR0OS8kprG1UyK78wOJprfDa4gC+CyNdmZZs6soQNFZpE5FTgmd9B\r\n1sL4p0IgnrUYknRWMLCYu5DK4mOHCznpVvpPHlvduZBKKAsmoKJZtLqvX56t\r\nsEF6f6RcgjLvOtRY1BnhKZ8mJZC04RwlRog=\r\n=KA/L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.16.17_1673474297039_0.856128583045396", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/linux-arm", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5a70a95bf336035884dee123b5453aeab9c608f3", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-Y2G2NU6155gcfNKvrakVmZV5xUAEhXjsN/uKtbKKRnvee0mHUuaT3OdQJDJKjHVGr6B0898pc3slRpI1PqspoQ==", "signatures": [{"sig": "MEUCIQC0tCeLgbJ1Qah0rg+1sHZGdeWhhkRSqrBivX2uu/Q82wIgDcadyd0L5375KFraHUyAm9Th8UOfjqnzyycNP4kxMC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjF0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe3Q/5AThJODQSFFXnHlb0MUYGihSKiWCuT5AUh1+i0H+SN22iH+BX\r\nGRmenIiB/bybXpuUduQ0Jwu2+6UbyS1UaMP0jeCSEUouXNMC0ouzKArbF4fm\r\n2rVh8nd1jGrK6MmDw5M3joUpWZVgIXSGDsYlgo+/VNeR4ND+a7JP8szoo+zH\r\nmExVg/c60HRbxVZIpDk2TDJaQT1obHQ92HlK9JqqmSahx8BbhiV/r02fbaEB\r\nBvEu58HK94q7i/sVPX97GeTFbTGB/Hl77pm3mPRJ+vfYFqA81mq3uDe4lF8l\r\nfwmit2/9G6Kh8pZ3oJ/MQtZHFE/b9ja+KCh8h8ot1zMQHP5MZSSsaVndBRiV\r\ntURJ/IKKXxoljTun6rAjfs1ka4sfWKiNzQSiDe0jZaxw4JMmSw2mol+8CBZo\r\nLZScn1VWiXiIV+8UPIP5MrEgn9Ce8wx4psBjNs4bhJ8D47dHJ0cTt0UB0ADa\r\nxNytQAz2y/FZzUmUvd+/SAw4Ux/PyDXcWlr6gc47cV3kpqXzK12QI3r0KxNb\r\n29Ql5bb+djH31coWBstLux3k6NWDr0HAEX7e4cJuggtzWztPj0p4VqK9zNHo\r\nE4wZ5VzgMFLIokN6jtQ3UJ62hAjzBmvLHxjKnvIQ9zvP/rc8BnxGUoY6LdzB\r\nLBMWPexz7rvXMWsSO2awoNgpYnOFT8Yx+3s=\r\n=pYGz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.0_1673671028072_0.13375090375581689", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/linux-arm", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7249342edfda0431ae17b66de025dcadfdada222", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-TIF51ocfKxPzyxE8llGWjS9fZB3916ZppHeZc14MokEzD4G9C0orDRD/VIVxoyln1bAQdWFJSHJ55JPmF7WhoQ==", "signatures": [{"sig": "MEUCIQD5eI5Sqbg39sfm2Sv0LlKW06HDpBVUYjWj4syrFrzmKwIgUkjO+5nawvat3wjp8ZDHPm93GDlXf5RTNLZUrVeLtas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZIIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDNBAAjX5BHJdG56xgScrlm6yIhgRMmRuhzn9+cF81PK4uPqp55nBD\r\nl62j18uq8ju1Z2c8NwZd2xt6VKMNBQwSU9uHRwdx/aQfDrW/Xn4MqZs+Lv1A\r\nAL4Xf1gH+OtRUsVHPykYUmatKy5/k3wOEqDAdtggfCpW3RxSf5ejff6uEPU4\r\nUPtTJRDJOBWAQs4+Ht7X3KpBlZ4N4DOpH8c6ERVduIh2lEE/kCrYRYIPbef7\r\ndEuErHEX5iIXFwbSk5T6xan1p84h/ghng6QBypVtwRHJZwH0mVOKlcLn59j/\r\nVxOIl+x0VWl4poXDW4Guv2weZabKMz5Gq+Q0fvl4J4LTkYpFDxXpPXof/8yU\r\nTlw8GXvo7F4zDdYs04c982UofpQdZPvQkvy5x3s8nBxpZDAXV4jUkbbmDTH6\r\nv57BatX57D4fDqvzUPqKnX3wYJoMSLCwOiwFxxSYwVjCJ4E+XNKH7HeU0nUP\r\nHRsObviYiTwgFWVjkRd7jBQ0NYQjhteD4aJ6x/rK1R/y11VVWTGREGwNdIpg\r\n9IW0679jAWQcKap7fT60N5yqlhDic+0bl8S3UcRD71l0YF9ezu9CUwTjjoPp\r\nRoyqas1COOieVe6jH1/7BHnjHKcrPoaCtejoUUSY7v0OE2PRjPX5RjSrwscj\r\nSXNAUHQpFU1lFByUy+PDcmyrrpsn9IzWb2M=\r\n=6gAh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.1_1673892360519_0.4093681279898438", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/linux-arm", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5b3f46608b682e32255f6dce10ddcc150826df4d", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-8dfrRTd39n+THdAetwQKNwK6zBPR5oPjMtgRNXvRq8gsn/J5o69zTaOWVi3QO09BljqdShxU2dxDA09lDhdIqQ==", "signatures": [{"sig": "MEYCIQCKEuIR6Xd6wYY1YtXaVfoQwqh3kw/sKJPnDfkYZFM55AIhAN2vVmZ3Z7e5/OohMR6/Pi8ZewohADMuOf2yt7jGbiir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkLBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZyg//UwrWXx9B3zXarYz+b61mMgJZQ0lPOljBjKf3ZTZzoY6CbmHj\r\nvG5Od44+p9RgLODzv0F/0tEOu4rYmsB/BxtHTL/KrlBK2pUqe/J7KGha8RNN\r\nQ/FV2OEJM4oDQEl0QS7oli36hWgQ3HeibBB1kql/v/StO1H1h+Vd5vFaxw1q\r\nCZxQC70lf1Ag4X9vF7zqZwWu5G8qKa2zthBemB4oGdPEfFnqEu255u2ACOor\r\nAQJLRJVHiEhBuMTZfw0oAdw0Ai6pIShlVEg2+jLNAmYeX5Lp2i4jIS20pKUO\r\nlVSf/Kg2AyCKUwKhOyPWYbZxZOfFk75SmfuGl/p5NvZE79PSzvHM/07Roxf1\r\nbd5pkFsUXa8W1eHTrPgj2gBzFLN63murT6X+X5CoH/BOYhYVV0Mx1oxFPGBs\r\nYojX/UBqtslu3ZUBH3d8VDna/eM+X10GloneJ89xiMR0jlR3QEG9w8FED5g7\r\nRCO+TSO9tK7DvZvtyIZ5MBk7AxiS2e72XNrvwbzhw2fbHYTSqgW0Ijrroamk\r\nu7r5EvFo+cD551sxaJvK8AYrburGUjXVFX/XpblPpIbgbpcly43gtJRDJo3K\r\nvqj5RAaymVsLedFvSg5OuoL7CUxsLN7Enf0K9k/OXzxGUrTo5JWjPFWgRk10\r\n/MHki6wZfLE9y4aC5c8S6BVbRrIopC9UfrM=\r\n=FO9A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.2_1673937601066_0.05285637958694611", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/linux-arm", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "15f876d127b244635ddc09eaaa65ae97bc472a63", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-CLP3EgyNuPcg2cshbwkqYy5bbAgK+VhyfMU7oIYyn+x4Y67xb5C5ylxsNUjRmr8BX+MW3YhVNm6Lq6FKtRTWHQ==", "signatures": [{"sig": "MEYCIQCbC0GNyOwmqMxnJ3hIn7oiRBwQKgQ7qAKepY+jeMY5pwIhAIDi/1Nw++dp9aPv/Bf3TJYEPMzQBin988Df+8YQMXXB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoijw/9HB0E2NgqYOqVix7KvbLcW7DXUcjS0Bm1X1Kg6EGJMqOvFhIR\r\n0IIORFwWFPyk1Bf8okQRNTdJW/Ha+Bke/vC1oTec8/rlH7aIVU5UqsiWfITi\r\nfr8lRlB6E3no8PJB3MZrJiT66+J2n1tjMQMkGJAYwdgxXkcw01Hber1abSHe\r\nA9UY0Ki1DBspQWKTw4sdLAB+w23I63aaAMYVhAolLgfTpWaaa7UCyfgJ8KgX\r\n0sJ6UdItleAAy6wNQiP/uuYRzFbU3+9loo50CIMelw78KuYNc1WZBiXkYRj0\r\nNuloDPqx6vYXliddOJFgllM4aIZ0EgO3UpkknKfsSp+tpHecsyha3HRyM7WD\r\nZrqsW24TF+Deq99C1Ek9Yau6bgRDC5ple2x9D3THtIM6guLQcDNFNQ+4vtku\r\nqwTzE8we5pS8t/Mn/Z6RK9PXRS0m3WKXjPmiRnxmKtVn9Wv+pgW/wq57Gruj\r\n+tbM2fjUiYfamFjrxbFwnwGlNs2+gYoOb4zBOvPAGmspKTNIptOPEkJNTfnE\r\n/qqUXzBNp6pnhdMXsIPKfxXF4d6EyaHKW3H/VQstKaVng5Ubkc5jbPHT3Y2G\r\nVQgYXc61aGkEgq7a2DIQfSNJFUWNxpiOvrLzDit9atUpp1yZwb8p8JetMIFP\r\nbc7VSmEOnWqbH/nslR2HXjJLVOcdkE3CJN4=\r\n=lruT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.3_1674069288648_0.8295953051533844", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/linux-arm", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "6eaa41f37e231d113da715a1d9cc820e5523aeb6", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-S2s9xWTGMTa/fG5EyMGDeL0wrWVgOSQcNddJWgu6rG1NCSXJHs76ZP9AsxjB3f2nZow9fWOyApklIgiTGZKhiw==", "signatures": [{"sig": "MEYCIQCC5kM8BSttUI6fEpafcMtJ/G3JmgMoiE6AGN/Q6sbndgIhAMDEehS9EOhmSRMRv0V2l6uFjkm7oFJafYhfAJYgC4zc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8585679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo11xAApH8JWmSclPfPYvsv6cADuFMbZDOtWwi/DU+fb08tgGNzOId6\r\n+gLyinlWEMkvfQ7Err27zAlvFI/cB877ODyVgAXX5o5E5OwgFX3hIh8YN+18\r\neP2GYP6O3MHkfh8bhIKL722tarvs+QOCamDvtNhiv/4LNo/4F/cjgbEqJB4Y\r\n3N6RlWEjJFhFBOAJP5oWs8PK+zKYrBojKMHSlotaenWgYD5uFZN3DR0jlJQO\r\nAioPo173Ymnm1X969tkBLyoOw9RiovH8cfxJQgvrUsvK+ygmJ8jgU1JY0xpx\r\nj4EkNsCTdg0d9cdgarj86vJlTuw1J7v4EP++BVt/KblZQ97BFYNaKZRyk5L6\r\n9OAUhVttdXloBt5qJRXdycm5kb3xh3iXwAWStac1fB10shSw+RWsp1aJBq1n\r\nPF8WZH4AmieK7dk1Fjw3UjcbyrGKrwWFNDlFBXGXDsoZK5XGYhBOnH2ClcKj\r\nvJzzxMDljg0L0GIO+b7kXZSo8Y3jui2cZtd2u/S8xHNc9deS1rn5o/HXMkwY\r\nzDtVisrWlcmstlGWG2VUpatwuQUTjjnmlkEW1xTA4Sd1ckCbEd95x2wVPzUC\r\ncMbn+LbZ6dLhDbvhZLEvXnJi+OJswMNpth9BVgB6TT4idRaBDOR4OpvPZkCB\r\nT3dR/ifoMIZlhBSZYTIVTwOA63ouRPyX2gs=\r\n=EpAw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.4_1674368034343_0.011167169775538222", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/linux-arm", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "86332e6293fd713a54ab299a5e2ed7c60c9e1c07", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-YBdCyQwA3OQupi6W2/WO4FnI+NWFWe79cZEtlbqSESOHEg7a73htBIRiE6uHPQe7Yp5E4aALv+JxkRLGEUL7tw==", "signatures": [{"sig": "MEUCIEaB/YNPHe46gida6v5R/+h9FUQ0Zmnccx7NWj2Ms2B+AiEAvWAqyO26Xlhgx234wTPB5e8SXgJhKehiMQk4bX+Xs/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8585679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHBg/+LC6wz8lTJaF2gmUO2PwdBdtFFftsVeV75YhgFzdcVaZ6tQD2\r\nX+IMJ5GTfaT03niXCVWFOzLOgQwRT71q7s81hYYOyrxSWMDCwPv7+LTLb4Wq\r\noEInib1oQ8xijht4CoOo5TrHG7wzFSruv9Yp55LLijucFExX3CZOEl8UonsW\r\nZRWeqWevCgaj5PRz2Ki7Xw4lmwV7z22C64x/+MbxCa98Pxo21QyjzGCiiubF\r\n7FKaAo8ZfGQpacEUQyN0Kf3e+I05m7MVoC96X6fnekxF4Gff0LMoe9W66EDp\r\no789Cp1PU4I8ChdokbUOB/qEg5q2WsAxXzHmqbJeAn6KNlLG8T0JXT6DUsrU\r\n3Y/TfXVif9U03gQxj/QWsMkaaWgCJiHl5OD9dYsNFjWaIVpXgYASxPQRMLbG\r\nXXZl4O42CQRaPdCARS6ogYWucqbjTAhq4xHSpO8AnQdjxqPxk0iUjjmhyFB0\r\nygWu/o/mj19tTSMgZv8CcIQv8pMoOXr7tIidH8aw7bYncPPvxWpRfPpA04Ia\r\nMPCS92xiawgEwI/7njtREhzrZjb6SdiOgAC3mvyrBBB1PgV5B6B3GrYGE3m/\r\noUL3qqphL3wyDohh1mM6sK3jzYNzBWKDs+Yjx3exPU/c/qVLBUOGwXnEt04f\r\nb9gf/e5FdiKDwW3rjT2JcXNUB3E3/IKCOm4=\r\n=1tf8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.5_1674837487849_0.832454148710055", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/linux-arm", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "097a0ee2be39fed3f37ea0e587052961e3bcc110", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-7YdGiurNt7lqO0Bf/U9/arrPWPqdPqcV6JCZda4LZgEn+PTQ5SMEI4MGR52Bfn3+d6bNEGcWFzlIxiQdS48YUw==", "signatures": [{"sig": "MEUCIQCKVX9eC+DVZ6T9DBMAgKXTgWcrJUfSh6242UNCPqJhyQIgHD/okB1IpNTyawrEBEQUixSv1e8/klKqH2TBzfvehRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9+Q/9HFZDnrURUp++ocXGH957Qw6XeZ+SsPVTj49Mg7cHJ79zqzkF\r\nrtc/Gg2lHxt9eO83/7uGLsm3lBhCXlyaKMwx8bdjbDBkInJF3alrcyeGtm44\r\nBSonHRv7lajFpaE3OjRRD6DqLcQ1INE+Ax3iMCcejwYoPCp6RPprok2mdmVX\r\n4Yr0b/izG83WEE4Iy2zK9u9IMlkGRJ6ZZoWylE/AsyTyyZfwwlgGPU58V/5X\r\noW44q101hc/7f0DWNGSY6/3+W/dYoEAvSxPxlFaznQIu9dMluUK7ZvNG+TWk\r\nVAiHqrw6f8ZXuVgoWoDs07Vqb+1ACqwW91OfQv0khUU60f9EkI/PoK3qyD5M\r\nVMhay+Wcmhpe4Xw+Tmn4WTgMbr+LBsMtub6LWOPeNvm/Z58QYEVJS8Cjjpcp\r\nnDTWPlq4EXYW3bexXvUZzQH5jGse28J1EGRAm1Smn1K4lM3rY739fN7Dy4h5\r\n7lgCxTFGhvUOh7V7YsQ9+9Keb3awKutu/3cz7e4kFlLssua2otrXj6taqE0P\r\nUu0rHMk5Itq7uNJcoy5nZK1XqWqgnrkghwYZmOAPBWkG1NLcfbG8/GVUDxM5\r\n3Yt3I9HansaGa5/sdWO5LgwDnicfpRoReBfdxL+O4yqbka5HGgvnoTg6IoQc\r\nfI2LggpkJZa7kE8taqbnjYyPvyNlZRzRmHo=\r\n=c66N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.6_1675702863371_0.898744649736344", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/linux-arm", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a378301c253ef64d19a112c9ec922680c2fb5a71", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-07RsAAzznWqdfJC+h3L2UVWwnUHepsFw5GmzySnUspHHb7glJ1+47rvlcH0SeUtoVOs8hF4/THgZbtJRyALaJA==", "signatures": [{"sig": "MEYCIQD7UQ6YvAez3ElBfcLE8ndlWWeHgq/JsGngSZvpQbuaqAIhAJMajbZHLkyKTPhrrEvUGDLd4gFTmixShPRZiEXK1hEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XM0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNFg/9Er+OXD0OroasCccm991HgZCEBYjjdq2Mhy3BtSC2epOYLW+Z\r\nV4cvg0+sCrGtOXzsl1jyJBlsVy7vftRe2huGcley/MgAAxy2LgrS+iWF0vm9\r\nhlk3YTz75VPYJP4M0Hj5VaVBBH4BoHrtgKhe5bafc1g1jVVduuGvcM1k5vx1\r\nVpvd9FGrZ5BbyfQgemJ2dYkCZ4f5vhrQTW19OPmXHxxQxlCrCjfHOGnIwT9K\r\noyB6619x96oced2gbLR3srypOcbYxtgt9kSuXaMH2BkZB2jNdzo4aK4olD3J\r\n1M390LKE+a8VP1cJRAhZ721FaGwhVAWx4Bm7QpqTGLJ7eFEinlGOFXGWU7Ma\r\nrJAl6AyUxRmz+389FTF6scqsz57TfPRWNrYT9Ysg8KFLZDGULL5IhBi7Frd+\r\nAjnqmAQSx8KEuNRf239qNnL2psDPUBNosGdcNNALykbo/UQgB8TPepnMfO0m\r\nFfCmR97McpnQ/S0HSfE605Csoaczxuslvo+4pxDpoVxOOyDQ+ZY8Z1fsV7kg\r\n2LztMjv5D4sTVxwA+0xLXA8dhizwUpZGoEI/59EemLP546Lq7VqWLlmnt5MO\r\nGNIdupFuDxvR0Q+hM/j1EnBcIPxcur/zvaGphZ4767qTRH0Oa9emHA/iY+CQ\r\niAUiBocO3ahchoRWx4wazwp1qMJ5UF+a/JI=\r\n=sFL2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.7_1675981619875_0.1216064255027105", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/linux-arm", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "18127072b270bb6321c6d11be20bfd30e0d6ad17", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-6Ij8gfuGszcEwZpi5jQIJCVIACLS8Tz2chnEBfYjlmMzVsfqBP1iGmHQPp7JSnZg5xxK9tjCc+pJ2WtAmPRFVA==", "signatures": [{"sig": "MEQCIBlV0psIIa8ctp1zSC0dFkc2JUmJivpCOByZ6SU8WkFcAiBJP0hz5rh/Z37XraIUGDrD81z3Vzj6GEC+SvlnC7SYvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmph8hAAhn7g8Owirw8WGeeXoVnSYE7uxo3TG0lJsYghDyvTk9Z5jSiT\r\nbYUCaWC9QSvsLpvriVNYKE95VBVasrEUSLqqcfgjZNOa9Ok0lGyoQlJWVQai\r\nQtXYHNp3Pp3Q7ukdO1cSvAEPwC/+49LM02qKjMJg9kytwGKu0q/Q1DL0DhVx\r\nZRBfVmey08BjFdNU8L/BbIJ8JNxR5niy5BdWLzZ/Oo0FQ2KdbCx8BgYCqHFU\r\nbfBiR67fZ7KuN9DOBO+WdjRTkogrf2PckFOZBnag0hF61lPpa4qPeIIgHSqA\r\nx6GQaW3eMX+ewjwRqNvGTHzpeqORmmJyW5D4/QYSiq+GFGuBOcceCCSQSi/Q\r\njGIwlHhJOvDuHHtM0T1Y8qUhajlGYWTJ0pswH0i2UXTddG3zyI0cCBbUgNNX\r\n4EGkn5XEqaq+PD5nbnRKu0p2EbA6kU/ROD53UqRl95KoLjR/u+lnjYHvHjjV\r\nuqtNtJfpa5gk4Tt6PMpB2b8d06lSMpalIfPBiUoe/GBvIDhFm0cRQJqbbdlp\r\npVrKDkd678/pOxUgXFQ742yE2O1unATlAWyebjl5Mwpn9EXhN0isMzhfp1M8\r\ndEFTp1zGFA9AXueqfB/hnQyTLLyI8FEJ9p3ZCYICWcse4DjEj6ex0bVctBVp\r\n/UgkeJpBmN6G6qQ/QOybquI/WCQywITcHMw=\r\n=jqWV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.8_1676270156229_0.8045870840258349", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/linux-arm", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a3a1b074a08dd9ee85b76e0ff2a8dafaf87a884b", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-AhSVW1uIbcXssQ1D+Mn0txGgcxU32ikvIxuqkmjLC7dUpcX0JuwkPgdqTOicuBjG06GV4WvXSHcKCBUjN+oBxA==", "signatures": [{"sig": "MEYCIQDyVEFci7Pwlr3ELcpsffqz6NAqPfkY3y3iN0xFl3jhJgIhAOetdOQ0uItyZk8lGyCNdSAOvkB1wi+QmP6TF3KwCd9R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mBBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrvhw/9EEoYtu8pm0fdkSExYAoZsWWYbTHQf1kookLRdtvC8Hm7dATw\r\ngnjw8Kmfo8w1NQ/K7HOWsPTF9bk2L2Jmo6krBhVrgryS+WCYUnCenO/8IbpZ\r\nPyGJos+wrkjpWBdebfEC5I+fMliM1KIZ9FUWYaRXE9DmKcaeVH6hLuu2E0TK\r\nfyi8oK1LuVfJ5qxzXOHM9kmAYDcgtCZqZrKujaSMOvPS1yrLqUn1LIWS+NU7\r\nBY6XLS5evXPJ79EDtsDP+cnHSq6qBzrZhTugnmP4QfYX/oZAJ8/B2KdEuRLK\r\nAXCkgl3S4npwfGxIMvchoUaW/SM2X9Ks2slGhYZVkDLgKA1ibVJw/KK75EVw\r\nOWBpzm37ovKnvlCBmzh527lqlizDh0I05OlUs+eRSNRB5AyOurmkrRWLyOjN\r\nimJ3tLRI2Hr6OIqFrXx+42+kq76gDRjysPiNy+YXkjuJ23Lfw6Y9qKGrTL0i\r\nfdN1DqT/Qysn+g14UvSwr3rpyao4/0roeHK5Y1o5M5QAPv6PCXpHLHZ5KdWE\r\ntnVkrIzW1jh814tMKUv++EkS5+Y1rF8b/W3g7EqBNw9RXAvpNYecGs4XrV+f\r\niVtwmIHLx6ey78lvEqQ5YGL14JayNGoxnCXd6+PABHht7/Wl0j+S9wgse8Tq\r\nT2ETdkTasDjI5AvstRdm4iI0CXRv+0HLEsM=\r\n=EiN8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.9_1676828737528_0.7723362618038141", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/linux-arm", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "dd11e0a5faa3ea94dc80278a601c3be7b4fdf1da", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-whRdrrl0X+9D6o5f0sTZtDM9s86Xt4wk1bf7ltx6iQqrIIOH+sre1yjpcCdrVXntQPCNw/G+XqsD4HuxeS+2QA==", "signatures": [{"sig": "MEUCIEQyVYWErqejI84KAEIRYDzJ8Z52AkrJ/unZev06cPymAiEAqrYpg0Qlv2k9nkLDWsf2m6s8Oq3NXc93XVWbgCvlf80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87QBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC4BAAhVmYCOR9SQr6K4ahOEjp77VVufFNcOLJ95gChy2EV8d4otVi\r\n0BhayNra4w/185MedybgDHE6ypqWb/ELhdnVfAFLk5vOfCHQxqsObA7xzihx\r\nRVMsyAudZ0q7HjQ0proUqyk+alLojOSOUMWIYrwMqPngMbXwn8prSHtVxhA0\r\nGdkNX5DWFohtFZMyy1Ct/NirhxRdaxttpm5CJvCBYWNMHooss4HhXDCCNAuP\r\nItyUC5hC/wSZWzZcrUqJeOVU0Y3LHC5aGzC/eEPiV03GRyAEK/1JRN2ihppB\r\nEJbcgRwx29/yKk0QN1SidkxJRpjbRQVYNCL+kI7SF+N5ALh/OrUQvDlYQEpg\r\nF9SHtLWogIz2qkK9ON9XAG+Jv8VIHooGyTDwEfH+B7bSNiOW9kLUrQ8zkSRR\r\nt47eFOFoZ4/rXKqG1Xn5yX4WZQfMMQQ74m9qKyiobO5Ov8Zy1gPljkUOwqYu\r\ny7UxhfC9oNiYZf/i60ZsqYmabWPCAQr8jtovZwRGmuvwcAnil7CBe1K9Om5R\r\nq7viPx6uI5sXXVAJN0sifDFCLbYkpfRoYvMStCr+8Csod6yae6XrF5RVMyxC\r\nVbfvCKgDcbedLZ2f+T4T4CT4j4Y1poan2Z6BTjea1IdZJkkVjF2T29HY9AqA\r\nb4J8LmSVrsanFYkIGc67n0DtYdu4dfy2MJI=\r\n=jLA1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.10_1676915713292_0.46643043194882483", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/linux-arm", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e56fb3b76828317a704f4a167c5bd790fe5314e7", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-M9iK/d4lgZH0U5M1R2p2gqhPV/7JPJcRz+8O8GBKVgqndTzydQ7B2XGDbxtbvFkvIs53uXTobOhv+RyaqhUiMg==", "signatures": [{"sig": "MEYCIQCsAhVsCs2YOfR0tt2TXIinDou+KT9zj1h0y38wvfe1cgIhAI8kdrCcpAGVEmVSVoZxj3IC8QReO4bMzZKePUjNrSku", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonJBAAoNIdR9nmC7rfo58gRoWlkXVHB8baVeffMqBcm4wfxe5Zp6QY\r\nj5AYytqPjJMxG+8SHHHCy45ScQjHfAOdRnOakrxqAEquevATZjDYd8J2gjdG\r\nwU5vUAI9PjEaCmQsRm9PFpwi+cT5zBdrHbHotyxVjsPF+jWhUQjsKZi3Krs+\r\n7MSuyGsDQzDVJ5KB6rvxgXx5QXcOXobLxjmo3aAPsLN5GWffQXhRkUA2BCU7\r\n4NxjbefJr0lqvCKapMN5Rsy+T80E6qq2SlIZfJknljk5rXZgzBjErVvr+rFM\r\nHug0m1Y0e0uWVoEotCI/Gv60q/oX+SLZxduuGB7GMqCVvD6LkSm3h1/sc5Tx\r\n+Y2AiWM4eppnI6fDusgK7zYB1eUiYgDLZrYGCFk79w9x1Jlek0wTsvipKzuI\r\nkreON/XIf9IQDeAKZ3XzNUy5/bocsouYH9+fed6W60E4HbsHpX9TsKpFgVpE\r\n3cUmjpexh79L+Emz5f0lIqdMNvAkTA9oGpeuuFSZQzej/PjAf0AGeisVgopv\r\nBJbuM5zV9xuffYYJ0ClT6SjIwNeygdcN+zwQqBBHyltRMkDdIpCVl5K6E6WZ\r\nDh3vcrdw6IJzlYwxFJ/fOEbqJod45Hkpo30U4c1BUw9cW7p01NFZxAqBNWC4\r\nbfm1m/FrtO1UuTL4fGRz4Zrr52yrNevcrZM=\r\n=nPL1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.11_1677883231272_0.12296697421431935", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/linux-arm", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b87c76ebf1fe03e01fd6bb5cfc2f3c5becd5ee93", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-WsHyJ7b7vzHdJ1fv67Yf++2dz3D726oO3QCu8iNYik4fb5YuuReOI9OtA+n7Mk0xyQivNTPbl181s+5oZ38gyA==", "signatures": [{"sig": "MEYCIQD+o+wceuk5tTIefd/qe+A4+An2qRPhsVhvuqXtOK/hQgIhAJLJxTbd8qatbcKBlNyb49PvDpJCbNdvks+NM/j4DGMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAX1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh5w/8CnyvOa2BS+hPmJ28mz4SXSxsLeTR/9Twe8q7mvtd2d34yaHX\r\nefek2JCbGh/xqLP7wr/7NDN9ws/Xt5M6Maltm5g1UL++m1E5Swq+TiEjt6HL\r\nEZikrVB4/TMJcUwYLE3sFIrLlZX3YhhjCNT1NdV6wnI5+LUvbRcAIWopoZ8A\r\n7M1Qi/gGD8XmWecl1IzRL9jeChzdng7Uj9XgdxkmqQm2ksYyFoUEWq+qogA2\r\npkmWZ0KKvcfyNMAVW5mfSQlG1ed7XSrzl3x6xY8UEchbc1XHatzZbZr5Fwfc\r\nplxpBw/5PTOhoqbp/z0HBHu0GEBqzGubQv/FrsVQ7gfGp/gDTP8T8hVMitSD\r\nmv/BpoxsplGgKby8tcJEwtxFkWraRAjVwWZshENyvN8mQLBaHUIcSpWQ4wgo\r\ngbcUCFuqhdVhCKsPOLfk1v78klzQ1hK5pMbL+GLt8Qot0/LD4kgZVK47tNIW\r\nV9jzmQ+hEHQxplpLrInE8wfFwdtDpfitBQShvcXaHacrDORyegqHR64mrMYE\r\nS60g6w08wD7Q1EwiDoiwczaPRUxknBLa//etKwj6rpstYEj0IyNbNg4alTzm\r\n/McPHu2zHd8f6w04ZLiR7F/wZF9maC7WNNeH9I2LlWCa3kl9RcgXrBzPKCGL\r\ntVDSqsIvs3Uj8NVGEomVtew2Lx4dtXg5/XE=\r\n=DaDW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.12_1679033845028_0.77697438966345", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/linux-arm", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3ebbf4764c36f90e58a6342f67844f7628988faf", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-RrhjzrCF6aCDH248nUAQoldnRmN7nHMxv85GOj5AH+qkxxYvcig7fnUmgANngntRu4btXhN9WKHMgQ5seERDMw==", "signatures": [{"sig": "MEQCIDOzU/JP7mib3pQi/sZzsKVfXiTRFit6ehn2i8ppxE4ZAiB+Ug173EFh/nNDhNDNa9VAmsmVLofs7jlakkOHQWubug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMVw//ao0FDJeUiJO/kZS+KxqXszESN7dRQV9OPpICxFTtYEcpszNz\r\nf73k1frmTvX0ITCQS3DZiZ4V3R5CL05gd9+RuPTaXVDaxFv7BOsuP1TusvMM\r\n3UJ3AIyLTUsMud0+i8ASj+I2K9DVhhRMQc8tGB2urUbSOCSazMEM5wVOHQ8J\r\nzZb4ywZa1G1KGUeLgilIxU3TXCZaNBgHpH93my4Fcd4Qzcxn4l7LSZ+BrV+t\r\nO/tLuso7PeVK0PW3h/gEDK0HAOza2ZwC0PbcKbbGtuORcOLmcFAYciyh7ffx\r\nsRROMCGIeN0IG2GH4K9ja7PWJQvSZWQWaRRPfdwi6RKToHjMoVP9/8qfgwCC\r\nTzNvp2GnRO9QH4X/uoGjbeoWI++rNmAFTKorGm20Gr2R+LWFQ/oacMHNc1gO\r\nwdjihZfDpUbFrbuJJjo22eV9S+eVlXplJxiRkkAYP3TgJx1hfhaqQlB2NhZF\r\nzEEZHYoh8Sm6jKa6HRaOR43dyPLqb8kpGn6maM7XlNfI+DNY8tZfe7Rc2zE/\r\nwNkGWFIgIoQJH/WuyqcT5PtYbKe+k79CooeDDFq++WapR8r8Ui1CyVAxjFVE\r\nFssf9TZ8sd1G+9Z/YVOoFwGMWoR1tpE4BbgAcxR36eWwrm0+eGP1QwoT5303\r\n/aF8Y7r9BdnFwrEUU1Sx+zvzSdo+HpxDgtM=\r\n=DOdF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.13_1679684244794_0.8740488111717593", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/linux-arm", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "18d594a49b64e4a3a05022c005cb384a58056a2a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-BNTl+wSJ1omsH8s3TkQmIIIQHwvwJrU9u1ggb9XU2KTVM4TmthRIVyxSp2qxROJHhZuW/r8fht46/QE8hU8Qvg==", "signatures": [{"sig": "MEUCICLdYm5L1Ke3SHQel7txJmOoifPOohTPBAeu6SsBqstJAiEA+6jMh+EQaJjzWf7bec+UpAj11DKPnh8+YG7IrOpT3/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCeRAAiblmKugJXmqmMePILcZoz5P7RWj5wjRk+4vS4yqton+NAxOJ\r\nqosqMRbKrfd+YAs9RLPnJELjCL3YBNm/FnZMMwCeQHZifH3ORjl75GJgGUam\r\nmeqyPdlJhxhh6Zh3UvFo6sEwqL7QFc5WUUVvRcFvc1WSeeihz5FwgvayDMnX\r\nYOxg6+KH80DOXxzoCKjczN1C9dbf+0CnnKEm7b7RgDT45p3oWAWc6ry7LOTL\r\ny8nsNN2xwQ2+4QDKlfCVkOcmYPscG+uThUErvgL2Q86UphRqG7QWRjLUjl6Z\r\n5rmNctn1WYQaMycxUxlOycw6YsfeJ+fRSfABYh6igwsNcRh68fkqvgK1Lbge\r\nbQGx54eO8Jcmij1DGxpdPW7oL3IU8GRPbfUhJG57ykB7sePwIIjFxkN629zp\r\nhNu/xCheCRlFV+GAmGuGkq68lXIWIMPEBQ2wU/4yfZsE+kOT6odOXCY8TaqD\r\nIQPHvtvEjJ4DUs3l7j5KubmxHifolCMsJfsZBFGi1iqjqqL96QRNvJmGRaBx\r\n+XI0/YhfnYGCJ+u5sgc82Qr3pQHna5+plTnYUV2ECuwb95Sz6QS9umrUVboi\r\nqQhxuo4fAgyPclkhEJQMzrMSVjOP0W3mIg6sq7UiYkdvwMgdA9MojiQn3T31\r\n8jbr/KFgeNyJsHpyTCSRaDGUUgQsLdUDAkc=\r\n=jXnn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.14_1679798880109_0.8957393348372191", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/linux-arm", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5b22062c54f48cd92fab9ffd993732a52db70cd3", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-MLTgiXWEMAMr8nmS9Gigx43zPRmEfeBfGCwxFQEMgJ5MC53QKajaclW6XDPjwJvhbebv+RzK05TQjvH3/aM4Xw==", "signatures": [{"sig": "MEUCIQCTrCGkqv6aKQePjDzC19hqR04kHV/a7ukwdsvZgGTrRwIgAQtF2/403bPNRU+Odztr1ex9LzIm56t/WRej+1TeWJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0fQ//fbJKxJgiQ9Vd4AnOlHcFXTrD0N7chEmvtOPhuW+I9qzo/urc\r\nTYrBNNjhiFYjfUENK5dCmW6YVQVVp93+sNLXj7x+7o3lURN8x4Ss2eX73GSa\r\njDlzBOEi11aG9KqdZ/iUqxwYVxfbKUyIhMTe9b9hPVcOE/1Sie/7nPk6W4F4\r\n5IOgNs1iad3zP7t0V9OHtOoVpRx+UBjULydsI8E3VpsN7HzI6gfKGPmwv8Bp\r\nmrzC6ZM4MSeKioWmddGTucHuCmYeF/nX4v7x++DJaT7hr6B/jXikpj+uDRap\r\niPRow9ik+lrtLS217GT6zCbRGNhFAKNVkcIoGQQjRpcetIBGjSHg2jwUCDnq\r\n5YeVfNWmRMrUIKhxoyZ9PBHEgFA8A1V1pKV/51m8+aheeRMVg7q/6w4sQbH1\r\nOrum7iUpB/DhIl7qY37Thg71WVFWQfQUGiPd4IR8MaRNB6M8b7ZHyj3M/cfY\r\nN1g9J2z7HEriF5pRtnbFk2+tFpA0G8p9+9Lk2zw/S5hOBZM8alSprtkWtGZ1\r\nULLRgCiwfuHI5EXYd/BfcuwuqWJ5UwilFxMa0p2FHs+N8VQ6Ta9q3ggbTlRx\r\n96Z5Hu5MUrYMHTVyiNZV9kpN4wFtq9AvHRjNg4iPHzF/nnLM4KeLSDxlighg\r\nF81NNEAkP4bOTJi8LS7TbneZOLhDLJBsN+Q=\r\n=Ur7m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.15_1680388031134_0.33087574052430924", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/linux-arm", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b65c7cd5b0eadd08f91aab66b9dda81b6a4b2a70", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-n4O8oVxbn7nl4+m+ISb0a68/lcJClIbaGAoXwqeubj/D1/oMMuaAXmJVfFlRjJLu/ZvHkxoiFJnmbfp4n8cdSw==", "signatures": [{"sig": "MEQCIEaUK9iP97Q7U8ot9ga5aXDmr9OfK3pK3Jn+hQu+acl2AiBP5kA3eDv8tedFFLGdl0ievmO6Ae5/2QxqcK6jgGoMug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5IHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW8Q/8De0T4awc5dWsrmw+A3pZrKDVY5Th/eVsruN1P/SdLgNU5uMJ\r\n20GDgspA9XJF3cTIbqKd83XOhhupkFOZBFFxrh5bUDusgcBLf5ooii0f6T3m\r\ncX79QvbAt9r1NTm7+KvnosI1wSfZZxSo9NMS9OvSIlORuXibujxXBFxAbpEs\r\nwrs1qbiSoNiq1cMm9JGzMnwV90Fu7TF8Lw6vOw55J+r73nwyv2CjPYMqN3nz\r\nPbQJNBfE+m88Ah+66V/QAHzay2vkXaWyKcagNQ3vvcMELPURbinglMNPTj8H\r\n0M8qW7FSt/VKBxKYTgK9Qv3tpXnVHDjs8g3KszMUNDlpIIVtMUVu0S6+iJXt\r\nwUhyPidFGlPOGE26Faty78s/XxZB87Tax4Eh0dg82gmPzsCAcGVzVELf+Giq\r\nyovhUl38IBu9VEUkay/9aDHet3LrvLrxmHZyYAntfrtqu5npXPAYMob6vBnT\r\nG3amATb0dhyf4R3G5/kk0GrsKcORXLmBfQnt2OiEuuteq2Du8VQ272HMbrik\r\nt741vXZN2A6QBQS0nfftj4EHug1HFNYUeirKC5bfhwu0bgzs7/qVhEYUAHsQ\r\n7HPeVmnzIx2NPixhQKid9VQQmgoCKZOL2PnoW7B/In4PY7HXdtTVm/3BemSm\r\nkC2VQDw74M121cDuKJK0adTGwVknEkS6Tik=\r\n=AY8o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.16_1681101319183_0.7378325981256852", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/linux-arm", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5c8e44c2af056bb2147cf9ad13840220bcb8948b", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-biDs7bjGdOdcmIk6xU426VgdRUpGg39Yz6sT9Xp23aq+IEHDb/u5cbmu/pAANpDB4rZpY/2USPhCA+w9t3roQg==", "signatures": [{"sig": "MEYCIQCumXWtOZ39GjknWJGp8LKXkcZBCAY6sUDsvaHxnsF8mgIhAPbAeJcadbPpfOtY61jrSHh32kEqtEzXvsu1bvHBoX2u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCNQ//ZgaCMYpF1uWUcu8aus61gJt+ONDwRAIIs8LwlMUkLk51Ufgt\r\nfzWeLwFkNB8dIczJ5VMpINDccQnJ/j1TdmBXSX4JNAxTcfDD4PjNSpVTClWE\r\n8/CmVZum1UHr3qHBVsu5vevzmlEL0ukngZwbhZAIgXKHR9jSmmVm2WA6PbVv\r\nV8OMEKj4HkG7UxQrcuqhIRHJszSOHEZirDGnfwEGN/VtBXKM2a4GzlHxtvwH\r\n/jvuw7111Vinut8wgT3kbNtkTtGX1CF5YYvUDUmpbPwEnVOXVPm35bOC8M6g\r\nhYzYsJmITuW+MLiq/PG1KEzuSyg5xgM6QsBTgzNoDfZeUnG7X8ee3HQB2Y+M\r\n9f5fZaLEcKqY5BFMqXQ9BGWqGLv+kdy7Zp/sCavJDXXob7ngrPaYEz2zepgh\r\nSJBpe0otarcWhFCSOEJtuVsIvj4xOvP4EU9AOUwY//R5usftHjmOGg0TncMP\r\nJkjjMRu296KlNajelP1RC4xFyMNNJH/MUxik9hqnu4v1ritTxV+tIyIRXeG2\r\nK39/J15IdSNVlZA2iRgoRKHBF1USd7y5AWWWVJFktWOBapTQbl4K86xmed32\r\nyTBlnVuqOxT6N8OgXaR9BehNp4TZTFS64Wl95fKsjOQLrV7ndJzRDCRQrSEZ\r\nx2UBaYS8bg7rFL8jkP5NL6+tFcIB1mGdqRo=\r\n=I9Zy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.17_1681680234195_0.17957740698721425", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/linux-arm", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e09e76e526df4f665d4d2720d28ff87d15cdf639", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-jW+UCM40LzHcouIaqv3e/oRs0JM76JfhHjCavPxMUti7VAPh8CaGSlS7cmyrdpzSk7A+8f0hiedHqr/LMnfijg==", "signatures": [{"sig": "MEQCIE/DdhzsPY4sWhF843osQcfvwOWelFSOvJEAHA8g7mydAiBqBoFlQn9rbERKv4BQ2DTl5PFtKzxn5qy6HG0BCkzylg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREaHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmogzw/+OeL3O9V/aAjUqg03Xq+3WC6nO/7+3Fbdj6SHgyXv3n1jiMDf\r\n/b8e8Ikl+Om8HbzMz3kO/ofA0al7RpQoACYvpe0odVh5Ypy1IZaZyBvIdl7q\r\nPo0JRdUptNXQ6oKihJp4fmmvw5JpEe9iV4PwhOLuSwb64Fz3S9d+M6wJ1ZyO\r\nNCbANEpnFi0pJVAOxEPj/oUGLmsb4qfFXXgtJKFyndEiMP/Y96eqYB7lN7aq\r\nlwWPrvUSSZEcAmsWHsGKxhkKTaPFpgcAU7FD6bXyku4WlU0YK36zWzhksbVL\r\ntlOctmsmwIrz1NzRg1JuRXTCtEfje12y4DoIZumaPlDdYNm5j7pQrhYeGZSJ\r\n9HiOu5olUw/lA+t/TwMJ3wGy4R2QIsGYi1miKeDQdAMCmQ0n1k/96wz2B9HY\r\nxQQRJb2mnvZeSxkJbVBP6fsnxKMx+pkK936eX/NuMWJ70Yi4OiHQreQ6C3+x\r\n5AegkLOjrf8g79128O5TlALi4nR2ppxtgg448GTLaUc3p7FyzqjB8EUJm0sF\r\nO/AgOq4cDR+f7fvLxAcmnHw6UucjHtGv15LBqDkNXEc06Gpp+8fAGg66N1Lm\r\nWNjnR/hNv29HNeIqGNM3Mt9hvm+s9ZgHUu0MDNn5A8XQhX+1pX4HjnzT7hF2\r\ncf/p6CfUiM+u4Ah1e8VTHAVDWk/5qvQ2V20=\r\n=EQZx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.18_1682196103591_0.27609432548448165", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/linux-arm", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/linux-arm@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1a2cd399c50040184a805174a6d89097d9d1559a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==", "signatures": [{"sig": "MEUCIGc6lT7/pG7jgQ3F0HtlybRyG+Yb34QLDgJK40zN8rdmAiEApqCVP7eTPz8B2HVl1S6PMo0JDomh1X8Xu5qjvfODCHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651216}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.17.19_1683936409929_0.41559252413511416", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/linux-arm", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "56aadc2369324c1a70505d296d5037576a0ad026", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-A3Ue/oZdb43znNpeY71FrAjZF20MtnBKCGb1vXLIVg5qg8rRM1gRgn6X2ixYwATiw5dE04JnP+aV4OBf8c5ZvQ==", "signatures": [{"sig": "MEUCIDrZD60yu8UdS97GNmLVMFr5QDPIgE0WzZY1OL8vBRg5AiEAjTf0IdE1uVs0kQrYG6Hag2MImKcaUX3xK/oQ3E860z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.0_1686345878102_0.4319737239571384", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/linux-arm", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "75d8bdea6f4ed10c2e8186d0aa4f06fe92ea4350", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-d6FXeb8F/cuXtSZuVHQN0Rz3gs3g2Xy/M4KJJRzbKsBx3pwCQuRdSrYxcr7g0PFN8geIOspiLQnUzwONyMA3Bw==", "signatures": [{"sig": "MEYCIQCH8/BpnuYkcm24F7ecJ+sqLqD9nP8KLM0Cz+BO83AtEgIhAJzU/Kvdt6ab8wOkXAJ1xFt28cfLUs03sCJ18ySGmP8d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.1_1686545521388_0.26325989990737453", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/linux-arm", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3ffd593d687338669f05356e98faa40802a3d4ec", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-jAbA75qJ70T5AOdmw9X8675ppeRfj7j57sOypoZ4mQlfQ/LKF8eoeLzTYVo8+aqLKqeIIl0vQ4hKOB0FyG98Zg==", "signatures": [{"sig": "MEQCIF345Y8pjQr56QQvY0NUrhx+LS3hmGaLJt1I6SR0tv9RAiAot/eouNVYzjKrkow111lkt1+1pK3HYgL70bDSntwm1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.2_1686624048975_0.02339027934877924", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/linux-arm", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e3c03cfbedd3a59464dacf31f1dbd91f0e637543", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-fc/T0QHMzvmnlF+kfD6bHLB8u+17gg13260p/E86yYjVoKNFjonL/+Y0GGQjMbFUas9QijqOa7pcR00a9RNkwg==", "signatures": [{"sig": "MEUCIFdA2XMM6s+q3U9xA8Pom9XKp6XLyei5/fr5UwA/neVXAiEAqNiNDAspxaqnM257nrlQeiebAJ9/78WtHnMNfXrFmy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.3_1686831694601_0.6608695214062763", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/linux-arm", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7eb1af7227506dd7cb01ef4a400c53f52ea1c4f9", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-4XCGqM/Ay1LCXUBH59bL4JbSbbTK1K22dWHymWMGaEh2sQCDOUw+OQxozYV/YdBb91leK2NbuSrE2BRamwgaYw==", "signatures": [{"sig": "MEQCIAEjilCBgb9OvULWh1rF4Q/rd/QQSiKTrwyjGaX5IXxIAiBMxBcUj53/+Fe9BvXOrouCJcFYqbox5RiH9QhUSUdUeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.4_1686929933984_0.0711272956945117", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/linux-arm", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7fb71030b17a1e8e5aed2360a52d2c6947703029", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-6R+vEIyfEvp+gOWKSc+m6hdnhWKQYzicqONQYiDGT6qepc6OGsLEZcyFwoz6BvFx5j233CBWMcJ69eXFrwXw9A==", "signatures": [{"sig": "MEYCIQCm1QmzGiZPPkhT3AA4lxfceeKuGuGxeK9LehwUQ793PAIhAKzhb9szRkclXWteOeL1MAtfHTr3r8qlqk6f6JmpOo5X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.5_1687222378309_0.7512464128806617", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/linux-arm", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f98002f9688fd3ac349e484a0de50ba4f7b47aeb", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-C+5kb6rgsGMmvIdUI7v1PPgC98A6BMv233e97aXZ5AE03iMdlILFD/20HlHrOi0x2CzbspXn9HOnlE4/Ijn5Kw==", "signatures": [{"sig": "MEUCIFNTT9/IL6lvr/jbb5iZJAIXEuTq04mplUpSTyO4LmxGAiEArYRUipb466tjRHqd67tJr1dKr23rlyCGmO14xqyl9UY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.6_1687303510138_0.8941011947847213", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/linux-arm", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "41a753d07dbac2204e50f61e3c76a5f3182237f2", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-GpUBqqJnQ+7qdb7NqKNVj7TgD2JnLrEdzPtatPow77Me/EQ01GE1tHKZLePqhf5thdLDb5Se2Kcf4D9WTbSjmw==", "signatures": [{"sig": "MEUCIQCoKLgNGRGOuOrYjl9rObljNeC1azRXHJNNlWxswIgVFwIgP140QXgAI7GiONiQL0I41ONUU3krh43mQl53puLx0xQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716751}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.7_1687574802955_0.9600656704551951", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/linux-arm", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e519c9d5d063b4970c9dffa072586bccc9cbfdd7", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-moCWasFnLWfVmZjux2wE1YRoJlQ36hlthVD/B+UTic3UgCZ5LvpHTeqnF037JL9zS1W6d+cB0hUwithdIyZ/1w==", "signatures": [{"sig": "MEYCIQD4pMHnsIUTgiZCbvXRl4qkgEWT0cz1KbjSujUIBzZ0fwIhAM1cA77dadQb0+F4ccVCuBYo/bv7yLcLubCfBBpGka5w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782287}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.8_1687663170702_0.3096883778146722", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/linux-arm", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4608fb5635e22a764310b2ff0a7bfafd2a29018b", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-YotJBEt9swVrEBRBIXQzI03A4kDQSWk+mbGTTBreIRvWWWTXXqhNYZgqiwnEvtyQi9aqSipEzkRzAGNqs54EXw==", "signatures": [{"sig": "MEUCIQDcpUcwbTURzpFbnGhi0lvMEbnamkANl8Ik/chK1pwSvQIgDZpWdiQzMnml1A4AG8oXyrFj7mOUUPLcHbVcAOZObJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782287}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.9_1687757307135_0.6785267223467026", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/linux-arm", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9a589ffb96837b7536cfa6739dd2e666cc5e548e", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-HfFoxY172tVHPIvJy+FHxzB4l8xU7e5cxmNS11cQ2jt4JWAukn/7LXaPdZid41UyTweqa4P/1zs201gRGCTwHw==", "signatures": [{"sig": "MEYCIQCMV+VQgp1tyZv+u8GD6vaETHDt3YitQl08f6wRmeS01wIhAPEjQuEFU4+j+/2cWe+FpkFG0tO8peXr2Ld7t1yFwfv+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782288}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.10_1687814444393_0.3665069806019734", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/linux-arm", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5175bd61b793b436e4aece6328aa0d9be07751e1", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-Idipz+Taso/toi2ETugShXjQ3S59b6m62KmLHkJlSq/cBejixmIydqrtM2XTvNCywFl3VC7SreSf6NV0i6sRyg==", "signatures": [{"sig": "MEQCIDx09MGA4RXQF7bkaB30mdRKbn5xzA3cUPhjl8YksFFBAiBY8+TpgLaQ/v7uUmHCVN+TdxbkxFqJ27blf4mNTpEmuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782288}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.11_1688191453944_0.7537716149911575", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/linux-arm", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "21688a452c82c1422eb7f7fee80fc649bef40fb8", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-y75OijvrBE/1XRrXq1jtrJfG26eHeMoqLJ2dwQNwviwTuTtHGCojsDO6BJNF8gU+3jTn1KzJEMETytwsFSvc+Q==", "signatures": [{"sig": "MEQCIB0h4SzdcBRc9lV1pDJ/kpZFVr+7YXn3oWNVbMZZlRPTAiATypgtntq4DKlsS/QcpHBpDIe+VgKrM6nz7tBSAJx6Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.12_1689212068883_0.7737997073816187", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/linux-arm", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "47639d73d894026350eaccf7c174f1d26b747d6a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-4iMxLRMCxGyk7lEvkkvrxw4aJeC93YIIrfbBlUJ062kilUUnAiMb81eEkVvCVoh3ON283ans7+OQkuy1uHW+Hw==", "signatures": [{"sig": "MEUCIFK7jWtuqWDeJJqRYCFY9ldl7K1jFa5H+5lHRl/V7ENPAiEA6d9zsjIkr9x/zfPktbwzC3TamP/HsdE6gU4gFlgU13E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782288}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.13_1689388652356_0.929915441388345", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/linux-arm", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7f3490320a4627f4c850a8613385bdf3ffb82285", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-5+7vehI1iqru5WRtJyU2XvTOvTGURw3OZxe3YTdE9muNNIdmKAVmSHpB3Vw2LazJk2ifEdIMt/wTWnVe5V98Kg==", "signatures": [{"sig": "MEMCIB8XV2GAWyt4n2RwsO92KWfE5M0e3k2992uqGtYLFmBaAh8948S1fveOLzXT4p26sv/b3u+q8P7ny5J1ObRpCVNB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.14_1689656437990_0.9478036514852772", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/linux-arm", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "172331fc66bbe89ba96e5e2ad583b2faa132d85c", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-dT4URUv6ir45ZkBqhwZwyFV6cH61k8MttIwhThp2BGiVtagYvCToF+Bggyx2VI57RG4Fbt21f9TmXaYx0DeUJg==", "signatures": [{"sig": "MEUCIQDSLXrIh6JQq50VEwT34jDTsqci4ScbcUdkZar+YdmXGwIgENE80/nbiUh/SVTFnu5E19b9zztjjI3+rLs6I47EsF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.15_1689857614145_0.6029640674794925", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/linux-arm", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d63923c63af534032cc5ea0b2a0b3de10f8357f5", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-b5ABb+5Ha2C9JkeZXV+b+OruR1tJ33ePmv9ZwMeETSEKlmu/WJ45XTTG+l6a2KDsQtJJ66qo/hbSGBtk0XVLHw==", "signatures": [{"sig": "MEUCIQC/nozsFebl+ycueM5CbacFVHBtAIMJ3qejMFOzbillKgIgB7yGjiLPRfa2jzAbs4wF/Jnzf//yEBe9h9lL8iwrT9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.16_1690087701288_0.6644479443769289", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/linux-arm", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9d78cf87a310ae9ed985c3915d5126578665c7b5", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-2d3Lw6wkwgSLC2fIvXKoMNGVaeY8qdN0IC3rfuVxJp89CRfA3e3VqWifGDfuakPmp90+ZirmTfye1n4ncjv2lg==", "signatures": [{"sig": "MEUCIHKZ2Ix2p7hbuuWuN++ypcX7HpLL5o1NGzp0t5PeLKstAiEA75piSi+f4OacGfzUGA+oMIRnG+94xHNMHlbPcfSQYoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.17_1690335671747_0.8326733093307868", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/linux-arm", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "863236dc47df2269f860001ca5c5ff50931e9933", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-Kck3jxPLQU4VeAGwe8Q4NU+IWIx+suULYOFUI9T0C2J1+UQlOHJ08ITN+MaJJ+2youzJOmKmcphH/t3SJxQ1Tw==", "signatures": [{"sig": "MEUCIQDLGLACOgP4L1u6uLLw3A4gTOCwh0JzcJWDxsT3B7QnzwIgf9+TZ1YOXBf+03Ujacut6UZ8iMIkVYbxOKpAz8fZ28k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.18_1691255203683_0.6564075057638195", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/linux-arm", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "39ea874c8e5177b83903bec1883a43f3c163627a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-qtWyoQskfJlb9MD45mvzCEKeO4uCnDZ7lPFeNqbfaaJHqBiH9qA5Vu2EuckqYZuFMJWy1l4dxTf9NOulCVfUjg==", "signatures": [{"sig": "MEQCIDM0sV0xEegWEO9ttMotxnlWH2GFm4Zvtv/4jhLfFwheAiAZQeux/yX5xPkaDZaC9euMc1MJqV+hgXcp0sKMSTjX0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.19_1691376698084_0.6681228899074207", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/linux-arm", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/linux-arm@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3e617c61f33508a27150ee417543c8ab5acc73b0", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==", "signatures": [{"sig": "MEUCIQDbyt0U5ReN+d55GjCicCUvJQq8fsvsd32oTKAdjsG7aQIgOPKmhUzYJoY3gCFlow/BRtlHrmM+n/N6hIoVSGT2ACA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847824}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.18.20_1691468120029_0.5944821305869241", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/linux-arm", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0b0f79dc72884f0ad02c0aabfc969a0bee7f6775", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-2F1+lH7ZBcCcgxiSs8EXQV0PPJJdTNiNcXxDb61vzxTRJJkXX1I/ye9mAhfHyScXzHaEibEXg1Jq9SW586zz7w==", "signatures": [{"sig": "MEQCIGZ7KpattZqADjf809LTGXGJ5GnlE8KqCpHluZ9q63REAiBSuXfQ6nuCobtBzWk/Zj/V5znvMXD3wNr/QhpVryNcWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.0_1691509969781_0.49710982374182877", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/linux-arm", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "16ab51f1928c5631cbd40b86997198691201fe4c", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-lCWDVPpQO/Dt5MEqctKujgtUVmwQx7J2Q83EqX/9BejN7BIX4fGJ0QKMiIyy21PFh+/64ArN+Ovh1tzYkTt2wg==", "signatures": [{"sig": "MEUCIFTs3bNIvRQ4OIkPlUShgepUlDF89+sD3PMnUQI0WAsxAiEAjQZC5g1ku8lPKLpMp/GazfnxeIWeDMDKxrBGZT36eOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.1_1691769467907_0.6725469285772296", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/linux-arm", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c32ae97bc0246664a1cfbdb4a98e7b006d7db8ae", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-Odalh8hICg7SOD7XCj0YLpYCEc+6mkoq63UnExDCiRA2wXEmGlK5JVrW50vZR9Qz4qkvqnHcpH+OFEggO3PgTg==", "signatures": [{"sig": "MEUCIQDk62U5XnruGnOlzx9BXAtiMTTGaCQlXGzVds94UMMIRQIgZbLNkLjd+8vs0pLmaCdR1PfEzVkkEDEBLE5slGAby4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.2_1691978314437_0.006427061244155707", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/linux-arm", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ff6a2f68d4fc3ab46f614bca667a1a81ed6eea26", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-zr48Cg/8zkzZCzDHNxXO/89bf9e+r4HtzNUPoz4GmgAkF1gFAFmfgOdCbR8zMbzFDGb1FqBBhdXUpcTQRYS1cQ==", "signatures": [{"sig": "MEYCIQDk/L92vTHpFV791K31ZXE+NVtUScaUPR99uAesgI0krgIhAOAXAaEVSSXE7A0z01+l+w3TL+8R58ySaWTjNS+8Bc5t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.3_1694653960673_0.3439537867030118", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/linux-arm", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d5b13a7ec1f1c655ce05c8d319b3950797baee55", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-z/4ArqOo9EImzTi4b6Vq+pthLnepFzJ92BnofU1jgNlcVb+UqynVFdoXMCFreTK7FdhqAzH0vmdwW5373Hm9pg==", "signatures": [{"sig": "MEQCIBEn8UY5uYcbch7LTtZ/vOgFyxaW6pibzia0kOMbwnA4AiAbSvPHhVJvzVo0T/i9LQ3f/aeU3JM2yW/qkEVMX7Rvag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.4_1695865672938_0.5599538468857683", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/linux-arm", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0acef93aa3e0579e46d33b666627bddb06636664", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-lrWXLY/vJBzCPC51QN0HM71uWgIEpGSjSZZADQhq7DKhPcI6NH1IdzjfHkDQws2oNpJKpR13kv7/pFHBbDQDwQ==", "signatures": [{"sig": "MEYCIQDSdQY0mRoKf01he9BGkJnRIHD+RNasQsNR9ttXuk13/gIhAKSl71T8EYCwsX3FW8N8Byr+U4hQtR4Yvu7PZdSUu4BL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.5_1697519450042_0.3914300726006299", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/linux-arm", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "50a537de609315979509120b0181882978294db1", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-G8IR5zFgpXad/Zp7gr7ZyTKyqZuThU6z1JjmRyN1vSF8j0bOlGzUwFSMTbctLAdd7QHpeyu0cRiuKrqK1ZTwvQ==", "signatures": [{"sig": "MEUCIQClTq08EtOU7XJmJ/bebrSV7612u9l+qWFm9DqxZKf1kQIgK2RysiCOErr+1EnSwvAOSV8ExogX86jExOpbCnKltyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.6_1700377911365_0.6724614170691845", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/linux-arm", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "12d5b65e089029ee1fe4c591b60969c9b1a85355", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-Y+SCmWxsJOdQtjcBxoacn/pGW9HDZpwsoof0ttL+2vGcHokFlfqV666JpfLCSP2xLxFpF1lj7T3Ox3sr95YXww==", "signatures": [{"sig": "MEUCIQCMxRcjByCqcOKtfZkKIeCVA7UGYkBAh5pR0da6jtpKDwIgM/NgiC+UR+G/xjIjlnMcURkbCaMkjvBZvPJMQAyVJ/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9044431}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.7_1700528486551_0.4109364110621203", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/linux-arm", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d6014d8b98b5cbc96b95dad3d14d75bb364fdc0f", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-H4vmI5PYqSvosPaTJuEppU9oz1dq2A7Mr2vyg5TF9Ga+3+MGgBdGzcyBP7qK9MrwFQZlvNyJrvz6GuCaj3OukQ==", "signatures": [{"sig": "MEUCIDCoteyVjiv8h3Ia98hsGD+1DVVdwyHB6nz1l0/rRMrbAiEAq7WFYsmPkVqwfhqpyC88Xdj3J0+wZdxhm2GVBLEBqP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978895}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.8_1701040101778_0.3333139889067218", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/linux-arm", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9807e92cfd335f46326394805ad488e646e506f2", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-C/ChPohUYoyUaqn1h17m/6yt6OB14hbXvT8EgM1ZWaiiTYz7nWZR0SYmMnB5BzQA4GXl3BgBO1l8MYqL/He3qw==", "signatures": [{"sig": "MEYCIQChvjZATuFkUKXlx8tu3dzsWne9yxGKUDzNanGtJP89hwIhAI2WW+8agi4bTiW+TRIFhiy4hr9pT4k8goF1nQ64Mqkq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175503}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.9_1702184982290_0.6123956534980937", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/linux-arm", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "477b8e7c7bcd34369717b04dd9ee6972c84f4029", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-j6gUW5aAaPgD416Hk9FHxn27On28H4eVI9rJ4az7oCGTFW48+LcgNDBN+9f8rKZz7EEowo889CPKyeaD0iw9Kg==", "signatures": [{"sig": "MEYCIQDcB3RJ3sgvKorwPyWR3qsGJGKuGa+2SEHfrNiItDsqnQIhAIVCd+y516tBhy10Tz2QXP/EQw3u5FXtVrgMTNw9bMOF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175504}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.10_1702945315288_0.2568298673371785", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/linux-arm", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ce82246d873b5534d34de1e5c1b33026f35e60e3", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-3CRkr9+vCV2XJbjwgzjPtO8T0SZUmRZla+UL1jw+XqHZPkPgZiyWvbDvl9rqAN8Zl7qJF0O/9ycMtjU67HN9/Q==", "signatures": [{"sig": "MEYCIQCwa9w/Fxub63KtnpkdldaUkP0uTmggRgctyZy2Qy+tCgIhALJgHli7FDgYHyLLvwPPvqman61HUJBvSbwLYwX/Qpf+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175504}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.11_1703881945692_0.11591650766033434", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/linux-arm", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/linux-arm@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "207ecd982a8db95f7b5279207d0ff2331acf5eef", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-J5jPms//KhSNv+LO1S1TX1UWp1ucM6N6XuL6ITdKWElCu8wXP72l9MM0zDTzzeikVyqFE6U8YAV9/tFyj0ti+w==", "signatures": [{"sig": "MEQCIHswaKY4M8nPQUxUkl9bZRZvERRqF4Mstrm56YlcacusAiBWZl5KuX0MLkuLJuGlLv5tlc3Bl1Wr3vrGCXTgAkmSJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175504}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.19.12_1706031645947_0.02529136637908236", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/linux-arm", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "6b586a488e02e9b073a75a957f2952b3b6e87b4c", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-2ezuhdiZw8vuHf1HKSf4TIk80naTbP9At7sOqZmdVwvvMyuoDiZB49YZKLsLOfKIr77+I40dWpHVeY5JHpIEIg==", "signatures": [{"sig": "MEQCIDoWsgwRiws5wynJcHoUE+q+eDcxRVeGqSiJjiMhzJmCAiAy0grBngzDMVDlDqevSPtRXAxahLMhEf2VlS/f1KZTsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175547}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.20.0_1706374190103_0.3683139085957414", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/linux-arm", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4503ca7001a8ee99589c072801ce9d7540717a21", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-LojC28v3+IhIbfQ+Vu4Ut5n3wKcgTu6POKIHN9Wpt0HnfgUGlBuyDDQR4jWZUZFyYLiz4RBBBmfU6sNfn6RhLw==", "signatures": [{"sig": "MEYCIQD9ofouOZAJ3+XwPKX+yVG9k/L/4xLNVGCZ4nrhKAmOMwIhAJZD/lJZc1wFsOiMCOZh9D/RAiSnUKcZJzWwIIfoFmqr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175547}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.20.1_1708324729222_0.19735196888711615", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/linux-arm", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "76b3b98cb1f87936fbc37f073efabad49dcd889c", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-VhLPeR8HTMPccbuWWcEUD1Az68TqaTYyj6nfE4QByZIQEQVWBB8vup8PpR7y1QHL3CpcF6xd5WVBU/+SBEvGTg==", "signatures": [{"sig": "MEUCIQCfjom1VBbnUZySjL76MpKNDLUX7hX6SMOTtv+lXXnHFAIgUXM7GmBp7jBiWCHJ/zR0nTCpEgbPSKzgMCqPuoSJhFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175547}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.20.2_1710445797981_0.33606282352189143", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/linux-arm", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2f3924977d19e2099761942be0c9f5a1636a430b", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-8s/YeLaUV3QTaGzwDqiTpb78Nw/DdIaUdIlRZItGgWf/8UZHsYUIWj9RfsEXVJB5qvtrg835Dgz/gf+GmFGa7w==", "signatures": [{"sig": "MEQCIALIaen+TI2ndHZy5L8kOjJq8pKX50FSJhZ9x4W7B7jnAiBg8B+HHEUpEp3yHvcqjgvlN+RBb1spKXboEMnnrq5S4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.0_1715050361887_0.22197746459056678", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/linux-arm", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d6f7c5873479dd97148bef3e3a7f09d486642883", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-tRHnxWJnvNnDpNVnsyDhr1DIQZUfCXlHSCDohbXFqmg9W4kKR7g8LmA3kzcwbuxbRMKeit8ladnCabU5f2traA==", "signatures": [{"sig": "MEQCIH9cFnwgH+v8bXJQb7MINWiDDsON0Oq8PRDHsnksPXuGAiAahJX138Idrv4B8ArTN/8KcqS3wuCifKBIoTwndurKeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.1_1715100922767_0.20959563760969036", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/linux-arm", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b1c5176479397b34c36334218063e223b4e588dd", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-nnGXjOAv+7cM3LYRx4tJsYdgy8dGDGkAzF06oIDGppWbUkUKN9SmgQA8H0KukpU0Pjrj9XmgbWqMVSX/U7eeTA==", "signatures": [{"sig": "MEUCIQDrUMVV3e2iyAr06vkjczQgZ+aICuz9UX10Qoznfca/LwIgLgVsceelGwDMblkw36JmGZNrtnQjaU/UDzNlmQaqMmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.2_1715545988129_0.2154378777092425", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/linux-arm", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "872a476ca18a962a98700024c447a79279db1d45", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-f6kz2QpSuyHHg01cDawj0vkyMwuIvN62UAguQfnNVzbge2uWLhA7TCXOn83DT0ZvyJmBI943MItgTovUob36SQ==", "signatures": [{"sig": "MEUCIQD7cwpOqRsDs94DXmyAR5Fz6JqqLzzQZDUQ6M158JLiIwIgdrij/oKowftJEk5J9YqRwjoLUwZDmyeO3Z65QVc1fII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.3_1715806366557_0.13678484884718567", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/linux-arm", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1144b5654764960dd97d90ddf0893a9afc63ad91", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-2rqFFefpYmpMs+FWjkzSgXg5vViocqpq5a1PSRgT0AvSgxoXmGF17qfGAzKedg6wAwyM7UltrKVo9kxaJLMF/g==", "signatures": [{"sig": "MEUCIQD0Qaipc3qvRND8I+C5fUqzTaklGjCF2TV+cJzjmpNregIgYI87XSTBOvLbfGINjxQMSWQ+oEIArZdQkJGC3p2ya8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.4_1716603062495_0.8005546551790457", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/linux-arm", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/linux-arm@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-bPb5<PERSON><PERSON>ZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==", "signatures": [{"sig": "MEQCIGo7ERM4LkjrySSXEVvlqdB0q8qnpxtWydbBL66l9cFRAiBmE3Qjiwtwpm70nMxgpJpup3UNfZqpypt5cP/NgV39YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306619}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.21.5_1717967832795_0.7908159969943724", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/linux-arm", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "6587d3e423e09766ea997229827e292e7c4acd6f", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-KEMWiA9aGuPUD4BH5yjlhElLgaRXe+Eri6gKBoDazoPBTo1BXc/e6IW5FcJO9DoL19FBeCxgONyh95hLDNepIg==", "signatures": [{"sig": "MEYCIQDoZ7SSBrYq0oPDaJ1zbQQUk9Wfeeg0kdgOc829boKZmQIhAM0rkpyEkhC+7kwp09POybt07PelbanFjQ9kiSC8m/ua", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503379}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.22.0_1719779881356_0.507520266420705", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/linux-arm", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "237a8548e3da2c48cd79ae339a588f03d1889aad", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-SEELSTEtOFu5LPykzA395Mc+54RMg1EUgXP+iw2SJ72+ooMwVsgfuwXo5Fn0wXNgWZsTVHwY2cg4Vi/bOD88qw==", "signatures": [{"sig": "MEUCIQC3LaJiZmhy3N/ZdyQ7+a5zZPw6oLgbn8xgs2dQ/PEwZQIgUfKDG1swa2bk3gNoOjMkUkMTu+B33eeBpty0N6PFyc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503379}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.23.0_1719891239722_0.9066855072690527", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/linux-arm", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ecaabd1c23b701070484990db9a82f382f99e771", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-CXXkzgn+dXAPs3WBwE+Kvnrf4WECwBdfjfeYHpMeVxWE0EceB6vhWGShs6wi0IYEqMSIzdOF1XjQ/Mkm5d7ZdQ==", "signatures": [{"sig": "MEUCIQDbGRLTNJwkgPvUR3U5cNwyiG6q4cgKJclfrRUSITLy4wIgMfCBrjLg7EJhpNkNC6C4FhNlrRi40+uuU0kY6WM7mXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503379}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.23.1_1723846412188_0.5510489478481222", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/linux-arm", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "8e4915df8ea3e12b690a057e77a47b1d5935ef6d", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-gJKIi2IjRo5G6Glxb8d3DzYXlxdEj2NlkixPsqePSZMhLudqPhtZ4BUrpIuTjJYXxvF9njql+vRjB2oaC9XpBw==", "signatures": [{"sig": "MEUCIA0vNc8ipQWLyYfW9kby5B4jdeoaw4RXy7Pq/GkJSwO3AiEAkVbsgo293fVo+op4zF/EIx+3ZZvWbK2BhpSjGUS+LE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634451}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.24.0_1726970800318_0.42587850990042275", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/linux-arm", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d69a209b1549a95db0770a6e5f5631c61572803d", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-USovmgDDpiWs16nRCH/NmRfQUJEaGGDPHqK6+pGzuMZOfoe0MAciJRMu1AKP3Ky4gnpuQcXv7aPHpX0IwLWRhA==", "signatures": [{"sig": "MEYCIQC+t4XNGCZASCkSes9knsedFywqa4KnsljE1JQtB6/DzAIhAPwW/2gphkpAdoWI1QiihezrhwHPBtVq73JxOliACyLx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634451}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.24.1_1734673293497_0.5224488657192556", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/linux-arm", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c846b4694dc5a75d1444f52257ccc5659021b736", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-n0WRM/gWIdU29J57hJyUdIsk0WarGd6To0s+Y+LwvlC55wt+GT/OgkwoXCXvIue1i1sSNWblHEig00GBWiJgfA==", "signatures": [{"sig": "MEQCIHSqcyeBtXeNW3UPHg+p0vEuc3Ed+eYtwKX71UCjrrerAiA/5Ujhbrm3WLZpdg/erJ8EU45fXSUjJPCgcKYi4sD1Ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9634451}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.24.2_1734717410350_0.0888332816376316", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/linux-arm", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/linux-arm@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "cc49305b3c6da317c900688995a4050e6cc91ca3", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-vkB3IYj2IDo3g9xX7HqhPYxVkNQe8qTK55fraQyTzTX/fxaDtXiEnavv9geOsonh2Fd2RMB+i5cbhu2zMNWJwg==", "signatures": [{"sig": "MEYCIQDThcf7oznRz3ycz6qAdbO4S4gl7zX03k5A9H8nuhg/pgIhAKSp/WSzP2ky4R0qteIUfGa8omTMfLeicuYsKXojPBCl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9699987}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.25.0_1738983759010_0.908241676735871", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/linux-arm", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/linux-arm@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "dfcefcbac60a20918b19569b4b657844d39db35a", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-NdKOhS4u7JhDKw9G3cY6sWqFcnLITn6SqivVArbzIaf3cemShqfLGHYMx8Xlm/lBit3/5d7kXvriTUGa5YViuQ==", "signatures": [{"sig": "MEUCIQDzwMJs18ukQ7z2P64AxEULckkypmHwQZ/e2y75Mavg5QIgdi6k1nKWWZ5yQgFMMLWp4pEw/mHVPYRT5kFTha4C4FY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9699987}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.25.1_1741578349970_0.49875013414318214", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/linux-arm", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/linux-arm@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "72a285b0fe64496e191fcad222185d7bf9f816f6", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-UHBRgJcmjJv5oeQF8EpTRZs/1knq6loLxTsjc3nxO9eXAPDLcWW55flrMVc97qFPbmZP31ta1AZVUKQzKTzb0g==", "signatures": [{"sig": "MEYCIQDl0F4O2dXmCVxlc9Ps2FxQQfUqZSijFqdFGGbAINHWlgIhANeNLS7g1boHJGAinegBtHSUk0rkbXr5+rTIk3Z3Awji", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9699987}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.25.2_1743355993454_0.8451398300550959", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/linux-arm", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/linux-arm@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e91cafa95e4474b3ae3d54da12e006b782e57225", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-dUOVmAUzuHy2ZOKIHIKHCm58HKzFqd+puLaS424h6I85GlSDRZIA5ycBixb3mFgM0Jdh+ZOSB6KptX30DD8YOQ==", "signatures": [{"sig": "MEQCIAHmmSnyOYuAF6vD93T+8XEHUs8KBK3OZi3B2kMrjR+ZAiAacHa98ACCQ4HuMrQaHTMa0HkflBAwmR9uyyKDB48Cfg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9699987}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.25.3_1745380588196_0.0034965239037039186", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/linux-arm", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/linux-arm@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9b93c3e54ac49a2ede6f906e705d5d906f6db9e8", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-kro4c0P85GMfFYqW4TWOpvmF8rFShbWGnrLqlzp4X1TNWjRY3JMYUfDCtOxPKOIY8B0WC8HN51hGP4I4hz4AaQ==", "signatures": [{"sig": "MEQCIHr03BdIHGdvFpVpkc5VEO1RP9SDLB+W8xCtoXll5wQzAiB0775hp+5D8FuqFHpi/LxWMMT7parOgWlrzUfpV9WaQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9699987}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/linux-arm_0.25.4_1746491463207_0.3075009362287251", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/linux-arm", "version": "0.25.5", "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["linux"], "cpu": ["arm"], "_id": "@esbuild/linux-arm@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==", "shasum": "2a0be71b6cd8201fa559aea45598dffabc05d911", "tarball": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9765523, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCD02j8BOK1DXB1he8Y+jfYmxO9ztzi3aEirVHwJlpbEQIhAMFMxf51Zhq6jSlPQKoGEYf8bu5qANwTUmfRcTRgQ8IB"}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/linux-arm_0.25.5_1748315590244_0.5891292797141101"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:45:22.028Z", "modified": "2025-05-27T03:13:10.714Z", "0.15.18": "2022-12-05T23:45:22.384Z", "0.16.0": "2022-12-07T03:55:20.006Z", "0.16.1": "2022-12-07T04:48:48.685Z", "0.16.2": "2022-12-08T07:00:09.218Z", "0.16.3": "2022-12-08T20:13:25.246Z", "0.16.4": "2022-12-10T03:50:53.015Z", "0.16.5": "2022-12-13T17:47:58.898Z", "0.16.6": "2022-12-14T05:23:31.675Z", "0.16.7": "2022-12-14T22:47:15.549Z", "0.16.8": "2022-12-16T23:39:07.600Z", "0.16.9": "2022-12-18T04:31:43.955Z", "0.16.10": "2022-12-19T23:26:55.463Z", "0.16.11": "2022-12-27T01:39:23.671Z", "0.16.12": "2022-12-28T02:08:20.593Z", "0.16.13": "2023-01-02T22:57:38.687Z", "0.16.14": "2023-01-04T20:13:25.596Z", "0.16.15": "2023-01-07T04:19:21.929Z", "0.16.16": "2023-01-08T22:44:10.299Z", "0.16.17": "2023-01-11T21:58:17.291Z", "0.17.0": "2023-01-14T04:37:08.342Z", "0.17.1": "2023-01-16T18:06:00.790Z", "0.17.2": "2023-01-17T06:40:01.336Z", "0.17.3": "2023-01-18T19:14:48.913Z", "0.17.4": "2023-01-22T06:13:54.627Z", "0.17.5": "2023-01-27T16:38:08.130Z", "0.17.6": "2023-02-06T17:01:03.673Z", "0.17.7": "2023-02-09T22:27:00.142Z", "0.17.8": "2023-02-13T06:35:56.499Z", "0.17.9": "2023-02-19T17:45:37.774Z", "0.17.10": "2023-02-20T17:55:13.499Z", "0.17.11": "2023-03-03T22:40:31.506Z", "0.17.12": "2023-03-17T06:17:25.339Z", "0.17.13": "2023-03-24T18:57:25.108Z", "0.17.14": "2023-03-26T02:48:00.313Z", "0.17.15": "2023-04-01T22:27:11.416Z", "0.17.16": "2023-04-10T04:35:19.446Z", "0.17.17": "2023-04-16T21:23:54.436Z", "0.17.18": "2023-04-22T20:41:43.822Z", "0.17.19": "2023-05-13T00:06:50.306Z", "0.18.0": "2023-06-09T21:24:38.365Z", "0.18.1": "2023-06-12T04:52:01.684Z", "0.18.2": "2023-06-13T02:40:49.259Z", "0.18.3": "2023-06-15T12:21:34.901Z", "0.18.4": "2023-06-16T15:38:54.234Z", "0.18.5": "2023-06-20T00:52:58.596Z", "0.18.6": "2023-06-20T23:25:10.399Z", "0.18.7": "2023-06-24T02:46:43.321Z", "0.18.8": "2023-06-25T03:19:31.147Z", "0.18.9": "2023-06-26T05:28:27.396Z", "0.18.10": "2023-06-26T21:20:44.743Z", "0.18.11": "2023-07-01T06:04:14.368Z", "0.18.12": "2023-07-13T01:34:29.263Z", "0.18.13": "2023-07-15T02:37:32.689Z", "0.18.14": "2023-07-18T05:00:38.248Z", "0.18.15": "2023-07-20T12:53:34.401Z", "0.18.16": "2023-07-23T04:48:21.592Z", "0.18.17": "2023-07-26T01:41:12.066Z", "0.18.18": "2023-08-05T17:06:43.980Z", "0.18.19": "2023-08-07T02:51:38.324Z", "0.18.20": "2023-08-08T04:15:20.281Z", "0.19.0": "2023-08-08T15:52:50.059Z", "0.19.1": "2023-08-11T15:57:48.158Z", "0.19.2": "2023-08-14T01:58:34.700Z", "0.19.3": "2023-09-14T01:12:40.907Z", "0.19.4": "2023-09-28T01:47:53.279Z", "0.19.5": "2023-10-17T05:10:50.439Z", "0.19.6": "2023-11-19T07:11:51.644Z", "0.19.7": "2023-11-21T01:01:26.846Z", "0.19.8": "2023-11-26T23:08:22.036Z", "0.19.9": "2023-12-10T05:09:42.628Z", "0.19.10": "2023-12-19T00:21:55.603Z", "0.19.11": "2023-12-29T20:32:25.962Z", "0.19.12": "2024-01-23T17:40:46.181Z", "0.20.0": "2024-01-27T16:49:50.270Z", "0.20.1": "2024-02-19T06:38:49.402Z", "0.20.2": "2024-03-14T19:49:58.220Z", "0.21.0": "2024-05-07T02:52:42.171Z", "0.21.1": "2024-05-07T16:55:22.988Z", "0.21.2": "2024-05-12T20:33:08.334Z", "0.21.3": "2024-05-15T20:52:46.754Z", "0.21.4": "2024-05-25T02:11:02.806Z", "0.21.5": "2024-06-09T21:17:13.044Z", "0.22.0": "2024-06-30T20:38:01.572Z", "0.23.0": "2024-07-02T03:33:59.956Z", "0.23.1": "2024-08-16T22:13:32.438Z", "0.24.0": "2024-09-22T02:06:40.573Z", "0.24.1": "2024-12-20T05:41:33.728Z", "0.24.2": "2024-12-20T17:56:50.665Z", "0.25.0": "2025-02-08T03:02:39.304Z", "0.25.1": "2025-03-10T03:45:50.204Z", "0.25.2": "2025-03-30T17:33:13.713Z", "0.25.3": "2025-04-23T03:56:28.447Z", "0.25.4": "2025-05-06T00:31:03.511Z", "0.25.5": "2025-05-27T03:13:10.511Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The Linux ARM binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Linux ARM binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}