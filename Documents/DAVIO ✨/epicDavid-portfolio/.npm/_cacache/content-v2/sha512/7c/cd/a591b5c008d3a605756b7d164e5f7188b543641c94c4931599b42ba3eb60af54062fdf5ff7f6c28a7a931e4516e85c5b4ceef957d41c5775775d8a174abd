{"_id": "file-entry-cache", "_rev": "41-e3c705539d98c89f173166b07edcdbef", "name": "file-entry-cache", "dist-tags": {"latest": "10.1.1"}, "versions": {"1.0.0": {"name": "file-entry-cache", "version": "1.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "f5ae2df96b41586a7589a590ad9f5dd1c3c36d8f", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.0.0.tgz", "integrity": "sha512-rFvyHTj4N2j8xQjq1wzlEEvOOt0wqtzS1H5daYg7qhrmwBfGzQ8sdI38J+y628QcAOnZDaMH8KFqYJRTs2lAwQ==", "signatures": [{"sig": "MEUCIAZxzJ9iDMED286mD6L0lFzKEQp6MR75vx2uRXm37bueAiEA0I0m+Apwhj66lQbHASFVqha1ZlIhkpUL/RC1py/B9nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "f5ae2df96b41586a7589a590ad9f5dd1c3c36d8f", "engines": {"node": ">=0.10.0"}, "gitHead": "7472eaac21ba358b88fa861b53a3d29bae782e3a", "scripts": {"test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "repository": {"url": "https://github.com/royriojas/file-entry-cache", "type": "git"}, "_npmVersion": "2.6.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.3", "lodash.assign": "^3.0.0"}, "devDependencies": {"del": "^1.1.1", "chai": "^2.1.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.1.1", "istanbul": "^0.3.6", "commander": "^2.6.0", "read-file": "^0.1.2", "watch-run": "^1.2.1", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "0.0.2"}}, "1.0.1": {"name": "file-entry-cache", "version": "1.0.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "8c3fbd8072ed34f1e10bfe74549d1d0f7fe9bdfa", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.0.1.tgz", "integrity": "sha512-nboKvcGKCNLaWh/rV7UwD+OOGJUNgx2w7OwLdvu2ZlLoCjfmICwJyD9Hsl8jWsPwu03mQXf7NuTeM8y1wddyBA==", "signatures": [{"sig": "MEUCIHkttUS9N+2ADzzRQsemQQw2V+zJEWlDu2lH6MpflucjAiEA01iN93FNjIvePmJMGjgKYlRrQBxEqN16lQRx/J/SEt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "8c3fbd8072ed34f1e10bfe74549d1d0f7fe9bdfa", "engines": {"node": ">=0.10.0"}, "gitHead": "7af29fc118dcfcc6077d33b676e15a43895ee8f0", "scripts": {"test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "repository": {"url": "https://github.com/royriojas/file-entry-cache", "type": "git"}, "_npmVersion": "2.6.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.3", "lodash.assign": "^3.0.0"}, "devDependencies": {"del": "^1.1.1", "chai": "^2.1.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.1.1", "istanbul": "^0.3.6", "commander": "^2.6.0", "read-file": "^0.1.2", "watch-run": "^1.2.1", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "0.0.2"}}, "1.1.1": {"name": "file-entry-cache", "version": "1.1.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "4509b16728e7bd29197c23044330397f03af8fda", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.1.1.tgz", "integrity": "sha512-t1nC/Y+q/4nm+DabrT5cCSRoDD87vlfahoi7QZddgPrIA8Nf47umYY5bP6mQAAa4H7mKbNcFWntsqNhd0ylhxg==", "signatures": [{"sig": "MEUCIAdp/gj7a8Z93URn8tzA5Rx27TrsUZwhdFtNxWxoN8gGAiEA83KInbtZKgYsf+vGd9x8LN9moTIVtu+P5+77uLM+YoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "4509b16728e7bd29197c23044330397f03af8fda", "engines": {"node": ">=0.10.0"}, "gitHead": "f35595de139f8156086024871b0177bcaa92f210", "scripts": {"test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "2.14.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.4", "lodash.assign": "^3.0.0"}, "devDependencies": {"del": "^1.1.1", "chai": "^2.1.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.1.1", "istanbul": "^0.3.6", "commander": "^2.6.0", "read-file": "^0.1.2", "watch-run": "^1.2.1", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "0.0.2"}}, "1.2.0": {"name": "file-entry-cache", "version": "1.2.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "8327500396f26c45505ff8a90bac11f334682e71", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.2.0.tgz", "integrity": "sha512-5yZdpJT3XHKN8orjIqMDJVt2DDmo5XhZtIG6rtfgwg4vIKmvMNG31QYIRoVX07xrVL9doGUvyxFV7rztTAf2vg==", "signatures": [{"sig": "MEUCIQCE1GzLiLSzGG+TryuD7W4x6uENw9KvXUjN8k3upPIQVgIgd8xZ6Q2A6mfJIkdo+OlbXGuQRqwnwVasuj+rUuzK9Tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "8327500396f26c45505ff8a90bac11f334682e71", "engines": {"node": ">=0.10.0"}, "gitHead": "2f8078eb781cc657e5dcd3cb0058cb80f2f606b8", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run verify", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'specs/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "esbeautifier -k 'cache.js' 'specs/**/*.js'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.4", "lodash.assign": "^3.0.0"}, "devDependencies": {"del": "^1.1.1", "chai": "^2.1.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.1.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "read-file": "^0.1.2", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "0.0.2", "esbeautifier": "^4.2.11"}}, "1.2.3": {"name": "file-entry-cache", "version": "1.2.3", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "8dc592ba3af6b4f5d92ffb71bc2460359e696cb6", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.2.3.tgz", "integrity": "sha512-PeDwO4LisXmnQnuSinpEc9DZ1tDLeS+uGaskrTxeaoL6jnmGDISjpB8UULvAkafTybQjztx4n7/VFQQwHLrYpA==", "signatures": [{"sig": "MEUCIEQb7J/fK5NBiHmNx7szq9rHe8XasaHKvgM61DjRrMl6AiEAyM88cBH6ao5QUyr5TLNggDfassIZU+mRlQQ+OwA4plM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "8dc592ba3af6b4f5d92ffb71bc2460359e696cb6", "engines": {"node": ">=0.10.0"}, "gitHead": "801a738f285fd6d90572ef6e2b7c7fd5eb7975f3", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run verify", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'specs/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "esbeautifier -k 'cache.js' 'specs/**/*.js'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.9", "object-assign": "^4.0.1"}, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.2.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "read-file": "^0.2.0", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}}, "1.2.4": {"name": "file-entry-cache", "version": "1.2.4", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "9a586072c69365a7ef7ec72a7c2b9046de091e9c", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.2.4.tgz", "integrity": "sha512-OhH/fWiMUCdN1eXs/jb8kv84xML59fq2vj+CStlrT/VAroorIUbX9npRWG4ZLGg2+EjMYfZ7Yf0reeNBEBm4hw==", "signatures": [{"sig": "MEYCIQDIVc1ilrh2E3l4N8b/JzvAHiOX9kqHC3i4akaNY0yecQIhAPZWh+o/ivxssOZN8VlEwiLNAvOTbeRt3d3HaEwI9Hm5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "9a586072c69365a7ef7ec72a7c2b9046de091e9c", "engines": {"node": ">=0.10.0"}, "gitHead": "3ca70d84f97822df7f769409f49ecf22c8908412", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run verify", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'specs/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "esbeautifier -k 'cache.js' 'specs/**/*.js'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"flat-cache": "^1.0.9", "object-assign": "^4.0.1"}, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.2.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "read-file": "^0.2.0", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}}, "1.3.0": {"name": "file-entry-cache", "version": "1.3.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "cf80d7f2a6880a06b1df08c0d90f936789ac6517", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.3.0.tgz", "integrity": "sha512-Rw0MbWnMGk7BDzBvr8tAr22HTwvvMc2jf7s0CPw1Mr5aucresMBVzS+uZhSlt6+wQkbNzdorgdHC2ZypXhu1iA==", "signatures": [{"sig": "MEUCIBBodI5Wf/BZuuAue9Hu2DzX6ZS7OhcED3izMrAIKku0AiEA8vxIIDq+hb1vTUDpW2KASNH3yK6lSM/hlOxyvkHx+ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "cf80d7f2a6880a06b1df08c0d90f936789ac6517", "engines": {"node": ">=0.10.0"}, "gitHead": "2b61acfaa69b1565725e40112cf57ff8de84d496", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "npm run verify && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache-1.3.0.tgz_1470048865108_0.6867655762471259", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "file-entry-cache", "version": "1.3.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "44c61ea607ae4be9c1402f41f44270cbfe334ff8", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.3.1.tgz", "integrity": "sha512-JyVk7P0Hvw6uEAwH4Y0j+rZMvaMWvLBYRmRGAF2S6jKTycf0mMDcC7d21Y2KyrKJk3XI8YghSsk5KmRdbvg0VQ==", "signatures": [{"sig": "MEYCIQDl5efLDGbf6hVCs2qECkUECDs6TrSpKNazwk+bOZszdwIhAOqZwj2p6Zbk09uH8tJ+bL+LMKF9xfsJOJofNV6kl8/v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "44c61ea607ae4be9c1402f41f44270cbfe334ff8", "engines": {"node": ">=0.10.0"}, "gitHead": "037bc03eb9c2e759a66da95cd5432499a74f754a", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "npm run verify && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache-1.3.1.tgz_1470049850988_0.09572241548448801", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "file-entry-cache", "version": "2.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "c392990c3e684783d838b8c84a45d8a048458361", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz", "integrity": "sha512-uXP/zGzxxFvFfcZGgBIwotm+Tdc55ddPAzF7iHshP4YGaXMww7rSF9peD9D1sui5ebONg5UobsZv+FfgEpGv/w==", "signatures": [{"sig": "MEUCIQCRYztJsreh/kaKTQorkG1bUqw7Qs+23MX/oXZQE2/X7QIgO1T4YMcWLcn2QUNOG33HyfNxJE5y0VgJECCHDPFsHNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "cache.js", "_from": ".", "files": ["cache.js"], "_shasum": "c392990c3e684783d838b8c84a45d8a048458361", "engines": {"node": ">=0.10.0"}, "gitHead": "8c015253938e1756104b524c09ea48798e788aa0", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "test": "npm run verify --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "^1.0.18", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache-2.0.0.tgz_1471380536263_0.40089720860123634", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.0": {"name": "file-entry-cache", "version": "4.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "633567d15364aefe0b299e1e217735e8f3a9f6e8", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-AVSwsnbV8vH/UVbvgEhf3saVQXORNv0ZzSkvkhQIaia5Tia+JhGTaa/ePUSVoPHQyGayQNmYfkzFi3WZV5zcpA==", "signatures": [{"sig": "MEUCIQD+dmxv58ujPrq+vNzzug0joxioPUUqCcwgZ7WYwFpNBAIgGxxd0G1UmZl8bCM5tRGn1eUn/5aykQgxnwHV1yii9cQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNXj+CRA9TVsSAnZWagAAPvAQAJpJD8xUj14mcbzP41KH\nqsCPrw09kDcU3/10R6uX9qeRrs9CYypusL5Y7lGDsmTMxYONCmOkS6x+8Axo\ndP2svv6y2OMeiuECzUDN7Uh1a96/LhJyqRtWutPtBvccsOn+cVUZql3FBHBf\nzjzYpWg3hd26j/MKnQOBY3FWh+QHB9k40HE18wUbOtmDZ++PYobGdqBBSQhQ\nl13Gw4SCu82si0msdolCeKUQr0ckdRm1nq7m4wP5QnMwawOjaZGpXfn6bAz7\nTnPuSJArAOLvOj3fNaOdGFGVg1gtGJgx+1kx5WoEbxP+Z4qA737sd3J9XJX+\nL7LaEPSQanj2K3pyawAy0azUhLamOjSBBs/Cc+yC+GkA2IxsUGQYfxCD2AZq\nw63bEg6PAOv0Zqs0olib/kxKXzEnzgrinvQ7yGBgv4Ma2XF6o1p4L/CNOtt1\n/KnuyVQOF31MusgY6yHwlqcaVMKc1emNoSucjSrxs7LboZC3oNDZaTkuaHkS\nb1667dk0OtkRv6KBwaMxVCBzEnQAgXuQutfrcN4WfYZBS2kwQxU9OblDNQd1\nhdpfAXrVHmlkYbgOygBdxzMfwswLocdJVWaLwZNKBbKLmjcrlCeDaCquv+dF\nDxmXSnVnWiwa4EdhKMJLLYIJiAvTbQz9xuYBOOGyV/6fH16qa53gvKqnBfGr\nRvw7\r\n=jrfi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "cache.js", "engines": {"node": ">=4"}, "gitHead": "b1368c9ad0d15f1013c4333b7e7b8cdc508c3eb3", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "perf": "node perf.js", "test": "npm run verify --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js' 'perf.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js' 'perf.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"flat-cache": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "3.0.0", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_4.0.0_1547008253627_0.8843953492824943", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "file-entry-cache", "version": "5.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "d9a52d59400ae45d328d7392ae2cca9590c58a5d", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-ZGLix5Mag+h/p5WQqOsdzNwae9h/luT5yfoV5zLxA6TctlxYkyZHGXrNFpjrvBQS7RUncmUjAAXh/BmBqq1m9A==", "signatures": [{"sig": "MEQCIE9yL3fMVWwCp5R9+XWsSVc1Bw32+zwp5aeeyPW0QRnAAiBU8GE46/rVIoYZRm98XUi+F8XnQinJX92J0NWeyjirfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcV6EFCRA9TVsSAnZWagAAqikP/2WnvoEqqH58bbEtJt91\nYL4fT9G+E2bW+M7c+VulSy2mNhajWR+/CI7BhWsEz03g5RvYi0xmmSkZqqMh\n+SdCp8+NsaMJ5y8e02sKDxkKf30Ny00p4qGoB63nBpU6Kdm7tynoigAE3clR\n+cOU4JAViaT6DMMIOQMSRGA+DHxN+cgx0Yb7RPJ+OTcAARHvEP6ARJf8kzPX\nYmhktVojx+yQhhTaFADf7ywbiGQdOiz/08eyR6YR2MwvWz2rthr8yNLXjjcJ\nAaK/GfzIYnoSgN7EgybHwh9JrqrJeMPPWOeI3ZDrAZjsSvJUOlyeVBaNkDRP\nhSGcdWVpit798UFQgE9nClngnb+V5abyKSDpWWkps1Q3lgGpV2GiLIdQyVF6\n9NOOuprmEpD8Edd837oMo1lErRjoEpMkV23DhK1d0FfI0HZg2CMyCEgh0rpf\niAfw94v5lG7TBddx9Gg3I7ikHX1suTaahSRouXDgocMpywCUFhUlmpkixrkp\nLWIyy1/GiaKeKyAO862S3HfEGSznxbLz4cK2wn+XkM7anOz9Rv0H4r1uK2ZE\nMPG8VzkNMcXz0uVOjxxlmCcpWwhniFRFV2egirCrfYxVhZroPKQ9xgHFCsc5\n+WwrFcaNqVCenys4ZIKyEyuVeoQGCXpE6PrKZSJipIOzHq2Qnh4utk5E6qmY\nPCDD\r\n=gXUb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "cache.js", "engines": {"node": ">=4"}, "gitHead": "875b3e16e4090aee52fc7debddac347beae87ff2", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "perf": "node perf.js", "test": "npm run verify --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js' 'perf.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js' 'perf.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"flat-cache": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "3.0.0", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_5.0.0_1549246724880_0.3431327890423166", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "file-entry-cache", "version": "5.0.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "ca0f6efa6dd3d561333fb14515065c2fafdf439c", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==", "signatures": [{"sig": "MEUCIFaH0npwRpmlgeEfkDpExxeoIM1X4evFUiW/LQzIY6rXAiEA/x2LkEuff9feUHQF99892J3F/UsAXnQBszuZDWGFgFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWHbfCRA9TVsSAnZWagAA+r0QAJgHt9+1XXt4EcoLLQvH\n+kpvsr12oKd14Dsst/vw9h9HCydZlE+hSBcaLF0Yy9Fc7UdrtJvFn6qkk5l2\n0AR2VIlmzyP9uOMPvI/jyUxH+4MmaQ54wIaYueX08wsmxISt0JzspGfCzDrD\n5BntEJ78mCpx8Q5jSFHVHPAQMJ3qeea+8JkrtU9Rxax6kwusvGa4aUHoe1oM\nxZ399mNBXatndwEWwYNthx5t9EHSBzyFGUGb8UugDu8utk0d1HR5cyWVUpOE\njV4EVof3g8BPpjtJb/Pp7VFQqi9kh1da4boSWawUSoWz6/nd3dmFmara4+lm\nZHvAUURhaMGgLm6GvhMM5NA62yTqcCG9MWhESXJySjwvrLMuDLQYoYLBHNVV\n5e+4tLh7RlDwLx2xZv8nEe3NYPiUe5tT4tAMNgx1eKAppIWnsirEPyVI5zlX\nxKKangzbLbsPLp314+uN5gFV4YkBghG5Ozyz1QmvPpERsJsw7LjlxuPO8UQU\n+XpNaRUmQLFhnZDc8EVecaQT+C3shOPkHTxhXoIecx5ha+3gO/UiRvaVX2r4\nVesR++ioYuVQB6tVsXcCkYT5hYVgsczOQVb36VH0QPwyYtUh5ouFn9s9aRkV\njLQ/XdwMXL6L4avvRs1Gu5tlf7KYvBZw2wB8uWLLavTu0c0e31Qdda1a9nUh\nSUEP\r\n=+xuX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "cache.js", "engines": {"node": ">=4"}, "gitHead": "03f700c99b76133dc14648b465a1550ec2930e5c", "prepush": ["npm run verify"], "scripts": {"lint": "npm run beautify && npm run eslint", "perf": "node perf.js", "test": "npm run verify --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslinter 'cache.js' 'specs/**/*.js' 'perf.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "verify": "npm run beautify-check && npm run eslint", "beautify": "esbeautifier 'cache.js' 'test/**/*.js' 'perf.js'", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install", "beautify-check": "npm run beautify -- -k"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run verify"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"flat-cache": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^2.0.2", "chai": "^3.2.0", "mocha": "^2.1.0", "sinon": "^1.12.2", "write": "^0.3.1", "prepush": "^3.1.4", "eslinter": "^2.3.3", "istanbul": "^0.3.6", "commander": "^2.6.0", "precommit": "^1.1.5", "watch-run": "^1.2.1", "changelogx": "3.0.0", "proxyquire": "^1.3.1", "sinon-chai": "^2.7.0", "glob-expand": "^0.1.0", "esbeautifier": "^4.2.11"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_5.0.1_1549301470793_0.20081957421306562", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "file-entry-cache", "version": "6.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "7921a89c391c6d93efec2169ac6bf300c527ea0a", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-fqoO76jZ3ZnYrXLDRxBR1YvOvc0k844kcOg40bgsPrE25LAb/PDqTY+ho64Xh2c8ZXgIKldchCFHczG2UVRcWA==", "signatures": [{"sig": "MEYCIQCM7ZIrNmr8KwxSPcxk7gQGrVODM/GfysVbEUuIDF+2kgIhAOOxUDKw1ov1S7k5p2RaY5+ZdEKcxVv56koD2p8Oyu70", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfp8vYCRA9TVsSAnZWagAAtpsQAItZmDwyL0a+hwr8tr5g\nofEOSg0p8kNZQuaK8AFATCfbFXtPH+seEYw4MvwzjKljqSNU0gHhLofQDkDx\nWCLKBWQjDyCXGATLJLDdhvOb+HWU5RgXLWvNwPjzAwtIrQjviqeWDSSKSq68\nUQlg6kl8Au9reO1D+N3rmBGatrBlUas613eejW7Tmqwj3SvqFkbju9EDCnb8\nnD1KCyfxeDJfFb/jxqu7ieBUF0hQuNb4KkbpqmHSTeN6ed1khc5QtxwQiEIU\n8tpthQ3MGvIcgzABxJKxGUnHX8QcNDbaBkTu9LtFPT5AfaaWe6QfsNfnTvuH\n6wICYslOHa8dFSNeEdt9QaCOBwjxHT1sbzQ/RH7K6RZHCGaXSMXUyRzEFNIz\nKP/Sc3/gVfyvWwRJ0rS4PTO6W3MkEcQcN0+4gYQeaUF+qu0tlxM1oI4TLJWv\nGyB3GRqrP1DjV+GfS5W2OxLIe9fzWQ95vejEmCzyxNeDKFLRnrnSF4cETHRd\nhFP/Vy4uzKpcm16PLNVORvYJg6u9frSg3h37UgM+aNVdNWYx8elQ9ZKM5Xj0\nI97fxtLG1extkb6Ro16Jb/86HqWdWaUfgBXk5zlUiFsfzbLRzXn90YiR5R69\nLjl8K8wa3rtSEghNDh/fN8H62p7uKvwIwkUnaHO/AFe0BvlsSUZWBQgghUz6\nEM4w\r\n=6pu6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "cache.js", "engines": {"node": "^10.12.0 || >=12.0.0"}, "gitHead": "6c2c42a8fe081ac2ee71b20f0cac3868e620ec2f", "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "autofix": "npm run eslint -- --fix", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run eslint --silent"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"flat-cache": "^3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "chai": "^4.2.0", "mocha": "^8.2.1", "write": "^2.0.0", "eslint": "^7.13.0", "prepush": "^3.1.11", "istanbul": "^0.4.5", "prettier": "^2.1.2", "precommit": "^1.2.2", "watch-run": "^1.2.5", "changelogx": "^5.0.6", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^8.0.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_6.0.0_1604832215815_0.8474781251845276", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "file-entry-cache", "version": "6.0.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "http://royriojas.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}], "homepage": "https://github.com/royriojas/file-entry-cache#readme", "bugs": {"url": "https://github.com/royriojas/file-entry-cache/issues"}, "dist": {"shasum": "211b2dd9659cb0394b073e7323ac3c933d522027", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==", "signatures": [{"sig": "MEUCICvskmlR9Bsi7v9cRQgYMqqLsUjhTgJbKjBhVcp7LndLAiEAghfp7zzUNtFT1PqlJ7m5bB8C4BeyX3UCz2qXn/hTrCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMIvQCRA9TVsSAnZWagAApVgP/3iT6YX40a5EwJz0mOPA\nX7KBI3byePivYf6Xs2EX0utlju/FAQB6uA1htUI4nExcJlUP5tjxGWXOt4Hq\n8eNNBF5PThNO7aCTklu5po8FFLC4DARqIePGXnywEYPXD4krTcZzbNEdr3nd\nhDUVeQ9rX7IqwiJlkXiVZXgqNWwH60qZmuXeGX+egfEJOQHjsyqOscwF9h36\nSoAsvUVLXe+lqRDX4HjohNrsdtEbl3LDlJ0YUeIkBUflqepV+yKkuhXaS+eM\nSpKSaecX2P+mw+MCH4xN++qaqeU/Sd8bC44JpeyJdA7B77I/4jNyzAw8soXV\n5qOuiofBB50JnG8NXK8rgl6Ryej7kT95J2j9ncv078HhdV/79a9i0VUshQ4Q\n05ei1VZpH0OZQY9ea1YcOkLe0rJ7bTM03KPcFH6n3fOlVHANheJCorRObEmC\nSyLLUXF7JanBvanpYgcZ9+402cH7c6G5gKh52OvsMFhQweWHt2d8C4iyQJAm\nwIROV/05mRZqGjv1GSx1H0tnv3jQ4mANR8aUZNCqDEIZZx8/O5lmVHEFBK6j\npxoYfU2pGQItWJrbjGt3SFNmO7XCRMlT6xyXzKKUIA75jootmIJEwY0230/u\nLXuKFIgVNF+G/3W+PaB7nmBBImjE7kscuYJw7WjMWpvFNE6GaKtTJtwPC4cR\nx9Sf\r\n=mnlC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "cache.js", "engines": {"node": "^10.12.0 || >=12.0.0"}, "gitHead": "22a7687365f6ce853f7e4720fece60818a6e77af", "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && mocha -R spec test/specs", "cover": "istanbul cover test/runner.js html text-summary", "pre-v": "npm run test", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "autofix": "npm run eslint -- --fix", "changelog": "changelogx -f markdown -o ./changelog.md", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "install-hooks": "prepush install && changelogx install-hook && precommit install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "roy<PERSON><EMAIL>"}, "precommit": ["npm run eslint --silent"], "changelogx": {"authorURL": "https://github.com/{0}", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache", "ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)"}, "repository": {"url": "git+https://github.com/royriojas/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"flat-cache": "^3.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "chai": "^4.2.0", "mocha": "^8.2.1", "write": "^2.0.0", "eslint": "^7.13.0", "prepush": "^3.1.11", "istanbul": "^0.4.5", "prettier": "^2.1.2", "precommit": "^1.2.2", "watch-run": "^1.2.5", "changelogx": "^5.0.6", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^8.0.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_6.0.1_1613794256283_0.950432315389824", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "file-entry-cache", "version": "7.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/file-entry-cache#readme", "bugs": {"url": "https://github.com/jaredwray/file-entry-cache/issues"}, "dist": {"shasum": "5bb4aef4f0a7dd2ff95966c6d97256b61504bd0a", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-OWhoO9dvvwspdI7YjGrs5wD7bPggVHc5b1NFAdyd1fEPIeno3Fj70fjBhklAqzUefgX7KCNDBnvrT8rZhS8Shw==", "signatures": [{"sig": "MEUCIQDSfhOtrcv7dNnbRNO4vtlTBjrrmq1lCOxir2c3SazwZQIgEGXRjcsJ9mLN1+njwML48dfA2hifGZBVmbSySJ0ysbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15948}, "main": "cache.js", "engines": {"node": ">=12.0.0"}, "gitHead": "7534eb6dd9357db2774a1b3c36c301db6d70b471", "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && c8 mocha -R spec test/specs", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "autofix": "npm run eslint -- --fix", "test:ci": "npm run eslint --silent && c8 --reporter=lcov mocha -R spec test/specs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run eslint --silent"], "repository": {"url": "git+https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"flat-cache": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "del": "^6.0.0", "chai": "^4.3.7", "mocha": "^10.2.0", "write": "^2.0.0", "eslint": "^7.13.0", "prettier": "^2.1.2", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^8.0.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_7.0.0_1693006074480_0.9812984696150886", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "file-entry-cache", "version": "7.0.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@7.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c71b3509badb040f362255a53e21f15a4e74fc0f", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-7.0.1.tgz", "fileCount": 5, "integrity": "sha512-uLfFktPmRetVCbHe5UPuekWrQ6hENufnA46qEGbfACkK5drjTTdQYUragRgMjHldcbYG+nslUerqMPjbBSHXjQ==", "signatures": [{"sig": "MEUCIHYRUHrec0sjkAdIwTHvLzKJDzwZdTU5j3gJ+933BvgRAiEA5pXGk7EqSuqBN5eTb6NgcW5cF7zqRZHJXsRNwQLejNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15949}, "main": "cache.js", "engines": {"node": ">=12.0.0"}, "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && c8 mocha -R spec test/specs", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "autofix": "npm run eslint -- --fix", "test:ci": "npm run eslint --silent && c8 --reporter=lcov mocha -R spec test/specs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run eslint --silent"], "repository": {"url": "https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> & <PERSON> Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "dependencies": {"flat-cache": "^3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "del": "^6.0.0", "chai": "^4.3.10", "mocha": "^10.2.0", "write": "^2.0.0", "eslint": "^8.50.0", "prettier": "^2.1.2", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^10.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^3.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_7.0.1_1696613319237_0.5453584869322894", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "file-entry-cache", "version": "7.0.2", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@7.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2d61bb70ba89b9548e3035b7c9173fe91deafff0", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-7.0.2.tgz", "fileCount": 5, "integrity": "sha512-TfW7/1iI4Cy7Y8L6iqNdZQVvdXn0f8B4QcIXmkIbtTIe/Okm/nSlHb4IwGzRVOd3WfSieCgvf5cMzEfySAIl0g==", "signatures": [{"sig": "MEUCIQCAkw9khGP5cp/0VMgCtIKb7mRB+oX9uLj6/rrlDHbqWwIgfvBddByVHecVJGuCbTHFnl4jrhHB8wRAfaGAYqi5x1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15928}, "main": "cache.js", "engines": {"node": ">=12.0.0"}, "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && c8 mocha -R spec test/specs", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "autofix": "npm run eslint -- --fix", "test:ci": "npm run eslint --silent && c8 --reporter=lcov mocha -R spec test/specs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run eslint --silent"], "repository": {"url": "https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> & <PERSON> Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "dependencies": {"flat-cache": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "mocha": "^10.2.0", "write": "^2.0.0", "eslint": "^8.50.0", "prettier": "^2.1.2", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^10.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^3.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_7.0.2_1700162302724_0.3617829891476847", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "file-entry-cache", "version": "8.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7787bddcf1131bffb92636c69457bbc0edd6d81f", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==", "signatures": [{"sig": "MEUCIQDlvowKnXFGms+/QHsqMjhmHWwlf5eVEDmrR8b1eGRXBQIgP4krXS6XN+8h9hGSJpNfumAvBo1l8KBch+zZAxXHn80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16022}, "main": "cache.js", "engines": {"node": ">=16.0.0"}, "prepush": ["npm run eslint --silent"], "scripts": {"perf": "node perf.js", "test": "npm run eslint --silent && c8 mocha -R spec test/specs", "clean": "rimraf ./node_modules ./package-lock.json ./yarn.lock", "eslint": "eslint --cache --cache-location=node_modules/.cache/ 'cache.js' 'test/**/*.js' 'perf.js'", "autofix": "npm run eslint -- --fix", "test:ci": "npm run eslint --silent && c8 --reporter=lcov mocha -R spec test/specs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run eslint --silent"], "repository": {"url": "https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> & <PERSON> Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "dependencies": {"flat-cache": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "mocha": "^10.2.0", "write": "^2.0.0", "eslint": "^8.56.0", "rimraf": "^5.0.5", "prettier": "^3.1.1", "glob-expand": "^0.2.1", "eslint-plugin-mocha": "^10.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_8.0.0_1702928038492_0.9045502235387175", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "file-entry-cache", "version": "9.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@9.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "xo": {"rules": {"unicorn/prefer-module": "off", "n/prefer-global/process": "off", "unicorn/prevent-abbreviations": "off"}}, "dist": {"shasum": "4478e7ceaa5191fa9676a2daa7030211c31b1e7e", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-9.0.0.tgz", "fileCount": 5, "integrity": "sha512-6MgEugi8p2tiUhqO7GnPsmbCCzj0YRCwwaTbpGRyKZesjRSzkqkAE9fPp7V2yMs5hwfgbQLgdvSSkGNg1s5Uvw==", "signatures": [{"sig": "MEUCIE52fVIk825n2wuCDKnJZM0bWaNxUBVj5EzA+ehuTxOAAiEA44cRM0Gu3WeB+rH0sZRC06PGcib/hz7a1yuhzM+Mcms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142}, "main": "cache.js", "engines": {"node": ">=18"}, "prepush": ["npm run test"], "scripts": {"perf": "node perf.js", "test": "xo --fix && c8 mocha -R spec test/specs", "clean": "rimraf ./coverage /node_modules ./package-lock.json ./yarn.lock ./pnpm-lock.yaml", "test:ci": "xo && c8 --reporter=lcov mocha -R spec test/specs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run test"], "repository": {"url": "https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> & <PERSON> Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "dependencies": {"flat-cache": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "chai": "^4.3.10", "mocha": "^10.4.0", "write": "^2.0.0", "rimraf": "^5.0.7", "webpack": "^5.91.0", "glob-expand": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_9.0.0_1716567533691_0.963037417324959", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "file-entry-cache", "version": "9.1.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"url": "https://jaredwray.com", "name": "<PERSON>"}, "license": "MIT", "_id": "file-entry-cache@9.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/file-entry-cache#readme", "bugs": {"url": "https://github.com/jaredwray/file-entry-cache/issues"}, "xo": {"rules": {"unicorn/prefer-module": "off", "n/prefer-global/process": "off", "unicorn/prevent-abbreviations": "off"}}, "dist": {"shasum": "2e66ad98ce93f49aed1b178c57b0b5741591e075", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-9.1.0.tgz", "fileCount": 4, "integrity": "sha512-/pqPFG+FdxWQj+/WSuzXSDaNzxgTLr/OrR1QuqfEZzDakpdYE70PwUxL7BPUa8hpjbvY1+qvCl8k+8Tq34xJgg==", "signatures": [{"sig": "MEUCIQCt69yYawkNcwyThDuuUVyX0T64mVH2EiyoydjFxKFx8AIgFQ40LLjpdHgRV8ye0gdYjTxO92Qth5Uc5VK3FqKHUgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16203}, "main": "cache.js", "engines": {"node": ">=18"}, "gitHead": "0be8ea67cd55cafa277ac199a62c3f087396d517", "prepush": ["npm run test"], "scripts": {"perf": "node perf.js", "test": "xo --fix && c8 mocha -R spec test/specs/cache.js test/relative.js", "clean": "rimraf ./coverage /node_modules ./package-lock.json ./yarn.lock ./pnpm-lock.yaml", "test:ci": "xo && c8 --reporter=lcov mocha -R spec test/specs/cache.js test/relative.js", "test:relative": "rimraf ./rfixtures ./tfixtures && mocha test/relative.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "precommit": ["npm run test"], "repository": {"url": "git+https://github.com/jaredwray/file-entry-cache.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.58.0", "chai": "^4.3.10", "mocha": "^10.5.1", "write": "^2.0.0", "rimraf": "^5.0.7", "webpack": "^5.92.1", "glob-expand": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_9.1.0_1724801613255_0.17112619896115522", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "file-entry-cache", "version": "10.0.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "b6f8a8a4dbd77bbb65b5333d58e121ad29c02213", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.0.tgz", "fileCount": 7, "integrity": "sha512-bqMgkF4SDLndrKMz7UFHsgfw3/cd1nIRZze96DcHSq2AsOiozgACOWNxeUI3C0NacQSyygzKsuWlYUxv+EUEEQ==", "signatures": [{"sig": "MEUCIQCBREYqDQBqvQeW//gcYIxxzEFpw6VFOefcWXgDBaY9HAIgCwzbxLVhAGLbGk9FQdk0ZkFqt149O28kThc9mtGVoeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49817}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/gs/5m4m2s857ts7l7nbvv0b57r80000gn/T/6279d7661e5de681968140c08158459a/file-entry-cache-10.0.0.tgz", "_integrity": "sha512-bqMgkF4SDLndrKMz7UFHsgfw3/cd1nIRZze96DcHSq2AsOiozgACOWNxeUI3C0NacQSyygzKsuWlYUxv+EUEEQ==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsup": "^8.3.0", "rimraf": "^6.0.1", "vitest": "^2.1.1", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@vitest/coverage-v8": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.0_1728083625106_0.304923438600996", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "file-entry-cache", "version": "10.0.1", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "d805aa0f3850926c8290fca8b48ecc56d4077932", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.1.tgz", "fileCount": 7, "integrity": "sha512-p3rsjCEU49LpXxkIW8W8JkGEOrD2HwQ1UHZx8GQ+9bVkbJyq/UR7fkHbLxPRRVXMT8akEjigh/szbOuKWYLdnA==", "signatures": [{"sig": "MEYCIQCsHKacxeSd9UHtC2AW+iNNy936twhwvTyz+BpWvjdiDgIhAOOJGE7RcjhZJjIsyzIih8oezcXVjxjk11oDJzqKbJbi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49934}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/003a2755da8ba1226fb6f297c2e0d8c6/file-entry-cache-10.0.1.tgz", "_integrity": "sha512-p3rsjCEU49LpXxkIW8W8JkGEOrD2HwQ1UHZx8GQ+9bVkbJyq/UR7fkHbLxPRRVXMT8akEjigh/szbOuKWYLdnA==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.8.3", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsup": "^8.3.5", "rimraf": "^6.0.1", "vitest": "^2.1.3", "typescript": "^5.6.3", "@types/node": "^22.8.1", "@vitest/coverage-v8": "^2.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.1_1730059574582_0.16113441691734542", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "file-entry-cache", "version": "10.0.2", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "41ef47976445087dc334b90d38a2de33f85a9d0a", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.2.tgz", "fileCount": 7, "integrity": "sha512-NCR+vD1IDP7rQ4D5yOpDfP1hH00jcoINoqB/hlN9p28tDbmr4ps2X10qEX3iOg5tKmVzzS4wEqJ66+aSALO6fQ==", "signatures": [{"sig": "MEUCIQC2ii5oznH6enuqlH+IhinaBu+s6uDDoNEsc4GedW4snwIgeHs0Epunz4ETXO33yNguG0SqNvUrX8Ip1fIRVDRqNqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49866}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/gs/5m4m2s857ts7l7nbvv0b57r80000gn/T/bb7d456e1348d021f412e75866c79dd0/file-entry-cache-10.0.2.tgz", "_integrity": "sha512-NCR+vD1IDP7rQ4D5yOpDfP1hH00jcoINoqB/hlN9p28tDbmr4ps2X10qEX3iOg5tKmVzzS4wEqJ66+aSALO6fQ==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsup": "^8.3.5", "rimraf": "^6.0.1", "vitest": "^2.1.4", "typescript": "^5.6.3", "@types/node": "^22.9.0", "@vitest/coverage-v8": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.2_1731276622158_0.308421098123171", "host": "s3://npm-registry-packages"}}, "10.0.3": {"name": "file-entry-cache", "version": "10.0.3", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "a26256cbcc3721e19b6ec881760cfb56eab7f491", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.3.tgz", "fileCount": 7, "integrity": "sha512-qYlnp0SEzk/UKOQHbpTLovLQzSOsFCr1Nttrdwq9YtH1oWJI50PEymtWbw53g042NImQxEW8mrTYzGS6ynL3mQ==", "signatures": [{"sig": "MEUCIQCeF+oDztHXVJ1L9LCg0GCrVbsyMg3tPhiEx5MLByvcsQIgfZgz0Pw5dWho07rDTV4AWfsg2deWvsKJ2/u9JDqBzgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50168}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/gs/5m4m2s857ts7l7nbvv0b57r80000gn/T/b12e682dbc6dfeab2b64547c39f0ec6b/file-entry-cache-10.0.3.tgz", "_integrity": "sha512-qYlnp0SEzk/UKOQHbpTLovLQzSOsFCr1Nttrdwq9YtH1oWJI50PEymtWbw53g042NImQxEW8mrTYzGS6ynL3mQ==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.1", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsup": "^8.3.5", "rimraf": "^6.0.1", "vitest": "^2.1.5", "typescript": "^5.7.2", "@types/node": "^22.9.3", "@vitest/coverage-v8": "^2.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.3_1732406165212_0.6830251518574575", "host": "s3://npm-registry-packages"}}, "10.0.4": {"name": "file-entry-cache", "version": "10.0.4", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "d2f414a6de2f2d4f056322ef91c90c46afbad19f", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.4.tgz", "fileCount": 7, "integrity": "sha512-8NCiVLrQ0IRlXuRW/OGsfwE2X4dvvK1YI66Ywi8slT41mFQHZasJZGMNKo/qTE7KcLPdxHK1PwLCoTt6rBuIqA==", "signatures": [{"sig": "MEYCIQCv+fArtnRemrsIEHvBMuCUtUUP03Bvfyy+soP20zTwKwIhAL9Lh5+khp7LGWNcQpWNOqtSbyDXRj8OyJuVVZS6YO3r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50004}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/9034418c9ba6d30a1fd67ca9470134b8/file-entry-cache-10.0.4.tgz", "_integrity": "sha512-8NCiVLrQ0IRlXuRW/OGsfwE2X4dvvK1YI66Ywi8slT41mFQHZasJZGMNKo/qTE7KcLPdxHK1PwLCoTt6rBuIqA==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsup": "^8.3.5", "rimraf": "^6.0.1", "vitest": "^2.1.5", "typescript": "^5.7.2", "@types/node": "^22.9.3", "@vitest/coverage-v8": "^2.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.4_1732469957195_0.4048643352844761", "host": "s3://npm-registry-packages"}}, "10.0.5": {"name": "file-entry-cache", "version": "10.0.5", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "0255cd065769ef930005073883389e432a16a9a7", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.5.tgz", "fileCount": 7, "integrity": "sha512-umpQsJrBNsdMDgreSryMEXvJh66XeLtZUwA8Gj7rHGearGufUFv6rB/bcXRFsiGWw/VeSUgUofF4Rf2UKEOrTA==", "signatures": [{"sig": "MEYCIQCTJJKCi0FpNBlNXQ6EIIwJ1hfIqmTolUC2t0ouoE91pwIhAJgfuqwh/GKhr3uYHUgkDd75f/AFaZZZXDnTPjHPnPvP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50043}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run", "prepublish": "pnpm build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/q4/x95kq1ln6cd7rrnct9cby32r0000gn/T/b6ade02e638b5b7fafec21130757e909/file-entry-cache-10.0.5.tgz", "_integrity": "sha512-umpQsJrBNsdMDgreSryMEXvJh66XeLtZUwA8Gj7rHGearGufUFv6rB/bcXRFsiGWw/VeSUgUofF4Rf2UKEOrTA==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"flat-cache": "^6.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsup": "^8.3.5", "rimraf": "^6.0.1", "vitest": "^2.1.8", "typescript": "^5.7.2", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.5_1735259084636_0.7548111710632699", "host": "s3://npm-registry-packages-npm-production"}}, "10.0.6": {"name": "file-entry-cache", "version": "10.0.6", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "1fc49c38231b56e792c65222c0aa519d40b5db55", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.6.tgz", "fileCount": 7, "integrity": "sha512-0wvv16mVo9nN0Md3k7DMjgAPKG/TY4F/gYMBVb/wMThFRJvzrpaqBFqF6km9wf8QfYTN+mNg5aeaBLfy8k35uA==", "signatures": [{"sig": "MEUCIQCF0l+5dtUYsyAlfv1NLWo/cP7pmpXSYrAxqs1pVR6CJQIgO3PlnQae4OLVdLTjAeKteaX6dSvv4L/Sc6lU3AQ/EZo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50038}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run", "prepublish": "pnpm build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/6939fe6e942853e3fe072f6ea9426776/file-entry-cache-10.0.6.tgz", "_integrity": "sha512-0wvv16mVo9nN0Md3k7DMjgAPKG/TY4F/gYMBVb/wMThFRJvzrpaqBFqF6km9wf8QfYTN+mNg5aeaBLfy8k35uA==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsup": "^8.3.6", "rimraf": "^6.0.1", "vitest": "^3.0.4", "typescript": "^5.7.3", "@types/node": "^22.10.10", "@vitest/coverage-v8": "^3.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.6_1738009619825_0.10573402643542651", "host": "s3://npm-registry-packages-npm-production"}}, "10.0.7": {"name": "file-entry-cache", "version": "10.0.7", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "e0ac34d4b8c44bea8a0a27ceb4dae982f2d32749", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.7.tgz", "fileCount": 7, "integrity": "sha512-txsf5fu3anp2ff3+gOJJzRImtrtm/oa9tYLN0iTuINZ++EyVR/nRrg2fKYwvG/pXDofcrvvb0scEbX3NyW/COw==", "signatures": [{"sig": "MEYCIQDIyS3be1tBqt9JgEQdlF1ZbqLu5wi3H+6S3eLz//bHHAIhALJrqdyD2hDYzV2QW8iOYbfezbKjgkwRW5f45wribVGl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50043}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run", "prepublish": "pnpm build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/q4/x95kq1ln6cd7rrnct9cby32r0000gn/T/bedb64ea1180eb5f7861b7b871602de3/file-entry-cache-10.0.7.tgz", "_integrity": "sha512-txsf5fu3anp2ff3+gOJJzRImtrtm/oa9tYLN0iTuINZ++EyVR/nRrg2fKYwvG/pXDofcrvvb0scEbX3NyW/COw==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "11.1.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"flat-cache": "^6.1.7"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsup": "^8.4.0", "rimraf": "^6.0.1", "vitest": "^3.0.7", "typescript": "^5.8.2", "@types/node": "^22.13.9", "@vitest/coverage-v8": "^3.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.7_1741043250340_0.36888796750093267", "host": "s3://npm-registry-packages-npm-production"}}, "10.0.8": {"name": "file-entry-cache", "version": "10.0.8", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "2b7a32c40615c4a6b59c385fb059a2762faf9624", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.0.8.tgz", "fileCount": 7, "integrity": "sha512-FGXHpfmI4XyzbLd3HQ8cbUcsFGohJpZtmQRHr8z8FxxtCe2PcpgIlVLwIgunqjvRmXypBETvwhV4ptJizA+Y1Q==", "signatures": [{"sig": "MEUCIQCJIlCyaIKxPUylfN+H4z9qTY9nX4P8boQaZzr2y7pTNwIgXQv2drgxZcmoasMOStEMUVzTK2bniIPDyMbzQEHWl6k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50043}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run", "prepublish": "pnpm build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/0ebe6c26a95fc76656a7a256975259ad/file-entry-cache-10.0.8.tgz", "_integrity": "sha512-FGXHpfmI4XyzbLd3HQ8cbUcsFGohJpZtmQRHr8z8FxxtCe2PcpgIlVLwIgunqjvRmXypBETvwhV4ptJizA+Y1Q==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "10.9.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"flat-cache": "^6.1.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsup": "^8.4.0", "rimraf": "^6.0.1", "vitest": "^3.1.1", "typescript": "^5.8.2", "@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.0.8_1743871259862_0.7162771618150221", "host": "s3://npm-registry-packages-npm-production"}}, "10.1.0": {"name": "file-entry-cache", "version": "10.1.0", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "file-entry-cache@10.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "dist": {"shasum": "54c0117fe76425e9f08a44a3a08bedde0cd93fe8", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.1.0.tgz", "fileCount": 7, "integrity": "sha512-Et/ex6smi3wOOB+n5mek+Grf7P2AxZR5ueqRUvAAn4qkyatXi3cUC1cuQXVkX0VlzBVsN4BkWJFmY/fYiRTdww==", "signatures": [{"sig": "MEYCIQD56+bWRvDciacfTVOT2lmso8P4wi0YnCeXYrBXr/U2mgIhAOmnpdoW6bMJIIGDZkMP1QaYEMpa5rIez4pQFZX4HBP+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56053}, "main": "./dist/index.cjs", "type": "module", "_from": "file:file-entry-cache-10.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "private": false, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./dist ./coverage ./node_modules", "test:ci": "xo && vitest run", "prepublish": "pnpm build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/q4/x95kq1ln6cd7rrnct9cby32r0000gn/T/e45f4005b87f53c5e63b8794c4c73960/file-entry-cache-10.1.0.tgz", "_integrity": "sha512-Et/ex6smi3wOOB+n5mek+Grf7P2AxZR5ueqRUvAAn4qkyatXi3cUC1cuQXVkX0VlzBVsN4BkWJFmY/fYiRTdww==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/file-entry-cache"}, "_npmVersion": "11.3.0", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"flat-cache": "^6.1.9"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsup": "^8.4.0", "rimraf": "^6.0.1", "vitest": "^3.1.3", "typescript": "^5.8.3", "@types/node": "^22.15.8", "@vitest/coverage-v8": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/file-entry-cache_10.1.0_1746546876636_0.844361504758806", "host": "s3://npm-registry-packages-npm-production"}}, "10.1.1": {"name": "file-entry-cache", "version": "10.1.1", "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/file-entry-cache"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "private": false, "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "devDependencies": {"@types/node": "^22.15.30", "@vitest/coverage-v8": "^3.2.2", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.2", "xo": "^1.1.0"}, "dependencies": {"flat-cache": "^6.1.10"}, "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "clean": "rimraf ./dist ./coverage ./node_modules"}, "_id": "file-entry-cache@10.1.1", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "homepage": "https://github.com/jaredwray/cacheable#readme", "_integrity": "sha512-zcmsHjg2B2zjuBgjdnB+9q0+cWcgWfykIcsDkWDB4GTPtl1eXUA+gTI6sO0u01AqK3cliHryTU55/b2Ow1hfZg==", "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/d803e6236c7154659e7d1d3d436cf7f7/file-entry-cache-10.1.1.tgz", "_from": "file:file-entry-cache-10.1.1.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "11.4.1", "dist": {"integrity": "sha512-zcmsHjg2B2zjuBgjdnB+9q0+cWcgWfykIcsDkWDB4GTPtl1eXUA+gTI6sO0u01AqK3cliHryTU55/b2Ow1hfZg==", "shasum": "ca46f5c4eb22cc37e4ac30214452a59c297d2119", "tarball": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-10.1.1.tgz", "fileCount": 7, "unpackedSize": 56213, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHwicpBRWyKCfdDp9i8b9D7RtK6QMYSrHhV2cd4tmrDGAiEAnhicLiQEysaP+15cYZX/4phAuZHmtLR6mDzpIOk6Uno="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/file-entry-cache_10.1.1_1749405502471_0.8476020995736535"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-03-02T09:30:04.174Z", "modified": "2025-06-08T17:58:22.869Z", "1.0.0": "2015-03-02T09:30:04.174Z", "1.0.1": "2015-03-03T07:25:43.492Z", "1.1.1": "2015-08-30T12:33:58.212Z", "1.2.0": "2015-09-04T19:41:15.848Z", "1.2.3": "2015-09-11T23:04:53.974Z", "1.2.4": "2015-09-24T02:37:08.705Z", "1.3.0": "2016-08-01T10:54:27.101Z", "1.3.1": "2016-08-01T11:10:51.661Z", "2.0.0": "2016-08-16T20:48:58.839Z", "4.0.0": "2019-01-09T04:30:53.721Z", "5.0.0": "2019-02-04T02:18:44.958Z", "5.0.1": "2019-02-04T17:31:10.923Z", "6.0.0": "2020-11-08T10:43:35.953Z", "6.0.1": "2021-02-20T04:10:56.462Z", "7.0.0": "2023-08-25T23:27:54.651Z", "7.0.1": "2023-10-06T17:28:39.423Z", "7.0.2": "2023-11-16T19:18:22.969Z", "8.0.0": "2023-12-18T19:33:58.734Z", "9.0.0": "2024-05-24T16:18:53.882Z", "9.1.0": "2024-08-27T23:33:33.402Z", "10.0.0": "2024-10-04T23:13:45.277Z", "10.0.1": "2024-10-27T20:06:14.775Z", "10.0.2": "2024-11-10T22:10:22.337Z", "10.0.3": "2024-11-23T23:56:05.420Z", "10.0.4": "2024-11-24T17:39:17.428Z", "10.0.5": "2024-12-27T00:24:44.839Z", "10.0.6": "2025-01-27T20:27:00.026Z", "10.0.7": "2025-03-03T23:07:30.575Z", "10.0.8": "2025-04-05T16:41:00.044Z", "10.1.0": "2025-05-06T15:54:36.825Z", "10.1.1": "2025-06-08T17:58:22.671Z"}, "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jaredwray/cacheable#readme", "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/file-entry-cache"}, "description": "A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "[<img align=\"center\" src=\"https://cacheable.org/symbol.svg\" alt=\"Cacheable\" />](https://github.com/jaredwray/cacheable)\n\n# file-entry-cache\n> A lightweight cache for file metadata, ideal for processes that work on a specific set of files and only need to reprocess files that have changed since the last run\n\n[![codecov](https://codecov.io/gh/jaredwray/cacheable/graph/badge.svg?token=lWZ9OBQ7GM)](https://codecov.io/gh/jaredwray/cacheable)\n[![tests](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml/badge.svg)](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml)\n[![npm](https://img.shields.io/npm/dm/file-entry-cache.svg)](https://www.npmjs.com/package/file-entry-cache)\n[![npm](https://img.shields.io/npm/v/file-entry-cache)](https://www.npmjs.com/package/file-entry-cache)\n[![license](https://img.shields.io/github/license/jaredwray/cacheable)](https://github.com/jaredwray/cacheable/blob/main/LICENSE)\n\n# Features\n\n- Lightweight cache for file metadata\n- Ideal for processes that work on a specific set of files\n- Persists cache to Disk via `reconcile()` or `persistInterval` on `cache` options.\n- Uses `checksum` to determine if a file has changed\n- Supports `relative` and `absolute` paths\n- Ability to rename keys in the cache. Useful when renaming directories.\n- ESM and CommonJS support with Typescript\n\n# Table of Contents\n\n- [Installation](#installation)\n- [Getting Started](#getting-started)\n- [Changes from v9 to v10](#changes-from-v9-to-v10)\n- [Global Default Functions](#global-default-functions)\n- [FileEntryCache Options (FileEntryCacheOptions)](#fileentrycache-options-fileentrycacheoptions)\n- [API](#api)\n- [Get File Descriptor](#get-file-descriptor)\n- [Using Checksums to Determine if a File has Changed (useCheckSum)](#using-checksums-to-determine-if-a-file-has-changed-usechecksum)\n- [Setting Additional Meta Data](#setting-additional-meta-data)\n- [How to Contribute](#how-to-contribute)\n- [License and Copyright](#license-and-copyright)\n\n# Installation\n```bash\nnpm install file-entry-cache\n```\n\n# Getting Started\n\n```javascript\nimport fileEntryCache from 'file-entry-cache';\nconst cache = fileEntryCache.create('cache1');\nlet fileDescriptor = cache.getFileDescriptor('file.txt');\nconsole.log(fileDescriptor.changed); // true as it is the first time\nfileDescriptor = cache.getFileDescriptor('file.txt');\nconsole.log(fileDescriptor.changed); // false as it has not changed\n// do something to change the file\nfs.writeFileSync('file.txt', 'new data foo bar');\n// check if the file has changed\nfileDescriptor = cache.getFileDescriptor('file.txt');\nconsole.log(fileDescriptor.changed); // true\n```\n\nSave it to Disk and Reconsile files that are no longer found\n```javascript\nimport fileEntryCache from 'file-entry-cache';\nconst cache = fileEntryCache.create('cache1');\nlet fileDescriptor = cache.getFileDescriptor('file.txt');\nconsole.log(fileDescriptor.changed); // true as it is the first time\nfileEntryCache.reconcile(); // save the cache to disk and remove files that are no longer found\n```\n\nLoad the cache from a file:\n\n```javascript\nimport fileEntryCache from 'file-entry-cache';\nconst cache = fileEntryCache.createFromFile('/path/to/cache/file');\nlet fileDescriptor = cache.getFileDescriptor('file.txt');\nconsole.log(fileDescriptor.changed); // false as it has not changed from the saved cache.\n```\n\n# Changes from v9 to v10\n\nThere have been many features added and changes made to the `file-entry-cache` class. Here are the main changes:\n- Added `cache` object to the options to allow for more control over the cache\n- Added `hashAlgorithm` to the options to allow for different checksum algorithms. Note that if you load from file it most likely will break if the value was something before. \n- Updated more on using Relative or Absolute paths. We now support both on `getFileDescriptor()`. You can read more on this in the `Get File Descriptor` section.\n- Migrated to Typescript with ESM and CommonJS support. This allows for better type checking and support for both ESM and CommonJS.\n- Once options are passed in they get assigned as properties such as `hashAlgorithm` and `currentWorkingDirectory`. This allows for better control and access to the options. For the Cache options they are assigned to `cache` such as `cache.ttl` and `cache.lruSize`.\n- Added `cache.persistInterval` to allow for saving the cache to disk at a specific interval. This will save the cache to disk at the interval specified instead of calling `reconsile()` to save. (`off` by default)\n- Added `getFileDescriptorsByPath(filePath: string): FileEntryDescriptor[]` to get all the file descriptors that start with the path specified. This is useful when you want to get all the files in a directory or a specific path.\n- Added `renameAbsolutePathKeys(oldPath: string, newPath: string): void` will rename the keys in the cache from the old path to the new path. This is useful when you rename a directory and want to update the cache without reanalyzing the files.\n- Using `flat-cache` v6 which is a major update. This allows for better performance and more control over the cache.\n- On `FileEntryDescriptor.meta` if using typescript you need to use the `meta.data` to set additional information. This is to allow for better type checking and to avoid conflicts with the `meta` object which was `any`.\n\n# Global Default Functions\n- `create(cacheId: string, cacheDirectory?: string, useCheckSum?: boolean, currentWorkingDirectory?: string)` - Creates a new instance of the `FileEntryCache` class\n- `createFromFile(cachePath: string, useCheckSum?: boolean, currentWorkingDirectory?: string)` - Creates a new instance of the `FileEntryCache` class and loads the cache from a file.\n\n# FileEntryCache Options (FileEntryCacheOptions)\n- `currentWorkingDirectory?` - The current working directory. Used when resolving relative paths.\n- `useModifiedTime?` - If `true` it will use the modified time to determine if the file has changed. Default is `true`\n- `useCheckSum?` - If `true` it will use a checksum to determine if the file has changed. Default is `false`\n- `hashAlgorithm?` - The algorithm to use for the checksum. Default is `md5` but can be any algorithm supported by `crypto.createHash`\n- `cache.ttl?` - The time to live for the cache in milliseconds. Default is `0` which means no expiration\n- `cache.lruSize?` - The number of items to keep in the cache. Default is `0` which means no limit\n- `cache.useClone?` - If `true` it will clone the data before returning it. Default is `false`\n- `cache.expirationInterval?` - The interval to check for expired items in the cache. Default is `0` which means no expiration\n- `cache.persistInterval?` - The interval to save the data to disk. Default is `0` which means no persistence\n- `cache.cacheDir?` - The directory to save the cache files. Default is `./cache`\n- `cache.cacheId?` - The id of the cache. Default is `cache1`\n- `cache.parse?` - The function to parse the data. Default is `flatted.parse`\n- `cache.stringify?` - The function to stringify the data. Default is `flatted.stringify`\n\n# API\n\n- `constructor(options?: FileEntryCacheOptions)` - Creates a new instance of the `FileEntryCache` class\n- `useCheckSum: boolean` - If `true` it will use a checksum to determine if the file has changed. Default is `false`\n- `hashAlgorithm: string` - The algorithm to use for the checksum. Default is `md5` but can be any algorithm supported by `crypto.createHash`\n- `currentWorkingDirectory: string` - The current working directory. Used when resolving relative paths.\n- `getHash(buffer: Buffer): string` - Gets the hash of a buffer used for checksums\n- `createFileKey(filePath: string): string` - Creates a key for the file path. This is used to store the data in the cache based on relative or absolute paths.\n- `deleteCacheFile(filePath: string): void` - Deletes the cache file\n- `destroy(): void` - Destroys the cache. This will also delete the cache file. If using cache persistence it will stop the interval.\n- `removeEntry(filePath: string): void` - Removes an entry from the cache. This can be `relative` or `absolute` paths.\n- `reconcile(): void` - Saves the cache to disk and removes any files that are no longer found.\n- `hasFileChanged(filePath: string): boolean` - Checks if the file has changed. This will return `true` if the file has changed.\n- `getFileDescriptor(filePath: string, options?: { useModifiedTime?: boolean, useCheckSum?: boolean, currentWorkingDirectory?: string }): FileEntryDescriptor` - Gets the file descriptor for the file. Please refer to the entire section on `Get File Descriptor` for more information.\n- `normalizeEntries(entries: FileEntryDescriptor[]): FileEntryDescriptor[]` - Normalizes the entries to have the correct paths. This is used when loading the cache from disk.\n- `analyzeFiles(files: string[])` will return `AnalyzedFiles` object with `changedFiles`, `notFoundFiles`, and `notChangedFiles` as FileDescriptor arrays.\n- `getUpdatedFiles(files: string[])` will return an array of `FileEntryDescriptor` objects that have changed.\n- `getFileDescriptorsByPath(filePath: string): FileEntryDescriptor[]` will return an array of `FileEntryDescriptor` objects that starts with the path specified.\n- `renameAbsolutePathKeys(oldPath: string, newPath: string): void` - Renames the keys in the cache from the old path to the new path. This is useful when you rename a directory and want to update the cache without reanalyzing the files.\n\n# Get File Descriptor\n\nThe `getFileDescriptor(filePath: string, options?: { useCheckSum?: boolean, currentWorkingDirectory?: string }): FileEntryDescriptor` function is used to get the file descriptor for the file. This function will return a `FileEntryDescriptor` object that has the following properties:\n\n- `key: string` - The key for the file. This is the relative or absolute path of the file.\n- `changed: boolean` - If the file has changed since the last time it was analyzed.\n- `notFound: boolean` - If the file was not found.\n- `meta: FileEntryMeta` - The meta data for the file. This has the following prperties: `size`, `mtime`, `ctime`, `hash`, `data`. Note that `data` is an object that can be used to store additional information.\n- `err` - If there was an error analyzing the file.\n\nWe have added the ability to use `relative` or `absolute` paths. If you pass in a `relative` path it will use the `currentWorkingDirectory` to resolve the path. If you pass in an `absolute` path it will use the path as is. This is useful when you want to use `relative` paths but also want to use `absolute` paths. \n\nIf you do not pass in `currentWorkingDirectory` in the class options or in the `getFileDescriptor` function it will use the `process.cwd()` as the default `currentWorkingDirectory`.\n\n```javascript\nconst fileEntryCache = new FileEntryCache();\nconst fileDescriptor = fileEntryCache.getFileDescriptor('file.txt', { currentWorkingDirectory: '/path/to/directory' });\n```\n\nSince this is a relative path it will use the `currentWorkingDirectory` to resolve the path. If you want to use an absolute path you can do the following:\n\n```javascript\nconst fileEntryCache = new FileEntryCache();\nconst filePath = path.resolve('/path/to/directory', 'file.txt');\nconst fileDescriptor = fileEntryCache.getFileDescriptor(filePath);\n```\n\nThis will save the key as the absolute path.\n\nIf there is an error when trying to get the file descriptor it will return an ``notFound` and `err` property with the error.\n\n```javascript\nconst fileEntryCache = new FileEntryCache();\nconst fileDescriptor = fileEntryCache.getFileDescriptor('no-file');\nif (fileDescriptor.err) {\n    console.error(fileDescriptor.err);\n}\n\nif (fileDescriptor.notFound) {\n    console.error('File not found');\n}\n```\n\n# Using Checksums to Determine if a File has Changed (useCheckSum)\n\nBy default the `useCheckSum` is `false`. This means that the `FileEntryCache` will use the `mtime` and `ctime` to determine if the file has changed. If you set `useCheckSum` to `true` it will use a checksum to determine if the file has changed. This is useful when you want to make sure that the file has not changed at all. \n\n```javascript\nconst fileEntryCache = new FileEntryCache();\nconst fileDescriptor = fileEntryCache.getFileDescriptor('file.txt', { useCheckSum: true });\n```\n\nYou can pass `useCheckSum` in the FileEntryCache options, as a property `.useCheckSum` to make it default for all files, or in the `getFileDescriptor` function. Here is an example where you set it globally but then override it for a specific file:\n\n```javascript\nconst fileEntryCache = new FileEntryCache({ useCheckSum: true });\nconst fileDescriptor = fileEntryCache.getFileDescriptor('file.txt', { useCheckSum: false });\n``` \n\n# Setting Additional Meta Data\n\nIn the past we have seen people do random values on the `meta` object. This can cause issues with the `meta` object. To avoid this we have `data` which can be anything. \n\n```javascript \nconst fileEntryCache = new FileEntryCache();\nconst fileDescriptor = fileEntryCache.getFileDescriptor('file.txt');\nfileDescriptor.meta.data = { myData: 'myData' }; //anything you want\n```\n# How to Contribute\n\nYou can contribute by forking the repo and submitting a pull request. Please make sure to add tests and update the documentation. To learn more about how to contribute go to our main README [https://github.com/jaredwray/cacheable](https://github.com/jaredwray/cacheable). This will talk about how to `Open a Pull Request`, `Ask a Question`, or `Post an Issue`.\n\n# License and Copyright\n[MIT © Jared Wray](./LICENSE)", "readmeFilename": "README.md", "users": {"elussich": true, "flumpus-dev": true}}