{"_id": "deep-eql", "_rev": "46-e5024e659da7afd1f9dac2f7bc4953a2", "name": "deep-eql", "description": "Improved deep equality testing for Node.js and the browser.", "dist-tags": {"latest": "5.0.2", "legacy": "4.1.4"}, "versions": {"0.1.0": {"name": "deep-eql", "version": "0.1.0", "keywords": ["deep equal", "object equal", "testing", "chai util"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@0.1.0", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "165c5c41887f68740408e29cc14bf668124aea7a", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-0.1.0.tgz", "integrity": "sha512-TMu9X75rPyYsY727g/1eMGt04zemUZ2hPE3hI7GL2NbAXporuXBddLqdo+B0pJKGa/ftjG8G5psV9YeXmoypvw==", "signatures": [{"sig": "MEQCIBEKmPRjn5BDrdjSaMfVL7nT102FX4SBn+qph1IKlTOoAiA4fUZvyRG0yObVs2ZPfGu6ST8tmkUqtd6dO/wQKViaMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "dependencies": {"type-detect": "0.1.0"}, "devDependencies": {"karma": "0.10.x", "mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "karma-mocha": "*", "simple-assert": "*", "mocha-lcov-reporter": "0.0.1"}}, "0.1.1": {"name": "deep-eql", "version": "0.1.1", "keywords": ["deep equal", "object equal", "testing", "chai util"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@0.1.1", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "0ec6f5f9b6eacae5dd87fa47a0e2c0f42d12419d", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-0.1.1.tgz", "integrity": "sha512-nsS8YYXiZu13nDaUNgyah274PvEgragA8Gh76crdsive3WJb5v2QNsRJG2eiSPpGOXCckuLmTK1EQo7stkfJKA==", "signatures": [{"sig": "MEQCIGGh4n8DxT/y04aYWZx/+0ZTV5xlJRtoYCOYGCD1Q49GAiBZHYhDp9vVfUl6vRrLvythwNTvOilmSkZZtM4sbiurkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "dependencies": {"type-detect": "0.1.0"}, "devDependencies": {"karma": "0.10.x", "mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "karma-mocha": "*", "simple-assert": "*", "mocha-lcov-reporter": "0.0.1"}}, "0.1.2": {"name": "deep-eql", "version": "0.1.2", "keywords": ["deep equal", "object equal", "testing", "chai util"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@0.1.2", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "b54feed3473a6448fbc198be6a6eca9b95d9c58a", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-0.1.2.tgz", "integrity": "sha512-+0XmfoJvDwuL0pfbGQWwaZoiEBcici3wzGZHcfGKToOmc5su5tUaxSO6fPhNVDfZoM/sCQuFvt7/xgG3XgH5Bg==", "signatures": [{"sig": "MEUCIQD70xSfzty4rvghTpLt8MvRYeqPLigHxJcWqBiE3b+JtwIgf4WhPLKIFResHgtBMF8nC46Hn7jDE+gsxlOSMUyaNfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "dependencies": {"type-detect": "0.1.0"}, "devDependencies": {"karma": "0.10.x", "mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "karma-mocha": "*", "simple-assert": "*", "mocha-lcov-reporter": "0.0.1"}}, "0.1.3": {"name": "deep-eql", "version": "0.1.3", "keywords": ["deep equal", "object equal", "testing", "chai util"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@0.1.3", "maintainers": [{"name": "jake<PERSON>r", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "ef558acab8de25206cd713906d74e56930eb69f2", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-0.1.3.tgz", "integrity": "sha512-6sEotTRGBFiNcqVoeHwnfopbSpi5NbH1VWJmYCVkmxMmaVTT0bUTrNaGyBwhgP4MZL012W/mkzIn3Da+iDYweg==", "signatures": [{"sig": "MEYCIQCocr4cmvlkq6Yo2Tv/lcKI89CdOZmp9ljN7dCeIh9hugIhAJ6VamKaDWkM7idhlGoad8IXOdlXNXjQNO4Rej3+Ppjf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "jake<PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "**************:chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "dependencies": {"type-detect": "0.1.1"}, "devDependencies": {"karma": "0.10.x", "mocha": "*", "component": "*", "coveralls": "2.0.16", "jscoverage": "0.3.7", "karma-mocha": "*", "simple-assert": "*", "mocha-lcov-reporter": "0.0.1"}}, "1.0.0": {"name": "deep-eql", "version": "1.0.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@1.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "3b2ce6cc80645f75fe90af5d7faab39112a15a31", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-1.0.0.tgz", "integrity": "sha512-cEIFHtw/572ZCg8Efpd+l/g1b/FX5qn72BY6WJ5CwF/ni08snwZgR0TJzqgzYJYHNKHDNsaY9Z9aS/r23WOOAw==", "signatures": [{"sig": "MEUCIA5qwaDrn/qsGRY9SNHhnYJisfqYgxpj7bh49z9+CHGJAiEApkXje4+V6q0q1j6ItYLvIa7BeqBOUL59hi4wZ4oiyVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "3b2ce6cc80645f75fe90af5d7faab39112a15a31", "engines": {"node": "*"}, "gitHead": "57944401cb5fc41dcf1588e0c44ffc60bbfe4843", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.47", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^2.4.5", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-1.0.0.tgz_1476037065146_0.8335718684829772", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "deep-eql", "version": "1.0.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@1.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "544eb3359b6710185d442073d60fe1aefdeacfa8", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-1.0.1.tgz", "integrity": "sha512-ADMNKpSBIQqW59z6uAldvRXyVVszbOqnCZWvEQvsnknKLsV1cRiSsp+GCeO3a15hgGz5L1lltYAx4U50Wrg6og==", "signatures": [{"sig": "MEUCIDqWCT1nhKWKI7xwDPH5A1uKvpY5Bs0ca9UnzWdQl8s0AiEAnI9T10Lxf1icB4tk8vV5yoSr9hFDanrDuLDltt3riZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "544eb3359b6710185d442073d60fe1aefdeacfa8", "engines": {"node": "*"}, "gitHead": "841f6f511c88cde8b29d9f2aac311a402cadcf0e", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-1.0.1.tgz_1476825163524_0.2820258445572108", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "deep-eql", "version": "1.0.2", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@1.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "ceeb80d659006af42e0c5610af0b8706bdabc426", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-1.0.2.tgz", "integrity": "sha512-O5w4bSjJOh3zdXWI4AT+hMuYV8Ka/D3+nS/5KNapMHH88GXG/63oRTx/f7vWv9HXqEzaDvyDMQxEq3o2mOHPkw==", "signatures": [{"sig": "MEUCIQDhR9mCyWrEhboppMXLTBu9Iwjh4vUbEhiTrbMrhPMWSwIgVcMcTy4P8Oo2mVMZzZQ6NFnnvgKLzEWlQpivQo44F64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "ceeb80d659006af42e0c5610af0b8706bdabc426", "engines": {"node": "*"}, "gitHead": "248a5b39a63a60feee94dbc4efd395f0ef60325d", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-1.0.2.tgz_1476828606912_0.3189946673810482", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.3": {"name": "deep-eql", "version": "1.0.3", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@1.0.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "8030fe56a2b0e62ebb9217cc23d095c656915b08", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-1.0.3.tgz", "integrity": "sha512-iRhR41rO+0HDhX0ZrjmqlYcPOQMJCOjxKPZbRIScaJGQL3aY6USoqjWpJBNhR8W7RhU5XQdV5fOntNHf4uxmNw==", "signatures": [{"sig": "MEUCIQCMGXAWnD6I62pvbdBXGPyHWOTmVRffZLTNZnJn/b+yRwIgXeMn4yvIkAea5NA+E4G5a8WZ3jcfGAObEBjo7neCtBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "8030fe56a2b0e62ebb9217cc23d095c656915b08", "engines": {"node": "*"}, "gitHead": "41dfd4b82667003f08709b811106aa9dbd0cb6f1", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-1.0.3.tgz_1476837468925_0.8042417075484991", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "deep-eql", "version": "2.0.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@2.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "842dabddf778c5e99b019168cf1142689b490388", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-2.0.0.tgz", "integrity": "sha512-J0c1fR1EYgd/hMCf9P95+pqtIjRI8/ZEsE7/usfKt4bCOIKV5QmO7mdwZkkZycX1Z4d0P8lwWGkVP9Ncoa+1Lw==", "signatures": [{"sig": "MEYCIQC2eh3fd24lA2Fwiq9Gsch9pkQfC7R4bg4IS1sQy/D8XwIhANfw35g35y4LaTetLiSkJIxOKpflBkgfW7Z9bPzwVUlP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "842dabddf778c5e99b019168cf1142689b490388", "engines": {"node": ">=0.12"}, "gitHead": "718922d9f9675281079c2b994215158cd3188c01", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.12.17", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-2.0.0.tgz_1478136787575_0.9432684623170644", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "deep-eql", "version": "2.0.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@2.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "2d5e54d261d24672a2bcedc9b6f1937fba258bc2", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-2.0.1.tgz", "integrity": "sha512-03ZXJy/m9TTh4nIHUvcf2fS9WX2P7nP0apoIaE3VG0Rkd+E9083oUNgf1WPOndSIk5Mu9/0yaNFcz2MbNl71Jw==", "signatures": [{"sig": "MEQCIDCVj6X0WZtD4wvcWh8bnogZuwGIbrac53HtP3YsQhDyAiBObyWk1lNqcqlpO7s9outCx1jTqOHEJmYDum5O8Ntz8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "2d5e54d261d24672a2bcedc9b6f1937fba258bc2", "engines": {"node": ">=0.12"}, "gitHead": "145dd2629e0e1e90e4fabaf0f8b8635ad3015a7a", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_pakcage_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.12.17", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-2.0.1.tgz_1479168409542_0.7753588203340769", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.2": {"name": "deep-eql", "version": "2.0.2", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@2.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "b1bac06e56f0a76777686d50c9feb75c2ed7679a", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-2.0.2.tgz", "integrity": "sha512-uts3fF4HnV1bcNx8K5c9NMjXXKtLOf1obUMq04uEuMaF8i1m0SfugbpDMd59cYfodQcMqeUISvL4Pmx5NZ7lcw==", "signatures": [{"sig": "MEUCIQCNMFUGMoCuDZDzcgnQ2wQeiVghpQSCRQG9ycx0JLeizQIgaqV5VxHunWgnDB18HZSoOWI+nqayttpfCIK8nVZ0pms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "_shasum": "b1bac06e56f0a76777686d50c9feb75c2ed7679a", "engines": {"node": ">=0.12"}, "gitHead": "76963e83adc01725435ea3b40f3aa2421be2a67c", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "0.12.18", "dependencies": {"type-detect": "^3.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-2.0.2.tgz_1493983183860_0.9219055201392621", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "deep-eql", "version": "3.0.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@3.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "b9162a49cf4b54d911425975ac95d03e56448471", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-3.0.0.tgz", "integrity": "sha512-9zef2MtjASSE1Pts2Nm6Yh5MTVdVh+s4Qt/e+jPV6qTBhqTc0WOEaWnLvLKGxky0gwZGmcY6TnUqyCD6fNs5Lg==", "signatures": [{"sig": "MEUCIQC9tlTK8rFseCPvCcjPhMmnp5hppabnzdZuAtsWW5Ud8gIgJRiv0QHDGS07ezH8p0ArYXuavjpWZaJEurUZlqQiHQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=0.12"}, "gitHead": "96ad468e8323de82d9e1c5cc2ac7bdf8fb407ba6", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-3.0.0.tgz_1501965634260_0.6518574329093099", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "deep-eql", "version": "3.0.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@3.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "dfc9404400ad1c8fe023e7da1df1c147c4b444df", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-3.0.1.tgz", "integrity": "sha512-+QeIQyN5ZuO+3Uk5DYh6/1eKO0m0YmJFGNmFHGACpf1ClL1nmlV/p4gNgbl2pJGxgXb4faqo6UE+M5ACEMyVcw==", "signatures": [{"sig": "MEQCIA2Guc+Zr2++fpu1HORRISQsk11kiAUxrDzV8xdJtCwvAiBEz45k8/VAewT+AIlg5CPaJ3gB2t17o0CIetQyvSPPcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": ["index.js", "deep-eql.js"], "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=0.12"}, "gitHead": "04d6da6518f8ddc288638ca42503752028810120", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "5.4.0", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql-3.0.1.tgz_1504732162073_0.5694719541352242", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "deep-eql", "version": "4.0.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "c70af2713a4e18d9c2c1203ff9d11abbd51c8fbd", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-GxJC5MOg2KyQlv6WiUF/VAnMj4MWnYiXo4oLgeptOELVoknyErb4Z8+5F/IM/K4g9/80YzzatxmWcyRwUseH0A==", "signatures": [{"sig": "MEUCIQC0rG7BYmeR2ochg3B0wFlnwHV9od7gYvvEV1P6/7m0GQIgNGlJJET2xLF768WCX77jsNXQ2+Hq+Q8NB0zn6lnbMSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxQkGCRA9TVsSAnZWagAAEpoQAIXPdNzHTKWUPqr7g0oE\nHyZoiN5Q2Kmt33h65Oi468x3xw0oY7KUb5GDVNyxpZ5M9EuAw37hpn65YSuB\n5+zT3ZfonbSxFqiZNrU1U4dvl7Xh2WiW5AxWMu9AQFYzKRAAwb8vYnbtcVJe\n8Ab1OdnYd4BFsMWoDldw3A7baQ4GbldJgHKzkBkSYmbJhIBdYtRG2vQpMaPQ\novrZdzuTWNGf5s3XG4n6b0CWe3+rNN7c0Bctaz+X86oXrCk7l80B45h5u5ij\n83Fo2ROuiXWHWiIS9yGx0DW0fewGuQ17kNLqifkzpIg6upLr76SWfPxjavAK\n4MWAp7cu/lSb2rzsFfXH1RUlIZo2SVpHUCy3OSQT08I0YrF583GeiYWmZnc0\nrUQzmr4lC1srQHd3YEjMH28mYC4fge405doRHNRwz3axJEAFj5WlwTiUuaRe\nSpivkdAVeOioZ/fnEMf0EqerctUv0GDP5rYPjQ8D6dBR6FklWFaLSOyxNpUH\nkvXv+Rw9QgUl660YDrP3qYeOW2tzEY7de8UPspSnkTSfUOrbB08xmNKS28Sp\nqmCnH7GT662Bi6NnZ720FlOxFX5EzdHeRe0uSWdqjhw5V9GHTgHZTdmtVm5K\nwcYAq8JwjnX5KlYqiEegVf7JckZutGdS5k06KEA8aav16HyRqA1/AiIZLLbQ\nXGuK\r\n=b7g6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=6"}, "gitHead": "fa5c6042e5c127df12e1f3ee4b9f9b3e2302864c", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^0.13.22", "kewlr": "^0.3.1", "mocha": "^3.1.2", "eslint": "^2.4.0", "ghooks": "^1.0.1", "istanbul": "^0.4.2", "watchify": "^3.7.0", "benchmark": "^2.1.0", "component": "*", "coveralls": "2.11.8", "browserify": "^13.0.0", "karma-mocha": "^0.2.2", "simple-assert": "^1.0.0", "karma-coverage": "^0.5.5", "lodash.isequal": "^4.4.0", "karma-browserify": "^5.0.2", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "lcov-result-merger": "^1.0.2", "phantomjs-prebuilt": "^2.1.5", "browserify-istanbul": "^1.0.0", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^8.5.0", "karma-sauce-launcher": "^0.3.1", "eslint-plugin-filenames": "^0.2.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.0.0_1539639557672_0.9535407783556269", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "deep-eql", "version": "4.0.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "2b65bc89491d193780c452edee2144a91bb0a445", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-D/Oxqobjr+kxaHsgiQBZq9b6iAWdEj5W/JdJm8deNduAPc9CwXQ3BJJCuEqlrPXcy45iOMkGPZ0T81Dnz7UDCA==", "signatures": [{"sig": "MEUCICF0CmEwRzBvF2PZFghcv1hjfLF1w8pf6XDvE0zv+WK9AiEAteTYOYZa8DskaM2muKx+M9mpP72LCKDFcF2yWeJH4N0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHJcpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxpg//WrpgPNU+eAdZTezqqK012lztzxBJi43duk64E7BUODeOCBvd\r\nS09MDdNSohPzQlH3a0eq6StF4bPLqvz7QyCuCEqlol1ZfS8aoFU1F2nbbjmr\r\nF0OUbExExMj7BUpNLpUQV/sgzCKPZ44kapATWB+c2vdJ3Q0AGkTodKt6mHc0\r\nvBU93J9RnD/QBBtHha+qQsE1ynXnoMz46NNtapOuiBlduR9H/UGFaUg2Tx5N\r\nQT6tDCgoa+Pbf4wjG4wxQksydnxBoeFWlGwKkwYNHFbYfcMHabr7eDWxrlA+\r\nios1iuX5Hm5yna5BDFC//sLR2rTkA95VrjpID+8oUvYikGrw8kALFu/kjMHs\r\nwN5AGIIrXD9PgTYRARUDLkC94TfyggNXq6zNgHYz3GMi4CvyLHQZr6Eb187N\r\ncLr9s51D8lh3yUNu1vsnTgdC79N7kLW4sPuc7HHoJf6Y+mOEB1xdF6u78VwW\r\ncpFr4gUDat2mzXhvs1EemCrQQ4gg4PsQ9SctAxvZprAWQCWE+xeFQTbHUc+b\r\n8jueUefHRKuuS3TurLJgdWfrlsGPr1u45Ge4/Ji7kk1SmbuvfY/Siw2hs09P\r\n23/eN4X6j8UZY3TPj3vQPqVQoPaEoLgba4oVzpHISvBL8pmA52D2WSFKYlyK\r\n1u5ga8lA9MBBjY292v9iqi3YQq4xXlnyKmk=\r\n=nYer\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=6"}, "gitHead": "280b03e567acb900a42ea37fd21599cac47c834c", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "7.24.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "17.4.0", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "ghooks": "^2.0.4", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "semantic-release": "^18.0.0", "lcov-result-merger": "^3.1.0", "browserify-istanbul": "^3.0.1", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.0.1_1646040873546_0.16899081895330603", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "deep-eql", "version": "4.1.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.1.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "67f2078a06d899d9d954762ef61358f2eef00507", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-4YM7QHOMBoVWqGPnp3OPPK7+WCIhUR2OTpahlNQFiyTH3QEeiu9MtBiTAJBkfny4PNhpFbV/jm3lv0iCfb40MA==", "signatures": [{"sig": "MEYCIQDkVukAcpuqSo+6/OFd7Y+xDCiUfEoc5glT6q60URhCygIhAMAQhgTmrFzoh7XW2JxWnyNdS6RZjvq5VJNDPIvgPyqm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuWWQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5jw//SnYmX6kJn73k4SLXQVo0cqCUSl3d22ixlE2qq8oBnvTqtCF+\r\nqcPL9RWzZLi+WgXV6ix+lDJXZfYPGydw2gdpHH7Bye3kheoI+Hlpq2ostMSs\r\nlREAjDSGZxfqYINtz1dxYpuusiZaikezzW3puO4Xsb7BKMF5TlAfk4FsIZe/\r\n4Nl3yjbwb2pTsWZDXOM+X6p1wm9ymzoSVPVZUMu7zRZA5FsHDECD/iZEHtrb\r\nd2SuqJ7PQx6aINV31Gp1bUW1/HcWiQRjT/Uc/QBMQvx1yZaZ+czMeWBrVi48\r\nXsEWTa/NRBsBZkg3hsrbBiHQpQlZMni2WIFkfDk+9kPP0Wsl/mfVnqZguQ3S\r\nIWMHMej1RjTTKW/qFrb6C3Os5P8UEVJPwUYcZnvdXKrk2LD7+M6VxdxttRkj\r\nLfOyV3QFU3wXo21j+88pV7c02WFTsYryZVVzKNmb4ChHYMKejoa5sL1ZTFxp\r\nAvDgmBQJymjlyFKVSGcLW7spXfAaPz1lAB2TZprhC8ORY9O6wxekHTsS4iKW\r\nepg77s4YqHGGSfqjpMcdf2a+lVs8ys1iuKfVbbwVkPlK7FYJui5xBZIWWFV9\r\nhBGp6c5REGMOG/IbB4JYsaEn9bYI6Fpn4GEXMaT8WYdYgnrGrSQbIguIxyBO\r\nk5YdURQ5tSFevh+BGgfu+KTjp7+vn3G7Wnw=\r\n=OhBF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=6"}, "gitHead": "c991032c6bb2c2c593f155e846727d96f8cc21ba", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "7.24.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "ghooks": "^2.0.4", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "semantic-release": "^18.0.0", "lcov-result-merger": "^3.1.0", "browserify-istanbul": "^3.0.1", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.3.6", "@js-temporal/polyfill": "^0.4.1", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.1.0_1656317328425_0.977462398514267", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "deep-eql", "version": "4.1.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.1.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "b1154ea8c95012d9f23f37f4eecfd2ee8e5b9323", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.1.tgz", "fileCount": 5, "integrity": "sha512-rc6HkZswtl+KMi/IODZ8k7C/P37clC2Rf1HYI11GqdbgvggIyHjsU5MdjlTlaP6eu24c0sR3mcW2SqsVZ1sXUw==", "signatures": [{"sig": "MEQCIELGv9+zoAY8IKblfdPJsejFepGc+W1j8Q9/1sJim2xcAiAMXveV4GHKTAFeHtDuTGFQOp4oUFveWnhLQ70G9z06IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKYb/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8URAAoJJOWz8Wbtdf0X7qGJtGTkBC1ayUcLzK9hYKufi+G9F2MsDi\r\ngGorCd1gf/76IgRdLO86AKOVE7675XGyIaPsO3+/gEQ0yfv+XVaN01F/fAh7\r\neF2sAoLzRA4ikcq7gpB6vcpLENG137sVJhx+1+Wo8fxYj++k6tbnI0KGrqQT\r\n2yBEQeTZRq1ncHgmWcX89P5rQYUKB3ErvBzZEQV8gtdzzaTejWFCzwv5Prsp\r\nX4isX/yZVVMi67LrlYBJambdq2kQyjDFiaHK25gR5QKHpaA4Ofx8SoTJSOQA\r\nE/sx4dHm9GkCcCT2gbsBq/BHkMNgWhgt5t7GGkSHSm+QfVKRGJG950pHFB7j\r\n1iJCf68Gfj1Ft9vhqrgzRXwnbRpqWNiYqbWIgEmr7r0+lumrLYfIMNr5ahGN\r\nZMdmDzKOKQcDH26mZK2+0vPSPjmIKsNitwdUsmHCk19sFrrxbMnvQXX3HKxX\r\niOTuiua6f0MHwQ3pWa3oVxPFjuaHcG1Et3b0ULxuuZdCnCmooKCj6XSt9qLs\r\nHn7ysFQcOCRK3f3Kik/FDIcMzeqvP5PzEPlg6s1X1QUSfpUaPeGfc7E36uVK\r\n+4O+2TBK1pwDbaPBHPAUqljKwzlSLN9/Y3Jsfi0LbRKpdX7VjI65mSfesfSU\r\nz354eN7gKCRpSoXfkPZfg21/3eFV5vtjCyA=\r\n=lbG1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "engines": {"node": ">=6"}, "gitHead": "9586dfbd2bfd9bbe7ee3c657c8e1f3f07c9ef6c3", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "7.24.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "17.9.1", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "ghooks": "^2.0.4", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "semantic-release": "^18.0.0", "lcov-result-merger": "^3.1.0", "browserify-istanbul": "^3.0.1", "validate-commit-msg": "^2.3.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.3.6", "@js-temporal/polyfill": "^0.4.1", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.1.1_1663665918999_0.8795261924244724", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "deep-eql", "version": "4.1.2", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.1.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "270ceb902f87724077e6f6449aed81463f42fc1c", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.2.tgz", "fileCount": 5, "integrity": "sha512-gT18+YW4CcW/DBNTwAmqTtkJh7f9qqScu2qFVlx7kCoeY9tlBu9cUcr7+I+Z/noG8INehS3xQgLpTtd/QUTn4w==", "signatures": [{"sig": "MEYCIQDIPtT4NWtqWWsO+WXbDADDvwgja3AvlGH9YXHbXcXUmQIhAP2BPAVvwia5tr6jVyhRNxqX28D+pSSZwnd3SshvePtk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaNMkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7+w//TrJiVMiMHkrQr8/JjXXNu+WxKJrqYB2DQWWvUjtnjaRbFFHx\r\n/ckGRT7Or9x3phkMOjO+d46OWY+wx56BOtAVKrI6DaLbHTEcrUSd1COVeo7n\r\nEPMzVUIOU0Ls98lUwsZo8g54uScTb597hfSR3NcEcr3zdyKwVUawxVTG6spI\r\nrJU4JxX0s3gSdaIi825g3rQO+uXPSBOtJa3ssE8+TriV36NYZsMIJKQwuzzj\r\nzRyDGXsRb2tevfGXxfz4Jipuh98KiZA3TqFPAImb5p6+6ODDR861V7vDhLxX\r\nqcTUKctcXgajCko54ChlbwjqBDYJ7VYdrINnjELzRbBEPNskHZWgK30aDqZT\r\nFEUo6U77eumbJBnV7gx5sl025gByuXqB+rBCJAfFSYO2J4uV+BflC2FubEnT\r\ndBIhmA3Di2FjAMxIigVJWaFUqXi2EZmnAayhAyavZjUOS9hmozxhJ69KQv/g\r\nCP2lNlJfI2LCmjSoCVD3G7PEw1WNcDxZZGMQyB0gmn+lafv0ZwynEWW0cyJI\r\n0Q0RDzKWYqS5ZCQD0NPvDsA9Rcu5i9s6SPa9gjKH5ZX9cRZw/XZmuGwqkHlm\r\n6+WgAw9r56Maus4J0q2aFRIPYSD88uNR5NfpoRsjEzdm7eq7Xj6wTI6K9ubF\r\nRl2g/DPt6/WEyB0FhifJEBBwIARba4NJePs=\r\n=uWB5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "engines": {"node": ">=6"}, "gitHead": "474b76d714fa94e8c78ce85501a029fb8c489a1e", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "lcov-result-merger": "^1.0.2", "browserify-istanbul": "^3.0.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.1.4", "@js-temporal/polyfill": "^0.4.1", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.1.2_1667814179866_0.7852468908366892", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "deep-eql", "version": "4.1.3", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.1.3", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "chai", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "7c7775513092f7df98d8df9996dd085eb668cc6d", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.3.tgz", "fileCount": 5, "integrity": "sha512-WaEtAOpRA1MQ0eohqZjpGD8zdI0Ovsm8mmFhaDN8dvDZzyoUMcYDnf5Y6iu7HTXxf8JDS23qWa4a+hKCDyOPzw==", "signatures": [{"sig": "MEYCIQCTp3DFmJ7MHdkb1BKaj4wMXBKFqmmd0V0hTK/oP6uxUAIhAPF6I/jB++302UCcbPlyI8MEuzGdTYOFw/UWmagW5au5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjja7zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8cxAAgnJyoZ1esZ/Pj3EQ1nAiew3kxo+PfTIOAFhn0fWUNerCF9Q6\r\nHA6Db0BEJ1zpmj7y/E/utVZFyf/2+MquyOyYO63nERpPperf7Bqt4oM1Ti0T\r\nUDFrYL4Gu3kbMt0zaxXz1KRgrHUpGBhZanQ1IYUy2kkZmAfsD8ex26dizV7d\r\nJ2qwtM3MSE3nWZ5AX89yX1rjE3vQAeoLB70R5VRi1AE4ew4A/asjUHaCaMht\r\nvFl4cRW4g457Qemkf9b9+DNhm24mzZ5R+xSNeEvFrztemgv2CilkTECwdzbq\r\nAlxFFL7TXIknj4ZkBlM9dkNUjbkUppnJJ0JZGTdsNv0FAwwcOL/7zM8Fu8PN\r\noukURFeR6ExKELTfVn6BXGC6bvgsl8pgXMZ3Z3x7xGvsOHaflFEZzDVrNxjh\r\nTIkRuz5wZx1sQhfBHkrm61j2y9pE0IgWP+JH8uEH8/bxZLWbyrBpvlo8JWCO\r\nEgd39rh9LDYZEAi5TF2Ct2Ivuszeyg0nssaGdZRlPdW/eXpm6heao2NA1plg\r\nEMwxN73RnVwJ0xh8TXlO9ESJxycmt4HhXx8cYwDdmzn8hKNhmVLKyJt4iY9Y\r\naqIZDa8VLq5o8TvT4TRj2jPbR0QIEPfXbmKyYuce6LLdLBUkrfMBK0iHFIFd\r\nioZp6ozPq47EzvxeOA6pcKKCodNMEF87IcI=\r\n=P5zf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "engines": {"node": ">=6"}, "gitHead": "0e417c7b98d927175a9e25e583014d25ca4a5044", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "lcov-result-merger": "^1.0.2", "browserify-istanbul": "^3.0.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.1.4", "@js-temporal/polyfill": "^0.4.1", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.1.3_1670229746968_0.15335099791964812", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "deep-eql", "version": "5.0.0", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@5.0.0", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "d2b1e34cd6956edc2ece7408f805168ee34213a2", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-dnTmE7uWLOa189EaS2rc2/3UU1MnmP3yW5hcsyc0vUhpGZuiHGTfcYrZ8yv3UHWzEHNXsAyLTh/SPxEXjn4V3g==", "signatures": [{"sig": "MEQCIBayiWVIYhT+77m148C9SQDWGubbk9V/1JxQO4FUFaR3AiA/5qhCXUJXtFUqOyRIduim3lArFFO9RuyArZVnX0S1nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23947}, "main": "./index.js", "type": "module", "engines": {"node": ">=6"}, "gitHead": "b8c99546f7a29d755d7e7e00c10b7a5f651fd15f", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "watch": "web-test-runner --watch", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "test:browser": "web-test-runner", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"], "parserOptions": {"sourceType": "module", "ecmaVersion": 2015}}, "_hasShrinkwrap": false, "devDependencies": {"kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "simple-assert": "^1.0.0", "lodash.isequal": "^4.4.0", "@web/test-runner": "^0.16.1", "lcov-result-merger": "^1.0.2", "eslint-config-strict": "^14.0.1", "@js-temporal/polyfill": "^0.4.3", "@rollup/plugin-commonjs": "^24.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_5.0.0_1697634608690_0.6386784686577913", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "deep-eql", "version": "5.0.1", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@5.0.1", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}, {"name": "keithamus", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "21ea2c0d561a4d08cdd99c417ac584e0fb121385", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.1.tgz", "fileCount": 4, "integrity": "sha512-nwQCf6ne2gez3o1MxWifqkciwt0zhl0LO1/UwVu4uMBuPmflWM4oQ70XMqHqnBJA+nhzncaqL9HVL6KkHJ28lw==", "signatures": [{"sig": "MEYCIQCsyya7jbjPkppFN7r/f2dCzAh514k9F8LC/x+IU96D6AIhAJ16MhIMHgA/o21lapFWNwVHP1i2aanHLrjOGwt5tW6v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23758}, "main": "./index.js", "type": "module", "engines": {"node": ">=6"}, "gitHead": "4e914befac38c01a8606b2bb86932dad1b0cb0d0", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "watch": "web-test-runner --watch", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "test:browser": "web-test-runner", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.18.2", "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"], "parserOptions": {"sourceType": "module", "ecmaVersion": 2015}}, "_hasShrinkwrap": false, "devDependencies": {"kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "simple-assert": "^2.0.0", "lodash.isequal": "^4.4.0", "@web/test-runner": "^0.16.1", "lcov-result-merger": "^1.0.2", "eslint-config-strict": "^14.0.1", "@js-temporal/polyfill": "^0.4.3", "@rollup/plugin-commonjs": "^24.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_5.0.1_1698232258876_0.8503458174913858", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "deep-eql", "version": "5.0.2", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@5.0.2", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "4b756d8d770a9257300825d52a2c2cff99c3a341", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.2.tgz", "fileCount": 4, "integrity": "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==", "signatures": [{"sig": "MEUCIQDYEYVNgPth5EuCzzXaxPq++HW5MQ3oE6+HwaqYl3DNiwIgRs4TyaA6qBIeuhqYi9Dv3KESUyMv4cD+0zNyAut3Plg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23888}, "main": "./index.js", "type": "module", "engines": {"node": ">=6"}, "gitHead": "8c140402693a23991e1d604a7a5a100a1795a190", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "watch": "web-test-runner --watch", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "test:browser": "web-test-runner", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.20.3", "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"], "parserOptions": {"sourceType": "module", "ecmaVersion": 2015}}, "_hasShrinkwrap": false, "devDependencies": {"kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "simple-assert": "^2.0.0", "lodash.isequal": "^4.4.0", "@web/test-runner": "^0.16.1", "lcov-result-merger": "^1.0.2", "eslint-config-strict": "^14.0.1", "@js-temporal/polyfill": "^0.4.3", "@rollup/plugin-commonjs": "^24.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_5.0.2_1717401796512_0.08283840636162298", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "deep-eql", "version": "4.1.4", "keywords": ["chai util", "deep equal", "object equal", "testing"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "deep-eql@4.1.4", "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "dist": {"shasum": "d0d3912865911bb8fac5afb4e3acfa6a28dc72b7", "tarball": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.4.tgz", "fileCount": 5, "integrity": "sha512-SUwdGfqdKOwxCPeVYjwSyRpJ7Z+fhpwIAtmCUdZIWZ/YP5R9WAsyuSgpLVDi9bjWoN2LXHNss/dk3urXtdQxGg==", "signatures": [{"sig": "MEYCIQCOwZ5AljKwPOFJd4QgaIzSgjayRg9bnUme04c5SKKthQIhAPjxkNq2NsYJT/dIJvx20/HFYwAhA3csPryo/3nVuMWE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24368}, "main": "./index", "engines": {"node": ">=6"}, "gitHead": "377458dfab1b7c0ab1775228205cf94d71584619", "scripts": {"lint": "eslint --ignore-path .gitignore .", "test": "npm run test:node && npm run test:browser", "bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "watch": "karma start --auto-watch --singleRun=false", "pretest": "npm run lint", "test:node": "istanbul cover _mocha", "prepublish": "npm run build", "test:browser": "karma start --singleRun=true", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "chai<PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Improved deep equality testing for Node.js and the browser.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"type-detect": "^4.0.0"}, "eslintConfig": {"rules": {"complexity": 0, "spaced-comment": 0, "no-underscore-dangle": 0, "no-use-before-define": 0}, "extends": ["strict/es5"]}, "_hasShrinkwrap": false, "devDependencies": {"karma": "^6.3.4", "kewlr": "^0.4.1", "mocha": "^9.1.1", "eslint": "^7.32.0", "istanbul": "^0.4.2", "benchmark": "^2.1.0", "coveralls": "^3.1.1", "browserify": "^17.0.0", "karma-mocha": "^2.0.1", "simple-assert": "^1.0.0", "karma-coverage": "^2.0.3", "lodash.isequal": "^4.4.0", "karma-browserify": "^8.1.0", "lcov-result-merger": "^1.0.2", "browserify-istanbul": "^3.0.1", "eslint-config-strict": "^14.0.1", "karma-sauce-launcher": "^4.1.4", "@js-temporal/polyfill": "^0.4.1", "karma-chrome-launcher": "^3.1.0", "eslint-plugin-filenames": "^1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/deep-eql_4.1.4_1717599702026_0.8464817320275702", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-09-18T15:48:42.366Z", "modified": "2024-06-05T15:04:41.621Z", "0.1.0": "2013-09-18T15:48:44.262Z", "0.1.1": "2013-09-18T16:05:30.471Z", "0.1.2": "2013-09-18T16:10:10.940Z", "0.1.3": "2013-10-10T10:39:17.348Z", "1.0.0": "2016-10-09T18:17:46.877Z", "1.0.1": "2016-10-18T21:12:45.251Z", "1.0.2": "2016-10-18T22:10:07.464Z", "1.0.3": "2016-10-19T00:37:49.534Z", "2.0.0": "2016-11-03T01:33:09.496Z", "2.0.1": "2016-11-15T00:06:51.565Z", "2.0.2": "2017-05-05T11:19:45.296Z", "3.0.0": "2017-08-05T20:40:35.807Z", "3.0.1": "2017-09-06T21:09:23.075Z", "4.0.0": "2018-10-15T21:39:17.819Z", "4.0.1": "2022-02-28T09:34:33.664Z", "4.1.0": "2022-06-27T08:08:48.621Z", "4.1.1": "2022-09-20T09:25:19.153Z", "4.1.2": "2022-11-07T09:43:00.023Z", "4.1.3": "2022-12-05T08:42:27.118Z", "5.0.0": "2023-10-18T13:10:08.984Z", "5.0.1": "2023-10-25T11:10:59.091Z", "5.0.2": "2024-06-03T08:03:16.644Z", "4.1.4": "2024-06-05T15:01:42.184Z"}, "maintainers": [{"name": "chai<PERSON>s", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/chaijs/deep-eql.git", "type": "git"}, "keywords": ["chai util", "deep equal", "object equal", "testing"], "license": "MIT", "homepage": "https://github.com/chaijs/deep-eql#readme", "bugs": {"url": "https://github.com/chaijs/deep-eql/issues"}, "readme": "<h1 align=center>\n  <a href=\"http://chaijs.com\" title=\"Chai Documentation\">\n    <img alt=\"deep-eql\" src=\"https://raw.githubusercontent.com/chaijs/deep-eql/main/deep-eql-logo.svg\"/>\n  </a>\n</h1>\n\n<p align=center>\n  Improved deep equality testing for <a href=\"http://nodejs.org/\">node</a> and the browser.\n</p>\n\n<p align=center>\n  <a href=\"https://github.com/chaijs/deep-eql/actions\">\n    <img\n      alt=\"build:?\"\n      src=\"https://github.com/chaijs/deep-eql/workflows/Build/badge.svg\"\n    />\n  </a><a href=\"https://coveralls.io/r/chaijs/deep-eql\">\n    <img\n      alt=\"coverage:?\"\n      src=\"https://img.shields.io/coveralls/chaijs/deep-eql/master.svg?style=flat-square\"\n    />\n  </a><a href=\"https://www.npmjs.com/packages/deep-eql\">\n    <img\n      alt=\"dependencies:?\"\n      src=\"https://img.shields.io/npm/dm/deep-eql.svg?style=flat-square\"\n    />\n  </a><a href=\"\">\n    <img\n      alt=\"devDependencies:?\"\n      src=\"https://img.shields.io/david/chaijs/deep-eql.svg?style=flat-square\"\n    />\n  </a>\n  <br>\n  <a href=\"https://chai-slack.herokuapp.com/\">\n    <img\n      alt=\"Join the Slack chat\"\n      src=\"https://img.shields.io/badge/slack-join%20chat-E2206F.svg?style=flat-square\"\n    />\n  </a>\n  <a href=\"https://gitter.im/chaijs/deep-eql\">\n    <img\n      alt=\"Join the Gitter chat\"\n      src=\"https://img.shields.io/badge/gitter-join%20chat-D0104D.svg?style=flat-square\"\n    />\n  </a>\n</p>\n\n## What is Deep-Eql?\n\nDeep Eql is a module which you can use to determine if two objects are \"deeply\" equal - that is, rather than having referential equality (`a === b`), this module checks an object's keys recursively, until it finds primitives to check for referential equality. For more on equality in JavaScript, read [the comparison operators article on mdn](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Comparison_Operators).\n\nAs an example, take the following:\n\n```js\n1 === 1 // These are primitives, they hold the same reference - they are strictly equal\n1 == '1' // These are two different primitives, through type coercion they hold the same value - they are loosely equal\n{ a: 1 } !== { a: 1 } // These are two different objects, they hold different references and so are not strictly equal - even though they hold the same values inside\n{ a: 1 } != { a: 1 } // They have the same type, meaning loose equality performs the same check as strict equality - they are still not equal.\n\nvar deepEql = require(\"deep-eql\");\ndeepEql({ a: 1 }, { a: 1 }) === true // deepEql can determine that they share the same keys and those keys share the same values, therefore they are deeply equal!\n```\n\n## Installation\n\n### Node.js\n\n`deep-eql` is available on [npm](http://npmjs.org).\n\n    $ npm install deep-eql\n\n## Usage\n\nThe primary export of `deep-eql` is function that can be given two objects to compare. It will always return a boolean which can be used to determine if two objects are deeply equal.\n\n### Rules\n\n- Strict equality for non-traversable nodes according to [`Object.is`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is):\n  - `eql(NaN, NaN).should.be.true;`\n  - `eql(-0, +0).should.be.false;`\n- All own and inherited enumerable properties are considered:\n  - `eql(Object.create({ foo: { a: 1 } }), Object.create({ foo: { a: 1 } })).should.be.true;`\n  - `eql(Object.create({ foo: { a: 1 } }), Object.create({ foo: { a: 2 } })).should.be.false;`\n- When comparing `Error` objects, only `name`, `message`, and `code` properties are considered, regardless of enumerability:\n  - `eql(Error('foo'), Error('foo')).should.be.true;`\n  - `eql(Error('foo'), Error('bar')).should.be.false;`\n  - `eql(Error('foo'), TypeError('foo')).should.be.false;`\n  - `eql(Object.assign(Error('foo'), { code: 42 }), Object.assign(Error('foo'), { code: 42 })).should.be.true;`\n  - `eql(Object.assign(Error('foo'), { code: 42 }), Object.assign(Error('foo'), { code: 13 })).should.be.false;`\n  - `eql(Object.assign(Error('foo'), { otherProp: 42 }), Object.assign(Error('foo'), { otherProp: 13 })).should.be.true;`\n- Arguments are not Arrays:\n  - `eql([], arguments).should.be.false;`\n  - `eql([], Array.prototype.slice.call(arguments)).should.be.true;`\n", "readmeFilename": "README.md", "users": {"pana": true, "mrzmmr": true, "takonyc": true, "erikvold": true, "justjavac": true, "flumpus-dev": true, "dpjayasekara": true}, "contributors": [{"url": "https://github.com/keithamus", "name": "<PERSON>"}, {"url": "https://github.com/dougluce", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/flowlo", "name": "<PERSON><PERSON><PERSON>"}]}