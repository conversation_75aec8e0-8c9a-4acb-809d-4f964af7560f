{"_id": "@csstools/css-tokenizer", "_rev": "23-ee162f2a878317448995f919ef7830ec", "name": "@csstools/css-tokenizer", "dist-tags": {"latest": "3.0.4"}, "versions": {"1.0.0": {"name": "@csstools/css-tokenizer", "version": "1.0.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@1.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "161c0c9b632952ee8c2f0a62eb479d736a5627ff", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-1.0.0.tgz", "fileCount": 37, "integrity": "sha512-xdFjdQ+zqqkOsmee+kYRieZD9Cqh4hr01YBQ2/8NtTkMMxbtRX18MC50LX6cMrtaLryqmIdZHN9e16/l0QqnQw==", "signatures": [{"sig": "MEQCID5ZiIWg74fOMC/2BXDOt/mGuAScaO1ti1Fyo0QqP5nXAiBmJN48SlmH39WIyrnUdH/MjU/SPrNSuZ4CPSVIhp4eew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcg4wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7tg//eyX3YYAPTgLq5WBDI+LUIykogvD+au+ZeWnRzOnYdbf4YAXD\r\nWiJs4bPZJAOFbl1Z94CkjnnRGfgJpbPIJ+gPBet1llpOsyQaKiV58NMdDM/8\r\nB2lXfYSMSXfTJGXGYzfxry/mVDTqIBSsxFDrT2isCy7mp2NBqwoaGhd8I+B+\r\nxv4mV1HoigCqSxNTVj9rL6EsGF31CYDWFGBXeTrLU89HP2I6qr0qtWUUVMCP\r\nOirkrxO/DTqFaayoyySBt1+jWBdlpgJVv/g9LK6gQT5MiudJhfNkOBIaXYFz\r\nwdfjzWEfpfTH5cVzHxYRIov0bTggK1P/iadht9QqdFI4aG3hppWPOzNcQSId\r\nU38dnzyzxvkhtzshmFVUV+SN9NXo4e2Uqiy4Kv3kjy6oLqObBCe9fgkQhW/1\r\n0Eepb8VipVo3R29SuyxkOJ9HQ6qedYf1zYJcMy5L6aJ3O4UakgoZjhlfGwmn\r\n8HWzr0hfQJBgMf1k7RwejaNECp86eIghC7CF6kgLkUYpatw7NBhxU3Psh30f\r\nn0m2/a6FkhOuEUQdwCgIepRPfh8UljCa9pyInRJtuv+zxUR9UBeGX/TwXf3Y\r\nunaMQvU2fUP6Zj+wnW6/lUi8O39fhByJzpnj8PJhdb2/Xe/thI0QkWnxxDwB\r\nNJHKUd4fGyEOMFa4Mf4lu728lvzy7vc/s1w=\r\n=6wM1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "3ef58e2e9369f40e3f60f405b7dd82edc0dd3e8e", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "stryker": "stryker run --logLevel error", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "8.11.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_1.0.0_1668419120669_0.22570465744154888", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@csstools/css-tokenizer", "version": "2.0.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "fa2a7e8f4ed965e73ba30ee80c00fa64980fd11e", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.0.tgz", "fileCount": 36, "integrity": "sha512-IB6EFP0Hc/YEz1sJVD47oFqJP6TXMB+OW1jXSYnOk5g+6wpk2/zkuBa0gm5edIMM9nVUZ3hF0xCBnyFbK5OIyg==", "signatures": [{"sig": "MEUCIEzDOMJDn7xV5jExdVOTtSrdubABZIFmKMsU7m5WF4CjAiEA6hSmRx/rNJUtEadKp112GGbD2B6pF2JMfXnrXxMX8tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyY/9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKbhAAlC6tQsL1LSjKkhqPqz6dmQXXth74fhp7Lqvvul2FzE5f1zEF\r\neVjYi66vh/jpxtPUSaH77XwDyXWaaHHwFpfFZ3GBa+bLKG3YCR/xYo2xTDsf\r\nfgTeagPQVCeEyIZ8nY56+zZ/73ss/ONQTTs6Ep3XSQ/N6iVSl9/7jQ8TBhxy\r\nIeqxN9NtPNpUxVNOvOHKWHXenPAqfm7n/hJU44yhJE1OrsGuXTVLGir8Y3J3\r\ncCsB0pj30b8DemKCa+Df3elhxaAi6231rxWaRHNLTuL0sscSPyaD+t5dTMeM\r\n6uVYBEaRQ6CNChkbz1HfZ8AlfmEps7opw+Z3pY/WrJqoY+Sy81nwUDzeAlrA\r\nnL7IXDTUvbGcJ3MlfTJZeMJwCoZkYeKiyTZ89CUmKJ3CEsVSCeAmadVl6/He\r\nUMJeGEdh6ozd7mc0oOp5vmY9CNqOGSPJVr1kvBy941vx7bu0eR2m8K2imweO\r\nio19daKXmp4Ob7QIfMku1BbSU4HKYi4/TP68cGhQ2R5IoJsNiLbsdtwFj/dS\r\n/j3uh2ZxVdP64EaPQOqDeOCgTHSok3ssKYWVatZpCwhh+2L5lKkwpGk+/Q8Y\r\nSmBNipCFj4iXLrCMAlqMasFQST271L4+MCvj0ZBqSOJDQdw7jz2knT0vTHl2\r\nFTglhhmNp0Zn60C3n9U+Jl6eB/AgdtJVLHA=\r\n=SFgR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "1f649f67cc178de33066bbe944fb50d36cd8b8d9", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "8.1.2", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.0.0_1674153981279_0.34750593989936185", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@csstools/css-tokenizer", "version": "2.0.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.0.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "cb1e11752db57e69d9aa0e84c3105a25845d4055", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.1.tgz", "fileCount": 36, "integrity": "sha512-sYD3H7ReR88S/4+V5VbKiBEUJF4FqvG+8aNJkxqoPAnbhFziDG22IDZc4+h+xA63SfgM+h15lq5OnLeCxQ9nPA==", "signatures": [{"sig": "MEUCIH4ugb7TJvpBF0Kc0F18VHsHjEyUaEAqmkcFkV1xHKQHAiEAykfRgoDDPhn9ivLXya4BiUzZ93j5Q2uNtYgXY2bAYss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1MzxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMvA//V1lI2XnSrj5EZm/AM/zD3h/orvOKPQEeAlWFiNyvbvLquIjm\r\nV7/QiWOBOqBEw5ZxoGlUik5EyOBhSnUL7WrLhM/hCbgmqwcQl96wyJZLCC+n\r\nGKkguDPZsbHd3Bwq2Z/Tl1ow+YddwHAWrEvQ+1i5FJz8IIfACuWROle+n+Cv\r\nbEMR0ItlEi/9cpx7osv5kqdalNyZLIRW3Tb9lehXkW0b+MM1iB9lN0fafda+\r\nekFS0iFpmNSVbHPMilrdii996RjDipOrJGOc2mEfLS0ObD72oaWOflohdwQa\r\nz/34xMny61bhIbL/++1vmN3atqIJgeoCzLcTZ32H501gIsCQMDWMUhWGYyny\r\ndtpq8D0xqIgz4PeWLspb5mawbeU7y62Aar9CnJM+5p5BTuOOXrwnEIdZEwcO\r\n2R7w9iwhGgDzJ2v5JBMS3VOZQAedbmHdtf8uXKSmZBZwUrCQOeXxWJc5IrW5\r\naS4Dnbjq7jOKqn2nmSybRw1kc84HqYwF9huz+J5rCfn0XggzK9qZsWEmj5gK\r\ns8y8QHzqWtKVmN+mHQygonsYGJuBRoRhXMkWyhXDxj7OnE+nh87CliX8Qyeu\r\nwLYGTZPlqND0LwcoYu/VkXLZJkjY7b+WeKoyFxf2eg7tI5ZVuWHK+BPDlThn\r\nofEJxqgkadR4z9WcOvawiOkIp29P9jTgcbE=\r\n=y7Xm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "141671243a19d739ad03293d56e31929accedd30", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "8.18.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.0.1_1674890480758_0.6633005764001174", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@csstools/css-tokenizer", "version": "2.0.2", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.0.2", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "3635560ffc8f1994295d7ce3482e14f956d3f9e1", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.2.tgz", "fileCount": 36, "integrity": "sha512-prUTipz0NZH7Lc5wyBUy93NFy3QYDMVEQgSeZzNdpMbKRd6V2bgRFyJ+O0S0Dw0MXWuE/H9WXlJk3kzMZRHZ/g==", "signatures": [{"sig": "MEUCIQDLSLGKKxvAiN2bEbNcfYlVS0U/HQbAo33RYK6AxEl9fwIgbF4DfOwHx05XixBpS1ioBh7bmgA/7TR57eDddlbxlIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6kgMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/lQ/+NjSriE+YuDYWucqHA8oUmAnpDEbFw34cFPGmvis5pP/7RYPY\r\nB3MsAXMtmG3nXNjfbPWpYVdJpp4iEQG87fnl0GEaxGRO8R5Ov9ey536fOd7C\r\n98fwtn63NVmjYDZ25l60/C8vigtKOXSmRhz4to5DjvwBhvVDZZcYQILAwUnk\r\n58a+1ytKrNeaBBJ2JTzD01D1Vinud/LEebDNIDPYmFMnf/Sciat9aZMzXSmU\r\ncWC6CVYLx8ed4NdpwxyseE1Rv9KujKIE8MdweAgwlA2XVmt9iQzW9CI4nx72\r\n0gidFZqZ4gM7qvGWFP1409UqZXHiXqD9AHSlIpli02BCp8uFPEo0jg9h5J5e\r\n1zt9TO8myUHdamat2/GY09fJFJb9Hp4r8KBtJhzKnGlT8aQziEaLmcBdpK4X\r\nu389LSHSeK/p6iZdzHjUwgJRl8ppFiUigZ+ZlEidDAHx4UOewLV6dC7a8jk8\r\naHlVeYcc2MffWN3UH3JwcqrCYCniG5qxY29oIpsL0nS0G+ewdBUpaCZ44SpP\r\neiX3BAtMCo4POS0b6ijZ6aDm710A4bQnBnGCa3vTDxCNrFJlrYbIaDcDsBAP\r\ntVNJWsPuAULRtvPfT6+/tQrNHL7AvMscjgSpo385PWtijk2jC2EuMY8hUorQ\r\nb2j+gtLDdUB/GDWjuZHn/GwyyCXk5flukoY=\r\n=q5z8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "718c02d64202b0539734295aff5a811a3348bfc9", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "9.4.1", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.0.2_1676298252480_0.7539872623501664", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@csstools/css-tokenizer", "version": "2.1.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.1.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "fee4de3d444db3ce9007f3af6474af8ba3e4b930", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.1.0.tgz", "fileCount": 36, "integrity": "sha512-dtqFyoJBHUxGi9zPZdpCKP1xk8tq6KPHJ/NY4qWXiYo6IcSGwzk3L8x2XzZbbyOyBs9xQARoGveU2AsgLj6D2A==", "signatures": [{"sig": "MEQCICzQs9DPxZlq2IMNX/Pgvd7EyNq3Qzi9wQ0UpqX5Xkx/AiBxju0Q1YWChMd5B/SJlQ1yjGNF2fIkNNdSErTuq7721w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9K5AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZNQ//SUaeDSmR3Mq0JG1D9qsqJucnIksfsDvak9po+ky4ATeirCbu\r\nIMPfINQbKu+eBSf5A1x/+n+/LvWvOBxH5Ok2vNzsEoJJtVjfFop6PhTp7SK9\r\nuYofU9UOrK1dIb87zDpJRL6Nu66tNZ6sVXZEsFJjBi0SI2c6iLL1as63CC4/\r\n2DU9lgydHUsDXUk5B7MgfBi5GVrWM3zZsNXVOv6aFAVXVGnpFl73UrgwKlff\r\n/u8gYCpSGazLoWmePdqQJDmLVF74kCzeFz8YprwNTdBDLKfLCu1bgUKd8cxK\r\nE6ijHayYyi0755i26zKR5mHhfN7iR9PvY5NxgFZWyQy/jjzOVX/0aSciXvwO\r\n+WrOUUuJ4GsMivqBAdc3/3H/CYz9xYGcRIaP+GtdpFPi4pP+hnCrzl+3Zizb\r\nm6IJwbBmL3pRwz4A9eA8l/3NKE+mKi6lrswyTm8E4o8WJ/+Ed7o0PIBB6iaL\r\npXI675JsrkUjp/jdG22vm67H8eQDJ9z7nsxX0IF0+RxuWhYdj83Q7Sv9dud1\r\nm3LVOyjtLxhWk6ecMrffrNgjiDHUhbbzQ3gKLw+MXpsOFUTXMNihbKWFvyd6\r\nhPnHWFzm6012Q7L8PVVNnb8KzRziEEB3ThSjU0sERC83SM4jhFfxxDiQ7/S7\r\nsfYluxfqC8bN+yLws5zQt14/xAmFbYafrmA=\r\n=kVSN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "e160e818d876b6d110453945746f040e2de40b86", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports && node ./test/test.mjs", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "benchmark": "node ./test/benchmark.mjs", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "9.4.1", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.1.0_1676979776471_0.5076008035207611", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@csstools/css-tokenizer", "version": "2.1.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.1.1", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "07ae11a0a06365d7ec686549db7b729bc036528e", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.1.1.tgz", "fileCount": 36, "integrity": "sha512-GbrTj2Z8MCTUv+52GE0RbFGM527xuXZ0Xa5g0Z+YN573uveS4G0qi6WNOMyz3yrFM/jaILTTwJ0+umx81EzqfA==", "signatures": [{"sig": "MEUCIQCKmjpKpzyiqktnctSDBD3N2bRlOMbXDrSG6bXq5Npp+wIgM3szBmfNYAk1cCtqjW2GUlsxTDVeyMdA8MLCubSgAo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+liACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodAg/+JccYYWHKEd3GXr121iC4zOFhXvn0PnD6cw8EB5oDZMcj0C56\r\ns2YAlE6AJmrKe05jgE9ghjVS/ERf0+932+RevLYbnelKbnzzhAUqi0DnECsC\r\na2n7M0Pf9Y9YU1wBQQ6COROAbpG4ccfA7YFYPBXRYt1EEytStIQn+SK6HNQY\r\n7QwbqxjZnsq1+3MvRfCKC1JYBjx0f8U2ucIikPnw9yKmG7ptVIDO3WxDInRe\r\nIuLsaWLvC0h+rIzceFYzfl61Cd/nkFJY2eqi0m/Ysg+ImumqNVqU8+0TTe+p\r\n17Kf10UZONvkiyemDFena9/4xiHOhHKH9yQm6qPSv/V7YJAFFV7na3YuAk9E\r\nr/ICJTW7k0qTU4BrGRGD0HQ0uxYte0M0BNA89CAtlITVCd56z/uuGaFpGoKW\r\nw9ubsZcZjh86FZygNjWhk13Yt6NDuYHxGdnnPekgA0vYWT81S3bEl0kcqqkA\r\nVJ83F0W5lILV4qPt0VlkcsmyNlDkyPJtJLS/ARDVMwEYIaLqfjk7doWtLkvM\r\nnMVjip6XVtWN7EIddYlKBF5AwxZICwflsDnwDetYkFMp+e90/chICO+EoGf8\r\n7jnz7ZDxdGZiJBY9t/mYDz4JI/K7S8fBMU+DwaiEqJWT59FaCuk4vl8trDVz\r\nnDAMtg0CMj7iHmFWHWYmcuHw1O5xmxXxsKY=\r\n=tXxq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "3ebd978ef0176b039bd41a9576d250f260e191db", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "benchmark": "node ./test/benchmark.mjs", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "9.4.1", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.1.1_1681123681960_0.46796077143323944", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "@csstools/css-tokenizer", "version": "2.2.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.2.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9d70e6dcbe94e44c7400a2929928db35c4de32b5", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.0.tgz", "fileCount": 38, "integrity": "sha512-wErmsWCbsmig8sQKkM6pFhr/oPha1bHfvxsUY5CYSQxwyhA9Ulrs8EqCgClhg4Tgg2XapVstGqSVcz0xOYizZA==", "signatures": [{"sig": "MEUCIG2An+y9urFv8ZpZSizSYjHs3TN3aosLmOke5U1rcuwOAiEA5rn0LeV6AsdIgdG0jfx2+pky1jKaE15PqonX2au2R9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65075}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "f9b14f5c4a9c2a578620fe579f198a4a26cd2efe", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "benchmark": "node ./test/benchmark.mjs", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "9.6.7", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.6.0", "@rmenke/css-tokenizer-tests": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.2.0_1690215458135_0.09081176458292828", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "@csstools/css-tokenizer", "version": "2.2.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.2.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9dc431c9a5f61087af626e41ac2a79cce7bb253d", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.1.tgz", "fileCount": 38, "integrity": "sha512-Zmsf2f/CaEPWEVgw29odOj+WEVoiJy9s9NOv5GgNY9mZ1CZ7394By6wONrONrTsnNDv6F9hR02nvFihrGVGHBg==", "signatures": [{"sig": "MEUCIQC3KOGqstk9Tr0UOUs16ly3m74IFxVf7yA54gotOEzLvwIgL4j+EAnz23L6v9K5ubE2jAS4goQXj+VKSlgKbyLrttA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64840}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "40be1de549473ac67dacdea71a6a3e8f7ad0931e", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "stryker": "stryker run --logLevel error", "benchmark": "node ./test/benchmark.mjs", "prepublishOnly": "npm run build && npm run test"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "9.8.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "devDependencies": {"postcss-parser-tests": "^8.8.0", "@rmenke/css-tokenizer-tests": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.2.1_1695584608780_0.4755734275622858", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "@csstools/css-tokenizer", "version": "2.2.2", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.2.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "bcd85cef4468c356833b21e96d38b940c9760605", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.2.tgz", "fileCount": 7, "integrity": "sha512-wCDUe/MAw7npAHFLyW3QjSyLA66S5QFaV1jIXlNQvdJ8RzXDSgALa49eWcUO6P55ARQaz0TsDdAgdRgkXFYY8g==", "signatures": [{"sig": "MEUCIQCQ3OjKCL2kGzSHoVJtR1WoHGr20pCkgxzsW3eBNCBqIQIgYvQszjYTWcvHNpO7F8XWtbyQpH9dT3AXVCcaPYidNMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54910}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "08d135d46f20ffb5781818a459c30a3a24679fd7", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.2.3", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.2.2_1702682090028_0.5716182144746753", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "@csstools/css-tokenizer", "version": "2.2.3", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.2.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "b099d543ea57b64f495915a095ead583866c50c6", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.3.tgz", "fileCount": 7, "integrity": "sha512-pp//EvZ9dUmGuGtG1p+n17gTHEOqu9jO+FiCUjNN3BDmyhdA2Jq9QsVeR7K8/2QCK17HSsioPlTW9ZkzoWb3Lg==", "signatures": [{"sig": "MEQCIEonlh71iVUO9wgh0rgnnNvDpXWqcEcJkaYCyswyLcBvAiAwIhow1kGw55Y2mwyMk6/3PNMpPT7EgijrYQxj3zGaig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61637}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "273ac9d1c682c55cc40832551961040270fef3a1", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.2.3", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.2.3_1704040169424_0.6530625843149771", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "@csstools/css-tokenizer", "version": "2.2.4", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.2.4", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a4b8718ed7fcd2dcd555de16b31ca59ad4b96a06", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.4.tgz", "fileCount": 7, "integrity": "sha512-PuWRAewQLbDhGeTvFuq2oClaSCKPIBmHyIobCV39JHRYN0byDcUWJl5baPeNUcqrjtdMNqFooE0FGl31I3JOqw==", "signatures": [{"sig": "MEUCIH55RHPvLGudR8s3RkuSxx/Vvu2BB0XxOa/tnRaERhmhAiEAqcMUHVfd1i4vZzICFbi3zRkOBCWkafQJ+NTzPGIw+rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61659}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "47811c2094407c6eb45620c70ec1b20e924a8d25", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.2.4", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.2.4_1710355531557_0.4992895821749088", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "@csstools/css-tokenizer", "version": "2.3.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.3.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "8f08b190fa696ed038bb3652510926ee7c6233d1", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.0.tgz", "fileCount": 7, "integrity": "sha512-v1WxYzvjjv5XwrHhSV/xRQFrToAX5FmRcYCAMzRD+ZTdG7962mf3JJXju7vPodWcuk2fJTmL+xbviMG8IxccHQ==", "signatures": [{"sig": "MEQCIAFTBuIo5fTmLo72Q+/w4j3GinXA5jYZ5d7TQrsibiYZAiBvv7XkPv6FCC2L6wO7dCNG0PZNIT31tkCzdHXfONJXYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71041}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "588bd5706137f7c404daa538c67896c3af74d253", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.5.1", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.3.0_1714838732510_0.9681874237195038", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "@csstools/css-tokenizer", "version": "2.3.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.3.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "3d47e101ad48d815a4bdce8159fb5764f087f17a", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.1.tgz", "fileCount": 7, "integrity": "sha512-iMNHTyxLbBlWIfGtabT157LH9DUx9X8+Y3oymFEuMj8HNc+rpE3dPFGFgHjpKfjeFDjLjYIAIhXPGvS2lKxL9g==", "signatures": [{"sig": "MEUCIQDu9uBcwvScyXsKxGBrEQcsN7Zwvs+9q9TzRVCZwEotKwIgCk23SyqRHwTdLTm5u5zMoaTge5W1puDRljDQr9KR68I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71103}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "e1f0b8ea881e52969ea56cff7b93197c7aab9926", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.5.1", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.3.1_1714857157948_0.030044838132827545", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "@csstools/css-tokenizer", "version": "2.3.2", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.3.2", "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "4050fc549921b26212d9fd51afca2231b40e867b", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.2.tgz", "fileCount": 7, "integrity": "sha512-0xYOf4pQpAaE6Sm2Q0x3p25oRukzWQ/O8hWVvhIt9Iv98/uu053u2CGm/g3kJ+P0vOYTAYzoU8Evq2pg9ZPXtw==", "signatures": [{"sig": "MEUCIQCP3l3p3wtcqXYyyvKRhluOsEPcG05mYHUUeJd6k+T17gIgHjPnPPAQ0LqkbHVRLI8+Av/y2dy1h1s1KLKOafVKor8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71104}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "151af6d2b513660f5fd6a32d5680c4c51acb930b", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.3.2_1719698262714_0.04380892639863854", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "@csstools/css-tokenizer", "version": "2.3.3", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.3.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "628a9dd388c9688fb81d4c2dd25b62b776109a60", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.3.tgz", "fileCount": 7, "integrity": "sha512-fTaF0vRcXVJ4cmwg8nHofydDjitKMDBzC8cCu+O/Lg13C4PdkC15GVjGpbmWauOOnhomVSTg5I5LpLJFJE2Hfw==", "signatures": [{"sig": "MEUCIFLpS/X3IXxFTOYkc9NmvRg55G/ikVWqnohm1XZHhzeLAiEA6OofWDeSaK/r+8C1BueZBdHwzmtIMy3FbWQlw0s4AIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71304}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "c36ab367d0ee2a68c771fcf49b60c64ee5aafe6f", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.3.3_1720016530550_0.6447810856672649", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "@csstools/css-tokenizer", "version": "2.4.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.4.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "ab65a0ac9256ef5214461389d92dbcb183134ceb", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-E3uoN77rNPJNuKsOJlPRs9Uy5Qv+gOB1E5xUOhLJtGktZ7+PaVFI6JMAM0Jd1VxJMOX5Y1EWfYRMK0U7t4w/NQ==", "signatures": [{"sig": "MEUCIDnKIGnI3aVKG+V/nrh4ukGMx/uTXY+VEhEd8kd/HwCyAiEA+cmcSORNgbVEhhFns6XO2LD8khAiXaRgfJAFwueS9/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74094}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "d98a7ab5080254209a6a411f8058af61117492c4", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.4.0_1720181083694_0.04459055962888092", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "@csstools/css-tokenizer", "version": "2.4.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@2.4.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "1d8b2e200197cf5f35ceb07ca2dade31f3a00ae8", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-eQ9DIktFJBhGjioABJRtUucoWR2mwllurfnM8LuNGAqX3ViZXaUchqk+1s7jjtkFiT9ySdACsFEA3etErkALUg==", "signatures": [{"sig": "MEQCIG/Th+ji9H3IXcB2qeTwt4qbGb+UK6F/QB5au127V3GyAiBomeddKznIlsvnBHSXZMHdExdr97QqeACWzGyVFi9KPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74071}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "2cce14d9522d782d8100f430fee9b5662b37baad", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_2.4.1_1720181855742_0.09664806182805186", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@csstools/css-tokenizer", "version": "3.0.0", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@3.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9a8a1ea564dd92b8b567e0507c29f74252e01e04", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-efZvfJyYrqH9hPCKtOBywlTsCXnEzAI9sLHFzUsDpBb+1bQ+bxJnwL9V2bRKv9w4cpIp75yxGeZRaVKoMQnsEg==", "signatures": [{"sig": "MEUCIQDrxfHpeLUv+tnA/UVGndcWOdATpkmUZGpDSyyqBQ6AtQIgU46lewp64+WURY6Vf3En2fW3mYQeXnoOcBNhEgUn+E4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72177}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "81aecf68fa16b6e9c48d33912dac2267fbf6aa2b", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_3.0.0_1722720427283_0.7328146434177472", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "@csstools/css-tokenizer", "version": "3.0.1", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@3.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "9dd9b10084f3011290f96789598091e5bcb3c29a", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-UBqaiu7kU0lfvaP982/o3khfXccVlHPWp0/vwwiIgDF0GmqqqxoiXC/6FCjlS9u92f7CoEz6nXKQnrn1kIAkOw==", "signatures": [{"sig": "MEYCIQC2eET5oswzC5lH0Augt1c2AcK68ee8ZdtKhVSRP5EhMgIhAOTWk3DLrK6KigGFY1fhFJW9P++67hvVRu8NjT6/T4Tv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72132}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "368ef2deb41ac508f91fa2cd869528a34cc1d326", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_3.0.1_1723989040607_0.48806876156156376", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "@csstools/css-tokenizer", "version": "3.0.2", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@3.0.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "1c1d7298f6a7b3db94afe53d949b9a7d6a8ebc57", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-IuTRcD53WHsXPCZ6W7ubfGqReTJ9Ra0yRRFmXYP/Re8hFYYfoIYIK4080X5luslVLWimhIeFq0hj09urVMQzTw==", "signatures": [{"sig": "MEUCIFtQW4tVUyPBK5eWDxh7QnNCVPRSzr8QUX2oTf1AZ9e8AiEAvsRDNFih8rHKvnpB9WO/iwfLrgDK2LtMORjftujk0ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71974}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "93fe3a1ca9351cd0d8ef402fd20def909a6b354a", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_3.0.2_1728563078352_0.431244776689266", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "@csstools/css-tokenizer", "version": "3.0.3", "keywords": ["css", "tokenizer"], "license": "MIT", "_id": "@csstools/css-tokenizer@3.0.3", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a5502c8539265fecbd873c1e395a890339f119c2", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-UJnjoFsmxfKUdNYdWgOB0mWUypuLvAfQPH1+pyvRJs6euowbFkFC6P13w1l8mJyi3vxYMxc9kld5jZEGRQs6bw==", "signatures": [{"sig": "MEYCIQDiHUUYhin6YGV2OuoP1yZGIX9slx9yQsF5n6RaUH5UPgIhAKr/fPXefbTT5dVb5J7PhwALErPKEHjqy71rKKXAEAwg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72786}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "8dc765a0c58350f2d9fd6199be7386a688dd2ff6", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/css-tokenizer"}, "_npmVersion": "10.7.0", "description": "Tokenize CSS", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/css-tokenizer_3.0.3_1729894947230_0.04986614944290757", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "@csstools/css-tokenizer", "description": "Tokenize CSS", "version": "3.0.4", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-tokenizer"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "keywords": ["css", "tokenizer"], "_id": "@csstools/css-tokenizer@3.0.4", "gitHead": "a3e4776ae125d52acc5cfb9b250a7625c968c7fd", "types": "./dist/index.d.ts", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw==", "shasum": "333fedabc3fd1a8e5d0100013731cf19e6a8c5d3", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz", "fileCount": 7, "unpackedSize": 72983, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFkpVTh3FbkNTVDT3KwRK60S4gYKgJRVFZrh55pPCtXkAiBCxpLqbeT3U51ScJsjJ0WlfYkvYEkgk+PU0peV97oDTw=="}]}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/css-tokenizer_3.0.4_1748342729754_0.3692601672993916"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-11-14T09:45:20.590Z", "modified": "2025-05-27T10:45:30.124Z", "1.0.0": "2022-11-14T09:45:20.857Z", "2.0.0": "2023-01-19T18:46:21.526Z", "2.0.1": "2023-01-28T07:21:20.974Z", "2.0.2": "2023-02-13T14:24:12.649Z", "2.1.0": "2023-02-21T11:42:56.628Z", "2.1.1": "2023-04-10T10:48:02.169Z", "2.2.0": "2023-07-24T16:17:38.302Z", "2.2.1": "2023-09-24T19:43:28.998Z", "2.2.2": "2023-12-15T23:14:50.250Z", "2.2.3": "2023-12-31T16:29:29.593Z", "2.2.4": "2024-03-13T18:45:31.718Z", "2.3.0": "2024-05-04T16:05:32.682Z", "2.3.1": "2024-05-04T21:12:38.105Z", "2.3.2": "2024-06-29T21:57:42.890Z", "2.3.3": "2024-07-03T14:22:10.672Z", "2.4.0": "2024-07-05T12:04:43.861Z", "2.4.1": "2024-07-05T12:17:35.960Z", "3.0.0": "2024-08-03T21:27:07.479Z", "3.0.1": "2024-08-18T13:50:40.785Z", "3.0.2": "2024-10-10T12:24:38.682Z", "3.0.3": "2024-10-25T22:22:27.438Z", "3.0.4": "2025-05-27T10:45:29.932Z"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "license": "MIT", "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer#readme", "keywords": ["css", "tokenizer"], "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-tokenizer"}, "description": "Tokenize CSS", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# CSS Tokenizer <img src=\"https://cssdb.org/images/css.svg\" alt=\"for CSS\" width=\"90\" height=\"90\" align=\"right\">\n\n[<img alt=\"npm version\" src=\"https://img.shields.io/npm/v/@csstools/css-tokenizer.svg\" height=\"20\">][npm-url]\n[<img alt=\"Build Status\" src=\"https://github.com/csstools/postcss-plugins/actions/workflows/test.yml/badge.svg?branch=main\" height=\"20\">][cli-url]\n[<img alt=\"Discord\" src=\"https://shields.io/badge/Discord-5865F2?logo=discord&logoColor=white\">][discord]\n\nImplemented from : https://www.w3.org/TR/2021/CRD-css-syntax-3-20211224/\n\n## API\n\n[Read the API docs](./docs/css-tokenizer.md)\n\n## Usage\n\nAdd [CSS Tokenizer] to your project:\n\n```bash\nnpm install @csstools/css-tokenizer --save-dev\n```\n\n```js\nimport { tokenizer, TokenType } from '@csstools/css-tokenizer';\n\nconst myCSS = `@media only screen and (min-width: 768rem) {\n\t.foo {\n\t\tcontent: 'Some content!' !important;\n\t}\n}\n`;\n\nconst t = tokenizer({\n\tcss: myCSS,\n});\n\nwhile (true) {\n\tconst token = t.nextToken();\n\tif (token[0] === TokenType.EOF) {\n\t\tbreak;\n\t}\n\n\tconsole.log(token);\n}\n```\n\nOr use the `tokenize` helper function:\n\n```js\nimport { tokenize } from '@csstools/css-tokenizer';\n\nconst myCSS =  `@media only screen and (min-width: 768rem) {\n\t.foo {\n\t\tcontent: 'Some content!' !important;\n\t}\n}\n`;\n\nconst tokens = tokenize({\n\tcss: myCSS,\n});\n\nconsole.log(tokens);\n```\n\n### Options\n\n```ts\n{\n\tonParseError?: (error: ParseError) => void\n}\n```\n\n#### `onParseError`\n\nThe tokenizer is forgiving and won't stop when a parse error is encountered.\n\nTo receive parsing error information you can set a callback.\n\n```js\nimport { tokenizer, TokenType } from '@csstools/css-tokenizer';\n\nconst t = tokenizer({\n\tcss: '\\\\',\n}, { onParseError: (err) => console.warn(err) });\n\nwhile (true) {\n\tconst token = t.nextToken();\n\tif (token[0] === TokenType.EOF) {\n\t\tbreak;\n\t}\n}\n```\n\nParser errors will try to inform you where in the tokenizer logic the error happened.\nThis tells you what kind of error occurred.\n\n## Goals and non-goals\n\nThings this package aims to be:\n- specification compliant CSS tokenizer\n- a reliable low level package to be used in CSS parsers\n\nWhat it is not:\n- opinionated\n- fast\n- small\n\n[cli-url]: https://github.com/csstools/postcss-plugins/actions/workflows/test.yml?query=workflow/test\n[discord]: https://discord.gg/bUadyRwkJS\n[npm-url]: https://www.npmjs.com/package/@csstools/css-tokenizer\n\n[CSS Tokenizer]: https://github.com/csstools/postcss-plugins/tree/main/packages/css-tokenizer\n", "readmeFilename": "README.md"}