{"_id": "@babel/helper-wrap-function", "_rev": "129-338657dcd631ff15422dc7d4e42a01f9", "name": "@babel/helper-wrap-function", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.4", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b86ac1ea24e79f1703f3ccdbf20be84fb863109a", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.4.tgz", "integrity": "sha512-SDGdyQJwREHSI2O3LlEP+7701z+ntz53TdWC76lgXzPwuv1BmxVDLxn8TG6vYw7SHY1eSJqPTKA1Mw2biSmiCQ==", "signatures": [{"sig": "MEUCIQDaqw313p9UD6kFdiLRncR9WpNt/VZ51HGUI8WhhAW3HQIgLtfYsBJt0cnID+AOU2m7k6U0W/GiwG2zNiFjchFFRwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/traverse": "7.0.0-beta.4", "@babel/helper-function-name": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.4.tgz_1509388579180_0.9470404665917158", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.5", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d112785a59ea172126cd88bbeb24bf8d72876118", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.5.tgz", "integrity": "sha512-cNU20zd3+QRWpuNfq4uI6J7AT0XGPpQNbgc1vwDDGCQdsqqwPPUDaagTiyOiD2qJKualIHGa+m4GoTocue1wvQ==", "signatures": [{"sig": "MEYCIQCCjdvZHbNjLK2v8+wgeHfxf/D8w5+Y366hH7lPkiFXjQIhAKdCYuhINyJMNXfAOz1VSbXhzfq/jkj4Wvokj3iUMGPl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/traverse": "7.0.0-beta.5", "@babel/helper-function-name": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.5.tgz_1509397075832_0.719653429929167", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.31", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "86b39a952250ce2cf930449e312a0a941a81e7d4", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.31.tgz", "integrity": "sha512-d5KaBKBNaLKbHFAIQmzPQIrFJN2cFb3L8fgN85CYI7G7viBZCLH80QotepqpLUUYyO5CPJkegvx0JXx5Qmsbwg==", "signatures": [{"sig": "MEQCIAcWv4JmLTMh7KHl06bDFH3BwCVsTNaFp0L7FNc3RnWKAiA/IqLYU00BYR0B9sE81q9qf7F+I4lzSGVyelnTSjCXKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/traverse": "7.0.0-beta.31", "@babel/helper-function-name": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.31.tgz_1509739470822_0.9344100244343281", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.32", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d4e2bec8849d4afd6450a26314f6a03d3b2918fa", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.32.tgz", "integrity": "sha512-iyGV+eUxmA20yNEbMm67/0EEaIRGv7g6R06Q4P4PTBye6v6RB6DEOWLw7FiUcl2o+C8VmksDcRtTfeqEe5I1kQ==", "signatures": [{"sig": "MEUCIQC6+kto9gdmpZHLGc1BLaIPYH1TKbdc0eoxjF1r5bey0AIgPKYQDpn6jb9VurUrWScrQd+HxQHgKAuTqurXr+p1lzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.32", "@babel/template": "7.0.0-beta.32", "@babel/traverse": "7.0.0-beta.32", "@babel/helper-function-name": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.32.tgz_1510493641344_0.8954907529987395", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.33", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a275e8ed7fe4053c01b4e3bfa7c8d776fbbc8a01", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.33.tgz", "integrity": "sha512-U2s/l0DYiFFHgauU43iyrFzKU0bi5QE7nq61T+2AOTXfJFpmD2dmyvJI9BbMaNw3E7aMMYBKSvjNNEGut9KqhQ==", "signatures": [{"sig": "MEYCIQCul/xhJcVkomRy3BBN411vcrRypcY40cRRekgktX4wCwIhANT0ytVpOWfObeHb8NaO3Z2WCH856cilSViddKxArZxp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.33", "@babel/template": "7.0.0-beta.33", "@babel/traverse": "7.0.0-beta.33", "@babel/helper-function-name": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.33.tgz_1512138557819_0.19819519063457847", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.34", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "dd17517c73c3ded126d295d848af93aa3ca8f316", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.34.tgz", "integrity": "sha512-hXE7ob10HNumhoVMHyBH9skoL6LTTkP/GUTMd8MB93KwXyezqvM/2dfIzGySI5YzfVqjg4Rij1jhfhV8wPAg/Q==", "signatures": [{"sig": "MEYCIQD2431+YcljBsd0sm2Mv4MbjwXeABt/PBpiWs+RuTblHQIhALHsNg4aT/dqySyVT/8Or+RvxdqAcPHVDpT4X7Emz+MX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.34", "@babel/template": "7.0.0-beta.34", "@babel/traverse": "7.0.0-beta.34", "@babel/helper-function-name": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.34.tgz_1512225616721_0.67206159979105", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.35", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2fb82e91842e0bbdc67fc60babf156b6fa781239", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.35.tgz", "integrity": "sha512-Pp3/4agQAldsoX9WtntyVq9yWOl3xuNxlMnqXCspYaJeBlvs4+oX1D3AvTS0ogG2mBMGwJm6wYbI3s8a8IYF8g==", "signatures": [{"sig": "MEYCIQCuXp3EQbekCMCBQQBJvp9Wp/seY/jKvhduU/v3I8369QIhALd1C1Py7dYREhmQ4kgy7phkmh+GSqC21xymiX8hV6QX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.35", "@babel/template": "7.0.0-beta.35", "@babel/traverse": "7.0.0-beta.35", "@babel/helper-function-name": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.35.tgz_1513288107011_0.4555666777305305", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.36", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "775073e4a60d4c01569a9950cb88b02bf359cbdd", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.36.tgz", "integrity": "sha512-vSE9XaqXbgodc+r+mVBMj/XRiXYgVSdCo7ntt5dAJ31/6UCKRkHZFn732/7o4kPT2jdE3tWAmy0KHEpcTYNQ6g==", "signatures": [{"sig": "MEUCIQC0wfsOjD72f5uWPJ3BLVLvqNewQkvcMdrV2+wx1j5P8wIgBrM814Y5/XCbbfvv4oRsp6yFLzCSOFj+SS+BQ6JmMhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.36", "@babel/template": "7.0.0-beta.36", "@babel/traverse": "7.0.0-beta.36", "@babel/helper-function-name": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.36.tgz_1514228737951_0.12865077052265406", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.37", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2a20694049099ea6a1762db46ee3817bd1f63256", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.37.tgz", "integrity": "sha512-xcpc8ccHx5+FfUIKJ87+XTLBdH0UXhMzgpnnyD6k7rvsjWqcmtM2EplRiwNLjXfFRHKd49oSThv8EE2/fqKUOw==", "signatures": [{"sig": "MEQCIB1zmHOtCiWkvOw1OdwPSVTVvHWOFYRD7AOUlGj+xcejAiBXR7sZGg5DOZJfEeUJV5y4hAwOJiOv+ce8XKsIyouAxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.37", "@babel/template": "7.0.0-beta.37", "@babel/traverse": "7.0.0-beta.37", "@babel/helper-function-name": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.37.tgz_1515427422041_0.20266656717285514", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.38", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "91605a09a193cf6939489f7fc066a353876ee506", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.38.tgz", "integrity": "sha512-JQY2Hc/NCuVukBavW2mA7GqWZlpScXpurmviG+hKAvxpci5ZdsmocgwOAKWpO+u18OyI0PrZ5lUTGnjlaq0j9A==", "signatures": [{"sig": "MEQCICi4EhnCd1IAFA+4rkgp0TSWA7B3I/Ixg1DFpXyF4WIWAiBmRiwNGG6eW6oHR4/9/TpzxD/2ItlJT9fFpf2zmIChdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.38", "@babel/template": "7.0.0-beta.38", "@babel/traverse": "7.0.0-beta.38", "@babel/helper-function-name": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.38.tgz_1516206760925_0.1953359383624047", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.39", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ef4e6ef66791276351b6609545394900552b35c9", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.39.tgz", "integrity": "sha512-nYG7S7CK3Yq8AVtz8iYuo/DAfqmS3EHt/hOU3maU7aVtPyQBCZz6kIMAcOcvnjVViNPTbadNt4rtyUblo4+FVg==", "signatures": [{"sig": "MEQCIBVMxYS6BF2JWhn/jr/x4OetMUmbvc+uTUaXJHO5PcVKAiBnQaN5GL/8D31m+7EcBlYLqQNZXIyuDBPGvSMIXjtOKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.39", "@babel/template": "7.0.0-beta.39", "@babel/traverse": "7.0.0-beta.39", "@babel/helper-function-name": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function-7.0.0-beta.39.tgz_1517344124784_0.5434241418261081", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.40", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4db4630cdaf4fd47fa2c45b5b7a9ecc33ff3f2be", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-VBXE/uGQuZC9IaYufa3eCoT5ZqcCPv21Uhs/vo4ZqNRDX5QemYEkgDj5SmV2p73bhC66jDwUHaQHKQIiW7ExxA==", "signatures": [{"sig": "MEUCIH7Zxr27OeKSfJM/AGGfc89FUCVuj45viU7Jm7FvfyT7AiEA+70EiCpmCcA6WP4igmeNOJ3GBA30iilS//BTe5+eXLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4187}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.40", "@babel/template": "7.0.0-beta.40", "@babel/traverse": "7.0.0-beta.40", "@babel/helper-function-name": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.40_1518453765294_0.27723242260980885", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.41", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3f10b9f23de4fa9619af2ce65f666a037ee0fd43", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-UONLUYYLyllU9jpZcZhW8ui0lIJx2aH8QqfQ54mRREeYaHpC8A8axyvewXFbWcUHGWeIMkftXuL7Yjd5adNnDQ==", "signatures": [{"sig": "MEUCIQCjtABD6HqGfJGRI0y+dn6IoeU60pJvXzaW74UltQBMxgIgPk86JuOD4EtlEIBVT5gZOZmcPs5CC39otzBxt2vaSu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4187}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.41", "@babel/template": "7.0.0-beta.41", "@babel/traverse": "7.0.0-beta.41", "@babel/helper-function-name": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.41_1521044814326_0.7308245340234989", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.42", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5ffc6576902aa2a10fe6666e063bd45029c36db3", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-jpZDbZROEw2HfmlImLXDB7BFoyo6M/Wn8jOOc1+JfCpg2uaZ+n6Q0C3sA6mCN6o7ZgpJkgT7IHQwdB3RMV6KLA==", "signatures": [{"sig": "MEQCIHETbP1X0AToOWBZesTQi4hnGzqRv58lwrJPGI/aNnbjAiBEEdKPHN7/NTTQDs5GOFhJPDEa7SdSDIa8vQ04dh/VDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4187}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.42", "@babel/template": "7.0.0-beta.42", "@babel/traverse": "7.0.0-beta.42", "@babel/helper-function-name": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.42_1521147127369_0.15149309391638366", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.43", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "37aa889550144ea7b7f5dbdc4a74c843bb06069a", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-Dp5j0l/MZg+4yy4mnYTl6T6ufxnDT17HN3K3JCXsbVTN47UyGB4j/kOsDtsUNkYClNVZF3guTfao7UT/216eJQ==", "signatures": [{"sig": "MEUCIENl4PPIz5WKti9kTSm2hgpRACFgFb7PGhD1Fz61TlcSAiEAiPvZKWlmb5+fzaR9U3Q2zsWe/jEC0mGREeGUow7sniM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4527}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.43", "@babel/template": "7.0.0-beta.43", "@babel/traverse": "7.0.0-beta.43", "@babel/helper-function-name": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.43_1522687737132_0.4199225390260477", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.44", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d128718a543f313264dff7cb386957e3e465c95d", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-qCdMAdMzDhO87r7yS2adqzIl2N9FJaVkPYq6bKllkNcmHquytve+hd/jD/lruD71i3JWkH+M352U+lhW2qkToA==", "signatures": [{"sig": "MEUCID836ZVB7VhsWeCLrt9oPyZRUiSVUZPLQlLQI1S18vnFAiEA/hYh3P1jPZMhvj8dhUTk7kCsVJ8OulzkOSx67Hx8ctQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.44", "@babel/template": "7.0.0-beta.44", "@babel/traverse": "7.0.0-beta.44", "@babel/helper-function-name": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.44_1522707637761_0.8115006221873469", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.45", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f84d46b29eb0358aae84b715978c938c2f39e478", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-Xy4/4XIEcjhCl1RAc+5niwElXgSCjsi/ARtW1cSflSStCx2H/PypFnSncRi0WLXxR5AerQ3QPqFcdwNrQTmyZA==", "signatures": [{"sig": "MEUCIQDDkG1nDpJgb+sY2R21rFh/GgEWeIpK17JMRzvH6OslNQIgAVbZ9hK80q792EEZ4LBnUCzqaiFbHCnm7YJSNZQWAcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T3GCRA9TVsSAnZWagAALAMP/3rKMgfyaBJvfneLVCp1\nTi/1qbqEwSYdLbapdgMIrMAiazgqvNNxMybkstPoUZuGppo6b0Yua71QaEjG\nBBAeAHo7P9qTvKpqL2fqROMZDS4oGPU9ZsmEX+7y6WG/yq37PfOfzF6rJutw\nzwDz5zc/r736zlBwvVx1qe9yk0nhBfTi8v2VOW6mXvzdTO64SNkrtwIbBj3v\nOGQh4B66R6gNGGjRHNPFXjJxN6yPLIDQclZZPxUaTArhot5scWeDGsj73HLP\nCKfqDvj8g2eU4kQyCDC1llajganKs1DXX59gTDq4Z12Y9yF1Cn0Cqutx0kRk\ntXwRDiVLs+Xm5Ov0rNMXXoe6ZH5gm8WY0HCh/ixpqVryaa5hkFi58mqjxsYF\n/rPJYlKB//cPkVBVCaTc1YFBzuAVcJ/4f9QGNI21V/FeShBVAS165Z1LFOJo\nahlkN9rrcpWWBXTtkJNzYl0VjQP2sfqgUQ0M/YGIkyQegrQ/LA5vGuZjq+b0\nb08VedFZrexl6XZI29SYtTqvOAixp/I/DdhFWl30Lh1CntZbtTDQNZ2SKM9N\nw1VTu8INcqIE/aDylHH1l/2+pkVmhvRFRW6y0g0waYBe12mnzQZwizwlybWx\n0rQN0imtiLCX04qvkgre17MHH/jMO+aLqIT1eJCEN5vBBjVhVbSMYbRz1NzX\nt5OS\r\n=FBcD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.45", "@babel/template": "7.0.0-beta.45", "@babel/traverse": "7.0.0-beta.45", "@babel/helper-function-name": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.45_1524448709316_0.18964601786645674", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.46", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d0fb836516d8a38ab80df1b434e4b76015be9035", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-W87M4bP6veTKK66OjzV/rU47tjsWmKj9J0J5BDmxq5BIJB1M13ouQ2FAURa4jGHwjPFWN3D5njBrsrifSOHzbQ==", "signatures": [{"sig": "MEUCIBtx7frkz0rhQYUSMK9bq+cdqRELU6L+II46KE/PsONEAiEAyHtk+SCaWgd3ro3yaCq9zu7nH8PFnJ7NElwvj1/3jLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHyCRA9TVsSAnZWagAA/UEP/Rxu/MizSruXG++8Iuu1\nv57cB1b8NbQv0m2AdCVwILCpNEHwyJ3UqK4vLIj6ezM715uesGwLwaYldqMq\nW1jgcylDDC0tVfH6vgroOdcxdbmr71S4Q1gENFx+EBGV4qKUHMvMmcA1JKh+\njHRrIgHz5zA3TQ5nMaPk2xSKKPExaDd35MpvFf4kUBnR+oXxPa9Zv/D/Q+BX\nA+6oB5NXziiGCM4oElbBoFe5O3SiejhjzUL063sEIubWuDmjAeWSqd+GiHVM\nXUaTtP4JqU3rrCuCV2JIeTSjYTDfCz71T80K60LwG/zEFkQl93p42s+ZDtOG\nwasz2FJDT3wEQ4JU/EB2ny2KoeTJNZkVU0br+WlI0iFTA49c8nW/wgJPOk15\n2Ni/BiiXbW21H2Mk+hAMUmnPGo0g3Nph2L6iFcPQGlQHvbis17AYOZMsTUDE\nvosD+27BNuw0yZSoItCNFXpZeBdQex/Mlr+HviqYPICrny4cdmLEkDKsAX8R\nBHVyHBkIEr4JJ9ovdGl21VTl2sc2QSl6NEdrLZJsRm5X8yqCkmBNd4Evqgww\nABHz1Je5zinYUWOPVFMPKuwHBNj+cyeOOJWnvAqM+4OEdxF7UWoyrd7QEwE1\nb3k8cR5g1zGXFWyuArWtK1tysXJkV70s/A+yGv30mDxhWjOTcf3n5QPgsEzt\nWI9o\r\n=LY7W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/types": "7.0.0-beta.46", "@babel/template": "7.0.0-beta.46", "@babel/traverse": "7.0.0-beta.46", "@babel/helper-function-name": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.46_1524457970074_0.6763302483355711", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.47", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6528b44a3ccb4f3aeeb79add0a88192f7eb81161", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-SAasvh80Mz5q9x15dqH6z8jpM0WTBmxQSNZATSwJwhmWdme6r2gxpufIMr8LwQIJHmXmgNLmvh0zdWSbE/PR4Q==", "signatures": [{"sig": "MEYCIQD6NhPkqliCkS4+RV1eIOD+IAkn9igUZuEiR96JwoRhQQIhAJpl9Wnl76oDtJ2kMnUWqv+l5imPlnji/hCArY05pNa7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icwCRA9TVsSAnZWagAA2XcP/3QAIrUIQVL8uIuiAKH7\nw78t5OlMuWPv6px8Uh/FESYBDMZKrQrneU58LjXy9FL86DZnFIyK1iampQTg\nrxiVzY0PYC9Ia88XTIKMf8z6FFQYe5PgHaJf4OyTKyYK+5HyhTelOJw3ZT3g\njuu0uwOozMt/ozxXj/n7FoOIYfGNyUglyIULlSvSpqzFcHG7v5otyZjEOk55\ni2vJPD2DxN6S+PP7uaNf51VUcKVpH8RPBGM0h6XGBynwNGqvdY+J6KsKPieU\nwGqqdf4418Ltw2AtlCXyYUR2Nx6bALsIJ5wiARQPY4hg12M9/9iCpNnZgDTc\ndx70o6e5aX0uCJ2w1y2ISOfCG58SIStdfKhORKjkvrmXhbWDG616cOh5zy1B\nLx7Q4bibpLlktlte1f/cNNTfXznaeLEQmJlJenNaWf/TCAoZJM5ek9l25/OI\nLufbg15PIw8ZnjCzKkofz5wNIJ7rs8USkLLewmDu6+n0g2tBMcdOmsVhMOz4\nQQXz/eLl7eL66jLw1lh5vXkcsNd4btxkdMnIsPqR1AyzTC7BPCEQh2FWded2\nM876PMjc3Q5cnzmFG3317+CA9D7d2nmVmy2kz8s0fcRGvOtSFoDnE+kojICC\nMdu/FxIPzoa+II1b4HMKml5XeaKcVzQUS1FBsJUK9q71Nv7tRr+KCNh+0adj\ndr7k\r\n=vyVL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.47", "@babel/template": "7.0.0-beta.47", "@babel/traverse": "7.0.0-beta.47", "@babel/helper-function-name": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.47_1526343472278_0.11805058041520766", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.48", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bdb1d7ff2c4f18afec9efec7e33bb255e1668537", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-eQhuwPxmQNUyThNIrc+c/NRRZP9UFT9JQ4jqAZYp+WtGuz3AsMqLxfaxYaeXLWGGDYELr4UTsResuXmPORWj1Q==", "signatures": [{"sig": "MEUCIFSOhWBqBxgck8ZEGf19ZzAmO6mrYD8FBFqF2cq7m1WnAiEA/48FdnT3DKbLSrn8W/CwMwAbV0DV9BvnsJZdvD/UP1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxF8CRA9TVsSAnZWagAAO8gP/1Flh9pq6Au1C4XYq5oC\nXss1MWXPeSswc3r2REETISCblzFqYAeMieZh661IFqMrDOCODMD7ASYD1eJp\n5shPx1MKceE5RglW+YMAzUICbguZ7ygmjr/MBSu00K4OnTol99/APiv7vDSY\nw+ICzs5MI0TXVcStVSWROyD2CWrdIN9eYKTEOWLjdnzkF3DNs6R/Jbmruvy2\nKqff9EpA2TtTiiWFT2+YFVJ09hvMp2LjNIhzmcyjwwV4ylj7P8nteNHPXKeK\n/ynmxtIr4Eroa7AuIcwMzCdYZPttHG0InUW6eodKtu9aA4iRC7icdnMeAM+J\nR13NNi8RlaUq5EaW06Ft0fr7k1ebnEo6jquUefAkyQCRWIOv+5D7QmAJrdex\n4zVtiLDDZ3qoqyl2gBut7NZkTnPXVzjFUf9HoNYYIabDu34zuQFMfW7SMyBG\n55yHE+CPT61rhMq0fVinhVnLMyrj2JeZMWaKxHpR2hJx/rV+vgZ8br6pCHnR\nk3ky8VeHYMHttOyliMhSjgTgr1uZUGgxQ30BfRVcYY9LQTX5GnrOlD3CfXTr\nUouuoe9ooPzYR2Vc2dXt1558Fp5RNooPZTOhV1rCD1yXMQujZcnMhiQwrll4\nlofBll3wX0QpO2NCOcQPIZPpSGaniPVJ62MaHcRAzxc9qPJLqehCiWSBJmUQ\nJyJP\r\n=cOaq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.48", "@babel/template": "7.0.0-beta.48", "@babel/traverse": "7.0.0-beta.48", "@babel/helper-function-name": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.48_1527189884183_0.13677443565267677", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.49", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "385591460b4d93ef96ee3819539c0cdc9bbd4758", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-LweAu283eKY2CMG8Oo4KwS5/Tndk+KLnGrVLF2KM/PI6Sc75NLuLogb/eujakt8ypUF5/SlmnGjTx1R+jKnAjw==", "signatures": [{"sig": "MEYCIQC4uHOf6wVdw7f9Md/SRpnNHZ5WHzjuLV002Ja0kW2liQIhALFro0VGGIHhNeS/D37oAsQMtwqHjP0NL60PShNol2Kw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4542, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDQQCRA9TVsSAnZWagAAC40P/A+MY/4vfRXnwcugPMSJ\nuyGHHXlSGPtXqneoswra+U9uhw2l1dEa7R205m1NLc+j36heXX26kZ2jplDp\npyy3rScnBKRkgqEYDKfIOtLZFJNUkaYnJYt7Iygknjy8cwiQwSpDMFbU1tDM\nPkDFSaFwnrR2HauBR3YqMQzANEo1fOPOOAazE6dSgYa162uUGyi0DjcaPVSb\n9NHMcolw/2sYUBr3wmQVe3RQY2BjkAQTkVvE/LvtfnKE9Tk1QB6+edunT4rc\n1QuO2WvdY3aFq3QyZXAWzTCCbv1yxfcXLRSg9aMGsBuThpEE3dSkZ7jS/Lvd\ndg8eyUg8UXLAtyjgozatQcOKtGGBv6lEDYwCD5bqsR/Do7atArvgSfthATPb\n8dsM43BcrChjlAHtHj7VleoaiwOMRVu6LyEGLxpmgm9Pe+g/WJqChroZ1rRZ\nOtPZLKCPb5u5lsa4LnfoGmXsEV29a9iw3qo7hiGyF3oAUkVWHX+Fp8iYu2Nz\nPeqc+7b2C3eeQmW13c50zYKYlIP/VCVVA/JR4i3N5G0QQm0MKZfArd7jt0pN\nkr8Y5l3mjSoZkc/r9NfXw/HzLVa7qEOLNj7NExsmWMdIk+vh71AjEurBJ/Fv\nNKxfyzGd8Vdg6QwO/ZcBsYKjpEJa0AmFztt9BUQt+D3PlfhX2VgdG1wK8Fk1\nLnCv\r\n=5hq4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "385591460b4d93ef96ee3819539c0cdc9bbd4758", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "3.10.10", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/types": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/helper-function-name": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.49_1527264271767_0.5559174883169997", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.50", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e1674899aba9c76ae89f8a339f4725c413051000", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-28O4eVgmdd3DxOsYmyVO7MGQKDqzQsVjrEj99DbMbCPrLGERJ2a7vUUrqIOR9/BbD65rDR6/60JH2kPpbpxYCg==", "signatures": [{"sig": "MEYCIQCQ50QUkCDTc9oj+mn5wxQDbV6tV+3f4A7ySVNTX4LPEAIhAJhEuKuccNptz2q/Q5mLVrxIMHVMVn+QmAPgEQAT2T1a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4474}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.50", "@babel/template": "7.0.0-beta.50", "@babel/traverse": "7.0.0-beta.50", "@babel/helper-function-name": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.50_1528832882917_0.8048305715142401", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.51", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6c516fb044109964ee031c22500a830313862fb1", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-JwD4cC7HnOtb11IEp14nHN7P9UCDFxr8a/sUXPd4f/Zl0DuPmkMJFVmFAo480wA/jyEFV8Jx7XkMakiFYKhYyw==", "signatures": [{"sig": "MEQCIE327II+hLqKmxB6q8Lt7V6409W1mvlAGiF9YY1bSukjAiBEWvEHrELpAZXjyes5PcA0ptDr9kzx65mz0TiXM9e0KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4474}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/helper-function-name": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.51_1528838441677_0.6962002328726471", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.52", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "36148e93176299c28a1d2befdb8fe1cc3b79b4b4", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-b6F1CoEslC92DSaGVulxL6W9jw+/LZX2xtFzNUKRfjcdNNbf35SEzN+96LYC74efzXuTR8utHSxkAmqcedafwg==", "signatures": [{"sig": "MEQCIBWAi+dgoATz/Be/oSzy9RCE8ywG+fSvyDsiML8S++nwAiAn+JVHLTm3kNNQe2akqRY00dccQw6rMxf92FtSmwHvRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4473}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.52", "@babel/template": "7.0.0-beta.52", "@babel/traverse": "7.0.0-beta.52", "@babel/helper-function-name": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.52_1530838786784_0.351100390217177", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.53", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "abfb2bfa9401042bab257c0190f5ad6db8df15d5", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-BAjHzqq+lkhepz79vY0eZ7WfBl09XGDsHrqfCN2fg2PLipWZYJR0O8rP94YUTpS0gy1AIB4lCQl/5dV4SvMV9A==", "signatures": [{"sig": "MEYCIQD9SJHlmG4eEIckzbmMPWL3MxgGkhxWSf7pTywyP5+o5AIhANXZSVaL+79zP5hbCfDWdsK0AmmfcqxE7PFIRV++m0Wa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4473}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.53", "@babel/template": "7.0.0-beta.53", "@babel/traverse": "7.0.0-beta.53", "@babel/helper-function-name": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.53_1531316446133_0.6603088121579122", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.54", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dc1b7a483a3074a3531b36523e03156d910a3a2a", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-jeMRCnFcTxoFVvSI+fj1Q4KG6Mq4IiK/Li/eHfIiRFWUuVBM96NJIrwWRNCAyZcruDL5HlR3vVvycyIrcXkryw==", "signatures": [{"sig": "MEUCIDM2PU30DPnYOq9zkTlGYwPl41Eqf2tbRSB9QLML3tUAAiEA6Cs5q4jm7kekEojPEJ4xT9TUZ/ywAofUgVXkMMExMHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4473}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.54", "@babel/template": "7.0.0-beta.54", "@babel/traverse": "7.0.0-beta.54", "@babel/helper-function-name": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.54_1531764027851_0.7495144105793474", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.55", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3053e77647057b29b88d9625503e033b1bd349b4", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-qfDq2TuEVLMKoEB5ZpIxCJ/AslQOjTs+2JNIFRRP38hvXgnQrGIisyglD9dHxVmshkNR+Fv20UPUaR8YMu1TcA==", "signatures": [{"sig": "MEUCIQC++YhosLinF/EZvBvK2D+L1ZxEWBwTpizWrLm6S9SyfwIgAbAyOCrl08JTdFDqSWD9/DSDCZJbvpPGuShZR9k8oho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4473}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.55", "@babel/template": "7.0.0-beta.55", "@babel/traverse": "7.0.0-beta.55", "@babel/helper-function-name": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.55_1532815672859_0.6045579730347383", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-wrap-function", "version": "7.0.0-beta.56", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e493eff444f161569c2c0091a7bd60670fc03d8d", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-WuGMzbpx12M40aHtocM0vs9af/LmdwpXsKBX2T2GqCMueD90MHvQU+148vHScgPbUoWP4lv+dFGZDf0XUc2qJA==", "signatures": [{"sig": "MEUCIFv5nrk45lGT+sqr4DO4fz9Y36DTKsRfsHqLWDre6GfrAiEAkALx1+yPBRAzs9Cms5GWu4BImogvo6vy1MuDeNnARFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPyXCRA9TVsSAnZWagAASbUP/jgm34ZMVvesP0Ce9eQ/\nILmAMqhfZj3YZhMe29XV3E5fb66DvzEuV579hKRvyRLcXMwm2Lv7beCWZDaU\nwH3njnOAaf2mSjNvyJfmC3N4LLHjttToY8DQC16z2zCBv7xZG9gVez+Vqqmr\nh1xmokEUzGDIEyXt1cLAW2kIYcoXf3ZLzP7dP6OqJgPLeFth2ZgenJusFzvK\n/Z9AjqReUmuxNtmCNa9pJJtJtQ4nfGs5xdxcDQnqJpcaLPKNP7r9XWOGA37X\nK4t3kFcOYwM2VsOor0BXxDKetjrbNNWdcr/ud9pBviKngWtI9DwVnba22dVG\nfJz0zhIbIg2f4QD2euQIRhwVGzoed7gXkPFwfmLcbHeDLEkUqLSIHQOwsFkI\ns9yxu0u2UcEq/mZQAj9Y22gwCiDgIPT6Ea4np+fTRzz2L5pYvZTdnCwtNH40\noFnf1tnNi8KmeTQuoco+Ib/5pCRxPsUjdWDb6v+oWFFwatTKYm416i3joHlD\nriBLuR5PT6c/77He7fDsYJ5CYjCES45Bw4c2giTUowUnIHQZWj+FMccXzeMQ\n9EBmLdvuaNY4Cd65pqzmf/ciDss8lcibt3B23lkIjkC+OSR5YOvjB+7meSgr\nm0CYEeIYcAqA8j5JqP8sBt3ertY6S3nrVTpvHNbB3rKFHmrHQMZPfggjGc+A\nRuxB\r\n=Z1/2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.56", "@babel/template": "7.0.0-beta.56", "@babel/traverse": "7.0.0-beta.56", "@babel/helper-function-name": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-beta.56_1533344919182_0.0644554488150626", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-wrap-function", "version": "7.0.0-rc.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1ddbc6fbbd42c7be6ada820cf57210a082247020", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-E8xdh6NvDuSVuaIPOEBuZvJynYmkpd903VVbDAoic+vvu8WeaGmJrUy5LIAi/HxHfDIKJ7jsB4zFDo3JsGt1/A==", "signatures": [{"sig": "MEYCIQDNIBT9uXoFfFqKbpVw33pZzQgp0S43MbgecmfmtpY1nwIhAJyimCmTVMAE4/P6nU9kjXqCi0XpbJjhb4/t+rILw2jb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGUACRA9TVsSAnZWagAASAkP/0VG+OeMD2r75EihO23I\n6xOy6xvr1CG/5VFE5oT5ONZ0JVyK1Uee5yZAepPvoFhh3ou/HZUG2qKTEbCY\nF2Rjq+0QRf8yAEtiL10UmiHCyeT98D8UeAg1JbA2f+9O/IK38/SfkIm/EA7m\nIPsJj5josQfrXGa7Ap94XaOU6ejyw4QwBlbB3CErIyG7qk6qXvGu7DezzVTW\npB6YuT+WB1VP05HFUcB6VKUZV3gha3WAXY4MVSRUMVhxaRJMOAagfIF39CY6\nsGtcICHrLR99w15+9jGW/Fpt7S2bCjktd5W/F7Rb8JBDcXAz6pxJTFnPZUhF\n/ca6qu5iXF+O6TKDdZDOPuMtTvjIL1J1qsMESnWyiQwn4fvAOu9DfUDiY4jJ\nq5+pb0Cf8T39wpw9y59h1X/YW4e7KWTvejgyB+qyWkcKRw9ZYCAzCJZcAlpO\nYobMcmjpl4M+ofSJSl4U9J7Yhd50nAkPUvhSYPGAl0vdQ7b3Sju4w4V3wqVf\nwWiEunPsQGD+k/xRy0+rSHWXqpn1BgsGWuPsgX3xiwkFb2VxYrzC8d+ElMB3\n1t8uoFEfJ2F6y/5xGfUdWCt79Y9SBPQan3giFeoujrWlZzOebMBah1cq3tG2\nMG4ubZXqjv39SYz42rYfmqdTHuQFszvxnOEQwu87YdR0qr0Hkns8QVMUDgo/\nSAmt\r\n=fTpQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.0", "@babel/template": "7.0.0-rc.0", "@babel/traverse": "7.0.0-rc.0", "@babel/helper-function-name": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-rc.0_1533830400018_0.8479232427037231", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-wrap-function", "version": "7.0.0-rc.1", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "168454fe350e9ead8d91cdc581597ea506e951ff", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-LrqRD4+jEkQGVQsCRi7bPkSmYFAUd3pv9tYAC8nsr9Y0Qfus8oycqxDj60QW4dmigRKBRRbVVLr/0kMI2pk0MA==", "signatures": [{"sig": "MEUCIQDw8D8PCnrFAyZNtZT5g+WVLcKtheHI+UrO85n0XhNB9QIgfJMlM+j1JtBRpXinbw/QxSl7gWtBhLyB0qre6xtcdFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+OCRA9TVsSAnZWagAAgMAQAID8MsOZDrMDAj6h6qIc\nsr3ye/mDFEvprYAyiTs0XPhhCSsLEPkdvxMKJFqMBv0P1bpV7Ift2DF9hWkg\nASGjPaogZx6HS6uCVJjCQu06c+glbJu5Cf6tVfrEa0n0bDDq3uWHWPb64Blm\n9We985gQfnPxCtOH9YmHLsU4UW+q1ODOMWkpIfUa0nPyJOGLzs/1n6jyAmxS\nA6pfoSDk7CWCG8/z1I8t+0IqKHy7jBVN9mbDjZCl/f2EwUkfKGYQQTsgPGAu\nwHecNLJ4lldOQ5UKpFCh+RG02WB9u6irFnys/dffHye1C1v6AT2Rup+DUhuK\nQZAsxrxz1/Xe/JhATru/Safq/d5vuetlc6ufhJVA+9xDG3EXsTSkxc1Ti0tJ\nr+74HX+EFfyqWioA3xaaC3GxIMnx4pJPiDtUro03obNgJ7+WeIvCMkodryzl\nAIcSJIjAIUZl/w0EcFdFqlyf4OzJsOWIjP+cHBmjuipg5/DvXV300mblSpLb\nVzwBq4hcuB39Z7g0z+OElbHNo/21wKnBqxarbbQ/aZEcGZgPNIqVcjqlkcHy\n9RZsZQ19F3WCu+OC1wvZl3tgvlK+RIFobllaNsXIERCknTMG633QiN+qjVxQ\nKoxOUSSYbhkgIpydoELmSnCuHtvTNS9OtUjriK7f+jxoOg/UvddIG/SgtEE6\nUsi0\r\n=iW6S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.1", "@babel/template": "7.0.0-rc.1", "@babel/traverse": "7.0.0-rc.1", "@babel/helper-function-name": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-rc.1_1533845390400_0.11489005220348414", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-wrap-function", "version": "7.0.0-rc.2", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "788cd70072254eefd33fc1f3936ba24cced28f48", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-uq+7oYjrJCDj2cV9ZpxiWzLeLZFmsM8V4lyfGyOIEsv87biGE+UcOb8R0OHnA8Hkq2CpF73pCj7nm3tdhj8k+g==", "signatures": [{"sig": "MEUCIQC3aUNcIGuV07UaIFKBhgpMEcmZs3so6npPkpq3jKUzOgIgSWCDGZlRHrDKC95Dzcfv0YTUeL8veV8dlTkFRL0de/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGdFCRA9TVsSAnZWagAA9UcQAI+XV3ArOW1T8uC8AXP9\nEISEweHgJitqcdtDg6Jme0GguAIVGGmet4dW9OueJlXxR8QxBEo1Lix1epZI\nzm+zHTlpYg067NqBf8yl6q4HVRY4mBXJJi6+sXumFWviFXYXZDZ8CyjQhjyx\nj7Fs+3c/+rj5cx0WQ1GwzqKKyBA+YErNA7J/u0fwXSdXXFddC0uoXXtVnRSA\nLTYQdin79Yg5m5EyCWU3lM1LgIma89SW9ugFXACEsCgRc6AbSZAnyKc1jt8k\nPE+oKRAAFWAruryORQWSXDPuXD2mzMBo9miJAkUSB4ilBTFAthbC8uO8EeiR\nQnJVFbJO7Ru06P6cELiGEtzn7Y/BTbWeTte5QxHa+X+9e85ZxFjjmmsg6PWV\n6S3K29ZUo5io2AKOPqEd0tLMSXNqauUDuBVorTBGAu+oigH0KPnLNy8M/87H\nL0dDslD3jDi1s20oQOhB8nPrGygw7U3Qj47XhkUOn1PU5ZWzh/ZcrEI2ArAE\n9X66MuDx4lUN8DbVDtFy+6rYax/OOEQK7/kEgLSLsW1h+V5rfwncUA1GRcIY\nJ3TqXuSuAZO4I3+4fOcDZ9sD7jhJLfE+8i9pRYrdu7dIh2iy20L0y7ODyoZ7\nRlu7H/zAoQavbecYv6iUAO094QuMWQGgbXOKnwhuB3Gw2Te86W1+6ktM3tfJ\nXUHJ\r\n=dObN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2", "@babel/template": "7.0.0-rc.2", "@babel/traverse": "7.0.0-rc.2", "@babel/helper-function-name": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-rc.2_1534879557256_0.6666323324636778", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-wrap-function", "version": "7.0.0-rc.3", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3abcc29bc93c46d61125f994d8667e297f7081fa", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-i0vX83/jMwC4f7ONF4dE+9chcUCKv8FKW2W5rpaVUcjxsxI77JaHIe2+AlkyJCqEel31HpdJ/MNBz38uhmiiwQ==", "signatures": [{"sig": "MEQCIBomyB5izAiYu+RS5u1ZUwAGmXiBdvRI/rsY/uVKcf7vAiBg/2InfDB9dNYuMrEJacuFqRrAn0ZCB/IQaHWohGh+HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnmCRA9TVsSAnZWagAAOckP/R6gotDfQVqO9TfmO7Nb\nX3CHL1ocBlpMlaDUTgE7M/qe5IoKGIq7oSVYqG4uQEu1FYPZ7MH4Fmrng/Nr\noFZkJHHwL0SqGJWz/LWDeytcujTe6CXHUfVwQ+30sU4NHvS8L+W3T+TB9Qva\neANkjUC0QheCHdrrRqcs8oUYyihA/1KoNeVqt86PbwyOPdR6VSpk87AnRWNx\nO6FUsi5sk8j8BoNzaHJs1ybiYaha5lV8grVXKcScJzAjDjMyVdLh3xM3J8fy\nS42C+E42bDBJcVaXiHtADOoAd1WFwI/rM6iMh0VYSxaL6XwGVKor5u3uSOH5\nD9hou4WoCpPqY3o98FKcdkYeXZ5Fnugw0stBKiZ8LU3GBPo1fFr9mMsBXGsc\nq+r7WHNjFf00h5Gus4eXWMnVUit/HeGj9UJiplBmBduQkYxTd+SXwZlwWpu1\nk48XoH3r1MHa2AHR8ci0UDSVJUjuLApcwWySkC1fy7HICFD1dyYZDmUJmzHH\n9F2KVvwiyJtRSyrVctj3qUeF2LTeJH0C1HGYAc+R+1VQdGZ1eT02fdn0WOwi\ne6K6SRIMTA7QjEnvB0iC9nbkKoHywFshkB1VR3XGRDgDi3uMXg5Ll6XnUtdp\nifjhxzEoHHzM6xzzuts3k7cdJl2f310fdun9/62g+e4MjwpSzKCIr50QJtUX\nEdqw\r\n=Wx14\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3", "@babel/template": "7.0.0-rc.3", "@babel/traverse": "7.0.0-rc.3", "@babel/helper-function-name": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-rc.3_1535134181642_0.09159918794823296", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-wrap-function", "version": "7.0.0-rc.4", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "cd2412af3019224af3ff815510a728810946f9ac", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-E/XW4q/+WKU2+2tt9pNzB55qDBTXi39vdYKvDU8RBSzGswW5Qs+ZF/0kLhwAm9jrTclTFzCqF8YxiZsI5zY4sA==", "signatures": [{"sig": "MEQCIC8wkamYvC7lMBhymldJKdtvqAB98Jt2ANnLA3+B0eGRAiB2I7G3Ce5ts/4gaIGPtip5WV1SvzbZCb/72tYEDH0Fyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrgCRA9TVsSAnZWagAAGh4P/2VRkqxknZE2huNbyrbb\nDTCCN2w7dnsxMMzNPu95ZjkVCnU1HU9G0HPg10Q9SPrdx/OaG8nhRBZpLJcv\nodKKEQ4ziWG00cgMCD0p3KINT55X+DbWQQ869SyETTISk4aEX6GXg/Ip34nt\n3yOovd8E1apB5uyaV+2MpJ5klOCMqqnEnKPpAN9GW9b9Q5C7cLsDQBVoPKnf\neFqqMaczU3iq9VNt604P6DwUVEczzIyhSbONtZuiVgAHLSFjKHbG72P94Ior\nEpZhxIdX5+RBBNVmaPAS+QFWPXcMsCxTn4C4QXDKo++fu4f+F2FAmTqNfJ42\nrqibx64Tk9Iwqw//Knr/w9PcDw66qLpmAl9FmWCeBcm1mAeFAJcdKfxrYY4G\nkRnr9jjY33lcz8vza+WseIiKyOAMs089lFDQNeC/c9z8C/yIwp02daXGyLK5\n6UBxErK0CaHGT5d/2p/U9A3BODMKVyP+O9sjbju9pZ5fyJ2rBu4Qd2HgMd++\nOaCtxog4bd/+tGjfe9BYWvMQggrUDfqRQ7OTNW65QW/QYeI/95cYh7kh+U9c\nCej+VAMfvDKcJMN/g6Jh4K56XD6GXuLHpa5iFoz11ca+3uqUHuaCu1QYpjSJ\nwChLfh3wQ490QGZ9d7Z+UyvK+8blmrX+7EuCpkj14pQkH8+Wu+940SeLqwdw\nWK6h\r\n=to4K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4", "@babel/template": "^7.0.0-rc.4", "@babel/traverse": "^7.0.0-rc.4", "@babel/helper-function-name": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0-rc.4_1535388383448_0.6070244878980005", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-wrap-function", "version": "7.0.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1c8e42a2cfb0808e3140189dfe9490782a6fa740", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-kjprWPDNVPZ/9pyLRXcZBvfjnFwqokmXTPTaC4AV8Ns7WRl7ewSxrB19AWZzQsC/WSPQLOw1ciR8uPYkAM1znA==", "signatures": [{"sig": "MEQCIHT0VrwvLbt8dRPGQ1bkohD0XjRXlKCupJWHAZEd3vnBAiAX9H3C0e+EczQLIahJbU9x+DggA7qkydPElDfXjD/lSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDVCRA9TVsSAnZWagAAwWYQAKAw2M4bLrsGhXDVdVFY\ng0VO7xlYWQTJy4tBOejlUONbOh6YWvdnoUd/XWZYKRqOubo+1MqN52GFAURF\n69EvCcu9S1/b1Nrckh6sGZ5aFDZmwD/vIpCTclIEhLyYuuG+PTkC8uU8wMZi\nwgftR3bgzumJeY0vCChNG/5AwlEwtWUjWQ7GsP2qwWPvNsc13Wb2fneGXu0m\nMZOrzvxBbymeRNF4ZIOj1Or9/9fsxh2rerNNHLcgbhCteWweAWqI2ktRy2pc\nDwRbu+rtBDxg58hdNAxCz7m2LgjdbqHlQH/x2ugDMg479J5u1IYD769P9wIq\nD+yIxp54BPBwhKGeD+6zWai/4laoB7W6mW2vgWf6wF0+gHGrZZdox8aa8HKB\nQ0OhOxb49GHIb9GQxUwgaL7WKnIrPq7AOWIkQP7uCTB90KVfh8Sv7vdFGTUt\n9wRndSHhd2jTATXsZwITor50rq7JLH6B87k4wkp+t7zM5rGuz0Jrow0jrM8E\nzsA4xdxm2QVXT5QFN+op+QwXIpz9b4uhq1ySr7iQjt2Rmw+gHke6y6y61VM2\nZ6PDIRnGGZDDvJk8gZnYrUWsn73C/+Wwmb4E2np5CJv87LpvxoHYiMwsuTLw\nbT4ClNWN7R3tdXPLGCMx4LB/MyWsfDyrz+WNFiE19ioFSrF+NvBWccM6gdUq\n1AZ+\r\n=ayf7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/helper-function-name": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.0.0_1535406293209_0.3244954913551148", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/helper-wrap-function", "version": "7.1.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8cf54e9190706067f016af8f75cb3df829cc8c66", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-R6HU3dete+rwsdAfrOzTlE9Mcpk4RjU3aX3gi9grtmugQY0u79X7eogUvfXA5sI81Mfq1cn6AgxihfN33STjJA==", "signatures": [{"sig": "MEYCIQCg2jlgI37eliDpwWGmg5M8jLh0zthhx5hdhlt9VZguOgIhAIljL0ReS/ejhRARWxqOj3emSZSg92cnT10qqzKJVXkS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAD3CRA9TVsSAnZWagAArPgQAIwseuHQjn8qG6mnbrDp\nMnNcAGVCSPXn9FyLlgMagfZN2vTFky6FaNRFHgN8ltMqRIwpYM5ftyrDECwN\n//LRLY8GDCmcjUqho/1/x7+1EQ4C4rieM81Mc8NSWdIRjxGMGl4HqSjZB5zP\nD3+Ai20xVuxo1SeMF/zN0jN3N3REcUs9ohFoyk86ggvyVGztICID7oNoha5F\nfcTbLqpGOWEh0akDSCLVGl4krnP76zkTUMlN1h7sXM4bmR6C0oltqGOVNQYI\nXRpy5t9uRnY1o9ZjXGsb4wME8D5kJNtMNwdQXCqzKHMWzLK6vmp8Bq48fhRq\npYpo1EdytDXrgZb7smFAgg8PDXP1qj3pbKQqPjy9aucTkooSi6FcqhmmlUdx\nZtsOdvR6LEmcNoSo9fxTJdQjLGSjyDvWq5rJ7Uq8GC5/dObhqriL28bq3HZ4\nNXrtRZ75fe5fRm6inKD83gsWZ7JrBK4FrrNARS3Gkx8UTVY54V6U8nuWI8Uu\nUh1sGne8CsMHWi396Wq+fjp6XuEJyigj8h6hNnFVBAlL3laFH3jgtg0spXtQ\nfNgUEs8f1HIPqfEVo0vEXIZ5BeyPu4UJx6LhEywTooQ86W0OFr/6MXDiS/6k\nyFheLd4vpkr7sq63glj3ezkiNgxZIlr9e44eyPQjHpUVVoCVlRNYUAj3vaik\nbNEK\r\n=3S94\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.1.0_1537212662473_0.1906905865338342", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/helper-wrap-function", "version": "7.2.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c4e0012445769e2815b55296ead43a958549f6fa", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-o9fP1BZLLSrYlxYEYyl2aS+Flun5gtjTIG8iln+XuEzQTs0PLagAGSXUcqruJwD5fM48jzIEggCKpIfWTcR7pQ==", "signatures": [{"sig": "MEUCIGeezKFQj7g95DnAVO/EDuMAR3Sjrr6rmKTypbFZC99rAiEAgVuG/8VULolgSRfCnJD+SuzLqGhUj3J7QyNNa0oXY1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX25CRA9TVsSAnZWagAAAqoQAICGYIU5l/0jsqD9wF9V\n8ev7I2G+YrKFx3Tq12gf+2/QIc7kvQMGpUhX0yyrgiZ7EN6mz4d/T1PUIh0E\nFFauZqdMbPlPL/RH/rZrxBEl6WWHsT9WSiL1Nw8kdR0MlLdCoHm71Ion6IJC\n2oMDpCEzd+sljPaE5FtqvFphFeQihbZX+dvYNLtQIjYTzBzsTmNcRrwFwN6+\nyArAbwA2lDH3NfCHKvbHhu/k553tN29PXNQaJpIUmExCxGaNXFtPJswugk8I\nZnQLwNh21a9G5BfT2Df5MHKsafEObumt9/v0J8cNFwMPB071yhmtsa/TK2Cz\nShXmm/lJ3xC3gxWy87L3NLhPUU4eoPN8U7NewNgOLUlTH/jIouqZ4ufYvL88\nqXdwzqpIbdIul9fL+zIsaEx24bjRuqLVkOCMOwMHZoebAeenEUSa/zu1XFjn\nhEgcfT/sN0vD/pGHTyP0xoIeYmW2z/MA7V7zFWjfUn2pnS+uAo4S5NRHA1hT\n5a8r2Qnsp5ZKPBFhuiUzYDYEW5WBUf0MHVAujaSa6CVUYju8v59bKOeo/7n0\neoetI7CKaB7ibjKt4DgaQ9HcJ0HTINXbo9vBRikYlioj6PmVqD3ChGKpLScs\nN9U778q3dF7qI/ZVnbMpX593ZCr21fVbWF9O/DewCcBWl7L8qRFxqct120mR\nKGU/\r\n=mubi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.2.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/helper-function-name": "^7.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.2.0_1543863736565_0.7028420696434907", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-wrap-function", "version": "7.7.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "15af3d3e98f8417a60554acbb6c14e75e0b33b74", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-sd4QjeMgQqzshSjecZjOp8uKfUtnpmCyQhKQrVJBBgeHAB/0FPi33h3AbVlVp07qQtMD4QgYSzaMI7VwncNK/w==", "signatures": [{"sig": "MEYCIQC1ct8CgJMMF+zInl+4da1ezKSDV4MM15FZQGmDtfdKiAIhAK2IA24JikbY8uIUSNAD18bUqVwubjI9UZi1zUqHFiwY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVTECRA9TVsSAnZWagAAVGYP/iIEwFwVuRrrEzy9aQUo\nFA7GH58SqMLY9Kiw04M5nUnm7e4me2CCVygPX7lb8L+MP/zVlXhoOI6u5sKl\njmTbfALwv/JlV2+dmbyfUP6JejbdbWpynnBHwSjOqrvAPwhcdZqZ7seYr6Lp\n+wdAUkQFLBKyix/1whF0OBYpzThCak2t4V9SwhA0dnXclX/aoK+KQjp7Y5gN\nQ9nJ3FnSSMbXAnvyoDb4JkS0vZCE3PvHPwyrtxIRECifCsGD51LW4bemzslm\nz/kk5nm9TpAb4IV6bG2CmKjhGqO2FqQQVvzstym5cZXx47DwYuMFAgipzm0N\nhFJCb04G2G2BW7cIDf7ruMlmiMS6+fU49ttNkTQ2jHNxL8IK6oTa+wkeO4rL\n51YWxPOZIoarTlnNOlYGsCyWEzD7JxM7DhRwMK8/tR8rR/gZ7iqy17ZtGTOd\n4qoXXZRMXHDWU088ilO1ZFd/KMkjqKrA9Kasz6kxLvAXFrtXO8QpDJxaAxoh\nPHhTZNDYLIZYMGeKKTkk6ucwlqr/2WX3uQIKjehZawc6MHBoRkaMr9vzcBvo\nYq2sGg81bjNmGWzp9wF1rkOBGCiqNnGcbhSVL4HI4Xj7MAlVOdJsT9Cotm31\nVxTQPhb7PefOEYReazZlu5Nyp+tbm4Gm87J/eRpwQpKAy+idzkgWKC8Z84CE\nPdRu\r\n=3K9e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0", "@babel/template": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/helper-function-name": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.7.0_1572951235769_0.07059195410270225", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-wrap-function", "version": "7.7.4", "license": "MIT", "_id": "@babel/helper-wrap-function@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "37ab7fed5150e22d9d7266e830072c0cdd8baace", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-VsfzZt6wmsocOaVU0OokwrIytHND55yvyT4BPB9AIIgwr8+x7617hetdJTsuGwygN5RC6mxA9EJztTjuwm2ofg==", "signatures": [{"sig": "MEUCICES6mD5o733eMROoImN1Lau3Vs8hFoW6m3zkRY5gugVAiEArkaJVp6Hq8PKf+NNOROldMy7Xj/tD6zlM8ZBdvLE+h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBbCRA9TVsSAnZWagAATrgP/A2Jqk2zvT7M6uyUH02H\ng59u7JmcKdfZsxUoLW/8vVoIwkVy0OByB3tl7Nl+vO7sYGEsMIEQ7LtETP+7\nmvJw7R33jrmkJBZhp3UfeHWdw/53KZkP+0GEb3BV4b0omqtlo99ChjWLB51p\nph49Py2X9KCtnYn4CUvVFo01fGOYrwBQ8/sfy4EWxChwJCOblZGKAbXnP/sX\n6EUVGFpWM6BAT/zc8BwWp/VxiZfruX6u2MGiPvry2m+iC2HeDzgbgsPww2Nx\n5DpVQ+uD2HODzTlvnTWFBKFsKQ8FTBnu2vUnF9uDwMkL45MB5myvXofXtvBo\n3Z8wQBUlS1sjNB+FqVDX0jGpTGQmf/6fP2OxIQXqUnpdnoRbRfTv5PvWtaJw\n7xyHVjLzsKbBU2779dV/TVtk1kYHh8nr/cHbyBU0ybTTuXIB6J+b/q+6P42n\nwoSCtyNN/w5U/Y1YFPZPAiI/VFEuWmVj+MxNMr9KmA9vPczd2g/iYhSnSmCL\nhGtg4nPVh5I6BIQvxDtnDufckvwzCI3hJECbd3J+jrl3G0Y7dc+2Z+K+FK67\njqoBbtKgVqK6rH6vghQsO4Cz9fjvXiUk2E0CdgXj3qWD7Z77TZlOTooc3AjR\noyBKQwDKuQ2o59DxEPJLYT/j0s6OARU26zDF2vpxDfjgXQ1kaEJGLcXk+mNw\n7w5T\r\n=hnkS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@babel/helper-function-name": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.7.4_1574465626754_0.13904806469489572", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-wrap-function", "version": "7.8.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "a26751c7b0be765a0db10162c6de485402cb505c", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-2j6idN2jt8Y+8nJ4UPN/6AZa53DAkcETMVmroJQh50qZc59PuQKVjgOIIqmrLoQf6Ia9bs90MHRcID1OW5tfag==", "signatures": [{"sig": "MEQCIDwBCTQSfvTqLPhyV1H5zj6WaBEQgsBJM7PcbYmtU/2IAiAXzaeCM0dpi1PBeJPRhwBLyDH6KqOjgTLk839PpM7wbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWcCRA9TVsSAnZWagAArKUQAKCAOuIHszPokomDq5Hr\nLBjQTXVReuxeEreYXel6U3kA788yTXxjhLAK7VxblOQxomnRJH2m9t6lX+q5\np1hWGs4mSQvta8R/eBRgoRx62vr1l1GTQ8GbQqPf+Ewber7td/a0Ert0Qzlu\nALiJLdDF6n8l2fqFbLkmtm89NlgdlY5T1l2jAPgfg/CP6Ndk/vpH1ytplfA9\nCro6/P/bSojsMNn4Vp0CxW1x8j9avoIPbZ8bVbkjHhaaNPYEhotRnp9OoQwG\n+9AV72qrJ61eOuhBH53pJorz+XhvryedEQNQWaG5K/5zrTTvephlwkkU4rPW\nyuEUBvE2C5SbxP2WQ4sWDTXZw419R3TMXqXhsHEuaeyGZal8q7qjOEnHPxX5\nLmW33/0kyIet8gO0CMrEBE+l4BGYqU+UqploCZkcBFPPZWtzFcry6/27geUN\nsp6wMs+sX6at2vFZ7l/1WAFNk7FEu9zrbsOc3kwi0abKSmoy80QqzIoiWEok\n9cCAiQ9+ZuWskVwBbhLH21G3EBQhzeDyC+phY6CDK+O3FJIVT5zrsOl6xSz6\nQLO5KllbP7xfW3LtvFTbkE1z/xWc4Hz+VmiFKJ5Hg0OgJzcUDhTlfKOuCVgP\nQOZ9lwE0sRZ26HyJ+BvkVPDCZcokRwXoWTuAQRAGGqAdBGIQ3zXaEvwqXd//\n3Axu\r\n=wmAb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0", "@babel/template": "^7.8.0", "@babel/traverse": "^7.8.0", "@babel/helper-function-name": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.8.0_1578788251653_0.6441012569482674", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-wrap-function", "version": "7.8.3", "license": "MIT", "_id": "@babel/helper-wrap-function@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9dbdb2bb55ef14aaa01fe8c99b629bd5352d8610", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-LACJrbUET9cQDzb6kG7EeD7+7doC3JNvUgTEQOx2qaO1fKlzE/Bf05qs9w1oXQMmXlPO65lC3Tq9S6gZpTErEQ==", "signatures": [{"sig": "MEUCIDD68W+L+poyMztDI2QWNRs1oZHSDUPXr4uXmSak7yHaAiEAq9PXC91MbvAZbeamBi5Zcj4RRYdJCPZzU79/XYNc180=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ+CRA9TVsSAnZWagAAb6cP/3Tiu2EP2XPEhfXcEYUy\nUPuKItnzSxjox7GlZx2B4UP8lmDFIpyVNXYoR2saHcjZA3m96o1MBNDqaK0B\nfecNpQ/uY2Qklmytv5hlXUlpFnpX8jZno/ojqj4qjZQr/gGCzLw0g2o4bX5g\no+mS+OoMS6aJU6aeuc6vnXRhxva5uKuieAD8fh7jK4DTcvBhxBu//GlAh9dh\nCx36LcQI0q3AYIAiyNqdQnRTVbNKQgpwAwNw/jakadXhMdUQJZFQHP3I1uUy\nI/7TTV3GIm5fRRhW6xuBYmd84xdVluBSpEQtJcmhsGWavVrtVatv0qVOPiT4\nnqCSL2QrThGupUvgp+JsmqY/TjXGlNr04a2GJ71re96ctG6kqj3wO4lG0XZx\naM8c0DuZF5788Y7zS07Mxz/drgGQ+2FHojQgO8i6bLRmjR7YtK76Bt2xJ1An\nxG1LVD7ggkCkgx+tgwMj61nCe7ObQitWzW6rIaiRwysSrBteX1meSFOUM+mN\nPTP3SmcCytIS3KU2WNe7NusFSlb/OrJQRgHPJ+qNe/rRbhkZvbix7aX1utPm\nASBu184YX6HXLEyuobhRI8e4shPBR2WRLLGzOe1B7Z6k66qui481zJQy3c+P\n+pdE1dQdd4HB/+58JsrJNCgi8NQMeupCKUuG4I4EYU98Ly4xcX/n2BhAbgjD\nX0sY\r\n=50bv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-wrap-function", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3", "@babel/template": "^7.8.3", "@babel/traverse": "^7.8.3", "@babel/helper-function-name": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.8.3_1578951741687_0.35122618794854565", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-wrap-function", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-wrap-function@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "956d1310d6696257a7afd47e4c42dfda5dfcedc9", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-C0MzRGteVDn+H32/ZgbAv5r56f2o1fZSA/rj/TYo8JEJNHg+9BdSmKBUND0shxWRztWhjlT2cvHYuynpPsVJwQ==", "signatures": [{"sig": "MEUCIFjBg9LlaEWIvPgn0RDbRvA9VYvXBZymstAiGY8JuVpZAiEA2tjG9p+E9jXIrEPkVmeRg5mghGGng734PKUKUHpT/zU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTPCRA9TVsSAnZWagAA/rIQAJfmea5bAA5O7G8KLl+M\nL9b6sJbAosJ8+9ZwnQ8gElxjByeyd459AHKnGpDpn6BlRqFGTj83aBmln1RJ\nqB0dwpTCw9KtaCUiWizMs1pTKRP3InsHaGM/vYoSfM3BYYuOIVYe8mjEQcDy\nSpWC9xqE2wPduC+Jq0iUbpsuiw+ewwuuvCMSDd50qhVT/LznsGRF/9/V4AZH\n992qHgsy8UmtBs6rkOfYPy0hQc3C1VfXB+O881TYH8MdD8JjCheBb1NvKYnd\nKJ1RQQhDyoYx+oKfaqs+sqv4O/XPTtIFabNbzM4ilOJ2I2MXg48zzl3sktxf\nschnmQjAfU7ul9CHvQfItA0XbFqCHn8xJuC/W5Whim2CBsogNJbIfqokxf3w\nYtGmuLy+/jtWRwpPthGGrrqgrqFKlupvyNbLn4YqwnmnHmJIYkqI5JieNk14\nzf5H50sXbo2veuMiSsskmU47h+Ap6ByJSs8HOxi50HIuOq3uW2rlU5BYDEdo\n7FF29pEUwApSPhSwxtgm4eJ4bR3g1iZJ/vY1U4XQ2ToqIQ+kcSi6tKT6xeGo\nGoqiUOvFKq2MuJ9ELMn8/KNHivgu0FiEhrNWlvvSya8QkpB2baEGbJY/U229\nZnQwUewGM3QZXIZul7Wkl/RTsnPaAgDooaGfd3CFhIYhS/DkjYzMTPGBCVaN\n8EH4\r\n=DFVw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1", "@babel/template": "^7.10.1", "@babel/traverse": "^7.10.1", "@babel/helper-function-name": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.10.1_1590617295529_0.6556673575155503", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-wrap-function", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-wrap-function@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8a6f701eab0ff39f765b5a1cfef409990e624b87", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-6py45WvEF0MhiLrdxtRjKjufwLL1/ob2qDJgg5JgNdojBAZSAKnAjkyOCNug6n+OBl4VW76XjvgSFTdaMcW0Ug==", "signatures": [{"sig": "MEQCIAa+xDQ1PhAwRvObfaQTJmgM+AuCAVnsKMJLvJNWAgU2AiBdB3TunXgWzNnYS36hs5Fp365b24ugFRNoHjlzP3KzFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zptCRA9TVsSAnZWagAAud0P/17A+D78oBomherEDzEi\nCA/3oco37xSb1qTLjGSH3Hu0Fczvg0+HQNZYFlolkDvnvOGksqDA+zt9PXaI\nVcgkR/01f8NWZ1nz1/B5K17AQ9HyfWLC9XyJ3ynKWQeF7rPWMSOLQI3zTKHV\n+4gdDO6YZv0oLe0Oo6SZUh1l3hG2zGwk515tidpFqFutnpjZOVFwlx/EBdw2\nFJoIG+y1MvgBjgZtJASRXir3krwydVVRQ9V8p1buGInv9ONjEBEc6tW1wqYe\ncRN1alZnfeORCuzMvNdk+MJ9AoyUeCaoqLe5BtiOcuDKq5APE1lsaE8F7C/2\nLkBebxg01Ptg2rzEgvIRm9A+XUJCVpdHRQEW6nZW6a/UBTWTGJRozW92Ip3j\nMb8RHJ+UsMY1P/10hOYsjdKQNaYFaPt9Qwq3/sTI91eofn6T/sYTC5gvKjMo\nZAKrEtrnbAbQCiAOoRjF5t2idTHrEBXPJDI+FlRGwUXogUKAQMGvNJejGM5G\nG9HFA1BbV2LH21rcTJhXAsV62ZC2oit5vcvw/5oPdBf+VBzmHP5m32A+XoJ6\nkVvWEDpjwPkxkqy3Gn/pl1FxGMfb+WXKtU7LZgVW/sankvciVM9f3RRuxlZT\n3MORECejKK+eIzD/fjU2zh4n2CntxU4edEMARO6VerLQjKpQxBAtnxtcWje+\ntXkb\r\n=7Qma\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper to wrap functions inside a function call.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/helper-function-name": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.10.4_1593522797454_0.6060015743160987", "host": "s3://npm-registry-packages"}}, "7.12.3": {"name": "@babel/helper-wrap-function", "version": "7.12.3", "license": "MIT", "_id": "@babel/helper-wrap-function@7.12.3", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "3332339fc4d1fbbf1c27d7958c27d34708e990d9", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.12.3.tgz", "fileCount": 4, "integrity": "sha512-Cvb8IuJDln3rs6tzjW3Y8UeelAOdnpB8xtQ4sme2MSZ9wOxrbThporC0y/EtE16VAtoyEfLM404Xr1e0OOp+ow==", "signatures": [{"sig": "MEUCIQDhDYLSR13QC1NHaWGiQmKyjluVXzZmsuA/BAN65elJ+AIgVP0cbaCBasJaChskJlMbKkNd4fn/PMsCIWwOSoS+fo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfig0tCRA9TVsSAnZWagAA9pgQAJXfnO0LHivcSQp0Zhw+\nLC7lsNdAzIwbGk72Bw3p4ENRlSQvjQsn0M314gNchfTW2EmQsz7C5D0U7WGU\nd5tO2xb2dbUmdPlxhPhar/wX2LqwHnEL/g1MzolMoHMrHw+Usj4YmY2yK1zP\n7U/utQeIcSdtYdXNzpc6RfZps3ajbsZRXK01QAj2hx6hGLXReECTc4tSyjhk\nkXqjlYw3Y0928E0bv39GFjLzYrxKIIAdqwo8awwT5DqeSCVPss1Q0/yW0DcH\nVXV0FZMmgB+S8Ci2dK8Qjd6Jk1Id/rgxYHAIXddDDSRSmQCvrzwLU+YmlBAi\nylUYQ706883uabFAJ8+zzy3XOMCFdKhQjzuZaKaq3MRLtxii5d+Hx6RrIuY5\no3Smy53Aw+DiUA0U7q38+0RLV+iqCGl9hEYuxeWVteoli4fS+giwF3FaDGBq\nbv75Ddji4zwUsEvRJsGWMAFXNIpMyLdY8gVpmpUQKKU3GJA1XNFfleSi/G17\nDw9w/xrJutxIhsaehksLFv2hSt9VLm1bjU3RmWK1fpLm5VaZRq1CVngWu1wf\negkRjSfMqRnEsdqp35eDSQE4rfbtejS3JdEYS3iyc2wdgZxQ/0Fr5Vid7IHT\nx/otNcgPf7u9KKIM2WXOEQgY52AhL/vddFi6hCuRd69P52FcdT0dPFu9zytA\nk3HM\r\n=cxFh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/helper-function-name": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.12.3_1602882861128_0.7958495211121801", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-wrap-function", "version": "7.12.13", "license": "MIT", "_id": "@babel/helper-wrap-function@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "e3ea8cb3ee0a16911f9c1b50d9e99fe8fe30f9ff", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-t0aZFEmBJ1LojdtJnhOaQEVejnzYhyjWHSsNSNo8vOYRbAJNh6r6GQF7pd36SqG7OKGbn+AewVQ/0IfYfIuGdw==", "signatures": [{"sig": "MEYCIQDuzpZg3tm2+T/wJK0zE1iCJn/K79zMEdPuv1bGeV61AgIhANQV5BaMBsuurVSgNKd2BtaUnMWP7BfgDY2qqVaEcxv9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhZCRA9TVsSAnZWagAA2UUP/2LjgmLlgyEdm3hQMef4\n0XIsaeeRRB42zHFxUel0Hhq9j71RQphyjuWJji/7Am7gCBTGcgAk5ZGhMwH+\nm8Brz8peA2/eT8qWeRg/2zid8+FQv60niSga0wXvVCQvDMy10X8GxuV5l6Om\n/DZd45SmEjX6dVgvKqRZ+CGI8UhTkp0QQo7V90qi/aMygZIfkyjqfJjTLDz7\nNwYxjs2qzSaua+Rid+Gr4kNI1bPK6UapZ7ZZf1xGvQP7nVQTFgrK/kOvyF/k\n3IAhq8iXq4RVFtMw0Ni05XOTsXVTtB9s6ZTHwseMYFvptHtn5VSDgq50xkdN\n2l3ITra8+Ex17MM/8a/CvManyAyqY4f3SpgsmyESTMJ+EkMmkpbS8C9aXKFI\nPGWs/sWfLHHymmW6ShG4D5qjsMRkvNaWRY3oM8p1tjFFyS3j5mTr1mFupOzQ\nG7EoDHXPwDrtNcSwvA42jeoRbwki/HRdwcZlz6JfIWLICqX0f8XD622MuG4p\ne/Cpb89O373jyErvtXn4xYRaxRNwZ8YxO/dC7pZpnbXyG5c5O61WVSD8sB8l\nvyUS8XvUXrADKXe4dmKngziGiUF8DI6HUi6mmmoeGaEy5ziVVm8DEmBJNCa1\n2/lRZJK5ox+hqW8icYV+zKjuciSLJuJQopiYadEgVEKR2UO2UhHn4toT+eXk\npjXu\r\n=PUNe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.12.13", "@babel/template": "^7.12.13", "@babel/traverse": "^7.12.13", "@babel/helper-function-name": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.12.13_1612314712675_0.11166025908686317", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-wrap-function", "version": "7.13.0", "license": "MIT", "_id": "@babel/helper-wrap-function@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "bdb5c66fda8526ec235ab894ad53a1235c79fcc4", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.13.0.tgz", "fileCount": 4, "integrity": "sha512-1UX9F7K3BS42fI6qd2A4BjKzgGjToscyZTdp1DjknHLCIvpgne6918io+aL5LXFcER/8QWiwpoY902pVEqgTXA==", "signatures": [{"sig": "MEYCIQCiCxjUyGJvcufEMJkfOLG0yVOEDbwr0uxsv3CHlNVGBAIhALAyQXiUrCsbDR1UGvohEBAn48PQNZHRkyF1D2AYKmUl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUmCRA9TVsSAnZWagAAkx0P/i/dkLD8DvJN5Ao/bcdP\n/uPqkq3bfaR9EkyiJgPcpEJ4BaOVPGlL/N9Vxo0IFO0f+m57iyPfeMLfeZpQ\ny/n0yr0EK8Jx2ivkPvWq1WBqorWaGdeDqS0SZQ6jQ6UU4W/dI6tLjSjkgwZN\ngZlBGq7qowp+vqMJTjUYJlBF28F6ml2WAwNn+7hjAyqbfylnMiWTLy7eXtlt\neysWvuUU9sZqgpwyVgwddrTHecvI/vhsDqmgHld57oF9+LIE+1wUaLDTYJ1p\nLkSeEaJAM2Xj2bvDG9g1UqZWQGyMyx9VeSFaQdS5oDTwDhqZiSyPMRNZll0R\n/PeP0QpneKEKIS2oOX8NJhqC9hzDRXP0bkrz9dlo1qDBI+BjYML6FB8Ovdtf\naAZVynJOxBlx82XnY1ijWA0RzlKgEwdZuHqlBixNHBTz5Lpqbif9k/jQS9dp\nOBcSEttBBMqGhJ8b6WURZC7yrU0He5Ht3r2cfoYSsszQUz88IRlqLetsWbKk\nvOR9VHcVMv/Ong/s/OM8f7IMaReA83qJnAc/JReSrfbQlVzsNjRvUGEHcl0j\nyQr2GugiBXuT7L665xrGxx0fP3k+/I8gQ5wLs5QOEL9ffvNpE8nMJWwgRH3M\nONoZqUb7uOa8H+gbh9Nqo95SRIglpg4D4cU3R1n0UxEm5u5jnUsZ5iYAgHyg\n9ifR\r\n=boDB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.13.0", "@babel/template": "^7.12.13", "@babel/traverse": "^7.13.0", "@babel/helper-function-name": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.13.0_1614034213690_0.06149374987221923", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-wrap-function", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "5919d115bf0fe328b8a5d63bcb610f51601f2bff", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-YEdjTCq+LNuNS1WfxsDCNpgXkJaIyqco6DAelTUjT4f2KIWC1nBcaCaSdHTBqQVLnTBexBcVcFhLSU1KnYuePQ==", "signatures": [{"sig": "MEQCIEwdx5euzldbPUFdaQP2jhSftyepSgj6tvce5Hz/CC7JAiAEpDqnG8mlxfGzdGRBVV+0c2xVZDthopQtNEK+BsefKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsBCRA9TVsSAnZWagAAvz0P/jw1WNtIGYkRBfVbRS2b\nGxzyJ5UqHDqgEXuvvihldq9pc+tJ0yw+rjEpIGTl4o/vzWKlci8wK05/DmpF\nz8OyzkxH1s9c+kS+i9Wu15vPfmlecgHq+BTWS9KC3a6jL//zvAwTfbz8l7j1\nVek2LTqMYzhCFe5zNJ/JsHVPTHo6koTjQzNY5ewhuTZUEv/6jy0/vybhISPm\n5nX9m0BCr0vKLdqmL2O6AIwb9a8Cj54GzxYW2f/dzPg46GPn/K9L65J7pkj5\nXIyIz2H9K6lB1D9btekywQByNc7+FXa/e4tBv8SEzspNRuaiRD/W9pfLFVYV\n2/RB6TIjV8sw0bnPGXgbrulhgdwMXNZH81rhbJc4DWa+nJK0uDnRUMidY44q\n9Kl5Jv9wGIH0dDu0paEKQimqx1FDYykaer7McHwc2+ITaTnpUJt+iUtcE0NL\naaI2cjf7zB98yLozcG3Emy0ghLeFzmX8sPxFtcV5apx753N5jg3pibMq1Z21\nlZ7phFmFG7z+5HU3K0aXqTKBZrQyKT5yvC73EXDP7TaATWXUHMrMODynbfF3\njZP3zg5nrn2kWSHfDeDxtIO1OTAMR7IdEE2SywB3m150amed5Fjz4uqAdcDu\nl7RIafLa287TF2j4FL0i2hZjC9RqF0A48zgNah7RKIA9SN0oQyupfbUQeI5g\nWMwC\r\n=VTXu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/helper-function-name": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.14.5_1623280385522_0.9966144398022818", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-wrap-function", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "6f754b2446cfaf3d612523e6ab8d79c27c3a3de7", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-Y2o+H/hRV5W8QhIfTpRIBwl57y8PrZt6JM3V8FOo5qarjshHItyH5lXlpMfBfmBefOqSCpKZs/6Dxqp0E/U+uw==", "signatures": [{"sig": "MEUCIQDjdNYrS/721g/ImDfR/IDEjdnn7nhVdBDhUW7hVkYV2wIgTpYKwBOZU7JJcKn6qa84HDXllJNSVz8rh1VedL8Miy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSlCRA9TVsSAnZWagAABZYP+wU3plM8HJShtR0EtoLJ\n0NYuLASffHJbYfKx2+kkEHDsXEENsmPJdCLItnIgLcjvp1xcXFr9k8oqwgAI\n+Df8w9ym44YfD7/cLVwRXSkZHTJc+VYzrSkKVszPJXKUK5/8MBf+Ms63EBMO\np5XlG0xYer8iEJxbWe61cxB+3G9bmds7xXiBpz5DNgzEiu6/OYkQH/1DURvq\nArOnmk8/npKlvJ7KgmRTpGfjTeEF5RYQPQQeLLBGt0AJ5nUdlsxsDjVn9x8g\n9yuOqKKOPVshdp05IMDXWn2nTirxSM+XmZAp715SlAOhwwnX/r8eg3zErtr0\nGy5GrqUwYwNKhwk8Jee3TPU6jzy+4QjkN+6LdhCJJ2m1XCp8yhAjGOmBXTLH\nZ/dsuais5cx0OLHA9jBXkmj0YlrKxXFeNIUlMfieFs9F9M8Z4cIvn2uZzmLX\naHWlE6UlgjyVu0J+WRe5gvkfgY2Sm8o0QsqNQysonDN2IVJLUrSa65l/ajcK\nFuoNgYCKtq/3xHjS17IAJJJ8pyybV4BOQryYKsRwgFOHj24vXQDRETiriGpG\nLT4X0sZ5fHNPNPvLcQ01aRZHzlKVH+7IvSitatzfvUuIvRnYsZrMaQ9zjNJp\nuALzAuGXgoktMMkQT9DcU/+rWN0clP/ssZhiTR053LFrLhYGLUTl3zhiVP/x\nHuaa\r\n=R/gZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.15.4", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/helper-function-name": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.15.4_1630618788938_0.1719617935863731", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-wrap-function", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "b3cf318afce774dfe75b86767cd6d68f3482e57c", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-VVMGzYY3vkWgCJML+qVLvGIam902mJW0FvT7Avj1zEe0Gn7D93aWdLblYARTxEw+6DhZmtzhBM2zv0ekE5zg1g==", "signatures": [{"sig": "MEQCIHD7sc0dZXjKaZMFQeD+5AZwBqUHMncB7JFIq9xPwLCWAiAvYePur7etLDBSQnpTwdgW0NyX7+aJlHNQkEVyDYmFig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5299}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/template": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-function-name": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.16.0_1635551273534_0.5085055231479267", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-wrap-function", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "0158fca6f6d0889c3fee8a6ed6e5e07b9b54e41f", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-2J2pmLBqUqVdJw78U0KPNdeE2qeuIyKoG4mKV7wAq3mc4jJG282UgjZw4ZYDnqiWQuS3Y3IYdF/AQ6CpyBV3VA==", "signatures": [{"sig": "MEQCIHdQaZQPyQfZd1d/l0t3b+5UKY/f++LTWYPQhyVOLVi2AiATbEnesMnXs7rT555MJTA+a2hwALertGET+35cwsFOHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kXCRA9TVsSAnZWagAA3LgP/RqLrs2nsD8nzQmBKNUT\nuSn0vsXz5iOXx/aYISWrlioh34qOFVmkCBpZ2JxpZCEaa5TBoAtQK554AWrL\nNQFjdWrd6nqAZE+Oxiezs+Q+GSWHH6zJMLJDh3pRVPz39Ey8D34ENxR2vYjf\nj0a2j1gp2y2qMERnpy/jKVPe6MucSzxcyS/o10z3/lCxCrTBSEt/DEM4v6E0\nVAp8NNFrqXHr8g3tN6PLQcfjvIMBVOoNqON3Oprg2TP8jSszUs5ikq0bvTHi\nb6SmW9590Np6Px5aWv6QpRqBchrBkeJjSn5vl1dm8z68EXed/PAk4/wx/1zX\nBaw/MvpZQK+TvA1YJY8wzW5srYAHeL6TwG6CFa1MMqCFKkB3DpvPQESLWLdf\n+w9u2rHBHJM1lRxhh4e0/hdc/PmbtuQJ4c1c/qhtskQPYZJimwmxSMNt8AXs\neB5gS+9Lb/xhJXW8eocu6oTTTjMvGG1kbMPuiDNRBTdfoaMsHvVL75GooDxi\n6v7Fcts6GU4gQCeCQcBpTQKkAoEVJr6vJ4kChmRiUgeW/uchONFTdMCPakIm\nl/eCDjGTJAn3RK7nCST76hhJnBRFbyPsKlJ91EFgRiLd+vnwbLqocYMhyoip\nrU6N6pIVDlOqDR/m3ZJ3Pq3tn9D53sjJMySiFgtVJH4w0w97aL8BKykZ7XfH\nwiQU\r\n=ydar\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/template": "^7.16.0", "@babel/traverse": "^7.16.5", "@babel/helper-function-name": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.16.5_1639434519098_0.3486447241922097", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-wrap-function", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "8ddf9eaa770ed43de4bc3687f3f3b0d6d5ecf014", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-7a9sABeVwcunnztZZ7WTgSw6jVYLzM1wua0Z4HIXm9S3/HC96WKQTkFgGEaj5W06SHHihPJ6Le6HzS5cGOQMNw==", "signatures": [{"sig": "MEUCIEhIT9AHlJTm7q7m6cbqorJ3u1P4GN6MWYZQ9MjvmiSpAiEA8hgeP6UqbcEDAvHQuS03OE8VN7wmFxjaBy4KU+p3pQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1hCRA9TVsSAnZWagAAXcIP/02cW/Aafe/RQfSFhJe2\niI0h0pqE7zF4s+UN+CXrrqpllu2Pc2AZ4XxMo31WyOtADfvxRnZYncGuF9A9\nA2wSkaNwzfLvIlKqIhCi0XYr0u/KXSizLlwcThNmrnJ96P92DcB/4VSt0LKQ\nK4YR7qoZvJ+bmE5VBWDzI8a2ZGUcjIpV1ity4pOJSkHyPOecN6J3TkAzcvPi\nkHc+PbwSpxrJOhkomgUPVC3DDoQzKGDs3ej2AdI7kJOHGbisV6Uq/Bx9IMZc\nqUXkiS5CH7s8YHfSZ4MrsgRb31KUcvqpxhafeRLDRXHpjHsG+lamqFUa8FCy\ncTtlb2Ha8BCuN4AyfrYbMQllBtzkQkMAaH4bVIoUsY66sj8V7bDtjIQtbABY\n688GCY3l956ixxcFLiz4tFtskZWRO886B5/50IruPvVjwvUtMhhOvLgUN90m\ngjD4koowH4vbB2Wo6Xcaf1JYDBTDyCrHicJfhcPyKfMHU67aLn8BHSDY1J1t\ny1744QtlPCzWD1w8ZL/zvn8FcE+Zd/bv1KoC65cbjC+ngYI/F9s2jqnC3XGv\ne7xNwkXd0gpDosMm6wYHr3tjPuC974H2gSbTJWSc+n8mi3XlEPdqTHg0lMdG\n1tgqzLdfD2Q+yAtsID0nWUY7iBeGKAu3REvJjgXsqij/15kH2h7KOPcx8Lpv\nahud\r\n=NjZt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/template": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-function-name": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.16.7_1640910176862_0.5455398969603167", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/helper-wrap-function", "version": "7.16.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "58afda087c4cd235de92f7ceedebca2c41274200", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz", "fileCount": 4, "integrity": "sha512-8RpyRVIAW1RcDDGTA+GpPAwV22wXCfKOoM9bet6TLkGIFTkRQSkH1nMQ5Yet4MpoXe1ZwHPVtNasc2w0uZMqnw==", "signatures": [{"sig": "MEUCIQCoNjcYkyuQjwoYBQ6nlQGj7wN7Mt8KqN5cyt9kKqtUAQIgNIFqeAwPjia7V2bCOnARFk1sa14VmTEESEDCP2riEq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKvCRA9TVsSAnZWagAAGYcP/2hQAtxqE7R2Tbog4PPJ\nhSr+HPRqTc9ohIf73mhpxOPvbtTNqloY9A5Gky5R2Ac7T46bQl4SZEA1/HUm\ncUFpa7OMCQJ7ys1gxEA5/0+brIk+HsJu+Lo55TgH9d+4k+4Tfo4laFWGPhx+\nu2xOgD+Zsp9kjsXUDVJkVz7OlYC/s7tdoMql5RpBZ7+hzjTdPHWr7nnkydBp\nm3Ol0T94P70sGdUuHlEO4FF395HdFsKb7G4Cficw867r7y78dRIK+IGkUawa\nCUGFAGR6iamA5LrdjGOrSFoJrKLlBe81i4EihtZYAlIkV8dUyHkIm2dIXqqG\nBloTiGJmogV3KU17MV48861QfeVB1jKw8IXfqtlXOFpBOQIPkxEt+ILfs3wE\nTu+MNL/jho9nE45XUdq3sZqLpGyV6wSR2KTtOqRCBbxcFUn3yvuskeqjuE6W\neguPcxCywT8UWtBXRULAfZeFTgp/C2G7TM7I79D/Y0WNDmCrDqJqp7EVsqdu\nk9d61Ar/PvzwC/VzzF8gtvAg5GtNibLg/8vyu8/cSvJ6Js95DFcT7l2cvPtU\nMGzdaazdN3J1dwXE1Y65IypGOIPcjIItTGqM+1J1/8RyIJes7gDGHUbmxRxO\n84n6P+g11JlMLPVuIeuJ16ZEJIqPotd2gnQ7ZkonE2/KrspBwt7G/jTyMetD\nmzRv\r\n=GxwA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.16.8", "@babel/template": "^7.16.7", "@babel/traverse": "^7.16.8", "@babel/helper-function-name": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.16.8_1641849519284_0.5344671439807407", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-wrap-function", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "ec44ea4ad9d8988b90c3e465ba2382f4de81a073", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-I5/LZfozwMNbwr/b1vhhuYD+J/mU+gfGAj5td7l5Rv9WYmH6i3Om69WGKNmlIpsVW/mF6O5bvTKbvDQZVgjqOw==", "signatures": [{"sig": "MEUCIQCeD1v4V0kBffDMuWSIo+U3RvuCADKJyG2u+s32Tdq1pAIgXqQUqx1n8IM+j441biA+zhiBx+AWysrDbsfuRnQBgao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLLw/8DXrSXrn+CmzhlVE1QHCZUHsrObJarenk8zg5wRzXfi3+FfrC\r\nd0p/d2MhZVfpezcmnXQjEvjnQLeT7/lOr22gDDy36YImj2I4aNCx6paYcO8v\r\nVrPoaUa0ULhi3A/Vj6QmhK/K0fF/PHahvGYV/6AYszgZlQjq3Mx4HQ4PUNg+\r\n3i/7s+u3SHAds++ROIBrDxALl9pTH+kvzpGB8f/TsZh75U7Uhs6JreDGwPxL\r\n+/kvgtX8/MsJ8SwoVu44Hwy8/da0KLjYG9tQMMEtSppUjDhur5rX/PzP0mB5\r\nxXTeedSFXatvXnCJc2qz6EJIzuphx/QxIkI7BSOkKVj7xJANtEhTxxx4MPpX\r\nMbFYPdScE5xatWI8uJEE0ZSAw8vw791tCW2tDfIZsnTPxleeYyum1bAy0WU3\r\nJFOG9LKegFViyN4NY+xOkf/g8duNjItSAx8cFoW9krUV8tvcfwrCrARiGgSS\r\nsTCKDGRlusL/zkgBWJst3DInhB8YrwcPOoMsFkktU/DD0eLnGW1jwhpIF8wS\r\nXOZ4L8cuycFM2ydM7rxpInW4SQQbQqxhHIBR9jf76HSEoM/jFnhP939hjkwj\r\nexuYdAXcv/wEfbtOLj2ebNuM+jFJOUiCAF68CQa+rpnF85el/jlYk0HQrZOz\r\nz9Y+yYgkb7FWTmNkor9asfoZuDQ42oZqi9M=\r\n=1QhY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/template": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-function-name": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.18.6_1656359433046_0.030533401729369247", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-wrap-function", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "ae1feddc6ebbaa2fd79346b77821c3bd73a39646", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-cG2ru3TRAL6a60tfQflpEfs4ldiPwF6YW3zfJiRgmoFVIaC1vGnBBgatfec+ZUziPHkHSaXAuEck3Cdkf3eRpQ==", "signatures": [{"sig": "MEUCIQC0ajxNs5cAe+pEESpwlv6YD1kZH2kMVRE1l99mkRXNlwIgbkrcj+IwisoojzdbQHEmqZPMDXG0ZIkntJ1XR1xjq4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5328, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGSQ//ZcqK9APwLilDhoWAmuTzcTKkweYocsIr/dDyJQMms8uRRbag\r\nZMMfedAHK1NSFejnhjHSepdSo20JPYW5JeLQ6sPb6O+iJBE4mdR0k4pVrVOi\r\nHJBtpHbYDqdifdNugOyFlFpbCGnrqDJ/MDlpryEsjOxKfl6KpRqfU+bp2rzB\r\noVHe8nQE7FPxf7K8hSM0J5iqJeTWUbKvfEBksnyT/xSXgtxMFY3M4q5kQWbV\r\nCKpOLYozBje3voKvS6WN6zsQKpFWVbbd3xBjDUptb/7S0f9uJPSCjkV5pXgS\r\n7f+YwO8jYRCll/RKZGjgVQmvNTgrxV6Mjii3rAGjjApVqceSWeDIY/OEYiYh\r\nkBg1fmehnXTuTL7mPU3rxbB1c0XFnX09Zuxn59hG7OZMvsvfuRV19nvyMRm2\r\nCBHPKD6Ap4TEDORRD7sxWJSCDRo4rVUicqdBgQRFAd8KXhW1LTdKAn+pIVl8\r\n/Pg/K9pBkF/HXzOvX5OvBh+XnTs+r5cP+06vbje3VjPEPrMjRwWtUwTnoGTw\r\naztUWHGwtxaNJM8tk89wN4M6fjLI7o8Gh9NlHOiS3qfZ+juh/SrEReOo5bpB\r\ndN/HPce6/DWBv6pgL5NHWzIblDGUwEgtzJQCWd07p5YW/0kJCiVn5Oeo4jHX\r\nyGXO4FJTaujoSwzujrQ5mx4phbbfCDufwGo=\r\n=mTHi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.18.9", "@babel/template": "^7.18.6", "@babel/traverse": "^7.18.9", "@babel/helper-function-name": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.18.9_1658135859898_0.2841970486279537", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/helper-wrap-function", "version": "7.18.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "a7fcd3ab9b1be4c9b52cf7d7fdc1e88c2ce93396", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.18.10.tgz", "fileCount": 4, "integrity": "sha512-95NLBP59VWdfK2lyLKe6eTMq9xg+yWKzxzxbJ1wcYNi1Auz200+83fMDADjRxBvc2QQor5zja2yTQzXGhk2GtQ==", "signatures": [{"sig": "MEUCIDS2sXfAFgecuy30qAtayqn5x0gAcgbwiHwSqcA4wKsLAiEAvoEGPdKD99SsEMWLyqpmD80Ds/KUXDSxD6A3PiQGCas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYHA/9Fl66WLxMdj/zkCuIdDi7ZKAC+/h4+zoZKldhgHFsrby/gnrC\r\n4+QTXWShDXWmOiFsY8tOa6EVljnEHMbovnG6HnsyP7EDUuoI4fQDsaqqAtRo\r\n2vXDm9CHIbSjaFqlBZqZOc4OnJRfR78tUeMUqE/okf+Xy7//Z+DIH0YUri7H\r\nwJX4BRXVqqKqK0Y8xTXGLfe4lWfpq1u3hetMojy4HO2oCgIjSMSNcvhGRP8k\r\n8oIcZgO0xPzjhZy+VV57UD3K3Hn8+Jt7k8Y446El76ySSdz+Rh1VRdklo3Yk\r\n7w3NDP6vtCCJ4cp137z/31xiHcggmvf+ld2HUOhL/dLua8vTfPw47O4D22fN\r\nsBxxD76WlE+k/pdlILQ9X5DOJUR87ymw6NR/0VsJjxowdOVK62a6anaghr1l\r\nZdvm4+DZbyMsE0WSQPKiwD+0Bf6YhKeMHqfsKVi/WHVfppi2wMZzbwEDwCyA\r\nol8eXCWt/mppKuRuimzLvmuWsF9PeR6XVggJzXQr5pT96rcTfc5NPO3kqk6O\r\nczb3b9PwEE8j7ahmQufXrAp4s91N4CIHKsuHqhG2FJ9EbSTZjOID2QPe7sji\r\nv9QkFtGMcua+jNTtHSBrSokKWLdt+/k2i77hePdA+CfEChur/D69EgVVuMyi\r\nq4yheCgqMdyyTIl55OMPdz7MHbopwHc0qCQ=\r\n=d4TP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.18.10", "@babel/template": "^7.18.10", "@babel/traverse": "^7.18.10", "@babel/helper-function-name": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.18.10_1659379607802_0.7590047593876252", "host": "s3://npm-registry-packages"}}, "7.18.11": {"name": "@babel/helper-wrap-function", "version": "7.18.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.18.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "bff23ace436e3f6aefb61f85ffae2291c80ed1fb", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.18.11.tgz", "fileCount": 4, "integrity": "sha512-oBUlbv+rjZLh2Ks9SKi4aL7eKaAXBWleHzU89mP0G6BMUlRxSckk9tSIkgDGydhgFxHuGSlBQZfnaD47oBEB7w==", "signatures": [{"sig": "MEQCIB5b4SSIk9Tx24eTDvpFCxopUYKi/Dv5mbA28KTqo91+AiBVzf4lxl7pxus5l3/jV3njLRrfOp3WUaZ6LGFhD09Y9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6791ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5cRAAn76rgbYn9cvAwJe1+rUfxTEcjKDrLLKUDc+a01zzTX/g09hf\r\ngT5rGNHEaSiIZER6DBW/fE7IXvuPkMftNpwhNVeWdxuiWdstk9t+ijtGBao3\r\nXsxVGrbFBLcA5pn0OmWImeZx0/k8ECSmtoxGClIxRaPdugTB1Rl8Ny3WtFWH\r\nYSdw3COLUIF4LBnZzcHxaf3rG6sWau8uuBQR7C5dUjY3pkCVMvCUeukd0L5d\r\nf4oeq0uARacNj8TtagZ84gfYSGwmNIcwD9toE/FS83dQZ0WR9BM6VxsMsX6v\r\nzF34KDi1mKozgCmT/7x3BkGAZf+czF3/sKAbcOj4ojnQlFDjbm1NYZZDP/bG\r\np6bQpfXAIaSnEHHqRFQhHHJkImTmIkVTMtEIycKb10jEf38mKLZM2o/2WJFe\r\nbcadsrwvP1Y5gRaTl1dg+ymyJ9RQxYPgRzKQdcpRrjOvjrWFJSVDH/xkvtcu\r\nhm1UHhT+fRyPEM4LsaQqTNyHMml8GHWQC1det1Dn9Q1gcyePzTu/I6l3rZVL\r\nWcpKlhF4uDSOxFq+yXO8a/7amM8TGnz/+evuhZORWZby7A6WHJsMUZlr6Udb\r\nyw+KduGkZaYyfyF+T98z9LSJum0E6qIayRy9rz34mtY0NDGIypDotzyuRmys\r\nGBWGre/tvY4jfT19z2N+A+UJVwKX31bU0gI=\r\n=FNTj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.18.10", "@babel/template": "^7.18.10", "@babel/traverse": "^7.18.11", "@babel/helper-function-name": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.18.11_1659617141045_0.3893972294292125", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-wrap-function", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "89f18335cff1152373222f76a4b37799636ae8b1", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz", "fileCount": 5, "integrity": "sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg==", "signatures": [{"sig": "MEUCIQDmQpLVNPSJSoe7fwb8yInQsrWzAl6u9Eq5/KgW70MycAIgQxbxQIcnBkl2fbFE3QTfpXrmmu2l6E+gc20p6w/VSuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphQg/+IwuXmDsGyRZ9JXmQiWMgcL0FPumr/vWGunzYUoX48hAxA5l4\r\n+kNMW1I7IF3+0QvAqPT2KREqmg8VZQOkDiVELbq+r0cOhtgiB77irvd9B6wa\r\nhvVeRVcckiitQd4wLI/onjMw92U9QQyWX+tb7XuSovXVqQ+ARD+SlcqffdXe\r\nnaBIt6aTa4t4eGVeQwPpKAu6e73m39LqlX3Tx8nwc2UvEQ8jEJzXCE0VmDe0\r\nHhKX7Y/zZkfU2sF3lAssUZIZW+uDe8Brab2z48hg6pDOVmKXSeKd74xNwy5f\r\nU3dPijjsFSWQ4v+T4UiZ/HopsoBAtyNfxp2mI4sdSZwAT1YvzXDeAUeobUZT\r\nHevFCE1Pko3EVsOoCoJRLB3yEBNVVTqtMz1zy1RpWyf5D6AW1LfDP8M7CYS3\r\nL2waXdwzmUm4b73EktkfB27PDZDqw492yVEtdIIi9P4KR0KJkfV3GUoIbw9o\r\nZbYVIJPu3g9OtQqxowz0f/wiaL9Om/920K+uqdndlZs+kzHygGYEufjaOKch\r\ns0HK95UrP9pEi5c2iS98tpicEBIiLtkMBmvQtkgEBCtR9Hnuz0k3Ak5TvmP8\r\ncEWVKAZ1dxXCU7RPpZRZKoTBHAgshrONXbitCuzAkSsG1ItVPtkc7SxPsvF6\r\nO53SzBE+/dlph+hISFb9BDPn0Rocvh/bFPE=\r\n=/nQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.0", "@babel/helper-function-name": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.19.0_1662404541213_0.6705701029715878", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/helper-wrap-function", "version": "7.20.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "75e2d84d499a0ab3b31c33bcfe59d6b8a45f62e3", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz", "fileCount": 5, "integrity": "sha512-bYMxIWK5mh+TgXGVqAtnu5Yn1un+v8DDZtqyzKRLUzrh70Eal2O3aZ7aPYiMADO4uKlkzOiRiZ6GX5q3qxvW9Q==", "signatures": [{"sig": "MEQCIB6fITYQ2jAJVs6bQ9U9V2fvkqj0ClyVGnqb8+RUMXVMAiAos4WBt34cpGY2xZBSo3MnLQ4i/5inTmGD3eqZaLtwLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6uw//bkvnOLJxDtnriEPZrcCCMy4gaNGwZvwRgBd/uR9T4B92ova8\r\n13tnC8SvNRUQP+4mbvdswuOij5GiD/3IR2A2+Dt4PyXJHjgZ6Pe5lGWpD88j\r\nutFslKhub69mpLIrakotDYGJo2P9/S0taFA/8M5l3yxj4eQ72cheOtbTc/1O\r\n9WDpNchDdWyCaleFgZLEIsWz2XqYnzj2r19ELRRnETg7bVVHzS+vkBCo0EFL\r\nj2VIxkO+vS3Qe0QCfJf5RlJcWq4lD9ndi2fOqXsWEfUHuE2hlQH5TF+PV0p5\r\nP+/0yLExiH+K8cdr0PM4KYPYFzJFR4aUY3i2ZJNZZaCJeDEk0vm+XUpMefjs\r\nZfzmfIkKyLOpGcH1qyF/cWIxyXLPteIij37yUTxbmJbNn2k5Jp4S+duAv6AP\r\nsFo6bTLCphjnEkIbSRhe9qx7Zv9j0L9TDH6iBaQ37X0+mMFcy5K5iiABWdQ+\r\ncQ7TzeRRX17SPEGv+96HeaLi9D2IWxAo3T/SaNUYhn4HD5TImOS4k/yo+y4Q\r\n5++1hi/BiIyXcr0GVb1ReAsMV9nR8+lmg3KlPhJOnJEZioPrAWbFkTxnKO0K\r\nI06B568Q5ebT6lIz+MZa43CMxSJ9/OguJymBQ6X5wajBw55Dy0qUuEQ+tod2\r\nXqrcz840qc0bmqORBXn2FswtYS7JZPWoTwc=\r\n=N6wH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.20.5", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/helper-function-name": "^7.19.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.20.5_1669630373908_0.1457885204783249", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-wrap-function", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "6abbd2f711da464836b27cbad7d049c3c0333075", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-l1PXGznVvpj1zHF3TyWEd0S1w3Vd2BwO+f+ygPALT2xDiwdumhsX3XOos7/aK+ougr2iVYdHlPE9J8jrPkdw2g==", "signatures": [{"sig": "MEQCIAX/VFD+3RF7+z1zzvpEljLsDgZJtr6EndI5V0JuzZzbAiBIBM4BtaWAGLXrW02/RJfIbLPSP5sDCNbezbmpzsOq6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCAg/8CmjzIQj4s6J4fWH3L6jmv8Za0Hxe9nrTcZ0um3/SfUOqgsfc\r\nmlwf8zE4nt9DrMvYaoCmOx6S1dvEeQj5DArle3uE4mq+MFOCVT8btp7IhrZn\r\n9Z3YNIEpmn9OckmtlzxVCiKM/Zvqw5lq/G+ng6iTpkmLLhckbEugMFsdjVWS\r\nuwW6fTgmJtw8KCEdDJWFHZwazkP23YI0LDzkUBBktvlP1T8h96RK+C9ontx2\r\nvso6ovHVso1ppcPhQAnPCGlrg9PRwnXcHQqV/8ZT77lEcewYHy1p+LzK8cAF\r\nSrORenV4siYmkOFWupBXJBmZGmpXSz+HR6D6qmBH+7s8GufNFDZrM8d6ypJL\r\n0kDws04RhPai3FXJ7BOCOuD9QcWgjTLv5JdTcufg5TqXvCRW/27aZ0u60dYk\r\nw9UdZAQZbLBL4Lt7cDN+q5n82AEi7mTAYE/NmwRkYSsAvED+uWV8IEmBlZDy\r\nZH23d0/YhQhZiftNvZ56XkSofb+/nbx2f4WH55uG2NgbOFOAqdG3y6U87M8j\r\nLKmS8g8ojuQVnuE4QtUMiyMXqlm298m8Srb0X062i59mnqJ3/Du5Upk/z1xz\r\nJhMsh/FvGJ/qeewETY4M1Ahpnu742kVQBG8zJjc2yq247vhP4c3doM4B1xSn\r\nmrwu4QIyZQUPctxUha5fubt1Y22rnMaM7Zs=\r\n=LWmr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/template": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-function-name": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.21.4-esm_1680617394219_0.8074830463323561", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-wrap-function", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "a21b0826846d38a75e89c26fa63b016f4a7e1505", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-A/1a4k8lAdC4IP1AD5a1CFMQZyXJALQ4LOG/mUbW6znR0QLJDL/0D5k+txsO4Ujw821EP2Lex23MhdGzbvkilw==", "signatures": [{"sig": "MEUCIDPOjHY7M9AfQlQmp9RP1pe8rAqdac+CvUhYCnc3YcAiAiEAsdXcEUp6I7qHiKGE9T8ugPmRV34CycOReastMP/Kfqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJ/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcKg/9Fj3h8wMqMqv/4sfv4LHCWznZ6N2UltaK/POTm5JykHXbmDRP\r\n7E06DgZRHeZP2PHveuslot/E85I+yiUzSiLunBnHmEKnrcvvGrI3+sp8BX/D\r\nY4fVc1+uM2DkeJ+b6yDrvWavXiUPaaA5Ms2rg5Fr0q3AeqdNXVg7s++e6e97\r\nk9xMJnf7ucOmEdKfIO+TW1G8GGBEQb6LPj+aaQTfqgnOd/ehUxeQo1FjwsPZ\r\nCE+T3dsvI9+NVIq7ov7OzJLn6IDL1BBUxkNgaysZ1q1fvzK9ciD28azQDptv\r\nnGk80n+k7W8fgwFa1lI+Fdn1P9FKT0gcSfEtXRN/7Gwt+D9andVgwLro/jTo\r\nvZp2fnAMtvWQIqf/s87fAUyJw8Xu8VpBnSviJVOfVExEM/OGAc5n8mAk/Zok\r\nPoVJrcLwLPaR4h73d5c8LHoUCNJE2NZWAbbDVvM6v23tx3tIA4Tk837l9F+t\r\n1zgXoXG806hzNxNFJbEDJxP91FSdnQZTqC1gO06ZpY22nDKyx/cC4GKoQ8Nv\r\n9PQ/sWaGWM1E9UlNxXzZYl1YNu7kFaJ9d+bzU4f7qeX4JuXU7KXcn6+hk7Ns\r\nOjQZxD3azoF2H95XmYxPMM8BQiaFFJR6p1XMhcI/8YTFgFqLEGIfAgrSWUPa\r\nVNtb8Ok35s4DBr8vCOATFPds4VQebEFm1eM=\r\n=lkzI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/template": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-function-name": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.21.4-esm.1_1680618111709_0.12679334348007032", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-wrap-function", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "44949138519b1ecf4e84807e63fe6999b5fc5d25", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-UtyG2ZukozopOiSR/8KYxoAiHAluScbY5MFwM+sNW223NjC438ay1pIs0tKK+Osw0uBQeVzwV13IXzeYm49yZw==", "signatures": [{"sig": "MEUCIQCfJDB3nj4BMLg+rIYUE1P4WVpjNsk7N+7vEZ/29ZTQuwIgRNfIEgX+xUnqp1q4mo+8MZNtT5bcsmDvUTzELHgAeNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTBBAAmDdJ8UwHgEFFN3o95jU8B3cqL+pYrOET/kKiOGoJVCLcChf5\r\nxi8nA5EQvJTUerLFZg+LbnFyxpn1UsqoqbA+mvBJm8i90SCYhfhQho9fwTXU\r\nh3l+OACnAWnejDd/J7WqZTOurF5jCFzkTr56m5H1uFzH0kPMFanMIAubV7In\r\nYDMmjVjT7glstl29j4AP5zLAc4SeYuqLvgrLe/SNM8AFMk/96tzclhhQEX0o\r\npF/1Mve2Sesc0zuMPdAir3EueGqRM3fBdV/VYVrlNRJny7PDZFP4OOkDJl03\r\nt6BBZe7BibANIOlOICSW5laxw9eqbfE7Pmudn67jr1aScXRg1u2U5iiymNmT\r\n++QWZZsWmE4G3v+4RnWuLyNGNXeQ+qGST1/XnM4TEm/bhYghumkdIoYaU0xY\r\nr8blfoYDkG1WLd0Hz2T5X5K9mR33Sj+pupN3jZpakv4LUKg69xMoapHseej9\r\nv3S9uoPEFrXCQJbuN2G8nmuo07CS1ZSy/g21eCW5gDJNXaaWDPVvykg41+E8\r\nrMn4xSi5yYa/if7Jszt28gCuzee9hSTPHgIMd4m3lcuZ0fEc1nq/H/ndk+5e\r\n3H3Ovb83YyoqLrBBfeFnToce6/1FLI7M6UNjUbF5R8qONdedFOWkxaaTrjgT\r\nH95CxoqlMd15X0yLY9Jpv8uwhPFLrsmzBtg=\r\n=zpVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/template": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-function-name": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.21.4-esm.2_1680619195060_0.13193858781821288", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-wrap-function", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "1bbfe1586d406420dc497746bcd8cd866250ac8b", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-A3MLX/5/zqQmIIPVylDYlEwsFIOVl//Zl1Ro02Jsifwy1Exo27mdOijlOR9Nd2kFgw8CArcfqMZHDKfTgs2f0A==", "signatures": [{"sig": "MEUCIE30BiswTzCxRXaQ1+PzpZ+0zw4PATyyeBBGZOzo2oziAiEA/DWbegk3+y+Bs0ZcNyyVCpyf7t6TUUZMl3eEst1eR3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw6g//fUyianObZgZkOvgCAbUBBmhYKG1EkYklth2xvJGoC2sPAD6Z\r\n99tTbfUxJyN9yutqqYZ2/b5NnqDs4TgGyCTKKHK9fRTV9cA+iKV80a/KwhWF\r\ngtoNySaXcD++BuPhPLbmC/RtnHBooPYso1E/jEnzzPEVz1zE+m4E4ToY7iWx\r\n9C7aKMrpFtKXYDeycqbdrZ7RMIkfwXNnY2oVWnQb/UhjWYgiz4L6bjHs5SVl\r\nLzVertWwkgjMwqfLq8zuoOW9wIHrkeSJC0TbWwUErGpJO013RaV066o9bK27\r\nadkEpVKY4fqofpXEThvwrUkhbdL/xmS3N+86UqsMAR2iWvXPAPaqfSgujjS9\r\nT4203pJccZ4EFjlnSOFUUYpIvXXoDFM8VQtfgZW9FWLwM9EsBGlc5ybJ2X36\r\n7zYj16zNnyL0NN2TRAfaBmXX54fXT/t7Io97jvzMO6uSDmtk+NxhW9P9A2iz\r\n8QMGt4Kj/xM6keVvrE/QdbMcDFLZ59QXQ4oN1QEcUcjaXB82LN38EW7oGEK3\r\nCTTDCIq+8CosFq3qbRON8oM5UaybGbgbiM7YuKI+kVG0Z583V05Cy7qcWbiz\r\nCB819+R7sl5bnU4yEj+ZbAzHzHf/SGGVhggZK8jgFkWh/ld2ibeJ9aav5WOB\r\ntDCXcMGL7JeLVQ7CusjkgSK3BN47e/yM+KI=\r\n=XttC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/template": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-function-name": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.21.4-esm.3_1680620198972_0.6773535962099397", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-wrap-function", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "50b1fd95e11de7e451dc5b73e768215e53ed869c", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-p31y3pve/NY13+Y2hUByDXN8I6djb+MJVkV3fR4rsVjvZY/rIe9iIR+3g7tfa5vShgtXLpTctlcG0QN6V21YAw==", "signatures": [{"sig": "MEUCIQCIg7rq3syGnN3cFrtaTAb39nUG56XPs+nBxqsMlswWdQIgMiEHG9ynlFdkDAthggH3kMrgyGTCGTAYFkUg/MuSBJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqQRAAkV0VoMLPvamvE7icmMddHTsqa0ggRLdeUO4k6FECKwKwx5pG\r\nNe2kt7dlCmRasYmO/7nB22y6btCGyQ3ve72SCOp6c8Zipe8fvkfz4e4YNDnZ\r\nM//4gSddstvDLdmNb06RneS/9v05R1KpElLjege2NYNyPNVRYBbZZNj2Wcts\r\n1rvPTIjZOzBnObT7LftzuTICbJWuDycWv2Qjm2hVKxgQ4V0o/5Rk7vywb7Gl\r\nXMNBK3M2T/g0dJ6ng+Yk/F8YpRE1LrWvSDVAYhn/haICkm53xVOndpRIFdn9\r\nkOMsSAfFQ/UDPqW0ciY+zn4cCr76sorD3Dqdjl+9CAy5W8RnUsKa6Xla6NNH\r\nTV6nZtXsQVX7HhEMQBWaQAXea39pmBz/vYqfzju3bA8DuQ8d2wlMUSuy+DSf\r\ngnlQX+GgnQC5qVl201Wj6ikwNvrxDsLaYeISiK5AmaOg2KfWn6z8y/ASiXMk\r\ntkpzbPo5PU7G1qOuqZ12BOP2hfjz2CaWfJ9aXro/L5KWZQ7qfHz8Ytww6t8A\r\n5nMgrrkdGD+gIuTzGXArCo9b1e7U3Q6QDb+Wj2nL7D0aM/u3xvPg2wNlsoXl\r\n/R0WGI9jx+mDEluKUaIchyMpIj2LNAOsNxuEOaIenLjZjxSlRMuxqFpklwCJ\r\nCydHRYqZNcmlxffF93lIULTDueZwbFMA1oI=\r\n=yBKY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/template": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-function-name": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.21.4-esm.4_1680621227350_0.16477308092587029", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-wrap-function", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "44d205af19ed8d872b4eefb0d2fa65f45eb34f06", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-bYqLIBSEshYcYQyfks8ewYA8S30yaGSeRslcvKMvoUk6HHPySbxHq9YRi6ghhzEU+yhQv9bP/jXnygkStOcqZw==", "signatures": [{"sig": "MEYCIQC7kvlwnpwGbrWz1nNWPKR6FNCbxn/ckNAId5rUwRl5NAIhANKjEZxTGRNvDm8nIU7YFs+qBBegdbJ/XHaQCvABi08t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15375}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-function-name": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.22.5_1686248502482_0.8741349233882942", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-wrap-function", "version": "7.22.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "189937248c45b0182c1dcf32f3444ca153944cb9", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.9.tgz", "fileCount": 5, "integrity": "sha512-sZ+QzfauuUEfxSEjKFmi3qDSHgLsTPK/pEpoD/qonZKOtTPTLbf59oabPQ4rKekt9lFcj/hTZaOhWwFYrgjk+Q==", "signatures": [{"sig": "MEUCIQD6kYl/mqSR5pRkNkUSDE4S49SI0TpLhsB6PpMOgfovcgIgSVvmAip3RNj7C7d00Gpj2Fibmm6fx2s5Asus5geuZGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15399}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/template": "^7.22.5", "@babel/helper-function-name": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.22.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.22.9_1689180813107_0.17045303010533264", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "e2017c2c242993d168ad6c2496d8e5f83d54d92a", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-5Gaji2i9fdFhg24j2OUE4eGNWVTxbHOiCQs57EhhQ/AZOFtvno6RDOr/xrX3MRCyR/FkWzz+U6JwYM54eCQGCA==", "signatures": [{"sig": "MEYCIQDrHgOk6MOrpiezvCjZDRMZ8pxqr6Z1sjyHSJJ+JYEe7AIhAMEfXAlUbPA5Imvcn20mphTxTSbNaCMaEdAL6lw3Q4w2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0", "@babel/template": "^8.0.0-alpha.0", "@babel/helper-function-name": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.0_1689861626612_0.627750741760789", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "0e467d8074bc32bdd88a13f0097f4dca24d9ee9b", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-Fe8oiTuB/e+4SivISPxrfdK9Sf/H34PK+Buv7z7g3NQ90GVMURsI0YaCJ65U/RsrH33RR1Fcih4dt61RUj9qUA==", "signatures": [{"sig": "MEQCIFJr+ZOxdHAQyHrfHerfSOsprEnyKghwFHH9DyQ5RyEEAiALGqFNad0Cfy47THXvVofS7ENBB977IUa2FAPT05alkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1", "@babel/template": "^8.0.0-alpha.1", "@babel/helper-function-name": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.1_1690221178923_0.7339254870935874", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/helper-wrap-function", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "d845e043880ed0b8c18bd194a12005cb16d2f614", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.10.tgz", "fileCount": 5, "integrity": "sha512-OnMhjWjuGYtdoO3FmsEFWvBStBAe2QOgwOLsLNDjN+aaiMD8InJk1/O3HSD8lkqTjCgg5YI34Tz15KNNA3p+nQ==", "signatures": [{"sig": "MEUCICuA4olnkSsEm+Dh9nHvJ9h3wvIeunR55fIRb/3w5w/EAiEA1NRP1o+/BYkk+7YJvCkWnHQpkSLwnivdacckPzEQ6wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15402}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.22.10", "@babel/template": "^7.22.5", "@babel/helper-function-name": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.22.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.22.10_1691429121690_0.5615453131442134", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "366a9f6274583d6c680e809335b63bfc99d83c6e", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-Ee+o5ntCcZq4AOHjRDEhNThdognMvDfxJJsRW3DntUGOvgrH289VX1I8lP2WG/vNxqUIMqdl0YxZV6cAF9hyog==", "signatures": [{"sig": "MEUCIF3V/rPC6NIR1ZJBdoXMDRJ0y4floo9K+MIKMMnGEdagAiEA/8r7a3NMEqbRpdEp12R7F4vXwEjKFzbFAwlZrTJvNJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2", "@babel/template": "^8.0.0-alpha.2", "@babel/helper-function-name": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.2_1691594121920_0.9015825284379981", "host": "s3://npm-registry-packages"}}, "7.22.17": {"name": "@babel/helper-wrap-function", "version": "7.22.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.22.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "222ac3ff9cc8f9b617cc1e5db75c0b538e722801", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.17.tgz", "fileCount": 5, "integrity": "sha512-nAhoheCMlrqU41tAojw9GpVEKDlTS8r3lzFmF0lP52LwblCPbuFSO7nGIZoIcoU5NIm1ABrna0cJExE4Ay6l2Q==", "signatures": [{"sig": "MEUCIEcpcpm5wo6u0CqKIkMEs7sTf4+THRqDrFP8LHdwT8oXAiEA8/7btSQEr45DjsAx8x4zGGiJcHQOCQflXKH3LQvKWjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12839}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.22.17", "@babel/template": "^7.22.15", "@babel/helper-function-name": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.22.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.22.17_1694181211359_0.5848599471721823", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/helper-wrap-function", "version": "7.22.20", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "15352b0b9bfb10fc9c76f79f6342c00e3411a569", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.20.tgz", "fileCount": 5, "integrity": "sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==", "signatures": [{"sig": "MEYCIQDrVVWVHd/9pxnPOKm5W9mMyK2j608tVMZ0QmcBpTPS9gIhALJy8oyZ7dy0gDC/4XKLar+DN9CdPzPgVpVAe/c68FNF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15403}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.22.19", "@babel/template": "^7.22.15", "@babel/helper-function-name": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.22.20"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.22.20_1694881716229_0.9813767865798178", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "75b4e12ba2a23705a6982c74bd28c7763790c614", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-VdBTTxTe3L3rLY3+Z9XpyXqoAkUcC7PNinfrYQ34MRN5bqP21P/kOn5AvN2nOseHAL4kN+ED/MMYyTB9smdDCg==", "signatures": [{"sig": "MEUCIAYAlN0LlnpdVj6Cvrb2jWsCoGYnGB9XfEXDHnufadJ6AiEAiO9OFhcj2uz9whctrVOFxvKG1IsMnLyObDsn7kJsZSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3", "@babel/template": "^8.0.0-alpha.3", "@babel/helper-function-name": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.3_1695740255322_0.4015875282524204", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "6fc11d963dfa35474253e6ed9cd9a43c216c5889", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-4Et2Y5wuAFOrDMz2bf9k8U4e0H01YCCNF8tjUmebMn4TNN1gZd1fSdyXGMOYU+mK+rtXUr3fprYUWjnkUSvVhQ==", "signatures": [{"sig": "MEUCIQDnWExowAnPlc8ieg0ve+ucJi8rVrAEv1TMeG/RLe8W7wIgcvywo+veAfNWuuNKBuwZqW7VRVXbOVHZ1kY7Zu2hD8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4", "@babel/template": "^8.0.0-alpha.4", "@babel/helper-function-name": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.4_1697076408275_0.971702322990923", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "7c25e8bc544388aa8ca07147dec64cd7435c5f89", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-8o8+APV1yhR/dE2HfewI4jidT32aqVyGr+8/yqTfxVi4zStdFcgXB4Lgk9awJ7VsnCdXIw4vn/i9jaC9XYoUhg==", "signatures": [{"sig": "MEUCIBgnuPwt4pIdkuQG9DMJJDuH96Be4WVTRplX0kdW10paAiEAuh+HsffUBVVPU/QPJ5QaM8dlmFbwnxUra4HmP1ytRMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5", "@babel/template": "^8.0.0-alpha.5", "@babel/helper-function-name": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.5_1702307981577_0.15251749279287052", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "3f94f2fec625e3ff46482e859c6540a0b02199d1", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-9dPUA5JCf80jeFg9H2LVl0IV05/rniFW3ZJJXHRuSYtMqmSZsNiJDkyB6T5mqe9p7KmpLvw6RnUTBtFB3hmpzw==", "signatures": [{"sig": "MEYCIQCDbrCkYVjLsigAC27lZYjeNw9nNlpfnMLVLernCtlwsQIhAOLQgbMIE89f/jdKmWbk+AtFR8GkqDiIZ859XCm27hiQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6", "@babel/template": "^8.0.0-alpha.6", "@babel/helper-function-name": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.6_1706285680483_0.6044211526513599", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "3b24ba18b1bda191469f8d53360d549859825fb9", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-9/hP65Umf1G5HtH9EGNv6NRUN71fG46sm6iP7juNIV8gNuMM51Dodm/D1ql1olMDKzPwWATqywS3bf5zcDH3Jg==", "signatures": [{"sig": "MEYCIQCBnKvCx7ZEcwgAOwLUC6AQgmohYlXB4ycT4DKcQvAb4gIhAMYJ7Dy23sjC+MWk6O2pK6uvFXCOiEEGM2BNODAjQTOs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7", "@babel/template": "^8.0.0-alpha.7", "@babel/helper-function-name": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.7_1709129142508_0.41545823253775627", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "755cbc6621f4d68e32e859ec954435387f879ed8", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-jn0hmxDHVhE2ZkYxpNrb6LClJW4Y+eEPpJMrdJD9TRs8oEspXsIgDtoiZAKbQdSDeQFwHLJHPl0Q719570OVBQ==", "signatures": [{"sig": "MEUCIDrL8Dh1PCyCCOexLOzBupqUwKDWE1TtVgsQAEtkSH8CAiEAxC9cVlQ0vvMESVskUAkH8yXREdjEY98E8YfDDnZHh40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15588}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8", "@babel/template": "^8.0.0-alpha.8", "@babel/helper-function-name": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.8_1712236818521_0.0417325246058915", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/helper-wrap-function", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "335f934c0962e2c1ed1fb9d79e06a56115067c09", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-/xxzuNvgRl4/HLNKvnFwdhdgN3cpLxgLROeLDl83Yx0AJ1SGvq1ak0OszTOjDfiB8Vx03eJbeDWh9r+jCCWttw==", "signatures": [{"sig": "MEUCIAvq4+SwBygdmERmLplaWYeevJRHZhSoypPzwPZtGgsDAiEAtpht19BKkDmtsT6XwJ/A/0a7OUtj+AHU4+ee6zqm2/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73571}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.24.5", "@babel/template": "^7.24.0", "@babel/helper-function-name": "^7.23.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.24.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.24.5_1714415663711_0.5799455626556773", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-wrap-function", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "c27af1006e310683fdc76b668a0a1f6003e36217", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-f1JLrlw/jbiNfxvdrfBgio/gRBk3yTAEJWirpAkiJG2Hb22E7cEYKHWo0dFPTv/niPovzIdPdEDetrv6tC6gPQ==", "signatures": [{"sig": "MEYCIQDzq/5noPdb1cGKdVoVyggkvaZPz7/0Uqfy3EVWxIM91QIhANulzBXKOZos0LeMr3Kj/tuy8l+zPMyG7PCO6dFLlAwq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73742}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.24.6", "@babel/template": "^7.24.6", "@babel/helper-function-name": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.24.6_1716553509147_0.4087302516672626", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "6f6d2c014f3140b2f70d2b271b68db3281036656", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-/5r3Hcth1BEi3lzex65BmOYyGctYlu8vzR8/4L8eHc7dfXUsmSKPUwfPjf6qy2Nk0GU02YS657ZLa9s7Bb+ePw==", "signatures": [{"sig": "MEUCIQDI3/kN1YTr+mK6d7SuVOlq2ZNl3vGC/2Jmd2Kt0oJ6mwIgETg5Iv4uPll7LrhR3c0XeEzUpb2sQrBXc91AveOEpBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74207}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9", "@babel/template": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9", "@babel/helper-function-name": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.9_1717423514495_0.7463039341142073", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "6e69939603aa53b9e0597adb6dbd85dda645430d", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-iRvK1UaXBavUzyZbjfoaZb5uamV24RDMEKMdq4w2GYkSzGxZ4wCf74a+xykvEdydARhTZViuLAnSLMEMx2GSbQ==", "signatures": [{"sig": "MEUCIDDAVDAzcF8r/GLQ1Bx8fd3VLAdeglG7Itb8WwNRTiWgAiEAlZBXJ3cY1m+TgAmALY1pGPZ2CVw13t4VLeYZv5Zcblo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74212}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10", "@babel/template": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10", "@babel/helper-function-name": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.10_1717500032953_0.06969438272995698", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-wrap-function", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "52d893af7e42edca7c6d2c6764549826336aae1f", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-N9JIYk3TD+1vq/wn77YnJOqMtfWhNewNE+DJV4puD2X7Ew9J4JvrzrFDfTfyv5EgEXVy9/Wt8QiOErzEmv5Ifw==", "signatures": [{"sig": "MEUCIQCT36gb/sjK9lcGgFzpqETmPkt1OmsL5JhnjhsW5CX7WgIgU3dbxzMgcqbc0wye0tvLQa29swTcDN/lj4EKnw1Wfqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73715}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.24.7", "@babel/template": "^7.24.7", "@babel/traverse": "^7.24.7", "@babel/helper-function-name": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.24.7_1717593347183_0.730542668354891", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "f86f3f045c71a3680dcdbbf340472d76b2d368c6", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-X+FjMx29QW+wwkMRZCvkjC1L/MvjYQUNRJfjD4sYqnBqfuXbhEeqi5y5cIHEJ1tArVqImo+FJHRzBm9RWP3dqA==", "signatures": [{"sig": "MEUCIHFv29P2O9bNShQxPxfK01l6WLSlRjlB+XF5cuKTTCfZAiEApu5kjwI7lRz3YP3pMD/udVnmTRXKtDvnAOTHJ8Rd1XI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74101}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11", "@babel/template": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11", "@babel/helper-function-name": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.11_1717751757653_0.46534497806482134", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-wrap-function", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "dab12f0f593d6ca48c0062c28bcfb14ebe812f81", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-s6Q1ebqutSiZnEjaofc/UKDyC4SbzV5n5SrA2Gq8UawLycr3i04f1dX4OzoQVnexm6aOCh37SQNYlJ/8Ku+PMQ==", "signatures": [{"sig": "MEUCIQDHwPeGRqWneG7Gr0njz4cndyMs8R0vp/XfmdXe9qFgBAIgb7cA3Ew3wsTrFHHUVC1LH8RTcGW5u34S31iTlcDHYxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71273}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.25.0", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.25.0_1722013169048_0.25293486826834366", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "82a7965539de7adf9be9309e0bc96732aaf11f0c", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-AVWNTHjc4TdLyfCtZ92znCZWd34YFbE26fiC1y2ix3HqPYeaCVteYgelNKYpdBRlgk5KJyphmMLhFIlqUJ1k1w==", "signatures": [{"sig": "MEUCIAdMnv1Rj5JpY07iuMWQc9ZcH2LgrKHq2AMbpSd8DWDfAiEAwtQ5XbcBEwyGXHa8fshQTY2M83Lt0GS0QnAk6PZnz+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71332}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12", "@babel/template": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.12_1722015230526_0.9774627133092095", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-wrap-function", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "9f6021dd1c4fdf4ad515c809967fc4bac9a70fe7", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-MA0roW3JF2bD1ptAaJnvcabsVlNQShUaThyJbCDD4bCp8NEgiFvpoqRI2YS22hHlc2thjO/fTg2ShLMC3jygAg==", "signatures": [{"sig": "MEYCIQCBuVN1ekEKNWsWnfRtW+y4NXtKO+Cf/mkJqjwqTNceZAIhAOAt9CArsmSnNkmdSJwHzPfcEcqwl2Qg62oE9gYcm8gv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78966}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.25.7", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.25.7_1727882118608_0.182939835889959", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-wrap-function", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "d99dfd595312e6c894bd7d237470025c85eea9d0", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-ETzz9UTjQSTmw39GboatdymDq4XIQbR8ySgVrylRhPOFpsd+JrKHIuF0de7GCWmem+T4uC5z7EZguod7Wj4A4g==", "signatures": [{"sig": "MEUCIE+DxqA9SelZShZRYP2g9hgQIwyrUWK9DyzmgueE40tcAiEArdHA5S6WsLlRp9Kq8LOAFEWVCDbtZMYRC9Ry+J5dKDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15802}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.25.9", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.25.9_1729610493957_0.23121458726742095", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "51ef71bdbf9d38aa04b4435d1d407431194bb285", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-eBdBVqrleEdaIDNTF5rTpQE3Y9z88W7mqg60nufQDJg+FGJ5vSlRfU4F9BJiXiALBsXt0OH0UC5n9vigqfP7hg==", "signatures": [{"sig": "MEQCIEy1Qb1u9E3d2CHrFjXFk7WAh8EB2ZGLYTNnE+jSpGpYAiAHo/TRlvqQ8oCaR++z/hY9owB72625Rje3M1fjmWmBzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13", "@babel/template": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.13_1729864474490_0.14866986183633024", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "4ea60eb1b99a2464c166143076f0c22f9f76b519", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-icNccbalYtHFussiNh1Qg6kANkhHqvcZLs2U3mr7rqOODz56Od/zpKdCb3M1uAfsYvOFhpx0r2sMcBoTP0LNrA==", "signatures": [{"sig": "MEUCICuVLnCON/90K8KagKMvx08Jdj338DsdwE2hLLNBD4dVAiEAg3nj5s6jAP+6GFLq6We6IyiCLyi7ecuMc+qgVJBYEuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14", "@babel/template": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.14_1733504064275_0.08457614962100801", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "9db1158222b30168517a2db24083eb89c1a2a7a7", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-9JntAu+bhv4igB9Um6H10K6qLIxWz1pMYY0aHYusHA5/XcMycwR6Z44egH2EnDJZtRL+2IiqGkwlGPV3ULhvDw==", "signatures": [{"sig": "MEUCIQC4v3k1k/wLfqERKEUlgBsh8wtCxg3hbI8/TDTCFzfxOAIgM4Agbz8yFF90sVJBwVUTd3O1Bw+SFT79INGR2XWgEu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15861}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15", "@babel/template": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.15_1736529892250_0.016846939798364824", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "75e1e06eec156f142f7f428ce946845e2c786ac8", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-MRmQtBCzIDkyLtrk5SD3UC+hqzIwkgj4VboasSxiLEYGnMTp4IvaGuhdYaCTpKDMJFnMm7cXGTVUKw3dmq5gEA==", "signatures": [{"sig": "MEYCIQCRuX/YXluD01zAXRGgof7TQcKjgLnqd6o5AJTuVWOhYQIhAONXOy3UL/YCTBZz6rzRmGc9DSRmogZuQWEWoVWX1b1H", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15861}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16", "@babel/template": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.16_1739534368638_0.7727699790963871", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-wrap-function", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "f9a200c300d5c2db262c8e227eddb323578e5806", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-olRRglsqxEvnW7Fi/5Wual66SrrSb7J6iNSZ5Ep97EQXkxQLtp+EBfTlLjzzKZIDzO3yuvl5CsHFhCbtLkIA6Q==", "signatures": [{"sig": "MEUCIQDaGgVkgG2YCmcQBD5TBtal7wqbRTVbirCmeD/YBrJwPgIgK/HtK2h96MBzrdO7WmMqaf2KHB7+4RAIp+tj9MtWpCM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15861}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17", "@babel/template": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-alpha.17_1741717521806_0.9521477798859321", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-wrap-function", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "b88285009c31427af318d4fe37651cd62a142409", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==", "signatures": [{"sig": "MEYCIQD565MdsAeIraFiyvCsUpQLZn0RtZXYb9KwjKI54kwfLgIhAO3z+NK4ZYIVbcOBZEGun5k+K+uIYL6vs8tAf+D236gs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18935}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_7.27.1_1746025757035_0.13254364540826935", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-wrap-function", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-wrap-function@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "dist": {"shasum": "addbc3ace1c244d6ab61de8679a81e71ee92d376", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-4t7OMGecaxbPWLdKDZi9ILQEUkGHwDJWI984uA/YWMFMjtBYgrCF/hPo9DkOyRYP990jC0GPkzhG4rVILNlEbA==", "signatures": [{"sig": "MEUCIQD/fG7GGlhTUagw+tGjCWAomjp36OsYASukmSjJR5LmCQIgOtHt2hNSJOYrTJ5SZPpiU1kyC9M7EhzQf1+7qJxHJ3U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 19070}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0", "@babel/template": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-wrap-function_8.0.0-beta.0_1748620294496_0.2842388692391571", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-wrap-function", "version": "8.0.0-beta.1", "description": "Helper to wrap functions inside a function call.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/template": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-wrap-function@8.0.0-beta.1", "dist": {"shasum": "1bc5b27e0ac1c40e415b71fa617d2b8fa143e7a3", "integrity": "sha512-UUTPBBQQ1SKbelq9iZkpXKgvSFY4RkSz6CeZ9dOG9QszU9ImmUWQPcSjQuFJnc3a4eT74dsMglSvZScVgCkAEg==", "tarball": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 19070, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDixVYCzZxBCW3fZ7D9BTkCQA0pSjbrN9ijFjSWCFtfLQIhAMuWYIn6qnTRBK3dZra/3tLuo24PyBC2xwVufek9CqqK"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-wrap-function_8.0.0-beta.1_1751447077528_0.9427977942560364"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:36:19.235Z", "modified": "2025-07-02T09:04:37.927Z", "7.0.0-beta.4": "2017-10-30T18:36:19.235Z", "7.0.0-beta.5": "2017-10-30T20:57:55.895Z", "7.0.0-beta.31": "2017-11-03T20:04:30.947Z", "7.0.0-beta.32": "2017-11-12T13:34:02.253Z", "7.0.0-beta.33": "2017-12-01T14:29:18.671Z", "7.0.0-beta.34": "2017-12-02T14:40:17.573Z", "7.0.0-beta.35": "2017-12-14T21:48:27.130Z", "7.0.0-beta.36": "2017-12-25T19:05:38.851Z", "7.0.0-beta.37": "2018-01-08T16:03:43.070Z", "7.0.0-beta.38": "2018-01-17T16:32:40.977Z", "7.0.0-beta.39": "2018-01-30T20:28:44.837Z", "7.0.0-beta.40": "2018-02-12T16:42:45.335Z", "7.0.0-beta.41": "2018-03-14T16:26:54.425Z", "7.0.0-beta.42": "2018-03-15T20:52:07.423Z", "7.0.0-beta.43": "2018-04-02T16:48:57.198Z", "7.0.0-beta.44": "2018-04-02T22:20:37.861Z", "7.0.0-beta.45": "2018-04-23T01:58:29.584Z", "7.0.0-beta.46": "2018-04-23T04:32:50.144Z", "7.0.0-beta.47": "2018-05-15T00:17:52.356Z", "7.0.0-beta.48": "2018-05-24T19:24:44.228Z", "7.0.0-beta.49": "2018-05-25T16:04:31.843Z", "7.0.0-beta.50": "2018-06-12T19:48:03.015Z", "7.0.0-beta.51": "2018-06-12T21:20:41.791Z", "7.0.0-beta.52": "2018-07-06T00:59:46.887Z", "7.0.0-beta.53": "2018-07-11T13:40:46.220Z", "7.0.0-beta.54": "2018-07-16T18:00:27.911Z", "7.0.0-beta.55": "2018-07-28T22:07:52.967Z", "7.0.0-beta.56": "2018-08-04T01:08:39.265Z", "7.0.0-rc.0": "2018-08-09T16:00:00.200Z", "7.0.0-rc.1": "2018-08-09T20:09:50.475Z", "7.0.0-rc.2": "2018-08-21T19:25:57.347Z", "7.0.0-rc.3": "2018-08-24T18:09:41.753Z", "7.0.0-rc.4": "2018-08-27T16:46:23.573Z", "7.0.0": "2018-08-27T21:44:53.274Z", "7.1.0": "2018-09-17T19:31:02.623Z", "7.2.0": "2018-12-03T19:02:16.685Z", "7.7.0": "2019-11-05T10:53:55.945Z", "7.7.4": "2019-11-22T23:33:46.858Z", "7.8.0": "2020-01-12T00:17:31.797Z", "7.8.3": "2020-01-13T21:42:21.798Z", "7.10.1": "2020-05-27T22:08:15.628Z", "7.10.4": "2020-06-30T13:13:17.559Z", "7.12.3": "2020-10-16T21:14:21.253Z", "7.12.13": "2021-02-03T01:11:52.825Z", "7.13.0": "2021-02-22T22:50:13.839Z", "7.14.5": "2021-06-09T23:13:05.650Z", "7.15.4": "2021-09-02T21:39:49.076Z", "7.16.0": "2021-10-29T23:47:53.669Z", "7.16.5": "2021-12-13T22:28:39.430Z", "7.16.7": "2021-12-31T00:22:57.012Z", "7.16.8": "2022-01-10T21:18:39.431Z", "7.18.6": "2022-06-27T19:50:33.173Z", "7.18.9": "2022-07-18T09:17:40.043Z", "7.18.10": "2022-08-01T18:46:47.990Z", "7.18.11": "2022-08-04T12:45:41.248Z", "7.19.0": "2022-09-05T19:02:21.368Z", "7.20.5": "2022-11-28T10:12:54.169Z", "7.21.4-esm": "2023-04-04T14:09:54.354Z", "7.21.4-esm.1": "2023-04-04T14:21:51.825Z", "7.21.4-esm.2": "2023-04-04T14:39:55.297Z", "7.21.4-esm.3": "2023-04-04T14:56:39.141Z", "7.21.4-esm.4": "2023-04-04T15:13:47.497Z", "7.22.5": "2023-06-08T18:21:42.648Z", "7.22.9": "2023-07-12T16:53:33.337Z", "8.0.0-alpha.0": "2023-07-20T14:00:26.765Z", "8.0.0-alpha.1": "2023-07-24T17:52:59.083Z", "7.22.10": "2023-08-07T17:25:21.857Z", "8.0.0-alpha.2": "2023-08-09T15:15:22.144Z", "7.22.17": "2023-09-08T13:53:31.631Z", "7.22.20": "2023-09-16T16:28:36.403Z", "8.0.0-alpha.3": "2023-09-26T14:57:35.579Z", "8.0.0-alpha.4": "2023-10-12T02:06:48.483Z", "8.0.0-alpha.5": "2023-12-11T15:19:41.819Z", "8.0.0-alpha.6": "2024-01-26T16:14:40.656Z", "8.0.0-alpha.7": "2024-02-28T14:05:42.639Z", "8.0.0-alpha.8": "2024-04-04T13:20:18.710Z", "7.24.5": "2024-04-29T18:34:23.883Z", "7.24.6": "2024-05-24T12:25:09.310Z", "8.0.0-alpha.9": "2024-06-03T14:05:14.643Z", "8.0.0-alpha.10": "2024-06-04T11:20:33.118Z", "7.24.7": "2024-06-05T13:15:47.354Z", "8.0.0-alpha.11": "2024-06-07T09:15:57.780Z", "7.25.0": "2024-07-26T16:59:29.266Z", "8.0.0-alpha.12": "2024-07-26T17:33:50.731Z", "7.25.7": "2024-10-02T15:15:18.802Z", "7.25.9": "2024-10-22T15:21:34.291Z", "8.0.0-alpha.13": "2024-10-25T13:54:34.737Z", "8.0.0-alpha.14": "2024-12-06T16:54:24.458Z", "8.0.0-alpha.15": "2025-01-10T17:24:52.414Z", "8.0.0-alpha.16": "2025-02-14T11:59:28.821Z", "8.0.0-alpha.17": "2025-03-11T18:25:22.006Z", "7.27.1": "2025-04-30T15:09:17.201Z", "8.0.0-beta.0": "2025-05-30T15:51:34.683Z", "8.0.0-beta.1": "2025-07-02T09:04:37.748Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-wrap-function"}, "description": "Helper to wrap functions inside a function call.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}