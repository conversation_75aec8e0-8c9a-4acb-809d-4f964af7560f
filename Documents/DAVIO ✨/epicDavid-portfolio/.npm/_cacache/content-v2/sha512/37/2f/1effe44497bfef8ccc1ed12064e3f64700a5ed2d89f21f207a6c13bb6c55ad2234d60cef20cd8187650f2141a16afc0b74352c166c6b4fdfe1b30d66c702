{"_id": "@babel/plugin-transform-object-rest-spread", "_rev": "40-1ec32c86ef66d412abf7306091278509", "name": "@babel/plugin-transform-object-rest-spread", "dist-tags": {"next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "4c2a7cedf7696ae0bbb8ea84eb193a4cd3ade5e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.0.tgz", "fileCount": 8, "integrity": "sha512-PHXQfxbe5EKp2+MuEdBFO4X1gsjvUZPjSDGvYz7PjWl8hZtYDCDxPrwZG+GwT/j6FnAmSz2bTZbQ5Jrh3fhRPg==", "signatures": [{"sig": "MEUCIBLPh3x/ArMdH9SWNf7j419FFR0ooJt5Tviv7xSKiUhLAiEA6W63dtdcPFhpije1a1MJEqYWs/NJOqDbqYaceF9yOQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75123}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.22.0", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-compilation-targets": "^7.21.5", "@babel/plugin-transform-parameters": "^7.22.0", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/parser": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.22.0_1685108739864_0.6561573780361825", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "da6fba693effb8c203d8c3bdf7bf4e2567e802e9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.3.tgz", "fileCount": 7, "integrity": "sha512-38bzTsqMMCI46/TQnJwPPpy33EjLCc1Gsm2hRTF6zTMWnKsN61vdrpuzIEGQyKEhDSYDKyZHrrd5FMj4gcUHhw==", "signatures": [{"sig": "MEQCIBIfv10aJIy13MwRDRoI61eijUKuOf77dXOkfdLugroJAiA5YARLlwWtkqskojJqWNb/1O+Bd30CwgeNIhBYfpwiKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75090}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.22.3", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-compilation-targets": "^7.22.1", "@babel/plugin-transform-parameters": "^7.22.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/parser": "^7.22.3", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.22.3_1685182269697_0.4026171595111061", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "9686dc3447df4753b0b2a2fae7e8bc33cdc1f2e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-Kk3lyDmEslH9DnvCDA1s1kkd3YWQITiBOHngOtDL9Pt6BZjzqb6hiOlb8VfjiiQJ2unmegBqZu0rx5RxJb5vmQ==", "signatures": [{"sig": "MEUCIQCqBtrAj/PeOQEV8cSuSwhCcLgkKORHTh9bF+AiO2t+nQIgXxVQBeRaGYOvddPFeqxlXv0coFSeWrxtnuGg9bGAy0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75090}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.5", "@babel/plugin-transform-parameters": "^7.22.5", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.22.5_1686248490924_0.9058407432387898", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "fbc2da953943811941ee0280c11dd16b3ed1ca82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-cOc5vpTqc/RPgy6Wp/he50np8WDb8nyyMf/nerqi4ZJUgLEH1ZbeP5mFB/tjlDOWf+JNHHYbCfNPzEu76WGfnQ==", "signatures": [{"sig": "MEQCICfdX0iXGjroAps1qJWvGS3kzEeUscuwJusIQ3GT8Q7SAiABeB93ilYy0Fmf3/B7hofNmuRUzTy8rWtFRUe868C3YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73026}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.0", "@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-compilation-targets": "^8.0.0-alpha.0", "@babel/plugin-transform-parameters": "^8.0.0-alpha.0", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/parser": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.0_1689861609622_0.48563486052811133", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "2fe7f6c2f92d0826c063408838c73171d4322f5e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-/OoQwQr7Qz3NBlUElj3MI5vLfwKWY9eYC4Tv1zVV5SZQru0MkTrdXAGZtbi7s1aczs79qwEzSD2jE5BA6A/zrA==", "signatures": [{"sig": "MEQCIHCuFe5iqo7k+ZBR/00dAaDAQOUtm+o1m0AgWjYZ5zH0AiB6VhHhRDaNmLPxA6uG6AirTSh4jwUwXF4XX2CwHXJS8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73026}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.1", "@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-compilation-targets": "^8.0.0-alpha.1", "@babel/plugin-transform-parameters": "^8.0.0-alpha.1", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/parser": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.1_1690221147474_0.14902948120005854", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "23939215b53fd97b247ce164b654375770a9ed7b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-+t3MHpAjPpf4V9YS+S2jVozoBFla6FAIzFynkKBbYdlkka6Kr66ESaaw6C1Ppl7nHLTFHPErFas8fuB5w054ZQ==", "signatures": [{"sig": "MEUCIQCE4Nq5OEmYbO0cvoyUYCTfhBlIp05dno3L9MN2Mt7l0AIgQv44kGqZGKoRq8K/H/OOIQ8ndb+qoeyvD03xyDCcr30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72884}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.2", "@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-compilation-targets": "^8.0.0-alpha.2", "@babel/plugin-transform-parameters": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/parser": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.2_1691594108818_0.6955302141800406", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "dbbb06ce783cd994a8f430d8cefa553e9b42ca62", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-nX8cPFa6+UmbepISvlf5jhQyaC7ASs/7UxHmMkuJ/k5xSHvDPPaibMo+v3TXwU/Pjqhep/nFNpd3zn4YR59pnw==", "signatures": [{"sig": "MEUCIQCgXBvAbZy1xaHHfKpgy/5HUVOph8w1cPEwC1WF3S0nogIgFXb1PoQXFNX/4972IsW7eBX+/lA5ODDIZyZ8SF+Nqmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70612}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.10", "@babel/plugin-transform-parameters": "^7.22.5", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/parser": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.22.11_1692882519957_0.7729042158904307", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "21a95db166be59b91cde48775310c0df6e1da56f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.22.15.tgz", "fileCount": 5, "integrity": "sha512-fEB+I1+gAmfAyxZcX1+ZUwLeAuuf8VIg67CTznZE0MqVFumWkh8xWtn58I4dxdVf080wn7gzWoF8vndOViJe9Q==", "signatures": [{"sig": "MEQCICRPQbWftRNeMHchb2z8ghvm7Usa1n+N7YFGdKdwUS9wAiAepAFBHoT+Lb7SqGA16xwrCaNCOqaDT5ehq6F3J+InXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70616}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/plugin-transform-parameters": "^7.22.15", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/parser": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.22.15_1693830318531_0.48161027562534686", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "8139b292ac12b90b2ae5794376442047444981a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-woIr/DWaWnXFpeD0+c2v7JjJUL4FyTJIkgtsfEtY/JIbYwRiaXZ/EycyevrpTyqDSOwoa5ePwGImmrUaB209dw==", "signatures": [{"sig": "MEQCIFpyvz0YvPwUIx1t0m1RRrmK/q16mUiJt3DtWPsYMSilAiAKjdccc/OdiABBm5G9oeABBT6BSBtojCjd2u/8PNnGWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68581}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.3", "@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-compilation-targets": "^8.0.0-alpha.3", "@babel/plugin-transform-parameters": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/parser": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.3_1695740234999_0.38296368652121804", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "93c566cf4ec253f5599b2268ac4fa6edb30a8efa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-oiemdBsQwXuewBMrvleNpGKnLdxAk+Q95AM3eDesTITWmDDgA+0w9wM+nU2IPk/0wrIGxC7L0+B1DltRb7A7XA==", "signatures": [{"sig": "MEQCIArIwXS5fso4r8DKiQKwkuHz3OGxKQcT/kmeJIs9dfl7AiAyPVSiSm3h8aqAtlwmsRL6rueP0+ss5pMmONiZdtrlWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68581}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.4", "@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-compilation-targets": "^8.0.0-alpha.4", "@babel/plugin-transform-parameters": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/parser": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.4_1697076392862_0.5234772900409994", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "509373753b5f7202fe1940e92fd075bd7874955f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-VxHt0ANkDmu8TANdE9Kc0rndo/ccsmfe2Cx2y5sI4hu3AukHQ5wAu4cM7j3ba8B9548ijVyclBU+nuDQftZsog==", "signatures": [{"sig": "MEQCIETaNUq56LMnwx6WujmjjnXhIZu+y1onwwT6q12GZ8IDAiBbVcA4HifaYtSElCl8/KceyWQoDC1aJPJ3f/gEzJaDxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70716}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/plugin-transform-parameters": "^7.23.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/parser": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.23.3_1699513453531_0.6101880134562099", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "2b9c2d26bf62710460bdc0d1730d4f1048361b83", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-9x9K1YyeQVw0iOXJlIzwm8ltobIIv7j2iLyP2jIhEbqPRQ7ScNgwQufU2I0Gq11VjyG4gI4yMXt2VFags+1N3g==", "signatures": [{"sig": "MEYCIQCn2fr44KH1Nkpqd3sM2V/EuXIcEbkJT+WYNqfjljrq5wIhAMjPF4NHrM6fy1/qQepBUP1hmkJcUm/Dy9kktSRiLyGh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70722}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.15", "@babel/plugin-transform-parameters": "^7.23.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/parser": "^7.23.4", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.23.4_1700490128272_0.25879628202264393", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "c219ae1927097cb9e22a2cd14998ba9ef6460d9c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-LGGksCkBmpWJhFYBaUlBfRLRsWUqSAbzUhI/2aarn8i9BM52M82nn2s52IZotJhE5cdXWXoe8oKAWNnHi4hLBw==", "signatures": [{"sig": "MEUCIQDA8Z4rWcER3A1VVWQ8KgSwU0AR66yJWgBqi91UILSXVQIgOWOQ7Sxq5dLdFlvdWP7WtxgReLtixKiuz/NblaBdCqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68700}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.5", "@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-compilation-targets": "^8.0.0-alpha.5", "@babel/plugin-transform-parameters": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/parser": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.5_1702307958947_0.7336019738100938", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "2e8630a2e13ae6ac2bf523b31994dad1d1087ed9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-0xQdAIcBID7xW6LVliNYnFaOtY5gdEaCpF7OIfemhzfFsH85voYf9FUYmzezmlAy8WRZjabYlbgSbOEg6ncMjA==", "signatures": [{"sig": "MEQCIGTF/+Gk9Zht498Yp0u0/7JCNAJxamIpU2HRz6/l9vT4AiAmTPUxLRyF/6mzOUJdJqoZQTjY/VQS2AfAihC/MXY9lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68695}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.6", "@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-compilation-targets": "^8.0.0-alpha.6", "@babel/plugin-transform-parameters": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/parser": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.6_1706285660761_0.2658545632740361", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.24.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "7b836ad0088fdded2420ce96d4e1d3ed78b71df1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.0.tgz", "fileCount": 5, "integrity": "sha512-y/yKMm7buHpFFXfxVFS4Vk1ToRJDilIa6fKRioB9Vjichv58TDGXTvqV0dN7plobAmTW5eSEGXDngE+Mm+uO+w==", "signatures": [{"sig": "MEQCIAIqbkJx/jw7OomrWJeZL22p63O5/KX4QthEIFQIN2WaAiAOFmnMbWdD9L5sPhSzJ+w42ffSovDW7lOfC36lFAADCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70931}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^7.23.5", "@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-compilation-targets": "^7.23.6", "@babel/plugin-transform-parameters": "^7.23.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.0", "@babel/parser": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.24.0_1709120861052_0.1591594656682349", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "25adbb375355da9b3dd3c113da4a3ef717057b3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-LQQaJ0Td7Tm41IbNA+aYwbybWLcIUxWy08kFembtQ3Z/i9IUj4hKSdFGk7skM36e0MIM1kqhwFwN9f1qXblzFA==", "signatures": [{"sig": "MEUCICgNZGYjfqPafjCRwTNTB+xmfB1TuqJBsvTHiz7gh6YtAiEAk8DLpDQRBSUu0JSPcONs0MVKtpuHNucnD1SBOH2MNVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68574}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/compat-data": "^8.0.0-alpha.7", "@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-compilation-targets": "^8.0.0-alpha.7", "@babel/plugin-transform-parameters": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/parser": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.7_1709129116111_0.775199857959429", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "5a3ce73caf0e7871a02e1c31e8b473093af241ff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-XjD5f0YqOtebto4HGISLNfiNMTTs6tbkFf2TOqJlYKYmbo+mN9Dnpl4SRoofiziuOWMIyq3sZEUqLo3hLITFEA==", "signatures": [{"sig": "MEYCIQDtArF2r/3QjYUkU8dHn71AaGJIvycHcKn9m369Y/kSrgIhAKHZSFkeYq4TFFtNc534vkkyV0I7ryd0EiYP1LDX6MhE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72808}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-compilation-targets": "^7.23.6", "@babel/plugin-transform-parameters": "^7.24.1", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/parser": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.24.1_1710841768432_0.28240087060768815", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "f8864d335ca6278b2e97920b12f827d2b16db410", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-gpAobcOXR56i+c/3MwRGbOOesDyYRkr/AHH5svOntysP7QGWKDR1PQzuHn2gn66q4XMKgfeXOvwwRKLMiZNa1Q==", "signatures": [{"sig": "MEUCIQCHT10B1Bf06mdaIfgVBfpP9pEYptvJ92JT7YjPG2cIkAIga6OdSpxqmk3YT+LO1Nr9UaAeVow2rxnEs8F+67SOt20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70465}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-compilation-targets": "^8.0.0-alpha.8", "@babel/plugin-transform-parameters": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/parser": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.8_1712236803276_0.8113379396353748", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "f91bbcb092ff957c54b4091c86bda8372f0b10ef", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-7EauQHszLGM3ay7a161tTQH7fj+3vVM/gThlz5HpFtnygTxjrlvoeq7MPVA1Vy9Q555OB8SnAOsMkLShNkkrHA==", "signatures": [{"sig": "MEYCIQDWaQGpXR4H+6Dd4WHgq+d8AognZ98DXucmuhLmEfMZAAIhANxjFSJYm8E+njbbJOu5pA2wqr9h0PkGmrKfXRTebkvp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139325}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/plugin-transform-parameters": "^7.24.5", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/parser": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.24.5_1714415663743_0.352810886653357", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "68d763f69955f9e599c405c6c876f5be46b47d8a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-OKmi5wiMoRW5Smttne7BwHM8s/fb5JFs+bVGNSeHWzwZkWXWValR1M30jyXo1s/RaqgwwhEC62u4rFH/FBcBPg==", "signatures": [{"sig": "MEUCIQCL2kVakhv4R71CQZsCU3wvwWCUC+EYJ9kpvtpEmQrncgIgLMa8sP6hVTyDQrOF73Qn3bEjmxBrqg+diogKn7fvF3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139480}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-compilation-targets": "^7.24.6", "@babel/plugin-transform-parameters": "^7.24.6", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/parser": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.24.6_1716553491295_0.1732346832154834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "e7628b244ec2057322f523c5a0551ae988357dc2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Dq9gatP2crCFv8xwDp+pgCgO8PUBojEvxQPQdX6/tNYdrQolA8QOGWLjbqi4I+r8a/TmaobRl8DkVHogPR5bVw==", "signatures": [{"sig": "MEQCID0oYIjnyvJqZUVGcOX9miGl5/WHp9KlKLf1U3+mO9HtAiBtnMpvYw++XHk3Wb0wX+L3xPs10sf+cnkgfl/hgt2Swg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137473}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-compilation-targets": "^8.0.0-alpha.9", "@babel/plugin-transform-parameters": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/parser": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.9_1717423482678_0.4449369181413816", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "5f4b3600f34376e337d8f1de7978292ce3650426", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-lpMVQDi2ttA+7JBI1sxkcnnYHoa9Lq/q1DUxJ0ZOU6RDndvrNGeMk8eTGRMljbhLhdwwk8czI9kFSoLmxdJPOg==", "signatures": [{"sig": "MEYCIQD8XzN/MZcCjlHnuCAOLlSS6ozZnXBMkGtI/MOuGKDSUwIhAIPL5rrFVXWxSUfrh8iuz7QCRY/zUFPsiOza3SOsxmgp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137483}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-compilation-targets": "^8.0.0-alpha.10", "@babel/plugin-transform-parameters": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/parser": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.10_1717500020598_0.015633568207944704", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "d13a2b93435aeb8a197e115221cab266ba6e55d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-4QrHAr0aXQCEFni2q4DqKLD31n2DL+RxcwnNjDFkSG0eNQ/xCavnRkfCUjsyqGC2OviNJvZOF/mQqZBw7i2C5Q==", "signatures": [{"sig": "MEUCIQCRSZ3ENsEKeGMte7GCqdRw4DVz6AETKr1ok+NGvyXe1wIgPEMKrGlw2FozwdcamEJsf6AFmunxtwgIKf9rfDD94Rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139430}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.7", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/parser": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.24.7_1717593334202_0.14481233053547848", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "261bc9d5d649b9c36551da39cdf6b8e06de149da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-vtzZ/JNQs6F2Q5SqplzGGQJufWNK4lF/AHREqJcVRSpIzTYgVB9mIi192LaB5++eb29QyMEQ1fZp8Zzm7w0CIg==", "signatures": [{"sig": "MEUCIDEHyb7SlUtfGUe9Ion7AYopGf0RXeKz+1T9R6n2P3qdAiEAznyATAdV2KWoNkKFQJAYc5+yUKxNCARsDyWdqVc4MwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137374}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-compilation-targets": "^8.0.0-alpha.11", "@babel/plugin-transform-parameters": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/parser": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.11_1717751744841_0.4685583212066933", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "5c13a8019943636b6c048c9adefeeaf0d0792758", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-xFDLGJ5BOoHIhdAIZmrKj0BsE1eZ2BUeAdvSE8Vlu0kkjpx2GAW3MGRsVyAbPqnC92F3a9TER8bGUOrM7LAZoQ==", "signatures": [{"sig": "MEQCICzng7LQf7Ay/EdzYFmofGbHBMryxS4gEceQFnoPN7FxAiAE8+h8mTdaHynRAwPVbF/BSDAQNmgrlIo66s9mmJexMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134122}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-compilation-targets": "^8.0.0-alpha.12", "@babel/plugin-transform-parameters": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/parser": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.12_1722015219967_0.37323413394974425", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "fa0916521be96fd434e2db59780b24b308c6d169", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-1JdVKPhD7Y5PvgfFy0Mv2brdrolzpzSoUq2pr6xsR+m+3viGGeHEokFKsCgOkbeFOQxfB1Vt2F0cPJLRpFI4Zg==", "signatures": [{"sig": "MEYCIQDQ9q/8/8oewlTk0EMpQotZ5aeWPOzBahFavDkNHddG8gIhAIJGyF/IOVaz6RXbi2NqcG0IyEjClNS0fsncJyYqQVUm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144055}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-compilation-targets": "^7.25.7", "@babel/plugin-transform-parameters": "^7.25.7", "@babel/plugin-syntax-object-rest-spread": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/parser": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.25.7_1727882104968_0.6799187999461538", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "0904ac16bcce41df4db12d915d6780f85c7fb04b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-LkUu0O2hnUKHKE7/zYOIjByMa4VRaV2CD/cdGz0AxU9we+VA3kDDggKEzI0Oz1IroG+6gUP6UmWEHBMWZU316g==", "signatures": [{"sig": "MEQCIHmoLEaFDjoJf8cwIlTnfcrj9S3GakIcqFBCYQP5wV/VAiAs9lqcWTH6WyAFoNaF0O8sngLps0nZ4ocsYEdxo3GQkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144340}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-compilation-targets": "^7.25.7", "@babel/plugin-transform-parameters": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/parser": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.25.8_1728566711607_0.06495444660669958", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "0203725025074164808bcf1a2cfa90c652c99f18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-fSaXafEE9CVHPweLYw4J0emp1t8zYTXyzN3UuG+lylqkvYd7RMrsOQ8TYx5RF231be0vqtFC6jnx3UmpJmKBYg==", "signatures": [{"sig": "MEYCIQDAvXWtu77NKbQa0EfTfaTdPVd18B0JwEcZpwpP1aTuIAIhAPE6YhtUL2D1Qrepg3A8tAt1L5rffCsrTM4fC7Jnj3Dp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72499}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-compilation-targets": "^7.25.9", "@babel/plugin-transform-parameters": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.25.9_1729610480144_0.9941244740017128", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "b1b00e2cacc195ba285721fde05a2b0224dd81b7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-YAFBM4hIbQnDLoFtHiDraqroQVf5pYaRcU/uyelOfsYIztWtwJ/FGr6xfYNeTjB/cN+/nrAAliWJ/wrRa4igqQ==", "signatures": [{"sig": "MEQCICu2Piu+o3ePgWZG64NQ6eOQt/0cKm3n/HidlPXRFSreAiBsPf8oB6NYDJONMK4K6nMOwRwgYDvKQQtchLSnt91WcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70708}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-compilation-targets": "^8.0.0-alpha.13", "@babel/plugin-transform-parameters": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/parser": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.13_1729864461878_0.06465404114754736", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "b6d6994ba36b842a0ddb4e3c0498ca7a8512173b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-tYu8Oejv+KlW3RW6EYIeqNOVoAs761T1VhtFwc7DfJURAsJM9WlpspvwwMkwjprPQ2qPQBM8PppjbqGNYSw4cQ==", "signatures": [{"sig": "MEUCIQDL7A9n39DKQKiZf5zsjIvkjxPRl9HU69gvey0eV4b17gIgKk7FHPu0Ycg6G6F16AH7kXT7WXgTtK4XSbufYRglsrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70708}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-compilation-targets": "^8.0.0-alpha.14", "@babel/plugin-transform-parameters": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/parser": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.14_1733504052114_0.9705264860967049", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "ab85d6b0115d74eade202ac67f21d0ec10436f29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-4rSGlpuIxlJM9+JJCsJ9a/WzWVWSUYpvw+vuyHZ2rQJEyhpl+LX8vcRliYblEz5qvsYexsipwM1u+PClner2Dw==", "signatures": [{"sig": "MEQCICvKJl6jdrVAL3P/g8WoBwRkdtNDJm/cjmjz/y50U01oAiA5kxRwKxQZlCW/DKXslCQxXO6Oqc/ZohxxvUKdc9vd8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70708}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-compilation-targets": "^8.0.0-alpha.15", "@babel/plugin-transform-parameters": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/parser": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.15_1736529878786_0.36372386131386625", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "3e4602324563cf0675feabae0a1fbedb39858154", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-A8eXBDXj8M8vdhZDElj/IjPsU1RkwUBG7Qv0emjSxZRwQQqfZ0jJrpq12aWquiEaRtN8oFjvXlkK9MmBlkPNOw==", "signatures": [{"sig": "MEUCIQD17FExHJ4UHHIJOfuBG+VdQxQ71jrQEerSt99mpaaqKAIgO1gHXChzrhsRdNMe1gf7umeRX3J4PUHP3hBJBxexlxA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70708}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-compilation-targets": "^8.0.0-alpha.16", "@babel/plugin-transform-parameters": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/parser": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.16_1739534355101_0.30740828548758303", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "5ed92bc565576b159b4430c40f725415257c5a9a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-bPfpqE8jOVZYFzuOTGNBidkoed2zxnmlsLDMum9rQXZ0u3JDq4vpwxnQAKhnESpIWQ8Bv7tUObwClBANECsKMw==", "signatures": [{"sig": "MEUCIDaf+o+GV9nFBfsJghIIcW/9TZtk38l33q3SaIB5gVVXAiEAsiSVAHdkLb74sRsJEeea7vh0vRZYoIsVyOt6RZ/ezN4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70708}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-compilation-targets": "^8.0.0-alpha.17", "@babel/plugin-transform-parameters": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/parser": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-alpha.17_1741717508034_0.45592955304234195", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "845bdcd74c87b8f565c25cc6812f7f4f43c9ed79", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-/sSliVc9gHE20/7D5qsdGlq7RG5NCDTWsAhyqzGuq174EtWJoGzIu1BQ7G56eDsTcy1jseBZwv50olSdXOlGuA==", "signatures": [{"sig": "MEQCICE8VtuvwKox979qr9rO7gEqJLDtnIJdxv34m1RleDWJAiB+8bskDFUYODP4Z+lxsOaANqRB5DaYazxhi5KFGShwog==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 72499}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.27.1_1746025744740_0.36894690394966023", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.2": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.27.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.27.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "67f9ab822347aa2bcee91e8996763da79bdea973", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.2.tgz", "fileCount": 5, "integrity": "sha512-AIUHD7xJ1mCrj3uPozvtngY3s0xpv7Nu7DoUSnzNY6Xam1Cy4rUznR//pvMHOhQ4AvbCexhbqXCtpxGHOGOO6g==", "signatures": [{"sig": "MEUCIQCEONOHN+WFniFteed8iK02LyosDaUC62VyKZYQjG8JYQIgO0aPrVLhz0hqIqWQRQuonU2MxN5zx0deYfvhXHwi5fc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76174}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.2", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.27.2_1746545630200_0.5511744528063363", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.27.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "ce130aa73fef828bc3e3e835f9bc6144be3eb1c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.3.tgz", "fileCount": 5, "integrity": "sha512-7ZZtznF9g4l2JCImCo5LNKFHB5eXnN39lLtLY5Tg+VkR0jwOt7TBciMckuiQIOIW7L5tkQOCh3bVGYeXgMx52Q==", "signatures": [{"sig": "MEUCIA6ZeLolcZD0Aj5jjrIf/zQgA0FMtuOxU4r3vbH9oi8/AiEAr3oay8FunljZSr4P6NweINtuQZivPMaJlLX1v3a8aMY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76183}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.2", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.27.3_1748335160953_0.10692340220741992", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "0ffb290e1f4cc01a8421aff1088e6abcc1612e2d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-kIp/AtJZ1MxjKNnhXcVerqS+iAlkq3WNCo5gOne6gQfSiNFwOCt0iclkyZ3NJhHLotXMHSTjJh4KBZpEaxzPSw==", "signatures": [{"sig": "MEQCIBysVCEKMnHWngAnv8APoMC6Ipwyq5FZOXjHgx3cyr4HAiBOFHdr3Q28IXURaoqnYPkHr2Sy2d9ofx58tXyT49mAgQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 74263}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-compilation-targets": "^8.0.0-beta.0", "@babel/plugin-transform-parameters": "^8.0.0-beta.0", "@babel/plugin-transform-destructuring": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/parser": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-beta.0_1748620276368_0.3156845276883984", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.7": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.27.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.27.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "2e5c02348b6a4326aa6e8b0b950602dc0d92227c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.7.tgz", "fileCount": 5, "integrity": "sha512-201B1kFTWhckclcXpWHc8uUpYziDX/Pl4rxl0ZX0DiCZ3jknwfSUALL3QCYeeXXB37yWxJbo+g+Vfq8pAaHi3w==", "signatures": [{"sig": "MEUCIGp8ph2rw0ICvjJbDCHxL4gASb3CqGB53ZxVJPVDX3KyAiEA2Pn5yM4N9w5+nKyBqtejRgfdjCqaaXBfJtKEJJDy+Uk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76061}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.7", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.2", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/plugin-transform-destructuring": "^7.27.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.7", "@babel/parser": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.27.7_1750946605653_0.5593686518048637", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-object-rest-spread", "version": "7.28.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-object-rest-spread@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "dist": {"shasum": "d23021857ffd7cd809f54d624299b8086402ed8d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.28.0.tgz", "fileCount": 5, "integrity": "sha512-9VNGikXxzu5eCiQjdE4IZn8sb9q7Xsk5EXLDBKUYg1e/Tve8/05+KJEtcxGxAgCY5t/BpKQM+JEL/yT4tvgiUA==", "signatures": [{"sig": "MEUCIQDMfAd7hBlQXhyIC4G+IoHst94OgaZ5Y2LeokBrtg06/QIgdIS9CdpM40oeuUfTuCx+c9O3OOgHUxNj3B944dPMyRs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76301}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "directories": {}, "dependencies": {"@babel/traverse": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.2", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/plugin-transform-destructuring": "^7.28.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-object-rest-spread_7.28.0_1751445509635_0.7816726543919525", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-object-rest-spread", "version": "8.0.0-beta.1", "description": "Compile object rest and spread to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-compilation-targets": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/plugin-transform-destructuring": "^8.0.0-beta.1", "@babel/plugin-transform-parameters": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/parser": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-object-rest-spread@8.0.0-beta.1", "dist": {"shasum": "5dd13e4bf5aad85877d2fbc278bb351249baf02b", "integrity": "sha512-GtkZgyC7jVqeI8VsrdYr66fq8xZI6TYXoS4ZvkgJuyLqly3dz0V34IpLHJT3YDyG1urB68uWV2Dchp5JUz2woQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 73882, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCfvnxFRMzzFqG7ww63ppg8UpKas65xAHH9hHgMSTqTpgIhAPkg9Y2hhFfPv35ReOrqShTzwrolXS3fWKE/xslofS8g"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-object-rest-spread_8.0.0-beta.1_1751447082947_0.5193999117900443"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:39.809Z", "modified": "2025-07-02T09:04:43.339Z", "7.22.0": "2023-05-26T13:45:40.064Z", "7.22.3": "2023-05-27T10:11:09.894Z", "7.22.5": "2023-06-08T18:21:31.083Z", "8.0.0-alpha.0": "2023-07-20T14:00:09.861Z", "8.0.0-alpha.1": "2023-07-24T17:52:27.632Z", "8.0.0-alpha.2": "2023-08-09T15:15:09.122Z", "7.22.11": "2023-08-24T13:08:40.182Z", "7.22.15": "2023-09-04T12:25:18.832Z", "8.0.0-alpha.3": "2023-09-26T14:57:15.275Z", "8.0.0-alpha.4": "2023-10-12T02:06:33.165Z", "7.23.3": "2023-11-09T07:04:13.736Z", "7.23.4": "2023-11-20T14:22:08.457Z", "8.0.0-alpha.5": "2023-12-11T15:19:19.140Z", "8.0.0-alpha.6": "2024-01-26T16:14:20.917Z", "7.24.0": "2024-02-28T11:47:41.206Z", "8.0.0-alpha.7": "2024-02-28T14:05:16.316Z", "7.24.1": "2024-03-19T09:49:28.686Z", "8.0.0-alpha.8": "2024-04-04T13:20:03.439Z", "7.24.5": "2024-04-29T18:34:23.917Z", "7.24.6": "2024-05-24T12:24:51.434Z", "8.0.0-alpha.9": "2024-06-03T14:04:42.881Z", "8.0.0-alpha.10": "2024-06-04T11:20:20.784Z", "7.24.7": "2024-06-05T13:15:34.369Z", "8.0.0-alpha.11": "2024-06-07T09:15:44.995Z", "8.0.0-alpha.12": "2024-07-26T17:33:40.182Z", "7.25.7": "2024-10-02T15:15:05.244Z", "7.25.8": "2024-10-10T13:25:11.821Z", "7.25.9": "2024-10-22T15:21:20.474Z", "8.0.0-alpha.13": "2024-10-25T13:54:22.168Z", "8.0.0-alpha.14": "2024-12-06T16:54:12.300Z", "8.0.0-alpha.15": "2025-01-10T17:24:39.006Z", "8.0.0-alpha.16": "2025-02-14T11:59:15.290Z", "8.0.0-alpha.17": "2025-03-11T18:25:08.223Z", "7.27.1": "2025-04-30T15:09:04.921Z", "7.27.2": "2025-05-06T15:33:50.571Z", "7.27.3": "2025-05-27T08:39:21.148Z", "8.0.0-beta.0": "2025-05-30T15:51:16.539Z", "7.27.7": "2025-06-26T14:03:25.845Z", "7.28.0": "2025-07-02T08:38:29.821Z", "8.0.0-beta.1": "2025-07-02T09:04:43.114Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "description": "Compile object rest and spread to ES5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}